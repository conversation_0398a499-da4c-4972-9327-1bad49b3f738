#ifndef SOCKCLASS
#define SOCKCLASS

#include <winsock2.h>

#define SERVER 0
#define CLIENT 1

void init_winsock(void);
void fini_winsock(void);

class SockClass {
	protected:

		// Internal method of generating a new Socket:
		SockClass();

	public:

		// Starts a server socket on the local machine
		SockClass(int port);  

		//Starts a client socket that connects to the designated addr.
		SockClass(char *machine, int port); 
						    
		virtual ~SockClass();

		// Receives up to *max* bytes from socket.
		int GetData(char *buffer, int max);

		// Sends *size* bytes to socket
		int SendData(const char *buffer, int size);

		// Server method only - returns new data socket
		SockClass *Listen();

		void PrintError(int err);

	protected:
		SOCKET this_socket;
		SOCKET that_socket;
		SOCKADDR_IN this_addr;
		SOCKADDR_IN that_addr;
		
		int SocketType;  // 0 = Server 1 = Client;

		char ReadCache[1025];	// Incoming data buffer
		char *CachePtr;
		int m_Port;

};



#endif //SOCKCLASS defined

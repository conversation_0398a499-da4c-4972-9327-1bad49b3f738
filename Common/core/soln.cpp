#include <stdlib.h>
#include <stdio.h>
#include <string.h>

#include "soln.h"

/* *************************************************************** */
/* This module provides the definition for all of the pieces of    */
/* the Soln class.                                                 */
/* *************************************************************** */

Soln::Soln()
{
	elemSize = sizeof(int);
	elemCount = (int)50;

	fitness = (float)0;

	rep = malloc(elemSize * elemCount);
	if(rep == NULL){
		printf("Error allocating memory for a solution!\n");
		exit(1);
	}
}

Soln::Soln(Soln *s)
{
	elemSize = s->elemSize;
	elemCount = s->elemCount;
	fitness = s->fitness;

	rep = malloc(elemSize * elemCount);
	if(rep == NULL){
		printf("Error allocating memory for a solution!\n");
		exit(1);
	}
	memcpy(rep, s->rep, elemSize * elemCount);
}

Soln::~Soln()
{
	free(rep);
}

float Soln::doFitEval(void)
{
	int i;
	float ret;

	ret = (float)0;
	for(i=0;i<elemCount;i++){
		ret += ((int *)rep)[i];
	}
	
	fitness = ret;

	return ret;
}

void Soln::doCrossOver(Soln *one, Soln *two)
{
	/* ********************************************************* */
	/* In the crossover operation, we accept into ourself the    */
	/* appropriate pieces from one and two.  We do not modify    */
	/* one or two, we just set our definition from them.         */	
	/* ********************************************************* */
	
	int pt;

	memcpy(rep, one->rep, elemCount * elemSize);

	pt = rnd->getRandi(elemCount);
	
	memcpy(rep, two->rep, elemSize * pt);
}
	
void Soln::doMutate(void)
{
	((int *)rep)[rnd->getRandi(elemCount)] = rnd->getRandi(elemCount);
}

void *Soln::duplicate(void)
{
	return(new Soln(this));
}

void Soln::init(void)
{
	int i,j,tmp;

	for(i=0;i<elemCount;i++){
		((int *)rep)[i] = i;
	}

	for(i=0;i<elemCount;i++){
		j = rnd->getRandi(elemCount);
		tmp = ((int *)rep)[i];
		((int *)rep)[i] = ((int *)rep)[j];
		((int *)rep)[j] = tmp;
	}
}	

void Soln::display(void)
{
	int i;

	for(i=0;i<elemCount;i++){
		printf("Soln element (%d) is (%d)\n", i, ((int *)rep)[i]);
	}
}

void Soln::defineSoln(Soln *s)
{
	memcpy(rep, s->rep, elemCount * elemSize);
}	


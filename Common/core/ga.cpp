#include <stdlib.h>
#include <stdio.h>
#include <string.h>

#include "ga.h"

ga::ga(Pop *p)
{
	generation = 0;

	oldPop = p;
	newPop = (Pop *)p->duplicate();
}

ga::ga(ga *g)
{
	generation = g->generation;

	oldPop = (Pop *)g->oldPop->duplicate();
	newPop = (Pop *)g->newPop->duplicate();
}

ga::~ga()
{
	delete newPop;
	delete oldPop;
}

ga *ga::duplicate(void)
{
	return (new ga(this));
}


void ga::init(void)
{
	generation = 0;

	oldPop->initialize();
}

float ga::evolve(int gc)
{
	int i, j, idx1, idx2;
	Pop *tmp;

	for(i=0;i<gc;i++, generation++){
		
		oldPop->evaluate();

		for(j=0;j<newPop->popSize;j++){
			idx1 = oldPop->select();
			idx2 = oldPop->select();

			newPop->sList[j]->doCrossOver(
				oldPop->sList[idx1], oldPop->sList[idx2]);

			newPop->sList[j]->doMutate();
		}

		newPop->sList[0]->defineSoln(
			oldPop->sList[oldPop->bestFitIdx]);

		tmp = oldPop;
		oldPop = newPop;
		newPop = tmp;
	}

	return oldPop->sList[0]->fitness;
}

void ga::display(void)
{
	oldPop->sList[0]->display();
}

Soln *ga::getBestSoln(void)
{
	return oldPop->sList[0];
}
		
void ga::receiveNewSoln(Soln *s, int pos)
{
	if((pos>0) && (pos < oldPop->popSize)){
		//oldPop->sList[pos]->defineSoln(s);
		oldPop->sList[pos]->doCrossOver( oldPop->sList[pos], s);
	}
}



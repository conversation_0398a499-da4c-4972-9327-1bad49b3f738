#include <stdlib.h>
#include <stdio.h>
#include <string.h>

#include "pop.h"

Pop::Pop()
{
	int i;

	popSize = 10;

	sList = (Soln **)malloc(popSize * sizeof(Soln *));
	if(sList == NULL){
		printf("Error allocating memory for solution list!\n");
		exit(1);
	}

	for(i=0;i<popSize;i++){
		sList[i] = new Soln();
	}		

	rnd = new RandObj();

	initialize();
}

Pop::Pop(int pSize)
{
	int i;

	popSize = pSize;

	sList = (Soln **)malloc(popSize * sizeof(Soln *));
	if(sList == NULL){
		printf("Error allocating memory for solution list!\n");
		exit(1);
	}

	for(i=0;i<popSize;i++){
		sList[i] = new Soln();
	}		

	rnd = new RandObj();

	initialize();
}

Pop::Pop(int pSize, Soln *starter)
{
	int i;

	popSize = pSize;

	sList = (Soln **)malloc(popSize * sizeof(Soln *));
	if(sList == NULL){
		printf("Error allocating memory for solution list!\n");
		exit(1);
	}

	sList[0] = starter;
	for(i=1;i<popSize;i++){
		sList[i] = (Soln *)sList[0]->duplicate();
	}		

	rnd = new RandObj();

	initialize();
}

Pop::Pop(Pop *p)
{
	int i;

	popSize = p->popSize;
	
	sList = (Soln **)malloc(popSize * sizeof(Soln *));
	if(sList == NULL){
		printf("Error allocating memory for solution list!\n");
		exit(1);
	}

	rnd = new RandObj();

	for(i=0;i<popSize;i++){
		sList[i] = (Soln *)p->sList[i]->duplicate();
		sList[i]->rnd = rnd;
	}

	bestFitIdx = 0;
	highFitIdx = 0;
	lowFitIdx = 0;
	
	bestFitVal = (float)0;
	highFitVal = (float)0;
	lowFitVal = (float)0;

	totalFitVal = (float)0;
	scaleFact = (float)1;
	scaleOff = (float)0;
	scaleInit = 0;

	minIsBest = 1;
}

Pop::~Pop()
{
	int i;
	
	for(i=0;i<popSize;i++){
		delete sList[i];
	}

	delete rnd;

	free(sList);
}

Pop *Pop::duplicate(void)
{
	return(new Pop(this));
}

void Pop::initialize(void)
{
	int i;

	bestFitIdx = 0;
	highFitIdx = 0;
	lowFitIdx = 0;
	
	bestFitVal = (float)0;
	highFitVal = (float)0;
	lowFitVal = (float)0;

	totalFitVal = (float)0;
	scaleFact = (float)1;
	scaleOff = (float)0;
	scaleInit = 0;

	minIsBest = 1;

	for(i=0;i<popSize;i++){
		sList[i]->rnd = rnd;
		sList[i]->init();
	}
}

void Pop::initScale(float x1, float x2, float y1, float y2)
{
	scaleFact = (y2-y1)/(x2-x1);

	scaleOff = y1 - scaleFact * x1;
	
	scaleInit = 1;
}

float Pop::doScale(float val)
{
	return(val * scaleFact + scaleOff);
}


void Pop::evaluate(void)
{
	int i;
	float tmp;

	highFitIdx = 0;
	highFitVal = sList[0]->doFitEval();

	lowFitIdx = highFitIdx;
	lowFitVal = highFitVal;

	for(i=1;i<popSize;i++){
		tmp = sList[i]->doFitEval();
	
		if(tmp > highFitVal){
			highFitVal = tmp;
			highFitIdx = i;
		}
		if(tmp < lowFitVal){
			lowFitVal = tmp;
			lowFitIdx = i;
		}
	}

	if(scaleInit == 0){
		if(minIsBest == 1){
			initScale(highFitVal, 0,   // First range maps into...
			          1,         100); // Second Range.
		} else {
			initScale(highFitVal, 0,   // First range maps into...
			          100,        1); // Second Range.
		}
	
		scaleInit = 1;
	}

	highFitVal = doScale(highFitVal);
	lowFitVal = doScale(lowFitVal);

	totalFitVal = 0;
	for(i=0;i<popSize;i++){
		sList[i]->fitness = doScale(sList[i]->fitness);
		totalFitVal += sList[i]->fitness;
	}

	if(minIsBest == 1){
		bestFitVal = lowFitVal;
		bestFitIdx = lowFitIdx;
	} else {
		bestFitVal = highFitVal;
		bestFitIdx = highFitIdx;
	}
}

int Pop::select(void)
{
	float tmp;
	int idx;

	tmp = rnd->getRandf(totalFitVal);
	idx = 0;

	while((idx < popSize-1) && (tmp > sList[idx]->fitness))
		tmp -= sList[idx++]->fitness;

	return idx;
}



#include <stdlib.h>
#include <stdio.h>
#include <string.h>

#include "pop.h"

void *handleEval(void *parm)
{
	popthread_arg *args;
	int i;

	args = (popthread_arg *)parm;

	for(i=0;i<args->solnCount;i++){
		args->solnList[i]->doFitEval();
	}

	return (void *)NULL;
}

Pop::Pop()
{
	int i;

	popSize = 10;

	sList = (Soln **)malloc(popSize * sizeof(Soln *));
	if(sList == NULL){
		printf("Error allocating memory for solution list!\n");
		exit(1);
	}

	for(i=0;i<popSize;i++){
		sList[i] = new Soln();
	}		

	rnd = new RandObj();

	initialize();

	evalList = NULL;
	evalCount = 1;

	isDuplicate = 0;

}

Pop::Pop(int pSize)
{
	int i;

	popSize = pSize;

	sList = (Soln **)malloc(popSize * sizeof(Soln *));
	if(sList == NULL){
		printf("Error allocating memory for solution list!\n");
		exit(1);
	}

	for(i=0;i<popSize;i++){
		sList[i] = new Soln();
	}		

	rnd = new RandObj();

	initialize();

	evalList = NULL;
	evalCount = 1;

	isDuplicate = 0;
}

Pop::Pop(int pSize, Soln *starter)
{
	int i;

	popSize = pSize;

	sList = (Soln **)malloc(popSize * sizeof(Soln *));
	if(sList == NULL){
		printf("Error allocating memory for solution list!\n");
		exit(1);
	}

	sList[0] = starter;
	for(i=1;i<popSize;i++){
		sList[i] = (Soln *)sList[0]->duplicate();
	}		

	rnd = new RandObj();

	initialize();

	evalList = NULL;
	evalCount = 1;

	isDuplicate = 0;
}

Pop::Pop(Pop *p)
{
	int i;

	popSize = p->popSize;
	
	sList = (Soln **)malloc(popSize * sizeof(Soln *));
	if(sList == NULL){
		printf("Error allocating memory for solution list!\n");
		exit(1);
	}

	rnd = new RandObj();

	for(i=0;i<popSize;i++){
		sList[i] = (Soln *)p->sList[i]->duplicate();
		sList[i]->rnd = rnd;
	}

	bestFitIdx = 0;
	highFitIdx = 0;
	lowFitIdx = 0;
	
	bestFitVal = (float)0;
	highFitVal = (float)0;
	lowFitVal = (float)0;

	totalFitVal = (float)0;
	scaleFact = (float)1;
	scaleOff = (float)0;
	scaleInit = 0;

	minIsBest = 1;

	evalList = p->evalList;
	evalCount = p->evalCount;
	evalArgs = p->evalArgs;

	isDuplicate = 1;
}

Pop::~Pop()
{
	int i;
	
	for(i=0;i<popSize;i++){
		delete sList[i];
	}

	for(i=0;i<evalCount;i++){
		if((evalList != NULL) && (isDuplicate == 0)){
			delete evalList[i];
			free(evalArgs[i]->solnList);
			free(evalArgs[i]);
		}
	}

	delete rnd;

	free(sList);
	if((evalList != NULL) && (isDuplicate == 0)){
		free(evalList);
		free(evalArgs);
	}
	evalList = NULL;
}

void *Pop::duplicate(void)
{
	return(new Pop(this));
}

void Pop::initialize(void)
{
	int i;

	bestFitIdx = 0;
	highFitIdx = 0;
	lowFitIdx = 0;
	
	bestFitVal = (float)0;
	highFitVal = (float)0;
	lowFitVal = (float)0;

	totalFitVal = (float)0;
	scaleFact = (float)1;
	scaleOff = (float)0;
	scaleInit = 0;

	minIsBest = 1;

	for(i=0;i<popSize;i++){
		sList[i]->rnd = rnd;
		sList[i]->init();
	}
}

void Pop::initScale(float x1, float x2, float y1, float y2)
{
	scaleFact = (y2-y1)/(x2-x1);

	scaleOff = y1 - scaleFact * x1;
	
	scaleInit = 1;
}

float Pop::doScale(float val)
{
	return(val * scaleFact + scaleOff);
}

void Pop::eval_thread(void)
{
	int i,j,k;

	/* ********************************************* */
	/* Here is where we set up to launch the threads */
	/* ********************************************* */
	k = 0;
	for(i=0;i<evalCount;i++){
		for(j=0;j<evalArgs[i]->solnCount;j++){
			evalArgs[i]->solnList[j] = sList[k++];
		}
	}

	for(i=0;i<evalCount;i++){
		evalList[i]->start(handleEval, evalArgs[i]);
	}

	for(i=0;i<evalCount;i++){
		evalList[i]->join();
	}
}

void Pop::eval_nothread(void)
{
	int i;

	for (i=0;i<popSize;i++){
		sList[i]->doFitEval();
	}
}

void Pop::evaluate(void)
{
	int i;

	if(evalCount > 1){
		eval_thread();
	} else {
		eval_nothread();
	}

	/* ********************************************* */
	/* Now just gather the statistics from the solns */
	/* ********************************************* */

	highFitIdx = 0;
	highFitVal = sList[0]->fitness;

	lowFitIdx = highFitIdx;
	lowFitVal = highFitVal;

	for(i=1;i<popSize;i++){
		if(sList[i]->fitness > highFitVal){
			highFitVal = sList[i]->fitness;
			highFitIdx = i;
		}
		if(sList[i]->fitness < lowFitVal){
			lowFitVal = sList[i]->fitness;
			lowFitIdx = i;
		}
	}

	if(scaleInit == 0){
		if(minIsBest == 1){
			initScale(highFitVal, (float)0,   // First range maps into...
			          (float)1,  (float)100); // Second Range.
		} else {
			initScale(highFitVal, (float)0,   // First range maps into...
			          (float)100,  (float)1); // Second Range.
		}
	
		scaleInit = 1;
	}

	highFitVal = doScale(highFitVal);
	lowFitVal = doScale(lowFitVal);

	totalFitVal = (float)0;
	for(i=0;i<popSize;i++){
		sList[i]->fitness = doScale(sList[i]->fitness);
		totalFitVal += sList[i]->fitness;
	}

	if(minIsBest == 1){
		bestFitVal = lowFitVal;
		bestFitIdx = lowFitIdx;
	} else {
		bestFitVal = highFitVal;
		bestFitIdx = highFitIdx;
	}
}

int Pop::select(void)
{
	float tmp;
	int idx;

	tmp = rnd->getRandf(totalFitVal);
	idx = 0;

	while((idx < popSize-1) && (tmp > sList[idx]->fitness))
		tmp -= sList[idx++]->fitness;

	return idx;
}

void Pop::setThreadCount(int tc)
{
	int i, mainCount, lastCount;

	if(evalList != NULL){
		for(i=0;i<evalCount;i++){
			delete evalList[i];
			free(evalArgs[i]->solnList);
			free(evalArgs[i]);
		}
		
		free(evalList);
		free(evalArgs);
	}

	printf("Setting Number of threads to %d\n", tc);

	evalCount = tc;

	/* ********************************************* */
	/* Try to evenly distribute the pop members on   */
	/* the threads.  If there are any left over, the */
	/* last thread will get the extras.              */
	/* ********************************************* */

	mainCount = popSize / evalCount;
	lastCount = popSize % evalCount;
	lastCount += mainCount;


	evalList = (threadWrp **)malloc(evalCount * sizeof(threadWrp *));
	if(evalList == NULL){
		printf("Error allocating memory for thread list\n");
		exit(1);
	}

	evalArgs = (popthread_arg **)malloc(evalCount * sizeof(popthread_arg *));
	if(evalList == NULL){
		printf("Error allocating memory for thread arg list\n");
		exit (1);
	}


	for(i=0;i<evalCount;i++){
		evalList[i] = new threadWrp();

		evalArgs[i] = (popthread_arg *)malloc(sizeof(thread_arg));
		if(i!=(evalCount - 1)){
			evalArgs[i]->solnList = (Soln **)malloc(mainCount * sizeof(Soln *));
			evalArgs[i]->solnCount = mainCount;
		} else {
			evalArgs[i]->solnList = (Soln **)malloc(lastCount * sizeof(Soln *));
			evalArgs[i]->solnCount = lastCount;
		}

	}
	
}

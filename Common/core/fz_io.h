#ifndef FZ_IO
#define FZ_IO

/* ********************************************************* */
/* This object will demonstrate the ability to load a fuzzy  */
/* definition file and the cbc runtime dll to execute the    */
/* pass 4 cost evaluation.  We will abstract this file into  */
/* a generic fuzzy object as a later step.                   */
/* ********************************************************* */

#include "c:\\cbc\\include\\wfz.h"
#include "c:\\cbc\\examples\\pass1.h"


class P1fzio {
	public:

		P1fzio();
		P1fzio(char *fname);

		virtual ~P1fzio();

		float getCost(float mvmt, float inven, float loc_type);

	protected:

		double *mem_inputs;
		double *mem_outputs;

		double mem_min;
		double mem_max;

		char *mem_FileName;

		int mem_fzChannel;

		HINSTANCE mem_fzInstance;

		int (__stdcall *mem_fzInit)(char *file);
		int (__stdcall *mem_fzClose)(int channel);
		int (__stdcall *mem_fzEval)(int channel, FZ_NUMERIC *input,
			FZ_NUMERIC *output);

};

#endif /* FZ_IO defined */ 	

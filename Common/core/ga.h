#ifndef GA
#define GA

#include "pop.h"

/* *************************************************************** */
/* This is the class definition for the c++ GA implementation.     */
/* *************************************************************** */

class ga {
	public:
		ga(Pop *p);
		ga(ga *g);
		virtual ~ga();

		void init(void);
		float evolve(int gen_count);
		void display(void);
		Soln *getBestSoln(void);
		virtual ga *duplicate(void);
		void receiveNewSoln(Soln *s, int pos);

	protected:

		int generation;
	
		Pop *newPop;
		Pop *oldPop;

};

#endif /* GA Defined */

#ifndef RANDOBJ
#define RANDOBJ

#define MAX(A,B) A > B ? A : B
#define MIN(A,B) A < B ? A : B

class RandObj {
	public:
		RandObj();
		RandObj(int seed);
		virtual ~RandObj();

		float getRandf(void);                      /* 0..1 random float */
		float getRandf(float max);                 /* 0..max random float */
		float getRandf(float min, float max);      /* min..max random float */
		
		int   getRandi();	                         /* 0..MAXINT random integer */
		int   getRandi(int max);	                 /* 0..max random integer */
		int   getRandi(int min, int max);	         /* min..max random integer */

		void  setSeed(int seed);

	protected:

		long   seed;
		long   IM1;
		long   IM2;
		long   IMM1;
		long   IA1;
		long   IA2;
		long   IQ1;
		long   IQ2;
		long   IR1;
		long   IR2;
		long   NTAB;
		long   NDIV;
		double EPS;
		double RNMX;
		double AM;

		long   idnum2;
		long   iy;
		long   iv[33];

};


#endif

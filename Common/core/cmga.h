#ifndef CMGA
#define CMGA

#include "ga.h"
#include "threadwrapper.h"

typedef struct {
	ga *g;
	int cyc;
} ga_arg;


class cmga {
	public:
		cmga(ga *g, int thread_count);
		virtual ~cmga();

		void setGACycles(int cyc);
		void setGANoChange(int cyc);
		void setXchngRate(int xchng);

		float exe(int cyc);		

	protected:

		ga **ga_list;
		int  ga_count;

		threadWrp **thread_list;
		ga_arg    *thread_args;

		int ga_cycles;
		int ga_no_change;
		int ga_xchng_rate;
	
};

#endif /* CMGA defined */

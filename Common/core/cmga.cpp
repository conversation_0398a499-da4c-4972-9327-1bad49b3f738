#include <stdlib.h>
#include <stdio.h>
#include <string.h>

#include "cmga.h"
#include "threadwrapper.h"

void *thread_fcn(void *arg)
{
	ga *localg;
	int count;
	float fit;

	localg = ((ga_arg *)arg)->g;
	count = ((ga_arg *)arg)->cyc;

	fit = localg->evolve(count);
	
	return NULL;
}

cmga::cmga(ga *g, int tc)
{
	int i;	

	ga_count = tc;

	ga_list = (ga **)malloc(ga_count * sizeof(ga *));
	if(ga_list == NULL){
		printf("Error allocating memory for the ga_list!\n");
		exit(1);
	}

	thread_list = (threadWrp **)malloc(ga_count * sizeof(threadWrp *));
	if(thread_list == NULL){
		printf("Error allocating memory for the thread_list!\n");
		exit(1);
	}
	
	thread_args = (ga_arg *)malloc(ga_count * sizeof(ga_arg));
	if(thread_args == NULL){
		printf("Error allocating memory for the thread_args!\n");
		exit(1);
	}
	
	ga_list[0] = g;
	for(i=1;i<ga_count;i++){
		ga_list[i] = ga_list[0]->duplicate();
	}

	for(i=0;i<ga_count;i++){
		thread_list[i] = new threadWrp();
	}

	/* ************************************************************** */
	/* Some defaults for saftey.                                      */
	/* **********************************************************s.c. */
	ga_cycles = 50;
	ga_no_change = 5;     /* Not implemented yet */
	ga_xchng_rate = 1;    /* Not implemented yet */
	
}	

cmga::~cmga()
{
	int i;

	for(i=0;i<ga_count;i++){
		delete ga_list[i];
		delete thread_list[i];
	}

	free(ga_list);
	free(thread_list);
	free(thread_args);

}

void cmga::setGACycles(int cyc)
{
	ga_cycles = cyc;
}

void cmga::setGANoChange(int cyc)
{
	ga_no_change = cyc;
}

void cmga::setXchngRate(int xchng)
{
	ga_xchng_rate = xchng;
}	

float cmga::exe(int cyc)
{
	
	int i, j, k;
	float best_fit;
	Soln *s;


	for(i=0;i<cyc;i++){
		for(j=0;j<ga_count;j++){
			thread_args[j].g = ga_list[j];
			thread_args[j].cyc = ga_cycles;

			thread_list[j]->start(thread_fcn, &thread_args[j]);
		}

		for(j=0;j<ga_count;j++){
			thread_list[j]->join();
		}	
		
		/* ***************************************** */
		/* Here is the cross pollenation stuff.      */
		/* *************************************s.c. */

		for(j=0;j<ga_count;j++){
			s = ga_list[j]->getBestSoln();
			for(k=0;k<ga_count;k++){
				if(k!=j){
					ga_list[k]->receiveNewSoln(s, (j*2)+1);
					ga_list[k]->receiveNewSoln(s, (j*2)+2);
				}
			}
		}
	}
						
	s = ga_list[0]->getBestSoln();
	best_fit = s->fitness;

	for(i=1;i<ga_count;i++){
		s = ga_list[i]->getBestSoln();
		if(best_fit < s->fitness){
			best_fit = s->fitness;
		}
	}

	return best_fit;
}


/*
 | wfz.h - definitions for RTC/Windows DLL runtime code
 |
 | Copyright (C) 1991-1993 HyperLogic Corporation.  All rights reserved.
*/



#ifdef _WIN32
#define RTDX _stdcall
#else
#define RTDX _far _pascal
#endif



			/*
			 | standard definitions for image file data
			 |
			 | FZ_IOIDX - the type of indices for all variables
			 | FZ_NUMERIC - the data type for all variables
			*/

# ifndef __NOWFZTYPES__			/* for internal use */

typedef unsigned int FZ_IOIDX;

typedef double FZ_NUMERIC;

# endif


			/*
			 | return values used by the RTC DLL functions
			*/

# define WFZ_NO_ERROR				(0)
# define WFZ_TOO_MANY_INSTANCES		(-1)
# define WFZ_PANIC					(-2)
# define WFZ_FILENAME				(-3)
# define WFZ_OPEN					(-4)
# define WFZ_FILE_SEEK				(-5)
# define WFZ_FILE_READ				(-6)
# define WFZ_FILE_FORMAT			(-7)
# define WFZ_CHECKSUM				(-8)
# define WFZ_NO_MEMORY				(-9)
# define WFZ_FILE_CORRUPT			(-10)
# define WFZ_BAD_CHANNEL			(-11)
# define WFZ_CHANNEL_NOT_OPEN		(-12)
# define WFZ_ILLEGAL_BANK			(-13)
# define WFZ_ILLEGAL_INDEX			(-14)
# define WFZ_BAD_ARGUMENT			(-15)
# define WFZ_NO_SUCH_VAR			(-16)
# define WFZ_NO_STRINGS				(-17)
# define WFZ_CONSTANT_WT			(-18)



			/*
			 | type arguments for wfz_get_index
			*/

# define FZTYPE_INPUT				(1)
# define FZTYPE_OUTPUT				(2)
# define FZTYPE_WEIGHT				(3)
# define FZTYPE_ACTIVATION			(4)



			/*
			 | prototypes for the RTC DLL functions
			 |
			 | #define __NOWFZPROTO__ to exclude (for internal use)
			*/

# ifndef __NOWFZPROTO__

int RTDX wfz_init(char FAR *file);
int RTDX wfz_close(int channel);
int RTDX wfz_eval(int channel, FZ_NUMERIC FAR *input,
													FZ_NUMERIC FAR *output);
int RTDX wfz_set_bank(int channel, unsigned int select);
int RTDX wfz_set_weight(int channel,
									 FZ_IOIDX index, FZ_NUMERIC weight);
int RTDX wfz_get_weight(int channel,
								 	FZ_IOIDX index, FZ_NUMERIC FAR *weight);
int RTDX wfz_get_activation(int channel,
									FZ_IOIDX index, FZ_NUMERIC FAR *rtnvalue);

int RTDX wfz_get_index(int channel, int req_type,
									char FAR *name, FZ_IOIDX FAR *pindex);

# endif


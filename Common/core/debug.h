#ifndef SLOT_DEBUG
#define SLOT_DEBUG 0
#endif

/////////////////////////////////////////////////////////
// Pass 4 logging.  Set values = 1 to turn on different
// logging channels.  SLOT_DEBUG = 0 turns everything off
// regardless of individual settings.
/////////////////////////////////////////////////////////

#ifndef SLOT_ERROR
#define SLOT_ERROR 1
#endif

#ifndef SLOT_DATA_IN
#define SLOT_DATA_IN 1
#endif

#ifndef SLOT_DATA_OUT
#define SLOT_DATA_OUT 1
#endif

#ifndef SLOT_RESULT
#define SLOT_RESULT 1
#endif

#ifndef SLOT_ANALYZE
#define SLOT_ANALYZE 1
#endif

#ifndef SLOT_REJECT
#define SLOT_REJECT 1
#endif

#ifndef SLOT_REORIENT
#define SLOT_REORIENT 1
#endif

#ifndef SLOT_METHOD
#define SLOT_METHOD 1
#endif

/////////////////////////////////////////////////////////
// End Pass 4 logging.
/////////////////////////////////////////////////////////

#ifndef SLOT_DIVISOR
#define SLOT_DIVISOR 1728
#endif

#ifndef NOLOGGING
#define NOLOGGING	0
#endif
#ifndef USRLOGGING
#define USRLOGGING	1
#endif
#ifndef DBGLOGGING
#define DBGLOGGING	2	
#endif

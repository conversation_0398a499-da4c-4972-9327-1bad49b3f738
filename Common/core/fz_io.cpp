#include "afxwin.h"
#include "fz_io.h"
#include <stdio.h>
#include <stdlib.h>


P1fzio::P1fzio()
{
	/* ************************************************** */
	/* Constructor without a filename doesn't do much for */
	/* us right now, so I won't do much in the way of any */
	/* code for it.                                       */
	/* ************************************************** */

}

P1fzio::P1fzio(char *fname)
{
	mem_FileName = (char *)malloc(512);
	strcpy(mem_FileName, fname);        
	/* <PERSON><PERSON> can now get rid of fname and we don't care */



	/* ******************************************* */
	/* Load up the fuzzy dll.                      */
	/* ******************************************* */
	
	mem_fzInstance = LoadLibrary("c:\\cbc\\lib\\cbc20r32.dll");
	if(mem_fzInstance == NULL){
		printf("Load of the fuzzy dll failed.\n");
		exit(1);
	}

	mem_fzInit = (int (__stdcall *)(char *))GetProcAddress(mem_fzInstance, "wfz_init"); 
	if(mem_fzInit == NULL){
		printf("Resolve of fuzzy init function failed.\n");
		exit(1);
	}

	mem_fzClose = (int (__stdcall *)(int ))GetProcAddress(mem_fzInstance, "wfz_close");
	if(mem_fzClose == NULL){
		printf("Resolve of fuzzy close function failed\n");
		exit(1);
	}

	mem_fzEval = (int (__stdcall *)(int, FZ_NUMERIC *, FZ_NUMERIC *))GetProcAddress(mem_fzInstance, "wfz_eval");
	if(mem_fzEval == NULL){
		printf("Resolve of fuzzy evaluation function failed\n");
		exit(1);
	}

	mem_inputs = (double *)malloc(P1_INPUT_COUNT * sizeof(double));
	mem_outputs = (double *)malloc(P1_OUTPUT_COUNT * sizeof(double));

	mem_min = 0;
	mem_max = 10000;

	mem_fzChannel = mem_fzInit(mem_FileName);

	/* ****************************************************** */
	/* fuzzy primed and ready to go.                          */
	/* ****************************************************** */

}

P1fzio::~P1fzio()
{
	mem_fzClose(mem_fzChannel);

	FreeLibrary(mem_fzInstance);

	free(mem_inputs);
	free(mem_outputs);
	free(mem_FileName);	
}

float P1fzio::getCost(float mvmt, float inven, float loc_type)
{
	mem_inputs[0] = loc_type;
	mem_inputs[1] = mvmt;
	mem_inputs[2] = inven;

	mem_outputs[0] = 0.0;

	mem_fzEval(mem_fzChannel, mem_inputs, mem_outputs);

	return (float)mem_outputs[0];
}

#include "afxwin.h"
#include "process.h"
#include "threadwrapper.h"


UINT __stdcall internal_thread_start_fcn(void *parms)
{
	thread_arg *args;

	args = (thread_arg *)parms;

	/* ******************************************** */
	/* Then launch the thread by calling the acutal */
	/* user-supplied starting function.             */
	/* ******************************************** */
	args->jmp(args->argList);

	_endthreadex(0);

	return 0;
}

threadWrp::threadWrp()
{
	mem_status = 0;
	mem_arg = (thread_arg *) malloc (sizeof(thread_arg));
}

threadWrp::~threadWrp()
{
	if(mem_status == 1){
		this->cancel();
	}

	free(mem_arg);
}

int threadWrp::start(void *(*jmpPoint)(void *), void *argList, void *attr)
{
	unsigned int res;

	mem_arg->jmp = jmpPoint;
	mem_arg->argList = argList;

	mem_thread = (HANDLE)_beginthreadex(NULL, 0, internal_thread_start_fcn, 
		mem_arg, 0, &res);

	if(mem_thread != 0){
		mem_status = 1;
		return 0;
	} else {
		mem_status = -1;
		return -1;
	}

}

int threadWrp::cancel(void)
{

	TerminateThread(mem_thread, 0);

	return 0;
}

int threadWrp::join(void **ret_status)
{
	/* **************************************** */
	/* Joining on this thread simply means that */
	/* we will wait for the user function to    */
	/* finish.  That will be signaled by the    */
	/* operating system when the thread is done */
	/* Use the WaitForSingleObject to watch for */
	/* this condition.                          */
	/* **************************************** */
	
	if(mem_status == 1){
		WaitForSingleObject(mem_thread, INFINITE);
		return 0;
	}

	return -1;
}

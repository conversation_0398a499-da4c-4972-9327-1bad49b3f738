#ifndef SOLN
#define SOLN

#include "randobj.h"

/* ******************************************************************* */
/* This is the class definition for the c++ solution implementation.   */
/* ******************************************************************* */

class Soln {
	public:
		Soln();
		Soln(Soln *s);
		virtual ~Soln();

		void *rep;
		float fitness;

		virtual float doFitEval(void);
		virtual void doCrossOver(Soln *, Soln *);
		virtual void doMutate(void);
		virtual void *duplicate(void);
		virtual void init(void);
		virtual void display(void);
		virtual void defineSoln(Soln *);

		RandObj *rnd;

	protected:
		int elemSize;
		int elemCount;

};

#endif /* SOLN Defined */

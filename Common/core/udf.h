#ifndef UDF
#define UDF

/* ********************************************************************* */
/* This is the C++ definition for the udf object.  This is the 'user     */
/* defined object' that is at the heart of the slotting project.  The    */
/* object itself is very simple.  It contains all of the necessary data  */
/* types, and an indicator to determine which data type is currently the */
/* active type.                                                          */
/* ********************************************************************* */
/* This definition is only temporary.  We will flush this definition out */
/* much more in the future.                                              */
/* ********************************************************************* */

#define MAX_CHAR 512

class UDF {
	public:

		UDF();
		UDF(float val);
		UDF(int val);
		UDF(char *val);

	protected:

		float mem_fval;
		int   mem_ival;
		char *mem_cval;

		int   mem_type;  // 0 = float; 1 = int; 2 = char *
};

#endif /* UDF defined */




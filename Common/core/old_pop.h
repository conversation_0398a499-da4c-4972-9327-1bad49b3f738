#ifndef POP
#define POP

#include "soln.h"

/* ****************************************************************** */
/* This is the class definition for the c++ Population implementation */
/* ****************************************************************** */

class Pop {
	public:
		Pop();
		Pop(int pSize);
		Pop(int pSize, Soln *starter);
		Pop(Pop *p);
		virtual ~Pop();

		void initialize(void);
		void evaluate(void);
		void  initScale(float x1, float x2, float y1, float y2); 
		float doScale(float val);
		int select(void);
		virtual Pop *duplicate(void);

		int popSize;
		Soln **sList;

		int bestFitIdx;
		float bestFitVal;

		RandObj *rnd;

	protected:
		int highFitIdx;
		int lowFitIdx;
	
		int minIsBest;

		float highFitVal;
		float lowFitVal;

		float totalFitVal;
		
		float scaleFact;
		float scaleOff;
		int   scaleInit;
};

#endif /* POP Defined */
			
	

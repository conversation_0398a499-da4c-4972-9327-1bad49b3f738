#pragma once

LPSTR makeSessionEnSO(LPSTR sStr);

class SSAEncrypt
{
//public:
	//SSAEncrypt(void);
	//~SSAEncrypt(void);

private:
	char sTemp[100];
	LPSTR ret;

public:
	LPSTR guessGraphSO(LPSTR  sStr);
	LPSTR guessSessionSO(LPSTR  sStr);
	LPSTR minGraphSO(LPSTR  sStr);
	LPSTR minSessionSO(LPSTR  sStr);
	LPSTR maxGraphSO(LPSTR  sStr);
	LPSTR maxSessionSO(LPSTR  sStr);
	LPSTR tuneGraphSO(LPSTR  sStr);
	LPSTR tuneSessionSO(LPSTR  sStr);

	LPSTR getGraphEnSO(LPSTR  sStr) ;
	LPSTR getGraphDeSO(LPSTR  sStr);
	LPSTR putGraphEnSO(LPSTR  sStr);
	LPSTR putGraphDeSO(LPSTR  sStr);

	LPSTR gMakeGraphEnSO(LPSTR  sStr) ;
	LPSTR gMakeGraphDeSO(LPSTR  sStr);
	LPSTR pMakeGraphEnSO(LPSTR  sStr);
	LPSTR pMakeGraphDeSO(LPSTR  sStr);

	LPSTR getSessionEnSO(LPSTR  sStr);
	LPSTR getSessionDeSO(LPSTR  sStr);
	LPSTR getGetRegi(LPSTR  sStr1, LPSTR  sStr2);

	LPSTR gMakeSessionEnSO(LPSTR  sStr);
	LPSTR gMakeSessionDeSO(LPSTR  sStr);
	LPSTR gMakeGetRegi(LPSTR  sStr1, LPSTR  sStr2);

	LPSTR putGetRegi(LPSTR  sStr1, LPSTR  sStr2);
	LPSTR getPutRegi(LPSTR  sStr1, LPSTR  sStr2);
	LPSTR putPutRegi(LPSTR  sStr1, LPSTR  sStr2);
	LPSTR putSessionEnSO(LPSTR  sStr);
	LPSTR putSessionDeSO(LPSTR  sStr);

	LPSTR pMakeGetRegi(LPSTR  sStr1, LPSTR  sStr2);
	LPSTR gMakePutRegi(LPSTR  sStr1, LPSTR  sStr2);
	LPSTR pMakePutRegi(LPSTR  sStr1, LPSTR  sStr2);
	LPSTR pMakeSessionEnSO(LPSTR  sStr);
	LPSTR pMakeSessionDeSO(LPSTR  sStr);

	LPSTR pickGraphSO(LPSTR  sStr);
	LPSTR pickSessionSO(LPSTR  sStr);
	LPSTR routeGraphSO(LPSTR  sStr);
	LPSTR routeSessionSO(LPSTR  sStr);
	LPSTR stopGraphSO(LPSTR  sStr);
	LPSTR stopSessionSO(LPSTR  sStr);
	LPSTR startGraphSO(LPSTR  sStr);
	LPSTR startSessionSO(LPSTR  sStr);

};

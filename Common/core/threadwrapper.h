#include <afxmt.h>

#ifndef THREADWRAPPER
#define THREADWRAPPER

typedef struct {
	void *(*jmp)(void *);
	void *argList;
} thread_arg;

class threadWrp {
	public:
		
		threadWrp();
		virtual ~threadWrp();

		int start(void *(*jmpPoint)(void *), void *argList, 
			void *attr = NULL);

		int cancel(void);
		int join(void **ret_status = NULL);	

	protected:
	
		HANDLE    mem_thread;
		int       mem_status;  // 0 = not started, 1 = o.k., -1 = error in start.
		thread_arg *mem_arg;
};

#endif /* THREADWRAPPER Defined */

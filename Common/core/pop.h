#ifndef POP
#define POP

#include "soln.h"
#include "threadwrapper.h"

/* ****************************************************************** */
/* This is the class definition for the c++ Population implementation */
/* ****************************************************************** */

typedef struct {
	Soln **solnList;
	int  solnCount;
} popthread_arg;



class Pop {
	public:
		Pop();
		Pop(int pSize);
		Pop(int pSize, Soln *starter);
		Pop(Pop *p);
		virtual ~Pop();

		void initialize(void);
		void evaluate(void);
		void  initScale(float x1, float x2, float y1, float y2); 
		float doScale(float val);
		int select(void);
		virtual void *duplicate(void);

		void setThreadCount(int tc);

		int popSize;
		Soln **sList;

		int bestFitIdx;
		float bestFitVal;

		RandObj *rnd;

	protected:
		void eval_thread(void);
		void eval_nothread(void);

		int highFitIdx;
		int lowFitIdx;
	
		int minIsBest;

		float highFitVal;
		float lowFitVal;

		float totalFitVal;
		
		float scaleFact;
		float scaleOff;
		int   scaleInit;

		threadWrp **evalList;
		popthread_arg **evalArgs;
		int         evalCount;

		int   isDuplicate;

};

#endif /* POP Defined */

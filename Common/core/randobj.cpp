#include <time.h>
#include <limits.h>
#include "randobj.h"

RandObj::RandObj()
{
	IM1  = 2147483563;
	IM2  = 2147483399;
	IMM1 = IM1 - 1;
	IA1  = 40014;
	IA2  = 40692;
	IQ1  = 53668;
	IQ2  = 52774;
	IR1  = 12211;
	IR2  = 3791;
	NTAB = 32;
	NDIV = 1 + IMM1 / NTAB;
	EPS = 1.2e-7;
	RNMX = 1.0 - EPS;
	AM = 1.0 / IM1;
	
	seed = time(NULL);

	idnum2 = 123456789L;
	iy = 0L;

}

RandObj::RandObj(int sed)
{
	IM1  = 2147483563;
	IM2  = 2147483399;
	IMM1 = IM1 - 1;
	IA1  = 40014;
	IA2  = 40692;
	IQ1  = 53668;
	IQ2  = 52774;
	IR1  = 12211;
	IR2  = 3791;
	NTAB = 32;
	NDIV = 1 + IMM1 / NTAB;
	EPS = 1.2e-7;
	RNMX = 1.0 - EPS;
	AM = 1.0 / IM1;
	
	seed = sed;

	idnum2 = 123456789L;
	iy = 0L;
}

RandObj::~RandObj()
{
	// Nothing to do.
}

float RandObj::getRandf(void)
{
	long j,k;

	if (seed <= 0){
		seed = MAX(-seed, 1);
		idnum2 = seed;
		for(j=NTAB+8; j>=1; j--){
			k = seed / IQ1;
			seed = IA1 * (seed - k * IQ1) - k * IR1;
			if (seed < 0)
				seed += IM1;
      
			if (j <= NTAB)
				iv[j] = seed;
		}
		iy = iv[1];
	}
  
	k = seed / IQ1;
	seed = IA1 * (seed - k * IQ1) - k * IR1;
  
	if (seed < 0)
		seed += IM1;

	k = idnum2 / IQ2;
	idnum2 = IA2 * (idnum2 - k * IQ2) - k * IR2;
  
	if (idnum2 < 0)
		idnum2 += IM2;

	j = 1 + iy / NDIV;
	iy = iv[j] - idnum2;
	iv[j] = seed;
  
	if (iy < 1)
		iy += IMM1;

	return MIN(AM * iy, RNMX);
}

float RandObj::getRandf(float max)
{
	return (getRandf() * max);
}

float RandObj::getRandf(float min, float max)
{
	return (getRandf() * (max - min) + min);
}

int RandObj::getRandi()
{
	return ((int)(getRandf() * INT_MAX));
}

int RandObj::getRandi(int max)
{
	return ((int)(getRandf() * max));
}

int RandObj::getRandi(int min, int max)
{
	return ((int)(getRandf() * (max - min)) + min);
}

void RandObj::setSeed(int sed)
{
	seed = sed;
}
	





























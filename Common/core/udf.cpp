#include "udf.h"
#include <stdlib.h>
#include <stdio.h>

UDF::UDF()
{
	/* *************************************** */
	/* Default type is float.  Just chose it.  */
	/* *************************************** */

	mem_type = 0;
	mem_fval = 0;
}

UDF::UDF(float f)
{
	mem_type = 0;
	mem_fval = f;
}

UDF::UDF(int i)
{
	mem_type = 1;
	mem_ival = i;
}

UDF::UDF(char *c)
{
	mem_type = 2;
	mem_cval = (char *)malloc(MAX_CHAR);
	VERIFY(mem_cval != NULL)

	strcpy(mem_cval, c);
}

// Microsoft Visual C++ generated resource script.
//
#include "resource.h"

#define APSTUDIO_READONLY_SYMBOLS
/////////////////////////////////////////////////////////////////////////////
//
// Generated from the TEXTINCLUDE 2 resource.
//
#include "afxres.h"

/////////////////////////////////////////////////////////////////////////////
#undef APSTUDIO_READONLY_SYMBOLS

/////////////////////////////////////////////////////////////////////////////
// English (U.S.) resources

#if !defined(AFX_RESOURCE_DLL) || defined(AFX_TARG_ENU)
#ifdef _WIN32
LANGUAGE LANG_ENGLISH, SUBLANG_ENGLISH_US
#pragma code_page(1252)
#endif //_WIN32

#ifdef APSTUDIO_INVOKED
/////////////////////////////////////////////////////////////////////////////
//
// TEXTINCLUDE
//

1 TEXTINCLUDE 
BEGIN
    "resource.h\0"
END

2 TEXTINCLUDE 
BEGIN
    "#include ""afxres.h""\r\n"
    "\0"
END

3 TEXTINCLUDE 
BEGIN
    "#define _AFX_NO_SPLITTER_RESOURCES\r\n"
    "#define _AFX_NO_OLE_RESOURCES\r\n"
    "#define _AFX_NO_TRACKER_RESOURCES\r\n"
    "#define _AFX_NO_PROPERTY_RESOURCES\r\n"
    "\r\n"
    "#if !defined(AFX_RESOURCE_DLL) || defined(AFX_TARG_ENU)\r\n"
    "#ifdef _WIN32\r\n"
    "LANGUAGE 9, 1\r\n"
    "#pragma code_page(1252)\r\n"
    "#endif //_WIN32\r\n"
    "#include ""res\\OptConfig.rc2""  // non-Microsoft Visual C++ edited resources\r\n"
    "#include ""afxres.rc""         // Standard components\r\n"
    "#endif\r\n"
    "\0"
END

#endif    // APSTUDIO_INVOKED


/////////////////////////////////////////////////////////////////////////////
//
// Icon
//

// Icon with lowest ID value placed first to ensure application icon
// remains consistent on all systems.
IDR_MAINFRAME           ICON                    "res\\OptConfig.ico"

/////////////////////////////////////////////////////////////////////////////
//
// Dialog
//

IDD_ABOUTBOX DIALOG  0, 0, 235, 55
STYLE DS_SETFONT | DS_MODALFRAME | WS_POPUP | WS_CAPTION | WS_SYSMENU
CAPTION "About OptConfig"
FONT 8, "MS Sans Serif"
BEGIN
    ICON            IDR_MAINFRAME,IDC_STATIC,11,17,20,20
    LTEXT           "OptConfig Version 1.0",IDC_STATIC,40,10,119,8,
                    SS_NOPREFIX
    LTEXT           "Copyright (C) 2001",IDC_STATIC,40,25,119,8
    DEFPUSHBUTTON   "OK",IDOK,178,7,50,14,WS_GROUP
END

IDD_STATUS DIALOGEX 0, 0, 377, 263
STYLE DS_SETFONT | DS_MODALFRAME | WS_POPUP | WS_VISIBLE | WS_CAPTION | 
    WS_SYSMENU
EXSTYLE WS_EX_APPWINDOW
CAPTION "Status"
FONT 8, "MS Sans Serif", 0, 0, 0x1
BEGIN
    GROUPBOX        "Services",IDC_STATIC,7,7,363,249
    CONTROL         "List1",IDC_SERVICE_LIST,"SysListView32",LVS_REPORT | 
                    LVS_SHOWSELALWAYS | WS_BORDER | WS_TABSTOP,21,24,270,104
    PUSHBUTTON      "&Start",IDC_START,303,24,38,14
    PUSHBUTTON      "S&top",IDC_STOP,303,47,38,14
    PUSHBUTTON      "&Refresh",IDC_REFRESH,303,70,38,14
    PUSHBUTTON      "Reset &Optimize",IDC_RESET,22,135,56,14
END

IDD_SERVER DIALOGEX 0, 0, 377, 275
STYLE DS_SETFONT | DS_MODALFRAME | WS_POPUP | WS_VISIBLE | WS_CAPTION | 
    WS_SYSMENU
EXSTYLE WS_EX_APPWINDOW
CAPTION "Database"
FONT 8, "MS Sans Serif", 0, 0, 0x1
BEGIN
    COMBOBOX        IDC_ENGINE_LOG_LIST,114,144,48,30,CBS_DROPDOWNLIST | 
                    WS_VSCROLL | WS_TABSTOP
    CONTROL         "List1",IDC_DATABASE_LIST,"SysListView32",LVS_REPORT | 
                    WS_BORDER | WS_TABSTOP,207,147,139,63
    COMBOBOX        IDC_MRUDB_LIST,114,174,66,100,CBS_DROPDOWNLIST | 
                    CBS_SORT | WS_VSCROLL | WS_TABSTOP
    CONTROL         "",IDC_DATABASE_LOGGING,"Button",BS_AUTOCHECKBOX | 
                    WS_TABSTOP,114,204,24,8
    GROUPBOX        "",IDC_GROUP,7,7,363,116
    GROUPBOX        "",IDC_STATIC,7,126,363,142
    RTEXT           "Database Logging:",IDC_STATIC,32,204,72,8
    RTEXT           "Engine Log Mode:",IDC_STATIC,16,146,86,8
    LTEXT           "Databases:",IDC_STATIC,207,134,124,8
    RTEXT           "Default Database:",IDC_STATIC,33,175,69,8
    CONTROL         "",IDC_SERVICE_LIST,"SysListView32",LVS_REPORT | 
                    LVS_SHOWSELALWAYS | WS_BORDER | WS_TABSTOP,23,23,270,90
    PUSHBUTTON      "&Start",IDC_START,303,23,56,14
    PUSHBUTTON      "S&top",IDC_STOP,303,47,56,14
    PUSHBUTTON      "&Refresh",IDC_REFRESH,303,71,56,14
    LTEXT           "Database Status",IDC_STATIC,23,14,71,8
    RTEXT           "Log Files Folder:",IDC_STATIC,35,232,68,8
    EDITTEXT        IDC_DATABASE_LOGFILE,114,229,188,14,ES_AUTOHSCROLL
    PUSHBUTTON      "...",IDC_BROWSE,307,229,16,14
END

IDD_CLIENT DIALOGEX 0, 0, 377, 275
STYLE DS_SETFONT | DS_MODALFRAME | WS_POPUP | WS_VISIBLE | WS_CAPTION | 
    WS_SYSMENU
EXSTYLE WS_EX_APPWINDOW
CAPTION "Main"
FONT 8, "MS Sans Serif", 0, 0, 0x1
BEGIN
    GROUPBOX        "",IDC_STATIC,7,7,363,116
    RTEXT           "User ID:",IDC_STATIC,217,151,39,8
    RTEXT           "Password:",IDC_STATIC,204,178,52,8
    RTEXT           "Debug:",IDC_STATIC,204,231,52,8
    GROUPBOX        "",IDC_STATIC,7,130,363,138
    EDITTEXT        IDC_USERNAME,267,151,60,14,ES_AUTOHSCROLL
    EDITTEXT        IDC_PASSWORD,267,178,60,14,ES_PASSWORD | ES_AUTOHSCROLL
    CONTROL         "",IDC_DEBUG,"Button",BS_AUTOCHECKBOX | WS_TABSTOP,267,
                    230,16,8
    CONTROL         "List1",IDC_VERSION_LIST,"SysListView32",LVS_REPORT | 
                    WS_BORDER | WS_TABSTOP,21,22,341,60
    LTEXT           "Program Information:",IDC_STATIC,20,13,85,8
    RTEXT           "Automatic Login:",IDC_STATIC,188,207,68,8
    CONTROL         "",IDC_AUTO_CHECK,"Button",BS_AUTOCHECKBOX | WS_TABSTOP,
                    267,207,16,8
    RTEXT           "Physical:",IDC_STATIC,35,97,28,8
    RTEXT           "Virtual:",IDC_STATIC,208,97,28,8
    LTEXT           "",IDC_PHYSICAL,67,97,83,8
    LTEXT           "",IDC_VIRTUAL,241,97,83,8
    GROUPBOX        "Memory Available/Total (K)",IDC_STATIC,21,86,343,30
    RTEXT           "Session Log Output:",IDC_STATIC,27,174,65,8
    CONTROL         "File (overwrite)",IDC_LOG_OVERWRITE,"Button",
                    BS_AUTORADIOBUTTON,101,173,67,10
    CONTROL         "File (no overwrite)",IDC_LOG_NOOVERWRITE,"Button",
                    BS_AUTORADIOBUTTON,101,196,77,10
    RTEXT           "Copy Buffer (Byte):",IDC_STATIC,27,222,62,8
    EDITTEXT        IDC_COPY_BUFFER,101,222,60,14,ES_AUTOHSCROLL
END

IDD_PASSWORD DIALOG  0, 0, 187, 82
STYLE DS_SETFONT | DS_MODALFRAME | WS_POPUP | WS_CAPTION | WS_SYSMENU
CAPTION "Logon"
FONT 8, "MS Sans Serif"
BEGIN
    EDITTEXT        IDC_USER,66,13,78,14,ES_AUTOHSCROLL
    EDITTEXT        IDC_PASSWORD,66,35,78,14,ES_PASSWORD | ES_AUTOHSCROLL
    DEFPUSHBUTTON   "OK",IDOK,56,61,29,14
    PUSHBUTTON      "Cancel",IDCANCEL,101,61,29,14
    LTEXT           "User:",IDC_STATIC,21,16,17,8
    LTEXT           "Password:",IDC_STATIC,21,38,33,8
END

IDD_PROGRESS DIALOG  0, 0, 187, 97
STYLE DS_SETFONT | DS_MODALFRAME | WS_POPUP | WS_CAPTION
CAPTION "Testing Server"
FONT 8, "MS Sans Serif"
BEGIN
    CONTROL         "Progress1",IDC_PROGRESS,"msctls_progress32",WS_BORDER,
                    53,43,80,14
    CTEXT           "",IDC_MESSAGE,19,26,150,11
    PUSHBUTTON      "Stop",IDC_STOP,77,73,33,14
END

IDD_DIALOG1 DIALOGEX 0, 0, 186, 90
STYLE DS_SETFONT | DS_MODALFRAME | DS_FIXEDSYS | WS_POPUP | WS_CAPTION | 
    WS_SYSMENU
CAPTION "Dialog"
FONT 8, "MS Shell Dlg", 400, 0, 0x1
BEGIN
    DEFPUSHBUTTON   "OK",IDOK,129,7,50,14
    PUSHBUTTON      "Cancel",IDCANCEL,129,24,50,14
END


/////////////////////////////////////////////////////////////////////////////
//
// Version
//

VS_VERSION_INFO VERSIONINFO
 FILEVERSION 3,0,0,0
 PRODUCTVERSION 3,0,0,0
 FILEFLAGSMASK 0x3fL
#ifdef _DEBUG
 FILEFLAGS 0x1L
#else
 FILEFLAGS 0x0L
#endif
 FILEOS 0x4L
 FILETYPE 0x1L
 FILESUBTYPE 0x0L
BEGIN
    BLOCK "StringFileInfo"
    BEGIN
        BLOCK "040904b0"
        BEGIN
            VALUE "FileDescription", "OptConfig MFC Application"
            VALUE "FileVersion", "3, 0, 0, 0"
            VALUE "InternalName", "OptConfig"
            VALUE "LegalCopyright", "(c) Copyright 2006 by SSA Global"
            VALUE "OriginalFilename", "OptConfig.EXE"
            VALUE "ProductName", "Optimize"
            VALUE "ProductVersion", "3, 0, 0, 0"
        END
    END
    BLOCK "VarFileInfo"
    BEGIN
        VALUE "Translation", 0x409, 1200
    END
END


/////////////////////////////////////////////////////////////////////////////
//
// DESIGNINFO
//

#ifdef APSTUDIO_INVOKED
GUIDELINES DESIGNINFO 
BEGIN
    IDD_ABOUTBOX, DIALOG
    BEGIN
        LEFTMARGIN, 7
        RIGHTMARGIN, 228
        TOPMARGIN, 7
        BOTTOMMARGIN, 48
    END

    IDD_STATUS, DIALOG
    BEGIN
        LEFTMARGIN, 7
        RIGHTMARGIN, 370
        TOPMARGIN, 7
        BOTTOMMARGIN, 256
        HORZGUIDE, 38
    END

    IDD_SERVER, DIALOG
    BEGIN
        LEFTMARGIN, 7
        RIGHTMARGIN, 370
        TOPMARGIN, 7
        BOTTOMMARGIN, 268
    END

    IDD_CLIENT, DIALOG
    BEGIN
        LEFTMARGIN, 7
        RIGHTMARGIN, 370
        TOPMARGIN, 7
        BOTTOMMARGIN, 268
    END

    IDD_PASSWORD, DIALOG
    BEGIN
        LEFTMARGIN, 7
        RIGHTMARGIN, 180
        TOPMARGIN, 6
        BOTTOMMARGIN, 75
    END

    IDD_PROGRESS, DIALOG
    BEGIN
        LEFTMARGIN, 7
        RIGHTMARGIN, 180
        VERTGUIDE, 90
        TOPMARGIN, 7
        BOTTOMMARGIN, 90
    END

    IDD_DIALOG1, DIALOG
    BEGIN
        LEFTMARGIN, 7
        RIGHTMARGIN, 179
        TOPMARGIN, 7
        BOTTOMMARGIN, 83
    END
END
#endif    // APSTUDIO_INVOKED


/////////////////////////////////////////////////////////////////////////////
//
// String Table
//

STRINGTABLE 
BEGIN
    IDS_ABOUTBOX            "&About OptConfig..."
END

#endif    // English (U.S.) resources
/////////////////////////////////////////////////////////////////////////////



#ifndef APSTUDIO_INVOKED
/////////////////////////////////////////////////////////////////////////////
//
// Generated from the TEXTINCLUDE 3 resource.
//
#define _AFX_NO_SPLITTER_RESOURCES
#define _AFX_NO_OLE_RESOURCES
#define _AFX_NO_TRACKER_RESOURCES
#define _AFX_NO_PROPERTY_RESOURCES

#if !defined(AFX_RESOURCE_DLL) || defined(AFX_TARG_ENU)
#ifdef _WIN32
LANGUAGE 9, 1
#pragma code_page(1252)
#endif //_WIN32
#include "res\OptConfig.rc2"  // non-Microsoft Visual C++ edited resources
#include "afxres.rc"         // Standard components
#endif

/////////////////////////////////////////////////////////////////////////////
#endif    // not APSTUDIO_INVOKED


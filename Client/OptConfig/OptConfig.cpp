// OptConfig.cpp : Defines the class behaviors for the application.
//

#include "stdafx.h"
#include "OptConfig.h"
#include "OptConfigDlg.h"
#include "ClientPage.h"
#include "ServerPage.h"
#include "StatusPage.h"
#include "Password.h"
#include "GenericPropertySheet.h"

#include <winsock2.h>
#include <afxsock.h>


#ifdef _DEBUG
#define new DEBUG_NEW
#undef THIS_FILE
static char THIS_FILE[] = __FILE__;
#endif

/////////////////////////////////////////////////////////////////////////////
// COptConfigApp

CStringArray g_ConnectionList;
typedef DWORD (APIENTRY *MYPROC)(HWND,LPNETRESOURCEA,LPCSTR,LPCSTR,DWORD); 
typedef DWORD  (APIENTRY *MYPROC2)(LPCTSTR, DWORD, BOOL);
CString GetLocalHostname();
DWORD ConnectToServer(CString &serverName);
DWORD DisconnectFromServer(const CString &serverName);
CMapStringToString m_PasswordMap;

BEGIN_MESSAGE_MAP(COptConfigApp, CWinApp)
	//{{AFX_MSG_MAP(COptConfigApp)
		// NOTE - the ClassWizard will add and remove mapping macros here.
		//    DO NOT EDIT what you see in these blocks of generated code!
	//}}AFX_MSG
	ON_COMMAND(ID_HELP, CWinApp::OnHelp)
END_MESSAGE_MAP()

/////////////////////////////////////////////////////////////////////////////
// COptConfigApp construction

COptConfigApp::COptConfigApp()
{
	// TODO: add construction code here,
	// Place all significant initialization in InitInstance
}

/////////////////////////////////////////////////////////////////////////////
// The one and only COptConfigApp object

COptConfigApp theApp;

/////////////////////////////////////////////////////////////////////////////
// COptConfigApp initialization

BOOL COptConfigApp::InitInstance()
{
	AfxEnableControlContainer();
	AfxSocketInit(NULL);

	// Standard initialization
	// If you are not using these features and wish to reduce the size
	//  of your final executable, you should remove from the following
	//  the specific initialization routines you do not need.
    if (!AfxOleInit()) {
        AfxMessageBox(_T("Failed to Initialize OLE"));
        return FALSE;
    }

    if (!InitSecurity()) {
        AfxMessageBox(_T("Failed to adjust security"));
        return FALSE;
    }

#ifdef _AFXDLL
	Enable3dControls();			// Call this when using MFC in a shared DLL
#else
	Enable3dControlsStatic();	// Call this when linking to MFC statically
#endif
	CClientPage dlg1;
	CServerPage dlg2;
	CStatusPage dlg3;

	CGenericPropertySheet sheet;
	sheet.SetTitle("Optimize Configuration");
	sheet.AddPage(&dlg1);
	sheet.AddPage(&dlg2); 
	//sheet.AddPage(&dlg3); Lester disabled this page


	m_pMainWnd = &sheet;
	int nResponse = sheet.DoModal();
	if (nResponse == IDOK)
	{
		// TODO: Place code here to handle when the dialog is
		//  dismissed with OK
	}
	else if (nResponse == IDCANCEL)
	{
		// TODO: Place code here to handle when the dialog is
		//  dismissed with Cancel
	}

	for (int i=0; i < g_ConnectionList.GetSize(); ++i) {
		DisconnectFromServer(g_ConnectionList[i]);
	}

	// Since the dialog has been closed, return FALSE so that we exit the
	//  application, rather than start the application's message pump.
	return FALSE;
}

CString GetLocalHostname()
{
	CString hostname;
	WORD wVersionRequested;
	WSADATA wsaData;
	int err;
	
	wVersionRequested = MAKEWORD( 2, 2 );
	
	err = WSAStartup( wVersionRequested, &wsaData );
	if ( err != 0 ) {
		return "";
	}

	gethostname(hostname.GetBuffer(256), 256);
	hostname.ReleaseBuffer();

	WSACleanup();

	return hostname;

}


DWORD ConnectToServer(const CString &serverName)
{
	NETRESOURCE nr;
	CString remoteName = "\\\\" + serverName;

	nr.lpRemoteName = remoteName.GetBuffer(0);
	remoteName.ReleaseBuffer();
	nr.dwType = RESOURCETYPE_ANY;
	nr.lpLocalName = NULL;

	nr.lpProvider = NULL;
	DWORD rc;

	CWaitCursor cwc;

	// Connect as current user
	// See if they can open the registry
	// If yes, return okay
	// If not, disconnect user, prompt for password, connect as administrator
	// If they cancel from the password prompt, connect back as user
	// See if they have access
	
	HKEY hRemoteKey, hKey;

	for (int i=0; i < 2; ++i) {
		rc = RegConnectRegistry(remoteName, HKEY_LOCAL_MACHINE, &hRemoteKey);
		if (rc == ERROR_SUCCESS) {
			rc = RegOpenKeyEx(hRemoteKey, "Software\\Microsoft", 0, KEY_ALL_ACCESS, &hKey);
			if (rc == ERROR_SUCCESS) {
				// They already have access so return
				RegCloseKey(hKey);
				RegCloseKey(hRemoteKey);
				return 0;
			}
			RegCloseKey(hRemoteKey);
		}
		RegCloseKey(hRemoteKey);
		
		// They must not have access so try to connect as current user
		// The second time through we will try to connect with this user
		// If we fail again we will prompt for the password
		if (i == 0) {
			rc = WNetAddConnection3(NULL, &nr, NULL, NULL, 0);
			if (rc != 0)
				break;
		}
		
	}
	

	// Couldn't connect, disconnect and try to find admin password
	DisconnectFromServer(serverName);

	char user[25], password[25];
	CPassword dlg;
	CString temp;


	rc = ERROR_INVALID_PASSWORD;

	if (m_PasswordMap.Lookup(serverName, temp)) {
		strcpy(user, temp.Left(temp.Find("|")));
		strcpy(password, temp.Mid(temp.Find("|")));
		CWaitCursor cwc;
		rc = WNetAddConnection3(NULL, &nr, password, user, 0);
		if (rc == ERROR_INVALID_PASSWORD)
			m_PasswordMap.RemoveKey(serverName);
		else if (rc == NO_ERROR)
			g_ConnectionList.Add(serverName);
		else {
			return rc;
		}
	}

	while (rc == ERROR_INVALID_PASSWORD || rc == ERROR_LOGON_FAILURE) {	
		temp.Format("Enter login information for %s", serverName);
		dlg.m_Message = temp;
		if (dlg.DoModal() != IDOK) {
			return -1;
		}
		strcpy(user, dlg.m_User);
		strcpy(password, dlg.m_Password);
		CWaitCursor cwc;
		rc = WNetAddConnection3((HWND)NULL, &nr, (LPCSTR)password, (LPCSTR)user, (DWORD)0);
		if (rc == NO_ERROR) {
			g_ConnectionList.Add(serverName);
			temp.Format("%s|%s", user, password);
			m_PasswordMap.SetAt(serverName, temp);
		}

	}

	return rc;

}


DWORD DisconnectFromServer(const CString &serverName)
{


	CString remoteName = "\\\\" + serverName;
 
	DWORD rc;

	rc = WNetCancelConnection2(remoteName, 0, TRUE);

	return 0;
}

CString GetErrorText(DWORD error)
{
	LPVOID lpMsgBuf;
	CString text;


	FormatMessage( 
		FORMAT_MESSAGE_ALLOCATE_BUFFER | 
		FORMAT_MESSAGE_FROM_SYSTEM | 
		FORMAT_MESSAGE_IGNORE_INSERTS,
		NULL,
		error,
		MAKELANGID(LANG_NEUTRAL, SUBLANG_DEFAULT), // Default language
		(LPTSTR) &lpMsgBuf,
		0,
		NULL 
		);
	// Process any inserts in lpMsgBuf.
	// ...
	// Display the string.
	text = (LPCSTR)lpMsgBuf;
	text.TrimRight("\r\n");

	LocalFree( lpMsgBuf );
	
	return text;
}


void ShowLastError(const CString &extra = "")
{
	LPVOID lpMsgBuf;
	FormatMessage( 
		FORMAT_MESSAGE_ALLOCATE_BUFFER | 
		FORMAT_MESSAGE_FROM_SYSTEM | 
		FORMAT_MESSAGE_IGNORE_INSERTS,
		NULL,
		GetLastError(),
		MAKELANGID(LANG_NEUTRAL, SUBLANG_DEFAULT), // Default language
		(LPTSTR) &lpMsgBuf,
		0,
		NULL 
		);
	// Process any inserts in lpMsgBuf.
	// ...
	// Display the string.
	CString msg = (char *)lpMsgBuf;
	if (extra != "")
		msg += ":" + extra;

	MessageBox( NULL, msg, "Error", MB_OK | MB_ICONINFORMATION );
	// Free the buffer.
	LocalFree( lpMsgBuf );
	
}

void ShowError(DWORD errorNo)
{
	LPVOID lpMsgBuf;
	FormatMessage( 
		FORMAT_MESSAGE_ALLOCATE_BUFFER | 
		FORMAT_MESSAGE_FROM_SYSTEM | 
		FORMAT_MESSAGE_IGNORE_INSERTS,
		NULL,
		errorNo,
		MAKELANGID(LANG_NEUTRAL, SUBLANG_DEFAULT), // Default language
		(LPTSTR) &lpMsgBuf,
		0,
		NULL 
		);
	// Process any inserts in lpMsgBuf.
	// ...
	// Display the string.
	MessageBox( NULL, (LPCTSTR)lpMsgBuf, "Error", MB_OK | MB_ICONINFORMATION );
	// Free the buffer.
	LocalFree( lpMsgBuf );
	
}

bool COptConfigApp::InitSecurity(void)
{
	HRESULT hres;

    // Adjust the security to allow client impersonation.
    hres = CoInitializeSecurity(NULL, -1, NULL, NULL, 
        RPC_C_AUTHN_LEVEL_NONE, 
        RPC_C_IMP_LEVEL_IMPERSONATE, 
        NULL, 0, 0);

	CoRevertToSelf();

    return (SUCCEEDED(hres));
}

void ShowScreenHelp(const CString &helpTopic)
{
	STARTUPINFO si;
	PROCESS_INFORMATION pi;
	CString hlpCmd;
	HANDLE helpProcHandle = NULL;
	int funcRet;
	DWORD exitCode;

	hlpCmd = "winhlp32 -i " + helpTopic + " OptConfig.hlp";


	si.cb = sizeof(si);                           // size of the structure for versioning
	si.lpReserved = NULL;						  // should be NULL
	si.lpDesktop = NULL;						  // Desktop
	si.lpTitle = "Succeed Help";           // Title of the window
	si.dwX =  si.dwY = STARTF_USEPOSITION;		  // Use default start up position
	si.dwXSize = si.dwYSize = STARTF_USESIZE;     // Use default start up size.
	si.dwXCountChars = si.dwYCountChars = STARTF_USECOUNTCHARS; // Console window buffer lengths
	si.dwFillAttribute = STARTF_USEFILLATTRIBUTE;  // use default background colors etc.
	//***********************************************************************************************************
	// Set dwFlags to STARTF_USESHOWWINDOW so that we can manipulate the SW_HIDE etc.
	//***********************************************************************************************************
	si.dwFlags =  STARTF_USESHOWWINDOW;            // use show window to set the window state as in wShowWindow
	si.cbReserved2 = 0;							   // Should be 0
	si.lpReserved2 = NULL;						   // Should be NULL
	si.wShowWindow = SW_MINIMIZE;				   // Minimize the MS DOS window

	if ((funcRet = GetExitCodeProcess(helpProcHandle, &exitCode)) != 0)
		if (exitCode == STILL_ACTIVE)
			TerminateProcess(helpProcHandle, 0);

	BOOL bCreated = CreateProcess(NULL, hlpCmd.GetBuffer(0), NULL, NULL, TRUE,
				NORMAL_PRIORITY_CLASS, NULL, ".", &si, &pi);
	hlpCmd.ReleaseBuffer();
	if (!bCreated)
		AfxMessageBox("Help command failed!");

	helpProcHandle = pi.hProcess;

	return;
}

void ShowFieldHelp(const CString &helpTopic)
{
	STARTUPINFO si;
	PROCESS_INFORMATION pi;
	CString hlpCmd;
	HANDLE helpProcHandle = NULL;
	int funcRet;
	DWORD exitCode;

	hlpCmd = "winhlp32 -p -i " + helpTopic + " OptConfig.hlp";

	si.cb = sizeof(si);                           // size of the structure for versioning
	si.lpReserved = NULL;						  // should be NULL
	si.lpDesktop = NULL;						  // Desktop
	si.lpTitle = "Succeed Help";           // Title of the window
	si.dwX =  si.dwY = STARTF_USEPOSITION;		  // Use default start up position
	si.dwXSize = si.dwYSize = STARTF_USESIZE;     // Use default start up size.
	si.dwXCountChars = si.dwYCountChars = STARTF_USECOUNTCHARS; // Console window buffer lengths
	si.dwFillAttribute = STARTF_USEFILLATTRIBUTE;  // use default background colors etc.
	//***********************************************************************************************************
	// Set dwFlags to STARTF_USESHOWWINDOW so that we can manipulate the SW_HIDE etc.
	//***********************************************************************************************************
	si.dwFlags =  STARTF_USESHOWWINDOW;            // use show window to set the window state as in wShowWindow
	si.cbReserved2 = 0;							   // Should be 0
	si.lpReserved2 = NULL;						   // Should be NULL
	si.wShowWindow = SW_MINIMIZE;				   // Minimize the MS DOS window

	if ((funcRet = GetExitCodeProcess(helpProcHandle, &exitCode)) != 0)
		if (exitCode == STILL_ACTIVE)
			TerminateProcess(helpProcHandle, 0);

	BOOL bCreated = CreateProcess(NULL, hlpCmd.GetBuffer(0), NULL, NULL, TRUE,
				NORMAL_PRIORITY_CLASS, NULL, ".", &si, &pi);
	hlpCmd.ReleaseBuffer();
	if (!bCreated)
		AfxMessageBox("Help command failed!");

	helpProcHandle = pi.hProcess;

	return;
}


int GetWMIProperty(IWbemLocator *pIWbemLocator, IWbemServices *m_pIWbemServices,
								CString &server, CStringArray &propertyList)
{
	CString nameSpace;


	AfxOleInit();

	m_pIWbemServices = NULL;
	HRESULT hRes;

	nameSpace.Format("\\\\%s\\root\\cimv2", server);

 // Adjust the security to allow client impersonation.
    hRes = CoInitializeSecurity(NULL, -1, NULL, NULL, 
        RPC_C_AUTHN_LEVEL_NONE, 
        RPC_C_IMP_LEVEL_IMPERSONATE, 
        NULL, 0, 0);

	CoRevertToSelf();

    hRes = CoCreateInstance(CLSID_WbemLocator, NULL, CLSCTX_INPROC_SERVER, IID_IWbemLocator,
        (LPVOID *) &pIWbemLocator);
	if (hRes == S_OK) {
		
        // If already connected, release m_pIWbemServices.
        if (m_pIWbemServices)  
			m_pIWbemServices->Release();
		
        // Using the locator, connect to WMI in the given namespace.
        BSTR pNamespace = nameSpace.AllocSysString();
		
	
        hRes = pIWbemLocator->ConnectServer(pNamespace,
			NULL,    //using current account
			NULL,    //using current password
			0L,      // locale
			0L,      // securityFlags
			NULL,    // authority (NTLM domain)
			NULL,    // context
			&m_pIWbemServices);
		if (hRes == S_OK) {
        }
        else  {
			char szErrorDesc[512];
			
			FormatMessage(FORMAT_MESSAGE_FROM_SYSTEM, NULL, hRes, 
				MAKELANGID(LANG_NEUTRAL, SUBLANG_NEUTRAL), 
				szErrorDesc, sizeof(szErrorDesc), NULL);
			// Done with pNamespace.
			SysFreeString(pNamespace);
			
			// Done with pIWbemLocator. 
			pIWbemLocator->Release(); 
			propertyList[0] = szErrorDesc;
			return -1;
		}
		
        // Done with pNamespace.
        SysFreeString(pNamespace);
		
        // Done with pIWbemLocator. 
        pIWbemLocator->Release(); 
		
		// Switch security level to IMPERSONATE. 
		CoSetProxyBlanket(m_pIWbemServices,    // proxy
			RPC_C_AUTHN_WINNT,        // authentication service
			RPC_C_AUTHZ_NONE,         // authorization service
			NULL,                         // server principle name
			RPC_C_AUTHN_LEVEL_CALL,   // authentication level
			RPC_C_IMP_LEVEL_IMPERSONATE,    // impersonation level
			NULL,                         // identity of the client
			EOAC_NONE);               // capability flags

	}

    else  {
		propertyList[0] = "Failed to create IWbemLocator object";
		return -1;
	}
	

	BSTR className = SysAllocString(L"Win32_OperatingSystem");
    IEnumWbemClassObject *pEnumOS = NULL;
	
    hRes = m_pIWbemServices->CreateInstanceEnum(
        className,             // name of class
        0,
        NULL,
        &pEnumOS);    // pointer to enumerator
	
	if (FAILED(hRes)) {
		propertyList[0] = "Failed to enumerate operating systems.";
		return -1;
	}
	
	IWbemClassObject *pOS = NULL;
	ULONG uReturned;

	hRes = pEnumOS->Next(2000, 1, &pOS, &uReturned);
	if (FAILED(hRes) || uReturned != 1) {
		propertyList[0] = "Could not get operating system object.";
		return -1;
	}
	
	VARIANT pVal;
		
	for (int i=0; i < propertyList.GetSize(); ++i) {
		CString property = propertyList[i];
		VariantClear(&pVal);
		BSTR propName = property.AllocSysString();
		
		hRes = pOS->Get(propName, 0L, &pVal, NULL, NULL);
		
		if ( SUCCEEDED(hRes)) {
			CString value = CString(V_BSTR(&pVal));
			propertyList[i] = value;
		}
		else {
			propertyList[i] = "";
		}
	}


	if (pOS) 
		pOS->Release();

	if (pEnumOS)
		pEnumOS->Release();

	return 0;
}
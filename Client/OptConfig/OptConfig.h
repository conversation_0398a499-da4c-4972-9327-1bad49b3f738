// OptConfig.h : main header file for the OPTCONFIG application
//

#if !defined(AFX_OPTCONFIG_H__8D027F9C_2971_4829_8F25_6152070FA069__INCLUDED_)
#define AFX_OPTCONFIG_H__8D027F9C_2971_4829_8F25_6152070FA069__INCLUDED_

#if _MSC_VER > 1000
#pragma once
#endif // _MSC_VER > 1000

#ifndef __AFXWIN_H__
	#error include 'stdafx.h' before including this file for PCH
#endif

#include "resource.h"		// main symbols

/////////////////////////////////////////////////////////////////////////////
// COptConfigApp:
// See OptConfig.cpp for the implementation of this class
//

class COptConfigApp : public CWinApp
{
public:
	COptConfigApp();
	bool InitSecurity();

// Overrides
	// ClassWizard generated virtual function overrides
	//{{AFX_VIRTUAL(COptConfigApp)
	public:
	virtual BOOL InitInstance();
	//}}AFX_VIRTUAL

// Implementation

	//{{AFX_MSG(COptConfigApp)
		// NOTE - the ClassWizard will add and remove member functions here.
		//    DO NOT EDIT what you see in these blocks of generated code !
	//}}AFX_MSG
	DECLARE_MESSAGE_MAP()
private:

};


/////////////////////////////////////////////////////////////////////////////

//{{AFX_INSERT_LOCATION}}
// Microsoft Visual C++ will insert additional declarations immediately before the previous line.

#endif // !defined(AFX_OPTCONFIG_H__8D027F9C_2971_4829_8F25_6152070FA069__INCLUDED_)

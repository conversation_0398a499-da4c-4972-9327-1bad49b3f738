#include "stdafx.h"

CString GetRegistryData(const CString &path, const CString &key);
int SetRegistryData(const CString &path, const CString &key, const CString &value, const CString subKey = "");
int GetRegistryData(const CString &path, const CStringArray &keys, CStringArray &values);
int SetRegistryData(const CString &path, const CStringArray &keys, const CStringArray &values);
DWORD GetRemoteRegistryData(const CString &machine, const CString &path, const CString &key, CString &data);
int GetKeyList(const CString &path, CStringArray &keyList);
DWORD GetRemoteKeyList(const CString &machine, const CString &path, CStringArray &keyList);
int SetRemoteRegistryData(const CString &machine, const CString &path, 
						  const CStringArray &keys, const CStringArray &values);
int SetRemoteRegistryData(const CString &machine, const CString &path, const CString &key, const CString &value,
						  const CString &subKey = "");
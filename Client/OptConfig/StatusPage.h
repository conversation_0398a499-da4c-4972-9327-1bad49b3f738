#if !defined(AFX_STATUSPAGE_H__B051BAF4_9740_499E_8DE6_A8F2C7EF085A__INCLUDED_)
#define AFX_STATUSPAGE_H__B051BAF4_9740_499E_8DE6_A8F2C7EF085A__INCLUDED_

#if _MSC_VER > 1000
#pragma once
#endif // _MSC_VER > 1000
// StatusPage.h : header file
//

/////////////////////////////////////////////////////////////////////////////
// CStatusPage dialog

class CStatusPage : public CPropertyPage
{
	DECLARE_DYNCREATE(CStatusPage)

// Construction
public:
	CStatusPage();
	~CStatusPage();

// Dialog Data
	//{{AFX_DATA(CStatusPage)
	enum { IDD = IDD_STATUS };
	CListCtrl	m_ServiceListCtrl;
	//}}AFX_DATA


// Overrides
	// ClassWizard generate virtual function overrides
	//{{AFX_VIRTUAL(CStatusPage)
	public:
	virtual BOOL OnSetActive();
	virtual void OnOK();
	protected:
	virtual void DoDataExchange(CDataExchange* pDX);    // DDX/DDV support
	virtual BOOL OnNotify(WPARAM wParam, LPARAM lParam, LRESULT* pResult);
	//}}AFX_VIRTUAL

// Implementation
protected:
	// Generated message map functions
	//{{AFX_MSG(CStatusPage)
	virtual BOOL OnInitDialog();
	afx_msg void OnStart();
	afx_msg void OnStop();
	afx_msg void OnRefresh();
	afx_msg BOOL OnHelpInfo(HELPINFO* pHelpInfo);
	afx_msg void OnResetOptimize();
	//}}AFX_MSG
	DECLARE_MESSAGE_MAP()

private:
	int StartAService(const CString &service);
	int StopAService(const CString &service, const CString& display);
	//int GetServiceList();
	void DisplayServiceStatus(int idx, DWORD currentStatus);
	void LoadServices();
	CString m_CurrentServer;
	CStringArray m_ServiceList;
};

//{{AFX_INSERT_LOCATION}}
// Microsoft Visual C++ will insert additional declarations immediately before the previous line.

#endif // !defined(AFX_STATUSPAGE_H__B051BAF4_9740_499E_8DE6_A8F2C7EF085A__INCLUDED_)

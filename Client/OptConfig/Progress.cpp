// Progress.cpp : implementation file
//

#include "stdafx.h"
#include "optconfig.h"
#include "Progress.h"

#ifdef _DEBUG
#define new DEBUG_NEW
#undef THIS_FILE
static char THIS_FILE[] = __FILE__;
#endif

/////////////////////////////////////////////////////////////////////////////
// CProgress dialog


CProgress::CProgress(CWnd* pParent /*=NULL*/)
	: CDialog(CProgress::IDD, pParent)
{
	//{{AFX_DATA_INIT(CProgress)
	m_Message = _T("");
	//}}AFX_DATA_INIT
	m_Stopped = FALSE;
	m_ShowProgress = TRUE;
}


void CProgress::DoDataExchange(CDataExchange* pDX)
{
	CDialog::DoDataExchange(pDX);
	//{{AFX_DATA_MAP(CProgress)
	DDX_Control(pDX, IDC_MESSAGE, m_MessageCtrl);
	DDX_Control(pDX, IDC_PROGRESS, m_ProgressCtrl);
	DDX_Text(pDX, IDC_MESSAGE, m_Message);
	//}}AFX_DATA_MAP
}


BEGIN_MESSAGE_MAP(CProgress, CDialog)
	//{{AFX_MSG_MAP(CProgress)
	ON_BN_CLICKED(IDC_STOP, OnStop)
	//}}AFX_MSG_MAP
END_MESSAGE_MAP()

/////////////////////////////////////////////////////////////////////////////
// CProgress message handlers

void CProgress::OnStop() 
{
	m_Stopped = TRUE;	
}

void CProgress::PostNcDestroy() 
{
	delete this;
}

BOOL CProgress::OnInitDialog() 
{
	CDialog::OnInitDialog();
	
	if (! m_ShowProgress) {
		m_ProgressCtrl.ShowWindow(SW_HIDE);
		GetDlgItem(IDC_STOP)->ShowWindow(SW_HIDE);
	}

	this->SetWindowText(m_Title);
	
	return TRUE;  // return TRUE unless you set the focus to a control
	              // EXCEPTION: OCX Property Pages should return FALSE
}

// ClientPage.cpp : implementation file
//

#include "stdafx.h"
#include "OptConfig.h"
#include "ClientPage.h"
#include "RegistryFunctions.h"
#include "Progress.h"
#include "..\..\common\core\ssaencrypt.h"


#include <winnetwk.h>
#include <winsvc.h>
#include <afxsock.h>
#include <afxmt.h>
#include <winbase.h>
#include <winperf.h>


#ifdef _DEBUG
#define new DEBUG_NEW
#undef THIS_FILE
static char THIS_FILE[] = __FILE__;
#endif

extern void ShowLastError(const CString& extra = "");
extern void ShowError(DWORD errorNo);
extern CString GetLocalHostname();
extern DWORD ConnectToServer(const CString &serverName);
extern void ShowScreenHelp(const CString &helpTopic);
extern void ShowFieldHelp(const CString &helpTopic);

BOOL PeekAndPump();
UINT GetDatabaseListThread(LPVOID pParam);

int SocketTalk(CStringArray &sendStringArray, 
			   CStringArray &recvStringArray, 
			   CString className, int operationNum, CString &serverName);
CEvent g_Event;
/////////////////////////////////////////////////////////////////////////////
// CClientPage property page

IMPLEMENT_DYNCREATE(CClientPage, CPropertyPage)

CClientPage::CClientPage() : CPropertyPage(CClientPage::IDD)
{
	//{{AFX_DATA_INIT(CClientPage)
	m_StartEngine = FALSE;
	m_Debug = FALSE;
	m_Password = _T("");
	m_Server = _T("");
	m_ShowSession = FALSE;
	m_UserName = _T("");
	m_AutoCheck = FALSE;
	m_PhysicalMemory = _T("");
	m_VirtualMemory = _T("");
	m_CopyBuffer = _T("");
	m_SaveAuto = _T("");
	//}}AFX_DATA_INIT

}

CClientPage::~CClientPage()
{
}

void CClientPage::DoDataExchange(CDataExchange* pDX)
{
	CPropertyPage::DoDataExchange(pDX);
	//{{AFX_DATA_MAP(CClientPage)
	DDX_Control(pDX, IDC_VERSION_LIST, m_VersionListCtrl);
	DDX_Check(pDX, IDC_DEBUG, m_Debug);
	DDX_Text(pDX, IDC_PASSWORD, m_Password);
	//DDX_Text(pDX, IDC_SERVER, m_Server);
	//DDX_Check(pDX, IDC_SHOW_SESSION, m_ShowSession);
	DDX_Text(pDX, IDC_USERNAME, m_UserName);
	DDX_Check(pDX, IDC_AUTO_CHECK, m_AutoCheck);
	DDX_Text(pDX, IDC_PHYSICAL, m_PhysicalMemory);
	DDX_Text(pDX, IDC_VIRTUAL, m_VirtualMemory);
	DDX_Text(pDX, IDC_COPY_BUFFER, m_CopyBuffer);
	//}}AFX_DATA_MAP
}


BEGIN_MESSAGE_MAP(CClientPage, CPropertyPage)
	//{{AFX_MSG_MAP(CClientPage)
	// ON_BN_CLICKED(IDC_TEST_SERVER, OnTestServer) Lester
	//ON_EN_CHANGE(IDC_SERVER, OnChangeServer)
	ON_BN_CLICKED(IDC_AUTO_CHECK, OnAutoLoginChange)
	ON_WM_HELPINFO()
	ON_EN_CHANGE(IDC_COPY_BUFFER, OnChangeCopyBuffer)
	ON_BN_CLICKED(IDC_DEBUG, OnChange)
	ON_EN_CHANGE(IDC_PASSWORD, OnChange)
	//ON_BN_CLICKED(IDC_SHOW_SESSION, OnChange)
	ON_EN_CHANGE(IDC_USERNAME, OnChange)
	ON_BN_CLICKED(IDC_LOG_NOOVERWRITE, OnChange)
	ON_BN_CLICKED(IDC_LOG_OVERWRITE, OnChange)
	//ON_BN_CLICKED(IDC_LOG_SCREEN, OnChange) Lester
	//}}AFX_MSG_MAP
END_MESSAGE_MAP()

/////////////////////////////////////////////////////////////////////////////
// CClientPage message handlers

BOOL CClientPage::OnInitDialog() 
{
	CPropertyPage::OnInitDialog();
	CRect r;


	for (int i=0; i < m_VersionListCtrl.GetHeaderCtrl()->GetItemCount(); ++i)
		m_VersionListCtrl.GetHeaderCtrl()->DeleteItem(0);

	m_VersionListCtrl.GetClientRect(&r);
	m_VersionListCtrl.InsertColumn(0, "Program", LVCFMT_LEFT, r.Width()/4, 0);
	m_VersionListCtrl.InsertColumn(1, "Version", LVCFMT_LEFT, r.Width()/5, 0);
	m_VersionListCtrl.InsertColumn(2, "Path", LVCFMT_LEFT, r.Width()*11/20, 0);
	
	LoadProgramList();
	
	LoadRegistryValues();

	CString temp;
	CPropertySheet *pParent = (CPropertySheet *)GetParent();

	temp.Format("Optimize Configuration - %s", m_Server);
	pParent->SetTitle(temp);

	return FALSE;  // return TRUE unless you set the focus to a control
	              // EXCEPTION: OCX Property Pages should return FALSE
}

BOOL CClientPage::OnSetActive() 
{
	MEMORYSTATUS memStats;

	memStats.dwLength = sizeof(MEMORYSTATUS);

	GlobalMemoryStatus(&memStats);

	m_PhysicalMemory.Format("%d / %d", memStats.dwAvailPhys/1024, 
		memStats.dwTotalPhys/1024);
	m_VirtualMemory.Format("%d / %d", memStats.dwAvailPageFile/1024,
		memStats.dwTotalPageFile/1024);

	UpdateData(FALSE);



	return CPropertyPage::OnSetActive();
}

void CClientPage::LoadProgramList()
{
	CRect r;
	CString temp, temp2, winVer;

	m_VersionListCtrl.InsertItem(0, "Optimize"); 

	temp = GetRegistryData("Software\\SSA Global\\Optimize", "MajorVersion");
	if (temp == "")
		m_VersionListCtrl.SetItemText(0, 1, "Not Installed");
	else {
		temp2 = GetRegistryData("Software\\SSA Global\\Optimize", "MinorVersion");
		if (temp2 != "" && temp2 != "0.0") {
			temp += " - Patch: ";
			temp += temp2;
		}
		m_VersionListCtrl.SetItemText(0, 1, temp);
		
		temp = GetRegistryData("Software\\SSA Global\\Optimize", "Home");
		
		m_VersionListCtrl.SetItemText(0, 2, temp);
	}

	// Oracle
	m_VersionListCtrl.InsertItem(1, "Oracle");

	CString serverResource;
	serverResource = "";

	CStringArray tmpArray;
	GetRemoteKeyList(serverResource, "Software\\Oracle", tmpArray);
	if (tmpArray.GetSize() == 0) {
		m_VersionListCtrl.SetItemText(1, 1, "Not Installed");
		m_VersionListCtrl.SetItemText(1, 2, "");
	}

	temp = "";
	for (int i=0; i<tmpArray.GetSize(); i++) {
		if (tmpArray.ElementAt(i).Left(strlen("KEY_OraDb10g_home")) == "KEY_OraDb10g_home") {
			//Found the Oracle 10g key
			
			temp = GetRegistryData("Software\\Oracle\\" + tmpArray.ElementAt(i), "ORACLE_BASE"); //Gets e.g. C:\oracle\product\10.2.0
			temp = temp.Right(temp.GetLength()-temp.ReverseFind('\\')-1); // Sets temp to e.g. 10.2.0
			m_VersionListCtrl.SetItemText(1, 1, temp);

			temp = GetRegistryData("Software\\Oracle\\" + tmpArray.ElementAt(i), "ORACLE_HOME");
			m_VersionListCtrl.SetItemText(1, 2, temp);
			break;
		}
	}

	if (temp == "") { //Oracle 10g key not found. Do the pre-10g thing.
		int i;

		GetRemoteRegistryData(serverResource, "Software\\Oracle", "SVRMGR", temp);
		if (temp != "") {
			int n;
			i = 0;
			n = temp.Find("\\");
			while (n >= 0) {
				i = n + 1;
				n = temp.Find("\\", i);
			}
			temp = temp.Mid(i);
			temp.MakeLower();
		}

		if (temp == "rdbms80")
			temp = "8.0";
		else if (temp == "rdbms73")
			temp = "7.3";
		else if (temp == "rdbms81") 
			temp = "8.1";
		else
			temp = "unknown";


		m_VersionListCtrl.SetItemText(1, 1, temp);
		if (temp != "rdbms80") {
			GetRemoteRegistryData(serverResource, "Software\\Oracle", "ORACLE_HOME", temp);
			m_VersionListCtrl.SetItemText(1, 2, temp);
		}
		else {
			GetRemoteRegistryData(serverResource, "Software\\Oracle\\ALL_HOMES", "HOME_COUNTER", temp);
			if (temp == "")
				temp2 = "Unknown";
			else {
				for (int i=0; i < atoi(temp); ++i) {
					temp2.Format("Software\\Oracle\\HOME%d", i);
					GetRemoteRegistryData(serverResource, temp2, "RDBMS_CONTROL", temp);
					if (temp == "") {
						temp2 = "Unknown";
						continue;
					}
					else {
						GetRemoteRegistryData(serverResource, temp2, "ORACLE_HOME", temp);
						if (temp != "") {
							temp2 = temp;
							break;
						}
						else
							temp2 = "Unknown";
					}
				}
				m_VersionListCtrl.SetItemText(1, 2, temp2);
			}
		}
	}


	m_VersionListCtrl.InsertItem(2, "Windows");
	
	// Windows
	winVer = GetRegistryData("Software\\Microsoft\\Windows NT\\CurrentVersion", "CurrentVersion");
	if (winVer == "4.0") {
		m_VersionListCtrl.SetItemText(2, 0, "Windows NT");
		m_VersionListCtrl.SetItemText(2, 1, winVer);
	}
	else if (winVer == "5.0")
		m_VersionListCtrl.SetItemText(2, 1, "2000");
	else if (winVer == "5.1")
		m_VersionListCtrl.SetItemText(2, 1, "XP");
	else if (winVer == "5.2")
		m_VersionListCtrl.SetItemText(2, 1, "2003");
	else
		m_VersionListCtrl.SetItemText(2, 1, winVer);

	m_VersionListCtrl.SetItemText(2, 2, 
		GetRegistryData("Software\\Microsoft\\Windows NT\\CurrentVersion", "PathName"));



	// Autocad
	m_VersionListCtrl.InsertItem(3, "Autocad");
	CStringArray acadArray;
	CString acadKey = "SOFTWARE\\Autodesk\\AutoCAD";
	GetRemoteKeyList(serverResource, acadKey, acadArray);
	if (acadArray.GetSize() == 0)
		m_VersionListCtrl.SetItemText(3, 1, "Not Installed");
	else {
		CStringArray acadSubArray;
		CString acadSubKey = acadKey + "\\" + acadArray.ElementAt(0);
		GetRemoteKeyList(serverResource, acadSubKey, acadSubArray);
		if (acadSubArray.GetSize() == 0)
			m_VersionListCtrl.SetItemText(3, 1, "Not Installed");
		else {
			CString acadSubSubKey = acadSubKey + "\\" + acadSubArray.ElementAt(0);
			temp = GetRegistryData(acadSubSubKey, "ProductName");
			m_VersionListCtrl.SetItemText(3, 1, temp);
			temp = GetRegistryData(acadSubSubKey, "AcadLocation");
			m_VersionListCtrl.SetItemText(3, 2, temp);
		}
	}
}

void CClientPage::LoadRegistryValues()
{
	CString temp ="";
	CButton *pButton;
	SSAEncrypt ss;	
	
	temp = GetRegistryData( "SOFTWARE\\SSA Global\\Optimize", "StartEngineAtBoot");
	if (temp == "")
		temp = "1";
	m_StartEngine = (atoi(temp) == 1);

	char sComputerName[64];
	DWORD dwSize = sizeof(sComputerName);
    GetComputerName(sComputerName, &dwSize);
	m_Server = sComputerName;

	temp = GetRegistryData("Software\\SSA Global\\Optimize", "UserID");
	if (temp != "") {
		m_UserName = temp;
	}
	
	//LPSTR ret2 = ss.getSessionDeSO(ss.getGetRegi("Software\\SSA Global\\Optimize", "ssaprofile"));
	//char xxcde[100];
	//strcpy(xxcde, ret2);
	//MessageBox(xxcde);

	

	temp = GetRegistryData("Software\\SSA Global\\Optimize", "ssaprofile");
	if (temp != "") {
		//m_Password = temp;
		LPSTR ret = ss.getSessionDeSO(temp.GetBuffer( ));
		char xxabc[100];
		strcpy(xxabc, ret);
		m_Password = xxabc;
		//MessageBox(m_Password);
		//m_Password.SetString(xx, (int)string(xx));
		//m_Password = ss.rets;
		//MessageBox(m_Password);
	}

	temp = GetRegistryData("Software\\SSA Global\\Optimize", "WindowMode");
	if (temp != "")
		m_ShowSession = TRUE;
	else
		m_ShowSession = FALSE;

	temp = GetRegistryData("Software\\SSA Global\\Optimize", "Debug");
	if (temp != "" && atoi(temp) != 0)
		m_Debug = TRUE;
	else
		m_Debug = FALSE;

	temp = GetRegistryData("Software\\SSA Global\\Optimize", "AutoLoginParameters");
	m_SaveAuto = temp;
	if (temp == "")
		m_AutoCheck = FALSE;
	else
		m_AutoCheck = TRUE;

	m_CopyBuffer = GetRegistryData("Software\\SSA Global\\Optimize", "CopyBufferSize");
	if (m_CopyBuffer == "")
		m_CopyBuffer = "50000000";
	
	temp = GetRegistryData("Software\\SSA Global\\Optimize", "SessionLog");
	if (temp == "1")
		pButton = (CButton *)GetDlgItem(IDC_LOG_OVERWRITE);
	else //if (temp == "2") Lester
		pButton = (CButton *)GetDlgItem(IDC_LOG_NOOVERWRITE);
	
	//else Lester
		//pButton = (CButton *)GetDlgItem(IDC_LOG_SCREEN); Lester
	pButton->SetCheck(TRUE);


	UpdateData(FALSE);
}

BOOL CClientPage::OnApply() 
{
	SaveValues();
	
	this->SetModified(FALSE);

	return CPropertyPage::OnApply();
}

void CClientPage::OnOK() 
{	
	CPropertyPage::OnOK();
}

BOOL CClientPage::SaveValues()
{
	CString temp;
	CStringArray keys, values;
	CButton *pButton;
	
	SSAEncrypt ss;	

	UpdateData(TRUE);

	keys.Add("UserID");
	values.Add(m_UserName);
	
	keys.Add("ssaprofile");
	LPSTR ret = ss.getSessionEnSO(m_Password.GetBuffer());
	//values.Add(ss.rets);
	//172172173173174174175175
	//values.Add(ret);
	char xxabc[100];
	strcpy(xxabc, ret);
	//MessageBox(xxabc);
	values.Add(xxabc);

	keys.Add("Debug");
	temp.Format("%d", m_Debug);
	values.Add(temp);

	keys.Add("AutoLoginParameters");
	if (m_AutoCheck)
		values.Add(m_SaveAuto);
	else
		values.Add("");

	keys.Add("SessionLog");
	temp = "";
	pButton = (CButton *)GetDlgItem(IDC_LOG_OVERWRITE);
	if (pButton->GetCheck())
		temp = "1";
	else {
		pButton = (CButton *)GetDlgItem(IDC_LOG_NOOVERWRITE);
		if (pButton->GetCheck())
			temp = "2";
		else
			temp = "0";
	}
	values.Add(temp);
	

	keys.Add("CopyBufferSize");
	values.Add(m_CopyBuffer);

	//Lester add:
	keys.Add("StartEngineAtBoot");
	temp.Format("%d", m_StartEngine);
	values.Add(temp);
	//Lester add till here

	SetRegistryData("Software\\SSA Global\\Optimize", keys, values);
	

	return FALSE;

}

void CClientPage::OnTestServer() 
{
	/*lester
	STARTUPINFO si;
	PROCESS_INFORMATION pi;
	HANDLE sessionHandle;
	CWnd *pSessionHandle;
	CString cmd, m_BinDir;

	UpdateData(TRUE);

	m_BinDir = m_ClientHome + "\\bin";

	si.cb = sizeof(si);
	si.lpTitle = "Optimize Session Manager";
	si.wShowWindow = SW_MINIMIZE;

	si.dwX =  si.dwY = STARTF_USEPOSITION;
	si.dwXSize = si.dwYSize = STARTF_USESIZE;
    si.dwXCountChars = si.dwYCountChars = STARTF_USECOUNTCHARS;
    si.dwFillAttribute = STARTF_USEFILLATTRIBUTE;
    si.dwFlags =  STARTF_USESHOWWINDOW;
    si.cbReserved2 = 0;
    si.lpReserved2 = NULL;
	si.lpReserved = NULL;
	si.lpDesktop = NULL;
	
	pSessionHandle = FindWindow(NULL,"Optimize Session Manager");
	if (pSessionHandle != NULL) {
		AfxMessageBox("Optimize is already running.\n"
			"Please end the current Optimize session\n"
			"before attempting to test the server.");
		return;
	}

	CProgress *pProgress = new CProgress();
	pProgress->m_Title = "Testing server ";
	pProgress->m_Title += m_Server;
	CStringArray sendArray, recvArray;
	CString temp;

	pProgress->Create(IDD_PROGRESS, this);
	
	pProgress->m_Message.Format("Attempting to start the client process...");
	pProgress->m_ProgressCtrl.SetRange32(0, 5);
	pProgress->m_ProgressCtrl.SetStep(1);
	pProgress->UpdateData(FALSE);
	pProgress->CenterWindow();
	pProgress->ShowWindow(SW_SHOW);
	pProgress->UpdateWindow();

	cmd.Format("%s\\slotse0.exe -fnd SlotClientNT -fns %s:6060", m_BinDir, m_Server);
	
	BOOL bCreated = CreateProcess(NULL, cmd.GetBuffer(0), NULL, NULL, FALSE,
		NORMAL_PRIORITY_CLASS, NULL, m_BinDir, &si, &pi);
	cmd.ReleaseBuffer();
	if (!bCreated) {
		ShowLastError();
		AfxMessageBox("Could not start the Optimize Session Manager.\n"
			"This indicates a problem on the client, not the server.");
		return;
	}
	
	sessionHandle = pi.hProcess;


	// Ack
	int maxTries = 10, rc;
	sendArray.Add("<SIO>SLOTSocketString\n");
	sendArray.Add("<SAI>ACK\n");
	sendArray.Add("<EIO>SLOTSocketString\n");

	while (maxTries > 0) {
		rc = SocketTalk(sendArray, recvArray, "SLOTSocketString", 6666, GetLocalHostname());
		if (rc == 0)
			break;
		maxTries--;
		PeekAndPump();
		if (pProgress->m_Stopped) {
			pProgress->DestroyWindow();
			TerminateProcess(sessionHandle, 0);
			return;
		}

		pProgress->m_ProgressCtrl.StepIt();

	}
	
	if (rc != 0) {
		ShowError(rc);
		pProgress->DestroyWindow();
		TerminateProcess(sessionHandle, 0);
		return;
	}


	BOOL connected = FALSE;

	for (int i=0; i < recvArray.GetSize(); ++i) {
		temp = recvArray[i];
		if (temp.Find("NAK", 0))
			connected = TRUE;
	}

	if (! connected) {
		AfxMessageBox("Unable to start the client process.");
		pProgress->DestroyWindow();
		TerminateProcess(sessionHandle, 0);
		return;
	}
	else {
		PeekAndPump();
		if (pProgress->m_Stopped) {
			pProgress->DestroyWindow();
			TerminateProcess(sessionHandle, 0);
			return;
		}
		pProgress->m_Message.Format("Attempting to communicate with %s...", m_Server);
		pProgress->m_ProgressCtrl.SetPos(0);
		pProgress->m_ProgressCtrl.SetRange32(0, 20);
		pProgress->UpdateData(FALSE);
	}


	try {
		CWaitCursor cwc;
		rc = 0;
		CWinThread *pThread = AfxBeginThread(GetDatabaseListThread, this);
		
		BOOL bThreadDone = false;
		
		g_Event.ResetEvent();

		for (int i=0; i < 20; ++i) {
			if (pProgress->m_Stopped) {
				pProgress->DestroyWindow();
				TerminateProcess(sessionHandle, 0);
				return;
			}
			pProgress->m_ProgressCtrl.StepIt();
			if ( !PeekAndPump() )
				break;
			
			bThreadDone = g_Event.Lock(0);
			if (bThreadDone)
				break;
		}
		
		if (! bThreadDone) {
			temp.Format("Error communicating with %s.\nVerify that the services are running.", m_Server);
			AfxMessageBox(temp);
		}
		else {
			if (m_Status == 100) {
				temp.Format("Error communicating with %s.\n"
					"Restart the services and try the test again.\n"
					"If the test continues to fail, it is possible\n"
					"that the client and server are using incompatible versions.", m_Server);
				AfxMessageBox(temp);
			}
			else if (m_Status != 0) {
				ShowError(m_Status);
			}
			else {
				temp.Format("Server test completed successfully.");
				AfxMessageBox(temp);
			}
		}
		
	}
	catch(...) 
	{
		temp.Format("Error communicating with %s.", m_Server);
		AfxMessageBox(temp);
		pProgress->DestroyWindow();
		TerminateProcess(sessionHandle, 0);
		return;
	}


	TerminateProcess(sessionHandle, 0);

	pProgress->DestroyWindow();
	
	return;
 Lester */
}

UINT GetDatabaseListThread(LPVOID pParam)
{
	CStringArray sendArray, recvArray;
	CClientPage *pClientPage = (CClientPage *)pParam;
	DWORD rc;
	CString temp;

	sendArray.Add("<SIO>SLOTSocketString\n");
	sendArray.Add("<SAI>GetDatabaseList\n");
	sendArray.Add("<EIO>SLOTSocketString\n");

	rc = SocketTalk(sendArray, recvArray, "SLOTSocketString", 8888, GetLocalHostname());
	if (rc == SOCKET_ERROR)
		pClientPage->m_Status = GetLastError();
	else {
		pClientPage->m_Status = 0;
		for (int i=0; i < recvArray.GetSize(); ++i) {
			temp = recvArray[i];
			if (temp.Find("EXCEPTION", 0) >= 0) {
				pClientPage->m_Status = 100;
				break;
			}
		}
	}

	g_Event.SetEvent();


	return rc;
}


int SocketTalk(CStringArray &sendStringArray, 
			   CStringArray &recvStringArray, 
			   CString className, int operationNum, CString &serverName)
{

	CSocket sock;	
	CString sendBuffer;
	CString receive;
	char receiveBuffer[1025];
	BOOL done;

	if (! sock.Create()) {
		return sock.GetLastError();
	}

	if (sock.Connect(serverName, 6010) == 0) {
		return sock.GetLastError();
	}


	// Send request type
	sendBuffer.Format("<SSO>\n<SON>%s:%d\n<EOS>\n", className, operationNum);
	int cnt = 0;
	do {
		cnt = sock.Send(sendBuffer.GetBuffer(0), sendBuffer.GetLength());
		if (cnt < sendBuffer.GetLength())
			sendBuffer = sendBuffer.Left(sendBuffer.GetLength()-cnt);
	}
	while (cnt < sendBuffer.GetLength());
	sendBuffer.ReleaseBuffer();


	// Receive handshaking
	done = FALSE;
	receive = "";
	while (! done) {
		sock.Receive(receiveBuffer, 1024);
		receive += receiveBuffer;
		if (receive.Find("<EOS>"))
			done = TRUE;
	}


	// Send data
	sendBuffer = "<SSO>\n<SLO>\n";
	for (int i = 0; i < sendStringArray.GetSize(); ++i)
		sendBuffer += sendStringArray[i];
	sendBuffer += "<ELO>\n<EOS>\n";
	do {
		cnt = sock.Send(sendBuffer.GetBuffer(0), sendBuffer.GetLength());
		if (cnt < sendBuffer.GetLength())
			sendBuffer = sendBuffer.Left(sendBuffer.GetLength()-cnt);
	}
	while (cnt < sendBuffer.GetLength());
	sendBuffer.ReleaseBuffer();

	
	// Receive handshaking
	done = FALSE;
	receive = "";
	while (! done) {
		sock.Receive(receiveBuffer, 1024);
		receive += receiveBuffer;
		if (receive.Find("<EOS>"))
			done = TRUE;
	}


	// Receive data
	done = FALSE;
	receive = "";
	while (! done) {
		sock.Receive(receiveBuffer, 1024);
		receive += receiveBuffer;
		if (receive.Find("<EOS>"))
			done = TRUE;
	}

	
	// Parse received data
	int n = 0;
	CString t;
	done = FALSE;
	while (! done) {
		n = receive.Find("\n");
		if ( n < 0 ) {
			if (receive.GetLength() > 0)
				recvStringArray.Add(receive);
			done = TRUE;
			continue;
		}
		else {
			t = receive.Left(n);
			recvStringArray.Add(t);
		}

		++n;
		if (n == receive.GetLength())
			done = TRUE;
		else
			receive = receive.Right(receive.GetLength()-n);
	}

	// Send handshaking
	sendBuffer.Format("<SSO>\n<EOS>\n", className, operationNum);
	do {
		cnt = sock.Send(sendBuffer.GetBuffer(0), sendBuffer.GetLength());
		if (cnt == SOCKET_ERROR)
			break;
		if (cnt < sendBuffer.GetLength())
			sendBuffer = sendBuffer.Left(sendBuffer.GetLength()-cnt);
	}
	while (cnt < sendBuffer.GetLength());
	sendBuffer.ReleaseBuffer();

	return 0;
}

BOOL PeekAndPump()
{
	MSG msg;
	while (::PeekMessage(&msg, NULL, 0, 0, PM_NOREMOVE)) {
		//fprintf(f, "In PeekAndPump loop\n");
		//fflush(f);
		if (!AfxGetApp()->PumpMessage()) {
			//fprintf(f, "Posting quit message\n");
			//fflush(f);
			::PostQuitMessage(0);
			return FALSE;
		}
	}
	
	LONG lIdle = 0;
	while (AfxGetApp()->OnIdle(lIdle++)) {
		//fprintf(f, "In OnIdle loop\n");
		//fflush(f);
	}

	//fprintf(f, "After OnIdle Loop\n");
	//fflush(f);

	Sleep(500);

	return TRUE;

}

void CClientPage::OnChangeServer() 
{
	/*Lester
	CString temp;
	CPropertySheet *pParent = (CPropertySheet *)GetParent();

	UpdateData(TRUE);

	temp.Format("Optimize Configuration - %s", m_Server);
	pParent->SetTitle(temp);
	this->SetModified(TRUE);
	Lester */
	
}


void CClientPage::OnChange() 
{
	this->SetModified(TRUE);
}

void CClientPage::OnAutoLoginChange() 
{
	this->SetModified(TRUE);
	UpdateData();
	if (m_AutoCheck) {
		GetRemoteRegistryData("", "SOFTWARE\\SSA Global\\Optimize", "MRUDB", m_SaveAuto);
		m_SaveAuto += '|';
	}
	else
		m_SaveAuto = "";
}

BOOL CClientPage::OnHelpInfo(HELPINFO* pHelpInfo) 
{
	CString topic;

	switch (pHelpInfo->iCtrlId) {
	case IDC_VERSION_LIST:
		topic = "Client_ProgramInformation";
		break;
	case IDC_PHYSICAL:
	case IDC_VIRTUAL:
		topic = "Client_Memory";
		break;
	//case IDC_SERVER:
		//topic = "Client_Server";
		//break;
	//case IDC_SHOW_SESSION:
		//topic = "Client_ShowSessionWindow";
		//break;
	case IDC_DEBUG:
		topic = "Client_Debug";
		break;
	case IDC_USERNAME:
		topic = "Client_UserID";
		break;
	case IDC_PASSWORD:
		topic = "Client_Password";
		break;
	/*case IDC_TEST_SERVER:
		topic = "Client_TestServer";
		break; Lester*/
	case IDC_AUTO_CHECK:
		topic = "Client_AutomaticLogin";
		break;
	/*case IDC_LOG_SCREEN:
		topic = "Client_SessionLog";
		break;Lester*/
	case IDC_LOG_OVERWRITE:
		topic = "Client_SessionLog";
		break;
	case IDC_LOG_NOOVERWRITE:
		topic = "Client_SessionLog";
		break;
	case IDC_COPY_BUFFER:
		topic = "Client_CopyBuffer";
		break;
	default:
		topic = "Client_Overview";
		ShowScreenHelp(topic);
		return FALSE;
	}


	ShowFieldHelp(topic);

	return FALSE;
}


BOOL CClientPage::OnNotify(WPARAM wParam, LPARAM lParam, LRESULT* pResult) 
{

	NMHDR* pNMHDR = (NMHDR*)lParam;
	if (pNMHDR->code == PSN_HELP) {
		ShowScreenHelp("Client_Overview");
		return TRUE;
	}
	
	return CPropertyPage::OnNotify(wParam, lParam, pResult);
}



void CClientPage::OnChangeCopyBuffer() 
{
	UpdateData(TRUE);
	
	this->SetModified(TRUE);

	for (int i=0; i < m_CopyBuffer.GetLength(); ++i) {
		if (! isdigit(m_CopyBuffer.GetAt(i))) {
			AfxMessageBox("Copy Buffer must be a number.");
			CEdit *pEdit = (CEdit *)GetDlgItem(IDC_COPY_BUFFER);
			pEdit->SetSel(0, -1);
			pEdit->SetFocus();
			return;
		}
	}

}

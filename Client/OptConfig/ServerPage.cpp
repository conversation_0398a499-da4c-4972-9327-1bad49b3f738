// ServerPage.cpp : implementation file
//

#include "stdafx.h"
#include "OptConfig.h"
#include "ServerPage.h"
#include "ClientPage.h"
#include "RegistryFunctions.h"
#include "Password.h"
#include "Progress.h"
#include <afxtempl.h>
#include <winbase.h>
#include <afxmt.h>
#include ".\serverpage.h"

#include "StatusPage.h"
#include "winsvc.h"
#include "XBrowseForFolder.h"

#ifdef _DEBUG
#define new DEBUG_NEW
#undef THIS_FILE
static char THIS_FILE[] = __FILE__;
#endif

/////////////////////////////////////////////////////////////////////////////
// CServerPage property page

IMPLEMENT_DYNCREATE(CServerPage, CPropertyPage)
extern void ShowLastError(const CString& extra = "");
void ParseString(const CString &string, const CString &delimiter, CStringArray &strings);
extern DWORD ConnectToServer(const CString &serverName);
extern CString GetLocalHostname();
extern BOOL PeekAndPump();
extern CString GetErrorText(DWORD error);
extern void ShowScreenHelp(const CString &helpTopic);
extern void ShowFieldHelp(const CString &helpTopic);
extern int GetWMIProperty(IWbemLocator *pIWbemLocator, IWbemServices *m_pIWbemServices,
								CString &server, CStringArray &propertyList);

UINT GetMemoryThread(LPVOID pParam);

CEvent g_ThreadDone;

CServerPage::CServerPage() : CPropertyPage(CServerPage::IDD)

{
	//{{AFX_DATA_INIT(CServerPage)
	
	m_AnalysisMemory = _T("");
	m_BusinessMemory = _T("");
	m_DatabaseBuffer = _T("");
	m_DatabaseLogging = FALSE;
	m_DatabaseMemory = _T("");
	m_StartEngine = FALSE;
	m_PhysicalMemory = _T("");
	m_VirtualMemory = _T("");
	m_str = _T("");
	//}}AFX_DATA_INIT
	m_pIWbemServices = NULL;
}

CServerPage::~CServerPage()
{
}

void CServerPage::DoDataExchange(CDataExchange* pDX)
{
	CPropertyPage::DoDataExchange(pDX);
	//{{AFX_DATA_MAP(CServerPage)
	DDX_Control(pDX, IDC_GROUP, m_GroupCtrl);
	DDX_Control(pDX, IDC_MRUDB_LIST, m_MRUDBListCtrl);
	DDX_Control(pDX, IDC_ENGINE_LOG_LIST, m_EngineLogListCtrl);
	DDX_Control(pDX, IDC_DATABASE_LIST, m_DatabaseListCtrl);
	//DDX_Control(pDX, IDC_VERSION_LIST, m_VersionListCtrl);
	//DDX_Text(pDX, IDC_ANALYSIS_MEMORY, m_AnalysisMemory);
	//DDX_Text(pDX, IDC_BUSINESS_MEMORY, m_BusinessMemory);
	//DDX_Text(pDX, IDC_DATABASE_BUFFER, m_DatabaseBuffer);
	DDX_Check(pDX, IDC_DATABASE_LOGGING, m_DatabaseLogging);
	//DDX_Text(pDX, IDC_DATABASE_MEMORY, m_DatabaseMemory);
	//DDX_Check(pDX, IDC_START_ENGINE, m_StartEngine);
	//DDX_Text(pDX, IDC_PHYSICAL, m_PhysicalMemory);
	//DDX_Text(pDX, IDC_VIRTUAL, m_VirtualMemory);
	//}}AFX_DATA_MAP
	DDX_Control(pDX, IDC_SERVICE_LIST, m_ServiceListCtrl); //Lester
	DDX_Text(pDX, IDC_DATABASE_LOGFILE, m_str);
}


BEGIN_MESSAGE_MAP(CServerPage, CPropertyPage)
	//{{AFX_MSG_MAP(CServerPage)
	//ON_EN_CHANGE(IDC_ANALYSIS_MEMORY, OnChange)
	/*ON_MESSAGE(WM_USER, OnGetMemory) Lester*/
	//ON_EN_CHANGE(IDC_BUSINESS_MEMORY, OnChange)
	//ON_EN_CHANGE(IDC_DATABASE_BUFFER, OnChange)
	ON_BN_CLICKED(IDC_DATABASE_LOGGING, OnChange)
	//ON_EN_CHANGE(IDC_DATABASE_MEMORY, OnChange)
	ON_CBN_SELCHANGE(IDC_ENGINE_LOG_LIST, OnChange)
	ON_CBN_SELCHANGE(IDC_MRUDB_LIST, OnChange)
	//ON_BN_CLICKED(IDC_START_ENGINE, OnChange)
	ON_WM_HELPINFO()
	//}}AFX_MSG_MAP
	ON_BN_CLICKED(IDC_RESET, OnBnClickedReset)
	ON_BN_CLICKED(IDC_START, OnBnClickedStart)
	ON_BN_CLICKED(IDC_REFRESH, OnBnClickedRefresh)
	ON_BN_CLICKED(IDC_STOP, OnBnClickedStop)
	ON_BN_CLICKED(IDC_BROWSE, OnBnClickedBrowse)
	ON_EN_CHANGE(IDC_DATABASE_LOGFILE, OnEnChangeDatabaseLogfile)
END_MESSAGE_MAP()

/////////////////////////////////////////////////////////////////////////////
// CServerPage message handlers

BOOL CServerPage::OnInitDialog() 
{
	CRect r;
	//int i;

	CPropertyPage::OnInitDialog();
	m_CurrentServer = "";
	
	//Lester start
	m_CurrentServer2 = "";
	m_str = "";

	for (int i=0; i < m_ServiceListCtrl.GetHeaderCtrl()->GetItemCount(); ++i)
		m_ServiceListCtrl.GetHeaderCtrl()->DeleteItem(0);

	m_ServiceListCtrl.GetClientRect(&r);
	m_ServiceListCtrl.InsertColumn(0, "Service", LVCFMT_LEFT, r.Width()/2, 0);
	m_ServiceListCtrl.InsertColumn(1, "Status", LVCFMT_LEFT, r.Width()/2, 0);
	m_ServiceListCtrl.InsertColumn(2, "InternalName", LVCFMT_LEFT, 0, 0);
	//Lester till here

	
	m_EngineLogListCtrl.AddString("None");
	m_EngineLogListCtrl.AddString("User");
	m_EngineLogListCtrl.AddString("Debug");

	m_EngineLogListCtrl.GetWindowRect(&r);
	m_EngineLogListCtrl.SetWindowPos(NULL, 0, 0, r.Width(), r.Height()*4, SWP_NOMOVE|SWP_NOZORDER);

	for (i=0; i < m_DatabaseListCtrl.GetHeaderCtrl()->GetItemCount(); ++i)
		m_DatabaseListCtrl.GetHeaderCtrl()->DeleteItem(0);

	m_DatabaseListCtrl.GetClientRect(&r);
	m_DatabaseListCtrl.InsertColumn(0, "Name", LVCFMT_LEFT, r.Width()/3, 0);
	m_DatabaseListCtrl.InsertColumn(1, "Connect String", LVCFMT_LEFT, r.Width()/3, 0);
	m_DatabaseListCtrl.InsertColumn(2, "Type", LVCFMT_LEFT, r.Width()/3, 0);
	

	return TRUE;  // return TRUE unless you set the focus to a control
	              // EXCEPTION: OCX Property Pages should return FALSE
}

BOOL CServerPage::OnSetActive() 
{
	CPropertyPage::OnSetActive();

	CPropertySheet *pParent = (CPropertySheet *)GetParent();
	CClientPage *pClientPage = (CClientPage *)pParent->GetPage(0);
	
	CWaitCursor cwc;

	//if (pClientPage->m_Server != m_CurrentServer) {
		//Clear();
		//m_CurrentServer = pClientPage->m_Server;
		LoadRegistryValues(); //Lester bug fix add this
		SendMessage(WM_USER, 0, 0); //Lester bug fix add this
	//}
	//else {	
		//if (m_PhysicalMemory.Find("Unavailable") != 0)
			//SendMessage(WM_USER, 0, 0);
		
	//}

	UpdateData(FALSE);

	//Lester add start, copy from status page
    
	//CPropertySheet *pParent = (CPropertySheet *)GetParent();
	//CClientPage *pClientPage = (CClientPage *)pParent->GetPage(0);
	CStringArray serviceList;

	//CWaitCursor cwc;

	if (pClientPage->m_Server != m_CurrentServer2) {
		m_CurrentServer2 = pClientPage->m_Server;
		m_ServiceList.RemoveAll();
		GetServiceList();
	}	
	
	LoadServices();
	return CPropertyPage::OnSetActive();

	//Lester add till here

	//return TRUE; Lester comment it out
}

DWORD CServerPage::LoadProgramList()
{
	return 1;
}

void CServerPage::LoadRegistryValues()
{
	CString temp, temp2, serverResource;
	temp = GetLocalHostname();
	temp.MakeUpper();
	temp2 = m_CurrentServer;
	temp2.MakeUpper();

	CWaitCursor cwc;

	//if (temp == temp2) 
		serverResource = "";
	//else
		//serverResource = "\\\\" + m_CurrentServer;

	m_str = "abce"; 

	//Lester add:
	GetRemoteRegistryData(serverResource, 
		"SOFTWARE\\SSA Global\\Optimize", "LogFilePath", m_str);
	//Lester add till here

	GetRemoteRegistryData(serverResource, 
		"System\\CurrentControlSet\\Services\\Optimize Analysis Engine Server\\Parameters", "CommandLine", temp);

	
	m_AnalysisMemory = ExtractMemoryFromService(temp);

	GetRemoteRegistryData(serverResource, 
		"System\\CurrentControlSet\\Services\\Optimize Business Logic Server\\Parameters", "CommandLine", temp);
	m_BusinessMemory = ExtractMemoryFromService(temp);

	GetRemoteRegistryData(serverResource, 
		"System\\CurrentControlSet\\Services\\Optimize Database Server\\Parameters", "CommandLine", temp);
	m_DatabaseMemory = ExtractMemoryFromService(temp);

	GetRemoteRegistryData(serverResource, 
		"SOFTWARE\\SSA Global\\Optimize", "OptimizationLogMode", temp);
	if (temp == "")
		temp = "0";
	m_EngineLogListCtrl.SetCurSel(atoi(temp));

	GetRemoteRegistryData(serverResource, "SOFTWARE\\SSA Global\\Optimize", "StartEngineAtBoot", temp);
	if (temp == "")
		temp = "1";
	m_StartEngine = (atoi(temp) == 1);

	GetRemoteRegistryData(serverResource, "SOFTWARE\\SSA Global\\Optimize", "DBLogging", temp);
	m_DatabaseLogging = (temp != "");

	GetRemoteRegistryData(serverResource, "SOFTWARE\\SSA Global\\Optimize", "DBBuffer", m_DatabaseBuffer);
	if (m_DatabaseBuffer == "")
		m_DatabaseBuffer = "500";

	CString mrudb;
	GetRemoteRegistryData(serverResource, "SOFTWARE\\SSA Global\\Optimize", "MRUDB", mrudb);
	int idx = 0;
	CStringArray strings;


	m_DatabaseListCtrl.DeleteAllItems();
	while (m_MRUDBListCtrl.GetCount() > 0)
		m_MRUDBListCtrl.DeleteString(0);

	temp2.Format("DB%d", idx);
	GetRemoteRegistryData(serverResource, "SOFTWARE\\SSA Global\\Optimize", temp2, temp);

	while (temp != "") {
		ParseString(temp, "|", strings);
		temp.Format("Before Idx: %d", idx);
		idx = m_DatabaseListCtrl.InsertItem(idx, strings[0]);
		temp.Format("After Idx: %d", idx);
		m_DatabaseListCtrl.SetItemText(idx, 1, strings[1]);
		m_DatabaseListCtrl.SetItemText(idx, 2, strings[2]);
		m_MRUDBListCtrl.AddString(strings[0]);
		idx++;
		temp2.Format("DB%d", idx);
		GetRemoteRegistryData(serverResource, "SOFTWARE\\SSA Global\\Optimize", temp2, temp);
	}
	
	CRect r;
	m_MRUDBListCtrl.GetClientRect(&r);
	m_MRUDBListCtrl.SetCurSel(m_MRUDBListCtrl.FindString(0, mrudb));
	

	UpdateData(FALSE);
	
	return;
}

CString CServerPage::ExtractMemoryFromService(CString &serviceParameters)
{
	CString memory = "";
	int maxMemory;

	if (serviceParameters == "")
		return "";

	int idx1, idx2;
	idx1 = serviceParameters.Find("x:", 0);
	idx2 = serviceParameters.Find(")", idx1);
	if (idx2 > idx1)
		memory = serviceParameters.Mid(idx1+2, (idx2-(idx1+2)));
	if (memory != "")
		maxMemory = atoi(memory);

	memory.Format("%.0f", (double)maxMemory/1024);

	return memory;
}

void ParseString(const CString &string, const CString &delimiter, CStringArray &strings)
{
	int idx;
	CString temp = string;

	strings.RemoveAll();

	idx = temp.Find(delimiter);
	if (idx < 0) {
		strings.Add(temp);
		return;
	}

	while (idx >= 0) {
		strings.Add(temp.Left(idx));
		temp = temp.Mid(idx+delimiter.GetLength());
		idx = temp.Find(delimiter);
	}

	if (temp.GetLength() > 0)
		strings.Add(temp);

	temp.Format("Strings: %d", strings.GetSize());

	return;

}

BOOL CServerPage::SaveValues()
{
	CString temp, temp2;
	CString serverResource;
	CStringArray keys, values;

	temp = GetLocalHostname();
	temp.MakeUpper();
	temp2 = m_CurrentServer;
	temp2.MakeUpper();

	CWaitCursor cwc;

	//if (temp == temp2)
		serverResource = "";
	//else
		//serverResource = "\\\\" + m_CurrentServer;


	UpdateData(TRUE);

	//keys.Add("DBBuffer");
	//values.Add(m_DatabaseBuffer);

	//keys.Add("StartEngineAtBoot");
	//temp.Format("%d", m_StartEngine);
	//values.Add(temp);

	keys.Add("MRUDB");
	m_MRUDBListCtrl.GetWindowText(temp);
	values.Add(temp);


	keys.Add("LogFilePath");
	values.Add(m_str);

	keys.Add("OptimizationLogMode");
	temp.Format("%d", m_EngineLogListCtrl.GetCurSel());
	values.Add(temp);

	keys.Add("DBLogging");
	if (m_DatabaseLogging)
		values.Add("2:1");
	else
		values.Add("");

	SetRemoteRegistryData(serverResource, "Software\\SSA Global\\Optimize", keys, values);

	//temp.Format("-fm(n:20000,x:%.0f) -fns %s:6060", atof(m_AnalysisMemory)*1024, m_CurrentServer);

	//SetRemoteRegistryData(serverResource, 
		//"System\\CurrentControlSet\\Services\\Optimize Analysis Engine Server\\Parameters",
		//"CommandLine", temp);

	//temp.Format("-fm(n:20000,x:%.0f) -fns %s:6060", atof(m_BusinessMemory)*1024, m_CurrentServer);
	//SetRemoteRegistryData(serverResource, 
		//"System\\CurrentControlSet\\Services\\Optimize Business Logic Server\\Parameters",
		//"CommandLine", temp);

	//temp.Format("-fm(n:20000,x:%.0f) -fns %s:6060", atof(m_DatabaseMemory)*1024, m_CurrentServer);
	//SetRemoteRegistryData(serverResource, 
		//"System\\CurrentControlSet\\Services\\Optimize Database Server\\Parameters",
		//"CommandLine", temp);
	
	return FALSE;

}

void CServerPage::OnOK() 
{
	
	CPropertyPage::OnOK();
}

BOOL CServerPage::OnApply() 
{
	SaveValues();
	
	this->SetModified(FALSE);

	return CPropertyPage::OnApply();
}

void CServerPage::Clear()
{
	m_AnalysisMemory = "";
	m_BusinessMemory = "";
	m_DatabaseMemory = "";
	m_DatabaseBuffer = "";
	m_DatabaseListCtrl.DeleteAllItems();
	m_DatabaseLogging = FALSE;
	m_EngineLogListCtrl.SetCurSel(-1);
	m_MRUDBListCtrl.SetCurSel(-1);
	m_StartEngine = FALSE;
	//m_VersionListCtrl.DeleteAllItems();
}


//afx_msg LRESULT CServerPage::OnGetMemory()
void CServerPage::OnGetMemory(WPARAM wParam, LPARAM lParam)
{
	if (m_CurrentServer == GetLocalHostname()) {
		MEMORYSTATUS memStats;
		
		memStats.dwLength = sizeof(MEMORYSTATUS);
		
		GlobalMemoryStatus(&memStats);
		
		m_PhysicalMemory.Format("%d / %d", memStats.dwAvailPhys/1024, 
			memStats.dwTotalPhys/1024);
		m_VirtualMemory.Format("%d / %d", memStats.dwAvailPageFile/1024,
			memStats.dwTotalPageFile/1024);
		
		return;
	}

	CProgress *pProgress = new CProgress();
	pProgress->m_Title = "Connecting to server ";
	pProgress->m_Title += m_CurrentServer;

	pProgress->Create(IDD_PROGRESS, this);
	pProgress->m_Message.Format("Retrieving information from server...");
	pProgress->m_ProgressCtrl.SetRange32(0, 20);
	pProgress->m_ProgressCtrl.SetStep(1);
	pProgress->UpdateData(FALSE);
	pProgress->CenterWindow();
	pProgress->ShowWindow(SW_SHOW);
	pProgress->UpdateWindow();
	pProgress->m_Stopped = FALSE;


	try {
		CWaitCursor cwc;
		int rc = 0;
		m_PropertyList.RemoveAll();
		m_PropertyList.Add("TotalVisibleMemorySize");
		m_PropertyList.Add("FreePhysicalMemory");
		m_PropertyList.Add("TotalVirtualMemorySize");
		m_PropertyList.Add("FreeVirtualMemory");

		CWinThread *pThread = AfxBeginThread(GetMemoryThread, this);
		
		BOOL bThreadDone = false;
		
		g_ThreadDone.ResetEvent();

		for (int i=0; i < 20; ++i) {
			if (pProgress->m_Stopped) {
				pProgress->DestroyWindow();
				//m_pIWbemLocator->Release();
				return;
			}
			pProgress->m_ProgressCtrl.StepIt();
			if ( !PeekAndPump() )
				break;
			
			bThreadDone = g_ThreadDone.Lock(0);
			if (bThreadDone)
				break;
		}
		
		if (bThreadDone) {
			if (! (m_PropertyList[0] == "-1"))  {
				CString temp1, temp2, temp3;
				temp1 = m_PropertyList[1];
				temp2 = m_PropertyList[0];
				
				m_PhysicalMemory.Format("%s / %s", m_PropertyList[1], m_PropertyList[0]);
				m_VirtualMemory.Format("%d / %d", atoi(m_PropertyList[3])-atoi(temp1), 
					atoi(m_PropertyList[2])-atoi(temp2));
			}
			else {
				m_PhysicalMemory = "Unavailable";
				m_VirtualMemory = "Unavailable";
			}
		}
		else {
			m_PhysicalMemory = "Unavailable";
			m_VirtualMemory = "Unavailable";
		}
		
	}
	catch(...) {
		m_PhysicalMemory = "Unavailable";
		m_VirtualMemory = "Unavailable";
	}




	pProgress->DestroyWindow();
	
	UpdateData(FALSE);


	return;

}

UINT GetMemoryThread(LPVOID pParam)
{
	CServerPage *pServerPage = (CServerPage *)pParam;

	if (GetWMIProperty(pServerPage->m_pIWbemLocator, pServerPage->m_pIWbemServices,
								pServerPage->m_CurrentServer, pServerPage->m_PropertyList) < 0) {
		pServerPage->m_PropertyList.InsertAt(0, "-1");
	}

	g_ThreadDone.SetEvent();

	return 0;

}

void CServerPage::OnChange() 
{
	this->SetModified(TRUE);	
}

BOOL CServerPage::OnHelpInfo(HELPINFO* pHelpInfo) 
{
	CString topic;

	switch (pHelpInfo->iCtrlId) {
	//case IDC_VERSION_LIST:
		//topic = "Server_ProgramInformation";
		//break;
	//case IDC_PHYSICAL:
	//case IDC_VIRTUAL:
		//topic = "Server_Memory";
		//break;
	//case IDC_ANALYSIS_MEMORY:
	//case IDC_BUSINESS_MEMORY:
	//case IDC_DATABASE_MEMORY:
		//topic = "Server_MaximumMemory";
		//break;


	case IDC_SERVICE_LIST: //Lester add
		topic = "Status_Services"; //Lester add
		break; //Lester add

	case IDC_ENGINE_LOG_LIST:
		topic = "Server_EngineLogMode";
		break;
	//case IDC_START_ENGINE:
		//topic = "Server_AutomaticallyStartEngine";
		//break;
	case IDC_DATABASE_LIST:
		topic = "Server_Databases";
		break;
	case IDC_MRUDB_LIST:
		topic = "Server_DefaultDatabase";
		break;
	case IDC_DATABASE_LOGGING:
		topic = "Server_DatabaseLogging";
		break;
	//case IDC_DATABASE_BUFFER:
		//topic = "Server_DatabaseBufferSize";
		//break;
	default:
		ShowScreenHelp("Server_Overview");
		return FALSE;
		break;
	}

	ShowFieldHelp(topic);

	return FALSE;
}

BOOL CServerPage::OnNotify(WPARAM wParam, LPARAM lParam, LRESULT* pResult) 
{
	NMHDR* pNMHDR = (NMHDR*)lParam;
	if (pNMHDR->code == PSN_HELP) {
		ShowScreenHelp("Server_Overview");
		return TRUE;
	}
	
	return CPropertyPage::OnNotify(wParam, lParam, pResult);
}

void CServerPage::OnBnClickedReset()
{
	// TODO: Add your control notification handler code here
}

void CServerPage::OnBnClickedStart()
{
	CString temp;
	CWaitCursor cwc;

	BOOL startFailed = FALSE;
	CArray<int, int> itemList;

	if (m_ServiceListCtrl.GetSelectedCount() > 0) {
		POSITION pos = m_ServiceListCtrl.GetFirstSelectedItemPosition();
		while (pos != NULL) {
			int curSel = m_ServiceListCtrl.GetNextSelectedItem(pos);
			itemList.Add(curSel);
		}
	}
	else {
		for (int i=0; i < m_ServiceListCtrl.GetItemCount(); ++i) {
			itemList.Add(i);
		}
	}

	for (int i=0; i < itemList.GetSize(); ++i) {
		int curSel = itemList[i];
		
		temp = m_ServiceListCtrl.GetItemText(curSel, 2);
		if (StartAService(temp))
			startFailed = TRUE;
	}
	

	if (startFailed)
		AfxMessageBox("At least one service failed to start.");

	Sleep(1000);
	
	OnBnClickedRefresh();
}

int CServerPage::StartAService(const CString &service)
{
	SC_HANDLE hServiceMgr, hService;
	CString serverResource, temp, temp2;
	DWORD rc;
	SERVICE_STATUS serviceStatus;

	temp = GetLocalHostname();
	temp.MakeUpper();
	temp2 = m_CurrentServer2;
	temp2.MakeUpper();

	if (temp == temp2)
		serverResource = "";
	else
		serverResource = "\\\\" + m_CurrentServer2;


	hServiceMgr = OpenSCManager(serverResource, SERVICES_ACTIVE_DATABASE, SC_MANAGER_ALL_ACCESS);
	if (hServiceMgr == NULL) {
		rc = GetLastError();
		if (rc == ERROR_ACCESS_DENIED || rc == ERROR_LOGON_FAILURE) {
			if (serverResource != "")
				rc = ConnectToServer(m_CurrentServer2);
			if (rc != 0)
				return FALSE;
			hServiceMgr = OpenSCManager(serverResource, SERVICES_ACTIVE_DATABASE, SC_MANAGER_ALL_ACCESS);
			if (hServiceMgr == NULL) {
				ShowLastError();
				return FALSE;
			}
		}
	}
	
	int numTimes = 5;
	BOOL startFailed = FALSE;
	
	if (service.Left(13) == "OracleService") {
		temp.Format("OracleStart%s", service.Mid(13));
		
		hService = OpenService(hServiceMgr, temp, SERVICE_ALL_ACCESS);
		if (hService == NULL) {
			hService = OpenService(hServiceMgr, service, SERVICE_ALL_ACCESS);	
		}
	}
	else
		hService = OpenService(hServiceMgr, service, SERVICE_ALL_ACCESS);	
	
	
	QueryServiceStatus(hService, &serviceStatus);
	numTimes = 5;
	while (serviceStatus.dwCurrentState != SERVICE_RUNNING &&
		serviceStatus.dwCurrentState != SERVICE_START_PENDING &&
		numTimes > 0) {
		
		numTimes--;
		if (! StartService(hService, 0, NULL)) {
			ShowLastError(service);
			startFailed = TRUE;
			break;
		}
		
		QueryServiceStatus(hService, &serviceStatus);
		
	}
	
	if (! startFailed && serviceStatus.dwCurrentState != SERVICE_RUNNING &&
		serviceStatus.dwCurrentState != SERVICE_START_PENDING) {
		startFailed = TRUE;
	}
	
	CloseServiceHandle(hService);
	CloseServiceHandle(hServiceMgr);

	return startFailed;
}

void CServerPage::OnBnClickedRefresh()
{
	LoadServices();
}

void CServerPage::LoadServices(void)
{
	SC_HANDLE hServiceMgr, hService;
	CString serverResource, temp, temp2;
	DWORD rc;
	SERVICE_STATUS serviceStatus;

	temp = GetLocalHostname();
	temp.MakeUpper();
	temp2 = m_CurrentServer2;
	temp2.MakeUpper();

	if (temp == temp2)
		serverResource = "";
	else
		serverResource = "\\\\" + m_CurrentServer2;


	hServiceMgr = OpenSCManager(serverResource, SERVICES_ACTIVE_DATABASE, SC_MANAGER_ALL_ACCESS);
	if (hServiceMgr == NULL) {
		rc = GetLastError();
		if (rc == ERROR_ACCESS_DENIED || rc == ERROR_LOGON_FAILURE) {
			if (serverResource != "")
				rc = ConnectToServer(m_CurrentServer2);
			if (rc != 0)
				return;
			hServiceMgr = OpenSCManager(serverResource, SERVICES_ACTIVE_DATABASE, SC_MANAGER_ALL_ACCESS);
			if (hServiceMgr == NULL) {
				ShowLastError();
				return;
			}
		}
	}
	
	m_ServiceListCtrl.DeleteAllItems();

	for (int i=0; i < m_ServiceList.GetSize(); ++i) {
		CString service = m_ServiceList[i];
		CString displayName;
		
		//if (m_ServiceList[i].Find("|") >= 0) { Lester comment out start
			//displayName = m_ServiceList[i].Left(m_ServiceList[i].Find("|"));
			//if (displayName != "Optimize Forte Manager")
				//displayName += " Database";

			//service = m_ServiceList[i].Mid(m_ServiceList[i].Find("|")+1);
		//}
		//else { Lester comment out till here
			displayName = service;
		//} Lester comment out

		m_ServiceListCtrl.InsertItem(i, displayName);
		m_ServiceListCtrl.SetItemText(i, 2, service);
		
		if (service.Left(13) == "OracleService")
			service.Replace("Service", "Start");

		hService = OpenService(hServiceMgr, service, SERVICE_QUERY_STATUS);
		if (hService == NULL) {
			if (service.Left(11) == "OracleStart") {
				service.Replace("OracleStart", "OracleService");
				hService = OpenService(hServiceMgr, service, SERVICE_QUERY_STATUS);

				if (hService == NULL) {
					m_ServiceListCtrl.SetItemText(i, 1, GetErrorText(GetLastError()));
					continue;
				}
			}
		}

		if (QueryServiceStatus(hService, &serviceStatus)) {
			DisplayServiceStatus(i, serviceStatus.dwCurrentState);
		}
		else
			m_ServiceListCtrl.SetItemText(i, 1, "Unknown");
		
		CloseServiceHandle(hService);

	}

	CloseServiceHandle(hServiceMgr);


	return;
}


int CServerPage::GetServiceList()
{
	CString temp, temp2;

	int idx = 0;
	CStringArray strings;

	temp2.Format("DB%d", idx);
	GetRemoteRegistryData("", "SOFTWARE\\SSA Global\\Optimize", temp2, temp);

	while (temp != "") {
		ParseString(temp, "|", strings);
		if (strings.GetSize() >= 3) {
			strings[1].TrimLeft("@");
			if (strings[2] == "Oracle") {
				temp.Format("OracleService%s", strings[1]);
				m_ServiceList.Add(temp);
			}
		}
		idx++;
		temp2.Format("DB%d", idx);
		GetRemoteRegistryData("", "SOFTWARE\\SSA Global\\Optimize", temp2, temp);
	}
	
	return 0;
}


void CServerPage::DisplayServiceStatus(int idx, DWORD currentStatus)
{
	switch (currentStatus) {
	case SERVICE_STOPPED:
		m_ServiceListCtrl.SetItemText(idx, 1, "Stopped");
		break;
	case SERVICE_START_PENDING:
		m_ServiceListCtrl.SetItemText(idx, 1, "Start Pending");
		break;
	case SERVICE_STOP_PENDING:
		m_ServiceListCtrl.SetItemText(idx, 1, "Stop Pending");
		break;
	case SERVICE_RUNNING:
		m_ServiceListCtrl.SetItemText(idx, 1, "Running");
		break;
	case SERVICE_CONTINUE_PENDING:
		m_ServiceListCtrl.SetItemText(idx, 1, "Continue Pending");
		break;
	case SERVICE_PAUSE_PENDING:
		m_ServiceListCtrl.SetItemText(idx, 1, "Pause Pending");
		break;
	case SERVICE_PAUSED:
		m_ServiceListCtrl.SetItemText(idx, 1, "Paused");
		break;
	}

	return;

}
void CServerPage::OnBnClickedStop()
{
	CString temp;

	CWaitCursor cwc;

	BOOL stopFailed = FALSE;
	CArray<int, int> itemList;

	if (m_ServiceListCtrl.GetSelectedCount() > 0) {
		POSITION pos = m_ServiceListCtrl.GetFirstSelectedItemPosition();
		while (pos != NULL) {
			int curSel = m_ServiceListCtrl.GetNextSelectedItem(pos);
			itemList.Add(curSel);
		}
	}
	else {
		for (int i=0; i < m_ServiceListCtrl.GetItemCount(); ++i) {
			itemList.Add(i);
		}
	}

	for (int i=0; i < itemList.GetSize(); ++i) {

		int curSel = itemList[i];
		temp = m_ServiceListCtrl.GetItemText(curSel, 2);
		if (StopAService(temp, m_ServiceListCtrl.GetItemText(curSel, 0)))
			stopFailed = TRUE;

	}

	if (stopFailed)
		AfxMessageBox("At least one service failed to stop.");

	Sleep(1000);

	OnBnClickedRefresh();
}

int CServerPage::StopAService(const CString &service, const CString& display)
{
	SC_HANDLE hServiceMgr, hService;
	CString serverResource, temp, temp2;
	DWORD rc;
	SERVICE_STATUS serviceStatus;

	hServiceMgr = OpenSCManager(serverResource, SERVICES_ACTIVE_DATABASE, SC_MANAGER_ALL_ACCESS);
	if (hServiceMgr == NULL) {
		rc = GetLastError();
		if (rc == ERROR_ACCESS_DENIED || rc == ERROR_LOGON_FAILURE) {
			if (serverResource != "")
				rc = ConnectToServer(m_CurrentServer);
			if (rc != 0)
				return FALSE;
			hServiceMgr = OpenSCManager("", SERVICES_ACTIVE_DATABASE, SC_MANAGER_ALL_ACCESS);
			if (hServiceMgr == NULL) {
				ShowLastError();
				return FALSE;
			}
		}
	}
	
	int numTimes = 5;
	BOOL stopFailed = FALSE;

	temp = service;
	
	if (temp.Left(13) == "OracleService") {
		temp2 = "OracleStart" + temp.Mid(13);
		
		hService = OpenService(hServiceMgr, temp2, SERVICE_ALL_ACCESS);
		if (hService != NULL) {				
			
			QueryServiceStatus(hService, &serviceStatus);
			numTimes = 5;
			while (serviceStatus.dwCurrentState != SERVICE_STOPPED &&
				serviceStatus.dwCurrentState != SERVICE_STOP_PENDING &&
				numTimes > 0) {
				
				numTimes--;
				if (! ControlService(hService,SERVICE_CONTROL_STOP, &serviceStatus)) {
					ShowLastError();
					stopFailed = TRUE;
				}
				
			}
			
			if (! stopFailed && serviceStatus.dwCurrentState != SERVICE_STOPPED &&
				serviceStatus.dwCurrentState != SERVICE_STOP_PENDING) {
				stopFailed = TRUE;
			}
			
			if (stopFailed) {
				CloseServiceHandle(hService);
				CloseServiceHandle(hServiceMgr);
				return FALSE;
			}
			
		}
	}




	hService = OpenService(hServiceMgr, service, SERVICE_ALL_ACCESS);
	if (hService == NULL) {
		ShowLastError();
		CloseServiceHandle(hServiceMgr);
		return FALSE;
	}
	
	int numTries = 5;

	while (numTries > 0) {
		
		LPENUM_SERVICE_STATUS lpServices;
		DWORD cbBytesNeeded;
		DWORD servicesReturned;
		
		if (! EnumDependentServices(hService, SERVICE_ACTIVE, NULL, 
			0, &cbBytesNeeded, &servicesReturned))
		{
			if (GetLastError() != ERROR_MORE_DATA)
				return FALSE;
			
			lpServices = (LPENUM_SERVICE_STATUS)malloc(cbBytesNeeded);
			
			EnumDependentServices(hService, SERVICE_ACTIVE, lpServices,
				cbBytesNeeded, &cbBytesNeeded, &servicesReturned);
			
			for (unsigned int i=0; i < servicesReturned; ++i) {
				StopAService(lpServices[i].lpServiceName, lpServices[i].lpDisplayName);
			}
			
			free(lpServices);
		}
		else
			break;

		Sleep(1000);
		numTries--;
	}


	if (numTries == 0) {
		CString temp;
		temp.Format("The %s service failed to stop because \n"
			"its dependent services could not be stopped.\n"
			"Please try again.",
			display);
		AfxMessageBox(temp);
		return TRUE;
	}

	QueryServiceStatus(hService, &serviceStatus);
	numTimes = 5;
	while (serviceStatus.dwCurrentState != SERVICE_STOPPED &&
		serviceStatus.dwCurrentState != SERVICE_STOP_PENDING &&
		numTimes > 0) {
		
		numTimes--;
		if (! ControlService(hService,SERVICE_CONTROL_STOP, &serviceStatus)) {
			ShowLastError(service);
			stopFailed = TRUE;
			break;
		}
		
	}
	
	if (! stopFailed && serviceStatus.dwCurrentState != SERVICE_STOPPED &&
		serviceStatus.dwCurrentState != SERVICE_STOP_PENDING) {
		stopFailed = TRUE;
	}
	
	CloseServiceHandle(hService);
	CloseServiceHandle(hServiceMgr);


	return stopFailed;

}

void CServerPage::OnBnClickedBrowse()
{
	char szLogFolder[_MAX_PATH] = {0};
	CString temp = GetRegistryData( "SOFTWARE\\SSA Global\\Optimize", "LogFilePath");
	if (temp != "") {
		strncpy(szLogFolder, temp.GetBuffer(), sizeof(szLogFolder)-1);
	}
	if (XBrowseForFolder(m_hWnd, m_str, szLogFolder, sizeof(szLogFolder))) {
		m_str = szLogFolder;
		this->SetModified(TRUE);
		this->SetDlgItemText(IDC_DATABASE_LOGFILE, szLogFolder);
	}
}

void CServerPage::OnEnChangeDatabaseLogfile()
{
	// TODO:  If this is a RICHEDIT control, the control will not
	// send this notification unless you override the CPropertyPage::OnInitDialog()
	// function and call CRichEditCtrl().SetEventMask()
	// with the ENM_CHANGE flag ORed into the mask.

	// TODO:  Add your control notification handler code here
	this->SetModified(TRUE);	
}

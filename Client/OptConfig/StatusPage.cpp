// StatusPage.cpp : implementation file
//

#include "stdafx.h"
#include "optconfig.h"
#include "StatusPage.h"
#include "ClientPage.h"
#include "RegistryFunctions.h"
#include "Progress.h"

#include "winsvc.h"
#include <afxtempl.h>

#ifdef _DEBUG
#define new DEBUG_NEW
#undef THIS_FILE
static char THIS_FILE[] = __FILE__;
#endif

/////////////////////////////////////////////////////////////////////////////
// CStatusPage property page

extern DWORD ConnectToServer(const CString &serverName);
extern CString GetLocalHostname();
extern void ShowLastError(const CString& extra = "");
extern CString GetErrorText(DWORD error);
extern void ParseString(const CString &string, const CString &delimiter, CStringArray &strings);
extern void ShowScreenHelp(const CString &helpTopic);
extern void ShowFieldHelp(const CString &helpTopic);

IMPLEMENT_DYNCREATE(CStatusPage, CPropertyPage)

CStatusPage::CStatusPage() : CPropertyPage(CStatusPage::IDD)
{
	//{{AFX_DATA_INIT(CStatusPage)
	//}}AFX_DATA_INIT
}

CStatusPage::~CStatusPage()
{
}

void CStatusPage::DoDataExchange(CDataExchange* pDX)
{
	CPropertyPage::DoDataExchange(pDX);
	//{{AFX_DATA_MAP(CStatusPage)
	DDX_Control(pDX, IDC_SERVICE_LIST, m_ServiceListCtrl);
	//}}AFX_DATA_MAP
}


BEGIN_MESSAGE_MAP(CStatusPage, CPropertyPage)
	//{{AFX_MSG_MAP(CStatusPage)
	ON_BN_CLICKED(IDC_START, OnStart)
	ON_BN_CLICKED(IDC_STOP, OnStop)
	ON_BN_CLICKED(IDC_REFRESH, OnRefresh)
	ON_WM_HELPINFO()
	ON_BN_CLICKED(IDC_RESET, OnResetOptimize)
	//}}AFX_MSG_MAP
END_MESSAGE_MAP()

/////////////////////////////////////////////////////////////////////////////
// CStatusPage message handlers

BOOL CStatusPage::OnInitDialog() 
{
	CPropertyPage::OnInitDialog();
	
	CRect r;
	m_CurrentServer = "";
	
	for (int i=0; i < m_ServiceListCtrl.GetHeaderCtrl()->GetItemCount(); ++i)
		m_ServiceListCtrl.GetHeaderCtrl()->DeleteItem(0);

	m_ServiceListCtrl.GetClientRect(&r);
	m_ServiceListCtrl.InsertColumn(0, "Service", LVCFMT_LEFT, r.Width()/2, 0);
	m_ServiceListCtrl.InsertColumn(1, "Status", LVCFMT_LEFT, r.Width()/2, 0);
	m_ServiceListCtrl.InsertColumn(2, "InternalName", LVCFMT_LEFT, 0, 0);


	
	return TRUE;  // return TRUE unless you set the focus to a control
	              // EXCEPTION: OCX Property Pages should return FALSE
}

BOOL CStatusPage::OnSetActive() 
{
	CPropertySheet *pParent = (CPropertySheet *)GetParent();
	CClientPage *pClientPage = (CClientPage *)pParent->GetPage(0);
	CStringArray serviceList;

	CWaitCursor cwc;

	if (pClientPage->m_Server != m_CurrentServer) {
		m_CurrentServer = pClientPage->m_Server;
		m_ServiceList.RemoveAll();
		//GetServiceList();
	}	
	
	LoadServices();
	return CPropertyPage::OnSetActive();
}

void CStatusPage::OnStart() 
{
	CString temp;
	CWaitCursor cwc;

	BOOL startFailed = FALSE;
	CArray<int, int> itemList;

	if (m_ServiceListCtrl.GetSelectedCount() > 0) {
		POSITION pos = m_ServiceListCtrl.GetFirstSelectedItemPosition();
		while (pos != NULL) {
			int curSel = m_ServiceListCtrl.GetNextSelectedItem(pos);
			itemList.Add(curSel);
		}
	}
	else {
		for (int i=0; i < m_ServiceListCtrl.GetItemCount(); ++i) {
			itemList.Add(i);
		}
	}

	for (int i=0; i < itemList.GetSize(); ++i) {
		int curSel = itemList[i];
		
		temp = m_ServiceListCtrl.GetItemText(curSel, 2);
		if (StartAService(temp))
			startFailed = TRUE;
	}
	

	if (startFailed)
		AfxMessageBox("At least one service failed to start.");

	Sleep(1000);


	
	OnRefresh();

}

void CStatusPage::OnStop() 
{
	CString temp;

	CWaitCursor cwc;

	BOOL stopFailed = FALSE;
	CArray<int, int> itemList;

	if (m_ServiceListCtrl.GetSelectedCount() > 0) {
		POSITION pos = m_ServiceListCtrl.GetFirstSelectedItemPosition();
		while (pos != NULL) {
			int curSel = m_ServiceListCtrl.GetNextSelectedItem(pos);
			itemList.Add(curSel);
		}
	}
	else {
		for (int i=0; i < m_ServiceListCtrl.GetItemCount(); ++i) {
			itemList.Add(i);
		}
	}

	for (int i=0; i < itemList.GetSize(); ++i) {

		int curSel = itemList[i];
		temp = m_ServiceListCtrl.GetItemText(curSel, 2);
		if (StopAService(temp, m_ServiceListCtrl.GetItemText(curSel, 0)))
			stopFailed = TRUE;

	}

	if (stopFailed)
		AfxMessageBox("At least one service failed to stop.");

	Sleep(1000);

	OnRefresh();

}

void CStatusPage::OnOK() 
{	
	CPropertyPage::OnOK();
}

void CStatusPage::LoadServices()
{
	SC_HANDLE hServiceMgr, hService;
	CString serverResource, temp, temp2;
	DWORD rc;
	SERVICE_STATUS serviceStatus;

	temp = GetLocalHostname();
	temp.MakeUpper();
	temp2 = m_CurrentServer;
	temp2.MakeUpper();

	if (temp == temp2)
		serverResource = "";
	else
		serverResource = "\\\\" + m_CurrentServer;


	hServiceMgr = OpenSCManager(serverResource, SERVICES_ACTIVE_DATABASE, SC_MANAGER_ALL_ACCESS);
	if (hServiceMgr == NULL) {
		rc = GetLastError();
		if (rc == ERROR_ACCESS_DENIED || rc == ERROR_LOGON_FAILURE) {
			if (serverResource != "")
				rc = ConnectToServer(m_CurrentServer);
			if (rc != 0)
				return;
			hServiceMgr = OpenSCManager(serverResource, SERVICES_ACTIVE_DATABASE, SC_MANAGER_ALL_ACCESS);
			if (hServiceMgr == NULL) {
				ShowLastError();
				return;
			}
		}
	}
	
	m_ServiceListCtrl.DeleteAllItems();

	for (int i=0; i < m_ServiceList.GetSize(); ++i) {
		CString service = m_ServiceList[i];
		CString displayName;
		
		if (m_ServiceList[i].Find("|") >= 0) {
			displayName = m_ServiceList[i].Left(m_ServiceList[i].Find("|"));
			if (displayName != "Optimize Forte Manager")
				displayName += " Database";

			service = m_ServiceList[i].Mid(m_ServiceList[i].Find("|")+1);
		}
		else {
			displayName = service;
		}

		m_ServiceListCtrl.InsertItem(i, displayName);
		m_ServiceListCtrl.SetItemText(i, 2, service);
		
		if (service.Left(13) == "OracleService")
			service.Replace("Service", "Start");

		hService = OpenService(hServiceMgr, service, SERVICE_QUERY_STATUS);
		if (hService == NULL) {
			if (service.Left(11) == "OracleStart") {
				service.Replace("OracleStart", "OracleService");
				hService = OpenService(hServiceMgr, service, SERVICE_QUERY_STATUS);

				if (hService == NULL) {
					m_ServiceListCtrl.SetItemText(i, 1, GetErrorText(GetLastError()));
					continue;
				}
			}
		}

		if (QueryServiceStatus(hService, &serviceStatus)) {
			DisplayServiceStatus(i, serviceStatus.dwCurrentState);
		}
		else
			m_ServiceListCtrl.SetItemText(i, 1, "Unknown");
		
		CloseServiceHandle(hService);

	}

	CloseServiceHandle(hServiceMgr);


	return;
}

void CStatusPage::DisplayServiceStatus(int idx, DWORD currentStatus)
{
	switch (currentStatus) {
	case SERVICE_STOPPED:
		m_ServiceListCtrl.SetItemText(idx, 1, "Stopped");
		break;
	case SERVICE_START_PENDING:
		m_ServiceListCtrl.SetItemText(idx, 1, "Start Pending");
		break;
	case SERVICE_STOP_PENDING:
		m_ServiceListCtrl.SetItemText(idx, 1, "Stop Pending");
		break;
	case SERVICE_RUNNING:
		m_ServiceListCtrl.SetItemText(idx, 1, "Running");
		break;
	case SERVICE_CONTINUE_PENDING:
		m_ServiceListCtrl.SetItemText(idx, 1, "Continue Pending");
		break;
	case SERVICE_PAUSE_PENDING:
		m_ServiceListCtrl.SetItemText(idx, 1, "Pause Pending");
		break;
	case SERVICE_PAUSED:
		m_ServiceListCtrl.SetItemText(idx, 1, "Paused");
		break;
	}

	return;

}

void CStatusPage::OnRefresh() 
{
	LoadServices();
}

/*
int CStatusPage::GetServiceList()
{
	CString temp, temp2, serverResource;
	temp = GetLocalHostname();
	temp.MakeUpper();
	temp2 = m_CurrentServer;
	temp2.MakeUpper();

	if (temp == temp2)
		serverResource = "";
	else
		serverResource = "\\\\" + m_CurrentServer;

	m_ServiceList.Add("Optimize Analysis Engine Server");
	m_ServiceList.Add("Optimize Business Logic Server");
	m_ServiceList.Add("Optimize Database Server");
	m_ServiceList.Add("Optimize Forte Manager|SlotEnv Environment Manager");

	int idx = 0;
	CStringArray strings;

	temp2.Format("DB%d", idx);
	GetRemoteRegistryData(serverResource, "SOFTWARE\\EXE Technologies\\Optimize", temp2, temp);

	while (temp != "") {
		ParseString(temp, "|", strings);
		if (strings.GetSize() == 3) {
			strings[1].TrimLeft("@");
			if (strings[2] == "Oracle") {
				temp.Format("%s|OracleService%s", strings[0], strings[1]);
				m_ServiceList.Add(temp);
			}
		}
		idx++;
		temp2.Format("DB%d", idx);
		GetRemoteRegistryData(serverResource, "SOFTWARE\\EXE Technologies\\Optimize", temp2, temp);
	}
	
	return 0;
}
*/

BOOL CStatusPage::OnHelpInfo(HELPINFO* pHelpInfo) 
{
	CString topic;
	
	switch (pHelpInfo->iCtrlId) {
	case IDC_SERVICE_LIST:
		topic = "Status_Services";
		break;
	case IDC_START:
		topic = "Status_Start";
		break;
	case IDC_STOP:
		topic = "Status_Stop";
		break;
	case IDC_REFRESH:
		topic = "Status_Refresh";
		break;
	default:
		topic = "Status_Overview";
		ShowScreenHelp(topic);
		return FALSE;
	}

	ShowFieldHelp(topic);

	return FALSE;
}

BOOL CStatusPage::OnNotify(WPARAM wParam, LPARAM lParam, LRESULT* pResult) 
{
	NMHDR* pNMHDR = (NMHDR*)lParam;
	if (pNMHDR->code == PSN_HELP) {
		ShowScreenHelp("Status_Page");
		return TRUE;
	}
	
	return CPropertyPage::OnNotify(wParam, lParam, pResult);
}

void CStatusPage::OnResetOptimize() 
{
	CProgress *pDlg = new CProgress;
	pDlg->m_ShowProgress = FALSE;
	pDlg->m_Message = "Resetting Optimize Services";
	pDlg->m_Title = "Reset Optimize";
	pDlg->Create(IDD_PROGRESS, this);
	pDlg->UpdateData(FALSE);
	pDlg->CenterWindow();
	pDlg->ShowWindow(SW_SHOW);
	pDlg->UpdateWindow();


	StopAService("SlotEnv Environment Manager", "Optimize Forte Manager");

	OnRefresh();

	int numTries, times = 5;
	MSG msg;
	while (::PeekMessage(&msg, NULL, 0, 0, PM_NOREMOVE)) {
		if (!AfxGetApp()->PumpMessage()) {
			::PostQuitMessage(0);
			break;
		}
		Sleep(100);
		if (times-- <= 0)
			break;
	}

	Sleep(1000);

	StartAService("Optimize Service Manager");

	numTries = 30;
	while (numTries > 0) {
	
		OnRefresh();
		MSG msg;
		times = 3;
		while (::PeekMessage(&msg, NULL, 0, 0, PM_NOREMOVE)) {
			if (!AfxGetApp()->PumpMessage()) {
				::PostQuitMessage(0);
				break;
			}
			Sleep(100);
			if (times-- <= 0)
				break;
		}
		numTries--;
		
		BOOL oneStillStopped = FALSE;
		for (int i=0; i < 4; ++i) {
			
			if (m_ServiceListCtrl.GetItemText(i, 1) == "Stopped" ||
				m_ServiceListCtrl.GetItemText(i, 1) == "Start Pending") {
				oneStillStopped = TRUE;
				break;
			}
		}
		
		if (! oneStillStopped)
			break;

		Sleep(1000);
	}
	
	pDlg->DestroyWindow();

	if (numTries == 0)
		AfxMessageBox("Optimize was not reset successfully.\n"
		"Please stop and start the services manually.");
	else
		AfxMessageBox("Optimize was successfully reset.");
		
}

int CStatusPage::StopAService(const CString &service, const CString& display)
{
	SC_HANDLE hServiceMgr, hService;
	CString serverResource, temp, temp2;
	DWORD rc;
	SERVICE_STATUS serviceStatus;

	temp = GetLocalHostname();
	temp.MakeUpper();
	temp2 = m_CurrentServer;
	temp2.MakeUpper();

	if (temp == temp2)
		serverResource = "";
	else
		serverResource = "\\\\" + m_CurrentServer;


	hServiceMgr = OpenSCManager(serverResource, SERVICES_ACTIVE_DATABASE, SC_MANAGER_ALL_ACCESS);
	if (hServiceMgr == NULL) {
		rc = GetLastError();
		if (rc == ERROR_ACCESS_DENIED || rc == ERROR_LOGON_FAILURE) {
			if (serverResource != "")
				rc = ConnectToServer(m_CurrentServer);
			if (rc != 0)
				return FALSE;
			hServiceMgr = OpenSCManager(serverResource, SERVICES_ACTIVE_DATABASE, SC_MANAGER_ALL_ACCESS);
			if (hServiceMgr == NULL) {
				ShowLastError();
				return FALSE;
			}
		}
	}
	
	int numTimes = 5;
	BOOL stopFailed = FALSE;

	temp = service;
	
	if (temp.Left(13) == "OracleService") {
		temp2 = "OracleStart" + temp.Mid(13);
		
		hService = OpenService(hServiceMgr, temp2, SERVICE_ALL_ACCESS);
		if (hService != NULL) {				
			
			QueryServiceStatus(hService, &serviceStatus);
			numTimes = 5;
			while (serviceStatus.dwCurrentState != SERVICE_STOPPED &&
				serviceStatus.dwCurrentState != SERVICE_STOP_PENDING &&
				numTimes > 0) {
				
				numTimes--;
				if (! ControlService(hService,SERVICE_CONTROL_STOP, &serviceStatus)) {
					ShowLastError();
					stopFailed = TRUE;
				}
				
			}
			
			if (! stopFailed && serviceStatus.dwCurrentState != SERVICE_STOPPED &&
				serviceStatus.dwCurrentState != SERVICE_STOP_PENDING) {
				stopFailed = TRUE;
			}
			
			if (stopFailed) {
				CloseServiceHandle(hService);
				CloseServiceHandle(hServiceMgr);
				return FALSE;
			}
			
		}
	}




	hService = OpenService(hServiceMgr, service, SERVICE_ALL_ACCESS);
	if (hService == NULL) {
		ShowLastError();
		CloseServiceHandle(hServiceMgr);
		return FALSE;
	}
	
	int numTries = 5;

	while (numTries > 0) {
		
		LPENUM_SERVICE_STATUS lpServices;
		DWORD cbBytesNeeded;
		DWORD servicesReturned;
		
		if (! EnumDependentServices(hService, SERVICE_ACTIVE, NULL, 
			0, &cbBytesNeeded, &servicesReturned))
		{
			if (GetLastError() != ERROR_MORE_DATA)
				return FALSE;
			
			lpServices = (LPENUM_SERVICE_STATUS)malloc(cbBytesNeeded);
			
			EnumDependentServices(hService, SERVICE_ACTIVE, lpServices,
				cbBytesNeeded, &cbBytesNeeded, &servicesReturned);
			
			for (unsigned int i=0; i < servicesReturned; ++i) {
				StopAService(lpServices[i].lpServiceName, lpServices[i].lpDisplayName);
			}
			
			free(lpServices);
		}
		else
			break;

		Sleep(1000);
		numTries--;
	}


	if (numTries == 0) {
		CString temp;
		temp.Format("The %s service failed to stop because \n"
			"its dependent services could not be stopped.\n"
			"Please try again.",
			display);
		AfxMessageBox(temp);
		return TRUE;
	}

	QueryServiceStatus(hService, &serviceStatus);
	numTimes = 5;
	while (serviceStatus.dwCurrentState != SERVICE_STOPPED &&
		serviceStatus.dwCurrentState != SERVICE_STOP_PENDING &&
		numTimes > 0) {
		
		numTimes--;
		if (! ControlService(hService,SERVICE_CONTROL_STOP, &serviceStatus)) {
			ShowLastError(service);
			stopFailed = TRUE;
			break;
		}
		
	}
	
	if (! stopFailed && serviceStatus.dwCurrentState != SERVICE_STOPPED &&
		serviceStatus.dwCurrentState != SERVICE_STOP_PENDING) {
		stopFailed = TRUE;
	}
	
	CloseServiceHandle(hService);
	CloseServiceHandle(hServiceMgr);


	return stopFailed;

}

int CStatusPage::StartAService(const CString &service)
{
	SC_HANDLE hServiceMgr, hService;
	CString serverResource, temp, temp2;
	DWORD rc;
	SERVICE_STATUS serviceStatus;

	temp = GetLocalHostname();
	temp.MakeUpper();
	temp2 = m_CurrentServer;
	temp2.MakeUpper();

	if (temp == temp2)
		serverResource = "";
	else
		serverResource = "\\\\" + m_CurrentServer;


	hServiceMgr = OpenSCManager(serverResource, SERVICES_ACTIVE_DATABASE, SC_MANAGER_ALL_ACCESS);
	if (hServiceMgr == NULL) {
		rc = GetLastError();
		if (rc == ERROR_ACCESS_DENIED || rc == ERROR_LOGON_FAILURE) {
			if (serverResource != "")
				rc = ConnectToServer(m_CurrentServer);
			if (rc != 0)
				return FALSE;
			hServiceMgr = OpenSCManager(serverResource, SERVICES_ACTIVE_DATABASE, SC_MANAGER_ALL_ACCESS);
			if (hServiceMgr == NULL) {
				ShowLastError();
				return FALSE;
			}
		}
	}
	
	int numTimes = 5;
	BOOL startFailed = FALSE;
	
	if (service.Left(13) == "OracleService") {
		temp.Format("OracleStart%s", service.Mid(13));
		
		hService = OpenService(hServiceMgr, temp, SERVICE_ALL_ACCESS);
		if (hService == NULL) {
			hService = OpenService(hServiceMgr, service, SERVICE_ALL_ACCESS);	
		}
	}
	else
		hService = OpenService(hServiceMgr, service, SERVICE_ALL_ACCESS);	
	
	
	QueryServiceStatus(hService, &serviceStatus);
	numTimes = 5;
	while (serviceStatus.dwCurrentState != SERVICE_RUNNING &&
		serviceStatus.dwCurrentState != SERVICE_START_PENDING &&
		numTimes > 0) {
		
		numTimes--;
		if (! StartService(hService, 0, NULL)) {
			ShowLastError(service);
			startFailed = TRUE;
			break;
		}
		
		QueryServiceStatus(hService, &serviceStatus);
		
	}
	
	if (! startFailed && serviceStatus.dwCurrentState != SERVICE_RUNNING &&
		serviceStatus.dwCurrentState != SERVICE_START_PENDING) {
		startFailed = TRUE;
	}
	
	CloseServiceHandle(hService);
	CloseServiceHandle(hServiceMgr);

	return startFailed;

}

#if !defined(AFX_SERVERPAGE_H__3C336703_B04E_498E_A922_A68299494FE6__INCLUDED_)
#define AFX_SERVERPAGE_H__3C336703_B04E_498E_A922_A68299494FE6__INCLUDED_

#if _MSC_VER > 1000
#pragma once
#endif // _MSC_VER > 1000
// ServerPage.h : header file
//
#include <wbemcli.h>

/////////////////////////////////////////////////////////////////////////////
// CServerPage dialog

class CServerPage : public CPropertyPage
{
	DECLARE_DYNCREATE(CServerPage)

// Construction
public:
	CStringArray m_PropertyList;
	IWbemLocator *m_pIWbemLocator;
	IWbemServices *m_pIWbemServices;
	CString m_CurrentServer;
	CListCtrl	m_ServiceListCtrl; //Lester add
	CServerPage();
	~CServerPage();

// Dialog Data
	//{{AFX_DATA(CServerPage)
	enum { IDD = IDD_SERVER };
	CButton	m_GroupCtrl;
	CComboBox	m_MRUDBListCtrl;
	CComboBox	m_EngineLogListCtrl;
	CListCtrl	m_DatabaseListCtrl;
	//CListCtrl	m_VersionListCtrl;
	CString	m_AnalysisMemory;
	CString	m_BusinessMemory;
	CString	m_DatabaseBuffer;
	BOOL	m_DatabaseLogging;
	CString	m_DatabaseMemory;
	BOOL	m_StartEngine;
	CString	m_PhysicalMemory;
	CString	m_VirtualMemory;
	//}}AFX_DATA


// Overrides
	// ClassWizard generate virtual function overrides
	//{{AFX_VIRTUAL(CServerPage)
	public:
	virtual BOOL OnSetActive();
	virtual void OnOK();
	virtual BOOL OnApply();
	protected:
	virtual void DoDataExchange(CDataExchange* pDX);    // DDX/DDV support
	virtual BOOL OnNotify(WPARAM wParam, LPARAM lParam, LRESULT* pResult);
	//}}AFX_VIRTUAL

// Implementation
protected:
	// Generated message map functions
	//{{AFX_MSG(CServerPage)
	virtual BOOL OnInitDialog();
	afx_msg void OnChange();
	afx_msg BOOL OnHelpInfo(HELPINFO* pHelpInfo);
	//}}AFX_MSG
	DECLARE_MESSAGE_MAP()
	void  OnGetMemory(WPARAM wParam, LPARAM lParam);

private:

	void Clear();
	BOOL SaveValues();
	CString ExtractMemoryFromService(CString &serviceParameters);
	void LoadRegistryValues();
	DWORD LoadProgramList();


	int StartAService(const CString &service);
	int StopAService(const CString &service, const CString& display);
	int GetServiceList();
	void DisplayServiceStatus(int idx, DWORD currentStatus);
	void LoadServices();
	CString m_CurrentServer2; //Lester copy this from the status tab and rename it
	CStringArray m_ServiceList;






public:
	afx_msg void OnBnClickedReset();
	afx_msg void OnBnClickedStart();
	
	afx_msg void OnBnClickedRefresh();
	
	afx_msg void OnBnClickedStop();

	CString m_str;
	afx_msg void OnBnClickedBrowse();
	afx_msg void OnEnChangeDatabaseLogfile();
};

//{{AFX_INSERT_LOCATION}}
// Microsoft Visual C++ will insert additional declarations immediately before the previous line.

#endif // !defined(AFX_SERVERPAGE_H__3C336703_B04E_498E_A922_A68299494FE6__INCLUDED_)

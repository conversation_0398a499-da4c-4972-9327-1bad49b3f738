// GenericPropertySheet.cpp : implementation file
//

#include "stdafx.h"
#include "optconfig.h"
#include "GenericPropertySheet.h"

#ifdef _DEBUG
#define new DEBUG_NEW
#undef THIS_FILE
static char THIS_FILE[] = __FILE__;
#endif

extern void ShowFieldHelp(const CString &helpTopic);

/////////////////////////////////////////////////////////////////////////////
// CGenericPropertySheet

IMPLEMENT_DYNAMIC(CGenericPropertySheet, CPropertySheet)

CGenericPropertySheet::CGenericPropertySheet()
{
	CommonConstruct(NULL, 0);
}

CGenericPropertySheet::CGenericPropertySheet(UINT nIDCaption, CWnd* pParentWnd, UINT iSelectPage)
	:CPropertySheet(nIDCaption, pParentWnd, iSelectPage)
{
}

CGenericPropertySheet::CGenericPropertySheet(LPCTSTR pszCaption, CWnd* pParentWnd, UINT iSelectPage)
	:CPropertySheet(pszCaption, pParentWnd, iSelectPage)
{
}

CGenericPropertySheet::~CGenericPropertySheet()
{
}


BEGIN_MESSAGE_MAP(CGenericPropertySheet, CPropertySheet)
	//{{AFX_MSG_MAP(CGenericPropertySheet)
	ON_WM_HELPINFO()
	//}}AFX_MSG_MAP
END_MESSAGE_MAP()

/////////////////////////////////////////////////////////////////////////////
// CGenericPropertySheet message handlers

BOOL CGenericPropertySheet::OnInitDialog() 
{
	BOOL bResult = CPropertySheet::OnInitDialog();
	
    ModifyStyleEx(0, WS_EX_CONTEXTHELP);
	
	return bResult;
}

BOOL CGenericPropertySheet::OnHelpInfo(HELPINFO* pHelpInfo) 
{
	CString topic;

	switch (pHelpInfo->iCtrlId) {
	case IDOK:
		topic = "OK";
		ShowFieldHelp(topic);
		break;
	case IDCANCEL:
		topic = "Cancel";
		ShowFieldHelp(topic);
		break;
	case IDHELP:
		topic = "Help";
		ShowFieldHelp(topic);
		break;
	case ID_APPLY_NOW:
		topic = "Apply";
		ShowFieldHelp(topic);
		break;
	default:
		this->PressButton(PSBTN_HELP);
	}

	return FALSE;

}

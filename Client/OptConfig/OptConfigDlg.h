// OptConfigDlg.h : header file
//

#if !defined(AFX_OPTCONFIGDLG_H__B785296C_D325_4590_9D11_D455A603EE82__INCLUDED_)
#define AFX_OPTCONFIGDLG_H__B785296C_D325_4590_9D11_D455A603EE82__INCLUDED_

#if _MSC_VER > 1000
#pragma once
#endif // _MSC_VER > 1000

/////////////////////////////////////////////////////////////////////////////
// COptConfigDlg dialog

class COptConfigDlg : public CDialog
{
// Construction
public:
	COptConfigDlg(CWnd* pParent = NULL);	// standard constructor

// Dialog Data
	//{{AFX_DATA(COptConfigDlg)
	enum { IDD = IDD_OPTCONFIG_DIALOG };
	CTabCtrl	m_TabCtrl;
	//}}AFX_DATA

	// ClassWizard generated virtual function overrides
	//{{AFX_VIRTUAL(COptConfigDlg)
	protected:
	virtual void DoDataExchange(CDataExchange* pDX);	// DDX/DDV support
	//}}AFX_VIRTUAL

// Implementation
protected:
	HICON m_hIcon;

	// Generated message map functions
	//{{AFX_MSG(COptConfigDlg)
	virtual BOOL OnInitDialog();
	afx_msg void OnSysCommand(UINT nID, LPARAM lParam);
	afx_msg void OnDestroy();
	afx_msg void OnPaint();
	afx_msg HCURSOR OnQueryDragIcon();
	//}}AFX_MSG
	DECLARE_MESSAGE_MAP()
};

//{{AFX_INSERT_LOCATION}}
// Microsoft Visual C++ will insert additional declarations immediately before the previous line.

#endif // !defined(AFX_OPTCONFIGDLG_H__B785296C_D325_4590_9D11_D455A603EE82__INCLUDED_)

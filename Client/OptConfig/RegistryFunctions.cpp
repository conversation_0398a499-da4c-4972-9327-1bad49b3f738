#include "stdafx.h"
#include "RegistryFunctions.h"


CString GetRegistryData(const CString &path, const CString &key)
{
	CString regPath;
	char data[256];
	HKEY hRegKey;
	DWORD dwReturnLength;
	DWORD dwType = REG_SZ;
	
	strcpy(data, "");

	regPath = path;

	if (RegOpenKeyEx(HKEY_LOCAL_MACHINE, regPath, 0, KEY_READ, &hRegKey) != ERROR_SUCCESS) {
		return data;
	}

	dwReturnLength = 256;

	if (RegQueryValueEx(hRegKey, key, NULL, &dwType, (LPBYTE)data, &dwReturnLength) != ERROR_SUCCESS) {
		RegCloseKey(hRegKey);
		return data;
	}

	RegCloseKey(hRegKey);

	return data;
}

int GetKeyList(const CString &path, CStringArray &keyList)
{
	CString regPath;
	char data[256], key[256];
	HKEY hRegKey;
	DWORD idx;
	CString temp;

	strcpy(data, "");

	regPath = path;

	if (RegOpenKeyEx(HKEY_LOCAL_MACHINE, regPath, 0, KEY_READ, &hRegKey) != ERROR_SUCCESS) {
		return -1;
	}

	idx = 0;
	while (RegEnumKey(hRegKey, idx, key, 4096) == ERROR_SUCCESS) {
		keyList.Add(key);
		idx++;
	}

	RegCloseKey(hRegKey);

	return keyList.GetSize();

}

DWORD GetRemoteKeyList(const CString &machine, const CString &path, CStringArray &keyList)
{
	CString regPath;
	char data[256], key[256];
	HKEY hRegKey, hRemoteKey;
	DWORD idx;
	CString temp;
	DWORD rc;

	strcpy(data, "");

	regPath = path;

	if (machine != "") {
		rc = RegConnectRegistry(machine, HKEY_LOCAL_MACHINE, &hRemoteKey);
		if (rc != ERROR_SUCCESS) {
			return GetLastError();
		}
		
		if (RegOpenKeyEx(hRemoteKey, regPath, 0, KEY_READ, &hRegKey) != ERROR_SUCCESS) {
			return 0;
		}
	}
	else {
		if (RegOpenKeyEx(HKEY_LOCAL_MACHINE, regPath, 0, KEY_READ, &hRegKey) != ERROR_SUCCESS) {
			return 0;
		}
	}

	idx = 0;
	while (RegEnumKey(hRegKey, idx, key, 4096) == ERROR_SUCCESS) {
		keyList.Add(key);
		idx++;
	}

	RegCloseKey(hRegKey);

	if (machine != "")
		RegCloseKey(hRemoteKey);

	return keyList.GetSize();

}
DWORD GetRemoteRegistryData(const CString &machine, const CString &path, const CString &key, CString &data)
{
	CString regPath;
	char dataBuffer[256];
	HKEY hRegKey, hRemoteKey;
	DWORD dwReturnLength;
	DWORD dwType = REG_SZ;
	LONG rc;

	strcpy(dataBuffer, "");
	data = "";

	regPath = path;

	if (machine != "") {
		rc = RegConnectRegistry(machine, HKEY_LOCAL_MACHINE, &hRemoteKey);
		if (rc != ERROR_SUCCESS) {
			return rc;
		}
		
		if (RegOpenKeyEx(hRemoteKey, regPath, 0, KEY_READ, &hRegKey) != ERROR_SUCCESS) {
			RegCloseKey(hRemoteKey);
			return -1;
		}
	}
	else {
		if (RegOpenKeyEx(HKEY_LOCAL_MACHINE, regPath, 0, KEY_READ, &hRegKey) != ERROR_SUCCESS) {
			return -1;
		}
	}

	dwReturnLength = 256;

	if (RegQueryValueEx(hRegKey, key, NULL, &dwType, (LPBYTE)dataBuffer, &dwReturnLength) != ERROR_SUCCESS) {
		RegCloseKey(hRegKey);
		if (machine != "")
			RegCloseKey(hRemoteKey);
		data = dataBuffer;
		return 0;
	}

	RegCloseKey(hRegKey);

	if (machine != "")
		RegCloseKey(hRemoteKey);

	data = dataBuffer;
	return 0;
}

int SetRegistryData(const CString &path, const CString &key, const CString &value, const CString &subKey)
{
	CString regPath;
	HKEY hRegKey;
	DWORD dwType = REG_SZ;
	char valueBuffer[256];
	long rc;

	regPath = path;

	if (RegOpenKeyEx(HKEY_LOCAL_MACHINE, regPath, 0, KEY_SET_VALUE, &hRegKey) != ERROR_SUCCESS) {
		if (subKey != "") {
			if (RegCreateKeyEx(HKEY_LOCAL_MACHINE, regPath, 0, "",
				REG_OPTION_NON_VOLATILE, KEY_ALL_ACCESS, NULL, &hRegKey, NULL) != ERROR_SUCCESS)
				return -1;
		}
	}
	
	if (value == "") {
		rc = RegDeleteValue(hRegKey, key);
		if (rc != ERROR_SUCCESS) {
			RegCloseKey(hRegKey);
			return -1;
		}
	}
	else {
		strcpy(valueBuffer, value);
		
		rc =  RegSetValueEx(hRegKey, key, 0, dwType, (LPBYTE)valueBuffer, strlen(valueBuffer)+1);
		if (rc != ERROR_SUCCESS) {
			RegCloseKey(hRegKey);
			return -1;
		}
	}
	
	RegCloseKey(hRegKey);

	return 0;
}


int GetRegistryData(const CString &path, const CStringArray &keys, CStringArray &values)
{
	CString regPath, data;
	HKEY hRegKey;
	DWORD dwReturnLength;
	DWORD dwType = REG_SZ;
	
	data = "";

	regPath = path;

	if (RegOpenKeyEx(HKEY_LOCAL_MACHINE, regPath, 0, KEY_READ, &hRegKey) != ERROR_SUCCESS) {
		return -1;
	}

	for (int i=0; i < keys.GetSize(); ++i) {
		dwReturnLength = 256;
		if (RegQueryValueEx(hRegKey, keys[i], NULL, &dwType, (LPBYTE)data.GetBuffer(0), &dwReturnLength) != ERROR_SUCCESS) {
			data.ReleaseBuffer();
			values.Add("");
		}
		else {
			data.ReleaseBuffer();
			values.Add(data);
		}
	}

	RegCloseKey(hRegKey);

	return values.GetSize();

}


int SetRegistryData(const CString &path, const CStringArray &keys, const CStringArray &values)
{
	CString regPath;
	HKEY hRegKey;
	DWORD dwType = REG_SZ;
	char valueBuffer[256];
	int rc;

	regPath = path;

	if (RegOpenKeyEx(HKEY_LOCAL_MACHINE, regPath, 0, KEY_SET_VALUE, &hRegKey) != ERROR_SUCCESS) {
		return -1;
	}
	
	for (int i=0; i < keys.GetSize(); ++i) {	
		if (values[i] == "") {
			rc = RegDeleteValue(hRegKey, keys[i]);
			if (rc != ERROR_SUCCESS) {
				RegCloseKey(hRegKey);
				return -1;
			}
		}
		else {
			
			strcpy(valueBuffer, values[i]);
			
			if (RegSetValueEx(hRegKey, keys[i], 0, dwType, (LPBYTE)valueBuffer, (DWORD)values[i].GetLength()) != ERROR_SUCCESS) {
				RegCloseKey(hRegKey);
				return -1;
			}
		}
	}

	RegCloseKey(hRegKey);

	return 0;
}

int SetRemoteRegistryData(const CString &machine, const CString &path, const CString &key, const CString &value,
						  const CString &subKey)
{
	CString regPath;
	HKEY hRegKey, hRemoteKey;
	DWORD dwType = REG_SZ;
	char valueBuffer[256];
	long rc;
	
	regPath = path;
	if (subKey != "") {
		regPath += "\\";
		regPath += subKey;
	}
	
	if (machine != "") {
		rc = RegConnectRegistry(machine, HKEY_LOCAL_MACHINE, &hRemoteKey);
		if (rc != ERROR_SUCCESS) {
			CString msg;
			msg.Format("Error connecting to remote: %d\n", rc);
			AfxMessageBox(msg);
			return -1;
		}
		
		if (RegOpenKeyEx(hRemoteKey, regPath, 0, KEY_SET_VALUE, &hRegKey) != ERROR_SUCCESS) {
			if (subKey != "") {
				if (RegCreateKeyEx(hRemoteKey, regPath, 0, "",
					REG_OPTION_NON_VOLATILE, KEY_ALL_ACCESS, NULL, &hRegKey, NULL) != ERROR_SUCCESS) {
					RegCloseKey(hRemoteKey);
					return -1;
				}
			}
		}
	}
	else {
		if (RegOpenKeyEx(HKEY_LOCAL_MACHINE, regPath, 0, KEY_SET_VALUE, &hRegKey) != ERROR_SUCCESS) {
			if (subKey != "") {
				if (RegCreateKeyEx(HKEY_LOCAL_MACHINE, regPath, 0, "",
					REG_OPTION_NON_VOLATILE, KEY_ALL_ACCESS, NULL, &hRegKey, NULL) != ERROR_SUCCESS) {
					RegCloseKey(hRegKey);
					return -1;
				}
			}
		}
	}


	if (value == "") {
		rc = RegDeleteValue(hRegKey, key);
		if (rc != ERROR_SUCCESS) {
			RegCloseKey(hRegKey);
			if (machine != "")
				RegCloseKey(hRemoteKey);
			return -1;
		}
	}
	else {
		strcpy(valueBuffer, value);
		
		rc =  RegSetValueEx(hRegKey, key, 0, dwType, (LPBYTE)valueBuffer, strlen(valueBuffer)+1);
		if (rc != ERROR_SUCCESS) {
			if (machine != "")
				RegCloseKey(hRemoteKey);
			RegCloseKey(hRegKey);
			return -1;
		}
	}
	
	RegCloseKey(hRegKey);

	if (machine != "")
		RegCloseKey(hRemoteKey);

	return 0;
}

int SetRemoteRegistryData(const CString &machine, const CString &path, 
						  const CStringArray &keys, const CStringArray &values)
{
	CString regPath;
	HKEY hRegKey, hRemoteKey;
	DWORD dwType = REG_SZ;
	char valueBuffer[256];
	int rc;

	regPath = path;

	if (machine != "") {
		rc = RegConnectRegistry(machine, HKEY_LOCAL_MACHINE, &hRemoteKey);
		if (rc != ERROR_SUCCESS) {
			CString msg;
			msg.Format("Error connecting to remote: %d\n", rc);
			return -1;
		}
		
		if (RegOpenKeyEx(hRemoteKey, regPath, 0, KEY_SET_VALUE, &hRegKey) != ERROR_SUCCESS) {
			RegCloseKey(hRemoteKey);
			return -1;
		}
	}
	else {
		if (RegOpenKeyEx(HKEY_LOCAL_MACHINE, regPath, 0, KEY_SET_VALUE, &hRegKey) != ERROR_SUCCESS) {
			return -1;
		}
	}

	
	for (int i=0; i < keys.GetSize(); ++i) {	
		if (values[i] == "") {
			rc = RegDeleteValue(hRegKey, keys[i]);
			if (rc != ERROR_SUCCESS) {
				RegCloseKey(hRegKey);
				if (machine != "")
					RegCloseKey(hRemoteKey);
				return -1;
			}
		}
		else {
			
			strcpy(valueBuffer, values[i]);
			
			if (RegSetValueEx(hRegKey, keys[i], 0, dwType, (LPBYTE)valueBuffer, (DWORD)values[i].GetLength()) != ERROR_SUCCESS) {
				RegCloseKey(hRegKey);
				if (machine != "")
					RegCloseKey(hRemoteKey);
				return -1;
			}
		}
	}

	RegCloseKey(hRegKey);

	if (machine != "")
		RegCloseKey(hRemoteKey);

	return 0;
}
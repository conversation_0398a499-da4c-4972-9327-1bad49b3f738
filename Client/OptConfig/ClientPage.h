#if !defined(AFX_CLIENTPAGE_H__D0734DEF_8993_4A68_B7F6_9555CD3C61BA__INCLUDED_)
#define AFX_CLIENTPAGE_H__D0734DEF_8993_4A68_B7F6_9555CD3C61BA__INCLUDED_

#if _MSC_VER > 1000
#pragma once
#endif // _MSC_VER > 1000
// ClientPage.h : header file
//

/////////////////////////////////////////////////////////////////////////////
// CClientPage dialog

class CClientPage : public CPropertyPage
{
	DECLARE_DYNCREATE(CClientPage)

// Construction
public:
	DWORD m_Status;
	CClientPage();
	~CClientPage();

// Dialog Data
	//{{AFX_DATA(CClientPage)
	enum { IDD = IDD_CLIENT };
	CListCtrl	m_VersionListCtrl;
	BOOL	m_Debug;
	CString	m_Password;
	CString	m_Server;
	BOOL	m_ShowSession;
	CString	m_UserName;
	BOOL	m_AutoCheck;
	CString	m_PhysicalMemory;
	CString	m_VirtualMemory;
	CString	m_CopyBuffer;
	//}}AFX_DATA


// Overrides
	// ClassWizard generate virtual function overrides
	//{{AFX_VIRTUAL(CClientPage)
	public:
	virtual BOOL OnSetActive();
	virtual BOOL OnApply();
	virtual void OnOK();
	protected:
	virtual void DoDataExchange(CDataExchange* pDX);    // DDX/DDV support
	virtual BOOL OnNotify(WPARAM wParam, LPARAM lParam, LRESULT* pResult);
	//}}AFX_VIRTUAL

// Implementation
protected:
	// Generated message map functions
	//{{AFX_MSG(CClientPage)
	afx_msg void OnTestServer();
	virtual BOOL OnInitDialog();
	afx_msg void OnChangeServer();
	afx_msg void OnChange();
	afx_msg BOOL OnHelpInfo(HELPINFO* pHelpInfo);
	afx_msg void OnChangeCopyBuffer();
	afx_msg void OnAutoLoginChange();
	//}}AFX_MSG
	DECLARE_MESSAGE_MAP()

private:
	CString m_ClientHome;
	CString m_SaveAuto;
	BOOL SaveValues();
	void LoadProgramList();
	void LoadRegistryValues();
public:
	BOOL m_StartEngine;
};

//{{AFX_INSERT_LOCATION}}
// Microsoft Visual C++ will insert additional declarations immediately before the previous line.

#endif // !defined(AFX_CLIENTPAGE_H__D0734DEF_8993_4A68_B7F6_9555CD3C61BA__INCLUDED_)

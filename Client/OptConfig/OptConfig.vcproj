<?xml version="1.0" encoding="Windows-1252"?>
<VisualStudioProject
	ProjectType="Visual C++"
	Version="7.10"
	Name="OptConfig"
	ProjectGUID="{C96ED44D-EEE0-405E-A9BB-577D39747CAF}"
	RootNamespace="OptConfig"
	SccProjectName="&quot;$/Optimize/OPTIMIZEBASE4/Client/OptConfig&quot;, XYCAAAAA"
	SccAuxPath=""
	SccLocalPath="."
	SccProvider="MSSCCI:Microsoft Visual SourceSafe"
	Keyword="MFCProj">
	<Platforms>
		<Platform
			Name="Win32"/>
	</Platforms>
	<Configurations>
		<Configuration
			Name="Release|Win32"
			OutputDirectory="..\..\Obj.Release"
			IntermediateDirectory="..\..\Obj.Release"
			ConfigurationType="1"
			UseOfMFC="2"
			ATLMinimizesCRunTimeLibraryUsage="FALSE"
			CharacterSet="2">
			<Tool
				Name="VCCLCompilerTool"
				Optimization="2"
				InlineFunctionExpansion="1"
				AdditionalIncludeDirectories="c:\program files\wmi\include"
				PreprocessorDefinitions="NDEBUG;WIN32;_WINDOWS;_WIN32_DCOM"
				StringPooling="TRUE"
				RuntimeLibrary="2"
				EnableFunctionLevelLinking="TRUE"
				UsePrecompiledHeader="3"
				PrecompiledHeaderThrough="stdafx.h"
				PrecompiledHeaderFile=".\Release/OptConfig.pch"
				AssemblerListingLocation=".\Release/"
				ObjectFile=".\Release/"
				ProgramDataBaseFileName=".\Release/"
				WarningLevel="3"
				SuppressStartupBanner="TRUE"
				CompileAs="0"/>
			<Tool
				Name="VCCustomBuildTool"/>
			<Tool
				Name="VCLinkerTool"
				AdditionalDependencies="wsock32.lib Wbemuuid.lib mpr.lib $(outdir)\Libraries\SsaGraphSession.lib"
				OutputFile="$(OutDir)/bin//OptConfig.exe"
				LinkIncremental="1"
				SuppressStartupBanner="TRUE"
				AdditionalLibraryDirectories="c:\program files\wmi\lib"
				ProgramDatabaseFile=".\Release/OptConfig.pdb"
				SubSystem="2"
				TargetMachine="1"/>
			<Tool
				Name="VCMIDLTool"
				PreprocessorDefinitions="NDEBUG"
				MkTypLibCompatible="TRUE"
				SuppressStartupBanner="TRUE"
				TargetEnvironment="1"
				TypeLibraryName=".\Release/OptConfig.tlb"
				HeaderFileName=""/>
			<Tool
				Name="VCPostBuildEventTool"/>
			<Tool
				Name="VCPreBuildEventTool"/>
			<Tool
				Name="VCPreLinkEventTool"/>
			<Tool
				Name="VCResourceCompilerTool"
				PreprocessorDefinitions="NDEBUG"
				Culture="1033"/>
			<Tool
				Name="VCWebServiceProxyGeneratorTool"/>
			<Tool
				Name="VCXMLDataGeneratorTool"/>
			<Tool
				Name="VCWebDeploymentTool"/>
			<Tool
				Name="VCManagedWrapperGeneratorTool"/>
			<Tool
				Name="VCAuxiliaryManagedWrapperGeneratorTool"/>
		</Configuration>
		<Configuration
			Name="Debug|Win32"
			OutputDirectory="..\..\Obj.Debug"
			IntermediateDirectory=".\Debug"
			ConfigurationType="1"
			UseOfMFC="2"
			ATLMinimizesCRunTimeLibraryUsage="FALSE"
			CharacterSet="2">
			<Tool
				Name="VCCLCompilerTool"
				Optimization="0"
				AdditionalIncludeDirectories="c:\program files\wmi\include"
				PreprocessorDefinitions="_DEBUG;_WIN32_DCOM;WIN32;_WINDOWS"
				BasicRuntimeChecks="3"
				RuntimeLibrary="3"
				UsePrecompiledHeader="3"
				PrecompiledHeaderThrough="stdafx.h"
				PrecompiledHeaderFile=".\Debug/OptConfig.pch"
				AssemblerListingLocation=".\Debug/"
				ObjectFile=".\Debug/"
				ProgramDataBaseFileName=".\Debug/"
				WarningLevel="3"
				SuppressStartupBanner="TRUE"
				DebugInformationFormat="4"
				CompileAs="0"/>
			<Tool
				Name="VCCustomBuildTool"/>
			<Tool
				Name="VCLinkerTool"
				AdditionalDependencies="wsock32.lib Wbemuuid.lib mpr.lib $(outdir)\Libraries\SsaGraphSession.lib"
				OutputFile="$(OutDir)/bin/OptConfig.exe"
				LinkIncremental="1"
				SuppressStartupBanner="TRUE"
				AdditionalLibraryDirectories="c:\program files\wmi\lib"
				GenerateDebugInformation="TRUE"
				ProgramDatabaseFile=".\Debug/OptConfig.pdb"
				SubSystem="2"
				TargetMachine="1"/>
			<Tool
				Name="VCMIDLTool"
				PreprocessorDefinitions="_DEBUG"
				MkTypLibCompatible="TRUE"
				SuppressStartupBanner="TRUE"
				TargetEnvironment="1"
				TypeLibraryName=".\Debug/OptConfig.tlb"
				HeaderFileName=""/>
			<Tool
				Name="VCPostBuildEventTool"/>
			<Tool
				Name="VCPreBuildEventTool"/>
			<Tool
				Name="VCPreLinkEventTool"/>
			<Tool
				Name="VCResourceCompilerTool"
				PreprocessorDefinitions="_DEBUG"
				Culture="1033"/>
			<Tool
				Name="VCWebServiceProxyGeneratorTool"/>
			<Tool
				Name="VCXMLDataGeneratorTool"/>
			<Tool
				Name="VCWebDeploymentTool"/>
			<Tool
				Name="VCManagedWrapperGeneratorTool"/>
			<Tool
				Name="VCAuxiliaryManagedWrapperGeneratorTool"/>
		</Configuration>
	</Configurations>
	<References>
	</References>
	<Files>
		<Filter
			Name="Source Files"
			Filter="cpp;c;cxx;rc;def;r;odl;idl;hpj;bat">
			<File
				RelativePath="ClientPage.cpp">
				<FileConfiguration
					Name="Release|Win32">
					<Tool
						Name="VCCLCompilerTool"
						Optimization="2"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug|Win32">
					<Tool
						Name="VCCLCompilerTool"
						Optimization="0"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
						BasicRuntimeChecks="3"/>
				</FileConfiguration>
			</File>
			<File
				RelativePath="GenericPropertySheet.cpp">
				<FileConfiguration
					Name="Release|Win32">
					<Tool
						Name="VCCLCompilerTool"
						Optimization="2"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug|Win32">
					<Tool
						Name="VCCLCompilerTool"
						Optimization="0"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
						BasicRuntimeChecks="3"/>
				</FileConfiguration>
			</File>
			<File
				RelativePath="OptConfig.cpp">
				<FileConfiguration
					Name="Release|Win32">
					<Tool
						Name="VCCLCompilerTool"
						Optimization="2"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug|Win32">
					<Tool
						Name="VCCLCompilerTool"
						Optimization="0"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
						BasicRuntimeChecks="3"/>
				</FileConfiguration>
			</File>
			<File
				RelativePath="OptConfigDlg.cpp">
				<FileConfiguration
					Name="Release|Win32">
					<Tool
						Name="VCCLCompilerTool"
						Optimization="2"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug|Win32">
					<Tool
						Name="VCCLCompilerTool"
						Optimization="0"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
						BasicRuntimeChecks="3"/>
				</FileConfiguration>
			</File>
			<File
				RelativePath="Password.cpp">
				<FileConfiguration
					Name="Release|Win32">
					<Tool
						Name="VCCLCompilerTool"
						Optimization="2"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug|Win32">
					<Tool
						Name="VCCLCompilerTool"
						Optimization="0"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
						BasicRuntimeChecks="3"/>
				</FileConfiguration>
			</File>
			<File
				RelativePath="Progress.cpp">
				<FileConfiguration
					Name="Release|Win32">
					<Tool
						Name="VCCLCompilerTool"
						Optimization="2"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug|Win32">
					<Tool
						Name="VCCLCompilerTool"
						Optimization="0"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
						BasicRuntimeChecks="3"/>
				</FileConfiguration>
			</File>
			<File
				RelativePath="RegistryFunctions.cpp">
				<FileConfiguration
					Name="Release|Win32">
					<Tool
						Name="VCCLCompilerTool"
						Optimization="2"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug|Win32">
					<Tool
						Name="VCCLCompilerTool"
						Optimization="0"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
						BasicRuntimeChecks="3"/>
				</FileConfiguration>
			</File>
			<File
				RelativePath="ServerPage.cpp">
				<FileConfiguration
					Name="Release|Win32">
					<Tool
						Name="VCCLCompilerTool"
						Optimization="2"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug|Win32">
					<Tool
						Name="VCCLCompilerTool"
						Optimization="0"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
						BasicRuntimeChecks="3"/>
				</FileConfiguration>
			</File>
			<File
				RelativePath="StatusPage.cpp">
				<FileConfiguration
					Name="Release|Win32">
					<Tool
						Name="VCCLCompilerTool"
						Optimization="2"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug|Win32">
					<Tool
						Name="VCCLCompilerTool"
						Optimization="0"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
						BasicRuntimeChecks="3"/>
				</FileConfiguration>
			</File>
			<File
				RelativePath="StdAfx.cpp">
				<FileConfiguration
					Name="Release|Win32">
					<Tool
						Name="VCCLCompilerTool"
						Optimization="2"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
						UsePrecompiledHeader="1"/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug|Win32">
					<Tool
						Name="VCCLCompilerTool"
						Optimization="0"
						AdditionalIncludeDirectories=""
						PreprocessorDefinitions=""
						BasicRuntimeChecks="3"
						UsePrecompiledHeader="1"/>
				</FileConfiguration>
			</File>
			<File
				RelativePath=".\XBrowseForFolder.cpp">
			</File>
		</Filter>
		<Filter
			Name="Header Files"
			Filter="h;hpp;hxx;hm;inl">
			<File
				RelativePath="ClientPage.h">
			</File>
			<File
				RelativePath="GenericPropertySheet.h">
			</File>
			<File
				RelativePath="OptConfig.h">
			</File>
			<File
				RelativePath="OptConfigDlg.h">
			</File>
			<File
				RelativePath="Password.h">
			</File>
			<File
				RelativePath="Progress.h">
			</File>
			<File
				RelativePath="RegistryFunctions.h">
			</File>
			<File
				RelativePath="Resource.h">
			</File>
			<File
				RelativePath="ServerPage.h">
			</File>
			<File
				RelativePath="..\SsaGraphSession\SsaGraphSession\SSAEncrypt.h">
			</File>
			<File
				RelativePath="StatusPage.h">
			</File>
			<File
				RelativePath="StdAfx.h">
			</File>
			<File
				RelativePath=".\XBrowseForFolder.h">
			</File>
		</Filter>
		<Filter
			Name="Resource Files"
			Filter="ico;cur;bmp;dlg;rc2;rct;bin;rgs;gif;jpg;jpeg;jpe">
			<File
				RelativePath="res\OptConfig.ico">
			</File>
			<File
				RelativePath="OptConfig.rc">
			</File>
			<File
				RelativePath="res\OptConfig.rc2">
			</File>
		</Filter>
	</Files>
	<Globals>
		<Global
			Name="RESOURCE_FILE"
			Value="OptConfig.rc"/>
	</Globals>
</VisualStudioProject>

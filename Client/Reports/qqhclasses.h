#ifndef QQHCLASSES_H
#define QQHCLASSES_H
#include <afxtempl.h>
#include "ExeCStringArray.h"

/////////////////////////////////////////////////////////////
//  Search strings and other global data
/////////////////////////////////////////////////////////////

#define AisleBegSearch "<SIO>SLOTAisle"
#define AisleEndSearch "<EIO>SLOTAisle"
#define CoordBegSearch "<SIO>SLOTCoordinate"
#define CoordEndSearch "<EIO>SLOTCoordinate"
#define PathBegSearch "<SIO>SLOTPickPath"
#define PathEndSearch "<EIO>SLOTPickPath"
#define SideBegSearch "<SIO>SLOTSide"
#define SideEndSearch "<EIO>SLOTSide"
#define BayBegSearch "<SIO>SLOTBay"
#define BayEndSearch "<EIO>SLOTBay"
#define LevelBegSearch "<SIO>SLOTLevel"
#define LevelEndSearch "<EIO>SLOTLevel"
#define LocationBegSearch "<SIO>SLOTLocation"
#define LocationEndSearch "<EIO>SLOTLocation"
#define FacilityBegSearch "<SIO>SLOTFacility"
#define FacilityEndSearch "<EIO>SLOTFacility"
#define SectionBegSearch "<SIO>SLOTSection"
#define SectionEndSearch "<EIO>SLOTSection"
#define DimenBegSearch "<SIO>SLOTDimension"
#define DimenEndSearch "<EIO>SLOTDimension"
#define RackTypeBegSearch "<SIO>SLOTRackType"
#define RackTypeEndSearch "<EIO>SLOTRackType"
#define RackTypeGroupBegSearch "<SIO>SLOTRackTypeGroup"
#define RackTypeGroupEndSearch "<EIO>SLOTRackTypeGroup"
#define RackTypeUsageBegSearch "<SIO>SLOTRackTypeUsage"
#define RackTypeUsageEndSearch "<EIO>SLOTRackTypeUsage"
#define GroupBayBegSearch "<SIO>SLOTGroupBay"
#define GroupBayEndSearch "<EIO>SLOTGroupBay"
#define REGBegSearch "<SAI>REG|"
#define UDFBegSearch "<SAI>UDF|"
#define BegObjSearch "<SIO>"
#define EndObjSearch "<EIO>"
#define ContainerBegSearch "<SIO>SLOTProductContainer"
#define ContainerEndSearch "<EIO>SLOTProductContainer"
#define PackBegSearch "<SIO>SLOTProductPack"
#define PackEndSearch "<EIO>SLOTProductPack"
#define ContainerListBeg "<SLO>SLOTProductContainer"
#define ContainerListEnd "<ELO>SLOTProductContainer"
#define SlotGroupBegSearch "<SIO>SLOTSlottingGroup"
#define SlotGroupEndSearch "<EIO>SLOTSlottingGroup"
#define QueryBegSearch "<SIO>SLOTQuery"
#define QueryEndSearch "<EIO>SLOTQuery"
#define QueryAttrBegSearch "<SIO>SLOTQueryAttr"
#define QueryAttrEndSearch "<EIO>SLOTQueryAttr"
#define RackTypeLaborBegSearch "<SIO>SLOTRackTypeLabor"
#define RackTypeLaborEndSearch "<EIO>SLOTRackTypeLabor"
#define HotSpotBegSearch "<SIO>SLOTHotSpot"
#define HotSpotEndSearch "<EIO>SLOTHotSpot"
#define LocationProfileEndSearch "<EIO>SLOTLocationProfile"
#define LocationProfileBegSearch "<SIO>SLOTLocationProfile"
#define AisleProfileBegSearch "<SIO>SLOTAisleProfile"
#define AisleProfileEndSearch "<EIO>SLOTAisleProfile"
#define SideProfileBegSearch "<SIO>SLOTSideProfile"
#define SideProfileEndSearch "<EIO>SLOTSideProfile"
#define BayProfileBegSearch "<SIO>SLOTBayProfile"
#define BayProfileEndSearch "<EIO>SLOTBayProfile"
#define LevelProfileBegSearch "<SIO>SLOTLevelProfile"
#define LevelProfileEndSearch "<EIO>SLOTLevelProfile"
#define LevelLaborProfileBegSearch "<SIO>SLOTLevelLaborProfile"
#define LevelLaborProfileEndSearch "<EIO>SLOTLevelLaborProfile"
#define FacingInfoBegSearch "<SIO>SLOTFacingInfo"
#define FacingInfoEndSearch "<EIO>SLOTFacingInfo"
#define BayRuleBegSearch "<SIO>SLOTBayRule"
#define BayRuleEndSearch "<EIO>SLOTBayRule"

class qqhSLOTCoordinate;
class qqhSLOTObject;
class qqhSLOTHolder;
class qqhSLOTFacility;
class qqhSLOTSection;
class qqhSLOTAisle;
class qqhSLOTSide;
class qqhSLOTBay;
class qqhSLOTLevel;
class qqhSLOTLocation;
class qqhSLOTQueryAttr;
class qqhSLOTQuery;
class qqhSLOTPickPath;
class qqhSLOTProductContainer;
class qqhSLOTProductPack;
class qqhSLOTRackTypeUsage;
class qqhSLOTRackTypeGroup;
class qqhSLOTRackType;
class qqhSLOTSlottingGroup;
class qqhSLOTRackTypeLabor;
class qqhSLOTAisleProfile;
class qqhSLOTSideProfile;
class qqhSLOTBayProfile;
class qqhSLOTLevelProfile;
class qqhSLOTLocationProfile;
class qqhSLOTLevelLaborProfile;
class qqhSLOTBayRule;
class qqhSLOTFacingInfo;

/////////////////////////////////////////
// Coordinate class
/////////////////////////////////////////
class qqhSLOTCoordinate
{
protected:
	int x;
	int y;
	int z;

public:
	///////////////////////////////////////////
	//Gets and Sets
	///////////////////////////////////////////
	int getX() { return x; }
	int getY() { return y; }
	int getZ() { return z; }

	void setX(int pX) { x = pX; }
	void setY(int pY) { y = pY; }
	void setZ(int pZ) { z = pZ; }

	///////////////////////////////////////////
	//Constructors, Destructors, and operators
	///////////////////////////////////////////
	qqhSLOTCoordinate(int xCoord, int yCoord, int zCoord);
	qqhSLOTCoordinate();
	virtual ~qqhSLOTCoordinate(){}
	qqhSLOTCoordinate(const qqhSLOTCoordinate & other);
	qqhSLOTCoordinate& operator=(const qqhSLOTCoordinate &other);

	///////////////////////////////////////////
	//Other methods
	///////////////////////////////////////////
	void BuildFromStream(CExeStringArray &bufArray);
	void StreamAttributes(CExeStringArray & attributBuf);
};


/////////////////////////////////////////
// Base Object class
/////////////////////////////////////////
class qqhSLOTObject
{
protected:

	int					DBID;
	qqhSLOTCoordinate	coord;
	CString				type;
	CString				description;
	CExeStringArray		UDFList;

public:
	///////////////////////////////////////////
	//Gets and Sets
	///////////////////////////////////////////
	int					getDBID() { return DBID; }
	qqhSLOTCoordinate&	getCoord() { return coord; }
	CString&			getType() { return type; }
	CString&			getDescription() { return description; }
	CExeStringArray&	getUDFList() { return UDFList; }

	void	setDBID(int pDBID) { DBID = pDBID; }
	void	setCoord(const qqhSLOTCoordinate & pCoord) { coord = pCoord; }
	void	setType(const CString & pType) { type = pType; }
	void	setDescription(const CString& pDescription) { description = pDescription; }
	void	setUDFList(const CExeStringArray & pUDFList) { UDFList.Copy(pUDFList); }

	///////////////////////////////////////////
	//Constructors, Destructors, and operators
	///////////////////////////////////////////
	qqhSLOTObject();
	qqhSLOTObject(const qqhSLOTObject & other);
	virtual ~qqhSLOTObject(){UDFList.RemoveAll();}
	///////////////////////////////////////////
	//Other Methods
	///////////////////////////////////////////
	void BuildFromStream(CExeStringArray &bufArray) { bufArray.RemoveAll(); return;}
	void StreamAttributes(CExeStringArray & attributBuf) {attributBuf.RemoveAll(); return;}
};

class qqhSLOTHotSpot :  public qqhSLOTObject
{
protected:
	int hotSpotType;
	char acadHandle[20];

public:
	///////////////////////////////////////////
	//Gets and Sets
	///////////////////////////////////////////
	int				getHotSpotType() { return hotSpotType; }
	char *			getAcadHandle() { return acadHandle; }

	void			setHotSpotType(int pHotSpotType) { hotSpotType = pHotSpotType; }
	void			setAcadHandle(const char * pAcadHandle) { strcpy(acadHandle,pAcadHandle); }

	///////////////////////////////////////////
	//Constructors, Destructors, and operators
	///////////////////////////////////////////
	qqhSLOTHotSpot();
	qqhSLOTHotSpot(const qqhSLOTHotSpot &other);
	qqhSLOTHotSpot& operator=(const qqhSLOTHotSpot &other);
	virtual ~qqhSLOTHotSpot() {}
	///////////////////////////////////////////
	//Other Methods
	///////////////////////////////////////////
	void BuildFromStream(CExeStringArray &bufArray);
	void StreamAttributes(CExeStringArray & attributBuf);
};

/////////////////////////////////////////
// SlottingGroup class
/////////////////////////////////////////
class qqhSLOTSlottingGroup : public qqhSLOTObject
{
protected:
	int priority;
	float percentOpenLocs;
	int locLocs; 
	int locProdGroup; 
	int suggestedSectionID;
	CString optimizeAttribute;
	int optimizeMethod;

public:
	///////////////////////////////////////////
	//Gets and Sets
	///////////////////////////////////////////
	int		getPriority() { return priority; }
	float	getPercentOpenLocs() { return percentOpenLocs; }
	int		getLocLocs()	{ return locLocs; }
	int		getLocProdGroup() { return locProdGroup; }
	int		getSuggestedSectionID() { return suggestedSectionID; }
	CString &	getOptimizeAttribute() { return optimizeAttribute; }
	int		getOptimizeMethod() { return optimizeMethod; }

	void	setPriority(int pPriority) { priority = pPriority; }
	void	setPercentOpenLocs(float pPercentOpenLocs) { percentOpenLocs = pPercentOpenLocs; }
	void	setLocLocs(int pLocLocs)	{ locLocs = pLocLocs; }
	void	setLocProdGroup(int pLocProdGroup) { locProdGroup = pLocProdGroup; }
	void	setSuggestedSectionID(int pSuggestedSectionID) { suggestedSectionID = pSuggestedSectionID; }
	void	setOptimizeAttribute(const CString & pOptimizeAttribute) { optimizeAttribute = pOptimizeAttribute; }
	void	setOptimizeMethod(int pOptimizeMethod) { optimizeMethod = pOptimizeMethod; }

	///////////////////////////////////////////
	//Constructors, Destructors, and operators
	///////////////////////////////////////////
	qqhSLOTSlottingGroup();
	virtual ~qqhSLOTSlottingGroup() {}
	qqhSLOTSlottingGroup(const qqhSLOTSlottingGroup &other);
	qqhSLOTSlottingGroup & operator=(const qqhSLOTSlottingGroup &other);
	///////////////////////////////////////////
	//Other Methods
	///////////////////////////////////////////
	void BuildFromStream(CExeStringArray &bufArray);
	void StreamAttributes(CExeStringArray & attributBuf);
};

/////////////////////////////////////////
// RackTypeGroupLabor class
/////////////////////////////////////////
class qqhSLOTRackTypeLabor : public qqhSLOTObject
{
protected:
	float	cube;
	float	variableFactor;
	float	fixedFactor;
	int		relativeLevel;

public:
	///////////////////////////////////////////
	//Gets and Sets
	///////////////////////////////////////////
	float	getCube() { return cube; }
	float	getVariableFactor() { return variableFactor; }
	float	getFixedFactor() { return fixedFactor; }
	int		getRelativeLevel() { return relativeLevel; }

	void	setCube(float pCube) { cube = pCube; }
	void	setVariableFactor(float pVariableFactor) { variableFactor = pVariableFactor; }
	void	setFixedFactor(float pFixedFactor) { fixedFactor = pFixedFactor; }
	void	setRelativeLevel(int pRelativeLevel) { relativeLevel = pRelativeLevel; }

	///////////////////////////////////////////
	//Constructors, Destructors, and operators
	///////////////////////////////////////////
	qqhSLOTRackTypeLabor();
	qqhSLOTRackTypeLabor(const qqhSLOTRackTypeLabor & other);
	virtual ~qqhSLOTRackTypeLabor() {}
	qqhSLOTRackTypeLabor & operator= (const qqhSLOTRackTypeLabor & other);

	///////////////////////////////////////////
	//Other Methods
	///////////////////////////////////////////
	void BuildFromStream(CExeStringArray &bufArray);
	void StreamAttributes(CExeStringArray & attributBuf);
};

/////////////////////////////////////////
// RackTypeGroup class
/////////////////////////////////////////

class qqhSLOTRackTypeGroup : public qqhSLOTObject
{
protected:

	int calculationMethod;
	float reserveUsage;

public:

	///////////////////////////////////////////
	//Gets and Sets
	///////////////////////////////////////////
	int		getCalculationMethod() { return calculationMethod; }
	float	getReserveUsage() { return reserveUsage; }

	void	setCalculationMethod(int pCalculationMethod) { calculationMethod = pCalculationMethod; }
	void	setReserveUsage(float pReserveUsage) { reserveUsage = pReserveUsage; }

	///////////////////////////////////////////
	//Constructors, Destructors, and operators
	///////////////////////////////////////////
	qqhSLOTRackTypeGroup();
	qqhSLOTRackTypeGroup(const qqhSLOTRackTypeGroup & other);
	virtual ~qqhSLOTRackTypeGroup() {}
	qqhSLOTRackTypeGroup& operator= (const qqhSLOTRackTypeGroup & other);

	///////////////////////////////////////////
	//Other Methods
	///////////////////////////////////////////
	void BuildFromStream(CExeStringArray &bufArray);
	void StreamAttributes(CExeStringArray & attributBuf);
};

/////////////////////////////////////////
// RackType class
/////////////////////////////////////////
class qqhSLOTRackType : public qqhSLOTObject
{
protected:
	float cost;
	float cube;
	int region;
	int units;
	qqhSLOTRackTypeGroup rackTypeGroup;

public:
	///////////////////////////////////////////
	//Gets and Sets
	///////////////////////////////////////////
	float	getCost() { return cost; }
	float	getCube() { return cube; }
	int		getRegion() { return region; }
	int		getUnits() { return units; }
	qqhSLOTRackTypeGroup & getRackTypeGroup() { return rackTypeGroup; }

	void	setCost(float pCost) { cost = pCost; }
	void	setCube(float pCube) { cube = pCube; }
	void	setRegion(int pRegion) { region = pRegion; }
	void	setRackTypeGroup(const qqhSLOTRackTypeGroup& pRackTypeGroup) { rackTypeGroup = pRackTypeGroup; }

	///////////////////////////////////////////
	//Constructors, Destructors, and operators
	///////////////////////////////////////////
	qqhSLOTRackType();
	qqhSLOTRackType(const qqhSLOTRackType & other);
	virtual ~qqhSLOTRackType() {}
	qqhSLOTRackType& operator= (const qqhSLOTRackType & other);

	///////////////////////////////////////////
	//Other Methods
	///////////////////////////////////////////
	void BuildFromStream(CExeStringArray &bufArray);
	void StreamAttributes(CExeStringArray & attributBuf);
};

/////////////////////////////////////////
// RackTypeUsage class
/////////////////////////////////////////
class qqhSLOTRackTypeUsage : public qqhSLOTObject
{
protected:
	float extendedBOH;
	float extendedCube;
	int facings;
	qqhSLOTRackType rackType;

public:
	///////////////////////////////////////////
	//Gets and Sets
	///////////////////////////////////////////
	float	getExtendedBOH() { return extendedBOH; }
	float	getExtendedCube() { return extendedCube; }
	int		getFacings() { return facings; }
	qqhSLOTRackType & getRackType() { return rackType; }

	void	setExtendedBOH(float pExtendedBOH) { extendedBOH = pExtendedBOH; }
	void	setExtendedCube(float pExtendedCube) { extendedCube = pExtendedCube; }
	void	setFacings(int pFacings) { facings = pFacings; }
	void	setRackType(const qqhSLOTRackType & pRackType) { rackType = pRackType; }

	///////////////////////////////////////////
	//Constructors, Destructors, and operators
	///////////////////////////////////////////
	qqhSLOTRackTypeUsage();
	qqhSLOTRackTypeUsage(const qqhSLOTRackTypeUsage & other);
	virtual ~qqhSLOTRackTypeUsage(){}
	qqhSLOTRackTypeUsage& operator= (const qqhSLOTRackTypeUsage & other);
	///////////////////////////////////////////
	//Other Methods
	///////////////////////////////////////////
	void BuildFromStream(CExeStringArray &bufArray);
	void StreamAttributes(CExeStringArray & attributBuf);
};

/////////////////////////////////////////
// Pick Path Object class
/////////////////////////////////////////
class qqhSLOTPickPath : public qqhSLOTObject
{
protected:
	float pathLength;
	char acadHandle[20];
	char conAcadHandle[20];

public:
	///////////////////////////////////////////
	//Gets and Sets
	///////////////////////////////////////////
	float			getPathLength() { return pathLength; }
	char *			getAcadHandle() { return acadHandle; }
	char *			getConAcadHandle() { return conAcadHandle; }

	void			setAcadHandle(const char * pAcadHandle) { strcpy(acadHandle,pAcadHandle); }
	void			setPathLength(float pPathLength) { pathLength = pPathLength; }
	void			setConAcadHandle(const char * pConAcadHandle) { strcpy(conAcadHandle,pConAcadHandle); }

	///////////////////////////////////////////
	//Constructors, Destructors, and operators
	///////////////////////////////////////////
	qqhSLOTPickPath();
	virtual ~qqhSLOTPickPath() {}
	qqhSLOTPickPath(const qqhSLOTPickPath & other);
	qqhSLOTPickPath& operator=(const qqhSLOTPickPath &other);
	///////////////////////////////////////////
	//Other Methods
	///////////////////////////////////////////
	void BuildFromStream(CExeStringArray &bufArray);
	void StreamAttributes(CExeStringArray & attributBuf);
};

/////////////////////////////////////////
// Query classes
/////////////////////////////////////////
class qqhSLOTQueryAttr : public qqhSLOTObject
{
protected :
	CString attrName;
	CString attrValue;
	CString conjunction;
	CString queryOperator;

public:
	///////////////////////////////////////////
	//Gets and Sets
	///////////////////////////////////////////
	CString &	getAttrName() { return attrName; }
	CString &	getAttrValue() { return attrValue; }
	CString &	getConjunction() { return conjunction; }
	CString &	getQueryOperator() { return queryOperator; }

	void	setAttrName(const CString & pAttrName) { attrName = pAttrName; }
	void	setAttrValue(const CString & pAttrValue) { attrValue = pAttrValue; }
	void	setConjunction(const CString & pConjunction) { conjunction = pConjunction; }
	void	setQueryOperator(const CString & pQueryOperator) { queryOperator = pQueryOperator; }

	///////////////////////////////////////////
	//Constructors, Destructors, and operators
	///////////////////////////////////////////
	qqhSLOTQueryAttr();
	virtual ~qqhSLOTQueryAttr(){}
	qqhSLOTQueryAttr(const qqhSLOTQueryAttr & other);
	qqhSLOTQueryAttr& operator=(const qqhSLOTQueryAttr &other);
	///////////////////////////////////////////
	//Other Methods
	///////////////////////////////////////////
	void BuildFromStream(CExeStringArray &bufArray);
	void StreamAttributes(CExeStringArray & attributBuf);
};

class qqhSLOTQuery : public qqhSLOTObject
{
protected:
	CArray <qqhSLOTQueryAttr, qqhSLOTQueryAttr&> attrList;
	CString objName;

public:
	///////////////////////////////////////////
	//Gets and Sets
	///////////////////////////////////////////
	CArray <qqhSLOTQueryAttr, qqhSLOTQueryAttr&>&	getAttrList() { return attrList; }
	CString &										getObjName() { return objName; }

	void setAttrList(const CArray <qqhSLOTQueryAttr, qqhSLOTQueryAttr&> & pAttrList) { attrList.Copy(pAttrList); }
	void setObjName(CString & pObjName) { objName = pObjName; }

	///////////////////////////////////////////
	//Constructors, Destructors, and operators
	///////////////////////////////////////////
	qqhSLOTQuery();
	virtual ~qqhSLOTQuery() {attrList.RemoveAll();}
	qqhSLOTQuery(qqhSLOTQuery & other);
	qqhSLOTQuery& operator=(qqhSLOTQuery & other);
	///////////////////////////////////////////
	//Other Methods
	///////////////////////////////////////////
	void AddQueryAttr(CString pAttribute,CString pValue, CString pConjunction, CString pQueryOperator);
	void BuildFromStream(CExeStringArray &bufArray);
	void StreamAttributes(CExeStringArray & attributBuf);
};

/////////////////////////////////////////
// Product Container class
/////////////////////////////////////////

class qqhSLOTProductContainer : public qqhSLOTObject
{
protected :
	float width;
	float depth;
	float height;
	int isDepthOverride;
	int isWidthOverride;
	int isHeightOverride;
	int storageTI;
	int storageHI;

public :
	///////////////////////////////////////////
	//Gets and Sets
	///////////////////////////////////////////
	float getWidth()  { return width; }
	float getDepth()  { return depth; }
	float getHeight() { return height; }
	int   getIsDepthOverride()  { return isDepthOverride; }
	int   getIsWidthOverride()  { return isWidthOverride; }
	int   getIsHeightOverride() { return isHeightOverride; }
	int   getStorageTI()  { return storageTI; }
	int   getStorageHI()  { return storageHI; }

	void setWidth(float pWidth)   { width = pWidth; }
	void setDepth(float pDepth)   { depth = pDepth; }
	void setHeight(float pHeight) { height = pHeight; }
	void setIsDepthOverride(int pIsDepthOverride)   { isDepthOverride = pIsDepthOverride; }
	void setIsWidthOverride(int pIsWidthOverride)   { isWidthOverride = pIsWidthOverride; }
	void setIsHeightOverride(int pIsHeightOverride) { isHeightOverride = pIsHeightOverride; }
	void setStorageTI(int pStorageTI) { storageTI = pStorageTI; }
	void setStorageHI(int pStorageHI) { storageHI = pStorageHI; }
	///////////////////////////////////////////
	//Constructors, Destructors, and operators
	///////////////////////////////////////////
	qqhSLOTProductContainer();
	virtual ~qqhSLOTProductContainer() {}
	qqhSLOTProductContainer(const qqhSLOTProductContainer & other);
	qqhSLOTProductContainer& operator=(const qqhSLOTProductContainer & other);
	///////////////////////////////////////////
	//Other Methods
	///////////////////////////////////////////
	void BuildFromStream(CExeStringArray &bufArray);
	void StreamAttributes(CExeStringArray & attributBuf);
};

/////////////////////////////////////////
// Product Pack class
/////////////////////////////////////////
class qqhSLOTProductPack  : public qqhSLOTObject
{
protected :
	float cube;
	float width;
	float depth;
	float height;
	float movement;
	float weight;
	int totalSelectionQty;
	int numberInPallet;
	CString isPickBelt;
	CString isHazard;
	int unitOfIssue;
	int balanceOnHand;
	int optimizeBy;
	int assignmentLocked;
	float rotatedWidth;
	float rotatedDepth;
	float rotatedHeight;
	CString rotateXAxis;
	CString rotateYAxis;
	CString rotateZAxis;
	int maxStackNumber;


	CArray <qqhSLOTProductContainer, qqhSLOTProductContainer&> containerList;

public:

	///////////////////////////////////////////
	//Gets and Sets
	///////////////////////////////////////////
	float     getCube()              { return cube; }
	float     getWidth()             { return width; }
	float     getDepth()             { return depth; }
	float     getHeight()            { return height; }
	float     getMovement()          { return movement; }
	float     getWeight()            { return weight; }
	int       getTotalSelectionQty() { return totalSelectionQty; }
	int       getNumberInPallet()    { return numberInPallet; }
	CString & getIsPickBelt()        { return isPickBelt; }
	CString & getIsHazard()          { return isHazard; }
	int       getUnitOfIssue()       { return unitOfIssue; }
	int       getBalanceOnHand()     { return balanceOnHand; }
	int       getOptimizeBy()        { return optimizeBy; }
	int       getAssignmentLocked()  { return assignmentLocked; }
	float     getRotatedWidth()      { return rotatedWidth; }
	float     getRotatedDepth()      { return rotatedDepth; }
	float     getRotatedHeight()     { return rotatedHeight; }
	CString & getRotateXAxis()       { return rotateXAxis; }
	CString & getRotateYAxis()       { return rotateYAxis; }
	CString & getRotateZAxis()       { return rotateZAxis; }
	int       getMaxStackNumber()    { return maxStackNumber; }
 	CArray	<qqhSLOTProductContainer, qqhSLOTProductContainer&> & getContainerList() { return containerList; }

	void setCube(float pCube)                         { cube = pCube; }
	void setWidth(float pWidth)                       { width = pWidth; }
	void setDepth(float pDepth)                       { depth = pDepth; }
	void setHeight(float pHeight)                     { height = pHeight; }
	void setMovement(float pMovement)                 { movement = pMovement; }
	void setWeight(float pWeight)                     { weight = pWeight; }
	void setTotatSelectionQty(int pTotalSelectionQty) { totalSelectionQty = pTotalSelectionQty; }
	void setNumberInPallet(int pNumberInPallet)       { numberInPallet = pNumberInPallet; }
	void setIsPickBelt(CString & pIsPickBelt)         { isPickBelt = pIsPickBelt; }
	void setIsHazard(CString & pIsHazard)             { isHazard = pIsHazard; }
	void setUnitOfIssue(int pUnitOfIssue)             { unitOfIssue = pUnitOfIssue; }
	void setBalanceOnHand(int pBalanceOnHand)         { balanceOnHand = pBalanceOnHand; }
	void setOptimizeBy(int pOptimizeBy)               { optimizeBy = pOptimizeBy; }
	void setAssignmentLocked(int pAssignmentLocked)   { assignmentLocked = pAssignmentLocked; }
	void setRotatedWidth(float pRotatedWidth)         { rotatedWidth = pRotatedWidth; }
	void setRotatedDepth(float pRotatedDepth)         { rotatedDepth = pRotatedDepth; }
	void setRotatedHeight(float pRotatedHeight)       { rotatedHeight = pRotatedHeight; }
	void setRotateXAxis(CString & pRotateXAxis)       { rotateXAxis = pRotateXAxis; }
	void setRotateYAxis(CString & pRotateYAxis)       { rotateYAxis = pRotateYAxis; }
	void setRotateZAxis(CString & pRotateZAxis)       { rotateZAxis = pRotateZAxis; }
	void setMaxStackNumber(int pMaxStackNumber)       { maxStackNumber = pMaxStackNumber; }
	void setContainerList(const CArray <qqhSLOTProductContainer, qqhSLOTProductContainer&> & pContainerList) { containerList.Copy(pContainerList); }


	///////////////////////////////////////////
	//Constructors, Destructors, and operators
	///////////////////////////////////////////
	qqhSLOTProductPack();
	virtual ~qqhSLOTProductPack() {containerList.RemoveAll();}
	qqhSLOTProductPack(qqhSLOTProductPack &other);
	qqhSLOTProductPack & operator=(qqhSLOTProductPack &other);
	///////////////////////////////////////////
	//Other Methods
	///////////////////////////////////////////
	void BuildFromStream(CExeStringArray &bufArray);
	void StreamAttributes(CExeStringArray & attributBuf);
};


/////////////////////////////////////////
// SLOTLevelLaborProfile class
/////////////////////////////////////////
class qqhSLOTLevelLaborProfile : public qqhSLOTObject
{
protected:

	float	cube;
	float	fixedFactor;
	float	variableFactor;
public:
	float	getCube(void) { return cube; }
	float	getFixedFactor(void) { return fixedFactor; }
	float	getVariableFactor(void) { return variableFactor; }

	void	setCube(float pCube) { cube = pCube; }
	void	setFixedFactor(float pFixedFactor) { fixedFactor = pFixedFactor; }
	void	setvariableFactor(float pVariableFactor) { variableFactor = pVariableFactor; }

	///////////////////////////////////////////
	//Constructors and Destructors 
	// and operators
	///////////////////////////////////////////
	qqhSLOTLevelLaborProfile();
	qqhSLOTLevelLaborProfile(const qqhSLOTLevelLaborProfile & other);
	virtual ~qqhSLOTLevelLaborProfile() {}
	qqhSLOTLevelLaborProfile& operator=(const qqhSLOTLevelLaborProfile &other);

	///////////////////////////////////////////
	//other methods
	///////////////////////////////////////////
	void BuildFromStream(CExeStringArray &bufArray);
	void StreamAttributes(CExeStringArray &attributBuf);
};

/////////////////////////////////////////
// SLOTLocationProfile class
/////////////////////////////////////////
class qqhSLOTLocationProfile : public qqhSLOTObject
{
protected:

	float	width;
	float	depth;
	float	height;
	float	locationSpace;
	float	maxWeight;
	int		isSelect;
	int		handlingMethod;
	 
public:
	///////////////////////////////////////////
	//Gets and Sets
	///////////////////////////////////////////
	float	getWidth(void) { return width; }
	float	getDepth(void) { return depth; }
	float	getHeight(void) { return height; }
	float	getLocationSpace(void) { return locationSpace; }
	float	getMaxWeight(void) { return maxWeight; }
	int		getHandlingMethod(void) { return handlingMethod; }
	int		getIsSelect(void) { return isSelect; }

	void	setWidth(float pWidth) { width = pWidth; }
	void	setDepth(float pDepth) { depth = pDepth; }
	void	setHeight(float pHeight) { height = pHeight; }
	void	setLocationSpace(float pLocationSpace) { locationSpace = pLocationSpace; }
	void	setMaxWeight(float pMaxWeight) { maxWeight = pMaxWeight; }
	void	setHandlingMethod(int pHandlingMethod) { handlingMethod = pHandlingMethod; }
	void	setIsSelect(int pIsSelect) { isSelect = pIsSelect; }

	///////////////////////////////////////////
	//Constructors, Destructors, and operators
	///////////////////////////////////////////
	qqhSLOTLocationProfile();
	qqhSLOTLocationProfile(const qqhSLOTLocationProfile & other);
	virtual ~qqhSLOTLocationProfile() {}
	qqhSLOTLocationProfile& operator=(const qqhSLOTLocationProfile &other);

	///////////////////////////////////////////
	//Other methods
	///////////////////////////////////////////
	void BuildFromStream(CExeStringArray &bufArray);
	void StreamAttributes(CExeStringArray &attributBuf);
};

/////////////////////////////////////////
// SLOTFacingInfo class
/////////////////////////////////////////
class qqhSLOTFacingInfo : public qqhSLOTObject
{
protected:
	float	extendedBOH;
	float	extendedCube;
	int		facingCount;

public:
	///////////////////////////////////////////
	//Gets and Sets
	///////////////////////////////////////////
	int		getFacingCount(void) { return facingCount;}
	float	getExtendedCube(void) { return extendedBOH;}
	float	getExtendedBOH(void) { return extendedCube;}

	void	setExtendedBOH(float pExtendedBOH) { extendedBOH = pExtendedBOH; }
	void	setExtendedCube(float pExtendedCube) { extendedCube = pExtendedCube; }
	void	setFacingCount(int pFacingCount) { facingCount = pFacingCount; }

	///////////////////////////////////////////
	//Constructors, Destructors, and operators
	///////////////////////////////////////////
	qqhSLOTFacingInfo();
	qqhSLOTFacingInfo(const qqhSLOTFacingInfo & other);
	virtual ~qqhSLOTFacingInfo() {}
	qqhSLOTFacingInfo& operator=(const qqhSLOTFacingInfo &other);

	///////////////////////////////////////////
	//Other methods
	///////////////////////////////////////////
	void BuildFromStream(CExeStringArray &bufArray);
	void StreamAttributes(CExeStringArray &attributBuf);
};


/////////////////////////////////////////
// SLOTBayRule class
/////////////////////////////////////////
class qqhSLOTBayRule : public qqhSLOTObject
{
protected:
	float	palletHeight;
	float	pctUtilSelPos;
	float	pctUtilRsvPos;
	float	pctRsvToSelPos;
	float	desiredRplnPerWeek;
	float	clearance;
	float	addlResvCube;
	CArray <qqhSLOTFacingInfo, qqhSLOTFacingInfo&> facingInfoList;

public:
	///////////////////////////////////////////
	//Gets and Sets
	///////////////////////////////////////////
	float	getPalletHeight() { return palletHeight; }
	float	getPctUtilSelPos() { return pctUtilSelPos; }
	float	getPctUtilRsvPos() { return pctUtilRsvPos; }
	float	getPctRsvToSelPos() { return pctRsvToSelPos; }
	float	getDesiredRplnPerWeek() { return desiredRplnPerWeek; }
	float	getClearance() { return clearance; }
	float	getAddlResvCube() { return addlResvCube; }
 	CArray	<qqhSLOTFacingInfo, qqhSLOTFacingInfo&> & getFacingInfoList() { return facingInfoList; }

	void	setPalletHeight(float pPalletHeight) { palletHeight = pPalletHeight; }
	void	setPctUtilSelPos(float pPctUtilSelPos) { pctUtilSelPos = pPctUtilSelPos; }
	void	setPctUtilRsvPos(float pPctUtilRsvPos) { pctUtilRsvPos = pPctUtilRsvPos; }
	void	setPctRsvToSelPos(float pPctRsvToSelPos) { pctRsvToSelPos = pPctRsvToSelPos; }
	void	setDesiredRplnPerWeek(float pDesiredRplnPerWeek) { desiredRplnPerWeek = pDesiredRplnPerWeek; }
	void	setClearance(float pClearance) { clearance = pClearance; }
	void	setAddlResvCube(float pAddlResvCube) { addlResvCube = pAddlResvCube; }
	void	setFacingInfoList(const CArray <qqhSLOTFacingInfo, qqhSLOTFacingInfo&> & pFacingInfoList) { facingInfoList.Copy(pFacingInfoList); }

	///////////////////////////////////////////
	//Constructors, Destructors, and operators
	///////////////////////////////////////////
	qqhSLOTBayRule();
	qqhSLOTBayRule(const qqhSLOTBayRule & other);
	virtual ~qqhSLOTBayRule() {}
	qqhSLOTBayRule& operator=(const qqhSLOTBayRule &other);

	///////////////////////////////////////////
	//Other methods
	///////////////////////////////////////////
	void BuildFromStream(CExeStringArray &bufArray);
	void StreamAttributes(CExeStringArray &attributBuf);
};

/////////////////////////////////////////
// SLOTLevelProfile class
/////////////////////////////////////////
class qqhSLOTLevelProfile : public qqhSLOTObject
{
protected:
	float	forkFixedInsertion;
	BOOL	isBarHidden;
	BOOL	isRotateAllowed;
	BOOL	isVariableLocationsAllowed;
	CArray	<qqhSLOTLocationProfile, qqhSLOTLocationProfile&> locationProfileList;
	CArray	<qqhSLOTLevelLaborProfile, qqhSLOTLevelLaborProfile&> levelLaborProfileList;
	float	maxWeight;
	int		relativeLevel;
	float	thickness;
	float	overhang;
	float	facingGap;
	float	facingSnap;
	float	minLocWidth;
	float	productGap;
	float	productSnap;

public:
	///////////////////////////////////////////
	//Gets and Sets
	///////////////////////////////////////////
	float	getForkFixedInsertion() { return forkFixedInsertion; }
	BOOL	getIsBarHidden() { return isBarHidden; }
	BOOL	getIsRotateAllowed() { return isRotateAllowed; }
	BOOL	getIsVariableLocationsAllowed() { return isVariableLocationsAllowed; }
	CArray	<qqhSLOTLocationProfile, qqhSLOTLocationProfile&>& getLocationProfileList() { return locationProfileList; }
	CArray	<qqhSLOTLevelLaborProfile, qqhSLOTLevelLaborProfile&>& getLevelLaborProfileList() { return levelLaborProfileList; }
	float	getMaxWeight() { return maxWeight; }
	int		getRelativeLevel() { return relativeLevel; }
	float	getThickness() { return thickness; }
	float	getOverhang() { return overhang; }
	float	getFacingGap() { return facingGap; }
	float	getFacingSnap() { return facingSnap; }
	float	getMinLocWidth() { return minLocWidth; }
	float	getProductGap() { return productGap; }
	float	getProductSnap() { return productSnap; }

	void	setForkFixedInsertion(float pForkFixedInsertion) { forkFixedInsertion = pForkFixedInsertion; }
	void	setIsBarHidden(BOOL pIsBarHidden) { isBarHidden = pIsBarHidden; }
	void	setIsRotateAllowed(BOOL pIsRotateAllowed) { isRotateAllowed = pIsRotateAllowed; }
	void	setIsVariableLocationsAllowed(BOOL pIsVariableLocationsAllowed) { isVariableLocationsAllowed = pIsVariableLocationsAllowed; }
	void	setLocationProfileList(const CArray <qqhSLOTLocationProfile, qqhSLOTLocationProfile&> & pLocationProfileList) { locationProfileList.Copy(pLocationProfileList); }
	void	setLevelLaborProfileList(const CArray <qqhSLOTLevelLaborProfile, qqhSLOTLevelLaborProfile&> & pLevelLaborProfileList) { levelLaborProfileList.Copy(pLevelLaborProfileList); }
	void	setMaxWeight(float pMaxWeight) { maxWeight = pMaxWeight; }
	void	setRelativeLevel(int pRelativeLevel) { relativeLevel = pRelativeLevel; }
	void	setThickness(float pThickness) { thickness = pThickness; }
	void	setOverhang(float pOverhang) { overhang = pOverhang; }
	void	setFacingGap(float pFacingGap) { facingGap = pFacingGap; }
	void	setFacingSnap(float pFacingSnap) { facingSnap = pFacingSnap; }
	void	setMinLocWidth(float pMinLocWidth) { minLocWidth = pMinLocWidth; }
	void	setProductGap(float pProductGap) { productGap = pProductGap; }
	void	setProductSnap(float pProductSnap) { productSnap = pProductSnap; }

	///////////////////////////////////////////
	//Constructors, Destructors, and operators
	///////////////////////////////////////////
	qqhSLOTLevelProfile();
	qqhSLOTLevelProfile(const qqhSLOTLevelProfile & other);
	virtual ~qqhSLOTLevelProfile() {locationProfileList.RemoveAll(); levelLaborProfileList.RemoveAll();}
	qqhSLOTLevelProfile& operator=(const qqhSLOTLevelProfile &other);

	///////////////////////////////////////////
	//Other methods
	///////////////////////////////////////////
	void BuildFromStream(CExeStringArray &bufArray);
	void StreamAttributes(CExeStringArray &attributBuf);
};

/////////////////////////////////////////
// SLOTBayProfile class
/////////////////////////////////////////
class qqhSLOTBayProfile : public qqhSLOTObject
{
protected:
	float	width;
	float	depth;
	float	height;
	float	barWidth;
	qqhSLOTBayRule bayRule;
	int		bayType;
	int		flowDifference;
	BOOL	isHazardRack;
	CArray <qqhSLOTLevelProfile, qqhSLOTLevelProfile&> levelProfileList;
	float	maxWeight;
	int		palletDepth;
	int		palletHeight;
	float	rackCost;

public:
	 
	///////////////////////////////////////////
	//Gets and Sets
	///////////////////////////////////////////
	float	getWidth() { return width; }
	float	getDepth() { return depth; }
	float	getHeight() { return height; }
	float	getBarWidth() { return barWidth; }
	qqhSLOTBayRule& getBayRule() { return bayRule; }
	int		getBayType() { return bayType; }
	int		getFlowDifference() { return flowDifference; }
	BOOL	getIsHazardRack() { return isHazardRack; }
	CArray	<qqhSLOTLevelProfile, qqhSLOTLevelProfile&>& getLevelProfileList() { return levelProfileList; }
	float	getMaxWeight() { return maxWeight; }
	int		getPalletDepth() { return palletDepth; }
	int		getPalletHeight() { return palletHeight; }
	float	getRackCost() { return rackCost; }

	void	setWidth(float pWidth) {  width = pWidth; }
	void	setDepth(float pDepth) {  depth = pDepth; }
	void	setHeight(float pHeight) {  height = pHeight; }
	void	setBarWidth(float pBarWidth) {  barWidth = pBarWidth; }
	void	setBayRule(const qqhSLOTBayRule& pBayRule) {  bayRule = pBayRule; }
	void	setBayType(int pBayType) {  bayType = pBayType; }
	void	setFlowDifference(int pFlowDifference) {  flowDifference = pFlowDifference; }
	void	setIsHazardRack(BOOL pIsHazardRack) {  isHazardRack = pIsHazardRack; }
	void	setLevelProfileList(const CArray <qqhSLOTLevelProfile, qqhSLOTLevelProfile&> & pLevelProfileList) {  levelProfileList.Copy(pLevelProfileList); }
	void	setMaxWeight(float pMaxWeight) {  maxWeight = pMaxWeight; }
	void	setPalletDepth(int pPalletDepth) {  palletDepth = pPalletDepth; }
	void	setPalletHeight(int pPalletHeight) {  palletHeight = pPalletHeight; }
	void	setRackCost(float pRackCost) {  rackCost = pRackCost; }

	///////////////////////////////////////////
	//Constructors, Destructors, and operators
	///////////////////////////////////////////
	qqhSLOTBayProfile();
	qqhSLOTBayProfile(const qqhSLOTBayProfile & other);
	virtual ~qqhSLOTBayProfile() {levelProfileList.RemoveAll();}
	qqhSLOTBayProfile& operator=(const qqhSLOTBayProfile &other);

	///////////////////////////////////////////
	//Other Methods
	///////////////////////////////////////////
	void BuildFromStream(CExeStringArray &bufArray);
	void StreamAttributes(CExeStringArray &attributBuf);
};

/////////////////////////////////////////
// SLOTSideProfile class
/////////////////////////////////////////
class qqhSLOTSideProfile : public qqhSLOTObject
{
protected:
	CArray	<qqhSLOTBayProfile, qqhSLOTBayProfile&> bayProfileList;
	float	maxBayDepth;
	float	maxBayHeight;
	float	sideTotalWidth;
	float	space;
	
public:
	///////////////////////////////////////////
	//Gets and Sets
	///////////////////////////////////////////
	CArray <qqhSLOTBayProfile, qqhSLOTBayProfile&>& getBayProfileList(){ return bayProfileList; }
	float	getMaxBayDepth(){ return maxBayDepth; }
	float	getMaxBayHeight(){ return maxBayHeight; }
	float	getSideTotalWidth(){ return sideTotalWidth; }
	float	getSpace() { return space; }

	void	setBayProfileList(const CArray<qqhSLOTBayProfile, qqhSLOTBayProfile&>& pBayProfileList){ bayProfileList.Copy(pBayProfileList); }
	void	setMaxBayDepth(float pMaxBayDepth){ maxBayDepth = pMaxBayDepth; }
	void	setMaxBayHeight(float pMaxBayHeight){ maxBayHeight = pMaxBayHeight; }
	void	setSideTotalWidth(float pSideTotalWidth){ sideTotalWidth = pSideTotalWidth; }
	void	setSpace(float pSpace) { space = pSpace; }

	///////////////////////////////////////////
	//Constructors, Destructors, and operators
	///////////////////////////////////////////
	qqhSLOTSideProfile();
	qqhSLOTSideProfile(const qqhSLOTSideProfile & other);
	virtual ~qqhSLOTSideProfile() {bayProfileList.RemoveAll();}
	qqhSLOTSideProfile& operator=(const qqhSLOTSideProfile &other);
	
	///////////////////////////////////////////
	//Other methods
	///////////////////////////////////////////
	void BuildFromStream(CExeStringArray &bufArray);
	void StreamAttributes(CExeStringArray &attributBuf);
};

/////////////////////////////////////////
// SLOTAisleProfile class
/////////////////////////////////////////
class qqhSLOTAisleProfile : public qqhSLOTObject
{
protected:
	qqhSLOTSideProfile	side1Profile;
	qqhSLOTSideProfile	side2Profile;

	float				side1Space;
	float				side2Space;
	float				aisleSpace;

public:
	///////////////////////////////////////////
	//Gets and Sets
	///////////////////////////////////////////
	qqhSLOTSideProfile&	getSide1Profile() { return side1Profile; }
	qqhSLOTSideProfile&	getSide2Profile() { return side2Profile; }
	float				getSide1Space()	{ return side1Space; }
	float				getSide2Space() { return side2Space; }
	float				getAisleSpace() { return aisleSpace; }

	void	setSide1Profile(const qqhSLOTSideProfile & pSide1Profile) { side1Profile = pSide1Profile; }
	void	setSide2Profile(const qqhSLOTSideProfile & pSide2Profile) { side2Profile = pSide2Profile; }
	void	setSide1Space(float pSide1Space)	{ side1Space = pSide1Space; }
	void	setSide2Space(float pSide2Space) { side2Space = pSide2Space; }
	void	setAisleSpace(float pAisleSpace) { aisleSpace = pAisleSpace; }

	///////////////////////////////////////////
	//Constructors, Destructors, and operators
	///////////////////////////////////////////
	qqhSLOTAisleProfile();
	qqhSLOTAisleProfile(const qqhSLOTAisleProfile & other);
	virtual ~qqhSLOTAisleProfile() {}
	qqhSLOTAisleProfile& operator=(const qqhSLOTAisleProfile &other);

	///////////////////////////////////////////
	//Other Methods
	///////////////////////////////////////////
	void BuildFromStream(CExeStringArray &bufArray);
	void StreamAttributes(CExeStringArray &attributBuf);
};

/////////////////////////////////////////
// Holder class
/////////////////////////////////////////
class qqhSLOTHolder : public qqhSLOTObject
{
protected:
	CString isChanged;

public:

	///////////////////////////////////////////
	//Gets and Sets
	///////////////////////////////////////////
	CString & getIsChanged() { return isChanged; }

	void setIsChanged( CString & pIsChanged ) { isChanged = pIsChanged; }

	///////////////////////////////////////////
	//Constructors, Destructors, and operators
	///////////////////////////////////////////
	qqhSLOTHolder();
	virtual ~qqhSLOTHolder() {}
	qqhSLOTHolder(const qqhSLOTHolder & other);
};

/////////////////////////////////////////
// Location class
/////////////////////////////////////////
class qqhSLOTLocation : public qqhSLOTHolder
{
protected:
	double maxWeight;
	double width;
	double depth;
	double height;
	int handlingMethod;
	int IsSelect;
	int IsOverridden;
	qqhSLOTLocationProfile locationProfile;

public:

	///////////////////////////////////////////
	//Gets and Sets
	///////////////////////////////////////////
	double getMaxWeight()        { return maxWeight; }
	double getWidth()            { return width; }
	double getDepth()            { return depth; }
	double getHeight()           { return height; }
	int    getHandlingMethod()   { return handlingMethod; }
	int    getIsSelect()         { return IsSelect; }
	int    getIsOverridden()     { return IsOverridden; }
	qqhSLOTLocationProfile & getLocationProfile() { return locationProfile; }

	void setMaxWeight( double pMaxWeight ) { maxWeight = pMaxWeight; }
	void setWidth( double pWidth ) { width = pWidth; }
	void setDepth( double pDepth ) { depth = pDepth; }
	void setHeight( double pHeight ) { height = pHeight; }
	void setHandlingMethod (int pHandlingMethod) { handlingMethod = pHandlingMethod; }
	void setIsSelect (int pIsSelect) { IsSelect = pIsSelect; }
	void setIsOverridden (int pIsOverridden) { IsOverridden = pIsOverridden; }
	void setLocationProfile(const qqhSLOTLocationProfile& pLocationProfile) { locationProfile = pLocationProfile; }

	///////////////////////////////////////////
	//Constructors, Destructors, and operators
	///////////////////////////////////////////
	qqhSLOTLocation();
	virtual ~qqhSLOTLocation() {}
	qqhSLOTLocation(const qqhSLOTLocation& other);
	qqhSLOTLocation& operator=(const qqhSLOTLocation& other);
	///////////////////////////////////////////
	//Other Methods
	///////////////////////////////////////////
	void BuildFromStream(CExeStringArray &bufArray);
	void StreamAttributes(CExeStringArray & attributBuf);
};

/////////////////////////////////////////
// Level class
/////////////////////////////////////////
class qqhSLOTLevel : public qqhSLOTHolder
{
protected:
	double forkFixedInsertion;
	BOOL	isRotateAllowed;
	BOOL	isVariableLocationsAllowed;
	float	facingGap;
	float	facingSnap;
	float	minLocWidth;
	float	productGap;
	float	productSnap;
	BOOL	isOverridden;
	qqhSLOTLevelProfile	levelProfile;
	CArray <qqhSLOTLocation, qqhSLOTLocation&> childList;

public:

	///////////////////////////////////////////
	//Gets and Sets
	///////////////////////////////////////////
	float	getForkFixedInsertion() { return (float)forkFixedInsertion; }
	BOOL	getIsRotateAllowed() { return isRotateAllowed; }
	BOOL	getIsVariableLocationsAllowed() { return isVariableLocationsAllowed; }
	float	getFacingGap() { return (float)facingGap; }
	float	getFacingSnap() { return (float)facingSnap; }
	float	getMinLocWidth() { return (float)minLocWidth; }
	float	getProductGap() { return (float)productGap; }
	float	getProductSnap() { return (float)productSnap; }
	BOOL	getIsOverridden() { return isOverridden; }
	qqhSLOTLevelProfile & getLevelProfile() { return levelProfile; }
	CArray <qqhSLOTLocation, qqhSLOTLocation&> & getChildList () { return childList; }

	void	setForkFixedInsertion(float pForkFixedInsertion) { forkFixedInsertion = pForkFixedInsertion; }
	void	setIsRotateAllowed(BOOL pIsRotateAllowed) { isRotateAllowed = pIsRotateAllowed; }
	void	setIsVariableLocationsAllowed(BOOL pIsVariableLocationsAllowed) { isVariableLocationsAllowed = pIsVariableLocationsAllowed; }
	void	setFacingGap(float pFacingGap) { facingGap = pFacingGap; }
	void	setFacingSnap(float pFacingSnap) { facingSnap = pFacingSnap; }
	void	setMinLocWidth(float pMinLocWidth) { minLocWidth = pMinLocWidth; }
	void	setProductGap(float pProductGap) { productGap = pProductGap; }
	void	setProductSnap(float pProductSnap) { productSnap = pProductSnap; }
	void	setIsOverridden(BOOL pIsOverridden) { isOverridden = pIsOverridden; }
	void	setLevelProfile(const qqhSLOTLevelProfile& pLevelProfile) { levelProfile = pLevelProfile; }
	void	setChildList(CArray <qqhSLOTLocation, qqhSLOTLocation&> & pChildList) { childList.Copy(pChildList); }


	///////////////////////////////////////////
	//Constructors, Destructors, and operators
	///////////////////////////////////////////
	qqhSLOTLevel();
	virtual ~qqhSLOTLevel() {childList.RemoveAll();}
	qqhSLOTLevel(const qqhSLOTLevel& other);
	qqhSLOTLevel& operator=(const qqhSLOTLevel& other);
	///////////////////////////////////////////
	//Other Methods
	///////////////////////////////////////////
	void BuildFromStream(CExeStringArray &bufArray);
	void StreamAttributes(CExeStringArray & attributBuf);
};

/////////////////////////////////////////
// Bay class
/////////////////////////////////////////
class qqhSLOTBay : public qqhSLOTHolder
{
protected:
	char acadHandle[20];
	qqhSLOTBayProfile bayProfile;

	CArray <qqhSLOTLevel, qqhSLOTLevel&> childList;

public:

	///////////////////////////////////////////
	//Gets and Sets
	///////////////////////////////////////////
	char * getAcadHandle()    { return acadHandle; }
	qqhSLOTBayProfile & getBayProfile() { return bayProfile; }
	CArray <qqhSLOTLevel, qqhSLOTLevel&> & getChildList() { return childList; }

	void setAcadHandle(const char * pAcadHandle)  { strcpy(acadHandle, pAcadHandle); }
	void setBayProfile(const qqhSLOTBayProfile& pBayProfile) { bayProfile = pBayProfile; }
	void setChildList(const CArray <qqhSLOTLevel, qqhSLOTLevel&> & pChildList) { childList.Copy(pChildList); }


	///////////////////////////////////////////
	//Constructors, Destructors, and operators
	///////////////////////////////////////////
	qqhSLOTBay();
	virtual ~qqhSLOTBay() {childList.RemoveAll();}
	qqhSLOTBay(const qqhSLOTBay& other);
	qqhSLOTBay& operator=(const qqhSLOTBay& other);
	///////////////////////////////////////////
	//Other Methods
	///////////////////////////////////////////
	void BuildFromStream(CExeStringArray &bufArray);
	void StreamAttributes(CExeStringArray & attributBuf);
};

/////////////////////////////////////////
// Side class
/////////////////////////////////////////
class qqhSLOTSide : public qqhSLOTHolder
{
protected:
	int IsRotated;
	CArray <qqhSLOTBay, qqhSLOTBay&> childList;

public:

	///////////////////////////////////////////
	//Gets and Sets
	///////////////////////////////////////////
	int getIsRotated() { return IsRotated; }
	CArray <qqhSLOTBay, qqhSLOTBay&> & getChildList() { return childList; }

	void setIsRotated(int pIsRotated) { IsRotated = pIsRotated; }
	void setChildList(const CArray <qqhSLOTBay, qqhSLOTBay&> & pChildList) { childList.Copy(pChildList); }

	qqhSLOTSide();
	virtual ~qqhSLOTSide() {childList.RemoveAll();}
	CArray <qqhSLOTBay, qqhSLOTBay&>& GetChildList();
	qqhSLOTSide(const qqhSLOTSide& other);
	qqhSLOTSide& operator=(const qqhSLOTSide& other);
	void SetIsRotated(int rot);
	int GetIsRotated();
	void BuildFromStream(CExeStringArray &bufArray);
	void StreamAttributes(CExeStringArray & attributBuf);
};

/////////////////////////////////////////
// Aisle class
/////////////////////////////////////////
class qqhSLOTAisle : public qqhSLOTHolder
{
protected:
	CArray <qqhSLOTSide, qqhSLOTSide&> childList;
	qqhSLOTPickPath pickPath;

	CString		leftBayStart;
	int			leftBayStep;
	int			leftBayScheme;
	CString		rightBayStart;
	int			rightBayStep;
	int			rightBayScheme;
	CString		leftLevelStart;
	int			leftLevelStep;
	int			leftLevelScheme;
	int			leftLevelBreak;
	CString		rightLevelStart;
	int			rightLevelStep;
	int			rightLevelScheme;
	int			rightLevelBreak;
	CString		leftLocationStart;
	int			leftLocationStep;
	int			leftLocationScheme;
	int			leftLocationBreak;
	CString		rightLocationStart;
	int			rightLocationStep;
	int			rightLocationScheme;
	int			rightLocationBreak;
	int			pickPathDirection;
	int			pickPathType;
	int			pickPathStartSide;
	int			baysInPattern;
	float		rotation;
public:
	///////////////////////////////////////////
	//Gets and Sets
	///////////////////////////////////////////
	CArray <qqhSLOTSide, qqhSLOTSide&> & getChildList() { return childList; }
	qqhSLOTPickPath & getPickPath() { return pickPath; }
	CString	&	getLeftBayStart() { return leftBayStart; }
	int			getLeftBayStep() { return leftBayStep; }
	int			getLeftBayScheme() { return leftBayScheme; }
	CString	&	getRightBayStart() { return rightBayStart; }
	int			getRightBayStep() { return rightBayStep; }
	int			getRightBayScheme() { return rightBayScheme; }
	CString	&	getLeftLevelStart() { return leftLevelStart; }
	int			getLeftLevelStep() { return leftLevelStep; }
	int			getLeftLevelScheme() { return leftLevelScheme; }
	int			getLeftLevelBreak() { return leftLevelBreak; }
	CString	&	getRightLevelStart() { return rightLevelStart; }
	int			getRightLevelStep() { return rightLevelStep; }
	int			getRightLevelScheme() { return rightLevelScheme; }
	int			getRightLevelBreak() { return rightLevelBreak; }
	CString	&	getLeftLocationStart() { return leftLocationStart; }
	int			getLeftLocationStep() { return leftLocationStep; }
	int			getLeftLocationScheme() { return leftLocationScheme; }
	int			getLeftLocationBreak() { return leftLocationBreak; }
	CString	&	getRightLocationStart() { return rightLocationStart; }
	int			getRightLocationStep() { return rightLocationStep; }
	int			getRightLocationScheme() { return rightLocationScheme; }
	int			getRightLocationBreak() { return rightLocationBreak; }
	int			getPickPathDirection() { return pickPathDirection; }
	int			getPickPathType() { return pickPathType; }
	int			getPickPathStartSide() { return pickPathStartSide; }
	int			getBaysInPattern() { return baysInPattern; }
	float		getRotation() { return rotation; }

	void	setChildList(CArray <qqhSLOTSide, qqhSLOTSide&> & pChildList) { childList.Copy(pChildList); }
	void	setPickPath(qqhSLOTPickPath & pPickPath) { pickPath = pPickPath; }
	void	setLeftBayStart(CString & pLeftBayStart) { leftBayStart = pLeftBayStart; }
	void	setLeftBayStep(int pLeftBayStep) { leftBayStep = pLeftBayStep; }
	void	setLeftBayScheme(int pLeftBayScheme) { leftBayScheme = pLeftBayScheme; }
	void	setRightBayStart(CString pRightBayStart) { rightBayStart = pRightBayStart; }
	void	setRightBayStep(int pRightBayStep) { rightBayStep = pRightBayStep; }
	void	setRightBayScheme(int pRightBayScheme) { rightBayScheme = pRightBayScheme; }
	void	setLeftLevelStart(CString & pLeftLevelStart) { leftLevelStart = pLeftLevelStart; }
	void	setLeftLevelStep(int pLeftLevelStep) { leftLevelStep = pLeftLevelStep; }
	void	setLeftLevelScheme(int pLeftLevelScheme) { leftLevelScheme = pLeftLevelScheme; }
	void	setLeftLevelBreak(int pLeftLevelBreak) { leftLevelBreak = pLeftLevelBreak; }
	void	setRightLevelStart(CString & pRightLevelStart) { rightLevelStart = pRightLevelStart; }
	void	setRightLevelStep(int pRightLevelStep) { rightLevelStep = pRightLevelStep; }
	void	setRightLevelScheme(int pRightLevelScheme) { rightLevelScheme = pRightLevelScheme; }
	void	setRightLevelBreak(int pRightLevelBreak) { rightLevelBreak = pRightLevelBreak; }
	void	setLeftLocationStart(CString & pLeftLocationStart) { leftLocationStart = pLeftLocationStart; }
	void	setLeftLocationStep(int pLeftLocationStep) { leftLocationStep = pLeftLocationStep; }
	void	setLeftLocationScheme(int pLeftLocationScheme) { leftLocationScheme = pLeftLocationScheme; }
	void	setLeftLocationBreak(int pLeftLocationBreak) { leftLocationBreak = pLeftLocationBreak; }
	void	setRightLocationStart(CString & pRightLocationStart) { rightLocationStart = pRightLocationStart; }
	void	setRightLocationStep(int pRightLocationStep) { rightLocationStep = pRightLocationStep; }
	void	setRightLocationScheme(int pRightLocationScheme) { rightLocationScheme = pRightLocationScheme; }
	void	setRightLocationBreak(int pRightLocationBreak) { rightLocationBreak = pRightLocationBreak; }
	void	setPickPathDirection(int pPickPathDirection) { pickPathDirection = pPickPathDirection; }
	void	setPickPathType(int pPickPathType) { pickPathType = pPickPathType; }
	void	setPickPathStartSide(int pPickPathStartSide) { pickPathStartSide = pPickPathStartSide; }
	void	setBaysInPattern(int pBaysInPattern) { baysInPattern = pBaysInPattern; }
	void	setRotation(float pRotation) { rotation = pRotation; }

	///////////////////////////////////////////
	//Constructors, Destructors, and operators
	///////////////////////////////////////////
	qqhSLOTAisle();
	virtual ~qqhSLOTAisle() {childList.RemoveAll();}
	CArray <qqhSLOTSide, qqhSLOTSide&>& GetChildList();
	qqhSLOTAisle(const qqhSLOTAisle& other);
	qqhSLOTAisle& operator=(const qqhSLOTAisle& other);
	///////////////////////////////////////////
	//Other Methods
	///////////////////////////////////////////
	void BuildFromStream(CExeStringArray &bufArray);
	void StreamAttributes(CExeStringArray & attributBuf);
};

/////////////////////////////////////////
// Section class
/////////////////////////////////////////
class qqhSLOTSection : public qqhSLOTHolder
{
protected:
	CArray <qqhSLOTAisle, qqhSLOTAisle&> childList;
	CArray <qqhSLOTHotSpot, qqhSLOTHotSpot&> hotSpotList;
	qqhSLOTPickPath pickPath;
	CString locationMask;
	float avgCubePerTrip;
	float forkDistFixed;
	float forkDistVar;
	float forkLaborRate;
	float replenAvgDist;
	float selectDistFixed;
	float selectDistVar;
	float selectLaborRate;
	float selDist;
	int avgOrdQty;
	int contQty;
	int orderCount;
	CString applyBrokenOrder;

public:

	///////////////////////////////////////////
	//Gets and Sets
	///////////////////////////////////////////
	CArray <qqhSLOTAisle, qqhSLOTAisle&> & getChildList() { return childList; }
	CArray <qqhSLOTHotSpot, qqhSLOTHotSpot&> & getHotSpotList() { return hotSpotList; }
	qqhSLOTPickPath & getPickPath() { return pickPath; }
	CString & getLocationMask() { return locationMask; }
	float getAvgCubePerTrip()   { return avgCubePerTrip; }
	float getForkDistFixed()    { return forkDistFixed; }
	float getForkDistVar()      { return forkDistVar; }
	float getForkLaborRate()    { return forkLaborRate; }
	float getReplenAvgDist()    { return replenAvgDist; }
	float getSelectDistFixed()  { return selectDistFixed; }
	float getSelectDistVar()    { return selectDistVar; }
	float getSelectLaborRate()  { return selectLaborRate; }
	float getSelDist()          { return selDist; }
	int   getAvgOrdQty()        { return avgOrdQty; }
	int   getContQty()          { return contQty; }
	int   getOrderCount()       { return orderCount; }
	CString & getApplyBrokenOrder() { return applyBrokenOrder; }

	void setChildList(CArray <qqhSLOTAisle, qqhSLOTAisle&> & pChildList) { childList.Copy(pChildList); }
	void setHotSpotList(CArray <qqhSLOTHotSpot, qqhSLOTHotSpot&> & pHotSpotList) { hotSpotList.Copy(pHotSpotList); }
	void setPickPath(qqhSLOTPickPath & pPickPath) { pickPath = pPickPath; }
	void setLocationMask(CString & pLocationMask) { locationMask = pLocationMask; }
	void setAvgCubePerTrip(float pAvgCubePerTrip) {  avgCubePerTrip = pAvgCubePerTrip; }
	void setForkDistFixed(float pForkDistFixed) { forkDistFixed = pForkDistFixed; }
	void setForkDistVar(float pForkDistVar) { forkDistVar = pForkDistVar; }
	void setForkLaborRate(float pForkLaborRate) { forkLaborRate = pForkLaborRate; }
	void setReplenAvgDist(float pReplenAvgDist) { replenAvgDist = pReplenAvgDist; }
	void setSelectDistFixed(float pSelectDistFixed) { selectDistFixed = pSelectDistFixed; }
	void setSelectDistVar(float pSelectDistVar) { selectDistVar = pSelectDistVar; }
	void setSelectLaborRate(float pSelectLaborRate) { selectLaborRate = pSelectLaborRate; }
	void setSelDist(float pSelDist) { selDist = pSelDist; }
	void setAvgOrdQty(int pAvgOrdQty) { avgOrdQty = pAvgOrdQty; }
	void setContQty(int pContQty) { contQty = pContQty; }
	void setOrderCount(int pOrderCount) { orderCount = pOrderCount; }
	void setApplyBrokenOrder(CString & pApplyBrokenOrder) { applyBrokenOrder = pApplyBrokenOrder; }

	///////////////////////////////////////////
	//Constructors, Destructors, and operators
	///////////////////////////////////////////
	qqhSLOTSection();
	virtual ~qqhSLOTSection() {childList.RemoveAll();}
	CArray <qqhSLOTAisle, qqhSLOTAisle&>& GetChildList();
	qqhSLOTSection(const qqhSLOTSection& other);
	qqhSLOTSection& operator=(const qqhSLOTSection& other);
	///////////////////////////////////////////
	//Other Methods
	///////////////////////////////////////////
	void BuildFromStream(CExeStringArray &bufArray);
	void StreamAttributes(CExeStringArray & attributBuf);
};

/////////////////////////////////////////
// Facility class
/////////////////////////////////////////
class qqhSLOTFacility: public qqhSLOTHolder
{
protected:
	int region;
	int units;
	int slotType;
	CString strCadFileName;
	int IsModified;
	int duration;
	float cost;
	int originalFacilityId;
	int timeHorizonUnit;
	CString clientNameOpened;

	CArray <qqhSLOTSection, qqhSLOTSection&> childList;
	CArray <qqhSLOTPickPath, qqhSLOTPickPath&> pickPathList;

public:

	///////////////////////////////////////////
	//Gets and Sets
	///////////////////////////////////////////
	int   getRegion()               { return region; }
	int   getUnits()                { return units; }
	int   getSlotType()             { return slotType; }
	CString & getStrCadFileName()   { return strCadFileName; }
	int   getIsModified()           { return IsModified; }
	int   getDuration()             { return duration; }
	float getCost()                 { return cost; }
	int   getOriginalFacilityId()   { return originalFacilityId; }
	CString & getClientNameOpened() { return clientNameOpened; }
	CArray <qqhSLOTSection, qqhSLOTSection&> & getChildList() { return childList; }
	CArray <qqhSLOTPickPath, qqhSLOTPickPath&> & getPickPathList() { return pickPathList; }
	int	  getTimeHorizonUnit()		{ return timeHorizonUnit; }

	void setTimeHorizonUnit(int pTimeHorizonUnit) { timeHorizonUnit = pTimeHorizonUnit; }
	void setRegion(int pRegion) { region = pRegion; }
	void setUnits(int pUnits) { units = pUnits; }
	void setSlotType(int pSlotType) { slotType = pSlotType; }
	void setStrCadFileName(CString & pStrCadFileName) { strCadFileName = pStrCadFileName; }
	void setIsModified(int pIsModified) { IsModified = pIsModified; }
	void setDuration(int pDuration) { duration = pDuration; }
	void setCost(float pCost) { cost = pCost; }
	void setOriginalFacilityId(int pOriginalFacilityId) { originalFacilityId = pOriginalFacilityId; }
	void setClientNameOpened(CString & pClientNameOpened) { clientNameOpened = pClientNameOpened; }
	void setChildList(CArray <qqhSLOTSection, qqhSLOTSection&> & pChildList) {childList.Copy(pChildList); }
	void setPickPathList(CArray <qqhSLOTPickPath, qqhSLOTPickPath&> & pPickPathList) { pickPathList.Copy(pPickPathList); }

	///////////////////////////////////////////
	//Constructors, Destructors, and operators
	///////////////////////////////////////////
	qqhSLOTFacility();
	virtual ~qqhSLOTFacility() {childList.RemoveAll();}
	qqhSLOTFacility(const qqhSLOTFacility& other);
	qqhSLOTFacility(const char *strName);
	qqhSLOTFacility& operator=(const qqhSLOTFacility& other);
	///////////////////////////////////////////
	//Other Methods
	///////////////////////////////////////////
	void BuildFromStream(CExeStringArray &bufArray);
	void StreamAttributes(CExeStringArray & attributBuf);
};

/////////////////////////////////////////
// SLOTGroupBay class
/////////////////////////////////////////
class qqhSLOTGroupBay : public qqhSLOTObject 
{
protected:
	qqhSLOTSlottingGroup slottingGroup;
	qqhSLOTBay Bay;

public:
	///////////////////////////////////////////
	//Gets and Sets
	///////////////////////////////////////////
	qqhSLOTSlottingGroup &  getSLOTSlottingGroup() { return slottingGroup; }
	qqhSLOTBay &			getBay() { return Bay; }

	void	setSlottingGroup(qqhSLOTSlottingGroup & pSlottingGroup) { slottingGroup = pSlottingGroup; }
	void	setBay(qqhSLOTBay & pBay) { Bay = pBay; }

	///////////////////////////////////////////
	//Constructors, Destructors, and operators
	///////////////////////////////////////////
	qqhSLOTGroupBay();
	qqhSLOTGroupBay(const qqhSLOTGroupBay & other);
	virtual ~qqhSLOTGroupBay() {}
	qqhSLOTGroupBay& operator= (const qqhSLOTGroupBay & other);
	///////////////////////////////////////////
	//Other Methods
	///////////////////////////////////////////
	void BuildFromStream(CExeStringArray &bufArray);
	void StreamAttributes(CExeStringArray & attributBuf);
};

#endif

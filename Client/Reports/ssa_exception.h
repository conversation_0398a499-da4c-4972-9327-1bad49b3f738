#ifndef SSA_EXCEPTION
#define SSA_EXCEPTION

#include <stdio.h>
#include <stdlib.h>
#include <string.h>

class SsaException  {
	public:
		SsaException()
		{
			mem_message = NULL;
			mem_file = NULL;
		}

		// The 'throw-catch' process needs this copy contructor to
		// ensure that the 'thrown' copy gets a new value for its
		// pointer.  If it is not defined explicitly, C++ causes a
		// double-delete error when it tries to destruct the
		// exception object.
		SsaException(const SsaException& e)
		{
			mem_message = (char *)malloc(strlen(e.mem_message) + 1);
			memset(mem_message, 0, strlen(e.mem_message) + 1);

			mem_file = (char *)malloc(strlen(e.mem_file) + 1);
			memset(mem_file, 0, strlen(e.mem_file) + 1);
			
			strcpy(mem_message, e.mem_message);
			strcpy(mem_file, e.mem_file);
			mem_line = e.mem_line;
			mem_severity = e.mem_severity;
		}

		SsaException(char *message, char *file, int line, 
			int severity)
		{
			mem_message = (char *)malloc(strlen(message) + 1);
			memset(mem_message, 0, strlen(message) + 1);

			mem_file = (char *)malloc(strlen(file) + 1);
			memset(mem_file, 0, strlen(file) + 1);
			
			strcpy(mem_message, message);
			strcpy(mem_file, file);
			mem_line = line;
			mem_severity = severity;
		}
		virtual ~SsaException()
		{
			if (mem_message)
			{
				free(mem_message);
				mem_message = NULL;
			}

			if (mem_file)
			{
				free(mem_file);
				mem_file = NULL;
			}
		}

		void GetMessage(char *m)
		{
			strcpy(m, mem_message);
		}
		void GetAllMessage(char *am)
		{
			sprintf(am, "\n----------\nException:\n----------\n%s\n----------\noccurred in file %s at line %d.\n----------\n",
				mem_message, mem_file, mem_line);
		}

		int IsRetry(void)
		{
			if(mem_severity < 100)
				return 1;
			else
				return 0;
		}

	protected:
		char *mem_message;
		char *mem_file;
		int   mem_line;
		int   mem_severity;
};

#endif // SSA_EXCEPTION defined

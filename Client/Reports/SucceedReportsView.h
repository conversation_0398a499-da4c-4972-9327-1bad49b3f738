// SucceedReportsView.h : interface of the CSucceedReportsView class
//

#if !defined(AFX_SUCCEEDREPORTSVIEW_H__749C5A3D_EDCF_11D2_9CD9_0080C742D9DF__INCLUDED_)
#define AFX_SUCCEEDREPORTSVIEW_H__749C5A3D_EDCF_11D2_9CD9_0080C742D9DF__INCLUDED_

#pragma once

class CSucceedReportsView : public CScrollView
{
protected: // create from serialization only
	CSucceedReportsView();
	DECLARE_DYNCREATE(CSucceedReportsView)

// Attributes
public:
	CSucceedReportsDoc* GetDocument();
	int m_nPage;

// Operations
public:

// Overrides
	// ClassWizard generated virtual function overrides
	//{{AFX_VIRTUAL(CSucceedReportsView)
	public:
	virtual void OnDraw(CDC* pDC);  // overridden to draw this view
	virtual BOOL PreCreateWindow(CREATESTRUCT& cs);
	virtual void OnPrepareDC(CDC* pDC, CPrintInfo* pInfo = NULL);
	virtual void OnInitialUpdate();
	protected:
	virtual BOOL OnPreparePrinting(CPrintInfo* pInfo);
	virtual void OnBeginPrinting(CDC* pDC, CPrintInfo* pInfo);
	virtual void OnEndPrinting(CDC* pDC, CPrintInfo* pInfo);
	virtual void OnPrint(CDC* pDC, CPrintInfo* pInfo);
	virtual void OnUpdate(CView* pSender, LPARAM lHint, CObject* pHint);
	//}}AFX_VIRTUAL

// Implementation
public:
	CString DeleteChars(CString s, int nStart, int nCount);
	virtual ~CSucceedReportsView();
#ifdef _DEBUG
	virtual void AssertValid() const;
	virtual void Dump(CDumpContext& dc) const;
#endif

protected:

// Generated message map functions
protected:
	CFont printFont;
	CFont printBoldFont;
	//{{AFX_MSG(CSucceedReportsView)
	afx_msg void OnFileExportToText();
	afx_msg void OnFileExportDelimited();
	afx_msg void OnExcel();
	//}}AFX_MSG
	DECLARE_MESSAGE_MAP()

private:
	void PrintPageHeader(CDC *pDC, int fromWhere);
	void PrintPageFooter(CDC *pDC, int fromWhere);
	void UpdateScrollSizes(CDC *pDC);
};

#ifndef _DEBUG  // debug version in SucceedReportsView.cpp
inline CSucceedReportsDoc* CSucceedReportsView::GetDocument()
   { return (CSucceedReportsDoc*)m_pDocument; }
#endif

#endif // !defined(AFX_SUCCEEDREPORTSVIEW_H__749C5A3D_EDCF_11D2_9CD9_0080C742D9DF__INCLUDED_)

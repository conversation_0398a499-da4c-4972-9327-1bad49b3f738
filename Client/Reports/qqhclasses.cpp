#include "qqhclasses.h"

////////////////////////////////////////
// qqhSLOTCoordinate Member functions
////////////////////////////////////////
qqhSLOTCoordinate::qqhSLOTCoordinate(int xCoord, int yCoord, int zCoord)
{
	x = xCoord;
	y = yCoord;
	z = zCoord;
}

qqhSLOTCoordinate::qqhSLOTCoordinate()
{
	x = 0;
	y = 0;
	z = 0;
}

qqhSLOTCoordinate::qqhSLOTCoordinate(const qqhSLOTCoordinate & other)
{
	this->x = other.x;
	this->y = other.y;
	this->z = other.z;
//	DBID = other.DBID;
}

qqhSLOTCoordinate& qqhSLOTCoordinate::operator=(const qqhSLOTCoordinate &other)
{
	this->x = other.x;
	this->y = other.y;
	this->z = other.z;
//	DBID = other.DBID;
	return *this;
}

void qqhSLOTCoordinate::BuildFromStream(CExeStringArray &bufArray ) {
	char tempBuf[512];
	char tempREGName[100];
	char tempREGVal[256];
	char tempheader[100];
	int i;
	CExeStringArray tempArray;
	CString tempChkStr;

	for ( i = 1; (bufArray[i]).Find(REGBegSearch) != -1; i++ ) {
		/////////////////////////////////////////////////////////////
		//  REGs are processed here
		/////////////////////////////////////////////////////////////
		strcpy(tempBuf,bufArray[i].GetBuffer(0));
		bufArray[i].ReleaseBuffer();
		tempChkStr = tempBuf;
		if (tempChkStr.GetAt(tempChkStr.GetLength()-1) == '|')
			strcat(tempBuf,"1");
		if (strchr(tempBuf,'\n') == NULL)
			strcat(tempBuf,"\n");
		bufArray[i] = tempBuf;
		strcpy(tempheader,strtok(tempBuf,"|"));
		strcpy(tempREGName,strtok(NULL,"|"));
		if (strstr(bufArray[i].GetBuffer(0),"|\n") == NULL )
			strcpy(tempREGVal,strtok(NULL,"\n"));
		else
			strcpy(tempREGVal,"");
		bufArray[i].ReleaseBuffer();
		if ( strcmp(tempREGName,"XCoord") == 0 ) {
			this->x = atoi(tempREGVal);
		}
		else if ( strcmp(tempREGName,"YCoord") == 0 ) {
			this->y = atoi(tempREGVal);
		}
		else if ( strcmp(tempREGName,"ZCoord") == 0 ) {
			this->z = atoi(tempREGVal);
		}
	}

	return;
}

void qqhSLOTCoordinate::StreamAttributes(CExeStringArray & attributBuf) {

	CString tempBuf;

	attributBuf.Add(CString(CoordBegSearch) + "\n");

	tempBuf.Format("%sXCoord|%d\n", REGBegSearch, this->x);
	attributBuf.Add(tempBuf);

	tempBuf.Format("%sYCoord|%d\n", REGBegSearch, this->y);
	attributBuf.Add(tempBuf);

	tempBuf.Format("%sZCoord|%d\n", REGBegSearch, this->z);
	attributBuf.Add(tempBuf);

	attributBuf.Add(CString(CoordEndSearch) + "\n");

	return;
}

////////////////////////////////////////
// qqhSLOTPickPath Member functions
////////////////////////////////////////
qqhSLOTPickPath::qqhSLOTPickPath() {
	return;
	description = "New PickPath";
	DBID = 0;
	strcpy(acadHandle,"XXX");
	pathLength = 0.0;
	strcpy(conAcadHandle,"XXX");
}

qqhSLOTPickPath::qqhSLOTPickPath(const qqhSLOTPickPath & other) {
	DBID = other.DBID;
	description = other.description;
//	for ( int i = 0; i < other.coordinateList.GetSize(); i++ )
//		coordinateList.Add(other.coordinateList[i]);
	UDFList.Copy(other.UDFList);
	pathLength = other.pathLength;
	strcpy(acadHandle, other.acadHandle);
	strcpy(conAcadHandle, other.conAcadHandle);
	return;
}

qqhSLOTPickPath& qqhSLOTPickPath::operator=(const qqhSLOTPickPath &other) {
	DBID = other.DBID;
	description = other.description;
//	for ( int i = 0; i < other.coordinateList.GetSize(); i++ )
//		coordinateList.Add(other.coordinateList[i]);
	UDFList.Copy(other.UDFList);
	pathLength = other.pathLength;
	strcpy(acadHandle, other.acadHandle);
	strcpy(conAcadHandle, other.conAcadHandle);
	return *this;
}

void qqhSLOTPickPath::BuildFromStream(CExeStringArray &bufArray ) {
	int i;
	qqhSLOTCoordinate tempCoord;
	CExeStringArray tempArray;
	char tempBuf[512];
	char tempREGName[100];
	char tempREGVal[256];
	char tempheader[100];
	CString tempChkStr;
 
	for ( i=0; i < bufArray.GetSize(); i++ ) {
		if ( bufArray[i].Find(REGBegSearch) != -1 ) {
			/////////////////////////////////////////////////////////////
			//  REGs are processed here
			/////////////////////////////////////////////////////////////
			strcpy(tempBuf,bufArray[i].GetBuffer(0));
			bufArray[i].ReleaseBuffer();
			tempChkStr = tempBuf;
			if (tempChkStr.GetAt(tempChkStr.GetLength()-1) == '|')
				strcat(tempBuf,"1");
			if (strchr(tempBuf,'\n') == NULL)
				strcat(tempBuf,"\n");
			bufArray[i] = tempBuf;
			strcpy(tempheader,strtok(tempBuf,"|"));
			strcpy(tempREGName,strtok(NULL,"|"));
			if (strstr(bufArray[i].GetBuffer(0),"|\n") == NULL )
				strcpy(tempREGVal,strtok(NULL,"\n"));
			else
				strcpy(tempREGVal,"");
			bufArray[i].ReleaseBuffer();
			if ( strcmp(tempREGName,"Description") == 0 )
				this->description = tempREGVal;
			else if ( strcmp(tempREGName,"DBID") == 0 )
				this->DBID = atoi(tempREGVal);
			else if ( strcmp(tempREGName,"AcadHandle") == 0 )
				strcpy(this->acadHandle,tempREGVal);
			else if ( strcmp(tempREGName,"PathLength") == 0 )
				this->pathLength = (float)atof(tempREGVal);
			else if ( strcmp(tempREGName,"ConnectAcadHandle") == 0 )
				strcpy(this->conAcadHandle,tempREGVal);
		}
/*		if ( bufArray[i].Find(CoordBegSearch) != -1 ) {
			tempArray.RemoveAll();
			while ( bufArray[i].Find(CoordEndSearch) == -1 ) {
				tempArray.Add(bufArray[i]);
				i++;
			}
			tempArray.Add(bufArray[i]);
			tempCoord.BuildFromStream(tempArray);
			this->coordinateList.Add(tempCoord);
		}
*/	}
	return;
}

void qqhSLOTPickPath::StreamAttributes(CExeStringArray & attributBuf) {
	CString tempBuf;

	attributBuf.Add(CString(PathBegSearch)+"\n");

	tempBuf.Format("%sDBID|%d\n", REGBegSearch, this->DBID );
	attributBuf.Add(tempBuf);

	tempBuf.Format("%sAcadHandle|%s\n", REGBegSearch, this->acadHandle );
	attributBuf.Add(tempBuf);

	tempBuf.Format("%sPathLength|%f\n", REGBegSearch, this->pathLength);
	attributBuf.Add(tempBuf);

	tempBuf.Format("%sConnectAcadHandle|%s\n", REGBegSearch, this->conAcadHandle );
	attributBuf.Add(tempBuf);

	attributBuf.Add(CString(PathEndSearch)+"\n");
	return;
}


////////////////////////////////////////
// qqhSLOTObject Member functions
////////////////////////////////////////
qqhSLOTObject::qqhSLOTObject()
{
	DBID = 0;
}

qqhSLOTObject::qqhSLOTObject(const qqhSLOTObject & other) {
	DBID = other.DBID;
	description = other.description;
	UDFList.Copy(other.UDFList);
}

////////////////////////////////////////
// qqhSLOTHotSpot Member functions
////////////////////////////////////////
qqhSLOTHotSpot::qqhSLOTHotSpot()
{
	hotSpotType = 0;
	strcpy(acadHandle,"");
}

qqhSLOTHotSpot::qqhSLOTHotSpot(const qqhSLOTHotSpot & other)
{
	coord = other.coord;
	hotSpotType = other.hotSpotType;
	strcpy(acadHandle,other.acadHandle);
	UDFList.Copy(other.UDFList);
	DBID = other.DBID;
}

qqhSLOTHotSpot& qqhSLOTHotSpot::operator=(const qqhSLOTHotSpot &other)
{
	coord = other.coord;
	hotSpotType = other.hotSpotType;
	strcpy(acadHandle,other.acadHandle);
	UDFList.Copy(other.UDFList);
	DBID = other.DBID;
	return *this;
}

void qqhSLOTHotSpot::BuildFromStream(CExeStringArray &bufArray ) {
	char tempBuf[512];
	char tempREGName[100];
	char tempREGVal[256];
	char tempheader[100];
	int i;
	CExeStringArray tempArray;
	qqhSLOTHotSpot tempAttr;
	CString tempChkStr;

	for ( i = 0; i  < bufArray.GetSize(); i++ ) {
		if ( bufArray[i].Find(REGBegSearch) != -1 ) {
			/////////////////////////////////////////////////////////////
			//  REGs are processed here
			/////////////////////////////////////////////////////////////
			strcpy(tempBuf,bufArray[i].GetBuffer(0));
			bufArray[i].ReleaseBuffer();
			tempChkStr = tempBuf;
			if (tempChkStr.GetAt(tempChkStr.GetLength()-1) == '|')
				strcat(tempBuf,"1");
			if (strchr(tempBuf,'\n') == NULL)
				strcat(tempBuf,"\n");
			bufArray[i] = tempBuf;
			strcpy(tempheader,strtok(tempBuf,"|"));
			strcpy(tempREGName,strtok(NULL,"|"));
			if (strstr(bufArray[i].GetBuffer(0),"|\n") == NULL )
				strcpy(tempREGVal,strtok(NULL,"\n"));
			else
				strcpy(tempREGVal,"");
			bufArray[i].ReleaseBuffer();
			if ( strcmp(tempREGName,"Description") == 0 )
				this->description = tempREGVal;
			else if ( strcmp(tempREGName,"DBID") == 0 )
				this->DBID = atoi(tempREGVal);
			else if ( strcmp(tempREGName,"Type") == 0 )
				this->hotSpotType = atoi(tempREGVal);
			else if ( strcmp(tempREGName,"AcadHandle") == 0 )
				strcpy(this->acadHandle,tempREGVal);
		}
	

		/////////////////////////////////////////////////////////////
		//  UDFs 
		/////////////////////////////////////////////////////////////
		if ( bufArray[i].Find(UDFBegSearch) != -1 ) {
			strcpy(tempBuf,bufArray[i].GetBuffer(0));
			bufArray[i].ReleaseBuffer();
			strcpy(tempheader,strtok(tempBuf,"|"));
			strcpy(tempheader,strtok(NULL,"\n"));
			this->UDFList.Add(tempheader);
		}

		/////////////////////////////////////////////////////////////
		//  Coordinate
		/////////////////////////////////////////////////////////////
		if ( (bufArray[i]).Find(CoordBegSearch) != -1 ) {
			tempArray.RemoveAll();
			tempArray.Add(bufArray[i]);
			i++;
			while ( (bufArray[i]).Find(CoordEndSearch) == -1 ) {
				tempArray.Add(bufArray[i]);
				i++;
			}
			tempArray.Add(bufArray[i]);
			this->coord.BuildFromStream(tempArray);
		}
	}
	return;
}

void qqhSLOTHotSpot::StreamAttributes(CExeStringArray & attributBuf) {

	CString tempBuf;
	CExeStringArray tempArray;

	attributBuf.Add(CString(HotSpotBegSearch) + "\n");

	tempBuf.Format("%sDBID|%d\n", REGBegSearch, this->DBID );
	attributBuf.Add(tempBuf);

	tempBuf.Format("%sDescription|%s\n", REGBegSearch, this->description);
	attributBuf.Add(tempBuf);

	tempBuf.Format("%sType|%d\n", REGBegSearch, this->hotSpotType);
	attributBuf.Add(tempBuf);

	tempBuf.Format("%sAcadHandle|%s\n", REGBegSearch, this->acadHandle);
	attributBuf.Add(tempBuf);

	this->coord.StreamAttributes(attributBuf);

	attributBuf.Add(CString(HotSpotEndSearch) + "\n");

	return;
}

////////////////////////////////////////
// qqhSLOTQueryAttr Member functions
////////////////////////////////////////
qqhSLOTQueryAttr::qqhSLOTQueryAttr() {
	attrName = "";
	attrValue = "";
	conjunction = "";
	queryOperator = "";
	type="SLOTQueryAttr";
}

qqhSLOTQueryAttr::qqhSLOTQueryAttr(const qqhSLOTQueryAttr & other) {
	attrName = other.attrName;
	attrValue = other.attrValue;
	conjunction = other.conjunction;
	queryOperator = other.queryOperator;
	type="SLOTQueryAttr";
	UDFList.Copy(other.UDFList);
	DBID = other.DBID;
}

qqhSLOTQueryAttr & qqhSLOTQueryAttr::operator=(const qqhSLOTQueryAttr & other) {
	attrName = other.attrName;
	attrValue = other.attrValue;
	conjunction = other.conjunction;
	queryOperator = other.queryOperator;
	type="SLOTQueryAttr";
	UDFList.Copy(other.UDFList);
	DBID = other.DBID;
	return *this;
}

void qqhSLOTQueryAttr::BuildFromStream(CExeStringArray &bufArray ) {
	char tempBuf[512];
	char tempREGName[100];
	char tempREGVal[256];
	char tempheader[100];
	int i;
	CExeStringArray tempArray;
	qqhSLOTQueryAttr tempAttr;
	CString tempChkStr;

	for ( i = 0; i  < bufArray.GetSize(); i++ ) {
		if ( bufArray[i].Find(REGBegSearch) != -1 ) {
			/////////////////////////////////////////////////////////////
			//  REGs are processed here
			/////////////////////////////////////////////////////////////
			strcpy(tempBuf,bufArray[i].GetBuffer(0));
			bufArray[i].ReleaseBuffer();
			tempChkStr = tempBuf;
			if (tempChkStr.GetAt(tempChkStr.GetLength()-1) == '|')
				strcat(tempBuf,"1");
			if (strchr(tempBuf,'\n') == NULL)
				strcat(tempBuf,"\n");
			bufArray[i] = tempBuf;
			strcpy(tempheader,strtok(tempBuf,"|"));
			strcpy(tempREGName,strtok(NULL,"|"));
			if (strstr(bufArray[i].GetBuffer(0),"|\n") == NULL )
				strcpy(tempREGVal,strtok(NULL,"\n"));
			else
				strcpy(tempREGVal,"");
			bufArray[i].ReleaseBuffer();
			if ( strcmp(tempREGName,"Description") == 0 )
				this->description = tempREGVal;
			else if ( strcmp(tempREGName,"DBID") == 0 )
				this->DBID = atoi(tempREGVal);
			else if ( strcmp(tempREGName,"AttributeName") == 0 )
				this->attrName = tempREGVal;
			else if ( strcmp(tempREGName,"AttributeValue") == 0 )
				this->attrValue = tempREGVal;
			else if ( strcmp(tempREGName,"Conjunction") == 0 )
				this->conjunction = tempREGVal;
			else if ( strcmp(tempREGName,"QueryOperator") == 0 )
				this->queryOperator = tempREGVal;
		}
	

		/////////////////////////////////////////////////////////////
		//  UDFs 
		/////////////////////////////////////////////////////////////
		if ( bufArray[i].Find(UDFBegSearch) != -1 ) {
			strcpy(tempBuf,bufArray[i].GetBuffer(0));
			bufArray[i].ReleaseBuffer();
			strcpy(tempheader,strtok(tempBuf,"|"));
			strcpy(tempheader,strtok(NULL,"\n"));
			this->UDFList.Add(tempheader);
		}
	}
	return;
}

void qqhSLOTQueryAttr::StreamAttributes(CExeStringArray & attributBuf) {

	int i;
	CString tempBuf;
	CExeStringArray tempArray;

	attributBuf.Add(CString(QueryAttrBegSearch) + "\n");

	tempBuf.Format("%sDBID|%d\n", REGBegSearch, this->DBID );
	attributBuf.Add(tempBuf);

	tempBuf.Format("%sDescription|%s\n", REGBegSearch, this->description);
	
	attributBuf.Add(tempBuf);

	tempBuf.Format("%sAttributeName|%s\n",REGBegSearch,this->attrName);
	attributBuf.Add(tempBuf);

	tempBuf.Format("%sAttributeValue|%s\n",REGBegSearch,this->attrValue);
	attributBuf.Add(tempBuf);
	
	tempBuf.Format("%sConjunction|%s\n",REGBegSearch,this->conjunction);
	attributBuf.Add(tempBuf);

	tempBuf.Format("%sQueryOperator|%s\n",REGBegSearch,this->queryOperator);
	attributBuf.Add(tempBuf);
	for (i = 0; i < UDFList.GetSize(); i++)
		attributBuf.Add(CString(CString(UDFBegSearch) + UDFList[i] + "\n"));

	attributBuf.Add(CString(QueryAttrEndSearch) + "\n");

	return;
}

////////////////////////////////////////
// qqhSLOTQuery Member functions
////////////////////////////////////////
qqhSLOTQuery::qqhSLOTQuery() {
	objName = "";
	type="SLOTQuery";
}

qqhSLOTQuery::qqhSLOTQuery(qqhSLOTQuery & other) {
	for (int i=0; i < other.attrList.GetSize();i++)
		attrList.Add(other.attrList[i]);
	objName = other.objName;
	UDFList.Copy(other.UDFList);
	type="SLOTQuery";
	DBID = other.DBID;
}

qqhSLOTQuery & qqhSLOTQuery::operator=(qqhSLOTQuery & other) {
	for (int i=0; i < other.attrList.GetSize();i++)
		attrList.Add(other.attrList[i]);
	objName = other.objName;
	type="SLOTQuery";
	UDFList.Copy(other.UDFList);
	DBID = other.DBID;
	return *this;
}

void qqhSLOTQuery::AddQueryAttr(CString pAttribute,CString pValue, CString pConjunction, CString pQueryOperator) {
	/////////////////////////////////////////////////////////////
	// builds a queryattr object and adds it to the list        //
	//////////////////////////////////////////////////////////////
	qqhSLOTQueryAttr theQueryAttr;

	if ( pValue != "" ) {
		theQueryAttr.setAttrName(pAttribute);
		theQueryAttr.setAttrValue(pValue);
		theQueryAttr.setConjunction(pConjunction);
		theQueryAttr.setQueryOperator(pQueryOperator);
		this->attrList.Add(theQueryAttr);
	}
	return;
}

void qqhSLOTQuery::BuildFromStream(CExeStringArray &bufArray ) {
	char tempBuf[512];
	char tempREGName[100];
	char tempREGVal[256];
	char tempheader[100];
	int i;
	CExeStringArray tempArray;
	qqhSLOTQueryAttr tempAttr;
	CString tempChkStr;

	for ( i = 0; i  < bufArray.GetSize(); i++ ) {
		if ( bufArray[i].Find(REGBegSearch) != -1 ) {
			/////////////////////////////////////////////////////////////
			//  REGs are processed here
			/////////////////////////////////////////////////////////////
			strcpy(tempBuf,bufArray[i].GetBuffer(0));
			bufArray[i].ReleaseBuffer();
			tempChkStr = tempBuf;
			if (tempChkStr.GetAt(tempChkStr.GetLength()-1) == '|')
				strcat(tempBuf,"1");
			if (strchr(tempBuf,'\n') == NULL)
				strcat(tempBuf,"\n");
			bufArray[i] = tempBuf;
			strcpy(tempheader,strtok(tempBuf,"|"));
			strcpy(tempREGName,strtok(NULL,"|"));
			if (strstr(bufArray[i].GetBuffer(0),"|\n") == NULL )
				strcpy(tempREGVal,strtok(NULL,"\n"));
			else
				strcpy(tempREGVal,"");
			bufArray[i].ReleaseBuffer();
			if ( strcmp(tempREGName,"Description") == 0 )
				this->description = tempREGVal;
			else if ( strcmp(tempREGName,"DBID") == 0 )
				this->DBID = atoi(tempREGVal);
			else if ( strcmp(tempREGName,"ObjectName") == 0 )
				this->objName = tempREGVal;
		}
	

		/////////////////////////////////////////////////////////////
		//  UDFs 
		/////////////////////////////////////////////////////////////
		if ( bufArray[i].Find(UDFBegSearch) != -1 ) {
			strcpy(tempBuf,bufArray[i].GetBuffer(0));
			bufArray[i].ReleaseBuffer();
			strcpy(tempheader,strtok(tempBuf,"|"));
			strcpy(tempheader,strtok(NULL,"\n"));
			this->UDFList.Add(tempheader);
		}

		if ( bufArray[i].Find(QueryAttrBegSearch) != -1  ) {
			tempArray.RemoveAll();
			tempArray.Add(bufArray[i]);
			i++;
			while ( (bufArray[i]).Find(QueryAttrEndSearch) == -1 ) {
				tempArray.Add(bufArray[i]);
				i++;
			}
			tempArray.Add(bufArray[i]);
			tempAttr.BuildFromStream(tempArray);
			this->attrList.Add(tempAttr);
		}
	}
	return;
}

void qqhSLOTQuery::StreamAttributes(CExeStringArray & attributBuf) {

	int i;
	CString tempBuf;
	CExeStringArray tempArray;

	attributBuf.Add(CString(QueryBegSearch) + "\n");


	tempBuf.Format("%sDBID|%d\n", REGBegSearch, this->DBID );
	attributBuf.Add(tempBuf);

	tempBuf.Format("%sDescription|%s\n", REGBegSearch, this->description);
	
	attributBuf.Add(tempBuf);

	tempBuf.Format("%sObjectName|%s\n",REGBegSearch,this->objName);
	attributBuf.Add(tempBuf);

	for (i = 0; i < UDFList.GetSize(); i++)
		attributBuf.Add(CString(CString(UDFBegSearch) + UDFList[i] + "\n"));
	for (i = 0; i < attrList.GetSize(); i++)
		attrList[i].StreamAttributes(attributBuf);

	attributBuf.Add(CString(QueryEndSearch) + "\n");

	return;
}

/////////////////////////////////////////
// qqhSLOTRackType member functions
/////////////////////////////////////////
qqhSLOTRackTypeLabor::qqhSLOTRackTypeLabor() {
	cube = 0.0;
	variableFactor = 0.0;
	fixedFactor = 0.0;
	relativeLevel = 0;
//	DBID = other.DBID;
	type = "SLOTRackTypeLabor";
}

qqhSLOTRackTypeLabor::qqhSLOTRackTypeLabor(const qqhSLOTRackTypeLabor & other) {
	cube = other.cube;
	variableFactor = other.variableFactor;
	fixedFactor = other.fixedFactor;
	relativeLevel = other.relativeLevel;
	DBID = other.DBID;
	description = other.description;
	UDFList.Copy(other.UDFList);
}

qqhSLOTRackTypeLabor & qqhSLOTRackTypeLabor::operator=(const qqhSLOTRackTypeLabor & other) {
	cube = other.cube;
	variableFactor = other.variableFactor;
	fixedFactor = other.fixedFactor;
	relativeLevel = other.relativeLevel;
	UDFList.Copy(other.UDFList);
	DBID = other.DBID;
	description = other.description;
	return *this;
}

void qqhSLOTRackTypeLabor::BuildFromStream(CExeStringArray &bufArray ) {
	char tempBuf[512];
	char tempREGName[100];
	char tempREGVal[256];
	char tempheader[100];
	int i;
	CExeStringArray tempArray;
	CString tempChkStr;

	for ( i = 0; i  < bufArray.GetSize(); i++ ) {
		if ( bufArray[i].Find(REGBegSearch) != -1 ) {
			/////////////////////////////////////////////////////////////
			//  REGs are processed here
			/////////////////////////////////////////////////////////////
			strcpy(tempBuf,bufArray[i].GetBuffer(0));
			bufArray[i].ReleaseBuffer();
			tempChkStr = tempBuf;
			if (tempChkStr.GetAt(tempChkStr.GetLength()-1) == '|')
				strcat(tempBuf,"1");
			if (strchr(tempBuf,'\n') == NULL)
				strcat(tempBuf,"\n");
			bufArray[i] = tempBuf;
			strcpy(tempheader,strtok(tempBuf,"|"));
			strcpy(tempREGName,strtok(NULL,"|"));
			if (strstr(bufArray[i].GetBuffer(0),"|\n") == NULL )
				strcpy(tempREGVal,strtok(NULL,"\n"));
			else
				strcpy(tempREGVal,"");
			bufArray[i].ReleaseBuffer();
			if ( strcmp(tempREGName,"Description") == 0 )
				this->description = tempREGVal;
			else if ( strcmp(tempREGName,"DBID") == 0 )
				this->DBID = atoi(tempREGVal);
			else if ( strcmp(tempREGName,"Cube") == 0 )
				this->cube = (float)atof(tempREGVal);
			else if ( strcmp(tempREGName,"VariableFactor") == 0 )
				this->variableFactor = (float)atof(tempREGVal);
			else if ( strcmp(tempREGName,"FixedFactor") == 0 )
				this->fixedFactor = (float)atof(tempREGVal);
			else if ( strcmp(tempREGName,"RelativeLevel") == 0 )
				this->relativeLevel = atoi(tempREGVal);
		}
	

		/////////////////////////////////////////////////////////////
		//  UDFs 
		/////////////////////////////////////////////////////////////
		if ( bufArray[i].Find(UDFBegSearch) != -1 ) {
			strcpy(tempBuf,bufArray[i].GetBuffer(0));
			bufArray[i].ReleaseBuffer();
			strcpy(tempheader,strtok(tempBuf,"|"));
			strcpy(tempheader,strtok(NULL,"\n"));
			this->UDFList.Add(tempheader);
		}
	}
	return;
}

void qqhSLOTRackTypeLabor::StreamAttributes(CExeStringArray & attributBuf) {

	int i;
	CString tempBuf;

	attributBuf.Add(CString(RackTypeLaborBegSearch) + "\n");


	tempBuf.Format("%sDBID|%d\n", REGBegSearch, this->DBID );
	attributBuf.Add(tempBuf);

	tempBuf.Format("%sDescription|%s\n", REGBegSearch, this->description);
	attributBuf.Add(tempBuf);

	tempBuf.Format("%sCube|%f\n", REGBegSearch, this->cube);
	attributBuf.Add(tempBuf);

	tempBuf.Format("%sFixedFactor|%f\n", REGBegSearch, this->fixedFactor);
	attributBuf.Add(tempBuf);

	tempBuf.Format("%sVariableFactor|%f\n", REGBegSearch, this->variableFactor);
	attributBuf.Add(tempBuf);

	tempBuf.Format("%sRelativeLevel|%d\n", REGBegSearch, this->relativeLevel);
	attributBuf.Add(tempBuf);

	for (i = 0; i < UDFList.GetSize(); i++)
		attributBuf.Add(CString(CString(UDFBegSearch) + UDFList[i] + "\n"));
	
	attributBuf.Add(CString(RackTypeLaborEndSearch) + "\n");

	return;
}
/////////////////////////////////////////
// qqhSLOTRackType member functions
/////////////////////////////////////////

qqhSLOTRackType::qqhSLOTRackType() {
	cost = 0.0;
	cube = 0.0;
	region = 0;
	units = 0;
	type = "SLOTRackType";
}

qqhSLOTRackType::qqhSLOTRackType(const qqhSLOTRackType & other) {
	cost = other.cost;
	cube = other.cube;
	region = other.region;
	units = other.units;
	rackTypeGroup = other.rackTypeGroup;
	DBID = other.DBID;
	description = other.description;
	UDFList.Copy(other.UDFList);
}

qqhSLOTRackType & qqhSLOTRackType::operator=(const qqhSLOTRackType & other) {
	cost = other.cost;
	cube = other.cube;
	region = other.region;
	units = other.units;
	rackTypeGroup = other.rackTypeGroup;
	UDFList.Copy(other.UDFList);
	DBID = other.DBID;
	description = other.description;
	return *this;
}

void qqhSLOTRackType::BuildFromStream(CExeStringArray &bufArray ) {
	char tempBuf[512];
	char tempREGName[100];
	char tempREGVal[256];
	char tempheader[100];
	int i;
	CExeStringArray tempArray;
	CString tempChkStr;

	for ( i = 0; i  < bufArray.GetSize(); i++ ) {
		if ( bufArray[i].Find(REGBegSearch) != -1 ) {
			/////////////////////////////////////////////////////////////
			//  REGs are processed here
			/////////////////////////////////////////////////////////////
			strcpy(tempBuf,bufArray[i].GetBuffer(0));
			bufArray[i].ReleaseBuffer();
			tempChkStr = tempBuf;
			if (tempChkStr.GetAt(tempChkStr.GetLength()-1) == '|')
				strcat(tempBuf,"1");
			if (strchr(tempBuf,'\n') == NULL)
				strcat(tempBuf,"\n");
			bufArray[i] = tempBuf;
			strcpy(tempheader,strtok(tempBuf,"|"));
			strcpy(tempREGName,strtok(NULL,"|"));
			if (strstr(bufArray[i].GetBuffer(0),"|\n") == NULL )
				strcpy(tempREGVal,strtok(NULL,"\n"));
			else
				strcpy(tempREGVal,"");
			bufArray[i].ReleaseBuffer();
			if ( strcmp(tempREGName,"Description") == 0 )
				this->description = tempREGVal;
			else if ( strcmp(tempREGName,"DBID") == 0 )
				this->DBID = atoi(tempREGVal);
			else if ( strcmp(tempREGName,"Cost") == 0 )
				this->cost = (float)atof(tempREGVal);
			else if ( strcmp(tempREGName,"Cube") == 0 )
				this->cube = (float)atof(tempREGVal);
			else if ( strcmp(tempREGName,"Units") == 0 )
				this->units = atoi(tempREGVal);
			else if ( strcmp(tempREGName,"Region") == 0 )
				this->region = atoi(tempREGVal);
		}
	

		/////////////////////////////////////////////////////////////
		//  UDFs 
		/////////////////////////////////////////////////////////////
		if ( bufArray[i].Find(UDFBegSearch) != -1 ) {
			strcpy(tempBuf,bufArray[i].GetBuffer(0));
			bufArray[i].ReleaseBuffer();
			strcpy(tempheader,strtok(tempBuf,"|"));
			strcpy(tempheader,strtok(NULL,"\n"));
			this->UDFList.Add(tempheader);
		}

		/////////////////////////////////////////////////////////////
		//  RackTypeGroup
		/////////////////////////////////////////////////////////////
		if ( bufArray[i].Find(RackTypeGroupBegSearch) != -1  ) {
			tempArray.RemoveAll();
			tempArray.Add(bufArray[i]);
			i++;
			while ( (bufArray[i].Find(RackTypeGroupEndSearch) == -1 ) ) {
				tempArray.Add(bufArray[i]);
				i++;
			}
			tempArray.Add(bufArray[i]);
			rackTypeGroup.BuildFromStream(tempArray);
		}
	}
	return;
}

void qqhSLOTRackType::StreamAttributes(CExeStringArray & attributBuf) {

	int i;
	CString tempBuf;

	attributBuf.Add(CString(RackTypeBegSearch) + "\n");


	tempBuf.Format("%sDBID|%d\n", REGBegSearch, this->DBID );
	attributBuf.Add(tempBuf);

	tempBuf.Format("%sDescription|%s\n", REGBegSearch, this->description);
	attributBuf.Add(tempBuf);

	tempBuf.Format("%sCost|%f\n", REGBegSearch, this->cost);
	attributBuf.Add(tempBuf);

	tempBuf.Format("%sCube|%f\n", REGBegSearch, this->cube);
	attributBuf.Add(tempBuf);

	tempBuf.Format("%sRegion|%d\n", REGBegSearch, this->region);
	attributBuf.Add(tempBuf);

	tempBuf.Format("%sUnits|%d\n", REGBegSearch, this->units);
	attributBuf.Add(tempBuf);

	for (i = 0; i < UDFList.GetSize(); i++)
		attributBuf.Add(CString(CString(UDFBegSearch) + UDFList[i] + "\n"));
	
	rackTypeGroup.StreamAttributes(attributBuf);

	attributBuf.Add(CString(RackTypeEndSearch) + "\n");

	return;
}

/////////////////////////////////////////
// RackTypeUsage member functions
/////////////////////////////////////////
qqhSLOTRackTypeUsage::qqhSLOTRackTypeUsage() {
	extendedBOH = 0.0;
	extendedCube = 0.0;
	facings = 0;
	type = "SLOTRackTypeUsage";
}

qqhSLOTRackTypeUsage::qqhSLOTRackTypeUsage(const qqhSLOTRackTypeUsage & other) {
	extendedBOH = other.extendedBOH;
	extendedCube = other.extendedCube;
	facings = other.facings;
	rackType = other.rackType;
	type = "SLOTRackTypeUsage";
	DBID = other.DBID;
	description = other.description;
	UDFList.Copy(other.UDFList);
}

qqhSLOTRackTypeUsage & qqhSLOTRackTypeUsage::operator=(const qqhSLOTRackTypeUsage & other) {
	extendedBOH = other.extendedBOH;
	extendedCube = other.extendedCube;
	facings = other.facings;
	rackType = other.rackType;
	type = "SLOTRackTypeUsage";
	DBID = other.DBID;
	description = other.description;
	UDFList.Copy(other.UDFList);
	return *this;
}

void qqhSLOTRackTypeUsage::BuildFromStream(CExeStringArray &bufArray ) {
	char tempBuf[512];
	char tempREGName[100];
	char tempREGVal[256];
	char tempheader[100];
	int i;
	CExeStringArray tempArray;
	CString tempChkStr;


	for ( i = 0; i  < bufArray.GetSize(); i++ ) {
		if ( bufArray[i].Find(REGBegSearch) != -1 ) {
			/////////////////////////////////////////////////////////////
			//  REGs are processed here
			/////////////////////////////////////////////////////////////
			strcpy(tempBuf,bufArray[i].GetBuffer(0));
			bufArray[i].ReleaseBuffer();
			tempChkStr = tempBuf;
			if (tempChkStr.GetAt(tempChkStr.GetLength()-1) == '|')
				strcat(tempBuf,"1");
			if (strchr(tempBuf,'\n') == NULL)
				strcat(tempBuf,"\n");
			bufArray[i] = tempBuf;
			strcpy(tempheader,strtok(tempBuf,"|"));
			strcpy(tempREGName,strtok(NULL,"|"));
			if (strstr(bufArray[i].GetBuffer(0),"|\n") == NULL )
				strcpy(tempREGVal,strtok(NULL,"\n"));
			else
				strcpy(tempREGVal,"");
			bufArray[i].ReleaseBuffer();
			if ( strcmp(tempREGName,"Description") == 0 )
				this->description = tempREGVal;
			else if ( strcmp(tempREGName,"DBID") == 0 )
				this->DBID = atoi(tempREGVal);
			else if ( strcmp(tempREGName,"ExtendedBOH") == 0 )
				this->extendedBOH = (float)atof(tempREGVal);
			else if ( strcmp(tempREGName,"ExtendedCube") == 0 )
				this->extendedCube = (float)atof(tempREGVal);
			else if ( strcmp(tempREGName,"Facings") == 0 )
				this->facings = atoi(tempREGVal);
		}
	

		/////////////////////////////////////////////////////////////
		//  UDFs 
		/////////////////////////////////////////////////////////////
		if ( bufArray[i].Find(UDFBegSearch) != -1 ) {
			strcpy(tempBuf,bufArray[i].GetBuffer(0));
			bufArray[i].ReleaseBuffer();
			strcpy(tempheader,strtok(tempBuf,"|"));
			strcpy(tempheader,strtok(NULL,"\n"));
			this->UDFList.Add(tempheader);
		}

		/////////////////////////////////////////////////////////////
		//  RackType
		/////////////////////////////////////////////////////////////
		if ( bufArray[i].Find(RackTypeBegSearch) != -1 &&
			 bufArray[i].Find(RackTypeUsageBegSearch) == -1 ) {
			tempArray.RemoveAll();
			tempArray.Add(bufArray[i]);
			i++;
			while ( (bufArray[i].Find(RackTypeEndSearch) == -1 ) ) {
				tempArray.Add(bufArray[i]);
				i++;
			}
			tempArray.Add(bufArray[i]);
			rackType.BuildFromStream(tempArray);
		}
	}
	return;
}

void qqhSLOTRackTypeUsage::StreamAttributes(CExeStringArray & attributBuf) {

	int i;
	CString tempBuf;

	attributBuf.Add(CString(RackTypeUsageBegSearch) + "\n");


	tempBuf.Format("%sDBID|%d\n", REGBegSearch, this->DBID );
	attributBuf.Add(tempBuf);

	tempBuf.Format("%sDescription|%s\n", REGBegSearch, this->description);
	attributBuf.Add(tempBuf);

	tempBuf.Format("%sExtendedBOH|%f\n", REGBegSearch, this->extendedBOH);
	attributBuf.Add(tempBuf);

	tempBuf.Format("%sExtendedCube|%f\n", REGBegSearch, this->extendedCube);
	attributBuf.Add(tempBuf);

	tempBuf.Format("%sFacings|%d\n", REGBegSearch, this->facings);
	attributBuf.Add(tempBuf);

	for (i = 0; i < UDFList.GetSize(); i++)
		attributBuf.Add(CString(CString(UDFBegSearch) + UDFList[i] + "\n"));
	
	rackType.StreamAttributes(attributBuf);

	attributBuf.Add(CString(RackTypeUsageEndSearch) + "\n");

	return;
}

/////////////////////////////////////////
// RackTypeGroup member functions
/////////////////////////////////////////

qqhSLOTRackTypeGroup::qqhSLOTRackTypeGroup() {
	calculationMethod = 0;
	reserveUsage = 0.0;
//	DBID = other.DBID;
	type = "SLOTRackTypeGroup";
}

qqhSLOTRackTypeGroup::qqhSLOTRackTypeGroup(const qqhSLOTRackTypeGroup & other) {
	calculationMethod = other.calculationMethod;
	reserveUsage = other.reserveUsage;
	type = "SLOTRackTypeGroup";
	DBID = other.DBID;
	description = other.description;
	UDFList.Copy(other.UDFList);
}

qqhSLOTRackTypeGroup & qqhSLOTRackTypeGroup::operator=(const qqhSLOTRackTypeGroup & other) {
	calculationMethod = other.calculationMethod;
	reserveUsage = other.reserveUsage;
	UDFList.Copy(other.UDFList);
	type = "SLOTRackTypeGroup";
	DBID = other.DBID;
	description = other.description;
	return *this;
}

void qqhSLOTRackTypeGroup::BuildFromStream(CExeStringArray &bufArray ) {
	char tempBuf[512];
	char tempREGName[100];
	char tempREGVal[256];
	char tempheader[100];
	int i;
	CExeStringArray tempArray;
	CString tempChkStr;

	for ( i = 0; i  < bufArray.GetSize(); i++ ) {
		if ( bufArray[i].Find(REGBegSearch) != -1 ) {
			/////////////////////////////////////////////////////////////
			//  REGs are processed here
			/////////////////////////////////////////////////////////////
			strcpy(tempBuf,bufArray[i].GetBuffer(0));
			bufArray[i].ReleaseBuffer();
			tempChkStr = tempBuf;
			if (tempChkStr.GetAt(tempChkStr.GetLength()-1) == '|')
				strcat(tempBuf,"1");
			if (strchr(tempBuf,'\n') == NULL)
				strcat(tempBuf,"\n");
			bufArray[i] = tempBuf;
			strcpy(tempheader,strtok(tempBuf,"|"));
			strcpy(tempREGName,strtok(NULL,"|"));
			if (strstr(bufArray[i].GetBuffer(0),"|\n") == NULL )
				strcpy(tempREGVal,strtok(NULL,"\n"));
			else
				strcpy(tempREGVal,"");
			bufArray[i].ReleaseBuffer();
			if ( strcmp(tempREGName,"Description") == 0 )
				this->description = tempREGVal;
			else if ( strcmp(tempREGName,"DBID") == 0 )
				this->DBID = atoi(tempREGVal);
			else if ( strcmp(tempREGName,"CalculationMethod") == 0 )
				this->calculationMethod = atoi(tempREGVal);
			else if ( strcmp(tempREGName,"ReserveUsage") == 0 )
				this->reserveUsage = (float)atof(tempREGVal);
		}
	

		/////////////////////////////////////////////////////////////
		//  UDFs 
		/////////////////////////////////////////////////////////////
		if ( bufArray[i].Find(UDFBegSearch) != -1 ) {
			strcpy(tempBuf,bufArray[i].GetBuffer(0));
			bufArray[i].ReleaseBuffer();
			strcpy(tempheader,strtok(tempBuf,"|"));
			strcpy(tempheader,strtok(NULL,"\n"));
			this->UDFList.Add(tempheader);
		}

	}
	return;
}

void qqhSLOTRackTypeGroup::StreamAttributes(CExeStringArray & attributBuf) {

	int i;
	CString tempBuf;

	attributBuf.Add(CString(RackTypeGroupBegSearch) + "\n");


	tempBuf.Format("%sDBID|%d\n", REGBegSearch, this->DBID );
	attributBuf.Add(tempBuf);

	tempBuf.Format("%sDescription|%s\n", REGBegSearch, this->description);
	attributBuf.Add(tempBuf);

	tempBuf.Format("%sCalculationMethod|%d\n", REGBegSearch, this->calculationMethod);
	attributBuf.Add(tempBuf);

	tempBuf.Format("%sReserveUsage|%f\n", REGBegSearch, this->reserveUsage);
	attributBuf.Add(tempBuf);

	for (i = 0; i < UDFList.GetSize(); i++)
		attributBuf.Add(CString(CString(UDFBegSearch) + UDFList[i] + "\n"));
	
	attributBuf.Add(CString(RackTypeGroupEndSearch) + "\n");

	return;
}

/////////////////////////////////////////
// SlotGroupBay member functions
/////////////////////////////////////////
qqhSLOTGroupBay::qqhSLOTGroupBay() {
	type = "SLOTGroupBay";

}

qqhSLOTGroupBay::qqhSLOTGroupBay(const qqhSLOTGroupBay & other) {
	slottingGroup = other.slottingGroup;
	Bay = other.Bay;
	UDFList.Copy(other.UDFList);
	DBID = other.DBID;
	description = other.description;
}

qqhSLOTGroupBay & qqhSLOTGroupBay::operator=(const qqhSLOTGroupBay & other) {
	slottingGroup = other.slottingGroup;
	Bay = other.Bay;
	UDFList.Copy(other.UDFList);
	DBID = other.DBID;
	description = other.description;
	return *this;
}

void qqhSLOTGroupBay::BuildFromStream(CExeStringArray &bufArray ) {
	char tempBuf[512];
	char tempREGName[100];
	char tempREGVal[256];
	char tempheader[100];
	int i;
	CExeStringArray tempArray;
	CString tempChkStr;

	for ( i = 0; i  < bufArray.GetSize(); i++ ) {
		if ( bufArray[i].Find(REGBegSearch) != -1 ) {
			/////////////////////////////////////////////////////////////
			//  REGs are processed here
			/////////////////////////////////////////////////////////////
			strcpy(tempBuf,bufArray[i].GetBuffer(0));
			bufArray[i].ReleaseBuffer();
			tempChkStr = tempBuf;
			if (tempChkStr.GetAt(tempChkStr.GetLength()-1) == '|')
				strcat(tempBuf,"1");
			if (strchr(tempBuf,'\n') == NULL)
				strcat(tempBuf,"\n");
			bufArray[i] = tempBuf;
			strcpy(tempheader,strtok(tempBuf,"|"));
			strcpy(tempREGName,strtok(NULL,"|"));
			if (strstr(bufArray[i].GetBuffer(0),"|\n") == NULL )
				strcpy(tempREGVal,strtok(NULL,"\n"));
			else
				strcpy(tempREGVal,"");
			bufArray[i].ReleaseBuffer();
			if ( strcmp(tempREGName,"Description") == 0 )
				this->description = tempREGVal;
			else if ( strcmp(tempREGName,"DBID") == 0 )
				this->DBID = atoi(tempREGVal);
		}
	

		/////////////////////////////////////////////////////////////
		//  UDFs 
		/////////////////////////////////////////////////////////////
		if ( bufArray[i].Find(UDFBegSearch) != -1 ) {
			strcpy(tempBuf,bufArray[i].GetBuffer(0));
			bufArray[i].ReleaseBuffer();
			strcpy(tempheader,strtok(tempBuf,"|"));
			strcpy(tempheader,strtok(NULL,"\n"));
			this->UDFList.Add(tempheader);
		}

		/////////////////////////////////////////////////////////////
		//  Bay
		/////////////////////////////////////////////////////////////
		if ( bufArray[i].Find(BayBegSearch) != -1  ) {
			tempArray.RemoveAll();
			tempArray.Add(bufArray[i]);
			i++;
			while ( (bufArray[i].Find(BayEndSearch) == -1 ) ) {
				tempArray.Add(bufArray[i]);
				i++;
			}
			tempArray.Add(bufArray[i]);
			Bay.BuildFromStream(tempArray);
		}
		/////////////////////////////////////////////////////////////
		//  SlottingGroup
		/////////////////////////////////////////////////////////////
		if ( bufArray[i].Find(SlotGroupBegSearch) != -1  ) {
			tempArray.RemoveAll();
			tempArray.Add(bufArray[i]);
			i++;
			while ( (bufArray[i].Find(SlotGroupEndSearch) == -1 ) ) {
				tempArray.Add(bufArray[i]);
				i++;
			}
			tempArray.Add(bufArray[i]);
			slottingGroup.BuildFromStream(tempArray);
		}
	}
	return;
}

void qqhSLOTGroupBay::StreamAttributes(CExeStringArray & attributBuf) {

	int i;
	CString tempBuf;

	attributBuf.Add(CString(GroupBayBegSearch) + "\n");


	tempBuf.Format("%sDBID|%d\n", REGBegSearch, this->DBID );
	attributBuf.Add(tempBuf);

	tempBuf.Format("%sDescription|%s\n", REGBegSearch, this->description);
	attributBuf.Add(tempBuf);

	for (i = 0; i < UDFList.GetSize(); i++)
		attributBuf.Add(CString(CString(UDFBegSearch) + UDFList[i] + "\n"));
	
	slottingGroup.StreamAttributes(attributBuf);
	Bay.StreamAttributes(attributBuf);

	attributBuf.Add(CString(GroupBayEndSearch) + "\n");

	return;
}


/////////////////////////////////////////
// qqhSLOTProductContainer member
// functions
////////////////////////////////////////
qqhSLOTProductContainer::qqhSLOTProductContainer() {
	width = 0.0;
	depth = 0.0;
	height = 0.0;
	isDepthOverride = 0;
	isWidthOverride = 0;
	isHeightOverride = 0;
	storageTI = storageHI = 0;
	type = "SLOTProductContainer";
}

qqhSLOTProductContainer::qqhSLOTProductContainer(const qqhSLOTProductContainer & other) {
	width = other.width;
	depth = other.depth;
	height = other.height;
	isDepthOverride = other.isDepthOverride;
	isWidthOverride = other.isWidthOverride;
	isHeightOverride = other.isHeightOverride;
	storageTI = storageHI = 0;
	type = "SLOTProductContainer";
	DBID = other.DBID;
	description = other.description;
	UDFList.Copy(other.UDFList);
}

qqhSLOTProductContainer& qqhSLOTProductContainer::operator=(const qqhSLOTProductContainer & other) {
	width = other.width;
	depth = other.depth;
	height = other.height;
	isDepthOverride = other.isDepthOverride;
	isWidthOverride = other.isWidthOverride;
	isHeightOverride = other.isHeightOverride;
	storageTI = other.storageTI;
	storageHI = other.storageHI;
	type = "SLOTProductContainer";
	UDFList.Copy(other.UDFList);
	DBID = other.DBID;
	description = other.description;
	return *this;
}

void qqhSLOTProductContainer::BuildFromStream(CExeStringArray &bufArray ) {
	char tempBuf[512];
	char tempREGName[100];
	char tempREGVal[256];
	char tempheader[100];
	int i;
	float tempfloat;
	CExeStringArray tempArray;
	CString tempChkStr;

	for ( i = 0; i  < bufArray.GetSize(); i++ ) {
		if ( bufArray[i].Find(REGBegSearch) != -1 ) {
			/////////////////////////////////////////////////////////////
			//  REGs are processed here
			/////////////////////////////////////////////////////////////
			strcpy(tempBuf,bufArray[i].GetBuffer(0));
			bufArray[i].ReleaseBuffer();
			tempChkStr = tempBuf;
			if (tempChkStr.GetAt(tempChkStr.GetLength()-1) == '|')
				strcat(tempBuf,"1");
			if (strchr(tempBuf,'\n') == NULL)
				strcat(tempBuf,"\n");
			bufArray[i] = tempBuf;
			strcpy(tempheader,strtok(tempBuf,"|"));
			strcpy(tempREGName,strtok(NULL,"|"));
			if (strstr(bufArray[i].GetBuffer(0),"|\n") == NULL )
				strcpy(tempREGVal,strtok(NULL,"\n"));
			else
				strcpy(tempREGVal,"");
			bufArray[i].ReleaseBuffer();
			if ( strcmp(tempREGName,"Description") == 0 )
				this->description = tempREGVal;
			else if ( strcmp(tempREGName,"IsWidthOverride") == 0 ) {
				if (strcmp(tempREGVal,"TRUE") == 0)
					this->isWidthOverride = 1;
				else
					this->isWidthOverride = 0;
			}
			else if ( strcmp(tempREGName,"IsDepthOverride") == 0 ||
					  strcmp(tempREGName,"IsLengthOverride") == 0 ) {
				if (strcmp(tempREGVal,"TRUE") == 0)
					this->isDepthOverride = 1;
				else
					this->isDepthOverride = 0;
			}
			else if ( strcmp(tempREGName,"IsHeightOverride") == 0 ) {
				if (strcmp(tempREGVal,"TRUE") == 0)
					this->isHeightOverride = 1;
				else
					this->isHeightOverride = 0;
			}
			else if ( strcmp(tempREGName,"StorageTI") == 0 )
				this->storageTI = atoi(tempREGVal);
			else if ( strcmp(tempREGName,"StorageHI") == 0 )
				this->storageHI = atoi(tempREGVal);
			else if ( strcmp(tempREGName,"DBID") == 0 )
				this->DBID = atoi(tempREGVal);
		}
	

		/////////////////////////////////////////////////////////////
		//  UDFs 
		/////////////////////////////////////////////////////////////
		if ( bufArray[i].Find(UDFBegSearch) != -1 ) {
			strcpy(tempBuf,bufArray[i].GetBuffer(0));
			bufArray[i].ReleaseBuffer();
			strcpy(tempheader,strtok(tempBuf,"|"));
			strcpy(tempheader,strtok(NULL,"\n"));
			this->UDFList.Add(tempheader);
		}

		/////////////////////////////////////////////////////////////
		//  Dimension
		/////////////////////////////////////////////////////////////
		if ( bufArray[i].Find(DimenBegSearch) != -1  ) {
			i++;
			while ( (bufArray[i].Find(DimenEndSearch) == -1 ) ) {
				strcpy(tempBuf,bufArray[i]);
				strcpy(tempheader,strtok(tempBuf,"|"));
				strcpy(tempREGName,strtok(NULL,"|"));
				strcpy(tempREGVal,strtok(NULL,"\n"));
				if ( strcmp(tempREGName,"Width") == 0 ||
					 strcmp(tempREGName,"ContainerWidth") == 0 )
				{
					tempfloat = (float) (atof(tempREGVal));
					this->width = tempfloat;
				}
				else if ( strcmp(tempREGName,"Depth") == 0 ||
						  strcmp(tempREGName,"ContainerLength") == 0 )
				{
					tempfloat = (float) (atof(tempREGVal));
					this->depth = tempfloat;
				}
				else if ( strcmp(tempREGName,"Height") == 0 ||
						  strcmp(tempREGName,"ContainerHeight") == 0 )
				{
					tempfloat = (float) (atof(tempREGVal));
					this->height = tempfloat;
				}
				i++;
			}
		}
	}
	return;
}

void qqhSLOTProductContainer::StreamAttributes(CExeStringArray & attributBuf) {

	int i;
	CString tempBuf;

	attributBuf.Add(CString(ContainerBegSearch) + "\n");


	tempBuf.Format("%sDBID|%d\n", REGBegSearch, this->DBID );
	attributBuf.Add(tempBuf);

	tempBuf.Format("%sDescription|%s\n", REGBegSearch, this->description);
	
	attributBuf.Add(tempBuf);

	if (this->isWidthOverride == 1)
		tempBuf.Format("%sIsWidthOverride|TRUE\n",REGBegSearch);
	else	
		tempBuf.Format("%sIsWidthOverride|FALSE\n",REGBegSearch);
	attributBuf.Add(tempBuf);

	if (this->isDepthOverride == 1)
		tempBuf.Format("%sIsDepthOverride|TRUE\n",REGBegSearch);
	else	
		tempBuf.Format("%sIsDepthOverride|FALSE\n",REGBegSearch);
	attributBuf.Add(tempBuf);

	if (this->isHeightOverride == 1)
		tempBuf.Format("%sIsHeightOverride|TRUE\n",REGBegSearch);
	else	
		tempBuf.Format("%sIsHeightOverride|FALSE\n",REGBegSearch);
	attributBuf.Add(tempBuf);

	tempBuf.Format("%sStorageTI|%d\n", REGBegSearch, this->storageTI );
	attributBuf.Add(tempBuf);

	tempBuf.Format("%sStorageHI|%d\n", REGBegSearch, this->storageHI );
	attributBuf.Add(tempBuf);

	attributBuf.Add(CString(DimenBegSearch)+CString("\n"));
	tempBuf.Format("%sWidth|%f\n", REGBegSearch, this->width);
	
	attributBuf.Add(tempBuf);

	tempBuf.Format("%sDepth|%f\n", REGBegSearch, this->depth);
	
	attributBuf.Add(tempBuf);

	tempBuf.Format("%sHeight|%f\n", REGBegSearch, this->height);
	
	attributBuf.Add(tempBuf);
	
	attributBuf.Add(CString(DimenEndSearch)+CString("\n"));

	
	for (i = 0; i < UDFList.GetSize(); i++)
		attributBuf.Add(CString(CString(UDFBegSearch) + UDFList[i] + "\n"));
	
	attributBuf.Add(CString(ContainerEndSearch) + "\n");

	return;
}

/////////////////////////////////////////
// qqhSLOTProductPack Member functions
////////////////////////////////////////
qqhSLOTProductPack::qqhSLOTProductPack() {
	cube = width = depth = height = movement = weight = 0.0;
	totalSelectionQty = 0;
	numberInPallet = 0;
	type = "SLOTProductPack";
	assignmentLocked = 0;
	isHazard = isPickBelt = "FALSE";
	rotatedWidth = rotatedDepth = rotatedHeight =  0.0;
	rotateXAxis = rotateYAxis = rotateZAxis = "FALSE";
	maxStackNumber = 0;
}

qqhSLOTProductPack::qqhSLOTProductPack(qqhSLOTProductPack & other) {
	cube = other.cube;
	width = other.width;
	depth = other.depth;
	height = other.height;
	movement = other.movement;
	weight = other.weight;
	description = other.description;
	DBID = other.DBID;
	isHazard = other.isHazard;
	isPickBelt = other.isPickBelt;
	balanceOnHand = other.balanceOnHand;
	optimizeBy = other.optimizeBy;
	unitOfIssue = other.unitOfIssue;
	containerList.Copy(other.containerList);
	numberInPallet = other.numberInPallet;
	UDFList.Copy(other.UDFList);
	type = "SLOTProductPack";
	assignmentLocked = other.assignmentLocked;
	rotatedWidth = other.rotatedWidth;
	rotatedDepth = other.rotatedDepth;
	rotatedHeight = other.rotatedHeight;
	rotateXAxis = other.rotateXAxis;
	rotateYAxis = other.rotateYAxis;
	rotateZAxis = other.rotateZAxis;
	maxStackNumber = other.maxStackNumber;
}

qqhSLOTProductPack& qqhSLOTProductPack::operator=(qqhSLOTProductPack & other) {
	cube = other.cube;
	width = other.width;
	depth = other.depth;
	height = other.height;
	movement = other.movement;
	weight = other.weight;
	containerList.Copy(other.containerList);
	description = other.description;
	DBID = other.DBID;
	isHazard = other.isHazard;
	isPickBelt = other.isPickBelt;
	balanceOnHand = other.balanceOnHand;
	optimizeBy = other.optimizeBy;
	unitOfIssue = other.unitOfIssue;
	type = "SLOTProductPack";
	numberInPallet = other.numberInPallet;
	UDFList.Copy(other.UDFList);
	assignmentLocked = other.assignmentLocked;
	rotatedWidth = other.rotatedWidth;
	rotatedDepth = other.rotatedDepth;
	rotatedHeight = other.rotatedHeight;
	rotateXAxis = other.rotateXAxis;
	rotateYAxis = other.rotateYAxis;
	rotateZAxis = other.rotateZAxis;
	maxStackNumber = other.maxStackNumber;
	return *this;
}

void qqhSLOTProductPack::BuildFromStream(CExeStringArray &bufArray ) {
	char tempBuf[512];
	char tempREGName[100];
	char tempREGVal[256];
	char tempheader[100];
	int i;
	float tempfloat;
	CExeStringArray tempArray;
	qqhSLOTProductContainer tempContainer;
	CString tempChkStr;

	for ( i = 0; i  < bufArray.GetSize(); i++ ) {
		if ( bufArray[i].Find(REGBegSearch) != -1 ) {
			/////////////////////////////////////////////////////////////
			//  REGs are processed here
			/////////////////////////////////////////////////////////////
			strcpy(tempBuf,bufArray[i].GetBuffer(0));
			bufArray[i].ReleaseBuffer();
			tempChkStr = tempBuf;
			if (tempChkStr.GetAt(tempChkStr.GetLength()-1) == '|')
				strcat(tempBuf,"1");
			if (strchr(tempBuf,'\n') == NULL)
				strcat(tempBuf,"\n");
			bufArray[i] = tempBuf;
			strcpy(tempheader,strtok(tempBuf,"|"));
			strcpy(tempREGName,strtok(NULL,"|"));
			if (strstr(bufArray[i].GetBuffer(0),"|\n") == NULL )
				strcpy(tempREGVal,strtok(NULL,"\n"));
			else
				strcpy(tempREGVal,"");
			bufArray[i].ReleaseBuffer();
			if ( strcmp(tempREGName,"Description") == 0 )
				this->description = tempREGVal;
			else if ( strcmp(tempREGName,"DBID") == 0 )
				this->DBID = atoi(tempREGVal);
			else if ( strcmp(tempREGName,"Cube") == 0 )
				this->cube = (float)atof(tempREGVal);
			else if ( strcmp(tempREGName,"Movement") == 0 )
				this->movement = (float)atof(tempREGVal);
			else if ( strcmp(tempREGName,"Weight") == 0 )
				this->weight = (float)atof(tempREGVal);
			else if ( strcmp(tempREGName,"TotalSelectionQty") == 0 )
				this->totalSelectionQty = atoi(tempREGVal);
			else if ( strcmp(tempREGName,"IsPickToBelt") == 0 )
				this->isPickBelt = tempREGVal;
			else if ( strcmp(tempREGName,"IsHazard") == 0 )
				this->isHazard = tempREGVal;
			else if ( strcmp(tempREGName,"BalanceOnHand") == 0 )
				this->balanceOnHand = atoi(tempREGVal);
			else if ( strcmp(tempREGName,"UnitOfIssue") == 0 )
				this->unitOfIssue = atoi(tempREGVal);
			else if ( strcmp(tempREGName,"OptimizeBy") == 0 )
				this->optimizeBy = atoi(tempREGVal);
			else if ( strcmp(tempREGName,"NumberInPallet") == 0 )
				this->numberInPallet = atoi(tempREGVal);
			else if ( strcmp(tempREGName,"IsAssignmentLocked") == 0 ) {
				if (strcmp(tempREGVal,"TRUE") == 0)
					this->assignmentLocked = 1;
				else
					this->assignmentLocked = 0;
			}

			else if ( strcmp(tempREGName,"RotateXAxis") == 0 )
				this->rotateXAxis = tempREGVal;
			else if ( strcmp(tempREGName,"RotateYAxis") == 0 )
				this->rotateYAxis = tempREGVal;
			else if ( strcmp(tempREGName,"RotateZAxis") == 0 )
				this->rotateZAxis = tempREGVal;
			/* not in 1.1
			else if ( strcmp(tempREGName,"RotatedWidth") == 0 )
				this->rotatedWidth = (float)atof(tempREGVal);
			else if ( strcmp(tempREGName,"RotatedDepth") == 0 )
				this->rotatedDepth = (float)atof(tempREGVal);
			else if ( strcmp(tempREGName,"RotatedHeight") == 0 )
				this->rotatedHeight = (float)atof(tempREGVal);
			else if ( strcmp(tempREGName,"MaxStackNumber") == 0 )
				this->maxStackNumber = atoi(tempREGVal);
			*/
		}
	

		/////////////////////////////////////////////////////////////
		//  UDFs 
		/////////////////////////////////////////////////////////////
		if ( bufArray[i].Find(UDFBegSearch) != -1 ) {
			strcpy(tempBuf,bufArray[i].GetBuffer(0));
			bufArray[i].ReleaseBuffer();
			strcpy(tempheader,strtok(tempBuf,"|"));
			strcpy(tempheader,strtok(NULL,"\n"));
			this->UDFList.Add(tempheader);
		}

		/////////////////////////////////////////////////////////////
		//  Dimension
		/////////////////////////////////////////////////////////////
		if ( bufArray[i].Find(DimenBegSearch) != -1  && bufArray[i].Find("Rotated") == -1) {
			i++;
			while ( (bufArray[i].Find(DimenEndSearch) == -1 ) ) {
				strcpy(tempBuf,bufArray[i]);
				strcpy(tempheader,strtok(tempBuf,"|"));
				strcpy(tempREGName,strtok(NULL,"|"));
				strcpy(tempREGVal,strtok(NULL,"\n"));
				if ( strcmp(tempREGName,"Width") == 0 ) {
					tempfloat = (float) (atof(tempREGVal));
					this->width = tempfloat;
				}
				else if ( strcmp(tempREGName,"Length") == 0 ) {
					tempfloat = (float) (atof(tempREGVal));
					this->depth = tempfloat;
				}
				else if ( strcmp(tempREGName,"Height") == 0 ) {
					tempfloat = (float) (atof(tempREGVal));
					this->height = tempfloat;
				}
				i++;
			}
		}
		if ( bufArray[i].Find(ContainerBegSearch) != -1  ) {
			tempArray.RemoveAll();
			tempArray.Add(bufArray[i]);
			i++;
			while ( (bufArray[i]).Find(ContainerEndSearch) == -1 ) {
				tempArray.Add(bufArray[i]);
				i++;
			}
			tempArray.Add(bufArray[i]);
			tempContainer.BuildFromStream(tempArray);
			this->containerList.Add(tempContainer);
		}
	}
	return;
}

void qqhSLOTProductPack::StreamAttributes(CExeStringArray & attributBuf) {

	int i,j;
	CString tempBuf;
	CExeStringArray tempArray;

	attributBuf.Add(CString(PackBegSearch) + "\n");


	tempBuf.Format("%sDBID|%d\n", REGBegSearch, this->DBID );
	attributBuf.Add(tempBuf);

	tempBuf.Format("%sDescription|%s\n", REGBegSearch, this->description);
	
	attributBuf.Add(tempBuf);

	tempBuf.Format("%sCube|%f\n",REGBegSearch,this->cube);
	attributBuf.Add(tempBuf);

	tempBuf.Format("%sWeight|%f\n",REGBegSearch,this->weight);
	attributBuf.Add(tempBuf);

	tempBuf.Format("%sTotalSelectionQty|%d\n",REGBegSearch,this->totalSelectionQty);
	attributBuf.Add(tempBuf);

	tempBuf.Format("%sIsPickToBelt|%s\n",REGBegSearch,this->isPickBelt);
	attributBuf.Add(tempBuf);
	
	tempBuf.Format("%sIsHazard|%s\n",REGBegSearch,this->isHazard);
	attributBuf.Add(tempBuf);
	
	tempBuf.Format("%sUnitOfIssue|%d\n",REGBegSearch,this->unitOfIssue);
	attributBuf.Add(tempBuf);
	
	tempBuf.Format("%sBalanceOnHand|%d\n",REGBegSearch,this->balanceOnHand);
	attributBuf.Add(tempBuf);
	
	tempBuf.Format("%sOptimizeBy|%d\n",REGBegSearch,this->optimizeBy);
	attributBuf.Add(tempBuf);

	tempBuf.Format("%sMovement|%f\n",REGBegSearch,this->movement);
	attributBuf.Add(tempBuf);

	tempBuf.Format("%sNumberInPallet|%d\n",REGBegSearch,this->numberInPallet);
	attributBuf.Add(tempBuf);

	if (assignmentLocked == 1)
		tempBuf.Format("%sIsAssignmentLocked|TRUE\n",REGBegSearch);
	else
		tempBuf.Format("%sIsAssignmentLocked|FALSE\n",REGBegSearch);
	attributBuf.Add(tempBuf);

	tempBuf.Format("%sRotateXAxis|%s\n",REGBegSearch,this->rotateXAxis);
	attributBuf.Add(tempBuf);
	
	tempBuf.Format("%sRotateYAxis|%s\n",REGBegSearch,this->rotateYAxis);
	attributBuf.Add(tempBuf);

	tempBuf.Format("%sRotateZAxis|%s\n",REGBegSearch,this->rotateZAxis);
	attributBuf.Add(tempBuf);

	/* not in 1.1
	tempBuf.Format("%sRotatedWidth|%f\n", REGBegSearch, this->rotatedWidth);
	attributBuf.Add(tempBuf);

	tempBuf.Format("%sRotatedLength|%f\n", REGBegSearch, this->rotatedDepth);
	attributBuf.Add(tempBuf);

	tempBuf.Format("%sRotatedHeight|%f\n", REGBegSearch, this->rotatedHeight);
	attributBuf.Add(tempBuf);

	tempBuf.Format("%sMaxStackNumber|%d\n",REGBegSearch,this->maxStackNumber);
	attributBuf.Add(tempBuf);

	*/
	
	attributBuf.Add(CString(DimenBegSearch)+"\n");

	tempBuf.Format("%sWidth|%f\n", REGBegSearch, this->width);
	attributBuf.Add(tempBuf);

	tempBuf.Format("%sLength|%f\n", REGBegSearch, this->depth);
	attributBuf.Add(tempBuf);

	tempBuf.Format("%sHeight|%f\n", REGBegSearch, this->height);
	attributBuf.Add(tempBuf);
	
	attributBuf.Add(CString(DimenEndSearch)+"\n");


	for (i = 0; i < UDFList.GetSize(); i++)
		attributBuf.Add(CString(CString(UDFBegSearch) + UDFList[i] + "\n"));
	if ( this->containerList.GetSize() > 0 ) {
		// not required as this is part of product object
		// attributBuf.Add(CString(ContainerListBeg)+"\n");
		for ( i = 0; i < containerList.GetSize(); i++ ) {
			tempArray.RemoveAll();
			containerList[i].StreamAttributes(tempArray);
			for ( j = 0; j < tempArray.GetSize(); j++)
				attributBuf.Add(tempArray[j]);
		}
		//attributBuf.Add(CString(ContainerListEnd)+"\n");
	}
	attributBuf.Add(CString(PackEndSearch) + "\n");

	return;
}

/////////////////////////////////////////
// qqhSLOTSlottingGroup Member functions
////////////////////////////////////////
qqhSLOTSlottingGroup::qqhSLOTSlottingGroup() {
	priority = 0;
	percentOpenLocs = 0.0;
	locLocs = 0;
	locProdGroup = 0;
	suggestedSectionID = 0;
	type = "SLOTSlottingGroup";
	optimizeAttribute = "Movement";
	optimizeMethod = 1;
}

qqhSLOTSlottingGroup::qqhSLOTSlottingGroup(const qqhSLOTSlottingGroup &other) {
	DBID = other.DBID;
	description = other.description;
	priority = other.priority;
	percentOpenLocs = other.percentOpenLocs;
	locLocs = other.locLocs;
	locProdGroup = other.locProdGroup;
	suggestedSectionID = other.suggestedSectionID;
	optimizeAttribute = other.optimizeAttribute;
	optimizeMethod = other.optimizeMethod;
	type = "SLOTSlottingGroup";
	UDFList.Copy(other.UDFList);
}

qqhSLOTSlottingGroup& qqhSLOTSlottingGroup::operator=(const qqhSLOTSlottingGroup &other) {
	DBID = other.DBID;
	description = other.description;
	priority = other.priority;
	type = "SLOTSlottingGroup";
	percentOpenLocs = other.percentOpenLocs;
	UDFList.Copy(other.UDFList);
	locLocs = other.locLocs;
	locProdGroup = other.locProdGroup;
	suggestedSectionID = other.suggestedSectionID;
	optimizeAttribute = other.optimizeAttribute;
	optimizeMethod = other.optimizeMethod;
	return *this;
}

void qqhSLOTSlottingGroup::BuildFromStream(CExeStringArray &bufArray ) {
	char tempBuf[512];
	char tempREGName[100];
	char tempREGVal[256];
	char tempheader[100];
	int i;
	CExeStringArray tempArray;
	qqhSLOTProductContainer tempContainer;
	CString tempChkStr;

	for ( i = 0; i  < bufArray.GetSize(); i++ ) {
		if ( bufArray[i].Find(REGBegSearch) != -1 ) {
			/////////////////////////////////////////////////////////////
			//  REGs are processed here
			/////////////////////////////////////////////////////////////
			strcpy(tempBuf,bufArray[i].GetBuffer(0));
			bufArray[i].ReleaseBuffer();
			tempChkStr = tempBuf;
			if (tempChkStr.GetAt(tempChkStr.GetLength()-1) == '|')
				strcat(tempBuf,"1");
			if (strchr(tempBuf,'\n') == NULL)
				strcat(tempBuf,"\n");
			bufArray[i] = tempBuf;
			strcpy(tempheader,strtok(tempBuf,"|"));
			strcpy(tempREGName,strtok(NULL,"|"));
			if (strstr(bufArray[i].GetBuffer(0),"|\n") == NULL )
				strcpy(tempREGVal,strtok(NULL,"\n"));
			else
				strcpy(tempREGVal,"");
			bufArray[i].ReleaseBuffer();
			if ( strcmp(tempREGName,"Description") == 0 )
				description = tempREGVal;
			else if ( strcmp(tempREGName,"DBID") == 0 )
				DBID = atoi(tempREGVal);
			else if ( strcmp(tempREGName,"Priority") == 0 )
				priority = atoi(tempREGVal);
			else if ( strcmp(tempREGName,"PercentOpenLocs") == 0 )
				percentOpenLocs = (float)atof(tempREGVal);
			else if ( strcmp(tempREGName,"IsProdGroupLocked") == 0 ) {
				if (strcmp(tempREGVal,"TRUE") == 0)
					locProdGroup = 1;
				else
					locProdGroup = 0;
			}
			else if ( strcmp(tempREGName,"IsAssignmentLocked") == 0 ) {
				if (strcmp(tempREGVal,"TRUE") == 0)
					locLocs = 1;
				else
					locLocs = 0;
			}
			else if ( strcmp(tempREGName,"SuggestedSectionID") == 0 ) 
				suggestedSectionID = atoi(tempREGVal);
			else if ( strcmp(tempREGName,"OptimizeMethod") == 0 )
				optimizeMethod = atoi(tempREGVal);
			else if ( strcmp(tempREGName,"OptimizeAttribute") == 0 )
				optimizeAttribute = tempREGVal;
		}
	

		/////////////////////////////////////////////////////////////
		//  UDFs 
		/////////////////////////////////////////////////////////////
		if ( bufArray[i].Find(UDFBegSearch) != -1 ) {
			strcpy(tempBuf,bufArray[i].GetBuffer(0));
			bufArray[i].ReleaseBuffer();
			strcpy(tempheader,strtok(tempBuf,"|"));
			strcpy(tempheader,strtok(NULL,"\n"));
			UDFList.Add(tempheader);
		}

	}
	return;
}

void qqhSLOTSlottingGroup::StreamAttributes(CExeStringArray & attributBuf) {

	int i;
	CString tempBuf;
	CExeStringArray tempArray;

	attributBuf.Add(CString(SlotGroupBegSearch) + "\n");


	tempBuf.Format("%sDBID|%d\n", REGBegSearch, DBID );
	attributBuf.Add(tempBuf);

	tempBuf.Format("%sDescription|%s\n", REGBegSearch, description);
	attributBuf.Add(tempBuf);

	tempBuf.Format("%sPriority|%d\n",REGBegSearch,priority);
	attributBuf.Add(tempBuf);

	tempBuf.Format("%sPercentOpenLocs|%f\n",REGBegSearch,percentOpenLocs);
	attributBuf.Add(tempBuf);

	tempBuf.Format("%sSuggestedSectionID|%d\n",REGBegSearch,suggestedSectionID);
	attributBuf.Add(tempBuf);

	if (locLocs == 1) 
		tempBuf.Format("%sIsAssignmentLocked|TRUE\n",REGBegSearch);
	else
		tempBuf.Format("%sIsAssignmentLocked|FALSE\n",REGBegSearch);
	attributBuf.Add(tempBuf);

	if (locProdGroup == 1)
		tempBuf.Format("%sIsProdGroupLocked|TRUE\n",REGBegSearch);
	else
		tempBuf.Format("%sIsProdGroupLocked|FALSE\n",REGBegSearch);
	attributBuf.Add(tempBuf);

	tempBuf.Format("%sOptimizeMethod|%d\n",REGBegSearch,optimizeMethod);
	attributBuf.Add(tempBuf);

	if (optimizeAttribute.GetLength() == 0)
		optimizeAttribute = "Movement";
	tempBuf.Format("%sOptimizeAttribute|%s\n", REGBegSearch, optimizeAttribute);
	attributBuf.Add(tempBuf);

	for (i = 0; i < UDFList.GetSize(); i++)
		attributBuf.Add(CString(CString(UDFBegSearch) + UDFList[i] + "\n"));
	
	attributBuf.Add(CString(SlotGroupEndSearch) + "\n");

	return;
}

/////////////////////////////////////////
// qqhSLOTHolder Member functions
////////////////////////////////////////
qqhSLOTHolder::qqhSLOTHolder() {
	//parent = NULL;
}

qqhSLOTHolder::qqhSLOTHolder(const qqhSLOTHolder & other) {
	//parent = other.parent;
}

//void qqhSLOTHolder::SetParent(qqhSLOTObject * aObjptr)
//{
	//this->parent = aObjptr;
//}

////////////////////////////////////////
// qqhSLOTHolder Member functions
////////////////////////////////////////
qqhSLOTLocation::qqhSLOTLocation()
{
	DBID = 0;
	handlingMethod = 1;
	IsSelect = 1;
	IsOverridden = 0;
	setType(CString("SLOTLocation"));
	width = depth = height = maxWeight = 0;
	isChanged = "TRUE";
}

qqhSLOTLocation::qqhSLOTLocation(const qqhSLOTLocation& other)
{
	DBID = other.DBID;
	description = other.description;
	coord = other.coord;
	maxWeight = other.maxWeight;
	width = other.width;
	height = other.height;
	depth = other.depth;
	setType(CString("SLOTLocation"));
	handlingMethod = other.handlingMethod;
	IsSelect = other.IsSelect;
	IsOverridden = other.IsOverridden;
	UDFList.Copy(other.UDFList);
	isChanged = other.isChanged;
	locationProfile = other.locationProfile;
}

qqhSLOTLocation& qqhSLOTLocation::operator=(const qqhSLOTLocation& other)
{
	DBID = other.DBID;
	description = other.description;
	coord = other.coord;
	maxWeight = other.maxWeight;
	setType(CString("SLOTLocation"));
	width = other.width;
	height = other.height;
	depth = other.depth;
	handlingMethod = other.handlingMethod;
	IsSelect = other.IsSelect;
	IsOverridden = other.IsOverridden;
	UDFList.Copy(other.UDFList);
	isChanged = other.isChanged;
	locationProfile = other.locationProfile;
	return *this;
}


void qqhSLOTLocation::BuildFromStream(CExeStringArray &bufArray ) {
	char tempBuf[512];
	char tempREGName[100];
	char tempREGVal[256];
	char tempheader[100];
	int i;
	float tempfloat;
	CExeStringArray tempArray;
	CString tempChkStr;

	for ( i = 0; i  < bufArray.GetSize(); i++ ) {
		if ( bufArray[i].Find(REGBegSearch) != -1 ) {
			/////////////////////////////////////////////////////////////
			//  REGs are processed here
			/////////////////////////////////////////////////////////////
			strcpy(tempBuf,bufArray[i].GetBuffer(0));
			bufArray[i].ReleaseBuffer();
			tempChkStr = tempBuf;
			if (tempChkStr.GetAt(tempChkStr.GetLength()-1) == '|')
				strcat(tempBuf,"1");
			if (strchr(tempBuf,'\n') == NULL)
				strcat(tempBuf,"\n");
			bufArray[i] = tempBuf;
			strcpy(tempheader,strtok(tempBuf,"|"));
			strcpy(tempREGName,strtok(NULL,"|"));
			if (strstr(bufArray[i].GetBuffer(0),"|\n") == NULL )
				strcpy(tempREGVal,strtok(NULL,"\n"));
			else
				strcpy(tempREGVal,"");
			bufArray[i].ReleaseBuffer();
			if ( strcmp(tempREGName,"Description") == 0 )
				this->description = tempREGVal;
			else if ( strcmp(tempREGName,"MaxWeight") == 0 )
				this->maxWeight = (float)atof(tempREGVal);
			else if ( strcmp(tempREGName,"DBID") == 0 )
				this->DBID = atoi(tempREGVal);
			else if ( strcmp(tempREGName,"HandlingMethod") == 0 )
				this->handlingMethod = atoi(tempREGVal);
			else if ( strcmp(tempREGName,"IsSelect") == 0 ) {
				if (strcmp(tempREGVal,"TRUE") == 0 )
					this->IsSelect = 1;
				else
					this->IsSelect = 0;
			}
			else if ( strcmp(tempREGName,"IsOverridden") == 0 ) {
				if (strcmp(tempREGVal,"TRUE") == 0 )
					this->IsOverridden = 1;
				else
					this->IsOverridden = 0;
			}
			else if ( strcmp(tempREGName,"IsChanged") == 0 )
				this->isChanged = tempREGVal;
		}
	

		/////////////////////////////////////////////////////////////
		//  UDFs 
		/////////////////////////////////////////////////////////////
		if ( bufArray[i].Find(UDFBegSearch) != -1 ) {
			strcpy(tempBuf,bufArray[i].GetBuffer(0));
			bufArray[i].ReleaseBuffer();
			strcpy(tempheader,strtok(tempBuf,"|"));
			strcpy(tempheader,strtok(NULL,"\n"));
			this->UDFList.Add(tempheader);
		}

		/////////////////////////////////////////////////////////////
		//  LocationProfile
		/////////////////////////////////////////////////////////////
		if ( bufArray[i].Find(LocationProfileBegSearch) != -1  ) {
			tempArray.RemoveAll();
			tempArray.Add(bufArray[i]);
			i++;
			while ( (bufArray[i].Find(LocationProfileEndSearch) == -1 ) ) {
				tempArray.Add(bufArray[i]);
				i++;
			}
			tempArray.Add(bufArray[i]);
			this->locationProfile.BuildFromStream(tempArray);
		}

		/////////////////////////////////////////////////////////////
		//  Holder Coordinate
		/////////////////////////////////////////////////////////////
		if ( (bufArray[i]).Find(CoordBegSearch) != -1 ) {
			tempArray.RemoveAll();
			tempArray.Add(bufArray[i]);
			i++;
			while ( (bufArray[i]).Find(CoordEndSearch) == -1 ) {
				tempArray.Add(bufArray[i]);
				i++;
			}
			tempArray.Add(bufArray[i]);
			this->coord.BuildFromStream(tempArray);
		}

		/////////////////////////////////////////////////////////////
		//  Dimension
		/////////////////////////////////////////////////////////////
		if ( bufArray[i].Find(DimenBegSearch) != -1  ) {
			i++;
			while ( (bufArray[i].Find(DimenEndSearch) == -1 ) ) {
				strcpy(tempBuf,bufArray[i]);
				strcpy(tempheader,strtok(tempBuf,"|"));
				strcpy(tempREGName,strtok(NULL,"|"));
				strcpy(tempREGVal,strtok(NULL,"\n"));
				if ( strcmp(tempREGName,"Width") == 0 ) {
					tempfloat = (float) (atof(tempREGVal));
					this->width = tempfloat;
				}
				else if ( strcmp(tempREGName,"Depth") == 0 ) {
					tempfloat = (float) (atof(tempREGVal));
					this->depth = tempfloat;
				}
				else if ( strcmp(tempREGName,"Height") == 0 ) {
					tempfloat = (float) (atof(tempREGVal));
					this->height = tempfloat;
				}
				i++;
			}
		}
	}
	return;
}

void qqhSLOTLocation::StreamAttributes(CExeStringArray & attributBuf) {

	int i;
	CString tempBuf;

	attributBuf.Add(CString(LocationBegSearch) + "\n");


	tempBuf.Format("%sDBID|%d\n", REGBegSearch, this->DBID );
	attributBuf.Add(tempBuf);

	tempBuf.Format("%sDescription|%s\n", REGBegSearch, this->description);
	
	attributBuf.Add(tempBuf);

	tempBuf.Format("%sType|%s\n", REGBegSearch, this->type);
	attributBuf.Add(tempBuf);

	tempBuf.Format("%sMaxWeight|%f\n", REGBegSearch, (float)this->maxWeight);
	attributBuf.Add(tempBuf);

	tempBuf.Format("%sHandlingMethod|%d\n", REGBegSearch, this->handlingMethod);
	attributBuf.Add(tempBuf);

	if (this->IsSelect == 1)
		tempBuf.Format("%sIsSelect|TRUE\n",REGBegSearch);
	else
		tempBuf.Format("%sIsSelect|FALSE\n",REGBegSearch);
	attributBuf.Add(tempBuf);

	if (this->IsOverridden == 1)
		tempBuf.Format("%sIsOverridden|TRUE\n",REGBegSearch);
	else
		tempBuf.Format("%sIsOverridden|FALSE\n",REGBegSearch);
	attributBuf.Add(tempBuf);

	tempBuf.Format("%sIsChanged|%s\n", REGBegSearch, this->isChanged);
	attributBuf.Add(tempBuf);

	attributBuf.Add(CString(DimenBegSearch)+"\n");

	tempBuf.Format("%sWidth|%f\n", REGBegSearch, this->width);
	attributBuf.Add(tempBuf);

	tempBuf.Format("%sDepth|%f\n", REGBegSearch, this->depth);
	
	attributBuf.Add(tempBuf);

	tempBuf.Format("%sHeight|%f\n", REGBegSearch, this->height);
	
	attributBuf.Add(tempBuf);
	attributBuf.Add(CString(DimenEndSearch)+"\n");

	for (i = 0; i < UDFList.GetSize(); i++)
		attributBuf.Add(CString(CString(UDFBegSearch) + UDFList[i] + "\n"));
	
	this->coord.StreamAttributes(attributBuf);

	this->locationProfile.StreamAttributes(attributBuf);

	attributBuf.Add(CString(LocationEndSearch) + "\n");

	return;
}

////////////////////////////////////////
// qqhSLOTLevel Member functions
////////////////////////////////////////

qqhSLOTLevel::qqhSLOTLevel()
{
	setType(CString("SLOTLevel"));
	DBID = 0;
	description = "";
	forkFixedInsertion = 0.0;
	isChanged = "TRUE";
	facingGap = facingSnap = minLocWidth = productGap = productSnap = 0.0;
	isOverridden = isRotateAllowed = isVariableLocationsAllowed = FALSE;
}

qqhSLOTLevel::qqhSLOTLevel(const qqhSLOTLevel& other)
{
	DBID = other.DBID;
	description = other.description;
	coord = other.coord;
	childList.Copy(other.childList);
	setType(CString("SLOTLevel"));
	UDFList.Copy(other.UDFList);
	forkFixedInsertion = other.forkFixedInsertion;
	isChanged = other.isChanged;
	isRotateAllowed = other.isRotateAllowed;
	isVariableLocationsAllowed = other.isVariableLocationsAllowed;
	facingGap = other.facingGap;
	facingSnap = other.facingSnap;
	minLocWidth = other.minLocWidth;
	productGap = other.productGap;
	productSnap = other.productSnap;
	isOverridden = other.isOverridden;
	levelProfile = other.levelProfile;
}

qqhSLOTLevel& qqhSLOTLevel::operator=(const qqhSLOTLevel& other)
{
	DBID = other.DBID;
	description = other.description;
	coord = other.coord;
	childList.Copy(other.childList);
	setType(CString("SLOTLevel"));
	UDFList.Copy(other.UDFList);
	forkFixedInsertion = other.forkFixedInsertion;
	isChanged = other.isChanged;
	isRotateAllowed = other.isRotateAllowed;
	isVariableLocationsAllowed = other.isVariableLocationsAllowed;
	facingGap = other.facingGap;
	facingSnap = other.facingSnap;
	minLocWidth = other.minLocWidth;
	productGap = other.productGap;
	productSnap = other.productSnap;
	isOverridden = other.isOverridden;
	levelProfile = other.levelProfile;
	return *this;
}


/////////////////////////////////////////////////////////////
//  Take a streamed level and make a level object
/////////////////////////////////////////////////////////////
void qqhSLOTLevel::BuildFromStream(CExeStringArray &bufArray ) {
	char tempBuf[512];
	char tempREGName[100];
	char tempREGVal[256];
	char tempheader[100];
	int i;
	CExeStringArray tempArray;
	qqhSLOTLevelProfile levelProfile;
	CString tempChkStr;

	for ( i = 0; i < bufArray.GetSize(); i++ ) {
		if ( bufArray[i].Find(REGBegSearch) != -1 ) {
			/////////////////////////////////////////////////////////////
			//  REGs are processed here
			/////////////////////////////////////////////////////////////
			strcpy(tempBuf,bufArray[i].GetBuffer(0));
			bufArray[i].ReleaseBuffer();
			tempChkStr = tempBuf;
			if (tempChkStr.GetAt(tempChkStr.GetLength()-1) == '|')
				strcat(tempBuf,"1");
			if (strchr(tempBuf,'\n') == NULL)
				strcat(tempBuf,"\n");
			bufArray[i] = tempBuf;
			strcpy(tempheader,strtok(tempBuf,"|"));
			strcpy(tempREGName,strtok(NULL,"|"));
			if (strstr(bufArray[i].GetBuffer(0),"|\n") == NULL )
				strcpy(tempREGVal,strtok(NULL,"\n"));
			else
				strcpy(tempREGVal,"");
			bufArray[i].ReleaseBuffer();
			if ( strcmp(tempREGName,"Description") == 0 )
				this->description = tempREGVal;
			else if ( strcmp(tempREGName,"DBID") == 0 )
				this->DBID = atoi(tempREGVal);
			else if ( strcmp(tempREGName,"ForkFixedInsertion") == 0 )
				this->forkFixedInsertion = atof(tempREGVal);
			else if ( strcmp(tempREGName,"IsChanged") == 0 )
				this->isChanged = tempREGVal;
			else if ( strcmp(tempREGName,"IsRotateAllowed") == 0 ) {
				if (strcmp(tempREGVal,"TRUE") == 0 )
					this->isRotateAllowed = TRUE;
				else
					this->isRotateAllowed = FALSE;
			}
			else if ( strcmp(tempREGName,"IsVarLocAllowed") == 0 ) {
				if (strcmp(tempREGVal,"TRUE") == 0 )
					this->isVariableLocationsAllowed = TRUE;
				else
					this->isVariableLocationsAllowed = FALSE;
			}
			else if ( strcmp(tempREGName,"FacingGap") == 0 )
				this->facingGap = (float) atof(tempREGVal);
			else if ( strcmp(tempREGName,"FacingSnap") == 0 )
				this->facingSnap = (float) atof(tempREGVal);
			else if ( strcmp(tempREGName,"MinimumLocWidth") == 0 )
				this->minLocWidth = (float) atof(tempREGVal);
			else if ( strcmp(tempREGName,"ProductGap") == 0 )
				this->productGap = (float) atof(tempREGVal);
			else if ( strcmp(tempREGName,"ProductSnap") == 0 )
				this->productSnap = (float) atof(tempREGVal);
			else if ( strcmp(tempREGName,"IsOverridden") == 0 ) {
				if (strcmp(tempREGVal,"TRUE") == 0 )
					this->isOverridden = TRUE;
				else
					this->isOverridden = FALSE;
			}
		}
	
		/////////////////////////////////////////////////////////////
		//  UDFs 
		/////////////////////////////////////////////////////////////
		if ( bufArray[i].Find(UDFBegSearch) != -1 ) {
			strcpy(tempBuf,bufArray[i].GetBuffer(0));
			bufArray[i].ReleaseBuffer();
			strcpy(tempheader,strtok(tempBuf,"|"));
			strcpy(tempheader,strtok(NULL,"\n"));
			this->UDFList.Add(tempheader);
		}
		/////////////////////////////////////////////////////////////
		//  Holder Coordinate
		/////////////////////////////////////////////////////////////
		if ( (bufArray[i]).Find(CoordBegSearch) != -1 ) {
			tempArray.RemoveAll();
			tempArray.Add(bufArray[i]);
			i++;
			while ( (bufArray[i]).Find(CoordEndSearch) == -1 ) {
				tempArray.Add(bufArray[i]);
				i++;
			}
			tempArray.Add(bufArray[i]);
			this->coord.BuildFromStream(tempArray);
			tempArray.RemoveAll();
		}

		/////////////////////////////////////////////////////////////
		//  LevelProfile
		/////////////////////////////////////////////////////////////
		if ( bufArray[i].Find(LevelProfileBegSearch) != -1  ) {
			tempArray.RemoveAll();
			tempArray.Add(bufArray[i]);
			i++;
			while ( (bufArray[i].Find(LevelProfileEndSearch) == -1 ) ) {
				tempArray.Add(bufArray[i]);
				i++;
			}
			tempArray.Add(bufArray[i]);
			this->levelProfile.BuildFromStream(tempArray);
		}

		/////////////////////////////////////////////////////////////
		//  Location Objects
		/////////////////////////////////////////////////////////////
//		if ( (bufArray[i]).Find(LocationBegSearch) != -1 ) {
//			tempArray.RemoveAll();
//			tempArray.Add(bufArray[i]);
//			i++;
//			while ( (bufArray[i]).Find(LocationEndSearch) == -1 ) {
//				tempArray.Add(bufArray[i]);
//				i++;
//			}
//			tempArray.Add(bufArray[i]);
//			i++;
//			this->GetChildList().Add(BuildLocationFromStream(tempArray));
//		}
	}
	return;
}

void qqhSLOTLevel::StreamAttributes(CExeStringArray & attributBuf) {

	int i;
	CString tempBuf;

	attributBuf.Add(CString(LevelBegSearch) + "\n");

	tempBuf.Format("%sDBID|%d\n", REGBegSearch, this->DBID );
	attributBuf.Add(tempBuf);
	
	tempBuf.Format("%sDescription|%s\n", REGBegSearch, this->description);
	attributBuf.Add(tempBuf);

	tempBuf.Format("%sType|%s\n", REGBegSearch, this->type);
	attributBuf.Add(tempBuf);

	tempBuf.Format("%sForkFixedInsertion|%f\n", REGBegSearch, (float)this->forkFixedInsertion);
	attributBuf.Add(tempBuf);

	tempBuf.Format("%sIsChanged|%s\n", REGBegSearch, this->isChanged);
	attributBuf.Add(tempBuf);

	if (isRotateAllowed)
		tempBuf.Format("%sIsRotateAllowed|TRUE\n", REGBegSearch);
	else
		tempBuf.Format("%sIsRotateAllowed|FALSE\n", REGBegSearch);
	attributBuf.Add(tempBuf);

	if (isVariableLocationsAllowed)
		tempBuf.Format("%sIsVarLocAllowed|TRUE\n", REGBegSearch);
	else
		tempBuf.Format("%sIsVarLocAllowed|FALSE\n", REGBegSearch);
	attributBuf.Add(tempBuf);

	tempBuf.Format("%sFacingGap|%f\n", REGBegSearch, (float)this->facingGap);
	attributBuf.Add(tempBuf);

	tempBuf.Format("%sFacingSnap|%f\n", REGBegSearch, (float)this->facingSnap);
	attributBuf.Add(tempBuf);

	tempBuf.Format("%sMinimumLocWidth|%f\n", REGBegSearch, (float)this->minLocWidth);
	attributBuf.Add(tempBuf);

	tempBuf.Format("%sProductGap|%f\n", REGBegSearch, (float)this->productGap);
	attributBuf.Add(tempBuf);

	tempBuf.Format("%sProductSnap|%f\n", REGBegSearch, (float)this->productSnap);
	attributBuf.Add(tempBuf);

	if (isOverridden)
		tempBuf.Format("%sIsOverridden|TRUE\n", REGBegSearch);
	else
		tempBuf.Format("%sIsOverridden|FALSE\n", REGBegSearch);
	attributBuf.Add(tempBuf);

	for (i = 0; i < UDFList.GetSize(); i++)
		attributBuf.Add(CString(CString(UDFBegSearch) + UDFList[i] + "\n"));

	this->coord.StreamAttributes(attributBuf);

	this->levelProfile.StreamAttributes(attributBuf);

	//	for ( i = 0; i < this->childList.GetSize(); i++ ) {
//		StreamLocationAttributes(this->childList[i], attributBuf);
//	}

	attributBuf.Add(CString(LevelEndSearch) + "\n");

	return;
}

////////////////////////////////////////
// qqhSLOTBay Member functions
////////////////////////////////////////

qqhSLOTBay::qqhSLOTBay()
{
	DBID = 0;
//	barWidth = 0.0;
	//rackTypeID = 1;
	setType(CString("SLOTBay"));
	strcpy(acadHandle,"No Handle");
	isChanged = "TRUE";
}

qqhSLOTBay::qqhSLOTBay(const qqhSLOTBay& other)
{
	DBID = other.DBID;
	//rackTypeID = other.rackTypeID;
	description = other.description;
	coord = other.coord;
	childList.Copy(other.childList);
//	barWidth = other.barWidth;
	strcpy(acadHandle,other.acadHandle);
	setType(CString("SLOTBay"));
	UDFList.Copy(other.UDFList);
	isChanged = other.isChanged;
	bayProfile = other.bayProfile;
}

qqhSLOTBay& qqhSLOTBay::operator=(const qqhSLOTBay& other)
{
	DBID = other.DBID;
	//rackTypeID = other.rackTypeID;
	description = other.description;
	coord = other.coord;
	childList.Copy(other.childList);
	strcpy(acadHandle,other.acadHandle);
	setType(CString("SLOTBay"));
	UDFList.Copy(other.UDFList);
	isChanged = other.isChanged;
	bayProfile = other.bayProfile;
	return *this;
}

void qqhSLOTBay::BuildFromStream(CExeStringArray &bufArray ) {
	char tempBuf[512];
	char tempREGName[100];
	char tempREGVal[256];
	char tempheader[100];
	int i;
	CExeStringArray tempArray;
	CString tempChkStr;

	////FILE * fp = fopen("C:\\junk.txt","w");
	for ( i = 0; i < bufArray.GetSize(); i++ ) {
		if ( bufArray[i].Find(REGBegSearch) != -1 ) {
			/////////////////////////////////////////////////////////////
			//  REGs are processed here
			/////////////////////////////////////////////////////////////
			strcpy(tempBuf,bufArray[i].GetBuffer(0));
			////fprintf(fp,"%s\n",tempBuf);
			bufArray[i].ReleaseBuffer();
			// to avoid strtok errors when no value after '|' sign
			tempChkStr = tempBuf;
			if (tempChkStr.GetAt(tempChkStr.GetLength()-1) == '|')
				strcat(tempBuf,"1");
			if (strchr(tempBuf,'\n') == NULL)
				strcat(tempBuf,"\n");
			bufArray[i] = tempBuf;
			strcpy(tempheader,strtok(tempBuf,"|"));
			strcpy(tempREGName,strtok(NULL,"|"));
			if (strstr(bufArray[i].GetBuffer(0),"|\n") == NULL )
				strcpy(tempREGVal,strtok(NULL,"\n"));
			else
				strcpy(tempREGVal,"");
			bufArray[i].ReleaseBuffer();
			if ( strcmp(tempREGName,"Description") == 0 )
				this->description = tempREGVal;
			else if (strcmp(tempREGName,"DBID") == 0 )
				this->DBID = atoi(tempREGVal);
			else if (strcmp(tempREGName,"AcadHandle") == 0 )
				strcpy(this->acadHandle,tempREGVal);
			else if ( strcmp(tempREGName,"IsChanged") == 0 )
				this->isChanged = tempREGVal;
		}
		/////////////////////////////////////////////////////////////
		//  UDFs 
		/////////////////////////////////////////////////////////////
		if ( bufArray[i].Find(UDFBegSearch) != -1 ) {
			strcpy(tempBuf,bufArray[i].GetBuffer(0));
			bufArray[i].ReleaseBuffer();
			strcpy(tempheader,strtok(tempBuf,"|"));
			strcpy(tempheader,strtok(NULL,"\n"));
			this->UDFList.Add(tempheader);
		}
	
		/////////////////////////////////////////////////////////////
		//  BayProfile
		/////////////////////////////////////////////////////////////
		if ( bufArray[i].Find(BayProfileBegSearch) != -1  ) {
			tempArray.RemoveAll();
			tempArray.Add(bufArray[i]);
			i++;
			while ( (bufArray[i].Find(BayProfileEndSearch) == -1 ) ) {
				tempArray.Add(bufArray[i]);
				i++;
			}
			tempArray.Add(bufArray[i]);
			this->bayProfile.BuildFromStream(tempArray);
		}

		/////////////////////////////////////////////////////////////
		//  Holder Coordinate
		/////////////////////////////////////////////////////////////
		if ( (bufArray[i]).Find(CoordBegSearch) != -1 ) {
			tempArray.RemoveAll();
			tempArray.Add(bufArray[i]);
			i++;
			while ( (bufArray[i]).Find(CoordEndSearch) == -1 ) {
				tempArray.Add(bufArray[i]);
				i++;
			}
			tempArray.Add(bufArray[i]);
			this->coord.BuildFromStream(tempArray);
			tempArray.RemoveAll();
		}

//		if ( bufArray[i].Find(RackTypeBegSearch) != -1 ) {
//			tempArray.RemoveAll();
//			tempArray.Add(bufArray[i]);
//			i++;
//			while ( (bufArray[i].Find(RackTypeEndSearch) == -1) ) {
//				tempArray.Add(bufArray[i]);
//				i++;
//			}
//			tempArray.Add(bufArray[i]);
//			rackType.BuildFromStream(tempArray);
//		}
		/////////////////////////////////////////////////////////////
		//  Level Objects
		/////////////////////////////////////////////////////////////
//		if ( (bufArray[i]).Find(LevelBegSearch) != -1 ) {
//			tempArray.RemoveAll();
//			tempArray.Add(bufArray[i]);
//			i++;
//			while ( (bufArray[i]).Find(LevelEndSearch) == -1 ) {
//				tempArray.Add(bufArray[i]);
//				i++;
//			}
//			tempArray.Add(bufArray[i]);
//			i++;
//			this->GetChildList().Add(BuildLevelFromStream(tempArray));
//		}
	}
	//fclose(fp);
	return;
}

void qqhSLOTBay::StreamAttributes(CExeStringArray & attributBuf) {

	int i;
	CString tempBuf;

	attributBuf.Add(CString(BayBegSearch) + "\n");

	tempBuf.Format("%sDBID|%d\n", REGBegSearch, this->DBID );
	attributBuf.Add(tempBuf);

	tempBuf.Format("%sDescription|%s\n", REGBegSearch, this->description);
	attributBuf.Add(tempBuf);

	tempBuf.Format("%sType|%s\n", REGBegSearch, this->type);
	attributBuf.Add(tempBuf);

	tempBuf.Format("%sAcadHandle|%s\n", REGBegSearch, this->acadHandle);
	attributBuf.Add(tempBuf);

	tempBuf.Format("%sIsChanged|%s\n", REGBegSearch, this->isChanged);
	attributBuf.Add(tempBuf);

	for (i = 0; i < UDFList.GetSize(); i++)
		attributBuf.Add(CString(CString(UDFBegSearch) + UDFList[i] + "\n"));

	this->coord.StreamAttributes(attributBuf);

	this->bayProfile.StreamAttributes(attributBuf);

//	for ( i = 0; i < this->childList.GetSize(); i++ ) {
//		StreamLevelAttributes(this->childList[i], attributBuf);
//	}

//	rackType.StreamAttributes(attributBuf);
//	attributBuf.Add(CString(RackTypeBegSearch) + "\n");
//	tempBuf.Format("%sDBID|%d\n", REGBegSearch, this->rackTypeID);
//	attributBuf.Add(tempBuf);
//	attributBuf.Add(CString(RackTypeEndSearch) + "\n");

	attributBuf.Add(CString(BayEndSearch) + "\n");

	return;
}
////////////////////////////////////////
// qqhSLOTSide Member functions
////////////////////////////////////////

qqhSLOTSide::qqhSLOTSide()
{
	setType(CString("SLOTSide"));
	isChanged = "TRUE";
}

CArray <qqhSLOTBay, qqhSLOTBay&>& qqhSLOTSide::GetChildList()
{
	return childList;
}

qqhSLOTSide::qqhSLOTSide(const qqhSLOTSide& other)
{
	DBID = other.DBID;
	description = other.description;
	coord = other.coord;
	childList.Copy(other.childList);
	IsRotated = other.IsRotated;
	setType(CString("SLOTSide"));
	UDFList.Copy(other.UDFList);
	isChanged = other.isChanged;
}

qqhSLOTSide& qqhSLOTSide::operator=(const qqhSLOTSide& other)
{
	DBID = other.DBID;
	description = other.description;
	coord = other.coord;
	childList.Copy(other.childList);
	IsRotated = other.IsRotated;
	setType(CString("SLOTSide"));
	UDFList.Copy(other.UDFList);
	isChanged = other.isChanged;
	return *this;
}

void qqhSLOTSide::SetIsRotated(int rot)
{
	IsRotated = rot;
}

int qqhSLOTSide::GetIsRotated()
{
	return IsRotated;
}

void qqhSLOTSide::BuildFromStream(CExeStringArray &bufArray ) {
	char tempBuf[512];
	char tempREGName[100];
	char tempREGVal[256];
	char tempheader[100];
	int i;
	CExeStringArray tempArray;
	CString tempChkStr;

	
	for ( i = 0; i < bufArray.GetSize(); i++ ) {
		if ( bufArray[i].Find(REGBegSearch) != -1 ) {
			/////////////////////////////////////////////////////////////
			//  REGs are processed here
			/////////////////////////////////////////////////////////////
			strcpy(tempBuf,bufArray[i].GetBuffer(0));
			bufArray[i].ReleaseBuffer();
			tempChkStr = tempBuf;
			if (tempChkStr.GetAt(tempChkStr.GetLength()-1) == '|')
				strcat(tempBuf,"1");
			if (strchr(tempBuf,'\n') == NULL)
				strcat(tempBuf,"\n");
			bufArray[i] = tempBuf;
			strcpy(tempheader,strtok(tempBuf,"|"));
			strcpy(tempREGName,strtok(NULL,"|"));
			if (strstr(bufArray[i].GetBuffer(0),"|\n") == NULL )
				strcpy(tempREGVal,strtok(NULL,"\n"));
			else
				strcpy(tempREGVal,"");
			bufArray[i].ReleaseBuffer();
			if ( strcmp(tempREGName,"Description") == 0 )
				this->description = tempREGVal;
			else if ( strcmp(tempREGName,"DBID") == 0 ) 
				this->DBID = atoi(tempREGVal);
			else if ( strcmp(tempREGName,"IsRotated") == 0 )  {
				if (strcmp(tempREGVal,"TRUE") == 0 )
					this->IsRotated = 1;
				else
					this->IsRotated = 0;
			}
			else if ( strcmp(tempREGName,"IsChanged") == 0 )
				this->isChanged = tempREGVal;
		}
	
		/////////////////////////////////////////////////////////////
		//  UDFs 
		/////////////////////////////////////////////////////////////
		if ( bufArray[i].Find(UDFBegSearch) != -1 ) {
			strcpy(tempBuf,bufArray[i].GetBuffer(0));
			bufArray[i].ReleaseBuffer();
			strcpy(tempheader,strtok(tempBuf,"|"));
			strcpy(tempheader,strtok(NULL,"\n"));
			this->UDFList.Add(tempheader);
		}
		/////////////////////////////////////////////////////////////
		//  Holder Coordinate
		/////////////////////////////////////////////////////////////
		if ( (bufArray[i]).Find(CoordBegSearch) != -1 ) {
			tempArray.RemoveAll();
			tempArray.Add(bufArray[i]);
			i++;
			while ( (bufArray[i]).Find(CoordEndSearch) == -1 ) {
				tempArray.Add(bufArray[i]);
				i++;
			}
			tempArray.Add(bufArray[i]);
			this->coord.BuildFromStream(tempArray);
			tempArray.RemoveAll();
		}

//		/////////////////////////////////////////////////////////////
//		//  Bay Objects
//		/////////////////////////////////////////////////////////////
//		if ( (bufArray[i]).Find(BayBegSearch) != -1 ) {
//			tempArray.RemoveAll();
//			tempArray.Add(bufArray[i]);
//			i++;
//			while ( (bufArray[i]).Find(BayEndSearch) == -1 ) {
//				tempArray.Add(bufArray[i]);
//				i++;
//			}
//			tempArray.Add(bufArray[i]);
//			i++;
//			this->.GetChildList().Add(BuildBayFromStream(tempArray));
//		}
	}
	return;
}

void qqhSLOTSide::StreamAttributes(CExeStringArray & attributBuf) {

	int i;
	CString tempBuf;

	attributBuf.Add(CString(SideBegSearch) + "\n");

	tempBuf.Format("%sDBID|%d\n", REGBegSearch, this->DBID );
	attributBuf.Add(tempBuf);
	
	tempBuf.Format("%sDescription|%s\n", REGBegSearch, this->description);
	attributBuf.Add(tempBuf);

	tempBuf.Format("%sType|%s\n", REGBegSearch, this->type);
	attributBuf.Add(tempBuf);

	if (this->IsRotated == 1 )
		tempBuf.Format("%sIsRotated|TRUE\n", REGBegSearch);
	else
		tempBuf.Format("%sIsRotated|FALSE\n", REGBegSearch);
	attributBuf.Add(tempBuf);

	tempBuf.Format("%sIsChanged|%s\n", REGBegSearch, this->isChanged);
	attributBuf.Add(tempBuf);

	for (i = 0; i < UDFList.GetSize(); i++)
		attributBuf.Add(CString(CString(UDFBegSearch) + UDFList[i] + "\n"));

	this->coord.StreamAttributes(attributBuf);

//	for ( i = 0; i < this->childList.GetSize(); i++ ) {
//		StreamBayAttributes(this->childList[i], attributBuf);
//	}

	attributBuf.Add(CString(SideEndSearch) + "\n");
	return;
}

////////////////////////////////////////
// qqhSLOTAisle Member functions
////////////////////////////////////////

qqhSLOTAisle::qqhSLOTAisle()
{
	DBID = 0;
	setType(CString("SLOTAisle"));
	pickPath.setAcadHandle("XXX");
//	strcpy(pickPath.acadHandle,"XXX");
	isChanged = "TRUE";
	leftBayStart = "1";
	leftBayStep = 0;
	leftBayScheme = 0;
	rightBayStart = "1";
	rightBayStep = 0;
	rightBayScheme = 0;
	leftLevelStart = "1";
	leftLevelStep = 0;
	leftLevelScheme = 0;
	leftLevelBreak = 0;
	rightLevelStart = "1";
	rightLevelStep = 0;
	rightLevelScheme = 0;
	rightLevelBreak = 0;
	leftLocationStart = "1";
	leftLocationStep = 0;
	leftLocationScheme = 0;
	leftLocationBreak = 0;
	rightLocationStart = "1";
	rightLocationStep = 0;
	rightLocationScheme = 0;
	rightLocationBreak = 0;
	pickPathDirection = 0;
	pickPathType = 0;
	pickPathStartSide = 0;
	baysInPattern = 0;
	rotation = 0;
}

qqhSLOTAisle::qqhSLOTAisle(const qqhSLOTAisle& other)
{
	DBID = other.DBID;
	description = other.description;
	coord = other.coord;
	childList.Copy(other.childList);
	pickPath = other.pickPath;
	setType(CString("SLOTAisle"));
	UDFList.Copy(other.UDFList);
	isChanged = other.isChanged;
	leftBayStart = other.leftBayStart;
	leftBayStep = other.leftBayStep;
	leftBayScheme = other.leftBayScheme;
	rightBayStart = other.rightBayStart;
	rightBayStep = other.rightBayStep;
	rightBayScheme = other.rightBayScheme;
	leftLevelStart = other.leftLevelStart;
	leftLevelStep = other.leftLevelStep;
	leftLevelScheme = other.leftLevelScheme;
	leftLevelBreak = other.leftLevelBreak;
	rightLevelStart = other.rightLevelStart;
	rightLevelStep = other.rightLevelStep;
	rightLevelScheme = other.rightLevelScheme;
	rightLevelBreak = other.rightLevelBreak;
	leftLocationStart = other.leftLocationStart;
	leftLocationStep = other.leftLocationStep;
	leftLocationScheme = other.leftLocationScheme;
	leftLocationBreak = other.leftLocationBreak;
	rightLocationStart = other.rightLocationStart;
	rightLocationStep = other.rightLocationStep;
	rightLocationScheme = other.rightLocationScheme;
	rightLocationBreak = other.rightLocationBreak;
	pickPathDirection = other.pickPathDirection;
	pickPathType = other.pickPathType;
	pickPathStartSide = other.pickPathStartSide;
	baysInPattern = other.baysInPattern;
	rotation = other.rotation;
}

qqhSLOTAisle& qqhSLOTAisle::operator=(const qqhSLOTAisle& other)
{
	DBID = other.DBID;
	description = other.description;
	coord = other.coord;
	childList.Copy(other.childList);
	pickPath = other.pickPath;
	setType(CString("SLOTAisle"));
	UDFList.Copy(other.UDFList);
	isChanged = other.isChanged;
	leftBayStart = other.leftBayStart;
	leftBayStep = other.leftBayStep;
	leftBayScheme = other.leftBayScheme;
	rightBayStart = other.rightBayStart;
	rightBayStep = other.rightBayStep;
	rightBayScheme = other.rightBayScheme;
	leftLevelStart = other.leftLevelStart;
	leftLevelStep = other.leftLevelStep;
	leftLevelScheme = other.leftLevelScheme;
	leftLevelBreak = other.leftLevelBreak;
	rightLevelStart = other.rightLevelStart;
	rightLevelStep = other.rightLevelStep;
	rightLevelScheme = other.rightLevelScheme;
	rightLevelBreak = other.rightLevelBreak;
	leftLocationStart = other.leftLocationStart;
	leftLocationStep = other.leftLocationStep;
	leftLocationScheme = other.leftLocationScheme;
	leftLocationBreak = other.leftLocationBreak;
	rightLocationStart = other.rightLocationStart;
	rightLocationStep = other.rightLocationStep;
	rightLocationScheme = other.rightLocationScheme;
	rightLocationBreak = other.rightLocationBreak;
	pickPathDirection = other.pickPathDirection;
	pickPathType = other.pickPathType;
	pickPathStartSide = other.pickPathStartSide;
	baysInPattern = other.baysInPattern;
	rotation = other.rotation;
	return *this;
}

void qqhSLOTAisle::BuildFromStream( CExeStringArray & bufArray ) {
	char tempBuf[512];
	char tempREGName[100];
	char tempREGVal[256];
	char tempheader[100];
	int i;
	CExeStringArray tempArray;
	CString tempChkStr;

	for ( i = 0; i < bufArray.GetSize(); i++ ) {
		if ( bufArray[i].Find(REGBegSearch) != -1 ) {
			/////////////////////////////////////////////////////////////
			//  REGs are processed here
			/////////////////////////////////////////////////////////////
			strcpy(tempBuf,bufArray[i].GetBuffer(0));
			bufArray[i].ReleaseBuffer();
			tempChkStr = tempBuf;
			if (tempChkStr.GetAt(tempChkStr.GetLength()-1) == '|')
				strcat(tempBuf,"1");
			if (strchr(tempBuf,'\n') == NULL)
				strcat(tempBuf,"\n");
			bufArray[i] = tempBuf;
			strcpy(tempheader,strtok(tempBuf,"|"));
			strcpy(tempREGName,strtok(NULL,"|"));
			if (strstr(bufArray[i].GetBuffer(0),"|\n") == NULL )
				strcpy(tempREGVal,strtok(NULL,"\n"));
			else
				strcpy(tempREGVal,"");
			bufArray[i].ReleaseBuffer();
			if ( strcmp(tempREGName,"Description") == 0 )
				this->description = tempREGVal;
			else if ( strcmp(tempREGName,"DBID") == 0 )
				this->DBID = atoi(tempREGVal);
			else if ( strcmp(tempREGName,"LeftBayScheme") == 0 )
				this->leftBayScheme = atoi(tempREGVal);
			else if ( strcmp(tempREGName,"RightBayScheme") == 0 )
				this->rightBayScheme = atoi(tempREGVal);
			else if ( strcmp(tempREGName,"LeftBayStep") == 0 )
				this->leftBayStep = atoi(tempREGVal);
			else if ( strcmp(tempREGName,"RightBayStep") == 0 )
				this->rightBayStep = atoi(tempREGVal);
			else if ( strcmp(tempREGName,"LeftLevelScheme") == 0 )
				this->leftLevelScheme = atoi(tempREGVal);
			else if ( strcmp(tempREGName,"LeftLocScheme") == 0 )
				this->leftLocationScheme = atoi(tempREGVal);
			else if ( strcmp(tempREGName,"RightLevelScheme") == 0 )
				this->rightLevelScheme = atoi(tempREGVal);
			else if ( strcmp(tempREGName,"RightLocScheme") == 0 )
				this->rightLocationScheme = atoi(tempREGVal);
			else if ( strcmp(tempREGName,"LeftLevelStep") == 0 )
				this->leftLevelStep = atoi(tempREGVal);
			else if ( strcmp(tempREGName,"RightLevelStep") == 0 )
				this->rightLevelStep = atoi(tempREGVal);
			else if ( strcmp(tempREGName,"LeftLocStep") == 0 )
				this->leftLocationStep = atoi(tempREGVal);
			else if ( strcmp(tempREGName,"RightLocStep") == 0 )
				this->rightLocationStep = atoi(tempREGVal);
			else if ( strcmp(tempREGName,"LeftLevelBreak") == 0 )
				this->leftLevelBreak = atoi(tempREGVal);
			else if ( strcmp(tempREGName,"RightLevelBreak") == 0 )
				this->rightLevelBreak = atoi(tempREGVal);
			else if ( strcmp(tempREGName,"LeftLocBreak") == 0 )
				this->leftLocationBreak = atoi(tempREGVal);
			else if ( strcmp(tempREGName,"RightLocBreak") == 0 )
				this->rightLocationBreak = atoi(tempREGVal);
			else if ( strcmp(tempREGName,"RightBayStart") == 0 )
				this->rightBayStart = tempREGVal;
			else if ( strcmp(tempREGName,"LeftBayStart") == 0 )
				this->leftBayStart = tempREGVal;
			else if ( strcmp(tempREGName,"RightLevelStart") == 0 )
				this->rightLevelStart = tempREGVal;
			else if ( strcmp(tempREGName,"LeftLevelStart") == 0 )
				this->leftLevelStart = tempREGVal;
			else if ( strcmp(tempREGName,"RightLocStart") == 0 )
				this->rightLocationStart = tempREGVal;
			else if ( strcmp(tempREGName,"LeftLocStart") == 0 )
				this->leftLocationStart = tempREGVal;
			else if ( strcmp(tempREGName,"IsChanged") == 0 )
				this->isChanged = tempREGVal;
			else if ( strcmp(tempREGName,"PickPathDirection") == 0 )
				this->pickPathDirection = atoi(tempREGVal);
			else if ( strcmp(tempREGName,"PickPathType") == 0 )
				this->pickPathType = atoi(tempREGVal);
			else if ( strcmp(tempREGName,"PickPathStartSide") == 0 )
				this->pickPathStartSide = atoi(tempREGVal);
			else if ( strcmp(tempREGName,"BaysInPattern") == 0 )
				this->baysInPattern = atoi(tempREGVal);
			else if ( strcmp(tempREGName,"Rotation") == 0 )
				this->rotation = (float) atof(tempREGVal);
		}
	
		/////////////////////////////////////////////////////////////
		//  UDFs 
		/////////////////////////////////////////////////////////////
		if ( bufArray[i].Find(UDFBegSearch) != -1 ) {
			strcpy(tempBuf,bufArray[i].GetBuffer(0));
			bufArray[i].ReleaseBuffer();
			strcpy(tempheader,strtok(tempBuf,"|"));
			strcpy(tempheader,strtok(NULL,"\n"));
			this->UDFList.Add(tempheader);
		}
		/////////////////////////////////////////////////////////////
		//  Holder Coordinate
		/////////////////////////////////////////////////////////////
		if ( (bufArray[i]).Find(CoordBegSearch) != -1 ) {
			tempArray.RemoveAll();
			tempArray.Add(bufArray[i]);
			i++;
			while ( (bufArray[i]).Find(CoordEndSearch) == -1 ) {
				tempArray.Add(bufArray[i]);
				i++;
			}
			tempArray.Add(bufArray[i]);
			this->coord.BuildFromStream(tempArray);
			tempArray.RemoveAll();
		}
		if ( bufArray[i].Find(PathBegSearch) != -1 ) {
			tempArray.RemoveAll();
			tempArray.Add(bufArray[i]);
			i++;
			while ( (bufArray[i].Find(PathEndSearch) == -1 ) ) {
				tempArray.Add(bufArray[i]);
				i++;
			}
			this->pickPath.BuildFromStream(tempArray);
			tempArray.RemoveAll();
		}


//		/////////////////////////////////////////////////////////////
//		//  Side Objects
//		/////////////////////////////////////////////////////////////
//		if ( (bufArray[i]).Find(SideBegSearch) != -1 ) {
//			tempArray.RemoveAll();
//			tempArray.Add(bufArray[i]);
//			i++;
//			while ( (bufArray[i]).Find(SideEndSearch) == -1 ) {
//				tempArray.Add(bufArray[i]);
//				i++;
//			}
//			tempArray.Add(bufArray[i]);
//			i++;
//			this->GetChildList().Add(BuildSideFromStream(tempArray));
//		}
	}
	return;
}

void qqhSLOTAisle::StreamAttributes(CExeStringArray & attributBuf) {

	int i;
	CString tempBuf;

	attributBuf.Add(CString(AisleBegSearch) + "\n");

	tempBuf.Format("%sDBID|%d\n", REGBegSearch, this->DBID );
	attributBuf.Add(tempBuf);

	tempBuf.Format("%sRightBayStep|%d\n", REGBegSearch, this->rightBayStep );
	attributBuf.Add(tempBuf);

	tempBuf.Format("%sLeftBayStep|%d\n", REGBegSearch, this->leftBayStep );
	attributBuf.Add(tempBuf);

	tempBuf.Format("%sRightLevelStep|%d\n", REGBegSearch, this->rightLevelStep );
	attributBuf.Add(tempBuf);

	tempBuf.Format("%sLeftLevelStep|%d\n", REGBegSearch, this->leftLevelStep );
	attributBuf.Add(tempBuf);

	tempBuf.Format("%sRightLocStep|%d\n", REGBegSearch, this->rightLocationStep );
	attributBuf.Add(tempBuf);

	tempBuf.Format("%sLeftLocStep|%d\n", REGBegSearch, this->leftLocationStep );
	attributBuf.Add(tempBuf);

	tempBuf.Format("%sRightBayScheme|%d\n", REGBegSearch, this->rightBayScheme );
	attributBuf.Add(tempBuf);

	tempBuf.Format("%sLeftBayScheme|%d\n", REGBegSearch, this->leftBayScheme );
	attributBuf.Add(tempBuf);

	tempBuf.Format("%sRightBayScheme|%d\n", REGBegSearch, this->rightBayScheme );
	attributBuf.Add(tempBuf);

	tempBuf.Format("%sLeftLevelScheme|%d\n", REGBegSearch, this->leftLevelScheme );
	attributBuf.Add(tempBuf);

	tempBuf.Format("%sRightLevelScheme|%d\n", REGBegSearch, this->rightLevelScheme );
	attributBuf.Add(tempBuf);

	tempBuf.Format("%sLeftLocScheme|%d\n", REGBegSearch, this->leftLocationScheme );
	attributBuf.Add(tempBuf);

	tempBuf.Format("%sRightLocScheme|%d\n", REGBegSearch, this->rightLocationScheme );
	attributBuf.Add(tempBuf);

	tempBuf.Format("%sPickPathDirection|%d\n", REGBegSearch, this->pickPathDirection );
	attributBuf.Add(tempBuf);

	tempBuf.Format("%sPickPathType|%d\n", REGBegSearch, this->pickPathType );
	attributBuf.Add(tempBuf);

	tempBuf.Format("%sPickPathStartSide|%d\n", REGBegSearch, this->pickPathStartSide );
	attributBuf.Add(tempBuf);

	tempBuf.Format("%sBaysInPattern|%d\n", REGBegSearch, this->baysInPattern );
	attributBuf.Add(tempBuf);

	tempBuf.Format("%sLeftLevelBreak|%d\n", REGBegSearch, this->leftLevelBreak );
	attributBuf.Add(tempBuf);

	tempBuf.Format("%sRightLevelBreak|%d\n", REGBegSearch, this->rightLevelBreak );
	attributBuf.Add(tempBuf);

	tempBuf.Format("%sLeftLocBreak|%d\n", REGBegSearch, this->leftLocationBreak );
	attributBuf.Add(tempBuf);

	tempBuf.Format("%sRightLocBreak|%d\n", REGBegSearch, this->rightLocationBreak );
	attributBuf.Add(tempBuf);

	tempBuf.Format("%sLeftBayStart|%s\n", REGBegSearch, this->leftBayStart);
	attributBuf.Add(tempBuf);

	tempBuf.Format("%sRightBayStart|%s\n", REGBegSearch, this->rightBayStart);
	attributBuf.Add(tempBuf);
	
	tempBuf.Format("%sLeftLevelStart|%s\n", REGBegSearch, this->leftLevelStart);
	attributBuf.Add(tempBuf);
	
	tempBuf.Format("%sRightLevelStart|%s\n", REGBegSearch, this->rightLevelStart);
	attributBuf.Add(tempBuf);
	
	tempBuf.Format("%sLeftLocStart|%s\n", REGBegSearch, this->leftLocationStart);
	attributBuf.Add(tempBuf);
	
	tempBuf.Format("%sRightLocStart|%s\n", REGBegSearch, this->rightLocationStart);
	attributBuf.Add(tempBuf);
	
	tempBuf.Format("%sDescription|%s\n", REGBegSearch, this->description);
	attributBuf.Add(tempBuf);

	tempBuf.Format("%sType|%s\n", REGBegSearch, this->type);
	attributBuf.Add(tempBuf);

	tempBuf.Format("%sIsChanged|%s\n", REGBegSearch, this->isChanged);
	attributBuf.Add(tempBuf);

	tempBuf.Format("%sRotation|%f\n", REGBegSearch, this->rotation);
	attributBuf.Add(tempBuf);

	for (i = 0; i < UDFList.GetSize(); i++)
		attributBuf.Add(CString(CString(UDFBegSearch) + UDFList[i] + "\n"));

	this->coord.StreamAttributes(attributBuf);
	this->pickPath.StreamAttributes(attributBuf);

//	for ( i = 0; i < this->childList.GetSize(); i++ ) {
//		StreamSideAttributes(this->childList[i], attributBuf);
//	}

	attributBuf.Add(CString(AisleEndSearch) + "\n");
	return;
}

////////////////////////////////////////
// qqhSLOTSection Member functions
////////////////////////////////////////

qqhSLOTSection::qqhSLOTSection()
{
	DBID = 0;
	setType("SLOTSection");
	locationMask = "";
	avgCubePerTrip = forkDistFixed = forkDistVar =
	forkLaborRate = replenAvgDist = selectDistFixed =
	selectDistVar = selectLaborRate = 0.0;
	hotSpotList.RemoveAll();
	selDist = 0.0;
	avgOrdQty = 0;
	contQty = 0;
	orderCount = 0;
	applyBrokenOrder = "FALSE";
	isChanged = "TRUE";
}

CArray <qqhSLOTAisle, qqhSLOTAisle&>& qqhSLOTSection::GetChildList()
{
	return childList;
}

qqhSLOTSection::qqhSLOTSection(const qqhSLOTSection& other)
{
	DBID = other.DBID;
	description = other.description;
	coord = other.coord;
	childList.Copy(other.childList);
	setType("SLOTSection");
//	pickPath = other.pickPath;
	locationMask = other.locationMask;
	avgCubePerTrip = other.avgCubePerTrip;
	forkDistFixed = other.forkDistFixed;
	forkDistVar = other.forkDistVar;
	forkLaborRate = other.forkLaborRate;
	replenAvgDist = other.replenAvgDist;
	selectDistFixed = other.selectDistFixed;
	selectDistVar = other.selectDistVar;
	selectLaborRate = other.selectLaborRate;
	hotSpotList.Copy(other.hotSpotList);
	UDFList.Copy(other.UDFList);
	selDist = other.selDist;
	avgOrdQty = other.avgOrdQty;
	contQty = other.contQty;
	orderCount = other.orderCount;
	applyBrokenOrder = other.applyBrokenOrder;
	isChanged = other.isChanged;
}

qqhSLOTSection& qqhSLOTSection::operator=(const qqhSLOTSection& other)
{
	DBID = other.DBID;
	description = other.description;
	coord = other.coord;
	childList.Copy(other.childList);
	setType("SLOTSection");
//	pickPath = other.pickPath;
	locationMask = other.locationMask;
	avgCubePerTrip = other.avgCubePerTrip;
	forkDistFixed = other.forkDistFixed;
	forkDistVar = other.forkDistVar;
	forkLaborRate = other.forkLaborRate;
	replenAvgDist = other.replenAvgDist;
	selectDistFixed = other.selectDistFixed;
	selectDistVar = other.selectDistVar;
	selectLaborRate = other.selectLaborRate;
	hotSpotList.Copy(other.hotSpotList);
	UDFList.Copy(other.UDFList);
	selDist = other.selDist;
	avgOrdQty = other.avgOrdQty;
	contQty = other.contQty;
	orderCount = other.orderCount;
	applyBrokenOrder = other.applyBrokenOrder;
	isChanged = other.isChanged;
	return *this;
}

void qqhSLOTSection::BuildFromStream(CExeStringArray &bufArray ) {
	char tempBuf[512];
	char tempREGName[100];
	char tempREGVal[256];
	char tempheader[100];
	int i;
	qqhSLOTHotSpot tempHotSpot;
	CExeStringArray tempArray;
	CString tempChkStr;

	
	for ( i = 0; i < bufArray.GetSize(); i++ ) {
		if ( bufArray[i].Find(REGBegSearch) != -1 ) {
			/////////////////////////////////////////////////////////////
			//  REGs are processed here
			/////////////////////////////////////////////////////////////
			strcpy(tempBuf,bufArray[i].GetBuffer(0));
			bufArray[i].ReleaseBuffer();
			tempChkStr = tempBuf;
			if (tempChkStr.GetAt(tempChkStr.GetLength()-1) == '|')
				strcat(tempBuf,"1");
			if (strchr(tempBuf,'\n') == NULL)
				strcat(tempBuf,"\n");
			bufArray[i] = tempBuf;
			strcpy(tempheader,strtok(tempBuf,"|"));
			strcpy(tempREGName,strtok(NULL,"|"));
			if (strstr(bufArray[i].GetBuffer(0),"|\n") == NULL )
				strcpy(tempREGVal,strtok(NULL,"\n"));
			else
				strcpy(tempREGVal,"");
			bufArray[i].ReleaseBuffer();
			if ( strcmp(tempREGName,"Description") == 0 )
				this->description = tempREGVal;
			else if ( strcmp(tempREGName,"DBID") == 0 )
				this->DBID = atoi(tempREGVal);
			else if ( strcmp(tempREGName,"LocationIDMask") == 0 )
				this->locationMask=tempREGVal;
			else if ( strcmp(tempREGName,"AvgCubePerTrip") == 0 )
				this->avgCubePerTrip=(float)atof(tempREGVal);
			else if ( strcmp(tempREGName,"ForkDistFixed") == 0 )
				this->forkDistFixed=(float)atof(tempREGVal);
			else if ( strcmp(tempREGName,"ForkDistVar") == 0 )
				this->forkDistVar=(float)atof(tempREGVal);
			else if ( strcmp(tempREGName,"ForkLaborRate") == 0 )
				this->forkLaborRate=(float)atof(tempREGVal);
			else if ( strcmp(tempREGName,"ReplenAvgDist") == 0 )
				this->replenAvgDist=(float)atof(tempREGVal);
			else if ( strcmp(tempREGName,"SelectDistFixed") == 0 )
				this->selectDistFixed=(float)atof(tempREGVal);
			else if ( strcmp(tempREGName,"SelectDistVar") == 0 )
				this->selectDistVar=(float)atof(tempREGVal);
			else if ( strcmp(tempREGName,"SelectLaborRate") == 0 )
				this->selectLaborRate=(float)atof(tempREGVal);
			else if ( strcmp(tempREGName,"ApplyBrokenOrder") == 0 )
				this->applyBrokenOrder= tempREGVal;
			else if ( strcmp(tempREGName,"AverageOrderQuantity") == 0 )
				this->avgOrdQty = atoi(tempREGVal);
			else if ( strcmp(tempREGName,"ContainerQuantity") == 0 )
				this->contQty = atoi(tempREGVal);
			else if ( strcmp(tempREGName,"OrderCount") == 0 )
				this->orderCount = atoi(tempREGVal);
			else if ( strcmp(tempREGName,"TotalSelectionDistance") == 0 )
				this->selDist = (float)atof(tempREGVal);
			else if ( strcmp(tempREGName,"IsChanged") == 0 )
				this->isChanged = tempREGVal;

		}
	
		/////////////////////////////////////////////////////////////
		//  UDFs 
		/////////////////////////////////////////////////////////////
		if ( bufArray[i].Find(UDFBegSearch) != -1 ) {
			strcpy(tempBuf,bufArray[i].GetBuffer(0));
			bufArray[i].ReleaseBuffer();
			strcpy(tempheader,strtok(tempBuf,"|"));
			strcpy(tempheader,strtok(NULL,"\n"));
			this->UDFList.Add(tempheader);
		}
		/////////////////////////////////////////////////////////////
		//  Holder Coordinate
		/////////////////////////////////////////////////////////////
		if ( (bufArray[i]).Find(CoordBegSearch) != -1 ) {
			tempArray.RemoveAll();
			tempArray.Add(bufArray[i]);
			i++;
			while ( (bufArray[i]).Find(CoordEndSearch) == -1 ) {
				tempArray.Add(bufArray[i]);
				i++;
			}
			tempArray.Add(bufArray[i]);
			this->coord.BuildFromStream(tempArray);
			tempArray.RemoveAll();
		}

		/////////////////////////////////////////////////////////////
		//  Hot Spot
		/////////////////////////////////////////////////////////////
		if ( (bufArray[i].Find(HotSpotBegSearch) != -1 )) {
			tempArray.RemoveAll();
			tempArray.Add(bufArray[i]);
			i++;
			while ( (bufArray[i].Find(HotSpotEndSearch) == -1) ) {
				tempArray.Add(bufArray[i]);
				i++;
			}
			tempArray.Add(bufArray[i]);
			tempHotSpot.BuildFromStream(tempArray);
			this->hotSpotList.Add(tempHotSpot);
			tempArray.RemoveAll();
		}
	}
	return;
}

void qqhSLOTSection::StreamAttributes(CExeStringArray & attributBuf) {

	int i;

	CString tempBuf;

	attributBuf.Add(CString(SectionBegSearch) + "\n");

	tempBuf.Format("%sDBID|%d\n", REGBegSearch, this->DBID );
	attributBuf.Add(tempBuf);

	tempBuf.Format("%sDescription|%s\n", REGBegSearch, this->description);
	attributBuf.Add(tempBuf);

	tempBuf.Format("%sType|%s\n", REGBegSearch, this->type);
	attributBuf.Add(tempBuf);

	tempBuf.Format("%sLocationIDMask|%s\n", REGBegSearch, this->locationMask);
	attributBuf.Add(tempBuf);

	tempBuf.Format("%sAvgCubePerTrip|%f\n", REGBegSearch, this->avgCubePerTrip);
	attributBuf.Add(tempBuf);
	
	tempBuf.Format("%sForkDistFixed|%f\n", REGBegSearch, this->forkDistFixed);
	attributBuf.Add(tempBuf);

	tempBuf.Format("%sForkDistVar|%f\n", REGBegSearch, this->forkDistVar);
	attributBuf.Add(tempBuf);
	
	tempBuf.Format("%sForkLaborRate|%f\n", REGBegSearch, this->forkLaborRate);
	attributBuf.Add(tempBuf);
	
	tempBuf.Format("%sReplenAvgDist|%f\n", REGBegSearch, this->replenAvgDist);
	attributBuf.Add(tempBuf);
	
	tempBuf.Format("%sSelectDistFixed|%f\n", REGBegSearch, this->selectDistFixed);
	attributBuf.Add(tempBuf);
	
	tempBuf.Format("%sSelectDistVar|%f\n", REGBegSearch, this->selectDistVar);
	attributBuf.Add(tempBuf);
	
	tempBuf.Format("%sSelectLaborRate|%f\n", REGBegSearch, this->selectLaborRate);
	attributBuf.Add(tempBuf);

	tempBuf.Format("%sApplyBrokenOrder|%s\n", REGBegSearch, this->applyBrokenOrder);
	attributBuf.Add(tempBuf);

	tempBuf.Format("%sAverageOrderQuantity|%d\n", REGBegSearch, this->avgOrdQty);
	attributBuf.Add(tempBuf);

	tempBuf.Format("%sContainerQuantity|%d\n", REGBegSearch, this->contQty);
	attributBuf.Add(tempBuf);

	tempBuf.Format("%sTotalSelectionDistance|%f\n", REGBegSearch, this->selDist);
	attributBuf.Add(tempBuf);
			
	tempBuf.Format("%sOrderCount|%d\n", REGBegSearch, this->orderCount);
	attributBuf.Add(tempBuf);

	tempBuf.Format("%sIsChanged|%s\n", REGBegSearch, this->isChanged);
	attributBuf.Add(tempBuf);

	for (i = 0; i < UDFList.GetSize(); i++)
		attributBuf.Add(CString(CString(UDFBegSearch) + UDFList[i] + "\n"));

	this->coord.StreamAttributes(attributBuf);

//	this->pickPath.StreamAttributes(attributBuf);

	for ( i = 0; i < this->hotSpotList.GetSize(); i++)
		hotSpotList[i].StreamAttributes(attributBuf);

	attributBuf.Add(CString(SectionEndSearch) + "\n");

	return;
}

////////////////////////////////////////
// qqhSLOTFacility Member functions
////////////////////////////////////////

qqhSLOTFacility::qqhSLOTFacility()
{
	DBID = 0;
	region = units = slotType = 1;
	strCadFileName = "";
	IsModified = 0;
	setType(CString("SLOTFacility"));
	cost = 0.0;
	units = 0;
	duration = 0;
	clientNameOpened = "";
	isChanged = "TRUE";
}

qqhSLOTFacility::qqhSLOTFacility(const qqhSLOTFacility& other)
{
	DBID = other.DBID;
	region = other.region;
	units = other.units;
	duration = other.duration;
	slotType = other.slotType;
	description = other.description;
	coord = other.coord;
	childList.Copy(other.childList);
	strCadFileName = other.strCadFileName;
	setType(CString("SLOTFacility"));
	IsModified = other.IsModified;
	cost = other.cost;
	timeHorizonUnit = other.timeHorizonUnit;
	region = other.region;
	slotType = other.slotType;
	originalFacilityId = other.originalFacilityId;
	clientNameOpened = other.clientNameOpened;
	UDFList.Copy(other.UDFList);
	isChanged = other.isChanged;
}

qqhSLOTFacility::qqhSLOTFacility(const char *strName)
{
	DBID = 0;
	strCadFileName = strName;
	setType(CString("SLOTFacility"));
	IsModified = 0;
}


qqhSLOTFacility& qqhSLOTFacility::operator=(const qqhSLOTFacility& other)
{
	DBID = other.DBID;
	IsModified = other.IsModified;
	description = other.description;
	coord = other.coord;
	childList.Copy(other.childList);
	strCadFileName = other.strCadFileName;
	setType(CString("SLOTFacility"));
	cost = other.cost;
	duration = other.duration;
	timeHorizonUnit = other.timeHorizonUnit;
	region = other.region;
	units = other.units;
	slotType = other.slotType;
	originalFacilityId = other.originalFacilityId;
	clientNameOpened = other.clientNameOpened;
	UDFList.Copy(other.UDFList);
	return *this;
}

void qqhSLOTFacility::BuildFromStream(CExeStringArray &bufArray ) {
	char tempBuf[512];
	char tempREGName[100];
	char tempREGVal[256];
	char tempheader[100];
	int i;
	CExeStringArray tempArray;
	qqhSLOTPickPath tempPath;
	CString tempChkStr;

	for ( i = 0; i < bufArray.GetSize(); i++ ) {
		/////////////////////////////////////////////////////////////
		//  REGs are processed here
		/////////////////////////////////////////////////////////////
		if ( bufArray[i].Find(REGBegSearch) != -1 ) {
		
			strcpy(tempBuf,bufArray[i].GetBuffer(0));
			bufArray[i].ReleaseBuffer();
			tempChkStr = tempBuf;
			if (tempChkStr.GetAt(tempChkStr.GetLength()-1) == '|')
				strcat(tempBuf,"1");
			if (strchr(tempBuf,'\n') == NULL)
				strcat(tempBuf,"\n");
			bufArray[i] = tempBuf;
			strcpy(tempheader,strtok(tempBuf,"|"));
			strcpy(tempREGName,strtok(NULL,"|"));
			if (strstr(bufArray[i].GetBuffer(0),"|\n") == NULL )
				strcpy(tempREGVal,strtok(NULL,"\n"));
			else
				strcpy(tempREGVal,"");
			bufArray[i].ReleaseBuffer();
			if ( strcmp(tempREGName,"Description") == 0 )
				this->description = tempREGVal;
			else if ( strcmp(tempREGName,"CadFileName") == 0 )
				this->strCadFileName = tempREGVal;
			else if ( strcmp(tempREGName,"DBID") == 0 ) 
				this->DBID = atoi(tempREGVal);
			else if ( strcmp(tempREGName,"TimeHorizonValue") == 0 ) 
				this->duration = atoi(tempREGVal);
			else if ( strcmp(tempREGName,"TimeHorizonUnit") == 0 ) 
				this->timeHorizonUnit = atoi(tempREGVal);
			else if ( strcmp(tempREGName,"Units") == 0 ) 
				this->units = atoi(tempREGVal);
			else if ( strcmp(tempREGName,"Cost") == 0 ) 
				this->cost = (float)atof(tempREGVal);
			else if ( strcmp(tempREGName,"OriginalFacilityID") == 0 ) 
				this->originalFacilityId = atoi(tempREGVal);
			else if ( strcmp(tempREGName,"Region") == 0 ) 
				this->region = atoi(tempREGVal);
			else if ( strcmp(tempREGName,"SlotType") == 0 ) 
				this->slotType = atoi(tempREGVal);
			else if ( strcmp(tempREGName,"ClientNameOpened") == 0 ) 
				this->clientNameOpened = tempREGVal;
			else if ( strcmp(tempREGName,"IsChanged") == 0 )
				this->isChanged = tempREGVal;


		}
	
		/////////////////////////////////////////////////////////////
		//  UDFs 
		/////////////////////////////////////////////////////////////
		if ( bufArray[i].Find(UDFBegSearch) != -1 ) {
			strcpy(tempBuf,bufArray[i].GetBuffer(0));
			bufArray[i].ReleaseBuffer();
			strcpy(tempheader,strtok(tempBuf,"|"));
			strcpy(tempheader,strtok(NULL,"\n"));
			this->UDFList.Add(tempheader);
		}
		/////////////////////////////////////////////////////////////
		//  Holder Coordinate
		/////////////////////////////////////////////////////////////
		if ( (bufArray[i]).Find(CoordBegSearch) != -1 ) {
			tempArray.RemoveAll();
			tempArray.Add(bufArray[i]);
			i++;
			while ( (bufArray[i]).Find(CoordEndSearch) == -1 ) {
				tempArray.Add(bufArray[i]);
				i++;
			}
			tempArray.Add(bufArray[i]);
			this->coord.BuildFromStream(tempArray);
			tempArray.RemoveAll();
		}

		/////////////////////////////////////////////////////////////
		//  Pick Path List
		/////////////////////////////////////////////////////////////
		if ( (bufArray[i]).Find(PathBegSearch) != -1 ) {
			tempArray.RemoveAll();
			tempArray.Add(bufArray[i]);
			i++;
			while ( (bufArray[i]).Find(PathEndSearch) == -1 ) {
				tempArray.Add(bufArray[i]);
				i++;
			}
			tempArray.Add(bufArray[i]);
			tempPath.BuildFromStream(tempArray);
			this->pickPathList.Add(tempPath);
//			i++;
			tempArray.RemoveAll();
		}

		/////////////////////////////////////////////////////////////
		//  Section Objects
		/////////////////////////////////////////////////////////////
//		if ( (bufArray[i]).Find(SectionBegSearch) != -1 ) {
//			tempArray.RemoveAll();
//			tempArray.Add(bufArray[i]);
//			i++;
//			while ( (bufArray[i]).Find(SectionEndSearch) == -1 ) {
//				tempArray.Add(bufArray[i]);
//				i++;
//			}
//			tempArray.Add(bufArray[i]);
//			i++;
//			this->GetChildList().Add(BuildSectionFromStream(tempArray));
//		}
	}
	return;
}

void qqhSLOTFacility::StreamAttributes(CExeStringArray & attributBuf) {

	int i;
	CString tempBuf;

	tempBuf.Format("%s\n", FacilityBegSearch);
	attributBuf.Add(tempBuf);

	tempBuf.Format("%sDBID|%d\n", REGBegSearch, this->DBID );
	attributBuf.Add(tempBuf);

	tempBuf.Format("%sDescription|%s\n", REGBegSearch, this->description );
	attributBuf.Add(tempBuf);

	tempBuf.Format("%sType|%s\n", REGBegSearch, this->type);
	attributBuf.Add(tempBuf);

	tempBuf.Format("%sCadFileName|%s\n", REGBegSearch, this->strCadFileName);
	attributBuf.Add(tempBuf);

	tempBuf.Format("%sSlotType|%d\n", REGBegSearch, this->slotType);
	attributBuf.Add(tempBuf);

	tempBuf.Format("%sUnits|%d\n", REGBegSearch, this->units);
	attributBuf.Add(tempBuf);

	tempBuf.Format("%sRegion|%d\n", REGBegSearch, this->region);
	attributBuf.Add(tempBuf);

	tempBuf.Format("%sIsModified|%d\n", REGBegSearch, this->IsModified);
	attributBuf.Add(tempBuf);

	tempBuf.Format("%sTimeHorizonValue|%d\n", REGBegSearch, this->duration);
	attributBuf.Add(tempBuf);
	
	tempBuf.Format("%sTimeHorizonUnit|%d\n", REGBegSearch, this->timeHorizonUnit);
	attributBuf.Add(tempBuf);

	tempBuf.Format("%sCost|%f\n", REGBegSearch, this->cost);
	attributBuf.Add(tempBuf);

	tempBuf.Format("%sClientNameOpened|%s\n", REGBegSearch, this->clientNameOpened);
	attributBuf.Add(tempBuf);

	tempBuf.Format("%sIsChanged|%s\n", REGBegSearch, this->isChanged);
	attributBuf.Add(tempBuf);

	for (i = 0; i < UDFList.GetSize(); i++)
		attributBuf.Add(CString(CString(UDFBegSearch) + UDFList[i] + "\n"));

	this->coord.StreamAttributes(attributBuf);
	
	for ( i = 0; i < pickPathList.GetSize(); i++ )
		this->pickPathList[i].StreamAttributes(attributBuf);

//	for ( i = 0; i < this->childList.GetSize(); i++ ) {
//		StreamSectionAttributes(this->childList[i], attributBuf);
//	}

	attributBuf.Add(CString(FacilityEndSearch) + "\n");

	return;
}

////////////////////////////////////////
// SLOTLevelLaborProfile class
////////////////////////////////////////
qqhSLOTLevelLaborProfile::qqhSLOTLevelLaborProfile()
{
	DBID = 0;
	cube = fixedFactor = variableFactor = 0;
}

qqhSLOTLevelLaborProfile::qqhSLOTLevelLaborProfile(const qqhSLOTLevelLaborProfile& other)
{
	DBID = other.DBID;
	description = other.description;
	cube = other.cube;
	fixedFactor = other.fixedFactor;
	variableFactor = other.variableFactor;
	UDFList.Copy(other.UDFList);
}

qqhSLOTLevelLaborProfile& qqhSLOTLevelLaborProfile::operator=(const qqhSLOTLevelLaborProfile& other)
{
	DBID = other.DBID;
	description = other.description;
	cube = other.cube;
	fixedFactor = other.fixedFactor;
	variableFactor = other.variableFactor;
	UDFList.Copy(other.UDFList);
	return *this;
}

void qqhSLOTLevelLaborProfile::BuildFromStream(CExeStringArray &bufArray ) {
	char tempBuf[512];
	char tempREGName[100];
	char tempREGVal[256];
	char tempheader[100];
	int i;
	CExeStringArray tempArray;
	qqhSLOTPickPath tempPath;
	CString tempChkStr;

	for ( i = 0; i < bufArray.GetSize(); i++ ) {
		/////////////////////////////////////////////////////////////
		//  REGs are processed here
		/////////////////////////////////////////////////////////////
		if ( bufArray[i].Find(REGBegSearch) != -1 ) {
		
			strcpy(tempBuf,bufArray[i].GetBuffer(0));
			bufArray[i].ReleaseBuffer();
			tempChkStr = tempBuf;
			if (tempChkStr.GetAt(tempChkStr.GetLength()-1) == '|')
				strcat(tempBuf,"1");
			if (strchr(tempBuf,'\n') == NULL)
				strcat(tempBuf,"\n");
			bufArray[i] = tempBuf;
			strcpy(tempheader,strtok(tempBuf,"|"));
			strcpy(tempREGName,strtok(NULL,"|"));
			if (strstr(bufArray[i].GetBuffer(0),"|\n") == NULL )
				strcpy(tempREGVal,strtok(NULL,"\n"));
			else
				strcpy(tempREGVal,"");
			bufArray[i].ReleaseBuffer();
			if ( strcmp(tempREGName,"Description") == 0 )
				this->description = tempREGVal;
			else if ( strcmp(tempREGName,"DBID") == 0 ) 
				this->DBID = atoi(tempREGVal);
			else if ( strcmp(tempREGName,"Cube") == 0 )
				this->cube = (float)atof(tempREGVal);
			else if ( strcmp(tempREGName,"FixedFactor") == 0 )
				this->fixedFactor = (float)atof(tempREGVal);
			else if ( strcmp(tempREGName,"VariableFactor") == 0 )
				this->variableFactor = (float)atof(tempREGVal);
		}
	
		/////////////////////////////////////////////////////////////
		//  UDFs 
		/////////////////////////////////////////////////////////////
		if ( bufArray[i].Find(UDFBegSearch) != -1 ) {
			strcpy(tempBuf,bufArray[i].GetBuffer(0));
			bufArray[i].ReleaseBuffer();
			strcpy(tempheader,strtok(tempBuf,"|"));
			strcpy(tempheader,strtok(NULL,"\n"));
			this->UDFList.Add(tempheader);
		}
	}
	return;
}

void qqhSLOTLevelLaborProfile::StreamAttributes(CExeStringArray & attributBuf) {

	int i;
	CString tempBuf;

	tempBuf.Format("%s\n", LevelLaborProfileBegSearch);
	attributBuf.Add(tempBuf);

	tempBuf.Format("%sDBID|%d\n", REGBegSearch, this->DBID );
	attributBuf.Add(tempBuf);

	tempBuf.Format("%sDescription|%s\n", REGBegSearch, this->description );
	attributBuf.Add(tempBuf);

	tempBuf.Format("%sCube|%f\n", REGBegSearch, this->cube );
	attributBuf.Add(tempBuf);

	tempBuf.Format("%sFixedFactor|%f\n", REGBegSearch, this->fixedFactor );
	attributBuf.Add(tempBuf);

	tempBuf.Format("%sVariableFactor|%f\n", REGBegSearch, this->variableFactor );
	attributBuf.Add(tempBuf);

	for (i = 0; i < UDFList.GetSize(); i++)
		attributBuf.Add(CString(CString(UDFBegSearch) + UDFList[i] + "\n"));
	
	attributBuf.Add(CString(LevelLaborProfileEndSearch) + "\n");

	return;
}


////////////////////////////////////////
// qqhSLOTLocationProfile Member functions
////////////////////////////////////////

qqhSLOTLocationProfile::qqhSLOTLocationProfile()
{
	DBID = 0;
	width = depth = height = locationSpace = 0.0;
	isSelect = handlingMethod = 0;
	maxWeight = 0.0;
}

qqhSLOTLocationProfile::qqhSLOTLocationProfile(const qqhSLOTLocationProfile& other)
{
	DBID = other.DBID;
	description = other.description;
	width = other.width;
	depth = other.depth;
	height = other.height;
	coord = other.coord;
	locationSpace = other.locationSpace;
	isSelect = other.isSelect;
	handlingMethod = other.handlingMethod;
	maxWeight = other.maxWeight;
	UDFList.Copy(other.UDFList);
}

qqhSLOTLocationProfile& qqhSLOTLocationProfile::operator=(const qqhSLOTLocationProfile& other)
{
	DBID = other.DBID;
	description = other.description;
	width = other.width;
	depth = other.depth;
	height = other.height;
	coord = other.coord;
	locationSpace = other.locationSpace;
	isSelect = other.isSelect;
	handlingMethod = other.handlingMethod;
	maxWeight = other.maxWeight;
	UDFList.Copy(other.UDFList);
	return *this;
}

void qqhSLOTLocationProfile::BuildFromStream(CExeStringArray &bufArray ) {
	char tempBuf[512];
	char tempREGName[100];
	char tempREGVal[256];
	char tempheader[100];
	int i;
	CExeStringArray tempArray;
	qqhSLOTPickPath tempPath;
	float tempfloat;
	CString tempChkStr;

//	//FILE * fp = fopen("c:\\junk3.txt","w");
	for ( i = 0; i < bufArray.GetSize(); i++ ) {
		/////////////////////////////////////////////////////////////
		//  REGs are processed here
		/////////////////////////////////////////////////////////////
		if ( bufArray[i].Find(REGBegSearch) != -1 ) {
		
			strcpy(tempBuf,bufArray[i].GetBuffer(0));
			bufArray[i].ReleaseBuffer();
			tempChkStr = tempBuf;
			if (tempChkStr.GetAt(tempChkStr.GetLength()-1) == '|')
				strcat(tempBuf,"1");
			if (strchr(tempBuf,'\n') == NULL)
				strcat(tempBuf,"\n");
			////fprintf(fp,"%s",tempBuf);
			bufArray[i] = tempBuf;
			strcpy(tempheader,strtok(tempBuf,"|"));
			strcpy(tempREGName,strtok(NULL,"|"));
			if (strstr(bufArray[i].GetBuffer(0),"|\n") == NULL )
				strcpy(tempREGVal,strtok(NULL,"\n"));
			else
				strcpy(tempREGVal,"");
			bufArray[i].ReleaseBuffer();
			if ( strcmp(tempREGName,"Description") == 0 )
				this->description = tempREGVal;
			else if ( strcmp(tempREGName,"DBID") == 0 ) 
				this->DBID = atoi(tempREGVal);
			else if ( strcmp(tempREGName,"IsSelect") == 0 ) {
				if (strcmp(tempREGVal,"TRUE") == 0 )
					this->isSelect = 1;
				else
					this->isSelect = 0;
			}
			else if ( strcmp(tempREGName,"HandlingMethod") == 0 ) 
				this->handlingMethod = atoi(tempREGVal);
			else if ( strcmp(tempREGName,"LocationSpace") == 0 )
				this->locationSpace = (float)atof(tempREGVal);
			else if ( strcmp(tempREGName,"MaxWeight") == 0 )
				this->maxWeight = (float) atof(tempREGVal);
		}
	
		/////////////////////////////////////////////////////////////
		//  UDFs 
		/////////////////////////////////////////////////////////////
		if ( bufArray[i].Find(UDFBegSearch) != -1 ) {
			//fprintf(fp,"In UDFs\n");
			strcpy(tempBuf,bufArray[i].GetBuffer(0));
			bufArray[i].ReleaseBuffer();
			strcpy(tempheader,strtok(tempBuf,"|"));
			strcpy(tempheader,strtok(NULL,"\n"));
			this->UDFList.Add(tempheader);
		}
		/////////////////////////////////////////////////////////////
		//  Holder Coordinate
		/////////////////////////////////////////////////////////////
		if ( (bufArray[i]).Find(CoordBegSearch) != -1 ) {
			//fprintf(fp,"In coordinate\n");
			tempArray.RemoveAll();
			tempArray.Add(bufArray[i]);
			i++;
			while ( (bufArray[i]).Find(CoordEndSearch) == -1 ) {
				tempArray.Add(bufArray[i]);
				i++;
			}
			tempArray.Add(bufArray[i]);
			this->coord.BuildFromStream(tempArray);
			tempArray.RemoveAll();
		}
		if ( bufArray[i].Find(DimenBegSearch) != -1  ) {
			i++;
			while ( (bufArray[i].Find(DimenEndSearch) == -1 ) ) {
				strcpy(tempBuf,bufArray[i]);
				tempChkStr = tempBuf;
				if (tempChkStr.GetAt(tempChkStr.GetLength()-1) == '|')
					strcat(tempBuf,"1");
				if (strchr(tempBuf,'\n') == NULL)
					strcat(tempBuf,"\n");
				//fprintf(fp,"%s",tempBuf);
				strcpy(tempheader,strtok(tempBuf,"|"));
				strcpy(tempREGName,strtok(NULL,"|"));
				if (strstr(bufArray[i].GetBuffer(0),"|\n") == NULL )
					strcpy(tempREGVal,strtok(NULL,"\n"));
				else
					strcpy(tempREGVal,"");
				bufArray[i].ReleaseBuffer();
				if ( strcmp(tempREGName,"Width") == 0 ) {
					tempfloat = (float) (atof(tempREGVal));
					this->width = tempfloat;
				}
				else if ( strcmp(tempREGName,"Depth") == 0 ) {
					tempfloat = (float) (atof(tempREGVal));
					this->depth = tempfloat;
				}
				else if ( strcmp(tempREGName,"Height") == 0 ) {
					tempfloat = (float) (atof(tempREGVal));
					this->height = tempfloat;
				}
				i++;
			}
		}
	}
	//fclose(fp);
	return;
}

void qqhSLOTLocationProfile::StreamAttributes(CExeStringArray & attributBuf) {

	int i;
	CString tempBuf;

	tempBuf.Format("%s\n", LocationProfileBegSearch);
	attributBuf.Add(tempBuf);

	tempBuf.Format("%sDBID|%d\n", REGBegSearch, this->DBID );
	attributBuf.Add(tempBuf);

	if (this->isSelect == 1)
		tempBuf.Format("%sIsSelect|TRUE\n",REGBegSearch);
	else
		tempBuf.Format("%sIsSelect|FALSE\n",REGBegSearch);
	attributBuf.Add(tempBuf);

	tempBuf.Format("%sHandlingMethod|%d\n", REGBegSearch, this->handlingMethod );
	attributBuf.Add(tempBuf);

	tempBuf.Format("%sDescription|%s\n", REGBegSearch, this->description );
	attributBuf.Add(tempBuf);

	tempBuf.Format("%sLocationSpace|%f\n", REGBegSearch, this->locationSpace );
	attributBuf.Add(tempBuf);

	tempBuf.Format("%sMaxWeight|%f\n", REGBegSearch, this->maxWeight );
	attributBuf.Add(tempBuf);

	attributBuf.Add(CString(DimenBegSearch)+CString("\n"));

	tempBuf.Format("%sWidth|%f\n", REGBegSearch, this->width);
	attributBuf.Add(tempBuf);

	tempBuf.Format("%sDepth|%f\n", REGBegSearch, this->depth);
	attributBuf.Add(tempBuf);

	tempBuf.Format("%sHeight|%f\n", REGBegSearch, this->height);
	attributBuf.Add(tempBuf);
	
	attributBuf.Add(CString(DimenEndSearch)+CString("\n"));

	for (i = 0; i < UDFList.GetSize(); i++)
		attributBuf.Add(CString(CString(UDFBegSearch) + UDFList[i] + "\n"));

	this->coord.StreamAttributes(attributBuf);
	
	attributBuf.Add(CString(LocationProfileEndSearch) + "\n");

	return;
}

////////////////////////////////////////
// SLOTLevelProfile class
////////////////////////////////////////
qqhSLOTLevelProfile::qqhSLOTLevelProfile()
{
	DBID = 0;
	forkFixedInsertion = maxWeight = thickness = 0.0;
	isBarHidden = isRotateAllowed = isVariableLocationsAllowed = FALSE;
	relativeLevel = 0;
	overhang = facingGap = facingSnap = minLocWidth = productGap = productSnap = 0.0;
}

qqhSLOTLevelProfile::qqhSLOTLevelProfile(const qqhSLOTLevelProfile& other)
{
	DBID = other.DBID;
	description = other.description;
	coord = other.coord;
	forkFixedInsertion = other.forkFixedInsertion;
	isBarHidden = other.isBarHidden;
	isRotateAllowed = other.isRotateAllowed;
	isVariableLocationsAllowed = other.isVariableLocationsAllowed;
	locationProfileList.Copy(other.locationProfileList);
	levelLaborProfileList.Copy(other.levelLaborProfileList);
	maxWeight = other.maxWeight;
	relativeLevel = other.relativeLevel;
	thickness = other.thickness;
	overhang = other.overhang;
	facingGap = other.facingGap;
	facingSnap = other.facingSnap;
	minLocWidth = other.minLocWidth;
	productGap = other.productGap;
	productSnap = other.productSnap;
	UDFList.Copy(other.UDFList);
}

qqhSLOTLevelProfile& qqhSLOTLevelProfile::operator=(const qqhSLOTLevelProfile& other)
{
	DBID = other.DBID;
	description = other.description;
	coord = other.coord;
	forkFixedInsertion = other.forkFixedInsertion;
	isBarHidden = other.isBarHidden;
	isRotateAllowed = other.isRotateAllowed;
	isVariableLocationsAllowed = other.isVariableLocationsAllowed;
	locationProfileList.Copy(other.locationProfileList);
	levelLaborProfileList.Copy(other.levelLaborProfileList);
	maxWeight = other.maxWeight;
	relativeLevel = other.relativeLevel;
	thickness = other.thickness;
	overhang = other.overhang;
	facingGap = other.facingGap;
	facingSnap = other.facingSnap;
	minLocWidth = other.minLocWidth;
	productGap = other.productGap;
	productSnap = other.productSnap;
	UDFList.Copy(other.UDFList);
	return *this;
}

void qqhSLOTLevelProfile::BuildFromStream(CExeStringArray &bufArray ) {
	char tempBuf[512];
	char tempREGName[100];
	char tempREGVal[256];
	char tempheader[100];
	int i,j = 0;
	CExeStringArray tempArray;
	qqhSLOTPickPath tempPath;
	CString tempChkStr;

	//FILE * fp = fopen("c:\\junk2.txt","w");
	for ( i = 0; i < bufArray.GetSize(); i++ ) {
		/////////////////////////////////////////////////////////////
		//  REGs are processed here
		/////////////////////////////////////////////////////////////
		if ( bufArray[i].Find(REGBegSearch) != -1 ) {
		
			strcpy(tempBuf,bufArray[i].GetBuffer(0));
			bufArray[i].ReleaseBuffer();
			tempChkStr = tempBuf;
			if (tempChkStr.GetAt(tempChkStr.GetLength()-1) == '|')
				strcat(tempBuf,"1");
			if (strchr(tempBuf,'\n') == NULL)
				strcat(tempBuf,"\n");
			bufArray[i] = tempBuf;
			//fprintf(fp,"%s",tempBuf);
			strcpy(tempheader,strtok(tempBuf,"|"));
			strcpy(tempREGName,strtok(NULL,"|"));
			if (strstr(bufArray[i].GetBuffer(0),"|\n") == NULL )
				strcpy(tempREGVal,strtok(NULL,"\n"));
			else
				strcpy(tempREGVal,"");
			bufArray[i].ReleaseBuffer();
			if ( strcmp(tempREGName,"Description") == 0 )
				this->description = tempREGVal;
			else if ( strcmp(tempREGName,"DBID") == 0 ) 
				this->DBID = atoi(tempREGVal);
			else if ( strcmp(tempREGName,"ForkFixedInsertion") == 0 )
				this->forkFixedInsertion = (float)atof(tempREGVal);
			else if ( strcmp(tempREGName,"MaximumWeight") == 0 )
				this->maxWeight = (float)atof(tempREGVal);
			else if ( strcmp(tempREGName,"Thickness") == 0 )
				this->thickness = (float)atof(tempREGVal);
			else if ( strcmp(tempREGName,"IsBarHidden") == 0 ) {
				if (strcmp(tempREGVal,"TRUE") == 0 )
					this->isBarHidden = TRUE;
				else
					this->isBarHidden = FALSE;
			}
			else if ( strcmp(tempREGName,"IsRotateAllowed") == 0 ) {
				if (strcmp(tempREGVal,"TRUE") == 0 )
					this->isRotateAllowed = TRUE;
				else
					this->isRotateAllowed = FALSE;
			}
			else if ( strcmp(tempREGName,"IsVariableLocationsAllowed") == 0 ) {
				if (strcmp(tempREGVal,"TRUE") == 0 )
					this->isVariableLocationsAllowed = TRUE;
				else
					this->isVariableLocationsAllowed = FALSE;
			}
			else if ( strcmp(tempREGName,"RelativeLevel") == 0 )
				this->relativeLevel = atoi(tempREGVal);
			else if ( strcmp(tempREGName,"Overhang") == 0 )
				this->overhang = (float)atof(tempREGVal);
			else if ( strcmp(tempREGName,"FacingGap") == 0 )
				this->facingGap = (float)atof(tempREGVal);
			else if ( strcmp(tempREGName,"FacingSnap") == 0 )
				this->facingSnap = (float)atof(tempREGVal);
			else if ( strcmp(tempREGName,"MinimumLocWidth") == 0 )
				this->minLocWidth = (float)atof(tempREGVal);
			else if ( strcmp(tempREGName,"ProductGap") == 0 )
				this->productGap = (float)atof(tempREGVal);
			else if ( strcmp(tempREGName,"ProductSnap") == 0 )
				this->productSnap = (float)atof(tempREGVal);
		}
	
		/////////////////////////////////////////////////////////////
		//  UDFs 
		/////////////////////////////////////////////////////////////
		if ( bufArray[i].Find(UDFBegSearch) != -1 ) {
			strcpy(tempBuf,bufArray[i].GetBuffer(0));
			bufArray[i].ReleaseBuffer();
			strcpy(tempheader,strtok(tempBuf,"|"));
			strcpy(tempheader,strtok(NULL,"\n"));
			this->UDFList.Add(tempheader);
		}
		/////////////////////////////////////////////////////////////
		//  Holder Coordinate
		/////////////////////////////////////////////////////////////
		if ( (bufArray[i]).Find(CoordBegSearch) != -1 ) {
			tempArray.RemoveAll();
			tempArray.Add(bufArray[i]);
			i++;
			while ( (bufArray[i]).Find(CoordEndSearch) == -1 ) {
				tempArray.Add(bufArray[i]);
				i++;
			}
			tempArray.Add(bufArray[i]);
			this->coord.BuildFromStream(tempArray);
			tempArray.RemoveAll();
		}
		/////////////////////////////////////////////////////////////
		//  Locations
		/////////////////////////////////////////////////////////////
		if ( (bufArray[i]).Find(LocationProfileBegSearch) != -1 ) {
			qqhSLOTLocationProfile tempLocationProfile;
			tempArray.RemoveAll();
			tempArray.Add(bufArray[i]);
			i++;
			while ( (bufArray[i]).Find(LocationProfileEndSearch) == -1 ) {
				tempArray.Add(bufArray[i]);
				i++;
			}
			tempArray.Add(bufArray[i]);
			tempLocationProfile.BuildFromStream(tempArray);
			this->locationProfileList.Add(tempLocationProfile);
			tempArray.RemoveAll();
		}
		/////////////////////////////////////////////////////////////
		//  LevelLabors
		/////////////////////////////////////////////////////////////
		if ( (bufArray[i]).Find(LevelLaborProfileBegSearch) != -1 ) {
			qqhSLOTLevelLaborProfile tempLevelLaborProfile;
			tempArray.RemoveAll();
			tempArray.Add(bufArray[i]);
			i++;
			while ( (bufArray[i]).Find(LevelLaborProfileEndSearch) == -1 ) {
				tempArray.Add(bufArray[i]);
				i++;
			}
			tempArray.Add(bufArray[i]);
			tempLevelLaborProfile.BuildFromStream(tempArray);
			this->levelLaborProfileList.Add(tempLevelLaborProfile);
			tempArray.RemoveAll();
		}
	}
	//fprintf(fp,"Leaving\n");
	//fclose(fp);
	return;
}

void qqhSLOTLevelProfile::StreamAttributes(CExeStringArray & attributBuf) {

	int i;
	CString tempBuf;

	tempBuf.Format("%s\n", LevelProfileBegSearch);
	attributBuf.Add(tempBuf);

	tempBuf.Format("%sDBID|%d\n", REGBegSearch, this->DBID );
	attributBuf.Add(tempBuf);

	tempBuf.Format("%sDescription|%s\n", REGBegSearch, this->description );
	attributBuf.Add(tempBuf);

	tempBuf.Format("%sForkFixedInsertion|%f\n", REGBegSearch, this->forkFixedInsertion );
	attributBuf.Add(tempBuf);

	tempBuf.Format("%sMaximumWeight|%f\n", REGBegSearch, this->maxWeight );
	attributBuf.Add(tempBuf);

	tempBuf.Format("%sThickness|%f\n", REGBegSearch, this->thickness );
	attributBuf.Add(tempBuf);

	tempBuf.Format("%sRelativeLevel|%d\n", REGBegSearch, this->relativeLevel );
	attributBuf.Add(tempBuf);

	tempBuf.Format("%sOverhang|%f\n", REGBegSearch, this->overhang );
	attributBuf.Add(tempBuf);

	tempBuf.Format("%sFacingGap|%f\n", REGBegSearch, this->facingGap );
	attributBuf.Add(tempBuf);

	tempBuf.Format("%sFacingSnap|%f\n", REGBegSearch, this->facingSnap );
	attributBuf.Add(tempBuf);

	tempBuf.Format("%sMinimumLocWidth|%f\n", REGBegSearch, this->minLocWidth );
	attributBuf.Add(tempBuf);

	tempBuf.Format("%sProductGap|%f\n", REGBegSearch, this->productGap );
	attributBuf.Add(tempBuf);

	tempBuf.Format("%sProductSnap|%f\n", REGBegSearch, this->productSnap );
	attributBuf.Add(tempBuf);

	if (isBarHidden)
		tempBuf.Format("%sIsBarHidden|TRUE\n", REGBegSearch);
	else
		tempBuf.Format("%sIsBarHidden|FALSE\n", REGBegSearch);
	attributBuf.Add(tempBuf);

	if (isRotateAllowed)
		tempBuf.Format("%sIsRotateAllowed|TRUE\n", REGBegSearch);
	else
		tempBuf.Format("%sIsRotateAllowed|FALSE\n", REGBegSearch);
	attributBuf.Add(tempBuf);

	if (isVariableLocationsAllowed)
		tempBuf.Format("%sIsVariableLocationsAllowed|TRUE\n", REGBegSearch);
	else
		tempBuf.Format("%sIsVariableLocationsAllowed|FALSE\n", REGBegSearch);
	attributBuf.Add(tempBuf);

	for (i = 0; i < UDFList.GetSize(); i++)
		attributBuf.Add(CString(CString(UDFBegSearch) + UDFList[i] + "\n"));
	
	this->coord.StreamAttributes(attributBuf);

	for ( i = 0; i < locationProfileList.GetSize(); i++)
		locationProfileList[i].StreamAttributes(attributBuf);

	for ( i = 0; i < levelLaborProfileList.GetSize(); i++)
		levelLaborProfileList[i].StreamAttributes(attributBuf);

	attributBuf.Add(CString(LevelProfileEndSearch) + "\n");

	return;
}

////////////////////////////////////////
// qqhSLOTBayProfile Member functions
////////////////////////////////////////

qqhSLOTBayProfile::qqhSLOTBayProfile()
{
	DBID = 0;
	width = depth = height = barWidth = maxWeight = rackCost = 0.0;
	bayType = flowDifference = palletDepth = palletHeight = 0;
	isHazardRack = FALSE;

}

qqhSLOTBayProfile::qqhSLOTBayProfile(const qqhSLOTBayProfile& other)
{
	DBID = other.DBID;
	description = other.description;
	width = other.width;
	depth = other.depth;
	height = other.height;
	coord = other.coord;
	barWidth = other.barWidth;
	bayType = other.bayType;
	flowDifference = other.flowDifference;
	isHazardRack = other.isHazardRack;
	maxWeight = other.maxWeight;
	palletDepth = other.palletDepth;
	palletHeight = other.palletHeight;
	rackCost = other.rackCost;
	levelProfileList.Copy(other.levelProfileList);
	bayRule = other.bayRule;
	UDFList.Copy(other.UDFList);
}

qqhSLOTBayProfile& qqhSLOTBayProfile::operator=(const qqhSLOTBayProfile& other)
{
	DBID = other.DBID;
	description = other.description;
	width = other.width;
	depth = other.depth;
	height = other.height;
	coord = other.coord;
	barWidth = other.barWidth;
	bayType = other.bayType;
	flowDifference = other.flowDifference;
	isHazardRack = other.isHazardRack;
	maxWeight = other.maxWeight;
	palletDepth = other.palletDepth;
	palletHeight = other.palletHeight;
	rackCost = other.rackCost;
	levelProfileList.Copy(other.levelProfileList);
	bayRule = other.bayRule;
	UDFList.Copy(other.UDFList);
	return *this;
}

void qqhSLOTBayProfile::BuildFromStream(CExeStringArray &bufArray ) {
	char tempBuf[512];
	char tempREGName[100];
	char tempREGVal[256];
	char tempheader[100];
	int i;
	CExeStringArray tempArray;
	qqhSLOTPickPath tempPath;
	float tempfloat;
	CString tempChkStr;

	for ( i = 0; i < bufArray.GetSize(); i++ ) {
		/////////////////////////////////////////////////////////////
		//  REGs are processed here
		/////////////////////////////////////////////////////////////
		if ( bufArray[i].Find(REGBegSearch) != -1 ) {
		
			strcpy(tempBuf,bufArray[i].GetBuffer(0));
			bufArray[i].ReleaseBuffer();
			// to avoid strtok errors when no value after '|' sign
			tempChkStr = tempBuf;
			if (tempChkStr.GetAt(tempChkStr.GetLength()-1) == '|')
				strcat(tempBuf,"1");
			if (strchr(tempBuf,'\n') == NULL)
				strcat(tempBuf,"\n");
			bufArray[i] = tempBuf;
			strcpy(tempheader,strtok(tempBuf,"|"));
			strcpy(tempREGName,strtok(NULL,"|"));
			if (strstr(bufArray[i].GetBuffer(0),"|\n") == NULL )
				strcpy(tempREGVal,strtok(NULL,"\n"));
			else
				strcpy(tempREGVal,"");
			bufArray[i].ReleaseBuffer();
			if ( strcmp(tempREGName,"Description") == 0 )
				this->description = tempREGVal;
			else if ( strcmp(tempREGName,"DBID") == 0 ) 
				this->DBID = atoi(tempREGVal);
			else if ( strcmp(tempREGName,"BarWidth") == 0 )
				this->barWidth = (float)atof(tempREGVal);
			else if ( strcmp(tempREGName,"RackCost") == 0 )
				this->rackCost = (float)atof(tempREGVal);
			else if ( strcmp(tempREGName,"MaximumWeight") == 0 )
				this->maxWeight = (float)atof(tempREGVal);
			else if ( strcmp(tempREGName,"IsHazardRack") == 0 ) {
				if (strcmp(tempREGVal,"TRUE") == 0 )
					this->isHazardRack = TRUE;
				else
					this->isHazardRack = FALSE;
			}
			else if ( strcmp(tempREGName,"BayType") == 0 )
				this->bayType = atoi(tempREGVal);
			else if ( strcmp(tempREGName,"PalletDepth") == 0 )
				this->palletDepth = atoi(tempREGVal);
			else if ( strcmp(tempREGName,"PalletHeight") == 0 )
				this->palletHeight = atoi(tempREGVal);
			else if ( strcmp(tempREGName,"FlowDifference") == 0 )
				this->flowDifference = atoi(tempREGVal);
		}
	
		/////////////////////////////////////////////////////////////
		//  UDFs 
		/////////////////////////////////////////////////////////////
		if ( bufArray[i].Find(UDFBegSearch) != -1 ) {
			strcpy(tempBuf,bufArray[i].GetBuffer(0));
			bufArray[i].ReleaseBuffer();
			strcpy(tempheader,strtok(tempBuf,"|"));
			strcpy(tempheader,strtok(NULL,"\n"));
			this->UDFList.Add(tempheader);
		}
		/////////////////////////////////////////////////////////////
		//  BayRule
		/////////////////////////////////////////////////////////////
		if ( bufArray[i].Find(BayRuleBegSearch) != -1 ) {
			tempArray.RemoveAll();
			tempArray.Add(bufArray[i]);
			i++;
			while ( (bufArray[i].Find(BayRuleEndSearch) == -1 ) ) {
				tempArray.Add(bufArray[i]);
				i++;
			}
			tempArray.Add(bufArray[i]);
			bayRule.BuildFromStream(tempArray);
			tempArray.RemoveAll();
		}
		/////////////////////////////////////////////////////////////
		//  Holder Coordinate
		/////////////////////////////////////////////////////////////
		if ( (bufArray[i]).Find(CoordBegSearch) != -1 ) {
			tempArray.RemoveAll();
			tempArray.Add(bufArray[i]);
			i++;
			while ( (bufArray[i]).Find(CoordEndSearch) == -1 ) {
				tempArray.Add(bufArray[i]);
				i++;
			}
			tempArray.Add(bufArray[i]);
			this->coord.BuildFromStream(tempArray);
			tempArray.RemoveAll();
		}
		if ( bufArray[i].Find(DimenBegSearch) != -1  ) {
			i++;
			while ( (bufArray[i].Find(DimenEndSearch) == -1 ) ) {
				strcpy(tempBuf,bufArray[i]);
				strcpy(tempheader,strtok(tempBuf,"|"));
				strcpy(tempREGName,strtok(NULL,"|"));
				strcpy(tempREGVal,strtok(NULL,"\n"));
				if ( strcmp(tempREGName,"Width") == 0 ) {
					tempfloat = (float) (atof(tempREGVal));
					this->width = tempfloat;
				}
				else if ( strcmp(tempREGName,"Depth") == 0 ) {
					tempfloat = (float) (atof(tempREGVal));
					this->depth = tempfloat;
				}
				else if ( strcmp(tempREGName,"Height") == 0 ) {
					tempfloat = (float) (atof(tempREGVal));
					this->height = tempfloat;
				}
				i++;
			}
		}
		/////////////////////////////////////////////////////////////
		//  Levels
		/////////////////////////////////////////////////////////////
		if ( (bufArray[i]).Find(LevelProfileBegSearch) != -1 ) {
			qqhSLOTLevelProfile tempLevelProfile;
			tempArray.RemoveAll();
			tempArray.Add(bufArray[i]);
			i++;
			while ( (bufArray[i]).Find(LevelProfileEndSearch) == -1 ) {
				tempArray.Add(bufArray[i]);
				i++;
			}
			tempArray.Add(bufArray[i]);
			tempLevelProfile.BuildFromStream(tempArray);
			this->levelProfileList.Add(tempLevelProfile);
			tempArray.RemoveAll();
		}
	}
	return;
}

void qqhSLOTBayProfile::StreamAttributes(CExeStringArray & attributBuf) {

	int i;
	CString tempBuf;

	tempBuf.Format("%s\n", BayProfileBegSearch);
	attributBuf.Add(tempBuf);

	tempBuf.Format("%sDBID|%d\n", REGBegSearch, this->DBID );
	attributBuf.Add(tempBuf);

	tempBuf.Format("%sDescription|%s\n", REGBegSearch, this->description );
	attributBuf.Add(tempBuf);

	tempBuf.Format("%sBarWidth|%f\n", REGBegSearch, this->barWidth );
	attributBuf.Add(tempBuf);

	tempBuf.Format("%sRackCost|%f\n", REGBegSearch, this->rackCost );
	attributBuf.Add(tempBuf);

	tempBuf.Format("%sMaximumWeight|%f\n", REGBegSearch, this->maxWeight );
	attributBuf.Add(tempBuf);

	tempBuf.Format("%sBayType|%d\n", REGBegSearch, this->bayType );
	attributBuf.Add(tempBuf);

	tempBuf.Format("%sFlowDifference|%d\n", REGBegSearch, this->flowDifference );
	attributBuf.Add(tempBuf);

	tempBuf.Format("%sPalletDepth|%d\n", REGBegSearch, this->palletDepth );
	attributBuf.Add(tempBuf);

	tempBuf.Format("%sPalletHeight|%d\n", REGBegSearch, this->palletHeight );
	attributBuf.Add(tempBuf);

	if (isHazardRack)
		tempBuf.Format("%sIsHazardRack|TRUE\n", REGBegSearch);
	else
		tempBuf.Format("%sIsHazardRack|FALSE\n", REGBegSearch);
	attributBuf.Add(tempBuf);

	attributBuf.Add(CString(DimenBegSearch)+CString("\n"));

	tempBuf.Format("%sWidth|%f\n", REGBegSearch, this->width);
	attributBuf.Add(tempBuf);

	tempBuf.Format("%sDepth|%f\n", REGBegSearch, this->depth);
	attributBuf.Add(tempBuf);

	tempBuf.Format("%sHeight|%f\n", REGBegSearch, this->height);
	attributBuf.Add(tempBuf);
	
	attributBuf.Add(CString(DimenEndSearch)+CString("\n"));

	for (i = 0; i < UDFList.GetSize(); i++)
		attributBuf.Add(CString(CString(UDFBegSearch) + UDFList[i] + "\n"));

	this->coord.StreamAttributes(attributBuf);
	
	bayRule.StreamAttributes(attributBuf);

	for (i = 0; i < levelProfileList.GetSize(); i++)
		levelProfileList[i].StreamAttributes(attributBuf);

	attributBuf.Add(CString(BayProfileEndSearch) + "\n");

	return;
}

////////////////////////////////////////
// qqhSLOTSideProfile Member functions
////////////////////////////////////////

qqhSLOTSideProfile::qqhSLOTSideProfile()
{
	DBID = 0;
	maxBayDepth = maxBayHeight = sideTotalWidth = space = 0.0;
}

qqhSLOTSideProfile::qqhSLOTSideProfile(const qqhSLOTSideProfile& other)
{
	DBID = other.DBID;
	description = other.description;
	coord = other.coord;
	UDFList.Copy(other.UDFList);
	maxBayDepth = other.maxBayDepth;
	maxBayHeight = other.maxBayHeight;
	sideTotalWidth = other.sideTotalWidth;
	bayProfileList.Copy(other.bayProfileList);
	space = other.space;
}

qqhSLOTSideProfile& qqhSLOTSideProfile::operator=(const qqhSLOTSideProfile& other)
{
	DBID = other.DBID;
	description = other.description;
	coord = other.coord;
	UDFList.Copy(other.UDFList);
	maxBayDepth = other.maxBayDepth;
	maxBayHeight = other.maxBayHeight;
	sideTotalWidth = other.sideTotalWidth;
	bayProfileList.Copy(other.bayProfileList);
	space = other.space;
	return *this;
}

void qqhSLOTSideProfile::BuildFromStream(CExeStringArray &bufArray ) {
	char tempBuf[512];
	char tempREGName[100];
	char tempREGVal[256];
	char tempheader[100];
	int i;
	CExeStringArray tempArray;
	qqhSLOTPickPath tempPath;
	qqhSLOTBayProfile tempBayProfile;
	CString tempChkStr;

	for ( i = 0; i < bufArray.GetSize(); i++ ) {
		/////////////////////////////////////////////////////////////
		//  REGs are processed here
		/////////////////////////////////////////////////////////////
		if ( bufArray[i].Find(REGBegSearch) != -1 ) {
		
			strcpy(tempBuf,bufArray[i].GetBuffer(0));
			bufArray[i].ReleaseBuffer();
			tempChkStr = tempBuf;
			if (tempChkStr.GetAt(tempChkStr.GetLength()-1) == '|')
				strcat(tempBuf,"1");
			if (strchr(tempBuf,'\n') == NULL)
				strcat(tempBuf,"\n");
			bufArray[i] = tempBuf;
			strcpy(tempheader,strtok(tempBuf,"|"));
			strcpy(tempREGName,strtok(NULL,"|"));
			if (strstr(bufArray[i].GetBuffer(0),"|\n") == NULL )
				strcpy(tempREGVal,strtok(NULL,"\n"));
			else
				strcpy(tempREGVal,"");
			bufArray[i].ReleaseBuffer();
			if ( strcmp(tempREGName,"Description") == 0 )
				this->description = tempREGVal;
			else if ( strcmp(tempREGName,"DBID") == 0 ) 
				this->DBID = atoi(tempREGVal);
			else if ( strcmp(tempREGName,"MaximumBayDepth") == 0 )
				this->maxBayDepth = (float)atof(tempREGVal);
			else if ( strcmp(tempREGName,"MaximumBayHeight") == 0 )
				this->maxBayHeight = (float)atof(tempREGVal);
			else if ( strcmp(tempREGName,"SideTotalWidth") == 0 )
				this->sideTotalWidth = (float)atof(tempREGVal);
			else if ( strcmp(tempREGName,"Space") == 0 )
				this->space = (float)atof(tempREGVal);
		}
	
		/////////////////////////////////////////////////////////////
		//  UDFs 
		/////////////////////////////////////////////////////////////
		if ( bufArray[i].Find(UDFBegSearch) != -1 ) {
			strcpy(tempBuf,bufArray[i].GetBuffer(0));
			bufArray[i].ReleaseBuffer();
			strcpy(tempheader,strtok(tempBuf,"|"));
			strcpy(tempheader,strtok(NULL,"\n"));
			this->UDFList.Add(tempheader);
		}
		/////////////////////////////////////////////////////////////
		//  Holder Coordinate
		/////////////////////////////////////////////////////////////
		if ( (bufArray[i]).Find(CoordBegSearch) != -1 ) {
			tempArray.RemoveAll();
			tempArray.Add(bufArray[i]);
			i++;
			while ( (bufArray[i]).Find(CoordEndSearch) == -1 ) {
				tempArray.Add(bufArray[i]);
				i++;
			}
			tempArray.Add(bufArray[i]);
			this->coord.BuildFromStream(tempArray);
			tempArray.RemoveAll();
		}
		/////////////////////////////////////////////////////////////
		//  BayProfile
		/////////////////////////////////////////////////////////////
		if ( bufArray[i].Find(BayProfileBegSearch) != -1 ) {
			tempArray.RemoveAll();
			tempArray.Add(bufArray[i]);
			i++;
			while ( (bufArray[i].Find(BayProfileEndSearch) == -1 ) ) {
				tempArray.Add(bufArray[i]);
				i++;
			}
			tempArray.Add(bufArray[i]);
			qqhSLOTBayProfile tempBayProfile;
			tempBayProfile.BuildFromStream(tempArray);
			bayProfileList.Add(tempBayProfile);
			tempArray.RemoveAll();
		}
	}
	return;
}

void qqhSLOTSideProfile::StreamAttributes(CExeStringArray & attributBuf) {

	int i;
	CString tempBuf;

	tempBuf.Format("%s\n", SideProfileBegSearch);
	attributBuf.Add(tempBuf);

	tempBuf.Format("%sDBID|%d\n", REGBegSearch, this->DBID );
	attributBuf.Add(tempBuf);

	tempBuf.Format("%sDescription|%s\n", REGBegSearch, this->description );
	attributBuf.Add(tempBuf);

	tempBuf.Format("%sMaximumBayDepth|%f\n", REGBegSearch, this->maxBayDepth );
	attributBuf.Add(tempBuf);

	tempBuf.Format("%sMaximumBayHeight|%f\n", REGBegSearch, this->maxBayHeight );
	attributBuf.Add(tempBuf);

	tempBuf.Format("%sSideTotalWidth|%f\n", REGBegSearch, this->sideTotalWidth );
	attributBuf.Add(tempBuf);

	tempBuf.Format("%sSpace|%f\n", REGBegSearch, this->space );
	attributBuf.Add(tempBuf);

	for (i = 0; i < UDFList.GetSize(); i++)
		attributBuf.Add(CString(CString(UDFBegSearch) + UDFList[i] + "\n"));

	this->coord.StreamAttributes(attributBuf);

	for ( i = 0; i < bayProfileList.GetSize(); i++)
		this->bayProfileList[i].StreamAttributes(attributBuf);
	
	attributBuf.Add(CString(SideProfileEndSearch) + "\n");

	return;
}

////////////////////////////////////////
// qqhSLOTAisleProfile Member functions
////////////////////////////////////////

qqhSLOTAisleProfile::qqhSLOTAisleProfile()
{
	DBID = 0;
	side1Space = side2Space = aisleSpace = 0.0;
}

qqhSLOTAisleProfile::qqhSLOTAisleProfile(const qqhSLOTAisleProfile& other)
{
	DBID = other.DBID;
	description = other.description;
	coord = other.coord;
	UDFList.Copy(other.UDFList);
	side1Space = other.side1Space;
	side2Space = other.side2Space;
	aisleSpace = other.aisleSpace;
	side1Profile = other.side1Profile;
	side2Profile = other.side2Profile;
}

qqhSLOTAisleProfile& qqhSLOTAisleProfile::operator=(const qqhSLOTAisleProfile& other)
{
	DBID = other.DBID;
	description = other.description;
	coord = other.coord;
	UDFList.Copy(other.UDFList);
	side1Space = other.side1Space;
	side2Space = other.side2Space;
	aisleSpace = other.aisleSpace;
	side1Profile = other.side1Profile;
	side2Profile = other.side2Profile;
	return *this;
}

void qqhSLOTAisleProfile::BuildFromStream(CExeStringArray &bufArray ) {
	char tempBuf[512];
	char tempREGName[100];
	char tempREGVal[256];
	char tempheader[100];
	int i;
	CExeStringArray tempArray;
	qqhSLOTPickPath tempPath;

	BOOL first = TRUE;
	CString tempChkStr;

	for ( i = 0; i < bufArray.GetSize(); i++ ) {
		/////////////////////////////////////////////////////////////
		//  REGs are processed here
		/////////////////////////////////////////////////////////////
		if ( bufArray[i].Find(REGBegSearch) != -1 ) {
		
			strcpy(tempBuf,bufArray[i].GetBuffer(0));
			bufArray[i].ReleaseBuffer();
			tempChkStr = tempBuf;
			if (tempChkStr.GetAt(tempChkStr.GetLength()-1) == '|')
				strcat(tempBuf,"1");
			if (strchr(tempBuf,'\n') == NULL)
				strcat(tempBuf,"\n");
			bufArray[i] = tempBuf;
			strcpy(tempheader,strtok(tempBuf,"|"));
			strcpy(tempREGName,strtok(NULL,"|"));
			if (strstr(bufArray[i].GetBuffer(0),"|\n") == NULL )
				strcpy(tempREGVal,strtok(NULL,"\n"));
			else
				strcpy(tempREGVal,"");
			bufArray[i].ReleaseBuffer();
			if ( strcmp(tempREGName,"Description") == 0 )
				this->description = tempREGVal;
			else if ( strcmp(tempREGName,"DBID") == 0 ) 
				this->DBID = atoi(tempREGVal);
			else if ( strcmp(tempREGName,"AisleSpace") == 0 )
				this->aisleSpace = (float)atof(tempREGVal);
			}
	
		/////////////////////////////////////////////////////////////
		//  UDFs 
		/////////////////////////////////////////////////////////////
		if ( bufArray[i].Find(UDFBegSearch) != -1 ) {
			strcpy(tempBuf,bufArray[i].GetBuffer(0));
			bufArray[i].ReleaseBuffer();
			strcpy(tempheader,strtok(tempBuf,"|"));
			strcpy(tempheader,strtok(NULL,"\n"));
			this->UDFList.Add(tempheader);
		}
		/////////////////////////////////////////////////////////////
		//  Holder Coordinate
		/////////////////////////////////////////////////////////////
		if ( (bufArray[i]).Find(CoordBegSearch) != -1 ) {
			tempArray.RemoveAll();
			tempArray.Add(bufArray[i]);
			i++;
			while ( (bufArray[i]).Find(CoordEndSearch) == -1 ) {
				tempArray.Add(bufArray[i]);
				i++;
			}
			tempArray.Add(bufArray[i]);
			this->coord.BuildFromStream(tempArray);
			tempArray.RemoveAll();
		}
	}
	return;
}

void qqhSLOTAisleProfile::StreamAttributes(CExeStringArray & attributBuf) {

	int i;
	CString tempBuf;

	tempBuf.Format("%s\n", AisleProfileBegSearch);
	attributBuf.Add(tempBuf);

	tempBuf.Format("%sDBID|%d\n", REGBegSearch, this->DBID );
	attributBuf.Add(tempBuf);

	tempBuf.Format("%sDescription|%s\n", REGBegSearch, this->description );
	attributBuf.Add(tempBuf);

	tempBuf.Format("%sAisleSpace|%f\n", REGBegSearch, this->aisleSpace );
	attributBuf.Add(tempBuf);

	for (i = 0; i < UDFList.GetSize(); i++)
		attributBuf.Add(CString(CString(UDFBegSearch) + UDFList[i] + "\n"));

	this->coord.StreamAttributes(attributBuf);

	// add the side1 and side2 profiles to the aisle profile
	if (this->side1Profile.getDescription() != "{NONE}")
		this->side1Profile.StreamAttributes(attributBuf);
	if (this->side2Profile.getDescription() != "{NONE}")
		this->side2Profile.StreamAttributes(attributBuf);

	attributBuf.Add(CString(AisleProfileEndSearch) + "\n");

	return;
}

////////////////////////////////////////
// qqhSLOTBayRule Member functions
////////////////////////////////////////

qqhSLOTBayRule::qqhSLOTBayRule()
{
	DBID = 0;
	palletHeight = pctUtilSelPos = pctUtilRsvPos = pctRsvToSelPos = clearance = addlResvCube = 0.0;
	desiredRplnPerWeek = 0.0;
	type = "SLOTBayRule";
}

qqhSLOTBayRule::qqhSLOTBayRule(const qqhSLOTBayRule& other)
{
	DBID = other.DBID;
	description = other.description;
	UDFList.Copy(other.UDFList);
	palletHeight = other.palletHeight;
	pctUtilSelPos = other.pctUtilSelPos;
	pctUtilRsvPos = other.pctUtilRsvPos;
	pctRsvToSelPos = other.pctRsvToSelPos;
	desiredRplnPerWeek = other.desiredRplnPerWeek;
	clearance = other.clearance;
	addlResvCube = other.addlResvCube;
	facingInfoList.Copy(other.facingInfoList);
}

qqhSLOTBayRule& qqhSLOTBayRule::operator=(const qqhSLOTBayRule& other)
{
	DBID = other.DBID;
	description = other.description;
	UDFList.Copy(other.UDFList);
	palletHeight = other.palletHeight;
	pctUtilSelPos = other.pctUtilSelPos;
	pctUtilRsvPos = other.pctUtilRsvPos;
	pctRsvToSelPos = other.pctRsvToSelPos;
	desiredRplnPerWeek = other.desiredRplnPerWeek;
	clearance = other.clearance;
	addlResvCube = other.addlResvCube;
	facingInfoList.Copy(other.facingInfoList);
	return *this;
}

void qqhSLOTBayRule::BuildFromStream(CExeStringArray &bufArray ) {
	char tempBuf[512];
	char tempREGName[100];
	char tempREGVal[256];
	char tempheader[100];
	int i;
	CExeStringArray tempArray;
	CString tempChkStr;

	for ( i = 0; i < bufArray.GetSize(); i++ ) {
		/////////////////////////////////////////////////////////////
		//  REGs are processed here
		/////////////////////////////////////////////////////////////
		if ( bufArray[i].Find(REGBegSearch) != -1 ) {
		
			strcpy(tempBuf,bufArray[i].GetBuffer(0));
			bufArray[i].ReleaseBuffer();
			tempChkStr = tempBuf;
			if (tempChkStr.GetAt(tempChkStr.GetLength()-1) == '|')
				strcat(tempBuf,"1");
			if (strchr(tempBuf,'\n') == NULL)
				strcat(tempBuf,"\n");
			bufArray[i] = tempBuf;
			strcpy(tempheader,strtok(tempBuf,"|"));
			strcpy(tempREGName,strtok(NULL,"|"));
			if (strstr(bufArray[i].GetBuffer(0),"|\n") == NULL )
				strcpy(tempREGVal,strtok(NULL,"\n"));
			else
				strcpy(tempREGVal,"");
			bufArray[i].ReleaseBuffer();
			if ( strcmp(tempREGName,"Description") == 0 )
				this->description = tempREGVal;
			else if ( strcmp(tempREGName,"DBID") == 0 ) 
				this->DBID = atoi(tempREGVal);
			else if ( strcmp(tempREGName,"PctUtilSelPos") == 0 ) 
				this->pctUtilSelPos = (float) atof(tempREGVal);
			else if ( strcmp(tempREGName,"PctUtilRsvPos") == 0 ) 
				this->pctUtilRsvPos = (float) atof(tempREGVal);
			else if ( strcmp(tempREGName,"PctRsvToSelPos") == 0 ) 
				this->pctRsvToSelPos = (float) atof(tempREGVal);
			else if ( strcmp(tempREGName,"DesiredRplnPerWeek") == 0 ) 
				this->desiredRplnPerWeek = (float) atof(tempREGVal);
			else if ( strcmp(tempREGName,"Clearance") == 0 ) 
				this->clearance = (float) atof(tempREGVal);
			else if ( strcmp(tempREGName,"AdditionalRsvCube") == 0 ) 
				this->addlResvCube = (float) atof(tempREGVal);
			else if ( strcmp(tempREGName,"PalletHeight") == 0 ) 
				this->palletHeight = (float) atof(tempREGVal);
		}
	
		/////////////////////////////////////////////////////////////
		//  UDFs 
		/////////////////////////////////////////////////////////////
		if ( bufArray[i].Find(UDFBegSearch) != -1 ) {
			strcpy(tempBuf,bufArray[i].GetBuffer(0));
			bufArray[i].ReleaseBuffer();
			strcpy(tempheader,strtok(tempBuf,"|"));
			strcpy(tempheader,strtok(NULL,"\n"));
			this->UDFList.Add(tempheader);
		}

		// facing info list
		if ( bufArray[i].Find(FacingInfoBegSearch) != -1  ) {
			qqhSLOTFacingInfo tempFacingInfo;
			tempArray.RemoveAll();
			tempArray.Add(bufArray[i]);
			i++;
			while ( (bufArray[i]).Find(FacingInfoEndSearch) == -1 ) {
				tempArray.Add(bufArray[i]);
				i++;
			}
			tempArray.Add(bufArray[i]);
			tempFacingInfo.BuildFromStream(tempArray);
			this->facingInfoList.Add(tempFacingInfo);
		}
	}
	return;
}

void qqhSLOTBayRule::StreamAttributes(CExeStringArray & attributBuf) {

	int i, j;
	CString tempBuf;
	CExeStringArray tempArray;

	tempBuf.Format("%s\n", BayRuleBegSearch);
	attributBuf.Add(tempBuf);

	tempBuf.Format("%sDBID|%d\n", REGBegSearch, this->DBID );
	attributBuf.Add(tempBuf);

	tempBuf.Format("%sDescription|%s\n", REGBegSearch, this->description );
	attributBuf.Add(tempBuf);

	tempBuf.Format("%sPalletHeight|%f\n", REGBegSearch, this->palletHeight );
	attributBuf.Add(tempBuf);

	tempBuf.Format("%sPctUtilSelPos|%f\n", REGBegSearch, this->pctUtilSelPos );
	attributBuf.Add(tempBuf);

	tempBuf.Format("%sPctUtilRsvPos|%f\n", REGBegSearch, this->pctUtilRsvPos );
	attributBuf.Add(tempBuf);

	tempBuf.Format("%sPctRsvToSelPos|%f\n", REGBegSearch, this->pctRsvToSelPos );
	attributBuf.Add(tempBuf);

	tempBuf.Format("%sDesiredRplnPerWeek|%f\n", REGBegSearch, this->desiredRplnPerWeek );
	attributBuf.Add(tempBuf);

	tempBuf.Format("%sClearance|%f\n", REGBegSearch, this->clearance );
	attributBuf.Add(tempBuf);

	tempBuf.Format("%sAdditionalRsvCube|%f\n", REGBegSearch, this->addlResvCube );
	attributBuf.Add(tempBuf);

	for (i = 0; i < UDFList.GetSize(); i++)
		attributBuf.Add(CString(CString(UDFBegSearch) + UDFList[i] + "\n"));

	if ( this->facingInfoList.GetSize() > 0 )
	{
		for ( i = 0; i < this->facingInfoList.GetSize(); i++ )
		{
			tempArray.RemoveAll();
			facingInfoList[i].StreamAttributes(tempArray);
			for ( j = 0; j < tempArray.GetSize(); j++)
				attributBuf.Add(tempArray[j]);
		}
	}

	attributBuf.Add(CString(BayRuleEndSearch) + "\n");

	return;
}

////////////////////////////////////////
// qqhSLOTFacingInfo Member functions
////////////////////////////////////////

qqhSLOTFacingInfo::qqhSLOTFacingInfo()
{
	DBID = 0;
	extendedCube = extendedBOH = 0.0;
	facingCount = 1;
	type = "SLOTFacingInfo";
}

qqhSLOTFacingInfo::qqhSLOTFacingInfo(const qqhSLOTFacingInfo& other)
{
	DBID = other.DBID;
	description = other.description;
	facingCount = other.facingCount;
	extendedCube = other.extendedCube;
	extendedBOH = other.extendedBOH;
	UDFList.Copy(other.UDFList);
}

qqhSLOTFacingInfo& qqhSLOTFacingInfo::operator=(const qqhSLOTFacingInfo& other)
{
	DBID = other.DBID;
	description = other.description;
	facingCount = other.facingCount;
	extendedCube = other.extendedCube;
	extendedBOH = other.extendedBOH;
	UDFList.Copy(other.UDFList);
	return *this;
}

void qqhSLOTFacingInfo::BuildFromStream(CExeStringArray &bufArray ) {
	char tempBuf[512];
	char tempREGName[100];
	char tempREGVal[256];
	char tempheader[100];
	int i;
	CExeStringArray tempArray;
	CString tempChkStr;

	for ( i = 0; i < bufArray.GetSize(); i++ ) {
		/////////////////////////////////////////////////////////////
		//  REGs are processed here
		/////////////////////////////////////////////////////////////
		if ( bufArray[i].Find(REGBegSearch) != -1 ) {
		
			strcpy(tempBuf,bufArray[i].GetBuffer(0));
			bufArray[i].ReleaseBuffer();
			tempChkStr = tempBuf;
			if (tempChkStr.GetAt(tempChkStr.GetLength()-1) == '|')
				strcat(tempBuf,"1");
			if (strchr(tempBuf,'\n') == NULL)
				strcat(tempBuf,"\n");
			bufArray[i] = tempBuf;
			strcpy(tempheader,strtok(tempBuf,"|"));
			strcpy(tempREGName,strtok(NULL,"|"));
			if (strstr(bufArray[i].GetBuffer(0),"|\n") == NULL )
				strcpy(tempREGVal,strtok(NULL,"\n"));
			else
				strcpy(tempREGVal,"");
			bufArray[i].ReleaseBuffer();
			if ( strcmp(tempREGName,"Description") == 0 )
				this->description = tempREGVal;
			else if ( strcmp(tempREGName,"DBID") == 0 ) 
				this->DBID = atoi(tempREGVal);
			else if ( strcmp(tempREGName,"FacingCount") == 0 )
				this->facingCount = atoi(tempREGVal);
			else if ( strcmp(tempREGName,"ExtendedCube") == 0 )
				this->extendedCube = (float)atof(tempREGVal);
			else if ( strcmp(tempREGName,"ExtendedBOH") == 0 )
				this->extendedBOH = (float)atof(tempREGVal);
		}
	
		/////////////////////////////////////////////////////////////
		//  UDFs 
		/////////////////////////////////////////////////////////////
		if ( bufArray[i].Find(UDFBegSearch) != -1 ) {
			strcpy(tempBuf,bufArray[i].GetBuffer(0));
			bufArray[i].ReleaseBuffer();
			strcpy(tempheader,strtok(tempBuf,"|"));
			strcpy(tempheader,strtok(NULL,"\n"));
			this->UDFList.Add(tempheader);
		}
	}
	return;
}

void qqhSLOTFacingInfo::StreamAttributes(CExeStringArray & attributBuf) {

	int i;
	CString tempBuf;

	tempBuf.Format("%s\n", FacingInfoBegSearch);
	attributBuf.Add(tempBuf);

	tempBuf.Format("%sDBID|%d\n", REGBegSearch, this->DBID );
	attributBuf.Add(tempBuf);

	tempBuf.Format("%sDescription|%s\n", REGBegSearch, this->description );
	attributBuf.Add(tempBuf);

	tempBuf.Format("%sFacingCount|%d\n", REGBegSearch, this->facingCount );
	attributBuf.Add(tempBuf);

	tempBuf.Format("%sExtendedCube|%f\n", REGBegSearch, this->extendedCube );
	attributBuf.Add(tempBuf);

	tempBuf.Format("%sExtendedBOH|%f\n", REGBegSearch, this->extendedBOH );
	attributBuf.Add(tempBuf);

	for (i = 0; i < UDFList.GetSize(); i++)
		attributBuf.Add(CString(CString(UDFBegSearch) + UDFList[i] + "\n"));
	
	attributBuf.Add(CString(FacingInfoEndSearch) + "\n");

	return;
}
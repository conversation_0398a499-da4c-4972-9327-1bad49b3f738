// SucceedReportsDoc.h : interface of the CSucceedReportsDoc class
//
/////////////////////////////////////////////////////////////////////////////

#if !defined(AFX_SUCCEEDREPORTSDOC_H__749C5A3B_EDCF_11D2_9CD9_0080C742D9DF__INCLUDED_)
#define AFX_SUCCEEDREPORTSDOC_H__749C5A3B_EDCF_11D2_9CD9_0080C742D9DF__INCLUDED_

#pragma once

#include "SUCCReport.h"

class CSucceedReportsDoc : public CDocument
{
protected: // create from serialization only
	CSucceedReportsDoc();
	DECLARE_DYNCREATE(CSucceedReportsDoc)

// Attributes
public:

	CSUCCReport m_report;

// Operations
public:

// Overrides
	// ClassWizard generated virtual function overrides
	//{{AFX_VIRTUAL(CSucceedReportsDoc)
	public:
	virtual BOOL OnNewDocument();
	virtual void Serialize(CArchive& ar);
	virtual BOOL OnOpenDocument(LPCTSTR lpszPathName);
	//}}AFX_VIRTUAL

// Implementation
public:
	CString m_ReportName;
	CString m_FacilityName;
	CString m_UserName;
	void ReadUser();
	virtual ~CSucceedReportsDoc();

#ifdef _DEBUG
	virtual void AssertValid() const;
	virtual void Dump(CDumpContext& dc) const;
#endif

protected:

// Generated message map functions
protected:
	DECLARE_MESSAGE_MAP()

private:

};



#endif // !defined(AFX_SUCCEEDREPORTSDOC_H__749C5A3B_EDCF_11D2_9CD9_0080C742D9DF__INCLUDED_)

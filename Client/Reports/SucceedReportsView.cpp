// SucceedReportsView.cpp : implementation of the CSucceedReportsView class
//

#include "stdafx.h"
#include "SucceedReports.h"

#include "SucceedReportsDoc.h"
#include "SucceedReportsView.h"

#include "excel8.h"

#ifdef _DEBUG
#define new DEBUG_NEW
#endif

#include "BuildReports.h"
//#include "ProgressThread.h"
#include "Progress.h"
#include "afxmt.h"
#include <errno.h>

#define MAX_ROWS 65000


CPoint pageEndPoint;
CPoint pageStartPoint;
BOOL bFirst = TRUE;
extern int bFirstTime;
extern int reportTypeParam;
extern BOOL bBuildingReport;

BOOL bExportingToText;
CFile fText;

/////////////////////////////////////////////////////////////////////////////
// CSucceedReportsView

IMPLEMENT_DYNCREATE(CSucceedReportsView, CScrollView)

BEGIN_MESSAGE_MAP(CSucceedReportsView, CScrollView)
	//{{AFX_MSG_MAP(CSucceedReportsView)
	ON_COMMAND(ID_FILE_EXPORT_TOTEXT, OnFileExportToText)
	ON_COMMAND(ID_FILE_EXPORT_DELIMITED, OnFileExportDelimited)
	ON_COMMAND(ID_EXCEL, OnExcel)
	//}}AFX_MSG_MAP
	// Standard printing commands
	ON_COMMAND(ID_FILE_PRINT, CScrollView::OnFilePrint)
	ON_COMMAND(ID_FILE_PRINT_DIRECT, CScrollView::OnFilePrint)
	ON_COMMAND(ID_FILE_PRINT_PREVIEW, CScrollView::OnFilePrintPreview)
END_MESSAGE_MAP()

/////////////////////////////////////////////////////////////////////////////
// CSucceedReportsView construction/destruction

CSucceedReportsView::CSucceedReportsView()
{
	// TODO: add construction code here
	m_nPage = 0;
}


CSucceedReportsView::~CSucceedReportsView()
{
}


BOOL CSucceedReportsView::PreCreateWindow(CREATESTRUCT& cs)
{
	// TODO: Modify the Window class or styles here by modifying
	//  the CREATESTRUCT cs

	return CScrollView::PreCreateWindow(cs);
}




/////////////////////////////////////////////////////////////////////////////
// CSucceedReportsView drawing

void CSucceedReportsView::OnDraw(CDC* pDC)
{
	CSucceedReportsDoc* pDoc;
	CWinApp *currentApp = AfxGetApp();
	CWnd *myWnd = GetParent();
	


	pDoc = GetDocument();

	if (pDoc->m_report.GetBodySize() == 0) {
		if (! bBuildingReport) {
			pDoc->m_report.AddBodyLine("");
			pDoc->m_report.AddBodyLine("");
			pDoc->m_report.AddBodyLine("<B>No records found</B>");

			UpdateScrollSizes(pDC);
		}
		return;
	}



	
	ASSERT_VALID(pDoc);

	int i, nHeight, n;
	CString str;
	CPoint point(720, -1440);
	CFont font;
	CFont boldFont;
	CFont *pOldFont = NULL;
	TEXTMETRIC tm;
	CString temp;
	SIZE strSize;
	pDC->SetMapMode(MM_TWIPS);
	pageStartPoint = point;
	m_nPage = 1;

	//m_nPage++; //pInfo->m_nCurPage;    //for PrintPageFooter's benefit
	//nStart = (m_nPage - 1) * pDoc->nLinesPerPage;
	//nEnd = nStart + pDoc->nLinesPerPage;

	//10-point fixed-pitch font
	//10-point * 20 = 200
	//8-point fixed-pitch font
	//8-point * 20 = 160
	font.CreateFont(-200, 0, 0, 0, 400, FALSE, FALSE,
				0, ANSI_CHARSET, OUT_DEFAULT_PRECIS,
				CLIP_DEFAULT_PRECIS, DEFAULT_QUALITY,
				DEFAULT_PITCH | FF_MODERN, "Courier New");

	boldFont.CreateFont(-200, 0, 0, 0, FW_BOLD, FALSE, FALSE,
				0, ANSI_CHARSET, OUT_DEFAULT_PRECIS,
				CLIP_DEFAULT_PRECIS, DEFAULT_QUALITY,
				DEFAULT_PITCH | FF_MODERN, "Courier New");

	if (! pDC->IsPrinting())
		pOldFont = (CFont *) (pDC->SelectObject(&font));


	if (bFirst)
	{
		//set the scroll sizes
		UpdateScrollSizes(pDC);
		bFirst = FALSE;
	}

	pOldFont = (CFont *) (pDC->SelectObject(&boldFont));
	PrintPageHeader(pDC, 1);
	pOldFont = (CFont *) (pDC->SelectObject(&font));

	pDC->GetTextMetrics(&tm);
	nHeight = tm.tmHeight + tm.tmExternalLeading;
	point = pageStartPoint;
	for (i = 0; i < pDoc->m_report.GetBodySize(); i++)
	{

		pDoc->m_report.GetBodyLineAt(str, i);
		if (str == "<P>")		// for printer spacing, skip in on-screen mode
			continue;
		point.y -= nHeight;
		point.x = 720;

		n = str.Find("<B>");		// start bold
		if (n < 0)
			pDC->TextOut(point.x, point.y, str);
		else {
			//AfxMessageBox(str.Left(n), MB_OK);
			while (! str.IsEmpty()) {
				
				//	AfxMessageBox(str.Left(n), MB_OK);
				temp.Format("point.x = %d", point.x);
				//	AfxMessageBox(temp, MB_OK);
				pDC->TextOut(point.x, point.y, str.Left(n));
				GetTextExtentPoint32(pDC->m_hDC, str.Left(n), n, &strSize);
				point.x += strSize.cx;
				str = str.Right(str.GetLength() - (n+3));
				//	AfxMessageBox(str, MB_OK);
				
				if (! str.IsEmpty()) {
					n = str.Find("</B>");
					if (n < 0)
						n = str.GetLength();
					pOldFont = (CFont *) (pDC->SelectObject(&boldFont));
					temp.Format("point.x = %d", point.x);
					//	AfxMessageBox(temp, MB_OK);
					pDC->TextOut(point.x, point.y, str.Left(n));
					
					GetTextExtentPoint32(pDC->m_hDC, str.Left(n), n, &strSize);
					point.x += strSize.cx;
					if (n < str.GetLength())
						str = str.Right(str.GetLength() - (n+4));
					else
						str = "";
					//	AfxMessageBox(str, MB_OK);
					pOldFont = (CFont *) (pDC->SelectObject(&font));
					n = str.Find("<B>");
					if ( n < 0 ) {
						pDC->TextOut(point.x, point.y, str);
						str = "";
					}
				}
			}
		}

	
	}

	pageEndPoint = point;
	point.y -= nHeight;

//	if (i >= pDoc->m_report.GetBodySize())
//		PrintPageFooter(pDC, 1);

  if (pOldFont != NULL && ! pDC->IsPrinting())
		pDC->SelectObject(pOldFont);

}


/////////////////////////////////////////////////////////////////////////////
// CSucceedReportsView printing

BOOL CSucceedReportsView::OnPreparePrinting(CPrintInfo* pInfo)
{
	// default preparation

	CSucceedReportsDoc *pDoc = GetDocument();
	pInfo->SetMaxPage(pDoc->m_report.GetReportSize() /
		pDoc->m_report.m_nLinesPerPage + 1);

	//do not show the print setup dialog box
	//pInfo->m_bDirect = TRUE;

	//get the printer default values
	//pInfo->m_pPD->GetDefaults();


	int ret = DoPreparePrinting(pInfo);

	return ret;
}


void CSucceedReportsView::OnBeginPrinting(CDC* pDC, CPrintInfo* pInfo)
{
	//AfxMessageBox("Before create font", MB_OK);
	int fontPitch;
	int cpl;

	CSucceedReportsDoc *pDoc = GetDocument();
	cpl = pDoc->m_report.m_nCharactersPerLine;

	if (cpl > 132)
		fontPitch = 7;
	else if (cpl > 80)
		fontPitch = 8;
	else 
		fontPitch = 10;

	printFont.CreateFont(fontPitch * -20, 0, 0, 0, 400, FALSE, FALSE,
				0, ANSI_CHARSET, OUT_DEFAULT_PRECIS,
				CLIP_DEFAULT_PRECIS, DEFAULT_QUALITY,
				DEFAULT_PITCH | FF_MODERN, "Courier New");

	printBoldFont.CreateFont(fontPitch * -20, 0, 0, 0, FW_BOLD, FALSE, FALSE,
			0, ANSI_CHARSET, OUT_DEFAULT_PRECIS,
			CLIP_DEFAULT_PRECIS, DEFAULT_QUALITY,
			DEFAULT_PITCH | FF_MODERN, "Courier New");

	//AfxMessageBox("Created font.", MB_OK);

	return;	
}


void CSucceedReportsView::OnEndPrinting(CDC* pDC, CPrintInfo* pInfo)
{
	// TODO: add cleanup after printing
	printFont.DeleteObject();
	printBoldFont.DeleteObject();
}


void CSucceedReportsView::PrintPageHeader(CDC *pDC, int fromWhere)
{
	CString headerLine;
	CString tString1;
	CString tString2;
	CString reportName;
	CString facilityName;
	CString userName;
	CTime theTime = CTime::GetCurrentTime();
	int sp1, sp2;
	TEXTMETRIC tm;
	int nHeight;
	int charactersPerLine;
	int nMax;			
	int nColonPos;		
	CString sPageNum;

	CPoint point(720, 0);
	CSucceedReportsDoc *pDoc = GetDocument();


	pDC->GetTextMetrics(&tm);
	nHeight = tm.tmHeight + tm.tmExternalLeading;

	reportName = pDoc->m_report.m_ReportName;
	facilityName = pDoc->m_report.m_FacilityName;
	userName = pDoc->m_UserName;
	sPageNum.Format("%d", m_nPage);
	
	// see which of the three header lines has the largest field on the right
	nMax = sPageNum.GetLength();
	if (nMax < userName.GetLength())
		nMax = userName.GetLength();
	if (nMax < facilityName.GetLength())
		nMax = facilityName.GetLength();

	charactersPerLine = pDoc->m_report.m_nCharactersPerLine;

	// the position of the colon on the right part of the header
	nColonPos = charactersPerLine - nMax - 2;

	tString1 = "SSA Global Technologies";
	tString2.Format("Page: %d", m_nPage);
	sp1 = (charactersPerLine/2) - (reportName.GetLength()/2); // space between left and center
	sp2 = nColonPos - sp1 - reportName.GetLength() - 4; // space between center and right
	headerLine.Format("%-*s%s%*s%s", sp1, tString1, reportName, sp2, " ",tString2);
	point.y -= nHeight;
	pDC->TextOut(point.x, point.y, headerLine);

	tString1 = "SSA Optimize";
	tString2 = "Facility: " + facilityName;
	sp1 = nColonPos - tString1.GetLength() - 8;		// space between left and right
	headerLine.Format("%-s%*s%s", tString1, sp1, " ", tString2);
	point.y -= nHeight;
	pDC->TextOut(point.x, point.y, headerLine);

	tString1 = theTime.Format("%m/%d/%Y %H:%M");
	tString2 = "User: " + userName;
	sp1 = nColonPos - tString1.GetLength() - 4;		// space between left and right
	headerLine.Format("%-s%*s%s", tString1, sp1, " ", tString2);
	point.y -= nHeight;
	pDC->TextOut(point.x, point.y, headerLine);

	headerLine.Format("%-*s", charactersPerLine, " ");
	point.y -= nHeight;
	pDC->TextOut(point.x, point.y, headerLine);


	for (int i = 0; i < pDoc->m_report.GetHeaderSize() - 6; i++)
	{
		pDoc->m_report.GetHeaderLineAt(headerLine, i);
		point.y -= nHeight;
		pDC->TextOut(point.x, point.y, headerLine);
	}

	pageStartPoint = point;

}


void CSucceedReportsView::PrintPageFooter(CDC *pDC, int fromWhere)
{
	CString str;
	CPoint point(720, -14400);   //Move 10 inches down);
	CSucceedReportsDoc *pDoc = GetDocument();
	TEXTMETRIC tm;
	int nHeight;

	pDC->GetTextMetrics(&tm);
	nHeight = tm.tmHeight + tm.tmExternalLeading;

	if (fromWhere == 1)
		point = pageEndPoint;
	else
	{
		float y = (pDoc->m_report.m_paperSizeLength - 1) * -1440;  //1 inch margin
		y = y + (nHeight * pDoc->m_report.GetFooterSize());
		point.y = int(y);
		if (y > pageEndPoint.y)
			point.y = pageEndPoint.y;
	}

	for (int i = 0; i < pDoc->m_report.GetFooterSize(); i++)
	{
		pDoc->m_report.GetFooterLineAt(str, i);
		if (str.Find("\\PAGE#\\") != -1)
			if (fromWhere == 1)
				continue;
			else
				InsertPageNumber(str, m_nPage);
		point.y -= nHeight;
		pDC->TextOut(point.x, point.y, str);
	}

}



/////////////////////////////////////////////////////////////////////////////
// CSucceedReportsView diagnostics

#ifdef _DEBUG
void CSucceedReportsView::AssertValid() const
{
	CScrollView::AssertValid();
}


void CSucceedReportsView::Dump(CDumpContext& dc) const
{
	CScrollView::Dump(dc);
}


CSucceedReportsDoc* CSucceedReportsView::GetDocument() // non-debug version is inline
{
	ASSERT(m_pDocument->IsKindOf(RUNTIME_CLASS(CSucceedReportsDoc)));
	return (CSucceedReportsDoc*)m_pDocument;
}
#endif //_DEBUG

/////////////////////////////////////////////////////////////////////////////
// CSucceedReportsView message handlers

void CSucceedReportsView::OnPrepareDC(CDC* pDC, CPrintInfo* pInfo) 
{
	// TODO: Add your specialized code here and/or call the base class
	
	CScrollView::OnPrepareDC(pDC, pInfo);
	pDC->SetMapMode(MM_TWIPS);


}

void CSucceedReportsView::OnPrint(CDC* pDC, CPrintInfo* pInfo) 
{
	
//	CScrollView::OnPrint(pDC, pInfo);

	CFont *pOldFont = NULL;

	CSucceedReportsDoc* pDoc = GetDocument();

	int i, nHeight, n, nBodyLinesPerPage;
	int nStart, nEnd;
		
	CString str;
	CPoint point(720, -1440);
	TEXTMETRIC tm;
	SIZE strSize;
	CString temp;


	pDC->SetMapMode(MM_TWIPS);
	pOldFont = (CFont *)(pDC->SelectObject(&printFont));
	
	m_nPage = pInfo->m_nCurPage;    
	nStart = (m_nPage - 1) *
			(pDoc->m_report.m_nLinesPerPage - 
			 pDoc->m_report.GetHeaderSize() - pDoc->m_report.GetFooterSize());
	

	
	temp.Format("BodySize : %d   Page: %d    nStart: %d", pDoc->m_report.GetBodySize(), m_nPage, nStart);
	//AfxMessageBox(temp, MB_OK);
	nBodyLinesPerPage = pDoc->m_report.m_nLinesPerPage - pDoc->m_report.GetHeaderSize()
		- pDoc->m_report.GetFooterSize();
	nEnd = nStart + nBodyLinesPerPage;

	temp.Format("nEnd: %d", nEnd);
	//AfxMessageBox(temp, MB_OK);

	if (nStart >= pDoc->m_report.GetBodySize())
	{
		return;
	}

	pOldFont = (CFont *) (pDC->SelectObject(&printBoldFont));
	PrintPageHeader(pDC, 2);
	pOldFont = (CFont *) (pDC->SelectObject(&printFont));

	pDC->GetTextMetrics(&tm);
	nHeight = tm.tmHeight + tm.tmExternalLeading;
	point = pageStartPoint;

	for (i = nStart; i < nEnd; i++)
	{
		if (i >= pDoc->m_report.GetBodySize())
		{
			break;
		}
		nBodyLinesPerPage -= 1;

		pDoc->m_report.GetBodyLineAt(str, i);
		if (str.Left(3) == "<P>")		// buffer lines to prevent breaking
			str = "";

		point.y -= nHeight;
		point.x = 720;

		n = str.Find("<B>");

		if (n < 0)
			pDC->TextOut(point.x, point.y, str);
		else {
			//AfxMessageBox(str.Left(n), MB_OK);
			while (! str.IsEmpty()) {
				
				//	AfxMessageBox(str.Left(n), MB_OK);
				temp.Format("1) point.x = %d", point.x);
				//AfxMessageBox(temp, MB_OK);
				pDC->TextOut(point.x, point.y, str.Left(n));
				GetTextExtentPoint32(pDC->m_hAttribDC, str.Left(n), n, &strSize);
				point.x += strSize.cx;
				str = str.Right(str.GetLength() - (n+3));
				//	AfxMessageBox(str, MB_OK);
				
				if (! str.IsEmpty()) {
					n = str.Find("</B>");
					if (n < 0)
						n = str.GetLength();
					pOldFont = (CFont *) (pDC->SelectObject(&printBoldFont));
					temp.Format("2) point.x = %d", point.x);
					//AfxMessageBox(temp, MB_OK);
					pDC->TextOut(point.x, point.y, str.Left(n));
					
					GetTextExtentPoint32(pDC->m_hAttribDC, str.Left(n), n, &strSize);
					point.x += strSize.cx;
					if (n < str.GetLength())
						str = str.Right(str.GetLength() - (n+4));
					else
						str = "";
					//	AfxMessageBox(str, MB_OK);
					pOldFont = (CFont *) (pDC->SelectObject(&printFont));
					n = str.Find("<B>");
					if ( n < 0 ) {
						temp.Format("3) point.x = %d", point.x);
						//AfxMessageBox(temp, MB_OK);
						pDC->TextOut(point.x, point.y, str);
						str = "";
					}
				}
			}
		}

	
	}


	pageEndPoint = point;
	point.y -= nHeight;

//	PrintPageFooter(pDC, 2);
	
	if (pOldFont != NULL) 
		pDC->SelectObject(pOldFont);

}


void CSucceedReportsView::OnUpdate(CView* pSender, LPARAM lHint, CObject* pHint) 
{
	CSize sizeTotal;

	CSucceedReportsDoc* pDoc = GetDocument();

	// (1 inch = 1440 twips)
	if (pDoc->m_report.m_paperSizeWidth != 0)
		sizeTotal.cx = int(pDoc->m_report.m_paperSizeWidth * 1440);
	else   //default it to 8.5 inches
		sizeTotal.cx = int(8.5 * 1440);
	if (pDoc->m_report.m_paperSizeLength != 0)
		sizeTotal.cy = int(pDoc->m_report.m_paperSizeLength * 1440);
	else   //default it to 11 inches
		sizeTotal.cy = 11 * 1440;

	//sizeTotal.cy = sizeTotal.cy * 4;

	SetScrollSizes(MM_TWIPS, sizeTotal);
	Invalidate(TRUE);
	
}


void CSucceedReportsView::UpdateScrollSizes(CDC *pDC) 
{

	CSize sizeTotal;
	int nHeight;
	TEXTMETRIC tm;

	CSucceedReportsDoc* pDoc = GetDocument();

	pDC->GetTextMetrics(&tm);
	
	/*
	nHeight = tm.tmHeight + tm.tmExternalLeading;
			
	//calculate total number of pages to print the report
	int numPages = (pDoc->m_report.GetBodySize()) /
					(pDoc->m_report.m_nLinesPerPage - 
					pDoc->m_report.GetHeaderSize() - 5 - pDoc->m_report.GetFooterSize())
					+ 1;

	// (1 inch = 1440 twips)
	if (pDoc->m_report.m_paperSizeWidth != 0)
		//sizeTotal.cx = int(pDoc->m_report.m_paperSizeWidth * 1440 + 2200);
		sizeTotal.cx = int(pDoc->m_report.m_paperSizeWidth * 1440 + 1440);
	else   //default it to 8.5 inches
		sizeTotal.cx = int(8.5 * 1440 + 1440);

	if (pDoc->m_report.m_paperSizeLength != 0)
		sizeTotal.cy = int(pDoc->m_report.m_paperSizeLength * 1440 * numPages + 1440);
	else   //default it to 11 inches
		sizeTotal.cy = int(11 * 1440 * numPages + 1440);
	*/

	nHeight = tm.tmHeight + tm.tmExternalLeading;
	sizeTotal.cx = (tm.tmAveCharWidth * pDoc->m_report.m_nCharactersPerLine) + 2880;
	sizeTotal.cy = nHeight * (pDoc->m_report.GetBodySize() + 
		pDoc->m_report.GetHeaderSize()) + 1440;

	SetScrollSizes(MM_TWIPS, sizeTotal);
	CString s;
	s.Format("size = %d, %d", sizeTotal.cx, sizeTotal.cy);
//	AfxMessageBox(s);
	
	
}


void CSucceedReportsView::OnInitialUpdate() 
{
	CScrollView::OnInitialUpdate();
	
	// TODO: Add your specialized code here and/or call the base class

}

void CSucceedReportsView::OnFileExportToText() 
{
	// TODO: Add your command handler code here

	CFileDialog dlgFile(FALSE);
	CString fileName;
	CString title;
	CString strFilter;
	CString strDefault;

	CString allFilter;
	VERIFY(allFilter.LoadString(AFX_IDS_ALLFILTER));
	strFilter += CString("Text Files (*.txt)");
	strFilter += (TCHAR)'\0';   
	strFilter += _T("*.txt");
	strFilter += (TCHAR)'\0';
	strFilter += allFilter;
	strFilter += (TCHAR)'\0';   
	strFilter += _T("*.*");
	strFilter += (TCHAR)'\0';   

	dlgFile.m_ofn.lpstrFilter = strFilter;
	dlgFile.m_ofn.lpstrDefExt = "txt";
	dlgFile.m_ofn.lpstrTitle = "Export to Text";
	dlgFile.m_ofn.lpstrFile = fileName.GetBuffer(_MAX_PATH);
	BOOL bResult = dlgFile.DoModal() == IDOK ? TRUE : FALSE;

	
	fileName.ReleaseBuffer();
	
	if (! bResult)
		return;

	if (! fText.Open(fileName, CFile::modeCreate|CFile::modeWrite)) {
		CString msg;
		msg = "Error opening file: ";
		msg += fileName;
		AfxMessageBox(msg);
		return;
	}

	CSucceedReportsDoc* pDoc;
	pDoc = GetDocument();

	CString headerLine;
	CString tString1;
	CString tString2;
	CString reportName;
	CString facilityName;
	CString userName;
	CTime theTime = CTime::GetCurrentTime();
	int sp1, sp2;

	int charactersPerLine;
	int nMax;			
	int nColonPos;		
	CString sPageNum;

	reportName = pDoc->m_report.m_ReportName;
	facilityName = pDoc->m_report.m_FacilityName;
	userName = pDoc->m_UserName;
	sPageNum.Format("%d", m_nPage);
	
	// see which of the three header lines has the largest field on the right
	nMax = sPageNum.GetLength();
	if (nMax < userName.GetLength())
		nMax = userName.GetLength();
	if (nMax < facilityName.GetLength())
		nMax = facilityName.GetLength();

	charactersPerLine = pDoc->m_report.m_nCharactersPerLine;

	// the position of the colon on the right part of the header
	nColonPos = charactersPerLine - nMax - 2;

	tString1 = "SSA Global Technologies";
	tString2.Format("Page: %d", m_nPage);
	sp1 = (charactersPerLine/2) - (reportName.GetLength()/2); // space between left and center
	sp2 = nColonPos - sp1 - reportName.GetLength() - 4; // space between center and right
	headerLine.Format("%-*s%s%*s%s\r\n", sp1, tString1, reportName, sp2, " ",tString2);
	fText.Write(headerLine, headerLine.GetLength());

	tString1 = "SSA Optimize";
	tString2 = "Facility: " + facilityName;
	sp1 = nColonPos - tString1.GetLength() - 8;		// space between left and right
	headerLine.Format("%-s%*s%s\r\n", tString1, sp1, " ", tString2);
	fText.Write(headerLine, headerLine.GetLength());

	tString1 = theTime.Format("%m/%d/%Y %H:%M");
	tString2 = "User: " + userName;
	sp1 = nColonPos - tString1.GetLength() - 4;		// space between left and right
	headerLine.Format("%-s%*s%s\r\n", tString1, sp1, " ", tString2);
	fText.Write(headerLine, headerLine.GetLength());


	headerLine.Format("%-*s\r\n", charactersPerLine, " ");
	fText.Write(headerLine, headerLine.GetLength());

	CString str;

	for (int i = 0; i < pDoc->m_report.GetHeaderSize() - 6; i++)
	{
		pDoc->m_report.GetHeaderLineAt(headerLine, i);

		int n = 0;
		while (n >= 0) {
			n = headerLine.Find("<B>");
			if (n >= 0)
				headerLine = DeleteChars(headerLine, n, 3);
		}

		n = 0;
		while (n >= 0) {
			n = headerLine.Find("</B>");
			if (n >= 0)
				headerLine = DeleteChars(headerLine, n, 4);
		}
		
		headerLine += "\r\n";
		fText.Write(headerLine, headerLine.GetLength());

	}


	for (i = 0; i < pDoc->m_report.GetBodySize(); i++)
	{

		pDoc->m_report.GetBodyLineAt(str, i);
		if (str == "<P>")		// for printer spacing, skip in on-screen mode
			continue;

		int n = 0;
		while (n >= 0) {
			n = str.Find("<B>");
			if (n >= 0)
				str = DeleteChars(str, n, 3);
		}

		n = 0;
		while (n >= 0) {
			n = str.Find("</B>");
			if (n >= 0)
				str = DeleteChars(str, n, 4);
		}
		
		str += "\r\n";
		fText.Write(str, str.GetLength());

	}
	
	fText.Close();


	
	return;	

	
}
	

CString CSucceedReportsView::DeleteChars(CString s, int nStart, int nCount)
{
	CString newString;
	if (nStart > 0)
		newString = s.Left(nStart);

	newString += s.Right(s.GetLength() - nStart - nCount);

	return newString;

}


void CSucceedReportsView::OnFileExportDelimited() 
{
	// TODO: Add your command handler code here
	CFileDialog dlgFile(FALSE);
	CString fileName;
	CString title;
	CString strFilter;
	CString strDefault;

	CString allFilter;
	VERIFY(allFilter.LoadString(AFX_IDS_ALLFILTER));
	strFilter += CString("Text Files (*.txt)");
	strFilter += (TCHAR)'\0';   
	strFilter += _T("*.txt");
	strFilter += (TCHAR)'\0';
	strFilter += allFilter;
	strFilter += (TCHAR)'\0';   
	strFilter += _T("*.*");
	strFilter += (TCHAR)'\0';   

	dlgFile.m_ofn.lpstrFilter = strFilter;
	dlgFile.m_ofn.lpstrDefExt = "txt";
	dlgFile.m_ofn.lpstrTitle = "Export to Delimited";
	dlgFile.m_ofn.lpstrFile = fileName.GetBuffer(_MAX_PATH);
	BOOL bResult = dlgFile.DoModal() == IDOK ? TRUE : FALSE;

	
	fileName.ReleaseBuffer();
	
	if (! bResult)
		return;

	if (! fText.Open(fileName, CFile::modeCreate|CFile::modeWrite)) {
		CString msg;
		msg = "Error opening file: ";
		msg += fileName;
		AfxMessageBox(msg);
		return;
	}

	CSucceedReportsDoc* pDoc;
	pDoc = GetDocument();
	CString str;

	for (int i = 0; i < pDoc->m_report.GetDataSize(); i++)
	{
		pDoc->m_report.GetDataLineAt(str, i);
		str += "\r\n";
		fText.Write(str, str.GetLength());

	}
	
	fText.Close();
	
}

void CSucceedReportsView::OnExcel() 
{

	
	CString fileName;
	CString line;
	
	COleSafeArray outer;
	COleSafeArray *inner;
	SAFEARRAYBOUND rgsabound, rgsabound2;
	long index1;
	long index2;
	int fileCtr;

	CSucceedReportsDoc* pDoc;
	pDoc = GetDocument();

    rgsabound.lLbound = 0;
    rgsabound.cElements = pDoc->m_report.m_columnCount;

	rgsabound2.lLbound = 0;
	rgsabound2.cElements = 2;

	DWORD numElements[] = {2};

	outer.Create(VT_VARIANT, 1, &rgsabound);
	
	inner = new COleSafeArray[pDoc->m_report.m_columnCount];

	// The maximum number of rows in an Excel worksheet is currently
	// 65536.  So if we have more lines than that, we will have to create
	// multiple files, each with 65536 lines until we have used up all the 
	// lines.  Then we do some bizarre Excel stuff to open each file individually
	// and move them all into a single book with multiple sheets.  If there's
	// a better way, please tell me.

	int itemStart = 0;
	int rowCount;

	try {
		FILE *f;
		rowCount = pDoc->m_report.m_txtArray.GetSize() - 1;	// subtract one for the header
		for (int sheetCount=1; sheetCount <= rowCount/MAX_ROWS+1; ++sheetCount) {
		
			CString tempF = getenv("TEMP");
			if (tempF.IsEmpty())
				tempF = "c:";

			for (fileCtr=0; fileCtr < 10; ++fileCtr) {
				fileName.Format("%s\\OptimizeResults%d-%d.txt", tempF, fileCtr, sheetCount);
			
				f = fopen(fileName, "w");
				if (f == NULL) {
					if (errno != EACCES) {
						int x = errno;
						AfxMessageBox("Error creating temporary file.");
						return;
					}
				}
				else
					break;
			}
		
			// write the header
			for (int j=0; j < pDoc->m_report.m_columnCount; ++j) {
				//line = pDoc->m_report.m_txtArray[j];
				//fprintf(f, "\"%s\"|", line);
				// This weirdness is to format each cell as text so it won't
				// take leading zeros off, etc
				// Excel expects an array of two-item arrays; the first
				// item is the column number and the second is the format;
				// text format = 2
				if (sheetCount == 1) {
					inner[j].Create(VT_I2, 1, &rgsabound2);			
					index2 = 0;
					inner[j].PutElement(&index2, &j);
					index2 = 1;
					short k = 2;		// text format
					inner[j].PutElement(&index2, &k);
					index1 = j;
					outer.PutElement(&index1, COleVariant(inner[j]));
				}
				
			}
			
			//fprintf(f, "\n");
			// Add a header to every page
			fprintf(f, "%s\n", pDoc->m_report.m_txtArray[0]);
			
			int itemCount = itemStart + MAX_ROWS;
			if (itemCount > rowCount)
				itemCount = rowCount;

			for (int i=itemStart; i < itemCount; ++i) {
				fprintf(f, "%s\n", pDoc->m_report.m_txtArray[i+1]);
			}
			itemStart += MAX_ROWS;

			fclose(f);
		}
		

	}
	catch (...) {
		AfxMessageBox("Error creating file for opening in Excel.");
		return;
	}


	try
	{
		_Application app;     // app is an _Application object.
		_Workbook book, mainBook;       // More object declarations.
		_Worksheet sheet, mainSheet;
		Workbooks books;
		Worksheets sheets, mainSheets;
		Range range;          // Used for Microsoft Excel 97 components.
		LPDISPATCH lpDisp;    // Often reused variable.
		Range cols;

		// Common OLE variants. Easy variants to use for calling arguments.
		COleVariant
			covTrue((short)TRUE),
			covFalse((short)FALSE),
			covOptional((long)DISP_E_PARAMNOTFOUND, VT_ERROR);
		
		// Start Microsoft Excel, get _Application object,
		// and attach to app object.
		if(!app.CreateDispatch("Excel.Application"))
		{
			AfxMessageBox("Couldn't CreateDispatch() for Excel");
			return;
		}
		
		app.SetWindowState(-4140);		// -4140 = xlMinimized
		app.SetVisible(TRUE);
		app.SetUserControl(TRUE);

		// Get the Workbooks collection.
		lpDisp = app.GetWorkbooks();     // Get an IDispatch pointer.
		ASSERT(lpDisp);
		books.AttachDispatch(lpDisp);    // Attach the IDispatch pointer
										// to the books object.

		VARIANT sheetVar = {0};

		for (int sheetCount=1; sheetCount <= rowCount/MAX_ROWS+1; ++sheetCount) {
			
			fileName = getenv("TEMP");
			if (fileName.IsEmpty())
				fileName = "c:";
			
			fileName.Format("%s\\OptimizeResults%d-%d.txt", fileName, fileCtr, sheetCount);
			
			// open the file as text specifiying pipe-delimited
			books.OpenText(fileName, 
				covOptional,				// Origin
				covOptional,				// Start row
				COleVariant((short)(1)),	// Datatype
				1,							// TextQualifier 
				covOptional,				// Consecutive delimiter
				covOptional,				// Tab
				covOptional,				// Semicolon
				covOptional,				// Comma
				covOptional,				// Space
				covTrue,					// Other
				COleVariant("|"),			// OtherChar
				COleVariant(outer),			// FieldInfo
				covOptional);				// TextVisualLayout

		
			// Get the book
			if (sheetCount == 1)
				lpDisp = books.GetItem(COleVariant((short)(1)));
			else
				lpDisp = books.GetItem(COleVariant((short)(2)));
			ASSERT(lpDisp);
			book.AttachDispatch(lpDisp);
			
			// Get the sheets
			lpDisp = book.GetSheets();
			ASSERT(lpDisp);
			sheets.AttachDispatch(lpDisp);
				
			// Get the sheet
			lpDisp = sheets.GetItem(COleVariant((short)1));
			//GetItem(const VARIANT &index)
			ASSERT(lpDisp);
			sheet.AttachDispatch(lpDisp);
			
			CString temp;
			temp.Format("Page %d", sheetCount);
			sheet.SetName(temp);
			
			// Change the entire worksheet to be autofitt
			if (pDoc->m_report.m_columnCount < 26)
				range = sheet.GetRange(COleVariant("A1"), COleVariant("Z1"));
			else if (pDoc->m_report.m_columnCount < 52)
				range = sheet.GetRange(COleVariant("A1"), COleVariant("AZ1"));
			else
				range = sheet.GetRange(COleVariant("A1"), COleVariant("BZ1"));


			Font font;
			font = range.GetFont();
			font.SetBold(covTrue);

			cols = range.GetEntireColumn();
			cols.AutoFit();
		
			if (sheetCount == 1) {
				// Get the first book and hold it for use by the others
				lpDisp = books.GetItem(COleVariant((short)1));
				ASSERT(lpDisp);
				mainBook.AttachDispatch(lpDisp);
				
				// Same for the sheets
				lpDisp = mainBook.GetSheets();
				ASSERT(lpDisp);
				mainSheets.AttachDispatch(lpDisp);

			}

			else {
				// Get the previous sheet we inserted in the main book
				lpDisp = mainSheets.GetItem(COleVariant((short)(sheetCount-1)));
				ASSERT(lpDisp);
				mainSheet.AttachDispatch(lpDisp);
				
				// Create a Variant to represent the previous sheet object
				// We will use this to copy the subsequent sheets
				sheetVar.vt = VT_DISPATCH;
				sheetVar.pdispVal = mainSheet.m_lpDispatch;
				mainSheet.m_lpDispatch->AddRef();
					
				// Add an additional sheet to the new workbook so that we can 
				// move the new sheet without getting an error because there 
				// are no sheets left because a book must have a minimum of one 
				// sheet and we are moving one
				
				// Use the default placement which will put it first
				_Worksheet tempSheet;
				tempSheet = sheets.Add(covOptional, covOptional, covOptional, covOptional);

				// Now move the new sheet to the main book
				sheet.Move(covOptional, sheetVar);

				VariantClear(&sheetVar);

				// Now close the new workbook without saving
				book.Close(covFalse, covOptional, covOptional);

			}

		}

		lpDisp = mainSheets.GetItem(COleVariant((short)1));
		ASSERT(lpDisp);
		sheet.AttachDispatch(lpDisp);
		sheet.Activate();

		app.SetWindowState(-4143);		// -4143 = xlNormal
		app.ReleaseDispatch();

      } // End of processing.

	  catch(COleException *e)
      {
		  char buf[1024];     // For the Try...Catch error message.
		  sprintf(buf, "COleException. SCODE: %08lx.", (long)e->m_sc);
		  //::MessageBox(NULL, buf, "COleException", MB_SETFOREGROUND | MB_OK);
      }
	  
      catch(COleDispatchException *e)
      {
		  char buf[1024];     // For the Try...Catch error message.
		  sprintf(buf,
			  "COleDispatchException. SCODE: %08lx, Description: \"%s\".",
			  (long)e->m_wCode,(LPSTR)e->m_strDescription.GetBuffer(512));
		  //::MessageBox(NULL, buf, "COleDispatchException",
		  //MB_SETFOREGROUND | MB_OK);
	  }
	  
      catch(...)
      {
		  //::MessageBox(NULL, "General Exception caught.", "Catch-All",
		  //MB_SETFOREGROUND | MB_OK);
      }

	  // Clean up the format arrays
	  for (int i=0; i < pDoc->m_report.m_columnCount; ++i) {
		  //inner[i].Destroy();	
		  inner[i].Detach();	
	  }
	  delete [] inner;
	  outer.Detach();


	  return;

	
}

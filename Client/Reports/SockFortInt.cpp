//////////////////////////////////////////////////////////////
// These functions are used to establish socket communication with
// the forte slotting executable
//////////////////////////////////////////////////////////////

/////////////////////////////////////////////////////////////
// include relevant header files
/////////////////////////////////////////////////////////////
#include "stdafx.h"

#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <winbase.h>
#include <process.h>
#include <math.h>
#include "ssa_exception.h"

#include "sockfortint.h"
//#include "utilfuncs.h"

#include "socket_class.h"

#include "base.h"
#include "MiscClasses.h"
#include "SLOTSessionMgr.h"
#include "SrvcObjs.h"

//Naveen 15Feb06 Reports Fix
extern long currentFacilityID; 
extern CString currentDatabase;

int initted = 0;
//void initbuf(char * buf, int length) {
//	memset(buf,0,length);
//}

int GetReportList(CString reportName, CSsaStringArray & returnList)
{
	CString tempString;
	//int tempInt;
	CSsaStringArray tempSendArray;
	CSsaStringArray tempRecvArray;
	CString sendString;
	int i = 0;
#if 0
	/////////////////////////////////////////////////////////////
	// Build the data and send to Forte connection
	/////////////////////////////////////////////////////////////
	sendString = "<SIO>SLOTSocketString\n";
	tempSendArray.Add(sendString);
	sendString = "<SAI>" + reportName + "\n";
	tempSendArray.Add(sendString);
	sendString = "<EIO>SLOTSocketString\n";
	tempSendArray.Add(sendString);

	SendToForteConnection(tempSendArray,tempRecvArray,
		CString("SLOTSocketString"), 10040);

	for (i=0; i < tempRecvArray.GetSize(); i++) {
		if ( tempRecvArray[i].Find("<SAI>") != -1 ) {
			tempString = tempRecvArray[i].Mid(5);
			tempInt = tempString.GetLength();
			if (tempString.GetAt(tempInt-1) == '\n' ) {
				tempString.SetAt(tempInt-1,' ');
				tempString.TrimRight();
			}
			returnList.Add(tempString);
		}
	}
	if ( tempString == "Failure" )
		return -1;
	else if ( returnList.GetSize() > 0 )
		return 0;
	else 
		return 1;
#else
	string repName = (LPCTSTR)reportName;

	//Naveen 15Feb06 Reports Fix
	CListstringPtr res = getSessionMgrSO()->GetReportListHelper(repName, (LPCTSTR)currentDatabase, currentFacilityID);

	POSITION posSL = res->GetHeadPosition();
	for (int i=0; i<res->GetCount(); i++)
	{
		returnList.Add((res->GetNext(posSL)).c_str());
	}
	if ( returnList.GetSize() > 0 )
		return 0;
	else 
		return 1;
#endif
}



void SendToForteConnection(CSsaStringArray &sendData, CSsaStringArray &recvData,
				 CString className, int operationNum) 
{
	CString sendBuf;
	CString recvBuf('\0',10*MAXBUF);
	CString tempString;
	CString leftString="";
	CSsaStringArray tempBufArray;
	//char intbuf[15];
	char readbuf[MAXBUF+1];

	if ( initted == 0 ) {
		init_winsock();
		initted = 1;
	}
	int readDone = 0;
	char nodename[129];
	int i = 0;
	int tempLength=0;
	int tempInd=0;

	/////////////////////////////////////////////////////////////
	// get the client host name and create a socket
	/////////////////////////////////////////////////////////////
	gethostname(nodename, 128);

	SockClass SockClient(nodename,FORTE_PORT);

	/////////////////////////////////////////////////////////////
	// notify what we are sending
	/////////////////////////////////////////////////////////////
	sendBuf.Format("<SSO>\n<SON>%s:%d\n<EOS>\n",className,operationNum);
	printf("Sending notification\n");
	SockClient.SendData(sendBuf.GetBuffer(0),sendBuf.GetLength());
	sendBuf.ReleaseBuffer();

	/////////////////////////////////////////////////////////////
	// socket server sends back handshaking
	/////////////////////////////////////////////////////////////
	memset(readbuf,0,sizeof(readbuf));
	readDone = 0;
	recvBuf.Empty();
	printf("Receiving handshaking\n");
	while (readDone == 0) {
		if (SockClient.GetData(readbuf,MAXBUF) == 0)
			readDone = 1;
		if (readDone == 0) {
			recvBuf += readbuf;
			if (recvBuf.Find("<EOS>") != -1 )
				readDone = 1;
			//initbuf(readbuf,MAXBUF+1);
			memset(readbuf, 0, sizeof(readbuf));
		}
	}

/*	if ( recvBuf.Find("<EXCEPTION>") != -1 ) {
		CString exceptText = recvBuf.Mid(11);
		char msg[1024];
		strncpy(msg,exceptText.GetBuffer(0),1023);
		exceptText.ReleaseBuffer();
		throw Ssa_Exception(msg, __FILE__, __LINE__, 200);
	}
*/
	/////////////////////////////////////////////////////////////
	// build and send the message to the socket
	/////////////////////////////////////////////////////////////
	sendBuf = "<SSO>\n<SLO>\n"; //<SIO>" + className + "\n";
	for ( i = 0; i < sendData.GetSize(); i++ )
		sendBuf += sendData[i];
	sendBuf += "<ELO>\n<EOS>\n";

	printf("Sending Data\n");
	printf("%s\n",sendBuf);
	SockClient.SendData(sendBuf.GetBuffer(0),sendBuf.GetLength());
	sendBuf.ReleaseBuffer();

	memset(readbuf,0,sizeof(readbuf));
	recvBuf.Empty();
	readDone = 0;
	
	/////////////////////////////////////////////////////////////
	// server sends back handshaking
	/////////////////////////////////////////////////////////////
	printf("Getting Handshaking\n");
	while (readDone == 0) {
		if (SockClient.GetData(readbuf,MAXBUF) == 0)
			readDone = 1;
		if (readDone == 0) {
			recvBuf += readbuf;
			if (recvBuf.Find("<EOS>") != -1 )
				readDone = 1;
			//initbuf(readbuf,MAXBUF+1);
			memset(readbuf,0, sizeof(readbuf));
		}
	}

	/////////////////////////////////////////////////////////////
	// get the necessary data back from the socket
	/////////////////////////////////////////////////////////////
	printf("Receiving Data\n");
	recvBuf.Empty();
	readDone = 0;
	memset(readbuf,0,sizeof(readbuf));

	while (readDone == 0) {
		if (SockClient.GetData(readbuf,MAXBUF) == 0)
			readDone = 1;
		//printf("%s",readbuf);
		if (readDone == 0) {
			if (leftString != "")
				tempString = leftString + readbuf;
			else
				tempString = readbuf;
			tempLength = tempString.GetLength();
			tempInd = tempString.Find('\n');
			if ( tempInd != -1 ) {
				recvData.Add(tempString.Left(tempInd));
				leftString = tempString.Mid(tempInd+1);
			}
			else
				leftString = tempString;
//			recvBuf += readbuf;
			if (strstr(readbuf,"<EOS>") != NULL )
				readDone = 1;
			//initbuf(readbuf,MAXBUF+1);
			memset(readbuf,0, sizeof(readbuf));
		}
	}

	for ( i = 0; i < recvData.GetSize(); i++ ) {
		if ( recvData[i].Find("<EXCEPTION>") != -1 ) {
			CString exceptText = recvData[i].Mid(16);
			char msg[1024];
			strncpy(msg,exceptText.GetBuffer(0),1023);
			exceptText.ReleaseBuffer();
			printf("Sending Recognition\n");
			sendBuf = "<SSO>\n<EOS>\n";
			SockClient.SendData(sendBuf.GetBuffer(0),sendBuf.GetLength());
			sendBuf.ReleaseBuffer();
			throw SsaException(msg, __FILE__, __LINE__, 200);
		}
	}

	/////////////////////////////////////////////////////////////
	// build return data to send to caller
	/////////////////////////////////////////////////////////////
	printf("Building strings\n");


	/////////////////////////////////////////////////////////////
	// send recognition of receiving the data
	/////////////////////////////////////////////////////////////
	printf("Sending Recognition\n");
	sendBuf = "<SSO>\n<EOS>\n";
	SockClient.SendData(sendBuf.GetBuffer(0),sendBuf.GetLength());
	sendBuf.ReleaseBuffer();
//	fini_winsock();

	return;
}


ExecuteQuery(const CString &queryName, const CString &query, CStringArray &results)
{
	CString tempString;
	int tempInt;
	CSsaStringArray tempSendArray;
	CSsaStringArray tempRecvArray;
	CString sendString;
	int i = 0;
#if 0
	/////////////////////////////////////////////////////////////
	// build the data to send to the Forte connection server
	////////////////////////////////////////////////////////////	

	sendString = "<SIO>SLOTSocketString\n";
	tempSendArray.Add(sendString);
	sendString.Format("<SAI>%s\n", queryName);
	tempSendArray.Add(sendString);
	tempString.Format("<SAI>%s\n",query);
	tempSendArray.Add(tempString);
	tempString.Format("<SAI>False\n");	// Do not include headers in the results
	tempSendArray.Add(tempString);
	sendString = "<EIO>SLOTSocketString\n";
	tempSendArray.Add(sendString);
	
	SendToForteConnection(tempSendArray,tempRecvArray,
		CString("SLOTSocketString"), 55555);

	for (i=0; i < tempRecvArray.GetSize(); i++) {
		if ( tempRecvArray[i].Find("<SAI>") != -1 ) {
			tempString = tempRecvArray[i].Mid(5);
			tempInt = tempString.GetLength();
			if (tempString.GetAt(tempInt-1) == '\n' ) {
				tempString.SetAt(tempInt-1,' ');
				tempString.TrimRight();
			}
			results.Add(tempString);			
		}
	}
#else
	CListstring *res = NULL;
	char strMsg[4096] = {0};

	try
	{
		string *queryTmp = new string;
		*queryTmp = query.GetString();
		bool *b = new bool;
			*b = false;

		SLOTSessionMgr* ptr= getSessionMgrSO();
		res = ptr->RunQueryHelper(queryTmp, b);

		delete queryTmp;
		delete b;
	}
	catch(CException *e)
	{
		e->GetErrorMessage (strMsg, 4096);
		e->Delete();
		return -1;
	}
	for (int i=0;i<res->GetCount();i++)
	{
		CString tmpStr = res->GetAt(res->FindIndex(i)).c_str();
		results.SetAtGrow(i, tmpStr);
	}
#endif
	if ( results.GetSize() == 0 )
		return 0;
	else {
		return results.GetSize();
	}

}

#ifndef CSSACSTRINGARRAY_H
#define CSSACSTRINGARRAY_H
#include <afx.h>
/////////////////////////////////////////////////////////////
// New CSsaStringArray class
/////////////////////////////////////////////////////////////
class CSsaStringArray : public CStringArray
{
public:

	CSsaStringArray() { }
	void RemoveAll() {
//		CString * tempPtr;
//
//		for (int i = this->GetSize()-1; i >= 0; i--) {
//			//this->GetAt(i).~CString();
//			tempPtr = &(this->GetAt(i));
//			this->RemoveAt(i,1);
//			delete tempPtr;
//		}
		this->SetSize(0);
		return;
	}
	CSsaStringArray(CSsaStringArray& other)
	{
		RemoveAll();
		for (int i = 0; i < other.GetSize(); i++)
			this->Add(other[i]);
	}
	virtual ~CSsaStringArray() { }
	CSsaStringArray& operator =(const CSsaStringArray& other)
	{
		RemoveAll();
		for (int i = 0; i < other.GetSize(); i++)
			Add(other[i]);
		return *this;
	}

};
#endif
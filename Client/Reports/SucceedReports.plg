<html>
<body>
<pre>
<h1>Build Log</h1>
<h3>
--------------------Configuration: SucceedReports - Win32 Debug--------------------
</h3>
<h3>Command Lines</h3>
Creating temporary file "C:\DOCUME~1\ADMINI~1\LOCALS~1\Temp\RSP3CD.tmp" with contents
[
/nologo /MDd /W3 /Gm /GX /ZI /Od /D "WIN32" /D "_DEBUG" /D "_WINDOWS" /D "_AFXDLL" /Fr"Debug/" /Fp"Debug/SucceedReports.pch" /YX"stdafx.h" /Fo"Debug/" /Fd"Debug/" /FD /c 
"C:\New2\Reports\SockFortInt.cpp"
]
Creating command line "cl.exe @C:\DOCUME~1\ADMINI~1\LOCALS~1\Temp\RSP3CD.tmp" 
Creating temporary file "C:\DOCUME~1\ADMINI~1\LOCALS~1\Temp\RSP3CE.tmp" with contents
[
wsock32.lib /nologo /subsystem:windows /incremental:yes /pdb:"Debug/OptimizeReports.pdb" /debug /machine:I386 /out:"Debug/OptimizeReports.exe" /pdbtype:sept 
.\Debug\BuildReports.obj
.\Debug\excel8.obj
.\Debug\MainFrm.obj
.\Debug\Progress.obj
.\Debug\socket_class.obj
.\Debug\SockFortInt.obj
.\Debug\StdAfx.obj
.\Debug\SucceedReports.obj
.\Debug\SucceedReportsDoc.obj
.\Debug\SucceedReportsView.obj
.\Debug\SUCCReport.obj
.\Debug\SucceedReports.res
]
Creating command line "link.exe @C:\DOCUME~1\ADMINI~1\LOCALS~1\Temp\RSP3CE.tmp"
<h3>Output Window</h3>
Compiling...
SockFortInt.cpp
f:\hashtable.h(5) : fatal error C1083: Cannot open include file: 'hash_map': No such file or directory
Error executing cl.exe.
Creating temporary file "C:\DOCUME~1\ADMINI~1\LOCALS~1\Temp\RSP3CF.tmp" with contents
[
/nologo /o"Debug/SucceedReports.bsc" 
.\Debug\StdAfx.sbr
.\Debug\BuildReports.sbr
.\Debug\excel8.sbr
.\Debug\MainFrm.sbr
.\Debug\Progress.sbr
.\Debug\socket_class.sbr
.\Debug\SockFortInt.sbr
.\Debug\SucceedReports.sbr
.\Debug\SucceedReportsDoc.sbr
.\Debug\SucceedReportsView.sbr
.\Debug\SUCCReport.sbr]
Creating command line "bscmake.exe @C:\DOCUME~1\ADMINI~1\LOCALS~1\Temp\RSP3CF.tmp"
Creating browse info file...
BSCMAKE: error BK1506 : cannot open file '.\Debug\SockFortInt.sbr': No such file or directory
Error executing bscmake.exe.
<h3>Output Window</h3>



<h3>Results</h3>
OptimizeReports.exe - 2 error(s), 0 warning(s)
</pre>
</body>
</html>

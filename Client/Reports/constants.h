#ifndef CONSTANTS_DEFINED
const int GROUP_LAYOUT = 0;
const int TACTICAL_LAYOUT = 1;
const int STRATEGIC_LAYOUT = 2;
const int NEW_PRODUCT_LAYOUT = 3;
const int MANUAL_LAYOUT = 4;

const int BAYTYPE_BIN = 1;
const int BAYTYPE_DRIVEIN = 2;
const int BAYTYPE_FLOOR = 3;
const int BAYTYPE_CASEFLOW = 4;
const int BAYTYPE_PALLET = 5;
const int BAYTYPE_PIR = 6;
const int BAYTYPE_CAROUSEL = 7;
const int BAYTYPE_PALLETFLOW = 8;

const int DT_NONE = 0;
const int DT_INT = 1;
const int DT_STRING = 2;
const int DT_FLOAT = 3;
const int DT_LIST = 4;
const int DT_FORMULA = 5;

const int SLOT_NIL_INTEGER = -32767;
const int SLOT_NIL_FLOAT = -32767;

const int UOI_EACH = 0;
const int UOI_INNER = 1;
const int UOI_CASE = 2;
const int UOI_PALLET = 3;

const CString TB_PRODUCT = "DBProductPack";
const CString TB_PRODUCTCONTAINER = "DBProdContainer";
const CString TB_PRODUCTUDFLIST = "DBProdPKUDFList";
const CString TB_PRODUCTUDFVAL = "DBProdPKUDFVal";
const CString TB_PRODUCTGROUP = "DBSlottingGroup";
const CString TB_PRODUCTGROUPLOC = "DBSlottingGroupLoc";
const CString TB_LOCATION = "DBLocation";
const CString TB_SOLUTION = "DBSlotSolution";
const CString TB_BAYPROFILE = "DBBayProfile";
const CString TB_LEVELPROFILE = "DBLevelProfile";
const CString TB_LOCATIONPROFILE = "DBLocationProf";
const CString TB_LEVEL = "DBLevel";

const int CQ_COMPOUND_START = 1;
const int CQ_COMPOUND_END = 2;

const int TIME_HORIZON_DAY = 1;
const int TIME_HORIZON_WEEK = 2;
const int TIME_HORIZON_MONTH = 3;
const int TIME_HORIZON_YEAR = 4;

const int MOVE_NORMAL = 0;
const int MOVE_TO_TEMP = 1;
const int MOVE_FROM_TEMP = 2;
const int MOVE_ADD_LOC = 3;
const int MOVE_DEL_LOC = 4;

const int UDF_PRODUCT = 0;
const int UDF_PRODUCT_GROUP = 1;
const int UDF_FACILITY = 2;
const int UDF_SECTION = 3;
const int UDF_AISLE = 4;
const int UDF_SIDE = 5;
const int UDF_BAY = 6;
const int UDF_LEVEL = 7;
const int UDF_LOCATION = 8;
const int UDF_LEVEL_PROFILE = 9;


#define CONSTANTS_DEFINED
#endif

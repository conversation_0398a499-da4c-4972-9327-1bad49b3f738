// SucceedReports.h : main header file for the SUCCEEDREPORTS application
//

#if !defined(AFX_SUCCEEDREPORTS_H__749C5A35_EDCF_11D2_9CD9_0080C742D9DF__INCLUDED_)
#define AFX_SUCCEEDREPORTS_H__749C5A35_EDCF_11D2_9CD9_0080C742D9DF__INCLUDED_

#pragma once

#ifndef __AFXWIN_H__
	#error include 'stdafx.h' before including this file for PCH
#endif

#include "resource.h"       // main symbols
#include <string>
using namespace std;

/////////////////////////////////////////////////////////////////////////////
// CSucceedReportsApp:
// See SucceedReports.cpp for the implementation of this class
//

class CSucceedReportsApp : public CWinApp
{
	string *DBName;
public:
	BOOL PeekAndPump();
	CSucceedReportsApp();
	~CSucceedReportsApp();

// Overrides
public:
	virtual BOOL InitInstance();
	virtual CDocument* OpenDocumentFile(LPCTSTR lpszFileName);	

// Implementation
	afx_msg void OnAppAbout();
	DECLARE_MESSAGE_MAP()
};

extern CSucceedReportsApp theApp;
#endif // !defined(AFX_SUCCEEDREPORTS_H__749C5A35_EDCF_11D2_9CD9_0080C742D9DF__INCLUDED_)
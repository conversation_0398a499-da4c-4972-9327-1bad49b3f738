//////////////////////////////////////////////////////////////////////
// Function Name :	debug.h
// Classname :		
// Description :	Global header for Succeed Engine.
// Date Created :	~9/1/98
// Author : 		mfs
//////////////////////////////////////////////////////////////////////
// Inputs :			None
// Outputs :		None
// Explanation :	Global header for Succeed Engine.  Contains the
//					Debug flag and the cube value divisor.
//////////////////////////////////////////////////////////////////////
// Revisions : 
//   November 6, 1998-mfs : Standard header comments added
//////////////////////////////////////////////////////////////////////
// (c) Copyright, EXE Tecnologies, 1998
//////////////////////////////////////////////////////////////////////

#ifndef SLOT_DEBUG
#define SLOT_DEBUG 1
#endif

#ifndef SLOT_DIVISOR
#define SLOT_DIVISOR 1728
#endif
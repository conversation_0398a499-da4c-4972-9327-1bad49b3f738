<?xml version="1.0" encoding="Windows-1252"?>
<VisualStudioProject
	ProjectType="Visual C++"
	Version="7.10"
	Name="SucceedReports"
	ProjectGUID="{5A75B5CC-556D-46EA-B7C5-AD43CE9EC3E8}"
	SccProjectName="&quot;$/Optimize/OPTIMIZEBASE4/Client/Reports&quot;, TABAAAAA"
	SccAuxPath=""
	SccLocalPath="."
	SccProvider="MSSCCI:Microsoft Visual SourceSafe"
	Keyword="MFCProj">
	<Platforms>
		<Platform
			Name="Win32"/>
	</Platforms>
	<Configurations>
		<Configuration
			Name="Debug|Win32"
			OutputDirectory="..\..\Obj.Debug"
			IntermediateDirectory="$(outdir)/Client/Reports"
			ConfigurationType="1"
			UseOfMFC="2"
			ATLMinimizesCRunTimeLibraryUsage="FALSE">
			<Tool
				Name="VCCLCompilerTool"
				Optimization="0"
				AdditionalIncludeDirectories="..\..\Server"
				PreprocessorDefinitions="WIN32;_DEBUG;_WINDOWS"
				RuntimeLibrary="3"
				UsePrecompiledHeader="2"
				PrecompiledHeaderThrough="stdafx.h"
				PrecompiledHeaderFile="$(outdir)/Client/Reports/SucceedReports.pch"
				AssemblerListingLocation="$(outdir)/Client/Reports/"
				ObjectFile="$(outdir)/Client/Reports/"
				ProgramDataBaseFileName="$(outdir)/Client/Reports/"
				WarningLevel="3"
				SuppressStartupBanner="TRUE"
				DebugInformationFormat="4"/>
			<Tool
				Name="VCCustomBuildTool"/>
			<Tool
				Name="VCLinkerTool"
				AdditionalDependencies="wsock32.lib $(outdir)/libraries/OptiServer.lib"
				OutputFile="$(OutDir)/bin/OptimizeReports.exe"
				LinkIncremental="1"
				SuppressStartupBanner="TRUE"
				GenerateDebugInformation="TRUE"
				ProgramDatabaseFile="$(outdir)/Client/Reports/OptimizeReports.pdb"
				SubSystem="2"
				ImportLibrary="$(outdir)/Libraries/OptimizeReports.lib"
				TargetMachine="1"/>
			<Tool
				Name="VCMIDLTool"
				PreprocessorDefinitions="_DEBUG"
				MkTypLibCompatible="TRUE"
				SuppressStartupBanner="TRUE"
				TargetEnvironment="1"
				TypeLibraryName=".\Debug/SucceedReports.tlb"
				HeaderFileName=""/>
			<Tool
				Name="VCPostBuildEventTool"/>
			<Tool
				Name="VCPreBuildEventTool"/>
			<Tool
				Name="VCPreLinkEventTool"/>
			<Tool
				Name="VCResourceCompilerTool"
				PreprocessorDefinitions="_DEBUG"
				Culture="1033"/>
			<Tool
				Name="VCWebServiceProxyGeneratorTool"/>
			<Tool
				Name="VCXMLDataGeneratorTool"/>
			<Tool
				Name="VCWebDeploymentTool"/>
			<Tool
				Name="VCManagedWrapperGeneratorTool"/>
			<Tool
				Name="VCAuxiliaryManagedWrapperGeneratorTool"/>
		</Configuration>
		<Configuration
			Name="Release|Win32"
			OutputDirectory="..\..\Obj.Release"
			IntermediateDirectory="$(outdir)/Client/Reports"
			ConfigurationType="1"
			UseOfMFC="2"
			ATLMinimizesCRunTimeLibraryUsage="FALSE">
			<Tool
				Name="VCCLCompilerTool"
				Optimization="2"
				InlineFunctionExpansion="1"
				AdditionalIncludeDirectories="..\..\Server"
				PreprocessorDefinitions="WIN32;NDEBUG;_WINDOWS;_WINDLL"
				StringPooling="TRUE"
				RuntimeLibrary="2"
				EnableFunctionLevelLinking="TRUE"
				UsePrecompiledHeader="2"
				PrecompiledHeaderThrough="stdafx.h"
				PrecompiledHeaderFile="$(outdir)/Client/Reports/SucceedReports.pch"
				AssemblerListingLocation="$(outdir)/Client/Reports/"
				ObjectFile="$(outdir)/Client/Reports/"
				ProgramDataBaseFileName="$(outdir)/Client/Reports/"
				WarningLevel="3"
				SuppressStartupBanner="TRUE"/>
			<Tool
				Name="VCCustomBuildTool"/>
			<Tool
				Name="VCLinkerTool"
				AdditionalDependencies="wsock32.lib $(outdir)/libraries/OptiServer.lib"
				OutputFile="$(OutDir)/bin/OptimizeReports.exe"
				LinkIncremental="1"
				SuppressStartupBanner="TRUE"
				ProgramDatabaseFile="$(outdir)/Client/Reports/OptimizeReports.pdb"
				SubSystem="2"
				ImportLibrary="$(outdir)/Libraries/OptimizeReports.lib"
				TargetMachine="1"/>
			<Tool
				Name="VCMIDLTool"
				PreprocessorDefinitions="NDEBUG"
				MkTypLibCompatible="TRUE"
				SuppressStartupBanner="TRUE"
				TargetEnvironment="1"
				TypeLibraryName=".\release/SucceedReports.tlb"
				HeaderFileName=""/>
			<Tool
				Name="VCPostBuildEventTool"/>
			<Tool
				Name="VCPreBuildEventTool"/>
			<Tool
				Name="VCPreLinkEventTool"/>
			<Tool
				Name="VCResourceCompilerTool"
				PreprocessorDefinitions="NDEBUG"
				Culture="1033"/>
			<Tool
				Name="VCWebServiceProxyGeneratorTool"/>
			<Tool
				Name="VCXMLDataGeneratorTool"/>
			<Tool
				Name="VCWebDeploymentTool"/>
			<Tool
				Name="VCManagedWrapperGeneratorTool"/>
			<Tool
				Name="VCAuxiliaryManagedWrapperGeneratorTool"/>
		</Configuration>
	</Configurations>
	<References>
	</References>
	<Files>
		<Filter
			Name="Source Files"
			Filter="cpp;c;cxx;rc;def;r;odl;idl;hpj;bat">
			<File
				RelativePath="BuildReports.cpp">
				<FileConfiguration
					Name="Debug|Win32">
					<Tool
						Name="VCCLCompilerTool"
						Optimization="0"
						PreprocessorDefinitions=""/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|Win32">
					<Tool
						Name="VCCLCompilerTool"
						Optimization="2"
						PreprocessorDefinitions=""/>
				</FileConfiguration>
			</File>
			<File
				RelativePath="excel8.cpp">
				<FileConfiguration
					Name="Debug|Win32">
					<Tool
						Name="VCCLCompilerTool"
						Optimization="0"
						PreprocessorDefinitions=""/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|Win32">
					<Tool
						Name="VCCLCompilerTool"
						Optimization="2"
						PreprocessorDefinitions=""/>
				</FileConfiguration>
			</File>
			<File
				RelativePath="MainFrm.cpp">
				<FileConfiguration
					Name="Debug|Win32">
					<Tool
						Name="VCCLCompilerTool"
						Optimization="0"
						PreprocessorDefinitions=""/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|Win32">
					<Tool
						Name="VCCLCompilerTool"
						Optimization="2"
						PreprocessorDefinitions=""/>
				</FileConfiguration>
			</File>
			<File
				RelativePath="Progress.cpp">
				<FileConfiguration
					Name="Debug|Win32">
					<Tool
						Name="VCCLCompilerTool"
						Optimization="0"
						PreprocessorDefinitions=""/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|Win32">
					<Tool
						Name="VCCLCompilerTool"
						Optimization="2"
						PreprocessorDefinitions=""/>
				</FileConfiguration>
			</File>
			<File
				RelativePath="socket_class.cpp">
				<FileConfiguration
					Name="Debug|Win32">
					<Tool
						Name="VCCLCompilerTool"
						Optimization="0"
						PreprocessorDefinitions=""/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|Win32">
					<Tool
						Name="VCCLCompilerTool"
						Optimization="2"
						PreprocessorDefinitions=""/>
				</FileConfiguration>
			</File>
			<File
				RelativePath="SockFortInt.cpp">
				<FileConfiguration
					Name="Debug|Win32">
					<Tool
						Name="VCCLCompilerTool"
						Optimization="0"
						PreprocessorDefinitions=""/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|Win32">
					<Tool
						Name="VCCLCompilerTool"
						Optimization="2"
						PreprocessorDefinitions=""/>
				</FileConfiguration>
			</File>
			<File
				RelativePath="StdAfx.cpp">
				<FileConfiguration
					Name="Debug|Win32">
					<Tool
						Name="VCCLCompilerTool"
						Optimization="0"
						PreprocessorDefinitions=""
						UsePrecompiledHeader="1"/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|Win32">
					<Tool
						Name="VCCLCompilerTool"
						Optimization="2"
						PreprocessorDefinitions=""
						UsePrecompiledHeader="1"/>
				</FileConfiguration>
			</File>
			<File
				RelativePath="SucceedReports.cpp">
				<FileConfiguration
					Name="Debug|Win32">
					<Tool
						Name="VCCLCompilerTool"
						Optimization="0"
						PreprocessorDefinitions=""/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|Win32">
					<Tool
						Name="VCCLCompilerTool"
						Optimization="2"
						PreprocessorDefinitions=""/>
				</FileConfiguration>
			</File>
			<File
				RelativePath="SucceedReports.rc">
			</File>
			<File
				RelativePath="SucceedReportsDoc.cpp">
				<FileConfiguration
					Name="Debug|Win32">
					<Tool
						Name="VCCLCompilerTool"
						Optimization="0"
						PreprocessorDefinitions=""/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|Win32">
					<Tool
						Name="VCCLCompilerTool"
						Optimization="2"
						PreprocessorDefinitions=""/>
				</FileConfiguration>
			</File>
			<File
				RelativePath="SucceedReportsView.cpp">
				<FileConfiguration
					Name="Debug|Win32">
					<Tool
						Name="VCCLCompilerTool"
						Optimization="0"
						PreprocessorDefinitions=""/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|Win32">
					<Tool
						Name="VCCLCompilerTool"
						Optimization="2"
						PreprocessorDefinitions=""/>
				</FileConfiguration>
			</File>
			<File
				RelativePath="SUCCReport.cpp">
				<FileConfiguration
					Name="Debug|Win32">
					<Tool
						Name="VCCLCompilerTool"
						Optimization="0"
						PreprocessorDefinitions=""/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|Win32">
					<Tool
						Name="VCCLCompilerTool"
						Optimization="2"
						PreprocessorDefinitions=""/>
				</FileConfiguration>
			</File>
		</Filter>
		<Filter
			Name="Header Files"
			Filter="h;hpp;hxx;hm;inl">
			<File
				RelativePath="BuildReports.h">
			</File>
			<File
				RelativePath="excel8.h">
			</File>
			<File
				RelativePath="MainFrm.h">
			</File>
			<File
				RelativePath="Progress.h">
			</File>
			<File
				RelativePath="ReportTypes.h">
			</File>
			<File
				RelativePath="Resource.h">
			</File>
			<File
				RelativePath="..\..\Server\SLOTSessionMgr.h">
			</File>
			<File
				RelativePath="socket_class.h">
			</File>
			<File
				RelativePath="SockFortInt.h">
			</File>
			<File
				RelativePath=".\SSACStringArray.h">
			</File>
			<File
				RelativePath="StdAfx.h">
			</File>
			<File
				RelativePath="SucceedReportsDoc.h">
			</File>
			<File
				RelativePath="SucceedReportsView.h">
			</File>
			<File
				RelativePath="SUCCReport.h">
			</File>
		</Filter>
		<Filter
			Name="Resource Files"
			Filter="ico;cur;bmp;dlg;rc2;rct;bin;cnt;rtf;gif;jpg;jpeg;jpe">
			<File
				RelativePath="res\Succeed.ico">
			</File>
			<File
				RelativePath="SucceedReports.h">
			</File>
			<File
				RelativePath="res\SucceedReports.ico">
			</File>
			<File
				RelativePath="res\SucceedReports.rc2">
			</File>
			<File
				RelativePath="res\SucceedReportsDoc.ico">
			</File>
			<File
				RelativePath="res\Toolbar.bmp">
			</File>
		</Filter>
		<File
			RelativePath="ReadMe.txt">
		</File>
		<File
			RelativePath="SucceedReports.reg">
		</File>
	</Files>
	<Globals>
	</Globals>
</VisualStudioProject>

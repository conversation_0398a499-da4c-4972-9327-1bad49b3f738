#ifndef FORTSOCKINT
#define FORTSOCKINT
#include "SSACStringArray.h"
#include "stdafx.h"
/////////////////////////////////////////////////////////////
// define appropriate macros
/////////////////////////////////////////////////////////////

#define SLOTTING_PORT   5010
#define FORTE_PORT		6010
#define FORTE_PORTINTERFACE		6020
#define MAXBUF 1024


int GetReportList(CString reportName, CSsaStringArray & returnList);
void SendToForteConnection(CSsaStringArray &sendData, CSsaStringArray &recvData,
				 CString className, int operationNum);
int ExecuteQuery(const CString &queryName, const CString &query, CStringArray &results);

/////////////////////////////////////////////////////////////
// Import DLL functions
/////////////////////////////////////////////////////////////

#endif

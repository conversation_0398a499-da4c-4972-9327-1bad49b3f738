// SucceedReportsDoc.cpp : implementation of the CSucceedReportsDoc class
//

#include "stdafx.h"
#include "SucceedReports.h"

#include "SucceedReportsDoc.h"

#ifdef _DEBUG
#define new DEBUG_NEW
#endif

#include "BuildReports.h"
#include "ReportTypes.h"
#include "Progress.h"
#include "afxmt.h"

extern int reportTypeParam;
extern int bFirstTime;
extern BOOL bFirst;
BOOL bBuildingReport;

CEvent g_ThreadDone;
UINT BuildReport(LPVOID pParam);
BOOL PeekAndPump();
CString GetReportName(int reportType);
extern CString reportTitle;

//FILE *f;
/////////////////////////////////////////////////////////////////////////////
// CSucceedReportsDoc

IMPLEMENT_DYNCREATE(CSucceedReportsDoc, CDocument)

BEGIN_MESSAGE_MAP(CSucceedReportsDoc, CDocument)
	//{{AFX_MSG_MAP(CSucceedReportsDoc)
	//}}AFX_MSG_MAP
END_MESSAGE_MAP()

/////////////////////////////////////////////////////////////////////////////
// CSucceedReportsDoc construction/destruction

CSucceedReportsDoc::CSucceedReportsDoc()
{
	// TODO: add one-time construction code here
	ReadUser();
//	f = fopen("c:\\temp\\doc.out", "w");
}

CSucceedReportsDoc::~CSucceedReportsDoc()
{
//	fclose(f);
}

BOOL CSucceedReportsDoc::OnNewDocument()
{

	static BOOL bProcessing = FALSE;

	//fprintf(f, "In OnNewDocument()\n");
	//fflush(f);

	if (bProcessing)
		return TRUE;

	if (!CDocument::OnNewDocument())
		return FALSE;

	// TODO: add reinitialization code here
	// (SDI documents will reuse this document)


	if (bFirstTime == 0)
		return TRUE;

	if (reportTypeParam < 1)
		return FALSE;
	
	CString rTitle = GetReportName(reportTypeParam);
	this->SetTitle(rTitle);

	Progress *m_dlg = new Progress();
	m_dlg->Create(IDD_PROGRESS);
	m_dlg->CenterWindow();
	m_dlg->ShowWindow(SW_SHOW);
	m_dlg->UpdateWindow();
 
	m_report.m_reportType = reportTypeParam;
	bProcessing = TRUE;		// prevent trying to open a doc while thread is running
	//fprintf(f, "Before BeginThread\n");

	bBuildingReport = TRUE;
	CWinThread *pThread = AfxBeginThread(BuildReport, this);

	HANDLE hThread = pThread->m_hThread;

	//fprintf(f, "Before while loop\n");
	//fflush(f);

	BOOL bThreadDone;

	while (TRUE) {
		// check for messages so screen doesn't freeze
		//fprintf(f,"Calling PeekAndPump\n");
		//fflush(f);
		if ( !PeekAndPump() )	
			break;

		// Check to see if thread has finished
		bThreadDone = g_ThreadDone.Lock(0);
		//fprintf(f, "lock returned %d\n", bThreadDone);
		//fflush(f);
		if (bThreadDone)
			break;
	}
	
	//fprintf(f, "After while loop\n");
	//fflush(f);
	m_dlg->DestroyWindow();		// get rid of the progress window
	bBuildingReport = FALSE;
	bProcessing = FALSE;
	bFirst = TRUE;

	return TRUE;
}



/////////////////////////////////////////////////////////////////////////////
// CSucceedReportsDoc serialization

void CSucceedReportsDoc::Serialize(CArchive& ar)
{


	m_report.GetBody().Serialize(ar);
	m_report.GetHeader().Serialize(ar);
	m_report.GetData().Serialize(ar);
	m_report.m_txtArray.Serialize(ar);

	CString str;
	if (ar.IsStoring())
	{
		ar << m_report.m_ReportName;
		ar << m_report.m_FacilityName;
		ar << m_UserName;
		ar << m_report.m_nCharactersPerLine;
		ar << m_report.m_nLinesPerPage;
		ar << m_report.m_paperSizeWidth;
		ar << m_report.m_paperSizeLength;
		ar << m_report.m_columnCount;
	}
	else
	{
		ar >> m_report.m_ReportName;
		ar >> m_report.m_FacilityName;
		ar >> m_UserName;
		ar >> m_report.m_nCharactersPerLine;
		ar >> m_report.m_nLinesPerPage;
		ar >> m_report.m_paperSizeWidth;
		ar >> m_report.m_paperSizeLength;
		ar >> m_report.m_columnCount;

		bFirst = TRUE;
	}
}

/////////////////////////////////////////////////////////////////////////////
// CSucceedReportsDoc diagnostics

#ifdef _DEBUG
void CSucceedReportsDoc::AssertValid() const
{
	CDocument::AssertValid();
}

void CSucceedReportsDoc::Dump(CDumpContext& dc) const
{
	CDocument::Dump(dc);
}
#endif //_DEBUG

/////////////////////////////////////////////////////////////////////////////
// CSucceedReportsDoc commands



UINT BuildReport(LPVOID pParam)
{

	int res;
	CSUCCReport tmpReport;
	CSucceedReportsDoc *pDocument = (CSucceedReportsDoc *)pParam;

	tmpReport.m_reportType = pDocument->m_report.m_reportType;

	switch (tmpReport.m_reportType) {
	case 1:
		res = BuildRackAssignmentResultsRep(tmpReport);
		break;
	case 2:
		res = BuildRackAssignmentDetailRep(tmpReport);
		break;
	case 3:
		res = BuildProductGroupDefineRep(tmpReport, 0);
		break;
	case 4:
		res = BuildProductGroupLayoutRep(tmpReport);
		break;
	case 5:
		res = BuildProductGroupFacingsRep(tmpReport);
		break;
	case 6:
		res = BuildProductLayoutRep(tmpReport);
		break;
	case 7:
		res = BuildProductsLayoutVarWidthLocRep(tmpReport);
		break;
	case 8:
		res = BuildProductsLayoutCaseReOrientRep(tmpReport);
		break;
	case 9:
		res = BuildFacilityMoveChainsRep(tmpReport);
		break;
	case 10:
		res = BuildLocationOutboundRep(tmpReport);
		break;
	case 11:
		res = BuildAssignmentOutboundRep(tmpReport);
		break;
	case 12:
		res = BuildProductDetailRep(tmpReport);
		break;
	case 13:
		res = BuildCostAnalysisDetRep(tmpReport);
		break;
	case 14:
		res = BuildCostAnalysisSumRep(tmpReport);
		break;
	case 15:
		res = BuildProductLayoutSortByProduct(tmpReport);
		break;
	case 16:
		res = BuildProductLayoutSortByLocation(tmpReport);
		break;
	case 17:
		res = BuildProductGroupDefineRep(tmpReport, 1);
		break;
	case 18:
		res = BuildProductGroupDefineRep(tmpReport, 2);
		break;
	case 19:
		res = BuildProductGroupDefineRep(tmpReport, 3);
		break;
	case 20:
		res = BuildRackUsageSummary(tmpReport);
		break;
	case 21:
		res = BuildUnassignedProduct(tmpReport);
		break;
	case 22:
		res = BuildCapitalCostRejection(tmpReport);
	}

	pDocument->m_report = tmpReport;

	//fprintf(f, "Returning from BuildReport\n");
	//fflush(f);
	// Tell the parent thread that we are done
	g_ThreadDone.SetEvent();

	
	return res;
}

void CSucceedReportsDoc::ReadUser()
{
	HKEY hRegKey;
	DWORD dwReturnLength;
	DWORD dwType = REG_SZ;
	int numTimes;
	char u[17];

	if (RegOpenKeyEx(HKEY_LOCAL_MACHINE, "Software\\SSA Global\\Optimize",0, KEY_READ, &hRegKey) != ERROR_SUCCESS) {
		AfxMessageBox("Cannot Open Registry");
		return;
	}

	//memset(childWindowState,0,17);
	numTimes = 0;
	while (RegQueryValueEx(hRegKey, "UserID", NULL, &dwType, 
		(LPBYTE)u, &dwReturnLength) != ERROR_SUCCESS && numTimes <= 100)
		numTimes++;

	if (numTimes > 100) {
		AfxMessageBox("Cannot Open Registry - username");
		m_UserName = "ssauser";}
	else
		m_UserName = (CString)u;

	RegCloseKey(hRegKey);


	return;
}


BOOL PeekAndPump()
{
	MSG msg;
	while (::PeekMessage(&msg, NULL, 0, 0, PM_NOREMOVE)) {
		//fprintf(f, "In PeekAndPump loop\n");
		//fflush(f);
		if (!AfxGetApp()->PumpMessage()) {
			//fprintf(f, "Posting quit message\n");
			//fflush(f);
			::PostQuitMessage(0);
			return FALSE;
		}
		Sleep(500);
	}
	
	LONG lIdle = 0;
	while (AfxGetApp()->OnIdle(lIdle++)) {
		//fprintf(f, "In OnIdle loop\n");
		//fflush(f);
	}

	//fprintf(f, "After OnIdle Loop\n");
	//fflush(f);

	return TRUE;

}

CString GetReportName(int reportType)
{
	
	CString reportName;
	
	switch (reportType) {
	case 1:
		reportName = "Capital Cost Summary";
		break;
	case 2:
		reportName = "Capital Cost Detail";
		break;
	case 3:
		reportName = "Product Group Definition";
		break;
	case 4:
		reportName = "Product Group Layout";
		break;
	case 5:
		reportName = "Product Group Facings";
		break;
	case 6:
		reportName = "Product Layout - Assignments";
		break;
	case 7:
		reportName = "Variable Width Locations";
		break;
	case 8:
		reportName = "Product Layout - Case Re-Orientation";
		break;
	case 9:
		reportName = "Product Moves";
		break;
	case 10:
		reportName = "Location Outbound";
		break;
	case 11:
		reportName = "Product Assignment Outbound";
		break;
	case 12:
		reportName = "Product Detail";
		break;
	case 13:
		reportName = "Cost Analysis Detail";
		break;
	case 14:
		reportName = "Cost Analysis Summary";
		break;
	case 15:
	case 16:
		reportName = "Product Layout - Assignments";
		break;
	case 17:
	case 18:
	case 19:
		reportName = "Product Group Layout";
		break;
	case 20:
		reportName = "Rack Usage Summary";
		break;
	case 21:
		reportName = "Unassigned Product";
		break;
	case 22:
		reportName = "Capital Cost Rejection";
		break;
	default:
		reportName = "SSA Optimize Reports";
	}
	
	return reportName;
}

BOOL CSucceedReportsDoc::OnOpenDocument(LPCTSTR lpszPathName) 
{
	if (!CDocument::OnOpenDocument(lpszPathName))
		return FALSE;
	
	// TODO: Add your specialized creation code here
	return TRUE;
}


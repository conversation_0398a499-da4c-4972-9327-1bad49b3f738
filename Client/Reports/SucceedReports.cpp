// SucceedReports.cpp : Defines the class behaviors for the application.
//

#include "stdafx.h"
#include "SucceedReports.h"

#include "MainFrm.h"
#include "SucceedReportsDoc.h"
#include "SucceedReportsView.h"
#include "Progress.h"

#include "base.h"
#include "MiscClasses.h"
#include "SLOTSessionMgr.h"
#include "SrvcObjs.h"


#ifdef _DEBUG
#define new DEBUG_NEW
#endif

int reportTypeParam;
int bFirstTime;
extern HANDLE hThread;
extern BOOL bBuildingReport;
extern long currentFacilityID;
extern CString currentDatabase;  //Naveen 15Feb06 Reports Fix
/////////////////////////////////////////////////////////////////////////////
// CSucceedReportsApp

// CgetdataApp

BEGIN_MESSAGE_MAP(CSucceedReportsApp, CWinApp)

	ON_COMMAND(ID_APP_ABOUT, OnAppAbout)
	// Standard file based document commands
	ON_COMMAND(ID_FILE_NEW, CWinApp::OnFileNew)
	ON_COMMAND(ID_FILE_OPEN, CWinApp::OnFileOpen)
	// Standard print setup command
	ON_COMMAND(ID_FILE_PRINT_SETUP, CWinApp::OnFilePrintSetup)
END_MESSAGE_MAP()

/////////////////////////////////////////////////////////////////////////////
// CSucceedReportsApp construction

CSucceedReportsApp::CSucceedReportsApp()
{
	// TODO: add construction code here,
	// Place all significant initialization in InitInstance
	DBName = NULL;
}

CSucceedReportsApp::~CSucceedReportsApp()
{
	// TODO: add construction code here,
	// Place all significant initialization in InitInstance
	if(DBName)
	{
		getSessionMgrSO()->DisconnectDatabase(*DBName);
		delete DBName;
	}
}

/////////////////////////////////////////////////////////////////////////////
// The one and only CSucceedReportsApp object

CSucceedReportsApp theApp;

/////////////////////////////////////////////////////////////////////////////
// CSucceedReportsApp initialization

BOOL CSucceedReportsApp::InitInstance()
{
	string userName, password, database;

	if(!AfxOleInit())  // Your addition starts here
	{
		AfxMessageBox("Could not initialize COM dll");
		return FALSE;
	}                 // End of your addition

      
	AfxEnableControlContainer();

	// Standard initialization
	// If you are not using these features and wish to reduce the size
	//  of your final executable, you should remove from the following
	//  the specific initialization routines you do not need.

#ifdef _AFXDLL
	Enable3dControls();			// Call this when using MFC in a shared DLL
#else
	Enable3dControlsStatic();	// Call this when linking to MFC statically
#endif

	// Change the registry key under which our settings are stored.
	// You should modify this string to be something appropriate
	// such as the name of your company or organization.
	SetRegistryKey(_T("Local AppWizard-Generated Applications"));

	LoadStdProfileSettings();  // Load standard INI file options (including MRU)

	//Ana's code...
	//Look for the report type parameter
	reportTypeParam = -1;

	//If no slashes are present we skip processing of arguments
	//and assume a filename is specified and open the specified report file. 
	CString tmpCmdLine = m_lpCmdLine;
	if (tmpCmdLine.Find("/") >= 0) {
		int idx = tmpCmdLine.Find("/r");
		if (idx < 0) {
			AfxMessageBox("Invalid parameters specified for report.");
			return FALSE;
		}

		int idx2 = tmpCmdLine.Find(" ", idx+2);
		if (idx2 < 0)
			idx2 = tmpCmdLine.GetLength();

		reportTypeParam = atoi(tmpCmdLine.Mid(idx+2,idx2-(idx+2)));
		
		idx = tmpCmdLine.Find("/f", idx2);
		if (idx < 0)
			currentFacilityID = 0;
		else {
			idx2 = tmpCmdLine.Find(" ", idx+2);
			if (idx2 < 0)
				idx2 = tmpCmdLine.GetLength();
			currentFacilityID = atoi(tmpCmdLine.Mid(idx+2, idx2-(idx+2)));
		}

		idx = tmpCmdLine.Find("/u", idx2);
		if (idx < 0){
			AfxMessageBox("Invalid parameters specified for report.");
			return FALSE;
		}
		else {
			idx2 = tmpCmdLine.Find(" ", idx+2);
			if (idx2 < 0)
				idx2 = tmpCmdLine.GetLength();
			userName = (LPCTSTR)tmpCmdLine.Mid(idx+2, idx2-(idx+2));
		}
		idx = tmpCmdLine.Find("/p", idx2);
		if (idx < 0){
			AfxMessageBox("Invalid parameters specified for report.");
			return FALSE;
		}
		else {
			idx2 = tmpCmdLine.Find(" ", idx+2);
			if (idx2 < 0)
				idx2 = tmpCmdLine.GetLength();
			password = (LPCTSTR)tmpCmdLine.Mid(idx+2, idx2-(idx+2));
		}

		idx = tmpCmdLine.Find("/d", idx2);
		if (idx < 0){
			AfxMessageBox("Invalid parameters specified for report.");
			return FALSE;
		}
		else {
			idx2 = tmpCmdLine.Find(" ", idx+2);
			if (idx2 < 0)
				idx2 = tmpCmdLine.GetLength();
			database = (LPCTSTR)tmpCmdLine.Mid(idx+2, idx2-(idx+2));
			currentDatabase = database.c_str(); //Naveen 15Feb06 Reports Fix
		}

		getSessionMgrSO()->ConnectDatabaseHelper(userName, password, database);
		DBName = new string(database);		
	}

	// Register the application's document templates.  Document templates
	//  serve as the connection between documents, frame windows and views.

	CSingleDocTemplate* pDocTemplate;
	pDocTemplate = new CSingleDocTemplate(
		IDR_MAINFRAME,
		RUNTIME_CLASS(CSucceedReportsDoc),
		RUNTIME_CLASS(CMainFrame),       // main SDI frame window
		RUNTIME_CLASS(CSucceedReportsView));
	AddDocTemplate(pDocTemplate);

	// Enable DDE Execute open
	EnableShellOpen();
	RegisterShellFileTypes(TRUE);
	/*
	int indx = tmpCmdLine.Find("/r");
	char reportTypeParamStr[1000];
	strcpy(reportTypeParamStr,"");
	for (int i = indx+2; i<tmpCmdLine.GetLength(); i++)
	{
		if ((tmpCmdLine == ' '))
			break;
		
		char t[2];
		t[0] = tmpCmdLine[i]; t[1] = '\0';
		strcat(reportTypeParamStr,t);
	}
	//AfxMessageBox(reportTypeParamStr);
	reportTypeParam = atoi(reportTypeParamStr);
	CString tmp;
	tmp.Format("param: %d", reportTypeParam);


	//AfxMessageBox(tmp);
	//now that we have the report type, remove this parameter
	//so that the rest of the command line can be process normally
	//by the MFC code.
	if (i >= tmpCmdLine.GetLength())
		m_lpCmdLine = "";
	else
	{
		tmpCmdLine = tmpCmdLine.Mid(i+1);
		strcpy(m_lpCmdLine,tmpCmdLine);
		//AfxMessageBox(m_lpCmdLine);
	}
	
	if (reportTypeParamStr == "")
		return FALSE;
	//end Ana's code...
	*/

	// Parse command line for standard shell commands, DDE, file open
	CCommandLineInfo cmdInfo;
	ParseCommandLine(cmdInfo);

	bFirstTime = 0;				// don't build report until the view is created

	// Dispatch commands specified on the command line
	if (!ProcessShellCommand(cmdInfo))
		return FALSE;
	
	bFirstTime = 1;				// build report
	bBuildingReport = TRUE;
	
	// The one and only window has been initialized, so show and update it.
	m_pMainWnd->ShowWindow(SW_SHOW);
	m_pMainWnd->UpdateWindow();

	if (reportTypeParam > 0)
		OnFileNew();			// build a new report
	else {
		if (*m_lpCmdLine == 0)
			OnFileOpen();	   // Show open file dialog if no file is specified on the command line
	}

	m_pMainWnd->Invalidate();	// force redraw of view

	// Enable drag/drop open
	m_pMainWnd->DragAcceptFiles();


	return TRUE;
}


/////////////////////////////////////////////////////////////////////////////
// CAboutDlg dialog used for App About

class CAboutDlg : public CDialog
{
public:
	CAboutDlg();

// Dialog Data
	enum { IDD = IDD_ABOUTBOX };

protected:
	virtual void DoDataExchange(CDataExchange* pDX);    // DDX/DDV support

// Implementation
protected:
	DECLARE_MESSAGE_MAP()
};

CAboutDlg::CAboutDlg() : CDialog(CAboutDlg::IDD)
{
}

void CAboutDlg::DoDataExchange(CDataExchange* pDX)
{
	CDialog::DoDataExchange(pDX);
}

BEGIN_MESSAGE_MAP(CAboutDlg, CDialog)
END_MESSAGE_MAP()

// App command to run the dialog
void CSucceedReportsApp::OnAppAbout()
{
	CAboutDlg aboutDlg;
	aboutDlg.DoModal();
}

/////////////////////////////////////////////////////////////////////////////
// CSucceedReportsApp commands

CDocument* CSucceedReportsApp::OpenDocumentFile(LPCTSTR lpszFileName) 
{
	// TODO: Add your specialized code here and/or call the base class
	
	return CWinApp::OpenDocumentFile(lpszFileName);
}

BOOL CSucceedReportsApp::PeekAndPump()
{
	MSG msg;
	while (::PeekMessage(&msg, NULL, 0, 0, PM_NOREMOVE)) {
		if (!AfxGetApp()->PumpMessage()) {
			::PostQuitMessage(0);
			return FALSE;
		}
	}
	
	LONG lIdle = 0;
	while (AfxGetApp()->OnIdle(lIdle++));

	return TRUE;

}

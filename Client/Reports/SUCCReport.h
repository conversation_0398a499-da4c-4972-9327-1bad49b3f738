#ifndef CSUCCReportS_H
#define CSUCCReportS_H
#include <afxtempl.h>


class CSUCCReport
{
public:
	int m_columnCount;
	CString m_FacilityName;
	CString m_ReportName;
	CStringArray m_txtArray;

	//data members
	int m_nLinesPerPage;
	int m_nCharactersPerLine;
	int m_reportType;
	float m_paperSizeWidth;
	float m_paperSizeLength;

	//methods
	CStringArray & GetHeader()	  { return m_header; }
	CStringArray & GetFooter()	  { return m_footer; }
	CStringArray & GetBody()	  { return m_body; }
	CStringArray & GetData()	  { return m_data; }

	CString & GetTitle()		  { return m_title; }	
	void SetHeader(const CStringArray &h)  	   { m_header.Copy(h); }
	void SetFooter(const CStringArray &f)  	   { m_footer.Copy(f); }
	void SetBody(const CStringArray &b)		   { m_body.Copy(b); }
	void SetData(const CStringArray &d)		   { m_data.Copy(d); }

	void SetTitle(CString t)				{ m_title = t; }
	int GetReportSize();
	int GetHeaderSize();
	int GetFooterSize();
	int GetBodySize();
	int GetDataSize();

	void GetHeaderLineAt(CString &s, int indx);
	void GetFooterLineAt(CString &s, int indx);
	void GetBodyLineAt(CString &s, int indx);
	void GetDataLineAt(CString &s, int indx);

	void AddHeaderLine(CString s);
	void AddFooterLine(CString s);
	void AddBodyLine(CString s);
	void AddDataLine(CString s);

	void AddHeaderLineCntr(CString s);

	CSUCCReport()  
		{	m_header.RemoveAll(); 
			m_footer.RemoveAll(); 
			m_body.RemoveAll();
			m_nLinesPerPage			= 54; 
			m_nCharactersPerLine	= 75; 
			m_title					= "";
			m_paperSizeWidth		= 0;
			m_paperSizeLength		= 0;
			m_firstPage.RemoveAll();
			m_lastPage.RemoveAll();
			m_data.RemoveAll();
			m_txtArray.RemoveAll();
			m_columnCount = 0;
		}

	virtual ~CSUCCReport() {}

	CSUCCReport& operator=(CSUCCReport &other);
	

private:

	//data members
	CStringArray m_header;
	CStringArray m_footer;
	CStringArray m_body;
	CString m_title;
	CStringArray m_firstPage;
	CStringArray m_lastPage;
	CStringArray m_data;
	
	//methods
};


#endif

#if !defined(AFX_PROGRESS_H__E6886571_9EB2_11D3_AFF7_0080C7FFDED7__INCLUDED_)
#define AFX_PROGRESS_H__E6886571_9EB2_11D3_AFF7_0080C7FFDED7__INCLUDED_

#if _MSC_VER >= 1000
#pragma once
#endif // _MSC_VER >= 1000
// Progress.h : header file
//

/////////////////////////////////////////////////////////////////////////////
// Progress dialog

class Progress : public CDialog
{
// Construction
public:
	Progress(CWnd* pParent = NULL);   // standard constructor

// Dialog Data
	//{{AFX_DATA(Progress)
	enum { IDD = IDD_PROGRESS };
	//}}AFX_DATA


// Overrides
	// ClassWizard generated virtual function overrides
	//{{AFX_VIRTUAL(Progress)
	protected:
	virtual void DoDataExchange(CDataExchange* pDX);    // DDX/DDV support
	virtual void PostNcDestroy();
	//}}AFX_VIRTUAL

// Implementation
protected:

	// Generated message map functions
	//{{AFX_MSG(Progress)
		// NOTE: the ClassWizard will add member functions here
	//}}AFX_MSG
	DECLARE_MESSAGE_MAP()
};

//{{AFX_INSERT_LOCATION}}
// Microsoft Developer Studio will insert additional declarations immediately before the previous line.

#endif // !defined(AFX_PROGRESS_H__E6886571_9EB2_11D3_AFF7_0080C7FFDED7__INCLUDED_)

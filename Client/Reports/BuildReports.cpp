#include <afxtempl.h>
#include "stdafx.h"
#include "SUCCReport.h"
#include "SSACStringArray.h"
#include "SockFortInt.h"
#include "constants.h"

#define DEBUG 0
#define SLOT_NIL_INTEGER -32767
#define SLOT_NIL_FLOAT -32767

CSsaStringArray resultList;
long currentFacilityID;
CString currentDatabase;  //Naveen 15Feb06 Reports Fix

void ParseString(const CString &string, const CString &delimiter, CStringArray &strings);
CString GetCurrentFacilityName();

///////////////////////////////////////////////////////////////////
//Inserts a string (strToAdd) into another string (targetStr)    //
//on the given zero-based index (col).                           //
///////////////////////////////////////////////////////////////////
void AddStrAtColumn(CString &targetStr, CString strToAdd, int col,
					int maxChInLine, BOOL removeExtraChars = FALSE)
{
	int l1, l2, l3;
	CString str1;
	CString str2;

	if ((col+1) > maxChInLine)
		return;

	l1 = targetStr.GetLength();
	l2 = strToAdd.GetLength();

	if ( col > l1)
	{
		for (int i = l1; i < col; i++)
			targetStr += " ";
		targetStr += strToAdd;
	}
	else
	{
		str1 = targetStr.Mid(0,col);
		str2 = targetStr.Mid(col);
		if (removeExtraChars)
			targetStr = str1 + strToAdd;
		else
			targetStr = str1 + strToAdd + str2;
	}

	l3 = targetStr.GetLength();
	if (l3 > maxChInLine)
	{
		targetStr = targetStr.Mid(0, maxChInLine);
	}

}


////////////////////////////////////////////////////////////
//Replaces the Page# code (\\PAGE#\\) with the word "Page //
//followed by the actual page number.                     //
////////////////////////////////////////////////////////////
void InsertPageNumber(CString &str, int pageNum)
{
	CString pgStr;
	CString str1;
	CString str2;
	int indx = str.Find("\\PAGE#\\");
	if (indx == -1)
		return;

	//remove Page Code first
	str1 = str.Mid(0,indx);
	str2 = str.Mid(indx+7);
	str1 += str2;

	//now Format string with page number and insert into the line
	pgStr.Format("Page %d", pageNum);
	AddStrAtColumn(str1, pgStr, indx, 133);

	str = str1;

}

//void GetNext(CString &retString, CString &line, CString format)
CString GetNext(CString &line, CString format)
{
	int indx;
	CString retString;
	CString type;
	CString str;
	double f;
	long i;
	CString s;

	if (line == "%")
		return "eof";

	indx = line.Find("|");
	if (indx == -1)
		indx = line.GetLength();

	s = line.Mid(0,indx);
	type = format.Right(1);

	if (type == "f") {
		f = atof(s);
		if (f == SLOT_NIL_FLOAT)
				retString = "n/a";
		else
			retString.Format(format, f);
	}
	else if (type == "d") {
		i = atol(s);
		if (i == SLOT_NIL_INTEGER)
			retString = "n/a";
		else
			retString.Format(format, i);
	}
	else if (type == "b") {
		str.Format(s, "%-1s");
		if (str == "1")
			retString = "Y";
		else
			retString = "N";
	}
	else
		retString.Format(format, s);
	
	line = line.Mid(indx+1);
	if ( line == "" )
		line = "%";
	
	return retString;
}


CString ConvertBayType(int nBayType)
{
	CString szBayType;
	switch (nBayType) {
	case 1:
		szBayType = "bin       ";
		break;
	case 2:
		szBayType = "drivein   ";
		break;
	case 3:
		szBayType = "floor     ";
		break;
	case 4:
		szBayType = "caseflow  ";
		break;
	case 5:
		szBayType = "pallet    ";
		break;
	case 6:
		szBayType = "PIR       ";
		break;
	case 7:
		szBayType = "carousel  ";
		break;
	case 8:
		szBayType = "palletflow";
		break;
	default:
		szBayType = "Unknown   ";
	}

	return szBayType;
}


CString ConvertUOI(int uoi)
{
	CString szUOI;
	switch (uoi) {
	case UOI_EACH:
		szUOI = "Each";
		break;
	case UOI_INNER:
		szUOI = "Inner";
		break;
	case UOI_CASE:
		szUOI = "Case";
		break;
	case UOI_PALLET:
		szUOI = "Pallet";
		break;
	}

	return szUOI;

}


int BuildRackAssignmentResultsRep(CSUCCReport &rep)
{
	CString tmpStr;
	CString tmpStr2;
	CString tmpStr3;
	CString dataType;

	CString bayProfile;
	CString cost;
	CString facingCount, bayCount;
	CStringArray strBestAvail;
	CStringArray strIdeal;

	CString facility;

	CString txtStr, txtBestAvail, txtIdeal;
	CStringArray arrayBestAvail, arrayIdeal;

	int indx;
	double totCostA = 0, totBayCountA = 0, totFacingCountA = 0;
	double totCostI = 0, totBayCountI = 0, totFacingCountI = 0;
	//CSsaStringArray resultList;

	//set general report data
	rep.m_nLinesPerPage = 60;  //69
	rep.m_nCharactersPerLine = 154;
	rep.m_paperSizeWidth = 11;
	rep.m_paperSizeLength = 8.5;
	rep.m_ReportName = "Capital Cost Summary";

	//call forte to get report data
	//Get list of bay profile names from the database

	try
	{
		resultList.RemoveAll();
		if (! DEBUG )
			GetReportList("RackAssignments", resultList);
		else {
			resultList.Add("F|15-Pass4|");
			resultList.Add("I|carousel\\HalfBasket|29|12675|15|");
			resultList.Add("I|PIR\\SmallOpening|1|490|1|");
			resultList.Add("I|pallet\\TwoLevelSelectWithReserves|1|420|1|");
			resultList.Add("I|caseflow\\FiveLevelCaseFlow|46|1200|3|");
			resultList.Add("I|caseflow\\VariableWidthFiveLevelCaseFlow|6|400|1|");
			resultList.Add("I|bin\\VariableWidthThreeLevelStaticShelf|5|175|1|");
			resultList.Add("B|pallet\\Whse15-108Crossbars|70|6300|18|");
			resultList.Add("B|pallet\\Whse15-96Crossbars|5|700|2|");
			resultList.Add("I|carousel\\OneThirdBasket|7|2535|3|");
		}

	}
	catch (...)
	{
		AfxMessageBox("Error Getting RackAssignResults from Database.");
		return -1;
	}


	strBestAvail.RemoveAll();
	strIdeal.RemoveAll();

	//create the report body
	for (int i=0; i < resultList.GetSize(); i++)
	{
		tmpStr3 = "";
		tmpStr = resultList.GetAt(i);
		rep.AddDataLine(tmpStr);
		indx   = tmpStr.Find("|");
		dataType = tmpStr.Mid(0,indx);
		tmpStr   = tmpStr.Mid(indx+1);

		if ((dataType == "B") || (dataType == "I"))
		{
			indx = tmpStr.Find("|");
			tmpStr2 = tmpStr.Mid(0, indx);
			bayProfile.Format("%-40.40s", tmpStr2);
			tmpStr = tmpStr.Mid(indx+1);

			indx = tmpStr.Find("|");
			tmpStr2 = tmpStr.Mid(0,indx);	
			facingCount.Format("%8.8s", tmpStr2);
			tmpStr = tmpStr.Mid(indx+1);

			indx = tmpStr.Find("|");
			tmpStr2 = tmpStr.Mid(0, indx);
			cost.Format("%11.11s", tmpStr2);
			tmpStr  = tmpStr.Mid(indx+1);

			indx = tmpStr.Find("|");
			tmpStr2 = tmpStr.Mid(0, indx);
			bayCount.Format("%8.8s", tmpStr2);
			tmpStr  = tmpStr.Mid(indx+1);

			if (dataType == "B")
			{
				AddStrAtColumn(tmpStr3, bayProfile, 0, rep.m_nCharactersPerLine);
				AddStrAtColumn(tmpStr3, facingCount, 42, rep.m_nCharactersPerLine);
				AddStrAtColumn(tmpStr3, bayCount, 53, rep.m_nCharactersPerLine);
				AddStrAtColumn(tmpStr3, cost, 64, rep.m_nCharactersPerLine);
				strBestAvail.Add(tmpStr3);
				totFacingCountA += atof(facingCount);
				totBayCountA += atof(bayCount);
				totCostA += atof(cost);
				txtBestAvail.Format("\"%s\"|\"%s\"|\"%s\"|\"%s\"|", bayProfile, facingCount, bayCount, cost);
				arrayBestAvail.Add(txtBestAvail);
			}
			else
			{
				AddStrAtColumn(tmpStr3, bayProfile, 0, rep.m_nCharactersPerLine);
				AddStrAtColumn(tmpStr3, facingCount, 42, rep.m_nCharactersPerLine);
				AddStrAtColumn(tmpStr3, bayCount, 53, rep.m_nCharactersPerLine);
				AddStrAtColumn(tmpStr3, cost, 64, rep.m_nCharactersPerLine);
				strIdeal.Add(tmpStr3);
				totFacingCountI += atof(facingCount);
				totBayCountI += atof(bayCount);
				totCostI += atof(cost);
				txtIdeal.Format("|\"%s\"|\"%s\"|\"%s\"|\"%s\"|", bayProfile, facingCount, bayCount, cost);
				arrayIdeal.Add(txtIdeal);
			}
		}
		else
		{
			if (dataType == "F")
			{
				indx = tmpStr.Find("|");
				facility = tmpStr.Mid(0, indx);
				tmpStr = tmpStr.Mid(indx+1);
				rep.m_FacilityName = facility;

	            //          1         2         3         4         5         6         7         8         9         10        11        12        13        14
	            //012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012
				//rep.AddHeaderLine("                          Best Available                                                     Ideal                      ");
				rep.AddHeaderLine("                                            Facing       Bay                                                              Facing       Bay");
				rep.AddHeaderLine("Best Available Bay Profile                   Count      Count          Cost   Ideal Bay Profile                            Count      Count          Cost");
				rep.AddHeaderLine("-----------------------------------------    -----      -----          ----   -----------------------------------------    -----      -----          ----");
				rep.AddHeaderLine(" ");
			}
		}
	}


	txtStr = "Best Available Bay Profile|Facing Count|Bay Count|Cost||Ideal Bay Profile|Facing Count|Bay Count|Cost|";
	rep.m_txtArray.Add(txtStr);
	rep.m_columnCount = 9;

	for (i=0; (i < strBestAvail.GetSize()) || (i < strIdeal.GetSize()); i++)
	{
		CString repStr;
		txtStr = "";
		if (i < strBestAvail.GetSize()) {
			repStr = strBestAvail.GetAt(i);
			txtStr += arrayBestAvail[i];
		}
		else {
			txtStr += "||||";
		}
		if (i < strIdeal.GetSize()) {
			tmpStr3 = strIdeal.GetAt(i);
			AddStrAtColumn(repStr, tmpStr3, 78, rep.m_nCharactersPerLine);
			txtStr += arrayIdeal[i];
		}
		else {
			txtStr += "|||||";
		}
		
		rep.m_txtArray.Add(txtStr);

		rep.AddBodyLine(repStr); 

	}



	if (strBestAvail.GetSize() > 0 || strIdeal.GetSize() > 0) {
	rep.AddBodyLine("");
	rep.AddBodyLine("");

	tmpStr3 = "<B>Totals: ";
	tmpStr3 += "                                ";
	tmpStr.Format("%10.0f",totFacingCountA);
	tmpStr3 += tmpStr;
	tmpStr.Format("%10.0f",totBayCountA);
	tmpStr3 += " " + tmpStr;
	tmpStr.Format("%13.0f", totCostA);
	tmpStr3 += " " + tmpStr;
	tmpStr3 += "    ";
	tmpStr.Format("%10.0f",totFacingCountI);
	tmpStr3 += "                                       " + tmpStr;
	tmpStr.Format("%10.0f",totBayCountI);
	tmpStr3 += " " + tmpStr;
	tmpStr.Format("%13.0f",totCostI);
	tmpStr3 += " " + tmpStr;
	tmpStr3 += "</B>";

	rep.AddBodyLine(tmpStr3);
	}
	return 0;

}



int BuildRackAssignmentDetailRep(CSUCCReport &rep)
{
	CString tmpStr;
	CString tmpStr2;
	CString dataType;

	CString prodPackId, prevProdPackId, prodPackDesc, skip, line;
	CString bayProfile;
	CString facingCount;

	CString facility;
	int indx;


	//set general report data
	rep.m_nLinesPerPage = 60;  
	rep.m_nCharactersPerLine = 152;
	rep.m_paperSizeWidth = 11;
	rep.m_paperSizeLength = 8.5;
	rep.m_ReportName = "Capital Cost Detail";

	//call forte to get report data
	//Get list of bay profile names from the database
	try
	{
		resultList.RemoveAll();
		if (! DEBUG)
			GetReportList("RackAssignmentsDetail", resultList);
		else {
			resultList.Add("F|bdtest|");
			resultList.Add("D|3630|test|1191411221-0|0|carousel\\FullBasket|7|1|1|drivein\\ThreeDeepFourHigh|2|1|Pallet|Not Fit|");
			resultList.Add("D|3630|test|1191411221-0|0|carousel\\FullBasket|7|2|1|drivein\\ThreeDeepFourHigh|2|1|Case|Not Fit|");
			resultList.Add("D|3630|test|1191411221-0|0|carousel\\FullBasket|7|3|1|drivein\\ThreeDeepFourHigh|2|1|Pallet|Not Fit|");
			resultList.Add("D|3631|test|1191411232-0|0|carousel\\FullBasket|7|1|1|drivein\\ThreeDeepFourHigh|2|1|Pallet|Not Fit|");
			resultList.Add("D|3631|test|1191411232-0|0|carousel\\FullBasket|7|2|1|drivein\\ThreeDeepFourHigh|2|1|Pallet|Not Fit|");
			resultList.Add("D|3631|test|1191411232-0|0|carousel\\FullBasket|7|3|1|drivein\\ThreeDeepFourHigh|2|1|Pallet|Not Fit|");
			resultList.Add("D|3632|test|1191411233-0|0|carousel\\FullBasket|7|1|1|drivein\\ThreeDeepFourHigh|2|1|Case|Not Fit|");
			resultList.Add("D|3632|test|1191411233-0|0|carousel\\FullBasket|7|2|1|drivein\\ThreeDeepFourHigh|2|1|Case|Not Fit|");
			resultList.Add("D|3632|test|1191411233-0|0|carousel\\FullBasket|7|3|1|drivein\\ThreeDeepFourHigh|2|1|Case|Not Fit|");
			resultList.Add("D|3633|test|1191411234-0|0|carousel\\FullBasket|7|1|1|drivein\\ThreeDeepFourHigh|2|1|Pallet|Not Fit|");
			resultList.Add("D|3633|test|1191411234-0|0|carousel\\FullBasket|7|2|1|drivein\\ThreeDeepFourHigh|2|1|Pallet|Not Fit|");
			resultList.Add("D|3633|test|1191411234-0|0|carousel\\FullBasket|7|3|1|drivein\\ThreeDeepFourHigh|2|1|Pallet|Not Fit|");
			resultList.Add("D|3634|test|1191411245-0|0|carousel\\FullBasket|7|1|1|drivein\\ThreeDeepFourHigh|2|1|Case|Not Fit|");
			resultList.Add("D|3634|test|1191411245-0|0|carousel\\FullBasket|7|2|1|drivein\\ThreeDeepFourHigh|2|1|Case|Not Fit|");
			resultList.Add("D|3634|test|1191411245-0|0|carousel\\FullBasket|7|3|1|drivein\\ThreeDeepFourHigh|2|1|Case|Not Fit|");
			// ppid|ppdesc|wmsid|wmsdetailid|availprof|baytype|ranking|facings|idealprof|baytype|Facings
		}
	}
	catch (...)
	{
		AfxMessageBox("Error Getting RackAssignDetail from Database.");
		return -1;
	}

	prevProdPackId = "";

	tmpStr = "Product Description|WMS ID|WMS Detail ID|Best Available Profile|Level Type|Ranking|Facings|Handling Method|"
		"Fit|Ideal Profile|Level Type|Facings|";
	rep.m_txtArray.Add(tmpStr);
	rep.m_columnCount = 12;

	//create the report body
	for (int i=0; i < resultList.GetSize(); i++)
	{
		tmpStr = resultList.GetAt(i);
		rep.AddDataLine(tmpStr);
		dataType = GetNext(tmpStr, "%s");

		if (dataType == "D")
		{

			rep.m_txtArray.Add(tmpStr.Right(tmpStr.GetLength() - tmpStr.Find("|")-1));
			line = "";
			prodPackId = GetNext(tmpStr, "%d");
			prodPackDesc = GetNext(tmpStr, "%-45.45s");
			if (prodPackId != prevProdPackId) {

				if (prevProdPackId != "") { rep.AddBodyLine(""); rep.AddBodyLine(""); }

				line += "<B>Product: " + prodPackDesc;
				line += "   WMS Product ID: " + GetNext(tmpStr, "%-20.20s");	// wms product id
				line += "   WMS Detail ID: " + GetNext(tmpStr, "%-10.10s");	// wms product detail
				line += "</B>";
				rep.AddBodyLine(line);

				line = "<B>Available Profiles                    ";
				line += "Level Type  ";
				line += "Ranking ";
				line += "Facings    ";
				line += "Handling Method ";
				line += "Fit         ";
				line += "Ideal Profile                        ";
				line += "Level Type ";
				line += "Facings ";

				rep.AddBodyLine(line);

				line = "<B>------------------                    ";
				line += "----------  ";
				line += "------- ";
				line += "-------    ";
				line += "--------------- ";
				line += "----------- ";
				line += "-----------------------              ";
				line += "---------- ";
				line += "-------</B>";

				rep.AddBodyLine(line);

			}
			else {
				skip = GetNext(tmpStr, "%s");		// skip wms id
				skip = GetNext(tmpStr, "%s");		// skip wms detail
			}
			
			line = GetNext(tmpStr, "%-35.35s");				// available profile
			//line += "   " + ConvertBayType(atoi(GetNext(tmpStr, "%d")));	// level type
			line += "   " + GetNext(tmpStr, "%-10s");	// level type
			line += "      " + GetNext(tmpStr, "%3d");		// ranking
			line += " " + GetNext(tmpStr, "%7d");			// facings
			line += "    " + GetNext(tmpStr, "%-15s");			// handling method
			line += " " + GetNext(tmpStr, "%-10s");			// fit

			if (prodPackId != prevProdPackId) {
				line += "  " + GetNext(tmpStr, "%-34.34s");	// ideal profile
				line += "   " + GetNext(tmpStr, "%-10s");	// level type
				line += " " + GetNext(tmpStr, "%7d");			// facings
			}
			else {		// skip the ideal since there should only be one
				skip = GetNext(tmpStr, "%s");
				skip = GetNext(tmpStr, "%s");
				skip = GetNext(tmpStr, "%s");
			}
			
			rep.AddBodyLine(line);

			prevProdPackId = prodPackId;
			
		}
		else
		{
			if (dataType == "F")
			{
				indx = tmpStr.Find("|");
				facility = tmpStr.Mid(0, indx);
				tmpStr = tmpStr.Mid(indx+1);
				rep.m_FacilityName = facility;

			}
		}
	}



	return 0;

}



int BuildProductGroupDefineRep(CSUCCReport &rep, int sortFlag)
{

	CStringArray groupList, productList;
	CStringArray groupFields, productFields;
	long prodGroup, prevProdGroup;
	CString line, query, groupName;
	CString headerFormat, detailFormat, textFormat, currentFormat, firstColumn;
	int rc;

	headerFormat = "<B>%-35.35s</B>    %-35.35s    %-20.20s       %-20.20s    %9.2f    %9.2f    %-7s";
	detailFormat = "  %-33.33s    %-35.35s    %-20.20s       %-20.20s    %9.2f    %9.2f    %-7s";
	textFormat = "%s|%s|%s|%s|%s|%9.2f|%9.2f|%7s|";

	//set general report data
	rep.m_nLinesPerPage = 60;  //69
	rep.m_nCharactersPerLine = 155;
	rep.m_paperSizeWidth = 8.5;
	rep.m_paperSizeLength = 11;
	rep.m_ReportName = "Product Group Definition";
	rep.m_FacilityName = GetCurrentFacilityName();
	
	//          1         2         3         4         5         6         7         8         9         10        11        12        13        14 
	//012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789
	rep.AddHeaderLine("Product Group                          Product Description                    WMS ProductID              WMS Product Detail ID    Movement          BOH    UOI");
	rep.AddHeaderLine("-------------                          -------------------                    --------------             ---------------------    --------          ---    ---");
	rep.AddHeaderLine(" ");

	groupList.RemoveAll();
	if (! DEBUG) {
		try {
			query.Format("select sg.dbslottinggroupid, sg.description, "
				"concat(c.name, concat(' ', concat('=', concat(' ', cr.description)))), sg.priority "
				"from dbprodgroupquery pq, dbslottinggroup sg, dbcriteria c, dbcriteriarange cr "
				"where sg.dbfacilityid = %d "
				"and sg.dbslottinggroupid = pq.dbslottinggroupid "
				"and c.dbcriteriaid = cr.dbcriteriaid "
				"and cr.dbcriteriarangeid = pq.dbcriteriarangeid "
				"and cr.description != 'Default' "
				"union "
				"select sg.dbslottinggroupid, sg.description, "
				"concat(c.name, concat(' ', concat(pq.operator, concat(' ', pq.value)))), sg.priority "
				"from dbprodgroupquery pq, dbslottinggroup sg, dbcriteria c, dbcriteriarange cr "
				"where sg.dbfacilityid = %d "
				"and sg.dbslottinggroupid = pq.dbslottinggroupid "
				"and c.dbcriteriaid = cr.dbcriteriaid "
				"and cr.dbcriteriarangeid = pq.dbcriteriarangeid "
				"and cr.description = 'Default' "
				"union "
				"select sg.dbslottinggroupid, sg.description, '', sg.priority "
				"from dbslottinggroup sg "
				"where not exists ( select dbslottinggroupid "
				"from dbprodgroupquery pq "
				"where pq.dbslottinggroupid = sg.dbslottinggroupid) "
				"and dbfacilityid = %d "
				"order by 4", currentFacilityID, currentFacilityID, currentFacilityID);
			rc = ExecuteQuery("GetProductGroupList", query, groupList);
			
		}
		catch (...) {
			rc = -1;
		}
	}
	if (rc < 0) {
		AfxMessageBox("Error retrieving product group list from database.");
		return rc;
	}

	rep.m_txtArray.Add("Product Group|Query|Product Description|WMS ID|WMS Detail ID|Movement|Balance on Hand|Unit of Issue|");
	rep.m_columnCount = 8;

	//create the report body
	prevProdGroup = -1;
	int i = 0;
	while (i < groupList.GetSize()) {

		rep.AddDataLine(groupList[i]);

		ParseString(groupList[i], "|", groupFields);

		prodGroup = atol(groupFields[0]);
		groupName = groupFields[1];
		
		groupFields[2].Replace("^", " ");

		if (prodGroup != prevProdGroup) {

			prevProdGroup = prodGroup;
			CStringArray productList;
			try {
				productList.RemoveAll();
				query.Format("select pp.description, pp.wmsproductid, pp.wmsproductdetailid, "
					"pp.movement, pp.balanceonhand, pp.unitofissue "
					"from dbproductpack pp, dbprodslotgroup psg "
					"where psg.dbslottinggroupid = %d "
					"and psg.dbproductpackid = pp.dbproductpackid ", prodGroup);
				switch (sortFlag) {
				case 0:
					// sort by product wms id
					query += " order by pp.wmsproductid, pp.wmsproductdetailid ";
					break;
				case 1:
					// sort by movement
					query += " order by pp.movement, pp.wmsproductid, pp.wmsproductdetailid  ";
					break;
				case 2:
					// sort by boh
					query += " order by pp.balanceonhand, pp.wmsproductid, pp.wmsproductdetailid  ";
					break;
				case 3:
					// sort by uoi
					query += " order by pp.unitofissue ";
					break;
				}
				rc = ExecuteQuery("GetProductByProductGroup", query, productList);
			}
			catch (...) {
				rc = -1;
			}
			if (rc < 0) {
				AfxMessageBox("Error retrieving products for product group.");
				return rc;
			}
			
			// skip a line
			rep.AddBodyLine("");
			
			// If there are no products we need to display the group name and the query text lines
			// otherwise they will be displayed with the products
			if (productList.GetSize() >= 1) {
				// add a line for the group name and the first product
				firstColumn = groupFields[1];	// the group description
				currentFormat = headerFormat;
				ParseString(productList[0], "|", productFields);
				line.Format(currentFormat, firstColumn, productFields[0], productFields[1], productFields[2], 
					atof(productFields[3]), atof(productFields[4]), ConvertUOI(atoi(productFields[5])));
				rep.AddBodyLine(line);

				line.Format(textFormat, firstColumn, "", productFields[0], productFields[1], productFields[2], 
					atof(productFields[3]), atof(productFields[4]), ConvertUOI(atoi(productFields[5])));
				rep.m_txtArray.Add(line);
			}
			else {		// there are no products; just display the group name
				firstColumn = groupFields[1];		// the group description
				currentFormat = "<B>%-35.35s</B>    No products in group";
				line.Format(currentFormat, firstColumn);
				rep.AddBodyLine(line);

				line.Format(textFormat, firstColumn, "", "","","","","","");
				rep.m_txtArray.Add(line);
			}

			if (productList.GetSize() >= 2) {
				// add a line for the first query text and the 2nd product
				firstColumn = groupFields[2];	// the query
				currentFormat = detailFormat;
				ParseString(productList[1], "|", productFields);
				line.Format(currentFormat, firstColumn, productFields[0], productFields[1], productFields[2], 
					atof(productFields[3]), atof(productFields[4]), ConvertUOI(atoi(productFields[5])));
				rep.AddBodyLine(line);
				i++;

				line.Format(textFormat, groupName, firstColumn, productFields[0], productFields[1], productFields[2], 
					atof(productFields[3]), atof(productFields[4]), ConvertUOI(atoi(productFields[5])));
				rep.m_txtArray.Add(line);
			}
			else { // there are not 2 products so the 2nd line is only the query
				firstColumn = groupFields[2];
				currentFormat = "%-33.33s";
				line.Format(currentFormat, firstColumn);
				rep.AddBodyLine(line);
				i++;
				if (firstColumn != "") {
					line.Format(textFormat, groupName, firstColumn, "","","","","","");
					rep.m_txtArray.Add(line);
				}
			}

			// Loop through the remaining products displaying the query text until we run out
			currentFormat = detailFormat;
			
			for (int j=2; j < productList.GetSize(); ++j) {
				ParseString(productList[j], "|", productFields);				
				
				// if we haven't run out of groups and we are still in the same group, display the query
				// next to the product 
				if (i < groupList.GetSize() && atol(groupList[i].Left(groupList[i].Find("|"))) == prodGroup) {
					ParseString(groupList[i], "|", groupFields);
					firstColumn = groupFields[2];		// the query
					i++;		 // increment group list counter
				}
				// we must have either run out of groups or switched to the next one
				else {
					firstColumn = "";
					currentFormat = detailFormat;
				}

				line.Format(currentFormat, firstColumn, productFields[0], productFields[1], productFields[2], 
					atof(productFields[3]), atof(productFields[4]), ConvertUOI(atoi(productFields[5])));
				rep.AddBodyLine(line);

				line.Format(textFormat, groupName, firstColumn, productFields[0], productFields[1], productFields[2], 
					atof(productFields[3]), atof(productFields[4]), ConvertUOI(atoi(productFields[5])));
				rep.m_txtArray.Add(line);
			}
		}
		// same product group as before, just display the query text
		else {
			firstColumn = groupFields[2];		// the query
			line.Format("  %-33.33s",firstColumn);
			rep.AddBodyLine(line);
			i++; 

			line.Format(textFormat, groupName, firstColumn, "","","","","","");
			rep.m_txtArray.Add(line);
		}

	}



	return 0;

}



int BuildProductGroupLayoutRep(CSUCCReport &rep)
{
	CString tmpStr;
	CString tmpStr2;
	CString tmpStr3;
//	CSsaStringArray resultList;
	CString dataType;

	CString prodGroup;
	CString numProds;
	CString numBays;
	CString numAlloc;
	CString numFaces;
	CString pctOpen;
	
	CString facility;
	int indx;


	//set general report data
	rep.m_nLinesPerPage = 60;  //69
	rep.m_nCharactersPerLine = 100;
	rep.m_paperSizeWidth = 8.5;
	rep.m_paperSizeLength = 11;
	rep.m_ReportName = "Product Group Layout";

	//call forte to get report data
	//Get list of bay profile names from the database
	try
	{
		resultList.RemoveAll();
		if (! DEBUG) 
			GetReportList("ProductGroupLayout", resultList);
		else {
			resultList.Add("F|testreports|");
			resultList.Add("D|PG1|1234567|1234567|1234567|1234567|100.00|");
		}
	}
	catch (...)
	{
		AfxMessageBox("Error Getting ProductGroupsLayout from Database.");
		return -1;
	}

	rep.m_txtArray.Add("Product Group|Number of Bays|Number of Products|Number of Facings|Apprx. Prods Allocated|Pct. Facings Open|");
	rep.m_columnCount = 7;

	//create the report body
	for (int i=0; i < resultList.GetSize(); i++)
	{
		tmpStr3 = "";
		tmpStr = resultList.GetAt(i);
		rep.AddDataLine(tmpStr);
		dataType = GetNext(tmpStr, "%s");

		if (dataType == "D")
		{

			rep.m_txtArray.Add(tmpStr);

			prodGroup = GetNext(tmpStr, "%-35.35s");
			numBays = GetNext(tmpStr, "%7d");
			numProds = GetNext(tmpStr, "%7d");
			numAlloc = GetNext(tmpStr, "%7d");
			numFaces = GetNext(tmpStr, "%7d");
			tmpStr2 = GetNext(tmpStr, "%3.2f");
			pctOpen.Format("%6s", tmpStr2);
			
			
			
			tmpStr3 = prodGroup;
			tmpStr3 += "     " + numBays;
			tmpStr3 += "    " + numProds;
			tmpStr3 += "     " + numFaces;
			tmpStr3 += "      " + numAlloc;
			tmpStr3 += "        " + pctOpen;

			/*
			AddStrAtColumn(tmpStr3, prodGroup, 0, rep.m_nCharactersPerLine, TRUE);
			AddStrAtColumn(tmpStr3, numBays, 40, rep.m_nCharactersPerLine, TRUE);
			AddStrAtColumn(tmpStr3, numProds, 51, rep.m_nCharactersPerLine, TRUE);
			AddStrAtColumn(tmpStr3, numFaces, 64, rep.m_nCharactersPerLine, TRUE);
			AddStrAtColumn(tmpStr3, numAlloc, 77, rep.m_nCharactersPerLine, TRUE);
			AddStrAtColumn(tmpStr3, pctOpen, 91, rep.m_nCharactersPerLine, TRUE);
			*/

			rep.AddBodyLine(tmpStr3);

		}
		else
		{
			if (dataType == "F")
			{
				indx = tmpStr.Find("|");
				facility = tmpStr.Mid(0, indx);
				tmpStr = tmpStr.Mid(indx+1);
				rep.m_FacilityName = facility;

                                 //          1         2         3         4         5         6         7         8         9         10        11        12        13
	                             //01234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456
				rep.AddHeaderLine("                                        Num Bays  Num Prods  Num Facings  Approx Prods  Approx Pct");
				rep.AddHeaderLine("Product Group                           Assigned  In Group   Assigned     Allocated     Facings Open");
 				rep.AddHeaderLine("-------------                           --------  ---------  -----------  -----------   ------------");
				rep.AddHeaderLine(" ");
			}
		}
	}



	return 0;

}

int BuildCapitalCostRejection(CSUCCReport &rep)
{
	CString tmpStr;
	CString tmpStr2;
	CString tmpStr3;
//	CSsaStringArray resultList;
	CString dataType;
	
	CString Product;
	CString WMSID;
	CString WMSDetailID;
	CString ProductLength;
	CString ProductWidth;
	CString ProductHeight;
	CString ProductTi;
	CString ProductHi;
	CString ContainerLength;
	CString ContainerWidth;
	CString ContainerHeight;
	CString HeightOverride;
	CString Type;
	CString Profile;
	CString LevelType, tmpLevelType;
	CString HandlingMethod, tmpHandlingMethod;
	CString MaxLength;
	CString MaxWidth;
	CString MaxHeight;
	CString Clearance;
	CString prevProduct;
	CString txt;

	int	iHandlingMethod, iLevelType;
	CString facility;
	CString line;
	int indx;


	//set general report data
	rep.m_nLinesPerPage = 60;  //69
	rep.m_nCharactersPerLine = 120;
	rep.m_paperSizeWidth = 8.5;
	rep.m_paperSizeLength = 11;
	rep.m_ReportName = "Capital Cost Rejection Report";

	//call forte to get report data
	//Get list of bay profile names from the database
	try {
		resultList.RemoveAll();
		if (! DEBUG)
			GetReportList("CapitalCostRejection",resultList);
		else {
			resultList.Add("F|testreports|");
			resultList.Add("D|case|4121651471-1|1|12.0|12.0|12.0|3|3|42.0|48.0|5.0|0|B|bin\\10InchHighOneLevel|1|1.|24.0|34.|9.|2.0|");
			resultList.Add("D|case|4121651471-1|1|12.0|12.0|12.0|3|3|42.0|48.0|5.0|0|B|pallet\\40InchPallet|5|3.|40.0|38.|36.|2.0|");
			resultList.Add("D|pallet|412165181-1|1|12.0|12.0|12.0|3|3|42.0|48.0|5.0|0|B|bin\\10InchHighOneLevel|1|1.|24.0|34.|9.|2.0|");
			resultList.Add("D|pallet|412165181-1|1|12.0|12.0|12.0|3|3|42.0|48.0|5.0|0|B|pallet\\40InchPallet|5|3.|40.0|38.|36.|2.0|");
		}
		
	}
	catch (...)
	{
		AfxMessageBox("Error Getting CapitalCostRejection from Database.");
		return -1;
	}

	rep.m_txtArray.Add("Product|WMS ID|WMS Detail ID|Length|Width|Height|Ti|Hi|"
				"Container Length|Container Width|Container Height|Container Height Override|"
				"Type|Bay Profile|Level Type|"
				"Handling Method|Max Length|Max Width|Max Height|Clearance|");
	rep.m_columnCount = 20;

	prevProduct = "";
	//create the report body
	for (int i=0; i < resultList.GetSize(); i++) {
		tmpStr3 = "";
		tmpStr = resultList.GetAt(i);
		rep.AddDataLine(tmpStr);
		indx   = tmpStr.Find("|");
		dataType = tmpStr.Mid(0,indx);
		tmpStr   = tmpStr.Mid(indx+1);
		
		
		if (dataType == "D") {

			Product = GetNext(tmpStr, "%-35s");
			WMSID = GetNext(tmpStr, "%-45.45s");
			WMSDetailID = GetNext(tmpStr, "%-45.45s");
			
			ProductLength = GetNext(tmpStr, "%-8.2f");
			ProductWidth  = GetNext(tmpStr, "%-8.2f");
			ProductHeight = GetNext(tmpStr, "%-8.2f");
			ProductTi	  = GetNext(tmpStr, "%-d");
			ProductHi	  = GetNext(tmpStr, "%-d");
			
			ContainerLength = GetNext(tmpStr, "%-8.2f");
			ContainerWidth  = GetNext(tmpStr, "%-8.2f");
			ContainerHeight = GetNext(tmpStr, "%-8.2f");
			HeightOverride  = GetNext(tmpStr, "%-3.2f");
			
			if (Product != prevProduct) {
				prevProduct = Product;

				rep.AddBodyLine(""); 
				line = "<B>Product: " + Product;
				line += "   WMS ID: " + WMSID;
				line += "   WMS Detail ID: " + WMSDetailID;
				line += "</B>";
				rep.AddBodyLine(line);
				
				line = "<B>Product Dimensions:   L: </B>" + ProductLength;
				line += " <B>W: </B>" + ProductWidth;
				line += " <B>H: </B>" + ProductHeight;
				line += " <B>Ti x Hi: </B>" + ProductTi;
				line += " x "+ ProductHi;
				rep.AddBodyLine(line);
				
				line = "<B>Container Dimensions: L: </B>" + ContainerLength;
				line += " <B>W: </B>" + ContainerWidth;
				line += " <B>H: </B>" + ContainerHeight;
				line += " <B>Height Override: </B>" + HeightOverride;
				rep.AddBodyLine(line);

				line = "<B>                                                          Level      Handling        Maximum Profile Dimensions</B>";
				rep.AddBodyLine(line);
				line = "<B>Type             Profile                                  Type       Method         Length      Width     Height  Clearance</B>";
				rep.AddBodyLine(line);
			}
			
			Type			= GetNext(tmpStr, "%s");
			if (Type == "B") 
				Type.Format("%-16s", "Best Available");
			else
				Type.Format("%-16s", "Ideal");
			
			Profile			= GetNext(tmpStr, "%-40.40s");
			
			iLevelType = atoi(GetNext(tmpStr, "%d"));
			tmpLevelType = ConvertBayType(iLevelType);
			LevelType.Format("%-10s", tmpLevelType);
			
			iHandlingMethod = atoi(GetNext(tmpStr, "%d"));
			if (iHandlingMethod == 3)
				tmpHandlingMethod = "Pallet";
			else
				tmpHandlingMethod = "Case";
			
			HandlingMethod.Format("%-10s", tmpHandlingMethod);
			
			MaxLength		= GetNext(tmpStr, "%10.2f");
			MaxWidth		= GetNext(tmpStr, "%10.2f");
			MaxHeight		= GetNext(tmpStr, "%10.2f");
			Clearance		= GetNext(tmpStr, "%10.2f");
			
			line = Type;
			line += " ";
			line += Profile;
			line += " ";
			line += LevelType;
			line += " ";
			line += HandlingMethod;
			line += " ";
			line += MaxLength;
			line += " ";
			line += MaxWidth;
			line += " ";
			line += MaxHeight;
			line += " ";
			line += Clearance;
			rep.AddBodyLine(line);
			
			txt.Format("%s|%s|%s|%s|%s|%s|%s|%s|%s|%s|%s|%s|%s|%s|%s|%s|%s|%s|%s|%s|",
				Product, WMSID, WMSDetailID, ProductLength, ProductWidth, ProductHeight, ProductTi, ProductHi,
				ContainerLength, ContainerWidth, ContainerHeight, HeightOverride, Type, Profile, LevelType,
				HandlingMethod, MaxLength, MaxWidth, MaxHeight, Clearance);
			rep.m_txtArray.Add(txt);

		}
		else {
			if (dataType == "F") {
				indx = tmpStr.Find("|");
				facility = tmpStr.Mid(0, indx);
				tmpStr = tmpStr.Mid(indx+1);
				rep.m_FacilityName = facility;
			}
		}
	}

	return 0;

}

int BuildUnassignedProduct(CSUCCReport &rep)
{
	CString tmpStr;
	CString tmpStr2;
	CString tmpStr3;
//	CSsaStringArray resultList;
	CString dataType;
	
	CString prodName;
	CString strReason;
	
	CString facility;
	int indx;


	//set general report data
	rep.m_nLinesPerPage = 60;  //69
	rep.m_nCharactersPerLine = 80;
	rep.m_paperSizeWidth = 8.5;
	rep.m_paperSizeLength = 11;
	rep.m_ReportName = "Unassigned Product";

	//call forte to get report data
	//Get list of bay profile names from the database
	try
	{
		resultList.RemoveAll();
		GetReportList("UnassignedProduct", resultList);
	}
	catch (...)
	{
		AfxMessageBox("Error Getting ProductGroupsLayout from Database.");
		return -1;
	}

	rep.m_txtArray.Add("Product|Reason|");
	rep.m_columnCount = 2;

	//create the report body
	for (int i=0; i < resultList.GetSize(); i++)
	{
		tmpStr3 = "";
		tmpStr = resultList.GetAt(i);
		rep.AddDataLine(tmpStr);
		indx   = tmpStr.Find("|");
		dataType = tmpStr.Mid(0,indx);
		tmpStr   = tmpStr.Mid(indx+1);

		if (dataType == "D")
		{
			rep.m_txtArray.Add(tmpStr);
			indx = tmpStr.Find("|");
			tmpStr2 = tmpStr.Mid(0, indx);
			prodName.Format("%-35s", tmpStr2);

			tmpStr = tmpStr.Mid(indx+1);
			indx = tmpStr.Find("|");
			tmpStr2 = tmpStr.Mid(0, indx);
			strReason.Format("%40s", tmpStr2);		

			AddStrAtColumn(tmpStr3, prodName, 0, rep.m_nCharactersPerLine, TRUE);
			AddStrAtColumn(tmpStr3, strReason, 40, rep.m_nCharactersPerLine, TRUE);

			rep.AddBodyLine(tmpStr3);
			
		}
		else
		{
			if (dataType == "F")
			{
				indx = tmpStr.Find("|");
				facility = tmpStr.Mid(0, indx);
				tmpStr = tmpStr.Mid(indx+1);
				rep.m_FacilityName = facility;

                                 //          1         2         3         4         5         6         7         8         9         10        11        12        13
	                             //01234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456
				rep.AddHeaderLine("Unassigned Product                      Reason Not Assigned");
				rep.AddHeaderLine("------------------                      ---------------------");
				rep.AddHeaderLine(" ");
			}
		}
	}



	return 0;

}

int BuildRackUsageSummary(CSUCCReport &rep)
{
	CString tmpStr;
	CString tmpStr2;
	CString tmpStr3;
//	CSsaStringArray resultList;
	CString dataType;

	CString RackName;
	CString VWRack;
	CString VWAvail;
	CString FxAvail;
	CString numBays;
	CString VWUsed;
	CString FxUsed;
	CString numBaysUsed;
	CString VWLeft;
	CString FxLeft;
	CString numOpenBay;

	CString facility;
	int indx;


	//set general report data
	rep.m_nLinesPerPage = 60;  //69
	rep.m_nCharactersPerLine = 115;
	rep.m_paperSizeWidth = 8.5;
	rep.m_paperSizeLength = 11;
	rep.m_ReportName = "Rack Usage Summary";

	//call forte to get report data
	//Get list of bay profile names from the database
	try
	{
		resultList.RemoveAll();
		GetReportList("RackUsageSummary", resultList);
	}
	catch (...)
	{
		AfxMessageBox("Error Getting ProductGroupsLayout from Database.");
		return -1;
	}

	rep.m_txtArray.Add("Bay Profile|Is Variable Width|Var. Width Available|Fixed Available|"
		"Number Bays Available|Var. Width Used|Fixed Used|Number Bays Used|"
		"Var. Width Remaining|Fixed Remaining|Number of Open Bays|");
	rep.m_columnCount = 11;

	//create the report body
	for (int i=0; i < resultList.GetSize(); i++)
	{
		tmpStr3 = "";
		tmpStr = resultList.GetAt(i);
		rep.AddDataLine(tmpStr);
		indx   = tmpStr.Find("|");
		dataType = tmpStr.Mid(0,indx);
		tmpStr   = tmpStr.Mid(indx+1);

		if (dataType == "D")
		{
			rep.m_txtArray.Add(tmpStr);

			indx = tmpStr.Find("|");
			tmpStr2 = tmpStr.Mid(0, indx);
			RackName.Format("%-35s", tmpStr2);

			tmpStr = tmpStr.Mid(indx+1);
			indx = tmpStr.Find("|");
			tmpStr2 = tmpStr.Mid(0, indx);
			VWRack.Format("%7s", tmpStr2);

			tmpStr = tmpStr.Mid(indx+1);
			indx = tmpStr.Find("|");
			tmpStr2 = tmpStr.Mid(0, indx);			
			VWAvail.Format("%7s", tmpStr2);

			tmpStr = tmpStr.Mid(indx+1);
			indx = tmpStr.Find("|");
			tmpStr2 = tmpStr.Mid(0, indx);
			FxAvail.Format("%7s", tmpStr2);

			tmpStr = tmpStr.Mid(indx+1);
			indx = tmpStr.Find("|");
			tmpStr2 = tmpStr.Mid(0, indx);
			numBays.Format("%7s", tmpStr2);

			tmpStr = tmpStr.Mid(indx+1);
			indx = tmpStr.Find("|");
			tmpStr2 = tmpStr.Mid(0, indx);
			VWUsed.Format("%7s",tmpStr2);

			tmpStr = tmpStr.Mid(indx+1);
			indx = tmpStr.Find("|");
			tmpStr2 = tmpStr.Mid(0, indx);
			FxUsed.Format("%7s", tmpStr2);

			tmpStr = tmpStr.Mid(indx+1);
			indx = tmpStr.Find("|");
			tmpStr2 = tmpStr.Mid(0, indx);			
			numBaysUsed.Format("%7s", tmpStr2);

			tmpStr = tmpStr.Mid(indx+1);
			indx = tmpStr.Find("|");
			tmpStr2 = tmpStr.Mid(0, indx);
			VWLeft.Format("%7s", tmpStr2);

			tmpStr = tmpStr.Mid(indx+1);
			indx = tmpStr.Find("|");
			tmpStr2 = tmpStr.Mid(0, indx);
			FxLeft.Format("%7s", tmpStr2);

			tmpStr = tmpStr.Mid(indx+1);
			indx = tmpStr.Find("|");
			tmpStr2 = tmpStr.Mid(0, indx);
			numOpenBay.Format("%7s",tmpStr2);
			

			AddStrAtColumn(tmpStr3, RackName, 0, rep.m_nCharactersPerLine, TRUE);
			AddStrAtColumn(tmpStr3, VWRack, 35, rep.m_nCharactersPerLine, TRUE);
			AddStrAtColumn(tmpStr3, VWAvail, 45, rep.m_nCharactersPerLine, TRUE);
			AddStrAtColumn(tmpStr3, FxAvail, 53, rep.m_nCharactersPerLine, TRUE);
			AddStrAtColumn(tmpStr3, numBays, 61, rep.m_nCharactersPerLine, TRUE);
			AddStrAtColumn(tmpStr3, VWUsed, 67, rep.m_nCharactersPerLine, TRUE);
			AddStrAtColumn(tmpStr3, FxUsed, 74, rep.m_nCharactersPerLine, TRUE);
			AddStrAtColumn(tmpStr3, numBaysUsed, 81, rep.m_nCharactersPerLine, TRUE);
			AddStrAtColumn(tmpStr3, VWLeft, 91, rep.m_nCharactersPerLine, TRUE);
			AddStrAtColumn(tmpStr3, FxLeft, 98, rep.m_nCharactersPerLine, TRUE);
			AddStrAtColumn(tmpStr3, numOpenBay, 105, rep.m_nCharactersPerLine, TRUE);

			rep.AddBodyLine(tmpStr3);

		}
		else
		{
			if (dataType == "F")
			{
				indx = tmpStr.Find("|");
				facility = tmpStr.Mid(0, indx);
				tmpStr = tmpStr.Mid(indx+1);
				rep.m_FacilityName = facility;

                                 //          1         2         3         4         5         6         7         8         9         10        11        12        13
	                             //01234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456
				rep.AddHeaderLine("Rack Name                           VW Rack? VWAvail FxAvail #Bays VWUsed FxUsed #BaysUsed VWLeft FxLeft #OpenBay ");
				rep.AddHeaderLine("-------------                       -------- ------- ------- ----- ------ ------ --------- ------ ------ --------");
				rep.AddHeaderLine(" ");
			}
		}
	}



	return 0;

}


int BuildProductGroupFacingsRep(CSUCCReport &rep)
{
	CString tmpStr;
	CString tmpStr2;
	CString tmpStr3;
//	CSsaStringArray resultList;
	CString dataType;
	CString prodGroup;
	CString numFacings;
	CString facility;
	int indx;


	//set general report data
	rep.m_nLinesPerPage = 60;  //69
	rep.m_nCharactersPerLine = 80;
	rep.m_paperSizeWidth = 8.5;
	rep.m_paperSizeLength = 11;
	rep.m_ReportName = "Product Group Facings";

	//call forte to get report data
	//Get list of bay profile names from the database
	try
	{
		resultList.RemoveAll();
		if (! DEBUG)
		GetReportList("ProductGroupFacings",resultList);
		else {
		resultList.Add("F|AnaFacility01|");
		resultList.Add("S|Product Group 1 xxxx|240|");
		resultList.Add("S|Product Group 2 xxxx|23|");
		resultList.Add("S|Product Group 3 xxxx|999|");
		}
	}
	catch (...)
	{
		AfxMessageBox("Error Getting ProductGroupFacings from Database.");
		return -1;
	}


	rep.m_txtArray.Add("Product Group|Number of Facings|");
	rep.m_columnCount = 2;

	//create the report body
	for (int i=0; i < resultList.GetSize(); i++)
	{
		tmpStr3 = "";
		tmpStr = resultList.GetAt(i);
		rep.AddDataLine(tmpStr);
		indx   = tmpStr.Find("|");
		dataType = tmpStr.Mid(0,indx);
		tmpStr   = tmpStr.Mid(indx+1);

		if (dataType == "S")
		{
			rep.m_txtArray.Add(tmpStr);

			indx = tmpStr.Find("|");
			tmpStr2 = tmpStr.Mid(0, indx);
			prodGroup.Format("%-35s", tmpStr2);
			tmpStr = tmpStr.Mid(indx+1);
			indx = tmpStr.Find("|");
			tmpStr2 = tmpStr.Mid(0, indx);
			numFacings.Format("%14s", tmpStr2);

			AddStrAtColumn(tmpStr3, prodGroup, 0, rep.m_nCharactersPerLine, TRUE);
			AddStrAtColumn(tmpStr3, numFacings, 40, rep.m_nCharactersPerLine, TRUE);

			rep.AddBodyLine(tmpStr3);

		}
		else
		{
			if (dataType == "F")
			{
				indx = tmpStr.Find("|");
				facility = tmpStr.Mid(0, indx);
				tmpStr = tmpStr.Mid(indx+1);
				rep.m_FacilityName = facility;

                                 //          1         2         3         4         5         6         7         8         9         10        11        12        13
	            //rep.AddHeaderLine("01234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456");
				rep.AddHeaderLine("Product Group                           Number of Facings");
				rep.AddHeaderLine("-------------                           -----------------");
				rep.AddHeaderLine(" ");
			}
		}
	}



	return 0;


}

int BuildProductLayoutRep(CSUCCReport &rep)
{
	CString tmpStr;
	CString tmpStr2;
	CString tmpStr3;
//	CSsaStringArray resultList;
	CString dataType;
	CString prodDesc;
	CString locDesc;
	CString caseCount;
	CString cost;
	CString facility;
	int indx;


	//set general report data
	rep.m_nLinesPerPage = 60;  //69
	rep.m_nCharactersPerLine = 85;
	rep.m_paperSizeWidth = 8.5;
	rep.m_paperSizeLength = 11;
	rep.m_ReportName = "Product Layout - Assignments";
	
	//call forte to get report data
	//Get list of bay profile names from the database
	try
	{
		resultList.RemoveAll();
		if (! DEBUG)
			GetReportList("ProductLayout",resultList);
		else {
		resultList.Add("F|AnaFacility01|");
		resultList.Add("D|Product Group 1 xxxx|Location descripti|111|120|");
		resultList.Add("D|Product Group 2 xxxx|Location descripti|2|10|");
		}
	}
	catch (...)
	{
		AfxMessageBox("Error Getting ProductsLayoutAssignments from Database.");
		return -1;
	}


	rep.m_txtArray.Add("Product Description|Location Description|Case Count|Cost|");
	rep.m_columnCount = 4;

	//create the report body
	for (int i=0; i < resultList.GetSize(); i++)
	{
		tmpStr3 = "";
		tmpStr = resultList.GetAt(i);
		rep.AddDataLine(tmpStr);
		indx   = tmpStr.Find("|");
		dataType = tmpStr.Mid(0,indx);
		tmpStr   = tmpStr.Mid(indx+1);

		if (dataType == "D")
		{
			rep.m_txtArray.Add(tmpStr);

			indx = tmpStr.Find("|");
			tmpStr2 = tmpStr.Mid(0, indx);
			prodDesc.Format("%-35.35s", tmpStr2);
			tmpStr = tmpStr.Mid(indx+1);
			indx = tmpStr.Find("|");
			tmpStr2 = tmpStr.Mid(0, indx);
			locDesc.Format("%-18.18s", tmpStr2);
			tmpStr = tmpStr.Mid(indx+1);
			indx = tmpStr.Find("|");
			tmpStr2 = tmpStr.Mid(0, indx);
			caseCount.Format("%10.10s", tmpStr2);
			tmpStr = tmpStr.Mid(indx+1);
			indx = tmpStr.Find("|");
			tmpStr2 = tmpStr.Mid(0, indx);
			cost.Format("%7.7s", tmpStr2);
			tmpStr = tmpStr.Mid(indx+1);

			AddStrAtColumn(tmpStr3, prodDesc, 0, rep.m_nCharactersPerLine, TRUE);
			AddStrAtColumn(tmpStr3, locDesc, 40, rep.m_nCharactersPerLine, TRUE);
			AddStrAtColumn(tmpStr3, caseCount, 62, rep.m_nCharactersPerLine, TRUE);
			AddStrAtColumn(tmpStr3, cost, 77, rep.m_nCharactersPerLine, TRUE);

			rep.AddBodyLine(tmpStr3);

		}
		else
		{
			if (dataType == "F")
			{
				indx = tmpStr.Find("|");
				facility = tmpStr.Mid(0, indx);
				tmpStr = tmpStr.Mid(indx+1);
				rep.m_FacilityName = facility;

								 //          1         2         3         4         5         6         7         8         9         10        11        12        13
	                             //01234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456
				rep.AddHeaderLine("Product                                 Location              Case Count        Cost");
				rep.AddHeaderLine("-------------------                     --------              ----------        ----");
				rep.AddHeaderLine(" ");
			}
		}
	}



	return 0;

}

int BuildProductLayoutSortByProduct(CSUCCReport &rep)
{
	CString tmpStr;
	CString tmpStr2;
	CString tmpStr3;
//	CSsaStringArray resultList;
	CString dataType;
	CString prodDesc;
	CString WMSID;
	CString WMSDetailID;
	CString Movement;
	CString BOH;
	CString locDesc;
	CString caseCount;
	CString cost;
	CString facility;
	int indx;


	//set general report data
	rep.m_nLinesPerPage = 60;  //69
	rep.m_nCharactersPerLine = 160;
	rep.m_paperSizeWidth = 8.5;
	rep.m_paperSizeLength = 11;
	rep.m_ReportName = "Product Layout - Assignments";
	
	//call forte to get report data
	//Get list of bay profile names from the database
	try
	{
		resultList.RemoveAll();
		if (! DEBUG)
			GetReportList("ProductLayoutSortByProduct",resultList);
		else {
		resultList.Add("F|AnaFacility01|");
		resultList.Add("D|Product Group 1 xxxx|Location descripti|111|120|");
		resultList.Add("D|Product Group 2 xxxx|Location descripti|2|10|");
		}
	}
	catch (...)
	{
		AfxMessageBox("Error Getting ProductsLayoutSortByProduct from Database.");
		return -1;
	}

	rep.m_txtArray.Add("Product Description|WMS ID|WMS Detail ID|Movement|Balance on Hand|Location Description|Case Count|Cost|");
	rep.m_columnCount = 8;

	//create the report body
	for (int i=0; i < resultList.GetSize(); i++)
	{
		tmpStr3 = "";
		tmpStr = resultList.GetAt(i);
		rep.AddDataLine(tmpStr);
		indx   = tmpStr.Find("|");
		dataType = tmpStr.Mid(0,indx);
		tmpStr   = tmpStr.Mid(indx+1);

		if (dataType == "D")
		{
			rep.m_txtArray.Add(tmpStr);

			// Product
			indx = tmpStr.Find("|");
			tmpStr2 = tmpStr.Mid(0, indx);
			prodDesc.Format("%-45.45s", tmpStr2);
			tmpStr = tmpStr.Mid(indx+1);

			// WMS Product ID
			indx = tmpStr.Find("|");
			tmpStr2 = tmpStr.Mid(0, indx);
			WMSID.Format("%-10.10s", tmpStr2);
			tmpStr = tmpStr.Mid(indx+1);

			// WMS Product Detail ID
			indx = tmpStr.Find("|");
			tmpStr2 = tmpStr.Mid(0, indx);
			WMSDetailID.Format("%-10.10s", tmpStr2);
			tmpStr = tmpStr.Mid(indx+1);

			// Movement
			indx = tmpStr.Find("|");
			tmpStr2 = tmpStr.Mid(0, indx);
			Movement.Format("%9.9s", tmpStr2);
			tmpStr = tmpStr.Mid(indx+1);

			// Balance On Hand
			indx = tmpStr.Find("|");
			tmpStr2 = tmpStr.Mid(0, indx);
			BOH.Format("%9.9s", tmpStr2);
			tmpStr = tmpStr.Mid(indx+1);

			// Location
			indx = tmpStr.Find("|");
			tmpStr2 = tmpStr.Mid(0, indx);
			locDesc.Format("%-18.18s", tmpStr2);
			tmpStr = tmpStr.Mid(indx+1);

			// Case Count
			indx = tmpStr.Find("|");
			tmpStr2 = tmpStr.Mid(0, indx);
			caseCount.Format("%10.10s", tmpStr2);
			tmpStr = tmpStr.Mid(indx+1);

			// Total Cost
			indx = tmpStr.Find("|");
			tmpStr2 = tmpStr.Mid(0, indx);
			cost.Format("%8.8s", tmpStr2);
			tmpStr = tmpStr.Mid(indx+1);

			AddStrAtColumn(tmpStr3, prodDesc,      0, rep.m_nCharactersPerLine, TRUE);
			AddStrAtColumn(tmpStr3, WMSID,        50, rep.m_nCharactersPerLine, TRUE);
			AddStrAtColumn(tmpStr3, WMSDetailID,  65, rep.m_nCharactersPerLine, TRUE);
			AddStrAtColumn(tmpStr3, Movement,     80, rep.m_nCharactersPerLine, TRUE);
			AddStrAtColumn(tmpStr3, BOH,          95, rep.m_nCharactersPerLine, TRUE);
			AddStrAtColumn(tmpStr3, locDesc,     110, rep.m_nCharactersPerLine, TRUE);
			AddStrAtColumn(tmpStr3, caseCount,   135, rep.m_nCharactersPerLine, TRUE);
			AddStrAtColumn(tmpStr3, cost,        150, rep.m_nCharactersPerLine, TRUE);

			rep.AddBodyLine(tmpStr3);

		}
		else
		{
			if (dataType == "F")
			{
				indx = tmpStr.Find("|");
				facility = tmpStr.Mid(0, indx);
				tmpStr = tmpStr.Mid(indx+1);
				rep.m_FacilityName = facility;

								 //          1         2         3         4         5         6         7         8         9         10        11        12        13         14        15 
	                             //0123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789
				rep.AddHeaderLine("Product                                           WMS ID         WMS Detail ID  Movement       BOH            Location                 Case Count  Cost         ");
				rep.AddHeaderLine("---------------------------------------------     ----------     -------------  -----------    ----------     ----------------         ----------  -----------  ");
				rep.AddHeaderLine(" ");
			}
		}
	}



	return 0;

}

int BuildProductLayoutSortByLocation(CSUCCReport &rep)
{
	CString tmpStr;
	CString tmpStr2;
	CString tmpStr3;
//	CSsaStringArray resultList;
	CString dataType;
	CString prodDesc;
	CString WMSID;
	CString WMSDetailID;
	CString Movement;
	CString BOH;
	CString locDesc;
	CString caseCount;
	CString cost;
	CString facility;
	int indx;


	//set general report data
	rep.m_nLinesPerPage = 60;  //69
	rep.m_nCharactersPerLine = 160;
	rep.m_paperSizeWidth = 8.5;
	rep.m_paperSizeLength = 11;
	rep.m_ReportName = "Product Layout - Assignments";
	
	//call forte to get report data
	//Get list of bay profile names from the database
	try
	{
		resultList.RemoveAll();
		if (! DEBUG)
			GetReportList("ProductLayoutSortByLocation",resultList);
		else {
		resultList.Add("F|AnaFacility01|");
		resultList.Add("D|Product Group 1 xxxx|Location descripti|111|120|");
		resultList.Add("D|Product Group 2 xxxx|Location descripti|2|10|");
		}
	}
	catch (...)
	{
		AfxMessageBox("Error Getting ProductsLayoutAssignments from Database.");
		return -1;
	}

	rep.m_txtArray.Add("Product Description|WMS ID|WMS Detail ID|Movement|Balance on Hand|Location Description|Case Count|Cost|");
	rep.m_columnCount = 8;

	//create the report body
	for (int i=0; i < resultList.GetSize(); i++)
	{
		tmpStr3 = "";
		tmpStr = resultList.GetAt(i);
		rep.AddDataLine(tmpStr);
		indx   = tmpStr.Find("|");
		dataType = tmpStr.Mid(0,indx);
		tmpStr   = tmpStr.Mid(indx+1);

		if (dataType == "D")
		{
			rep.m_txtArray.Add(tmpStr);

			// Product
			indx = tmpStr.Find("|");
			tmpStr2 = tmpStr.Mid(0, indx);
			prodDesc.Format("%-45.45s", tmpStr2);
			tmpStr = tmpStr.Mid(indx+1);

			// WMS Product ID
			indx = tmpStr.Find("|");
			tmpStr2 = tmpStr.Mid(0, indx);
			WMSID.Format("%-10s", tmpStr2);
			tmpStr = tmpStr.Mid(indx+1);

			// WMS Product Detail ID
			indx = tmpStr.Find("|");
			tmpStr2 = tmpStr.Mid(0, indx);
			WMSDetailID.Format("%-10.10s", tmpStr2);
			tmpStr = tmpStr.Mid(indx+1);

			// Movement
			indx = tmpStr.Find("|");
			tmpStr2 = tmpStr.Mid(0, indx);
			Movement.Format("%9.9s", tmpStr2);
			tmpStr = tmpStr.Mid(indx+1);

			// Balance On Hand
			indx = tmpStr.Find("|");
			tmpStr2 = tmpStr.Mid(0, indx);
			BOH.Format("%9.9s", tmpStr2);
			tmpStr = tmpStr.Mid(indx+1);

			// Location
			indx = tmpStr.Find("|");
			tmpStr2 = tmpStr.Mid(0, indx);
			locDesc.Format("%-18.18s", tmpStr2);
			tmpStr = tmpStr.Mid(indx+1);

			// Case Count
			indx = tmpStr.Find("|");
			tmpStr2 = tmpStr.Mid(0, indx);
			caseCount.Format("%10.10s", tmpStr2);
			tmpStr = tmpStr.Mid(indx+1);

			// Total Cost
			indx = tmpStr.Find("|");
			tmpStr2 = tmpStr.Mid(0, indx);
			cost.Format("%8.8s", tmpStr2);
			tmpStr = tmpStr.Mid(indx+1);

			AddStrAtColumn(tmpStr3, prodDesc,      0, rep.m_nCharactersPerLine, TRUE);
			AddStrAtColumn(tmpStr3, WMSID,        50, rep.m_nCharactersPerLine, TRUE);
			AddStrAtColumn(tmpStr3, WMSDetailID,  65, rep.m_nCharactersPerLine, TRUE);
			AddStrAtColumn(tmpStr3, Movement,     80, rep.m_nCharactersPerLine, TRUE);
			AddStrAtColumn(tmpStr3, BOH,          95, rep.m_nCharactersPerLine, TRUE);
			AddStrAtColumn(tmpStr3, locDesc,     110, rep.m_nCharactersPerLine, TRUE);
			AddStrAtColumn(tmpStr3, caseCount,   135, rep.m_nCharactersPerLine, TRUE);
			AddStrAtColumn(tmpStr3, cost,        150, rep.m_nCharactersPerLine, TRUE);

			rep.AddBodyLine(tmpStr3);

		}
		else
		{
			if (dataType == "F")
			{
				indx = tmpStr.Find("|");
				facility = tmpStr.Mid(0, indx);
				tmpStr = tmpStr.Mid(indx+1);
				rep.m_FacilityName = facility;

								 //          1         2         3         4         5         6         7         8         9         10        11        12        13         14        15 
	                             //0123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789
				rep.AddHeaderLine("Product                                           WMS ID         WMS Detail ID  Movement       BOH            Location                 Case Count  Cost         ");
				rep.AddHeaderLine("---------------------------------------------     ----------     -------------  -----------    ----------     ----------------         ----------  -----------  ");
				rep.AddHeaderLine(" ");
			}
		}
	}



	return 0;

}

int BuildProductsLayoutVarWidthLocRep(CSUCCReport &rep)
{
	CString tmpStr;
	CString tmpStr2;
	CString tmpStr3;
//	CSsaStringArray resultList;
	CString dataType;
	CString locDesc;
	CString width;
	CString height;
	CString length;
	CString facility;
	int indx;


	//set general report data
	rep.m_nLinesPerPage = 60;  //69
	rep.m_nCharactersPerLine = 80;
	rep.m_paperSizeWidth = 8.5;
	rep.m_paperSizeLength = 11;
	rep.m_ReportName = "Variable Width Locations";

	//call forte to get report data
	//Get list of bay profile names from the database
	try
	{
		resultList.RemoveAll();
		if (! DEBUG)
		GetReportList("ProductsLayoutVarWidthLocations",resultList);
		else {
		resultList.Add("F|AnaFacility01|");
		resultList.Add("D|Product Location 1 xxxx|111|120|123|");
		resultList.Add("D|Product Location 2 xxxx|2|10|45|");
		}
	}
	catch (...)
	{
		AfxMessageBox("Error Getting ProductsLayoutVarWidthLocations from Database.");
		return -1;
	}

	rep.m_txtArray.Add("Location|Width|Height|Length|");
	rep.m_columnCount = 4;


	//create the report body
	for (int i=0; i < resultList.GetSize(); i++)
	{
		tmpStr3 = "";
		tmpStr = resultList.GetAt(i);
		rep.AddDataLine(tmpStr);
		indx   = tmpStr.Find("|");
		dataType = tmpStr.Mid(0,indx);
		tmpStr   = tmpStr.Mid(indx+1);

		if (dataType == "D")
		{
			rep.m_txtArray.Add(tmpStr);

			indx = tmpStr.Find("|");
			tmpStr2 = tmpStr.Mid(0, indx);
			locDesc.Format("%-18.18s", tmpStr2);
			tmpStr = tmpStr.Mid(indx+1);
			indx = tmpStr.Find("|");
			tmpStr2 = tmpStr.Mid(0, indx);
			width.Format("%7.7s", tmpStr2);
			tmpStr = tmpStr.Mid(indx+1);
			indx = tmpStr.Find("|");
			tmpStr2 = tmpStr.Mid(0, indx);
			height.Format("%7.7s", tmpStr2);
			tmpStr = tmpStr.Mid(indx+1);
			indx = tmpStr.Find("|");
			tmpStr2 = tmpStr.Mid(0, indx);
			length.Format("%7.7s", tmpStr2);
			tmpStr = tmpStr.Mid(indx+1);

			AddStrAtColumn(tmpStr3, locDesc, 0, rep.m_nCharactersPerLine, TRUE);
			AddStrAtColumn(tmpStr3, width, 33, rep.m_nCharactersPerLine, TRUE);
			AddStrAtColumn(tmpStr3, height, 44, rep.m_nCharactersPerLine, TRUE);
			AddStrAtColumn(tmpStr3, length, 55, rep.m_nCharactersPerLine, TRUE);

			rep.AddBodyLine(tmpStr3);

		}
		else
		{
			if (dataType == "F")
			{
				indx = tmpStr.Find("|");
				facility = tmpStr.Mid(0, indx);
				tmpStr = tmpStr.Mid(indx+1);
				rep.m_FacilityName = facility;

	                             //          1         2         3         4         5         6         7         8         9         10        11        12        13
	                             //01234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456
				rep.AddHeaderLine("Location                           Width     Height     Length");
				rep.AddHeaderLine("--------                           -----     ------     ------");
				rep.AddHeaderLine(" "); 
			}
		}
	}



	return 0;

}


int BuildProductsLayoutCaseReOrientRep(CSUCCReport &rep)
{
	CString tmpStr;
	CString tmpStr2;
	CString line;
//	CSsaStringArray resultList;
	CString dataType;
	CString prodDesc;
	CString locDesc;
	CString width;
	CString height;
	CString length;
	CString facility;
	double fHeight;
	double fWidth;
	double fLength;
	int indx;


	//set general report data
	rep.m_nLinesPerPage = 60;  //69
	rep.m_nCharactersPerLine = 141;
	rep.m_paperSizeWidth = 8.5;
	rep.m_paperSizeLength = 11;
	rep.m_ReportName = "Product Layout - Case Re-Orientation";

	//call forte to get report data
	//Get list of bay profile names from the database
	try
	{
		resultList.RemoveAll();
		GetReportList("ProductsLayoutCaseReOrientation",resultList);
	}
	catch (...)
	{
		AfxMessageBox("Error Getting ProductsLayoutCaseReorientation from Database.");
		return -1;
	}


	rep.m_txtArray.Add("Product Description|WMS ID|WMS Detail ID|Location|Height|Width|Length|");
	rep.m_columnCount = 7;


	//create the report body
	for (int i=0; i < resultList.GetSize(); i++)
	{
		//tmpStr3 = "";
		tmpStr = resultList.GetAt(i);
		rep.AddDataLine(tmpStr);
		//indx   = tmpStr.Find("|");
		dataType = GetNext(tmpStr, "%s");
		//tmpStr   = tmpStr.Mid(indx+1);

		if (dataType == "D")
		{
			rep.m_txtArray.Add(tmpStr);

			line =  GetNext(tmpStr, "%-30.30s");
			line += "     ";
			line += GetNext(tmpStr, "%-20.20s");
			line += "     ";
			line += GetNext(tmpStr, "%-20.20s");
			line += "     ";
			line += GetNext(tmpStr, "%-20.20s");
			line += "     ";
			height = GetNext(tmpStr, "%9.2f");
			fHeight = atof(height);
			if (fHeight < 0) {
				fHeight = 0 - fHeight;
				height.Format("%9.2f ", fHeight);
			}
			else
				height.Format("<B>%9.2f*</B>", fHeight);
			
			line += height;
			line += "  ";

			width = GetNext(tmpStr, "%9.2f");
			fWidth = atof(width);
			if (fWidth < 0) {
				fWidth = 0 - fWidth;
				width.Format("%9.2f ", fWidth);
			}
			else
				width.Format("<B>%9.2f*</B>", fWidth);
			
			line += width;

			line += " ";
			length = GetNext(tmpStr, "%9.2f");
			fLength = atof(length);
			if (fLength < 0) {
				fLength = 0 - fLength;
				length.Format("%9.2f ", fLength);
			}
			else
				length.Format("<B>%9.2f*</B>", fLength);


			line += length;


			rep.AddBodyLine(line);

		}
		else
		{
			if (dataType == "F")
			{
				indx = tmpStr.Find("|");
				facility = tmpStr.Mid(0, indx);
				tmpStr = tmpStr.Mid(indx+1);
				rep.m_FacilityName = facility;

	                             //          1         2         3         4         5         6         7         8         9         10        11        12        13        14
	                             //0123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901
				rep.AddHeaderLine("Product                            WMSProductID              WMSProductDetailID       Location                 Height     Width      Length   ");
				rep.AddHeaderLine("------------------------------     ---------------------     --------------------     --------------------     ---------  ---------  ---------");
				rep.AddHeaderLine(" ");                   
			}
		}
	}



	return 0;

}

int BuildFacilityMoveChainsRep(CSUCCReport &rep)
{
	CString tmpStr;
	CString tmpStr2;
	CString tmpStr3;
//	CSsaStringArray resultList;
	CString dataType;
	CString chainId;
	CString prodDesc;
	CString WMSID;
	CString WMSDetailID;
	CString savings;
	CString fromLoc;
	CString toLoc;
	CString total;
	float   chainTotal;
	float   grandTotal;
	int moveType;
	double f;
	CString facility;
	int indx;
	CString prevChainId;


	//set general report data
	rep.m_nLinesPerPage = 60;  //69
	rep.m_nCharactersPerLine = 170;
	rep.m_paperSizeWidth = 11;
	rep.m_paperSizeLength = 8.5;
	rep.m_ReportName = "Product Moves";

	//call forte to get report data
	//Get list of bay profile names from the database
	try
	{
		resultList.RemoveAll();
		if (! DEBUG)
		GetReportList("FacilityMoveChains",resultList);
		else {
			resultList.Add("F|15-P1|");
			resultList.Add("D|1|-32767|66040-CAMEL REGULAR KINGS|Y13933|SCCD-TMP|Y13933|");
			resultList.Add("D|1|-0.056|66023-BASIC ULTRA LGT KING|Y10123|Y13933|Y13933|");
			resultList.Add("D|1|-32767|66040-CAMEL REGULAR KINGS|SCCD-TMP|Y10123|Y10123|");
			resultList.Add("D|2|-32767|66044-CARLTON 100'S FILTER|Y13931|SCCD-TMP|Y13931|");
			resultList.Add("D|2|-0.057|66058-CAMEL ULTRA LITE 100 BOX|Y10121|Y13931|Y13931|");
			resultList.Add("D|2|-32767|66044-CARLTON 100'S FILTER|SCCD-TMP|Y10121|Y10121|");
			resultList.Add("D|3|-32767|66036-CAMEL LIGHT KINGS BOX|Y13813|SCCD-TMP|Y13813|");
			resultList.Add("D|3|-32767|66036-CAMEL LIGHT KINGS BOX|SCCD-TMP|Y13922|Y13922|");
			resultList.Add("D|4|-32767|66017-BARCLAY 100'S|Y13921|SCCD-TMP|Y13921|");
			resultList.Add("D|4|-32767|66017-BARCLAY 100'S|SCCD-TMP|Y10113|Y10113|");
			resultList.Add("D|5|-0.058|66062-CAMBRIDGE LOWEST KINGS  2780|Y13932|Y13823|Y13823|");
			resultList.Add("D|6|-0.056|66074-CAMBRIDGE MTHL LT KG 1430|Y13930|Y13821|Y13821|");
			resultList.Add("D|7|-32767|66018-BARCLAY BOX|Y13920|SCCD-TMP|Y13920|");
			resultList.Add("D|7|-0.055|66039-CAMEL LIGHT KINGS|Y13811|Y13920|Y13920|");
			resultList.Add("D|7|-32767|66018-BARCLAY BOX|SCCD-TMP|Y13811|Y13811|");
			resultList.Add("D|8|-0.054|66026-BEN HED FILTER 100         400|Y13923|Y10111|Y10111|");
		}
	}
	catch (...)
	{
		AfxMessageBox("Error Getting FacilityMoveChains from Database.");
		return -1;
	}

	rep.m_txtArray.Add("Chain Id|Savings|Product Description|WMS ID|WMS Detail ID|From Location|To Location|");
	rep.m_columnCount = 7;

	//create the report body
	prevChainId = "";
	chainTotal = 0;
	grandTotal = 0;
	for (int i=0; i < resultList.GetSize(); i++)
	{
		tmpStr3 = "";
		tmpStr = resultList.GetAt(i);
		rep.AddDataLine(tmpStr);
		indx   = tmpStr.Find("|");
		dataType = tmpStr.Mid(0,indx);
		tmpStr   = tmpStr.Mid(indx+1);

		if (dataType == "D")
		{
			indx = tmpStr.Find("|");
			tmpStr2 = tmpStr.Mid(0, indx);
			chainId.Format("%8s", tmpStr2);
			tmpStr = tmpStr.Mid(indx+1);			// chain id

			indx = tmpStr.Find("|");
			tmpStr = tmpStr.Mid(indx+1);			// chain sequence - skip

			indx = tmpStr.Find("|");
			tmpStr2 = tmpStr.Mid(0, indx);
			f = atof(tmpStr2);
			if ( f == SLOT_NIL_INTEGER )
				savings.Format("%9.9s", "0");
			else
				savings.Format("%9.9s", tmpStr2);
			tmpStr = tmpStr.Mid(indx+1);			// reslot savings
			
			indx = tmpStr.Find("|");                 // Product Description
			tmpStr2 = tmpStr.Mid(0, indx);
			prodDesc.Format("%-35.35s", tmpStr2);    
			tmpStr = tmpStr.Mid(indx+1);
			
			indx = tmpStr.Find("|");                 // WMS Product ID
			tmpStr2 = tmpStr.Mid(0, indx);
			WMSID.Format("%-20.20s", tmpStr2);    
			tmpStr = tmpStr.Mid(indx+1);
			
			indx = tmpStr.Find("|");                 // WMS Product Detail ID
			tmpStr2 = tmpStr.Mid(0, indx);
			WMSDetailID.Format("%-20.20s", tmpStr2);    
			tmpStr = tmpStr.Mid(indx+1);

			
			indx = tmpStr.Find("|");                 // Move Type
			tmpStr2 = tmpStr.Mid(0, indx);
			moveType = atoi(tmpStr);   
			tmpStr = tmpStr.Mid(indx+1);

			indx = tmpStr.Find("|");
			tmpStr2 = tmpStr.Mid(0, indx);
			fromLoc.Format("%-18.18s", tmpStr2);
			tmpStr = tmpStr.Mid(indx+1);

			indx = tmpStr.Find("|");
			tmpStr2 = tmpStr.Mid(0, indx);
			toLoc.Format("%-18.18s", tmpStr2);
			tmpStr = tmpStr.Mid(indx+1);

			switch (moveType) {
			case 0:
				break;
			case 1:
				toLoc = "SCCD-TMP";
				break;
			case 2:
				fromLoc = "SCCD-TMP";
				break;
			case 3:
				fromLoc = "SCCD-ADD";
				break;
			case 4:
				toLoc = "SCCD-DEL";
				break;
			default:
				break;
			}

			if (prevChainId != chainId)
			{
				if (prevChainId != "")
				{
					rep.AddBodyLine(" ");
					total.Format("TOTAL: %14.3f", chainTotal);
					AddStrAtColumn(tmpStr3, total, 2, rep.m_nCharactersPerLine, TRUE);
					rep.AddBodyLine(tmpStr3);
					rep.AddBodyLine(" ");
					rep.AddBodyLine(" ");
					grandTotal += chainTotal;
				}
				AddStrAtColumn(tmpStr3, chainId, 0, rep.m_nCharactersPerLine, TRUE);
				prevChainId = chainId;
				chainTotal = 0;
			}
			AddStrAtColumn(tmpStr3, savings,      14, rep.m_nCharactersPerLine, TRUE);
			AddStrAtColumn(tmpStr3, prodDesc,     30, rep.m_nCharactersPerLine, TRUE);
			AddStrAtColumn(tmpStr3, WMSID,        70, rep.m_nCharactersPerLine, TRUE);
			AddStrAtColumn(tmpStr3, WMSDetailID,  95, rep.m_nCharactersPerLine, TRUE);
			AddStrAtColumn(tmpStr3, fromLoc,     121, rep.m_nCharactersPerLine, TRUE);
			AddStrAtColumn(tmpStr3, toLoc,       146, rep.m_nCharactersPerLine, TRUE);

			chainTotal += (float)atof(savings.GetBuffer(0));	

			rep.AddBodyLine(tmpStr3);
			CString tmp;
			tmp.Format("%s|%s|%s|%s|%s|%s|%s|", 
				chainId, savings, prodDesc, WMSID, WMSDetailID, fromLoc, toLoc);
			rep.m_txtArray.Add(tmp);
		}
		else
		{
			if (dataType == "F")
			{
				indx = tmpStr.Find("|");
				facility = tmpStr.Mid(0, indx);
				tmpStr = tmpStr.Mid(indx+1);
				rep.m_FacilityName = facility;

				//create header information
				
                                 //          1         2         3         4         5         6         7         8         9         10        11        12        13        14        15
	                             //0123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456
				rep.AddHeaderLine("Chain Id        Savings       Product Description                     WMS Product ID           WMS Product Detail ID     From Location            To Location");
				rep.AddHeaderLine("--------        -------       -------------------                     --------------           ---------------------     -------------            -----------");
				rep.AddHeaderLine(" ");

			}
		}
	}

	if (prevChainId != "")
	{
		rep.AddBodyLine(" ");
		tmpStr3.Empty();
		total.Format("TOTAL: %14.3f", chainTotal);
		AddStrAtColumn(tmpStr3, total, 2, rep.m_nCharactersPerLine, TRUE);
		rep.AddBodyLine(tmpStr3);
		rep.AddBodyLine(" ");
		rep.AddBodyLine(" ");
		grandTotal += chainTotal;
	}

	if (resultList.GetSize() > 1) {
	rep.AddBodyLine(" ");
	rep.AddBodyLine(" ");
	rep.AddBodyLine(" ");
	total.Format("GRAND TOTAL: %14.3f", grandTotal);
	tmpStr3.Empty();
	AddStrAtColumn(tmpStr3, total, 0, rep.m_nCharactersPerLine, TRUE);
	rep.AddBodyLine(tmpStr3);
	rep.AddBodyLine(" ");
	rep.AddBodyLine(" ");
	}


	return 0;

}


int BuildLocationOutboundRep(CSUCCReport &rep)
{

	CString tmpStr;
	CString tmpStr2;
	CString tmpStr3;
	CString dataType;
	CString line;
//	CSsaStringArray resultList;
	CString facility, backfillLoc, stockerLoc;
	int indx;

	//set general report data
	
	rep.m_nLinesPerPage = 66;
	rep.m_nCharactersPerLine = 168;
	rep.m_paperSizeWidth = 16;
	rep.m_paperSizeLength = 8.5;

	rep.m_ReportName = "Location Outbound";

	try
	{
		resultList.RemoveAll();
		if (! DEBUG)
			GetReportList("LocationOutbound",resultList);
		else {
		resultList.Add("F|brd2|");
		resultList.Add("D|01010242|00042|00028|00072|0000|0000|0001|0068|0048|0040|02|P|R|RK|N|H|        |00000|00000|00000|        |00000|00000|00000|00|100|");
		resultList.Add("D|01010244|00042|00076|00072|0000|0000|0001|0068|0048|0040|02|P|R|RK|N|H|        |00000|00000|00000|        |00000|00000|00000|00|100|");
		resultList.Add("D|01010262|00042|00028|00144|0000|0000|0001|0068|0048|0040|03|P|R|RK|N|H|        |00000|00000|00000|        |00000|00000|00000|00|100|");
		resultList.Add("D|01010264|00042|00076|00144|0000|0000|0001|0068|0048|0040|03|P|R|RK|N|H|        |00000|00000|00000|        |00000|00000|00000|00|100|");
		resultList.Add("D|01010282|00042|00028|00216|0000|0000|0001|0068|0048|0040|04|P|R|RK|N|H|        |00000|00000|00000|        |00000|00000|00000|00|100|");
		resultList.Add("D|01010284|00042|00076|00216|0000|0000|0001|0068|0048|0040|04|P|R|RK|N|H|        |00000|00000|00000|        |00000|00000|00000|00|100|");
		resultList.Add("D|01010222|00042|00028|00000|0001|0068|0000|0000|0048|0040|01|P|S|RK|N|H|        |00000|00000|00000|        |00000|00000|00000|00|100|");
		resultList.Add("D|01010224|00042|00076|00000|0001|0068|0000|0000|0048|0040|01|P|S|RK|N|H|        |00000|00000|00000|        |00000|00000|00000|00|100|");
		resultList.Add("D|01010422|00042|00128|00000|0001|0068|0000|0000|0048|0040|01|P|S|RK|N|H|        |00000|00000|00000|        |00000|00000|00000|00|100|");
		resultList.Add("D|01010424|00042|00176|00000|0001|0068|0000|0000|0048|0040|01|P|S|RK|N|H|        |00000|00000|00000|        |00000|00000|00000|00|100|");
		resultList.Add("D|01010442|00042|00128|00072|0000|0000|0001|0068|0048|0040|02|P|R|RK|N|H|        |00000|00000|00000|        |00000|00000|00000|00|100|");
		resultList.Add("D|01010444|00042|00176|00072|0000|0000|0001|0068|0048|0040|02|P|R|RK|N|H|        |00000|00000|00000|        |00000|00000|00000|00|100|");
		resultList.Add("D|01010462|00042|00128|00144|0000|0000|0001|0068|0048|0040|03|P|R|RK|N|H|        |00000|00000|00000|        |00000|00000|00000|00|100|");
		resultList.Add("D|01010464|00042|00176|00144|0000|0000|0001|0068|0048|0040|03|P|R|RK|N|H|        |00000|00000|00000|        |00000|00000|00000|00|100|");
		resultList.Add("D|01010482|00042|00128|00216|0000|0000|0001|0068|0048|0040|04|P|R|RK|N|H|        |00000|00000|00000|        |00000|00000|00000|00|100|");
		resultList.Add("D|01010484|00042|00176|00216|0000|0000|0001|0068|0048|0040|04|P|R|RK|N|H|        |00000|00000|00000|        |00000|00000|00000|00|100|");
		resultList.Add("D|01010622|00042|00228|00000|0001|0068|0000|0000|0048|0040|01|P|S|RK|N|H|        |00000|00000|00000|        |00000|00000|00000|00|100|");
		resultList.Add("D|01010624|00042|00276|00000|0001|0068|0000|0000|0048|0040|01|P|S|RK|N|H|        |00000|00000|00000|        |00000|00000|00000|00|100|");
		resultList.Add("D|01010642|00042|00228|00072|0000|0000|0001|0068|0048|0040|02|P|R|RK|N|H|        |00000|00000|00000|        |00000|00000|00000|00|100|");
		resultList.Add("D|01010644|00042|00276|00072|0000|0000|0001|0068|0048|0040|02|P|R|RK|N|H|        |00000|00000|00000|        |00000|00000|00000|00|100|");
		resultList.Add("D|01010664|00042|00276|00144|0000|0000|0001|0068|0048|0040|03|P|R|RK|N|H|        |00000|00000|00000|        |00000|00000|00000|00|100|");
		resultList.Add("D|01010662|00042|00228|00144|0000|0000|0001|0068|0048|0040|03|P|R|RK|N|H|        |00000|00000|00000|        |00000|00000|00000|00|100|");
		resultList.Add("D|01010682|00042|00228|00216|0000|0000|0001|0068|0048|0040|04|P|R|RK|N|H|        |00000|00000|00000|        |00000|00000|00000|00|100|");
		resultList.Add("D|01010684|00042|00276|00216|0000|0000|0001|0068|0048|0040|04|P|R|RK|N|H|        |00000|00000|00000|        |00000|00000|00000|00|100|");
		resultList.Add("D|01010822|00042|00328|00000|0001|0068|0000|0000|0048|0040|01|P|S|RK|N|H|        |00000|00000|00000|        |00000|00000|00000|00|100|");
		resultList.Add("D|01010824|00042|00376|00000|0001|0068|0000|0000|0048|0040|01|P|S|RK|N|H|        |00000|00000|00000|        |00000|00000|00000|00|100|");
		resultList.Add("D|01010842|00042|00328|00072|0000|0000|0001|0068|0048|0040|02|P|R|RK|N|H|        |00000|00000|00000|        |00000|00000|00000|00|100|");
		resultList.Add("D|01010844|00042|00376|00072|0000|0000|0001|0068|0048|0040|02|P|R|RK|N|H|        |00000|00000|00000|        |00000|00000|00000|00|100|");
		resultList.Add("D|01010862|00042|00328|00144|0000|0000|0001|0068|0048|0040|03|P|R|RK|N|H|        |00000|00000|00000|        |00000|00000|00000|00|100|");
		resultList.Add("D|01010864|00042|00376|00144|0000|0000|0001|0068|0048|0040|03|P|R|RK|N|H|        |00000|00000|00000|        |00000|00000|00000|00|100|");
		resultList.Add("D|01010882|00042|00328|00216|0000|0000|0001|0068|0048|0040|04|P|R|RK|N|H|        |00000|00000|00000|        |00000|00000|00000|00|100|");
		resultList.Add("D|01010884|00042|00376|00216|0000|0000|0001|0068|0048|0040|04|P|R|RK|N|H|        |00000|00000|00000|        |00000|00000|00000|00|100|");
		resultList.Add("D|01010311|00192|00028|00000|0001|0068|0000|0000|0048|0040|01|P|S|RK|N|H|        |00000|00000|00000|        |00000|00000|00000|00|100|");
		resultList.Add("D|01010313|00192|00076|00000|0001|0068|0000|0000|0048|0040|01|P|S|RK|N|H|        |00000|00000|00000|        |00000|00000|00000|00|100|");
		resultList.Add("D|01010331|00192|00028|00072|0000|0000|0001|0068|0048|0040|02|P|R|RK|N|H|        |00000|00000|00000|        |00000|00000|00000|00|100|");
		resultList.Add("D|01010333|00192|00076|00072|0000|0000|0001|0068|0048|0040|02|P|R|RK|N|H|        |00000|00000|00000|        |00000|00000|00000|00|100|");
		resultList.Add("D|01010351|00192|00028|00144|0000|0000|0001|0068|0048|0040|03|P|R|RK|N|H|        |00000|00000|00000|        |00000|00000|00000|00|100|");
		resultList.Add("D|01010353|00192|00076|00144|0000|0000|0001|0068|0048|0040|03|P|R|RK|N|H|        |00000|00000|00000|        |00000|00000|00000|00|100|");
		resultList.Add("D|01010371|00192|00028|00216|0000|0000|0001|0068|0048|0040|04|P|R|RK|N|H|        |00000|00000|00000|        |00000|00000|00000|00|100|");
		resultList.Add("D|01010373|00192|00076|00216|0000|0000|0001|0068|0048|0040|04|P|R|RK|N|H|        |00000|00000|00000|        |00000|00000|00000|00|100|");
		resultList.Add("D|01010511|00192|00128|00000|0001|0068|0000|0000|0048|0040|01|P|S|RK|N|H|        |00000|00000|00000|        |00000|00000|00000|00|100|");
		resultList.Add("D|01010513|00192|00176|00000|0001|0068|0000|0000|0048|0040|01|P|S|RK|N|H|        |00000|00000|00000|        |00000|00000|00000|00|100|");
		resultList.Add("D|01010533|00192|00176|00072|0000|0000|0001|0068|0048|0040|02|P|R|RK|N|H|        |00000|00000|00000|        |00000|00000|00000|00|100|");
		resultList.Add("D|01010531|00192|00128|00072|0000|0000|0001|0068|0048|0040|02|P|R|RK|N|H|        |00000|00000|00000|        |00000|00000|00000|00|100|");
		resultList.Add("D|01010551|00192|00128|00144|0000|0000|0001|0068|0048|0040|03|P|R|RK|N|H|        |00000|00000|00000|        |00000|00000|00000|00|100|");
		resultList.Add("D|01010553|00192|00176|00144|0000|0000|0001|0068|0048|0040|03|P|R|RK|N|H|        |00000|00000|00000|        |00000|00000|00000|00|100|");
		resultList.Add("D|01010571|00192|00128|00216|0000|0000|0001|0068|0048|0040|04|P|R|RK|N|H|        |00000|00000|00000|        |00000|00000|00000|00|100|");
		resultList.Add("D|01010573|00192|00176|00216|0000|0000|0001|0068|0048|0040|04|P|R|RK|N|H|        |00000|00000|00000|        |00000|00000|00000|00|100|");
		resultList.Add("D|01010771|00192|00228|00216|0000|0000|0001|0068|0048|0040|04|P|R|RK|N|H|        |00000|00000|00000|        |00000|00000|00000|00|100|");
		resultList.Add("D|01010773|00192|00276|00216|0000|0000|0001|0068|0048|0040|04|P|R|RK|N|H|        |00000|00000|00000|        |00000|00000|00000|00|100|");
		resultList.Add("D|01010711|00192|00228|00000|0001|0068|0000|0000|0048|0040|01|P|S|RK|N|H|        |00000|00000|00000|        |00000|00000|00000|00|100|");
		resultList.Add("D|01010713|00192|00276|00000|0001|0068|0000|0000|0048|0040|01|P|S|RK|N|H|        |00000|00000|00000|        |00000|00000|00000|00|100|");
		resultList.Add("D|01010731|00192|00228|00072|0000|0000|0001|0068|0048|0040|02|P|R|RK|N|H|        |00000|00000|00000|        |00000|00000|00000|00|100|");
		resultList.Add("D|01010733|00192|00276|00072|0000|0000|0001|0068|0048|0040|02|P|R|RK|N|H|        |00000|00000|00000|        |00000|00000|00000|00|100|");
		resultList.Add("D|01010751|00192|00228|00144|0000|0000|0001|0068|0048|0040|03|P|R|RK|N|H|        |00000|00000|00000|        |00000|00000|00000|00|100|");
		resultList.Add("D|01010753|00192|00276|00144|0000|0000|0001|0068|0048|0040|03|P|R|RK|N|H|        |00000|00000|00000|        |00000|00000|00000|00|100|");
		resultList.Add("D|01010911|00192|00328|00000|0001|0068|0000|0000|0048|0040|01|P|S|RK|N|H|        |00000|00000|00000|        |00000|00000|00000|00|100|");
		resultList.Add("D|01010913|00192|00376|00000|0001|0068|0000|0000|0048|0040|01|P|S|RK|N|H|        |00000|00000|00000|        |00000|00000|00000|00|100|");
		resultList.Add("D|01010931|00192|00328|00072|0000|0000|0001|0068|0048|0040|02|P|R|RK|N|H|        |00000|00000|00000|        |00000|00000|00000|00|100|");
		resultList.Add("D|01010933|00192|00376|00072|0000|0000|0001|0068|0048|0040|02|P|R|RK|N|H|        |00000|00000|00000|        |00000|00000|00000|00|100|");
		resultList.Add("D|01010951|00192|00328|00144|0000|0000|0001|0068|0048|0040|03|P|R|RK|N|H|        |00000|00000|00000|        |00000|00000|00000|00|100|");
		resultList.Add("D|01010953|00192|00376|00144|0000|0000|0001|0068|0048|0040|03|P|R|RK|N|H|        |00000|00000|00000|        |00000|00000|00000|00|100|");
		resultList.Add("D|01010971|00192|00328|00216|0000|0000|0001|0068|0048|0040|04|P|R|RK|N|H|        |00000|00000|00000|        |00000|00000|00000|00|100|");
		resultList.Add("D|01010973|00192|00376|00216|0000|0000|0001|0068|0048|0040|04|P|R|RK|N|H|        |00000|00000|00000|        |00000|00000|00000|00|100|");
		
		resultList.Add("D|01010284|00042|00076|00216|0000|0000|0001|0068|0048|0040|04|P|R|RK|N|H|        |00000|00000|00000|        |00000|00000|00000|00|100|");
		resultList.Add("D|01010222|00042|00028|00000|0001|0068|0000|0000|0048|0040|01|P|S|RK|N|H|        |00000|00000|00000|        |00000|00000|00000|00|100|");
		resultList.Add("D|01010224|00042|00076|00000|0001|0068|0000|0000|0048|0040|01|P|S|RK|N|H|        |00000|00000|00000|        |00000|00000|00000|00|100|");
		resultList.Add("D|01010422|00042|00128|00000|0001|0068|0000|0000|0048|0040|01|P|S|RK|N|H|        |00000|00000|00000|        |00000|00000|00000|00|100|");
		resultList.Add("D|01010424|00042|00176|00000|0001|0068|0000|0000|0048|0040|01|P|S|RK|N|H|        |00000|00000|00000|        |00000|00000|00000|00|100|");
		resultList.Add("D|01010442|00042|00128|00072|0000|0000|0001|0068|0048|0040|02|P|R|RK|N|H|        |00000|00000|00000|        |00000|00000|00000|00|100|");
		resultList.Add("D|01010444|00042|00176|00072|0000|0000|0001|0068|0048|0040|02|P|R|RK|N|H|        |00000|00000|00000|        |00000|00000|00000|00|100|");
		resultList.Add("D|01010462|00042|00128|00144|0000|0000|0001|0068|0048|0040|03|P|R|RK|N|H|        |00000|00000|00000|        |00000|00000|00000|00|100|");
		resultList.Add("D|01010464|00042|00176|00144|0000|0000|0001|0068|0048|0040|03|P|R|RK|N|H|        |00000|00000|00000|        |00000|00000|00000|00|100|");
		resultList.Add("D|01010482|00042|00128|00216|0000|0000|0001|0068|0048|0040|04|P|R|RK|N|H|        |00000|00000|00000|        |00000|00000|00000|00|100|");
		resultList.Add("D|01010484|00042|00176|00216|0000|0000|0001|0068|0048|0040|04|P|R|RK|N|H|        |00000|00000|00000|        |00000|00000|00000|00|100|");
		resultList.Add("D|01010622|00042|00228|00000|0001|0068|0000|0000|0048|0040|01|P|S|RK|N|H|        |00000|00000|00000|        |00000|00000|00000|00|100|");
		resultList.Add("D|01010624|00042|00276|00000|0001|0068|0000|0000|0048|0040|01|P|S|RK|N|H|        |00000|00000|00000|        |00000|00000|00000|00|100|");
		resultList.Add("D|01010642|00042|00228|00072|0000|0000|0001|0068|0048|0040|02|P|R|RK|N|H|        |00000|00000|00000|        |00000|00000|00000|00|100|");
		resultList.Add("D|01010644|00042|00276|00072|0000|0000|0001|0068|0048|0040|02|P|R|RK|N|H|        |00000|00000|00000|        |00000|00000|00000|00|100|");
		resultList.Add("D|01010664|00042|00276|00144|0000|0000|0001|0068|0048|0040|03|P|R|RK|N|H|        |00000|00000|00000|        |00000|00000|00000|00|100|");
		resultList.Add("D|01010662|00042|00228|00144|0000|0000|0001|0068|0048|0040|03|P|R|RK|N|H|        |00000|00000|00000|        |00000|00000|00000|00|100|");
		resultList.Add("D|01010682|00042|00228|00216|0000|0000|0001|0068|0048|0040|04|P|R|RK|N|H|        |00000|00000|00000|        |00000|00000|00000|00|100|");
		resultList.Add("D|01010684|00042|00276|00216|0000|0000|0001|0068|0048|0040|04|P|R|RK|N|H|        |00000|00000|00000|        |00000|00000|00000|00|100|");
		resultList.Add("D|01010822|00042|00328|00000|0001|0068|0000|0000|0048|0040|01|P|S|RK|N|H|        |00000|00000|00000|        |00000|00000|00000|00|100|");
		resultList.Add("D|01010824|00042|00376|00000|0001|0068|0000|0000|0048|0040|01|P|S|RK|N|H|        |00000|00000|00000|        |00000|00000|00000|00|100|");
		resultList.Add("D|01010842|00042|00328|00072|0000|0000|0001|0068|0048|0040|02|P|R|RK|N|H|        |00000|00000|00000|        |00000|00000|00000|00|100|");
		resultList.Add("D|01010844|00042|00376|00072|0000|0000|0001|0068|0048|0040|02|P|R|RK|N|H|        |00000|00000|00000|        |00000|00000|00000|00|100|");
		resultList.Add("D|01010862|00042|00328|00144|0000|0000|0001|0068|0048|0040|03|P|R|RK|N|H|        |00000|00000|00000|        |00000|00000|00000|00|100|");
		resultList.Add("D|01010864|00042|00376|00144|0000|0000|0001|0068|0048|0040|03|P|R|RK|N|H|        |00000|00000|00000|        |00000|00000|00000|00|100|");
		resultList.Add("D|01010882|00042|00328|00216|0000|0000|0001|0068|0048|0040|04|P|R|RK|N|H|        |00000|00000|00000|        |00000|00000|00000|00|100|");
		resultList.Add("D|01010884|00042|00376|00216|0000|0000|0001|0068|0048|0040|04|P|R|RK|N|H|        |00000|00000|00000|        |00000|00000|00000|00|100|");
		resultList.Add("D|01010311|00192|00028|00000|0001|0068|0000|0000|0048|0040|01|P|S|RK|N|H|        |00000|00000|00000|        |00000|00000|00000|00|100|");
		resultList.Add("D|01010313|00192|00076|00000|0001|0068|0000|0000|0048|0040|01|P|S|RK|N|H|        |00000|00000|00000|        |00000|00000|00000|00|100|");
		resultList.Add("D|01010331|00192|00028|00072|0000|0000|0001|0068|0048|0040|02|P|R|RK|N|H|        |00000|00000|00000|        |00000|00000|00000|00|100|");
		resultList.Add("D|01010333|00192|00076|00072|0000|0000|0001|0068|0048|0040|02|P|R|RK|N|H|        |00000|00000|00000|        |00000|00000|00000|00|100|");
		resultList.Add("D|01010351|00192|00028|00144|0000|0000|0001|0068|0048|0040|03|P|R|RK|N|H|        |00000|00000|00000|        |00000|00000|00000|00|100|");
		resultList.Add("D|01010353|00192|00076|00144|0000|0000|0001|0068|0048|0040|03|P|R|RK|N|H|        |00000|00000|00000|        |00000|00000|00000|00|100|");
		resultList.Add("D|01010371|00192|00028|00216|0000|0000|0001|0068|0048|0040|04|P|R|RK|N|H|        |00000|00000|00000|        |00000|00000|00000|00|100|");
		resultList.Add("D|01010373|00192|00076|00216|0000|0000|0001|0068|0048|0040|04|P|R|RK|N|H|        |00000|00000|00000|        |00000|00000|00000|00|100|");
		resultList.Add("D|01010511|00192|00128|00000|0001|0068|0000|0000|0048|0040|01|P|S|RK|N|H|        |00000|00000|00000|        |00000|00000|00000|00|100|");
		resultList.Add("D|01010513|00192|00176|00000|0001|0068|0000|0000|0048|0040|01|P|S|RK|N|H|        |00000|00000|00000|        |00000|00000|00000|00|100|");
		resultList.Add("D|01010533|00192|00176|00072|0000|0000|0001|0068|0048|0040|02|P|R|RK|N|H|        |00000|00000|00000|        |00000|00000|00000|00|100|");
		resultList.Add("D|01010531|00192|00128|00072|0000|0000|0001|0068|0048|0040|02|P|R|RK|N|H|        |00000|00000|00000|        |00000|00000|00000|00|100|");
		resultList.Add("D|01010551|00192|00128|00144|0000|0000|0001|0068|0048|0040|03|P|R|RK|N|H|        |00000|00000|00000|        |00000|00000|00000|00|100|");
		resultList.Add("D|01010553|00192|00176|00144|0000|0000|0001|0068|0048|0040|03|P|R|RK|N|H|        |00000|00000|00000|        |00000|00000|00000|00|100|");
		resultList.Add("D|01010571|00192|00128|00216|0000|0000|0001|0068|0048|0040|04|P|R|RK|N|H|        |00000|00000|00000|        |00000|00000|00000|00|100|");
		resultList.Add("D|01010573|00192|00176|00216|0000|0000|0001|0068|0048|0040|04|P|R|RK|N|H|        |00000|00000|00000|        |00000|00000|00000|00|100|");
		resultList.Add("D|01010771|00192|00228|00216|0000|0000|0001|0068|0048|0040|04|P|R|RK|N|H|        |00000|00000|00000|        |00000|00000|00000|00|100|");
		resultList.Add("D|01010773|00192|00276|00216|0000|0000|0001|0068|0048|0040|04|P|R|RK|N|H|        |00000|00000|00000|        |00000|00000|00000|00|100|");
		resultList.Add("D|01010711|00192|00228|00000|0001|0068|0000|0000|0048|0040|01|P|S|RK|N|H|        |00000|00000|00000|        |00000|00000|00000|00|100|");
		resultList.Add("D|01010713|00192|00276|00000|0001|0068|0000|0000|0048|0040|01|P|S|RK|N|H|        |00000|00000|00000|        |00000|00000|00000|00|100|");
		resultList.Add("D|01010731|00192|00228|00072|0000|0000|0001|0068|0048|0040|02|P|R|RK|N|H|        |00000|00000|00000|        |00000|00000|00000|00|100|");
		resultList.Add("D|01010733|00192|00276|00072|0000|0000|0001|0068|0048|0040|02|P|R|RK|N|H|        |00000|00000|00000|        |00000|00000|00000|00|100|");
		resultList.Add("D|01010751|00192|00228|00144|0000|0000|0001|0068|0048|0040|03|P|R|RK|N|H|        |00000|00000|00000|        |00000|00000|00000|00|100|");
		resultList.Add("D|01010753|00192|00276|00144|0000|0000|0001|0068|0048|0040|03|P|R|RK|N|H|        |00000|00000|00000|        |00000|00000|00000|00|100|");
		resultList.Add("D|01010911|00192|00328|00000|0001|0068|0000|0000|0048|0040|01|P|S|RK|N|H|        |00000|00000|00000|        |00000|00000|00000|00|100|");
		resultList.Add("D|01010913|00192|00376|00000|0001|0068|0000|0000|0048|0040|01|P|S|RK|N|H|        |00000|00000|00000|        |00000|00000|00000|00|100|");
		resultList.Add("D|01010931|00192|00328|00072|0000|0000|0001|0068|0048|0040|02|P|R|RK|N|H|        |00000|00000|00000|        |00000|00000|00000|00|100|");
		resultList.Add("D|01010933|00192|00376|00072|0000|0000|0001|0068|0048|0040|02|P|R|RK|N|H|        |00000|00000|00000|        |00000|00000|00000|00|100|");
		resultList.Add("D|01010951|00192|00328|00144|0000|0000|0001|0068|0048|0040|03|P|R|RK|N|H|        |00000|00000|00000|        |00000|00000|00000|00|100|");
		resultList.Add("D|01010953|00192|00376|00144|0000|0000|0001|0068|0048|0040|03|P|R|RK|N|H|        |00000|00000|00000|        |00000|00000|00000|00|100|");
		resultList.Add("D|********|00192|00328|00216|0000|0000|0001|0068|0048|0040|04|P|R|RK|N|H|        |00000|00000|00000|        |00000|00000|00000|00|100|");
		}
	 }
	catch (...)
	{
		AfxMessageBox("Error Getting location outbound data from Database.");
		return -1;
	}

	rep.m_txtArray.Add("Location|X-Coordinate|Y-Coordinate|Z-Coordinate|Sel Stack Pos|Sel Pos Hgt|"
		"Rsv Stack Pos|Rsv Pos Hgt|Stack Pos Depth|Stack Pos Width|Level|Handling Method|Category|"
		"Description|Comingle Flag|Comingle Dimension|Backfill Location|Backfill X-Coord|Backfill Y-Coord|"
		"Backfill Z-Coord|Stocker Location|Stocker X-Coord|Stocker Y-Coord|Stocker Z-Coord|Stack Limit|Loc Usage Pct|");

	rep.m_columnCount = 26;

	//create the report body

	for (int i=0; i < resultList.GetSize(); i++)
	{
		line = "";
		tmpStr = resultList.GetAt(i);
		rep.AddDataLine(tmpStr);
		indx   = tmpStr.Find("|");
		dataType = tmpStr.Mid(0,indx);
		tmpStr   = tmpStr.Mid(indx+1);

		if (dataType == "D")
		{

			rep.m_txtArray.Add(tmpStr);

			indx = tmpStr.Find("|");
			tmpStr2 = tmpStr.Mid(0, indx);
			tmpStr3.Format("%-18.18s", tmpStr2);		// Location
			AddStrAtColumn(line, tmpStr3, 0, rep.m_nCharactersPerLine, TRUE);
			tmpStr = tmpStr.Mid(indx+1);

			indx = tmpStr.Find("|");
			tmpStr2 = tmpStr.Mid(0,indx);
			tmpStr3.Format("%5d", atoi(tmpStr2));			// X-Coord
			AddStrAtColumn(line, tmpStr3, 17, rep.m_nCharactersPerLine, TRUE);
			tmpStr = tmpStr.Mid(indx+1);
			
			indx = tmpStr.Find("|");
			tmpStr2 = tmpStr.Mid(0,indx);
			tmpStr3.Format("%5d", atoi(tmpStr2));			// Y-Coord
			AddStrAtColumn(line, tmpStr3, 23, rep.m_nCharactersPerLine, TRUE);
			tmpStr = tmpStr.Mid(indx+1);

			indx = tmpStr.Find("|");
			tmpStr2 = tmpStr.Mid(0,indx);
			tmpStr3.Format("%5d", atoi(tmpStr2));			// Z-Coord
			AddStrAtColumn(line, tmpStr3, 29, rep.m_nCharactersPerLine, TRUE);
			tmpStr = tmpStr.Mid(indx+1);
			
			indx = tmpStr.Find("|");
			tmpStr2 = tmpStr.Mid(0,indx);
			tmpStr3.Format("%4d", atoi(tmpStr2));			// Sel-Stk-Pos
			AddStrAtColumn(line, tmpStr3, 35, rep.m_nCharactersPerLine, TRUE);
			tmpStr = tmpStr.Mid(indx+1);
			
			indx = tmpStr.Find("|");
			tmpStr2 = tmpStr.Mid(0,indx);
			tmpStr3.Format("%4d", atoi(tmpStr2));			// Sel-Pos-Height
			AddStrAtColumn(line, tmpStr3, 40, rep.m_nCharactersPerLine, TRUE);
			tmpStr = tmpStr.Mid(indx+1);

			indx = tmpStr.Find("|");
			tmpStr2 = tmpStr.Mid(0,indx);
			tmpStr3.Format("%4d", atoi(tmpStr2));			// Rsv-Stk-Pos
			AddStrAtColumn(line, tmpStr3, 45, rep.m_nCharactersPerLine, TRUE);
			tmpStr = tmpStr.Mid(indx+1);

			indx = tmpStr.Find("|");
			tmpStr2 = tmpStr.Mid(0,indx);
			tmpStr3.Format("%4d", atoi(tmpStr2));			// Rsv-Pos-Height
			AddStrAtColumn(line, tmpStr3, 50, rep.m_nCharactersPerLine, TRUE);
			tmpStr = tmpStr.Mid(indx+1);

			indx = tmpStr.Find("|");
			tmpStr2 = tmpStr.Mid(0,indx);
			tmpStr3.Format("%4d", atoi(tmpStr2));			// Stk-Pos-Depth
			AddStrAtColumn(line, tmpStr3, 55, rep.m_nCharactersPerLine, TRUE);
			tmpStr = tmpStr.Mid(indx+1);

			indx = tmpStr.Find("|");
			tmpStr2 = tmpStr.Mid(0,indx);
			tmpStr3.Format("%4d", atoi(tmpStr2));			// Stk-Pos-Width
			AddStrAtColumn(line, tmpStr3, 60, rep.m_nCharactersPerLine, TRUE);
			tmpStr = tmpStr.Mid(indx+1);

			indx = tmpStr.Find("|");
			tmpStr2 = tmpStr.Mid(0,indx);
			tmpStr3.Format("%3.3s", tmpStr2);			// Level
			AddStrAtColumn(line, tmpStr3, 65, rep.m_nCharactersPerLine, TRUE);
			tmpStr = tmpStr.Mid(indx+1);

			indx = tmpStr.Find("|");
			tmpStr2 = tmpStr.Mid(0,indx);
			tmpStr3.Format("%-2.2s", tmpStr2);		// Handling Method
			AddStrAtColumn(line, tmpStr3, 70, rep.m_nCharactersPerLine, TRUE);
			tmpStr = tmpStr.Mid(indx+1);

			indx = tmpStr.Find("|");
			tmpStr2 = tmpStr.Mid(0,indx);
			tmpStr3.Format("%-3.3s", tmpStr2);		// Category
			AddStrAtColumn(line, tmpStr3, 73, rep.m_nCharactersPerLine, TRUE);
			tmpStr = tmpStr.Mid(indx+1);

			indx = tmpStr.Find("|");
			tmpStr2 = tmpStr.Mid(0,indx);
			tmpStr3.Format("%-4.4s", tmpStr2);		// Description
			AddStrAtColumn(line, tmpStr3, 77, rep.m_nCharactersPerLine, TRUE);
			tmpStr = tmpStr.Mid(indx+1);

			indx = tmpStr.Find("|");
			tmpStr2 = tmpStr.Mid(0,indx);
			tmpStr3.Format("%-2.2s", tmpStr2);		// Comingle Flag
			AddStrAtColumn(line, tmpStr3, 82, rep.m_nCharactersPerLine, TRUE);
			tmpStr = tmpStr.Mid(indx+1);

			indx = tmpStr.Find("|");
			tmpStr2 = tmpStr.Mid(0,indx);
			tmpStr3.Format("%-2.2s", tmpStr2);		// Comingle Dimension
			AddStrAtColumn(line, tmpStr3, 85, rep.m_nCharactersPerLine, TRUE);
			tmpStr = tmpStr.Mid(indx+1);

			indx = tmpStr.Find("|");
			tmpStr2 = tmpStr.Mid(0,indx);
			tmpStr3.Format("%-18.18s", tmpStr2);		// Backfill Location
			AddStrAtColumn(line, tmpStr3, 87, rep.m_nCharactersPerLine, TRUE);
			tmpStr = tmpStr.Mid(indx+1);
			backfillLoc = tmpStr2;
			backfillLoc.TrimRight();

			indx = tmpStr.Find("|");
			tmpStr2 = tmpStr.Mid(0,indx);
			tmpStr3.Format("%5.5s", tmpStr2);			// Backfill X-Coord
			if (backfillLoc.GetLength() > 0)
				AddStrAtColumn(line, tmpStr3, 106, rep.m_nCharactersPerLine, TRUE);
			tmpStr = tmpStr.Mid(indx+1);

			indx = tmpStr.Find("|");
			tmpStr2 = tmpStr.Mid(0,indx);
			tmpStr3.Format("%5.5s", tmpStr2);			// Backfill Y-Coord
			if (backfillLoc.GetLength() > 0)
				AddStrAtColumn(line, tmpStr3, 112, rep.m_nCharactersPerLine, TRUE);
			tmpStr = tmpStr.Mid(indx+1);

			indx = tmpStr.Find("|");
			tmpStr2 = tmpStr.Mid(0,indx);	
			tmpStr3.Format("%5.5s", tmpStr2);			// Backfill Z-Coord
			if (backfillLoc.GetLength() > 0)
				AddStrAtColumn(line, tmpStr3, 118, rep.m_nCharactersPerLine, TRUE);
			tmpStr = tmpStr.Mid(indx+1);
			
			indx = tmpStr.Find("|");
			tmpStr2 = tmpStr.Mid(0,indx);
			tmpStr3.Format("%-18.18s", tmpStr2);		// Stocker Location
			AddStrAtColumn(line, tmpStr3, 124, rep.m_nCharactersPerLine, TRUE);
			tmpStr = tmpStr.Mid(indx+1);
			stockerLoc = tmpStr2;
			stockerLoc.TrimRight();

			indx = tmpStr.Find("|");
			tmpStr2 = tmpStr.Mid(0,indx);
			tmpStr3.Format("%5.5s", tmpStr2);			// Stock X-Coord
			if (stockerLoc.GetLength() > 0)
				AddStrAtColumn(line, tmpStr3, 143, rep.m_nCharactersPerLine, TRUE);
			tmpStr = tmpStr.Mid(indx+1);

			indx = tmpStr.Find("|");
			tmpStr2 = tmpStr.Mid(0,indx);
			tmpStr3.Format("%5.5s", tmpStr2);			// Stocker Y-Coord
			if (stockerLoc.GetLength() > 0)
				AddStrAtColumn(line, tmpStr3, 149, rep.m_nCharactersPerLine, TRUE);
			tmpStr = tmpStr.Mid(indx+1);

			indx = tmpStr.Find("|");
			tmpStr2 = tmpStr.Mid(0,indx);
			tmpStr3.Format("%5.5s", tmpStr2);			// Stocker Z-Coord
			if (stockerLoc.GetLength() > 0)
				AddStrAtColumn(line, tmpStr3, 155, rep.m_nCharactersPerLine, TRUE);
			tmpStr = tmpStr.Mid(indx+1);

			indx = tmpStr.Find("|");
			tmpStr2 = tmpStr.Mid(0,indx);
			tmpStr3.Format("%3d", atoi(tmpStr2));			// Stack Limit
			AddStrAtColumn(line, tmpStr3, 161, rep.m_nCharactersPerLine, TRUE);
			tmpStr = tmpStr.Mid(indx+1);

			indx = tmpStr.Find("|");
			tmpStr2 = tmpStr.Mid(0,indx);
			tmpStr3.Format("%3d", atoi(tmpStr2));			// Location Usage Percent
			AddStrAtColumn(line, tmpStr3, 165, rep.m_nCharactersPerLine, TRUE);
			tmpStr = tmpStr.Mid(indx+1);

			rep.AddBodyLine(line);

		}
		else
		{
			if (dataType == "F")
			{
				indx = tmpStr.Find("|");
				facility = tmpStr.Mid(0, indx);
				tmpStr = tmpStr.Mid(indx+1);
				
				rep.m_FacilityName = facility;
                                 //          1         2         3         4         5         6         7         8         9         10        11        12        13        14        15        16
	                             //01234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456780
				rep.AddHeaderLine("                                    Sel  Sel  Rsv  Rsv Stk   Stk                                               Backfill     Stocker                 Stocker          Loc");
				rep.AddHeaderLine("                   X     Y     Z    Stk  Pos  Stk  Pos Pos   Pos                       Backfill             X     Y     Z   Staging              X     Y     Z   Stk Usg");
				rep.AddHeaderLine("Location         Coord Coord Coord  Pos  Hgt  Pos  Hgt Dpth  Wid Lvl HM Cat Desc CF CD Location           Coord Coord Coord Location           Coord Coord Coord Lmt Pct");
				rep.AddHeaderLine("--------         ----- ----- -----  ---  ---  ---  --- ----  --- --- -- --- ---- -- -- --------           ----- ----- ----- --------           ----- ----- ----- --- ---");
				rep.AddHeaderLine("");
 		      //resultList.Add("D|   01010973xxxxxxxx|00192|00376|00216|0000|0000|0001|0068|0048|0040|004|xP|xxR|xxRK|xN|xH|xxxxxxxxxxxxxxxxxx|00000|00000|00000|xxxxxxxxxxxxxxxxxx|00000|00000|00000|x00|100|");
			}
		}
	}


	return 0;

}


int BuildAssignmentOutboundRep(CSUCCReport &rep)
{

	CString tmpStr;
	CString tmpStr2;
	CString tmpStr3;
	CString dataType;
	CString line;
//	CSsaStringArray resultList;
	CString facility;
	int indx;
	CString txt;

	//set general report data
	
	rep.m_nLinesPerPage = 60;
	rep.m_nCharactersPerLine = 85;
	rep.m_paperSizeWidth = 8.5;
	rep.m_paperSizeLength = 11;

	rep.m_ReportName = "Product Assignment Outbound";

	try
	{
		resultList.RemoveAll();
		if (! DEBUG)
			GetReportList("ProdAssignOutbound",resultList);
		else {
			resultList.Add("F|1test2|");
			resultList.Add("D|000000000000066605|00001|Y12210  |Y12210  |");
			resultList.Add("D|000000000000066612|00001|Y12311  |Y12311  |");
			resultList.Add("D|000000000000066612|00001|Y12313  |Y12313  |");
			resultList.Add("D|000000000000066619|00001|Y12120  |Y12120  |");
			resultList.Add("D|000000000000066619|00001|Y12122  |Y12122  |");
			resultList.Add("D|000000000000066629|00001|Y12020  |Y12020  |");
			resultList.Add("D|000000000000066629|00001|Y12022  |Y12022  |");
			resultList.Add("D|000000000000066642|00001|Y12221  |Y12221  |");
			resultList.Add("D|000000000000066657|00001|Y12121  |Y12121  |");
			resultList.Add("D|000000000000066657|00001|Y12123  |Y12123  |");
			resultList.Add("D|000000000000066667|00001|Y12012  |Y12012  |");
			resultList.Add("D|000000000000066672|00001|Y12112  |Y12112  |");
			resultList.Add("D|000000000000066672|00001|Y12110  |Y12110  |");
			resultList.Add("D|000000000000066733|00001|Y12010  |Y12010  |");
			resultList.Add("D|000000000000066769|00001|Y12211  |Y12211  |");
			resultList.Add("D|000000000000066769|00001|Y12213  |Y12213  |");
			resultList.Add("D|000000000000066782|00001|Y12111  |Y12111  |");
			resultList.Add("D|000000000000066782|00001|Y12113  |Y12113  |");
			resultList.Add("D|000000000000066285|00001|Y13720  |Y13720  |");
			resultList.Add("D|000000000000066295|00001|Y13512  |Y13512  |");
			resultList.Add("D|000000000000066295|00001|Y13510  |Y13510  |");
			resultList.Add("D|000000000000066310|00001|Y13722  |Y13722  |");
			resultList.Add("D|000000000000066368|00001|Y13820  |Y13820  |");
			resultList.Add("D|000000000000066369|00001|Y13612  |Y13612  |");
		}
	  }
	catch (...)
	{
		AfxMessageBox("Error Getting location outbound data from Database.");
		return -1;
	}


	rep.m_txtArray.Add("Product Description|WMS ID|Location|Search Anchor Point|");
	rep.m_columnCount = 4;

	//create the report body

	for (int i=0; i < resultList.GetSize(); i++)
	{
		line = "";
		tmpStr = resultList.GetAt(i);
		rep.AddDataLine(tmpStr);
		indx   = tmpStr.Find("|");
		dataType = tmpStr.Mid(0,indx);
		tmpStr   = tmpStr.Mid(indx+1);


		if (dataType == "D")
		{

			indx = tmpStr.Find("|");
			tmpStr2 = tmpStr.Mid(0, indx);
			tmpStr3.Format("%-18.18s", tmpStr2);		// Description
			AddStrAtColumn(line, tmpStr3, 0, rep.m_nCharactersPerLine, TRUE);
			tmpStr = tmpStr.Mid(indx+1);
			txt = tmpStr3;
			txt += "|";

			indx = tmpStr.Find("|");
			tmpStr2 = tmpStr.Mid(0,indx);
			tmpStr3.Format("%5d", atoi(tmpStr2));			// Product Detail ID
			AddStrAtColumn(line, tmpStr3, 28, rep.m_nCharactersPerLine, TRUE);
			tmpStr = tmpStr.Mid(indx+1);
			txt += tmpStr3;
			txt += "|";

			indx = tmpStr.Find("|");
			tmpStr2 = tmpStr.Mid(0,indx);
			tmpStr3.Format("%-18.18s", tmpStr2);				// Location Description
			AddStrAtColumn(line, tmpStr3, 39, rep.m_nCharactersPerLine, TRUE);
			tmpStr = tmpStr.Mid(indx+1);
			txt += tmpStr3;
			txt += "|";

			indx = tmpStr.Find("|");
			tmpStr2 = tmpStr.Mid(0,indx);
			tmpStr3.Format("%-18.18s", tmpStr2);				// Search Anchor Point
			AddStrAtColumn(line, tmpStr3, 61, rep.m_nCharactersPerLine, TRUE);
			tmpStr = tmpStr.Mid(indx+1);
			txt += tmpStr3;
			txt += "|";
			
			rep.m_txtArray.Add(txt);

			rep.AddBodyLine(line);

		}
		else
		{
			if (dataType == "F")
			{
				indx = tmpStr.Find("|");
				facility = tmpStr.Mid(0, indx);
				tmpStr = tmpStr.Mid(indx+1);
				
				rep.m_FacilityName = facility;
                                 //          1         2         3         4         5         6         7         8         9         10        11        12        13        14        15        16
	                             //01234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456780
				rep.AddHeaderLine("                                                             Search");
				rep.AddHeaderLine("                          Product                            Anchor");
				rep.AddHeaderLine("Product                   Detail ID    Location              Point");
				rep.AddHeaderLine("--------                  ---------    --------              -------");
				rep.AddHeaderLine("");
			}
		}
	}


	return 0;

}



int BuildProductDetailRep(CSUCCReport &rep)
{
	CString tmpStr;
	CString tmpStr2;
	CString tmpStr3;
	CString dataType;
	CString line;
	CString b;
	CString skip;
//	CSsaStringArray resultList;
	CString facility;
	CStringArray udfList;
	CString udfName, udfValue, query;
	int udfIndx;
	int indx, j, k, l;

	//set general report data
	
	rep.m_nLinesPerPage = 60;
	rep.m_nCharactersPerLine = 120;
	rep.m_paperSizeWidth = 11;
	rep.m_paperSizeLength = 8.5;

	rep.m_ReportName = "Product Detail";

	try
	{
		resultList.RemoveAll();
		GetReportList("ProductDetail",resultList);
	}
	catch (...)
	{
		AfxMessageBox("Error Getting product detail data from Database.");
		return -1;
	}

	//create the report body
	rep.m_txtArray.Add("Product Description|WMS ID|WMS Detail ID|Weight|Movement|Width|Length|Height|Each Width|Each Length|Each Height|"
		"Inner Width|Inner Length|Inner Height|Is Hazard|Unit of Issue|Is Pick-to-belt|Optimization Method|"
		"Balance on Hand|Number in Pallet|Rotate X|Rotate Y|Rotate Z|Changed in Pass|Locked|Max Stack Number|"
		"Container|Container Width|Container Length|Container Height|Is Width Override|Is Length Override|Is Height Override|"
		"TI|HI|");

	rep.m_columnCount = 35;

	for (int i=0; i < resultList.GetSize(); i++)
	{
		tmpStr = resultList.GetAt(i);
		if (tmpStr.GetAt(tmpStr.GetLength()-1) != '|')
			tmpStr += "|";

		rep.AddDataLine(tmpStr);
		dataType = GetNext(tmpStr, "%s");
		
		if (dataType == "U") {
			skip = GetNext(tmpStr, "%s");
			while (skip != "eof") {
				udfList.Add(skip);
				rep.m_txtArray[0] += skip;
				rep.m_txtArray[0] += "|";
				rep.m_columnCount++;
				skip = GetNext(tmpStr, "%s");
			}
		}
		else if (dataType == "D") {
			
			CString txt;
			txt = tmpStr;
			txt.Replace("-32767", "n/a");

			rep.m_txtArray.Add(txt);

			// make sure there is enough room left on the page to print the full record
			// 54 = 60 (lines per page) - 6 (header size)   // default header is 6
			k = 54 - (rep.GetBodySize() % 54);
			l = 5 + (udfList.GetSize() / 2) + (1 * (udfList.GetSize() % 2));

			if ( k < l ) {
				for (j=0; j < k; ++j)
					rep.AddBodyLine("<P>");		// this won't actually print
			}
		
			rep.AddBodyLine("");

			line = "<B>Product: " + GetNext(tmpStr, "%-40.40s") + "</B>";
			rep.AddBodyLine(line);

			line = "";
			line = "<B>WMSProductID: " + GetNext(tmpStr, "%-30.30s");
			line += " WMSProductDetailID: " + GetNext(tmpStr, "%-30.30s");
			line += "</B>";
			rep.AddBodyLine(line);

			line = "";
			line = "Weight: " + GetNext(tmpStr, "%-8.2f");
			line += " Movement: " + GetNext(tmpStr, "%-8.2f");
			line += " Width: " + GetNext(tmpStr, "%-8.2f");
			line += " Length: " + GetNext(tmpStr, "%-8.2f");
			line += " Height: " + GetNext(tmpStr, "%-8.2f");
			rep.AddBodyLine(line);

			line = "EachWidth:  " + GetNext(tmpStr, "%-8.2f");
			line += "            EachLength:  " + GetNext(tmpStr, "%-8.2f");
			line += "            EachHeight:  " + GetNext(tmpStr, "%-8.2f");
			rep.AddBodyLine(line);

			line = "InnerWidth: " + GetNext(tmpStr, "%-8.2f");
			line += "            InnerLength: " + GetNext(tmpStr, "%-8.2f");
			line += "            InnerHeight: " + GetNext(tmpStr, "%-8.2f");
			rep.AddBodyLine(line);

			line = "IsHazard: " + GetNext(tmpStr, "%-5.5s");
			line += "    Unit of Issue: " + GetNext(tmpStr, "%-7.7s");
			line += "    IsPickToBelt: " + GetNext(tmpStr, "%-5.5s");
			/*
			tmpStr2 = GetNext(tmpStr, "%-1.1s");
			if (tmpStr2 == "0")
				tmpStr3 = "Labor";
			else if (tmpStr2 == "1")
				tmpStr3 = "Cube";
			else
				tmpStr3 = "n/a";
			tmpStr2.Format(tmpStr3, "%-10.10s");
			line += "    OptimizeBy: " + tmpStr2;
			*/
			line += "    OptimizeBy: " + GetNext(tmpStr, "%-5.5s");
			line += "    BOH: " + GetNext(tmpStr, "%-7.2f");
			line += "    Num in Pallet: " + GetNext(tmpStr, "%-4d");
			rep.AddBodyLine(line);



			line = "Rotate On Axis:  X: " + GetNext(tmpStr, "%-5.5s");
			line += "  Y: " + GetNext(tmpStr, "%-5.5s");
			line += "  Z: " + GetNext(tmpStr, "%-5.5s");
			line += "      Changed in Pass: " + GetNext(tmpStr, "%-5.5s");
			line += "   Locked: " + GetNext(tmpStr, "%-5.5s");
			line += "   Max Stack Number: " + GetNext(tmpStr, "%-5d");
			rep.AddBodyLine(line);


		}
		else if (dataType == "C") {		// container
			rep.m_txtArray[rep.m_txtArray.GetSize()-1] += tmpStr;

			line = "Container: " + GetNext(tmpStr, "%-30.30s");
			line += "  Width: " + GetNext(tmpStr, "%-8.2f");
			line += "  Length: " + GetNext(tmpStr, "%-8.2f");
			line += "  Height: " + GetNext(tmpStr, "%-8.2f");
			skip = GetNext(tmpStr, "%s");	// IsWidthOverride
			skip = GetNext(tmpStr, "%s");	// IsDepthOverride
			skip = GetNext(tmpStr, "%s");	// IsHeightOverride
			line += "  TiXHi: " + GetNext(tmpStr, "%d");
			line += " X " + GetNext(tmpStr, "%-d");
			
			rep.AddBodyLine(line);
			
		}

		else if (dataType == "V") {
			rep.m_txtArray[rep.m_txtArray.GetSize()-1] += tmpStr;
			udfIndx = 0;
			line = "";
			while (udfIndx < udfList.GetSize()) {
				udfValue = GetNext(tmpStr, "%s");
				udfName = udfList.GetAt(udfIndx);
				line += udfName + ": ";
				if (udfValue == "eof")
					line += "n/a";
				else
					line += udfValue;
				if (udfIndx % 2 != 0) {
					rep.AddBodyLine(line);
					line = "";
				}
				else {
					while (line.GetLength() < 60) 
						line += " ";
				}
				udfIndx += 1;
			}

			if (line != "")
				rep.AddBodyLine(line);
			
		}
		else if (dataType == "F") {
			
			indx = tmpStr.Find("|");
			facility = tmpStr.Mid(0, indx);
			tmpStr = tmpStr.Mid(indx+1);
			
			rep.m_FacilityName = facility;
			/*
							 //          1         2         3         4         5         6         7         8         9         10        11        12        13        14        15        16
							 //01234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456780
			rep.AddHeaderLine("                                                                                                 Number   Rotate   Changed         Max");
			rep.AddHeaderLine("                                                                            Is      Is             In      Axis       In           Stk");
			rep.AddHeaderLine("Description                   |  Weight|Movement|   Width|  Length|  Height|Hzd|UOI|PTB|Opt|  BOH|Pallet| X | Y | Z |Pass|Locked|  Num");
			rep.AddHeaderLine("-----------                      ------ --------    -----   ------   ------ --- --- --- ---   --- ------  -   -   -  ---- ------   ---");
			*/
		}

	}	// end for


	return 0;

}



int BuildCostAnalysisDetRep(CSUCCReport &rep)
{
	CString tmpStr;
	CString dataType;
	CString line;
	CString facility;
	CString totalSelectTime;
	CString casesPerHour;

	int indx, j, k;

	//set general report data
	
	rep.m_nLinesPerPage = 60;
	rep.m_nCharactersPerLine = 163;
	rep.m_paperSizeWidth = 14.5;
	rep.m_paperSizeLength = 8.5;

	rep.m_ReportName = "Cost Analysis Detail";

	try
	{
		resultList.RemoveAll();
		if (! DEBUG)
			GetReportList("CostAnalysisDetail",resultList);
		else {
			resultList.Add("F|15-Pass4|");
			resultList.Add("D|A&C CLASSIC CORONAS DARK, 4    PK|66619|1|5.0|36|Y10913|Y1|1037|pallet\\Whse15-108Crossbars|1048|0.13889|1197.30066|0.00218|0.00009|0.03804|0.13889|83|0.00138|0.00541|0.11389|10|4049|0.00114|0.0033|0.01648|0.27266|5.0|0.0|0.0|0.00165|0.00824|0.12362|0.0|0.0|0.0|0.0|0.0|0.0|4|1489.96106|100088.45|0.00011|0.02586|193.34871|0.415147|0.56714|");
			resultList.Add("D|A+C PANETLA DARK BOX, 50    CT|66667|1|5.0|39|Y11011|Y1|1037|pallet\\Whse15-108Crossbars|1048|0.12821|1250.25159|0.00214|0.00008|0.03728|0.12821|76|0.00136|0.005|0.10657|10|4049|0.00113|0.00322|0.01608|0.26604|5.0|0.0|0.0|0.00161|0.00804|0.12063|0.0|0.0|0.0|0.0|0.0|0.0|4|1489.96106|100088.45|0.00008|0.02525|198.03809|0.4053715|0.54923|");
			resultList.Add("D|ALPINE 100'S               930, 1    CN|66370|1|5.0|10|Y10323|Y1|1037|pallet\\Whse15-108Crossbars|1048|0.5|577.25647|0.00296|0.00032|0.05506|0.5|300|0.00208|0.01959|0.36297|10|4049|0.00119|0.00432|0.02158|0.3572|5.0|0.0|0.0|0.00216|0.01079|0.16188|0.0|0.0|0.0|0.0|0.0|0.0|4|1489.96106|100088.45|0.00024|0.03357|148.95103|0.5387745|0.95686|");
			resultList.Add("D|ALPINE KING 920, 1    CN|66371|1|5.0|10|Y10712|Y1|1037|pallet\\Whse15-108Crossbars|1048|0.5|1013.28082|0.00436|0.00032|0.07839|0.5|300|0.00208|0.01949|0.3613|10|4049|0.00119|0.00375|0.01874|0.31017|5.0|0.0|0.0|0.00188|0.00938|0.14063|0.0|0.0|0.0|0.0|0.0|0.0|4|1489.96106|100088.45|0.00023|0.02931|170.60748|0.4704945|0.91019|");
			resultList.Add("D|ALPINE LIGHTS 100'S 950, 1    CN|66372|1|5.0|10|Y10221|Y1|1037|pallet\\Whse15-108Crossbars|1048|0.5|435.1655|0.00251|0.00032|0.04746|0.5|300|0.00208|0.01959|0.36297|10|4049|0.00119|0.00431|0.02153|0.35638|5.0|0.0|0.0|0.00215|0.01077|0.1615|0.0|0.0|0.0|0.0|0.0|0.0|4|1489.96106|100088.45|0.00024|0.03349|149.28459|0.5375745|0.94805|");
			resultList.Add("D|ARGOSY BLACK POUCH, 1    PK|66503|1|5.0|15|Y12122|Y1|1037|pallet\\Whse15-108Crossbars|1048|0.33333|1192.43652|0.00366|0.00022|0.0649|0.33333|200|0.00176|0.01306|0.24823|10|4049|0.00114|0.00335|0.01675|0.27721|5.0|0.0|0.0|0.00168|0.00838|0.12563|0.0|0.0|0.0|0.0|0.0|0.0|4|1489.96106|100088.45|0.0001|0.02626|190.37677|0.421707|0.73481|");
			resultList.Add("D|BARCLAY 100'S, 1    CN|66017|1|5.0|10|Y10223|Y1|1037|pallet\\Whse15-108Crossbars|1048|0.5|481.13303|0.00266|0.00032|0.04992|0.5|300|0.00208|0.01959|0.36297|10|4049|0.00119|0.00432|0.02158|0.3572|5.0|0.0|0.0|0.00216|0.01079|0.16188|0.0|0.0|0.0|0.0|0.0|0.0|4|1489.96106|100088.45|0.00024|0.03357|148.95103|0.5387745|0.95172|");
			resultList.Add("D|BARCLAY BOX, 1    CN|66018|1|5.0|10|Y10313|Y1|1037|pallet\\Whse15-108Crossbars|1048|0.5|577.25647|0.00296|0.00032|0.05506|0.5|300|0.00208|0.01949|0.3613|10|4049|0.00119|0.00372|0.01861|0.30797|5.0|0.0|0.0|0.00186|0.00931|0.13963|0.0|0.0|0.0|0.0|0.0|0.0|4|1489.96106|100088.45|0.00023|0.02911|171.77977|0.4672945|0.88365|");
			resultList.Add("D|BARCLAY KING, 1    CN|66016|1|5.0|10|Y10311|Y1|1037|pallet\\Whse15-108Crossbars|1048|0.5|528.62177|0.00281|0.00032|0.05246|0.5|300|0.00208|0.01949|0.3613|10|4049|0.00119|0.00372|0.01861|0.30797|5.0|0.0|0.0|0.00186|0.00931|0.13963|0.0|0.0|0.0|0.0|0.0|0.0|4|1489.96106|100088.45|0.00023|0.02911|171.77977|0.4672945|0.88105|");
			resultList.Add("D|BASIC FF 100'S            6230, 10    PK|66215|1|5.0|10|Y14510|Y1|1037|pallet\\Whse15-96Crossbars|1049|0.5|1576.99084|0.00616|0.00032|0.10856|0.5|300|0.00208|0.01949|0.3613|10|4049|0.00119|0.00367|0.01837|0.304|5.0|0.0|0.0|0.00184|0.00919|0.13783|0.0|0.0|0.0|0.0|0.0|0.0|4|1489.96106|100088.45|0.00024|0.02875|173.9146|0.4615245|0.93142|");
			resultList.Add("D|BASIC FF KING             6210, 10    PK|66011|1|5.0|10|Y10112|Y1|1037|pallet\\Whse15-108Crossbars|1048|0.5|454.27966|0.00257|0.00032|0.04848|0.5|300|0.00208|0.01949|0.3613|10|4049|0.00119|0.00367|0.01837|0.304|5.0|0.0|0.0|0.00184|0.00919|0.13783|0.0|0.0|0.0|0.0|0.0|0.0|4|1489.96106|100088.45|0.00023|0.02875|173.93097|0.4615245|0.8713|");
			resultList.Add("D|BASIC FF MNTH KING        6330, 1    CN|66226|1|5.0|10|Y10511|Y1|1037|pallet\\Whse15-108Crossbars|1048|0.5|727.7149|0.00344|0.00032|0.06311|0.5|300|0.00208|0.01949|0.3613|10|4049|0.00119|0.00372|0.01861|0.30797|5.0|0.0|0.0|0.00186|0.00931|0.13963|0.0|0.0|0.0|0.0|0.0|0.0|4|1489.96106|100088.45|0.00023|0.02911|171.77977|0.4672945|0.8917|");
			resultList.Add("D|BASIC MNTH LGT 100'S      6280, 10    PK|66285|1|5.0|10|Y10210|Y1|1037|pallet\\Whse15-108Crossbars|1048|0.5|492.6033|0.00269|0.00032|0.05053|0.5|300|0.00208|0.01949|0.3613|10|4049|0.00119|0.00367|0.01837|0.304|5.0|0.0|0.0|0.00184|0.00919|0.13783|0.0|0.0|0.0|0.0|0.0|0.0|4|1489.96106|100088.45|0.00024|0.02875|173.9146|0.4615245|0.8734|");
			resultList.Add("D|BASIC ULTRA LGT KING, 10    PK|66023|1|5.0|10|Y10213|Y1|1037|pallet\\Whse15-108Crossbars|1048|0.5|481.13303|0.00266|0.00032|0.04992|0.5|300|0.00208|0.01949|0.3613|10|4049|0.00119|0.00367|0.01837|0.304|5.0|0.0|0.0|0.00184|0.00919|0.13783|0.0|0.0|0.0|0.0|0.0|0.0|4|1489.96106|100088.45|0.00023|0.02875|173.93097|0.4615245|0.87274|");
			resultList.Add("D|BEECHNUT WINTERGREEN LARGE, 1    PK|66511|1|5.0|140|Y12012|Y1|1037|pallet\\Whse15-108Crossbars|1048|0.03571|1294.32336|0.00141|0.00002|0.02407|0.03571|21|0.00119|0.00139|0.0432|10|4049|0.00111|0.00292|0.01458|0.24133|5.0|0.0|0.0|0.00146|0.0073|0.10943|0.0|0.0|0.0|0.0|0.0|0.0|4|1489.96106|100088.45|0.00002|0.02298|217.53468|0.3691305|0.43636|");
			resultList.Add("D|BEN HED FILTER 100         400, 1    CN|66026|1|5.0|10|Y10322|Y1|1037|pallet\\Whse15-108Crossbars|1048|0.5|621.70251|0.0031|0.00032|0.05744|0.5|300|0.00208|0.01959|0.36297|10|4049|0.00119|0.00435|0.02175|0.35996|5.0|0.0|0.0|0.00218|0.01088|0.16313|0.0|0.0|0.0|0.0|0.0|0.0|4|1489.96106|100088.45|0.00024|0.03382|147.8499|0.5427845|0.96325|");
			resultList.Add("D|BEST V FF MNTHL KING, 10    CT|66295|1|5.0|10|Y10711|Y1|1037|pallet\\Whse15-108Crossbars|1048|0.5|934.34949|0.0041|0.00032|0.07417|0.5|300|0.00208|0.01949|0.3613|10|4049|0.00119|0.00374|0.01868|0.30907|5.0|0.0|0.0|0.00187|0.00934|0.14013|0.0|0.0|0.0|0.0|0.0|0.0|4|1489.96106|100088.45|0.00023|0.02921|171.19162|0.4688945|0.90436|");
			resultList.Add("D|BORKUM RIFF BRBN LG TIN, 7    OZ|66516|1|5.0|16|Y12111|Y1|1037|pallet\\Whse15-108Crossbars|1048|0.3125|1279.47253|0.00367|0.0002|0.06492|0.3125|187|0.00172|0.01218|0.23284|10|4049|0.00115|0.00316|0.01581|0.26163|5.0|0.0|0.0|0.00158|0.00791|0.11863|0.0|0.0|0.0|0.0|0.0|0.0|4|1489.96106|100088.45|0.00013|0.02487|201.07707|0.3992925|0.69703|");
			resultList.Add("D|BUCKS LIGHTS              1260, 1    CN|66369|1|5.0|10|Y10212|Y1|1037|pallet\\Whse15-108Crossbars|1048|0.5|533.64594|0.00282|0.00032|0.05273|0.5|300|0.00208|0.01949|0.3613|10|4049|0.00119|0.00371|0.01857|0.30731|5.0|0.0|0.0|0.00186|0.00929|0.13933|0.0|0.0|0.0|0.0|0.0|0.0|4|1489.96106|100088.45|0.00023|0.02905|172.1346|0.4663345|0.88036|");
			resultList.Add("D|BUCKS, 1    CN|66368|1|5.0|10|Y10510|Y1|1037|pallet\\Whse15-108Crossbars|1048|0.5|763.45135|0.00356|0.00032|0.06503|0.5|300|0.00208|0.01949|0.3613|10|4049|0.00119|0.00373|0.01864|0.30841|5.0|0.0|0.0|0.00186|0.00932|0.13983|0.0|0.0|0.0|0.0|0.0|0.0|4|1489.96106|100088.45|0.00023|0.02915|171.54402|0.4679345|0.89426|");
			resultList.Add("D|CAMBRIDGE LOWEST KINGS  2780, 1    CN|66062|1|5.0|10|Y10713|Y1|1037|pallet\\Whse15-108Crossbars|1048|0.5|986.63519|0.00427|0.00032|0.07697|0.5|300|0.00208|0.01949|0.3613|10|4049|0.00119|0.00374|0.01868|0.30907|5.0|0.0|0.0|0.00187|0.00934|0.14013|0.0|0.0|0.0|0.0|0.0|0.0|4|1489.96106|100088.45|0.00023|0.02921|171.19162|0.4688945|0.90716|");
			resultList.Add("D|CAMBRIDGE MTHL LT KG 1430, 1    CN|66074|1|5.0|10|Y10611|Y1|1037|pallet\\Whse15-108Crossbars|1048|0.5|830.43665|0.00377|0.00032|0.06861|0.5|300|0.00208|0.01949|0.3613|10|4049|0.00119|0.00374|0.01868|0.30907|5.0|0.0|0.0|0.00187|0.00934|0.14013|0.0|0.0|0.0|0.0|0.0|0.0|4|1489.96106|100088.45|0.00023|0.02921|171.19162|0.4688945|0.8988|");
			resultList.Add("D|CAMEL LIGHT KINGS BOX, 1    CN|66036|1|5.0|10|Y14512|Y1|1037|pallet\\Whse15-96Crossbars|1049|0.5|1558.74438|0.0061|0.00032|0.10758|0.5|300|0.00208|0.01949|0.3613|10|4049|0.00119|0.00335|0.01674|0.27707|5.0|0.0|0.0|0.00168|0.00838|0.12563|0.0|0.0|0.0|0.0|0.0|0.0|4|1489.96106|100088.45|0.00023|0.02631|190.06322|0.4223945|0.89128|");
			resultList.Add("D|CAMEL LIGHT KINGS, 1    CN|66039|1|5.0|10|Y10512|Y1|1037|pallet\\Whse15-108Crossbars|1048|0.5|812.38538|0.00371|0.00032|0.06764|0.5|300|0.00208|0.01949|0.3613|10|4049|0.00119|0.00374|0.01868|0.30907|5.0|0.0|0.0|0.00187|0.00934|0.14013|0.0|0.0|0.0|0.0|0.0|0.0|4|1489.96106|100088.45|0.00023|0.02921|171.19162|0.4688945|0.89784|");
			resultList.Add("D|CAMEL REGULAR KINGS, 1    CN|66040|1|5.0|10|Y10110|Y1|1037|pallet\\Whse15-108Crossbars|1048|0.5|419.42102|0.00246|0.00032|0.04662|0.5|300|0.00208|0.01949|0.3613|10|4049|0.00119|0.00362|0.01808|0.29914|5.0|0.0|0.0|0.00181|0.00904|0.13563|0.0|0.0|0.0|0.0|0.0|0.0|4|1489.96106|100088.45|0.00023|0.02831|176.63452|0.4544645|0.86238|");
			resultList.Add("D|CAMEL ULTRA LITE 100 BOX, 1    CN|66058|1|5.0|10|Y10113|Y1|1037|pallet\\Whse15-108Crossbars|1048|0.5|391.25568|0.00237|0.00032|0.04511|0.5|300|0.00208|0.01949|0.3613|10|4049|0.00119|0.00342|0.01708|0.28259|5.0|0.0|0.0|0.00171|0.00854|0.12813|0.0|0.0|0.0|0.0|0.0|0.0|4|1489.96106|100088.45|0.00024|0.02681|186.49936|0.4304145|0.83687|");
			resultList.Add("D|CAPRI 120 MNTHL BOX, 1    CN|66269|1|5.0|10|Y10222|Y1|1037|pallet\\Whse15-108Crossbars|1048|0.5|533.64594|0.00282|0.00032|0.05273|0.5|300|0.00208|0.01959|0.36297|10|4049|0.0012|0.00432|0.02158|0.3572|5.0|0.0|0.0|0.00216|0.01079|0.16188|0.0|0.0|0.0|0.0|0.0|0.0|4|1489.96106|100088.45|0.00025|0.03357|148.92358|0.53894|0.95463|");
			resultList.Add("D|CARLTON 100'S FILTER, 1    CN|66044|1|5.0|10|Y10810|Y1|1037|pallet\\Whse15-108Crossbars|1048|0.5|1064.42944|0.00452|0.00032|0.08113|0.5|300|0.00208|0.01949|0.3613|10|4049|0.00119|0.00379|0.01893|0.31326|5.0|0.0|0.0|0.00189|0.00947|0.14203|0.0|0.0|0.0|0.0|0.0|0.0|4|1489.96106|100088.45|0.00024|0.02959|168.97746|0.4749845|0.91746|");
			resultList.Add("D|CHERRY BLEND CIGARS, 5    PK|66629|1|5.0|28|Y10911|Y1|1037|pallet\\Whse15-108Crossbars|1048|0.17857|1144.44788|0.00242|0.00012|0.04255|0.17857|107|0.00146|0.00696|0.14108|10|4049|0.00115|0.00335|0.01674|0.27707|5.0|0.0|0.0|0.00168|0.00838|0.12563|0.0|0.0|0.0|0.0|0.0|0.0|4|1489.96106|100088.45|0.00013|0.02627|190.34442|0.4217325|0.60538|");
			resultList.Add("D|D MSTR CADET TIPS, 5    PK|66657|1|5.0|51|Y10921|Y1|1037|pallet\\Whse15-108Crossbars|1048|0.09804|1144.44788|0.00184|0.00006|0.03181|0.09804|58|0.00131|0.00384|0.08623|10|4049|0.00112|0.00325|0.01625|0.26894|5.0|0.0|0.0|0.00163|0.00812|0.12187|0.0|0.0|0.0|0.0|0.0|0.0|4|1489.96106|100088.45|0.00006|0.0255|196.08989|0.409346|0.52744|");
			resultList.Add("D|D MSTR PRESIDENTS BOX, 50    CT|66642|1|5.0|4|Y14612|Y1|1037|pallet\\Whse15-96Crossbars|1049|1.25|1525.81946|0.0133|0.00081|0.23645|1.25|750|0.00351|0.04873|0.87515|10|4049|0.00129|0.00482|0.02409|0.39862|5.0|0.0|0.0|0.00241|0.01205|0.18071|0.0|0.0|0.0|0.0|0.0|0.0|4|1489.96106|100088.45|0.00048|0.03743|133.5955|0.6006795|1.71233|");
			resultList.Add("D|DM CANENDISH POUCH, 1    PK|66556|1|5.0|26|Y12110|Y1|1037|pallet\\Whse15-108Crossbars|1048|0.19231|1243.25098|0.00265|0.00012|0.04641|0.19231|115|0.00149|0.0075|0.15049|10|4049|0.00115|0.00338|0.01688|0.27928|5.0|0.0|0.0|0.00169|0.00844|0.12663|0.0|0.0|0.0|0.0|0.0|0.0|4|1489.96106|100088.45|0.00013|0.02647|188.90074|0.4249425|0.62188|");
			resultList.Add("D|DM PALMA MADURO, 4    PK|66439|1|5.0|12|Y10910|Y1|1037|pallet\\Whse15-108Crossbars|1048|0.41667|1167.49731|0.00423|0.00027|0.07533|0.41667|249|0.00192|0.01624|0.3042|10|4049|0.00115|0.00327|0.01634|0.27045|5.0|0.0|0.0|0.00164|0.00818|0.12263|0.0|0.0|0.0|0.0|0.0|0.0|4|1489.96106|100088.45|0.00014|0.02567|194.77676|0.4121125|0.7917|");
			resultList.Add("D|DM WHISKEY   B1G1F POUCH, 1    PK|66541|1|5.0|26|Y12112|Y1|1037|pallet\\Whse15-108Crossbars|1048|0.19231|1192.43652|0.00258|0.00012|0.04537|0.19231|115|0.00149|0.0075|0.15049|10|4049|0.00115|0.00338|0.01688|0.27928|5.0|0.0|0.0|0.00169|0.00844|0.12663|0.0|0.0|0.0|0.0|0.0|0.0|4|1489.96106|100088.45|0.00013|0.02647|188.90074|0.4249425|0.62083|");
			resultList.Add("D|DORAL FLTR LGHT KINGS, 1    CN|66080|1|5.0|10|Y10310|Y1|1037|pallet\\Whse15-108Crossbars|1048|0.5|576.82751|0.00296|0.00032|0.05504|0.5|300|0.00208|0.01949|0.3613|10|4049|0.00119|0.00372|0.01861|0.30797|5.0|0.0|0.0|0.00186|0.00931|0.13963|0.0|0.0|0.0|0.0|0.0|0.0|4|1489.96106|100088.45|0.00023|0.02911|171.77977|0.4672945|0.88363|");
			resultList.Add("D|DORAL FULL FLAVOR KINGS, 1    CN|66084|1|5.0|10|Y10613|Y1|1037|pallet\\Whse15-108Crossbars|1048|0.5|882.27039|0.00394|0.00032|0.07138|0.5|300|0.00208|0.01949|0.3613|10|4049|0.00119|0.00374|0.01868|0.30907|5.0|0.0|0.0|0.00187|0.00934|0.14013|0.0|0.0|0.0|0.0|0.0|0.0|4|1489.96106|100088.45|0.00023|0.02921|171.19162|0.4688945|0.90158|");
			resultList.Add("D|DORAL LIGHT 100'S, 1    CN|66079|1|5.0|10|Y10122|Y1|1037|pallet\\Whse15-108Crossbars|1048|0.5|454.27966|0.00257|0.00032|0.04848|0.5|300|0.00208|0.01959|0.36297|10|4049|0.00119|0.00428|0.02142|0.35445|5.0|0.0|0.0|0.00214|0.01071|0.16063|0.0|0.0|0.0|0.0|0.0|0.0|4|1489.96106|100088.45|0.00024|0.03332|150.06868|0.5347745|0.94627|");
			resultList.Add("D|EL PRO CORONAS, 5    PK|66672|1|5.0|24|Y10912|Y1|1037|pallet\\Whse15-108Crossbars|1048|0.20833|1219.35144|0.00274|0.00014|0.04819|0.20833|125|0.00152|0.00812|0.16147|10|4049|0.00115|0.00327|0.01634|0.27045|5.0|0.0|0.0|0.00164|0.00818|0.12263|0.0|0.0|0.0|0.0|0.0|0.0|4|1489.96106|100088.45|0.00013|0.02567|194.79343|0.4121125|0.62179|");
			resultList.Add("D|GARRET SCOTCH SNUF POCKET, 1    CT|66598|1|5.0|490|Y11012|Y1|1037|pallet\\Whse15-108Crossbars|1048|0.0102|1323.57471|0.0012|0.00001|0.02029|0.0102|6|0.00114|0.0004|0.02572|10|4049|0.0011|0.00288|0.0144|0.23824|5.0|0.0|0.0|0.00144|0.0072|0.10803|0.0|0.0|0.0|0.0|0.0|0.0|4|1489.96106|100088.45|0.00001|0.0227|220.28191|0.364475|0.4105|");
			resultList.Add("D|GPC FF BOX KING, 1    CT|66210|1|5.0|10|Y10421|Y1|1037|pallet\\Whse15-108Crossbars|1048|0.5|626.77026|0.00312|0.00032|0.05771|0.5|300|0.00208|0.01959|0.36297|10|4049|0.00119|0.00435|0.02175|0.35996|5.0|0.0|0.0|0.00218|0.01088|0.16313|0.0|0.0|0.0|0.0|0.0|0.0|4|1489.96106|100088.45|0.00023|0.03382|147.86174|0.5427845|0.96347|");
			resultList.Add("D|GPC FULL FLVR KING, 1    CN|66199|1|5.0|10|Y10410|Y1|1037|pallet\\Whse15-108Crossbars|1048|0.5|667.92963|0.00325|0.00032|0.05991|0.5|300|0.00208|0.01949|0.3613|10|4049|0.00119|0.00372|0.01861|0.30797|5.0|0.0|0.0|0.00186|0.00931|0.13963|0.0|0.0|0.0|0.0|0.0|0.0|4|1489.96106|100088.45|0.00023|0.02911|171.77977|0.4672945|0.88851|");
			resultList.Add("D|GPC FULL FLVR MNYH KING, 1    CN|66202|1|5.0|10|Y10412|Y1|1037|pallet\\Whse15-108Crossbars|1048|0.5|715.24683|0.0034|0.00032|0.06245|0.5|300|0.00208|0.01949|0.3613|10|4049|0.00119|0.00372|0.01861|0.30797|5.0|0.0|0.0|0.00186|0.00931|0.13963|0.0|0.0|0.0|0.0|0.0|0.0|4|1489.96106|100088.45|0.00023|0.02911|171.77977|0.4672945|0.89104|");
			resultList.Add("D|KENT GOLDEN LIGHTS, 1    CN|66093|1|5.0|10|Y10111|Y1|1037|pallet\\Whse15-108Crossbars|1048|0.5|350.17853|0.00224|0.00032|0.04291|0.5|300|0.00208|0.01949|0.3613|10|4049|0.00119|0.00335|0.01674|0.27707|5.0|0.0|0.0|0.00168|0.00838|0.12563|0.0|0.0|0.0|0.0|0.0|0.0|4|1489.96106|100088.45|0.00023|0.02631|190.06322|0.4223945|0.82661|");
			resultList.Add("D|KENTUCKY CLUB REG POUCH, 1    PK|66548|1|5.0|22|Y12113|Y1|1037|pallet\\Whse15-108Crossbars|1048|0.22727|1230.15527|0.0029|0.00015|0.05113|0.22727|136|0.00155|0.00886|0.17444|10|4049|0.00117|0.00335|0.01674|0.27707|5.0|0.0|0.0|0.00168|0.00838|0.12563|0.0|0.0|0.0|0.0|0.0|0.0|4|1489.96106|100088.45|0.00018|0.02629|190.20981|0.4220635|0.64763|");
			resultList.Add("D|KODIAK SNUFF, 1    CT|66591|1|5.0|60|Y12010|Y1|1037|pallet\\Whse15-108Crossbars|1048|0.08333|1345.62439|0.00183|0.00005|0.03164|0.08333|50|0.00128|0.00325|0.07583|10|4049|0.00112|0.00311|0.01554|0.25721|5.0|0.0|0.0|0.00156|0.00778|0.11663|0.0|0.0|0.0|0.0|0.0|0.0|4|1489.96106|100088.45|0.00005|0.02443|204.63353|0.392376|0.4998|");
			resultList.Add("D|KOOL ULTRA KINGS, 1    CN|66101|1|5.0|10|Y10312|Y1|1037|pallet\\Whse15-108Crossbars|1048|0.5|621.70251|0.0031|0.00032|0.05744|0.5|300|0.00208|0.01949|0.3613|10|4049|0.00119|0.00372|0.01861|0.30797|5.0|0.0|0.0|0.00186|0.00931|0.13963|0.0|0.0|0.0|0.0|0.0|0.0|4|1489.96106|100088.45|0.00023|0.02911|171.77977|0.4672945|0.88603|");
			resultList.Add("D|LUCKY STRIKE LT KING FILTER, 1    CN|66136|1|5.0|10|Y10811|Y1|1037|pallet\\Whse15-108Crossbars|1048|0.5|1039.09619|0.00444|0.00032|0.07977|0.5|300|0.00208|0.01949|0.3613|10|4049|0.00119|0.00375|0.01875|0.3104|5.0|0.0|0.0|0.00188|0.00938|0.14073|0.0|0.0|0.0|0.0|0.0|0.0|4|1489.96106|100088.45|0.00023|0.02933|170.49115|0.4708245|0.91189|");
			resultList.Add("D|LUCKY STRIKE LW TAR KG FILTER, 1    CN|66138|1|5.0|10|Y10813|Y1|1037|pallet\\Whse15-108Crossbars|1048|0.5|1091.7074|0.00461|0.00032|0.08259|0.5|300|0.00208|0.01949|0.3613|10|4049|0.00119|0.00378|0.01888|0.31238|5.0|0.0|0.0|0.00189|0.00944|0.14162|0.0|0.0|0.0|0.0|0.0|0.0|4|1489.96106|100088.45|0.00023|0.02951|169.45111|0.4736945|0.91759|");
			resultList.Add("D|MARLBORO LIGHTS KING, 1    CN|66161|1|5.0|10|Y10710|Y1|1037|pallet\\Whse15-108Crossbars|1048|0.5|962.44379|0.00419|0.00032|0.07567|0.5|300|0.00208|0.01949|0.3613|10|4049|0.00119|0.00375|0.01873|0.30995|5.0|0.0|0.0|0.00187|0.00937|0.14053|0.0|0.0|0.0|0.0|0.0|0.0|4|1489.96106|100088.45|0.00023|0.02929|170.724|0.4701745|0.90715|");
			resultList.Add("D|MARLBORO LT 100'S BOX, 1    CN|66156|1|5.0|10|Y10420|Y1|1037|pallet\\Whse15-108Crossbars|1048|0.5|667.92963|0.00325|0.00032|0.05991|0.5|300|0.00208|0.01959|0.36297|10|4049|0.00119|0.00453|0.02267|0.37513|5.0|0.0|0.0|0.00227|0.01133|0.17|0.0|0.0|0.0|0.0|0.0|0.0|4|1489.96106|100088.45|0.00024|0.03519|142.07339|0.5648245|0.98777|");
			resultList.Add("D|MARLBORO MED KG 2710, 1    CN|66183|1|5.0|10|Y10320|Y1|1037|pallet\\Whse15-108Crossbars|1048|0.5|576.82751|0.00296|0.00032|0.05504|0.5|300|0.00208|0.01959|0.36297|10|4049|0.00119|0.00432|0.0216|0.35748|5.0|0.0|0.0|0.00216|0.0108|0.162|0.0|0.0|0.0|0.0|0.0|0.0|4|1489.96106|100088.45|0.00023|0.03359|148.85219|0.5391745|0.95719|");
			resultList.Add("D|MERIT ULT LTS MNTHL100 1040, 1    CN|66143|1|5.0|10|Y10121|Y1|1037|pallet\\Whse15-108Crossbars|1048|0.5|350.17853|0.00224|0.00032|0.04291|0.5|300|0.00208|0.01959|0.36297|10|4049|0.00119|0.00427|0.02137|0.35362|5.0|0.0|0.0|0.00214|0.01068|0.16025|0.0|0.0|0.0|0.0|0.0|0.0|4|1489.96106|100088.45|0.00024|0.03324|150.40726|0.5335645|0.9395|");
			resultList.Add("D|MERIT ULTIMA KING         2870, 1    CT|66280|1|5.0|10|Y10120|Y1|1037|pallet\\Whse15-108Crossbars|1048|0.5|419.42102|0.00246|0.00032|0.04662|0.5|300|0.00208|0.01959|0.36297|10|4049|0.00119|0.00428|0.02138|0.35389|5.0|0.0|0.0|0.00214|0.01069|0.16038|0.0|0.0|0.0|0.0|0.0|0.0|4|1489.96106|100088.45|0.00023|0.03327|150.30646|0.5339645|0.94356|");
			resultList.Add("D|MERIT ULTRA LIGHT BX 1800, 1    CN|66126|1|5.0|10|Y10123|Y1|1037|pallet\\Whse15-108Crossbars|1048|0.5|391.25568|0.00237|0.00032|0.04511|0.5|300|0.00208|0.01959|0.36297|10|4049|0.00119|0.00428|0.02138|0.35389|5.0|0.0|0.0|0.00214|0.01069|0.16038|0.0|0.0|0.0|0.0|0.0|0.0|4|1489.96106|100088.45|0.00024|0.03327|150.29424|0.5339645|0.9421|");
			resultList.Add("D|MERIT ULTRA LT BX 100 1830, 1    CN|66125|1|5.0|10|Y10423|Y1|1037|pallet\\Whse15-108Crossbars|1048|0.5|676.97046|0.00328|0.00032|0.0604|0.5|300|0.00208|0.01959|0.36297|10|4049|0.00119|0.0044|0.02198|0.36382|5.0|0.0|0.0|0.0022|0.01099|0.16487|0.0|0.0|0.0|0.0|0.0|0.0|4|1489.96106|100088.45|0.00024|0.03417|146.33543|0.5483845|0.97182|");
			resultList.Add("D|MISTY SLIM MNTHL 100 LT BOX, 1    CN|66015|1|5.0|10|Y10211|Y1|1037|pallet\\Whse15-108Crossbars|1048|0.5|435.1655|0.00251|0.00032|0.04746|0.5|300|0.00208|0.01949|0.3613|10|4049|0.00119|0.00367|0.01837|0.304|5.0|0.0|0.0|0.00184|0.00919|0.13783|0.0|0.0|0.0|0.0|0.0|0.0|4|1489.96106|100088.45|0.00024|0.02875|173.9146|0.4615245|0.87032|");
			resultList.Add("D|MURIEL SWEET MINIS, 5    PK|66769|1|5.0|56|Y14610|Y1|1037|pallet\\Whse15-96Crossbars|1049|0.08929|1541.43738|0.002|0.00006|0.03443|0.08929|53|0.00129|0.00348|0.0799|10|4049|0.00113|0.00305|0.01527|0.2528|5.0|0.0|0.0|0.00153|0.00764|0.11462|0.0|0.0|0.0|0.0|0.0|0.0|4|1489.96106|100088.45|0.00007|0.02404|207.94809|0.3861215|0.50043|");
			resultList.Add("D|NOW FILTER 100'S, 1    CN|66186|1|5.0|10|Y10411|Y1|1037|pallet\\Whse15-108Crossbars|1048|0.5|626.77026|0.00312|0.00032|0.05771|0.5|300|0.00208|0.01949|0.3613|10|4049|0.00119|0.00372|0.01861|0.30797|5.0|0.0|0.0|0.00186|0.00931|0.13963|0.0|0.0|0.0|0.0|0.0|0.0|4|1489.96106|100088.45|0.00024|0.02911|171.76379|0.4672945|0.88635|");
			resultList.Add("D|OLD GOLD FILTER, 1    CN|66192|1|5.0|10|Y10413|Y1|1037|pallet\\Whse15-108Crossbars|1048|0.5|676.97046|0.00328|0.00032|0.0604|0.5|300|0.00208|0.01949|0.3613|10|4049|0.00119|0.00372|0.01861|0.30797|5.0|0.0|0.0|0.00186|0.00931|0.13963|0.0|0.0|0.0|0.0|0.0|0.0|4|1489.96106|100088.45|0.00023|0.02911|171.77977|0.4672945|0.88899|");
			resultList.Add("D|PLOW BOY LARGE, 10    OZ|66605|1|5.0|22|Y12013|Y1|1037|pallet\\Whse15-108Crossbars|1048|0.22727|1329.15393|0.00305|0.00015|0.05353|0.22727|136|0.00155|0.00886|0.17444|10|4049|0.00115|0.00315|0.01576|0.26075|5.0|0.0|0.0|0.00158|0.00788|0.11823|0.0|0.0|0.0|0.0|0.0|0.0|4|1489.96106|100088.45|0.00014|0.02479|201.69147|0.3980125|0.62604|");
			resultList.Add("D|PRNC ALBRT MLW WHISKY, 1    PK|66560|1|5.0|180|Y11010|Y1|1037|pallet\\Whse15-108Crossbars|1048|0.02778|1271.38428|0.00134|0.00002|0.02281|0.02778|16|0.00117|0.00108|0.03776|10|4049|0.0011|0.00286|0.01429|0.23647|5.0|0.0|0.0|0.00143|0.00715|0.10723|0.0|0.0|0.0|0.0|0.0|0.0|4|1489.96106|100088.45|0.00001|0.02254|221.84792|0.361905|0.4225|");
			resultList.Add("D|RALEIGH X/LT MNTHL KING, 1    CN|66232|1|5.0|10|Y10513|Y1|1037|pallet\\Whse15-108Crossbars|1048|0.5|778.89728|0.00361|0.00032|0.06585|0.5|300|0.00208|0.01949|0.3613|10|4049|0.00119|0.00372|0.01861|0.30797|5.0|0.0|0.0|0.00186|0.00931|0.13963|0.0|0.0|0.0|0.0|0.0|0.0|4|1489.96106|100088.45|0.00023|0.02911|171.77977|0.4672945|0.89444|");
			resultList.Add("D|SAIL AROMATIC BOGOF, 2    PK|66535|1|5.0|80|Y12120|Y1|1037|pallet\\Whse15-108Crossbars|1048|0.0625|1243.25098|0.00161|0.00004|0.02773|0.0625|37|0.00124|0.00245|0.06176|10|4049|0.0011|0.00307|0.01535|0.25404|5.0|0.0|0.0|0.00153|0.00767|0.11512|0.0|0.0|0.0|0.0|0.0|0.0|4|1489.96106|100088.45|0.00001|0.02413|207.22357|0.387365|0.47692|");
			resultList.Add("D|SALEM KINGS, 1    CN|66250|1|5.0|10|Y10612|Y1|1037|pallet\\Whse15-108Crossbars|1048|0.5|911.9704|0.00403|0.00032|0.07297|0.5|300|0.00208|0.01949|0.3613|10|4049|0.00119|0.00374|0.01868|0.30907|5.0|0.0|0.0|0.00187|0.00934|0.14013|0.0|0.0|0.0|0.0|0.0|0.0|4|1489.96106|100088.45|0.00023|0.02921|171.19162|0.4688945|0.90317|");
			resultList.Add("D|SALEM ULTRA 100'S, 1    CN|66256|1|5.0|10|Y10220|Y1|1037|pallet\\Whse15-108Crossbars|1048|0.5|492.6033|0.00269|0.00032|0.05053|0.5|300|0.00208|0.01959|0.36297|10|4049|0.00119|0.00432|0.02158|0.3572|5.0|0.0|0.0|0.00216|0.01079|0.16188|0.0|0.0|0.0|0.0|0.0|0.0|4|1489.96106|100088.45|0.00024|0.03357|148.95103|0.5387745|0.95233|");
			resultList.Add("D|SIR WALTER RALEIGH, 1    CN|66582|1|5.0|20|Y12011|Y1|1037|pallet\\Whse15-108Crossbars|1048|0.25|1379.15991|0.00332|0.00016|0.05835|0.25|150|0.0016|0.00975|0.19001|10|4049|0.00115|0.00322|0.01608|0.26604|5.0|0.0|0.0|0.00161|0.00804|0.12063|0.0|0.0|0.0|0.0|0.0|0.0|4|1489.96106|100088.45|0.00014|0.02527|197.85519|0.4057025|0.65414|");
			resultList.Add("D|SKOAL CHERRY LONG CUT, 1    CT|66507|1|5.0|30|Y11023|Y1|1037|pallet\\Whse15-108Crossbars|1048|0.16667|1303.28857|0.00251|0.00011|0.04379|0.16667|100|0.00144|0.00653|0.13348|10|4049|0.00113|0.00357|0.01785|0.29539|5.0|0.0|0.0|0.00178|0.00892|0.13386|0.0|0.0|0.0|0.0|0.0|0.0|4|1489.96106|100088.45|0.00009|0.02791|179.17035|0.4479515|0.62529|");
			resultList.Add("D|SKOAL LONG CUT CLASSIC, 1    CT|66612|1|5.0|24|Y11021|Y1|1037|pallet\\Whse15-108Crossbars|1048|0.20833|1250.25159|0.00278|0.00014|0.04887|0.20833|125|0.00152|0.00816|0.16217|10|4049|0.00114|0.00356|0.01778|0.29431|5.0|0.0|0.0|0.00178|0.00889|0.13337|0.0|0.0|0.0|0.0|0.0|0.0|4|1489.96106|100088.45|0.00011|0.02782|179.74181|0.446547|0.65764|");
			resultList.Add("D|SLIM PRICE MNTHL LT 100'S, 1    CN|66310|1|5.0|10|Y10321|Y1|1037|pallet\\Whse15-108Crossbars|1048|0.5|528.62177|0.00281|0.00032|0.05246|0.5|300|0.00208|0.01959|0.36297|10|4049|0.00119|0.00432|0.02158|0.3572|5.0|0.0|0.0|0.00216|0.01079|0.16188|0.0|0.0|0.0|0.0|0.0|0.0|4|1489.96106|100088.45|0.00024|0.03357|148.95103|0.5387745|0.95426|");
			resultList.Add("D|SLIM PRICE NON FILTER KG, 1    CN|66095|1|5.0|10|Y10610|Y1|1037|pallet\\Whse15-108Crossbars|1048|0.5|861.92456|0.00387|0.00032|0.07029|0.5|300|0.00208|0.01949|0.3613|10|4049|0.00119|0.00374|0.01868|0.30907|5.0|0.0|0.0|0.00187|0.00934|0.14013|0.0|0.0|0.0|0.0|0.0|0.0|4|1489.96106|100088.45|0.00023|0.02921|171.19162|0.4688945|0.90049|");
			resultList.Add("D|TAREYTON DUAL FILTER, 1    CN|66276|1|5.0|10|Y10812|Y1|1037|pallet\\Whse15-108Crossbars|1048|0.5|1115.8468|0.00468|0.00032|0.08388|0.5|300|0.00208|0.01949|0.3613|10|4049|0.00119|0.00384|0.0192|0.31768|5.0|0.0|0.0|0.00192|0.0096|0.14402|0.0|0.0|0.0|0.0|0.0|0.0|4|1489.96106|100088.45|0.00023|0.02999|166.73871|0.4813945|0.92658|");
			resultList.Add("D|WINDSOR PANATELLA, 5    PK|66733|1|5.0|40|Y11013|Y1|1037|pallet\\Whse15-108Crossbars|1048|0.125|1303.28857|0.00216|0.00008|0.03753|0.125|75|0.00136|0.00487|0.10437|10|4049|0.00114|0.00319|0.01594|0.26383|5.0|0.0|0.0|0.0016|0.00797|0.11962|0.0|0.0|0.0|0.0|0.0|0.0|4|1489.96106|100088.45|0.00011|0.02506|199.54048|0.402317|0.54424|");
		}
	}
	catch (...)
	{
		AfxMessageBox("Error Getting cost analysis detail data from Database.");
		return -1;
	}

	rep.m_txtArray.Add("Product|WMS ID|WMS Detail ID|Movement|Number in Pallet|Location|Section|Section ID|"
		"Bay Profile|Bay Profile ID|Number of Putaways|Ptwy Distance|Ptwy Travel Hours|Ptwy Handling Hours|"
		"Putaway Cost|Number of Replenishments|Rpln Distance|Rpln Travel Hours|Rpln Handling Hours|Replenishment Cost|"
		"Number of Orders|Select Distance|Select Travel Hours|Case Handling Hours|Movement * Case Handling Hours|"
		"Select Cost|Stocker Cases|Stocker Distance|Stocker Travel Hours|Stocker Handling Hours|"
		"Movement * Stkr Handling Hours|Stocker Cost|Pallet Selects|Pal Sel Distance|Pal Sel Travel|Pal Sel Handling|"
		"Movement * Pal Sel Handling|Pallet Select Cost|Broken Order Count|Broken Order Distance|Pick Distance|Scale|"
		"Total Select Time|Cases Per Hour|Selection Cost|Total Cost|");

	rep.m_columnCount = 46;

	//create the report body

	for (int i=0; i < resultList.GetSize(); i++)
	{
		tmpStr = resultList.GetAt(i);
		rep.AddDataLine(tmpStr);
		dataType = GetNext(tmpStr, "%s");
		
		if (dataType == "D") {
			
			// make sure there is enough room on the page to print the full record
			// 54 = 60 (lines per page) - 6 (header size)   // default header is 6
			k = 54 - (rep.GetBodySize() % 54);
			if ( k < 13 ) {
				for (j=0; j < k; ++j)
					rep.AddBodyLine("<P>");		// this won't actually print
			}


			rep.m_txtArray.Add(tmpStr);

			line = "<B>Product: " + GetNext(tmpStr, "%-50.50s");
			line += "  WMSID: " + GetNext(tmpStr, "%-18.18s");
			line += "  WMS Detail ID: " + GetNext(tmpStr, "%-5.5s");
			line += "  Movement: " + GetNext(tmpStr, "%-5d");
			line += "  Number in Pallet: " + GetNext(tmpStr, "%-6d");
			line  += "</B>";
			rep.AddBodyLine(line);

			line = "<B>Location: " + GetNext(tmpStr, "%-18.18s");
			line += "  Section: " + GetNext(tmpStr, "%-10.10s");
			line += "  Section ID: " + GetNext(tmpStr, "%06d");
			line += "     Bay Profile: " + GetNext(tmpStr, "%-30.30s");
			line += "     Bay Profile ID: " + GetNext(tmpStr, "%06d");
			line += "</B>";
			rep.AddBodyLine(line);
			//rep.AddBodyLine("");
				  //          1         2         3         4         5         6         7         8         9         10        11        12        13        14        15        16
				  //01234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456780
			
			
			line = " Putaway:         ";
			line += "Number: " + GetNext(tmpStr, "%8.2f"); // Number of Putaways
			line += "  Distance: " + GetNext(tmpStr, "%12.2f"); // Putaway distance
			line += "  Travel Hours: " + GetNext(tmpStr, "%12.4f") ;	// Putway Travel Hours
			line += "  Handling Hours: " + GetNext(tmpStr, "%9.5f"); // Putaway Handling Hours
			line += "                                   ";
			line += " Cost: " + GetNext(tmpStr, "%13.5f"); // Putaway Cost
			rep.AddBodyLine(line);

			line = " Replenishment:   ";
			line += "Number: " + GetNext(tmpStr, "%8.2f"); // Number of Replens
			line += "  Distance: " + GetNext(tmpStr, "%12.2f");    // Replen Distance
			line += "  Travel Hours: " + GetNext(tmpStr, "%12.4f"); // Replen Hours
			line += "  Handling Hours: " + GetNext(tmpStr, "%9.5f"); // Replen Handling Hours
			line += "                                   ";
			line += " Cost: " + GetNext(tmpStr, "%13.5f"); // Replen Cost
			rep.AddBodyLine(line);
			
			line = " Selection:       ";
			line += "Orders: " + GetNext(tmpStr, "%8d"); // Number Orders
			line += "  Distance: " + GetNext(tmpStr, "%12.2f"); // Select Distance
			line += "  Travel Hours: " + GetNext(tmpStr, "%12.4f"); // Selection Travel Hours
			line += "  Handling Hours: " + GetNext(tmpStr, "%9.5f"); // Case Handling Hours
			line += "  Handling * Movement: " + GetNext(tmpStr, "%12.5f"); // Movement * Case Handling Hours
			line += " Cost: " + GetNext(tmpStr, "%13.5f");	// Select cost
			rep.AddBodyLine(line);

			line = " Stocker:         ";
			line += " Cases: " + GetNext(tmpStr, "%8d");	// Stocker cases
			line += "  Distance: " + GetNext(tmpStr, "%12.2f"); // Stocker Distance
			line += "  Travel Hours: " + GetNext(tmpStr, "%12.4f"); // Stocker travel 
			line += "  Handling Hours: " + GetNext(tmpStr, "%9.5f"); // Stocker handling
			line += "  Handling * Movement: " + GetNext(tmpStr, "%12.5f"); // Handling * movement
			line += " Cost: " + GetNext(tmpStr, "%13.5f"); // Stocker cost
			rep.AddBodyLine(line);

			line = " Pallet Selects:  ";
			line += "Number: " + GetNext(tmpStr, "%8d");	 // pallet selects
			line += "  Distance: " + GetNext(tmpStr, "%12.2f");	// pallet select distance
			line += "  Travel Hours: " + GetNext(tmpStr, "%12.4f"); // pallet select travel
			line += "  Handling Hours: " + GetNext(tmpStr, "%9.5f"); // pallet select handling
			line += "  Handling * Movement: " + GetNext(tmpStr, "%12.5f"); // handling * movement
			line += " Cost: " + GetNext(tmpStr, "%13.5f"); // pallet select cost
			rep.AddBodyLine(line);

			line = "      Broken Order Count: " + GetNext(tmpStr, "%8d");	// Broken Order Count
			line += "  Distance: " + GetNext(tmpStr, "%12.2f");    // Broken Order Distance
			line += "  Total Select Distance: " + GetNext(tmpStr, "%-8d");    // Pick Distance
			line += "     Selection Scale:  " + GetNext(tmpStr, "%-12.5f");			// Scale
			rep.AddBodyLine(line);	
			
			totalSelectTime = GetNext(tmpStr, "%9.2f");
			casesPerHour = GetNext(tmpStr, "%-9.2f");
			line = " Select Totals:                                                                     ";
			line += "     Cases Per Hour: " + casesPerHour;
			line += "         Select Time: " + totalSelectTime;
			line += "    Cost: " + GetNext(tmpStr, "%10.2f"); // Selection Cost
			rep.AddBodyLine(line);

			line = " Total:                                                                                                                                        ";
			line += "Total Cost: " + GetNext(tmpStr, "%10.2f"); // total cost
			rep.AddBodyLine(line);
			
			rep.AddBodyLine("");
			rep.AddBodyLine("");


		}

		else if (dataType == "F") {
		
			indx = tmpStr.Find("|");
			facility = tmpStr.Mid(0, indx);
			tmpStr = tmpStr.Mid(indx+1);
			
			rep.m_FacilityName = facility;
		}

	}	// end for

	return 0;

}




int BuildCostAnalysisSumRep(CSUCCReport &rep)
{
	CString tmpStr, tmpStr2;
	CString dataType;
	CString line;
	CString facility;

	CStringArray ptwyArray;
	CStringArray rplnArray;
	CStringArray caseArray;
	CStringArray stockerArray;
	CStringArray psArray;
	CString txt;
	CString section, sectionID;
	
	double count, travel, handling, cost, distance, totalCost, totalTime;
	
	int indx;
	
	//set general report data
	
	totalCost = 0;
	totalTime = 0;
	
	rep.m_nLinesPerPage = 60;
	rep.m_nCharactersPerLine = 165;
	rep.m_paperSizeWidth = 14.5;
	rep.m_paperSizeLength = 8.5;
	
	rep.m_ReportName = "Cost Analysis Summary";
	
	try
	{
		resultList.RemoveAll();
		if (! DEBUG)
			GetReportList("CostAnalysisSummary",resultList);
		else {
			resultList.Add("F|Training|");
			resultList.Add("D|1282|AA|182.84419|925326.9142|3.27501|2.75975|125.21991|317.80856|762576.0|2.83467|12.27671|313.56122|22057.21|7416548.0|0.46758|69.38216|1210.33499|539.18|1.58906|27.80848|0.0|0.0|0.0|");
			resultList.Add("D|1283|BB|242.35796|472519.9575|5.57924|3.65721|191.6564|412.00855|296580.0|1.09971|22.79619|495.83957|17625.4|622336.0|0.0|60.95775|0.0|0.0|0.0|0.0|0.0|0.0|0.0|");
			resultList.Add("D|1284|FF|4.22299|1033388.09156|0.45258|0.06373|10.71285|152.82191|1100176.0|3.88401|1.69052|115.66929|2886.03|653730.0|0.45579|9.66887|169.20477|1271.9662|4.59218|80.36332|0.0|0.0|0.0|");
			resultList.Add("D|1285|HZ|0.17175|177060.9575|0.05771|0.00259|1.25091|18.37803|44086.0|0.19454|0.70154|18.59403|1271.53|254304.0|0.14751|4.37886|76.63056|0.0|0.0|0.0|0.0|0.0|0.0|");
			resultList.Add("D|0.0|Totals|429.59689|2608295.9207600001|9.36454|6.48328|328.84007|901.01705|2203418.0|8.01293|37.46496|943.66411|43840.17|8946918.0|1.07088|144.38764|1456.17032|1811.1462|6.18124|108.1718|0.0|0.0|0.0|");
		}
	}
	catch (...)
	{
		AfxMessageBox("Error Getting cost analysis detail data from Database.");
		return -1;
	}
	
	rep.m_txtArray.Add("Section ID|Section|Putaways|Ptwy Travel Distance|Ptwy Travel Time|Ptwy Insertion Time|Total Ptwy Time|Ptwy Avg Travel Distance|Ptwy Avg Insertion Time|Avg Ptwy Time|Ptwy Units Per Hour|Total Ptwy Cost|Ptwy Cost Per Unit|"
		"Replenishments|Rpln Travel Distance|Rpln Travel Time|Rpln Insertion Time|Total Rpln Time|Rpln Avg Travel Distance|Rpln Avg Insertion Time|Avg Rpln Time|Rpln Units Per Hour|Rpln Total Cost|Rpln Cost Per Unit|"
		"Selection Units|Sel Travel Distance|Sel Travel Time|Sel Handling Time|Sel Total Time|Sel Avg Travel Distance|Sel Avg Handling Time|Avg Selection Time|Sel Units Per Hour|Sel Total Cost|Sel Cost Per Unit|"
		"Stocker Cases|Stkr Travel Distance|Stkr Travel Time|Stkr Handling Time|Stkr Total Time|Stkr Avg Travel Distance|Stkr Avg Handling Time|Avg Stkr Time|Stkr Units Per Hour|Stkr Total Cost|Stkr Cost Per Unit|"
		"Pallet Selects|Pal Sel Travel Distance|Pal Sel Travel Time|Pal Sel Handling Time|Pal Sel Selection Time|Pal Sel Avg Travel Distance|Pal Sel Avg Handling Time|Pal Sel Avg Select Time|Pal Sel Units Per Hour|Pal Sel Total Cost|Pal Sel Cost Per Unit|");
	rep.m_columnCount = 57;
	
	//create the report body
	
	for (int i=0; i < resultList.GetSize(); i++)
	{
		tmpStr = resultList.GetAt(i);
		rep.AddDataLine(tmpStr);
		dataType = GetNext(tmpStr, "%s");
		
		if (dataType == "D") {
			
			// Putaway
			sectionID = GetNext(tmpStr, "%7d");		// section id
			if (atoi(sectionID) > 0) {
				line = sectionID;
				txt = sectionID;
				txt += "|";
				section = GetNext(tmpStr, "%-10.10s");
				line += "  " + section;
				txt += section;
				txt += "|";
			}
			else {
				line = "Section Totals:    ";
				txt = "Section Totals||";
				section = GetNext(tmpStr, "%-10.10s");
			}
			
			tmpStr2 = GetNext(tmpStr, "%14.2f");
			line += " " + tmpStr2;
			count = atof(tmpStr2);			// number of putaways		b
			txt += tmpStr2;
			txt += "|";

			tmpStr2 = GetNext(tmpStr, "%13.2f");
			line += " " + tmpStr2;
			txt += tmpStr2;
			txt += "|";
			distance = atof(tmpStr2);		// fork distance			c
			
			tmpStr2 = GetNext(tmpStr, "%10.2f");
			line += " " + tmpStr2;
			txt += tmpStr2;
			txt += "|";
			travel = atof(tmpStr2);			// putaway travel hours		d
			
			tmpStr2 = GetNext(tmpStr, "%10.2f");
			line += " " + tmpStr2;
			txt += tmpStr2;
			txt += "|";
			handling = atof(tmpStr2);		// putaway handling hours	e
			
			tmpStr2.Format("%10.2f", travel + handling);	
			line += " " + tmpStr2;			// total ptwy time			f
			txt += tmpStr2;
			txt += "|";
			
			if (count > 0)
				tmpStr2.Format("%13.2f", distance / count);		
			else
				tmpStr2.Format("%13.2f", 0.0);
			line += " " + tmpStr2;			// avg travel dist			g
			txt += tmpStr2;
			txt += "|";
			
			if (count > 0)
				tmpStr2.Format("%13.5f", handling / count);
			else
				tmpStr2.Format("%13.5f", 0.0);
			line += " " + tmpStr2;			// avg insertion time		h
			txt += tmpStr2;
			txt += "|";
			
			if (count > 0)
				tmpStr2.Format("%13.4f", (travel + handling) / count);
			else
				tmpStr2.Format("%13.4f", 0.0);
			line += " " + tmpStr2;			// avg putaway time			i
			txt += tmpStr2;
			txt += "|";
			
			if (travel+handling > 0)
				tmpStr2.Format("%15.2f", count / (travel + handling));
			else
				tmpStr2.Format("%15.2f", 0.0);
			line += " " + tmpStr2;			// units per hour			j
			txt += tmpStr2;
			txt += "|";
			
			tmpStr2 = GetNext(tmpStr, "%10.2f");
			line += " " + tmpStr2;
			txt += tmpStr2;
			txt += "|";			
			cost = atof(tmpStr2);			// total cost				k
			
			if (count > 0)
				tmpStr2.Format("%14.2f", cost / count);
			else
				tmpStr2.Format("%14.2f", 0.0);
			line += " " + tmpStr2;			// cost per unit			l
			txt += tmpStr2;
			txt += "|";
			
			ptwyArray.Add(line);
			
			if (atoi(sectionID) > 0) {
				totalCost += cost;
				totalTime += travel + handling;
			}
			
			
			
			// Replenishment
			if (atoi(sectionID) > 0) {
				line = sectionID;
				line += "  " + section;
			}
			else {
				line = "Section Totals:    ";
			}
			
			tmpStr2 = GetNext(tmpStr, "%14.2f");
			line += " " + tmpStr2;
			txt += tmpStr2;
			txt += "|";
			count = atof(tmpStr2);			// number of replens		b
			
			tmpStr2 = GetNext(tmpStr, "%13.2f");
			line += " " + tmpStr2;
			txt += tmpStr2;
			txt += "|";
			distance = atof(tmpStr2);		// fork distance			c
			
			tmpStr2 = GetNext(tmpStr, "%10.2f");
			line += " " + tmpStr2;
			txt += tmpStr2;
			txt += "|";
			travel = atof(tmpStr2);			// replen travel hours		d
			
			tmpStr2 = GetNext(tmpStr, "%10.2f");
			line += " " + tmpStr2;
			txt += tmpStr2;
			txt += "|";
			handling = atof(tmpStr2);		// replen handling hours	e
			
			tmpStr2.Format("%10.2f", travel + handling);	
			line += " " + tmpStr2;			// total rpln time			f
			txt += tmpStr2;
			txt += "|";
			
			if (count > 0)
				tmpStr2.Format("%13.2f", distance / count);		
			else
				tmpStr2.Format("%13.2f", 0.0);
			line += " " + tmpStr2;			// avg travel dist			g
			txt += tmpStr2;
			txt += "|";
			
			if (count > 0)
				tmpStr2.Format("%13.5f", handling / count);
			else
				tmpStr2.Format("%13.5f", 0.0);
			line += " " + tmpStr2;			// avg insertion time		h
			txt += tmpStr2;
			txt += "|";
			
			if (count > 0)
				tmpStr2.Format("%13.4f", (travel + handling) / count);
			else
				tmpStr2.Format("%13.4f", 0.0);
			line += " " + tmpStr2;			// avg rpln time			i
			txt += tmpStr2;
			txt += "|";
			
			if (travel+handling > 0)
				tmpStr2.Format("%15.2f", count / (travel + handling));
			else
				tmpStr2.Format("%15.2f", 0.0);
			line += " " + tmpStr2;			// units per hour			j
			txt += tmpStr2;
			txt += "|";
			
			tmpStr2 = GetNext(tmpStr, "%10.2f");
			line += " " + tmpStr2;	
			txt += tmpStr2;
			txt += "|";			
			cost = atof(tmpStr2);			// total cost				k
			
			if (count > 0)
				tmpStr2.Format("%14.2f", cost / count);
			else
				tmpStr2.Format("%14.2f", 0.0);
			line += " " + tmpStr2;			// cost per unit			l
			txt += tmpStr2;
			txt += "|";
			
			rplnArray.Add(line);
			
			if (atoi(sectionID) > 0) {
				totalCost += cost;
				totalTime += travel + handling;
			}
			
			
			
			// Selection
			if (atoi(sectionID) > 0) {
				line = sectionID;
				line += "  " + section;
			}
			else {
				line = "Section Totals:    ";
			}
			
			tmpStr2 = GetNext(tmpStr, "%14.2f");
			line += " " + tmpStr2;
			txt += tmpStr2;
			txt += "|";
			count = atof(tmpStr2);			// movement					b
			
			tmpStr2 = GetNext(tmpStr, "%13.2f");
			line += " " + tmpStr2;
			txt += tmpStr2;
			txt += "|";
			distance = atof(tmpStr2);		// select distance			c
			
			tmpStr2 = GetNext(tmpStr, "%10.2f");
			line += " " + tmpStr2;
			txt += tmpStr2;
			txt += "|";
			travel = atof(tmpStr2);			// select travel hours		d
			
			tmpStr2 = GetNext(tmpStr, "%10.2f");
			line += " " + tmpStr2;
			txt += tmpStr2;
			txt += "|";
			handling = atof(tmpStr2);		// select handling hours	e
			
			tmpStr2.Format("%10.2f", travel + handling);	
			line += " " + tmpStr2;			// total select time			f
			txt += tmpStr2;
			txt += "|";
			
			if (count > 0)
				tmpStr2.Format("%13.2f", distance / count);		
			else
				tmpStr2.Format("%13.2f", 0.0);
			line += " " + tmpStr2;			// avg travel dist			g
			txt += tmpStr2;
			txt += "|";
			
			if (count > 0)
				tmpStr2.Format("%13.5f", handling / count);
			else
				tmpStr2.Format("%13.5f", 0.0);
			line += " " + tmpStr2;			// avg insertion time		h
			txt += tmpStr2;
			txt += "|";
			
			if (count > 0)
				tmpStr2.Format("%13.5f", (travel + handling) / count);
			else
				tmpStr2.Format("%13.5f", 0.0);
			line += " " + tmpStr2;			// avg select time			i
			txt += tmpStr2;
			txt += "|";
			
			if (travel+handling > 0)
				tmpStr2.Format("%15.2f", count / (travel + handling));
			else
				tmpStr2.Format("%15.2f", 0.0);
			line += " " + tmpStr2;			// units per hour			j
			txt += tmpStr2;
			txt += "|";
			
			tmpStr2 = GetNext(tmpStr, "%10.2f");
			line += " " + tmpStr2;
			txt += tmpStr2;
			txt += "|";			
			cost = atof(tmpStr2);			// total select cost				k
			
			if (count > 0)
				tmpStr2.Format("%14.2f", cost / count);
			else
				tmpStr2.Format("%14.2f", 0.0);
			line += " " + tmpStr2;			// cost per unit			l
			txt += tmpStr2;
			txt += "|";
			
			caseArray.Add(line);
			
			if (atoi(sectionID) > 0) {
				totalCost += cost;
				totalTime += travel + handling;
			}			
			
			
			// stocker
			if (atoi(sectionID) > 0) {
				line = sectionID;
				line += "  " + section;
			}
			else {
				line = "Section Totals:    ";
			}
			
			tmpStr2 = GetNext(tmpStr, "%14.2f");
			line += " " + tmpStr2;
			txt += tmpStr2;
			txt += "|";
			count = atof(tmpStr2);			// stocker cases
			
			tmpStr2.Format("%13.2f", 0.0);
			line += " " + tmpStr2;
			txt += tmpStr2;
			txt += "|";
			distance = atof(tmpStr2);		// stocker distance	
			
			tmpStr2.Format("%10.2f", 0.0);
			line += " " + tmpStr2;
			txt += tmpStr2;
			txt += "|";
			travel = atof(tmpStr2);			// stocker travel hours
			
			tmpStr2 = GetNext(tmpStr, "%10.2f");
			line += " " + tmpStr2;
			txt += tmpStr2;
			txt += "|";
			handling = atof(tmpStr2);		// stocker handling hours
			
			tmpStr2.Format("%10.2f", travel + handling);	
			line += " " + tmpStr2;			// total stocker time
			txt += tmpStr2;
			txt += "|";
			
			if (count > 0)
				tmpStr2.Format("%13.2f", distance / count);	
			else
				tmpStr2.Format("%13.2f", 0.0);
			line += " " + tmpStr2;			// avg travel dist
			txt += tmpStr2;
			txt += "|";
			
			if (count > 0)
				tmpStr2.Format("%13.5f", handling / count);
			else
				tmpStr2.Format("%13.5f", 0.0);
			line += " " + tmpStr2;			// avg insertion time
			txt += tmpStr2;
			txt += "|";
			
			if (count > 0)
				tmpStr2.Format("%13.5f", (travel + handling) / count);
			else
				tmpStr2.Format("%13.5f", 0.0);
			line += " " + tmpStr2;			// avg stocker time	
			txt += tmpStr2;
			txt += "|";
			
			if (travel+handling > 0)
				tmpStr2.Format("%15.2f", count / (travel + handling));
			else
				tmpStr2.Format("%15.2f", 0.0);
			line += " " + tmpStr2;			// units per hour
			txt += tmpStr2;
			txt += "|";
			
			tmpStr2 = GetNext(tmpStr, "%10.2f");
			line += " " + tmpStr2;	
			txt += tmpStr2;
			txt += "|";			
			cost = atof(tmpStr2);			// total stocker cost				k
			
			if (count > 0)
				tmpStr2.Format("%14.2f", cost / count);
			else
				tmpStr2.Format("%14.2f", 0.0);
			line += " " + tmpStr2;			// cost per unit			l
			txt += tmpStr2;
			txt += "|";
			
			stockerArray.Add(line);
			
			if (atoi(sectionID) > 0) {
				totalCost += cost;
				totalTime += travel + handling;
			}
			
			
			
			// pallet selects
			if (atoi(sectionID) > 0) {
				line = sectionID;
				line += "  " + section;
			}
			else {
				line = "Section Totals:    ";
			}
			
			tmpStr2 = GetNext(tmpStr, "%14.2f");
			line += " " + tmpStr2;
			txt += tmpStr2;
			txt += "|";
			count = atof(tmpStr2);			// pallet selects
			
			tmpStr2.Format("%13.2f", 0.0);
			line += " " + tmpStr2;
			txt += tmpStr2;
			txt += "|";
			distance = atof(tmpStr2);		// pallet selects distance	
			
			tmpStr2.Format("%10.2f", 0.0);
			line += " " + tmpStr2;
			txt += tmpStr2;
			txt += "|";
			travel = atof(tmpStr2);			// pallet selects travel hours
			
			tmpStr2 = GetNext(tmpStr, "%10.2f");
			line += " " + tmpStr2;
			txt += tmpStr2;
			txt += "|";
			handling = atof(tmpStr2);		// pallet selects handling hours
			
			tmpStr2.Format("%10.2f", travel + handling);	
			line += " " + tmpStr2;			// pallet selects  time
			txt += tmpStr2;
			txt += "|";
            
			if (count > 0)
				tmpStr2.Format("%13.2f", distance / count);	
			else
				tmpStr2.Format("%13.2f", 0.0);
			line += " " + tmpStr2;			// avg travel dist
			txt += tmpStr2;
			txt += "|";
			
			if (count > 0)
				tmpStr2.Format("%13.5f", handling / count);
			else
				tmpStr2.Format("%13.5f", 0.0);
			line += " " + tmpStr2;			// avg insertion time
			txt += tmpStr2;
			txt += "|";
			
			if (count > 0)
				tmpStr2.Format("%13.5f", (travel + handling) / count);
			else
				tmpStr2.Format("%13.5f", 0.0);
			line += " " + tmpStr2;			// avg pallet select time	
			txt += tmpStr2;
			txt += "|";
			
			if (travel+handling > 0)
				tmpStr2.Format("%15.2f", count / (travel + handling));
			else
				tmpStr2.Format("%15.2f", 0.0);
			line += " " + tmpStr2;			// units per hour
			txt += tmpStr2;
			txt += "|";
			
			tmpStr2 = GetNext(tmpStr, "%10.2f");
			line += " " + tmpStr2;	
			txt += tmpStr2;
			txt += "|";			
			cost = atof(tmpStr2);			// total pallet selects cost				k
			
			if (count > 0)
				tmpStr2.Format("%14.2f", cost / count);
			else
				tmpStr2.Format("%14.2f", 0.0);
			
			line += " " + tmpStr2;			// cost per unit			l
			txt += tmpStr2;
			txt += "|";
			
			psArray.Add(line);
			
			if (atoi(sectionID) > 0) {
				totalCost += cost;
				totalTime += travel + handling;
			}

			rep.m_txtArray.Add(txt);
		}
		
		else if (dataType == "F") {
			
			indx = tmpStr.Find("|");
			facility = tmpStr.Mid(0, indx);
			tmpStr = tmpStr.Mid(indx+1);
			
			rep.m_FacilityName = facility;
		}
		
	}	// end for
	
	//	tmpStr2.Format("%d", resultList.GetSize());
	//	line = "Total Records: " + tmpStr2;
	

	line = "<B>Putaway</B>";
	rep.AddBodyLine(line);
	line = "-------";
	rep.AddBodyLine(line);
	line = "                                           Total      Total      Total      Total       Average       Average       Average           Units                      Cost";
	rep.AddBodyLine(line);
	line = "                                          Travel     Travel  Insertion    Putaway        Travel     Insertion       Putaway             Per      Total            Per";
	rep.AddBodyLine(line);
	line = "Sect ID  Section          Putaways      Distance       Time       Time       Time      Distance          Time          Time            Hour       Cost           Unit";
	rep.AddBodyLine(line);	
	line = "-------  -------          --------      --------     ------  ---------    -------      --------     ---------       -------           -----      -----           ----";
	rep.AddBodyLine(line);	
	
	for (i=0;i<ptwyArray.GetSize();++i)
		rep.AddBodyLine(ptwyArray.GetAt(i));
	
	rep.AddBodyLine("");
	line = "<B>Replenishment</B>";
	rep.AddBodyLine(line);
	line = "-------------";
	rep.AddBodyLine(line);
	line = "                                           Total      Total      Total      Total       Average      Average        Average           Units                      Cost";
	rep.AddBodyLine(line);
	line = "                                          Travel     Travel  Insertion     Replen        Travel    Insertion         Replen             Per      Total            Per";
	rep.AddBodyLine(line);  
	line = "Sect ID  Section           Replens      Distance       Time       Time       Time      Distance         Time           Time            Hour       Cost           Unit";
	rep.AddBodyLine(line);
	line = "-------  -------           -------      --------     ------  ---------    -------      --------    ---------        -------           -----      -----           ----";
	rep.AddBodyLine(line);
	
	for (i=0;i<rplnArray.GetSize();++i)
		rep.AddBodyLine(rplnArray.GetAt(i));
	
	rep.AddBodyLine("");
	line = "<B>Selection Handling</B>";
	rep.AddBodyLine(line);
	line = "-------------";
	rep.AddBodyLine(line);
	line = "                                           Total      Total      Total      Total       Average      Average        Average           Units                      Cost";
	rep.AddBodyLine(line);
	line = "                                          Travel     Travel   Handling  Selection        Travel     Handling      Selection             Per      Total            Per";
	rep.AddBodyLine(line);
	line = "Sect ID  Section             Units      Distance       Time       Time       Time      Distance         Time           Time            Hour       Cost           Unit";
	rep.AddBodyLine(line);
	line = "-------  -------          --------      --------     ------  ---------    -------      --------    ---------        -------           -----      -----           ----";	
	rep.AddBodyLine(line);
	
	for (i=0;i<caseArray.GetSize();++i)
		rep.AddBodyLine(caseArray.GetAt(i));
	
	rep.AddBodyLine("");
	line = "<B>Stocker Handling</B>";
	rep.AddBodyLine(line);
	line = "-------------";
	rep.AddBodyLine(line);
	line = "                                           Total      Total      Total      Total       Average      Average        Average           Units                      Cost";
	rep.AddBodyLine(line);
	line = "                                          Travel     Travel   Handling    Stocker        Travel     Handling        Stocker             Per      Total            Per";
	rep.AddBodyLine(line);
	line = "Sect ID  Section             Cases      Distance       Time       Time       Time      Distance         Time           Time            Hour       Cost           Unit";
	rep.AddBodyLine(line);
	line = "-------  -------             -----      --------     ------  ---------    -------      --------    ---------        -------           -----      -----           ----";	
	rep.AddBodyLine(line);


	for (i=0;i<stockerArray.GetSize();++i)
		rep.AddBodyLine(stockerArray.GetAt(i));
	
	rep.AddBodyLine("");
	line = "<B>Full Pallet Selects</B>";
	rep.AddBodyLine(line);
	line = "-------------";
	rep.AddBodyLine(line);
	line = "                                           Total      Total      Total      Total       Average      Average        Average           Units                      Cost";
	rep.AddBodyLine(line);
	line = "                                          Travel     Travel   Handling  Selection        Travel     Handling      Selection             Per      Total            Per";
	rep.AddBodyLine(line);
	line = "Sect ID  Section           Pallets      Distance       Time       Time       Time      Distance         Time           Time            Hour       Cost           Unit";
	rep.AddBodyLine(line);
	line = "-------  -------           -------      --------     ------  ---------    -------      --------    ---------        -------           -----      -----           ----";	
	rep.AddBodyLine(line);
	
	for (i=0;i<psArray.GetSize();++i)
		rep.AddBodyLine(psArray.GetAt(i));
	
	
	rep.AddBodyLine("");
	rep.AddBodyLine("");
	rep.AddBodyLine("");
	
	line = "              Hours           Cost";
	rep.AddBodyLine(line);
	line = "              -----           ----";
	rep.AddBodyLine(line);
	
	line = "<B>Totals:</B>  ";
	tmpStr2.Format("%10.2f", totalTime);
	line += tmpStr2;
	tmpStr2.Format("%10.2f", totalCost);
	line += "     " + tmpStr2;
	rep.AddBodyLine(line);
	
	return 0;

}


void ParseString(const CString &string, const CString &delimiter, CStringArray &strings)
{
	int idx;
	CString temp = string;

	strings.RemoveAll();

	idx = temp.Find(delimiter);
	if (idx < 0) {
		strings.Add(temp);
		return;
	}

	while (idx >= 0) {
		strings.Add(temp.Left(idx));
		temp = temp.Mid(idx+delimiter.GetLength());
		idx = temp.Find(delimiter);
	}

	if (temp.GetLength() > 0)
		strings.Add(temp);

	return;

}

CString GetCurrentFacilityName()
{
	CString query;
	CStringArray results;
	int rc;

	query.Format("select description from dbfacility where dbfacilityid = %d", currentFacilityID);
	try {
		rc = ExecuteQuery("GetFacilityName", query, results);
	}
	catch (...) {
		rc = -1;
	}
	
	if (rc <= 0)
		return "Unknown";
	else {
		results[0].TrimRight("|");
		return results[0];
	}
		
}
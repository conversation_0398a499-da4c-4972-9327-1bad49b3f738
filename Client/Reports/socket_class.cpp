#include <stdio.h>
#include <stdlib.h>
#include "socket_class.h"
#include "debug.h"

void init_winsock()
{

	WORD wVerReq;
	WSADATA wsaData;
	int err;

	wVerReq = MAKEWORD(2,0);
	err = WSAStartup(wVerReq, &wsaData);
	if(err != 0){
		if (SLOT_DEBUG) printf("No usable WinSock DLL found\n");
		return;
	}

	if(LOBYTE(wsaData.wVersion) != 2 ||
		HIBYTE(wsaData.wVersion) != 0){
		WSACleanup();
		if (SLOT_DEBUG) printf("No usable WinSock DLL found\n");
		return;
	}
}

void fini_winsock()
{
	WSACleanup();
}

SockClass::SockClass()
{
	/* ***************************************************** */
	/* This version of the constructor will only be called   */
	/* by another socket class.  It will handle populating   */
	/* our data for us.                                      */
	/* ***************************************************** */

	memset(ReadCache, 0, sizeof(ReadCache));
	CachePtr = ReadCache;

}

/* ********************************************************* */
/* This constructor will generate a server style socket      */
/* on the local machine that will bind itself to the given   */
/* port and return.  After instantiating one of these, call  */
/* the Listen method to wait for a connection.               */
/* ********************************************************* */
SockClass::SockClass(int port)
{
	char hostname[128];
	struct hostent *this_host;
	int err;

	this_socket = socket(AF_INET, SOCK_STREAM, 0);
	if(this_socket == INVALID_SOCKET){
		if (SLOT_DEBUG) printf("Socket creation failed!\n");
		return;
	}

	/* ********************************************** */
	/* Get the local name and address of this machine */
	/* ********************************************** */
	err = gethostname(hostname, 128);
	if(err == SOCKET_ERROR){
		if (SLOT_DEBUG) printf("Error getting local host name\n");
		return;
	}
	this_host = gethostbyname(hostname);
	if(this_host == NULL){
		if (SLOT_DEBUG) printf("Error getting host information for %s\n", hostname);
		return;
	}

	this_addr.sin_family = AF_INET;
	this_addr.sin_port = htons(port);
	this_addr.sin_addr = *((IN_ADDR *)this_host->h_addr);

	if(bind(this_socket, (const struct sockaddr *)&this_addr, 
		sizeof(SOCKADDR)) == SOCKET_ERROR){
		if (SLOT_DEBUG) printf("Error binding to socket on port %d\n", port);
		return;
	}

	SocketType = SERVER;
	memset(ReadCache, 0, sizeof(ReadCache));
	CachePtr = ReadCache;
}

/* ********************************************************* */
/* This constructor will generate a client style socket      */
/* originating on the machine passed as a parameter on the   */
/* port passed as a parameter.                               */
/* ********************************************************* */
SockClass::SockClass(char *machine, int port)
{
	struct hostent *that_host;
	//int err;

	that_socket = socket(AF_INET, SOCK_STREAM, 0);
	if(that_socket == INVALID_SOCKET){
		if (SLOT_DEBUG) printf("Socket creation failed!\n");
		return;
	}

	/* ********************************************** */
	/* Get the local name and address of this machine */
	/* ********************************************** */
	that_host = gethostbyname(machine);
	if(that_host == NULL){
		if (SLOT_DEBUG) printf("Error getting host information for %s\n", machine);
		return;
	}

	that_addr.sin_family = AF_INET;
	that_addr.sin_port = htons(port);
	that_addr.sin_addr = *((IN_ADDR *)that_host->h_addr);
	
	/* ********************************************** */
	/* Now open the connection to that machine.       */
	/* ********************************************** */
	if(connect(that_socket, (struct sockaddr *)&that_addr, 
		sizeof(SOCKADDR)) == SOCKET_ERROR){
		if (SLOT_DEBUG) printf("Error connecting to %s on port %d\n", machine, port);
		return;
	}

	SocketType = CLIENT;
	memset(ReadCache, 0, sizeof(ReadCache));
	CachePtr = ReadCache;
}

SockClass::~SockClass()
{
	if(SocketType == SERVER)
		closesocket(this_socket);
	else
		closesocket(that_socket);
}

/* ******************************************************* */
/* This function assumes that we have already connected to */
/* or have accepted a connection from someone and are ready*/
/* to use the recv function on the socket.                 */
/* ******************************************************* */
int SockClass::GetData(char *buffer, int max)
{
	int cnt;
	char *ptr;
	int charsLeft;

	if(max>1024)
		max = 1024;

	//////////////////////////////////////////////////////////////////
	// At the end of the cache information.  This happens the first
	// time through and after the buffer has coincidentally ended at
	// the end of a line of data.
	//////////////////////////////////////////////////////////////////
	if(*CachePtr == '\0'){
		memset(ReadCache, 0, sizeof(ReadCache));

		if(SocketType == SERVER){
			cnt = recv(this_socket, ReadCache, max, 0);
			//if (SLOT_DEBUG) printf("rcv: %s\n", ReadCache);
		}
		else {
			cnt = recv(that_socket, ReadCache, max, 0);
			//if (SLOT_DEBUG) printf("rcv: %s\n", ReadCache);
		}

		if(cnt == SOCKET_ERROR){
			if (SLOT_DEBUG) printf("Error receiving data on the socket\n");
			return -1;
		}

		CachePtr = ReadCache;
	}

	//////////////////////////////////////////////////////////////////
	// Now we set the end pointer to the position of the next newline
	// character in the data.  If the last line of the buffer is a
	// broken line and there is no newline character, ptr is NULL.
	// This means we have to read in the next buffer full of data and
	// append the first line of it to what we already have.
	//////////////////////////////////////////////////////////////////
	ptr = strchr(CachePtr, '\n');
	if(ptr == NULL){				// Part of a line left
		// Avoid overflowing the buffer
		// This will only happen if the cache has exactly max chars
		// or they lower the max between calls
		if (max <= strlen(CachePtr)) {
			strncpy(buffer, CachePtr, max);
			CachePtr = CachePtr + max;
			// We've filled up the buffer so return
			return strlen(buffer);
		}
		else {	// copy the entire cache
			strcpy(buffer, CachePtr);
		}		

		memset(ReadCache, 0, sizeof(ReadCache));	// Refresh the buffer and read more

		if(SocketType == SERVER){
			cnt = recv(this_socket, ReadCache, max, 0);
			//if (SLOT_DEBUG) printf("rcv: %s", ReadCache);
		}
		else {
			cnt = recv(that_socket, ReadCache, max, 0); 
			//if (SLOT_DEBUG) printf("rcv: %s", ReadCache);
		}

		if(cnt == SOCKET_ERROR){
			if (SLOT_DEBUG) printf("Error receiving data on the socket\n");
			return -1;
		}

		CachePtr = ReadCache;			// Reset the first pointer
		ptr = strchr(CachePtr, '\n');	// Reset the end pointer

		while ( ptr == NULL ) {
			// Avoid overflowing the buffer
			// If the number of chars left in the buffer is less than
			// the length of the cache, only copy that many characters and return
			charsLeft = max - strlen(buffer);
			if (charsLeft <= strlen(CachePtr)) {
				strncpy((buffer + strlen(buffer)), CachePtr, charsLeft);
				CachePtr = CachePtr + charsLeft;
				// We've filled up the buffer so return
				return strlen(buffer);
			}
			else {		// copy the entire cache since it fits
				strcat(buffer, CachePtr);
			}

			
			memset(ReadCache, 0, sizeof(ReadCache));	// Refresh the buffer and read more

			if(SocketType == SERVER){
				cnt = recv(this_socket, ReadCache, max, 0);
				//if (SLOT_DEBUG) printf("rcv: %s", ReadCache);
			}
			else {
				cnt = recv(that_socket, ReadCache, max, 0); 
				//if (SLOT_DEBUG) printf("rcv: %s", ReadCache);
			}

			if(cnt == SOCKET_ERROR){
				if (SLOT_DEBUG) printf("Error receiving data on the socket\n");
				return -1;
			}

			CachePtr = ReadCache;			// Reset the first pointer
			ptr = strchr(CachePtr, '\n');	// Reset the end pointer
		}		
		
		// Add the rest of the line to what's already in the buffer
		// Move the end pointer just past the newline
		ptr++;

		// Avoid overflowing the buffer
		// If the characters up to the newline won't fit in the buffer,
		// only copy enough to fill the buffer and leave the start 
		// pointer (CachePtr) where we left off; otherwise copy everything
		// up to the newline and leave the start pointer one position
		// after the newline
		charsLeft = max - strlen(buffer);
		if (charsLeft <= (ptr-CachePtr)) {	// Only fill buffer to max
			strncpy((buffer + strlen(buffer)), CachePtr, charsLeft);
			// Move the first pointer to where we left off
			CachePtr = CachePtr + charsLeft;
			cnt = strlen(buffer);
		}
		else {		
			strncpy((buffer + strlen(buffer)), CachePtr, ptr-CachePtr);
			// Move the first pointer up as usual and set the byte
			// counter = buffer size.
			CachePtr = ptr;
			cnt = strlen(buffer);
		}
	}
	else {
		// Move the end pointer from the newline to what will be the
		// beginning of the next line and copy the current line into
		// the buffer.  Move the start pointer to the new starting
		// position and set cnt to return the number of bytes in the
		// line.
		ptr++;
		strncpy(buffer, CachePtr, ptr-CachePtr);
		CachePtr = ptr;
		cnt = strlen(buffer);
	}

	return cnt;
}

//////////////////////////////////////////////////////////////////
// Using the appropriate socket for client or server, send bytes
// through until all have been sent.  Normally, send() will return
// *size*, exiting the loop after one pass.  Otherwise, *cnt* is
// added to the buffer pointer so that the first bytes are not re-
// sent.
//////////////////////////////////////////////////////////////////
int SockClass::SendData(char *buffer, int size)
{
	int cnt, err;

	cnt = 0;
	if(SocketType == SERVER)
		do {
			//if (SLOT_DEBUG) printf("snd: %s", buffer+cnt);
			err = send(this_socket, buffer+cnt, size, 0);
			if(err == SOCKET_ERROR){
				PrintError(GetLastError());
				return -1;
			}
			cnt += err;
		} while (cnt < size);
	else
		do {
			//if (SLOT_DEBUG) printf("snd: %s", buffer+cnt);
			err = send(that_socket, buffer+cnt, size, 0);
			if(err == SOCKET_ERROR){
				PrintError(GetLastError());
				return -1;
			}
			cnt += err;
		} while (cnt < size);

	return size;
}


/* ********************************************************* */
/* This method is only valid for Server sockets.  It will    */
/* sit and listen on the socket for a connection.  Once a    */
/* Connection is detected, it will use the accept function   */
/* to establish a new socket, fill out a new Server socket   */
/* class with the new socket information and return the new  */
/* socket class.  The new class may then be used to send and */
/* receive data.                                             */
/* ********************************************************* */

SockClass *SockClass::Listen()
{
	int err;
	SOCKET new_sock;
	SOCKADDR_IN new_addr;
	SockClass *tmp;
	
	if(SocketType != SERVER)
	{
		if (SLOT_DEBUG) printf("Listen: Not a server socket\n");
		return NULL;
	}

	err = listen(this_socket, 5);
	if(err == SOCKET_ERROR){
		if (SLOT_DEBUG) printf("Error listening on socket\n");
		return NULL;
	}

	new_sock = accept(this_socket, (struct sockaddr *)&new_addr, NULL);
	if(new_sock == INVALID_SOCKET){
		if (SLOT_DEBUG) printf("Error during accept of connection\n");
		return NULL;
	}

	tmp = new SockClass();

	tmp->this_socket = new_sock;
	memcpy(&(tmp->this_addr), &this_addr, sizeof(SOCKADDR_IN)); 
	tmp->SocketType = SERVER;

	return tmp;
}

void SockClass::PrintError(int err)
{
	switch (err) {
	case WSANOTINITIALISED: if (SLOT_DEBUG) printf("A successful AfxSocketInit must occur before using this API."); break;
	case WSAENETDOWN: if (SLOT_DEBUG) printf("The Windows Sockets implementation detected that the network subsystem failed."); break;
	case WSAEACCES: if (SLOT_DEBUG) printf("The requested address is a broadcast address, but the appropriate flag was not set."); break;
	case WSAEINPROGRESS: if (SLOT_DEBUG) printf("A blocking Windows Sockets operation is in progress."); break;
	case WSAEFAULT: if (SLOT_DEBUG) printf("The lpBuf argument is not in a valid part of the user address space."); break;
	case WSAENETRESET: if (SLOT_DEBUG) printf("The connection must be reset because the Windows Sockets implementation dropped it."); break;
	case WSAENOBUFS: if (SLOT_DEBUG) printf("The Windows Sockets implementation reports a buffer deadlock."); break;
	case WSAENOTCONN: if (SLOT_DEBUG) printf("The socket is not connected."); break;
	case WSAENOTSOCK: if (SLOT_DEBUG) printf("The descriptor is not a socket."); break;
	case WSAEOPNOTSUPP: if (SLOT_DEBUG) printf("MSG_OOB was specified, but the socket is not of type SOCK_STREAM."); break;
	case WSAESHUTDOWN: if (SLOT_DEBUG) printf("The socket has been shut down; it is not possible to call Send on a socket after ShutDown has been invoked with nHow set to 1 or 2."); break;
	case WSAEWOULDBLOCK: if (SLOT_DEBUG) printf("The socket is marked as nonblocking and the requested operation would block."); break;
	case WSAEMSGSIZE: if (SLOT_DEBUG) printf("The socket is of type SOCK_DGRAM, and the datagram is larger than the maximum supported by the Windows Sockets implementation."); break;
	case WSAEINVAL: if (SLOT_DEBUG) printf("The socket has not been bound with Bind."); break;
	case WSAECONNABORTED: if (SLOT_DEBUG) printf("The virtual circuit was aborted due to timeout or other failure."); break;
	case WSAECONNRESET: if (SLOT_DEBUG) printf("The virtual circuit was reset by the remote side. "); break;
	}
}













	


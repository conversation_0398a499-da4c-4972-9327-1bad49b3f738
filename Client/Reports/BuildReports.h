

void InsertPageNumber(CString &str, int pageNum);

extern int BuildRackAssignmentResultsRep(CSUCCReport &rep);
extern int BuildRackAssignmentDetailRep(CSUCCReport &rep);
extern int BuildProductGroupDefineRep(CSUCCReport &rep, int sortFlag);
extern int BuildProductGroupLayoutRep(CSUCCReport &rep);
extern int BuildProductGroupFacingsRep(CSUCCReport &rep);
extern int BuildProductLayoutRep(CSUCCReport &rep);
extern int BuildProductLayoutSortByProduct(CSUCCReport &rep);
extern int BuildProductLayoutSortByLocation(CSUCCReport &rep);
extern int BuildProductsLayoutVarWidthLocRep(CSUCCReport &rep);
extern int BuildProductsLayoutCaseReOrientRep(CSUCCReport &rep);
extern int BuildFacilityMoveChainsRep(CSUCCReport &rep);
extern int BuildLocationOutboundRep(CSUCCReport &rep);
extern int BuildAssignmentOutboundRep(CSUCCReport &rep);

extern int BuildProductDetailRep(CSUCCReport &rep);
extern int BuildCostAnalysisDetRep(CSUCCReport &rep);
extern int BuildCostAnalysisSumRep(CSUCCReport &rep);
extern int BuildUnassignedProduct(CSUCCReport &rep);
extern int BuildRackUsageSummary(CSUCCReport &rep);
extern int BuildCapitalCostRejection(CSUCCReport &rep);


CString GetNext(CString &retString, CString &line, CString format);

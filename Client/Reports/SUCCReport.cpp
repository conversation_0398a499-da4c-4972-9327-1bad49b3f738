
#include "SUCCReport.h"


int CSUCCReport::GetReportSize()
{
	int sz, pages;

	sz = m_header.GetSize() + m_body.GetSize() + m_footer.GetSize() +
		m_firstPage.GetSize() + m_lastPage.GetSize();

	pages = m_body.GetSize()/(m_nLinesPerPage - GetHeaderSize()) + 1;
	sz = (pages * GetHeaderSize()) + m_body.GetSize();

	return sz;
}

int CSUCCReport::GetHeaderSize()
{
	// add 6 because we generate an extra six lines of header per page
	return m_header.GetSize() + 6;
}


int CSUCCReport::GetFooterSize()
{
	return m_footer.GetSize();
}

int CSUCCReport::GetBodySize()
{
	return m_body.GetSize();
}

int CSUCCReport::GetDataSize()
{
	return m_data.GetSize();
}

void CSUCCReport::GetHeaderLineAt(CString &s, int indx)
{

	if (indx > (m_header.GetSize()-1))
		s = "";
	else
		s = m_header.GetAt(indx);

}


void CSUCCReport::GetFooterLineAt(CString &s, int indx)
{

	if (indx > (m_footer.GetSize()-1))
		s = "";
	else
		s = m_footer.GetAt(indx);

}

void CSUCCReport::GetBodyLineAt(CString &s, int indx)
{

	if (indx > (m_body.GetSize()-1))
		s = "";
	else
		s = m_body.GetAt(indx);

}

void CSUCCReport::GetDataLineAt(CString &s, int indx)
{

	if (indx > (m_data.GetSize()-1))
		s = "";
	else
		s = m_data.GetAt(indx);

}

void CSUCCReport::AddHeaderLine(CString s)
{
	m_header.Add(s);
}


void CSUCCReport::AddFooterLine(CString s)
{
	m_footer.Add(s);
}


void CSUCCReport::AddBodyLine(CString s)
{
	m_body.Add(s);
}

void CSUCCReport::AddDataLine(CString s)
{
	m_data.Add(s);
}

void CSUCCReport::AddHeaderLineCntr(CString s)
{
	int l;
	int indx;
	int n;
	CString str;

	l = s.GetLength();
	n = m_nCharactersPerLine;

	if ( ((l % 2) != 0) && ((n % 2) != 0) )
		indx = ((n - l) / 2 ) + 1;
	else
		indx = (n - l) / 2;

	str = "";
	for (int i = 0; i < indx; i++)
		str += " ";

	str += s;

	AddHeaderLine(str);
}


CSUCCReport& CSUCCReport::operator=(CSUCCReport &other)
{
	CStringArray h;
	CStringArray f;
	CStringArray b;
	CStringArray d;
	CString t;

	h.Copy(other.GetHeader());
	f.Copy(other.GetFooter());
	b.Copy(other.GetBody());
	d.Copy(other.GetData());
	t = other.GetTitle();

	this->m_nLinesPerPage      = other.m_nLinesPerPage;
	this->m_nCharactersPerLine = other.m_nCharactersPerLine;
	this->m_paperSizeLength    = other.m_paperSizeLength;
	this->m_paperSizeWidth     = other.m_paperSizeWidth;
	this->m_reportType         = other.m_reportType;
	this->m_ReportName = other.m_ReportName;
	this->m_FacilityName = other.m_FacilityName;

	this->SetHeader(h);
	this->SetFooter(f);
	this->SetBody(b); 
	this->SetData(d);
	this->SetTitle(t);

	this->m_txtArray.Copy(other.m_txtArray);
	this->m_columnCount = other.m_columnCount;

	return *this;
}
	
	

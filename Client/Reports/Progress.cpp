// Progress.cpp : implementation file
//

#include "stdafx.h"
#include "SucceedReports.h"
#include "Progress.h"

#ifdef _DEBUG
#define new DEBUG_NEW
#undef THIS_FILE
static char THIS_FILE[] = __FILE__;
#endif

/////////////////////////////////////////////////////////////////////////////
// Progress dialog


Progress::Progress(CWnd* pParent /*=NULL*/)
	: CDialog(Progress::IDD, pParent)
{
	//{{AFX_DATA_INIT(Progress)
	//}}AFX_DATA_INIT
}


void Progress::DoDataExchange(CDataExchange* pDX)
{
	CDialog::DoDataExchange(pDX);
	//{{AFX_DATA_MAP(Progress)
	//}}AFX_DATA_MAP
}


BEGIN_MESSAGE_MAP(Progress, CDialog)
	//{{AFX_MSG_MAP(Progress)
		// NOTE: the ClassWizard will add message map macros here
	//}}AFX_MSG_MAP
END_MESSAGE_MAP()

/////////////////////////////////////////////////////////////////////////////
// Progress message handlers

void Progress::PostNcDestroy() 
{
	delete this;
}

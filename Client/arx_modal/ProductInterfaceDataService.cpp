// ProductInterfaceDataService.cpp: implementation of the CProductInterfaceDataService class.
//
//////////////////////////////////////////////////////////////////////

#include "stdafx.h"
#include "modal.h"
#include "ProductInterfaceDataService.h"
#include "SSACStringArray.h"
#include "ForteService.h"

#ifdef _DEBUG
#undef THIS_FILE
static char THIS_FILE[]=__FILE__;
#define new DEBUG_NEW
#endif

//////////////////////////////////////////////////////////////////////
// Construction/Destruction
//////////////////////////////////////////////////////////////////////

CProductInterfaceDataService::CProductInterfaceDataService()
{

}

CProductInterfaceDataService::~CProductInterfaceDataService()
{

}


int CProductInterfaceDataService::ProductInbound(CStringArray &records) 
{
	CString tempString;
	int tempInt;
	CSsaStringArray tempSendArray;
	CSsaStringArray tempRecvArray;
	CString sendString;
	int i = 0;
	CForteService forteService;

	/////////////////////////////////////////////////////////////
	// Build the data and send to Forte connection
	/////////////////////////////////////////////////////////////
	sendString = "<SIO>SLOTSocketString\n";
	tempSendArray.Add(sendString);
	sendString = "<SAI>ProductInbound\n";
	tempSendArray.Add(sendString);
	sendString = "<EIO>SLOTSocketString\n";
	tempSendArray.Add(sendString);

	try {
		forteService.SendToForteConnection(tempSendArray,tempRecvArray,
			CString("SLOTSocketString"), 6030);
	}
	catch(...) {
		return -1;
	}

	for (i=0; i < tempRecvArray.GetSize(); i++) {
		if ( tempRecvArray[i].Find("<SAI>") != -1 ) {
			tempString = tempRecvArray[i].Mid(5);
			tempInt = tempString.GetLength();
			if (tempString.GetAt(tempInt-1) == '\n' ) {
				tempString.SetAt(tempInt-1,' ');
				tempString.TrimRight();
			}
			records.Add(tempString);
		}
	}

	return 0;
}

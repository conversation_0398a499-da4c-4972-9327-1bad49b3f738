#if !defined(AFX_CHOOSEAISLEPROFILEDIALOG_H__BFA08BB1_FFBA_11D1_9BCC_0080C781D9DF__INCLUDED_)
#define AFX_CHOOSEAISLEPROFILEDIALOG_H__BFA08BB1_FFBA_11D1_9BCC_0080C781D9DF__INCLUDED_

#if _MSC_VER >= 1000
#pragma once
#endif // _MSC_VER >= 1000
// ChooseAisleProfileDialog.h : header file
//
#include "SSACStringArray.h"

/////////////////////////////////////////////////////////////////////////////
// CChooseAisleProfileDialog dialog

class CChooseAisleProfileDialog : public CDialog
{
// Construction
public:
	CString GetAisleName();
	CChooseAisleProfileDialog(CWnd* pParent = NULL);   // standard constructor
	CSsaStringArray m_SectionNameList;

// Dialog Data
	//{{AFX_DATA(CChooseAisleProfileDialog)
	enum { IDD = IDD_CHOOSEAISLE_DIALOG };
	CEdit	m_ChooseAisle_NumAislesBox;
	CComboBox	m_ChooseAisle_SectionChoice;
	CTreeCtrl	m_ChooseAisle_Tree;
	CString	m_ChooseAisle_SlotDir;
	CString	m_ChooseAisle_AisleName;
	int		m_ChooseAisle_SectionVal;
	int		m_ChooseAisle_NumAisles;
	//}}AFX_DATA

// Overrides
	// ClassWizard generated virtual function overrides
	//{{AFX_VIRTUAL(CChooseAisleProfileDialog)
	protected:
	virtual void DoDataExchange(CDataExchange* pDX);    // DDX/DDV support
	//}}AFX_VIRTUAL
	CImageList m_ImageList;

// Implementation
protected:

	// Generated message map functions
	//{{AFX_MSG(CChooseAisleProfileDialog)
	afx_msg void OnOK();
	afx_msg void OnHelp();
	afx_msg void OnDblclkChooseAisleTree(NMHDR* pNMHDR, LRESULT* pResult);
	virtual BOOL OnInitDialog();
	afx_msg BOOL OnHelpInfo(HELPINFO* pHelpInfo);
	//}}AFX_MSG
	DECLARE_MESSAGE_MAP()

private:
	CStringArray m_AisleProfileNameList;
};

//{{AFX_INSERT_LOCATION}}
// Microsoft Developer Studio will insert additional declarations immediately before the previous line.

#endif // !defined(AFX_CHOOSEAISLEPROFILEDIALOG_H__BFA08BB1_FFBA_11D1_9BCC_0080C781D9DF__INCLUDED_)

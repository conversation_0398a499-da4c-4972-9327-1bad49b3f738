// UtilityCommands.cpp: implementation of the CUtilityCommands class.
//
//////////////////////////////////////////////////////////////////////

#include "stdafx.h"
#include "modal.h"
#include "UtilityCommands.h"
#include "AutoCADCommands.h"
#include "UtilityHelper.h"
#include "ControlService.h"
#include "ExportFacility.h"

#include <aced.h>
#include <adscodes.h>

#ifdef _DEBUG
#undef THIS_FILE
static char THIS_FILE[]=__FILE__;
#define new DEBUG_NEW
#endif

extern CControlService controlService;

//////////////////////////////////////////////////////////////////////
// Construction/Destruction
//////////////////////////////////////////////////////////////////////

CUtilityCommands::CUtilityCommands()
{

}

CUtilityCommands::~CUtilityCommands()
{

}

void CUtilityCommands::RegisterCommands()
{
	acedRegCmds->addCommand( "SLOTFAC", "VALIDATEFACILITY", "VALIDATEFACILITY",
		ACRX_CMD_MODAL, &CUtilityCommands::ValidateFacility );
	acedRegCmds->addCommand( "SLOTFAC", "LISTHANDLES", "LISTHANDLES",
		ACRX_CMD_MODAL, &CUtilityCommands::ListHandles );
	acedRegCmds->addCommand( "SLOTGEN", "USERQUERY", "USERQUERY",
		ACRX_CMD_MODAL, &CUtilityCommands::UserQuery );
	acedRegCmds->addCommand( "SLOTFAC", "DUMPTREE", "DUMPTREE",
		ACRX_CMD_MODAL, &CUtilityCommands::DumpFacilityTree );
	acedRegCmds->addCommand( "SLOTFAC", "ZOOMBYHANDLE", "ZOOMBYHANDLE",
		ACRX_CMD_MODAL, &CUtilityCommands::ZoomByHandle );
	acedRegCmds->addCommand( "SLOTFAC", "CLEANFACILITY", "CLEANFACILITY",
		ACRX_CMD_MODAL, &CUtilityCommands::CleanFacility );
	acedRegCmds->addCommand( "SLOTFAC", "CHECKFACILITY", "CHECKFACILITY",
		ACRX_CMD_MODAL, &CUtilityCommands::CleanFacility );
	acedRegCmds->addCommand( "SLOTFAC", "CONVERTFACILITY", "CONVERTFACILITY",
		ACRX_CMD_MODAL, &CUtilityCommands::ConvertFacility );
	acedRegCmds->addCommand( "SLOTFAC", "EXPORTDATABASE", "EXPORTDATABASE",
		ACRX_CMD_MODAL, &CUtilityCommands::ExportDatabase );
	
	acedRegCmds->addCommand( "SLOTFAC", "COLORDISCONNECTED", "COLORDISCONNECTED",
		ACRX_CMD_MODAL, &CUtilityCommands::ColorDisconnectedPickpaths);

}

void CUtilityCommands::ZoomByHandle() 
{
	if (! controlService.ValidateCommand(CCommands::newFacilityMode))
		return;

	CUtilityHelper helper;

	helper.ZoomByHandle();

	return;
}

void CUtilityCommands::ValidateFacility()
{ 
	if (! controlService.ValidateCommand(CCommands::savedFacilityMode))
		return;

	CUtilityHelper helper;

	helper.ValidateFacility();

	return;

}

void CUtilityCommands::ListHandles()
{
	if (! controlService.ValidateCommand(CCommands::savedFacilityMode))
		return;

	CUtilityHelper helper;

	helper.ListHandles();

	return;

}

void CUtilityCommands::UserQuery()
{
	if (! controlService.ValidateCommand(CCommands::anyMode))
		return;

	CUtilityHelper helper;

	helper.UserQuery();

	return;

}

void CUtilityCommands::DumpFacilityTree()
{
	if (! controlService.ValidateCommand(CCommands::newFacilityMode))
		return;

	CUtilityHelper helper;

	helper.DumpFacilityTree();

	return;

}


void CUtilityCommands::CleanFacility()
{
	if (! controlService.ValidateCommand(CCommands::savedFacilityMode))
		return;

	CUtilityHelper helper;

	helper.CleanFacility();

	return;
}

void CUtilityCommands::ConvertFacility()
{
	if (! controlService.ValidateCommand(CCommands::savedFacilityMode))
		return;

	CUtilityHelper helper;

	helper.ConvertDrawing();

	return;
}

void CUtilityCommands::ColorDisconnectedPickpaths()
{
	if (! controlService.ValidateCommand(CCommands::openFacilityMode))
		return;

	CUtilityHelper helper;

	helper.ColorDisconnectedPickpaths();

	return;
}

void CUtilityCommands::ExportDatabase()
{

	CExportFacility cdlg;

	cdlg.DoModal();
	
	//system("cmd.exe /c exp80 exeslot/slotting@Opti file=export.dmp owner=exeslot");

}


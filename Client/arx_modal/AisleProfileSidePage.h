#if !defined(AFX_AisleProfileSidePage_H__D37F5667_599F_47AE_B109_E384BB7F0A57__INCLUDED_)
#define AFX_AisleProfileSidePage_H__D37F5667_599F_47AE_B109_E384BB7F0A57__INCLUDED_
#include "AisleProfile.h"

#if _MSC_VER > 1000
#pragma once
#endif // _MSC_VER > 1000
// AisleProfileSidePage.h : header file
//

/////////////////////////////////////////////////////////////////////////////
// CAisleProfileSidePage dialog

class CAisleProfileSidePage : public CPropertyPage
{
	DECLARE_DYNCREATE(CAisleProfileSidePage)

// Construction
public:
	CAisleProfileSidePage();
	~CAisleProfileSidePage();

// Dialog Data
	//{{AFX_DATA(CAisleProfileSidePage)
	enum { IDD = IDD_AISLE_PROFILE_SIDE_PAGE };
	CListBox	m_RightSideListCtrl;
	CListBox	m_LeftSideListCtrl;
	CString	m_Name;
	//}}AFX_DATA


// Overrides
	// ClassWizard generate virtual function overrides
	//{{AFX_VIRTUAL(CAisleProfileSidePage)
	public:
	virtual BOOL OnSetActive();
	virtual BOOL OnKillActive();
	protected:
	virtual void DoDataExchange(CDataExchange* pDX);    // DDX/DDV support
	virtual BOOL OnNotify(WPARAM wParam, LPARAM lParam, LRESULT* pResult);
	//}}AFX_VIRTUAL

// Implementation
protected:
	// Generated message map functions
	//{{AFX_MSG(CAisleProfileSidePage)
	virtual BOOL OnInitDialog();
	afx_msg void OnDblclkLeftSideList();
	afx_msg void OnDblclkRightSideList();
	afx_msg void OnContextMenu(CWnd* pWnd, CPoint point);
	afx_msg BOOL OnHelpInfo(HELPINFO* pHelpInfo);
	//}}AFX_MSG
	DECLARE_MESSAGE_MAP()

private:
	CAisleProfile *m_pAisleProfile;
};

//{{AFX_INSERT_LOCATION}}
// Microsoft Visual C++ will insert additional declarations immediately before the previous line.

#endif // !defined(AFX_AisleProfileSidePage_H__D37F5667_599F_47AE_B109_E384BB7F0A57__INCLUDED_)

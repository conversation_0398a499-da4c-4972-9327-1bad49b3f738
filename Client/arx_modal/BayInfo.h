// BayInfo.h: interface for the CBayInfo class.
//
//////////////////////////////////////////////////////////////////////

#if !defined(AFX_BAYINFO_H__556897A9_F5D9_47F1_BEE4_2E2990BFBD7E__INCLUDED_)
#define AFX_BAYINFO_H__556897A9_F5D9_47F1_BEE4_2E2990BFBD7E__INCLUDED_

#include "BayLevelInfo.h"
#include "TreeElement.h"

#if _MSC_VER > 1000
#pragma once
#endif // _MSC_VER > 1000

class CBayInfo : public CObject  
{
public:
	CBayInfo();
	virtual ~CBayInfo();

	double m_UprightHeight;
	double m_Height;
	double m_Width;
	
	CTypedPtrArray<CObArray, CBayLevelInfo*> m_LevelList;

	int m_BayProfileDBId;

	TreeElement *m_pBayTreeElement;
	BOOL m_IsRotated;			// true if the bay is on the opposite side of the aisle
	int m_PickPathDirection;	// 0 means from the start of the aisle, 1 means from the end of the aisle
	int m_PickPathType;			// see CPickPath for enum
};

#endif // !defined(AFX_BAYINFO_H__556897A9_F5D9_47F1_BEE4_2E2990BFBD7E__INCLUDED_)

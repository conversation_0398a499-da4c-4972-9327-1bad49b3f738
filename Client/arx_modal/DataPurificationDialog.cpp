// DataPurificationDialog.cpp : implementation file
//

#include "stdafx.h"
#include <afxmt.h>
#include "DataPurificationDialog.h"
#include "ssa_exception.h"
#include "ProductAttribute.h"
#include "qqhclasses.h"
#include "ProductMaintenance.h"
#include "UDFPage.h"
#include "ResourceHelper.h"
#include "Progress.h"
#include "ProductGroupDataService.h"
#include "ProductDataService.h"
#include "UtilityHelper.h"
#include "HelpService.h"
#include "DataAccessService.h"

#include <dbsymtb.h>
#ifdef _DEBUG
#define new DEBUG_NEW
#undef THIS_FILE
static char THIS_FILE[] = __FILE__;
#endif

extern CHelpService helpService;
extern CUtilityHelper utilityHelper;
/////////////////////////////////////////////////////////////////////////////
// CDataPurificationDialog dialog

extern CEvent g_ThreadDone;

extern FILE *flog;

CDataPurificationDialog::CDataPurificationDialog(CWnd* pParent /*=NULL*/)
	: CDialog(CDataPurificationDialog::IDD, pParent)
{
	//{{AFX_DATA_INIT(CDataPurificationDialog)	
	//}}AFX_DATA_INIT
}

CDataPurificationDialog::~CDataPurificationDialog()
{
	for (int i=0; i < m_Products.GetSize(); ++i)
		delete m_Products[i];
}

void CDataPurificationDialog::DoDataExchange(CDataExchange* pDX)
{
	CDialog::DoDataExchange(pDX);
	//{{AFX_DATA_MAP(CDataPurificationDialog)
	DDX_Control(pDX, IDVIEW, m_ViewButton);
	DDX_Control(pDX, IDC_DATA_GRID, m_DataGrid);
	//}}AFX_DATA_MAP
}


BEGIN_MESSAGE_MAP(CDataPurificationDialog, CDialog)
	//{{AFX_MSG_MAP(CDataPurificationDialog)
	ON_BN_CLICKED(IDVIEW, OnView)
	ON_BN_CLICKED(IDRESET, OnReset)
	ON_MESSAGE(WM_DISPLAY_RESULTS_BUTTON1, (LRESULT (AFX_MSG_CALL CWnd::*)(WPARAM, LPARAM))OnRunPurification)
	ON_MESSAGE(WM_DISPLAY_RESULTS_BUTTON2, (LRESULT (AFX_MSG_CALL CWnd::*)(WPARAM, LPARAM))OnProductMaintenance)
	ON_MESSAGE(WM_DISPLAY_RESULTS_BUTTON3, (LRESULT (AFX_MSG_CALL CWnd::*)(WPARAM, LPARAM))OnDeleteProducts)
	ON_MESSAGE(WM_CLOSEDISPLAY, (LRESULT (AFX_MSG_CALL CWnd::*)(WPARAM, LPARAM))OnCloseDisplay)
	ON_BN_CLICKED(IDHELP, OnHelp)
	ON_WM_HELPINFO()
	//}}AFX_MSG_MAP
END_MESSAGE_MAP()

/////////////////////////////////////////////////////////////////////////////
// CDataPurificationDialog message handlers

BOOL CDataPurificationDialog::OnInitDialog() 
{
	CDialog::OnInitDialog();
	CComboBox *pProductGroupBox = (CComboBox *)GetDlgItem(IDC_PRODUCT_GROUP_LIST);
	CButton *pButton = (CButton *)GetDlgItem(IDC_BOTH);
	CProductGroup *pProductGroup;
	CRect r;
	ProductGroupArrayType pgList;
	CProductGroupDataService productGroupDataService;

	productGroupDataService.LoadProductGroups(pgList);

	for (int i=0; i < pgList.GetSize(); ++i) {
		pProductGroup = pgList[i];
		int n = pProductGroupBox->AddString(pProductGroup->m_Description);
		pProductGroupBox->SetItemData(n, pProductGroup->m_ProductGroupDBID);
		delete pProductGroup;
	}

	pProductGroupBox->SetItemHeight(0, 2000);
	pProductGroupBox->GetWindowRect(&r);
	pProductGroupBox->SetWindowPos(NULL, 0, 0, r.Width(), r.Height()*10, SWP_NOMOVE|SWP_NOZORDER);
	
	pButton->SetCheck(1);

	LoadGrid();

	UpdateData(FALSE);

	return TRUE;  // return TRUE unless you set the focus to a control
	              // EXCEPTION: OCX Property Pages should return FALSE
}

void CDataPurificationDialog::LoadGrid()
{

	CDataGridAttribute *pAttr;
	CProductAttribute *pProdAttr;
	long row, col;
	CStringArray attributes;
	CString attribute;
	CString;
	int rc;

	try {
		rc = m_ProductDataService.LoadProductAttributes();
	}
	catch(Ssa_Exception e) {
		utilityHelper.ProcessError("Error getting product attributes.", &e);
		return;
	}
	catch(...) {
		utilityHelper.ProcessError("Error getting product attributes.");
		return;
	}

	m_DataGrid.SetRows(m_ProductDataService.m_ProductAttributeList.GetSize()+1);
	m_DataGrid.SetCols(6);
	m_DataGrid.m_MainHelpTopic = "DataPurification_DataGrid";

	row = m_DataGrid.GetRows();
	col = m_DataGrid.GetCols();

	m_DataGrid.SetFixedRows(1);
	m_DataGrid.SetFixedCols(2);

	row = m_DataGrid.GetFixedRows();
	col = m_DataGrid.GetFixedCols();


	// First add all attributes to the grid
	for (row=0; row < m_DataGrid.GetRows(); ++row) {
		for (col=0; col < m_DataGrid.GetCols(); ++col) {	
			pAttr = new CDataGridAttribute;
			if (row < m_DataGrid.GetFixedRows() || col < m_DataGrid.GetFixedCols()) {
				pAttr->m_Type = AT_FIXED;
			}
			else {
				pAttr->m_Type = AT_VAR;
			}
			m_DataGrid.m_DataGridAttributes.Add(pAttr);
		}
	}

	

	int i;
	// Load the column attributes from the hard-coded values
	for (i=0; i < m_DataGrid.GetCols(); ++i) {
		pAttr = m_DataGrid.m_DataGridAttributes[i];
		pAttr->m_InitialValue = m_DataGrid.GetTextMatrix(0, i);
		pAttr->m_DataType = DT_NONE;
		pAttr->m_Value = m_DataGrid.GetTextMatrix(0, i);
		pAttr->m_AttributeID = 0;
	}

	row = 0;

	for (i=0; i < m_ProductDataService.m_ProductAttributeList.GetSize(); ++i) {
		pProdAttr = m_ProductDataService.m_ProductAttributeList[i];
		// SetAttribute(name, type, min, max, initial, list)
		SetAttribute(++row, pProdAttr->m_Name, pProdAttr->m_Type, pProdAttr->m_MinimumValue, 
			pProdAttr->m_MaximumValue, pProdAttr->m_Initial, pProdAttr->m_ListValues, 
			pProdAttr->m_AttributeDBID, pProdAttr->m_HelpTopic);
	}
	
	m_DataGrid.LoadAttributes();

	m_DataGrid.m_LeaveCellFunction = CDataPurificationDialog::OnLeaveCellDataGrid;

	return;

}

void CDataPurificationDialog::SetAttribute(int row, CString name, int type, double min, double max, 
									CString initial, CStringArray &listValues, int attributeID,
									CString helpTopic)
{
	BOOL found = FALSE;
	CDataGridAttribute *pAttr;
	int idx, i;

	// Add the name to the 1st column
	idx = row * m_DataGrid.GetCols();
	pAttr = m_DataGrid.m_DataGridAttributes[idx];
	pAttr->m_InitialValue = name;
	pAttr->m_DataType = DT_NONE;
	pAttr->m_Value = name;
	pAttr->m_AttributeID = attributeID;
	pAttr->m_HelpTopic = helpTopic;

	// Add the data type to the 2nd column
	idx++;
	pAttr = m_DataGrid.m_DataGridAttributes[idx];
	switch(type) {
	case DT_INT:
		pAttr->m_InitialValue = "Integer";
		break;
	case DT_FLOAT:
		pAttr->m_InitialValue = "Float";
		break;
	case DT_STRING:
		pAttr->m_InitialValue = "String";
		break;
	case DT_LIST:
		pAttr->m_InitialValue = "List";
		break;
	}
	pAttr->m_Value = pAttr->m_InitialValue;
	pAttr->m_DataType = DT_NONE;


	// Handle the minimum column (skip it for strings and lists)
	idx++;
	pAttr = m_DataGrid.m_DataGridAttributes[idx];
	if (type == DT_STRING || type == DT_LIST) {
		pAttr->m_DataType = DT_NONE;
		pAttr->m_InitialValue = "";
		pAttr->m_Value = "";
		pAttr->m_ReadOnly = TRUE;
	}
	else {
		pAttr->m_DataType = DT_FLOAT;
		pAttr->m_InitialValue = "";
		pAttr->m_Value = "";
		pAttr->m_Min = min;
		pAttr->m_Max = max;
	}


	// Handle the maximum value
	idx++;
	pAttr = m_DataGrid.m_DataGridAttributes[idx];
	if (type == DT_STRING || type == DT_LIST) {
		pAttr->m_DataType = DT_NONE;
		pAttr->m_InitialValue = "";
		pAttr->m_Value = "";
		pAttr->m_ReadOnly = TRUE;
	}
	else {
		pAttr->m_DataType = type;
		pAttr->m_InitialValue = "";
		pAttr->m_Value = "";
		pAttr->m_Min = min;
		pAttr->m_Max = max;
	}

	// Handle the find value
	idx++;
	pAttr = m_DataGrid.m_DataGridAttributes[idx];
	pAttr->m_DataType = type;
	pAttr->m_InitialValue = "";
	pAttr->m_Value = "";
	if (type == DT_INT || type == DT_FLOAT) {
		pAttr->m_Min = min;
		pAttr->m_Max = max;
	}
	if (type == DT_LIST) {
		// Add a blank so once they make a selection they can 
		// change it back to no selection
		pAttr->m_ListValues.Add(" ");
		pAttr->m_InitialValue = " ";
		pAttr->m_Value = " ";
		for (i=0; i < listValues.GetSize(); ++i) {
			pAttr->m_ListValues.Add(listValues[i]);
		}
	}

	// Handle the replace value
	idx++;
	pAttr = m_DataGrid.m_DataGridAttributes[idx];
	pAttr->m_DataType = type;
	if (type == DT_INT || type == DT_FLOAT) {
		pAttr->m_Min = min;
		pAttr->m_Max = max;
	}
	pAttr->m_InitialValue = "";
	pAttr->m_Value = "";
	if (type == DT_LIST) {
		pAttr->m_ListValues.Add(" ");
		pAttr->m_InitialValue = " ";
		pAttr->m_Value = " ";
		for (i=0; i < listValues.GetSize(); ++i) {
			pAttr->m_ListValues.Add(listValues[i]);
		}
	}

}

// The purpose of this function is to allow us to do stuff in the parent
// class after we leave a cell so we can re-use the child class (CDataGrid).
// I had trouble passing it to CDataGrid as a member function so I made it
// a global.  CDataGrid will pass in it's parent which happens to be this class.
// That way we can still access all of the member attributes.  Kinda weird but
// it works.  The same thing can be done for EnterCell.
// Update.  Made it a static function so I could still pass it in to the DataGrid
void CDataPurificationDialog::OnLeaveCellDataGrid(void *parent)
{
	long row, col, col2;
	int idx, findIdx;
	CDataGridAttribute *pAttr;
	
	CDataPurificationDialog *me = (CDataPurificationDialog *)parent;

	row = me->m_DataGrid.GetRow();
	col = me->m_DataGrid.GetCol();
	
	if (me->m_DataGrid.m_Initializing)
		return;

	if (flog != NULL) {
		fprintf(flog, "OnLeaveCellDataGrid: %d - %d\n", row, col);
		fflush(flog);
	}

	idx = row * me->m_DataGrid.GetCols() + col;
	findIdx = row * me->m_DataGrid.GetCols() + 4;

	// When leaving column 2 or 3 (the min/max types), if the attribute is not blank,
	// set the find to readonly 
	// otherwise set the value to normal
	if (col == 2 || col == 3) {
		if (col == 2)
			col2 = 3;
		else
			col2 = 2;
		pAttr = me->m_DataGrid.m_DataGridAttributes[findIdx];
		
		if (pAttr->m_DataType == DT_INT || pAttr->m_DataType == DT_FLOAT) {
			if (me->m_DataGrid.GetTextMatrix(row, col) != "" ||
				me->m_DataGrid.GetTextMatrix(row, col2) != "") {
				pAttr = me->m_DataGrid.m_DataGridAttributes[findIdx];
				pAttr->m_ReadOnly = TRUE;
			}
			else {
				pAttr = me->m_DataGrid.m_DataGridAttributes[findIdx];
				pAttr->m_ReadOnly = FALSE;
			}
		}
	}
}


void CDataPurificationDialog::ViewMatchingProducts()
{
	CString min, max, find, replace, name, temp, temp2, columnName, tableName;
	int dataType;
	CProductAttribute *pProdAttr;
	int idx;
	qqhSLOTQuery query;
	
	m_UsedAttributeList.RemoveAll();

	for (int i=0; i < m_Products.GetSize(); ++i)
		delete m_Products[i];

	m_Products.RemoveAll();

	for (i=1; i < m_DataGrid.GetRows(); ++i) {

		idx = m_DataGrid.GetCols() * i;
		
		name = m_DataGrid.m_DataGridAttributes[idx]->m_InitialValue;
		dataType = m_DataGrid.m_DataGridAttributes[idx+5]->m_DataType;
		min = m_DataGrid.m_DataGridAttributes[idx+2]->m_Value;
		max = m_DataGrid.m_DataGridAttributes[idx+3]->m_Value;
		find = m_DataGrid.m_DataGridAttributes[idx+4]->m_Value;
		replace = m_DataGrid.m_DataGridAttributes[idx+5]->m_Value;

		if (min == "" && max == "" && (find == "" || (find == " " && dataType == DT_LIST)))
			continue;
		else
			m_UsedAttributeList.Add(name);


		if (! m_ProductDataService.m_ProductAttributeMap.Lookup(name, (CObject *&)pProdAttr))
			continue;
		
		columnName = pProdAttr->m_ColumnName;
		tableName = pProdAttr->m_TableName;
		
		// Note: service will take care of adding quotes to 
		// udfs and strings for simple operators but we need
		// to do it here for between
		if (min != "" && max != "") {
			if (pProdAttr->m_AttributeDBID > 0)
				temp.Format("'%s' and '%s'", min, max);
			else
				temp.Format("%s and %s", min, max);
			m_ProductDataService.AddQueryAttribute(query, tableName, columnName, temp, "not between");
		}
		else {
			if (min != "") {
				temp.Format("%s", min);
				m_ProductDataService.AddQueryAttribute(query, tableName, columnName, temp, "<");
			}
			
			if (max != "") {
				temp.Format("%s", max);
				m_ProductDataService.AddQueryAttribute(query, tableName, columnName, temp, ">");
			}
		}

		if (find != "") {
			if (dataType == DT_STRING)
				temp.Format("%s", find);
			else if (dataType == DT_FLOAT || dataType == DT_INT)
				temp.Format("%s", find);
			else {		// list
				// First, convert the numeric list selection to the list value
				if (atoi(find)-1 < 0)	// 0 is always blank for purification list boxes
					continue;
				temp = pProdAttr->m_ListValues[atoi(find)-1];

				// Now lookup the displayed value to get the internal value
				if (! pProdAttr->m_DisplayToInternalMap.Lookup(temp, temp2))
					continue;

				temp.Format("%s", temp2);
			}
			m_ProductDataService.AddQueryAttribute(query, tableName, columnName, temp, "=");
		}

	}
	
	// Now handle the assignment status operators
	CComboBox *pGroupBox = (CComboBox *)GetDlgItem(IDC_PRODUCT_GROUP_LIST);
	CButton *pAssignedBtn = (CButton *)GetDlgItem(IDC_ASSIGNED);
	CButton *pNotAssignedBtn = (CButton *)GetDlgItem(IDC_NOT_ASSIGNED);

	int curSel = pGroupBox->GetCurSel();
	if (curSel == 0)		// Assigned
		m_ProductDataService.AddQueryAttribute(query, TB_PRODUCTGROUP, "dbslottinggroupid", " ", "exists");
	else if (curSel == 1)
		m_ProductDataService.AddQueryAttribute(query, TB_PRODUCTGROUP, "dbslottinggroupid", " ", "not exists");
	else if (curSel > 1) {
		temp.Format("%d", pGroupBox->GetItemData(curSel));
		m_ProductDataService.AddQueryAttribute(query, TB_PRODUCTGROUP, "dbslottinggroupid", temp, DT_INT);
	}

	if (pAssignedBtn->GetCheck())
		m_ProductDataService.AddQueryAttribute(query, TB_LOCATION, "dblocationid", " ", "exists");
	else if (pNotAssignedBtn->GetCheck())
		m_ProductDataService.AddQueryAttribute(query, TB_LOCATION, "dblocationid", " ", "not exists");

	CStringArray productList;
	int rc;

	try {
		CWaitCursor cwc;
		rc = m_ProductDataService.GetProductListByQuery(query, productList);
	}
	catch (Ssa_Exception e) {
		utilityHelper.ProcessError("Error querying products.", &e);
		return;
	}
	catch (...) {
		utilityHelper.ProcessError("Error querying products.");
		return;
	}

	if (productList.GetSize() <= 0) {
		AfxMessageBox("No products were found that match the criteria.");
		return;
	}
	
	if (productList.GetSize() == 1)
		temp = "1 product selected.";
	else
		temp.Format("%d products selected.", productList.GetSize());
	AfxMessageBox(temp);


	CProductPack *pProduct;
	BOOL bNewDialog = FALSE;

	if (m_ProductListDialog == NULL) {
		bNewDialog = TRUE;
		m_ProductListDialog = new CDisplayResults;
		m_ProductListDialog->m_Headers.Add("");
		m_ProductListDialog->m_Tabs.Add("Matching Products");
		m_ProductListDialog->m_WindowCaptions.Add("Data Purification");
		m_ProductListDialog->m_HelpTopics.Add("DataPurificationResults_Main");
		m_ProductListDialog->m_ListHelpTopics.Add("DataPurificationResults_ProductList");
		m_ProductListDialog->m_OrigColumnSize = 150;
		m_ProductListDialog->m_MessageReceiver = this;
		m_ProductListDialog->m_NextCaption = "&Purify Products";
		m_ProductListDialog->m_NextClosesWindow = FALSE;
		m_ProductListDialog->m_NextCaption2 = "Product &Maintenance";
		m_ProductListDialog->m_NextClosesWindow2 = FALSE;
		m_ProductListDialog->m_NextCaption3 = "&Delete Products";
		m_ProductListDialog->m_NextClosesWindow3 = FALSE;
		m_ProductListDialog->m_IsModeless = TRUE;
		m_ProductListDialog->m_AllowMultipleSelections = TRUE;
		m_ProductListDialog->m_MainHelpTopic = "DataPurificationResults_Main";
		m_ProductListDialog->m_NextHelp1 = "DataPurificationResults_PurifyProducts";
		m_ProductListDialog->m_NextHelp2 = "DataPurificationResults_ProductMaintenance";
		m_ProductListDialog->m_NextHelp3 = "DataPurificationResults_Delete";
		m_ProductListDialog->m_SelectAllHelp = "DataPurificationResults_SelectAll";
	}

	m_ProductListDialog->m_Data.RemoveAll();

	BuildHeaderLine(m_ProductListDialog->m_Headers[0]);
	
	for (i=0; i < productList.GetSize(); ++i) {
		pProduct = new CProductPack;
		pProduct->ParseAll(productList[i]);
		m_Products.Add(pProduct);
		BuildDisplayLine(temp, *pProduct);
		m_ProductListDialog->m_Data.Add(temp);
	}
		
	if (bNewDialog)
		m_ProductListDialog->Create(IDD_DISPLAY_RESULTS, this);
	else
		m_ProductListDialog->ReloadPage();

	m_ProductListDialog->CenterWindow();
	m_ProductListDialog->ShowWindow(SW_SHOW);

	// Don't allow them to do another query while we are
	// showing the results of this one
	m_ViewButton.EnableWindow(FALSE);

}


BOOL CDataPurificationDialog::ValidateCells()
{
	int i, idx;
	CString tmp, min, max, find, replace, name;
	int dataType;

	for (i=1; i < m_DataGrid.GetRows(); ++i) {
		idx = m_DataGrid.GetCols() * i;

		// the last column has the actual datatype
		dataType = m_DataGrid.m_DataGridAttributes[idx+5]->m_DataType;
		min = m_DataGrid.m_DataGridAttributes[idx+2]->m_Value;
		max = m_DataGrid.m_DataGridAttributes[idx+3]->m_Value;
		find = m_DataGrid.m_DataGridAttributes[idx+4]->m_Value;
		replace = m_DataGrid.m_DataGridAttributes[idx+5]->m_Value;
		name = m_DataGrid.m_DataGridAttributes[idx]->m_InitialValue;

		if (dataType != DT_STRING && dataType != DT_LIST) {

			if (min != "" && ! utilityHelper.IsNumeric(min)) {
				tmp.Format("The minimum range value for attribute %s must be a valid number.", name);
				AfxMessageBox(tmp);
				m_DataGrid.SetFocus();
				m_DataGrid.SetRow(i);
				m_DataGrid.SetCol(2);
				return FALSE;
			}

			if (max != "" && ! utilityHelper.IsNumeric(max)) {
				tmp.Format("The maximum range value for attribute %s must be a valid number.", name);
				AfxMessageBox(tmp);
				m_DataGrid.SetFocus();
				m_DataGrid.SetRow(i);
				m_DataGrid.SetCol(3);
				return FALSE;
			}

			/*
			if (min != "" && max == "") {
				tmp.Format("The maximum range value is required for attribute %s.", name);
				AfxMessageBox(tmp);
				m_DataGrid.SetFocus();
				m_DataGrid.SetRow(i);
				m_DataGrid.SetCol(2);
				return FALSE;
			}

			if (max != "" && min == "") {
				tmp.Format("The minimum range value is required for attribute %s.", name);
				AfxMessageBox(tmp);
				m_DataGrid.SetFocus();
				m_DataGrid.SetRow(i);
				m_DataGrid.SetCol(2);
				return FALSE;
			}
			*/

			if (min != "" && max != "" && atof(min) > atof(max)) {
				tmp.Format("The minimum range value for attribute %s must be less than the maximum.", name);
				AfxMessageBox(tmp);
				m_DataGrid.SetFocus();
				m_DataGrid.SetRow(i);
				m_DataGrid.SetCol(2);
				return FALSE;
			}
			
			if (min != "" && max != "")
				continue;		// no need to validate find/replace

		}

		// Validate find/replace
		if (find == "" && replace != "") {
			tmp.Format("The \"find\" value for attribute %s must be present if the \"replace\" value is present.", name);
			AfxMessageBox(tmp);
			m_DataGrid.SetFocus();
			m_DataGrid.SetRow(i);
			m_DataGrid.SetCol(4);
			return FALSE;
		}

		if ((dataType == DT_INT || dataType == DT_FLOAT) && ! utilityHelper.IsNumeric(find)) {
			tmp.Format("The \"find\" value for attribute %s must be a valid number.", name);
			AfxMessageBox(tmp);
			m_DataGrid.SetFocus();
			m_DataGrid.SetRow(i);
			m_DataGrid.SetCol(4);
			return FALSE;
		}

		if ((dataType == DT_INT || dataType == DT_FLOAT) && ! utilityHelper.IsNumeric(replace)) {
			tmp.Format("The \"replace\" value for attribute %s must be a valid number.", name);
			AfxMessageBox(tmp);
			m_DataGrid.SetFocus();
			m_DataGrid.SetRow(i);
			m_DataGrid.SetCol(5);
			return FALSE;
		}


	}

	return TRUE;

}



// Build the header for the product list
// Put the basic info, wms id, description in front,
// followed by anything they queried by, followed
// by the rest of the data
void CDataPurificationDialog::BuildHeaderLine(CString &header)
{
	CString temp, temp2;
	CMapStringToString map;		// this is used to keep track of what we've already displayed
	CProductAttribute *pAttr;

	header.Format("WMS Product ID|WMS Product Detail ID|Product Name|");
	for (int i=0; i < m_UsedAttributeList.GetSize(); ++i) {
		// m_UsedAttributeList contains display names of columns used in query
		
		// Don't display these again - we always show them first
		if (m_UsedAttributeList[i].CompareNoCase("WMS Product ID") == 0 ||
			m_UsedAttributeList[i].CompareNoCase("WMS Product Detail ID") == 0 ||
			m_UsedAttributeList[i].CompareNoCase("Product Name") == 0)
			continue;

		// Try to find the product attribute based on the display name
		if (! m_ProductDataService.m_ProductAttributeMap.Lookup(m_UsedAttributeList[i], (CObject *&)pAttr))
			continue;

		if (pAttr->m_AttributeDBID > 0) {	// it's a UDF
			header += pAttr->m_Name;
			header += "|";
			temp.Format("UDF:%s", pAttr->m_Name);
			map.SetAt(temp, "dummy");
		}
		else {
			header += m_UsedAttributeList[i];
			header += "|";
			map.SetAt(m_UsedAttributeList[i], "dummy");
		}
	}

	// Add the rest of the attributes if they are not in the attribute list
	if (! map.Lookup("Weight", temp)) { 
		temp.Format("Weight|"); 
		header += temp; 
	}

	if (! map.Lookup("Movement", temp)) { 
		temp.Format("Movement|"); 
		header += temp; 
	}

	if (! map.Lookup("Case Width", temp)) { 
		temp.Format("Case Width|"); 
		header += temp; 
	}

	if (! map.Lookup("Case Length", temp)) { 
		temp.Format("Case Length|"); 
		header += temp; 
	}
	if (! map.Lookup("Case Height", temp)) { 
		temp.Format("Case Height|"); 
		header += temp; 
	}
	if (! map.Lookup("Hazard Flag", temp)) { 
		temp.Format("Hazard Flag|"); 
		header += temp; 
	}
	if (! map.Lookup("Unit of Issue", temp)) { 
		temp.Format("Unit of Issue|"); 
		header += temp; 
	}
	if (! map.Lookup("Pick-to-Belt Flag", temp)) { 
		temp.Format("Pick-to-Belt Flag|"); 
		header += temp; 
	}
	if (! map.Lookup("Optimize Method", temp)) { 
		temp.Format("Optimize Method|"); 
		header += temp; 
	}
	if (! map.Lookup("Balance on Hand", temp)) { 
		temp.Format("Balance on Hand|"); 
		header += temp; 
	}
	if (! map.Lookup("Number In Pallet", temp)) { 
		temp.Format("Number In Pallet|"); 
		header += temp; 
	}
	if (! map.Lookup("Allow  Height-Length Swap", temp)) { 
		temp.Format("Allow  Height-Length Swap|"); 
		header += temp; 
	}
	if (! map.Lookup("Allow Height-Width Swap", temp)) { 
		temp.Format("Allow Height-Width Swap|"); 
		header += temp; 
	}
	if (! map.Lookup("Allow Width-Length Swap", temp)) { 
		temp.Format("Allow Width-Length Swap|"); 
		header += temp;
	}
	if (! map.Lookup("Each Width", temp)) { 
		temp.Format("Each Width|"); 
		header += temp; 
	}
	if (! map.Lookup("Each Length", temp)) { 
		temp.Format("Each Length|"); 
		header += temp; 
	}
	if (! map.Lookup("Each Height", temp)) { 
		temp.Format("Each Height|"); 
		header += temp; 
	}
	if (! map.Lookup("Inner Width", temp)) {
		temp.Format("Inner Width|"); 
		header += temp; 
	}
	if (! map.Lookup("Inner Length", temp)) { 
		temp.Format("Inner Length|");
		header += temp;
	}
	if (! map.Lookup("Inner Height", temp)) { 
		temp.Format("Inner Height|");
		header += temp; 
	}
	if (! map.Lookup("Case Pack", temp)) {
		temp.Format("Case Pack|"); 
		header += temp; 
	}
	if (! map.Lookup("Inner Pack", temp)) {
		temp.Format("Inner Pack|");
		header += temp;
	}
	if (! map.Lookup("Number of Hits", temp)) { 
		temp.Format("Number of Hits|");
		header += temp; 
	}
	if (! map.Lookup("Container Width", temp)) {
		temp.Format("Container Width|");
		header += temp;
	}
	if (! map.Lookup("Container Length", temp)) {
		temp.Format("Container Length|"); 
		header += temp;
	}
	if (! map.Lookup("Container Height", temp)) { 
		temp.Format("Container Height|");
		header += temp; 
	}
	if (! map.Lookup("Container Width Override Flag", temp)) { 
		temp.Format("Container Width Override Flag|"); 
		header += temp;
	}
	if (! map.Lookup("Container Length Override Flag", temp)) {
		temp.Format("Container Length Override Flag|"); 
		header += temp;
	}
	if (! map.Lookup("Container Height Override Flag", temp)) { 
		temp.Format("Container Height Override Flag|"); 
		header += temp; 
	}
	if (! map.Lookup("Storage TI", temp)) { 
		temp.Format("Storage TI|"); 
		header += temp;
	}
	if (! map.Lookup("Storage HI", temp)) {
		temp.Format("Storage HI|"); 
		header += temp; 
	}

	return;

}


void CDataPurificationDialog::BuildDisplayLine(CString &line, CProductPack &product)
{
	CString temp, temp2, columnName;
	CProductAttribute *pAttr;
	CMapStringToString map;

	line.Format("WMS Product ID|WMS Product Detail ID|Product Name|");
	line.Format("%s|%s|%s|", product.m_WMSProductID, product.m_WMSProductDetailID,
		product.m_Description);

	for (int i=0; i < m_UsedAttributeList.GetSize(); ++i) {

		// Don't display these again - we always show them first
		if (m_UsedAttributeList[i].CompareNoCase("WMS Product ID") == 0 ||
			m_UsedAttributeList[i].CompareNoCase("WMS Product Detail ID") == 0 ||
			m_UsedAttributeList[i].CompareNoCase("Product Name") == 0)
			continue;

		if (! m_ProductDataService.m_ProductAttributeMap.Lookup(m_UsedAttributeList[i], (CObject *&)pAttr))
			continue;

		if (pAttr->m_AttributeDBID > 0) {	// it's a UDF
			for (int j=0; j < product.m_UDFList.GetSize(); ++j) {
				if (product.m_UDFList[j]->m_ListID == pAttr->m_AttributeDBID) {
					line += product.m_UDFList[j]->m_Value;
					line += "|";
					temp.Format("UDF:%s", pAttr->m_Name);
					map.SetAt(temp, "dummy");
					break;
				}
			}
		}
		else {
			product.GetValueByName(temp2, pAttr->m_Name);
			if (pAttr->m_Type == DT_LIST) {
				pAttr->m_InternalToDisplayMap.Lookup(temp, temp2);
			}
			line += temp2;
			line += "|";
			map.SetAt(m_UsedAttributeList[i], "dummy");
		}
	}

	// Add the rest of the attributes if they are not in the attribute list
	if (! map.Lookup("Weight", temp)) { 
		temp.Format("%9.2f|", product.m_Weight); 
		line += temp; 
	}
	if (! map.Lookup("Movement", temp)) { 
		temp.Format("%9.2f|", product.m_Movement); 
		line += temp; 
	}
	if (! map.Lookup("Case Width", temp)) { 
		temp.Format("%9.2f|", product.m_CaseWidth); 
		line += temp; 
	}
	if (! map.Lookup("Case Length", temp)) { 
		temp.Format("%9.2f|", product.m_CaseLength); 
		line += temp; 
	}
	if (! map.Lookup("Case Height", temp)) { 
		temp.Format("%9.2f|", product.m_CaseHeight); 
		line += temp; 
	}
	if (! map.Lookup("Hazard Flag", temp))
		line += (product.m_IsHazard) ? "True|" : "False|";


	if (! map.Lookup("Unit of Issue", temp)) {
		switch (product.m_UnitOfIssue) {
		case UOI_EACH:
			line += "Each|";
			break;
		case UOI_INNER:
			line += "Inner|";
			break;
		case UOI_CASE:
			line += "Case|";
			break;
		case UOI_PALLET:
			line += "Pallet|";
			break;
		}
	}

	if (! map.Lookup("Pick-to-Belt Flag", temp))
		line += (product.m_IsPickToBelt) ? "True|" : "False|";
		
	if (! map.Lookup("Optimize Method", temp))
		line += (product.m_OptimizeBy == 0) ? "Cube|" : "Labor|";

	if (! map.Lookup("Balance on Hand", temp)) { 
		temp.Format("%9.2f|", product.m_BalanceOnHand); 
		line += temp; 
	}
	if (! map.Lookup("Number In Pallet", temp)) { 
		temp.Format("%9.2f|", product.m_NumberInPallet); 
		line += temp; 
	}
	if (! map.Lookup("Allow  Height-Length Swap", temp)) 
		line += (product.m_RotateXAxis) ? "True|" : "False|";

	if (! map.Lookup("Allow Height-Width Swap", temp))
		line += (product.m_RotateYAxis) ? "True|" : "False|";

	if (! map.Lookup("Allow Width-Length Swap", temp))
		line += (product.m_RotateZAxis) ? "True|" : "False|";

	if (! map.Lookup("Each Width", temp)) { 
		temp.Format("%9.2f|", product.m_EachWidth); 
		line += temp; 
	}
	if (! map.Lookup("Each Length", temp)) { 
		temp.Format("%9.2f|", product.m_EachLength); 
		line += temp; 
	}
	if (! map.Lookup("Each Height", temp)) { 
		temp.Format("%9.2f|", product.m_EachHeight); 
		line += temp; 
	}
	if (! map.Lookup("Inner Width", temp)) {
		temp.Format("%9.2f|", product.m_InnerWidth); 
		line += temp; 
	}
	if (! map.Lookup("Inner Length", temp)) { 
		temp.Format("%9.2f|", product.m_InnerLength);
		line += temp;
	}
	if (! map.Lookup("Inner Height", temp)) { 
		temp.Format("%9.2f|", product.m_InnerHeight);
		line += temp; 
	}
	if (! map.Lookup("Case Pack", temp)) {
		temp.Format("%d|",product.m_CasePack); 
		line += temp; 
	}
	if (! map.Lookup("Inner Pack", temp)) {
		temp.Format("%d|", product.m_InnerPack);
		line += temp;
	}
	if (! map.Lookup("Number of Hits", temp)) { 
		temp.Format("%9.2f|", product.m_NumberOfHits);
		line += temp; 
	}
	if (! map.Lookup("Container Width", temp)) {
		temp.Format("%9.2f|", product.m_Container.m_Width);
		line += temp;
	}
	if (! map.Lookup("Container Length", temp)) {
		temp.Format("%9.2f|", product.m_Container.m_Length); 
		line += temp;
	}
	if (! map.Lookup("Container Height", temp)) { 
		temp.Format("%9.2f|", product.m_Container.m_Height);
		line += temp; 
	}
	if (! map.Lookup("Container Width Override Flag", temp))
		line += (product.m_Container.m_IsWidthOverride) ? "True|" : "False|";

	if (! map.Lookup("Container Length Override Flag", temp))
		line += (product.m_Container.m_IsLengthOverride) ? "True|" : "False|";

	if (! map.Lookup("Container Height Override Flag", temp))
		line += (product.m_Container.m_IsHeightOverride) ? "True|" : "False|";

	if (! map.Lookup("Storage TI", temp)) { 
		temp.Format("%d|", product.m_Container.m_Ti); 
		line += temp;
	}
	if (! map.Lookup("Storage HI", temp)) {
		temp.Format("%d|", product.m_Container.m_Hi); 
		line += temp; 
	}

}

void CDataPurificationDialog::OnProductMaintenance(WPARAM wParam, LPARAM lParam)
{
	UNREFERENCED_PARAMETER(lParam);
	if (lParam == 0) {
		AfxMessageBox("Please select a product to modify.");
		return;
	}

	this->EnableWindow(FALSE);
	m_ProductListDialog->EnableWindow(FALSE);

	CTemporaryResourceOverride tro;
	CProductSheet sheet("Data Purification - Product Maintenance", this, 0);
	CProductPage productPage;
	CProductContainerPage containerPage;
	CProductOptimizePage optimizePage;
	CUDFPage udfPage;
	CStringArray productList;
	CProductPack product;
	CString line;
	int rc;

	sheet.m_DisplayProductID = m_Products[wParam]->m_ProductPackDBID;

	sheet.m_psh.dwFlags |= PSH_NOAPPLYNOW;
	sheet.AddPage(&productPage);
	sheet.AddPage(&containerPage);
	sheet.AddPage(&udfPage);
	sheet.AddPage(&optimizePage);

	try {
		sheet.DoModal();
	}
	catch (...) {
		AfxMessageBox("Error running ProductMaintenance");
	}

	// Re-select the product after maintenance in case, they 
	// modified or deleted it
	try {
		rc = m_ProductDataService.GetProductByID(m_Products[wParam]->m_ProductPackDBID, productList);
	}
	catch (Ssa_Exception e) {
		utilityHelper.ProcessError("Error re-selecting product after maintenance.", &e);
		this->EnableWindow(TRUE);
		m_ProductListDialog->EnableWindow(TRUE);
		return;
	}
	catch (...) {
		utilityHelper.ProcessError("Generic error re-selecting product after maintenance.");
		this->EnableWindow(TRUE);
		m_ProductListDialog->EnableWindow(TRUE);
		return;
	}

	if (productList.GetSize() == 0) {		// the product must have been deleted
		delete m_Products[wParam];
		m_Products.RemoveAt(wParam);
		m_ProductListDialog->DeleteItem(wParam);
		this->EnableWindow(TRUE);
		m_ProductListDialog->EnableWindow(TRUE);
		return;
	}

	// The may have updated the product so re-build the display line
	// and update the list
	product.ParseAll(productList[0]);
	BuildDisplayLine(line, product);
	
	m_ProductListDialog->ModifyItem(wParam, line);
	
	this->EnableWindow(TRUE);
	m_ProductListDialog->EnableWindow(TRUE);

}

void CDataPurificationDialog::OnRunPurification(WPARAM wParam, LPARAM lParam)
{
	UNREFERENCED_PARAMETER(wParam);

	CMapStringToString minMap, maxMap, findMap;
	CStringArray minList, maxList, findList;

	CString sql, name, dataType, min, max, find, replace;
	int idx, i, k, j;
	CProductPack *product;
	CString currentValue, columnName, tableName;
	CStringArray sqlList;
	CString temp, temp2;
	CProductAttribute *pProdAttr;
	CWinThread *pThread;
	CProgress *pProgress = new CProgress;
	BOOL bThreadDone;
	int rc, curSel;
	CMap<int, int, int, int> map;
	CDWordArray purifiedList;

	this->EnableWindow(FALSE);
	m_ProductListDialog->EnableWindow(FALSE);

	if (lParam == 0) {	// means they selected all products
		lParam = m_Products.GetSize();
		for (i=0; i < m_Products.GetSize(); ++i)
			map.SetAt(i, i);
	} else {
		for (i=0; i < m_ProductListDialog->m_SelectionList.GetSize(); ++i) {
			int t = m_ProductListDialog->m_SelectionList[i];
			map.SetAt(m_ProductListDialog->m_SelectionList[i], m_ProductListDialog->m_SelectionList[i]);
		}
	}

	for (i=1; i < m_DataGrid.GetRows(); ++i) {

		idx = m_DataGrid.GetCols() * i;
		
		CDataGridAttribute *pAttr = m_DataGrid.m_DataGridAttributes[idx+4];

		name = m_DataGrid.m_DataGridAttributes[idx]->m_InitialValue;
		min = m_DataGrid.m_DataGridAttributes[idx+2]->m_Value;
		max = m_DataGrid.m_DataGridAttributes[idx+3]->m_Value;
		find = m_DataGrid.m_DataGridAttributes[idx+4]->m_Value;
		min.TrimLeft();
		max.TrimLeft();
		find.TrimLeft();

		if (m_DataGrid.m_DataGridAttributes[idx+4]->m_DataType == DT_LIST && find != "") {
			temp = m_DataGrid.m_DataGridAttributes[idx+4]->m_ListValues[atoi(find)];
			find = temp;
		}
		replace = m_DataGrid.m_DataGridAttributes[idx+5]->m_Value;
		replace.TrimLeft();
		if (m_DataGrid.m_DataGridAttributes[idx+4]->m_DataType == DT_LIST && replace != "") {
			temp = m_DataGrid.m_DataGridAttributes[idx+5]->m_ListValues[atoi(replace)];
			replace = temp;
		}

		if (min == "" && max == "" && find == "")
			continue;

		if (! m_ProductDataService.m_ProductAttributeMap.Lookup(name, (CObject *&)pProdAttr))
			continue;

		if (min != "") {
			minMap.SetAt(name, min);
			minList.Add(name);
		}

		if (max != "") {
			maxMap.SetAt(name, max);
			maxList.Add(name);
		}

		if (find != "" && replace != "") {
			temp.Format("%s|%s", find, replace);
			findMap.SetAt(name, temp);
			findList.Add(name);
		}
	}

	if (minList.GetSize() == 0 && maxList.GetSize() == 0 && findList.GetSize() == 0) {
		AfxMessageBox("You must enter at least one minimum, maximum, or find value "
			"in order to purify the data.");
		this->EnableWindow(TRUE);
		m_ProductListDialog->EnableWindow(TRUE);
		return;
	}

	BOOL bMatch;
	pProgress->Create(IDD_PROGRESS, this);
	pProgress->CenterWindow();
	pProgress->ShowWindow(SW_SHOW);
	pProgress->UpdateWindow();
	pProgress->m_ProgressCtrl.SetRange32(0, lParam);
	pProgress->m_ProgressCtrl.SetPos(0);
	pProgress->m_ProgressCtrl.SetStep(1);
	pProgress->m_StatusTextCtrl.SetWindowText("Purifying Data...");

	for (i=0; i < m_Products.GetSize(); ++i) {
		if (! map.Lookup(i, curSel))
			continue;

		product = m_Products[i];
		//sqlList.RemoveAll();

		for (j=0; j < minList.GetSize(); ++j) {
			m_ProductDataService.m_ProductAttributeMap.Lookup(minList[j], (CObject *&)pProdAttr);
			if (pProdAttr->m_AttributeDBID > 0) {		// UDF
				for (k=0; k < product->m_UDFList.GetSize(); ++k) {
					if (product->m_UDFList[k]->m_ListID == pProdAttr->m_AttributeDBID) {
						currentValue = product->m_UDFList[k]->m_Value;
						break;
					}
				}
			}
			else
				product->GetValueByName(currentValue, pProdAttr->m_Name);
			minMap.Lookup(minList[j], min);
			bMatch = FALSE;
			if (pProdAttr->m_Type == DT_INT) {
				if (atoi(currentValue) < atoi(min))
					bMatch = TRUE;
			}
			else if (pProdAttr->m_Type == DT_FLOAT) {
				if (atof(currentValue) < atof(min))
					bMatch = TRUE;
			}

			if (! bMatch)
				continue;

			if (pProdAttr->m_TableName == TB_PRODUCTUDFVAL) {
				sql.Format("update dbprodpkudfval set value = '%s', "
					"integervalue = %d, floatvalue = %f "
					"where dbproductpackid = %d "
					"and dbprodpkudflistid = %d",
					min, 
					utilityHelper.IsNumeric(min) ? atoi(min) : 0,
					utilityHelper.IsNumeric(min) ? atof(min) : 0.0,
					product->m_ProductPackDBID, pProdAttr->m_AttributeDBID);
				// Update the product so we can re-display it with the new values
				for (k=0; k < product->m_UDFList.GetSize(); ++k) {
					if (product->m_UDFList[k]->m_ListID == pProdAttr->m_AttributeDBID) {
						product->m_UDFList[k]->m_Value = min;
						break;
					}
				}
				
			}
			else {
				sql.Format("update %s set %s = %s "
					"where dbproductpackid = %d", 
					pProdAttr->m_TableName,	pProdAttr->m_ColumnName, min, product->m_ProductPackDBID);
				product->SetValueByName(min, pProdAttr->m_Name);
			}
			sqlList.Add(sql);
		}

		for (j=0; j < maxList.GetSize(); ++j) {
			m_ProductDataService.m_ProductAttributeMap.Lookup(maxList[j], (CObject *&)pProdAttr);	
			if (pProdAttr->m_AttributeDBID > 0) {		// UDF
				for (k=0; k < product->m_UDFList.GetSize(); ++k) {
					if (product->m_UDFList[k]->m_ListID == pProdAttr->m_AttributeDBID) {
						currentValue = product->m_UDFList[k]->m_Value;
						break;
					}
				}
			}
			else
				product->GetValueByName(currentValue, pProdAttr->m_Name);
			maxMap.Lookup(maxList[j], max);
			bMatch = FALSE;
			if (pProdAttr->m_Type == DT_INT) {
				if (atoi(currentValue) > atoi(max))
					bMatch = TRUE;
			}
			else if (pProdAttr->m_Type == DT_FLOAT) {
				if (atof(currentValue) > atof(max))
					bMatch = TRUE;
			}

			if (! bMatch)
				continue;

			if (pProdAttr->m_TableName == TB_PRODUCTUDFVAL) {
				sql.Format("update dbprodpkudfval set value = '%s', "
					"IntegerValue = %d, FloatValue = %f "
					"where dbproductpackid = %d "
					"and dbprodpkudflistid = %d",
					max, 
					utilityHelper.IsNumeric(max) ? atoi(max) : 0,
					utilityHelper.IsNumeric(max) ? atof(max) : 0.0,
					product->m_ProductPackDBID, pProdAttr->m_AttributeDBID);
				for (k=0; k < product->m_UDFList.GetSize(); ++k) {
					if (product->m_UDFList[k]->m_ListID == pProdAttr->m_AttributeDBID) {
						product->m_UDFList[k]->m_Value = max;
						break;
					}
				}
			}
			else {
				sql.Format("update %s set %s = %s "
					"where dbproductpackid = %d", 
					pProdAttr->m_TableName,	pProdAttr->m_ColumnName, max, product->m_ProductPackDBID);
				product->SetValueByName(max, pProdAttr->m_Name);
			}
			sqlList.Add(sql);
		}


		for (j=0; j < findList.GetSize(); ++j) {
			m_ProductDataService.m_ProductAttributeMap.Lookup(findList[j], (CObject *&)pProdAttr);
			if (pProdAttr->m_AttributeDBID > 0) {		// UDF
				for (k=0; k < product->m_UDFList.GetSize(); ++k) {
					if (product->m_UDFList[k]->m_ListID == pProdAttr->m_AttributeDBID) {
						currentValue = product->m_UDFList[k]->m_Value;
						break;
					}
				}
			}
			else
				product->GetValueByName(currentValue, pProdAttr->m_Name);

			findMap.Lookup(findList[j], temp);
			find = temp.Left(temp.Find("|"));
			replace = temp.Mid(temp.Find("|")+1);

			bMatch = FALSE;
			if (pProdAttr->m_Type == DT_INT) {
				if (atoi(currentValue) == atoi(find))
					bMatch = TRUE;
			}
			else if (pProdAttr->m_Type == DT_FLOAT) {
				if (atof(currentValue) == atof(find))
					bMatch = TRUE;
			}
			else if (pProdAttr->m_Type == DT_STRING) {
				if (currentValue == find)
					bMatch = TRUE;
			}
			
			else if (pProdAttr->m_Type == DT_LIST) {
				if (currentValue == find)
					bMatch = TRUE;
			}

			if (! bMatch)
				continue;

			if (pProdAttr->m_TableName == TB_PRODUCTUDFVAL) {
				sql.Format("update dbprodpkudfval set value = '%s', "
					"Integervalue = %d, FloatValue = %f "
					"where dbproductpackid = %d "
					"and dbprodpkudflistid = %d",
					replace, 
					utilityHelper.IsNumeric(replace) ? atoi(replace) : 0,
					utilityHelper.IsNumeric(replace) ? atof(replace) : 0,
					product->m_ProductPackDBID, pProdAttr->m_AttributeDBID);
				for (k=0; k < product->m_UDFList.GetSize(); ++k) {
					if (product->m_UDFList[k]->m_ListID == pProdAttr->m_AttributeDBID) {
						product->m_UDFList[k]->m_Value = replace;
						break;
					}
				}
			}
			else {
				if (pProdAttr->m_Type == DT_FLOAT || pProdAttr->m_Type == DT_INT) {
					sql.Format("update %s set %s = %s "
						"where dbproductpackid = %d", 
						pProdAttr->m_TableName,	pProdAttr->m_ColumnName, 
						replace, product->m_ProductPackDBID);
					product->SetValueByName(replace, pProdAttr->m_Name);
				}
				else if (pProdAttr->m_Type == DT_STRING) {
					sql.Format("update %s set %s = '%s' "
					"where dbproductpackid = %d", 
					pProdAttr->m_TableName,	pProdAttr->m_ColumnName, replace, product->m_ProductPackDBID);
					product->SetValueByName(replace, pProdAttr->m_Name);
				}
				else if (pProdAttr->m_Type == DT_LIST) {
					// Now lookup the displayed value to get the internal value
					temp = replace;
					if (! pProdAttr->m_DisplayToInternalMap.Lookup(temp, replace))
						continue;

					sql.Format("update %s set %s = %s "
						"where dbproductpackid = %d ",
						pProdAttr->m_TableName, pProdAttr->m_ColumnName, 
						replace, product->m_ProductPackDBID);
					product->SetValueByName(replace, pProdAttr->m_Name);
				}
			}
			sqlList.Add(sql);
		}
		pProgress->m_ProgressCtrl.StepIt();
		
		purifiedList.Add(i);

		// If we've built 100 statements or 
		// it's the last product
		// or it's the last selected product, execute the sql
		if (pProgress->m_Stopping ||
			sqlList.GetSize() > 100 ||
			(sqlList.GetSize() > 0 && i == m_Products.GetSize()-1) ||
			(sqlList.GetSize() > 0 && purifiedList.GetSize() == lParam))
		{
			pThread = AfxBeginThread(CDataAccessService::ExecuteStatementsThread, &sqlList);
		
			bThreadDone = FALSE;
			while (TRUE) {
				if ( ! utilityHelper.PeekAndPump() )
					break;
				
				bThreadDone = g_ThreadDone.Lock(0);
				if (bThreadDone)
					break;
			}
			
			rc = atoi(sqlList[0]);
			if (rc < 0) {
				AfxMessageBox(sqlList[1]);
				break;
			}
			sqlList.RemoveAll();
		}
		
		if (pProgress->m_Stopping)
			break;
	}

	pProgress->DestroyWindow();

	// This will reload the data so they can see the changed values
	for (i=0; i < purifiedList.GetSize(); ++i) {
		product = m_Products[purifiedList[i]];
		BuildDisplayLine(temp, *product);
		m_ProductListDialog->ModifyItem(purifiedList[i], temp);
	}

	this->EnableWindow(TRUE);
	m_ProductListDialog->EnableWindow(TRUE);
	
	return;

}

void CDataPurificationDialog::OnCloseDisplay(WPARAM wParam, LPARAM lParam)
{
	UNREFERENCED_PARAMETER(wParam);
	UNREFERENCED_PARAMETER(lParam);

	if (m_ProductListDialog != NULL)
		m_ProductListDialog = NULL;
	m_ViewButton.EnableWindow(TRUE);
	m_ViewButton.SetFocus();
}

void CDataPurificationDialog::OnView() 
{
	UpdateData(TRUE);

	// Make sure all the values are within their ranges
	if (! ValidateCells())
		return;

	ViewMatchingProducts();	
}

void CDataPurificationDialog::OnReset() 
{
	CDataGridAttribute *pAttr;
	CComboBox *pComboBox = (CComboBox *)GetDlgItem(IDC_PRODUCT_GROUP_LIST);
	CButton *pButton = (CButton *)GetDlgItem(IDC_BOTH);
	long row, col;

	// Clear out all of the values
	for (int i=0; i < m_DataGrid.m_DataGridAttributes.GetSize(); ++i) {
		row = i/m_DataGrid.GetCols();
		col = i - (row * m_DataGrid.GetCols());

		pAttr = m_DataGrid.m_DataGridAttributes[i];
		if (pAttr->m_Type == AT_VAR) {
			pAttr->m_Value = "";
			m_DataGrid.SetTextMatrix(row, col, "");
		}
	}
	
	pComboBox->SetCurSel(-1);
	pButton->SetCheck(1);
	pButton = (CButton *)GetDlgItem(IDC_ASSIGNED);
	pButton->SetCheck(0);
	pButton = (CButton *)GetDlgItem(IDC_NOT_ASSIGNED);
	pButton->SetCheck(0);

}

void CDataPurificationDialog::OnDeleteProducts(WPARAM wParam, LPARAM lParam)
{
	UNREFERENCED_PARAMETER(wParam);
	int rc, curSel;
	CProgress *pProgress;
	CStringArray results;
	CWinThread *pThread;
	BOOL bThreadDone;
	CMap<int, int, int, int> map;
	CDWordArray deleteList;
	int i;

	this->EnableWindow(FALSE);
	m_ProductListDialog->EnableWindow(FALSE);
	
	if (lParam == 0) {	// means they haven't selected anything
		// Commented out by Dylan Savage
		//if (AfxMessageBox("Are you sure you wish to delete all the products in the list?", MB_YESNO) != IDYES) {
			this->EnableWindow(TRUE);
			m_ProductListDialog->EnableWindow(TRUE);
			return;
		//}
		//lParam = m_Products.GetSize();
		//for (i=0; i < m_Products.GetSize(); ++i)
			//map.SetAt(i, i);
	} else {
		for (i=0; i < m_ProductListDialog->m_SelectionList.GetSize(); ++i) {
			int t = m_ProductListDialog->m_SelectionList[i];
			map.SetAt(m_ProductListDialog->m_SelectionList[i], m_ProductListDialog->m_SelectionList[i]);
		}
	}

	pProgress = new CProgress;
	pProgress->Create(IDD_PROGRESS, m_ProductListDialog);
	pProgress->CenterWindow();
	pProgress->ShowWindow(SW_SHOW);
	pProgress->UpdateWindow();
	pProgress->m_ProgressCtrl.SetRange32(0, lParam);
	pProgress->m_ProgressCtrl.SetPos(0);
	pProgress->m_ProgressCtrl.SetStep(1);
	pProgress->m_StatusTextCtrl.SetWindowText("Deleting products...");



	try {

		for (i=0; i < m_Products.GetSize(); ++i) {
			if (! map.Lookup(i, curSel))
				continue;

			results.RemoveAll();
			results.SetSize(2);
			results[0].Format("%d", m_Products[i]->m_ProductPackDBID);

			pThread = AfxBeginThread(DeleteProductThread, &results);
		
			bThreadDone = FALSE;
			while (TRUE) {
				if ( ! utilityHelper.PeekAndPump() )
					break;
				
				bThreadDone = g_ThreadDone.Lock(0);
				if (bThreadDone)
					break;
			}
			
			rc = atoi(results[0]);
			if (rc < 0) {
				AfxMessageBox(results[1]);
				pProgress->DestroyWindow();
				this->EnableWindow(TRUE);
				m_ProductListDialog->EnableWindow(TRUE);
				return;
			}

			deleteList.Add(i);

			pProgress->m_ProgressCtrl.StepIt();
			if (pProgress->m_Stopping)
				break;

		}
	}
	catch (Ssa_Exception e) {
		utilityHelper.ProcessError("Error deleting product.", &e);
		pProgress->DestroyWindow();
		this->EnableWindow(TRUE);
		m_ProductListDialog->EnableWindow(TRUE);
		return;
	}
	catch (...) {
		utilityHelper.ProcessError("Generic error deleting product.");
		pProgress->DestroyWindow();
		this->EnableWindow(TRUE);
		m_ProductListDialog->EnableWindow(TRUE);
		return;
	}


	for (i=deleteList.GetSize()-1; i >= 0; --i) {
		curSel = deleteList[i];
		delete m_Products[curSel];
		m_Products.RemoveAt(curSel);
		m_ProductListDialog->DeleteItem(curSel);
	}
	
	
	m_ProductListDialog->EnableWindow(TRUE);
	if (pProgress != NULL)
		pProgress->DestroyWindow();

	// Close the window if they delete all the products
	if (m_Products.GetSize() == 0) {
		AfxMessageBox("Products successfully deleted.");
		m_ProductListDialog->m_NextClosesWindow3 = TRUE;
	}
	else
		m_ProductListDialog->m_NextClosesWindow3 = FALSE;



	if (deleteList.GetSize() < lParam)
		AfxMessageBox("Not all products were deleted.");

	this->EnableWindow(TRUE);


}

UINT CDataPurificationDialog::DeleteProductThread(LPVOID pParam)
{
	CStringArray *pArray;
	long productID;
	int rc;
	CProductDataService productDataService;

	pArray = (CStringArray *)pParam;
	productID = atol(pArray->GetAt(0));

	try {
		rc = productDataService.DeleteProductByID(productID);

		(*pArray)[0].Format("%d", rc);
	}
	catch (Ssa_Exception e) {
		char msgBuf[1024];
		e.GetMessage(msgBuf);
		(CString(pArray->GetAt(0))).Format("%d", -1);
		(CString(pArray->GetAt(1))).Format("%s", msgBuf);
	}
	catch (...) {
		(CString(pArray->GetAt(0))).Format("%d", -1);
		(CString(pArray->GetAt(1))).Format("Generic error.");
	}

	g_ThreadDone.SetEvent();

	return rc;

}



void CDataPurificationDialog::OnHelp() 
{
	helpService.ShowScreenHelp(IDD);	
}

BOOL CDataPurificationDialog::OnHelpInfo(HELPINFO* pHelpInfo) 
{
	helpService.ShowFieldHelp(IDD, pHelpInfo->iCtrlId);
	return FALSE;
}

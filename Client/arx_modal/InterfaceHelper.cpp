// InterfaceHelper.cpp: implementation of the CInterfaceHelper class.
//
//////////////////////////////////////////////////////////////////////

#include "stdafx.h"
#include "modal.h"
#include "InterfaceHelper.h"
#include "AssignmentOutboundDialog.h"
#include "LocationOutboundDialog.h"
#include "SearchAnchorDialog.h"
#include "GenerateMovesDialog.h"
#include "ProductInterfaceDataService.h"
#include "InterfaceMapDialog.h"
#include "ForteService.h"
#include "WMSSheet.h"
#include "WMSImportPage.h"
#include "WMSExportPage.h"
#include "WMSGroupDialog.h"
#include "TreeElement.h"
#include "FacilityDataService.h"

#include "IntegrationStatusDialog.h"
#include "InterfaceProductFileConvertDialog.h"

#include "ControlService.h"

#include "ResourceHelper.h"

#include "base.h"
#include "MiscClasses.h"
#include "SLOTSessionMgr.h"
#include "SrvcObjs.h"

#include <aced.h>

#ifdef _DEBUG
#undef THIS_FILE
static char THIS_FILE[]=__FILE__;
#define new DEBUG_NEW
#endif

extern CForteService forteService;
extern CFacilityDataService facilityDataService;
extern TreeElement changesTree;
extern CControlService controlService;
//////////////////////////////////////////////////////////////////////
// Construction/Destruction
//////////////////////////////////////////////////////////////////////

CInterfaceHelper::CInterfaceHelper()
{

}

CInterfaceHelper::~CInterfaceHelper()
{

}

void CInterfaceHelper::ProductInbound()
{
	CWinApp * currentApp;
	currentApp = AfxGetApp();
	CStringArray records;

	if (AfxMessageBox("Are you sure you wish to start the product import?",MB_YESNO) == IDNO)
		return;
	ads_printf("Importing Products");

	currentApp->DoWaitCursor(1);
	try {
		CProductInterfaceDataService productInterfaceDataService;

		/*if (productInterfaceDataService.ProductInbound(records) != 0) {
			AfxMessageBox("Products were not imported successfully");
		*/
		CListstringPtr recordList = getSessionMgrSO()->ProductInboundHelper();
/*		if (  recordList->GetCount() != 0) {

			return;
		}
*/
		if (recordList->GetCount() > 0) {
			POSITION posSL = recordList->GetHeadPosition();
			for (int i=0; i<recordList->GetCount(); i++)
			{
				records.SetAtGrow(i, (recordList->GetNext(posSL)).c_str() );
			}
		}
	}
	catch(...) {
		currentApp->DoWaitCursor(-1);
		AfxMessageBox("Products were not imported successfully");
		return;
	}
	currentApp->DoWaitCursor(-1);
	AfxMessageBox("Product import complete");

	CDisplayResults dlg;

	dlg.m_Data.InsertAt(0, &records);
	dlg.m_AllowSort = TRUE;
	dlg.m_Headers.Add("Status|Line|Column|Information|Data|");
	dlg.m_WindowCaptions.Add("Product Inbound Results");
	dlg.m_Tabs.Add("Data");

	dlg.DoModal();

}



void CInterfaceHelper::SearchAnchorMaintenance()
{
	CSearchAnchorDialog dlg;

	dlg.DoModal();

	return;

}

void CInterfaceHelper::LocationOutbound()
{
	CLocationOutboundDialog dlg;

	dlg.DoModal();

}


void CInterfaceHelper::AssignmentOutbound()
{
	CAssignmentOutboundDialog dlg;
	
	dlg.DoModal();
}


void CInterfaceHelper::GenerateMoves()
{
	try {
		CGenerateMovesDialog dlg;
		dlg.DoModal();
	}
	catch (...) {
		AfxMessageBox("Error running generate moves.");
	}

}


void CInterfaceHelper::InterfaceMap()
{
	CInterfaceMapDialog dialog;

	try {
		dialog.DoModal();
	}
	catch (...) {
		AfxMessageBox("Error running Interface Map");

	}

	return;

}

void CInterfaceHelper::WMSSetup()
{

	CTemporaryResourceOverride tro;
	CWMSSheet dialog("WMS Integration Configuration", NULL, 0);
	dialog.m_psh.dwFlags |= PSH_NOAPPLYNOW;

	CWMSImportPage importPage;
	CWMSExportPage exportPage;

	dialog.AddPage(&importPage);
	dialog.AddPage(&exportPage);

	try {
		dialog.DoModal();
	}
	catch (...) {
		AfxMessageBox("Error running WMS Setup");
	}

	// Update the integration status of the current facility
	int rc = facilityDataService.IsFacilityIntegrated(changesTree.elementDBID);
	if (rc <= 0)
		facilityDataService.SetIntegrationStatus(FALSE);
	else
		facilityDataService.SetIntegrationStatus(TRUE);

	return;
}

void CInterfaceHelper::WMSGroup()
{
	CWMSGroupDialog dialog;

	try {
		dialog.DoModal();
	}
	catch (...) {
		AfxMessageBox("Error running WMS Facility Setup");
	}

}


void CInterfaceHelper::RunNewProductLayout()
{
	CSsaStringArray retArray;
	CDisplayResults dlg;
	CWaitCursor cwc;

	if (this->NewProductLayout(retArray) == 0) {
		for (int i=0; i < retArray.GetSize(); ++i) {
			dlg.m_Data.Add(retArray[i]);
		}

		dlg.m_Headers.Add("Product|WMS Product ID|WMS Detail ID|Location|");
		dlg.m_Tabs.Add("Assigned Products");
		dlg.m_WindowCaptions.Add("New Product Layout");
		dlg.DoModal();
	}
	else {
		AfxMessageBox("Error assigning new products.");
	}
	
	return;
}

int CInterfaceHelper::NewProductLayout(CSsaStringArray &retArray) 
{
	CString tempString;
	int tempInt;
	CSsaStringArray tempSendArray;
	CSsaStringArray tempRecvArray;
	CString sendString;
	int i = 0;

	/////////////////////////////////////////////////////////////
	// Build the data and send to Forte connection
	/////////////////////////////////////////////////////////////
	sendString = "<SIO>SLOTSocketString\n";
	tempSendArray.Add(sendString);
	sendString = "<SAI>StartPass1\n";
	tempSendArray.Add(sendString);
	sendString = "<EIO>SLOTSocketString\n";
	tempSendArray.Add(sendString);

	forteService.SendToForteConnection(tempSendArray,tempRecvArray,
		CString("SLOTSocketString"), 4070);

	for (i=0; i < tempRecvArray.GetSize(); i++) {
		if ( tempRecvArray[i].Find("<SAI>") != -1 ) {
			tempString = tempRecvArray[i].Mid(5);
			tempInt = tempString.GetLength();
			if (tempString.GetAt(tempInt-1) == '\n' ) {
				tempString.SetAt(tempInt-1,' ');
				tempString.TrimRight();
			}
			retArray.Add(tempString);
		}
	}

	if ( retArray.GetSize() > 0 )
		return 0;
	else {
		return 1;
	}
}

void CInterfaceHelper::WMSSync()
{
	CIntegrationStatusDialog dlg;

	try {
		dlg.DoModal();
	}
	catch (...) {
		controlService.Log("Error running WMS Synchronization.", "Generic exception in CIntegrationSyncDialog.");
	}
	
	return;
}

void CInterfaceHelper::ConvertProductFileToXML()
{
	CInterfaceProductFileConvertDialog dlg;

	try {
		dlg.DoModal();
	}
	catch (...) {
		controlService.Log("Error displaying file conversion dialog.", 
			"Generic exception in CInterfaceProductFileConvertDialog.\n");
	}

	return;
}

CString CInterfaceHelper::ConvertStatusToText(int status)
{
	CString temp;

	switch (status) {
	case NotSent:
		temp = "Not Integrated";
		break;
	case Sent:
		temp = "Pending";
		break;
	case Confirmed:
		temp = "Confirmed";
		break;
	case Rejected:
		temp = "Rejected";
		break;
	case -1:
		temp = "N/A";
		break;
	default:
		temp = "Unknown";
		break;
	}

	return temp;

}

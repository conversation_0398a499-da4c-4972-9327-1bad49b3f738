// FacingInfo.cpp: implementation of the CFacingInfo class.
//
//////////////////////////////////////////////////////////////////////

#include "stdafx.h"
#include "modal.h"
#include "FacingInfo.h"
#include "UtilityHelper.h"

#ifdef _DEBUG
#undef THIS_FILE
static char THIS_FILE[]=__FILE__;
#define new DEBUG_NEW
#endif

extern CUtilityHelper utilityHelper;

//////////////////////////////////////////////////////////////////////
// Construction/Destruction
//////////////////////////////////////////////////////////////////////

CFacingInfo::CFacingInfo()
{
	m_FacingInfoDBId = 0;
	m_BayRuleDBId = 0;
}

CFacingInfo::~CFacingInfo()
{

}

CFacingInfo::CFacingInfo(const CFacingInfo& other)
{
	m_FacingInfoDBId = other.m_FacingInfoDBId;
	m_Description = other.m_Description;
	m_ExtendedCube = other.m_ExtendedCube;
	m_ExtendedBOH = other.m_ExtendedBOH;
	m_FacingCount = other.m_FacingCount;
	m_BayRuleDBId = other.m_BayRuleDBId;
}


CFacingInfo& CFacingInfo::operator=(const CFacingInfo &other)
{
	m_FacingInfoDBId = other.m_FacingInfoDBId;
	m_Description = other.m_Description;
	m_ExtendedCube = other.m_ExtendedCube;
	m_ExtendedBOH = other.m_ExtendedBOH;
	m_FacingCount = other.m_FacingCount;
	m_BayRuleDBId = other.m_BayRuleDBId;
	
	return *this;
}

BOOL CFacingInfo::operator==(const CFacingInfo& other)
{
	if (m_FacingInfoDBId != other.m_FacingInfoDBId) return FALSE;
	if (m_Description != other.m_Description) return FALSE;
	if (m_ExtendedCube != other.m_ExtendedCube) return FALSE;
	if (m_ExtendedBOH != other.m_ExtendedBOH) return FALSE;
	if (m_FacingCount != other.m_FacingCount) return FALSE;
	if (m_BayRuleDBId != other.m_BayRuleDBId) return FALSE;
	
	return TRUE;
}

int CFacingInfo::Parse(CString &line)
{
	CStringArray strings;
	utilityHelper.ParseString(line, "|", strings);

	for (int i=0; i < strings.GetSize(); ++i) {
		switch (i) {
		case 0:
			m_FacingInfoDBId = atoi(strings[i]);
			break;
		case 1:
			m_Description = strings[i];
			break;
		case 2:
			m_ExtendedCube = atof(strings[i]);
			break;
		case 3:
			m_ExtendedBOH = atof(strings[i]);
			break;
		case 4:
			m_FacingCount = atoi(strings[i]);
			break;
		case 5:
			m_BayRuleDBId = atoi(strings[i]);
			break;
		}
	}

	return 0;
}
	

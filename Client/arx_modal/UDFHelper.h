// UDFHelper.h: interface for the CUDFHelper class.
//
//////////////////////////////////////////////////////////////////////

#if !defined(AFX_UDFHELPER_H__DE69EF6D_EAB6_4B51_AB4B_3988430CA428__INCLUDED_)
#define AFX_UDFHELPER_H__DE69EF6D_EAB6_4B51_AB4B_3988430CA428__INCLUDED_

#if _MSC_VER > 1000
#pragma once
#endif // _MSC_VER > 1000

class CUDFHelper  
{
public:
	CUDFHelper();
	virtual ~CUDFHelper();

	void UDFMaintenance();

};

#endif // !defined(AFX_UDFHELPER_H__DE69EF6D_EAB6_4B51_AB4B_3988430CA428__INCLUDED_)

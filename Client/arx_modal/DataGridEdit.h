#if !defined(AFX_DATAGRIDEDIT_H__0859ABE3_CC7D_11D4_9EC1_00C04FAC149C__INCLUDED_)
#define AFX_DATAGRIDEDIT_H__0859ABE3_CC7D_11D4_9EC1_00C04FAC149C__INCLUDED_

#if _MSC_VER > 1000
#pragma once
#endif // _MSC_VER > 1000
// DataGridEdit.h : header file
//

/////////////////////////////////////////////////////////////////////////////
// CDataGridEdit window

class CDataGridEdit : public CEdit
{
// Construction
public:
	CDataGridEdit();

// Attributes
public:

// Operations
public:

// Overrides
	// ClassWizard generated virtual function overrides
	//{{AFX_VIRTUAL(CDataGridEdit)
	//}}AFX_VIRTUAL

// Implementation
public:
	virtual ~CDataGridEdit();

	// Generated message map functions
protected:
	//{{AFX_MSG(CDataGridEdit)
	afx_msg void OnChar(UINT nChar, UINT nRepCnt, UINT nFlags);
	afx_msg void OnKeyDown(UINT nChar, UINT nRepCnt, UINT nFlags);
	afx_msg void OnKillFocus(CWnd* pNewWnd);
	afx_msg void OnSetFocus(CWnd* pOldWnd);
	afx_msg UINT OnGetDlgCode();
	afx_msg BOOL OnHelpInfo(HELPINFO* pHelpInfo);
	afx_msg void OnKeyUp(UINT nChar, UINT nRepCnt, UINT nFlags);
	//}}AFX_MSG

	DECLARE_MESSAGE_MAP()
};

/////////////////////////////////////////////////////////////////////////////

//{{AFX_INSERT_LOCATION}}
// Microsoft Visual C++ will insert additional declarations immediately before the previous line.

#endif // !defined(AFX_DATAGRIDEDIT_H__0859ABE3_CC7D_11D4_9EC1_00C04FAC149C__INCLUDED_)

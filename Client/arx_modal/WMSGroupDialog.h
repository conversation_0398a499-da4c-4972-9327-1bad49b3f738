#if !defined(AFX_WMSGROUPDIALOG_H__D8FC6562_38F7_48D7_AA25_140B49303F1F__INCLUDED_)
#define AFX_WMSGROUPDIALOG_H__D8FC6562_38F7_48D7_AA25_140B49303F1F__INCLUDED_

#if _MSC_VER > 1000
#pragma once
#endif // _MSC_VER > 1000
// WMSGroupDialog.h : header file
//
#include "WMSGroup.h"
#include "DragDropTreeCtrl.h"

/////////////////////////////////////////////////////////////////////////////
// CWMSGroupDialog dialog

class CWMSGroupDialog : public CDialog
{
// Construction
public:
	int OnDropItem(WPARAM wParam, LPARAM lParam);
	int LoadGroups();
	<PERSON>O<PERSON> m_SomethingChanged;

	CImageList m_CmdImageList;
	CImageList m_GroupImageList;
	CWMSGroupDialog(CWnd* pParent = NULL);   // standard constructor
	virtual ~CWMSGroupDialog();

// Dialog Data
	//{{AFX_DATA(CWMSGroupDialog)
	enum { IDD = IDD_WMS_GROUPS };
	CDragDropTreeCtrl	m_GroupTreeCtrl;
	CTreeCtrl	m_CommandTreeCtrl;
	//}}AFX_DATA


// Overrides
	// ClassWizard generated virtual function overrides
	//{{AFX_VIRTUAL(CWMSGroupDialog)
	protected:
	virtual void DoDataExchange(CDataExchange* pDX);    // DDX/DDV support
	//}}AFX_VIRTUAL

// Implementation
protected:

	// Generated message map functions
	//{{AFX_MSG(CWMSGroupDialog)
	virtual BOOL OnInitDialog();
	afx_msg void OnItemexpandedCommandTree(NMHDR* pNMHDR, LRESULT* pResult);
	afx_msg void OnSelchangedWmsGroupTree(NMHDR* pNMHDR, LRESULT* pResult);
	afx_msg void OnContextMenu(CWnd* pWnd, CPoint point);
	afx_msg void OnGenericAdd();
	afx_msg void OnGenericDelete();
	afx_msg void OnGenericProperties();
	afx_msg void OnSelchangingCommandTree(NMHDR* pNMHDR, LRESULT* pResult);
	afx_msg void OnDblclkWmsGroupTree(NMHDR* pNMHDR, LRESULT* pResult);
	afx_msg BOOL OnHelpInfo(HELPINFO* pHelpInfo);
	afx_msg void OnHelp();
	//}}AFX_MSG
	DECLARE_MESSAGE_MAP()
private:
	int MoveWMS(CWMS *pWMS, CWMSGroup *pFromGroup, CWMSGroup *pToGroup);
	HTREEITEM AddWMSToTree(CWMS *pWMS, HTREEITEM hItem);
	HTREEITEM AddGroupToTree(CWMSGroup *pGroup);
	int SelectedType();
	void DeleteWMS();
	void DeleteGroup();
	void EditWMS();
	void EditGroup();
	void AddWMS();
	void AddGroup();
	void CollapseCommandTree();
	void ExpandCommandTree();
	HTREEITEM m_RootCmdTreeItem;
	HTREEITEM m_DeleteCmdTreeItem;
	HTREEITEM m_EditCmdTreeItem;
	HTREEITEM m_AddGroupCmdTreeItem;
	HTREEITEM m_AddWMSCmdTreeItem;

	typedef enum {
		noneSelected,
		groupSelected,
		wmsSelected
	} selectionType;
	CTypedPtrArray<CObArray, CWMS*> m_WMSList;
	CTypedPtrArray<CObArray, CWMSGroup*> m_GroupList;
};

//{{AFX_INSERT_LOCATION}}
// Microsoft Visual C++ will insert additional declarations immediately before the previous line.

#endif // !defined(AFX_WMSGROUPDIALOG_H__D8FC6562_38F7_48D7_AA25_140B49303F1F__INCLUDED_)

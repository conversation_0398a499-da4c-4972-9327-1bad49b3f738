// DataAccessService.h: interface for the CDataAccessService class.
//
//////////////////////////////////////////////////////////////////////

#if !defined(AFX_DATAACCESSSERVICE_H__275ECDB0_A486_4F1F_A718_4663279317D9__INCLUDED_)
#define AFX_DATAACCESSSERVICE_H__275ECDB0_A486_4F1F_A718_4663279317D9__INCLUDED_

#if _MSC_VER > 1000
#pragma once
#endif // _MSC_VER > 1000

#include "UserQuery.h"
#include <sql.h>

class CDataAccessService  
{
public:
	int ExecuteODBCQuery(const CString &queryName, const CString &query, CStringArray &results);
	CDataAccessService();
	virtual ~CDataAccessService();

	int ExecuteStatements(const CString &statementName, CStringArray &sqlArray);
	int ExecuteStatement(const CString &statementName, const CString &sqlText);
	int ExecuteQuery(const CString &queryName, const CString &query, CStringArray &results, BOOL trimTrailingDelimiter = FALSE);
	int GetNextKey(const CString &tableName, int pNumberOfKeys);

	static UINT ExecuteQueryThread(LPVOID pParam);
	static UINT ExecuteStatementsThread(LPVOID pParam);
};

#endif // !defined(AFX_DATAACCESSSERVICE_H__275ECDB0_A486_4F1F_A718_4663279317D9__INCLUDED_)

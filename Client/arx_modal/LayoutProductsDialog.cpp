// LayoutProductsDialog.cpp : implementation file
//

#include "stdafx.h"
#include "modal.h"
#include "LayoutProductsDialog.h"
#include "HelpService.h"

#ifdef _DEBUG
#define new DEBUG_NEW
#undef THIS_FILE
static char THIS_FILE[] = __FILE__;
#endif

/////////////////////////////////////////////////////////////////////////////
// CLayoutProductsDialog dialog

extern CHelpService helpService;


CLayoutProductsDialog::CLayoutProductsDialog(CWnd* pParent /*=NULL*/)
	: CDialog(CLayoutProductsDialog::IDD, pParent)
{
	//{{AFX_DATA_INIT(CLayoutProductsDialog)
	m_ConstrainAmount = 0.0f;
	m_ConstrainType = -1;
	m_LayoutType = -1;
	m_Overlap = FALSE;
	m_Reorient = FALSE;
	m_VarWidth = FALSE;
	//}}AFX_DATA_INIT
}


void CLayoutProductsDialog::DoDataExchange(CDataExchange* pDX)
{
	CDialog::DoDataExchange(pDX);
	//{{AFX_DATA_MAP(CLayoutProductsDialog)
	DDX_Text(pDX, IDC_CONSTRAIN_AMOUNT, m_ConstrainAmount);
	DDX_CBIndex(pDX, IDC_CONSTRAIN_TYPE, m_ConstrainType);
	DDX_CBIndex(pDX, IDC_LAYOUT_TYPE, m_LayoutType);
	DDX_Check(pDX, IDC_OVERLAP, m_Overlap);
	DDX_Check(pDX, IDC_REORIENT, m_Reorient);
	DDX_Check(pDX, IDC_VARWIDTH, m_VarWidth);
	//}}AFX_DATA_MAP
}


BEGIN_MESSAGE_MAP(CLayoutProductsDialog, CDialog)
	//{{AFX_MSG_MAP(CLayoutProductsDialog)
	ON_CBN_SELCHANGE(IDC_LAYOUT_TYPE, OnSelchangeLayoutType)
	ON_BN_CLICKED(IDHELP, OnHelp)
	ON_WM_HELPINFO()
	//}}AFX_MSG_MAP
END_MESSAGE_MAP()

/////////////////////////////////////////////////////////////////////////////
// CLayoutProductsDialog message handlers

void CLayoutProductsDialog::OnSelchangeLayoutType() 
{
	// TODO: Add your control notification handler code here
	CComboBox *pLayoutTypeCombo = (CComboBox *)GetDlgItem(IDC_LAYOUT_TYPE);
	CString text;
	pLayoutTypeCombo->GetWindowText(text);
	
	if (text == "Tactical") {
		CComboBox *pConstrainTypeCombo = (CComboBox *)GetDlgItem(IDC_CONSTRAIN_TYPE);
		pConstrainTypeCombo->EnableWindow(TRUE);
		if (pConstrainTypeCombo->GetCurSel() == CB_ERR)
			pConstrainTypeCombo->SetCurSel(1);
		CEdit *pConstrainAmountEdit = (CEdit *)GetDlgItem(IDC_CONSTRAIN_AMOUNT);
		pConstrainAmountEdit->EnableWindow(TRUE);
	}
	else {
		CComboBox *pConstrainTypeCombo = (CComboBox *)GetDlgItem(IDC_CONSTRAIN_TYPE);
		pConstrainTypeCombo->EnableWindow(FALSE);
		
		CEdit *pConstrainAmountEdit = (CEdit *)GetDlgItem(IDC_CONSTRAIN_AMOUNT);
		pConstrainAmountEdit->EnableWindow(FALSE);
	}

	if (text == "Strategic") {
		(CButton *)GetDlgItem(IDC_VARWIDTH)->EnableWindow(TRUE);
		(CButton *)GetDlgItem(IDC_REORIENT)->EnableWindow(TRUE);
		(CButton *)GetDlgItem(IDC_OVERLAP)->EnableWindow(TRUE);
	}
	else {
		(CButton *)GetDlgItem(IDC_VARWIDTH)->EnableWindow(FALSE);
		(CButton *)GetDlgItem(IDC_REORIENT)->EnableWindow(FALSE);
		(CButton *)GetDlgItem(IDC_OVERLAP)->EnableWindow(FALSE);
	}
}

BOOL CLayoutProductsDialog::OnInitDialog() 
{
	CDialog::OnInitDialog();
	
	CRect r;

	// TODO: Add extra initialization here
	CComboBox *pLayoutTypeCombo = (CComboBox *)GetDlgItem(IDC_LAYOUT_TYPE);
	pLayoutTypeCombo->SetItemHeight(0,2000);
	pLayoutTypeCombo->SetCurSel(2);		// default to strategic
	pLayoutTypeCombo->GetWindowRect(&r);
	pLayoutTypeCombo->SetWindowPos(NULL, 0, 0, r.Width(), r.Height()*5, SWP_NOMOVE|SWP_NOZORDER);


	CComboBox *pConstrainTypeCombo = (CComboBox *)GetDlgItem(IDC_CONSTRAIN_TYPE);
	pConstrainTypeCombo->SetItemHeight(0,2000);
	pConstrainTypeCombo->EnableWindow(FALSE);
	pConstrainTypeCombo->GetWindowRect(&r);
	pConstrainTypeCombo->SetWindowPos(NULL, 0, 0, r.Width(), r.Height()*3, SWP_NOMOVE|SWP_NOZORDER);

	CEdit *pConstrainAmountEdit = (CEdit *)GetDlgItem(IDC_CONSTRAIN_AMOUNT);
	pConstrainAmountEdit->EnableWindow(FALSE);

	
	return FALSE;  // return TRUE unless you set the focus to a control
	              // EXCEPTION: OCX Property Pages should return FALSE
}

void CLayoutProductsDialog::OnOK() 
{
	// TODO: Add extra validation here
	UpdateData(TRUE);
	CDialog::OnOK();
}

BOOL CLayoutProductsDialog::OnHelpInfo(HELPINFO* pHelpInfo) 
{
	helpService.ShowFieldHelp(IDD, pHelpInfo->iCtrlId);

	return FALSE;

}


void CLayoutProductsDialog::OnHelp() 
{
	helpService.ShowScreenHelp(IDD);

	return;
}


//{{AFX_INCLUDES()
#include "DataGrid.h"
//}}AFX_INCLUDES
#if !defined(AFX_DataModelDialog_H__68C1A244_CC5A_11D4_9EC1_00C04FAC149C__INCLUDED_)
#define AFX_DataModelDialog_H__68C1A244_CC5A_11D4_9EC1_00C04FAC149C__INCLUDED_

#if _MSC_VER > 1000
#pragma once
#endif // _MSC_VER > 1000
// DataModelDialog.h : header file
//
#include "Resource.h"
#include "ProductDataService.h"

/////////////////////////////////////////////////////////////////////////////
// CDataModelDialog dialog

class CDataModelDialog : public CDialog
{
// Construction
public:
	CDataModelDialog(CWnd* pParent = NULL);   // standard constructor
//	afx_msg void OnLeaveCellDataGrid();
	

// Dialog Data
	//{{AFX_DATA(CDataModelDialog)
	enum { IDD = IDD_DATA_MODEL };
	CString	m_Quantity;
	CDataGrid	m_DataGrid;
	//}}AFX_DATA


// Overrides
	// ClassWizard generated virtual function overrides
	//{{AFX_VIRTUAL(CDataModelDialog)
	protected:
	virtual void DoDataExchange(CDataExchange* pDX);    // DDX/DDV support
	//}}AFX_VIRTUAL

// Implementation
protected:

	// Generated message map functions
	//{{AFX_MSG(CDataModelDialog)
	virtual BOOL OnInitDialog();
	virtual void OnOK();
	afx_msg void OnSize(UINT nType, int cx, int cy);
	afx_msg BOOL OnHelpInfo(HELPINFO* pHelpInfo);
	afx_msg void OnHelp();
	DECLARE_EVENTSINK_MAP()
	//}}AFX_MSG
	DECLARE_MESSAGE_MAP()
private:
	int m_ParentOldHeight;
	int m_ParentOldWidth;
	void ModelProducts();
	BOOL ValidateCells();
	void LoadGrid();
	void SetAttribute(int row, CString name, int type, double min, double max, 
		CString initial, CStringArray &listValues, int attributeID, CString helpTopic);
	CProductDataService m_ProductDataService;
};

//{{AFX_INSERT_LOCATION}}
// Microsoft Visual C++ will insert additional declarations immediately before the previous line.

#endif // !defined(AFX_DataModelDialog_H__68C1A244_CC5A_11D4_9EC1_00C04FAC149C__INCLUDED_)

// AisleDataService.h: interface for the CAisleDataService class.
//
//////////////////////////////////////////////////////////////////////

#if !defined(AFX_AISLEDATASERVICE_H__64D5EC30_D773_46C1_A32D_C79195C24DF7__INCLUDED_)
#define AFX_AISLEDATASERVICE_H__64D5EC30_D773_46C1_A32D_C79195C24DF7__INCLUDED_

#if _MSC_VER > 1000
#pragma once
#endif // _MSC_VER > 1000

class CAisleDataService  
{
public:
	CAisleDataService();
	virtual ~CAisleDataService();

};

#endif // !defined(AFX_AISLEDATASERVICE_H__64D5EC30_D773_46C1_A32D_C79195C24DF7__INCLUDED_)

#if !defined(AFX_INTEGRATIONSYNCDIALOG_H__D6AAE500_A79A_4778_89FF_A71E30E0267E__INCLUDED_)
#define AFX_INTEGRATIONSYNCDIALOG_H__D6AAE500_A79A_4778_89FF_A71E30E0267E__INCLUDED_

#include "WMS.h"

#if _MSC_VER > 1000
#pragma once
#endif // _MSC_VER > 1000
// IntegrationSyncDialog.h : header file
//

/////////////////////////////////////////////////////////////////////////////
// CIntegrationSyncDialog dialog

class CIntegrationSyncDialog : public CDialog
{
// Construction
public:
	CIntegrationSyncDialog(CWnd* pParent = NULL);   // standard constructor
	~CIntegrationSyncDialog();
	CMap<int, int, CWMS*, CWMS*> m_WMSGroupMap;

// Dialog Data
	//{{AFX_DATA(CIntegrationSyncDialog)
	enum { IDD = IDD_INTEGRATION_SYNC };
	CComboBox	m_WMSListCtrl;
	//}}AFX_DATA


// Overrides
	// ClassWizard generated virtual function overrides
	//{{AFX_VIRTUAL(CIntegrationSyncDialog)
	protected:
	virtual void DoDataExchange(CDataExchange* pDX);    // DDX/DDV support
	//}}AFX_VIRTUAL

// Implementation
protected:

	// Generated message map functions
	//{{AFX_MSG(CIntegrationSyncDialog)
	virtual BOOL OnInitDialog();
	virtual void OnOK();
	afx_msg BOOL OnHelpInfo(HELPINFO* pHelpInfo);
	//}}AFX_MSG
	DECLARE_MESSAGE_MAP()

private:


};

//{{AFX_INSERT_LOCATION}}
// Microsoft Visual C++ will insert additional declarations immediately before the previous line.

#endif // !defined(AFX_INTEGRATIONSYNCDIALOG_H__D6AAE500_A79A_4778_89FF_A71E30E0267E__INCLUDED_)

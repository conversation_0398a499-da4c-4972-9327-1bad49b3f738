#if !defined(AFX_WMSPROPERTIES_H__70090739_9BBE_4B9E_90DD_08EE0F3A6F8F__INCLUDED_)
#define AFX_WMSPROPERTIES_H__70090739_9BBE_4B9E_90DD_08EE0F3A6F8F__INCLUDED_

#if _MSC_VER > 1000
#pragma once
#endif // _MSC_VER > 1000
// WMSProperties.h : header file
//
#include "WMS.h"
/////////////////////////////////////////////////////////////////////////////
// CWMSProperties dialog

class CWMSProperties : public CDialog
{
// Construction
public:
	CWMSProperties(CWnd* pParent = NULL);   // standard constructor
	int m_GroupDBId;

// Dialog Data
	//{{AFX_DATA(CWMSProperties)
	enum { IDD = IDD_WMS_PROPERTIES };
	CString	m_Description;
	CString	m_Name;
	CString	m_WMSId;
	CString	m_IdText;
	//}}AFX_DATA
	CWMS m_WMS;

// Overrides
	// ClassWizard generated virtual function overrides
	//{{AFX_VIRTUAL(CWMSProperties)
	protected:
	virtual void DoDataExchange(CDataExchange* pDX);    // DDX/DDV support
	//}}AFX_VIRTUAL

// Implementation
protected:

	// Generated message map functions
	//{{AFX_MSG(CWMSProperties)
	virtual void OnOK();
	virtual BOOL OnInitDialog();
	afx_msg BOOL OnHelpInfo(HELPINFO* pHelpInfo);
	afx_msg void OnHelp();
	//}}AFX_MSG
	DECLARE_MESSAGE_MAP()
};

//{{AFX_INSERT_LOCATION}}
// Microsoft Visual C++ will insert additional declarations immediately before the previous line.

#endif // !defined(AFX_WMSPROPERTIES_H__70090739_9BBE_4B9E_90DD_08EE0F3A6F8F__INCLUDED_)

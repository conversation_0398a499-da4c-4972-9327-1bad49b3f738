#if !defined(AFX_PICKPATHOPTIONDIALOG_H__23412B51_0521_11D2_9BD3_0080C781D9DF__INCLUDED_)
#define AFX_PICKPATHOPTIONDIALOG_H__23412B51_0521_11D2_9BD3_0080C781D9DF__INCLUDED_

#if _MSC_VER >= 1000
#pragma once
#endif // _MSC_VER >= 1000
// PickPathOptionDialog.h : header file
//

/////////////////////////////////////////////////////////////////////////////
// CPickPathOptionDialog dialog

class CPickPathOptionDialog : public CDialog
{
// Construction
public:
	CPickPathOptionDialog(CWnd* pParent = NULL);   // standard constructor

// Dialog Data
	//{{AFX_DATA(CPickPathOptionDialog)
	enum { IDD = IDD_PICKPATH_OPTION };
	//}}AFX_DATA


// Overrides
	// ClassWizard generated virtual function overrides
	//{{AFX_VIRTUAL(CPickPathOptionDialog)
	protected:
	virtual void DoDataExchange(CDataExchange* pDX);    // DDX/DDV support
	//}}AFX_VIRTUAL

// Implementation
protected:

	// Generated message map functions
	//{{AFX_MSG(CPickPathOptionDialog)
	afx_msg void OnPickpathOptionHelp();
	afx_msg BOOL OnHelpInfo(HELPINFO* pHelpInfo);
	afx_msg void OnConnect();
	afx_msg void OnNew();
	//}}AFX_MSG
	DECLARE_MESSAGE_MAP()
};

//{{AFX_INSERT_LOCATION}}
// Microsoft Developer Studio will insert additional declarations immediately before the previous line.

#endif // !defined(AFX_PICKPATHOPTIONDIALOG_H__23412B51_0521_11D2_9BD3_0080C781D9DF__INCLUDED_)

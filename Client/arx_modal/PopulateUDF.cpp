// PopulateUDF.cpp : implementation file
//

#include "stdafx.h"
#include "modal.h"
#include "PopulateUDF.h"
#include "ProductDataService.h"
#include "ProductGroupDataService.h"
#include "ssa_exception.h"
#include "ControlService.h"
#include "HelpService.h"

#ifdef _DEBUG
#define new DEBUG_NEW
#undef THIS_FILE
static char THIS_FILE[] = __FILE__;
#endif


/////////////////////////////////////////////////////////////////////////////
// CPopulateUDF dialog
extern CControlService controlService;
extern CHelpService helpService;

CPopulateUDF::CPopulateUDF(CWnd* pParent /*=NULL*/)
	: CDialog(CPopulateUDF::IDD, pParent)
{
	//{{AFX_DATA_INIT(CPopulateUDF)
	m_Formula = _T("");
	//}}AFX_DATA_INIT
}


void CPopulateUDF::DoDataExchange(CDataExchange* pDX)
{
	CDialog::DoDataExchange(pDX);
	//{{AFX_DATA_MAP(CPopulateUDF)
	DDX_Control(pDX, IDC_ATTRIBUTE_LIST, m_AttributeListCtrl);
	DDX_Control(pDX, IDC_UDF_LIST, m_UDFListCtrl);
	DDX_Text(pDX, IDC_FORMULA, m_Formula);
	//}}AFX_DATA_MAP
}


BEGIN_MESSAGE_MAP(CPopulateUDF, CDialog)
	//{{AFX_MSG_MAP(CPopulateUDF)
	ON_WM_HELPINFO()
	ON_BN_CLICKED(IDHELP, OnHelp)
	//}}AFX_MSG_MAP
END_MESSAGE_MAP()

/////////////////////////////////////////////////////////////////////////////
// CPopulateUDF message handlers

BOOL CPopulateUDF::OnInitDialog() 
{
	CDialog::OnInitDialog();
	
	CProductGroupDataService dataService;
	CProductDataService productDataService;

	CRect r;
	int rc, i;
	CStringArray attributes;
	CProductAttribute attribute;

	try {
		rc = productDataService.GetProductAttributes(controlService.GetCurrentFacilityDBId(), attributes);
	}
	catch(Ssa_Exception e) {
		throw e;
		return TRUE;
	}
	catch(...) {
		throw Ssa_Exception("Generic error getting product attributes", __FILE__, __LINE__, 200);
		return TRUE;
	}

	for (i=0; i < attributes.GetSize(); ++i) {
		attribute.Parse(attributes[i]);

		if (attribute.m_AttributeDBID > 0) {	// it's a udf
			int nItem = m_UDFListCtrl.AddString(attribute.m_Name);
			m_UDFListCtrl.SetItemData(nItem, attribute.m_AttributeDBID);
		}

		m_AttributeListCtrl.AddString(attribute.m_Name);

	}

	m_UDFListCtrl.GetWindowRect(&r);
	m_UDFListCtrl.SetWindowPos(NULL, 0, 0, r.Width(), r.Height()*10, SWP_NOMOVE|SWP_NOZORDER);

	m_AttributeListCtrl.GetWindowRect(&r);
	m_AttributeListCtrl.SetWindowPos(NULL, 0, 0, r.Width(), r.Height()*10, SWP_NOMOVE|SWP_NOZORDER);


	UpdateData(FALSE);

	return TRUE;  // return TRUE unless you set the focus to a control
	              // EXCEPTION: OCX Property Pages should return FALSE
}

void CPopulateUDF::OnOK() 
{
	UpdateData(TRUE);
	CProductGroupDataService service;
	BOOL rc;
	CString attributes, temp;

	if (m_UDFListCtrl.GetCurSel() == CB_ERR) {
		AfxMessageBox("Please select the UDF to which you wish to assign values.");
		m_UDFListCtrl.SetFocus();
		m_UDFListCtrl.ShowDropDown(TRUE);
		return;
	}


	try {
		rc = service.ValidateFormula(m_Formula, attributes);
	}
	catch (...) {
		rc = FALSE;
	}
	
	if (! rc) {
		temp.Format("The formula is not valid.  The following matches were found:\n%s", attributes);\
		AfxMessageBox(temp, MB_ICONERROR);
		return;
	}
	else
		AfxMessageBox("The formula is valid.", MB_ICONINFORMATION);
	
	CWaitCursor cwc;

	if (service.UpdateUDFWithFormula(m_Formula, m_UDFListCtrl.GetItemData(m_UDFListCtrl.GetCurSel()))) {
		AfxMessageBox("Unable to update the UDF with the formula.");
		return;
	}

	AfxMessageBox("UDF was successfully updated.");



//	CDialog::OnOK();
}

BOOL CPopulateUDF::OnHelpInfo(HELPINFO* pHelpInfo) 
{
	helpService.ShowFieldHelp(IDD, pHelpInfo->iCtrlId);

	return FALSE;

}

void CPopulateUDF::OnHelp() 
{
	helpService.ShowScreenHelp(IDD);

	return;

}

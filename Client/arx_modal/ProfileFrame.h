#if !defined(AFX_PROFILEFRAME_H__A1BF623C_73A2_453A_9874_28D6270E5074__INCLUDED_)
#define AFX_PROFILEFRAME_H__A1BF623C_73A2_453A_9874_28D6270E5074__INCLUDED_

#if _MSC_VER > 1000
#pragma once
#endif // _MSC_VER > 1000
// ProfileFrame.h : header file
//

/////////////////////////////////////////////////////////////////////////////
// CProfileFrame frame

class CProfileFrame : public CFrameWnd
{
	DECLARE_DYNCREATE(CProfileFrame)
public:
	CProfileFrame();           // protected constructor used by dynamic creation

// Attributes
public:
	CSplitterWnd m_Splitter;

// Operations
public:
// Overrides
	// ClassWizard generated virtual function overrides
	//{{AFX_VIRTUAL(CProfileFrame)
	protected:
	virtual BOOL OnCreateClient(LPCREATESTRUCT lpcs, CCreateContext* pContext);
	//}}AFX_VIRTUAL

// Implementation
protected:
public:
	virtual ~CProfileFrame();

	// Generated message map functions
	//{{AFX_MSG(CProfileFrame)
		// NOTE - the ClassWizard will add and remove member functions here.
	//}}AFX_MSG
	DECLARE_MESSAGE_MAP()
};

/////////////////////////////////////////////////////////////////////////////

//{{AFX_INSERT_LOCATION}}
// Microsoft Visual C++ will insert additional declarations immediately before the previous line.

#endif // !defined(AFX_PROFILEFRAME_H__A1BF623C_73A2_453A_9874_28D6270E5074__INCLUDED_)

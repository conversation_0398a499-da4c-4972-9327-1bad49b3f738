// 3DPoint.cpp: implementation of the C3DPoint class.
//
//////////////////////////////////////////////////////////////////////

#include "stdafx.h"
#include "modal.h"
#include "3DPoint.h"
#include "Constants.h"

#include <math.h>

#ifdef _DEBUG
#undef THIS_FILE
static char THIS_FILE[]=__FILE__;
#define new DEBUG_NEW
#endif

//////////////////////////////////////////////////////////////////////
// Construction/Destruction
//////////////////////////////////////////////////////////////////////

C3DPoint::C3DPoint()
{

}

C3DPoint::~C3DPoint()
{

}

BOOL C3DPoint::operator==(const C3DPoint& other)
{
	if (m_X != other.m_X) return FALSE;
	if (m_Y != other.m_Y) return FALSE;
	if (m_Z != other.m_Z) return FALSE;

	return TRUE;
}

C3DPoint& C3DPoint::operator+=(const C3DPoint& other)
{
	m_X += other.m_X;
	m_Y += other.m_Y;
	m_Z += other.m_Z;

	return *this;
}

void C3DPoint::Rotate(double angle)
{
	double x, y;
	x = m_X;
	y = m_Y;

	double radians = PI/180 * angle;

	m_X = x * cos(radians) - y * sin(radians);
	m_Y = x * sin(radians) + y * cos(radians);

}

C3DPoint C3DPoint::RotatedPoint(double angle)
{
	C3DPoint pt;

	double radians = PI/180 * angle;

	pt.m_X = m_X * cos(radians) - m_Y * sin(radians);
	pt.m_Y = m_X * sin(radians) + m_Y * cos(radians);
	pt.m_Z = m_Z;

	return pt;

}

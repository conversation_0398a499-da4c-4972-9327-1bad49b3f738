#if !defined(AFX_DEFINECONNECTIONDIALOG_H__E8C60E82_5A66_4DC0_B592_D67EA9398FAC__INCLUDED_)
#define AFX_DEFINECONNECTIONDIALOG_H__E8C60E82_5A66_4DC0_B592_D67EA9398FAC__INCLUDED_

#if _MSC_VER > 1000
#pragma once
#endif // _MSC_VER > 1000
// DefineConnectionDialog.h : header file
//
#include "ExternalConnection.h"

/////////////////////////////////////////////////////////////////////////////
// CDefineConnectionDialog dialog

class CDefineConnectionDialog : public CDialog
{
// Construction
public:
	CExternalConnection *m_pConnection;

	void OffsetControl(int id, int offset);
	void ShowPrompt();
	void ShowLocal();
	void ShowFTP();
	void ShowMQ();
	void HideLocal();
	void HideFTP();
	void HideMQ();
	CDefineConnectionDialog(CWnd* pParent = NULL);   // standard constructor

// Dialog Data
	//{{AFX_DATA(CDefineConnectionDialog)
	enum { IDD = IDD_INTEGRATION_CONNECTION };
	CComboBox	m_TransportCtrl;
	CString	m_Channel;
	CString	m_ConfirmPassword;
	CString	m_FileName;
	CString	m_Host;
	CString	m_Login;
	CString	m_Password;
	CString	m_Path;
	CString	m_Port;
	CString	m_QueueManager;
	CString	m_TriggerName;
	CString	m_Name;
	CString	m_Queue;
	//}}AFX_DATA


// Overrides
	// ClassWizard generated virtual function overrides
	//{{AFX_VIRTUAL(CDefineConnectionDialog)
	protected:
	virtual void DoDataExchange(CDataExchange* pDX);    // DDX/DDV support
	//}}AFX_VIRTUAL

// Implementation
protected:

	// Generated message map functions
	//{{AFX_MSG(CDefineConnectionDialog)
	virtual BOOL OnInitDialog();
	afx_msg void OnSelchangeTransport();
	afx_msg void OnTest();
	virtual void OnOK();
	afx_msg BOOL OnHelpInfo(HELPINFO* pHelpInfo);
	afx_msg void OnHelp();
	//}}AFX_MSG
	DECLARE_MESSAGE_MAP()
};

//{{AFX_INSERT_LOCATION}}
// Microsoft Visual C++ will insert additional declarations immediately before the previous line.

#endif // !defined(AFX_DEFINECONNECTIONDIALOG_H__E8C60E82_5A66_4DC0_B592_D67EA9398FAC__INCLUDED_)

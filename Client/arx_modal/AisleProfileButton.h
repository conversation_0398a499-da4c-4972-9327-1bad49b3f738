#if !defined(AFX_AISLEPROFILEBUTTON_H__6D313D6B_4B98_402E_A5C7_819FA0BE22B7__INCLUDED_)
#define AFX_AISLEPROFILEBUTTON_H__6D313D6B_4B98_402E_A5C7_819FA0BE22B7__INCLUDED_

#if _MSC_VER > 1000
#pragma once
#endif // _MSC_VER > 1000
// AisleProfileButton.h : header file
//

/////////////////////////////////////////////////////////////////////////////
// CAisleProfileButton window

class CAisleProfileButton : public CButton
{
// Construction
public:
	CAisleProfileButton();

// Attributes
public:

// Operations
public:

// Overrides
	// ClassWizard generated virtual function overrides
	//{{AFX_VIRTUAL(CAisleProfileButton)
	public:
	virtual void DrawItem(LPDRAWITEMSTRUCT lpDrawItemStruct);
	//}}AFX_VIRTUAL

// Implementation
public:
	virtual ~CAisleProfileButton();

	// Generated message map functions
protected:
	//{{AFX_MSG(CAisleProfileButton)
		// NOTE - the ClassWizard will add and remove member functions here.
	//}}AFX_MSG

	DECLARE_MESSAGE_MAP()
};

/////////////////////////////////////////////////////////////////////////////

//{{AFX_INSERT_LOCATION}}
// Microsoft Visual C++ will insert additional declarations immediately before the previous line.

#endif // !defined(AFX_AISLEPROFILEBUTTON_H__6D313D6B_4B98_402E_A5C7_819FA0BE22B7__INCLUDED_)

// InterfaceMapDataService.cpp: implementation of the CInterfaceMapDataService class.
//
//////////////////////////////////////////////////////////////////////

#include "stdafx.h"
#include "modal.h"
#include "InterfaceMapDataService.h"
#include "DataAccessService.h"

#ifdef _DEBUG
#undef THIS_FILE
static char THIS_FILE[]=__FILE__;
#define new DEBUG_NEW
#endif

//////////////////////////////////////////////////////////////////////
// Construction/Destruction
//////////////////////////////////////////////////////////////////////

CInterfaceMapDataService::CInterfaceMapDataService()
{

}

CInterfaceMapDataService::~CInterfaceMapDataService()
{

}

int CInterfaceMapDataService::GetInterfaceMapList(int type, CStringArray &mapList)
{
	CString query;
	CDataAccessService dataAccessService;

	query.Format("select DBInterfaceMapID, DBInterfaceMap.Name, IsNamed, "
		"FormatType, Delimiter, "
		"DBInterfaceMap.DBInterfaceTypeID "
		"from DBInterfaceMap, DBInterfaceType "
		"where type = %d "
		"and languageid = 0 "
		"and DBInterfaceMap.DBInterfaceTypeID = DBInterfaceType.DBInterfaceTypeID", type);

	return dataAccessService.ExecuteQuery("GetInterfaceMapList", query, mapList);

}

int CInterfaceMapDataService::GetInterfaceMapAttributes(long interfaceMapID, CStringArray &mapAttributes)
{
	CString query;
	CDataAccessService dataAccessService;

	query.Format("select DBInterfaceMapAttrID, InternalAttribute, ExternalAttribute, IsConstant, "
		"DataType, IsUDF, UDFElementType "
		"from DBInterfaceMapAttr "
		"where DBInterfaceMapID = %d "
		"order by DBInterfaceMapAttrID", interfaceMapID);

	return dataAccessService.ExecuteQuery("GetInterfaceMapAttributes", query, mapAttributes);
	
}

int CInterfaceMapDataService::StoreInterfaceMap(CInterfaceMap &interfaceMap)
{
	CString statement;
	CStringArray statements;
	CDataAccessService das;
	int nextKey;

	if (interfaceMap.m_InterfaceMapDBID > 0) {	// update

		statement.Format("delete from DBInterfaceMapAttr "
			"where DBInterfaceMapID = %d", interfaceMap.m_InterfaceMapDBID);
		statements.Add(statement);

		statement.Format("update DBInterfaceMap set "
			"Name = '%s', IsNamed = %d, FormatType = %d, Delimiter = '%s' "
			"where DBInterfaceMapID = %d", 
			interfaceMap.m_Name, interfaceMap.m_IsNamed, interfaceMap.m_FormatType,
			interfaceMap.m_Delimiter,
			interfaceMap.m_InterfaceMapDBID);
	}

	else {	// new map
	
		try {
			interfaceMap.m_InterfaceMapDBID = das.GetNextKey("DBInterfaceMap", 1);\
		}
		catch (...) {
			return -2;			// unable to get key
		}
		
		statement.Format("insert into DBInterfaceMap ("
			"DBInterfaceMapID, Name, IsNamed, FormatType, Delimiter, DBInterfaceTypeID) values ("
			"%d, '%s', %d, %d, '%s', %d)",
			interfaceMap.m_InterfaceMapDBID,
			interfaceMap.m_Name,
			interfaceMap.m_IsNamed,
			interfaceMap.m_FormatType,
			(interfaceMap.m_Delimiter == "|") ? "pipe" : interfaceMap.m_Delimiter,
			interfaceMap.m_InterfaceMapTypeDBID);
		
		statements.Add(statement);
	}

	nextKey = das.GetNextKey("DBInterfaceMapAttr", interfaceMap.m_MappedAttributeList.GetSize());
	
	for (int i=0; i < interfaceMap.m_MappedAttributeList.GetSize(); ++i) {
		
		CInterfaceMapAttribute *attr = interfaceMap.m_MappedAttributeList[i];
		attr->m_InterfaceMapAttributeDBID = nextKey;

		statement.Format("insert into DBInterfaceMapAttr ( "
			"DBInterfaceMapAttrID, InternalAttribute, ExternalAttribute, "
			"IsConstant, DataType, IsUDF, UDFElementType, DBInterfaceMapID) values ("
			"%d, '%s', '%s', %d, %d, %d, %d, %d)",
			attr->m_InterfaceMapAttributeDBID, attr->m_InternalAttribute,
			attr->m_ExternalAttribute, attr->m_IsConstant, attr->m_DataType,
			attr->m_IsUDF, attr->m_UDFElementType, interfaceMap.m_InterfaceMapDBID);
		
		statements.Add(statement);
		nextKey++;
	}

	return das.ExecuteStatements("StoreInterfaceMap", statements);


}

BOOL CInterfaceMapDataService::IsFacilityDefaultMap(long facilityID, long interfaceMapID)
{
	CString query;
	CStringArray results;
	CDataAccessService das;

	query.Format("select dbinterfacemapid "
		"from DBFacilityInterfaceMap "
		"where DBInterfaceMapID = %d "
		"and DBFacilityid = %d", interfaceMapID, facilityID);

	das.ExecuteQuery("IsFacilityDefault", query, results);
	if (results.GetSize() > 0)
		return TRUE;
	else
		return FALSE;

}

int CInterfaceMapDataService::SetFacilityDefaultMap(long facilityID, long interfaceMapID, BOOL isDefault)
{
	CString stmt;
	CStringArray stmts;
	CDataAccessService das;
	long nextKey;

	stmt.Format("delete from DBFacilityInterfaceMap "
		"where DBFacilityID = %d", facilityID);
	stmts.Add(stmt);
	
	if (isDefault) {
		nextKey = das.GetNextKey("DBFacilityInterfaceMap", 1);
		
		stmt.Format("insert into DBFacilityInterfaceMap ( "
			"DBFacilityInterfaceMapID, DBFacilityID, DBInterfaceMapID) values ("
			"%d, %d, %d)", nextKey, facilityID, interfaceMapID);
		stmts.Add(stmt);
	}
	return das.ExecuteStatements("SetFacilityDefaultMap", stmts);
	
}

int CInterfaceMapDataService::DeleteInterfaceMap(long interfaceMapID)
{
	CString statement;
	CStringArray statements;
	CDataAccessService das;

	statement.Format("delete from DBInterfaceMapAttr "
		"where DBInterfaceMapID = %d", interfaceMapID);
	statements.Add(statement);
	
	statement.Format("delete from DBInterfaceMap "
		"where DBInterfaceMapID = %d", interfaceMapID);
	statements.Add(statement);

	return das.ExecuteStatements("DeleteInterfaceMap", statements);

}

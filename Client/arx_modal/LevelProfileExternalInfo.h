// LevelProfileExternalInfo.h: interface for the CLevelProfileExternalInfo class.
//
//////////////////////////////////////////////////////////////////////

#if !defined(AFX_LEVELPROFILEEXTERNALINFO_H__45825A1B_BBCF_4FBA_A75E_C5D8242ADB8D__INCLUDED_)
#define AFX_LEVELPROFILEEXTERNALINFO_H__45825A1B_BBCF_4FBA_A75E_C5D8242ADB8D__INCLUDED_

#if _MSC_VER > 1000
#pragma once
#endif // _MSC_VER > 1000

class CLevelProfileExternalInfo : public CObject  
{
public:
	CString Stream();
	CLevelProfileExternalInfo();
	virtual ~CLevelProfileExternalInfo();

	CLevelProfileExternalInfo(const CLevelProfileExternalInfo& other);
	CLevelProfileExternalInfo& operator=(const CLevelProfileExternalInfo &other);
	BOOL operator==(const CLevelProfileExternalInfo& other);
	BOOL operator!=(const CLevelProfileExternalInfo& other) { return !(*this == other);};

	int Parse(const CString& line);

	int m_LevelProfileInfoDBId;
	CString m_Value;
	int m_LevelProfileDBId;
	int m_ExternalInfoDBId;

	CString m_Name;
	int m_DataType;
	int m_Length;
	CString m_DefaultValue;
	CStringArray m_ListValues;
	CStringArray m_ListDisplayValues;
	int m_Sequence;
	int m_ExternalSystemDBId;


};

#endif // !defined(AFX_LEVELPROFILEEXTERNALINFO_H__45825A1B_BBCF_4FBA_A75E_C5D8242ADB8D__INCLUDED_)

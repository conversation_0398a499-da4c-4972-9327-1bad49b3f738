// OptimizationDataService.h: interface for the COptimizationDataService class.
//
//////////////////////////////////////////////////////////////////////

#if !defined(AFX_OPTIMIZATIONDATASERVICE_H__280D96E9_7DD0_4E3D_A910_25A3CEC8C9A8__INCLUDED_)
#define AFX_OPTIMIZATIONDATASERVICE_H__280D96E9_7DD0_4E3D_A910_25A3CEC8C9A8__INCLUDED_

#if _MSC_VER > 1000
#pragma once
#endif // _MSC_VER > 1000

class COptimizationDataService  
{
public:
	COptimizationDataService();
	virtual ~COptimizationDataService();
	
	int GetProductRankings(int productDBID, CStringArray &rankings);
};

#endif // !defined(AFX_OPTIMIZATIONDATASERVICE_H__280D96E9_7DD0_4E3D_A910_25A3CEC8C9A8__INCLUDED_)

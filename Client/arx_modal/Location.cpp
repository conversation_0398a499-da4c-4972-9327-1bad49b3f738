// Location.cpp: implementation of the CLocation class.
//
//////////////////////////////////////////////////////////////////////

#include "stdafx.h"
#include "modal.h"
#include "Location.h"
#include "UtilityHelper.h"

#ifdef _DEBUG
#undef THIS_FILE
static char THIS_FILE[]=__FILE__;
#define new DEBUG_NEW
#endif

extern CUtilityHelper utilityHelper;

//////////////////////////////////////////////////////////////////////
// Construction/Destruction
//////////////////////////////////////////////////////////////////////

CLocation::CLocation()
{
	this->Clear();
}

CLocation::CLocation(const CLocation& other)
{
	m_DBId = other.m_DBId;
	m_Description = other.m_Description;
	m_HandlingMethod = other.m_HandlingMethod;
	m_Usage = other.m_Usage;
	m_Width = other.m_Width;
	m_Depth = other.m_Depth;
	m_Height = other.m_Height;
	m_WeightCapacity = other.m_WeightCapacity;
	m_Coordinates.m_X = other.m_Coordinates.m_X;
	m_Coordinates.m_Y = other.m_Coordinates.m_Y;
	m_Coordinates.m_Z = other.m_Coordinates.m_Z;
	m_ChangedInPass = other.m_ChangedInPass;
	m_IsOverridden = other.m_IsOverridden;
	m_Status = other.m_Status;
	m_IsActive = other.m_IsActive;
	m_Clearance = other.m_Clearance;
	m_BackfillId = other.m_BackfillId;
	m_BackfillCoordinates.m_X = other.m_BackfillCoordinates.m_X;
	m_BackfillCoordinates.m_Y = other.m_BackfillCoordinates.m_Y;
	m_BackfillCoordinates.m_Z = other.m_BackfillCoordinates.m_Z;
	m_StockerId = other.m_StockerId;
	m_StockerCoordinates.m_X = other.m_StockerCoordinates.m_X;
	m_StockerCoordinates.m_Y = other.m_StockerCoordinates.m_Y;
	m_StockerCoordinates.m_Z = other.m_StockerCoordinates.m_Z;
	m_TraceEnabled = other.m_TraceEnabled;
	m_SelectionSequence = other.m_SelectionSequence;
	m_ReplenishmentSequence = other.m_ReplenishmentSequence;
	m_LocationKey = other.m_LocationKey;

	for (int i=0; i < m_InfoList.GetSize(); ++i)
		delete m_InfoList[i];

	m_InfoList.RemoveAll();

	for (i=0; i < other.m_InfoList.GetSize(); ++i) {
		m_InfoList.Add(new CLevelProfileExternalInfo(*other.m_InfoList[i]));
	}

}

CLocation& CLocation::operator=(const CLocation& other)
{
	m_DBId = other.m_DBId;
	m_Description = other.m_Description;
	m_HandlingMethod = other.m_HandlingMethod;
	m_Usage = other.m_Usage;
	m_Width = other.m_Width;
	m_Depth = other.m_Depth;
	m_Height = other.m_Height;
	m_WeightCapacity = other.m_WeightCapacity;
	m_Coordinates.m_X = other.m_Coordinates.m_X;
	m_Coordinates.m_Y = other.m_Coordinates.m_Y;
	m_Coordinates.m_Z = other.m_Coordinates.m_Z;
	m_ChangedInPass = other.m_ChangedInPass;
	m_IsOverridden = other.m_IsOverridden;
	m_Status = other.m_Status;
	m_IsActive = other.m_IsActive;
	m_Clearance = other.m_Clearance;
	m_BackfillId = other.m_BackfillId;
	m_BackfillCoordinates.m_X = other.m_BackfillCoordinates.m_X;
	m_BackfillCoordinates.m_Y = other.m_BackfillCoordinates.m_Y;
	m_BackfillCoordinates.m_Z = other.m_BackfillCoordinates.m_Z;
	m_StockerId = other.m_StockerId;
	m_StockerCoordinates.m_X = other.m_StockerCoordinates.m_X;
	m_StockerCoordinates.m_Y = other.m_StockerCoordinates.m_Y;
	m_StockerCoordinates.m_Z = other.m_StockerCoordinates.m_Z;
	m_TraceEnabled = other.m_TraceEnabled;
	m_SelectionSequence = other.m_SelectionSequence;
	m_ReplenishmentSequence = other.m_ReplenishmentSequence;
	m_LocationKey = other.m_LocationKey;

	for (int i=0; i < m_InfoList.GetSize(); ++i)
		delete m_InfoList[i];

	m_InfoList.RemoveAll();

	for (i=0; i < other.m_InfoList.GetSize(); ++i) {
		m_InfoList.Add(new CLevelProfileExternalInfo(*other.m_InfoList[i]));
	}

	return *this;
}

CLocation::~CLocation()
{

}

void CLocation::Clear()
{
	m_BackfillCoordinates.m_X = 0;
	m_BackfillCoordinates.m_Y = 0;
	m_BackfillCoordinates.m_Z = 0;
	m_BackfillId = "";
	m_Clearance = 0;
	m_Coordinates.m_X = 0;
	m_Coordinates.m_Y = 0;
	m_Coordinates.m_Z = 0;

	m_Depth = m_Height = m_Width = 0;
	m_HandlingMethod = -1;
	m_LocationKey = -1;
	m_LocationProfileDBId = -1;
	m_ReplenishmentSequence = "";
	m_SelectionSequence = "";

	m_Status = -1;
	m_Usage = -1;
	m_WeightCapacity = -1;
	m_IsActive = FALSE;

	for (int i=0; i < m_InfoList.GetSize(); ++i)
		delete m_InfoList[i];

	m_InfoList.RemoveAll();
}

CString CLocation::Stream()
{	
	CString temp;

	temp.Format("%d|%s|%d|%.0f|%.0f|%.0f|"
		"%.0f|%.0f|%.0f|"
		"%d|%d|"
		"%s|%.0f|%.0f|%.0f|"
		"%s|%.0f|%.0f|%.0f|"
		"%s|%s|%.0f|%.0f|%d|",
		m_DBId, m_Description, m_LocationKey, m_Coordinates.m_X, m_Coordinates.m_Y, m_Coordinates.m_Z,
		m_Width, m_Depth, m_Height, 
		m_HandlingMethod, m_Usage,
		m_BackfillId, m_BackfillCoordinates.m_X, m_BackfillCoordinates.m_Y, m_BackfillCoordinates.m_Z,
		m_StockerId, m_StockerCoordinates.m_X, m_StockerCoordinates.m_Y, m_StockerCoordinates.m_Z,
		m_SelectionSequence, m_ReplenishmentSequence, m_Clearance, m_WeightCapacity, m_IsActive);

	for (int i=0; i < m_InfoList.GetSize(); ++i) {
		CString temp2;
		CString name = m_InfoList[i]->m_Name;

		temp2.Format("%s^%s|", m_InfoList[i]->m_Name, m_InfoList[i]->m_Value);
		temp += temp2;
	}

	return temp;

}

int CLocation::Parse(const CString &line)
{
	CStringArray strings;

	utilityHelper.ParseString(line, "|", strings);

	for (int i=0; i < strings.GetSize(); ++i) {
		switch (i) {
		case 0:
			m_DBId = atoi(strings[i]);
			break;
		case 1:
			m_Description = strings[i];
			break;
		case 2:
			m_HandlingMethod = atoi(strings[i]);
			break;
		case 3:
			m_Usage = atoi(strings[i]);
			break;
		case 4:
			m_Width = atof(strings[i]);
			break;
		case 5:
			m_Depth = atof(strings[i]);
			break;
		case 6:
			m_Height = atof(strings[i]);
			break;
		case 7:
			m_WeightCapacity = atof(strings[i]);
			break;
		case 8:
			m_Coordinates.m_X = atoi(strings[i]);
			break;
		case 9:
			m_Coordinates.m_Y = atoi(strings[i]);
			break;
		case 10:
			m_Coordinates.m_Z = atoi(strings[i]);
			break;
		case 11:
			m_ChangedInPass = atoi(strings[i]);
			break;
		case 12:
			m_IsOverridden = atoi(strings[i]);
			break;
			// skip dates and user and level dbid
		case 16:
			m_LocationProfileDBId = atoi(strings[i]);
			break;
		case 18:
			m_Status = atoi(strings[i]);
			break;
		case 19:
			m_IsActive = atoi(strings[i]);
			break;
		case 20:
			m_Clearance = atof(strings[i]);
			break;
		case 21:
			m_BackfillId = strings[i];
			break;
		case 22:
			m_BackfillCoordinates.m_X = atof(strings[i]);
			break;
		case 23:
			m_BackfillCoordinates.m_Y = atof(strings[i]);
			break;
		case 24:
			m_BackfillCoordinates.m_Z = atof(strings[i]);
			break;
		case 25:
			m_StockerId = strings[i];
			break;
		case 26:
			m_StockerCoordinates.m_X = atof(strings[i]);
			break;
		case 27:
			m_StockerCoordinates.m_Y = atof(strings[i]);
			break;
		case 28:
			m_StockerCoordinates.m_Z = atof(strings[i]);
			break;
		case 29:
			m_TraceEnabled = atoi(strings[i]);
			break;
		case 30:
			m_SelectionSequence = strings[i];
			break;
		case 31:
			m_ReplenishmentSequence = strings[i];
			break;
		case 32:
			m_LocationKey = atoi(strings[i]);
			break;
		}
	}

	return 0;
}

CString CLocation::ConvertStatusToText(int status)
{
	CString temp;

	switch (status) {
	case NotIntegrated:
		temp = "Not Integrated";
		break;
	case IntegrationPending:
		temp = "Pending";
		break;
	case Integrated:
		temp = "Integrated";
		break;
	case -1:
		temp = "N/A";
		break;
	default:
		temp = "Uknown";
		break;
	}

	return temp;

}

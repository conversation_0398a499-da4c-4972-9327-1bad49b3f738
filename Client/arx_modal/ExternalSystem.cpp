// ExternalSystem.cpp: implementation of the CExternalSystem class.
//
//////////////////////////////////////////////////////////////////////

#include "stdafx.h"
#include "modal.h"
#include "ExternalSystem.h"
#include "UtilityHelper.h"

#ifdef _DEBUG
#undef THIS_FILE
static char THIS_FILE[]=__FILE__;
#define new DEBUG_NEW
#endif

extern CUtilityHelper utilityHelper;
//////////////////////////////////////////////////////////////////////
// Construction/Destruction
//////////////////////////////////////////////////////////////////////

CExternalSystem::CExternalSystem()
{

}

CExternalSystem::~CExternalSystem()
{

}

CExternalSystem::CExternalSystem(const CExternalSystem& other)
{
	m_ExternalSystemDBId = other.m_ExternalSystemDBId;
	m_Name = other.m_Name;
	m_Vendor = other.m_Vendor;
	m_Version = other.m_Version;
	m_SystemTypeDBId = other.m_SystemTypeDBId;
	m_TypeName = other.m_TypeName;

}

CExternalSystem& CExternalSystem::operator=(const CExternalSystem& other)
{
	m_ExternalSystemDBId = other.m_ExternalSystemDBId;
	m_Name = other.m_Name;
	m_Vendor = other.m_Vendor;
	m_Version = other.m_Version;
	m_SystemTypeDBId = other.m_SystemTypeDBId;
	m_TypeName = other.m_TypeName;

	return *this;

}

BOOL CExternalSystem::operator==(const CExternalSystem& other)
{
	if (m_ExternalSystemDBId != other.m_ExternalSystemDBId) return FALSE;
	if (m_Name != other.m_Name) return FALSE;
	if (m_Vendor != other.m_Vendor) return FALSE;
	if (m_Version != other.m_Version) return FALSE;
	if (m_SystemTypeDBId != other.m_SystemTypeDBId) return FALSE;
	if (m_TypeName != other.m_TypeName) return FALSE;

	return TRUE;
}

BOOL CExternalSystem::operator!=(const CExternalSystem& other)
{
	return (! (*this == other));
}

int CExternalSystem::Parse(const CString &line)
{
	CStringArray strings;

	utilityHelper.ParseString(line, "|", strings);

	for (int i=0; i < strings.GetSize(); ++i) {
		switch (i) {
		case 0:
			m_ExternalSystemDBId = atoi(strings[i]);
			break;
		case 1:
			m_Vendor = strings[i];
			break;
		case 2:
			m_Name = strings[i];
			break;
		case 3:
			m_Version = strings[i];
			break;
		case 4:
			m_SystemTypeDBId = atoi(strings[i]);
			break;
		case 5:
			m_TypeName = strings[i];
			break;
		}
	}

	return 0;
}

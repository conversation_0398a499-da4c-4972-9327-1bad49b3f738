// ProductGroupDialog.cpp : implementation file
//

#include "stdafx.h"
#include "ssa_exception.h"
#include "ResourceHelper.h"
#include "HelpService.h"
#include "GenericPropertySheet.h"
#include "ProductGroupDialog.h"
#include "ProductGroupPropertiesPage.h"
#include "ProductGroupConstraintsPage.h"
#include "ProductGroupFrame.h"
#include "ProductGroupNavigator.h"
#include "UtilityHelper.h"

#include <dbsymtb.h>

#ifdef _DEBUG
#define new DEBUG_NEW
#undef THIS_FILE
static char THIS_FILE[] = __FILE__;
#endif

extern CHelpService helpService;
extern CUtilityHelper utilityHelper;

/////////////////////////////////////////////////////////////////////////////
// CProductGroupDialog dialog

extern CProductGroupFrame *m_ProductGroupFrame;

IMPLEMENT_DYNCREATE(CProductGroupDialog, CDialog)

CProductGroupDialog::CProductGroupDialog(CWnd* pParent /*=NULL*/)
	: CDialog(CProductGroupDialog::IDD, pParent)
{
	//{{AFX_DATA_INIT(CProductGroupDialog)
		// NOTE: the ClassWizard will add member initialization here
	//}}AFX_DATA_INIT
}

CProductGroupDialog::~CProductGroupDialog()
{
}


void CProductGroupDialog::DoDataExchange(CDataExchange* pDX)
{
	CDialog::DoDataExchange(pDX);
	//{{AFX_DATA_MAP(CProductGroupDialog)
	DDX_Control(pDX, IDC_PRODUCT_GROUP_LIST, m_ProductGroupListCtrl);
	//}}AFX_DATA_MAP
}


BEGIN_MESSAGE_MAP(CProductGroupDialog, CDialog)
	//{{AFX_MSG_MAP(CProductGroupDialog)
	ON_BN_CLICKED(IDAPPLY, OnApply)
	ON_BN_CLICKED(IDC_ADD, OnAdd)
	ON_BN_CLICKED(IDC_DELETE, OnDelete)
	ON_BN_CLICKED(IDC_MOVE_DOWN, OnMoveDown)
	ON_BN_CLICKED(IDC_MOVE_UP, OnMoveUp)
	ON_BN_CLICKED(IDC_PROPERTIES, OnProperties)
	ON_BN_CLICKED(IDHELP, OnHelp)
	ON_WM_CONTEXTMENU()
	ON_BN_CLICKED(IDC_ASSIGN_PRODUCTS, OnAssignProducts)
	ON_COMMAND(ID_PRODUCTGROUPMENU_DELETE, OnDelete)
	ON_COMMAND(ID_PRODUCTGROUPMENU_ADD, OnAdd)
	ON_COMMAND(ID_PRODUCTGROUPMENU_PROPERTIES, OnProperties)
	ON_LBN_DBLCLK(IDC_PRODUCT_GROUP_LIST, OnProperties)
	ON_WM_HELPINFO()
	//}}AFX_MSG_MAP
END_MESSAGE_MAP()

/////////////////////////////////////////////////////////////////////////////
// CProductGroupDialog message handlers

BOOL CProductGroupDialog::OnInitDialog() 
{
	CDialog::OnInitDialog();

	for (int i=0; i < m_ProductGroupList->GetSize(); ++i) {
		m_ProductGroupListCtrl.AddString((*m_ProductGroupList)[i]->m_Description);
	}

	UpdateData(FALSE);
	
	
	return TRUE;  // return TRUE unless you set the focus to a control
	              // EXCEPTION: OCX Property Pages should return FALSE
}


void CProductGroupDialog::OnMoveDown() 
{
	int curSel = m_ProductGroupListCtrl.GetCurSel();

	if (curSel < 0) {
		AfxMessageBox("Please select a product group.");
		return;
	}

	if (curSel == m_ProductGroupList->GetSize()-1)
		return;

	CProductGroup *group1 = (*m_ProductGroupList)[curSel];
	m_ProductGroupList->RemoveAt(curSel);
	m_ProductGroupList->InsertAt(curSel+1, group1);


	m_ProductGroupListCtrl.DeleteString(curSel);
	m_ProductGroupListCtrl.InsertString(curSel+1, group1->m_Description);

	m_ProductGroupListCtrl.SetCurSel(curSel+1);

	UpdatePriorities();

	UpdateData(FALSE);

	return;
	
}

void CProductGroupDialog::OnMoveUp() 
{
	int curSel = m_ProductGroupListCtrl.GetCurSel();

	if (curSel < 0) {
		AfxMessageBox("Please select a product group.");
		return;
	}

	if (curSel == 0)
		return;

	CProductGroup *group1 = (*m_ProductGroupList)[curSel];
	m_ProductGroupList->RemoveAt(curSel);
	m_ProductGroupList->InsertAt(curSel-1, group1);


	m_ProductGroupListCtrl.DeleteString(curSel);
	m_ProductGroupListCtrl.InsertString(curSel-1, group1->m_Description);
	
	m_ProductGroupListCtrl.SetCurSel(curSel-1);

	UpdatePriorities();

	UpdateData(FALSE);

	return;

}


void CProductGroupDialog::OnAdd() 
{
	int rc;
	CGenericPropertySheet productGroupSheet;
	CProductGroupPropertiesPage propertiesPage;
	CProductGroupConstraintsPage constraintsPage;
	CProductGroup *tempProductGroup;
	CString temp;

	productGroupSheet.m_psh.dwFlags |= PSH_NOAPPLYNOW;

	productGroupSheet.AddPage(&propertiesPage);
	productGroupSheet.AddPage(&constraintsPage);

	tempProductGroup = new CProductGroup;
	tempProductGroup->m_Exclusive = TRUE;	// default to true (means look outside constraints)
	propertiesPage.m_ProductGroup = tempProductGroup;
	propertiesPage.m_ProductGroup->m_Priority = m_ProductGroupList->GetSize()+1;
	propertiesPage.m_ProductAttributeList = m_ProductAttributeList;
	constraintsPage.m_ProductGroup = tempProductGroup;

	temp.Format("Add Product Group");
	productGroupSheet.SetTitle(temp);

	BOOL bDone = FALSE;
	while (! bDone ) {
		
		try {
			rc = productGroupSheet.DoModal();
			bDone = TRUE;
		}
		catch (Ssa_Exception e) {
			utilityHelper.ProcessError("Error displaying product group properties", &e);
			rc = -1;
		}
		catch (...) {
			utilityHelper.ProcessError("Error displaying product group properties");
			rc = -1;
		}
		
		if (rc != IDOK) {
			delete tempProductGroup;
			return;
		}
		
		for (int i=0; i < m_ProductGroupList->GetSize(); ++i) {
			if (tempProductGroup->m_Description == m_ProductGroupList->GetAt(i)->m_Description) {
				AfxMessageBox("A product group with the same name already exists.");
				bDone = FALSE;
				break;
			}
		}

	}

	try {
		CWaitCursor cwc;
		rc = m_ProductGroupDataService->StoreProductGroup(*tempProductGroup);
	}
	catch (Ssa_Exception e) {
		utilityHelper.ProcessError("Error adding product group to database.", &e);
		delete tempProductGroup;
		return;
	}
	catch (...) {
		utilityHelper.ProcessError("Error adding product group to database.");
		delete tempProductGroup;
		return;
	}

	m_ProductGroupList->Add(tempProductGroup);
	m_ProductGroupListCtrl.AddString(tempProductGroup->m_Description);

	UpdateData(FALSE);

}


void CProductGroupDialog::OnDelete() 
{
	int rc;

	int curSel = m_ProductGroupListCtrl.GetCurSel();

	if (curSel < 0) {
		AfxMessageBox("Please select a product group.");
		return;
	}

	if (AfxMessageBox("Are you sure you wish to delete this product group?", MB_YESNO) != IDYES)
		return;

	try {
		rc = m_ProductGroupDataService->DeleteProductGroup(m_ProductGroupList->GetAt(curSel)->m_ProductGroupDBID);
	}
	catch (Ssa_Exception e) {
		utilityHelper.ProcessError("Error deleting product group.", &e);
		return;
	}
	catch (...) {
		utilityHelper.ProcessError("Error deleting product group.");
		return;
	}

	delete m_ProductGroupList->GetAt(curSel);
	m_ProductGroupList->RemoveAt(curSel);
	m_ProductGroupListCtrl.DeleteString(curSel);

	UpdateData(FALSE);

	return;

}


void CProductGroupDialog::OnProperties() 
{
	int rc;
	CGenericPropertySheet productGroupSheet;
	CProductGroupPropertiesPage propertiesPage;
	CProductGroupConstraintsPage constraintsPage;
	CProductGroup *tempProductGroup;
	CString oldName, temp;

	int curSel = m_ProductGroupListCtrl.GetCurSel();

	if (curSel < 0) {
		AfxMessageBox("Please select a product group.");
		return;
	}

	productGroupSheet.m_psh.dwFlags |= PSH_NOAPPLYNOW;
	productGroupSheet.AddPage(&propertiesPage);
	productGroupSheet.AddPage(&constraintsPage);

	// Make a copy in case they cancel
	tempProductGroup = new CProductGroup;
	*tempProductGroup = *(m_ProductGroupList->GetAt(curSel));

	temp.Format("Product Group Properties - %s", tempProductGroup->m_Description);
	productGroupSheet.SetTitle(temp);
	propertiesPage.m_ProductGroup = tempProductGroup;
	propertiesPage.m_ProductAttributeList = m_ProductAttributeList;
	constraintsPage.m_ProductGroup = tempProductGroup;

	BOOL bDone = FALSE;
	
	while (! bDone) {
		bDone = TRUE;

		try {
			rc = productGroupSheet.DoModal();
			if (rc != IDOK) {
				delete tempProductGroup;
				return;
			}
		}
		catch (Ssa_Exception e) {
			utilityHelper.ProcessError("Error displaying product group properties", &e);
			delete tempProductGroup;
			return;
		}
		catch (...) {
			utilityHelper.ProcessError("Error displaying product group properties");
			delete tempProductGroup;
			return;
		}
		
		if (m_ProductGroupList->GetAt(curSel)->IsEqual(*tempProductGroup) ) {
			delete tempProductGroup;
			return;
		}
		
		for (int i=0; i < m_ProductGroupList->GetSize(); ++i ) {
			if (i != curSel && tempProductGroup->m_Description == m_ProductGroupList->GetAt(i)->m_Description) {
				AfxMessageBox("A product group with the same name already exists.");
				bDone = FALSE;
				break;
			}
		}
		
	}

	try {
		CWaitCursor cwc;
		rc = m_ProductGroupDataService->StoreProductGroup(*tempProductGroup);
		if (rc < 0) {
			AfxMessageBox("Error updating product group.");
			delete tempProductGroup;
			return;
		}

	}
	catch (Ssa_Exception e) {
		utilityHelper.ProcessError("Error updating product group", &e);
		delete tempProductGroup;
		return;
	}
	catch (...) {
		utilityHelper.ProcessError("Error updating product group.");
		delete tempProductGroup;
		return;
	}

	delete m_ProductGroupList->GetAt(curSel);
	m_ProductGroupList->SetAt(curSel, tempProductGroup);

	// Update the list if they changed the name
	m_ProductGroupListCtrl.GetText(curSel, oldName);
	if (m_ProductGroupList->GetAt(curSel)->m_Description.Compare(oldName) != 0) {
		m_ProductGroupListCtrl.DeleteString(curSel);
		m_ProductGroupListCtrl.InsertString(curSel, m_ProductGroupList->GetAt(curSel)->m_Description);
		UpdateData(FALSE);
	}

	return;
		
}

void CProductGroupDialog::OnApply() 
{
	
	return;

}


void CProductGroupDialog::OnCancel() 
{
	BOOL found = FALSE;

	for (int i=0; i < m_ProductGroupList->GetSize(); ++i) {
		if ((*m_ProductGroupList)[i]->m_Priority != i+1) {
			found = TRUE;
			break;
		}
	}

	if (found)
		if (AfxMessageBox("Product Group priority has changed. Do you wish to save these changes?", MB_YESNO) == IDYES)
			//if (UpdatePriorities() >= 0)
				//AfxMessageBox("Priorities have been updated.");

	CDialog::OnCancel();
}

void CProductGroupDialog::OnHelp() 
{
	helpService.ShowScreenHelp(IDD);	
}


int CProductGroupDialog::UpdatePriorities()
{
	int rc = 0;

	for (int i=0; i < m_ProductGroupList->GetSize(); ++i) {
		if (m_ProductGroupList->GetAt(i)->m_Priority != i+1) {
			try {
				rc = m_ProductGroupDataService->UpdateProductGroupPriority(
					m_ProductGroupList->GetAt(i)->m_ProductGroupDBID, i+1);
				m_ProductGroupList->GetAt(i)->m_Priority = i+1;
			}
			catch (Ssa_Exception e) {
				utilityHelper.ProcessError("Error updating product group priority.", &e);
				break;
			}
			catch (...) {
				utilityHelper.ProcessError("Error updating product group priority.");
				break;
			}
		}
	}

	return rc;

}

void CProductGroupDialog::OnContextMenu(CWnd* pWnd, CPoint point) 
{
	CMenu menu;

	if (pWnd == &m_ProductGroupListCtrl) {
		menu.LoadMenu(IDR_PRODUCTGROUP_MENU);
		if (m_ProductGroupListCtrl.GetCurSel() < 0) {
			menu.GetSubMenu(0)->EnableMenuItem(1, MF_BYPOSITION|MF_GRAYED);
			menu.GetSubMenu(0)->EnableMenuItem(2, MF_BYPOSITION|MF_GRAYED);
		}
		else {
			menu.GetSubMenu(0)->EnableMenuItem(1, MF_BYPOSITION|MF_ENABLED);
			menu.GetSubMenu(0)->EnableMenuItem(2, MF_BYPOSITION|MF_ENABLED);
			menu.GetSubMenu(0)->SetDefaultItem(2, TRUE);
		}
		menu.GetSubMenu(0)->TrackPopupMenu(TPM_LEFTALIGN|TPM_RIGHTBUTTON, point.x, point.y, this);
	}
	
	
}


void CProductGroupDialog::OnAssignProducts() 
{
	CTemporaryResourceOverride t;

	
//	m_ProductGroupAssignmentDialog.m_ProductGroupList = &m_ProductGroupList;
//	m_ProductGroupAssignmentDialog.DoModal();
	
}


BOOL CProductGroupDialog::Create(LPCTSTR lpszClassName, LPCTSTR lpszWindowName, DWORD dwStyle, const RECT& rect, CWnd* pParentWnd, UINT nID, CCreateContext* pContext) 
{
	UNREFERENCED_PARAMETER(lpszClassName);
	UNREFERENCED_PARAMETER(lpszWindowName);
	UNREFERENCED_PARAMETER(dwStyle);
	UNREFERENCED_PARAMETER(rect);
	UNREFERENCED_PARAMETER(nID);
	UNREFERENCED_PARAMETER(pContext);
	CProductGroupNavigator *pNavigator;
	int id;
	BOOL bReturn;

	pNavigator = (CProductGroupNavigator *)m_ProductGroupFrame->m_Splitter.GetPane(0, 0);
	
	m_ProductGroupList = &pNavigator->m_ProductGroupList;
	m_ProductAttributeList = &pNavigator->m_ProductAttributeList;
	m_ProductGroupDataService = &pNavigator->m_ProductGroupDataService;

	bReturn = CDialog::Create(IDD, pParentWnd);
	id = m_ProductGroupFrame->m_Splitter.IdFromRowCol(0, 1);

	if ( bReturn )
		::SetWindowLong ( m_hWnd, GWL_ID, id);
	
	return bReturn;
}

void CProductGroupDialog::PostNcDestroy() 
{
	delete this;
}

BOOL CProductGroupDialog::ValidateClose()
{
	BOOL found = FALSE;
	int rc;

	for (int i=0; i < m_ProductGroupList->GetSize(); ++i) {
		if ((*m_ProductGroupList)[i]->m_Priority != i+1) {
			found = TRUE;
			break;
		}
	}

	if (found) {
		rc = AfxMessageBox("Product Group priority has changed. Do you wish to save these changes?", MB_YESNOCANCEL);
		if (rc == IDCANCEL)
			return FALSE;
		else if (rc == IDYES) {
			if (UpdatePriorities() >= 0)
				AfxMessageBox("Priorities have been updated.");
		}
	}

	return TRUE;
			
}

BOOL CProductGroupDialog::OnHelpInfo(HELPINFO* pHelpInfo) 
{
	helpService.ShowFieldHelp(IDD, pHelpInfo->iCtrlId);
	return FALSE;
}

#if !defined(AFX_NEWSECTIONPAGE1_H__3FAACE11_5779_11D3_AFD6_0080C79D254D__INCLUDED_)
#define AFX_NEWSECTIONPAGE1_H__3FAACE11_5779_11D3_AFD6_0080C79D254D__INCLUDED_

#if _MSC_VER >= 1000
#pragma once
#endif // _MSC_VER >= 1000
// NewSectionPage1.h : header file
//

/////////////////////////////////////////////////////////////////////////////
// CNewSectionPage1 dialog

class CNewSectionPage1 : public CPropertyPage
{
	DECLARE_DYNCREATE(CNewSectionPage1)

// Construction
public:
	void OnApplyBrokenOrder();

	int m_SectionDBId;
	CNewSectionPage1();
	~CNewSectionPage1();

// Dialog Data
	//{{AFX_DATA(CNewSectionPage1)
	enum { IDD = IDD_SECTION_PAGE1 };
	CEdit	m_NewSection_OrderCount_Box;
	CEdit	m_NewSectionLocationMask_Box;
	CEdit	m_NewSection_DescriptionBox;
	CEdit	m_NewSection_ContainerCount_Box;
	CEdit	m_NewSection_AvgOrdQty_Box;
	BOOL	m_NewSection_ApplyBrokenOrder;
	int		m_NewSection_AvgOrdQty;
	int		m_NewSection_ContainerCount;
	CString	m_NewSection_Description;
	CString	m_NewSection_LocationMask;
	int		m_NewSection_OrderCount;
	int		m_XCoordinate;
	int		m_YCoordinate;
	int		m_ZCoordinate;
	CString	m_ForkHotspotCoordinates;
	CString	m_SelHotspotCoordinates;
	CString	m_TravelDistance;
	//}}AFX_DATA


// Overrides
	// ClassWizard generate virtual function overrides
	//{{AFX_VIRTUAL(CNewSectionPage1)
	public:
	virtual BOOL OnApply();
	virtual BOOL OnKillActive();
	virtual BOOL OnSetActive();
	virtual void OnCancel();
	virtual void OnOK();
	protected:
	virtual void DoDataExchange(CDataExchange* pDX);    // DDX/DDV support
	virtual BOOL OnNotify(WPARAM wParam, LPARAM lParam, LRESULT* pResult);
	//}}AFX_VIRTUAL

// Implementation
protected:
	// Generated message map functions
	//{{AFX_MSG(CNewSectionPage1)
	afx_msg void OnKillfocusNewsectionAvgordqty();
	afx_msg void OnKillfocusNewsectionContqty();
	afx_msg void OnKillfocusNewsectionDescription();
	afx_msg void OnKillfocusNewsectionLocationmask();
	afx_msg void OnKillfocusNewsectionOrdercount();
	afx_msg BOOL OnHelpInfo(HELPINFO* pHelpInfo);
	virtual BOOL OnInitDialog();
	//}}AFX_MSG
	DECLARE_MESSAGE_MAP()

private:
	CString m_OriginalName;
};

//{{AFX_INSERT_LOCATION}}
// Microsoft Developer Studio will insert additional declarations immediately before the previous line.

#endif // !defined(AFX_NEWSECTIONPAGE1_H__3FAACE11_5779_11D3_AFD6_0080C79D254D__INCLUDED_)

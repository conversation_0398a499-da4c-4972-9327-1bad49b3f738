// ProductGroupAssignmentDialog.cpp : implementation file
//

#include "stdafx.h"
#include <afxmt.h>
#include "ssa_exception.h"
#include "Constants.h"
#include "HelpService.h"
#include "UtilityHelper.h"
#include "ControlService.h"

#include "ProductGroupDialog.h"
#include "ProductGroupAssignmentDialog.h"
#include "ProductGroupDataService.h"
#include "DisplayResults.h"
#include "Progress.h"
#include "ProductGroupFrame.h"
#include "ProductGroupNavigator.h"

#include "ProgressMessage.h"
#include "ThreadParameters.h"

#include <dbsymtb.h>

#ifdef _DEBUG
#define new DEBUG_NEW
#undef THIS_FILE
static char THIS_FILE[] = __FILE__;
#endif

/////////////////////////////////////////////////////////////////////////////
// CProductGroupAssignmentDialog dialog
extern CHelpService helpService;
extern CUtilityHelper utilityHelper;
extern CProductGroupFrame *m_ProductGroupFrame;
extern CControlService controlService;

IMPLEMENT_DYNCREATE(CProductGroupAssignmentDialog, CDialog)

CProductGroupAssignmentDialog::CProductGroupAssignmentDialog(CWnd* pParent /*=NULL*/)
	: CDialog(CProductGroupAssignmentDialog::IDD, pParent)
{
	//{{AFX_DATA_INIT(CProductGroupAssignmentDialog)
	//}}AFX_DATA_INIT
	m_FirstTime = TRUE;

}


void CProductGroupAssignmentDialog::DoDataExchange(CDataExchange* pDX)
{
	CDialog::DoDataExchange(pDX);
	//{{AFX_DATA_MAP(CProductGroupAssignmentDialog)
	DDX_Control(pDX, IDC_PRODUCT_GROUP_LIST, m_ProductGroupListCtrl);
	//}}AFX_DATA_MAP
}


BEGIN_MESSAGE_MAP(CProductGroupAssignmentDialog, CDialog)
	//{{AFX_MSG_MAP(CProductGroupAssignmentDialog)
	ON_BN_CLICKED(IDC_ASSIGN, OnAssign)
	ON_BN_CLICKED(IDC_COUNT, OnCount)
	ON_BN_CLICKED(IDC_VIEW_PRODUCTS, OnViewProducts)
	ON_WM_CONTEXTMENU()
	ON_COMMAND(ID_ASSIGNMENTMENU_UPDATE_COUNT, OnCount)
	ON_COMMAND(ID_ASSIGNMENTMENU_VIEW_PRODUCTS, OnViewProducts)
	ON_COMMAND(ID_ASSIGNMENTMENU_ASSIGN_PRODUTS, OnAssign)
	ON_WM_HELPINFO()
	ON_BN_CLICKED(IDHELP, OnHelp)
	//}}AFX_MSG_MAP
END_MESSAGE_MAP()

/////////////////////////////////////////////////////////////////////////////
// CProductGroupAssignmentDialog message handlers

BOOL CProductGroupAssignmentDialog::OnInitDialog() 
{
	CDialog::OnInitDialog();

	int n, nItem;
	CRect r;
	LVITEM lvItem;
	CProductGroup *pGroup;
	CString temp;

	n = m_ProductGroupListCtrl.GetHeaderCtrl()->GetItemCount();
	for (int i=0; i < n; ++i)
		m_ProductGroupListCtrl.DeleteColumn(0);
	
	m_ProductGroupListCtrl.GetClientRect(&r);
	
	int w = r.Width()-16;//workaround to get columns fit the width
	m_ProductGroupListCtrl.InsertColumn(0, "Product Group", LVCFMT_LEFT, w*6/14, 0);
	m_ProductGroupListCtrl.InsertColumn(1, "Maximum Count", LVCFMT_RIGHT, w*4/14, 0);
	m_ProductGroupListCtrl.InsertColumn(2, "Current Count", LVCFMT_RIGHT, w*4/14, 0);
	

	if (m_FirstTime) {
		CWaitCursor cwc;
		m_FirstTime = FALSE;
		CProductGroupDataService ds;
		CStringArray results;
		ds.GetProductGroupAssignmentCount(results);
		for (i=0; i < results.GetSize(); ++i) {
			results[i].TrimRight("|");
			long sgid = atol(results[i].Left(results[i].Find("|")));
			CString countStr;
			countStr = results[i].Mid(results[i].Find("|")+1);

			m_ActualCountMap->SetAt(sgid, countStr);
		}
	}

	for (i=0; i < m_ProductGroupList->GetSize(); ++i) {
		pGroup = (*m_ProductGroupList)[i];

		lvItem.mask = LVIF_TEXT|LVIF_PARAM;
		lvItem.iItem = i;
		lvItem.iSubItem = 0;
		lvItem.pszText = pGroup->m_Description.GetBuffer(0);
		pGroup->m_Description.ReleaseBuffer();
		lvItem.lParam = pGroup->m_ProductGroupDBID;
		nItem = m_ProductGroupListCtrl.InsertItem(&lvItem);

		lvItem.mask = LVIF_TEXT;
		lvItem.iItem = nItem;
		lvItem.iSubItem = 1;
		if (m_MaximumCountMap->Lookup(pGroup->m_ProductGroupDBID, temp)) {
			lvItem.pszText = temp.GetBuffer(0);
			temp.ReleaseBuffer();
		}
		else {
			lvItem.pszText = "";
			m_MaximumCountMap->SetAt(pGroup->m_ProductGroupDBID, "");
		}
		m_ProductGroupListCtrl.SetItem(&lvItem);

		lvItem.iSubItem = 2;
		if (m_ActualCountMap->Lookup(pGroup->m_ProductGroupDBID, temp)) {
			lvItem.pszText = temp.GetBuffer(0);
			temp.ReleaseBuffer();
		}
		else {
			lvItem.pszText = "";
			m_ActualCountMap->SetAt(pGroup->m_ProductGroupDBID, "");
		}
		m_ProductGroupListCtrl.SetItem(&lvItem);
	}

	lvItem.mask = LVIF_TEXT|LVIF_PARAM;	
	lvItem.iItem = m_ProductGroupList->GetSize();
	lvItem.iSubItem = 0;
	lvItem.pszText = "Not Assigned";
	lvItem.lParam = 0;
	nItem = m_ProductGroupListCtrl.InsertItem(&lvItem);

	lvItem.mask = LVIF_TEXT;
	lvItem.iItem = nItem;
	lvItem.iSubItem = 1;
	if (m_ActualCountMap->Lookup(-1, temp)) {
		lvItem.pszText = temp.GetBuffer(0);
		temp.ReleaseBuffer();
	}
	else {
		lvItem.pszText = "";
		m_ActualCountMap->SetAt(-1, "");
	}
	m_ProductGroupListCtrl.SetItem(&lvItem);

	lvItem.iSubItem = 2;
	CProductGroupDataService service;

	temp.Format("%d", service.GetProductGroupUnassignedCount());
	lvItem.pszText = temp.GetBuffer(0);
	temp.ReleaseBuffer();
	m_ProductGroupListCtrl.SetItem(&lvItem);

	UpdateData(FALSE);

	return TRUE;  // return TRUE unless you set the focus to a control
	              // EXCEPTION: OCX Property Pages should return FALSE
}


void CProductGroupAssignmentDialog::OnAssign() 
{
	CProductGroupDataService service;
	CString temp;
	CStringArray results;
	int rc, stopIdx;
	CWinThread *pThread;
	BOOL bThreadDone;
	POSITION pos;
	int curSel;
	pos = m_ProductGroupListCtrl.GetFirstSelectedItemPosition();
	if (pos == NULL)
		curSel = -1;
	else {
		curSel = m_ProductGroupListCtrl.GetNextSelectedItem(pos);
		if (curSel != 0)
			if (AfxMessageBox("Warning!  Assigning products to a single group may cause the\n"
								"group to contain products that should be in a group with a\n"
								"higher priority. This will only occur if the group definitions\n"
								"overlap and there are products not currently assigned to any\n"
								"group.  You can ignore this message if you plan to assign\n"
								"products to all of the groups that have a higher priority\n"
								"than this one or if you are sure that the groups do not overlap.\n\n"
								"Do you wish to continue?", MB_YESNO) != IDYES)
				return;
	}



	if (curSel == m_ProductGroupListCtrl.GetItemCount()-1)
		return;

	int lower = 0, upper = 1;
	if (curSel < 0)
		upper = m_ProductGroupList->GetSize();
	
	
	CProgressMessage progress("Creating assignments...", lower, upper, 1, this);
	results.SetSize(5);
	stopIdx = m_ProductGroupList->GetSize()-1;
	CWaitCursor cwc;

	CThreadParameters parms;
	CEvent event;
	parms.m_pEvent = &event;
	parms.m_pInList = &results;
	event.ResetEvent();


	for (int i=0; i < m_ProductGroupList->GetSize(); ++i) {

		if (curSel >= 0 && curSel != i)
			continue;

		results[0].Format("%d", m_ProductGroupList->GetAt(i)->m_ProductGroupDBID);
		results[1].Format("%d", m_ProductGroupList->GetAt(i)->m_Priority);
		results[2] = "0";
		results[3] = "0";
		results[4].Format("%d", controlService.GetCurrentFacilityDBId());

		pThread = AfxBeginThread(CProductGroupAssignmentDialog::AssignProductsThread, &parms);
		
		bThreadDone = FALSE;
		while (TRUE) {
			if ( ! utilityHelper.PeekAndPump() )
				break;
			
			bThreadDone = event.Lock(0);
			if (bThreadDone)
				break;
		}

		rc = parms.m_ReturnCode;
		if (rc < 0) {
			progress.Hide();
			ads_printf("%s\n", parms.m_ReturnMessage);
			controlService.Log("An error occurred during assignment creation.\n"
				"Please contact Optimize support.", "Error in AssignProductsThread.\n");
			return;
		}

		
		progress.Step();

		if (progress.IsStopping()) {
			stopIdx = i;
			break;
		}
	}


	progress.UpdateMessage("Updating counts...");
	if (curSel >= 0)
		progress.m_ProgressDialog->m_ProgressCtrl.SetRange32(0, 1);
	else
		progress.m_ProgressDialog->m_ProgressCtrl.SetRange32(0, m_ProductGroupList->GetSize()+1);
	progress.m_ProgressDialog->m_ProgressCtrl.SetPos(0);
	progress.m_ProgressDialog->m_Stopping = 0;

	// We have to recount after assigning them all in case there is overlap
	for (i=0; i <= stopIdx; ++i) {

		if (curSel >= 0 && curSel != i)
			continue;
	
		results[0].Format("%d", (*m_ProductGroupList)[i]->m_ProductGroupDBID);
		
		event.ResetEvent();

		pThread = AfxBeginThread(CProductGroupAssignmentDialog::CountAssignmentsThread, &parms);
		
		bThreadDone = FALSE;
		while (TRUE) {
			if ( ! utilityHelper.PeekAndPump() )
				break;
			
			bThreadDone = event.Lock(0);
			if (bThreadDone)
				break;
		}

		if (parms.m_ReturnCode < 0) {
			progress.Hide();
			ads_printf("%s\n", parms.m_ReturnMessage);
			controlService.Log("An error occurred during assignment creation.\n"
				"Please contact Optimize support.", "Error in CountAssignmentsThread.\n");
			return;
		}

		progress.Step();

		temp.Format("%d", parms.m_ReturnCode);
		m_ProductGroupListCtrl.SetItemText(i, 2, temp);
		m_ActualCountMap->SetAt((*m_ProductGroupList)[i]->m_ProductGroupDBID, temp);

	}

	event.ResetEvent();
	// Get a count of products not assigned to any product group
	pThread = AfxBeginThread(CProductGroupAssignmentDialog::GetUnassignedCountThread, &parms);
	
	bThreadDone = FALSE;
	while (TRUE) {
		if ( ! utilityHelper.PeekAndPump() )
			break;
		
		bThreadDone = event.Lock(0);
		if (bThreadDone)
			break;
	}
	
	if (parms.m_ReturnCode < 0) {
		progress.Hide();
		ads_printf("%s\n", parms.m_ReturnMessage);
		AfxMessageBox("An error occurred during assignment creation.\n"
			"Please contact Optimize support.");
		return;
	}

	progress.Step();

	temp.Format("%d", parms.m_ReturnCode);
	m_ProductGroupListCtrl.SetItemText(m_ProductGroupList->GetSize(), 2, temp);
	m_ActualCountMap->SetAt(-1, temp);

	Sleep(500);

}

/*
void CProductGroupAssignmentDialog::OnAssign() 
{
	CProductGroupDataService service;
	CString temp;
	CStringArray results;
	int rc, stopIdx;
	CWinThread *pThread;
	BOOL bThreadDone;
	POSITION pos;
	int curSel;
	pos = m_ProductGroupListCtrl.GetFirstSelectedItemPosition();
	if (pos == NULL)
		curSel = -1;
	else {
		curSel = m_ProductGroupListCtrl.GetNextSelectedItem(pos);
		if (curSel != 0)
			if (AfxMessageBox("Warning!  Assigning products to a single group may cause the\n"
								"group to contain products that should be in a group with a\n"
								"higher priority. This will only occur if the group definitions\n"
								"overlap and there are products not currently assigned to any\n"
								"group.  You can ignore this message if you plan to assign\n"
								"products to all of the groups that have a higher priority\n"
								"than this one or if you are sure that the groups do not overlap.\n\n"
								"Do you wish to continue?", MB_YESNO) != IDYES)
				return;
	}



	if (curSel == m_ProductGroupListCtrl.GetItemCount()-1)
		return;

	int lower = 0, upper = 1;
	if (curSel < 0)
		upper = m_ProductGroupList->GetSize();
	
	
	CProgressMessage progress("Deleting existing assignments...", lower, upper, 1, this);
	results.SetSize(5);
	stopIdx = 0;
	CWaitCursor cwc;

	CThreadParameters parms;
	CEvent event;
	parms.m_pEvent = &event;
	parms.m_pInList = &results;
	event.ResetEvent();


	for (int i=m_ProductGroupList->GetSize()-1; i >= 0; --i) {

		if (curSel >= 0 && curSel != i)
			continue;

		results[0].Format("%d", m_ProductGroupList->GetAt(i)->m_ProductGroupDBID);
		results[1].Format("%d", m_ProductGroupList->GetAt(i)->m_Priority);
		results[2] = "0";
		results[3] = "1";
		results[4].Format("%d", controlService.GetCurrentFacilityDBId());

		pThread = AfxBeginThread(CProductGroupAssignmentDialog::AssignProductsThread, &parms);
		
		bThreadDone = FALSE;
		while (TRUE) {
			if ( ! utilityHelper.PeekAndPump() )
				break;
			
			bThreadDone = event.Lock(0);
			if (bThreadDone)
				break;
		}

		rc = parms.m_ReturnCode;
		if (rc < 0) {
			progress.Hide();
			ads_printf("%s\n", parms.m_ReturnMessage);
			AfxMessageBox("An error occurred during assignment deletion.\n"
				"Please contact Optimize support.");
			return;
		}

		
		progress.Step();

		if (progress.IsStopping()) {
			stopIdx = i;
			break;
		}
	}

	progress.m_ProgressDialog->m_Stopping = FALSE;
	progress.UpdateMessage("Creating new assignments...");
	
	if (stopIdx == 0) {
		for (int i=m_ProductGroupList->GetSize()-1; i >= 0; --i) {
			
			if (curSel >= 0 && curSel != i)
				continue;
			
			results[0].Format("%d", m_ProductGroupList->GetAt(i)->m_ProductGroupDBID);
			results[1].Format("%d", m_ProductGroupList->GetAt(i)->m_Priority);
			results[2] = "0";
			results[3] = "0";
			
			event.ResetEvent();

			pThread = AfxBeginThread(CProductGroupAssignmentDialog::AssignProductsThread, &parms);
			
			bThreadDone = FALSE;
			while (TRUE) {
				if ( ! utilityHelper.PeekAndPump() )
					break;
				
				bThreadDone = event.Lock(0);
				if (bThreadDone)
					break;
			}
			
			rc = parms.m_ReturnCode;
			if (rc < 0) {
				ads_printf("%s\n", parms.m_ReturnMessage);
				AfxMessageBox("An error occurred during assignment creation.\n"
					"Please contact Optimize support.");
				return;
			}
			
			
			progress.Step();
			
			if (progress.IsStopping()) {
				stopIdx = i;
				break;
			}
		}
	}

	progress.UpdateMessage("Updating counts...");
	if (curSel >= 0)
		progress.m_ProgressDialog->m_ProgressCtrl.SetRange32(0, 1);
	else
		progress.m_ProgressDialog->m_ProgressCtrl.SetRange32(0, m_ProductGroupList->GetSize()+1);
	progress.m_ProgressDialog->m_ProgressCtrl.SetPos(0);
	progress.m_ProgressDialog->m_Stopping = 0;

	// We have to recount after assigning them all in case there is overlap
	for (i=m_ProductGroupList->GetSize()-1; i >= stopIdx; --i) {

		if (curSel >= 0 && curSel != i)
			continue;
	
		results[0].Format("%d", (*m_ProductGroupList)[i]->m_ProductGroupDBID);
		
		event.ResetEvent();

		pThread = AfxBeginThread(CProductGroupAssignmentDialog::CountAssignmentsThread, &parms);
		
		bThreadDone = FALSE;
		while (TRUE) {
			if ( ! utilityHelper.PeekAndPump() )
				break;
			
			bThreadDone = event.Lock(0);
			if (bThreadDone)
				break;
		}

		if (parms.m_ReturnCode < 0) {
			progress.Hide();
			ads_printf("%s\n", parms.m_ReturnMessage);
			AfxMessageBox("An error occurred during assignment creation.\n"
				"Please contact Optimize support.");
			return;
		}

		progress.m_ProgressDialog->m_ProgressCtrl.StepIt();

		temp.Format("%d", parms.m_ReturnCode);
		m_ProductGroupListCtrl.SetItemText(i, 2, temp);
		m_ActualCountMap->SetAt((*m_ProductGroupList)[i]->m_ProductGroupDBID, temp);

	}

	event.ResetEvent();
	// Get a count of products not assigned to any product group
	pThread = AfxBeginThread(CProductGroupAssignmentDialog::GetUnassignedCountThread, &parms);
	
	bThreadDone = FALSE;
	while (TRUE) {
		if ( ! utilityHelper.PeekAndPump() )
			break;
		
		bThreadDone = event.Lock(0);
		if (bThreadDone)
			break;
	}
	
	if (parms.m_ReturnCode < 0) {
		progress.Hide();
		ads_printf("%s\n", parms.m_ReturnMessage);
		AfxMessageBox("An error occurred during assignment creation.\n"
			"Please contact Optimize support.");
		return;
	}

	progress.Step();

	temp.Format("%d", parms.m_ReturnCode);
	m_ProductGroupListCtrl.SetItemText(m_ProductGroupList->GetSize(), 2, temp);
	m_ActualCountMap->SetAt(-1, temp);

	Sleep(500);

}
*/

void CProductGroupAssignmentDialog::OnCount() 
{
	CProductGroupDataService service;
	CString temp;
	CStringArray results;
	int stopIdx;
	CWinThread *pThread;
	BOOL bThreadDone;
	int curSel;
	POSITION pos;

	pos = m_ProductGroupListCtrl.GetFirstSelectedItemPosition();
	if (pos == NULL) {
		if (! (AfxMessageBox("Do you wish to update the count for all product groups?", MB_YESNO) == IDYES) )
			return;
		curSel = -1;
	}
	else 
		curSel = m_ProductGroupListCtrl.GetNextSelectedItem(pos);
	
	int lower = 0, upper = 1;
	if (curSel < 0)
		upper =  m_ProductGroupList->GetSize();
	CProgressMessage progress("Updating maximum counts...", lower, upper, 1, this);

	results.SetSize(5);
	stopIdx = 0;

	CWaitCursor cwc;
	
	CThreadParameters parms;
	CEvent event;
	parms.m_pEvent = &event;
	parms.m_pInList = &results;

	for (int i=0; i < m_ProductGroupList->GetSize(); ++i) {
		
		if (curSel >= 0 && curSel != i)
			continue;

		results[0].Format("%d", m_ProductGroupList->GetAt(i)->m_ProductGroupDBID);
		results[1].Format("%d", m_ProductGroupList->GetAt(i)->m_Priority);
		results[2] = "1";
		results[3] = "0";
		results[4].Format("%d", controlService.GetCurrentFacilityDBId());


		event.ResetEvent();

		pThread = AfxBeginThread(CProductGroupAssignmentDialog::AssignProductsThread, &parms);
		
		bThreadDone = FALSE;
		while (TRUE) {
			if ( ! utilityHelper.PeekAndPump() )
				break;
			
			bThreadDone = event.Lock(0);
			if (bThreadDone)
				break;
		}

		if (parms.m_ReturnCode < 0) {
			progress.Hide();
			ads_printf("%s\n", parms.m_ReturnMessage);
			AfxMessageBox("An error occurred while counting product assignments.\n"
				"Please contact Optimize support.");
			return;
		}


		temp.Format("%d", parms.m_ReturnCode);
		m_ProductGroupListCtrl.SetItemText(i, 1, temp);
		
		m_MaximumCountMap->SetAt((*m_ProductGroupList)[i]->m_ProductGroupDBID, temp);

		progress.Step();

		if (progress.IsStopping())
			return;

	}

	Sleep(500);

	// Now count the actuals
	progress.UpdateMessage("Updating actual counts...");
	if (curSel < 0)
		progress.m_ProgressDialog->m_ProgressCtrl.SetRange32(0, m_ProductGroupList->GetSize()+1);
	else
		progress.m_ProgressDialog->m_ProgressCtrl.SetRange32(0, 1);
	
	progress.m_ProgressDialog->m_ProgressCtrl.SetPos(0);


	for (i=0; i < m_ProductGroupList->GetSize(); ++i) {

		if (curSel >= 0 && curSel != i)
			continue;

		results[0].Format("%d", (*m_ProductGroupList)[i]->m_ProductGroupDBID);

		event.ResetEvent();
		pThread = AfxBeginThread(CProductGroupAssignmentDialog::CountAssignmentsThread, &parms);
		
		bThreadDone = FALSE;
		while (TRUE) {
			if ( ! utilityHelper.PeekAndPump() )
				break;
			
			bThreadDone = event.Lock(0);
			if (bThreadDone)
				break;
		}

		if (parms.m_ReturnCode < 0) {
			progress.Hide();
			ads_printf("%s\n", parms.m_ReturnMessage);
			AfxMessageBox("An error occurred during assignment creation.\n"
				"Please contact Optimize support.");
			return;
		}

		progress.m_ProgressDialog->m_ProgressCtrl.StepIt();

		temp.Format("%d", parms.m_ReturnCode);
		m_ProductGroupListCtrl.SetItemText(i, 2, temp);

		m_ActualCountMap->SetAt((*m_ProductGroupList)[i]->m_ProductGroupDBID, temp);

		if (progress.IsStopping())
			return;

	}


	// Get the not assigned count
	if (curSel < 0 || curSel == m_ProductGroupList->GetSize()) {
		
		event.ResetEvent();

		pThread = AfxBeginThread(CProductGroupAssignmentDialog::GetUnassignedCountThread, &parms);
		
		bThreadDone = FALSE;
		while (TRUE) {
			if ( ! utilityHelper.PeekAndPump() )
				break;
			
			bThreadDone = event.Lock(0);
			if (bThreadDone)
				break;
		}
		
		if (parms.m_ReturnCode < 0) {
			progress.Hide();
			ads_printf("%s\n", parms.m_ReturnMessage);
			AfxMessageBox("An error occurred during assignment creation.\n"
				"Please contact Optimize support.");
			return;
		}
		
		progress.Step();
		
		temp.Format("%d", parms.m_ReturnCode);
		m_ProductGroupListCtrl.SetItemText(m_ProductGroupList->GetSize(), 1, temp);

		m_ActualCountMap->SetAt(-1, temp);
		
		Sleep(500);
	}

	return;
	
}

void CProductGroupAssignmentDialog::OnViewProducts() 
{
	CDisplayResults dlg;
	CStringArray productList;
	CString temp;
	int curSel, rc;
	POSITION pos;
	CProductGroupDataService service;

	pos = m_ProductGroupListCtrl.GetFirstSelectedItemPosition();
	if (pos == NULL) {
		AfxMessageBox("Please select a product group.");
		return;
	}

	curSel = m_ProductGroupListCtrl.GetNextSelectedItem(pos);

	try {
		CWaitCursor cwc;
		if (curSel < m_ProductGroupList->GetSize())
			rc = service.GetProductInfoByProductGroup(m_ProductGroupList->GetAt(curSel)->m_ProductGroupDBID, productList);
		else
			rc = service.GetProductInfoByProductGroup(0, productList);
	}
	catch(Ssa_Exception e) {
		utilityHelper.ProcessError("Error getting assigned products.", &e);
		return;
	}
	catch(...) {
		utilityHelper.ProcessError("Error getting assigned products.");
		return;
	}

	if (productList.GetSize() <= 0) {
		AfxMessageBox("No products assigned to the selected product group.");
		return;
	}

	if (productList.GetSize() >= 1) {
		CWaitCursor cwc;
		dlg.m_Headers.Add("WMS Product ID|WMS Detail ID|Description|Unit Of Issue|Case Pack|Inner Pack|");
		dlg.m_Tabs.Add("Products");
		CProductGroup *pGroup = m_ProductGroupList->GetAt(curSel);
		if (curSel < m_ProductGroupList->GetSize())
			temp.Format("Product Group Assignments - %s ", m_ProductGroupList->GetAt(curSel)->m_Description);
		else
			temp.Format("Product Group Assignments - Not Assigned");

		dlg.m_WindowCaptions.Add(temp);
		dlg.m_OrigColumnSize = 150;
		dlg.m_HelpTopics.Add("ProductGroupAssignmentDisplay_Main");
		dlg.m_MainHelpTopic = "ProductGroupAssignmentDisplay_Main";
	}

	CProductPack product;
	
	for (int i=0; i < productList.GetSize(); ++i) {
		product.ParseAll(productList[i]);
		BuildDisplayLine(&product, temp);
		dlg.m_Data.Add(temp);
	}
	try {
		int rc = dlg.DoModal();
	}
	catch (...) {
		AfxMessageBox("Error displaying products.");
	}
				
}

void CProductGroupAssignmentDialog::BuildDisplayLine(CProductPack *product, CString &line)
{
	CString uoi;

	switch (product->m_UnitOfIssue) {
	case UOI_EACH:
		uoi = "Each";
		break;
	case UOI_INNER:
		uoi = "Inner";
		break;
	case UOI_CASE:
		uoi = "Case";
		break;
	default:
		uoi = "Pallet";
		break;
	}

	line.Format("%s|%s|%s|%s|%d|%d|",
		product->m_WMSProductID,
		product->m_WMSProductDetailID,
		product->m_Description,
		uoi, product->m_CasePack, product->m_InnerPack);

	return;

}

UINT CProductGroupAssignmentDialog::AssignProductsThread(LPVOID pParam)
{
	CProductGroupDataService service;
	long productGroupDBID;
	int rc = 0;
	int priority, countOnly, deleteOnly, facilityId;
	CString msg("");
	
	CThreadParameters &parms = *(CThreadParameters *)pParam;

	productGroupDBID = atoi(parms.m_pInList->GetAt(0));
	priority = atoi(parms.m_pInList->GetAt(1));
	countOnly = atoi(parms.m_pInList->GetAt(2));
	deleteOnly = atoi(parms.m_pInList->GetAt(3));
	facilityId = atoi(parms.m_pInList->GetAt(4));

	try {
		rc = service.CreateProductGroupAssignments(facilityId, productGroupDBID, priority, 
			(countOnly == 1), (deleteOnly == 1));
	}
	catch (Ssa_Exception e) {
		char msgBuf[1024];
		e.GetMessage(msgBuf);
		msg = msgBuf;
		rc = -1;
	}
	catch (...) {
		rc = -1;
		msg = "Generic exception in AssignProductsThread.";
	}
	
	parms.m_ReturnCode = rc;
	parms.m_ReturnMessage = msg;
	parms.m_pEvent->SetEvent();

	return rc;


}

UINT CProductGroupAssignmentDialog::CountAssignmentsThread(LPVOID pParam)
{
	CProductGroupDataService service;
	long productGroupDBID;
	int rc = 0;
	CString msg("");
	
	CThreadParameters &parms = *(CThreadParameters *)pParam;

	productGroupDBID = atoi(parms.m_pInList->GetAt(0));

	try {
		rc = service.GetProductCountByProductGroup(productGroupDBID);
	}
	catch (Ssa_Exception e) {
		char msgBuf[1024];
		e.GetMessage(msgBuf);
		msg = msgBuf;
		rc = -1;
	}
	catch (...) {
		rc = -1;
		msg = "Generic exception in CountAssignmentsThread.";
	}
	
	parms.m_ReturnCode = rc;
	parms.m_ReturnMessage = msg;
	parms.m_pEvent->SetEvent();

	return rc;

}

UINT CProductGroupAssignmentDialog::GetUnassignedCountThread(LPVOID pParam)
{
	CProductGroupDataService service;
	long productGroupDBID;
	int rc = 0;
	CString msg("");
	
	CThreadParameters &parms = *(CThreadParameters *)pParam;

	productGroupDBID = atoi(parms.m_pInList->GetAt(0));

	try {
		rc = service.GetProductGroupUnassignedCount();
	}
	catch (Ssa_Exception e) {
		char msgBuf[1024];
		e.GetMessage(msgBuf);
		msg = msgBuf;
		rc = -1;
	}
	catch (...) {
		rc = -1;
		msg = "Generic exception in GetUnassignedCountThread.";
	}
	
	parms.m_ReturnCode = rc;
	parms.m_ReturnMessage = msg;
	parms.m_pEvent->SetEvent();

	return rc;

}


void CProductGroupAssignmentDialog::OnContextMenu(CWnd* pWnd, CPoint point) 
{
	UNREFERENCED_PARAMETER(point);
	POSITION pos;
	CMenu menu;
	menu.LoadMenu(IDR_PRODUCTGROUP_MENU);

	if (pWnd == &m_ProductGroupListCtrl) {
		pos = m_ProductGroupListCtrl.GetFirstSelectedItemPosition();
		if (pos == NULL)
			menu.GetSubMenu(1)->EnableMenuItem(2, MF_BYPOSITION|MF_GRAYED);
		else {
			menu.GetSubMenu(1)->EnableMenuItem(2, MF_BYPOSITION|MF_ENABLED);
			// If they selected unassigned, disable assign products option
			if (m_ProductGroupListCtrl.GetNextSelectedItem(pos) == m_ProductGroupListCtrl.GetItemCount()-1)
				menu.GetSubMenu(1)->EnableMenuItem(3, MF_BYPOSITION|MF_GRAYED);
		}
		menu.GetSubMenu(1)->TrackPopupMenu(TPM_LEFTALIGN|TPM_RIGHTBUTTON, point.x, point.y, this);
	}
	
}

BOOL CProductGroupAssignmentDialog::Create(LPCTSTR lpszClassName, LPCTSTR lpszWindowName, DWORD dwStyle, const RECT& rect, CWnd* pParentWnd, UINT nID, CCreateContext* pContext) 
{
	UNREFERENCED_PARAMETER(lpszClassName);
	UNREFERENCED_PARAMETER(lpszWindowName);
	UNREFERENCED_PARAMETER(dwStyle);
	UNREFERENCED_PARAMETER(rect);
	UNREFERENCED_PARAMETER(nID);
	UNREFERENCED_PARAMETER(pContext);

	CProductGroupNavigator *pNavigator;

	pNavigator = (CProductGroupNavigator *)m_ProductGroupFrame->m_Splitter.GetPane(0, 0);
	this->m_ProductGroupList = &pNavigator->m_ProductGroupList;
	this->m_ActualCountMap = &pNavigator->m_ActualCountMap;
	this->m_MaximumCountMap = &pNavigator->m_MaximumCountMap;

	BOOL bReturn = CDialog::Create(IDD, pParentWnd);

	int id;
	id = m_ProductGroupFrame->m_Splitter.IdFromRowCol(0, 1);

	if ( bReturn )
		::SetWindowLong ( m_hWnd, GWL_ID, id);
	
	return bReturn;
}

void CProductGroupAssignmentDialog::PostNcDestroy() 
{
	delete this;
}

BOOL CProductGroupAssignmentDialog::ValidateClose()
{

	return TRUE;
}

BOOL CProductGroupAssignmentDialog::OnHelpInfo(HELPINFO* pHelpInfo) 
{
	helpService.ShowFieldHelp(IDD, pHelpInfo->iCtrlId);
	return FALSE;
}

void CProductGroupAssignmentDialog::OnHelp() 
{
	helpService.ShowScreenHelp(IDD);	
}

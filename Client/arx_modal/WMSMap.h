// WMSMap.h: interface for the CWMSMap class.
//
//////////////////////////////////////////////////////////////////////

#if !defined(AFX_WMSMAP_H__385B2F8B_7BB5_45D7_AFF9_3DC6E8BF9055__INCLUDED_)
#define AFX_WMSMAP_H__385B2F8B_7BB5_45D7_AFF9_3DC6E8BF9055__INCLUDED_

#if _MSC_VER > 1000
#pragma once
#endif // _MSC_VER > 1000

class CWMSMap : public CObject  
{
public:
	int Parse(CString &line);
	CWMSMap();
	void operator=(CWMSMap& other);		// Fix by <PERSON><PERSON><PERSON>, inserted 'void'
	CWMSMap(CWMSMap& other);

	virtual ~CWMSMap();
	
	int m_WMSMapDBId;
	int m_WMSDBId;
	int m_SectionDBId;
	int m_FacilityDBId;
	CString m_WMSName;
	CString m_FacilityName;
	CString m_SectionName;
	int m_Direction;

	typedef enum {
		importToOptimize,
		exportToWMS
	} directionEnum;


};

#endif // !defined(AFX_WMSMAP_H__385B2F8B_7BB5_45D7_AFF9_3DC6E8BF9055__INCLUDED_)

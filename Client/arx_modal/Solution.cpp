// Solution.cpp: implementation of the CSolution class.
//
//////////////////////////////////////////////////////////////////////

#include "stdafx.h"
#include "modal.h"
#include "Solution.h"

#ifdef _DEBUG
#undef THIS_FILE
static char THIS_FILE[]=__FILE__;
#define new DEBUG_NEW
#endif

//////////////////////////////////////////////////////////////////////
// Construction/Destruction
//////////////////////////////////////////////////////////////////////

CSolution::CSolution()
{
	m_SolutionDBID = 0;
	m_ProductDBID = 0;
	m_LocationDBID = 0;
	m_LevelDBID = 0;
	m_CaseQuantity = 0;
	m_ProductWeight = 0;
	m_UnitOfIssue = -1;
	m_NumberInPallet = 0;
	m_CasePack = 0;
	m_InnerPack = 0;
	m_IsPrimary = -1;
	m_RotatedWidth = m_RotatedLength = m_RotatedHeight = -1;
}

CSolution::~CSolution()
{

}


CSolution& CSolution::operator=(const CSolution & other)
{
	m_SolutionDBID = other.m_SolutionDBID;
	m_ProductDBID = other.m_ProductDBID;
	m_LocationDBID = other.m_LocationDBID;
	m_LevelDBID = other.m_LevelDBID;
	m_CaseQuantity = other.m_CaseQuantity;
	m_ProductWeight = other.m_ProductWeight;
	m_UnitOfIssue = other.m_UnitOfIssue;
	m_NumberInPallet = other.m_NumberInPallet;
	m_CasePack = other.m_CasePack;
	m_InnerPack = other.m_InnerPack;
	m_IsPrimary = other.m_IsPrimary;
	m_RotatedWidth = other.m_RotatedWidth;
	m_RotatedLength = other.m_RotatedLength;
	m_RotatedHeight = other.m_RotatedHeight;

	return *this;
}

void CSolution::Parse(CString line)
{
	char *str;
	char *ptr;
	CString temp;
	temp = line;


	line.Replace("||", "| |");

	str = line.GetBuffer(0);
	
	ptr = strtok(str, "|");
	m_SolutionDBID = atoi(ptr);
	ptr = strtok(NULL, "|");
	m_ProductDBID = atoi(ptr);
	ptr = strtok(NULL, "|");
	m_LocationDBID = atoi(ptr);
	ptr = strtok(NULL, "|");
	m_LevelDBID = atoi(ptr);
	ptr = strtok(NULL, "|");
	m_CaseQuantity = atoi(ptr);
	ptr = strtok(NULL, "|");
	m_CasePack = atoi(ptr);
	ptr = strtok(NULL, "|");
	m_InnerPack = atoi(ptr);
	ptr = strtok(NULL, "|");
	m_NumberInPallet = atoi(ptr);
	ptr = strtok(NULL, "|");
	m_ProductWeight = atof(ptr);
	ptr = strtok(NULL, "|");
	m_UnitOfIssue = atoi(ptr);
	
	// no rotated dimensions on the way in for  now
	line.ReleaseBuffer();

	return;
}

// ProductGroupCriteriaQuery.h: interface for the CProductGroupCriteriaQuery class.
//
//////////////////////////////////////////////////////////////////////

#if !defined(AFX_PRODUCTGROUPCRITERIAQUERY_H__C2D63EB4_2840_11D5_9ECA_00C04FAC149C__INCLUDED_)
#define AFX_PRODUCTGROUPCRITERIAQUERY_H__C2D63EB4_2840_11D5_9ECA_00C04FAC149C__INCLUDED_

#if _MSC_VER > 1000
#pragma once
#endif // _MSC_VER > 1000

class CProductGroupCriteriaQuery : public CObject  
{
public:
	int Parse(CString &line);
	CProductGroupCriteriaQuery();
	virtual ~CProductGroupCriteriaQuery();
	CProductGroupCriteriaQuery& operator=(const CProductGroupCriteriaQuery &other);
	BOOL IsEqual(const CProductGroupCriteriaQuery &other);

	long m_CriteriaQueryDBID;
	CString m_Attribute;
	int m_AttributeType;
	CString m_Operator;
	CString m_Value;
	int m_Sequence;
	int m_Precedence;
	CString m_Conjunction;
	long m_CriteriaRangeDBID;
};

#endif // !defined(AFX_PRODUCTGROUPCRITERIAQUERY_H__C2D63EB4_2840_11D5_9ECA_00C04FAC149C__INCLUDED_)

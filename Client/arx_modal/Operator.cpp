// Operator.cpp: implementation of the COperator class.
//
//////////////////////////////////////////////////////////////////////

#include "stdafx.h"
#include "modal.h"
#include "Operator.h"

#ifdef _DEBUG
#undef THIS_FILE
static char THIS_FILE[]=__FILE__;
#define new DEBUG_NEW
#endif

//////////////////////////////////////////////////////////////////////
// Construction/Destruction
//////////////////////////////////////////////////////////////////////

COperator::COperator(const CString &display, const CString &internal, int operandCount)
{
	m_Internal = internal;
	m_Display = display;
	m_OperandCount = operandCount;
}

COperator::COperator()
{

}

COperator::~COperator()
{

}

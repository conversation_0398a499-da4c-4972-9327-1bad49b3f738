// SolutionDataService.cpp: implementation of the CSolutionDataService class.
//
//////////////////////////////////////////////////////////////////////

#include "stdafx.h"
#include "modal.h"
#include "DataAccessService.h"
#include "SolutionDataService.h"
#include "ForteService.h"
#include "TreeElement.h"
#include "UtilityHelper.h"
#include "ProductInfo.h"
#include "LocationInfo.h"
#include "ManualAssignmentDialog.h"
#include "ControlService.h"
#include "DisplayResults.h"
#include "ColoringHelper.h"
#include "InterfaceHelper.h"

#include <rxmfcapi.h>

#ifdef _DEBUG
#undef THIS_FILE
static char THIS_FILE[]=__FILE__;
#define new DEBUG_NEW
#endif

extern CControlService controlService;
extern CForteService forteService;
extern TreeElement changesTree;
extern CDataAccessService dataAccessService;
extern CUtilityHelper utilityHelper;

//////////////////////////////////////////////////////////////////////
// Construction/Destruction
//////////////////////////////////////////////////////////////////////

CSolutionDataService::CSolutionDataService()
{

}

CSolutionDataService::~CSolutionDataService()
{

}


int CSolutionDataService::AddToPrimary(int productDBID, int caseCount)
{
	CString queryText;

	queryText.Format("update dbslotsolution set casequantity = casequantity + %d"
		"where dbproductpackid = %d "
		"and isprimary = 1 "
		"and origin = %d", caseCount, productDBID, CSolution::Optimize);

		
	return dataAccessService.ExecuteStatement("AddToPrimary", queryText);

}


int CSolutionDataService::GetSolutionsByBay(int bayDBID, CStringArray &baySolutions)
{

	CString queryText;

	queryText.Format("select ss.dbslotsolutionid, ss.dbproductpackid, ss.dblocationid, l.dblevelid, "
		"ss.casequantity, pp.casepack, pp.innerpack, pp.numberinpallet, pp.weight, pp.unitofissue "
		"from dbslotsolution ss, dblocation l, dblevel le, dbproductpack pp "
		"where ss.dblocationid = l.dblocationid "
		"and l.dblevelid = le.dblevelid "
		"and le.dbbayid = %d "
		"and pp.dbproductpackid = ss.dbproductpackid "
		"and ss.origin = %d", 
		bayDBID, CSolution::Optimize);
		

	return dataAccessService.ExecuteQuery("GetSolutionsByBay", queryText, baySolutions);

}



int CSolutionDataService::GetPrimaryFacing(int productDBID, CString &primaryLocString)
{
	CString queryText;
	CStringArray resultList;

	queryText.Format("select l.dblocationid, l.description, l.dblevelid "
		"from dblocation l, dbslotsolution ss "
		"where ss.dbproductpackid = %d "
		"and ss.dblocationid = l.dblocationid "
		"and ss.isprimary = 1 "
		"and ss.origin = %d",
		productDBID, CSolution::Optimize);
		

	try {
		dataAccessService.ExecuteQuery("GetPrimaryFacing", queryText, resultList);
	}
	catch (...) {
		primaryLocString = "";
		return -1;
	}

	if ( resultList.GetSize() == 0 ) {
		primaryLocString = "";
		return -1;
	}
	else {
		primaryLocString = resultList[0];
		return 0;
	}

}

int CSolutionDataService::GetFacingCountByProduct(int productDBID)
{
	CString queryText;
	CStringArray resultList;

	queryText.Format("select count(*) from dbslotsolution "
		"where dbproductpackid = %d "
		"and origin = %d", productDBID, CSolution::Optimize);
	
	dataAccessService.ExecuteQuery("GetFacingCountByProduct", queryText, resultList, TRUE);

	if (resultList.GetSize() == 0)
		return 0;

	return atoi(resultList[0]);
}


int CSolutionDataService::StoreSolution(CSolution &solution)
{
	CString queryText;
	CStringArray resultList;
	int nextKey;

	nextKey = dataAccessService.GetNextKey("DBSlotSolution", 1);

	queryText.Format("insert into dbslotsolution  "
		"(dbslotsolutionid, casequantity, numberptwys,  "
		"numberinpallet, ptwytravelhours, ptwyhandlinghours, ptwycost, numberrplns,  "
		"rplndistance, rplnhours, rplnhandlinghours, rplncost, brokenorderdist,  "
		"brokenordercount, pickdistance, numberorders, selectdistance, selectscale,  "
		"selecttravelhours, selecthandlehours, totaltime, casesperhour, selectcost,  "
		"totalcost, rotatedwidth, rotatedlength, rotatedheight, forkdistance, isprimary,  "
		"stockerhandlehours, forkhandlehours, totalstockercases, totalpalletselects,  "
		"stockercost, forkcost, seltimesmovement, stkrtimesmovement, forktimesmovement,  "
		"createdate, changedate, lastuserid, dblocationid, dbproductpackid, origin) "
		"values  "
		"(%d, %d, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, "
		"0, %f, %f, %f, 0, %d, 0, 0, 0, 0, 0, 0, 0, 0, 0, sysdate, sysdate, 1, %d, %d, %d)",
		nextKey, solution.m_CaseQuantity, solution.m_RotatedWidth, solution.m_RotatedLength,
		solution.m_RotatedHeight, solution.m_IsPrimary, solution.m_LocationDBID, 
		solution.m_ProductDBID, solution.m_Origin);

		
	return dataAccessService.ExecuteStatement("StoreSolution", queryText);

}




int CSolutionDataService::DeleteSolutionsByProduct(int productDBID)
{
	CString queryText;

	queryText.Format("delete from dbslotsolution where dbproductpackid = %d "
		"and origin = %d", productDBID, CSolution::Optimize);

		
	return dataAccessService.ExecuteStatement("DeleteSolutionsByProduct", queryText);
}


int CSolutionDataService::DeleteSolutionsByLocation(int locationDBID)
{
	CString queryText;

	queryText.Format("delete from dbslotsolution where dblocationid = %d "
		"and origin = %d", locationDBID, CSolution::Optimize);

	return dataAccessService.ExecuteStatement("DeleteSolutionsByLocation", queryText);
}

int CSolutionDataService::GetSolutionLocationsByRange(int facilityId, const CProductAttribute *pAttr, 
		int level, int origin, double minHeight, double maxHeight, const CString &startValue, const CString &endValue, 
		CStringArray& results)
{
	CString sql, temp;
	CString selectClause, fromClause, whereClause, valueClause;

	selectClause = "select b.acadhandle, ";

	fromClause = "from dbproduct p, dbproductpack, dbslotsolution ss, dblocation l, dblevel le, dbbay b, "
		"dblevelprofile lp ";
	
	whereClause.Format("where p.dbfacilityid = %d "
		"and p.dbproductid = dbproductpack.dbproductid "
		"and dbproductpack.dbproductpackid = ss.dbproductpackid "
		"and ss.dblocationid = l.dblocationid "
		"and l.dblevelid = le.dblevelid "
		"and le.dbbayid = b.dbbayid "
		"and lp.dblevelprofileid = le.dblevelprofileid "
		"and ss.origin = %d ", facilityId, origin);

	if (pAttr->m_TableName == TB_PRODUCT) {
		selectClause += pAttr->m_TableName;
		selectClause += ".";
		selectClause += pAttr->m_ColumnName;
		valueClause.Format("and %s.%s between ", pAttr->m_TableName, pAttr->m_ColumnName);
	}
	else if (pAttr->m_TableName == TB_PRODUCTCONTAINER) {
		selectClause += pAttr->m_TableName;
		selectClause += ".";
		selectClause += pAttr->m_ColumnName;
		fromClause += ", dbprodcontainer ";
		whereClause += "and dbproductpack.dbproductpackid = dbprodcontainer.dbproductpackid ";
		valueClause.Format("and %s.%s between ", pAttr->m_TableName, pAttr->m_ColumnName);
	}
	else if (pAttr->m_TableName == TB_PRODUCTUDFVAL) {
		selectClause += "udf.value";
		fromClause += ", dbprodpkudfval udf ";
		whereClause += "and udf.dbproductpackid = dbproductpack.dbproductpackid "
			"and udf.dbprodpkudflistid = ";
		temp.Format("%d ", pAttr->m_AttributeDBID);
		whereClause += temp;

		switch (pAttr->m_Type) {
		case DT_INT:
			valueClause.Format("and udf.integervalue between ");
			break;
		case DT_FLOAT:
			valueClause.Format("and udf.floatvalue between ");
			break;
		default:
			valueClause.Format("and udf.value between ");
			break;
		}
	}

	switch (pAttr->m_Type) {
	case DT_INT:
		temp.Format("%d and %d ", atoi(startValue), endValue.IsEmpty() ? atoi(startValue) : atoi(endValue));
		valueClause += temp;
		break;
	case DT_FLOAT:
		temp.Format("%f and %f ", atof(startValue), endValue.IsEmpty() ? atof(startValue) : atof(endValue));
		valueClause += temp;
		break;
	default:
		temp.Format("'%s' and '%s' ", startValue, endValue.IsEmpty() ? startValue : endValue);
		valueClause += temp;
	}


	if (level > 0) {
		temp.Format("and lp.relativelevel = %d ", level);
		whereClause += temp;
	}

	if (minHeight >= 0) {
		temp.Format("and l.zcoordinate >= %f ", minHeight);
		whereClause += temp;
	}

	if (maxHeight >= 0) {
		temp.Format("and l.zcoordinate <= %f ", maxHeight);
		whereClause += temp;
	}

	selectClause += " ";

	sql.Format("%s "
		"%s "
		"%s "
		"%s "
		"order by 2 desc", selectClause, fromClause, whereClause, valueClause);


	return dataAccessService.ExecuteQuery("GetSolutionLocationsByRange", sql, results);


}



int CSolutionDataService::GetSolutionLocationsByAttribute(int facilityId, const CProductAttribute *pAttr, 
														  int multiType, int level, int origin,
														  double minHeight, double maxHeight,
														  CStringArray& results)
{
	CString sql, temp;
	CString selectClause, fromClause, whereClause;

	selectClause = "select b.acadhandle, ";

	switch (multiType) {
	case CColoringHelper::Avg:
		selectClause += "avg(";
		break;
	case CColoringHelper::Min:
		selectClause += "min(";
		break;
	case CColoringHelper::Max:
		selectClause += "max(";
		break;
	case CColoringHelper::Sum:
	default:
		selectClause += "sum(";
		break;
	}

	fromClause = "from dbproduct p, dbproductpack, dbslotsolution ss, dblocation l, dblevel le, dbbay b, "
		"dblevelprofile lp ";
	
	whereClause.Format("where p.dbfacilityid = %d "
		"and p.dbproductid = dbproductpack.dbproductid "
		"and dbproductpack.dbproductpackid = ss.dbproductpackid "
		"and ss.dblocationid = l.dblocationid "
		"and l.dblevelid = le.dblevelid "
		"and le.dbbayid = b.dbbayid "
		"and lp.dblevelprofileid = le.dblevelprofileid "
		"and ss.origin = %d ", facilityId, origin);

	if (pAttr->m_TableName == "DBProductPack") {
		selectClause += pAttr->m_TableName;
		selectClause += ".";
		selectClause += pAttr->m_ColumnName;
	}
	else if (pAttr->m_TableName == "DBProdContainer") {
		selectClause += pAttr->m_TableName;
		selectClause += ".";
		selectClause += pAttr->m_ColumnName;
		fromClause += ", dbprodcontainer ";
		whereClause += "and dbproductpack.dbproductpackid = dbprodcontainer.dbproductpackid ";
	}
	else if (pAttr->m_TableName == "DBProdPKUDFVal") {
		selectClause += "udf.value";
		fromClause += ", dbprodpkudfval udf ";
		whereClause += "and udf.dbproductpackid = dbproductpack.dbproductpackid "
			"and udf.dbprodpkudflistid = ";
		temp.Format("%d ", pAttr->m_AttributeDBID);
		whereClause += temp;
	}

	if (level > 0) {
		temp.Format("and lp.relativelevel = %d ", level);
		whereClause += temp;
	}

	if (minHeight >= 0) {
		temp.Format("and l.zcoordinate >= %f ", minHeight);
		whereClause += temp;
	}

	if (maxHeight >= 0) {
		temp.Format("and l.zcoordinate <= %f ", maxHeight);
		whereClause += temp;
	}

	selectClause += ") ";

	sql.Format("%s "
		"%s "
		"%s "
		"group by b.acadhandle "
		"order by 2 desc", selectClause, fromClause, whereClause);


	return dataAccessService.ExecuteQuery("GetSolutionLocationsByAttribute", sql, results);

}


int CSolutionDataService::GetAssignments(CStringArray &assignmentList, long productGroupID, BOOL fullExport, long sectionID)
{
	CString queryText;
	CString temp1, temp2, temp3, temp4;

	if (productGroupID > 0) {
		temp1.Format(", dbprodslotgroup psg ");
		temp2.Format("and psg.dbproductpackid = pp.dbproductpackid and psg.dbslottinggroupid = %d ", productGroupID);
	}
	else {
		temp1 = "";
		temp2 = "";
	}

	// temporary hack so that we can run it multiple times without getting all the data every time
	if (! fullExport)
		temp3 = "and ss.lastuserid <> 2 ";
	else
		temp3 = "";
	
	if (sectionID > 0)
		temp4.Format("and a.dbsectionid = %d ", sectionID);
	else 
		temp4 = "";

	// Assignments without a search anchor point that are either primary or non-floating
	queryText.Format("select pp.wmsproductid \"Product ID\", "
		"pp.wmsproductdetailid \"Product Detail ID\", lo.description \"Location\", '' \"Search Anchor Point\", "
		"ss.isprimary \"Primary\", ss.casequantity \"Case Capacity\", a.dbsectionid "
		"from dbproduct p, dbproductpack pp, dbslotsolution ss, "
		"dblocation lo, dblevel le, dbbay b, dbbayprofile bp, dbside si, dbaisle a "
		"%s "	// , dbprodslotgroup psg
		"where p.dbfacilityid = %d "
		"and ss.origin = %d "
		"and pp.dbproductid = p.dbproductid "
		"and ss.dbproductpackid = pp.dbproductpackid "
		"and lo.dblocationid = ss.dblocationid "
		"and le.dblevelid = lo.dblevelid "
		"and b.dbbayid = le.dbbayid "
		"and si.dbsideid = b.dbsideid "
		"and a.dbaisleid = si.dbaisleid "
		"and bp.dbbayprofileid = b.dbbayprofileid "
		"and ( (bp.isfloating = 1 and ss.isprimary = 1) or (bp.isfloating = 0)) "
		"and not exists ( select sap.dbsearchanchorid "
		"from dbsearchanchor sap "
		"where sap.dbfacilityid = p.dbfacilityid "
		"and sap.startinglocation <= lo.description "
		"and sap.endinglocation >= lo.description) "
		"%s "	// and slottinggroupid = 
		"%s "	// and lastuserid <> 2
		"%s "	// and sectionid = 
		"union "
		// Assignments with a search anchor point that are non floating
		"select pp.wmsproductid \"Product ID\", "
		"pp.wmsproductdetailid \"Product Detail ID\", lo.description \"Location\", "
		"'' \"Search Anchor Point\", "
		"ss.isprimary \"Primary\", ss.casequantity \"Case Capacity\", a.dbsectionid "
		"from dbproduct p, dbproductpack pp, dbslotsolution ss, "
		"dblocation lo, dblevel le, dbbay b, dbbayprofile bp, "
		"dbsearchanchor sap, dbside si, dbaisle a "
		"%s "	// , dbprodslotgroup
		"where p.dbfacilityid = %d "
		"and pp.dbproductid = p.dbproductid "
		"and ss.dbproductpackid = pp.dbproductpackid "
		"and ss.origin = %d "
		"and lo.dblocationid = ss.dblocationid "
		"and le.dblevelid = lo.dblevelid "
		"and b.dbbayid = le.dbbayid "
		"and si.dbsideid = b.dbsideid "
		"and a.dbaisleid = si.dbaisleid "
		"and bp.dbbayprofileid = b.dbbayprofileid "
		"and bp.isfloating = 0 "
		"and sap.dbfacilityid = p.dbfacilityid "
		"and sap.startinglocation <= lo.description "
		"and sap.endinglocation >= lo.description "
		"%s "	// and slottinggroupid = 
		"%s "	// and lastuserid
		"%s "	// and sectionID = 
		"union "
		// Assignments with a search anchor point that are primary and floating
		"select pp.wmsproductid \"Product ID\", "
		"pp.wmsproductdetailid \"Product Detail ID\", lo.description \"Location\", "
		"sap.searchanchorpoint \"Search Anchor Point\", "
		"ss.isprimary \"Primary\", ss.casequantity \"Case Capacity\", a.dbsectionid "
		"from dbproduct p, dbproductpack pp, dbslotsolution ss, "
		"dblocation lo, dblevel le, dbbay b, dbbayprofile bp, "
		"dbsearchanchor sap, dbside si, dbaisle a "
		"%s "	// , dbprodslotgroup
		"where p.dbfacilityid = %d "
		"and pp.dbproductid = p.dbproductid "
		"and ss.dbproductpackid = pp.dbproductpackid "
		"and ss.origin = %d "
		"and lo.dblocationid = ss.dblocationid "
		"and le.dblevelid = lo.dblevelid "
		"and b.dbbayid = le.dbbayid "
		"and si.dbsideid = b.dbsideid "
		"and a.dbaisleid = si.dbaisleid "
		"and bp.dbbayprofileid = b.dbbayprofileid "
		"and bp.isfloating = 1 and ss.isprimary = 1 "
		"and sap.dbfacilityid = p.dbfacilityid "
		"and sap.startinglocation <= lo.description "
		"and sap.endinglocation >= lo.description "
		"%s "	// and slottinggroupid = 
		"%s "	// and lastuserid = 
		"%s "	// and sectionID = 
		"order by 3, 4, 7 desc, 5 ",
		temp1, 	changesTree.elementDBID, CSolution::Optimize, temp2, temp3, temp4,
		temp1, changesTree.elementDBID, CSolution::Optimize, temp2, temp3, temp4,
		temp1, changesTree.elementDBID, CSolution::Optimize, temp2, temp3, temp4);

			
	dataAccessService.ExecuteQuery("GetAssignments", queryText, assignmentList);

	queryText.Format("update dbslotsolution "
		"set lastuserid = 2 "
		"where exists ( select dbproductpackid "
		"from dbproductpack pp, dbproduct p "
		"where pp.dbproductid = p.dbproductid "
		"and p.dbfacilityid = %d "
		"and pp.dbproductpackid = dbslotsolution.dbproductpackid) "
		"and origin = %d",
		changesTree.elementDBID, CSolution::Optimize);
		//"and lastuserid <> 2",	// this seems to make the query slower

	dataAccessService.ExecuteStatement("UpdateAssignments", queryText);

	if ( assignmentList.GetSize() == 0 )
		return 0;
	else {
		return assignmentList.GetSize();
	}


}


int CSolutionDataService::GetProductForLocation(int locationDBID, CString &productWMSID, 
						  CString &productWMSDetailID, CString &productDescription, int origin)
{
	CStringArray results;
	CString queryText;
	queryText.Format("select pp.wmsproductid, pp.wmsproductdetailid, pp.description "
		"from dblocation l, dbslotsolution ss, dbproductpack pp "
		"where l.dblocationid = %d "
		"and l.dblocationid = ss.dblocationid "
		"and ss.origin = %d "
		"and ss.dbproductpackid = pp.dbproductpackid", locationDBID, origin);
	
	int rc = dataAccessService.ExecuteQuery("GetProductForLocation", queryText, results);

	if ( results.GetSize() == 0 )
		return 0;
	else {
		CString temp = results[0];
		int idx = temp.Find("|");
		productWMSID = temp.Left(idx);
		temp = temp.Right(temp.GetLength() - (idx+1));
		idx = temp.Find("|");
		productWMSDetailID = temp.Left(idx);
		temp = temp.Right(temp.GetLength() - (idx+1));
		idx = temp.Find("|");
		if (idx < 0) idx = temp.GetLength();
		productDescription = temp.Left(idx);

		return 1;
	}

}

int CSolutionDataService::GetMultipleProductsInLocations(int facilityDBID, CStringArray &results)
{
	CString queryText;

	queryText.Format("select 'D', l.description, count(*) "

		"from dbsection s, dbaisle a, dbside si, dbbay b, "
		"dblevel le, dblocation l, dblocationprof lop, dbslotsolution ss "
		"where s.dbfacilityid = %d "
		"and s.dbsectionid = a.dbsectionid "
		"and a.dbaisleid = si.dbaisleid "
		"and si.dbsideid = b.dbsideid "
		"and b.dbbayid = le.dbbayid "
		"and le.dblevelid = l.dblevelid "
		"and l.dblocationprofid = lop.dblocationprofid "
		"and ss.dblocationid = l.dblocationid "
		"and ss.origin = %d "
		//"and ((lop.isselect = 1 and l.isoverridden = 0) or (l.isselect = 1 and l.isoverridden = 1)) "
		"group by l.description "
		"having count(*) > 1 "
		"order by l.description",
		facilityDBID, CSolution::Optimize);
		

	return dataAccessService.ExecuteQuery("GetMultipleProductsInLocation", queryText, results);

}


int CSolutionDataService::GetProductForLocation(int locationDBID, CString &productWMSID, 
												CString &productWMSDetailID, CString &productDescription, 
												long &prodDBID, int origin)
{
	CStringArray results;
	CString queryText;

	/////////////////////////////////////////////////////////////
	// build the data to send to the Forte connection server
	/////////////////////////////////////////////////////////////

	queryText.Format("select pp.wmsproductid, pp.wmsproductdetailid, pp.description, pp.dbproductpackid "
		"from dblocation l, dbslotsolution ss, dbproductpack pp "
		"where l.dblocationid = %d "
		"and l.dblocationid = ss.dblocationid "
		"and ss.dbproductpackid = pp.dbproductpackid "
		"and ss.origin = %d", locationDBID, origin);
			

	dataAccessService.ExecuteQuery("GetProductForLocation", queryText, results);

	if ( results.GetSize() == 0 )
		return 0;
	else {
		CStringArray strings;
		utilityHelper.ParseString(results[0], "|", strings);
		productWMSID = strings[0];
		productWMSDetailID = strings[1];
		productDescription = strings[2];
		prodDBID = atol(strings[3]);

		return 1;
	}

}


int CSolutionDataService::UpdateSolutionCaseCount(int facilityId, int origin, int override, CStringArray &msgList, 
												  CStringArray& notFitList)
{
	CString sql;
	CStringArray results, strings;
	CProductPack product;
	CLocation location;
	CProductContainer container;

	int caseCount;

	CWaitCursor cwc;

	sql.Format("select pp.*, '<EOP>', pc.*, '<EOC>', l.*, '<EOL>', lp.baytype, lp.isrotateallowed, ss.isprimary, "
		"ss.dbslotsolutionid "
		"from dbproduct p, dbproductpack pp, dbprodcontainer pc, dbslotsolution ss, "
		"dblocation l, dblevel le, dblevelprofile lp "
		"where p.dbfacilityid = %d "
		"and pp.dbproductid = p.dbproductid "
		"and pc.dbproductpackid = pp.dbproductpackid "
		"and ss.dbproductpackid = pp.dbproductpackid "
		"and l.dblocationid = ss.dblocationid "
		"and l.dblevelid = le.dblevelid "
		"and lp.dblevelprofileid = le.dblevelprofileid "
		"%s "
		"and ss.origin = %d "
		"order by ss.dbproductpackid, ss.isprimary", facilityId, 
		override ? "" : "and ss.casequantity < 0", origin);


	try {
		dataAccessService.ExecuteQuery("GetProductsWithSolutions", sql, results);
	}
	catch (...) {
		utilityHelper.ProcessError("Error getting products with solutions.");
		return -1;
	}

	int primaryCaseCount = 0;
	int prevProductId = -1;

	for (int i=0; i < results.GetSize(); ++i) {
		int idx;
		CString line, temp;
		
		line = results[i];
		idx = line.Find("<EOP>");
		temp = line.Left(idx);

		product.Parse(temp);
		
		line = line.Mid(idx+6);

		idx = line.Find("<EOC>");
		temp = line.Left(idx);

		container.Parse(temp);
		product.m_Container = container;

		line = line.Mid(idx+6);

		idx = line.Find("<EOL>");
		temp = line.Left(idx);

		location.Parse(temp);

		line = line.Mid(idx+6);

		utilityHelper.ParseString(line, "|", strings);

		int bayType = atoi(strings[0]);
		int rotateAllowed = atoi(strings[1]);
		int isPrimary = atoi(strings[2]);
		int ssId = atoi(strings[3]);
	
		if (product.m_ProductPackDBID != prevProductId) {
			primaryCaseCount = 0;
			prevProductId = product.m_ProductPackDBID;
		}

		caseCount = GetCaseCount(product, location, bayType, rotateAllowed, msgList);
		if (caseCount == 0) {
			CString temp;
			temp = product.m_WMSProductID;
			temp += "-";
			temp += product.m_WMSProductDetailID;
			temp += "|";
			notFitList.Add(temp);
			caseCount = 1;
		}
		
		primaryCaseCount += caseCount;

		sql.Format("update dbslotsolution set casequantity = %d, rotatedwidth = %f, "
			"rotatedlength = %f, rotatedheight = %f "
			"where dbslotsolutionid = %d", 
			isPrimary ? primaryCaseCount : 0, product.m_RotatedWidth, product.m_RotatedLength,
			product.m_RotatedHeight, ssId);
			
		try {
			dataAccessService.ExecuteStatement("UpdatePrimaryCount", sql);
		}
		catch (...) {
			controlService.Log("Error updating assignment case quantities.",
				"Generic exception in UpdateSolutionCaseCount");
			return -1;
		}
		
	}
	
	return 0;

}


int CSolutionDataService::UpdateSolutionCaseCount(const CStringArray &batchList, CStringArray &msgList)
{
	CString sql;
	CStringArray results, strings;
	CProductPack product;
	CLocation location;
	CProductContainer container;
	CStringArray stmts;

	int caseCount;

	CWaitCursor cwc;

	for (int i=0; i < batchList.GetSize(); ++i) {
		
		sql.Format("select pp.*, '<EOP>', pc.*, '<EOC>', l.*, '<EOL>', lp.baytype, lp.isrotateallowed, ss.isprimary, "
			"ss.dbslotsolutionid "
			"from dbproduct p, dbproductpack pp, dbprodcontainer pc, dbslotsolution ss, "
			"dblocation l, dblevel le, dblevelprofile lp, dbassignmentqueue q "
			"where pp.dbproductid = p.dbproductid "
			"and pc.dbproductpackid = pp.dbproductpackid "
			"and ss.dbproductpackid = pp.dbproductpackid "
			"and l.dblocationid = ss.dblocationid "
			"and l.dblevelid = le.dblevelid "
			"and lp.dblevelprofileid = le.dblevelprofileid "
			"and ss.origin = %d "
			"and q.dbfacilityid = p.dbfacilityid "
			"and q.productkey = pp.externalkey "
			"and q.batchid = %s "
			"and q.sender = %d "
			"order by ss.dbproductpackid, ss.isprimary", CSolution::Baseline, batchList[i], 
			CInterfaceHelper::WMSSender);
		
		results.RemoveAll();
		try {
			dataAccessService.ExecuteQuery("GetInboundProductsWithSolutions", sql, results);
		}
		catch (...) {
			utilityHelper.ProcessError("Error getting products with solutions.");
			return -1;
		}
		
		int primaryCaseCount = 0;
		int prevProductId = -1;
		
		for (int j=0; j < results.GetSize(); ++j) {
			int idx;
			CString line, temp;
			
			line = results[j];
			idx = line.Find("<EOP>");
			temp = line.Left(idx);
			
			product.Parse(temp);
			
			line = line.Mid(idx+6);
			
			idx = line.Find("<EOC>");
			temp = line.Left(idx);
			
			container.Parse(temp);
			product.m_Container = container;
			
			line = line.Mid(idx+6);
			
			idx = line.Find("<EOL>");
			temp = line.Left(idx);
			
			location.Parse(temp);
			
			line = line.Mid(idx+6);
			
			utilityHelper.ParseString(line, "|", strings);
			
			int bayType = atoi(strings[0]);
			int rotateAllowed = atoi(strings[1]);
			int isPrimary = atoi(strings[2]);
			int ssId = atoi(strings[3]);
			
			if (product.m_ProductPackDBID != prevProductId) {
				primaryCaseCount = 0;
				prevProductId = product.m_ProductPackDBID;
			}
			
			caseCount = GetCaseCount(product, location, bayType, rotateAllowed, msgList);
			if (caseCount == 0) {
				sql.Format("update dbassignmentqueue set reasoncode = 1, reasontext = '%s' "
					"where productkey = %d "
					"and batchid = %s "
					"and sender = %d ", "Warning. Product does not fit in location.",
					product.m_ProductKey, batchList[i], CInterfaceHelper::WMSSender);
				stmts.Add(sql);
				caseCount = 1;
			}
			
			primaryCaseCount += caseCount;
			
			sql.Format("update dbslotsolution set casequantity = %d, rotatedwidth = %f, "
				"rotatedlength = %f, rotatedheight = %f "
				"where dbslotsolutionid = %d", 
				isPrimary ? primaryCaseCount : 0, product.m_RotatedWidth, product.m_RotatedLength,
				product.m_RotatedHeight, ssId);
			
			stmts.Add(sql);
		}		
	}
	
	if (stmts.GetSize() == 0)
		return 0;

	try {
		dataAccessService.ExecuteStatements("UpdateInboundSolutionCaseCount", stmts);
	}
	catch (...) {
		controlService.Log("", "Generic exception in UpdateSolutionCaseCount.\n");
		return -1;
	}

	return 0;

}

int CSolutionDataService::GetCaseCount(CProductPack &product, const CLocation &location, int bayType,
									   int rotateAllowed, CStringArray &msgList)
{

	CString temp;
	int cases, origCases, maxCases;
	double prodWidth, prodLength, prodHeight;
	BOOL bGetMaxCases = TRUE;
	CString prodStr;

	prodStr.Format("%s-%s(%d)->%s", product.m_WMSProductID, product.m_WMSProductDetailID, product.m_ProductKey,
		location.m_Description);

	// determine the number of cases that will fit
	// if none fit, display the dimensions to the user and ask if they
	// want to override

	if (product.m_CaseWidth <= 0 || product.m_CaseLength <= 0 || product.m_CaseHeight <= 0) {
		temp.Format("%s: Invalid product dimensions. (%-4.1f x %-4.1f x %-4.1f).\n",
			prodStr, product.m_CaseWidth, product.m_CaseLength, product.m_CaseHeight);
		msgList.Add(temp);
		return 0;
	}

	if (location.m_Width <= 0 || location.m_Depth <= 0 || location.m_Height <= 0) {
		temp.Format("%s: Invalid location dimensions. (%-5.1f x %-5.1f x %-5.1f).\n",
			prodStr, location.m_Width, location.m_Depth, location.m_Height);
		msgList.Add(temp);
		return 0;
	}

	if (location.m_HandlingMethod == PALLET_HANDLING) {

		prodWidth = product.m_Container.m_Width;
		prodLength = product.m_Container.m_Length;
		prodHeight = product.m_Container.m_Hi * product.m_CaseHeight + product.m_Container.m_Height;
		if (product.m_Container.m_IsHeightOverride != 0)
			prodHeight = product.m_Container.m_Height;

		cases = GetPalletHandlingCount(location.m_Width, location.m_Depth, location.m_Height,
			prodWidth, prodLength, prodHeight);
		
		origCases = cases;
		maxCases = cases;

		// only rotate on the z-axis for pallet handling (switch width and length)
		if ( (cases == 0 || bGetMaxCases) && product.m_RotateZAxis && rotateAllowed) {
			cases = GetPalletHandlingCount(location.m_Width, location.m_Depth, location.m_Height,
				prodLength, prodWidth, prodHeight);
			if (cases > 0 && origCases == 0) {
				temp.Format("%s: The product will not fit unless the width and length are switched.\n", prodStr);
				msgList.Add(temp);
				
				product.m_RotatedWidth = prodLength;
				product.m_RotatedLength = prodWidth;
				product.m_RotatedHeight = -1.0;
			}

			if (cases > maxCases)
				maxCases = cases;
		}

		cases = maxCases;

		if (product.m_NumberInPallet > 0)
			cases = cases * product.m_NumberInPallet;
		else
			cases = cases * (product.m_Container.m_Ti * product.m_Container.m_Hi);
		
		if (cases <= 0) {
			temp.Format("%s: The product will not fit in the location.\n\tProduct: %-4.1f x %-4.1f x %-4.1f\n"
				"\tLocation: %-4.1f x %-4.1f x %-4.1f.\n\tProduct rotation is %s.\n\tLocation rotation is %s.\n",
				prodStr, prodWidth, prodLength, prodHeight, 
				location.m_Width, location.m_Depth, location.m_Height,
				product.m_RotateZAxis ? "on" : "off", 
				rotateAllowed ? "on" : "off");
			msgList.Add(temp);
		}
		
	}
	else {		// case handling
		
		// Always fit flow type racks using case dimensions
		if (bayType == BAYTYPE_CASEFLOW || product.m_UnitOfIssue == UOI_PALLET) {
			prodWidth = product.m_CaseWidth;
			prodLength = product.m_CaseLength;
			prodHeight = product.m_CaseHeight;
		}
		else {
			prodWidth = product.m_CaseWidth;
			prodLength = product.m_CaseLength;
			prodHeight = product.m_CaseHeight;
		}
		
		cases = GetCaseHandlingCount(location.m_Width, location.m_Depth, location.m_Height,
			prodWidth, prodLength, prodHeight, bayType);
		
		origCases = cases;
		maxCases = cases;
		
		if ((cases == 0 || bGetMaxCases) && product.m_RotateZAxis && rotateAllowed) {
			cases = GetCaseHandlingCount(location.m_Width, location.m_Depth, location.m_Height,
				prodLength, prodWidth, prodHeight, bayType);

			if (cases > 0 && origCases == 0) {
				temp.Format("%s: The product will not fit unless the width and length are switched.\n", prodStr);
				msgList.Add(temp);
			}

			if (cases > maxCases)
				maxCases = cases;
		}
		
		if ((cases == 0 || bGetMaxCases) && product.m_RotateXAxis && rotateAllowed) {
			cases = GetCaseHandlingCount(location.m_Width, location.m_Depth, location.m_Height,
				prodWidth, prodHeight, prodLength, bayType);
			if (cases > 0 && origCases == 0) {
				temp.Format("%s: The product will not fit unless the length and height are switched.\n", prodStr);
				msgList.Add(temp);
			}

			if (cases > maxCases)
				maxCases = cases;
		}
		
		
		if ((cases == 0 || bGetMaxCases) && product.m_RotateYAxis && rotateAllowed) {
			cases = GetCaseHandlingCount(location.m_Width, location.m_Depth, location.m_Height,
				prodHeight, prodLength, prodWidth, bayType);
			if (cases > 0 && origCases == 0) {
				temp.Format("%s: The product will not fit unless the width and height are switched.\n", prodStr);
				msgList.Add(temp);
			}

			if (cases > maxCases)
				maxCases = cases;
		}
		
		if ((cases == 0 || bGetMaxCases) && product.m_RotateYAxis && product.m_RotateXAxis && product.m_RotateZAxis && rotateAllowed) {
			cases = GetCaseHandlingCount(location.m_Width, location.m_Depth, location.m_Height,
				prodLength, prodHeight, prodWidth, bayType);
			if (cases > 0 && origCases == 0) {
				temp.Format("%s: The product will not fit unless all the dimensions are switched.\n", prodStr);
				msgList.Add(temp);
			}

			if (cases > maxCases)
				maxCases = cases;
		}
		
		if ((cases == 0 || bGetMaxCases) && product.m_RotateYAxis && product.m_RotateXAxis && product.m_RotateZAxis && rotateAllowed) {
			cases = GetCaseHandlingCount(location.m_Width, location.m_Depth, location.m_Height,
				prodHeight, prodWidth, prodLength, bayType);
			if (cases > 0 && origCases == 0) {
				temp.Format("%s: The product will not fit unless all the dimensions are switched.\n", prodStr);
				msgList.Add(temp);
			}

			if (cases > maxCases)
				maxCases = cases;
		}

		cases = maxCases;

		// Since flow racks are fitted using case dimensions, we don't have to convert them back to cases
		if ((cases == 0 || bGetMaxCases) && ! (bayType == BAYTYPE_CASEFLOW || bayType == BAYTYPE_PALLETFLOW) ) {
			switch (product.m_UnitOfIssue) {
			case 0:	// eaches
				cases = cases / product.m_CasePack;
				break;
			case 1:	// inners
				cases = cases / (product.m_CasePack / product.m_InnerPack);
				break;
			case 2:	// cases
			case 3:
				cases = cases;
				break;
			}
			if (cases == 0)
				cases = 1;
		}
		
		if (cases <= 0) {
			temp.Format("%s: The product will not fit in the location.\n\tProduct: %-4.1f x %-4.1f x %-4.1f\n"
				"\tLocation: %-4.1f x %-4.1f x %-4.1f.\n", prodStr,
				prodWidth, prodLength, prodHeight, location.m_Width, location.m_Depth, location.m_Height);
			msgList.Add(temp);
		}
	}

	return cases;
}

int CSolutionDataService::GetCaseHandlingCount(double lw, double ld, double lh,
												  double pw, double pl, double ph,
												  int levelType)
{

	int wCount, lCount, hCount, cases;
	
	wCount = (int)(lw / pw);
	lCount = (int)(ld / pl);
	hCount = (int)(lh / ph);
	
	if (levelType == BAYTYPE_CASEFLOW || levelType == BAYTYPE_PALLETFLOW) {
		if (hCount > 0)
			hCount = 1;
	}
	
	cases = wCount * lCount * hCount;

	return cases;
}

int CSolutionDataService::GetPalletHandlingCount(double lw, double ld, double lh,
													double pw, double pl, double ph)
{
	int wCount, lCount, hCount, cases;
	
	wCount = (int)(lw / pw);
	lCount = (int)(ld / pl);
	hCount = (int)(lh / ph);
	
	cases = wCount * lCount * hCount;
	
	return cases;
}
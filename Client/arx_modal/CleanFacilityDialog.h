#if !defined(AFX_CLEANFACILITYDIALOG_H__621E72F3_0BF2_492C_91B2_18AD64A7A18D__INCLUDED_)
#define AFX_CLEANFACILITYDIALOG_H__621E72F3_0BF2_492C_91B2_18AD64A7A18D__INCLUDED_

#if _MSC_VER > 1000
#pragma once
#endif // _MSC_VER > 1000
// CleanFacilityDialog.h : header file
//

/////////////////////////////////////////////////////////////////////////////
// CCleanFacilityDialog dialog

class CCleanFacilityDialog : public CDialog
{
// Construction
public:
	afx_msg LONG OnAcadKeepFocus(UINT, LONG);
	CCleanFacilityDialog(CWnd* pParent = NULL);   // standard constructor

// Dialog Data
	//{{AFX_DATA(CCleanFacilityDialog)
	enum { IDD = IDD_CLEAN_FACILITY };
	CListCtrl	m_DBListCtrl;
	CListCtrl	m_DrawingListCtrl;
	//}}AFX_DATA


// Overrides
	// ClassWizard generated virtual function overrides
	//{{AFX_VIRTUAL(CCleanFacilityDialog)
	protected:
	virtual void DoDataExchange(CDataExchange* pDX);    // DDX/DDV support
	virtual void PostNcDestroy();
	//}}AFX_VIRTUAL

// Implementation
protected:

	// Generated message map functions
	//{{AFX_MSG(CCleanFacilityDialog)
	virtual BOOL OnInitDialog();
	afx_msg void OnDeleteDrawingBay();
	afx_msg void OnDeleteDbBay();
	afx_msg void OnHelp();
	afx_msg BOOL OnHelpInfo(HELPINFO* pHelpInfo);
	afx_msg void OnLoad();
	afx_msg void OnColorDrawingBay();
	virtual void OnCancel();
	//}}AFX_MSG
	DECLARE_MESSAGE_MAP()

private:
	void AddStatements(CStringArray &statements, CString &handles);
};

//{{AFX_INSERT_LOCATION}}
// Microsoft Visual C++ will insert additional declarations immediately before the previous line.

#endif // !defined(AFX_CLEANFACILITYDIALOG_H__621E72F3_0BF2_492C_91B2_18AD64A7A18D__INCLUDED_)

// CheckMessageDialog.cpp : implementation file
//

#include "stdafx.h"
#include "modal.h"
#include "CheckMessageDialog.h"
#include "UtilityHelper.h"
#include "ControlService.h"

#ifdef _DEBUG
#define new DEBUG_NEW
#undef THIS_FILE
static char THIS_FILE[] = __FILE__;
#endif

extern CUtilityHelper utilityHelper;
extern CControlService controlService;
/////////////////////////////////////////////////////////////////////////////
// CCheckMessageDialog dialog


CCheckMessageDialog::CCheckMessageDialog(CWnd* pParent /*=NULL*/)
	: CDialog(CCheckMessageDialog::IDD, pParent)
{
	//{{AFX_DATA_INIT(CCheckMessageDialog)
	m_Skip = FALSE;
	m_Message = _T("");
	//}}AFX_DATA_INIT
	m_DefaultOption = IDOK;
	m_Options = MB_OK;
}


void CCheckMessageDialog::DoDataExchange(CDataExchange* pDX)
{
	CDialog::DoDataExchange(pDX);
	//{{AFX_DATA_MAP(CCheckMessageDialog)
	DDX_Check(pDX, IDC_SKIP_CHECKBOX, m_Skip);
	DDX_Text(pDX, IDC_MESSAGE, m_Message);
	//}}AFX_DATA_MAP
}


BEGIN_MESSAGE_MAP(CCheckMessageDialog, CDialog)
	//{{AFX_MSG_MAP(CCheckMessageDialog)
	//}}AFX_MSG_MAP
END_MESSAGE_MAP()

/////////////////////////////////////////////////////////////////////////////
// CCheckMessageDialog message handlers

BOOL CCheckMessageDialog::OnInitDialog() 
{
	CDialog::OnInitDialog();

	if (m_MessageCode == "") {
		GetDlgItem(IDC_SKIP_CHECKBOX)->ShowWindow(SW_HIDE);
	}
	else {
		CString skipped = controlService.GetApplicationData(m_MessageCode, "MessageSkipCodes");
		if (skipped == "1")
			EndDialog(m_DefaultOption);
	}
	
	if (m_Caption == "")
		m_Caption = "Optimize";

	this->SetWindowText(m_Caption);

	UpdateData(FALSE);

	return TRUE;  // return TRUE unless you set the focus to a control
	              // EXCEPTION: OCX Property Pages should return FALSE
}

void CCheckMessageDialog::OnOK() 
{
	UpdateData(TRUE);

	if (m_MessageCode != "" && m_Skip) {
		controlService.SetApplicationData(m_MessageCode, "1", "MessageSkipCodes");
	}

	CDialog::OnOK();
}

#if !defined(AFX_CHECKMESSAGEDIALOG_H__C94C88FC_0161_4004_9211_F4E8092B048E__INCLUDED_)
#define AFX_CHECKMESSAGEDIALOG_H__C94C88FC_0161_4004_9211_F4E8092B048E__INCLUDED_

#if _MSC_VER > 1000
#pragma once
#endif // _MSC_VER > 1000
// CheckMessageDialog.h : header file
//

/////////////////////////////////////////////////////////////////////////////
// CCheckMessageDialog dialog

class CCheckMessageDialog : public CDialog
{
// Construction
public:
	int m_Options;
	int m_DefaultOption;
	CString m_Caption;
	CString m_MessageCode;
	CCheckMessageDialog(CWnd* pParent = NULL);   // standard constructor

// Dialog Data
	//{{AFX_DATA(CCheckMessageDialog)
	enum { IDD = IDD_CHECK_MESSAGE };
	BOOL	m_Skip;
	CString	m_Message;
	//}}AFX_DATA


// Overrides
	// ClassWizard generated virtual function overrides
	//{{AFX_VIRTUAL(CCheckMessageDialog)
	protected:
	virtual void DoDataExchange(CDataExchange* pDX);    // DDX/DDV support
	//}}AFX_VIRTUAL

// Implementation
protected:

	// Generated message map functions
	//{{AFX_MSG(CCheckMessageDialog)
	virtual BOOL OnInitDialog();
	virtual void OnOK();
	//}}AFX_MSG
	DECLARE_MESSAGE_MAP()
};

//{{AFX_INSERT_LOCATION}}
// Microsoft Visual C++ will insert additional declarations immediately before the previous line.

#endif // !defined(AFX_CHECKMESSAGEDIALOG_H__C94C88FC_0161_4004_9211_F4E8092B048E__INCLUDED_)

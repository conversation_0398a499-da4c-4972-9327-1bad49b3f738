// ProductGroup.h: interface for the CProductGroup class.
//
//////////////////////////////////////////////////////////////////////

#if !defined(AFX_PRODUCTGROUP_H__3FE4D181_DCDF_11D4_9EC1_00C04FAC149C__INCLUDED_)
#define AFX_PRODUCTGROUP_H__3FE4D181_DCDF_11D4_9EC1_00C04FAC149C__INCLUDED_

#if _MSC_VER > 1000
#pragma once
#endif // _MSC_VER > 1000

#include "ProductGroupQuery.h"
#include "ProductGroupConstraint.h"

class CProductGroup : public CObject  
{
public:
	BOOL IsEqual(CProductGroup &other);
	int Parse(CString &line);
	long m_ProductGroupDBID;
	CString m_Description;
	int m_Priority;
	float m_PercentOpenLocs;
	BOOL m_IsProdGroupLocked;
	BO<PERSON> m_IsAssignmentLocked;
	BOOL m_Exclusive;
	CString m_OptimizeAttribute;
	int m_OptimizeMethod;
	CTypedPtrArray<CObArray, CProductGroupQuery*> m_QueryList;
	CTypedPtrArray<CObArray, CProductGroupConstraint*> m_ConstraintList;
	int m_ProductCount;

	CProductGroup();
	virtual ~CProductGroup();
	CProductGroup& operator=(const CProductGroup &other);

};

#endif // !defined(AFX_PRODUCTGROUP_H__3FE4D181_DCDF_11D4_9EC1_00C04FAC149C__INCLUDED_)

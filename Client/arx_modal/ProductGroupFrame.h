#if !defined(AFX_PRODUCTGROUPFRAME_H__C6242624_17CC_11D5_9ECA_00C04FAC149C__INCLUDED_)
#define AFX_PRODUCTGROUPFRAME_H__C6242624_17CC_11D5_9ECA_00C04FAC149C__INCLUDED_

#if _MSC_VER > 1000
#pragma once
#endif // _MSC_VER > 1000
// ProductGroupFrame.h : header file
//

/////////////////////////////////////////////////////////////////////////////
// CProductGroupFrame frame

class CProductGroupFrame : public CFrameWnd
{
	DECLARE_DYNCREATE(CProductGroupFrame)
protected:
          // protected constructor used by dynamic creation

// Attributes
public:
	CProductGroupFrame();
	virtual ~CProductGroupFrame();
// Operations
public:
	CSplitterWnd m_Splitter;

// Overrides
	// ClassWizard generated virtual function overrides
	//{{AFX_VIRTUAL(CProductGroupFrame)
	protected:
	virtual BOOL OnCreateClient(LPCREATESTRUCT lpcs, CCreateContext* pContext);
	//}}AFX_VIRTUAL

// Implementation
protected:


	// Generated message map functions
	//{{AFX_MSG(CProductGroupFrame)
		// NOTE - the ClassWizard will add and remove member functions here.
	//}}AFX_MSG
	DECLARE_MESSAGE_MAP()
};

/////////////////////////////////////////////////////////////////////////////

//{{AFX_INSERT_LOCATION}}
// Microsoft Visual C++ will insert additional declarations immediately before the previous line.

#endif // !defined(AFX_PRODUCTGROUPFRAME_H__C6242624_17CC_11D5_9ECA_00C04FAC149C__INCLUDED_)

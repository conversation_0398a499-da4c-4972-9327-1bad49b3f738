#ifndef CONSTANTS_DEFINED

#include <ADesk.h>

static const int GROUP_LAYOUT = 0;
static const int TACTICAL_LAYOUT = 1;
static const int STRATEGIC_LAYOUT = 2;
static const int NEW_PRODUCT_LAYOUT = 3;
static const int MANUAL_LAYOUT = 4;

static const int BAYTYPE_BIN = 1;
static const int BAYTYPE_DRIVEIN = 2;
static const int BAYTYPE_FLOOR = 3;
static const int BAYTYPE_CASEFLOW = 4;
static const int BAYTYPE_PALLET = 5;
static const int BAYTYPE_PIR = 6;
static const int BAYTYPE_CAROUSEL = 7;
static const int BAYTYPE_PALLETFLOW = 8;

static const int DT_NONE = 0;
static const int DT_INT = 1;
static const int DT_STRING = 2;
static const int DT_FLOAT = 3;
static const int DT_LIST = 4;
static const int DT_FORMULA = 5;

static const int SLOT_NIL_INTEGER = -32767;
static const int SLOT_NIL_FLOAT = -32767;

static const int UOI_EACH = 0;
static const int UOI_INNER = 1;
static const int UOI_CASE = 2;
static const int UOI_PALLET = 3;

static const int CASE_HANDLING = 1;
static const int PALLET_HANDLING = 3;

static const CString TB_PRODUCT = "DBProductPack";
static const CString TB_PRODUCTCONTAINER = "DBProdContainer";
static const CString TB_PRODUCTUDFLIST = "DBProdPKUDFList";
static const CString TB_PRODUCTUDFVAL = "DBProdPKUDFVal";
static const CString TB_PRODUCTGROUP = "DBSlottingGroup";
static const CString TB_PRODUCTGROUPLOC = "DBSlottingGroupLoc";
static const CString TB_LOCATION = "DBLocation";
static const CString TB_SOLUTION = "DBSlotSolution";
static const CString TB_BAYPROFILE = "DBBayProfile";
static const CString TB_LEVELPROFILE = "DBLevelProfile";
static const CString TB_LOCATIONPROFILE = "DBLocationProf";
static const CString TB_LEVEL = "DBLevel";

static const CString TB_PRODUCTGROUPLOC_OPT = "DBSlottingGroupLocOpt";
static const CString TB_LOCATION_OPT = "DBLocationOpt";
static const CString TB_SOLUTION_OPT = "DBSlotSolutionOpt";
static const CString TB_BAYPROFILE_OPT = "DBBayProfileOpt";
static const CString TB_LEVELPROFILE_OPT = "DBLevelProfileOpt";
static const CString TB_LOCATIONPROFILE_OPT = "DBLocationProfOpt";

static const int CQ_COMPOUND_START = 1;
static const int CQ_COMPOUND_END = 2;

static const int TIME_HORIZON_DAY = 1;
static const int TIME_HORIZON_WEEK = 2;
static const int TIME_HORIZON_MONTH = 3;
static const int TIME_HORIZON_YEAR = 4;

static const int MOVE_NORMAL = 0;
static const int MOVE_TO_TEMP = 1;
static const int MOVE_FROM_TEMP = 2;
static const int MOVE_ADD_LOC = 3;
static const int MOVE_DEL_LOC = 4;

static const int UDF_PRODUCT = 0;
static const int UDF_PRODUCT_GROUP = 1;
static const int UDF_FACILITY = 2;
static const int UDF_SECTION = 3;
static const int UDF_AISLE = 4;
static const int UDF_SIDE = 5;
static const int UDF_BAY = 6;
static const int UDF_LEVEL = 7;
static const int UDF_LOCATION = 8;
static const int UDF_LEVEL_PROFILE = 9;

static const Adesk::UInt16 kColorByBlock   = 0;
static const Adesk::UInt16 kRed            = 1;
static const Adesk::UInt16 kYellow         = 2;
static const Adesk::UInt16 kGreen          = 3;
static const Adesk::UInt16 kCyan           = 4;
static const Adesk::UInt16 kBlue           = 5;
static const Adesk::UInt16 kMagenta        = 6;
static const Adesk::UInt16 kWhite          = 7;
static const Adesk::UInt16 kColorByLayer   = 256;
static const Adesk::UInt16 kOrange		   = 30;

static const int LOC_STATUS_NOT_INTEGRATED = 0;
static const int LOC_STATUS_INTEGRATION_PENDING = 1;
static const int LOC_STATUS_INTEGRATED = 2;

static const CString BAD_FILE_CHARACTERS = "<>:\"/\\|";

static const double PI = 3.1415926535;

#define CONSTANTS_DEFINED
#endif



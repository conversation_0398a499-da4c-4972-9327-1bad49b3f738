 // ChangeRackType.cpp : implementation file
//

#include "stdafx.h"
#include "modal.h"
#include "ChangeRackType.h"
#include "HelpService.h"
#include "FacilityDataService.h"
#include "BayProfileDataService.h"
#include "UtilityHelper.h"
#include "Constants.h"

#ifdef _DEBUG
#define new DEBUG_NEW
#undef THIS_FILE
static char THIS_FILE[] = __FILE__;
#endif

extern char slotDir[256];

extern CHelpService helpService;
extern CUtilityHelper utilityHelper;

/////////////////////////////////////////////////////////////////////////////
// CChangeRackType dialog

CChangeRackType::CChangeRackType(CWnd* pParent /*=NULL*/)
	: CDialog(CChangeRackType::IDD, pParent)
{
	//{{AFX_DATA_INIT(CChangeRackType)
	//}}AFX_DATA_INIT
	m_bCurrentFacilityOnly = FALSE;
}

CChangeRackType::~CChangeRackType()
{
	for (int i=0; i < m_BayProfileList.GetSize(); ++i)
		delete m_BayProfileList[i];

}

void CChangeRackType::DoDataExchange(CDataExchange* pDX)
{
	CDialog::DoDataExchange(pDX);
	//{{AFX_DATA_MAP(CChangeRackType)
	DDX_Control(pDX, IDC_RACKTREE, m_ProfileTreeCtrl);
	//}}AFX_DATA_MAP
}


BEGIN_MESSAGE_MAP(CChangeRackType, CDialog)
	//{{AFX_MSG_MAP(CChangeRackType)
	ON_BN_CLICKED(IDHELP, OnHelp)
	ON_WM_HELPINFO()
	//}}AFX_MSG_MAP
END_MESSAGE_MAP()

/////////////////////////////////////////////////////////////////////////////
// CChangeRackType message handlers

void CChangeRackType::OnOK() 
{
	//    Check to see if an item is selected in tree control
	HTREEITEM hItem = m_ProfileTreeCtrl.GetSelectedItem( );	
	if (hItem == NULL) {
		AfxMessageBox("Please select a bay profile from the list.");
		return;
	}

	if (m_ProfileTreeCtrl.GetParentItem(hItem) == NULL) {
		AfxMessageBox("Please select a specific bay profile from the list.");
		return;
	}
	
	CBayProfile *pBayProfile = (CBayProfile *)m_ProfileTreeCtrl.GetItemData(hItem);
	m_SelectedBayProfileId = pBayProfile->m_BayProfileDBId;

	CDialog::OnOK();

}


BOOL CChangeRackType::OnInitDialog() 
{

	CDialog::OnInitDialog();
	
	if (m_bCurrentFacilityOnly)
		this->SetWindowText("Color Profile");

	CStringArray bayProfileNameList;
	
	if (m_Title != "")
		this->SetWindowText(m_Title);

	try
	{
		CFacilityDataService facilityDataService;
		CBayProfileDataService bayProfileDataService;

		if (m_bCurrentFacilityOnly)
			facilityDataService.GetBayProfileNameListByFacility(bayProfileNameList);	
		else
			bayProfileDataService.GetBayProfileNameList(bayProfileNameList);

	}
	catch (...)
	{
		AfxMessageBox("Error Getting Bay Profile Name List from Database.");
		//return;
	}

	CStringArray strings;
	for (int i=0; i < bayProfileNameList.GetSize(); ++i) {
		CBayProfile *pBayProfile = new CBayProfile;
		strings.RemoveAll();
		utilityHelper.ParseString(bayProfileNameList[i], "|", strings);
		pBayProfile->m_BayProfileDBId = atoi(strings[0]);
		pBayProfile->m_Description = strings[1];
		pBayProfile->m_BayType = atoi(strings[2]);
		m_BayProfileList.Add(pBayProfile);
	}

	BuildBayProfileTree();

	return TRUE;  // return TRUE unless you set the focus to a control
	              // EXCEPTION: OCX Property Pages should return FALSE
}

void CChangeRackType::OnHelp() 
{
	if (m_bCurrentFacilityOnly)
		helpService.ShowScreenHelp("ColorBayProfile_Main");
	else
		helpService.ShowScreenHelp("ChangeBayProfile_Main");

	return;
}

BOOL CChangeRackType::OnHelpInfo(HELPINFO* pHelpInfo) 
{
	if (m_bCurrentFacilityOnly) {
		switch (pHelpInfo->iCtrlId) {
		case IDC_RACKTREE:
			helpService.ShowFieldHelp("ColorBayProfile_SelectProfile");
			break;
		case IDOK:
			helpService.ShowFieldHelp("Coloring_OK");
			break;
		default:
			helpService.ShowFieldHelp(IDD, pHelpInfo->iCtrlId);
			break;
		}
	}

	else {
		switch (pHelpInfo->iCtrlId) {
		case IDC_RACKTREE:
			helpService.ShowFieldHelp("ChangeBayProfile_SelectProfile");
			break;
		case IDOK:
			helpService.ShowFieldHelp("ChangeBayProfile_OK");
			break;
		default:
			helpService.ShowFieldHelp(IDD, pHelpInfo->iCtrlId);
			break;
		}
	}

	return FALSE;

}

int CChangeRackType::LoadBayTypeList()
{
	m_ImageList.Create(16, 16, TRUE, 4, 1);
	m_ImageList.Add(AfxGetApp()->LoadIcon(IDI_FLDCLS));
	m_ImageList.Add(AfxGetApp()->LoadIcon(IDI_FLDOPEN));
	m_ImageList.Add(AfxGetApp()->LoadIcon(IDI_LOCATIONICON));

	m_ProfileTreeCtrl.SetImageList(&m_ImageList, TVSIL_NORMAL);

	HTREEITEM hItem;

	hItem = m_ProfileTreeCtrl.InsertItem("Bin", 1, 1, TVI_ROOT, TVI_LAST);
	m_ProfileTreeCtrl.SetItemData(hItem, BAYTYPE_BIN);
	m_MapBayTypeToTree.SetAt(BAYTYPE_BIN, hItem);

	hItem = m_ProfileTreeCtrl.InsertItem("Case Flow", 1, 1, TVI_ROOT, TVI_LAST);
	m_ProfileTreeCtrl.SetItemData(hItem, BAYTYPE_CASEFLOW);
	m_MapBayTypeToTree.SetAt(BAYTYPE_CASEFLOW, hItem);

	hItem = m_ProfileTreeCtrl.InsertItem("Drive In", 1, 1, TVI_ROOT, TVI_LAST);
	m_ProfileTreeCtrl.SetItemData(hItem, BAYTYPE_DRIVEIN);
	m_MapBayTypeToTree.SetAt(BAYTYPE_DRIVEIN, hItem);

	hItem = m_ProfileTreeCtrl.InsertItem("Floor", 1, 1, TVI_ROOT, TVI_LAST);
	m_ProfileTreeCtrl.SetItemData(hItem, BAYTYPE_FLOOR);
	m_MapBayTypeToTree.SetAt(BAYTYPE_FLOOR, hItem);

	hItem = m_ProfileTreeCtrl.InsertItem("Pallet", 1, 1, TVI_ROOT, TVI_LAST);
	m_ProfileTreeCtrl.SetItemData(hItem, BAYTYPE_PALLET);
	m_MapBayTypeToTree.SetAt(BAYTYPE_PALLET, hItem);

	hItem = m_ProfileTreeCtrl.InsertItem("Pallet Flow", 1, 1, TVI_ROOT, TVI_LAST);
	m_ProfileTreeCtrl.SetItemData(hItem, BAYTYPE_PALLETFLOW);
	m_MapBayTypeToTree.SetAt(BAYTYPE_PALLETFLOW, hItem);

	return 0;

}

void CChangeRackType::BuildBayProfileTree()
{
	m_ProfileTreeCtrl.DeleteAllItems();

	LoadBayTypeList();

	HTREEITEM hTypeItem;
	for (int i=0; i < m_BayProfileList.GetSize(); ++i) {
		CBayProfile *pBayProfile = m_BayProfileList[i];

		if (m_MapBayTypeToTree.Lookup(pBayProfile->m_BayType, hTypeItem)) {
			CString temp = pBayProfile->m_Description;
			if (pBayProfile->m_Active)
				temp += " (Active)";

			HTREEITEM hItem = m_ProfileTreeCtrl.InsertItem(temp, 2, 2, hTypeItem, TVI_LAST);
			m_ProfileTreeCtrl.SetItemData(hItem, (unsigned long)pBayProfile);
		}
	}

	HTREEITEM hItem = m_ProfileTreeCtrl.GetNextItem(TVI_ROOT, TVGN_CHILD);
	HTREEITEM hTopItem = hItem;
	while (hItem != NULL) {
		m_ProfileTreeCtrl.Expand(hItem, TVE_EXPAND);
		HTREEITEM hNextItem = m_ProfileTreeCtrl.GetNextItem(hItem, TVGN_NEXT);
		hItem = hNextItem;
	}


	m_ProfileTreeCtrl.EnsureVisible(hTopItem);
}
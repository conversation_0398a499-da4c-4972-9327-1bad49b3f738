// ProductGroupCommands.h: interface for the CProductGroupCommands class.
//
//////////////////////////////////////////////////////////////////////

#if !defined(AFX_PRODUCTGROUPCOMMANDS_H__11F332FD_9BF6_4FE1_B8B1_BD6DBA6D1F81__INCLUDED_)
#define AFX_PRODUCTGROUPCOMMANDS_H__11F332FD_9BF6_4FE1_B8B1_BD6DBA6D1F81__INCLUDED_

#include "Commands.h"

#if _MSC_VER > 1000
#pragma once
#endif // _MSC_VER > 1000

class CProductGroupCommands : public CCommands
{
public:
	CProductGroupCommands();
	virtual ~CProductGroupCommands();

	static void RegisterCommands();
	static void ProductGroupMaintenance();
};

#endif // !defined(AFX_PRODUCTGROUPCOMMANDS_H__11F332FD_9BF6_4FE1_B8B1_BD6DBA6D1F81__INCLUDED_)

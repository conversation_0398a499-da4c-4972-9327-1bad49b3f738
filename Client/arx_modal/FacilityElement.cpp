// FacilityElement.cpp: implementation of the CFacilityElement class.
//
//////////////////////////////////////////////////////////////////////

#include "stdafx.h"
#include "modal.h"
#include "FacilityElement.h"

#ifdef _DEBUG
#undef THIS_FILE
static char THIS_FILE[]=__FILE__;
#define new DEBUG_NEW
#endif

//////////////////////////////////////////////////////////////////////
// Construction/Destruction
//////////////////////////////////////////////////////////////////////

CFacilityElement::CFacilityElement()
{
	m_DBId = 0;
}

CFacilityElement::~CFacilityElement()
{

}

//int CFacilityElement::Parse(CString &line)
//{
//
//}

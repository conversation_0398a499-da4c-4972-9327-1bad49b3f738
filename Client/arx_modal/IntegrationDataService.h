// IntegrationDataService.h: interface for the CIntegrationDataService class.
//
//////////////////////////////////////////////////////////////////////

#if !defined(AFX_INTEGRATIONDATASERVICE_H__A4BA565A_768D_462B_A429_961243BA978F__INCLUDED_)
#define AFX_INTEGRATIONDATASERVICE_H__A4BA565A_768D_462B_A429_961243BA978F__INCLUDED_
#include "Location.h"

#if _MSC_VER > 1000
#pragma once
#endif // _MSC_VER > 1000


#include "WMSGroup.h"
#include "ExternalConnection.h"
#include <afxmt.h>
#include "ThreadParameters.h"

class CIntegrationDataService  
{
public:
	BOOL IsConnectionNameUsed(const CString &name, int dbid);

	int UpdateMoveStatus(int facilityId, const CString &chainList, int status);
	int ApplyMovesToBaseline(int facilityId, const CString &chainList);
	int CopyOptimizedSolutionsToBaseline(int facilityId);
	int GetSearchAnchorList(int facilityDBId, CStringArray &results);
	int DeleteExternalConnection(int dbid);
	int StoreExternalConnection(CExternalConnection &connection);

	int UpdateStatusForLocationOutbound(CWMSMap *pMap, BOOL autoConfirm, BOOL fullExport);

	int GetExternalInfo(int externalSystemDBId, CWMSMap *pMap,
		CMapStringToString &defaultInfoMap, CMapStringToString &levelProfileInfoMap,
		CMapStringToString &locationInfoMap);

	int GetAllExternalInfo(int externalSystemDBId, int facilityDBId, int sectionDBId,
											 CMapStringToString &defaultInfoMap,
											 CMapStringToString &levelProfileInfoMap, 
											 CMapStringToString &locationInfoMap);

	int UpdateBatchForLocationOutbound(CWMSMap *pMap);

	int GetLocationOutboundData(CWMS *pWMS, CWMSMap *pMap, CStringArray &locList);
	int GetAllLocationOutboundData(int facilityDBId, int sectionDBId, CStringArray &locList);

	int GetAllAssignmentOutboundData(int facilityDBId, int batchId, CStringArray &assgList, BOOL integratedOnly);
	int GetAssignmentDataByChain(int facilityDBId, const CString &chainList, CStringArray &assgList);
	int UpdateAssignmentOutboundStatus(const CWMSMap *pMap, const CString &chainList, BOOL autoConfirm, BOOL fullExport);

	int PurifyLocationOutboundQueue(int facilityId, int sectionId);
	
	int ProcessLocationInbound(CStringArray &locList, int &errorCount, int addCount);
	static UINT ProcessLocationInboundThread(LPVOID pParam);

	int ProcessLocationConfirmation(CStringArray &locConfList);
	static UINT ProcessLocationConfirmationThread(LPVOID pParam);

	int ProcessProductInbound(CStringArray &prodList, int &errorCount, int addCount);
	static UINT ProcessProductInboundThread(LPVOID pParam);
	
	int ProcessAssignmentInbound(CStringArray &assgList, int &errorCount, int addCount);
	static UINT ProcessAssignmentInboundThread(LPVOID pParam);

	int ProcessAssignmentConfirmation(CStringArray &assgConfList);
	static UINT ProcessAssignmentConfirmationThread(LPVOID pParam);

	int GetExternalConnectionList(CStringArray &connectionList);
	int LoadGroupConnectionList(CWMSGroup &group);

	int GetIntegratedGroupList(CStringArray &groupList);
	int CheckLocationIntegrationStatus(int facilityDBId, int sectionDBId);
	int CheckAssignmentIntegrationStatus(int facilityDBId, int sectionDBId);
	int GetWMSMapSummaryList(CStringArray &results);
	int GetWMSGroupList(CStringArray &groupList);

	int GetWMSImportMap(int wmsDBId, CStringArray& mapList);
	int GetWMSExportMap(int wmsDBId, CStringArray& mapList);

	int GetWMSList(int groupDBId, CStringArray &wmsList);



	int GetSectionWMSImportAssignment(int sectionDBId, CWMS& wms);
	int GetSectionWMSExportAssignment(int sectionDBId, CWMS& wms);
	
	int DeleteWMSGroup(int groupDBId);
	int DeleteWMS(int wmsdbid);
	int DeleteWMSImportMap(int mapDBId);
	int DeleteWMSExportMap(int mapDBId);
	
	int GetExternalSystemList(const CString &type, CStringArray &results);
	
	int StoreWMS(CWMS &wms);
	int StoreWMSGroup(CWMSGroup &group);
	int StoreWMSImportMap(CWMSMap &map);
	int StoreWMSExportMap(CWMSMap &map);

	BOOL WMSExists(CString &name, int dbid);
	BOOL WMSIdExists(CString &wmsId, int groupDBId, int wmsDBId);
	BOOL WMSGroupExists(CString &name, int dbid);
	BOOL WMSGroupIdExists(CString &wmsId, int groupDBId);

	CIntegrationDataService();
	virtual ~CIntegrationDataService();

};

#endif // !defined(AFX_INTEGRATIONDATASERVICE_H__A4BA565A_768D_462B_A429_961243BA978F__INCLUDED_)

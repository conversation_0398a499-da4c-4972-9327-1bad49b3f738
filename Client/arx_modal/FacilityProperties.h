#if !defined(AFX_FACILITYPROPERTIES_H__18B640C4_AB54_11D4_9EBD_00C04FAC149C__INCLUDED_)
#define AFX_FACILITYPROPERTIES_H__18B640C4_AB54_11D4_9EBD_00C04FAC149C__INCLUDED_

#if _MSC_VER > 1000
#pragma once
#endif // _MSC_VER > 1000
// FacilityProperties.h : header file
//

/////////////////////////////////////////////////////////////////////////////
// CFacilityProperties dialog

class CFacilityProperties : public CPropertyPage
{
	DECLARE_DYNCREATE(CFacilityProperties)

// Construction
public:
	CFacilityProperties();
	~CFacilityProperties();

// Dialog Data
	//{{AFX_DATA(CFacilityProperties)
	enum { IDD = IDD_FACILITY_PROPERTIES };
	CString	m_Description;
	CString	m_Notes;
	int		m_TimeHorizonUnits;
	int		m_TimeHorizonValue;
	int		m_UOM;
	//}}AFX_DATA
	CString m_MasterPassword;

// Overrides
	// ClassWizard generate virtual function overrides
	//{{AFX_VIRTUAL(CFacilityProperties)
	public:
	virtual BOOL OnKillActive();
	virtual void OnCancel();
	virtual void OnOK();
	protected:
	virtual void DoDataExchange(CDataExchange* pDX);    // DDX/DDV support
	virtual BOOL OnNotify(WPARAM wParam, LPARAM lParam, LRESULT* pResult);
	//}}AFX_VIRTUAL

// Implementation
protected:
	// Generated message map functions
	//{{AFX_MSG(CFacilityProperties)
	virtual BOOL OnInitDialog();
	afx_msg BOOL OnHelpInfo(HELPINFO* pHelpInfo);
	//}}AFX_MSG
	DECLARE_MESSAGE_MAP()

private:
};

//{{AFX_INSERT_LOCATION}}
// Microsoft Visual C++ will insert additional declarations immediately before the previous line.

#endif // !defined(AFX_FACILITYPROPERTIES_H__18B640C4_AB54_11D4_9EBD_00C04FAC149C__INCLUDED_)

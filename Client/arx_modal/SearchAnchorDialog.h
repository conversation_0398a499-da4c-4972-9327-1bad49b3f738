//{{AFX_INCLUDES()
#include "EditGrid.h"
//}}AFX_INCLUDES
#if !defined(AFX_SEARCHANCHORDIALOG_H__B94031A3_B9B0_11D4_9EBD_00C04FAC149C__INCLUDED_)
#define AFX_SEARCHANCHORDIALOG_H__B94031A3_B9B0_11D4_9EBD_00C04FAC149C__INCLUDED_

#if _MSC_VER > 1000
#pragma once
#endif // _MSC_VER > 1000
// SearchAnchorDialog.h : header file
//
#include "Resource.h"

/////////////////////////////////////////////////////////////////////////////
// CSearchAnchorDialog dialog

class CSearchAnchorDialog : public CDialog
{
// Construction
public:
	CString m_ThreadMessage;
	long m_ProductGroupID;
	~CSearchAnchorDialog();
	CSearchAnchorDialog(CWnd* pParent = NULL);   // standard constructor
	static UINT GenerateSearchAnchorPointsThread(LPVOID pParam);

// Dialog Data
	//{{AFX_DATA(CSearchAnchorDialog)
	enum { IDD = IDD_SEARCH_ANCHOR };
	CButton	m_SortButton;
	CButton	m_ExcelButton;
	CEditGrid	m_Grid;
	//}}AFX_DATA


// Overrides
	// ClassWizard generated virtual function overrides
	//{{AFX_VIRTUAL(CSearchAnchorDialog)
	public:
	virtual BOOL PreTranslateMessage(MSG* pMsg);
	protected:
	virtual void DoDataExchange(CDataExchange* pDX);    // DDX/DDV support
	//}}AFX_VIRTUAL

// Implementation
protected:

	// Generated message map functions
	//{{AFX_MSG(CSearchAnchorDialog)
	virtual BOOL OnInitDialog();
	virtual void OnOK();
	afx_msg void OnExcel();
	afx_msg void OnSort();
	afx_msg BOOL OnHelpInfo(HELPINFO* pHelpInfo);
	afx_msg void OnHelp();
	afx_msg void OnGenerate();
	//}}AFX_MSG
	DECLARE_MESSAGE_MAP()
private:
	int ValidateRange();
	CObArray m_SearchAnchorPoints;
	void LoadSearchAnchorPoints();
	void LoadGrid();
	CToolTipCtrl m_ToolTip;
	void LoadToolTips();
};

//{{AFX_INSERT_LOCATION}}
// Microsoft Visual C++ will insert additional declarations immediately before the previous line.

#endif // !defined(AFX_SEARCHANCHORDIALOG_H__B94031A3_B9B0_11D4_9EBD_00C04FAC149C__INCLUDED_)

// ProductInfo.h: interface for the CProductInfo class.
//
//////////////////////////////////////////////////////////////////////

#if !defined(AFX_PRODUCTINFO_H__299660A9_790D_11D4_91CC_00400542E36B__INCLUDED_)
#define AFX_PRODUCTINFO_H__299660A9_790D_11D4_91CC_00400542E36B__INCLUDED_

#if _MSC_VER > 1000
#pragma once
#endif // _MSC_VER > 1000


class CProductInfo  
{
public:
	CProductInfo();
	virtual ~CProductInfo();
	CProductInfo& operator=(const CProductInfo & other);
	void Parse(CString line);

	int m_ProductDBID;
	CString m_ProductDescription;
	int m_CasePack;
	CString m_WMSProductID;
	CString m_WMSProductDetailID;
	int m_ProdProductGroupDBID;
	CString m_ProdPGDescription;
	int m_CurrentLocationDBID;
	CString m_LocationDescription;
	CString m_BayProfileDescription;
	int m_LevelType;
	int m_LocProductGroupDBID;
	CString m_LocPGDescription;
	int m_CaseQuantity;
	double m_Width;		// UOI width
	double m_Length;	// UOI length
	double m_Height;	// UOI height
	double m_CaseWidth;
	double m_CaseLength;
	double m_CaseHeight;
	double m_InnerWidth;
	double m_InnerLength;
	double m_InnerHeight;
	double m_EachWidth;
	double m_EachLength;
	double m_EachHeight;
	int m_UnitOfIssue;
	int m_NumberInPallet;
	int m_Ti;
	int m_Hi;
	double m_ContainerWidth;
	double m_ContainerLength;
	double m_ContainerHeight;
	int m_ContainerHeightOverride;
	int m_InnerPack;
	double m_RotatedWidth;
	double m_RotatedLength;
	double m_RotatedHeight;
	int m_RotateXAxis;
	int m_RotateYAxis;
	int m_RotateZAxis;
	double m_Weight;

	long m_SolutionDBID;	

};

#endif // !defined(AFX_PRODUCTINFO_H__299660A9_790D_11D4_91CC_00400542E36B__INCLUDED_)

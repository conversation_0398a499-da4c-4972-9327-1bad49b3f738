// Level.h: interface for the CLevel class.
//
//////////////////////////////////////////////////////////////////////

#if !defined(AFX_LEVEL_H__6A9D1E2D_067B_44A8_9A30_7C6BBA43B2F2__INCLUDED_)
#define AFX_LEVEL_H__6A9D1E2D_067B_44A8_9A30_7C6BBA43B2F2__INCLUDED_

#if _MSC_VER > 1000
#pragma once
#endif // _MSC_VER > 1000

#include "FacilityElement.h"
#include "Location.h"
#include "3DPoint.h"

class CLevel : public CFacilityElement  
{
public:
	CLevel();
	virtual ~CLevel();

	C3DPoint m_Coordinates;
	BOOL m_IsVariableWidthAllowed;		// IsVarLocAllowed
	BOOL m_IsRotationAllowed;			// IsRotateAllowed
	double m_ForkInsertionTime;			// ForkFixedInsertion
	double m_ForkExtractionTime;		// Not in database
	double m_MinimumLocationWidth;		// MinimumLocWidth
	double m_ProductGap;
	double m_ProductSnap;
	double m_FacingGap;
	double m_FacingSnap;

	int m_LevelProfileDBID;
	BOOL m_IsProfileOverridden;			// IsOverridden

	CTypedPtrArray<CObArray, CLocation*> m_LocationArray;
	CMap<long, long, CLocation*, CLocation*> m_LocationMapById;
	CMap<CString, LPCSTR, CLocation*, CLocation*> m_LocationMapByName;

};

#endif // !defined(AFX_LEVEL_H__6A9D1E2D_067B_44A8_9A30_7C6BBA43B2F2__INCLUDED_)

// 3DPoint.h: interface for the C3DPoint class.
//
//////////////////////////////////////////////////////////////////////

#if !defined(AFX_3DPOINT_H__C02C67CA_23A3_4EE6_BA08_DE4696E44EED__INCLUDED_)
#define AFX_3DPOINT_H__C02C67CA_23A3_4EE6_BA08_DE4696E44EED__INCLUDED_

#if _MSC_VER > 1000
#pragma once
#endif // _MSC_VER > 1000

class C3DPoint : public CObject  
{
public:
	C3DPoint RotatedPoint(double angle);
	void Rotate(double angle);
	C3DPoint(const C3DPoint& other) { m_X = other.m_X; m_Y = other.m_Y; m_Z = other.m_Z; };
	C3DPoint();
	C3DPoint(double x, double y, double z) { m_X = x; m_Y = y; m_Z = z; };
	virtual ~C3DPoint();
	void operator=(const C3DPoint& other) { m_X = other.m_X; m_Y = other.m_Y; m_Z = other.m_Z;};	// Fix by Manohar, inserted 'void'
	BOOL operator==(const C3DPoint& other);
	BOOL operator!=(const C3DPoint& other) { return !(*this == other); };
	C3DPoint& operator+=(const C3DPoint& other);

	double m_X;
	double m_Y;
	double m_Z;
};

#endif // !defined(AFX_3DPOINT_H__C02C67CA_23A3_4EE6_BA08_DE4696E44EED__INCLUDED_)

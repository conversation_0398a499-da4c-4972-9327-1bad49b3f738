#if !defined(AFX_PRODUCTGROUPCRITERIALISTPAGE_H__5E791C69_041F_11D5_9EC8_00C04FAC149C__INCLUDED_)
#define AFX_PRODUCTGROUPCRITERIALISTPAGE_H__5E791C69_041F_11D5_9EC8_00C04FAC149C__INCLUDED_

#include "ProductGroupCriteria.h"	// Added by ClassView
#include "ProductAttribute.h"
#include "ProductGroupDataService.h"

#if _MSC_VER > 1000
#pragma once
#endif // _MSC_VER > 1000
// ProductGroupCriteriaListPage.h : header file
//

#include "Resource.h"

/////////////////////////////////////////////////////////////////////////////
// CProductGroupCriteriaListPage dialog

class CProductGroupCriteriaListPage : public CPropertyPage
{
	DECLARE_DYNCREATE(CProductGroupCriteriaListPage)

// Construction
public:
	CProductGroupCriteria *m_Criteria;
	CProductGroupCriteriaListPage();
	~CProductGroupCriteriaListPage();
	

// Dialog Data
	//{{AFX_DATA(CProductGroupCriteriaListPage)
	enum { IDD = IDD_CRITERIA_LIST };
	CButton	m_LoadPossibleCtrl;
	CListCtrl	m_ValueListCtrl;
	CStatic	m_FromStaticCtrl;
	CStatic	m_ToStaticCtrl;
	CEdit	m_ToValueCtrl;
	CEdit	m_FromValueCtrl;
	CComboBox	m_OperatorCtrl;
	CListCtrl	m_RangeListCtrl;
	CString	m_FromValue;
	CString	m_ToValue;
	//}}AFX_DATA
	CProductGroupDataService *m_ProductGroupDataService;

// Overrides
	// ClassWizard generate virtual function overrides
	//{{AFX_VIRTUAL(CProductGroupCriteriaListPage)
	public:
	virtual void OnOK();
	virtual BOOL OnKillActive();
	virtual BOOL OnSetActive();
	protected:
	virtual void DoDataExchange(CDataExchange* pDX);    // DDX/DDV support
	virtual BOOL OnNotify(WPARAM wParam, LPARAM lParam, LRESULT* pResult);
	//}}AFX_VIRTUAL

// Implementation
protected:
	// Generated message map functions
	//{{AFX_MSG(CProductGroupCriteriaListPage)
	virtual BOOL OnInitDialog();
	afx_msg void OnSelchangeOperator();
	afx_msg void OnAddRange();
	afx_msg void OnAddValue();
	afx_msg void OnDeleteRange();
	afx_msg void OnDeleteValue();
	afx_msg void OnLoadValues();
	afx_msg void OnItemchangedRangeList(NMHDR* pNMHDR, LRESULT* pResult);
	afx_msg void OnEndlabeleditRangeList(NMHDR* pNMHDR, LRESULT* pResult);
	afx_msg void OnEndlabeleditValueList(NMHDR* pNMHDR, LRESULT* pResult);
	afx_msg void OnChangeFromValue();
	afx_msg void OnChangeToValue();
	afx_msg void OnItemchangedValueList(NMHDR* pNMHDR, LRESULT* pResult);
	afx_msg void OnContextMenu(CWnd* pWnd, CPoint point);
	afx_msg void OnLoadPossibleValues();
	afx_msg void OnDblclkValueList(NMHDR* pNMHDR, LRESULT* pResult);
	afx_msg BOOL OnHelpInfo(HELPINFO* pHelpInfo);
	//}}AFX_MSG
	DECLARE_MESSAGE_MAP()

private:
	BOOL m_ClearingQuery;
	BOOL m_AddingValue;
	void ClearQuery();
	void DeselectAllValues();
	void UpdateCurrentElement();
	int AddValueItem(CString &str);
	int FindValueItem(CString &str);
	BOOL ValidateRange(int idx);
	void DisplayQuery(int curSel);
	void DisplayValues();
	void DisplayRanges();
	int GetCurrentSelection();
	CProductAttribute *m_Attribute;
	BOOL m_IsFormula;
	CString m_Formula;

};

//{{AFX_INSERT_LOCATION}}
// Microsoft Visual C++ will insert additional declarations immediately before the previous line.

#endif // !defined(AFX_PRODUCTGROUPCRITERIALISTPAGE_H__5E791C69_041F_11D5_9EC8_00C04FAC149C__INCLUDED_)

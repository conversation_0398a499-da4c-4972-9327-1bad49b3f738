// UserQueryDataService.cpp: implementation of the CUserQueryDataService class.
//
//////////////////////////////////////////////////////////////////////

#include "stdafx.h"
#include "modal.h"
#include "DataAccessService.h"
#include "UserQueryDataService.h"
#include "ForteService.h"

#include "base.h"
#include "MiscClasses.h"
#include "SLOTSessionMgr.h"
#include "SrvcObjs.h"

#ifdef _DEBUG
#undef THIS_FILE
static char THIS_FILE[]=__FILE__;
#define new DEBUG_NEW
#endif

extern CForteService forteService;
extern CDataAccessService dataAccessService;

//////////////////////////////////////////////////////////////////////
// Construction/Destruction
//////////////////////////////////////////////////////////////////////

CUserQueryDataService::CUserQueryDataService()
{

}

CUserQueryDataService::~CUserQueryDataService()
{

}


int CUserQueryDataService::GetUserQueryList(CStringArray &queryList)
{
#if 0
	CString tempString;
	int tempInt;
	CSsaStringArray tempSendArray;
	CSsaStringArray tempRecvArray;
	CString sendString;
	int i = 0;
#endif
	CString queryText;

	/////////////////////////////////////////////////////////////
	// build the data to send to the Forte connection server
	/////////////////////////////////////////////////////////////

	queryText.Format("select * from DBUserQuery order by name");
		
#if 0
	sendString = "<SIO>SLOTSocketString\n";
	tempSendArray.Add(sendString);
	sendString = "<SAI>GetUserQueryList\n";
	tempSendArray.Add(sendString);
	tempString.Format("<SAI>%s\n",queryText);
	tempSendArray.Add(tempString);
	sendString = "<EIO>SLOTSocketString\n";
	tempSendArray.Add(sendString);
	
	forteService.SendToForteConnection(tempSendArray,tempRecvArray,
		CString("SLOTSocketString"), 55555);

	for (i=0; i < tempRecvArray.GetSize(); i++) {
		if ( tempRecvArray[i].Find("<SAI>") != -1 ) {
			tempString = tempRecvArray[i].Mid(5);
			tempInt = tempString.GetLength();
			if (tempString.GetAt(tempInt-1) == '\n' ) {
				tempString.SetAt(tempInt-1,' ');
				tempString.TrimRight();
			}
			queryList.Add(tempString);			
		}
	}
#else
	CListstring *res = NULL;
	char strMsg[4096] = {0};

	try
	{
		string* queryTmp = new string;
		*queryTmp = (LPCTSTR)queryText;
		bool * b = new bool;
		*b=false;

		SLOTSessionMgr* ptr= getSessionMgrSO();
		res = ptr->RunQueryHelper(queryTmp, b);
	}
	catch(CException *e)
	{
		e->GetErrorMessage (strMsg, 4096);
		e->Delete();
		return -1;
	}
	for (int i=0;i<res->GetCount();i++)
	{
		CString tmpStr = res->GetAt(res->FindIndex(i)).c_str();
		queryList.SetAtGrow(i, tmpStr);
	}
#endif
	if ( queryList.GetSize() == 0 )
		return 0;
	else {
		return queryList.GetSize();
	}

}


int CUserQueryDataService::StoreUserQuery(CUserQuery &query)
{
#if 0
	CString tempString;
	int tempInt;
	CSsaStringArray tempSendArray;
	CSsaStringArray tempRecvArray;
	CString sendString;
#endif
	int i = 0;
	CString queryText;
	CStringArray resultList;
	int nextKey;

	/////////////////////////////////////////////////////////////
	// build the data to send to the Forte connection server
	/////////////////////////////////////////////////////////////
	if (query.m_QueryID == 0) {
		nextKey = dataAccessService.GetNextKey(CString("DBUserQuery"), 1);
		
		queryText.Format("insert into DBUserQuery ( DBUserQueryID, Name, Description, Query, Database) "
			"values  "
			"(%d, '%s', '%s', '%s', '%s')",
			nextKey, query.m_Name, query.m_Description, query.m_Query, query.m_Database);
	}
	else {
		nextKey = query.m_QueryID;
		queryText.Format("update DBUserQuery set Description = '%s', Query = '%s', Database = '%s' "
			"where DBUserQueryID = %d", 
			query.m_Description, query.m_Query, query.m_Database, query.m_QueryID);
	}
	
#if 0
	sendString = "<SIO>SLOTSocketString\n";
	tempSendArray.Add(sendString);
	sendString = "<SAI>StoreUserQuery\n";
	tempSendArray.Add(sendString);
	tempString.Format("<SAI>%s\n",queryText);
	tempSendArray.Add(tempString);
	sendString = "<EIO>SLOTSocketString\n";
	tempSendArray.Add(sendString);
	
	forteService.SendToForteConnection(tempSendArray,tempRecvArray,
		CString("SLOTSocketString"), 44444);

	for (i=0; i < tempRecvArray.GetSize(); i++) {
		if ( tempRecvArray[i].Find("<SAI>") != -1 ) {
			tempString = tempRecvArray[i].Mid(5);
			tempInt = tempString.GetLength();
			if (tempString.GetAt(tempInt-1) == '\n' ) {
				tempString.SetAt(tempInt-1,' ');
				tempString.TrimRight();
			}
			resultList.Add(tempString);			
		}
	}
	if (resultList[0].CompareNoCase("Success") == 0)
		return nextKey;
	else
		return -1;
#else
	int ret = dataAccessService.ExecuteStatement("StoreUserQuery",queryText);
	if (ret == 0)
		return nextKey;
	else
		return -1;
#endif
}


int CUserQueryDataService::RunUserQuery(CString &query, CSsaStringArray &queryResults)
{
#if 0
	CString tempString;
	int tempInt;
	CSsaStringArray tempSendArray;
	CSsaStringArray tempRecvArray;
	CString sendString;
	int i = 0;
	CString queryText;

	/////////////////////////////////////////////////////////////
	// build the data to send to the Forte connection server
	/////////////////////////////////////////////////////////////
	

	sendString = "<SIO>SLOTSocketString\n";
	tempSendArray.Add(sendString);
	sendString = "<SAI>RunUserQuery\n";
	tempSendArray.Add(sendString);
	tempString.Format("<SAI>%s\n",query);
	tempSendArray.Add(tempString);
	tempString.Format("<SAI>True\n");
	tempSendArray.Add(tempString);
	sendString = "<EIO>SLOTSocketString\n";
	tempSendArray.Add(sendString);
	
	forteService.SendToForteConnection(tempSendArray,tempRecvArray,
		CString("SLOTSocketString"), 55555);

	for (i=0; i < tempRecvArray.GetSize(); i++) {
		if ( tempRecvArray[i].Find("<SAI>") != -1 ) {
			tempString = tempRecvArray[i].Mid(5);
			tempInt = tempString.GetLength();
			if (tempString.GetAt(tempInt-1) == '\n' ) {
				tempString.SetAt(tempInt-1,' ');
				tempString.TrimRight();
			}
			queryResults.Add(tempString);			
		}
	}
#else
	CListstring *res = NULL;
	char strMsg[4096] = {0};

	try
	{
		string *queryTmp = new string;
			*queryTmp = (LPCTSTR)query;
		bool *b = new bool;
			*b = false;

		SLOTSessionMgr* ptr= getSessionMgrSO();
		res = ptr->RunQueryHelper(queryTmp, b);

		delete queryTmp;
		delete b;
	}
	catch(CException *e)
	{
		e->GetErrorMessage (strMsg, 4096);
		e->Delete();
		return -1;
	}
	for (int i=0;i<res->GetCount();i++)
	{
		CString tmpStr = res->GetAt(res->FindIndex(i)).c_str();
		queryResults.SetAtGrow(i, tmpStr);
	}
#endif

	if ( queryResults.GetSize() == 0 )
		return 0;
	else {
		return queryResults.GetSize();
	}
	

}

int CUserQueryDataService::DeleteUserQuery(int queryID) 
{
#if 0
	CString tempString;
	int tempInt;
	CSsaStringArray tempSendArray;
	CSsaStringArray tempRecvArray;
	CString sendString;
	int i = 0;
#endif
	CString queryText;
#if 0
	CStringArray resultList;
#endif

	/////////////////////////////////////////////////////////////
	// build the data to send to the Forte connection server
	/////////////////////////////////////////////////////////////

	queryText.Format("delete from DBUserQuery where DBUserQueryID = %d", queryID);
		
#if 0
	sendString = "<SIO>SLOTSocketString\n";
	tempSendArray.Add(sendString);
	sendString = "<SAI>DeleteUserQuery\n";
	tempSendArray.Add(sendString);
	tempString.Format("<SAI>%s\n",queryText);
	tempSendArray.Add(tempString);
	sendString = "<EIO>SLOTSocketString\n";
	tempSendArray.Add(sendString);
	
	forteService.SendToForteConnection(tempSendArray,tempRecvArray,
		CString("SLOTSocketString"), 44444);

	for (i=0; i < tempRecvArray.GetSize(); i++) {
		if ( tempRecvArray[i].Find("<SAI>") != -1 ) {
			tempString = tempRecvArray[i].Mid(5);
			tempInt = tempString.GetLength();
			if (tempString.GetAt(tempInt-1) == '\n' ) {
				tempString.SetAt(tempInt-1,' ');
				tempString.TrimRight();
			}
			resultList.Add(tempString);			
		}
	}

	if (resultList[0].CompareNoCase("Success") == 0)
		return 0;
	else
		return -1;
#else
	return dataAccessService.ExecuteStatement("DeleteUserQuery",queryText);
#endif
}

int CUserQueryDataService::RunUserStatement(CString &statementName, CString &sqlText)
{
#if 0
	CString tempString;
	int tempInt;
	CSsaStringArray tempSendArray;
	CSsaStringArray tempRecvArray;
	CString sendString;
	int i = 0;
	CString cmdText;
	CStringArray resultList;


	/////////////////////////////////////////////////////////////
	// build the data to send to the Forte connection server
	/////////////////////////////////////////////////////////////

	// For now assume only one sql statement; later add support for multiple
	// statements to be run within a transaction


	sendString = "<SIO>SLOTSocketString\n";
	tempSendArray.Add(sendString);
	sendString.Format("<SAI>%s\n", statementName);
	tempSendArray.Add(sendString);
	tempString.Format("<SAI>%s\n",sqlText);
	tempSendArray.Add(tempString);
	sendString = "<EIO>SLOTSocketString\n";
	tempSendArray.Add(sendString);
	
	forteService.SendToForteConnection(tempSendArray,tempRecvArray,
		CString("SLOTSocketString"), 44444);

	for (i=0; i < tempRecvArray.GetSize(); i++) {
		if ( tempRecvArray[i].Find("<SAI>") != -1 ) {
			tempString = tempRecvArray[i].Mid(5);
			tempInt = tempString.GetLength();
			if (tempString.GetAt(tempInt-1) == '\n' ) {
				tempString.SetAt(tempInt-1,' ');
				tempString.TrimRight();
			}
			resultList.Add(tempString);			
		}
	}
	if (resultList[0].CompareNoCase("Success") == 0)
		return 0;
	else
		return -1;
#else
	return dataAccessService.ExecuteStatement(statementName,sqlText);
#endif
}

// ProductAttribute.cpp: implementation of the CProductAttribute class.
//
//////////////////////////////////////////////////////////////////////

#include "stdafx.h"
#include "modal.h"
#include "ProductAttribute.h"
#include "Constants.h"

#include <dbsymtb.h>

#ifdef _DEBUG
#undef THIS_FILE
static char THIS_FILE[]=__FILE__;
#define new DEBUG_NEW
#endif

//////////////////////////////////////////////////////////////////////
// Construction/Destruction
//////////////////////////////////////////////////////////////////////

CProductAttribute::CProductAttribute()
{
	m_AttributeDBID = -1;
	m_Initial = "";
	m_ListValues.RemoveAll();
	m_InternalToDisplayMap.RemoveAll();
	m_DisplayToInternalMap.RemoveAll();
	m_MaximumValue = -1;
	m_MinimumValue = -1;
	m_Name = "";
	m_Type = -1;
	m_TableName = "";
	m_ColumnName = "";
	m_HelpTopic = "";
}

CProductAttribute::~CProductAttribute()
{

}

CProductAttribute& CProductAttribute::operator=(const CProductAttribute &other)
{
	POSITION pos;
	CString key, value;

	m_AttributeDBID = other.m_AttributeDBID;
	m_ColumnName = other.m_ColumnName;
	m_Initial = other.m_Initial;
	m_MaximumValue = other.m_MaximumValue;
	m_MinimumValue = other.m_MinimumValue;
	m_Name = other.m_Name;
	m_TableName = other.m_TableName;
	m_Type = other.m_Type;
	m_HelpTopic = other.m_HelpTopic;

	m_ListValues.RemoveAll();
	m_ListValues.Copy(other.m_ListValues);

	m_InternalToDisplayMap.RemoveAll();
	pos = other.m_InternalToDisplayMap.GetStartPosition();
	while (pos != NULL) {
		other.m_InternalToDisplayMap.GetNextAssoc(pos, key, value);
		m_InternalToDisplayMap.SetAt(key, value);
	}

	m_DisplayToInternalMap.RemoveAll();
	pos = other.m_DisplayToInternalMap.GetStartPosition();
	while (pos != NULL) {
		other.m_DisplayToInternalMap.GetNextAssoc(pos, key, value);
		m_DisplayToInternalMap.SetAt(key, value);
	}


	return *this;
}


CProductAttribute::CProductAttribute(const CProductAttribute &other)
{
	POSITION pos;
	CString key, value;

	m_AttributeDBID = other.m_AttributeDBID;
	m_ColumnName = other.m_ColumnName;
	m_Initial = other.m_Initial;
	m_MaximumValue = other.m_MaximumValue;
	m_MinimumValue = other.m_MinimumValue;
	m_Name = other.m_Name;
	m_TableName = other.m_TableName;
	m_Type = other.m_Type;
	m_HelpTopic = other.m_HelpTopic;

	m_ListValues.RemoveAll();
	m_ListValues.Copy(other.m_ListValues);

	m_InternalToDisplayMap.RemoveAll();
	pos = other.m_InternalToDisplayMap.GetStartPosition();
	while (pos != NULL) {
		other.m_InternalToDisplayMap.GetNextAssoc(pos, key, value);
		m_InternalToDisplayMap.SetAt(key, value);
	}

	m_DisplayToInternalMap.RemoveAll();
	pos = other.m_DisplayToInternalMap.GetStartPosition();
	while (pos != NULL) {
		other.m_DisplayToInternalMap.GetNextAssoc(pos, key, value);
		m_DisplayToInternalMap.SetAt(key, value);
	}

}

int CProductAttribute::Parse(CString &line)
{
	char *str;
	char *ptr;
	CString tmp;
	int idx;
	CString listValues;

	// Format is:
	// DBID|Description|...|[container attributes|[UDFSTART|UDF attributes|][UDFSTART|UDF attributes]...|[other attributes]|
	tmp = line;

	try {
		idx = tmp.Find("||");
		while (idx >= 0) {
			tmp.Insert(idx+1, " ");
			idx = tmp.Find("||");
		}
		
		//tmp.Replace("||", "| |");
		//tmp.Replace("||", "| |");

		str = tmp.GetBuffer(0);
		//columnname, type, min, max, default, listValues, attributeID, tablename, displayname;
		
		ptr = strtok(str, "|");
		m_ColumnName = ptr;
		ptr = strtok(NULL, "|");
		m_Type = atoi(ptr);
		ptr = strtok(NULL, "|");
		m_MinimumValue = atof(ptr);
		ptr = strtok(NULL, "|");
		m_MaximumValue = atof(ptr);
		ptr = strtok(NULL, "|");
		m_Initial = ptr;
		ptr = strtok(NULL, "|");
		listValues = ptr;
		ptr = strtok(NULL, "|");
		m_AttributeDBID = atoi(ptr);
		ptr = strtok(NULL, "|");
		m_TableName = ptr;
		ptr = strtok(NULL, "|");
		m_Name = ptr;
		ptr = strtok(NULL, "|");
		m_HelpTopic = ptr;

		tmp.ReleaseBuffer();

		tmp = listValues;
		str = tmp.GetBuffer(0);
		ptr = strtok(str, ",");
		CString displayValue, internalValue;
		while (ptr != NULL) {
			displayValue = ptr;
			// Internal values are optional; if they are provided (separated by ^),
			// use them, otherwise use the same as the display value
			if (displayValue.Find("^") < 0) {
				m_ListValues.Add(displayValue);
				internalValue = displayValue;
				m_InternalToDisplayMap.SetAt(internalValue, displayValue);
				m_DisplayToInternalMap.SetAt(displayValue, internalValue);
			}
			else {
				internalValue = displayValue.Mid(displayValue.Find("^")+1);
				displayValue = displayValue.Left(displayValue.Find("^"));
				m_ListValues.Add(displayValue);
				m_InternalToDisplayMap.SetAt(internalValue, displayValue);
				m_DisplayToInternalMap.SetAt(displayValue, internalValue);
			}
			ptr = strtok(NULL, ",");
		}
		tmp.ReleaseBuffer();
		
	}
	catch (...) {
		tmp.ReleaseBuffer();
		AfxMessageBox("Error processing product attribute list.\n");
		ads_printf("%s\n", line);
		return -1;
	}

	return 0;
}

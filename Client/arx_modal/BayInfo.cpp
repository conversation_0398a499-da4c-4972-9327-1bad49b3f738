// BayInfo.cpp: implementation of the CBayInfo class.
//
//////////////////////////////////////////////////////////////////////

#include "stdafx.h"
#include "modal.h"
#include "BayInfo.h"

#ifdef _DEBUG
#undef THIS_FILE
static char THIS_FILE[]=__FILE__;
#define new DEBUG_NEW
#endif

//////////////////////////////////////////////////////////////////////
// Construction/Destruction
//////////////////////////////////////////////////////////////////////

CBayInfo::CBayInfo()
{

}

CBayInfo::~CBayInfo()
{
	for (int i=0; i < m_LevelList.GetSize(); ++i)
		delete m_LevelList[i];
}

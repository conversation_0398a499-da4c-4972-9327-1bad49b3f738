// BayProfilePalletPage.cpp : implementation file
//

#include "stdafx.h"
#include "modal.h"
#include "BayProfilePalletPage.h"
#include "BayProfileSheet.h"
#include "UtilityHelper.h"
#include "Constants.h"
#include "HelpService.h"

#ifdef _DEBUG
#define new DEBUG_NEW
#undef THIS_FILE
static char THIS_FILE[] = __FILE__;
#endif

extern CHelpService helpService;
extern CUtilityHelper utilityHelper;
/////////////////////////////////////////////////////////////////////////////
// CBayProfilePalletPage property page

IMPLEMENT_DYNCREATE(CBayProfilePalletPage, CPropertyPage)

CBayProfilePalletPage::CBayProfilePalletPage() : CPropertyPage(CBayProfilePalletPage::IDD)
{
	//{{AFX_DATA_INIT(CBayProfilePalletPage)
	m_BayDepth = 0.0;
	m_BayWidth = 0.0;
	m_NumberOfPallets = 0;
	m_BayHeight = 0.0;
	m_UprightHeight = 0.0;
	m_UprightWidth = 0.0;
	m_WeightCapacity = 0.0;
	m_PalletSpace = 0.0;
	//}}AFX_DATA_INIT
	m_Validating = FALSE;
	m_SideViewButton.m_DimensionInfo.m_BayType = BAYTYPE_PALLET;
	m_TopViewButton.m_DimensionInfo.m_BayType = BAYTYPE_PALLET;
}

CBayProfilePalletPage::~CBayProfilePalletPage()
{
}

void CBayProfilePalletPage::DoDataExchange(CDataExchange* pDX)
{
	CPropertyPage::DoDataExchange(pDX);
	//{{AFX_DATA_MAP(CBayProfilePalletPage)
	DDX_Control(pDX, IDC_TOP_VIEW_BUTTON, m_TopViewButton);
	DDX_Control(pDX, IDC_SIDE_VIEW_BUTTON, m_SideViewButton);
	DDX_Text(pDX, IDC_BAY_DEPTH, m_BayDepth);
	DDV_MinMaxDouble(pDX, m_BayDepth, 0., 999999999.);
	DDX_Text(pDX, IDC_BAY_WIDTH, m_BayWidth);
	DDV_MinMaxDouble(pDX, m_BayWidth, 0., 999999999.);
	DDX_Text(pDX, IDC_NUMBER_OF_PALLETS, m_NumberOfPallets);
	DDV_MinMaxInt(pDX, m_NumberOfPallets, 0, 999999999);
	DDX_Text(pDX, IDC_STACKABLE_HEIGHT, m_BayHeight);
	DDV_MinMaxDouble(pDX, m_BayHeight, 0., 999999999.);
	DDX_Text(pDX, IDC_UPRIGHT_HEIGHT, m_UprightHeight);
	DDV_MinMaxDouble(pDX, m_UprightHeight, 0., 999999999.);
	DDX_Text(pDX, IDC_UPRIGHT_WIDTH, m_UprightWidth);
	DDV_MinMaxDouble(pDX, m_UprightWidth, 0., 999999999.);
	DDX_Text(pDX, IDC_WEIGHT_CAPACITY, m_WeightCapacity);
	DDV_MinMaxDouble(pDX, m_WeightCapacity, 0., 999999999.);
	DDX_Text(pDX, IDC_PALLET_SPACE, m_PalletSpace);
	DDV_MinMaxDouble(pDX, m_PalletSpace, 0., 999999999.);
	//}}AFX_DATA_MAP

	m_SideViewButton.m_DimensionInfo.m_BayDepth = m_BayDepth;
	m_SideViewButton.m_DimensionInfo.m_BayHeight = m_BayHeight;
	m_SideViewButton.m_DimensionInfo.m_BayWidth = m_BayWidth;
	m_SideViewButton.m_DimensionInfo.m_BayType = BAYTYPE_PALLET;
	m_SideViewButton.m_DimensionInfo.m_UprightHeight = m_UprightHeight;
	m_SideViewButton.m_DimensionInfo.m_UprightWidth = m_UprightWidth;
	m_SideViewButton.m_DimensionInfo.m_PalletSpace = m_PalletSpace;
	m_SideViewButton.m_DimensionInfo.m_PositionsDeep = m_NumberOfPallets;
	
	m_TopViewButton.m_DimensionInfo = m_SideViewButton.m_DimensionInfo;

}


BEGIN_MESSAGE_MAP(CBayProfilePalletPage, CPropertyPage)
	//{{AFX_MSG_MAP(CBayProfilePalletPage)
	ON_WM_HELPINFO()
	//}}AFX_MSG_MAP
END_MESSAGE_MAP()

/////////////////////////////////////////////////////////////////////////////
// CBayProfilePalletPage message handlers

BOOL CBayProfilePalletPage::OnInitDialog() 
{
	CPropertyPage::OnInitDialog();
	
	return TRUE;  // return TRUE unless you set the focus to a control
	              // EXCEPTION: OCX Property Pages should return FALSE
}

BOOL CBayProfilePalletPage::OnKillActive() 
{
	UpdateData(TRUE);

	m_pBayProfile->m_Width = m_BayWidth;
	m_pBayProfile->m_Depth = m_BayDepth;
	m_pBayProfile->m_Height = m_BayHeight;
	m_pBayProfile->m_UprightHeight = m_UprightHeight;
	m_pBayProfile->m_UprightWidth = m_UprightWidth;
	m_pBayProfile->m_PalletDepth = m_NumberOfPallets;
	m_pBayProfile->m_PalletSpace = m_PalletSpace;

	for (int x = 0; x < m_pBayProfile->m_LevelProfileList.GetSize(); x++) {
		
		if (m_pBayProfile->m_LevelProfileList[x]->m_LocationProfileList.GetSize() > 0) {	
			
			//Modifications for multi-position pallet locations
			int totalPositions = m_NumberOfPallets;
			
			m_pBayProfile->m_LevelProfileList[x]->m_StackDepth = 
				m_pBayProfile->m_LevelProfileList[x]->m_LocationProfileList[0]->m_Depth;
			m_pBayProfile->m_LevelProfileList[x]->m_StackWidth = 
				m_pBayProfile->m_LevelProfileList[x]->m_LocationProfileList[0]->m_Width;
			if (m_pBayProfile->m_LevelProfileList[x]->m_LocationProfileList[0]->m_IsSelect == 1) {
				m_pBayProfile->m_LevelProfileList[x]->m_SelectPositionHeight = 
					m_pBayProfile->m_LevelProfileList[x]->m_LocationProfileList[0]->m_Height;
				m_pBayProfile->m_LevelProfileList[x]->m_SelectPositions = totalPositions;
				m_pBayProfile->m_LevelProfileList[x]->m_ReservePositionHeight = 0;
				m_pBayProfile->m_LevelProfileList[x]->m_ReservePositions = 0;
			}
			else {
				m_pBayProfile->m_LevelProfileList[x]->m_ReservePositionHeight = 
					m_pBayProfile->m_LevelProfileList[x]->m_LocationProfileList[0]->m_Height;
				m_pBayProfile->m_LevelProfileList[x]->m_ReservePositions = totalPositions;
				m_pBayProfile->m_LevelProfileList[x]->m_SelectPositionHeight = 0;
				m_pBayProfile->m_LevelProfileList[x]->m_SelectPositions = 0;
			}
			m_pBayProfile->m_LevelProfileList[x]->m_WeightCapacity = m_WeightCapacity / 
				(m_pBayProfile->m_LevelProfileList.GetSize());
		}
	}
	
	m_pBayProfile->m_WeightCapacity = m_WeightCapacity;
	
	if (! Validate())
		return 0;

	return CPropertyPage::OnKillActive();
}



BOOL CBayProfilePalletPage::OnCommand(WPARAM wParam, LPARAM lParam) 
{
	if (HIWORD(wParam) == EN_KILLFOCUS && ! m_Validating) {
		m_Validating = TRUE;
		if (! UpdateData(TRUE))
			m_Validating = FALSE;
		else {
			m_SideViewButton.Invalidate(TRUE);
			m_TopViewButton.Invalidate(TRUE);
		}
		m_Validating = FALSE;
	}
	
	return CPropertyPage::OnCommand(wParam, lParam);
}

BOOL CBayProfilePalletPage::Validate()
{
	if (m_BayHeight < m_UprightHeight) {
		AfxMessageBox("The maximum stackable height (Bay Height) must not be less than the "
			"upright height.");
		return utilityHelper.SetEditControlErrorState(this, IDC_STACKABLE_HEIGHT);
	}

	if (m_BayDepth < (m_NumberOfPallets * (m_PalletSpace+1))) {
		AfxMessageBox("The Bay Depth is not large enough to accommodate the number of pallets specified. "
			"Please change the Bay Depth, Number of Pallets Deep, or Space Between Pallets.");
		return utilityHelper.SetEditControlErrorState(this, IDC_BAY_DEPTH);
	}


	double sumLevelWeights = 0;
	double highestCrossbar = 0;
	for (int i=0; i < m_pBayProfile->m_LevelProfileList.GetSize(); ++i) {
		sumLevelWeights += m_pBayProfile->m_LevelProfileList[i]->m_WeightCapacity;
		double hgt = m_pBayProfile->m_LevelProfileList[i]->m_Coordinates.m_Z;
		if (hgt >= m_UprightHeight && (hgt < highestCrossbar || highestCrossbar == 0)) {			
			highestCrossbar = hgt;
		}
	}

	if (m_WeightCapacity < sumLevelWeights) {
		CString temp;
		temp.Format("The bay Weight Capacity must be greater than or equal to the sum of the "
			"level weight capacities(%.0f)", sumLevelWeights);
		AfxMessageBox(temp);
		return utilityHelper.SetEditControlErrorState(this, IDC_WEIGHT_CAPACITY);
	}

	if ((m_UprightHeight - highestCrossbar) < 0) {
		CString temp;
		temp.Format("The Upright Height must be greater than the height of each of the crossbars.\n"
			"Currently there is a crossbar at height %.0f.", 
			highestCrossbar);
		AfxMessageBox(temp);
		return utilityHelper.SetEditControlErrorState(this, IDC_UPRIGHT_HEIGHT);
	}

	for (i=0; i < m_pBayProfile->m_LevelProfileList.GetSize(); ++i) {
		CLevelProfile *pLevel = m_pBayProfile->m_LevelProfileList[i];
		if (pLevel->m_LocationProfileList.GetSize() == 0)
			continue;

		if ( (m_BayDepth + pLevel->m_Overhang)/pLevel->m_LocationRowCount < 1) {
			CString temp;
			temp.Format("The Bay Depth is not large enough to accommodate the "
				"existing locations on level %d.", i+1);
			AfxMessageBox(temp);
			return utilityHelper.SetEditControlErrorState(this, IDC_BAY_DEPTH);
		}

		double locSpace = pLevel->m_LocationProfileList[0]->m_LocationSpace;
		int locsAcross = pLevel->m_LocationProfileList.GetSize()/pLevel->m_LocationRowCount;

		double totalLocSpace = locSpace*2*locsAcross;
		if ( (m_BayWidth - totalLocSpace)/locsAcross < 1) {
			CString temp;
			temp.Format("The Bay Width is not large enough to accommodate the "
				"existing locations on level %d.", i+1);
			AfxMessageBox(temp);
			return utilityHelper.SetEditControlErrorState(this, IDC_BAY_WIDTH);
		}

	}

	// This method will adjust the location sizes based on the current 
	int rc = m_pBayProfile->ResetLocationSizes();
	// We shouldn't meet any of these error conditions because we already checked them above,
	// but just in case.
	if (rc == -1) {
		AfxMessageBox("The bay height can not accommodate the specified locations.");
		return utilityHelper.SetEditControlErrorState(this, IDC_STACKABLE_HEIGHT);
	}
	else if (rc == -2) {
		AfxMessageBox("The bay width can not accommodate the specified locations.");
		return utilityHelper.SetEditControlErrorState(this, IDC_BAY_WIDTH);
	}
	else if (rc == -3) {
		AfxMessageBox("The bay depth can not accommodate the specified locations.");
		return utilityHelper.SetEditControlErrorState(this, IDC_BAY_DEPTH);
	}

	return TRUE;
}

BOOL CBayProfilePalletPage::OnSetActive() 
{
	CBayProfileSheet *pSheet = (CBayProfileSheet *)GetParent();
	m_pBayProfile = pSheet->m_pBayProfile;

	if (m_pBayProfile->m_BayProfileDBId > 0) {

		m_BayDepth = m_pBayProfile->m_Depth;
		m_BayHeight = m_pBayProfile->m_Height;
		m_BayWidth = m_pBayProfile->m_Width;
		m_UprightHeight = m_pBayProfile->m_UprightHeight;
		m_UprightWidth = m_pBayProfile->m_UprightWidth;
		
		m_NumberOfPallets = m_pBayProfile->m_PalletDepth;
		m_PalletSpace = m_pBayProfile->m_PalletSpace;

		m_WeightCapacity = m_pBayProfile->m_WeightCapacity;
	}

	if (m_pBayProfile->m_Active) {
		GetDlgItem(IDC_BAY_DEPTH)->EnableWindow(FALSE);
		GetDlgItem(IDC_BAY_WIDTH)->EnableWindow(FALSE);
		GetDlgItem(IDC_STACKABLE_HEIGHT)->EnableWindow(FALSE);
		GetDlgItem(IDC_UPRIGHT_WIDTH)->EnableWindow(FALSE);
		GetDlgItem(IDC_UPRIGHT_HEIGHT)->EnableWindow(FALSE);
		GetDlgItem(IDC_NUMBER_OF_PALLETS)->EnableWindow(FALSE);
		GetDlgItem(IDC_PALLET_SPACE)->EnableWindow(FALSE);
	}
	else {
		GetDlgItem(IDC_BAY_DEPTH)->EnableWindow(TRUE);
		GetDlgItem(IDC_BAY_WIDTH)->EnableWindow(TRUE);
		GetDlgItem(IDC_STACKABLE_HEIGHT)->EnableWindow(TRUE);
		GetDlgItem(IDC_UPRIGHT_WIDTH)->EnableWindow(TRUE);
		GetDlgItem(IDC_UPRIGHT_HEIGHT)->EnableWindow(TRUE);
		GetDlgItem(IDC_NUMBER_OF_PALLETS)->EnableWindow(TRUE);
		GetDlgItem(IDC_PALLET_SPACE)->EnableWindow(TRUE);
	}
		
	UpdateData(FALSE);	

	return CPropertyPage::OnSetActive();
}

BOOL CBayProfilePalletPage::OnHelpInfo(HELPINFO* pHelpInfo) 
{
	helpService.ShowFieldHelp(IDD, pHelpInfo->iCtrlId);
	return FALSE;

}

BOOL CBayProfilePalletPage::OnNotify(WPARAM wParam, LPARAM lParam, LRESULT* pResult) 
{
	NMHDR* pNMHDR = (NMHDR*)lParam;

	if (pNMHDR->code == PSN_HELP) {
		helpService.ShowScreenHelp(IDD);
		return TRUE;
	}
	
	return CPropertyPage::OnNotify(wParam, lParam, pResult);
}
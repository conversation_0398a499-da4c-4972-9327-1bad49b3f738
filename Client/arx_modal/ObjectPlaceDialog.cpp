// ObjectPlaceDialog.cpp : implementation file
//

#include "stdafx.h"
#include "modal.h"
#include "ObjectPlaceDialog.h"
#include "HelpService.h"
#include "UtilityHelper.h"
#include "HelpService.h"

#include <process.h>

#ifdef _DEBUG
#define new DEBUG_NEW
#undef THIS_FILE
static char THIS_FILE[] = __FILE__;
#endif

extern CHelpService helpService;
extern CUtilityHelper utilityHelper;

/////////////////////////////////////////////////////////////////////////////
// CObjectPlaceDialog dialog


CObjectPlaceDialog::CObjectPlaceDialog(CWnd* pParent /*=NULL*/)
	: CDialog(CObjectPlaceDialog::IDD, pParent)
{
	//{{AFX_DATA_INIT(CObjectPlaceDialog)
	m_XCoordinate = _T("0");
	m_YCoordinate = _T("0");
	m_ZCoordinate = _T("0");
	m_Rotation = _T("90");
	m_Message = _T("");
	m_Increment = _T("1");
	m_Name = _T("01");
	m_Width = _T("");
	m_Length = _T("");
	m_Height = _T("");
	//}}AFX_DATA_INIT

	m_AddingMultiples = FALSE;
}


void CObjectPlaceDialog::DoDataExchange(CDataExchange* pDX)
{
	CDialog::DoDataExchange(pDX);
	//{{AFX_DATA_MAP(CObjectPlaceDialog)
	DDX_Control(pDX, IDC_ROTATION_DISPLAY, m_RotationButton);
	DDX_Text(pDX, IDC_XCOORDINATE, m_XCoordinate);
	DDX_Text(pDX, IDC_YCOORDINATE, m_YCoordinate);
	DDX_Text(pDX, IDC_ZCOORDINATE, m_ZCoordinate);
	DDX_Text(pDX, IDC_ROTATION, m_Rotation);
	DDX_Text(pDX, IDC_MESSAGE, m_Message);
	DDX_Text(pDX, IDC_INCREMENT, m_Increment);
	DDX_Text(pDX, IDC_NAME, m_Name);
	DDX_Text(pDX, IDC_WIDTH, m_Width);
	DDX_Text(pDX, IDC_LENGTH, m_Length);
	DDX_Text(pDX, IDC_HEIGHT, m_Height);
	//}}AFX_DATA_MAP
}


BEGIN_MESSAGE_MAP(CObjectPlaceDialog, CDialog)
	//{{AFX_MSG_MAP(CObjectPlaceDialog)
	ON_BN_CLICKED(IDHELP, OnHelp)
	ON_WM_HELPINFO()
	//}}AFX_MSG_MAP
END_MESSAGE_MAP()

/////////////////////////////////////////////////////////////////////////////
// CObjectPlaceDialog message handlers
BOOL CObjectPlaceDialog::OnInitDialog() 
{
	CDialog::OnInitDialog();
	

	if (m_ObjectType == AddAisle) {
		this->SetWindowText("Add Aisle");
		m_Message.Format("Specify the attributes of the aisle to be placed.\n"
			"The coordinates represent the point of the front, left corner \n"
			"of the aisle as you are walking into it.  The rotation is the \n"
			"degree to rotate around the coordinate.  ");
		GetDlgItem(IDC_WIDTH)->EnableWindow(FALSE);
		GetDlgItem(IDC_LENGTH)->EnableWindow(FALSE);
		GetDlgItem(IDC_HEIGHT)->EnableWindow(FALSE);

		GetDlgItem(IDC_ROTATION)->ShowWindow(SW_SHOW);
		GetDlgItem(IDC_NAME)->ShowWindow(SW_SHOW);
		GetDlgItem(IDC_ROTATION_STATIC)->ShowWindow(SW_SHOW);
		GetDlgItem(IDC_NAME_STATIC)->ShowWindow(SW_SHOW);
		if (m_AddingMultiples) {
			GetDlgItem(IDC_INCREMENT)->ShowWindow(SW_SHOW);
			GetDlgItem(IDC_INCREMENT_STATIC)->ShowWindow(SW_SHOW);
		}
		else {
			GetDlgItem(IDC_INCREMENT)->ShowWindow(SW_HIDE);
			GetDlgItem(IDC_INCREMENT_STATIC)->ShowWindow(SW_HIDE);
		}
	}

	else if (m_ObjectType == AddHotspot) {
		this->SetWindowText("Add/Move Hotspot");
		m_Message.Format("Specify the attributes of the hotspot to be added or moved.\n"
			"The coordinates represent the center point of the hotspot.\n"
			"You may also specify the width of the drawing object that\n"
			"represents the hotspot.");
		GetDlgItem(IDC_WIDTH)->EnableWindow(TRUE);
		GetDlgItem(IDC_LENGTH)->EnableWindow(FALSE);
		GetDlgItem(IDC_HEIGHT)->EnableWindow(FALSE);

		GetDlgItem(IDC_ROTATION)->ShowWindow(SW_HIDE);
		GetDlgItem(IDC_NAME)->ShowWindow(SW_HIDE);
		GetDlgItem(IDC_INCREMENT)->ShowWindow(SW_HIDE);
		GetDlgItem(IDC_ROTATION_STATIC)->ShowWindow(SW_HIDE);
		GetDlgItem(IDC_NAME_STATIC)->ShowWindow(SW_HIDE);
		GetDlgItem(IDC_INCREMENT_STATIC)->ShowWindow(SW_HIDE);
	}

	UpdateData(FALSE);

	m_RotationButton.Invalidate(TRUE);

	return TRUE;  // return TRUE unless you set the focus to a control
	              // EXCEPTION: OCX Property Pages should return FALSE
}

void CObjectPlaceDialog::OnHelp() 
{
	if (m_ObjectType != AddAisle)		// not an aisle - use generic place
		helpService.ShowScreenHelp(IDD);
	else
		helpService.ShowScreenHelp("AddAisle_PlaceObject");

	return;

}

void CObjectPlaceDialog::OnCancel() 
{
	EndDialog(IDCANCEL);
}

void CObjectPlaceDialog::OnOK() 
{
	if (UpdateData(TRUE) == 0 )
		return;

	if (! utilityHelper.IsInteger(m_XCoordinate)) {
		AfxMessageBox("Each of the coordinate values must be a positive integer.");
		utilityHelper.SetEditControlErrorState(this, IDC_XCOORDINATE);
		return;
	}

	if (! utilityHelper.IsInteger(m_YCoordinate)) {
		AfxMessageBox("Each of the coordinate values must be a positive integer.");
		utilityHelper.SetEditControlErrorState(this, IDC_YCOORDINATE);
		return;
	}

	if (! utilityHelper.IsInteger(m_ZCoordinate)) {
		AfxMessageBox("Each of the coordinate values must be a positive integer.");
		utilityHelper.SetEditControlErrorState(this, IDC_ZCOORDINATE);
		return;
	}

	// Placing aisles -- description is required
	if (m_ObjectType == AddAisle) {
		
		if (! utilityHelper.IsInteger(m_Rotation)) {
			AfxMessageBox("The rotation must be a positive integer between 0 and 360.");
			utilityHelper.SetEditControlErrorState(this, IDC_ROTATION);
			return;
		}

		if (atoi(m_Rotation) < 0 || atoi(m_Rotation) > 360) {
			AfxMessageBox("The rotation must be between 0 and 360.");
			utilityHelper.SetEditControlErrorState(this, IDC_ROTATION);
			return;
		}

		if (m_Name == "") {
			AfxMessageBox("Specify a name for the aisle.");
			utilityHelper.SetEditControlErrorState(this, IDC_NAME);
			return;
		}
		
		if (m_AddingMultiples) {
			if (! utilityHelper.IsInteger(m_Increment)) {
				AfxMessageBox("The increment must a positive or negative integer.");
				utilityHelper.SetEditControlErrorState(this, IDC_INCREMENT);
				return;
			}

			if (atoi(m_Increment) == 0) {
				if (AfxMessageBox("When adding multiple aisles, it is recommended that you set "
					"the increment so that each aisle will have a different name.  Do you wish to "
					"set the increment?", MB_YESNO) == IDYES) {
					utilityHelper.SetEditControlErrorState(this, IDC_INCREMENT);
					return;
				}
			}
			
		}
	}

	EndDialog(IDOK);	
}




BOOL CObjectPlaceDialog::OnHelpInfo(HELPINFO* pHelpInfo) 
{
	if (m_ObjectType == AddAisle)
		helpService.ShowFieldHelp(IDD, pHelpInfo->iCtrlId);
	else
		helpService.ShowFieldHelp(IDD_CHOOSEAISLE_DIALOG, pHelpInfo->iCtrlId);

	return FALSE;
}

BOOL CObjectPlaceDialog::OnCommand(WPARAM wParam, LPARAM lParam) 
{

	if (HIWORD(wParam) == EN_KILLFOCUS) {
		UpdateData(TRUE);
		m_RotationButton.Invalidate(TRUE);
	}

	return CDialog::OnCommand(wParam, lParam);
}

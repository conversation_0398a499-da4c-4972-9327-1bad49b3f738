#if !defined(AFX_DATAGRIDLISTBOX_H__68C1A241_CC5A_11D4_9EC1_00C04FAC149C__INCLUDED_)
#define AFX_DATAGRIDLISTBOX_H__68C1A241_CC5A_11D4_9EC1_00C04FAC149C__INCLUDED_

#if _MSC_VER > 1000
#pragma once
#endif // _MSC_VER > 1000
// DataGridListBox.h : header file
//

/////////////////////////////////////////////////////////////////////////////
// CDataGridListBox window

class CDataGridListBox : public CComboBox
{
// Construction
public:
	CDataGridListBox();

// Attributes
public:

// Operations
public:

// Overrides
	// ClassWizard generated virtual function overrides
	//{{AFX_VIRTUAL(CDataGridListBox)
	//}}AFX_VIRTUAL

// Implementation
public:
	virtual ~CDataGridListBox();

	// Generated message map functions
protected:
	//{{AFX_MSG(CDataGridListBox)
	afx_msg void OnChar(UINT nChar, UINT nRepCnt, UINT nFlags);
	afx_msg void OnKeyDown(UINT nChar, UINT nRepCnt, UINT nFlags);
	afx_msg void OnKillFocus(CWnd* pNewWnd);
	afx_msg void OnSetFocus(CWnd* pOldWnd);
	afx_msg UINT OnGetDlgCode();
	afx_msg BOOL OnHelpInfo(HELPINFO* pHelpInfo);
	//}}AFX_MSG

	DECLARE_MESSAGE_MAP()
};

/////////////////////////////////////////////////////////////////////////////

//{{AFX_INSERT_LOCATION}}
// Microsoft Visual C++ will insert additional declarations immediately before the previous line.

#endif // !defined(AFX_DATAGRIDLISTBOX_H__68C1A241_CC5A_11D4_9EC1_00C04FAC149C__INCLUDED_)

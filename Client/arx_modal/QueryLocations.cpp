// QueryLocations.cpp : implementation file
//

#include "stdafx.h"
#include "QueryLocations.h"
#include "qqhclasses.h"
#include "ssa_exception.h"
#include "HelpService.h"
#include "BayProfile.h"
#include "FacilityDataService.h"

#include <dbsymtb.h>

#ifdef _DEBUG
#define new DEBUG_NEW
#undef THIS_FILE
static char THIS_FILE[] = __FILE__;
#endif

/////////////////////////////////////////////////////////////////////////////
// CQueryLocations dialog

extern CHelpService helpService;

CQueryLocations::CQueryLocations(CWnd* pParent /*=NULL*/)
	: CDialog(CQueryLocations::IDD, pParent)
{
	//{{AFX_DATA_INIT(CQueryLocations)
	m_Description = _T("");
	m_ProductGroupID = -1;
	m_Assigned = -1;
	m_LevelType = -1;
	//}}AFX_DATA_INIT
}


void CQueryLocations::DoDataExchange(CDataExchange* pDX)
{
	CDialog::DoDataExchange(pDX);
	//{{AFX_DATA_MAP(CQueryLocations)
	DDX_Text(pDX, IDC_LOC_DESC_EDIT, m_Description);
	DDX_CBIndex(pDX, IDC_PRODUCT_GROUP_BOX, m_ProductGroupID);
	DDX_CBIndex(pDX, IDC_ASSIGNMENT_BOX, m_Assigned);
	DDX_CBIndex(pDX, IDC_LOCTYPE_BOX, m_LevelType);
	//}}AFX_DATA_MAP
}


BEGIN_MESSAGE_MAP(CQueryLocations, CDialog)
	//{{AFX_MSG_MAP(CQueryLocations)
	ON_WM_HELPINFO()
	ON_BN_CLICKED(IDHELP, OnHelp)
	//}}AFX_MSG_MAP
END_MESSAGE_MAP()

/////////////////////////////////////////////////////////////////////////////
// CQueryLocations message handlers

BOOL CQueryLocations::OnInitDialog() 
{
	CDialog::OnInitDialog();
	
	// TODO: Add extra initialization here
	CRect r;
	CString s, desc, levelType;
	int i, idx;

	CComboBox *pAsgnBox = (CComboBox *)GetDlgItem(IDC_ASSIGNMENT_BOX);
	pAsgnBox->SetItemHeight(0,2000);
	pAsgnBox->SetCurSel(0);
	pAsgnBox->GetWindowRect(&r);
	pAsgnBox->SetWindowPos(NULL, 0, 0, r.Width(), r.Height()*5, SWP_NOMOVE|SWP_NOZORDER);

	CComboBox *pProdGroupBox = (CComboBox *)GetDlgItem(IDC_PRODUCT_GROUP_BOX);
	pProdGroupBox->SetItemHeight(0,2000);
	pProdGroupBox->SetCurSel(0);
	pProdGroupBox->GetWindowRect(&r);
	pProdGroupBox->SetWindowPos(NULL, 0, 0, r.Width(), r.Height()*5, SWP_NOMOVE|SWP_NOZORDER);

	
	for (i=0; i < m_ProductGroupList.GetSize(); ++i) {
		s = m_ProductGroupList[i];
		idx = s.Find("|");
		if (idx <= 0)
			continue;
		s = s.Right(s.GetLength() - (idx+1));
		idx = s.Find("|");
		if (idx <= 0)
			idx = s.GetLength();
	
		desc = s.Left(idx);
		pProdGroupBox->AddString(desc);
	}

	CComboBox *pLocTypeBox = (CComboBox *)GetDlgItem(IDC_LOCTYPE_BOX);
	pLocTypeBox->SetItemHeight(0,2000);
	pLocTypeBox->SetCurSel(0);
	pLocTypeBox->GetWindowRect(&r);
	pLocTypeBox->SetWindowPos(NULL, 0, 0, r.Width(), r.Height()*5, SWP_NOMOVE|SWP_NOZORDER);

	pLocTypeBox->AddString("Any");

	for (i=1; i < 8; ++i) {
		if (i == 6 || i == 7)
			continue;
		CBayProfile::ConvertBayType(i, levelType);
		int nItem = pLocTypeBox->AddString(levelType);
		pLocTypeBox->SetItemData(nItem, i);
	}

	return TRUE;  // return TRUE unless you set the focus to a control
	              // EXCEPTION: OCX Property Pages should return FALSE
}

void CQueryLocations::OnOK() 
{
	int rc;
	int pg, asg, idx,levelType;
	CString pgStr, temp;

	UpdateData(TRUE);

	qqhSLOTQuery query;

	query.setObjName("SLOTLocation");
	
	if (m_Description.GetLength() > 0) {
		if (m_Description.Find("%") >= 0)
			temp.Format(" LIKE '%s'", m_Description);
		else
			temp.Format(" = '%s'", m_Description);
		query.AddQueryAttr("SLOTLocation.Description", temp, "AND", "=");
	}

	if (m_Assigned == 1)			// Assigned
		asg = 1;
	else if (m_Assigned == 0)		// Un-assigned
		asg = 0;
	else							// Both
		asg = -1;
	
	if (m_ProductGroupID == 0)		// All
		pg = -2;
	else if (m_ProductGroupID == 1)	// Assigned
		pg = 0;
	else if (m_ProductGroupID == 2)	// Un-assigned
		pg = -1;
	else if (m_ProductGroupID > 2) {
		pgStr = m_ProductGroupList[m_ProductGroupID-3];
		idx = pgStr.Find("|");
		pg = atoi(pgStr.Left(idx));
	}
	else
		pg = -2;					// Default to all
	
	m_Results.SetSize(0);

	CComboBox *pLocTypeBox = (CComboBox *)GetDlgItem(IDC_LOCTYPE_BOX);
	if (pLocTypeBox->GetCurSel() >= 0)
		levelType = pLocTypeBox->GetItemData(pLocTypeBox->GetCurSel());
	else
		levelType = -1;
	
	try {
		CWaitCursor cwc;
		CFacilityDataService facilityDataService;
		rc = facilityDataService.QueryLocations(asg, pg, query, levelType, m_Results);
	}
	catch(Ssa_Exception e) {
		char eMsg[1024];
		e.GetMessage(eMsg);
		ads_printf("%s\n", eMsg);
		AfxMessageBox("Error querying locations.");
	}
	catch(...) {
		AfxMessageBox("Error querying locations.");
	}	

	if (m_Results.GetSize() == 0) {
		AfxMessageBox("No locations were found that meet the selection criteria.");
		return;
	}

	CDialog::OnOK();
}

BOOL CQueryLocations::OnHelpInfo(HELPINFO* pHelpInfo) 
{
	helpService.ShowFieldHelp(IDD, pHelpInfo->iCtrlId);

	return TRUE;
}

void CQueryLocations::OnHelp() 
{
	helpService.ShowScreenHelp(IDD);

	return;
	
}

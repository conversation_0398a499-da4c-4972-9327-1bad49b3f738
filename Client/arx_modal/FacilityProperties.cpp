// FacilityProperties.cpp : implementation file
//

#include "stdafx.h"
#include "modal.h"
#include "FacilityProperties.h"
#include "HelpService.h"
#include "TreeElement.h"
#include "FacilityDataService.h"
#include "PasswordDialog.h"

#ifdef _DEBUG
#define new DEBUG_NEW
#undef THIS_FILE
static char THIS_FILE[] = __FILE__;
#endif

extern CHelpService helpService;
extern TreeElement changesTree;
extern CFacilityDataService facilityDataService;

/////////////////////////////////////////////////////////////////////////////
// CFacilityProperties property page

IMPLEMENT_DYNCREATE(CFacilityProperties, CPropertyPage)

CFacilityProperties::CFacilityProperties() : CPropertyPage(CFacilityProperties::IDD)
{
	//{{AFX_DATA_INIT(CFacilityProperties)
	m_Description = _T("");
	m_Notes = _T("");
	m_TimeHorizonUnits = -1;
	m_TimeHorizonValue = 0;
	m_UOM = -1;
	//}}AFX_DATA_INIT
}

CFacilityProperties::~CFacilityProperties()
{
}

void CFacilityProperties::DoDataExchange(CDataExchange* pDX)
{
	CPropertyPage::DoDataExchange(pDX);
	//{{AFX_DATA_MAP(CFacilityProperties)
	DDX_Text(pDX, IDC_DESCRIPTION, m_Description);
	DDX_Text(pDX, IDC_NOTES, m_Notes);
	DDX_CBIndex(pDX, IDC_TIME_HORIZON_UNITS, m_TimeHorizonUnits);
	DDX_Text(pDX, IDC_TIME_HORIZON_VALUE, m_TimeHorizonValue);
	DDX_CBIndex(pDX, IDC_UOM, m_UOM);
	//}}AFX_DATA_MAP
}


BEGIN_MESSAGE_MAP(CFacilityProperties, CPropertyPage)
	//{{AFX_MSG_MAP(CFacilityProperties)
	ON_WM_HELPINFO()
	//}}AFX_MSG_MAP
END_MESSAGE_MAP()

/////////////////////////////////////////////////////////////////////////////
// CFacilityProperties message handlers

BOOL CFacilityProperties::OnInitDialog() 
{
	CPropertyPage::OnInitDialog();
	
	CRect r;
	CComboBox *pComboBox = (CComboBox *)GetDlgItem(IDC_UOM);
	pComboBox->SetItemHeight(0,2000);
	pComboBox->GetWindowRect(&r);
	pComboBox->SetWindowPos(NULL, 0, 0, r.Width(), r.Height()*3, SWP_NOMOVE|SWP_NOZORDER);

	pComboBox = (CComboBox *)GetDlgItem(IDC_TIME_HORIZON_UNITS);
	pComboBox->SetItemHeight(0,2000);
	pComboBox->GetWindowRect(&r);
	pComboBox->SetWindowPos(NULL, 0, 0, r.Width(), r.Height()*4, SWP_NOMOVE|SWP_NOZORDER);
	
	if (m_Notes == " ")
		m_Notes = "";

	UpdateData(FALSE);

	return TRUE;  // return TRUE unless you set the focus to a control
	              // EXCEPTION: OCX Property Pages should return FALSE
}



BOOL CFacilityProperties::OnHelpInfo(HELPINFO* pHelpInfo) 
{
	helpService.ShowFieldHelp(IDD, pHelpInfo->iCtrlId);
	return FALSE;
}

BOOL CFacilityProperties::OnNotify(WPARAM wParam, LPARAM lParam, LRESULT* pResult) 
{
	NMHDR* pNMHDR = (NMHDR*)lParam;

	if (pNMHDR->code == PSN_HELP) {
		helpService.ShowScreenHelp(IDD);
		return TRUE;
	}
	return CPropertyPage::OnNotify(wParam, lParam, pResult);
}

BOOL CFacilityProperties::OnKillActive() 
{
	UpdateData(TRUE);
	if (m_Notes == "")
		m_Notes = " ";

	UpdateData(FALSE);

	return CPropertyPage::OnKillActive();
}

void CFacilityProperties::OnCancel() 
{
	UpdateData(TRUE);
	if (m_Notes == "")
		m_Notes = " ";

	UpdateData(FALSE);

	CPropertyPage::OnCancel();
}



void CFacilityProperties::OnOK() 
{
	CPropertyPage::OnOK();
}

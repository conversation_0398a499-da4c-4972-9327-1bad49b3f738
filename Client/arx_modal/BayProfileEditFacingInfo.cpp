// BayProfileEditFacingInfo.cpp : implementation file
//

#include "stdafx.h"
#include "modal.h"
#include "BayProfileEditFacingInfo.h"
#include "UtilityHelper.h"
#include "HelpService.h"

#ifdef _DEBUG
#define new DEBUG_NEW
#undef THIS_FILE
static char THIS_FILE[] = __FILE__;
#endif

extern CUtilityHelper utilityHelper;
extern CHelpService helpService;
/////////////////////////////////////////////////////////////////////////////
// CBayProfileEditFacingInfo dialog


CBayProfileEditFacingInfo::CBayProfileEditFacingInfo(CWnd* pParent /*=NULL*/)
	: CDialog(CBayProfileEditFacingInfo::IDD, pParent)
{
	//{{AFX_DATA_INIT(CBayProfileEditFacingInfo)
	m_FacingCount = _T("");
	m_ExtendedBOH = _T("");
	m_ExtendedCube = _T("");
	//}}AFX_DATA_INIT
}


void CBayProfileEditFacingInfo::DoDataExchange(CDataExchange* pDX)
{
	CDialog::DoDataExchange(pDX);
	//{{AFX_DATA_MAP(CBayProfileEditFacingInfo)
	DDX_Text(pDX, IDC_FACING_COUNT_STATIC, m_FacingCount);
	DDX_Text(pDX, IDC_XBOH, m_ExtendedBOH);
	DDX_Text(pDX, IDC_XCUBE, m_ExtendedCube);
	//}}AFX_DATA_MAP
}


BEGIN_MESSAGE_MAP(CBayProfileEditFacingInfo, CDialog)
	//{{AFX_MSG_MAP(CBayProfileEditFacingInfo)
	ON_BN_CLICKED(IDHELP, OnHelp)
	ON_WM_HELPINFO()
	//}}AFX_MSG_MAP
END_MESSAGE_MAP()

/////////////////////////////////////////////////////////////////////////////
// CBayProfileEditFacingInfo message handlers

BOOL CBayProfileEditFacingInfo::OnInitDialog() 
{
	CDialog::OnInitDialog();
	
	UpdateData(FALSE);

	return TRUE;  // return TRUE unless you set the focus to a control
	              // EXCEPTION: OCX Property Pages should return FALSE
}

void CBayProfileEditFacingInfo::OnOK() 
{
	UpdateData(TRUE);
	
	if (! utilityHelper.IsFloat(m_ExtendedCube)) {
		AfxMessageBox("Please enter a valid decimal number for extended cube.");
		utilityHelper.SetEditControlErrorState(this, IDC_XCUBE);
		return;
	}

	if (! utilityHelper.IsFloat(m_ExtendedBOH)) {
		AfxMessageBox("Please enter a valid decimal number for extended balance on hand.");
		utilityHelper.SetEditControlErrorState(this, IDC_XBOH);
		return;
	}

	CDialog::OnOK();
}

BOOL CBayProfileEditFacingInfo::OnHelpInfo(HELPINFO* pHelpInfo) 
{
	helpService.ShowFieldHelp(IDD, pHelpInfo->iCtrlId);

	return FALSE;

}

void CBayProfileEditFacingInfo::OnHelp() 
{
	helpService.ShowScreenHelp(IDD);

	return;

}

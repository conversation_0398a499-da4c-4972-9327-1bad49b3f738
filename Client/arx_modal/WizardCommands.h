// WizardCommands.h: interface for the CWizardCommands class.
//
//////////////////////////////////////////////////////////////////////

#if !defined(AFX_WIZARDCOMMANDS_H__8AACB770_7799_4115_9212_4376C6069929__INCLUDED_)
#define AFX_WIZARDCOMMANDS_H__8AACB770_7799_4115_9212_4376C6069929__INCLUDED_

#include "Commands.h"

#if _MSC_VER > 1000
#pragma once
#endif // _MSC_VER > 1000

class CWizardCommands : CCommands
{
public:
	CWizardCommands();
	virtual ~CWizardCommands();
	
	static void RegisterCommands();

	static void BayWizard();
	static void SideWizard();
	static void AisleWizard();

	static void ProfileMaintenance();

	static void ViewBayProfileDrawing();
	static void ViewSideProfileDrawing();
	static void ViewAisleProfileDrawing();
};

#endif // !defined(AFX_WIZARDCOMMANDS_H__8AACB770_7799_4115_9212_4376C6069929__INCLUDED_)

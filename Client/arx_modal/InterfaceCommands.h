// InterfaceCommands.h: interface for the CInterfaceCommands class.
//
//////////////////////////////////////////////////////////////////////

#if !defined(AFX_INTERFACECOMMANDS_H__CF3819E4_0578_4B29_A481_2F1D28717866__INCLUDED_)
#define AFX_INTERFACECOMMANDS_H__CF3819E4_0578_4B29_A481_2F1D28717866__INCLUDED_

#include "Commands.h"

#if _MSC_VER > 1000
#pragma once
#endif // _MSC_VER > 1000

class CInterfaceCommands : public CCommands
{
public:
	CInterfaceCommands();
	virtual ~CInterfaceCommands();
	
	static void RegisterCommands();
	static void ProductInbound();
	static void LocationOutbound();
	static void AssignmentOutbound();
	static void GenerateMoves();
	static void SearchAnchorMaintenance();
	static void NewProductLayout();
	static void WMSSetup();
	static void WMSGroup();
	static void WMSSync();
	static void ConvertProductFileToXML();

};

#endif // !defined(AFX_INTERFACECOMMANDS_H__CF3819E4_0578_4B29_A481_2F1D28717866__INCLUDED_)

#if !defined(AFX_DRAGDROPTREECTRL_H__63AC05AD_E0DC_11D1_8E53_006008A82731__INCLUDED_)
#define AFX_DRAGDROPTREECTRL_H__63AC05AD_E0DC_11D1_8E53_006008A82731__INCLUDED_

#if _MSC_VER >= 1000
#pragma once
#endif // _MSC_VER >= 1000
// DragDropTreeCtrl.h : header file

/////////////////////////////////////////////////////////////////////////////
// CDragDropTreeCtrl window

#define MESSAGE_DROP WM_USER+10

class CDragDropTreeCtrl : public CTreeCtrl
{
	// Construction
public:
	CDragDropTreeCtrl();
	
	// Attributes
public:

	// Operations
public:
	
	// Overrides
	// ClassWizard generated virtual function overrides
	//{{AFX_VIRTUAL(CDragDropTreeCtrl)
protected:
	virtual BOOL PreCreateWindow(CREATESTRUCT& cs);
	//}}AFX_VIRTUAL
	
	// Implementation
public:
	virtual ~CDragDropTreeCtrl();
	
	// Generated message map functions
protected:
	HTREEITEM HighlightDropTarget (CPoint point);
	int m_nDelayInterval;
	BOOL IsItemExpanded (HTREEITEM hItem);
	int m_nScrollMargin;
	int m_nScrollInterval;
	void CopyChildren (HTREEITEM hDest, HTREEITEM hSrc);
	HTREEITEM CopyTree (HTREEITEM hDest, HTREEITEM hSrc);
	void MoveTree (HTREEITEM hDest, HTREEITEM hSrc);
	BOOL IsChildOf (HTREEITEM hItem1, HTREEITEM hItem2);
	BOOL m_bDragging;
	CImageList* m_pImageList;
	HTREEITEM m_hDragItem;
	//{{AFX_MSG(CDragDropTreeCtrl)
	afx_msg void OnBeginDrag(NMHDR* pNMHDR, LRESULT* pResult);
	afx_msg void OnMouseMove(UINT nFlags, CPoint point);
	afx_msg void OnLButtonUp(UINT nFlags, CPoint point);
	afx_msg void OnTimer(UINT nIDEvent);
	//}}AFX_MSG
	
	DECLARE_MESSAGE_MAP()
};

/////////////////////////////////////////////////////////////////////////////
//{{AFX_INSERT_LOCATION}}
// Microsoft Developer Studio will insert additional declarations immediately
// before the previous line.

#endif
// !defined(AFX_DRAGDROPTREECTRL_H__63AC05AD_E0DC_11D1_8E53_006008A82731__INCLUDED_)

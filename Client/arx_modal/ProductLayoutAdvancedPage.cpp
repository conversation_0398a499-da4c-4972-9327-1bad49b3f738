// ProductLayoutAdvancedPage.cpp : implementation file
//

#include "stdafx.h"
#include "modal.h"
#include "ProductLayoutAdvancedPage.h"
#include "HelpService.h"

#ifdef _DEBUG
#define new DEBUG_NEW
#undef THIS_FILE
static char THIS_FILE[] = __FILE__;
#endif

extern CHelpService helpService;
/////////////////////////////////////////////////////////////////////////////
// CProductLayoutAdvancedPage property page

IMPLEMENT_DYNCREATE(CProductLayoutAdvancedPage, CPropertyPage)

CProductLayoutAdvancedPage::CProductLayoutAdvancedPage() : CPropertyPage(CProductLayoutAdvancedPage::IDD)
{
	//{{AFX_DATA_INIT(CProductLayoutAdvancedPage)
	m_FactorSlider = 100;
	m_IgnoreWeight = FALSE;
	m_Overlap = FALSE;
	m_LogMode = _T("None");
	m_MaxResults = 5000;
	m_IgnoreRankings = -1;
	//}}AFX_DATA_INIT
	m_OptimizationRatio = 1;
	m_FollowPickPath = 0;
	m_IgnoreRankings = 0;
}

CProductLayoutAdvancedPage::~CProductLayoutAdvancedPage()
{
}

void CProductLayoutAdvancedPage::DoDataExchange(CDataExchange* pDX)
{
	CPropertyPage::DoDataExchange(pDX);
	//{{AFX_DATA_MAP(CProductLayoutAdvancedPage)
	DDX_Control(pDX, IDC_IGNORE_RANKINGS, m_IgnoreRankingsCtrl);
	DDX_Control(pDX, IDC_LOG_MODE, m_LogModeCtrl);
	DDX_Control(pDX, IDC_FACTOR_SLIDER, m_FactorSliderCtrl);
	DDX_Slider(pDX, IDC_FACTOR_SLIDER, m_FactorSlider);
	DDX_Check(pDX, IDC_IGNORE_WEIGHT, m_IgnoreWeight);
	DDX_Check(pDX, IDC_OVERLAP, m_Overlap);
	DDX_CBString(pDX, IDC_LOG_MODE, m_LogMode);
	DDX_Text(pDX, IDC_MAX_RESULTS, m_MaxResults);
	DDX_CBIndex(pDX, IDC_IGNORE_RANKINGS, m_IgnoreRankings);
	//}}AFX_DATA_MAP
}


BEGIN_MESSAGE_MAP(CProductLayoutAdvancedPage, CPropertyPage)
	//{{AFX_MSG_MAP(CProductLayoutAdvancedPage)
	ON_WM_HELPINFO()
	//}}AFX_MSG_MAP
END_MESSAGE_MAP()

/////////////////////////////////////////////////////////////////////////////
// CProductLayoutAdvancedPage message handlers
BOOL CProductLayoutAdvancedPage::OnInitDialog() 
{
	CPropertyPage::OnInitDialog();

	m_LogModeCtrl.AddString("None");
	m_LogModeCtrl.AddString("User");
	m_LogModeCtrl.AddString("Expert");
	
	CRect r;
	m_LogModeCtrl.GetWindowRect(&r);
	m_LogModeCtrl.SetWindowPos(NULL, 0, 0, r.Width(), r.Height()*4, SWP_NOMOVE|SWP_NOZORDER);
	
	int curSel = m_LogModeCtrl.FindString(-1, m_LogMode);
	if (curSel >= 0)
		m_LogModeCtrl.SetCurSel(curSel);
	else
		m_LogModeCtrl.SetCurSel(0);

	m_IgnoreRankingsCtrl.GetWindowRect(&r);
	m_IgnoreRankingsCtrl.SetWindowPos(NULL, 0, 0, r.Width(), r.Height()*4, SWP_NOMOVE|SWP_NOZORDER);

	m_FactorSliderCtrl.SetRange(0, 100, TRUE);
	m_FactorSlider = 100 - m_UtilizationStart;

	CButton *pButton;
	if (m_FollowPickPath)
		pButton = (CButton *)GetDlgItem(IDC_PICKPATH_SEQUENCE);
	else
		pButton = (CButton *)GetDlgItem(IDC_COST_SEQUENCE);

	pButton->SetCheck(1);

	UpdateData(FALSE);

	return TRUE;  // return TRUE unless you set the focus to a control
	              // EXCEPTION: OCX Property Pages should return FALSE
}

BOOL CProductLayoutAdvancedPage::OnKillActive() 
{
	UpdateData(TRUE);
	
	CButton *pButton = (CButton *)GetDlgItem(IDC_PICKPATH_SEQUENCE);
	m_FollowPickPath = pButton->GetCheck();

	m_OptimizationRatio = 1;
	//m_OptimizationRatio = (double)m_FactorSlider/100.0;

	m_UtilizationStart = 100 - m_FactorSlider;
	m_UtilizationDecrement = 25;



	return CPropertyPage::OnKillActive();
}


BOOL CProductLayoutAdvancedPage::OnHelpInfo(HELPINFO* pHelpInfo) 
{
	helpService.ShowFieldHelp(IDD, pHelpInfo->iCtrlId);
	return FALSE;

}

BOOL CProductLayoutAdvancedPage::OnNotify(WPARAM wParam, LPARAM lParam, LRESULT* pResult) 
{
	NMHDR* pNMHDR = (NMHDR*)lParam;

	if (pNMHDR->code == PSN_HELP) {
		helpService.ShowScreenHelp(IDD);
		return TRUE;
	}
	
	return CPropertyPage::OnNotify(wParam, lParam, pResult);
}

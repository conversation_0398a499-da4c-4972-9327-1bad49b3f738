// CostComparisonDialog.cpp : implementation file
//

#include "stdafx.h"
#include <afxmt.h>

#include "HelpService.h"
#include "UtilityHelper.h"
#include "OptimizationHelper.h"
#include "FacilityDataService.h"
#include "BTreeHelper.h"
#include "ssa_exception.h"
#include "modal.h"
#include "CostComparisonDialog.h"
#include "TreeElement.h"
#include "qqhclasses.h"
#include "ProcessingMessage.h"
#include "Constants.h"
#include "ControlService.h"
#include "ThreadParameters.h"
#include "ResourceHelper.h"

#include <dbsymtb.h>

#ifdef _DEBUG
#define new DEBUG_NEW
#undef THIS_FILE
static char THIS_FILE[] = __FILE__;
#endif

extern CHelpService helpService;
extern CUtilityHelper utilityHelper;
extern CBTreeHelper bTreeHelper;
extern CControlService controlService;
extern CFacilityDataService facilityDataService;
/////////////////////////////////////////////////////////////////////////////
// CCostComparisonDialog dialog

extern TreeElement changesTree;


CCostComparisonDialog::CCostComparisonDialog(CWnd* pParent /*=NULL*/)
	: CDialog(CCostComparisonDialog::IDD, pParent)
{
	//{{AFX_DATA_INIT(CCostComparisonDialog)
	m_TimeHorizonUnits = TIME_HORIZON_WEEK-1;
	m_TimeHorizonValue = _T("1");
	//}}AFX_DATA_INIT
}


void CCostComparisonDialog::DoDataExchange(CDataExchange* pDX)
{
	CDialog::DoDataExchange(pDX);
	//{{AFX_DATA_MAP(CCostComparisonDialog)
	DDX_Control(pDX, IDC_TIME_HORIZON_UNITS, m_TimeHorizonUnitsCtrl);
	DDX_Control(pDX, IDC_FACILITY_LIST, m_FacilityListCtrl);
	DDX_CBIndex(pDX, IDC_TIME_HORIZON_UNITS, m_TimeHorizonUnits);
	DDX_Text(pDX, IDC_TIME_HORIZON_VALUE, m_TimeHorizonValue);
	//}}AFX_DATA_MAP
}


BEGIN_MESSAGE_MAP(CCostComparisonDialog, CDialog)
	//{{AFX_MSG_MAP(CCostComparisonDialog)
	ON_BN_CLICKED(IDC_RECALCULATE_COST, OnRecalculateCost)
	ON_BN_CLICKED(IDC_REFRESH, OnRefresh)
	ON_WM_HELPINFO()
	ON_BN_CLICKED(IDHELP, OnHelp)
	ON_BN_CLICKED(IDC_UP, OnUp)
	ON_BN_CLICKED(IDC_DOWN, OnDown)
	//}}AFX_MSG_MAP
END_MESSAGE_MAP()

/////////////////////////////////////////////////////////////////////////////
// CCostComparisonDialog message handlers
BOOL CCostComparisonDialog::OnInitDialog() 
{
	CDialog::OnInitDialog();
	CRect r;

	CComboBox *pComboBox = (CComboBox *)GetDlgItem(IDC_TIME_HORIZON_UNITS);
	pComboBox->GetWindowRect(&r);
	pComboBox->SetWindowPos(NULL, 0, 0, r.Width(), r.Height()*4, SWP_NOMOVE|SWP_NOZORDER);

	GetDlgItem(IDC_UP)->ShowWindow(SW_SHOW);
	GetDlgItem(IDC_DOWN)->ShowWindow(SW_SHOW);

	CButton *pButton = (CButton *)GetDlgItem(IDC_UP);
	//pButton->SetIcon(AfxGetApp()->LoadIcon(IDI_MOVEUP));

	CTemporaryResourceOverride tro;
	
	HICON hIcon = (HICON)LoadImage(AfxGetResourceHandle(),
		MAKEINTRESOURCE(IDI_MOVEUP), IMAGE_ICON, 16, 16, LR_DEFAULTCOLOR);
	pButton->SetIcon(hIcon);

	pButton = (CButton *)GetDlgItem(IDC_DOWN);
	hIcon = (HICON)LoadImage(AfxGetResourceHandle(),
		MAKEINTRESOURCE(IDI_MOVEDOWN), IMAGE_ICON, 16, 16, LR_DEFAULTCOLOR);
	pButton->SetIcon(hIcon);

	for (int i=0; i < m_FacilityListCtrl.GetItemCount(); ++i)
		m_FacilityListCtrl.DeleteColumn(0);

	m_FacilityListCtrl.GetClientRect(&r);
	m_FacilityListCtrl.InsertColumn(0, "Facility", LVCFMT_LEFT, r.Width()/2, 0);
	m_FacilityListCtrl.InsertColumn(1, "Baseline Cost", LVCFMT_LEFT, r.Width()/4, 0);
	m_FacilityListCtrl.InsertColumn(2, "Optimize Cost", LVCFMT_LEFT, r.Width()/4, 0);

	LoadFacilities();
	UpdateData(FALSE);
	
	//ConvertCosts();
	BuildList();

	return TRUE;  // return TRUE unless you set the focus to a control
	              // EXCEPTION: OCX Property Pages should return FALSE
}

void CCostComparisonDialog::LoadFacilities()
{
	int rc;
	CStringArray facilityList, strings;
	qqhSLOTFacility facility;


	try {
		rc = facilityDataService.GetFacilityCost(facilityList);
	}
	catch (Ssa_Exception e) {
		controlService.Log("Error loading facilities.", &e);
		return;
	}
	catch (...) {
		controlService.Log("Error loading facilities.", "Generic exception in GetFacilityCost.\n");
		return;
	}

	// facilityDBID|facilityName|cost|units|value
	for (int i=0; i < facilityList.GetSize(); ++i) {
		try {
			strings.RemoveAll();
			utilityHelper.ParseString(facilityList[i], "|", strings);

			facCostStruct fac;

			fac.facilityId = atoi(strings[0]);
			fac.description = strings[1];
			fac.optimizeCost = atof(strings[2]);
			fac.baselineCost = atof(strings[3]);
			//fac.timeHorizonDuration = atoi(strings[4]);
			//fac.timeHorizonUnits = atoi(strings[5]);
		
			// default the horizon to the current facility values
			if (fac.facilityId == changesTree.elementDBID) {
				bTreeHelper.GetBtFacility(changesTree.fileOffset, facility);
				//m_TimeHorizonUnits = facility.getTimeHorizonUnit()-1;
				//m_TimeHorizonValue.Format("%d", facility.getDuration());
				//fac.timeHorizonUnits = facility.getTimeHorizonUnit();
				//fac.timeHorizonDuration = facility.getDuration();
				fac.optimizeCost = facility.getCost();
				fac.baselineCost = facility.getBaselineCost();
			}
			
			m_FacilityList.Add(fac);	
		}
		catch (...) {
			controlService.Log("Error parsing facility data.", "Error in LoadFacilities.\n");
			return;
		}

	}
}

void CCostComparisonDialog::ConvertCosts()
{
	double newCost;
	int newUnits, newValue;
	CString temp;

	newUnits = m_TimeHorizonUnitsCtrl.GetCurSel()+1;
	newValue = atoi(m_TimeHorizonValue);

	for (int i=0; i < m_FacilityListCtrl.GetItemCount(); ++i) {
		int idx = m_FacilityListCtrl.GetItemData(i);
		facCostStruct fac = m_FacilityList[idx];

		// now we have the cost at 1 week; convert it to the current settings
		switch (newUnits) {
		case TIME_HORIZON_DAY:
			newCost = fac.baselineCost/(double)7*(double)newValue;
			break;
		case TIME_HORIZON_WEEK:
			newCost = fac.baselineCost*(double)newValue;
			break;
		case TIME_HORIZON_MONTH:
			newCost = fac.baselineCost*4.35*(double)newValue;
			break;
		case TIME_HORIZON_YEAR:
			newCost = fac.baselineCost*52.15*(double)newValue;
			break;
		}

		temp.Format("%.2f", newCost);
		m_FacilityListCtrl.SetItemText(i, 1, temp);

		switch (newUnits) {
		case TIME_HORIZON_DAY:
			newCost = fac.optimizeCost/(double)7*(double)newValue;
			break;
		case TIME_HORIZON_WEEK:
			newCost = fac.optimizeCost*(double)newValue;
			break;
		case TIME_HORIZON_MONTH:
			newCost = fac.optimizeCost*4.35*(double)newValue;
			break;
		case TIME_HORIZON_YEAR:
			newCost = fac.optimizeCost*52.15*(double)newValue;
			break;
		}

		temp.Format("%.2f", newCost);
		m_FacilityListCtrl.SetItemText(i, 2, temp);

		temp.Format("%d",m_TimeHorizonUnits);
		
		controlService.SetApplicationData("TimeUnits",temp, "dialogs\\CostComparisonDialog");

		temp.Format("%s",m_TimeHorizonValue);

		controlService.SetApplicationData("TimeValue", temp, "dialogs\\CostComparisonDialog");

	}
}

void CCostComparisonDialog::OnRecalculateCost() 
{
	int curSel = -1;
	CString cost;
	POSITION pos;
	CStringArray results;

	pos = m_FacilityListCtrl.GetFirstSelectedItemPosition();
	if (pos != NULL)
		curSel = m_FacilityListCtrl.GetNextSelectedItem(pos);

	if (curSel < 0) {
		AfxMessageBox("Please select a facility.");
		return;
	}

	int idx = m_FacilityListCtrl.GetItemData(curSel);
	facCostStruct &fac = m_FacilityList[idx];

	CProcessingMessage dlg("Calculating Cost...", this);

	GetDlgItem(IDC_RECALCULATE_COST)->EnableWindow(FALSE);

	try {
		
		CThreadParameters parms;
		CEvent event;
		parms.m_pEvent = &event;
		CStringArray inList;
		inList.SetSize(1);
		inList[0].Format("%d", fac.facilityId);
		parms.m_pInList = &inList;
		parms.m_pOutList = &results;

		parms.m_pEvent->ResetEvent();
		CWinThread *pThread = AfxBeginThread(CCostComparisonDialog::CalculateBaselineCostThread, &parms);
		
		BOOL bThreadDone = false;
		while (TRUE) {
			if ( ! utilityHelper.PeekAndPump() )
				break;
			
			bThreadDone = parms.m_pEvent->Lock(0);
			if (bThreadDone)
				break;
		}
		
		if (parms.m_ReturnCode < 0) {
			controlService.Log("Error re-calculating cost.", parms.m_ReturnMessage);
			GetDlgItem(IDC_RECALCULATE_COST)->EnableWindow(TRUE);
			return;
		}
		
	}

	catch (Ssa_Exception e) {
		ads_printf(results[1]);
		controlService.Log("Error re-calculating cost.", &e);
		GetDlgItem(IDC_RECALCULATE_COST)->EnableWindow(TRUE);
		return;
	}
	catch (...) {
		controlService.Log("Error re-calculating cost.", results[1] + "\n");
		GetDlgItem(IDC_RECALCULATE_COST)->EnableWindow(TRUE);
		return;
	}

	fac.baselineCost = atof(results[0]);
	fac.optimizeCost = atof(results[1]);

	ConvertCosts();

	UpdateData(FALSE);

	GetDlgItem(IDC_RECALCULATE_COST)->EnableWindow(TRUE);
	DWORD err = GetLastError();

	return;		 

}

void CCostComparisonDialog::OnRefresh() 
{
	UpdateData(TRUE);

	if (m_TimeHorizonValue == "") {
		AfxMessageBox("The time horizon value must be specified.");
		utilityHelper.SetEditControlErrorState(this, IDC_TIME_HORIZON_VALUE);
		return;
	}

	if (! utilityHelper.IsNumeric(m_TimeHorizonValue)) {
		AfxMessageBox("The time horizon value must be numeric.");
		utilityHelper.SetEditControlErrorState(this, IDC_TIME_HORIZON_VALUE);
		return;
	}


	ConvertCosts();
	
}


UINT CCostComparisonDialog::CalculateBaselineCostThread(LPVOID pParam)
{
	CThreadParameters &parms = *(CThreadParameters *)pParam;
	int rc = 0;;
	CString cost;
	COptimizationHelper optimizationHelper;

	parms.m_ReturnCode = 0;

	int facilityId = atoi(parms.m_pInList->GetAt(0));

	try {
		rc = optimizationHelper.CalculateBaselineCost(cost, facilityId);
		parms.m_pOutList->Add(cost);

		rc = optimizationHelper.CalculateOptimizeCost(cost, facilityId);
		parms.m_pOutList->Add(cost);
	}
	catch (Ssa_Exception e) {
		char msgBuf[1024];
		e.GetMessage(msgBuf);
		parms.m_ReturnCode = -1;
		parms.m_ReturnMessage = msgBuf;
	}
	catch (...) {
		parms.m_ReturnCode = -1;
		parms.m_ReturnMessage = "Error calculating cost.";
	}

	if (rc < 0) {
		parms.m_ReturnCode = -1;
		parms.m_ReturnMessage = "Error calculating cost.";
	}

	parms.m_pEvent->SetEvent();

	return parms.m_ReturnCode;

}


BOOL CCostComparisonDialog::OnHelpInfo(HELPINFO* pHelpInfo) 
{
	helpService.ShowFieldHelp(IDD, pHelpInfo->iCtrlId);
	return FALSE;
}

void CCostComparisonDialog::OnHelp() 
{
	helpService.ShowScreenHelp(IDD);
}

void CCostComparisonDialog::BuildList()
{
	for (int i=0; i < m_FacilityList.GetSize(); ++i) {
		
		facCostStruct fac = m_FacilityList[i];
		
		int nItem = m_FacilityListCtrl.InsertItem(i, fac.description);
		m_FacilityListCtrl.SetItemData(nItem, i);
		
		CString temp;
		temp.Format("%.02f", fac.baselineCost);
		m_FacilityListCtrl.SetItemText(nItem, 1, temp);
		
		temp.Format("%.02f", fac.optimizeCost);
		m_FacilityListCtrl.SetItemText(nItem, 2, temp);
	}

	Reorder();

	CString	temp_TimeHorizonUnits;
	CString	temp_TimeHorizonValue;
	CString temp;

	temp_TimeHorizonUnits = controlService.GetApplicationData("TimeUnits", "dialogs\\CostComparisonDialog");
	temp_TimeHorizonValue = controlService.GetApplicationData("TimeValue", "dialogs\\CostComparisonDialog");

	if (temp_TimeHorizonUnits != "")
		m_TimeHorizonUnits = atoi(temp_TimeHorizonUnits);
	else {
		m_TimeHorizonUnits = 2;
		temp.Format("%d",m_TimeHorizonUnits);
		controlService.SetApplicationData("TimeUnits",temp, "dialogs\\CostComparisonDialog");
	}
	if (temp_TimeHorizonValue != "")
		m_TimeHorizonValue = temp_TimeHorizonValue;
	else {
		m_TimeHorizonValue = "6";
		controlService.SetApplicationData("TimeValue",m_TimeHorizonValue, "dialogs\\CostComparisonDialog");
	}

	UpdateData(FALSE);

	ConvertCosts();
	
}

void CCostComparisonDialog::OnUp() 
{
	
	int curSel = -1;
	CString cost;
	POSITION pos;
	CStringArray results;
	int ItemDataIndex;

	pos = m_FacilityListCtrl.GetFirstSelectedItemPosition();

	if (pos != NULL)
		curSel = m_FacilityListCtrl.GetNextSelectedItem(pos);

	if (curSel < 0) {
		AfxMessageBox("Please select a facility.");
		return;
	} 
	
	ItemDataIndex = m_FacilityListCtrl.GetItemData(curSel);

	

	if (curSel == 0)
		return;

	m_FacilityListCtrl.DeleteItem(curSel);

	curSel -= 1; //move the item up in the list 1 spot



	m_FacilityListCtrl.InsertItem(curSel,m_FacilityList[ItemDataIndex].description);
	m_FacilityListCtrl.SetItemData(curSel,ItemDataIndex);

	CString temp;
	temp.Format("%.02f", m_FacilityList[ItemDataIndex].baselineCost);
	m_FacilityListCtrl.SetItemText(curSel, 1, temp);
	
	temp.Format("%.02f", m_FacilityList[ItemDataIndex].optimizeCost);
	m_FacilityListCtrl.SetItemText(curSel, 2, temp);
	
	m_FacilityListCtrl.SetItemState(curSel, LVIS_SELECTED, LVIS_SELECTED);
	
	CString RegKeyName;
	RegKeyName = controlService.m_CurrentDatabase;
	temp = m_FacilityListCtrl.GetItemText(curSel,0);
	RegKeyName += temp;

	temp.Format(TEXT("%d"), curSel);

	controlService.SetApplicationData(RegKeyName, temp , "dialogs\\CostComparisonDialog");

}

void CCostComparisonDialog::OnDown() 
{
	
	int curSel = -1;
	CString cost;
	POSITION pos;
	CStringArray results;
	int ItemDataIndex;

	pos = m_FacilityListCtrl.GetFirstSelectedItemPosition();

	if (pos != NULL)
		curSel = m_FacilityListCtrl.GetNextSelectedItem(pos);

	if (curSel < 0) {
		AfxMessageBox("Please select a facility.");
		return;
	} 
	
	ItemDataIndex = m_FacilityListCtrl.GetItemData(curSel);	

	if (curSel == (m_FacilityListCtrl.GetItemCount() - 1))
		return;


	m_FacilityListCtrl.DeleteItem(curSel);

	curSel += 1; // move the item down in the list 1 spot

	m_FacilityListCtrl.InsertItem(curSel,m_FacilityList[ItemDataIndex].description);
	m_FacilityListCtrl.SetItemData(curSel,ItemDataIndex);

	
	CString temp;
	temp.Format("%.02f", m_FacilityList[ItemDataIndex].baselineCost);
	m_FacilityListCtrl.SetItemText(curSel, 1, temp);
	
	temp.Format("%.02f", m_FacilityList[ItemDataIndex].optimizeCost);
	m_FacilityListCtrl.SetItemText(curSel, 2, temp);

	m_FacilityListCtrl.SetItemState(curSel, LVIS_SELECTED, LVIS_SELECTED);

	CString RegKeyName;
	RegKeyName = controlService.m_CurrentDatabase;
	temp = m_FacilityListCtrl.GetItemText(curSel,0);
	RegKeyName += temp;

	temp.Format(TEXT("%d"), curSel);

	controlService.SetApplicationData(RegKeyName, temp , "dialogs\\CostComparisonDialog");

}

void CCostComparisonDialog::Reorder()
{
	int numItems = -1;
	int loopVar = 0;
	CString m_DataValue;
	CString m_ReturnValue;
	CString temp;
	int newIndex = 0;
	int ItemDataIndex = 0;

	numItems = m_FacilityListCtrl.GetItemCount();

	for (loopVar = 0; loopVar < numItems; loopVar++)
	{
		m_DataValue = controlService.m_CurrentDatabase;
		temp = m_FacilityListCtrl.GetItemText(loopVar,0);
		m_DataValue += temp;

		m_ReturnValue = controlService.GetApplicationData(m_DataValue, "dialogs\\CostComparisonDialog");

		if (m_ReturnValue != "")
		{
			newIndex = atoi(m_ReturnValue);
			ItemDataIndex = m_FacilityListCtrl.GetItemData(loopVar);
			m_FacilityListCtrl.DeleteItem(loopVar);			
			m_FacilityListCtrl.InsertItem(newIndex,m_FacilityList[ItemDataIndex].description);
			m_FacilityListCtrl.SetItemData(newIndex,ItemDataIndex);
			
			temp.Format("%.02f", m_FacilityList[ItemDataIndex].baselineCost);
			m_FacilityListCtrl.SetItemText(newIndex, 1, temp);
			
			temp.Format("%.02f", m_FacilityList[ItemDataIndex].optimizeCost);
			m_FacilityListCtrl.SetItemText(newIndex, 2, temp);
			
			m_FacilityListCtrl.SetItemState(newIndex, LVIS_SELECTED, LVIS_SELECTED);
		}

	}
	
}

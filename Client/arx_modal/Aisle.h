// Aisle.h: interface for the CAisle class.
//
//////////////////////////////////////////////////////////////////////

#if !defined(AFX_AISLE_H__A14DF698_E5CB_4118_82F6_828FC5F7162E__INCLUDED_)
#define AFX_AISLE_H__A14DF698_E5CB_4118_82F6_828FC5F7162E__INCLUDED_

#if _MSC_VER > 1000
#pragma once
#endif // _MSC_VER > 1000

#include "FacilityElement.h"
#include "Side.h"
#include "3DPoint.h"
#include "PickPath.h"

class CAisle : public CFacilityElement  
{
public:
	CAisle();
	virtual ~CAisle();
	
	C3DPoint m_Coordinates;
	double m_Rotation;				// Rotation
	CPickPath m_PickPath;

	// New fields
	double m_AisleSpace;			// AisleSpace - space between aisles

	CTypedPtrArray<CObArray, CSide*> m_SideArray;
	CMap<long, long, CSide*, CSide*> m_SideMapById;
	CMap<CString, LPCSTR, CSide*, CSide*> m_SideMapByName;

};

#endif // !defined(AFX_AISLE_H__A14DF698_E5CB_4118_82F6_828FC5F7162E__INCLUDED_)

// UtilityHelper.h: interface for the CUtilityHelper class.
//
//////////////////////////////////////////////////////////////////////

#if !defined(AFX_UTILITYHELPER_H__9CA58F97_885F_42A0_B1EA_0BD2427F3473__INCLUDED_)
#define AFX_UTILITYHELPER_H__9CA58F97_885F_42A0_B1EA_0BD2427F3473__INCLUDED_

#include "TreeElement.h"


#if _MSC_VER > 1000
#pragma once
#endif // _MSC_VER > 1000



typedef struct {
	CBayProfile *bayprofile;
	double rotation;
	C3DPoint point;
	CString handle;
	double uprightWidth;
} ref_struct;


class CUtilityHelper  
{
public:
	void ColorDisconnectedPickpaths();
	void ConvertDrawing();
	int GetNextEventPosition(int eventId);
	int GetNextEventId(int totalCount);
	int ConvertXData();
	CString GetUTCDate();
	CString GetUTCFileDate();
	CString NonNull(const CString &field) { return (field != "") ? field : " "; }

	CString AddXML(const CString &tag, const CString &value);
	CString AddXML(const CString &tag, int value);
	CString AddXML(const CString &tag, double value, int precision = 0);


	double ConvertDegreesToRadians(double degrees);
	double ConvertRadiansToDegrees(double radians);
	CWnd * GetParentWindow();


	
	
	BOOL SetEditControlErrorState(CWnd *pDlg, int controlId);
	int SkipMessageBox(const CString &message, const CString &caption, 
		int options, int defaultOption, const CString &messageCode = "");
	BOOL GetShiftKeyState();
	void SortStringArray (CStringArray &array);
	CString GetElementTypeAsText(int elementType);
	CString GetDataTypeAsText(int dataType);
	CString Soundex(const CString &string);
	
	CUtilityHelper();
	virtual ~CUtilityHelper();

	
	void ZoomByHandle();
	

	void ValidateFacility();
	void ListHandles();
	void UserQuery();
	
	void DumpFacilityTree();
	
	void CleanFacility();
	
	void BuildArrayofStrings(CSsaStringArray &tempArray, CString & buf);
	CString BuildStringBuf(CSsaStringArray & bufArray);

	static int CompareCStrings(const void *p1, const void *p2);
	static int CompareStrings(const void **p1, const void **p2);
	BOOL IsFloat(CString str);
	BOOL IsInteger(CString str);
	BOOL IsNumeric(CString str);
	int ParseString(const CString &string, const CString &delimiter, CStringArray &strings);
	void BuildDelimitedString(const CStringArray &fields, CString &string, const CString &delimiter, 
		int numFields = 0);
	CString ParseField(CString field, int fieldNumber, CString delimiter);
	void ProcessError(CString displayMsg, void *exception=NULL);
	BOOL PeekAndPump(int sleepTime = 1000);
	void ShowLastError();
	void SortStringArray(CSsaStringArray &array);
	void DumpArray(CStringArray &array);
	int FindStringInArray(const CString &string, CStringArray &array);
	
private:
	void DumpTree(CString file, TreeElement &tree);
	
};

#endif // !defined(AFX_UTILITYHELPER_H__9CA58F97_885F_42A0_B1EA_0BD2427F3473__INCLUDED_)

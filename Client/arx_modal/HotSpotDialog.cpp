// HotSpotDialog.cpp : implementation file
//

#include "stdafx.h"
#include "HotSpotDialog.h"
#include "HelpService.h"
#include "TreeElement.h"
#include "Hotspot.h"
#include "BTreeHelper.h"

#ifdef _DEBUG
#define new DEBUG_NEW
#undef THIS_FILE
static char THIS_FILE[] = __FILE__;
#endif


extern CHelpService helpService;
extern TreeElement changesTree;
extern CBTreeHelper bTreeHelper;

/////////////////////////////////////////////////////////////////////////////
// CHotSpotDialog dialog


CHotSpotDialog::CHotSpotDialog(CWnd* pParent /*=NULL*/)
	: CDialog(CHotSpotDialog::IDD, pParent)
{
	//{{AFX_DATA_INIT(CHotSpotDialog)
	//}}AFX_DATA_INIT
}


void CHotSpotDialog::DoDataExchange(CDataExchange* pDX)
{
	CDialog::DoDataExchange(pDX);
	//{{AFX_DATA_MAP(CHotSpotDialog)
	DDX_Control(pDX, IDC_SECTION_LIST, m_SectionListCtrl);
	//}}AFX_DATA_MAP
}


BEGIN_MESSAGE_MAP(CHotSpotDialog, CDialog)
	//{{AFX_MSG_MAP(CHotSpotDialog)
	ON_BN_CLICKED(IDHELP, OnHelp)
	ON_WM_HELPINFO()
	//}}AFX_MSG_MAP
	ON_CBN_SELCHANGE(IDC_SECTION_LIST, OnCbnSelchangeSectionList)
END_MESSAGE_MAP()

/////////////////////////////////////////////////////////////////////////////
// CHotSpotDialog message handlers
BOOL CHotSpotDialog::OnInitDialog() 
{
	CDialog::OnInitDialog();
	
	CWaitCursor cwc;

	for (int i=0; i < changesTree.treeChildren.GetSize(); ++i) {
		qqhSLOTSection section;
		bTreeHelper.GetBtSection(changesTree.treeChildren[i].fileOffset, section);
		int nItem = m_SectionListCtrl.AddString(section.getDescription());
		m_SectionListCtrl.SetItemData(nItem, section.getDBID());
	}

	CRect r;
	m_SectionListCtrl.GetWindowRect(&r);
	m_SectionListCtrl.SetWindowPos(NULL, 0, 0, r.Width(), r.Height()*(m_SectionListCtrl.GetCount()+1),
		SWP_NOMOVE|SWP_NOZORDER);

	if (m_SectionListCtrl.GetCount() == 1)
		m_SectionListCtrl.SetCurSel(0);

	CButton *pButton = (CButton *)GetDlgItem(IDC_FORKLIFT);
	pButton->SetCheck(1);

	return TRUE;  // return TRUE unless you set the focus to a control
	              // EXCEPTION: OCX Property Pages should return FALSE
}


void CHotSpotDialog::OnOK() 
{
	int curSel = m_SectionListCtrl.GetCurSel();
	if (curSel < 0) {
		AfxMessageBox("Select the section that will use the hotspot.");
		m_SectionListCtrl.SetFocus();
		m_SectionListCtrl.ShowDropDown();
		return;
	}

	m_SectionDBId = m_SectionListCtrl.GetItemData(curSel);

	CButton *pButton = (CButton *)GetDlgItem(IDC_FORKLIFT);
	if (pButton->GetCheck())
		m_Type = CHotspot::hsPutaway;
	else
		m_Type = CHotspot::hsSelection;

	EndDialog(IDOK);
}


void CHotSpotDialog::OnHelp() 
{
	helpService.ShowScreenHelp(IDD);

}

BOOL CHotSpotDialog::OnHelpInfo(HELPINFO* pHelpInfo) 
{
	helpService.ShowFieldHelp(IDD, pHelpInfo->iCtrlId);
	return FALSE;
}

void CHotSpotDialog::OnCbnSelchangeSectionList()
{
	// TODO: Add your control notification handler code here
}

// ExportFacility.cpp : implementation file
//


#include "stdafx.h"
#include "modal.h"
#include "ExportFacility.h"
#include "ControlService.h"
#include "HelpService.h"
#include "UtilityHelper.h"
#include "ProcessingMessage.h"
extern CUtilityHelper utilityHelper;
extern CControlService controlService;


#ifdef _DEBUG
#define new DEBUG_NEW
#undef THIS_FILE
static char THIS_FILE[] = __FILE__;
#endif

/////////////////////////////////////////////////////////////////////////////
// CExportFacility dialog


CExportFacility::CExportFacility(CWnd* pParent /*=NULL*/)
	: CDialog(CExportFacility::IDD, pParent)
{
	EnableAutomation();

	//{{AFX_DATA_INIT(CExportFacility)
	//}}AFX_DATA_INIT
}


void CExportFacility::OnFinalRelease()
{
	// When the last reference for an automation object is released
	// OnFinalRelease is called.  The base class will automatically
	// deletes the object.  Add additional cleanup required for your
	// object before calling the base class.

	CDialog::OnFinalRelease();
}

void CExportFacility::DoDataExchange(CDataExchange* pDX)
{
	CDialog::DoDataExchange(pDX);
	//{{AFX_DATA_MAP(CExportFacility)
	DDX_Control(pDX, IDC_DATABASE_LIST, m_DatabaseDropDown);
	//}}AFX_DATA_MAP
}


BEGIN_MESSAGE_MAP(CExportFacility, CDialog)
	//{{AFX_MSG_MAP(CExportFacility)
	ON_BN_CLICKED(ID_EXPORT, OnExport)
	//}}AFX_MSG_MAP
END_MESSAGE_MAP()

BEGIN_DISPATCH_MAP(CExportFacility, CDialog)
	//{{AFX_DISPATCH_MAP(CExportFacility)
		// NOTE - the ClassWizard will add and remove mapping macros here.
	//}}AFX_DISPATCH_MAP
END_DISPATCH_MAP()

// Note: we add support for IID_IExportFacility to support typesafe binding
//  from VBA.  This IID must match the GUID that is attached to the 
//  dispinterface in the .ODL file.

// {25CD6BCA-5D4A-4455-B8B4-FCD26F252D6C}
static const IID IID_IExportFacility =
{ 0x25cd6bca, 0x5d4a, 0x4455, { 0xb8, 0xb4, 0xfc, 0xd2, 0x6f, 0x25, 0x2d, 0x6c } };

BEGIN_INTERFACE_MAP(CExportFacility, CDialog)
	INTERFACE_PART(CExportFacility, IID_IExportFacility, Dispatch)
END_INTERFACE_MAP()

/////////////////////////////////////////////////////////////////////////////
// CExportFacility message handlers

BOOL CExportFacility::OnInitDialog()
{
	CDialog::OnInitDialog();

	GetDataBaseList();
	
	for (int i = 0; i < databaseList.GetSize(); i++) {
		int nItem = m_DatabaseDropDown.AddString(databaseList[i]);
		m_DatabaseDropDown.SetItemData(nItem, i);
	}

	CRect r;
	m_DatabaseDropDown.GetWindowRect(&r);
	m_DatabaseDropDown.SetWindowPos(NULL, 0, 0, r.Width(), 
		r.Height()*(m_DatabaseDropDown.GetCount()+1), SWP_NOMOVE|SWP_NOZORDER);

	UpdateData(FALSE);

	m_DatabaseDropDown.SetCurSel(0);
	
	return TRUE;
}

void CExportFacility::GetDataBaseList()
{
	
	CString m_ReturnValue, m_DataValue;
	CString tempString;
	CStringArray parseResults;
	int i = 0;
	
	m_DataValue.Format("DB%d",i);
	m_ReturnValue = controlService.GetApplicationData(m_DataValue, "");
	
	if (m_ReturnValue != "")
		do  {
			utilityHelper.ParseString(m_ReturnValue, "|", parseResults);
			databaseList.Add(parseResults[0]);
			tempString = parseResults[1];
			tempString.Remove('@');
			sidList.Add(tempString);
			i += 1;
			m_DataValue.Format("DB%d",i);
			m_ReturnValue = controlService.GetApplicationData(m_DataValue, "");
		}while(m_ReturnValue != "");

}

void CExportFacility::OnExport() 
{
	CString system_command;
	CString fileName;

	int curSel = m_DatabaseDropDown.GetCurSel();
	if (curSel < 0)
		return;

	CFileDialog dlgFile(FALSE);
	CString title;
	CString strFilter;
	CString strDefault;

	CString allFilter;
	VERIFY(allFilter.LoadString(AFX_IDS_ALLFILTER));
	strFilter += CString("Export Files (*.dmp)");
	strFilter += (TCHAR)'\0';   
	strFilter += _T("*.dmp");
	strFilter += (TCHAR)'\0';
	strFilter += allFilter;
	strFilter += (TCHAR)'\0';   
	strFilter += _T("*.*");
	strFilter += (TCHAR)'\0';   

	dlgFile.m_ofn.lpstrFilter = strFilter;
	dlgFile.m_ofn.lpstrDefExt = "dmp";
	dlgFile.m_ofn.lpstrTitle = "Database Export File";
	dlgFile.m_ofn.lpstrFile = fileName.GetBuffer(_MAX_PATH);
	//dlgFile.m_ofn.Flags &= ~OFN_OVERWRITEPROMPT;
	BOOL bResult = dlgFile.DoModal() == IDOK ? TRUE : FALSE;
	fileName.ReleaseBuffer();
	
	if (! bResult)
		return;

	//Needed for GetShortPathName to work properly.. file has to exist.. 
	FILE *f = fopen(fileName, "w");
	fclose(f);
	
	// exp.exe won't take long pathnames.. so GetShortPathName is required here.
	char shortFile[2048];
	GetShortPathName(fileName,shortFile,2048);
	CString shortfileName(shortFile);
	int errorCode = GetLastError();

	// MFS Jan 20, 2006 Updated for Oracle 10g
	// * Changed exp80.exe to exp.exe
	// * Changed to read path from Optimize registry entry
	CString dbase_sid = sidList[m_DatabaseDropDown.GetItemData(curSel)];
	CString oraclePath = controlService.GetRegistryData("Software\\SSA Global\\Optimize", "ORACLE_HOME");
	CString exportPath;
	exportPath = oraclePath + "\\bin\\exp.exe";

	HANDLE testHandle = CreateFile(exportPath, GENERIC_READ, 0, NULL, OPEN_EXISTING, 0, NULL);
	if (testHandle == INVALID_HANDLE_VALUE) {
		exportPath = oraclePath + "\\bin\\exp80.exe";
		testHandle = CreateFile(exportPath, GENERIC_READ, 0, NULL, OPEN_EXISTING, 0, NULL);
		if (testHandle == INVALID_HANDLE_VALUE) {
			exportPath = oraclePath + "\\bin\\exp73.exe";
			testHandle = CreateFile(exportPath, GENERIC_READ, 0, NULL, OPEN_EXISTING, 0, NULL);
		}
	}

	if (testHandle == INVALID_HANDLE_VALUE) {
		CString notFound;
		notFound = "Unable to find any version of the Oracle export utility (exp.exe) in directory '" +
			oraclePath.Trim() + "'.\nPlease check the Optimize setting for ORACLE_HOME in the Windows Registry.";
		AfxMessageBox(notFound);
		// AfxMessageBox("Unable to find the Oracle export utility.");  MFS Jan 23, 2006 Improved message
		return;
	}
	else
		CloseHandle(testHandle);


	system_command.Format("\"%s\" ssauser/opti30@%s file=\"%s\" owner=ssauser",
		exportPath, dbase_sid, shortfileName);


	//creates a new process to avoid having the DOS Window pop up
	PROCESS_INFORMATION ProcInfo;
    STARTUPINFO Info;

    memset(&Info, 0, sizeof(STARTUPINFO));
    Info.cb = sizeof(STARTUPINFO);
    Info.dwFillAttribute;
    Info.dwFlags = STARTF_USESHOWWINDOW | STARTF_USEPOSITION | STARTF_USESIZE;
    Info.wShowWindow = SW_HIDE;

	//Create Process expects a string pointer, not a CString
	char system_command_char[2048];

	lstrcpy(system_command_char, system_command);

	BOOL returnBOOL;

	CProcessingMessage dlg("Exporting Database...", this);

    returnBOOL = CreateProcess(NULL, system_command_char, NULL, NULL, FALSE, CREATE_DEFAULT_ERROR_MODE, NULL, NULL, &Info, &ProcInfo);

	//check for any errors
	if (!returnBOOL) {
		utilityHelper.ShowLastError();
		return;
	}

	//continue to update the autocad screen while the process is running
	while (1) {
		utilityHelper.PeekAndPump(500);
		if (WaitForSingleObject(ProcInfo.hProcess, 500) != WAIT_TIMEOUT)
			break;
	}

	/* MFS Jan 23, 2006 Let the user do his own zipping, it's 2006.
	CStringArray parseResults;
	CString zipfileName;

	utilityHelper.ParseString(fileName, ".", parseResults);

	zipfileName = parseResults[0];
	zipfileName += ".zip";
	f = fopen(zipfileName, "w");
	fclose(f);

	char shortZipName[2048];
	GetShortPathName(zipfileName, shortZipName, 2048);

	DeleteFile(shortZipName);

	//system_command.Format("pkzip %s -R \"c:\\progra~1\\exetec~1\\optimize\\facility\\*.*\" \"%s\"",
	//	zipfileName, shortfileName);
	
	CString facilityDir;
	facilityDir.Format("%s\\facility\\%s", controlService.m_ClientHome, controlService.m_CurrentDatabase);
	char shortFacDir[2048];
	GetShortPathName(facilityDir,shortFacDir,2048);
	
	system_command.Format("%s\\bin\\pkzip %s -R %s\\*.dwg", 
		controlService.m_ClientHome, shortZipName, shortFacDir);
	//creates a new process to avoid having the DOS Window pop up

    memset(&Info, 0, sizeof(STARTUPINFO));
    Info.cb = sizeof(STARTUPINFO);
    Info.dwFillAttribute;
    Info.dwFlags = STARTF_USESHOWWINDOW | STARTF_USEPOSITION | STARTF_USESIZE;
    Info.wShowWindow = SW_HIDE;

	//Create Process expects a string pointer, not a CString

	lstrcpy(system_command_char, system_command);

	CProcessingMessage dlg1("Compressing Files...", this);

    returnBOOL = CreateProcess(NULL, system_command_char, NULL, NULL, FALSE, CREATE_DEFAULT_ERROR_MODE, NULL, NULL, &Info, &ProcInfo);

	//check for any errors
	if (!returnBOOL) {
		utilityHelper.ShowLastError();
		return;
	}

	//continue to update the autocad screen while the process is running
	while (1) {
		utilityHelper.PeekAndPump(500);
		if (WaitForSingleObject(ProcInfo.hProcess, 500) != WAIT_TIMEOUT)
			break;
	}

	system_command.Format("%s\\bin\\pkzip %s -a %s", 
		controlService.m_ClientHome, shortZipName, shortfileName);
	//creates a new process to avoid having the DOS Window pop up

    memset(&Info, 0, sizeof(STARTUPINFO));
    Info.cb = sizeof(STARTUPINFO);
    Info.dwFillAttribute;
    Info.dwFlags = STARTF_USESHOWWINDOW | STARTF_USEPOSITION | STARTF_USESIZE;
    Info.wShowWindow = SW_HIDE;

	//Create Process expects a string pointer, not a CString

	lstrcpy(system_command_char, system_command);

    returnBOOL = CreateProcess(NULL, system_command_char, NULL, NULL, FALSE, CREATE_DEFAULT_ERROR_MODE, NULL, NULL, &Info, &ProcInfo);

	//check for any errors
	if (!returnBOOL) {
		utilityHelper.ShowLastError();
		return;
	}

	//continue to update the autocad screen while the process is running
	while (1) {
		utilityHelper.PeekAndPump(500);
		if (WaitForSingleObject(ProcInfo.hProcess, 500) != WAIT_TIMEOUT)
			break;
	}
	MFS Jan 23, 2006, End */

	CString tmp;
	// MFS Jan 23, 2006, Use DMP file name, no longer zipping file.
	// tmp.Format("The database was successfully exported to: \n%s", zipfileName);
	tmp.Format("The database was successfully exported to: \n%s", fileName);
	AfxMessageBox(tmp);

	CDialog::OnOK();
}

// Assignment.cpp: implementation of the CAssignment class.
//
//////////////////////////////////////////////////////////////////////

#include "stdafx.h"
#include "modal.h"
#include "Assignment.h"

#ifdef _DEBUG
#undef THIS_FILE
static char THIS_FILE[]=__FILE__;
#define new DEBUG_NEW
#endif

//////////////////////////////////////////////////////////////////////
// Construction/Destruction
//////////////////////////////////////////////////////////////////////

CAssignment::CAssignment()
{
	m_ProductKey = 0;
	m_FromLocationKey = 0;
	m_ToLocationKey = 0;
	m_CaseQuantity = 0;
	m_IsFromTemp = m_IsToTemp = 0;
	m_IsAddFacing = m_IsDeleteFacing = 0;
	m_WMSId = "";
	m_WMSDetailId = "";
}

CAssignment::~CAssignment()
{

}


CAssignment::CAssignment(const CAssignment &other)
{
	m_ProductKey = other.m_ProductKey;
	m_FromLocationKey = other.m_FromLocationKey;
	m_ToLocationKey = other.m_ToLocationKey;
	m_IsFromTemp = other.m_IsFromTemp;
	m_IsToTemp = other.m_IsToTemp;
	m_IsAddFacing = other.m_IsAddFacing;
	m_IsDeleteFacing = other.m_IsDeleteFacing;
	m_WMSId = other.m_WMSId;
	m_WMSDetailId = other.m_WMSDetailId;
	m_CaseQuantity = other.m_CaseQuantity;

}


CAssignment& CAssignment::operator=(const CAssignment &other)
{
	m_ProductKey = other.m_ProductKey;
	m_FromLocationKey = other.m_FromLocationKey;
	m_ToLocationKey = other.m_ToLocationKey;
	m_IsFromTemp = other.m_IsFromTemp;
	m_IsToTemp = other.m_IsToTemp;
	m_IsAddFacing = other.m_IsAddFacing;
	m_IsDeleteFacing = other.m_IsDeleteFacing;
	m_WMSId = other.m_WMSId;
	m_WMSDetailId = other.m_WMSDetailId;
	m_CaseQuantity = other.m_CaseQuantity;

	return *this;
}


BOOL CAssignment::operator==(const CAssignment &other)
{

	if (m_ProductKey != other.m_ProductKey) return FALSE;
	if (m_FromLocationKey != other.m_FromLocationKey) return FALSE;
	if (m_ToLocationKey != other.m_ToLocationKey) return FALSE;
	if (m_IsFromTemp != other.m_IsFromTemp) return FALSE;
	if (m_IsToTemp != other.m_IsToTemp) return FALSE;
	if (m_IsAddFacing != other.m_IsAddFacing) return FALSE;
	if (m_IsDeleteFacing != other.m_IsDeleteFacing) return FALSE;	
	if (m_WMSId != other.m_WMSId) return FALSE;
	if (m_WMSDetailId != other.m_WMSDetailId) return FALSE;
	if (m_CaseQuantity != other.m_CaseQuantity) return FALSE;

	return TRUE;

}


CString CAssignment::Stream()
{
	CString temp;

	temp.Format("%d|%d|%d|%d|%d|%d|%d|%s|%s|%d|",
		m_ProductKey, m_FromLocationKey, m_ToLocationKey,
		m_IsFromTemp, m_IsToTemp, m_IsAddFacing, m_IsDeleteFacing,
		m_WMSId, m_WMSDetailId, m_CaseQuantity);

	return temp;

}
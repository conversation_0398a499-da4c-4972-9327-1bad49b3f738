#if !defined(AFX_PICKPATHPROPERTIESDIALOG_H__23412B52_0521_11D2_9BD3_0080C781D9DF__INCLUDED_)
#define AFX_PICKPATHPROPERTIESDIALOG_H__23412B52_0521_11D2_9BD3_0080C781D9DF__INCLUDED_

#if _MSC_VER >= 1000
#pragma once
#endif // _MSC_VER >= 1000
// PickPathPropertiesDialog.h : header file
//

/////////////////////////////////////////////////////////////////////////////
// CPickPathPropertiesDialog dialog

class CPickPathPropertiesDialog : public CDialog
{
// Construction
public:
	BOOL m_Updateable;
	BOOL m_RLocSchemeBoxEnabled;
	BOOL m_RLevelSchemeBoxEnabled;
	BOOL m_LLocSchemeBoxEnabled;
	BOOL m_LLevelSchemeBoxEnabled;
	BOOL m_RBaySchemeBoxEnabled;
	BOOL m_LBaySchemeBoxEnabled;
	CPickPathPropertiesDialog(CWnd* pParent = NULL);   // standard constructor

// Dialog Data
	//{{AFX_DATA(CPickPathPropertiesDialog)
	enum { IDD = IDD_PICKPATH_PROPERTIES_DIALOG };
	CEdit	m_RLocStepBox;
	CEdit	m_RLevelStepBox;
	CEdit	m_RBayStepBox;
	CEdit	m_LLocStepBox;
	CEdit	m_LBayStepBox;
	CEdit	m_LLevelStepBox;
	CEdit	m_RLocStart_Box;
	CEdit	m_RLevelStart_Box;
	CEdit	m_RBayStart_Box;
	CEdit	m_LLocStart_Box;
	CEdit	m_LLevelStart_Box;
	CEdit	m_LBayStart_Box;
	CComboBox	m_RLocShemeBox;
	CComboBox	m_RLocBreakBox;
	CComboBox	m_RLevelSchemeBox;
	CComboBox	m_RBaySchemeBox;
	CComboBox	m_LLocSchemeBox;
	CComboBox	m_LLocBreakBox;
	CComboBox	m_LLevelSchemeBox;
	CComboBox	m_LBaySchemeBox;
	CEdit	m_PickPathProperties_PatternNum_Box;
	CComboBox	m_PickPathProperties_PathType_List;
	CButton	m_PickPathProperties_Ok;
	CButton	m_PickPathProperties_Help;
	CButton	m_PickPathProperites_Cancel;
	int		m_PickPathProperties_PathType_Val;
	int		m_PickPathProperties_PatternNum;
	int		m_LBaySchemeVal;
	CString	m_LBayStartVal;
	BOOL	m_LLevelBreak;
	int		m_LLevelSchemeVal;
	CString	m_LLevelStart;
	int		m_LLocBreakVal;
	int		m_LLocSchemeVal;
	CString	m_LLocStart;
	int		m_RBaySchemeVal;
	CString	m_RBayStart;
	BOOL	m_RLevelBreak;
	int		m_RLevelSchemeVal;
	CString	m_RLevelStart;
	int		m_RLocBreakVal;
	int		m_RLocSchemeVal;
	CString	m_RLocStart;
	int		m_LBayStepVal;
	int		m_LLevelStep;
	int		m_LLocStep;
	int		m_RBayStep;
	int		m_RLevelStep;
	int		m_RLocStep;
	CString	m_LBayPatternVal;
	CString	m_LLevelPatternVal;
	CString	m_LLocPatternVal;
	CString	m_RBayPatternVal;
	CString	m_RLevelPatternVal;
	CString	m_RLocPatternVal;
	//}}AFX_DATA


// Overrides
	// ClassWizard generated virtual function overrides
	//{{AFX_VIRTUAL(CPickPathPropertiesDialog)
	protected:
	virtual void DoDataExchange(CDataExchange* pDX);    // DDX/DDV support
	//}}AFX_VIRTUAL

// Implementation
protected:

	// Generated message map functions
	//{{AFX_MSG(CPickPathPropertiesDialog)
	afx_msg void OnHelp();
	afx_msg void OnOK();
	afx_msg void OnSelchangePickPathPropertiesPathtype();
	virtual BOOL OnInitDialog();
	afx_msg void OnPickpathPropertiesGroup();
	afx_msg void OnKillfocusLbaystartval();
	afx_msg void OnKillfocusLlevelstartval();
	afx_msg void OnKillfocusLlocstartval();
	afx_msg void OnKillfocusRbaystartval();
	afx_msg void OnKillfocusRlevelstartval();
	afx_msg void OnKillfocusRlocstartval();
	afx_msg void OnKillfocusLbaystepval();
	afx_msg void OnKillfocusLlevelstepval();
	afx_msg void OnKillfocusLlocstepval();
	afx_msg void OnKillfocusRbaystepval();
	afx_msg void OnKillfocusRlevelstepval();
	afx_msg void OnKillfocusRlocstepval();
	afx_msg BOOL OnHelpInfo(HELPINFO* pHelpInfo);
	afx_msg void OnKillfocusLbaypattern();
	afx_msg void OnKillfocusLlevelpattern();
	afx_msg void OnKillfocusLlocpattern();
	afx_msg void OnKillfocusRbaypattern();
	afx_msg void OnKillfocusRlevelpattern();
	afx_msg void OnKillfocusRlocpattern();
	virtual void OnCancel();
	//}}AFX_MSG
	DECLARE_MESSAGE_MAP()
};

//{{AFX_INSERT_LOCATION}}
// Microsoft Developer Studio will insert additional declarations immediately before the previous line.

#endif // !defined(AFX_PICKPATHPROPERTIESDIALOG_H__23412B52_0521_11D2_9BD3_0080C781D9DF__INCLUDED_)

#if !defined(AFX_NEWSECTIONPAGE2_H__3FAACE12_5779_11D3_AFD6_0080C79D254D__INCLUDED_)
#define AFX_NEWSECTIONPAGE2_H__3FAACE12_5779_11D3_AFD6_0080C79D254D__INCLUDED_

#if _MSC_VER >= 1000
#pragma once
#endif // _MSC_VER >= 1000
// NewSectionPage2.h : header file
//

/////////////////////////////////////////////////////////////////////////////
// CNewSectionPage2 dialog

class CNewSectionPage2 : public CPropertyPage
{
	DECLARE_DYNCREATE(CNewSectionPage2)

// Construction
public:
	CNewSectionPage2();
	~CNewSectionPage2();

// Dialog Data
	//{{AFX_DATA(CNewSectionPage2)
	enum { IDD = IDD_SECTION_PAGE2 };
	CEdit	m_NewSectionAvgCubePerTrip_Box;
	CEdit	m_NewSectionStockerVar_Box;
	CEdit	m_NewSectionStockerLaborRate_Box;
	CEdit	m_NewSectionStockerFixed_Box;
	CEdit	m_NewSectionPalletsPerTrip_Box;
	CEdit	m_NewSectionForkPickup_Box;
	CEdit	m_NewSectionForkInsertion_Box;
	CEdit	m_NewSectionSelVarFactor_Box;
	CEdit	m_NewSectionSelLabor_Box;
	CEdit	m_NewSectionFixedFactor_Box;
	CEdit	m_NewSectionReplAvgDist_Box;
	CEdit	m_NewSectionForkLabor_Box;
	CEdit	m_NewSectionDistVar_Box;
	CEdit	m_NewSectionDistFixed_Box;
	float	m_NewSection_ForkDistFixed;
	float	m_NewSection_ForkDistVar;
	float	m_NewSection_ForkLaborRate;
	float	m_NewSection_ReplAvgDist;
	float	m_NewSection_SelFixedFactor;
	float	m_NewSection_SelLaborRate;
	float	m_NewSection_SelVarFactor;
	float	m_NewSection_ForkInsertion;
	float	m_NewSection_ForkPickup;
	float	m_NewSection_PalletsPerTrip;
	float	m_NewSection_StockerFixed;
	float	m_NewSection_StockerLaborRate;
	float	m_NewSection_StockerVar;
	float	m_NewSection_AvgCubePerTrip;
	//}}AFX_DATA


// Overrides
	// ClassWizard generate virtual function overrides
	//{{AFX_VIRTUAL(CNewSectionPage2)
	protected:
	virtual void DoDataExchange(CDataExchange* pDX);    // DDX/DDV support
	virtual BOOL OnNotify(WPARAM wParam, LPARAM lParam, LRESULT* pResult);
	//}}AFX_VIRTUAL

// Implementation
protected:
	// Generated message map functions
	//{{AFX_MSG(CNewSectionPage2)
	afx_msg void OnKillfocusNewsectionForkdistfixed();
	afx_msg void OnKillfocusNewsectionForkdistvar();
	afx_msg void OnKillfocusNewsectionForkinsertion();
	afx_msg void OnKillfocusNewsectionForklaborrate();
	afx_msg void OnKillfocusNewsectionForkpickup();
	afx_msg void OnKillfocusNewsectionPalletsPerTrip();
	afx_msg void OnKillfocusNewsectionReplavgdist();
	afx_msg void OnKillfocusNewsectionSelfixedfactor();
	afx_msg void OnKillfocusNewsectionSellaborrate();
	afx_msg void OnKillfocusNewsectionSelvarfactor();
	afx_msg void OnKillfocusNewsectionStockerfixed();
	afx_msg void OnKillfocusNewsectionStockerlaborrate();
	afx_msg void OnKillfocusNewsectionStockervar();
	afx_msg BOOL OnHelpInfo(HELPINFO* pHelpInfo);
	virtual BOOL OnInitDialog();
	afx_msg void OnKillfocusNewsectionAvgcubepertrip();
	//}}AFX_MSG
	DECLARE_MESSAGE_MAP()

};

//{{AFX_INSERT_LOCATION}}
// Microsoft Developer Studio will insert additional declarations immediately before the previous line.

#endif // !defined(AFX_NEWSECTIONPAGE2_H__3FAACE12_5779_11D3_AFD6_0080C79D254D__INCLUDED_)

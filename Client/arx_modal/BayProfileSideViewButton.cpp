// BayProfileSideViewButton.cpp : implementation file
//

#include "stdafx.h"
#include "modal.h"
#include "BayProfileSideViewButton.h"
#include "Constants.h"

#ifdef _DEBUG
#define new DEBUG_NEW
#undef THIS_FILE
static char THIS_FILE[] = __FILE__;
#endif

#define ARROWLEN 4

/////////////////////////////////////////////////////////////////////////////
// CBayProfileSideViewButton

CBayProfileSideViewButton::CBayProfileSideViewButton()
{
}

CBayProfileSideViewButton::~CBayProfileSideViewButton()
{
}


BEGIN_MESSAGE_MAP(CBayProfileSideViewButton, CButton)
	//{{AFX_MSG_MAP(CBayProfileSideViewButton)
		// NOTE - the ClassWizard will add and remove mapping macros here.
	//}}AFX_MSG_MAP
END_MESSAGE_MAP()

/////////////////////////////////////////////////////////////////////////////
// CBayProfileSideViewButton message handlers

void CBayProfileSideViewButton::DrawItem(LPDRAWITEMSTRUCT lpDrawItemStruct) 
{
	UINT uStyle = DFCS_BUTTONPUSH;
	
	// This code only works with buttons.
	ASSERT(lpDrawItemStruct->CtlType == ODT_BUTTON);
	
	
	// If drawing selected, add the pushed style to DrawFrameControl.
	if (lpDrawItemStruct->itemState & ODS_SELECTED)
		uStyle |= DFCS_PUSHED;
	
	// Draw the button frame.
	::DrawFrameControl(lpDrawItemStruct->hDC, &lpDrawItemStruct->rcItem, 
		DFC_BUTTON, uStyle);
	
	// Get the button's text.
	CString strText;
	GetWindowText(strText);
	
	// Draw the button text using the text color red.
	COLORREF crOldColor = ::SetTextColor(lpDrawItemStruct->hDC, RGB(255,0,0));
	::DrawText(lpDrawItemStruct->hDC, strText, strText.GetLength(), 
		&lpDrawItemStruct->rcItem, DT_SINGLELINE|DT_VCENTER|DT_CENTER);
	::SetTextColor(lpDrawItemStruct->hDC, crOldColor);
	
	CRect r;
	GetClientRect(&r);

	CDC cdc;
	cdc.Attach(lpDrawItemStruct->hDC);
	cdc.SetMapMode(MM_ANISOTROPIC);
	cdc.SetViewportOrg(r.left, r.bottom);
	cdc.SetWindowExt(1, 1);
	cdc.SetViewportExt(1, -1);
	
	switch (m_DimensionInfo.m_BayType) {
	case BAYTYPE_BIN:
		DrawBin(cdc);
		break;
	case BAYTYPE_PALLET:
		DrawPallet(cdc);
		break;
	case BAYTYPE_CASEFLOW:
	case BAYTYPE_PALLETFLOW:
		DrawFlow(cdc);
		break;
	case BAYTYPE_FLOOR:
		DrawFloor(cdc);
		break;
	case BAYTYPE_DRIVEIN:
		DrawDriveIn(cdc);
		break;
	}

	// Draw information lines
	CPen pen, *prevPen;
	CFont font, *pOldFont;

	try {
		if (font.CreateFont(12, 0,0,0,FW_NORMAL, 0,0,0, ANSI_CHARSET, OUT_DEFAULT_PRECIS,
			CLIP_DEFAULT_PRECIS, DEFAULT_QUALITY, DEFAULT_PITCH|FF_DONTCARE, "Arial")) {			
			pOldFont = cdc.SelectObject(&font);
		}
		else {
			return;
		}
	}
	catch (...) {
		return;
	}

	pen.CreatePen(PS_SOLID, 1, RGB(0,0,0));
	prevPen = cdc.SelectObject(&pen);
	CSize sz = cdc.GetTextExtent("Depth");
	cdc.TextOut(3, 10+sz.cy/2, "Depth");

	cdc.MoveTo(sz.cx+5, 10);
	cdc.LineTo(sz.cx+5+r.Width()/8, 10);
	cdc.LineTo(cdc.GetCurrentPosition().x-ARROWLEN, cdc.GetCurrentPosition().y-ARROWLEN);
	cdc.MoveTo(sz.cx+5+r.Width()/8, 10);
	cdc.LineTo(cdc.GetCurrentPosition().x-ARROWLEN, cdc.GetCurrentPosition().y+ARROWLEN);

	cdc.SelectObject(pOldFont);
	font.DeleteObject();

	try {
		if (font.CreateFont(12, 0, 2700, 2700, FW_NORMAL, 0,0,0, ANSI_CHARSET, OUT_DEFAULT_PRECIS,
			CLIP_DEFAULT_PRECIS, DEFAULT_QUALITY, DEFAULT_PITCH|FF_DONTCARE, "Arial")) {			
			pOldFont = cdc.SelectObject(&font);
		}
		else {
			return;
		}
	}
	catch (...) {
		return;
	}

	sz = cdc.GetTextExtent("Height");
	cdc.TextOut(7-sz.cy/2, r.Width()/8, "Height");

	cdc.MoveTo(7, r.Width()/8+sz.cx+2);
	cdc.LineTo(7, r.Width()/8+sz.cx+2+r.Width()/8);
	cdc.LineTo(cdc.GetCurrentPosition().x-ARROWLEN, cdc.GetCurrentPosition().y-ARROWLEN);
	cdc.MoveTo(7,  r.Width()/8+sz.cx+2+r.Width()/8);
	cdc.LineTo(cdc.GetCurrentPosition().x+ARROWLEN, cdc.GetCurrentPosition().y-ARROWLEN);

	cdc.SelectObject(prevPen);
	pen.DeleteObject();
	cdc.SelectObject(pOldFont);
	font.DeleteObject();	
	cdc.Detach();

	
}

void CBayProfileSideViewButton::DrawBin(CDC &cdc)
{
	CRect r;
	this->GetClientRect(&r);
	
	double ratio;
	if (m_DimensionInfo.m_BayHeight >= m_DimensionInfo.m_UprightHeight &&
		m_DimensionInfo.m_BayHeight > 0)
		ratio = m_DimensionInfo.m_UprightHeight/m_DimensionInfo.m_BayHeight;
	else
		ratio = 1;

	double len = r.Height()*.6;
	double urLen = len * ratio;

	CString temp;
	temp.Format("%.0f", m_DimensionInfo.m_UprightHeight);
	DrawVertDimLine(cdc, CPoint(r.Width()/4-15, r.Height()*.25), urLen, 1, temp);

	// Left upright
	DrawVertLine(cdc, CPoint(r.Width()/4, r.Height()*.25), urLen, 2, FALSE);
	// Right upright
	DrawVertLine(cdc, CPoint(r.Width()*3/4, r.Height()*.25), urLen, 2, FALSE);

	if (m_DimensionInfo.m_BayHeight <= m_DimensionInfo.m_UprightHeight)
		temp.Format("");
	else
		temp.Format("%.0f", m_DimensionInfo.m_BayHeight-m_DimensionInfo.m_UprightHeight);
	DrawVertDimLine(cdc, CPoint(r.Width()*3/4+15, r.Height()*.25+urLen+2), len-urLen, 1, temp);

	// Left stackable height
	DrawVertLine(cdc, CPoint(r.Width()/4, r.Height()*.25+urLen), len-urLen, 2, TRUE);
	// Right stackable height
	DrawVertLine(cdc, CPoint(r.Width()*3/4, r.Height()*.25+urLen), len-urLen, 2, TRUE);

	// Crossbar
	//DrawHorzLine(cdc, CPoint(r.Width()/3, urLen), r.Width()/3, 2, FALSE);

	// Depth line
	temp.Format("%.0f", m_DimensionInfo.m_BayDepth);
	DrawHorzDimLine(cdc, CPoint(r.Width()/4+1, r.Height()*3/4+10), r.Width()/2-3, 1, temp);


}

void CBayProfileSideViewButton::DrawPallet(CDC &cdc)
{
	DrawBin(cdc);

	if (m_DimensionInfo.m_PositionsDeep <= 0)
		return;

	CRect r;
	this->GetClientRect(&r);

	CRect boxRect;
	double rDepth = (double)(r.Width()/2)/(double)m_DimensionInfo.m_PositionsDeep;
	double rHeight = r.Height()/4;
	boxRect.bottom = r.Height()/4+1;
	boxRect.top = boxRect.bottom+rHeight;
	double startLeft = r.Width()/4;

	for (int i=0; i < m_DimensionInfo.m_PositionsDeep; ++i) {
		boxRect.left = startLeft + i*(rDepth);
		boxRect.right = boxRect.left + rDepth;

		DrawBox(cdc, boxRect, 1, TRUE);
	}
}


void CBayProfileSideViewButton::DrawFlow(CDC &cdc)
{
	DrawBin(cdc);

	CRect r;
	this->GetClientRect(&r);
	

	double ratio;
	if (m_DimensionInfo.m_BayHeight > 0)
		ratio = (r.Height()*.6)/m_DimensionInfo.m_BayHeight;
	else
		return;

	double startX = r.Width()*3/4;
	double startY = r.Height()*.25 + r.Height()*.6/2;

	double finishX = r.Width()/4;
	double finishY = startY - m_DimensionInfo.m_FlowDifference * ratio;
	
	CPen pen, *prevPen;
	pen.CreatePen(PS_SOLID, 1, RGB(255,0,0));
	
	prevPen = cdc.SelectObject(&pen);
	
	cdc.MoveTo(startX, startY);
	cdc.LineTo(finishX, finishY);
	
	cdc.SelectObject(prevPen);
	pen.DeleteObject();

	CString temp;
	temp.Format("%.0f", m_DimensionInfo.m_FlowDifference);

	DrawVertDimLine(cdc, CPoint(startX+10, finishY), startY-finishY, 1, temp, 8);
}

void CBayProfileSideViewButton::DrawFloor(CDC &cdc)
{
	CRect r;
	this->GetClientRect(&r);

	double len = r.Height()*.6;

	CString temp;
	temp.Format("%.0f", m_DimensionInfo.m_BayHeight);
	DrawVertDimLine(cdc, CPoint(r.Width()/4-15, r.Height()*.25), len, 1, temp);

	// Depth line
	temp.Format("%.0f", m_DimensionInfo.m_BayDepth);
	DrawHorzDimLine(cdc, CPoint(r.Width()/4, r.Height()/4-10), r.Width()/2-1, 1, temp);


	if (m_DimensionInfo.m_BayHeight <= 0 ||
		(m_DimensionInfo.m_SelectPositionHeight <= 0 && m_DimensionInfo.m_ReservePositionHeight <= 0) ||
		m_DimensionInfo.m_BayDepth <= 0 ||
		m_DimensionInfo.m_StackDepth <= 0)
		return;

//	int positionsDeep = m_DimensionInfo.m_BayDepth / m_DimensionInfo.m_StackDepth;
	int positionsDeep = m_DimensionInfo.m_SelectPositions + m_DimensionInfo.m_ReservePositions;

	CRect boxRect;
//	double rDepth = (double)(r.Width()/2)/(double)positionsDeep;
	double locSpace;
	double stackDepth = m_DimensionInfo.m_StackDepth;
	if (positionsDeep == 0)
		return;

	if (m_DimensionInfo.m_BayDepth/positionsDeep < stackDepth)
		stackDepth = m_DimensionInfo.m_BayDepth/positionsDeep;

	locSpace = (m_DimensionInfo.m_BayDepth - (positionsDeep * stackDepth))/
		(positionsDeep+1);

	double rDepth = (double)(r.Width()/2)/(m_DimensionInfo.m_BayDepth/stackDepth);
	double rSpace = (double)((r.Width()/2)- rDepth*positionsDeep)/(positionsDeep+1);

	double rHeight = r.Height()/4;
	boxRect.bottom = r.Height()/4+1;
	boxRect.top = boxRect.bottom+rHeight;
	double startLeft = r.Width()/4;
	
	double selHgt = m_DimensionInfo.m_SelectPositionHeight;
	double rsvHgt = m_DimensionInfo.m_ReservePositionHeight;

	if (selHgt > m_DimensionInfo.m_BayHeight)
		selHgt = m_DimensionInfo.m_BayHeight;

	if (rsvHgt > m_DimensionInfo.m_BayHeight)
		rsvHgt = m_DimensionInfo.m_BayHeight;

	double bayHgt = r.Height() * .6;
	selHgt = (selHgt/m_DimensionInfo.m_BayHeight)*bayHgt;
	rsvHgt = (rsvHgt/m_DimensionInfo.m_BayHeight)*bayHgt;

	int numSelect = m_DimensionInfo.m_SelectPositions;

	for (int i=0; i < positionsDeep; ++i) {
		boxRect.left = startLeft + i*(rDepth) + (i+1)*rSpace;
		boxRect.right = boxRect.left + rDepth;

		if (numSelect > 0) {
			boxRect.top = boxRect.bottom + selHgt;
			DrawBoxWithText(cdc, boxRect, 1, TRUE, "S", 12);
			numSelect--;
		}
		else {
			boxRect.top = boxRect.bottom + rsvHgt;
			DrawBoxWithText(cdc, boxRect, 1, TRUE, "R", 12);
		}

	}

	temp.Format("%.0f", m_DimensionInfo.m_StackDepth);
	DrawHorzDimLine(cdc, CPoint(boxRect.left, boxRect.top+10), boxRect.Width(), 1, temp);


}

void CBayProfileSideViewButton::DrawDriveIn(CDC &cdc)
{
	CRect r;
	this->GetClientRect(&r);
	
	double ratio;
	if (m_DimensionInfo.m_BayHeight >= m_DimensionInfo.m_UprightHeight &&
		m_DimensionInfo.m_BayHeight > 0)
		ratio = m_DimensionInfo.m_UprightHeight/m_DimensionInfo.m_BayHeight;
	else
		ratio = 1;

	double len = r.Height()*.6;
	double urLen = len * ratio;

	CString temp;
	temp.Format("%.0f", m_DimensionInfo.m_UprightHeight);
	DrawVertDimLine(cdc, CPoint(r.Width()/4-15, r.Height()*.25), urLen, 1, temp);

	// Left upright
	DrawVertLine(cdc, CPoint(r.Width()/4, r.Height()*.25), urLen, 2, FALSE);
	// Left stackable height
	DrawVertLine(cdc, CPoint(r.Width()*3/4, r.Height()*.25), urLen, 2, FALSE);

	if (m_DimensionInfo.m_BayHeight <= m_DimensionInfo.m_UprightHeight)
		temp.Format("");
	else
		temp.Format("%.0f", m_DimensionInfo.m_BayHeight-m_DimensionInfo.m_UprightHeight);
	DrawVertDimLine(cdc, CPoint(r.Width()*3/4+15, r.Height()*.25+urLen+2), len-urLen, 1, temp);

	// Right upright
	DrawVertLine(cdc, CPoint(r.Width()/4, r.Height()*.25+urLen), len-urLen, 2, TRUE);
	// Right stackable height
	DrawVertLine(cdc, CPoint(r.Width()*3/4, r.Height()*.25+urLen), len-urLen, 2, TRUE);

	// Depth line
	temp.Format("%.0f", m_DimensionInfo.m_BayDepth);
	DrawHorzDimLine(cdc, CPoint(r.Width()/4, r.Height()/4-10), r.Width()/2-1, 1, temp);


	// Now draw the positions

	if (m_DimensionInfo.m_BayHeight == 0 ||
		m_DimensionInfo.m_SelectPositionHeight == 0 ||
		m_DimensionInfo.m_BayDepth == 0 ||
		m_DimensionInfo.m_StackDepth == 0)
		return;

	int palletsHigh = m_DimensionInfo.m_BayHeight / 
		(m_DimensionInfo.m_SelectPositionHeight);
	int palletsDeep = (m_DimensionInfo.m_BayDepth+m_DimensionInfo.m_Overhang) /
		m_DimensionInfo.m_StackDepth;

	double bayBottom = r.Height()/4;
	double bayTop = bayBottom + r.Height()*.6;
	double height = bayTop - bayBottom;
	double boxHeight = height / palletsHigh; //+1


	double bayLeft = r.Width()/4;
	double bayRight = r.Width()*3/4;
	double width = bayRight - bayLeft;
	double boxDepth = width / (palletsDeep);
	int numSelect = m_DimensionInfo.m_SelectPositions;

	for (int i=0; i < palletsDeep; ++i) {
		for (int j=0; j < palletsHigh; ++j) {
			CRect boxRect;
			boxRect.bottom = bayBottom + boxHeight*j;
			boxRect.top = boxRect.bottom + boxHeight;
			boxRect.left = bayLeft + boxDepth*i;
			boxRect.right = boxRect.left + boxDepth;
			CString temp;
			if (numSelect > 0) {
				temp = "S";
				numSelect--;
			}
			else
				temp = "R";

			DrawBoxWithText(cdc, boxRect, 1, TRUE, temp, 12);
		}
	}

}

void CBayProfileSideViewButton::DrawVertLine(CDC &cdc, const CPoint &startPt, int len, int width, BOOL bDashed)
{
	CPen pen, *prevPen;

	if (bDashed)
		pen.CreatePen(PS_DOT, width, RGB(255,0,0));
	else
		pen.CreatePen(PS_SOLID, width, RGB(0,0,0));

	prevPen = cdc.SelectObject(&pen);
	cdc.MoveTo(startPt);
	cdc.LineTo(startPt.x, startPt.y+len);

	cdc.SelectObject(prevPen);
	pen.DeleteObject();	
}

void CBayProfileSideViewButton::DrawHorzLine(CDC &cdc, const CPoint &startPt, int len, int width, BOOL bDashed)
{
	CPen pen, *prevPen;

	if (bDashed)
		pen.CreatePen(PS_DOT, width, RGB(255,0,0));
	else
		pen.CreatePen(PS_SOLID, width, RGB(0,0,0));

	prevPen = cdc.SelectObject(&pen);
	cdc.MoveTo(startPt);
	cdc.LineTo(startPt.x+len, startPt.y);

	cdc.SelectObject(prevPen);
	pen.DeleteObject();	
}


void CBayProfileSideViewButton::DrawHorzDimLine(CDC &cdc, const CPoint &startPt, int len, int width, const CString &text,
												int textSize)
{
	CPen pen, *prevPen;
	CFont font, *pOldFont;

	try {
		
		if (font.CreateFont(textSize, 0,0,0,FW_BOLD, 0,0,0, ANSI_CHARSET, OUT_DEFAULT_PRECIS,
			CLIP_DEFAULT_PRECIS, DEFAULT_QUALITY, DEFAULT_PITCH|FF_DONTCARE, "Arial")) {			
			pOldFont = cdc.SelectObject(&font);
		}
		else {
			return;
		}
	}
	catch (...) {
		return;
	}
	CSize size = cdc.GetTextExtent(text);
	cdc.SetTextColor(RGB(0,0, 255));
	cdc.TextOut(startPt.x+len/2-size.cx/2, startPt.y+size.cy/2, text);
	cdc.SetTextColor(RGB(0,0, 0));
	
	if (size.cx < len) {
		pen.CreatePen(PS_SOLID, width, RGB(0,0,0));
		prevPen = cdc.SelectObject(&pen);
		
		// Left Arrow
		cdc.MoveTo(startPt);
		cdc.LineTo(startPt.x+ARROWLEN, startPt.y-ARROWLEN);
		cdc.MoveTo(startPt);
		cdc.LineTo(startPt.x+ARROWLEN, startPt.y+ARROWLEN);

		// Main line
		cdc.MoveTo(startPt);
		cdc.LineTo(startPt.x+len/2-size.cx/2-2, startPt.y);
		cdc.MoveTo(startPt.x+len/2+size.cx/2+1, startPt.y);
		cdc.LineTo(startPt.x+len, startPt.y);
		
		// Right Arrow
		cdc.LineTo((startPt.x+len)-ARROWLEN, startPt.y+ARROWLEN);
		cdc.MoveTo(startPt.x+len, startPt.y);
		cdc.LineTo((startPt.x+len)-ARROWLEN, startPt.y-ARROWLEN);
		
		cdc.MoveTo(startPt.x-1, startPt.y+ARROWLEN+1);
		cdc.LineTo(startPt.x-1, (startPt.y-ARROWLEN));
		
		cdc.MoveTo(startPt.x+len+1, startPt.y+ARROWLEN+1);
		cdc.LineTo(startPt.x+len+1, (startPt.y-ARROWLEN));
		
		
		cdc.SelectObject(prevPen);
		pen.DeleteObject();
	}
	cdc.SelectObject(pOldFont);
	font.DeleteObject();
}

void CBayProfileSideViewButton::DrawVertDimLine(CDC &cdc, const CPoint &startPt, int len, 
												int width, const CString &text, int textSize)
{
	CPen pen, *prevPen;
	CFont font, *pOldFont;

	try {
		
		if (font.CreateFont(textSize, 0,0,0,FW_BOLD, 0,0,0, ANSI_CHARSET, OUT_DEFAULT_PRECIS,
			CLIP_DEFAULT_PRECIS, DEFAULT_QUALITY, DEFAULT_PITCH|FF_DONTCARE, "Arial")) {			
			pOldFont = cdc.SelectObject(&font);
		}
		else {
			return;
		}
	}
	catch (...) {
		return;
	}

	CSize size = cdc.GetTextExtent(text);
	cdc.SetTextColor(RGB(0,0, 255));
	cdc.TextOut(startPt.x-size.cx/2, startPt.y+len/2+size.cy/2, text);
	cdc.SetTextColor(RGB(0,0, 0));
	
	if (size.cy < len) {
		pen.CreatePen(PS_SOLID, width, RGB(0,0,0));
		prevPen = cdc.SelectObject(&pen);
		
		cdc.MoveTo(startPt);
		cdc.LineTo(startPt.x-ARROWLEN, startPt.y+ARROWLEN);
		cdc.MoveTo(startPt);
		cdc.LineTo(startPt.x+ARROWLEN, startPt.y+ARROWLEN);
		cdc.MoveTo(startPt);
		
		cdc.LineTo(startPt.x, (startPt.y+len/2-size.cy/2-1));
		cdc.MoveTo(startPt.x, (startPt.y+len/2+size.cy/2+1));
		cdc.LineTo(startPt.x, startPt.y+len);
		
		cdc.LineTo(startPt.x-ARROWLEN, startPt.y+len-ARROWLEN);
		cdc.MoveTo(startPt.x, startPt.y+len);
		cdc.LineTo(startPt.x+ARROWLEN, startPt.y+len-ARROWLEN);
		
		cdc.MoveTo((startPt.x-ARROWLEN), startPt.y-1);
		cdc.LineTo(startPt.x+ARROWLEN+1, startPt.y-1);
		
		cdc.MoveTo((startPt.x-ARROWLEN), startPt.y+len+1);
		cdc.LineTo(startPt.x+ARROWLEN+1, startPt.y+len+1);
		
		
		cdc.SelectObject(prevPen);
		pen.DeleteObject();
	}
	cdc.SelectObject(pOldFont);
	font.DeleteObject();
}


void CBayProfileSideViewButton::DrawBoxWithText(CDC &cdc, const CRect& r, int width, BOOL bDashed,
												const CString &text, int textSize)
{
	CPen pen, *prevPen;
	CFont font, *pOldFont;
	
	try {
		
		if (font.CreateFont(textSize, 0,0,0,FW_BOLD, 0,0,0, ANSI_CHARSET, OUT_DEFAULT_PRECIS,
			CLIP_DEFAULT_PRECIS, DEFAULT_QUALITY, DEFAULT_PITCH|FF_DONTCARE, "Arial")) {			
			pOldFont = cdc.SelectObject(&font);
		}
		else {
			return;
		}
	}
	catch (...) {
		return;
	}
	
	
	CSize size = cdc.GetTextExtent(text);
	int hgt = r.Height();
	int wid = r.Width();
	if (size.cx < abs(r.Width()) && size.cy < abs(r.Height())) {
		cdc.SetTextColor(RGB(0,0, 255));
		cdc.TextOut(r.CenterPoint().x-size.cx/2, r.CenterPoint().y+size.cy/2, text);
		cdc.SetTextColor(RGB(0,0,0));
	}
	
	cdc.SelectObject(pOldFont);
	font.DeleteObject();
	
	if (bDashed)
		pen.CreatePen(PS_SOLID, width, RGB(255,0,0));
	else
		pen.CreatePen(PS_SOLID, width, RGB(0,0,0));
	
	prevPen = cdc.SelectObject(&pen);
	
	cdc.MoveTo(r.BottomRight());
	cdc.LineTo(r.right, r.top);
	cdc.LineTo(r.left, r.top);
	cdc.LineTo(r.left, r.bottom);
	cdc.LineTo(r.right, r.bottom);
	
	
	
	cdc.SelectObject(prevPen);
	pen.DeleteObject();	
}


void CBayProfileSideViewButton::DrawBox(CDC &cdc, const CRect& r, int width, BOOL bDashed)
{
	CPen pen, *prevPen;

	if (bDashed)
		pen.CreatePen(PS_DOT, width, RGB(255,0,0));
	else
		pen.CreatePen(PS_SOLID, width, RGB(0,0,0));

	prevPen = cdc.SelectObject(&pen);
	
	cdc.MoveTo(r.BottomRight());
	cdc.LineTo(r.right, r.top);
	cdc.LineTo(r.left, r.top);
	cdc.LineTo(r.left, r.bottom);
	cdc.LineTo(r.right, r.bottom);

	cdc.SelectObject(prevPen);
	pen.DeleteObject();	
}


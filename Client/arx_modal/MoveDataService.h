// MoveDataService.h: interface for the CMoveDataService class.
//
//////////////////////////////////////////////////////////////////////

#if !defined(AFX_MOVEDATASERVICE_H__41F7F3DC_C8B0_432D_B8E2_9897E65268A1__INCLUDED_)
#define AFX_MOVEDATASERVICE_H__41F7F3DC_C8B0_432D_B8E2_9897E65268A1__INCLUDED_

#if _MSC_VER > 1000
#pragma once
#endif // _MSC_VER > 1000

class CMoveDataService  
{
public:
	CMoveDataService();
	virtual ~CMoveDataService();
	int GetMoves(int facilityId, CStringArray &moveList);
	int GetOutboundMoves(int facilityId, CString &chainList, CStringArray &moveList);
};

#endif // !defined(AFX_MOVEDATASERVICE_H__41F7F3DC_C8B0_432D_B8E2_9897E65268A1__INCLUDED_)

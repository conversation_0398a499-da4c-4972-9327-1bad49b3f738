// WMSGroup.h: interface for the CWMSGroup class.
//
//////////////////////////////////////////////////////////////////////

#if !defined(AFX_WMSGROUP_H__257EECF0_C55A_4674_BF51_96B42C5EA1E3__INCLUDED_)
#define AFX_WMSGROUP_H__257EECF0_C55A_4674_BF51_96B42C5EA1E3__INCLUDED_

#if _MSC_VER > 1000
#pragma once
#endif // _MSC_VER > 1000

#include "WMS.h"
#include "WMSGroupConnection.h"

class CWMSGroup : public CObject  
{
public:
	CExternalConnection * GetExternalConnection(int interfaceType, int direction);
	void SetConnectionsByType();
	int Parse(CString &line);
	CWMSGroup();
	CWMSGroup(const CWMSGroup& other);
	CWMSGroup& operator=(const CWMSGroup& other);
	BOOL operator==(const CWMSGroup& other);
	BOOL operator!=(const CWMSGroup& other) { return (! (*this == other)); }
	virtual ~CWMSGroup();

	int m_WMSGroupDBId;
	CString m_Name;
	CString m_Description;
	CString m_WMSId;
	int m_ExternalSystemDBId;
	CString m_ExternalSystemName;

	CTypedPtrArray<CObArray, CWMS*> m_WMSList;

	CTypedPtrArray<CObArray, CWMSGroupConnection*> m_ConnectionList;

	CWMSGroupConnection *m_pLocInboundConnection;
	CWMSGroupConnection *m_pProdInboundConnection;
	CWMSGroupConnection *m_pAssgInboundConnection;

	CWMSGroupConnection *m_pLocOutboundConnection;
	CWMSGroupConnection *m_pAssgOutboundConnection;

	CWMSGroupConnection *m_pLocConfInboundConnection;
	CWMSGroupConnection *m_pAssgConfInboundConnection;;

	CWMSGroupConnection *m_pLocConfOutboundConnection;
	CWMSGroupConnection *m_pProdConfOutboundConnection;
	CWMSGroupConnection *m_pAssgConfOutboundConnection;

};

#endif // !defined(AFX_WMSGROUP_H__257EECF0_C55A_4674_BF51_96B42C5EA1E3__INCLUDED_)

#if !defined(AFX_COLORBUTTON_H__E946E460_A11C_4115_B22C_06B5F03D000D__INCLUDED_)
#define AFX_COLORBUTTON_H__E946E460_A11C_4115_B22C_06B5F03D000D__INCLUDED_

#if _MSC_VER > 1000
#pragma once
#endif // _MSC_VER > 1000
// ColorButton.h : header file
//
#include "ColorListBox.h"

/////////////////////////////////////////////////////////////////////////////
// CColorButton window

class CMyColorButton : public CButton
{
// Construction
public:
	CMyColorButton();

// Attributes
public:
	CColorObject *m_pColorObject;
// Operations
public:

// Overrides
	// ClassWizard generated virtual function overrides
	//{{AFX_VIRTUAL(CMyColorButton)
	public:
	virtual void DrawItem(LPDRAWITEMSTRUCT lpDrawItemStruct);
	//}}AFX_VIRTUAL

// Implementation
public:
	virtual ~CMyColorButton();

	// Generated message map functions
protected:
	//{{AFX_MSG(CMyColorButton)
		// NOTE - the ClassWizard will add and remove member functions here.
	//}}AFX_MSG

	DECLARE_MESSAGE_MAP()
};

/////////////////////////////////////////////////////////////////////////////

//{{AFX_INSERT_LOCATION}}
// Microsoft Visual C++ will insert additional declarations immediately before the previous line.

#endif // !defined(AFX_COLORBUTTON_H__E946E460_A11C_4115_B22C_06B5F03D000D__INCLUDED_)

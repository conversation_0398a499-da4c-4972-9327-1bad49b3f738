// DataGrid.cpp : implementation file
//

#include "stdafx.h"
#include "DataGrid.h"
#include "font.h"
#include "HelpService.h"
#include "UtilityHelper.h"

#include <dbsymtb.h>

#ifdef _DEBUG
#define new DEBUG_NEW
#undef THIS_FILE
static char THIS_FILE[] = __FILE__;
#endif
FILE *flog;

extern CHelpService helpService;
extern CUtilityHelper utilityHelper;

/////////////////////////////////////////////////////////////////////////////
// CDataGrid

CDataGrid::CDataGrid()
{
	m_AllowInsertDelete = FALSE;
	m_EnterCellFunction = NULL;
	m_LeaveCellFunction = NULL;
	m_DoubleClickCellFunction = NULL;
	m_ParentOldWidth = 0;
	m_ParentOldHeight = 0;
	m_Initializing = TRUE;
	m_ResetColumnWidths = FALSE;
	m_SortColumn = -1;
	m_ScaleGridToWindow = TRUE;
}

CDataGrid::~CDataGrid()
{
	for (int i=0; i < m_DataGridAttributes.GetSize(); ++i) {
		delete m_DataGridAttributes[i];
	}

	if (flog != NULL)
		fclose(flog);

}


BEGIN_MESSAGE_MAP(CDataGrid, CWnd)
	//{{AFX_MSG_MAP(CDataGrid)
	ON_WM_GETDLGCODE()
	ON_WM_SETFOCUS()
	ON_WM_KEYDOWN()
	ON_WM_KILLFOCUS()
	ON_WM_SIZE()
	ON_WM_HSCROLL()
	ON_WM_HELPINFO()
	ON_WM_VSCROLL()
	ON_WM_SHOWWINDOW()
	//}}AFX_MSG_MAP
END_MESSAGE_MAP()

BEGIN_EVENTSINK_MAP(CDataGrid, CMSFlexGrid)
// {{AFX_EVENTSINK_MAP(CDataGrid)
// }}AFX_EVENTSINK_MAP
ON_EVENT_REFLECT(CDataGrid, -603 /* KeyPress */, OnKeyPressGrid, VTS_PI2)
ON_EVENT_REFLECT(CDataGrid, -602 /* KeyDown */, OnKeyDownGrid, VTS_PI2 VTS_I2)
ON_EVENT_REFLECT(CDataGrid, -605 /* MouseDown */, OnMouseDownGrid, VTS_I2 VTS_I2 VTS_XPOS_PIXELS VTS_YPOS_PIXELS)
ON_EVENT_REFLECT(CDataGrid, -601 /* DblClick */, OnDblClickGrid, VTS_NONE)
ON_EVENT_REFLECT(CDataGrid, 72 /* LeaveCell */, OnLeaveCellGrid, VTS_NONE)
ON_EVENT_REFLECT(CDataGrid, 71 /* EnterCell */, OnEnterCellGrid, VTS_NONE)
END_EVENTSINK_MAP()

/////////////////////////////////////////////////////////////////////////////
// CDataGrid message handlers
void CDataGrid::OnDblClickGrid()
{
	if (flog != NULL) {
		fprintf(flog, "OnDblClickGrid: %d - %d, %d - %d\n", GetRow(), GetCol(), GetMouseRow(), GetMouseCol());
		fflush(flog);
	}
	if (! m_Initializing) {
		ShowCell(GetRow(), GetCol());
		if (m_DoubleClickCellFunction != NULL)
			m_DoubleClickCellFunction(GetParent());
	}
}

void CDataGrid::OnEnterCellGrid()
{
	if (flog != NULL) {
		fprintf(flog, "OnEnterCellGrid: %d - %d, %d - %d\n", GetRow(), GetCol(), GetMouseRow(), GetMouseCol());
		fflush(flog);
	}
	if (! m_Initializing) {
		ShowCell(GetRow(), GetCol());
		if (m_EnterCellFunction != NULL)
			m_EnterCellFunction(GetParent());

		if (m_PreviousRow != GetRow() && m_SortColumn >= 0) {
			HideCell(GetRow(), GetCol());
			SortAttributes(TRUE);
			LoadAttributes(TRUE);
			ShowCell(GetRow(), GetCol());
		}
	}

	m_PreviousRow = GetRow();
	m_PreviousColumn = GetCol();

}

void CDataGrid::OnLeaveCellGrid()
{
	int i = 0;
	if (flog != NULL) {
		fprintf(flog, "OnLeaveCellGrid: %d - %d, %d - %d\n", GetRow(), GetCol(), GetMouseRow(), GetMouseCol());
		fflush(flog);
	}

	if (! m_Initializing) {
		HideCell(GetRow(), GetCol());
		if (m_LeaveCellFunction != NULL)
			m_LeaveCellFunction(GetParent());
		
	}

}

void CDataGrid::OnMouseDownGrid(short Button, short shift, OLE_XPOS_PIXELS x, OLE_YPOS_PIXELS y)
{
	UNREFERENCED_PARAMETER(Button);
	UNREFERENCED_PARAMETER(shift);
	UNREFERENCED_PARAMETER(x);
	UNREFERENCED_PARAMETER(y);
	if (flog != NULL) {
		fprintf(flog, "OnMouseDownGrid: %d - %d, %d - %d\n", GetRow(), GetCol(), GetMouseRow(), GetMouseCol());
		fflush(flog);
	}
}

void CDataGrid::OnKeyPressGrid(short FAR* KeyAscii)
{
	if (flog != NULL) {
		fprintf(flog, "OnKeyPressGrid(%d): %d - %d, %d - %d\n", *KeyAscii, GetRow(), GetCol(), GetMouseRow(), GetMouseCol());
		fflush(flog);
	}
}

void CDataGrid::OnKeyDownGrid(short *KeyCode, short Shift)
{	
	UNREFERENCED_PARAMETER(Shift);
	if (flog != NULL) {
		fprintf(flog, "OnKeyDownGrid(%d): %d - %d, %d - %d\n", *KeyCode, GetRow(), GetCol(), GetMouseRow(), GetMouseCol());
		fflush(flog);
	}
	if (*KeyCode == 9) {		// Tab
		ProcessTab();
	}
	/*
	else if (*KeyCode == 112) {	// F1
		ShowHelp();
	}
	*/

}

void CDataGrid::OnSetFocus(CWnd* pOldWnd) 
{
	if (flog != NULL) {
		fprintf(flog, "OnSetFocus: %d - %d, %d - %d\n", GetRow(), GetCol(), GetMouseRow(), GetMouseCol());
		fflush(flog);
	}
	
	// If the focus came from another window or control (i.e. not the edits (or listboxes) within the grid,
	// show the current cell.
	// Note: there's a minor problem in that if you come back to the grid from another window
	// and you click on the exact same cell that you left, it will not fire the Enter Cell event
	// so the edit control doesn't appear on that cell.
	if (pOldWnd != NULL) {
		if (pOldWnd->GetParent() == this) {
			if (flog != NULL) {
				fprintf(flog, "    LeaveSetFocus(child): %d - %d, %d - %d\n", GetRow(), GetCol(), GetMouseRow(), GetMouseCol());
				fflush(flog);
			}
			return;
		}
	}

	if (GetRow() >= GetFixedRows() && GetCol() >= GetFixedCols()) {
		ShowCell(GetRow(), GetCol());
	}

	CMSFlexGrid::OnSetFocus(pOldWnd);
}

void CDataGrid::OnKillFocus(CWnd* pNewWnd) 
{
	if (flog != NULL) {
		fprintf(flog, "OnKillFocus: %d - %d, %d - %d\n", GetRow(), GetCol(), GetMouseRow(), GetMouseCol());
		fflush(flog);
	}
	CMSFlexGrid::OnKillFocus(pNewWnd);	
}

void CDataGrid::OnKeyDown(UINT nChar, UINT nRepCnt, UINT nFlags) 
{
	if (flog != NULL) {
		fprintf(flog, "OnKeyDown(%d): %d - %d, %d - %d\n", nChar, GetRow(), GetCol(), GetMouseRow(), GetMouseCol());
		fflush(flog);
	}
	CMSFlexGrid::OnKeyDown(nChar, nRepCnt, nFlags);
}

UINT CDataGrid::OnGetDlgCode() 
{
	if (flog != NULL) {
		fprintf(flog, "OnGetDlgCode: %d - %d, %d - %d\n", GetRow(), GetCol(), GetMouseRow(), GetMouseCol());
		fflush(flog);
	}
	return DLGC_WANTTAB|DLGC_WANTALLKEYS|DLGC_WANTARROWS;
	//return CMSFlexGrid::OnGetDlgCode();
}

void CDataGrid::ShowCell(long row, long col)
{
	int idx;
	CDataGridAttribute *pAttribute;
	if (flog != NULL) {
		fprintf(flog, "   In ShowCell: %d - %d, %d - %d\n", GetRow(), GetCol(), GetMouseRow(), GetMouseCol());
		fflush(flog);
	}

	idx = GetCols()*row + col;
	pAttribute = m_DataGridAttributes[idx];
	if (pAttribute->m_DataType == DT_NONE) {
		if (flog != NULL) {
			fprintf(flog, "   Out ShowCell(dt_none): %d - %d, %d - %d\n", GetRow(), GetCol(), GetMouseRow(), GetMouseCol());
			fflush(flog);
		}
		return;
	}

	if (pAttribute->m_DataType == DT_LIST) {
		
		if (m_ListBox.m_hWnd == NULL || pAttribute->m_ReadOnly) {
			if (flog != NULL) {
				fprintf(flog, "   Out ShowCell(list=null): %d - %d, %d - %d\n", GetRow(), GetCol(), GetMouseRow(), GetMouseCol());
				fflush(flog);
			}
			return;
		}
		
		m_ListBox.ResetContent();
		for (int i=0; i < pAttribute->m_ListValues.GetSize(); ++i)
			m_ListBox.AddString(pAttribute->m_ListValues[i]);
		
		// m_nLogy is the number of pixels in a logical inch
		// GetCellHeight() is in twips (1/1440th of an inch)
		m_ListBox.SetItemHeight(-1, (GetCellHeight() * m_nLogY)/2000);		// the edit box
		m_ListBox.SetItemHeight(0, (GetCellHeight() * m_nLogY)/1440);		// the list items
		
		int ch = (GetCellHeight() * m_nLogY * (pAttribute->m_ListValues.GetSize()*2))/1440;;
		int cl = ((GetCellLeft() - m_lBorderWidth) * m_nLogX)/1440;
		int ct = ((GetCellTop() - m_lBorderHeight) * m_nLogY)/1440;
		int cw = (GetCellWidth() * m_nLogX)/1440;
		int sz = pAttribute->m_ListValues.GetSize();

		m_ListBox.SetWindowText(GetText());
		m_ListBox.SetCurSel(m_ListBox.FindStringExact(0, GetText()));
	
		// MoveWindow(x, y, cx, cy)
		// cx = width of the whole list
		// cy = height of the whole list including the dropdown part
		m_ListBox.MoveWindow(((GetCellLeft() - m_lBorderWidth) * m_nLogX)/1440,
			((GetCellTop() - m_lBorderHeight) * m_nLogY)/1440,
			(GetCellWidth() * m_nLogX)/1440,
			(GetCellHeight() * m_nLogY * min(pAttribute->m_ListValues.GetSize()*2, 20))/1440, FALSE);

	
		CRect r;
		/*
		m_ListBox.GetWindowRect(&r);
		this->ScreenToClient(&r);
		r.top -= 2;
		r.bottom -= 3;

		m_ListBox.MoveWindow(&r, TRUE);

		for (int zz=0; zz < pAttribute->m_ListValues.GetSize(); ++zz)
			m_ListBox.SetItemHeight(zz, r.Height()-4);

		m_ListBox.SetItemHeight(-1, r.Height()-4);

		m_ListBox.GetWindowRect(&r);
		*/

		this->GetClientRect(&r);
		InvalidateRect(&r, TRUE);
		
		m_ListBox.ShowWindow(SW_SHOW);
		m_ListBox.SetFocus();		
	}
	else {
		
		if (pAttribute->m_DataType == DT_STRING)
			m_Edit = &m_LeftEdit;
		else
			m_Edit = &m_RightEdit;
		
		if (m_Edit->m_hWnd == NULL) {
			if (flog != NULL) {
				fprintf(flog, "   Out ShowCell(edit=null): %d - %d, %d - %d\n", GetRow(), GetCol(), GetMouseRow(), GetMouseCol());
				fflush(flog);
			}
			return;
		}

		m_Edit->SetWindowText(GetText());
		
		// Adjust for border height and convert from twips to screen
		// units.
		m_Edit->MoveWindow(((GetCellLeft() - m_lBorderWidth) *
			m_nLogX)/1440,
			((GetCellTop() - m_lBorderHeight) * m_nLogY)/1440,
			(GetCellWidth()* m_nLogX)/1440,
			(GetCellHeight()* m_nLogY)/1440, FALSE);
		
		if (pAttribute->m_ReadOnly) {
			SetCellBackColor(0x80000016);
			if (flog != NULL) {
				fprintf(flog, "   Out ShowCell(readonly): %d - %d, %d - %d\n", GetRow(), GetCol(), GetMouseRow(), GetMouseCol());
				fflush(flog);
			}
			return;
		}

		CRect r;
		this->GetClientRect(&r);
		InvalidateRect(&r, TRUE);
		
		m_Edit->ShowWindow(SW_SHOW);
		m_Edit->SetSel(0,-1);
		m_Edit->SetFocus();
		
	}
	if (flog != NULL) {
		fprintf(flog, "   Out ShowCell(normal): %d - %d, %d - %d\n", GetRow(), GetCol(), GetMouseRow(), GetMouseCol());
		fflush(flog);
	}

}

void CDataGrid::HideCell(long row, long col)
{
	int idx;
	CDataGridAttribute *pAttribute;
	CString txt, oldTxt;

	if (flog != NULL) {
		fprintf(flog, "   In HideCell: %d - %d, %d - %d\n", GetRow(), GetCol(), GetMouseRow(), GetMouseCol());
		fflush(flog);
	}


	idx = GetCols()*row + col;
	pAttribute = m_DataGridAttributes[idx];

	if (pAttribute->m_DataType == DT_NONE) {
		if (flog != NULL) {
			fprintf(flog, "   Out HideCell(dt_none): %d - %d, %d - %d\n", GetRow(), GetCol(), GetMouseRow(), GetMouseCol());
			fflush(flog);
		}
		return;
	}

	if (pAttribute->m_DataType == DT_LIST) {
		m_ListBox.GetWindowText(txt);
		m_ListBox.ShowWindow(SW_HIDE);
		oldTxt = GetTextMatrix(row, col);
		SetTextMatrix(row, col, txt);
		//pAttribute->m_Value = txt;
		pAttribute->m_Value.Format("%d", m_ListBox.FindStringExact(0, txt));
	}
	else {
		m_Edit->GetWindowText(txt);
		m_Edit->ShowWindow(SW_HIDE);
		oldTxt = GetTextMatrix(row, col);
		pAttribute->m_Value = FormatCell(pAttribute, txt);
		SetTextMatrix(row, col, txt);
		//m_Edit->SetWindowText("");
	}


	SetCellBackColor((COLORREF)0);

//	if (m_SortColumn >= 0 && ! m_Initializing && oldTxt != txt) {
//		LoadAttributes(TRUE);
//	}

	if (flog != NULL) {
		fprintf(flog, "   Out HideCell(normal): %d - %d, %d - %d\n", GetRow(), GetCol(), GetMouseRow(), GetMouseCol());
		fflush(flog);
	}

}

void CDataGrid::PreSubclassWindow() 
{

	
	flog = NULL;
//	flog = fopen("c:\\temp\\grid.log", "w");

//	if (flog == NULL) {
//		AfxMessageBox("Unable to open file.");
//		return;
//	}

	// Create invisible edit control
	m_RightEdit.Create(WS_BORDER|WS_CHILD|ES_WANTRETURN|ES_RIGHT|ES_MULTILINE|ES_AUTOHSCROLL,
		CRect(0,0,0,0), this, GetDlgCtrlID());

	m_LeftEdit.Create(WS_BORDER|WS_CHILD|ES_WANTRETURN|ES_LEFT|ES_MULTILINE|ES_AUTOHSCROLL,
		CRect(0,0,0,0), this, GetDlgCtrlID());

	m_EditFont.CreatePointFont((int)(this->GetCellFontSize()*10), this->GetCellFontName(), NULL);
	m_LeftEdit.SetFont(&m_EditFont);
	m_RightEdit.SetFont(&m_EditFont);

//	if (m_Alignment == RIGHT_ALIGNMENT)
//		m_Edit = &m_rightEdit;
//	else 
	m_Edit = &m_LeftEdit;

	float x = this->GetCellFontSize();
	int pointSz = (int)(10 * x);
	m_ListBox.Create(WS_CHILD|CBS_DROPDOWNLIST|WS_VSCROLL, CRect(0,0,0,0), this, GetDlgCtrlID());
	BOOL bCPF = m_ListFont.CreatePointFont(pointSz, this->GetCellFontName(), NULL);
	m_ListBox.SetFont(&m_ListFont);

	// Calculate border size.
	m_Initializing = TRUE;
	SetRow(0);
	SetCol(0);

	m_lBorderWidth = GetCellLeft();
	m_lBorderHeight = GetCellTop();
	
	for (int i=0; i < this->GetFixedCols(); ++i)
		SetColAlignment(i, 4);

	for (i=this->GetFixedCols(); i < this->GetCols(); ++i) {
//		if (m_Alignment == RIGHT_ALIGNMENT)	// right
//			SetColAlignment(i, 7);
//		else
			SetColAlignment(i, 1);
	}

	// To convert grid rect from twips to DC units you need
	// pixels per inch.
	CDC* pDC = GetDC();
	m_nLogX = pDC->GetDeviceCaps(LOGPIXELSX);
	m_nLogY = pDC->GetDeviceCaps(LOGPIXELSY);
	ReleaseDC(pDC);


}

void CDataGrid::ProcessInsert(BOOL bAfterRow)
{
	if (! m_AllowInsertDelete)
		return;

	CDataGridAttribute *pAttr, *pCopyAttr;
	long rows, currentRow, cols, col;
	int idx, copyIdx;

	rows = this->GetRows();
	currentRow = this->GetRow();


	// Force the edit box to be hidden for the current cell;
	// this copies the value into the grid cell and the attribute record
	HideCell(currentRow, this->GetCol());

	// bAfterRow means we are adding a new row at the end (usually by tabbing past the last row);
	if (bAfterRow)
		currentRow++;

	// If we are sorting, always put the new row at the beginning
	if (m_SortColumn >= 0) {
		currentRow = GetFixedRows();
		bAfterRow = FALSE;
	}

	// Add the row to the grid control
	this->AddItem("", COleVariant(currentRow));
	rows = this->GetRows();
	cols = this->GetCols();


	// Allocate a single row's worth of attribute records
	for (col=0; col < cols; ++col) {
		idx = currentRow * cols + col;
		pAttr = new CDataGridAttribute;
		if (idx < m_DataGridAttributes.GetSize())
			m_DataGridAttributes.InsertAt(idx, pAttr);
		else
			m_DataGridAttributes.Add(pAttr);

	}


	// Now copy the row directly below the new row if one exists; otherwise, the one directly above
	// Clear out the values
	for (col=0; col < cols; ++col) {
		idx = currentRow * cols + col;
		if (currentRow == 0 || currentRow-1 < this->GetFixedRows() )
			copyIdx = (currentRow+1)*cols + col;
		else
			copyIdx = (currentRow-1) * cols + col;

		if (this->m_DataGridAttributes.GetSize() >= copyIdx) {
			pCopyAttr = this->m_DataGridAttributes[copyIdx];
			pAttr = m_DataGridAttributes[idx];
			pAttr->m_Type = pCopyAttr->m_Type;
			pAttr->m_DataType = pCopyAttr->m_DataType;
			pAttr->m_HelpTopic = pCopyAttr->m_HelpTopic;
			pAttr->m_Min = pCopyAttr->m_Min;
			pAttr->m_Max = pCopyAttr->m_Max;
			pAttr->m_Value = pAttr->m_InitialValue = "";
			pAttr->m_Format = pCopyAttr->m_Format;
		}
		else {		// just use some generic values
			pAttr = m_DataGridAttributes[idx];
			pAttr->m_Type = AT_VAR;
			pAttr->m_DataType = DT_STRING;
			pAttr->m_Value = pAttr->m_InitialValue = "";
		}

	}
	
	// If we inserted a row before the current, any rows after it are now one higher
	// so update the previous row which tells us whether we have left a row
	// when we leave a row, we resort the list
	if (m_PreviousRow >= currentRow)
		m_PreviousRow++;

	// Change back to the original cell we were in without updating anything
	m_Initializing = TRUE;
	if (bAfterRow)
		SetRow(currentRow-1);
	else
		SetRow(m_PreviousRow);
	m_Initializing = FALSE;


	// Now change to the newly inserted row; this will cause a resort if necessary
	this->SetRow(currentRow);
	// Set the column to the first non-fixed column;
	// (remember, columns are 0-based)
	this->SetCol(this->GetFixedCols());

	// Start editing the new row
	OnDblClickGrid();


	return;


}

void CDataGrid::ProcessDelete()
{
	if (! m_AllowInsertDelete)
		return;
	
	int idx;
	long currentRow = this->GetRow();
	long rows = this->GetRows();
	long cols = this->GetCols();
	CDataGridAttribute *pAttr;

	if (rows == 2) {
		// If there's only one non-fixed row, just clear it out instead of removing it
		long cols = this->GetCols();
		for (int i=0; i < cols; ++i) {
			this->SetTextMatrix(1, i, "");
			idx = currentRow * cols + i;
			pAttr = m_DataGridAttributes[idx];
			pAttr->m_Value = "";
		}

	}
	else {
		this->RemoveItem(currentRow);
		for (int i=0; i < cols; ++i) {
			idx = currentRow * cols + i;
			delete m_DataGridAttributes[idx];
		}
		m_DataGridAttributes.RemoveAt(currentRow*cols, cols);
		if (currentRow == rows-1)
			this->SetRow(currentRow-1);
	}

	OnDblClickGrid();

}


void CDataGrid::ProcessTab()
{
	long currentRow, currentCol, rows, cols, newRow, newCol;
	
	rows = this->GetRows();
	cols = this->GetCols();
	currentRow = newRow = this->GetRow();
	currentCol = newCol = this->GetCol();
	

	if (flog != NULL) {
		fprintf(flog, "Process tab: %d, %d\n", currentRow, currentCol);
		fprintf(flog, "Tab: Before set\n");
	}
	if (currentRow == rows-1) {		// last row
		if (currentCol == cols-1)	// last column
			ProcessInsert(TRUE);
		else {
			this->SetCol(currentCol+1);
		}
	}
	else {
		if (currentCol == cols-1) {		// last column
			this->SetRow(currentRow+1);
			this->SetCol(this->GetFixedCols());	// 1st non-fixed column
		}
		else {
			this->SetCol(currentCol+1);
		}
	}

	if (flog != NULL) {
		fprintf(flog, "Out tab: %d, %d\n", GetRow(), GetCol());
	}

	return;
}

CDataGrid::CDataGrid(BOOL pAllowInsertDelete)
{
	m_AllowInsertDelete = pAllowInsertDelete;
	m_ParentOldWidth = 0;
	m_ParentOldHeight = 0;
	m_Initializing = TRUE;
	m_SortColumn = -1;
	m_ScaleGridToWindow = TRUE;
}

void CDataGrid::LoadAttributes(BOOL bUpdating)
{

	CDataGridAttribute *pAttr;
	long rows, cols, row, col, twips, currentRow = -1;
	CStringArray maxStrings;
	SIZE size;
	float pixels, inches;

	m_Initializing = TRUE;

	rows = this->GetRows();
	cols = this->GetCols();

	// If this is the first time we've loaded the screen, sort the attributes if necessary
	if (! bUpdating && m_SortColumn >= 0) {
		currentRow = SortAttributes(bUpdating);
	}

	// Set the cells in the grid control based on the values in the attribute records
	for (int i=0; i < m_DataGridAttributes.GetSize(); ++i) {
		row = i/cols;
		col = i - row*cols;
		pAttr = m_DataGridAttributes[i];

		if (pAttr->m_DataType == DT_LIST) {
 			if (pAttr->m_ListValues.GetSize() > 0 && pAttr->m_Value != "" )
				this->SetTextMatrix(row, col, pAttr->m_ListValues[atoi(pAttr->m_Value)]);
			else
				this->SetTextMatrix(row, col, "");
		}
		else {
			if (bUpdating)
				this->SetTextMatrix(row, col, FormatCell(pAttr, pAttr->m_Value));
			else
				this->SetTextMatrix(row, col, FormatCell(pAttr, pAttr->m_InitialValue));
		}

		if (pAttr->m_DataType == DT_STRING || pAttr->m_DataType == DT_LIST || pAttr->m_Type == AT_FIXED) {
			// assume that if we allow sorting, all of the items in a column are the same type
			// in which case we can set the entire column alignment; otherwise set individual cell alignments
			if (m_SortColumn < 0) {
				SetRow(row);
				SetCol(col);
				SetCellAlignment(1);
			}
			else
				this->SetColAlignment(col, 1);	 // left
		}
		else {
			if (m_SortColumn < 0) {
				SetRow(row);
				SetCol(col);
				SetCellAlignment(7);
			}
			else
				this->SetColAlignment(col, 7);	// right
		}

	}
	

	// Set the initial width of the columns based on 
	// the biggest value in each column
	if (m_ResetColumnWidths) {
		CString str;
		// Get the maximum length of the first column
		for (col=0; col < cols; ++col) {
			// Set the max to the column header
			maxStrings.Add(GetTextMatrix(0, col));
			if (cols+col >= m_DataGridAttributes.GetSize())
				continue;

			CDataGridAttribute *pAttr = m_DataGridAttributes[cols+col];
			
			// If the type is a list, find the longest list element
			if (pAttr->m_DataType == DT_LIST) {
				for (int i=0; i < pAttr->m_ListValues.GetSize(); ++i) {
					if (pAttr->m_ListValues[i].GetLength() > maxStrings[col].GetLength())
						maxStrings[col] = pAttr->m_ListValues[i];
				}
			}
			// If the type is not a list, find the longest value in the column
			else {
				for (row = 1; row < rows; ++row) {
					if (GetTextMatrix(row, col).GetLength() > maxStrings[col].GetLength())
						maxStrings[col] = GetTextMatrix(row, col);
				}
			}
		}
	}


	
	if (m_ScaleGridToWindow) {

		CRect r;
		int totalWindowWidth = 0, totalCellWidth = 0;
		this->GetClientRect(&r);
		totalWindowWidth = r.Width();
		// convert to twips
		inches = (float)totalWindowWidth/m_nLogX;
		twips = (long)(inches * 1440);
		totalWindowWidth = twips;
		CDWordArray twipArray;
		
		for (col=0; col < cols; ++col) {
			if (maxStrings.GetSize() == 0) {
				twips = this->GetColWidth(col);
				totalCellWidth += twips;
				twipArray.Add(twips);
			}
			else {
				// Get length of string in logical units
				GetTextExtentPoint32(GetDC()->m_hDC, maxStrings[col], maxStrings[col].GetLength(), &size);
				// 1 pixel = 1 logical unit
				pixels = (float)size.cx;
				// Convert pixels to inches - m_nLogx is the number of pixels in a logical inch
				inches = pixels / m_nLogX;
				// Convert inches to twips - 1440 twips = 1 inch
				twips = (long)(inches * 1440);
				totalCellWidth += twips;
				twipArray.Add(twips);
			}
		}
		
		if (totalCellWidth == 0)
			totalCellWidth = 1;
		
		for (col=0; col < cols; ++col) {
			twips = twipArray[col];
			// scale it up so the whole window is taken up
			if (totalWindowWidth > totalCellWidth)
				twips = (int)((float)twips * ((float)totalWindowWidth/(float)totalCellWidth));
			this->SetColWidth(col, twips-5);		// subtract one from every column just to be safe
		}

	}

	// the first time, set the cell to the top left
	if (! bUpdating) {
		SetRow(0);
		SetCol(0);
	}

	m_Initializing = FALSE;

}


void CDataGrid::OnSize(UINT nType, int cx, int cy) 
{
	CMSFlexGrid::OnSize(nType, cx, cy);

	// Ability to resize is still a work in progress, fraught with peril, I might add.
	/*
	if (! m_Initializing) {

		m_lBorderWidth = GetCellLeft();
		m_lBorderHeight = GetCellTop();
		HideCell(GetRow(), GetCol());
		
		int oldCellWidth, newCellWidth;
		float pct;
		for (int i=0; i < GetCols(); ++i) {
			oldCellWidth = GetColWidth(i);
			pct = ((float)cx/(float)m_ParentOldWidth);
			newCellWidth = oldCellWidth * pct;
			if (i == GetCols()-1) {
			}
			SetColWidth(i, newCellWidth);
		}
		ShowCell(GetRow(), GetCol());
	}
	
	m_ParentOldWidth = cx;
	m_ParentOldHeight = cy;
	*/

}

void CDataGrid::OnHScroll(UINT nSBCode, UINT nPos, CScrollBar* pScrollBar) 
{
	SetRow(0);
	SetCol(0);
	CMSFlexGrid::OnHScroll(nSBCode, nPos, pScrollBar);
}

BOOL CDataGrid::OnHelpInfo(HELPINFO* pHelpInfo) 
{
	UNREFERENCED_PARAMETER(pHelpInfo);
	ShowHelp();

	return FALSE;

}

void CDataGrid::ShowHelp()
{
	int idx, row, col, cols;

	cols = GetCols();
	row = GetRow();
	col = GetCol();

	idx = cols * row;	// for now show the help for the first column only

	if (m_DataGridAttributes[idx]->m_HelpTopic != "")
		helpService.ShowFieldHelp(m_DataGridAttributes[idx]->m_HelpTopic);
	else if (m_MainHelpTopic != "")
		helpService.ShowScreenHelp(m_MainHelpTopic);
	else {
		CString helpTopic = "Generic_Grid";
		helpService.ShowScreenHelp(helpTopic);
	}


}

void CDataGrid::OnVScroll(UINT nSBCode, UINT nPos, CScrollBar* pScrollBar) 
{
	SetRow(0);
	SetCol(0);
	CMSFlexGrid::OnVScroll(nSBCode, nPos, pScrollBar);
}

void CDataGrid::ProcessArrow(int direction)
{
	long col, row, newRow, newCol;
	
	col = newCol = GetCol();
	row = newRow = GetRow();


	// 0 = left; 1 = right; 2 = up; 3 = down
	switch (direction) {
	case 0:
		if (col > GetFixedCols())
			newCol = col - 1;
		else {
			if (row > GetFixedRows()) {
				newCol = GetCols()-1;
				newRow = row - 1;
			}
		}
		break;
	case 1:
		if (col < GetCols()-1)
			newCol = col + 1;
		else {
			if (row < GetRows()-1) {
				newCol = GetFixedCols();
				newRow = row + 1;
			}
		}
		break;
	case 2:
		if (row > GetFixedRows())
			newRow = row - 1;
		break;
	case 3:
		if (row < GetRows()-1)
			newRow = row + 1;
		break;
	}
	this->SetRow(newRow);
	this->SetCol(newCol);

}

long CDataGrid::SortAttributes(BOOL bUpdating)
{
	long row, col, rows, cols, currentRow, newRow;
	int idx;
	CString temp, sortString;
	CStringArray sortList;
	CDataGridAttribute *pAttr;

	if (flog != NULL) {
		fprintf(flog, "In Sort: %d, %d\n", this->GetRow(), this->GetCol());
	}

	// Not a very efficient way of sorting; build a list of strings containing
	// the columns formatted out to a large size so a text sort will still sort
	// numeric values accurately

	rows = this->GetRows();
	cols = this->GetCols();

	currentRow = this->GetRow();

	// get the row right after the fixed rows
	row = this->GetFixedRows();
	col = m_SortColumn;
	idx = row*cols + col;

	pAttr = m_DataGridAttributes[idx];

	for (row=this->GetFixedRows(); row < rows; ++row) {
		// put the first column at the beginning of the sort string
		col = m_SortColumn;
		idx = row*cols+col;
		pAttr = m_DataGridAttributes[idx];

		if (bUpdating || pAttr->m_DataType == DT_LIST)
			temp = pAttr->m_Value;
		else
			temp = pAttr->m_InitialValue;

		if (pAttr->m_DataType == DT_INT) {
			int data = atoi(temp);
			temp.Format("%020d", data);
		}
		else if (pAttr->m_DataType == DT_FLOAT) {
			double data = atof(temp);
			temp.Format("%025.10f", data);
		}
		else if (pAttr->m_DataType == DT_LIST) {
			if (temp != "")
				temp = pAttr->m_ListValues[atoi(temp)];	
			else
				temp = "";
		}
		
		sortString = temp;

		// add the formatted values of all the columns for sorting;
		// the sort column will be a duplicate but it won't affect the sort
		for (col=this->GetFixedCols(); col < cols; ++col) {

			idx = row*cols+col;
			pAttr = m_DataGridAttributes[idx];

			if (bUpdating || pAttr->m_DataType == DT_LIST)
				temp = pAttr->m_Value;
			else
				temp = pAttr->m_InitialValue;

			if (pAttr->m_DataType == DT_INT) {
				int data = atoi(temp);
				temp.Format("%020d", data);
			}
			else if (pAttr->m_DataType == DT_FLOAT) {
				double data = atof(temp);
				temp.Format("%025.10f", data);
			}
			else if (pAttr->m_DataType == DT_LIST) {
				if (temp != "")
					temp = pAttr->m_ListValues[atoi(temp)];	
				else
					temp = "";
			}

			sortString += "|";
			sortString += temp;
		}

		// add the original values of the columns so we can put the values
		// back in the table with the original format
		for (col=this->GetFixedCols(); col < cols; ++col) {
			idx = row*cols + col;
			pAttr = m_DataGridAttributes[idx];

			if (bUpdating)
				temp = pAttr->m_Value;
			else
				temp = pAttr->m_InitialValue;
			sortString += "|";
			sortString += temp;
		}
		temp.Format("%d", row);
		sortString += "|";
		sortString += temp;

		sortList.Add(sortString);

	}

	utilityHelper.SortStringArray(sortList);
	
	int i = 0, j = 0;
	CStringArray strings;
	
	// Now put the sorted items back in the attribute list
	for (row=this->GetFixedRows(); row < rows; ++row) {
		utilityHelper.ParseString(sortList[i], "|", strings);
		
		long oldRow = atol(strings[strings.GetSize()-1]);
		if (currentRow == oldRow)
			newRow = row;

		// skip the first set of columns which are the formatted values
		// (and the extra first sort column) and use the real values
		j = cols+1;
		for (col=this->GetFixedCols(); col < cols; ++col) {
			idx = row*cols + col;
			pAttr = m_DataGridAttributes[idx];
			if (pAttr->m_DataType != DT_LIST) {
				pAttr->m_Value = strings[j];
				if (! bUpdating)
					pAttr->m_InitialValue = pAttr->m_Value;
			}
			else {
				pAttr->m_Value = "";
				for (int k=0; k < pAttr->m_ListValues.GetSize(); ++k) {
					if (pAttr->m_ListValues[k] == strings[j]) {
						pAttr->m_Value.Format("%d", k);
						if (! bUpdating)
							pAttr->m_InitialValue = pAttr->m_Value;
						break;
					}
				}
			}
			j++;
		}

		i++;
	}

	if (flog != NULL) {
		fprintf(flog, "Out Sort: newrow = %d\n", newRow);
	}

	return newRow;
}

BOOL CDataGrid::IsCurrentCellChanged()
{
	long row, col;
	int idx;
	CDataGridAttribute *pAttribute;
	CString txt, oldTxt;

	row = GetRow();
	col = GetCol();

	idx = GetCols()*row + col;
	pAttribute = m_DataGridAttributes[idx];

	if (pAttribute->m_DataType == DT_NONE) {	
		return FALSE;
	}

	if (pAttribute->m_DataType == DT_LIST) {
		if (m_ListBox.GetCurSel() == atoi(pAttribute->m_Value))
			return FALSE;
		else
			return TRUE;
	}
	else {
		m_Edit->GetWindowText(txt);
		if (txt == pAttribute->m_Value)
			return FALSE;
		else
			return TRUE;
	}

	return FALSE;
}

CString CDataGrid::FormatCell(CDataGridAttribute *pAttribute, CString &txt)
{
	if (pAttribute->m_Format == "")
		return txt;

	if (pAttribute->m_DataType == DT_FLOAT) {
		double data = atof(txt);
		txt.Format(pAttribute->m_Format, data);
	}
	else if (pAttribute->m_DataType == DT_INT) {
		int data = atoi(txt);
		txt.Format(pAttribute->m_Format, data);
	}
	else if (pAttribute->m_DataType == DT_STRING) {
		CString data = txt;
		txt.Format(pAttribute->m_Format, data);
	}

	return txt;
}


void CDataGrid::EndEdit()
{
	HideCell(GetRow(), GetCol());

}

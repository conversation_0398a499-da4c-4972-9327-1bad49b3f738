#if !defined(AFX_EXPORTFACILITY_H__6D85F39A_6037_40A4_BF7C_282127F23AD8__INCLUDED_)
#define AFX_EXPORTFACILITY_H__6D85F39A_6037_40A4_BF7C_282127F23AD8__INCLUDED_

#include "SSACStringArray.h"	// Added by ClassView
#if _MSC_VER > 1000
#pragma once
#endif // _MSC_VER > 1000
// ExportFacility.h : header file
//

/////////////////////////////////////////////////////////////////////////////
// CExportFacility dialog

class CExportFacility : public CDialog
{
// Construction
public:
	
	void GetDataBaseList();

	CExportFacility(CWnd* pParent = NULL);   // standard constructor

// Dialog Data
	//{{AFX_DATA(CExportFacility)
	enum { IDD = IDD_EXPORT_FACILITY };
	CComboBox	m_DatabaseDropDown;
	//}}AFX_DATA


// Overrides
	// ClassWizard generated virtual function overrides
	//{{AFX_VIRTUAL(CExportFacility)
	public:
	virtual void OnFinalRelease();
	protected:
	virtual void DoDataExchange(CDataExchange* pDX);    // DDX/DDV support
	//}}AFX_VIRTUAL

// Implementation
	CStringArray databaseList;
	CStringArray sidList;
protected:
	virtual BOOL OnInitDialog();
	// Generated message map functions
	//{{AFX_MSG(CExportFacility)
	afx_msg void OnExport();
	//}}AFX_MSG
	DECLARE_MESSAGE_MAP()
	// Generated OLE dispatch map functions
	//{{AFX_DISPATCH(CExportFacility)
		// NOTE - the ClassWizard will add and remove member functions here.
	//}}AFX_DISPATCH
	DECLARE_DISPATCH_MAP()
	DECLARE_INTERFACE_MAP()
};

//{{AFX_INSERT_LOCATION}}
// Microsoft Visual C++ will insert additional declarations immediately before the previous line.

#endif // !defined(AFX_EXPORTFACILITY_H__6D85F39A_6037_40A4_BF7C_282127F23AD8__INCLUDED_)

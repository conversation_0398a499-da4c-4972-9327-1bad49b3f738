#include "stdafx.h"
#include "modal.h"

#include "ControlService.h"
#include "TreeElement.h"
#include "ssa_exception.h"
#include "BTreeHelper.h"
#include "DataAccessService.h"


extern CDataAccessService dataAccessService;
extern CBTreeHelper bTreeHelper;
extern CControlService controlService;

int TreeElement::getSectionCount()
{
	// Assume we are at the facility level
	// otherwise; abort
	if (stricmp(facilityElement, "SLOTFacility") != 0)
		return -1;

	return treeChildren.GetSize();

}

int TreeElement::getAisleCountForSection(int sectionIdx)
{
	if (stricmp(facilityElement, "SLOTFacility") != 0)
		return -1;

	return treeChildren[sectionIdx].treeChildren.GetSize();

}

int TreeElement::getSideCountForAisle(int sectionIdx, int aisleIdx)
{
	if (stricmp(facilityElement, "SLOTFacility") != 0)
		return -1;

	return treeChildren[sectionIdx].treeChildren[aisleIdx].treeChildren.GetSize();
}

int TreeElement::getBayCountForSide(int sectionIdx, int aisleIdx, int sideIdx)
{
	if (stricmp(facilityElement, "SLOTFacility") != 0)
		return -1;

	return treeChildren[sectionIdx].treeChildren[aisleIdx].treeChildren[sideIdx].treeChildren.GetSize();
}

int TreeElement::getLevelCountForBay(int sectionIdx, int aisleIdx, int sideIdx, int bayIdx)
{
	if (stricmp(facilityElement, "SLOTFacility") != 0)
		return -1;

	return treeChildren[sectionIdx].treeChildren[aisleIdx].treeChildren[sideIdx].treeChildren[bayIdx].treeChildren.GetSize();
}

int TreeElement::getLocationCountForLevel(int sectionIdx, int aisleIdx, int sideIdx, int bayIdx, int levelIdx)
{
	if (stricmp(facilityElement, "SLOTFacility") != 0)
		return -1;

	return treeChildren[sectionIdx].treeChildren[aisleIdx].treeChildren[sideIdx].treeChildren[bayIdx].treeChildren[levelIdx].treeChildren.GetSize();
}

TreeElement *TreeElement::getSection(int sectionIdx)
{
	if (stricmp(facilityElement, "SLOTFacility") != 0)
		return NULL;

	if (treeChildren.GetSize() < sectionIdx)
		return NULL;

	return &(treeChildren[sectionIdx]);

}

int TreeElement::setBtSection(int sectionIdx, qqhSLOTSection &section)
{
	if (stricmp(facilityElement, "SLOTFacility") != 0)
		return -1;

	if (treeChildren.GetSize() < sectionIdx)
		return -1;

	return bTreeHelper.SetBtSection(treeChildren[sectionIdx].fileOffset, section);

}

int TreeElement::getBtSection(int sectionIdx, qqhSLOTSection &section)
{
	if (stricmp(facilityElement, "SLOTFacility") != 0)
		return -1;

	if (treeChildren.GetSize() < sectionIdx)
		return -1;

	return bTreeHelper.GetBtSection(treeChildren[sectionIdx].fileOffset, section);

}

TreeElement *TreeElement::getAisle(int sectionIdx, int aisleIdx)
{
	if (stricmp(facilityElement, "SLOTFacility") != 0)
		return NULL;

	if (treeChildren.GetSize() < sectionIdx)
		return NULL;

	if (treeChildren[sectionIdx].treeChildren.GetSize() < aisleIdx)
		return NULL;

	return &(treeChildren[sectionIdx].treeChildren[aisleIdx]);

}

int TreeElement::getBtAisle(int sectionIdx, int aisleIdx, qqhSLOTAisle &aisle)
{
	if (stricmp(facilityElement, "SLOTFacility") != 0)
		return -1;

	if (treeChildren.GetSize() < sectionIdx)
		return -1;

	if (treeChildren[sectionIdx].treeChildren.GetSize() < aisleIdx)
		return -1;

	return bTreeHelper.GetBtAisle(treeChildren[sectionIdx].treeChildren[aisleIdx].fileOffset, aisle);
}

int TreeElement::setBtAisle(int sectionIdx, int aisleIdx, qqhSLOTAisle &aisle)
{
	if (stricmp(facilityElement, "SLOTFacility") != 0)
		return -1;

	if (treeChildren.GetSize() < sectionIdx)
		return -1;

	if (treeChildren[sectionIdx].treeChildren.GetSize() < aisleIdx)
		return -1;

	return bTreeHelper.SetBtAisle(treeChildren[sectionIdx].treeChildren[aisleIdx].fileOffset, aisle);
}


TreeElement *TreeElement::getSide(int sectionIdx, int aisleIdx, int sideIdx)
{
	if (stricmp(facilityElement, "SLOTFacility") != 0)
		return NULL;

	if (treeChildren.GetSize() < sectionIdx)
		return NULL;

	if (treeChildren[sectionIdx].treeChildren.GetSize() < aisleIdx)
		return NULL;

	if (treeChildren[sectionIdx].treeChildren[aisleIdx].treeChildren.GetSize() < sideIdx)
		return NULL;

	return &(treeChildren[sectionIdx].treeChildren[aisleIdx].treeChildren[sideIdx]);

}

int TreeElement::getBtSide(int sectionIdx, int aisleIdx, int sideIdx, qqhSLOTSide &side)
{
	if (stricmp(facilityElement, "SLOTFacility") != 0)
		return -1;

	if (treeChildren.GetSize() < sectionIdx)
		return -1;

	if (treeChildren[sectionIdx].treeChildren.GetSize() < aisleIdx)
		return -1;

	if (treeChildren[sectionIdx].treeChildren[aisleIdx].treeChildren.GetSize() < sideIdx)
		return -1;

	return bTreeHelper.GetBtSide(treeChildren[sectionIdx].treeChildren[aisleIdx].treeChildren[sideIdx].fileOffset, side);

}

int TreeElement::setBtSide(int sectionIdx, int aisleIdx, int sideIdx, qqhSLOTSide &side)
{
	if (stricmp(facilityElement, "SLOTFacility") != 0)
		return -1;

	if (treeChildren.GetSize() < sectionIdx)
		return -1;

	if (treeChildren[sectionIdx].treeChildren.GetSize() < aisleIdx)
		return -1;

	if (treeChildren[sectionIdx].treeChildren[aisleIdx].treeChildren.GetSize() < sideIdx)
		return -1;

	return bTreeHelper.SetBtSide(treeChildren[sectionIdx].treeChildren[aisleIdx].treeChildren[sideIdx].fileOffset, side);

}

TreeElement *TreeElement::getBay(int sectionIdx, int aisleIdx, int sideIdx, int bayIdx)
{
	if (stricmp(facilityElement, "SLOTFacility") != 0)
		return NULL;

	if (treeChildren.GetSize() < sectionIdx)
		return NULL;

	if (treeChildren[sectionIdx].treeChildren.GetSize() < aisleIdx)
		return NULL;

	if (treeChildren[sectionIdx].treeChildren[aisleIdx].treeChildren.GetSize() < sideIdx)
		return NULL;

	if (treeChildren[sectionIdx].treeChildren[aisleIdx].treeChildren[sideIdx].treeChildren.GetSize() < bayIdx)
		return NULL;

	return &(treeChildren[sectionIdx].treeChildren[aisleIdx].treeChildren[sideIdx].treeChildren[bayIdx]);

}

int TreeElement::getBtBay(int sectionIdx, int aisleIdx, int sideIdx, int bayIdx, qqhSLOTBay &bay)
{
	if (stricmp(facilityElement, "SLOTFacility") != 0)
		return -1;

	if (treeChildren.GetSize() < sectionIdx)
		return -1;

	if (treeChildren[sectionIdx].treeChildren.GetSize() < aisleIdx)
		return -1;

	if (treeChildren[sectionIdx].treeChildren[aisleIdx].treeChildren.GetSize() < sideIdx)
		return -1;

	if (treeChildren[sectionIdx].treeChildren[aisleIdx].treeChildren[sideIdx].treeChildren.GetSize() < bayIdx)
		return -1;

	return bTreeHelper.GetBtBay(treeChildren[sectionIdx].treeChildren[aisleIdx].treeChildren[sideIdx].treeChildren[bayIdx].fileOffset, bay);

}

int TreeElement::setBtBay(int sectionIdx, int aisleIdx, int sideIdx, int bayIdx, qqhSLOTBay &bay)
{
	if (stricmp(facilityElement, "SLOTFacility") != 0)
		return -1;

	if (treeChildren.GetSize() < sectionIdx)
		return -1;

	if (treeChildren[sectionIdx].treeChildren.GetSize() < aisleIdx)
		return -1;

	if (treeChildren[sectionIdx].treeChildren[aisleIdx].treeChildren.GetSize() < sideIdx)
		return -1;

	if (treeChildren[sectionIdx].treeChildren[aisleIdx].treeChildren[sideIdx].treeChildren.GetSize() < bayIdx)
		return -1;

	return bTreeHelper.SetBtBay(treeChildren[sectionIdx].treeChildren[aisleIdx].treeChildren[sideIdx].treeChildren[bayIdx].fileOffset, bay);

}

TreeElement *TreeElement::getLevel(int sectionIdx, int aisleIdx, int sideIdx, int bayIdx, int levelIdx)
{
	if (stricmp(facilityElement, "SLOTFacility") != 0)
		return NULL;

	if (treeChildren.GetSize() < sectionIdx)
		return NULL;

	if (treeChildren[sectionIdx].treeChildren.GetSize() < aisleIdx)
		return NULL;

	if (treeChildren[sectionIdx].treeChildren[aisleIdx].treeChildren.GetSize() < sideIdx)
		return NULL;

	if (treeChildren[sectionIdx].treeChildren[aisleIdx].treeChildren[sideIdx].treeChildren.GetSize() < bayIdx)
		return NULL;
	
	if (treeChildren[sectionIdx].treeChildren[aisleIdx].treeChildren[sideIdx].treeChildren[bayIdx].treeChildren.GetSize() < levelIdx)
		return NULL;

	return &(treeChildren[sectionIdx].treeChildren[aisleIdx].treeChildren[sideIdx].treeChildren[bayIdx].treeChildren[levelIdx]);

}

int TreeElement::getBtLevel(int sectionIdx, int aisleIdx, int sideIdx, int bayIdx, int levelIdx, qqhSLOTLevel &level)
{
	if (stricmp(facilityElement, "SLOTFacility") != 0)
		return -1;

	if (treeChildren.GetSize() < sectionIdx)
		return -1;

	if (treeChildren[sectionIdx].treeChildren.GetSize() < aisleIdx)
		return -1;

	if (treeChildren[sectionIdx].treeChildren[aisleIdx].treeChildren.GetSize() < sideIdx)
		return -1;

	if (treeChildren[sectionIdx].treeChildren[aisleIdx].treeChildren[sideIdx].treeChildren.GetSize() < bayIdx)
		return -1;
	
	if (treeChildren[sectionIdx].treeChildren[aisleIdx].treeChildren[sideIdx].treeChildren[bayIdx].treeChildren.GetSize() < levelIdx)
		return -1;

	return bTreeHelper.GetBtLevel(treeChildren[sectionIdx].treeChildren[aisleIdx].treeChildren[sideIdx].treeChildren[bayIdx].treeChildren[levelIdx].fileOffset, level);

}

int TreeElement::setBtLevel(int sectionIdx, int aisleIdx, int sideIdx, int bayIdx, int levelIdx, qqhSLOTLevel &level)
{
	if (stricmp(facilityElement, "SLOTFacility") != 0)
		return -1;

	if (treeChildren.GetSize() < sectionIdx)
		return -1;

	if (treeChildren[sectionIdx].treeChildren.GetSize() < aisleIdx)
		return -1;

	if (treeChildren[sectionIdx].treeChildren[aisleIdx].treeChildren.GetSize() < sideIdx)
		return -1;

	if (treeChildren[sectionIdx].treeChildren[aisleIdx].treeChildren[sideIdx].treeChildren.GetSize() < bayIdx)
		return -1;
	
	if (treeChildren[sectionIdx].treeChildren[aisleIdx].treeChildren[sideIdx].treeChildren[bayIdx].treeChildren.GetSize() < levelIdx)
		return -1;

	return bTreeHelper.SetBtLevel(treeChildren[sectionIdx].treeChildren[aisleIdx].treeChildren[sideIdx].treeChildren[bayIdx].treeChildren[levelIdx].fileOffset, level);

}

TreeElement *TreeElement::getLocation(int sectionIdx, int aisleIdx, int sideIdx, int bayIdx, int levelIdx, int locationIdx)
{
	if (stricmp(facilityElement, "SLOTFacility") != 0)
		return NULL;

	if (treeChildren.GetSize() < sectionIdx)
		return NULL;

	if (treeChildren[sectionIdx].treeChildren.GetSize() < aisleIdx)
		return NULL;

	if (treeChildren[sectionIdx].treeChildren[aisleIdx].treeChildren.GetSize() < sideIdx)
		return NULL;

	if (treeChildren[sectionIdx].treeChildren[aisleIdx].treeChildren[sideIdx].treeChildren.GetSize() < bayIdx)
		return NULL;
	
	if (treeChildren[sectionIdx].treeChildren[aisleIdx].treeChildren[sideIdx].treeChildren[bayIdx].treeChildren.GetSize() < levelIdx)
		return NULL;

	if (treeChildren[sectionIdx].treeChildren[aisleIdx].treeChildren[sideIdx].treeChildren[bayIdx].treeChildren[levelIdx].treeChildren.GetSize() < locationIdx)
		return NULL;

	return &(treeChildren[sectionIdx].treeChildren[aisleIdx].treeChildren[sideIdx].treeChildren[bayIdx].treeChildren[levelIdx].treeChildren[locationIdx]);

}

int TreeElement::getBtLocation(int sectionIdx, int aisleIdx, int sideIdx, int bayIdx, int levelIdx, int locationIdx, qqhSLOTLocation &location)
{
	if (stricmp(facilityElement, "SLOTFacility") != 0)
		return -1;

	if (treeChildren.GetSize() < sectionIdx)
		return -1;

	if (treeChildren[sectionIdx].treeChildren.GetSize() < aisleIdx)
		return -1;

	if (treeChildren[sectionIdx].treeChildren[aisleIdx].treeChildren.GetSize() < sideIdx)
		return -1;

	if (treeChildren[sectionIdx].treeChildren[aisleIdx].treeChildren[sideIdx].treeChildren.GetSize() < bayIdx)
		return -1;
	
	if (treeChildren[sectionIdx].treeChildren[aisleIdx].treeChildren[sideIdx].treeChildren[bayIdx].treeChildren.GetSize() < levelIdx)
		return -1;

	if (treeChildren[sectionIdx].treeChildren[aisleIdx].treeChildren[sideIdx].treeChildren[bayIdx].treeChildren[levelIdx].treeChildren.GetSize() < locationIdx)
		return -1;

	return bTreeHelper.GetBtLocation(treeChildren[sectionIdx].treeChildren[aisleIdx].treeChildren[sideIdx].treeChildren[bayIdx].treeChildren[levelIdx].treeChildren[locationIdx].fileOffset, location);

}


int TreeElement::setBtLocation(int sectionIdx, int aisleIdx, int sideIdx, int bayIdx, int levelIdx, int locationIdx, qqhSLOTLocation &location)
{
	if (stricmp(facilityElement, "SLOTFacility") != 0)
		return -1;

	if (treeChildren.GetSize() < sectionIdx)
		return -1;

	if (treeChildren[sectionIdx].treeChildren.GetSize() < aisleIdx)
		return -1;

	if (treeChildren[sectionIdx].treeChildren[aisleIdx].treeChildren.GetSize() < sideIdx)
		return -1;

	if (treeChildren[sectionIdx].treeChildren[aisleIdx].treeChildren[sideIdx].treeChildren.GetSize() < bayIdx)
		return -1;
	
	if (treeChildren[sectionIdx].treeChildren[aisleIdx].treeChildren[sideIdx].treeChildren[bayIdx].treeChildren.GetSize() < levelIdx)
		return -1;

	if (treeChildren[sectionIdx].treeChildren[aisleIdx].treeChildren[sideIdx].treeChildren[bayIdx].treeChildren[levelIdx].treeChildren.GetSize() < locationIdx)
		return -1;

	return bTreeHelper.SetBtLocation(treeChildren[sectionIdx].treeChildren[aisleIdx].treeChildren[sideIdx].treeChildren[bayIdx].treeChildren[levelIdx].treeChildren[locationIdx].fileOffset, location);

}

TreeElement *TreeElement::getSectionByBayHandle(CString &handle)
{
	int sectionIdx, aisleIdx, sideIdx, bayIdx;
	TreeElement *bay;
	
	for (sectionIdx = 0; sectionIdx < getSectionCount(); ++sectionIdx) {
		for (aisleIdx = 0; aisleIdx < getAisleCountForSection(sectionIdx); ++aisleIdx) {
			for (sideIdx = 0; sideIdx < getSideCountForAisle(sectionIdx, aisleIdx); ++sideIdx) {
				for (bayIdx = 0; bayIdx < getBayCountForSide(sectionIdx, aisleIdx, sideIdx); ++bayIdx) {
					bay = getBay(sectionIdx, aisleIdx, sideIdx, bayIdx);
					if (handle.CompareNoCase(bay->acadHandle) == 0) {
						return getSection(sectionIdx);
					}
				}
			}
		}

	}

	// We couldn't find it so try to get the bay from the database
	if (bTreeHelper.UpdateBTWithBay(handle, *this) < 0)
		return NULL;			// could not get handle from database

	// Try to get it from the tree again
	for (sectionIdx = 0; sectionIdx < getSectionCount(); ++sectionIdx) {
		for (aisleIdx = 0; aisleIdx < getAisleCountForSection(sectionIdx); ++aisleIdx) {
			for (sideIdx = 0; sideIdx < getSideCountForAisle(sectionIdx, aisleIdx); ++sideIdx) {
				for (bayIdx = 0; bayIdx < getBayCountForSide(sectionIdx, aisleIdx, sideIdx); ++bayIdx) {
					bay = getBay(sectionIdx, aisleIdx, sideIdx, bayIdx);
					if (handle.CompareNoCase(bay->acadHandle) == 0) {
						return getSection(sectionIdx);
					}
				}
			}
		}
		
	}


	return NULL;

}


TreeElement *TreeElement::getAisleByBayHandle(CString &handle)
{
	int sectionIdx, aisleIdx, sideIdx, bayIdx;
	TreeElement *bay;
	
	for (sectionIdx = 0; sectionIdx < getSectionCount(); ++sectionIdx) {
		for (aisleIdx = 0; aisleIdx < getAisleCountForSection(sectionIdx); ++aisleIdx) {
			for (sideIdx = 0; sideIdx < getSideCountForAisle(sectionIdx, aisleIdx); ++sideIdx) {
				for (bayIdx = 0; bayIdx < getBayCountForSide(sectionIdx, aisleIdx, sideIdx); ++bayIdx) {
					bay = getBay(sectionIdx, aisleIdx, sideIdx, bayIdx);
					if (handle.CompareNoCase(bay->acadHandle) == 0) {
						return getAisle(sectionIdx, aisleIdx);
					}
				}
			}
		}

	}

	// We couldn't find it so try to get the bay from the database
	if (bTreeHelper.UpdateBTWithBay(handle, *this) < 0)
		return NULL;			// could not get handle from database

	// Try to get it from the tree again
	for (sectionIdx = 0; sectionIdx < getSectionCount(); ++sectionIdx) {
		for (aisleIdx = 0; aisleIdx < getAisleCountForSection(sectionIdx); ++aisleIdx) {
			for (sideIdx = 0; sideIdx < getSideCountForAisle(sectionIdx, aisleIdx); ++sideIdx) {
				for (bayIdx = 0; bayIdx < getBayCountForSide(sectionIdx, aisleIdx, sideIdx); ++bayIdx) {
					bay = getBay(sectionIdx, aisleIdx, sideIdx, bayIdx);
					if (handle.CompareNoCase(bay->acadHandle) == 0) {
						return getAisle(sectionIdx, aisleIdx);
					}
				}
			}
		}
		
	}


	return NULL;


}


TreeElement *TreeElement::getSideByBayHandle(CString &handle)
{
	int sectionIdx, aisleIdx, sideIdx, bayIdx;
	TreeElement *bay;
	
	for (sectionIdx = 0; sectionIdx < getSectionCount(); ++sectionIdx) {
		for (aisleIdx = 0; aisleIdx < getAisleCountForSection(sectionIdx); ++aisleIdx) {
			for (sideIdx = 0; sideIdx < getSideCountForAisle(sectionIdx, aisleIdx); ++sideIdx) {
				for (bayIdx = 0; bayIdx < getBayCountForSide(sectionIdx, aisleIdx, sideIdx); ++bayIdx) {
					bay = getBay(sectionIdx, aisleIdx, sideIdx, bayIdx);
					if (handle.CompareNoCase(bay->acadHandle) == 0) {
						return getSide(sectionIdx, aisleIdx, sideIdx);
					}
				}
			}
		}

	}

	// We couldn't find it so try to get the bay from the database
	if (bTreeHelper.UpdateBTWithBay(handle, *this) < 0)
		return NULL;			// could not get handle from database

	// Try to get it from the tree again
	for (sectionIdx = 0; sectionIdx < getSectionCount(); ++sectionIdx) {
		for (aisleIdx = 0; aisleIdx < getAisleCountForSection(sectionIdx); ++aisleIdx) {
			for (sideIdx = 0; sideIdx < getSideCountForAisle(sectionIdx, aisleIdx); ++sideIdx) {
				for (bayIdx = 0; bayIdx < getBayCountForSide(sectionIdx, aisleIdx, sideIdx); ++bayIdx) {
					bay = getBay(sectionIdx, aisleIdx, sideIdx, bayIdx);
					if (handle.CompareNoCase(bay->acadHandle) == 0) {
						return getSide(sectionIdx, aisleIdx, sideIdx);
					}
				}
			}
		}
		
	}


	return NULL;


}


TreeElement *TreeElement::getBayByHandle(CString &handle)
{
	int sectionIdx, aisleIdx, sideIdx, bayIdx;
	TreeElement *bay;
	
	for (sectionIdx = 0; sectionIdx < getSectionCount(); ++sectionIdx) {
		for (aisleIdx = 0; aisleIdx < getAisleCountForSection(sectionIdx); ++aisleIdx) {
			for (sideIdx = 0; sideIdx < getSideCountForAisle(sectionIdx, aisleIdx); ++sideIdx) {
				for (bayIdx = 0; bayIdx < getBayCountForSide(sectionIdx, aisleIdx, sideIdx); ++bayIdx) {
					bay = getBay(sectionIdx, aisleIdx, sideIdx, bayIdx);
					if (handle.CompareNoCase(bay->acadHandle) == 0) {
						return getBay(sectionIdx, aisleIdx, sideIdx, bayIdx);
					}
				}
			}
		}

	}

	// We couldn't find it so try to get the bay from the database
	if (bTreeHelper.UpdateBTWithBay(handle, *this) < 0)
		return NULL;			// could not get handle from database

	// Try to get it from the tree again
	for (sectionIdx = 0; sectionIdx < getSectionCount(); ++sectionIdx) {
		for (aisleIdx = 0; aisleIdx < getAisleCountForSection(sectionIdx); ++aisleIdx) {
			for (sideIdx = 0; sideIdx < getSideCountForAisle(sectionIdx, aisleIdx); ++sideIdx) {
				for (bayIdx = 0; bayIdx < getBayCountForSide(sectionIdx, aisleIdx, sideIdx); ++bayIdx) {
					bay = getBay(sectionIdx, aisleIdx, sideIdx, bayIdx);
					if (handle.CompareNoCase(bay->acadHandle) == 0) {
						return getBay(sectionIdx, aisleIdx, sideIdx, bayIdx);
					}
				}
			}
		}
		
	}


	return NULL;


}


TreeElement *TreeElement::getLevelByHandle(CString &handle, int dbid)
{

	TreeElement *bPtr, *lPtr;


	bPtr = getBayByHandle(handle);

	if (bPtr == NULL)
		return NULL;

	for (int lIdx=0; lIdx < bPtr->treeChildren.GetSize(); ++lIdx) {
		lPtr = &(bPtr->treeChildren[lIdx]);
		if (lPtr->elementDBID == dbid) {
			return lPtr;
		}
	}


	return NULL;


}


TreeElement *TreeElement::getLocationByHandle(CString &handle, int dbid)
{
	TreeElement *bPtr, *lPtr, *loPtr;


	bPtr = getBayByHandle(handle);

	if (bPtr == NULL)
		return NULL;

	for (int lIdx=0; lIdx < bPtr->treeChildren.GetSize(); ++lIdx) {
		lPtr = &(bPtr->treeChildren[lIdx]);
		for (int loIdx=0; loIdx < lPtr->treeChildren.GetSize(); ++loIdx) {
			loPtr = &(lPtr->treeChildren[loIdx]);
			if (loPtr->elementDBID == dbid) {
				return loPtr;
			}
		}
	}


	return NULL;
}

int TreeElement::getBayHandlesByAisle(TreeElement *aislePtr, CStringArray &tempHandles)
{
	int  sideIdx, bayIdx;
	
	
	for (sideIdx = 0; sideIdx < aislePtr->treeChildren.GetSize(); ++sideIdx) {
		for (bayIdx = 0; bayIdx < aislePtr->treeChildren[sideIdx].treeChildren.GetSize(); ++bayIdx)
			tempHandles.Add(aislePtr->treeChildren[sideIdx].treeChildren[bayIdx].acadHandle);
	}
	
	
	return tempHandles.GetSize();

}

int TreeElement::getLevelDBIDsByBay(TreeElement *bayPtr, CDWordArray &levelIDList)
{
	int levelIdx;
	for (levelIdx = 0; levelIdx < bayPtr->treeChildren.GetSize(); ++levelIdx)
		levelIDList.Add(bayPtr->treeChildren[levelIdx].elementDBID);

	return levelIDList.GetSize();

}

int TreeElement::deleteBranch(CString elementType, TreeElement *elementPtr)
{

	int sectionIdx, aisleIdx, sideIdx, bayIdx, levelIdx, locIdx;

	for (sectionIdx = 0; sectionIdx < getSectionCount(); ++sectionIdx) {
		if (strcmpi(elementType, "SLOTSection") == 0) {
			if (elementPtr->fileOffset == getSection(sectionIdx)->fileOffset) {
				treeChildren.RemoveAt(sectionIdx);
				return 1;
			}
			continue;
		}
		
		for (aisleIdx = 0; aisleIdx < getAisleCountForSection(sectionIdx); ++aisleIdx) {
			if (strcmpi(elementType, "SLOTAisle") == 0) {
				if (elementPtr->fileOffset == getAisle(sectionIdx, aisleIdx)->fileOffset) {
					getSection(sectionIdx)->treeChildren.RemoveAt(aisleIdx);
					return 1;
				}
				continue;
			}
			
			for (sideIdx = 0; sideIdx < getSideCountForAisle(sectionIdx, aisleIdx); ++sideIdx) {
				if (strcmpi(elementType, "SLOTSide") == 0) {
					if (elementPtr->fileOffset == getSide(sectionIdx, aisleIdx, sideIdx)->fileOffset) {
						getAisle(sectionIdx, aisleIdx)->treeChildren.RemoveAt(sideIdx);
						return 1;
					}
					continue;
				}
				
				for (bayIdx = 0; bayIdx < getBayCountForSide(sectionIdx, aisleIdx, sideIdx); ++bayIdx) {
					if (strcmpi(elementType, "SLOTBay") == 0) {
						if (elementPtr->fileOffset == getBay(sectionIdx, aisleIdx, sideIdx, bayIdx)->fileOffset) {
							getSide(sectionIdx, aisleIdx, sideIdx)->treeChildren.RemoveAt(bayIdx);
							return 1;
						}
						continue;
					}
					
					for (levelIdx = 0; levelIdx < getLevelCountForBay(sectionIdx, aisleIdx, sideIdx, bayIdx); ++levelIdx) {
						if (strcmpi(elementType, "SLOTLevel") == 0) {
							if (elementPtr->fileOffset == getLevel(sectionIdx, aisleIdx, sideIdx, bayIdx, levelIdx)->fileOffset) {
								getBay(sectionIdx, aisleIdx, sideIdx, bayIdx)->treeChildren.RemoveAt(levelIdx);
								return 1;
							}
							continue;
						}

						for (locIdx = 0; locIdx < getLocationCountForLevel(sectionIdx, aisleIdx, sideIdx, bayIdx, levelIdx); ++locIdx) {
							if (strcmpi(elementType, "SLOTLocation") == 0) {
								if (elementPtr->fileOffset == getLocation(sectionIdx, aisleIdx, sideIdx, bayIdx, levelIdx, locIdx)->fileOffset) {
									getLevel(sectionIdx, aisleIdx, sideIdx, bayIdx, levelIdx)->treeChildren.RemoveAt(locIdx);
									return 1;
								}
								continue;
							}
						}
					}
				}
								
			}
		}
	}

	return 0;



}


int TreeElement::getLocationOffsetByFacility(int facilityOffset, CArray<int, int> &locOffsetList)
{

	int sectionIdx, aisleIdx, sideIdx, bayIdx, levelIdx, locIdx;
	locOffsetList.RemoveAll();

	for (sectionIdx = 0; sectionIdx < getSectionCount(); ++sectionIdx) {
			for (aisleIdx = 0; aisleIdx < getAisleCountForSection(sectionIdx); ++aisleIdx)
				for (sideIdx = 0; sideIdx < getSideCountForAisle(sectionIdx, aisleIdx); ++sideIdx)
					for (bayIdx = 0; bayIdx < getBayCountForSide(sectionIdx, aisleIdx, sideIdx); ++bayIdx)
						for (levelIdx = 0; levelIdx < getLevelCountForBay(sectionIdx, aisleIdx, sideIdx, bayIdx); ++levelIdx)
							for (locIdx = 0; locIdx < getLocationCountForLevel(sectionIdx, aisleIdx, sideIdx, bayIdx, levelIdx); ++locIdx)
								locOffsetList.Add(getLocation(sectionIdx, aisleIdx, sideIdx, bayIdx, levelIdx, locIdx)->fileOffset);
	}

	return locOffsetList.GetSize();

}


int TreeElement::getLocationOffsetBySection(int sectionOffset, CArray<int, int> &locOffsetList)
{

	int sectionIdx, aisleIdx, sideIdx, bayIdx, levelIdx, locIdx;
	locOffsetList.RemoveAll();

	for (sectionIdx = 0; sectionIdx < getSectionCount(); ++sectionIdx) {
		if (getSection(sectionIdx)->fileOffset == sectionOffset) {
			for (aisleIdx = 0; aisleIdx < getAisleCountForSection(sectionIdx); ++aisleIdx)
				for (sideIdx = 0; sideIdx < getSideCountForAisle(sectionIdx, aisleIdx); ++sideIdx)
					for (bayIdx = 0; bayIdx < getBayCountForSide(sectionIdx, aisleIdx, sideIdx); ++bayIdx)
						for (levelIdx = 0; levelIdx < getLevelCountForBay(sectionIdx, aisleIdx, sideIdx, bayIdx); ++levelIdx)
							for (locIdx = 0; locIdx < getLocationCountForLevel(sectionIdx, aisleIdx, sideIdx, bayIdx, levelIdx); ++locIdx)
								locOffsetList.Add(getLocation(sectionIdx, aisleIdx, sideIdx, bayIdx, levelIdx, locIdx)->fileOffset);
			break;
		}
	}

	return locOffsetList.GetSize();

}

int TreeElement::getLocationOffsetByAisle(int aisleOffset, CArray<int, int> &locOffsetList)
{

	int sectionIdx, aisleIdx, sideIdx, bayIdx, levelIdx, locIdx;
	locOffsetList.RemoveAll();

	for (sectionIdx = 0; sectionIdx < getSectionCount(); ++sectionIdx) {
		for (aisleIdx = 0; aisleIdx < getAisleCountForSection(sectionIdx); ++aisleIdx)
			if (getAisle(sectionIdx, aisleIdx)->fileOffset == aisleOffset) {
				for (sideIdx = 0; sideIdx < getSideCountForAisle(sectionIdx, aisleIdx); ++sideIdx)
					for (bayIdx = 0; bayIdx < getBayCountForSide(sectionIdx, aisleIdx, sideIdx); ++bayIdx)
						for (levelIdx = 0; levelIdx < getLevelCountForBay(sectionIdx, aisleIdx, sideIdx, bayIdx); ++levelIdx)
							for (locIdx = 0; locIdx < getLocationCountForLevel(sectionIdx, aisleIdx, sideIdx, bayIdx, levelIdx); ++locIdx)
								locOffsetList.Add(getLocation(sectionIdx, aisleIdx, sideIdx, bayIdx, levelIdx, locIdx)->fileOffset);
				return locOffsetList.GetSize();
			}
	}

	return locOffsetList.GetSize();

}

int TreeElement::getLocationOffsetBySide(int sideOffset, CArray<int, int> &locOffsetList)
{

	int sectionIdx, aisleIdx, sideIdx, bayIdx, levelIdx, locIdx;
	locOffsetList.RemoveAll();

	for (sectionIdx = 0; sectionIdx < getSectionCount(); ++sectionIdx) {
		for (aisleIdx = 0; aisleIdx < getAisleCountForSection(sectionIdx); ++aisleIdx)
			for (sideIdx = 0; sideIdx < getSideCountForAisle(sectionIdx, aisleIdx); ++sideIdx)
				if (getSide(sectionIdx, aisleIdx, sideIdx)->fileOffset == sideOffset) {
					for (bayIdx = 0; bayIdx < getBayCountForSide(sectionIdx, aisleIdx, sideIdx); ++bayIdx)
						for (levelIdx = 0; levelIdx < getLevelCountForBay(sectionIdx, aisleIdx, sideIdx, bayIdx); ++levelIdx)
							for (locIdx = 0; locIdx < getLocationCountForLevel(sectionIdx, aisleIdx, sideIdx, bayIdx, levelIdx); ++locIdx)
								locOffsetList.Add(getLocation(sectionIdx, aisleIdx, sideIdx, bayIdx, levelIdx, locIdx)->fileOffset);
							
				}	
				return locOffsetList.GetSize();
	}
	
	return locOffsetList.GetSize();

}

int TreeElement::getLocationOffsetByBay(int bayOffset, CArray<int, int> &locOffsetList)
{

	int sectionIdx, aisleIdx, sideIdx, bayIdx, levelIdx, locIdx;
	locOffsetList.RemoveAll();

	for (sectionIdx = 0; sectionIdx < getSectionCount(); ++sectionIdx) {
		for (aisleIdx = 0; aisleIdx < getAisleCountForSection(sectionIdx); ++aisleIdx)
			for (sideIdx = 0; sideIdx < getSideCountForAisle(sectionIdx, aisleIdx); ++sideIdx)
				for (bayIdx = 0; bayIdx < getBayCountForSide(sectionIdx, aisleIdx, sideIdx); ++bayIdx)
					if (getBay(sectionIdx, aisleIdx, sideIdx, bayIdx)->fileOffset == bayOffset) {
						for (levelIdx = 0; levelIdx < getLevelCountForBay(sectionIdx, aisleIdx, sideIdx, bayIdx); ++levelIdx)
							for (locIdx = 0; locIdx < getLocationCountForLevel(sectionIdx, aisleIdx, sideIdx, bayIdx, levelIdx); ++locIdx)
								locOffsetList.Add(getLocation(sectionIdx, aisleIdx, sideIdx, bayIdx, levelIdx, locIdx)->fileOffset);
						return locOffsetList.GetSize();
					}	
	}
	
	return locOffsetList.GetSize();

}


int TreeElement::getLocationOffsetByLevel(int levelOffset, CArray<int, int> &locOffsetList)
{

	int sectionIdx, aisleIdx, sideIdx, bayIdx, levelIdx, locIdx;
	locOffsetList.RemoveAll();

	for (sectionIdx = 0; sectionIdx < getSectionCount(); ++sectionIdx) {
		for (aisleIdx = 0; aisleIdx < getAisleCountForSection(sectionIdx); ++aisleIdx)
			for (sideIdx = 0; sideIdx < getSideCountForAisle(sectionIdx, aisleIdx); ++sideIdx)
				for (bayIdx = 0; bayIdx < getBayCountForSide(sectionIdx, aisleIdx, sideIdx); ++bayIdx)
					for (levelIdx = 0; levelIdx < getLevelCountForBay(sectionIdx, aisleIdx, sideIdx, bayIdx); ++levelIdx)
						if (getLevel(sectionIdx, aisleIdx, sideIdx, bayIdx, levelIdx)->fileOffset == levelOffset) {
							for (locIdx = 0; locIdx < getLocationCountForLevel(sectionIdx, aisleIdx, sideIdx, bayIdx, levelIdx); ++locIdx)
								locOffsetList.Add(getLocation(sectionIdx, aisleIdx, sideIdx, bayIdx, levelIdx, locIdx)->fileOffset);
							return locOffsetList.GetSize();
						}	
	}
	
	return locOffsetList.GetSize();

}


BOOL TreeElement::IsDirty()
{
	qqhSLOTSection section;
	
	// Consider the tree dirty if any of the sections have changed
	// or any aisles or lower have been retrieved into the tree

	for (int sectionIdx = 0; sectionIdx < getSectionCount(); ++sectionIdx) {
		bTreeHelper.GetBtSection(this->treeChildren[sectionIdx].fileOffset, section);
		if (section.getIsChanged() == "TRUE")
			return TRUE;

		if (this->treeChildren[sectionIdx].treeChildren.GetSize() > 0)
			return TRUE;
	}

	return FALSE;

}


TreeElement *TreeElement::getSectionByDBID(long dbid)
{
	for (int sectionIdx = 0; sectionIdx < this->treeChildren.GetSize(); ++sectionIdx) {
		if (this->treeChildren[sectionIdx].elementDBID == dbid) {
			return &(this->treeChildren[sectionIdx]);
			break;
		}
	}

	return NULL;

}

TreeElement *TreeElement::getAisleByDBID(long dbid)
{
	TreeElement *sPtr, *aPtr;

	for (int sIdx=0; sIdx < this->treeChildren.GetSize(); ++sIdx) {
		sPtr = &(this->treeChildren[sIdx]);
		for (int aIdx=0; aIdx < sPtr->treeChildren.GetSize(); ++aIdx) {
			aPtr = &(sPtr->treeChildren[aIdx]);
			if (aPtr->elementDBID == dbid)
				return aPtr;
		}
	}

	return NULL;
}

TreeElement *TreeElement::getSideByDBID(long dbid)
{
	TreeElement *sPtr, *aPtr, *siPtr;

	for (int sIdx=0; sIdx < this->treeChildren.GetSize(); ++sIdx) {
		sPtr = &(this->treeChildren[sIdx]);
		for (int aIdx=0; aIdx < sPtr->treeChildren.GetSize(); ++aIdx) {
			aPtr = &(sPtr->treeChildren[aIdx]);
			for (int siIdx=0; siIdx < aPtr->treeChildren.GetSize(); ++siIdx) {
				siPtr = &(aPtr->treeChildren[siIdx]);
				if (siPtr->elementDBID == dbid)
					return siPtr;
			}
		}
	}

	return NULL;
}

TreeElement *TreeElement::getBayByDBID(long dbid)
{
	TreeElement *sPtr, *aPtr, *siPtr, *bPtr;

	for (int sIdx=0; sIdx < this->treeChildren.GetSize(); ++sIdx) {
		sPtr = &(this->treeChildren[sIdx]);
		for (int aIdx=0; aIdx < sPtr->treeChildren.GetSize(); ++aIdx) {
			aPtr = &(sPtr->treeChildren[aIdx]);
			for (int siIdx=0; siIdx < aPtr->treeChildren.GetSize(); ++siIdx) {
				siPtr = &(aPtr->treeChildren[siIdx]);
				for (int bIdx=0; bIdx < siPtr->treeChildren.GetSize(); ++bIdx) {
					bPtr = &(siPtr->treeChildren[bIdx]);
					if (bPtr->elementDBID == dbid)
						return bPtr;
				}
			}
		}
	}

	return NULL;	return NULL;
}

TreeElement *TreeElement::getLevelByDBID(long dbid)
{
	TreeElement *sPtr, *aPtr, *siPtr, *bPtr, *lPtr;

	for (int sIdx=0; sIdx < this->treeChildren.GetSize(); ++sIdx) {
		sPtr = &(this->treeChildren[sIdx]);
		for (int aIdx=0; aIdx < sPtr->treeChildren.GetSize(); ++aIdx) {
			aPtr = &(sPtr->treeChildren[aIdx]);
			for (int siIdx=0; siIdx < aPtr->treeChildren.GetSize(); ++siIdx) {
				siPtr = &(aPtr->treeChildren[siIdx]);
				for (int bIdx=0; bIdx < siPtr->treeChildren.GetSize(); ++bIdx) {
					bPtr = &(siPtr->treeChildren[bIdx]);
					for (int lIdx=0; lIdx < bPtr->treeChildren.GetSize(); ++lIdx) {
						lPtr = &(bPtr->treeChildren[lIdx]);
						if (lPtr->elementDBID == dbid)
							return lPtr;
					}
				}
			}
		}
	}

	return NULL;
}

TreeElement *TreeElement::getLocationByDBID(long dbid, BOOL getFromDatabase)
{
	TreeElement *sPtr, *aPtr, *siPtr, *bPtr, *lPtr, *loPtr;
	int sIdx, aIdx, siIdx, bIdx, lIdx, loIdx;

	// see if the item is already in the btree
	for (sIdx=0; sIdx < this->treeChildren.GetSize(); ++sIdx) {
		sPtr = &(this->treeChildren[sIdx]);
		for (aIdx=0; aIdx < sPtr->treeChildren.GetSize(); ++aIdx) {
			aPtr = &(sPtr->treeChildren[aIdx]);
			for (siIdx=0; siIdx < aPtr->treeChildren.GetSize(); ++siIdx) {
				siPtr = &(aPtr->treeChildren[siIdx]);
				for (bIdx=0; bIdx < siPtr->treeChildren.GetSize(); ++bIdx) {
					bPtr = &(siPtr->treeChildren[bIdx]);
					for (lIdx=0; lIdx < bPtr->treeChildren.GetSize(); ++lIdx) {
						lPtr = &(bPtr->treeChildren[lIdx]);
						for (loIdx=0; loIdx < lPtr->treeChildren.GetSize(); ++loIdx) {
							loPtr = &(lPtr->treeChildren[loIdx]);
							if (loPtr->elementDBID == dbid)
								return loPtr;
						}
					}
				}
			}
		}
	}

	if (! getFromDatabase)
		return NULL;

	// it's not in the btree, so get the bay handle and retrieve it to the btree
	CString sql, handle;
	CStringArray results;
	sql.Format("select dbbay.acadhandle "
		"from dbbay, dblevel, dblocation "
		"where dbbay.dbbayid = dblevel.dbbayid "
		"and dblevel.dblevelid = dblocation.dblevelid "
		"and dblocation.dblocationid = %d ", dbid);
	try {
		dataAccessService.ExecuteQuery("GetBayHandleByLocationId", sql, results);
	}
	catch (Ssa_Exception e) {
		//ProcessError("Error finding location.", &e);
		return NULL;
	}
	catch (...) {
		//ProcessError("Error finding location.");
		return NULL;
	}

	if (results.GetSize() == 0)
		return NULL;

	handle = results[0];
	handle.TrimRight("|");
	
	bPtr = getBayByHandle(handle);
	if (bPtr == NULL)
		return NULL;

	for (lIdx=0; lIdx < bPtr->treeChildren.GetSize(); ++lIdx) {
		lPtr = &(bPtr->treeChildren[lIdx]);
		for (loIdx=0; loIdx < lPtr->treeChildren.GetSize(); ++loIdx) {
			loPtr = &(lPtr->treeChildren[loIdx]);
			if (loPtr->elementDBID == dbid)
				return loPtr;
		}
	}

	return NULL;
}

TreeElement * TreeElement::getSectionByOffset(int fileOffset)
{
	TreeElement *sPtr;

	for (int sIdx=0; sIdx < this->treeChildren.GetSize(); ++sIdx) {
		sPtr = &(this->treeChildren[sIdx]);
		if (sPtr->fileOffset == fileOffset)
			return sPtr;

	}

	return NULL;

}

TreeElement * TreeElement::getAisleByOffset(int fileOffset)
{
	TreeElement *sPtr, *aPtr;

	for (int sIdx=0; sIdx < this->treeChildren.GetSize(); ++sIdx) {
		sPtr = &(this->treeChildren[sIdx]);
		for (int aIdx=0; aIdx < sPtr->treeChildren.GetSize(); ++aIdx) {
			aPtr = &(sPtr->treeChildren[aIdx]);
			if (aPtr->fileOffset == fileOffset)
				return aPtr;
		}
	}

	return NULL;

}

TreeElement * TreeElement::getSideByOffset(int fileOffset)
{
	TreeElement *sPtr, *aPtr, *siPtr;

	for (int sIdx=0; sIdx < this->treeChildren.GetSize(); ++sIdx) {
		sPtr = &(this->treeChildren[sIdx]);
		for (int aIdx=0; aIdx < sPtr->treeChildren.GetSize(); ++aIdx) {
			aPtr = &(sPtr->treeChildren[aIdx]);
			for (int siIdx=0; siIdx < aPtr->treeChildren.GetSize(); ++siIdx) {
				siPtr = &(aPtr->treeChildren[siIdx]);
				if (siPtr->fileOffset == fileOffset)
					return siPtr;
			}
		}
	}

	return NULL;

}

TreeElement * TreeElement::getBayByOffset(int fileOffset)
{
	TreeElement *sPtr, *aPtr, *siPtr, *bPtr;

	for (int sIdx=0; sIdx < this->treeChildren.GetSize(); ++sIdx) {
		sPtr = &(this->treeChildren[sIdx]);
		for (int aIdx=0; aIdx < sPtr->treeChildren.GetSize(); ++aIdx) {
			aPtr = &(sPtr->treeChildren[aIdx]);
			for (int siIdx=0; siIdx < aPtr->treeChildren.GetSize(); ++siIdx) {
				siPtr = &(aPtr->treeChildren[siIdx]);
				for (int bIdx=0; bIdx < siPtr->treeChildren.GetSize(); ++bIdx) {
					bPtr = &(siPtr->treeChildren[bIdx]);
					if (bPtr->fileOffset == fileOffset)
						return bPtr;
				}
			}
		}
	}

	return NULL;

}



TreeElement * TreeElement::getLevelByOffset(int fileOffset)
{
	TreeElement *sPtr, *aPtr, *siPtr, *bPtr, *lPtr;

	for (int sIdx=0; sIdx < this->treeChildren.GetSize(); ++sIdx) {
		sPtr = &(this->treeChildren[sIdx]);
		for (int aIdx=0; aIdx < sPtr->treeChildren.GetSize(); ++aIdx) {
			aPtr = &(sPtr->treeChildren[aIdx]);
			for (int siIdx=0; siIdx < aPtr->treeChildren.GetSize(); ++siIdx) {
				siPtr = &(aPtr->treeChildren[siIdx]);
				for (int bIdx=0; bIdx < siPtr->treeChildren.GetSize(); ++bIdx) {
					bPtr = &(siPtr->treeChildren[bIdx]);
					for (int lIdx=0; lIdx < bPtr->treeChildren.GetSize(); ++lIdx) {
						lPtr = &(bPtr->treeChildren[lIdx]);
						if (lPtr->fileOffset == fileOffset)
							return lPtr;
					}
				}
			}
		}
	}

	return NULL;

}


TreeElement * TreeElement::getLocationByOffset(int fileOffset)
{
	TreeElement *sPtr, *aPtr, *siPtr, *bPtr, *lPtr, *loPtr;

	for (int sIdx=0; sIdx < this->treeChildren.GetSize(); ++sIdx) {
		sPtr = &(this->treeChildren[sIdx]);
		for (int aIdx=0; aIdx < sPtr->treeChildren.GetSize(); ++aIdx) {
			aPtr = &(sPtr->treeChildren[aIdx]);
			for (int siIdx=0; siIdx < aPtr->treeChildren.GetSize(); ++siIdx) {
				siPtr = &(aPtr->treeChildren[siIdx]);
				for (int bIdx=0; bIdx < siPtr->treeChildren.GetSize(); ++bIdx) {
					bPtr = &(siPtr->treeChildren[bIdx]);
					for (int lIdx=0; lIdx < bPtr->treeChildren.GetSize(); ++lIdx) {
						lPtr = &(bPtr->treeChildren[lIdx]);
						for (int loIdx=0; loIdx < lPtr->treeChildren.GetSize(); ++loIdx) {
							loPtr = &(lPtr->treeChildren[loIdx]);
							if (loPtr->fileOffset == fileOffset)
								return loPtr;
						}
					}
				}
			}
		}
	}

	return NULL;

}



TreeElement *TreeElement::FindFacilityElement(int parentID, char * childType, TreeElement & changesTree) 
{
	int i,j,k,l,m = 0;
	int parentLevel;
//	int foundParent = 0;

	printf("In Find Parent : %d %s\n",parentID, childType);
	if (strcmp(childType,"SLOTSection") == 0)
		parentLevel = 0;
	else if (strcmp(childType,"SLOTAisle") == 0)
		parentLevel = 1;
	else if (strcmp(childType,"SLOTSide") == 0)
		parentLevel = 2;
	else if (strcmp(childType,"SLOTBay") == 0)
		parentLevel = 3;
	else if (strcmp(childType,"SLOTLevel") == 0)
		parentLevel = 4;
	else if (strcmp(childType,"SLOTLocation") == 0)
		parentLevel = 5;
	else 
		parentLevel = -1;

	/////////////////////////////////////////////////////////////
	//  Run through the current tree until we find the 
	//  parent node that we are looking for.  Note that we only
	//  go as far down as we should based on the level number
	//  assigned by looking at the facility element.  We return a
	//  pointer to a tree node.
	/////////////////////////////////////////////////////////////
	i = j = k = l = m = 0;
	while ( parentLevel >= 1 && i < changesTree.treeChildren.GetSize()) {
		//sections
		if ( parentLevel == 1 && changesTree.treeChildren[i].fileOffset == parentID )
			return &(changesTree.treeChildren[i]);
		j = 0;
		while ( parentLevel >= 2 && j < changesTree.treeChildren[i].treeChildren.GetSize()) {
			//aisles
			if ( parentLevel == 2 && changesTree.treeChildren[i].treeChildren[j].fileOffset == parentID )
				return &(changesTree.treeChildren[i].treeChildren[j]);
			k = 0;
			while ( parentLevel >= 3 && k < changesTree.treeChildren[i].treeChildren[j].treeChildren.GetSize()) {
				//sides
				if ( parentLevel == 3 && changesTree.treeChildren[i].treeChildren[j].treeChildren[k].fileOffset == parentID )
					return &(changesTree.treeChildren[i].treeChildren[j].treeChildren[k]);
				l = 0;
				while ( parentLevel >= 4 && l < changesTree.treeChildren[i].treeChildren[j].treeChildren[k].treeChildren.GetSize()) {
					//bays 
					if ( parentLevel == 4 && changesTree.treeChildren[i].treeChildren[j].treeChildren[k].treeChildren[l].fileOffset == parentID )
						return &(changesTree.treeChildren[i].treeChildren[j].treeChildren[k].treeChildren[l]);
					m = 0;
					while ( parentLevel >= 5 && m < changesTree.treeChildren[i].treeChildren[j].treeChildren[k].treeChildren[l].treeChildren.GetSize()) {
						//levels
						if ( parentLevel == 5 && changesTree.treeChildren[i].treeChildren[j].treeChildren[k].treeChildren[l].treeChildren[m].fileOffset == parentID )
							return &(changesTree.treeChildren[i].treeChildren[j].treeChildren[k].treeChildren[l].treeChildren[m]);
						m++;
					}
					l++;
				}
				k++;
			}
			j++;
		}
		i++;
	}
	if ( parentLevel == 0 && parentID == changesTree.fileOffset)
		return &(changesTree);
	return NULL;
}

void TreeElement::clear()
{
	treeChildren.RemoveAll();
	elementDBID = 0;
	fileOffset = 1;
	strcpy(acadHandle,"");
	strcpy(facilityElement,"SLOTFacility");

	DeletedSectionMap.RemoveAll();
	DeletedAisleMap.RemoveAll();
	DeletedSideMap.RemoveAll();
	DeletedBayMap.RemoveAll();
}

TreeElement * TreeElement::getAisleByPickPathHandle(const CString &handle)
{
	int i, j;

	for ( i = 0; i < this->treeChildren.GetSize(); i++ ) {
		for ( j = 0; j < this->treeChildren[i].treeChildren.GetSize(); j++ ) {
			if ( strcmp(this->treeChildren[i].treeChildren[j].acadHandle, handle) == 0 )
				return &this->treeChildren[i].treeChildren[j];
		}
	}
	
	// If the aisle wasn't in the current btree, add it from the database
	try {
		if ( bTreeHelper.UpdateBTWithAisleByPickPathAcadHandle(handle, *this) < 0 ) {
			controlService.Log("", "Error in UpdateBTWithAisleByPickPathAcadHandle for %s\n", handle);
			return NULL;
		}
	}
	catch(...) {
		controlService.Log("", "Generic exception in UpdateBTWithAisleByPickPathAcadHandle for %s\n",handle);
		return NULL;
	}
	
	
	// Now that the aisle has been added from the database, find it in the btree and clear out the pick path
	for ( i = 0; i < this->treeChildren.GetSize(); i++ ) {
		for ( j = 0; j < this->treeChildren[i].treeChildren.GetSize(); j++ ) {
			if ( strcmp(this->treeChildren[i].treeChildren[j].acadHandle, handle) == 0 )
				return &this->treeChildren[i].treeChildren[j];
		}
	}
	
	return NULL;
}

TreeElement * TreeElement::getAisleByConnectedPickPathHandle(const CString &handle)
{
	int i, j;
	qqhSLOTAisle aisle;

	for ( i = 0; i < this->treeChildren.GetSize(); i++ ) {
		for ( j = 0; j < this->treeChildren[i].treeChildren.GetSize(); j++ ) {
			bTreeHelper.GetBtAisle(this->treeChildren[i].treeChildren[j].fileOffset, aisle);
			if ( strcmp(aisle.getPickPath().getConAcadHandle(), handle) == 0 )
				return &this->treeChildren[i].treeChildren[j];
		}
	}
	
	// If the aisle wasn't in the current btree, add it from the database
	try {
		if ( bTreeHelper.UpdateBTWithAisleByConPickPathAcadHandle(handle, *this) < 0 ) {
			controlService.Log("", "Error in UpdateBTWithAisleByConPickPathAcadHandle for %s\n", handle);
			return NULL;
		}
	}
	catch(...) {
		controlService.Log("", "Generic exception in UpdateBTWithAisleByConPickPathAcadHandle for %s\n", handle);
		return NULL;
	}
	
	
	// Now that the aisle has been added from the database, find it in the btree and clear out the pick path
	for ( i = 0; i < this->treeChildren.GetSize(); i++ ) {
		for ( j = 0; j < this->treeChildren[i].treeChildren.GetSize(); j++ ) {
			bTreeHelper.GetBtAisle(this->treeChildren[i].treeChildren[j].fileOffset, aisle);
			if ( strcmp(aisle.getPickPath().getConAcadHandle(), handle) == 0 )
				return &this->treeChildren[i].treeChildren[j];
		}
	}
	
	return NULL;
}

// BayProfile.h: interface for the CBayProfile class.
//
//////////////////////////////////////////////////////////////////////

#if !defined(AFX_BAYPROFILE_H__B3D00EC3_DCDF_11D4_9EC1_00C04FAC149C__INCLUDED_)
#define AFX_BAYPROFILE_H__B3D00EC3_DCDF_11D4_9EC1_00C04FAC149C__INCLUDED_

#if _MSC_VER > 1000
#pragma once
#endif // _MSC_VER > 1000
#include "3DPoint.h"
#include "LevelProfile.h"
#include "BayRule.h"
#include <dbsymtb.h>
#include <aced.h>
#include <direct.h>
#include "codes.h"
#include <acdb.h>
#include <dbents.h>

class CBayProfile : public CObject  
{
public:
	AcDbObjectId CreateAsBlock(const CString& blockName, double leftUprightWidth, double rightUprightWidth);
	int ResetRules();
	int CalculateExtendedValues(BOOL checkOnly, int ruleIdx = -1, int facingIdx = -1);

	int Draw(BOOL currentDB, BOOL doSave);
	int DrawByPosition(AcDbDatabase *pDatabase, const C3DPoint &centerPoint, 
		double rotation, double leftBarWidth=-1, double rightBarWidth=-1);

	int ResetLocationSizes();
	void ResetLevels(BOOL bIn);

	CBayProfile();
	virtual ~CBayProfile();
	CBayProfile& operator=(const CBayProfile &other);
	CBayProfile(const CBayProfile &other);
	BOOL operator==(const CBayProfile& other);
	BOOL operator!=(const CBayProfile& other) { return !(*this == other);};
	int Parse(CString &line);

	long m_BayProfileDBId;
	CString m_Description;
	C3DPoint m_Coordinates;
	double m_Width;
	double m_Depth;
	double m_Height;
	int m_BayType;
	int m_PalletHeight;
	int m_PalletDepth;		// pallets deep for floor, drive-in, pallet
	int m_FlowDifference;
	double m_WeightCapacity;
	double m_RackCost;
	double m_UprightWidth;
	double m_UprightHeight;
	int m_IsHazard;
	int m_AllowBaySpanning;
	int m_ExcludeFromOptimization;
	int m_IsFloating;
	double m_PalletSpace;
	int m_Active;

	CTypedPtrArray<CObArray, CLevelProfile*> m_LevelProfileList;
	CTypedPtrArray<CObArray, CBayRule*> m_BayRuleList;
	
	static CString ConvertBayTypeToPath(int bayType);
	CString ConvertBayTypeToPath();

	static void ConvertBayType(int bayType, CString &description);
	static CString ConvertBayType(int bayType);
	CString ConvertBayType();

	static int ConvertBayType(CString &description);

	typedef enum {
		loadBayOnly = 0,
		loadLevels = 1,
		loadLocations = 2,
		loadRules = 4,
		loadInfo = 8,
		loadLabor = 16,
		loadAll = 31
	} enumLoadFlags;

private:
	Acad::ErrorStatus DrawDriveInLevels();
	Acad::ErrorStatus DrawLevels();
	Acad::ErrorStatus DrawCrossbars(BOOL bEnd);
	Acad::ErrorStatus DrawUprights(BOOL bEnd);
	Acad::ErrorStatus DrawPalletLevels();
	Acad::ErrorStatus DrawBin(BOOL bEndUprights);
	Acad::ErrorStatus DrawFlow(BOOL bEndUprights);
	Acad::ErrorStatus DrawFloor();
	Acad::ErrorStatus DrawDriveIn(BOOL bEndUprights);
	Acad::ErrorStatus DrawPallet(BOOL bEndUprights);

	int CreatePath();
	int CalculateCube(int ruleIdx, double &selectCube, double &reserveCube);
	AcDbDatabase *m_pDatabase;
    AcDbBlockTable *m_pBlockTable;
    AcDbBlockTableRecord *m_pBlockTableRecord;
	AcDbObjectId m_BlockId;

};

#endif // !defined(AFX_BAYPROFILE_H__B3D00EC3_DCDF_11D4_9EC1_00C04FAC149C__INCLUDED_)

#if !defined(AFX_BAYPROFILEPALLETPAGE_H__00A27A3E_83CA_4240_89A0_03506189F806__INCLUDED_)
#define AFX_BAYPROFILEPALLETPAGE_H__00A27A3E_83CA_4240_89A0_03506189F806__INCLUDED_

#if _MSC_VER > 1000
#pragma once
#endif // _MSC_VER > 1000
// BayProfilePalletPage.h : header file
//
#include "BayProfileTopViewButton.h"
#include "BayProfileSideViewButton.h"
#include "BayProfile.h"

/////////////////////////////////////////////////////////////////////////////
// CBayProfilePalletPage dialog

class CBayProfilePalletPage : public CPropertyPage
{
	DECLARE_DYNCREATE(CBayProfilePalletPage)

// Construction
public:
	CBayProfilePalletPage();
	~CBayProfilePalletPage();

// Dialog Data
	//{{AFX_DATA(CBayProfilePalletPage)
	enum { IDD = IDD_BAY_PROFILE_PALLET_ATTRIBUTES };
	CBayProfileTopViewButton	m_TopViewButton;
	CBayProfileSideViewButton	m_SideViewButton;
	double	m_BayDepth;
	double	m_BayWidth;
	int		m_NumberOfPallets;
	double	m_BayHeight;
	double	m_UprightHeight;
	double	m_UprightWidth;
	double	m_WeightCapacity;
	double	m_PalletSpace;
	//}}AFX_DATA


// Overrides
	// ClassWizard generate virtual function overrides
	//{{AFX_VIRTUAL(CBayProfilePalletPage)
	public:
	virtual BOOL OnKillActive();
	virtual BOOL OnSetActive();
	protected:
	virtual void DoDataExchange(CDataExchange* pDX);    // DDX/DDV support
	virtual BOOL OnCommand(WPARAM wParam, LPARAM lParam);
	virtual BOOL OnNotify(WPARAM wParam, LPARAM lParam, LRESULT* pResult);
	//}}AFX_VIRTUAL

// Implementation
protected:
	// Generated message map functions
	//{{AFX_MSG(CBayProfilePalletPage)
	virtual BOOL OnInitDialog();
	afx_msg BOOL OnHelpInfo(HELPINFO* pHelpInfo);
	//}}AFX_MSG
	DECLARE_MESSAGE_MAP()

private:
	BOOL Validate();
	CBayProfile *m_pBayProfile;
	BOOL m_Validating;
};

//{{AFX_INSERT_LOCATION}}
// Microsoft Visual C++ will insert additional declarations immediately before the previous line.

#endif // !defined(AFX_BAYPROFILEPALLETPAGE_H__00A27A3E_83CA_4240_89A0_03506189F806__INCLUDED_)

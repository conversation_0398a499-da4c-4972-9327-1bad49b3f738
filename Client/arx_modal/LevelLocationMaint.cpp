// LevelLocationMaint.cpp : implementation file
//

#include "stdafx.h"
#include "modal.h"
#include "LevelLocationMaint.h"
#include "HelpService.h"

#include <dbsymtb.h>

#ifdef _DEBUG
#define new DEBUG_NEW
#undef THIS_FILE
static char THIS_FILE[] = __FILE__;
#endif

extern CHelpService helpService;

/////////////////////////////////////////////////////////////////////////////
// CLevelLocationMaint dialog


CLevelLocationMaint::CLevelLocationMaint(CWnd* pParent /*=NULL*/)
	: CDialog(CLevelLocationMaint::IDD, pParent)
{
	//{{AFX_DATA_INIT(CLevelLocationMaint)
		// NOTE: the ClassWizard will add member initialization here
	//}}AFX_DATA_INIT

	m_levelLocationList.RemoveAll();
}


void CLevelLocationMaint::DoDataExchange(CDataExchange* pDX)
{
	CDialog::DoDataExchange(pDX);
	//{{AFX_DATA_MAP(CLevelLocationMaint)
	DDX_Control(pDX, IDC_LEVELLOC_TREE, m_ctlLevelLocTree);
	//}}AFX_DATA_MAP
}


BEGIN_MESSAGE_MAP(CLevelLocationMaint, CDialog)
	//{{AFX_MSG_MAP(CLevelLocationMaint)
	ON_BN_CLICKED(IDC_CHOOSELEVELLOCHELP, OnChooselevellochelp)
	ON_WM_HELPINFO()
	ON_NOTIFY(NM_DBLCLK, IDC_LEVELLOC_TREE, OnDblclkLevellocTree)
	//}}AFX_MSG_MAP
END_MESSAGE_MAP()




/////////////////////////////////////////////////////////////////////////////
// CLevelLocationMaint message handlers


BOOL CLevelLocationMaint::OnInitDialog() 
{
	CDialog::OnInitDialog();

	CWinApp * currentApp;
	HICON treeIcon;
	int resImage;
	
	m_selectedDescription = "";
	m_selectedType        = "";
	m_selectedFileOffset  = -1;

	//*****************************************************
	//insert the levels and locations into the Tree control
	//*****************************************************

	// Create the image list needed for the tree control
	m_ImageList.Create(::GetSystemMetrics(SM_CXICON),
		::GetSystemMetrics(SM_CYICON), TRUE, 20, 3);
	currentApp = AfxGetApp();
	treeIcon = currentApp->LoadIcon(IDI_LEVELICON);
	if ( treeIcon == NULL ) 
	{
		;
	}
	else 
	{
		resImage = m_ImageList.Add(treeIcon);
		::DeleteObject(treeIcon);
	}
	treeIcon = currentApp->LoadIcon(IDI_LOCATIONICON);
	if ( treeIcon == NULL ) 
	{
		;
	}
	else 
	{
		resImage = m_ImageList.Add(treeIcon);
		::DeleteObject(treeIcon);
	}

	// Set the image list of the tree control
	m_ctlLevelLocTree.SetImageList( &m_ImageList,TVSIL_NORMAL);

	TV_INSERTSTRUCT TreeCtrlItem;

	//parse through array and insert items into the tree control
	// Initialize and insert items for each level/location.
	int prevLevelFileOffset = 0;

	for (int i = 0; i < m_levelLocationList.GetSize(); i++)
	{
		HTREEITEM hTreeItem;
		HTREEITEM hTreeItem2;
		CLevelLocationInfo cInfo;
		ParseInfo(m_levelLocationList.GetAt(i), cInfo);
		int levelFileOffset    = cInfo.m_levelFileOffset;
		int locationFileOffset = cInfo.m_locationFileOffset;
		CString locationDesc = cInfo.m_locationDescription;


		//if a new level was encountered, then insert level as a root node
		if (levelFileOffset != prevLevelFileOffset)
		{
			// Insert bay type as Root node
			TreeCtrlItem.hParent      = TVI_ROOT;
			TreeCtrlItem.hInsertAfter = TVI_LAST;
			TreeCtrlItem.item.mask = TVIF_TEXT |TVIF_SELECTEDIMAGE|TVIF_IMAGE|TVIF_PARAM;
			TreeCtrlItem.item.pszText = cInfo.m_levelDescription.GetBuffer(0);
			TreeCtrlItem.item.iImage  = 0;
			TreeCtrlItem.item.iSelectedImage = 0;
			TreeCtrlItem.item.lParam = levelFileOffset;
			hTreeItem = m_ctlLevelLocTree.InsertItem(&TreeCtrlItem);
			prevLevelFileOffset = levelFileOffset;
		}

		// Insert the location under the current root node
		if (locationDesc != "")
		{
			TreeCtrlItem.hParent      = hTreeItem;
			TreeCtrlItem.item.mask = TVIF_TEXT|TVIF_IMAGE|TVIF_SELECTEDIMAGE|TVIF_PARAM;
			TreeCtrlItem.item.pszText = locationDesc.GetBuffer(0);
			TreeCtrlItem.item.iImage  = 1;
			TreeCtrlItem.item.iSelectedImage = 1;
			TreeCtrlItem.item.lParam  = locationFileOffset;
			hTreeItem2 = m_ctlLevelLocTree.InsertItem(&TreeCtrlItem);
		}
		m_ctlLevelLocTree.Expand(hTreeItem,TVE_EXPAND);

	}
	
	m_ctlLevelLocTree.SetScrollPos(SB_VERT, 0, FALSE);

	return TRUE;  // return TRUE unless you set the focus to a control
	              // EXCEPTION: OCX Property Pages should return FALSE
}

void CLevelLocationMaint::ParseInfo(CString s, CLevelLocationInfo &c)
{
	char *string;
	char *ptr;

	string = s.GetBuffer(0);
	ptr = strtok(string, "|");
	c.m_levelDescription = ptr;
	ptr = strtok(NULL, "|");
	c.m_levelFileOffset = atoi(ptr);
	ptr = strtok(NULL, "|");
	c.m_locationDescription = ptr;
	ptr = strtok(NULL, "|");
	c.m_locationFileOffset = atoi(ptr);

	s.ReleaseBuffer();

	return;
}


void CLevelLocationMaint::OnCancel() 
{
	// TODO: Add extra cleanup here
	
	CDialog::OnCancel();
}


void CLevelLocationMaint::OnOK() 
{

	CLevelLocationInfo cInfo;
	
	//********************************************************
	// Check to see if an item is selected in tree control
	//********************************************************

	HTREEITEM hItem = m_ctlLevelLocTree.GetSelectedItem( );	
	if (hItem == NULL)
	{
		m_selectedDescription = "";
		m_selectedType = "";
		AfxMessageBox("Please select a level or a location!");
		return;
	}

	// Get the item detail - copies into szBuffer.
	char szBuffer[256];
	TV_ITEM item;
	item.hItem = hItem;
	item.pszText = szBuffer;
	item.cchTextMax = 256;
	item.mask = TVIF_TEXT | TVIF_PARAM;
	if (m_ctlLevelLocTree.GetItem(&item) == 0)
	{
		AfxMessageBox("GetItem failed!");
		m_selectedDescription = "";
		m_selectedType = "";
		return;
	}
	
	m_selectedDescription = szBuffer;

	HTREEITEM hItem2 = m_ctlLevelLocTree.GetParentItem(hItem);
	if (hItem2 == NULL)
		m_selectedType = "Level";
	else
		m_selectedType = "Location";

	// fixed bug - was using description to find offset, but
	// description is not always unique so use lParam to store
	// actual offset with the item
	m_selectedFileOffset = item.lParam;

//	ads_printf("lparam = %d\n", m_selectedFileOffset);

	CDialog::OnOK();
}


void CLevelLocationMaint::OnChooselevellochelp() 
{
	helpService.ShowScreenHelp(IDD);
	
}


BOOL CLevelLocationMaint::OnHelpInfo(HELPINFO* pHelpInfo) 
{
	helpService.ShowFieldHelp(IDD, pHelpInfo->iCtrlId);

	return TRUE;	

}



void CLevelLocationMaint::OnDblclkLevellocTree(NMHDR* pNMHDR, LRESULT* pResult) 
{
	UNREFERENCED_PARAMETER(pNMHDR);
	
	OnOK();
	
	*pResult = 0;
}

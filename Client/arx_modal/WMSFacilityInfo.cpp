// WMSFacilityInfo.cpp: implementation of the CWMSFacilityInfo class.
//
//////////////////////////////////////////////////////////////////////

#include "stdafx.h"
#include "modal.h"
#include "WMSFacilityInfo.h"
#include "UtilityHelper.h"

#ifdef _DEBUG
#undef THIS_FILE
static char THIS_FILE[]=__FILE__;
#define new DEBUG_NEW
#endif

extern CUtilityHelper utilityHelper;
//////////////////////////////////////////////////////////////////////
// Construction/Destruction
//////////////////////////////////////////////////////////////////////

CWMSFacilityInfo::CWMSFacilityInfo()
{

}

CWMSFacilityInfo::~CWMSFacilityInfo()
{

}

CWMSFacilityInfo::Parse(CString &line)
{
	CStringArray strings;
	utilityHelper.ParseString(line, "|", strings);

	for (int i=0; i < strings.GetSize(); ++i) {
		switch (i) {
		case 0:
			m_FacilityDBId = atol(strings[i]);
			break;
		case 1:
			m_FacilityName = strings[i];
			break;
		case 2:
			m_SectionDBIdList.Add(atol(strings[i]));
			break;
		case 3:
			m_SectionNameList.Add(strings[i]);
			break;
		}
	}

	return 0;

}

// ProductGroupCriteriaMaintenance.cpp : implementation file
//

#include "stdafx.h"
#include "ssa_exception.h"
#include "HelpService.h"

#include "GenericPropertySheet.h"
#include "ProductGroupCriteriaMaintenance.h"
#include "ProductGroupCriteriaPropertiesPage.h"
#include "ProductGroupCriteriaListPage.h"
#include "Processing.h"
#include "ProductGroupFrame.h"
#include "ProductGroupNavigator.h"
#include "UtilityHelper.h"

#ifdef _DEBUG
#define new DEBUG_NEW
#undef THIS_FILE
static char THIS_FILE[] = __FILE__;
#endif

/////////////////////////////////////////////////////////////////////////////
// CProductGroupCriteriaMaintenance dialog
extern CHelpService helpService;
extern CProductGroupFrame *m_ProductGroupFrame;
extern CUtilityHelper utilityHelper;

IMPLEMENT_DYNCREATE(CProductGroupCriteriaMaintenance, CDialog)

CProductGroupCriteriaMaintenance::CProductGroupCriteriaMaintenance(CWnd* pParent /*=NULL*/)
	: CDialog(CProductGroupCriteriaMaintenance::IDD, pParent)
{
	//{{AFX_DATA_INIT(CProductGroupCriteriaMaintenance)
	//}}AFX_DATA_INIT
	m_SomethingChanged = FALSE;

}

CProductGroupCriteriaMaintenance::~CProductGroupCriteriaMaintenance()
{
//	for (int i=0; i < m_CriteriaList.GetSize(); ++i)
//		delete m_CriteriaList[i];

}


void CProductGroupCriteriaMaintenance::DoDataExchange(CDataExchange* pDX)
{
	CDialog::DoDataExchange(pDX);
	//{{AFX_DATA_MAP(CProductGroupCriteriaMaintenance)
	DDX_Control(pDX, IDC_CRITERIA_LIST, m_CriteriaListCtrl);
	//}}AFX_DATA_MAP
}


BEGIN_MESSAGE_MAP(CProductGroupCriteriaMaintenance, CDialog)
	//{{AFX_MSG_MAP(CProductGroupCriteriaMaintenance)
	ON_BN_CLICKED(IDC_ADD, OnAdd)
	ON_BN_CLICKED(IDC_DELETE, OnDelete)
	ON_BN_CLICKED(IDC_PROPERTIES, OnProperties)
	ON_BN_CLICKED(IDHELP, OnHelp)
	ON_WM_CONTEXTMENU()
	ON_NOTIFY(NM_DBLCLK, IDC_CRITERIA_LIST, OnDblclkCriteriaList)
	ON_COMMAND(ID_CRITERIAMENU_ADD, OnAdd)
	ON_COMMAND(ID_CRITERIAMENU_DELETE, OnDelete)
	ON_COMMAND(ID_CRITERIAMENU_PROPERTIES, OnProperties)
	ON_WM_HELPINFO()
	//}}AFX_MSG_MAP
END_MESSAGE_MAP()

/////////////////////////////////////////////////////////////////////////////
// CProductGroupCriteriaMaintenance message handlers

BOOL CProductGroupCriteriaMaintenance::OnInitDialog() 
{
	CDialog::OnInitDialog();
	
	int n, i;
	CStringArray criteriaList;
	CRect r;

	n = m_CriteriaListCtrl.GetHeaderCtrl()->GetItemCount();
	for (i=0; i < n; ++i)
		m_CriteriaListCtrl.DeleteColumn(0);

	m_CriteriaListCtrl.GetClientRect(&r);
	m_CriteriaListCtrl.InsertColumn(0, "Name", LVCFMT_LEFT, r.Width()/3, 0);
	m_CriteriaListCtrl.InsertColumn(1, "Attribute", LVCFMT_LEFT, r.Width()/3, 0);
	m_CriteriaListCtrl.InsertColumn(2, "Type", LVCFMT_LEFT, r.Width()/3, 0);

	DisplayCriteria();

	return TRUE;  // return TRUE unless you set the focus to a control
	              // EXCEPTION: OCX Property Pages should return FALSE
}


void CProductGroupCriteriaMaintenance::OnAdd() 
{
	int rc, nItem;
	CGenericPropertySheet criteriaSheet;
	CProductGroupCriteriaPropertiesPage propertiesPage;
	CProductGroupCriteriaListPage listPage;
	CProductGroupCriteria *pCriteria;
	LVITEM lvItem;
	CString attribute;

	criteriaSheet.SetWindowText("Add Product Group Criteria");
	criteriaSheet.m_psh.dwFlags |= PSH_NOAPPLYNOW;
	criteriaSheet.AddPage(&propertiesPage);

	pCriteria = new CProductGroupCriteria;

	propertiesPage.m_Criteria = pCriteria;
	listPage.m_Criteria = pCriteria;
	propertiesPage.m_ListPage = &listPage;
	listPage.m_ProductGroupDataService = m_ProductGroupDataService;
	propertiesPage.m_ProductGroupDataService = m_ProductGroupDataService;

	BOOL bDone = FALSE;

	while (! bDone) {

		bDone = TRUE;
		
		try {
			rc = criteriaSheet.DoModal();
		}
		catch (Ssa_Exception e) {
			utilityHelper.ProcessError("Error displaying Product Group Criteria Maintenance.", &e);
			delete pCriteria;
			return;
		}
		catch (...) {
			utilityHelper.ProcessError("Error displaying Product Group Criteria Maintenance.");
			delete pCriteria;
			return;
		}
		
		if (rc != IDOK) {
			delete pCriteria;
			return;
		}
		
		// Check to see if the name is unique
		for (int i=0; i < m_CriteriaList->GetSize(); ++i) {
			if (pCriteria->m_Name == m_CriteriaList->GetAt(i)->m_Name) {
				AfxMessageBox("The criteria name is already in use.");
				bDone = FALSE;
				criteriaSheet.SetActivePage(0);
				break;
			}
		}
		if (! bDone)
			continue;

		if (pCriteria->m_IsDiscrete) {
			if (pCriteria->m_CriteriaRangeList.GetSize() == 0 ||
				(pCriteria->m_CriteriaRangeList.GetSize() == 1 && 
				pCriteria->m_CriteriaRangeList[0]->m_Description == "Default") ) {
				AfxMessageBox("Please create at least one list element or change the criteria type to range.");
				bDone = FALSE;
				criteriaSheet.SetActivePage(1);
			}
		}
	}

	// Create the default range if necessary
	if (! pCriteria->m_IsDiscrete && pCriteria->m_CriteriaRangeList.GetSize() == 0) {
		CProductGroupCriteriaRange *pRange = new CProductGroupCriteriaRange;
		pRange->m_CriteriaRangeDBID = 0;
		pRange->m_Description = "Default";
		pCriteria->m_CriteriaRangeList.Add(pRange);
		CProductGroupCriteriaQuery *pQuery = new CProductGroupCriteriaQuery;
		pQuery->m_Attribute = pCriteria->m_Attribute;
		pQuery->m_AttributeType = pCriteria->m_AttributeType;
		pQuery->m_Operator = " ";
		pQuery->m_Value = " ";
		pQuery->m_Sequence = 1;
		pQuery->m_Precedence = 0;
		pQuery->m_Conjunction = "AND";
		pRange->m_CriteriaQueryList.Add(pQuery);	
	}

	CProcessing *dlg = new CProcessing;
	dlg->Create(IDD_PROCESSING, this);
	dlg->m_StatusText = "Storing Criteria";
	dlg->UpdateData(FALSE);
	dlg->CenterWindow();
	dlg->ShowWindow(SW_SHOW);
	dlg->UpdateWindow();

	// Store new criteria
	try {
		CWaitCursor cwc;
		rc = m_ProductGroupDataService->StoreCriteria(*pCriteria);
	}
	catch (Ssa_Exception e) {
		utilityHelper.ProcessError("Error storing criteria.", &e);
	}
	catch (...) {
		utilityHelper.ProcessError("Error storing criteria.");
	}
	
	dlg->DestroyWindow();

	if (rc >= 0) {
		m_SomethingChanged = TRUE;
		m_CriteriaList->Add(pCriteria);

		lvItem.mask = LVIF_TEXT|LVIF_PARAM;
		lvItem.pszText = pCriteria->m_Name.GetBuffer(0);
		pCriteria->m_Name.ReleaseBuffer();
		lvItem.lParam = pCriteria->m_CriteriaDBID;
		lvItem.iItem = m_CriteriaListCtrl.GetItemCount();
		lvItem.iSubItem = 0;
		nItem = m_CriteriaListCtrl.InsertItem(&lvItem);
		
		lvItem.mask = LVIF_TEXT;
		attribute = pCriteria->GetAttribute();
		lvItem.pszText = attribute.GetBuffer(0);
		attribute.ReleaseBuffer();
		lvItem.iItem = nItem;
		lvItem.iSubItem = 1;
		m_CriteriaListCtrl.SetItem(&lvItem);
		
		if (pCriteria->m_IsDiscrete)
			lvItem.pszText = "List";
		else
			lvItem.pszText = "Range";
		lvItem.iItem = nItem;
		lvItem.iSubItem = 2;
		m_CriteriaListCtrl.SetItem(&lvItem);

		UpdateData(FALSE);
	}
	else
		delete pCriteria;

	return;
	
}

void CProductGroupCriteriaMaintenance::OnDelete() 
{
	int rc, curSel, idx;

	curSel = GetCurSel();

	if (curSel < 0) {
		AfxMessageBox("Please select an item from the criteria list.");
		return;
	}

	if (AfxMessageBox("Are you sure you wish to delete this criteria?", MB_YESNO) != IDYES)
		return;

	for (idx=0; idx < m_CriteriaList->GetSize(); ++idx) {
		if ((*m_CriteriaList)[idx]->m_CriteriaDBID == (long)m_CriteriaListCtrl.GetItemData(curSel))
			break;
	}

	try {
		rc = m_ProductGroupDataService->DeleteCriteria((*m_CriteriaList)[idx]->m_CriteriaDBID);
	}
	catch (Ssa_Exception e) {
		utilityHelper.ProcessError("Error deleting criteria.", &e);
		return;
	}
	catch (...) {
		utilityHelper.ProcessError("Error deleting criteria.");
		return;
	}

	m_SomethingChanged = TRUE;

	CProductGroupQuery *pQuery;
	for (int i=0; i < m_ProductGroupList->GetSize(); ++i) {
		for (int j=0; j < m_ProductGroupList->GetAt(i)->m_QueryList.GetSize(); ++j) {
			pQuery = m_ProductGroupList->GetAt(i)->m_QueryList[j];
			if (pQuery->m_CriteriaDBID == m_CriteriaList->GetAt(idx)->m_CriteriaDBID) {
				delete pQuery;
				m_ProductGroupList->GetAt(i)->m_QueryList.RemoveAt(j);
			}
		}
	}

	delete m_CriteriaList->GetAt(idx);
	m_CriteriaList->RemoveAt(idx);
	m_CriteriaListCtrl.DeleteItem(curSel);

	UpdateData(FALSE);

	return;

}

void CProductGroupCriteriaMaintenance::OnProperties() 
{
	int rc, curSel, idx;
	CGenericPropertySheet criteriaSheet;
	CProductGroupCriteriaPropertiesPage propertiesPage;
	CProductGroupCriteriaListPage listPage;
	CProductGroupCriteria *tempCriteria;
	CString title;

	curSel = GetCurSel();

	if (curSel < 0) {
		AfxMessageBox("Please select an item from the criteria list.");
		return;
	}

	for (idx=0; idx < m_CriteriaList->GetSize(); ++idx) {
		if (m_CriteriaList->GetAt(idx)->m_CriteriaDBID == (long)m_CriteriaListCtrl.GetItemData(curSel))
			break;
	}

	// For now, there will only be a single CriteriaQuery for each range
	// and the attribute will be the same for all of them.  Later, each
	// range can have it's own attribute, possibly more than one

	title = "Product Group Criteria Properties";
	title += " - ";
	title += m_CriteriaList->GetAt(idx)->GetAttribute();
	criteriaSheet.SetTitle(title);

	criteriaSheet.m_psh.dwFlags |= PSH_NOAPPLYNOW;

	// Load up criteria pages with current selection
	if (m_CriteriaList->GetAt(idx)->m_CriteriaValueList.GetSize() == 0)
		m_ProductGroupDataService->LoadCriteriaValues(*(m_CriteriaList->GetAt(idx)));

	// Make a copy of the one they are editing in case they cancel
	tempCriteria = new CProductGroupCriteria;
	*tempCriteria = *(m_CriteriaList->GetAt(idx));

	propertiesPage.m_Criteria = tempCriteria;
	listPage.m_Criteria = tempCriteria;
	propertiesPage.m_ListPage = &listPage;
	listPage.m_ProductGroupDataService = m_ProductGroupDataService;
	propertiesPage.m_ProductGroupDataService = m_ProductGroupDataService;

	criteriaSheet.AddPage(&propertiesPage);
	if (tempCriteria->m_IsDiscrete)
		criteriaSheet.AddPage(&listPage);

	BOOL bDone = FALSE;

	while (! bDone) {
		bDone = TRUE;

		try {
			rc = criteriaSheet.DoModal();
			if (rc != IDOK) {
				delete tempCriteria;
				return;
			}
		}
		catch (Ssa_Exception e) {
			utilityHelper.ProcessError("Error displaying Product Group Criteria Maintenance.", &e);
			delete tempCriteria;
			return;
		}
		catch (...) {
			utilityHelper.ProcessError("Error displaying Product Group Criteria Maintenance.");
			delete tempCriteria;
			return;
		}
		
		for (int i=0; i < m_CriteriaList->GetSize(); ++i) {
			if (i != idx && tempCriteria->m_Name == m_CriteriaList->GetAt(i)->m_Name) {
				AfxMessageBox("The criteria name is already in use.");
				bDone = FALSE;
				criteriaSheet.SetActivePage(0);
				break;
			}
		}

		if (! bDone)
			continue;

		if (tempCriteria->m_IsDiscrete) {
			if (tempCriteria->m_CriteriaRangeList.GetSize() == 0 ||
				(tempCriteria->m_CriteriaRangeList.GetSize() == 1 && 
				tempCriteria->m_CriteriaRangeList[0]->m_Description == "Default") ) {
				AfxMessageBox("Please create at least one list element or change the criteria type to range.");
				bDone = FALSE;
				criteriaSheet.SetActivePage(1);
			}
		}
		
	}
	// If they are equal don't bother to update
	if (m_CriteriaList->GetAt(idx)->IsEqual(*tempCriteria) ) {
		delete tempCriteria;
		return;
	}

	// Create the default range if necessary
	if (! tempCriteria->m_IsDiscrete && tempCriteria->m_CriteriaRangeList.GetSize() == 0) {
		CProductGroupCriteriaRange *pRange = new CProductGroupCriteriaRange;
		pRange->m_CriteriaRangeDBID = 0;
		pRange->m_Description = "Default";
		tempCriteria->m_CriteriaRangeList.Add(pRange);
		CProductGroupCriteriaQuery *pQuery = new CProductGroupCriteriaQuery;
		pQuery->m_Attribute = tempCriteria->m_Attribute;
		pQuery->m_AttributeType = tempCriteria->m_AttributeType;
		pQuery->m_Operator = " ";
		pQuery->m_Value = " ";
		pQuery->m_Sequence = 1;
		pQuery->m_Precedence = 0;
		pQuery->m_Conjunction = "AND";
		pRange->m_CriteriaQueryList.Add(pQuery);	
	}

	CProcessing *dlg = new CProcessing;
	dlg->Create(IDD_PROCESSING, this);
	dlg->m_StatusText = "Storing Criteria";
	dlg->UpdateData(FALSE);
	dlg->CenterWindow();
	dlg->ShowWindow(SW_SHOW);
	dlg->UpdateWindow();
	
	try {
		CWaitCursor cwc;
		rc = m_ProductGroupDataService->StoreCriteria(*tempCriteria);
		if (rc < 0) {
			dlg->DestroyWindow();
			AfxMessageBox("Error storing criteria.");
			delete tempCriteria;
			return;
		}
	}
	catch (Ssa_Exception e) {
		dlg->DestroyWindow();
		utilityHelper.ProcessError("Error storing criteria.", &e);
		delete tempCriteria;
		return;
	}
	catch (...) {
		dlg->DestroyWindow();
		utilityHelper.ProcessError("Error storing criteria.");
		delete tempCriteria;
		return;
	}
		
	m_SomethingChanged = TRUE;
	// Delete the old one in that spot
	delete m_CriteriaList->GetAt(idx);
	// Set the new one
	m_CriteriaList->SetAt(idx, tempCriteria);
	m_CriteriaListCtrl.SetItemText(curSel, 0, tempCriteria->m_Name);
	m_CriteriaListCtrl.SetItemText(curSel, 1, m_CriteriaList->GetAt(idx)->GetAttribute());
	if (m_CriteriaList->GetAt(idx)->m_IsDiscrete)
		m_CriteriaListCtrl.SetItemText(curSel, 2, "List");
	else
		m_CriteriaListCtrl.SetItemText(curSel, 2, "Range");
	
	dlg->DestroyWindow();


	return;
	
}


void CProductGroupCriteriaMaintenance::OnOK() 
{
	// Not really anything to do here
	
	CDialog::OnOK();
}

void CProductGroupCriteriaMaintenance::OnCancel() 
{	
	CDialog::OnCancel();
}

void CProductGroupCriteriaMaintenance::OnHelp() 
{
	helpService.ShowScreenHelp(IDD);	
}


//DEL void CProductGroupCriteriaMaintenance::LoadCriteriaDetail(int curSel)
//DEL {
//DEL 	int rc;
//DEL 	CStringArray rangeList, valueList;
//DEL 	CProductGroupCriteriaRange *pRange;
//DEL 	CProductGroupCriteriaValue *pValue;
//DEL 
//DEL 	// See if we've already loaded this one
//DEL 	if ((*m_CriteriaList)[curSel]->m_CriteriaRangeList.GetSize() > 0)
//DEL 		return;
//DEL 
//DEL 	try {
//DEL 		rc = GetCriteriaRangeList((*m_CriteriaList)[curSel]->m_CriteriaDBID, rangeList);
//DEL 	}
//DEL 	catch (Ssa_Exception e) {
//DEL 		utilityHelper.ProcessError("Error loading criteria ranges.", &e);
//DEL 		return;
//DEL 	}
//DEL 	catch (...) {
//DEL 		utilityHelper.ProcessError("Error loading criteria ranges.");
//DEL 		return;
//DEL 	}
//DEL 
//DEL 	for (int i=0; i < rangeList.GetSize(); ++i) {
//DEL 		pRange = new CProductGroupCriteriaRange;
//DEL 		pRange->Parse(rangeList[i]);
//DEL 		(*m_CriteriaList)[curSel]->m_CriteriaRangeList.Add(pRange);
//DEL 		try {
//DEL 			pRange->m_InUse = (GetUsedCriteriaRangeCount(pRange->m_CriteriaRangeDBID) > 0);
//DEL 		}
//DEL 		catch (Ssa_Exception e) {
//DEL 			utilityHelper.ProcessError("Error getting criteria in use flag.", &e);
//DEL 			return;
//DEL 		}
//DEL 		catch (...) {
//DEL 			utilityHelper.ProcessError("Error getting criteria in use flag.");
//DEL 			return;
//DEL 		}
//DEL 	}
//DEL 
//DEL 	try {
//DEL 		rc = GetCriteriaValueList((*m_CriteriaList)[curSel]->m_CriteriaDBID, valueList);
//DEL 	}
//DEL 	catch (Ssa_Exception e) {
//DEL 		utilityHelper.ProcessError("Error loading criteria values.", &e);
//DEL 		return;
//DEL 	}
//DEL 	catch (...) {
//DEL 		utilityHelper.ProcessError("Error loading criteria values.");
//DEL 		return;
//DEL 	}
//DEL 
//DEL 	for (i=0; i < valueList.GetSize(); ++i) {
//DEL 		pValue = new CProductGroupCriteriaValue;
//DEL 		pValue->Parse(valueList[i]);
//DEL 		(*m_CriteriaList)[curSel]->m_CriteriaValueList.Add(pValue);
//DEL 	}
//DEL 
//DEL 		
//DEL 	return;
//DEL 
//DEL }



void CProductGroupCriteriaMaintenance::OnContextMenu(CWnd* pWnd, CPoint point) 
{
	CMenu menu;

	if (pWnd == &m_CriteriaListCtrl) {
		menu.LoadMenu(IDR_CRITERIA_MENU);
		if (GetCurSel() < 0) {
			menu.GetSubMenu(0)->EnableMenuItem(1, MF_BYPOSITION|MF_GRAYED);
			menu.GetSubMenu(0)->EnableMenuItem(2, MF_BYPOSITION|MF_GRAYED);
		}
		else {
			menu.GetSubMenu(0)->EnableMenuItem(1, MF_BYPOSITION|MF_ENABLED);
			menu.GetSubMenu(0)->EnableMenuItem(2, MF_BYPOSITION|MF_ENABLED);
			menu.GetSubMenu(0)->SetDefaultItem(2, TRUE);
		}
		menu.GetSubMenu(0)->TrackPopupMenu(TPM_LEFTALIGN|TPM_RIGHTBUTTON, point.x, point.y, this);
	}
	
}


void CProductGroupCriteriaMaintenance::DisplayCriteria()
{
	LVITEM lvItem;
	int nItem;
	CString attribute;

	for (int i=0; i < m_CriteriaList->GetSize(); ++i) {

		lvItem.mask = LVIF_TEXT | LVIF_PARAM;

		lvItem.pszText = (*m_CriteriaList)[i]->m_Name.GetBuffer(0);
		(*m_CriteriaList)[i]->m_Name.ReleaseBuffer();
		lvItem.lParam = (*m_CriteriaList)[i]->m_CriteriaDBID;
		lvItem.iItem = i;
		lvItem.iSubItem = 0;
		nItem = m_CriteriaListCtrl.InsertItem(&lvItem);
		m_CriteriaListCtrl.SetItemData(nItem, (*m_CriteriaList)[i]->m_CriteriaDBID);

		lvItem.mask = LVIF_TEXT;
		attribute = m_CriteriaList->GetAt(i)->GetAttribute();
		lvItem.pszText = attribute.GetBuffer(0);
		attribute.ReleaseBuffer();
		lvItem.iItem = nItem;
		lvItem.iSubItem = 1;
		m_CriteriaListCtrl.SetItem(&lvItem);

		if ((*m_CriteriaList)[i]->m_IsDiscrete)
			lvItem.pszText = "List";
		else
			lvItem.pszText = "Range";
		lvItem.iSubItem = 2;
		m_CriteriaListCtrl.SetItem(&lvItem);

	}

	return;

}

void CProductGroupCriteriaMaintenance::OnDblclkCriteriaList(NMHDR* pNMHDR, LRESULT* pResult) 
{
	UNREFERENCED_PARAMETER(pNMHDR);

	if (GetCurSel() >= 0)
		OnProperties();

	*pResult = 0;
}

int CProductGroupCriteriaMaintenance::GetCurSel()
{
	POSITION pos;

	pos = m_CriteriaListCtrl.GetFirstSelectedItemPosition();
	if (pos != NULL)
		return m_CriteriaListCtrl.GetNextSelectedItem(pos);
	else
		return -1;
}

BOOL CProductGroupCriteriaMaintenance::Create(LPCTSTR lpszClassName, LPCTSTR lpszWindowName, DWORD dwStyle, const RECT& rect, CWnd* pParentWnd, UINT nID, CCreateContext* pContext) 
{
	UNREFERENCED_PARAMETER(lpszClassName);
	UNREFERENCED_PARAMETER(lpszWindowName);
	UNREFERENCED_PARAMETER(dwStyle);
	UNREFERENCED_PARAMETER(rect);
	UNREFERENCED_PARAMETER(nID);
	UNREFERENCED_PARAMETER(pContext);
	CProductGroupNavigator *pNavigator;
	int id;
	BOOL bReturn;

	pNavigator = (CProductGroupNavigator *)m_ProductGroupFrame->m_Splitter.GetPane(0, 0);

	m_CriteriaList = &pNavigator->m_ProductGroupCriteriaList;
	m_ProductAttributeList = &pNavigator->m_ProductAttributeList;
	m_ProductGroupDataService = &pNavigator->m_ProductGroupDataService;
	m_ProductGroupList = &pNavigator->m_ProductGroupList;

	bReturn = CDialog::Create(IDD, pParentWnd);
	id = m_ProductGroupFrame->m_Splitter.IdFromRowCol(0, 1);

	if ( bReturn )
		::SetWindowLong ( m_hWnd, GWL_ID, id);
	
	return bReturn;
}

void CProductGroupCriteriaMaintenance::PostNcDestroy() 
{
	delete this;
}

BOOL CProductGroupCriteriaMaintenance::ValidateClose()
{
	return TRUE;
}

BOOL CProductGroupCriteriaMaintenance::OnHelpInfo(HELPINFO* pHelpInfo) 
{
	helpService.ShowFieldHelp(IDD, pHelpInfo->iCtrlId);
	return FALSE;
}

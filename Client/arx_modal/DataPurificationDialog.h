//{{AFX_INCLUDES()
#include "DataGrid.h"
//}}AFX_INCLUDES
#if !defined(AFX_DATAPURIFICATIONDIALOG_H__7AF13B40_7478_4516_920A_E4FDF2D704C5__INCLUDED_)
#define AFX_DATAPURIFICATIONDIALOG_H__7AF13B40_7478_4516_920A_E4FDF2D704C5__INCLUDED_
#include "Resource.h"
#include "ProductDataService.h"
#include "ProductMaintenance.h"

#if _MSC_VER > 1000
#pragma once
#endif // _MSC_VER > 1000
// DataPurificationDialog.h : header file
//

/////////////////////////////////////////////////////////////////////////////
// CDataPurificationDialog dialog

#include "DisplayResults.h"	// Added by ClassView
class CDataPurificationDialog : public CDialog
{
// Construction
public:
	CDataPurificationDialog(CWnd* pParent = NULL);   // standard constructor
	~CDataPurificationDialog();
	static UINT DeleteProductThread(LPVOID pParam);
	static void OnLeaveCellDataGrid(void *parent);
	static UINT ExecuteStatementsThread(LPVOID pParam);
// Dialog Data
	//{{AFX_DATA(CDataPurificationDialog)
	enum { IDD = IDD_DATA_PURIFICATION };
	CButton	m_ViewButton;
	CDataGrid	m_DataGrid;
	//}}AFX_DATA


// Overrides
	// ClassWizard generated virtual function overrides
	//{{AFX_VIRTUAL(CDataPurificationDialog)
	protected:
	virtual void DoDataExchange(CDataExchange* pDX);    // DDX/DDV support
	//}}AFX_VIRTUAL

// Implementation
protected:

	// Generated message map functions
	//{{AFX_MSG(CDataPurificationDialog)
	virtual BOOL OnInitDialog();
	afx_msg void OnView();
	afx_msg void OnReset();
	afx_msg void OnHelp();
	afx_msg BOOL OnHelpInfo(HELPINFO* pHelpInfo);
	//}}AFX_MSG
	DECLARE_MESSAGE_MAP()
private:
	void OnDeleteProducts(WPARAM wParam, LPARAM lParam);
	CStringArray m_UsedAttributeList;
	CDisplayResults *m_ProductListDialog;
	void OnCloseDisplay(WPARAM wParam, LPARAM lParam);
	void OnRunPurification(WPARAM wParam, LPARAM lParam);
	void OnProductMaintenance(WPARAM wParam, LPARAM lParam);
	void BuildDisplayLine(CString &line, CProductPack &product);
	void BuildHeaderLine(CString &header);
	void ViewMatchingProducts();
	BOOL ValidateCells();
	void LoadGrid();
	void SetAttribute(int row, CString name, int type, double min, double max, 
		CString initial, CStringArray &listValues, int attributeID, CString helpTopic);
	CProductDataService m_ProductDataService;
	CTypedPtrArray<CObArray, CProductPack*> m_Products;

};

//{{AFX_INSERT_LOCATION}}
// Microsoft Visual C++ will insert additional declarations immediately before the previous line.

#endif // !defined(AFX_DATAPURIFICATIONDIALOG_H__7AF13B40_7478_4516_920A_E4FDF2D704C5__INCLUDED_)

// BayProfileDataService.h: interface for the CBayProfileDataService class.
//
//////////////////////////////////////////////////////////////////////

#if !defined(AFX_BAYPROFILEDATASERVICE_H__62D93937_9B0F_4BAA_8C5E_951FF6BF542E__INCLUDED_)
#define AFX_BAYPROFILEDATASERVICE_H__62D93937_9B0F_4BAA_8C5E_951FF6BF542E__INCLUDED_

#if _MSC_VER > 1000
#pragma once
#endif // _MSC_VER > 1000

#include "qqhclasses.h"
#include "BayProfile.h"

class CBayProfileDataService  
{
public:
	
	int GetSideProfileNamesByBayProfile(int bayProfileDBId, CStringArray &sideNames);
	int GetLocationExternalInfo(qqhSLOTLocation& location);
	int GetLocationProfile(int locProfileDBId, CLocationProfile& locProfile);
	int GetLevelProfile(int levelProfileDBId, CLevelProfile& levelProfile);
	CBayProfileDataService();
	virtual ~CBayProfileDataService();
	
	int SetBayProfileToExcluded(int bayProfileDBId, int flag);
	int UpdateBayProfileName(int bayProfileDBId, const CString &name);
	int GetBayProfileUsedList(CStringArray &bayProfileList);

	BOOL IsBayProfileInUse(int bayProfileDBId);
	BOOL IsBayProfileNameInUse(int bayProfileDBId, const CString &name);

	int GetLevelProfileExternalAttributes(CLevelProfile &levelProfile);
	int GetExternalSystemList(CStringArray &results);
	int GetExternalAttributeList(int externalSystemId, CStringArray &results);

	int GetBayProfileList(CStringArray &bayNameList);
	int GetBayProfile(int bayProfileDBId, CBayProfile& bayProfile, int loadFlags = CBayProfile::loadAll);

	int DeleteBayProfile(int bayProfileDBId);
	int StoreBayProfile(CBayProfile& bayProfile);

	int GetBayProfileNameList(CStringArray &bayNameList);
	int GetBayProfiles(CStringArray &profileList);

private:
	int GetBayProfileUpdateStmts(CBayProfile &bayProfile, CStringArray &stmts);
	int GetBayProfileInsertStmts(CBayProfile &bayProfile, CStringArray &stmts);
	int GetBayProfileDeleteStmts(int bayProfileDBId, CStringArray &stmts);
	int GetBayProfileDeleteStmts(CBayProfile &bayProfile, CStringArray &stmts);
	int InsertBayProfile(CBayProfile &bayProfile);
	int UpdateBayProfile(CBayProfile &bayProfile);
};

#endif // !defined(AFX_BAYPROFILEDATASERVICE_H__62D93937_9B0F_4BAA_8C5E_951FF6BF542E__INCLUDED_)

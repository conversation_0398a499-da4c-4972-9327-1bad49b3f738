// DataGridEdit.cpp : implementation file
//

#include "stdafx.h"
#include "DataGridEdit.h"
#include "DataGrid.h"
#include "UtilityHelper.h"


#ifdef _DEBUG
#define new DEBUG_NEW
#undef THIS_FILE
static char THIS_FILE[] = __FILE__;
#endif

/////////////////////////////////////////////////////////////////////////////
// CDataGridEdit
extern FILE *flog;
extern CUtilityHelper utilityHelper;


CDataGridEdit::CDataGridEdit()
{
}

CDataGridEdit::~CDataGridEdit()
{
}


BEGIN_MESSAGE_MAP(CDataGridEdit, CEdit)
	//{{AFX_MSG_MAP(CDataGridEdit)
	ON_WM_CHAR()
	ON_WM_KEYDOWN()
	ON_WM_KILLFOCUS()
	ON_WM_SETFOCUS()
	ON_WM_GETDLGCODE()
	ON_WM_HELPINFO()
	ON_WM_KEYUP()
	//}}AFX_MSG_MAP
END_MESSAGE_MAP()

/////////////////////////////////////////////////////////////////////////////
// CDataGridEdit message handlers

void CDataGridEdit::OnChar(UINT nChar, UINT nRepCnt, UINT nFlags) 
{
	if (flog != NULL) {
		fprintf(flog, "  EditOnChar(%d)\n", nChar);
		fflush(flog);
	}

	if (nChar == 9)
		return;

	CEdit::OnChar(nChar, nRepCnt, nFlags);
}

void CDataGridEdit::OnKeyDown(UINT nChar, UINT nRepCnt, UINT nFlags) 
{
	if (flog != NULL) {
		fprintf(flog, "   EditOnKeyDown(%d, %d)\n", nChar, nFlags);
		fflush(flog);
	}
	
	BOOL bShiftOn = utilityHelper.GetShiftKeyState();

	if (nChar == 9 && ! bShiftOn) {			// Tab
		CDataGrid *pDataGrid = (CDataGrid *)this->GetParent();
		//pEditGrid->ProcessInsert();
		pDataGrid->ProcessTab();
		return;
	}
	else if (nChar == 9 && bShiftOn) {		// Back tab
		CDataGrid *pDataGrid = (CDataGrid *)this->GetParent();
		pDataGrid->ProcessArrow(0);
		return;
	}
	else if (nChar == 45) {		// Insert
		CDataGrid *pDataGrid = (CDataGrid *)this->GetParent();
		pDataGrid->ProcessInsert();
		return;
	}
	else if (nChar == 46) {	// Delete
		CDataGrid *pDataGrid = (CDataGrid *)this->GetParent();
		pDataGrid->ProcessDelete();
		return;
	}
	else if (nChar == 37) { // left arrow
		CDataGrid *pDataGrid = (CDataGrid *)this->GetParent();
		pDataGrid->ProcessArrow(0);
		return;
	}
	else if (nChar == 39) { // right arrow
		CDataGrid *pDataGrid = (CDataGrid *)this->GetParent();
		pDataGrid->ProcessArrow(1);
		return;
	}
	else if (nChar == 38) { // Up arrow
		CDataGrid *pDataGrid = (CDataGrid *)this->GetParent();
		pDataGrid->ProcessArrow(2);
		return;
	}
	else if (nChar == 40) { // Down arrow
		CDataGrid *pDataGrid = (CDataGrid *)this->GetParent();
		pDataGrid->ProcessArrow(3);
		return;
	}



	/*
	else if (nChar == 27) {	// Esc means "Cancel".
		SetWindowText("");
		ShowWindow(SW_HIDE);
		GetParent()->SetFocus();
	}
	else if (nChar == 13)  // Enter means "OK".
		GetParent()->SetFocus();
	*/

	CEdit::OnKeyDown(nChar, nRepCnt, nFlags);
}

void CDataGridEdit::OnKillFocus(CWnd* pNewWnd) 
{
	if (flog != NULL) {
		fprintf(flog, "   EditKillFocus\n");
		fflush(flog);
	}

	// If the another control or another window gets the focus,
	// make sure the grid processes the leave cell event so that
	// it gets updated with the contents of the edit box.
	if (pNewWnd != this->GetParent()) {
		if (flog != NULL) {
			fprintf(flog, "   EditKillFocus: Calling parent LeaveCellGrid\n");
			fflush(flog);
		}
		CEdit::OnKillFocus(pNewWnd);
		CDataGrid *pParent = (CDataGrid *)this->GetParent();
		pParent->OnLeaveCellGrid();
		return;
	}
		
	CEdit::OnKillFocus(pNewWnd);

}

void CDataGridEdit::OnSetFocus(CWnd* pOldWnd) 
{
	if (flog != NULL) {
		fprintf(flog, "   EditSetFocus\n");
		fflush(flog);
	}
	
	CEdit::OnSetFocus(pOldWnd);
	CEdit::SetSel(0,-1);

}

UINT CDataGridEdit::OnGetDlgCode() 
{
	return DLGC_WANTTAB|DLGC_WANTCHARS|DLGC_WANTALLKEYS;
}

BOOL CDataGridEdit::OnHelpInfo(HELPINFO* pHelpInfo) 
{
	UNREFERENCED_PARAMETER(pHelpInfo);

	CDataGrid *pParent = (CDataGrid *)this->GetParent();
	pParent->ShowHelp();

	return FALSE;
	
}

void CDataGridEdit::OnKeyUp(UINT nChar, UINT nRepCnt, UINT nFlags) 
{

	if (flog != NULL) {
		fprintf(flog, "\tEdit: OnKeyUp (%d)\n", nChar);
		fflush(flog);
	}

	/*
	if (nChar == 16) {
		if (gShiftOn) {
			gShiftOn = 0;
			if (flog != NULL)
			fprintf(flog, "Shift Off\n");
		}
	}
	*/
	CEdit::OnKeyUp(nChar, nRepCnt, nFlags);
}

// WMSExportPage.cpp : implementation file
//

#include "stdafx.h"
#include "modal.h"
#include "WMSExportPage.h"
#include "WMSSheet.h"
#include "IntegrationDataService.h"
#include "UtilityHelper.h"
#include "ControlService.h"
#include "HelpService.h"

#include <dbsymtb.h>

#ifdef _DEBUG
#define new DEBUG_NEW
#undef THIS_FILE
static char THIS_FILE[] = __FILE__;
#endif

extern CIntegrationDataService integrationDataService;
extern CUtilityHelper utilityHelper;
extern CControlService controlService;
extern CHelpService helpService;
/////////////////////////////////////////////////////////////////////////////
// CWMSExportPage property page

IMPLEMENT_DYNCREATE(CWMSExportPage, CPropertyPage)

CWMSExportPage::CWMSExportPage() : CPropertyPage(CWMSExportPage::IDD)
{
	//{{AFX_DATA_INIT(CWMSExportPage)
	//}}AFX_DATA_INIT
	m_hDragItem = NULL;
	m_hDropItem = NULL;
	m_pDragImageList = NULL;
	m_bDraggingWMS = FALSE;
	m_bDraggingSection = FALSE;
	m_nDelayInterval = 500;     // Default delay interval = 500 milliseconds
	m_nScrollInterval = 200;    // Default scroll interval = 200 milliseconds
	m_nScrollMargin = 10;       // Default scroll margin = 10 pixels
}

CWMSExportPage::~CWMSExportPage()
{
	for (int i=0; i < m_ExportMapList.GetSize(); ++i)
		delete m_ExportMapList[i];
}

void CWMSExportPage::DoDataExchange(CDataExchange* pDX)
{
	CPropertyPage::DoDataExchange(pDX);
	//{{AFX_DATA_MAP(CWMSExportPage)
	DDX_Control(pDX, IDC_WMS_MAP_TREE, m_WMSTreeCtrl);
	DDX_Control(pDX, IDC_FACILITY_TREE, m_FacilityTreeCtrl);
	//}}AFX_DATA_MAP
}


BEGIN_MESSAGE_MAP(CWMSExportPage, CPropertyPage)
	//{{AFX_MSG_MAP(CWMSExportPage)
	ON_BN_CLICKED(IDC_ASSIGN, OnAssign)
	ON_BN_CLICKED(IDC_REMOVE, OnRemove)
	ON_NOTIFY(TVN_BEGINDRAG, IDC_FACILITY_TREE, OnBegindragFacilityTree)
	ON_NOTIFY(TVN_BEGINDRAG, IDC_WMS_MAP_TREE, OnBegindragWmsGroupTree)
	ON_WM_MOUSEMOVE()
	ON_WM_LBUTTONUP()
	ON_WM_TIMER()
	ON_WM_HELPINFO()
	//}}AFX_MSG_MAP
END_MESSAGE_MAP()

/////////////////////////////////////////////////////////////////////////////
// CWMSExportPage message handlers


BOOL CWMSExportPage::OnInitDialog() 
{
	CPropertyPage::OnInitDialog();
	
	m_FacilityImageList.Create(16, 16, TRUE, 4, 2);
	m_FacilityImageList.Add(AfxGetApp()->LoadIcon(IDI_FACILITY));
	m_FacilityImageList.Add(AfxGetApp()->LoadIcon(IDI_FACILITY_INVERSE));
	m_FacilityImageList.Add(AfxGetApp()->LoadIcon(IDI_SECTION));
	m_FacilityImageList.Add(AfxGetApp()->LoadIcon(IDI_SECTION_INVERSE));
	m_FacilityTreeCtrl.SetImageList(&m_FacilityImageList, TVSIL_NORMAL);
	
	LoadFacilityList();
	LoadFacilityTree();

	m_WMSImageList.Create(16, 16, TRUE, 3, 1);
	m_WMSImageList.Add(AfxGetApp()->LoadIcon(IDI_BOX));
	m_WMSImageList.Add(AfxGetApp()->LoadIcon(IDI_FORKLIFT2));
	m_WMSImageList.Add(AfxGetApp()->LoadIcon(IDI_SECTION_INVERSE));

	m_WMSTreeCtrl.SetImageList(&m_WMSImageList, TVSIL_NORMAL);
	
	LoadWMSList();
	LoadWMSTree();

	LoadMapList();
	UpdateMapTree();

	
	return TRUE;  // return TRUE unless you set the focus to a control
	              // EXCEPTION: OCX Property Pages should return FALSE
}

int CWMSExportPage::LoadFacilityList()
{

	CWMSSheet *pParent = (CWMSSheet *)GetParent();

	pParent->LoadFacilityList();


	return 0;
}

int CWMSExportPage::LoadFacilityTree()
{

	HTREEITEM hFacItem, hSectionItem;

	CWMSSheet *pParent = (CWMSSheet *)GetParent();

	CTypedPtrArray<CObArray, CWMSFacilityInfo*> &facilityList = pParent->m_FacilityList;

	for (int i=0; i < facilityList.GetSize(); ++i) {
		CWMSFacilityInfo *pFacInfo = facilityList[i];

		hFacItem = m_FacilityTreeCtrl.InsertItem(pFacInfo->m_FacilityName, 0, 0, TVI_ROOT, TVI_LAST);
		m_FacilityTreeCtrl.SetItemData(hFacItem, (unsigned long)pFacInfo);

		for (int j=0; j < pFacInfo->m_SectionDBIdList.GetSize(); ++j) {
			hSectionItem = 
				m_FacilityTreeCtrl.InsertItem(pFacInfo->m_SectionNameList[j], 2, 2, hFacItem, TVI_LAST);
			m_FacilityTreeCtrl.SetItemData(hSectionItem, pFacInfo->m_SectionDBIdList[j]);
			m_MapSectionToTree.SetAt(pFacInfo->m_SectionDBIdList[j], hSectionItem);
			m_MapTreeToSection.SetAt(hSectionItem, pFacInfo->m_SectionDBIdList[j]);
		}
	}


	return 0;

}

int CWMSExportPage::LoadWMSList()
{
	CWMSSheet *pParent = (CWMSSheet *)GetParent();

	return pParent->LoadWMSList();

}

int CWMSExportPage::LoadWMSTree()
{
	CWMSSheet *pParent = (CWMSSheet *)GetParent();
	CTypedPtrArray<CObArray, CWMSGroup*> &groupList = pParent->m_GroupList;

	HTREEITEM hGroupItem;

	for (int i=0; i < groupList.GetSize(); ++i) {
		CWMSGroup *pGroup = groupList[i];
		hGroupItem = AddGroupToTree(pGroup);
		for (int j=0; j < pGroup->m_WMSList.GetSize(); ++j) {
			CWMS *pWMS = pGroup->m_WMSList[j];
			AddWMSToTree(pWMS, hGroupItem);
		}
	}

	return 0;

}

HTREEITEM CWMSExportPage::AddGroupToTree(CWMSGroup *pGroup)
{
	HTREEITEM hNewItem;

	hNewItem = m_WMSTreeCtrl.InsertItem(pGroup->m_Name, 0, 0, TVI_ROOT, TVI_LAST);
	m_WMSTreeCtrl.SetItemData(hNewItem, (unsigned long)pGroup);
	m_MapTreeToGroup.SetAt(hNewItem, pGroup->m_WMSGroupDBId);

	return hNewItem;
}

HTREEITEM CWMSExportPage::AddWMSToTree(CWMS *pWMS, HTREEITEM hItem)
{
	HTREEITEM hNewItem;

	hNewItem = m_WMSTreeCtrl.InsertItem(pWMS->m_Name, 1, 1, hItem, TVI_LAST);
	m_WMSTreeCtrl.SetItemData(hNewItem, (unsigned long)pWMS);

	m_MapWMSToTree.SetAt(pWMS->m_WMSDBId, hNewItem);
	m_MapTreeToWMS.SetAt(hNewItem, pWMS->m_WMSDBId);

	return hNewItem;
}


int CWMSExportPage::LoadMapList()
{
	CStringArray mapList;

	if (m_ExportMapList.GetSize() > 0)
		return 0;

	try {
		integrationDataService.GetWMSExportMap(0, mapList);
	}
	catch (...) {
		utilityHelper.ProcessError("Error loading export map.");
		return -1;
	}

	for (int i=0; i < mapList.GetSize(); ++i) {
		CWMSMap *pMap = new CWMSMap;
		pMap->Parse(mapList[i]);
		m_ExportMapList.Add(pMap);
	}

	return 0;

}

void CWMSExportPage::OnAssign() 
{	
	
	if (! ValidateAssignment())
		return;


	HTREEITEM hWMSItem = m_WMSTreeCtrl.GetSelectedItem();
	HTREEITEM hFacItem = m_FacilityTreeCtrl.GetSelectedItem();

	CWMS *pWMS = (CWMS *)m_WMSTreeCtrl.GetItemData(hWMSItem);


	CArray<int, int> sectionList;
	int sectionDBId;
	CWMSFacilityInfo *pFacInfo;

	if (FacTreeType(hFacItem) == CWMSExportPage::sectionType)	{			// Single section selected
		if (m_MapTreeToSection.Lookup(hFacItem, sectionDBId))
			;
		else
			sectionDBId = 0;
		pFacInfo = (CWMSFacilityInfo *)m_FacilityTreeCtrl.GetItemData(m_FacilityTreeCtrl.GetParentItem(hFacItem));
	}
	else {									// multiple sections selected
		pFacInfo = (CWMSFacilityInfo *)m_FacilityTreeCtrl.GetItemData(hFacItem);
		sectionDBId = 0;
	}
	
	Assign(pWMS, pFacInfo->m_FacilityDBId, sectionDBId);


	return;
	
}

BOOL CWMSExportPage::ValidateAssignment()
{
	// No WMS item selected
	HTREEITEM hWMSItem = m_WMSTreeCtrl.GetSelectedItem();
	if (hWMSItem == NULL) {
		AfxMessageBox("Please select a WMS section to connect.");
		return FALSE;
	}

	// No facility item selected
	HTREEITEM hFacItem = m_FacilityTreeCtrl.GetSelectedItem();
	if (hFacItem == NULL) {
		AfxMessageBox("Please select a facility or section to connect.");
		return FALSE;
	}

	// Wrong WMS type selected
	if (WMSTreeType(hWMSItem) != CWMSExportPage::wmsType) {
		AfxMessageBox("Please select a single WMS section to connect.");
		return FALSE;
	}

	CWMS *pWMS = (CWMS *)m_WMSTreeCtrl.GetItemData(hWMSItem);
	CWMSFacilityInfo *pFacInfo;
	int sectionDBId;

	if (FacTreeType(hFacItem) == sectionType) {
		pFacInfo = (CWMSFacilityInfo *)m_FacilityTreeCtrl.GetItemData(m_FacilityTreeCtrl.GetParentItem(hFacItem));
		sectionDBId = m_FacilityTreeCtrl.GetItemData(hFacItem);
		// Section already mapped to this WMS
		if (IsSectionMapped(hWMSItem, sectionDBId)) {
			AfxMessageBox("The section is already mapped to the selected WMS section.");
			return FALSE;
		}

		CWMSMap *pMap = NULL;
		for (int i=0; i < m_ExportMapList.GetSize(); ++i) {
			CWMSMap *pTempMap = m_ExportMapList[i];
			if (pTempMap->m_SectionDBId == sectionDBId) {
				AfxMessageBox("The section is already mapped to a different WMS section.");
				return FALSE;
			}
		}

		if (IsFacilityMapped(hWMSItem, pFacInfo->m_FacilityDBId)) {
			AfxMessageBox("The entire facility is already mapped to the selected WMS section.");
			return FALSE;
		}
	}
	else {
		pFacInfo = (CWMSFacilityInfo *)m_FacilityTreeCtrl.GetItemData(hFacItem);
		if (IsFacilityMapped(hWMSItem, pFacInfo->m_FacilityDBId)) {
			AfxMessageBox("The facility is already mapped to the selected WMS section.");
			return FALSE;
		}
	}

	// Different facility mapped to WMS
	int mappedFac = GetMappedFacilityId(hWMSItem);
	if (mappedFac > 0 && pFacInfo->m_FacilityDBId != mappedFac) {
		
		HTREEITEM hTempMapItem = m_WMSTreeCtrl.GetChildItem(hWMSItem);
		CWMSMap *pTempMap = (CWMSMap *)m_WMSTreeCtrl.GetItemData(hTempMapItem);
		CString temp;
		temp.Format("The facility %s is already connected to WMS section %s.  Only "
			"one facility may be connected to a WMS section", pTempMap->m_FacilityName, 
			pWMS->m_Name);
		AfxMessageBox(temp);
		return FALSE;
	}

	if (FacTreeType(hFacItem) == CWMSExportPage::facType) {
		if (AfxMessageBox("Do you wish to make this the default WMS section for all future sections in the Optimize facility?", MB_YESNO) != IDYES)
			return FALSE;
	}

	return TRUE;
}

int CWMSExportPage::Assign(CWMS *pWMS, int facilityDBId, int sectionDBId)
{
	CWaitCursor cwc;

	CWMSFacilityInfo *pFacInfo;
	HTREEITEM hItem;
	if (sectionDBId > 0) {
		if (! m_MapSectionToTree.Lookup(sectionDBId, hItem)) {
			AfxMessageBox("Error finding section in tree.");
			return -1;
		}

		pFacInfo = (CWMSFacilityInfo *)m_FacilityTreeCtrl.GetItemData(m_FacilityTreeCtrl.GetParentItem(hItem));
	}
	else {
		hItem = GetFacilityNode(facilityDBId);
		pFacInfo = (CWMSFacilityInfo *)m_FacilityTreeCtrl.GetItemData(hItem);
	}


	// handle case where wms already assigned to a facility
	CWMSMap *pMap = NULL;
	for (int i=0; i < m_ExportMapList.GetSize(); ++i) {
		CWMSMap *pTempMap = m_ExportMapList[i];
		if (pTempMap->m_WMSDBId == pWMS->m_WMSDBId) {
			if (pTempMap->m_SectionDBId == sectionDBId)		// wms already assigned to this section; return
				return 0;
				// otherwise, it must be a different section from the same facility
		}
	}
	
	CWMSMap newMap;

	newMap.m_Direction = CWMSMap::exportToWMS;
	newMap.m_FacilityDBId = pFacInfo->m_FacilityDBId;
	newMap.m_FacilityName = pFacInfo->m_FacilityName;
	newMap.m_SectionDBId = sectionDBId;
	newMap.m_SectionName = "All Sections";
	for (int j=0; j < pFacInfo->m_SectionDBIdList.GetSize(); ++j) {
		if (sectionDBId == pFacInfo->m_SectionDBIdList[j]) { 
			newMap.m_SectionName = pFacInfo->m_SectionNameList[j];
			break;
		}
	}
	newMap.m_WMSDBId = pWMS->m_WMSDBId;
	newMap.m_WMSName = pWMS->m_Name;
	newMap.m_WMSMapDBId = 0;

	// Remove maps to sections within the facility if we are mapping the entire facility
	if (sectionDBId <= 0) {
		for (int i=0; i < m_ExportMapList.GetSize(); ++i) {
			CWMSMap *pTempMap = m_ExportMapList[i];
			if (pTempMap->m_WMSDBId == pWMS->m_WMSDBId &&
				pTempMap->m_FacilityDBId == pFacInfo->m_FacilityDBId) {
				HTREEITEM hWMSItem;
				CArray<HTREEITEM, HTREEITEM&> removeList;
				
				if (m_MapWMSToTree.Lookup(pTempMap->m_WMSDBId, hWMSItem)) {
					CArray<HTREEITEM, HTREEITEM&> removeList;
					HTREEITEM hMappedSectionItem = m_WMSTreeCtrl.GetChildItem(hWMSItem);
					while (hMappedSectionItem != NULL) {
						removeList.Add(hMappedSectionItem);
						HTREEITEM hNextItem = m_WMSTreeCtrl.GetNextItem(hMappedSectionItem, TVGN_NEXT);
						hMappedSectionItem= hNextItem;
					}
					
					for (int i=0; i < removeList.GetSize(); ++i)
						Remove(removeList[i]);
				}
			}
		}
	}


	try {
		integrationDataService.StoreWMSExportMap(newMap);
	}
	catch (...) {
		AfxMessageBox("Error creating connection.");
		return -1;
	}

	pMap = new CWMSMap(newMap);
	m_ExportMapList.Add(pMap);

	UpdateMapTreeItem(pMap);


	return 0;
}

void CWMSExportPage::OnRemove() 
{
	HTREEITEM hWMSItem = m_WMSTreeCtrl.GetSelectedItem();
	if (hWMSItem == NULL) {
		AfxMessageBox("Please select the Optimize section connected to a WMS section that you wish to disconnect.");
		return;
	}

	CWMS *pWMS = (CWMS *)m_WMSTreeCtrl.GetItemData(hWMSItem);
	
	
	int type = WMSTreeType(hWMSItem);

	if (type == CWMSExportPage::wmsGroupType) {
		AfxMessageBox("You must disconnect each WMS section within a WMS facility individually.");
		return;
	} else if (type == CWMSExportPage::wmsType) {
		HTREEITEM hMappedSectionItem = m_WMSTreeCtrl.GetChildItem(hWMSItem);
		if (hMappedSectionItem == NULL)
			return;		// no mapped sections

		CWMSMap *pMap = (CWMSMap *)m_WMSTreeCtrl.GetItemData(hMappedSectionItem);
		CString temp;
		temp.Format("Do you wish to disconnect the entire %s facility from %s", 
			pMap->m_WMSName, pMap->m_FacilityName);
		if (AfxMessageBox(temp, MB_YESNO) != IDYES)
			return;
		
		CArray<HTREEITEM, HTREEITEM&> removeList;
		while (hMappedSectionItem != NULL) {
			removeList.Add(hMappedSectionItem);
			HTREEITEM hNextItem = m_WMSTreeCtrl.GetNextItem(hMappedSectionItem, TVGN_NEXT);
			hMappedSectionItem= hNextItem;
		}

		for (int i=0; i < removeList.GetSize(); ++i)
			Remove(removeList[i]);
	}
	else {			// type is section
		Remove(hWMSItem);
	}


}

int CWMSExportPage::Remove(HTREEITEM& hItem)
{
	CWaitCursor cwc;

	CWMSMap *pMap = (CWMSMap *)m_WMSTreeCtrl.GetItemData(hItem);
	
	int rc;

	try {
		rc = integrationDataService.CheckLocationIntegrationStatus(pMap->m_FacilityDBId, pMap->m_SectionDBId);
	}
	catch (...) {
		controlService.Log("Error checking location integration status.", "Generic exception in CheckLocationIntegrationStatus\n");
		return -1;
	}

	if (rc > 0) {
		if (AfxMessageBox("Warning! The WMS section you are attempting to disconnect already has integrated locations.\n"
			"If you disconnect it, inconsistencies between Optimize and the WMS may result.\n"
			"Do you wish to continue?", MB_YESNO) != IDYES)
			return -1;
	}

	for (int i=0; i < m_ExportMapList.GetSize(); ++i) {
		CWMSMap *pTempMap = m_ExportMapList[i];
		if (pMap->m_WMSMapDBId == pTempMap->m_WMSMapDBId) {
			try {
				integrationDataService.DeleteWMSExportMap(pMap->m_WMSMapDBId);
			}
			catch (...) {
				CString temp;
				temp.Format("Error disconnecting section %s from WMS section %s",
					pMap->m_SectionName, pMap->m_WMSName);
				AfxMessageBox(temp);
				return -1;
			}

			m_ExportMapList.RemoveAt(i);
			CString wmsName = pMap->m_WMSName;
			delete pMap;
			HTREEITEM hParentItem = m_WMSTreeCtrl.GetParentItem(hItem);
			m_WMSTreeCtrl.DeleteItem(hItem);
			if (! m_WMSTreeCtrl.ItemHasChildren(hParentItem))
				UpdateWMSTreeText(hParentItem, wmsName, NULL);

			return 0;
		}
	}

	return 0;
}

void CWMSExportPage::OnBegindragFacilityTree(NMHDR* pNMHDR, LRESULT* pResult) 
{
	NM_TREEVIEW* pNMTreeView = (NM_TREEVIEW*)pNMHDR;

	CPoint hitPoint;
		
	m_FacilityTreeCtrl.UpdateWindow();

	// Determine where the cursor is positioned relative to the tree control
	GetCursorPos(&hitPoint);
	m_FacilityTreeCtrl.ScreenToClient(&hitPoint);

	// Get the tree node where the cursor is - this will be the drag item
	UINT flags;
	m_hDragItem = m_FacilityTreeCtrl.HitTest(hitPoint, &flags);
	if (m_hDragItem == NULL || ! (flags & TVHT_ONITEM))
		return;

	// Verify that the drag item is something that can be dragged
	if (! ValidateSectionDragItem(m_hDragItem)) {
		m_hDragItem = NULL;
		return;
	}

	//ShowCursor(FALSE);
	m_FacilityTreeCtrl.SelectDropTarget(NULL);

	// Create the drag image list
	m_pDragImageList = m_FacilityTreeCtrl.CreateDragImage(m_hDragItem);
	
	// Force the image to be shown while dragging
	m_pDragImageList->DragShowNolock(TRUE);

	// Set the image to be used while dragging
	// Not sure where they are getting it (from the original image list?)
//	m_pDragImageList->SetDragCursorImage(0, CPoint(0, 0));

	// Start the drag operation; offset the cursor into the middle of the drag image
	m_pDragImageList->BeginDrag(0, CPoint(8, 8));

	// Move the cursor to the point where they started dragging
	//m_pDragImageList->DragMove(hitPoint);

	// Lock updates to the window and display the image
	m_pDragImageList->DragEnter(GetDesktopWindow(), pNMTreeView->ptDrag);

	// Capture the mouse so we can respond to the moves
	SetCapture();

	m_bDraggingSection = TRUE;
	m_hDropItem = NULL;


	*pResult = 0;
}

void CWMSExportPage::OnBegindragWmsGroupTree(NMHDR* pNMHDR, LRESULT* pResult) 
{
	NM_TREEVIEW* pNMTreeView = (NM_TREEVIEW*)pNMHDR;

	CPoint hitPoint;
		
	m_WMSTreeCtrl.UpdateWindow();

	// Determine where the cursor is positioned relative to the tree control
	GetCursorPos(&hitPoint);
	m_WMSTreeCtrl.ScreenToClient(&hitPoint);

	// Get the tree node where the cursor is - this will be the drag item
	UINT flags;
	m_hDragItem = m_WMSTreeCtrl.HitTest(hitPoint, &flags);
	if (m_hDragItem == NULL || ! (flags & TVHT_ONITEM))
		return;

	// Verify that the drag item is something that can be dragged
	if (! ValidateWMSDragItem(m_hDragItem)) {
		m_hDragItem = NULL;
		return;
	}

	//ads_printf("Dragging WMS item\n");

	//ShowCursor(FALSE);
	m_WMSTreeCtrl.SelectDropTarget(NULL);
	m_FacilityTreeCtrl.SelectItem(NULL);

	// Create the drag image list
	m_pDragImageList = m_WMSTreeCtrl.CreateDragImage(m_hDragItem);
	
	// Force the image to be shown while dragging
	m_pDragImageList->DragShowNolock(TRUE);

	// Set the image to be used while dragging
	// Not sure where they are getting it (from the original image list?)
//	m_pDragImageList->SetDragCursorImage(0, CPoint(0, 0));

	// Start the drag operation	
	m_pDragImageList->BeginDrag(0, CPoint(8, 8));

	// Move the cursor to the point where they started dragging
	//m_pDragImageList->DragMove(hitPoint);

	// Lock updates to the window and display the image
	m_pDragImageList->DragEnter(GetDesktopWindow(), pNMTreeView->ptDrag);

	// Capture the mouse so we can respond to the moves
	SetCapture();

	m_bDraggingWMS = TRUE;
	m_hDropItem = NULL;


	*pResult = 0;
}

void CWMSExportPage::OnMouseMove(UINT nFlags, CPoint point) 
{	
	if (m_bDraggingWMS)
		OnMouseMoveWMS(point);
	else if (m_bDraggingSection)
		OnMouseMoveSection(point);
	
	CPropertyPage::OnMouseMove(nFlags, point);
}


void CWMSExportPage::OnMouseMoveWMS(CPoint point)
{
	// Point is relative to upper left corner of CWMSExportPage
	// convert it to global screen coordinates since that's what we started
	// dragging with
	CPoint pt(point);
	ClientToScreen(&pt);
	
	// Move the image to the current cursor position
	m_pDragImageList->DragMove(pt);
	
	// Temporarily allow screen updates
	m_pDragImageList->DragShowNolock (FALSE);
	
	m_hDropItem = NULL;
	
	// See if they are outside of the facility tree
	CWnd *pWnd = WindowFromPoint(pt);
	if (pWnd == &m_WMSTreeCtrl)
		::SetCursor (AfxGetApp ()->LoadStandardCursor (IDC_NO));
	else
		::SetCursor((HCURSOR)::GetClassLong(this->m_hWnd, GCL_HCURSOR));
	
	// Re-lock screen for dragging
	m_pDragImageList->DragShowNolock (TRUE);

}


void CWMSExportPage::OnMouseMoveSection(CPoint point)
{
	KillTimer (1);
	// Point is relative to upper left corner of CWMSExportPage
	// convert it to global screen coordinates since that's what we started
	// dragging with
	CPoint pt(point);
	ClientToScreen(&pt);
	
	// Move the image to the current cursor position
	m_pDragImageList->DragMove(pt);
	
	// Temporarily allow screen updates
	m_pDragImageList->DragShowNolock (FALSE);
	
	m_hDropItem = NULL;
	m_WMSTreeCtrl.SelectDropTarget(NULL);
	
	// See if they are passing over the WMS tree
	CWnd *pWnd = WindowFromPoint(pt);
	if (pWnd == &m_WMSTreeCtrl) {
		
		CPoint hitPoint(pt);
		// Convert the screen coordinates to the WMS tree client coordinates
		m_WMSTreeCtrl.ScreenToClient(&hitPoint);
		// See if they are over a WMS tree item
		UINT flags;
		m_hDropItem = m_WMSTreeCtrl.HitTest(hitPoint, &flags);
		if (m_hDropItem != NULL && (flags & TVHT_ONITEM) && 
			WMSTreeType(m_hDropItem) == CWMSExportPage::wmsType) {
			
			// Highlight the WMS tree item they are over
			m_WMSTreeCtrl.SelectDropTarget(m_hDropItem);
			::SetCursor((HCURSOR)::GetClassLong(this->m_hWnd, GCL_HCURSOR));
			
		}
		else {
			m_hDropItem = NULL;
			::SetCursor (AfxGetApp ()->LoadStandardCursor (IDC_NO));
		}
		CRect r;
		m_WMSTreeCtrl.GetClientRect(&r);
		if (abs(hitPoint.y-r.bottom) < 20)
			SetTimer(2, m_nScrollInterval/2, NULL);
		else if (abs(hitPoint.y-r.top) < 20)
			SetTimer(2, m_nScrollInterval/2, NULL);
		else if (abs(hitPoint.x-r.right) < 20)
			SetTimer(2, m_nScrollInterval/2, NULL);
		else if (abs(hitPoint.x-r.left) < 20)
			SetTimer(2, m_nScrollInterval/2, NULL);
		else
			KillTimer(2);
		
		if (m_hDropItem != NULL && 
			m_WMSTreeCtrl.ItemHasChildren(m_hDropItem))
			SetTimer(1, m_nDelayInterval, NULL);
	}
	else {
		m_hDropItem = NULL;
		::SetCursor (AfxGetApp ()->LoadStandardCursor (IDC_NO));
	}
	

	// Re-lock screen for dragging
	m_pDragImageList->DragShowNolock (TRUE);
}


void CWMSExportPage::OnTimer(UINT nIDEvent) 
{
	CPropertyPage::OnTimer(nIDEvent);
	//
	// Reset the timer.
	//

	// Get the current cursor position and window height.
	DWORD dwPos = ::GetMessagePos();
	CPoint point (LOWORD (dwPos), HIWORD (dwPos));
	m_WMSTreeCtrl.ScreenToClient(&point);
	
	if (nIDEvent == 1) {

		SetTimer (nIDEvent, m_nScrollInterval, NULL);
		
		// If the cursor is hovering over a collapsed item, expand the tree.
		UINT nFlags;
		HTREEITEM hItem = m_WMSTreeCtrl.HitTest(point, &nFlags);
		
		if (hItem != NULL && (nFlags & TVHT_ONITEM)) {
			m_pDragImageList->DragShowNolock(FALSE);
			m_WMSTreeCtrl.Expand(hItem, TVE_TOGGLE);
			m_pDragImageList->DragShowNolock(TRUE);
			KillTimer(1);
			return;
		}
	}
	else if (nIDEvent == 2) {
		HTREEITEM hFirstVisible = m_WMSTreeCtrl.GetFirstVisibleItem();
		SetTimer (nIDEvent, m_nScrollInterval/2, NULL);
		CRect r;
		m_WMSTreeCtrl.GetClientRect(&r);
		m_pDragImageList->DragShowNolock(FALSE);
		if (abs(point.y-r.bottom) < 20)
			m_WMSTreeCtrl.SendMessage(WM_VSCROLL, MAKEWPARAM(SB_LINEDOWN, 0), NULL);
		else if (abs(point.y-r.top) < 20)
			m_WMSTreeCtrl.SendMessage(WM_VSCROLL, MAKEWPARAM(SB_LINEUP, 0), NULL);
		
		if (abs(point.x-r.right) < 20)
			m_WMSTreeCtrl.SendMessage(WM_HSCROLL, MAKEWPARAM(SB_LINEDOWN, 0), NULL);
		else if (abs(point.x-r.left) < 20)
			m_WMSTreeCtrl.SendMessage(WM_HSCROLL, MAKEWPARAM(SB_LINEUP, 0), NULL);

		if (hFirstVisible == m_WMSTreeCtrl.GetFirstVisibleItem())
			KillTimer(2);
		m_pDragImageList->DragShowNolock(TRUE);

	}

}

void CWMSExportPage::OnLButtonUp(UINT nFlags, CPoint point) 
{
	if (m_bDraggingSection) {

		KillTimer (1);

		// Stop dragging
		m_pDragImageList->DragLeave(GetDesktopWindow());
		m_pDragImageList->EndDrag();

		// Delete the image list
		delete m_pDragImageList;
		m_bDraggingSection = FALSE;

		ReleaseCapture();

		m_WMSTreeCtrl.SelectDropTarget(NULL);

		if (m_hDropItem != NULL) {
			// Highlight the drop item
			m_WMSTreeCtrl.SelectItem(m_hDropItem);
			m_FacilityTreeCtrl.SelectItem(m_hDragItem);

			OnAssign();
			
		}
	}
	else if (m_bDraggingWMS) {
		// Stop dragging
		m_pDragImageList->DragLeave(GetDesktopWindow());
		m_pDragImageList->EndDrag();

		// Delete the image list
		delete m_pDragImageList;
		m_bDraggingWMS = FALSE;

		ReleaseCapture();

		CPoint pt(point);
		ClientToScreen(&pt);

		CWnd *pWnd = WindowFromPoint(pt);
		if (pWnd != &m_WMSTreeCtrl) {
			m_WMSTreeCtrl.SelectItem(m_hDragItem);
			OnRemove();
		}

	}

	CPropertyPage::OnLButtonUp(nFlags, point);
}



BOOL CWMSExportPage::ValidateWMSDragItem(HTREEITEM &hItem)
{
	if (WMSTreeType(hItem) == CWMSExportPage::wmsGroupType)
		return FALSE;

	return TRUE;

}

BOOL CWMSExportPage::ValidateSectionDragItem(HTREEITEM &hItem)
{

	UNREFERENCED_PARAMETER(hItem);
	// They can drag anything in the facility tree
	return TRUE;

}


CWMSExportPage::UpdateMapTree()
{
	for (int i=0; i < m_ExportMapList.GetSize(); ++i) {
		CWMSMap *pMap = m_ExportMapList[i];
		UpdateMapTreeItem(pMap);
	}

	return 0;
}

int CWMSExportPage::UpdateMapTreeItem(CWMSMap *pMap)
{
	HTREEITEM hItem;
	if (m_MapWMSToTree.Lookup(pMap->m_WMSDBId, hItem)) {
		HTREEITEM hNewItem;
		hNewItem = m_WMSTreeCtrl.InsertItem(pMap->m_SectionName, 2, 2, hItem, TVI_LAST);
		m_WMSTreeCtrl.SetItemData(hNewItem, (unsigned long)pMap);
		m_WMSTreeCtrl.SortChildren(hItem);
		m_WMSTreeCtrl.Expand(hItem, TVE_EXPAND);
		UpdateWMSTreeText(hItem, pMap->m_WMSName, pMap);
	}

	return 0;

}

void CWMSExportPage::Reload()
{

	for (int i=0; i < m_ExportMapList.GetSize(); ++i)
		delete  m_ExportMapList[i];
	
	m_ExportMapList.RemoveAll();

	
	// Just to be nice, keep track of the ones that were expanded and
	// reset to that 
	CMap<int, int, int, int> expandedMap;
	CMap<int, int, int, int> expandedWMSMap;

	HTREEITEM hGroupItem = m_WMSTreeCtrl.GetChildItem(TVI_ROOT);
	CString temp;
	temp.Format("%s", m_WMSTreeCtrl.GetItemText(hGroupItem));
	int dbid;
	while (hGroupItem != NULL) {

		if (m_WMSTreeCtrl.GetItemState(hGroupItem, TVIS_EXPANDED) & TVIS_EXPANDED) {
			if (m_MapTreeToGroup.Lookup(hGroupItem, dbid))
				expandedMap.SetAt(dbid, 1);
		}
		
		HTREEITEM hWMSItem = m_WMSTreeCtrl.GetChildItem(hGroupItem);
		while (hWMSItem != NULL) {
			if (m_WMSTreeCtrl.GetItemState(hWMSItem, TVIS_EXPANDED) & TVIS_EXPANDED) {
				if (m_MapTreeToWMS.Lookup(hWMSItem, dbid))
					expandedWMSMap.SetAt(dbid, 1);
			}

			HTREEITEM hNextWMSItem = m_WMSTreeCtrl.GetNextItem(hWMSItem, TVGN_NEXT);
			hWMSItem = hNextWMSItem;
		}

		HTREEITEM hNextItem = m_WMSTreeCtrl.GetNextItem(hGroupItem, TVGN_NEXT);
		CString temp2;
		temp2.Format("%s", m_WMSTreeCtrl.GetItemText(hNextItem));
		hGroupItem = hNextItem;
	}

	m_WMSTreeCtrl.DeleteAllItems();
	m_MapWMSToTree.RemoveAll();
	LoadWMSTree();

	LoadMapList();
	UpdateMapTree();

	hGroupItem = m_WMSTreeCtrl.GetNextItem(TVI_ROOT, TVGN_CHILD);
	while (hGroupItem != NULL) {
		CWMSGroup *pGroup = (CWMSGroup *)m_WMSTreeCtrl.GetItemData(hGroupItem);

		int dummy;
		if (expandedMap.Lookup(pGroup->m_WMSGroupDBId, dummy))
			m_WMSTreeCtrl.Expand(hGroupItem, TVE_EXPAND);
		else
			m_WMSTreeCtrl.Expand(hGroupItem, TVE_COLLAPSE);
		
		HTREEITEM hWMSItem = m_WMSTreeCtrl.GetChildItem(hGroupItem);
		while (hWMSItem != NULL) {
			CWMS *pWMS = (CWMS *)m_WMSTreeCtrl.GetItemData(hWMSItem);
			int dummy;
			if (expandedWMSMap.Lookup(pWMS->m_WMSDBId, dummy))
				m_WMSTreeCtrl.Expand(hWMSItem, TVE_EXPAND);
			else
				m_WMSTreeCtrl.Expand(hWMSItem, TVE_COLLAPSE);

			HTREEITEM hNextWMSItem = m_WMSTreeCtrl.GetNextItem(hWMSItem, TVGN_NEXT);
			hWMSItem = hNextWMSItem;
		}

		HTREEITEM hNextItem = m_WMSTreeCtrl.GetNextItem(hGroupItem, TVGN_NEXT);
		hGroupItem = hNextItem;
	}


}

void CWMSExportPage::UpdateWMSTreeText(HTREEITEM &hWMSItem, const CString& wmsName, CWMSMap *pMap)
{

	CString temp;

	if (pMap == NULL)
		temp.Format("%s", wmsName);
	else 
		temp.Format("%s (%s)", wmsName, pMap->m_FacilityName);
	

	m_WMSTreeCtrl.SetItemText(hWMSItem, temp);

}


int CWMSExportPage::WMSTreeType(HTREEITEM &hItem)
{
	HTREEITEM hParentItem = m_WMSTreeCtrl.GetParentItem(hItem);
	if (hParentItem == NULL)
		return CWMSExportPage::wmsGroupType;

	HTREEITEM hNextParentItem = m_WMSTreeCtrl.GetParentItem(hParentItem);
	if (hNextParentItem == NULL)
		return CWMSExportPage::wmsType;

	return CWMSExportPage::wmsMappedSectionType;
}


int CWMSExportPage::FacTreeType(HTREEITEM &hItem)
{
	HTREEITEM hParentItem = m_FacilityTreeCtrl.GetParentItem(hItem);
	if (hParentItem == NULL)
		return CWMSExportPage::facType;

	return CWMSExportPage::sectionType;
}




int CWMSExportPage::GetMappedFacilityId(HTREEITEM &hWMSItem)
{
	HTREEITEM hMapItem = m_WMSTreeCtrl.GetChildItem(hWMSItem);
	if (hMapItem != NULL) {
		CWMSMap *pMap = (CWMSMap *)m_WMSTreeCtrl.GetItemData(hMapItem);
		return pMap->m_FacilityDBId;
	}

	return 0;
}

BOOL CWMSExportPage::IsSectionMapped(HTREEITEM &hWMSItem, int sectionDBId)
{
	HTREEITEM hMapItem = m_WMSTreeCtrl.GetChildItem(hWMSItem);
	CWMSMap *pMap;
	while (hMapItem != NULL) {
		pMap = (CWMSMap *)m_WMSTreeCtrl.GetItemData(hMapItem);
		if (pMap->m_SectionDBId == sectionDBId)
			return TRUE;
		HTREEITEM hNextItem = m_WMSTreeCtrl.GetNextItem(hMapItem, TVGN_NEXT);
		hMapItem = hNextItem;
	}

	return FALSE;
}

BOOL CWMSExportPage::IsFacilityMapped(HTREEITEM &hWMSItem, int facilityDBId)
{
	HTREEITEM hMapItem = m_WMSTreeCtrl.GetChildItem(hWMSItem);
	CWMSMap *pMap;
	while (hMapItem != NULL) {
		pMap = (CWMSMap *)m_WMSTreeCtrl.GetItemData(hMapItem);
		if (pMap->m_FacilityDBId == facilityDBId && pMap->m_SectionDBId == 0)
			return TRUE;
		HTREEITEM hNextItem = m_WMSTreeCtrl.GetNextItem(hMapItem, TVGN_NEXT);
		hMapItem = hNextItem;
	}

	return FALSE;
}


HTREEITEM CWMSExportPage::GetFacilityNode(int facilityDBId)
{
	CWMSFacilityInfo *pInfo = NULL;

	HTREEITEM hItem = m_FacilityTreeCtrl.GetNextItem(TVI_ROOT, TVGN_CHILD);
	while (hItem != NULL) {
		CWMSFacilityInfo *pInfo = (CWMSFacilityInfo *)m_FacilityTreeCtrl.GetItemData(hItem);
		if (pInfo->m_FacilityDBId == facilityDBId) {
			return hItem;
			break;
		}

		HTREEITEM hNextItem = m_FacilityTreeCtrl.GetNextItem(hItem, TVGN_NEXT);
		hItem = hNextItem;
	}

	return NULL;
}

BOOL CWMSExportPage::OnHelpInfo(HELPINFO* pHelpInfo) 
{
	helpService.ShowFieldHelp(IDD, pHelpInfo->iCtrlId);
	return FALSE;

}

BOOL CWMSExportPage::OnNotify(WPARAM wParam, LPARAM lParam, LRESULT* pResult) 
{
	NMHDR* pNMHDR = (NMHDR*)lParam;

	if (pNMHDR->code == PSN_HELP) {
		helpService.ShowScreenHelp(IDD);
		return TRUE;
	}
	
	return CPropertyPage::OnNotify(wParam, lParam, pResult);
}
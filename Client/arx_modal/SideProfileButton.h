#if !defined(AFX_SIDEPROFILEBUTTON_H__A16F75F4_ED0C_44DD_84F3_0BCE3E8A05BA__INCLUDED_)
#define AFX_SIDEPROFILEBUTTON_H__A16F75F4_ED0C_44DD_84F3_0BCE3E8A05BA__INCLUDED_

#if _MSC_VER > 1000
#pragma once
#endif // _MSC_VER > 1000
// SideProfileButton.h : header file
//

/////////////////////////////////////////////////////////////////////////////
// CSideProfileButton window
#define WM_BAY_PROPERTIES WM_USER+99

class CSideProfileButton : public CButton
{
// Construction
public:
	CSideProfileButton();

// Attributes
public:

// Operations
public:

// Overrides
	// ClassWizard generated virtual function overrides
	//{{AFX_VIRTUAL(CSideProfileButton)
	public:
	virtual void DrawItem(LPDRAWITEMSTRUCT lpDrawItemStruct);
	//}}AFX_VIRTUAL

// Implementation
public:
	virtual ~CSideProfileButton();

	// Generated message map functions
protected:
	//{{AFX_MSG(CSideProfileButton)
	afx_msg void OnLButtonDown(UINT nFlags, CPoint point);
	afx_msg void OnLButtonDblClk(UINT nFlags, CPoint point);
	afx_msg void OnMouseMove(UINT nFlags, CPoint point);
	//}}AFX_MSG

	DECLARE_MESSAGE_MAP()
private:
	int GetCurrentBayIndex(CPoint &point);
};

/////////////////////////////////////////////////////////////////////////////

//{{AFX_INSERT_LOCATION}}
// Microsoft Visual C++ will insert additional declarations immediately before the previous line.

#endif // !defined(AFX_SIDEPROFILEBUTTON_H__A16F75F4_ED0C_44DD_84F3_0BCE3E8A05BA__INCLUDED_)

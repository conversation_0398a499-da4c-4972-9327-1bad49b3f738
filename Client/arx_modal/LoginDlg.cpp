//////////////////////////////////////////////////////////////////////
// Classname :		CLogin
// Description :	This class handles the behavior and data for the
//					"Login" dialog.
// Date Created :	05/1998
// Author :			SP
//////////////////////////////////////////////////////////////////////
// Inputs : Input params
// Outputs : Output params / return values
// Explanation : This dialog is the first dialog that appears to the
//			user when he opens the Succeed application.  This 
//			dialog allows the user to enter a User Name and a password.
//			If no username or password is entered the user is not
//			allowed to continue.                                
//////////////////////////////////////////////////////////////////////
//////////////////////////////////////////////////////////////////////
// Revisions :		1.00.00
//   Date-initials : 
//					11/04/1998	- Added comments
//////////////////////////////////////////////////////////////////////
	/*******************************************************
	*
	*                       NOTICE
	*
	*  THIS SOFTWARE IS THE PROPERTY OF AND CONTAINS
	*  CONFIDENTIAL INFORMATION OF SSA GLOBAL 
	*  TECHNOLOGIES, INC., AND SHALL NOT BE COPIED,
	*  USED, NOR DISCLOSED WITHOUT EXPRESS WRITTEN
	*  AUTHORIZATION.  ALTHOUGH PUBLICATION IS NOT
	*  INTENDED, IN THE EVENT OF PUBLICATION, THE
	*  FOLLOWING NOTICE IS APPLICABLE:
	*
	*  (c) COPYRIGHT 2004 SSA GLOBAL TECHNOLOGIES, INC.
	*
	*           SSA GLOBAL TECHNOLOGIES, INC.
	*
	********************************************************/
//////////////////////////////////////////////////////////////////////

// LoginDlg.cpp : implementation file
//

#include "stdafx.h"
#include "LoginDlg.h"
#include <process.h>
#include "HelpService.h"
#include "ControlService.h"

#ifdef _DEBUG
#define new DEBUG_NEW
#undef THIS_FILE
static char THIS_FILE[] = __FILE__;
#endif

extern char slotDir[256];
extern CHelpService helpService;

/////////////////////////////////////////////////////////////////////////////
// CLoginDlg dialog


CLoginDlg::CLoginDlg(CWnd* pParent /*=NULL*/)
	: CDialog(CLoginDlg::IDD, pParent)
{
	//{{AFX_DATA_INIT(CLoginDlg)
	m_strPassword = _T("");
	m_strUserID = _T("");
	m_strVersionNumber = _T("");
	m_strBuildNumber = _T("");
	m_strDatabase = _T("");
	//}}AFX_DATA_INIT
	m_buildDisplayed = FALSE;
}


void CLoginDlg::DoDataExchange(CDataExchange* pDX)
{
	CDialog::DoDataExchange(pDX);
	//{{AFX_DATA_MAP(CLoginDlg)
	DDX_Control(pDX, IDC_DATABASE, m_ctlDatabase);
	DDX_Control(pDX, IDC_BUILDNUM, m_ctlBuildNumber);
	DDX_Text(pDX, IDC_PASSWORD, m_strPassword);
	DDV_MaxChars(pDX, m_strPassword, 16);
	DDX_Text(pDX, IDC_USER, m_strUserID);
	DDV_MaxChars(pDX, m_strUserID, 16);
	DDX_Text(pDX, IDC_STATIC_VERNUM, m_strVersionNumber);
	DDX_Text(pDX, IDC_BUILDNUM, m_strBuildNumber);
	DDX_CBString(pDX, IDC_DATABASE, m_strDatabase);
	//}}AFX_DATA_MAP
}


BEGIN_MESSAGE_MAP(CLoginDlg, CDialog)
	//{{AFX_MSG_MAP(CLoginDlg)
	ON_WM_RBUTTONDOWN()
	ON_WM_HELPINFO()
	ON_BN_CLICKED(IDC_AUTO_CHECK, OnAutoCheck)
	ON_CBN_SELCHANGE(IDC_DATABASE, OnSelchangeDatabase)
	ON_BN_CLICKED(IDHELP, OnHelp)
	//}}AFX_MSG_MAP
	ON_BN_CLICKED(IDOK, OnBnClickedOk)
END_MESSAGE_MAP()

/////////////////////////////////////////////////////////////////////////////
// CLoginDlg message handlers


////////////////////////////////////////////////////////////////
//This method is executed after the user selects the OK button.
//It makes sure that the user entered a User Id and Password.
////////////////////////////////////////////////////////////////
void CLoginDlg::OnOK() 
{
	UpdateData(TRUE);

	if ((m_strUserID == "") || (m_strPassword == "") || m_ctlDatabase.GetCurSel() < 0)
		return;


	//CButton *pButton = (CButton *)GetDlgItem(IDC_AUTO_RESET);

	/*
	CString autoParam;
	autoParam = GetApplicationData("AutoLoginParameters");
	if (pButton->GetCheck())
		SetApplicationData("AutoLoginParameters", "");
	else {
		pButton = (CButton *)GetDlgItem(IDC_AUTO_CHECK);
		if (pButton->GetCheck()) {
			if (autoParam == "")
				autoParam.Format("%s|", m_databaseList[m_ctlDatabase.GetCurSel()]);
			else
				autoParam.Format("%s|%d", m_databaseList[m_ctlDatabase.GetCurSel()], atol(autoParam.Mid(autoParam.Find("|")+1)));
			SetApplicationData("AutoLoginParameters", autoParam);
		}
	}
	*/
	CString temp = "";
	if (m_AutoDatabase != "") {
		temp = m_AutoDatabase;
		temp += "|";
		if (m_AutoFacility != "") {
			temp += m_AutoFacility;
		}
	}
	else {
		if (m_AutoFacility != "") {
			temp += "|";
			temp += m_AutoFacility;
		}
	}
	CControlService controlService;
	controlService.SetApplicationData("AutoLoginParameters", temp);

	CDialog::OnOK();
}


///////////////////////////////////////////////////////////////////
//This function is executed when the user clicks the right mouse
//button.
///////////////////////////////////////////////////////////////////
void CLoginDlg::OnRButtonDown(UINT nFlags, CPoint point) 
{
//	CDialog::OnRButtonDown(nFlags, point);

	UINT tmp1;
	tmp1 = MK_CONTROL+MK_RBUTTON;

	//if the user clicked the right mouse button plus the
	//Control key then we either display the Build Number
	//or hide it (depending what is the current stage).
	//The right mouse click plus the Control key act as a 
	//toggle key.
	if (nFlags == tmp1)
	{
		if (m_buildDisplayed)
		{
			m_ctlBuildNumber.ShowWindow(SW_HIDE);
			m_buildDisplayed = FALSE;
		}
		else
		{
			m_ctlBuildNumber.ShowWindow(SW_SHOW);
			m_buildDisplayed = TRUE;
		}
	}

	
	CDialog::OnRButtonDown(nFlags, point);
}


///////////////////////////////////////////////////////////
//This method is executed during the initialization of the
//dialog.
///////////////////////////////////////////////////////////
BOOL CLoginDlg::OnInitDialog() 
{
	CDialog::OnInitDialog();
	CString temp;
	CButton *pCheckBtn = (CButton *)GetDlgItem(IDC_AUTO_CHECK);

	m_ctlBuildNumber.ShowWindow(SW_HIDE);
	m_buildDisplayed = FALSE;

	for (int i=0; i < m_databaseList.GetSize(); ++i)
			m_ctlDatabase.AddString(m_databaseList[i]);

	UpdateData(FALSE);

	m_ctlDatabase.SetCurSel(0);
	m_ctlDatabase.SetItemHeight(0,2000);

	CControlService controlService;
	temp = controlService.GetApplicationData("AutoLoginParameters");
	if (temp != "") {
		if (temp.Find("|") > 0) {		// both database and facility specified
			m_AutoDatabase = temp.Left(temp.Find("|"));
			m_AutoFacility = temp.Mid(temp.Find("|")+1);
		}
		else if (temp.Find("|") == 0) {	// if first char is | then only facility specified
			m_AutoDatabase = "";
			m_AutoFacility = temp.Mid(temp.Find("|")+1);
		}
		else {							// if no | and string not blank, only database specified
			m_AutoDatabase = temp;
			m_AutoFacility = "";
		}
	}
	else {
		m_AutoDatabase = "";
		m_AutoFacility = "";
	}

	if (m_AutoDatabase != "") {
		int curSel = m_ctlDatabase.FindStringExact(0, m_AutoDatabase);
		m_ctlDatabase.SetCurSel(curSel);
		pCheckBtn->SetCheck(1);
	}
	else {
		int curSel = m_ctlDatabase.FindStringExact(0, controlService.GetApplicationData("MRUDB"));
		m_ctlDatabase.SetCurSel(curSel);
		pCheckBtn->SetCheck(0);
	}

	if (m_strUserID != "") {
		CEdit *pPassword = (CEdit *)GetDlgItem(IDC_PASSWORD);
		pPassword->SetSel(0,-1,FALSE);
		pPassword->SetFocus();
		return FALSE;
	}

	
	return TRUE;  // return TRUE unless you set the focus to a control
	              // EXCEPTION: OCX Property Pages should return FALSE
}


/////////////////////////////////////////////////////////////
//This method is executed when the user hits the F1 button.
//A pop help message is displayed if the user is on the Password
//field.
/////////////////////////////////////////////////////////////
BOOL CLoginDlg::OnHelpInfo(HELPINFO* pHelpInfo) 
{
	helpService.ShowFieldHelp(IDD, pHelpInfo->iCtrlId);

	return TRUE;	

}

void CLoginDlg::OnAutoCheck() 
{
	CButton *pCheckBtn = (CButton *)GetDlgItem(IDC_AUTO_CHECK);
	UpdateData(TRUE);

	if (pCheckBtn->GetCheck()) {
		if (m_ctlDatabase.GetCurSel() < 0) {
			pCheckBtn->SetCheck(0);
			AfxMessageBox("Please select a database from the list.");
			m_ctlDatabase.ShowDropDown(TRUE);
			return;
		}
		else {
			m_ctlDatabase.GetWindowText(m_AutoDatabase);
		}
	}
	else {
		m_AutoDatabase = "";
	}


}


void CLoginDlg::OnSelchangeDatabase() 
{
	CString database;
	CButton *pCheckBtn = (CButton *)GetDlgItem(IDC_AUTO_CHECK);	

	m_ctlDatabase.GetWindowText(database);

	if (database == m_AutoDatabase) {
		pCheckBtn->SetCheck(1);
	}
	else {
		pCheckBtn->SetCheck(0);
	}

}

void CLoginDlg::OnHelp() 
{
	helpService.ShowScreenHelp(IDD);	
}

void CLoginDlg::OnBnClickedOk()
{
	// TODO: Add your control notification handler code here
	OnOK();
}

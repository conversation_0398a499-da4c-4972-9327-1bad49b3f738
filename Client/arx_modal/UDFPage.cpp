// UDFPage.cpp : implementation file
//

#include "stdafx.h"
#include "UDFPage.h"
#include "UDF.h"
#include "HelpService.h"
#include "UtilityHelper.h"

#ifdef _DEBUG
#define new DEBUG_NEW
#undef THIS_FILE
static char THIS_FILE[] = __FILE__;
#endif

extern CHelpService helpService;
extern CUtilityHelper utilityHelper;
/////////////////////////////////////////////////////////////////////////////
// CUDFPage property page

IMPLEMENT_DYNCREATE(CUDFPage, CPropertyPage)

CUDFPage::CUDFPage() : CPropertyPage(CUDFPage::IDD)
{
	//{{AFX_DATA_INIT(CUDFPage)
		// NOTE: the ClassWizard will add member initialization here
	//}}AFX_DATA_INIT
}

CUDFPage::~CUDFPage()
{
	for (int i=0; i < m_Values.GetSize(); ++i) {
		if (m_UDFs[i]->m_Type == DT_LIST)
			delete (CComboBox *)m_Values[i];
		else
			delete (CEdit *)m_Values[i];
		delete m_UDFs[i];
		delete m_Names[i];
	}
}

void CUDFPage::DoDataExchange(CDataExchange* pDX)
{
	CPropertyPage::DoDataExchange(pDX);
	//{{AFX_DATA_MAP(CUDFPage)
		// NOTE: the ClassWizard will add DDX and DDV calls here
	//}}AFX_DATA_MAP
	CString temp;

	if (m_Values.GetSize() > 0) {	// Make sure we've built the screen
		if (! pDX->m_bSaveAndValidate) {
			for (int i=0; i < m_UDFs.GetSize(); ++i) {
				CUDF *pUDF = m_UDFs[i];
				if (pUDF->m_Type == DT_LIST) {
					CComboBox *pComboBox = (CComboBox *)m_Values[i];
					pComboBox->SetCurSel(pComboBox->FindStringExact(0, pUDF->m_Value));
				}
				else {
					CEdit *pEdit = (CEdit *)m_Values[i];
					if (pUDF->m_Value != "") {
						if (pUDF->m_Type == DT_INT)
							temp.Format("%d", pUDF->m_IntegerValue);
						else if (pUDF->m_Type == DT_FLOAT)
							temp.Format("%0.3f", pUDF->m_FloatValue);
						else
							temp = pUDF->m_Value;
					}
					else
						temp = "";
					pEdit->SetWindowText(temp);
				}
			}
		}
		else {
			for (int i=0; i < m_UDFs.GetSize(); ++i) {
				CUDF *pUDF = m_UDFs[i];
				if (pUDF->m_Type == DT_LIST) {
					CComboBox *pComboBox = (CComboBox *)m_Values[i];
					pComboBox->GetWindowText(pUDF->m_Value);
				}
				else {
					CEdit *pEdit = (CEdit *)m_Values[i];
					pEdit->GetWindowText(pUDF->m_Value);
				}

				if (utilityHelper.IsNumeric(pUDF->m_Value)) {
					pUDF->m_IntegerValue = atoi(pUDF->m_Value);
					pUDF->m_FloatValue = atof(pUDF->m_Value);
				}
				else {
					pUDF->m_IntegerValue = 0;
					pUDF->m_FloatValue = 0.0;
				}
			}
		}
	}
}


BEGIN_MESSAGE_MAP(CUDFPage, CPropertyPage)
	//{{AFX_MSG_MAP(CUDFPage)
	ON_WM_VSCROLL()
	ON_WM_SIZE()
	ON_WM_MOUSEWHEEL()
	ON_WM_HELPINFO()
	//}}AFX_MSG_MAP
END_MESSAGE_MAP()

/////////////////////////////////////////////////////////////////////////////
// CUDFPage message handlers
void CUDFPage::OnVScroll(UINT nSBCode, UINT nPos, CScrollBar* pScrollBar) 
{
	CRect r;
	int yInc;
	SCROLLINFO si;

	this->GetClientRect(&r);

	switch (nSBCode) {
	case SB_BOTTOM:
		AfxMessageBox("Bottom");
		//SetScrollPos(SB_VERT, 
		break;
	case SB_LINEDOWN:
		yInc = 5;
		break;
	case SB_LINEUP:
		yInc = -5;
		break;
	case SB_PAGEDOWN:
		yInc = r.Height();
		break;
	case SB_PAGEUP:
		yInc = -r.Height();
		break;
	case SB_TOP:
		AfxMessageBox("Top");
		break;
	case SB_THUMBTRACK:
		yInc = nPos - m_scrollPos;
		break;
	default:
		yInc = 0;
		break;
	}

	if (yInc != 0) {
		this->GetScrollInfo(SB_VERT, &si, SIF_ALL);
		if (m_scrollPos + yInc > si.nMax) {
			yInc = si.nMax - m_scrollPos;
			m_scrollPos = si.nMax;
		} else if (m_scrollPos + yInc < 0) {
			yInc = -m_scrollPos;
			m_scrollPos = 0;
		} else {
			m_scrollPos += yInc;
		}
		
		ScrollWindow(0, -yInc);
		si.fMask = SIF_POS;
		si.cbSize = sizeof(si);
		si.nPos = m_scrollPos;
		this->SetScrollInfo(SB_VERT, &si, TRUE);
		UpdateWindow();
	}

	
	CPropertyPage::OnVScroll(nSBCode, nPos, pScrollBar);
}

void CUDFPage::OnSize(UINT nType, int cx, int cy) 
{
	
	SCROLLINFO si;
	int newMax, oldMax;
	CRect r;

	this->GetClientRect(&r);

	CPropertyPage::OnSize(nType, cx, cy);

	this->GetScrollInfo(SB_VERT, &si, SIF_ALL);

	si.fMask = SIF_RANGE | SIF_PAGE | SIF_POS;
	si.cbSize = sizeof(si);
	si.nMin = 0;
	oldMax = si.nMax;
	newMax = max(m_bottom - cy, 0);
	if (newMax == 0)
		ScrollWindow(0, m_scrollPos);

	si.nPos = min(m_scrollPos, newMax);
	m_scrollPos = si.nPos;
	si.nMax = newMax;
	si.nPage = 5;
	SetScrollInfo(SB_VERT, &si);
	
	return;
	
}

BOOL CUDFPage::OnInitDialog() 
{
	CPropertyPage::OnInitDialog();
	
	if (m_UDFs.GetSize() == 0) {
		GetDlgItem(IDC_STATIC_MESSAGE)->ShowWindow(SW_SHOW);
	}
	else {
		GetDlgItem(IDC_STATIC_MESSAGE)->ShowWindow(SW_HIDE);
		CreateDisplay();
	}
	
	return TRUE;  // return TRUE unless you set the focus to a control
	              // EXCEPTION: OCX Property Pages should return FALSE
}


void CUDFPage::CreateDisplay()
{
	CRect clientRect, r, r2;
	CString tmp, str;
	CUDF *pUDF;
	CStatic *pStatic;
	CEdit *pEdit;
	CComboBox *pComboBox;
	SCROLLINFO si;
	CFont *font = this->GetFont();
	CWnd *pWnd;
	CString temp;

	this->GetClientRect(&clientRect);

	r.left = 3;							// starting x position within property page
	r.top = 50;							// starting y position within property page
	r.right = clientRect.Width()/3;		// use a third of the page for the static text
	r.bottom = r.top + 30;				// the size of each field is 30

	for (int i=0; i < m_UDFs.GetSize(); ++i) {

		pUDF = m_UDFs[i];
		pStatic = new CStatic;
		r.top += 5;		// to make more centered
		pStatic->Create(NULL, WS_VISIBLE|SS_RIGHT, r, this, 0);
		str = pUDF->m_Name;
		str += ":";
		pStatic->SetFont(font);
		pStatic->SetWindowText(str);
		m_Names.Add(pStatic);

		r.top -= 5;
		r2.left = clientRect.Width()/3 + 10;
		r2.top = r.top;
		r2.right = clientRect.right - clientRect.Width()/3;
		r2.bottom = r2.top + 30;

		if (pUDF->m_Type != DT_LIST) {
			pEdit = new CEdit;
			pEdit->CreateEx( WS_EX_CLIENTEDGE, "EDIT", "", WS_VISIBLE|WS_CHILD|WS_TABSTOP|ES_AUTOHSCROLL|WS_BORDER, r2, 
				this, WM_USER+i);
			pEdit->SetFont(font);
			if (pUDF->m_Value != "") {
				if (pUDF->m_Type == DT_INT)
					temp.Format("%d", pUDF->m_IntegerValue);
				else if (pUDF->m_Type == DT_FLOAT)
					temp.Format("%0.3f", pUDF->m_FloatValue);
				else
					temp = pUDF->m_Value;
			}

			pEdit->SetWindowText(temp);
			pEdit->EnableWindow(TRUE);
			//pEdit->SetLimitText(255);
			m_Values.Add((CWnd *)pEdit);
		}
		else {
			pComboBox = new CComboBox;
			pComboBox->Create(WS_VISIBLE|WS_CHILD|WS_TABSTOP|WS_BORDER|CBS_DROPDOWNLIST, 
				r2, this, WM_USER+i);
			pComboBox->SetFont(font);
			pComboBox->EnableWindow(TRUE);
			for (int j=0; j < pUDF->m_ListValues.GetSize(); ++j)
				pComboBox->AddString(pUDF->m_ListValues[j]);
			pComboBox->SetCurSel(pComboBox->FindStringExact(0, pUDF->m_Value));
			m_Values.Add((CWnd *)pComboBox);
			pComboBox->SetWindowPos(NULL, 0, 0, r2.Width(), r2.Height()*pUDF->m_ListValues.GetSize()+1, SWP_NOMOVE|SWP_NOZORDER);
		}

		r.top = r.top + 30 + 10;
		r.bottom = r.top + 30;

	}

	if (m_Values.GetSize() > 0) {
		pWnd = m_Values[0];
		pWnd->SetFocus();
	}

	m_bottom = r.bottom;		// save the position of the edit for scrolling purposes
	
	this->GetClientRect(&r);

	si.fMask = SIF_RANGE | SIF_PAGE | SIF_POS;
	si.cbSize = sizeof(si);
	si.nMin = 0;
	si.nMax = m_bottom - r.Height();	// subtract viewable height so we scroll to top of last page
	si.nPage = 5;						// I chose this value at random
	si.nPos = 0;
	m_scrollPos = 0;
	SetScrollInfo(SB_VERT, &si);

	UpdateData(FALSE);
}

BOOL CUDFPage::OnApply() 
{
	int i;
	CString value, str;
	CUDF *pUDF;
	CEdit *pEdit;
	CComboBox *pComboBox;
	CPropertySheet *pParent = (CPropertySheet *)this->GetParent();

	if (m_UDFs.GetSize() > 0) {

		for	(i=0; i < m_UDFs.GetSize(); ++i) {
			pUDF = m_UDFs[i];
			if (pUDF->m_Type == DT_LIST) {
				pComboBox = (CComboBox *)m_Values[i];
				pComboBox->GetWindowText(value);
			}
			else {
				pEdit = (CEdit *)m_Values[i];
				pEdit->GetWindowText(value);
				
				switch (pUDF->m_Type) {
				case DT_INT:
					if (! utilityHelper.IsInteger(value)) {
						str.Format("Please enter a valid integer for UDF %s.", pUDF->m_Name);
						AfxMessageBox(str);
						pParent->SetActivePage(this);
						pEdit->SetSel(0,-1);
						pEdit->SetFocus();
						return FALSE;
					}
					break;
				case DT_FLOAT:
					
					if (! utilityHelper.IsFloat(value)) {
						str.Format("Please enter a valid floating point number for UDF %s.", pUDF->m_Name);
						AfxMessageBox(str);
						pParent->SetActivePage(this);
						pEdit->SetSel(0,-1);
						pEdit->SetFocus();
						return FALSE;
					}
					break;
				}
			}
			
			pUDF->m_Value = value;
			pUDF->m_IntegerValue = 0;
			pUDF->m_FloatValue = 0;
			if (pUDF->m_Type == DT_INT) {
				pUDF->m_IntegerValue = atol(value);
				pUDF->m_FloatValue = atof(value);
			}
			else if (pUDF->m_Type == DT_FLOAT) {
				pUDF->m_FloatValue = atof(value);
				pUDF->m_IntegerValue = atoi(value);
			}
			else {
				if (utilityHelper.IsNumeric(pUDF->m_Value)) {
					pUDF->m_FloatValue = atof(pUDF->m_Value);
					pUDF->m_IntegerValue = atoi(pUDF->m_Value);
				}
				else {
					pUDF->m_FloatValue = 0;
					pUDF->m_IntegerValue = 0;
				}
			}

		}
	}

			  
	return CPropertyPage::OnApply();
}


BOOL CUDFPage::OnMouseWheel(UINT nFlags, short zDelta, CPoint pt) 
{
	CRect r;
	int yInc;
	SCROLLINFO si;

	yInc = zDelta / -24;

	if (yInc != 0) {
		this->GetScrollInfo(SB_VERT, &si, SIF_ALL);
		if (m_scrollPos + yInc > si.nMax) {
			yInc = si.nMax - m_scrollPos;
			m_scrollPos = si.nMax;
		} else if (m_scrollPos + yInc < 0) {
			yInc = -m_scrollPos;
			m_scrollPos = 0;
		} else {
			m_scrollPos += yInc;
		}
		
		ScrollWindow(0, -yInc);
		si.fMask = SIF_POS;
		si.cbSize = sizeof(si);
		si.nPos = m_scrollPos;
		this->SetScrollInfo(SB_VERT, &si, TRUE);
		UpdateWindow();
	}

	
	return CPropertyPage::OnMouseWheel(nFlags, zDelta, pt);
}





BOOL CUDFPage::OnNotify(WPARAM wParam, LPARAM lParam, LRESULT* pResult) 
{
	NMHDR* pNMHDR = (NMHDR*)lParam;
	if (pNMHDR->code == PSN_HELP) {
		helpService.ShowScreenHelp(IDD);
		return TRUE;
	}		
	return CPropertyPage::OnNotify(wParam, lParam, pResult);
}

BOOL CUDFPage::OnHelpInfo(HELPINFO* pHelpInfo) 
{
	helpService.ShowFieldHelp(IDD, pHelpInfo->iCtrlId);
	return FALSE;
}

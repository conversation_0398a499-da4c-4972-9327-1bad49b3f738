// FacingInfo.h: interface for the CFacingInfo class.
//
//////////////////////////////////////////////////////////////////////

#if !defined(AFX_FACINGINFO_H__BB26F4E1_08A8_4A57_86B0_784FE6811F2F__INCLUDED_)
#define AFX_FACINGINFO_H__BB26F4E1_08A8_4A57_86B0_784FE6811F2F__INCLUDED_

#if _MSC_VER > 1000
#pragma once
#endif // _MSC_VER > 1000

class CFacingInfo : public CObject  
{
public:
	CFacingInfo();
	CFacingInfo(const CFacingInfo& other);
	CFacingInfo& operator=(const CFacingInfo &other);
	BOOL operator==(const CFacingInfo& other);
	BOOL operator!=(const CFacingInfo& other) { return !(*this == other);};
	virtual ~CFacingInfo();

	int Parse(CString &line);
	
	int m_FacingInfoDBId;
	CString m_Description;
	double m_ExtendedCube;
	double m_ExtendedBOH;
	int m_FacingCount;
	int m_BayRuleDBId;
};

#endif // !defined(AFX_FACINGINFO_H__BB26F4E1_08A8_4A57_86B0_784FE6811F2F__INCLUDED_)

// NewSectionPage2.cpp : implementation file
//

#include "stdafx.h"
#include "modal.h"
#include "NewSectionPage2.h"
#include "HelpService.h"

#ifdef _DEBUG
#define new DEBUG_NEW
#undef THIS_FILE
static char THIS_FILE[] = __FILE__;
#endif

extern char slotDir[256];
extern CHelpService helpService;

/////////////////////////////////////////////////////////////////////////////
// CNewSectionPage2 property page

IMPLEMENT_DYNCREATE(CNewSectionPage2, CPropertyPage)

CNewSectionPage2::CNewSectionPage2() : CPropertyPage(CNewSectionPage2::IDD)
{
	//{{AFX_DATA_INIT(CNewSectionPage2)
	m_NewSection_ForkDistFixed = 0.0f;
	m_NewSection_ForkDistVar = 0.0f;
	m_NewSection_ForkLaborRate = 0.0f;
	m_NewSection_ReplAvgDist = 0.0f;
	m_NewSection_SelFixedFactor = 0.0f;
	m_NewSection_SelLaborRate = 0.0f;
	m_NewSection_SelVarFactor = 0.0f;
	m_NewSection_ForkInsertion = 0.0f;
	m_NewSection_ForkPickup = 0.0f;
	m_NewSection_PalletsPerTrip = 0.0f;
	m_NewSection_StockerFixed = 0.0f;
	m_NewSection_StockerLaborRate = 0.0f;
	m_NewSection_StockerVar = 0.0f;
	m_NewSection_AvgCubePerTrip = 0.0f;
	//}}AFX_DATA_INIT
}

CNewSectionPage2::~CNewSectionPage2()
{
}

void CNewSectionPage2::DoDataExchange(CDataExchange* pDX)
{
	CPropertyPage::DoDataExchange(pDX);
	//{{AFX_DATA_MAP(CNewSectionPage2)
	DDX_Control(pDX, IDC_NEWSECTION_AVGCUBEPERTRIP, m_NewSectionAvgCubePerTrip_Box);
	DDX_Control(pDX, IDC_NEWSECTION_STOCKERVAR, m_NewSectionStockerVar_Box);
	DDX_Control(pDX, IDC_NEWSECTION_STOCKERLABORRATE, m_NewSectionStockerLaborRate_Box);
	DDX_Control(pDX, IDC_NEWSECTION_STOCKERFIXED, m_NewSectionStockerFixed_Box);
	DDX_Control(pDX, IDC_NEWSECTION_PALLETS_PER_TRIP, m_NewSectionPalletsPerTrip_Box);
	DDX_Control(pDX, IDC_NEWSECTION_FORKPICKUP, m_NewSectionForkPickup_Box);
	DDX_Control(pDX, IDC_NEWSECTION_FORKINSERTION, m_NewSectionForkInsertion_Box);
	DDX_Control(pDX, IDC_NEWSECTION_SELVARFACTOR, m_NewSectionSelVarFactor_Box);
	DDX_Control(pDX, IDC_NEWSECTION_SELLABORRATE, m_NewSectionSelLabor_Box);
	DDX_Control(pDX, IDC_NEWSECTION_SELFIXEDFACTOR, m_NewSectionFixedFactor_Box);
	DDX_Control(pDX, IDC_NEWSECTION_REPLAVGDIST, m_NewSectionReplAvgDist_Box);
	DDX_Control(pDX, IDC_NEWSECTION_FORKLABORRATE, m_NewSectionForkLabor_Box);
	DDX_Control(pDX, IDC_NEWSECTION_FORKDISTVAR, m_NewSectionDistVar_Box);
	DDX_Control(pDX, IDC_NEWSECTION_FORKDISTFIXED, m_NewSectionDistFixed_Box);
	DDX_Text(pDX, IDC_NEWSECTION_FORKDISTFIXED, m_NewSection_ForkDistFixed);
	DDX_Text(pDX, IDC_NEWSECTION_FORKDISTVAR, m_NewSection_ForkDistVar);
	DDX_Text(pDX, IDC_NEWSECTION_FORKLABORRATE, m_NewSection_ForkLaborRate);
	DDX_Text(pDX, IDC_NEWSECTION_REPLAVGDIST, m_NewSection_ReplAvgDist);
	DDX_Text(pDX, IDC_NEWSECTION_SELFIXEDFACTOR, m_NewSection_SelFixedFactor);
	DDX_Text(pDX, IDC_NEWSECTION_SELLABORRATE, m_NewSection_SelLaborRate);
	DDX_Text(pDX, IDC_NEWSECTION_SELVARFACTOR, m_NewSection_SelVarFactor);
	DDX_Text(pDX, IDC_NEWSECTION_FORKINSERTION, m_NewSection_ForkInsertion);
	DDX_Text(pDX, IDC_NEWSECTION_FORKPICKUP, m_NewSection_ForkPickup);
	DDX_Text(pDX, IDC_NEWSECTION_PALLETS_PER_TRIP, m_NewSection_PalletsPerTrip);
	DDX_Text(pDX, IDC_NEWSECTION_STOCKERFIXED, m_NewSection_StockerFixed);
	DDX_Text(pDX, IDC_NEWSECTION_STOCKERLABORRATE, m_NewSection_StockerLaborRate);
	DDX_Text(pDX, IDC_NEWSECTION_STOCKERVAR, m_NewSection_StockerVar);
	DDX_Text(pDX, IDC_NEWSECTION_AVGCUBEPERTRIP, m_NewSection_AvgCubePerTrip);
	//}}AFX_DATA_MAP
}


BEGIN_MESSAGE_MAP(CNewSectionPage2, CPropertyPage)
	//{{AFX_MSG_MAP(CNewSectionPage2)
	ON_EN_KILLFOCUS(IDC_NEWSECTION_FORKDISTFIXED, OnKillfocusNewsectionForkdistfixed)
	ON_EN_KILLFOCUS(IDC_NEWSECTION_FORKDISTVAR, OnKillfocusNewsectionForkdistvar)
	ON_EN_KILLFOCUS(IDC_NEWSECTION_FORKINSERTION, OnKillfocusNewsectionForkinsertion)
	ON_EN_KILLFOCUS(IDC_NEWSECTION_FORKLABORRATE, OnKillfocusNewsectionForklaborrate)
	ON_EN_KILLFOCUS(IDC_NEWSECTION_FORKPICKUP, OnKillfocusNewsectionForkpickup)
	ON_EN_KILLFOCUS(IDC_NEWSECTION_PALLETS_PER_TRIP, OnKillfocusNewsectionPalletsPerTrip)
	ON_EN_KILLFOCUS(IDC_NEWSECTION_REPLAVGDIST, OnKillfocusNewsectionReplavgdist)
	ON_EN_KILLFOCUS(IDC_NEWSECTION_SELFIXEDFACTOR, OnKillfocusNewsectionSelfixedfactor)
	ON_EN_KILLFOCUS(IDC_NEWSECTION_SELLABORRATE, OnKillfocusNewsectionSellaborrate)
	ON_EN_KILLFOCUS(IDC_NEWSECTION_SELVARFACTOR, OnKillfocusNewsectionSelvarfactor)
	ON_EN_KILLFOCUS(IDC_NEWSECTION_STOCKERFIXED, OnKillfocusNewsectionStockerfixed)
	ON_EN_KILLFOCUS(IDC_NEWSECTION_STOCKERLABORRATE, OnKillfocusNewsectionStockerlaborrate)
	ON_EN_KILLFOCUS(IDC_NEWSECTION_STOCKERVAR, OnKillfocusNewsectionStockervar)
	ON_WM_HELPINFO()
	ON_EN_KILLFOCUS(IDC_NEWSECTION_AVGCUBEPERTRIP, OnKillfocusNewsectionAvgcubepertrip)
	//}}AFX_MSG_MAP
END_MESSAGE_MAP()

/////////////////////////////////////////////////////////////////////////////
// CNewSectionPage2 message handlers

void CNewSectionPage2::OnKillfocusNewsectionForkdistfixed() 
{
	// TODO: Add your control notification handler code here
	char buf[32]="";
	char buf2[32]="";
	char * ptr;

	if (this->IsWindowVisible() == 0)
		return;

	if (UpdateData(TRUE) == 0 ) {
		m_NewSectionDistFixed_Box.SetFocus();
		return;
	}
	if (m_NewSection_ForkDistFixed < 0 ) {
		AfxMessageBox("Number must be non-negative.");
		m_NewSectionDistFixed_Box.SetFocus();
	}
	if (m_NewSection_ForkDistFixed > 999999999.0 ) {
		AfxMessageBox("Number is out of range.");
		m_NewSection_ForkDistFixed = 0.0;
		UpdateData(FALSE);
		m_NewSectionDistFixed_Box.SetFocus();
	}
	m_NewSectionDistFixed_Box.GetWindowText(buf,31);
	if ( strchr(buf,'+') != NULL ) {
		ptr = strchr(buf,'+');
		ptr++;
		strcpy(buf2,ptr);
		m_NewSectionDistFixed_Box.SetWindowText(buf2);
	}
	UpdateData(FALSE);	
}

void CNewSectionPage2::OnKillfocusNewsectionForkdistvar() 
{
	// TODO: Add your control notification handler code here
	char buf[32]="";
	char buf2[32]="";
	char * ptr;

	if (this->IsWindowVisible() == 0)
		return;

	if (UpdateData(TRUE) == 0 ) {
		m_NewSectionDistVar_Box.SetFocus();
		return;
	}
	if (m_NewSection_ForkDistVar < 0 ) {
		AfxMessageBox("Number must be non-negative.");
		m_NewSectionDistVar_Box.SetFocus();
	}
	if (m_NewSection_ForkDistVar > 999999999.0 ) {
		AfxMessageBox("Number is out of range.");
		m_NewSection_ForkDistVar = 0.0;
		UpdateData(FALSE);
		m_NewSectionDistVar_Box.SetFocus();
	}
	m_NewSectionDistVar_Box.GetWindowText(buf,31);
	if ( strchr(buf,'+') != NULL ) {
		ptr = strchr(buf,'+');
		ptr++;
		strcpy(buf2,ptr);
		m_NewSectionDistVar_Box.SetWindowText(buf2);
	}
	UpdateData(FALSE);	
}

void CNewSectionPage2::OnKillfocusNewsectionForkinsertion() 
{
	// TODO: Add your control notification handler code here
	char buf[32]="";
	char buf2[32]="";
	char * ptr;

	if (this->IsWindowVisible() == 0)
		return;

	if (UpdateData(TRUE) == 0 ) {
		m_NewSectionForkInsertion_Box.SetFocus();
		return;
	}
	if (m_NewSection_ForkInsertion< 0 ) {
		AfxMessageBox("Number must be non-negative.");
		m_NewSectionForkInsertion_Box.SetFocus();
	}
	if (m_NewSection_ForkInsertion > 999999999.0 ) {
		AfxMessageBox("Number is out of range.");
		m_NewSection_ForkInsertion = 0.0;
		UpdateData(FALSE);
		m_NewSectionForkInsertion_Box.SetFocus();
	}
	m_NewSectionForkInsertion_Box.GetWindowText(buf,31);
	if ( strchr(buf,'+') != NULL ) {
		ptr = strchr(buf,'+');
		ptr++;
		strcpy(buf2,ptr);
		m_NewSectionForkInsertion_Box.SetWindowText(buf2);
	}
	UpdateData(FALSE);		
}

void CNewSectionPage2::OnKillfocusNewsectionForklaborrate() 
{
	// TODO: Add your control notification handler code here
	char buf[32]="";
	char buf2[32]="";
	char * ptr;

	if (this->IsWindowVisible() == 0)
		return;

	if (UpdateData(TRUE) == 0 ) {
		m_NewSectionForkLabor_Box.SetFocus();
		return;
	}
	if (m_NewSection_ForkLaborRate < 0 ) {
		AfxMessageBox("Number must be non-negative.");
		m_NewSectionForkLabor_Box.SetFocus();
	}
	if (m_NewSection_ForkLaborRate > 999999999.0 ) {
		AfxMessageBox("Number is out of range.");
		m_NewSection_ForkLaborRate = 0.0;
		UpdateData(FALSE);
		m_NewSectionForkLabor_Box.SetFocus();
	}
	m_NewSectionForkLabor_Box.GetWindowText(buf,31);
	if ( strchr(buf,'+') != NULL ) {
		ptr = strchr(buf,'+');
		ptr++;
		strcpy(buf2,ptr);
		m_NewSectionForkLabor_Box.SetWindowText(buf2);
	}
	UpdateData(FALSE);	
}

void CNewSectionPage2::OnKillfocusNewsectionForkpickup() 
{
	// TODO: Add your control notification handler code here
	char buf[32]="";
	char buf2[32]="";
	char * ptr;

	if (this->IsWindowVisible() == 0)
		return;

	if (UpdateData(TRUE) == 0 ) {
		m_NewSectionForkPickup_Box.SetFocus();
		return;
	}
	if (m_NewSection_ForkPickup < 0 ) {
		AfxMessageBox("Number must be non-negative.");
		m_NewSectionForkPickup_Box.SetFocus();
	}
	if (m_NewSection_ForkPickup > 999999999.0 ) {
		AfxMessageBox("Number is out of range.");
		m_NewSection_ForkPickup = 0.0;
		UpdateData(FALSE);
		m_NewSectionForkPickup_Box.SetFocus();
	}
	m_NewSectionForkPickup_Box.GetWindowText(buf,31);
	if ( strchr(buf,'+') != NULL ) {
		ptr = strchr(buf,'+');
		ptr++;
		strcpy(buf2,ptr);
		m_NewSectionForkPickup_Box.SetWindowText(buf2);
	}
	UpdateData(FALSE);		
}

void CNewSectionPage2::OnKillfocusNewsectionPalletsPerTrip() 
{
	// TODO: Add your control notification handler code here
	char buf[32]="";
	char buf2[32]="";
	char * ptr;

	if (this->IsWindowVisible() == 0)
		return;

	if (UpdateData(TRUE) == 0 ) {
		m_NewSectionPalletsPerTrip_Box.SetFocus();
		return;
	}
	if (m_NewSection_PalletsPerTrip < 0 ) {
		AfxMessageBox("Number must be non-negative.");
		m_NewSectionPalletsPerTrip_Box.SetFocus();
	}
	if (m_NewSection_PalletsPerTrip > 999999999.0 ) {
		AfxMessageBox("Number is out of range.");
		m_NewSection_PalletsPerTrip = 0.0;
		UpdateData(FALSE);
		m_NewSectionPalletsPerTrip_Box.SetFocus();
	}
	m_NewSectionPalletsPerTrip_Box.GetWindowText(buf,31);
	if ( strchr(buf,'+') != NULL ) {
		ptr = strchr(buf,'+');
		ptr++;
		strcpy(buf2,ptr);
		m_NewSectionPalletsPerTrip_Box.SetWindowText(buf2);
	}
	UpdateData(FALSE);		
}

void CNewSectionPage2::OnKillfocusNewsectionReplavgdist() 
{
	// TODO: Add your control notification handler code here
	char buf[32]="";
	char buf2[32]="";
	char * ptr;

	if (this->IsWindowVisible() == 0)
		return;

	if (UpdateData(TRUE) == 0 ) {
		m_NewSectionReplAvgDist_Box.SetFocus();
		return;
	}
	if (m_NewSection_ReplAvgDist < 0 ) {
		AfxMessageBox("Number must be non-negative.");
		m_NewSectionReplAvgDist_Box.SetFocus();
	}
	if (m_NewSection_ReplAvgDist > 999999999.0 ) {
		AfxMessageBox("Number is out of range.");
		m_NewSection_ReplAvgDist = 0.0;
		UpdateData(FALSE);
		m_NewSectionReplAvgDist_Box.SetFocus();
	}
	m_NewSectionReplAvgDist_Box.GetWindowText(buf,31);
	if ( strchr(buf,'+') != NULL ) {
		ptr = strchr(buf,'+');
		ptr++;
		strcpy(buf2,ptr);
		m_NewSectionReplAvgDist_Box.SetWindowText(buf2);
	}
	UpdateData(FALSE);
}

void CNewSectionPage2::OnKillfocusNewsectionSelfixedfactor() 
{
	// TODO: Add your control notification handler code here
	char buf[32]="";
	char buf2[32]="";
	char * ptr;

	if (this->IsWindowVisible() == 0)
		return;

	if (UpdateData(TRUE) == 0 ) {
		m_NewSectionFixedFactor_Box.SetFocus();
		return;
	}
	if (m_NewSection_SelFixedFactor < 0 ) {
		AfxMessageBox("Number must be non-negative.");
		m_NewSectionFixedFactor_Box.SetFocus();
	}
	if (m_NewSection_SelFixedFactor > 999999999.0 ) {
		AfxMessageBox("Number is out of range.");
		m_NewSection_SelFixedFactor = 0.0;
		UpdateData(FALSE);
		m_NewSectionFixedFactor_Box.SetFocus();
	}
	m_NewSectionFixedFactor_Box.GetWindowText(buf,31);
	if ( strchr(buf,'+') != NULL ) {
		ptr = strchr(buf,'+');
		ptr++;
		strcpy(buf2,ptr);
		m_NewSectionFixedFactor_Box.SetWindowText(buf2);
	}
	UpdateData(FALSE);	
}

void CNewSectionPage2::OnKillfocusNewsectionSellaborrate() 
{
	// TODO: Add your control notification handler code here
	char buf[32]="";
	char buf2[32]="";
	char * ptr;

	if (this->IsWindowVisible() == 0)
		return;

	if (UpdateData(TRUE) == 0 ) {
		m_NewSectionSelLabor_Box.SetFocus();
		return;
	}
	if (m_NewSection_SelLaborRate < 0 ) {
		AfxMessageBox("Number must be non-negative.");
		m_NewSectionSelLabor_Box.SetFocus();
	}
	if (m_NewSection_SelLaborRate > 999999999.0 ) {
		AfxMessageBox("Number is out of range.");
		m_NewSection_SelLaborRate = 0.0;
		UpdateData(FALSE);
		m_NewSectionSelLabor_Box.SetFocus();
	}
	m_NewSectionSelLabor_Box.GetWindowText(buf,31);
	if ( strchr(buf,'+') != NULL ) {
		ptr = strchr(buf,'+');
		ptr++;
		strcpy(buf2,ptr);
		m_NewSectionSelLabor_Box.SetWindowText(buf2);
	}
	UpdateData(FALSE);
		
}

void CNewSectionPage2::OnKillfocusNewsectionSelvarfactor() 
{
	// TODO: Add your control notification handler code here
	char buf[32]="";
	char buf2[32]="";
	char * ptr;

	if (this->IsWindowVisible() == 0)
		return;

	if (UpdateData(TRUE) == 0 ) {
		m_NewSectionSelVarFactor_Box.SetFocus();
		return;
	}
	if (m_NewSection_SelVarFactor < 0 ) {
		AfxMessageBox("Number must be non-negative.");
		m_NewSectionSelVarFactor_Box.SetFocus();
	}
	if (m_NewSection_SelVarFactor > 999999999.0 ) {
		AfxMessageBox("Number is out of range.");
		m_NewSection_SelVarFactor = 0.0;
		UpdateData(FALSE);
		m_NewSectionSelVarFactor_Box.SetFocus();
	}
	m_NewSectionSelVarFactor_Box.GetWindowText(buf,31);
	if ( strchr(buf,'+') != NULL ) {
		ptr = strchr(buf,'+');
		ptr++;
		strcpy(buf2,ptr);
		m_NewSectionSelVarFactor_Box.SetWindowText(buf2);
	}
	UpdateData(FALSE);	
}

void CNewSectionPage2::OnKillfocusNewsectionStockerfixed() 
{
	// TODO: Add your control notification handler code here
	char buf[32]="";
	char buf2[32]="";
	char * ptr;

	if (this->IsWindowVisible() == 0)
		return;

	if (UpdateData(TRUE) == 0 ) {
		m_NewSectionStockerFixed_Box.SetFocus();
		return;
	}
	if (m_NewSection_StockerFixed < 0 ) {
		AfxMessageBox("Number must be non-negative.");
		m_NewSectionStockerFixed_Box.SetFocus();
	}
	if (m_NewSection_StockerFixed > 999999999.0 ) {
		AfxMessageBox("Number is out of range.");
		m_NewSection_StockerFixed = 0.0;
		UpdateData(FALSE);
		m_NewSectionStockerFixed_Box.SetFocus();
	}
	m_NewSectionStockerFixed_Box.GetWindowText(buf,31);
	if ( strchr(buf,'+') != NULL ) {
		ptr = strchr(buf,'+');
		ptr++;
		strcpy(buf2,ptr);
		m_NewSectionStockerFixed_Box.SetWindowText(buf2);
	}
	UpdateData(FALSE);		
}

void CNewSectionPage2::OnKillfocusNewsectionStockerlaborrate() 
{
	// TODO: Add your control notification handler code here
	char buf[32]="";
	char buf2[32]="";
	char * ptr;

	if (this->IsWindowVisible() == 0)
		return;

	if (UpdateData(TRUE) == 0 ) {
		m_NewSectionStockerLaborRate_Box.SetFocus();
		return;
	}
	if (m_NewSection_StockerLaborRate < 0 ) {
		AfxMessageBox("Number must be non-negative.");
		m_NewSectionStockerLaborRate_Box.SetFocus();
	}
	if (m_NewSection_StockerLaborRate > 999999999.0 ) {
		AfxMessageBox("Number is out of range.");
		m_NewSection_StockerLaborRate = 0.0;
		UpdateData(FALSE);
		m_NewSectionStockerLaborRate_Box.SetFocus();
	}
	m_NewSectionStockerLaborRate_Box.GetWindowText(buf,31);
	if ( strchr(buf,'+') != NULL ) {
		ptr = strchr(buf,'+');
		ptr++;
		strcpy(buf2,ptr);
		m_NewSectionStockerLaborRate_Box.SetWindowText(buf2);
	}
	UpdateData(FALSE);
			
}

void CNewSectionPage2::OnKillfocusNewsectionStockervar() 
{
	// TODO: Add your control notification handler code here
	char buf[32]="";
	char buf2[32]="";
	char * ptr;

	if (this->IsWindowVisible() == 0)
		return;

	if (UpdateData(TRUE) == 0 ) {
		m_NewSectionStockerVar_Box.SetFocus();
		return;
	}
	if (m_NewSection_StockerVar < 0 ) {
		AfxMessageBox("Number must be non-negative.");
		m_NewSectionStockerVar_Box.SetFocus();
	}
	if (m_NewSection_StockerVar > 999999999.0 ) {
		AfxMessageBox("Number is out of range.");
		m_NewSection_StockerVar = 0.0;
		UpdateData(FALSE);
		m_NewSectionStockerVar_Box.SetFocus();
	}
	m_NewSectionStockerVar_Box.GetWindowText(buf,31);
	if ( strchr(buf,'+') != NULL ) {
		ptr = strchr(buf,'+');
		ptr++;
		strcpy(buf2,ptr);
		m_NewSectionStockerVar_Box.SetWindowText(buf2);
	}
	UpdateData(FALSE);		
}

BOOL CNewSectionPage2::OnHelpInfo(HELPINFO* pHelpInfo) 
{
	helpService.ShowFieldHelp(IDD, pHelpInfo->iCtrlId);
	return FALSE;
}

BOOL CNewSectionPage2::OnInitDialog() 
{
	CPropertyPage::OnInitDialog();
	
	// TODO: Add extra initialization here
	UpdateData(FALSE);
	m_NewSectionDistVar_Box.SetFocus();		
	return FALSE;  // return TRUE unless you set the focus to a control
	              // EXCEPTION: OCX Property Pages should return FALSE
}


BOOL CNewSectionPage2::OnNotify(WPARAM wParam, LPARAM lParam, LRESULT* pResult) 
{
	NMHDR* pNMHDR = (NMHDR*)lParam;

	// if they press the help button
	if (pNMHDR->code == PSN_HELP) {
		helpService.ShowScreenHelp(IDD);
		return TRUE;
	}	
	return CPropertyPage::OnNotify(wParam, lParam, pResult);
}

void CNewSectionPage2::OnKillfocusNewsectionAvgcubepertrip() 
{
	// TODO: Add your control notification handler code here
	char buf[32]="";
	char buf2[32]="";
	char * ptr;

	if (this->IsWindowVisible() == 0)
		return;

	if (UpdateData(TRUE) == 0 ) {
		m_NewSectionAvgCubePerTrip_Box.SetFocus();
		return;
	}
	if (m_NewSection_AvgCubePerTrip < 0 ) {
		AfxMessageBox("Number must be non-negative.");
		m_NewSectionAvgCubePerTrip_Box.SetFocus();
	}
	if (m_NewSection_AvgCubePerTrip > 999999999.0 ) {
		AfxMessageBox("Number is out of range.");
		m_NewSection_AvgCubePerTrip = 0.0;
		UpdateData(FALSE);
		m_NewSectionAvgCubePerTrip_Box.SetFocus();
	}
	m_NewSectionAvgCubePerTrip_Box.GetWindowText(buf,31);
	if ( strchr(buf,'+') != NULL ) {
		ptr = strchr(buf,'+');
		ptr++;
		strcpy(buf2,ptr);
		m_NewSectionAvgCubePerTrip_Box.SetWindowText(buf2);
	}
	UpdateData(FALSE);			
}

// IntegrationOptionsPage.cpp : implementation file
//

#include "stdafx.h"
#include "modal.h"
#include "IntegrationOptionsPage.h"
#include "HelpService.h"

#ifdef _DEBUG
#define new DEBUG_NEW
#undef THIS_FILE
static char THIS_FILE[] = __FILE__;
#endif

extern CHelpService helpService;
/////////////////////////////////////////////////////////////////////////////
// CIntegrationOptionsPage property page

IMPLEMENT_DYNCREATE(CIntegrationOptionsPage, CPropertyPage)

CIntegrationOptionsPage::CIntegrationOptionsPage() : CPropertyPage(CIntegrationOptionsPage::IDD)
{
	//{{AFX_DATA_INIT(CIntegrationOptionsPage)
	m_DetailXMLLogging = FALSE;
	m_SkipXMLLogging = FALSE;
	m_SaveOptions = FALSE;
	//}}AFX_DATA_INIT
}

CIntegrationOptionsPage::~CIntegrationOptionsPage()
{
}

void CIntegrationOptionsPage::DoDataExchange(CDataExchange* pDX)
{
	CPropertyPage::DoDataExchange(pDX);
	//{{AFX_DATA_MAP(CIntegrationOptionsPage)
	DDX_Check(pDX, IDC_DETAIL_XML_LOGGING, m_DetailXMLLogging);
	DDX_Check(pDX, IDC_SKIP_XML_LOGGING, m_SkipXMLLogging);
	DDX_Check(pDX, IDC_SAVE_OPTIONS, m_SaveOptions);
	//}}AFX_DATA_MAP
}


BEGIN_MESSAGE_MAP(CIntegrationOptionsPage, CPropertyPage)
	//{{AFX_MSG_MAP(CIntegrationOptionsPage)
	ON_BN_CLICKED(IDC_DETAIL_XML_LOGGING, OnDetailXmlLogging)
	ON_BN_CLICKED(IDC_SKIP_XML_LOGGING, OnSkipXmlLogging)
	ON_WM_HELPINFO()
	//}}AFX_MSG_MAP
END_MESSAGE_MAP()

/////////////////////////////////////////////////////////////////////////////
// CIntegrationOptionsPage message handlers

void CIntegrationOptionsPage::OnDetailXmlLogging() 
{
	// TODO: Add your control notification handler code here
	
}

void CIntegrationOptionsPage::OnSkipXmlLogging() 
{
	UpdateData(TRUE);

	if (m_SkipXMLLogging)
		GetDlgItem(IDC_DETAIL_XML_LOGGING)->EnableWindow(FALSE);
	else
		GetDlgItem(IDC_DETAIL_XML_LOGGING)->EnableWindow(TRUE);
}

BOOL CIntegrationOptionsPage::OnInitDialog() 
{
	CPropertyPage::OnInitDialog();
	
	OnSkipXmlLogging();
	OnDetailXmlLogging();

	return TRUE;  // return TRUE unless you set the focus to a control
	              // EXCEPTION: OCX Property Pages should return FALSE
}

BOOL CIntegrationOptionsPage::OnHelpInfo(HELPINFO* pHelpInfo) 
{
	helpService.ShowFieldHelp(IDD, pHelpInfo->iCtrlId);
	return FALSE;

}

BOOL CIntegrationOptionsPage::OnNotify(WPARAM wParam, LPARAM lParam, LRESULT* pResult) 
{
	NMHDR* pNMHDR = (NMHDR*)lParam;

	if (pNMHDR->code == PSN_HELP) {
		helpService.ShowScreenHelp(IDD);
		return TRUE;
	}
	
	return CPropertyPage::OnNotify(wParam, lParam, pResult);
}

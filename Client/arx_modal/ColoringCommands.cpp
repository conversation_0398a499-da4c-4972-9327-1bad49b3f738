// ColoringCommands.cpp: implementation of the CColoringCommands class.
//
//////////////////////////////////////////////////////////////////////
// This class is used to translate the AutoCAD command invocation into 
// a function call that could also be called from a different front-end.

#include "stdafx.h"
#include "modal.h"
#include "ColoringCommands.h"
#include "ColoringHelper.h"
#include "ControlService.h"

#ifdef _DEBUG
#undef THIS_FILE
static char THIS_FILE[]=__FILE__;
#define new DEBUG_NEW
#endif

extern CControlService controlService;
//////////////////////////////////////////////////////////////////////
// Construction/Destruction
//////////////////////////////////////////////////////////////////////

CColoringCommands::CColoringCommands()
{

}

CColoringCommands::~CColoringCommands()
{

}

void CColoringCommands::RegisterCommands()
{

	// Coloring
	acedRegCmds->addCommand( "SLOTGEN", "COLORBYHANDLE", "COLORBYHANDLE",
		ACRX_CMD_MODAL, CColoringCommands::ColorByHandle );
	acedRegCmds->addCommand( "SLOTGEN", "COLORAISLE", "COLORAISLE",
		ACRX_CMD_MODAL, &CColoringCommands::ColorAisle );
	acedRegCmds->addCommand( "SLOTGEN", "COLORBYPROFILE", "COLORBYPROFILE",
		ACRX_CMD_MODAL, &CColoringCommands::ColorByProfile );
	acedRegCmds->addCommand( "SLOTFAC", "COLORMODEL", "COLORMODEL",
		ACRX_CMD_MODAL, &CColoringCommands::ColorModelDlg );
	acedRegCmds->addCommand( "SLOTFAC", "COLORSLOTGROUP", "COLORSLOTGROUP",
		ACRX_CMD_MODAL, &CColoringCommands::ColorProductGroups );
	acedRegCmds->addCommand( "SLOTFAC", "CPG", "CPG",
		ACRX_CMD_MODAL, &CColoringCommands::ColorProductGroups );
	acedRegCmds->addCommand( "SLOTFAC", "COLORBYPRODUCT", "COLORBYPRODUCT",
		ACRX_CMD_MODAL, &CColoringCommands::ColorProduct );
	acedRegCmds->addCommand( "SLOTFAC", "COLORPRODUCT", "COLORPRODUCT",
		ACRX_CMD_MODAL, &CColoringCommands::ColorProduct );
}


void CColoringCommands::ColorByHandle()
{
	if (! controlService.ValidateCommand(CCommands::newFacilityMode))
		return;

	CColoringHelper coloring;

	coloring.ColorByHandle();

	return;
}


void CColoringCommands::ColorAisle()
{
	if (! controlService.ValidateCommand(CCommands::openFacilityMode))
		return;

	CColoringHelper coloring;

	coloring.ColorAisle();

	return;


}

void CColoringCommands::ColorByProfile()
{
	if (! controlService.ValidateCommand(CCommands::openFacilityMode))
		return;

	CColoringHelper coloring;

	coloring.ColorByProfile();

	return;

}


void CColoringCommands::ColorModelDlg()
{
	if (! controlService.ValidateCommand(CCommands::openFacilityMode))
		return;

	CColoringHelper coloring;

	coloring.ColorModel();

	return;

}


void CColoringCommands::ColorProductGroups()
{
	if (! controlService.ValidateCommand(CCommands::openFacilityMode))
		return;

	CColoringHelper coloring;

	coloring.ColorProductGroup();

	return;

}

void CColoringCommands::ColorProduct()
{
	if (! controlService.ValidateCommand(CCommands::openFacilityMode))
		return;

	CColoringHelper coloring;

	coloring.ColorProduct();

	return;
}

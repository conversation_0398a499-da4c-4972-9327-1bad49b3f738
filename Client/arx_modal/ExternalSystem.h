// ExternalSystem.h: interface for the CExternalSystem class.
//
//////////////////////////////////////////////////////////////////////

#if !defined(AFX_EXTERNALSYSTEM_H__C6D8AB7B_80BA_4422_A194_388804A0C7E0__INCLUDED_)
#define AFX_EXTERNALSYSTEM_H__C6D8AB7B_80BA_4422_A194_388804A0C7E0__INCLUDED_

#if _MSC_VER > 1000
#pragma once
#endif // _MSC_VER > 1000

class CExternalSystem : public CObject  
{
public:
	CExternalSystem();
	CExternalSystem(const CExternalSystem& other);
	CExternalSystem& operator=(const CExternalSystem& other);
	BOOL operator==(const CExternalSystem& other);
	BOOL operator!=(const CExternalSystem& other);
	int Parse(const CString &line);
	virtual ~CExternalSystem();

	int m_ExternalSystemDBId;
	CString m_Name;
	CString m_Vendor;
	CString m_Version;
	int m_SystemTypeDBId;
	CString m_TypeName;
};

#endif // !defined(AFX_EXTERNALSYSTEM_H__C6D8AB7B_80BA_4422_A194_388804A0C7E0__INCLUDED_)

#if !defined(AFX_BAYPROFILEPROPERTYPAGE_H__5E3E0997_7C2E_4CE1_9426_3656C9C678DE__INCLUDED_)
#define AFX_BAYPROFILEPROPERTYPAGE_H__5E3E0997_7C2E_4CE1_9426_3656C9C678DE__INCLUDED_

#if _MSC_VER > 1000
#pragma once
#endif // _MSC_VER > 1000
// BayProfilePropertyPage.h : header file
//

/////////////////////////////////////////////////////////////////////////////
// CBayProfilePropertyPage dialog

class CBayProfilePropertyPage : public CPropertyPage
{
	DECLARE_DYNCREATE(CBayProfilePropertyPage)

// Construction
public:
	CBayProfilePropertyPage();
	~CBayProfilePropertyPage();

// Dialog Data
	//{{AFX_DATA(CBayProfilePropertyPage)
	// NOTE - ClassWizard will add data members here.
		//    DO NOT EDIT what you see in these blocks of generated code !
	//}}AFX_DATA


// Overrides
	// ClassWizard generate virtual function overrides
	//{{AFX_VIRTUAL(CBayProfilePropertyPage)
	protected:
	virtual void DoDataExchange(CDataExchange* pDX);    // DDX/DDV support
	virtual BOOL OnNotify(WPARAM wParam, LPARAM lParam, LRESULT* pResult);
	//}}AFX_VIRTUAL

// Implementation
protected:
	// Generated message map functions
	//{{AFX_MSG(CBayProfilePropertyPage)
	afx_msg BOOL OnHelpInfo(HELPINFO* pHelpInfo);
	//}}AFX_MSG
	DECLARE_MESSAGE_MAP()
	BOOL Validate();
	CBayProfile *m_pBayProfile;
};

//{{AFX_INSERT_LOCATION}}
// Microsoft Visual C++ will insert additional declarations immediately before the previous line.

#endif // !defined(AFX_BAYPROFILEPROPERTYPAGE_H__5E3E0997_7C2E_4CE1_9426_3656C9C678DE__INCLUDED_)

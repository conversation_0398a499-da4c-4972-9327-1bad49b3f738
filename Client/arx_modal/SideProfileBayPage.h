#if !defined(AFX_SideProfileBayPage_H__7363BFA6_0EA6_4A51_BAA1_5A7DD0160C27__INCLUDED_)
#define AFX_SideProfileBayPage_H__7363BFA6_0EA6_4A51_BAA1_5A7DD0160C27__INCLUDED_

#include "SideProfile.h"	// Added by ClassView
#include "SideProfileButton.h"
#if _MSC_VER > 1000
#pragma once
#endif // _MSC_VER > 1000
// SideProfileBayPage.h : header file
//

/////////////////////////////////////////////////////////////////////////////
// CSideProfileBayPage dialog

class CSideProfileBayPage : public CPropertyPage
{
	DECLARE_DYNCREATE(CSideProfileBayPage)

// Construction
public:
	void UpdateToolTip(int x, int y, int currentBayIdx);
	CSideProfile *m_pSideProfile;
	double m_CurrentLength;
	CMap<int, int, CBayProfile*, CBayProfile*> m_SelectedMap;
	CSideProfileBayPage();
	~CSideProfileBayPage();

// Dialog Data
	//{{AFX_DATA(CSideProfileBayPage)
	enum { IDD = IDD_SIDE_PROFILE_BAY_PAGE };
	CTreeCtrl	m_ProfileTreeCtrl;
	CSideProfileButton	m_SideButton;
	CString	m_Quantity;
	BOOL	m_HideExcluded;
	//}}AFX_DATA


// Overrides
	// ClassWizard generate virtual function overrides
	//{{AFX_VIRTUAL(CSideProfileBayPage)
	public:
	virtual BOOL OnSetActive();
	virtual BOOL OnKillActive();
	virtual BOOL PreTranslateMessage(MSG* pMsg);
	protected:
	virtual void DoDataExchange(CDataExchange* pDX);    // DDX/DDV support
	virtual BOOL OnNotify(WPARAM wParam, LPARAM lParam, LRESULT* pResult);
	//}}AFX_VIRTUAL

// Implementation
protected:
	// Generated message map functions
	//{{AFX_MSG(CSideProfileBayPage)
	virtual BOOL OnInitDialog();
	afx_msg void OnInsert();
	afx_msg void OnFill();
	afx_msg void OnDelete();
	afx_msg void OnHideExcluded();
	afx_msg void OnDblclkBayList(NMHDR* pNMHDR, LRESULT* pResult);
	afx_msg void OnContextMenu(CWnd* pWnd, CPoint point);
	afx_msg void OnProfileProperties();
	afx_msg BOOL OnHelpInfo(HELPINFO* pHelpInfo);
	//}}AFX_MSG
	DECLARE_MESSAGE_MAP()
	afx_msg void OnBayProperties(WPARAM wParam, LPARAM lParam);

	CToolTipCtrl m_ToolTip;
	CString m_ToolTipText;
private:

	void BuildBayProfileTree();
	CImageList m_ImageList;
	int LoadBayTypeList();
	int LoadBayProfileList();

	CMap<int, int, HTREEITEM, HTREEITEM&> m_MapBayTypeToTree;
	CTypedPtrArray<CObArray, CBayProfile*> m_BayProfileList;	
};

//{{AFX_INSERT_LOCATION}}
// Microsoft Visual C++ will insert additional declarations immediately before the previous line.

#endif // !defined(AFX_SideProfileBayPage_H__7363BFA6_0EA6_4A51_BAA1_5A7DD0160C27__INCLUDED_)

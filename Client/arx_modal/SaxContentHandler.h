#ifndef SaxContentHandler_h_defined
#define SaxContentHandler_h_defined

#if _MSC_VER > 1000
#pragma once
#endif // _MSC_VER > 1000

#include "SAXContentHandlerImpl.h"
#include "SAXErrorHandlerImpl.h"
#include "Location.h"
#include "ProductPack.h"
#include "Assignment.h"
#include "ExternalConnection.h"
#include "InboundQueueRecord.h"
#include "Confirmation.h"

class CSaxContentHandler : public SAXContentHandlerImpl, public SAXErrorHandlerImpl
{
public:
    CSaxContentHandler();
    virtual ~CSaxContentHandler();
	
	virtual HRESULT STDMETHODCALLTYPE startElement( 
		/* [in] */ wchar_t __RPC_FAR *pwchNamespaceUri,
		/* [in] */ int cchNamespaceUri,
		/* [in] */ wchar_t __RPC_FAR *pwchLocalName,
		/* [in] */ int cchLocalName,
		/* [in] */ wchar_t __RPC_FAR *pwchQName,
		/* [in] */ int cchQName,
		/* [in] */ ISAXAttributes __RPC_FAR *pAttributes);
        
	virtual HRESULT STDMETHODCALLTYPE endElement( 
		/* [in] */ wchar_t __RPC_FAR *pwchNamespaceUri,
		/* [in] */ int cchNamespaceUri,
		/* [in] */ wchar_t __RPC_FAR *pwchLocalName,
		/* [in] */ int cchLocalName,
		/* [in] */ wchar_t __RPC_FAR *pwchQName,
		/* [in] */ int cchQName);
		
	virtual HRESULT STDMETHODCALLTYPE startDocument();
	virtual HRESULT STDMETHODCALLTYPE endDocument();

    
    virtual HRESULT STDMETHODCALLTYPE characters( 
		/* [in] */ wchar_t __RPC_FAR *pwchChars,
		/* [in] */ int cchChars);


	HRESULT STDMETHODCALLTYPE error(
		/* [in] */ ISAXLocator * pLocator, 
		/* [in] */ wchar_t * pwchErrorMessage,
		/* [in] */ HRESULT hrErrorCode);
		
	virtual HRESULT STDMETHODCALLTYPE fatalError(
		/* [in] */ ISAXLocator * pLocator, 
		/* [in] */ wchar_t * pwchErrorMessage,
		/* [in] */ HRESULT hrErrorCode);
		
	virtual HRESULT STDMETHODCALLTYPE ignorableWarning(
		/* [in] */ ISAXLocator * pLocator, 
		/* [in] */ wchar_t * pwchErrorMessage,
		/* [in] */ HRESULT hrErrorCode);

	static CSaxContentHandler *CreateInstance();

	int m_InterfaceType;
	
	
	CTypedPtrArray<CObArray, CInboundQueueRecord*> *m_pQueue;
	
	int m_Error;
	CString m_ErrorText;
	
	CString m_LogFileName;
	BOOL m_LogDetail;

private:
	CString m_CurrentCharacters;
	void Log(const CString &text);
	void LogDetail(const CString &text);

	HANDLE m_hLogFile;


	CLocation *m_pCurrentLocation;
	CProductPack *m_pCurrentProduct;
	CAssignment *m_pCurrentAssignment;
	CConfirmation *m_pCurrentConfirmation;

	int m_CurrentFeedId;
	int m_CurrentBatchId;
	int m_CurrentLineNumber;
	int m_CurrentAction;

	CString m_CurrentWMSId;
	CString m_CurrentWMSDetailId;

	CString m_CurrentTag;

	void ProcessLocationElement(const CString &value);
	void ProcessProductElement(const CString &value);
	void ProcessAssignmentElement(const CString &value);
	void ProcessLocationConfirmationElement(const CString &value);
	void ProcessAssignmentConfirmationElement(const CString &value);

};

#endif
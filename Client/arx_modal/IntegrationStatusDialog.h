// IntegrationStatusDialog.h : header file
//
#if !defined(AFX_INTEGRATIONSTATUSDIALOG_H__F412BD17_04E4_447B_84CC_586EA39292F6__INCLUDED_)
#define AFX_INTEGRATIONSTATUSDIALOG_H__F412BD17_04E4_447B_84CC_586EA39292F6__INCLUDED_

#include "WMSGroup.h"
#include "ExternalConnection.h"
#include "SaxContentHandler.h"
#include "InboundQueueRecord.h"
#include "MessageQueueHelper.h"
#include <afxmt.h>

#if _MSC_VER > 1000
#pragma once
#endif // _MSC_VER > 1000


/////////////////////////////////////////////////////////////////////////////
// CIntegrationStatusDialog dialog

class CIntegrationStatusDialog : public CDialog
{
// Construction
public:
	static UINT ParseMessageThread(LPVOID pParam);

	BOOL m_InProgress;
	int m_ErrorCount;
	ISAXXMLReader *m_pXMLReader;
	CString m_ParseFileName;

	CIntegrationStatusDialog(CWnd* pParent = NULL);   // standard constructor
	~CIntegrationStatusDialog();

	CEvent m_Event;
	CEvent m_KillEvent;
	int m_ThreadReturnCode;

	CTypedPtrArray<CObArray, CInboundQueueRecord*> m_InboundQueue;

// Dialog Data
	//{{AFX_DATA(CIntegrationStatusDialog)
	enum { IDD = IDD_INTEGRATION_STATUS };
	CAnimateCtrl	m_AnimateCtrl;
	CComboBox	m_GroupListCtrl;
	CProgressCtrl	m_OverallProgressCtrl;
	CProgressCtrl	m_ProgressCtrl;
	CString	m_Message;
	CString	m_InterfaceMessage;
	CString	m_Details;
	//}}AFX_DATA


// Overrides
	// ClassWizard generated virtual function overrides
	//{{AFX_VIRTUAL(CIntegrationStatusDialog)
	protected:
	virtual void DoDataExchange(CDataExchange* pDX);    // DDX/DDV support
	//}}AFX_VIRTUAL

// Implementation
protected:
	void OnViewErrors();

	// Generated message map functions
	//{{AFX_MSG(CIntegrationStatusDialog)
	virtual BOOL OnInitDialog();
	afx_msg void OnStart();
	afx_msg void OnOptions();
	afx_msg void OnDetails();
	virtual void OnCancel();
	afx_msg void OnSelchangeGroupList();
	afx_msg void OnWmsProperties();
	afx_msg BOOL OnHelpInfo(HELPINFO* pHelpInfo);
	afx_msg void OnHelp();
	//}}AFX_MSG
	DECLARE_MESSAGE_MAP()
private:

	void Start();
	void DeleteInboundMessage(CExternalConnection &connection);
	int DeleteRemoteFile(CExternalConnection &connection);
	
	CMessageQueueHelper *m_pMQHelper;

	CWMSGroup *m_pGroup;
	void LoadOptions();
	int m_NoDetailHeight;
	int LoadGroup();
	int LoadWMSGroupList();
	CString BackupMessage(char *msg, int size, int interfaceType, BOOL error);
	CString BackupMessage(const CString &originalFile, int interfaceType);
	int FormatLocationOutbound(CWMS *pWMS, const CString &location, CString &xml,
		CMapStringToString &defaultInfoMap, CMapStringToString &levelProfileInfoMap,
		CMapStringToString &locationInfoMap);
	int LoadWMSList();

	int LoadConnectionList();
	int LoadInboundMapList();
	int LoadOutboundMapList();

	int ProcessLocationOutbound();
	int ProcessAssignmentOutbound();
	int GetMoves(CWMS *pWMS, CWMSMap *pMap, CString &chainList, CStringArray &assgList);
	int FormatAssignmentOutbound(const CWMS *pWMS, const CWMSMap *pMap, const CString &assignment, int sequence, CString &xml);
	CString LookupSearchAnchor(const CString &location);
	CString GetWMSForSection(int facilityId, int sectionId);

	CTypedPtrArray<CObArray, CWMSGroup*> m_GroupList;

	int MessageType(char *msg, int size);

	int ProcessInbound(int interfaceType, int options, const CString& interfaceName);
	int ProcessInboundMessage(int options, int& errorCount);
	int ParseMessage(int interfaceType, const CString &fileName, int size, const CString& interfaceName);

	int UpdateProductGroupAssignments(int facilityDBId);

	int BeginQueueTransaction(CExternalConnection &connection);
	int ReadQueueMessage(CExternalConnection &connection, char **msg, int &size);
	int AbortQueueTransaction(CExternalConnection &connection);
	int CommitQueueTransaction(CExternalConnection &connection);

	int ReadRemoteMessage(CExternalConnection &connection, char **msg, int &size);
	int ReadLocalMessage(CExternalConnection &connection, char **msg, int &size);
	int ReadUserMessage(int interfaceType, char **msg, int &size);

	int WriteQueueMessage(CExternalConnection &connection, const CString &msg);
	int WriteRemoteMessage(CExternalConnection &connection, const CString& msg);
	int WriteLocalMessage(CExternalConnection &connection, const CString& msg);
	int WriteUserMessage(int interfaceType, const CString& msg);

	int GetFile(const CString &title, CString &fileName, BOOL inbound);

	int ProcessLocationInbound(CStringArray &locList, int &errorCount, int totalCount, int addCount);
	int ProcessProductInbound(CStringArray &prodList, int &errorCount, int eventId, int addCount);
	
	int ProcessAssignmentInbound(CStringArray &assgList, int &errorCount, int eventId, int addCount);
	
		CString LastError();
	CSaxContentHandler *m_pXMLHandler;

	typedef enum {
		SkipInterface = 1,
		FullExport = 2,
		InboundPrompt = 4,
		OutboundPrompt = 8,
		SkipStatusUpdate = 16,
		AutoConfirm = 32,
		SkipXMLLogging = 64,
		DetailXMLLogging = 128,
		SkipProductGroupUpdate = 256,
		SaveOptions = 512,
		AllowNotIntegrated = 1024
	} enumInterfaceOptions;

	int m_GeneralOptions;
	int m_LocationOptions;
	int m_AssignmentOptions;
	int m_ProductOptions;

	int LoadSearchAnchorList(int facilityDBId);

	typedef struct {
		CString low;
		CString high;
		CString searchAnchorPoint;
	} searchAnchorStruct;

	CArray<searchAnchorStruct, searchAnchorStruct&> m_SearchAnchorList;
	
};

//{{AFX_INSERT_LOCATION}}
// Microsoft Visual C++ will insert additional declarations immediately before the previous line.

#endif // !defined(AFX_INTEGRATIONSTATUSDIALOG_H__F412BD17_04E4_447B_84CC_586EA39292F6__INCLUDED_)

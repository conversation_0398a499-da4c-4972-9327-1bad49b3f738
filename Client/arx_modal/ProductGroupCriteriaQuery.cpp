// ProductGroupCriteriaQuery.cpp: implementation of the CProductGroupCriteriaQuery class.
//
//////////////////////////////////////////////////////////////////////

#include "stdafx.h"
#include "modal.h"
#include "ProductGroupCriteriaQuery.h"

#include <dbsymtb.h>

#ifdef _DEBUG
#undef THIS_FILE
static char THIS_FILE[]=__FILE__;
#define new DEBUG_NEW
#endif

//////////////////////////////////////////////////////////////////////
// Construction/Destruction
//////////////////////////////////////////////////////////////////////

CProductGroupCriteriaQuery::CProductGroupCriteriaQuery()
{
	m_CriteriaQueryDBID = -1;
	m_Attribute = "";
	m_AttributeType = -1;
	m_Operator = "";
	m_Value = "";
	m_Sequence = 1;
	m_Precedence = 0;
	m_Conjunction = "and";
	m_CriteriaRangeDBID = -1;
}

CProductGroupCriteriaQuery::~CProductGroupCriteriaQuery()
{

}

CProductGroupCriteriaQuery& CProductGroupCriteriaQuery::operator=(const CProductGroupCriteriaQuery &other)
{

	m_CriteriaQueryDBID = other.m_CriteriaQueryDBID;
	m_CriteriaRangeDBID = other.m_CriteriaRangeDBID;
	m_Attribute = other.m_Attribute;
	m_AttributeType = other.m_AttributeType;
	m_Operator = other.m_Operator;
	m_Value = other.m_Value;
	m_Sequence = other.m_Sequence;
	m_Precedence = other.m_Precedence;
	m_Conjunction = other.m_Conjunction;

	return *this;
}

BOOL CProductGroupCriteriaQuery::IsEqual(const CProductGroupCriteriaQuery &other)
{
	if (m_CriteriaQueryDBID != other.m_CriteriaQueryDBID) return FALSE;
	if (m_CriteriaRangeDBID != other.m_CriteriaRangeDBID) return FALSE;
	if (m_Attribute != other.m_Attribute) return FALSE;
	if (m_AttributeType != other.m_AttributeType) return FALSE;
	if (m_Operator != other.m_Operator) return FALSE;
	if (m_Value != other.m_Value) return FALSE;
	if (m_Sequence != other.m_Sequence) return FALSE;
	if (m_Precedence != other.m_Precedence) return FALSE;
	if (m_Conjunction != other.m_Conjunction) return FALSE;

	return TRUE;

}

int CProductGroupCriteriaQuery::Parse(CString &line)
{
	char *str;
	char *ptr;
	CString tmp;
	int idx;

	tmp = line;

	try {
		// strtok doesn't interpret double pipes as a field
		idx = tmp.Find("||");
		while (idx >= 0) {
			tmp.Insert(idx+1, " ");
			idx = tmp.Find("||");
		}

		str = tmp.GetBuffer(0);
		
		ptr = strtok(str, "|");
		m_CriteriaQueryDBID = atol(ptr);
		ptr = strtok(NULL, "|");
		m_Attribute = ptr;
		ptr = strtok(NULL, "|");
		m_AttributeType = atoi(ptr);
		ptr = strtok(NULL, "|");
		m_Operator = ptr;
		ptr = strtok(NULL, "|");
		m_Value = ptr;
		ptr = strtok(NULL, "|");
		m_Sequence = atoi(ptr);
		ptr = strtok(NULL, "|");
		m_Precedence = atoi(ptr);
		ptr = strtok(NULL, "|");
		m_Conjunction = ptr;
		ptr = strtok(NULL, "|");
		m_CriteriaRangeDBID = atol(ptr);

		tmp.ReleaseBuffer();
	}
	catch (...) {
		tmp.ReleaseBuffer();
		AfxMessageBox("Error processing criteria query list.\n");
		ads_printf("%s\n", line);
		return -1;
	}

	return 0;
}

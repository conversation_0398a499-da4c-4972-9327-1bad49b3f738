#if !defined(AFX_PRODUCTGROUPDIALOG_H__5E791C63_041F_11D5_9EC8_00C04FAC149C__INCLUDED_)
#define AFX_PRODUCTGROUPDIALOG_H__5E791C63_041F_11D5_9EC8_00C04FAC149C__INCLUDED_

#if _MSC_VER > 1000
#pragma once
#endif // _MSC_VER > 1000
// ProductGroupDialog.h : header file
//
#include "ProductGroupDataService.h"
#include "ProductGroupAssignmentDialog.h"

/////////////////////////////////////////////////////////////////////////////
// CProductGroupDialog dialog

class CProductGroupDialog : public CDialog
{
// Construction
	DECLARE_DYNCREATE(CProductGroupDialog)
public:
	BOOL ValidateClose();
	~CProductGroupDialog();
	CProductGroupDialog(CWnd* pParent = NULL);   // standard constructor
	ProductGroupArrayType *m_ProductGroupList;
	CTypedPtrArray<CObArray, CProductAttribute*> *m_ProductAttributeList;

	CProductGroupAssignmentDialog m_ProductGroupAssignmentDialog;
	CProductGroupDataService *m_ProductGroupDataService;
// Dialog Data
	//{{AFX_DATA(CProductGroupDialog)
	enum { IDD = IDD_PRODUCT_GROUP_MAINTENANCE };
	CListBox	m_ProductGroupListCtrl;
	//}}AFX_DATA


// Overrides
	// ClassWizard generated virtual function overrides
	//{{AFX_VIRTUAL(CProductGroupDialog)
	public:
	virtual BOOL Create(LPCTSTR lpszClassName, LPCTSTR lpszWindowName, DWORD dwStyle, const RECT& rect, CWnd* pParentWnd, UINT nID, CCreateContext* pContext = NULL);
	protected:
	virtual void DoDataExchange(CDataExchange* pDX);    // DDX/DDV support
	virtual void PostNcDestroy();
	//}}AFX_VIRTUAL

// Implementation
protected:

	// Generated message map functions
	//{{AFX_MSG(CProductGroupDialog)
	afx_msg void OnApply();
	afx_msg void OnAdd();
	afx_msg void OnDelete();
	afx_msg void OnMoveDown();
	afx_msg void OnMoveUp();
	afx_msg void OnProperties();
	virtual void OnCancel();
	afx_msg void OnHelp();
	virtual BOOL OnInitDialog();
	afx_msg void OnContextMenu(CWnd* pWnd, CPoint point);
	afx_msg void OnAssignProducts();
	afx_msg BOOL OnHelpInfo(HELPINFO* pHelpInfo);
	//}}AFX_MSG
	DECLARE_MESSAGE_MAP()
private:
	int UpdatePriorities();
};

//{{AFX_INSERT_LOCATION}}
// Microsoft Visual C++ will insert additional declarations immediately before the previous line.

#endif // !defined(AFX_PRODUCTGROUPDIALOG_H__5E791C63_041F_11D5_9EC8_00C04FAC149C__INCLUDED_)

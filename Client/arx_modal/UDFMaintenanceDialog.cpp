// UDFMaintenanceDialog.cpp : implementation file
//

#include "stdafx.h"
#include <afxmt.h>

#include "ssa_exception.h"

#include "UDFMaintenanceDialog.h"
#include "TreeElement.h"
#include "Progress.h"
#include "Processing.h"
#include "HelpService.h"
#include "UDFDataService.h"
#include "UtilityHelper.h"
#include "BayProfileDataService.h"

#include <dbsymtb.h>

#ifdef _DEBUG
#define new DEBUG_NEW
#undef THIS_FILE
static char THIS_FILE[] = __FILE__;
#endif



/////////////////////////////////////////////////////////////////////////////
// CUDFMaintenanceDialog dialog


extern CHelpService helpService;
extern CUtilityHelper utilityHelper;
extern TreeElement changesTree;
extern CEvent g_ThreadDone;

CUDF *gUDF;


CUDFDataService udfDataService;

CUDFMaintenanceDialog::CUDFMaintenanceDialog(CWnd* pParent /*=NULL*/)
	: CDialog(CUDFMaintenanceDialog::IDD, pParent)
{
	//{{AFX_DATA_INIT(CUDFMaintenanceDialog)
	//}}AFX_DATA_INIT
	m_PreviousUDFType = -1;
	m_PreviousBayProfile = 0;
}


void CUDFMaintenanceDialog::DoDataExchange(CDataExchange* pDX)
{
	CDialog::DoDataExchange(pDX);
	//{{AFX_DATA_MAP(CUDFMaintenanceDialog)
	DDX_Control(pDX, IDC_STATIC_BAY_PROFILE, m_BayProfileStaticCtrl);
	DDX_Control(pDX, IDC_BAY_PROFILE_LIST, m_BayProfileListCtrl);
	DDX_Control(pDX, IDC_UDF_TYPE, m_UDFTypeCtrl);
	DDX_Control(pDX, IDC_UDF_LIST, m_UDFListCtrl);
	//}}AFX_DATA_MAP
}


BEGIN_MESSAGE_MAP(CUDFMaintenanceDialog, CDialog)
	//{{AFX_MSG_MAP(CUDFMaintenanceDialog)
	ON_BN_CLICKED(IDHELP, OnHelp)
	ON_BN_CLICKED(IDC_ADD_UDF, OnAddUdf)
	ON_BN_CLICKED(IDC_DELETE_UDF, OnDeleteUdf)
	ON_BN_CLICKED(IDC_MODIFY_UDF, OnModifyUdf)
	ON_CBN_SELCHANGE(IDC_UDF_TYPE, OnSelchangeUdfType)
	ON_NOTIFY(LVN_ENDLABELEDIT, IDC_UDF_LIST, OnEndlabeleditUdfList)
	ON_CBN_SELCHANGE(IDC_BAY_PROFILE_LIST, OnSelchangeBayProfileList)
	ON_WM_CONTEXTMENU()
	ON_NOTIFY(NM_DBLCLK, IDC_UDF_LIST, OnDblclkUdfList)
	ON_COMMAND(ID_GENERIC_ADD, OnAddUdf)
	ON_COMMAND(ID_GENERIC_DELETE, OnDeleteUdf)
	ON_COMMAND(ID_GENERIC_PROPERTIES, OnModifyUdf)
	ON_WM_HELPINFO()
	//}}AFX_MSG_MAP
END_MESSAGE_MAP()

/////////////////////////////////////////////////////////////////////////////
// CUDFMaintenanceDialog message handlers

BOOL CUDFMaintenanceDialog::OnInitDialog() 
{
	CDialog::OnInitDialog();
	CRect r;
	int n;

	m_UDFTypeCtrl.GetWindowRect(&r);
	m_UDFTypeCtrl.SetWindowPos(NULL, 0, 0, r.Width(), r.Height()*10, SWP_NOMOVE|SWP_NOZORDER);

	m_BayProfileListCtrl.GetWindowRect(&r);
	m_BayProfileListCtrl.SetWindowPos(NULL, 0, 0, r.Width(), r.Height() * 15, SWP_NOMOVE|SWP_NOZORDER);

	n = m_UDFTypeCtrl.AddString("Product");
	m_UDFTypeCtrl.SetItemData(n, UDF_PRODUCT);
	//n = m_UDFTypeCtrl.AddString("Product Group");
	//m_UDFTypeCtrl.SetItemData(n, UDF_PRODUCT_GROUP);
	n = m_UDFTypeCtrl.AddString("Facility");
	m_UDFTypeCtrl.SetItemData(n, UDF_FACILITY);
	n = m_UDFTypeCtrl.AddString("Section");
	m_UDFTypeCtrl.SetItemData(n, UDF_SECTION);
	n = m_UDFTypeCtrl.AddString("Aisle");
	m_UDFTypeCtrl.SetItemData(n, UDF_AISLE);
	n = m_UDFTypeCtrl.AddString("Side");
	m_UDFTypeCtrl.SetItemData(n, UDF_SIDE);
	n = m_UDFTypeCtrl.AddString("Bay");
	m_UDFTypeCtrl.SetItemData(n, UDF_BAY);


	for (int i=0; i < m_UDFListCtrl.GetHeaderCtrl()->GetItemCount(); ++i)
		m_UDFListCtrl.GetHeaderCtrl()->DeleteItem(0);

	m_UDFListCtrl.GetClientRect(&r);
	m_UDFListCtrl.InsertColumn(0, "Name", LVCFMT_LEFT, r.Width()/3, 0);
	m_UDFListCtrl.InsertColumn(1, "Type", LVCFMT_LEFT, r.Width()/3, 0);
	m_UDFListCtrl.InsertColumn(2, "Default Value", LVCFMT_LEFT, r.Width()/3, 0);


	m_BayProfileListCtrl.ShowWindow(SW_HIDE);
	m_BayProfileStaticCtrl.ShowWindow(SW_HIDE);


	return TRUE;  // return TRUE unless you set the focus to a control
	              // EXCEPTION: OCX Property Pages should return FALSE
}

void CUDFMaintenanceDialog::OnSelchangeUdfType() 
{
	int curSel, parentID;

	// Load the udf list for this type
	curSel = m_UDFTypeCtrl.GetCurSel();
	if (curSel == m_PreviousUDFType)
		return;

	m_PreviousUDFType = curSel;


	if (m_UDFTypeCtrl.GetItemData(curSel) == UDF_LEVEL_PROFILE) {
		m_BayProfileListCtrl.ShowWindow(SW_SHOW);
		m_BayProfileStaticCtrl.ShowWindow(SW_SHOW);

		if (m_BayProfileList.GetSize() == 0)
			LoadBayProfiles();
		else {
			if (m_BayProfileListCtrl.GetCurSel() < 0) {
				m_PreviousUDFType = curSel;
				return;
			}
			else
				parentID = m_BayProfileListCtrl.GetItemData(m_BayProfileListCtrl.GetCurSel());
		}
	}
	else {
		if (changesTree.elementDBID <= 0) {
			AfxMessageBox("Please save the facility before creating user-defined fields.");
			return;
		}
		m_BayProfileListCtrl.ShowWindow(SW_HIDE);
		m_BayProfileStaticCtrl.ShowWindow(SW_HIDE);
		parentID = changesTree.elementDBID;
	}

	
	LoadUDFs(m_UDFTypeCtrl.GetItemData(curSel), parentID);
	
	m_PreviousUDFType = curSel;

	return;

}

void CUDFMaintenanceDialog::OnOK() 
{
	CDialog::OnOK();
}

void CUDFMaintenanceDialog::OnCancel() 
{
	CDialog::OnCancel();
}

void CUDFMaintenanceDialog::OnHelp() 
{
	helpService.ShowScreenHelp(IDD);	
}

void CUDFMaintenanceDialog::OnAddUdf() 
{
	CUDF *pUDF;
	CUDFProperties dlg;
	int rc, curSel, baySel, idx;
	BOOL bDone = FALSE;
	CWinThread *pThread;
	BOOL bThreadDone;
	CProcessing *pProcessDlg;
	CStringArray results;

	curSel = m_UDFTypeCtrl.GetCurSel();
	
	if (curSel < 0) {
		AfxMessageBox("Please select an element type from the list.");
		m_UDFTypeCtrl.SetFocus();
		m_UDFTypeCtrl.ShowDropDown(TRUE);
		return;
	}

	if (m_UDFTypeCtrl.GetItemData(curSel) == UDF_LEVEL_PROFILE) {
		baySel = m_BayProfileListCtrl.GetCurSel();
		if (baySel < 0) {
			AfxMessageBox("Please select a bay profile from the list.");
			m_BayProfileListCtrl.SetFocus();
			m_BayProfileListCtrl.ShowDropDown(TRUE);
			return;
		}
	}

	pUDF = new CUDF;
	pUDF->m_ElementType = m_UDFTypeCtrl.GetItemData(curSel);

	dlg.m_UDF = pUDF;

	while (! bDone) {
		bDone = TRUE;

		try {
			rc = dlg.DoModal();
			if (rc != IDOK) {
				delete pUDF;
				return;
			}
		}
		catch (...) {
			AfxMessageBox("Error displaying UDF properties.");
			return;
		}

		for (int i=0; i < m_UDFList.GetSize(); ++i) {
			if (pUDF->m_Name == m_UDFList[i]->m_Name) {
				AfxMessageBox("The UDF name is already in use.");
				bDone = FALSE;
				break;
			}
		}
	}
	
	pProcessDlg = new CProcessing;
	pProcessDlg->Create(IDD_PROCESSING, this);
	pProcessDlg->m_StatusText = "Storing User Define Field...";
	pProcessDlg->UpdateData(FALSE);
	pProcessDlg->CenterWindow();
	pProcessDlg->ShowWindow(SW_SHOW);
	pProcessDlg->UpdateWindow();

	CWaitCursor cwc;

	try {		
		pUDF->m_ElementType =  m_UDFTypeCtrl.GetItemData(curSel);
		if (pUDF->m_ElementType == UDF_LEVEL_PROFILE)
			pUDF->m_ParentID = m_BayProfileListCtrl.GetItemData(baySel);
		else
			pUDF->m_ParentID = changesTree.elementDBID;

		gUDF = pUDF;		// hate to do it, but use a global so the thread has access to it

		pThread = AfxBeginThread(CUDFMaintenanceDialog::StoreUDFThread, &results);
		
		bThreadDone = false;
		while (TRUE) {
			if ( ! utilityHelper.PeekAndPump() )
				break;
			
			bThreadDone = g_ThreadDone.Lock(0);
			if (bThreadDone)
				break;
		}
		
		pProcessDlg->DestroyWindow();
		
		rc = atoi(results[0]);
		if (rc < 0) {
			this->MessageBox(results[1], "Error", MB_ICONERROR);
			return;
		}
	}
	catch (...) {
		pProcessDlg->DestroyWindow();
		utilityHelper.ProcessError("Error storing user defined field.");
		return;
	}

	// everything's copascetic so update the lists
	idx = m_UDFList.Add(pUDF);
	AddUDFToList(pUDF, idx);

	return;


}

void CUDFMaintenanceDialog::OnDeleteUdf() 
{
	int curSel, rc, idx, elementType, i;
	CUDF *pUDF;
	POSITION pos;
	CProgress *pProgress;
	CStringArray results;
	CDWordArray deleteList;
	CWinThread *pThread;
	BOOL bThreadDone;
	
	pos = m_UDFListCtrl.GetFirstSelectedItemPosition();

	if (pos == NULL) {
		AfxMessageBox("Please select one or more user defined fields to delete.");
		return;
	}

	elementType = m_UDFTypeCtrl.GetItemData(m_UDFTypeCtrl.GetCurSel());

	pProgress = new CProgress;
	pProgress->Create(IDD_PROGRESS, this);
	pProgress->CenterWindow();
	pProgress->ShowWindow(SW_SHOW);
	pProgress->UpdateWindow();
	pProgress->m_ProgressCtrl.SetRange32(0, m_UDFListCtrl.GetSelectedCount());
	pProgress->m_ProgressCtrl.SetPos(0);
	pProgress->m_ProgressCtrl.SetStep(1);
	pProgress->m_StatusTextCtrl.SetWindowText("Deleting user defined fields...");

	results.SetSize(2);

	CWaitCursor cwc;
	
	while (pos != NULL) {
		curSel = m_UDFListCtrl.GetNextSelectedItem(pos);
		idx = m_UDFListCtrl.GetItemData(curSel);
		pUDF = m_UDFList[idx];

		results[0].Format("%d", elementType);
		results[1].Format("%d", pUDF->m_ListID);

		pThread = AfxBeginThread(CUDFMaintenanceDialog::DeleteUDFThread, &results);
		
		bThreadDone = FALSE;
		while (TRUE) {
			if ( ! utilityHelper.PeekAndPump() )
				break;
			
			bThreadDone = g_ThreadDone.Lock(0);
			if (bThreadDone)
				break;
		}

		rc = atoi(results[0]);
		if (rc < 0) {
			ads_printf("%s\n", results[1]);
			AfxMessageBox("An error occurred during UDF deletion.");
			break;
		}
	
		deleteList.Add(curSel);

		pProgress->m_ProgressCtrl.StepIt();

		if (pProgress->m_Stopping)
			break;

	}
	
	for (i=deleteList.GetSize()-1; i >= 0; --i) {
		curSel = deleteList[i];
		idx = m_UDFListCtrl.GetItemData(curSel);
		pUDF = m_UDFList[idx];
		delete pUDF;
		m_UDFList.RemoveAt(idx);
		// Delete the item from the list and update the remaining items
		// to have the correct index into the object list
		DeleteUDFFromList(curSel);
	}

	pProgress->DestroyWindow();

	UpdateData(FALSE);

}

void CUDFMaintenanceDialog::OnModifyUdf() 
{
	CUDF *pUDF;
	CUDFProperties dlg;
	int rc, curSel, baySel, udfSel, i, idx;
	POSITION pos;
	BOOL bDone = FALSE;

	curSel = m_UDFTypeCtrl.GetCurSel();
	
	if (curSel < 0) {
		AfxMessageBox("Please select an element type from the list.");
		m_UDFTypeCtrl.SetFocus();
		m_UDFTypeCtrl.ShowDropDown(TRUE);
		return;
	}

	if (m_UDFTypeCtrl.GetItemData(curSel) == UDF_LEVEL_PROFILE) {
		baySel = m_BayProfileListCtrl.GetCurSel();
		if (baySel < 0) {
			AfxMessageBox("Please select a bay profile from the list.");
			m_BayProfileListCtrl.SetFocus();
			m_BayProfileListCtrl.ShowDropDown(TRUE);
			return;
		}
	}

	pos = m_UDFListCtrl.GetFirstSelectedItemPosition();
	if (pos == NULL) {
		AfxMessageBox("Please select user defined field from the list.");
		return;
	}

	udfSel = m_UDFListCtrl.GetNextSelectedItem(pos);

	pUDF = new CUDF;
	idx = m_UDFListCtrl.GetItemData(udfSel);
	*pUDF = *(m_UDFList[idx]);

	dlg.m_UDF = pUDF;

	while (! bDone) {
		bDone = TRUE;

		try {
			rc = dlg.DoModal();
			if (rc != IDOK) {
				delete pUDF;
				return;
			}
		}
		catch (...) {
			AfxMessageBox("Error displaying UDF properties.");
			return;
		}

		for (i=0; i < m_UDFList.GetSize(); ++i) {
			if (pUDF->m_Name == m_UDFList[i]->m_Name && i != idx) {
				AfxMessageBox("The UDF name is already in use.");
				bDone = FALSE;
				break;
			}
		}
	}
	
	CWaitCursor cwc;
	try {
		rc = udfDataService.StoreUDF(pUDF);
	}
	catch (Ssa_Exception e) {
		utilityHelper.ProcessError("Error updating UDF.", &e);
		delete pUDF;
		return;
	}
	catch (...) {
		utilityHelper.ProcessError("Error updating UDF.");
		delete pUDF;
		return;
	}

	// Update the list
	delete m_UDFList[idx];
	m_UDFList[idx] = pUDF;

	
	m_UDFListCtrl.SetItemText(udfSel, 0, pUDF->m_Name);
	m_UDFListCtrl.SetItemText(udfSel, 1, pUDF->GetTypeAsString());
	m_UDFListCtrl.SetItemText(udfSel, 2, pUDF->m_DefaultValue);

	UpdateData(FALSE);

	return;		
}



void CUDFMaintenanceDialog::OnEndlabeleditUdfList(NMHDR* pNMHDR, LRESULT* pResult) 
{
	LV_DISPINFO* pDispInfo = (LV_DISPINFO*)pNMHDR;

	LVITEM lvItem;
	CUDF *pUDF;
	int rc;

	*pResult = 0;
	lvItem = pDispInfo->item;	

	if (lvItem.pszText != NULL) {
		for (int i=0; i < m_UDFListCtrl.GetItemCount(); ++i) {
			if (m_UDFListCtrl.GetItemText(i, 0).Compare(lvItem.pszText) == 0 &&	lvItem.iItem != i) {
				AfxMessageBox("Please enter a name that does not already exist.");
				return;
			}
		}
		*pResult = 1;
		pUDF = m_UDFList[m_UDFListCtrl.GetItemData(lvItem.iItem)];
		pUDF->m_Name == lvItem.pszText;
		CWaitCursor cwc;
		try {
			rc = udfDataService.StoreUDF(pUDF);
		}
		catch (Ssa_Exception e) {
			utilityHelper.ProcessError("Error updating UDF.", &e);
			return;
		}
		catch (...) {
			utilityHelper.ProcessError("Error updating UDF.");
			return;
		}
	}
		
}

void CUDFMaintenanceDialog::OnSelchangeBayProfileList() 
{
	int curSel;

	curSel = m_BayProfileListCtrl.GetCurSel();
	if (m_BayProfileListCtrl.GetItemData(curSel) == m_PreviousBayProfile)
		return;

	m_PreviousBayProfile = m_BayProfileListCtrl.GetItemData(curSel);

	LoadUDFs(UDF_LEVEL_PROFILE, m_PreviousBayProfile);

	return;

}

void CUDFMaintenanceDialog::AddUDFToList(CUDF *pUDF, int idx)
{
	LVITEM lvItem;
	int nItem;
	CString temp;

	lvItem.mask = LVIF_TEXT|LVIF_PARAM;

	lvItem.iItem = idx;
	lvItem.iSubItem = 0;
	lvItem.pszText = pUDF->m_Name.GetBuffer(0);
	lvItem.lParam = idx;
	pUDF->m_Name.ReleaseBuffer();
	nItem = m_UDFListCtrl.InsertItem(&lvItem);

	lvItem.mask = LVIF_TEXT;
	lvItem.iItem = nItem;
	lvItem.iSubItem = 1;
	temp = pUDF->GetTypeAsString();
	lvItem.pszText = temp.GetBuffer(0);
	temp.ReleaseBuffer();
	m_UDFListCtrl.SetItem(&lvItem);

	lvItem.iSubItem = 2;
	lvItem.pszText = pUDF->m_DefaultValue.GetBuffer(0);
	pUDF->m_DefaultValue.ReleaseBuffer();
	m_UDFListCtrl.SetItem(&lvItem);
	return;

}

int CUDFMaintenanceDialog::LoadBayProfiles()
{
	CStringArray profileList;
	CBayProfile *pProfile;
	int nItem, rc;

	try {
		CBayProfileDataService bayProfileDataService;

		rc = bayProfileDataService.GetBayProfiles(profileList);
	}
	catch (Ssa_Exception e) {
		utilityHelper.ProcessError("Error getting bay profiles.", &e);
		return -1;
	}
	catch (...) {
		utilityHelper.ProcessError("Error getting bay profiles.");
		return -1;
	}

	for (int i=0; i < profileList.GetSize(); ++i) {
		pProfile = new CBayProfile;
		pProfile->Parse(profileList[i]);
		m_BayProfileList.Add(pProfile);
		nItem = m_BayProfileListCtrl.AddString(pProfile->m_Description);
		m_BayProfileListCtrl.SetItemData(nItem, pProfile->m_BayProfileDBId);
	}

	UpdateData(FALSE);

	return 0;
}

int CUDFMaintenanceDialog::LoadUDFs(int elementType, int parentID)
{
	int rc, idx;
	CStringArray udfList;
	CUDF *pUDF;

	// Clear the current list
	m_UDFListCtrl.DeleteAllItems();

	for (int i=0; i < m_UDFList.GetSize(); ++i)
		delete m_UDFList[i];

	m_UDFList.RemoveAll();

	// Load the new ones
	try {
		rc = udfDataService.GetUDFList(elementType, parentID, udfList);
	}
	catch(Ssa_Exception e) {
		utilityHelper.ProcessError("Error loading user defined fields.", &e);
		return -1;
	}
	catch (...) {
		utilityHelper.ProcessError("Error loading user defined fields.");
		return -1;
	}

	for (i=0; i < udfList.GetSize(); ++i) {
		pUDF = new CUDF;
		pUDF->Parse(udfList[i]);
		idx = m_UDFList.Add(pUDF);
		AddUDFToList(pUDF, idx);
	}

	return 0;

}


UINT CUDFMaintenanceDialog::DeleteUDFThread(LPVOID pParam)
{
	CStringArray *pArray;
	int rc, elementType;
	long listID;

	pArray = (CStringArray *)pParam;
	elementType = atoi((*pArray)[0]);
	listID = atol((*pArray)[1]);

	try {
		rc = udfDataService.DeleteUDF(elementType, listID);
		(*pArray)[0].Format("%d", rc);
	}
	catch (Ssa_Exception e) {
		char msgBuf[1024];
		e.GetMessage(msgBuf);
		(*pArray)[0].Format("%d", -1);
		(*pArray)[1].Format("%s", msgBuf);
	}
	catch (...) {
		(*pArray)[0].Format("%d", -1);
		(*pArray)[1].Format("Generic error.");
	}

	g_ThreadDone.SetEvent();

	return 0;

}


UINT CUDFMaintenanceDialog::StoreUDFThread(LPVOID pParam)
{
	int rc;
	CStringArray *pArray;

	pArray = (CStringArray *)pParam;
	pArray->SetSize(2);

	try {
		rc = udfDataService.StoreUDF(gUDF);
		(*pArray)[0].Format("%d", rc);
	}
	catch (Ssa_Exception e) {
		char msgBuf[1024];
		e.GetMessage(msgBuf);
		(*pArray)[0].Format("%d", -1);
		(*pArray)[1].Format("%s", msgBuf);
	}
	catch (...) {
		(*pArray)[0].Format("%d", -1);
		(*pArray)[1].Format("Generic error.");
	}

	g_ThreadDone.SetEvent();

	return 0;

}

void CUDFMaintenanceDialog::OnContextMenu(CWnd* pWnd, CPoint point) 
{
	CMenu menu;
	int curSel;

	if (pWnd == &m_UDFListCtrl) {
		menu.LoadMenu(IDR_GENERIC_MENU);
		curSel = m_UDFListCtrl.GetSelectedCount();
		
		if (curSel <= 0) {
			menu.GetSubMenu(0)->EnableMenuItem(1, MF_BYPOSITION|MF_GRAYED);	// disable delete
			menu.GetSubMenu(0)->EnableMenuItem(2, MF_BYPOSITION|MF_GRAYED); // disable properties
		}
		else {
			menu.GetSubMenu(0)->EnableMenuItem(1, MF_BYPOSITION|MF_ENABLED);
			menu.GetSubMenu(0)->EnableMenuItem(2, MF_BYPOSITION|MF_ENABLED);
		}

		menu.GetSubMenu(0)->TrackPopupMenu(TPM_LEFTALIGN|TPM_RIGHTBUTTON, point.x, point.y, this);
	}

}

void CUDFMaintenanceDialog::DeleteUDFFromList(int idx)
{
	DWORD oldListIdx = m_UDFListCtrl.GetItemData(idx);

	m_UDFListCtrl.DeleteItem(idx);
	// Reset the data (which is the index) of all the items
	// that come after the one we are deleting
	for (int i=0; i < m_UDFListCtrl.GetItemCount(); ++i) {
		if (m_UDFListCtrl.GetItemData(i) < oldListIdx)
			continue;
		m_UDFListCtrl.SetItemData(i, m_UDFListCtrl.GetItemData(i)-1);
	}

	return;
}

void CUDFMaintenanceDialog::OnDblclkUdfList(NMHDR* pNMHDR, LRESULT* pResult) 
{
	UNREFERENCED_PARAMETER(pNMHDR);

	*pResult = 1;
	OnModifyUdf();
}

BOOL CUDFMaintenanceDialog::OnHelpInfo(HELPINFO* pHelpInfo) 
{
	helpService.ShowFieldHelp(IDD, pHelpInfo->iCtrlId);
	return FALSE;
}

// AisleProfileDataService.cpp: implementation of the CAisleProfileDataService class.
//
//////////////////////////////////////////////////////////////////////

#include "stdafx.h"
#include "DataAccessService.h"
#include "modal.h"
#include "AisleProfileDataService.h"
#include "ForteService.h"
#include "ssa_exception.h"
#include "SideProfileDataService.h"
#include "UtilityHelper.h"

#ifdef _DEBUG
#undef THIS_FILE
static char THIS_FILE[]=__FILE__;
#define new DEBUG_NEW
#endif

extern CForteService forteService;
extern CDataAccessService dataAccessService;
extern CUtilityHelper utilityHelper;
extern CSideProfileDataService sideProfileDataService;

//////////////////////////////////////////////////////////////////////
// Construction/Destruction
//////////////////////////////////////////////////////////////////////

CAisleProfileDataService::CAisleProfileDataService()
{

}

CAisleProfileDataService::~CAisleProfileDataService()
{

}


int CAisleProfileDataService::GetAisleProfileNameList(CStringArray &list)
{
	CString query;

	query.Format("select description from dbaisleprofile");

	return dataAccessService.ExecuteQuery("GetAisleProfileNameList", query, list, TRUE);

}


BOOL CAisleProfileDataService::IsSideProfileInAisle(int sideProfileDBID)
{
	CString tempString;
	CSsaStringArray tempSendArray;
	CSsaStringArray tempRecvArray;
	CString sendString;
	int i;
	int tempInt;

	/////////////////////////////////////////////////////////////
	// Build the data and send to Forte connection
	/////////////////////////////////////////////////////////////
	sendString = "<SIO>SLOTSocketString\n";
	tempSendArray.Add(sendString);

	sendString.Format("<SAI>%d\n", sideProfileDBID);
	tempSendArray.Add(sendString);

	sendString = "<EIO>SLOTSocketString\n";
	tempSendArray.Add(sendString);

	forteService.SendToForteConnection(tempSendArray,tempRecvArray,
		CString("SLOTSocketString"), 10020);

	for (i=0; i < tempRecvArray.GetSize(); i++)
	{
		if ( tempRecvArray[i].Find("<SAI>") != -1 )
		{
			tempString = tempRecvArray[i].Mid(5);
			tempInt = tempString.GetLength();
			if (tempString.GetAt(tempInt-1) == '\n' )
			{
				tempString.SetAt(tempInt-1,' ');
				tempString.TrimRight();
			}
		}
	}
	if (tempString == "TRUE")
		return TRUE;
	else
		return FALSE;
}


BOOL CAisleProfileDataService::IsAisleProfileNameInUse(int dbid, const CString &name)
{
	CString sql;
	CStringArray results;

	sql.Format("select count(*) from dbaisleprofile "
		"where description = '%s' "
		"and dbaisleprofileid != %d", name, dbid);
	
	dataAccessService.ExecuteQuery("IsAisleProfileNameInuse", sql, results, TRUE);

	if (results.GetSize() > 0)
		return (atoi(results[0]) != 0);

	return FALSE;
}

int CAisleProfileDataService::UpdateAisleProfileName(int aisleProfileDBId, const CString &name)
{
	CString sql;

	sql.Format("update dbaisleprofile set description = '%s' where dbaisleprofileid = %d",
		name, aisleProfileDBId);

	return dataAccessService.ExecuteStatement("UpdateAisleProfileName", sql);
}


int CAisleProfileDataService::GetAisleProfileList(CStringArray &sideNameList)
{
	CString sql;

	sql.Format("select dbaisleprofileid, description "
		"from dbaisleprofile "
		"order by description");

	return dataAccessService.ExecuteQuery("GetAisleProfileList", sql, sideNameList);
}

int CAisleProfileDataService::GetAisleProfile(int aisleProfileDBId, CAisleProfile& aisleProfile)
{
	CString sql;
	CStringArray results;

	sql.Format("select dbaisleprofileid, description, aislespace "
		"from dbaisleprofile "
		"where dbaisleprofileid = %d", aisleProfileDBId);
	
	dataAccessService.ExecuteQuery("GetAisleProfile", sql, results);
	if (results.GetSize() == 0)
		return -1;
	aisleProfile.Parse(results[0]);
	
	results.RemoveAll();

	sql.Format("select dbsideprofile.dbsideprofileid, dbsideprofile.description, "
		"space "
		"from dbaislesideprof, dbsideprofile "
		"where dbaislesideprof.dbaisleprofileid = %d "
		"and dbaislesideprof.dbsideprofileid = dbsideprofile.dbsideprofileid "
		"order by space", aisleProfileDBId);
	dataAccessService.ExecuteQuery("GetAisleSideProfile", sql, results, TRUE);
	
	for (int i=0; i < results.GetSize(); ++i) {
		CStringArray strings;
		utilityHelper.ParseString(results[i], "|", strings);
		if (atoi(strings[2]) < 0) {		// left side has negative space
			aisleProfile.m_pLeftSideProfile = new CSideProfile;
			aisleProfile.m_pLeftSideProfile->m_SideProfileDBId = atoi(strings[0]);
			aisleProfile.m_pLeftSideProfile->m_Description = strings[1];
			aisleProfile.m_LeftSpace = -atof(strings[2]);
			if (aisleProfile.m_LeftSpace == 999)
				aisleProfile.m_LeftSpace = 0;
		}
		else {
			aisleProfile.m_pRightSideProfile = new CSideProfile;
			aisleProfile.m_pRightSideProfile->m_SideProfileDBId = atoi(strings[0]);
			aisleProfile.m_pRightSideProfile->m_Description = strings[1];
			aisleProfile.m_RightSpace = atof(strings[2]);
			if (aisleProfile.m_RightSpace == 999)
				aisleProfile.m_RightSpace = 0;
		}
	}


	return 0;
}

int CAisleProfileDataService::DeleteAisleProfile(int aisleProfileDBId)
{
	CString sql;
	CStringArray stmts;

	sql.Format("delete from dbaislesideprof where dbaisleprofileid = %d", aisleProfileDBId);
	stmts.Add(sql);
	sql.Format("delete from dbaisleprofile where dbaisleprofileid = %d", aisleProfileDBId);
	stmts.Add(sql);

	return dataAccessService.ExecuteStatements("DeleteAisleProfile", stmts);
}

int CAisleProfileDataService::StoreAisleProfile(CAisleProfile& aisleProfile)
{
	CString sql;
	CStringArray stmts;

	if (aisleProfile.m_AisleProfileDBId <= 0) {
		aisleProfile.m_AisleProfileDBId = dataAccessService.GetNextKey("DBAisleProfile", 1);
		sql.Format("insert into dbaisleprofile (dbaisleprofileid, description, "
			"aislespace, createdate, changedate, lastuserid) "
			"values (%d, '%s', %f, sysdate, sysdate, 1)",
			aisleProfile.m_AisleProfileDBId, aisleProfile.m_Description,
			aisleProfile.m_AisleSpace);
		stmts.Add(sql);
		
		int nextKey, keyCount = 0;
		if (aisleProfile.m_pLeftSideProfile != NULL)
			keyCount++;
		
		if (aisleProfile.m_pRightSideProfile != NULL)
			keyCount++;
			
		nextKey = dataAccessService.GetNextKey("DBAisleSideProf", keyCount);	

		if (aisleProfile.m_pLeftSideProfile != NULL) {
			double leftSpace = -aisleProfile.m_LeftSpace;
			if (leftSpace == 0)
				leftSpace = -999;

			sql.Format("insert into dbaislesideprof (dbaislesideprofid, space, "
				"createdate, changedate, lastuserid, dbaisleprofileid, dbsideprofileid) "
				"values (%d, %f, sysdate, sysdate, 1, %d, %d)",
				nextKey++, leftSpace,
				aisleProfile.m_AisleProfileDBId, aisleProfile.m_pLeftSideProfile->m_SideProfileDBId);
			stmts.Add(sql);
		}
		
		if (aisleProfile.m_pRightSideProfile != NULL) {
			double rightSpace = aisleProfile.m_RightSpace;
			if (rightSpace == 0)
				rightSpace = 999;

			sql.Format("insert into dbaislesideprof (dbaislesideprofid, space, "
				"createdate, changedate, lastuserid, dbaisleprofileid, dbsideprofileid) "
				"values (%d, %f, sysdate, sysdate, 1, %d, %d)",
				nextKey++, rightSpace,
				aisleProfile.m_AisleProfileDBId, aisleProfile.m_pRightSideProfile->m_SideProfileDBId);
			stmts.Add(sql);
			
		}
	}
	else {		// update
		sql.Format("update dbaisleprofile set description = '%s', "
			"aislespace = %f "
			"where dbaisleprofileid = %d", aisleProfile.m_Description,
			aisleProfile.m_AisleSpace, aisleProfile.m_AisleProfileDBId);
		stmts.Add(sql);

		sql.Format("delete from dbaislesideprof where dbaisleprofileid = %d", aisleProfile.m_AisleProfileDBId);
		stmts.Add(sql);

		int nextKey, keyCount = 0;
		if (aisleProfile.m_pLeftSideProfile != NULL)
			keyCount++;
		
		if (aisleProfile.m_pRightSideProfile != NULL)
			keyCount++;
		
		nextKey = dataAccessService.GetNextKey("DBAisleSideProf", keyCount);
		
		if (aisleProfile.m_pLeftSideProfile != NULL) {
			double leftSpace = -aisleProfile.m_LeftSpace;
			if (leftSpace == 0)
				leftSpace = -999;

			sql.Format("insert into dbaislesideprof (dbaislesideprofid, space, "
				"createdate, changedate, lastuserid, dbaisleprofileid, dbsideprofileid) "
				"values (%d, %f, sysdate, sysdate, 1, %d, %d)",
				nextKey++, leftSpace,
				aisleProfile.m_AisleProfileDBId, aisleProfile.m_pLeftSideProfile->m_SideProfileDBId);
			stmts.Add(sql);
		}
		
		if (aisleProfile.m_pRightSideProfile != NULL) {
			double rightSpace = aisleProfile.m_RightSpace;
			if (rightSpace == 0)
				rightSpace = 999;

			sql.Format("insert into dbaislesideprof (dbaislesideprofid, space, "
				"createdate, changedate, lastuserid, dbaisleprofileid, dbsideprofileid) "
				"values (%d, %f, sysdate, sysdate, 1, %d, %d)",
				nextKey++, rightSpace,
				aisleProfile.m_AisleProfileDBId, aisleProfile.m_pRightSideProfile->m_SideProfileDBId);
			stmts.Add(sql);
			
		}
	}

	return dataAccessService.ExecuteStatements("StoreAisleProfile", stmts);
}


int CAisleProfileDataService::GetAisleProfile(const CString &name, CAisleProfile& aisleProfile)
{
	CString sql;
	CStringArray results;

	sql.Format("select dbaisleprofileid from dbaisleprofile where description = '%s'", name);
	if (dataAccessService.ExecuteQuery("GetAisleProfileByName", sql, results, TRUE) < 0)
		return -1;

	if (results.GetSize() == 0)
		return -1;

	return GetAisleProfile(atoi(results[0]), aisleProfile);

}


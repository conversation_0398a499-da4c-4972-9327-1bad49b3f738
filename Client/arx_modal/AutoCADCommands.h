// AutoCADCommands.h: interface for the CAutoCADCommands class.
//
//////////////////////////////////////////////////////////////////////

#if !defined(AFX_AUTOCADCOMMANDS_H__4DF7E689_8C4C_444D_BA48_E11659045C6A__INCLUDED_)
#define AFX_AUTOCADCOMMANDS_H__4DF7E689_8C4C_444D_BA48_E11659045C6A__INCLUDED_


#if _MSC_VER > 1000
#pragma once
#endif // _MSC_VER > 1000

#include <gepnt3d.h>
#include <adsdef.h>

class CAutoCADCommands
{
public:
	int CreateNextLayer(CString &newLayerName, const CString &layerType);
	static CString GetXDataForObject(CString &handle);
	static int GetDrawingObjectCoordinates(CString &handle, AcGePoint3d &point, double &rotation);
	static BOOL IsHandleABay(CString &handle);
	static void Flush();
	void RunScript(CStringArray &script, const CString &scriptName);
	void ModifyDrawing();
	CString GetColorFromIndex(int nIndex);

	CAutoCADCommands();
	virtual ~CAutoCADCommands();
	
	static short Get_Int_Var (const char* Varname);
	static void Set_Int_Var(const char *varName, const short intVal);
	static void Set_String_Var(const char *varName, char *strVal);
	static void CAutoCADCommands::ColorAllObjects(int colorIndex=7);
	static void ColorDrawingObjectByHandle(CString handle, int colorIndex);
	static int GetColorIndexByName(const CString &strColor);
	static int GetSelectedHandles(CStringArray &handles);
	static int GetSelectedHandlesPrompt(CStringArray &handles);
	static int GetSelectedHandle(CString &handle);
	static int DeleteDrawingObjectByHandle(CString handle);
	static int GetColorChoice();
	static void DeleteXData(CString &handle);
	static void ZoomToHandle(CString handle, int magnification);
	static void HighlightDrawingObjectByHandle(CString handle);
	static void ListHandles();
	static int GetAllDrawingBays(CStringArray &bayList);
	static void ZoomExtentsByHandle(CString &handle);

	static void RotatePoint(ads_point initPoint, ads_point &rotPoint, double angle);

};

#endif // !defined(AFX_AUTOCADCOMMANDS_H__4DF7E689_8C4C_444D_BA48_E11659045C6A__INCLUDED_)

#if !defined(AFX_UDFPROPERTIES_H__007B5313_2F6A_11D5_9ED0_00C04FAC149C__INCLUDED_)
#define AFX_UDFPROPERTIES_H__007B5313_2F6A_11D5_9ED0_00C04FAC149C__INCLUDED_

#if _MSC_VER > 1000
#pragma once
#endif // _MSC_VER > 1000
// UDFProperties.h : header file
//
#include "Resource.h"
#include "UDF.h"
/////////////////////////////////////////////////////////////////////////////
// CUDFProperties dialog

class CUDFProperties : public CDialog
{
// Construction
public:
	CUDF *m_UDF;
	CUDFProperties(CWnd* pParent = NULL);   // standard constructor

// Dialog Data
	//{{AFX_DATA(CUDFProperties)
	enum { IDD = IDD_UDF_PROPERTIES };
	CListCtrl	m_ListElementsCtrl;
	CStatic	m_StaticListCtrl;
	CButton	m_DeleteElementCtrl;
	CButton	m_AddElementCtrl;
	CComboBox	m_TypeCtrl;
	CString	m_DefaultValue;
	CString	m_Name;
	//}}AFX_DATA


// Overrides
	// ClassWizard generated virtual function overrides
	//{{AFX_VIRTUAL(CUDFProperties)
	protected:
	virtual void DoDataExchange(CDataExchange* pDX);    // DDX/DDV support
	//}}AFX_VIRTUAL

// Implementation
protected:

	// Generated message map functions
	//{{AFX_MSG(CUDFProperties)
	virtual BOOL OnInitDialog();
	virtual void OnOK();
	virtual void OnCancel();
	afx_msg void OnHelp();
	afx_msg void OnAddElement();
	afx_msg void OnDeleteElement();
	afx_msg void OnSelchangeType();
	afx_msg void OnEndlabeleditListElements(NMHDR* pNMHDR, LRESULT* pResult);
	afx_msg BOOL OnHelpInfo(HELPINFO* pHelpInfo);
	//}}AFX_MSG
	DECLARE_MESSAGE_MAP()
private:
	int m_PreviousType;
	BOOL m_Adding;
	CUDF m_OrigUDF;
};

//{{AFX_INSERT_LOCATION}}
// Microsoft Visual C++ will insert additional declarations immediately before the previous line.

#endif // !defined(AFX_UDFPROPERTIES_H__007B5313_2F6A_11D5_9ED0_00C04FAC149C__INCLUDED_)

// BayProfileLevelPage.cpp : implementation file
//

#include "stdafx.h"
#include "modal.h"
#include "BayProfileLevelPage.h"
#include "BayProfileSheet.h"
#include "UtilityHelper.h"
#include "HelpService.h"

#ifdef _DEBUG
#define new DEBUG_NEW
#undef THIS_FILE
static char THIS_FILE[] = __FILE__;
#endif

extern CUtilityHelper utilityHelper;
extern CHelpService helpService;
/////////////////////////////////////////////////////////////////////////////
// CBayProfileLevelPage property page

IMPLEMENT_DYNCREATE(CBayProfileLevelPage, CPropertyPage)

CBayProfileLevelPage::CBayProfileLevelPage() : CPropertyPage(CBayProfileLevelPage::IDD)
{
	//{{AFX_DATA_INIT(CBayProfileLevelPage)
	m_BackfillCode = _T("");
	m_Rotation = FALSE;
	m_VariableWidth = FALSE;
	m_MaximumCaseCount = _T("");
	m_MaximumCaseWeight = _T("");
	m_FlowDifference = _T("");
	//}}AFX_DATA_INIT
}

CBayProfileLevelPage::~CBayProfileLevelPage()
{
}

void CBayProfileLevelPage::DoDataExchange(CDataExchange* pDX)
{
	CPropertyPage::DoDataExchange(pDX);
	//{{AFX_DATA_MAP(CBayProfileLevelPage)
	DDX_Control(pDX, IDC_LEVEL_TYPE_LIST, m_LevelTypeListCtrl);
	DDX_Control(pDX, IDC_LEVEL_LIST, m_LevelListCtrl);
	DDX_Control(pDX, IDC_LEVEL_BUTTON, m_LevelButton);
	DDX_Text(pDX, IDC_BACKFILL_CODE, m_BackfillCode);
	DDX_Check(pDX, IDC_ROTATION_CHECKBOX, m_Rotation);
	DDX_Check(pDX, IDC_VARIABLE_WIDTH_CHECKBOX, m_VariableWidth);
	DDX_Text(pDX, IDC_MAXIMUM_CASE_COUNT, m_MaximumCaseCount);
	DDX_Text(pDX, IDC_MAXIMUM_CASE_WEIGHT, m_MaximumCaseWeight);
	DDX_Text(pDX, IDC_FLOW_DIFFERENCE, m_FlowDifference);
	//}}AFX_DATA_MAP
}


BEGIN_MESSAGE_MAP(CBayProfileLevelPage, CPropertyPage)
	//{{AFX_MSG_MAP(CBayProfileLevelPage)
	ON_CBN_SELCHANGE(IDC_LEVEL_LIST, OnSelchangeLevelList)
	ON_CBN_SELCHANGE(IDC_LEVEL_TYPE_LIST, OnSelchangeLevelTypeList)
	ON_MESSAGE(WM_SELECT_LEVEL, (LRESULT (AFX_MSG_CALL CWnd::*)(WPARAM, LPARAM))OnSelectLevel)
	ON_WM_HELPINFO()
	//}}AFX_MSG_MAP
END_MESSAGE_MAP()

/////////////////////////////////////////////////////////////////////////////
// CBayProfileLevelPage message handlers

BOOL CBayProfileLevelPage::OnInitDialog() 
{
	CPropertyPage::OnInitDialog();
	
	return TRUE;  // return TRUE unless you set the focus to a control
	              // EXCEPTION: OCX Property Pages should return FALSE
}

BOOL CBayProfileLevelPage::OnSetActive() 
{
	CBayProfileSheet *pSheet = (CBayProfileSheet *)GetParent();

	m_pBayProfile = pSheet->m_pBayProfile;

	m_LevelButton.m_CrossbarList.RemoveAll();
	m_LevelButton.m_BayHeight = m_pBayProfile->m_Height;
	m_LevelButton.m_UprightHeight = m_pBayProfile->m_UprightHeight;
	m_LevelButton.m_BayWidth = m_pBayProfile->m_Width;	

	for (int i=0; i < m_pBayProfile->m_LevelProfileList.GetSize(); ++i) {

		CLevelProfile *pLevelProfile = m_pBayProfile->m_LevelProfileList[i];

		CBayProfileCrossbarInfo info;
		info.m_Clearance = pLevelProfile->m_Clearance;
		info.m_Height = pLevelProfile->m_Coordinates.m_Z;
		info.m_IsHidden = pLevelProfile->m_IsBarHidden;
		info.m_IsSelected = (i == pSheet->m_SelectedLevel);
		info.m_LocationCount = pLevelProfile->m_LocationProfileList.GetSize();
		info.m_MinimumWidth = pLevelProfile->m_MinimumLocWidth;
		info.m_LocationRowCount = pLevelProfile->m_LocationRowCount;

		if (info.m_LocationCount == 0)
			info.m_LocationSpace = 0;
		else
			info.m_LocationSpace = pLevelProfile->m_LocationProfileList[0]->m_LocationSpace;

		info.m_Thickness = pLevelProfile->m_Thickness;
		
		m_LevelButton.m_CrossbarList.Add(info);

	}

	BuildLevelTypeList();

	RebuildLevelList();

	m_LevelListCtrl.SetCurSel(pSheet->m_SelectedLevel);
	UpdateScreenFromLevelProfile(pSheet->m_SelectedLevel);
	m_LevelButton.Invalidate();
	
//	if (m_pBayProfile->m_Active) {
//		GetDlgItem(IDC_LEVEL_TYPE_LIST)->EnableWindow(FALSE);
//	}
//	else {
		GetDlgItem(IDC_LEVEL_TYPE_LIST)->EnableWindow(TRUE);
//	}

	return CPropertyPage::OnSetActive();
}

BOOL CBayProfileLevelPage::OnKillActive() 
{
	CBayProfileSheet *pSheet = (CBayProfileSheet *)GetParent();

	if (UpdateLevelProfileFromScreen(pSheet->m_SelectedLevel) < 0)
		return FALSE;

	if (! Validate())
		return FALSE;

	// This will make sure we have the right number of rules for the different level types
	m_pBayProfile->ResetRules();


	return CPropertyPage::OnKillActive();
}

void CBayProfileLevelPage::OnSelchangeLevelList() 
{
	int curSel = m_LevelListCtrl.GetCurSel();
	if (curSel < 0)
		return;

	if (m_LevelButton.SelectLevel(curSel) < 0) {
		// Set the selection back to the previous which
		// is kept on the parent if it fails
		CBayProfileSheet *pSheet = (CBayProfileSheet *)GetParent();
		m_LevelListCtrl.SetCurSel(pSheet->m_SelectedLevel);
	}	
}

int CBayProfileLevelPage::OnSelectLevel(WPARAM wParam, LPARAM lParam)
{
	UNREFERENCED_PARAMETER(lParam);

	CBayProfileSheet *pSheet = (CBayProfileSheet *)GetParent();
	int newLevel = wParam;

	if (pSheet->m_SelectedLevel != newLevel) {	
		
		if (UpdateLevelProfileFromScreen(pSheet->m_SelectedLevel) < 0)
			return -1;

		if (newLevel >= 0) {
			if (UpdateScreenFromLevelProfile(newLevel) < 0)
				return -1;
		}
		
		pSheet->m_SelectedLevel = newLevel;
	}

	m_LevelListCtrl.SetCurSel(newLevel);

	return 0;
}

void CBayProfileLevelPage::RebuildLevelList()
{
	int curSel = m_LevelListCtrl.GetCurSel();

	m_LevelListCtrl.ResetContent();
	for (int i=0; i < m_pBayProfile->m_LevelProfileList.GetSize(); ++i) {
		m_pBayProfile->m_LevelProfileList[i]->m_Description.Format("%d", i+1);
		m_pBayProfile->m_LevelProfileList[i]->m_RelativeLevel = i+1;
		CString temp;
		if (i == 0)
			temp = "Level 1 - Floor";
		else
			temp.Format("Level: %d - Position: %.0f", i+1, m_pBayProfile->m_LevelProfileList[i]->m_Coordinates.m_Z);
		int nItem = m_LevelListCtrl.AddString(temp);
		//m_LevelListCtrl.SetItemData(nItem, m_pBayProfile->m_LevelProfileList[i]->m_Coordinates.m_Z);
	}

	if (curSel >= m_LevelListCtrl.GetCount())
		curSel = m_LevelListCtrl.GetCount()-1;

	m_LevelListCtrl.SetCurSel(curSel);

	CRect r;
	m_LevelListCtrl.GetWindowRect(&r);
	m_LevelListCtrl.SetWindowPos(NULL, 0, 0, r.Width(), 
		r.Height()*(m_pBayProfile->m_LevelProfileList.GetSize()+1), SWP_NOMOVE|SWP_NOZORDER);

}

int CBayProfileLevelPage::UpdateScreenFromLevelProfile(int currentLevel)
{
	CLevelProfile *pLevelProfile = m_pBayProfile->m_LevelProfileList[currentLevel];
	
	m_BackfillCode = pLevelProfile->m_BackfillCode;
	m_VariableWidth = pLevelProfile->m_IsVariableWidthAllowed;
	m_Rotation = pLevelProfile->m_IsRotateAllowed;
	m_MaximumCaseCount.Format("%d", pLevelProfile->m_MaximumCaseCount);
	m_MaximumCaseWeight.Format("%.1f", pLevelProfile->m_MaximumCaseWeight);
	m_FlowDifference.Format("%.0f", pLevelProfile->m_FlowDifference);

	if (pLevelProfile->m_Baytype != BAYTYPE_CASEFLOW && 
		pLevelProfile->m_Baytype != BAYTYPE_PALLETFLOW)
		GetDlgItem(IDC_FLOW_DIFFERENCE)->EnableWindow(FALSE);
	else
		GetDlgItem(IDC_FLOW_DIFFERENCE)->EnableWindow(TRUE);

	for (int i=0; i < m_LevelTypeListCtrl.GetCount(); ++i) {
		if ((int)m_LevelTypeListCtrl.GetItemData(i) == pLevelProfile->m_Baytype) {
			m_LevelTypeListCtrl.SetCurSel(i);
			break;
		}
	}

	UpdateData(FALSE);

	return 0;
}


int CBayProfileLevelPage::UpdateLevelProfileFromScreen(int currentLevel)
{
	UpdateData(TRUE);
	
	CLevelProfile *pLevelProfile = m_pBayProfile->m_LevelProfileList[currentLevel];

	pLevelProfile->m_BackfillCode = m_BackfillCode;
	pLevelProfile->m_IsVariableWidthAllowed = m_VariableWidth;
	pLevelProfile->m_IsRotateAllowed = m_Rotation;
	pLevelProfile->m_Baytype = m_LevelTypeListCtrl.GetItemData(m_LevelTypeListCtrl.GetCurSel());

	if (utilityHelper.IsNumeric(m_MaximumCaseWeight))
		pLevelProfile->m_MaximumCaseWeight = atof(m_MaximumCaseWeight);
	else
		pLevelProfile->m_MaximumCaseWeight = 0;

	if (utilityHelper.IsNumeric(m_MaximumCaseCount))
		pLevelProfile->m_MaximumCaseCount = atoi(m_MaximumCaseCount);
	else
		pLevelProfile->m_MaximumCaseCount = 0;

	if (utilityHelper.IsNumeric(m_FlowDifference))
		pLevelProfile->m_FlowDifference = atof(m_FlowDifference);

	return 0;
}

void CBayProfileLevelPage::BuildLevelTypeList()
{
	m_LevelTypeListCtrl.ResetContent();
	
	// Need to think about which combinations of level types
	// can go with which bay types
	// floor - none
	// drive-in - none
	// pallet - floor, bin, pallet
	// flow - floor, bin, pallet
	// bin - pallet, floor, pallet

	int nItem;

	switch (m_pBayProfile->m_BayType) {
	case  BAYTYPE_FLOOR:		// irrelevant for now since we don't show this page on floor
		nItem = m_LevelTypeListCtrl.AddString("Floor");
		m_LevelTypeListCtrl.SetItemData(nItem, BAYTYPE_FLOOR);
		m_LevelTypeListCtrl.EnableWindow(FALSE);
		break;
	case BAYTYPE_DRIVEIN:		// irrelevant for now since we don't show this page on floor
		nItem = m_LevelTypeListCtrl.AddString("Drive In");
		m_LevelTypeListCtrl.SetItemData(nItem, BAYTYPE_DRIVEIN);
		m_LevelTypeListCtrl.EnableWindow(FALSE);
		break;
	default:
		nItem = m_LevelTypeListCtrl.AddString("Case Flow");
		m_LevelTypeListCtrl.SetItemData(nItem, BAYTYPE_CASEFLOW);
		nItem = m_LevelTypeListCtrl.AddString("Pallet Flow");
		m_LevelTypeListCtrl.SetItemData(nItem, BAYTYPE_PALLETFLOW);
		m_LevelTypeListCtrl.EnableWindow(TRUE);
		nItem = m_LevelTypeListCtrl.AddString("Bin");
		m_LevelTypeListCtrl.SetItemData(nItem, BAYTYPE_BIN);
		nItem = m_LevelTypeListCtrl.AddString("Floor");
		m_LevelTypeListCtrl.SetItemData(nItem, BAYTYPE_FLOOR);
		nItem = m_LevelTypeListCtrl.AddString("Pallet");
		m_LevelTypeListCtrl.SetItemData(nItem, BAYTYPE_PALLET);
		break;
	}

	CRect r;
	m_LevelTypeListCtrl.GetWindowRect(&r);
	m_LevelTypeListCtrl.SetWindowPos(NULL, 0, 0, r.Width(), r.Height()*m_LevelTypeListCtrl.GetCount()+1, SWP_NOMOVE|SWP_NOZORDER);
}


BOOL CBayProfileLevelPage::Validate()
{

	return TRUE;
}

void CBayProfileLevelPage::OnSelchangeLevelTypeList() 
{
	int curSel = m_LevelTypeListCtrl.GetCurSel();
	if (curSel < 0)
		return;

	int bayType = m_LevelTypeListCtrl.GetItemData(curSel);

	if (bayType != BAYTYPE_PALLETFLOW && bayType != BAYTYPE_CASEFLOW)
		GetDlgItem(IDC_FLOW_DIFFERENCE)->EnableWindow(FALSE);
	else
		GetDlgItem(IDC_FLOW_DIFFERENCE)->EnableWindow(TRUE);
}

BOOL CBayProfileLevelPage::OnHelpInfo(HELPINFO* pHelpInfo) 
{
	helpService.ShowFieldHelp(IDD, pHelpInfo->iCtrlId);
	return FALSE;

}

BOOL CBayProfileLevelPage::OnNotify(WPARAM wParam, LPARAM lParam, LRESULT* pResult) 
{
	NMHDR* pNMHDR = (NMHDR*)lParam;

	if (pNMHDR->code == PSN_HELP) {
		helpService.ShowScreenHelp(IDD);
		return TRUE;
	}
	
	return CPropertyPage::OnNotify(wParam, lParam, pResult);
}

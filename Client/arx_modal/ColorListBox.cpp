// ColorListBox.cpp : implementation file
//

#include "stdafx.h"
#include "modal.h"
#include "ColorListBox.h"
#include "Constants.h"

#include <aced.h>

#ifdef _DEBUG
#define new DEBUG_NEW
#undef THIS_FILE
static char THIS_FILE[] = __FILE__;
#endif

/////////////////////////////////////////////////////////////////////////////
// CColorListBox

CColorListBox::CColorListBox()
{
}

CColorListBox::~CColorListBox()
{
}


BEGIN_MESSAGE_MAP(CColorListBox, CListBox)
	//{{AFX_MSG_MAP(CColorListBox)
	//}}AFX_MSG_MAP
END_MESSAGE_MAP()

/////////////////////////////////////////////////////////////////////////////
// CColorListBox message handlers

void CColorListBox::DrawItem(LPDRAWITEMSTRUCT lpDrawItemStruct) 
{

	CDC  dc;
	dc.Attach(lpDrawItemStruct->hDC);
	//LPCTSTR lpszText = (LPCTSTR) lpDrawItemStruct->itemData;
	CString lpszText = (LPCTSTR)lpDrawItemStruct->itemData;
	ASSERT(lpDrawItemStruct->CtlType == ODT_LISTBOX);
   
	CColorObject *pColor = (CColorObject *)lpDrawItemStruct->itemData;

	CRect r = lpDrawItemStruct->rcItem;

	CBrush* pTempBrush = NULL;
	CBrush OrigBrush, br;
	CString displayText = pColor->text;

	/*
	int idx, red, green, blue;
	CString displayText;
	idx = lpszText.Find("|");
	displayText = lpszText.Left(idx);
	lpszText = lpszText.Right(lpszText.GetLength()-(idx+1));
	idx = lpszText.Find(",");
	red = atoi(lpszText.Left(idx));
	
	lpszText = lpszText.Right(lpszText.GetLength()-(idx+1));
	idx = lpszText.Find(",");
	green = atoi(lpszText.Left(idx));

	lpszText = lpszText.Right(lpszText.GetLength()-(idx+1));
	blue = atoi(lpszText);
	*/
	br.CreateSolidBrush(RGB(pColor->red, pColor->green, pColor->blue));
	pTempBrush = (CBrush*)dc.SelectObject(br);
	// Save original brush.
	OrigBrush.FromHandle((HBRUSH)pTempBrush);



	dc.Rectangle(r.left+1, r.top+1, 19, r.bottom-1);

	CString tmp;
	tmp.Format("%s - top: %d, left: %d, bottom: %d, right: %d",
		lpszText, r.top, r.left, r.bottom, r.right);


	//AfxMessageBox(tmp);

   COLORREF crOldTextColor = dc.GetTextColor();
   COLORREF crOldBkColor = dc.GetBkColor();

   // If this item is selected, set the background color 
   // and the text color to appropriate values. Also, erase
   // rect by filling it with the background color.
   	r.left += 25;
   if ((lpDrawItemStruct->itemAction | ODA_SELECT) &&
      (lpDrawItemStruct->itemState & ODS_SELECTED))
   {
      dc.SetTextColor(::GetSysColor(COLOR_HIGHLIGHTTEXT));
      dc.SetBkColor(::GetSysColor(COLOR_HIGHLIGHT));
      dc.FillSolidRect(&r, 
         ::GetSysColor(COLOR_HIGHLIGHT));
   }
   else {
      dc.FillSolidRect(&r, crOldBkColor);
   }


   if ((lpDrawItemStruct->itemAction | ODA_FOCUS) &&
      (lpDrawItemStruct->itemState & ODS_FOCUS))
   {
      //CBrush br(RGB(255, 0, 0));
      //dc.FrameRect(&r, &br);
   }

   r.left++;

	dc.DrawText(
      displayText,
      displayText.GetLength(),
      &r,
      DT_SINGLELINE|DT_VCENTER);

	dc.SetTextColor(crOldTextColor);
	dc.SetBkColor(crOldBkColor);

	//frontDC.SelectObject(prevPen);
	dc.SelectObject(&OrigBrush);
	dc.Detach();
}




CColorObject::CColorObject(int colorIndex, CString text)
{
	unsigned long rgbColor;

	rgbColor = acdbGetRGB(colorIndex);

	// rgbColor is 4 bytes, low byte is read, 2nd is green, 3rd is blue
	// so use bitwise and to mask the first byte and then shift 
	// it right by a byte and repeat
	red = rgbColor & 0x0ff;
	rgbColor /= 0x100;
	green = rgbColor & 0xff;
	rgbColor /= 0x100;
	blue = rgbColor & 0xff;
	this->colorIndex = colorIndex;
	this->text = text;
	this->pItem = NULL;
		
}

void CColorObject::SetColorIndex(int colorIdx)
{
	unsigned long rgbColor;

	rgbColor = acdbGetRGB(colorIdx);

	// rgbColor is 4 bytes, low byte is read, 2nd is green, 3rd is blue
	// so use bitwise and to mask the first byte and then shift 
	// it right by a byte and repeat
	red = rgbColor & 0x0ff;
	rgbColor /= 0x100;
	green = rgbColor & 0xff;
	rgbColor /= 0x100;
	blue = rgbColor & 0xff;
	this->colorIndex = colorIdx;
	this->text = text;
	this->pItem = NULL;
}

void CColorObject::SetText(const CString &text)
{
	this->text = text;

	if (text.IsEmpty()) {
		switch (colorIndex) {
		case kRed:
			this->text = "Red";
			break;
		case kBlue:
			this->text = "Blue";
			break;
		case kGreen:
			this->text = "Green";
			break;
		case kYellow:
			this->text = "Yellow";
			break;
		case kMagenta:
			this->text = "Magenta";
			break;
		case kWhite:
			this->text = "White";
			break;
		case kCyan:
			this->text = "Cyan";
			break;
		}
	}
}

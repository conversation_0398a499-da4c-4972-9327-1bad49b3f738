// ReportCommands.cpp: implementation of the CReportCommands class.
//
//////////////////////////////////////////////////////////////////////

#include "stdafx.h"
#include "modal.h"
#include "ReportCommands.h"
#include "ReportHelper.h"
#include "ControlService.h"

#ifdef _DEBUG
#undef THIS_FILE
static char THIS_FILE[]=__FILE__;
#define new DEBUG_NEW
#endif

extern CControlService controlService;

//////////////////////////////////////////////////////////////////////
// Construction/Destruction
//////////////////////////////////////////////////////////////////////

CReportCommands::CReportCommands()
{

}

CReportCommands::~CReportCommands()
{

}


void CReportCommands::RegisterCommands()
{
	acedRegCmds->addCommand( "SLOTREP", "OPENSAVEDREP", "OPENSAVEDREP",
		ACRX_CMD_MODAL, &CReportCommands::OpenSavedRep );
	acedRegCmds->addCommand( "SLOTREP", "RACKASSIGNREP", "RACKASSIGNREP",
		ACRX_CMD_MODAL, &CReportCommands::RackAssignmentRep );
	acedRegCmds->addCommand( "SLOTREP", "RACKASSIGNDETREP", "RACKASSIGNDETREP",
		ACRX_CMD_MODAL, &CReportCommands::RackAssignmentDetailRep );
	acedRegCmds->addCommand( "SLOTREP", "PRODUCTGROUPREP", "PRODUCTGROUPREP",
		ACRX_CMD_MODAL, &CReportCommands::ProductGroupDefineRep );
	acedRegCmds->addCommand( "SLOTREP", "PRODGROUPLAYOUTREP", "PRODGROUPLAYOUTREP",
		ACRX_CMD_MODAL, &CReportCommands::ProductGroupLayoutRep );
	acedRegCmds->addCommand( "SLOTREP", "PRODGROUPFACINGREP", "PRODGROUPFACINGREP",
		ACRX_CMD_MODAL, &CReportCommands::ProductGroupFacingsRep );
	acedRegCmds->addCommand( "SLOTREP", "PRODGROUPLAYOUTASSIGNREP", "PRODGROUPLAYOUTASSIGNREP",
		ACRX_CMD_MODAL, &CReportCommands::ProductsLayoutAssignmentRep );
	acedRegCmds->addCommand( "SLOTREP", "PRODGROUPLAYOUTLOCREP", "PRODGROUPLAYOUTLOCREP",
		ACRX_CMD_MODAL, &CReportCommands::ProductsLayoutVarWidthLocRep );
	acedRegCmds->addCommand( "SLOTREP", "PRODGROUPLAYOUTCASEREP", "PRODGROUPLAYOUTCASEREP",
		ACRX_CMD_MODAL, &CReportCommands::ProductsLayoutCaseReOrientRep );
	acedRegCmds->addCommand( "SLOTREP", "FACILITYMOVEREP", "FACILITYMOVEREP",
		ACRX_CMD_MODAL, &CReportCommands::FacilityMoveChainsRep );
	acedRegCmds->addCommand( "SLOTREP", "LOCATIONOUTBOUNDREP", "LOCATIONOUTBOUNDREP",
		ACRX_CMD_MODAL, &CReportCommands::LocationOutboundRep );
	acedRegCmds->addCommand( "SLOTREP", "ASSIGNMENTOUTBOUNDREP", "ASSIGNMENTOUTBOUNDREP",
		ACRX_CMD_MODAL, &CReportCommands::AssignmentOutboundRep );
	acedRegCmds->addCommand( "SLOTREP", "PRODUCTDETAILREP", "PRODUCTDETAILREP",
		ACRX_CMD_MODAL, &CReportCommands::ProductDetailRep );
	
	acedRegCmds->addCommand( "SLOTREP", "COSTANALYSISDETREP", "COSTANALYSISDETREP",
		ACRX_CMD_MODAL, &CReportCommands::CostAnalysisDetRep );
	acedRegCmds->addCommand( "SLOTREP", "COSTANALYSISSUMREP", "COSTANALYSISSUMREP",
		ACRX_CMD_MODAL, &CReportCommands::CostAnalysisSumRep );
	
	acedRegCmds->addCommand( "SLOTREP", "PRODGROUPLAYOUTBYPRODUCTREP", "PRODGROUPLAYOUTBYPRODUCTREP",
		ACRX_CMD_MODAL, &CReportCommands::ProductsLayoutAssignmentByProductRep );
	acedRegCmds->addCommand( "SLOTREP", "PRODGROUPLAYOUTBYLOCATIONREP", "PRODGROUPLAYOUTBYLOCATIONREP",
		ACRX_CMD_MODAL, &CReportCommands::ProductsLayoutAssignmentByLocationRep );
	
	acedRegCmds->addCommand( "SLOTREP", "PRODUCTGROUPREP", "PRODUCTGROUPREP",
		ACRX_CMD_MODAL, &CReportCommands::ProductGroupDefineRep );
	acedRegCmds->addCommand( "SLOTREP", "PRODUCTGROUPBYMOVEMENTREP", "PRODUCTGROUPBYMOVEMENTREP",
		ACRX_CMD_MODAL, &CReportCommands::ProductGroupDefineByMovementRep );
	acedRegCmds->addCommand( "SLOTREP", "PRODUCTGROUPBYBOHREP", "PRODUCTGROUPBYBOHREP",
		ACRX_CMD_MODAL, &CReportCommands::ProductGroupDefineByBOHRep );
	acedRegCmds->addCommand( "SLOTREP", "PRODUCTGROUPBYUOIREP", "PRODUCTGROUPBYUOIREP",
		ACRX_CMD_MODAL, &CReportCommands::ProductGroupDefineByUOIRep );
	acedRegCmds->addCommand( "SLOTREP", "RACKUSAGESUMMARYREP", "RACKUSAGESUMMARYREP",
		ACRX_CMD_MODAL, &CReportCommands::RackUsageSummaryRep );
	acedRegCmds->addCommand( "SLOTREP", "UNASSIGNEDPRODUCTSREP", "UNASSIGNEDPRODUCTSREP",
		ACRX_CMD_MODAL, &CReportCommands::UnassignedProductsRep );
	acedRegCmds->addCommand( "SLOTREP", "CAPITALCOSTREJECTIONREP", "CAPITALCOSTREJECTIONREP",
		ACRX_CMD_MODAL, &CReportCommands::CapitalCostRejectionRep );
}

void CReportCommands::OpenSavedRep() 
{
	if (! controlService.ValidateCommand(CCommands::anyMode))
		return;

	CReportHelper helper;
	helper.OpenSavedRep(); 

	return; 
}

void CReportCommands::RackAssignmentRep() 
{ 
	if (! controlService.ValidateCommand(CCommands::openFacilityMode))
		return;

	CReportHelper helper;
	helper.RackAssignmentRep();

	return; 
}

void CReportCommands::RackAssignmentDetailRep() 
{ 
	if (! controlService.ValidateCommand(CCommands::openFacilityMode))
		return;

	CReportHelper helper;
	helper.RackAssignmentDetailRep();

	return; 
}

void CReportCommands::ProductGroupDefineRep() 
{ 
	CReportHelper helper;
	helper.ProductGroupDefineRep();

	return; 
}

void CReportCommands::ProductGroupLayoutRep() 
{ 
	if (! controlService.ValidateCommand(CCommands::openFacilityMode))
		return;

	CReportHelper helper;
	helper.ProductGroupLayoutRep();

	return;	
}

void CReportCommands::ProductGroupFacingsRep() 
{ 
	if (! controlService.ValidateCommand(CCommands::openFacilityMode))
		return;

	CReportHelper helper;
	helper.ProductGroupFacingsRep();

	return; 
}

void CReportCommands::ProductsLayoutAssignmentRep() 
{ 
	if (! controlService.ValidateCommand(CCommands::openFacilityMode))
		return;

	CReportHelper helper;
	helper.ProductsLayoutAssignmentRep();

	return;	
}

void CReportCommands::ProductsLayoutVarWidthLocRep() 
{ 
	if (! controlService.ValidateCommand(CCommands::openFacilityMode))
		return;

	CReportHelper helper;
	helper.ProductsLayoutVarWidthLocRep();

	return;
}

void CReportCommands::ProductsLayoutCaseReOrientRep() 
{ 
	if (! controlService.ValidateCommand(CCommands::openFacilityMode))
		return;

	CReportHelper helper;
	helper.ProductsLayoutCaseReOrientRep();

	return;
}

void CReportCommands::FacilityMoveChainsRep() 
{ 
	if (! controlService.ValidateCommand(CCommands::openFacilityMode))
		return;

	CReportHelper helper;
	helper.FacilityMoveChainsRep();

	return;
}

void CReportCommands::LocationOutboundRep() 
{ 
	if (! controlService.ValidateCommand(CCommands::savedFacilityMode))
		return;

	CReportHelper helper;
	helper.LocationOutboundRep();

	return;	
}

void CReportCommands::AssignmentOutboundRep() 
{ 
	if (! controlService.ValidateCommand(CCommands::openFacilityMode))
		return;

	CReportHelper helper;
	helper.AssignmentOutboundRep();

	return;	
}

void CReportCommands::ProductDetailRep() 
{
	if (! controlService.ValidateCommand(CCommands::openFacilityMode))
		return;

	CReportHelper helper;
	helper.ProductDetailRep();

	return;	
}

void CReportCommands::CostAnalysisDetRep() 
{ 
	if (! controlService.ValidateCommand(CCommands::openFacilityMode))
		return;

	CReportHelper helper;
	helper.CostAnalysisDetRep();

	return;	
}

void CReportCommands::CostAnalysisSumRep() 
{ 
	if (! controlService.ValidateCommand(CCommands::openFacilityMode))
		return;

	CReportHelper helper;
	helper.CostAnalysisSumRep();

	return;
}

void CReportCommands::ProductsLayoutAssignmentByProductRep() 
{ 
	if (! controlService.ValidateCommand(CCommands::openFacilityMode))
		return;

	CReportHelper helper;
	helper.ProductsLayoutAssignmentByProductRep();

	return;	
}

void CReportCommands::ProductsLayoutAssignmentByLocationRep() 
{ 
	if (! controlService.ValidateCommand(CCommands::openFacilityMode))
		return;

	CReportHelper helper;
	helper.ProductsLayoutAssignmentByLocationRep();

	return;
}


void CReportCommands::ProductGroupDefineByMovementRep() 
{ 
	if (! controlService.ValidateCommand(CCommands::openFacilityMode))
		return;

	CReportHelper helper;
	helper.ProductGroupDefineByMovementRep();

	return;
}

void CReportCommands::ProductGroupDefineByBOHRep() 
{  
	if (! controlService.ValidateCommand(CCommands::openFacilityMode))
		return;

	CReportHelper helper;
	helper.ProductGroupDefineByBOHRep();

	return;
}

void CReportCommands::ProductGroupDefineByUOIRep() 
{ 
	if (! controlService.ValidateCommand(CCommands::openFacilityMode))
		return;
	
	CReportHelper helper;
	helper.ProductGroupDefineByUOIRep();

	return;
}

void CReportCommands::RackUsageSummaryRep() 
{ 
	if (! controlService.ValidateCommand(CCommands::openFacilityMode))
		return;
	
	CReportHelper helper;
	helper.RackUsageSummaryRep();

	return;
}

void CReportCommands::UnassignedProductsRep() 
{ 
	 
	if (! controlService.ValidateCommand(CCommands::openFacilityMode))
		return;

	CReportHelper helper;
	helper.UnassignedProductsRep();

	return;	
}

void CReportCommands::CapitalCostRejectionRep() 
{
 
	if (! controlService.ValidateCommand(CCommands::openFacilityMode))
		return;

	CReportHelper helper;
	helper.CapitalCostRejectionRep();

	return;
}

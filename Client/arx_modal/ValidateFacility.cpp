#include "stdafx.h"
#include <aced.h>
#include <gemat3d.h>
#include <adscodes.h>
#include "constants.h"

#include <dbsymtb.h>
#include <dbents.h>
#include "ValidateFacility.h"

/////////////////////////////////////////////////////////////////////////////
// CValidateFacility dialog


CValidateFacility::CValidateFacility(CWnd* pParent /*=NULL*/)
	: CDialog(CValidateFacility::IDD, pParent)
{
	//{{AFX_DATA_INIT(CValidateFacility)
	m_text = _T("");
	//}}AFX_DATA_INIT
}


void CValidateFacility::DoDataExchange(CDataExchange* pDX)
{
	CDialog::DoDataExchange(pDX);
	//{{AFX_DATA_MAP(CValidateFacility)
//	DDX_Control(pDX, IDC_LIST, m_listCtrl);
	DDX_Text(pDX, IDC_EDIT1, m_text);
	//}}AFX_DATA_MAP
}


BEGIN_MESSAGE_MAP(CValidateFacility, CDialog)
	//{{AFX_MSG_MAP(CValidateFacility)
	//}}AFX_MSG_MAP
END_MESSAGE_MAP()

/////////////////////////////////////////////////////////////////////////////
// CValidateFacility message handlers

 void CValidateFacility::OnOK() 
{
	// TODO: Add extra validation here

	CDialog::OnOK();
}



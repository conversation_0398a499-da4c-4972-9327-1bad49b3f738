// Location.h: interface for the CLocation class.
//
//////////////////////////////////////////////////////////////////////

#if !defined(AFX_LOCATION_H__135F30C6_3F59_4329_8135_DBD6C15C197B__INCLUDED_)
#define AFX_LOCATION_H__135F30C6_3F59_4329_8135_DBD6C15C197B__INCLUDED_

#if _MSC_VER > 1000
#pragma once
#endif // _MSC_VER > 1000

#include "FacilityElement.h"
#include "3DPoint.h"
#include "LevelProfileExternalInfo.h"

class CLocation : public CFacilityElement  
{
public:
	static CString ConvertStatusToText(int status);
	int Parse(const CString &line);
	CString Stream();
	void Clear();
	CLocation();
	CLocation(const CLocation& other);
	CLocation& operator=(const CLocation &other);

	virtual ~CLocation();

	C3DPoint m_Coordinates;
	int m_Usage;				// IsSelect
	int m_HandlingMethod;		// HandlingMethod

	double m_Width;
	double m_Depth;
	double m_Height;
	double m_WeightCapacity;	// MaxWeight

	BOOL m_ChangedInPass;

	int m_LocationProfileDBId;

	BOOL m_IsOverridden;

	// New fields
	BOOL m_Status;		// Status
	BOOL m_TraceEnabled;
	BOOL m_IsActive;

	double m_Clearance;

	CString m_BackfillId;
	C3DPoint m_BackfillCoordinates;
	
	CString m_StockerId;
	C3DPoint m_StockerCoordinates;

	CString m_SelectionSequence;
	CString m_ReplenishmentSequence;

	int m_LocationKey;

	CTypedPtrArray<CObArray, CLevelProfileExternalInfo*> m_InfoList;

	typedef enum {
		loReserve = 0,
		loSelect = 1,
		loStocker = 2,
		loVirtualVariableWidth = 3
	} loLocationTypeEnum;

	typedef enum {
		loCase = 2,
		loPallet = 3
	} loHandlingMethodEnum;

	typedef enum {
		loInActive = 0,
		loActive = 1
	} loLocationActiveEnum;

	typedef enum {
		NotIntegrated = 0,
		IntegrationPending = 1,
		Integrated = 2
	} loIntegrationStatusEnum;

};

#endif // !defined(AFX_LOCATION_H__135F30C6_3F59_4329_8135_DBD6C15C197B__INCLUDED_)

#if !defined(AFX_INTERFACEMAPUDFDIALOG_H__3DC11604_DEA5_4B39_A388_BDB3640BA5D7__INCLUDED_)
#define AFX_INTERFACEMAPUDFDIALOG_H__3DC11604_DEA5_4B39_A388_BDB3640BA5D7__INCLUDED_

#if _MSC_VER > 1000
#pragma once
#endif // _MSC_VER > 1000
// InterfaceMapUDFDialog.h : header file
//

/////////////////////////////////////////////////////////////////////////////
// CInterfaceMapUDFDialog dialog

class CInterfaceMapUDFDialog : public CDialog
{
// Construction
public:
	CWordArray m_ValidElementTypes;
	int m_ElementType;
	int m_DataType;
	CInterfaceMapUDFDialog(CWnd* pParent = NULL);   // standard constructor

// Dialog Data
	//{{AFX_DATA(CInterfaceMapUDFDialog)
	enum { IDD = IDD_INTERFACE_MAP_UDF };
	CComboBox	m_TypeListCtrl;
	CComboBox	m_ElementListCtrl;
	CString	m_Name;
	//}}AFX_DATA


// Overrides
	// ClassWizard generated virtual function overrides
	//{{AFX_VIRTUAL(CInterfaceMapUDFDialog)
	protected:
	virtual void DoDataExchange(CDataExchange* pDX);    // DDX/DDV support
	//}}AFX_VIRTUAL

// Implementation
protected:

	// Generated message map functions
	//{{AFX_MSG(CInterfaceMapUDFDialog)
	virtual BOOL OnInitDialog();
	virtual void OnOK();
	virtual void OnCancel();
	afx_msg void OnHelp();
	afx_msg BOOL OnHelpInfo(HELPINFO* pHelpInfo);
	afx_msg void OnSelchangeElementType();
	//}}AFX_MSG
	DECLARE_MESSAGE_MAP()
private:
	void LoadDataTypeList();
};

//{{AFX_INSERT_LOCATION}}
// Microsoft Visual C++ will insert additional declarations immediately before the previous line.

#endif // !defined(AFX_INTERFACEMAPUDFDIALOG_H__3DC11604_DEA5_4B39_A388_BDB3640BA5D7__INCLUDED_)

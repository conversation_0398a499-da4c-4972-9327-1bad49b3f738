#if !defined(AFX_HOTSPOTDIALOG_H__71EE0831_2D39_11D2_9C1B_0080C781D9DF__INCLUDED_)
#define AFX_HOTSPOTDIALOG_H__71EE0831_2D39_11D2_9C1B_0080C781D9DF__INCLUDED_
#include "resource.h"
#if _MSC_VER >= 1000
#pragma once
#endif // _MSC_VER >= 1000
// HotSpotDialog.h : header file
//

/////////////////////////////////////////////////////////////////////////////
// CHotSpotDialog dialog

class CHotSpotDialog : public CDialog
{
// Construction
public:
	int m_SectionDBId;
	int m_Type;
	CHotSpotDialog(CWnd* pParent = NULL);   // standard constructor
// Dialog Data
	//{{AFX_DATA(CHotSpotDialog)
	enum { IDD = IDD_HOTSPOT_DIALOG };
	CComboBox	m_SectionListCtrl;
	//}}AFX_DATA


// Overrides
	// ClassWizard generated virtual function overrides
	//{{AFX_VIRTUAL(CHotSpotDialog)
	protected:
	virtual void DoDataExchange(CDataExchange* pDX);    // DDX/DDV support
	//}}AFX_VIRTUAL

// Implementation
protected:

	// Generated message map functions
	//{{AFX_MSG(CHotSpotDialog)
	afx_msg void OnOK();
	virtual BOOL OnInitDialog();
	afx_msg void OnHelp();
	afx_msg BOOL OnHelpInfo(HELPINFO* pHelpInfo);
	//}}AFX_MSG
	DECLARE_MESSAGE_MAP()
public:
	afx_msg void OnCbnSelchangeSectionList();
};

//{{AFX_INSERT_LOCATION}}
// Microsoft Developer Studio will insert additional declarations immediately before the previous line.

#endif // !defined(AFX_HOTSPOTDIALOG_H__71EE0831_2D39_11D2_9C1B_0080C781D9DF__INCLUDED_)

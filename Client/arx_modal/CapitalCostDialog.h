#if !defined(AFX_CAPITALCOSTDIALOG_H__68004148_60B5_11D4_9198_00400542E36B__INCLUDED_)
#define AFX_CAPITALCOSTDIALOG_H__68004148_60B5_11D4_9198_00400542E36B__INCLUDED_

#if _MSC_VER > 1000
#pragma once
#endif // _MSC_VER > 1000
// CapitalCostDialog.h : header file
//
#include "resource.h"

/////////////////////////////////////////////////////////////////////////////
// CCapitalCostDialog dialog

class CCapitalCostDialog : public CDialog
{
// Construction
public:
	int m_SkipUsed;
	BOOL m_Advanced;
	CCapitalCostDialog(CWnd* pParent = NULL);   // standard constructor

// Dialog Data
	//{{AFX_DATA(CCapitalCostDialog)
	enum { IDD = IDD_CAPITAL_COST_DIALOG };
	CSliderCtrl	m_SliderCtrl;
	CComboBox	m_LogModeCtrl;
	BOOL	m_BreakPallet;
	BOOL	m_RankHeight;
	int		m_Ranking;
	CString	m_LogMode;
	int		m_MaxResults;
	//}}AFX_DATA


// Overrides
	// ClassWizard generated virtual function overrides
	//{{AFX_VIRTUAL(CCapitalCostDialog)
	protected:
	virtual void DoDataExchange(CDataExchange* pDX);    // DDX/DDV support
	//}}AFX_VIRTUAL

// Implementation
protected:

	// Generated message map functions
	//{{AFX_MSG(CCapitalCostDialog)
	afx_msg BOOL OnHelpInfo(HELPINFO* pHelpInfo);
	afx_msg void OnHelp();
	afx_msg void OnAdvanced();
	virtual BOOL OnInitDialog();
	virtual void OnOK();
	//}}AFX_MSG
	DECLARE_MESSAGE_MAP()
private:
	int m_BayTypeCount;
};

//{{AFX_INSERT_LOCATION}}
// Microsoft Visual C++ will insert additional declarations immediately before the previous line.

#endif // !defined(AFX_CAPITALCOSTDIALOG_H__68004148_60B5_11D4_9198_00400542E36B__INCLUDED_)

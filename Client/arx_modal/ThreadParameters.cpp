// ThreadParameters.cpp: implementation of the CThreadParameters class.
//
//////////////////////////////////////////////////////////////////////

#include "stdafx.h"
#include "modal.h"
#include "ThreadParameters.h"

#ifdef _DEBUG
#undef THIS_FILE
static char THIS_FILE[]=__FILE__;
#define new DEBUG_NEW
#endif

//////////////////////////////////////////////////////////////////////
// Construction/Destruction
//////////////////////////////////////////////////////////////////////

CThreadParameters::CThreadParameters()
{
	m_pEvent = NULL;
	m_pWnd = NULL;
	m_pInList= NULL;
	m_pOutList = NULL;
	m_pProgressCtrl = NULL;
	m_ReturnCode = 0;
	m_ReturnMessage = "";
	m_EventId = 0;
}

CThreadParameters::~CThreadParameters()
{
  //No Data is "new"ed so clean up to be done by outside source.
}

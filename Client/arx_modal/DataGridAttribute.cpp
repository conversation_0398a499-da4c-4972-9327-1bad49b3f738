// DataGridAttribute.cpp: implementation of the CDataGridAttribute class.
//
//////////////////////////////////////////////////////////////////////

#include "stdafx.h"
#include "DataGridAttribute.h"

#ifdef _DEBUG
#undef THIS_FILE
static char THIS_FILE[]=__FILE__;
#define new DEBUG_NEW
#endif

//////////////////////////////////////////////////////////////////////
// Construction/Destruction
//////////////////////////////////////////////////////////////////////

CDataGridAttribute::CDataGridAttribute()
{
	m_Type = AT_FIXED;
	m_DataType = DT_NONE;
	m_InitialValue = "";
	m_Max = 0;
	m_Min = 0;
	m_Value = "";
	m_ListValues.RemoveAll();
	m_ReadOnly = FALSE;
	m_HelpTopic = "";
	m_AttributeID = 0;
	m_Format = "";
}

CDataGridAttribute::~CDataGridAttribute()
{

}

CDataGridAttribute& CDataGridAttribute::operator=(const CDataGridAttribute & other)
{
	m_DataType = other.m_DataType;
	m_InitialValue = other.m_InitialValue;
	m_Max = other.m_Max;
	m_Min = other.m_Min;
	m_Type = other.m_Type;
	m_Value = other.m_Value;
	m_HelpTopic = other.m_HelpTopic;
	m_AttributeID = other.m_AttributeID;
	m_Format = other.m_Format;

	for (int i=0; i < other.m_ListValues.GetSize(); ++i) {
		m_ListValues.Add(other.m_ListValues[i]);
	}

	return *this;
}

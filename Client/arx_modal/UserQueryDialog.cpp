// UserQueryDialog.cpp : implementation file
//

#include "stdafx.h"
#include "modal.h"
#include "UserQueryDialog.h"
#include "DisplayResults.h"
#include "ssa_exception.h"
#include "Prompt.h"
#include "TreeElement.h"
#include "HelpService.h"
#include "UtilityHelper.h"

#include <dbsymtb.h>

#ifdef _DEBUG
#define new DEBUG_NEW
#undef THIS_FILE
static char THIS_FILE[] = __FILE__;
#endif

/////////////////////////////////////////////////////////////////////////////
// CUserQueryDialog dialog

extern CHelpService helpService;
extern CUtilityHelper utilityHelper;
extern TreeElement changesTree;

CUserQueryDialog::CUserQueryDialog(CWnd* pParent /*=NULL*/)
	: CDialog(CUserQueryDialog::IDD, pParent)
{
	//{{AFX_DATA_INIT(CUserQueryDialog)
		// NOTE: the ClassWizard will add member initialization here
	//}}AFX_DATA_INIT
	m_QueryCount = 0;
	m_QueryArray = NULL;
}


void CUserQueryDialog::DoDataExchange(CDataExchange* pDX)
{
	CDialog::DoDataExchange(pDX);
	//{{AFX_DATA_MAP(CUserQueryDialog)
	DDX_Control(pDX, IDC_QUERY_LIST, m_QueryListBox);
	//}}AFX_DATA_MAP
}


BEGIN_MESSAGE_MAP(CUserQueryDialog, CDialog)
	//{{AFX_MSG_MAP(CUserQueryDialog)
	ON_CBN_SELCHANGE(IDC_QUERY_LIST, OnSelchangeQueryList)
	ON_BN_CLICKED(IDRUN, OnRun)
	ON_WM_RBUTTONDBLCLK()
	ON_BN_CLICKED(IDUPDATE, OnUpdate)
	ON_CBN_EDITCHANGE(IDC_QUERY_LIST, OnEditchangeQueryList)
	ON_BN_CLICKED(IDEXPORT, OnExport)
	ON_BN_CLICKED(IDIMPORT, OnImport)
	ON_BN_CLICKED(IDDELETE, OnDelete)
	ON_WM_HELPINFO()
	ON_BN_CLICKED(IDHELP, OnHelp)
	//}}AFX_MSG_MAP
END_MESSAGE_MAP()

/////////////////////////////////////////////////////////////////////////////
// CUserQueryDialog message handlers

BOOL CUserQueryDialog::OnInitDialog() 
{
	CDialog::OnInitDialog();
	
	int rc;
	CStringArray queryList;
	CString s;

	m_SuperUser = FALSE;

	try {
		rc = m_UserQueryDataService.GetUserQueryList(queryList);
	}
	catch(Ssa_Exception e) {
		utilityHelper.ProcessError("Error getting list of queries.", &e);
		EndDialog(IDCANCEL);
	}
	catch(...) {
		utilityHelper.ProcessError("Error getting list of queries.");
		EndDialog(IDCANCEL);
	}

	m_QueryCount = queryList.GetSize();
	m_QueryArray = new CUserQuery[m_QueryCount];

	for (int i=0; i < m_QueryCount; ++i) {
		s = queryList[i];
		m_QueryArray[i].Parse(s);
		m_QueryArray[i].m_Query.Replace("<nl>", "\r\n");
		m_QueryListBox.AddString(m_QueryArray[i].m_Name);
	}
	
	CRect r;
	m_QueryListBox.SetItemHeight(0, 4000);
	m_QueryListBox.GetWindowRect(&r);
	m_QueryListBox.SetWindowPos(NULL, 0, 0, r.Width(), r.Height()*5, SWP_NOMOVE|SWP_NOZORDER);

	CEdit *pEdit = (CEdit *)GetDlgItem(IDC_DATABASE);
	pEdit->SetWindowText("Oracle");

	UpdateData(TRUE);



	return TRUE;  // return TRUE unless you set the focus to a control
	              // EXCEPTION: OCX Property Pages should return FALSE
}

void CUserQueryDialog::OnSelchangeQueryList() 
{
	CString desc;
	int idx;
	CEdit *pDescription = (CEdit *)GetDlgItem(IDC_DESCRIPTION);
	CEdit *pQuery = (CEdit *)GetDlgItem(IDC_QUERY);
	CEdit *pDatabase = (CEdit *)GetDlgItem(IDC_DATABASE);

	idx = m_QueryListBox.GetCurSel();

	if (idx == CB_ERR) {
		pDescription->SetWindowText("");
		pQuery->SetWindowText("");
		pDatabase->SetWindowText("Oracle");
	}
	else {
		pDescription->SetWindowText(m_QueryArray[idx].m_Description);
		pQuery->SetWindowText(m_QueryArray[idx].m_Query);
		pDatabase->SetWindowText(m_QueryArray[idx].m_Database);
	}

	UpdateData(FALSE);	
}


CUserQueryDialog::~CUserQueryDialog()
{
	if (m_QueryCount > 0)
		delete [] m_QueryArray;
}

void CUserQueryDialog::OnRun() 
{
	CDisplayResults dlg;
	int rc;
	int qType = 0;
	CSsaStringArray results;
	CString temp, header;
	CString query, name;
	CUserQuery q;
	CEdit *pName = (CEdit *)GetDlgItem(IDC_QUERY_LIST);
	CEdit *pQuery = (CEdit *)GetDlgItem(IDC_QUERY);
	
	pName->GetWindowText(name);
	pQuery->GetWindowText(query);

	if (query == "") {
		AfxMessageBox("No query to run");
		return;
	}

	if (GetParameters(query) < 0)
		return;

	query.MakeLower();
	query.TrimLeft();

	if (query.GetAt(0) == 's')
		qType = 1;
	
	if (qType == 0)
	{
		try {
			CWaitCursor cwc;
			temp = query;
			temp.Replace("\r\n", " ");
			AfxMessageBox(temp);
			rc = m_UserQueryDataService.RunUserStatement(name,temp);
			if (rc != 0)
				AfxMessageBox("Error in query.");
			
		}
		catch(Ssa_Exception e) {
			utilityHelper.ProcessError("Error running query - .", &e);
			return;
		}
		catch(...) {
			utilityHelper.ProcessError("Error running query.");
			return;
		}
		
	}
	else
	{
		try {
			CWaitCursor cwc;
			temp = query;
			temp.Replace("\r\n", " ");
			rc = m_UserQueryDataService.RunUserQuery(temp, results);
			
		}
		catch(Ssa_Exception e) {
			utilityHelper.ProcessError("Error running query.", &e);
			return;
		}
		catch(...) {
			utilityHelper.ProcessError("Error running query.");
			return;
		}
		
		
		dlg.m_Tabs.Add(name);
		dlg.m_WindowCaptions.Add(name);
		
		if (results.GetSize() == 0) {
			dlg.m_Headers.Add("");
		}
		else {
			dlg.m_Headers.Add(results[0]);
			results.RemoveAt(0);
		}
		
		dlg.m_Data = results;
		dlg.m_OrigColumnSize = 100;
		try {
			int rc = dlg.DoModal();
		}
		catch (...) {
			AfxMessageBox("Error displaying query results.");
		}
	}

}

void CUserQueryDialog::OnRButtonDblClk(UINT nFlags, CPoint point) 
{
	
	if (nFlags & MK_CONTROL) {
		//AfxMessageBox("Control Down");
	}
	else {
		//AfxMessageBox("Control Not Down");
		return;
	}

	(CStatic *)GetDlgItem(IDC_STATIC_QUERY)->ShowWindow(SW_SHOW);
	(CStatic *)GetDlgItem(IDC_STATIC_DATABASE)->ShowWindow(SW_SHOW);
	(CEdit *)GetDlgItem(IDC_QUERY)->ShowWindow(SW_SHOW);
	(CEdit *)GetDlgItem(IDC_DATABASE)->ShowWindow(SW_SHOW);
	(CButton *)GetDlgItem(IDEXPORT)->ShowWindow(SW_SHOW);

	m_SuperUser = TRUE;

	CDialog::OnRButtonDblClk(nFlags, point);
}

void CUserQueryDialog::OnUpdate() 
{
	int idx, rc;
	BOOL bNew = FALSE;

	CUserQuery q;
	CEdit *pDescription = (CEdit *)GetDlgItem(IDC_DESCRIPTION);
	CEdit *pQuery = (CEdit *)GetDlgItem(IDC_QUERY);
	CEdit *pDatabase = (CEdit *)GetDlgItem(IDC_DATABASE);

	idx = m_QueryListBox.GetCurSel();
	if (idx == CB_ERR) {
		CString tmp;
		m_QueryListBox.GetWindowText(tmp);
		idx = m_QueryListBox.FindStringExact(0, tmp);
	}

	if (idx == CB_ERR) {		// New entry
		bNew = TRUE;
		if (! m_SuperUser) {
			AfxMessageBox("Unable to create query.  Select a query to update.");
			return;
		}
		q.m_QueryID = 0;
		m_QueryListBox.GetWindowText(q.m_Name);
		pDescription->GetWindowText(q.m_Description);
		pQuery->GetWindowText(q.m_Query);
		pDatabase->GetWindowText(q.m_Database);
	}
	else {
		q = m_QueryArray[idx];
		pDescription->GetWindowText(q.m_Description);
		if (m_SuperUser) {
			pQuery->GetWindowText(q.m_Query);
			pDatabase->GetWindowText(q.m_Database);
		}
	}

	q.m_Query.Replace("\r\n", "<nl>");
	q.m_Query.Replace("'", "''");

	try {
		rc = m_UserQueryDataService.StoreUserQuery(q);
		if (rc < 0) {
			AfxMessageBox("Unable to update query.");
			return;
		}

		if (bNew) {
			q.m_QueryID = rc;
			// brd - this is weird; I should have used an obarray or something
			if (m_QueryCount > 0) {
				CUserQuery *tempQueryArray;
				tempQueryArray = new CUserQuery[m_QueryCount];
				for (int i=0; i < m_QueryCount; ++i) {
					tempQueryArray[i] = m_QueryArray[i];
				}
				delete [] m_QueryArray;
				m_QueryArray = new CUserQuery[m_QueryCount+1];
				for (i=0; i < m_QueryCount; ++i) {
					m_QueryArray[i] = tempQueryArray[i];
				}
				delete [] tempQueryArray;
			}
			else {
				m_QueryArray = new CUserQuery[1];
			}
			q.m_Query.Replace("<nl>", "\r\n");
			q.m_Query.Replace("''", "'");
			m_QueryArray[m_QueryCount] = q;
			m_QueryCount++;
			m_QueryListBox.AddString(q.m_Name);
			m_QueryListBox.SetCurSel(m_QueryCount-1);
		}
		else {
			q.m_Query.Replace("<nl>", "\r\n");
			q.m_Query.Replace("''", "'");
			m_QueryArray[idx].m_Description = q.m_Description;
			m_QueryArray[idx].m_Query = q.m_Query;
			m_QueryArray[idx].m_Database = q.m_Database;
		}
		UpdateData(FALSE);

	}
	catch(Ssa_Exception e) {
		utilityHelper.ProcessError("Error storing query.", &e);
		return;
	}
	catch(...) {
		utilityHelper.ProcessError("Error storing query.");
		return;
	}

	AfxMessageBox("Query updated.");

	return;

}


void CUserQueryDialog::OnEditchangeQueryList() 
{
	CString name;
	int i;
	CEdit *pDescription = (CEdit *)GetDlgItem(IDC_DESCRIPTION);
	CEdit *pQuery = (CEdit *)GetDlgItem(IDC_QUERY);
	CEdit *pDatabase = (CEdit *)GetDlgItem(IDC_DATABASE);
	BOOL bFound = FALSE;

	m_QueryListBox.GetWindowText(name);
	for (i=0; i < m_QueryCount; ++i) {
		if (m_QueryArray[i].m_Name == name) {
			m_QueryListBox.SetCurSel(i);
			pDescription->SetWindowText(m_QueryArray[i].m_Description);
			pQuery->SetWindowText(m_QueryArray[i].m_Query);
			pDatabase->SetWindowText(m_QueryArray[i].m_Database);
			bFound = TRUE;
			break;
		}
	}
	
	if (! bFound) {
	//	pDescription->SetWindowText("");
	//	pQuery->SetWindowText("");
	//	pDatabase->SetWindowText("");
	}

	UpdateData(FALSE);		
}

void CUserQueryDialog::OnExport() 
{
	CUserQuery q;
	CArchive *pArchive;
	CFile archiveFile;
	CFileDialog dlgFile(FALSE);
	CString fileName;
	CString title;
	CString strFilter;
	CString strDefault;
	CString allFilter;
	CString temp;

	UpdateData(TRUE);

	CComboBox *pName = (CComboBox *)GetDlgItem(IDC_QUERY_LIST);
	CEdit *pDescription = (CEdit *)GetDlgItem(IDC_DESCRIPTION);
	CEdit *pQuery = (CEdit *)GetDlgItem(IDC_QUERY);
	CEdit *pDatabase = (CEdit *)GetDlgItem(IDC_DATABASE);

	pName->GetWindowText(q.m_Name);
	pDescription->GetWindowText(q.m_Description);
	pQuery->GetWindowText(q.m_Query);
	pDatabase->GetWindowText(q.m_Database);

	q.m_Query.Replace("\r\n", "<nl>");

	VERIFY(allFilter.LoadString(AFX_IDS_ALLFILTER));
	strFilter += CString("Optimize Query Files (*.qry)");
	strFilter += (TCHAR)'\0';   
	strFilter += _T("*.qry");
	strFilter += (TCHAR)'\0';
	strFilter += allFilter;
	strFilter += (TCHAR)'\0';   
	strFilter += _T("*.*");
	strFilter += (TCHAR)'\0';   

	dlgFile.m_ofn.lpstrFilter = strFilter;
	dlgFile.m_ofn.lpstrDefExt = "qry";
	dlgFile.m_ofn.lpstrTitle = "Export Query";
	dlgFile.m_ofn.lpstrFile = fileName.GetBuffer(_MAX_PATH);
	BOOL bResult = dlgFile.DoModal() == IDOK ? TRUE : FALSE;

	
	fileName.ReleaseBuffer();
	
	if (! bResult)
		return;

	if (! archiveFile.Open(fileName, CFile::modeCreate|CFile::modeWrite)) {
		CString msg;
		msg = "Error opening file: ";
		msg += fileName;
		AfxMessageBox(msg);
		return;
	}

	if (fileName.Find(".txt", fileName.GetLength()-4) > 0 ) {
		// text export
		temp.Format("%s\n%s\n%s\n%s\n", 
			q.m_Name, q.m_Description, q.m_Query, q.m_Database);
		archiveFile.Write(temp, temp.GetLength());
	}
	else {
		pArchive = new CArchive(&archiveFile, CArchive::store);
		q.Serialize(*(pArchive));
		delete pArchive;
	}
	archiveFile.Close();
	AfxMessageBox("Query exported.");

}

void CUserQueryDialog::OnImport() 
{
	CUserQuery q;
	CArchive *pArchive;
	CFile archiveFile;
	CFileDialog dlgFile(TRUE);
	CString fileName;
	CString title;
	CString strFilter;
	CString strDefault;
	CString allFilter;
	CString temp;

	CComboBox *pName = (CComboBox *)GetDlgItem(IDC_QUERY_LIST);
	CEdit *pDescription = (CEdit *)GetDlgItem(IDC_DESCRIPTION);
	CEdit *pQuery = (CEdit *)GetDlgItem(IDC_QUERY);
	CEdit *pDatabase = (CEdit *)GetDlgItem(IDC_DATABASE);

	VERIFY(allFilter.LoadString(AFX_IDS_ALLFILTER));
	strFilter += CString("Optimize Query Files (*.qry)");
	strFilter += (TCHAR)'\0';   
	strFilter += _T("*.qry");
	strFilter += (TCHAR)'\0';
	strFilter += allFilter;
	strFilter += (TCHAR)'\0';   
	strFilter += _T("*.*");
	strFilter += (TCHAR)'\0';   

	dlgFile.m_ofn.lpstrFilter = strFilter;
	dlgFile.m_ofn.lpstrDefExt = "qry";
	dlgFile.m_ofn.lpstrTitle = "Import Query";
	dlgFile.m_ofn.lpstrFile = fileName.GetBuffer(_MAX_PATH);
	BOOL bResult = dlgFile.DoModal() == IDOK ? TRUE : FALSE;

	
	fileName.ReleaseBuffer();
	
	if (! bResult)
		return;



	if (fileName.Find(".txt", fileName.GetLength()-4) > 0 ) {
		// text export
		//int i = archiveFile.GetLength();
		//archiveFile.Read(temp.GetBuffer(0), archiveFile.GetLength());
		//temp.ReleaseBuffer();
		/*
		FILE *f = fopen(fileName, "r");
		if (f == NULL) {
			AfxMessageBox("Can not read file");
			return;
		}

		CStringArray s;
		char x[1000];
		while (fgets(x, 1000, f) != NULL) {
			s.Add(x);
		}

		q.m_Name = s[0];
		q.m_Description = s[1];
		q.m_Query = s[2];
		q.m_Database = s[3];
		fclose(f);
		*/
		CStringArray s;
		CStdioFile f;
		CString temp;
		f.Open(fileName, CFile::modeRead|CFile::typeText);
		while (f.ReadString(temp)) {
			s.Add(temp);
		}
		q.m_Name = s[0];
		q.m_Description = s[1];
		q.m_Query = s[2];
		q.m_Database = s[3];
		f.Close();

	}
	else {

		if (! archiveFile.Open(fileName, CFile::modeRead)) {
			CString msg;
			msg = "Error opening file: ";
			msg += fileName;
			AfxMessageBox(msg);
			return;
		}
		pArchive = new CArchive(&archiveFile, CArchive::load);
		q.Serialize(*(pArchive));
		delete pArchive;
	}


	q.m_QueryID = 0;

	int rc;

	try {
		rc = m_UserQueryDataService.StoreUserQuery(q);
		if (rc < 0) {
			AfxMessageBox("Unable to save query to database.");
			return;
		}
	}
	catch (...) {
		AfxMessageBox("Unable to save query to database.");
		return;
	}

	q.m_QueryID = rc;
	// brd - this is weird; I should have used an obarray or something
	if (m_QueryCount > 0) {
		CUserQuery *tempQueryArray;
		tempQueryArray = new CUserQuery[m_QueryCount];
		for (int i=0; i < m_QueryCount; ++i) {
			tempQueryArray[i] = m_QueryArray[i];
		}
		delete [] m_QueryArray;
		m_QueryArray = new CUserQuery[m_QueryCount+1];
		for (i=0; i < m_QueryCount; ++i) {
			m_QueryArray[i] = tempQueryArray[i];
		}
		delete [] tempQueryArray;
	}
	else {
		m_QueryArray = new CUserQuery[1];
	}
	q.m_Query.Replace("<nl>", "\r\n");
	q.m_Query.Replace("''", "'");
	m_QueryArray[m_QueryCount] = q;
	m_QueryCount++;
	m_QueryListBox.AddString(q.m_Name);
	m_QueryListBox.SetCurSel(m_QueryCount-1);

	pDescription->SetWindowText(q.m_Description);
	pQuery->SetWindowText(q.m_Query);
	pDatabase->SetWindowText(q.m_Database);

	AfxMessageBox("Query imported.");
	
}

int CUserQueryDialog::GetParameters(CString &query)
{
	int idx, idx2, i, rc;
	CStringArray parmList;
	CString parmName, parmValue, tempQuery;
	CPrompt dlg;

	tempQuery = query;
	idx = tempQuery.Find(":");

	while (idx >= 0) {
		tempQuery = tempQuery.Right(tempQuery.GetLength()-(idx+1));
		idx2 = tempQuery.FindOneOf("	'\"\r\n");
		if (idx2 < 0)	// must be at end of line
			idx2 = tempQuery.GetLength();

		parmName = tempQuery.Left(idx2);
		parmList.Add(parmName);
		idx = tempQuery.Find(":");
	}

	for (i=0; i < parmList.GetSize(); ++i) {
		parmName = parmList[i];
		if (parmName.CompareNoCase("CurrentFacility") == 0)
			parmValue.Format("%d", changesTree.elementDBID);
		else {
			//parmName.Delete(parmName[0]);	// delete the colon
			dlg.m_ParameterName = parmName;
			dlg.m_ParameterValue = "";
			
			rc = dlg.DoModal();
			if (rc == IDCANCEL)
				return -1;
			parmValue = dlg.m_ParameterValue;
		}
		parmName.Format(":%s", parmName);
		query.Replace(parmName, parmValue);
	}

	if (m_SuperUser)
		AfxMessageBox(query);


	return 0;

}

void CUserQueryDialog::OnDelete() 
{
	int idx, rc;
	
	CEdit *pDescription = (CEdit *)GetDlgItem(IDC_DESCRIPTION);
	CEdit *pQuery = (CEdit *)GetDlgItem(IDC_QUERY);
	CEdit *pDatabase = (CEdit *)GetDlgItem(IDC_DATABASE);

	CUserQuery q;

	idx = m_QueryListBox.GetCurSel();
	if (idx == CB_ERR) {		// New entry
		AfxMessageBox("Please select a query to delete.");
		return;
	}

	q = m_QueryArray[idx];

	try {
		rc = m_UserQueryDataService.DeleteUserQuery(q.m_QueryID);
		if (rc < 0) {
			AfxMessageBox("Unable to delete query.");
			return;
		}

		if (m_QueryCount > 1) {
			CUserQuery *tempQueryArray;
				
			tempQueryArray = new CUserQuery[m_QueryCount-1];
			int j = 0;
			for (int i=0; i < m_QueryCount; ++i) {
				if (i != idx) {
					tempQueryArray[j] = m_QueryArray[i];
					j++;
				}
			}
			delete [] m_QueryArray;
			m_QueryArray = tempQueryArray;
			m_QueryCount--;
		}
		else {
			delete [] m_QueryArray;
			m_QueryCount = 0;
		}

		m_QueryListBox.SetCurSel(CB_ERR);
		m_QueryListBox.DeleteString(idx);
		pDescription->SetWindowText("");
		pQuery->SetWindowText("");
		pDatabase->SetWindowText("");
		UpdateData(FALSE);

	}
	catch(Ssa_Exception e) {
		utilityHelper.ProcessError("Error storing query.", &e);
		return;
	}
	catch(...) {
		utilityHelper.ProcessError("Error storing query.");
		return;
	}

	AfxMessageBox("Query deleted.");

	return;
}

BOOL CUserQueryDialog::OnHelpInfo(HELPINFO* pHelpInfo) 
{
	helpService.ShowFieldHelp(IDD, pHelpInfo->iCtrlId);

	return FALSE;
}

void CUserQueryDialog::OnHelp() 
{
	helpService.ShowScreenHelp(IDD);

	return;
}

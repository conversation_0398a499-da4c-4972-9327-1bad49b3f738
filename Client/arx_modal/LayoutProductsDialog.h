#if !defined(AFX_LAYOUTPRODUCTSDIALOG_H__AE8C9120_5D89_11D4_9193_00400542E36B__INCLUDED_)
#define AFX_LAYOUTPRODUCTSDIALOG_H__AE8C9120_5D89_11D4_9193_00400542E36B__INCLUDED_

#if _MSC_VER > 1000
#pragma once
#endif // _MSC_VER > 1000
// LayoutProductsDialog.h : header file
//
#include "resource.h"
/////////////////////////////////////////////////////////////////////////////
// CLayoutProductsDialog dialog

class CLayoutProductsDialog : public CDialog
{
// Construction
public:
	CLayoutProductsDialog(CWnd* pParent = NULL);   // standard constructor

// Dialog Data
	//{{AFX_DATA(CLayoutProductsDialog)
	enum { IDD = IDD_LAYOUT_PRODUCTS };
	float	m_ConstrainAmount;
	int		m_ConstrainType;
	int		m_LayoutType;
	BOOL	m_Overlap;
	BOOL	m_Reorient;
	BOOL	m_VarWidth;
	//}}AFX_DATA


// Overrides
	// ClassWizard generated virtual function overrides
	//{{AFX_VIRTUAL(CLayoutProductsDialog)
	protected:
	virtual void DoDataExchange(CDataExchange* pDX);    // DDX/DDV support
	//}}AFX_VIRTUAL

// Implementation
protected:

	// Generated message map functions
	//{{AFX_MSG(CLayoutProductsDialog)
	afx_msg void OnSelchangeLayoutType();
	virtual BOOL OnInitDialog();
	virtual void OnOK();
	afx_msg void OnHelp();
	afx_msg BOOL OnHelpInfo(HELPINFO* pHelpInfo);
	//}}AFX_MSG
	DECLARE_MESSAGE_MAP()
};

//{{AFX_INSERT_LOCATION}}
// Microsoft Visual C++ will insert additional declarations immediately before the previous line.

#endif // !defined(AFX_LAYOUTPRODUCTSDIALOG_H__AE8C9120_5D89_11D4_9193_00400542E36B__INCLUDED_)

// SideProfileSheet.cpp : implementation file
//

#include "stdafx.h"
#include "modal.h"
#include "SideProfileSheet.h"

#ifdef _DEBUG
#define new DEBUG_NEW
#undef THIS_FILE
static char THIS_FILE[] = __FILE__;
#endif

/////////////////////////////////////////////////////////////////////////////
// CSideProfileSheet

IMPLEMENT_DYNAMIC(CSideProfileSheet, CPropertySheet)

CSideProfileSheet::CSideProfileSheet(UINT nIDCaption, CWnd* pParentWnd, UINT iSelectPage)
	:CPropertySheet(nIDCaption, pParentWnd, iSelectPage)
{
}

CSideProfileSheet::CSideProfileSheet(LPCTSTR pszCaption, CWnd* pParentWnd, UINT iSelectPage)
	:CPropertySheet(pszCaption, pParentWnd, iSelectPage)
{
	AddPage(&m_AttributesPage);
	AddPage(&m_BayPage);
}

CSideProfileSheet::~CSideProfileSheet()
{
}


BEGIN_MESSAGE_MAP(CSideProfileSheet, CPropertySheet)
	//{{AFX_MSG_MAP(CSideProfileSheet)
	ON_WM_HELPINFO()
	//}}AFX_MSG_MAP
END_MESSAGE_MAP()

/////////////////////////////////////////////////////////////////////////////
// CSideProfileSheet message handlers

BOOL CSideProfileSheet::OnNotify(WPARAM wParam, LPARAM lParam, LRESULT* pResult) 
{
	// TODO: Add your specialized code here and/or call the base class
	
	return CPropertySheet::OnNotify(wParam, lParam, pResult);
}

BOOL CSideProfileSheet::OnHelpInfo(HELPINFO* pHelpInfo) 
{
	// TODO: Add your message handler code here and/or call default
	
	return CPropertySheet::OnHelpInfo(pHelpInfo);
}

#if !defined(AFX_LEVELLOCATIONMAINT_H__CF8F8D01_F8C8_11D2_9CE4_0080C742D9DF__INCLUDED_)
#define AFX_LEVELLOCATIONMAINT_H__CF8F8D01_F8C8_11D2_9CE4_0080C742D9DF__INCLUDED_

#include "resource.h"
#if _MSC_VER >= 1000
#pragma once
#endif // _MSC_VER >= 1000
// LevelLocationMaint.h : header file
//
#include <afxtempl.h>

class CLevelLocationInfo
{
public:
	CString m_levelDescription;
	int m_levelFileOffset;
	CString m_locationDescription;
	int m_locationFileOffset;

	CLevelLocationInfo()
	{
		m_levelDescription    = "";
		m_levelFileOffset     = -1;
		m_locationDescription = "";
		m_locationFileOffset  = -1;
	}

	CLevelLocationInfo(CString eD, int eF, CString oD, int oF )
	{ 
		m_levelDescription    = eD;
		m_levelFileOffset     = eF;
		m_locationDescription = oD;
		m_locationFileOffset  = oF;
	}

	CLevelLocationInfo(const CLevelLocationInfo& c) 
	{
		m_levelDescription    = c.m_levelDescription;
		m_levelFileOffset     = c.m_levelFileOffset;
		m_locationDescription = c.m_locationDescription;
		m_locationFileOffset  = c.m_locationFileOffset;
	}

	CLevelLocationInfo& operator= (const CLevelLocationInfo& c) 
	{ 
		m_levelDescription    = c.m_levelDescription;
		m_levelFileOffset     = c.m_levelFileOffset;
		m_locationDescription = c.m_locationDescription;
		m_locationFileOffset  = c.m_locationFileOffset;
		return *this;
	}
	virtual ~CLevelLocationInfo() {return;}

};

/////////////////////////////////////////////////////////////////////////////
// CLevelLocationMaint dialog

class CLevelLocationMaint : public CDialog
{
// Construction
public:
	CLevelLocationMaint(CWnd* pParent = NULL);   // standard constructor
	void ParseInfo(CString s, CLevelLocationInfo &c);
// Dialog Data
	//{{AFX_DATA(CLevelLocationMaint)
	enum { IDD = IDD_LEVLOCMAINT_DLG };
	CTreeCtrl	m_ctlLevelLocTree;
	//}}AFX_DATA

	CString m_selectedDescription;
	int		m_selectedFileOffset;
	CString m_selectedType;
	CStringArray m_levelLocationList;
	CImageList   m_ImageList;

// Overrides
	// ClassWizard generated virtual function overrides
	//{{AFX_VIRTUAL(CLevelLocationMaint)
	protected:
	virtual void DoDataExchange(CDataExchange* pDX);    // DDX/DDV support
	//}}AFX_VIRTUAL

// Implementation
protected:

	// Generated message map functions
	//{{AFX_MSG(CLevelLocationMaint)
	virtual void OnCancel();
	afx_msg void OnChooselevellochelp();
	virtual void OnOK();
	afx_msg BOOL OnHelpInfo(HELPINFO* pHelpInfo);
	virtual BOOL OnInitDialog();
	afx_msg void OnDblclkLevellocTree(NMHDR* pNMHDR, LRESULT* pResult);
	//}}AFX_MSG
	DECLARE_MESSAGE_MAP()
};

//{{AFX_INSERT_LOCATION}}
// Microsoft Developer Studio will insert additional declarations immediately before the previous line.

#endif // !defined(AFX_LEVELLOCATIONMAINT_H__CF8F8D01_F8C8_11D2_9CE4_0080C742D9DF__INCLUDED_)

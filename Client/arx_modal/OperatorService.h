// OperatorService.h: interface for the COperatorService class.
//
//////////////////////////////////////////////////////////////////////

#if !defined(AFX_OPERATORSERVICE_H__C2D63EB6_2840_11D5_9ECA_00C04FAC149C__INCLUDED_)
#define AFX_OPERATORSERVICE_H__C2D63EB6_2840_11D5_9ECA_00C04FAC149C__INCLUDED_

#if _MSC_VER > 1000
#pragma once
#endif // _MSC_VER > 1000

#include "Operator.h"

class COperatorService : public CObject  
{
public:
	COperatorService();
	virtual ~COperatorService();

	CTypedPtrArray<CObArray, COperator*> m_OperatorList;
	CTypedPtrArray<CObArray, COperator*> m_OperatorDisplayList;

	COperator* ConvertInternalToDisplay(const CString &internal);
	COperator* ConvertDisplayToInternal(const CString &display);

private:
	CMapStringToOb m_InternalToDisplayMap;
	CMapStringToOb m_DisplayToInternalMap;

};

#endif // !defined(AFX_OPERATORSERVICE_H__C2D63EB6_2840_11D5_9ECA_00C04FAC149C__INCLUDED_)

// LevelProfileExternalInfo.cpp: implementation of the CLevelProfileExternalInfo class.
//
//////////////////////////////////////////////////////////////////////

#include "stdafx.h"
#include "modal.h"
#include "LevelProfileExternalInfo.h"
#include "UtilityHelper.h"

#ifdef _DEBUG
#undef THIS_FILE
static char THIS_FILE[]=__FILE__;
#define new DEBUG_NEW
#endif

extern CUtilityHelper utilityHelper;
//////////////////////////////////////////////////////////////////////
// Construction/Destruction
//////////////////////////////////////////////////////////////////////

CLevelProfileExternalInfo::CLevelProfileExternalInfo()
{
	m_LevelProfileInfoDBId = 0;
	m_LevelProfileDBId = 0;
	m_ExternalInfoDBId = 0;
	m_Sequence = 0;
}

CLevelProfileExternalInfo::~CLevelProfileExternalInfo()
{

}

CLevelProfileExternalInfo::CLevelProfileExternalInfo(const CLevelProfileExternalInfo& other)
{
	m_LevelProfileInfoDBId = other.m_LevelProfileInfoDBId;
	m_Value = other.m_Value;
	m_LevelProfileDBId = other.m_LevelProfileDBId;
	m_ExternalInfoDBId = other.m_ExternalInfoDBId;
	m_Name = other.m_Name;
	m_DataType = other.m_DataType;
	m_Length = other.m_Length;
	m_DefaultValue = other.m_DefaultValue;
	m_ExternalSystemDBId = other.m_ExternalSystemDBId;
	m_Sequence = other.m_Sequence;

	m_ListValues.RemoveAll();
	m_ListValues.Copy(other.m_ListValues);
	
	m_ListDisplayValues.RemoveAll();
	m_ListDisplayValues.Copy(other.m_ListDisplayValues);

}

CLevelProfileExternalInfo& CLevelProfileExternalInfo::operator=(const CLevelProfileExternalInfo &other)
{
	m_LevelProfileInfoDBId = other.m_LevelProfileInfoDBId;
	m_Value = other.m_Value;
	m_LevelProfileDBId = other.m_LevelProfileDBId;
	m_ExternalInfoDBId = other.m_ExternalInfoDBId;
	m_Name = other.m_Name;
	m_DataType = other.m_DataType;
	m_Length = other.m_Length;
	m_DefaultValue = other.m_DefaultValue;
	m_ExternalSystemDBId = other.m_ExternalSystemDBId;
	m_Sequence = other.m_Sequence;

	m_ListValues.RemoveAll();
	m_ListValues.Copy(other.m_ListValues)
		;
	m_ListDisplayValues.RemoveAll();
	m_ListDisplayValues.Copy(other.m_ListDisplayValues);

	return *this;
}

BOOL CLevelProfileExternalInfo::operator==(const CLevelProfileExternalInfo& other)
{
	if (m_LevelProfileInfoDBId != other.m_LevelProfileInfoDBId) return FALSE;
	if (m_Value != other.m_Value) return FALSE;
	if (m_LevelProfileDBId != other.m_LevelProfileDBId) return FALSE;
	if (m_ExternalInfoDBId != other.m_ExternalInfoDBId) return FALSE;
	if (m_Name != other.m_Name) return FALSE;
	if (m_DataType != other.m_DataType) return FALSE;
	if (m_Length != other.m_Length) return FALSE;
	if (m_DefaultValue != other.m_DefaultValue) return FALSE;
	if (m_ExternalSystemDBId != other.m_ExternalSystemDBId) return FALSE;
	if (m_Sequence != other.m_Sequence) return FALSE;

	if (m_ListValues.GetSize() != other.m_ListValues.GetSize()) return FALSE;
	for (int i=0; i < m_ListValues.GetSize(); ++i) {
		if (m_ListValues[i] != other.m_ListValues[i])
			return FALSE;
	}

	if (m_ListDisplayValues.GetSize() != other.m_ListDisplayValues.GetSize()) return FALSE;
	for (i=0; i < m_ListDisplayValues.GetSize(); ++i) {
		if (m_ListDisplayValues[i] != other.m_ListDisplayValues[i])
			return FALSE;
	}

	return TRUE;

}

int CLevelProfileExternalInfo::Parse(const CString &line)
{
	CStringArray strings;
	CString listValues;

	utilityHelper.ParseString(line, "|", strings);

	for (int i=0; i < strings.GetSize(); ++i) {
		switch (i) {
		case 0:
			m_LevelProfileInfoDBId = atoi(strings[i]);
			break;
		case 1:
			m_Value = strings[i];
			break;
		case 2:
			m_LevelProfileDBId = atoi(strings[i]);
			break;
		case 3:
			m_ExternalInfoDBId = atoi(strings[i]);
			break;
		case 4:
			m_Name = strings[i];
			break;
		case 5:
			m_DataType = atoi(strings[i]);
			break;	
		case 6:
			m_Length = atoi(strings[i]);
			break;
		case 7:
			m_Sequence = atoi(strings[i]);
			break;
		case 8:
			m_DefaultValue = strings[i];
			break;
		case 9:
			listValues = strings[i];
			break;
		case 10:
			m_ExternalSystemDBId = atoi(strings[i]);
			break;
		}
	}

	strings.RemoveAll();
	utilityHelper.ParseString(listValues, ",", strings);
	for (i=0; i < strings.GetSize(); ++i) {
		CStringArray moreStrings;
		utilityHelper.ParseString(strings[i], "^", moreStrings);
		m_ListValues.Add(moreStrings[0]);
		if (moreStrings.GetSize() > 1)
			m_ListDisplayValues.Add(moreStrings[1]);
		else
			m_ListDisplayValues.Add(moreStrings[0]);
	}


	return 0;

}

CString CLevelProfileExternalInfo::Stream()
{
	CString stream, listValues;

	for (int i=0; i < m_ListValues.GetSize(); ++i) {
		CString temp;
		temp.Format("%s^%s,", m_ListValues[i], m_ListDisplayValues[i]);
		listValues += temp;
	}

	listValues.TrimRight(",");
	stream.Format("%s|%d|%s|%d|%d|%s|%d|%d|%d|%s|%s|%d|",
		m_Name, m_LevelProfileInfoDBId, m_Value, m_LevelProfileDBId, m_ExternalInfoDBId, m_Name,
		m_DataType, m_Length, m_Sequence, m_DefaultValue, listValues, m_ExternalSystemDBId);
	
	return stream;

}

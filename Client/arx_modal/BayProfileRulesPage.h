//{{AFX_INCLUDES()
#include "DataGrid.h"
//}}AFX_INCLUDES
#if !defined(AFX_BAYPROFILERULESPAGE_H__68527E69_4B1D_48B0_8D9E_040A8A597C5D__INCLUDED_)
#define AFX_BAYPROFILERULESPAGE_H__68527E69_4B1D_48B0_8D9E_040A8A597C5D__INCLUDED_

#if _MSC_VER > 1000
#pragma once
#endif // _MSC_VER > 1000
// BayProfileRulesPage.h : header file
//
#include "BayProfile.h"

/////////////////////////////////////////////////////////////////////////////
// CBayProfileRulesPage dialog

class CBayProfileRulesPage : public CPropertyPage
{
	DECLARE_DYNCREATE(CBayProfileRulesPage)

// Construction
public:
	CBayProfileRulesPage();
	~CBayProfileRulesPage();

// Dialog Data
	//{{AFX_DATA(CBayProfileRulesPage)
	enum { IDD = IDD_BAY_PROFILE_RULE_PAGE };
	CComboBox	m_BayTypeListCtrl;
	CListCtrl	m_FacingListCtrl;
	double	m_PalletHeight;
	double	m_AdditionalRsvCube;
	double	m_PercentReserves;
	double	m_PctRsvUtil;
	double	m_PctSelUtil;
	double	m_DesiredReplenishments;
	//}}AFX_DATA


// Overrides
	// ClassWizard generate virtual function overrides
	//{{AFX_VIRTUAL(CBayProfileRulesPage)
	public:
	virtual BOOL OnSetActive();
	virtual BOOL OnKillActive();
	protected:
	virtual void DoDataExchange(CDataExchange* pDX);    // DDX/DDV support
	virtual BOOL OnNotify(WPARAM wParam, LPARAM lParam, LRESULT* pResult);
	//}}AFX_VIRTUAL

// Implementation
protected:
	// Generated message map functions
	//{{AFX_MSG(CBayProfileRulesPage)
	virtual BOOL OnInitDialog();
	afx_msg void OnSelchangeBaytypeList();
	afx_msg void OnRecalculate();
	afx_msg void OnAddFacing();
	afx_msg void OnEdit();
	afx_msg BOOL OnHelpInfo(HELPINFO* pHelpInfo);
	//}}AFX_MSG
	DECLARE_MESSAGE_MAP()

private:
	int m_PreviousSel;
	int UpdateScreenFromBayRule();
	int UpdateBayRuleFromScreen(int idx);
	CBayProfile *m_pBayProfile;

};

//{{AFX_INSERT_LOCATION}}
// Microsoft Visual C++ will insert additional declarations immediately before the previous line.

#endif // !defined(AFX_BAYPROFILERULESPAGE_H__68527E69_4B1D_48B0_8D9E_040A8A597C5D__INCLUDED_)

// SideProfileBayPage.cpp : implementation file
//

#include "stdafx.h"
#include "modal.h"
#include "SideProfileBayPage.h"
#include "SideProfileSheet.h"
#include "BayProfileDataService.h"
#include "UtilityHelper.h"
#include "ControlService.h"
#include "WizardHelper.h"
#include "Constants.h"
#include "HelpService.h"

#ifdef _DEBUG
#define new DEBUG_NEW
#undef THIS_FILE
static char THIS_FILE[] = __FILE__;
#endif

extern CBayProfileDataService bayProfileDataService;
extern CUtilityHelper utilityHelper;
extern CControlService controlService;
extern CHelpService helpService;

/////////////////////////////////////////////////////////////////////////////
// CSideProfileBayPage property page

IMPLEMENT_DYNCREATE(CSideProfileBayPage, CPropertyPage)

CSideProfileBayPage::CSideProfileBayPage() : CPropertyPage(CSideProfileBayPage::IDD)
{
	//{{AFX_DATA_INIT(CSideProfileBayPage)
	m_Quantity = _T("");
	m_HideExcluded = FALSE;
	//}}AFX_DATA_INIT
}

CSideProfileBayPage::~CSideProfileBayPage()
{
	for (int i=0; i < m_BayProfileList.GetSize(); ++i)
		delete m_BayProfileList[i];
}

void CSideProfileBayPage::DoDataExchange(CDataExchange* pDX)
{
	CPropertyPage::DoDataExchange(pDX);
	//{{AFX_DATA_MAP(CSideProfileBayPage)
	DDX_Control(pDX, IDC_BAY_LIST, m_ProfileTreeCtrl);
	DDX_Control(pDX, IDC_SIDE_BUTTON, m_SideButton);
	DDX_Text(pDX, IDC_QUANTITY, m_Quantity);
	DDX_Check(pDX, IDC_HIDE_EXCLUDED, m_HideExcluded);
	//}}AFX_DATA_MAP
}


BEGIN_MESSAGE_MAP(CSideProfileBayPage, CPropertyPage)
	//{{AFX_MSG_MAP(CSideProfileBayPage)
	ON_BN_CLICKED(IDC_INSERT, OnInsert)
	ON_BN_CLICKED(IDC_FILL, OnFill)
	ON_BN_CLICKED(IDC_DELETE, OnDelete)
	ON_BN_CLICKED(IDC_HIDE_EXCLUDED, OnHideExcluded)
	ON_NOTIFY(NM_DBLCLK, IDC_BAY_LIST, OnDblclkBayList)
	ON_WM_CONTEXTMENU()
	ON_COMMAND(ID_GENERIC_PROPERTIES, OnProfileProperties)
	ON_MESSAGE(WM_BAY_PROPERTIES, (LRESULT (AFX_MSG_CALL CWnd::*)(WPARAM, LPARAM))OnBayProperties)
	ON_WM_HELPINFO()
	//}}AFX_MSG_MAP
END_MESSAGE_MAP()

/////////////////////////////////////////////////////////////////////////////
// CSideProfileBayPage message handlers

BOOL CSideProfileBayPage::OnInitDialog() 
{
	CPropertyPage::OnInitDialog();
	
	LoadBayProfileList();
	
	BuildBayProfileTree();

	EnableToolTips(TRUE);

	m_ToolTip.Create(this, TTF_IDISHWND | TTF_TRACK | TTF_ABSOLUTE);
	
	m_ToolTipText = "";
	m_ToolTip.AddTool(GetDlgItem(IDC_SIDE_BUTTON),m_ToolTipText);

	m_ToolTip.SendMessage(TTM_SETDELAYTIME, (WPARAM)(DWORD)TTDT_INITIAL,
		(LPARAM)(INT)MAKELONG(100,0));
	m_ToolTip.SendMessage(TTM_SETDELAYTIME, (WPARAM)(DWORD)TTDT_AUTOPOP,
		(LPARAM)(INT)MAKELONG(3000,0));
	m_ToolTip.SendMessage(TTM_SETMAXTIPWIDTH, 0, (LPARAM)(INT)500);

	CToolInfo ti;
	m_ToolTip.GetToolInfo(ti, GetDlgItem(IDC_SIDE_BUTTON), 0);
	m_ToolTip.SendMessage(TTM_TRACKACTIVATE, (WPARAM)TRUE, (LPARAM)&ti);
	
	m_ToolTip.Activate(FALSE);

	m_Quantity = "1";

	UpdateData(FALSE);

	return TRUE;  // return TRUE unless you set the focus to a control
	              // EXCEPTION: OCX Property Pages should return FALSE
}

BOOL CSideProfileBayPage::OnSetActive() 
{
	CSideProfileSheet *pSheet = (CSideProfileSheet *)GetParent();
	m_pSideProfile = pSheet->m_pSideProfile;

	m_CurrentLength = m_pSideProfile->m_TotalLength;

	return CPropertyPage::OnSetActive();
}

BOOL CSideProfileBayPage::OnKillActive() 
{
	UpdateData(TRUE);
	
	if (m_pSideProfile->m_BayProfileList.GetSize() == 0) {
		AfxMessageBox("Please add at least one bay to the side.");
		return FALSE;
	}

	if (m_pSideProfile->m_FixedLength) {
		if (m_pSideProfile->CalculateLength() > m_pSideProfile->m_TotalLength) {
			CString temp;
			temp.Format("The calculated length of the aisle (%.0f) is greater than the\n"
				"specified fixed length (%.0f).  Please remove one or more\n"
				"bays or decrease the width of one or more bays.",
				m_pSideProfile->CalculateLength(), m_pSideProfile->m_TotalLength);
			AfxMessageBox(temp);
			return FALSE;
		}
	}
	return CPropertyPage::OnKillActive();
}

void CSideProfileBayPage::OnInsert() 
{
	UpdateData(TRUE);

	HTREEITEM hItem = m_ProfileTreeCtrl.GetSelectedItem();
	if (hItem == NULL) {
		AfxMessageBox("Please select a bay profile to insert.");
		return;
	}

	HTREEITEM hParentItem = m_ProfileTreeCtrl.GetParentItem(hItem);
	if (hParentItem == NULL) {
		AfxMessageBox("Please select a specific bay profile to insert.");
		return;
	}

	CWaitCursor cwc;

	CBayProfile *pBayProfile = m_BayProfileList[m_ProfileTreeCtrl.GetItemData(hItem)];
	if (pBayProfile->m_LevelProfileList.GetSize() == 0) {
		try {
			// only load the bay profile and the levels - we don't need rules or locations
			bayProfileDataService.GetBayProfile(pBayProfile->m_BayProfileDBId, *pBayProfile, 
				CBayProfile::loadLevels);
		}
		catch (...) {
			utilityHelper.ProcessError("Error retrieving bay profile from database.");
			return;
		}
	}

	int quantity;

	if (! utilityHelper.IsInteger(m_Quantity) || atoi(m_Quantity) <= 0) {
		AfxMessageBox("Please specify the number of bays to insert.");
		utilityHelper.SetEditControlErrorState(this, IDC_QUANTITY);
		return;
	}
	
	quantity = atoi(m_Quantity);

	if (quantity > 300) {
		CString str;
		str.Format("The maximum number of bays that can be inserted at a time is 300.");
		AfxMessageBox(str);
		utilityHelper.SetEditControlErrorState(this, IDC_QUANTITY);
		return;
	}

	double newLength = m_CurrentLength;
	double bayWidth = pBayProfile->m_Width;
	double barWidth = pBayProfile->m_UprightWidth;



	// If the upright width of the new bay profile is greater than that of the previous one,
	// subtract the previous one and add the new one so that the thicker bar is used
	int count = m_pSideProfile->m_BayProfileList.GetSize();

	POSITION pos = m_SelectedMap.GetStartPosition();
	int insertIdx = count;

	while (pos != NULL) {
		CBayProfile *pBayProfile;
		int idx;
		m_SelectedMap.GetNextAssoc(pos, idx, pBayProfile);
		if (idx < insertIdx)
			insertIdx = idx;
	}

	if (insertIdx > 0) {
		if (barWidth > m_pSideProfile->m_BayProfileList[insertIdx-1]->m_UprightWidth) {
			newLength -= m_pSideProfile->m_BayProfileList[insertIdx-1]->m_UprightWidth;
			newLength += barWidth;
		}
	}
//else
//		newLength = +=barWidth;
	if (insertIdx == count)
		newLength += barWidth;

	newLength += quantity*(bayWidth+barWidth);
		

	if ((m_pSideProfile->m_FixedLength) && (newLength > m_pSideProfile->m_TotalLength)) {
		CString temp;
		temp.Format("Inserting the bays will cause the aisle length to exceed the specified fixed length.\n"
			"Maximum length: %.0f, Current: %.0f, Requested: %.0f",
			m_pSideProfile->m_TotalLength, m_CurrentLength, newLength);
		AfxMessageBox(temp);
		utilityHelper.SetEditControlErrorState(this, IDC_QUANTITY);
		return;
	}
    


	//add bay(s) to the user bayProfile list
	for (int i = 0; i < quantity; i++) {
		CBayProfile *pNewProfile = new CBayProfile(*pBayProfile);
		m_pSideProfile->m_BayProfileList.InsertAt(insertIdx, pNewProfile);
	}

	m_CurrentLength = m_pSideProfile->CalculateLength();

	if (! m_pSideProfile->m_FixedLength)
		m_pSideProfile->m_TotalLength = m_CurrentLength;

	m_SideButton.Invalidate();
}

void CSideProfileBayPage::OnFill() 
{
	if (! m_pSideProfile->m_FixedLength) {
		AfxMessageBox("The Fill function can only be used with fixed length side profiles.");
		return;
	}

	UpdateData(TRUE);

	HTREEITEM hItem = m_ProfileTreeCtrl.GetSelectedItem();
	if (hItem == NULL) {
		AfxMessageBox("Please select a bay profile to add.");
		return;
	}

	HTREEITEM hParentItem = m_ProfileTreeCtrl.GetParentItem(hItem);
	if (hParentItem == NULL) {
		AfxMessageBox("Please select a specific bay profile to add.");
		return;
	}

	CWaitCursor cwc;

	CBayProfile *pBayProfile = m_BayProfileList[m_ProfileTreeCtrl.GetItemData(hItem)];
	if (pBayProfile->m_LevelProfileList.GetSize() == 0) {
		try {
			// only load the bay profile and the levels - we don't need rules or locations
			bayProfileDataService.GetBayProfile(pBayProfile->m_BayProfileDBId, *pBayProfile, 
				CBayProfile::loadLevels);
		}
		catch (...) {
			utilityHelper.ProcessError("Error retrieving bay profile from database.");
			return;
		}
	}
	
	int quantity;
	double availableLength = m_pSideProfile->m_TotalLength - m_CurrentLength;

	double bayWidth = pBayProfile->m_Width;
	double barWidth = pBayProfile->m_UprightWidth;

	// If the upright width of the last bay is less than the new one, the upright will
	// be replaced with the new one
	int count = m_pSideProfile->m_BayProfileList.GetSize();
	if (count > 0) {
		if (barWidth > m_pSideProfile->m_BayProfileList[count-1]->m_UprightWidth) {
			availableLength -= barWidth;
			availableLength += m_pSideProfile->m_BayProfileList[count-1]->m_UprightWidth;
		}
	}
	else
		// first bay, subtract the upright
		availableLength = m_pSideProfile->m_TotalLength - barWidth;

	if ( (bayWidth+barWidth) > availableLength) {
		CString temp;
		temp.Format("There is not enough space left to add a bay of this type to the aisle.\n"
			"Maximum length: %.0f, Current: %.0f, Length of inserted bay: %.0f",
			m_pSideProfile->m_TotalLength, m_CurrentLength, 
			(bayWidth+barWidth + (count == 0 ? barWidth : 0)));
		AfxMessageBox(temp);
		return;
	}

	quantity = (int)(availableLength/(bayWidth + barWidth));


	for (int i = 0; i < quantity; i++) {
		CBayProfile *pNewProfile = new CBayProfile(*pBayProfile);
		m_pSideProfile->m_BayProfileList.Add(pNewProfile);
	}

	m_CurrentLength = m_pSideProfile->CalculateLength();

	m_SideButton.Invalidate();
}

void CSideProfileBayPage::OnDelete() 
{
	POSITION pos = m_SelectedMap.GetStartPosition();

	if (pos == NULL) {
		AfxMessageBox("Please select one or more bay profiles to delete by clicking on \nthe "
			"bays in the picture above.  The selected bays will be highlighted.");
		return;
	}

	while (pos != NULL) {
		CBayProfile *pBayProfile;
		int idx;
		m_SelectedMap.GetNextAssoc(pos, idx, pBayProfile);
		m_pSideProfile->m_BayProfileList[idx]->m_BayProfileDBId = -1337;
	}

	for (int i=m_pSideProfile->m_BayProfileList.GetSize()-1; i >= 0; --i) {
		if (m_pSideProfile->m_BayProfileList[i]->m_BayProfileDBId == -1337) {
			delete m_pSideProfile->m_BayProfileList[i];
			m_pSideProfile->m_BayProfileList.RemoveAt(i);
		}
	}

	// clear the selection list
	m_SelectedMap.RemoveAll();

	m_CurrentLength = m_pSideProfile->CalculateLength();

	if (! m_pSideProfile->m_FixedLength)
		m_pSideProfile->m_TotalLength = m_CurrentLength;

	m_SideButton.Invalidate();	
}

void CSideProfileBayPage::OnHideExcluded() 
{
	UpdateData(TRUE);
	BuildBayProfileTree();
}

int CSideProfileBayPage::LoadBayProfileList()
{
	CStringArray bayNameList, strings, usedList;
	CMap<int, int, int, int> usedMap;

	CWaitCursor cwc;

	if (m_BayProfileList.GetSize() > 0)
		return 0;

	try {
		bayProfileDataService.GetBayProfileList(bayNameList);
	}
	catch (...) {
		utilityHelper.ProcessError("Error getting list of bay profiles.");
		return -1;
	}

	for (int i=0; i < bayNameList.GetSize(); ++i) {
		strings.RemoveAll();
		utilityHelper.ParseString(bayNameList[i], "|", strings);
		CBayProfile *pBayProfile = new CBayProfile();

		pBayProfile->m_BayProfileDBId = atoi(strings[0]);
		pBayProfile->m_Description = strings[1];
		if (pBayProfile->m_Description.Find("\\") >= 0)
			pBayProfile->m_Description.Delete(0, pBayProfile->m_Description.Find("\\")+1);

		pBayProfile->m_BayType = atoi(strings[2]);
		pBayProfile->m_ExcludeFromOptimization = atoi(strings[3]);
		pBayProfile->m_Active = atoi(strings[4]);
		m_BayProfileList.Add(pBayProfile);
	}

	return 0;

}

int CSideProfileBayPage::LoadBayTypeList()
{
	m_ImageList.Create(16, 16, TRUE, 4, 1);
	m_ImageList.Add(AfxGetApp()->LoadIcon(IDI_FLDOPEN));
	m_ImageList.Add(AfxGetApp()->LoadIcon(IDI_FLDCLS));
	m_ImageList.Add(AfxGetApp()->LoadIcon(IDI_LOCATIONICON));

	m_ProfileTreeCtrl.SetImageList(&m_ImageList, TVSIL_NORMAL);

	HTREEITEM hItem;

	hItem = m_ProfileTreeCtrl.InsertItem("Bin", 0, 1, TVI_ROOT, TVI_LAST);
	m_ProfileTreeCtrl.SetItemData(hItem, BAYTYPE_BIN);
	m_MapBayTypeToTree.SetAt(BAYTYPE_BIN, hItem);

	hItem = m_ProfileTreeCtrl.InsertItem("Case Flow", 0, 1, TVI_ROOT, TVI_LAST);
	m_ProfileTreeCtrl.SetItemData(hItem, BAYTYPE_CASEFLOW);
	m_MapBayTypeToTree.SetAt(BAYTYPE_CASEFLOW, hItem);

	hItem = m_ProfileTreeCtrl.InsertItem("Drive In", 0, 1, TVI_ROOT, TVI_LAST);
	m_ProfileTreeCtrl.SetItemData(hItem, BAYTYPE_DRIVEIN);
	m_MapBayTypeToTree.SetAt(BAYTYPE_DRIVEIN, hItem);

	hItem = m_ProfileTreeCtrl.InsertItem("Floor", 0, 1, TVI_ROOT, TVI_LAST);
	m_ProfileTreeCtrl.SetItemData(hItem, BAYTYPE_FLOOR);
	m_MapBayTypeToTree.SetAt(BAYTYPE_FLOOR, hItem);

	hItem = m_ProfileTreeCtrl.InsertItem("Pallet", 0, 1, TVI_ROOT, TVI_LAST);
	m_ProfileTreeCtrl.SetItemData(hItem, BAYTYPE_PALLET);
	m_MapBayTypeToTree.SetAt(BAYTYPE_PALLET, hItem);

	hItem = m_ProfileTreeCtrl.InsertItem("Pallet Flow", 0, 1, TVI_ROOT, TVI_LAST);
	m_ProfileTreeCtrl.SetItemData(hItem, BAYTYPE_PALLETFLOW);
	m_MapBayTypeToTree.SetAt(BAYTYPE_PALLETFLOW, hItem);

	return 0;

}

void CSideProfileBayPage::BuildBayProfileTree()
{
	m_ProfileTreeCtrl.DeleteAllItems();

	LoadBayTypeList();

	HTREEITEM hTypeItem;
	for (int i=0; i < m_BayProfileList.GetSize(); ++i) {
		CBayProfile *pBayProfile = m_BayProfileList[i];
		if (m_HideExcluded && pBayProfile->m_ExcludeFromOptimization)
			continue;

		if (m_MapBayTypeToTree.Lookup(pBayProfile->m_BayType, hTypeItem)) {
			CString temp = pBayProfile->m_Description;
			if (pBayProfile->m_Active)
				temp += " (Active)";

			HTREEITEM hItem = m_ProfileTreeCtrl.InsertItem(temp, 2, 2, hTypeItem, TVI_LAST);
			m_ProfileTreeCtrl.SetItemData(hItem, (unsigned long)i);
		}
	}

	HTREEITEM hItem = m_ProfileTreeCtrl.GetNextItem(TVI_ROOT, TVGN_CHILD);
	HTREEITEM hTopItem = hItem;
	while (hItem != NULL) {
		m_ProfileTreeCtrl.Expand(hItem, TVE_EXPAND);
		HTREEITEM hNextItem = m_ProfileTreeCtrl.GetNextItem(hItem, TVGN_NEXT);
		hItem = hNextItem;
	}


	m_ProfileTreeCtrl.EnsureVisible(hTopItem);
}

void CSideProfileBayPage::OnDblclkBayList(NMHDR* pNMHDR, LRESULT* pResult) 
{
	OnProfileProperties();

	*pResult = 0;
}

void CSideProfileBayPage::OnContextMenu(CWnd* pWnd, CPoint point) 
{
	if (pWnd != &m_ProfileTreeCtrl)
		return;

	CMenu menu;
	menu.LoadMenu(IDR_GENERIC_MENU);

	CPoint pt(point);
	m_ProfileTreeCtrl.ScreenToClient(&pt);
	UINT nFlags;
	HTREEITEM hItem = m_ProfileTreeCtrl.HitTest(pt, &nFlags);
	if (hItem == NULL)
		return;
	else
		m_ProfileTreeCtrl.SelectItem(hItem);

	HTREEITEM hParentItem = m_ProfileTreeCtrl.GetParentItem(hItem);
	if (hParentItem != NULL) {
		menu.GetSubMenu(0)->DeleteMenu(1, MF_BYPOSITION); 
		menu.GetSubMenu(0)->DeleteMenu(1, MF_BYPOSITION);
		menu.GetSubMenu(0)->ModifyMenu(0, MF_BYPOSITION|MF_STRING, ID_GENERIC_PROPERTIES, "&Properties");
	}

	menu.GetSubMenu(0)->TrackPopupMenu(TPM_LEFTALIGN|TPM_RIGHTBUTTON, point.x, point.y, this);		
}

void CSideProfileBayPage::OnProfileProperties() 
{
	HTREEITEM hItem = m_ProfileTreeCtrl.GetSelectedItem();
	if (hItem == NULL)
		return;

	HTREEITEM hParentItem = m_ProfileTreeCtrl.GetParentItem(hItem);
	if (hParentItem == NULL)
		return;
	
	CBayProfile *pBayProfile = m_BayProfileList[m_ProfileTreeCtrl.GetItemData(hItem)];
	CString oldDesc = pBayProfile->m_Description;

	CWizardHelper wizardHelper;
	if (wizardHelper.ShowBayWizard(pBayProfile) >= 0) {

		// Since copies of the bay profiles are stored in the side, we need
		// to make sure we update the copy as well
		for (int i=0; i < m_pSideProfile->m_BayProfileList.GetSize(); ++i) {
			if (m_pSideProfile->m_BayProfileList[i]->m_BayProfileDBId == pBayProfile->m_BayProfileDBId)
				*m_pSideProfile->m_BayProfileList[i] = *pBayProfile;
		}

		// Update the side based on any new bay info (e.g. depth changing)
		m_CurrentLength = m_pSideProfile->CalculateLength();
		if (! m_pSideProfile->m_FixedLength)
			m_pSideProfile->m_TotalLength = m_CurrentLength;

		if (oldDesc != pBayProfile->m_Description)
			m_ProfileTreeCtrl.SetItemText(hItem, pBayProfile->m_Description);

		m_SideButton.Invalidate(TRUE);
	}


}


void CSideProfileBayPage::OnBayProperties(WPARAM wParam, LPARAM lParam) 
{
	CBayProfile *pBayProfile = NULL;
	CBayProfile *pTempProfile = m_pSideProfile->m_BayProfileList[wParam];

	for (int i=0; i < m_BayProfileList.GetSize(); ++i) {
		if (m_BayProfileList[i]->m_BayProfileDBId == pTempProfile->m_BayProfileDBId) {
			pBayProfile = m_BayProfileList[i];
			break;
		}
	}

	// Shouldn't happen, but just in case we don't find the bay profile in the main list
	if (pBayProfile == NULL)
		return;

	CString oldDesc = pBayProfile->m_Description;
	CWizardHelper wizardHelper;
	if (wizardHelper.ShowBayWizard(pBayProfile) >= 0) {

		// Since copies of the bay profiles are stored in the side, we need
		// to make sure we update the copy as well
		for (i=0; i < m_pSideProfile->m_BayProfileList.GetSize(); ++i) {
			if (m_pSideProfile->m_BayProfileList[i]->m_BayProfileDBId ==
				pBayProfile->m_BayProfileDBId)
				*m_pSideProfile->m_BayProfileList[i] = *pBayProfile;
		}

		// Update the side based on any new bay info (e.g. depth changing)
		m_CurrentLength = m_pSideProfile->CalculateLength();
		if (! m_pSideProfile->m_FixedLength)
			m_pSideProfile->m_TotalLength = m_CurrentLength;

		if (oldDesc != pBayProfile->m_Description) {
			HTREEITEM hFolderItem = m_ProfileTreeCtrl.GetChildItem(TVI_ROOT);

			HTREEITEM hItem = m_ProfileTreeCtrl.GetChildItem(hFolderItem);
			while (hItem != NULL) {	
				CBayProfile *pTempProfile = m_BayProfileList[m_ProfileTreeCtrl.GetItemData(hItem)];
				if (pTempProfile->m_BayProfileDBId == pBayProfile->m_BayProfileDBId) {
					m_ProfileTreeCtrl.SetItemText(hItem, pBayProfile->m_Description);
					break;
				}
				HTREEITEM hNextItem = m_ProfileTreeCtrl.GetNextItem(hItem, TVGN_NEXT);
				hItem = hNextItem;
			}
		}

		m_SideButton.Invalidate(TRUE);
	}	
}

BOOL CSideProfileBayPage::PreTranslateMessage(MSG* pMsg) 
{
	m_ToolTip.RelayEvent(pMsg);	

	return CPropertyPage::PreTranslateMessage(pMsg);
}


void CSideProfileBayPage::UpdateToolTip(int x, int y, int currentBayIdx)
{
	if (currentBayIdx < 0)
		m_ToolTip.Activate(FALSE);
	else {
		m_ToolTip.Activate(TRUE);
		CBayProfile *pBayProfile = m_pSideProfile->m_BayProfileList[currentBayIdx];

		CString temp;
		pBayProfile->ConvertBayType(pBayProfile->m_BayType, temp);

		m_ToolTipText.Format("  %s\\%s  \r\n  Bay Width: %.0f  \r\n  Upright Width: %.0f  ",
			temp, pBayProfile->m_Description, pBayProfile->m_Width, pBayProfile->m_UprightWidth);
		m_ToolTip.UpdateTipText(m_ToolTipText, GetDlgItem(IDC_SIDE_BUTTON), 0);
	}

	m_ToolTip.SendMessage(TTM_TRACKPOSITION, 0, (LPARAM)MAKELPARAM(x, y));
}

BOOL CSideProfileBayPage::OnHelpInfo(HELPINFO* pHelpInfo) 
{
	helpService.ShowFieldHelp(IDD, pHelpInfo->iCtrlId);
	return FALSE;

}

BOOL CSideProfileBayPage::OnNotify(WPARAM wParam, LPARAM lParam, LRESULT* pResult) 
{
	NMHDR* pNMHDR = (NMHDR*)lParam;

	if (pNMHDR->code == PSN_HELP) {
		helpService.ShowScreenHelp(IDD);
		return TRUE;
	}
	
	return CPropertyPage::OnNotify(wParam, lParam, pResult);
}
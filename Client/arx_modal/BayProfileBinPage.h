#if !defined(AFX_BAYPROFILEBINPAGE_H__E6D74FFE_2EFD_4757_8746_F46D3C8E1A0C__INCLUDED_)
#define AFX_BAYPROFILEBINPAGE_H__E6D74FFE_2EFD_4757_8746_F46D3C8E1A0C__INCLUDED_

#if _MSC_VER > 1000
#pragma once
#endif // _MSC_VER > 1000
// BayProfileBinPage.h : header file
//
#include "BayProfileTopViewButton.h"
#include "BayProfileSideViewButton.h"
#include "BayProfile.h"
/////////////////////////////////////////////////////////////////////////////
// CBayProfileBinPage dialog

class CBayProfileBinPage : public CPropertyPage
{
	DECLARE_DYNCREATE(CBayProfileBinPage)

// Construction
public:
	CBayProfileBinPage();
	~CBayProfileBinPage();

// Dialog Data
	//{{AFX_DATA(CBayProfileBinPage)
	enum { IDD = IDD_BAY_PROFILE_BIN_ATTRIBUTES };
	CBayProfileTopViewButton	m_TopViewButton;
	CBayProfileSideViewButton	m_SideViewButton;
	double	m_UprightHeight;
	double	m_UprightWidth;
	double	m_WeightCapacity;
	double	m_BayDepth;
	double	m_BayHeight;
	double	m_BayWidth;
	//}}AFX_DATA


// Overrides
	// ClassWizard generate virtual function overrides
	//{{AFX_VIRTUAL(CBayProfileBinPage)
	public:
	virtual BOOL OnSetActive();
	virtual BOOL OnKillActive();
	protected:
	virtual void DoDataExchange(CDataExchange* pDX);    // DDX/DDV support
	virtual BOOL OnCommand(WPARAM wParam, LPARAM lParam);
	virtual BOOL OnNotify(WPARAM wParam, LPARAM lParam, LRESULT* pResult);
	//}}AFX_VIRTUAL

// Implementation
protected:
	// Generated message map functions
	//{{AFX_MSG(CBayProfileBinPage)
	virtual BOOL OnInitDialog();
	afx_msg BOOL OnHelpInfo(HELPINFO* pHelpInfo);
	//}}AFX_MSG
	DECLARE_MESSAGE_MAP()

private:
	BOOL Validate();
	CBayProfile *m_pBayProfile;
	BOOL m_Validating;
};

//{{AFX_INSERT_LOCATION}}
// Microsoft Visual C++ will insert additional declarations immediately before the previous line.

#endif // !defined(AFX_BAYPROFILEBINPAGE_H__E6D74FFE_2EFD_4757_8746_F46D3C8E1A0C__INCLUDED_)

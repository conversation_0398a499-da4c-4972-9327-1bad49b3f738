// SearchAnchorDataService.h: interface for the CSearchAnchorDataService class.
//
//////////////////////////////////////////////////////////////////////

#if !defined(AFX_SEARCHANCHORDATASERVICE_H__83CE7F76_4E70_4027_81C7_5069263A6677__INCLUDED_)
#define AFX_SEARCHANCHORDATASERVICE_H__83CE7F76_4E70_4027_81C7_5069263A6677__INCLUDED_

#if _MSC_VER > 1000
#pragma once
#endif // _MSC_VER > 1000

class CSearchAnchorDataService  
{
public:
	CSearchAnchorDataService();
	virtual ~CSearchAnchorDataService();

	int GetSearchAnchorPoints(CStringArray &searchAnchorList);
	int UpdateSearchAnchorPoints(CObArray &searchAnchorList);
	int CSearchAnchorDataService::GetSearchAnchorLocations(CStringArray &locList);

};

#endif // !defined(AFX_SEARCHANCHORDATASERVICE_H__83CE7F76_4E70_4027_81C7_5069263A6677__INCLUDED_)

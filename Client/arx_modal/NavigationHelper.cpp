// NavigationHelper.cpp: implementation of the CNavigationHelper class.
//
//////////////////////////////////////////////////////////////////////

#include "stdafx.h"
#include <afxmt.h>

#include "modal.h"
#include "NavigationCommands.h"
#include "NavigationHelper.h"

#include "ssa_exception.h"
#include "modal.h"
#include "AutoCADCommands.h"
#include "FacilityCommands.h"
#include "FacilityHelper.h"
#include "BTreeHelper.h"
#include "ForteService.h"
#include "ControlService.h"
#include "UtilityHelper.h"

#include "SplashWnd.h"
#include "Startup.h"
#include "MainWizard.h"
#include "TreeElement.h"
#include "HelpService.h"
#include "ResourceHelper.h"
#include "Processing.h"
#include "LoginDlg.h"

#include "WizardHelper.h"

#include <aced.h>
#include <adscodes.h>

#include "base.h"
#include "MiscClasses.h"
#include "SrvcObjs.h"
#include "SLOTSessionMgr.h"
#include "SsaEncrypt.h"

#ifdef _DEBUG
#undef THIS_FILE
static char THIS_FILE[]=__FILE__;
#define new DEBUG_NEW
#endif
//////////////////////////////////////////////////////////////////////
// External Variables and Functions
//////////////////////////////////////////////////////////////////////
extern BOOL DEBUG;
extern CControlService controlService;
//////////////////////////////////////////////////////////////////////
extern TreeElement changesTree;
extern CForteService forteService;
extern CHelpService helpService;
extern CUtilityHelper utilityHelper;

extern CString AutoLoginParameters;
extern int WMS_TYPE;
extern int WMS_UNITS;

extern CEvent g_ThreadDone;
//////////////////////////////////////////////////////////////////////
// Module Variables and Functions
CNavigationHelper::CNavigationHelper()
{

}

CNavigationHelper::~CNavigationHelper()
{

}

void CNavigationHelper::RunRegisteredCommand(const CString &command)
{
	CStringArray script;
	CAutoCADCommands acCmds;

	script.Add(".new");
	script.Add("yes");
	script.Add(".");
	script.Add(command);

	acCmds.ModifyDrawing();
	acCmds.RunScript(script, "RunRegisteredCommand");

}


void CNavigationHelper::ShowWizard()
{
	
	CStartup start;
	//CMainWizard mainWizardDlg;
	CString strUnits, strWMS;
	CString scriptFile;
	BOOL autoOpen = FALSE;
	CFacilityHelper facHelper;
	int newMode;

	char wmsTypeStr[256];
	memset(wmsTypeStr,0,256);

	// Check for auto-open
	if (AutoLoginParameters != "") {
		int idx = AutoLoginParameters.Find("|");
		if (idx >= 0) {
			if (AutoLoginParameters.Mid(idx+1).GetLength() > 0) {
				newMode = IDC_OPENFACILITY;
				autoOpen = TRUE;
			}
		}
	}


	if (! autoOpen) {
		CTemporaryResourceOverride tro;

		if (start.DoModal() == IDCANCEL)
			if (controlService.m_Mode > 0)
				return;
		newMode = start.m_selectedCtlId;
	}

	CWizardHelper wizardHelper;

	switch (newMode) {
	case IDC_USEWIZARD:
		wizardHelper.BayProfileWizard();
		break;

	case IDC_NEWFACILITY:
		facHelper.NewFacility();
		break;
		
	case IDC_OPENFACILITY:
		// Open a facility; this function will close the old drawing,
		// create a new drawing, display the open facility dialog,
		// open the new facility, and show the open facility menus
		// This has a problem in that we end up calling the autocad menu
		// (acad.mnl) from within the menu so we get an error, but as
		// long as the ShowWizard command is the last one in acad.mnl
		// it doesn't seem to affect anything
		//CFacilityCommands::OpenFacility();
		// menus and commands will be registered in the OpenFacility function
		facHelper.OpenFacility();
		break;
	}

	CAutoCADCommands::Set_Int_Var("FILEDIA", 0);
	CAutoCADCommands::Set_Int_Var("CMDECHO", 0);

}



//////////////////////////////////////////////////////////////////////
// Function Name : DisplayCursorMenu
// Classname : None
// Description : display the pop-up menu
// Date Created : 10/01/98
// Author : acs
//////////////////////////////////////////////////////////////////////
// Inputs : none
// Outputs : none
// Explanation : 
//   Display the right-click pop-up menu
//   
//////////////////////////////////////////////////////////////////////
// Algorithm : None
//////////////////////////////////////////////////////////////////////
// Revisions : 
//   Date-initials : Description_of_revision
//////////////////////////////////////////////////////////////////////
	/*******************************************************
	*
	*                       NOTICE
	*
	*  THIS SOFTWARE IS THE PROPERTY OF AND CONTAINS
	*  CONFIDENTIAL INFORMATION OF SSA GLOBAL 
	*  TECHNOLOGIES, INC., AND SHALL NOT BE COPIED,
	*  USED, NOR DISCLOSED WITHOUT EXPRESS WRITTEN
	*  AUTHORIZATION.  ALTHOUGH PUBLICATION IS NOT
	*  INTENDED, IN THE EVENT OF PUBLICATION, THE
	*  FOLLOWING NOTICE IS APPLICABLE:
	*
	*  (c) COPYRIGHT 2004 SSA GLOBAL TECHNOLOGIES, INC.
	*
	*           SSA GLOBAL TECHNOLOGIES, INC.
	*
	********************************************************/
//////////////////////////////////////////////////////////////////////
void CNavigationHelper::DisplayCursorMenu()
{

	ads_menucmd("P0=SLOTTING2.BAYCURSORMN");
	ads_menucmd("A1=+SLOTTING2.BAYPROP");
	ads_menucmd("P0=*");

}

//////////////////////////////////////////////////////////////////////
// Function Name : SucceedHelp
// Classname : None
// Description : Kick off the Robo-Help Help screen for succeed
// Date Created : 4/5/99
// Author : np
//////////////////////////////////////////////////////////////////////
// Inputs : none
// Outputs : none
// Explanation : 
//////////////////////////////////////////////////////////////////////
// Algorithm : None
//////////////////////////////////////////////////////////////////////
// Revisions : 
//   Date-initials : Description_of_revision
//////////////////////////////////////////////////////////////////////
	/*******************************************************
	*
	*                       NOTICE
	*
	*  THIS SOFTWARE IS THE PROPERTY OF AND CONTAINS
	*  CONFIDENTIAL INFORMATION OF SSA GLOBAL 
	*  TECHNOLOGIES, INC., AND SHALL NOT BE COPIED,
	*  USED, NOR DISCLOSED WITHOUT EXPRESS WRITTEN
	*  AUTHORIZATION.  ALTHOUGH PUBLICATION IS NOT
	*  INTENDED, IN THE EVENT OF PUBLICATION, THE
	*  FOLLOWING NOTICE IS APPLICABLE:
	*
	*  (c) COPYRIGHT 2004 SSA GLOBAL TECHNOLOGIES, INC.
	*
	*           SSA GLOBAL TECHNOLOGIES, INC.
	*
	********************************************************/
//////////////////////////////////////////////////////////////////////
void CNavigationHelper::SucceedHelp() {

	helpService.ShowScreenHelp("What_is_EXceed_Optimize");

	return;

}


void CNavigationHelper::NewConnection()
{
	int prevFacilityDBID = 0;
	CBTreeHelper btHelper;

	//////////////////////////////////////////////////////////////////////
	//first check to see if we have a facility open
	//////////////////////////////////////////////////////////////////////
	if ( (changesTree.treeChildren.GetSize() != 0)
		&& (changesTree.elementDBID != 0)) {
		prevFacilityDBID = changesTree.elementDBID;

		try {
			btHelper.UnlockFacility(prevFacilityDBID);
		}
		catch(...) {
			AfxMessageBox("Warning : Error closing previous facility");
		}
	}

	CString scriptFile;
	if ( btHelper.NewFacilityTree(changesTree, controlService.m_DefaultWMS, controlService.m_MeasurementType) != 0 ) {
		ads_printf("Error building blank facility\n");
		return;
	}
	
	CFacilityHelper facHelper;

	facHelper.RunNewFacilityScript("ProcessLogin");

	// todo: add code here to delete dwg file for previous facility from client

}

void CNavigationHelper::ProcessLogin()
{
	CFacilityHelper facHelper;

	CSsaStringArray tmpDatabaseList;
	CTemporaryResourceOverride tro;
	CProcessing *pProcessDlg = NULL;
	CLoginDlg loginDlg;
	CWaitCursor cwc;

	if (! facHelper.CheckCurrentFacility())
		return;
	CListstringPtr dbList;
	try {
		dbList = getSessionMgrSO()->GetDatabaseListHelper();
	}
	catch (Ssa_Exception e) 
	{
		char eMsg[1024];
		e.GetMessage(eMsg);
		ads_printf("%s\n", eMsg);
		CString msg = "Unable to get the list of databases from the server.\n"
			"You may need to restart the Optimize services on the server.\n"
			"Please correct the problem and restart the application.";
		AfxMessageBox(msg);
		return;
	}
	
	/// Extracted from old code: ForteService::StartForte() 
	/// don't need Forte anymore, but need the AutoLoginParameters
	AutoLoginParameters = controlService.GetApplicationData("AutoLoginParameters");

	// Load up the login dialog
	loginDlg.m_strUserID = controlService.GetApplicationData("UserID");
	loginDlg.m_strVersionNumber = controlService.GetApplicationData("MajorVersion");
	loginDlg.m_strBuildNumber.Format("Build Number: %s", controlService.GetApplicationData("BuildNumber"));
	
	ads_printf("\n");
///	for (int i=0; i < tmpDatabaseList.GetSize(); ++i) {
	for (int i=0; i < dbList->GetSize(); ++i) {

///OC		loginDlg.m_databaseList.Add(tmpDatabaseList[i]);
		loginDlg.m_databaseList.Add(CString(dbList->GetAt(dbList->FindIndex(i)).c_str()));
	}
	
	BOOL done = FALSE;
	BOOL found = FALSE;

	while (! done) {

		found = FALSE;

		// See if they are set up for automatic login
		if (AutoLoginParameters != "") {
			int idx = AutoLoginParameters.Find("|");
			if (idx > 0) {
				loginDlg.m_strDatabase = AutoLoginParameters.Left(idx);
				AutoLoginParameters = AutoLoginParameters.Mid(idx);

				for (i=0; i < loginDlg.m_databaseList.GetSize(); ++i) {
					if (loginDlg.m_databaseList[i].CompareNoCase(loginDlg.m_strDatabase) == 0) {
						found = TRUE;
						break;
					}
				}
				if (! found)
					loginDlg.m_strDatabase = "";
			}
		}
		
		if (! found) {
			// Show login dialog
			int rc = loginDlg.DoModal();
			if (rc != IDOK) {
				return;
			}
		
			SSAEncrypt se;
			char szPwd[128]; 
			strncpy(szPwd, se.getSessionDeSO(controlService.GetApplicationData("ssaprofile").GetBuffer()), sizeof(szPwd));
			if (loginDlg.m_strPassword != szPwd) {
				AfxMessageBox("The password you entered was incorrect.  Passwords are case-sensitive.\n"
					"Please enter the correct password.");
				continue;
			}
		}
		else {
			HWND hWnd = adsw_acadDocWnd(); 
			CWnd *parent;
			parent = CWnd::FromHandle(hWnd);
			pProcessDlg = new CProcessing;
			pProcessDlg->Create(IDD_PROCESSING, parent);
			pProcessDlg->m_StatusText = "Processing Automatic Login...";
			pProcessDlg->UpdateData(FALSE);
			pProcessDlg->CenterWindow();
			pProcessDlg->ShowWindow(SW_SHOW);
			pProcessDlg->UpdateWindow();
			acedUpdateDisplay();
		}

		CWaitCursor cwc2;
		
		ads_printf("Connecting to database...\n");
		try {
			getSessionMgrSO()->ConnectDatabaseHelper((LPCTSTR)loginDlg.m_strUserID, (LPCTSTR)loginDlg.m_strPassword, (LPCTSTR)loginDlg.m_strDatabase);
			
///			forteService.ConnectDatabase(loginDlg.m_strUserID, loginDlg.m_strPassword, loginDlg.m_strDatabase);
		}
		catch (Ssa_Exception e) {
			char eMsg[1024];
			e.GetMessage(eMsg);
			ads_printf("%s\n", eMsg);
			
			CString msg = "Unable to connect to database: ";
			msg += loginDlg.m_strDatabase;
			AfxMessageBox(msg);
			if (pProcessDlg != NULL) {
				pProcessDlg->DestroyWindow();
				pProcessDlg = NULL;
			}
			continue;
		}
		ads_printf("Connected.\n");
		done = TRUE;


		//UserID,Password & database are required startup parameters for OptimizeReports.exe
		controlService.m_CurrentDatabase = loginDlg.m_strDatabase;
		controlService.m_CurrentUserID = loginDlg.m_strUserID;
		SSAEncrypt se;
		controlService.m_Password = se.getSessionDeSO(controlService.GetApplicationData("ssaprofile").GetBuffer());
	}
	
	if (pProcessDlg != NULL) {
		if (! DEBUG)
			Sleep(1000);
		pProcessDlg->DestroyWindow();
	}

	controlService.Initialize();

	if (controlService.GetApplicationData("ShowWizard", "Dialogs\\Main") != "No")
		facHelper.RunNewFacilityScript("ShowWizard");
	else
		facHelper.RunNewFacilityScript("FacNew");

	return;
}

void CNavigationHelper::ModifyDrawing()
{
	CAutoCADCommands acCmds;

	acCmds.ModifyDrawing();

}


void CNavigationHelper::LoadOptimize()
{
	CSplashWnd *pWnd = new CSplashWnd();
	HWND hWnd = adsw_acadDocWnd(); 
	CWnd *parent;
	parent = CWnd::FromHandle(hWnd);
	pWnd->Create("STATIC", "Splash Window", WS_CHILD|WS_VISIBLE, 
		CRect(0, 0, 1, 1), parent, WM_USER+1);
	pWnd->CenterWindow();
	pWnd->UpdateWindow();
	pWnd->ShowWindow(SW_SHOW);
	Sleep(2000);	// MFS 2Mar06 Login screen covers up splash screen

	ads_regapp("Slotting");
	ads_printf("Initializing Application... Please wait.");

	ads_printf("Loading help map...\n");
	helpService.Initialize();

	parent->SetFocus();

	ads_printf("Command: ");

}


/*
int CNavigationHelper::GetDatabaseList(CSsaStringArray & databaseList) 
{
	CString tempString;
	CSsaStringArray tempSendArray;
	CSsaStringArray tempRecvArray;
	CSsaStringArray tempArray;
	CString sendString;
	int i = 0;
	int tempInt;

	/////////////////////////////////////////////////////////////
	// build the data to send to the Forte connection server
	/////////////////////////////////////////////////////////////
	sendString = "<SIO>SLOTSocketString\n";
	tempSendArray.Add(sendString);
	sendString = "<SAI>GetDatabaseList\n";
	tempSendArray.Add(sendString);
	sendString = "<EIO>SLOTSocketString\n";
	tempSendArray.Add(sendString);
	
	forteService.SendToForteConnection(tempSendArray,tempRecvArray,
		CString("SLOTSocketString"), 8888);

	for (i=0; i < tempRecvArray.GetSize(); i++) {
		if ( tempRecvArray[i].Find("<SAI>") != -1 ) {
			tempString = tempRecvArray[i].Mid(5);
			tempInt = tempString.GetLength();
			if (tempString.GetAt(tempInt-1) == '\n' ) {
				tempString.SetAt(tempInt-1,' ');
				tempString.TrimRight();
			}
			databaseList.Add(tempString);
		}
	}
	if ( databaseList.GetSize() == 0 )
		return -1;
	else
		return 0;
}
*/


void CNavigationHelper::InitApp()
{
	CTemporaryResourceOverride thisResource;
	CWaitCursor cwc;

	if(!AfxOleInit()) {
		AfxMessageBox("Could not initialize COM services");
		return;
	}

	controlService.Initialize();

	CString debugFlag = controlService.GetApplicationData("Debug");
	if (debugFlag == "")
		DEBUG = FALSE;
	else
		DEBUG = (atoi(debugFlag) != 0);

	LoadOptimize();

	CCommands::RegisterCommands();
	
	CBTreeHelper btHelper;
	btHelper.NewFacilityTree(changesTree, 0, 0);
	controlService.SetMode(IDC_NEWFACILITY);

}

void CNavigationHelper::UnloadApp()
{

	CWaitCursor cwc;
	CBTreeHelper btHelper;
	int prevFacilityDBID = 0;

	//first check to see if we have a facility open
	if ( (changesTree.treeChildren.GetSize() != 0)
		  && (changesTree.elementDBID != 0))
	{
		//unlock the current facility before exiting
		prevFacilityDBID = changesTree.elementDBID;
		try {
			btHelper.UnlockFacility(prevFacilityDBID);
		}
		catch (...) {
			AfxMessageBox("Error unlocking facility.");
		}
	}
	CCommands::UnRegisterAllCommands();

	try {
		forteService.StopForte();  //kill the Forte client partition
	}
	catch (...) {}

	if(controlService.m_CurrentDatabase.GetLength() > 0)
	{
		try
		{
			getSessionMgrSO()->DisconnectDatabase((LPCTSTR)controlService.m_CurrentDatabase);
		}
		catch( ... )
		{
		}
	}

}

// This doesn't work very well so don't use it - I left it here in case you 
// forget that it doesn't work
int CNavigationHelper::ShowModelessWizard()
{
	CStartup *pStartDlg = new CStartup;
	pStartDlg->m_Modeless = TRUE;
	HWND hWnd = adsw_acadMainWnd(); 
	CWnd *parent;
	parent = CWnd::FromHandle(hWnd);
	
	pStartDlg->Create(IDD_STARTUP, parent);
	pStartDlg->CenterWindow();
	pStartDlg->ShowWindow(SW_SHOW);
	pStartDlg->UpdateWindow();

	parent->UpdateWindow();

	BOOL bThreadDone = false;
	while (TRUE) {
		if ( ! utilityHelper.PeekAndPump() )
			break;
		
		bThreadDone = g_ThreadDone.Lock(0);
		if (bThreadDone)
			break;
		
		Sleep(1000);
	}
	
	int rc = pStartDlg->m_selectedCtlId;

	delete pStartDlg;

	return rc;
}

/////////////////////////////////////////////////////////////
// The tree element class declaration
/////////////////////////////////////////////////////////////
#ifndef TREEELEMENTINCLUDE
#define TREEELEMENTINCLUDE
#include "stdafx.h"
#include "qqhclasses.h"

class TreeElement;

class TreeElement : public CObject {
public :
	TreeElement * getAisleByConnectedPickPathHandle(const CString &handle);
	TreeElement * getAisleByPickPathHandle(const CString &handle);
	void clear();

	CMap<int, int, int, int> DeletedSectionMap;
	CMap<int, int, int, int> DeletedAisleMap;
	CMap<int, int, int, int> DeletedSideMap;
	CMap<int, int, int, int> DeletedBayMap;

	TreeElement *FindFacilityElement(int parentID, char * childType, TreeElement & changesTree);

	TreeElement *getSectionByOffset(int fileOffset);
	TreeElement *getAisleByOffset(int fileOffset);
	TreeElement *getSideByOffset(int fileOffset);
	TreeElement *getBayByOffset(int fileOffset);
	TreeElement *getLevelByOffset(int fileOffset);
	TreeElement *getLocationByOffset(int fileOffset);

	BOOL IsDirty();

	TreeElement *getSectionByDBID(long dbid);
	TreeElement *getAisleByDBID(long dbid);
	TreeElement *getSideByDBID(long dbid);
	TreeElement *getBayByDBID(long dbid);
	TreeElement *getLevelByDBID(long dbid);
	TreeElement *getLocationByDBID(long dbid, BOOL getFromDatabase = TRUE);

	int setBtSection(int sectionIdx, qqhSLOTSection &section);
	int setBtAisle(int sectionIdx, int aisleIdx, qqhSLOTAisle &aisle);
	int setBtSide(int sectionIdx, int aisleIdx, int sideIdx, qqhSLOTSide &side);
	int setBtBay(int sectionIdx, int aisleIdx, int sideIdx, int bayIdx, qqhSLOTBay &bay);
	int setBtLevel(int sectionIdx, int aisleIdx, int sideIdx, int bayIdx, 
			 int levelIdx, qqhSLOTLevel &level);
	int setBtLocation(int sectionIdx, int aisleIdx, int sideIdx, int bayIdx, 
				int levelIdx, int locIdx, qqhSLOTLocation &location);

	int getBtSection(int sectionIdx, qqhSLOTSection &section);
	int getBtAisle(int sectionIdx, int aisleIdx, qqhSLOTAisle &aisle);
	int getBtSide(int sectionIdx, int aisleIdx, int sideIdx, qqhSLOTSide &side);
	int getBtBay(int sectionIdx, int aisleIdx, int sideIdx, int bayIdx, qqhSLOTBay &bay);
	int getBtLevel(int sectionIdx, int aisleIdx, int sideIdx, int bayIdx, 
			 int levelIdx, qqhSLOTLevel &level);
	int getBtLocation(int sectionIdx, int aisleIdx, int sideIdx, int bayIdx, 
				int levelIdx, int locIdx, qqhSLOTLocation &location);

	TreeElement *getSection(int sectionIdx);
	TreeElement *getAisle(int sectionIdx, int aisleIdx);
	TreeElement *getSide(int sectionIdx, int aisleIdx, int sideIdx);
	TreeElement *getBay(int sectionIdx, int aisleIdx, int sideIdx, int bayIdx);
	TreeElement *getLevel(int sectionIdx, int aisleIdx, int sideIdx, int bayIdx, int levelIdx);
	TreeElement *getLocation(int sectionIdx, int aisleIdx, int sideIdx, int bayIdx, int levelIdx, int locationIdx);

	int getLocationCountForLevel(int sectionidx, int aisleIdx, int sideIdx, int bayIdx, int levelIdx);
	int getLevelCountForBay(int sectionIdx, int aisleIdx, int sideIdx, int bayIdx);
	int getBayCountForSide(int sectionIdx, int aisleIdx, int sideIdx);
	int getSideCountForAisle(int sectionIdx, int aisleIdx);
	int getAisleCountForSection(int sectionIdx);
	int getSectionCount();

	TreeElement *getSectionByBayHandle(CString &handle);
	TreeElement *getAisleByBayHandle(CString &handle);
	TreeElement *getSideByBayHandle(CString &handle);
	TreeElement *getBayByHandle(CString &handle);
	TreeElement *getLevelByHandle(CString &handle, int dbid);
	TreeElement *getLocationByHandle(CString &handle, int dbid);

	int getBayHandlesByAisle(TreeElement *aislePtr, CStringArray &tempHandles);
	int deleteBranch(CString elementType, TreeElement *elementPtr);
	int getLocationOffsetByFacility(int facilityOffset, CArray<int, int> &locOffsetList);
	int getLocationOffsetBySection(int sectionOffset, CArray<int, int> &locOffsetList);
	int getLocationOffsetByAisle(int aisleOffset, CArray<int, int> &locOffsetList);
	int getLocationOffsetBySide(int sideOffset, CArray<int, int> &locOffsetList);
	int getLocationOffsetByBay(int bayOffset, CArray<int, int> &locOffsetList);
	int getLocationOffsetByLevel(int levelOffset, CArray<int, int> &locOffsetList);

	int TreeElement::getLevelDBIDsByBay(TreeElement *bayPtr, CDWordArray &levelIDList);

	int elementDBID;
	int fileOffset;
	char acadHandle[20];
	char facilityElement[64];
	int sideIndex;
	CArray <TreeElement, TreeElement&> treeChildren;
	TreeElement * treeParent;
	int xCoord;
	TreeElement() {
		elementDBID = 0;
		fileOffset = 0;
		memset(facilityElement,0,64);
		treeParent = NULL;
		memset(acadHandle,0,20);
		xCoord = 0;
		sideIndex = -1;
	}
	TreeElement(const TreeElement& other) {
		elementDBID = other.elementDBID;
		fileOffset = other.fileOffset;
		strcpy(facilityElement,other.facilityElement);
		treeParent = other.treeParent;
		treeChildren.Copy(other.treeChildren);
		strcpy(acadHandle,other.acadHandle);
		xCoord = other.xCoord;
		sideIndex = other.sideIndex;

		int key, value;
		POSITION pos = other.DeletedSectionMap.GetStartPosition();
		while (pos != NULL) {
			other.DeletedSectionMap.GetNextAssoc(pos, key, value);
			DeletedSectionMap.SetAt(key, value);
		}


		pos = other.DeletedAisleMap.GetStartPosition();
		while (pos != NULL) {
			other.DeletedAisleMap.GetNextAssoc(pos, key, value);
			DeletedAisleMap.SetAt(key, value);
		}

		pos = other.DeletedSideMap.GetStartPosition();
		while (pos != NULL) {
			other.DeletedSideMap.GetNextAssoc(pos, key, value);
			DeletedSideMap.SetAt(key, value);
		}
		
		pos = other.DeletedBayMap.GetStartPosition();
		while (pos != NULL) {
			other.DeletedBayMap.GetNextAssoc(pos, key, value);
			DeletedBayMap.SetAt(key, value);
		}

	}
	TreeElement& operator=(const TreeElement& other)
	{
		elementDBID = other.elementDBID;
		fileOffset = other.fileOffset;
		strcpy(facilityElement,other.facilityElement);
		treeParent = other.treeParent;
		treeChildren.Copy(other.treeChildren);
		strcpy(acadHandle,other.acadHandle);
		xCoord = other.xCoord;
		sideIndex = other.sideIndex;

		int key, value;
		POSITION pos = other.DeletedSectionMap.GetStartPosition();
		while (pos != NULL) {
			other.DeletedSectionMap.GetNextAssoc(pos, key, value);
			DeletedSectionMap.SetAt(key, value);
		}


		pos = other.DeletedAisleMap.GetStartPosition();
		while (pos != NULL) {
			other.DeletedAisleMap.GetNextAssoc(pos, key, value);
			DeletedAisleMap.SetAt(key, value);
		}

		pos = other.DeletedSideMap.GetStartPosition();
		while (pos != NULL) {
			other.DeletedSideMap.GetNextAssoc(pos, key, value);
			DeletedSideMap.SetAt(key, value);
		}

		pos = other.DeletedBayMap.GetStartPosition();
		while (pos != NULL) {
			other.DeletedBayMap.GetNextAssoc(pos, key, value);
			DeletedBayMap.SetAt(key, value);
		}

		return *this;
	}
	virtual ~TreeElement() {this->treeChildren.RemoveAll();}
};

#endif

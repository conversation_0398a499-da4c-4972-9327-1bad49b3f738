// ProductGroupCriteriaValue.cpp: implementation of the CProductGroupCriteriaValue class.
//
//////////////////////////////////////////////////////////////////////

#include "stdafx.h"
#include "modal.h"
#include "ProductGroupCriteriaValue.h"

#include <dbsymtb.h>

#ifdef _DEBUG
#undef THIS_FILE
static char THIS_FILE[]=__FILE__;
#define new DEBUG_NEW
#endif

//////////////////////////////////////////////////////////////////////
// Construction/Destruction
//////////////////////////////////////////////////////////////////////

CProductGroupCriteriaValue::CProductGroupCriteriaValue()
{

}

CProductGroupCriteriaValue::~CProductGroupCriteriaValue()
{

}

CProductGroupCriteriaValue& CProductGroupCriteriaValue::operator=(const CProductGroupCriteriaValue &other)
{
	m_CriteriaValueDBID = other.m_CriteriaValueDBID;
	m_Description = other.m_Description;

	return *this;
}

int CProductGroupCriteriaValue::Parse(CString &line)
{
	char *str;
	char *ptr;
	CString tmp;
	int idx;

	tmp = line;

	try {
		// strtok doesn't interpret double pipes as a field
		idx = tmp.Find("||");
		while (idx >= 0) {
			tmp.Insert(idx+1, " ");
			idx = tmp.Find("||");
		}

		str = tmp.GetBuffer(0);
		
		ptr = strtok(str, "|");
		m_CriteriaValueDBID = atoi(ptr);
		ptr = strtok(NULL, "|");
		m_Description = ptr;

		tmp.ReleaseBuffer();
	}
	catch (...) {
		tmp.ReleaseBuffer();
		AfxMessageBox("Error processing criteria value list.\n");
		ads_printf("%s\n", line);
		return -1;
	}

	return 0;
}

BOOL CProductGroupCriteriaValue::IsEqual(CProductGroupCriteriaValue &other)
{
	if (other.m_CriteriaValueDBID != m_CriteriaValueDBID) return FALSE;
	if (other.m_Description != m_Description) return FALSE;

	return TRUE;

}

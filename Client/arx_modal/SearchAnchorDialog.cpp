// SearchAnchorDialog.cpp : implementation file
//

#include "stdafx.h"
#include <afxtempl.h>
#include <afxmt.h>

#include "SearchAnchorDialog.h"
#include "SearchAnchor.h"
#include "ssa_exception.h"
#include "excel8.h"
#include "font.h"
#include "HelpService.h"
#include "ProductGroupDataService.h"
#include "SearchAnchorGenerate.h"
#include "ControlService.h"
#include "Processing.h"
#include "UtilityHelper.h"
#include "SearchAnchorDataService.h"

#include "dbsymtb.h"

#ifdef _DEBUG
#define new DEBUG_NEW
#undef THIS_FILE
static char THIS_FILE[] = __FILE__;
#endif

#define MAX_ROWS 65500


/////////////////////////////////////////////////////////////////////////////
// CSearchAnchorDialog dialog
extern CEvent g_ThreadDone;
extern CHelpService helpService;
extern CUtilityHelper utilityHelper;
extern CControlService controlService;

CSearchAnchorDialog::CSearchAnchorDialog(CWnd* pParent /*=NULL*/)
	: CDialog(CSearchAnchorDialog::IDD, pParent)
{
	//{{AFX_DATA_INIT(CSearchAnchorDialog)
	//}}AFX_DATA_INIT}
}


CSearchAnchorDialog::~CSearchAnchorDialog()
{
	for (int i=0; i < m_SearchAnchorPoints.GetSize(); ++i)
		delete (CSearchAnchor *)m_SearchAnchorPoints[i];
}


void CSearchAnchorDialog::DoDataExchange(CDataExchange* pDX)
{
	CDialog::DoDataExchange(pDX);
	//{{AFX_DATA_MAP(CSearchAnchorDialog)
	DDX_Control(pDX, IDSORT, m_SortButton);
	DDX_Control(pDX, IDEXCEL, m_ExcelButton);
	DDX_Control(pDX, IDC_GRID, m_Grid);
	//}}AFX_DATA_MAP
}


BEGIN_MESSAGE_MAP(CSearchAnchorDialog, CDialog)
	//{{AFX_MSG_MAP(CSearchAnchorDialog)
	ON_BN_CLICKED(IDEXCEL, OnExcel)
	ON_BN_CLICKED(IDSORT, OnSort)
	ON_WM_HELPINFO()
	ON_BN_CLICKED(IDHELP, OnHelp)
	ON_BN_CLICKED(IDC_GENERATE, OnGenerate)
	//}}AFX_MSG_MAP
END_MESSAGE_MAP()

/////////////////////////////////////////////////////////////////////////////
// CSearchAnchorDialog message handlers

BOOL CSearchAnchorDialog::OnInitDialog() 
{
	CDialog::OnInitDialog();
	
	CBitmap bitmap;
	bitmap.LoadBitmap(IDB_EXCEL);
	m_ExcelButton.SetBitmap((HBITMAP)bitmap.Detach());
	
	CBitmap bitmap2;
	bitmap2.LoadBitmap(IDB_SORT_BITMAP);
	m_SortButton.SetBitmap((HBITMAP)bitmap2.Detach());

	LoadSearchAnchorPoints();
	LoadGrid();
	
	m_Grid.SetRow(0);
	m_Grid.SetCol(0);

	LoadToolTips();
	
	m_Grid.m_HelpTopic = "SearchAnchor_Grid";

	return TRUE;  // return TRUE unless you set the focus to a control
	              // EXCEPTION: OCX Property Pages should return FALSE
}

void CSearchAnchorDialog::LoadSearchAnchorPoints()
{
	int rc, i;
	CStringArray sapArray;

	m_Grid.m_AllowInsertDelete = TRUE;
	m_Grid.ResetAlignment(LEFT_ALIGNMENT);

	try {
		CSearchAnchorDataService searchAnchorDataService;
		rc = searchAnchorDataService.GetSearchAnchorPoints(sapArray);
	}
	catch(Ssa_Exception e) {
		char eMsg[1024];
		e.GetMessage(eMsg);
		ads_printf("%s\n", eMsg);
		AfxMessageBox("Error loading search anchor points.");
	}
	catch(...) {
		AfxMessageBox("Error loading search anchor points.");
	}

	if (rc <= 0)
		return;

	for (i=0; i < sapArray.GetSize(); ++i) {
		CSearchAnchor *sap = new CSearchAnchor();
		if (sap->Parse(sapArray[i]) >= 0) {
			m_SearchAnchorPoints.Add((CObject *)sap);
			
		}
	}

	return;

}

void CSearchAnchorDialog::LoadGrid()
{
	CSearchAnchor *sap;
	
	if (m_SearchAnchorPoints.GetSize() > 0)
		m_Grid.SetRows(m_SearchAnchorPoints.GetSize()+1);
	else
		m_Grid.SetRows(2);

	for (int i=0; i < m_SearchAnchorPoints.GetSize(); ++i) {
		sap = (CSearchAnchor *)m_SearchAnchorPoints[i];
		
		m_Grid.SetTextMatrix(i+1, 0, sap->m_StartingLocation);
		m_Grid.SetTextMatrix(i+1, 1, sap->m_EndingLocation);
		m_Grid.SetTextMatrix(i+1, 2, sap->m_SearchAnchorPoint);
			
	}

	return;

}

void CSearchAnchorDialog::OnOK() 
{
	int i, rc;
	CSearchAnchor *pSearchAnchor;

	if (ValidateRange() < 0) {
		return;
	}

	for (i=0; i < m_SearchAnchorPoints.GetSize(); ++i)
		delete (CSearchAnchor *)m_SearchAnchorPoints[i];

	m_SearchAnchorPoints.RemoveAll();
	
	for (i=1; i < m_Grid.GetRows(); ++i) {
		if (m_Grid.GetTextMatrix(i, 0) == "")
			continue;

		pSearchAnchor = new CSearchAnchor();
		pSearchAnchor->m_SearchAnchorDBID = 0;
		pSearchAnchor->m_StartingLocation = m_Grid.GetTextMatrix(i, 0);
		pSearchAnchor->m_EndingLocation = m_Grid.GetTextMatrix(i, 1);
		pSearchAnchor->m_SearchAnchorPoint = m_Grid.GetTextMatrix(i, 2);

		m_SearchAnchorPoints.Add((CObject *)pSearchAnchor);

	}


	try {
		CSearchAnchorDataService searchAnchorDataService;
		rc = searchAnchorDataService.UpdateSearchAnchorPoints(m_SearchAnchorPoints);
	}
	catch(Ssa_Exception e) {
		char eMsg[1024];
		e.GetMessage(eMsg);
		ads_printf("%s\n", eMsg);
		AfxMessageBox("Error updating search anchor points.");
		return;
	}
	catch(...) {
		AfxMessageBox("Error updating search anchor points.");
		return;
	}

	if (rc >= 0)
		AfxMessageBox("Search Anchor Points updated.");
	else {
		AfxMessageBox("Error updating search anchor points.");
		return;
	}

	CDialog::OnOK();

}

void CSearchAnchorDialog::OnExcel() 
{
	CString fileName;
	CString line;
	
	COleSafeArray outer;
	COleSafeArray *inner;
	SAFEARRAYBOUND rgsabound, rgsabound2;
	long index1;
	long index2;

    rgsabound.lLbound = 0;
    rgsabound.cElements = 3;

	rgsabound2.lLbound = 0;
	rgsabound2.cElements = 2;

	DWORD numElements[] = {2};

	outer.Create(VT_VARIANT, 1, &rgsabound);
	
	inner = new COleSafeArray[3];

	// The maximum number of rows in an Excel worksheet is currently
	// 65536.  So if we have more lines than that, we will have to create
	// multiple files, each with 65536 lines until we have used up all the 
	// lines.  Then we do some bizarre Excel stuff to open each file individually
	// and move them all into a single book with multiple sheets.  If there's
	// a better way, please tell me.

	int itemStart = 0;
	int rowCount;

	try {
		FILE *f;
		rowCount = m_Grid.GetRows() - 1;	// subtract one for the header
		for (int sheetCount=1; sheetCount <= rowCount/MAX_ROWS+1; ++sheetCount) {
		
			CString tempF = getenv("TEMP");
			if (tempF.IsEmpty())
				tempF = "c:";

			fileName.Format("%s\\results%d.txt", tempF, sheetCount);
			
			f = fopen(fileName, "w");
		
			// write the header
			for (int j=0; j < 3; ++j) {
				//line = pDoc->m_report.m_txtArray[j];
				//fprintf(f, "\"%s\"|", line);
				// This weirdness is to format each cell as text so it won't
				// take leading zeros off, etc
				// Excel expects an array of two-item arrays; the first
				// item is the column number and the second is the format;
				// text format = 2
				if (sheetCount == 1) {
					inner[j].Create(VT_I2, 1, &rgsabound2);			
					index2 = 0;
					inner[j].PutElement(&index2, &j);
					index2 = 1;
					short k = 2;		// text format
					inner[j].PutElement(&index2, &k);
					index1 = j;
					outer.PutElement(&index1, COleVariant(inner[j]));
				}
				
			}
			
			//fprintf(f, "\n");
			// Add a header to every page
			for (int i=0; i < 3; ++i)
				fprintf(f, "%s|", m_Grid.GetTextMatrix(0, i));

			fprintf(f, "\n");
			
			int itemCount = itemStart + MAX_ROWS;
			if (itemCount > rowCount)
				itemCount = rowCount;

			for (i=itemStart; i < itemCount; ++i) {
				for (int j=0; j < 3; ++j)
					fprintf(f, "%s|", m_Grid.GetTextMatrix(i+1, j));

				fprintf(f, "\n");
			}
			itemStart += MAX_ROWS;

			fclose(f);
		}
		

	}
	catch (...) {
		AfxMessageBox("Error creating file for opening in Excel.");
		return;
	}


	try
	{
		_Application app;     // app is an _Application object.
		_Workbook book, mainBook;       // More object declarations.
		_Worksheet sheet, mainSheet;
		Workbooks books;
		Worksheets sheets, mainSheets;
		Range range;          // Used for Microsoft Excel 97 components.
		LPDISPATCH lpDisp;    // Often reused variable.
		Range cols;

		// Common OLE variants. Easy variants to use for calling arguments.
		COleVariant
			covTrue((short)TRUE),
			covFalse((short)FALSE),
			covOptional((long)DISP_E_PARAMNOTFOUND, VT_ERROR);
		
		// Start Microsoft Excel, get _Application object,
		// and attach to app object.
		if(!app.CreateDispatch("Excel.Application"))
		{
			AfxMessageBox("Couldn't CreateDispatch() for Excel");
			return;
		}
		
		app.SetWindowState(-4140);		// -4140 = xlMinimized
		app.SetVisible(TRUE);
		app.SetUserControl(TRUE);

		// Get the Workbooks collection.
		lpDisp = app.GetWorkbooks();     // Get an IDispatch pointer.
		ASSERT(lpDisp);
		books.AttachDispatch(lpDisp);    // Attach the IDispatch pointer
										// to the books object.

		VARIANT sheetVar = {0};

		for (int sheetCount=1; sheetCount <= rowCount/MAX_ROWS+1; ++sheetCount) {
			
			fileName = getenv("TEMP");
			if (fileName.IsEmpty())
				fileName = "c:";
			
			fileName.Format("%s\\results%d.txt", fileName, sheetCount);
			
			// open the file as text specifiying pipe-delimited
			books.OpenText(fileName, 
				covOptional,				// Origin
				covOptional,				// Start row
				COleVariant((short)(1)),	// Datatype
				1,							// TextQualifier 
				covOptional,				// Consecutive delimiter
				covOptional,				// Tab
				covOptional,				// Semicolon
				covOptional,				// Comma
				covOptional,				// Space
				covTrue,					// Other
				COleVariant("|"),			// OtherChar
				COleVariant(outer),			// FieldInfo
				covOptional);				// TextVisualLayout

		
			// Get the book
			if (sheetCount == 1)
				lpDisp = books.GetItem(COleVariant((short)(1)));
			else
				lpDisp = books.GetItem(COleVariant((short)(2)));
			ASSERT(lpDisp);
			book.AttachDispatch(lpDisp);
			
			// Get the sheets
			lpDisp = book.GetSheets();
			ASSERT(lpDisp);
			sheets.AttachDispatch(lpDisp);
				
			// Get the sheet
			lpDisp = sheets.GetItem(COleVariant((short)1));
			//GetItem(const VARIANT &index)
			ASSERT(lpDisp);
			sheet.AttachDispatch(lpDisp);
			
			CString temp;
			temp.Format("Page %d", sheetCount);
			sheet.SetName(temp);
			
			// Change the entire worksheet to be autofitt
			range = sheet.GetRange(COleVariant("A1"), COleVariant("C1"));


			Font8 font;
			font = range.GetFont();
			font.SetBold(covTrue);

			cols = range.GetEntireColumn();
			cols.AutoFit();
		
			if (sheetCount == 1) {
				// Get the first book and hold it for use by the others
				lpDisp = books.GetItem(COleVariant((short)1));
				ASSERT(lpDisp);
				mainBook.AttachDispatch(lpDisp);
				
				// Same for the sheets
				lpDisp = mainBook.GetSheets();
				ASSERT(lpDisp);
				mainSheets.AttachDispatch(lpDisp);

			}

			else {
				// Get the previous sheet we inserted in the main book
				lpDisp = mainSheets.GetItem(COleVariant((short)(sheetCount-1)));
				ASSERT(lpDisp);
				mainSheet.AttachDispatch(lpDisp);
				
				// Create a Variant to represent the previous sheet object
				// We will use this to copy the subsequent sheets
				sheetVar.vt = VT_DISPATCH;
				sheetVar.pdispVal = mainSheet.m_lpDispatch;
				mainSheet.m_lpDispatch->AddRef();
					
				// Add an additional sheet to the new workbook so that we can 
				// move the new sheet without getting an error because there 
				// are no sheets left because a book must have a minimum of one 
				// sheet and we are moving one
				
				// Use the default placement which will put it first
				_Worksheet tempSheet;
				tempSheet = sheets.Add(covOptional, covOptional, covOptional, covOptional);

				// Now move the new sheet to the main book
				sheet.Move(covOptional, sheetVar);

				VariantClear(&sheetVar);

				// Now close the new workbook without saving
				book.Close(covFalse, covOptional, covOptional);

			}

		}

		lpDisp = mainSheets.GetItem(COleVariant((short)1));
		ASSERT(lpDisp);
		sheet.AttachDispatch(lpDisp);
		sheet.Activate();

		app.SetWindowState(-4143);		// -4143 = xlNormal
		app.ReleaseDispatch();

      } // End of processing.

	  catch(COleException *e)
      {
		  char buf[1024];     // For the Try...Catch error message.
		  sprintf(buf, "COleException. SCODE: %08lx.", (long)e->m_sc);
		  //::MessageBox(NULL, buf, "COleException", MB_SETFOREGROUND | MB_OK);
      }
	  
      catch(COleDispatchException *e)
      {
		  char buf[1024];     // For the Try...Catch error message.
		  sprintf(buf,
			  "COleDispatchException. SCODE: %08lx, Description: \"%s\".",
			  (long)e->m_wCode,(LPSTR)e->m_strDescription.GetBuffer(512));
		  //::MessageBox(NULL, buf, "COleDispatchException",
		  //MB_SETFOREGROUND | MB_OK);
	  }
	  
      catch(...)
      {
		  //::MessageBox(NULL, "General Exception caught.", "Catch-All",
		  //MB_SETFOREGROUND | MB_OK);
      }

	  // Clean up the format arrays
	  for (int i=0; i < 3; ++i) {
		  //inner[i].Destroy();	
		  inner[i].Detach();	
	  }
	  delete [] inner;
	  outer.Detach();


	  return;

	
}

void CSearchAnchorDialog::OnSort() 
{
	CSsaStringArray sortList;
	CString txt;
	CSearchAnchor sa;

	for (int i=0; i < m_Grid.GetRows()-1; ++i) {
		txt.Format("%-255s|%-255s|%-255s|0|",
			m_Grid.GetTextMatrix(i+1, 0),
			m_Grid.GetTextMatrix(i+1, 1),
			m_Grid.GetTextMatrix(i+1, 2));
		sortList.Add(txt);
	}

	utilityHelper.SortStringArray(sortList);

	for (i=0; i < sortList.GetSize(); ++i) {
		//m_Grid.SetRow(i+1);
		//m_Grid.Clear();
		txt = sortList[i];
		sa.Parse(txt);
		m_Grid.SetTextMatrix(i+1, 0, sa.m_StartingLocation);
		m_Grid.SetTextMatrix(i+1, 1, sa.m_EndingLocation);
		m_Grid.SetTextMatrix(i+1, 2, sa.m_SearchAnchorPoint);
	}

	m_Grid.Invalidate(TRUE);

}


void CSearchAnchorDialog::LoadToolTips()
{

	EnableToolTips(TRUE);

	m_ToolTip.Create(this, TTF_IDISHWND);
	m_ToolTip.Activate(TRUE);
	
	m_ToolTip.AddTool(GetDlgItem(IDEXCEL),"View in Excel");
	m_ToolTip.AddTool(GetDlgItem(IDCANCEL),"Cancel changes and exit");
	m_ToolTip.AddTool(GetDlgItem(IDHELP),"View online help");
	m_ToolTip.AddTool(GetDlgItem(IDOK),"Save changes and exit");
	m_ToolTip.AddTool(GetDlgItem(IDSORT), "Sort by starting location");

}


BOOL CSearchAnchorDialog::PreTranslateMessage(MSG* pMsg) 
{
	// TODO: Add your specialized code here and/or call the base class
	
	if(pMsg->message== WM_LBUTTONDOWN ||
        pMsg->message== WM_LBUTTONUP ||
        pMsg->message== WM_MOUSEMOVE)
		m_ToolTip.RelayEvent(pMsg);


	return CDialog::PreTranslateMessage(pMsg);
}


int CSearchAnchorDialog::ValidateRange()
{
	CString start1, end1, start2, end2;
	CString txt;

	for (int i=1; i < m_Grid.GetRows(); ++i) {
		start1 = m_Grid.GetTextMatrix(i, 0);
		end1 = m_Grid.GetTextMatrix(i, 1);

		for (int j=1; j < m_Grid.GetRows(); ++j) {
			if (i == j)
				continue;
			start2 = m_Grid.GetTextMatrix(j, 0);
			end2 = m_Grid.GetTextMatrix(j, 1);

			if (start1.Compare(start2) >= 0 && start1.Compare(end2) <= 0) {
				txt.Format("The following ranges overlap:\n%s - %s\n%s - %s\nPlease correct this before saving.",
					start1, end1, start2, end2);
				AfxMessageBox(txt);
				return -1;
			}
			else if (end1.Compare(start2) >= 0 && end1.Compare(end2) <= 0) {
				txt.Format("The following ranges overlap:\n%s - %s\n%s - %s\nPlease correct this before saving.",
					start1, end1, start2, end2);
				AfxMessageBox(txt);
				return -1;
			}

		}

	}

	return 0;

}

BOOL CSearchAnchorDialog::OnHelpInfo(HELPINFO* pHelpInfo) 
{
	helpService.ShowFieldHelp(IDD, pHelpInfo->iCtrlId);
	return FALSE;
}

void CSearchAnchorDialog::OnHelp() 
{
	helpService.ShowScreenHelp(IDD);	
}

void CSearchAnchorDialog::OnGenerate() 
{
	// select all the selection locations in the facility and the product group
	// they are assigned to, sorted by the location name
	// for each product group, find the first location in the facility and
	// set it to be the starting range
	// continue until the subsequent location is a different product group
	// then set the previous one to be the ending range; find the next one
	// in the group; repea
	CSearchAnchorGenerate sagdlg;

	if (sagdlg.DoModal() != IDOK)
		return;

	if (sagdlg.m_ClearExisting) {
		m_Grid.SetRows(2);
		m_Grid.SetTextMatrix(1, 0, "");
		m_Grid.SetTextMatrix(1, 1, "");
		m_Grid.SetTextMatrix(1, 2, "");
	}
	m_ProductGroupID = sagdlg.m_ProductGroupID;

	CWaitCursor cwc;

	CProcessing *dlg = new CProcessing();
	dlg->Create(IDD_PROCESSING);

	try {
		dlg->m_StatusText = "Generating Search Anchor Points";
		dlg->UpdateData(FALSE);
		dlg->CenterWindow();
		dlg->ShowWindow(SW_SHOW);
		dlg->UpdateWindow();

		CWinThread *pThread = AfxBeginThread(CSearchAnchorDialog::GenerateSearchAnchorPointsThread, this);
		HANDLE hThread = pThread->m_hThread;

		BOOL bThreadDone = false;
		while (TRUE) {
			if ( ! utilityHelper.PeekAndPump() )
				break;
			
			bThreadDone = g_ThreadDone.Lock(0);
			if (bThreadDone)
				break;
		}
		
		dlg->DestroyWindow();

	}
	catch(Ssa_Exception e) 
	{
		char eMsg[1024];
		e.GetMessage(eMsg);
		ads_printf("%s\n", eMsg);
		dlg->DestroyWindow();
		this->MessageBox("An error occurred while generating the search anchor points.", "Error", MB_ICONERROR);
		return;
	}	
	
	if (m_ThreadMessage != "") {
		this->MessageBox(m_ThreadMessage, "Error", MB_ICONERROR);
		return;
	}

	return;


}



UINT CSearchAnchorDialog::GenerateSearchAnchorPointsThread(LPVOID pParam)
{
	CSearchAnchorDialog *pDlg = (CSearchAnchorDialog *)pParam;
	CStringArray locList, productGroupList;
	CProductGroupDataService service;
	CProductGroup productGroup;
	CString pgName, temp, loc, startingLoc, endingLoc, middleLoc;
	CMap <long, long, CString, CString> pgMap;
	long pgID, tempId, locIdx, startingLocIdx, endingLocIdx, middleLocIdx, rows;
	int idx;
	CStringArray sapList;

	try {
		service.GetProductGroups(controlService.GetCurrentFacilityDBId(), productGroupList);
	}
	catch (Ssa_Exception e) {
		pDlg->m_ThreadMessage = "Error getting product groups.";
		g_ThreadDone.SetEvent();
		return 0;
	}
	catch (...) {
		pDlg->m_ThreadMessage = "Error getting product groups.";
		g_ThreadDone.SetEvent();
		return 0;
	}

	if (productGroupList.GetSize() == 0) {
		g_ThreadDone.SetEvent();
		return 0;
	}

	try {
		CSearchAnchorDataService searchAnchorDataService;
		searchAnchorDataService.GetSearchAnchorLocations(locList);
	}
	catch (Ssa_Exception e) {
		pDlg->m_ThreadMessage.Format("Error getting locations.");
		g_ThreadDone.SetEvent();
		return 0;
	}
	catch (...) {
		pDlg->m_ThreadMessage.Format("Error getting locations.");
		g_ThreadDone.SetEvent();
		return 0;
	}

	locIdx = 0;
	for (int pgIdx = 0; pgIdx < productGroupList.GetSize(); ++pgIdx) {
		productGroup.Parse(productGroupList[pgIdx]);
		pgID = productGroup.m_ProductGroupDBID;
		
		if (pDlg->m_ProductGroupID > 0 && pDlg->m_ProductGroupID != pgID)
			continue;

		startingLoc = "";
		endingLoc = "";
		startingLocIdx = endingLocIdx = -1;
		locIdx = 0;

		while (locIdx < locList.GetSize()) {
			temp = locList[locIdx];
			idx = temp.Find("|");
			tempId = atol(temp.Left(idx));
			loc = temp.Mid(idx+1);
			loc.TrimRight("|");

			if (tempId == pgID) {
				if (startingLocIdx < 0) {
					startingLocIdx = locIdx;
					startingLoc = loc;
				}
				endingLocIdx = locIdx;
				endingLoc = loc;
			}
			else {
				if (startingLocIdx >= 0) {
					rows = pDlg->m_Grid.GetRows();
					if (rows > 2 || pDlg->m_Grid.GetTextMatrix(1, 0) != "")
						pDlg->m_Grid.SetRows(++rows);
					rows--;
					pDlg->m_Grid.SetTextMatrix(rows, 0, startingLoc);
					pDlg->m_Grid.SetTextMatrix(rows, 1, endingLoc);
					middleLocIdx = startingLocIdx + (int)((endingLocIdx-startingLocIdx)/2);
					temp = locList[middleLocIdx];
					middleLoc = temp.Mid(temp.Find("|")+1);
					middleLoc.TrimRight("|");
					pDlg->m_Grid.SetTextMatrix(rows, 2, middleLoc);
					
					startingLoc = "";
					endingLoc = "";
					startingLocIdx = -1;
					endingLocIdx = -1;
				}
			}
			locIdx++;
					
		}
		
		// Get the last one if there is one
		if (startingLocIdx >= 0) {
			rows = pDlg->m_Grid.GetRows();
			if (rows > 2 || pDlg->m_Grid.GetTextMatrix(1, 0) != "")
				pDlg->m_Grid.SetRows(++rows);
			rows--;
			pDlg->m_Grid.SetTextMatrix(rows, 0, startingLoc);
			pDlg->m_Grid.SetTextMatrix(rows, 1, endingLoc);
			middleLocIdx = startingLocIdx + (int)((endingLocIdx-startingLocIdx)/2);
			temp = locList[middleLocIdx];
			middleLoc = temp.Mid(temp.Find("|")+1);
			middleLoc.TrimRight("|");
			pDlg->m_Grid.SetTextMatrix(rows, 2, middleLoc);
		}
		

	}

	pDlg->m_ThreadMessage = "";
	g_ThreadDone.SetEvent();

	return 0;

}

// AssignConnectionDialog.cpp : implementation file
//

#include "stdafx.h"
#include "modal.h"
#include "AssignConnectionDialog.h"
#include "IntegrationDataService.h"
#include "ControlService.h"
#include "DefineConnectionDialog.h"
#include "HelpService.h"

#ifdef _DEBUG
#define new DEBUG_NEW
#undef THIS_FILE
static char THIS_FILE[] = __FILE__;
#endif

extern CIntegrationDataService integrationDataService;
extern CControlService controlService;
extern CHelpService helpService;

/////////////////////////////////////////////////////////////////////////////
// CAssignConnectionDialog dialog


CAssignConnectionDialog::CAssignConnectionDialog(CWnd* pParent /*=NULL*/)
	: CDialog(CAssignConnectionDialog::IDD, pParent)
{
	//{{AFX_DATA_INIT(CAssignConnectionDialog)
		// NOTE: the ClassWizard will add member initialization here
	//}}AFX_DATA_INIT
}


void CAssignConnectionDialog::DoDataExchange(CDataExchange* pDX)
{
	CDialog::DoDataExchange(pDX);
	//{{AFX_DATA_MAP(CAssignConnectionDialog)
	DDX_Control(pDX, IDC_CONNECTION_LIST, m_ConnectionListCtrl);
	DDX_Control(pDX, IDC_INTERFACE_TREE, m_InterfaceTreeCtrl);
	//}}AFX_DATA_MAP
}

CAssignConnectionDialog::~CAssignConnectionDialog()
{
	for (int i=0; i < m_ConnectionListCtrl.GetItemCount(); ++i) {
		CExternalConnection *pConn = (CExternalConnection *)m_ConnectionListCtrl.GetItemData(i);
		delete pConn;
	}

}

BEGIN_MESSAGE_MAP(CAssignConnectionDialog, CDialog)
	//{{AFX_MSG_MAP(CAssignConnectionDialog)
	ON_BN_CLICKED(IDC_ASSIGN, OnAssign)
	ON_BN_CLICKED(IDC_DELETE, OnDelete)
	ON_BN_CLICKED(IDC_EDIT, OnEdit)
	ON_BN_CLICKED(IDC_NEW, OnNew)
	ON_BN_CLICKED(IDC_REMOVE, OnRemove)
	ON_BN_CLICKED(IDHELP, OnHelp)
	ON_WM_HELPINFO()
	//}}AFX_MSG_MAP
END_MESSAGE_MAP()

/////////////////////////////////////////////////////////////////////////////
// CAssignConnectionDialog message handlers

BOOL CAssignConnectionDialog::OnInitDialog() 
{
	CDialog::OnInitDialog();
	
	CRect r;
	m_ConnectionListCtrl.GetClientRect(&r);

	for (int i=0; i < m_ConnectionListCtrl.GetHeaderCtrl()->GetItemCount(); ++i)
		m_ConnectionListCtrl.DeleteColumn(0);

	m_ConnectionListCtrl.InsertColumn(0, "Connection Name", LVCFMT_LEFT, r.Width()/2);
	m_ConnectionListCtrl.InsertColumn(1, "Transport Type", LVCFMT_LEFT, r.Width()/2);

	LoadConnectionList();
	
	LoadInterfaceTree();

	LoadConnectionsForGroup();

	return TRUE;  // return TRUE unless you set the focus to a control
	              // EXCEPTION: OCX Property Pages should return FALSE
}

void CAssignConnectionDialog::OnAssign() 
{
	// todo: messages at all these checks for none selected
	POSITION pos = m_ConnectionListCtrl.GetFirstSelectedItemPosition();
	if (pos == NULL)
		return;

	int curSel = m_ConnectionListCtrl.GetNextSelectedItem(pos);
	if (curSel < 0)
		return;
	
	HTREEITEM hItem = m_InterfaceTreeCtrl.GetSelectedItem();
	if (hItem == NULL)
		return;

	int direction, interfaceType;

	int param = m_InterfaceTreeCtrl.GetItemData(hItem);
	direction = LOWORD(param);
	interfaceType = HIWORD(param);

	CExternalConnection *pConnection = (CExternalConnection *)m_ConnectionListCtrl.GetItemData(curSel);
	
	CString temp = m_InterfaceTreeCtrl.GetItemText(hItem);
	if (temp.Find(" - ") >= 0)
		OnRemove();


	CWMSGroupConnection *pConn = new CWMSGroupConnection;
	pConn->m_WMSGroupDBId = m_pGroup->m_WMSGroupDBId;
	pConn->m_GroupName = m_pGroup->m_Name;
	pConn->m_ExternalConnectionDBId = pConnection->m_ExternalConnectionDBId;
	pConn->m_ConnectionName = pConnection->m_Name;
	pConn->m_Direction = direction;
	pConn->m_InterfaceType = interfaceType;
	pConn->m_pExternalConnection = new CExternalConnection(*pConnection);

	m_pGroup->m_ConnectionList.Add(pConn);

	LoadConnectionsForGroup();

}

void CAssignConnectionDialog::OnDelete() 
{
	POSITION pos = m_ConnectionListCtrl.GetFirstSelectedItemPosition();
	if (pos == NULL)
		return;

	int curSel = m_ConnectionListCtrl.GetNextSelectedItem(pos);
	if (curSel < 0)
		return;
	
	CExternalConnection *pConn = (CExternalConnection *)m_ConnectionListCtrl.GetItemData(curSel);

	try {
		integrationDataService.DeleteExternalConnection(pConn->m_ExternalConnectionDBId);
	}
	catch (...) {
		controlService.Log("Error deleting connection.", "Generic exception in DeleteExternalConnection.\n");
		return;
	}

	for (int i=0; i < m_ConnectionList.GetSize(); ++i) {
		if (m_ConnectionList[i]->m_ExternalConnectionDBId == pConn->m_ExternalConnectionDBId) {
			m_ConnectionList.RemoveAt(i);
			break;
		}
	}

	delete pConn;

	m_ConnectionListCtrl.DeleteItem(curSel);

		
}

void CAssignConnectionDialog::OnEdit() 
{
	POSITION pos = m_ConnectionListCtrl.GetFirstSelectedItemPosition();
	if (pos == NULL)
		return;

	int curSel = m_ConnectionListCtrl.GetNextSelectedItem(pos);
	if (curSel < 0)
		return;

	CDefineConnectionDialog dlg;
	
	CExternalConnection *pConn = (CExternalConnection *)m_ConnectionListCtrl.GetItemData(curSel);
	CExternalConnection conn = *pConn;
	dlg.m_pConnection = &conn;

	try {
		if (dlg.DoModal() != IDOK)
			return;
	}
	catch (...) {
		controlService.Log("Error displaying Define Connection dialog.",
			"Generic exception in CDefineConnectionDialog.\n");
		return;
	}

	if (pConn->m_Name != conn.m_Name) {
		m_ConnectionListCtrl.SetItemText(curSel, 0, conn.m_Name);
		LoadConnectionsForGroup();
	}


	if (pConn->m_ConnectionType != conn.m_ConnectionType)
		m_ConnectionListCtrl.SetItemText(curSel, 1, conn.ConnectionTypeAsText());

	*pConn = conn;	

}

void CAssignConnectionDialog::OnNew() 
{
	CDefineConnectionDialog dlg;
	
	CExternalConnection conn;

	dlg.m_pConnection = &conn;

	try {
		if (dlg.DoModal() != IDOK)
			return;
	}
	catch (...) {
		controlService.Log("Error displaying Define Connection dialog.",
			"Generic exception in CDefineConnectionDialog.\n");
		return;
	}

	CExternalConnection *pConn = new CExternalConnection(conn);
	m_ConnectionList.Add(pConn);
	AddConnectionToList(*pConn);

}

void CAssignConnectionDialog::OnRemove() 
{
	
	HTREEITEM hItem = m_InterfaceTreeCtrl.GetSelectedItem();
	if (hItem == NULL)
		return;

	CString temp = m_InterfaceTreeCtrl.GetItemText(hItem);
	int idx = temp.Find(" - ");
	if (idx < 0)
		return;

	temp = temp.Left(idx);
	m_InterfaceTreeCtrl.SetItemText(hItem, temp);

	int direction, interfaceType;
	int param = m_InterfaceTreeCtrl.GetItemData(hItem);
	direction = LOWORD(param);
	interfaceType = HIWORD(param);

	for (int i=0; i < m_pGroup->m_ConnectionList.GetSize(); ++i) {
		CWMSGroupConnection *pGConn = m_pGroup->m_ConnectionList[i];

		if (pGConn->m_Direction == direction && pGConn->m_InterfaceType == interfaceType) {
			delete pGConn;
			m_pGroup->m_ConnectionList.RemoveAt(i);
			break;
		}
	}

}


int CAssignConnectionDialog::LoadConnectionList()
{
	CStringArray connectionList;

	try {
		integrationDataService.GetExternalConnectionList(connectionList);
	}
	catch (...) {
		controlService.Log("Error loading external connections.", "Generic exception in GetExternalConnectionList.\n");
		return -1;
	}

	for (int i=0; i < connectionList.GetSize(); ++i) {
		CExternalConnection *pConnection = new CExternalConnection;
		pConnection->Parse(connectionList[i]);
		m_ConnectionList.Add(pConnection);

		AddConnectionToList(*pConnection);
	}

	return 0;
}

int CAssignConnectionDialog::LoadInterfaceTree()
{
	HTREEITEM hItem;

	hItem = m_InterfaceTreeCtrl.InsertItem("All Interfaces", TVI_ROOT, TVI_LAST);
	m_InterfaceTreeCtrl.SetItemData(hItem, MAKELPARAM(CWMSGroupConnection::Both, CWMSGroupConnection::All));
	m_InterfaceTreeMap[MAKELPARAM(CWMSGroupConnection::Both, CWMSGroupConnection::All)] = hItem;

	HTREEITEM hInboundItem = m_InterfaceTreeCtrl.InsertItem("Inbound Interfaces (To Optimize)", hItem, TVI_LAST);
	m_InterfaceTreeCtrl.SetItemData(hInboundItem, MAKELPARAM(CWMSGroupConnection::Inbound, CWMSGroupConnection::All));
	m_InterfaceTreeMap[MAKELPARAM(CWMSGroupConnection::Inbound, CWMSGroupConnection::All)] = hInboundItem;

	HTREEITEM hOutboundItem = m_InterfaceTreeCtrl.InsertItem("Outbound Interfaces (From Optimize)", hItem, TVI_LAST);
	m_InterfaceTreeCtrl.SetItemData(hOutboundItem, MAKELPARAM(CWMSGroupConnection::Outbound, CWMSGroupConnection::All));
	m_InterfaceTreeMap[MAKELPARAM(CWMSGroupConnection::Outbound, CWMSGroupConnection::All)] = hOutboundItem;


	// Inbound

	hItem = m_InterfaceTreeCtrl.InsertItem("Location", hInboundItem, TVI_LAST);
	m_InterfaceTreeCtrl.SetItemData(hItem, MAKELPARAM(CWMSGroupConnection::Inbound, CWMSGroupConnection::LocationInterface));
	m_InterfaceTreeMap[MAKELPARAM(CWMSGroupConnection::Inbound, CWMSGroupConnection::LocationInterface)] = hItem;

	hItem = m_InterfaceTreeCtrl.InsertItem("Product", hInboundItem, TVI_LAST);
	m_InterfaceTreeCtrl.SetItemData(hItem, MAKELPARAM(CWMSGroupConnection::Inbound, CWMSGroupConnection::ProductInterface));
	m_InterfaceTreeMap[MAKELPARAM(CWMSGroupConnection::Inbound, CWMSGroupConnection::ProductInterface)] = hItem;

	hItem = m_InterfaceTreeCtrl.InsertItem("Assignment/Move", hInboundItem, TVI_LAST);
	m_InterfaceTreeCtrl.SetItemData(hItem, MAKELPARAM(CWMSGroupConnection::Inbound, CWMSGroupConnection::AssignmentInterface));
	m_InterfaceTreeMap[MAKELPARAM(CWMSGroupConnection::Inbound, CWMSGroupConnection::AssignmentInterface)] = hItem;

	HTREEITEM hConfItem = m_InterfaceTreeCtrl.InsertItem("Confirmation Interfaces", hInboundItem, TVI_LAST);
	m_InterfaceTreeCtrl.SetItemData(hConfItem, MAKELPARAM(CWMSGroupConnection::Inbound, CWMSGroupConnection::Confirmation));
	m_InterfaceTreeMap[MAKELPARAM(CWMSGroupConnection::Inbound, CWMSGroupConnection::Confirmation)] = hConfItem;

	hItem = m_InterfaceTreeCtrl.InsertItem("Location Confirmation", hConfItem, TVI_LAST);
	m_InterfaceTreeCtrl.SetItemData(hItem, MAKELPARAM(CWMSGroupConnection::Inbound, CWMSGroupConnection::LocationConfirmationInterface));
	m_InterfaceTreeMap[MAKELPARAM(CWMSGroupConnection::Inbound, CWMSGroupConnection::LocationConfirmationInterface)] = hItem;

	hItem = m_InterfaceTreeCtrl.InsertItem("Assignment/Move Confirmation", hConfItem, TVI_LAST);
	m_InterfaceTreeCtrl.SetItemData(hItem, MAKELPARAM(CWMSGroupConnection::Inbound, CWMSGroupConnection::AssignmentConfirmationInterface));
	m_InterfaceTreeMap[MAKELPARAM(CWMSGroupConnection::Inbound, CWMSGroupConnection::AssignmentConfirmationInterface)] = hItem;
	
	// Outbound

	hItem = m_InterfaceTreeCtrl.InsertItem("Location", hOutboundItem, TVI_LAST);
	m_InterfaceTreeCtrl.SetItemData(hItem, MAKELPARAM(CWMSGroupConnection::Outbound, CWMSGroupConnection::LocationInterface));
	m_InterfaceTreeMap[MAKELPARAM(CWMSGroupConnection::Outbound, CWMSGroupConnection::LocationInterface)] = hItem;
	
	hItem = m_InterfaceTreeCtrl.InsertItem("Assignment/Move", hOutboundItem, TVI_LAST);
	m_InterfaceTreeCtrl.SetItemData(hItem, MAKELPARAM(CWMSGroupConnection::Outbound, CWMSGroupConnection::AssignmentInterface));
	m_InterfaceTreeMap[MAKELPARAM(CWMSGroupConnection::Outbound, CWMSGroupConnection::AssignmentInterface)] = hItem;

	hConfItem = m_InterfaceTreeCtrl.InsertItem("Confirmation Interfaces", hOutboundItem, TVI_LAST);
	m_InterfaceTreeCtrl.SetItemData(hConfItem, MAKELPARAM(CWMSGroupConnection::Outbound, CWMSGroupConnection::Confirmation));
	m_InterfaceTreeMap[MAKELPARAM(CWMSGroupConnection::Outbound, CWMSGroupConnection::Confirmation)] = hConfItem;

	hItem = m_InterfaceTreeCtrl.InsertItem("Product Confirmation", hConfItem, TVI_LAST);
	m_InterfaceTreeCtrl.SetItemData(hItem, MAKELPARAM(CWMSGroupConnection::Outbound, CWMSGroupConnection::ProductConfirmationInterface));
	m_InterfaceTreeMap[MAKELPARAM(CWMSGroupConnection::Outbound, CWMSGroupConnection::ProductConfirmationInterface)] = hItem;

	hItem = m_InterfaceTreeCtrl.InsertItem("Assignment/Move Confirmation", hConfItem, TVI_LAST);
	m_InterfaceTreeCtrl.SetItemData(hItem, MAKELPARAM(CWMSGroupConnection::Outbound, CWMSGroupConnection::AssignmentConfirmationInterface));
	m_InterfaceTreeMap[MAKELPARAM(CWMSGroupConnection::Outbound, CWMSGroupConnection::AssignmentConfirmationInterface)] = hItem;

    return 1;
}

int CAssignConnectionDialog::AddConnectionToList(CExternalConnection &connection)
{
	int nItem = m_ConnectionListCtrl.InsertItem(m_ConnectionListCtrl.GetItemCount(), connection.m_Name);
	m_ConnectionListCtrl.SetItemText(nItem, 1, connection.ConnectionTypeAsText());
	m_ConnectionListCtrl.SetItemData(nItem, (unsigned long)&connection);

	return 0;
}

int CAssignConnectionDialog::LoadConnectionsForGroup()
{
	for (int i=0; i < m_pGroup->m_ConnectionList.GetSize(); ++i) {
		CWMSGroupConnection *pConn = m_pGroup->m_ConnectionList[i];
		
		HTREEITEM hItem;
		int param = MAKELPARAM(pConn->m_Direction, pConn->m_InterfaceType);

		if (m_InterfaceTreeMap.Lookup(param, hItem)) {
			CString temp = m_InterfaceTreeCtrl.GetItemText(hItem);
			int idx = temp.Find(" - ");
			if (idx >= 0)
				temp = temp.Left(idx);

			temp += " - ";
			temp += pConn->m_ConnectionName;
			m_InterfaceTreeCtrl.SetItemText(hItem, temp);
		}

	}


	return 0;
}

BOOL CAssignConnectionDialog::OnHelpInfo(HELPINFO* pHelpInfo) 
{
	helpService.ShowFieldHelp(IDD, pHelpInfo->iCtrlId);

	return FALSE;

}

void CAssignConnectionDialog::OnHelp() 
{
	helpService.ShowScreenHelp(IDD);

	return;

}

//////////////////////////////////////////////////////////////////////
// File Name : OptimizationHelper.cpp
// Classname :
// Description : Implementation of the COptimizationHelper class.
// Date Created :
// Author :
//////////////////////////////////////////////////////////////////////
// Revisions :
//   Date(mm-dd-yyyy) : initials : Description_of_revision
//   03-02-2006       : SMB      : Add Prolog
//////////////////////////////////////////////////////////////////////
	/*******************************************************
	*
	*                       NOTICE
	*
	*  THIS SOFTWARE IS THE PROPERTY OF AND CONTAINS
	*  CONFIDENTIAL INFORMATION OF SSA GLOBAL
	*  TECHNOLOGIES, INC., AND SHALL NOT BE COPIED,
	*  USED, NOR DISCLOSED WITHOUT EXPRESS WRITTEN
	*  AUTHORIZATION.  ALTHOUGH PUBLICATION IS NOT
	*  INTENDED, IN THE EVENT OF PUBLICATION, THE
	*  FOLLOWING NOTICE IS APPLICABLE:
	*
	*  (c) COPYRIGHT 2004 SSA GLOBAL TECHNOLOGIES, INC.
	*
	*           SSA GLOBAL TECHNOLOGIES, INC.
	*
	********************************************************/
//////////////////////////////////////////////////////////////////////

#include "stdafx.h"
#include "modal.h"
#include "OptimizationHelper.h"
#include "ProductGroupHelper.h"
#include "BTreeHelper.h"
#include "UtilityHelper.h"
#include "LocationNumberingService.h"
#include "ProductGroupDataService.h"
#include "FacilityDataService.h"
#include "BayProfileDataService.h"
#include "ForteService.h"
#include "SolutionDataService.h"
#include "ControlService.h"

#include "TreeElement.h"
#include "AssignSlotGroup.h"

#include "SSACStringArray.h"
#include "CapitalCostDialog.h"
#include "LayoutGroupsDialog.h"
#include "DisplayResults.h"
#include "ssa_exception.h"
#include "constants.h"
#include "ManualAssignmentDialog.h"
#include "ProcessingMessage.h"
#include "CostComparisonDialog.h"
#include "Solution.h"
#include "BTreeHelper.h"
#include "ThreadParameters.h"

#include "ProductLayoutStartPage.h"
#include "ProductLayoutAdvancedPage.h"

#include "FacilityDataService.h"
#include "FacilityHelper.h"

#include <afxmt.h>

#include <adscodes.h>
#include <aced.h>

#include "base.h"
#include "MiscClasses.h"
#include "SLOTSessionMgr.h"


#ifdef _DEBUG
#undef THIS_FILE
static char THIS_FILE[]=__FILE__;
#define new DEBUG_NEW
#endif

extern TreeElement changesTree;
CEvent g_ThreadDone;

extern CControlService controlService;
extern CUtilityHelper utilityHelper;
extern CForteService forteService;
extern CBTreeHelper bTreeHelper;
extern CFacilityDataService facilityDataService;
extern CFacilityHelper facilityHelper;


//////////////////////////////////////////////////////////////////////
// Function Name : COptimizationHelper
// Classname : COptimizationHelper
// Description : Construction
// Date Created :
// Author :
//////////////////////////////////////////////////////////////////////
// Inputs : None
// Outputs : None
// Explanation :
//////////////////////////////////////////////////////////////////////
// Revisions :
//   Date(mm-dd-yyyy) : initials : Description_of_revision
//   03-02-2006       : SMB      : Add Prolog
//////////////////////////////////////////////////////////////////////
COptimizationHelper::COptimizationHelper()
{

} //END COptimizationHelper

//////////////////////////////////////////////////////////////////////
// Function Name : COptimizationHelper
// Classname : COptimizationHelper
// Description : Destruction
// Date Created :
// Author :
//////////////////////////////////////////////////////////////////////
// Inputs : None
// Outputs : None
// Explanation :
//////////////////////////////////////////////////////////////////////
// Revisions :
//   Date(mm-dd-yyyy) : initials : Description_of_revision
//   03-02-2006       : SMB      : Add Prolog
//////////////////////////////////////////////////////////////////////
COptimizationHelper::~COptimizationHelper()
{

} //END ~COptimizationHelper

//////////////////////////////////////////////////////////////////////
// Function Name : AssignProductGroup
// Classname : COptimizationHelper
// Description :
// Date Created :
// Author :
//////////////////////////////////////////////////////////////////////
// Inputs : None
// Outputs : None
// Explanation :
//////////////////////////////////////////////////////////////////////
// Revisions :
//   Date(mm-dd-yyyy) : initials : Description_of_revision
//   03-02-2006       : SMB      : Add Prolog
//////////////////////////////////////////////////////////////////////
void COptimizationHelper::AssignProductGroup()
{
	CAssignSlotGroup dlg;
	dlg.DoModal();
}//END AssignProductGroup

//////////////////////////////////////////////////////////////////////
// Function Name : UnassignProductGroup
// Classname : COptimizationHelper
// Description :
// Date Created :
// Author :
//////////////////////////////////////////////////////////////////////
// Inputs : None
// Outputs : None
// Explanation :
//////////////////////////////////////////////////////////////////////
// Revisions :
//   Date(mm-dd-yyyy) : initials : Description_of_revision
//   03-02-2006       : SMB      : Add Prolog
//////////////////////////////////////////////////////////////////////
void COptimizationHelper::UnassignProductGroup()
{
	CArray <int, int&> bayDBIDs;
	int found;
	CWinApp * currentApp;
	currentApp = AfxGetApp();
	CBTreeHelper btHelper;

	//////////////////////////////////////////////////////////////////////
	// Find the bay(s) chosen
	//////////////////////////////////////////////////////////////////////
	ads_name ss;
	if (ads_ssget("P", NULL, NULL, NULL, ss) != RTNORM)
	{
		AfxMessageBox("Please select one or more bays before running this command.");
		return;
	}//END if (ads_ssget("P", NULL, NULL, NULL, ss) != RTNORM)

	long nLength;
	ads_sslength(ss, &nLength);
	ads_name E_name;

	//////////////////////////////////////////////////////////////////////
	// Add the bays to the list
	//////////////////////////////////////////////////////////////////////
	if (nLength == 1)
	{
		if ( AfxMessageBox("Are you sure you want unassign\nthis bay?",MB_YESNO) == IDNO )
			return;
	}//END (nLength == 1)
	else
	{
		if ( AfxMessageBox("Are you sure you want unassign\nthese bays?",MB_YESNO) == IDNO )
			return;
	} //END else

	currentApp->DoWaitCursor(1);
	for (int n=0; n < nLength; n++)
	{
		Acad::ErrorStatus errorStatus;
		AcDbHandle ObjHandle;
		AcDbEntity * entityPtr;
		char acadHandle[20];

		ads_ssname(ss, n, E_name);
		AcDbObjectId objId;
		acdbGetObjectId(objId, E_name);
		errorStatus = acdbOpenAcDbEntity(entityPtr, objId, AcDb::kForRead);
		entityPtr->getAcDbHandle(ObjHandle);
		ObjHandle.getIntoAsciiBuffer(acadHandle);
		ads_printf("Got Bay handle %s\n",acadHandle);
		entityPtr->close();
		found = 0;

		//////////////////////////////////////////////////////////////////////
		// Check to see if it is in the temp area (recent changes)
		//////////////////////////////////////////////////////////////////////
		for (int sectionIndex=0; sectionIndex < changesTree.treeChildren.GetSize(); sectionIndex++)
		{
			for (int aisleIndex=0; aisleIndex < changesTree.treeChildren[sectionIndex].treeChildren.GetSize(); aisleIndex++)
			{
				for (int sideIndex=0; sideIndex < changesTree.treeChildren[sectionIndex].treeChildren[aisleIndex].treeChildren.GetSize(); sideIndex++)
				{
					for (int bayIndex=0; bayIndex < changesTree.treeChildren[sectionIndex].treeChildren[aisleIndex].treeChildren[sideIndex].treeChildren.GetSize(); bayIndex++)
					{
						TreeElement bayElement = changesTree.treeChildren[sectionIndex].treeChildren[aisleIndex].treeChildren[sideIndex].treeChildren[bayIndex];
						CString bayAcadHandle = bayElement.acadHandle;
						if (bayAcadHandle == acadHandle)
						{
							// Set the Slotting Group
							qqhSLOTBay bay;
							int rc = btHelper.GetBtBay(bayElement.fileOffset, bay);

							if (rc == -1)
							{
								AfxMessageBox("GetBtBay failed!");
								continue;
							}//END if (rc == -1)

							if ( bay.getDBID() == 0 )
							{
								AfxMessageBox("Skipping an unsaved bay");
							}//END if ( bay.getDBID() == 0 )
							else
							{
								int tempDBID = bay.getDBID();
								bayDBIDs.Add(tempDBID);
							}//END else
						}//END if (bayAcadHandle == acadHandle)
					}//END for (int bayIndex=0; bayIndex < changesTree.treeChildren[sectionIndex].treeChildren[aisleIndex].treeChildren[sideIndex].treeChildren.GetSize(); bayIndex++)
				}//END for (int sideIndex=0; sideIndex < changesTree.treeChildren[sectionIndex].treeChildren[aisleIndex].treeChildren.GetSize(); sideIndex++)
			}//END for (int aisleIndex=0; aisleIndex < changesTree.treeChildren[sectionIndex].treeChildren.GetSize(); aisleIndex++)
		}//END for (int sectionIndex=0; sectionIndex < changesTree.treeChildren.GetSize(); sectionIndex++)

		//////////////////////////////////////////////////////////////////////
		// If we didn't find it in the temporary area, read from DB to temp
		// area
		//////////////////////////////////////////////////////////////////////
		if (found == 0)
		{
			int bayFileOffset = btHelper.UpdateBTWithBay(acadHandle,changesTree);

			if (bayFileOffset == -1)
			{
				AfxMessageBox("Error Finding Bay in Facility");
				currentApp->DoWaitCursor(-1);
				return;
			}//END if (bayFileOffset == -1)

			qqhSLOTBay bay;
			int rc = btHelper.GetBtBay(bayFileOffset, bay);
			if (rc == -1)
			{
				AfxMessageBox("GetBtBay failed!");
				continue;
			}//END if (rc == -1)

			if ( bay.getDBID() == 0 )
			{
				AfxMessageBox("Skipping an unsaved bay");
			}//END if ( bay.getDBID() == 0 )
			else
			{
				int tempDBID = bay.getDBID();
				bayDBIDs.Add(tempDBID);
			}//END else
		}//END if (found == 0)
	}//END for (int n=0; n < nLength; n++)

	//////////////////////////////////////////////////////////////////////
	// Remove the associations
	//////////////////////////////////////////////////////////////////////
	CProductGroupDataService pgdService;
	pgdService.DeleteSlotGroupBays(bayDBIDs);
	currentApp->DoWaitCursor(-1);

} //END UnassignProductGroup

//////////////////////////////////////////////////////////////////////
// Function Name : CapitalCostPass
// Classname : COptimizationHelper
// Description :
// Date Created :
// Author :
//////////////////////////////////////////////////////////////////////
// Inputs : None
// Outputs : None
// Explanation :
//////////////////////////////////////////////////////////////////////
// Revisions :
//   Date(mm-dd-yyyy) : initials : Description_of_revision
//   03-02-2006       : SMB      : Add Prolog
//////////////////////////////////////////////////////////////////////
void COptimizationHelper::CapitalCostPass()
{
	CCapitalCostDialog dlgCapitalCost;
	CStringArray results;
	CString temp, line;
	int idx, rc;
	BOOL foundTooMany = FALSE;

	if (facilityHelper.FacilityIsModified())
	{
		int rc = AfxMessageBox("The facility has been modified since the last save.\n"
			"It is recommended that you save the facility before running an optimization.\n"
			"Do you wish to save the facility?", MB_YESNOCANCEL);

		if (rc == IDYES)
		{
			facilityHelper.QuickSave();
		}//END if (rc == IDYES)
		else if (rc == IDCANCEL)
		{
			return;
		}//END else if (rc == IDCANCEL)
	}//END if (facilityHelper.FacilityIsModified())

	if (dlgCapitalCost.DoModal() == IDOK)
	{

		try {
			CThreadParameters parms;
			CStringArray parmList;
			CEvent event;

			parms.m_pEvent = &event;
			parms.m_pInList = &parmList;
			parms.m_pOutList = &results;
			temp.Format("%d", controlService.GetCurrentFacilityDBId());
			parmList.Add(temp);
			// temp.Format("%d|%d|%s|%d|%d|%d",
			temp.Format("%d|%d|%s|%d|%d",
				dlgCapitalCost.m_BreakPallet, dlgCapitalCost.m_RankHeight,
				dlgCapitalCost.m_LogMode, dlgCapitalCost.m_Ranking,
				dlgCapitalCost.m_SkipUsed);
				// dlgCapitalCost.m_SkipUsed, controlService.GetDivisor());
			parmList.Add(temp);
			temp.Format("%d", dlgCapitalCost.m_MaxResults);
			parmList.Add(temp);

			CProcessingMessage procDlg("Running Capital Cost Optimization", utilityHelper.GetParentWindow());

			rc = 0;
			CWinThread *pThread = AfxBeginThread(COptimizationHelper::RunCapitalCostThread, &parms);
			//FIXME: Why commented out - SMB
			//rc = StartRackAssignment(dlgCapitalCost.m_BreakPallet,
			//dlgCapitalCost.m_RankHeight, results);

			BOOL bThreadDone = false;
			while (TRUE)
			{
				if ( ! utilityHelper.PeekAndPump() )
				{
					break;
				}//END if ( ! utilityHelper.PeekAndPump() )

				bThreadDone = parms.m_pEvent->Lock(0);
				if (bThreadDone)
				{
					break;
				}//END if (bThreadDone)
			}//END while (TRUE)

			procDlg.Hide();

			rc = parms.m_ReturnCode;

			if (rc < 0)
			{
				controlService.Log("Error running capital cost optimization.", parms.m_ReturnMessage);
				return;
			}//END if (rc < 0)
		}//END try
		catch(Ssa_Exception e)
		{
			controlService.Log("Error running capital cost optimization.", &e);
			return;
		} //END catch(Ssa_Exception e)
		catch (...)
		{
			controlService.Log("Error running capital cost optimization.",
				"Generic exception in CapitalCostPass.\n");
			return;
		} //END catch (...)

		if (rc == 0)
		{
			/*FIXME: Why commented out - SMB
			FILE *f;
			f = fopen("c:\\temp\\pass1.out", "w");
			for(int i=0; i < results.GetSize(); i++)
			fprintf(f, "%s\n", results.GetAt(i));
			fclose(f);
			*/

			// Find any error message lines or two many rows lines
			for (int i=0; i < results.GetSize(); ++i)
			{
				line = results[i];
				idx = line.Find("|");
				if (idx > 0)
				{
					temp = line.Left(idx);
					line = line.Right(line.GetLength()-(idx+1));

					if (temp.CompareNoCase("E") == 0)
					{
						line.Replace("|", "\n");
						AfxMessageBox(line, MB_OK|MB_ICONSTOP);
						return;
					}//END if (temp.CompareNoCase("E") == 0)

					if (temp.CompareNoCase("Z") == 0 && ! foundTooMany)
					{
						line.Replace("|", "\n");
						AfxMessageBox(line, MB_OK|MB_ICONINFORMATION);
						foundTooMany = true;
						continue;
					}//END f (temp.CompareNoCase("Z") == 0 && ! foundTooMany)
				}//END if (idx > 0)
			}//END for (int i=0; i < results.GetSize(); ++i)

			// delete the first | in every line to make the
			// codes a single parameter, e.g. S|B| becomes SB|
			for (i=0; i< results.GetSize(); ++i)
			{
				results[i].Delete(results[i].Find("|"), 1);
			}//END for (i=0; i< results.GetSize(); ++i)

			CDisplayResults dlg;
			// Create tabs
			dlg.m_Tabs.Add("Best Available Summary");
			dlg.m_Tabs.Add("Ideal Summary");
			dlg.m_Tabs.Add("Best Available Detail");
			dlg.m_Tabs.Add("Ideal Detail");
			dlg.m_Tabs.Add("Rack Assignments");

			dlg.m_MainHelpTopic = "CapitalCostResults_Main";

			dlg.m_HelpTopics.Add("CapitalCostResults_BestAvailableSummaryPage");
			dlg.m_HelpTopics.Add("CapitalCostResults_IdealSummaryPage");
			dlg.m_HelpTopics.Add("CapitalCostResults_BestAvailableDetailPage");
			dlg.m_HelpTopics.Add("CapitalCostResults_IdealDetailPage");
			dlg.m_HelpTopics.Add("CapitalCostResults_RackUsagePage");

			dlg.m_ListHelpTopics.Add("CapitalCostResults_BestAvailableSummaryPage");
			dlg.m_ListHelpTopics.Add("CapitalCostResults_IdealSummaryPage");
			dlg.m_ListHelpTopics.Add("CapitalCostResults_BestAvailableDetailPage");
			dlg.m_ListHelpTopics.Add("CapitalCostResults_IdealDetailPage");
			dlg.m_ListHelpTopics.Add("CapitalCostResults_RackUsagePage");

			dlg.m_NextHelp1 = "CapitalCostResults_DefineProductGroups";

			dlg.m_WindowCaptions.Add("Capital Cost - Best Available Summary");
			dlg.m_WindowCaptions.Add("Capital Cost - Ideal Summary");
			dlg.m_WindowCaptions.Add("Capital Cost - Best Available Detail");
			dlg.m_WindowCaptions.Add("Capital Cost - Ideal Detail");
			dlg.m_WindowCaptions.Add("Capital Cost - Rack Assignments");

			// Create headers
			CStringArray headers;
			CString header;
			header = "Profile Description|Facings|Bays|Cost|";
			dlg.m_Headers.Add(header);
			header = "Profile Description|Facings|Bays|Cost|";
			dlg.m_Headers.Add(header);
			header = "Product Description|WMS Product ID|WMS Detail ID|Profile Description|Level Type|Ranking|Facings|Handling Method|Fit|";
			dlg.m_Headers.Add(header);
			header = "Product Description|WMS Product ID|WMS Detail ID|Profile Description|Level Type|";
			dlg.m_Headers.Add(header);
			header = "Profile|VW|Start VW Units|Start Fixed Facings|Start Bays|Used VW Units|Used Fixed Facings|Used Bays|Available VW Units|Available Fixed Facings|Available Bays|";
			dlg.m_Headers.Add(header);

			// Create keys used to parse elements into tabs
			// These are the codes at the front of each line that indicate
			// which type of information it is
			dlg.m_HeaderKeys.Add("SB");		// summary best available
			dlg.m_HeaderKeys.Add("SI");		// summary ideal
			dlg.m_HeaderKeys.Add("DB");		// detail best available
			dlg.m_HeaderKeys.Add("DI");		// detail ideal
			dlg.m_HeaderKeys.Add("SR");		// summary rack usage information

			dlg.m_MainHelpTopic = "Calculate_Capital_Cost";

			// The name of the next button
			dlg.m_NextCaption = "Define Product Groups";

			char *str, *ptr;
			int bestFacingCount, idealFacingCount, bestBayCount, idealBayCount;
			bestFacingCount = idealFacingCount = bestBayCount = idealBayCount = 0;
			double bestCost, idealCost;
			CString line;

			bestCost = idealCost = 0;

			// Insert total lines
			for (i=0; i < results.GetSize(); ++i)
			{
				line = results[i];

				if (line.Left(2) == "SI")
				{
					str = line.GetBuffer(0);
					ptr = strtok(str, "|");
					// skip code
					ptr = strtok(NULL, "|");
					// skip description
					ptr = strtok(NULL, "|");
					idealFacingCount += atoi(ptr);
					ptr = strtok(NULL, "|");
					idealBayCount += atoi(ptr);
					ptr = strtok(NULL, "|");
					idealCost += atof(ptr);
					line.ReleaseBuffer();
				}//END if (line.Left(2) == "SI")

				if (line.Left(2) == "SB")
				{
					str = line.GetBuffer(0);
					ptr = strtok(str, "|");
					// skip code
					ptr = strtok(NULL, "|");
					// skip description
					ptr = strtok(NULL, "|");
					bestFacingCount += atoi(ptr);
					ptr = strtok(NULL, "|");
					bestBayCount += atoi(ptr);
					ptr = strtok(NULL, "|");
					bestCost += atof(ptr);
					line.ReleaseBuffer();
				} //END if (line.Left(2) == "SB")
			}//END for (i=0; i < results.GetSize(); ++i)

			line.Format("SI|Totals:|%d|%d|%-10.2f|", idealFacingCount, idealBayCount, idealCost);
			results.Add(line);
			line.Format("SB|Totals:|%d|%d|%-10.2f|", bestFacingCount, bestBayCount, bestCost);
			results.Add(line);

			dlg.m_Data = results;

			try
			{
				rc = dlg.DoModal();
			}//END try
			catch (...)
			{
				AfxMessageBox("Error displaying results.");
			}//END catch (...)

			ads_printf("After DoModal\n");
			if (rc == IDC_DISPLAY_RESULTS_NEXT1)
			{
				CProductGroupHelper productGroupHelper;
				productGroupHelper.ProductGroupMaintenance();
			} //END if (rc == IDC_DISPLAY_RESULTS_NEXT1)
		}//END if (rc == 0)
	}//END if (dlgCapitalCost.DoModal() == IDOK)

	ads_printf("Exiting CapitalCostPass\n");

}// END CapitalCostPass

//////////////////////////////////////////////////////////////////////
// Function Name : LayoutProductGroupPass
// Classname : COptimizationHelper
// Description :
// Date Created :
// Author :
//////////////////////////////////////////////////////////////////////
// Inputs : None
// Outputs : None
// Explanation :
//////////////////////////////////////////////////////////////////////
// Revisions :
//   Date(mm-dd-yyyy) : initials : Description_of_revision
//   03-02-2006       : SMB      : Add Prolog
//////////////////////////////////////////////////////////////////////
void COptimizationHelper::LayoutProductGroupPass()
{

	CLayoutGroupsDialog dlgLayoutGroups;
	CStringArray results;
	CString temp, line;
	int idx, rc;

	if (facilityHelper.FacilityIsModified())
	{
		int rc = AfxMessageBox("The facility has been modified since the last save.\n"
			"It is recommended that you save the facility before running an optimization.\n"
			"Do you wish to save the facility?", MB_YESNOCANCEL);
		if (rc == IDYES)
		{
			facilityHelper.QuickSave();
		}//END if (rc == IDYES)
		else if (rc == IDCANCEL)
		{
			return;
		}//END else if (rc == IDCANCEL)
	}//END if (facilityHelper.FacilityIsModified())

	if (dlgLayoutGroups.DoModal() == IDOK)
	{

		try {
			CThreadParameters parms;
			CEvent event;
			parms.m_pEvent = &event;
			parms.m_pOutList = &results;
			CStringArray parmList;
			parms.m_pInList = &parmList;

			temp.Format("%d", controlService.GetCurrentFacilityDBId());
			parmList.Add(temp);
			temp.Format("%d|%d|%s|%d", dlgLayoutGroups.m_Layout, dlgLayoutGroups.m_Contiguous,
				dlgLayoutGroups.m_LogMode, dlgLayoutGroups.m_IgnoreRankings);
			parmList.Add(temp);
			temp.Format("%d", dlgLayoutGroups.m_MaxResults);
			parmList.Add(temp);

			CProcessingMessage pMsg("Running Product Group Layout", utilityHelper.GetParentWindow());

			rc = 0;
			CWinThread *pThread = AfxBeginThread(COptimizationHelper::RunProductGroupLayoutThread, &parms);
			//rc = StartSlotGroupAssignment(dlgLayoutGroups.m_Layout, results);

			BOOL bThreadDone = false;
			while (TRUE)
			{
				if ( ! utilityHelper.PeekAndPump() )
					break;

				bThreadDone = event.Lock(0);
				if (bThreadDone)
					break;
			}//END while (TRUE)

			rc = parms.m_ReturnCode;
			if (rc < 0)
			{
				ads_printf("%s\n", parms.m_ReturnMessage);
				AfxMessageBox("Error running product group layout.");
				return;
			}//END if (rc < 0)

		}//END try
		catch(Ssa_Exception e)
		{
			controlService.Log("Error running product group layout.", &e);
			return;
		}//END catch(Ssa_Exception e)
		catch (...)
		{
			controlService.Log("Error running product group layout.", "Generic exception in LayoutProductGroupPass.\n");
			return;
		}// catch (...)

		if (rc == 0)
		{
			/*FIXME:
			FILE *f;
			f = fopen("c:\\temp\\pass3.out", "w");
			for(int i=0; i < results.GetSize(); i++)
			fprintf(f, "%s\n", results.GetAt(i));
			fclose(f);
			*/
			boolean foundTooMany = false;

			// Find any error or two many rows lines
			for (int i=0; i < results.GetSize(); ++i)
			{
				line = results[i];
				idx = line.Find("|");
				if (idx > 0)
				{
					temp = line.Left(idx);
					line = line.Right(line.GetLength()-(idx+1));

					if (temp.CompareNoCase("E") == 0)
					{
						line.Replace("|", "\n");
						AfxMessageBox(line, MB_OK|MB_ICONSTOP);
						return;
					}//END if (temp.CompareNoCase("E") == 0)

					if (temp.CompareNoCase("Z") == 0 && ! foundTooMany)
					{
						line.Replace("|", "\n");
						AfxMessageBox(line, MB_OK|MB_ICONINFORMATION);
						foundTooMany = true;
						continue;
					}//END if (temp.CompareNoCase("Z") == 0 && ! foundTooMany)
				}//END if (idx > 0)
			}//END for (int i=0; i < results.GetSize(); ++i)

			CDisplayResults dlg;
			// Create tabs
			dlg.m_Tabs.Add("Assigned");
			dlg.m_Tabs.Add("Not Assigned");
			dlg.m_Tabs.Add("Detail");

			dlg.m_WindowCaptions.Add("Layout Product Groups - Assigned");
			dlg.m_WindowCaptions.Add("Layout Product Groups - Not Assigned");
			dlg.m_WindowCaptions.Add("Layout Product Groups - Detail");

			dlg.m_MainHelpTopic = "LayoutProductGroupsResults_Main";
			dlg.m_HelpTopics.Add("LayoutProductGroupsResults_AssignedPage");
			dlg.m_HelpTopics.Add("LayoutProductGroupsResults_NotAssignedPage");
			dlg.m_HelpTopics.Add("LayoutProductGroupsResults_DetailPage");

			dlg.m_ListHelpTopics.Add("LayoutProductGroupsResults_AssignedPage");
			dlg.m_ListHelpTopics.Add("LayoutProductGroupsResults_NotAssignedPage");
			dlg.m_ListHelpTopics.Add("LayoutProductGroupsResults_DetailPage");

			dlg.m_NextHelp1 = "LayoutProductGroupsResults_LayoutProductButton";

			// Create headers
			CStringArray headers;
			CString header;
			header = "Product Group|Bays|Products|Facings|Approximate Products Allocated|Percent Open Space|";
			dlg.m_Headers.Add(header);
			header = "Product Group|Facings|Profile|Level Type|";
			dlg.m_Headers.Add(header);
			header = "Product Group|Section|Aisle|Side|Bay|Relative Level|";
			dlg.m_Headers.Add(header);


			// Create keys used to parse elements into tabs
			dlg.m_HeaderKeys.Add("A");		// Assigned
			dlg.m_HeaderKeys.Add("U");		// Un-assigned
			dlg.m_HeaderKeys.Add("D");		// Detail

			// The name of the next button
			dlg.m_NextCaption = "Layout Products";

			dlg.m_MainHelpTopic = "Layout_Product_Groups";
			dlg.m_Data = results;

			try
			{
				rc = dlg.DoModal();
			}//END try
			catch (...)
			{
				AfxMessageBox("Error displaying results.");
			}//END catch (...)

			if (rc == IDC_DISPLAY_RESULTS_NEXT1)
			{
				LayoutProductPass();
			}//END if (rc == IDC_DISPLAY_RESULTS_NEXT1)
		}//END if (rc == 0)
	}//END if (dlgLayoutGroups.DoModal() == IDOK)

	return;
}//END LayoutProductGroupPass

//////////////////////////////////////////////////////////////////////
// Function Name : LayoutProductPass
// Classname : COptimizationHelper
// Description :
// Date Created :
// Author :
//////////////////////////////////////////////////////////////////////
// Inputs : None
// Outputs : None
// Explanation :
//////////////////////////////////////////////////////////////////////
// Revisions :
//   Date(mm-dd-yyyy) : initials : Description_of_revision
//   03-02-2006       : SMB      : Add Prolog
//////////////////////////////////////////////////////////////////////
void COptimizationHelper::LayoutProductPass()
{
	CPropertySheet sheet;

	CProductLayoutStartPage page1;
	CProductLayoutAdvancedPage page2;

	CStringArray results;
	CString temp, line, tooManyMsg, options;
	int idx, rc;
	BOOL foundTooMany = false;
	int mode;

	if (facilityHelper.FacilityIsModified())
	{
		int rc = AfxMessageBox("The facility has been modified since the last save.\n"
			"It is recommended that you save the facility before running an optimization.\n"
			"Do you wish to save the facility?", MB_YESNOCANCEL);

		if (rc == IDYES)
		{
			facilityHelper.QuickSave();
		}//END if (rc == IDYES)
		else if (rc == IDCANCEL)
		{
			return;
		}//END else if (rc == IDCANCEL)
	}//END if (facilityHelper.FacilityIsModified())

	temp = controlService.GetApplicationData("Options", "Dialogs\\ProductLayoutStartup");
	CStringArray optionList;
	page1.m_slotnew = 1;
	page1.m_reslot = 1;
	page1.m_findswaps = 1;

	if (temp != "")
	{
		utilityHelper.ParseString(temp, "|", optionList);
		for (int i=0; i < optionList.GetSize(); ++i)
		{
			switch (i)
			{
			case 0:
				page1.m_LayoutType = atoi(optionList[i]);
				break;
			case 1:
				page1.m_VariableWidth = atoi(optionList[i]);
				break;
			case 2:
				page1.m_Rotate = atoi(optionList[i]);
				break;
			case 3:
				page1.m_slotnew = atoi(optionList[i]);
				break;
			case 4:
				page1.m_reslot = atoi(optionList[i]);
				break;
			case 5:
				page1.m_findswaps = atoi(optionList[i]);
				break;
			case 6:
				page2.m_OptimizationRatio = atof(optionList[i]);
				break;
			case 7:
				page2.m_FollowPickPath = atoi(optionList[i]);
				break;
			case 8:
				page2.m_IgnoreRankings = atoi(optionList[i]);
				break;
			case 9:
				page2.m_IgnoreWeight = atoi(optionList[i]);
				break;
			case 10:
				page2.m_Overlap = atoi(optionList[i]);
				break;
			case 11:
				page2.m_LogMode = optionList[i];
				break;
			case 12:
				page2.m_MaxResults = atoi(optionList[i]);
				break;
			case 13:
				page2.m_UtilizationStart = atoi(optionList[i]);
				break;
			case 14:
				page2.m_UtilizationDecrement = atoi(optionList[i]);
				break;
			}//END switch (i)
		}//END for (int i=0; i < optionList.GetSize(); ++i)
	}//END if (temp != "")

	sheet.AddPage(&page1);
	sheet.AddPage(&page2);

	sheet.SetTitle("Layout Products Parameters");

	if (sheet.DoModal() != IDOK)
	{
		return;
	}//END if (sheet.DoModal() != IDOK)

	mode = page1.m_LayoutType;
	options.Format("%d|%d|%d|%d|%d|%d|%f|%d|%d|%d|%d|%s|%d|%d|%d|", page1.m_LayoutType,
		page1.m_VariableWidth, page1.m_Rotate, page1.m_slotnew, page1.m_reslot,
		page1.m_findswaps,
		page2.m_OptimizationRatio, page2.m_FollowPickPath, page2.m_IgnoreRankings,
		page2.m_IgnoreWeight, page2.m_Overlap, page2.m_LogMode, page2.m_MaxResults,
		page2.m_UtilizationStart, page2.m_UtilizationDecrement,page1.m_findswaps);
	controlService.SetApplicationData("Options", options, "Dialogs\\ProductLayoutStartup");


	if (mode == ManualMode) {
		RunManualLayout();
	}
	else
	{


		CProcessingMessage procMsg("Checking for duplicate locations.", utilityHelper.GetParentWindow());

		try
		{
			rc = GetDuplicateLocations();
			if (rc != 0)
			{
				return;
			}//END if (rc != 0)

			procMsg.UpdateMessage("Running Product Layout");

			CThreadParameters parms;
			CEvent event;
			parms.m_pEvent = &event;
			parms.m_pOutList = &results;
			CStringArray parmList;
			parms.m_pInList = &parmList;
			temp.Format("%d", controlService.GetCurrentFacilityDBId());
			parmList.Add(temp);
			temp.Format("%d", mode);
			parmList.Add(temp);
			parmList.Add(options);
			temp.Format("%d", page2.m_MaxResults);
			parmList.Add(temp);
			int facilityIntegrated = facilityDataService.IsFacilityIntegrated(controlService.GetCurrentFacilityDBId());

			if (facilityIntegrated < 0)
			{
				return;
			}//END if (facilityIntegrated < 0)

			temp.Format("%d", facilityIntegrated);
			parmList.Add(temp);

			rc = 0;
			CWinThread *pThread = AfxBeginThread(COptimizationHelper::RunProductLayoutThread, &parms);

			BOOL bThreadDone = false;
			while (TRUE)
			{
				if ( ! utilityHelper.PeekAndPump() )
				{
					break;
				}//END if ( ! utilityHelper.PeekAndPump() )

				bThreadDone = event.Lock(0);
				if (bThreadDone)
				{
					break;
				}//END if (bThreadDone)
			}//END while (TRUE)

			rc = parms.m_ReturnCode;
			if (rc < 0)
			{
				ads_printf("%s\n",parms.m_ReturnMessage);
				AfxMessageBox("Error running product layout.");
				return;
			}//END if (rc < 0)
		}//END try
		catch(Ssa_Exception e)
		{
			controlService.Log("Error running Layout Product Groups", &e);
			return;
		}//END catch(Ssa_Exception e)
		catch(...)
		{
			controlService.Log("Error running Layout Product Groups", "Generic exception in LayoutProductPass.\n");
			return;
		}//END catch (...)

		if ( rc == 0)
		{
			// Display results

			/*FIXME:
			FILE *f;
			f = fopen("c:\\temp\\pass4.out", "w");
			for(int j=0; j < results.GetSize(); j++)
				fprintf(f, "%s\n", results.GetAt(j));
			fclose(f);
			*/

			//store the cost in the btree/database
			double newcost;
			qqhSLOTFacility tempFac;
			newcost = atof(results.GetAt(0));
			bTreeHelper.GetBtFacility(changesTree.fileOffset, tempFac);
			tempFac.setCost(newcost);
			bTreeHelper.SetBtFacility(changesTree.fileOffset, tempFac);

			CDWordArray aisleDBIDList;
			boolean foundTooMany = false;

			// Find any error or two many rows lines
			for (int i=0; i < results.GetSize(); ++i)
			{
				line = results[i];
				idx = line.Find("|");
				if (idx > 0)
				{
					temp = line.Left(idx);
					line = line.Right(line.GetLength()-(idx+1));

					if (temp.CompareNoCase("E") == 0)
					{
						line.Replace("|", "\n");
						AfxMessageBox(line, MB_OK|MB_ICONSTOP);
						return;
					}//END if (temp.CompareNoCase("E") == 0)

					if (temp.CompareNoCase("Z") == 0 && ! foundTooMany)
					{
						line.Replace("|", "\n");
						tooManyMsg = line;
						foundTooMany = true;
						continue;
					}//END if (temp.CompareNoCase("Z") == 0 && ! foundTooMany)

					// these are the aisles to renumber; add them to the aisle dbid list
					if (temp.CompareNoCase("A") == 0)
					{
						idx = line.Find("|");
						int i = atoi(line.Left(idx));
						aisleDBIDList.Add(i);
					}//END if (temp.CompareNoCase("A") == 0)
				}//END if (idx > 0)
			}//END for (int i=0; i < results.GetSize(); ++i)

			if (aisleDBIDList.GetSize() > 0)
			{
				procMsg.UpdateMessage("Renumbering Aisles");
				ads_printf("Renumbering aisles...\n");
				CLocationNumberingService locationNumberingService;
				locationNumberingService.RenumberAisles(aisleDBIDList, changesTree);
			}//END if (aisleDBIDList.GetSize() > 0)

			if (foundTooMany)
			{
				AfxMessageBox(tooManyMsg, MB_OK|MB_ICONINFORMATION);
			}//END if (foundTooMany)

			CDisplayResults dlg;
			CStringArray headers;
			CString header;



			dlg.m_MainHelpTopic = "Layout_Products_Detail";

			switch (mode)
			{
			case StrategicMode:
				dlg.m_MainHelpTopic = "LayoutProductsResults_Main";
				dlg.m_HelpTopics.Add("LayoutProductsResults_FacingsPage");
				dlg.m_HelpTopics.Add("LayoutProductsResults_DetailPage");
				dlg.m_HelpTopics.Add("LayoutProductsResults_CaseReorientationPage");
				dlg.m_HelpTopics.Add("LayoutProductsResults_VariableWidthPage");
				dlg.m_HelpTopics.Add("LayoutProductsResults_NotAssignedPage");

				dlg.m_ListHelpTopics.Add("LayoutProductsResults_FacingsPage");
				dlg.m_ListHelpTopics.Add("LayoutProductsResults_DetailPage");
				dlg.m_ListHelpTopics.Add("LayoutProductsResults_CaseReorientationPage");
				dlg.m_ListHelpTopics.Add("LayoutProductsResults_VariableWidthPage");
				dlg.m_ListHelpTopics.Add("LayoutProductsResults_NotAssignedPage");

				dlg.m_NextHelp1 = "LayoutProductsResults_CostComparisonButton";

				// Create tabs
				dlg.m_Tabs.Add("Facings");
				dlg.m_Tabs.Add("Detail");
				dlg.m_Tabs.Add("Case Re-Orientation");
				dlg.m_Tabs.Add("Variable Width");
				dlg.m_Tabs.Add("Not Assigned");

				dlg.m_WindowCaptions.Add("Layout Products - Facings");
				dlg.m_WindowCaptions.Add("Layout Products - Details");
				dlg.m_WindowCaptions.Add("Layout Products - Case Re-Orientation");
				dlg.m_WindowCaptions.Add("Layout Products - Variable Width");
				dlg.m_WindowCaptions.Add("Layout Products - Not Assigned Products");

				// Create headers

				header = "Product Group|Facings Assigned|";
				dlg.m_Headers.Add(header);
				header = "Product Description|WMS Product ID|WMS Detail ID|Location ID|Case Count|Cost|";
				dlg.m_Headers.Add(header);
				header = "Product Description|WMS Product ID|WMS Detail ID|Location ID|Height|Width|Length|";
				dlg.m_Headers.Add(header);
				header = "Location ID|Width|Height|Depth|";
				dlg.m_Headers.Add(header);
				header = "Product Description|WMS Product ID|WMS Product Detail ID|Reason Not Assigned|";
				dlg.m_Headers.Add(header);

				// Create keys used to parse elements into tabs
				dlg.m_HeaderKeys.Add("S");		// Summary
				dlg.m_HeaderKeys.Add("D");		// Detail
				dlg.m_HeaderKeys.Add("C");		// Case Re-orientation
				dlg.m_HeaderKeys.Add("V");		// Variable width
				dlg.m_HeaderKeys.Add("U");		// Unassigned

				break;

			case GroupMode:
				dlg.m_MainHelpTopic = "LayoutProductsGroupResults_Main";
				dlg.m_HelpTopics.Add("LayoutProductsGroupResults_MovePage");
				dlg.m_HelpTopics.Add("LayoutProductsGroupResultsAssignmentPage");

				dlg.m_ListHelpTopics.Add("LayoutProductsGroupResults_MovePage");
				dlg.m_ListHelpTopics.Add("LayoutProductsGroupResultsAssignmentPage");

				dlg.m_NextHelp1 = "LayoutProductsResults_CostComparisonButton";

				dlg.m_Tabs.Add("Moves");
				dlg.m_WindowCaptions.Add("Layout Products - Group Mode - Product Movement");
				header = "Product ID|Product Description|Product Group|Old Location|New Location|";
				dlg.m_Headers.Add(header);
				dlg.m_HeaderKeys.Add("G");

				dlg.m_Tabs.Add("Assignments");
				dlg.m_WindowCaptions.Add("Layout Products - Group Mode - Assignments");
				header = "Product Description|WMS Product ID|WMS Detail ID|Location ID|Case Count|Cost|";
				dlg.m_Headers.Add(header);
				dlg.m_HeaderKeys.Add("D");

				break;

			case TacticalMode:
				dlg.m_MainHelpTopic = "LayoutProductsTacticalResults_Main";
				dlg.m_HelpTopics.Add("LayoutProductsTacticalResults_CostPage");
				dlg.m_HelpTopics.Add("LayoutProductsTacticalResults_AssignmentPage");

				dlg.m_ListHelpTopics.Add("LayoutProductsTacticalResults_CostPage");
				dlg.m_ListHelpTopics.Add("LayoutProductsTacticalResults_AssignmentPage");

				dlg.m_NextHelp1 = "LayoutProductsResults_CostComparisonButton";

				dlg.m_Tabs.Add("Cost");
				dlg.m_WindowCaptions.Add("Layout Products - Tactical Mode - Cost Detail");
				header = "Product ID|Product Description|Original Primary|Original Facings|Original Profile|"
					"New Primary|New Facings|New Profile|Displaced Prod ID|Displaced Prod Desc|"
					"Original Cost|New Cost|Move Cost|";
				dlg.m_Headers.Add(header);
				dlg.m_HeaderKeys.Add("T");

				dlg.m_Tabs.Add("Assignments");
				dlg.m_WindowCaptions.Add("Layout Products - Tactical Mode - Assignments");
				header = "Product Description|WMS Product ID|WMS Detail ID|Location ID|Case Count|Cost|";
				dlg.m_Headers.Add(header);
				dlg.m_HeaderKeys.Add("D");

				break;

			case NewMode:
				dlg.m_MainHelpTopic = "LayoutProductsNewResults_Main";
				dlg.m_HelpTopics.Add("LayoutProductsNewResults_Main");
				dlg.m_ListHelpTopics.Add("LayoutProductsNewResults_Main");

				dlg.m_NextHelp1 = "LayoutProductsResults_CostComparisonButton";

				dlg.m_Tabs.Add("Assignments");
				dlg.m_WindowCaptions.Add("Layout Products - New Mode - Assignments");
				header = "Product Description|WMS Product ID|WMS Detail ID|Location ID|Case Count|Cost|";
				dlg.m_Headers.Add(header);
				dlg.m_HeaderKeys.Add("D");

				break;
			}//END switch (mode)

			// The name of the next button
			dlg.m_NextCaption = "&Analyze Cost";

			if (mode == TacticalMode)
			{
				CStringArray profileList, strings;
				CString oldbp, newbp;
				CMapStringToString map;
				try
				{
					CBayProfileDataService bayProfileDataService;
					bayProfileDataService.GetBayProfiles(profileList);
				}//END try
				catch (...)
				{
				}//END catch (...)

				for (int i=0; i < profileList.GetSize(); ++i)
				{
					utilityHelper.ParseString(profileList[i], "|", strings);
					map.SetAt(strings[0], strings[1]);
				}//END for (int i=0; i < profileList.GetSize(); ++i)

				for (i=0; i < results.GetSize(); ++i)
				{
					if (results[i].GetAt(0) != 'T')
					{
						continue;
					}//END if (results[i].GetAt(0) != 'T')

					utilityHelper.ParseString(results[i], "|", strings);
					oldbp = strings[5];

					if (map.Lookup(oldbp, newbp))
					{
						strings[5] = newbp;
					}//END if (map.Lookup(oldbp, newbp))

					oldbp = strings[8];

					if (map.Lookup(oldbp, newbp))
					{
						strings[8] = newbp;
					}//END if (map.Lookup(oldbp, newbp))

					utilityHelper.BuildDelimitedString(strings, results[i], "|");

				}//END for (i=0; i < results.GetSize(); ++i)
			}//END if (mode == TacticalMode)

			dlg.m_Data = results;
			dlg.m_OrigColumnSize = 100;
			try
			{
				rc = dlg.DoModal();
			}//END try
			catch (...)
			{
				AfxMessageBox("Error displaying results.");
			}//END catch (...)

			if (rc == IDC_DISPLAY_RESULTS_NEXT1)
			{

				CostComparison();
			}//END if (rc == IDC_DISPLAY_RESULTS_NEXT1)
		}//END if ( rc == 0)
		else
		{
			/*
			FILE *f;
			f = fopen("c:\\temp\\pass4.out", "w");
			for(int i=0; i < results.GetSize(); i++)
				fprintf(f, results.GetAt(i));

			fclose(f);
			*/
		}//END else
	}//END else
}//END LayoutProductPass

//////////////////////////////////////////////////////////////////////
// Function Name : RunManualLayout
// Classname : COptimizationHelper
// Description :
// Date Created :
// Author :
//////////////////////////////////////////////////////////////////////
// Inputs : None
// Outputs : None
// Explanation :
//////////////////////////////////////////////////////////////////////
// Revisions :
//   Date(mm-dd-yyyy) : initials : Description_of_revision
//   03-02-2006       : SMB      : Add Prolog
//////////////////////////////////////////////////////////////////////
void COptimizationHelper::RunManualLayout()
{
	CManualAssignmentDialog dlg;
	dlg.DoModal();
} //END RunManualLayout

//////////////////////////////////////////////////////////////////////
// Function Name : RunCapitalCostThread
// Classname : COptimizationHelper
// Description :
// Date Created :
// Author :
//////////////////////////////////////////////////////////////////////
// Inputs : LPVOID pParam - CThreadParameters
// Outputs : UINT - value in CThreadParameters::m_ReturnCode
//             -1 - ERROR
//              0 -
//              1 -
// Explanation :
//////////////////////////////////////////////////////////////////////
// Revisions :
//   Date(mm-dd-yyyy) : initials : Description_of_revision
//   03-02-2006       : SMB      : Add Prolog
//////////////////////////////////////////////////////////////////////
//FIXME: -
// typedef auto_ptr < CList< CString, CString &> > CListCStringPtr;
// #pragma optimize( "agpswy", off )
// #pragma optimize( "agptwy", off )
// #pragma optimize( "atp", off )
// #pragma optimize( "g", off )
// #pragma optimize( "", off )
UINT COptimizationHelper::RunCapitalCostThread(LPVOID pParam)
{
	CThreadParameters &threadParms = *(CThreadParameters *)pParam;

	int facilityId = atoi(threadParms.m_pInList->GetAt(0));
	CString options = threadParms.m_pInList->GetAt(1);
	int maxResults = atoi(threadParms.m_pInList->GetAt(2));

	try {

		string pOptionsTmp = (LPCTSTR)options;

		CListstringPtr a = getSessionMgrSO()->EvaluateHelper(facilityId, pOptionsTmp, (__int32)maxResults);
		if (a->GetCount() > 0)
		{
			threadParms.m_ReturnCode = 0;
		}//END if (a->GetCount() > 0)
		else
		{
			threadParms.m_ReturnCode = 1;
		}//END else

		/// TBR CStringArray strArr;
		POSITION posSL = a->GetHeadPosition();
		for (int i=0; i<a->GetCount(); i++)
		{
			(*threadParms.m_pOutList).Add((a->GetNext(posSL)).c_str());
		}//END for (int i=0; i<a->GetCount(); i++)

	}//END try
	catch(Ssa_Exception e)
	{
		char eMsg[1024];
		e.GetMessage(eMsg);
		threadParms.m_ReturnCode = -1;
		threadParms.m_ReturnMessage = eMsg;
	}//END catch(Ssa_Exception e)
	catch(...)
	{
		threadParms.m_ReturnCode = -1;
		threadParms.m_ReturnMessage = "Generic error running Capital Cost Optimization.";
	} //END catch (...)

	threadParms.m_pEvent->SetEvent();

	return threadParms.m_ReturnCode;

}//END RunCapitalCostThread

//////////////////////////////////////////////////////////////////////
// Function Name : RunProductGroupLayoutThread
// Classname : COptimizationHelper
// Description :
// Date Created :
// Author :
//////////////////////////////////////////////////////////////////////
// Inputs : LPVOID pParam - CThreadParameters
// Outputs : UINT - value in CThreadParameters::m_ReturnCode
//             -1 - ERROR
//              0 -
//              1 -
// Explanation :
//////////////////////////////////////////////////////////////////////
// Revisions :
//   Date(mm-dd-yyyy) : initials : Description_of_revision
//   03-02-2006       : SMB      : Add Prolog
//////////////////////////////////////////////////////////////////////
UINT COptimizationHelper::RunProductGroupLayoutThread(LPVOID pParam)
{
	CThreadParameters &threadParms = *(CThreadParameters *)pParam;
	int facilityId = atoi(threadParms.m_pInList->GetAt(0));
	CString options = threadParms.m_pInList->GetAt(1);
	int maxResults = atoi(threadParms.m_pInList->GetAt(2));

	try
	{
		COptimizationHelper optimizationHelper;
		threadParms.m_ReturnCode = optimizationHelper.ProductGroupLayout(facilityId, options, maxResults,
			*threadParms.m_pOutList);
	}//END try
	catch(Ssa_Exception e)
	{
		char eMsg[1024];
		e.GetMessage(eMsg);
		threadParms.m_ReturnCode = -1;
		threadParms.m_ReturnMessage = eMsg;
	}//END catch(Ssa_Exception e)
	catch (...)
	{
		threadParms.m_ReturnCode = -1;
		threadParms.m_ReturnMessage = "Generic exception in ProductGroupLayout";
	}//END catch (...)

	threadParms.m_pEvent->SetEvent();
	return threadParms.m_ReturnCode;

}//END RunProductGroupLayoutThread

//////////////////////////////////////////////////////////////////////
// Function Name : RunProductLayoutThread
// Classname : COptimizationHelper
// Description :
// Date Created :
// Author :
//////////////////////////////////////////////////////////////////////
// Inputs : LPVOID pParam - CThreadParameters
// Outputs : UINT - value in CThreadParameters::m_ReturnCode
//             -1 - ERROR
//              0 -
//              1 -
// Explanation :
//////////////////////////////////////////////////////////////////////
// Revisions :
//   Date(mm-dd-yyyy) : initials : Description_of_revision
//   03-02-2006       : SMB      : Add Prolog
//////////////////////////////////////////////////////////////////////
UINT COptimizationHelper::RunProductLayoutThread(LPVOID pParam)
{
	CThreadParameters &threadParms = *(CThreadParameters *)pParam;

	int facilityId = atoi(threadParms.m_pInList->GetAt(0));
	int mode = atoi(threadParms.m_pInList->GetAt(1));
	CString options = threadParms.m_pInList->GetAt(2);
	int maxResults = atoi(threadParms.m_pInList->GetAt(3));
	int facilityIntegrated = atoi(threadParms.m_pInList->GetAt(4));

	try
	{
		COptimizationHelper optimizationHelper;
		threadParms.m_ReturnCode = optimizationHelper.ProductLayout(facilityId, mode, options, maxResults,
			facilityIntegrated, *threadParms.m_pOutList);
	}//END try
	catch(Ssa_Exception e)
	{
		char eMsg[1024];
		e.GetMessage(eMsg);
		threadParms.m_ReturnCode = -1;
		threadParms.m_ReturnMessage = eMsg;
	}//END catch(Ssa_Exception e)
	catch (...)
	{
		threadParms.m_ReturnCode = -1;
		threadParms.m_ReturnMessage = "Generic exception in ProductLayout";
	}//END catch (...)

	threadParms.m_pEvent->SetEvent();
	return threadParms.m_ReturnCode;

}//END RunProductLayoutThread

//////////////////////////////////////////////////////////////////////
// Function Name : GetDuplicateLocations
// Classname : COptimizationHelper
// Description :
// Date Created :
// Author :
//////////////////////////////////////////////////////////////////////
// Inputs : LPVOID pParam - CThreadParameters
// Outputs : UINT - value in CThreadParameters::m_ReturnCode
//             -1 - ERROR
//              0 -
//              1 -
// Explanation :
//////////////////////////////////////////////////////////////////////
// Revisions :
//   Date(mm-dd-yyyy) : initials : Description_of_revision
//   03-02-2006       : SMB      : Add Prolog
//////////////////////////////////////////////////////////////////////
int COptimizationHelper::GetDuplicateLocations()
{
	CSsaStringArray results;

	CDisplayResults dlg;
	CStringArray headers;
	CString header;
	int dupCount;

	try
	{
		CFacilityDataService facilityDataService;

		dupCount = facilityDataService.GetDuplicateLocationsByFacility(controlService.GetCurrentFacilityDBId(), results);
	}//END try
	catch(Ssa_Exception e)
	{
		utilityHelper.ProcessError("Error getting duplicate locations.", &e);
		return -1;
	}//END catch(Ssa_Exception e)
	catch(...)
	{
		utilityHelper.ProcessError("Error getting duplicate locations.");
		return -1;
	}//END catch (...)

	if (dupCount > 0)
	{
		AfxMessageBox("There are duplicate locations in this facility.\n"
			"Please correct this before running product layout.");
		// Create tabs
		dlg.m_Tabs.Add("Duplicate Locations");
		dlg.m_WindowCaptions.Add("Duplicate Locations");
		// Create headers
		header = "Section|Aisle|Bay|Level|Location|Number of Duplicates|";
		dlg.m_Headers.Add(header);
		// Create keys used to parse elements into tabs
		dlg.m_HeaderKeys.Add("D");
		dlg.m_Data = results;

		int rc = dlg.DoModal();
	}//END if (dupCount > 0)

	return dupCount;
}//END GetDuplicateLocations

//////////////////////////////////////////////////////////////////////
// Function Name : CostComparison
// Classname : COptimizationHelper
// Description :
// Date Created :
// Author :
//////////////////////////////////////////////////////////////////////
// Inputs :
// Outputs :
// Explanation :
//////////////////////////////////////////////////////////////////////
// Revisions :
//   Date(mm-dd-yyyy) : initials : Description_of_revision
//   03-02-2006       : SMB      : Add Prolog
//////////////////////////////////////////////////////////////////////
void COptimizationHelper::CostComparison()
{
	try
	{
		CCostComparisonDialog dlg;
		dlg.DoModal();
	}//END try
	catch (...)
	{
		AfxMessageBox("Error running cost comparison.");
	}//END catch (...)
}//END CostComparison

//////////////////////////////////////////////////////////////////////
// Function Name : RunBaselineCost
// Classname : COptimizationHelper
// Description :
// Date Created :
// Author :
//////////////////////////////////////////////////////////////////////
// Inputs :
// Outputs :
// Explanation :
//////////////////////////////////////////////////////////////////////
// Revisions :
//   Date(mm-dd-yyyy) : initials : Description_of_revision
//   03-02-2006       : SMB      : Add Prolog
//////////////////////////////////////////////////////////////////////
void COptimizationHelper::RunBaselineCost()
{
	CWinApp * currentApp;
	currentApp = AfxGetApp();

	if (AfxMessageBox("Are you sure you wish to run the baseline analysis?",MB_YESNO) == IDNO )
	{
		return;
	}//END if (AfxMessageBox("Are you sure you wish to run the baseline analysis?",MB_YESNO) == IDNO )

	currentApp->DoWaitCursor(1);

	try
	{
		CString cost;
		try
		{
			CalculateBaselineCost(cost);
		}//END try
		catch(...)
		{
			currentApp->DoWaitCursor(-1);
			AfxMessageBox("Error running baseline");
			return;
		}//end catch (...)

		currentApp->DoWaitCursor(-1);
		CString costRes = "The resulting baseline cost is : " + cost;
		AfxMessageBox(costRes);
	}//END try
	catch(...)
	{
		return;
	}//END catch
}//END RunBaselineCost

//////////////////////////////////////////////////////////////////////
// Function Name : CalculateBaselineCost
// Classname : COptimizationHelper
// Description :
// Date Created :
// Author :
//////////////////////////////////////////////////////////////////////
// Inputs : CString &cost -
//          long facilityDBID -
//          CString prodCostList)
// Outputs :
// Explanation :
//////////////////////////////////////////////////////////////////////
// Revisions :
//   Date(mm-dd-yyyy) : initials : Description_of_revision
//   03-02-2006       : SMB      : Add Prolog
//////////////////////////////////////////////////////////////////////
int COptimizationHelper::CalculateBaselineCost(CString &cost, long facilityDBID, CString prodCostList)
{
#if 0
	CString tempString;
	int tempInt;
	CSsaStringArray tempSendArray;
	CSsaStringArray tempRecvArray;
	CString sendString;
	int i = 0;

	/////////////////////////////////////////////////////////////
	// Build the data and send to Forte connection
	/////////////////////////////////////////////////////////////
	sendString = "<SIO>SLOTSocketString\n";
	tempSendArray.Add(sendString);
	sendString = "<SAI>Calculate BaseLine Cost\n";
	tempSendArray.Add(sendString);

	sendString.Format("<SAI>%d\n", facilityDBID);
	tempSendArray.Add(sendString);

	sendString.Format("<SAI>%s\n", prodCostList);
	tempSendArray.Add(sendString);

	sendString.Format("<SAI>%d\n", CSolution::Baseline);
	tempSendArray.Add(sendString);

	sendString = "<EIO>SLOTSocketString\n";
	tempSendArray.Add(sendString);

	try
	{
		forteService.SendToForteConnection(tempSendArray,tempRecvArray,
			CString("SLOTSocketString"), 6040);
	}
	catch(...) {
		cost = "0.00";
		return -1;
	}

	for (i=0; i < tempRecvArray.GetSize(); i++) {
		if ( tempRecvArray[i].Find("<SAI>") != -1 ) {
			tempString = tempRecvArray[i].Mid(5);
			tempInt = tempString.GetLength();
			if (tempString.GetAt(tempInt-1) == '\n' ) {
				tempString.SetAt(tempInt-1,' ');
				tempString.TrimRight();
			}
		}
	}
	cost = tempString;
#else
	string prodCostListTmp = (LPCTSTR)prodCostList;
	double resCost = getSessionMgrSO()->RunBaselineHelper(facilityDBID, prodCostListTmp, CSolution::Baseline);
	cost.Format(_T("%f"), resCost);
#endif

	return 0;

}//END CalculateBaselineCost

int COptimizationHelper::CalculateOptimizeCost(CString &cost, long facilityDBID, CString prodCostList)
{
#if 0
	CString tempString;
	int tempInt;
	CSsaStringArray tempSendArray;
	CSsaStringArray tempRecvArray;
	CString sendString;
	int i = 0;

	/////////////////////////////////////////////////////////////
	// Build the data and send to Forte connection
	/////////////////////////////////////////////////////////////
	sendString = "<SIO>SLOTSocketString\n";
	tempSendArray.Add(sendString);
	sendString = "<SAI>Calculate Optimize Cost\n";
	tempSendArray.Add(sendString);
	sendString.Format("<SAI>%d\n", facilityDBID);
	tempSendArray.Add(sendString);

	sendString.Format("<SAI>%s\n", prodCostList);
	tempSendArray.Add(sendString);

	sendString.Format("<SAI>%d\n", CSolution::Optimize);
	tempSendArray.Add(sendString);

	sendString = "<EIO>SLOTSocketString\n";
	tempSendArray.Add(sendString);

	try {
		forteService.SendToForteConnection(tempSendArray,tempRecvArray,
			CString("SLOTSocketString"), 6040);
	}
	catch(...) {
		cost = "0.00";
		return -1;
	}

	for (i=0; i < tempRecvArray.GetSize(); i++) {
		if ( tempRecvArray[i].Find("<SAI>") != -1 ) {
			tempString = tempRecvArray[i].Mid(5);
			tempInt = tempString.GetLength();
			if (tempString.GetAt(tempInt-1) == '\n' ) {
				tempString.SetAt(tempInt-1,' ');
				tempString.TrimRight();
			}
		}
	}
	cost = tempString;
#else
	string prodCostListTmp = (LPCTSTR)prodCostList;
	double resCost = getSessionMgrSO()->RunBaselineHelper(facilityDBID, prodCostListTmp, CSolution::Optimize);
	cost.Format(_T("%f"), resCost);
#endif

	return 0;

}

int COptimizationHelper::ProductLayout(int facilityId, int mode, const CString &options,
									   int maxResults, int facilityIntegrated, CStringArray &results)
{
#if 0
	CString tempString;
	int tempInt;
	CSsaStringArray tempSendArray;
	CSsaStringArray tempRecvArray;
	CString sendString;
	int i = 0;

	/////////////////////////////////////////////////////////////
	// build the data to send to the Forte connection server
	/////////////////////////////////////////////////////////////
	sendString = "<SIO>SLOTSocketString\n";
	tempSendArray.Add(sendString);
	tempString.Format("<SAI>%d\n", facilityId);
	tempSendArray.Add(tempString);
	tempString.Format("<SAI>%d\n", mode);
	tempSendArray.Add(tempString);
	tempString.Format("<SAI>%s\n", options);
	tempSendArray.Add(tempString);
	tempString.Format("<SAI>%d\n", maxResults);
	tempSendArray.Add(tempString);
	tempString.Format("<SAI>%d\n", facilityIntegrated);
	tempSendArray.Add(tempString);
	sendString = "<EIO>SLOTSocketString\n";
	tempSendArray.Add(sendString);

	forteService.SendToForteConnection(tempSendArray,tempRecvArray,
		CString("SLOTSocketString"), 3080);

	for (i=0; i < tempRecvArray.GetSize(); i++) {
		if ( tempRecvArray[i].Find("<SAI>") != -1 ) {
			tempString = tempRecvArray[i].Mid(5);
			tempInt = tempString.GetLength();
			if (tempString.GetAt(tempInt-1) == '\n' ) {
				tempString.SetAt(tempInt-1,' ');
				tempString.TrimRight();
			}
			results.Add(tempString);
		}
	}
#else
	string *optionsTmp = new string;
		*optionsTmp = (LPCTSTR)options;
	CListstringPtr res = getSessionMgrSO()->Pass4EvaluateHelper(facilityId,mode,optionsTmp,maxResults,facilityIntegrated);
	POSITION posSL = res->GetHeadPosition();
	for (int i=0; i<res->GetCount(); i++)
	{
		results.Add((res->GetNext(posSL)).c_str());
	}
#endif

	if ( results.GetSize() > 0 )
		return 0;
	else
		return 1;
}

///This function will be totally bypassed. RunCaipatlCostThread will directly call the function in OptiServer.dll
int COptimizationHelper::CapitalCost(int facilityId, const CString &options, int maxResults,
									 CStringArray &results)
{
	CString tempString;
	int tempInt;
	CSsaStringArray tempSendArray;
	CSsaStringArray tempRecvArray;
	CString sendString;
	int i = 0;

	/////////////////////////////////////////////////////////////
	// Build the data and send to Forte connection
	/////////////////////////////////////////////////////////////
	sendString = "<SIO>SLOTSocketString\n";
	tempSendArray.Add(sendString);
	sendString.Format("<SAI>%d\n", facilityId);
	tempSendArray.Add(sendString);
	sendString.Format("<SAI>%s\n", options);
	tempSendArray.Add(sendString);
	sendString.Format("<SAI>%d\n", maxResults);
	tempSendArray.Add(sendString);
	sendString = "<EIO>SLOTSocketString\n";
	tempSendArray.Add(sendString);

	forteService.SendToForteConnection(tempSendArray, tempRecvArray,
		CString("SLOTSocketString"), 2050);

	for (i=0; i < tempRecvArray.GetSize(); i++) {
		if ( tempRecvArray[i].Find("<SAI>") != -1 ) {
			tempString = tempRecvArray[i].Mid(5);
			tempInt = tempString.GetLength();
			if (tempString.GetAt(tempInt-1) == '\n' ) {
				tempString.SetAt(tempInt-1,' ');
				tempString.TrimRight();
			}
			results.Add(tempString);
		}
	}

	if ( results.GetSize() > 0 )
		return 0;
	else {
		return 1;
	}
}

int COptimizationHelper::ProductGroupLayout(int facilityId, const CString &options, int maxResults, CStringArray &results)
{
#if 0
	CString tempString;
	int tempInt;
	CSsaStringArray tempSendArray;
	CSsaStringArray tempRecvArray;
	CString sendString;
	int i = 0;

	/////////////////////////////////////////////////////////////
	// build the data to send to the Forte connection server
	/////////////////////////////////////////////////////////////
	sendString = "<SIO>SLOTSocketString\n";
	tempSendArray.Add(sendString);
	tempString.Format("<SAI>%d\n", facilityId);
	tempSendArray.Add(tempString);
	tempString.Format("<SAI>%s\n", options);
	tempSendArray.Add(tempString);
	tempString.Format("<SAI>%d\n", maxResults);
	tempSendArray.Add(tempString);
	sendString = "<EIO>SLOTSocketString\n";
	tempSendArray.Add(sendString);

	forteService.SendToForteConnection(tempSendArray,tempRecvArray,
		CString("SLOTSocketString"), 3070);

	for (i=0; i < tempRecvArray.GetSize(); i++) {
		if ( tempRecvArray[i].Find("<SAI>") != -1 ) {
			tempString = tempRecvArray[i].Mid(5);
			tempInt = tempString.GetLength();
			if (tempString.GetAt(tempInt-1) == '\n' ) {
				tempString.SetAt(tempInt-1,' ');
				tempString.TrimRight();
			}
			results.Add(tempString);
		}
	}
#else
	string optionsTmp = (LPCTSTR)options;
	CListstringPtr res = getSessionMgrSO()->Pass3RunPassHelper(facilityId,optionsTmp,maxResults);
	POSITION posSL = res->GetHeadPosition();
	for (int i=0; i<res->GetCount(); i++)
	{
		results.Add((res->GetNext(posSL)).c_str());
	}
#endif
	if ( results.GetSize() > 0 )
		return 0;
	else
		return 1;
}


int COptimizationHelper::StartModelComparison(int facilityDBID, int moveLength,int hoursWork, int maxMoves, CStringArray &retArray)
{
#if 0
	CString tempString;
	int tempInt;
	CSsaStringArray tempSendArray;
	CSsaStringArray tempRecvArray;
	CString sendString;
	int i = 0;

	/////////////////////////////////////////////////////////////
	// Build the data and send to Forte connection
	/////////////////////////////////////////////////////////////
	sendString = "<SIO>SLOTSocketString\n";
	tempSendArray.Add(sendString);
	sendString.Format("<SAI>%d\n",facilityDBID);
	tempSendArray.Add(sendString);
	sendString.Format("<SAI>%d\n",moveLength);
	tempSendArray.Add(sendString);
	sendString.Format("<SAI>%d\n",hoursWork);
	tempSendArray.Add(sendString);
	sendString.Format("<SAI>%d\n",maxMoves);
	tempSendArray.Add(sendString);
	sendString = "<EIO>SLOTSocketString\n";
	tempSendArray.Add(sendString);

	forteService.SendToForteConnection(tempSendArray,tempRecvArray,
		CString("SLOTSocketString"), 4080);

	for (i=0; i < tempRecvArray.GetSize(); i++) {
		if ( tempRecvArray[i].Find("<SAI>") != -1 ) {
			tempString = tempRecvArray[i].Mid(5);
			tempInt = tempString.GetLength();
			if (tempString.GetAt(tempInt-1) == '\n' ) {
				tempString.SetAt(tempInt-1,' ');
				tempString.TrimRight();
			}
			retArray.Add(tempString);
		}
	}
#else
	CListstringPtr res = getSessionMgrSO()->Pass5RunPassHelper(facilityDBID,moveLength,hoursWork,maxMoves);
	POSITION posSL = res->GetHeadPosition();
	for (int i=0; i<res->GetCount(); i++)
	{
		retArray.Add((res->GetNext(posSL)).c_str());
	}
#endif
	return 0;
}

void COptimizationHelper::ResetCaseCounts()
{
	CProcessingMessage dlg("Updating Assignment Case Counts", utilityHelper.GetParentWindow());
	CStringArray notFitList, msgList;

	try {

		CSolutionDataService s;

		int rc = AfxMessageBox("This process will recalculate the case capacity of all baseline "
			"assignments.  Do you wish to continue?", MB_YESNO);
		if (rc != IDYES)
			return;

		if (s.UpdateSolutionCaseCount(controlService.GetCurrentFacilityDBId(),
			CSolution::Baseline, 1, msgList, notFitList) < 0) {
			AfxMessageBox("Error updating case counts.");
		}
		else {
			if (notFitList.GetSize() > 0) {
				CDisplayResults dlg;
				dlg.m_WindowCaptions.Add("Products that do not fit in the assigned location.");
				dlg.m_Data = notFitList;
				dlg.m_AllowSort = TRUE;
				dlg.m_Headers.Add("Product Id|");
				dlg.m_Tabs.Add("Products|");

				dlg.DoModal();
			}

			AfxMessageBox("Case counts were successfully updated.\n"
			"Run Cost Comparison to recalculate the facility cost.");
		}

	}
	catch (...) {
		AfxMessageBox("Error updating case counts.");
	}

}

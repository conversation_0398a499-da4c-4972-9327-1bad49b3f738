// LayoutGroupsDialog.cpp : implementation file
//

#include "stdafx.h"
#include "modal.h"
#include "LayoutGroupsDialog.h"
#include "HelpService.h"
#include "ControlService.h"
#include "UtilityHelper.h"

#ifdef _DEBUG
#define new DEBUG_NEW
#undef THIS_FILE
static char THIS_FILE[] = __FILE__;
#endif

/////////////////////////////////////////////////////////////////////////////
// CLayoutGroupsDialog dialog

extern CHelpService helpService;
extern CControlService controlService;
extern CUtilityHelper utilityHelper;

CLayoutGroupsDialog::CLayoutGroupsDialog(CWnd* pParent /*=NULL*/)
	: CDialog(CLayoutGroupsDialog::IDD, pParent)
{
	//{{AFX_DATA_INIT(CLayoutGroupsDialog)
	m_LogMode = _T("None");
	m_MaxResults = 5000;
	m_IgnoreRankings = FALSE;
	//}}AFX_DATA_INIT
	m_Advanced = FALSE;
	m_Layout = LayoutByPickPath;
	m_Contiguous = 0;
}


void CLayoutGroupsDialog::DoDataExchange(CDataExchange* pDX)
{
	CDialog::DoDataExchange(pDX);
	//{{AFX_DATA_MAP(CLayoutGroupsDialog)
	DDX_Control(pDX, IDC_LOG_MODE, m_LogModeCtrl);
	DDX_CBString(pDX, IDC_LOG_MODE, m_LogMode);
	DDX_Text(pDX, IDC_MAX_RESULTS, m_MaxResults);
	DDX_Check(pDX, IDC_IGNORE_RANKINGS, m_IgnoreRankings);
	//}}AFX_DATA_MAP
}


BEGIN_MESSAGE_MAP(CLayoutGroupsDialog, CDialog)
	//{{AFX_MSG_MAP(CLayoutGroupsDialog)
	ON_WM_HELPINFO()
	ON_BN_CLICKED(IDHELP, OnHelp)
	ON_BN_CLICKED(IDC_ADVANCED, OnAdvanced)
	ON_BN_CLICKED(IDC_CONTIGUOUS, OnContiguous)
	//}}AFX_MSG_MAP
END_MESSAGE_MAP()

/////////////////////////////////////////////////////////////////////////////
// CLayoutGroupsDialog message handlers
BOOL CLayoutGroupsDialog::OnInitDialog() 
{
	CDialog::OnInitDialog();
	
	CString options;
	options = controlService.GetApplicationData("Options", "Dialogs\\ProductGroupLayoutStartup");
	
	if (options != "") {
		CStringArray optList;
		utilityHelper.ParseString(options, "|", optList);
		for (int i=0; i < optList.GetSize(); ++i) {
			switch (i) {
			case 0:
				m_Layout = atoi(optList[i]);
				break;
			case 1:
				m_Contiguous = atoi(optList[i]);
				break;
			case 2:
				m_LogMode = optList[i];
				break;
			case 3:
				m_MaxResults = atoi(optList[i]);
				break;
			case 4:
				m_IgnoreRankings = atoi(optList[i]);
				break;
			}
		}
	}

	CButton *pButton = (CButton *)GetDlgItem(IDC_LAYOUT_PICKPATHS);
	if (m_Layout == LayoutByPickPath)
		pButton->SetCheck(1);
	else {
		pButton = (CButton *)GetDlgItem(IDC_LAYOUT_HOTSPOT);
		pButton->SetCheck(1);
	}

	pButton = (CButton *)GetDlgItem(IDC_CONTIGUOUS);
	pButton->SetCheck(m_Contiguous);
	if (! m_Contiguous) {
		pButton = (CButton *)GetDlgItem(IDC_NOT_CONTIGUOUS);
		pButton->SetCheck(1);
	}

	m_LogModeCtrl.AddString("None");
	m_LogModeCtrl.AddString("User");
	m_LogModeCtrl.AddString("Expert");
	
	CRect r;
	m_LogModeCtrl.GetWindowRect(&r);
	m_LogModeCtrl.SetWindowPos(NULL, 0, 0, r.Width(), r.Height()*4, SWP_NOMOVE|SWP_NOZORDER);
	
	int curSel = m_LogModeCtrl.FindString(-1, m_LogMode);
	if (curSel >= 0)
		m_LogModeCtrl.SetCurSel(curSel);
	else
		m_LogModeCtrl.SetCurSel(0);

	int offset = 75;


	GetDlgItem(IDOK)->GetWindowRect(&r);
	this->ScreenToClient(&r);
	GetDlgItem(IDOK)->SetWindowPos(NULL, r.left, r.top-offset, 0, 0, SWP_NOZORDER|SWP_NOSIZE);
	
	GetDlgItem(IDCANCEL)->GetWindowRect(&r);
	this->ScreenToClient(&r);
	GetDlgItem(IDCANCEL)->SetWindowPos(NULL, r.left, r.top-offset, 0, 0, SWP_NOZORDER|SWP_NOSIZE);
	
	GetDlgItem(IDHELP)->GetWindowRect(&r);
	this->ScreenToClient(&r);
	GetDlgItem(IDHELP)->SetWindowPos(NULL, r.left, r.top-offset, 0, 0, SWP_NOZORDER|SWP_NOSIZE);
	
	this->GetWindowRect(&r);
	this->SetWindowPos(NULL, 0, 0, r.Width(), r.Height()-offset, SWP_NOMOVE|SWP_NOZORDER);

	UpdateData(FALSE);

	return TRUE;  // return TRUE unless you set the focus to a control
	              // EXCEPTION: OCX Property Pages should return FALSE
}

BOOL CLayoutGroupsDialog::OnHelpInfo(HELPINFO* pHelpInfo) 
{
	helpService.ShowFieldHelp(IDD, pHelpInfo->iCtrlId);

	return FALSE;

}

void CLayoutGroupsDialog::OnHelp() 
{
	helpService.ShowScreenHelp(IDD);

	return;

}

void CLayoutGroupsDialog::OnAdvanced() 
{
	int offset = 75;

	CRect r;
	if (m_Advanced) {
		m_Advanced = FALSE;
		GetDlgItem(IDC_LOGGING_STATIC)->ShowWindow(SW_HIDE);
		GetDlgItem(IDC_LOG_MODE)->ShowWindow(SW_HIDE);
		GetDlgItem(IDC_MAX_RESULTS)->ShowWindow(SW_HIDE);
		GetDlgItem(IDC_MAX_RESULTS_STATIC)->ShowWindow(SW_HIDE);
		GetDlgItem(IDC_IGNORE_RANKINGS)->ShowWindow(SW_HIDE);
		GetDlgItem(IDC_IGNORE_RANKINGS_STATIC)->ShowWindow(SW_HIDE);

		GetDlgItem(IDOK)->GetWindowRect(&r);
		this->ScreenToClient(&r);
		GetDlgItem(IDOK)->SetWindowPos(NULL, r.left, r.top-offset, 0, 0, SWP_NOZORDER|SWP_NOSIZE);

		GetDlgItem(IDCANCEL)->GetWindowRect(&r);
		this->ScreenToClient(&r);
		GetDlgItem(IDCANCEL)->SetWindowPos(NULL, r.left, r.top-offset, 0, 0, SWP_NOZORDER|SWP_NOSIZE);

		GetDlgItem(IDHELP)->GetWindowRect(&r);
		this->ScreenToClient(&r);
		GetDlgItem(IDHELP)->SetWindowPos(NULL, r.left, r.top-offset, 0, 0, SWP_NOZORDER|SWP_NOSIZE);

		this->GetWindowRect(&r);
		this->SetWindowPos(NULL, 0, 0, r.Width(), r.Height()-offset, SWP_NOMOVE|SWP_NOZORDER);
	}
	else {
		m_Advanced = TRUE;
		GetDlgItem(IDC_LOGGING_STATIC)->ShowWindow(SW_SHOW);
		GetDlgItem(IDC_LOG_MODE)->ShowWindow(SW_SHOW);
		GetDlgItem(IDC_MAX_RESULTS)->ShowWindow(SW_SHOW);
		GetDlgItem(IDC_MAX_RESULTS_STATIC)->ShowWindow(SW_SHOW);
		GetDlgItem(IDC_IGNORE_RANKINGS)->ShowWindow(SW_SHOW);
		GetDlgItem(IDC_IGNORE_RANKINGS_STATIC)->ShowWindow(SW_SHOW);

		GetDlgItem(IDOK)->GetWindowRect(&r);
		this->ScreenToClient(&r);
		GetDlgItem(IDOK)->SetWindowPos(NULL, r.left, r.top+offset, 0, 0, SWP_NOZORDER|SWP_NOSIZE);

		GetDlgItem(IDCANCEL)->GetWindowRect(&r);
		this->ScreenToClient(&r);
		GetDlgItem(IDCANCEL)->SetWindowPos(NULL, r.left, r.top+offset, 0, 0, SWP_NOZORDER|SWP_NOSIZE);

		GetDlgItem(IDHELP)->GetWindowRect(&r);
		this->ScreenToClient(&r);
		GetDlgItem(IDHELP)->SetWindowPos(NULL, r.left, r.top+offset, 0, 0, SWP_NOZORDER|SWP_NOSIZE);
		
		this->GetWindowRect(&r);
		this->SetWindowPos(NULL, 0, 0, r.Width(), r.Height()+offset, SWP_NOMOVE|SWP_NOZORDER);

	}	
}

void CLayoutGroupsDialog::OnOK() 
{

	UpdateData(TRUE);

	CButton *pButton = (CButton *)GetDlgItem(IDC_LAYOUT_PICKPATHS);
	if (pButton->GetCheck())
		m_Layout = LayoutByPickPath;
	else
		m_Layout = LayoutByHotspot;

	pButton = (CButton *)GetDlgItem(IDC_CONTIGUOUS);
	if (pButton->GetCheck()) 
		m_Contiguous = 1;
	else
		m_Contiguous = 0;

	CString temp;
	temp.Format("%d|%d|%s|%d|%d",
		m_Layout, m_Contiguous, m_LogMode, m_MaxResults, m_IgnoreRankings);

	controlService.SetApplicationData("Options", temp, "Dialogs\\ProductGroupLayoutStartup");

	CDialog::OnOK();
}

void CLayoutGroupsDialog::OnContiguous() 
{
	UpdateData(TRUE);
	
	if (m_Contiguous)
		GetDlgItem(IDC_IGNORE_RANKINGS)->EnableWindow(TRUE);
	else
		GetDlgItem(IDC_IGNORE_RANKINGS)->EnableWindow(FALSE);
}

// PickPathOptionDialog.cpp : implementation file
//

#include "stdafx.h"
#include "modal.h"
#include "PickPathOptionDialog.h"
#include "HelpService.h"

#ifdef _DEBUG
#define new DEBUG_NEW
#undef THIS_FILE
static char THIS_FILE[] = __FILE__;
#endif

extern CHelpService helpService;

/////////////////////////////////////////////////////////////////////////////
// CPickPathOptionDialog dialog


CPickPathOptionDialog::CPickPathOptionDialog(CWnd* pParent /*=NULL*/)
	: CDialog(CPickPathOptionDialog::IDD, pParent)
{
	//{{AFX_DATA_INIT(CPickPathOptionDialog)
	//}}AFX_DATA_INIT
}


void CPickPathOptionDialog::DoDataExchange(CDataExchange* pDX)
{
	CDialog::DoDataExchange(pDX);
	//{{AFX_DATA_MAP(CPickPathOptionDialog)
	//}}AFX_DATA_MAP
}


BEGIN_MESSAGE_MAP(CPickPathOptionDialog, CDialog)
	//{{AFX_MSG_MAP(CPickPathOptionDialog)
	ON_WM_HELPINFO()
	ON_BN_CLICKED(IDC_CONNECT, OnConnect)
	ON_BN_CLICKED(IDC_NEW, OnNew)
	//}}AFX_MSG_MAP
END_MESSAGE_MAP()

/////////////////////////////////////////////////////////////////////////////
// CPickPathOptionDialog message handlers
void CPickPathOptionDialog::OnPickpathOptionHelp() 
{
	helpService.ShowScreenHelp(IDD);

	return;
	
}

BOOL CPickPathOptionDialog::OnHelpInfo(HELPINFO* pHelpInfo) 
{
	helpService.ShowFieldHelp(IDD, pHelpInfo->iCtrlId);

	return FALSE;

}



void CPickPathOptionDialog::OnConnect() 
{
	EndDialog(WM_USER+1);	
}

void CPickPathOptionDialog::OnNew() 
{
	EndDialog(WM_USER);
}

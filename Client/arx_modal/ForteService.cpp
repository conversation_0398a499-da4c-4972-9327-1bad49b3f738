// ForteService.cpp: implementation of the CForteService class.
//
//////////////////////////////////////////////////////////////////////

#include "stdafx.h"
#include "modal.h"
#include "ForteService.h"
#include <socket_class.h>
#include "ssa_exception.h"
#include "ControlService.h"

#include <aced.h>
#ifdef _DEBUG
#undef THIS_FILE
static char THIS_FILE[]=__FILE__;
#define new DEBUG_NEW
#endif

#define SLOTTING_PORT   5010
#define FORTE_PORT		6010
#define FORTE_PORTINTERFACE		6020
#define MAXBUF 1024

int initted = 0;

//////////////////////////////////////////////////////////////////////
// Construction/Destruction
//////////////////////////////////////////////////////////////////////

extern CString AutoLoginParameters;
BOOL DEBUG = FALSE;
HANDLE sessionHandle;
extern char slotDir[256];
int storeNextNumber = 0;

CForteService::CForteService()
{

}

CForteService::~CForteService()
{

}

void CForteService::SendToForteConnection(CStringArray &sendData, CStringArray &recvData,
				 CString className, int operationNum) 
{
	CString sendBuf;
	CString recvBuf('\0',10*MAXBUF);
	CString tempString;
	CString leftString="";
	CStringArray tempBufArray;
	//char intbuf[15];
	char readbuf[MAXBUF+1];

	if ( initted == 0 ) {
		SockClass::init_winsock();
		initted = 1;
	}
	int readDone = 0;
	char nodename[129];
	int i = 0;
	int tempLength=0;
	int tempInd=0;

	/////////////////////////////////////////////////////////////
	// get the client host name and create a socket
	/////////////////////////////////////////////////////////////
	gethostname(nodename, 128);

	SockClass SockClient(nodename,FORTE_PORT);

	/////////////////////////////////////////////////////////////
	// notify what we are sending
	/////////////////////////////////////////////////////////////
	sendBuf.Format("<SSO>\n<SON>%s:%d\n<EOS>\n",className,operationNum);
	printf("Sending notification\n");
	SockClient.SendData(sendBuf.GetBuffer(0),sendBuf.GetLength());
	sendBuf.ReleaseBuffer();

	/////////////////////////////////////////////////////////////
	// socket server sends back handshaking
	/////////////////////////////////////////////////////////////
	memset(readbuf,0,MAXBUF);
	readDone = 0;
	recvBuf.Empty();
	printf("Receiving handshaking\n");
	while (readDone == 0) {
		if (SockClient.GetData(readbuf,MAXBUF) == 0)
			readDone = 1;
		if (readDone == 0) {
			recvBuf += readbuf;
			if (recvBuf.Find("<EOS>") != -1 )
				readDone = 1;
			memset(readbuf,0,MAXBUF+1);
		}
	}

/*	if ( recvBuf.Find("<EXCEPTION>") != -1 ) {
		CString exceptText = recvBuf.Mid(11);
		char msg[1024];
		strncpy(msg,exceptText.GetBuffer(0),1023);
		exceptText.ReleaseBuffer();
		throw Ssa_Exception(msg, __FILE__, __LINE__, 200);
	}
*/
	/////////////////////////////////////////////////////////////
	// build and send the message to the socket
	/////////////////////////////////////////////////////////////
	sendBuf = "<SSO>\n<SLO>\n"; //<SIO>" + className + "\n";
	for ( i = 0; i < sendData.GetSize(); i++ )
		sendBuf += sendData[i];
	sendBuf += "<ELO>\n<EOS>\n";

	printf("Sending Data\n");
	printf("%s\n",sendBuf);
	SockClient.SendData(sendBuf.GetBuffer(0),sendBuf.GetLength());
	sendBuf.ReleaseBuffer();

	memset(readbuf,0,MAXBUF);
	recvBuf.Empty();
	readDone = 0;
	
	/////////////////////////////////////////////////////////////
	// server sends back handshaking
	/////////////////////////////////////////////////////////////
	printf("Getting Handshaking\n");
	while (readDone == 0) {
		if (SockClient.GetData(readbuf,MAXBUF) == 0)
			readDone = 1;
		if (readDone == 0) {
			recvBuf += readbuf;
			if (recvBuf.Find("<EOS>") != -1 )
				readDone = 1;
			memset(readbuf,0,MAXBUF+1);
		}
	}

	/////////////////////////////////////////////////////////////
	// get the necessary data back from the socket
	/////////////////////////////////////////////////////////////
	printf("Receiving Data\n");
	recvBuf.Empty();
	readDone = 0;
	memset(readbuf,0,MAXBUF);

	while (readDone == 0) {
		if (SockClient.GetData(readbuf,MAXBUF) == 0)
			readDone = 1;
		//printf("%s",readbuf);
		if (readDone == 0) {
			if (leftString != "")
				tempString = leftString + readbuf;
			else
				tempString = readbuf;
			tempLength = tempString.GetLength();
			tempInd = tempString.Find('\n');
			if ( tempInd != -1 ) {
				recvData.Add(tempString.Left(tempInd));
				leftString = tempString.Mid(tempInd+1);
			}
			else
				leftString = tempString;
//			recvBuf += readbuf;
			if (strstr(readbuf,"<EOS>") != NULL )
				readDone = 1;
			memset(readbuf,0,MAXBUF+1);
		}
	}

	for ( i = 0; i < recvData.GetSize(); i++ ) {
		if ( recvData[i].Find("<EXCEPTION>") != -1 ) {
			CString exceptText = recvData[i].Mid(16);
			exceptText.Replace("<nl>", "\n");
			char msg[1024];
			strncpy(msg,exceptText.GetBuffer(0),1023);
			exceptText.ReleaseBuffer();
			printf("Sending Recognition\n");
			sendBuf = "<SSO>\n<EOS>\n";
			SockClient.SendData(sendBuf.GetBuffer(0),sendBuf.GetLength());
			sendBuf.ReleaseBuffer();
			throw Ssa_Exception(msg, __FILE__, __LINE__, 200);
		}
	}

	/////////////////////////////////////////////////////////////
	// build return data to send to caller
	/////////////////////////////////////////////////////////////
	printf("Building strings\n");


	/////////////////////////////////////////////////////////////
	// send recognition of receiving the data
	/////////////////////////////////////////////////////////////
	printf("Sending Recognition\n");
	sendBuf = "<SSO>\n<EOS>\n";
	SockClient.SendData(sendBuf.GetBuffer(0),sendBuf.GetLength());
	sendBuf.ReleaseBuffer();

	return;
}


// This one is the same as the above except we return immediately if the
// server doesn't respond.  This function is used to establish the initial
// connection so we don't want to wait in case there is a problem
int CForteService::SendToForteConnection2(CStringArray &sendData, CStringArray &recvData,
				 CString className, int operationNum) 
{
	CString sendBuf;
	CString recvBuf('\0',10*MAXBUF);
	CString tempString;
	CString leftString="";
	int retCode;
	CStringArray tempBufArray;
	//char intbuf[15];
	char readbuf[MAXBUF+1];

	if ( initted == 0 ) {
		SockClass::init_winsock();
		initted = 1;
	}
	int readDone = 0;
	char nodename[129];
	int i = 0;
	int tempLength=0;
	int tempInd=0;

	/////////////////////////////////////////////////////////////
	// get the client host name and create a socket
	/////////////////////////////////////////////////////////////
	gethostname(nodename, 128);

	SockClass SockClient(nodename,FORTE_PORT);

	/////////////////////////////////////////////////////////////
	// notify what we are sending
	/////////////////////////////////////////////////////////////
	sendBuf.Format("<SSO>\n<SON>%s:%d\n<EOS>\n",className,operationNum);
	printf("Sending notification\n");
	retCode = SockClient.SendData(sendBuf.GetBuffer(0),sendBuf.GetLength());
	sendBuf.ReleaseBuffer();
	if (retCode < 0)
		return retCode;

	/////////////////////////////////////////////////////////////
	// socket server sends back handshaking
	/////////////////////////////////////////////////////////////
	memset(readbuf,0,MAXBUF);
	readDone = 0;
	recvBuf.Empty();
	printf("Receiving handshaking\n");
	while (readDone == 0) {
		retCode = SockClient.GetData(readbuf,MAXBUF);
		if (retCode < 0)
			return retCode;
		if ( retCode == 0)
			readDone = 1;
		if (readDone == 0) {
			recvBuf += readbuf;
			if (recvBuf.Find("<EOS>") != -1 )
				readDone = 1;
			memset(readbuf,0,MAXBUF+1);
		}
	}

	/////////////////////////////////////////////////////////////
	// build and send the message to the socket
	/////////////////////////////////////////////////////////////
	sendBuf = "<SSO>\n<SLO>\n"; //<SIO>" + className + "\n";
	for ( i = 0; i < sendData.GetSize(); i++ )
		sendBuf += sendData[i];
	sendBuf += "<ELO>\n<EOS>\n";

	printf("Sending Data\n");
	printf("%s\n",sendBuf);
	retCode = SockClient.SendData(sendBuf.GetBuffer(0),sendBuf.GetLength());
	sendBuf.ReleaseBuffer();
	if (retCode < 0)
		return retCode;

	memset(readbuf,0,MAXBUF);
	recvBuf.Empty();
	readDone = 0;
	
	/////////////////////////////////////////////////////////////
	// server sends back handshaking
	/////////////////////////////////////////////////////////////
	printf("Getting Handshaking\n");
	while (readDone == 0) {
		retCode = SockClient.GetData(readbuf,MAXBUF);
		if (retCode < 0)
			return retCode;
		if ( retCode == 0)
			readDone = 1;
		if (readDone == 0) {
			recvBuf += readbuf;
			if (recvBuf.Find("<EOS>") != -1 )
				readDone = 1;
			memset(readbuf,0,MAXBUF+1);
		}
	}

	/////////////////////////////////////////////////////////////
	// get the necessary data back from the socket
	/////////////////////////////////////////////////////////////
	printf("Receiving Data\n");
	recvBuf.Empty();
	readDone = 0;
	memset(readbuf,0,MAXBUF);

	while (readDone == 0) {
		retCode = SockClient.GetData(readbuf,MAXBUF);
		if (retCode < 0)
			return retCode;
		if ( retCode == 0)
			readDone = 1;
		//printf("%s",readbuf);
		if (readDone == 0) {
			if (leftString != "")
				tempString = leftString + readbuf;
			else
				tempString = readbuf;
			tempLength = tempString.GetLength();
			tempInd = tempString.Find('\n');
			if ( tempInd != -1 ) {
				recvData.Add(tempString.Left(tempInd));
				leftString = tempString.Mid(tempInd+1);
			}
			else
				leftString = tempString;
//			recvBuf += readbuf;
			if (strstr(readbuf,"<EOS>") != NULL )
				readDone = 1;
			memset(readbuf,0,MAXBUF+1);
		}
	}

	for ( i = 0; i < recvData.GetSize(); i++ ) {
		if ( recvData[i].Find("<EXCEPTION>") != -1 ) {
			CString exceptText = recvData[i].Mid(16);
			char msg[1024];
			strncpy(msg,exceptText.GetBuffer(0),1023);
			exceptText.ReleaseBuffer();
			printf("Sending Recognition\n");
			sendBuf = "<SSO>\n<EOS>\n";
			SockClient.SendData(sendBuf.GetBuffer(0),sendBuf.GetLength());
			sendBuf.ReleaseBuffer();
			throw Ssa_Exception(msg, __FILE__, __LINE__, 200);
		}
	}

	/////////////////////////////////////////////////////////////
	// build return data to send to caller
	/////////////////////////////////////////////////////////////
	printf("Building strings\n");


	/////////////////////////////////////////////////////////////
	// send recognition of receiving the data
	/////////////////////////////////////////////////////////////
	printf("Sending Recognition\n");
	sendBuf = "<SSO>\n<EOS>\n";
	retCode = SockClient.SendData(sendBuf.GetBuffer(0),sendBuf.GetLength());
	sendBuf.ReleaseBuffer();
	if (retCode < 0)
		return retCode;

	return 0;
}




int CForteService::ConnectDatabase(CString userName, CString password, CString database)
{
	CString tempString;
	CStringArray tempSendArray;
	CStringArray tempRecvArray;
	CStringArray tempArray;
	CStringArray nakList;
	CString sendString;
	int i = 0;
	int tempInt;

	/////////////////////////////////////////////////////////////
	// build the data to send to the Forte connection server
	/////////////////////////////////////////////////////////////
	sendString = "<SIO>SLOTSocketString\n";
	tempSendArray.Add(sendString);
	sendString = "<SAI>" + userName + "\n";
	tempSendArray.Add(sendString);
	sendString = "<SAI>" + password + "\n";
	tempSendArray.Add(sendString);
	sendString = "<SAI>" + database + "\n";
	tempSendArray.Add(sendString);
	sendString = "<EIO>SLOTSocketString\n";
	tempSendArray.Add(sendString);

	SendToForteConnection(tempSendArray,tempRecvArray,
		CString("SLOTSocketString"), 7777);

	for (i=0; i < tempRecvArray.GetSize(); i++) {
		if ( tempRecvArray[i].Find("<SAI>") != -1 ) {
			tempString = tempRecvArray[i].Mid(5);
			tempInt = tempString.GetLength();
			if (tempString.GetAt(tempInt-1) == '\n' ) {
				tempString.SetAt(tempInt-1,' ');
				tempString.TrimRight();
			}
		}
	}

	return 0;
}

//////////////////////////////////////////////////////////////////////
// Function Name : StartSlotting
// Classname : None
// Description : Startup the additional Succeed processes
// Date Created : 10/01/98
// Author : wbh
//////////////////////////////////////////////////////////////////////
// Inputs : none
// Outputs : success code
// Explanation : 
//   Creates additional processes to start Succeed.  Waits 10 seconds
//   for them to initialize.
//////////////////////////////////////////////////////////////////////
// Algorithm : None
//////////////////////////////////////////////////////////////////////
// Revisions : 
//   Date-initials : Description_of_revision
//////////////////////////////////////////////////////////////////////
	/*******************************************************
	*
	*                       NOTICE
	*
	*  THIS SOFTWARE IS THE PROPERTY OF AND CONTAINS
	*  CONFIDENTIAL INFORMATION OF SSA GLOBAL 
	*  TECHNOLOGIES, INC., AND SHALL NOT BE COPIED,
	*  USED, NOR DISCLOSED WITHOUT EXPRESS WRITTEN
	*  AUTHORIZATION.  ALTHOUGH PUBLICATION IS NOT
	*  INTENDED, IN THE EVENT OF PUBLICATION, THE
	*  FOLLOWING NOTICE IS APPLICABLE:
	*
	*  (c) COPYRIGHT 2004 SSA GLOBAL TECHNOLOGIES, INC.
	*
	*           SSA GLOBAL TECHNOLOGIES, INC.
	*
	********************************************************/
//////////////////////////////////////////////////////////////////////
int CForteService::StartForte() 
{
	CString tempElementName;
	CString forteStart;
	CString forteRoot;
	CString windowMode;
	int tempRet;
	CControlService controlService;

	strcpy(slotDir, controlService.GetApplicationData("Home"));
	if (strcmp(slotDir, "") == 0) {
		AfxMessageBox("Fatal Error!  Cannot find registry entry.  Exit Program");
		return -1;
	}
	strcat(slotDir, "\\");

	STARTUPINFO si;
	PROCESS_INFORMATION pi;
	si.cb = sizeof(si);
	si.lpReserved = NULL;
	si.lpDesktop = NULL;
	si.lpTitle = "Optimize Session Manager";

    si.dwX =  si.dwY = STARTF_USEPOSITION;
		;
	si.dwXSize = si.dwYSize = STARTF_USESIZE;
    si.dwXCountChars = si.dwYCountChars = STARTF_USECOUNTCHARS;
    si.dwFillAttribute = STARTF_USEFILLATTRIBUTE;
    si.dwFlags =  STARTF_USESHOWWINDOW;
    si.cbReserved2 = 0;
    si.lpReserved2 = NULL;

	windowMode = controlService.GetApplicationData("WindowMode");
	if (windowMode != "")
		si.wShowWindow = SW_MINIMIZE;
	else
		si.wShowWindow = SW_HIDE; 

	/////////////////////////////////////////////////////////////
	// Set up the facility elements list
	/////////////////////////////////////////////////////////////
	//forteRoot = getenv("TEMP");
	CString memFlag;
	CString fnsFlag;
	CString autoParms;
	CString slotBinDir;
	CString logSessionToFile;
	
	memFlag = controlService.GetApplicationData("Memory");
	if (memFlag == "")
		memFlag = "50000";


	fnsFlag = controlService.GetApplicationData("ServerFNS");
	if (fnsFlag == "") {
		AfxMessageBox("Unable to determine the Optimize server from the registry.\n"
			"Please contact Optimize support.");
		return -1;
	}
	
	AutoLoginParameters = controlService.GetApplicationData("AutoLoginParameters");



	BOOL bRunning = FALSE;

	// Try it once just in case it's already up
	if (DEBUG) {
		if (GetTheNak(1) >= 0)
			return 0;
	}

	sessionHandle = NULL;	
	HWND oldSessionHandle = FindWindow(NULL,"Optimize Session Manager");
	if (oldSessionHandle != NULL) {
		if (DEBUG) 
			bRunning = TRUE;
		else {
			AfxMessageBox("Another instance of the Optimize Session Manager is already running.\n"
				"Exit Optimize, then use NT Task Manager to end the process called \"slotse0.exe\"."
				"\nIf the process does not exist, restart Optimize.");
			return -1;
		}
	}

	slotBinDir.Format("%sbin",slotDir);
	CString logFlags;

	logSessionToFile = controlService.GetApplicationData("SessionLog");
	if (logSessionToFile != "" && logSessionToFile != "0") {
		if (logSessionToFile == "1")		// 1 means default file
			logFlags.Format("-fl \"%s\\log\\SessionMgr.log(trc:* cfg:* err:*)\"", slotDir);
		else if (logSessionToFile == "2") {		// 2 means don't overwrite
			CTime t = CTime::GetCurrentTime();
			logFlags.Format("-fl \"%s\\log\\SessionMgr_%s.log(trc:* cfg:* err:*)\"", slotDir,
				t.Format("%y%m%d%H%M%S"));
		}
		else
			logFlags.Format("-fl %s", logSessionToFile);
	}
	forteStart.Format("%s\\slotse0.exe -fnd SlotClientNT -fm (n:%s) -fns %s %s", slotBinDir, memFlag, fnsFlag, logFlags);
	//forteStart.Format("%s\\slotse0.exe -fnd SlotClientNT -fm (n:%s) -fns %s", slotBinDir, memFlag, fnsFlag);

	tempRet = 1;

	if (! bRunning) {
		BOOL bCreated = CreateProcess(NULL, forteStart.GetBuffer(0), NULL, NULL, FALSE,
			NORMAL_PRIORITY_CLASS, NULL, slotBinDir.GetBuffer(0), &si, &pi);
		forteStart.ReleaseBuffer();
		slotBinDir.ReleaseBuffer();

		if (!bCreated) {
			AfxMessageBox("Could not start the Optimize Session Manager.\n"\
				"Please restart the application.\n"\
				"If this problem persists contact customer support.");
			return -1;
		}
		
		sessionHandle = pi.hProcess;
	}


	// this will send a message to slotse0 and wait for a response to make
	// sure we don't try to do anything before it is ready
	if (GetTheNak() < 0) {
		AfxMessageBox("Failed to connect to the Optimize Session Manager.\n"\
			"Please restart the application.\nIf this problem persists "\
			"contact customer support.");
		return -1;
	}

//	Sleep(3000);

	return 0;
}

//////////////////////////////////////////////////////////////////////
// Function Name : StopSlotting
// Classname : None
// Description : Stop the additional Succeed processes
// Date Created : 10/01/98
// Author : wbh
//////////////////////////////////////////////////////////////////////
// Inputs : none
// Outputs : success code
// Explanation : 
//   Send the necessary "kill" message across the sockets to stop
//   the additional Succeed processes.
//////////////////////////////////////////////////////////////////////
// Algorithm : None
//////////////////////////////////////////////////////////////////////
// Revisions : 
//   Date-initials : Description_of_revision
//////////////////////////////////////////////////////////////////////
	/*******************************************************
	*
	*                       NOTICE
	*
	*  THIS SOFTWARE IS THE PROPERTY OF AND CONTAINS
	*  CONFIDENTIAL INFORMATION OF SSA GLOBAL 
	*  TECHNOLOGIES, INC., AND SHALL NOT BE COPIED,
	*  USED, NOR DISCLOSED WITHOUT EXPRESS WRITTEN
	*  AUTHORIZATION.  ALTHOUGH PUBLICATION IS NOT
	*  INTENDED, IN THE EVENT OF PUBLICATION, THE
	*  FOLLOWING NOTICE IS APPLICABLE:
	*
	*  (c) COPYRIGHT 2004 SSA GLOBAL TECHNOLOGIES, INC.
	*
	*           SSA GLOBAL TECHNOLOGIES, INC.
	*
	********************************************************/
//////////////////////////////////////////////////////////////////////
int CForteService::StopForte() 
{
	CString sendBuf;
	CString recvBuf;
	CStringArray tempBufArray;
	int readDone = 0;
	char hostname[129];
	char readbuf[MAXBUF+1];
	int retCode;

	// if we didn't get a session, then we shouldn't try to close it
	if (sessionHandle != NULL) {
		SockClass::init_winsock();
		
		gethostname(hostname, 128);	
		try {
			SockClass SockClient(hostname,FORTE_PORT);
			
			sendBuf = "<SSO>\n<SON>SSAObject:9999\n<EOS>\n";
			
			retCode = SockClient.SendData(sendBuf.GetBuffer(0),sendBuf.GetLength());
			sendBuf.ReleaseBuffer();
			if (retCode > -1) {
				while (readDone == 0) {
					retCode = SockClient.GetData(readbuf,MAXBUF);
					if (retCode < 0)
						break;
					if ( retCode == 0)
						readDone = 1;
					if (readDone == 0) {
						recvBuf += readbuf;
						if (recvBuf.Find("<EOS>") != -1 )
							readDone = 1;
						memset(readbuf,0,MAXBUF+1);
					}
				}
			}
			if (retCode < 0) {
				int funcRet;
				DWORD exitCode;
				if ((funcRet = GetExitCodeProcess(sessionHandle, &exitCode)) != 0)
					if (exitCode == STILL_ACTIVE)
						TerminateProcess(sessionHandle, 0);
			}
		}
		catch (...) {
			int funcRet;
			// return code associated with process
			DWORD exitCode;
			
			if ((funcRet = GetExitCodeProcess(sessionHandle, &exitCode)) != 0)
				if (exitCode == STILL_ACTIVE)
					TerminateProcess(sessionHandle, 0);
		}
		
		WSACleanup();
	}

	return 0;
}

int CForteService::GetTheNak(int maxTries)
{
	CString tempString;
	CStringArray tempSendArray;
	CStringArray tempRecvArray;
	CStringArray tempArray;
	CStringArray nakList;
	CString sendString;
	CForteService forteService;
	int i = 0;
	int tempInt;

	/////////////////////////////////////////////////////////////
	// build the data to send to the Forte connection server
	/////////////////////////////////////////////////////////////
	sendString = "<SIO>SLOTSocketString\n";
	tempSendArray.Add(sendString);
	sendString = "<SAI>ACK\n";
	tempSendArray.Add(sendString);
	sendString = "<EIO>SLOTSocketString\n";
	tempSendArray.Add(sendString);

	BOOL bFailed = TRUE;
	CString msg;
	msg = "";
	ads_printf("\nConnecting to Session Manager...\n");
	while (maxTries > 0 && bFailed) {
		
		try {
			bFailed = TRUE;
			if (forteService.SendToForteConnection2(tempSendArray,tempRecvArray,
				CString("SLOTSocketString"), 6666) < 0) {
				msg = msg + ".";
				ads_printf("%s\n", msg);
				Sleep(1000);
				maxTries--;
				continue;
			}
			bFailed = FALSE;
		}
		catch (...) {
			bFailed = TRUE;
			msg = msg + ".";
			ads_printf("%s\n", msg);
			Sleep(1000);
			maxTries--;
		}
	}

	if (bFailed)
		return -1;


	ads_printf("Connected.\n");
	for (i=0; i < tempRecvArray.GetSize(); i++) {
		if ( tempRecvArray[i].Find("<SAI>") != -1 ) {
			tempString = tempRecvArray[i].Mid(5);
			tempInt = tempString.GetLength();
		
			if (tempString.GetAt(tempInt-1) == '\n' ) {
				tempString.SetAt(tempInt-1,' ');
				tempString.TrimRight();
			}
			nakList.Add(tempString);
		}
	}
	if ( nakList.GetSize() == 0 )
		return -2;
	else
		return 0;
}
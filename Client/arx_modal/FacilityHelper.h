// FacilityHelper.h: interface for the CFacilityHelper class.
//
//////////////////////////////////////////////////////////////////////

#if !defined(AFX_FACILITYHELPER_H__62929529_4D20_424E_9269_A8197879B2F7__INCLUDED_)
#define AFX_FACILITYHELPER_H__62929529_4D20_424E_9269_A8197879B2F7__INCLUDED_

#if _MSC_VER > 1000
#pragma once
#endif // _MSC_VER > 1000

class CFacilityHelper  
{
public:
	BOOL FacilityIsModified();
	void ClearDrawing();
	CFacilityHelper();
	virtual ~CFacilityHelper();
	void OpenDrawing();
	void OpenFacility();
	void NewFacility();
	void SaveFacility();
	void SaveAsFacility();
	void DeleteFacility();
	void ExportFacility();
	void ImportFacility();
	void DoForteSave();
	void ConvertDrawing();

	BOOL CheckCurrentFacility();
	int QuickSave();
	int QuickSaveAs();
	void RunNewFacilityScript(const CString &command = "");
	int ImportFacility(CString fileName, CString &origName, CString &newName);
	CString ExportFacility(int facilityID, CString fileName);
};

#endif // !defined(AFX_FACILITYHELPER_H__62929529_4D20_424E_9269_A8197879B2F7__INCLUDED_)



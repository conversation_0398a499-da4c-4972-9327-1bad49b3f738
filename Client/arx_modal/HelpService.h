// HelpService.h: interface for the CHelpService class.
//
//////////////////////////////////////////////////////////////////////

#if !defined(AFX_HELPSERVICE_H__64DD845F_0E8B_4524_B0B9_3FDB87E6C9C6__INCLUDED_)
#define AFX_HELPSERVICE_H__64DD845F_0E8B_4524_B0B9_3FDB87E6C9C6__INCLUDED_

#if _MSC_VER > 1000
#pragma once
#endif // _MSC_VER > 1000

class CHelpService : public CObject  
{
public:
	void ShowScreenHelp(const CString &helpTopic);
	void ShowFieldHelp(const CString &helpTopic);
	void Initialize();
	void ShowScreenHelp(int screenID);
	void ShowFieldHelp(int screenID, int fieldID);
	CHelpService();
	virtual ~CHelpService();
private:
	void RunHelp(const CString &helpTopic);
	CMapWordToPtr m_HelpMap;
};

#endif // !defined(AFX_HELPSERVICE_H__64DD845F_0E8B_4524_B0B9_3FDB87E6C9C6__INCLUDED_)

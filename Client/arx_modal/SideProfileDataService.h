// SideProfileDataService.h: interface for the CSideProfileDataService class.
//
//////////////////////////////////////////////////////////////////////

#if !defined(AFX_SIDEPROFILEDATASERVICE_H__14154340_41CE_4DBD_AC71_648CFC672A88__INCLUDED_)
#define AFX_SIDEPROFILEDATASERVICE_H__14154340_41CE_4DBD_AC71_648CFC672A88__INCLUDED_

#if _MSC_VER > 1000
#pragma once
#endif // _MSC_VER > 1000

#include "qqhclasses.h"
#include "SideProfile.h"

class CSideProfileDataService  
{
public:
	int GetAisleProfileNamesBySideProfile(int sideProfileDBId, CStringArray &aisleNames);
	int UpdateSideProfileName(int sideProfileDBId, const CString &name);
	int GetSideProfileList(CStringArray &sideNameList);
	int GetSideProfile(int sideProfileDBId, CSideProfile& sideProfile, int loadBayOptions = CBayProfile::loadLevels);
	int DeleteSideProfile(int sideProfileDBId);
	int StoreSideProfile(CSideProfile& sideProfile);

	BOOL IsSideProfileNameInUse(int dbid, const CString &name);
	CSideProfileDataService();
	virtual ~CSideProfileDataService();
	int GetSideProfileNameList(CStringArray &sideNameList);


private:
};

#endif // !defined(AFX_SIDEPROFILEDATASERVICE_H__14154340_41CE_4DBD_AC71_648CFC672A88__INCLUDED_)

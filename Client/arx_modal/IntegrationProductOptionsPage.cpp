// IntegrationProductOptionsPage.cpp : implementation file
//

#include "stdafx.h"
#include "modal.h"
#include "IntegrationProductOptionsPage.h"
#include "HelpService.h"


#ifdef _DEBUG
#define new DEBUG_NEW
#undef THIS_FILE
static char THIS_FILE[] = __FILE__;
#endif

extern CHelpService helpService;
/////////////////////////////////////////////////////////////////////////////
// CIntegrationProductOptionsPage property page

IMPLEMENT_DYNCREATE(CIntegrationProductOptionsPage, CPropertyPage)

CIntegrationProductOptionsPage::CIntegrationProductOptionsPage() : CPropertyPage(CIntegrationProductOptionsPage::IDD)
{
	//{{AFX_DATA_INIT(CIntegrationProductOptionsPage)
	m_SkipProduct = FALSE;
	m_InboundPrompt = FALSE;
	m_SkipPgUpdate = FALSE;
	//}}AFX_DATA_INIT
}

CIntegrationProductOptionsPage::~CIntegrationProductOptionsPage()
{
}

void CIntegrationProductOptionsPage::DoDataExchange(CDataExchange* pDX)
{
	CPropertyPage::DoDataExchange(pDX);
	//{{AFX_DATA_MAP(CIntegrationProductOptionsPage)
	DDX_Check(pDX, IDC_SKIP, m_SkipProduct);
	DDX_Check(pDX, IDC_INBOUND_PROMPT, m_InboundPrompt);
	DDX_Check(pDX, IDC_SKIP_PG_UPDATE, m_SkipPgUpdate);
	//}}AFX_DATA_MAP
}


BEGIN_MESSAGE_MAP(CIntegrationProductOptionsPage, CPropertyPage)
	//{{AFX_MSG_MAP(CIntegrationProductOptionsPage)
	ON_BN_CLICKED(IDC_INBOUND_PROMPT, OnInboundPrompt)
	ON_BN_CLICKED(IDC_SKIP, OnSkip)
	ON_BN_CLICKED(IDC_SKIP_PG_UPDATE, OnSkipPgUpdate)
	ON_WM_HELPINFO()
	//}}AFX_MSG_MAP
END_MESSAGE_MAP()

/////////////////////////////////////////////////////////////////////////////
// CIntegrationProductOptionsPage message handlers

BOOL CIntegrationProductOptionsPage::OnInitDialog() 
{
	CPropertyPage::OnInitDialog();
	
	OnSkip();
	
	return TRUE;  // return TRUE unless you set the focus to a control
	              // EXCEPTION: OCX Property Pages should return FALSE
}

void CIntegrationProductOptionsPage::OnInboundPrompt() 
{
	// TODO: Add your control notification handler code here
	
}

void CIntegrationProductOptionsPage::OnSkip() 
{
	UpdateData(TRUE);
	
	if (m_SkipProduct) {
		GetDlgItem(IDC_SKIP_PG_UPDATE)->EnableWindow(FALSE);
		GetDlgItem(IDC_INBOUND_PROMPT)->EnableWindow(FALSE);
	}
	else {
		GetDlgItem(IDC_SKIP_PG_UPDATE)->EnableWindow(TRUE);
		GetDlgItem(IDC_INBOUND_PROMPT)->EnableWindow(TRUE);
	}	
}

void CIntegrationProductOptionsPage::OnSkipPgUpdate() 
{
	// TODO: Add your control notification handler code here
	
}

BOOL CIntegrationProductOptionsPage::OnHelpInfo(HELPINFO* pHelpInfo) 
{
	helpService.ShowFieldHelp(IDD, pHelpInfo->iCtrlId);
	return FALSE;

}

BOOL CIntegrationProductOptionsPage::OnNotify(WPARAM wParam, LPARAM lParam, LRESULT* pResult) 
{
	NMHDR* pNMHDR = (NMHDR*)lParam;

	if (pNMHDR->code == PSN_HELP) {
		helpService.ShowScreenHelp(IDD);
		return TRUE;
	}
	
	return CPropertyPage::OnNotify(wParam, lParam, pResult);
}
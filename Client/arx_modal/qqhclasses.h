#ifndef QQHCLASSES_H
#define QQHCLASSES_H
#include <afxtempl.h>
#include "SSACStringArray.h"

#include "BayProfile.h"
#include "Facility.h"

/////////////////////////////////////////////////////////////
//  Search strings and other global data
/////////////////////////////////////////////////////////////

#define AisleBegSearch "<SIO>SLOTAisle"
#define AisleEndSearch "<EIO>SLOTAisle"
#define CoordBegSearch "<SIO>SLOTCoordinate"
#define CoordEndSearch "<EIO>SLOTCoordinate"
#define PathBegSearch "<SIO>SLOTPickPath"
#define PathEndSearch "<EIO>SLOTPickPath"
#define SideBegSearch "<SIO>SLOTSide"
#define SideEndSearch "<EIO>SLOTSide"
#define BayBegSearch "<SIO>SLOTBay"
#define BayEndSearch "<EIO>SLOTBay"
#define LevelBegSearch "<SIO>SLOTLevel"
#define LevelEndSearch "<EIO>SLOTLevel"
#define LocationBegSearch "<SIO>SLOTLocation"
#define LocationEndSearch "<EIO>SLOTLocation"
#define FacilityBegSearch "<SIO>SLOTFacility"
#define FacilityEndSearch "<EIO>SLOTFacility"
#define SectionBegSearch "<SIO>SLOTSection"
#define SectionEndSearch "<EIO>SLOTSection"
#define DimenBegSearch "<SIO>SLOTDimension"
#define DimenEndSearch "<EIO>SLOTDimension"
#define GroupBayBegSearch "<SIO>SLOTGroupBay"
#define GroupBayEndSearch "<EIO>SLOTGroupBay"
#define REGBegSearch "<SAI>REG|"
#define UDFBegSearch "<SAI>UDF|"
#define BegObjSearch "<SIO>"
#define EndObjSearch "<EIO>"
#define ContainerBegSearch "<SIO>SLOTProductContainer"
#define ContainerEndSearch "<EIO>SLOTProductContainer"
#define PackBegSearch "<SIO>SLOTProductPack"
#define PackEndSearch "<EIO>SLOTProductPack"
#define ContainerListBeg "<SLO>SLOTProductContainer"
#define ContainerListEnd "<ELO>SLOTProductContainer"
#define SlotGroupBegSearch "<SIO>SLOTSlottingGroup"
#define SlotGroupEndSearch "<EIO>SLOTSlottingGroup"
#define QueryBegSearch "<SIO>SLOTQuery"
#define QueryEndSearch "<EIO>SLOTQuery"
#define QueryAttrBegSearch "<SIO>SLOTQueryAttr"
#define QueryAttrEndSearch "<EIO>SLOTQueryAttr"
#define HotSpotBegSearch "<SIO>SLOTHotspot"
#define HotSpotEndSearch "<EIO>SLOTHotspot"


class qqhSLOTCoordinate;
class qqhSLOTObject;
class qqhSLOTHolder;
class qqhSLOTFacility;
class qqhSLOTSection;
class qqhSLOTAisle;
class qqhSLOTSide;
class qqhSLOTBay;
class qqhSLOTLevel;
class qqhSLOTLocation;
class qqhSLOTQueryAttr;
class qqhSLOTQuery;
class qqhSLOTPickPath;
class qqhSLOTProductContainer;
class qqhSLOTProductPack;


/////////////////////////////////////////
// Coordinate class
/////////////////////////////////////////
class qqhSLOTCoordinate
{
protected:
	int x;
	int y;
	int z;

public:
	///////////////////////////////////////////
	//Gets and Sets
	///////////////////////////////////////////
	int getX() { return x; }
	int getY() { return y; }
	int getZ() { return z; }

	void setX(int pX) { x = pX; }
	void setY(int pY) { y = pY; }
	void setZ(int pZ) { z = pZ; }

	///////////////////////////////////////////
	//Constructors, Destructors, and operators
	///////////////////////////////////////////
	qqhSLOTCoordinate(int xCoord, int yCoord, int zCoord);
	qqhSLOTCoordinate();
	virtual ~qqhSLOTCoordinate(){}
	qqhSLOTCoordinate(const qqhSLOTCoordinate & other);
	qqhSLOTCoordinate& operator=(const qqhSLOTCoordinate &other);
	BOOL operator==(const qqhSLOTCoordinate &other);
	BOOL operator!=(const qqhSLOTCoordinate &other) { return (! (*this == other)); };
	///////////////////////////////////////////
	//Other methods
	///////////////////////////////////////////
	void BuildFromStream(CSsaStringArray &bufArray);
	void StreamAttributes(CSsaStringArray & attributBuf);
	CString ToText();
	void FromText(CString &txt);
};


/////////////////////////////////////////
// Base Object class
/////////////////////////////////////////
class qqhSLOTObject : public CObject
{
protected:

	int					DBID;
	qqhSLOTCoordinate	coord;
	CString				type;
	CString				description;
	CSsaStringArray		UDFList;


public:
	///////////////////////////////////////////
	//Gets and Sets
	///////////////////////////////////////////
	int					getDBID() { return DBID; }
	qqhSLOTCoordinate&	getCoord() { return coord; }
	CString&			getType() { return type; }
	CString&			getDescription() { return description; }
	CSsaStringArray&	getUDFList() { return UDFList; }


	void	setDBID(int pDBID) { DBID = pDBID; }
	void	setCoord(const qqhSLOTCoordinate & pCoord) { coord = pCoord; }
	void	setType(const CString & pType) { type = pType; }
	void	setDescription(const CString& pDescription) { description = pDescription; }
	void	setUDFList(const CSsaStringArray & pUDFList) { UDFList.Copy(pUDFList); }

	///////////////////////////////////////////
	//Constructors, Destructors, and operators
	///////////////////////////////////////////
	qqhSLOTObject();
	qqhSLOTObject(const qqhSLOTObject & other);
	virtual ~qqhSLOTObject(){UDFList.RemoveAll();}
	///////////////////////////////////////////
	//Other Methods
	///////////////////////////////////////////
	void BuildFromStream(CSsaStringArray &bufArray) { UDFList.RemoveAll(); bufArray.RemoveAll(); return;}
	void StreamAttributes(CSsaStringArray & attributBuf) {attributBuf.RemoveAll(); return;}
};

class qqhSLOTHotSpot :  public qqhSLOTObject
{
protected:
	int hotSpotType;
	char acadHandle[20];

public:
	///////////////////////////////////////////
	//Gets and Sets
	///////////////////////////////////////////
	int				getHotSpotType() { return hotSpotType; }
	char *			getAcadHandle() { return acadHandle; }

	void			setHotSpotType(int pHotSpotType) { hotSpotType = pHotSpotType; }
	void			setAcadHandle(const char * pAcadHandle) { strcpy(acadHandle,pAcadHandle); }

	///////////////////////////////////////////
	//Constructors, Destructors, and operators
	///////////////////////////////////////////
	qqhSLOTHotSpot();
	qqhSLOTHotSpot(const qqhSLOTHotSpot &other);
	qqhSLOTHotSpot& operator=(const qqhSLOTHotSpot &other);
	virtual ~qqhSLOTHotSpot() {}
	///////////////////////////////////////////
	//Other Methods
	///////////////////////////////////////////
	void BuildFromStream(CSsaStringArray &bufArray);
	void StreamAttributes(CSsaStringArray & attributBuf);
};


/////////////////////////////////////////
// Pick Path Object class
/////////////////////////////////////////
class qqhSLOTPickPath : public qqhSLOTObject
{
protected:
	float pathLength;
	char acadHandle[20];
	char conAcadHandle[20];

public:
	///////////////////////////////////////////
	//Gets and Sets
	///////////////////////////////////////////
	float			getPathLength() { return pathLength; }
	char *			getAcadHandle() { return acadHandle; }
	char *			getConAcadHandle() { return conAcadHandle; }

	void			setAcadHandle(const char * pAcadHandle) { strcpy(acadHandle,pAcadHandle); }
	void			setPathLength(float pPathLength) { pathLength = pPathLength; }
	void			setConAcadHandle(const char * pConAcadHandle) { strcpy(conAcadHandle,pConAcadHandle); }

	///////////////////////////////////////////
	//Constructors, Destructors, and operators
	///////////////////////////////////////////
	qqhSLOTPickPath();
	virtual ~qqhSLOTPickPath() {}
	qqhSLOTPickPath(const qqhSLOTPickPath & other);
	qqhSLOTPickPath& operator=(const qqhSLOTPickPath &other);
	///////////////////////////////////////////
	//Other Methods
	///////////////////////////////////////////
	void BuildFromStream(CSsaStringArray &bufArray);
	void StreamAttributes(CSsaStringArray & attributBuf);
};

/////////////////////////////////////////
// Query classes
/////////////////////////////////////////
class qqhSLOTQueryAttr : public qqhSLOTObject
{
protected :
	CString attrName;
	CString attrValue;
	CString conjunction;
	CString queryOperator;

public:
	///////////////////////////////////////////
	//Gets and Sets
	///////////////////////////////////////////
	CString &	getAttrName() { return attrName; }
	CString &	getAttrValue() { return attrValue; }
	CString &	getConjunction() { return conjunction; }
	CString &	getQueryOperator() { return queryOperator; }

	void	setAttrName(const CString & pAttrName) { attrName = pAttrName; }
	void	setAttrValue(const CString & pAttrValue) { attrValue = pAttrValue; }
	void	setConjunction(const CString & pConjunction) { conjunction = pConjunction; }
	void	setQueryOperator(const CString & pQueryOperator) { queryOperator = pQueryOperator; }

	///////////////////////////////////////////
	//Constructors, Destructors, and operators
	///////////////////////////////////////////
	qqhSLOTQueryAttr();
	virtual ~qqhSLOTQueryAttr(){}
	qqhSLOTQueryAttr(const qqhSLOTQueryAttr & other);
	qqhSLOTQueryAttr& operator=(const qqhSLOTQueryAttr &other);
	///////////////////////////////////////////
	//Other Methods
	///////////////////////////////////////////
	void BuildFromStream(CSsaStringArray &bufArray);
	void StreamAttributes(CSsaStringArray & attributBuf);
};

class qqhSLOTQuery : public qqhSLOTObject
{
protected:
	CArray <qqhSLOTQueryAttr, qqhSLOTQueryAttr&> attrList;
	CString objName;

public:
	///////////////////////////////////////////
	//Gets and Sets
	///////////////////////////////////////////
	CArray <qqhSLOTQueryAttr, qqhSLOTQueryAttr&>&	getAttrList() { return attrList; }
	CString &getObjName() { return objName; }

	void setAttrList(const CArray <qqhSLOTQueryAttr, qqhSLOTQueryAttr&> & pAttrList) { attrList.Copy(pAttrList); }
	void setObjName(const CString & pObjName) { objName = pObjName; }

	///////////////////////////////////////////
	//Constructors, Destructors, and operators
	///////////////////////////////////////////
	qqhSLOTQuery();
	virtual ~qqhSLOTQuery() {attrList.RemoveAll();}
	qqhSLOTQuery(qqhSLOTQuery & other);
	qqhSLOTQuery& operator=(qqhSLOTQuery & other);
	///////////////////////////////////////////
	//Other Methods
	///////////////////////////////////////////
	void AddQueryAttr(CString pAttribute,CString pValue, CString pConjunction, CString pQueryOperator);
	void BuildFromStream(CSsaStringArray &bufArray);
	void StreamAttributes(CSsaStringArray & attributBuf);
};





/////////////////////////////////////////
// Holder class
/////////////////////////////////////////
class qqhSLOTHolder : public qqhSLOTObject
{
protected:
	CString isChanged;

public:

	///////////////////////////////////////////
	//Gets and Sets
	///////////////////////////////////////////
	CString & getIsChanged() { return isChanged; }

	void setIsChanged( const CString & pIsChanged ) { isChanged = pIsChanged; }

	///////////////////////////////////////////
	//Constructors, Destructors, and operators
	///////////////////////////////////////////
	qqhSLOTHolder();
	virtual ~qqhSLOTHolder() {}
	qqhSLOTHolder(const qqhSLOTHolder & other);
};

/////////////////////////////////////////
// Location class
/////////////////////////////////////////
class qqhSLOTLocation : public qqhSLOTHolder
{
protected:
	double maxWeight;
	double width;
	double depth;
	double height;
	int handlingMethod;
	int IsSelect;
	int IsOverridden;
	int ChangedInPass;
	int Status;
	int IsActive;
	double Clearance;
	CString BackfillId;
	qqhSLOTCoordinate BackfillCoordinates;
	CString StockerId;
	qqhSLOTCoordinate StockerCoordinates;
	int Trace;
	int locationProfileId;
	CString selectionSequence;
	CString replenishmentSequence;
	int locationKey;
public:
	CLocationProfile *pLocationProfile;
	CTypedPtrArray<CObArray, CLevelProfileExternalInfo*> m_InfoList;

	BOOL isLocationOverridden(const CLocationProfile& other);

	///////////////////////////////////////////
	//Gets and Sets
	///////////////////////////////////////////
	double getMaxWeight()        { return maxWeight; }
	double getWidth()            { return width; }
	double getDepth()            { return depth; }
	double getHeight()           { return height; }
	int    getHandlingMethod()   { return handlingMethod; }
	int    getIsSelect()         { return IsSelect; }
	int    getIsOverridden()     { return IsOverridden; }
	int		getChangedInPass()	 { return ChangedInPass; }
	int getStatus() { return Status; }
	int	getIsActive() { return IsActive; }
	double getClearance() { return Clearance; }
	CString getBackfillId() { return BackfillId; }
	qqhSLOTCoordinate& getBackfillCoordinates() { return BackfillCoordinates; }
	CString getStockerId() { return StockerId; }
	qqhSLOTCoordinate& getStockerCoordinates() { return StockerCoordinates; }
	int getTrace() { return Trace; }
	CString getSelectionSequence() { return selectionSequence; }
	CString getReplenishmentSequence() { return replenishmentSequence; }
	CString getStatusText();
	int getLocationKey() { return locationKey; }

	int getLocationProfileId() { return locationProfileId; }

	void setMaxWeight( double pMaxWeight ) { maxWeight = pMaxWeight; }
	void setWidth( double pWidth ) { width = pWidth; }
	void setDepth( double pDepth ) { depth = pDepth; }
	void setHeight( double pHeight ) { height = pHeight; }
	void setHandlingMethod (int pHandlingMethod) { handlingMethod = pHandlingMethod; }
	void setIsSelect (int pIsSelect) { IsSelect = pIsSelect; }
	void setIsOverridden (int pIsOverridden) { IsOverridden = pIsOverridden; }
	void setChangedInPass (int pChangedInPass) { ChangedInPass = pChangedInPass; }
	void setLocationProfileId(int pLocationProfileId) { locationProfileId = pLocationProfileId; }
	void setStatus(int pStatus) { Status = pStatus; }
	void setIsActive(int pIsActive) { IsActive = pIsActive; }
	void setClearance(double pClearance) { Clearance = pClearance; }
	void setBackfillId(const CString &pBackfillId) { BackfillId = pBackfillId; }
	void setBackfillCoordinates(qqhSLOTCoordinate &pCoord) { BackfillCoordinates = pCoord; }
	void setStockerId(CString& pStockerId) { StockerId = pStockerId; }
	void setStockerCoordinates(qqhSLOTCoordinate &pCoord) { StockerCoordinates = pCoord; }
	void setTrace(int pTrace) { Trace = pTrace; }
	void setSelectionSequence(const CString& pSelectionSequence) { selectionSequence = pSelectionSequence; }
	void setReplenishmentSequence(const CString& pReplenishmentSequence) { replenishmentSequence = pReplenishmentSequence; }
	void setLocationKey(int pLocationKey) { locationKey = pLocationKey; }
	///////////////////////////////////////////
	//Constructors, Destructors, and operators
	///////////////////////////////////////////
	qqhSLOTLocation();
	virtual ~qqhSLOTLocation();

	qqhSLOTLocation(const qqhSLOTLocation& other);
	qqhSLOTLocation& operator=(const qqhSLOTLocation& other);
	BOOL operator==(const qqhSLOTLocation &other);
	///////////////////////////////////////////
	//Other Methods
	///////////////////////////////////////////
	void BuildFromStream(CSsaStringArray &bufArray);
	void StreamAttributes(CSsaStringArray & attributBuf);
};

/////////////////////////////////////////
// Level class
/////////////////////////////////////////
class qqhSLOTLevel : public qqhSLOTHolder
{
protected:
	double forkFixedInsertion;
	BOOL	isRotateAllowed;
	BOOL	isVariableLocationsAllowed;
	float	facingGap;
	float	facingSnap;
	float	minLocWidth;
	float	productGap;
	float	productSnap;
	BOOL	isOverridden;
	int		levelProfileId;
	CArray <qqhSLOTLocation, qqhSLOTLocation&> childList;


public:
	CLevelProfile *pLevelProfile;
	BOOL isLevelOverridden(const CLevelProfile& other);

	///////////////////////////////////////////
	//Gets and Sets
	///////////////////////////////////////////
	float	getForkFixedInsertion() { return (float)forkFixedInsertion; }
	BOOL	getIsRotateAllowed() { return isRotateAllowed; }
	BOOL	getIsVariableLocationsAllowed() { return isVariableLocationsAllowed; }
	float	getFacingGap() { return (float)facingGap; }
	float	getFacingSnap() { return (float)facingSnap; }
	float	getMinLocWidth() { return (float)minLocWidth; }
	float	getProductGap() { return (float)productGap; }
	float	getProductSnap() { return (float)productSnap; }
	BOOL	getIsOverridden() { return isOverridden; }
	int		getLevelProfileId() { return levelProfileId; }
	CArray <qqhSLOTLocation, qqhSLOTLocation&> & getChildList () { return childList; }

	void	setForkFixedInsertion(float pForkFixedInsertion) { forkFixedInsertion = pForkFixedInsertion; }
	void	setIsRotateAllowed(BOOL pIsRotateAllowed) { isRotateAllowed = pIsRotateAllowed; }
	void	setIsVariableLocationsAllowed(BOOL pIsVariableLocationsAllowed) { isVariableLocationsAllowed = pIsVariableLocationsAllowed; }
	void	setFacingGap(float pFacingGap) { facingGap = pFacingGap; }
	void	setFacingSnap(float pFacingSnap) { facingSnap = pFacingSnap; }
	void	setMinLocWidth(float pMinLocWidth) { minLocWidth = pMinLocWidth; }
	void	setProductGap(float pProductGap) { productGap = pProductGap; }
	void	setProductSnap(float pProductSnap) { productSnap = pProductSnap; }
	void	setIsOverridden(BOOL pIsOverridden) { isOverridden = pIsOverridden; }
	void	setLevelProfileId(int pLevelProfileId) { levelProfileId = pLevelProfileId; }
	void	setChildList(const CArray <qqhSLOTLocation, qqhSLOTLocation&> & pChildList) { childList.Copy(pChildList); }


	///////////////////////////////////////////
	//Constructors, Destructors, and operators
	///////////////////////////////////////////
	qqhSLOTLevel();
	virtual ~qqhSLOTLevel();
	qqhSLOTLevel(const qqhSLOTLevel& other);
	qqhSLOTLevel& operator=(const qqhSLOTLevel& other);
	BOOL operator==(const qqhSLOTLevel &other);
	///////////////////////////////////////////
	//Other Methods
	///////////////////////////////////////////
	void BuildFromStream(CSsaStringArray &bufArray);
	void StreamAttributes(CSsaStringArray & attributBuf);
};

/////////////////////////////////////////
// Bay class
/////////////////////////////////////////
class qqhSLOTBay : public qqhSLOTHolder
{
protected:
	char acadHandle[20];
	int bayProfileId;
	CArray <qqhSLOTLevel, qqhSLOTLevel&> childList;

public:
	BOOL isEndBay;
	CBayProfile *pBayProfile;
	///////////////////////////////////////////
	//Gets and Sets
	///////////////////////////////////////////
	char * getAcadHandle()    { return acadHandle; }
	int getBayProfileId() { return bayProfileId; }
	CArray <qqhSLOTLevel, qqhSLOTLevel&> & getChildList() { return childList; }

	void setAcadHandle(const char * pAcadHandle)  { strcpy(acadHandle, pAcadHandle); }
	void setBayProfileId(int pBayProfileId) { bayProfileId = pBayProfileId; }
	void setChildList(const CArray <qqhSLOTLevel, qqhSLOTLevel&> & pChildList) { childList.Copy(pChildList); }


	///////////////////////////////////////////
	//Constructors, Destructors, and operators
	///////////////////////////////////////////
	qqhSLOTBay();
	virtual ~qqhSLOTBay();
	qqhSLOTBay(const qqhSLOTBay& other);
	qqhSLOTBay& operator=(const qqhSLOTBay& other);
	BOOL operator==(const qqhSLOTBay &other);
	///////////////////////////////////////////
	//Other Methods
	///////////////////////////////////////////
	void BuildFromStream(CSsaStringArray &bufArray);
	void StreamAttributes(CSsaStringArray & attributBuf);
};

/////////////////////////////////////////
// Side class
/////////////////////////////////////////
class qqhSLOTSide : public qqhSLOTHolder
{
protected:
	int IsRotated;
	CArray <qqhSLOTBay, qqhSLOTBay&> childList;

public:

	///////////////////////////////////////////
	//Gets and Sets
	///////////////////////////////////////////
	int getIsRotated() { return IsRotated; }
	CArray <qqhSLOTBay, qqhSLOTBay&> & getChildList() { return childList; }

	void setIsRotated(int pIsRotated) { IsRotated = pIsRotated; }
	void setChildList(const CArray <qqhSLOTBay, qqhSLOTBay&> & pChildList) { childList.Copy(pChildList); }

	qqhSLOTSide();
	virtual ~qqhSLOTSide() {childList.RemoveAll();}
	CArray <qqhSLOTBay, qqhSLOTBay&>& GetChildList();
	qqhSLOTSide(const qqhSLOTSide& other);
	qqhSLOTSide& operator=(const qqhSLOTSide& other);
	BOOL operator==(const qqhSLOTSide &other);
	void SetIsRotated(int rot);
	int GetIsRotated();
	void BuildFromStream(CSsaStringArray &bufArray);
	void StreamAttributes(CSsaStringArray & attributBuf);
};

/////////////////////////////////////////
// Aisle class
/////////////////////////////////////////
class qqhSLOTAisle : public qqhSLOTHolder
{
protected:
	CArray <qqhSLOTSide, qqhSLOTSide&> childList;
	qqhSLOTPickPath pickPath;

	CString		leftBayStart;
	int			leftBayStep;
	int			leftBayScheme;
	CString		rightBayStart;
	int			rightBayStep;
	int			rightBayScheme;
	CString		leftLevelStart;
	int			leftLevelStep;
	int			leftLevelScheme;
	int			leftLevelBreak;
	CString		rightLevelStart;
	int			rightLevelStep;
	int			rightLevelScheme;
	int			rightLevelBreak;
	CString		leftLocationStart;
	int			leftLocationStep;
	int			leftLocationScheme;
	int			leftLocationBreak;
	CString		rightLocationStart;
	int			rightLocationStep;
	int			rightLocationScheme;
	int			rightLocationBreak;
	int			pickPathDirection;
	int			pickPathType;
	int			pickPathStartSide;
	int			baysInPattern;
	float		rotation;
	CString		rightBayPattern;
	CString		rightLevelPattern;
	CString		rightLocPattern;
	CString		leftBayPattern;
	CString		leftLevelPattern;
	CString		leftLocPattern;
	double		aisleSpace;
public:
	///////////////////////////////////////////
	//Gets and Sets
	///////////////////////////////////////////
	CArray <qqhSLOTSide, qqhSLOTSide&> & getChildList() { return childList; }
	qqhSLOTPickPath & getPickPath() { return pickPath; }
	CString	&	getLeftBayStart() { return leftBayStart; }
	int			getLeftBayStep() { return leftBayStep; }
	int			getLeftBayScheme() { return leftBayScheme; }
	CString	&	getRightBayStart() { return rightBayStart; }
	int			getRightBayStep() { return rightBayStep; }
	int			getRightBayScheme() { return rightBayScheme; }
	CString	&	getLeftLevelStart() { return leftLevelStart; }
	int			getLeftLevelStep() { return leftLevelStep; }
	int			getLeftLevelScheme() { return leftLevelScheme; }
	int			getLeftLevelBreak() { return leftLevelBreak; }
	CString	&	getRightLevelStart() { return rightLevelStart; }
	int			getRightLevelStep() { return rightLevelStep; }
	int			getRightLevelScheme() { return rightLevelScheme; }
	int			getRightLevelBreak() { return rightLevelBreak; }
	CString	&	getLeftLocationStart() { return leftLocationStart; }
	int			getLeftLocationStep() { return leftLocationStep; }
	int			getLeftLocationScheme() { return leftLocationScheme; }
	int			getLeftLocationBreak() { return leftLocationBreak; }
	CString	&	getRightLocationStart() { return rightLocationStart; }
	int			getRightLocationStep() { return rightLocationStep; }
	int			getRightLocationScheme() { return rightLocationScheme; }
	int			getRightLocationBreak() { return rightLocationBreak; }
	int			getPickPathDirection() { return pickPathDirection; }
	int			getPickPathType() { return pickPathType; }
	int			getPickPathStartSide() { return pickPathStartSide; }
	int			getBaysInPattern() { return baysInPattern; }
	float		getRotation() { return rotation; }
	CString	&	getRightBayPattern() { return rightBayPattern; }
	CString	&	getRightLevelPattern() { return rightLevelPattern; }
	CString	&	getRightLocPattern() { return rightLocPattern; }
	CString	&	getLeftBayPattern() { return leftBayPattern; }
	CString	&	getLeftLevelPattern() { return leftLevelPattern; }
	CString	&	getLeftLocPattern() { return leftLocPattern; }
	double		getAisleSpace() { return aisleSpace; }

	void	setChildList(const CArray <qqhSLOTSide, qqhSLOTSide&> & pChildList) { childList.Copy(pChildList); }
	void	setPickPath(qqhSLOTPickPath & pPickPath) { pickPath = pPickPath; }
	void	setLeftBayStart(const CString & pLeftBayStart) { leftBayStart = pLeftBayStart; }
	void	setLeftBayStep(int pLeftBayStep) { leftBayStep = pLeftBayStep; }
	void	setLeftBayScheme(int pLeftBayScheme) { leftBayScheme = pLeftBayScheme; }
	void	setRightBayStart(const CString pRightBayStart) { rightBayStart = pRightBayStart; }
	void	setRightBayStep(int pRightBayStep) { rightBayStep = pRightBayStep; }
	void	setRightBayScheme(int pRightBayScheme) { rightBayScheme = pRightBayScheme; }
	void	setLeftLevelStart(const CString & pLeftLevelStart) { leftLevelStart = pLeftLevelStart; }
	void	setLeftLevelStep(int pLeftLevelStep) { leftLevelStep = pLeftLevelStep; }
	void	setLeftLevelScheme(int pLeftLevelScheme) { leftLevelScheme = pLeftLevelScheme; }
	void	setLeftLevelBreak(int pLeftLevelBreak) { leftLevelBreak = pLeftLevelBreak; }
	void	setRightLevelStart(const CString & pRightLevelStart) { rightLevelStart = pRightLevelStart; }
	void	setRightLevelStep(int pRightLevelStep) { rightLevelStep = pRightLevelStep; }
	void	setRightLevelScheme(int pRightLevelScheme) { rightLevelScheme = pRightLevelScheme; }
	void	setRightLevelBreak(int pRightLevelBreak) { rightLevelBreak = pRightLevelBreak; }
	void	setLeftLocationStart(const CString & pLeftLocationStart) { leftLocationStart = pLeftLocationStart; }
	void	setLeftLocationStep(int pLeftLocationStep) { leftLocationStep = pLeftLocationStep; }
	void	setLeftLocationScheme(int pLeftLocationScheme) { leftLocationScheme = pLeftLocationScheme; }
	void	setLeftLocationBreak(int pLeftLocationBreak) { leftLocationBreak = pLeftLocationBreak; }
	void	setRightLocationStart(const CString & pRightLocationStart) { rightLocationStart = pRightLocationStart; }
	void	setRightLocationStep(int pRightLocationStep) { rightLocationStep = pRightLocationStep; }
	void	setRightLocationScheme(int pRightLocationScheme) { rightLocationScheme = pRightLocationScheme; }
	void	setRightLocationBreak(int pRightLocationBreak) { rightLocationBreak = pRightLocationBreak; }
	void	setPickPathDirection(int pPickPathDirection) { pickPathDirection = pPickPathDirection; }
	void	setPickPathType(int pPickPathType) { pickPathType = pPickPathType; }
	void	setPickPathStartSide(int pPickPathStartSide) { pickPathStartSide = pPickPathStartSide; }
	void	setBaysInPattern(int pBaysInPattern) { baysInPattern = pBaysInPattern; }
	void	setRotation(float pRotation) { rotation = pRotation; }
	void	setRightBayPattern(const CString & pRightBayPattern) { rightBayPattern = pRightBayPattern;  if (rightBayPattern == "") rightBayPattern = " ";}
	void	setRightLevelPattern(const CString & pRightLevelPattern) { rightLevelPattern = pRightLevelPattern; if (rightLevelPattern == "") rightLevelPattern = " ";}
	void	setRightLocPattern(const CString & pRightLocPattern) { rightLocPattern = pRightLocPattern; if (rightLocPattern == "") rightLocPattern = " ";}
	void	setLeftBayPattern(const CString & pLeftBayPattern) { leftBayPattern = pLeftBayPattern; if (leftBayPattern == "") leftBayPattern = " ";}
	void	setLeftLevelPattern(const CString & pLeftLevelPattern) { leftLevelPattern = pLeftLevelPattern; if (leftLevelPattern == "") leftLevelPattern = " ";}
	void	setLeftLocPattern(const CString & pLeftLocPattern) { leftLocPattern = pLeftLocPattern; if (leftLocPattern == "") leftLocPattern = " ";}
	void	setAisleSpace(int pAisleSpace) { aisleSpace = pAisleSpace; }
	///////////////////////////////////////////
	//Constructors, Destructors, and operators
	///////////////////////////////////////////
	qqhSLOTAisle();
	virtual ~qqhSLOTAisle() {childList.RemoveAll();}
	CArray <qqhSLOTSide, qqhSLOTSide&>& GetChildList();
	qqhSLOTAisle(const qqhSLOTAisle& other);
	qqhSLOTAisle& operator=(const qqhSLOTAisle& other);
	BOOL operator==(const qqhSLOTAisle &other);
	///////////////////////////////////////////
	//Other Methods
	///////////////////////////////////////////
	void BuildFromStream(CSsaStringArray &bufArray);
	void StreamAttributes(CSsaStringArray & attributBuf);
};

/////////////////////////////////////////
// Section class
/////////////////////////////////////////
class qqhSLOTSection : public qqhSLOTHolder
{
protected:
	CArray <qqhSLOTAisle, qqhSLOTAisle&> childList;
	CArray <qqhSLOTHotSpot, qqhSLOTHotSpot&> hotSpotList;
	qqhSLOTPickPath pickPath;
	CString locationMask;
	float avgCubePerTrip;
	float forkDistFixed;
	float forkDistVar;
	float forkLaborRate;
	float replenAvgDist;
	float selectDistFixed;
	float selectDistVar;
	float selectLaborRate;
	float selDist;
	int avgOrdQty;
	int contQty;
	int orderCount;
	CString applyBrokenOrder;
	float palletsPerPtwyTrip;
	float insertForkTravel;
	float pickupForkTravel;
	float stockerDistFixed;
	float stockerDistVar;
	float stockerLaborRate;
	CString WMSSectionId;

public:

	///////////////////////////////////////////
	//Gets and Sets
	///////////////////////////////////////////
	CArray <qqhSLOTAisle, qqhSLOTAisle&> & getChildList() { return childList; }
	CArray <qqhSLOTHotSpot, qqhSLOTHotSpot&> & getHotSpotList() { return hotSpotList; }
	qqhSLOTPickPath & getPickPath() { return pickPath; }
	CString & getLocationMask() { return locationMask; }
	float getAvgCubePerTrip()   { return avgCubePerTrip; }
	float getForkDistFixed()    { return forkDistFixed; }
	float getForkDistVar()      { return forkDistVar; }
	float getForkLaborRate()    { return forkLaborRate; }
	float getReplenAvgDist()    { return replenAvgDist; }
	float getSelectDistFixed()  { return selectDistFixed; }
	float getSelectDistVar()    { return selectDistVar; }
	float getSelectLaborRate()  { return selectLaborRate; }
	float getSelDist()          { return selDist; }
	int   getAvgOrdQty()        { return avgOrdQty; }
	int   getContQty()          { return contQty; }
	int   getOrderCount()       { return orderCount; }
	CString & getApplyBrokenOrder() { return applyBrokenOrder; }
	float getStockerDistFixed()  { return stockerDistFixed; }
	float getStockerDistVar()    { return stockerDistVar; }
	float getStockerLaborRate()  { return stockerLaborRate; }
	float getPalletsPerPtwyTrip()  { return palletsPerPtwyTrip; }
	float getInsertForkTravel()  { return insertForkTravel; }
	float getPickupForkTravel()  { return pickupForkTravel; }
	CString & getWMSSectionId() { return WMSSectionId; }

	void setChildList(const CArray <qqhSLOTAisle, qqhSLOTAisle&> & pChildList) { childList.Copy(pChildList); }
	void setHotSpotList(const CArray <qqhSLOTHotSpot, qqhSLOTHotSpot&> & pHotSpotList) { hotSpotList.Copy(pHotSpotList); }
	void setPickPath(qqhSLOTPickPath & pPickPath) { pickPath = pPickPath; }
	void setLocationMask(const CString & pLocationMask) { locationMask = pLocationMask; }
	void setAvgCubePerTrip(float pAvgCubePerTrip) {  avgCubePerTrip = pAvgCubePerTrip; }
	void setForkDistFixed(float pForkDistFixed) { forkDistFixed = pForkDistFixed; }
	void setForkDistVar(float pForkDistVar) { forkDistVar = pForkDistVar; }
	void setForkLaborRate(float pForkLaborRate) { forkLaborRate = pForkLaborRate; }
	void setReplenAvgDist(float pReplenAvgDist) { replenAvgDist = pReplenAvgDist; }
	void setSelectDistFixed(float pSelectDistFixed) { selectDistFixed = pSelectDistFixed; }
	void setSelectDistVar(float pSelectDistVar) { selectDistVar = pSelectDistVar; }
	void setSelectLaborRate(float pSelectLaborRate) { selectLaborRate = pSelectLaborRate; }
	void setSelDist(float pSelDist) { selDist = pSelDist; }
	void setAvgOrdQty(int pAvgOrdQty) { avgOrdQty = pAvgOrdQty; }
	void setContQty(int pContQty) { contQty = pContQty; }
	void setOrderCount(int pOrderCount) { orderCount = pOrderCount; }
	void setApplyBrokenOrder(const CString & pApplyBrokenOrder) { applyBrokenOrder = pApplyBrokenOrder; }
	void setStockerDistFixed(float pStockerDistFixed) { stockerDistFixed = pStockerDistFixed; }
	void setStockerDistVar(float pStockerDistVar) { stockerDistVar = pStockerDistVar; }
	void setStockerLaborRate(float pStockerLaborRate) { stockerLaborRate = pStockerLaborRate; }
	void setPalletsPerPtwyTrip(float pPalletsPerPtwyTrip) { palletsPerPtwyTrip = pPalletsPerPtwyTrip; }
	void setInsertForkTravel(float pInsertForkTravel) { insertForkTravel = pInsertForkTravel; }
	void setPickupForkTravel(float pPickupForkTravel) { pickupForkTravel = pPickupForkTravel; }
	void setWMSSectionId(const CString &pWMSSectionId) { WMSSectionId = pWMSSectionId; }
	///////////////////////////////////////////
	//Constructors, Destructors, and operators
	///////////////////////////////////////////
	qqhSLOTSection();
	virtual ~qqhSLOTSection() {childList.RemoveAll();}
	CArray <qqhSLOTAisle, qqhSLOTAisle&>& GetChildList();
	qqhSLOTSection(const qqhSLOTSection& other);
	qqhSLOTSection& operator=(const qqhSLOTSection& other);
	BOOL operator==(const qqhSLOTSection &other);
	///////////////////////////////////////////
	//Other Methods
	///////////////////////////////////////////
	void BuildFromStream(CSsaStringArray &bufArray);
	void StreamAttributes(CSsaStringArray & attributBuf);
};

/////////////////////////////////////////
// Facility class
/////////////////////////////////////////
class qqhSLOTFacility: public qqhSLOTHolder
{
protected:
	int region;
	int units;
	int slotType;
	CString strCadFileName;
	int IsModified;
	int duration;
	double cost;
	double baselineCost;
	int originalFacilityId;
	int timeHorizonUnit;
	CString clientNameOpened;
	CString notes;
	CArray <qqhSLOTSection, qqhSLOTSection&> childList;
	CArray <qqhSLOTPickPath, qqhSLOTPickPath&> pickPathList;

public:
	void ConvertToFacility(CFacility &facility);

	///////////////////////////////////////////
	//Gets and Sets
	///////////////////////////////////////////
	int   getRegion()               { return region; }
	int   getUnits()                { return units; }
	int   getSlotType()             { return slotType; }
	CString & getStrCadFileName()   { return strCadFileName; }
	int   getIsModified()           { return IsModified; }
	int   getDuration()             { return duration; }
	double getCost()                 { return cost; }
	double getBaselineCost()		{ return baselineCost; }
	int   getOriginalFacilityId()   { return originalFacilityId; }
	CString & getClientNameOpened() { return clientNameOpened; }
	CArray <qqhSLOTSection, qqhSLOTSection&> & getChildList() { return childList; }
	CArray <qqhSLOTPickPath, qqhSLOTPickPath&> & getPickPathList() { return pickPathList; }
	int	  getTimeHorizonUnit()		{ return timeHorizonUnit; }
	CString & getNotes()   { return notes; }

	void setTimeHorizonUnit(int pTimeHorizonUnit) { timeHorizonUnit = pTimeHorizonUnit; }
	void setRegion(int pRegion) { region = pRegion; }
	void setUnits(int pUnits) { units = pUnits; }
	void setSlotType(int pSlotType) { slotType = pSlotType; }
	void setStrCadFileName(const CString & pStrCadFileName) { strCadFileName = pStrCadFileName; }
	void setIsModified(int pIsModified) { IsModified = pIsModified; }
	void setDuration(int pDuration) { duration = pDuration; }
	void setCost(double pCost) { cost = pCost; }
	void setBaselineCost(double pCost) { baselineCost = pCost; }
	void setOriginalFacilityId(int pOriginalFacilityId) { originalFacilityId = pOriginalFacilityId; }
	void setClientNameOpened(const CString & pClientNameOpened) { clientNameOpened = pClientNameOpened; }
	void setChildList(const CArray <qqhSLOTSection, qqhSLOTSection&> & pChildList) {childList.Copy(pChildList); }
	void setPickPathList(const CArray <qqhSLOTPickPath, qqhSLOTPickPath&> & pPickPathList) { pickPathList.Copy(pPickPathList); }
	void setNotes(const CString & pNotes) { notes = pNotes; }

	///////////////////////////////////////////
	//Constructors, Destructors, and operators
	///////////////////////////////////////////
	qqhSLOTFacility();
	virtual ~qqhSLOTFacility() {childList.RemoveAll();}
	qqhSLOTFacility(const qqhSLOTFacility& other);
	qqhSLOTFacility(const char *strName);
	qqhSLOTFacility& operator=(const qqhSLOTFacility& other);
	BOOL operator==(const qqhSLOTFacility& other);

	///////////////////////////////////////////
	//Other Methods
	///////////////////////////////////////////
	void BuildFromStream(CSsaStringArray &bufArray);
	void StreamAttributes(CSsaStringArray & attributBuf);
};

#endif

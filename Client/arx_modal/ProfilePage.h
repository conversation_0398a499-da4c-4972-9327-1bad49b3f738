#if !defined(AFX_PROFILEPAGE_H__BC3C59A0_99E1_4376_985B_C7717ABD5CC7__INCLUDED_)
#define AFX_PROFILEPAGE_H__BC3C59A0_99E1_4376_985B_C7717ABD5CC7__INCLUDED_

#if _MSC_VER > 1000
#pragma once
#endif // _MSC_VER > 1000
// ProfilePage.h : header file
//

/////////////////////////////////////////////////////////////////////////////
// CProfilePage dialog

class CProfilePage : public CPropertyPage
{
	DECLARE_DYNCREATE(CProfilePage)

// Construction
public:
	CProfilePage();
	~CProfilePage();

// Dialog Data
	//{{AFX_DATA(CProfilePage)
	enum { IDD = IDD_PROFILE_LIST_PANE };
		// NOTE - ClassWizard will add data members here.
		//    DO NOT EDIT what you see in these blocks of generated code !
	//}}AFX_DATA


// Overrides
	// ClassWizard generate virtual function overrides
	//{{AFX_VIRTUAL(CProfilePage)
	protected:
	virtual void DoDataExchange(CDataExchange* pDX);    // DDX/DDV support
	//}}AFX_VIRTUAL

// Implementation
protected:
	// Generated message map functions
	//{{AFX_MSG(CProfilePage)
		// NOTE: the ClassWizard will add member functions here
	//}}AFX_MSG
	DECLARE_MESSAGE_MAP()

};

//{{AFX_INSERT_LOCATION}}
// Microsoft Visual C++ will insert additional declarations immediately before the previous line.

#endif // !defined(AFX_PROFILEPAGE_H__BC3C59A0_99E1_4376_985B_C7717ABD5CC7__INCLUDED_)

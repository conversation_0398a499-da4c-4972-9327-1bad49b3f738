#if !defined(AFX_COLORMODELDLG_H__1B1FFF71_1107_11D2_A477_00C04FA8700B__INCLUDED_)
#define AFX_COLORMODELDLG_H__1B1FFF71_1107_11D2_A477_00C04FA8700B__INCLUDED_
#include "resource.h"
#if _MSC_VER >= 1000
#pragma once
#endif // _MSC_VER >= 1000
// ColorModelDlg.h : header file
//
#include "ProductAttribute.h"
#include "ColorListBox.h"	// Added by ClassView
#include "ColorButton.h"
#include "ProductDataService.h"	// Added by ClassView

/////////////////////////////////////////////////////////////////////////////
// CColorModelDlg dialog

class CColorModelDlg : public CDialog
{
// Construction
public:
	CProductDataService m_ProductDataService;
	CColorModelDlg(CWnd* pParent = NULL);   // standard constructor
	~CColorModelDlg();

	CProductAttribute *m_pProductAttribute;
	int m_Direction;
	int m_Mode;
	int m_Origin;
	int m_MultiType;		// what to do when there are multiple products in a bay
							// Sum (add them all together), Min (use the minimum), Max (use the max)
							// Avg (average them)
	int m_RelativeLevel;
	double m_MinHeight;
	double m_MaxHeight;

	CColorObject m_CurrentColor;

// Dialog Data
	//{{AFX_DATA(CColorModelDlg)
	enum { IDD = IDD_COLORMODEL };
	CMyColorButton	m_ColorButton;
	CStatic	m_ColorPicture;
	CComboBox	m_DirectionCtrl;
	CComboBox	m_ColorCtrl;
	CComboBox	m_AttributeCtrl;
	CString	m_StartValue;
	CString	m_EndValue;
	BOOL	m_Reset;
	//}}AFX_DATA



// Overrides
	// ClassWizard generated virtual function overrides
	//{{AFX_VIRTUAL(CColorModelDlg)
	protected:
	virtual void DoDataExchange(CDataExchange* pDX);    // DDX/DDV support
	//}}AFX_VIRTUAL



// Implementation
protected:

	// Generated message map functions
	//{{AFX_MSG(CColorModelDlg)
	virtual BOOL OnInitDialog();
	virtual void OnOK();
	afx_msg void OnHelp();
	afx_msg void OnPercentage();
	afx_msg void OnSpecific();
	afx_msg void OnTruescale();
	afx_msg void OnReset();
	afx_msg BOOL OnHelpInfo(HELPINFO* pHelpInfo);
	afx_msg HBRUSH OnCtlColor(CDC* pDC, CWnd* pWnd, UINT nCtlColor);
	afx_msg void OnChooseColor();
	afx_msg void OnDestroy();
	afx_msg void OnAdvanced();
	//}}AFX_MSG

	DECLARE_MESSAGE_MAP()

	CBrush* m_pColorBrush;

private:
	void SaveValues();
	void LoadPreviousValues();
};

//{{AFX_INSERT_LOCATION}}
// Microsoft Developer Studio will insert additional declarations immediately before the previous line.

#endif // !defined(AFX_COLORMODELDLG_H__1B1FFF71_1107_11D2_A477_00C04FA8700B__INCLUDED_)

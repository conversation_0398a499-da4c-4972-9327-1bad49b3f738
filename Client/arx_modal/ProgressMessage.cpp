// ProgressMessage.cpp: implementation of the CProgressMessage class.
//
//////////////////////////////////////////////////////////////////////

#include "stdafx.h"
#include "modal.h"
#include "ProgressMessage.h"

#ifdef _DEBUG
#undef THIS_FILE
static char THIS_FILE[]=__FILE__;
#define new DEBUG_NEW
#endif

//////////////////////////////////////////////////////////////////////
// Construction/Destruction
//////////////////////////////////////////////////////////////////////

CProgressMessage::CProgressMessage()
{

}

CProgressMessage::CProgressMessage(const CString &message, int lower, int upper, int step, CWnd *pParent)
{

	m_ProgressDialog = new CProgress;
	m_ProgressDialog->Create(IDD_PROGRESS, pParent);
	m_ProgressDialog->m_StatusTextCtrl.SetWindowText(message);
	
	
	m_ProgressDialog->m_NoStop = FALSE;
	m_ProgressDialog->m_ProgressCtrl.SetRange32(lower, upper);
	m_ProgressDialog->m_ProgressCtrl.SetStep(step);

	m_ProgressDialog->UpdateData(FALSE);
	m_ProgressDialog->CenterWindow();
	m_ProgressDialog->ShowWindow(SW_SHOW);
	m_ProgressDialog->UpdateWindow();


}

CProgressMessage::CProgressMessage(const CString &message, int lower, int upper, int step, CWnd *pParent,
								   BOOL noStop)
{

	m_ProgressDialog = new CProgress;
	m_ProgressDialog->m_NoStop = noStop;

	m_ProgressDialog->Create(IDD_PROGRESS, pParent);
	m_ProgressDialog->m_StatusTextCtrl.SetWindowText(message);
	
	m_ProgressDialog->m_ProgressCtrl.SetRange32(lower, upper);
	m_ProgressDialog->m_ProgressCtrl.SetStep(step);

	m_ProgressDialog->UpdateData(FALSE);
	m_ProgressDialog->CenterWindow();
	m_ProgressDialog->ShowWindow(SW_SHOW);
	m_ProgressDialog->UpdateWindow();


}


CProgressMessage::~CProgressMessage()
{
	m_ProgressDialog->DestroyWindow();
}

void CProgressMessage::Step()
{
	m_ProgressDialog->m_ProgressCtrl.StepIt();
}

void CProgressMessage::UpdateMessage(const CString &message)
{
	if (m_ProgressDialog == NULL)
		return;

	m_ProgressDialog->m_StatusTextCtrl.SetWindowText(message);
	m_ProgressDialog->UpdateData(FALSE);

}

void CProgressMessage::Hide()
{
	m_ProgressDialog->ShowWindow(SW_HIDE);
}

BOOL CProgressMessage::IsStopping()
{
	return m_ProgressDialog->m_Stopping;
}

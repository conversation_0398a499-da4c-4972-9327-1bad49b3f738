// ProductGroupPropertiesPage.cpp : implementation file
//

#include "stdafx.h"
#include "ssa_exception.h"
#include "HelpService.h"

#include "ProductGroupPropertiesPage.h"
#include "ProductAttribute.h"
#include "UtilityHelper.h"

#include <dbsymtb.h>

#ifdef _DEBUG
#define new DEBUG_NEW
#undef THIS_FILE
static char THIS_FILE[] = __FILE__;
#endif

extern CHelpService helpService;
extern CUtilityHelper utilityHelper;
/////////////////////////////////////////////////////////////////////////////
// CProductGroupPropertiesPage property page

IMPLEMENT_DYNCREATE(CProductGroupPropertiesPage, CPropertyPage)

CProductGroupPropertiesPage::CProductGroupPropertiesPage() : CPropertyPage(CProductGroupPropertiesPage::IDD)
{
	//{{AFX_DATA_INIT(CProductGroupPropertiesPage)
	m_LockLocations = FALSE;
	m_LockProductGroup = FALSE;
	m_Name = _T("");
	m_PercentOpenLocs = _T("0");
	//}}AFX_DATA_INIT
}

CProductGroupPropertiesPage::~CProductGroupPropertiesPage()
{
}

void CProductGroupPropertiesPage::DoDataExchange(CDataExchange* pDX)
{
	CPropertyPage::DoDataExchange(pDX);
	//{{AFX_DATA_MAP(CProductGroupPropertiesPage)
	DDX_Check(pDX, IDC_LOCK_LOCATIONS, m_LockLocations);
	DDX_Check(pDX, IDC_LOCK_PRODUCT_GROUP, m_LockProductGroup);
	DDX_Text(pDX, IDC_NAME, m_Name);
	DDX_Text(pDX, IDC_PERCENT_OPEN_LOCS, m_PercentOpenLocs);
	//}}AFX_DATA_MAP
}


BEGIN_MESSAGE_MAP(CProductGroupPropertiesPage, CPropertyPage)
	//{{AFX_MSG_MAP(CProductGroupPropertiesPage)
	ON_BN_CLICKED(IDC_LOCK_LOCATIONS, OnLockLocations)
	ON_BN_CLICKED(IDC_LOCK_PRODUCT_GROUP, OnLockProductGroup)
	ON_WM_HELPINFO()
	//}}AFX_MSG_MAP
END_MESSAGE_MAP()

/////////////////////////////////////////////////////////////////////////////
// CProductGroupPropertiesPage message handlers

BOOL CProductGroupPropertiesPage::OnInitDialog() 
{
	CPropertyPage::OnInitDialog();
	
	CDialog::OnInitDialog();
	CStringArray attributes;
	CProductAttribute *attribute;
	CRect r;

	CComboBox *pAttributeBox = (CComboBox *)GetDlgItem(IDC_OPTIMIZE_ATTRIBUTE);
	CComboBox *pMethodBox = (CComboBox *)GetDlgItem(IDC_OPTIMIZE_METHOD);


	pAttributeBox->SetItemHeight(0, 2000);
	pAttributeBox->GetWindowRect(&r);
	pAttributeBox->SetWindowPos(NULL, 0, 0, r.Width(), r.Height()*10, SWP_NOMOVE|SWP_NOZORDER);

	for (int i=0; i < m_ProductAttributeList->GetSize(); ++i) {
		attribute = (*m_ProductAttributeList)[i];
		pAttributeBox->AddString(attribute->m_Name);
	}

	pMethodBox->SetItemHeight(0, 2000);
	pMethodBox->GetWindowRect(&r);
	pMethodBox->SetWindowPos(NULL, 0, 0, r.Width(), r.Height()*4, SWP_NOMOVE|SWP_NOZORDER);

	pMethodBox->AddString("Ascending");
	pMethodBox->AddString("Descending");
	pMethodBox->AddString("Uniform");

	if (m_ProductGroup->m_ProductGroupDBID > 0) {
		m_Name = m_ProductGroup->m_Description;
		m_PercentOpenLocs.Format("%5.2f", m_ProductGroup->m_PercentOpenLocs);
		pAttributeBox->SetCurSel(pAttributeBox->FindString(0, m_ProductGroup->m_OptimizeAttribute));
		pMethodBox->SetCurSel(m_ProductGroup->m_OptimizeMethod-1);
		
		m_LockLocations = (m_ProductGroup->m_IsAssignmentLocked == 1);
		m_LockProductGroup = (m_ProductGroup->m_IsProdGroupLocked == 1);
	}

	UpdateData(FALSE);

		
	return TRUE;  // return TRUE unless you set the focus to a control
	              // EXCEPTION: OCX Property Pages should return FALSE
}

void CProductGroupPropertiesPage::OnOK() 
{
	CComboBox *pAttributeBox = (CComboBox *)GetDlgItem(IDC_OPTIMIZE_ATTRIBUTE);
	CComboBox *pMethodBox = (CComboBox *)GetDlgItem(IDC_OPTIMIZE_METHOD);

	m_ProductGroup->m_Description = m_Name;

	if (m_LockLocations)
		m_ProductGroup->m_IsAssignmentLocked = 1;
	else
		m_ProductGroup->m_IsAssignmentLocked = 0;

	if (m_LockProductGroup)
		m_ProductGroup->m_IsProdGroupLocked = 1;
	else
		m_ProductGroup->m_IsProdGroupLocked = 0;

	m_ProductGroup->m_PercentOpenLocs = (float)atof(m_PercentOpenLocs);
	pAttributeBox->GetWindowText(m_ProductGroup->m_OptimizeAttribute);
	m_ProductGroup->m_OptimizeMethod = pMethodBox->GetCurSel()+1;

	CPropertyPage::OnOK();
}

BOOL CProductGroupPropertiesPage::OnKillActive() 
{
	CComboBox *pAttributeBox = (CComboBox *)GetDlgItem(IDC_OPTIMIZE_ATTRIBUTE);
	CComboBox *pMethodBox = (CComboBox *)GetDlgItem(IDC_OPTIMIZE_METHOD);

	UpdateData(TRUE);
	
	if (m_Name == "") {
		AfxMessageBox("Please enter a name for the product group.");
		GetDlgItem(IDC_NAME)->SetFocus();
		return FALSE;
	}

	if (m_PercentOpenLocs == "") {
		AfxMessageBox("Please enter an open location percentage between 0 and 100.");
		GetDlgItem(IDC_PERCENT_OPEN_LOCS)->SetFocus();
		return FALSE;
	}

	if (! utilityHelper.IsFloat(m_PercentOpenLocs)) {
		AfxMessageBox("Please enter an number between 0 and 100 for open location percentage.");
		CEdit *pEdit = (CEdit *)GetDlgItem(IDC_PERCENT_OPEN_LOCS);
		pEdit->SetSel(0, -1);
		pEdit->SetFocus();
		return FALSE;
	}

	if (pAttributeBox->GetCurSel() < 0) {
		AfxMessageBox("Please select an optimize attribute.");
		pAttributeBox->SetFocus();
		pAttributeBox->ShowDropDown(TRUE);
		return FALSE;
	}

	if (pMethodBox->GetCurSel() < 0) {
		AfxMessageBox("Please select an optimize method.");
		pMethodBox->SetFocus();
		pMethodBox->ShowDropDown(TRUE);
		return FALSE;
	}

	return CPropertyPage::OnKillActive();
}

void CProductGroupPropertiesPage::OnLockLocations() 
{
	UpdateData(TRUE);

	if (m_LockLocations)
		m_LockProductGroup = TRUE;

	UpdateData(FALSE);

}

void CProductGroupPropertiesPage::OnLockProductGroup() 
{
	UpdateData(TRUE);
	
	if (! m_LockProductGroup)
		m_LockLocations = FALSE;

	UpdateData(FALSE);

}

BOOL CProductGroupPropertiesPage::OnNotify(WPARAM wParam, LPARAM lParam, LRESULT* pResult) 
{
	NMHDR* pNMHDR = (NMHDR*)lParam;
	if (pNMHDR->code == PSN_HELP) {
		helpService.ShowScreenHelp(IDD);
		return TRUE;
	}		
	return CPropertyPage::OnNotify(wParam, lParam, pResult);
}

BOOL CProductGroupPropertiesPage::OnHelpInfo(HELPINFO* pHelpInfo) 
{
	helpService.ShowFieldHelp(IDD, pHelpInfo->iCtrlId);
	return FALSE;
}

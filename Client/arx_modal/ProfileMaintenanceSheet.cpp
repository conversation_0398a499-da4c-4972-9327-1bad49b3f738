// ProfileMaintenanceSheet.cpp : implementation file
//

#include "stdafx.h"
#include "modal.h"
#include "ProfileMaintenanceSheet.h"

#ifdef _DEBUG
#define new DEBUG_NEW
#undef THIS_FILE
static char THIS_FILE[] = __FILE__;
#endif

/////////////////////////////////////////////////////////////////////////////
// CProfileMaintenanceSheet

IMPLEMENT_DYNAMIC(CProfileMaintenanceSheet, CPropertySheet)

CProfileMaintenanceSheet::CProfileMaintenanceSheet(UINT nIDCaption, CWnd* pParentWnd, UINT iSelectPage)
	:CPropertySheet(nIDCaption, pParentWnd, iSelectPage)
{
}

CProfileMaintenanceSheet::CProfileMaintenanceSheet(LPCTSTR pszCaption, CWnd* pParentWnd, UINT iSelectPage)
	:CPropertySheet(pszCaption, pParentWnd, iSelectPage)
{
	this->AddPage(&m_Page);
}

CProfileMaintenanceSheet::~CProfileMaintenanceSheet()
{
}


BEGIN_MESSAGE_MAP(CProfileMaintenanceSheet, CPropertySheet)
	//{{AFX_MSG_MAP(CProfileMaintenanceSheet)
		// NOTE - the ClassWizard will add and remove mapping macros here.
	//}}AFX_MSG_MAP
END_MESSAGE_MAP()

/////////////////////////////////////////////////////////////////////////////
// CProfileMaintenanceSheet message handlers

void CProfileMaintenanceSheet::PostNcDestroy() 
{	
	CPropertySheet::PostNcDestroy();
	delete this;
}


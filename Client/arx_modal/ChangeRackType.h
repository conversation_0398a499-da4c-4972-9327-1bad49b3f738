#if !defined(AFX_CHANGERACKTYPE_H__1ABB7A92_2237_11D2_9C06_0080C742D9DF__INCLUDED_)
#define AFX_CHANGERACKTYPE_H__1ABB7A92_2237_11D2_9C06_0080C742D9DF__INCLUDED_

#if _MSC_VER >= 1000
#pragma once
#endif // _MSC_VER >= 1000
// ChangeRackType.h : header file
//
#include "resource.h"
#include "BayProfile.h"

/////////////////////////////////////////////////////////////////////////////
// CChangeRackType dialog

class CChangeRackType : public CDialog
{
// Construction
public:
	int m_SelectedBayProfileId;
	CString m_Title;
	BOOL m_bCurrentFacilityOnly;
	CChangeRackType(CWnd* pParent = NULL);   // standard constructor
	~CChangeRackType();

// Dialog Data
	//{{AFX_DATA(CChangeRackType)
	enum { IDD = IDD_CHANGERACKTYPE };
	CTreeCtrl	m_ProfileTreeCtrl;
	//}}AFX_DATA

// Overrides
	// ClassWizard generated virtual function overrides
	//{{AFX_VIRTUAL(CChangeRackType)
	protected:
	virtual void DoDataExchange(CDataExchange* pDX);    // DDX/DDV support
	//}}AFX_VIRTUAL

// Implementation
protected:

	// Generated message map functions
	//{{AFX_MSG(CChangeRackType)
	virtual void OnOK();
	virtual BOOL OnInitDialog();
	afx_msg void OnHelp();
	afx_msg BOOL OnHelpInfo(HELPINFO* pHelpInfo);
	//}}AFX_MSG
	DECLARE_MESSAGE_MAP()

	int LoadBayTypeList();
	void BuildBayProfileTree();
	CMap<int, int, HTREEITEM, HTREEITEM&> m_MapBayTypeToTree;
	CTypedPtrArray<CObArray, CBayProfile*> m_BayProfileList;
	CImageList m_ImageList;

};

//{{AFX_INSERT_LOCATION}}
// Microsoft Developer Studio will insert additional declarations immediately before the previous line.

#endif // !defined(AFX_CHANGERACKTYPE_H__1ABB7A92_2237_11D2_9C06_0080C742D9DF__INCLUDED_)

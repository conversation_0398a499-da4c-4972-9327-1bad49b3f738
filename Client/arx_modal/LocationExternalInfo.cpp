// LocationExternalInfo.cpp: implementation of the CLocationExternalInfo class.
//
//////////////////////////////////////////////////////////////////////

#include "stdafx.h"
#include "modal.h"
#include "LocationExternalInfo.h"
#include "UtilityHelper.h"

#ifdef _DEBUG
#undef THIS_FILE
static char THIS_FILE[]=__FILE__;
#define new DEBUG_NEW
#endif

extern CUtilityHelper utilityHelper;

//////////////////////////////////////////////////////////////////////
// Construction/Destruction
//////////////////////////////////////////////////////////////////////

CLocationExternalInfo::CLocationExternalInfo()
{
	m_LocationInfoDBId = 0;
	m_ExternalInfoDBId = 0;
	m_LocationDBId = 0;
	m_DefaultValue = "";
	m_Value = "";
}

CLocationExternalInfo::~CLocationExternalInfo()
{

}


CLocationExternalInfo::CLocationExternalInfo(const CLocationExternalInfo& other)
{
	m_LocationInfoDBId = other.m_LocationInfoDBId;
	m_ExternalInfoDBId = other.m_ExternalInfoDBId;
	m_LocationDBId = other.m_LocationDBId;
	m_Value = other.m_Value;
	m_DefaultValue = other.m_DefaultValue;

}

CLocationExternalInfo& CLocationExternalInfo::operator=(const CLocationExternalInfo& other)
{
	m_LocationDBId = other.m_LocationDBId;
	m_ExternalInfoDBId = other.m_ExternalInfoDBId;
	m_LocationDBId = other.m_LocationDBId;
	m_Value = other.m_Value;
	m_DefaultValue = other.m_DefaultValue;

	return *this;
}


int CLocationExternalInfo::Parse(const CString &line)
{
	CStringArray strings;

	utilityHelper.ParseString(line, "|", strings);

	for (int i=0; i < strings.GetSize(); ++i) {
		switch (i) {
		case 0:
			m_LocationInfoDBId = atoi(strings[i]);
			break;
		case 1:
			m_Value = strings[i];
			break;
		case 2:
			m_LocationDBId = atoi(strings[i]);
			break;
		case 3:
			m_ExternalInfoDBId= atoi(strings[i]);
			break;
		case 4:
			m_DefaultValue = strings[i];
			break;
		}
	}

	return 0;
}

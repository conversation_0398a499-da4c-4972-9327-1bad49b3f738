// SideProfileAttributesPage.cpp : implementation file
//

#include "stdafx.h"
#include "modal.h"
#include "SideProfileAttributesPage.h"
#include "SideProfileSheet.h"
#include "SideProfileDataService.h"
#include "Constants.h"
#include "HelpService.h"
#include "UtilityHelper.h"

#ifdef _DEBUG
#define new DEBUG_NEW
#undef THIS_FILE
static char THIS_FILE[] = __FILE__;
#endif

extern CUtilityHelper utilityHelper;
extern CHelpService helpService;
/////////////////////////////////////////////////////////////////////////////
// CSideProfileAttributesPage property page

IMPLEMENT_DYNCREATE(CSideProfileAttributesPage, CPropertyPage)

CSideProfileAttributesPage::CSideProfileAttributesPage() : CPropertyPage(CSideProfileAttributesPage::IDD)
{
	//{{AFX_DATA_INIT(CSideProfileAttributesPage)
	m_Length = _T("");
	m_Name = _T("");
	//}}AFX_DATA_INIT
}

CSideProfileAttributesPage::~CSideProfileAttributesPage()
{
}

void CSideProfileAttributesPage::DoDataExchange(CDataExchange* pDX)
{
	CPropertyPage::DoDataExchange(pDX);
	//{{AFX_DATA_MAP(CSideProfileAttributesPage)
	DDX_Text(pDX, IDC_LENGTH, m_Length);
	DDX_Text(pDX, IDC_NAME, m_Name);
	//}}AFX_DATA_MAP
}


BEGIN_MESSAGE_MAP(CSideProfileAttributesPage, CPropertyPage)
	//{{AFX_MSG_MAP(CSideProfileAttributesPage)
	ON_BN_CLICKED(IDC_FIXED_RADIO, OnFixedRadio)
	ON_BN_CLICKED(IDC_VARIABLE_RADIO, OnVariableRadio)
	ON_WM_HELPINFO()
	//}}AFX_MSG_MAP
END_MESSAGE_MAP()

/////////////////////////////////////////////////////////////////////////////
// CSideProfileAttributesPage message handlers

BOOL CSideProfileAttributesPage::OnSetActive() 
{
	CSideProfileSheet *pSheet = (CSideProfileSheet *)GetParent();
	m_pSideProfile = pSheet->m_pSideProfile;

	m_Name = m_pSideProfile->m_Description;
	m_Length.Format("%.0f", m_pSideProfile->m_TotalLength);

	if (m_IsFixed) {
		CButton *pButton = (CButton *)GetDlgItem(IDC_FIXED_RADIO);
		pButton->SetCheck(TRUE);
		CEdit *pEdit = (CEdit *)GetDlgItem(IDC_LENGTH);
		pEdit->EnableWindow(TRUE);
	}
	else {
		CButton *pButton = (CButton *)GetDlgItem(IDC_VARIABLE_RADIO);
		pButton->SetCheck(TRUE);
		CEdit *pEdit = (CEdit *)GetDlgItem(IDC_LENGTH);
		pEdit->EnableWindow(FALSE);
	}

	UpdateData(FALSE);

	return CPropertyPage::OnSetActive();
}

BOOL CSideProfileAttributesPage::OnKillActive() 
{
	UpdateData(TRUE);

	if ( m_Name.FindOneOf(BAD_FILE_CHARACTERS) != -1 ) {
		CString temp;
		temp.Format("The following characters are not allowed in the side profile name: \n%s",
			BAD_FILE_CHARACTERS);
		AfxMessageBox(temp);
		utilityHelper.SetEditControlErrorState(this, IDC_NAME);
		return FALSE;
	}


	CSideProfileDataService service;

	try {
		if (service.IsSideProfileNameInUse(m_pSideProfile->m_SideProfileDBId, m_Name)) {
			AfxMessageBox("The side profile name is already in use. Please enter a unique name "
				"for the side profile.");
			CEdit *pEdit = (CEdit *)GetDlgItem(IDC_NAME);
			pEdit->SetSel(0,-1);
			pEdit->SetFocus();
			return FALSE;
		}
	}
	catch (...) {
		utilityHelper.ProcessError("Error determining if profile name is in use.");
		return FALSE;
	}


	if (! utilityHelper.IsFloat(m_Length)) {
		AfxMessageBox("Please enter a valid decimal number for length.");
		return utilityHelper.SetEditControlErrorState(this, IDC_LENGTH);
	}

	if (m_IsFixed && m_pSideProfile->CalculateLength() > atof(m_Length)) {
		CString temp;
		temp.Format("The calculated length of the aisle (%.0f) is greater than the\n"
			"specified fixed length (%.0f).  Please increase the fixed length.",
			m_pSideProfile->CalculateLength(), m_pSideProfile->m_TotalLength);
		AfxMessageBox(temp);
		utilityHelper.SetEditControlErrorState(this, IDC_LENGTH);
		return FALSE;
	}

	m_pSideProfile->m_Description = m_Name;
	if (m_IsFixed)
		m_pSideProfile->m_TotalLength = atof(m_Length);
	else
		m_pSideProfile->m_TotalLength = m_pSideProfile->CalculateLength();
	m_pSideProfile->m_FixedLength = m_IsFixed;

	return CPropertyPage::OnKillActive();
}

void CSideProfileAttributesPage::OnFixedRadio() 
{
	CEdit *pEdit = (CEdit *)GetDlgItem(IDC_LENGTH);
	pEdit->EnableWindow(TRUE);
	m_IsFixed = TRUE;
}

void CSideProfileAttributesPage::OnVariableRadio() 
{
	CEdit *pEdit = (CEdit *)GetDlgItem(IDC_LENGTH);
	pEdit->EnableWindow(FALSE);
	m_IsFixed = FALSE;
}


BOOL CSideProfileAttributesPage::OnInitDialog() 
{
	CPropertyPage::OnInitDialog();
	
	m_IsFixed = FALSE;
	
	return TRUE;  // return TRUE unless you set the focus to a control
	              // EXCEPTION: OCX Property Pages should return FALSE
}

BOOL CSideProfileAttributesPage::OnHelpInfo(HELPINFO* pHelpInfo) 
{
	helpService.ShowFieldHelp(IDD, pHelpInfo->iCtrlId);
	return FALSE;

}

BOOL CSideProfileAttributesPage::OnNotify(WPARAM wParam, LPARAM lParam, LRESULT* pResult) 
{
	NMHDR* pNMHDR = (NMHDR*)lParam;

	if (pNMHDR->code == PSN_HELP) {
		helpService.ShowScreenHelp(IDD);
		return TRUE;
	}
	
	return CPropertyPage::OnNotify(wParam, lParam, pResult);
}
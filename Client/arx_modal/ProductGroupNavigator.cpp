// ProductGroupNavigator.cpp : implementation file
//

#include "stdafx.h"
#include "modal.h"
#include "ProductGroupNavigator.h"
#include "ProductGroupFrame.h"
#include "ResourceHelper.h"
#include "ProductGroupDialog.h"
#include "ProductGroupCriteriaMaintenance.h"
#include "ProductGroupCriteriaMatrix.h"
#include "ProductGroupAssignmentDialog.h"
#include "Processing.h"
#include "HelpService.h"

#include <adscodes.h>
#include <dbsymtb.h>

#ifdef _DEBUG
#define new DEBUG_NEW
#undef THIS_FILE
static char THIS_FILE[] = __FILE__;
#endif

/////////////////////////////////////////////////////////////////////////////
// CProductGroupNavigator dialog
extern CProductGroupFrame *m_ProductGroupFrame;
extern CHelpService helpService;

const int PT_MAINTAIN_GROUPS = 1;
const int PT_MAINTAIN_CRITERIA = 2;
const int PT_ASSIGN_CRITERIA = 3;
const int PT_ASSIGN_PRODUCTS = 4;
const int PT_RACK_USAGE = 5;

IMPLEMENT_DYNCREATE(CProductGroupNavigator, CDialog)

CProductGroupNavigator::CProductGroupNavigator(CWnd* pParent /*=NULL*/)
	: CDialog(CProductGroupNavigator::IDD, pParent)
{
	//{{AFX_DATA_INIT(CProductGroupNavigator)
		// NOTE: the ClassWizard will add member initialization here
	//}}AFX_DATA_INIT
}

CProductGroupNavigator::~CProductGroupNavigator()
{
	for (int i=0; i < m_ProductGroupList.GetSize(); ++i)
		delete m_ProductGroupList[i];

	for (i=0; i < m_ProductGroupCriteriaList.GetSize(); ++i)
		delete m_ProductGroupCriteriaList[i];

	for (i=0; i < m_ProductAttributeList.GetSize(); ++i)
		delete m_ProductAttributeList[i];

}

void CProductGroupNavigator::DoDataExchange(CDataExchange* pDX)
{
	CDialog::DoDataExchange(pDX);
	//{{AFX_DATA_MAP(CProductGroupNavigator)
	//}}AFX_DATA_MAP
}


BEGIN_MESSAGE_MAP(CProductGroupNavigator, CDialog)
	//{{AFX_MSG_MAP(CProductGroupNavigator)
	ON_BN_CLICKED(IDC_MAINTAIN_CRITERIA, OnMaintainCriteria)
	ON_BN_CLICKED(IDC_MAINTAIN_GROUPS, OnMaintainGroups)
	ON_BN_CLICKED(IDC_ASSIGN_CRITERIA, OnAssignCriteria)
	ON_BN_CLICKED(IDC_ASSIGN_PRODUCTS, OnAssignProducts)
	ON_BN_CLICKED(IDC_QUIT, OnQuit)
	ON_BN_CLICKED(IDC_SHOW_HELP, OnShowHelp)
	ON_WM_HELPINFO()
	//}}AFX_MSG_MAP
END_MESSAGE_MAP()

/////////////////////////////////////////////////////////////////////////////
// CProductGroupNavigator message handlers


BOOL CProductGroupNavigator::Create(LPCTSTR lpszClassName, LPCTSTR lpszWindowName, DWORD dwStyle, const RECT& rect, CWnd* pParentWnd, UINT nID, CCreateContext* pContext) 
{

	UNREFERENCED_PARAMETER(lpszClassName);
	UNREFERENCED_PARAMETER(lpszWindowName);
	UNREFERENCED_PARAMETER(dwStyle);
	UNREFERENCED_PARAMETER(rect);
	UNREFERENCED_PARAMETER(nID);
	UNREFERENCED_PARAMETER(pContext);

	BOOL bReturn = CDialog::Create(IDD, pParentWnd);

	int id;
	id = m_ProductGroupFrame->m_Splitter.IdFromRowCol(0, 0);

	if ( bReturn )
		::SetWindowLong ( m_hWnd, GWL_ID, id);
	
	return bReturn;

	return CWnd::Create(lpszClassName, lpszWindowName, dwStyle, rect, pParentWnd, nID, pContext);

}

void CProductGroupNavigator::PostNcDestroy() 
{
	delete this;
}


BOOL CProductGroupNavigator::OnInitDialog() 
{
	CDialog::OnInitDialog();
	CButton *pButton;
	CBitmap bitmap;
	CStringArray results;
	CTemporaryResourceOverride temp;

	pButton = (CButton *)GetDlgItem(IDC_MAINTAIN_GROUPS);
	bitmap.LoadMappedBitmap(IDB_MAINTAIN_GROUPS);
	pButton->SetBitmap((HBITMAP)bitmap.Detach());
	pButton->SetCheck(1);
	m_CurrentType = PT_MAINTAIN_GROUPS;

	pButton = (CButton *)GetDlgItem(IDC_MAINTAIN_CRITERIA);
	bitmap.LoadMappedBitmap(IDB_MAINTAIN_CRITERIA);
	pButton->SetBitmap((HBITMAP)bitmap.Detach());
	
	pButton = (CButton *)GetDlgItem(IDC_ASSIGN_CRITERIA);
	bitmap.LoadMappedBitmap(IDB_ASSIGN_CRITERIA);
	pButton->SetBitmap((HBITMAP)bitmap.Detach());
	
	pButton = (CButton *)GetDlgItem(IDC_ASSIGN_PRODUCTS);
	bitmap.LoadMappedBitmap(IDB_ASSIGN_PRODUCTS);
	pButton->SetBitmap((HBITMAP)bitmap.Detach());
	
	pButton = (CButton *)GetDlgItem(IDC_SHOW_HELP);
	bitmap.LoadMappedBitmap(IDB_HELP);
	pButton->SetBitmap((HBITMAP)bitmap.Detach());
	
	pButton = (CButton *)GetDlgItem(IDC_QUIT);
	bitmap.LoadMappedBitmap(IDB_EXIT);
	pButton->SetBitmap((HBITMAP)bitmap.Detach());

	CProcessing *dlg = new CProcessing;
	dlg->Create(IDD_PROCESSING, m_ProductGroupFrame->GetParent());
	dlg->m_StatusText = "Loading Product Groups...";
	dlg->UpdateData(FALSE);
	dlg->CenterWindow();
	dlg->ShowWindow(SW_SHOW);
	dlg->UpdateWindow();

	CWaitCursor cwc;

	this->ShowWindow(SW_HIDE);


	try {
		dlg->m_StatusText = "Loading Product Groups...";
		dlg->UpdateData(FALSE);
		m_ProductGroupDataService.LoadProductGroups(m_ProductGroupList);
		
		dlg->m_StatusText = "Loading Product Attributes...";
		dlg->UpdateData(FALSE);
		m_ProductGroupDataService.LoadProductAttributes(m_ProductAttributeList);
		
		dlg->m_StatusText = "Load Product Group Criteria...";
		dlg->UpdateData(FALSE);
		m_ProductGroupDataService.LoadProductGroupCriteria(m_ProductGroupCriteriaList);
		
		dlg->m_StatusText = "Loading Product Group Queries...";
		dlg->UpdateData(FALSE);
		m_ProductGroupDataService.LoadAllProductGroupQueries(m_ProductGroupList);
		
		dlg->m_StatusText = "Loading Product Group Constraints...";
		dlg->UpdateData(FALSE);
		m_ProductGroupDataService.LoadAllProductGroupConstraints(m_ProductGroupList);

		
		dlg->m_StatusText = "Loading Ranges...";
		dlg->UpdateData(FALSE);
		
		m_ProductGroupDataService.LoadAllCriteriaDetail(m_ProductGroupCriteriaList);

	}
	catch (...) {
		AfxMessageBox("Error loading product group information.");
	}

	dlg->DestroyWindow();
	this->CenterWindow();
	this->ShowWindow(SW_SHOW);

	return TRUE;  // return TRUE unless you set the focus to a control
	              // EXCEPTION: OCX Property Pages should return FALSE
}

void CProductGroupNavigator::OnMaintainGroups() 
{
	CButton *pButton;
	CWnd *pWnd;

	pButton = (CButton *)GetDlgItem(IDC_MAINTAIN_GROUPS);
	if (! pButton->GetCheck())
		return;

	pWnd = m_ProductGroupFrame->m_Splitter.GetPane(0, 1);
	if (! ValidateClose(pWnd)) {
		pButton->SetCheck(0);
		return;
	}

	m_ProductGroupFrame->m_Splitter.DeleteView(0, 1);
	m_ProductGroupFrame->m_Splitter.CreateView(0, 1, RUNTIME_CLASS(CProductGroupDialog), CSize(0,0), NULL);
	
	m_ProductGroupFrame->SetWindowText("Maintain Product Groups");
	m_CurrentType = PT_MAINTAIN_GROUPS;

	m_ProductGroupFrame->m_Splitter.RecalcLayout();

}

void CProductGroupNavigator::OnMaintainCriteria() 
{	
	CButton *pButton;
	CWnd *pWnd;

	pButton = (CButton *)GetDlgItem(IDC_MAINTAIN_CRITERIA);
	if (! pButton->GetCheck())
		return;
	
	pWnd = m_ProductGroupFrame->m_Splitter.GetPane(0, 1);
	if (! ValidateClose(pWnd)) {
		pButton->SetCheck(0);
		return;
	}

	m_ProductGroupFrame->m_Splitter.DeleteView(0, 1);
	m_ProductGroupFrame->m_Splitter.CreateView(0, 1, RUNTIME_CLASS(CProductGroupCriteriaMaintenance), CSize(0,0), NULL);

	m_ProductGroupFrame->SetWindowText("Maintain Product Group Criteria");
	m_CurrentType = PT_MAINTAIN_CRITERIA;

	m_ProductGroupFrame->m_Splitter.RecalcLayout();
	
}

void CProductGroupNavigator::OnAssignCriteria() 
{
	CButton *pButton;
	CWnd *pWnd;

	pButton = (CButton *)GetDlgItem(IDC_ASSIGN_CRITERIA);
	if (! pButton->GetCheck())
		return;

	pWnd = m_ProductGroupFrame->m_Splitter.GetPane(0, 1);
	if (! ValidateClose(pWnd)) {
		pButton->SetCheck(0);
		return;
	}

	m_ProductGroupFrame->m_Splitter.DeleteView(0, 1);
	m_ProductGroupFrame->m_Splitter.CreateView(0, 1, RUNTIME_CLASS(CProductGroupCriteriaMatrix), CSize(0,0), NULL);

	m_ProductGroupFrame->SetWindowText("Assign Criteria to Product Groups");
	m_CurrentType = PT_ASSIGN_CRITERIA;

	m_ProductGroupFrame->m_Splitter.RecalcLayout();
	
}

void CProductGroupNavigator::OnAssignProducts() 
{
	CButton *pButton;
	CWnd *pWnd;

	pButton = (CButton *)GetDlgItem(IDC_ASSIGN_PRODUCTS);
	if (! pButton->GetCheck())
		return;

	pWnd = m_ProductGroupFrame->m_Splitter.GetPane(0, 1);
	if (! ValidateClose(pWnd)) {
		pButton->SetCheck(0);
		return;
	}

	m_ProductGroupFrame->m_Splitter.DeleteView(0, 1);
	m_ProductGroupFrame->m_Splitter.CreateView(0, 1, RUNTIME_CLASS(CProductGroupAssignmentDialog), CSize(0,0), NULL);

	m_ProductGroupFrame->SetWindowText("Assign Products");
	m_CurrentType = PT_ASSIGN_PRODUCTS;

	m_ProductGroupFrame->m_Splitter.RecalcLayout();
}

void CProductGroupNavigator::OnQuit() 
{
	CButton *pButton;
	CWnd *pWnd;

	pButton = (CButton *)GetDlgItem(IDC_QUIT);
	if (! pButton->GetCheck())
		return;

	pWnd = m_ProductGroupFrame->m_Splitter.GetPane(0, 1);
	if (! ValidateClose(pWnd)) {
		pButton->SetCheck(0);
		return;
	}

	m_ProductGroupFrame->DestroyWindow();
		
	HWND hWnd = adsw_acadMainWnd(); 
	CWnd *parent;
	parent = CWnd::FromHandle(hWnd);
	parent->BringWindowToTop();

	return;

}

BOOL CProductGroupNavigator::ValidateClose(CWnd *pWnd)
{
	CProductGroupDialog *pDlg1;
	CProductGroupCriteriaMaintenance *pDlg2;
	CProductGroupCriteriaMatrix *pDlg3;
	CProductGroupAssignmentDialog *pDlg4;
	CButton *pButton;

	switch (m_CurrentType) {
	case PT_MAINTAIN_GROUPS:
		pDlg1 = (CProductGroupDialog *)pWnd;
		if (! pDlg1->ValidateClose()) {
			pButton = (CButton *)GetDlgItem(IDC_MAINTAIN_GROUPS);
			pButton->SetCheck(1);
			return FALSE;
		}
		break;
	case PT_MAINTAIN_CRITERIA:
		pDlg2 = (CProductGroupCriteriaMaintenance *)pWnd;
		if (! pDlg2->ValidateClose()) {
			pButton = (CButton *)GetDlgItem(IDC_MAINTAIN_CRITERIA);
			pButton->SetCheck(1);
			return FALSE;
		}
		break;
	case PT_ASSIGN_CRITERIA:
		pDlg3 = (CProductGroupCriteriaMatrix *)pWnd;
		if (! pDlg3->ValidateClose()) {
			pButton = (CButton *)GetDlgItem(IDC_ASSIGN_CRITERIA);
			pButton->SetCheck(1);
			return FALSE;
		}
		break;
	case PT_ASSIGN_PRODUCTS:
		pDlg4 = (CProductGroupAssignmentDialog *)pWnd;
		if (! pDlg4->ValidateClose()) {
			pButton = (CButton *)GetDlgItem(IDC_ASSIGN_PRODUCTS);
			pButton->SetCheck(1);
			return FALSE;
		}
		break;
	case PT_RACK_USAGE:
		break;
	}

	return TRUE;

}

void CProductGroupNavigator::OnShowHelp() 
{
	CButton *pButton;

	helpService.ShowScreenHelp(IDD);

	switch (m_CurrentType) {
	case PT_MAINTAIN_GROUPS:
		pButton = (CButton *)GetDlgItem(IDC_MAINTAIN_GROUPS);
		pButton->SetCheck(1);
		break;
	case PT_MAINTAIN_CRITERIA:
		pButton = (CButton *)GetDlgItem(IDC_MAINTAIN_CRITERIA);
		pButton->SetCheck(1);
		break;
	case PT_ASSIGN_CRITERIA:
		pButton = (CButton *)GetDlgItem(IDC_ASSIGN_CRITERIA);
		pButton->SetCheck(1);
		break;
	case PT_ASSIGN_PRODUCTS:
		pButton = (CButton *)GetDlgItem(IDC_ASSIGN_PRODUCTS);
		pButton->SetCheck(1);
	case PT_RACK_USAGE:
		break;
	}
	
	pButton = (CButton *)GetDlgItem(IDC_SHOW_HELP);
	pButton->SetCheck(0);

	return;
	
}

BOOL CProductGroupNavigator::OnHelpInfo(HELPINFO* pHelpInfo) 
{
	helpService.ShowFieldHelp(IDD, pHelpInfo->iCtrlId);
	
	return FALSE;
}

#if !defined(AFX_cOSTCOMPARISONdIALOG_H__B23E8EF3_82B8_4B2D_8A43_233316A591CB__INCLUDED_)
#define AFX_COSTCOMPARISONdIALOG_H__B23E8EF3_82B8_4B2D_8A43_233316A591CB__INCLUDED_

#if _MSC_VER > 1000
#pragma once
#endif // _MSC_VER > 1000
// CostComparisonDialog.h : header file
//

/////////////////////////////////////////////////////////////////////////////
// CCostComparisonDialog dialog

class CCostComparisonDialog : public CDialog
{
// Construction
public:
	void Reorder();
	CCostComparisonDialog(CWnd* pParent = NULL);   // standard constructor
	static UINT CalculateBaselineCostThread(LPVOID pParam);
	
// Dialog Data
	//{{AFX_DATA(CCostComparisonDialog)
	enum { IDD = IDD_COST_COMPARISON };
	CComboBox	m_TimeHorizonUnitsCtrl;
	CListCtrl	m_FacilityListCtrl;
	int		m_TimeHorizonUnits;
	CString	m_TimeHorizonValue;
	//}}AFX_DATA


// Overrides
	// ClassWizard generated virtual function overrides
	//{{AFX_VIRTUAL(CCostComparisonDialog)
	protected:
	virtual void DoDataExchange(CDataExchange* pDX);    // DDX/DDV support
	//}}AFX_VIRTUAL

// Implementation
protected:

	// Generated message map functions
	//{{AFX_MSG(CCostComparisonDialog)
	afx_msg void OnRecalculateCost();
	afx_msg void OnRefresh();
	virtual BOOL OnInitDialog();
	afx_msg BOOL OnHelpInfo(HELPINFO* pHelpInfo);
	afx_msg void OnHelp();
	afx_msg void OnUp();
	afx_msg void OnDown();
	//}}AFX_MSG
	DECLARE_MESSAGE_MAP()

private:
	void BuildList();
	void ConvertCosts();
	void LoadFacilities();

	typedef struct {
		int facilityId;
		CString description;
		double optimizeCost;
		double baselineCost;
		int timeHorizonUnits;
		int timeHorizonDuration;
	} facCostStruct;

	CArray<facCostStruct, facCostStruct&> m_FacilityList;
public:
	afx_msg void OnBnClickedShowDetails();
};

//{{AFX_INSERT_LOCATION}}
// Microsoft Visual C++ will insert additional declarations immediately before the previous line.

#endif // !defined(AFX_cOSTcOMPARISONdIALOG_H__B23E8EF3_82B8_4B2D_8A43_233316A591CB__INCLUDED_)

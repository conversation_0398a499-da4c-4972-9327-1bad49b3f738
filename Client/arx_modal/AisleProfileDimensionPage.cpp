// AisleProfileDimensionPage.cpp : implementation file
//

#include "stdafx.h"
#include "modal.h"
#include "AisleProfileDimensionPage.h"
#include "AisleProfileSheet.h"
#include "UtilityHelper.h"
#include "HelpService.h"

#ifdef _DEBUG
#define new DEBUG_NEW
#undef THIS_FILE
static char THIS_FILE[] = __FILE__;
#endif

extern CUtilityHelper utilityHelper;
extern CHelpService helpService;
/////////////////////////////////////////////////////////////////////////////
// CAisleProfileDimensionPage property page

IMPLEMENT_DYNCREATE(CAisleProfileDimensionPage, CPropertyPage)

CAisleProfileDimensionPage::CAisleProfileDimensionPage() : CPropertyPage(CAisleProfileDimensionPage::IDD)
{
	//{{AFX_DATA_INIT(CAisleProfileDimensionPage)
	m_AisleSpace = _T("");
	m_RightSpace = _T("");
	m_LeftSpace = _T("");
	//}}AFX_DATA_INIT
	m_pAisleProfile = NULL;
}

CAisleProfileDimensionPage::~CAisleProfileDimensionPage()
{
}

void CAisleProfileDimensionPage::DoDataExchange(CDataExchange* pDX)
{
	CPropertyPage::DoDataExchange(pDX);
	//{{AFX_DATA_MAP(CAisleProfileDimensionPage)
	DDX_Control(pDX, IDC_AISLE_BUTTON, m_AisleButton);
	DDX_Text(pDX, IDC_AISLE_SPACE, m_AisleSpace);
	DDX_Text(pDX, IDC_RIGHT_SPACE, m_RightSpace);
	DDX_Text(pDX, IDC_LEFT_SPACE, m_LeftSpace);
	//}}AFX_DATA_MAP
}


BEGIN_MESSAGE_MAP(CAisleProfileDimensionPage, CPropertyPage)
	//{{AFX_MSG_MAP(CAisleProfileDimensionPage)
	ON_WM_HELPINFO()
	//}}AFX_MSG_MAP
END_MESSAGE_MAP()

/////////////////////////////////////////////////////////////////////////////
// CAisleProfileDimensionPage message handlers

BOOL CAisleProfileDimensionPage::OnSetActive() 
{
	CAisleProfileSheet *pSheet = (CAisleProfileSheet *)GetParent();

	m_pAisleProfile = pSheet->m_pAisleProfile;

	m_AisleSpace.Format("%.02f", m_pAisleProfile->m_AisleSpace);
	m_LeftSpace.Format("%.02f", m_pAisleProfile->m_LeftSpace);
	m_RightSpace.Format("%.02f", m_pAisleProfile->m_RightSpace);

	if (m_pAisleProfile->m_pLeftSideProfile == NULL)
		GetDlgItem(IDC_LEFT_SPACE)->EnableWindow(FALSE);
	else
		GetDlgItem(IDC_LEFT_SPACE)->EnableWindow(TRUE);

	if (m_pAisleProfile->m_pRightSideProfile == NULL)
		GetDlgItem(IDC_RIGHT_SPACE)->EnableWindow(FALSE);
	else
		GetDlgItem(IDC_RIGHT_SPACE)->EnableWindow(TRUE);

	UpdateData(FALSE);

	return CPropertyPage::OnSetActive();
}

BOOL CAisleProfileDimensionPage::OnKillActive() 
{
	UpdateData(TRUE);

	if (m_AisleSpace == "" && 
		m_pAisleProfile->m_pLeftSideProfile != NULL && 
		m_pAisleProfile->m_pRightSideProfile != NULL) {
		AfxMessageBox("Aisle space is required when both sides of the aisle are defined.");
		return utilityHelper.SetEditControlErrorState(this, IDC_AISLE_SPACE);
	}

	if (! utilityHelper.IsFloat(m_AisleSpace)) {
		AfxMessageBox("Please enter a positive decimal number for Aisle Space.");
		return utilityHelper.SetEditControlErrorState(this, IDC_AISLE_SPACE);
	}

	if (! utilityHelper.IsFloat(m_LeftSpace)) {
		AfxMessageBox("Please enter a positive decimal number for Left Side Space.");
		return utilityHelper.SetEditControlErrorState(this, IDC_LEFT_SPACE);
	}
	
	if (! utilityHelper.IsFloat(m_RightSpace)) {
		AfxMessageBox("Please enter a positive decimal number for Right Side Space.");
		return utilityHelper.SetEditControlErrorState(this, IDC_RIGHT_SPACE);
	}

	if (atof(m_AisleSpace) < 0) {
		AfxMessageBox("Please enter a positive decimal number for Aisle Space.");
		return utilityHelper.SetEditControlErrorState(this, IDC_AISLE_SPACE);
	}

	if (atof(m_LeftSpace) < 0) {
		AfxMessageBox("Please enter a positive decimal number for Left Side Space.");
		return utilityHelper.SetEditControlErrorState(this, IDC_LEFT_SPACE);
	}
	
	if (atof(m_RightSpace) < 0) {
		AfxMessageBox("Please enter a positive decimal number for Right Side Space.");
		return utilityHelper.SetEditControlErrorState(this, IDC_RIGHT_SPACE);
	}

	m_pAisleProfile->m_AisleSpace = atof(m_AisleSpace);
	m_pAisleProfile->m_LeftSpace = atof(m_LeftSpace);
	m_pAisleProfile->m_RightSpace = atof(m_RightSpace);


	return CPropertyPage::OnKillActive();
}

BOOL CAisleProfileDimensionPage::OnCommand(WPARAM wParam, LPARAM lParam) 
{
	if  (HIWORD(wParam) == EN_KILLFOCUS) {
		UpdateData(TRUE);
		m_AisleButton.Invalidate();
	}

	return CPropertyPage::OnCommand(wParam, lParam);
}

BOOL CAisleProfileDimensionPage::OnHelpInfo(HELPINFO* pHelpInfo) 
{
	helpService.ShowFieldHelp(IDD, pHelpInfo->iCtrlId);
	return FALSE;

}

BOOL CAisleProfileDimensionPage::OnNotify(WPARAM wParam, LPARAM lParam, LRESULT* pResult) 
{
	NMHDR* pNMHDR = (NMHDR*)lParam;

	if (pNMHDR->code == PSN_HELP) {
		helpService.ShowScreenHelp(IDD);
		return TRUE;
	}
	
	return CPropertyPage::OnNotify(wParam, lParam, pResult);
}
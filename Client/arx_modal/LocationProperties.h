#if !defined(AFX_LOCATIONPROPERTIES_H__18B640C3_AB54_11D4_9EBD_00C04FAC149C__INCLUDED_)
#define AFX_LOCATIONPROPERTIES_H__18B640C3_AB54_11D4_9EBD_00C04FAC149C__INCLUDED_

#if _MSC_VER > 1000
#pragma once
#endif // _MSC_VER > 1000
// LocationProperties.h : header file
//
#include "Resource.h"

/////////////////////////////////////////////////////////////////////////////
// CLocationProperties dialog

class CLocationProperties : public CPropertyPage
{
	DECLARE_DYNCREATE(CLocationProperties)

// Construction
public:
	void OnDeactivate();
	void OnActivate();
	double m_OriginalWidth;
	double m_MaxHeight;
	double m_MaxDepth;
	double m_MinWidth;
	double m_MaxWidth;
	double m_TotalWidth;
	int m_LocationDBId;
	long m_ProductDBID;
	CLocationProperties();
	~CLocationProperties();

// Dialog Data
	//{{AFX_DATA(CLocationProperties)
	enum { IDD = IDD_LOCATION_PROPERTIES };
	CString	m_Coordinates;
	CString	m_Description;
	int		m_HandlingMethod;
	double	m_MaxWeight;
	CString	m_ProductDescription;
	CString	m_ProductGroup;
	BOOL	m_IsSelect;
	CString	m_WMSID;
	double	m_Depth;
	double	m_Height;
	double	m_Width;
	CString	m_Status;
	BOOL	m_IsActive;
	CString	m_BackfillCoordinates;
	CString	m_BackfillId;
	BOOL	m_Comingle;
	CString	m_StockerId;
	BOOL	m_Trace;
	double	m_Clearance;
	CString	m_StockerCoordinates;
	CString	m_SelectionSequence;
	CString	m_ReplenishmentSequence;
	//}}AFX_DATA


// Overrides
	// ClassWizard generate virtual function overrides
	//{{AFX_VIRTUAL(CLocationProperties)
	public:
	virtual BOOL OnKillActive();
	protected:
	virtual void DoDataExchange(CDataExchange* pDX);    // DDX/DDV support
	virtual BOOL OnNotify(WPARAM wParam, LPARAM lParam, LRESULT* pResult);
	//}}AFX_VIRTUAL

// Implementation
protected:
	// Generated message map functions
	//{{AFX_MSG(CLocationProperties)
	virtual BOOL OnInitDialog();
	afx_msg BOOL OnHelpInfo(HELPINFO* pHelpInfo);
	afx_msg void OnViewProduct();
	afx_msg void OnBaselineProduct();
	afx_msg void OnOptimizedProduct();
	//}}AFX_MSG
	DECLARE_MESSAGE_MAP()

};

//{{AFX_INSERT_LOCATION}}
// Microsoft Visual C++ will insert additional declarations immediately before the previous line.

#endif // !defined(AFX_LOCATIONPROPERTIES_H__18B640C3_AB54_11D4_9EBD_00C04FAC149C__INCLUDED_)

// BayProfileCrossbarPage.cpp : implementation file
//

#include "stdafx.h"
#include "modal.h"
#include "BayProfileCrossbarPage.h"
#include "BayProfileSheet.h"
#include "BayProfileCrossbarProperties.h"
#include "UtilityHelper.h"
#include "ResourceHelper.h"
#include "HelpService.h"

#ifdef _DEBUG
#define new DEBUG_NEW
#undef THIS_FILE
static char THIS_FILE[] = __FILE__;
#endif

extern CHelpService helpService;
extern CUtilityHelper utilityHelper;
/////////////////////////////////////////////////////////////////////////////
// CBayProfileCrossbarPage property page

IMPLEMENT_DYNCREATE(CBayProfileCrossbarPage, CPropertyPage)

CBayProfileCrossbarPage::CBayProfileCrossbarPage() : CPropertyPage(CBayProfileCrossbarPage::IDD)
{
	//{{AFX_DATA_INIT(CBayProfileCrossbarPage)
	m_Position = _T("");
	m_Hidden = _T("");
	m_Thickness = _T("");
	m_WeightCapacity = _T("");
	m_Clearance = _T("");
	m_Overhang = _T("");
	//}}AFX_DATA_INIT
}

CBayProfileCrossbarPage::~CBayProfileCrossbarPage()
{
}

void CBayProfileCrossbarPage::DoDataExchange(CDataExchange* pDX)
{
	CPropertyPage::DoDataExchange(pDX);
	//{{AFX_DATA_MAP(CBayProfileCrossbarPage)
	DDX_Control(pDX, IDC_LEVEL_LIST, m_LevelListCtrl);
	DDX_Control(pDX, IDC_CROSSBAR_BUTTON, m_CrossbarButton);
	DDX_Text(pDX, IDC_CROSSBAR_POSITION, m_Position);
	DDX_Text(pDX, IDC_HIDDEN, m_Hidden);
	DDX_Text(pDX, IDC_THICKNESS, m_Thickness);
	DDX_Text(pDX, IDC_WEIGHT_CAPACITY, m_WeightCapacity);
	DDX_Text(pDX, IDC_CLEARANCE, m_Clearance);
	DDX_Text(pDX, IDC_OVERHANG, m_Overhang);
	//}}AFX_DATA_MAP
}


BEGIN_MESSAGE_MAP(CBayProfileCrossbarPage, CPropertyPage)
	//{{AFX_MSG_MAP(CBayProfileCrossbarPage)
	ON_WM_CONTEXTMENU()
	ON_COMMAND(ID_GENERIC_ADD, OnMenuAdd)
	ON_COMMAND(ID_GENERIC_PROPERTIES, OnEdit)
	ON_COMMAND(ID_GENERIC_DELETE, OnDeleteCrossbar)
	ON_CBN_SELCHANGE(IDC_LEVEL_LIST, OnSelchangeLevelList)
	ON_BN_CLICKED(IDC_ADD_CROSSBAR, OnAddButton)
	ON_BN_CLICKED(IDC_EDIT_CROSSBAR, OnEdit)
	ON_BN_CLICKED(IDC_DELETE_CROSSBAR, OnDeleteCrossbar)
	ON_MESSAGE(WM_SELECT_LEVEL, OnSelectLevel)
	ON_MESSAGE(WM_ADD_LEVEL, OnAddCrossbar)
	ON_MESSAGE(WM_VIEW_LEVEL, OnView)
	ON_WM_HELPINFO()
	//}}AFX_MSG_MAP
END_MESSAGE_MAP()

/////////////////////////////////////////////////////////////////////////////
// CBayProfileCrossbarPage message handlers
BOOL CBayProfileCrossbarPage::OnInitDialog() 
{
	CPropertyPage::OnInitDialog();	
	
	CBayProfileSheet *pSheet = (CBayProfileSheet *)GetParent();
	m_pBayProfile = pSheet->m_pBayProfile;

	if (pSheet->m_SelectedLevel < 0)
		pSheet->m_SelectedLevel = 0;

	EnableToolTips(TRUE);

	m_ToolTip.Create(this, TTF_IDISHWND | TTF_TRACK | TTF_ABSOLUTE);
	//m_tooltip.Activate(TRUE);
	
	m_ToolTipText = "T";
	m_ToolTip.AddTool(GetDlgItem(IDC_CROSSBAR_BUTTON),m_ToolTipText);

	m_ToolTip.SendMessage(TTM_SETDELAYTIME, (WPARAM)(DWORD)TTDT_INITIAL,
		(LPARAM)(INT)MAKELONG(100,0));
	m_ToolTip.SendMessage(TTM_SETDELAYTIME, (WPARAM)(DWORD)TTDT_AUTOPOP,
		(LPARAM)(INT)MAKELONG(3000,0));


	CToolInfo ti;
	m_ToolTip.GetToolInfo(ti, GetDlgItem(IDC_CROSSBAR_BUTTON), 0);
	m_ToolTip.SendMessage(TTM_TRACKACTIVATE, (WPARAM)TRUE, (LPARAM)&ti);
	
	m_ToolTip.Activate(FALSE);

	return TRUE;  // return TRUE unless you set the focus to a control
	              // EXCEPTION: OCX Property Pages should return FALSE
}

BOOL CBayProfileCrossbarPage::OnSetActive() 
{
	CBayProfileSheet *pSheet = (CBayProfileSheet *)GetParent();

	m_pBayProfile = pSheet->m_pBayProfile;

	m_CrossbarButton.m_CrossbarList.RemoveAll();
	m_CrossbarButton.m_UprightHeight = m_pBayProfile->m_UprightHeight;
	m_CrossbarButton.m_BayHeight = m_pBayProfile->m_Height;

	for (int i=0; i < m_pBayProfile->m_LevelProfileList.GetSize(); ++i) {
		CLevelProfile *pLevelProfile = m_pBayProfile->m_LevelProfileList[i];

		CBayProfileCrossbarInfo info;
		info.m_Clearance = pLevelProfile->m_Clearance;
		info.m_Height = pLevelProfile->m_Coordinates.m_Z;
		info.m_IsHidden = pLevelProfile->m_IsBarHidden;
		info.m_IsSelected = (i == pSheet->m_SelectedLevel);
		info.m_LocationCount = pLevelProfile->m_LocationProfileList.GetSize();
		if (info.m_LocationCount == 0)
			info.m_LocationSpace = 0;
		else
			info.m_LocationSpace = pLevelProfile->m_LocationProfileList[0]->m_LocationSpace;
		info.m_Thickness = pLevelProfile->m_Thickness;
		
		m_CrossbarButton.m_CrossbarList.Add(info);

	}

	RebuildLevelList();

	//OnSelectLevel(pSheet->m_SelectedLevel, 0);
	m_CrossbarButton.SelectCrossbar(pSheet->m_SelectedLevel);

	if (m_pBayProfile->m_Active) {
		GetDlgItem(IDC_ADD_CROSSBAR)->EnableWindow(FALSE);
		GetDlgItem(IDC_DELETE_CROSSBAR)->EnableWindow(FALSE);
	}
	else {
		GetDlgItem(IDC_ADD_CROSSBAR)->EnableWindow(TRUE);
		GetDlgItem(IDC_DELETE_CROSSBAR)->EnableWindow(TRUE);
	}

	return CPropertyPage::OnSetActive();
}


BOOL CBayProfileCrossbarPage::OnKillActive()
{
	if (! Validate())
		return FALSE;

	return CPropertyPage::OnKillActive();
}


afx_msg LRESULT CBayProfileCrossbarPage::OnSelectLevel(WPARAM wParam, LPARAM lParam)
{
	UNREFERENCED_PARAMETER(lParam);

	int currentLevel = wParam;
	
	CBayProfileSheet *pSheet = (CBayProfileSheet *)GetParent();

	if (currentLevel >= 0) {
		if (UpdateScreenFromLevelProfile(currentLevel) < 0)
			return -1;
	}

	pSheet->m_SelectedLevel = currentLevel;
	
	m_LevelListCtrl.SetCurSel(currentLevel);

	return 0;
}

int CBayProfileCrossbarPage::GetLevelByHeight(double height)
{
	for (int i=0; i < m_pBayProfile->m_LevelProfileList.GetSize(); ++i) {
		if (m_pBayProfile->m_LevelProfileList[i]->m_Coordinates.m_Z == height)
			return i;
	}

	return -1;
}

void CBayProfileCrossbarPage::OnDeleteCrossbar() 
{
	int curSel = GetCurSel();
	if (curSel <= 0)
		return;

	m_CrossbarButton.m_CrossbarList.RemoveAt(curSel);
	delete m_pBayProfile->m_LevelProfileList[curSel];
	m_pBayProfile->m_LevelProfileList.RemoveAt(curSel);

	RebuildLevelList();


	if (curSel >= m_pBayProfile->m_LevelProfileList.GetSize())
		curSel = m_pBayProfile->m_LevelProfileList.GetSize()-1;

	CBayProfileSheet *pSheet = (CBayProfileSheet *)GetParent();
	pSheet->m_SelectedLevel = -1;

	//OnSelectLevel(WPARAM(curSel), 0);
	m_CrossbarButton.SelectCrossbar(curSel);
}


afx_msg LRESULT CBayProfileCrossbarPage::OnAddCrossbar(WPARAM wParam, LPARAM lParam)
{
	double height = wParam;
	double maxThickness = lParam;

	// Just to be nice - use the thickness that they specified before if possible
	if (m_pBayProfile->m_LevelProfileList.GetSize() > 1)
		maxThickness = m_pBayProfile->m_LevelProfileList[1]->m_Thickness;

	OnAdd(height, maxThickness);
	/*
	for (int i=0; i < m_pBayProfile->m_LevelProfileList.GetSize(); ++i) {
		CLevelProfile *pLevelProfile = m_pBayProfile->m_LevelProfileList[i];
		if (pLevelProfile->m_Coordinates.m_Z == height)
			return -1;		// Can't add a duplicate

		if (pLevelProfile->m_Coordinates.m_Z > height)
			break;
	}

	CLevelProfile *pLevelProfile = new CLevelProfile();
	m_pBayProfile->m_LevelProfileList.InsertAt(i, pLevelProfile);
	pLevelProfile->m_BayProfileDBId = m_pBayProfile->m_BayProfileDBId;
	pLevelProfile->m_Baytype = m_pBayProfile->m_BayType;
	pLevelProfile->m_Coordinates.m_Z = height;
	pLevelProfile->m_Thickness = maxThickness;

	CBayProfileCrossbarInfo info;
	info.m_Clearance = pLevelProfile->m_Clearance;
	info.m_Height = height;
	info.m_IsHidden = FALSE;
	info.m_IsSelected = FALSE;		// we'll select it below
	info.m_LocationCount = 0;
	info.m_LocationSpace = 0;
	info.m_Thickness = maxThickness;
	m_CrossbarButton.m_CrossbarList.InsertAt(i, info);

	RebuildLevelList();

//	OnSelectLevel(i, 0);
	m_CrossbarButton.SelectCrossbar(i);
//	m_CrossbarButton.Invalidate(TRUE);
	*/

	return 0;
}


void CBayProfileCrossbarPage::OnSelchangeLevelList() 
{
	int curSel = m_LevelListCtrl.GetCurSel();
	if (curSel < 0)
		return;

	if (m_CrossbarButton.SelectCrossbar(curSel) < 0) {
		// Set the selection back to the previous which
		// is kept on the parent
		CBayProfileSheet *pSheet = (CBayProfileSheet *)GetParent();
		m_LevelListCtrl.SetCurSel(pSheet->m_SelectedLevel);
	}
			
	return;

}

int CBayProfileCrossbarPage::UpdateScreenFromLevelProfile(int currentLevel)
{

	CLevelProfile *pLevelProfile = m_pBayProfile->m_LevelProfileList[currentLevel];
	m_Clearance.Format("%.0f", pLevelProfile->m_Clearance);
	m_Overhang.Format("%.0f", pLevelProfile->m_Overhang);
	m_Position.Format("%.0f", pLevelProfile->m_Coordinates.m_Z);
	m_Hidden = (pLevelProfile->m_IsBarHidden) ? "Yes" : "No";
	m_Thickness.Format("%.0f", pLevelProfile->m_Thickness);
	m_WeightCapacity.Format("%.0f", pLevelProfile->m_WeightCapacity);

	UpdateData(FALSE);
//	m_LevelListCtrl.SetCurSel(currentLevel);

	return 0;
}



BOOL CBayProfileCrossbarPage::PreTranslateMessage(MSG* pMsg) 
{
	m_ToolTip.RelayEvent(pMsg);	

	return CPropertyPage::PreTranslateMessage(pMsg);
}

void CBayProfileCrossbarPage::UpdateToolTip(int x, int y, const CString &msg)
{
	if (msg == "")
		m_ToolTip.Activate(FALSE);
	else {
		m_ToolTip.Activate(TRUE);
		m_ToolTipText = msg;
		m_ToolTip.UpdateTipText(m_ToolTipText, GetDlgItem(IDC_CROSSBAR_BUTTON), 0);
	}

	m_ToolTip.SendMessage(TTM_TRACKPOSITION, 0, (LPARAM)MAKELPARAM(x, y));
}

BOOL CBayProfileCrossbarPage::Validate()
{
	// Shoudn't need to check here because we already checked before adding a level
	m_pBayProfile->ResetLocationSizes();
	
	return TRUE;
}



int CBayProfileCrossbarPage::GetCurSel()
{
	return m_LevelListCtrl.GetCurSel();
}

void CBayProfileCrossbarPage::OnAdd(double height, double thickness) 
{
	CTemporaryResourceOverride tro;
	CBayProfileCrossbarProperties dlg;
	dlg.m_pBayProfile = m_pBayProfile;
	int rc;

	if (height > 0)
		dlg.m_Position = height;

	if (thickness > 0)
		dlg.m_Thickness = thickness;

	dlg.m_CurrentLevel = -1;

	try {
		rc = dlg.DoModal();
	}
	catch (...) {
		utilityHelper.ProcessError("Error displaying Add Crossbar window.");
		return;
	}

	if (rc == IDCANCEL)
		return;

	// Assume the properties dialog did all of the overlap and error checking
	// Find the position in the level profile list
	for (int i=0; i < m_pBayProfile->m_LevelProfileList.GetSize(); ++i) {
		if (m_pBayProfile->m_LevelProfileList[i]->m_Coordinates.m_Z == dlg.m_Position)
			return;
		if (m_pBayProfile->m_LevelProfileList[i]->m_Coordinates.m_Z > dlg.m_Position)
			break;
	}


	CLevelProfile *pLevelProfile = new CLevelProfile();
	m_pBayProfile->m_LevelProfileList.InsertAt(i, pLevelProfile);
	pLevelProfile->m_BayProfileDBId = m_pBayProfile->m_BayProfileDBId;
	pLevelProfile->m_Baytype = m_pBayProfile->m_BayType;
	pLevelProfile->m_Coordinates.m_Z = dlg.m_Position;
	pLevelProfile->m_Thickness = dlg.m_Thickness;
	pLevelProfile->m_Clearance = dlg.m_Clearance;
	pLevelProfile->m_Overhang = dlg.m_Overhang;
	pLevelProfile->m_IsBarHidden = dlg.m_Hidden;
	pLevelProfile->m_WeightCapacity = dlg.m_WeightCapacity;
	
	if (pLevelProfile->m_Baytype == BAYTYPE_CASEFLOW ||
		pLevelProfile->m_Baytype == BAYTYPE_PALLETFLOW)
		pLevelProfile->m_FlowDifference = m_pBayProfile->m_FlowDifference;
	else
		pLevelProfile->m_FlowDifference = 0;

	CBayProfileCrossbarInfo info;
	info.m_Clearance = pLevelProfile->m_Clearance;
	info.m_Height = pLevelProfile->m_Coordinates.m_Z;
	info.m_IsHidden = pLevelProfile->m_IsBarHidden;
	info.m_IsSelected = FALSE;
	info.m_LocationCount = 0;
	info.m_LocationSpace = 0;
	info.m_Thickness = pLevelProfile->m_Thickness;;
	m_CrossbarButton.m_CrossbarList.InsertAt(i, info);

	RebuildLevelList();

	//OnSelectLevel(i, 0);
	m_CrossbarButton.SelectCrossbar(i);
//	m_CrossbarButton.Invalidate(TRUE);
	
}

void CBayProfileCrossbarPage::OnEdit() 
{
	int curSel = GetCurSel();
	if (curSel < 0) {
		AfxMessageBox("Please select a crossbar to edit by clicking on it "
			"or by choosing from the dropdown list.");
		return;
	}

	CTemporaryResourceOverride tro;
	CBayProfileCrossbarProperties dlg;
	dlg.m_pBayProfile = m_pBayProfile;

	CLevelProfile *pLevelProfile = m_pBayProfile->m_LevelProfileList[curSel];
	dlg.m_Clearance = pLevelProfile->m_Clearance;
	dlg.m_Overhang = pLevelProfile->m_Overhang;
	dlg.m_Position = pLevelProfile->m_Coordinates.m_Z;
	dlg.m_Hidden = pLevelProfile->m_IsBarHidden;
	dlg.m_Thickness = pLevelProfile->m_Thickness;
	dlg.m_WeightCapacity = pLevelProfile->m_WeightCapacity;
	dlg.m_CurrentLevel = curSel;

	int rc;
	try {
		rc = dlg.DoModal();
	}
	catch (...) {
		utilityHelper.ProcessError("Error displaying Edit Crossbar window.");
		return;
	}

	if (rc == IDCANCEL)
		return;

	// Assume the properties dialog did all of the overlap and error checking
	pLevelProfile->m_Coordinates.m_Z = dlg.m_Position;
	pLevelProfile->m_Thickness = dlg.m_Thickness;
	pLevelProfile->m_Clearance = dlg.m_Clearance;
	pLevelProfile->m_Overhang = dlg.m_Overhang;
	pLevelProfile->m_IsBarHidden = dlg.m_Hidden;
	pLevelProfile->m_WeightCapacity = dlg.m_WeightCapacity;

	CBayProfileCrossbarInfo &info = m_CrossbarButton.m_CrossbarList[curSel];
	info = m_CrossbarButton.m_CrossbarList[curSel];
	info.m_Clearance = pLevelProfile->m_Clearance;
	info.m_Height = pLevelProfile->m_Coordinates.m_Z;
	info.m_IsHidden = pLevelProfile->m_IsBarHidden;
	info.m_IsSelected = FALSE;		// we'll select it below
	info.m_LocationCount = 0;
	info.m_LocationSpace = 0;
	info.m_Thickness = pLevelProfile->m_Thickness;;

	RebuildLevelList();

	//OnSelectLevel(curSel, 0);
	m_CrossbarButton.SelectCrossbar(curSel);
	//m_CrossbarButton.Invalidate(TRUE);	
}

void CBayProfileCrossbarPage::RebuildLevelList()
{
	int curSel = m_LevelListCtrl.GetCurSel();

	m_LevelListCtrl.ResetContent();
	for (int i=0; i < m_pBayProfile->m_LevelProfileList.GetSize(); ++i) {
		m_pBayProfile->m_LevelProfileList[i]->m_Description.Format("%d", i+1);
		m_pBayProfile->m_LevelProfileList[i]->m_RelativeLevel = i+1;
		CString temp;
		if (i == 0)
			temp = "Floor";
		else
			temp.Format("Level: %d - Position: %.0f", i+1, m_pBayProfile->m_LevelProfileList[i]->m_Coordinates.m_Z);
		int nItem = m_LevelListCtrl.AddString(temp);
		//m_LevelListCtrl.SetItemData(nItem, m_pBayProfile->m_LevelProfileList[i]->m_Coordinates.m_Z);
	}

	if (curSel >= m_LevelListCtrl.GetCount())
		curSel = m_LevelListCtrl.GetCount()-1;

	m_LevelListCtrl.SetCurSel(curSel);

	CRect r;
	m_LevelListCtrl.GetWindowRect(&r);
	m_LevelListCtrl.SetWindowPos(NULL, 0, 0, r.Width(), 
		r.Height()*(m_pBayProfile->m_LevelProfileList.GetSize()+1), SWP_NOMOVE|SWP_NOZORDER);

}

void CBayProfileCrossbarPage::OnContextMenu(CWnd* pWnd, CPoint point) 
{
	if (pWnd != &m_CrossbarButton)
		return;

	CMenu menu;
	menu.LoadMenu(IDR_GENERIC_MENU);

	CPoint pt(point);
	m_CrossbarButton.ScreenToClient(&pt);
	
	int curSel = m_CrossbarButton.GetSelectedLevel(pt);
	if (curSel < 0) {
		menu.GetSubMenu(0)->ModifyMenu(0, MF_BYPOSITION|MF_STRING, ID_GENERIC_ADD, "&New");
		menu.GetSubMenu(0)->DeleteMenu(1, MF_BYPOSITION); //|MF_STRING|MF_GRAYED, ID_GENERIC_PROPERTIES, "&Properties");
		menu.GetSubMenu(0)->DeleteMenu(1, MF_BYPOSITION); //|MF_STRING|MF_GRAYED, ID_GENERIC_DELETE, "&Delete");
	}
	else {
		menu.GetSubMenu(0)->DeleteMenu(0, MF_BYPOSITION); //|MF_STRING|MF_GRAYED, ID_GENERIC_ADD, "&New");
		menu.GetSubMenu(0)->ModifyMenu(0, MF_BYPOSITION|MF_STRING, ID_GENERIC_PROPERTIES, "&Properties");
		if (curSel == 0)
			menu.GetSubMenu(0)->DeleteMenu(1, MF_BYPOSITION);
		else
			menu.GetSubMenu(0)->ModifyMenu(1, MF_BYPOSITION|MF_STRING, ID_GENERIC_DELETE, "&Delete");
	}

	menu.GetSubMenu(0)->TrackPopupMenu(TPM_LEFTALIGN|TPM_RIGHTBUTTON, point.x, point.y, this);	
}

void CBayProfileCrossbarPage::OnMenuAdd() 
{

	CPoint pt;
	GetCursorPos(&pt);
//	ClientToScreen(&pt);
	m_CrossbarButton.ScreenToClient(&pt);
	double height = m_CrossbarButton.GetBayHeightFromPoint(pt);
	int maxThickness;
	if (m_CrossbarButton.CheckForCrossbar((int)height, maxThickness) >= 0)		// shouldn't happen
		return;

	if (maxThickness > 4)
		maxThickness = 4;

	OnAddCrossbar(WPARAM(height), LPARAM(maxThickness));
	
}

void CBayProfileCrossbarPage::OnAddButton() 
{
	OnAddCrossbar(0, 4);	
}

afx_msg LRESULT CBayProfileCrossbarPage::OnView(WPARAM wParam, LPARAM lParam)
{
	OnEdit();

	return 0;
}

BOOL CBayProfileCrossbarPage::OnHelpInfo(HELPINFO* pHelpInfo) 
{
	helpService.ShowFieldHelp(IDD, pHelpInfo->iCtrlId);
	return FALSE;

}

BOOL CBayProfileCrossbarPage::OnNotify(WPARAM wParam, LPARAM lParam, LRESULT* pResult) 
{
	NMHDR* pNMHDR = (NMHDR*)lParam;

	if (pNMHDR->code == PSN_HELP) {
		helpService.ShowScreenHelp(IDD);
		return TRUE;
	}
	
	return CPropertyPage::OnNotify(wParam, lParam, pResult);
}
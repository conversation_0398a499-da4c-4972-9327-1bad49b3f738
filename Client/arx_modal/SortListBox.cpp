// SortListBox.cpp : implementation file
//

#include "stdafx.h"
#include "modal.h"
#include "SortListBox.h"
#include "ProductGroup.h"

#ifdef _DEBUG
#define new DEBUG_NEW
#undef THIS_FILE
static char THIS_FILE[] = __FILE__;
#endif

/////////////////////////////////////////////////////////////////////////////
// CSortListBox

CSortListBox::CSortListBox()
{
}

CSortListBox::~CSortListBox()
{
}


BEGIN_MESSAGE_MAP(CSortListBox, CListBox)
	//{{AFX_MSG_MAP(CSortListBox)
	//}}AFX_MSG_MAP
END_MESSAGE_MAP()

/////////////////////////////////////////////////////////////////////////////
// CSortListBox message handlers

void CSortListBox::DrawItem(LPDRAWITEMSTRUCT lpDrawItemStruct) 
{
	CDC  dc;
	dc.Attach(lpDrawItemStruct->hDC);
	CString displayTxt;

	displayTxt = m_GetDrawTextFunction((void *)lpDrawItemStruct->itemData);

	ASSERT(lpDrawItemStruct->CtlType == ODT_LISTBOX);
   
	CRect r = lpDrawItemStruct->rcItem;

	CBrush* pTempBrush = NULL;
	CBrush OrigBrush, br;

	dc.Attach(lpDrawItemStruct->hDC);


   COLORREF crOldTextColor = dc.GetTextColor();
   COLORREF crOldBkColor = dc.GetBkColor();

   // If this item is selected, set the background color 
   // and the text color to appropriate values. Also, erase
   // rect by filling it with the background color.
   	r.left += 1;
   if ((lpDrawItemStruct->itemAction | ODA_SELECT) &&
      (lpDrawItemStruct->itemState & ODS_SELECTED))
   {
      dc.SetTextColor(::GetSysColor(COLOR_HIGHLIGHTTEXT));
      dc.SetBkColor(::GetSysColor(COLOR_HIGHLIGHT));
      dc.FillSolidRect(&r, 
         ::GetSysColor(COLOR_HIGHLIGHT));
   }
   else {
      dc.FillSolidRect(&r, crOldBkColor);
   }


   if ((lpDrawItemStruct->itemAction | ODA_FOCUS) &&
      (lpDrawItemStruct->itemState & ODS_FOCUS))
   {
      //CBrush br(RGB(255, 0, 0));
      //dc.FrameRect(&r, &br);

   }


	dc.DrawText(
      displayTxt,
      displayTxt.GetLength(),
      &r,
      DT_SINGLELINE|DT_VCENTER);
 
	dc.SetTextColor(crOldTextColor);
	dc.SetBkColor(crOldBkColor);

	//frontDC.SelectObject(prevPen);
	dc.SelectObject(&OrigBrush);
	dc.Detach();	
}

int CSortListBox::CompareItem(LPCOMPAREITEMSTRUCT lpCompareItemStruct) 
{
	// TODO: Add your code to determine the sorting order of the specified items
	// return -1 = item 1 sorts before item 2
	// return 0 = item 1 and item 2 sort the same
	// return 1 = item 1 sorts after item 2
	return m_CompareFunction((void *)lpCompareItemStruct->itemData1, (void *)lpCompareItemStruct->itemData2);
	//return 0;
}

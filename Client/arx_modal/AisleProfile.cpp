// AisleProfile.cpp: implementation of the CAisleProfile class.
//
//////////////////////////////////////////////////////////////////////

#include "stdafx.h"
#include "modal.h"
#include "AisleProfile.h"
#include "UtilityHelper.h"

#ifdef _DEBUG
#undef THIS_FILE
static char THIS_FILE[]=__FILE__;
#define new DEBUG_NEW
#endif

extern CUtilityHelper utilityHelper;
//////////////////////////////////////////////////////////////////////
// Construction/Destruction
//////////////////////////////////////////////////////////////////////

CAisleProfile::CAisleProfile()
{
	m_AisleProfileDBId = 0;
	m_Description = "";
	m_AisleSpace = 0;
	m_pLeftSideProfile = NULL;
	m_pRightSideProfile = NULL;
	m_LeftSpace = m_RightSpace = 0;
}

CAisleProfile::~CAisleProfile()
{
	if (m_pLeftSideProfile != NULL)
		delete m_pLeftSideProfile;

	if (m_pRightSideProfile != NULL)
		delete m_pRightSideProfile;
}

CAisleProfile& CAisleProfile::operator=(const CAisleProfile &other)
{
	m_AisleProfileDBId = other.m_AisleProfileDBId;
	m_Description = other.m_Description;
	m_AisleSpace = other.m_AisleSpace;
	
	m_LeftSpace = other.m_LeftSpace;
	m_RightSpace = other.m_RightSpace;

	if (m_pLeftSideProfile != NULL) {
		delete m_pLeftSideProfile;
		m_pLeftSideProfile = NULL;
	}

	if (m_pRightSideProfile != NULL) {
		delete m_pRightSideProfile;
		m_pRightSideProfile = NULL;
	}

	if (other.m_pLeftSideProfile != NULL)
		m_pLeftSideProfile = new CSideProfile(*other.m_pLeftSideProfile);

	if (other.m_pRightSideProfile != NULL)
		m_pRightSideProfile = new CSideProfile(*other.m_pRightSideProfile);

	return *this;
}

CAisleProfile::CAisleProfile(const CAisleProfile &other)
{
	m_AisleProfileDBId = other.m_AisleProfileDBId;
	m_Description = other.m_Description;
	m_AisleSpace = other.m_AisleSpace;
	
	m_LeftSpace = other.m_LeftSpace;
	m_RightSpace = other.m_RightSpace;

	if (m_pLeftSideProfile != NULL) {
		delete m_pLeftSideProfile;
		m_pLeftSideProfile = NULL;
	}

	if (m_pRightSideProfile != NULL) {
		delete m_pRightSideProfile;
		m_pRightSideProfile = NULL;
	}

	if (other.m_pLeftSideProfile != NULL)
		m_pLeftSideProfile = new CSideProfile(*other.m_pLeftSideProfile);

	if (other.m_pRightSideProfile != NULL)
		m_pRightSideProfile = new CSideProfile(*other.m_pRightSideProfile);

}


BOOL CAisleProfile::operator==(const CAisleProfile& other)
{
	if (m_AisleProfileDBId != other.m_AisleProfileDBId) return FALSE;
	if (m_Description != other.m_Description) return FALSE;
	if (m_AisleSpace != other.m_AisleSpace) return FALSE;
		
	if (m_LeftSpace != other.m_LeftSpace) return FALSE;
	if (m_RightSpace != other.m_RightSpace) return FALSE;

	if (m_pLeftSideProfile == NULL && other.m_pLeftSideProfile != NULL) return FALSE;
	if (m_pLeftSideProfile != NULL && other.m_pLeftSideProfile == NULL) return FALSE;
	if (m_pLeftSideProfile != NULL)
		if (*m_pLeftSideProfile != *other.m_pLeftSideProfile) return FALSE;

	if (m_pRightSideProfile == NULL && other.m_pRightSideProfile != NULL) return FALSE;
	if (m_pRightSideProfile != NULL && other.m_pRightSideProfile == NULL) return FALSE;
	if (m_pRightSideProfile != NULL)
		if (*m_pRightSideProfile != *other.m_pRightSideProfile) return FALSE;

	return TRUE;
}

int CAisleProfile::Parse(CString &line)
{
	CStringArray strings;

	utilityHelper.ParseString(line, "|", strings);
	for (int i=0; i < strings.GetSize(); ++i) {
		switch (i) {
		case 0:
			m_AisleProfileDBId = atoi(strings[i]);
			break;
		case 1:
			m_Description = strings[i];
			break;
		case 2:
			m_AisleSpace = atof(strings[i]);
			break;
		}
	}

	return 0;

}

int CAisleProfile::Draw(BOOL currentDB, BOOL doSave)
{
	if (! currentDB)
		m_pDatabase = new AcDbDatabase();
	else
		m_pDatabase = acdbCurDwg();

	//DrawAisle();
	
	if (! currentDB)
		delete m_pDatabase;

	return 0;

}

int CAisleProfile::DrawByPosition(AcDbDatabase *pDatabase, const C3DPoint &leftSideCornerPoint, double rotation)
{
	
	m_pDatabase = pDatabase;

	if (m_pLeftSideProfile != NULL) {
		m_pLeftSideProfile->DrawByPosition(pDatabase, leftSideCornerPoint, rotation, 0);
		
		if (m_pRightSideProfile != NULL) {
			C3DPoint rightSideCornerPoint(leftSideCornerPoint);			
			
			//rightSideCornerPoint.m_X += m_pRightSideProfile->CalculateLength();

			rightSideCornerPoint.m_Y -= m_pLeftSideProfile->m_BayProfileList[0]->m_Depth;
			rightSideCornerPoint.m_Y -= m_AisleSpace;
			rightSideCornerPoint.m_Y -= m_pRightSideProfile->m_BayProfileList[0]->m_Depth;

			
			m_pRightSideProfile->DrawByPosition(pDatabase, rightSideCornerPoint, rotation, 1);
		}
	}
	else {
		if (m_pRightSideProfile != NULL) {
			C3DPoint rightSideCornerPoint(leftSideCornerPoint);	

			rightSideCornerPoint.m_Y -= m_AisleSpace;
			rightSideCornerPoint.m_Y -= m_pRightSideProfile->m_BayProfileList[0]->m_Depth;
			
			m_pRightSideProfile->DrawByPosition(pDatabase, rightSideCornerPoint, rotation, 1);
		}
	}
	
	
		
	

	return Acad::eOk;
}

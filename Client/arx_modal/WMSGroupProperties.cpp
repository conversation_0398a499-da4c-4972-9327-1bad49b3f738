// WMSGroupProperties.cpp : implementation file
//

#include "stdafx.h"
#include "modal.h"
#include "WMSGroupProperties.h"
#include "IntegrationDataService.h"
#include "UtilityHelper.h"
#include "AssignConnectionDialog.h"
#include "ControlService.h"
#include "HelpService.h"

#ifdef _DEBUG
#define new DEBUG_NEW
#undef THIS_FILE
static char THIS_FILE[] = __FILE__;
#endif

extern CIntegrationDataService integrationDataService;
extern CUtilityHelper utilityHelper;
extern CControlService controlService;
extern CHelpService helpService;
/////////////////////////////////////////////////////////////////////////////
// CWMSGroupProperties dialog


CWMSGroupProperties::CWMSGroupProperties(CWnd* pParent /*=NULL*/)
	: CDialog(CWMSGroupProperties::IDD, pParent)
{
	//{{AFX_DATA_INIT(CWMSGroupProperties)
	m_Description = _T("");
	m_Name = _T("");
	m_WMSId = _T("");
	m_IdText = _T("");
	//}}AFX_DATA_INIT
	m_IdText = "WMS Id";
}


void CWMSGroupProperties::DoDataExchange(CDataExchange* pDX)
{
	CDialog::DoDataExchange(pDX);
	//{{AFX_DATA_MAP(CWMSGroupProperties)
	DDX_Control(pDX, IDC_WMS_SYSTEM, m_WMSSystemCtrl);
	DDX_Text(pDX, IDC_DESCRIPTION, m_Description);
	DDX_Text(pDX, IDC_NAME, m_Name);
	DDX_Text(pDX, IDC_WMS_ID, m_WMSId);
	//}}AFX_DATA_MAP
}


BEGIN_MESSAGE_MAP(CWMSGroupProperties, CDialog)
	//{{AFX_MSG_MAP(CWMSGroupProperties)
	ON_WM_HELPINFO()
	ON_BN_CLICKED(IDHELP, OnHelp)
	ON_BN_CLICKED(IDC_DEFINE_CONNECTIONS, OnDefineConnections)
	ON_CBN_SELCHANGE(IDC_WMS_SYSTEM, OnSelchangeWmsSystem)
	//}}AFX_MSG_MAP
END_MESSAGE_MAP()

/////////////////////////////////////////////////////////////////////////////
// CWMSGroupProperties message handlers

BOOL CWMSGroupProperties::OnInitDialog() 
{
	CDialog::OnInitDialog();
	
	CStringArray systemList, strings;
	CWaitCursor cwc;

	try {
		integrationDataService.GetExternalSystemList("WMS", systemList);
	}
	catch (...) {
		utilityHelper.ProcessError("Error getting list of external systems.");
	}

	for (int i=0; i < systemList.GetSize(); ++i) {
		strings.RemoveAll();
		utilityHelper.ParseString(systemList[i], "|", strings);
		CString temp;
		temp.Format("%s - %s", strings[1], strings[2]);
		int nItem = m_WMSSystemCtrl.AddString(temp);
		m_WMSSystemCtrl.SetItemData(nItem, atoi(strings[0]));
	}

	CRect r;
	m_WMSSystemCtrl.GetWindowRect(&r);
	m_WMSSystemCtrl.SetWindowPos(NULL, 0, 0, r.Width(), r.Height()*5, SWP_NOMOVE|SWP_NOZORDER);

	if (m_Group.m_WMSGroupDBId > 0) {	// Properties
		m_Name = m_Group.m_Name;
		m_Description = m_Group.m_Description;
		m_WMSId = m_Group.m_WMSId;

		for (i=0; i < m_WMSSystemCtrl.GetCount(); ++i) {
			if (m_WMSSystemCtrl.GetItemData(i) == (unsigned long)m_Group.m_ExternalSystemDBId) {
				m_WMSSystemCtrl.SetCurSel(i);
				break;
			}
		}
	}

	CString temp;
	m_WMSSystemCtrl.GetWindowText(temp);
	if (temp.Find("5.3") >= 0)
		m_IdText = "DC Id";
	else if (temp.Find("3.6") >= 0)
		m_IdText = "WMS Facility Id";

	

	if (m_Group.m_ConnectionList.GetSize() == 0) {
		try {
			integrationDataService.LoadGroupConnectionList(m_Group);
		}
		catch (...) {
			controlService.Log("Error loading connections for WMS Facility.",
				"Generic exception in LoadGroupConnectionList.\n");
		}
	}


	CStatic *pStatic = (CStatic *)GetDlgItem(IDC_WMSID_STATIC);
	pStatic->SetWindowText(m_IdText + ":");

	UpdateData(FALSE);

	return TRUE;  // return TRUE unless you set the focus to a control
	              // EXCEPTION: OCX Property Pages should return FALSE
}

void CWMSGroupProperties::OnOK() 
{
	UpdateData(TRUE);

	if (m_Name == "") {
		AfxMessageBox("Please enter a name for the WMS Facility.");
		CEdit *pEdit = (CEdit *)GetDlgItem(IDC_NAME);
		pEdit->SetSel(0,-1);
		pEdit->SetFocus();
		return;
	}

	if (m_Description == "") {
		AfxMessageBox("Please enter a description for the WMS Facility.");
		CEdit *pEdit = (CEdit *)GetDlgItem(IDC_DESCRIPTION);
		pEdit->SetSel(0,-1);
		pEdit->SetFocus();
		return;
	}

	if (m_WMSId == "") {
		CString temp;
		temp.Format("Please enter a value for %s", m_IdText);
		CEdit *pEdit = (CEdit *)GetDlgItem(IDC_WMS_ID);
		pEdit->SetSel(0,-1);
		pEdit->SetFocus();
		return;
	}

	if (m_WMSSystemCtrl.GetCurSel() == CB_ERR) {
		AfxMessageBox("Please select a WMS System.");
		m_WMSSystemCtrl.ShowDropDown(TRUE);
		m_WMSSystemCtrl.SetFocus();
		return;
	}

	m_WMSSystemCtrl.GetWindowText(m_Group.m_ExternalSystemName);
	m_Group.m_ExternalSystemDBId = m_WMSSystemCtrl.GetItemData(m_WMSSystemCtrl.GetCurSel());

	// Populate the WMS with the current properties
	m_Group.m_Name = m_Name;
	m_Group.m_Description = m_Description;
	m_Group.m_WMSId = m_WMSId;

	// Save the CWMSGroup to the database
	try {
		if (integrationDataService.WMSGroupExists(m_Group.m_Name, m_Group.m_WMSGroupDBId)) {
			AfxMessageBox("A WMS Facility with the same name already exists.");
			CEdit *pEdit = (CEdit *)GetDlgItem(IDC_NAME);
			pEdit->SetSel(0,-1);
			pEdit->SetFocus();
			return;
		}

		if (integrationDataService.WMSGroupIdExists(m_Group.m_WMSId, m_Group.m_WMSGroupDBId)) {
			CString temp;
			temp.Format("A WMS Facility with the same %s already exists.", m_IdText);
			AfxMessageBox(temp);
			CEdit *pEdit = (CEdit *)GetDlgItem(IDC_WMS_ID);
			pEdit->SetSel(0,-1);
			pEdit->SetFocus();
			return;
		}

		integrationDataService.StoreWMSGroup(m_Group);
	}
	catch (...) {
		utilityHelper.ProcessError("Error storing WMS Facility to database.");
		return;
	}

	CDialog::OnOK();

}

BOOL CWMSGroupProperties::OnHelpInfo(HELPINFO* pHelpInfo) 
{
	helpService.ShowFieldHelp(IDD, pHelpInfo->iCtrlId);

	return FALSE;

}

void CWMSGroupProperties::OnHelp() 
{
	helpService.ShowScreenHelp(IDD);

	return;

}

void CWMSGroupProperties::OnDefineConnections() 
{
	CAssignConnectionDialog dlg;

	dlg.m_pGroup = &m_Group;

	try {
		dlg.DoModal();
	}
	catch (...) {
		controlService.Log("Error displaying Assign Connection dialog.","Generic exception in CAssignConnectionDialog.\n");
	}

}

void CWMSGroupProperties::OnSelchangeWmsSystem() 
{
	CString temp;
	m_WMSSystemCtrl.GetWindowText(temp);
	if (temp.Find("5.3") >= 0)
		m_IdText = "DC Id";
	else if (temp.Find("3.6") >= 0)
		m_IdText = "WMS Facility Id";

	CStatic *pStatic = (CStatic *)GetDlgItem(IDC_WMSID_STATIC);
	pStatic->SetWindowText(m_IdText + ":");
}

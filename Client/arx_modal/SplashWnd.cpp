// SplashWnd.cpp : implementation file
//

#include "stdafx.h"
#include "modal.h"
#include "SplashWnd.h"
#include <aced.h>
#include <adscodes.h>

#ifdef _DEBUG
#define new DEBUG_NEW
#undef THIS_FILE
static char THIS_FILE[] = __FILE__;
#endif

/////////////////////////////////////////////////////////////////////////////
// CSplashWnd

CSplashWnd::CSplashWnd()
{
}

CSplashWnd::~CSplashWnd()
{
}


BEGIN_MESSAGE_MAP(CSplashWnd, CWnd)
	//{{AFX_MSG_MAP(CSplashWnd)
	ON_WM_PAINT()
	ON_WM_LBUTTONDOWN()
	ON_WM_TIMER()
	ON_WM_CREATE()
	ON_MESSAGE( WM_ACAD_KEEPFOCUS, onAcadKeepFocus )
	ON_WM_KEYDOWN()
	//}}AFX_MSG_MAP
END_MESSAGE_MAP()


/////////////////////////////////////////////////////////////////////////////
// CSplashWnd message handlers

afx_msg LONG CSplashWnd::onAcadKeepFocus( UINT, LONG )
{
	return TRUE;

}

void CSplashWnd::OnPaint() 
{

	CRect r;

	CPaintDC dc(this); // device context for painting
	
	//CWnd::OnPaint();

	CBitmap bmp;
	if (bmp.LoadBitmap(IDB_SPLASH))
	{
		// Get the size of the bitmap
		BITMAP bmpInfo;
		bmp.GetBitmap(&bmpInfo);
		this->GetWindowRect(&r);
		this->SetWindowPos(NULL, 0, 0, bmpInfo.bmWidth, bmpInfo.bmHeight, SWP_NOMOVE|SWP_NOZORDER);
		this->CenterWindow();
		this->GetWindowRect(&r);
		
		// Create an in-memory DC compatible with the
		// display DC we're using to paint
		CDC dcMemory;
		dcMemory.CreateCompatibleDC(&dc);
		
		// Select the bitmap into the in-memory DC
		CBitmap* pOldBitmap = dcMemory.SelectObject(&bmp);
		
		// Find a centerpoint for the bitmap in the client area
		CRect rect;
		GetClientRect(&rect);
		int nX = rect.left + (rect.Width() - bmpInfo.bmWidth) / 2;
		int nY = rect.top + (rect.Height() - bmpInfo.bmHeight) / 2;
		
		// Copy the bits from the in-memory DC into the on-
		// screen DC to actually do the painting. Use the centerpoint
		// we computed for the target offset.
		dc.BitBlt(nX, nY, bmpInfo.bmWidth, bmpInfo.bmHeight, &dcMemory, 
			0, 0, SRCCOPY);
		
		dcMemory.SelectObject(pOldBitmap);
	}
}


void CSplashWnd::OnLButtonDown(UINT nFlags, CPoint point) 
{
	
	KillTimer(m_TimerId);

	CWnd::OnLButtonDown(nFlags, point);
}

void CSplashWnd::OnTimer(UINT nIDEvent) 
{

	CRect r;

	GetWindowRect(&r);
	
	KillTimer(nIDEvent);
	InvalidateRect(&r, FALSE);
	this->DestroyWindow();

	


}

void CSplashWnd::PostNcDestroy() 
{
	acedUpdateDisplay();

	delete this;	
}



int CSplashWnd::OnCreate(LPCREATESTRUCT lpCreateStruct) 
{
	if (CWnd::OnCreate(lpCreateStruct) == -1)
		return -1;
	
	m_TimerId = SetTimer(1, 3000, 0);
	
	return 0;
}

void CSplashWnd::OnKeyDown(UINT nChar, UINT nRepCnt, UINT nFlags) 
{
	
	CWnd::OnKeyDown(nChar, nRepCnt, nFlags);
}

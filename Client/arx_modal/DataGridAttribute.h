// DataGridAttribute.h: interface for the CDataGridAttribute class.
//
//////////////////////////////////////////////////////////////////////
#include "constants.h"

#if !defined(AFX_DATAGRIDATTRIBUTE_H__F578C5B3_CAE9_11D4_9EC1_00C04FAC149C__INCLUDED_)
#define AFX_DATAGRIDATTRIBUTE_H__F578C5B3_CAE9_11D4_9EC1_00C04FAC149C__INCLUDED_

#if _MSC_VER > 1000
#pragma once
#endif // _MSC_VER > 1000

const int AT_FIXED = 0;
const int AT_VAR = 1;

class CDataGridAttribute : public CObject
{
public:
	CDataGridAttribute();
	virtual ~CDataGridAttribute();
	CDataGridAttribute& operator=(const CDataGridAttribute &other);

	int m_Type;		// Fixed or Variable
	CString m_InitialValue;		// Fixed -> the display value; Variable -> the default
	CString m_Value;			// Variable only -> the final value;s
								// If list, the number of the selected list element
	int m_DataType;				// Variable only -> Float, Int, String, List
	double m_Min;				// Variable, Int/Float only
	double m_Max;				// Variable, Int/Float only
	CStringArray m_ListValues;	// Variable, list only
	BOOL m_ReadOnly;			// Variable
	CString m_HelpTopic;
	int m_AttributeID;			// For UDFs only
	CString m_Format;			// printf-style format string

};

#endif // !defined(AFX_DATAGRIDATTRIBUTE_H__F578C5B3_CAE9_11D4_9EC1_00C04FAC149C__INCLUDED_)

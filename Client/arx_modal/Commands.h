#ifndef COMMANDS_H
#define COMMANDS_H

#include <aced.h>
#include <adscodes.h>

class CCommands  
{
public:
	static void RegisterCommands(int mode);
	static void UnRegisterAllCommands();
	static void RegisterSavedFacilityCommands();
	static void RegisterNewFacilityCommands();
	static void RegisterCommonCommands();
	static void RegisterCommands();
	CCommands();
	virtual ~CCommands();

	typedef enum {
		anyMode,
		wizardMode,
		newFacilityMode,
		openFacilityMode,
		savedFacilityMode
	} enumRequiredMode;

};

#endif
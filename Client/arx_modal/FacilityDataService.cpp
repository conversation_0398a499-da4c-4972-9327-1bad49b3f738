// FacilityDataService.cpp: implementation of the CFacilityDataService class.
//
//////////////////////////////////////////////////////////////////////

#include "stdafx.h"
#include "modal.h"
#include "FacilityDataService.h"
#include "DataAccessService.h"
#include "ForteService.h"
#include "TreeElement.h"
#include "UDF.h"
#include "BTreeHelper.h"
#include "UtilityHelper.h"
#include "ControlService.h"

#include "base.h"
#include "MiscClasses.h"
#include "SLOTSessionMgr.h"
#include "SrvcObjs.h"

#ifdef _DEBUG
#undef THIS_FILE
static char THIS_FILE[]=__FILE__;
#define new DEBUG_NEW
#endif

//////////////////////////////////////////////////////////////////////
// Construction/Destruction
//////////////////////////////////////////////////////////////////////

extern CForteService forteService;
extern TreeElement changesTree;
extern CDataAccessService dataAccessService;
extern CUtilityHelper utilityHelper;
extern CControlService controlService;
extern CBTreeHelper bTreeHelper;

CFacilityDataService::CFacilityDataService()
{

}

CFacilityDataService::~CFacilityDataService()
{

}


int CFacilityDataService::GetSectionsByFacility(long facilityDBID, CStringArray &sectionList)
{
	CString sql;

	sql.Format("select dbsection.dbsectionid, dbsection.description "
		"from dbsection "
		"where dbfacilityid = %d "
		"order by dbsection.description, dbsection.dbsectionid", facilityDBID);

	CStringArray results;
	dataAccessService.ExecuteQuery("GetSectionsByFacility", sql, results);
	
	// Eliminate those that have been deleted since the last save
	int dummy;
	for (int i=0; i < results.GetSize(); ++i) {
		int dbid = atoi(results[i].Left(results[i].Find("|")));
		if (changesTree.DeletedAisleMap.Lookup(dbid, dummy))
			continue;
		sectionList.Add(results[i]);
	}

	return sectionList.GetSize();
}


int CFacilityDataService::GetAislesBySection(long sectionDBID, CStringArray &aisleList)
{
	CString sql;

	sql.Format("select dbaisle.dbaisleid, dbaisle.description "
		"from dbaisle "
		"where dbsectionid = %d "
		"order by dbaisle.description, dbaisle.dbaisleid", sectionDBID);

	CStringArray results;
	dataAccessService.ExecuteQuery("GetAislesBySection", sql, results);

	int dummy;
	for (int i=0; i < results.GetSize(); ++i) {
		int dbid = atoi(results[i].Left(results[i].Find("|")));
		if (changesTree.DeletedAisleMap.Lookup(dbid, dummy))
			continue;
		aisleList.Add(results[i]);
	}

	return aisleList.GetSize();

}

int CFacilityDataService::GetSidesByAisle(long aisleDBID, CStringArray &sideList)
{
	CString sql;
	
	sql.Format("select dbside.dbsideid, dbside.description "
		"from dbside "
		"where dbaisleid = %d "
		"order by dbside.description, dbside.dbsideid ", aisleDBID);

	CStringArray results;

	dataAccessService.ExecuteQuery("GetSidesByAisle", sql, results);

	// Eliminate those that have been deleted since the last save
	int dummy;
	for (int i=0; i < results.GetSize(); ++i) {
		int dbid = atoi(results[i].Left(results[i].Find("|")));
		if (changesTree.DeletedSideMap.Lookup(dbid, dummy))
			continue;
		sideList.Add(results[i]);
	}

	return sideList.GetSize();

}

int CFacilityDataService::GetBaysBySide(long sideDBID, CStringArray &bayList)
{
	CString sql;
	
	sql.Format("select dbbay.dbbayid, dbbay.description "
		"from dbbay "
		"where dbsideid = %d "
		"order by dbbay.xcoordinate, dbbay.dbbayid", sideDBID);
	
	CStringArray results;
	dataAccessService.ExecuteQuery("GetBaysBySide", sql, results);
	// Eliminate those that have been deleted since the last save
	int dummy;
	for (int i=0; i < results.GetSize(); ++i) {
		int dbid = atoi(results[i].Left(results[i].Find("|")));
		if (changesTree.DeletedAisleMap.Lookup(dbid, dummy))
			continue;
		bayList.Add(results[i]);
	}

	return bayList.GetSize();

}

int CFacilityDataService::GetLevelsByBay(long bayDBID, CStringArray &levelList)
{
	CString sql;

	sql.Format("select dblevel.dblevelid, dblevel.description "
		"from dblevel, dblevelprofile "
		"where dbbayid = %d "
		"and dblevel.dblevelprofileid = dblevelprofile.dblevelprofileid "
		"order by dblevelprofile.relativelevel", bayDBID);

	return dataAccessService.ExecuteQuery("GetLevelsByBay", sql, levelList);

}

int CFacilityDataService::GetLocationsByLevel(long levelDBID, CStringArray &locList)
{
	CString sql;

	sql.Format("select dblocation.dblocationid, dblocation.description "
		"from dblocation, dblocationprof "
		"where dblocation.dblevelid = %d "
		"and dblocationprof.dblocationprofid = dblocation.dblocationprofid "
		"order by dblocationprof.xcoordinate", levelDBID);

	return dataAccessService.ExecuteQuery("GetLocationsByLevel", sql, locList);

}

int CFacilityDataService::GetMaxRelativeLevelBySection(long sectionDBID)
{
	CString sql;
	CStringArray results;
	int rc;

	sql.Format("select max(relativelevel) "
		"from dbaisle, dbside, dbbay, dblevelprofile "
		"where dbaisle.dbsectionid = %d "
		"and dbaisle.dbaisleid = dbside.dbaisleid "
		"and dbside.dbsideid = dbbay.dbsideid "
		"and dblevelprofile.dbbayprofileid = dbbay.dbbayprofileid", sectionDBID);

	rc = dataAccessService.ExecuteQuery("GetMaxRelativeLevelBySection", sql, results);

	if (rc < 0)
		return rc;
	else {
		if (results[0].Right(1) == "|")
			results[0].Delete(results[0].GetLength()-1, 1);
		return atoi(results[0]);
	}

}


int CFacilityDataService::GetMaxRelativeLevelByAisle(long aisleDBID)
{
	CString sql;
	CStringArray results;
	int rc;

	sql.Format("select max(relativelevel) "
		"from dbside, dbbay, dblevelprofile "
		"where dbside.dbaisleid = %d "
		"and dbside.dbsideid = dbbay.dbsideid "
		"and dblevelprofile.dbbayprofileid = dbbay.dbbayprofileid", aisleDBID);

	rc = dataAccessService.ExecuteQuery("GetMaxRelativeLevelByAisle", sql, results);

	if (rc < 0)
		return rc;
	else {
		if (results[0].Right(1) == "|")
			results[0].Delete(results[0].GetLength()-1, 1);
		return atoi(results[0]);
	}

}


int CFacilityDataService::GetMaxRelativeLevelBySide(long sideDBID)
{
	CString sql;
	CStringArray results;
	int rc;

	sql.Format("select max(relativelevel) "
		"from dbbay, dblevelprofile "
		"where dbbay.dbsideid = %d "
		"and dblevelprofile.dbbayprofileid = dbbay.dbbayprofileid", sideDBID);

	rc = dataAccessService.ExecuteQuery("GetMaxRelativeLevelBySide", sql, results);

	if (rc < 0)
		return rc;
	else {
		if (results[0].Right(1) == "|")
			results[0].Delete(results[0].GetLength()-1, 1);
		return atoi(results[0]);
	}

}


int CFacilityDataService::GetMaxRelativeLevelByBay(long bayDBID)
{
	CString sql;
	CStringArray results;
	int rc;

	sql.Format("select max(relativelevel) "
		"from dbbay, dblevelprofile "
		"where dbbay.dbbayid = %d "
		"and dblevelprofile.dbbayprofileid = dbbay.dbbayprofileid", bayDBID);

	rc = dataAccessService.ExecuteQuery("GetMaxRelativeLevelByBay", sql, results);

	if (rc < 0)
		return rc;
	else {
		if (results[0].Right(1) == "|")
			results[0].Delete(results[0].GetLength()-1, 1);
		return atoi(results[0]);
	}

}

int CFacilityDataService::GetAvailableAttributes(CString elementType, CSsaStringArray & AttributeList) 
{
	CString tempString;
	CSsaStringArray tempSendArray;
	CSsaStringArray tempRecvArray;
	CString sendString;
	int i = 0;

	/////////////////////////////////////////////////////////////
	// Build the data and send to Forte connection
	/////////////////////////////////////////////////////////////
#if 0
	int tempInt;
	sendString = "<SIO>SLOTSocketString\n";
	tempSendArray.Add(sendString);
	sendString = "<SAI>" + elementType + "\n";
	tempSendArray.Add(sendString);
	sendString = "<EIO>SLOTSocketString\n";
	tempSendArray.Add(sendString);

	forteService.SendToForteConnection(tempSendArray,tempRecvArray,
		CString("SLOTSocketString"), 2040);

	for (i=0; i < tempRecvArray.GetSize(); i++) {
		if ( tempRecvArray[i].Find("<SAI>") != -1 ) {
			tempString = tempRecvArray[i].Mid(5);
			tempInt = tempString.GetLength();
			if (tempString.GetAt(tempInt-1) == '\n' ) {
				tempString.SetAt(tempInt-1,' ');
				tempString.TrimRight();
			}
			AttributeList.Add(tempString);
		}
	}
#else
	///OLD CODE: SessionMgrSO.GetAvailableAttributesHelper( (LPCTSTR)ClassNameStr(SessionMgrSO) );
 	
	CListstringPtr results = getSessionMgrSO()->GetAvailableAttributesHelper( (LPCTSTR)elementType );
	for (i=0; i < results->GetCount(); i++)
	{
		AttributeList.Add((results->GetAt(results->FindIndex(i))).c_str());	
	}
#endif
	if ( AttributeList.GetSize() > 0 )
		return 0;
	else 
		return -1;
}

int CFacilityDataService::GetAvailableAttributesByFacID(CString elementType, int facilityID,
									  CSsaStringArray & AttributeList) 
{
#if 0
	CString tempString;
	int tempInt;
	CSsaStringArray tempSendArray;
	CSsaStringArray tempRecvArray;
	CString sendString;
	int i = 0;

	/////////////////////////////////////////////////////////////
	// Build the data and send to Forte connection
	/////////////////////////////////////////////////////////////
	sendString = "<SIO>SLOTSocketString\n";
	tempSendArray.Add(sendString);
	sendString = "<SAI>" + elementType + "\n";
	tempSendArray.Add(sendString);
	sendString.Format("<SAI>%d\n",facilityID);
	tempSendArray.Add(sendString);
	sendString = "<EIO>SLOTSocketString\n";
	tempSendArray.Add(sendString);

	forteService.SendToForteConnection(tempSendArray,tempRecvArray,
		CString("SLOTSocketString"), 5080);

	for (i=0; i < tempRecvArray.GetSize(); i++) {
		if ( tempRecvArray[i].Find("<SAI>") != -1 ) {
			tempString = tempRecvArray[i].Mid(5);
			tempInt = tempString.GetLength();
			if (tempString.GetAt(tempInt-1) == '\n' ) {
				tempString.SetAt(tempInt-1,' ');
				tempString.TrimRight();
			}
			AttributeList.Add(tempString);
		}
	}
#else
	string elementTypeTmp = (LPCTSTR)elementType;
	CListstringPtr res = getSessionMgrSO()->GetAvailableAttributesByFacilityHelper(elementTypeTmp, facilityID);

	POSITION posSL = res->GetHeadPosition();
	for (int i=0; i < res->GetCount(); i++)
	{
		AttributeList.Add((res->GetNext(posSL)).c_str());
	}
#endif

	if ( AttributeList.GetSize() > 0 )
		return 0;
	else 
		return -1;
}


int CFacilityDataService::GetBayHandlesByAisleId(int aisleId, CStringArray &bayHandles)
{
	CString sql;
	
	sql.Format("select b.dbbayid, b.acadhandle from dbside si, dbbay b "
		"where si.dbaisleid = %d "
		"and b.dbsideid = si.dbsideid",
		aisleId);
	
	CStringArray results;
	dataAccessService.ExecuteQuery("GetBayHandlesByAisleId", sql, results, TRUE);
	for (int i=0; i < results.GetSize(); ++i) {
		int bayDBId = atoi(results[i].Left(results[i].Find("|")));
		int dummy;
		// Make sure the bay hasn't been deleted since we last saved
		if (changesTree.DeletedBayMap.Lookup(bayDBId, dummy))
			continue;

		CString handle = results[i].Mid(results[i].Find("|"));
		if (handle.Left(1) == "|")  // MFS 18Feb06 Not removing the leading pipe delimiter
			handle.Delete(0, 1);    // causes the calling function to misidentify the object.
		bayHandles.Add(handle);
	}

	return bayHandles.GetSize();

}


int CFacilityDataService::GetBayHandlesByProfile(int bayProfileDBId, CStringArray &bayHandles)
{
	CString sql;

	sql.Format("select b.dbbayid, b.acadhandle "
		"from dbbay b, dbside si, dbaisle a, dbsection se "
		"where b.dbbayprofileid = %d "
		"and si.dbsideid = b.dbsideid "
		"and a.dbaisleid = si.dbaisleid "
		"and se.dbsectionid = a.dbsectionid "
		"and se.dbfacilityid = %d", bayProfileDBId, changesTree.elementDBID);

	CStringArray results;
	dataAccessService.ExecuteQuery("GetBayHandlesByProfile", sql, results, TRUE);
	for (int i=0; i < results.GetSize(); ++i) {
		int bayDBId = atoi(results[i].Left(results[i].Find("|")));
		int dummy;
		// Make sure the bay hasn't been deleted since we last saved
		if (changesTree.DeletedBayMap.Lookup(bayDBId, dummy))
			continue;

		CString handle = results[i].Mid(results[i].Find("|")+1);
		bayHandles.Add(handle);
	}
			

	return bayHandles.GetSize();

}

int CFacilityDataService::GetBayProfileNameListByFacility(CStringArray &bayList)
{
	CString queryText;

	// This may return invalid bay profiles if they have deleted all of a bay profile type
	// since last saving
	queryText.Format("select unique bp.dbbayprofileid, bp.description, bp.baytype "
		"from dbbay b, dbbayprofile bp, dbside si, dbaisle a, dbsection se "
		"where b.dbbayprofileid = bp.dbbayprofileid "
		"and si.dbsideid = b.dbsideid "
		"and a.dbaisleid = si.dbaisleid "
		"and se.dbsectionid = a.dbsectionid "
		"and se.dbfacilityid = %d "
		"order by bp.description", changesTree.elementDBID);
			

	return dataAccessService.ExecuteQuery("GetBayProfileNameListByFacility", queryText, bayList);

}

int CFacilityDataService::GetBayHandleList(CStringArray &handleList)
{
	CString query;

	query.Format("select b.acadhandle "
		"from dbbay, dbaisle, dbsection "
		"where dbsection.dbfacilityid = %d "
		"and dbsection.dbsectionid = dbaisle.dbsectionid "
		"and dbaisle.dbaisleid = dbbay.dbaisleid", changesTree.elementDBID);

	return dataAccessService.ExecuteQuery("GetBayHandleList", query, handleList);

}



double CFacilityDataService::GetBayMaxWeight(int bayDBID)
{
	CString query;
	CStringArray results;

	/////////////////////////////////////////////////////////////
	// build the data to send to the Forte connection server
	/////////////////////////////////////////////////////////////

	query.Format("select maximumweight from dbbayprofile bp, dbbay b "
		"where b.dbbayprofileid = bp.dbbayprofileid "
		" and b.dbbayid = %d", bayDBID);

	int rc = dataAccessService.ExecuteQuery("GetMaxBayWeight", query, results);
	if (results.GetSize() > 0) {
		results[0].TrimRight("|");
		return atof(results[0]);
	}

	return 0;

}


double CFacilityDataService::GetLevelMaxWeight(int levelDBID)
{
	CStringArray results;
	CString query;
	
	// This does a union of three queries but only one will actually
	// have return a value
	query.Format("select lp.maximumweight "		// gets all non-relative level 1
		"from dblevel le, dblevelprofile lp, dbbayprofile bp "
		"where le.dblevelprofileid = lp.dblevelprofileid "
		"and le.dblevelid = %d "
		"and lp.relativelevel <> 1 "
		"and bp.dbbayprofileid = lp.dbbayprofileid "
		"union "
		"select bp.maximumweight - sum(lp2.maximumweight) "		// gets level 1 when others exist
		"from dblevel le, dblevelprofile lp, dbbayprofile bp, dblevelprofile lp2 "
		"where le.dblevelprofileid = lp.dblevelprofileid "
		"and le.dblevelid = %d "
		"and lp.relativelevel = 1 "
		"and bp.dbbayprofileid = lp.dbbayprofileid "
		"and lp2.dbbayprofileid = bp.dbbayprofileid "
		"and lp2.relativelevel <> 1 "
		"group by bp.maximumweight "
		"union "
		"select bp.maximumweight "								// gets level 1 when no others exist
		"from dblevel le, dblevelprofile lp, dbbayprofile bp "
		"where le.dblevelprofileid = lp.dblevelprofileid "
		"and bp.dbbayprofileid = lp.dbbayprofileid "
		"and lp.relativelevel = 1 "
		"and le.dblevelid = %d "
		"and 1 = (select count(*) "
		"from dblevelprofile lp2 "
		"where lp2.dbbayprofileid = bp.dbbayprofileid)", 
		levelDBID, levelDBID, levelDBID);

	int rc = dataAccessService.ExecuteQuery("GetMaxBayWeight", query, results);
	if (results.GetSize() > 0) {
		results[0].TrimRight("|");
		return atof(results[0]);
	}
	
	return 0;
}

int CFacilityDataService::GetBayProfilesByFacility(CStringArray &bayProfileList)
{
	CString query;

	query.Format(" select unique bp.dbbayprofileid, bp.description, bp.baytype "
		"from dbbay b, dbbayprofile bp, dbside si, dbaisle a, dbsection se "
		"where b.dbbayprofileid = bp.dbbayprofileid "
		"and si.dbsideid = b.dbsideid "
		"and a.dbaisleid = si.dbaisleid "
		"and se.dbsectionid = a.dbsectionid "
		"and se.dbfacilityid = %d "
		"order by bp.baytype, bp.description", changesTree.elementDBID);

	return dataAccessService.ExecuteQuery("GetBayProfilesByFacility", query, bayProfileList);

}

CString CFacilityDataService::GetCurrentDC()
{
	int facilityId = controlService.GetCurrentFacilityDBId();
	CString sql;

	sql.Format("select unique dbwmsgroup.wmsid from dbwmsgroup, dbwms, dbwmsexportmap "
		"where dbwmsgroup.dbwmsgroupid = dbwms.dbwmsgroupid "
		"and dbwmsexportmap.dbwmsid = dbwms.dbwmsid "
		"and dbwmsexportmap.dbfacilityid = %d", facilityId);

	CStringArray results;

	try {
		dataAccessService.ExecuteQuery("GetCurrentDC", sql, results, TRUE);
	}
	catch (...) {
		controlService.Log("", "Error getting WMS Id for current facility.\n");
		return "";
	}

	if (results.GetSize() > 0)
		return results[0];

	return "";

}

CString CFacilityDataService::GetCurrentWarehouse()
{
	int facilityId = controlService.GetCurrentFacilityDBId();
	CString sql;

	sql.Format("select unique dbwms.wmsid from dbwmsgroup, dbwms, dbwmsexportmap "
		"where dbwmsgroup.dbwmsgroupid = dbwms.dbwmsgroupid "
		"and dbwmsexportmap.dbwmsid = dbwms.dbwmsid "
		"and dbwmsexportmap.dbfacilityid = %d", facilityId);

	CStringArray results;

	try {
		dataAccessService.ExecuteQuery("GetCurrentDC", sql, results, TRUE);
	}
	catch (...) {
		controlService.Log("", "Error getting WMS Id for current facility.\n");
		return "";
	}

	if (results.GetSize() > 0)
		return results[0];

	return "";


}


CString CFacilityDataService::GetDCBySection(int facilityId, int sectionId)
{
	CString sql;

	sql.Format("select unique dbwmsgroup.wmsid from dbwmsgroup, dbwms, dbwmsexportmap "
		"where dbwmsgroup.dbwmsgroupid = dbwms.dbwmsgroupid "
		"and dbwmsexportmap.dbwmsid = dbwms.dbwmsid "
		"and dbwmsexportmap.dbfacilityid = %d "
		"and dbwmsexportmap.dbsectionid = %d", facilityId, sectionId);

	CStringArray results;

	try {
		dataAccessService.ExecuteQuery("GetCurrentDC", sql, results, TRUE);
	}
	catch (...) {
		controlService.Log("", "Error getting WMS Id for section.\n");
		return "";
	}

	if (results.GetSize() > 0)
		return results[0];

	// If a dc doesn't map to a specific section
	sql.Format("select unique dbwmsgroup.wmsid from dbwmsgroup, dbwms, dbwmsexportmap "
		"where dbwmsgroup.dbwmsgroupid = dbwms.dbwmsgroupid "
		"and dbwmsexportmap.dbwmsid = dbwms.dbwmsid "
		"and dbwmsexportmap.dbfacilityid = %d "
		"and dbwmsexportmap.dbsectionid = 0", facilityId);

	try {
		dataAccessService.ExecuteQuery("GetCurrentDC", sql, results, TRUE);
	}
	catch (...) {
		controlService.Log("", "Error getting WMS Id for section.\n");
		return "";
	}

	if (results.GetSize() > 0)
		return results[0];

	return "";

}

CString CFacilityDataService::GetWarehouseBySection(int facilityId, int sectionId)
{
	CString sql;

	sql.Format("select unique dbwms.wmsid from dbwmsgroup, dbwms, dbwmsexportmap "
		"where dbwmsgroup.dbwmsgroupid = dbwms.dbwmsgroupid "
		"and dbwmsexportmap.dbwmsid = dbwms.dbwmsid "
		"and dbwmsexportmap.dbfacilityid = %d "
		"and dbwmsexportmap.dbsectionid = %d", facilityId, sectionId);

	CStringArray results;

	try {
		dataAccessService.ExecuteQuery("GetCurrentDC", sql, results, TRUE);
	}
	catch (...) {
		controlService.Log("", "Error getting WMS Id for section.\n");
		return "";
	}

	if (results.GetSize() > 0)
		return results[0];

	// If a dc doesn't map to a specific section
	sql.Format("select unique dbwms.wmsid from dbwmsgroup, dbwms, dbwmsexportmap "
		"where dbwmsgroup.dbwmsgroupid = dbwms.dbwmsgroupid "
		"and dbwmsexportmap.dbwmsid = dbwms.dbwmsid "
		"and dbwmsexportmap.dbfacilityid = %d "
		"and dbwmsexportmap.dbsectionid = 0", facilityId);

	try {
		dataAccessService.ExecuteQuery("GetCurrentDC", sql, results, TRUE);
	}
	catch (...) {
		controlService.Log("", "Error getting WMS Id for section.\n");
		return "";
	}

	if (results.GetSize() > 0)
		return results[0];

	return "";

}


int CFacilityDataService::GetDuplicateLocationsByFacility(int facilityDBID, CSsaStringArray &duplicates)
{
	CString queryText;

	queryText.Format("select 'D', s.description, a.description, b.description, le.description, "
		"l.description, count(*) "
		"from dbsection s, dbaisle a, dbside si, dbbay b, "
		"dblevel le, dblocation l, dblocationprof lop "
		"where s.dbfacilityid = %d "
		"and s.dbsectionid = a.dbsectionid "
		"and a.dbaisleid = si.dbaisleid "
		"and si.dbsideid = b.dbsideid "
		"and b.dbbayid = le.dbbayid "
		"and le.dblevelid = l.dblevelid "
		"and l.dblocationprofid = lop.dblocationprofid "
		"and ((lop.isselect = 1 and l.isoverridden = 0) or (l.isselect = 1 and l.isoverridden = 1)) "
		"group by s.description, a.description, b.description, le.description, l.description "
		"having count(*) > 1 "
		"order by l.description",
		facilityDBID);
		

	return dataAccessService.ExecuteQuery("GetDuplicateLocationsByFacility", queryText, duplicates);

}

int CFacilityDataService::GetFacilityCost(CStringArray &facilityList)
{
	CString sql;

	sql.Format("select dbfacilityid, description, cost, baselinecost, timehorizonvalue, timehorizonunit "
		"from dbfacility "
		"order by description");

	return dataAccessService.ExecuteQuery("GetFacilityCost", sql, facilityList);

}


int CFacilityDataService::GetFacilityNameList(CStringArray &facilityList)
{
	CString sql;

	sql.Format("select dbfacilityid, description "
		"from dbfacility "
		"order by description");

	return dataAccessService.ExecuteQuery("GetFacilityNameList", sql, facilityList);

}



int CFacilityDataService::GetFacilityDetailList(CStringArray &facilityList) 
{
	CString query;

	query.Format("SELECT dbfacility.dbfacilityid, description, timestamp, clientnameopened, Notes, actualsize "
		"FROM dbfacility, dbfacilitydrawing "
		"WHERE dbfacility.dbfacilityid = dbfacilitydrawing.dbfacilityid "
		"UNION "
		"select dbfacility.dbfacilityid, description, '', '', '', 0 "
		"from dbfacility where not exists ( select fd.dbfacilityid "
		"from dbfacilitydrawing fd "
		"where fd.dbfacilityid = dbfacility.dbfacilityid) "
		"order by 2");

	return dataAccessService.ExecuteQuery("GetFacilityDetailList", query, facilityList);

}


int CFacilityDataService::GetLevelProfileListByFacility(CStringArray &levelProfileList, long sectionID)
{
	CString queryText;

	/////////////////////////////////////////////////////////////
	// build the data to send to the Forte connection server
	/////////////////////////////////////////////////////////////

	queryText.Format("select unique lp.dblevelprofileid, lp.relativelevel, lp.baytype, bp.isfloating "
		"from dbsection se, dbaisle a, dbbay b, dbside si, dbbayprofile bp, dblevelprofile lp "
		"where se.dbfacilityid = %d "
		"and se.dbsectionid = a.dbsectionid "
		"and a.dbaisleid = si.dbaisleid "
		"and si.dbsideid = b.dbsideid "
		"and b.dbbayprofileid = bp.dbbayprofileid "
		"and bp.dbbayprofileid = lp.dbbayprofileid", changesTree.elementDBID);
		
	if (sectionID > 0) {
		CString temp;
		temp.Format(" and se.dbsectionid = %d ", sectionID);
		queryText += temp;
	}

	return dataAccessService.ExecuteQuery("GetLevelProfileList", queryText, levelProfileList);


}

int CFacilityDataService::GetLevelProfileUDFListByFacility(CStringArray &levelUDFList, long sectionID) 
{
	CString queryText;
	// ListID|Name|Type|DefaultValue|List Values|Parent (facilityid or profileid)|ElementType (i.e. table name)|...
	// |Value ID|String Value|Integer Value|Float Value|ElementID


	queryText.Format("select unique l.dblevprofudflistid, l.Description, l.Type, l.DefaultValue, "
		"l.ListValues, l.dbbayprofileid, %d, v.dblevprofudfvalid, v.Value, v.integervalue, v.floatvalue, "
		"lp.dblevelprofileid "
		"from dbsection se, dbaisle a, dbside si, dbbay b, dblevelprofile lp, "
		"dblevprofudflist l, dblevprofudfval v "
		"where se.dbfacilityid = %d "
		"and se.dbsectionid = a.dbsectionid "
		"and a.dbaisleid = si.dbaisleid "
		"and si.dbsideid = b.dbsideid "
		"and lp.dbbayprofileid = b.dbbayprofileid "
		"and l.dbbayprofileid = b.dbbayprofileid "
		"and l.dblevprofudflistid = v.dblevprofudflistid "
		"and v.dblevelprofileid = lp.dblevelprofileid ", UDF_LEVEL_PROFILE, changesTree.elementDBID);
	
	if (sectionID > 0) {
		CString temp;
		temp.Format(" and se.dbsectionid = %d ", sectionID);
		queryText += temp;
	}

	return dataAccessService.ExecuteQuery("GetLevelProfileUDFList", queryText, levelUDFList);

}


int CFacilityDataService::GetProductGroupForLocation(int locationDBID, CString &productGroupDescription)
{

	CStringArray results;
	CString queryText;

	/////////////////////////////////////////////////////////////
	// build the data to send to the Forte connection server
	/////////////////////////////////////////////////////////////

	queryText.Format("select sg.description "
		"from dbslottinggroup sg, dbslotgrpbay sgb, dblocation l "
		"where l.dblocationid = %d "
		"and l.dblevelid = sgb.dblevelid "
		"and sgb.dbslottinggroupid = sg.dbslottinggroupid", locationDBID);
			
	
	dataAccessService.ExecuteQuery("GetProductGroupForLocation", queryText, results, TRUE);

	if ( results.GetSize() == 0 )
		return 0;
	else {
		productGroupDescription = results[0];
		return 1;
	}

}


BOOL CFacilityDataService::IsBayProfileInUse(CString & bayProfileName)
{
#if 0
	CString tempString;
	CSsaStringArray tempSendArray;
	CSsaStringArray tempRecvArray;
	CString sendString;
	int i;
	int tempInt;

	/////////////////////////////////////////////////////////////
	// Build the data and send to Forte connection
	/////////////////////////////////////////////////////////////
	sendString = "<SIO>SLOTSocketString\n";
	tempSendArray.Add(sendString);

	tempSendArray.Add(CString("<SAI>") + bayProfileName + CString("\n"));

	sendString = "<EIO>SLOTSocketString\n";
	tempSendArray.Add(sendString);

	forteService.SendToForteConnection(tempSendArray,tempRecvArray,
		CString("SLOTSocketString"), 8070);

	for (i=0; i < tempRecvArray.GetSize(); i++) {
		if ( tempRecvArray[i].Find("<SAI>") != -1 ) {
			tempString = tempRecvArray[i].Mid(5);
			tempInt = tempString.GetLength();
			if (tempString.GetAt(tempInt-1) == '\n' ) {
				tempString.SetAt(tempInt-1,' ');
				tempString.TrimRight();
			}
//			aisleNameList.Add(tempString);
		}
	}
	if (tempString == "TRUE")
		return TRUE;
	else
		return FALSE;
#else
	string bayProfileNameTmp = (LPCTSTR)bayProfileName;
	bool ret = getSessionMgrSO()->IsBayProfileInUseHelper(bayProfileNameTmp);
	if (ret == true)
		return TRUE;
	else
		return FALSE;
#endif
}


int CFacilityDataService::QueryLocations(int pAssigned, int pProdGroupID, qqhSLOTQuery &pQuery, int pLevelType, CStringArray &pResults)
{
	CSsaStringArray tempSendArray, tempRecvArray, tempQueryArray;
	CString sendString, tempString;

	//CHECK0091: CListstring* tempSendStrArray= new CListstring;
	CListstringPtr tempSendStrArray(new CListstring);
	// wrap the product query in a string so we can send it with the other data
	/** CHECK0091:  strip off the socket code
	sendString = "<SIO>SLOTSocketString\n";
	tempSendArray.Add(sendString);
	*/
	pQuery.StreamAttributes(tempQueryArray);
	for (int i=0; i < tempQueryArray.GetSize(); ++i) {
		///tempQueryArray[i].Insert(0, "<SAI>");
		tempSendArray.Add(tempQueryArray[i]);
	}

	/* CHECK0091:
	sendString = "<EIO>SLOTSocketString\n";
	tempSendArray.Add(sendString);
	*/
	for (i=0; i < tempSendArray.GetCount(); i++)
	{
		///Strip off the trailing '\n'
		//string tmpStr = (LPCTSTR)(tempSendArray[i].Left(tempSendArray[i].GetLength()-1));
		string tmpStr = (LPCTSTR)(tempSendArray.GetAt(i).Left(tempSendArray.GetAt(i).GetLength()-1));
		tempSendStrArray->AddTail(tmpStr);
	}
#if 0
	// now send the other data
	sendString = "<SIO>SLOTSocketString\n";
	tempSendArray.Add(sendString);
	sendString.Format("<SAI>%d\n", pAssigned);
	tempSendArray.Add(sendString);
	sendString.Format("<SAI>%d\n",pProdGroupID);
	tempSendArray.Add(sendString);
	sendString.Format("<SAI>%d\n", pLevelType);
	tempSendArray.Add(sendString);
	sendString = "<EIO>SLOTSocketString\n";
	tempSendArray.Add(sendString);

	forteService.SendToForteConnection(tempSendArray, tempRecvArray,
		CString("SLOTSocketString"), 10330);

	for (i=0; i < tempRecvArray.GetSize(); i++) {
		if ( tempRecvArray[i].Find("<SAI>") != -1 ) {
			tempString = tempRecvArray[i].Mid(5);
			tempInt = tempString.GetLength();
			if (tempString.GetAt(tempInt-1) == '\n' ) {
				tempString.SetAt(tempInt-1,' ');
				tempString.TrimRight();
			}
			pResults.Add(tempString);
		}
	}
#else
	CListstringPtr res = getSessionMgrSO()->QueryLocationsHelper(pAssigned, 
																pProdGroupID, 
																pLevelType, 
																tempSendStrArray);
	POSITION posSL = res->GetHeadPosition();
	for (i=0; i < res->GetCount(); i++)
	{
		pResults.Add((res->GetNext(posSL)).c_str());
	}
#endif
	///delete tempSendStrArray;
	if ( pResults.GetSize() > 0 )
		return 0;
	else 
		return -1;
}


int CFacilityDataService::GetFacilitySectionList(CStringArray &results)
{
	CString sql;

	sql.Format("select dbfacility.dbfacilityid, dbfacility.description, "
		"dbsection.dbsectionid, dbsection.description "
		"from dbfacility, dbsection "
		"where dbfacility.dbfacilityid = dbsection.dbfacilityid "
		"union "
		"select dbfacility.dbfacilityid, dbfacility.description, 0, '' "
		"from dbfacility "
		"where not exists "
		"(select dbfacilityid from dbsection where dbsection.dbfacilityid = dbfacility.dbfacilityid) "
		"order by 2, 4");

	return dataAccessService.ExecuteQuery("GetFacilitySectionList", sql, results);

}

BOOL CFacilityDataService::IsEndBay(qqhSLOTBay &bay)
{
	CString sql;

	sql.Format("select dbside.isrotated, dbbay.xcoordinate, dbbay.dbbayid "
		"from dbbay, dbside "
		"where dbbay.dbsideid = dbside.dbsideid "
		"and dbbay.dbsideid = (select dbsideid from dbbay b2 where dbbayid = %d) "
		"order by dbbay.xcoordinate desc", bay.getDBID());
	
	CStringArray results;

	dataAccessService.ExecuteQuery("IsEndBay", sql, results, TRUE);
	
	if (results.GetSize() == 0)
		return TRUE;

	CStringArray strings;
	utilityHelper.ParseString(results[0], "|", strings);
	
	int rotated = atoi(strings[0]);
	int max, dbid;

	if (! rotated) {

		for (int i=0; i < results.GetSize(); ++i) {
			
			strings.RemoveAll();
			utilityHelper.ParseString(results[i], "|", strings);
			
			max = atoi(strings[1]);
			dbid = atoi(strings[2]);
			
			int dummy;
			// If the bay has been deleted from the drawing, ignore it
			if (changesTree.DeletedBayMap.Lookup(dbid, dummy))
				continue;
			
			if (dbid == bay.getDBID()) {
				return TRUE;
			}
			else {
				return FALSE;
			}
		}
	}
	else {
		for (int i=results.GetSize()-1; i >= 0; --i) {
			
			strings.RemoveAll();
			utilityHelper.ParseString(results[i], "|", strings);
			
			max = atoi(strings[1]);
			dbid = atoi(strings[2]);
			
			int dummy;
			// If the bay has been deleted from the drawing, ignore it
			if (changesTree.DeletedBayMap.Lookup(dbid, dummy))
				continue;
			
			if (dbid == bay.getDBID()) {
				return TRUE;
			}
			else {
				return FALSE;
			}
		}		
		
	}

	// if we get past all the bays in the aisle without finding ours
	// it must be a new bay, so let the calling program figure it out
	return bay.isEndBay;
}

int CFacilityDataService::SetIntegrationStatus(BOOL isIntegrated)
{
#if 0
	CSsaStringArray tempSendArray;
	CSsaStringArray tempRecvArray;
	CString sendString;
	CString tempString;
	int tempInt;

	/////////////////////////////////////////////////////////////
	// build the data to send to the Forte connection server
	/////////////////////////////////////////////////////////////
	sendString = "<SIO>SLOTSocketString\n";
	tempSendArray.Add(sendString);
	tempString.Format("<SAI>%s\n",isIntegrated ? "TRUE" : "FALSE");
	tempSendArray.Add(tempString);
	sendString = "<EIO>SLOTSocketString\n";
	tempSendArray.Add(sendString);

	forteService.SendToForteConnection(tempSendArray,tempRecvArray,
		CString("SLOTSocketString"), 10350);

	for (int i=0; i < tempRecvArray.GetSize(); i++) {
		if ( tempRecvArray[i].Find("<SAI>") != -1 ) {
			tempString = tempRecvArray[i].Mid(5);
			tempInt = tempString.GetLength();
			if (tempString.GetAt(tempInt-1) == '\n' ) {
				tempString.SetAt(tempInt-1,' ');
				tempString.TrimRight();
			}
		}
	}
	if ( tempString == "Success")
		return 0;
	else
		return -1;
#else
	if (isIntegrated)
		///SessionMgrSO.SetIntegrationStatusHelper(true);
		getSessionMgrSO()->SetIntegrationStatusHelper(true);
	else
		getSessionMgrSO()->SetIntegrationStatusHelper(false);
	return 0;
#endif
}

BOOL CFacilityDataService::IsFacilityIntegrated(int facilityDBId)
{
	CString sql;
	CStringArray results;

	sql.Format("select count(*) from dbwmsexportmap "
		"where dbfacilityid = %d", facilityDBId);

	try {
		dataAccessService.ExecuteQuery("IsFacilityIntegrated", sql, results, TRUE);
	}
	catch (...) {
		utilityHelper.ProcessError("Error determining if facility is integrated.");
		return -1;
	}

	if (results.GetSize() == 0) {
		AfxMessageBox("Error determining if facility is integrated.");
		return -1;
	}

	return (atoi(results[0]) > 0);
}

BOOL CFacilityDataService::IsHandleAPickPath(const CString &handle)
{
	CString sql;

	sql.Format("select count(*) "
		"from dbsection, dbaisle, dbaislepath, dbpickpath "
		"where dbsection.dbfacilityid = %d "
		"and dbsection.dbsectionid = dbaisle.dbsectionid "
		"and dbaisle.dbaisleid = dbaislepath.dbaisleid "
		"and dbaislepath.dbpickpathid = dbpickpath.dbpickpathid "
		"and dbpickpath.acadhandle = '%s'", changesTree.elementDBID, handle);

	CStringArray results;
	dataAccessService.ExecuteQuery("IsHandleAPickPath", sql, results, TRUE);
	
	BOOL found = (atoi(results[0]) > 0);

	if (found)
		return TRUE;

	// If the item was just added to the facility, it won't be in the database, so
	// we have to check the btree

	for (int sIdx=0; sIdx < changesTree.treeChildren.GetSize(); ++sIdx) {
		TreeElement *sPtr = &changesTree.treeChildren[sIdx];
		for (int aIdx=0; aIdx < sPtr->treeChildren.GetSize(); ++aIdx) {
			TreeElement *aPtr = &sPtr->treeChildren[aIdx];
			if (strcmp(aPtr->acadHandle, handle) == 0)
				return TRUE;
		}
	}

	return FALSE;

}

BOOL CFacilityDataService::IntegratedSectionHasAssignments(int sectionDBId)
{
	if (! IsFacilityIntegrated(changesTree.elementDBID))
		return FALSE;

	CString sql;
	sql.Format("select count(*) "
		"from dblocation l, dblevel le, dbbay b, dbside si, dbaisle a, dbslotsolution ss "
		"where l.dblevelid = le.dblevelid "
		"and le.dbbayid = b.dbbayid "
		"and b.dbsideid = si.dbsideid "
		"and si.dbaisleid = a.dbaisleid "
		"and a.dbsectionid = %d "
		"and ss.dblocationid = l.dblocationid "
		"and l.status in (%d, %d) ", sectionDBId,
		LOC_STATUS_INTEGRATION_PENDING, LOC_STATUS_INTEGRATED);

	CStringArray results;
	try {
		dataAccessService.ExecuteQuery("IntegratedSectionHasAssignments", sql, results, TRUE);
	}
	catch (...) {
		controlService.Log("Error retrieving integration status for section.", 
			"Error in IntegratedSectionHasAssignments for section: %d\n", sectionDBId);
		return TRUE;
	}

	if (atoi(results[0]) > 0)
		return TRUE;

	return FALSE;

}

BOOL CFacilityDataService::IntegratedAisleHasAssignments(int aisleDBId)
{
	if (! IsFacilityIntegrated(changesTree.elementDBID))
		return FALSE;

	CString sql;
	sql.Format("select count(*) "
		"from dblocation l, dblevel le, dbbay b, dbside si, dbslotsolution ss "
		"where l.dblevelid = le.dblevelid "
		"and le.dbbayid = b.dbbayid "
		"and b.dbsideid = si.dbsideid "
		"and si.dbaisleid = %d "
		"and ss.dblocationid = l.dblocationid "
		"and l.status in (%d, %d) ", aisleDBId,
		LOC_STATUS_INTEGRATION_PENDING, LOC_STATUS_INTEGRATED);

	CStringArray results;
	try {
		dataAccessService.ExecuteQuery("IntegratedAisleHasAssignments", sql, results, TRUE);
	}
	catch (...) {
		controlService.Log("Error retrieving integration status for aisle.", 
			"Error in IntegratedAisleHasAssignments for aisle: %d\n", aisleDBId);
		return TRUE;
	}

	if (atoi(results[0]) > 0)
		return TRUE;

	return FALSE;

}


BOOL CFacilityDataService::IntegratedSideHasAssignments(int sideDBId)
{
	if (! IsFacilityIntegrated(changesTree.elementDBID))
		return FALSE;

	CString sql;
	sql.Format("select count(*) "
		"from dblocation l, dblevel le, dbbay b, dbslotsolution ss "
		"where l.dblevelid = le.dblevelid "
		"and le.dbbayid = b.dbbayid "
		"and b.dbsideid = %d "
		"and ss.dblocationid = l.dblocationid "
		"and l.status in (%d, %d) ", sideDBId,
		LOC_STATUS_INTEGRATION_PENDING, LOC_STATUS_INTEGRATED);

	CStringArray results;
	try {
		dataAccessService.ExecuteQuery("IntegratedSideHasAssignments", sql, results, TRUE);
	}
	catch (...) {
		controlService.Log("Error retrieving integration status for side.", 
			"Error in IntegratedSideHasAssignments for side: %d\n", sideDBId);
		return TRUE;
	}

	if (atoi(results[0]) > 0)
		return TRUE;

	return FALSE;

}


BOOL CFacilityDataService::IntegratedBayHasAssignments(int bayDBId)
{
	if (! IsFacilityIntegrated(changesTree.elementDBID))
		return FALSE;

	CString sql;
	sql.Format("select count(*) "
		"from dblocation l, dblevel le, dbslotsolution ss "
		"where l.dblevelid = le.dblevelid "
		"and le.dbbayid = %d "
		"and ss.dblocationid = l.dblocationid "
		"and l.status in (%d, %d) ", bayDBId,
		LOC_STATUS_INTEGRATION_PENDING, LOC_STATUS_INTEGRATED);

	CStringArray results;
	try {
		dataAccessService.ExecuteQuery("IntegratedBayHasAssignments", sql, results, TRUE);
	}
	catch (...) {
		controlService.Log("Error retrieving integration status for bay.", 
			"Error in IntegratedBayHasAssignments for bay: %d\n", bayDBId);
		return TRUE;
	}

	if (atoi(results[0]) > 0)
		return TRUE;

	return FALSE;
}

int CFacilityDataService::GetFacility(int facilityId, CFacility &facility)
{

	if (facilityId == controlService.GetCurrentFacilityDBId()) {
		qqhSLOTFacility fac;
		bTreeHelper.GetBtFacility(changesTree.fileOffset, fac);

		fac.ConvertToFacility(facility);

	}
	else {
		CString sql;

		sql.Format("select * from dbfacility where dbfacilityid = %d", facilityId);
		CStringArray results;
		dataAccessService.ExecuteQuery("GetFacility", sql, results);
		if (results.GetSize() == 0)
			return -1;

		facility.Parse(results[0]);

	}

	return 0;
	
}


int CFacilityDataService::GetOptimizeBayTypeCount(int facilityId)
{
	CString sql;
	CStringArray results;

	sql.Format("select count(unique lp.dbbayprofileid) "
		"from dbbayprofile bp, dblevelprofile lp, dbbay b, dbside si, dbaisle a, dbsection se "
		"where se.dbfacilityid = %d "
		"and a.dbsectionid = se.dbsectionid "
		"and si.dbaisleid = a.dbaisleid "
		"and b.dbsideid = si.dbsideid "
		"and bp.dbbayprofileid = b.dbbayprofileid "
		"and lp.dbbayprofileid = bp.dbbayprofileid "
		"and bp.excludefromopt = 0 ", facilityId);

	try {
		dataAccessService.ExecuteQuery("GetOptimizeBayTypeCount", sql, results, TRUE);
	}
	catch (...) {
		controlService.Log("", "Generic exception in GetOptimizeBayTypeCount.\n");
		return 0;
	}

	if (results.GetSize() == 0)
		return 0;

	return atoi(results[0]);

}

BOOL CFacilityDataService::IsSectionNameInUse(const CString &name, int facilityId, int sectionId)
{
	CString sql;
	CStringArray results;
	int count = 0;
	
	if (facilityId > 0) {
		sql.Format("select dbsectionid from dbsection "
			"where dbfacilityid = %d "
			"and description = '%s' ", facilityId, name);
		
		if (sectionId > 0) {
			CString temp;
			temp.Format(" and dbsectionid <> %d", sectionId);
			sql += temp;
		}
		
		dataAccessService.ExecuteQuery("IsSectionNameInUse", sql, results, TRUE);

		for (int i=0; i < results.GetSize(); ++i) {
			int sectionId = atoi(results[i]);
			int dummy;
			// Make sure the section hasn't been deleted
			if (! changesTree.DeletedSectionMap.Lookup(sectionId, dummy))
				return TRUE;
		}

		
	}

	// Now check the b-tree
	for (int i=0; i < changesTree.treeChildren.GetSize(); ++i) {
		if (sectionId > 0 && changesTree.treeChildren[i].elementDBID == sectionId)
			continue;

		qqhSLOTSection section;
		bTreeHelper.GetBtSection(changesTree.treeChildren[i].fileOffset, section);
		if (section.getDescription() == name)
			return TRUE;
	}

	return FALSE;
}

int CFacilityDataService::MakeInActive(int facilityId, int sectionId, int aisleId, int sideId, int bayId, int levelId, int locationId)
{
	CString sql;
	CString from, where;

	from.Format("%s %s %s %s %s dblocation l",
		facilityId > 0 ? "dbsection se, " : "",
		sectionId > 0 || facilityId > 0 ? "dbaisle a, " : "",
		aisleId > 0 || sectionId > 0 || facilityId > 0 ? "dbside si, " : "",
		sideId > 0 || aisleId > 0 || sectionId > 0 || facilityId > 0 ? "dbbay b, " : "",
		bayId > 0 || sideId > 0 || aisleId > 0 || sectionId > 0 || facilityId > 0 ? "dblevel le, " : "");

	CString temp;

	if (locationId > 0) {
		temp.Format("and l.dblocationid = %d ", locationId);
		where += temp;
	}
	else {	
		if (levelId > 0) {
			temp.Format("and l.dblevelid = %d ", levelId);
			where += temp;
		}
		else {
			temp.Format("and l.dblevelid = le.dblevelid ");
			where += temp;
			
			if (bayId > 0) {
				temp.Format("and le.dbbayid = %d ", bayId);
				where += temp;
			}
			else {
				temp.Format("and le.dbbayid = b.dbbayid ");
				where += temp;
				
				if (sideId > 0) {
					temp.Format("and b.dbsideid = %d ", sideId);
					where += temp;
				}
				else {
					temp.Format("and b.dbsideid = si.dbsideid ");
					where += temp;
					
					if (aisleId > 0) {
						temp.Format("and si.dbaisleid = %d ", aisleId);
						where += temp;
					}
					else {
						temp.Format("and si.dbaisleid = a.dbaisleid ");
						where += temp;
						
						if (sectionId > 0) {
							temp.Format("and a.dbsectionid = %d ", sectionId);
							where += temp;
						}
						else {
							temp.Format("and a.dbsectionid = se.dbsectionid ");
							where += temp;
							
							if (facilityId > 0) {
								temp.Format("and se.dbfacilityid = %d ", facilityId);
								where += temp;
							}
						}
					}
				}
			}
		}
	}


	sql.Format("update dblocation set isactive = 0 "
		"where exists ( "
		"select dblocationid "
		"from %s "
		"where l.dblocationid = dblocation.dblocationid "
		"%s)", from, where);

	CArray<int, int> locOffsetList;
	TreeElement *ptr;

	if (facilityId > 0)
		changesTree.getLocationOffsetByFacility(changesTree.fileOffset, locOffsetList);
	else if (sectionId > 0) {
		ptr = changesTree.getSectionByDBID(sectionId);
		changesTree.getLocationOffsetBySection(ptr->fileOffset, locOffsetList);
	}
	else if (aisleId > 0) {
		ptr = changesTree.getAisleByDBID(aisleId);
		changesTree.getLocationOffsetByAisle(ptr->fileOffset, locOffsetList);
	}
	else if (sideId > 0) {
		ptr = changesTree.getSideByDBID(sideId);
		changesTree.getLocationOffsetBySide(ptr->fileOffset, locOffsetList);
	}
	else if (bayId > 0) {
		ptr = changesTree.getBayByDBID(bayId);
		changesTree.getLocationOffsetByBay(ptr->fileOffset, locOffsetList);
	}
	else if (levelId > 0) {
		ptr = changesTree.getLevelByDBID(levelId);
		changesTree.getLocationOffsetByLevel(ptr->fileOffset, locOffsetList);
	}
	else if (locationId > 0) {
		ptr = changesTree.getLocationByDBID(locationId, FALSE);
		locOffsetList.Add(ptr->fileOffset);
	}

	for (int i=0; i < locOffsetList.GetSize(); ++i) {
		qqhSLOTLocation loc;
		bTreeHelper.GetBtLocation(locOffsetList[i], loc);
		loc.setIsActive(0);
		bTreeHelper.SetBtLocation(locOffsetList[i], loc);
	}


	return dataAccessService.ExecuteStatement("MakeInActive", sql);

}

int CFacilityDataService::MakeActive(int facilityId, int sectionId, int aisleId, int sideId, int bayId, int levelId, int locationId)
{
	CString sql;
	CString from, where;

	from.Format("%s %s %s %s %s dblocation l",
		facilityId > 0 ? "dbsection se, " : "",
		sectionId > 0 || facilityId > 0 ? "dbaisle a, " : "",
		aisleId > 0 || sectionId > 0 || facilityId > 0 ? "dbside si, " : "",
		sideId > 0 || aisleId > 0 || sectionId > 0 || facilityId > 0 ? "dbbay b, " : "",
		bayId > 0 || sideId > 0 || aisleId > 0 || sectionId > 0 || facilityId > 0 ? "dblevel le, " : "");

	CString temp;

	if (locationId > 0) {
		temp.Format("and l.dblocationid = %d ", locationId);
		where += temp;
	}
	else {	
		if (levelId > 0) {
			temp.Format("and l.dblevelid = %d ", levelId);
			where += temp;
		}
		else {
			temp.Format("and l.dblevelid = le.dblevelid ");
			where += temp;
			
			if (bayId > 0) {
				temp.Format("and le.dbbayid = %d ", bayId);
				where += temp;
			}
			else {
				temp.Format("and le.dbbayid = b.dbbayid ");
				where += temp;
				
				if (sideId > 0) {
					temp.Format("and b.dbsideid = %d ", sideId);
					where += temp;
				}
				else {
					temp.Format("and b.dbsideid = si.dbsideid ");
					where += temp;
					
					if (aisleId > 0) {
						temp.Format("and si.dbaisleid = %d ", aisleId);
						where += temp;
					}
					else {
						temp.Format("and si.dbaisleid = a.dbaisleid ");
						where += temp;
						
						if (sectionId > 0) {
							temp.Format("and a.dbsectionid = %d ", sectionId);
							where += temp;
						}
						else {
							temp.Format("and a.dbsectionid = se.dbsectionid ");
							where += temp;
							
							if (facilityId > 0) {
								temp.Format("and se.dbfacilityid = %d ", facilityId);
								where += temp;
							}
						}
					}
				}
			}
		}
	}


	sql.Format("update dblocation set isactive = 1 "
		"where exists ( "
		"select dblocationid "
		"from %s "
		"where l.dblocationid = dblocation.dblocationid "
		"%s)", from, where);

	CArray<int, int> locOffsetList;
	TreeElement *ptr;

	if (facilityId > 0)
		changesTree.getLocationOffsetByFacility(changesTree.fileOffset, locOffsetList);
	else if (sectionId > 0) {
		ptr = changesTree.getSectionByDBID(sectionId);
		changesTree.getLocationOffsetBySection(ptr->fileOffset, locOffsetList);
	}
	else if (aisleId > 0) {
		ptr = changesTree.getAisleByDBID(aisleId);
		changesTree.getLocationOffsetByAisle(ptr->fileOffset, locOffsetList);
	}
	else if (sideId > 0) {
		ptr = changesTree.getSideByDBID(sideId);
		changesTree.getLocationOffsetBySide(ptr->fileOffset, locOffsetList);
	}
	else if (bayId > 0) {
		ptr = changesTree.getBayByDBID(bayId);
		changesTree.getLocationOffsetByBay(ptr->fileOffset, locOffsetList);
	}
	else if (levelId > 0) {
		ptr = changesTree.getLevelByDBID(levelId);
		changesTree.getLocationOffsetByLevel(ptr->fileOffset, locOffsetList);
	}
	else if (locationId > 0) {
		ptr = changesTree.getLocationByDBID(locationId, FALSE);
		locOffsetList.Add(ptr->fileOffset);
	}

	for (int i=0; i < locOffsetList.GetSize(); ++i) {
		qqhSLOTLocation loc;
		bTreeHelper.GetBtLocation(locOffsetList[i], loc);
		loc.setIsActive(1);
		bTreeHelper.SetBtLocation(locOffsetList[i], loc);
	}


	return dataAccessService.ExecuteStatement("MakeActive", sql);
}
#if !defined(AFX_BAYPROFILESIDEVIEWBUTTON_H__D4807B93_7D53_4DFF_A656_8C88582A3866__INCLUDED_)
#define AFX_BAYPROFILESIDEVIEWBUTTON_H__D4807B93_7D53_4DFF_A656_8C88582A3866__INCLUDED_

#if _MSC_VER > 1000
#pragma once
#endif // _MSC_VER > 1000
// BayProfileSideViewButton.h : header file
//
#include "BayProfileDimensionInfo.h"

/////////////////////////////////////////////////////////////////////////////
// CBayProfileSideViewButton window

class CBayProfileSideViewButton : public CButton
{
// Construction
public:
	CBayProfileSideViewButton();

// Attributes
public:
	CBayProfileDimensionInfo m_DimensionInfo;

// Operations
public:

// Overrides
	// ClassWizard generated virtual function overrides
	//{{AFX_VIRTUAL(CBayProfileSideViewButton)
	public:
	virtual void DrawItem(LPDRAWITEMSTRUCT lpDrawItemStruct);
	protected:
	//}}AFX_VIRTUAL

// Implementation
public:
	virtual ~CBayProfileSideViewButton();

	// Generated message map functions
protected:
	//{{AFX_MSG(CBayProfileSideViewButton)
		// NOTE - the ClassWizard will add and remove member functions here.
	//}}AFX_MSG

	DECLARE_MESSAGE_MAP()

private:
	void DrawVertDimLine(CDC &cdc, const CPoint &startPt, int len, int width, const CString &text,
		int textSize = 14);
	void DrawHorzDimLine(CDC &cdc, const CPoint &startPt, int len, int width, const CString &text,
		int textSize = 14);
	void DrawVertLine(CDC &cdc, const CPoint &startPt, int len, int width, BOOL bDashed = FALSE);
	void DrawHorzLine(CDC &cdc, const CPoint &startPt, int len, int width, BOOL bDashed = FALSE);
	void DrawBox(CDC &cdc, const CRect& r, int width, BOOL bDashed = FALSE);
	void DrawBoxWithText(CDC &cdc, const CRect& r, int width, BOOL bDashed = FALSE,
		const CString &text = "", int textSize = 14);
	void DrawBin(CDC &cdc);
	void DrawPallet(CDC &cdc);
	void DrawFlow(CDC &cdc);
	void DrawFloor(CDC &cdc);
	void DrawDriveIn(CDC &cdc);
};

/////////////////////////////////////////////////////////////////////////////

//{{AFX_INSERT_LOCATION}}
// Microsoft Visual C++ will insert additional declarations immediately before the previous line.

#endif // !defined(AFX_BAYPROFILESIDEVIEWBUTTON_H__D4807B93_7D53_4DFF_A656_8C88582A3866__INCLUDED_)

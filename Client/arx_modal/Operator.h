// Operator.h: interface for the COperator class.
//
//////////////////////////////////////////////////////////////////////

#if !defined(AFX_OPERATOR_H__C2D63EB5_2840_11D5_9ECA_00C04FAC149C__INCLUDED_)
#define AFX_OPERATOR_H__C2D63EB5_2840_11D5_9ECA_00C04FAC149C__INCLUDED_

#if _MSC_VER > 1000
#pragma once
#endif // _MSC_VER > 1000

class COperator : public CObject  
{
public:
	COperator(const CString &internal, const CString &display, int operandCount);
	COperator();
	virtual ~COperator();

	CString m_Internal;
	CString m_Display;
	int m_OperandCount;

};

#endif // !defined(AFX_OPERATOR_H__C2D63EB5_2840_11D5_9ECA_00C04FAC149C__INCLUDED_)

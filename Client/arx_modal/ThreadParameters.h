// ThreadParameters.h: interface for the CThreadParameters class.
//
//////////////////////////////////////////////////////////////////////

#if !defined(AFX_THREADPARAMETERS_H__20C32F9C_4A5E_4C00_ACC8_794529FCA3D9__INCLUDED_)
#define AFX_THREADPARAMETERS_H__20C32F9C_4A5E_4C00_ACC8_794529FCA3D9__INCLUDED_

#if _MSC_VER > 1000
#pragma once
#endif // _MSC_VER > 1000

#include <afxmt.h>

class CThreadParameters : public CObject  
{
public:
	CThreadParameters();
	virtual ~CThreadParameters();

	CWnd *m_pWnd;
	CEvent *m_pEvent;
	CStringArray *m_pInList;
	CStringArray *m_pOutList;
	CProgressCtrl *m_pProgressCtrl;
	int m_ReturnCode;
	CString m_ReturnMessage;
	int m_EventId;
};

#endif // !defined(AFX_THREADPARAMETERS_H__20C32F9C_4A5E_4C00_ACC8_794529FCA3D9__INCLUDED_)

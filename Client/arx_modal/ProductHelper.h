// ProductHelper.h: interface for the CProductHelper class.
//
//////////////////////////////////////////////////////////////////////

#if !defined(AFX_PRODUCTHELPER_H__FDEAD866_69A6_4130_AEEA_0EF903A847A4__INCLUDED_)
#define AFX_PRODUCTHELPER_H__FDEAD866_69A6_4130_AEEA_0EF903A847A4__INCLUDED_

#if _MSC_VER > 1000
#pragma once
#endif // _MSC_VER > 1000

class CProductHelper  
{
public:
	CProductHelper();
	virtual ~CProductHelper();
	
	void DataModel();
	void DataPurification();
	void ProductMaintenance();
	void PopulateUDFWithFormula();
	void DeleteProductsByFacility();
};

#endif // !defined(AFX_PRODUCTHELPER_H__FDEAD866_69A6_4130_AEEA_0EF903A847A4__INCLUDED_)

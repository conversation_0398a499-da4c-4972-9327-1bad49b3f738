#if !defined(AFX_PRODUCTGROUPNAVIGATOR_H__C6242621_17CC_11D5_9ECA_00C04FAC149C__INCLUDED_)
#define AFX_PRODUCTGROUPNAVIGATOR_H__C6242621_17CC_11D5_9ECA_00C04FAC149C__INCLUDED_

#if _MSC_VER > 1000
#pragma once
#endif // _MSC_VER > 1000
// ProductGroupNavigator.h : header file
//
#include "ProductGroup.h"
#include "ProductAttribute.h"
#include "ProductGroupCriteria.h"
#include "ProductGroupDataService.h"
/////////////////////////////////////////////////////////////////////////////
// CProductGroupNavigator dialog

class CProductGroupNavigator : public CDialog
{
// Construction
	DECLARE_DYNCREATE(CProductGroupNavigator)

public:
	CProductGroupNavigator(CWnd* pParent = NULL);   // standard constructor
	~CProductGroupNavigator();

	// The following variables are used by the various dialog boxes; some
	// by more than one which is why they are stored in the navigator
	ProductGroupArrayType m_ProductGroupList;
	CTypedPtrArray<CObArray, CProductGroupCriteria*> m_ProductGroupCriteriaList;
	CTypedPtrArray<CObArray, CProductAttribute*> m_ProductAttributeList;
	CProductGroupDataService m_ProductGroupDataService;
	CMap<long, long, CString, CString> m_ActualCountMap;
	CMap<long, long, CString, CString> m_MaximumCountMap;

// Dialog Data
	//{{AFX_DATA(CProductGroupNavigator)
	enum { IDD = IDD_PRODUCT_GROUP_NAVIGATOR };
	//}}AFX_DATA


// Overrides
	// ClassWizard generated virtual function overrides
	//{{AFX_VIRTUAL(CProductGroupNavigator)
	public:
	virtual BOOL Create(LPCTSTR lpszClassName, LPCTSTR lpszWindowName, DWORD dwStyle, const RECT& rect, CWnd* pParentWnd, UINT nID, CCreateContext* pContext = NULL);
	protected:
	virtual void DoDataExchange(CDataExchange* pDX);    // DDX/DDV support
	virtual void PostNcDestroy();
	//}}AFX_VIRTUAL

// Implementation
protected:

	// Generated message map functions
	//{{AFX_MSG(CProductGroupNavigator)
	virtual BOOL OnInitDialog();
	afx_msg void OnMaintainCriteria();
	afx_msg void OnMaintainGroups();
	afx_msg void OnAssignCriteria();
	afx_msg void OnAssignProducts();
	afx_msg void OnQuit();
	afx_msg void OnShowHelp();
	afx_msg BOOL OnHelpInfo(HELPINFO* pHelpInfo);
	//}}AFX_MSG
	DECLARE_MESSAGE_MAP()
private:
	int m_CurrentType;
	BOOL ValidateClose(CWnd *pWnd);
};

//{{AFX_INSERT_LOCATION}}
// Microsoft Visual C++ will insert additional declarations immediately before the previous line.

#endif // !defined(AFX_PRODUCTGROUPNAVIGATOR_H__C6242621_17CC_11D5_9ECA_00C04FAC149C__INCLUDED_)

// Facility.cpp: implementation of the CFacility class.
//
//////////////////////////////////////////////////////////////////////

#include "stdafx.h"
#include "modal.h"
#include "Facility.h"
#include "UtilityHelper.h"

#ifdef _DEBUG
#undef THIS_FILE
static char THIS_FILE[]=__FILE__;
#define new DEBUG_NEW
#endif

extern CUtilityHelper utilityHelper;

//////////////////////////////////////////////////////////////////////
// Construction/Destruction
//////////////////////////////////////////////////////////////////////

CFacility::CFacility()
{
}

CFacility::~CFacility()
{

}

int CFacility::Parse(const CString &line)
{
	CStringArray strings;

	utilityHelper.ParseString(line, "|", strings);

	for (int i=0; i < strings.GetSize(); ++i) {
		switch (i) {
		case 0:
			m_DBId = atoi(strings[i]);
			break;
		case 1:
			m_Description = strings[i];
			break;
		case 2:
			m_DrawingName = strings[i];
			break;
		case 3:
			m_Coordinates.m_X = atof(strings[i]);
			break;
		case 4:
			m_Coordinates.m_Y = atof(strings[i]);
			break;
		case 5:
			m_Coordinates.m_Z = atof(strings[i]);
			break;
		case 6:
			// skip region
			break;
		case 7:
			m_UnitOfMeasurement = atoi(strings[i]);
			break;
		case 8:
			// skip slottype
			break;
		case 9:
			m_TimeHorizonDuration = atoi(strings[i]);
			break;
		case 10:
			m_TimeHorizonUnits = atoi(strings[i]);
			break;
		case 11:
			// skip original facility id
			break;
		case 12:
			m_Cost = atof(strings[i]);
			break;
		case 13:
			m_ClientNameOpened = strings[i];
			break;
		case 14:
			m_Notes = strings[i];
			m_Notes.Replace("<nls>", "\r\n");
			break;
		case 15:
			break;		// create date
		case 16:
			break;		// change date
		case 17:
			break;		// last user
		case 18:
			m_BaselineCost = atof(strings[i]);
			break;
		}
	}

	return 0;

}
#if !defined(AFX_SORTLISTBOX_H__404796D7_859A_47AD_912F_909DA69E1A4C__INCLUDED_)
#define AFX_SORTLISTBOX_H__404796D7_859A_47AD_912F_909DA69E1A4C__INCLUDED_

#if _MSC_VER > 1000
#pragma once
#endif // _MSC_VER > 1000
// SortListBox.h : header file
//

/////////////////////////////////////////////////////////////////////////////
// CSortListBox window

class CSortListBox : public CListBox
{
// Construction
public:
	CSortListBox();

// Attributes
public:

// Operations
public:

// Overrides
	// ClassWizard generated virtual function overrides
	//{{AFX_VIRTUAL(CSortListBox)
	public:
	virtual void DrawItem(LPDRAWITEMSTRUCT lpDrawItemStruct);
	virtual int CompareItem(LPCOMPAREITEMSTRUCT lpCompareItemStruct);
	//}}AFX_VIRTUAL

// Implementation
public:
	int (*m_CompareFunction)(void *p1, void *p2);
	CString (*m_GetDrawTextFunction)(void *pItemData);
	virtual ~CSortListBox();

	// Generated message map functions
protected:
	//{{AFX_MSG(CSortListBox)
	//}}AFX_MSG

	DECLARE_MESSAGE_MAP()
};

/////////////////////////////////////////////////////////////////////////////

//{{AFX_INSERT_LOCATION}}
// Microsoft Visual C++ will insert additional declarations immediately before the previous line.

#endif // !defined(AFX_SORTLISTBOX_H__404796D7_859A_47AD_912F_909DA69E1A4C__INCLUDED_)

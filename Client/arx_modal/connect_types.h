/* This contains connect defined datatypes */

#ifndef _CONNECT_TYPES_H_
#define _CONNECT_TYPES_H_



/***********************/
/* Program Constants */
#define DONT_WAIT 0
#define WAIT_FOREVER -1




/***********************/
/* Error Constants */
#define USER_BASE 0xE0000000L

#define SSAMQ_NO_ERROR           0L
#define SSAMQ_INVALID_ARG        (USER_BASE + 1) /* An invalid argument was passed to a functions */
#define SSAMQ_OUT_OF_MEMORY         (USER_BASE + 2) /* The function ran out of memory during processing */
#define SSAMQ_NO_QMGR            (USER_BASE + 3) /* The specified queue manager does not appear to exist */
#define SSAMQ_NO_QUEUE           (USER_BASE + 4) /* The specified queue does not appear to exist */
#define SSAMQ_BAD_DEST_QUEUE     (USER_BASE + 5) /* The specified destination queue does not exist */
#define SSAMQ_SEND_FAILED        (USER_BASE + 6) /* Failed to send data */
#define SSAMQ_NO_MESSAGES        (USER_BASE + 7) /* No messages are available */
#define SSAMQ_NOT_INITIALIZED    (USER_BASE + 8) /* No messages are available */
#define SSAMQ_SUBSCRIBE_FAILED      (USER_BASE + 9) /* Failed to subscribe with the broker */
#define SSAMQ_UNSUBSCRIBE_FAILED (USER_BASE + 10) /* Failed to unsubscribe from the broker */
#define SSAMQ_CONNECTION_BUSY    (USER_BASE + 11) /* Connection is busy - issue one of Commit or Abort */
#define SSAMQ_NO_TRANSACTION     (USER_BASE + 12) /* There is no current transaction */
#define SSAMQ_BAD_XML            (USER_BASE + 13) /* The XML text was malformed */
#define SSAMQ_FIELD_NULL         (USER_BASE + 14) /* The requested field is NULL */
#define SSAMQ_OVERFLOW           (USER_BASE + 15) /* Event or Alert data could not fit within size limits */
#define SSAMQ_BAD_LIST_STRING    (USER_BASE + 16) /* The distribution list string is bad */
#define SSAMQ_NOT_CONNECTED         (USER_BASE + 17)
#define SSAMQ_READQ_NOT_OPEN		(USER_BASE + 18)
#define SSAMQ_EVENTS_NOT_OPEN		(USER_BASE + 19)
#define SSAMQ_ALERTS_NOT_OPEN		(USER_BASE + 20)
#define SSAMQ_NO_RESPONSE_QUEUE		(USER_BASE + 21)
#define SSAMQ_ALREADY_CONNECTED		(USER_BASE + 22)
#define SSAMQ_READ_FAILED			(USER_BASE + 23)
#define SSAMQ_DISCONNECT_FAILED		(USER_BASE + 24)
#define SSAMQ_CLOSE_FAILED			(USER_BASE + 25)
#define SSAMQ_INVALID_URI			(USER_BASE + 26)


#ifdef _LIBBUILD

typedef char * SSAMQCHARPTR;

struct tagConnectionCache
{
	long Magic;

	unsigned long ReadQMRU[15];
	MQHOBJ        ReadQConns[15];
	SSAMQCHARPTR  ReadQNames[15];

	unsigned long WriteQMRU[15];
	MQHOBJ        WriteQConns[15];
	SSAMQCHARPTR  WriteQNames[15];
};

typedef struct tagConnectionCache ConnectionCache;



struct tagMQConnection
{
   /* The magic number set by the allocator */
   int m_Magic;

   /* Various names to keep track of */
   char *m_ChannelName;
   char *m_ConnectName;
   char *m_QManager;

   /* Internal flags */
   int m_IsConnected;
   int m_InWorkUnit;
   long m_LastResponseCode;

   /* Cached MQSeries handles */
   MQHCONN m_hConn;
   ConnectionCache *m_ConnCache;
};

typedef struct tagMQConnection MQConnection;




struct tagDistributionList
{
   int m_Magic;
   char **m_QueueNameList;
   size_t m_Entries;
   size_t m_MaxEntries;
};



typedef struct tagDistributionList DistributionList;



#else



typedef void MQConnection;
typedef void EventXML;
typedef void AlertXML;
typedef void MessageXML;
typedef void DistributionList;
typedef void ConnectionCache;



#endif



struct tagRawOptions
{
	int m_Magic;
	char MessageID[24];
	char CorrelationID[24];
	char *ReplyToQueue;
	char *MessageFormat;
	int MessageType;
	int Priority;
	int MessageOptions;
	int Encoding;
	int Code;
};

typedef struct tagRawOptions RawOptions;

#endif

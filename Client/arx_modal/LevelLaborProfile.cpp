// LevelLaborProfile.cpp: implementation of the CLevelLaborProfile class.
//
//////////////////////////////////////////////////////////////////////

#include "stdafx.h"
#include "modal.h"
#include "LevelLaborProfile.h"
#include "UtilityHelper.h"

#ifdef _DEBUG
#undef THIS_FILE
static char THIS_FILE[]=__FILE__;
#define new DEBUG_NEW
#endif

extern CUtilityHelper utilityHelper;
//////////////////////////////////////////////////////////////////////
// Construction/Destruction
//////////////////////////////////////////////////////////////////////

CLevelLaborProfile::CLevelLaborProfile()
{
	m_LevelLaborProfileDBId = 0;
	m_LevelProfileDBId = 0;
}

CLevelLaborProfile::~CLevelLaborProfile()
{

}

CLevelLaborProfile::CLevelLaborProfile(const CLevelLaborProfile& other)
{
	m_LevelLaborProfileDBId = other.m_LevelLaborProfileDBId;
	m_Description = other.m_Description;
	m_Cube = other.m_Cube;
	m_FixedFactor = other.m_FixedFactor;
	m_VariableFactor = other.m_VariableFactor;
	m_WorkType = other.m_WorkType;
	m_LevelProfileDBId = other.m_LevelProfileDBId;
	
}


CLevelLaborProfile& CLevelLaborProfile::operator=(const CLevelLaborProfile &other)
{	
	m_LevelLaborProfileDBId = other.m_LevelLaborProfileDBId;
	m_Description = other.m_Description;
	m_Cube = other.m_Cube;
	m_FixedFactor = other.m_FixedFactor;
	m_VariableFactor = other.m_VariableFactor;
	m_WorkType = other.m_WorkType;
	m_LevelProfileDBId = other.m_LevelProfileDBId;
	
	return *this;
}

BOOL CLevelLaborProfile::operator==(const CLevelLaborProfile& other)
{
	if (m_LevelLaborProfileDBId != other.m_LevelLaborProfileDBId) return FALSE;
	if (m_Description != other.m_Description) return FALSE;
	if (m_Cube != other.m_Cube) return FALSE;
	if (m_FixedFactor != other.m_FixedFactor) return FALSE;
	if (m_VariableFactor != other.m_VariableFactor) return FALSE;
	if (m_WorkType != other.m_WorkType) return FALSE;
	if (m_LevelProfileDBId != other.m_LevelProfileDBId) return FALSE;
	
	return TRUE;
}

int CLevelLaborProfile::Parse(CString &line)
{
	CStringArray strings;
	utilityHelper.ParseString(line, "|", strings);

	for (int i=0; i < strings.GetSize(); ++i) {
		switch (i) {
		case 0:
			m_LevelLaborProfileDBId = atoi(strings[i]);
			break;
		case 1:
			m_Description = strings[i];
			break;
		case 2:
			m_Cube = atof(strings[i]);
			break;
		case 3:
			m_FixedFactor = atof(strings[i]);
			break;
		case 4:
			m_VariableFactor = atof(strings[i]);
			break;
		case 5:
			m_WorkType = atoi(strings[i]);
			break;
		case 6:
			m_LevelProfileDBId = atoi(strings[i]);
			break;
		}
	}


	return 0;
}
	


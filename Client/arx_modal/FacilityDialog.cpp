// FacilityDialog.cpp : implementation file
//

#include "stdafx.h"
#include "FacilityDialog.h"
#include "HelpService.h"
#include "FacilityDataService.h"
#include "ControlService.h"
#include "UtilityHelper.h"

#ifdef _DEBUG
#define new DEBUG_NEW
#undef THIS_FILE
static char THIS_FILE[] = __FILE__;
#endif


#define SAVE_FACILITY 1
#define OPEN_FACILITY 2
#define DELETE_FACILITY 3
#define	EXPORT_FACILITY 4
#define IMPORT_FACILITY 5

extern CHelpService helpService;
extern CControlService controlService;
extern CUtilityHelper utilityHelper;

/////////////////////////////////////////////////////////////////////////////
// CFacilityDialog dialog

CFacilityDialog::CFacilityDialog(CWnd* pParent /*=NULL*/)
	: CDialog(CFacilityDialog::IDD, pParent)
{
	//{{AFX_DATA_INIT(CFacilityDialog)
	m_FacilityName = _T("");
	m_FacilityNotes = _T("");
	//}}AFX_DATA_INIT

	m_pImageListLarge = NULL;
	m_pImageListSmall = NULL;
	m_PrevFacilityName = "";

	m_FacilityList.RemoveAll();
	m_OpenedByList.RemoveAll();
	m_DateList.RemoveAll();
	m_NotesList.RemoveAll();
	m_SizeList.RemoveAll();

}


void CFacilityDialog::DoDataExchange(CDataExchange* pDX)
{
	CDialog::DoDataExchange(pDX);
	//{{AFX_DATA_MAP(CFacilityDialog)
	DDX_Control(pDX, IDC_FACILITY_NOTES, m_NotesCtrl);
	DDX_Control(pDX, IDC_FACILITY_NAME, m_FacilityNameCtrl);
	DDX_Control(pDX, IDC_FACILITY_LIST, m_FacilityListCtrl);
	DDX_Text(pDX, IDC_FACILITY_NAME, m_FacilityName);
	DDX_Text(pDX, IDC_FACILITY_NOTES, m_FacilityNotes);
	//}}AFX_DATA_MAP
}


BEGIN_MESSAGE_MAP(CFacilityDialog, CDialog)
	//{{AFX_MSG_MAP(CFacilityDialog)
	ON_EN_CHANGE(IDC_FACILITY_NAME, OnChangeFacilityName)
	ON_NOTIFY(NM_CLICK, IDC_FACILITY_LIST, OnClickFacilityList)
	ON_NOTIFY(NM_DBLCLK, IDC_FACILITY_LIST, OnDblclkFacilityList)
	ON_NOTIFY(LVN_ITEMCHANGED, IDC_FACILITY_LIST, OnItemchangedFacilityList)
	ON_BN_CLICKED(IDC_LARGE_ICONS, OnLargeIcons)
	ON_BN_CLICKED(IDC_LIST, OnList)
	ON_BN_CLICKED(IDC_SMALL_ICONS, OnSmallIcons)
	ON_BN_CLICKED(IDC_DETAILS, OnDetails)
	ON_EN_CHANGE(IDC_FACILITY_NOTES, OnChangeFacilityNotes)
	ON_BN_CLICKED(IDC_AUTO_CHECK, OnAutoCheck)
	ON_WM_HELPINFO()
	ON_BN_CLICKED(IDHELP, OnHelp)
	//}}AFX_MSG_MAP
END_MESSAGE_MAP()

/////////////////////////////////////////////////////////////////////////////
// CFacilityDialog message handlers

void CFacilityDialog::OnOK() 
{
	// TODO: Add extra validation here
	UpdateData(TRUE);

	switch (m_Mode) {
	case OPEN_FACILITY:
		if (m_ItemSelected == -1) {
			AfxMessageBox("Please select an existing facility to open.");
			m_FacilityNameCtrl.SetFocus();
			m_FacilityNameCtrl.SetSel(0,-1,FALSE);
			return;
		}
		else {
			int i = m_FacilityListCtrl.GetItemData(m_ItemSelected);
			m_FacilityId = m_FacilityIdList[i];
			m_FacilityName = m_FacilityList[i];
		}
		break;
	case EXPORT_FACILITY:
		if (m_ItemSelected == -1) {
			AfxMessageBox("Please select an existing facility to export.");
			m_FacilityNameCtrl.SetFocus();
			m_FacilityNameCtrl.SetSel(0,-1,FALSE);
			return;
		}
		else {
			int i = m_FacilityListCtrl.GetItemData(m_ItemSelected);
			m_FacilityId = m_FacilityIdList[i];
			m_FacilityName = m_FacilityList[i];
		}
		break;
	case DELETE_FACILITY:
		if (m_ItemSelected == -1) {
			AfxMessageBox("Please select an existing facility to delete.");
			m_FacilityNameCtrl.SetFocus();
			m_FacilityNameCtrl.SetSel(0,-1,FALSE);
			return;
		}
		else {
			int i = m_FacilityListCtrl.GetItemData(m_ItemSelected);
			m_FacilityId = m_FacilityIdList[i];
			m_FacilityName = m_FacilityList[i];
		}
		break;
		
	case SAVE_FACILITY:
		if (m_ItemSelected != -1) {
			AfxMessageBox("The facility you have chosen already exists.\nPlease enter a new facility name.");
			m_FacilityNameCtrl.SetFocus();
			m_FacilityNameCtrl.SetSel(0,-1,FALSE);
			return;
		}
		else {
			m_FacilityId = 0;
		}
		break;
		
	default:
		break;
	}
	
	CString temp = "";
	if (m_AutoDatabase != "") {
		temp = m_AutoDatabase;
		temp += "|";
		if (m_AutoFacility != "") {
			temp += m_AutoFacility;
		}
	} 
	else {
		if (m_AutoFacility != "") {
			temp += "|";
			temp += m_AutoFacility;
		}
	}

	controlService.SetApplicationData("AutoLoginParameters", temp);
		
	CDialog::OnOK();
}

BOOL CFacilityDialog::OnInitDialog() 
{
	CDialog::OnInitDialog();
	CFacilityDataService facilityDataService;
	CString header, temp;

	CWaitCursor cwc;

	m_FacilityList.RemoveAll();
	m_OpenedByList.RemoveAll();
	m_DateList.RemoveAll();
	m_NotesList.RemoveAll();
	m_SizeList.RemoveAll();
	
	m_OrigNotes = m_FacilityNotes;
	m_OrigNotes.Replace("<nl>", "\r\n");

	switch (m_Mode) {
	case SAVE_FACILITY: 
		header = "Save Facility";
		break;
	case OPEN_FACILITY:
		header = "Open Facility";
		break;
	case DELETE_FACILITY:
		header = "Delete Facility";
		break;
	case EXPORT_FACILITY:
		header = "Export Facility";
		break;
	case IMPORT_FACILITY:
		header = "Import Facility";
		break;
	default:
		header = "";
		break;
	}

	header += " - " + controlService.m_CurrentDatabase;
	SetWindowText(header);

	CStringArray tmpFacilityList;
	try {
		facilityDataService.GetFacilityDetailList(tmpFacilityList);
	}
	catch(...) {
		AfxMessageBox("Error finding facilities. Please exit, check the services\nand restart the application.");
		EndDialog(IDCANCEL);
		return TRUE;
	}

	CString notesStr;
	CStringArray strings;

	for (int i=0; i < tmpFacilityList.GetSize(); ++i) {
		utilityHelper.ParseString(tmpFacilityList[i], "|", strings);

		m_FacilityIdList[i] = atoi(strings[0]);
		m_FacilityList.Add(strings[1]);
		
		m_DateList.Add(strings[2]);
		m_OpenedByList.Add(strings[3]);
		notesStr = strings[4];
		notesStr.Replace("<nl>", "\r\n");
		m_NotesList.Add(notesStr);
		m_SizeList.Add(strings[5]);

	}

    m_pImageListLarge = new CImageList();   // image list for icon view 
    m_pImageListSmall = new CImageList();   // image list for other views 
 
	m_pImageListLarge->Create(32,32,TRUE,4 ,4);
    m_pImageListSmall->Create(16,16, TRUE, 4,4); 
 
    // Add an icon to each image list. 
    AfxGetApp()->LoadIcon(IDI_SUCCEEDICON); 
    m_pImageListLarge->Add(AfxGetApp()->LoadIcon(IDI_SUCCEEDICON)); 
    m_pImageListSmall->Add(AfxGetApp()->LoadIcon(IDI_SUCCEEDICON)); 


	FillListCtrl();

	LoadButtonBitmaps();
	LoadToolTips();

	CButton *pButton = (CButton *)GetDlgItem(IDC_LIST);
	pButton->SetCheck(1);

	CEdit *pEdit = (CEdit *)GetDlgItem(IDC_FACILITY_NAME);
	pEdit->SetFocus();

	temp = controlService.GetApplicationData("AutoLoginParameters");
	if (temp != "") {
		if (temp.Find("|") > 0) {		// both database and facility specified
			m_AutoDatabase = temp.Left(temp.Find("|"));
			m_AutoFacility = temp.Mid(temp.Find("|")+1);
		}
		else if (temp.Find("|") == 0) {	// if first char is | then only facility specified
			m_AutoDatabase = "";
			m_AutoFacility = temp.Mid(temp.Find("|")+1);
		}
		else {							// if no | and string not blank, only database specified
			m_AutoDatabase = temp;
			m_AutoFacility = "";
		}
	}
	else {
		m_AutoDatabase = "";
		m_AutoFacility = "";
	}

	if (m_Mode != SAVE_FACILITY && m_Mode != EXPORT_FACILITY) {
		m_NotesCtrl.SetReadOnly(TRUE);
		m_FacilityNotes = m_OrigNotes;
	}
	else {
		m_NotesCtrl.SetReadOnly(FALSE);
		m_FacilityNotes = m_OrigNotes;
	}

	return FALSE;

	//return TRUE;  // return TRUE unless you set the focus to a control
	              // EXCEPTION: OCX Property Pages should return FALSE
}


void CFacilityDialog::OnClickFacilityList(NMHDR* pNMHDR, LRESULT* pResult) 
{

	UNREFERENCED_PARAMETER(pNMHDR);

	UpdateData(TRUE);
	POSITION pos = m_FacilityListCtrl.GetFirstSelectedItemPosition();

	if (pos != NULL) {
		int nItem = m_FacilityListCtrl.GetNextSelectedItem(pos);
		m_ItemSelected = nItem;
		int i = m_FacilityListCtrl.GetItemData(nItem);
		m_FacilityName = m_FacilityList[i];
		m_FacilityNotes = m_NotesList[i];
		m_PrevFacilityName = m_FacilityName;
		UpdateData(FALSE);				// populate facility name edit box
	}

	
	*pResult = 0;
}

void CFacilityDialog::OnDblclkFacilityList(NMHDR* pNMHDR, LRESULT* pResult) 
{
	UNREFERENCED_PARAMETER(pNMHDR);
	UpdateData(TRUE);
	POSITION pos = m_FacilityListCtrl.GetFirstSelectedItemPosition();

	if (pos != NULL) {
		int nItem = m_FacilityListCtrl.GetNextSelectedItem(pos);
		m_ItemSelected = nItem;
		int i = m_FacilityListCtrl.GetItemData(nItem);
		m_FacilityName = m_FacilityList[i];
		m_FacilityNotes = m_NotesList[i];
		m_PrevFacilityName = m_FacilityName;
		UpdateData(FALSE);				// populate facility name edit box
	}

	*pResult = 0;

	OnOK();

}


void CFacilityDialog::OnChangeFacilityName() 
{
	UpdateData(TRUE);
	
	int nItem, i;

	if (m_Mode == SAVE_FACILITY)
		return;


	if (m_FacilityName == "") {
		if (m_ItemSelected > -1) {
			m_FacilityListCtrl.SetItemState(m_ItemSelected, 0, LVIS_SELECTED);
			m_ItemSelected = -1;
		}
		m_PrevFacilityName = "";
		return;
	}

	CString currentName = m_FacilityName;

	// If the current name is contained in the previous 
	// and is not equal to the previous name then
	// they must be backing up so don't keep highlighting the
	// selection
	if (m_PrevFacilityName.Find(currentName) == 0) { 
		nItem = MatchWithCase(currentName);
		if (m_FacilityListCtrl.GetItemText(nItem, 0) != currentName) {
			m_FacilityListCtrl.SetItemState(m_ItemSelected, 0, LVIS_SELECTED);
			m_ItemSelected = -1;
		}
		else {
			m_ItemSelected = nItem;
			m_FacilityListCtrl.SetItemState(nItem, LVIS_SELECTED, LVIS_SELECTED);
		}
		m_PrevFacilityName = currentName;
		UpdateData(FALSE);
		return;
	}


	m_PrevFacilityName = currentName;
	nItem = MatchWithCase(currentName);
	if (nItem >= 0) {
		i = m_FacilityListCtrl.GetItemData(nItem);
		m_ItemSelected = nItem;
		m_FacilityName = m_FacilityList[i];
		UpdateData(FALSE);
		if (currentName.GetLength() != m_FacilityName.GetLength())		
			m_FacilityNameCtrl.SetSel(currentName.GetLength(), m_FacilityName.GetLength());
		m_FacilityListCtrl.SetItemState(nItem, LVIS_SELECTED, LVIS_SELECTED);
	}
	else {		// item does not match (regardless of what FindItem seems to think)
		if (m_ItemSelected > -1) {	// de-select any existing selection
			m_FacilityListCtrl.SetItemState(m_ItemSelected, 0, LVIS_SELECTED);
			m_ItemSelected = -1;
		}
	}
	
	UpdateData(FALSE);

	return;

}

CFacilityDialog::~CFacilityDialog()
{
	if (m_pImageListLarge != NULL)
		delete m_pImageListLarge;

	if (m_pImageListSmall != NULL)
		delete m_pImageListSmall;
}


void CFacilityDialog::OnItemchangedFacilityList(NMHDR* pNMHDR, LRESULT* pResult) 
{
	NM_LISTVIEW* pNMListView = (NM_LISTVIEW*)pNMHDR;
	CButton *pCheckBtn = (CButton *)GetDlgItem(IDC_AUTO_CHECK);

	POSITION pos = m_FacilityListCtrl.GetFirstSelectedItemPosition();

	if (pos != NULL) {
		int nItem = m_FacilityListCtrl.GetNextSelectedItem(pos);
		m_ItemSelected = nItem;
		int i = m_FacilityListCtrl.GetItemData(nItem);
		m_FacilityName = m_FacilityList[i];

		if (m_FacilityIdList[i] == atoi(m_AutoFacility))
			pCheckBtn->SetCheck(1);
		else
			pCheckBtn->SetCheck(0);
		m_FacilityNotes = m_NotesList[i];
	}
	else {
		m_ItemSelected = -1;
		pCheckBtn->SetCheck(0);
		m_FacilityNotes = m_OrigNotes;
	}
	
	UpdateData(FALSE);

	*pResult = 0;
}


void CFacilityDialog::OnLargeIcons() 
{
	// TODO: Add your control notification handler code here
	
	long lStyleOld, lStyle;
	lStyle = LVS_ICON;

	lStyleOld = GetWindowLong(m_FacilityListCtrl.m_hWnd, GWL_STYLE);
	lStyleOld &= ~(LVS_TYPEMASK);  // turn off all the style (view mode) bits
	lStyleOld |= lStyle;        // Set the new style for the control
	SetWindowLong(m_FacilityListCtrl.m_hWnd, GWL_STYLE, lStyleOld);
	m_FacilityListCtrl.Arrange(LVS_ALIGNLEFT);

/*
	RenewListCtrl(lStyleOld, 1);


	long dwStyle = 0;
	long dwExtStyles;
	CRect rect;

	dwStyle |= WS_VISIBLE|WS_TABSTOP|WS_BORDER;
	dwStyle |= LVS_ICON|LVS_SORTASCENDING|LVS_ALIGNLEFT|LVS_SHOWSELALWAYS|LVS_SINGLESEL;
	m_FacilityListCtrl.GetWindowRect(&rect);
	ScreenToClient(&rect);
	dwExtStyles= m_FacilityListCtrl.GetExtendedStyle(); //save extended styles
	m_FacilityListCtrl.DestroyWindow();
	m_FacilityListCtrl.Create(dwStyle, rect, this, IDC_FACILITY_LIST);
	m_FacilityListCtrl.ModifyStyleEx(0,WS_EX_CLIENTEDGE); // renew the 3D border of the control
	m_FacilityListCtrl.SetExtendedStyle(dwExtStyles);

	FillListCtrl(); // repopulate with a new item group

	UpdateData(FALSE);  // update the dialog
*/
}

void CFacilityDialog::OnList() 
{
	// TODO: Add your control notification handler code here
	long lStyleOld, lStyle;
	lStyle = LVS_LIST;

	lStyleOld = GetWindowLong(m_FacilityListCtrl.m_hWnd, GWL_STYLE);
	lStyleOld &= ~(LVS_TYPEMASK);  // turn off all the style (view mode) bits
	lStyleOld |= lStyle;        // Set the new style for the control
	SetWindowLong(m_FacilityListCtrl.m_hWnd, GWL_STYLE, lStyleOld);
	
}


void CFacilityDialog::OnSmallIcons() 
{
	// TODO: Add your control notification handler code here

	long lStyleOld, lStyle;
	lStyle = LVS_SMALLICON;

	lStyleOld = GetWindowLong(m_FacilityListCtrl.m_hWnd, GWL_STYLE);
	lStyleOld &= ~(LVS_TYPEMASK);  // turn off all the style (view mode) bits
	lStyleOld |= lStyle;        // Set the new style for the control
	SetWindowLong(m_FacilityListCtrl.m_hWnd, GWL_STYLE, lStyleOld);
	m_FacilityListCtrl.Arrange(LVS_ALIGNLEFT);

/*
	RenewListCtrl(lStyleOld,1);

	long dwStyle = 0;
	long dwExtStyles;
	CRect rect;
	dwStyle = WS_VISIBLE|WS_TABSTOP|WS_BORDER;
	dwStyle |= LVS_SMALLICON|LVS_SORTASCENDING|LVS_ALIGNTOP|LVS_SHOWSELALWAYS|LVS_SINGLESEL|LVS_AUTOARRANGE;
	m_FacilityListCtrl.GetWindowRect(&rect);
	ScreenToClient(&rect);
	dwExtStyles= m_FacilityListCtrl.GetExtendedStyle(); //save extended styles
	m_FacilityListCtrl.DestroyWindow();
	m_FacilityListCtrl.Create(dwStyle, rect, this, IDC_FACILITY_LIST);
	m_FacilityListCtrl.ModifyStyleEx(0,WS_EX_CLIENTEDGE); // renew the 3D border of the control
	m_FacilityListCtrl.SetExtendedStyle(dwExtStyles);

	FillListCtrl(); // repopulate with a new item group

	m_FacilityListCtrl.Arrange(LVS_ALIGNLEFT);

	UpdateData(FALSE);  // update the dialog
*/
}

// brd - not sure if this will be need or not, copied from sample program
void CFacilityDialog::RenewListCtrl(DWORD dwStyle, BOOL bSetBits)
{


	DWORD   dwStyleOld, dwExtStyles;
	CRect   rect;

	dwStyleOld = GetWindowLong(m_FacilityListCtrl.m_hWnd, GWL_STYLE);
	if (bSetBits)
		dwStyleOld |= dwStyle;   // turn on bits specified by caller.
	else
		dwStyleOld &= ~dwStyle;  // turn off bits specified by caller.


	m_FacilityListCtrl.GetWindowRect(&rect);
	ScreenToClient(&rect);
	dwExtStyles= m_FacilityListCtrl.GetExtendedStyle(); //save extended styles
	m_FacilityListCtrl.DestroyWindow();
	m_FacilityListCtrl.Create(dwStyleOld, rect, this, IDC_FACILITY_LIST);
	m_FacilityListCtrl.ModifyStyleEx(0,WS_EX_CLIENTEDGE); // renew the 3D border of the control
	m_FacilityListCtrl.SetExtendedStyle(dwExtStyles);

	FillListCtrl(); // repopulate with a new item group

	UpdateData(FALSE);  // update the dialog


}

void CFacilityDialog::FillListCtrl()
{

	CRect rect;
	LVITEM lvItem;
   	CWinApp *currentApp;
	currentApp = AfxGetApp();

	m_FacilityListCtrl.SetImageList(m_pImageListLarge, LVSIL_NORMAL); 
	m_FacilityListCtrl.SetImageList(m_pImageListSmall, LVSIL_SMALL); 

	m_FacilityListCtrl.GetWindowRect(&rect);
	m_FacilityListCtrl.InsertColumn(0, "Name", LVCFMT_LEFT, rect.Width()*1/2, 0);
	m_FacilityListCtrl.InsertColumn(1, "Size", LVCFMT_LEFT, rect.Width()*1/4, 1);
	m_FacilityListCtrl.InsertColumn(2, "Modified", LVCFMT_LEFT, rect.Width()*1/3, 2);
	m_FacilityListCtrl.InsertColumn(3, "Opened By", LVCFMT_LEFT, rect.Width()*1/4, 3);

	int nItem;

	for (int i=0; i < m_FacilityList.GetSize(); ++i) {
		
		lvItem.mask = LVIF_TEXT | LVIF_IMAGE;
		lvItem.iItem = i;
		lvItem.iSubItem = 0;
		lvItem.pszText = m_FacilityList[i].GetBuffer(0);
		m_FacilityList[i].ReleaseBuffer();
		lvItem.iImage = 0;

		nItem = m_FacilityListCtrl.InsertItem(&lvItem);
		m_FacilityListCtrl.SetItemData(nItem, i);

		lvItem.mask = LVIF_TEXT;
		lvItem.iItem = nItem;
		lvItem.iSubItem = 1;
		lvItem.pszText = m_SizeList[i].GetBuffer(0);
		m_SizeList[i].ReleaseBuffer();
		m_FacilityListCtrl.SetItem(&lvItem);

		lvItem.iSubItem = 2;
		lvItem.pszText = m_DateList[i].GetBuffer(0);
		m_DateList[i].ReleaseBuffer();
		m_FacilityListCtrl.SetItem(&lvItem);

		lvItem.iSubItem = 3;
		lvItem.pszText = m_OpenedByList[i].GetBuffer(0);
		m_OpenedByList[i].ReleaseBuffer();
		m_FacilityListCtrl.SetItem(&lvItem);

	}

	UpdateData(FALSE);

	m_ItemSelected = -1;

	return;
}

void CFacilityDialog::LoadButtonBitmaps()
{
	CButton *pButton;
	CBitmap bitmap;

	bitmap.LoadBitmap(IDB_LARGE_ICONS);
	pButton = (CButton *)GetDlgItem(IDC_LARGE_ICONS);
	pButton->SetBitmap((HBITMAP)bitmap.Detach());

	bitmap.LoadBitmap(IDB_SMALL_ICONS);
	pButton = (CButton *)GetDlgItem(IDC_SMALL_ICONS);
	pButton->SetBitmap((HBITMAP)bitmap.Detach());
		
	bitmap.LoadBitmap(IDB_DETAILS);
	pButton = (CButton *)GetDlgItem(IDC_DETAILS);
	pButton->SetBitmap((HBITMAP)bitmap.Detach());

	bitmap.LoadBitmap(IDB_LIST);
	pButton = (CButton *)GetDlgItem(IDC_LIST);
	pButton->SetBitmap((HBITMAP)bitmap.Detach());


	return;


}


void CFacilityDialog::OnDetails() 
{
	// TODO: Add your control notification handler code here
	long lStyleOld, lStyle;
	lStyle = LVS_REPORT;

	lStyleOld = GetWindowLong(m_FacilityListCtrl.m_hWnd, GWL_STYLE);
	lStyleOld &= ~(LVS_TYPEMASK);  // turn off all the style (view mode) bits
	lStyleOld |= lStyle;        // Set the new style for the control
	SetWindowLong(m_FacilityListCtrl.m_hWnd, GWL_STYLE, lStyleOld);	

}

void CFacilityDialog::LoadToolTips()
{

	EnableToolTips(TRUE);

	m_ToolTip.Create(this, TTF_IDISHWND);
	m_ToolTip.Activate(TRUE);
	
	m_ToolTip.AddTool(GetDlgItem(IDC_LARGE_ICONS),"Large Icons");
	m_ToolTip.AddTool(GetDlgItem(IDC_SMALL_ICONS),"Small Icons");
	m_ToolTip.AddTool(GetDlgItem(IDC_LIST),"List");
	m_ToolTip.AddTool(GetDlgItem(IDC_DETAILS),"Details");

}


BOOL CFacilityDialog::PreTranslateMessage(MSG* pMsg) 
{
	// TODO: Add your specialized code here and/or call the base class
	
	if(pMsg->message== WM_LBUTTONDOWN ||
        pMsg->message== WM_LBUTTONUP ||
        pMsg->message== WM_MOUSEMOVE)
		m_ToolTip.RelayEvent(pMsg);


	return CDialog::PreTranslateMessage(pMsg);
}


void CFacilityDialog::OnChangeFacilityNotes() 
{
	UpdateData(TRUE);
	m_OrigNotes = m_FacilityNotes;
	
}


void CFacilityDialog::OnAutoCheck() 
{
	CButton *pCheckBtn = (CButton *)GetDlgItem(IDC_AUTO_CHECK);
	POSITION pos;
	CString id = "";

	pos = m_FacilityListCtrl.GetFirstSelectedItemPosition();
	if (pos != NULL) {
		int curSel = m_FacilityListCtrl.GetNextSelectedItem(pos);
		int i = m_FacilityListCtrl.GetItemData(curSel);
		id.Format("%d", m_FacilityIdList[i]);
	}


	if (pCheckBtn->GetCheck()) {
		if (id == "") {
			AfxMessageBox("Please select a facility from the list.");
			pCheckBtn->SetCheck(0);
		}
		else {
			m_AutoFacility = id;
		}
	}
	else
		m_AutoFacility = "";

	return;

}

int CFacilityDialog::MatchWithCase(CString &str)
{
	int nItem, i;
	LVFINDINFO *pFindInfo = new LVFINDINFO;
	pFindInfo->flags = LVFI_STRING|LVFI_PARTIAL;
	pFindInfo->psz = str;
	CString matchName;

	nItem = m_FacilityListCtrl.FindItem(pFindInfo, -1);
	while (nItem >= 0) {
		i = m_FacilityListCtrl.GetItemData(nItem);
		matchName = m_FacilityList[i];
		if (matchName.Find(str, 0) == 0)
			return nItem;
		nItem = m_FacilityListCtrl.FindItem(pFindInfo, nItem);
	}

	delete pFindInfo;

	return -1;

}

BOOL CFacilityDialog::OnHelpInfo(HELPINFO* pHelpInfo) 
{
	helpService.ShowFieldHelp(IDD, pHelpInfo->iCtrlId);
	return FALSE;
}

void CFacilityDialog::OnHelp() 
{
	helpService.ShowScreenHelp(IDD);	
}

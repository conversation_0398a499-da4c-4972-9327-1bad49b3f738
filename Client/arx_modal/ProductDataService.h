// ProductDataService.h: interface for the CProductDataService class.
//
//////////////////////////////////////////////////////////////////////

#if !defined(AFX_PRODUCTDATASERVICE_H__11164893_251F_11D5_9ECA_00C04FAC149C__INCLUDED_)
#define AFX_PRODUCTDATASERVICE_H__11164893_251F_11D5_9ECA_00C04FAC149C__INCLUDED_

#if _MSC_VER > 1000
#pragma once
#endif // _MSC_VER > 1000

#include "ProductPack.h"
#include "UDF.h"
#include "ProductAttribute.h"
// ProductInfo is the old one used by manual assignment - it will be replaced by product pack eventually
#include "ProductInfo.h"
class CProductDataService : public CObject  
{
public:
	CProductDataService();
	virtual ~CProductDataService();

	static UINT DeleteProductsByFacilityThread(LPVOID pParam);
	int UpdateProductGroup(long productDBID, long newProductGroupDBID, long oldProductGroupDBID);
	int RunDataModel(int quantity, CStringArray &attributes);
	int DeleteProductByID(int productDBID);
	int QueryProductsAll(CString &sql, CStringArray &productList);
	int GetProductAttributes(int facilityDBId, CStringArray &attributes);
	int GetProductFacings(long productId, CStringArray &facings, int origin);
	int GetBayHandlesForProducts(CStringArray &dbids, CStringArray &handles);
	void AddQueryAttribute(qqhSLOTQuery &query, const CString &objectName, const CString &fieldName, const CString &fieldValue, const CString &oper);
	int BuildQuery(CString &sql, qqhSLOTQuery &query);
	int Delete(long productDBID);
	int Update(CProductPack &product);
	int LoadProductAttributes();
	int InitProductAttributes();
	int GetProductByID(long productDBID, CStringArray &productList);
	int GetProductListByQuery(qqhSLOTQuery &query, CStringArray &productList);
	void AddQueryAttribute(qqhSLOTQuery &query, const CString &objectName, 
							const CString &fieldName, const CString &fieldValue, int type);

	CTypedPtrArray<CObArray, CProductAttribute*> m_ProductAttributeList;
	CTypedPtrArray<CObArray, CUDF*> m_UDFList;
	CMapStringToOb m_ProductAttributeMap;
	CMapStringToString m_OperatorMap;

	typedef enum {
		Baseline = 0,
		Optimize = 1
	} enumOrigin;

private:
	int BuildQueryByID(CString &sql, long productID);
	CMapStringToString m_AttributeTypeMap;
};

#endif // !defined(AFX_PRODUCTDATASERVICE_H__11164893_251F_11D5_9ECA_00C04FAC149C__INCLUDED_)

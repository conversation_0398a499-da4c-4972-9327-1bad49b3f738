#if !defined(AFX_PRODUCTLAYOUTSTARTPAGE_H__0C383F55_76BB_4123_84F2_0DE9AF75AD2D__INCLUDED_)
#define AFX_PRODUCTLAYOUTSTARTPAGE_H__0C383F55_76BB_4123_84F2_0DE9AF75AD2D__INCLUDED_

#if _MSC_VER > 1000
#pragma once
#endif // _MSC_VER > 1000
// ProductLayoutStartPage.h : header file
//

/////////////////////////////////////////////////////////////////////////////
// CProductLayoutStartPage dialog

class CProductLayoutStartPage : public CPropertyPage
{
	DECLARE_DYNCREATE(CProductLayoutStartPage)

// Construction
public:
	CProductLayoutStartPage();
	~CProductLayoutStartPage();

// Dialog Data
	//{{AFX_DATA(CProductLayoutStartPage)
	enum { IDD = IDD_LAYOUT_PRODUCTS };
	double	m_ConstrainAmount;
	BOOL	m_Rotate;
	BOOL	m_VariableWidth;
	BOOL	m_ChangedOnly;
	BOOL	m_slotnew;
	BOOL	m_reslot;
	BOOL	m_findswaps;
	//}}AFX_DATA
	int m_ConstrainType;
	int	m_LayoutType;

// Overrides
	// ClassWizard generate virtual function overrides
	//{{AFX_VIRTUAL(CProductLayoutStartPage)
	public:
	virtual BOOL OnKillActive();
	protected:
	virtual void DoDataExchange(CDataExchange* pDX);    // DDX/DDV support
	virtual BOOL OnNotify(WPARAM wParam, LPARAM lParam, LRESULT* pResult);
	//}}AFX_VIRTUAL

// Implementation
protected:
	// Generated message map functions
	//{{AFX_MSG(CProductLayoutStartPage)
	virtual BOOL OnInitDialog();
	afx_msg void OnStrategic();
	afx_msg void OnGroup();
	afx_msg void OnTactical();
	afx_msg void OnManual();
	afx_msg BOOL OnHelpInfo(HELPINFO* pHelpInfo);
	//}}AFX_MSG
	DECLARE_MESSAGE_MAP()
	void ChangeProductLayoutMode(int mode);

};

//{{AFX_INSERT_LOCATION}}
// Microsoft Visual C++ will insert additional declarations immediately before the previous line.

#endif // !defined(AFX_PRODUCTLAYOUTSTARTPAGE_H__0C383F55_76BB_4123_84F2_0DE9AF75AD2D__INCLUDED_)

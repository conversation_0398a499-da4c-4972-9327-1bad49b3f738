// ProfileMaintenanceView.cpp : implementation file
//

#include "stdafx.h"
#include "modal.h"
#include "ProfileMaintenanceView.h"
#include "ProfileFrame.h"

#include <dbsymtb.h>

#ifdef _DEBUG
#define new DEBUG_NEW
#undef THIS_FILE
static char THIS_FILE[] = __FILE__;
#endif

/////////////////////////////////////////////////////////////////////////////
// CProfileMaintenanceView

extern CProfileFrame *m_ProfileFrame;

IMPLEMENT_DYNCREATE(CProfileMaintenanceView, CListView)

CProfileMaintenanceView::CProfileMaintenanceView()
{
	m_pSheet = new CProfileMaintenanceSheet("Profile Maintenance", this, 0);

}

CProfileMaintenanceView::~CProfileMaintenanceView()
{
}


BEGIN_MESSAGE_MAP(CProfileMaintenanceView, CListView)
	//{{AFX_MSG_MAP(CProfileMaintenanceView)
	ON_WM_CREATE()
	ON_WM_SIZE()
	ON_WM_DESTROY()
	//}}AFX_MSG_MAP
END_MESSAGE_MAP()

/////////////////////////////////////////////////////////////////////////////
// CProfileMaintenanceView diagnostics

#ifdef _DEBUG
void CProfileMaintenanceView::AssertValid() const
{
	CListView::AssertValid();
}

void CProfileMaintenanceView::Dump(CDumpContext& dc) const
{
	CListView::Dump(dc);
}
#endif //_DEBUG

/////////////////////////////////////////////////////////////////////////////
// CProfileMaintenanceView message handlers

int CProfileMaintenanceView::OnCreate(LPCREATESTRUCT lpCreateStruct) 
{
	if (CListView::OnCreate(lpCreateStruct) == -1)
		return -1;
	
	m_pSheet->Create(this, WS_CHILD|WS_VISIBLE);
	
	return 0;
}

void CProfileMaintenanceView::OnSize(UINT nType, int cx, int cy) 
{
	CListView::OnSize(nType, cx, cy);
	
	
	m_pSheet->MoveWindow(CRect(0, 0, cx, cy));
	m_pSheet->GetTabControl()->MoveWindow(0,0,cx,cy);
	
	m_ProfileFrame->Invalidate(TRUE);
}

void CProfileMaintenanceView::OnDestroy() 
{
	CListView::OnDestroy();
	
	m_pSheet->DestroyWindow();
	
}

void CProfileMaintenanceView::OnDraw(CDC* pDC) 
{
	ads_printf("Got draw\n");
//	m_ProfileFrame->m_Splitter.RecalcLayout();


}

void CProfileMaintenanceView::OnUpdate(CView* pSender, LPARAM lHint, CObject* pHint) 
{

	ads_printf("IN onupdate\n");

	this->Invalidate(TRUE);
	
}

// ProductGroupConstraint.h: interface for the CProductGroupConstraint class.
//
//////////////////////////////////////////////////////////////////////

#if !defined(AFX_PRODUCTGROUPCONSTRAINT_H__A4E0968D_14BF_4D71_A89C_E36E9A182536__INCLUDED_)
#define AFX_PRODUCTGROUPCONSTRAINT_H__A4E0968D_14BF_4D71_A89C_E36E9A182536__INCLUDED_

#if _MSC_VER > 1000
#pragma once
#endif // _MSC_VER > 1000

class CProductGroupConstraint : public CObject  
{
public:
	BOOL IsChild(CProductGroupConstraint &other);
	BOOL IsParent(CProductGroupConstraint &other);
	BOOL IsEqual(CProductGroupConstraint &other);
	int Parse(CString &line);
	CProductGroupConstraint();
	virtual ~CProductGroupConstraint();
	CProductGroupConstraint& operator=(const CProductGroupConstraint &other);
	long m_ProductGroupConstraintDBID;
	CString m_SectionDescription;
	long m_SectionDBID;
	CString m_AisleDescription;
	long m_AisleDBID;
	CString m_SideDescription;
	long m_SideDBID;
	CString m_BayDescription;
	long m_BayDBID;
	int m_RelativeLevel;
	BOOL m_IsExclusive;
	int m_ProductGroupDBID;
};

#endif // !defined(AFX_PRODUCTGROUPCONSTRAINT_H__A4E0968D_14BF_4D71_A89C_E36E9A182536__INCLUDED_)

#if !defined(AFX_LOCATIONATTRIBUTESPAGE_H__ABCA1D2D_ADCF_4A4C_8E05_8CDBCE8A3EC7__INCLUDED_)
#define AFX_LOCATIONATTRIBUTESPAGE_H__ABCA1D2D_ADCF_4A4C_8E05_8CDBCE8A3EC7__INCLUDED_

#include "LevelProfile.h"	// Added by ClassView
#include "ExternalSystem.h"
#include "qqhclasses.h"

#if _MSC_VER > 1000
#pragma once
#endif // _MSC_VER > 1000
// LocationAttributesPage.h : header file
//

/////////////////////////////////////////////////////////////////////////////
// CLocationAttributesPage dialog

class CLocationAttributesPage : public CPropertyPage
{
	DECLARE_DYNCREATE(CLocationAttributesPage)

// Construction
public:
	qqhSLOTLocation *m_pLocation;
	CLocationAttributesPage();
	~CLocationAttributesPage();

// Dialog Data
	//{{AFX_DATA(CLocationAttributesPage)
	enum { IDD = IDD_LOCATION_ATTRIBUTES };
	CScrollBar	m_ScrollBar;
	CButton	m_GroupButton;
	CComboBox	m_ExternalListCtrl;
	//}}AFX_DATA


// Overrides
	// ClassWizard generate virtual function overrides
	//{{AFX_VIRTUAL(CLocationAttributesPage)
	public:
	virtual BOOL OnSetActive();
	virtual BOOL OnKillActive();
	protected:
	virtual void DoDataExchange(CDataExchange* pDX);    // DDX/DDV support
	virtual BOOL OnNotify(WPARAM wParam, LPARAM lParam, LRESULT* pResult);
	//}}AFX_VIRTUAL

// Implementation
protected:
	// Generated message map functions
	//{{AFX_MSG(CLocationAttributesPage)
	afx_msg void OnSelchangeExternalSystemList();
	virtual BOOL OnInitDialog();
	afx_msg void OnVScroll(UINT nSBCode, UINT nPos, CScrollBar* pScrollBar);
	afx_msg BOOL OnHelpInfo(HELPINFO* pHelpInfo);
	//}}AFX_MSG
	DECLARE_MESSAGE_MAP()

private:
	void SetScrollSizes();
	int UpdateLocationFromScreen();
	int UpdateScreenFromLocation();
	CTypedPtrArray<CObArray, CStatic*> m_Labels;
	CTypedPtrArray<CObArray, CWnd*> m_InputControls;
	int m_ScrollPos;
	int CreateAttributeDisplay();
	int m_CurrentSystemId;
	CTypedPtrArray<CObArray, CExternalSystem*> m_ExternalSystemList;

};

//{{AFX_INSERT_LOCATION}}
// Microsoft Visual C++ will insert additional declarations immediately before the previous line.

#endif // !defined(AFX_LOCATIONATTRIBUTESPAGE_H__ABCA1D2D_ADCF_4A4C_8E05_8CDBCE8A3EC7__INCLUDED_)

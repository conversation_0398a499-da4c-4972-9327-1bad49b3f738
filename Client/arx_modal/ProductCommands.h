// ProductCommands.h: interface for the CProductCommands class.
//
//////////////////////////////////////////////////////////////////////

#if !defined(AFX_PRODUCTCOMMANDS_H__5CA8F939_DF6E_4D28_B1EE_F28609653E30__INCLUDED_)
#define AFX_PRODUCTCOMMANDS_H__5CA8F939_DF6E_4D28_B1EE_F28609653E30__INCLUDED_

#include "Commands.h"

#if _MSC_VER > 1000
#pragma once
#endif // _MSC_VER > 1000

class CProductCommands : public CCommands
{
public:
	CProductCommands();
	virtual ~CProductCommands();

	static void RegisterCommands();
	static void DataModel();
	static void DataPurification();
	static void ProductMaintenance();
	static void PopulateUDFWithFormula();
	static void DeleteProductsByFacility();
};

#endif // !defined(AFX_PRODUCTCOMMANDS_H__5CA8F939_DF6E_4D28_B1EE_F28609653E30__INCLUDED_)

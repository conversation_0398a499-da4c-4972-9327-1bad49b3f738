#if !defined(AFX_LOGINDLG_H__3F7DD5D0_20BF_11D2_A47C_00C04FA8700B__INCLUDED_)
#define AFX_LOGINDLG_H__3F7DD5D0_20BF_11D2_A47C_00C04FA8700B__INCLUDED_
#include "resource.h"
#if _MSC_VER >= 1000
#pragma once
#endif // _MSC_VER >= 1000
// LoginDlg.h : header file
//

/////////////////////////////////////////////////////////////////////////////
// CLoginDlg dialog

class CLoginDlg : public CDialog
{
// Construction
public:
	CStringArray m_databaseList;
	CLoginDlg(CWnd* pParent = NULL);   // standard constructor

// Dialog Data
	//{{AFX_DATA(CLoginDlg)
	enum { IDD = IDD_LOGINDLG };
	CComboBox	m_ctlDatabase;
	CStatic	m_ctlBuildNumber;
	CString	m_strPassword;
	CString	m_strUserID;
	CString	m_strVersionNumber;
	CString	m_strBuildNumber;
	CString	m_strDatabase;
	//}}AFX_DATA

	BOOL m_buildDisplayed;

// Overrides
	// ClassWizard generated virtual function overrides
	//{{AFX_VIRTUAL(CLoginDlg)
	protected:
	virtual void DoDataExchange(CDataExchange* pDX);    // DDX/DDV support
	//}}AFX_VIRTUAL

// Implementation
protected:

	// Generated message map functions
	//{{AFX_MSG(CLoginDlg)
	virtual void OnOK();
	afx_msg void OnRButtonDown(UINT nFlags, CPoint point);
	virtual BOOL OnInitDialog();
	afx_msg BOOL OnHelpInfo(HELPINFO* pHelpInfo);
	afx_msg void OnAutoCheck();
	afx_msg void OnSelchangeDatabase();
	afx_msg void OnHelp();
	//}}AFX_MSG
	DECLARE_MESSAGE_MAP()
private:
	CString m_AutoDatabase;
	CString m_AutoFacility;
public:
	afx_msg void OnBnClickedOk();
};

//{{AFX_INSERT_LOCATION}}
// Microsoft Developer Studio will insert additional declarations immediately before the previous line.

#endif // !defined(AFX_LOGINDLG_H__3F7DD5D0_20BF_11D2_A47C_00C04FA8700B__INCLUDED_)

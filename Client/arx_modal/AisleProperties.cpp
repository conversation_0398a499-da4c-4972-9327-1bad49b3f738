// AisleProperties.cpp : implementation file
//

#include "stdafx.h"
#include "modal.h"
#include "AisleProperties.h"
#include "HelpService.h"

#ifdef _DEBUG
#define new DEBUG_NEW
#undef THIS_FILE
static char THIS_FILE[] = __FILE__;
#endif

extern CHelpService helpService;

/////////////////////////////////////////////////////////////////////////////
// CAisleProperties property page

IMPLEMENT_DYNCREATE(CAisleProperties, CPropertyPage)

CAisleProperties::CAisleProperties() : CPropertyPage(CAisleProperties::IDD)
{
	//{{AFX_DATA_INIT(CAisleProperties)
	m_Coordinates = _T("");
	m_Description = _T("");
	m_Rotation = 0.0f;
	//}}AFX_DATA_INIT
}

CAisleProperties::~CAisleProperties()
{
}

void CAisleProperties::DoDataExchange(CDataExchange* pDX)
{
	CPropertyPage::DoDataExchange(pDX);
	//{{AFX_DATA_MAP(CAisleProperties)
	DDX_Text(pDX, IDC_COORDINATES, m_Coordinates);
	DDX_Text(pDX, IDC_DESCRIPTION, m_Description);
	DDX_Text(pDX, IDC_ROTATION, m_Rotation);
	DDV_MinMaxFloat(pDX, m_Rotation, 0.f, 360.f);
	//}}AFX_DATA_MAP
}


BEGIN_MESSAGE_MAP(CAisleProperties, CPropertyPage)
	//{{AFX_MSG_MAP(CAisleProperties)
	ON_WM_HELPINFO()
	//}}AFX_MSG_MAP
END_MESSAGE_MAP()

/////////////////////////////////////////////////////////////////////////////
// CAisleProperties message handlers

BOOL CAisleProperties::OnHelpInfo(HELPINFO* pHelpInfo) 
{
	helpService.ShowFieldHelp(IDD, pHelpInfo->iCtrlId);
	return FALSE;

}

BOOL CAisleProperties::OnNotify(WPARAM wParam, LPARAM lParam, LRESULT* pResult) 
{
	NMHDR* pNMHDR = (NMHDR*)lParam;

	if (pNMHDR->code == PSN_HELP) {
		helpService.ShowScreenHelp(IDD);
		return TRUE;
	}
	
	return CPropertyPage::OnNotify(wParam, lParam, pResult);
}

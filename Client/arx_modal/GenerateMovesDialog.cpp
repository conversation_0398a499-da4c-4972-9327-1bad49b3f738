// GenerateMovesDialog.cpp : implementation file
//

#include "stdafx.h"
#include <afxmt.h>

#include "ssa_exception.h"

#include "GenerateMovesDialog.h"
#include "ProcessingMessage.h"
#include "DisplayResults.h"
#include "DisplayCount.h"
#include "Constants.h"
#include "HelpService.h"
#include "Facility.h"
#include "FacilityDataService.h"
#include "UtilityHelper.h"
#include "BTreeHelper.h"
#include "OptimizationHelper.h"
#include "AutoCADCommands.h"
#include "MoveDataService.h"
#include "SolutionDataService.h"
#include "DataAccessService.h"
#include "ControlService.h"
#include "ThreadParameters.h"
#include "InterfaceHelper.h"

#include <dbsymtb.h>


#ifdef _DEBUG
#define new DEBUG_NEW
#undef THIS_FILE
static char THIS_FILE[] = __FILE__;
#endif

extern CHelpService helpService;
extern CFacilityDataService facilityDataService;
extern CUtilityHelper utilityHelper;
extern CBTreeHelper bTreeHelper;
extern CSolutionDataService solutionDataService;
extern CDataAccessService dataAccessService;
extern CControlService controlService;

/////////////////////////////////////////////////////////////////////////////
// CGenerateMovesDialog dialog

extern CEvent g_ThreadDone;



CGenerateMovesDialog::CGenerateMovesDialog(CWnd* pParent /*=NULL*/)
	: CDialog(CGenerateMovesDialog::IDD, pParent)
{
	//{{AFX_DATA_INIT(CGenerateMovesDialog)
	//}}AFX_DATA_INIT

	m_Standalone = TRUE;
	m_IntegratedOnly = FALSE;
}

CGenerateMovesDialog::~CGenerateMovesDialog()
{
	for (int i=0; i < m_MoveList.GetSize(); ++i)
		delete m_MoveList[i];
}

void CGenerateMovesDialog::DoDataExchange(CDataExchange* pDX)
{
	CDialog::DoDataExchange(pDX);
	//{{AFX_DATA_MAP(CGenerateMovesDialog)
	//}}AFX_DATA_MAP
}


BEGIN_MESSAGE_MAP(CGenerateMovesDialog, CDialog)
	//{{AFX_MSG_MAP(CGenerateMovesDialog)
	ON_MESSAGE(WM_CLOSEDISPLAY, (LRESULT (AFX_MSG_CALL CWnd::*)(WPARAM, LPARAM))OnCloseDisplay)
	ON_MESSAGE(WM_DISPLAY_RESULTS_BUTTON1, (LRESULT (AFX_MSG_CALL CWnd::*)(WPARAM, LPARAM))OnExportMoves)
	ON_MESSAGE(WM_DISPLAY_RESULTS_BUTTON2, (LRESULT (AFX_MSG_CALL CWnd::*)(WPARAM, LPARAM))OnViewMoves)
	ON_MESSAGE(WM_DISPLAY_RESULTS_BUTTON3, (LRESULT (AFX_MSG_CALL CWnd::*)(WPARAM, LPARAM))OnColorMoves)
	ON_WM_HELPINFO()
	ON_BN_CLICKED(IDHELP, OnHelp)
	//}}AFX_MSG_MAP
END_MESSAGE_MAP()

/////////////////////////////////////////////////////////////////////////////
// CGenerateMovesDialog message handlers
BOOL CGenerateMovesDialog::OnInitDialog() 
{
	CDialog::OnInitDialog();
	CRect r;
	CButton *pButton;

	pButton = (CButton *)GetDlgItem(IDC_LOAD_MOVES);
	pButton->SetCheck(1);
	pButton = (CButton *)GetDlgItem(IDC_GENERATE_MOVES);
	pButton->SetCheck(0);
	
	if (m_Standalone)
		m_FacilityDBId = controlService.GetCurrentFacilityDBId();
	else {
		CString txt;
		txt.Format("Select moves to export from facility: %s", m_FacilityName);
		SetWindowText(txt);
	}

	m_InProcess = FALSE;

	UpdateData(FALSE);

	return TRUE;  // return TRUE unless you set the focus to a control
	              // EXCEPTION: OCX Property Pages should return FALSE
}

void CGenerateMovesDialog::OnOK() 
{
	int chainID = -1, prevChainID = -1;
	double cost = 0;
	CString strCost, line, chainList, temp;
	BOOL bNewDialog = FALSE;
	CStringArray results;
	int rc;
	CWinThread *pThread;
	CButton *pLoadMovesBtn = (CButton *)GetDlgItem(IDC_LOAD_MOVES);
	

	if (m_InProcess) return;

	CWaitCursor cwc;

	m_InProcess = TRUE;


	if (! pLoadMovesBtn->GetCheck()) {
		if (CheckForDuplicateLocations() == 1) {
			m_InProcess = FALSE;
			return;
		}
		if (CheckForMultipleProdsInLocation() == 1) {
			m_InProcess = FALSE;
			return;
		}
	}

	CString statusText;
	
	if (pLoadMovesBtn->GetCheck())
		statusText = "Loading Moves...";
	else
		statusText = "Generating Moves...";
	
	CProcessingMessage dlg(statusText, this);

	CThreadParameters parm;
	CStringArray parmList;
	CEvent event;
	parm.m_pEvent = &event;
	parmList.SetSize(4);
	parmList[0].Format("%d", m_FacilityDBId);
	parmList[1] = "0";		// chain length not currently available
	parmList[2] = "0";		// maximum hours not currently available
	parmList[3] = "0";		// maximum moves not currently available
	parm.m_pInList = &parmList;
	parm.m_pOutList = &results;

	try {
		CWaitCursor cwc;
		
		rc = 0;
		if (! pLoadMovesBtn->GetCheck() ) {

		

			pThread = AfxBeginThread(GenerateMovesThread, &parm);
		}
		else {
			pThread = AfxBeginThread(GetExistingMovesThread, &parm);
		}

		BOOL bThreadDone = false;
		while (TRUE) {
			if ( !utilityHelper.PeekAndPump() )
				break;
			
			bThreadDone = parm.m_pEvent->Lock(0);
			if (bThreadDone)
				break;
		}
		
		rc = parm.m_ReturnCode;;
		if (rc < 0) {
			controlService.Log("Error generating moves.", "Error during GenerateMovesThread: %s\n", parm.m_ReturnMessage);
			m_InProcess = FALSE;
			return;
		}
		
	}
	catch (Ssa_Exception e) {
		controlService.Log("Error getting moves.", &e);
		m_InProcess = FALSE;
		return;
	}
	catch (...) {
		controlService.Log("Error getting moves.", "Generic exception in GenerateMoves.\n");
		m_InProcess = FALSE;
		return;
	}
	
	// get rid of the return code and message
	if (results.GetSize() == 0) {
		pLoadMovesBtn = (CButton *)GetDlgItem(IDC_LOAD_MOVES);
		if (pLoadMovesBtn->GetCheck())
			AfxMessageBox("No moves were found.");
		else
			AfxMessageBox("No moves were generated.");
		m_InProcess = FALSE;
		return;
	}

	if (results[0].GetAt(0) == 'E') {
		CString msg = results[0];
		msg.Delete(0, 1);
		msg.Replace("|", "\n");
		controlService.Log(msg, "Error returned from server.\n");
		m_InProcess = FALSE;
		return;
	}

	DisplayMoves(results);

	return;

}

void CGenerateMovesDialog::DisplayMoves(CStringArray &moveList)
{
	CTypedPtrArray<CObArray, CMove*> *pChainMoveList;

	CFacility facility;
	try {
		facilityDataService.GetFacility(m_FacilityDBId, facility);
	}
	catch (...) {
		controlService.Log("Error getting facility properties.", "Generic exception in GetFacility.\n");
		return;
	}

	m_TimeHorizonUnits = facility.m_TimeHorizonUnits;
	m_TimeHorizonDuration = facility.m_TimeHorizonDuration;

	m_ChainListDialog = new CDisplayResults;
	m_ChainListDialog->m_Tabs.Add("Moves");
	m_ChainListDialog->m_WindowCaptions.Add("Moves");
	m_ChainListDialog->m_OrigColumnSize = -1;
	m_ChainListDialog->m_MessageReceiver = this;
	

	if (m_Standalone) {
		m_ChainListDialog->m_NextCaption = "Export to WMS";
		m_ChainListDialog->m_NextClosesWindow = FALSE;

		m_ChainListDialog->m_NextCaption3 = "Color Chain";
		m_ChainListDialog->m_NextClosesWindow3 = FALSE;
	}
	else {
		m_ChainListDialog->m_NextCaption = "Export Selected";
		m_ChainListDialog->m_NextClosesWindow = FALSE;
	}
	
	m_ChainListDialog->m_NextCaption2 = "View Moves";
	m_ChainListDialog->m_NextClosesWindow2 = FALSE;
	
	m_ChainListDialog->m_NextHelp2 = "MoveChainResults_ViewMoves";
	m_ChainListDialog->m_NextHelp3 = "MoveChainResutls_ColorChain";

	m_ChainListDialog->m_IsModeless = TRUE;
	m_ChainListDialog->m_AllowSort = TRUE;
	m_ChainListDialog->m_AllowMultipleSelections = TRUE;
	m_ChainListDialog->m_Headers.Add("Chain|Count|Net Savings|Move Cost|Savings|Move Time|Status|");
	m_ChainListDialog->m_ColumnFormat.Add("2|1|1|1|1|1|0|");
	m_ChainListDialog->m_NextHelp1 = "MoveChainResults_Export";
	m_ChainListDialog->m_HelpTopics.Add("MoveChainResults_Main");
	m_ChainListDialog->m_MainHelpTopic = "MoveChainResults_Main";

	m_ChainListDialog->m_Data.RemoveAll();
	m_ResultSelectionMap.RemoveAll();

	// Loop through all the results, aggregating the totals for each chain
	for (int i=0; i < moveList.GetSize(); ++i) {
		CMove *pMove = new CMove;
		CChainCostSummary *pChainCostSummary;

		pMove->Parse(moveList[i]);
		pMove->m_TimeHorizonDuration = m_TimeHorizonDuration;
		pMove->m_TimeHorizonUnits = m_TimeHorizonUnits;

		m_MoveList.Add(pMove);
		
		// m_ChainCostMap contains the totals for a chain Id
		if (m_ChainCostMap.Lookup(pMove->m_ChainId, pChainCostSummary)) {
			pChainCostSummary->count++;
			pChainCostSummary->moveCost += pMove->GetTotalMoveCost();
			pChainCostSummary->moveTime += pMove->GetTotalMoveTime();
			pChainCostSummary->savings += pMove->GetMoveSavings();	// need to convert by time horizon
			pChainCostSummary->netSavings += pMove->GetNetMoveSavings();
			pChainCostSummary->status = CInterfaceHelper::ConvertStatusToText(pMove->m_MoveStatus);
		}
		else {
			pChainCostSummary = new CChainCostSummary;
			pChainCostSummary->chainId = pMove->m_ChainId;
			pChainCostSummary->count = 1;
			pChainCostSummary->moveCost = pMove->GetTotalMoveCost();
			pChainCostSummary->moveTime = pMove->GetTotalMoveTime();
			pChainCostSummary->netSavings = pMove->GetNetMoveSavings();
			pChainCostSummary->savings = pMove->GetMoveSavings();
			pChainCostSummary->status = CInterfaceHelper::ConvertStatusToText(pMove->m_MoveStatus);
			m_ChainCostMap.SetAt(pMove->m_ChainId, pChainCostSummary);
		}
		

		// m_MoveMap associates a list of actual moves with the chain Id
		if (m_MoveMap.Lookup(pMove->m_ChainId, pChainMoveList)) {
			pChainMoveList->Add(pMove);
		}
		else {
			pChainMoveList = new CTypedPtrArray<CObArray, CMove*>;
			pChainMoveList->Add(pMove);
			m_MoveMap.SetAt(pMove->m_ChainId, pChainMoveList);
		}
	}

	// Now that we've added up all the totals for each chain id,
	// loop through them and add each chain id to the display list
	
	CChainCostSummary *pChainCostSummary;
	CChainCostSummary **chainList;
	chainList = (CChainCostSummary **)malloc(m_ChainCostMap.GetCount()*sizeof(CChainCostSummary *));


	POSITION pos = m_ChainCostMap.GetStartPosition();
	i = 0;
	while (pos != NULL) {
		long chainId;
		
		m_ChainCostMap.GetNextAssoc(pos, chainId, pChainCostSummary);
		chainList[i] = pChainCostSummary;
		i++;
		
	}

	qsort(chainList, m_ChainCostMap.GetCount(), sizeof(CChainCostSummary *), (int (*)(const void *, const void *))CGenerateMovesDialog::Compare);

	for (i=0; i < m_ChainCostMap.GetCount(); ++i) {
		pChainCostSummary = chainList[i];

		CString tempStr;
		tempStr.Format("%d|%d|%9.2f|%9.2f|%9.2f|%9.2f|%s|", 
			pChainCostSummary->chainId, pChainCostSummary->count, pChainCostSummary->netSavings,
			pChainCostSummary->moveCost, pChainCostSummary->savings,
			pChainCostSummary->moveTime, pChainCostSummary->status);
		m_ChainListDialog->m_Data.Add(tempStr);

		// This map keeps track of which chain is at which position in the display list
		// That way, when they select one to view we will know which chain to display
		m_ResultSelectionMap.SetAt(m_ChainListDialog->m_Data.GetSize()-1, pChainCostSummary->chainId);
	}

	delete chainList;

	m_ChainListDialog->Create(IDD_DISPLAY_RESULTS, this);
	m_ChainListDialog->CenterWindow();
	
	this->ShowWindow(SW_HIDE);

	m_ChainListDialog->ShowWindow(SW_SHOW);

}


void CGenerateMovesDialog::OnCloseDisplay(WPARAM wParam, LPARAM lParam)
{
	UNREFERENCED_PARAMETER(wParam);
	UNREFERENCED_PARAMETER(lParam);

	CTypedPtrArray<CObArray, CMove*> *pMoveList;
	CChainCostSummary *pChainCostSummary;

	// Dialog will delete itself
	if (wParam == (unsigned int)(void *)m_ChainListDialog) {
		m_ResultSelectionMap.RemoveAll();

		POSITION pos = m_ChainCostMap.GetStartPosition();
		while (pos != NULL) {
			long chainId;
			m_ChainCostMap.GetNextAssoc(pos, chainId, pChainCostSummary);
			delete pChainCostSummary;
		}
		m_ChainCostMap.RemoveAll();

		pos = m_MoveMap.GetStartPosition();
		while (pos != NULL) {
			long chainId;
			m_MoveMap.GetNextAssoc(pos, chainId, pMoveList);
			delete pMoveList;
		}
		m_MoveMap.RemoveAll();
		m_ChainListDialog = NULL;
		m_InProcess = FALSE;
		this->ShowWindow(SW_SHOW);
	}
}


void CGenerateMovesDialog::ExportMoves()
{
	CFile file;
	int rc;
	CStringArray results;

	if (! m_Standalone) {
		EndDialog(IDOK);
		return;
	}

	if (GetFile() < 0)
		return;

	if (! file.Open(m_FileName, CFile::modeCreate|CFile::modeWrite)) {
		CString msg;
		msg = "Error opening file: ";
		msg += m_FileName;
		AfxMessageBox(msg);
		return;
	}

	results.RemoveAll();

	CProcessingMessage dlg("Exporting Moves", this);
	
	try {
		CThreadParameters parm;
		CStringArray parmList;
		CEvent event;
		parm.m_pEvent = &event;
		parmList.SetSize(2);
		parmList[0].Format("%d", m_FacilityDBId);
		parmList[1] = m_ExportChainList;
		parm.m_pInList = &parmList;
		parm.m_pOutList = &results;
		
		CWinThread *pThread = AfxBeginThread(CGenerateMovesDialog::GetOutboundMovesThread, &parm);

	
		BOOL bThreadDone = false;
		while (TRUE) {
			if ( !utilityHelper.PeekAndPump() )
				break;
			
			bThreadDone = parm.m_pEvent->Lock(0);
			if (bThreadDone)
				break;
		}
		
		rc = parm.m_ReturnCode;
		if (rc < 0) {
			controlService.Log("Error generating moves.", "Error during ExportMoves: %s\n", parm.m_ReturnMessage);
			m_InProcess = FALSE;

			return;
		}
		
	}
	catch(Ssa_Exception e) {
		controlService.Log("An error occurred while exporting the moves.", &e);
		return;
	}
	catch (...) {
		controlService.Log("An error occurred while exporting the moves.", "Generic exception in ExportMoves.\n");
		return;
	}


	if (results.GetSize() == 0) {
		this->MessageBox("No moves were found.", "No records found.", MB_OK);
		return;
	}


//	m_DefaultDC = facilityDataService.GetCurrentDC();
//	m_DefaultWhse = facilityDataService.GetCurrentWarehouse();
	m_SectionDCMap.RemoveAll();
	m_SectionWhseMap.RemoveAll();
	for (int i=0; i < results.GetSize(); ++i) {	// skip the header
		Format(results[i]);

		file.Write(results[i], results[i].GetLength());
		file.Write("\r\n", 2);
	}

	file.Close();


	CDisplayCount dcDlg(this);
	dcDlg.m_Start = 0;
	dcDlg.m_End = results.GetSize();

	dcDlg.DoModal();

	rc = dcDlg.m_Count;
	if (rc > 0)
		ShowResults(results, rc);

	return;	
}


int CGenerateMovesDialog::GetFile()
{
	CFileDialog dlgFile(FALSE);
	CString title;
	CString strFilter;
	CString strDefault;

	CString allFilter;
	VERIFY(allFilter.LoadString(AFX_IDS_ALLFILTER));
	strFilter += CString("Interface Files (*.dat)");
	strFilter += (TCHAR)'\0';   
	strFilter += _T("*.dat");
	strFilter += (TCHAR)'\0';
	strFilter += allFilter;
	strFilter += (TCHAR)'\0';   
	strFilter += _T("*.*");
	strFilter += (TCHAR)'\0';   

	dlgFile.m_ofn.lpstrFilter = strFilter;
	dlgFile.m_ofn.lpstrDefExt = "dat";
	dlgFile.m_ofn.lpstrTitle = "Product Move Outbound Interface File";
	dlgFile.m_ofn.lpstrFile = m_FileName.GetBuffer(_MAX_PATH);
	//dlgFile.m_ofn.Flags &= ~OFN_OVERWRITEPROMPT;
	BOOL bResult = dlgFile.DoModal() == IDOK ? TRUE : FALSE;

	
	m_FileName.ReleaseBuffer();
	
	if (! bResult)
		return -1;
	else
		return 0;

}

void CGenerateMovesDialog::ShowResults(CStringArray &moveList, int count)
{
	CDisplayResults dlg;

	dlg.m_Headers.Add("DC|Warehouse|Chain|Sequence|WMS Product ID|WMS Detail ID|From Location|To Location|Is Anchor|");
	for (int i=0; i < count; ++i)
		dlg.m_Data.Add(moveList[i]);

	CString tmp;
	tmp.Format("Product Move Outbound Results - %s", m_FileName);
	dlg.m_WindowCaptions.Add(tmp);
	dlg.m_Tabs.Add("Product Moves");
	dlg.m_OrigColumnSize = 100;


	try {
		dlg.DoModal();
	}
	catch (...) {
		AfxMessageBox("Error displaying moves.");
	}

	return;
		
}

void CGenerateMovesDialog::Format(CString &move)
{
	int dc, whse;
	CString strDC, strWhse;
	CString prod, floc, tloc;
	int chain, chainSeq, prodDetail, moveType, isAnchor;
	long sectionId;
	CStringArray fields, strings;
	CString temp;


	utilityHelper.ParseString(move, "|", fields);

	chain = atoi(fields[0]);
	chainSeq = atoi(fields[1]);
	prod = fields[2];
	prodDetail = atoi(fields[3]);
	floc = fields[4];
	tloc = fields[5];
	moveType = atoi(fields[6]);
	isAnchor = atoi(fields[7]);
	sectionId = atoi(fields[8]);
	
	if (! m_SectionDCMap.Lookup(sectionId, strDC)) {
		strDC = facilityDataService.GetDCBySection(m_FacilityDBId, sectionId);
		if (strDC == "")
			strDC = m_DefaultDC;
		m_SectionDCMap[sectionId] = strDC;
	}

	if (! m_SectionWhseMap.Lookup(sectionId, strWhse)) {
		strWhse = facilityDataService.GetWarehouseBySection(m_FacilityDBId, sectionId);
		if (strWhse == "")
			strWhse = m_DefaultWhse;
		m_SectionWhseMap[sectionId] = strWhse;
	}


	dc = atoi(strDC);
	whse = atoi(strWhse);

	if (moveType == MOVE_FROM_TEMP)
		floc = "TempLoc";
	else if (moveType == MOVE_ADD_LOC)
		floc = "AddLoc";
	else if (moveType == MOVE_TO_TEMP)
		tloc = "TempLoc";
	else if (moveType == MOVE_DEL_LOC)
		tloc = "DelLoc";

	
	move.Format("%05d|%05d|%10d|%10d|%18.18s|%05d|%-8.8s|%-8.8s|%1s|",
		dc, whse, chain, chainSeq, prod, prodDetail, floc, tloc, isAnchor ? "Y" : "N");

	return;


}


void CGenerateMovesDialog::OnExportMoves(WPARAM wParam, LPARAM lParam)
{
	CString chainList, temp;
	int curSel, chainID;
	UNREFERENCED_PARAMETER(wParam);

	m_ExportChainList = "";
	CStringArray badChains;
	CStringArray sentChains;
	CStringArray goodChains;
	CTypedPtrArray<CObArray, CMove*> *pMoveList;

	if (lParam == 0) {
		AfxMessageBox("Please select one or more moves to export.");
		return;
	}

	for (int i=0; i < m_ChainListDialog->m_SelectionList.GetSize(); ++i) {
		curSel = m_ChainListDialog->m_SelectionList[i];
		if (m_ResultSelectionMap.Lookup(curSel, chainID)) {

			BOOL bad = FALSE;
			CString chainStr;
			chainStr.Format("%d", chainID);	

			if (! m_Standalone && m_IntegratedOnly) {
				
				if (! m_MoveMap.Lookup(chainID, pMoveList)) {
					CString msg;
					msg.Format("Error finding moves for chain: %d", chainID);
					AfxMessageBox(msg);
					return; 
				}
					
				for (int j=0; j < pMoveList->GetSize(); ++j) {
					CMove *pMove = pMoveList->GetAt(j);
					
					if (pMove->m_ProductStatus != CProductPack::Integrated) {
						badChains.Add(chainStr);
						bad = TRUE;
						break;
					}
					
					if (pMove->m_FromLocationStatus > 0 && pMove->m_FromLocationStatus != CLocation::Integrated) {
						badChains.Add(chainStr);
						bad = TRUE;
						break;
					}
					
					if (pMove->m_ToLocationStatus > 0 && pMove->m_ToLocationStatus != CLocation::Integrated) {
						badChains.Add(chainStr);
						bad = TRUE;
						break;
					}
					
					if (pMove->m_MoveStatus == CInterfaceHelper::Confirmed ||
						pMove->m_MoveStatus == CInterfaceHelper::Sent) {
						sentChains.Add(chainStr);
						bad = TRUE;
						break;
					}
				}
			}

			if (bad)
				continue;
			else
				goodChains.Add(chainStr);

		}
	}
	m_ExportChainList.TrimRight(",");

	if (badChains.GetSize() > 0) {
		CString temp;
		temp.Format("The following chains cannot be sent because a product or location is not yet integrated:\n");
		for (int i=0; i < badChains.GetSize(); ++i) {
			temp += "\t";
			temp += badChains[i];
			temp += "\n";
		}
		if (goodChains.GetSize() == 0) {
			temp += "No moves available to export.";
			AfxMessageBox(temp);
			return;
		}
		else {
			temp += "Do you wish to export the remaining moves?";
			if (AfxMessageBox(temp, MB_YESNO) != IDYES)
				return;
		}
	}

	if (sentChains.GetSize() > 0) {
		CString temp;
		temp.Format("The following chains have already been sent:\n");
		for (int i=0; i < sentChains.GetSize(); ++i) {
			temp += "\t";
			temp += sentChains[i];
			temp += "\n";
		}
		if (goodChains.GetSize() == 0) {
			temp += "No moves available to export.";
			AfxMessageBox(temp);
			return;
		}
		else {
			temp += "Do you wish to export the remaining moves?";
			if (AfxMessageBox(temp, MB_YESNO) != IDYES)
				return;
		}
	}

	for (i=0; i < goodChains.GetSize(); ++i) {
		m_ExportChainList += goodChains[i];
		m_ExportChainList += ",";
	}

	m_ExportChainList.TrimRight(",");


	ExportMoves();

	return;
}

UINT CGenerateMovesDialog::GenerateMovesThread(LPVOID pParam)
{
	int rc = 0;
	long facilityID;
	int moveLength, maxHours, maxMoves;
	COptimizationHelper optimizationHelper;
	
	CThreadParameters &parm = *(CThreadParameters *)pParam;
	facilityID = atol(parm.m_pInList->GetAt(0));
	moveLength = atoi(parm.m_pInList->GetAt(1));
	maxHours = atoi(parm.m_pInList->GetAt(2));
	maxMoves = atoi(parm.m_pInList->GetAt(3));

	try {
		rc = optimizationHelper.StartModelComparison(facilityID, moveLength, maxHours, maxMoves, *parm.m_pOutList);
	}
	catch (Ssa_Exception e) {
		char msgBuf[1024];
		e.GetMessage(msgBuf);
		parm.m_pOutList->RemoveAll();
		parm.m_pOutList->Add(msgBuf);
		parm.m_ReturnCode = 1;
	}
	catch (...) {
		parm.m_pOutList->RemoveAll();
		parm.m_pOutList->Add(" Error generating moves.");
		parm.m_ReturnCode = 1;
	}

	if (rc < 0) {
		parm.m_pOutList->RemoveAll();
		parm.m_pOutList->Add(" Error generating moves.");
		parm.m_ReturnCode = 1;
	}
	else
		parm.m_ReturnCode = 0;

	BOOL b = parm.m_pEvent->SetEvent();
	if (! b) {
		DWORD err = GetLastError();
	}

	return parm.m_ReturnCode;
}


UINT CGenerateMovesDialog::GetExistingMovesThread(LPVOID pParam)
{
	int rc = 0;
	CMoveDataService moveDataService;

	CThreadParameters &parm = *(CThreadParameters *)pParam;
	int facilityId = atol(parm.m_pInList->GetAt(0));

	try {
		rc = moveDataService.GetMoves(facilityId, *parm.m_pOutList);
	}
	catch(Ssa_Exception e) {
		char eMsg[1024];
		e.GetMessage(eMsg);
		parm.m_ReturnMessage = eMsg;
		parm.m_ReturnCode = -1;
		rc = 1;
	}
	catch(...) {
		parm.m_ReturnMessage = "Error getting moves.";
		parm.m_ReturnCode = -1;
		rc = 1;
	}

	parm.m_pEvent->SetEvent();

	return rc;

}


UINT CGenerateMovesDialog::GetOutboundMovesThread(LPVOID pParam)
{
	int rc;
	
	CString moveList;
	CMoveDataService moveDataService;
	
	CThreadParameters &parm = *(CThreadParameters *)pParam;
	int facilityId = atol(parm.m_pInList->GetAt(0));
	moveList = parm.m_pInList->GetAt(1);
	
	rc = 0;

	try {
		rc = moveDataService.GetOutboundMoves(facilityId, moveList, *parm.m_pOutList);
		if (rc < 0)
			rc = 1;
	}
	catch(Ssa_Exception e) {
		char eMsg[1024];
		e.GetMessage(eMsg);
		parm.m_ReturnCode = -1;
		parm.m_ReturnMessage = eMsg;
		rc = 1;
	}
	catch(...) {
		parm.m_ReturnCode = -1;
		parm.m_ReturnMessage = "Unknown error getting moves.";
		rc = 1;
	}

	parm.m_pEvent->SetEvent();

	return rc;

}


void CGenerateMovesDialog::OnCancel() 
{
	if (m_InProcess)
		return;
	
	CDialog::OnCancel();
	//DestroyWindow();

}


BOOL CGenerateMovesDialog::OnHelpInfo(HELPINFO* pHelpInfo) 
{
	helpService.ShowFieldHelp(IDD, pHelpInfo->iCtrlId);
	return FALSE;
}

void CGenerateMovesDialog::OnHelp() 
{
	helpService.ShowScreenHelp(IDD);	
}

void CGenerateMovesDialog::OnViewMoves(WPARAM wParam, LPARAM lParam)
{
	CMove *pMove;
	CTypedPtrArray<CObArray, CMove*> *pMoveList;
	CDisplayResults m_MoveListDialog;

	int chainId;
	CString tempStr;

	// lParam is size of selection which will be one or zero in this case
	// wParam is the item selected
	if (lParam == 0) {
		AfxMessageBox("Please select one or more moves to view.");
		return;
	}

	if (! m_ResultSelectionMap.Lookup(wParam, chainId)) {
		AfxMessageBox("Invalid selection.");
		return;
	}

	if (! m_MoveMap.Lookup(chainId, pMoveList)) {
		AfxMessageBox("Unable to find moves for chain.");
		return;
	}

	m_MoveListDialog.m_Tabs.Add("Moves");

	tempStr.Format("Moves for chain: %d", chainId);
	m_MoveListDialog.m_WindowCaptions.Add(tempStr);
	m_MoveListDialog.m_OrigColumnSize = 75;
//	m_MoveListDialog.m_MessageReceiver = this;
	m_MoveListDialog.m_IsModeless = FALSE;
	m_MoveListDialog.m_Headers.Add("Chain Sequence|Product/Detail Id|Net Savings|From Location|To Location|From Cost|To Cost|"
		"Savings|Move Cost|Move Time|Product Description|"
		"Fork Handling Time|Fork Handling Cost|Fork Travel Distance|Fork Travel Time|Fork Travel Cost|"
		"Stocker Handling Time|Stocker Handling Cost|Stocker Travel Distance|Stocker Travel Time|Stocker Travel Cost|"
		"From Location Status|To Location Status|Product Status|");
	m_MoveListDialog.m_ColumnFormat.Add("2|2|1|2|2|1|1|1|1|1|2|1|1|1|1|1|1|1|1|1|1|0|0|0|");
	m_MoveListDialog.m_MainHelpTopic = "MoveListResults_Main";
	m_MoveListDialog.m_HelpTopics.Add("MoveListResults_Main");
	

	for (int i=0; i < pMoveList->GetSize(); ++i) {
		pMove = pMoveList->GetAt(i);
		
		tempStr.Format("%d|%s/%s|%9.2f|%s|%s|%9.2f|"
			"%9.2f|%9.2f|%9.2f|%9.2f|%s|"
			"%9.2f|%9.2f|%9.2f|%9.2f|%9.2f|"
			"%9.2f|%9.2f|%9.2f|%9.2f|%9.2f|"
			"%s|%s|%s|",
			pMove->m_ChainSequence, pMove->m_Product.m_WMSProductID, pMove->m_Product.m_WMSProductDetailID,
			pMove->GetNetMoveSavings(),
			pMove->m_FromLocation, pMove->m_ToLocation, pMove->GetFromTotalCost(), pMove->GetToTotalCost(), 
			pMove->GetMoveSavings(), pMove->GetTotalMoveCost(),
			pMove->GetTotalMoveTime(),
			pMove->m_Product.m_Description,
			pMove->m_ForkHandlingTime, pMove->m_ForkHandlingCost, pMove->m_ForkTravelDistance,
			pMove->m_ForkTravelTime, pMove->m_ForkTravelCost,
			pMove->m_StockerHandlingTime, pMove->m_StockerHandlingCost, pMove->m_StockerTravelDistance,
			pMove->m_StockerTravelTime, pMove->m_StockerTravelCost,
			CLocation::ConvertStatusToText(pMove->m_FromLocationStatus),
			CLocation::ConvertStatusToText(pMove->m_ToLocationStatus),
			CProductPack::ConvertStatusToText(pMove->m_ProductStatus));
		
		m_MoveListDialog.m_Data.Add(tempStr);
	}

	m_MoveListDialog.DoModal();

}

int CGenerateMovesDialog::CheckForDuplicateLocations()
{
	CSsaStringArray results;
	CProcessingMessage procdlg("Checking for duplicate locations...", this);
	CDisplayResults dlg;
	CStringArray headers;
	CString header;
	int dupCount;			
	
	try {
		dupCount = facilityDataService.GetDuplicateLocationsByFacility(m_FacilityDBId, results);
	}
	catch(Ssa_Exception e) {
		controlService.Log("Error getting duplicate locations.", &e);
		return -1;
	}
	catch(...) {
		controlService.Log("Error getting duplicate locations.", "Generic exception in GetDuplicateLocationsByFacility.\n");
		return -1;
	}
	
	if (dupCount > 0) {
		AfxMessageBox("There are duplicate locations in this facility.\n"
			"Please correct this before generating moves.");
		// Create tabs
		dlg.m_Tabs.Add("Duplicate Locations");
		dlg.m_WindowCaptions.Add("Duplicate Locations");
		// Create headers
		header = "Section|Aisle|Bay|Level|Location|Number of Duplicates|";
		dlg.m_Headers.Add(header);
		// Create keys used to parse elements into tabs
		dlg.m_HeaderKeys.Add("D");
		dlg.m_Data = results;
		
		try {
			int rc = dlg.DoModal();
		}
		catch (...) {
			AfxMessageBox("Error displaying duplicate locations.");
		}
		
		return 1;
	}
				

	return 0;

}

int CGenerateMovesDialog::CheckForMultipleProdsInLocation()
{

	CStringArray results;
	CProcessing *pdlg = new CProcessing();
	CDisplayResults dlg;
	CStringArray headers;
	CString header;
	int dupCount;

	CProcessingMessage procDlg("Checking for duplicate locations.", this);;
			
	
	try {
		dupCount = solutionDataService.GetMultipleProductsInLocations(m_FacilityDBId, results);
	}
	catch(Ssa_Exception e) {
		controlService.Log("Error getting multiple products in locations.", &e);
		return -1;
	}
	catch(...) {
		controlService.Log("Error getting multiple products in locations.", "Generic exception in GetMultipleProductsInLocations.\n");
		return -1;
	}
	
	if (dupCount > 0) {
		AfxMessageBox("There are multiple products assigned to locations in this facility.\n"
			"Please correct this before generating moves.");
		// Create tabs
		dlg.m_Tabs.Add("Multiple Products in Locations");
		dlg.m_WindowCaptions.Add("Multiple Products in Locations");
		// Create headers
		header = "Location|Number of Products|";
		dlg.m_Headers.Add(header);
		// Create keys used to parse elements into tabs
		dlg.m_HeaderKeys.Add("D");
		dlg.m_Data = results;
		
		try {
			int rc = dlg.DoModal();
		}
		catch (...) {
			AfxMessageBox("Error displaying multiple products in locations.");
		}
		
		return 1;
	}


	return 0;
}


int CGenerateMovesDialog::Compare(const void **p1, const void **p2) 
{
	
	CChainCostSummary *c1 = (CChainCostSummary *)*p1;
	CChainCostSummary *c2 = (CChainCostSummary *)*p2;

	if (c1->netSavings < c2->netSavings)
		return 1;
	else
		return -1;

}


void CGenerateMovesDialog::OnColorMoves(WPARAM wParam, LPARAM lParam)
{
	int chainId;
	CStringArray fromBays, toBays;

	if (lParam == 0) {
		AfxMessageBox("Please select one or more moves to export.");
		return;
	}

	if (! m_ResultSelectionMap.Lookup(wParam, chainId)) {
		AfxMessageBox("Invalid selection.");
		return;
	}

	CWaitCursor cwc;

	CString sql;
	
	try {
		sql.Format("select unique acadhandle from dbmovef, dblocation, dblevel, dbbay "
			"where dbmovef.dbfacilityid = %d "
			"and dbmovef.fromlocationid = dblocation.dblocationid "
			"and dblocation.dblevelid = dblevel.dblevelid "
			"and dblevel.dbbayid = dbbay.dbbayid "
			"and dbmovef.chain = %d", m_FacilityDBId, chainId);

		dataAccessService.ExecuteQuery("GetFromBays", sql, fromBays);

		sql.Format("select unique acadhandle from dbmovef, dblocation, dblevel, dbbay "
			"where dbmovef.dbfacilityid = %d "
			"and dbmovef.tolocationid = dblocation.dblocationid "
			"and dblocation.dblevelid = dblevel.dblevelid "
			"and dblevel.dbbayid = dbbay.dbbayid "
			"and dbmovef.chain = %d", m_FacilityDBId, chainId);
		
		dataAccessService.ExecuteQuery("GetToBays", sql, toBays);
	}
	catch (...) {
		AfxMessageBox("Error retrieving bays for chain from database.");
		return;
	}

	CAutoCADCommands::ColorAllObjects();

	for (int i=0; i < fromBays.GetSize(); ++i) {
		fromBays[i].TrimRight("|");
		CAutoCADCommands::ColorDrawingObjectByHandle(fromBays[i], kRed);		// 1 = red
	}

	for (i=0; i < toBays.GetSize(); ++i) {
		toBays[i].TrimRight("|");
		CAutoCADCommands::ColorDrawingObjectByHandle(toBays[i], kBlue);		// 5 = blue
		for (int j=0; j < fromBays.GetSize(); ++j) {
			if (fromBays[j] == toBays[i]) {
				CAutoCADCommands::ColorDrawingObjectByHandle(toBays[i], kMagenta);	// 6 = purple
				break;
			}
		}

	}

	CAutoCADCommands::Flush();

	AfxMessageBox("Chain has been colored.  From bays are red; to bays are blue; common bays are purple.");

}

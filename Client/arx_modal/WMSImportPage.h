#if !defined(AFX_WMSIMPORTPAGE_H__620A6F3A_21A8_4EE4_B532_392CD727A26E__INCLUDED_)
#define AFX_WMSIMPORTPAGE_H__620A6F3A_21A8_4EE4_B532_392CD727A26E__INCLUDED_

#if _MSC_VER > 1000
#pragma once
#endif // _MSC_VER > 1000
// WMSImportPage.h : header file
//
#include "WMSFacilityInfo.h"
#include "WMSGroup.h"

/////////////////////////////////////////////////////////////////////////////
// CWMSImportPage dialog

class CWMSImportPage : public CPropertyPage
{
	DECLARE_DYNCREATE(CWMSImportPage)

// Construction
public:
	HTREEITEM GetFacilityNode(int facilityDBId);
	int CountChildren(HTREEITEM &hItem);
	void Reload();
	CWMSImportPage();
	~CWMSImportPage();
	CTypedPtrArray<CObArray, CWMSMap*> m_ImportMapList;
// Dialog Data
	//{{AFX_DATA(CWMSImportPage)
	enum { IDD = IDD_WMS_IMPORT };
	CTreeCtrl	m_WMSTreeCtrl;
	CTreeCtrl	m_FacilityTreeCtrl;
	//}}AFX_DATA


// Overrides
	// ClassWizard generate virtual function overrides
	//{{AFX_VIRTUAL(CWMSImportPage)
	protected:
	virtual void DoDataExchange(CDataExchange* pDX);    // DDX/DDV support
	virtual BOOL OnNotify(WPARAM wParam, LPARAM lParam, LRESULT* pResult);
	//}}AFX_VIRTUAL

// Implementation

protected:
	// Generated message map functions
	//{{AFX_MSG(CWMSImportPage)
	virtual BOOL OnInitDialog();
	afx_msg void OnAssign();
	afx_msg void OnRemove();
	afx_msg void OnBegindragWmsGroupTree(NMHDR* pNMHDR, LRESULT* pResult);
	afx_msg void OnMouseMove(UINT nFlags, CPoint point);
	afx_msg void OnLButtonUp(UINT nFlags, CPoint point);
	afx_msg void OnTimer(UINT nIDEvent);
	afx_msg void OnBegindragFacilityMapTree(NMHDR* pNMHDR, LRESULT* pResult);
	afx_msg BOOL OnHelpInfo(HELPINFO* pHelpInfo);
	//}}AFX_MSG
	DECLARE_MESSAGE_MAP()

private:
	int m_nDelayInterval;
	int m_nScrollMargin;
	int m_nScrollInterval;
	HTREEITEM m_hDropItem;
	BOOL m_bDraggingWMS;
	BOOL m_bDraggingSection;

	BOOL ValidateWMSDragItem(HTREEITEM &hItem);
	BOOL ValidateSectionDragItem(HTREEITEM &hItem);

	CImageList *m_pDragImageList;
	HTREEITEM m_hDragItem;
	void UpdateFacilityTreeText(HTREEITEM &hItem, const CString &name, 
		const CString &wmsName, const CString &groupName);
	int Remove(int facilityDBId, int sectionDBId);
	int UpdateMapTreeItem(CWMSMap *pMap);
	int Assign(CWMS *pWMS, int facilityDBId, int sectionDBId);
	int UpdateMapTree();
	int LoadMapList();
	CImageList m_WMSImageList;
	CImageList m_FacilityImageList;
	CMap<int, int, HTREEITEM, HTREEITEM&> m_MapSectionToTree;
	CMap<HTREEITEM, HTREEITEM&, int, int> m_MapTreeToSection;
	CMap<int, int, HTREEITEM, HTREEITEM&> m_MapWMSToTree;
	CMap<HTREEITEM, HTREEITEM&, int, int> m_MapTreeToGroup;


	HTREEITEM AddWMSToTree(CWMS *pWMS, HTREEITEM hItem);
	HTREEITEM AddGroupToTree(CWMSGroup *pGroup);
	int LoadWMSTree();
	int LoadWMSList();
	int LoadFacilityList();
	int LoadFacilityTree();

	void OnMouseMoveWMS(CPoint point);
	void OnMouseMoveSection(CPoint point);
};

//{{AFX_INSERT_LOCATION}}
// Microsoft Visual C++ will insert additional declarations immediately before the previous line.

#endif // !defined(AFX_WMSIMPORTPAGE_H__620A6F3A_21A8_4EE4_B532_392CD727A26E__INCLUDED_)

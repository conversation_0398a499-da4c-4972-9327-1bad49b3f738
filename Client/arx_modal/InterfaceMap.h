// InterfaceMap.h: interface for the CInterfaceMap class.
//
//////////////////////////////////////////////////////////////////////

#if !defined(AFX_INTERFACEMAP_H__FB92FEDB_DE10_4E83_9483_F729A0AC0B7F__INCLUDED_)
#define AFX_INTERFACEMAP_H__FB92FEDB_DE10_4E83_9483_F729A0AC0B7F__INCLUDED_

#if _MSC_VER > 1000
#pragma once
#endif // _MSC_VER > 100

#include "InterfaceMapAttribute.h"

class CInterfaceMap : public CObject  
{
public:
	void Clear();
	int Parse(const CString &line);
	CInterfaceMap();
	virtual ~CInterfaceMap();
	CInterfaceMap& operator=(CInterfaceMap &other);
	BOOL operator==(CInterfaceMap &other);

	long m_InterfaceMapDBID;
	CString m_Name;
	int m_InterfaceMapTypeDBID;
	BOOL m_IsNamed;
	int m_FormatType;
	CString m_Delimiter;

	CTypedPtrArray<CObArray, CInterfaceMapAttribute*> m_MappedAttributeList;
	
	enum InterfaceTypes {
		ProductInboundType,
		AssignmentInboundType,
		LocationOutboundType,
		AssignmentOutboundType,
		MoveOutboundType
	};

	enum FormatTypes {
		DelimitedFormat,
		XMLFormat
	};

};

#endif // !defined(AFX_INTERFACEMAP_H__FB92FEDB_DE10_4E83_9483_F729A0AC0B7F__INCLUDED_)

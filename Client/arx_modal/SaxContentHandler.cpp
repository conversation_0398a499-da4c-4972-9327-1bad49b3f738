#include "stdafx.h"
#include "SaxContentHandler.h"
#include "WMSGroupConnection.h"
#include "InterfaceHelper.h"

#include <dbsymtb.h>

//////////////////////////////////////////////////////////////////////
// Construction/Destruction
//////////////////////////////////////////////////////////////////////

CSaxContentHandler::CSaxContentHandler()
{
	m_LogDetail = FALSE;
}

CSaxContentHandler::~CSaxContentHandler()
{

}

HRESULT STDMETHODCALLTYPE CSaxContentHandler::startElement(/* [in] */ wchar_t __RPC_FAR *pwchNamespaceUri,
														   /* [in] */ int cchNamespaceUri,
														   /* [in] */ wchar_t __RPC_FAR *pwchLocalName,
														   /* [in] */ int cchLocalName,
														   /* [in] */ wchar_t __RPC_FAR *pwchQName,
														   /* [in] */ int cchQName,
														   /* [in] */ ISAXAttributes __RPC_FAR *pAttributes)
{
	UNREFERENCED_PARAMETER(pAttributes);
	UNREFERENCED_PARAMETER(pwchNamespaceUri);
	UNREFERENCED_PARAMETER(pwchQName);
	UNREFERENCED_PARAMETER(cchQName);
	UNREFERENCED_PARAMETER(cchNamespaceUri);

	HRESULT hr = S_OK;
	
	int size = cchLocalName+1;

	char *buf = (char *)malloc(sizeof(char) * size);
	memset(buf, 0, size);
	wcstombs(buf, pwchLocalName, cchLocalName);

	LogDetail("\tStart Element: ");
	LogDetail(buf);
	LogDetail("\n");

	if (strcmp(buf, "ID") == 0 && (m_CurrentTag == "Batch" || m_CurrentTag == "Feed"))
		;
	else
		m_CurrentTag = buf;

	if (m_CurrentTag.CompareNoCase("Location") == 0) {
		m_pCurrentLocation = new CLocation;
		m_InterfaceType = CWMSGroupConnection::LocationInterface;
		Log("\t\tStart of Location\n");
	}

	else if (m_CurrentTag.CompareNoCase("Product") == 0) {
		m_pCurrentProduct = new CProductPack;
		m_InterfaceType = CWMSGroupConnection::ProductInterface;
		Log("\t\tStart of Product\n");
	}

	else if (m_CurrentTag.CompareNoCase("ProductLocationAssignment") == 0) {
		m_pCurrentAssignment = new CAssignment;
		m_InterfaceType = CWMSGroupConnection::AssignmentInterface;
		Log("\t\tStart of Assignment/Move\n");
	}

	else if (m_CurrentTag.CompareNoCase("LocationConfirmation") == 0) {
		m_pCurrentConfirmation = new CConfirmation;
		m_pCurrentConfirmation->m_InterfaceType = CWMSGroupConnection::LocationConfirmationInterface;
		m_InterfaceType = CWMSGroupConnection::LocationConfirmationInterface;
		Log("\t\tStart of Location Confirmation\n");
	}

	else if (m_CurrentTag.CompareNoCase("ProductLocationAssignmentConfirmation") == 0) {
		m_pCurrentConfirmation = new CConfirmation;
		m_pCurrentConfirmation->m_InterfaceType = CWMSGroupConnection::AssignmentConfirmationInterface;
		m_InterfaceType = CWMSGroupConnection::AssignmentConfirmationInterface;
		Log("\t\tStart of Assignment/Move Confirmation\n");
	}

	free(buf);

	m_CurrentCharacters.Empty();

    return hr;
}

       
HRESULT STDMETHODCALLTYPE CSaxContentHandler::endElement(/* [in] */ wchar_t __RPC_FAR *pwchNamespaceUri,
														 /* [in] */ int cchNamespaceUri,
														 /* [in] */ wchar_t __RPC_FAR *pwchLocalName,
														 /* [in] */ int cchLocalName,
														 /* [in] */ wchar_t __RPC_FAR *pwchQName,
														 /* [in] */ int cchQName)
{
	UNREFERENCED_PARAMETER(pwchNamespaceUri);
	UNREFERENCED_PARAMETER(pwchQName);
	UNREFERENCED_PARAMETER(cchQName);
	UNREFERENCED_PARAMETER(cchNamespaceUri);
	
	int size = cchLocalName+1;
	char *buf = (char *)malloc(sizeof(char) * size);
	memset(buf, 0, size);
	wcstombs(buf, pwchLocalName, cchLocalName);
	LogDetail("\tEnd Element: ");
	LogDetail(buf);
	LogDetail("\n");
	
	CString tag(buf);
	free(buf);
	
	tag.TrimLeft();
	tag.TrimRight();

	m_CurrentCharacters.TrimLeft();
	m_CurrentCharacters.TrimRight();

	if (tag.CompareNoCase("Location") == 0) {
		CInboundQueueRecord *pRecord = new CInboundQueueRecord;
		pRecord->m_BatchId = m_CurrentBatchId;
		pRecord->m_FeedId = m_CurrentFeedId;
		pRecord->m_LineNumber = m_CurrentLineNumber;
		pRecord->m_InterfaceType = m_InterfaceType;
		pRecord->m_WMSId = m_CurrentWMSId;
		pRecord->m_WMSDetailId = m_CurrentWMSDetailId;
		pRecord->m_Action = m_CurrentAction;
		pRecord->m_pRecord = (void *)m_pCurrentLocation;
		m_pQueue->Add(pRecord);
		// Main program is responsible for deleting records
		CString temp;
		temp.Format("\t\tEnd of Location: %s(%d)\n", m_pCurrentLocation->m_Description, 
			m_pCurrentLocation->m_LocationKey);
		Log(temp);
	}
	else if (tag.CompareNoCase("Product") == 0) {
		CInboundQueueRecord *pRecord = new CInboundQueueRecord;
		pRecord->m_BatchId = m_CurrentBatchId;
		pRecord->m_FeedId = m_CurrentFeedId;
		pRecord->m_LineNumber = m_CurrentLineNumber;
		pRecord->m_InterfaceType = m_InterfaceType;
		pRecord->m_WMSId = m_CurrentWMSId;
		pRecord->m_WMSDetailId = m_CurrentWMSDetailId;
		pRecord->m_Action = m_CurrentAction;
		pRecord->m_pRecord = (void *)m_pCurrentProduct;
		m_pCurrentProduct->m_Status = CProductPack::Integrated;
		m_pQueue->Add(pRecord);
		
		// Some minor business logic - probably should be somewhere else
		// If they don't have UOI but they do have units per shipping unit
		// compare that to packs to determine what the UOI is
		if (m_pCurrentProduct->m_UnitOfIssue < 0) {
			if (m_pCurrentProduct->m_RetailUnitsPerShippingUnit == m_pCurrentProduct->m_CasePack)
				m_pCurrentProduct->m_UnitOfIssue = UOI_CASE;
			else if (m_pCurrentProduct->m_RetailUnitsPerShippingUnit == m_pCurrentProduct->m_InnerPack)
				m_pCurrentProduct->m_UnitOfIssue = UOI_INNER;
			else if (m_pCurrentProduct->m_RetailUnitsPerShippingUnit == 1)
				m_pCurrentProduct->m_UnitOfIssue = UOI_EACH;
			else
				m_pCurrentProduct->m_UnitOfIssue = UOI_CASE;
		}
		
		// Main program is responsible for deleting records
		CString temp;
		temp.Format("\t\tEnd of Product:  %s/%s - %s(%d)\n", 
			m_pCurrentProduct->m_WMSProductID, m_pCurrentProduct->m_WMSProductDetailID,
			m_pCurrentProduct->m_Description, 
			m_pCurrentProduct->m_ProductKey);
		Log(temp);
	}
	else if (tag.CompareNoCase("ProductLocationAssignment") == 0) {
		CInboundQueueRecord *pRecord = new CInboundQueueRecord;
		pRecord->m_BatchId = m_CurrentBatchId;
		pRecord->m_FeedId = m_CurrentFeedId;
		pRecord->m_LineNumber = m_CurrentLineNumber;
		pRecord->m_InterfaceType = m_InterfaceType;
		pRecord->m_WMSId = m_CurrentWMSId;
		pRecord->m_WMSDetailId = m_CurrentWMSDetailId;
		pRecord->m_Action = m_CurrentAction;
		pRecord->m_pRecord = (void *)m_pCurrentAssignment;
		if (m_pCurrentAssignment->m_IsAddFacing || m_pCurrentAssignment->m_FromLocationKey <= 0)
			pRecord->m_Action = CInterfaceHelper::Add;
		else if (m_pCurrentAssignment->m_IsDeleteFacing || m_pCurrentAssignment->m_ToLocationKey <= 0)
			pRecord->m_Action = CInterfaceHelper::Delete;
		else
			pRecord->m_Action = CInterfaceHelper::Modify;
		
		m_pQueue->Add(pRecord);
		// Main program is responsible for deleting records
		CString temp;
		temp.Format("\t\tEnd of ProductLocationAssignment: %d: %d -> %d\n", m_pCurrentAssignment->m_ProductKey, 
			m_pCurrentAssignment->m_FromLocationKey, m_pCurrentAssignment->m_ToLocationKey);
		Log(temp);
	}
	else if (tag.CompareNoCase("LocationConfirmation") == 0) {
		CInboundQueueRecord *pRecord = new CInboundQueueRecord;
		pRecord->m_BatchId = m_CurrentBatchId;
		pRecord->m_FeedId = m_CurrentFeedId;
		pRecord->m_LineNumber = m_CurrentLineNumber;
		pRecord->m_InterfaceType = m_InterfaceType;
		pRecord->m_WMSId = m_CurrentWMSId;
		pRecord->m_WMSDetailId = m_CurrentWMSDetailId;
		pRecord->m_Action = m_CurrentAction;
		pRecord->m_pRecord = m_pCurrentConfirmation;
		
		m_pQueue->Add(pRecord);
		// Main program is responsible for deleting records
		CString temp;
		temp.Format("\t\tEnd of LocationConfirmation: Batch: %d -> %d(%s)\n", 
			m_pCurrentConfirmation->m_BatchId, m_pCurrentConfirmation->m_ReasonCode,
			m_pCurrentConfirmation->m_ReasonText);
		Log(temp);
	}
	else if (tag.CompareNoCase("ProductLocationAssignmentConfirmation") == 0) {
		CInboundQueueRecord *pRecord = new CInboundQueueRecord;
		pRecord->m_BatchId = m_CurrentBatchId;
		pRecord->m_FeedId = m_CurrentFeedId;
		pRecord->m_LineNumber = m_CurrentLineNumber;
		pRecord->m_InterfaceType = m_InterfaceType;
		pRecord->m_WMSId = m_CurrentWMSId;
		pRecord->m_WMSDetailId = m_CurrentWMSDetailId;
		pRecord->m_Action = m_CurrentAction;
		pRecord->m_pRecord = m_pCurrentConfirmation;
		
		m_pQueue->Add(pRecord);
		// Main program is responsible for deleting records
		CString temp;
		temp.Format("\t\tEnd of ProductLocationAssignmentConfirmation: Batch: %d -> %d(%s)\n", 
			m_pCurrentConfirmation->m_BatchId, m_pCurrentConfirmation->m_ReasonCode,
			m_pCurrentConfirmation->m_ReasonText);
		Log(temp);
	}
	else if (tag.CompareNoCase("Batch") == 0) {
		// push end-of-batch on to queue
		CInboundQueueRecord *pRecord = new CInboundQueueRecord;
		pRecord->m_BatchId = m_CurrentBatchId;
		pRecord->m_EndOfBatch = 1;
		m_pQueue->Add(pRecord);
		CString temp;
		temp.Format("\tEnd of Batch: %d\n", m_CurrentBatchId);
		Log(temp);
	}
	else if (tag.CompareNoCase("Feed") == 0) {
		CInboundQueueRecord *pRecord = new CInboundQueueRecord;
		pRecord->m_FeedId = m_CurrentFeedId;
		pRecord->m_EndOfFeed = 1;
		m_pQueue->Add(pRecord);
		CString temp;
		temp.Format("End of Feed: %d\n", m_CurrentFeedId);
		Log(temp);
	}
	else if (m_CurrentTag.CompareNoCase("Feed") == 0) {
		if (tag.CompareNoCase("ID") == 0) {
			m_CurrentFeedId = atoi(m_CurrentCharacters);
			CString temp;
			temp.Format("Start of Feed: %d\n", m_CurrentFeedId);
			Log(temp);
		}
	}
	else if (m_CurrentTag.CompareNoCase("Batch") == 0) {
		if (tag.CompareNoCase("ID") == 0) {
			m_CurrentBatchId = atoi(m_CurrentCharacters);
			CString temp;
			temp.Format("\tStart of Batch: %d\n", m_CurrentBatchId);
			Log(temp);
		}
	}
	else {
		CString tmp;
		tmp.Format("\t\t\t\tValue: %s\n", m_CurrentCharacters);
		LogDetail(tmp);
		
		if (m_CurrentBatchId < 0 || m_InterfaceType < 0) {
			return S_OK;
		}
		
		if (m_CurrentTag.CompareNoCase("Action") == 0) {
			if (tag.CompareNoCase("Add") == 0)
				m_CurrentAction = CInterfaceHelper::Add;
			else if (tag.CompareNoCase("Modify") == 0)
				m_CurrentAction = CInterfaceHelper::Modify;
			else if (tag.CompareNoCase("Delete") == 0)
				m_CurrentAction = CInterfaceHelper::Delete;
			else {
				m_CurrentAction = -1;
				return S_OK;
			}
		}
		
		switch (m_InterfaceType) {
		case CWMSGroupConnection::LocationInterface:
			ProcessLocationElement(m_CurrentCharacters);
			break;
		case CWMSGroupConnection::ProductInterface:
			ProcessProductElement(m_CurrentCharacters);
			break;
		case CWMSGroupConnection::AssignmentInterface:
			ProcessAssignmentElement(m_CurrentCharacters);
			break;
		case CWMSGroupConnection::LocationConfirmationInterface:
			ProcessLocationConfirmationElement(m_CurrentCharacters);
			break;
		case CWMSGroupConnection::AssignmentConfirmationInterface:
			ProcessAssignmentConfirmationElement(m_CurrentCharacters);
			break;
		}
		
	}


    return S_OK;
}

HRESULT STDMETHODCALLTYPE CSaxContentHandler::startDocument()
{
	m_CurrentFeedId = -1;
	m_CurrentBatchId = -1;
	m_CurrentLineNumber = -1;
	m_CurrentAction = -1;
	m_CurrentTag = "";

	m_Error = 0;
	m_ErrorText = "";
	m_hLogFile = NULL;

	if (m_LogFileName != "") {
		m_hLogFile = CreateFile(
			m_LogFileName,
			GENERIC_WRITE,
			FILE_SHARE_WRITE,
			NULL,
			CREATE_ALWAYS,
			NULL,
			NULL);
		if (m_hLogFile == INVALID_HANDLE_VALUE)
			m_hLogFile = NULL;

	}

	Log("Starting XML Document\n");
	

	return S_OK;
}


HRESULT STDMETHODCALLTYPE CSaxContentHandler::endDocument()
{
	Log("End of Document");

	if (m_hLogFile != NULL)
		CloseHandle(m_hLogFile);

    return S_OK;
}
    

HRESULT STDMETHODCALLTYPE CSaxContentHandler::characters(/* [in] */ wchar_t __RPC_FAR *pwchChars,
											 /* [in] */ int cchChars)
{
	/*
	BOOL isWhiteSpace = TRUE;

	for (int i=0; i < cchChars; ++i) {
		if (! iswspace(pwchChars[i])) {
			isWhiteSpace = FALSE;
			break;
		}
	}

	if (isWhiteSpace)
		return S_OK;
	
	*/

	int size = cchChars+1;
	char *buf = (char *)malloc(sizeof(char) * size);
	memset(buf, 0, size);
	wcstombs(buf, pwchChars, cchChars);

	m_CurrentCharacters += buf;

	/*
	CString tmp;
	tmp.Format("\t\t\t\tValue: %s\n", buf);
	LogDetail(tmp);

	if (m_CurrentTag.CompareNoCase("Feed") == 0) {
		m_CurrentFeedId = atoi(buf);
		CString temp;
		temp.Format("Start of Feed: %d\n", m_CurrentFeedId);
		Log(temp);
	}

	else if (m_CurrentTag.CompareNoCase("Batch") == 0) {
		m_CurrentBatchId = atoi(buf);
		CString temp;
		temp.Format("\tStart of Batch: %d\n", m_CurrentBatchId);
		Log(temp);
	}

	else {
		
		if (m_CurrentBatchId < 0 || m_InterfaceType < 0) {
			free(buf);
			return S_OK;
		}

		if (m_CurrentTag.CompareNoCase("Action") == 0) {
			if (strcmp(buf, "Add") == 0)
				m_CurrentAction = CInterfaceHelper::Add;
			else if (strcmp(buf, "Modify") == 0)
				m_CurrentAction = CInterfaceHelper::Modify;
			else if (strcmp(buf, "Delete") == 0)
				m_CurrentAction = CInterfaceHelper::Delete;
			else {
				m_CurrentAction = -1;
				free(buf);
				return S_OK;
			}
		}

		switch (m_InterfaceType) {
		case CWMSGroupConnection::LocationInterface:
			ProcessLocationElement(buf);
			break;
		case CWMSGroupConnection::ProductInterface:
			ProcessProductElement(buf);
			break;
		case CWMSGroupConnection::AssignmentInterface:
			ProcessAssignmentElement(buf);
			break;
		case CWMSGroupConnection::LocationConfirmationInterface:
			ProcessLocationConfirmationElement(buf);
			break;
		case CWMSGroupConnection::AssignmentConfirmationInterface:
			ProcessAssignmentConfirmationElement(buf);
			break;
		}
		
	}

	*/
	free(buf);
	
	return S_OK;
}


CSaxContentHandler *CSaxContentHandler::CreateInstance()
{
	CSaxContentHandler *pSaxContentHandler = NULL;
	pSaxContentHandler = new CSaxContentHandler();
	assert(pSaxContentHandler!=NULL);
	return pSaxContentHandler;
}

void CSaxContentHandler::ProcessLocationElement(const CString &value)
{
	CString str = value;

	str.TrimLeft(" ");
	str.TrimRight(" ");

	if (m_CurrentTag.CompareNoCase("Key") == 0) {
		m_pCurrentLocation->m_LocationKey = atoi(str);
		LogDetail("\t\t\tRecognized Key\n");
	}

	else if (m_CurrentTag.CompareNoCase("LineNumber") == 0) {
		m_CurrentLineNumber = atoi(str);
		LogDetail("\t\t\tRecognized LineNumber\n");
	}

	else if (m_CurrentTag.CompareNoCase("DCID") == 0) {
		m_CurrentWMSId = str;
		LogDetail("\t\t\tRecognized DCID\n");
	}
	
	else if (m_CurrentTag.CompareNoCase("WarehouseID") == 0) {
		m_CurrentWMSDetailId = str;
		LogDetail("\t\t\tRecognized WarehouseID\n");
	}

	else if (m_CurrentTag.CompareNoCase("WMSID") == 0) {
		m_CurrentWMSId = str;
		LogDetail("\t\t\tRecognized WMSID\n");
	}
	
	else if (m_CurrentTag.CompareNoCase("WMSDetailID") == 0) {
		m_CurrentWMSDetailId = str;
		LogDetail("\t\t\tRecognized WMSDetailID\n");
	}

	else if (m_CurrentTag.CompareNoCase("Name") == 0) {
		m_pCurrentLocation->m_Description = str;
		LogDetail("\t\t\tRecognized Name\n");
	}

	else if (m_CurrentTag.CompareNoCase("Category") == 0) {
		if (strcmp(str, "Selection") == 0)
			m_pCurrentLocation->m_Usage = CLocation::loSelect;
		else if (strcmp(str, "Reserve") == 0)
			m_pCurrentLocation->m_Usage = CLocation::loReserve;
		else
			m_pCurrentLocation->m_Usage = CLocation::loReserve;
		LogDetail("\t\t\tRecognized Category\n");
	}

	else if (m_CurrentTag.CompareNoCase("Handling") == 0) {
		if (strcmp(str, "Pallet") == 0)
			m_pCurrentLocation->m_HandlingMethod = CLocation::loPallet;
		else if (strcmp(str, "Case") == 0)
			m_pCurrentLocation->m_HandlingMethod = CLocation::loCase;
		else
			m_pCurrentLocation->m_HandlingMethod = CLocation::loCase;
		LogDetail("\t\t\tRecognized Handling\n");
	}

	else if (m_CurrentTag.CompareNoCase("SelectionSequence") == 0) {
		m_pCurrentLocation->m_SelectionSequence = str;
		LogDetail("\t\t\tRecognized SelectionSequence\n");
	}

	else if (m_CurrentTag.CompareNoCase("ReplenishmentSequence") == 0) {
		m_pCurrentLocation->m_ReplenishmentSequence = str;
		LogDetail("\t\t\tRecognized ReplenishmentSequence\n");
	}
	
	else if (m_CurrentTag.CompareNoCase("BackfillID") == 0) {
		m_pCurrentLocation->m_BackfillId = str;
		LogDetail("\t\t\tRecognized BackfillID\n");
	}

	else if (m_CurrentTag.CompareNoCase("BackfillXCoordinate") == 0) {
		m_pCurrentLocation->m_BackfillCoordinates.m_X = atoi(str);
		LogDetail("\t\t\tRecognized BackfillXCoordinate\n");
	}
	
	else if (m_CurrentTag.CompareNoCase("BackfillYCoordinate") == 0) {
		m_pCurrentLocation->m_BackfillCoordinates.m_Y = atoi(str);
		LogDetail("\t\t\tRecognized BackfillYCoordinate\n");
	}

	else if (m_CurrentTag.CompareNoCase("BackfillZCoordinate") == 0) {
		m_pCurrentLocation->m_BackfillCoordinates.m_Z = atoi(str);
		LogDetail("\t\t\tRecognized BackfillZCoordinate\n");
	}

	else if (m_CurrentTag.CompareNoCase("StockerPoint") == 0) {
		m_pCurrentLocation->m_StockerId = str;
		LogDetail("\t\t\tRecognized StockerPoint\n");
	}

	else if (m_CurrentTag.CompareNoCase("StockerXCoordinate") == 0) {
		m_pCurrentLocation->m_StockerCoordinates.m_X = atoi(str);
		LogDetail("\t\t\tRecognized StockerXCoordinate\n");
	}
	
	else if (m_CurrentTag.CompareNoCase("StockerYCoordinate") == 0) {
		m_pCurrentLocation->m_StockerCoordinates.m_Y = atoi(str);
		LogDetail("\t\t\tRecognized StockerYCoordinate\n");
	}

	else if (m_CurrentTag.CompareNoCase("StockerZCoordinate") == 0) {
		m_pCurrentLocation->m_StockerCoordinates.m_Z = atoi(str);
		LogDetail("\t\t\tRecognized StockerZCoordinate\n");
	}

	else if (m_CurrentTag.CompareNoCase("MaximumLocationWeight") == 0) {
		m_pCurrentLocation->m_WeightCapacity = atoi(str);
		LogDetail("\t\t\tRecognized MaximumLocationWeight\n");
	}

	else if (m_CurrentTag.CompareNoCase("XCoordinate") == 0) {
		m_pCurrentLocation->m_Coordinates.m_X = atof(str);
		LogDetail("\t\t\tRecognized XCoordinate - Update not allowed\n");
	}

	else if (m_CurrentTag.CompareNoCase("YCoordinate") == 0) {
		m_pCurrentLocation->m_Coordinates.m_Y = atof(str);
		LogDetail("\t\t\tRecognized YCoordinate - Update not allowed\n");
	}

	else if (m_CurrentTag.CompareNoCase("ZCoordinate") == 0) {
		m_pCurrentLocation->m_Coordinates.m_Z = atof(str);
		LogDetail("\t\t\tRecognized ZCoordinate - Update not allowed\n");
	}
	
	else if (m_CurrentTag.CompareNoCase("Depth") == 0) {
		m_pCurrentLocation->m_Depth = atof(str);
		LogDetail("\t\t\tRecognized Depth - Update not allowed\n");
	}

	else if (m_CurrentTag.CompareNoCase("Width") == 0) {
		m_pCurrentLocation->m_Width = atof(str);
		LogDetail("\t\t\tRecognized Width - Update not allowed\n");
	}
	
	else if (m_CurrentTag.CompareNoCase("SelectionPositionHeight") == 0) {
		m_pCurrentLocation->m_Height = atof(str);
		LogDetail("\t\t\tRecognized SelectionPositionHeight - Update not allowed\n");
	}
	
	// The following fields cannot be updated by the WMS
	else if (m_CurrentTag.CompareNoCase("StackLimit") == 0) {
		LogDetail("\t\t\tRecognized StackLimit - Update not allowed\n");
	}
	
	else if (m_CurrentTag.CompareNoCase("Level") == 0) {
		LogDetail("\t\t\tRecognized Level - Update not allowed\n");
	}
	
	else if (m_CurrentTag.CompareNoCase("MaximumPositionWeight") == 0) {
		LogDetail("\t\t\tRecognized MaximumPositionWeight - Update not allowed\n");
	}
	
	else if (m_CurrentTag.CompareNoCase("MaximumColumnWeight") == 0) {
		LogDetail("\t\t\tRecognized MaximumColumnWeight - Update not allowed\n");
	}
	
	else if (m_CurrentTag.CompareNoCase("Action") == 0) {
		LogDetail("\t\t\tRecognized Action\n");
	}
	
	else if (m_CurrentTag.CompareNoCase("RackType") == 0) {
		LogDetail("\t\t\tRecognized RackType - Update not allowed\n");
	}
	
	else if (m_CurrentTag.CompareNoCase("SelectionPositions") == 0) {
		LogDetail("\t\t\tRecognized SelectionPositions - Update not allowed\n");
	}
	
	else if (m_CurrentTag.CompareNoCase("ReservePositions") == 0) {
		LogDetail("\t\t\tRecognized ReservePositions - Update not allowed\n");
	}
	
	else if (m_CurrentTag.CompareNoCase("ReservePositionHeight") == 0) {
		LogDetail("\t\t\tRecognized ReservePositionHeight - Update not allowed\n");
	}

	else {
		CString temp;
		temp.Format("\t\t\t%s not recognized. Processing as UDF.\n", m_CurrentTag);
		LogDetail(temp);
		CLevelProfileExternalInfo *pInfo = new CLevelProfileExternalInfo;
		pInfo->m_Name = m_CurrentTag;
		pInfo->m_Value = str;
		m_pCurrentLocation->m_InfoList.Add(pInfo);
	}

	
}

void CSaxContentHandler::ProcessProductElement(const CString &value)
{
	CString str = value;
	CString temp;

	str.TrimLeft(" ");
	str.TrimRight(" ");

	if (m_CurrentTag.CompareNoCase("Key") == 0) {
		if (str == "Generate")
			m_pCurrentProduct->m_ProductKey = CProductPack::KeyGenerate;
		else if (str == "Lookup")
			m_pCurrentProduct->m_ProductKey = CProductPack::KeyLookup;
		else
			m_pCurrentProduct->m_ProductKey = atoi(str);

		LogDetail("\t\t\tRecognized Key\n");
	}

	else if (m_CurrentTag.CompareNoCase("LineNumber") == 0) {
		m_CurrentLineNumber = atoi(str);
		LogDetail("\t\t\tRecognized LineNumber\n");
	}

	else if (m_CurrentTag.CompareNoCase("Action") == 0) {
		LogDetail("\t\t\tRecognized Action\n");
	}

	else if (m_CurrentTag.CompareNoCase("DCID") == 0 || m_CurrentTag.CompareNoCase("WMSID") == 0) {
		m_CurrentWMSId = str;
		LogDetail("\t\t\tRecognized DCID\n");
	}
	
	else if (m_CurrentTag.CompareNoCase("WarehouseID") == 0 || m_CurrentTag.CompareNoCase("WMSDetailID") == 0) {
		m_CurrentWMSDetailId = str;
		LogDetail("\t\t\tRecognized WarehouseID\n");
	}

	else if (m_CurrentTag.CompareNoCase("Name") == 0 || m_CurrentTag.CompareNoCase("WMSProductID") == 0 ||
		m_CurrentTag.CompareNoCase("ID") == 0) {
		m_pCurrentProduct->m_WMSProductID = str;
		LogDetail("\t\t\tRecognized Name\n");
	}

	else if (m_CurrentTag.CompareNoCase("Detail") == 0 || m_CurrentTag.CompareNoCase("WMSProductDetailID") == 0) {
		m_pCurrentProduct->m_WMSProductDetailID = str;
		LogDetail("\t\t\tRecognized Detail\n");
	}

	else if (m_CurrentTag.CompareNoCase("Description") == 0) {
		m_pCurrentProduct->m_Description = str;
		m_pCurrentProduct->m_Container.m_Description = str;
		LogDetail("\t\t\tRecognized Description\n");
	}

	else if (m_CurrentTag.CompareNoCase("IsActive") == 0) {
		m_pCurrentProduct->m_IsActive = (str.Left(1).CompareNoCase("Y") == 0);
		LogDetail("\t\t\tRecognized IsActive\n");
	}

	else if (m_CurrentTag.CompareNoCase("CrushFactor") == 0) {
		m_pCurrentProduct->m_CrushFactor = str;
		LogDetail("\t\t\tRecognized CrushFactor\n");
	}
	
	else if (m_CurrentTag.CompareNoCase("ProductGroup") == 0) {
		m_pCurrentProduct->m_CommodityType = str;
		LogDetail("\t\t\tRecognized ProductGroup\n");
	}

	else if (m_CurrentTag.CompareNoCase("IsHazardous") == 0) {
		m_pCurrentProduct->m_IsHazard = (str.Left(1).CompareNoCase("Y") == 0);
		LogDetail("\t\t\tRecognized IsHazardous\n");
	}

	else if (m_CurrentTag.CompareNoCase("IsConveyable") == 0) {
		m_pCurrentProduct->m_IsPickToBelt = (str.Left(1).CompareNoCase("Y") == 0);
		LogDetail("\t\t\tRecognized IsConveyable\n");
	}

	else if (m_CurrentTag.CompareNoCase("UnitsPerStorageCase") == 0) {
		m_pCurrentProduct->m_CasePack = atoi(str);
		LogDetail("\t\t\tRecognized UnitsPerStorageCase\n");
	}

	else if (m_CurrentTag.CompareNoCase("UnitsPerShipCase") == 0) {
		m_pCurrentProduct->m_RetailUnitsPerShippingUnit = atoi(str);
		LogDetail("\t\t\tRecognized UnitsPerShipCase\n");
	}

	else if (m_CurrentTag.CompareNoCase("UnitsPerInnerPack") == 0) {
		m_pCurrentProduct->m_InnerPack = atoi(str);
		LogDetail("\t\t\tRecognized UnitsPerInnerPack\n");
	}

	else if (m_CurrentTag.CompareNoCase("CasesPerTier") == 0) {
		m_pCurrentProduct->m_Container.m_Ti = atoi(str);
		LogDetail("\t\t\tRecognized CasesPerTier\n");
	}

	else if (m_CurrentTag.CompareNoCase("TiersPerPallet") == 0) {
		m_pCurrentProduct->m_Container.m_Hi = atoi(str);
		LogDetail("\t\t\tRecognized TiersPerPallet\n");
	}

	else if (m_CurrentTag.CompareNoCase("NumberInPallet") == 0) {
		m_pCurrentProduct->m_NumberInPallet = atoi(str);
		LogDetail("\t\t\tRecognized NumberInPallet\n");
	}

	else if (m_CurrentTag.CompareNoCase("FullPalletHeight") == 0) {
		if (atof(str) > 0) {
			m_pCurrentProduct->m_Container.m_Height = atof(str);
			m_pCurrentProduct->m_Container.m_IsHeightOverride = TRUE;
		}
		else
			m_pCurrentProduct->m_Container.m_IsHeightOverride = FALSE;

		LogDetail("\t\t\tRecognized FullPalletHeight\n");
	}

	else if (m_CurrentTag.CompareNoCase("StorageCaseHeight") == 0) {
		m_pCurrentProduct->m_CaseHeight = atof(str);
		LogDetail("\t\t\tRecognized StorageCaseHeight\n");
	}

	else if (m_CurrentTag.CompareNoCase("StorageCaseWidth") == 0) {
		m_pCurrentProduct->m_CaseWidth = atof(str);
		LogDetail("\t\t\tRecognized StorageCaseWidth\n");
	}
	
	else if (m_CurrentTag.CompareNoCase("StorageCaseLength") == 0) {
		m_pCurrentProduct->m_CaseLength = atof(str);
		LogDetail("\t\t\tRecognized StorageCaseLength\n");
	}

	else if (m_CurrentTag.CompareNoCase("StorageCaseWeight") == 0) {
		m_pCurrentProduct->m_Weight = atof(str);
		LogDetail("\t\t\tRecognized StorageCaseWeight\n");
	}

	else if (m_CurrentTag.CompareNoCase("InnerPackHeight") == 0) {
		m_pCurrentProduct->m_InnerHeight = atof(str);
		LogDetail("\t\t\tRecognized InnerPackHeight\n");
	}

	else if (m_CurrentTag.CompareNoCase("InnerPackWidth") == 0) {
		m_pCurrentProduct->m_InnerWidth = atof(str);
		LogDetail("\t\t\tRecognized InnerPackWidth\n");
	}
	
	else if (m_CurrentTag.CompareNoCase("InnerPackLength") == 0) {
		m_pCurrentProduct->m_InnerLength = atof(str);
		LogDetail("\t\t\tRecognized InnerPackLength\n");
	}

	else if (m_CurrentTag.CompareNoCase("InnerCaseWeight") == 0) {
		LogDetail("\t\t\tRecognized InnerPackWeight - Ignoring.\n");
	}

	else if (m_CurrentTag.CompareNoCase("EachesHeight") == 0) {
		m_pCurrentProduct->m_EachHeight = atof(str);
		LogDetail("\t\t\tRecognized EachesHeight\n");
	}

	else if (m_CurrentTag.CompareNoCase("EachesWidth") == 0) {
		m_pCurrentProduct->m_EachWidth = atof(str);
		LogDetail("\t\t\tRecognized EachesWidth\n");
	}
	
	else if (m_CurrentTag.CompareNoCase("EachesLength") == 0) {
		m_pCurrentProduct->m_EachLength = atof(str);
		LogDetail("\t\t\tRecognized EachesLength\n");
	}

	else if (m_CurrentTag.CompareNoCase("EachesWeight") == 0) {
		LogDetail("\t\t\tRecognized EachesWeight - Ignoring.\n");
	}
	
	else if (m_CurrentTag.CompareNoCase("PalletWoodHeight") == 0) {
		m_pCurrentProduct->m_Container.m_Height = atof(str);
		LogDetail("\t\t\tRecognized PalletWoodHeight\n");
	}
	
	else if (m_CurrentTag.CompareNoCase("PalletWoodWidth") == 0) {
		m_pCurrentProduct->m_Container.m_Width = atof(str);
		LogDetail("\t\t\tRecognized PalletWoodWidth\n");
	}
	
	else if (m_CurrentTag.CompareNoCase("PalletWoodLength") == 0) {
		m_pCurrentProduct->m_Container.m_Length = atof(str);
		LogDetail("\t\t\tRecognized PalletWoodLength\n");
	}	

	else if (m_CurrentTag.CompareNoCase("NestingWidthIncrement") == 0) {
		m_pCurrentProduct->m_NestedWidth = atof(str);
		LogDetail("\t\t\tRecognized NestingWidthIncrement\n");
	}

	else if (m_CurrentTag.CompareNoCase("NestingLengthIncrement") == 0) {
		m_pCurrentProduct->m_NestedLength = atof(str);
		LogDetail("\t\t\tRecognized NestingLengthIncrement\n");
	}
	
	else if (m_CurrentTag.CompareNoCase("NestingHeightIncrement") == 0) {
		m_pCurrentProduct->m_NestedHeight = atof(str);
		LogDetail("\t\t\tRecognized NestingHeightIncrement\n");
	}

	else if (m_CurrentTag.CompareNoCase("ErgonomicPalletPosition") == 0) {
		LogDetail("\t\t\tRecognized ErgonomicPalletPosition - Ignoring\n");
	}
	
	else if (m_CurrentTag.CompareNoCase("UnitOfIssue") == 0) {
		LogDetail("\t\t\tRecognized UnitOfIssue\n");

		if (str == "Each")
			m_pCurrentProduct->m_UnitOfIssue = UOI_EACH;
		else if (str == "Inner")
			m_pCurrentProduct->m_UnitOfIssue = UOI_INNER;
		else if (str == "Case")
			m_pCurrentProduct->m_UnitOfIssue = UOI_CASE;
		else if (str == "Pallet")
			m_pCurrentProduct->m_UnitOfIssue = UOI_PALLET;
		else  {
			m_pCurrentProduct->m_UnitOfIssue = -1;
			temp.Format("\t\t\tUnitOfIssue - value %s not recognized.\n", str);
			LogDetail(temp);
		}
			

	}
	
	else if (m_CurrentTag.CompareNoCase("AverageBalanceOnHand") == 0) {
		LogDetail("\t\t\tRecognized AverageBalanceOnHand\n");
		m_pCurrentProduct->m_BalanceOnHand = atof(str);
	}
	
	else if (m_CurrentTag.CompareNoCase("AverageWeeklyMovement") == 0) {
		m_pCurrentProduct->m_Movement = atof(str);
		LogDetail("\t\t\tRecognized AverageWeeklyMovement\n");
	}
	
	else if (m_CurrentTag.CompareNoCase("AverageWeeklyHits") == 0) {
		m_pCurrentProduct->m_NumberOfHits = atof(str);
		LogDetail("\t\t\tRecognized AverageWeeklyHits\n");
	}

	else if (m_CurrentTag.CompareNoCase("AllowWidthLengthSwap") == 0) {
		m_pCurrentProduct->m_RotateZAxis = atoi(str);
		LogDetail("\t\t\tRecognized AllowWidthLengthSwap\n");
	}

	else if (m_CurrentTag.CompareNoCase("AllowWidthHeightSwap") == 0) {
		m_pCurrentProduct->m_RotateYAxis = atoi(str);
		LogDetail("\t\t\tRecognized AllowWidthHeightSwap\n");
	}

	else if (m_CurrentTag.CompareNoCase("AllowHeightLengthSwap") == 0) {
		m_pCurrentProduct->m_RotateXAxis = atoi(str);
		LogDetail("\t\t\tRecognized AllowHeightLengthSwap\n");
	}

	else if (m_CurrentTag.CompareNoCase("LockAssignment") == 0) {
		m_pCurrentProduct->m_IsAssignmentLocked = atoi(str);
		LogDetail("\t\t\tRecognized LockAssignment\n");
	}

	else if (m_CurrentTag.CompareNoCase("OptimizationMethod") == 0) {
		if (str == "Labor")
			m_pCurrentProduct->m_OptimizeBy = CProductPack::OptimizeByLabor;
		else
			m_pCurrentProduct->m_OptimizeBy = CProductPack::OptimizeByCube;
		LogDetail("\t\t\tRecognized OptimizationMethod\n");
	}
	else {
		CString temp;
		temp.Format("\t\t\t%s not recognized. Processing as UDF.\n", m_CurrentTag);
		LogDetail(temp);
		CUDF *pUDF = new CUDF;
		pUDF->m_Name = m_CurrentTag;
		pUDF->m_Value = str;
		m_pCurrentProduct->m_UDFList.Add(pUDF);
	}

}

void CSaxContentHandler::ProcessAssignmentElement(const CString &value)
{
	CString str = value;
	CString temp;

	str.TrimLeft(" ");
	str.TrimRight(" ");

	if (m_CurrentTag.CompareNoCase("ProductKey") == 0) {
		if (str == "Generate")
			m_pCurrentAssignment->m_ProductKey = CProductPack::KeyGenerate;
		else if (str == "Lookup")
			m_pCurrentAssignment->m_ProductKey = CProductPack::KeyLookup;
		else
			m_pCurrentAssignment->m_ProductKey = atoi(str);
		LogDetail("\t\t\tRecognized Key\n");
	}

	else if (m_CurrentTag.CompareNoCase("LineNumber") == 0) {
		m_CurrentLineNumber = atoi(str);
		LogDetail("\t\t\tRecognized LineNumber\n");
	}

	else if (m_CurrentTag.CompareNoCase("Action") == 0) {
		LogDetail("\t\t\tRecognized Action\n");
	}

	else if (m_CurrentTag.CompareNoCase("DCID") == 0) {
		m_CurrentWMSId = str;
		LogDetail("\t\t\tRecognized DCID\n");
	}
	
	else if (m_CurrentTag.CompareNoCase("WarehouseID") == 0) {
		m_CurrentWMSDetailId = str;
		LogDetail("\t\t\tRecognized WarehouseID\n");
	}

	else if (m_CurrentTag.CompareNoCase("WMSID") == 0) {
		m_CurrentWMSId = str;
		LogDetail("\t\t\tRecognized WMSID\n");
	}
	
	else if (m_CurrentTag.CompareNoCase("WMSDetailID") == 0) {
		m_CurrentWMSDetailId = str;
		LogDetail("\t\t\tRecognized WMSDetailID\n");
	}	

	else if (m_CurrentTag.CompareNoCase("FromLocationKey") == 0) {
		m_pCurrentAssignment->m_FromLocationKey = atoi(str);
		LogDetail("\t\t\tRecognized FromLocationKey\n");
	}

	else if (m_CurrentTag.CompareNoCase("ToLocationKey") == 0) {
		m_pCurrentAssignment->m_ToLocationKey = atoi(str);
		LogDetail("\t\t\tRecognized ToLocationKey\n");
	}

	else if (m_CurrentTag.CompareNoCase("IsFromTemp") == 0) {
		m_pCurrentAssignment->m_IsFromTemp = (str.Left(1).CompareNoCase("Y") == 0);
		LogDetail("\t\t\tRecognized IsFromTemp\n");
	}

	else if (m_CurrentTag.CompareNoCase("IsToTemp") == 0) {
		m_pCurrentAssignment->m_IsToTemp = (str.Left(1).CompareNoCase("Y") == 0);
		LogDetail("\t\t\tRecognized IsToTemp\n");
	}

	else if (m_CurrentTag.CompareNoCase("IsAddFacing") == 0) {
		m_pCurrentAssignment->m_IsAddFacing = (str.Left(1).CompareNoCase("Y") == 0);
		LogDetail("\t\t\tRecognized IsAddFacing\n");
	}

	else if (m_CurrentTag.CompareNoCase("IsDeleteFacing") == 0) {
		m_pCurrentAssignment->m_IsDeleteFacing = (str.Left(1).CompareNoCase("Y") == 0);
		LogDetail("\t\t\tRecognized IsDeleteFacing\n");
	}

	else if (m_CurrentTag.CompareNoCase("WMSProductID") == 0) {
		m_pCurrentAssignment->m_WMSId = str;
		LogDetail("\t\t\tRecognized WMSProductID\n");
	}

	else if (m_CurrentTag.CompareNoCase("WMSProductDetailID") == 0) {
		m_pCurrentAssignment->m_WMSDetailId = str;
		LogDetail("\t\t\tRecognized WMSProductDetailID\n");
	}
	else if (m_CurrentTag.CompareNoCase("CaseQuantity") == 0) {
		m_pCurrentAssignment->m_CaseQuantity = atoi(str);
		LogDetail("\t\t\tRecognized CaseQuantity\n");
	}

}

void CSaxContentHandler::ProcessLocationConfirmationElement(const CString &value)
{
	CString str = value;

	str.TrimLeft(" ");
	str.TrimRight(" ");

	if (m_CurrentTag.CompareNoCase("ReasonCode") == 0) {
		m_pCurrentConfirmation->m_ReasonCode = atoi(str);
		LogDetail("\t\t\tRecognized Reason\n");
	}

	else if (m_CurrentTag.CompareNoCase("ReasonText") == 0) {
		m_pCurrentConfirmation->m_ReasonText = str;
		LogDetail("\t\t\tRecognized ReasonText\n");
	}
}

void CSaxContentHandler::ProcessAssignmentConfirmationElement(const CString &value)
{
}



HRESULT STDMETHODCALLTYPE CSaxContentHandler::error(/* [in] */ ISAXLocator * pLocator, 
								  /* [in] */ wchar_t * pwchErrorMessage,
								  /* [in] */ HRESULT hrErrorCode)
{
	UNREFERENCED_PARAMETER(hrErrorCode);

	m_Error = 0;
	int size = wcslen(pwchErrorMessage)+1;
	char *buf = (char *)malloc(sizeof(char) * size);
	memset(buf, 0, size);
	wcstombs(buf, pwchErrorMessage, size);

	int lineNo, colNo;

	pLocator->getLineNumber(&lineNo);
	pLocator->getColumnNumber(&colNo);
	
	m_ErrorText.Format("Non-fatal error - %s: Line %d, Column %d", buf, lineNo, colNo);
	
	Log(m_ErrorText);

	return S_OK;
}


HRESULT STDMETHODCALLTYPE CSaxContentHandler::fatalError(/* [in] */ ISAXLocator * pLocator, 
									   /* [in] */ wchar_t * pwchErrorMessage,
									   /* [in] */ HRESULT hrErrorCode)
{	
	UNREFERENCED_PARAMETER(hrErrorCode);

	m_Error = 1;

	int size = wcslen(pwchErrorMessage)+1;
	char *buf = (char *)malloc(sizeof(char) * size);
	memset(buf, 0, size);
	wcstombs(buf, pwchErrorMessage, size);
	
	int lineNo, colNo;

	pLocator->getLineNumber(&lineNo);
	pLocator->getColumnNumber(&colNo);

	m_ErrorText.Format("Fatal error - %s\t\tLine %d, Column %d\r\n", buf, lineNo, colNo);

	Log(m_ErrorText);

	if (m_hLogFile != NULL)
		CloseHandle(m_hLogFile);

	return S_OK;
}


HRESULT STDMETHODCALLTYPE CSaxContentHandler::ignorableWarning(/* [in] */ ISAXLocator * pLocator, 
											 /* [in] */ wchar_t * pwchErrorMessage,
											 /* [in] */ HRESULT hrErrorCode)
{
	UNREFERENCED_PARAMETER(hrErrorCode);
	UNREFERENCED_PARAMETER(pLocator);

	CString temp;
	temp.Format("Warning: %s\n", pwchErrorMessage);
	LogDetail(temp);

	/*
	m_Error = -1;
	int size = wcslen(pwchErrorMessage);
	char *buf = (char *)malloc(sizeof(char) * size);
	memset(buf, 0, size);
	wcstombs(buf, pwchErrorMessage, size);
	
	m_ErrorText = buf;
	*/

	return S_OK;
}

void CSaxContentHandler::Log(const CString &text)
{
	if (m_hLogFile == NULL)
		return;

	unsigned long bytes;

	WriteFile(m_hLogFile, text, text.GetLength(), &bytes, NULL);

	return;
}

void CSaxContentHandler::LogDetail(const CString &text)
{

	if (! m_LogDetail)
		return;

	if (m_hLogFile == NULL)
		return;

	unsigned long bytes;

	WriteFile(m_hLogFile, text, text.GetLength(), &bytes, NULL);

	return;
}

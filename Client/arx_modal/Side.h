// Side.h: interface for the CSide class.
//
//////////////////////////////////////////////////////////////////////

#if !defined(AFX_SIDE_H__CD2A42A3_3611_43E5_A22F_8C38814CE8DC__INCLUDED_)
#define AFX_SIDE_H__CD2A42A3_3611_43E5_A22F_8C38814CE8DC__INCLUDED_

#if _MSC_VER > 1000
#pragma once
#endif // _MSC_VER > 1000

#include "FacilityElement.h"
#include "Bay.h"

class CSide : public CFacilityElement  
{
public:
	CSide();
	virtual ~CSide();

	CTypedPtrArray<CObArray, CBay*> m_BayArray;
	CMap<long, long, CBay*, CBay*> m_BayMapById;
	CMap<CString, LPCSTR, CBay*, CBay*> m_BayMapByName;
	CMap<CString, LPCSTR, CBay*, CBay*> m_BayMapByHandle;

};

#endif // !defined(AFX_SIDE_H__CD2A42A3_3611_43E5_A22F_8C38814CE8DC__INCLUDED_)

// HotSpotPropertiesDialog.cpp : implementation file
//

#include "stdafx.h"
#include "modal.h"
#include "HotSpotPropertiesDialog.h"

#ifdef _DEBUG
#define new DEBUG_NEW
#undef THIS_FILE
static char THIS_FILE[] = __FILE__;
#endif

/////////////////////////////////////////////////////////////////////////////
// CHotSpotPropertiesDialog dialog


CHotSpotPropertiesDialog::CHotSpotPropertiesDialog(CWnd* pParent /*=NULL*/)
	: CDialog(CHotSpotPropertiesDialog::IDD, pParent)
{
	//{{AFX_DATA_INIT(CHotSpotPropertiesDialog)
	m_Coordinates = _T("");
	m_section = _T("");
	m_Type = _T("");
	//}}AFX_DATA_INIT
}


void CHotSpotPropertiesDialog::DoDataExchange(CDataExchange* pDX)
{
	CDialog::DoDataExchange(pDX);
	//{{AFX_DATA_MAP(CHotSpotPropertiesDialog)
	DDX_Text(pDX, IDC_HOTSPOTPROPERTIES_COORDINATES, m_Coordinates);
	DDX_Text(pDX, IDC_HOTSPOTPROPERTIES_SECTION, m_section);
	DDX_Text(pDX, IDC_HOTSPOTPROPERTIES_TYPE, m_Type);
	//}}AFX_DATA_MAP
}


BEGIN_MESSAGE_MAP(CHotSpotPropertiesDialog, CDialog)
	//{{AFX_MSG_MAP(CHotSpotPropertiesDialog)
		// NOTE: the ClassWizard will add message map macros here
	//}}AFX_MSG_MAP
END_MESSAGE_MAP()

/////////////////////////////////////////////////////////////////////////////
// CHotSpotPropertiesDialog message handlers

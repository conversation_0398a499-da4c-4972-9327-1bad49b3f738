// InterfaceHelper.h: interface for the CInterfaceHelper class.
//
//////////////////////////////////////////////////////////////////////

#if !defined(AFX_INTERFACEHELPER_H__6FA69FD5_BC2A_44B1_9A04_34F00E078B46__INCLUDED_)
#define AFX_INTERFACEHELPER_H__6FA69FD5_BC2A_44B1_9A04_34F00E078B46__INCLUDED_

#if _MSC_VER > 1000
#pragma once
#endif // _MSC_VER > 1000

class CInterfaceHelper  
{
public:
	static CString ConvertStatusToText(int status);
	CInterfaceHelper();
	virtual ~CInterfaceHelper();

	void ConvertProductFileToXML();

	void ProductInbound();
	void LocationOutbound();
	void AssignmentOutbound();
	void GenerateMoves();
	void SearchAnchorMaintenance();
	void RunNewProductLayout();
	int NewProductLayout(CSsaStringArray &retArray);
	void InterfaceMap();
	void WMSSetup();
	void WMSGroup();
	void WMSSync();

	typedef enum {
		Add,
		Modify,
		Delete,
		ConfirmAdd,
		ConfirmModify,
		ConfirmDelete
	} enumAction;

	typedef enum {
		NotSent,
		Sent,
		Confirmed,
		Rejected
	} enumStatus;

	typedef enum {
		OptimizeSender = 0,
		WMSSender = 1
	} enumSender;

};

#endif // !defined(AFX_INTERFACEHELPER_H__6FA69FD5_BC2A_44B1_9A04_34F00E078B46__INCLUDED_)

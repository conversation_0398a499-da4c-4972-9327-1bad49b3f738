// Assignment.h: interface for the CAssignment class.
//
//////////////////////////////////////////////////////////////////////

#if !defined(AFX_ASSIGNMENT_H__34206E78_1C99_4A1D_8D3A_6463861E9C07__INCLUDED_)
#define AFX_ASSIGNMENT_H__34206E78_1C99_4A1D_8D3A_6463861E9C07__INCLUDED_

#if _MSC_VER > 1000
#pragma once
#endif // _MSC_VER > 1000

class CAssignment : public CObject  
{
public:
	CAssignment();
	CAssignment(const CAssignment &other);
	CAssignment& operator=(const CAssignment &other);
	BOOL operator==(const CAssignment &other);
	BOOL operator!=(const CAssignment &other) { return ! (*this == other); }
	CString Stream();

	virtual ~CAssignment();
	
	int m_ProductKey;
	int m_FromLocationKey;
	int m_ToLocationKey;

	int m_IsFromTemp;
	int m_IsToTemp;
	int m_IsAddFacing;
	int m_IsDeleteFacing;
	int m_CaseQuantity;

	CString m_WMSId;
	CString m_WMSDetailId;
};

#endif // !defined(AFX_ASSIGNMENT_H__34206E78_1C99_4A1D_8D3A_6463861E9C07__INCLUDED_)

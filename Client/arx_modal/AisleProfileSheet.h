#if !defined(AFX_AISLEPROFILESHEET_H__240A8F5F_CEA0_4FE6_8B71_E30D4D6AD6CA__INCLUDED_)
#define AFX_AISLEPROFILESHEET_H__240A8F5F_CEA0_4FE6_8B71_E30D4D6AD6CA__INCLUDED_
#include "AisleProfile.h"
#include "AisleProfileSidePage.h"
#include "AisleProfileDimensionPage.h"

#if _MSC_VER > 1000
#pragma once
#endif // _MSC_VER > 1000
// AisleProfileSheet.h : header file
//

/////////////////////////////////////////////////////////////////////////////
// CAisleProfileSheet

class CAisleProfileSheet : public CPropertySheet
{
	DECLARE_DYNAMIC(CAisleProfileSheet)

// Construction
public:
	CAisleProfileSheet(UINT nIDCaption, CWnd* pParentWnd = NULL, UINT iSelectPage = 0);
	CAisleProfileSheet(LPCTSTR pszCaption, CWnd* pParentWnd = NULL, UINT iSelectPage = 0);

// Attributes
public:

// Operations
public:

// Overrides
	// ClassWizard generated virtual function overrides
	//{{AFX_VIRTUAL(CAisleProfileSheet)
	protected:
	virtual BOOL OnNotify(WPARAM wParam, LPARAM lParam, LRESULT* pResult);
	//}}AFX_VIRTUAL

// Implementation
public:
	CAisleProfileSidePage m_SidePage;
	CAisleProfileDimensionPage m_DimensionPage;
	CAisleProfile *m_pAisleProfile;
	virtual ~CAisleProfileSheet();

	// Generated message map functions
protected:
	//{{AFX_MSG(CAisleProfileSheet)
	afx_msg BOOL OnHelpInfo(HELPINFO* pHelpInfo);
	//}}AFX_MSG
	DECLARE_MESSAGE_MAP()
};

/////////////////////////////////////////////////////////////////////////////

//{{AFX_INSERT_LOCATION}}
// Microsoft Visual C++ will insert additional declarations immediately before the previous line.

#endif // !defined(AFX_AISLEPROFILESHEET_H__240A8F5F_CEA0_4FE6_8B71_E30D4D6AD6CA__INCLUDED_)

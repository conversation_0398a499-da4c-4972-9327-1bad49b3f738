// LevelLocationDialog.cpp : implementation file
//

#include "stdafx.h"
#include "modal.h"
#include "LevelLocationDialog.h"
#include "TreeElement.h"
#include "BayProfileDataService.h"
#include "BTreeHelper.h"
#include "ElementMaintenanceHelper.h"
#include "BayInfo.h"
#include "HelpService.h"

#ifdef _DEBUG
#define new DEBUG_NEW
#undef THIS_FILE
static char THIS_FILE[] = __FILE__;
#endif

/////////////////////////////////////////////////////////////////////////////
// CLevelLocationDialog dialog

extern CBayProfileDataService bayProfileDataService;
extern TreeElement changesTree;
extern CBTreeHelper bTreeHelper;
extern CElementMaintenanceHelper elementMaintenanceHelper;
extern int numItemsProcessed;
extern CHelpService helpService;

CLevelLocationDialog::CLevelLocationDialog(CWnd* pParent /*=NULL*/)
	: CDialog(CLevelLocationDialog::IDD, pParent)
{
	//{{AFX_DATA_INIT(CLevelLocationDialog)
	m_Mask = _T("");
	//}}AFX_DATA_INIT
}


CLevelLocationDialog::~CLevelLocationDialog()
{
	delete m_pBayProfile;
}

void CLevelLocationDialog::DoDataExchange(CDataExchange* pDX)
{
	CDialog::DoDataExchange(pDX);
	//{{AFX_DATA_MAP(CLevelLocationDialog)
	DDX_Control(pDX, IDC_ELEMENT_LIST, m_ElementListCtrl);
	DDX_Control(pDX, IDC_VIEW_ELEMENT, m_ViewElementCtrl);
	DDX_Control(pDX, IDC_VIEW_LOCATION, m_ViewLocationCtrl);
	DDX_Control(pDX, IDC_VIEW_LEVEL, m_ViewLevelCtrl);
	DDX_Control(pDX, IDC_LOCATION_LIST, m_LocationListCtrl);
	DDX_Control(pDX, IDC_LEVEL_LIST, m_LevelListCtrl);
	DDX_Control(pDX, IDC_LEVEL_BUTTON, m_LevelButton);
	DDX_Text(pDX, IDC_MASK, m_Mask);
	//}}AFX_DATA_MAP
}


BEGIN_MESSAGE_MAP(CLevelLocationDialog, CDialog)
	//{{AFX_MSG_MAP(CLevelLocationDialog)
	ON_WM_HELPINFO()
	ON_WM_SIZE()
	ON_WM_MOVE()
	ON_BN_CLICKED(IDC_VIEW_LEVEL, OnViewLevel)
	ON_BN_CLICKED(IDC_VIEW_LOCATION, OnViewLocation)
	ON_BN_CLICKED(IDHELP, OnHelp)
	ON_CBN_SELCHANGE(IDC_LEVEL_LIST, OnSelchangeLevelList)
	ON_CBN_SELCHANGE(IDC_LOCATION_LIST, OnSelchangeLocationList)
	ON_MESSAGE(WM_DBLCLK_LEVEL, (LRESULT (AFX_MSG_CALL CWnd::*)(WPARAM, LPARAM))OnDblClkLevel)
	ON_MESSAGE(WM_SELECT_LEVEL, (LRESULT (AFX_MSG_CALL CWnd::*)(WPARAM, LPARAM))OnSelectLevel)
	ON_BN_CLICKED(IDC_VIEW_ELEMENT, OnViewElement)
	//}}AFX_MSG_MAP
END_MESSAGE_MAP()

/////////////////////////////////////////////////////////////////////////////
// CLevelLocationDialog message handlers

BOOL CLevelLocationDialog::OnInitDialog() 
{
	CDialog::OnInitDialog();

	EnableToolTips(TRUE);

	m_ToolTip.Create(this, TTF_IDISHWND | TTF_TRACK | TTF_ABSOLUTE);
	//m_tooltip.Activate(TRUE);
	
	m_ToolTipText = "T";
	m_ToolTip.AddTool(GetDlgItem(IDC_LEVEL_BUTTON),m_ToolTipText);

	m_ToolTip.SendMessage(TTM_SETDELAYTIME, (WPARAM)(DWORD)TTDT_INITIAL,
		(LPARAM)(INT)MAKELONG(200,0));
	m_ToolTip.SendMessage(TTM_SETDELAYTIME, (WPARAM)(DWORD)TTDT_AUTOPOP,
		(LPARAM)(INT)MAKELONG(10000,0));


	CToolInfo ti;
	m_ToolTip.GetToolInfo(ti, GetDlgItem(IDC_LEVEL_BUTTON), 0);
	m_ToolTip.SendMessage(TTM_TRACKACTIVATE, (WPARAM)TRUE, (LPARAM)&ti);
	m_ToolTip.SendMessage(TTM_SETMAXTIPWIDTH, 0, (LPARAM)(INT)500);

	m_ToolTip.Activate(FALSE);

	CBitmap bitmap;
	bitmap.LoadBitmap(IDB_GO);
	m_ViewElementCtrl.SetBitmap((HBITMAP)bitmap.Detach());

	bitmap.LoadBitmap(IDB_GO);
	m_ViewLocationCtrl.SetBitmap((HBITMAP)bitmap.Detach());

	bitmap.LoadBitmap(IDB_GO);
	m_ViewLevelCtrl.SetBitmap((HBITMAP)bitmap.Detach());


	if (LoadBay() < 0) {
		EndDialog(IDCANCEL);
		return TRUE;
	}


	BuildLevelList();
	BuildLocationList();

	m_LevelButton.Invalidate();

	
	return TRUE;  // return TRUE unless you set the focus to a control
	              // EXCEPTION: OCX Property Pages should return FALSE
}


void CLevelLocationDialog::OnSize(UINT nType, int cx, int cy) 
{
	CDialog::OnSize(nType, cx, cy);
	
	// TODO: Add your message handler code here
	
}

void CLevelLocationDialog::OnMove(int x, int y) 
{
	CDialog::OnMove(x, y);
	
	// TODO: Add your message handler code here
	
}


BOOL CLevelLocationDialog::OnHelpInfo(HELPINFO* pHelpInfo) 
{
	helpService.ShowFieldHelp(IDD, pHelpInfo->iCtrlId);

	return FALSE;

}

void CLevelLocationDialog::OnHelp() 
{
	helpService.ShowScreenHelp(IDD);

	return;

}

int CLevelLocationDialog::LoadBay()
{
	TreeElement *bayPtr;

	CWaitCursor cwc;

	bayPtr = changesTree.getBayByHandle(m_Handle);
	if (bayPtr == NULL) {
		AfxMessageBox("Unable to determine the bay for the selected object.\n");
		return -1;
	}

	qqhSLOTBay bay;
	bTreeHelper.GetBtBay(bayPtr->fileOffset, bay);

	m_pBayProfile = new CBayProfile;
	try {
		bayProfileDataService.GetBayProfile(bay.getBayProfileId(), *m_pBayProfile);
	}
	catch (...) {
		AfxMessageBox("Error getting bay profile from database.");
		return -1;
	}

	if (bay.pBayProfile == NULL) {
		bay.pBayProfile = new CBayProfile(*m_pBayProfile);
	}

	TreeElement *sidePtr, *aislePtr, *sectionPtr;

	sidePtr = bayPtr->treeParent;
	aislePtr = sidePtr->treeParent;
	sectionPtr = aislePtr->treeParent;

	qqhSLOTFacility facility;
	qqhSLOTAisle aisle;
	qqhSLOTSide side;
	qqhSLOTSection section;
	bTreeHelper.GetBtFacility(changesTree.fileOffset, facility);
	bTreeHelper.GetBtAisle(aislePtr->fileOffset, aisle);
	bTreeHelper.GetBtSide(sidePtr->fileOffset, side);
	bTreeHelper.GetBtSection(sectionPtr->fileOffset, section);

	CString temp;
	temp.Format("Facility: %s", facility.getDescription());
	m_ElementListCtrl.AddString(temp);
	temp.Format("Section: %s", section.getDescription());
	m_ElementListCtrl.AddString(temp);
	temp.Format("Aisle: %s", aisle.getDescription());
	m_ElementListCtrl.AddString(temp);
	temp.Format("Side: %s", side.getDescription());
	m_ElementListCtrl.AddString(temp);
	temp.Format("Bay: %s", bay.getDescription());
	m_ElementListCtrl.AddString(temp);
	m_ElementListCtrl.SetCurSel(2);
	CRect r;
	m_ElementListCtrl.GetWindowRect(&r);
	m_ElementListCtrl.SetWindowPos(NULL, 0, 0, r.Width(), 
		r.Height()*(m_ElementListCtrl.GetCount()+1), SWP_NOMOVE|SWP_NOZORDER);

	m_Mask = section.getLocationMask();

	CBayInfo &bayInfo = m_LevelButton.m_BayInfo;

	if (stricmp(aisle.getPickPath().getAcadHandle(), "XXX") != 0) {
		bayInfo.m_PickPathDirection = aisle.getPickPathDirection();
		bayInfo.m_PickPathType = aisle.getPickPathType();
	}
	else {
		bayInfo.m_PickPathDirection = -1;
		bayInfo.m_PickPathType = -1;
	}
	
	bayInfo.m_IsRotated = side.getIsRotated();
	bayInfo.m_pBayTreeElement = bayPtr;
	bayInfo.m_Height = m_pBayProfile->m_Height;
	bayInfo.m_UprightHeight = m_pBayProfile->m_UprightHeight;
	bayInfo.m_Width = m_pBayProfile->m_Width;

	for (int levelIdx=0; levelIdx < bayPtr->treeChildren.GetSize(); ++levelIdx) {
		qqhSLOTLevel level;
		TreeElement *levelPtr = &bayPtr->treeChildren[levelIdx];

		bTreeHelper.GetBtLevel(levelPtr->fileOffset, level);

		CLevelProfile *pLevelProfile = NULL;
		for (int j=0; j < m_pBayProfile->m_LevelProfileList.GetSize(); ++j) {
			if (level.getLevelProfileId() == m_pBayProfile->m_LevelProfileList[j]->m_LevelProfileDBId) {
				pLevelProfile = m_pBayProfile->m_LevelProfileList[j];
				break;
			}
		}

		if (pLevelProfile == NULL) {
			AfxMessageBox("Inconsistency found between level and level profiles.\n"
				"Please contact customer support.");
			return -1;
		}

		CBayLevelInfo *pLevel = new CBayLevelInfo;
		bayInfo.m_LevelList.Add(pLevel);

		pLevel->m_pLevelTreeElement = levelPtr;
		// Display the bay as if it were on the ground so use the profile z coordinate
		// Besides, older versions did not populate the level coordinates correctly
		//pLevel->m_Height = level.getCoord().getZ();
		pLevel->m_Height = pLevelProfile->m_Coordinates.m_Z;
		pLevel->m_IsHidden = pLevelProfile->m_IsBarHidden;
		pLevel->m_IsSelected = FALSE;
		pLevel->m_LevelDBId = level.getDBID();
		pLevel->m_Thickness = pLevelProfile->m_Thickness;
		pLevel->m_Coordinates.m_X = level.getCoord().getX();
		pLevel->m_Coordinates.m_Y = level.getCoord().getY();
		//pLevel->m_Coordinates.m_Z = level.getCoord().getZ();
		pLevel->m_Coordinates.m_Z = pLevelProfile->m_Coordinates.m_Z;
		pLevel->m_Description = level.getDescription();
		pLevel->m_LocationRowCount = pLevelProfile->m_LocationRowCount;

		for (int locIdx=0; locIdx < levelPtr->treeChildren.GetSize(); ++locIdx) {
			qqhSLOTLocation location;
			TreeElement *locPtr = &levelPtr->treeChildren[locIdx];

			bTreeHelper.GetBtLocation(locPtr->fileOffset, location);

			CLocationProfile *pLocationProfile = NULL;
			for (int j=0; j < pLevelProfile->m_LocationProfileList.GetSize(); ++j) {
				if (location.getLocationProfileId() == pLevelProfile->m_LocationProfileList[j]->m_LocationProfileDBId) {
					pLocationProfile = pLevelProfile->m_LocationProfileList[j];
					break;
				}
			}

			if (pLocationProfile == NULL) {
				AfxMessageBox("Inconsistency found between location and location profiles.\n"
					"Please contact customer support.");
				return -1;
			}

			CBayLocationInfo *pLocation = new CBayLocationInfo;
			pLocation->m_pLocationTreeElement = locPtr;
			pLocation->m_Coordinates.m_X = location.getCoord().getX();
			pLocation->m_Coordinates.m_Y = location.getCoord().getY();
			// We want to use the profile z coordinate because the aisle may have been placed off
			// the ground but we are displaying the bay as if it were on the ground
			//pLocation->m_Coordinates.m_Z = location.getCoord().getZ();
			pLocation->m_Coordinates.m_Z = pLocationProfile->m_Coordinates.m_Z;
			pLocation->m_Description = location.getDescription();
			pLocation->m_IsSelected = FALSE;
			pLocation->m_Height = location.getHeight();
			pLocation->m_LocationDBId = location.getDBID();
			pLocation->m_LocationSpace = pLocationProfile->m_LocationSpace;
			pLocation->m_Width = location.getWidth();
			pLocation->m_Clearance = location.getClearance();

			pLevel->m_LocationList.Add(pLocation);

		}
	}

	UpdateData(FALSE);

	return 0;
}


int CLevelLocationDialog::OnDblClkLevel(WPARAM wParam, LPARAM lParam)
{
	int levelIdx = wParam;
	int locationIdx = lParam;

	m_LevelListCtrl.SetCurSel(levelIdx);
	
	BuildLocationList();

	m_LocationListCtrl.SetCurSel(locationIdx);

	if (locationIdx >= 0)
		OnViewLocation();
	else
		OnViewLevel();

	return 0;
}

int CLevelLocationDialog::OnSelectLevel(WPARAM wParam, LPARAM lParam)
{
	int levelIdx = wParam;
	int locationIdx = lParam;

	m_LevelListCtrl.SetCurSel(levelIdx);
	
	BuildLocationList();

	m_LocationListCtrl.SetCurSel(locationIdx);

		
	return 0;
}

void CLevelLocationDialog::OnViewFacility() 
{
	qqhSLOTFacility facility, oldFacility;
	CWaitCursor cwc;

	if (bTreeHelper.GetBtFacility(changesTree.fileOffset, facility) < 0) {
		AfxMessageBox("Unable to read facility.");
		return;
	}

	oldFacility = facility;

	elementMaintenanceHelper.FacilityProperties(facility);

	if (! (facility == oldFacility)) {
		numItemsProcessed++;
		bTreeHelper.SetBtFacility(changesTree.fileOffset, facility);
	}
	
}

void CLevelLocationDialog::OnViewSection() 
{
	TreeElement *bayPtr;
	qqhSLOTSection section, oldSection;
	CWaitCursor cwc;

	bayPtr = changesTree.getBayByHandle(m_Handle);

	TreeElement *sidePtr, *aislePtr, *sectionPtr;

	sidePtr = bayPtr->treeParent;
	aislePtr = sidePtr->treeParent;
	sectionPtr = aislePtr->treeParent;

	if (bTreeHelper.GetBtSection(sectionPtr->fileOffset, section) < 0) {
		AfxMessageBox("Unable to read section.");
		return;
	}

	oldSection = section;

	elementMaintenanceHelper.SectionProperties(section);

	if (! (section == oldSection)) {
		numItemsProcessed++;
		bTreeHelper.SetBtSection(sectionPtr->fileOffset,section);
		
		if (oldSection.getDescription() != section.getDescription())
			elementMaintenanceHelper.UpdateLocationsForSectionChange(sectionPtr->fileOffset, section);
	}
	
}

void CLevelLocationDialog::OnViewAisle() 
{
	TreeElement *bayPtr, *sidePtr, *aislePtr, *sectionPtr;
	qqhSLOTAisle aisle, oldAisle;

	bayPtr = changesTree.getBayByHandle(m_Handle);

	sidePtr = bayPtr->treeParent;
	aislePtr = sidePtr->treeParent;
	if (bTreeHelper.GetBtAisle(aislePtr->fileOffset, aisle) < 0) {
		AfxMessageBox("Unable to read aisle.");
		return;
	}
	
	oldAisle = aisle;
	sectionPtr = aislePtr->treeParent;

	elementMaintenanceHelper.AisleProperties(aisle);

	if (! (aisle == oldAisle)) {
		aisle.setIsChanged("TRUE");
		bTreeHelper.SetBtAisle(aislePtr->fileOffset,aisle);
		numItemsProcessed++;
		
		if (oldAisle.getDescription() != aisle.getDescription()) {
			elementMaintenanceHelper.UpdateLocationsForAisleChange(sectionPtr->fileOffset, aislePtr->fileOffset, aisle);

			//Replace the description in the the dropdown control with the new one
			CString newItemText;
			CString oldItemText;
			oldItemText.Format("Aisle: %s", oldAisle.getDescription());
			newItemText.Format("Aisle: %s", aisle.getDescription());
			int i = m_ElementListCtrl.FindStringExact(0, oldItemText.GetBuffer());
			ASSERT(i != CB_ERR);//An assertion would mean an error in the program logic
			if (i != CB_ERR) {
				m_ElementListCtrl.DeleteString(i);
				m_ElementListCtrl.InsertString(i, newItemText);
				m_ElementListCtrl.SetCurSel(2);
			}
		}
	}
}

void CLevelLocationDialog::OnViewSide()
{
	qqhSLOTSide side, oldSide;
	CWaitCursor cwc;
	TreeElement *bayPtr, *sidePtr;


	bayPtr = changesTree.getBayByHandle(m_Handle);

	sidePtr = bayPtr->treeParent;
	if (bTreeHelper.GetBtSide(sidePtr->fileOffset, side) < 0)
		return;

	oldSide = side;

	elementMaintenanceHelper.SideProperties(side);

	if (! (side == oldSide)) {
		numItemsProcessed++;
		bTreeHelper.SetBtSide(sidePtr->fileOffset, side);
	}
}

void CLevelLocationDialog::OnViewBay() 
{
	TreeElement *bayPtr, *sidePtr, *aislePtr, *sectionPtr;
	qqhSLOTBay oldBay, bay;

	bayPtr = changesTree.getBayByHandle(m_Handle);
	sidePtr = bayPtr->treeParent;
	aislePtr = sidePtr->treeParent;
	sectionPtr = aislePtr->treeParent;

	if (bTreeHelper.GetBtBay(bayPtr->fileOffset, bay) < 0) {
		AfxMessageBox("Unable to read bay.");
		return;
	}
	
	oldBay = bay;
	
	elementMaintenanceHelper.BayProperties(bay);

	if (! (bay == oldBay)) {
		bay.setIsChanged("TRUE");
		numItemsProcessed++;

		bTreeHelper.SetBtBay(bayPtr->fileOffset,bay);
	
		if (oldBay.getDescription() != bay.getDescription())
			elementMaintenanceHelper.UpdateLocationsForBayChange(sectionPtr->fileOffset, bayPtr->fileOffset, bay);
	}
}


void CLevelLocationDialog::OnViewLevel() 
{
	int curSel = m_LevelListCtrl.GetCurSel();
	if (curSel < 0)
		return;

	TreeElement *levelPtr = m_LevelButton.m_BayInfo.m_LevelList[curSel]->m_pLevelTreeElement;

	elementMaintenanceHelper.LevelMaintenance(levelPtr);

/*
	qqhSLOTLevel level;

	bTreeHelper.GetBtLevel(levelPtr->fileOffset, level);


	TreeElement *bayPtr = levelPtr->treeParent;
	TreeElement *sidePtr = bayPtr->treeParent;
	TreeElement *aislePtr = sidePtr->treeParent;
	TreeElement *sectionPtr = aislePtr->treeParent;

	qqhSLOTLevel oldLevel = level;

	elementMaintenanceHelper.LevelProperties(level);

	if (! (level == oldLevel)) {
		numItemsProcessed++;
		
		level.setIsChanged("TRUE");
		bTreeHelper.SetBtLevel(levelPtr->fileOffset, level);
		
		if (oldLevel.getDescription() != level.getDescription()) {
			elementMaintenanceHelper.UpdateLocationsForLevelChange(sectionPtr->fileOffset, levelPtr->fileOffset, level);
		}
	}
*/

}

void CLevelLocationDialog::OnViewLocation() 
{
	TreeElement *locationPtr;

	int curLevel = m_LevelListCtrl.GetCurSel();
	if (curLevel < 0)
		return;

	int curLoc = m_LocationListCtrl.GetCurSel();
	if (curLoc < 0)
		return;

	//Search for the location corresponding to the location text item selection in the control
	locationPtr = 0;
	CString curLocText;
	m_LocationListCtrl.GetLBText(curLoc, curLocText);
	for (int i=0; i<m_LevelButton.m_BayInfo.m_LevelList[curLevel]->m_LocationList.GetSize() && locationPtr==0; i++) {
		CString desc = m_LevelButton.m_BayInfo.m_LevelList[curLevel]->m_LocationList[i]->m_Description;
		if (desc == curLocText) 
			locationPtr = m_LevelButton.m_BayInfo.m_LevelList[curLevel]->m_LocationList[i]->m_pLocationTreeElement;
	}


	elementMaintenanceHelper.LocationMaintenance(locationPtr);


	/*
	qqhSLOTLocation location, tempLocation, oldLocation;
	qqhSLOTLevel level;
	qqhSLOTBay bay;
	qqhSLOTSection section;
	qqhSLOTAisle aisle;

	double totalWidth;
	TreeElement *levelPtr, *bayPtr, *sidePtr, *aislePtr, *sectionPtr, *tempLocPtr;

	int curLevel = m_LevelListCtrl.GetCurSel();
	if (curLevel < 0)
		return;

	int curLoc = m_LocationListCtrl.GetCurSel();
	if (curLoc < 0)
		return;

	locationPtr = m_LevelButton.m_BayInfo.m_LevelList[curLevel]->m_LocationList[curLoc]->m_pLocationTreeElement;

	levelPtr = locationPtr->treeParent;
	if (bTreeHelper.GetBtLevel(levelPtr->fileOffset, level) < 0) {
		AfxMessageBox("Unable to read level.");
		return;
	}
	bayPtr = levelPtr->treeParent;
	if (bTreeHelper.GetBtBay(bayPtr->fileOffset, bay) < 0) {
		AfxMessageBox("Unable to read bay.");
		return;
	}

	sidePtr = bayPtr->treeParent;
	aislePtr = sidePtr->treeParent;
	if (bTreeHelper.GetBtAisle(aislePtr->fileOffset, aisle) < 0) {
		AfxMessageBox("Unable to read aisle.");
		return;
	}
	sectionPtr = aislePtr->treeParent;
	if (bTreeHelper.GetBtSection(sectionPtr->fileOffset, section) < 0) {
		AfxMessageBox("Unable to read section.");
		return;
	}

	if (bTreeHelper.OpenBTree() < 0) {
		AfxMessageBox("Error updating locations.  Unable to open binary tree.");
		return;
	}

	// Need to add some logic here to only do this if descriptions have changed
	numberingService.UpdateLocationDescriptionPart(locationPtr->fileOffset,
		section.getLocationMask(), 1, section.getDescription());
	numberingService.UpdateLocationDescriptionPart(locationPtr->fileOffset,
		section.getLocationMask(), 2, aisle.getDescription());
	numberingService.UpdateLocationDescriptionPart(locationPtr->fileOffset,
		section.getLocationMask(), 3, bay.getDescription());
	numberingService.UpdateLocationDescriptionPart(locationPtr->fileOffset,
		section.getLocationMask(), 4, level.getDescription());
			
	if (bTreeHelper.CloseBTree() < 0) {
		AfxMessageBox("Warning.  Unable to close binary tree.");
		return;
	}

	if (bTreeHelper.GetBtLocation(locationPtr->fileOffset, location) < 0) {
		AfxMessageBox("Unable to read location.");
		return;
	}

	oldLocation = location;


	totalWidth = 0;
	for (i=0; i < levelPtr->treeChildren.GetSize(); ++i) {
		tempLocPtr = &(levelPtr->treeChildren[i]);
		if (bTreeHelper.GetBtLocation(tempLocPtr->fileOffset, tempLocation) >= 0) {
			totalWidth += tempLocation.getWidth();
		}
	}

	elementMaintenanceHelper.LocationProperties(location, level, bay, totalWidth);

	if (! (location == oldLocation)) {
		numItemsProcessed++;
		location.setIsChanged("TRUE");
		bTreeHelper.SetBtLocation(locationPtr->fileOffset, location);
		if (location.getDescription() != oldLocation.getDescription()) {
			m_TreeCtrl.SetItemText(hItem, location.getDescription().GetBuffer(0));
			location.getDescription().ReleaseBuffer();
		}
	}
	*/
}


void CLevelLocationDialog::BuildLevelList()
{
	int curSel = m_LevelListCtrl.GetCurSel();

	m_LevelListCtrl.ResetContent();
	for (int i=0; i < m_pBayProfile->m_LevelProfileList.GetSize(); ++i) {
		m_pBayProfile->m_LevelProfileList[i]->m_Description.Format("%d", i+1);
		m_pBayProfile->m_LevelProfileList[i]->m_RelativeLevel = i+1;
		CString temp;
		if (i == 0)
			temp = "Level 1 - Floor";
		else
			temp.Format("Level: %d - Position: %.0f", i+1, m_pBayProfile->m_LevelProfileList[i]->m_Coordinates.m_Z);
		int nItem = m_LevelListCtrl.AddString(temp);
	}

	if (curSel >= m_LevelListCtrl.GetCount())
		curSel = m_LevelListCtrl.GetCount()-1;

	m_LevelListCtrl.SetCurSel(curSel);

	CRect r;
	m_LevelListCtrl.GetWindowRect(&r);
	m_LevelListCtrl.SetWindowPos(NULL, 0, 0, r.Width(), 
		r.Height()*(m_pBayProfile->m_LevelProfileList.GetSize()+1), SWP_NOMOVE|SWP_NOZORDER);

}

void CLevelLocationDialog::BuildLocationList()
{
	m_LocationListCtrl.ResetContent();

	int curLevel = m_LevelListCtrl.GetCurSel();
	if (curLevel < 0)
		return;

	CBayLevelInfo *pLevel = m_LevelButton.m_BayInfo.m_LevelList[curLevel];

	for (int i=0; i < pLevel->m_LocationList.GetSize(); ++i) {
		CBayLocationInfo *pLocation = pLevel->m_LocationList[i];
		int nItem = m_LocationListCtrl.AddString(pLocation->m_Description);
		m_LocationListCtrl.SetItemData(nItem, pLocation->m_LocationDBId);
	}

	CRect r;
	m_LocationListCtrl.GetWindowRect(&r);
	m_LocationListCtrl.SetWindowPos(NULL, 0, 0, r.Width(), 
		r.Height()*(m_LocationListCtrl.GetCount()+1), SWP_NOMOVE|SWP_NOZORDER);

}

void CLevelLocationDialog::OnSelchangeLevelList() 
{
	BuildLocationList();

	m_LevelButton.SelectLevel(m_LevelListCtrl.GetCurSel());

}

void CLevelLocationDialog::OnSelchangeLocationList() 
{
	m_LevelButton.SelectLocation(m_LevelListCtrl.GetCurSel(), m_LocationListCtrl.GetCurSel());	
}

BOOL CLevelLocationDialog::PreTranslateMessage(MSG* pMsg) 
{
	m_ToolTip.RelayEvent(pMsg);	
	
	return CDialog::PreTranslateMessage(pMsg);
}

void CLevelLocationDialog::UpdateToolTip(int x, int y, const CString &msg)
{
	if (msg == "")
		m_ToolTip.Activate(FALSE);
	else {
		m_ToolTip.Activate(TRUE);
		m_ToolTipText = msg;
		m_ToolTip.UpdateTipText(m_ToolTipText, GetDlgItem(IDC_LEVEL_BUTTON), 0);
	}

	m_ToolTip.SendMessage(TTM_TRACKPOSITION, 0, (LPARAM)MAKELPARAM(x, y));
}

void CLevelLocationDialog::OnViewElement() 
{
	int curSel = m_ElementListCtrl.GetCurSel();
	
	switch (curSel) {
	case 0:
		OnViewFacility();
		break;
	case 1:
		OnViewSection();
		break;
	case 2:
		OnViewAisle();
		break;
	case 3:
		OnViewSide();
		break;
	case 4:
		OnViewBay();
		break;
	}
}

// NavigationHelper.h: interface for the CNavigationHelper class.
//
//////////////////////////////////////////////////////////////////////

#if !defined(AFX_NAVIGATIONHELPER_H__AF1B84D4_B059_45F1_8D7D_E1DE31D81C6C__INCLUDED_)
#define AFX_NAVIGATIONHELPER_H__AF1B84D4_B059_45F1_8D7D_E1DE31D81C6C__INCLUDED_

#if _MSC_VER > 1000
#pragma once
#endif // _MSC_VER > 1000

class CNavigationHelper  
{
public:
	int ShowModelessWizard();
	CNavigationHelper();
	virtual ~CNavigationHelper();
	
	void RunRegisteredCommand(const CString &command);
	void LoadOptimize();
	void ShowWizard();
	void DisplayCursorMenu();
	void SucceedHelp();
	void ProcessLogin();
	void NewConnection();
	void ModifyDrawing();

	void InitApp();
	void UnloadApp();

	///int GetDatabaseList(CSsaStringArray & databaseList);
};

#endif // !defined(AFX_NAVIGATIONHELPER_H__AF1B84D4_B059_45F1_8D7D_E1DE31D81C6C__INCLUDED_)

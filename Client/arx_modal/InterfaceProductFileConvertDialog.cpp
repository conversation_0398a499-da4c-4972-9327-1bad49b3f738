// InterfaceProductFileConvertDialog.cpp : implementation file
//

#include "stdafx.h"
#include "modal.h"
#include "InterfaceProductFileConvertDialog.h"
#include "ControlService.h"
#include "UtilityHelper.h"
#include "UDFDataService.h"
#include "DataAccessService.h"
#include "DisplayResults.h"
#include "Prompt.h"
#include "ProgressMessage.h"
#include "InterfaceHelper.h"
#include "HelpService.h"

#ifdef _DEBUG
#define new DEBUG_NEW
#undef THIS_FILE
static char THIS_FILE[] = __FILE__;
#endif

extern CControlService controlService;
extern CUtilityHelper utilityHelper;
extern CUDFDataService udfDataService;
extern CDataAccessService dataAccessService;
extern CHelpService helpService;

/////////////////////////////////////////////////////////////////////////////
// CInterfaceProductFileConvertDialog dialog


CInterfaceProductFileConvertDialog::CInterfaceProductFileConvertDialog(CWnd* pParent /*=NULL*/)
	: CDialog(CInterfaceProductFileConvertDialog::IDD, pParent)
{
	//{{AFX_DATA_INIT(CInterfaceProductFileConvertDialog)
	m_FileName = _T("");
	m_ValidateUDFs = FALSE;
	m_OldFormat = FALSE;
	m_CreateAssignments = FALSE;
	//}}AFX_DATA_INIT
}


void CInterfaceProductFileConvertDialog::DoDataExchange(CDataExchange* pDX)
{
	CDialog::DoDataExchange(pDX);
	//{{AFX_DATA_MAP(CInterfaceProductFileConvertDialog)
	DDX_Control(pDX, IDC_TYPE_LIST, m_TypeListCtrl);
	DDX_Text(pDX, IDC_FILENAME, m_FileName);
	DDX_Check(pDX, IDC_VALIDATE_UDFS, m_ValidateUDFs);
	DDX_Check(pDX, IDC_OLD_FORMAT, m_OldFormat);
	DDX_Check(pDX, IDC_CREATE_ASSIGNMENTS, m_CreateAssignments);
	//}}AFX_DATA_MAP
}


BEGIN_MESSAGE_MAP(CInterfaceProductFileConvertDialog, CDialog)
	//{{AFX_MSG_MAP(CInterfaceProductFileConvertDialog)
	ON_BN_CLICKED(IDC_BROWSE, OnBrowse)
	ON_BN_CLICKED(IDC_CONVERT, OnConvert)
	ON_WM_HELPINFO()
	ON_BN_CLICKED(IDHELP, OnHelp)
	ON_CBN_SELCHANGE(IDC_TYPE_LIST, OnSelchangeTypeList)
	//}}AFX_MSG_MAP
END_MESSAGE_MAP()

/////////////////////////////////////////////////////////////////////////////
// CInterfaceProductFileConvertDialog message handlers

BOOL CInterfaceProductFileConvertDialog::OnInitDialog() 
{
	CDialog::OnInitDialog();
	
	CRect r;
	m_TypeListCtrl.GetWindowRect(&r);
	m_TypeListCtrl.SetWindowPos(NULL, 0, 0, r.Width(), r.Height()*(m_TypeListCtrl.GetCount()+1),
		SWP_NOZORDER|SWP_NOMOVE);

	return TRUE;  // return TRUE unless you set the focus to a control
	              // EXCEPTION: OCX Property Pages should return FALSE
}


void CInterfaceProductFileConvertDialog::OnBrowse() 
{
	m_FileName = GetFile(TRUE, TRUE);

	UpdateData(FALSE);

}

void CInterfaceProductFileConvertDialog::OnConvert() 
{
	CStringArray lines;
	CStringArray headers, fields;
	CString prodxml, assgxml;
	CString temp;
	CString key, wmsProductId, wmsProductDetailId;
	int feedId, batchId;

	UpdateData(TRUE);

	int curSel = m_TypeListCtrl.GetCurSel();
	if (curSel < 0) {
		AfxMessageBox("Select a file type from the drop-down list.");
		m_TypeListCtrl.ShowDropDown(TRUE);
		return;
	}

	m_Map.RemoveAll();
	m_DetailList.RemoveAll();

	CWaitCursor cwc;

	if (LoadFile(lines) < 0)
		return;

	if (lines.GetSize() < 2) {
		 controlService.Log("The file must have at least one header line and one data line.", 
			 "No data in %s.\n", m_FileName);
		 return;
	}

	CString header = lines[0];
	CString delimiter;

	if (header.Find("|") >= 0)
		delimiter = "|";
	else if (header.Find(",") >= 0)
		delimiter = ",";
	else {
		controlService.Log("Unable to determine the field delimiter.\nVerify that the file is comma or pipe-delimited.",
			"Unable to find delimiter in: %s\n", header);
		return;
	}

	utilityHelper.ParseString(header, delimiter, headers);

	CString type;
	m_TypeListCtrl.GetWindowText(type);

	if (type == "Product")
		ConvertHeader(headers);
	else {
		for (int i=0; i < headers.GetSize(); ++i)
			m_Map.SetAt(headers[i], headers[i]);
	}

	if (type != "Other") {
		
		try {
			feedId = dataAccessService.GetNextKey("DBInterfaceFeed", 1 + m_CreateAssignments ? 1 : 0);
			batchId = dataAccessService.GetNextKey("DBInterfaceBatch", 1 + m_CreateAssignments ? 1 : 0);
		}
		catch (...) {
			controlService.Log("Error getting next batch number.", "Generic exception in GetNextKey.\n");
			return;
		}
		
	}

	CString productFile = GetFile(FALSE, TRUE);
	if (productFile == "")
		return;
	
	HANDLE hProductFile = CreateFile(
		productFile,
		GENERIC_WRITE,
		FILE_SHARE_WRITE,
		NULL,
		CREATE_ALWAYS,
		NULL,
		NULL);
	
	if (hProductFile == NULL) {
		CString temp;
		temp.Format("Error in CreateFile(open) for %s: %s\n",
			productFile, LastError());
		controlService.Log("Error opening file.", temp);
		return;
	}
	
	if (type != "Other") {
		temp.Format("<Feed>\n<ID>%d</ID>\n"
			"<Source>WMS - Converted</Source>\n"
			"<Date>%s</Date>\n"
			"<Batch>\n<ID>%d</ID>\n",
			feedId, utilityHelper.GetUTCDate(), batchId);
	}

	if (SaveFile(hProductFile, productFile, temp) < 0) {
		CloseHandle(hProductFile);
		return;
	}

	HANDLE hAssgFile;
	CString assgFile;

	if (m_CreateAssignments) {
		assgFile = GetFile(FALSE, FALSE);
		if (assgFile == "")
			return;

		hAssgFile = CreateFile(
			assgFile,
			GENERIC_WRITE,
			FILE_SHARE_WRITE,
			NULL,
			CREATE_ALWAYS,
			NULL,
			NULL);
		
		if (hAssgFile == NULL) {
			CString temp;
			temp.Format("Error in CreateFile(open) for %s: %s\n",
				assgFile, LastError());
			controlService.Log("Error opening file.", temp);
			return;
		}

		temp.Format("<Feed>\n<ID>%d</ID>\n"
			"<Source>WMS - Converted</Source>\n"
			"<Date>%s</Date>\n"
			"<Batch>\n<ID>%d</ID>\n", ++feedId, utilityHelper.GetUTCDate(), ++batchId);

		if (SaveFile(hAssgFile, assgFile, temp) < 0) {
			CloseHandle(hAssgFile);
			CloseHandle(hProductFile);
			return;
		}
	}

	int assgLineNumber = 0;
	CString dcId("");
	CString dummy;

	if (type != "Other" && ! m_Map.Lookup("DCID", dummy)) {
		CPrompt dlg;
		dlg.m_ParameterName = "Please enter a WMS Facility Id (DC Id) for this file:";
		dlg.m_Title = "Specify WMS Facility Id";
		if (dlg.DoModal() == IDCANCEL)
			return;
		dcId = dlg.m_ParameterValue;
	}
	
	temp.Format("Processing line %d of %d...", 1, lines.GetSize());

	CProgressMessage progressDlg(temp, 1, lines.GetSize(), 1, this);

	BOOL fieldsLessThanHeaders = FALSE;
	BOOL fieldsGreaterThanHeaders = FALSE;

	for (int i=1; i < lines.GetSize(); ++i) {
		prodxml = "";
		assgxml = "";

		temp.Format("Processing line %d of %d...", i, lines.GetSize());
		progressDlg.UpdateMessage(temp);

		CStringArray assgxmlList;
		key = "";
		wmsProductId = "";
		wmsProductDetailId = "";
		
		fields.RemoveAll();
		utilityHelper.ParseString(lines[i], delimiter, fields);
		
		if (type == "Product")
			prodxml += "<Product>\n";
		else if (type == "Location")
			prodxml += "<Location>\n";
		else if (type == "Assignment")
			prodxml += "<Assignment>\n";
		else
			prodxml += "<Record>\n";

		if (type != "Other") {	
			temp.Format("<LineNumber>%d</LineNumber>\n<Action>Add</Action>\n", i);
			prodxml += temp;
			
			if (dcId != "") {
				temp.Format("<DCID>%s</DCID>\n", dcId);
				prodxml += temp;
			}
		}


		if (fields.GetSize() < headers.GetSize())
			fieldsLessThanHeaders = TRUE;

		if (fields.GetSize() > headers.GetSize())
			fieldsGreaterThanHeaders = TRUE;

		for (int j=0; j < fields.GetSize(); ++j) {
			
			if (j > headers.GetSize()-1)
				break;

			if (type == "Product" && headers[j] == "Location") {
				if (m_CreateAssignments && fields[j] != "") {
					temp.Format("<ToLocationKey><Lookup>%s</Lookup></ToLocationKey>\n"
						"<IsAddFacing>No</IsAddFacing>\n", fields[j]);
					assgxmlList.Add(temp);
				}
				else
					continue;
			}
			else if (type == "Product" && headers[j] == "Facing") {
				if (m_CreateAssignments && fields[j] != "") {
					temp.Format("<ToLocationKey><Lookup>%s</Lookup></ToLocationKey>\n"
						"<IsAddFacing>Yes</IsAddFacing>\n", fields[j]);
					assgxmlList.Add(temp);
				}
				else
					continue;
			}
			else if (headers[j] != "") {
				
				if (type == "Product" && headers[j] == "FullPalletHeight") {
					if (fields[j] == "Yes") {
						CString value;
						if (m_Map.Lookup("PalletWoodHeight", value))
							fields[j] = value;
					}
				}
			}
			else
				continue;

			
			if (type == "Product")
				temp.Format("<%s>%s</%s>\n", headers[j], ConvertValue(headers[j], fields[j]), headers[j]);
			else
				temp.Format("<%s>%s</%s>\n", headers[j], fields[j], headers[j]);

			prodxml += temp;
			
			if (type == "Product") {
				if (headers[j] == "Key")
					key = fields[j];
				else if (headers[j] == "Name")
					wmsProductId = fields[j];
				else if (headers[j] == "Detail")
					wmsProductDetailId = fields[j];
			}

			if (dcId == "" && headers[j] == "DCID")
				dcId = fields[j];

		}

		if (type != "Other" && key == "")
			prodxml += "<Key>Generate</Key>\n";

		if (type == "Product")
			prodxml += "</Product>\n";
		else if (type == "Location")
			prodxml += "</Location>\n";
		else if (type == "Assignment")
			prodxml += "</Assignment>\n";
		else
			prodxml += "</Record>\n";

		
		if (m_CreateAssignments) {
			for (int j=0; j < assgxmlList.GetSize(); ++j) {
				temp.Format("<Assignment>\n"
					"<LineNumber>%d</LineNumber>\n<Action>Add</Action>\n"
					"<DCID>%s</DCID>\n", ++assgLineNumber, CInterfaceHelper::Add, dcId);
				assgxml += temp;

				if (key != "") {
					temp.Format("<ProductKey>%s</ProductKey>\n", key);
					assgxml += temp;
				} 
				else if (wmsProductId != "" && wmsProductDetailId != "") {
					temp.Format("<ProductKey>\n<Lookup>\n"
						"<ProductId>%s</ProductId>\n"
						"<DetailId>%s</DetailId>\n"
						"</Lookup>\n</ProductKey>\n", wmsProductId, wmsProductDetailId);
					assgxml += temp;
				}

				assgxml += assgxmlList[j];
				assgxml += "<IsFromTemp>No</IsFromTemp>\n<IsToTemp>No</IsToTemp>\n<IsDeleteFacing>No</IsDeleteFacing>\n";
				assgxml += "</Assignment>\n";
			}
		}
		
		if (SaveFile(hProductFile, productFile, prodxml) < 0) {
			CloseHandle(hProductFile);
			if (assgFile != "")
				CloseHandle(hAssgFile);
			return;
		}

		if (assgxml != "") {
			if (SaveFile(hAssgFile, assgFile, assgxml) < 0) {
				CloseHandle(hProductFile);
				CloseHandle(hAssgFile);
				return;
			}
		}

		progressDlg.Step();
		if (progressDlg.IsStopping()) {
			AfxMessageBox("Conversion Cancelled");
			return;
		}
	}

	progressDlg.Hide();

	if (type != "Other") {
	prodxml = "</Batch>\n</Feed>\n";
	assgxml = "</Batch>\n</Feed>\n";
	}

	if (SaveFile(hProductFile, productFile, prodxml) < 0) {
		CloseHandle(hProductFile);
		if (assgFile != "")
			CloseHandle(hAssgFile);
		return;
	}

	if (assgFile != "") {
		if (SaveFile(hAssgFile, assgFile, assgxml) < 0) {
			CloseHandle(hProductFile);
			CloseHandle(hAssgFile);
			return;
		}
	}

	CloseHandle(hProductFile);
	if (assgFile != "")
		CloseHandle(hAssgFile);
	
	if (fieldsGreaterThanHeaders || fieldsLessThanHeaders) {
		AfxMessageBox("The number of header columns did not always match the number of data columns.\n"
			"This may cause problems if you try to inbound the file.");
	}

	if (type == "Product") {
		CDisplayResults resultsDlg;
		resultsDlg.m_Headers.Add("Old Field|New Field|Field Type|");
		resultsDlg.m_Tabs.Add("Converted Fields");
		resultsDlg.m_WindowCaptions.Add("Product File Conversion");
		resultsDlg.m_HelpTopics.Add("ProductFileConversion_Main");
		resultsDlg.m_ListHelpTopics.Add("ProductFileConversion_Main");
		resultsDlg.m_IsModeless = FALSE;
		resultsDlg.m_MainHelpTopic = "ProductFileConversion_Main";
		resultsDlg.m_Data.Copy(m_DetailList);
		
		resultsDlg.DoModal();
	}
	

}


CString CInterfaceProductFileConvertDialog::ConvertValue(const CString &field, const CString &oldValue)
{
	CString newValue(oldValue);

	if (field == "UnitOfIssue") {
		if (utilityHelper.IsNumeric(oldValue)) {
			switch (atoi(oldValue)) {
			case UOI_EACH:
				newValue = "Each";
				break;
			case UOI_INNER:
				newValue = "Inner";
				break;
			case UOI_CASE:
				newValue = "Case";
				break;
			case UOI_PALLET:
				newValue = "Pallet";
				break;
			}
			
		}
	}
	// Handle other fields that used to be numeric or abbreviated
	// Rotate*, ContainerOverride*, IsHaz, IsPTB, OptMeth, LockAssignment
	else if (field == "AllowWidthHeightSwap" || field == "AllowWidthLengthSwap" ||
		field == "AllowHeightLengthSwap" || field == "IsHazardous" || field == "IsConveyable" ||
		field == "OptimizationMethod" || field == "LockAssignment") {
		if (oldValue == "Y" || oldValue == "1" || oldValue == "Yes" || oldValue == "YES" ||
			oldValue == "T" || oldValue == "True" || oldValue == "TRUE")
			newValue = "Yes";
		else
			newValue == "No";
	}

	return newValue;
}



int CInterfaceProductFileConvertDialog::ConvertHeader(CStringArray &headers)
{
	CMapStringToString map;
	CMap<CString, LPCTSTR, CUDF, CUDF&> udfMap;

	if (m_ValidateUDFs) {
		CStringArray udfList;
		try {
			udfDataService.GetUDFList(UDF_PRODUCT, controlService.GetCurrentFacilityDBId(), udfList);
		}
		catch (...) {
			controlService.Log("Error getting user defined fields for current facility.",
				"Generic exception in GetUDFList.\n");
		}

		for (int i=0; i < udfList.GetSize(); ++i) {
			CUDF udf;
			udf.Parse(udfList[i]);
			udfMap.SetAt(udf.m_Name, udf);
		}
	}

	if (m_OldFormat)
		LoadOldFormatMap();
	else
		LoadNewFormatMap();


	for (int i=0; i < headers.GetSize(); ++i) {
		CUDF udf;
		CString header = headers[i];
		CString newHeader;
		CString temp;
		CString upper(header);
		upper.MakeUpper();

		if (m_Map.Lookup(upper, newHeader)) {
			if (newHeader != "")
				temp.Format("%s|%s|Static|", header, newHeader);
			else
				temp.Format("%s|%s|Obsolete|", header, newHeader);

			headers[i] = newHeader;
		}
		else if (udfMap.Lookup(header, udf)) {
			temp.Format("%s|%s|UDF|", header, udf.m_Name);
			headers[i] = udf.m_Name;
		}
		else {
			if (header != "") {
				temp.Format("%s||Not Found|", header);
				if (m_ValidateUDFs)
					headers[i] = "";
			}
		}
		
		m_DetailList.Add(temp);
			
	}

	return 0;

}

int CInterfaceProductFileConvertDialog::LoadFile(CStringArray &lines)
{

	int size;
	char *msg;

	HANDLE hFile = CreateFile(
		m_FileName,
		GENERIC_READ,
		FILE_SHARE_READ,
		NULL,
		OPEN_EXISTING,
		FILE_FLAG_SEQUENTIAL_SCAN,
		NULL);

	if (hFile ==  INVALID_HANDLE_VALUE) {
		CString temp;
		temp.Format("Error in CreateFile(open) for %s: %s\n",
			m_FileName, LastError());
		controlService.Log("Error opening file.", temp);
		return -1;
	}
	
	size = GetFileSize(hFile, NULL);
	if (size <= 0) {
		controlService.Log("Error processing file. Invalid size.", "File %s has invalid size.\n", m_FileName);
		CloseHandle(hFile);
		return -1;
	}

	msg = (char *)malloc((size+1)*sizeof(char));
	if (msg == NULL) {
		CString temp, temp2;
		temp.Format("There is not enough memory to open the message files %s.", m_FileName);
		temp2.Format("Error allocating %d bytes for file %s.\n", size, m_FileName);
		controlService.Log(temp, temp2);
		CloseHandle(hFile);
		return -1;
	}
	memset(msg, 0, size+1);

	unsigned long bytesRead;
	ReadFile(hFile, msg, size, &bytesRead, NULL);
	if ((int)bytesRead != size) {
		CString temp, temp2;
		temp.Format("Error reading file %s.", m_FileName);
		temp2.Format("%s size %d, read %d\n", m_FileName, size, bytesRead);
		controlService.Log(temp, temp2);
		CloseHandle(hFile);
		free(msg);
		size = 0;
		return -1;
	}

	
	utilityHelper.ParseString(msg, "\n", lines);
	
	for (int i=0; i < lines.GetSize(); ++i)
		lines[i].Replace("\r", "");

	free(msg);

	CloseHandle(hFile);

	return 0;
}


CString CInterfaceProductFileConvertDialog::LastError()
{
	LPVOID lpMsgBuf;
	FormatMessage( 
		FORMAT_MESSAGE_ALLOCATE_BUFFER | 
		FORMAT_MESSAGE_FROM_SYSTEM | 
		FORMAT_MESSAGE_IGNORE_INSERTS,
		NULL,
		GetLastError(),
		MAKELANGID(LANG_NEUTRAL, SUBLANG_DEFAULT), // Default language
		(LPTSTR) &lpMsgBuf,
		0,
		NULL 
		);
	
	CString msg((char *)lpMsgBuf);

	LocalFree( lpMsgBuf );

	return msg;
	
}


void CInterfaceProductFileConvertDialog::LoadOldFormatMap()
{
	m_Map.RemoveAll();

	m_Map[Upper("ProductID")] = "Name";
	m_Map[Upper("ProductDescription")] = "Description";
	m_Map[Upper("ProductDetailID")] = "Detail";
	m_Map[Upper("UnitOfIssue")] = "UnitOfIssue";
	m_Map[Upper("CaseLength")] = "StorageCaseLength";
	m_Map[Upper("CaseHeight")] = "StorageCaseHeight";
	m_Map[Upper("CaseWidth")] = "StorageCaseWidth";
	m_Map[Upper("CasePack")] = "UnitsPerStorageCase";
	m_Map[Upper("InnerPack")] = "UnitsPerInnerPack";
	m_Map[Upper("InnerLength")] = "InnerPackLength";
	m_Map[Upper("InnerHeight")] = "InnerPackHeight";
	m_Map[Upper("InnerWidth")] = "InnerPackWidth";
	m_Map[Upper("EachLength")] = "EachesLength";
	m_Map[Upper("EachHeight")] = "EachesHeight";
	m_Map[Upper("EachWidth")] = "EachesWidth";
	m_Map[Upper("WeeklyMovement")] = "AverageWeeklyMovement";
	m_Map[Upper("NumberOfHits")] = "AverageWeeklyHits";
	m_Map[Upper("BalanceOnHand")] = "AverageBalanceOnHand";
	m_Map[Upper("ContainerLength")] = "PalletWoodLength";
	m_Map[Upper("ContainerHeight")] = "PalletWoodHeight";
	m_Map[Upper("ContainerWidth")] = "PalletWoodWidth";
	m_Map[Upper("IsHeightOverride")] = "FullPalletHeight";
	m_Map[Upper("IsLengthOverride")] = "";
	m_Map[Upper("IsWidthOverride")] = "";
	m_Map[Upper("StorageTI")] = "CasesPerTier";
	m_Map[Upper("StorageHI")] = "TiersPerPallet";
	m_Map[Upper("NumberInPallet")] = "NumberInPallet";
	m_Map[Upper("OptimizeBy")] = "OptimizationMethod";
	m_Map[Upper("Weight")] = "StorageCaseWeight";
	m_Map[Upper("IsAssignmentLocked")] = "LockAssignment";
	m_Map[Upper("IsPickToBelt")] = "IsConveyable";
	m_Map[Upper("IsHazardFlag")] = "IsHazardous";
	if (m_CreateAssignments) {
		m_Map[Upper("CurrentSelectionLocation")] = "Location";
		m_Map[Upper("Location")] = "Facing";
	}
	else {
		m_Map[Upper("CurrentSelectionLocation")] = "";
		m_Map[Upper("Location")] = "";
	}
	m_Map[Upper("AllowSwapHeightLength")] = "AllowHeightLengthSwap";
	m_Map[Upper("AllowSwapHeightWidth")] = "AllowWidthHeightSwap";
	m_Map[Upper("AllowSwapWidthLength")] = "AllowWidthLengthSwap";
	m_Map[Upper("PackSize")] = "";
	m_Map[Upper("CaseQuantity")] = "";
}

void CInterfaceProductFileConvertDialog::LoadNewFormatMap()
{
	m_Map.RemoveAll();

	m_Map[Upper("Name")] = "Name";
	m_Map[Upper("Description")] = "Description";
	m_Map[Upper("Detail")] = "Detail";
	m_Map[Upper("UnitOfIssue")] = "UnitOfIssue";

	m_Map[Upper("StorageCaseLength")] = "StorageCaseLength";
	m_Map[Upper("StorageCaseHeight")] = "StorageCaseHeight";
	m_Map[Upper("StorageCaseWidth")] = "StorageCaseWidth";
	m_Map[Upper("NumberOfHits")] = "NumberOfHits";
	m_Map[Upper("UnitsPerStorageCase")] = "UnitsPerStorageCase";
	m_Map[Upper("UnitsPerShipCase")] = "UnitsPerShipCase";
	m_Map[Upper("UnitsPerInnerPack")] = "UnitsPerInnerPack";
	m_Map[Upper("InnerPackLength")] = "InnerPackLength";
	m_Map[Upper("InnerPackHeight")] = "InnerPackHeight";
	m_Map[Upper("InnerPackWidth")] = "InnerPackWidth";
	m_Map[Upper("EachesLength")] = "EachesLength";
	m_Map[Upper("EachesHeight")] = "EachesHeight";
	m_Map[Upper("EachesWidth")] = "EachesWidth";
	m_Map[Upper("AverageWeeklyMovement")] = "AverageWeeklyMovement";
	m_Map[Upper("AverageWeeklyHits")] = "AverageWeeklyHits";
	m_Map[Upper("AverageBalanceOnHand")] = "AverageBalanceOnHand";
	m_Map[Upper("PalletWoodLength")] = "PalletWoodLength";
	m_Map[Upper("PalletWoodHeight")] = "PalletWoodHeight";
	m_Map[Upper("PalletWoodWidth")] = "PalletWoodWidth";
	m_Map[Upper("FullPalletHeight")] = "FullPalletHeight";
	m_Map[Upper("CasesPerTier")] = "CasesPerTier";
	m_Map[Upper("TiersPerPallet")] = "TiersPerPallet";
	m_Map[Upper("NumberInPallet")] = "NumberInPallet";
	m_Map[Upper("OptimizationMethod")] = "OptimizationMethod";
	m_Map[Upper("StorageCaseWeight")] = "StorageCaseWeight";
	m_Map[Upper("LockAssignment")] = "LockAssignment";
	m_Map[Upper("IsConveyable")] = "IsConveyable";
	m_Map[Upper("IsHazardous")] = "IsHazardous";
	m_Map[Upper("AllowHeightLengthSwap")] = "AllowHeightLengthSwap";
	m_Map[Upper("AllowWidthHeightSwap")] = "AllowWidthHeightSwap";
	m_Map[Upper("AllowWidthLengthSwap")] = "AllowWidthLengthSwap";
	m_Map[Upper("Key")] = "Key";
	m_Map[Upper("LineNumber")] = "LineNumber";
	m_Map[Upper("DCID")] = "DCID";
	m_Map[Upper("WarehouseID")] = "WarehouseID";
	m_Map[Upper("IsActive")] = "IsActive";
	m_Map[Upper("CrushFactor")] = "CrushFactor";
	m_Map[Upper("ProductGroup")] = "ProductGroup";
	m_Map[Upper("UnitsPerShipCase")] = "UnitsPerShipCase";
	m_Map[Upper("NestingWidthIncrement")] = "NestingWidthIncrement";
	m_Map[Upper("NestingLengthIncrement")] = "NestingLengthIncrement";
	m_Map[Upper("NestingHeightIncrement")] = "NestingHeightIncrement";
	m_Map[Upper("ErgnomicPalletPosition")] = "ErgnomicPalletPosition";
	m_Map[Upper("PackDescription")] = "PackDescription";

}

CString CInterfaceProductFileConvertDialog::GetFile(BOOL inbound, BOOL product)
{
	CFileDialog dlgFile(inbound);
	CString strFilter;
	CString strDefault;
	CString fileName;

	if (inbound) {
		if (m_FileName != "")
			fileName = m_FileName;
		else
			fileName.Format("%s\\Interface\\prdasg.dat", controlService.m_ClientHome);
	}
	else {
		if (product)
			fileName = "product.xml";
		else
			fileName = "assignment.xml";
	}

	CString allFilter;
	VERIFY(allFilter.LoadString(AFX_IDS_ALLFILTER));
	if (inbound)
		strFilter += CString("Interface Files (*.csv;*.dat)");
	else
		strFilter += CString("Interface Files (*.xml;*.dat)");

	strFilter += (TCHAR)'\0';
	if (inbound)
		strFilter += _T("*.csv;*.dat");
	else
		strFilter += _T("*.xml;*.dat");
	strFilter += (TCHAR)'\0';
	strFilter += allFilter;
	strFilter += (TCHAR)'\0';   
	strFilter += _T("*.*");
	strFilter += (TCHAR)'\0';   

	dlgFile.m_ofn.lpstrFilter = strFilter;
	dlgFile.m_ofn.lpstrDefExt = "dat";
	if (inbound)
		dlgFile.m_ofn.lpstrTitle = "Select File to Convert";
	else {
		if (product)
			dlgFile.m_ofn.lpstrTitle = "Save Product File As";
		else
			dlgFile.m_ofn.lpstrTitle = "Save Assignment File As";
	}

	dlgFile.m_ofn.lpstrFile = fileName.GetBuffer(_MAX_PATH);
	if (inbound)
		dlgFile.m_ofn.Flags |= OFN_FILEMUSTEXIST;

	BOOL bResult = dlgFile.DoModal() == IDOK ? TRUE : FALSE;

	fileName.ReleaseBuffer();

	if (! bResult)
		return "";
	
	fileName = dlgFile.GetPathName();

	UpdateData(FALSE);

	return fileName;
}

int CInterfaceProductFileConvertDialog::SaveFile(HANDLE &hFile, const CString &fileName, const CString &data)
{


	unsigned long bytesWritten;
	WriteFile(hFile, data, data.GetLength(), &bytesWritten, NULL);
	if ((int)bytesWritten != data.GetLength()) {
		CString temp, temp2;
		temp.Format("Error writing file %s.", fileName);
		temp2.Format("%s size %d, read %d\n", fileName, data.GetLength(), bytesWritten);
		controlService.Log(temp, temp2);
		return -1;
	}

	return 0;
}

CString CInterfaceProductFileConvertDialog::Upper(const CString &str)
{
	CString upper(str);

	upper.MakeUpper();

	return upper;
}



BOOL CInterfaceProductFileConvertDialog::OnHelpInfo(HELPINFO* pHelpInfo) 
{
	helpService.ShowFieldHelp(IDD, pHelpInfo->iCtrlId);

	return FALSE;

}

void CInterfaceProductFileConvertDialog::OnHelp() 
{
	helpService.ShowScreenHelp(IDD);

	return;

}

void CInterfaceProductFileConvertDialog::OnSelchangeTypeList() 
{
	CString text;
	m_TypeListCtrl.GetWindowText(text);

	BOOL bEnable = FALSE;
	if (text == "Product")
		bEnable = TRUE;

	GetDlgItem(IDC_OLD_FORMAT)->EnableWindow(bEnable);
	GetDlgItem(IDC_VALIDATE_UDFS)->EnableWindow(bEnable);
	GetDlgItem(IDC_CREATE_ASSIGNMENTS)->EnableWindow(bEnable);

}

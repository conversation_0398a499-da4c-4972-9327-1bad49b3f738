// ProductGroupCriteriaListPage.cpp : implementation file
//

#include "stdafx.h"
#include "ssa_exception.h"
#include "Constants.h"

#include "ProductGroupCriteriaListPage.h"
#include "ProductGroupCriteriaPropertiesPage.h"
#include "OperatorService.h"
#include "HelpService.h"
#include "UtilityHelper.h"

#ifdef _DEBUG
#define new DEBUG_NEW
#undef THIS_FILE
static char THIS_FILE[] = __FILE__;
#endif

extern CHelpService helpService;
extern CUtilityHelper utilityHelper;

/////////////////////////////////////////////////////////////////////////////
// CProductGroupCriteriaListPage property page

IMPLEMENT_DYNCREATE(CProductGroupCriteriaListPage, CPropertyPage)

CProductGroupCriteriaListPage::CProductGroupCriteriaListPage() : CPropertyPage(CProductGroupCriteriaListPage::IDD)
{
	//{{AFX_DATA_INIT(CProductGroupCriteriaListPage)
	m_FromValue = _T("");
	m_ToValue = _T("");
	//}}AFX_DATA_INIT
	m_AddingValue = FALSE;
	m_ClearingQuery = FALSE;
}

CProductGroupCriteriaListPage::~CProductGroupCriteriaListPage()
{
}

void CProductGroupCriteriaListPage::DoDataExchange(CDataExchange* pDX)
{
	CPropertyPage::DoDataExchange(pDX);
	//{{AFX_DATA_MAP(CProductGroupCriteriaListPage)
	DDX_Control(pDX, IDC_LOAD_POSSIBLE_VALUES, m_LoadPossibleCtrl);
	DDX_Control(pDX, IDC_VALUE_LIST, m_ValueListCtrl);
	DDX_Control(pDX, IDC_FROM_STATIC, m_FromStaticCtrl);
	DDX_Control(pDX, IDC_TO_STATIC, m_ToStaticCtrl);
	DDX_Control(pDX, IDC_TO_VALUE, m_ToValueCtrl);
	DDX_Control(pDX, IDC_FROM_VALUE, m_FromValueCtrl);
	DDX_Control(pDX, IDC_OPERATOR, m_OperatorCtrl);
	DDX_Control(pDX, IDC_RANGE_LIST, m_RangeListCtrl);
	DDX_Text(pDX, IDC_FROM_VALUE, m_FromValue);
	DDX_Text(pDX, IDC_TO_VALUE, m_ToValue);
	//}}AFX_DATA_MAP
}


BEGIN_MESSAGE_MAP(CProductGroupCriteriaListPage, CPropertyPage)
	//{{AFX_MSG_MAP(CProductGroupCriteriaListPage)
	ON_CBN_SELCHANGE(IDC_OPERATOR, OnSelchangeOperator)
	ON_BN_CLICKED(IDC_ADD_ELEMENT, OnAddRange)
	ON_BN_CLICKED(IDC_ADD_VALUE, OnAddValue)
	ON_BN_CLICKED(IDC_DELETE_ELEMENT, OnDeleteRange)
	ON_BN_CLICKED(IDC_DELETE_VALUE, OnDeleteValue)
	ON_BN_CLICKED(IDC_LOAD_VALUES, OnLoadValues)
	ON_NOTIFY(LVN_ITEMCHANGED, IDC_RANGE_LIST, OnItemchangedRangeList)
	ON_NOTIFY(LVN_ENDLABELEDIT, IDC_RANGE_LIST, OnEndlabeleditRangeList)
	ON_NOTIFY(LVN_ENDLABELEDIT, IDC_VALUE_LIST, OnEndlabeleditValueList)
	ON_EN_CHANGE(IDC_FROM_VALUE, OnChangeFromValue)
	ON_EN_CHANGE(IDC_TO_VALUE, OnChangeToValue)
	ON_NOTIFY(LVN_ITEMCHANGED, IDC_VALUE_LIST, OnItemchangedValueList)
	ON_WM_CONTEXTMENU()
	ON_BN_CLICKED(IDC_LOAD_POSSIBLE_VALUES, OnLoadPossibleValues)
	ON_NOTIFY(NM_DBLCLK, IDC_VALUE_LIST, OnDblclkValueList)
	ON_COMMAND(ID_RANGEMENU_ADD, OnAddRange)
	ON_COMMAND(ID_RANGEMENU_DELETE, OnDeleteRange)
	ON_COMMAND(ID_VALUEMENU_DELETE, OnDeleteValue)
	ON_COMMAND(ID_VALUEMENU_ADD, OnAddValue)
	ON_COMMAND(ID_VALUEMENU_LOADVALUES, OnLoadValues)
	ON_WM_HELPINFO()
	//}}AFX_MSG_MAP
END_MESSAGE_MAP()

/////////////////////////////////////////////////////////////////////////////
// CProductGroupCriteriaListPage message handlers

BOOL CProductGroupCriteriaListPage::OnInitDialog() 
{
	CPropertyPage::OnInitDialog();
	CRect r;
	int n;
	COperatorService *pOpService = &m_ProductGroupDataService->m_OperatorService;
	COperator *pOperator;

	for (int i=0; i < pOpService->m_OperatorDisplayList.GetSize(); ++i) {
		pOperator = pOpService->m_OperatorDisplayList[i];
		m_OperatorCtrl.AddString(pOperator->m_Display);
		m_OperatorCtrl.SetItemDataPtr(i, pOperator);
	}

	m_OperatorCtrl.GetWindowRect(&r);
	m_OperatorCtrl.SetWindowPos(NULL, 0, 0, r.Width(), r.Height()*m_OperatorCtrl.GetCount(), SWP_NOMOVE|SWP_NOZORDER);

	n = m_RangeListCtrl.GetHeaderCtrl()->GetItemCount();
	for (i=0; i < n; ++i)
		m_RangeListCtrl.DeleteColumn(0);

	m_RangeListCtrl.GetClientRect(&r);
	m_RangeListCtrl.InsertColumn(0, "Name", LVCFMT_LEFT, r.Width()*4/12, 0);
	m_RangeListCtrl.InsertColumn(1, "Operator", LVCFMT_LEFT, r.Width()*2/12, 0);
	m_RangeListCtrl.InsertColumn(2, "Value", LVCFMT_LEFT, r.Width()*4/12, 0);
	m_RangeListCtrl.InsertColumn(3, "HiddenValue", LVCFMT_LEFT, 0, 0);
	m_RangeListCtrl.InsertColumn(4, "In Use", LVCFMT_LEFT, r.Width()*2/12, 0);
	m_RangeListCtrl.InsertColumn(5, "HiddenQueryDBID", LVCFMT_LEFT, 0, 0);

	n = m_ValueListCtrl.GetHeaderCtrl()->GetItemCount();
	for (i=0; i < n; ++i)
		m_ValueListCtrl.DeleteColumn(0);

	m_ValueListCtrl.GetClientRect(&r);
	m_ValueListCtrl.InsertColumn(0, "Value", LVCFMT_LEFT, (int)( (float)r.Width() ), 0);
	

	return TRUE;  // return TRUE unless you set the focus to a control
	              // EXCEPTION: OCX Property Pages should return FALSE
}

void CProductGroupCriteriaListPage::DisplayRanges()
{
	LVITEM lvItem;
	int nItem;
	CString sql, internalOp, displayOp, internalValue, displayValue;

	CString temp;
	COperator *pOperator;

	m_RangeListCtrl.DeleteAllItems();

	for (int i=0; i < m_Criteria->m_CriteriaRangeList.GetSize(); ++i) {
		
		lvItem.mask = LVIF_TEXT | LVIF_PARAM;

		if (m_Criteria->m_CriteriaRangeList[i]->m_CriteriaQueryList.GetSize() == 0) {
			AfxMessageBox("Error loading criteria list element.  The criteria element is corrupt.");
			return;
		}

		internalOp = m_Criteria->m_CriteriaRangeList[i]->m_CriteriaQueryList[0]->m_Operator;
		if (internalOp == "")
			displayOp = "";
		else {
			pOperator = m_ProductGroupDataService->m_OperatorService.ConvertInternalToDisplay(internalOp);
			displayOp = pOperator->m_Display;
		}
		internalValue = m_Criteria->m_CriteriaRangeList[i]->m_CriteriaQueryList[0]->m_Value;
		displayValue = internalValue;
		if (pOperator != NULL) {
			if (pOperator->m_OperandCount > 2)
				displayValue.Replace("^", ",");
			else
				displayValue.Replace("^", " ");	
		}

		lvItem.pszText = m_Criteria->m_CriteriaRangeList[i]->m_Description.GetBuffer(0);
		m_Criteria->m_CriteriaRangeList[i]->m_Description.ReleaseBuffer();
		lvItem.lParam = m_Criteria->m_CriteriaRangeList[i]->m_CriteriaRangeDBID;
		lvItem.iItem = i;
		lvItem.iSubItem = 0;
		nItem = m_RangeListCtrl.InsertItem(&lvItem);
		
		lvItem.mask = LVIF_TEXT;
		lvItem.pszText = internalOp.GetBuffer(0);
		internalOp.ReleaseBuffer();
		lvItem.iItem = nItem;
		lvItem.iSubItem = 1;
		m_RangeListCtrl.SetItem(&lvItem);
		
		lvItem.pszText = displayValue.GetBuffer(0);
		displayValue.ReleaseBuffer();
		lvItem.iSubItem = 2;
		m_RangeListCtrl.SetItem(&lvItem);

		lvItem.pszText = internalValue.GetBuffer(0);
		internalValue.ReleaseBuffer();
		lvItem.iSubItem = 3;
		m_RangeListCtrl.SetItem(&lvItem);

		if (m_Criteria->m_CriteriaRangeList[i]->m_InUse)
			lvItem.pszText = "Yes";
		else
			lvItem.pszText = "No";
		lvItem.iSubItem = 4;
		m_RangeListCtrl.SetItem(&lvItem);
		
		if (m_Criteria->m_CriteriaRangeList[i]->m_CriteriaQueryList.GetSize() > 0)
			temp.Format("%d", m_Criteria->m_CriteriaRangeList[i]->m_CriteriaQueryList[0]->m_CriteriaQueryDBID);
		else
			temp = "0";
		lvItem.pszText = temp.GetBuffer(0);
		temp.ReleaseBuffer();
		lvItem.iSubItem = 5;
		m_RangeListCtrl.SetItem(&lvItem);

	}

	return;

}

void CProductGroupCriteriaListPage::DisplayValues()
{
	LVITEM lvItem;
	int nItem;

	lvItem.mask = LVIF_TEXT | LVIF_PARAM;
	m_ValueListCtrl.DeleteAllItems();

	for (int i=0; i < m_Criteria->m_CriteriaValueList.GetSize(); ++i) {
		lvItem.iItem = i;
		lvItem.iSubItem = 0;
		lvItem.pszText = m_Criteria->m_CriteriaValueList[i]->m_Description.GetBuffer(0);
		m_Criteria->m_CriteriaValueList[i]->m_Description.ReleaseBuffer();
		lvItem.lParam = m_Criteria->m_CriteriaValueList[i]->m_CriteriaValueDBID;
		nItem = m_ValueListCtrl.InsertItem(&lvItem);
	}

	return;

}

void CProductGroupCriteriaListPage::OnAddRange() 
{
	LVITEM lvItem;
	int nItem;

	CProductGroupCriteriaRange *pRange = new CProductGroupCriteriaRange;

	lvItem.mask = LVIF_TEXT | LVIF_PARAM;
	lvItem.pszText = "New Range";
	lvItem.lParam = 0;
	lvItem.iItem = m_RangeListCtrl.GetItemCount();
	lvItem.iSubItem = 0;
	nItem = m_RangeListCtrl.InsertItem(&lvItem);

	lvItem.mask = LVIF_TEXT;
	lvItem.pszText = "";
	lvItem.iItem = nItem;
	lvItem.iSubItem = 1;
	m_RangeListCtrl.SetItem(&lvItem);
	
	lvItem.iSubItem = 2;
	m_RangeListCtrl.SetItem(&lvItem);

	lvItem.iSubItem = 3;
	m_RangeListCtrl.SetItem(&lvItem);

	lvItem.iSubItem = 4;
	lvItem.pszText = "No";
	m_RangeListCtrl.SetItem(&lvItem);

	lvItem.iSubItem = 5;
	lvItem.pszText = "";
	m_RangeListCtrl.SetItem(&lvItem);

	ClearQuery();

	m_RangeListCtrl.SetFocus();
	m_RangeListCtrl.EditLabel(nItem);

	UpdateData(FALSE);

	return;

}

void CProductGroupCriteriaListPage::OnDeleteRange() 
{
	POSITION pos;
	int nItem;

	pos = m_RangeListCtrl.GetFirstSelectedItemPosition();
	if (pos == NULL)
		return;

	nItem = m_RangeListCtrl.GetNextSelectedItem(pos);
	if (nItem < 0)
		return;
	
	if (m_RangeListCtrl.GetItemText(nItem, 4) == "Yes") {
		AfxMessageBox("The currently selected list element can not be deleted because it "
			"is being used by a product group.");
		return;
	}

	m_RangeListCtrl.DeleteItem(nItem);

	ClearQuery();

	return;
	
}

void CProductGroupCriteriaListPage::OnAddValue() 
{
	LVITEM lvItem;
	POSITION pos;
	int nItem;
	CArray <int, int> selectedValues;

	m_AddingValue = TRUE;

	// Save the values that were already selected
	pos = m_ValueListCtrl.GetFirstSelectedItemPosition();
	while (pos != NULL) {
		selectedValues.Add(m_ValueListCtrl.GetNextSelectedItem(pos));
	}

	lvItem.mask = LVIF_TEXT | LVIF_STATE | LVIF_PARAM;
	lvItem.stateMask = LVIS_SELECTED;
	lvItem.state = 0;
	lvItem.pszText = "New Value";
	lvItem.lParam = 0;
	lvItem.iItem = m_ValueListCtrl.GetItemCount();
	lvItem.iSubItem = 0;
	nItem = m_ValueListCtrl.InsertItem(&lvItem);
	
	m_ValueListCtrl.SetFocus();
	m_ValueListCtrl.EditLabel(nItem);

	// Restore the selected values
	for (int i=0; i < selectedValues.GetSize(); ++i) {
		m_ValueListCtrl.SetItemState(selectedValues[i], LVIS_SELECTED, LVIS_SELECTED);
	}

	m_ValueListCtrl.SetItemState(nItem, 0, LVIS_SELECTED);
	m_ValueListCtrl.SetItemState(nItem, LVIS_FOCUSED, LVIS_FOCUSED);

	UpdateData(FALSE);

	m_AddingValue = FALSE;

	return;
	
}

void CProductGroupCriteriaListPage::OnDeleteValue() 
{
	int nCount = m_ValueListCtrl.GetItemCount();

	// Have to go backwards so the list doesn't change as we delete things
	for (int i=nCount-1; i >= 0; i--) {
		if (m_ValueListCtrl.GetItemState(i, LVIS_SELECTED) == LVIS_SELECTED) {
			m_ValueListCtrl.DeleteItem(i);
		}
	}

	UpdateData(FALSE);
	
	return;

}

void CProductGroupCriteriaListPage::OnLoadValues() 
{
	int nCount, i, j, rc;
	CStringArray valueList;
	CString displayValue, internalValue;
	CString temp;
	BOOL found;
	CProductGroupDataService ds;
	
	if (m_ValueListCtrl.GetItemCount() > 0) {
		if (AfxMessageBox("Do you wish to clear the current values before loading?", MB_YESNO) == IDYES) {
			m_ValueListCtrl.DeleteAllItems();
		}
	}
	
	try {
		CWaitCursor cwc;
		if (m_IsFormula)
			nCount = ds.GetCriteriaValueCount(m_Formula);
		else
			nCount = ds.GetCriteriaValueCount(*m_Attribute);
	}
	catch (Ssa_Exception e) {
		utilityHelper.ProcessError("Error counting unique values for attribute.", &e);
		return;
	}
	catch (...) {
		utilityHelper.ProcessError("Error counting unique values for attribute.");
		return;
	}
	
	if (nCount > 50) {
		CString temp;
		temp.Format("Warning. There are %d unique values for this attribute.\n"
			"Are you sure you wish to load them?", nCount);
		if (AfxMessageBox(temp, MB_YESNO) == IDNO)
			return;
	}
	
	try {
		CWaitCursor cwc;
		if (m_IsFormula)
			rc = ds.GetUniqueCriteriaValues(m_Formula, valueList);
		else
			rc = ds.GetUniqueCriteriaValues(*m_Attribute, valueList);
	}
	catch (Ssa_Exception e) {
		utilityHelper.ProcessError("Error selecting unique values for attribute.", &e);
		return;
	}
	catch (...) {
		utilityHelper.ProcessError("Error selecting unique values for attribute.");
		return;
	}

	for (i=0; i < valueList.GetSize(); ++i) {
		temp = valueList[i];
		temp.TrimRight("|");

		if (m_IsFormula) {
			if (temp == "")
				temp = "0";
		}
		else {
			// Need to convert the internal values of a list to display values
			if (m_Attribute->m_Type == DT_LIST) {
				if (m_Attribute->m_InternalToDisplayMap.Lookup(temp, displayValue))
					temp = displayValue;
			}
		}

		found = FALSE;
		for (j=0; j < m_ValueListCtrl.GetItemCount(); ++j) {
			if (temp == m_ValueListCtrl.GetItemText(j, 0)) {
				found = TRUE;
				break;
			}
		}

		if (! found) {
			AddValueItem(temp);
		}
	}

	UpdateData(FALSE);

	return;
}

void CProductGroupCriteriaListPage::OnLoadPossibleValues() 
{
	CStringArray valueList;
	CString temp;
	BOOL found;

	if (m_IsFormula)
		return;

	if (m_ValueListCtrl.GetItemCount() > 0) {
		if (AfxMessageBox("Do you wish to clear the current values before loading?", MB_YESNO) == IDYES) {
			m_ValueListCtrl.DeleteAllItems();
		}
	}
	
	valueList.Copy(m_Attribute->m_ListValues);

	for (int i=0; i < valueList.GetSize(); ++i) {
		temp = valueList[i];

		found = FALSE;
		for (int j=0; j < m_ValueListCtrl.GetItemCount(); ++j) {
			if (temp == m_ValueListCtrl.GetItemText(j, 0)) {
				found = TRUE;
				break;
			}
		}
		if (! found)
			AddValueItem(temp);
	}

	UpdateData(FALSE);
	
}


void CProductGroupCriteriaListPage::OnOK() 
{
	
	CPropertyPage::OnOK();

	return;

}

BOOL CProductGroupCriteriaListPage::OnKillActive() 
{
	CString sql, temp;
	CProductGroupCriteriaRange *pRange;
	CProductGroupCriteriaValue *pValue;
	CProductGroupCriteriaQuery *pQuery;

	for (int i=0; i < m_RangeListCtrl.GetItemCount(); ++i) {
		if (! ValidateRange(i)) {
			m_RangeListCtrl.SetItemState(i, LVIS_SELECTED, LVIS_SELECTED);
			return FALSE;
		}
	}

	for (i=0; i < m_Criteria->m_CriteriaRangeList.GetSize(); ++i)
		delete m_Criteria->m_CriteriaRangeList[i];

	m_Criteria->m_CriteriaRangeList.RemoveAll();

	for (i=0; i < m_RangeListCtrl.GetItemCount(); ++i) {
		pRange = new CProductGroupCriteriaRange;
		
		pRange->m_CriteriaRangeDBID = m_RangeListCtrl.GetItemData(i);
		pRange->m_Description = m_RangeListCtrl.GetItemText(i, 0);

		int insertPoint = m_Criteria->m_CriteriaRangeList.GetSize();
		for (int j=0; j < m_Criteria->m_CriteriaRangeList.GetSize(); ++j) {
			insertPoint = j;
			if (m_Criteria->m_CriteriaRangeList[j]->m_Description.Compare(pRange->m_Description) > 0)
				break;
		}
		m_Criteria->m_CriteriaRangeList.InsertAt(insertPoint, pRange);

		pQuery = new CProductGroupCriteriaQuery;
		temp = m_RangeListCtrl.GetItemText(i, 1);	// internal operator
		pQuery->m_Operator = temp;
		pQuery->m_Value = m_RangeListCtrl.GetItemText(i, 3);	// column 3 is the internal value
		pQuery->m_Attribute = m_Criteria->m_Attribute;
		pQuery->m_AttributeType = m_Criteria->m_AttributeType;
		pQuery->m_CriteriaQueryDBID = atol(m_RangeListCtrl.GetItemText(i, 5));
		pQuery->m_CriteriaRangeDBID = pRange->m_CriteriaRangeDBID;
		pQuery->m_Conjunction = "and";
		pQuery->m_Sequence = 1;
		pQuery->m_Precedence = 0;
	
		pRange->m_CriteriaQueryList.Add(pQuery);

		if (m_RangeListCtrl.GetItemText(i, 4) == "Yes")
			pRange->m_InUse = TRUE;
		else
			pRange->m_InUse = FALSE;
	}

	for (i=0; i < m_Criteria->m_CriteriaValueList.GetSize(); ++i)
		delete m_Criteria->m_CriteriaValueList[i];

	m_Criteria->m_CriteriaValueList.RemoveAll();

	for (i=0; i < m_ValueListCtrl.GetItemCount(); ++i) {
		pValue = new CProductGroupCriteriaValue;
		m_Criteria->m_CriteriaValueList.Add(pValue);
		pValue->m_CriteriaValueDBID = m_ValueListCtrl.GetItemData(i);
		pValue->m_Description = m_ValueListCtrl.GetItemText(i, 0);
	}

	return CPropertyPage::OnKillActive();
}


void CProductGroupCriteriaListPage::OnContextMenu(CWnd* pWnd, CPoint point) 
{
	CMenu menu;
	int curSel;

	if (pWnd == &m_RangeListCtrl) {
		menu.LoadMenu(IDR_CRITERIA_MENU);
		curSel = m_RangeListCtrl.GetSelectedCount();
		
		if (curSel <= 0)
			menu.GetSubMenu(1)->EnableMenuItem(1, MF_BYPOSITION|MF_GRAYED);
		else
			menu.GetSubMenu(1)->EnableMenuItem(1, MF_BYPOSITION|MF_ENABLED);

		menu.GetSubMenu(1)->TrackPopupMenu(TPM_LEFTALIGN|TPM_RIGHTBUTTON, point.x, point.y, this);
	}
	else if (pWnd == &m_ValueListCtrl) {
		menu.LoadMenu(IDR_CRITERIA_MENU);
		curSel = m_ValueListCtrl.GetSelectedCount();
		
		if (curSel < 0)
			menu.GetSubMenu(2)->EnableMenuItem(1, MF_BYPOSITION|MF_GRAYED);
		else
			menu.GetSubMenu(2)->EnableMenuItem(1, MF_BYPOSITION|MF_ENABLED);
		
		if (m_IsFormula)
			menu.GetSubMenu(2)->EnableMenuItem(0, MF_BYPOSITION|MF_GRAYED);
		else {
			if (m_Attribute->m_Type == DT_LIST)
				menu.GetSubMenu(2)->EnableMenuItem(0, MF_BYPOSITION|MF_GRAYED);
			else
				menu.GetSubMenu(2)->EnableMenuItem(0, MF_BYPOSITION|MF_ENABLED);
		}
		menu.GetSubMenu(2)->TrackPopupMenu(TPM_LEFTALIGN|TPM_RIGHTBUTTON, point.x, point.y, this);
	}

}



BOOL CProductGroupCriteriaListPage::OnSetActive() 
{
	CPropertySheet *pParent = (CPropertySheet *)GetParent();
	CProductGroupCriteriaPropertiesPage *pSibling = (CProductGroupCriteriaPropertiesPage *)pParent->GetPage(0);
	CString displayValue, internalValue, temp;

	if (m_Criteria->GetAttributeType() == DT_FORMULA) {
		m_Formula = m_Criteria->GetAttribute();
		m_IsFormula = TRUE;
	}
	else {
		m_IsFormula = FALSE;
		temp = m_Criteria->GetAttribute();
		m_ProductGroupDataService->m_ProductAttributeMap.Lookup(temp, (CObject *&)m_Attribute);
	}
	
	m_OperatorCtrl.SetCurSel(-1);
	m_FromValueCtrl.ShowWindow(SW_HIDE);
	m_FromStaticCtrl.ShowWindow(SW_HIDE);
	m_ToValueCtrl.ShowWindow(SW_HIDE);
	m_ToStaticCtrl.ShowWindow(SW_HIDE);

	// For list types, do not allow them to add values because the internal
	// values will not match
	if (m_IsFormula) {
		GetDlgItem(IDC_ADD_VALUE)->EnableWindow(TRUE);
		m_LoadPossibleCtrl.ShowWindow(SW_HIDE);
	}
	else {
		if (m_Attribute->m_Type == DT_LIST) {
			m_LoadPossibleCtrl.ShowWindow(SW_SHOW);
			GetDlgItem(IDC_ADD_VALUE)->EnableWindow(FALSE);
		}
		else {
			GetDlgItem(IDC_ADD_VALUE)->EnableWindow(TRUE);
			m_LoadPossibleCtrl.ShowWindow(SW_HIDE);
		}
	}

	DisplayRanges();

	DisplayValues();

	UpdateData(FALSE);

	return CPropertyPage::OnSetActive();
}

BOOL CProductGroupCriteriaListPage::ValidateRange(int idx)
{
	CString valueList, op, fromValue, toValue;
	COperator *pOperator = NULL;
	int opCurSel, i;

	op = m_RangeListCtrl.GetItemText(idx, 1);
	valueList = m_RangeListCtrl.GetItemText(idx, 2);

	if (op == "") {
		AfxMessageBox("Please select an operator from the list.");
		m_OperatorCtrl.SetFocus();
		return FALSE;
	}

	pOperator = m_ProductGroupDataService->m_OperatorService.ConvertInternalToDisplay(op);
	if (pOperator == NULL) {
		AfxMessageBox("PLease select an operator from the list.");
		return FALSE;
	}

	opCurSel = m_OperatorCtrl.FindString(0, pOperator->m_Display);
	if (opCurSel < 0) {
		AfxMessageBox("Please select an operator from the list.");
		m_OperatorCtrl.SetFocus();
		return FALSE;
	}

	if (pOperator->m_OperandCount == 1) {
		if (valueList == "") {
			AfxMessageBox("Please enter a comparison value.");
			m_FromValueCtrl.SetFocus();
			return FALSE;
		}
		
		// If they tried to put quotes around it,
		// tell them to use \"
		if ( (valueList[0] == '\'' && valueList[valueList.GetLength()-1] == '\'') ||
			(valueList[0] == '"' && valueList[valueList.GetLength()-1] == '"') )
		{
			AfxMessageBox("To indicate a quoted string, please use \\\" in place of ' or \".");
			m_FromValueCtrl.SetFocus();
			return FALSE;
		}
		
		if (! m_IsFormula) {
			if ((m_Attribute->m_Type == DT_INT || m_Attribute->m_Type == DT_FLOAT) && ! utilityHelper.IsNumeric(valueList)) {
				AfxMessageBox("Please enter a numeric comparison value.");
				m_FromValueCtrl.SetFocus();
				return FALSE;
			}
		}
	}
	else if (pOperator->m_OperandCount > 2) {

		if (valueList == "") {
			AfxMessageBox("Please select at least one value from the list.");
			return FALSE;
		}

		if ( (valueList[0] == '\'' && valueList[valueList.GetLength()-1] == '\'') ||
			(valueList[0] == '"' && valueList[valueList.GetLength()-1] == '"') )
		{
			AfxMessageBox("To indicate a quoted string, please use \\\" in place of ' or \".");
			m_FromValueCtrl.SetFocus();
			return FALSE;
		}

	}
	else {	// between, not between
		if (valueList == "") {
			AfxMessageBox("Please enter numeric starting and ending values.");
			m_FromValueCtrl.SetFocus();
			return FALSE;
		}
		else {
			i = valueList.Find(" ");
			if (i < 0) {
				AfxMessageBox("Please enter a numeric ending value.");
				m_ToValueCtrl.SetFocus();
				return FALSE;
			}
			else {
				fromValue = valueList.Left(i);
				if ( (fromValue[0] == '\'' && fromValue[fromValue.GetLength()-1] == '\'') ||
					(fromValue[0] == '"' && fromValue[fromValue.GetLength()-1] == '"') ) 
				{
					AfxMessageBox("To indicate a quoted string, please use \\\" in place of '. or \"");
					m_FromValueCtrl.SetFocus();
					return FALSE;
				}
				
				toValue = valueList.Mid(i+5);		// skip the " and "
				if ( (toValue[0] == '\'' && toValue[toValue.GetLength()-1] == '\'') ||
					(toValue[0] == '"' && toValue[toValue.GetLength()-1] == '"') ) 
				{
					AfxMessageBox("To indicate a quoted string, please use \\\" in place of ' or \".");
					m_ToValueCtrl.SetFocus();
					return FALSE;
				}

				if (! m_IsFormula) {
					if ((m_Attribute->m_Type == DT_INT || m_Attribute->m_Type == DT_FLOAT) && ! utilityHelper.IsNumeric(fromValue)) {
						AfxMessageBox("Please enter a numeric starting value.");
						m_FromValueCtrl.SetSel(0, -1);
						m_FromValueCtrl.SetFocus();
						return FALSE;
					}

					if ((m_Attribute->m_Type == DT_INT || m_Attribute->m_Type == DT_FLOAT) && ! utilityHelper.IsNumeric(toValue)) {
						AfxMessageBox("Please enter a numeric ending value.");
						m_ToValueCtrl.SetSel(0,-1);
						m_ToValueCtrl.SetFocus();
						return FALSE;
					}
				}
			}
		}
	}

	return TRUE;
	

}

void CProductGroupCriteriaListPage::DisplayQuery(int curSel)
{
	CString op, sql, valueList, internalValueList, value, temp;
	int idx, opCurSel, nItem;
	COperator *pOperator = NULL;

	op = m_RangeListCtrl.GetItemText(curSel, 1);
	internalValueList = m_RangeListCtrl.GetItemText(curSel, 3);

	ClearQuery();
	
	pOperator = m_ProductGroupDataService->m_OperatorService.ConvertInternalToDisplay(op);
	if (pOperator == NULL)
		return;

	// operators are: =, <, >, <=, >=, between
	opCurSel = m_OperatorCtrl.FindStringExact(0, pOperator->m_Display);
	m_OperatorCtrl.SetCurSel(opCurSel);
	
	if (pOperator->m_OperandCount > 2) {
		// equals/not equals - multi-value function
		
		m_FromValueCtrl.ShowWindow(SW_HIDE);
		m_FromStaticCtrl.ShowWindow(SW_HIDE);
		m_ToValueCtrl.ShowWindow(SW_HIDE);
		m_ToStaticCtrl.ShowWindow(SW_HIDE);

		// Parse the list and mark them as selected in the list box
		if (internalValueList != "") {
			idx = internalValueList.Find("^");
			while (idx >= 0) {
				value = internalValueList.Left(idx);
				
				nItem = FindValueItem(value);
				if (nItem < 0)
					nItem = AddValueItem(value);
				m_ValueListCtrl.SetItemState(nItem, LVIS_SELECTED, LVIS_SELECTED);
				
				internalValueList = internalValueList.Right(internalValueList.GetLength() - (idx+1));
				idx = internalValueList.Find("^");
			}
			value = internalValueList;
			nItem = FindValueItem(value);
			if (nItem < 0)
				nItem = AddValueItem(value);
			m_ValueListCtrl.SetItemState(nItem, LVIS_SELECTED, LVIS_SELECTED);
		}

	}
	else if (pOperator->m_OperandCount == 1) {		// single-value functions (<,>,<=,>=)
		value = internalValueList;
		m_FromValueCtrl.ShowWindow(SW_SHOW);
		m_FromStaticCtrl.SetWindowText("Value:");
		m_FromStaticCtrl.ShowWindow(SW_SHOW);
		m_ToValueCtrl.ShowWindow(SW_HIDE);
		m_ToStaticCtrl.ShowWindow(SW_HIDE);
		m_FromValue = value;
	}
	else {			// two-value functions (between)
		idx = internalValueList.Find("^");
		m_FromValue = internalValueList.Left(idx);
		m_ToValue = internalValueList.Mid(idx+5);	 // " AND "
		m_FromValueCtrl.ShowWindow(SW_SHOW);
		m_FromStaticCtrl.SetWindowText("Starting Value:");
		m_FromStaticCtrl.ShowWindow(SW_SHOW);
		m_ToValueCtrl.ShowWindow(SW_SHOW);
		m_ToStaticCtrl.ShowWindow(SW_SHOW);
	}

	UpdateData(FALSE);

	return;

}

void CProductGroupCriteriaListPage::OnItemchangedRangeList(NMHDR* pNMHDR, LRESULT* pResult) 
{
	NM_LISTVIEW* pNMListView = (NM_LISTVIEW*)pNMHDR;

	if ((pNMListView->uNewState & LVIS_SELECTED) && ! (pNMListView->uOldState & LVIS_SELECTED) ) {
		DisplayQuery(pNMListView->iItem);
	}

	*pResult = 0;

	return;
}

void CProductGroupCriteriaListPage::OnEndlabeleditRangeList(NMHDR* pNMHDR, LRESULT* pResult) 
{
	LV_DISPINFO* pDispInfo = (LV_DISPINFO*)pNMHDR;
	LVITEM lvItem;

	*pResult = 0;
	lvItem = pDispInfo->item;	

	if (lvItem.pszText == NULL) {
		if (m_RangeListCtrl.GetItemText(lvItem.iItem, 0) == "New Range")
			lvItem.pszText = "New Range";
	}
	
	if (lvItem.pszText != NULL) {
		for (int i=0; i < lvItem.iItem; ++i) {
			if (m_RangeListCtrl.GetItemText(i, 0).Compare(lvItem.pszText) == 0) {
				AfxMessageBox("Please enter a name that does not already exist.");
				if (m_RangeListCtrl.GetItemText(lvItem.iItem, 0) == "New Range") {
					m_RangeListCtrl.DeleteItem(lvItem.iItem);
					OnAddRange();
				}
				return;
			}
		}
		*pResult = 1;
	}

	return;
}

void CProductGroupCriteriaListPage::OnItemchangedValueList(NMHDR* pNMHDR, LRESULT* pResult) 
{
	NM_LISTVIEW* pNMListView = (NM_LISTVIEW*)pNMHDR;
	BOOL oldSelected, newSelected;

	*pResult = 0;
	
	oldSelected = pNMListView->uOldState & LVIS_SELECTED;
	newSelected = pNMListView->uNewState & LVIS_SELECTED;
	
	if (oldSelected != newSelected && ! m_AddingValue && m_RangeListCtrl.GetSelectedCount() > 0)
		UpdateCurrentElement();

}

void CProductGroupCriteriaListPage::OnEndlabeleditValueList(NMHDR* pNMHDR, LRESULT* pResult) 
{
	LV_DISPINFO* pDispInfo = (LV_DISPINFO*)pNMHDR;
	LVITEM lvItem;
	*pResult = 0;
	lvItem = pDispInfo->item;

	if (lvItem.pszText == NULL) {
		if (m_ValueListCtrl.GetItemText(lvItem.iItem, 0) == "New Value")
			lvItem.pszText = "New Value";
	}
	
	if (lvItem.pszText != NULL) {
		CString temp;
		temp = lvItem.pszText;
		if (temp.Find("'") >= 0) {
			AfxMessageBox("To indicate a quoted string, please use \\\" in place of '.");
			if (m_ValueListCtrl.GetItemText(lvItem.iItem, 0) == "New Value") {
				m_ValueListCtrl.DeleteItem(lvItem.iItem);
				OnAddValue();
			}
			return;
		}
		
		for (int i=0; i < lvItem.iItem; ++i) {
			if (m_ValueListCtrl.GetItemText(i, 0).Compare(lvItem.pszText) == 0) {
				AfxMessageBox("Please enter a name that does not already exist.");
				if (m_ValueListCtrl.GetItemText(lvItem.iItem, 0) == "New Value") {
					m_ValueListCtrl.DeleteItem(lvItem.iItem);
					OnAddValue();
				}
				return;
			}
		}
		*pResult = 1;
	}

	return;
}

void CProductGroupCriteriaListPage::OnSelchangeOperator() 
{
	COperator *pOperator;

	int curSel = m_OperatorCtrl.GetCurSel();
	if (curSel < 0)
		return;

	pOperator = (COperator *)m_OperatorCtrl.GetItemDataPtr(curSel);

	if (pOperator->m_OperandCount > 2) {
		m_FromValueCtrl.ShowWindow(SW_HIDE);
		m_FromStaticCtrl.ShowWindow(SW_HIDE);
		m_ToValueCtrl.ShowWindow(SW_HIDE);
		m_ToStaticCtrl.ShowWindow(SW_HIDE);
	}
	else if (pOperator->m_OperandCount == 1) {
		m_FromValueCtrl.ShowWindow(SW_SHOW);
		m_FromStaticCtrl.SetWindowText("Value:");
		m_FromStaticCtrl.ShowWindow(SW_SHOW);
		m_ToValueCtrl.ShowWindow(SW_HIDE);
		m_ToStaticCtrl.ShowWindow(SW_HIDE);
	}
	else {
		m_FromValueCtrl.ShowWindow(SW_SHOW);
		m_ToValueCtrl.ShowWindow(SW_SHOW);
		m_FromStaticCtrl.SetWindowText("Starting Value:");
		m_FromStaticCtrl.ShowWindow(SW_SHOW);
		m_ToStaticCtrl.ShowWindow(SW_SHOW);
	}
	
	UpdateCurrentElement();

	return;
	
}

void CProductGroupCriteriaListPage::OnChangeFromValue() 
{
	UpdateCurrentElement();	
}

void CProductGroupCriteriaListPage::OnChangeToValue() 
{
	UpdateCurrentElement();	
}


void CProductGroupCriteriaListPage::UpdateCurrentElement()
{
	int opCurSel, nCount, nItem, curSel, i;
	CString valueList, internalValueList, temp, op, displayValue, internalValue;
	LVITEM lvItem;
	POSITION pos;
	COperator *pOperator;

	if (m_ClearingQuery )
		return;

	UpdateData(TRUE);

	curSel = GetCurrentSelection();
	if (curSel < 0)
		return;

	opCurSel = m_OperatorCtrl.GetCurSel();
	if (opCurSel < 0)
		return;

	pOperator = (COperator *)m_OperatorCtrl.GetItemDataPtr(opCurSel);

	m_OperatorCtrl.GetWindowText(op);

	valueList = "";

	if (pOperator->m_OperandCount > 2) {			
		// multi-value items (in, not in)

		nCount = m_ValueListCtrl.GetSelectedCount();
		if (nCount > 0) {
			pos = m_ValueListCtrl.GetFirstSelectedItemPosition();
			i = 0;
			while (pos != NULL) {
				nItem = m_ValueListCtrl.GetNextSelectedItem(pos);
				temp = m_ValueListCtrl.GetItemText(nItem, 0);
				if (i > 0) {
					internalValueList += "^";
					valueList += ",";
				}
				internalValueList += temp;
				valueList += temp;
				i++;
			}
		}		
	}
	else if (pOperator->m_OperandCount == 1) {		// single-value items (<,>,<=,>=)
		if (m_FromValue != "") {
			valueList = m_FromValue;
			internalValueList = m_FromValue;
		}
	}
	else if (pOperator->m_OperandCount == 2) {							// two-value items
		if (m_FromValue != "" ) {
			valueList = m_FromValue;
			internalValueList = m_FromValue;
		}

		if (m_ToValue != "") {
			valueList += " and ";
			valueList += m_ToValue;
			internalValueList += "^and^";
			internalValueList += m_ToValue;
		}
	}
	// Update the list box
	lvItem.mask = LVIF_TEXT;
	// Show the internal operator in the list box for brevity
	lvItem.pszText = pOperator->m_Internal.GetBuffer(0);
	pOperator->m_Internal.ReleaseBuffer();
	lvItem.iItem = curSel;
	lvItem.iSubItem = 1;
	nItem = m_RangeListCtrl.SetItem(&lvItem);

	lvItem.pszText = valueList.GetBuffer(0);
	valueList.ReleaseBuffer();
	lvItem.iSubItem = 2;
	nItem = m_RangeListCtrl.SetItem(&lvItem);

	lvItem.pszText = internalValueList.GetBuffer(0);
	internalValueList.ReleaseBuffer();
	lvItem.iSubItem = 3;
	nItem = m_RangeListCtrl.SetItem(&lvItem);

	UpdateData(FALSE);

	return;
}

int CProductGroupCriteriaListPage::FindValueItem(CString &str)
{
	CString temp;

	LVFINDINFO lvFindInfo;

	lvFindInfo.flags = LVFI_STRING;
	lvFindInfo.psz = str.GetBuffer(0);
	str.ReleaseBuffer();

	return m_ValueListCtrl.FindItem(&lvFindInfo);

}

int CProductGroupCriteriaListPage::AddValueItem(CString &str)
{
	LVITEM lvItem;
	int nItem;
	lvItem.mask = LVIF_TEXT;
	
	lvItem.iItem = m_ValueListCtrl.GetItemCount();
	lvItem.iSubItem = 0;
	lvItem.pszText = str.GetBuffer(0);
	str.ReleaseBuffer();
	nItem = m_ValueListCtrl.InsertItem(&lvItem);

	return nItem;
}


int CProductGroupCriteriaListPage::GetCurrentSelection()
{
	POSITION pos;
	int nItem;

	pos = m_RangeListCtrl.GetFirstSelectedItemPosition();
	if (pos == NULL)
		return -1;

	nItem = m_RangeListCtrl.GetNextSelectedItem(pos);

	return nItem;
}

void CProductGroupCriteriaListPage::DeselectAllValues()
{
	POSITION pos;
	int nItem;

	pos = m_ValueListCtrl.GetFirstSelectedItemPosition();
	while (pos != NULL) {
		nItem = m_ValueListCtrl.GetNextSelectedItem(pos);
		m_ValueListCtrl.SetItemState(nItem, 0, LVIS_SELECTED);
	}

	return;

}

void CProductGroupCriteriaListPage::ClearQuery()
{
	// Clear out previous query
	m_ClearingQuery = TRUE;

	DeselectAllValues();
	m_OperatorCtrl.SetCurSel(-1);
	m_FromValue = "";
	m_ToValue = "";
	m_FromValueCtrl.ShowWindow(SW_HIDE);
	m_FromStaticCtrl.ShowWindow(SW_HIDE);
	m_ToValueCtrl.ShowWindow(SW_HIDE);
	m_ToStaticCtrl.ShowWindow(SW_HIDE);

	UpdateData(FALSE);

	m_ClearingQuery = FALSE;

	return;
}




void CProductGroupCriteriaListPage::OnDblclkValueList(NMHDR* pNMHDR, LRESULT* pResult) 
{
	NMITEMACTIVATE* pNMListView = (NMITEMACTIVATE*)pNMHDR;
	int nItem;
	
	nItem = pNMListView->iItem;

	if (nItem < 0)
		return;

	if (m_FromValueCtrl.IsWindowVisible()) {
		m_FromValue = m_ValueListCtrl.GetItemText(nItem, 0);
		UpdateData(FALSE);
		UpdateCurrentElement();	
	}

	
	*pResult = 0;
}

BOOL CProductGroupCriteriaListPage::OnNotify(WPARAM wParam, LPARAM lParam, LRESULT* pResult) 
{
	NMHDR* pNMHDR = (NMHDR*)lParam;
	if (pNMHDR->code == PSN_HELP) {
		helpService.ShowScreenHelp(IDD);
		return TRUE;
	}		
	return CPropertyPage::OnNotify(wParam, lParam, pResult);
}

BOOL CProductGroupCriteriaListPage::OnHelpInfo(HELPINFO* pHelpInfo) 
{
	helpService.ShowFieldHelp(IDD, pHelpInfo->iCtrlId);
	return FALSE;
}

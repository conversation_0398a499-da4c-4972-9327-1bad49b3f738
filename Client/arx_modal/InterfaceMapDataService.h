// InterfaceMapDataService.h: interface for the CInterfaceMapDataService class.
//
//////////////////////////////////////////////////////////////////////

#if !defined(AFX_INTERFACEMAPDATASERVICE_H__0A1116E5_BDC7_464D_A919_C1C224DF773D__INCLUDED_)
#define AFX_INTERFACEMAPDATASERVICE_H__0A1116E5_BDC7_464D_A919_C1C224DF773D__INCLUDED_

#if _MSC_VER > 1000
#pragma once
#endif // _MSC_VER > 1000

#include "InterfaceMap.h"

class CInterfaceMapDataService  
{
public:
	int DeleteInterfaceMap(long interfaceMapID);
	int SetFacilityDefaultMap(long facilityID, long interfaceMapID, BOOL isDefault);
	BOOL IsFacilityDefaultMap(long facilityID, long interfaceMapID);
	int GetInterfaceMapAttributes(long interfaceMapID, CStringArray &mapAttributes);
	int GetInterfaceMapList(int type, CStringArray &mapList);
	CInterfaceMapDataService();
	virtual ~CInterfaceMapDataService();

	int StoreInterfaceMap(CInterfaceMap &interfaceMap);

};

#endif // !defined(AFX_INTERFACEMAPDATASERVICE_H__0A1116E5_BDC7_464D_A919_C1C224DF773D__INCLUDED_)

//#include "SockFortInt.h"
#include "utilfuncs.h"
#include "stdafx.h"
#include "qqhclasses.h"
#include "ssaBtree.h"

extern TreeElement changesTree;

void NumberBays(CArray <int, int&> &bayIndexes, 
				int selIndex, 
				int aisleIndex, 
				CArray <int,int&> &aisleIndexList, 
				int sideSel, 
				CString &leftBayStart, CString &rightBayStart,
				CString &leftLevelStart, CString &rightLevelStart,
				CString &leftLocationStart, CString &rightLocationStart,
				int leftBayScheme, int rightBayScheme,
				int leftLevelScheme, int rightLevelScheme,
				int leftLocationScheme, int rightLocationScheme,
				int bayleftStep, int bayrightStep,
				int levelleftStep, int levelrightStep,
				int locationleftStep, int locationrightStep,
				int levelleftBreak, int levelrightBreak,
				int locationleftBreak, int locationrightBreak,
				int bayPattern, 
				int maxBayLength, 
				TreeElement & changesTree, 
				int sectionIndex,
				CArray <int, int&> &sideSelArray,
				CString &leftBayPattern,
				CString &leftLevelPattern,
				CString &leftLocPattern,
				CString &rightBayPattern,
				CString &rightLevelPattern,
				CString &rightLocPattern);

// Move.h: interface for the CMove class.
//
//////////////////////////////////////////////////////////////////////

#if !defined(AFX_MOVE_H__9E97BF1D_649A_4FE5_9DBF_79B17FFF9EFE__INCLUDED_)
#define AFX_MOVE_H__9E97BF1D_649A_4FE5_9DBF_79B17FFF9EFE__INCLUDED_

#if _MSC_VER > 1000
#pragma once
#endif // _MSC_VER > 1000

#include "ProductPack.h"

class CMove : public CObject  
{
public:
	double GetNetMoveSavings();
	double GetMoveSavings();
	double GetTotalMoveTime();
	double GetTotalMoveCost();
	double GetToTotalCost();
	double GetFromTotalCost();
	int Parse(CString &line);
	CMove();
	virtual ~CMove();

	long m_MoveDBID;
	long m_ChainId;
	int m_ChainSequence;
	CProductPack m_Product;
	long m_FromLocationDBID;
	CString m_FromLocation;
	long m_ToLocationDBID;
	CString m_ToLocation;
	double m_FromTotalCost;
	double m_ToTotalCost;
	double m_ForkHandlingTime;
	double m_ForkHandlingCost;
	double m_ForkTravelDistance;
	double m_ForkTravelTime;
	double m_ForkTravelCost;
	double m_StockerHandlingTime;
	double m_StockerHandlingCost;
	double m_StockerTravelDistance;
	double m_StockerTravelTime;
	double m_StockerTravelCost;
	int m_MoveType;
	
	int m_MoveStatus;
	int m_FromLocationStatus;
	int m_ToLocationStatus;
	int m_ProductStatus;

	int m_CaseQuantity;
	int m_TimeHorizonUnits;
	int m_TimeHorizonDuration;

	typedef enum {
		normalMove = 0,
		moveToTemp = 1,
		moveFromTemp = 2,
		addFacing = 3,
		deleteFacing = 4
	} enumMoveType;

	typedef enum {
		NotIntegrated = 0,
		IntegrationPending = 1,
		Integrated = 2
	} enumIntegrationStatus;

};

#endif // !defined(AFX_MOVE_H__9E97BF1D_649A_4FE5_9DBF_79B17FFF9EFE__INCLUDED_)

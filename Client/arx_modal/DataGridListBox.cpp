// DataGridListBox.cpp : implementation file
//

#include "stdafx.h"
#include "DataGridListBox.h"
#include "DataGrid.h"

#ifdef _DEBUG
#define new DEBUG_NEW
#undef THIS_FILE
static char THIS_FILE[] = __FILE__;
#endif

/////////////////////////////////////////////////////////////////////////////
// CDataGridListBox

extern FILE *flog;

CDataGridListBox::CDataGridListBox()
{
}

CDataGridListBox::~CDataGridListBox()
{
}


BEGIN_MESSAGE_MAP(CDataGridListBox, CComboBox)
	//{{AFX_MSG_MAP(CDataGridListBox)
	ON_WM_CHAR()
	ON_WM_KEYDOWN()
	ON_WM_KILLFOCUS()
	ON_WM_SETFOCUS()
	ON_WM_GETDLGCODE()
	ON_WM_HELPINFO()
	//}}AFX_MSG_MAP
END_MESSAGE_MAP()

/////////////////////////////////////////////////////////////////////////////
// CDataGridListBox message handlers

void CDataGridListBox::OnChar(UINT nChar, UINT nRepCnt, UINT nFlags) 
{
	if (flog != NULL) {
		fprintf(flog, "   ListOnChar(%d)\n", nChar);
		fflush(flog);	
	}
	CComboBox::OnChar(nChar, nRepCnt, nFlags);
}

void CDataGridListBox::OnKeyDown(UINT nChar, UINT nRepCnt, UINT nFlags) 
{
	if (flog != NULL) {
		fprintf(flog, "   ListOnKeyDown(%d)\n", nChar);
		fflush(flog);
	}

	if (nChar == 9) {			// Tab
		CDataGrid *pDataGrid = (CDataGrid *)this->GetParent();
		pDataGrid->ProcessTab();
		return;
	}
	else if (nChar == 16) {		// Back tab
		CDataGrid *pDataGrid = (CDataGrid *)this->GetParent();
		pDataGrid->ProcessArrow(0);
		return;
	}
	else if (nChar == 45) {		// Insert
		CDataGrid *pDataGrid = (CDataGrid *)this->GetParent();
		pDataGrid->ProcessInsert();
		return;
	}
	else if (nChar == 46) {	// Delete
		CDataGrid *pDataGrid = (CDataGrid *)this->GetParent();
		pDataGrid->ProcessDelete();
		return;
	}	
	else if (nChar == 37) { // left arrow
		CDataGrid *pDataGrid = (CDataGrid *)this->GetParent();
		pDataGrid->ProcessArrow(0);
		return;
	}
	else if (nChar == 39) { // right arrow
		CDataGrid *pDataGrid = (CDataGrid *)this->GetParent();
		pDataGrid->ProcessArrow(1);
		return;
	}
	else if (nChar == 38) { // Up arrow
		CDataGrid *pDataGrid = (CDataGrid *)this->GetParent();
		pDataGrid->ProcessArrow(2);
		return;
	}
	else if (nChar == 40) { // Down arrow
		CDataGrid *pDataGrid = (CDataGrid *)this->GetParent();
		pDataGrid->ProcessArrow(3);
		return;
	}
	/*
	else if (nChar == 27) {	// Esc means "Cancel".
		SetWindowText("");
		ShowWindow(SW_HIDE);
		GetParent()->SetFocus();
	}
	else if (nChar == 13)  // Enter means "OK".
		GetParent()->SetFocus();

	*/

	CComboBox::OnKeyDown(nChar, nRepCnt, nFlags);
}

void CDataGridListBox::OnKillFocus(CWnd* pNewWnd) 
{
	if (flog != NULL) {
		fprintf(flog, "   ListKillFocus\n");
		fflush(flog);
	}

	// If the another control or another window gets the focus,
	// make sure the grid processes the leave cell event so that
	// it gets updated with the contents of the edit box.
	if (pNewWnd != this->GetParent()) {
		if (flog != NULL) {
			fprintf(flog, "   ListKillFocus: Calling parent LeaveCellGrid\n");
			fflush(flog);
		}
		CComboBox::OnKillFocus(pNewWnd);
		CDataGrid *pParent = (CDataGrid *)this->GetParent();
		pParent->OnLeaveCellGrid();
		return;
	}
	CComboBox::OnKillFocus(pNewWnd);
	
}

void CDataGridListBox::OnSetFocus(CWnd* pOldWnd) 
{
	if (flog != NULL) {
		fprintf(flog, "   ListSetFocus\n");
		fflush(flog);
	}

	CComboBox::OnSetFocus(pOldWnd);
	
}

UINT CDataGridListBox::OnGetDlgCode() 
{
	if (flog != NULL) {
		fprintf(flog, "   ListGetDlgCode\n");
		fflush(flog);
	}
	return DLGC_WANTTAB|DLGC_WANTCHARS|DLGC_WANTALLKEYS;
}

BOOL CDataGridListBox::OnHelpInfo(HELPINFO* pHelpInfo) 
{
	UNREFERENCED_PARAMETER(pHelpInfo);

	CDataGrid *pParent = (CDataGrid *)this->GetParent();
	pParent->ShowHelp();

	return FALSE;

}

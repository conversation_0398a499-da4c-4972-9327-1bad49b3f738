// AisleProfileDataService.h: interface for the CAisleProfileDataService class.
//
//////////////////////////////////////////////////////////////////////

#if !defined(AFX_AISLEPROFILEDATASERVICE_H__37CA4C24_A91F_4B94_87B2_46DB2E6EF2E9__INCLUDED_)
#define AFX_AISLEPROFILEDATASERVICE_H__37CA4C24_A91F_4B94_87B2_46DB2E6EF2E9__INCLUDED_

#if _MSC_VER > 1000
#pragma once
#endif // _MSC_VER > 1000

#include "qqhclasses.h"
#include "AisleProfile.h"

class CAisleProfileDataService  
{
public:
	int UpdateAisleProfileName(int aisleProfileDBId, const CString &name);
	int GetAisleProfileList(CStringArray &aisleNameList);
	int GetAisleProfile(int aisleProfileDBId, CAisleProfile& aisleProfile);
	int GetAisleProfile(const CString &name, CAisleProfile& aisleProfile);

	int DeleteAisleProfile(int aisleProfileDBId);
	int StoreAisleProfile(CAisleProfile& aisleProfile);
	BOOL IsAisleProfileNameInUse(int dbid, const CString &name);

	int GetAisleProfileNameList(CStringArray &list);
	CAisleProfileDataService();
	virtual ~CAisleProfileDataService();
	BOOL IsSideProfileInAisle(int sideProfileDBID);
};

#endif // !defined(AFX_AISLEPROFILEDATASERVICE_H__37CA4C24_A91F_4B94_87B2_46DB2E6EF2E9__INCLUDED_)

// ProductContainer.cpp: implementation of the CProductContainer class.
//
//////////////////////////////////////////////////////////////////////

#include "stdafx.h"
#include "ProductContainer.h"
#include "UtilityHelper.h"

#ifdef _DEBUG
#undef THIS_FILE
static char THIS_FILE[]=__FILE__;
#define new DEBUG_NEW
#endif

extern CUtilityHelper utilityHelper;

//////////////////////////////////////////////////////////////////////
// Construction/Destruction
//////////////////////////////////////////////////////////////////////

CProductContainer::CProductContainer()
{
	m_Description = "";
	m_Height = 0;
	m_Hi = 0;
	m_IsHeightOverride = FALSE;
	m_IsLengthOverride = FALSE;
	m_IsWidthOverride = FALSE;
	m_Length = 0;
	m_Ti = 0;
	m_Width = 0;
	m_ProductContainerDBID = -1;
}

CProductContainer::CProductContainer(const CProductContainer &other)
{
	m_ProductContainerDBID = other.m_ProductContainerDBID;
	m_Width = other.m_Width;
	m_Length = other.m_Length;
	m_Height = other.m_Height;
	m_Ti = other.m_Ti;
	m_Hi = other.m_Hi;
	m_IsWidthOverride = other.m_IsWidthOverride;
	m_IsLengthOverride = other.m_IsLengthOverride;
	m_IsHeightOverride = other.m_IsHeightOverride;
	m_IsWidthOverrideIncludeInQuery = other.m_IsWidthOverrideIncludeInQuery;
	m_IsLengthOverrideIncludeInQuery = other.m_IsLengthOverrideIncludeInQuery;
	m_IsHeightOverrideIncludeInQuery = other.m_IsHeightOverrideIncludeInQuery;

}


CProductContainer::~CProductContainer()
{

}

CProductContainer& CProductContainer::operator=(const CProductContainer &other)
{
	m_ProductContainerDBID = other.m_ProductContainerDBID;
	m_Width = other.m_Width;
	m_Length = other.m_Length;
	m_Height = other.m_Height;
	m_Ti = other.m_Ti;
	m_Hi = other.m_Hi;
	m_IsWidthOverride = other.m_IsWidthOverride;
	m_IsLengthOverride = other.m_IsLengthOverride;
	m_IsHeightOverride = other.m_IsHeightOverride;
	m_IsWidthOverrideIncludeInQuery = other.m_IsWidthOverrideIncludeInQuery;
	m_IsLengthOverrideIncludeInQuery = other.m_IsLengthOverrideIncludeInQuery;
	m_IsHeightOverrideIncludeInQuery = other.m_IsHeightOverrideIncludeInQuery;

	return *this;
}

BOOL CProductContainer::operator==(const CProductContainer &other)
{
	if (m_ProductContainerDBID != other.m_ProductContainerDBID) return FALSE;
	if (m_Description != other.m_Description) return FALSE;
	if (m_Height != other.m_Height) return FALSE;
	if (m_Hi != other.m_Hi) return FALSE;
	if (m_IsHeightOverride != other.m_IsHeightOverride) return FALSE;
	if (m_IsLengthOverride != other.m_IsLengthOverride) return FALSE;
	if (m_IsWidthOverride != other.m_IsWidthOverride) return FALSE;
	if (m_Length != other.m_Length) return FALSE;
	if (m_Ti != other.m_Ti) return FALSE;
	if (m_Width != other.m_Width) return FALSE;

	return TRUE;
}

CString CProductContainer::Stream()
{
	CString stream;

	stream.Format("%d|%s|%f|%f|%f|"
		"%d|%d|%d|%d|%d|",
		m_ProductContainerDBID, m_Description, m_Width, m_Length, m_Height,
		m_IsWidthOverride, m_IsLengthOverride, m_IsHeightOverride, m_Ti,m_Hi);

	return stream;
}



int CProductContainer::Parse(const CString &line)
{
	CStringArray strings;

	utilityHelper.ParseString(line, "|", strings);

	for (int i=0; i < strings.GetSize(); ++i) {
		switch (i) {
		case 0:
			m_ProductContainerDBID = atoi(strings[i]);
			break;
		case 1:
			m_Description = strings[i];
			break;
		case 2:
			m_Width = atof(strings[i]);
			break;
		case 3:
			m_Length = atof(strings[i]);
			break;
		case 4:
			m_Height = atof(strings[i]);
			break;
		case 7:
			m_IsHeightOverride = atoi(strings[i]);
			break;
		case 8:
			m_Ti = atoi(strings[i]);
			break;
		case 9:
			m_Hi = atoi(strings[i]);
			break;
		// ignore the rest for now

		}
	}

	return 0;
}
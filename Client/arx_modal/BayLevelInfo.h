// BayLevelInfo.h: interface for the CBayLevelInfo class.
//
//////////////////////////////////////////////////////////////////////
#if !defined(AFX_BAYLEVELINFO_H__9A2237C9_F96A_4620_9AD5_A75E6F4D5A29__INCLUDED_)
#define AFX_BAYLEVELINFO_H__9A2237C9_F96A_4620_9AD5_A75E6F4D5A29__INCLUDED_

#include "BayLocationInfo.h"
#include "3DPoint.h"
#include "TreeElement.h"

#if _MSC_VER > 1000
#pragma once
#endif // _MSC_VER > 1000

class CBayLevelInfo : public CObject  
{
public:
	CBayLevelInfo();
	virtual ~CBayLevelInfo();

	double m_Height;
	double m_Thickness;
	BO<PERSON> m_IsHidden;
	BOOL m_IsSelected;

	CTypedPtrArray<CObArray, CBayLocationInfo*> m_LocationList;
	C3DPoint m_Coordinates;
	CString m_Description;

	int m_LocationRowCount;
	int m_LevelDBId;

	CRect m_BoundingRect;

	TreeElement *m_pLevelTreeElement;
};

#endif // !defined(AFX_BAYLEVELINFO_H__9A2237C9_F96A_4620_9AD5_A75E6F4D5A29__INCLUDED_)

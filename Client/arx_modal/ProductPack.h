// ProductPack.h: interface for the CProductPack class.
//
//////////////////////////////////////////////////////////////////////

#if !defined(AFX_PRODUCTPACK_H__5A7F0543_D1F6_11D4_9EC1_00C04FAC149C__INCLUDED_)
#define AFX_PRODUCTPACK_H__5A7F0543_D1F6_11D4_9EC1_00C04FAC149C__INCLUDED_

#if _MSC_VER > 1000
#pragma once
#endif // _MSC_VER > 1000

#include "ProductContainer.h"
#include "UDF.h"
#include "qqhclasses.h"

class CProductPack  : public CObject
{
public:
	static CString ConvertStatusToText(int status);
	CString ConvertStatusToText();

	void SetValueByName(CString &value, CString &name);
	void GetValueByName(CString &value, CString &name);
	CString Stream();
	int ParseAll(const CString &line);
	int Parse(const CString &line);

	CProductPack();
	CProductPack(const CProductPack& other);
	virtual ~CProductPack();
	CProductPack& operator=(const CProductPack &other);
	BOOL operator==(const CProductPack& other);
	BOOL operator!=(const CProductPack& other) { return (! (*this == other)); }

	BOOL m_IsProxy;			// indicates if partial (proxy) product was selected or not
	long m_ProductPackDBID;
	CString m_Description;
	CString m_WMSProductID;
	CString m_WMSProductDetailID;;
	int m_CasePack;
	int m_InnerPack;
	int m_UnitOfIssue;
	BOOL m_IsAssignmentLocked;

	int m_ProductGroupDBID;
	CString m_ProductGroup;

	int m_LocationDBID;
	CString m_Location;
	int m_CaseQuantity;
	int m_LocProductGroupDBID;
	CString m_LocProductGroup;
	int m_BayProfileDBID;
	CString m_BayProfile;
	CString m_LevelType;

	int m_OptimizedLocationDBID;
	CString m_OptimizedLocation;
	int m_OptimizedCaseQuantity;
	int m_OptimizedLocProductGroupDBID;
	CString m_OptimizedLocProductGroup;
	int m_OptimizedBayProfileDBID;
	CString m_OptimizedBayProfile;
	CString m_OptimizedLevelType;

	double m_Movement;
	double m_BalanceOnHand;
	int m_OptimizeBy;
	double m_NumberOfHits;
	BOOL m_IsHazard;
	BOOL m_IsPickToBelt;
	BOOL m_RotateXAxis;
	BOOL m_RotateYAxis;
	BOOL m_RotateZAxis;

	double m_CaseWidth;
	double m_CaseLength;
	double m_CaseHeight;
	double m_InnerWidth;
	double m_InnerLength;
	double m_InnerHeight;
	double m_EachWidth;
	double m_EachLength;
	double m_EachHeight;

	double m_NestedWidth;
	double m_NestedLength;
	double m_NestedHeight;

	CString m_LastOptimizeAttribute;

	double m_Weight;
	int m_MaxStackNumber;
	int m_NumberInPallet;

	double m_RotatedWidth;
	double m_RotatedLength;
	double m_RotatedHeight;

	int m_Status;
	int m_IsActive;
	int m_Trace;
	double m_PreviousMovement;
	double m_PreviousBOH;
	CString m_CommodityType;
	CString m_CrushFactor;
	int m_ProductKey;
	
	int m_RetailUnitsPerShippingUnit;

	CProductContainer m_Container;
	// Use this if it turns out we do need multiple containers per product
	//CTypedPtrArray<CObArray, CProductContainer*> m_ContainerList;
	CTypedPtrArray<CObArray, CUDF*> m_UDFList;

	typedef enum {
		NotIntegrated = 0,
		Integrated = 1
	} enumIntegrationStatus;

	typedef enum {
		OptimizeByCube = 0,
		OptimizeByLabor = 1
	} enumOptimizationMethod;

	typedef enum {
		KeyGenerate = -1,
		KeyLookup = -2
	} enumKeyMethod;
};

#endif // !defined(AFX_PRODUCTPACK_H__5A7F0543_D1F6_11D4_9EC1_00C04FAC149C__INCLUDED_)

// MessageQueueHelper.h: interface for the CMessageQueueHelper class.
//
//////////////////////////////////////////////////////////////////////

#if !defined(AFX_MESSAGEQUEUEHELPER_H__25115813_FCC2_4FBF_B09D_5CAD199C8270__INCLUDED_)
#define AFX_MESSAGEQUEUEHELPER_H__25115813_FCC2_4FBF_B09D_5CAD199C8270__INCLUDED_

extern "C" {
//	#include <mqagent.h>
	#include <connect_types.h>
}


#if _MSC_VER > 1000
#pragma once
#endif // _MSC_VER > 1000

class CMessageQueueHelper : public CObject  
{
public:
	void DisplayError(int result);
	int Rollback();
	int Commit();
	int BeginTransaction();
	int OpenQueue(const CString &host, int port, const CString &queueMgr, const CString &queue,
		const CString& channel);
	int GetMessage(char **msg);
	int PutMessage(const char *msg, int size);
	int CloseQueue();

	CMessageQueueHelper();
	virtual ~CMessageQueueHelper();
	
	typedef int(*ssamq_BuildMQSeriesURIFunction)(const char *, const char *, const char *, int, char *);
	typedef long (*ssamq_GetLastErrorFunction)(MQConnection *p_Conn);
	typedef int (*ssamq_GetCorrelIDFunction)(long **p_ID);
	typedef int (*ssamq_SetConnectionParametersFunction)(MQConnection *p_Conn, const char *p_URI);
	typedef int (*ssamq_DisconnectFunction)(MQConnection *p_Conn);
	typedef int (*ssamq_FreeStringFunction)(char *p_String);
	typedef int (*ssamq_FreeMemoryFunction)(void *p_Mem);
	typedef int (*ssamq_AllocMemoryFunction)(long Size, void **p_Mem);
	typedef int (*ssamq_ConnectFunction)(MQConnection **p_Conn, const char *URI);
	typedef int (*ssamq_GetDateTimeStampFunction)(char **p_DateTime);
	typedef int (*ssamq_GetUUIDFunction)(char **p_UUID);
	typedef int (*ssamq_BeginTransactionFunction)(MQConnection *p_Conn);
	typedef int (*ssamq_CommitTransactionFunction)(MQConnection *p_Conn);
	typedef int (*ssamq_AbortTransactionFunction)(MQConnection *p_Conn);
	typedef int (*ssamq_AppendStrFunction)(char **p_Str, char **p_StrEnd, size_t *p_StrLen, size_t *p_BufferSize, size_t p_Increment, const char *p_NewStr);
	typedef int (*ssamq_GetErrorDescriptionFunction)(int Error, char **Desc);
	typedef int (*ssamq_GetLongErrorDescriptionFunction)(int Error, char **Desc);
	
	typedef int (*ssamq_GetMessageFunction)(MQConnection *p_Conn, const char *SourceQ, char **p_Message, int p_Timeout);
	typedef int (*ssamq_SendMessageFunction)(MQConnection *p_Conn, const char *p_Message, const char *p_DestinationQ, int Persistent);

private:
	CString m_Queue;
	MQConnection *m_pConnection;
	HMODULE m_hConnect;
};

#endif // !defined(AFX_MESSAGEQUEUEHELPER_H__25115813_FCC2_4FBF_B09D_5CAD199C8270__INCLUDED_)

// WMSGroup.cpp: implementation of the CWMSGroup class.
//
//////////////////////////////////////////////////////////////////////

#include "stdafx.h"
#include "modal.h"
#include "WMSGroup.h"
#include "UtilityHelper.h"

#ifdef _DEBUG
#undef THIS_FILE
static char THIS_FILE[]=__FILE__;
#define new DEBUG_NEW
#endif

extern CUtilityHelper utilityHelper;

//////////////////////////////////////////////////////////////////////
// Construction/Destruction
//////////////////////////////////////////////////////////////////////

CWMSGroup::CWMSGroup()
{
	m_WMSGroupDBId = 0;
	m_ExternalSystemDBId = 0;
}

CWMSGroup& CWMSGroup::operator=(const CWMSGroup& other)
{
	m_WMSGroupDBId = other.m_WMSGroupDBId;
	m_Name = other.m_Name;
	m_Description = other.m_Description;
	m_WMSId = other.m_WMSId;
	m_ExternalSystemDBId = other.m_ExternalSystemDBId;
	m_ExternalSystemName = other.m_ExternalSystemName;

	for (int i=0; i < m_WMSList.GetSize(); ++i)
		delete m_WMSList[i];
	m_WMSList.RemoveAll();

	for (i=0; i < other.m_WMSList.GetSize(); ++i) {
		CWMS *pWMS = new CWMS(*other.m_WMSList[i]);
		m_WMSList.Add(pWMS);
	}

	for (i=0; i < m_ConnectionList.GetSize(); ++i)
		delete m_ConnectionList[i];

	m_ConnectionList.RemoveAll();

	for (i=0; i < other.m_ConnectionList.GetSize(); ++i) {
		CWMSGroupConnection *pConn = new CWMSGroupConnection(*other.m_ConnectionList[i]);
		m_ConnectionList.Add(pConn);
	}
	
	return *this;
}

CWMSGroup::CWMSGroup(const CWMSGroup& other)
{
	m_WMSGroupDBId = other.m_WMSGroupDBId;
	m_Name = other.m_Name;
	m_Description = other.m_Description;
	m_WMSId = other.m_WMSId;
	m_ExternalSystemDBId = other.m_ExternalSystemDBId;
	m_ExternalSystemName = other.m_ExternalSystemName;

	for (int i=0; i < m_WMSList.GetSize(); ++i)
		delete m_WMSList[i];
	m_WMSList.RemoveAll();

	for (i=0; i < other.m_WMSList.GetSize(); ++i) {
		CWMS *pWMS = new CWMS(*other.m_WMSList[i]);
		m_WMSList.Add(pWMS);
	}

	for (i=0; i < m_ConnectionList.GetSize(); ++i)
		delete m_ConnectionList[i];

	m_ConnectionList.RemoveAll();

	for (i=0; i < other.m_ConnectionList.GetSize(); ++i) {
		CWMSGroupConnection *pConn = new CWMSGroupConnection(*other.m_ConnectionList[i]);
		m_ConnectionList.Add(pConn);
	}
	
}

BOOL CWMSGroup::operator==(const CWMSGroup& other)
{
	if (m_WMSGroupDBId != other.m_WMSGroupDBId) return FALSE;
	if (m_Name != other.m_Name) return FALSE;
	if (m_Description != other.m_Description) return FALSE;
	if (m_WMSId != other.m_WMSId) return FALSE;
	if (m_ExternalSystemDBId != other.m_ExternalSystemDBId) return FALSE;
	if (m_ExternalSystemName != other.m_ExternalSystemName) return FALSE;

	if (m_WMSList.GetSize() != other.m_WMSList.GetSize()) return FALSE;
	if (m_ConnectionList.GetSize() != other.m_ConnectionList.GetSize()) return FALSE;

	for (int i=0; i < other.m_WMSList.GetSize(); ++i) {
		if (m_WMSList[i] != other.m_WMSList[i]) return FALSE;
	}

	for (i=0; i < other.m_ConnectionList.GetSize(); ++i) {
		if (m_ConnectionList[i] != other.m_ConnectionList[i]) return FALSE;
	}

	return TRUE;

}

CWMSGroup::~CWMSGroup()
{
	for (int i=0; i < m_WMSList.GetSize(); ++i)
		delete m_WMSList[i];
	
	for (i=0; i < m_ConnectionList.GetSize(); ++i)
		delete m_ConnectionList[i];
}

int CWMSGroup::Parse(CString &line)
{
	CStringArray strings;

	utilityHelper.ParseString(line, "|", strings);
	for (int i=0; i < strings.GetSize(); ++i) {
		switch (i) {
		case 0:
			m_WMSGroupDBId = atoi(strings[i]);
			break;
		case 1:
			m_Name = strings[i];
			break;
		case 2:
			m_Description = strings[i];
			break;
		case 3:
			m_WMSId = strings[i];
			break;
		case 4:
			m_ExternalSystemDBId = atoi(strings[i]);
			break;
		case 5:
		case 6:
			break;
		case 7:
			m_ExternalSystemName = strings[i];
			break;
		case 8:
			m_ExternalSystemName += " - ";
			m_ExternalSystemName += strings[i];
			break;
		}
	}

	return 0;
}

void CWMSGroup::SetConnectionsByType()
{
	CMapStringToPtr map;

	// we need a default connection that catches any interfaces that don't have a specific connection

	BOOL foundDefault = FALSE;
	for (int i=0; i < m_ConnectionList.GetSize(); ++i) {
		CWMSGroupConnection *pConn = m_ConnectionList[i];
		
		CString temp;
		temp.Format("%d-%d", pConn->m_Direction, pConn->m_InterfaceType);

		map.SetAt(temp, pConn);

		if (pConn->m_Direction == CWMSGroupConnection::Both && pConn->m_InterfaceType == CWMSGroupConnection::All)
			foundDefault = TRUE;

	}

	if (! foundDefault) {
		CExternalConnection *pExConn = new CExternalConnection;
		pExConn->m_ConnectionType = CExternalConnection::Prompt;
		CWMSGroupConnection *pConn = new CWMSGroupConnection;
		pConn->m_ConnectionName = "Default";
		pConn->m_Direction = CWMSGroupConnection::Both;
		pConn->m_InterfaceType = CWMSGroupConnection::All;
		pConn->m_ExternalConnectionDBId = 0;
		pConn->m_pExternalConnection = pExConn;
		pConn->m_GroupName = this->m_Name;
		pConn->m_GroupConnectionDBId = 0;
		pConn->m_WMSGroupDBId = this->m_WMSGroupDBId;
		m_ConnectionList.Add(pConn);
		
		CString temp;
		temp.Format("%d-%d", pConn->m_Direction, pConn->m_InterfaceType);
		map.SetAt(temp, pConn);		
	}


	CString temp, allInbound, allOutbound, all, inboundConf, outboundConf;

	all.Format("%d-%d", CWMSGroupConnection::Both, CWMSGroupConnection::All);
	allInbound.Format("%d-%d", CWMSGroupConnection::Inbound, CWMSGroupConnection::All);
	allOutbound.Format("%d-%d", CWMSGroupConnection::Outbound, CWMSGroupConnection::All);
	inboundConf.Format("%d-%d", CWMSGroupConnection::Inbound, CWMSGroupConnection::Confirmation);
	outboundConf.Format("%d-%d", CWMSGroupConnection::Outbound, CWMSGroupConnection::Confirmation);

	CWMSGroupConnection *pConn;

	temp.Format("%d-%d", CWMSGroupConnection::Inbound, CWMSGroupConnection::LocationInterface);
	if (map.Lookup(temp, (void *&)pConn))
		m_pLocInboundConnection = pConn;
	else if (map.Lookup(allInbound, (void *&)pConn))
		m_pLocInboundConnection = pConn;
	else if (map.Lookup(all, (void *&)pConn))
		m_pLocInboundConnection = pConn;
	else
		m_pLocInboundConnection = NULL;

	temp.Format("%d-%d", CWMSGroupConnection::Inbound, CWMSGroupConnection::ProductInterface);
	if (map.Lookup(temp, (void *&)pConn))
		m_pProdInboundConnection = pConn;
	else if (map.Lookup(allInbound, (void *&)pConn))
		m_pProdInboundConnection = pConn;
	else if (map.Lookup(all, (void *&)pConn))
		m_pProdInboundConnection = pConn;
	else
		m_pProdInboundConnection = NULL;

	temp.Format("%d-%d", CWMSGroupConnection::Inbound, CWMSGroupConnection::AssignmentInterface);
	if (map.Lookup(temp, (void *&)pConn))
		m_pAssgInboundConnection = pConn;
	else if (map.Lookup(allInbound, (void *&)pConn))
		m_pAssgInboundConnection = pConn;
	else if (map.Lookup(all, (void *&)pConn))
		m_pAssgInboundConnection = pConn;
	else
		m_pAssgInboundConnection = NULL;

	temp.Format("%d-%d", CWMSGroupConnection::Outbound, CWMSGroupConnection::LocationInterface);
	if (map.Lookup(temp, (void *&)pConn))
		m_pLocOutboundConnection = pConn;
	else if (map.Lookup(allOutbound, (void *&)pConn))
		m_pLocOutboundConnection = pConn;
	else if (map.Lookup(all, (void *&)pConn))
		m_pLocOutboundConnection = pConn;
	else
		m_pLocOutboundConnection = NULL;

	temp.Format("%d-%d", CWMSGroupConnection::Outbound, CWMSGroupConnection::AssignmentInterface);
	if (map.Lookup(temp, (void *&)pConn))
		m_pAssgOutboundConnection = pConn;
	else if (map.Lookup(allOutbound, (void *&)pConn))
		m_pAssgOutboundConnection = pConn;
	else if (map.Lookup(all, (void *&)pConn))
		m_pAssgOutboundConnection = pConn;
	else
		m_pAssgOutboundConnection = NULL;

	temp.Format("%d-%d", CWMSGroupConnection::Inbound, CWMSGroupConnection::LocationConfirmationInterface);
	if (map.Lookup(temp, (void *&)pConn))
		m_pLocConfInboundConnection = pConn;
	else if (map.Lookup(inboundConf, (void *&)pConn))
		m_pLocConfInboundConnection = pConn;
	else if (map.Lookup(allInbound, (void *&)pConn))
		m_pLocConfInboundConnection = pConn;
	else if (map.Lookup(all, (void *&)pConn))
		m_pLocConfInboundConnection = pConn;
	else
		m_pLocConfInboundConnection = NULL;

	temp.Format("%d-%d", CWMSGroupConnection::Inbound, CWMSGroupConnection::AssignmentConfirmationInterface);
	if (map.Lookup(temp, (void *&)pConn))
		m_pAssgConfInboundConnection = pConn;
	else if (map.Lookup(inboundConf, (void *&)pConn))
		m_pAssgConfInboundConnection = pConn;
	else if (map.Lookup(allInbound, (void *&)pConn))
		m_pAssgConfInboundConnection = pConn;
	else if (map.Lookup(all, (void *&)pConn))
		m_pAssgConfInboundConnection = pConn;
	else
		m_pAssgConfInboundConnection = NULL;

	temp.Format("%d-%d", CWMSGroupConnection::Outbound, CWMSGroupConnection::LocationConfirmationInterface);
	if (map.Lookup(temp, (void *&)pConn))
		m_pLocConfOutboundConnection = pConn;
	else if (map.Lookup(outboundConf, (void *&)pConn))
		m_pLocConfOutboundConnection = pConn;
	else if (map.Lookup(allOutbound, (void *&)pConn))
		m_pLocConfOutboundConnection = pConn;
	else if (map.Lookup(all, (void *&)pConn))
		m_pLocConfOutboundConnection = pConn;
	else
		m_pLocConfOutboundConnection = NULL;

	temp.Format("%d-%d", CWMSGroupConnection::Outbound, CWMSGroupConnection::ProductConfirmationInterface);
	if (map.Lookup(temp, (void *&)pConn))
		m_pLocConfOutboundConnection = pConn;
	else if (map.Lookup(outboundConf, (void *&)pConn))
		m_pLocConfOutboundConnection = pConn;
	else if (map.Lookup(allOutbound, (void *&)pConn))
		m_pProdConfOutboundConnection = pConn;
	else if (map.Lookup(all, (void *&)pConn))
		m_pProdConfOutboundConnection = pConn;
	else
		m_pProdConfOutboundConnection = NULL;

	temp.Format("%d-%d", CWMSGroupConnection::Outbound, CWMSGroupConnection::AssignmentConfirmationInterface);
	if (map.Lookup(temp, (void *&)pConn))
		m_pAssgConfOutboundConnection = pConn;
	else if (map.Lookup(outboundConf, (void *&)pConn))
		m_pAssgConfOutboundConnection = pConn;
	else if (map.Lookup(allOutbound, (void *&)pConn))
		m_pAssgConfOutboundConnection = pConn;
	else if (map.Lookup(all, (void *&)pConn))
		m_pAssgConfOutboundConnection = pConn;
	else
		m_pAssgConfOutboundConnection = NULL;

}



CExternalConnection * CWMSGroup::GetExternalConnection(int interfaceType, int direction)
{
	CExternalConnection *pConn = NULL;

	if (direction == CWMSGroupConnection::Inbound) {	
		switch (interfaceType) {
		case CWMSGroupConnection::LocationInterface:
			pConn = m_pLocInboundConnection->m_pExternalConnection;
			break;
		case CWMSGroupConnection::ProductInterface:
			pConn = m_pProdInboundConnection->m_pExternalConnection;
			break;
		case CWMSGroupConnection::AssignmentInterface:
			pConn = m_pAssgInboundConnection->m_pExternalConnection;
			break;
		case CWMSGroupConnection::LocationConfirmationInterface:
			pConn = m_pLocConfInboundConnection->m_pExternalConnection;
			break;
		case CWMSGroupConnection::AssignmentConfirmationInterface:
			pConn = m_pAssgConfInboundConnection->m_pExternalConnection;
			break;
		}
	}
	else if (direction == CWMSGroupConnection::Outbound) {		
		switch (interfaceType) {
		case CWMSGroupConnection::LocationInterface:
			pConn = m_pLocOutboundConnection->m_pExternalConnection;
			break;
		case CWMSGroupConnection::AssignmentInterface:
			pConn = m_pAssgOutboundConnection->m_pExternalConnection;
			break;
		case CWMSGroupConnection::LocationConfirmationInterface:
			pConn = m_pLocConfOutboundConnection->m_pExternalConnection;
			break;
		case CWMSGroupConnection::ProductConfirmationInterface:
			pConn = m_pProdConfOutboundConnection->m_pExternalConnection;
			break;
		case CWMSGroupConnection::AssignmentConfirmationInterface:
			pConn = m_pAssgConfOutboundConnection->m_pExternalConnection;
			break;
		}
	}

	return pConn;
}

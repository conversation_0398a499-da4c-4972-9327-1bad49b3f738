// SideProfile.h: interface for the CSideProfile class.
//
//////////////////////////////////////////////////////////////////////

#if !defined(AFX_SIDEPROFILE_H__4A2F3657_6514_4753_BD2F_FDC0CF505409__INCLUDED_)
#define AFX_SIDEPROFILE_H__4A2F3657_6514_4753_BD2F_FDC0CF505409__INCLUDED_
#include "BayProfile.h"

#if _MSC_VER > 1000
#pragma once
#endif // _MSC_VER > 1000

class CSideProfile : public CObject  
{
public:
	double CalculateMaxBayHeight();
	double CalculateLength();
	double GetMaxBayDepth();

	CSideProfile();
	virtual ~CSideProfile();
	CSideProfile& operator=(const CSideProfile &other);
	CSideProfile(const CSideProfile &other);
	BOOL operator==(const CSideProfile& other);
	BOOL operator!=(const CSideProfile& other) { return !(*this == other);};
	int Parse(CString &line);
	int Draw(BOOL currentDB, BOOL doSave);
	int DrawByPosition(AcDbDatabase *pDatabase, const C3DPoint &backLeftPoint, double rotation, int isRightSide);

	int m_SideProfileDBId;
	CString m_Description;
	double m_TotalLength;
	double m_MaximumBayDepth;
	double m_MaximumBayHeight;
	
	BOOL m_FixedLength;

	CTypedPtrArray<CObArray, CBayProfile*> m_BayProfileList;
private:
	Acad::ErrorStatus DrawSide();
	AcDbDatabase *m_pDatabase;
    AcDbBlockTable *m_pBlockTable;
    AcDbBlockTableRecord *m_pBlockTableRecord;
};

#endif // !defined(AFX_SIDEPROFILE_H__4A2F3657_6514_4753_BD2F_FDC0CF505409__INCLUDED_)

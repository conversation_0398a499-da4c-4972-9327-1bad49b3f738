#if !defined(AFX_PROCESSING_H__F38A5F97_8AFF_11D4_91ED_00400542E36B__INCLUDED_)
#define AFX_PROCESSING_H__F38A5F97_8AFF_11D4_91ED_00400542E36B__INCLUDED_

#if _MSC_VER > 1000
#pragma once
#endif // _MSC_VER > 1000
// Processing.h : header file
//
#include "Resource.h"

/////////////////////////////////////////////////////////////////////////////
// CProcessing dialog

class CProcessing : public CDialog
{
// Construction
public:
	CProcessing(CWnd* pParent = NULL);   // standard constructor

// Dialog Data
	//{{AFX_DATA(CProcessing)
	enum { IDD = IDD_PROCESSING };
	CString	m_StatusText;
	//}}AFX_DATA


// Overrides
	// ClassWizard generated virtual function overrides
	//{{AFX_VIRTUAL(CProcessing)
	protected:
	virtual void DoDataExchange(CDataExchange* pDX);    // DDX/DDV support
	virtual void PostNcDestroy();
	//}}AFX_VIRTUAL

// Implementation
protected:

	// Generated message map functions
	//{{AFX_MSG(CProcessing)
		// NOTE: the ClassWizard will add member functions here
	//}}AFX_MSG
	DECLARE_MESSAGE_MAP()
	
	afx_msg LONG OnAcadKeepFocus(UINT, LONG);
};

//{{AFX_INSERT_LOCATION}}
// Microsoft Visual C++ will insert additional declarations immediately before the previous line.

#endif // !defined(AFX_PROCESSING_H__F38A5F97_8AFF_11D4_91ED_00400542E36B__INCLUDED_)

//////////////////////////////////////////////////////////////////////
// Classname :		CStartup
// Description :	This class handles the behavior and data for the
//					"Succeed User Mode" dialog.
// Date Created :	05/1998
// Author :			ACS
//////////////////////////////////////////////////////////////////////
// Inputs : Input params
// Outputs : Output params / return values
// Explanation : The user is allow to choose the mode they want to
//			work on when working on Succeed.  There are three
//			modes the user can choose from: Wizard mode, Open a
//			facility and modified it, and Start a new facility.
//			The user also can select the WMS type and the units
//			when seleting Wizard mode or New Facility mode.
//////////////////////////////////////////////////////////////////////
//////////////////////////////////////////////////////////////////////
// Revisions :		1.00.00
//   Date-initials : 
//					11/04/1998	- Added comments
//////////////////////////////////////////////////////////////////////
	/*******************************************************
	*
	*                       NOTICE
	*
	*  THIS SOFTWARE IS THE PROPERTY OF AND CONTAINS
	*  CONFIDENTIAL INFORMATION OF SSA GLOBAL 
	*  TECHNOLOGIES, INC., AND SHALL NOT BE COPIED,
	*  USED, NOR DISCLOSED WITHOUT EXPRESS WRITTEN
	*  AUTHORIZATION.  ALTHOUGH PUBLICATION IS NOT
	*  INTENDED, IN THE EVENT OF PUBLICATION, THE
	*  FOLLOWING NOTICE IS APPLICABLE:
	*
	*  (c) COPYRIGHT 2004 SSA GLOBAL TECHNOLOGIES, INC.
	*
	*           SSA GLOBAL TECHNOLOGIES, INC.
	*
	********************************************************/
//////////////////////////////////////////////////////////////////////

// Startup.cpp : implementation file
//

#include "stdafx.h"
#include "RESOURCEHELPER.H"

#include <afxmt.h>
#include <adscodes.h>

#include "modal.h"
#include "Startup.h"
#include "process.h"
#include "HelpService.h"
#include "ControlService.h"

#ifdef _DEBUG
#define new DEBUG_NEW
#undef THIS_FILE
static char THIS_FILE[] = __FILE__;
#endif

extern CHelpService helpService;
extern CEvent g_ThreadDone;
extern CControlService controlService;

/////////////////////////////////////////////////////////////////////////////
// CStartup dialog


///////////////////////
// Constructor.
///////////////////////
CStartup::CStartup(CWnd* pParent /*=NULL*/)
	: CDialog(CStartup::IDD, pParent)
{
	//{{AFX_DATA_INIT(CStartup)
	//}}AFX_DATA_INIT
	m_Units = -1;
	m_WMS = -1;

	m_UnitsListCreated   = FALSE;
	m_WMSListCreated     = FALSE;
	m_InstructCreated    = FALSE;
	m_DescriptionCreated = FALSE;
	m_HelpCreated		 = FALSE;
}



///////////////////////
//Destructor
///////////////////////
CStartup::~CStartup()
{
	DestroyUnitsBox();
	DestroyWMSBox();
	DestroyInstructBox();
}



void CStartup::DoDataExchange(CDataExchange* pDX)
{
	CDialog::DoDataExchange(pDX);
	//{{AFX_DATA_MAP(CStartup)
	DDX_Control(pDX, IDC_SUCCEEDHELP, m_ctlHelpStart);
	DDX_Control(pDX, IDC_OPENFACILITY, m_ctlOpenFacility);
	DDX_Control(pDX, IDC_NEWFACILITY, m_ctlNewFacility);
	DDX_Control(pDX, IDC_INSTRUCTIONS, m_ctlInstructions);
	DDX_Control(pDX, IDC_USEWIZARD, m_ctlUseWizard);
	//}}AFX_DATA_MAP
}


BEGIN_MESSAGE_MAP(CStartup, CDialog)
	//{{AFX_MSG_MAP(CStartup)
	ON_BN_CLICKED(IDC_INSTRUCTIONS, OnInstructions)
	ON_BN_CLICKED(IDC_USEWIZARD, OnUsewizard)
	ON_BN_CLICKED(IDC_NEWFACILITY, OnNewFacility)
	ON_BN_CLICKED(IDC_OPENFACILITY, OnOpenFacility)
	ON_WM_HELPINFO()
	ON_BN_CLICKED(IDC_SUCCEEDHELP, OnSucceedhelp)
	ON_BN_DOUBLECLICKED(IDC_OPENFACILITY, OnDoubleclickedOpenfacility)
	ON_BN_DOUBLECLICKED(IDC_NEWFACILITY, OnDoubleclickedNewfacility)
	ON_BN_DOUBLECLICKED(IDC_USEWIZARD, OnDoubleclickedUsewizard)
	ON_BN_CLICKED(IDHELP, OnHelp)
	ON_MESSAGE(WM_ACAD_KEEPFOCUS, OnAcadKeepFocus)
	ON_COMMAND(IDR_PROFILE_WIZARD, OnUsewizard)
	ON_COMMAND(IDR_OPEN_FACILITY, OnOpenFacility)
	ON_COMMAND(IDR_NEW_FACILITY, OnDoubleclickedNewfacility)
	//}}AFX_MSG_MAP
END_MESSAGE_MAP()


/////////////////////////////////////////////////////////////////////////////
// CStartup message handlers

/////////////////////////////////////////////////////////
//This method is executed when initializing the dialog.
/////////////////////////////////////////////////////////
BOOL CStartup::OnInitDialog() 
{
	CDialog::OnInitDialog();

	m_hAccel = LoadAccelerators(AfxGetInstanceHandle(), MAKEINTRESOURCE(IDD_STARTUP_ACC));
	DWORD err = GetLastError();


	CString temp = controlService.GetApplicationData("ShowWizard", "Dialogs\\Main");
	CButton *pButton = (CButton *)GetDlgItem(IDC_SHOW_CHECK);
	if (temp == "No")
		pButton->SetCheck(TRUE);
	else
		pButton->SetCheck(FALSE);

	m_startingFont = CWnd::GetFont();

	//load the push buttons with the bitmaps//
	/*
	m_ctlUseWizard.AutoLoad(IDC_USEWIZARD, this);
	m_ctlNewFacility.AutoLoad(IDC_NEWFACILITY, this);
	m_ctlOpenFacility.AutoLoad(IDC_OPENFACILITY, this);
	m_ctlInstructions.AutoLoad(IDC_INSTRUCTIONS, this);
	m_ctlHelpStart.AutoLoad(IDC_SUCCEEDHELP, this);
	*/

	m_ctlUseWizard.AutoLoad(IDC_USEWIZARD, this);
	m_ctlNewFacility.AutoLoad(IDC_NEWFACILITY, this);
	m_ctlOpenFacility.AutoLoad(IDC_OPENFACILITY, this);
	m_ctlInstructions.AutoLoad(IDC_INSTRUCTIONS, this);
	m_ctlHelpStart.AutoLoad(IDC_SUCCEEDHELP, this);

	//initialize our data

	//set the 'use wizard' to selected status//
	m_selectedCtlId      = IDC_USEWIZARD;
	OnUsewizard();
	
	return TRUE;  // return TRUE unless you set the focus to a control
	              // EXCEPTION: OCX Property Pages should return FALSE
}



////////////////////////////////////////////////////////////////////
//This method is executed when the user selects the "Instructions"
//button of the dialog.
////////////////////////////////////////////////////////////////////
void CStartup::OnInstructions() 
{
	CString tmpString;

	//set other buttons to a non-selected status
	m_ctlUseWizard.SetState(FALSE);
	m_ctlNewFacility.SetState(FALSE);
	m_ctlOpenFacility.SetState(FALSE);
	m_ctlHelpStart.SetState(FALSE);
	m_ctlInstructions.SetState(TRUE);

//	m_ctlDescription->SetWindowText("Instructions Description");
//	tmpString = "By selecting this option you will get a brief description of each button on the Left side.\n";
//	m_ctlDescription2->SetWindowText(tmpString);

	DestroyUnitsBox();
	DestroyWMSBox();
	DestroyDescriptionBox();
	CreateInstructBox();

	m_ctlInstructions.SetState(TRUE);
	m_selectedCtlId = IDC_INSTRUCTIONS;

}



/////////////////////////////////////////////////////////////////
//This method is executed when the user seletes "Use a Wizard"
//button of the dialog.
/////////////////////////////////////////////////////////////////
void CStartup::OnUsewizard() 
{

	CString tmpString;

	//set the other buttons to a non-selected status
	m_ctlNewFacility.SetState(FALSE);
	m_ctlOpenFacility.SetState(FALSE);
	m_ctlInstructions.SetState(FALSE);
	m_ctlHelpStart.SetState(FALSE);

	m_ctlUseWizard.SetState(TRUE);
	DestroyInstructBox();
	CreateUnitsBox();
	CreateWMSBox();
	CreateDescriptionBox();

	//m_ctlUnitsList->EnableWindow(TRUE);
	m_ctlWMSList->EnableWindow(TRUE);

	//Display Description for the Wizard mode
	m_ctlDescription->SetWindowText("Wizard Description");
	tmpString = "\"Use a Wizard\" allows you to create bays, aisle sides, and aisles by using a wizard ";
	tmpString += "designed for each of these areas in your facility.  ";

	m_ctlDescription2->SetWindowText(tmpString);


	m_ctlUseWizard.SetState(TRUE);
	m_selectedCtlId = IDC_USEWIZARD;

}


//**********************************************************************************************************
    // The following two methods are required for DEBUG - they provide only the stubs
//**********************************************************************************************************

#ifdef _DEBUG
void CStartup::Dump(CDumpContext& dc) const
{
	UNREFERENCED_PARAMETER(dc);
}

void CStartup::AssertValid() const
{
}
#endif

#ifdef _DEBUG
/*
void CBitmapButton::Dump(CDumpContext& dc) const
{
	UNREFERENCED_PARAMETER(dc);
}

void CBitmapButton::AssertValid() const
{
}
*/
#endif



////////////////////////////////////////////////////////////////////
//This method is executed when the user selects the "New Facility"
//button of the dialog.
////////////////////////////////////////////////////////////////////
void CStartup::OnNewFacility() 
{
	CString tmpString;
	
	//set the other buttons to a non-selected status
	m_ctlUseWizard.SetState(FALSE);
	m_ctlInstructions.SetState(FALSE);
	m_ctlOpenFacility.SetState(FALSE);
	m_ctlHelpStart.SetState(FALSE);

	m_ctlNewFacility.SetState(TRUE);

	DestroyInstructBox();
	CreateUnitsBox();
	CreateWMSBox();
	CreateDescriptionBox();

	//m_ctlUnitsList->EnableWindow(TRUE);
	m_ctlWMSList->EnableWindow(TRUE);

	//Show description for the New Facility option
	m_ctlDescription->SetWindowText("New Facility Description");
	tmpString = "Creates a new facility.\n";
	tmpString += "The current unit of measurement and WMS system are displayed.\n";
	tmpString += "These values are set during the install and should not be changed.\n";
	m_ctlDescription2->SetWindowText(tmpString);

	m_ctlNewFacility.SetState(TRUE);
	m_selectedCtlId = IDC_NEWFACILITY;
	
}


////////////////////////////////////////////////////////////////////
//This method is executed when the user selects the "Open Facility"
//method.
////////////////////////////////////////////////////////////////////
void CStartup::OnOpenFacility() 
{
	CString tmpString;

	//set the other buttons to a non-selected state
	m_ctlUseWizard.SetState(FALSE);
	m_ctlInstructions.SetState(FALSE);
	m_ctlNewFacility.SetState(FALSE);
	m_ctlHelpStart.SetState(FALSE);

	m_ctlOpenFacility.SetState(TRUE);


	DestroyInstructBox();
	CreateUnitsBox();
	CreateWMSBox();
	CreateDescriptionBox();

	m_ctlUnitsList->EnableWindow(FALSE);
	m_ctlWMSList->EnableWindow(FALSE);

	//Show description for Open Facility option
	m_ctlDescription->SetWindowText("Open Facility Description");
	tmpString = "Displays the Open Facility dialog and allows you to choose an ";
	tmpString += "existing facility to view or modify.";
	m_ctlDescription2->SetWindowText(tmpString);

	m_ctlOpenFacility.SetState(TRUE);
	m_selectedCtlId = IDC_OPENFACILITY;
	
}

////////////////////////////////////////////////////////////////////
//This method creates and displays the Units List box that is 
//available for the "New Facility" and the "Use a Wizard" options.
////////////////////////////////////////////////////////////////////
void CStartup::CreateUnitsBox()
{

	int  index1, index2;
	CRect rect2;
	RECT rect1;
	GetClientRect(&rect1);

	if (!m_UnitsListCreated)
	{
		rect2.SetRect(rect1.right/3 -21, rect1.bottom/10 -1, 
						rect1.right/2 +10, rect1.bottom/2 -1);

//BOOL CreateEx(DWORD dwExStyle, LPCTSTR lpszClassName, LPCTSTR lpszWindowName, DWORD dwStyle, const RECT& rect, CWnd* pParentWnd, UINT nID, LPVOID lpParam = NULL);

		m_ctlUnitsList = new CListBox();
//		BOOL bCreated = m_ctlUnitsList->CreateEx(WS_EX_CLIENTEDGE, NULL," ", 
//					WS_VISIBLE | WS_BORDER | WS_CHILD | LBS_NOINTEGRALHEIGHT | WS_VSCROLL | WS_TABSTOP |LBS_SORT,
//							rect2, this, IDC_UNITSLIST);
		m_ctlUnitsList->Create(WS_VISIBLE | WS_BORDER | WS_CHILD | LBS_NOINTEGRALHEIGHT | WS_VSCROLL | WS_TABSTOP |LBS_SORT,
							rect2, this, IDC_UNITSLIST);

		m_ctlUnitsList->SetFont(m_startingFont,TRUE);
		index1 = m_ctlUnitsList->AddString("English");
		index2 = m_ctlUnitsList->AddString("Metric");

		CString UnitStr;
		CControlService controlService;
		UnitStr = controlService.GetApplicationData("Units");

		if ( UnitStr.CompareNoCase("Metric") == 0 )
			m_Units = index2;
		else
			m_Units = index1;


		//if (m_Units == -1)
		//	m_Units = index1;    //set English units as the default

		m_ctlUnitsList->SetCurSel(m_Units);  //set the selection back to previous
	
		m_ctlUnitsList->EnableWindow(FALSE);

		m_UnitsListCreated = TRUE;

		//create title Static Box
		rect2.SetRect(rect1.right/3 -21, rect1.bottom/25,
						rect1.right/2 +10, rect1.bottom/12);
		m_ctlUnitsStatic = new CStatic();
		m_ctlUnitsStatic->Create(" ", WS_VISIBLE | WS_CHILD | SS_LEFT,
							rect2, this, IDC_UNITSSTATIC);
		m_ctlUnitsStatic->SetFont(m_startingFont,TRUE);
		m_ctlUnitsStatic->SetWindowText("Unit of Measurement:");

	}
}



//////////////////////////////////////////////////////
//This method destroys the Units List Box.
//////////////////////////////////////////////////////
void CStartup::DestroyUnitsBox()
{

	if (m_UnitsListCreated)
	{
		m_Units = m_ctlUnitsList->GetCurSel();
		if (m_Units == LB_ERR)
			m_Units = -1;
		if (m_ctlUnitsList != NULL)
			delete m_ctlUnitsList;
		m_UnitsListCreated = FALSE;
		if (m_ctlUnitsStatic != NULL)
			delete m_ctlUnitsStatic;
	}
}



///////////////////////////////////////////////////////////////////
//This method creates and display the WMS Type List Box that is
//available to the "New Facility" and the "Use a Wizard" options.
///////////////////////////////////////////////////////////////////
void CStartup::CreateWMSBox()
{

	int  index1, index2;
	CRect rect2;
	RECT rect1;
	GetClientRect(&rect1);

	if (!m_WMSListCreated)
	{
//		rect2.SetRect(265, 30, 378, 150);
		rect2.SetRect(rect1.right/2 +22, rect1.bottom/10 -1, 
					(rect1.right - rect1.right/3 +53), rect1.bottom/2 -1);

		m_ctlWMSList = new CListBox();
		m_ctlWMSList->Create(WS_VISIBLE | WS_BORDER | WS_CHILD | LBS_NOINTEGRALHEIGHT | WS_VSCROLL | WS_TABSTOP,
							rect2, this, IDC_WMSLIST);

		m_ctlWMSList->SetFont(m_startingFont,TRUE);
		index1 = m_ctlWMSList->AddString("SSA WM 2000");
		index2 = m_ctlWMSList->AddString("SSA WM 4000");
		index2 = m_ctlWMSList->AddString("(other)");

		if (m_WMS == -1)
			m_WMS = index1;    //set English  as the default

		m_ctlWMSList->SetCurSel(m_WMS);  //set the selection back to previous

		m_WMSListCreated = TRUE;

		//create title Static Box:
//		rect2.SetRect(265, 12, 378, 25);
		rect2.SetRect(rect1.right/2 +22, rect1.bottom/25, 
					(rect1.right - rect1.right/3 +53), rect1.bottom/12);
		m_ctlWMSStatic = new CStatic();
		m_ctlWMSStatic->Create(" ", WS_VISIBLE | WS_CHILD | SS_LEFT,
							rect2, this, IDC_WMSSTATIC);
		m_ctlWMSStatic->SetFont(m_startingFont,TRUE);
		m_ctlWMSStatic->SetWindowText("Current WMS:");

	}
}



/////////////////////////////////////////////////
//This method destroys the WMS Type List Box.
/////////////////////////////////////////////////
void CStartup::DestroyWMSBox()
{

	if (m_WMSListCreated)
	{
		m_WMS = m_ctlWMSList->GetCurSel();
		if (m_WMS == LB_ERR)
			m_WMS = -1;
		delete m_ctlWMSList;
		m_WMSListCreated = FALSE;
		delete m_ctlWMSStatic;
	}
}



/////////////////////////////////////////////////////////////////////
// This method creates and displays the Instruction description box.
//It is available to the Instructions button off the dialog.
/////////////////////////////////////////////////////////////////////
void CStartup::CreateInstructBox()
{

	CString InstructText;
	CRect rect2;
	RECT rect1;
	GetClientRect(&rect1);

	if (!m_InstructCreated)
	{
//		rect2.SetRect(142, 20, 395, 292);
		rect2.SetRect(rect1.right/3 -21, rect1.bottom/10 -1 , 
						(rect1.right - rect1.right/3 +53), rect1.bottom -20);

		InstructText = "Each of the buttons on the left determines how you will use Optimize. \n\n";
		InstructText += "\"Use a Wizard\"\n";
		InstructText += "Allows you to create bays, aisles sides, and aisles using wizards designed for ";
		InstructText += "each of these areas in your facility.\n\n";
		InstructText += "\"Start a New Facility\"\n";
		InstructText += "Allows you to create a new facility model.\n\n";
		InstructText += "\"Open a Facility\"\n";
		InstructText += "Allows you to open an existing facility mode to modify.\n";

		m_ctlInstruct = new CStatic();
		m_ctlInstruct->Create(InstructText, WS_VISIBLE | WS_CHILD | SS_LEFT | WS_THICKFRAME,
							rect2, this, IDC_INSTRUCTSTATIC);

		m_ctlInstruct->SetFont(m_startingFont,TRUE);
		m_ctlInstruct->SetWindowText(InstructText);
		m_InstructCreated = TRUE;

		//create title Static Box
//		rect2.SetRect(142, 5, 255, 25);
		rect2.SetRect(rect1.right/3 -21, rect1.bottom/25,
						rect1.right/2 +10, rect1.bottom/12);
		m_ctlInstruct2 = new CStatic();
		m_ctlInstruct2->Create(" ", WS_VISIBLE | WS_CHILD | SS_LEFT,
							rect2, this, IDC_INSTRUCTSTATIC2);
		m_ctlInstruct2->SetFont(m_startingFont,TRUE);
		m_ctlInstruct2->SetWindowText("Instructions:");
	
	}
}



/////////////////////////////////////////////////////////
//This method destroys the Instruction description box.
/////////////////////////////////////////////////////////
void CStartup::DestroyInstructBox()
{

	if (m_InstructCreated)
	{
		delete m_ctlInstruct;
		m_InstructCreated = FALSE;
		delete m_ctlInstruct2;
	}
}



/////////////////////////////////////////////////////////////////////
//This method creates and displays the Description box that provides
//information on the current button selected.   It is available
//for the "Use a Wizard", "New Facility", and "Open Facility"
//options.
/////////////////////////////////////////////////////////////////////
void CStartup::CreateDescriptionBox()
{

	CRect rect2;
	RECT rect1;
	GetClientRect(&rect1);

	if (!m_DescriptionCreated)
	{

//		rect2.SetRect(142, 165, 395, 292);
		rect2.SetRect(rect1.right/3 -21, rect1.bottom/2 +9 , 
						(rect1.right - rect1.right/3 +53), rect1.bottom -20);

		m_ctlDescription = new CButton();
		m_ctlDescription->Create("Description", WS_VISIBLE | WS_CHILD | BS_GROUPBOX,
							rect2, this, IDC_DESCRIPTIONSTATIC);

		m_ctlDescription->SetFont(m_startingFont, TRUE);

		rect2.SetRect(rect1.right/3 -15, rect1.bottom/2 +24, 
						(rect1.right - rect1.right/3 +47), rect1.bottom -35);
		m_ctlDescription2 = new CStatic();
		m_ctlDescription2->Create(" ", WS_VISIBLE | WS_CHILD | SS_LEFT,
							rect2, this, IDC_DESCRIPTIONSTATIC2);
		m_ctlDescription2->SetFont(m_startingFont, TRUE);

		m_DescriptionCreated = TRUE;
	
	}
}



/////////////////////////////////////////////////////
//This method destroys the Description box.
/////////////////////////////////////////////////////
void CStartup::DestroyDescriptionBox()
{

	if (m_DescriptionCreated)
	{
		delete m_ctlDescription;
		m_DescriptionCreated = FALSE;
		delete m_ctlDescription2;
	}
}

////////////////////////////////////////////////////////////////
//This method is executed when the user click on the Ok button.
////////////////////////////////////////////////////////////////
void CStartup::OnOK() 
{

	UpdateData(TRUE);

	CButton *pButton = (CButton *)GetDlgItem(IDC_SHOW_CHECK);
	if (pButton->GetCheck())
		controlService.SetApplicationData("ShowWizard", "No", "Dialogs\\Main");
	else
		controlService.SetApplicationData("ShowWizard", "Yes", "Dialogs\\Main");

	BOOL exitDialog;

	exitDialog = TRUE;

	//check that the user selected the required values
	switch (m_selectedCtlId)
	{
		case IDC_USEWIZARD:
			m_Units = m_ctlUnitsList->GetCurSel();
			if (m_Units == LB_ERR)
			{
				AfxMessageBox("Please select the Measurement System.");
				exitDialog = FALSE;
			}
			m_WMS = m_ctlWMSList->GetCurSel();
			if (m_WMS == LB_ERR)
			{
				AfxMessageBox("Please select the WMS.");
				exitDialog =  FALSE;
			}
			
			break;
		case IDC_NEWFACILITY:
			m_Units = m_ctlUnitsList->GetCurSel();
			if (m_Units == LB_ERR)
			{
				AfxMessageBox("Please select the Measurement System that you want to use.");
				exitDialog = FALSE;
			}
			m_WMS = m_ctlWMSList->GetCurSel();
			if (m_WMS == LB_ERR)
			{
				AfxMessageBox("Please select the Warehouse Management System to be used.");
				exitDialog =  FALSE;
			}
			
			break;
		case IDC_INSTRUCTIONS:
			{
				AfxMessageBox("Please select the mode you want to work with.");
				exitDialog = FALSE;
			}
			break;
		case IDC_SUCCEEDHELP:
			exitDialog = FALSE;
	}

	if (exitDialog) {
		g_ThreadDone.SetEvent();
		CDialog::OnOK();
	}
}




////////////////////////////////////////////////////////////
//This method is executed when the user presses the F1 key.
////////////////////////////////////////////////////////////
BOOL CStartup::OnHelpInfo(HELPINFO* pHelpInfo) 
{
	helpService.ShowFieldHelp(IDD, pHelpInfo->iCtrlId);

	return TRUE;
}

void CStartup::OnSucceedhelp() 
{
	m_ctlNewFacility.SetState(FALSE);
	m_ctlOpenFacility.SetState(FALSE);
	m_ctlInstructions.SetState(FALSE);
	m_ctlUseWizard.SetState(FALSE);
	m_ctlHelpStart.SetState(FALSE);

	DestroyInstructBox();
	CreateUnitsBox();
	CreateWMSBox();
	CreateDescriptionBox();

	//m_ctlUnitsList->EnableWindow(TRUE);
	m_ctlWMSList->EnableWindow(TRUE);

	helpService.ShowScreenHelp("What_is_EXceed_Optimize");
	


	m_selectedCtlId = IDC_SUCCEEDHELP;

	return;

}

void CStartup::OnDoubleclickedOpenfacility() 
{
	m_selectedCtlId = IDC_OPENFACILITY;
	OnOK();
}

void CStartup::OnDoubleclickedNewfacility() 
{
	m_selectedCtlId = IDC_NEWFACILITY;
	OnOK();
	
}

void CStartup::OnDoubleclickedUsewizard() 
{
	m_selectedCtlId = IDC_USEWIZARD;
	OnOK();
}

void CStartup::OnHelp() 
{
	helpService.ShowScreenHelp(IDD);
	
	return;
}


afx_msg LONG CStartup::OnAcadKeepFocus(UINT, LONG)
{
	return TRUE;
}


BOOL CStartup::PreTranslateMessage(MSG* pMsg) 
{
	if (WM_KEYFIRST <= pMsg->message && 
        pMsg->message <= WM_KEYLAST) { 
        HACCEL hAccel = m_hAccel; 
        if (hAccel && 
            ::TranslateAccelerator(m_hWnd, hAccel, pMsg)) 
            return TRUE; 
    }	
	return CDialog::PreTranslateMessage(pMsg);
}

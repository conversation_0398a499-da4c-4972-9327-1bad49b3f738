 // AssignSlotGroup.cpp : implementation file
//

#include "stdafx.h"
#include "TreeElement.h"
#include "AssignSlotGroup.h"
#include "UtilityHelper.h"
#include "ProductGroupDataService.h"
#include "HelpService.h"
#include "AutoCADCommands.h"
#include "ControlService.h"

#include <dbsymtb.h>



#ifdef _DEBUG
#define new DEBUG_NEW
#undef THIS_FILE
static char THIS_FILE[] = __FILE__;
#endif

extern CHelpService helpService;
extern TreeElement changesTree;
extern CUtilityHelper utilityHelper;
extern CControlService controlService;

/////////////////////////////////////////////////////////////////////////////
// CAssignSlotGroup dialog


//////////////////////////////////////////////////////////////////////
// Function Name : CAssignSlotGroup
// Classname : CAssignSlotGroup
// Description : 
// Date Created : 1st October 1998
// Author : ACS
//////////////////////////////////////////////////////////////////////
// Inputs : MFC
// Outputs : NONE
// Explanation : 
/////////////////////////////////////////////////////////////////////
// Revisions : 
//   Date-initials : 5th November 1998
//////////////////////////////////////////////////////////////////////
	/*******************************************************
	*
	*                       NOTICE
	*
	*  THIS SOFTWARE IS THE PROPERTY OF AND CONTAINS
	*  CONFIDENTIAL INFORMATION OF SSA GLOBAL 
	*  TECHNOLOGIES, INC., AND SHALL NOT BE COPIED,
	*  USED, NOR DISCLOSED WITHOUT EXPRESS WRITTEN
	*  AUTHORIZATION.  ALTHOUGH PUBLICATION IS NOT
	*  INTENDED, IN THE EVENT OF PUBLICATION, THE
	*  FOLLOWING NOTICE IS APPLICABLE:
	*
	*  (c) COPYRIGHT 2004 SSA GLOBAL TECHNOLOGIES, INC.
	*
	*           SSA GLOBAL TECHNOLOGIES, INC.
	*
	********************************************************/
//////////////////////////////////////////////////////////////////////

CAssignSlotGroup::CAssignSlotGroup(CWnd* pParent /*=NULL*/)
	: CDialog(CAssignSlotGroup::IDD, pParent)
{
	//{{AFX_DATA_INIT(CAssignSlotGroup)
	//}}AFX_DATA_INIT
}

//////////////////////////////////////////////////////////////////////
// Function Name : DoDataExchange
// Classname : CAssignSlotGroup
// Description : 
// Date Created : 1st October 1998
// Author : ACS
//////////////////////////////////////////////////////////////////////
// Inputs : MFC
// Outputs : NONE
// Explanation : 
/////////////////////////////////////////////////////////////////////
// Revisions : 
//   Date-initials : 5th November 1998
//////////////////////////////////////////////////////////////////////
	/*******************************************************
	*
	*                       NOTICE
	*
	*  THIS SOFTWARE IS THE PROPERTY OF AND CONTAINS
	*  CONFIDENTIAL INFORMATION OF SSA GLOBAL 
	*  TECHNOLOGIES, INC., AND SHALL NOT BE COPIED,
	*  USED, NOR DISCLOSED WITHOUT EXPRESS WRITTEN
	*  AUTHORIZATION.  ALTHOUGH PUBLICATION IS NOT
	*  INTENDED, IN THE EVENT OF PUBLICATION, THE
	*  FOLLOWING NOTICE IS APPLICABLE:
	*
	*  (c) COPYRIGHT 2004 SSA GLOBAL TECHNOLOGIES, INC.
	*
	*           SSA GLOBAL TECHNOLOGIES, INC.
	*
	********************************************************/
//////////////////////////////////////////////////////////////////////

void CAssignSlotGroup::DoDataExchange(CDataExchange* pDX)
{
	CDialog::DoDataExchange(pDX);
	//{{AFX_DATA_MAP(CAssignSlotGroup)
	DDX_Control(pDX, IDC_SLOTGROUPLIST, m_slotGroupList);
	//}}AFX_DATA_MAP
}


BEGIN_MESSAGE_MAP(CAssignSlotGroup, CDialog)
	//{{AFX_MSG_MAP(CAssignSlotGroup)
	ON_WM_HELPINFO()
	ON_BN_CLICKED(IDHELP, OnHelp)
	ON_LBN_DBLCLK(IDC_SLOTGROUPLIST, OnDblclkSlotgrouplist)
	//}}AFX_MSG_MAP
END_MESSAGE_MAP()

/////////////////////////////////////////////////////////////////////////////
// CAssignSlotGroup message handlers

//////////////////////////////////////////////////////////////////////
/////////////////////////////////////////////////////////////////////

BOOL CAssignSlotGroup::OnInitDialog() 
{
	CStringArray productGroupList;
	int rc, nItem;
	CProductGroupDataService dataService;
	CProductGroup group;

	CDialog::OnInitDialog();
	
	if (CAutoCADCommands::GetSelectedHandles(m_SelectedHandles) <= 0) {
		AfxMessageBox("Please select one or more bays before running this command.");
		EndDialog(IDCANCEL);
		return TRUE;
	}

	try {
		rc = dataService.GetProductGroups(controlService.GetCurrentFacilityDBId(), productGroupList);
	}
	catch (...) {
		rc = -1;
	}

	if (rc < 0) {
		AfxMessageBox("Unable to retrieve product groups from database.");
		return TRUE;
	}
	
	for (int i=0; i < productGroupList.GetSize(); ++i) {
		group.Parse(productGroupList[i]);
		nItem = m_slotGroupList.AddString(group.m_Description);
		m_slotGroupList.SetItemData(nItem, group.m_ProductGroupDBID);
	}

	return TRUE;  // return TRUE unless you set the focus to a control
	              // EXCEPTION: OCX Property Pages should return FALSE
}


void CAssignSlotGroup::OnOK() 
{
	TreeElement *bayPtr;
	qqhSLOTBay bay;
	CString temp;
	CStringArray handleList;
	CDWordArray levelIDList;
	CTypedPtrArray<CObArray, CProductGroupLevel*> groupLevelList;
	CProductGroupLevel *groupLevel;
	CProductGroupDataService dataService;
	BOOL foundNotSaved = FALSE;
	long groupID;
	int bayCount = 0;
	int curSel = m_slotGroupList.GetCurSel();

	if (curSel < 0) {
		AfxMessageBox("Please select a product group from the list.");
		return;
	}

	CWaitCursor cwc;

	groupID = m_slotGroupList.GetItemData(curSel);
	
	for (int i=0; i < m_SelectedHandles.GetSize(); ++i) {
		bayPtr = changesTree.getBayByHandle(m_SelectedHandles[i]);
		if (bayPtr == NULL) {
			temp.Format("Unable to find bay for handle: %s\n", m_SelectedHandles[i]);
			ads_printf(temp);
			continue;
		}
		
		if (bayPtr->elementDBID <= 0) {
			foundNotSaved = TRUE;
			continue;
		}

		levelIDList.RemoveAll();
		changesTree.getLevelDBIDsByBay(bayPtr, levelIDList);
		
		bayCount++;

		for (int j=0; j < levelIDList.GetSize(); ++j) {
			groupLevel = new CProductGroupLevel;
			groupLevel->m_BayDBID = bayPtr->elementDBID;
			groupLevel->m_LevelDBID = levelIDList[j];
			groupLevel->m_ProductGroupDBID = groupID;
			groupLevelList.Add(groupLevel);
		}
	}

	if (groupLevelList.GetSize() > 0) {
		try {
			dataService.StoreProductGroupLevel(groupLevelList);
		}
		catch (...) {
			utilityHelper.ProcessError("Error creating product group assignments.");
			for (int i=0; i < groupLevelList.GetSize(); ++i)
				delete groupLevelList[i];
			return;
		}
		temp.Format("%d bays, %d levels assigned to product group.", bayCount, groupLevelList.GetSize());
	}

	if (foundNotSaved) {
		if (groupLevelList.GetSize() == 0) {
			temp += "No bays were assigned.\n"
				"Please save the facility before attempting to assign new bays to a product group.";
		}
		else {
			temp += "\nSome bays could not be assigned.\n"
				"Please save the facility before attempting to assign new bays to a product group.";
		}
	}

	AfxMessageBox(temp);

	for (i=0; i < groupLevelList.GetSize(); ++i)
		delete groupLevelList[i];

	CDialog::OnOK();

}

BOOL CAssignSlotGroup::OnHelpInfo(HELPINFO* pHelpInfo) 
{
	helpService.ShowFieldHelp(IDD, pHelpInfo->iCtrlId);

	return FALSE;

}


void CAssignSlotGroup::OnHelp() 
{
	helpService.ShowScreenHelp(IDD);
	return;
	
}


void CAssignSlotGroup::OnDblclkSlotgrouplist() 
{
	OnOK();	
}

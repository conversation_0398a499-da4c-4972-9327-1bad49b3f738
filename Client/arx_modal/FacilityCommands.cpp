// FacilityCommands.cpp: implementation of the CFacilityCommands class.
//
//////////////////////////////////////////////////////////////////////

#include "stdafx.h"
#include "modal.h"
#include "FacilityCommands.h"
#include "ControlService.h"
#include "FacilityHelper.h"

#ifdef _DEBUG
#undef THIS_FILE
static char THIS_FILE[]=__FILE__;
#define new DEBUG_NEW
#endif

extern CControlService controlService;
//////////////////////////////////////////////////////////////////////
// Construction/Destruction
//////////////////////////////////////////////////////////////////////

CFacilityCommands::CFacilityCommands()
{

}

CFacilityCommands::~CFacilityCommands()
{

}

void CFacilityCommands::RegisterCommands()
{

	// Facility
	acedRegCmds->addCommand("SLOTGEN", "OPENDRAWING", "OPENDRAWING",
		ACRX_CMD_MODAL, &CFacilityCommands::OpenDrawing);
	acedRegCmds->addCommand("SLOTGEN", "OPENFACILITY", "OPENFACILITY",
		ACRX_CMD_MODAL, &CFacilityCommands::OpenFacility);
	acedRegCmds->addCommand("SLOTGEN", "NEWFACILITY", "NEWFACILITY",
		ACRX_CMD_MODAL, &CFacilityCommands::NewFacility);
	acedRegCmds->addCommand( "SLOTGEN", "FACSAVE", "FACSAVE",
		ACRX_CMD_MODAL, &CFacilityCommands::SaveFacility );
	acedRegCmds->addCommand( "SLOTGEN", "FACSAVEAS", "FACSAVEAS",
		ACRX_CMD_MODAL, &CFacilityCommands::SaveAsFacility );
	acedRegCmds->addCommand( "SLOTGEN", "FACDELETE", "FACDELETE",
		ACRX_CMD_MODAL, &CFacilityCommands::DeleteFacility );
	acedRegCmds->addCommand( "SLOTGEN", "FACEXPORT", "FACEXPORT",
		ACRX_CMD_MODAL, &CFacilityCommands::ExportFacility );
	acedRegCmds->addCommand( "SLOTGEN", "FACIMPORT", "FACIMPORT",
		ACRX_CMD_MODAL, &CFacilityCommands::ImportFacility );
	acedRegCmds->addCommand( "SLOTGEN", "FACOPEN", "FACOPEN",
		ACRX_CMD_MODAL, &CFacilityCommands::OpenFacility );
	acedRegCmds->addCommand( "SLOTGEN", "FACNEW", "FACNEW",
		ACRX_CMD_MODAL, &CFacilityCommands::NewFacility );
	acedRegCmds->addCommand( "SLOTGEN", "DOFORTESAVE", "DOFORTESAVE",
		ACRX_CMD_MODAL, &CFacilityCommands::DoForteSave);
	acedRegCmds->addCommand( "SLOTGEN", "CONVERTDRAWING", "CONVERTDRAWING",
		ACRX_CMD_MODAL, &CFacilityCommands::ConvertDrawing);
}


void CFacilityCommands::OpenDrawing()
{
	if (! controlService.ValidateCommand(CCommands::anyMode))
		return;

	CFacilityHelper facHelper;

	facHelper.OpenDrawing();


	return;

}


void CFacilityCommands::OpenFacility()
{
	if (! controlService.ValidateCommand(CCommands::anyMode))
		return;

	CFacilityHelper facHelper;

	facHelper.OpenFacility();

	return;


}


void CFacilityCommands::NewFacility()
{
	if (! controlService.ValidateCommand(CCommands::anyMode))
		return;

	CFacilityHelper facHelper;
	
	facHelper.NewFacility();
	
	return;
}


void CFacilityCommands::SaveFacility()
{
	if (! controlService.ValidateCommand(CCommands::newFacilityMode))
		return;

	CFacilityHelper facHelper;

	facHelper.SaveFacility();

	return;
}


void CFacilityCommands::SaveAsFacility()
{
	if (! controlService.ValidateCommand(CCommands::newFacilityMode))
		return;

	CFacilityHelper facHelper;

	facHelper.SaveAsFacility();

	return;

}

void CFacilityCommands::DeleteFacility()
{
	if (! controlService.ValidateCommand(CCommands::anyMode))
		return;

	CFacilityHelper facHelper;

	facHelper.DeleteFacility();

	return;
}


void CFacilityCommands::ExportFacility()
{
	if (! controlService.ValidateCommand(CCommands::anyMode))
		return;

	CFacilityHelper facHelper;

	facHelper.ExportFacility();

	return;

}

void CFacilityCommands::ImportFacility()
{
	if (! controlService.ValidateCommand(CCommands::anyMode))
		return;

	CFacilityHelper facHelper;
	
	facHelper.ImportFacility();
	
	return;
}


void CFacilityCommands::DoForteSave()
{
	if (! controlService.ValidateCommand(CCommands::newFacilityMode))
		return;

	CFacilityHelper facHelper;

	facHelper.DoForteSave();

	return;
}

void CFacilityCommands::ConvertDrawing()
{
	if (! controlService.ValidateCommand(CCommands::anyMode))
		return;

	CFacilityHelper facHelper;

	facHelper.ConvertDrawing();


	return;

}
// ColorButton.cpp : implementation file
//

#include "stdafx.h"
#include "modal.h"
#include "ColorButton.h"

#ifdef _DEBUG
#define new DEBUG_NEW
#undef THIS_FILE
static char THIS_FILE[] = __FILE__;
#endif

/////////////////////////////////////////////////////////////////////////////
// CColorButton

CMyColorButton::CMyColorButton()
{
}

CMyColorButton::~CMyColorButton()
{
}


BEGIN_MESSAGE_MAP(CMyColorButton, CButton)
	//{{AFX_MSG_MAP(CMyColorButton)
		// NOTE - the ClassWizard will add and remove mapping macros here.
	//}}AFX_MSG_MAP
END_MESSAGE_MAP()

/////////////////////////////////////////////////////////////////////////////
// CColorButton message handlers

void CMyColorButton::DrawItem(LPDRAWITEMSTRUCT lpDrawItemStruct) 
{
	CDC  dc;
	dc.Attach(lpDrawItemStruct->hDC);

	CBrush* pTempBrush = NULL;
	CBrush OrigBrush, br;
	CString displayText = m_pColorObject->text;

	br.CreateSolidBrush(RGB(m_pColorObject->red, m_pColorObject->green, m_pColorObject->blue));
	pTempBrush = (CBrush*)dc.SelectObject(br);

	// Save original brush.
	OrigBrush.FromHandle((HBRUSH)pTempBrush);

	UINT uStyle = DFCS_BUTTONPUSH;
	::DrawFrameControl(dc.m_hDC, &lpDrawItemStruct->rcItem, 
		DFC_BUTTON, uStyle);

	dc.Rectangle(&lpDrawItemStruct->rcItem);

	dc.SetBkColor(RGB(m_pColorObject->red, m_pColorObject->green, m_pColorObject->blue));

	COLORREF crOldColor = ::SetTextColor(lpDrawItemStruct->hDC, RGB(0, 0, 0));
   
	::DrawText(lpDrawItemStruct->hDC, displayText, displayText.GetLength(), 
      &lpDrawItemStruct->rcItem, DT_SINGLELINE|DT_VCENTER|DT_CENTER);

   ::SetTextColor(lpDrawItemStruct->hDC, crOldColor);


	dc.SelectObject(&OrigBrush);
	dc.Detach();
}

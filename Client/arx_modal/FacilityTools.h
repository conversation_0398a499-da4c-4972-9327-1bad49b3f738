#if !defined(AFX_FACILITYTOOLS_H__FA64C827_A659_48CF_881D_11B260609018__INCLUDED_)
#define AFX_FACILITYTOOLS_H__FA64C827_A659_48CF_881D_11B260609018__INCLUDED_

#if _MSC_VER > 1000
#pragma once
#endif // _MSC_VER > 1000
// FacilityTools.h : header file
//

#include <adscodes.h>
#include <aced.h>
#include <actrans.h>
/////////////////////////////////////////////////////////////////////////////
// CFacilityTools dialog

class CFacilityTools : public CDialog
{
// Construction
public:
	CFacilityTools(CWnd* pParent = NULL);   // standard constructor
	typedef struct {
		int dbid;
		CString description;
		CString strSortKey;
		int intSortKey;
	} itemStruct;

// Dialog Data
	//{{AFX_DATA(CFacilityTools)
	enum { IDD = IDD_FACILITY_TOOLS };
	CTreeCtrl	m_TreeCtrl;
	//}}AFX_DATA


// Overrides
	// ClassWizard generated virtual function overrides
	//{{AFX_VIRTUAL(CFacilityTools)
	protected:
	virtual void DoDataExchange(CDataExchange* pDX);    // DDX/DDV support
	//}}AFX_VIRTUAL

// Implementation
protected:

	// Generated message map functions
	//{{AFX_MSG(CFacilityTools)
	virtual BOOL OnInitDialog();
	afx_msg void OnColor();
	afx_msg void OnProperties();
	afx_msg void OnDelete();
	afx_msg void OnHelp();
	afx_msg BOOL OnHelpInfo(HELPINFO* pHelpInfo);
	afx_msg void OnCollapse();
	afx_msg void OnItemexpandingFacilityTree(NMHDR* pNMHDR, LRESULT* pResult);
	afx_msg void OnZoom();
	virtual void OnCancel();
	afx_msg void OnDblclkFacilityTree(NMHDR* pNMHDR, LRESULT* pResult);
	//}}AFX_MSG
	DECLARE_MESSAGE_MAP()
private:
	BOOL m_SaveNeeded;
	int DelLocation(HTREEITEM hItem);
	int DelLevel(HTREEITEM hItem);
	int DelBay(HTREEITEM hItem);
	int DelSide(HTREEITEM hItem);
	int DelAisle(HTREEITEM hItem);
	int DelSection(HTREEITEM hItem);

	void GetSectionExtents(HTREEITEM hItem, ads_point &point1, ads_point &point2);
	void GetAisleExtents(HTREEITEM hItem, ads_point &point1, ads_point &point2);
	void GetSideExtents(HTREEITEM hItem, ads_point &point1, ads_point &point2);
	void GetBayExtents(HTREEITEM hItem, ads_point &point1, ads_point &point2);

	void GetHandlesByFacility(HTREEITEM hItem, CStringArray &handles);
	void GetHandlesBySection(HTREEITEM hItem, CStringArray &handles, BOOL bIncludePickPath);
	void GetHandlesByAisle(HTREEITEM hItem, CStringArray &handles, BOOL bIncludePickPath);
	void GetHandlesBySide(HTREEITEM hItem, CStringArray &handles);
	void GetHandlesByBay(HTREEITEM hItem, CStringArray &handles);
	void GetHandlesByLevel(HTREEITEM hItem, CStringArray &handles);
	void GetHandlesByLocation(HTREEITEM hItem, CStringArray &handles);

	void SortListByString(CList<itemStruct*, itemStruct*> &list);
	void SortListByInt(CList<itemStruct*, itemStruct*> &list);
	static int CompareStrings(const void **p1, const void **p2);
	static int CompareInts(const void **p1, const void **p2);

	void AddAislesBySection(HTREEITEM &hItem);
	void AddSidesByAisle(HTREEITEM &hItem);
	void AddBaysBySide(HTREEITEM &hItem);
	void AddLevelsByBay(HTREEITEM &hItem);
	void AddLocationsByLevel(HTREEITEM &hItem);

	void UpdateLocationsInTree(int type, HTREEITEM &hItem, CString &sectionMask, CString &newPart);
	void ViewFacilityProperties();
	void ViewSectionProperties();
	void ViewAisleProperties();
	void ViewSideProperties();
	void ViewBayProperties();
	void ViewLevelProperties();
	void ViewLocationProperties();

	int GetItemType(HTREEITEM &hItem);
	void LoadTree();
};

//{{AFX_INSERT_LOCATION}}
// Microsoft Visual C++ will insert additional declarations immediately before the previous line.

#endif // !defined(AFX_FACILITYTOOLS_H__FA64C827_A659_48CF_881D_11B260609018__INCLUDED_)

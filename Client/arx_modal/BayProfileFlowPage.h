#if !defined(AFX_BAYPROFILEFLOWPAGE_H__6F67D5EE_057E_43DC_B257_0F4A0DAA9F99__INCLUDED_)
#define AFX_BAYPROFILEFLOWPAGE_H__6F67D5EE_057E_43DC_B257_0F4A0DAA9F99__INCLUDED_

#if _MSC_VER > 1000
#pragma once
#endif // _MSC_VER > 1000
// BayProfileFlowPage.h : header file
//
#include "BayProfileTopViewButton.h"
#include "BayProfileSideViewButton.h"
#include "BayProfile.h"
/////////////////////////////////////////////////////////////////////////////
// CBayProfileFlowPage dialog

class CBayProfileFlowPage : public CPropertyPage
{
	DECLARE_DYNCREATE(CBayProfileFlowPage)

// Construction
public:
	CBayProfileFlowPage();
	~CBayProfileFlowPage();

// Dialog Data
	//{{AFX_DATA(CBayProfileFlowPage)
	enum { IDD = IDD_BAY_PROFILE_FLOW_ATTRIBUTES };
	CBayProfileTopViewButton	m_TopViewButton;
	CBayProfileSideViewButton	m_SideViewButton;
	double	m_BayDepth;
	double	m_BayWidth;
	double	m_FlowDifference;
	double	m_BayHeight;
	double	m_UprightHeight;
	double	m_UprightWidth;
	double	m_WeightCapacity;
	double  m_StackDepth;
	double  m_StackHeight;
	double  m_StackWidth;
	//}}AFX_DATA


// Overrides
	// ClassWizard generate virtual function overrides
	//{{AFX_VIRTUAL(CBayProfileFlowPage)
	public:
	virtual BOOL OnKillActive();
	virtual BOOL OnSetActive();
	protected:
	virtual void DoDataExchange(CDataExchange* pDX);    // DDX/DDV support
	virtual BOOL OnCommand(WPARAM wParam, LPARAM lParam);
	virtual BOOL OnNotify(WPARAM wParam, LPARAM lParam, LRESULT* pResult);
	//}}AFX_VIRTUAL

// Implementation
protected:
	// Generated message map functions
	//{{AFX_MSG(CBayProfileFlowPage)
	virtual BOOL OnInitDialog();
	afx_msg BOOL OnHelpInfo(HELPINFO* pHelpInfo);
	//}}AFX_MSG
	DECLARE_MESSAGE_MAP()

private:
	BOOL Validate();
	CBayProfile *m_pBayProfile;
	BOOL m_Validating;
};

//{{AFX_INSERT_LOCATION}}
// Microsoft Visual C++ will insert additional declarations immediately before the previous line.

#endif // !defined(AFX_BAYPROFILEFLOWPAGE_H__6F67D5EE_057E_43DC_B257_0F4A0DAA9F99__INCLUDED_)

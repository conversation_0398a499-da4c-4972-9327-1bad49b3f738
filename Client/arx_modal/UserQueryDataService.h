// UserQueryDataService.h: interface for the CUserQueryDataService class.
//
//////////////////////////////////////////////////////////////////////

#if !defined(AFX_USERQUERYDATASERVICE_H__EC43173B_D2C5_4816_8520_E2D4D5FFD8F0__INCLUDED_)
#define AFX_USERQUERYDATASERVICE_H__EC43173B_D2C5_4816_8520_E2D4D5FFD8F0__INCLUDED_

#if _MSC_VER > 1000
#pragma once
#endif // _MSC_VER > 1000

#include "UserQuery.h"

class CUserQueryDataService  
{
public:
	int RunUserStatement(CString &name, CString &query);
	CUserQueryDataService();
	virtual ~CUserQueryDataService();

	int GetUserQueryList(CStringArray &queryList);
	int StoreUserQuery(CUserQuery &query);
	int RunUserQuery(CString &query, CSsaStringArray &queryResults);
	int DeleteUserQuery(int queryID);
};

#endif // !defined(AFX_USERQUERYDATASERVICE_H__EC43173B_D2C5_4816_8520_E2D4D5FFD8F0__INCLUDED_)

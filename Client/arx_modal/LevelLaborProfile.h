// LevelLaborProfile.h: interface for the CLevelLaborProfile class.
//
//////////////////////////////////////////////////////////////////////

#if !defined(AFX_LEVELLABORPROFILE_H__366710D8_C778_4120_8801_376D72FCC455__INCLUDED_)
#define AFX_LEVELLABORPROFILE_H__366710D8_C778_4120_8801_376D72FCC455__INCLUDED_

#if _MSC_VER > 1000
#pragma once
#endif // _MSC_VER > 1000

class CLevelLaborProfile : public CObject  
{
public:
	CLevelLaborProfile();
	CLevelLaborProfile(const CLevelLaborProfile& other);
	CLevelLaborProfile& operator=(const CLevelLaborProfile &other);
	BOOL operator==(const CLevelLaborProfile& other);
	BOOL operator!=(const CLevelLaborProfile& other) { return !(*this == other);};
	virtual ~CLevelLaborProfile();

	int Parse(CString &line);

	int m_LevelLaborProfileDBId;
	CString m_Description;
	double m_Cube;
	double m_FixedFactor;
	double m_VariableFactor;
	int m_WorkType;
	int m_LevelProfileDBId;

	typedef enum {
		workTypeSelection = 0,
		workTypeStocker = 1
	} workTypeEnum;
};

#endif // !defined(AFX_LEVELLABORPROFILE_H__366710D8_C778_4120_8801_376D72FCC455__INCLUDED_)

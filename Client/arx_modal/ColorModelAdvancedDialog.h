#if !defined(AFX_COLORMODELADVANCEDDIALOG_H__58551C25_8D5D_410A_92DE_E1D3B86923B9__INCLUDED_)
#define AFX_COLORMODELADVANCEDDIALOG_H__58551C25_8D5D_410A_92DE_E1D3B86923B9__INCLUDED_

#if _MSC_VER > 1000
#pragma once
#endif // _MSC_VER > 1000
// ColorModelAdvancedDialog.h : header file
//

/////////////////////////////////////////////////////////////////////////////
// CColorModelAdvancedDialog dialog

class CColorModelAdvancedDialog : public CDialog
{
// Construction
public:
	int m_MultiType;
	CColorModelAdvancedDialog(CWnd* pParent = NULL);   // standard constructor

// Dialog Data
	//{{AFX_DATA(CColorModelAdvancedDialog)
	enum { IDD = IDD_COLOR_MODEL_ADVANCED };
	CString	m_Level;
	CString	m_MaxHeight;
	CString	m_MinHeight;
	//}}AFX_DATA


// Overrides
	// ClassWizard generated virtual function overrides
	//{{AFX_VIRTUAL(CColorModelAdvancedDialog)
	protected:
	virtual void DoDataExchange(CDataExchange* pDX);    // DDX/DDV support
	//}}AFX_VIRTUAL

// Implementation
protected:

	// Generated message map functions
	//{{AFX_MSG(CColorModelAdvancedDialog)
	virtual void OnOK();
	virtual BOOL OnInitDialog();
	afx_msg BOOL OnHelpInfo(HELPINFO* pHelpInfo);
	afx_msg void OnHelp();
	//}}AFX_MSG
	DECLARE_MESSAGE_MAP()
};

//{{AFX_INSERT_LOCATION}}
// Microsoft Visual C++ will insert additional declarations immediately before the previous line.

#endif // !defined(AFX_COLORMODELADVANCEDDIALOG_H__58551C25_8D5D_410A_92DE_E1D3B86923B9__INCLUDED_)

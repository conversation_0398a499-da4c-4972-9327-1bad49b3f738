// ProductGroupCommands.cpp: implementation of the CProductGroupCommands class.
//
//////////////////////////////////////////////////////////////////////

#include "stdafx.h"
#include "modal.h"
#include "ProductGroupCommands.h"
#include "ProductGroupHelper.h"

#ifdef _DEBUG
#undef THIS_FILE
static char THIS_FILE[]=__FILE__;
#define new DEBUG_NEW
#endif

//////////////////////////////////////////////////////////////////////
// Construction/Destruction
//////////////////////////////////////////////////////////////////////

CProductGroupCommands::CProductGroupCommands()
{

}

CProductGroupCommands::~CProductGroupCommands()
{

}

void CProductGroupCommands::RegisterCommands()
{
	// Product Group
	acedRegCmds->addCommand( "SLOTJAVA", "SLOTGROUPQUERY", "SLOTGROUPQUERY",
		ACRX_CMD_MODAL, &CProductGroupCommands::ProductGroupMaintenance );
	acedRegCmds->addCommand("SLOTJAVA", "PRODUCTGROUPMAINTENANCE", "PRODUCTGROUPMAINTENANCE",
		ACRX_CMD_MODAL, &CProductGroupCommands::ProductGroupMaintenance);
	acedRegCmds->addCommand("SLOTJAVA", "PGM", "PGM",
		ACRX_CMD_MODAL, &CProductGroupCommands::ProductGroupMaintenance);
}


void CProductGroupCommands::ProductGroupMaintenance()
{
	CProductGroupHelper helper;

	helper.ProductGroupMaintenance();

	return;

}
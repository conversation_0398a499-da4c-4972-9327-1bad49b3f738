// WMSImportPage.cpp : implementation file
//

#include "stdafx.h"
#include "modal.h"
#include "WMSImportPage.h"
#include "WMSSheet.h"
#include "UtilityHelper.h"
#include "FacilityDataService.h"
#include "IntegrationDataService.h"
#include "ControlService.h"
#include "HelpService.h"

#include <dbsymtb.h>

#ifdef _DEBUG
#define new DEBUG_NEW
#undef THIS_FILE
static char THIS_FILE[] = __FILE__;
#endif

extern CUtilityHelper utilityHelper;
extern CFacilityDataService facilityDataService;
extern CIntegrationDataService integrationDataService;
extern CControlService controlService;
extern CHelpService helpService;
/////////////////////////////////////////////////////////////////////////////
// CWMSImportPage property page

IMPLEMENT_DYNCREATE(CWMSImportPage, CPropertyPage)

CWMSImportPage::CWMSImportPage() : CPropertyPage(CWMSImportPage::IDD)
{
	//{{AFX_DATA_INIT(CWMSImportPage)
	//}}AFX_DATA_INIT
	m_hDragItem = NULL;
	m_hDropItem = NULL;
	m_pDragImageList = NULL;
	m_bDraggingWMS = FALSE;
	m_bDraggingSection = FALSE;
	m_nDelayInterval = 500;     // Default delay interval = 500 milliseconds
	m_nScrollInterval = 200;    // Default scroll interval = 200 milliseconds
	m_nScrollMargin = 10;       // Default scroll margin = 10 pixels
}

CWMSImportPage::~CWMSImportPage()
{
	for (int i=0; i < m_ImportMapList.GetSize(); ++i)
		delete m_ImportMapList[i];
}

void CWMSImportPage::DoDataExchange(CDataExchange* pDX)
{
	CPropertyPage::DoDataExchange(pDX);
	//{{AFX_DATA_MAP(CWMSImportPage)
	DDX_Control(pDX, IDC_WMS_GROUP_TREE, m_WMSTreeCtrl);
	DDX_Control(pDX, IDC_FACILITY_MAP_TREE, m_FacilityTreeCtrl);
	//}}AFX_DATA_MAP
}


BEGIN_MESSAGE_MAP(CWMSImportPage, CPropertyPage)
	//{{AFX_MSG_MAP(CWMSImportPage)
	ON_BN_CLICKED(IDC_ASSIGN, OnAssign)
	ON_BN_CLICKED(IDC_REMOVE, OnRemove)
	ON_NOTIFY(TVN_BEGINDRAG, IDC_WMS_GROUP_TREE, OnBegindragWmsGroupTree)
	ON_WM_MOUSEMOVE()
	ON_WM_LBUTTONUP()
	ON_WM_TIMER()
	ON_NOTIFY(TVN_BEGINDRAG, IDC_FACILITY_MAP_TREE, OnBegindragFacilityMapTree)
	ON_WM_HELPINFO()
	//}}AFX_MSG_MAP
END_MESSAGE_MAP()

/////////////////////////////////////////////////////////////////////////////
// CWMSImportPage message handlers

BOOL CWMSImportPage::OnInitDialog() 
{
	CPropertyPage::OnInitDialog();

	m_FacilityImageList.Create(16, 16, TRUE, 4, 2);
	m_FacilityImageList.Add(AfxGetApp()->LoadIcon(IDI_FACILITY));
	m_FacilityImageList.Add(AfxGetApp()->LoadIcon(IDI_FACILITY_INVERSE));
	m_FacilityImageList.Add(AfxGetApp()->LoadIcon(IDI_SECTION));
	m_FacilityImageList.Add(AfxGetApp()->LoadIcon(IDI_SECTION_INVERSE));
	m_FacilityTreeCtrl.SetImageList(&m_FacilityImageList, TVSIL_NORMAL);
	
	LoadFacilityList();
	LoadFacilityTree();

	m_WMSImageList.Create(16, 16, TRUE, 2, 2);
	m_WMSImageList.Add(AfxGetApp()->LoadIcon(IDI_BOX));
	m_WMSImageList.Add(AfxGetApp()->LoadIcon(IDI_FORKLIFT2));
	m_WMSTreeCtrl.SetImageList(&m_WMSImageList, TVSIL_NORMAL);
	
	LoadWMSList();
	LoadWMSTree();

	LoadMapList();
	UpdateMapTree();
	
	return TRUE;  // return TRUE unless you set the focus to a control
	              // EXCEPTION: OCX Property Pages should return FALSE
}

int CWMSImportPage::LoadFacilityTree()
{

	HTREEITEM hFacItem, hSectionItem;

	CWMSSheet *pParent = (CWMSSheet *)GetParent();

	CTypedPtrArray<CObArray, CWMSFacilityInfo*> &facilityList = pParent->m_FacilityList;

	for (int i=0; i < facilityList.GetSize(); ++i) {
		CWMSFacilityInfo *pFacInfo = facilityList[i];

		hFacItem = m_FacilityTreeCtrl.InsertItem(pFacInfo->m_FacilityName, 0, 0, TVI_ROOT, TVI_LAST);
		m_FacilityTreeCtrl.SetItemData(hFacItem, (unsigned long)pFacInfo);

		for (int j=0; j < pFacInfo->m_SectionDBIdList.GetSize(); ++j) {
			hSectionItem = 
				m_FacilityTreeCtrl.InsertItem(pFacInfo->m_SectionNameList[j], 2, 2, hFacItem, TVI_LAST);
			m_FacilityTreeCtrl.SetItemData(hSectionItem, pFacInfo->m_SectionDBIdList[j]);
			m_MapSectionToTree.SetAt(pFacInfo->m_SectionDBIdList[j], hSectionItem);
			m_MapTreeToSection.SetAt(hSectionItem, pFacInfo->m_SectionDBIdList[j]);
		}
	}


	return 0;

}

int CWMSImportPage::LoadFacilityList()
{

	CWMSSheet *pParent = (CWMSSheet *)GetParent();

	pParent->LoadFacilityList();


	return 0;
}

int CWMSImportPage::LoadWMSList()
{
	CWMSSheet *pParent = (CWMSSheet *)GetParent();

	return pParent->LoadWMSList();

}

int CWMSImportPage::LoadWMSTree()
{
	CWMSSheet *pParent = (CWMSSheet *)GetParent();
	CTypedPtrArray<CObArray, CWMSGroup*> &groupList = pParent->m_GroupList;

	HTREEITEM hGroupItem;

	for (int i=0; i < groupList.GetSize(); ++i) {
		CWMSGroup *pGroup = groupList[i];
		hGroupItem = AddGroupToTree(pGroup);
		for (int j=0; j < pGroup->m_WMSList.GetSize(); ++j) {
			CWMS *pWMS = pGroup->m_WMSList[j];
			AddWMSToTree(pWMS, hGroupItem);
		}
	}

	return 0;

}

HTREEITEM CWMSImportPage::AddGroupToTree(CWMSGroup *pGroup)
{
	HTREEITEM hNewItem;

	hNewItem = m_WMSTreeCtrl.InsertItem(pGroup->m_Name, 0, 0, TVI_ROOT, TVI_LAST);
	m_WMSTreeCtrl.SetItemData(hNewItem, (unsigned long)pGroup);
	m_MapTreeToGroup.SetAt(hNewItem, pGroup->m_WMSGroupDBId);
	
	return hNewItem;
}

HTREEITEM CWMSImportPage::AddWMSToTree(CWMS *pWMS, HTREEITEM hItem)
{
	HTREEITEM hNewItem;

	hNewItem = m_WMSTreeCtrl.InsertItem(pWMS->m_Name, 1, 1, hItem, TVI_LAST);
	m_WMSTreeCtrl.SetItemData(hNewItem, (unsigned long)pWMS);
	
	m_MapWMSToTree.SetAt(pWMS->m_WMSDBId, hNewItem);

	return hNewItem;
}

int CWMSImportPage::LoadMapList()
{
	CStringArray mapList;

	try {
		integrationDataService.GetWMSImportMap(0, mapList);
	}
	catch (...) {
		utilityHelper.ProcessError("Error loading import map.");
		return -1;
	}

	for (int i=0; i < mapList.GetSize(); ++i) {
		CWMSMap *pMap = new CWMSMap;
		pMap->Parse(mapList[i]);
		m_ImportMapList.Add(pMap);
	}

	return 0;

}

CWMSImportPage::UpdateMapTree()
{
	for (int i=0; i < m_ImportMapList.GetSize(); ++i) {
		CWMSMap *pMap = m_ImportMapList[i];
		UpdateMapTreeItem(pMap);
	}

	return 0;
}

int CWMSImportPage::UpdateMapTreeItem(CWMSMap *pMap)
{
	CWMSSheet *pParent = (CWMSSheet *)GetParent();
	
	HTREEITEM hItem;
	if (pMap->m_SectionDBId > 0) {
		if (m_MapSectionToTree.Lookup(pMap->m_SectionDBId, hItem)) {	
			UpdateFacilityTreeText(hItem, pMap->m_SectionName, pMap->m_WMSName,
				pParent->GetWMSGroupName(pMap->m_WMSDBId));
		}
	}
	else {
		HTREEITEM hItem = m_FacilityTreeCtrl.GetNextItem(TVI_ROOT, TVGN_CHILD);
		while (hItem != NULL) {
			CWMSFacilityInfo *pFacInfo = (CWMSFacilityInfo *)m_FacilityTreeCtrl.GetItemData(hItem);
			if (pFacInfo->m_FacilityDBId == pMap->m_FacilityDBId) {
					UpdateFacilityTreeText(hItem, pMap->m_FacilityName, pMap->m_WMSName,
						pParent->GetWMSGroupName(pMap->m_WMSDBId));

				break;
			}
			HTREEITEM hNextFacItem = m_FacilityTreeCtrl.GetNextItem(hItem, TVGN_NEXT);
			hItem = hNextFacItem;
		}
	}

	return 0;

}

void CWMSImportPage::OnAssign() 
{
	HTREEITEM hWMSItem = m_WMSTreeCtrl.GetSelectedItem();
	if (hWMSItem == NULL) {
		AfxMessageBox("Please select a WMS section to connect.");
		return;
	}

	HTREEITEM hFacItem = m_FacilityTreeCtrl.GetSelectedItem();
	if (hFacItem == NULL) {
		AfxMessageBox("Please select an Optimize facility or section to connect.");
		return;
	}

	HTREEITEM hWMSParentItem = m_WMSTreeCtrl.GetParentItem(hWMSItem);
	if (hWMSParentItem == NULL) {		// selecting an entire group
		AfxMessageBox("Only one WMS section can be connected to an Optimize section.");
		return;
	}
	
	HTREEITEM hFacParentItem = m_FacilityTreeCtrl.GetParentItem(hFacItem);
	if (hFacParentItem == NULL && hWMSParentItem == NULL) {
		AfxMessageBox("Each Optimize section can have only one WMS section. "
			"Choose either a single Optimize section or facility and a single WMS section to connect.");
		return;
	}
	
	if (hFacParentItem == NULL) {
		if (AfxMessageBox("Do you wish to make this the default WMS section for all future Optimize sections in the facility?", MB_YESNO) != IDYES)
			return;
	}

	//CTypedPtrArray<CObArray, CWMS*> wmsList;
	//if (hWMSParentItem != NULL)
		//wmsList.Add((CWMS *)m_WMSTreeCtrl.GetItemData(hWMSItem));
	CWMS *pWMS = (CWMS *)m_WMSTreeCtrl.GetItemData(hWMSItem);
	/*
	else {
		HTREEITEM hItem = m_WMSTreeCtrl.GetNextItem(hWMSParentItem, TVGN_CHILD);
		while (hItem != NULL) {
			wmsList.Add((CWMS *)m_WMSTreeCtrl.GetItemData(hItem));
			HTREEITEM hNextItem = m_WMSTreeCtrl.GetNextItem(hItem, TVGN_NEXT);
			hItem = hNextItem;
		}
	}
	*/
	/*
	if (wmsList.GetSize() == 0) {
		AfxMessageBox("Please select at least one WMS to connect.");
		return;
	}
	*/
	//CArray<int, int> sectionList;
	int sectionDBId;
	int facilityDBId;

	if (hFacParentItem != NULL)	{			// Single section selected
		if (! m_MapTreeToSection.Lookup(hFacItem, sectionDBId))
			//sectionList.Add(sectionDBId);
			sectionDBId = 0;

		CWMSFacilityInfo *pFacInfo = (CWMSFacilityInfo *)m_FacilityTreeCtrl.GetItemData(hFacParentItem);
		facilityDBId = pFacInfo->m_FacilityDBId;
	}
	else {									// entire facility selected
		//sectionList.Add(0);
		sectionDBId = 0;
		CWMSFacilityInfo *pFacInfo = (CWMSFacilityInfo *)m_FacilityTreeCtrl.GetItemData(hFacItem);
		facilityDBId = pFacInfo->m_FacilityDBId;

		// Now if they choose a facility, we assign all current and future sections
		/*
		HTREEITEM hItem = m_FacilityTreeCtrl.GetNextItem(hFacItem, TVGN_CHILD);
		while (hItem != NULL) {
			CString temp = m_FacilityTreeCtrl.GetItemText(hItem);
			if (m_MapTreeToSection.Lookup(hItem, sectionDBId))
				sectionList.Add(sectionDBId);
			HTREEITEM hNextItem = m_FacilityTreeCtrl.GetNextItem(hItem, TVGN_NEXT);
			hItem = hNextItem;
		}
		*/
	}

	Assign(pWMS, facilityDBId, sectionDBId);

	/*
	// Now we have a list of the facility items and the WMS items one of which is single
	for (int i=0; i < wmsList.GetSize(); ++i) {
		for (int j=0; j < sectionList.GetSize(); ++j) {
			Assign(wmsList[i], facilityDBId, sectionDBId);
		}
	}
	*/

	return;


}

void CWMSImportPage::OnRemove() 
{
	HTREEITEM hFacItem = m_FacilityTreeCtrl.GetSelectedItem();
	if (hFacItem == NULL) {
		AfxMessageBox("Please select a facility or section to disconnect.");
		return;
	}
	
	HTREEITEM hFacParentItem = m_FacilityTreeCtrl.GetParentItem(hFacItem);
	
	CArray<int, int> sectionList;
	int facilityDBId, sectionDBId;

	if (hFacParentItem != NULL)	{			// Single section selected
		if (m_MapTreeToSection.Lookup(hFacItem, sectionDBId)) {
			CWMSFacilityInfo *pFacInfo = (CWMSFacilityInfo *)m_FacilityTreeCtrl.GetItemData(hFacParentItem);
			facilityDBId = pFacInfo->m_FacilityDBId;
		}
	}
	else {									// entire facility selected
		CWMSFacilityInfo *pFacInfo = (CWMSFacilityInfo *)m_FacilityTreeCtrl.GetItemData(hFacItem);
		facilityDBId = pFacInfo->m_FacilityDBId;
		sectionDBId = 0;
		/*
		HTREEITEM hItem = m_FacilityTreeCtrl.GetNextItem(hFacItem, TVGN_CHILD);
		while (hItem != NULL) {
			if (m_MapTreeToSection.Lookup(hItem, sectionDBId))
				sectionList.Add(sectionDBId);
			HTREEITEM hNextItem = m_FacilityTreeCtrl.GetNextItem(hItem, TVGN_NEXT);
			hItem = hNextItem;
		}
		*/
	}

	Remove(facilityDBId, sectionDBId);
	/*
	// Now we have a list of all the sections to remove
	for (int i=0; i < sectionList.GetSize(); ++i)
		Remove(facilityDBId, sectionList[i]);
	*/
}

int CWMSImportPage::Assign(CWMS *pWMS, int facilityDBId, int sectionDBId)
{

	CWaitCursor cwc;

	// handle case where section already assigned to a WMS
	CWMSMap *pMap = NULL;
	int oldIdx = -1;
	for (int i=0; i < m_ImportMapList.GetSize(); ++i) {
		CWMSMap *pTempMap = m_ImportMapList[i];
		if (pTempMap->m_FacilityDBId == facilityDBId &&
			(pTempMap->m_SectionDBId == sectionDBId) ) {
			
			if (pTempMap->m_WMSDBId == pWMS->m_WMSDBId)			// Already assigned to this WMS; return
				return 0;
			else {											// section assigned to different WMS; update
				pMap = pTempMap;
				oldIdx = i;
				CString temp;
				if (sectionDBId > 0) {
					temp.Format("Section %s is already connected to WMS section %s\n"
						"Do you wish to replace the connection?",
						pTempMap->m_SectionName, pTempMap->m_WMSName);
				}
				else {
					temp.Format("Facility %s is already connected to WMS section %s.\n"
						"Do you wish to replace the connection?", pTempMap->m_FacilityName,
						pTempMap->m_WMSName);
				}
				
				if (AfxMessageBox(temp,	MB_YESNO) != IDYES)
					return 0;
			}
			break;	// section can only be in the import list once
		}
	}
	


	CWMSFacilityInfo *pFacInfo = NULL;
	HTREEITEM hItem = m_FacilityTreeCtrl.GetNextItem(TVI_ROOT, TVGN_CHILD);
	while (hItem != NULL) {
		CWMSFacilityInfo *pInfo = (CWMSFacilityInfo *)m_FacilityTreeCtrl.GetItemData(hItem);
		if (pInfo->m_FacilityDBId == facilityDBId) {
			pFacInfo = pInfo;
			break;
		}

		HTREEITEM hNextItem = m_FacilityTreeCtrl.GetNextItem(hItem, TVGN_NEXT);
		hItem = hNextItem;
	}

	if (pFacInfo == NULL) {
		controlService.Log("Error finding facility in tree.", "Error in WMS Import Assign finding facility %d in tree\n", facilityDBId);
		return -1;
	}

	CWMSMap newMap;
	newMap.m_Direction = CWMSMap::importToOptimize;
	newMap.m_FacilityDBId = pFacInfo->m_FacilityDBId;
	newMap.m_FacilityName = pFacInfo->m_FacilityName;
	newMap.m_SectionDBId = sectionDBId;

	if (sectionDBId > 0) {
		for (int j=0; j < pFacInfo->m_SectionDBIdList.GetSize(); ++j) {
			if (sectionDBId == pFacInfo->m_SectionDBIdList[j]) { 
				newMap.m_SectionName = pFacInfo->m_SectionNameList[j];
				break;
			}
		}
	}
	else
		newMap.m_SectionName = "All Sections";

	newMap.m_WMSDBId = pWMS->m_WMSDBId;
	newMap.m_WMSName = pWMS->m_Name;

	if (pMap != NULL && pMap->m_SectionDBId > 0)
		newMap.m_WMSMapDBId = pMap->m_WMSMapDBId;

	try {
		integrationDataService.StoreWMSImportMap(newMap);
	}
	catch (...) {
		AfxMessageBox("Error creating connection.");
		return -1;
	}

	if (pMap != NULL)		// the section was already mapped, update it
		*pMap = newMap;
	else {
		pMap = new CWMSMap(newMap);
		m_ImportMapList.Add(pMap);
	}

	UpdateMapTreeItem(pMap);


	return 0;
}



int CWMSImportPage::Remove(int facilityDBId, int sectionDBId)
{
	CWaitCursor cwc;

	int rc;

	try {
		rc = integrationDataService.CheckAssignmentIntegrationStatus(facilityDBId, sectionDBId);
	}
	catch (...) {
		controlService.Log("Error checking assignment integration status.", "Generic exception in CheckAssignmentIntegrationStatus\n");
		return -1;
	}

	if (rc > 0) {
		if (AfxMessageBox("Warning! The WMS section you are attempting to disconnect already has integrated assignments.\n"
			"If you disconnect it, inconsistencies between Optimize and the WMS result.\n"
			"Do you wish to continue?", MB_YESNO) != IDYES)
			return -1;
	}


	CWMSMap *pMap;
	for (int i=0; i < m_ImportMapList.GetSize(); ++i) {
		pMap = m_ImportMapList[i];
		if (pMap->m_FacilityDBId == facilityDBId && 
			(pMap->m_SectionDBId == sectionDBId || sectionDBId == 0) ) {
			try {
				integrationDataService.DeleteWMSImportMap(pMap->m_WMSMapDBId);
			}
			catch (...) {
				AfxMessageBox("Error disconnecting WMS section.");
				return -1;
			}
			HTREEITEM hItem;
			// Remove the map from the list first, because the update method
			// uses the list to determine if the facility is mapped
			m_ImportMapList.RemoveAt(i);
			if (sectionDBId > 0) {
				if (m_MapSectionToTree.Lookup(sectionDBId, hItem))
					UpdateFacilityTreeText(hItem, pMap->m_SectionName, "", "");
			}
			else {
				hItem = GetFacilityNode(facilityDBId);
				if (hItem != NULL)
					UpdateFacilityTreeText(hItem, pMap->m_FacilityName, "", "");
			}
			delete pMap;

			return 0;
		}
	}

	return 0;
}

void CWMSImportPage::Reload()
{
	for (int i=0; i < m_ImportMapList.GetSize(); ++i) {
		CWMSMap *pMap = m_ImportMapList[i];
		HTREEITEM hItem;
		// reset the section name to be unmapped since we are going to reload the maps
		// and they might have deleted a WMS
		if (m_MapSectionToTree.Lookup(pMap->m_SectionDBId, hItem))
			m_FacilityTreeCtrl.SetItemText(hItem, pMap->m_SectionName);
		delete m_ImportMapList[i];
	}


	m_ImportMapList.RemoveAll();
	
	CMap<int, int, int, int> expandedMap;
	HTREEITEM hGroupItem = m_WMSTreeCtrl.GetChildItem(TVI_ROOT);
	CString temp;
	temp.Format("%s", m_WMSTreeCtrl.GetItemText(hGroupItem));
	int dbid;
	while (hGroupItem != NULL) {
	
		if (m_WMSTreeCtrl.GetItemState(hGroupItem, TVIS_EXPANDED) & TVIS_EXPANDED) {
			if (m_MapTreeToGroup.Lookup(hGroupItem, dbid))
				expandedMap.SetAt(dbid, 1);
		}
		
		HTREEITEM hNextItem = m_WMSTreeCtrl.GetNextItem(hGroupItem, TVGN_NEXT);
		CString temp;
		temp.Format("%s", m_WMSTreeCtrl.GetItemText(hNextItem));
		hGroupItem = hNextItem;
	}

	m_WMSTreeCtrl.DeleteAllItems();
	m_MapWMSToTree.RemoveAll();
	m_MapTreeToGroup.RemoveAll();
	LoadWMSTree();

	hGroupItem = m_WMSTreeCtrl.GetNextItem(TVI_ROOT, TVGN_CHILD);
	while (hGroupItem != NULL) {
		CWMSGroup *pGroup = (CWMSGroup *)m_WMSTreeCtrl.GetItemData(hGroupItem);

		int dummy;
		if (expandedMap.Lookup(pGroup->m_WMSGroupDBId, dummy))
			m_WMSTreeCtrl.Expand(hGroupItem, TVE_EXPAND);
		
		HTREEITEM hNextItem = m_WMSTreeCtrl.GetNextItem(hGroupItem, TVGN_NEXT);
		hGroupItem = hNextItem;
	}

	LoadMapList();
	UpdateMapTree();
}

void CWMSImportPage::UpdateFacilityTreeText(HTREEITEM &hItem, const CString &name,
											const CString &wmsName, const CString &groupName)
{
	CString temp;

	if (wmsName == "")
		temp.Format("%s", name);
	else
		temp.Format("%s - %s (%s)", name, wmsName, groupName);

	m_FacilityTreeCtrl.SetItemText(hItem, temp);

	if (m_FacilityTreeCtrl.GetParentItem(hItem) == NULL) {	// must be a facility
		if (wmsName == "")
			m_FacilityTreeCtrl.SetItemImage(hItem, 0, 0);
		else
			m_FacilityTreeCtrl.SetItemImage(hItem, 1, 1);
	}
	else {
		if (wmsName == "")
			m_FacilityTreeCtrl.SetItemImage(hItem, 2, 2);
		else
			m_FacilityTreeCtrl.SetItemImage(hItem, 3, 3);
	}

	/*
	CWMSFacilityInfo *pFacInfo = (CWMSFacilityInfo *)m_FacilityTreeCtrl.GetItemData(hFacItem);

	for (int i=0; i < m_ImportMapList.GetSize(); ++i) {
		CWMSMap *pTempMap = m_ImportMapList[i];
		if (pTempMap->m_FacilityDBId == pFacInfo->m_FacilityDBId) {
			m_FacilityTreeCtrl.SetItemImage(hFacItem, 1, 1);
			return;
		}
	}

	m_FacilityTreeCtrl.SetItemImage(hFacItem, 0, 0);
	*/
}

void CWMSImportPage::OnBegindragWmsGroupTree(NMHDR* pNMHDR, LRESULT* pResult) 
{
	NM_TREEVIEW* pNMTreeView = (NM_TREEVIEW*)pNMHDR;

	CPoint hitPoint;
		
	m_WMSTreeCtrl.UpdateWindow();

	// Determine where the cursor is positioned relative to the tree control
	GetCursorPos(&hitPoint);
	m_WMSTreeCtrl.ScreenToClient(&hitPoint);

	// Get the tree node where the cursor is - this will be the drag item
	UINT flags;
	m_hDragItem = m_WMSTreeCtrl.HitTest(hitPoint, &flags);
	if (m_hDragItem == NULL || ! (flags & TVHT_ONITEM))
		return;

	// Verify that the drag item is something that can be dragged
	if (! ValidateWMSDragItem(m_hDragItem)) {
		m_hDragItem = NULL;
		return;
	}

	//ShowCursor(FALSE);
	m_WMSTreeCtrl.SelectDropTarget(NULL);
	m_FacilityTreeCtrl.SelectItem(NULL);

	// Create the drag image list
	m_pDragImageList = m_WMSTreeCtrl.CreateDragImage(m_hDragItem);
	
	// Force the image to be shown while dragging
	m_pDragImageList->DragShowNolock(TRUE);

	// Set the image to be used while dragging
	// Not sure where they are getting it (from the original image list?)
//	m_pDragImageList->SetDragCursorImage(0, CPoint(0, 0));

	// Start the drag operation	
	m_pDragImageList->BeginDrag(0, CPoint(8, 8));

	// Move the cursor to the point where they started dragging
	//m_pDragImageList->DragMove(hitPoint);

	// Lock updates to the window and display the image
	m_pDragImageList->DragEnter(GetDesktopWindow(), pNMTreeView->ptDrag);

	// Capture the mouse so we can respond to the moves
	SetCapture();

	m_bDraggingWMS = TRUE;
	m_hDropItem = NULL;


	*pResult = 0;
}

BOOL CWMSImportPage::ValidateWMSDragItem(HTREEITEM &hItem)
{
	if (m_WMSTreeCtrl.GetParentItem(hItem) == NULL)
		return FALSE;

	return TRUE;

}

BOOL CWMSImportPage::ValidateSectionDragItem(HTREEITEM &hItem)
{
	// Only a section can be dragged from the facility tree
	if (m_WMSTreeCtrl.GetParentItem(hItem) == NULL) {
		//ads_printf("ValidateDrag: not a section\n");
		return FALSE;
	}

	// If the section is not mapped they can't drag it
	int sectionDBId = m_WMSTreeCtrl.GetItemData(hItem);
	for (int i=0; i < m_ImportMapList.GetSize(); ++i) {
		if (m_ImportMapList[i]->m_SectionDBId == sectionDBId) {
			//ads_printf("ValidateDrag: Found map\n");
			return TRUE;
		}
	}
	ads_printf("ValidateDrag: Not mapped\n");
	return FALSE;

}

void CWMSImportPage::OnMouseMove(UINT nFlags, CPoint point) 
{
	if (m_bDraggingWMS)
		OnMouseMoveWMS(point);
	else if (m_bDraggingSection)
		OnMouseMoveSection(point);


	CPropertyPage::OnMouseMove(nFlags, point);

}

void CWMSImportPage::OnMouseMoveWMS(CPoint point)
{
	KillTimer (1);
	// Point is relative to upper left corner of CWMSImportPage
	// convert it to global screen coordinates since that's what we started
	// dragging with
	CPoint pt(point);
	ClientToScreen(&pt);
	
	// Move the image to the current cursor position
	m_pDragImageList->DragMove(pt);
	
	// Temporarily allow screen updates
	m_pDragImageList->DragShowNolock (FALSE);
	
	m_hDropItem = NULL;
	m_FacilityTreeCtrl.SelectDropTarget(NULL);
	//m_WMSTreeCtrl.SelectDropTarget(NULL);
	
	// See if they are passing over the facility tree
	CWnd *pWnd = WindowFromPoint(pt);
	if (pWnd == &m_FacilityTreeCtrl) {
		
		CPoint hitPoint(pt);
		// Convert the screen coordinates to the facility tree client coordinates
		m_FacilityTreeCtrl.ScreenToClient(&hitPoint);
		// See if they are over a facility tree item
		UINT flags;
		m_hDropItem = m_FacilityTreeCtrl.HitTest(hitPoint, &flags);
		if (m_hDropItem != NULL && (flags & TVHT_ONITEM)) {
			
			// Highlight the facility tree item they are over
			m_FacilityTreeCtrl.SelectDropTarget(m_hDropItem);
			::SetCursor((HCURSOR)::GetClassLong(this->m_hWnd, GCL_HCURSOR));
			
		}
		else {
			m_hDropItem = NULL;
			::SetCursor (AfxGetApp ()->LoadStandardCursor (IDC_NO));
		}
		CRect r;
		m_FacilityTreeCtrl.GetClientRect(&r);
		if (abs(hitPoint.y-r.bottom) < 20)
			SetTimer(2, m_nScrollInterval/2, NULL);
		else if (abs(hitPoint.y-r.top) < 20)
			SetTimer(2, m_nScrollInterval/2, NULL);
		else
			KillTimer(2);
		
		if (m_hDropItem != NULL && 
			m_FacilityTreeCtrl.ItemHasChildren(m_hDropItem))
			SetTimer(1, m_nDelayInterval, NULL);
	}
	else {
		m_hDropItem = NULL;
		::SetCursor (AfxGetApp ()->LoadStandardCursor (IDC_NO));
	}
	
	
	
	// Re-lock screen for dragging
	m_pDragImageList->DragShowNolock (TRUE);

}


void CWMSImportPage::OnMouseMoveSection(CPoint point)
{
	// Point is relative to upper left corner of CWMSImportPage
	// convert it to global screen coordinates since that's what we started
	// dragging with
	CPoint pt(point);
	ClientToScreen(&pt);
	
	// Move the image to the current cursor position
	m_pDragImageList->DragMove(pt);
	
	// Temporarily allow screen updates
	m_pDragImageList->DragShowNolock (FALSE);
	
	m_hDropItem = NULL;
	
	// See if they are outside of the facility tree
	CWnd *pWnd = WindowFromPoint(pt);
	if (pWnd == &m_FacilityTreeCtrl)
		::SetCursor (AfxGetApp ()->LoadStandardCursor (IDC_NO));
	else
		::SetCursor((HCURSOR)::GetClassLong(this->m_hWnd, GCL_HCURSOR));
	
	// Re-lock screen for dragging
	m_pDragImageList->DragShowNolock (TRUE);
	
}

void CWMSImportPage::OnLButtonUp(UINT nFlags, CPoint point) 
{
	if (m_bDraggingWMS) {

		KillTimer (1);

		// Stop dragging
		m_pDragImageList->DragLeave(GetDesktopWindow());
		m_pDragImageList->EndDrag();

		// Delete the image list
		delete m_pDragImageList;
		m_bDraggingWMS = FALSE;

		ReleaseCapture();

		m_FacilityTreeCtrl.SelectDropTarget(NULL);

		if (m_hDropItem != NULL) {
			// Highlight the drop item
			m_FacilityTreeCtrl.SelectItem(m_hDropItem);
			m_WMSTreeCtrl.SelectItem(m_hDragItem);

			OnAssign();
			
		}
	}
	else if (m_bDraggingSection) {
		// Stop dragging
		m_pDragImageList->DragLeave(GetDesktopWindow());
		m_pDragImageList->EndDrag();

		// Delete the image list
		delete m_pDragImageList;
		m_bDraggingSection = FALSE;

		ReleaseCapture();

		CPoint pt(point);
		ClientToScreen(&pt);

		CWnd *pWnd = WindowFromPoint(pt);
		if (pWnd != &m_FacilityTreeCtrl) {
			m_FacilityTreeCtrl.SelectItem(m_hDragItem);
			OnRemove();
		}

	}


	CPropertyPage::OnLButtonUp(nFlags, point);
}

void CWMSImportPage::OnTimer(UINT nIDEvent) 
{
	CPropertyPage::OnTimer(nIDEvent);
	//
	// Reset the timer.
	//

	// Get the current cursor position and window height.
	DWORD dwPos = ::GetMessagePos();
	CPoint point (LOWORD (dwPos), HIWORD (dwPos));
	m_FacilityTreeCtrl.ScreenToClient(&point);
	
	if (nIDEvent == 1) {

		SetTimer (nIDEvent, m_nScrollInterval, NULL);
		
		// If the cursor is hovering over a collapsed item, expand the tree.
		UINT nFlags;
		HTREEITEM hItem = m_FacilityTreeCtrl.HitTest(point, &nFlags);
		
		if (hItem != NULL && (nFlags & TVHT_ONITEM)) {
			m_pDragImageList->DragShowNolock(FALSE);
			m_FacilityTreeCtrl.Expand(hItem, TVE_TOGGLE);
			m_pDragImageList->DragShowNolock(TRUE);
			KillTimer(1);
			return;
		}
	}
	else if (nIDEvent == 2) {
		HTREEITEM hFirstVisible = m_FacilityTreeCtrl.GetFirstVisibleItem();
		SetTimer (nIDEvent, m_nScrollInterval/2, NULL);
		CRect r;
		m_FacilityTreeCtrl.GetClientRect(&r);
		m_pDragImageList->DragShowNolock(FALSE);
		if (abs(point.y-r.bottom) < 20)
			m_FacilityTreeCtrl.SendMessage(WM_VSCROLL, MAKEWPARAM (SB_LINEDOWN, 0), NULL);
		else if (abs(point.y-r.top) < 20)
			m_FacilityTreeCtrl.SendMessage(WM_VSCROLL, MAKEWPARAM (SB_LINEUP, 0), NULL);
		if (hFirstVisible == m_FacilityTreeCtrl.GetFirstVisibleItem())
			KillTimer(2);
		m_pDragImageList->DragShowNolock(TRUE);

	}

}

void CWMSImportPage::OnBegindragFacilityMapTree(NMHDR* pNMHDR, LRESULT* pResult) 
{
	NM_TREEVIEW* pNMTreeView = (NM_TREEVIEW*)pNMHDR;

	CPoint hitPoint;
		
	m_FacilityTreeCtrl.UpdateWindow();

	// Determine where the cursor is positioned relative to the tree control
	GetCursorPos(&hitPoint);
	m_FacilityTreeCtrl.ScreenToClient(&hitPoint);

	// Get the tree node where the cursor is - this will be the drag item
	UINT flags;
	m_hDragItem = m_FacilityTreeCtrl.HitTest(hitPoint, &flags);
	if (m_hDragItem == NULL || ! (flags & TVHT_ONITEM))
		return;

	// Verify that the drag item is something that can be dragged
	if (! ValidateSectionDragItem(m_hDragItem)) {
		m_hDragItem = NULL;
		return;
	}

	//ShowCursor(FALSE);
	m_FacilityTreeCtrl.SelectDropTarget(NULL);

	// Create the drag image list
	m_pDragImageList = m_FacilityTreeCtrl.CreateDragImage(m_hDragItem);
	
	// Force the image to be shown while dragging
	m_pDragImageList->DragShowNolock(TRUE);

	// Set the image to be used while dragging
	// Not sure where they are getting it (from the original image list?)
//	m_pDragImageList->SetDragCursorImage(0, CPoint(0, 0));

	// Start the drag operation	
	m_pDragImageList->BeginDrag(0, CPoint(8, 8));

	// Move the cursor to the point where they started dragging
	//m_pDragImageList->DragMove(hitPoint);

	// Lock updates to the window and display the image
	m_pDragImageList->DragEnter(GetDesktopWindow(), pNMTreeView->ptDrag);

	// Capture the mouse so we can respond to the moves
	SetCapture();

	m_bDraggingSection = TRUE;
	m_hDropItem = NULL;


	*pResult = 0;
}

int CWMSImportPage::CountChildren(HTREEITEM &hItem)
{

	return 0;
}

HTREEITEM CWMSImportPage::GetFacilityNode(int facilityDBId)
{
	CWMSFacilityInfo *pInfo = NULL;

	HTREEITEM hItem = m_FacilityTreeCtrl.GetNextItem(TVI_ROOT, TVGN_CHILD);
	while (hItem != NULL) {
		CWMSFacilityInfo *pInfo = (CWMSFacilityInfo *)m_FacilityTreeCtrl.GetItemData(hItem);
		if (pInfo->m_FacilityDBId == facilityDBId) {
			return hItem;
			break;
		}

		HTREEITEM hNextItem = m_FacilityTreeCtrl.GetNextItem(hItem, TVGN_NEXT);
		hItem = hNextItem;
	}

	return NULL;
}

BOOL CWMSImportPage::OnHelpInfo(HELPINFO* pHelpInfo) 
{
	helpService.ShowFieldHelp(IDD, pHelpInfo->iCtrlId);
	return FALSE;

}

BOOL CWMSImportPage::OnNotify(WPARAM wParam, LPARAM lParam, LRESULT* pResult) 
{
	NMHDR* pNMHDR = (NMHDR*)lParam;

	if (pNMHDR->code == PSN_HELP) {
		helpService.ShowScreenHelp(IDD);
		return TRUE;
	}
	
	return CPropertyPage::OnNotify(wParam, lParam, pResult);
}
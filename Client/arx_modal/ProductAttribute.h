// ProductAttribute.h: interface for the CProductAttribute class.
//
//////////////////////////////////////////////////////////////////////

#if !defined(AFX_PRODUCTATTRIBUTE_H__5E791C65_041F_11D5_9EC8_00C04FAC149C__INCLUDED_)
#define AFX_PRODUCTATTRIBUTE_H__5E791C65_041F_11D5_9EC8_00C04FAC149C__INCLUDED_

#if _MSC_VER > 1000
#pragma once
#endif // _MSC_VER > 1000

class CProductAttribute : public CObject  
{
public:
	int Parse(CString &line);
	CStringArray m_ListValues;
	CMapStringToString m_InternalToDisplayMap;	// key = internal, value = display
	CMapStringToString m_DisplayToInternalMap;	// key = display, value = internal
	int m_AttributeDBID;
	int m_Type;
	double m_MaximumValue;
	double m_MinimumValue;
	CString m_Initial;
	CString m_Name;
	CString m_TableName;
	CString m_ColumnName;
	CString m_HelpTopic;
	CProductAttribute();
	CProductAttribute(const CProductAttribute &other);
	virtual ~CProductAttribute();
	CProductAttribute& operator=(const CProductAttribute &other);

};

#endif // !defined(AFX_PRODUCTATTRIBUTE_H__5E791C65_041F_11D5_9EC8_00C04FAC149C__INCLUDED_)

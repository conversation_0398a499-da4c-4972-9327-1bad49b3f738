// OptimizationCommands.cpp: implementation of the COptimizationCommands class.
//
//////////////////////////////////////////////////////////////////////

#include "stdafx.h"
#include "modal.h"
#include "OptimizationCommands.h"
#include "OptimizationHelper.h"
#include "ControlService.h"

#ifdef _DEBUG
#undef THIS_FILE
 char THIS_FILE[]=__FILE__;
#define new DEBUG_NEW
#endif

extern CControlService controlService;

//////////////////////////////////////////////////////////////////////
// Construction/Destruction
//////////////////////////////////////////////////////////////////////

COptimizationCommands::COptimizationCommands()
{

}

COptimizationCommands::~COptimizationCommands()
{

}

void COptimizationCommands::RegisterCommands()
{
	// Optimization
	acedRegCmds->addCommand( "SLOTFAC", "ASSIGNSLOTGROUP", "ASSIGNSLOTGROUP",
		ACRX_CMD_MODAL, &COptimizationCommands::AssignProductGroup );
	acedRegCmds->addCommand( "SLOTFAC", "RUNBASELINE", "RUNBASELINE",
		ACRX_CMD_MODAL, &COptimizationCommands::RunBaselineCost );
	acedRegCmds->addCommand( "SLOTFAC", "UNASSIGNSLOTGROUP", "UNASSIGNSLOTGROUP",
		ACRX_CMD_MODAL, &COptimizationCommands::UnassignProductGroup );
	acedRegCmds->addCommand( "SLOTFAC", "MANUAL", "MANUAL",
		ACRX_CMD_MODAL, &COptimizationCommands::RunManualLayout );

	acedRegCmds->addCommand( "SLOTJAVA", "PRODUCTLOC", "PRODUCTLOC",
		ACRX_CMD_MODAL, &COptimizationCommands::LayoutProductPass );
	acedRegCmds->addCommand( "SLOTJAVA", "CAPITALCOST", "CAPITALCOST",
		ACRX_CMD_MODAL, &COptimizationCommands::CapitalCostPass );
	acedRegCmds->addCommand( "SLOTJAVA", "RACKASSIGN", "RACKASSIGN",
		ACRX_CMD_MODAL, &COptimizationCommands::CapitalCostPass );
	acedRegCmds->addCommand( "SLOTJAVA", "SLOTGROUP", "SLOTGROUP",
		ACRX_CMD_MODAL, &COptimizationCommands::LayoutProductGroupPass );
	acedRegCmds->addCommand("SLOTJAVA", "COSTCOMPARISON", "COSTCOMPARISON",
		ACRX_CMD_MODAL, &COptimizationCommands::CostComparison);
	
	acedRegCmds->addCommand("SLOTJAVA", "RESETCASECOUNTS", "RESETCASECOUNTS",
		ACRX_CMD_MODAL, &COptimizationCommands::ResetCaseCounts);
}


void COptimizationCommands::AssignProductGroup()
{
	if (! controlService.ValidateCommand(CCommands::savedFacilityMode))
		return;

	COptimizationHelper helper;

	helper.AssignProductGroup();

	return;

}

void COptimizationCommands::RunBaselineCost()
{
	if (! controlService.ValidateCommand(CCommands::savedFacilityMode))
		return;
	
	COptimizationHelper helper;

	helper.RunBaselineCost();

	return;
}

void COptimizationCommands::UnassignProductGroup()
{
	if (! controlService.ValidateCommand(CCommands::savedFacilityMode))
		return;

	COptimizationHelper helper;

	helper.UnassignProductGroup();

	return;
}


void COptimizationCommands::RunManualLayout()
{
	if (! controlService.ValidateCommand(CCommands::savedFacilityMode))
		return;

	COptimizationHelper helper;

	helper.RunManualLayout();

	return;
}

void COptimizationCommands::LayoutProductPass()
{
	if (! controlService.ValidateCommand(CCommands::savedFacilityMode))
		return;

	COptimizationHelper helper;

	helper.LayoutProductPass();

	return;
}

void COptimizationCommands::CapitalCostPass()
{
	if (! controlService.ValidateCommand(CCommands::savedFacilityMode))
		return;

	COptimizationHelper helper;

	helper.CapitalCostPass();

	return;
}

void COptimizationCommands::LayoutProductGroupPass()
{
	if (! controlService.ValidateCommand(CCommands::savedFacilityMode))
		return;

	COptimizationHelper helper;

	helper.LayoutProductGroupPass();

	return;
}

void COptimizationCommands::CostComparison()
{
	if (! controlService.ValidateCommand(CCommands::anyMode))
		return;

	COptimizationHelper helper;

	helper.CostComparison();

	return;
}

void COptimizationCommands::ResetCaseCounts()
{
	if (! controlService.ValidateCommand(CCommands::savedFacilityMode))
		return;

	COptimizationHelper helper;

	helper.ResetCaseCounts();

	return;
}

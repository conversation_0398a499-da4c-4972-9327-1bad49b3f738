//////////////////////////////////////////////////////////////////////
// File Name : OptimizationHelper.h
// Classname :
// Description : Interface for the COptimizationHelper class.
// Date Created :
// Author :
//////////////////////////////////////////////////////////////////////
// Revisions :
//   Date(mm-dd-yyyy) : initials : Description_of_revision
//   03-02-2006       : SMB      : Add Prolog
//////////////////////////////////////////////////////////////////////
	/*******************************************************
	*
	*                       NOTICE
	*
	*  THIS SOFTWARE IS THE PROPERTY OF AND CONTAINS
	*  CONFIDENTIAL INFORMATION OF SSA GLOBAL
	*  TECHNOLOGIES, INC., AND SHALL NOT BE COPIED,
	*  USED, NOR DISCLOSED WITHOUT EXPRESS WRITTEN
	*  AUTHORIZATION.  ALTHOUGH PUBLICATION IS NOT
	*  INTENDED, IN THE EVENT OF PUBLICATION, THE
	*  FOLLOWING NOTICE IS APPLICABLE:
	*
	*  (c) COPYRIGHT 2004 SSA GLOBAL TECHNOLOGIES, INC.
	*
	*           SSA GLOBAL TECHNOLOGIES, INC.
	*
	********************************************************/
//////////////////////////////////////////////////////////////////////

#if !defined(AFX_OPTIMIZATIONHELPER_H__7AFBC08E_5D3E_4125_AD06_D24F2AD40E54__INCLUDED_)
#define AFX_OPTIMIZATIONHELPER_H__7AFBC08E_5D3E_4125_AD06_D24F2AD40E54__INCLUDED_

#if _MSC_VER > 1000
#pragma once
#endif // _MSC_VER > 1000

//////////////////////////////////////////////////////////////////////
// Classname : COptimizationHelper
// Description :
// Date Created :
// Author :
//////////////////////////////////////////////////////////////////////
// Revisions :
//   Date(mm-dd-yyyy) : initials : Description_of_revision
//   03-02-2006       : SMB      : Add Prolog
//////////////////////////////////////////////////////////////////////
class COptimizationHelper
{
	public:
	   COptimizationHelper();
	   virtual ~COptimizationHelper();

	   void ResetCaseCounts();
	   void AssignProductGroup();
	   void RunBaselineCost();
	   void UnassignProductGroup();
	   void RunManualLayout();
	   void LayoutProductPass();
	   void CapitalCostPass();
	   void LayoutProductGroupPass();
	   void CostComparison();
	   int GetDuplicateLocations();

	   int CalculateBaselineCost(CString & cost, long facilityDBID = -1, CString prodCostList = "");
	   int CalculateOptimizeCost(CString & cost, long facilityDBID = -1, CString prodCostList = "");

	   static UINT RunProductGroupLayoutThread(LPVOID pParam);
	   static UINT RunProductLayoutThread(LPVOID pParam);
	   static UINT RunCapitalCostThread(LPVOID pParam);

	   int ProductLayout(int facilityId, int mode, const CString &options, int maxResults,
	   int facilityIntegrated, CStringArray &results);
	   int CapitalCost(int facilityId, const CString &options, int maxResults, CStringArray &results);
	   int ProductGroupLayout(int facilityId, const CString &options, int maxResults, CStringArray &results);
	   int StartModelComparison(int facilityDBID, int moveLength,int hoursWork, int maxMoves, CStringArray &retArray);

	   typedef enum
	   {
		   GroupMode     = 0,
		   TacticalMode  = 1,
		   StrategicMode = 2,
		   NewMode       = 3,
		   ManualMode    = 4
       }enumProductLayoutMode;

}; // END class COptimizationHelper

#endif // !defined(AFX_OPTIMIZATIONHELPER_H__7AFBC08E_5D3E_4125_AD06_D24F2AD40E54__INCLUDED_)

// WMSFacilityInfo.h: interface for the CWMSFacilityInfo class.
//
//////////////////////////////////////////////////////////////////////

#if !defined(AFX_WMSFACILITYINFO_H__85BCAB61_9EC1_47F0_B95E_C3C0AD99A8B2__INCLUDED_)
#define AFX_WMSFACILITYINFO_H__85BCAB61_9EC1_47F0_B95E_C3C0AD99A8B2__INCLUDED_

#if _MSC_VER > 1000
#pragma once
#endif // _MSC_VER > 1000

class CWMSFacilityInfo : public CObject
{
public:
	CWMSFacilityInfo();
	virtual ~CWMSFacilityInfo();
	
	int m_FacilityDBId;
	CString m_FacilityName;
	CArray<int, int> m_SectionDBIdList;
	CStringArray m_SectionNameList;

	int Parse(CString &line);
};

#endif // !defined(AFX_WMSFACILITYINFO_H__85BCAB61_9EC1_47F0_B95E_C3C0AD99A8B2__INCLUDED_)

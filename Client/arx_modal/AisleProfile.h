// AisleProfile.h: interface for the CAisleProfile class.
//
//////////////////////////////////////////////////////////////////////

#if !defined(AFX_AISLEPROFILE_H__12E54B47_E710_4013_ABDF_A2AF33F44D09__INCLUDED_)
#define AFX_AISLEPROFILE_H__12E54B47_E710_4013_ABDF_A2AF33F44D09__INCLUDED_

#include "SideProfile.h"
#include <acdb.h>

#if _MSC_VER > 1000
#pragma once
#endif // _MSC_VER > 1000

class CAisleProfile : public CObject  
{
public:
	CAisleProfile();
	virtual ~CAisleProfile();
	CAisleProfile& operator=(const CAisleProfile &other);
	CAisleProfile(const CAisleProfile &other);
	BOOL operator==(const CAisleProfile& other);
	BOOL operator!=(const CAisleProfile& other) { return !(*this == other);};
	int Parse(CString &line);
	int Draw(BOOL currentDB, BOOL doSave);
	int DrawByPosition(AcDbDatabase *pDatabase, const C3DPoint &leftSideCornerPoint, double rotation);

	int m_AisleProfileDBId;
	CString m_Description;
	double m_AisleSpace;

	CSideProfile *m_pLeftSideProfile;
	double m_LeftSpace;

	CSideProfile *m_pRightSideProfile;
	double m_RightSpace;

private:
	Acad::ErrorStatus DrawSide();
	AcDbDatabase *m_pDatabase;
    AcDbBlockTable *m_pBlockTable;
    AcDbBlockTableRecord *m_pBlockTableRecord;
};

#endif // !defined(AFX_AISLEPROFILE_H__12E54B47_E710_4013_ABDF_A2AF33F44D09__INCLUDED_)

// BayProfileLaborPage.cpp : implementation file
//

#include "stdafx.h"
#include "modal.h"
#include "BayProfileLaborPage.h"
#include "BayProfileSheet.h"
#include "BayProfileHandlingProperties.h"
#include "HelpService.h"
#include "UtilityHelper.h"

#ifdef _DEBUG
#define new DEBUG_NEW
#undef THIS_FILE
static char THIS_FILE[] = __FILE__;
#endif

extern CUtilityHelper utilityHelper;
extern CHelpService helpService;
/////////////////////////////////////////////////////////////////////////////
// CBayProfileLaborPage property page

IMPLEMENT_DYNCREATE(CBayProfileLaborPage, CPropertyPage)

CBayProfileLaborPage::CBayProfileLaborPage() : CPropertyPage(CBayProfileLaborPage::IDD)
{
	//{{AFX_DATA_INIT(CBayProfileLaborPage)
	m_ForkExtractionTime = _T("");
	m_ForkInsertionTime = _T("");
	//}}AFX_DATA_INIT
	m_LastWorkType = CLevelLaborProfile::workTypeSelection;
}

CBayProfileLaborPage::~CBayProfileLaborPage()
{
}

void CBayProfileLaborPage::DoDataExchange(CDataExchange* pDX)
{
	CPropertyPage::DoDataExchange(pDX);
	//{{AFX_DATA_MAP(CBayProfileLaborPage)
	DDX_Control(pDX, IDC_HANDLING_LIST, m_HandlingListCtrl);
	DDX_Control(pDX, IDC_LEVEL_LIST, m_LevelListCtrl);
	DDX_Control(pDX, IDC_LEVEL_BUTTON, m_LevelButton);
	DDX_Text(pDX, IDC_FORK_EXTRACTION_TIME, m_ForkExtractionTime);
	DDX_Text(pDX, IDC_FORK_INSERTION_TIME, m_ForkInsertionTime);
	//}}AFX_DATA_MAP
}


BEGIN_MESSAGE_MAP(CBayProfileLaborPage, CPropertyPage)
	//{{AFX_MSG_MAP(CBayProfileLaborPage)
	ON_CBN_SELCHANGE(IDC_LEVEL_LIST, OnSelchangeLevelList)
	ON_BN_CLICKED(IDC_ADD, OnAdd)
	ON_BN_CLICKED(IDC_MODIFY, OnModify)
	ON_BN_CLICKED(IDC_DELETE, OnDelete)
	ON_NOTIFY(LVN_COLUMNCLICK, IDC_HANDLING_LIST, OnColumnclickHandlingList)
	ON_MESSAGE(WM_SELECT_LEVEL, (LRESULT (AFX_MSG_CALL CWnd::*)(WPARAM, LPARAM))OnSelectLevel)
	ON_WM_HELPINFO()
	//}}AFX_MSG_MAP
END_MESSAGE_MAP()

/////////////////////////////////////////////////////////////////////////////
// CBayProfileLaborPage message handlers

BOOL CBayProfileLaborPage::OnInitDialog() 
{
	CPropertyPage::OnInitDialog();
	
	for (int i=0; i < m_HandlingListCtrl.GetHeaderCtrl()->GetItemCount(); ++i)
		m_HandlingListCtrl.GetHeaderCtrl()->DeleteItem(0);

	CRect r;
	m_HandlingListCtrl.GetClientRect(&r);

	m_HandlingListCtrl.InsertColumn(0, "Work Type", LVCFMT_LEFT, r.Width()/4);
	m_HandlingListCtrl.InsertColumn(1, "Cube", LVCFMT_RIGHT, r.Width()/4);
	m_HandlingListCtrl.InsertColumn(2, "Variable", LVCFMT_RIGHT, r.Width()/4);
	m_HandlingListCtrl.InsertColumn(3, "Fixed", LVCFMT_RIGHT, r.Width()/4);

	return TRUE;  // return TRUE unless you set the focus to a control
	              // EXCEPTION: OCX Property Pages should return FALSE
}

BOOL CBayProfileLaborPage::OnSetActive() 
{
	CBayProfileSheet *pSheet = (CBayProfileSheet *)GetParent();
	m_pBayProfile = pSheet->m_pBayProfile;

	m_LevelButton.m_CrossbarList.RemoveAll();
	m_LevelButton.m_BayHeight = m_pBayProfile->m_Height;
	m_LevelButton.m_UprightHeight = m_pBayProfile->m_UprightHeight;
	m_LevelButton.m_BayWidth = m_pBayProfile->m_Width;	

	for (int i=0; i < m_pBayProfile->m_LevelProfileList.GetSize(); ++i) {

		CLevelProfile *pLevelProfile = m_pBayProfile->m_LevelProfileList[i];

		CBayProfileCrossbarInfo info;
		info.m_Clearance = pLevelProfile->m_Clearance;
		info.m_Height = pLevelProfile->m_Coordinates.m_Z;
		info.m_IsHidden = pLevelProfile->m_IsBarHidden;
		info.m_IsSelected = (i == pSheet->m_SelectedLevel);
		info.m_LocationCount = pLevelProfile->m_LocationProfileList.GetSize();
		info.m_MinimumWidth = pLevelProfile->m_MinimumLocWidth;
		info.m_LocationRowCount = pLevelProfile->m_LocationRowCount;

		if (info.m_LocationCount == 0)
			info.m_LocationSpace = 0;
		else
			info.m_LocationSpace = pLevelProfile->m_LocationProfileList[0]->m_LocationSpace;

		info.m_Thickness = pLevelProfile->m_Thickness;
		
		m_LevelButton.m_CrossbarList.Add(info);

	}

	RebuildLevelList();

	m_LevelListCtrl.SetCurSel(pSheet->m_SelectedLevel);
	UpdateScreenFromLevelProfile(pSheet->m_SelectedLevel);
	m_LevelButton.Invalidate();
	
	return CPropertyPage::OnSetActive();
}

BOOL CBayProfileLaborPage::OnKillActive() 
{
	CBayProfileSheet *pSheet = (CBayProfileSheet *)GetParent();
	
	if (UpdateLevelProfileFromScreen(pSheet->m_SelectedLevel) < 0)
		return FALSE;

	if (! Validate())
		return FALSE;

	return CPropertyPage::OnKillActive();
}

void CBayProfileLaborPage::OnSelchangeLevelList() 
{
	int curSel = m_LevelListCtrl.GetCurSel();
	if (curSel < 0)
		return;

	if (m_LevelButton.SelectLevel(curSel) < 0) {
		// Set the selection back to the previous which
		// is kept on the parent if it fails
		CBayProfileSheet *pSheet = (CBayProfileSheet *)GetParent();
		m_LevelListCtrl.SetCurSel(pSheet->m_SelectedLevel);
	}	
}

int CBayProfileLaborPage::OnSelectLevel(WPARAM wParam, LPARAM lParam)
{
	UNREFERENCED_PARAMETER(lParam);

	CBayProfileSheet *pSheet = (CBayProfileSheet *)GetParent();
	int newLevel = wParam;

	if (pSheet->m_SelectedLevel != newLevel) {	
		
		if (UpdateLevelProfileFromScreen(pSheet->m_SelectedLevel) < 0)
			return -1;

		if (newLevel >= 0) {
			if (UpdateScreenFromLevelProfile(newLevel) < 0)
				return -1;
		}
		
		pSheet->m_SelectedLevel = newLevel;
	}

	m_LevelListCtrl.SetCurSel(newLevel);

	return 0;
}

int CBayProfileLaborPage::UpdateScreenFromLevelProfile(int currentLevel)
{
	CLevelProfile *pLevelProfile = m_pBayProfile->m_LevelProfileList[currentLevel];

	m_ForkExtractionTime.Format("%.4f", pLevelProfile->m_ForkFixedExtraction);
	m_ForkInsertionTime.Format("%.4f", pLevelProfile->m_ForkFixedInsertion);

	m_HandlingListCtrl.DeleteAllItems();

	for (int i=0; i < pLevelProfile->m_LevelLaborProfileList.GetSize(); ++i) {
		CLevelLaborProfile *pLaborProfile = pLevelProfile->m_LevelLaborProfileList[i];
		CString temp;
		if (pLaborProfile->m_WorkType == CLevelLaborProfile::workTypeSelection)
			temp = "Selection";
		else
			temp = "Stocker";

		int nItem = m_HandlingListCtrl.InsertItem(i, temp);
		m_HandlingListCtrl.SetItemData(nItem, (LPARAM)pLaborProfile);
		temp.Format("%.4f", pLaborProfile->m_Cube);
		m_HandlingListCtrl.SetItemText(nItem, 1, temp);

		temp.Format("%.4f", pLaborProfile->m_VariableFactor);
		m_HandlingListCtrl.SetItemText(nItem, 2, temp);

		temp.Format("%.4f", pLaborProfile->m_FixedFactor);
		m_HandlingListCtrl.SetItemText(nItem, 3, temp);
	}
	
	m_HandlingListCtrl.SortItems(CBayProfileLaborPage::SortHandlingList, 0);

	UpdateData(FALSE);

	return 0;
}

int CBayProfileLaborPage::UpdateLevelProfileFromScreen(int currentLevel)
{
	CLevelProfile *pLevelProfile = m_pBayProfile->m_LevelProfileList[currentLevel];
	UpdateData(TRUE);
	
	if (! utilityHelper.IsFloat(m_ForkExtractionTime)) {
		AfxMessageBox("Please enter a valid floating point number for Fork Extraction Time");
		CEdit *pEdit = (CEdit *)GetDlgItem(IDC_FORK_EXTRACTION_TIME);
		pEdit->SetSel(0,-1);
		pEdit->SetFocus();
		return -1;
	}

	if (! utilityHelper.IsFloat(m_ForkInsertionTime)) {
		AfxMessageBox("Please enter a valid floating point number for Fork Insertion Time");
		CEdit *pEdit = (CEdit *)GetDlgItem(IDC_FORK_INSERTION_TIME);
		pEdit->SetSel(0,-1);
		pEdit->SetFocus();
		return -1;
	}

	pLevelProfile->m_ForkFixedExtraction = atof(m_ForkExtractionTime);
	pLevelProfile->m_ForkFixedInsertion = atof(m_ForkInsertionTime);

	return 0;
}

void CBayProfileLaborPage::RebuildLevelList()
{
	int curSel = m_LevelListCtrl.GetCurSel();

	m_LevelListCtrl.ResetContent();
	for (int i=0; i < m_pBayProfile->m_LevelProfileList.GetSize(); ++i) {
		m_pBayProfile->m_LevelProfileList[i]->m_Description.Format("%d", i+1);
		m_pBayProfile->m_LevelProfileList[i]->m_RelativeLevel = i+1;
		CString temp;
		if (i == 0)
			temp = "Level 1 - Floor";
		else
			temp.Format("Level: %d - Position: %.0f", i+1, m_pBayProfile->m_LevelProfileList[i]->m_Coordinates.m_Z);
		int nItem = m_LevelListCtrl.AddString(temp);
		//m_LevelListCtrl.SetItemData(nItem, m_pBayProfile->m_LevelProfileList[i]->m_Coordinates.m_Z);
	}

	if (curSel >= m_LevelListCtrl.GetCount())
		curSel = m_LevelListCtrl.GetCount()-1;

	m_LevelListCtrl.SetCurSel(curSel);

	CRect r;
	m_LevelListCtrl.GetWindowRect(&r);
	m_LevelListCtrl.SetWindowPos(NULL, 0, 0, r.Width(), 
		r.Height()*(m_pBayProfile->m_LevelProfileList.GetSize()+1), SWP_NOMOVE|SWP_NOZORDER);

}


void CBayProfileLaborPage::OnAdd() 
{
	CBayProfileHandlingProperties dlg;
	CBayProfileSheet *pSheet = (CBayProfileSheet *)GetParent();

	UpdateLevelProfileFromScreen(pSheet->m_SelectedLevel);

	dlg.m_WorkType = m_LastWorkType;
	dlg.m_Cube = "0";
	dlg.m_Fixed = "0";
	dlg.m_Variable = "0";
	dlg.m_CurrentHandlingIdx = -1;
	dlg.m_CurrentLevel = pSheet->m_SelectedLevel;
	
	try {
		if (dlg.DoModal() != IDOK)
			return;
	}
	catch (...) {
		AfxMessageBox("Error displaying Handling Properties screen.");
		return;
	}

	m_LastWorkType = dlg.m_WorkType;

	CLevelProfile *pLevelProfile = m_pBayProfile->m_LevelProfileList[pSheet->m_SelectedLevel];
	CLevelLaborProfile *pLevelLaborProfile = new CLevelLaborProfile;

	pLevelLaborProfile->m_Cube = atof(dlg.m_Cube);
	pLevelLaborProfile->m_FixedFactor = atof(dlg.m_Fixed);
	pLevelLaborProfile->m_VariableFactor = atof(dlg.m_Variable);
	pLevelLaborProfile->m_WorkType = dlg.m_WorkType;
	pLevelLaborProfile->m_LevelProfileDBId = pLevelProfile->m_LevelProfileDBId;

	int count = 0;
	for (int i=0; i < pLevelProfile->m_LevelLaborProfileList.GetSize(); ++i) {
		if (pLevelProfile->m_LevelLaborProfileList[i]->m_WorkType == pLevelLaborProfile->m_WorkType)
			count++;
	}
	pLevelLaborProfile->m_Description.Format("%d", count+1);
	pLevelProfile->m_LevelLaborProfileList.Add(pLevelLaborProfile);

	UpdateScreenFromLevelProfile(pSheet->m_SelectedLevel);

}

void CBayProfileLaborPage::OnModify() 
{
	int curLabor;

	POSITION pos = m_HandlingListCtrl.GetFirstSelectedItemPosition();
	if (pos != NULL)
		curLabor = m_HandlingListCtrl.GetNextSelectedItem(pos);
	else {
		AfxMessageBox("Please select a handling value to modify.");
		return;
	}

	CBayProfileHandlingProperties dlg;
	CBayProfileSheet *pSheet = (CBayProfileSheet *)GetParent();
	CLevelProfile *pLevelProfile = m_pBayProfile->m_LevelProfileList[pSheet->m_SelectedLevel];
	CLevelLaborProfile *pLevelLaborProfile = pLevelProfile->m_LevelLaborProfileList[curLabor];

	
	dlg.m_WorkType = pLevelLaborProfile->m_WorkType;
	(dlg.m_Cube).Format("%.4f", pLevelLaborProfile->m_Cube);
	(dlg.m_Fixed).Format("%.4f", pLevelLaborProfile->m_FixedFactor);
	(dlg.m_Variable).Format("%.4f", pLevelLaborProfile->m_VariableFactor);
	dlg.m_CurrentHandlingIdx = curLabor;
	dlg.m_CurrentLevel = pSheet->m_SelectedLevel;
	
	try {
		if (dlg.DoModal() != IDOK)
			return;
	}
	catch (...) {
		AfxMessageBox("Error displaying Handling Properties screen.");
		return;
	}

	m_LastWorkType = dlg.m_WorkType;

	pLevelLaborProfile->m_Cube = atof(dlg.m_Cube);
	pLevelLaborProfile->m_FixedFactor = atof(dlg.m_Fixed);
	pLevelLaborProfile->m_VariableFactor = atof(dlg.m_Variable);
	pLevelLaborProfile->m_WorkType = dlg.m_WorkType;

	UpdateScreenFromLevelProfile(pSheet->m_SelectedLevel);
	
}

void CBayProfileLaborPage::OnDelete() 
{
	int curLabor;

	POSITION pos = m_HandlingListCtrl.GetFirstSelectedItemPosition();
	if (pos != NULL)
		curLabor = m_HandlingListCtrl.GetNextSelectedItem(pos);
	else {
		AfxMessageBox("Please select a handling value to delete.");
		return;
	}

	CLevelLaborProfile *pLaborProfile = (CLevelLaborProfile *)m_HandlingListCtrl.GetItemData(curLabor);
	pLaborProfile->m_LevelLaborProfileDBId = -1337;
	
	CBayProfileSheet *pSheet = (CBayProfileSheet *)GetParent();
	CLevelProfile *pLevelProfile = m_pBayProfile->m_LevelProfileList[pSheet->m_SelectedLevel];
	for (int i=0; i < pLevelProfile->m_LevelLaborProfileList.GetSize(); ++i) {
		if (pLevelProfile->m_LevelLaborProfileList[i]->m_LevelLaborProfileDBId == -1337)
			break;
	}
	delete pLaborProfile;
	pLevelProfile->m_LevelLaborProfileList.RemoveAt(i);

	m_HandlingListCtrl.DeleteItem(curLabor);

	UpdateScreenFromLevelProfile(pSheet->m_SelectedLevel);

}

int CALLBACK CBayProfileLaborPage::SortHandlingList(LPARAM lParam1, LPARAM lParam2, LPARAM column)
{
	CLevelLaborProfile *pLaborProfile1, *pLaborProfile2;
	pLaborProfile1 = (CLevelLaborProfile *)lParam1;
	pLaborProfile2 = (CLevelLaborProfile *)lParam2;

	switch (column) {
	case 0:		// work type
		if (pLaborProfile1->m_WorkType < pLaborProfile2->m_WorkType)
			return -1;
		else if (pLaborProfile1->m_WorkType > pLaborProfile2->m_WorkType)
			return 1;
		else
			return (pLaborProfile1->m_Cube < pLaborProfile2->m_Cube) ? -1 : 1;
		break;
	case 1:		// cube
		if (pLaborProfile1->m_Cube < pLaborProfile2->m_Cube)
			return -1;
		else if (pLaborProfile1->m_Cube > pLaborProfile2->m_Cube)
			return 1;
		else
			return (pLaborProfile1->m_WorkType < pLaborProfile2->m_WorkType) ? -1 : 1;
		break;
	case 2:		// variable factor
		if (pLaborProfile1->m_VariableFactor < pLaborProfile2->m_VariableFactor)
			return -1;
		else if (pLaborProfile1->m_VariableFactor > pLaborProfile2->m_VariableFactor)
			return 1;
		else 
			if (pLaborProfile1->m_Cube < pLaborProfile2->m_Cube)
				return -1;
			else if (pLaborProfile1->m_Cube > pLaborProfile2->m_Cube)
				return 1;
			else
				return (pLaborProfile1->m_WorkType < pLaborProfile2->m_WorkType) ? -1 : 1;
		break;
	case 3:		// fixed factor
		if (pLaborProfile1->m_FixedFactor < pLaborProfile2->m_FixedFactor)
			return -1;
		else if (pLaborProfile1->m_FixedFactor > pLaborProfile2->m_FixedFactor)
			return 1;
		else 
			if (pLaborProfile1->m_Cube < pLaborProfile2->m_Cube)
				return -1;
			else if (pLaborProfile1->m_Cube > pLaborProfile2->m_Cube)
				return 1;
			else
				return (pLaborProfile1->m_WorkType < pLaborProfile2->m_WorkType) ? -1 : 1;
			
		break;
	}
	
	return 0;
}

void CBayProfileLaborPage::OnColumnclickHandlingList(NMHDR* pNMHDR, LRESULT* pResult) 
{
	NM_LISTVIEW* pNMListView = (NM_LISTVIEW*)pNMHDR;
	
	m_HandlingListCtrl.SortItems(CBayProfileLaborPage::SortHandlingList, pNMListView->iSubItem);

	*pResult = 0;
}

BOOL CBayProfileLaborPage::Validate()
{
	return TRUE;
}

BOOL CBayProfileLaborPage::OnHelpInfo(HELPINFO* pHelpInfo) 
{
	helpService.ShowFieldHelp(IDD, pHelpInfo->iCtrlId);
	return FALSE;

}

BOOL CBayProfileLaborPage::OnNotify(WPARAM wParam, LPARAM lParam, LRESULT* pResult) 
{
	NMHDR* pNMHDR = (NMHDR*)lParam;

	if (pNMHDR->code == PSN_HELP) {
		helpService.ShowScreenHelp(IDD);
		return TRUE;
	}
	
	return CPropertyPage::OnNotify(wParam, lParam, pResult);
}

// ColorModelAdvancedDialog.cpp : implementation file
//

#include "stdafx.h"
#include "modal.h"
#include "ColorModelAdvancedDialog.h"
#include "ColoringHelper.h"
#include "UtilityHelper.h"
#include "HelpService.h"

#ifdef _DEBUG
#define new DEBUG_NEW
#undef THIS_FILE
static char THIS_FILE[] = __FILE__;
#endif

/////////////////////////////////////////////////////////////////////////////
// CColorModelAdvancedDialog dialog

extern CUtilityHelper utilityHelper;
extern CHelpService helpService;

CColorModelAdvancedDialog::CColorModelAdvancedDialog(CWnd* pParent /*=NULL*/)
	: CDialog(CColorModelAdvancedDialog::IDD, pParent)
{
	//{{AFX_DATA_INIT(CColorModelAdvancedDialog)
	m_Level = _T("");
	m_MaxHeight = _T("");
	m_MinHeight = _T("");
	//}}AFX_DATA_INIT
}


void CColorModelAdvancedDialog::DoDataExchange(CDataExchange* pDX)
{
	CDialog::DoDataExchange(pDX);
	//{{AFX_DATA_MAP(CColorModelAdvancedDialog)
	DDX_Text(pDX, IDC_LEVEL, m_Level);
	DDX_Text(pDX, IDC_MAX_HEIGHT, m_MaxHeight);
	DDX_Text(pDX, IDC_MIN_HEIGHT, m_MinHeight);
	//}}AFX_DATA_MAP
}


BEGIN_MESSAGE_MAP(CColorModelAdvancedDialog, CDialog)
	//{{AFX_MSG_MAP(CColorModelAdvancedDialog)
	ON_WM_HELPINFO()
	ON_BN_CLICKED(IDHELP, OnHelp)
	//}}AFX_MSG_MAP
END_MESSAGE_MAP()

/////////////////////////////////////////////////////////////////////////////
// CColorModelAdvancedDialog message handlers
BOOL CColorModelAdvancedDialog::OnInitDialog() 
{
	CDialog::OnInitDialog();
	
	CButton *pButton;
	switch (m_MultiType) {
	case CColoringHelper::Avg:
		pButton = (CButton *)GetDlgItem(IDC_AVG);
		break;
	case CColoringHelper::Max:
		pButton = (CButton *)GetDlgItem(IDC_MAX);
		break;
	case CColoringHelper::Min:
		pButton = (CButton *)GetDlgItem(IDC_MIN);
		break;
	case CColoringHelper::Sum:
	default:
		pButton = (CButton *)GetDlgItem(IDC_SUM);
		break;
	}

	pButton->SetCheck(1);

	UpdateData(FALSE);

	return TRUE;  // return TRUE unless you set the focus to a control
	              // EXCEPTION: OCX Property Pages should return FALSE
}

void CColorModelAdvancedDialog::OnOK() 
{
	UpdateData(TRUE);

	if (! utilityHelper.IsInteger(m_Level)) {
		AfxMessageBox("Level must be a positive integer.");
		utilityHelper.SetEditControlErrorState(this, IDC_LEVEL);
		return;
	}

	if (! utilityHelper.IsFloat(m_MaxHeight)) {
		AfxMessageBox("Maximum height must be a number.");
		utilityHelper.SetEditControlErrorState(this, IDC_MAX_HEIGHT);
		return;
	}

	if (! utilityHelper.IsFloat(m_MinHeight)) {
		AfxMessageBox("Minimum height must be a number.");
		utilityHelper.SetEditControlErrorState(this, IDC_MIN_HEIGHT);
		return;
	}

	m_MultiType = CColoringHelper::Sum;
	
	CButton *pButton = (CButton *)GetDlgItem(IDC_SUM);
	if (pButton->GetCheck())
		m_MultiType = CColoringHelper::Sum;
	else {
		pButton = (CButton *)GetDlgItem(IDC_AVG);
		if (pButton->GetCheck())
			m_MultiType = CColoringHelper::Avg;
		else {
			pButton = (CButton *)GetDlgItem(IDC_MIN);
			if (pButton->GetCheck())
				m_MultiType = CColoringHelper::Min;
			else {
				pButton = (CButton *)GetDlgItem(IDC_MAX);
				if (pButton->GetCheck())
					m_MultiType = CColoringHelper::Max;
			}
		}
	}

	CDialog::OnOK();
}


BOOL CColorModelAdvancedDialog::OnHelpInfo(HELPINFO* pHelpInfo) 
{
	// TODO: Add your message handler code here and/or call default
	
	return CDialog::OnHelpInfo(pHelpInfo);
}

void CColorModelAdvancedDialog::OnHelp() 
{
	// TODO: Add your control notification handler code here
	
}

// HelpService.cpp: implementation of the CHelpService class.
//
//////////////////////////////////////////////////////////////////////

#include "stdafx.h"
#include "modal.h"
#include "HelpService.h"
#include "UtilityHelper.h"

#include <dbsymtb.h>

#ifdef _DEBUG
#undef THIS_FILE
static char THIS_FILE[]=__FILE__;
#define new DEBUG_NEW
#endif

extern char slotDir[256];
extern CUtilityHelper utilityHelper;
//////////////////////////////////////////////////////////////////////
// Construction/Destruction
//////////////////////////////////////////////////////////////////////

CHelpService::CHelpService()
{
	return;

}

CHelpService::~CHelpService()
{
	POSITION pos;
	WORD key;
	CMap<int, int, CString, CString> *pFieldMap;

	pos = m_HelpMap.GetStartPosition();
	while (pos != NULL) {
		m_HelpMap.GetNextAssoc(pos, key, (void *&)pFieldMap);
		delete pFieldMap;
	}


}

void CHelpService::ShowFieldHelp(int screenID, int fieldID)
{
	CMap<int, int, CString, CString> *pFieldMap;
	CString helpTopic;

	if (m_HelpMap.Lookup((WORD)screenID, (void *&)pFieldMap))
		pFieldMap->Lookup(fieldID, helpTopic);
	else
		return;

	if (helpTopic == "") {
		pFieldMap->Lookup(0, helpTopic);
		if (helpTopic == "")
			return;
		else {
			ShowScreenHelp(helpTopic);
			return;
		}
	}

	ShowFieldHelp(helpTopic);

	return;

}




void CHelpService::ShowScreenHelp(int screenID)
{
	CMap<int, int, CString, CString> *pFieldMap;
	CString helpTopic;

	if (! m_HelpMap.Lookup((WORD)screenID, (void *&)pFieldMap))
		helpTopic = "";
	else
		pFieldMap->Lookup(0, helpTopic);

	if (helpTopic == "") {
		ShowScreenHelp(CString("HelpNotFound"));
		return;
	}

	ShowScreenHelp(helpTopic);

	return;	
}


void CHelpService::RunHelp(const CString &helpTopic)
{
	STARTUPINFO si;
	PROCESS_INFORMATION pi;
	CString hlpCmd;
	HANDLE helpProcHandle = NULL;
	int funcRet;
	DWORD exitCode;

	hlpCmd = "winhlp32 " + helpTopic + CString(" ") + CString(slotDir) + CString("help\\") + CString("Optimize.hlp");

	si.cb = sizeof(si);                           // size of the structure for versioning
	si.lpReserved = NULL;						  // should be NULL
	si.lpDesktop = NULL;						  // Desktop
	si.lpTitle = "Succeed Help";           // Title of the window
	si.dwX =  si.dwY = STARTF_USEPOSITION;		  // Use default start up position
	si.dwXSize = si.dwYSize = STARTF_USESIZE;     // Use default start up size.
	si.dwXCountChars = si.dwYCountChars = STARTF_USECOUNTCHARS; // Console window buffer lengths
	si.dwFillAttribute = STARTF_USEFILLATTRIBUTE;  // use default background colors etc.
	//***********************************************************************************************************
	// Set dwFlags to STARTF_USESHOWWINDOW so that we can manipulate the SW_HIDE etc.
	//***********************************************************************************************************
	si.dwFlags =  STARTF_USESHOWWINDOW;            // use show window to set the window state as in wShowWindow
	si.cbReserved2 = 0;							   // Should be 0
	si.lpReserved2 = NULL;						   // Should be NULL
	si.wShowWindow = SW_MINIMIZE;				   // Minimize the MS DOS window

	if ((funcRet = GetExitCodeProcess(helpProcHandle, &exitCode)) != 0)
		if (exitCode == STILL_ACTIVE)
			TerminateProcess(helpProcHandle, 0);

	BOOL bCreated = CreateProcess(NULL, hlpCmd.GetBuffer(0), NULL, NULL, TRUE,
				NORMAL_PRIORITY_CLASS, NULL, ".", &si, &pi);
	hlpCmd.ReleaseBuffer();
	if (!bCreated)
		AfxMessageBox("Help command failed!");

	helpProcHandle = pi.hProcess;

	return;
}


void CHelpService::Initialize()
{
	CMap<int, int, CString, CString> *pFieldMap;
	CStdioFile fHelp;
	CString fileName, line;
	CStringArray strings;
	WORD screenID, fieldID;

	fileName = slotDir;
	fileName += "help\\help.map";

	if (! fHelp.Open(fileName, CFile::modeRead|CFile::typeText)) {
		ads_printf("Error opening help map file: %s\n", fileName);
		return;
	}
	while (fHelp.ReadString(line)) {
		line.TrimLeft();
		line.TrimRight();
		if (line == "" || line.GetAt(0) == '#' || line.Left(2) == "//")
			continue;
		utilityHelper.ParseString(line, "|", strings);
		if (strings.GetSize() < 3)
			continue;

		if (strings[0] == "" || strings[1] == "" || strings[2] == "")
			continue;

		screenID = (WORD)atoi(strings[0]);
		fieldID = (WORD)atoi(strings[1]);
		if (m_HelpMap.Lookup(screenID, (void *&)pFieldMap))
			pFieldMap->SetAt(fieldID, strings[2]);
		else {
			pFieldMap = new CMap<int, int, CString, CString>;
			pFieldMap->SetAt(fieldID, strings[2]);
			m_HelpMap.SetAt(screenID, pFieldMap);
		}
	}

	fHelp.Close();
}

void CHelpService::ShowFieldHelp(const CString &helpTopic)
{
	CString topic;
	topic = " -p -i ";
	topic += helpTopic;

	RunHelp(topic);
}

void CHelpService::ShowScreenHelp(const CString &helpTopic)
{
	CString topic;
	topic = " -i ";
	topic += helpTopic;

	RunHelp(topic);

}

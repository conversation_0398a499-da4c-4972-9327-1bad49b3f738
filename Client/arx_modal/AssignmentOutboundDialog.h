#if !defined(AFX_ASSIGNMENTOUTBOUNDDIALOG_H__DE31CA5E_BF3A_11D4_9EC1_00C04FAC149C__INCLUDED_)
#define AFX_ASSIGNMENTOUTBOUNDDIALOG_H__DE31CA5E_BF3A_11D4_9EC1_00C04FAC149C__INCLUDED_

#if _MSC_VER > 1000
#pragma once
#endif // _MSC_VER > 1000
// AssignmentOutboundDialog.h : header file
//
/////////////////////////////////////////////////////////////////////////////
// CAssignmentOutboundDialog dialog

#include "FacilityDataService.h"

class CAssignmentOutboundDialog : public CDialog
{
// Construction
public:
	long m_SectionID;
	CArray<long, long> m_SectionIDList;
	CString m_ThreadMessage;
	int m_ThreadCode;
	long m_ProductGroupID;
	CStringArray m_AssignmentData;
	CAssignmentOutboundDialog(CWnd* pParent = NULL);   // standard constructor
	static UINT GetAssignmentDataThread(LPVOID pParam);
// Dialog Data
	//{{AFX_DATA(CAssignmentOutboundDialog)
	enum { IDD = IDD_ASSIGNMENT_INTERFACE };
	CComboBox	m_SectionCtrl;
	BOOL	m_FullExport;
	//}}AFX_DATA


// Overrides
	// ClassWizard generated virtual function overrides
	//{{AFX_VIRTUAL(CAssignmentOutboundDialog)
	protected:
	virtual void DoDataExchange(CDataExchange* pDX);    // DDX/DDV support
	//}}AFX_VIRTUAL

// Implementation
protected:

	// Generated message map functions
	//{{AFX_MSG(CAssignmentOutboundDialog)
	virtual BOOL OnInitDialog();
	virtual void OnOK();
	afx_msg void OnHelp();
	afx_msg BOOL OnHelpInfo(HELPINFO* pHelpInfo);
	//}}AFX_MSG
	DECLARE_MESSAGE_MAP()
private:
	void Format(CString &assignment);
	void ShowResults(CStringArray &assignmentList, int count);
	int GetFile();
	CString m_FileName;
	CMap<long, long, CString, CString&> m_SectionFacilityMap;
	CMap<long, long, CString, CString&> m_SectionWarehouseMap;
	CFacilityDataService m_FacilityDataService;
};

//{{AFX_INSERT_LOCATION}}
// Microsoft Visual C++ will insert additional declarations immediately before the previous line.

#endif // !defined(AFX_ASSIGNMENTOUTBOUNDDIALOG_H__DE31CA5E_BF3A_11D4_9EC1_00C04FAC149C__INCLUDED_)

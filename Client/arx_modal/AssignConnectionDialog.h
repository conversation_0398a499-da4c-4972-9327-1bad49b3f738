#if !defined(AFX_ASSIGNCONNECTIONDIALOG_H__30270743_5FE6_4B4B_BB3D_4891783205E8__INCLUDED_)
#define AFX_ASSIGNCONNECTIONDIALOG_H__30270743_5FE6_4B4B_BB3D_4891783205E8__INCLUDED_

#if _MSC_VER > 1000
#pragma once
#endif // _MSC_VER > 1000
// AssignConnectionDialog.h : header file
//
#include "ExternalConnection.h"
#include "WMSGroupConnection.h"
#include "WMSGroup.h"

/////////////////////////////////////////////////////////////////////////////
// CAssignConnectionDialog dialog

class CAssignConnectionDialog : public CDialog
{
// Construction
public:
	CWMSGroup *m_pGroup;

	int LoadConnectionsForGroup();
	int AddConnectionToList(CExternalConnection &connection);
	CAssignConnectionDialog(CWnd* pParent = NULL);   // standard constructor
	~CAssignConnectionDialog();
// Dialog Data
	//{{AFX_DATA(CAssignConnectionDialog)
	enum { IDD = IDD_INTEGRATION_ASSIGN_CONNECTION };
	CListCtrl	m_ConnectionListCtrl;
	CTreeCtrl	m_InterfaceTreeCtrl;
	//}}AFX_DATA


// Overrides
	// ClassWizard generated virtual function overrides
	//{{AFX_VIRTUAL(CAssignConnectionDialog)
	protected:
	virtual void DoDataExchange(CDataExchange* pDX);    // DDX/DDV support
	//}}AFX_VIRTUAL

// Implementation
protected:

	// Generated message map functions
	//{{AFX_MSG(CAssignConnectionDialog)
	virtual BOOL OnInitDialog();
	afx_msg void OnAssign();
	afx_msg void OnDelete();
	afx_msg void OnEdit();
	afx_msg void OnNew();
	afx_msg void OnRemove();
	afx_msg void OnHelp();
	afx_msg BOOL OnHelpInfo(HELPINFO* pHelpInfo);
	//}}AFX_MSG
	DECLARE_MESSAGE_MAP()

private:
	CTypedPtrArray<CObArray, CExternalConnection*> m_ConnectionList;
	CMap<int, int, HTREEITEM, HTREEITEM&> m_InterfaceTreeMap;
	int LoadConnectionList();		// Fix by Manohar, inserted 'int' to avoid Warning
	int LoadInterfaceTree();		// Fix by Manohar, inserted 'int' to avoid Warning

};

//{{AFX_INSERT_LOCATION}}
// Microsoft Visual C++ will insert additional declarations immediately before the previous line.

#endif // !defined(AFX_ASSIGNCONNECTIONDIALOG_H__30270743_5FE6_4B4B_BB3D_4891783205E8__INCLUDED_)

// ProfileFrame.cpp : implementation file
//

#include "stdafx.h"
#include "modal.h"
#include "ProfileFrame.h"
#include "ProfileTreePane.h"
#include "ProfileMaintenanceView.h"

#ifdef _DEBUG
#define new DEBUG_NEW
#undef THIS_FILE
static char THIS_FILE[] = __FILE__;
#endif

/////////////////////////////////////////////////////////////////////////////
// CProfileFrame

IMPLEMENT_DYNCREATE(CProfileFrame, CFrameWnd)

CProfileFrame::CProfileFrame()
{
}

CProfileFrame::~CProfileFrame()
{
}


BEGIN_MESSAGE_MAP(CProfileFrame, CFrameWnd)
	//{{AFX_MSG_MAP(CProfileFrame)
		// NOTE - the ClassWizard will add and remove mapping macros here.
	//}}AFX_MSG_MAP
END_MESSAGE_MAP()

/////////////////////////////////////////////////////////////////////////////
// CProfileFrame message handlers

BOOL CProfileFrame::OnCreateClient(LPCREATESTRUCT lpcs, CCreateContext* pContext) 
{
	UNREFERENCED_PARAMETER(pContext);
	CRect rect;

	if (! m_Splitter.CreateStatic(this, 1, 2)) {
		AfxMessageBox("Could not create splitter.");
		return FALSE;
	}

	if (! m_Splitter.CreateView(0, 0, RUNTIME_CLASS(CProfileTreePane), CSize(100, 100), pContext)) {
		AfxMessageBox("Could not create first view.");
		return FALSE;
	}

	
	if (! m_Splitter.CreateView(0, 1, RUNTIME_CLASS(CProfileMaintenanceView), CSize(100, 100), pContext)) {
		AfxMessageBox("Could not create second view.");
		return FALSE;
	}
	
	m_Splitter.SetColumnInfo(0, 100, 10);
	m_Splitter.SetColumnInfo(1, 100, 10);
	
	m_Splitter.RecalcLayout();

	return CFrameWnd::OnCreateClient(lpcs, pContext);
}

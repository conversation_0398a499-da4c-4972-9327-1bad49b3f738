// AutoCADCommands.cpp: implementation of the CAutoCADCommands class.
//
//////////////////////////////////////////////////////////////////////

#include "stdafx.h"
#include "modal.h"
#include "AutoCADCommands.h"
#include "UtilityHelper.h"
#include "Constants.h"
#include "ControlService.h"

#include <errno.h>
#include <math.h>


#include <aced.h>

#include <adscodes.h>
#include <dbsymtb.h>
#include <dbents.h>

#include <actrans.h>


#ifdef _DEBUG
#undef THIS_FILE
static char THIS_FILE[]=__FILE__;
#define new DEBUG_NEW
#endif

extern CUtilityHelper utilityHelper;
extern CControlService controlService;
//////////////////////////////////////////////////////////////////////
// Construction/Destruction
//////////////////////////////////////////////////////////////////////

CAutoCADCommands::CAutoCADCommands()
{

}

CAutoCADCommands::~CAutoCADCommands()
{
	actrTransactionManager->flushGraphics();
	acedUpdateDisplay();
}

/////////////////////////////////////////////////////////////////////
// Function Name : Get_Int_Var
// Classname : None
// Description : get autocad system variable
// Date Created : 10/01/98
// Author : acs
//////////////////////////////////////////////////////////////////////
// Inputs : variableName
// Outputs : value
// Explanation : 
//   Get the value of AutoCAD system variables.
//   
//////////////////////////////////////////////////////////////////////
// Algorithm : None
//////////////////////////////////////////////////////////////////////
// Revisions : 
//   Date-initials : Description_of_revision
//////////////////////////////////////////////////////////////////////
	/*******************************************************
	*
	*                       NOTICE
	*
	*  THIS SOFTWARE IS THE PROPERTY OF AND CONTAINS
	*  CONFIDENTIAL INFORMATION OF SSA GLOBAL 
	*  TECHNOLOGIES, INC., AND SHALL NOT BE COPIED,
	*  USED, NOR DISCLOSED WITHOUT EXPRESS WRITTEN
	*  AUTHORIZATION.  ALTHOUGH PUBLICATION IS NOT
	*  INTENDED, IN THE EVENT OF PUBLICATION, THE
	*  FOLLOWING NOTICE IS APPLICABLE:
	*
	*  (c) COPYRIGHT 2004 SSA GLOBAL TECHNOLOGIES, INC.
	*
	*           SSA GLOBAL TECHNOLOGIES, INC.
	*
	********************************************************/
//////////////////////////////////////////////////////////////////////
short CAutoCADCommands::Get_Int_Var (const char* Varname)
{
    struct resbuf rb ;

    if (ads_getvar (Varname,&rb) == RTNORM) {
        if (rb.restype == RTSHORT) {
            return (rb.resval.rint) ;
        }
    }

    ads_printf ("\nWARNING, GIV, could not get %s",Varname) ;

    return (0) ; // Return something sensible at least
}

//////////////////////////////////////////////////////////////////////
// Function Name : Set_Int_Var
// Classname : None
// Description : set autocad system variable
// Date Created : 10/01/98
// Author : acs
//////////////////////////////////////////////////////////////////////
// Inputs : variableName
// Outputs : value
// Explanation : 
//   Set the value of AutoCAD system variables.
//   
//////////////////////////////////////////////////////////////////////
// Algorithm : None
//////////////////////////////////////////////////////////////////////
// Revisions : 
//   Date-initials : Description_of_revision
//////////////////////////////////////////////////////////////////////
	/*******************************************************
	*
	*                       NOTICE
	*
	*  THIS SOFTWARE IS THE PROPERTY OF AND CONTAINS
	*  CONFIDENTIAL INFORMATION OF SSA GLOBAL 
	*  TECHNOLOGIES, INC., AND SHALL NOT BE COPIED,
	*  USED, NOR DISCLOSED WITHOUT EXPRESS WRITTEN
	*  AUTHORIZATION.  ALTHOUGH PUBLICATION IS NOT
	*  INTENDED, IN THE EVENT OF PUBLICATION, THE
	*  FOLLOWING NOTICE IS APPLICABLE:
	*
	*  (c) COPYRIGHT 2004 SSA GLOBAL TECHNOLOGIES, INC.
	*
	*           SSA GLOBAL TECHNOLOGIES, INC.
	*
	********************************************************/
//////////////////////////////////////////////////////////////////////
void CAutoCADCommands::Set_Int_Var(const char *varName, const short intVal)
{
	struct resbuf rb;
	rb.restype = RTSHORT;
	rb.resval.rint = intVal;
	if (ads_setvar(varName, &rb) != RTNORM)
		ads_printf("Could not set var %s to %d\n", varName, intVal);
}


void CAutoCADCommands::Set_String_Var(const char *varName, char *strVal)
{
	struct resbuf rb;
	rb.restype = RTSTR;
	rb.resval.rstring = strVal;
	if (ads_setvar(varName, &rb) != RTNORM)
		ads_printf("Could not set var %s to %s\n", varName, strVal);
}

void CAutoCADCommands::ModifyDrawing()
{
	AcDbBlockTable *pBlockTable;
	AcDbBlockTableRecord *pBlockTableRecord;
	AcDbText *pTextEntity;
	AcDbObjectId textId;
	AcGePoint3d point;

	point.set(0, 0, 0);

	Acad::ErrorStatus es = acdbCurDwg()->getBlockTable(pBlockTable, AcDb::kForWrite);
	if (es != Acad::eOk)
		return;

	// Open the Model Space Record here...
	es = pBlockTable->getAt(ACDB_MODEL_SPACE, pBlockTableRecord, AcDb::kForWrite);
	if (es != Acad::eOk) {
		pBlockTable->close();
		return;
	}

	pTextEntity = new AcDbText(point, "", AcDbObjectId::kNull, 0.0, 0.0);

	es = pBlockTableRecord->appendAcDbEntity(textId, pTextEntity);	

	pTextEntity->erase();

	pTextEntity->close();

	es = pBlockTableRecord->close();

	es = pBlockTable->close();
	if (es != Acad::eOk)
		return;
	

	return;
}


void CAutoCADCommands::ColorAllObjects(int colorIndex) 
{
	//User wants to reset the facility to normal color.
	//parse the entire drawing and color everything back to normal
	AcDbLayerTable *pLayerTable;
	AcDbObjectId layerId;
	AcDbLayerTableRecord *pLayerRecord;
	AcDbLayerTableIterator *pIterator;
	AcCmColor color;
	color.setColorIndex((unsigned short)colorIndex);
	Acad::ErrorStatus es;

	es = acdbCurDwg()->getLayerTable(pLayerTable, AcDb::kForRead);
	pLayerTable->newIterator(pIterator);//, Adesk::kTrue, Adesk:kTrue);

    for (; !pIterator->done(); pIterator->step()) 
	{
        es = pIterator->getRecord(pLayerRecord, AcDb::kForWrite);
		if (es != Acad::eOk) {
			AfxMessageBox("Error getting layer!");
			return;
		}
		char *layerBuf;
		pLayerRecord->getName(layerBuf);
		CString layerName(layerBuf);
		if (layerName.Find("BAY-") < 0 && layerName.Find("Bay-") < 0) {
			pLayerRecord->close();
			continue;
		}

		pLayerRecord->setColor(color);
		pLayerRecord->close();
	}

	delete pIterator;
	pLayerTable->close();

	Flush();
}


void CAutoCADCommands::ColorDrawingObjectByHandle(CString handle, int colorIndex)
{
	Acad::ErrorStatus es;
	CWinApp * currentApp;
	AcDbHandle objHandle;
	AcDbObjectId objId;
	AcDbEntity *entityPtr;
	AcCmColor color;
	AcDbObjectId layerId;
	
	currentApp = AfxGetApp();
	
	objHandle = handle.GetBuffer(0);
	handle.ReleaseBuffer();

	es = acdbCurDwg()->getAcDbObjectId(objId, FALSE, objHandle);
	if (es != Acad::eOk) {
		ads_printf("Warning - unable to get bay: %s\n", handle);
		return;
	}

	es = acdbOpenAcDbEntity(entityPtr, objId, AcDb::kForWrite);
	if (es != Acad::eOk) {
		ads_printf("Warning - unable to open bay: %s\n", handle);
		return;
	}
	
	//qet the layer on which the bay resides.  We need to
	//set the color on the layer.
	color.setColorIndex((unsigned short)colorIndex);
	entityPtr->setColorIndex((unsigned short) colorIndex);

	CString strLayerName = entityPtr->layer();
	layerId = entityPtr->layerId();
	if (layerId != AcDbObjectId::kNull) {
		AcDbLayerTable *pLayerTable;
		AcDbLayerTableRecord *pLayerRec;
		Acad::ErrorStatus es = acdbCurDwg()->getLayerTable(pLayerTable, AcDb::kForWrite);
		es = pLayerTable->getAt(strLayerName, pLayerRec, AcDb::kForWrite);
		if (es != Acad::eOk) {
			ads_printf("Warning - unable to get layer table for bay: %s\n", handle);
			entityPtr->close();
			return;
		}
		pLayerRec->setColor(color);
		pLayerRec->close();
		pLayerTable->close();
	}
	entityPtr->close();


}


int CAutoCADCommands::GetColorIndexByName(const CString &strColor)
{

	int colorIndex = -1;

	if (strColor.CompareNoCase("Red") == 0)
		colorIndex = kRed;
	else {
		if (strColor.CompareNoCase("Yellow") == 0)
			colorIndex = kYellow;
		else
			if (strColor.CompareNoCase("Green") == 0)
				colorIndex = kGreen;
			else
				if (strColor.CompareNoCase("Cyan") == 0)
					colorIndex = kCyan;
				else
					if (strColor.CompareNoCase("Blue") == 0)
						colorIndex = kBlue;
					else
						if (strColor.CompareNoCase("Magenta") == 0)
							colorIndex = kMagenta;
						else
							if (strColor.CompareNoCase("White") == 0)
								colorIndex = kWhite;	}

	return colorIndex;
}




int CAutoCADCommands::GetSelectedHandle(CString &handle)
{

	AcDbHandle objHandle;
	int res;
	ads_name Set, E_name;
	AcDbObjectId objId;
	AcDbEntity *pEnt;
	char objHandleStr[20];
	Acad::ErrorStatus eStatus;

	strcpy(objHandleStr,"");

//////////////////////////////////////////////////////////////////////
	// Get the bay the user chose
	//////////////////////////////////////////////////////////////////////
	res = ads_ssget("P", NULL, NULL, NULL, Set);
	if (res != RTNORM) {
		ads_printf("Nothing is selected.\n");
		return 0;
	}

	res = ads_ssname(Set, 0, E_name);
	if (res != RTNORM) {
		ads_ssfree(Set);
		ads_printf("Unable to get first object from selection.\n");
		return 0;
	}
	eStatus = acdbGetObjectId(objId, E_name);
	if (eStatus != Acad::eOk) {
		ads_printf("Unable to get object id for selected object.\n");
		return -1;
	}
	eStatus = acdbOpenAcDbEntity(pEnt, objId, AcDb::kForRead);
	if (eStatus != Acad::eOk) {
		ads_printf("Unable to open selected object.\n");
		return -1;
	}
	
	pEnt->getAcDbHandle(objHandle);
	objHandle.getIntoAsciiBuffer(objHandleStr);
	pEnt->close();
	

	ads_ssfree(Set);

	if (strcmp(objHandleStr, "") == 0)
		return 0;

	handle = objHandleStr;

	return 1;


}

int CAutoCADCommands::GetSelectedHandles(CStringArray &handles)
{

	AcDbHandle objHandle;
	int res;
	ads_name Set, E_name;
	long nLength;
	AcDbObjectId objId;
	AcDbEntity *pEnt;
	char objHandleStr[20];
	Acad::ErrorStatus eStatus;
	CString handle;
	char *prompts[2];
	
	prompts[0] = "\nPlease select one or more bays: ";
	prompts[1] = "\nPlease remove one or more bays: ";
	
	strcpy(objHandleStr,"");
	
	//////////////////////////////////////////////////////////////////////
	// Get the bay the user chose
	//////////////////////////////////////////////////////////////////////
	res = ads_ssget("P", NULL, NULL, NULL, Set);
	
	ads_sslength(Set, &nLength);
	if (nLength == 0 || res != RTNORM) {
		ads_printf("No objects selected. Aborting.");
		return -1;
	}
	
	for (int n=0; n < nLength; n++)	{
		
		res = ads_ssname(Set, n, E_name);
		if (res != RTNORM) {
			ads_ssfree(Set);
			ads_printf("Unable to get object from selection.\n");
			return -1;
		}
		eStatus = acdbGetObjectId(objId, E_name);
		if (eStatus != Acad::eOk) {
			ads_printf("Unable to get object id for object.\n");
			return -1;
		}
		eStatus = acdbOpenAcDbEntity(pEnt, objId, AcDb::kForRead);
		if (eStatus != Acad::eOk) {
			ads_printf("Unable to open selected object.\n");
			return -1;
		}
		
		pEnt->getAcDbHandle(objHandle);
		objHandle.getIntoAsciiBuffer(objHandleStr);
		pEnt->close();
		
		handles.Add(objHandleStr);
		
	}
	ads_ssfree(Set);


	return handles.GetSize();

}

int CAutoCADCommands::GetSelectedHandlesPrompt(CStringArray &handles)
{

	AcDbHandle objHandle;
	int res;
	ads_name Set, E_name;
	long nLength;
	AcDbObjectId objId;
	AcDbEntity *pEnt;
	char objHandleStr[20];
	Acad::ErrorStatus eStatus;
	CString handle;
	char *prompts[2];
	
	prompts[0] = "\nPlease select one or more bays: ";
	prompts[1] = "\nPlease remove one or more bays: ";
	
	strcpy(objHandleStr,"");
	
	//////////////////////////////////////////////////////////////////////
	// Get the bay the user chose
	//////////////////////////////////////////////////////////////////////
	res = ads_ssget(NULL, NULL, NULL, NULL, Set);
	
	ads_sslength(Set, &nLength);
	if (nLength == 0 || res != RTNORM) {
		ads_printf("No objects selected. Aborting.");
		return -1;
	}
	
	for (int n=0; n < nLength; n++)	{
		
		res = ads_ssname(Set, n, E_name);
		if (res != RTNORM) {
			ads_ssfree(Set);
			ads_printf("Unable to get object from selection.\n");
			return -1;
		}
		eStatus = acdbGetObjectId(objId, E_name);
		if (eStatus != Acad::eOk) {
			ads_printf("Unable to get object id for object.\n");
			return -1;
		}
		eStatus = acdbOpenAcDbEntity(pEnt, objId, AcDb::kForRead);
		if (eStatus != Acad::eOk) {
			ads_printf("Unable to open selected object.\n");
			return -1;
		}
		
		pEnt->getAcDbHandle(objHandle);
		objHandle.getIntoAsciiBuffer(objHandleStr);
		pEnt->close();
		
		handles.Add(objHandleStr);
		
	}
	ads_ssfree(Set);


	return handles.GetSize();

}

int CAutoCADCommands::DeleteDrawingObjectByHandle(CString handle)
{
	Acad::ErrorStatus es;
	AcDbHandle objHandle;
	AcDbObjectId objId;
	AcDbEntity *entityPtr;
	AcCmColor color;
	AcDbObjectId layerId;

	
	objHandle = handle.GetBuffer(0);
	handle.ReleaseBuffer();

	es = acdbCurDwg()->getAcDbObjectId(objId, FALSE, objHandle);
	if (es != Acad::eOk) {
		ads_printf("Warning - could not find object: %s\n", handle);
		return -1;
	}

	es = acdbOpenAcDbEntity(entityPtr, objId, AcDb::kForWrite);
	if (es != Acad::eOk) {
		ads_printf("Warning - could not open object: %s\n", handle);
		return -1;
	}

	// Get rid of the layer associated with this object
	CString strLayerName = entityPtr->layer();
	layerId = entityPtr->layerId();
	if (layerId != AcDbObjectId::kNull) {
		AcDbLayerTable *pLayerTable;
		AcDbLayerTableRecord *pLayerRec;
		es = acdbCurDwg()->getLayerTable(pLayerTable, AcDb::kForWrite);
		es = pLayerTable->getAt(strLayerName, pLayerRec, AcDb::kForWrite);
		if (es != Acad::eOk) {
			ads_printf("Warning - unable to get layer table record for bay: %s\n", handle);
		}
		else {		
			pLayerRec->erase();
			pLayerRec->close();
		}
		pLayerTable->close();
	}

	// Delete the drawing object
	entityPtr->erase();
	entityPtr->close();
		
	return 1;

}



int CAutoCADCommands::GetColorChoice()
{
	
	struct resbuf *arglist, *rslt=NULL; 
	short color; 
	int rc; 
	
	color = kWhite;
	
	arglist = ads_buildlist(RTSTR, "acad_colordlg", RTSHORT, color, RTNIL, 0 ); 
	
	if (arglist == NULL) {
		// Do error handling 
		ads_printf("Unable to build color dialog.\n");
		return -1;
	}
		
	rc = ads_invoke(arglist, &rslt); 
	
	if (rc != RTNORM) { 
		return -1; 
	} 
	else if (rslt == NULL) { 
		return -1;
	} 
	else { 
		// Save the user-selected color 
		color = rslt->resval.rint; 	
	}

	return color;

}



void CAutoCADCommands::DeleteXData(CString &handle)
{

	AcDbObjectId objId;
	AcDbEntity *pEnt;
	Acad::ErrorStatus eStatus;
	AcDbHandle objHandle;
	
	objHandle = handle.GetBuffer(0);
	handle.ReleaseBuffer();

	eStatus = acdbCurDwg()->getAcDbObjectId(objId, FALSE, objHandle);
	if (eStatus != Acad::eOk) {
		ads_printf("Warning - could not find object: %s\n", handle);
		return ;
	}

	eStatus = acdbOpenAcDbEntity(pEnt, objId, AcDb::kForWrite);
	if (eStatus != Acad::eOk) {
		ads_printf("Unable to open selected object.\n");
		return;
	}

	struct resbuf *rb, *pTemp;  // = ads_buildlist (-3, EXTD_APP_NAME, "Slotting", RTNONE);
	rb = ads_newrb(AcDb::kDxfRegAppName);
	pTemp = rb;
	pTemp->resval.rstring = (char *)malloc(strlen("slotting")+1);
	strcpy(pTemp->resval.rstring, "slotting");
	pTemp = rb->rbnext;
	pEnt->setXData(rb);
	ads_relrb(rb);
	pEnt->close();


	return;
	
}


void CAutoCADCommands::ZoomToHandle(CString handle, int magnification)
{

	CWinApp * currentApp;
	char *objHandle;
	ads_name entityName;
	
	struct resbuf *pRb;

	currentApp = AfxGetApp();
	
	objHandle = handle.GetBuffer(0);

	if (ads_handent(objHandle, entityName) == RTERROR) {
		AfxMessageBox("Unable to find entity.");
		handle.ReleaseBuffer();
		return;
	}

	handle.ReleaseBuffer();

	pRb = ads_entget(entityName);
	double x, y;
	x = y = -1;

	while (pRb != NULL) {
		if (pRb->restype == 10 || pRb->restype == RTPOINT || pRb->restype == RT3DPOINT) {
			x = pRb->resval.rpoint[X];
			y = pRb->resval.rpoint[Y];
			ads_printf("%f,%f\n", x, y);
			break;
		}
		pRb= pRb->rbnext;
	}

	if (x < 0) {
		AfxMessageBox("Not a valid object");
		return;
	}

	HighlightDrawingObjectByHandle(handle);

	CString mag;
	mag.Format("%dX", magnification);

	try {
		ads_command(RTSTR,"_ZOOM", RTSTR, "C", RTPOINT, pRb->resval.rpoint, RTSTR, mag.GetBuffer(0), RTNONE);
	}
	catch (...) {
		AfxMessageBox("Autocad error while zooming");
	}

	mag.ReleaseBuffer();


	return;

}

void CAutoCADCommands::HighlightDrawingObjectByHandle(CString handle)
{
	Acad::ErrorStatus es;
	CWinApp * currentApp;
	AcDbHandle objHandle;
	AcDbObjectId objId;
	AcDbEntity *entityPtr;
	
	currentApp = AfxGetApp();
	
	objHandle = handle.GetBuffer(0);
	handle.ReleaseBuffer();

	es = acdbCurDwg()->getAcDbObjectId(objId, FALSE, objHandle);
	if (es != Acad::eOk) {
		ads_printf("Warning - unable to get bay: %s\n", handle);
		return;
	}

	es = acdbOpenAcDbEntity(entityPtr, objId, AcDb::kForWrite);
	if (es != Acad::eOk) {
		ads_printf("Warning - unable to open bay: %s\n", handle);
		return;
	}
	
	entityPtr->highlight();

	entityPtr->close();


}



void CAutoCADCommands::ListHandles()
{
	AcDbDatabase *pDb;
	pDb = acdbCurDwg();

	AcDbHandle objHandle;
	resbuf *rb;
	int res;
	ads_name Set, E_name;
	long nLength;
	AcDbObjectId objId;
	char objHandleStr[20];
	Acad::ErrorStatus eStatus;
	AcDbEntity *pEnt;
	CString strHandle;
	CString fileName;

	FILE *fp = fopen("c:\\temp\\handles.txt","w");
	

	//////////////////////////////////////////////////////////////////////
	// Get the bay that was chosen
	//////////////////////////////////////////////////////////////////////
	res = ads_ssget("A", NULL, NULL, NULL, Set);
	if (res == RTNORM) {		// get all objects in the drawing
		ads_sslength(Set, &nLength);
		for (int n=0; n < nLength; n++)
		{
			res = ads_ssname(Set, n, E_name);
			if (res != RTNORM)
			{
				ads_ssfree(Set);
				return;
			}
			eStatus = acdbGetObjectId(objId, E_name);
			if (eStatus != Acad::eOk)
			{
				AfxMessageBox("Warning : The autocad object could not be found.\nPlease exit, reopen the facility and try again.");
				return;
			}
			eStatus = acdbOpenAcDbEntity(pEnt, objId, AcDb::kForRead);
			if (eStatus != Acad::eOk)
			{
				AfxMessageBox("Warning : The autocad object could not be found.\nPlease exit, reopen the facility and try again.");
				return;
			}

			pEnt->getAcDbHandle(objHandle);
			objHandle.getIntoAsciiBuffer(objHandleStr);
			ads_printf("%s\n", objHandleStr);
			fprintf(fp,"%s\n",objHandleStr);

			pEnt->close();
			strHandle = objHandleStr;
			ads_redraw(E_name, 1);
			rb = ads_entget(E_name);
		}
		ads_ssfree(Set);
	}

	fclose(fp);

	return;
}

void CAutoCADCommands::RunScript(CStringArray &script, const CString &scriptName)
{
	CString scriptFile;

	scriptFile = getenv("TEMP");
	if (scriptFile == "")
		scriptFile = "c:\\";
	scriptFile += "\\";
	scriptFile += scriptName;
	scriptFile += ".scr";

	FILE *f = fopen(scriptFile.GetBuffer(0), "w");
	scriptFile.ReleaseBuffer();
	if (f == NULL) {
		if (errno != EEXIST) {
			utilityHelper.ShowLastError();
			AfxMessageBox("Error running AutoCAD script.");
			return;
		}
	}
	else {	
		for (int i=0; i < script.GetSize(); ++i)
			fprintf(f, "%s\n", script[i]);
		
		fclose(f);
	}
	ads_command(RTSTR, "_script", RTSTR, scriptFile, RTNONE);

}


//Convert the input index into a color name.
CString CAutoCADCommands::GetColorFromIndex(int nIndex)
{
	CString strColor;

	if (nIndex == 0)
		strColor = "Red";		// color 10
	if (nIndex == 1)
		strColor = "Orange";	// color 30
	if (nIndex == 2)
		strColor = "Yellow";	// color 50
	if (nIndex == 3)
		strColor = "Green";		// color 80
	if (nIndex == 4)
		strColor = "Blue";		// color 162
//	if (nIndex == )
//		strColor = "Indigo";
	if (nIndex == 5)
		strColor = "Violet";	//color 202

	return strColor;
}


//////////////////////////////////////////////////////////////////////
// Function Name : RotatePoint
// Classname : None
// Description : rotate and point based on an angle
// Date Created : 10/01/98
// Author : wbh
//////////////////////////////////////////////////////////////////////
// Inputs : initialPoint, angle
// Outputs : rotatedPoint
// Explanation : 
//   Sweep a point based on an angle (in radians)
//////////////////////////////////////////////////////////////////////
// Algorithm : None
//////////////////////////////////////////////////////////////////////
// Revisions : 
//   Date-initials : Description_of_revision
//////////////////////////////////////////////////////////////////////
	/*******************************************************
	*
	*                       NOTICE
	*
	*  THIS SOFTWARE IS THE PROPERTY OF AND CONTAINS
	*  CONFIDENTIAL INFORMATION OF SSA GLOBAL 
	*  TECHNOLOGIES, INC., AND SHALL NOT BE COPIED,
	*  USED, NOR DISCLOSED WITHOUT EXPRESS WRITTEN
	*  AUTHORIZATION.  ALTHOUGH PUBLICATION IS NOT
	*  INTENDED, IN THE EVENT OF PUBLICATION, THE
	*  FOLLOWING NOTICE IS APPLICABLE:
	*
	*  (c) COPYRIGHT 2004 SSA GLOBAL TECHNOLOGIES, INC.
	*
	*           SSA GLOBAL TECHNOLOGIES, INC.
	*
	********************************************************/
//////////////////////////////////////////////////////////////////////
void CAutoCADCommands::RotatePoint(ads_point initPoint, ads_point &rotPoint, double angle) 
{
	rotPoint[Z] = initPoint[Z];
	rotPoint[X] = initPoint[X] * cos(angle) - initPoint[Y] * sin(angle);
	rotPoint[Y] = initPoint[X] * sin(angle) + initPoint[Y] * cos(angle);
	return;
}


void CAutoCADCommands::ZoomExtentsByHandle(CString &handle)
{
	Acad::ErrorStatus es;
	AcDbHandle objHandle;
	AcDbObjectId objId;
	AcDbEntity *entityPtr;
	ads_point point1, point2;
	AcDbExtents extents;
	AcGePoint3d pt1, pt2;
	
	objHandle = handle.GetBuffer(0);
	handle.ReleaseBuffer();

	es = acdbCurDwg()->getAcDbObjectId(objId, FALSE, objHandle);
	if (es != Acad::eOk) {
		ads_printf("Warning - unable to get bay: %s\n", handle);
		return;
	}

	es = acdbOpenAcDbEntity(entityPtr, objId, AcDb::kForRead);
	if (es != Acad::eOk) {
		ads_printf("Warning - unable to open bay: %s\n", handle);
		return;
	}
	
	if (entityPtr->getGeomExtents(extents) != Acad::eInvalidExtents) {
		point1[X] = extents.minPoint().x - 10;
		point1[Y] = extents.minPoint().y - 10;
		point2[X] = extents.maxPoint().x + 10;
		point2[Y] = extents.maxPoint().y + 10;
	}

	entityPtr->close();

	ads_command(RTSTR, "_ZOOM", RTSTR, "W", RTPOINT, point1, RTPOINT, point2, RTNONE);

	return;
}


int CAutoCADCommands::GetAllDrawingBays(CStringArray &bayList)
{
	Acad::ErrorStatus es;
	AcDbDatabase *pDb;
	pDb = acdbCurDwg();
	AcDbHandle objHandle;
	AcDbObjectId objId;
	char objHandleStr[20];
	//AcDbEntity *pEnt;
	CString strHandle;

	CString bayHandle, handle, tmpStr;
	AcDbBlockReference * pBlockRef;
	AcGePoint3d tempPoint;
	AcDbBlockTable *pBlkTbl;
	AcDbBlockTableRecord *pBlkTblRcd;

	es = acdbCurDwg()->getBlockTable(pBlkTbl, AcDb::kForRead);
	es = pBlkTbl->getAt(ACDB_MODEL_SPACE, pBlkTblRcd, AcDb::kForRead);
	
	es = pBlkTbl->close();
	
	AcDbBlockTableRecordIterator *pBlkTblRcdItr;
	es = pBlkTblRcd->newIterator(pBlkTblRcdItr);
	
	int i=0;
	AcDbEntity *pEnt;

	for (pBlkTblRcdItr->start(); ! pBlkTblRcdItr->done(); pBlkTblRcdItr->step())	{
	


		i++;
		es = pBlkTblRcdItr->getEntity(pEnt, AcDb::kForRead);
		
		if (es != Acad::eOk)
			continue;

		// make sure it's a bay
		CString strLayerName = pEnt->layer();
		if (strLayerName.Find("BAY-") < 0) {
			pEnt->close();
			continue;
		}

		pEnt->getAcDbHandle(objHandle);
		objHandle.getIntoAsciiBuffer(objHandleStr);
		
		
		const char *pCname = pEnt->isA()->name();
		if (strcmp(pCname, "AcDbBlockReference") == 0) {
			pBlockRef = (AcDbBlockReference *)(pEnt);
			tempPoint = pBlockRef->position();
			
			tmpStr.Format("%s|%.2f|%.2f|",objHandleStr,tempPoint.x,tempPoint.y);
			bayList.Add(tmpStr);
		}
		
		
		pEnt->close();

	}
	
	delete pBlkTblRcdItr;
	pBlkTblRcd->close();
	
	return bayList.GetSize();
}

void CAutoCADCommands::Flush()
{
	actrTransactionManager->flushGraphics();
	acedUpdateDisplay();

}

BOOL CAutoCADCommands::IsHandleABay(CString &handle)
{
	Acad::ErrorStatus errorStatus;
	AcDbObjectId objId;
	AcDbEntity *pEntity;

	acdbCurDwg()->getAcDbObjectId(objId, Adesk::kFalse,handle.GetBuffer(0));
	handle.ReleaseBuffer();
	
	errorStatus = acdbOpenAcDbEntity(pEntity, objId, AcDb::kForRead);
	if ( errorStatus != Acad::eOk) {
		CString temp;
		temp.Format("Unable to open to read the drawing object: %s.", handle);
		AfxMessageBox(temp);
		return FALSE;
	}

	CString layerName = pEntity->layer();
	if (layerName.Find("BAY-") >= 0) {
		pEntity->close();
		return TRUE;
	}

	pEntity->close();

	return FALSE;

}

int CAutoCADCommands::GetDrawingObjectCoordinates(CString &handle, AcGePoint3d &point, double &rotation)
{
	AcDbObjectId objId;
	AcDbObject *pObj;
	AcDbBlockReference *pBlockRef;
	Acad::ErrorStatus errorStatus;

	errorStatus = acdbCurDwg()->getAcDbObjectId(objId,Adesk::kFalse,handle.GetBuffer(0));
	handle.ReleaseBuffer();
	if (errorStatus != Acad::eOk)
		return -1;
		
	errorStatus = acdbOpenAcDbObject(pObj, objId, AcDb::kForRead);
	if ( errorStatus != Acad::eOk)
		return -1;

	pBlockRef = (AcDbBlockReference *)(pObj);
	rotation = pBlockRef->rotation();
	// the global coordinates of the old bay
	point = pBlockRef->position();

	pObj->close();

	return 0;

}

CString CAutoCADCommands::GetXDataForObject(CString &handle)
{
	AcDbObjectId objId;
	AcDbObject *pObj;
	Acad::ErrorStatus errorStatus;
	resbuf *rb;

	errorStatus = acdbCurDwg()->getAcDbObjectId(objId,Adesk::kFalse,handle.GetBuffer(0));
	handle.ReleaseBuffer();
	if (errorStatus != Acad::eOk)
		return "";	// Fix by Manohar
		
	errorStatus = acdbOpenAcDbObject(pObj, objId, AcDb::kForRead);
	if ( errorStatus != Acad::eOk)
		return "";	// Fix by Manohar

	rb = pObj->xData();
	if (rb) {
		pObj->close();
		return CString(rb->rbnext->resval.rstring);
	}

	pObj->close();

	return "";
}

int CAutoCADCommands::CreateNextLayer(CString &newLayerName, const CString &layerType)
{

	Acad::ErrorStatus es;
	AcDbLayerTable *pLayerTable;
	CString lastName;
	CString layerMatch;
	layerMatch.Format("%s-", layerType);

	es = acdbCurDwg()->getLayerTable(pLayerTable, AcDb::kForWrite);
	if (es != Acad::eOk) {
		controlService.Log("", "Error(%d) getting layer symbol table.\n", es);
		return -1;
	}
	
	AcDbLayerTableRecord *pLastRecord;
	int nextNum = 1;
	char *nameBuf;

	AcDbLayerTableIterator *pIterator;
	pLayerTable->newIterator(pIterator);
	for (pIterator->start(Adesk::kFalse); ! pIterator->done(); pIterator->step(Adesk::kFalse)) {

		es = pIterator->getRecord(pLastRecord, AcDb::kForRead);
		if (es != Acad::eOk) {
			controlService.Log("", "Error(%d) getting last layer table record.\n", es);
			delete pIterator;
			pLayerTable->close();
			return -1;
		}

		es = pLastRecord->getName(nameBuf);
		if (es != Acad::eOk) {
			controlService.Log("", "Error(%d) getting layer record name.\n", es);
			delete pIterator;
			pLastRecord->close();
			pLayerTable->close();
			return -1;
		}
		
		if (controlService.m_Debug)
			ads_printf("Layer: %s\n", nameBuf);

		pLastRecord->close();

		lastName = nameBuf;
		lastName.MakeUpper();
		layerMatch.MakeUpper();
		int idx = lastName.Find(layerMatch);
		if (idx >= 0) {
			nextNum = atoi(lastName.Mid(lastName.Find("-")+1)) + 1;
			break;
		}
	}

	delete pIterator;

	newLayerName.Format("%s-%d", layerType, nextNum);

	if (controlService.m_Debug)
		ads_printf("New Layer: %s\n", nameBuf);

	pLastRecord = new AcDbLayerTableRecord();
	if (! pLastRecord) {
		pLayerTable->close();
		controlService.Log("", "Error creating new layer table record.\n");
		return -1;
	}
		
	pLastRecord->setName(newLayerName);
	
	AcDbObjectId layerId;

	es = pLayerTable->add(layerId, pLastRecord);
	if (es != Acad::eOk) {
		pLastRecord->close();
		pLayerTable->close();
		controlService.Log("", "Error(%d) adding layer record for drawing.\n", es);
		return -1;
	}
		
	pLastRecord->setIsLocked(FALSE);
	
	
	es = pLastRecord->close();
	if (es != Acad::eOk) {
		pLayerTable->close();
		controlService.Log("", "Error(%d) closing layer record.\n", es);
		return -1;
	}
	
	es = pLayerTable->close();
	if (es != Acad::eOk) {
		controlService.Log("", "Error(%d) closing layer table.\n", es);
		return -1;
	}

	return 0;

}

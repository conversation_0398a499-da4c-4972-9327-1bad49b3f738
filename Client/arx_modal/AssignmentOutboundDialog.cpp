// AssignmentOutboundDialog.cpp : implementation file
//

#include "stdafx.h"
#include <afxmt.h>

#include "modal.h"
#include "AssignmentOutboundDialog.h"
#include "DisplayResults.h"
#include "ProcessingMessage.h"
#include "DisplayCount.h"
#include "ProductGroup.h"
#include "Constants.h"
#include "HelpService.h"
#include "UtilityHelper.h"
#include "SolutionDataService.h"
#include "ControlService.h"

#include "ssa_exception.h"
#include "dbsymtb.h"
#include "FacilityDataService.h"


#ifdef _DEBUG
#define new DEBUG_NEW
#undef THIS_FILE
static char THIS_FILE[] = __FILE__;
#endif

#include "ProductGroupDataService.h"

/////////////////////////////////////////////////////////////////////////////
// CAssignmentOutboundDialog dialog
extern CControlService controlService;

extern CEvent g_ThreadDone;
extern CHelpService helpService;
extern CUtilityHelper utilityHelper;

CAssignmentOutboundDialog::CAssignmentOutboundDialog(CWnd* pParent /*=NULL*/)
	: CDialog(CAssignmentOutboundDialog::IDD, pParent)
{
	//{{AFX_DATA_INIT(CAssignmentOutboundDialog)
	m_FullExport = FALSE;
	//}}AFX_DATA_INIT
}


void CAssignmentOutboundDialog::DoDataExchange(CDataExchange* pDX)
{
	CDialog::DoDataExchange(pDX);
	//{{AFX_DATA_MAP(CAssignmentOutboundDialog)
	DDX_Control(pDX, IDC_SECTION, m_SectionCtrl);
	DDX_Check(pDX, IDC_FULL_EXPORT, m_FullExport);
	//}}AFX_DATA_MAP
}


BEGIN_MESSAGE_MAP(CAssignmentOutboundDialog, CDialog)
	//{{AFX_MSG_MAP(CAssignmentOutboundDialog)
	ON_BN_CLICKED(IDHELP, OnHelp)
	ON_WM_HELPINFO()
	//}}AFX_MSG_MAP
END_MESSAGE_MAP()

/////////////////////////////////////////////////////////////////////////////
// CAssignmentOutboundDialog message handlers

BOOL CAssignmentOutboundDialog::OnInitDialog() 
{
	CDialog::OnInitDialog();
	CRect r;
	CStringArray pgList;
	int rc;
	CProductGroup pProductGroup;
	CProductGroupDataService service;
	CFacilityDataService facilityDataService;

	CComboBox *pProductGroupBox = (CComboBox *)GetDlgItem(IDC_PRODUCT_GROUP);
	
	CComboBox *pComboBox;
	m_FileName = controlService.m_ClientHome;
	m_FileName += "\\Temp\\";
	m_FileName += "dsciprag.dat";

	try {
		rc = service.GetProductGroups(controlService.GetCurrentFacilityDBId(), pgList);
	}
	catch (Ssa_Exception e) {
		utilityHelper.ProcessError("Error getting product groups.", &e);
	}
	catch (...) {
		utilityHelper.ProcessError("Error getting product groups.");
	}

	pProductGroupBox->AddString("All");

	for (int i=0; i < pgList.GetSize(); ++i) {
		pProductGroup.Parse(pgList[i]);
		pProductGroupBox->AddString(pProductGroup.m_Description);
		pProductGroupBox->SetItemData(pProductGroupBox->GetCount()-1, pProductGroup.m_ProductGroupDBID);
	}

	pProductGroupBox->GetWindowRect(&r);
	pProductGroupBox->SetWindowPos(NULL, 0, 0, r.Width(), r.Height()*10, SWP_NOMOVE|SWP_NOZORDER);
	pProductGroupBox->SetCurSel(0);

	pComboBox = (CComboBox *)GetDlgItem(IDC_SECTION);
	pComboBox->GetClientRect(&r);
	pComboBox->SetWindowPos(NULL, 0, 0, r.Width(), r.Height()*12, SWP_NOMOVE|SWP_NOZORDER);

	CStringArray sectionList, strings;
	int nItem;

	try {
		facilityDataService.GetSectionsByFacility(controlService.GetCurrentFacilityDBId(), sectionList);
	}
	catch (Ssa_Exception e) {
		utilityHelper.ProcessError("Error getting list of sections.", &e);
	}
	catch (...) {
		utilityHelper.ProcessError("Error getting list of sections.");
	}

	nItem = pComboBox->AddString("All");
	pComboBox->SetItemData(nItem, 0);

	for (i=0; i < sectionList.GetSize(); ++i) {
		utilityHelper.ParseString(sectionList[i], "|", strings);
		m_SectionID = atol(strings[0]);
		nItem = pComboBox->AddString(strings[1]);
		pComboBox->SetItemData(nItem, m_SectionID);
		m_SectionIDList.Add(m_SectionID);
	}

	pComboBox->SetCurSel(0);


	UpdateData(FALSE);

	return TRUE;  // return TRUE unless you set the focus to a control
	              // EXCEPTION: OCX Property Pages should return FALSE
}

void CAssignmentOutboundDialog::OnOK() 
{
	
	CFile file;
	int rc;
	CStringArray assignmentList;
	UpdateData(TRUE);

	CComboBox *pComboBox = (CComboBox *)GetDlgItem(IDC_PRODUCT_GROUP);

	if (pComboBox->GetCurSel() > 0)
		m_ProductGroupID = pComboBox->GetItemData(pComboBox->GetCurSel());
	else
		m_ProductGroupID = -1;

	pComboBox = (CComboBox *)GetDlgItem(IDC_SECTION);
	m_SectionID = pComboBox->GetItemData(pComboBox->GetCurSel());

	if (GetFile() < 0)
		return;

	if (! file.Open(m_FileName, CFile::modeCreate|CFile::modeWrite)) {
		CString msg;
		msg = "Error opening file: ";
		msg += m_FileName;
		AfxMessageBox(msg);
		return;
	}

	m_AssignmentData.RemoveAll();

	CProcessingMessage dlg("Running Assignment Outbound", this);

	
	try {
		CWaitCursor cwc;

		
		rc = 0;
		CWinThread *pThread = AfxBeginThread(CAssignmentOutboundDialog::GetAssignmentDataThread, this);
		g_ThreadDone.ResetEvent();
		BOOL bThreadDone = false;
		while (TRUE) {
			if ( ! utilityHelper.PeekAndPump() )
				break;
			
			bThreadDone = g_ThreadDone.Lock(0);
			if (bThreadDone)
				break;
		}
		

		rc = m_ThreadCode;
		if (m_ThreadCode < 0) {
			this->MessageBox(m_ThreadMessage, "Error", MB_ICONERROR);
			return;
		}
		
	}
	catch(Ssa_Exception e) 
	{
		char eMsg[1024];
		e.GetMessage(eMsg);
		ads_printf("%s\n", eMsg);
		this->MessageBox("An error occurred while retrieving the assignments.", "Error", MB_ICONERROR);
		return;
	}


	if (rc == 0) {
		this->MessageBox("No assignments were found.", "No records found.", MB_OK);
		return;
	}

	for (int i=0; i < m_AssignmentData.GetSize(); ++i) {	// skip the header
		Format(m_AssignmentData[i]);

		file.Write(m_AssignmentData[i], m_AssignmentData[i].GetLength());
		file.Write("\r\n", 2);
	}

	file.Close();


	CDisplayCount dcDlg(this);
	dcDlg.m_Start = 0;
	dcDlg.m_End = m_AssignmentData.GetSize();

	dcDlg.DoModal();

	rc = dcDlg.m_Count;
	if (rc > 0)
		ShowResults(m_AssignmentData, rc);


	//CDialog::OnOK();
	return;
}

int CAssignmentOutboundDialog::GetFile()
{
	CFileDialog dlgFile(FALSE);
	CString title;
	CString strFilter;
	CString strDefault;

	CString allFilter;
	VERIFY(allFilter.LoadString(AFX_IDS_ALLFILTER));
	strFilter += CString("Interface Files (*.dat)");
	strFilter += (TCHAR)'\0';   
	strFilter += _T("*.dat");
	strFilter += (TCHAR)'\0';
	strFilter += allFilter;
	strFilter += (TCHAR)'\0';   
	strFilter += _T("*.*");
	strFilter += (TCHAR)'\0';   

	dlgFile.m_ofn.lpstrFilter = strFilter;
	dlgFile.m_ofn.lpstrDefExt = "dat";
	dlgFile.m_ofn.lpstrTitle = "Assignment Outbound Interface File";
	dlgFile.m_ofn.lpstrFile = m_FileName.GetBuffer(_MAX_PATH);
	BOOL bResult = dlgFile.DoModal() == IDOK ? TRUE : FALSE;

	
	m_FileName.ReleaseBuffer();
	
	if (! bResult)
		return -1;
	else
		return 0;

}

void CAssignmentOutboundDialog::ShowResults(CStringArray &assignmentList, int count)
{
	CDisplayResults dlg;

	dlg.m_Headers.Add("DC|Warehouse|WMS Product ID|WMS Detail ID|Location|Search Anchor Point|Is Primary|Case Capacity|");
	for (int i=0; i < count; ++i)
		dlg.m_Data.Add(assignmentList[i]);

	CString tmp;
	tmp.Format("Assignment Outbound Results - %s", m_FileName);
	dlg.m_WindowCaptions.Add(tmp);
	dlg.m_Tabs.Add("Assignments");
	dlg.m_OrigColumnSize = 100;


	try {
		dlg.DoModal();
	}
	catch (...) {
		AfxMessageBox("Error displaying assignments.");
	}

	return;
		
}

void CAssignmentOutboundDialog::Format(CString &assignment)
{
	int dc, whse;
	CString prod, loc, sap, strDC, strWarehouse;
	int prodDetail, isPrimary, caseQuantity;
	long sectionID;
	CStringArray strings;
	CFacilityDataService facilityDataService;


	// WMSID|WMS Detail|Location|Search Anchor|IsPrimary|CaseCapacity|

	utilityHelper.ParseString(assignment, "|", strings);
	prod = strings[0];
	prodDetail = atoi(strings[1]);
	loc = strings[2];
	sap = strings[3];
	isPrimary = atoi(strings[4]);
	caseQuantity = atoi(strings[5]);
	if (caseQuantity == SLOT_NIL_INTEGER)
		caseQuantity = 0;

	sectionID = atol(strings[6]);
	
	if (! m_SectionFacilityMap.Lookup(sectionID, strDC)) {
		strDC = facilityDataService.GetDCBySection(controlService.GetCurrentFacilityDBId(), sectionID);
		if (strDC == "")
			strDC = facilityDataService.GetCurrentDC();
		if (strDC == "")
			strDC = "0";
		m_SectionFacilityMap.SetAt(sectionID, strDC);
	}
	dc = atoi(strDC);

	if (! m_SectionWarehouseMap.Lookup(sectionID, strWarehouse)) {
		strWarehouse = facilityDataService.GetWarehouseBySection(controlService.GetCurrentFacilityDBId(), sectionID);
		if (strWarehouse == "")
			strWarehouse = facilityDataService.GetCurrentWarehouse();
		if (strWarehouse == "")
			strWarehouse = "0";
		m_SectionWarehouseMap.SetAt(sectionID, strWarehouse);
	}
	whse = atoi(strWarehouse);

	assignment.Format("%05d|%05d|%18.18s|%05d|%8.8s|%8.8s|%1d|%08d|",
		dc, whse, prod, prodDetail, loc, sap, isPrimary, caseQuantity);

	return;


}

UINT CAssignmentOutboundDialog::GetAssignmentDataThread(LPVOID pParam)
{
	int rc;
	CStringArray pArray;
	CAssignmentOutboundDialog *pDlg = (CAssignmentOutboundDialog *)pParam;
	CSolutionDataService solutionDataService;
	
	try {
		rc = solutionDataService.GetAssignments(pArray, pDlg->m_ProductGroupID, pDlg->m_FullExport, pDlg->m_SectionID);
	}
	catch(Ssa_Exception e) {
		char eMsg[1024];
		e.GetMessage(eMsg);
		ads_printf("%s\n", eMsg);
		pDlg->m_ThreadMessage = "An error occurred while retrieving the sections.";
		pDlg->m_ThreadCode = -1;
		g_ThreadDone.SetEvent();
		return pDlg->m_ThreadCode;
	}

	for (int i=0; i < pArray.GetSize(); ++i)
		pDlg->m_AssignmentData.Add(pArray[i]);

	pDlg->m_ThreadCode = pArray.GetSize();

	g_ThreadDone.SetEvent();

	return rc;

}


void CAssignmentOutboundDialog::OnHelp() 
{
	helpService.ShowScreenHelp(IDD);

	return;
}

BOOL CAssignmentOutboundDialog::OnHelpInfo(HELPINFO* pHelpInfo) 
{
	
	helpService.ShowFieldHelp(IDD, pHelpInfo->iCtrlId);

	return TRUE;
}

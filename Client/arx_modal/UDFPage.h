#if !defined(AFX_UDFPAGE_H__5D30CD44_A927_11D4_9EBD_00C04FAC149C__INCLUDED_)
#define AFX_UDFPAGE_H__5D30CD44_A927_11D4_9EBD_00C04FAC149C__INCLUDED_

#if _MSC_VER > 1000
#pragma once
#endif // _MSC_VER > 1000
// UDFPage.h : header file
//
#include "Resource.h"
#include "UDF.h"
/////////////////////////////////////////////////////////////////////////////
// CUDFPage dialog

class CUDFPage : public CPropertyPage
{
	DECLARE_DYNCREATE(CUDFPage)

// Construction
public:
	CUDFPage();
	~CUDFPage();

	CTypedPtrArray<CObArray, CUDF*> m_UDFs;
	CObArray m_Names;
	CTypedPtrArray<CObArray, CWnd*> m_Values;

// Dialog Data
	//{{AFX_DATA(CUDFPage)
	enum { IDD = IDD_UDF };
		// NOTE - ClassWizard will add data members here.
		//    DO NOT EDIT what you see in these blocks of generated code !
	//}}AFX_DATA


// Overrides
	// ClassWizard generate virtual function overrides
	//{{AFX_VIRTUAL(CUDFPage)
	public:
	virtual BOOL OnApply();
	protected:
	virtual void DoDataExchange(CDataExchange* pDX);    // DDX/DDV support
	virtual BOOL OnNotify(WPARAM wParam, LPARAM lParam, LRESULT* pResult);
	//}}AFX_VIRTUAL

// Implementation
protected:
	// Generated message map functions
	//{{AFX_MSG(CUDFPage)
	afx_msg void OnVScroll(UINT nSBCode, UINT nPos, CScrollBar* pScrollBar);
	afx_msg void OnSize(UINT nType, int cx, int cy);
	virtual BOOL OnInitDialog();
	afx_msg BOOL OnMouseWheel(UINT nFlags, short zDelta, CPoint pt);
	afx_msg BOOL OnHelpInfo(HELPINFO* pHelpInfo);
	//}}AFX_MSG
	DECLARE_MESSAGE_MAP()

private:
	int m_scrollPos;
	int m_bottom;
	void CreateDisplay();
};

//{{AFX_INSERT_LOCATION}}
// Microsoft Visual C++ will insert additional declarations immediately before the previous line.

#endif // !defined(AFX_UDFPAGE_H__5D30CD44_A927_11D4_9EBD_00C04FAC149C__INCLUDED_)

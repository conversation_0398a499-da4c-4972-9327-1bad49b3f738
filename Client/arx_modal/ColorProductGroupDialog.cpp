// ColorProductGroupDialog.cpp : implementation file
//

#include "stdafx.h"
#include "modal.h"
#include "ColorProductGroupDialog.h"
#include "ProductGroupDataService.h"
#include "ssa_exception.h"
#include "Constants.h"
#include "HelpService.h"
#include "UtilityHelper.h"
#include "ControlService.h"
#include "AutoCADCommands.h"
#include "ProgressMessage.h"
#include "DataAccessService.h"

//#include <dbmain.h>
#include <aced.h>
#include <actrans.h>

#ifdef _DEBUG
#define new DEBUG_NEW
#undef THIS_FILE
static char THIS_FILE[] = __FILE__;
#endif



/////////////////////////////////////////////////////////////////////////////
// CColorProductGroupDialog dialog

extern CUtilityHelper utilityHelper;
extern CControlService controlService;

extern CHelpService helpService;

CColorProductGroupDialog::CColorProductGroupDialog(CWnd* pParent /*=NULL*/)
	: CDialog(CColorProductGroupDialog::IDD, pParent)
{
	//{{AFX_DATA_INIT(CColorProductGroupDialog)
	//}}AFX_DATA_INIT
}

CColorProductGroupDialog::~CColorProductGroupDialog()
{
	for (int i=0; i < m_ColorListCtrl.GetCount(); ++i)
		delete (CColorObject *)m_ColorListCtrl.GetItemDataPtr(i);

	for (i=0; i < m_ProductGroupList.GetSize(); ++i)
		delete m_ProductGroupList[i];
}


void CColorProductGroupDialog::DoDataExchange(CDataExchange* pDX)
{
	CDialog::DoDataExchange(pDX);
	//{{AFX_DATA_MAP(CColorProductGroupDialog)
	DDX_Control(pDX, IDC_GROUP_LIST, m_GroupListCtrl);
	DDX_Control(pDX, IDC_COLOR_LIST, m_ColorListCtrl);
	//}}AFX_DATA_MAP
}


BEGIN_MESSAGE_MAP(CColorProductGroupDialog, CDialog)
	//{{AFX_MSG_MAP(CColorProductGroupDialog)
	ON_WM_HELPINFO()
	ON_BN_CLICKED(IDHELP, OnHelp)
	ON_BN_CLICKED(IDC_ASSIGN, OnAssign)
	ON_BN_CLICKED(IDC_REMOVE, OnRemove)
	ON_NOTIFY(TVN_BEGINDRAG, IDC_PRODUCT_GROUP_LIST, OnBegindragProductGroupList)
	ON_BN_CLICKED(IDC_NEW_COLOR, OnNewColor)
	ON_BN_CLICKED(IDC_ASSIGN_ALL, OnAssignAll)
	ON_BN_CLICKED(IDC_REMOVE_ALL, OnRemoveAll)
	ON_BN_CLICKED(IDC_DELETE_COLOR, OnDeleteColor)
	ON_BN_CLICKED(IDAPPLY, OnApply)
	//}}AFX_MSG_MAP
END_MESSAGE_MAP()

/////////////////////////////////////////////////////////////////////////////
// CColorProductGroupDialog message handlers

BOOL CColorProductGroupDialog::OnInitDialog() 
{
	CDialog::OnInitDialog();
	CString temp;

	m_GroupListCtrl.m_CompareFunction = &CColorProductGroupDialog::CompareGroups;
	m_GroupListCtrl.m_GetDrawTextFunction = &CColorProductGroupDialog::GetGroupText;

	LoadColors();
	LoadGroups();

	m_ColorListCtrl.SetCurSel(0);
	if (m_GroupListCtrl.GetCount() > 0)
		m_GroupListCtrl.SetCurSel(0);

	temp = controlService.GetApplicationData("AutoReset", "ColorProductGroups");
	if (temp == "1") {
		CButton *pButton = (CButton *)GetDlgItem(IDC_RESET);
		pButton->SetCheck(1);
	}

	temp = controlService.GetApplicationData("ByProduct", "ColorProductGroups");
	if (temp == "1") {
		CButton *pButton = (CButton *)GetDlgItem(IDC_COLOR_BY_PRODUCT);
		pButton->SetCheck(1);
	}
	





	return TRUE;  // return TRUE unless you set the focus to a control
	              // EXCEPTION: OCX Property Pages should return FALSE
}

BOOL CColorProductGroupDialog::OnHelpInfo(HELPINFO* pHelpInfo) 
{
	helpService.ShowFieldHelp(IDD, pHelpInfo->iCtrlId);

	return FALSE;
}

void CColorProductGroupDialog::OnHelp() 
{
	helpService.ShowScreenHelp(IDD);	
}

void CColorProductGroupDialog::OnOK() 
{

	SaveSettings();

	if (ColorGroups() < 0)
		return;

	AfxMessageBox("Product groups were successfully colored.");

	CDialog::OnOK();
}

void CColorProductGroupDialog::OnAssign() 
{
	int curSel, curColSel;
	CColorObject *pColor;

	curSel = m_GroupListCtrl.GetCurSel();
	if (curSel == LB_ERR) {
		AfxMessageBox("Please select a product group to assign.");
		return;
	}

	curColSel = m_ColorListCtrl.GetCurSel();
	if (curColSel == LB_ERR) {
		AfxMessageBox("Please select a color to assign to the group.");
		return;
	}

	AssignColor(curSel, curColSel);

	// Set the current selection to the next available color
	int origColSel = curColSel;
	curColSel++;
	if (curColSel >= m_ColorListCtrl.GetCount())
		curColSel = 0;
	if (curColSel != origColSel) {
		pColor = (CColorObject *)m_ColorListCtrl.GetItemDataPtr(curColSel);
		
		while (pColor->pItem != NULL) {
			curColSel++;
			if (curColSel == origColSel)
				break;
			if (curColSel >= m_ColorListCtrl.GetCount())
				curColSel = 0;
			pColor = (CColorObject *)m_ColorListCtrl.GetItemDataPtr(curColSel);
		}
	}
	
	if (curColSel != origColSel)
		m_ColorListCtrl.SetCurSel(curColSel);

	// Set the selection to the next available group
	if (m_GroupListCtrl.GetCount() > 0) {
		if (curSel > m_GroupListCtrl.GetCount()-1)
			curSel = 0;
		m_GroupListCtrl.SetCurSel(curSel);
	}

}

void CColorProductGroupDialog::OnRemove() 
{
	int curColSel;
	CColorObject *pColor;
	CProductGroup *pGroup;

	curColSel = m_ColorListCtrl.GetCurSel();
	if (curColSel == LB_ERR) {
		AfxMessageBox("Please select a color to unassign.");
		return;
	}

	pColor = (CColorObject *)m_ColorListCtrl.GetItemDataPtr(curColSel);
	pGroup = (CProductGroup *)pColor->pItem;
	if (pGroup == NULL) {
		AfxMessageBox("Please select a color that is assigned to a group.");
		return;
	}

	RemoveColor(curColSel);

	
}

void CColorProductGroupDialog::OnBegindragProductGroupList(NMHDR* pNMHDR, LRESULT* pResult) 
{
	NM_TREEVIEW* pNMTreeView = (NM_TREEVIEW*)pNMHDR;
	// TODO: Add your control notification handler code here
	
	*pResult = 0;
}

void CColorProductGroupDialog::OnNewColor() 
{
	CColorObject *pColor;
	int nItem;
	int colorIdx = CAutoCADCommands::GetColorChoice();
	BOOL found;

	while (colorIdx >= 0) {
		// See if the color is already in the list
		found = FALSE;
		for (int i=0; i < m_ColorListCtrl.GetCount(); ++i) {
			pColor = (CColorObject *)m_ColorListCtrl.GetItemDataPtr(i);
			if (pColor->colorIndex == colorIdx) {
				AfxMessageBox("The selected color is already in the list.  Please choose a different color.");
				found = TRUE;
				break;
			}
		}

		if (! found) {
			pColor = new CColorObject(colorIdx);
			nItem = m_ColorListCtrl.AddString((LPCTSTR)pColor);
			m_ColorListCtrl.SetItemDataPtr(nItem, (void *)pColor);
			break;
		}
		colorIdx = CAutoCADCommands::GetColorChoice();
	}

	return;

}

void CColorProductGroupDialog::OnAssignAll() 
{
	CProductGroup *pGroup;
	CColorObject *pColor;
	BOOL found;

	int i=0;
	while (i < m_GroupListCtrl.GetCount()) {
		pGroup = (CProductGroup *)m_GroupListCtrl.GetItemDataPtr(i);
		found = FALSE;
		for (int j=0; j < m_ColorListCtrl.GetCount(); ++j) {
			pColor = (CColorObject *)m_ColorListCtrl.GetItemDataPtr(j);
			if (pColor->pItem == NULL) {
				AssignColor(i, j);
				found = TRUE;
				break;
			}
		}
		if (! found)
			break;
	}

}

void CColorProductGroupDialog::OnRemoveAll() 
{
	CColorObject *pColor;

	for (int i=0; i < m_ColorListCtrl.GetCount(); ++i) {
		pColor = (CColorObject *)m_ColorListCtrl.GetItemDataPtr(i);
		if (pColor->pItem == NULL)
			continue;

		RemoveColor(i);

	}

	m_ColorListCtrl.SetCurSel(0);
	if (m_GroupListCtrl.GetCount() > 0)
		m_GroupListCtrl.SetCurSel(0);

}

void CColorProductGroupDialog::RedrawColorItem(int nItem)
{
	CRect r;
	m_ColorListCtrl.GetItemRect(nItem, &r);
	m_ColorListCtrl.RedrawWindow(&r);
}

void CColorProductGroupDialog::AssignColor(int pGroupIdx, int pColorIdx)
{
	CColorObject *pColor;
	CProductGroup *pGroup;

	pColor = (CColorObject *)m_ColorListCtrl.GetItemDataPtr(pColorIdx);
	pGroup = (CProductGroup *)m_GroupListCtrl.GetItemDataPtr(pGroupIdx);
	
	m_GroupListCtrl.DeleteString(pGroupIdx);

	if (pColor->pItem != NULL)
		RemoveColor(pColorIdx);

	pColor->text = pGroup->m_Description;
	pColor->pItem = (void *)pGroup;

	RedrawColorItem(pColorIdx);

}


void CColorProductGroupDialog::RemoveColor(int pColorIdx)
{
	CColorObject *pColor;
	CProductGroup *pGroup;
	int nItem;

	pColor = (CColorObject *)m_ColorListCtrl.GetItemDataPtr(pColorIdx);
	pGroup = (CProductGroup *)pColor->pItem;

	if (pGroup == NULL)
		return;

	nItem = m_GroupListCtrl.AddString((LPCTSTR)pGroup);
	m_GroupListCtrl.SetItemDataPtr(nItem, (void *)pGroup);
	m_GroupListCtrl.SetCurSel(nItem);

	pColor->text = "";
	pColor->pItem = NULL;

	RedrawColorItem(pColorIdx);
}

int CColorProductGroupDialog::CompareGroups(void *p1, void *p2)
{
	CProductGroup *pGroup1, *pGroup2;

	pGroup1 = (CProductGroup *)p1;
	pGroup2 = (CProductGroup *)p2;

	if (pGroup1->m_Priority < pGroup2->m_Priority)
		return -1;
	else 
		return 1;

}

CString CColorProductGroupDialog::GetGroupText(void *pItemData)
{
	CProductGroup *pGroup;
	
	if (pItemData == NULL)
		return "";

	pGroup = (CProductGroup *)pItemData;

	return pGroup->m_Description;

}

int CColorProductGroupDialog::LoadColors()
{
	CColorObject *pColor;
	int nItem;
	CString colors;
	CStringArray colorList;
	CString sql;
	CStringArray results;
	int numOfProdGroups = 0;
	CDataAccessService dataAccessService;
	int colorMap[65] = {1,2,3,4,5,6,8,10,15,20,25,30,40,50,80,130,160,210,240,52,72,132,172,222,51,82,134,12,174,211,244,53,70,120,161,202,180,220,32,102,140,242,61,154,230,60,134,192,223,43,93,153,233,14,54,94,164,246,86,166,41};

	colors = controlService.GetApplicationData("Colors", "ColorProductGroups");
	
	if (colors != "") {
		utilityHelper.ParseString(colors, "|", colorList);
		for (int i=0; i < colorList.GetSize(); ++i) {;
			pColor = new CColorObject(atoi(colorList[i]));
			nItem = m_ColorListCtrl.AddString((LPCTSTR)pColor);
			m_ColorListCtrl.SetItemDataPtr(nItem, (void *)pColor);
		}

		return 0;
	}

	// If no colors are defined, supply some defaults

    
	sql.Format("select count(*) from dbslottinggroup where dbfacilityid = %d", controlService.GetCurrentFacilityDBId());
	dataAccessService.ExecuteQuery("Query1",sql, results, TRUE);
	numOfProdGroups = atoi(results[0]);

	/*pColor = new CColorObject(kRed);		// red
	nItem = m_ColorListCtrl.AddString((LPCTSTR)pColor);
	m_ColorListCtrl.SetItemDataPtr(nItem, (void *)pColor);
	
	pColor = new CColorObject(kOrange);		// orange
	nItem = m_ColorListCtrl.AddString((LPCTSTR)pColor);
	m_ColorListCtrl.SetItemDataPtr(nItem, (void *)pColor);

	pColor = new CColorObject(kYellow);		// yellow 
	nItem = m_ColorListCtrl.AddString((LPCTSTR)pColor);
	m_ColorListCtrl.SetItemDataPtr(nItem, (void *)pColor);

	pColor = new CColorObject(kGreen);		// green
	nItem = m_ColorListCtrl.AddString((LPCTSTR)pColor);
	m_ColorListCtrl.SetItemDataPtr(nItem, (void *)pColor);

	pColor = new CColorObject(kCyan);		// cyan
	nItem = m_ColorListCtrl.AddString((LPCTSTR)pColor);
	m_ColorListCtrl.SetItemDataPtr(nItem, (void *)pColor);	

	pColor = new CColorObject(kBlue);		// blue
	nItem = m_ColorListCtrl.AddString((LPCTSTR)pColor);
	m_ColorListCtrl.SetItemDataPtr(nItem, (void *)pColor);

	pColor = new CColorObject(kMagenta);		// magenta
	nItem = m_ColorListCtrl.AddString((LPCTSTR)pColor);
	m_ColorListCtrl.SetItemDataPtr(nItem, (void *)pColor); */

	for (int i = 0; i < numOfProdGroups; i++)  {
		pColor = new CColorObject(colorMap[i]);
		nItem = m_ColorListCtrl.AddString((LPCTSTR)pColor);
		m_ColorListCtrl.SetItemDataPtr(nItem, (void *)pColor);
	}



	return 0;

}

void CColorProductGroupDialog::OnDeleteColor() 
{
	int curSel;

	curSel = m_ColorListCtrl.GetCurSel();
	if (curSel == LB_ERR) {
		AfxMessageBox("Please select a color to delete.");
		return;
	}

	RemoveColor(curSel);
	m_ColorListCtrl.DeleteString(curSel);

	if (curSel < m_ColorListCtrl.GetCount())
		m_ColorListCtrl.SetCurSel(curSel);

}

int CColorProductGroupDialog::LoadGroups()
{
	CStringArray productGroupList, savedGroupList, savedColor;
	CProductGroupDataService productGroupDataService;
	CString savedGroups, temp;
	CColorObject *pColor;
	long groupId;
	int colorIdx;
	CMap<long, long, int, int> pgMap;
	CMap<int, int, int, int> colorMap;
	CProductGroup *pGroup;
	int nItem, colorListIdx;

	try {
		productGroupDataService.GetProductGroups(controlService.GetCurrentFacilityDBId(), productGroupList);
	}
	catch (Ssa_Exception e) {
		utilityHelper.ProcessError("Error getting product groups from database.", &e);
		return -1;
	}
	catch (...) {
		utilityHelper.ProcessError("Error getting product groups from database.");
		return -1;
	}

	//This sets the horizontal scroll bar to work properly in the listbox control
	CString str, prodGroupName;
	CSize   sz;
	int     dx = 0;

	CDC*    pDC = m_GroupListCtrl.GetDC();

	for (int i=0;i < productGroupList.GetSize();i++)
	{
		prodGroupName = utilityHelper.ParseField(productGroupList[i], 2, "|");
		sz = pDC->GetTextExtent(prodGroupName);
		if (sz.cx > dx)
			dx = sz.cx;
	}
	m_GroupListCtrl.ReleaseDC(pDC);
	
	m_GroupListCtrl.SetHorizontalExtent(dx);

	for (i=0; i < productGroupList.GetSize(); ++i) {
		CProductGroup *pGroup = new CProductGroup;
		pGroup->Parse(productGroupList[i]);
		m_ProductGroupList.Add(pGroup);
		nItem = m_GroupListCtrl.AddString((LPCTSTR)pGroup);
		m_GroupListCtrl.SetItemDataPtr(nItem, (void *)pGroup);
		pgMap.SetAt(pGroup->m_ProductGroupDBID, nItem);
	}
	
	temp.Format("Groups.%s.%d", controlService.m_CurrentDatabase, controlService.GetCurrentFacilityDBId());
	savedGroups = controlService.GetApplicationData(temp, "ColorProductGroups");
	if (savedGroups == "")
		return 0;

	for (i=0; i < m_ColorListCtrl.GetCount(); ++i) {
		pColor = (CColorObject *)m_ColorListCtrl.GetItemDataPtr(i);
		colorMap.SetAt(pColor->colorIndex, i);
	}


	utilityHelper.ParseString(savedGroups, "|", savedGroupList);

	for (i=0; i < savedGroupList.GetSize(); ++i) {
		utilityHelper.ParseString(savedGroupList[i], ",", savedColor);
		groupId = atoi(savedColor[0]);
		colorIdx = atoi(savedColor[1]);

		if (! colorMap.Lookup(colorIdx, colorListIdx))
			continue;

		for (int groupListIdx=0; groupListIdx < m_GroupListCtrl.GetCount(); ++groupListIdx) {
			pGroup = (CProductGroup *)m_GroupListCtrl.GetItemDataPtr(groupListIdx);
			if (pGroup->m_ProductGroupDBID == groupId) {
				AssignColor(groupListIdx, colorListIdx);
				break;
			}
		}
	}



	return 0;

}

int CColorProductGroupDialog::ColorGroups()
{
	CProductGroupDataService productGroupDataService;
	CProductGroup *pGroup;
	CColorObject *pColor;
	CStringArray groupColorList;
	CString temp;
	CButton *pButton = (CButton *)GetDlgItem(IDC_RESET);
	
	if (pButton->GetCheck())
		CAutoCADCommands::ColorAllObjects();

	for (int i=0; i < m_ColorListCtrl.GetCount(); ++i) {
		pColor = (CColorObject *)m_ColorListCtrl.GetItemDataPtr(i);
		if (pColor->pItem == NULL)
			continue;

		pGroup = (CProductGroup *)pColor->pItem;
		temp.Format("%05d|%d|%d", pGroup->m_Priority, pGroup->m_ProductGroupDBID, pColor->colorIndex);
		groupColorList.Add(temp);
	}

	// Sort the groups by priority
	utilityHelper.SortStringArray(groupColorList);
	
	utilityHelper.GetParentWindow()->SetActiveWindow();
	this->ShowWindow(SW_HIDE);

	CProgressMessage progress("Coloring Product Groups...", 0, groupColorList.GetSize(), 1, utilityHelper.GetParentWindow());
	CStringArray strings, handles;

	// Go backwards so the highest priority (lowest number) will be colored
	// if more than one group is in the same bay
	for (i=groupColorList.GetSize()-1; i >= 0; --i) {
		if (progress.IsStopping())
			break;

		utilityHelper.ParseString(groupColorList[i], "|", strings);
		int groupId = atol(strings[1]);
		int colorIdx = atoi(strings[2]);

		handles.RemoveAll();
		try {
			CButton *pButton = (CButton *)GetDlgItem(IDC_COLOR_BY_PRODUCT);
			if (pButton->GetCheck())
				productGroupDataService.GetBayHandlesByProductInGroup(groupId, handles);
			else
				productGroupDataService.GetBayHandlesByProductGroup(groupId, handles);
		}
		catch (Ssa_Exception e) {
			utilityHelper.ProcessError("Error getting bay handles from database.", &e);
			return -1;
		}
		catch (...) {
			utilityHelper.ProcessError("Error getting bay handles from database.");
			return -1;
		}

		for (int j=0; j < handles.GetSize(); ++j) {
			handles[j].TrimRight("|");
			CAutoCADCommands::ColorDrawingObjectByHandle(handles[j], colorIdx);
		}
		
		CAutoCADCommands::Flush();

		progress.Step();

	}
	Sleep(500);
	this->ShowWindow(SW_SHOW);

	return 0;
}


void CColorProductGroupDialog::OnApply() 
{
	SaveSettings();

	int rc = ColorGroups();

	if (rc < 0)
		AfxMessageBox("Error coloring product groups.");
	else
		AfxMessageBox("Product groups were successfully colored.");

}

void CColorProductGroupDialog::SaveSettings()
{
	CColorObject *pColor;
	CString colors, groups, temp;
	CProductGroup *pGroup;
	CButton *pButton = (CButton *)GetDlgItem(IDC_RESET);

	if (pButton->GetCheck())
		controlService.SetApplicationData("AutoReset", "1", "ColorProductGroups");
	else
		controlService.SetApplicationData("AutoReset", "0", "ColorProductGroups");


	pButton = (CButton *)GetDlgItem(IDC_COLOR_BY_PRODUCT);
	if (pButton->GetCheck())
		controlService.SetApplicationData("ByProduct", "1", "ColorProductGroups");
	else
		controlService.SetApplicationData("ByProduct", "0", "ColorProductGroups");

	// Colors are global: idx1|idx2|idx3
	// Groups are by database, facility: groupId1,colorIdx1|groupId2,colorIdx2...
	for (int i=0; i < m_ColorListCtrl.GetCount(); ++i) {
		pColor = (CColorObject *)m_ColorListCtrl.GetItemDataPtr(i);
		temp.Format("%d", pColor->colorIndex);
		colors += temp;
		colors += "|";
		if (pColor->pItem != NULL) {
			pGroup = (CProductGroup *)pColor->pItem;
			temp.Format("%d,%d", pGroup->m_ProductGroupDBID, pColor->colorIndex);
			groups += temp;
			groups += "|";
		}
	}


	controlService.SetApplicationData("Colors", colors, "ColorProductGroups");

	temp.Format("Groups.%s.%d", controlService.m_CurrentDatabase, controlService.GetCurrentFacilityDBId());
	controlService.SetApplicationData(temp, groups, "ColorProductGroups");
}

// EditWnd.cpp : implementation file
//

#include "stdafx.h"
#include "EditWnd.h"
#include "EditGrid.h"

#ifdef _DEBUG
#define new DEBUG_NEW
#undef THIS_FILE
static char THIS_FILE[] = __FILE__;
#endif

/////////////////////////////////////////////////////////////////////////////
// CEditWnd

CEditWnd::CEditWnd()
{
}

CEditWnd::~CEditWnd()
{
}


BEGIN_MESSAGE_MAP(CEditWnd, CEdit)
	//{{AFX_MSG_MAP(CEditWnd)
	ON_WM_CHAR()
	ON_WM_KEYDOWN()
	ON_WM_SETFOCUS()
	ON_WM_KILLFOCUS()
	ON_WM_GETDLGCODE()
	ON_WM_HELPINFO()
	//}}AFX_MSG_MAP
END_MESSAGE_MAP()

/////////////////////////////////////////////////////////////////////////////
// CEditWnd message handlers

void CEditWnd::OnChar(UINT nChar, UINT nRepCnt, UINT nFlags) 
{
	
	if (nChar != 13 && nChar != 9) // && nChar != 45 && nChar != 46) // Ignore ENTER key.
		CEdit::OnChar(nChar, nRepCnt, nFlags);
      
}

void CEditWnd::OnKeyDown(UINT nChar, UINT nRepCnt, UINT nFlags) 
{
	if (nChar == 9) {			// Tab
		CEditGrid *pEditGrid = (CEditGrid *)this->GetParent();
		//pEditGrid->ProcessInsert();
		pEditGrid->ProcessTab();
		return;
	}
	else if (nChar == 45) {		// Insert
		CEditGrid *pEditGrid = (CEditGrid *)this->GetParent();
		pEditGrid->ProcessInsert();
		return;
	}
	else if (nChar == 46) {	// Delete
		CEditGrid *pEditGrid = (CEditGrid *)this->GetParent();
		pEditGrid->ProcessDelete();
		return;
	}
	else if (nChar == 27) {	// Esc means "Cancel".
		SetWindowText("");
		ShowWindow(SW_HIDE);
		GetParent()->SetFocus();
	}
	else if (nChar == 13)  // Enter means "OK".
		GetParent()->SetFocus();


	CEdit::OnKeyDown(nChar, nRepCnt, nFlags);
}

void CEditWnd::OnSetFocus(CWnd* pOldWnd) 
{
	CEdit::OnSetFocus(pOldWnd);
	CEdit::SetSel(0,-1);
	
}

void CEditWnd::OnKillFocus(CWnd* pNewWnd) 
{
	CEdit::OnKillFocus(pNewWnd);
	
	if (pNewWnd != this->GetParent()) {
		CEditGrid *pEditGrid = (CEditGrid *)this->GetParent();
		pEditGrid->OnUpdateGrid(TRUE);
		this->ShowWindow(SW_HIDE);
	}
	
}


UINT CEditWnd::OnGetDlgCode() 
{

	return DLGC_WANTTAB|DLGC_WANTCHARS;

	//return CEdit::OnGetDlgCode();
}

BOOL CEditWnd::OnHelpInfo(HELPINFO* pHelpInfo) 
{
	UNREFERENCED_PARAMETER(pHelpInfo);

	CEditGrid *pParent = (CEditGrid *)this->GetParent();
	pParent->ShowHelp();

	return FALSE;
}

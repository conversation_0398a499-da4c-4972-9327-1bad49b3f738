// LocationOutboundInfo.cpp: implementation of the CLevelProfileInfo class.
//
//////////////////////////////////////////////////////////////////////

#include "stdafx.h"
#include "modal.h"
#include "LocationOutboundInfo.h"

#include "dbsymtb.h"

#ifdef _DEBUG
#undef THIS_FILE
static char THIS_FILE[]=__FILE__;
#define new DEBUG_NEW
#endif

//////////////////////////////////////////////////////////////////////
// Construction/Destruction
//////////////////////////////////////////////////////////////////////

CLevelProfileInfo::CLevelProfileInfo()
{

}

CLevelProfileInfo::~CLevelProfileInfo()
{

	CString key;
	POSITION pos;
	CUDF *udf;
	
	for( pos = m_UDFMap.GetStartPosition(); pos != NULL; ) {
		m_UDFMap.GetNextAssoc( pos, key, (CObject *&)udf);
		delete (CUDF *)udf;
	}
	
	m_UDFMap.RemoveAll();

}

int CLevelProfileInfo::Parse(CString &line)
{
	char *str, *ptr;
	CString tmpLine;

	try {
		line.Replace("||", "| |");
		line.Replace("||", "| |");
	
		tmpLine = line;
		str = tmpLine.GetBuffer(0);
		ptr = strtok(str, "|");

		m_LevelProfileDBID = atoi(ptr);
		ptr = strtok(NULL, "|");
		m_RelativeLevel = atoi(ptr);
		
		ptr = strtok(NULL, "|");
		m_BayType = atoi(ptr);
		
		ptr = strtok(NULL, "|");
		m_IsFloating = atoi(ptr);
	

		tmpLine.ReleaseBuffer();

	}
	catch (...) {
		tmpLine.ReleaseBuffer();
		ads_printf("Error processing level profile list.\n");
		ads_printf("%s\n", line);
		return -1;
	}

	return 0;	
}

void CLevelProfileInfo::AddUDF(CUDF &pUDF)
{
	CUDF *udf = new CUDF;

	*udf = pUDF;
	m_UDFMap.SetAt(udf->m_Name, (CObject *)udf);

}

//////////////////////////////////////////////////////////////////////
// CLocationOutboundInfo Class
//////////////////////////////////////////////////////////////////////

//////////////////////////////////////////////////////////////////////
// Construction/Destruction
//////////////////////////////////////////////////////////////////////

CLocationOutboundInfo::CLocationOutboundInfo()
{

}

CLocationOutboundInfo::~CLocationOutboundInfo()
{

}

int CLocationOutboundInfo::Parse(CString &line)
{
	char *str, *ptr;
	CString tmpLine;
	int isOverridden;

	try {
		line.Replace("||", "| |");
		line.Replace("||", "| |");
	
		tmpLine = line;
		str = tmpLine.GetBuffer(0);
		ptr = strtok(str, "|");

		m_LevelProfileDBID = atoi(ptr);
		ptr = strtok(NULL, "|");
		m_Description = ptr;
		ptr = strtok(NULL, "|");
		m_XCoordinate = atoi(ptr);
		ptr = strtok(NULL, "|");
		m_YCoordinate = atoi(ptr);
		ptr = strtok(NULL, "|");
		m_ZCoordinate = atoi(ptr);
		ptr = strtok(NULL, "|");
		m_HandlingMethod = atoi(ptr);
		ptr = strtok(NULL, "|");
		m_IsSelect = atoi(ptr);
		ptr = strtok(NULL, "|");
		isOverridden = atoi(ptr);
		if (isOverridden == 0) {	// Use the LocationProfile values
			ptr = strtok(NULL, "|");
			m_HandlingMethod = atoi(ptr);
			ptr = strtok(NULL, "|");
			m_IsSelect = atoi(ptr);
		}
		else {
			ptr = strtok(NULL, "|");
			ptr = strtok(NULL, "|");
		}
		ptr = strtok(NULL, "|");
		m_SectionDBID = atol(ptr);


		tmpLine.ReleaseBuffer();

	}
	catch (...) {
		tmpLine.ReleaseBuffer();
		ads_printf("Error processing location list.\n");
		ads_printf("%s\n", line);
		return -1;
	}

	return 0;	
}

CString CLevelProfileInfo::GetUDFValue(const CString &udfName)
{
	CUDF *pUDF;
	if (m_UDFMap.Lookup(udfName, (CObject *&)pUDF) == 0)
		return "";
	else
		return pUDF->m_Value;

}

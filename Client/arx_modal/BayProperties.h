#if !defined(AFX_BAYPROPERTIES_H__6769885D_AA29_11D4_9212_00400542E36B__INCLUDED_)
#define AFX_BAYPROPERTIES_H__6769885D_AA29_11D4_9212_00400542E36B__INCLUDED_

#include "BayProfile.h"

#if _MSC_VER > 1000
#pragma once
#endif // _MSC_VER > 1000
// BayProperties.h : header file
//

/////////////////////////////////////////////////////////////////////////////
// CBayProperties dialog

class CBayProperties : public CPropertyPage
{
	DECLARE_DYNCREATE(CBayProperties)

// Construction
public:
	CBayProperties();
	~CBayProperties();
	CBayProfile *m_pBayProfile;
// Dialog Data
	//{{AFX_DATA(CBayProperties)
	enum { IDD = IDD_BAY_PROPERTIES };
	CString	m_Description;
	CString	m_Profile;
	//}}AFX_DATA


// Overrides
	// ClassWizard generate virtual function overrides
	//{{AFX_VIRTUAL(CBayProperties)
	protected:
	virtual void DoDataExchange(CDataExchange* pDX);    // DDX/DDV support
	virtual BOOL OnNotify(WPARAM wParam, LPARAM lParam, LRESULT* pResult);
	//}}AFX_VIRTUAL

// Implementation
protected:
	// Generated message map functions
	//{{AFX_MSG(CBayProperties)
	afx_msg BOOL OnHelpInfo(HELPINFO* pHelpInfo);
	afx_msg void OnViewProfile();
	//}}AFX_MSG
	DECLARE_MESSAGE_MAP()

};

//{{AFX_INSERT_LOCATION}}
// Microsoft Visual C++ will insert additional declarations immediately before the previous line.

#endif // !defined(AFX_BAYPROPERTIES_H__6769885D_AA29_11D4_9212_00400542E36B__INCLUDED_)

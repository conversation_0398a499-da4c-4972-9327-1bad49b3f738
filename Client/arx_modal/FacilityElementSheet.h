#if !defined(AFX_FACILITYELEMENTSHEET_H__B6AB3A08_FCA4_4877_808A_70FCCEC2A81E__INCLUDED_)
#define AFX_FACILITYELEMENTSHEET_H__B6AB3A08_FCA4_4877_808A_70FCCEC2A81E__INCLUDED_

#if _MSC_VER > 1000
#pragma once
#endif // _MSC_VER > 1000
// FacilityElementSheet.h : header file
//

/////////////////////////////////////////////////////////////////////////////
// CFacilityElementSheet
#define MESSAGE_ACTIVATE WM_USER+1000
#define MESSAGE_DEACTIVATE WM_USER+1001

class CFacilityElementSheet : public CPropertySheet
{
	DECLARE_DYNAMIC(CFacilityElementSheet)

// Construction
public:
	CFacilityElementSheet(UINT nIDCaption, CWnd* pParentWnd = NULL, UINT iSelectPage = 0);
	CFacilityElementSheet(LPCTSTR pszCaption, CWnd* pParentWnd = NULL, UINT iSelectPage = 0);
	CFacilityElementSheet();
// Attributes
public:

	int m_ElementDBId;
	int m_ElementType;
	BOOL m_AllowUpdate;

// Operations
public:

// Overrides
	// ClassWizard generated virtual function overrides
	//{{AFX_VIRTUAL(CFacilityElementSheet)
	public:
	virtual BOOL OnInitDialog();
	protected:
	virtual BOOL OnCommand(WPARAM wParam, LPARAM lParam);
	//}}AFX_VIRTUAL

// Implementation
public:
	virtual ~CFacilityElementSheet();

	// Generated message map functions
protected:
	//{{AFX_MSG(CFacilityElementSheet)
	afx_msg BOOL OnHelpInfo(HELPINFO* pHelpInfo);
	//}}AFX_MSG
	DECLARE_MESSAGE_MAP()

private:
	CButton *m_pActivateButton;
	CButton *m_pDeactivateButton;
	void OnActivate();
	void OnDeactivate();
};

/////////////////////////////////////////////////////////////////////////////

//{{AFX_INSERT_LOCATION}}
// Microsoft Visual C++ will insert additional declarations immediately before the previous line.

#endif // !defined(AFX_FACILITYELEMENTSHEET_H__B6AB3A08_FCA4_4877_808A_70FCCEC2A81E__INCLUDED_)

// LocationNumberingService.h: interface for the CLocationNumberingService class.
//
//////////////////////////////////////////////////////////////////////

#if !defined(AFX_LOCATIONNUMBERINGSERVICE_H__078EA498_2D64_4A8B_9AED_A7174753A2C5__INCLUDED_)
#define AFX_LOCATIONNUMBERINGSERVICE_H__078EA498_2D64_4A8B_9AED_A7174753A2C5__INCLUDED_

#if _MSC_VER > 1000
#pragma once
#endif // _MSC_VER > 1000

#include "TreeElement.h"

class CLocationNumberingService  
{
public:
	CLocationNumberingService();
	virtual ~CLocationNumberingService();
	void GetChangedLocationDescription(CString &oldDescription,
							  CString locationMask,
							  int nameType,
							  CString partName );
	void NumberBays(CArray <int, int&> &bayIndexes, 
		int selIndex, 
		int aisleIndex, 
		CArray <int,int&> &aisleIndexList, 
		int sideSel, 
		CString &leftBayStart, CString &rightBayStart,
		CString &leftLevelStart, CString &rightLevelStart,
		CString &leftLocationStart, CString &rightLocationStart,
		int leftBayScheme, int rightBayScheme,
		int leftLevelScheme, int rightLevelScheme,
		int leftLocationScheme, int rightLocationScheme,
		int bayleftStep, int bayrightStep,
		int levelleftStep, int levelrightStep,
		int locationleftStep, int locationrightStep,
		int levelleftBreak, int levelrightBreak,
		int locationleftBreak, int locationrightBreak,
		int bayPattern, 
		int maxBayLength, 
		TreeElement & changesTree, 
		int sectionIndex,
		CArray <int, int&> &sideSelArray,
		CString &leftBayPattern,
		CString &leftLevelPattern,
		CString &leftLocPattern,
		CString &rightBayPattern,
		CString &rightLevelPattern,
		CString &rightLocPattern);
	
	void BuildPatternList(CStringArray &patternList, CString pattern);

	void RenumberAisles(CDWordArray &aisleDBIDList, TreeElement & changesTree);

	void CLocationNumberingService::FormatLocationAndUpdate(CString locationPart,int fileOffset, CString locationMask,
		CString sectionDesc, CString aisleDesc, CString bayDesc, CString levelDesc );

	void CLocationNumberingService::UpdateLocationDescriptionPart(int locationOffset,
		CString locationMask,
		int nameType,
		CString partName);

	CString CLocationNumberingService::FindNextID(CString &previousID, int stepValue,
		int schemeValue, int firstFlag,
		CString &locationMask, int facilityMember);
	
	CString CLocationNumberingService::FindNextID(CString &previousID, int stepValue,
		int schemeValue, int firstFlag,
		CString &locationMask, int facilityMember, 
		CStringArray &patternList, int patternPos);

	void CLocationNumberingService::numberRightBay(CString & nextrightBayID, CString & previousrightBayID, int bayrightStep,
		CString & locationMask, int levelrightBreak, CString & previousrightLevelID,
		int locationrightBreak, CString & rightLevelStart, CString & rightLocationStart,
		CString & previousrightLocationID,
		CString & nextrightLevelID, CString & nextrightLocationID, int rightLevelScheme, int rightLocationScheme,
		int rightBayScheme, CString & aisleDesc, CString & sectionDesc, CSsaStringArray & levelrightHoldIDs, int startInd,
		int levelrightStep, int direction, int locationrightStep,
		TreeElement & bayTree,
		CStringArray &rightBayPatternList, CStringArray &rightLevelPatternList, CStringArray &rightLocPatternList,
		CWordArray &levelRightPatternHolds);

	void CLocationNumberingService::numberLeftBay(CString & nextleftBayID, CString & previousleftBayID, int bayleftStep,
		CString & locationMask, int levelleftBreak, CString & previousleftLevelID,
		int locationleftBreak, CString & leftLevelStart, CString & leftLocationStart,
		CString & previousleftLocationID,
		CString & nextleftLevelID, CString & nextleftLocationID, int leftLevelScheme, int leftLocationScheme,
		int leftBayScheme, CString & aisleDesc, CString & sectionDesc, CSsaStringArray & levelleftHoldIDs, int startInd,
		int levelleftStep, int direction, int locationleftStep,
		TreeElement & bayTree,
		CStringArray &leftBayPatternList, CStringArray &leftLevelPatternList, CStringArray &leftLocPatternList,
		CWordArray &levelLeftPatternHolds);
};

#endif // !defined(AFX_LOCATIONNUMBERINGSERVICE_H__078EA498_2D64_4A8B_9AED_A7174753A2C5__INCLUDED_)

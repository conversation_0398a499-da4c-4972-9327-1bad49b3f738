// UDFDataService.cpp: implementation of the CUDFDataService class.
//
//////////////////////////////////////////////////////////////////////

#include "stdafx.h"
#include "modal.h"
#include "DataAccessService.h"
#include "UDFDataService.h"
#include "BTreeHelper.h"
#include "TreeElement.h"
#include "UtilityHelper.h"

#ifdef _DEBUG
#undef THIS_FILE
static char THIS_FILE[]=__FILE__;
#define new DEBUG_NEW
#endif

//////////////////////////////////////////////////////////////////////
// Construction/Destruction
//////////////////////////////////////////////////////////////////////

extern TreeElement changesTree;
extern CUtilityHelper utilityHelper;
extern CDataAccessService dataAccessService;

CUDFDataService::CUDFDataService()
{

}

CUDFDataService::~CUDFDataService()
{

}

int CUDFDataService::GetUDFList(int elementType, long parentID, CStringArray &udfList)
{
	CString sql, prefix;

	prefix = GetUDFPrefix(elementType);
	if (prefix == "")
		return -1;

	sql.Format("select l.%sUDFListID, l.Description, l.Type, l.DefaultValue, l.ListValues, %d, %d "
		"from %sUDFList l "
		"where %s = %d "
		"order by l.Description", 
		prefix, parentID, elementType, prefix, 
		prefix == "DBLevProf" ? "DBBayProfileID" : "DBFacilityID",
		parentID);

	return dataAccessService.ExecuteQuery("GetUDFList", sql, udfList);

}

int CUDFDataService::StoreUDF(CUDF *pUDF)
{
	CString sql, prefix, listValues, listTable, valueTable, elementTable, stringListValues;
	CStringArray statements, tempListValues, results;
	CString elementQuery, temp;
	int nextKey;

	prefix = GetUDFPrefix(pUDF->m_ElementType);
	if (prefix == "")
		return -1;

	if (pUDF->m_ListValues.GetSize() > 0) {
		utilityHelper.BuildDelimitedString(pUDF->m_ListValues, listValues, ",");
		tempListValues.RemoveAll();
		for (int i=0; i < pUDF->m_ListValues.GetSize(); ++i) {
			tempListValues.Add("");
			tempListValues[i].Format("'%s'", pUDF->m_ListValues[i]);
		}
		utilityHelper.BuildDelimitedString(tempListValues, stringListValues, ",");
		listValues.TrimRight(",");
		stringListValues.TrimRight(",");
	}
	else {
		listValues = " ";
		stringListValues = " ";
	}

	listTable.Format("%sUDFList", prefix);
	valueTable.Format("%sUDFVal", prefix);


	if (pUDF->m_ListID <= 0) {	
		nextKey = dataAccessService.GetNextKey(listTable, 1);
		
		sql.Format("insert into %s "
			"(%sID, Description, Type, DefaultValue, ListValues, "
			"createdate, changedate, lastuserid, %s) values ("
			"%d, '%s', %d, '%s', '%s', sysdate, sysdate, 1, %d)",
			listTable, listTable,
			pUDF->m_ElementType == UDF_LEVEL_PROFILE ? "DBBayProfileID" : "DBFacilityID",
			nextKey, pUDF->m_Name, pUDF->m_Type, pUDF->m_DefaultValue, listValues,
			pUDF->m_ParentID);
		pUDF->m_ListID = nextKey;
		statements.Add(sql);

		elementTable = GetUDFElementTable(pUDF->m_ElementType);
		BuildUDFElementQuery(pUDF->m_ElementType, pUDF->m_ParentID, elementQuery);
		
		sql.Format("select count(*) "
			"%s", elementQuery);
		dataAccessService.ExecuteQuery("GetUDFElementCount", sql, results);

		if (atol(results[0]) > 0) {
			nextKey = dataAccessService.GetNextKey(valueTable, atol(results[0]));
			
			// rownum starts at 1 so subtract 1 from the next key value
			sql.Format("insert into %s select rownum+%d-1, '%s', sysdate, sysdate, "
				"1, %sID, %d, %d, %f "
				"%s ",
				valueTable, nextKey, pUDF->m_DefaultValue, elementTable, 
				pUDF->m_ListID, pUDF->m_IntegerValue, pUDF->m_FloatValue,
				elementQuery);
			statements.Add(sql);
			
			// Update current tree elements with the new udf
			AddUDFToTree(pUDF);
		}
	}
	else {		// Update existing udf
		sql.Format("update %s set Description = '%s', Type = %d, DefaultValue = '%s', ListValues = '%s' "
			"where %sID = %d", 
			listTable, pUDF->m_Name, pUDF->m_Type, pUDF->m_DefaultValue, listValues,
			listTable, pUDF->m_ListID);
		statements.Add(sql);

		// Make sure there are not values that aren't in the list;
		// if so set them to the default values
		if (pUDF->m_Type == DT_LIST && stringListValues != " ") {
			sql.Format("update %s set value = '%s', integervalue = %d, floatvalue = %f "
				"where %sID = %d "
				"and value not in (%s)", valueTable, pUDF->m_Value,
				pUDF->m_IntegerValue, pUDF->m_FloatValue,
				listTable, pUDF->m_ListID, stringListValues);
			statements.Add(sql);
		} 
		// if the new type is numeric, set the values that are non-numeric
		// to the default
		else if (pUDF->m_Type == DT_INT || pUDF->m_Type == DT_FLOAT) {
			sql.Format("update %s set value = '%s', integervalue = %d, floatvalue = %f "
				"where %sID = %d "
				"and to_char(integervalue) <> value "
				"and to_char(floatvalue) <> value", valueTable, 
				pUDF->m_Value, pUDF->m_IntegerValue, pUDF->m_FloatValue,
				listTable, pUDF->m_ListID);
			statements.Add(sql);
		}


		// Take care of the ones that are currently loaded into the b-tree
		UpdateUDFInTree(pUDF);
	}

	return dataAccessService.ExecuteStatements("StoreUDF", statements);

}

void CUDFDataService::AddUDFToTree(CUDF *pUDF)
{
	int sectionIdx, aisleIdx, sideIdx, bayIdx, levelIdx, locIdx;
	qqhSLOTFacility facility;
	qqhSLOTSection section;
	qqhSLOTAisle aisle;
	qqhSLOTSide side;
	qqhSLOTBay bay;
	qqhSLOTLevel level;
	qqhSLOTLocation location;
	CBTreeHelper btHelper;

	switch (pUDF->m_ElementType) {
	case UDF_FACILITY:
		btHelper.GetBtFacility(changesTree.fileOffset, facility);
		facility.getUDFList().Add(pUDF->Stream());
		btHelper.SetBtFacility(changesTree.fileOffset, facility);
		break;
	case UDF_SECTION:
		for (sectionIdx=0; sectionIdx < changesTree.getSectionCount(); ++sectionIdx) {
			changesTree.getBtSection(sectionIdx, section);
			section.getUDFList().Add(pUDF->Stream());
			changesTree.setBtSection(sectionIdx, section);
		}
		break;
	case UDF_AISLE:
		for (sectionIdx=0; sectionIdx < changesTree.getSectionCount(); ++sectionIdx) {
			for (aisleIdx = 0; aisleIdx < changesTree.getAisleCountForSection(sectionIdx); ++aisleIdx) {
				changesTree.getBtAisle(sectionIdx, aisleIdx, aisle);
				aisle.getUDFList().Add(pUDF->Stream());
				changesTree.setBtAisle(sectionIdx, aisleIdx, aisle);
			}
		}
		break;
	case UDF_SIDE:
		for (sectionIdx=0; sectionIdx < changesTree.getSectionCount(); ++sectionIdx) {
			for (aisleIdx = 0; aisleIdx < changesTree.getAisleCountForSection(sectionIdx); ++aisleIdx) {
				for (sideIdx = 0; sideIdx < changesTree.getSideCountForAisle(sectionIdx, sideIdx); ++sideIdx) {
					changesTree.getBtSide(sectionIdx, aisleIdx, sideIdx, side);
					side.getUDFList().Add(pUDF->Stream());
					changesTree.setBtSide(sectionIdx, aisleIdx, sideIdx, side);
				}
			}
		}
		break;
	case UDF_BAY:
		for (sectionIdx=0; sectionIdx < changesTree.getSectionCount(); ++sectionIdx) {
			for (aisleIdx = 0; aisleIdx < changesTree.getAisleCountForSection(sectionIdx); ++aisleIdx) {
				for (sideIdx = 0; sideIdx < changesTree.getSideCountForAisle(sectionIdx, sideIdx); ++sideIdx) {
					for (bayIdx = 0; bayIdx < changesTree.getBayCountForSide(sectionIdx, aisleIdx, sideIdx); ++bayIdx) {
						changesTree.getBtBay(sectionIdx, aisleIdx, sideIdx, bayIdx, bay);
						bay.getUDFList().Add(pUDF->Stream());
						changesTree.setBtBay(sectionIdx, aisleIdx, sideIdx, bayIdx, bay);
					}
				}
			}
		}
		break;
	case UDF_LEVEL:
		for (sectionIdx=0; sectionIdx < changesTree.getSectionCount(); ++sectionIdx) {
			for (aisleIdx = 0; aisleIdx < changesTree.getAisleCountForSection(sectionIdx); ++aisleIdx) {
				for (sideIdx = 0; sideIdx < changesTree.getSideCountForAisle(sectionIdx, sideIdx); ++sideIdx) {
					for (bayIdx = 0; bayIdx < changesTree.getBayCountForSide(sectionIdx, aisleIdx, sideIdx); ++bayIdx) {
						for (levelIdx = 0; levelIdx < changesTree.getLevelCountForBay(sectionIdx, aisleIdx, sideIdx, bayIdx); ++levelIdx) {
							changesTree.getBtLevel(sectionIdx, aisleIdx, sideIdx, bayIdx, levelIdx, level);
							level.getUDFList().Add(pUDF->Stream());
							changesTree.setBtLevel(sectionIdx, aisleIdx, sideIdx, bayIdx, levelIdx, level);
						}
					}
				}
			}
		}
		break;	
	case UDF_LOCATION:
		for (sectionIdx=0; sectionIdx < changesTree.getSectionCount(); ++sectionIdx) {
			for (aisleIdx = 0; aisleIdx < changesTree.getAisleCountForSection(sectionIdx); ++aisleIdx) {
				for (sideIdx = 0; sideIdx < changesTree.getSideCountForAisle(sectionIdx, sideIdx); ++sideIdx) {
					for (bayIdx = 0; bayIdx < changesTree.getBayCountForSide(sectionIdx, aisleIdx, sideIdx); ++bayIdx) {
						for (levelIdx = 0; levelIdx < changesTree.getLevelCountForBay(sectionIdx, aisleIdx, sideIdx, bayIdx); ++levelIdx) {
							for (locIdx = 0; locIdx < changesTree.getLocationCountForLevel(sectionIdx, aisleIdx, sideIdx, bayIdx, levelIdx); ++locIdx) {
								changesTree.getBtLocation(sectionIdx, aisleIdx, sideIdx, bayIdx, levelIdx, locIdx, location);
								location.getUDFList().Add(pUDF->Stream());
								changesTree.setBtLocation(sectionIdx, aisleIdx, sideIdx, bayIdx, levelIdx, locIdx, location);
							}
						}
					}
				}
			}
		}
		break;
	}

	return;

}


void CUDFDataService::UpdateUDFInTree(CUDF *pUDF)
{
	int sectionIdx, aisleIdx, sideIdx, bayIdx, levelIdx, locIdx;
	qqhSLOTFacility facility;
	qqhSLOTSection section;
	qqhSLOTAisle aisle;
	qqhSLOTSide side;
	qqhSLOTBay bay;
	qqhSLOTLevel level;
	qqhSLOTLocation location;
	CBTreeHelper btHelper;

	switch (pUDF->m_ElementType) {
	case UDF_FACILITY:
		btHelper.GetBtFacility(changesTree.fileOffset, facility);
		UpdateObjectUDF(pUDF, facility);
		btHelper.SetBtFacility(changesTree.fileOffset, facility);
		break;
	case UDF_SECTION:
		for (sectionIdx=0; sectionIdx < changesTree.getSectionCount(); ++sectionIdx) {
			changesTree.getBtSection(sectionIdx, section);
			UpdateObjectUDF(pUDF, section);
			changesTree.setBtSection(sectionIdx, section);
		}
		break;
	case UDF_AISLE:
		for (sectionIdx=0; sectionIdx < changesTree.getSectionCount(); ++sectionIdx) {
			for (aisleIdx = 0; aisleIdx < changesTree.getAisleCountForSection(sectionIdx); ++aisleIdx) {
				changesTree.getBtAisle(sectionIdx, aisleIdx, aisle);
				UpdateObjectUDF(pUDF, aisle);
				changesTree.setBtAisle(sectionIdx, aisleIdx, aisle);
			}
		}
		break;
	case UDF_SIDE:
		for (sectionIdx=0; sectionIdx < changesTree.getSectionCount(); ++sectionIdx) {
			for (aisleIdx = 0; aisleIdx < changesTree.getAisleCountForSection(sectionIdx); ++aisleIdx) {
				for (sideIdx = 0; sideIdx < changesTree.getSideCountForAisle(sectionIdx, sideIdx); ++sideIdx) {
					changesTree.getBtSide(sectionIdx, aisleIdx, sideIdx, side);
					UpdateObjectUDF(pUDF, side);
					changesTree.setBtSide(sectionIdx, aisleIdx, sideIdx, side);
				}
			}
		}
		break;
	case UDF_BAY:
		for (sectionIdx=0; sectionIdx < changesTree.getSectionCount(); ++sectionIdx) {
			for (aisleIdx = 0; aisleIdx < changesTree.getAisleCountForSection(sectionIdx); ++aisleIdx) {
				for (sideIdx = 0; sideIdx < changesTree.getSideCountForAisle(sectionIdx, sideIdx); ++sideIdx) {
					for (bayIdx = 0; bayIdx < changesTree.getBayCountForSide(sectionIdx, aisleIdx, sideIdx); ++bayIdx) {
						changesTree.getBtBay(sectionIdx, aisleIdx, sideIdx, bayIdx, bay);
						UpdateObjectUDF(pUDF, bay);
						changesTree.setBtBay(sectionIdx, aisleIdx, sideIdx, bayIdx, bay);
					}
				}
			}
		}
		break;
	case UDF_LEVEL:
		for (sectionIdx=0; sectionIdx < changesTree.getSectionCount(); ++sectionIdx) {
			for (aisleIdx = 0; aisleIdx < changesTree.getAisleCountForSection(sectionIdx); ++aisleIdx) {
				for (sideIdx = 0; sideIdx < changesTree.getSideCountForAisle(sectionIdx, sideIdx); ++sideIdx) {
					for (bayIdx = 0; bayIdx < changesTree.getBayCountForSide(sectionIdx, aisleIdx, sideIdx); ++bayIdx) {
						for (levelIdx = 0; levelIdx < changesTree.getLevelCountForBay(sectionIdx, aisleIdx, sideIdx, bayIdx); ++levelIdx) {
							changesTree.getBtLevel(sectionIdx, aisleIdx, sideIdx, bayIdx, levelIdx, level);
							UpdateObjectUDF(pUDF, level);
							changesTree.setBtLevel(sectionIdx, aisleIdx, sideIdx, bayIdx, levelIdx, level);
						}
					}
				}
			}
		}
		break;	
	case UDF_LOCATION:
		for (sectionIdx=0; sectionIdx < changesTree.getSectionCount(); ++sectionIdx) {
			for (aisleIdx = 0; aisleIdx < changesTree.getAisleCountForSection(sectionIdx); ++aisleIdx) {
				for (sideIdx = 0; sideIdx < changesTree.getSideCountForAisle(sectionIdx, sideIdx); ++sideIdx) {
					for (bayIdx = 0; bayIdx < changesTree.getBayCountForSide(sectionIdx, aisleIdx, sideIdx); ++bayIdx) {
						for (levelIdx = 0; levelIdx < changesTree.getLevelCountForBay(sectionIdx, aisleIdx, sideIdx, bayIdx); ++levelIdx) {
							for (locIdx = 0; locIdx < changesTree.getLocationCountForLevel(sectionIdx, aisleIdx, sideIdx, bayIdx, levelIdx); ++locIdx) {
								changesTree.getBtLocation(sectionIdx, aisleIdx, sideIdx, bayIdx, levelIdx, locIdx, location);
								UpdateObjectUDF(pUDF, location);
								changesTree.setBtLocation(sectionIdx, aisleIdx, sideIdx, bayIdx, levelIdx, locIdx, location);
							}
						}
					}
				}
			}
		}
		break;
	}

	return;

}

void CUDFDataService::UpdateObjectUDF(CUDF *pUDF, qqhSLOTObject &element)
{
	CUDF origUDF;
	BOOL found = FALSE;
	int idx;

	for (int i=0; i < element.getUDFList().GetSize(); ++i) {
		origUDF.Parse(element.getUDFList()[i]);
		if (origUDF.m_ListID == pUDF->m_ListID) {
			found = TRUE;
			idx = i;
			break;
		}
	}

	if (! found) {	// this shouldn't happen because we only call this from update
		element.getUDFList().Add(pUDF->Stream());
		return;
	}

	// First set the new values to the original - we will change them if necessary
	pUDF->m_Value = origUDF.m_Value;
	pUDF->m_IntegerValue = origUDF.m_IntegerValue;
	pUDF->m_FloatValue = origUDF.m_FloatValue;

	// If the type is list, check to see if the value is in the list, if not use the default
	if (pUDF->m_Type == DT_LIST) {
		found = FALSE;
		for (int j=0; j < pUDF->m_ListValues.GetSize(); ++j) {
			if (pUDF->m_ListValues[j] == pUDF->m_Value) {
				found = TRUE;
				break;
			}
		}
		if (! found) {
			pUDF->m_Value = pUDF->m_DefaultValue;
			// Try to set the numeric values to the original value
			if (utilityHelper.IsNumeric(pUDF->m_Value)) {
				pUDF->m_IntegerValue = atoi(pUDF->m_Value);
				pUDF->m_FloatValue = atof(pUDF->m_Value);
			}
			else {
				pUDF->m_IntegerValue = 0;
				pUDF->m_FloatValue = 0;
			}
		}
	}
	else if ( (pUDF->m_Type == DT_INT || pUDF->m_Type == DT_FLOAT) &&
		(origUDF.m_Type == DT_LIST || origUDF.m_Type == DT_STRING) ) {
		if (utilityHelper.IsNumeric(origUDF.m_Value)) {
			pUDF->m_IntegerValue = atoi(origUDF.m_Value);
			pUDF->m_FloatValue = atof(origUDF.m_Value);
			pUDF->m_Value = origUDF.m_Value;
		}
		else {
			pUDF->m_IntegerValue = atoi(pUDF->m_DefaultValue);
			pUDF->m_FloatValue = atof(pUDF->m_DefaultValue);
			pUDF->m_Value = pUDF->m_DefaultValue;
		}
	}

	element.getUDFList()[idx] = pUDF->Stream();


}

// Instead of the following mess, just leave the values alone
// if they change the type; kept this function in case I
// change my mind
void CUDFDataService::UpdateTreeElementUDF(qqhSLOTObject &element, CUDF *pUDF)
{
	BOOL bFound;
	CUDF origUDF;
	int idx;

	bFound = FALSE;
	// See if the udf is already in the list
	for (int i=0; i < element.getUDFList().GetSize(); ++i) {
		origUDF.Parse(element.getUDFList()[i]);
		if (origUDF.m_ListID == pUDF->m_ListID) {
			bFound = TRUE;
			idx = i;
			break;
		}
	}

	// If it's not already in the list add it
	if (! bFound) {
		element.getUDFList().Add(pUDF->Stream());
		return;
	}

	// If this is a list, make sure the value is still
	// one of the list values; if not set it to the default
	if (pUDF->m_Type == DT_LIST) {
		bFound = FALSE;
		for (int j=0; j < pUDF->m_ListValues.GetSize(); ++j) {
			if (origUDF.m_Value == pUDF->m_ListValues[j]) {
				bFound = TRUE;
				break;
			}
		}
		if (! bFound)
			pUDF->m_Value = pUDF->m_DefaultValue;
	}
	else
		pUDF->m_Value = origUDF.m_Value;
	
	// If the string value is numeric, set the numeric values to it
	// regardless of what the new type is
	if (utilityHelper.IsNumeric(pUDF->m_Value)) {
		pUDF->m_IntegerValue = atoi(origUDF.m_Value);
		pUDF->m_FloatValue = atof(origUDF.m_Value);
	}
	else {
		// the string value is not numeric
		// if the new type is numeric, set the numeric values
		// to the default value
		if (pUDF->m_Type == DT_INT || pUDF->m_Type == DT_FLOAT) {
			pUDF->m_Value = pUDF->m_DefaultValue;
			pUDF->m_IntegerValue = atoi(pUDF->m_DefaultValue);
			pUDF->m_FloatValue = atoi(pUDF->m_DefaultValue);
		}
		// if the new type is not numeric set the numeric values to 0
		else {
			pUDF->m_IntegerValue = 0;
			pUDF->m_FloatValue = 0;
		}
	}

	element.getUDFList()[i] = pUDF->Stream();

	return;
}

CString CUDFDataService::GetUDFPrefix(int elementType)
{

	CString prefix;

	switch (elementType) {
	case UDF_PRODUCT:
		prefix = "DBProdPK";
		break;
	case UDF_PRODUCT_GROUP:
		prefix = "DBSltGrp";
		break;
	case UDF_FACILITY:
		prefix = "DBFac";
		break;
	case UDF_SECTION:
		prefix = "DBSect";
		break;
	case UDF_AISLE:
		prefix = "DBAisle";
		break;
	case UDF_SIDE:
		prefix = "DBSide";
		break;
	case UDF_BAY:
		prefix = "DBBay";
		break;
	case UDF_LEVEL:
		prefix = "DBLevel";
		break;
	case UDF_LOCATION:
		prefix = "DBLoc";
		break;
	case UDF_LEVEL_PROFILE:
		prefix = "DBLevProf";
		break;
	default:
		prefix = "";
		break;
	}

	return prefix;

}

CString CUDFDataService::GetUDFElementTable(int elementType)
{
	CString table;

	switch (elementType) {
	case UDF_PRODUCT:
		table = "DBProductPack";
		break;
	case UDF_PRODUCT_GROUP:
		table = "DBSlottingGroup";
		break;
	case UDF_FACILITY:
		table = "DBFacility";
		break;
	case UDF_SECTION:
		table = "DBSection";
		break;
	case UDF_AISLE:
		table = "DBAisle";
		break;
	case UDF_SIDE:
		table = "DBSide";
		break;
	case UDF_BAY:
		table = "DBBay";
		break;
	case UDF_LEVEL:
		table = "DBLevel";
		break;
	case UDF_LOCATION:
		table = "DBLocation";
		break;
	case UDF_LEVEL_PROFILE:
		table = "DBLevelProfile";
		break;
	default:
		table = "";
		break;
	}

	return table;
}

void CUDFDataService::BuildUDFElementQuery(int elementType, long parentID, CString &elementQuery)
{
	switch (elementType) {
	case UDF_PRODUCT:
		elementQuery.Format("from dbproduct p, dbproductpack pp "
			"where p.dbfacilityid = %d "
			"and pp.dbproductid = p.dbproductid ", parentID);
		break;
	case UDF_PRODUCT_GROUP:
		elementQuery.Format("from dbslottinggroup "
			"where dbfacilityid = %d ", parentID);
		break;
	case UDF_FACILITY:
		elementQuery.Format("from dbfacility "
			"where dbfacilityid = %d ", parentID);
		break;
	case UDF_SECTION:
		elementQuery.Format("from dbsection "
			"where dbfacilityid = %d ", parentID);
		break;
	case UDF_AISLE:
		elementQuery.Format("from dbsection s, dbaisle a "
			"where s.dbfacilityid = %d "
			"and a.dbsectionid = s.dbsectionid ", parentID);
		break;
	case UDF_SIDE:
		elementQuery.Format("from dbsection s, dbaisle a, dbside si "
			"where dbfacilityid = %d "
			"and s.dbsectionid = a.dbsectionid "
			"and a.dbaisleid = si.dbaisleid ", parentID);
		break;
	case UDF_BAY:
		elementQuery.Format("from dbsection s, dbaisle a, dbside si, dbbay b "
			"where dbfacilityid = %d "
			"and s.dbsectionid = a.dbsectionid "
			"and a.dbaisleid = si.dbaisleid "
			"and si.dbsideid = b.dbsideid ", parentID);
		break;
	case UDF_LEVEL:
		elementQuery.Format("from dbsection s, dbaisle a, dbside si, dbbay b, dblevel le "
			"where dbfacilityid = %d "
			"and s.dbsectionid = a.dbsectionid "
			"and a.dbaisleid = si.dbaisleid "
			"and si.dbsideid = b.dbsideid "
			"and b.dbbayid = le.dbbayid ", parentID);
		break;
	case UDF_LOCATION:
		elementQuery.Format("from dbsection s, dbaisle a, dbside si, dbbay b, dblevel le, dblocation l "
			"where dbfacilityid = %d "
			"and s.dbsectionid = a.dbsectionid "
			"and a.dbaisleid = si.dbaisleid "
			"and si.dbsideid = b.dbsideid "
			"and b.dbbayid = le.dbbayid "
			"and le.dblevelid = l.dblevelid ", parentID);
		break;
	case UDF_LEVEL_PROFILE:
		elementQuery.Format("from dblevelprofile lp "
			"where lp.dbbayprofileid = %d ", parentID);
		break;
	}

}


int CUDFDataService::DeleteUDF(long elementType, long listID)
{
	CString sql, prefix;
	CStringArray sqlList;

	prefix = GetUDFPrefix(elementType);
	if (prefix == "")
		return -1;

	sql.Format("delete from %sUDFVal where %sUDFListID = %d",
		prefix, prefix, listID);
	sqlList.Add(sql);

	sql.Format("delete from %sUDFList where %sUDFListID = %d",
		prefix, prefix, listID);
	sqlList.Add(sql);

	DeleteUDFFromTree(elementType, listID);

	return dataAccessService.ExecuteStatements("DeleteUDF", sqlList);

}

void CUDFDataService::DeleteUDFFromTree(long elementType, long listID)
{
	int sectionIdx, aisleIdx, sideIdx, bayIdx, levelIdx, locIdx;
	qqhSLOTFacility facility;
	qqhSLOTSection section;
	qqhSLOTAisle aisle;
	qqhSLOTSide side;
	qqhSLOTBay bay;
	qqhSLOTLevel level;
	qqhSLOTLocation location;
	CBTreeHelper btHelper;

	switch (elementType) {
	case UDF_FACILITY:
		btHelper.GetBtFacility(changesTree.fileOffset, facility);
		DeleteObjectUDF(facility, listID);
		facility.setNotes("changed udf");
		btHelper.SetBtFacility(changesTree.fileOffset, facility);
		break;
	case UDF_SECTION:
		for (sectionIdx=0; sectionIdx < changesTree.getSectionCount(); ++sectionIdx) {
			changesTree.getBtSection(sectionIdx, section);
			DeleteObjectUDF(section, listID);
			changesTree.setBtSection(sectionIdx, section);
		}
		break;
	case UDF_AISLE:
		for (sectionIdx=0; sectionIdx < changesTree.getSectionCount(); ++sectionIdx) {
			for (aisleIdx = 0; aisleIdx < changesTree.getAisleCountForSection(sectionIdx); ++aisleIdx) {
				changesTree.getBtAisle(sectionIdx, aisleIdx, aisle);
				DeleteObjectUDF(aisle, listID);
				changesTree.setBtAisle(sectionIdx, aisleIdx, aisle);
			}
		}
		break;
	case UDF_SIDE:
		for (sectionIdx=0; sectionIdx < changesTree.getSectionCount(); ++sectionIdx) {
			for (aisleIdx = 0; aisleIdx < changesTree.getAisleCountForSection(sectionIdx); ++aisleIdx) {
				for (sideIdx = 0; sideIdx < changesTree.getSideCountForAisle(sectionIdx, sideIdx); ++sideIdx) {
					changesTree.getBtSide(sectionIdx, aisleIdx, sideIdx, side);
					DeleteObjectUDF(side, listID);
					changesTree.setBtSide(sectionIdx, aisleIdx, sideIdx, side);
				}
			}
		}
		break;
	case UDF_BAY:
		for (sectionIdx=0; sectionIdx < changesTree.getSectionCount(); ++sectionIdx) {
			for (aisleIdx = 0; aisleIdx < changesTree.getAisleCountForSection(sectionIdx); ++aisleIdx) {
				for (sideIdx = 0; sideIdx < changesTree.getSideCountForAisle(sectionIdx, sideIdx); ++sideIdx) {
					for (bayIdx = 0; bayIdx < changesTree.getBayCountForSide(sectionIdx, aisleIdx, sideIdx); ++bayIdx) {
						changesTree.getBtBay(sectionIdx, aisleIdx, sideIdx, bayIdx, bay);
						DeleteObjectUDF(bay, listID);
						changesTree.setBtBay(sectionIdx, aisleIdx, sideIdx, bayIdx, bay);
					}
				}
			}
		}
		break;
	case UDF_LEVEL:
		for (sectionIdx=0; sectionIdx < changesTree.getSectionCount(); ++sectionIdx) {
			for (aisleIdx = 0; aisleIdx < changesTree.getAisleCountForSection(sectionIdx); ++aisleIdx) {
				for (sideIdx = 0; sideIdx < changesTree.getSideCountForAisle(sectionIdx, sideIdx); ++sideIdx) {
					for (bayIdx = 0; bayIdx < changesTree.getBayCountForSide(sectionIdx, aisleIdx, sideIdx); ++bayIdx) {
						for (levelIdx = 0; levelIdx < changesTree.getLevelCountForBay(sectionIdx, aisleIdx, sideIdx, bayIdx); ++levelIdx) {
							changesTree.getBtLevel(sectionIdx, aisleIdx, sideIdx, bayIdx, levelIdx, level);
							DeleteObjectUDF(level, listID);
							changesTree.setBtLevel(sectionIdx, aisleIdx, sideIdx, bayIdx, levelIdx, level);
						}
					}
				}
			}
		}
		break;	
	case UDF_LOCATION:
		for (sectionIdx=0; sectionIdx < changesTree.getSectionCount(); ++sectionIdx) {
			for (aisleIdx = 0; aisleIdx < changesTree.getAisleCountForSection(sectionIdx); ++aisleIdx) {
				for (sideIdx = 0; sideIdx < changesTree.getSideCountForAisle(sectionIdx, sideIdx); ++sideIdx) {
					for (bayIdx = 0; bayIdx < changesTree.getBayCountForSide(sectionIdx, aisleIdx, sideIdx); ++bayIdx) {
						for (levelIdx = 0; levelIdx < changesTree.getLevelCountForBay(sectionIdx, aisleIdx, sideIdx, bayIdx); ++levelIdx) {
							for (locIdx = 0; locIdx < changesTree.getLocationCountForLevel(sectionIdx, aisleIdx, sideIdx, bayIdx, levelIdx); ++locIdx) {
								changesTree.getBtLocation(sectionIdx, aisleIdx, sideIdx, bayIdx, levelIdx, locIdx, location);
								DeleteObjectUDF(location, listID);
								changesTree.setBtLocation(sectionIdx, aisleIdx, sideIdx, bayIdx, levelIdx, locIdx, location);
							}
						}
					}
				}
			}
		}
		break;
	}

	return;

}

void CUDFDataService::DeleteObjectUDF(qqhSLOTObject &element, long listID)
{
	CUDF udf;

	for (int i=0; i < element.getUDFList().GetSize(); ++i) {
		udf.Parse(element.getUDFList()[i]);
		if (udf.m_ListID == listID) {
			element.getUDFList().RemoveAt(i);
			return;
		}
	}

	return;

}





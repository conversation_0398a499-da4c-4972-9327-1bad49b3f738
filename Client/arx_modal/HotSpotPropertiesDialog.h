#if !defined(AFX_HOTSPOTPROPERTIESDIALOG_H__354BC27B_6F12_4B30_8953_29BB76C90934__INCLUDED_)
#define AFX_HOTSPOTPROPERTIESDIALOG_H__354BC27B_6F12_4B30_8953_29BB76C90934__INCLUDED_

#if _MSC_VER > 1000
#pragma once
#endif // _MSC_VER > 1000
// HotSpotPropertiesDialog.h : header file
//

/////////////////////////////////////////////////////////////////////////////
// CHotSpotPropertiesDialog dialog

class CHotSpotPropertiesDialog : public CDialog
{
// Construction
public:
	CHotSpotPropertiesDialog(CWnd* pParent = NULL);   // standard constructor

// Dialog Data
	//{{AFX_DATA(CHotSpotPropertiesDialog)
	enum { IDD = IDD_HOTSPOTPROPERTIES };
	CString	m_Coordinates;
	CString	m_section;
	CString	m_Type;
	//}}AFX_DATA


// Overrides
	// ClassWizard generated virtual function overrides
	//{{AFX_VIRTUAL(CHotSpotPropertiesDialog)
	protected:
	virtual void DoDataExchange(CDataExchange* pDX);    // DDX/DDV support
	//}}AFX_VIRTUAL

// Implementation
protected:

	// Generated message map functions
	//{{AFX_MSG(CHotSpotPropertiesDialog)
		// NOTE: the ClassWizard will add member functions here
	//}}AFX_MSG
	DECLARE_MESSAGE_MAP()
};

//{{AFX_INSERT_LOCATION}}
// Microsoft Visual C++ will insert additional declarations immediately before the previous line.

#endif // !defined(AFX_HOTSPOTPROPERTIESDIALOG_H__354BC27B_6F12_4B30_8953_29BB76C90934__INCLUDED_)

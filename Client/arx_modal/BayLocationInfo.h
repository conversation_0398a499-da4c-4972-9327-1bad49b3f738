// BayLocationInfo.h: interface for the CBayLocationInfo class.
//
//////////////////////////////////////////////////////////////////////

#if !defined(AFX_BAYLOCATIONINFO_H__A2909988_5528_4323_8489_15786D767D45__INCLUDED_)
#define AFX_BAYLOCATIONINFO_H__A2909988_5528_4323_8489_15786D767D45__INCLUDED_

#include "3DPoint.h"
#include "TreeElement.h"

#if _MSC_VER > 1000
#pragma once
#endif // _MSC_VER > 1000

class CBayLocationInfo : public CObject  
{
public:
	CBayLocationInfo();
	virtual ~CBayLocationInfo();
	BOOL m_IsSelected;
	double m_LocationSpace;
	double m_Width;
	double m_Height;
	double m_Clearance;

	int m_LocationDBId;
	C3DPoint m_Coordinates;
	CString m_Description;

	CRect m_BoundingRect;

	TreeElement *m_pLocationTreeElement;
};

#endif // !defined(AFX_BAYLOCATIONINFO_H__A2909988_5528_4323_8489_15786D767D45__INCLUDED_)

// BayProfileLocationPage.cpp : implementation file
//

#include "stdafx.h"
#include "modal.h"
#include "BayProfileLocationPage.h"
#include "BayProfileSheet.h"
#include "BayProfileLocationProperties.h"
#include "UtilityHelper.h"
#include "HelpService.h"

#ifdef _DEBUG
#define new DEBUG_NEW
#undef THIS_FILE
static char THIS_FILE[] = __FILE__;
#endif

extern CUtilityHelper utilityHelper;
extern CHelpService helpService;
/////////////////////////////////////////////////////////////////////////////
// CBayProfileLocationPage property page

IMPLEMENT_DYNCREATE(CBayProfileLocationPage, CPropertyPage)

CBayProfileLocationPage::CBayProfileLocationPage() : CPropertyPage(CBayProfileLocationPage::IDD)
{
	//{{AFX_DATA_INIT(CBayProfileLocationPage)
	m_Dimensions = _T("");
	m_FacingGap = _T("");
	m_FacingSnap = _T("");
	m_MinimumWidth = _T("");
	m_LocationsAcross = _T("");
	m_LocationsDeep = _T("");
	m_ProductGap = _T("");
	m_ProductSnap = _T("");
	m_LocationSpace = _T("");
	m_LocationUsage = _T("");
	m_HandlingMethod = _T("");
	m_LocationCount = _T("");
	//}}AFX_DATA_INIT
}

CBayProfileLocationPage::~CBayProfileLocationPage()
{
}

void CBayProfileLocationPage::DoDataExchange(CDataExchange* pDX)
{
	CPropertyPage::DoDataExchange(pDX);
	//{{AFX_DATA_MAP(CBayProfileLocationPage)
	DDX_Control(pDX, IDC_LEVEL_LIST, m_LevelListCtrl);
	DDX_Control(pDX, IDC_LEVEL_BUTTON, m_LevelButton);
	DDX_Text(pDX, IDC_DIMENSIONS, m_Dimensions);
	DDX_Text(pDX, IDC_FACING_GAP, m_FacingGap);
	DDX_Text(pDX, IDC_FACING_SNAP, m_FacingSnap);
	DDX_Text(pDX, IDC_MINIMUM_WIDTH, m_MinimumWidth);
	DDX_Text(pDX, IDC_LOCATIONS_ACROSS, m_LocationsAcross);
	DDX_Text(pDX, IDC_LOCATIONS_DEEP, m_LocationsDeep);
	DDX_Text(pDX, IDC_PRODUCT_GAP, m_ProductGap);
	DDX_Text(pDX, IDC_PRODUCT_SNAP, m_ProductSnap);
	DDX_Text(pDX, IDC_SURROUNDING_SPACE, m_LocationSpace);
	DDX_Text(pDX, IDC_LOCATION_USAGE, m_LocationUsage);
	DDX_Text(pDX, IDC_HANDLING_METHOD, m_HandlingMethod);
	DDX_Text(pDX, IDC_LOCATION_COUNT, m_LocationCount);
	//}}AFX_DATA_MAP
}


BEGIN_MESSAGE_MAP(CBayProfileLocationPage, CPropertyPage)
	//{{AFX_MSG_MAP(CBayProfileLocationPage)
	ON_WM_CONTEXTMENU()
	ON_CBN_SELCHANGE(IDC_LEVEL_LIST, OnSelchangeLevelList)
	ON_BN_CLICKED(IDC_PROPERTIES, OnProperties)
	ON_MESSAGE(WM_SELECT_LEVEL, (LRESULT (AFX_MSG_CALL CWnd::*)(WPARAM, LPARAM))OnSelectLevel)
	ON_MESSAGE(WM_DBLCLK_LEVEL, (LRESULT (AFX_MSG_CALL CWnd::*)(WPARAM, LPARAM))OnDblClkLevel)
	ON_WM_HELPINFO()
	//}}AFX_MSG_MAP
END_MESSAGE_MAP()

/////////////////////////////////////////////////////////////////////////////
// CBayProfileLocationPage message handlers

BOOL CBayProfileLocationPage::OnInitDialog() 
{
	CPropertyPage::OnInitDialog();
	
	// TODO: Add extra initialization here
	
	return TRUE;  // return TRUE unless you set the focus to a control
	              // EXCEPTION: OCX Property Pages should return FALSE
}

void CBayProfileLocationPage::OnContextMenu(CWnd* pWnd, CPoint point) 
{
	// TODO: Add your message handler code here
	
}

BOOL CBayProfileLocationPage::OnSetActive() 
{
	CBayProfileSheet *pSheet = (CBayProfileSheet *)GetParent();

	m_pBayProfile = pSheet->m_pBayProfile;

	m_Validating = FALSE;

	m_LevelButton.m_CrossbarList.RemoveAll();
	m_LevelButton.m_UprightHeight = m_pBayProfile->m_UprightHeight;
	m_LevelButton.m_BayHeight = m_pBayProfile->m_Height;
	m_LevelButton.m_BayWidth = m_pBayProfile->m_Width;	

	for (int i=0; i < m_pBayProfile->m_LevelProfileList.GetSize(); ++i) {
		CLevelProfile *pLevelProfile = m_pBayProfile->m_LevelProfileList[i];

		CBayProfileCrossbarInfo info;
		info.m_Clearance = pLevelProfile->m_Clearance;
		info.m_Height = pLevelProfile->m_Coordinates.m_Z;
		info.m_IsHidden = pLevelProfile->m_IsBarHidden;
		info.m_IsSelected = (i == pSheet->m_SelectedLevel);
		info.m_LocationCount = pLevelProfile->m_LocationProfileList.GetSize();
		info.m_MinimumWidth = pLevelProfile->m_MinimumLocWidth;
		info.m_LocationRowCount = pLevelProfile->m_LocationRowCount;

		if (info.m_LocationCount == 0)
			info.m_LocationSpace = 0;
		else
			info.m_LocationSpace = pLevelProfile->m_LocationProfileList[0]->m_LocationSpace;
		info.m_Thickness = pLevelProfile->m_Thickness;
		
		m_LevelButton.m_CrossbarList.Add(info);

	}

	RebuildLevelList();

	m_LevelListCtrl.SetCurSel(pSheet->m_SelectedLevel);
	UpdateScreenFromLevelProfile(pSheet->m_SelectedLevel);
	m_LevelButton.Invalidate();
	
	return CPropertyPage::OnSetActive();	
}

BOOL CBayProfileLocationPage::OnKillActive() 
{
	CBayProfileSheet *pSheet = (CBayProfileSheet *)GetParent();
	
	if (! Validate())
		return FALSE;

	return CPropertyPage::OnKillActive();
}

void CBayProfileLocationPage::OnSelchangeLevelList() 
{
	int curSel = m_LevelListCtrl.GetCurSel();
	if (curSel < 0)
		return;

	if (m_LevelButton.SelectLevel(curSel) < 0) {
		// Set the selection back to the previous which
		// is kept on the parent if it fails
		CBayProfileSheet *pSheet = (CBayProfileSheet *)GetParent();
		m_LevelListCtrl.SetCurSel(pSheet->m_SelectedLevel);
	}		
}

void CBayProfileLocationPage::OnProperties() 
{
	CBayProfileLocationProperties dlg;
	CBayProfileSheet *pSheet = (CBayProfileSheet *)GetParent();
	CLevelProfile *pLevelProfile = m_pBayProfile->m_LevelProfileList[pSheet->m_SelectedLevel];

	if (pLevelProfile->m_IsBarHidden) {
		AfxMessageBox("Locations cannot be defined above a hidden crossbar.");
		return;
	}

	dlg.m_CurrentLevel = pSheet->m_SelectedLevel;
	try {
		if (dlg.DoModal() == IDCANCEL)
			return;
	}
	catch (...) {
		utilityHelper.ProcessError("Error displaying location properties dialog.");
		return;
	}

	UpdateScreenFromLevelProfile(pSheet->m_SelectedLevel);	
	

	CBayProfileCrossbarInfo &info = m_LevelButton.m_CrossbarList[pSheet->m_SelectedLevel];
	info.m_LocationCount = pLevelProfile->m_LocationProfileList.GetSize();
	if (info.m_LocationCount > 0)
		info.m_LocationSpace = pLevelProfile->m_LocationProfileList[0]->m_LocationSpace;
	else
		info.m_LocationSpace = 0;

	info.m_MinimumWidth = pLevelProfile->m_MinimumLocWidth;
	info.m_LocationRowCount = pLevelProfile->m_LocationRowCount;

	m_LevelButton.Invalidate();

}

int CBayProfileLocationPage::OnSelectLevel(WPARAM wParam, LPARAM lParam)
{
	UNREFERENCED_PARAMETER(lParam);

	CBayProfileSheet *pSheet = (CBayProfileSheet *)GetParent();
	int newLevel = wParam;

	if (pSheet->m_SelectedLevel != newLevel) {	
		
		if (newLevel >= 0) {
			if (UpdateScreenFromLevelProfile(newLevel) < 0)
				return -1;
		}
		
		pSheet->m_SelectedLevel = newLevel;
	}

	m_LevelListCtrl.SetCurSel(newLevel);

	return 0;
}

int CBayProfileLocationPage::OnDblClkLevel(WPARAM wParam, LPARAM lParam)
{
	OnProperties();

	return 0;
}

void CBayProfileLocationPage::RebuildLevelList()
{
	int curSel = m_LevelListCtrl.GetCurSel();

	m_LevelListCtrl.ResetContent();
	for (int i=0; i < m_pBayProfile->m_LevelProfileList.GetSize(); ++i) {
		m_pBayProfile->m_LevelProfileList[i]->m_Description.Format("%d", i+1);
		m_pBayProfile->m_LevelProfileList[i]->m_RelativeLevel = i+1;
		CString temp;
		if (i == 0)
			temp = "Level 1 - Floor";
		else
			temp.Format("Level: %d - Position: %.0f", i+1, m_pBayProfile->m_LevelProfileList[i]->m_Coordinates.m_Z);
		int nItem = m_LevelListCtrl.AddString(temp);
		//m_LevelListCtrl.SetItemData(nItem, m_pBayProfile->m_LevelProfileList[i]->m_Coordinates.m_Z);
	}

	if (curSel >= m_LevelListCtrl.GetCount())
		curSel = m_LevelListCtrl.GetCount()-1;

	m_LevelListCtrl.SetCurSel(curSel);

	CRect r;
	m_LevelListCtrl.GetWindowRect(&r);
	m_LevelListCtrl.SetWindowPos(NULL, 0, 0, r.Width(), 
		r.Height()*(m_pBayProfile->m_LevelProfileList.GetSize()+1), SWP_NOMOVE|SWP_NOZORDER);

}

int CBayProfileLocationPage::UpdateScreenFromLevelProfile(int currentLevel)
{
	CLevelProfile *pLevelProfile = m_pBayProfile->m_LevelProfileList[currentLevel];
	
	m_FacingGap.Format("%.2f", pLevelProfile->m_FacingGap);
	m_FacingSnap.Format("%.2f", pLevelProfile->m_FacingSnap);

	m_MinimumWidth.Format("%.2f",  pLevelProfile->m_MinimumLocWidth);
	m_ProductGap.Format("%.2f", pLevelProfile->m_ProductGap);
	m_ProductSnap.Format("%.2f", pLevelProfile->m_ProductSnap);
	m_LocationCount.Format("%d", pLevelProfile->m_LocationProfileList.GetSize());
	m_LocationsAcross.Format("%d", 
		pLevelProfile->m_LocationProfileList.GetSize()/pLevelProfile->m_LocationRowCount);
	m_LocationsDeep.Format("%d", pLevelProfile->m_LocationRowCount);

	int handlingMethod;
	if (pLevelProfile->m_LocationProfileList.GetSize() > 0) {
		m_LocationSpace.Format("%.2f", pLevelProfile->m_LocationProfileList[0]->m_LocationSpace);
		m_LocationUsage.Format("%s", pLevelProfile->m_LocationProfileList[0]->m_IsSelect ? "Select" : "Reserve");
		handlingMethod = pLevelProfile->m_LocationProfileList[0]->m_HandlingMethod;
		m_HandlingMethod.Format("%s", handlingMethod == PALLET_HANDLING ? "Pallet" : "Case");

		if (m_pBayProfile->m_BayType == BAYTYPE_PALLET && m_pBayProfile->m_PalletDepth > 1)
			m_Dimensions.Format("%.0f x %.0f (%d x %.0f) x %.0f",
				pLevelProfile->m_LocationProfileList[0]->m_Width-.5,
				pLevelProfile->m_LocationProfileList[0]->m_Depth-.5,
				m_pBayProfile->m_PalletDepth, (pLevelProfile->m_LocationProfileList[0]->m_Depth-.5)/2,
				pLevelProfile->m_LocationProfileList[0]->m_Height-.5);
		else
			m_Dimensions.Format("%.0f x %.0f x %.0f", 
				pLevelProfile->m_LocationProfileList[0]->m_Width-.5,
				pLevelProfile->m_LocationProfileList[0]->m_Depth-.5,
				pLevelProfile->m_LocationProfileList[0]->m_Height-.5);	// truncate instead of round
	}
	else {
		m_LocationSpace = "0";
		m_LocationUsage = "Reserve";
		if (pLevelProfile->m_Baytype == BAYTYPE_BIN || pLevelProfile->m_Baytype == BAYTYPE_CASEFLOW)
			m_HandlingMethod = "Case";
		else
			m_HandlingMethod = "Pallet";
		m_Dimensions.Format("0 x 0 x 0");
	}

	SetControlStates(currentLevel);

	UpdateData(FALSE);

	return 0;
}
/*

  Handle variable width
	- disable edits	- done
  Display variable depth info
	- picture
	- tool tip for location dimensions or warn on leaving screen
  Only allow valid combinations of bay type and level type (checkbox) - done
  Only allow valid combinations of handling methods (warning) - skip for now
  Don't allow select when no locations defined - skip
  Handle hidden crossbar logic (no locations above or below, check to see how we do this)
  Validate min loc width, number of locations - done
  Validate gaps and snaps - not much we can do on this for now

*/

BOOL CBayProfileLocationPage::Validate()
{
	for (int i=0; i < m_pBayProfile->m_LevelProfileList.GetSize(); ++i) {
		CLevelProfile *pLevelProfile = m_pBayProfile->m_LevelProfileList[i];
	}

	return TRUE;
}


void CBayProfileLocationPage::SetControlStates(int currentLevel)
{
	CLevelProfile *pLevelProfile = m_pBayProfile->m_LevelProfileList[currentLevel];

	if (pLevelProfile->m_IsVariableWidthAllowed) {
		// Enable gaps and snaps
		GetDlgItem(IDC_MINIMUM_WIDTH_STATIC)->ShowWindow(SW_SHOW);
		GetDlgItem(IDC_MINIMUM_WIDTH)->ShowWindow(SW_SHOW);

		GetDlgItem(IDC_PRODUCT_GAP)->ShowWindow(SW_SHOW);
		GetDlgItem(IDC_PRODUCT_SNAP)->ShowWindow(SW_SHOW);
		GetDlgItem(IDC_FACING_GAP)->ShowWindow(SW_SHOW);
		GetDlgItem(IDC_FACING_SNAP)->ShowWindow(SW_SHOW);
		
		GetDlgItem(IDC_PRODUCT_GAP_STATIC)->ShowWindow(SW_SHOW);
		GetDlgItem(IDC_PRODUCT_SNAP_STATIC)->ShowWindow(SW_SHOW);
		GetDlgItem(IDC_FACING_GAP_STATIC)->ShowWindow(SW_SHOW);
		GetDlgItem(IDC_FACING_SNAP_STATIC)->ShowWindow(SW_SHOW);

		GetDlgItem(IDC_VARIABLE_WIDTH_STATIC)->ShowWindow(SW_SHOW);

	}
	else {
		GetDlgItem(IDC_MINIMUM_WIDTH_STATIC)->ShowWindow(SW_HIDE);
		GetDlgItem(IDC_MINIMUM_WIDTH)->ShowWindow(SW_HIDE);

		GetDlgItem(IDC_PRODUCT_GAP)->ShowWindow(SW_HIDE);
		GetDlgItem(IDC_PRODUCT_SNAP)->ShowWindow(SW_HIDE);
		GetDlgItem(IDC_FACING_GAP)->ShowWindow(SW_HIDE);
		GetDlgItem(IDC_FACING_SNAP)->ShowWindow(SW_HIDE);
		
		GetDlgItem(IDC_PRODUCT_GAP_STATIC)->ShowWindow(SW_HIDE);
		GetDlgItem(IDC_PRODUCT_SNAP_STATIC)->ShowWindow(SW_HIDE);
		GetDlgItem(IDC_FACING_GAP_STATIC)->ShowWindow(SW_HIDE);
		GetDlgItem(IDC_FACING_SNAP_STATIC)->ShowWindow(SW_HIDE);
		GetDlgItem(IDC_VARIABLE_WIDTH_STATIC)->ShowWindow(SW_HIDE);
	}

	if (pLevelProfile->m_Baytype != BAYTYPE_BIN) {
		GetDlgItem(IDC_LOCATIONS_DEEP)->ShowWindow(SW_HIDE);
		GetDlgItem(IDC_LOCATIONS_DEEP_STATIC)->ShowWindow(SW_HIDE);
		GetDlgItem(IDC_LOCATIONS_ACROSS_STATIC)->ShowWindow(SW_HIDE);
		GetDlgItem(IDC_LOCATIONS_ACROSS)->ShowWindow(SW_HIDE);
	}
	else {
		GetDlgItem(IDC_LOCATIONS_DEEP)->ShowWindow(SW_SHOW);
		GetDlgItem(IDC_LOCATIONS_DEEP_STATIC)->ShowWindow(SW_SHOW);
		GetDlgItem(IDC_LOCATIONS_ACROSS_STATIC)->ShowWindow(SW_SHOW);
		GetDlgItem(IDC_LOCATIONS_ACROSS)->ShowWindow(SW_SHOW);
	}
}

BOOL CBayProfileLocationPage::OnHelpInfo(HELPINFO* pHelpInfo) 
{
	helpService.ShowFieldHelp(IDD, pHelpInfo->iCtrlId);
	return FALSE;

}

BOOL CBayProfileLocationPage::OnNotify(WPARAM wParam, LPARAM lParam, LRESULT* pResult) 
{
	NMHDR* pNMHDR = (NMHDR*)lParam;

	if (pNMHDR->code == PSN_HELP) {
		helpService.ShowScreenHelp(IDD);
		return TRUE;
	}
	
	return CPropertyPage::OnNotify(wParam, lParam, pResult);
}
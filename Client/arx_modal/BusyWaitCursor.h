// BusyWaitCursor.h: interface for the CBusyWaitCursor class.
//
//////////////////////////////////////////////////////////////////////

#if !defined(AFX_BUSYWAITCURSOR_H__1155DEC3_D90E_4D72_B41C_57970BD857F4__INCLUDED_)
#define AFX_BUSYWAITCURSOR_H__1155DEC3_D90E_4D72_B41C_57970BD857F4__INCLUDED_

#if _MSC_VER > 1000
#pragma once
#endif // _MSC_VER > 1000

class CBusyWaitCursor  
{
public:
	void Restore();
	CBusyWaitCursor();
	virtual ~CBusyWaitCursor();

};

#endif // !defined(AFX_BUSYWAITCURSOR_H__1155DEC3_D90E_4D72_B41C_57970BD857F4__INCLUDED_)

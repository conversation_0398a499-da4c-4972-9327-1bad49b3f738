// ManualAssignmentDialog.cpp : implementation file
//

#include "stdafx.h"
#include "modal.h"
#include "ManualAssignmentDialog.h"
#include "Constants.h"
#include "HelpService.h"
#include "ProductDataService.h"
#include "TreeElement.h"
#include "Processing.h"
#include "ProductMaintenance.h"
#include "UDFPage.h"
#include "ResourceHelper.h"
#include "qqhclasses.h"

#include "BayProfile.h"
#include "UtilityHelper.h"
#include "SolutionDataService.h"
#include "FacilityDataService.h"
#include "OptimizationDataService.h"
#include "OptimizationHelper.h"
#include "ControlService.h"
#include "BTreeHelper.h"
#include "ElementMaintenanceHelper.h"
#include "ProductGroupDataService.h"
#include "ProductDataService.h"

#include <dbsymtb.h>


#ifdef _DEBUG
#define new DEBUG_NEW
#undef THIS_FILE
static char THIS_FILE[] = __FILE__;
#endif


/////////////////////////////////////////////////////////////////////////////
// CManualAssignmentDialog dialog
extern CHelpService helpService;
extern CUtilityHelper utilityHelper;
extern CSolutionDataService solutionDataService;
extern CControlService controlService;
extern CFacilityDataService facilityDataService;
extern CProductDataService productDataService;

extern TreeElement changesTree;


CManualAssignmentDialog::CManualAssignmentDialog(CWnd* pParent /*=NULL*/)
	: CDialog(CManualAssignmentDialog::IDD, pParent)
{
	//{{AFX_DATA_INIT(CManualAssignmentDialog)
	m_SkipCostUpdate = FALSE;
	//}}AFX_DATA_INIT
	m_ProductGroupList.SetSize(0);
	m_LocationList = NULL;
	m_LocationCount = 0;
	m_WarningLevel = 0;

}


CManualAssignmentDialog::~CManualAssignmentDialog()
{
	
	for (int i=0; i < m_ProductList.GetSize(); ++i)
		delete m_ProductList[i];

	if (m_LocationCount > 0)
		delete [] m_LocationList;

}

void CManualAssignmentDialog::DoDataExchange(CDataExchange* pDX)
{
	CDialog::DoDataExchange(pDX);
	//{{AFX_DATA_MAP(CManualAssignmentDialog)
	DDX_Control(pDX, IDC_LOCATION_LIST, m_LocationListBox);
	DDX_Control(pDX, IDC_PRODUCT_LIST, m_ProductListBox);
	DDX_Check(pDX, IDC_SKIP_COST_UPDATE, m_SkipCostUpdate);
	//}}AFX_DATA_MAP
}


BEGIN_MESSAGE_MAP(CManualAssignmentDialog, CDialog)
	//{{AFX_MSG_MAP(CManualAssignmentDialog)
	ON_BN_CLICKED(IDQUERYPRODUCTS, OnQueryProducts)
	ON_LBN_SELCHANGE(IDC_PRODUCT_LIST, OnSelchangeProductList)
	ON_LBN_SELCHANGE(IDC_LOCATION_LIST, OnSelchangeLocationList)
	ON_BN_CLICKED(IDQUERYLOCATIONS, OnQueryLocations)
	ON_BN_CLICKED(IDASSIGN, OnAssign)
	ON_BN_CLICKED(IDUNASSIGNPRODUCT, OnUnassignProduct)
	ON_BN_CLICKED(IDUNASSIGNLOCATION, OnUnassignLocation)
	ON_WM_HELPINFO()
	ON_BN_CLICKED(IDHELP, OnHelp)
	ON_WM_RBUTTONDBLCLK()
	ON_BN_CLICKED(IDC_SKIP_COST_UPDATE, OnSkipCostUpdate)
	ON_LBN_DBLCLK(IDC_PRODUCT_LIST, OnDblclkProductList)
	ON_LBN_DBLCLK(IDC_LOCATION_LIST, OnDblclkLocationList)
	//}}AFX_MSG_MAP
END_MESSAGE_MAP()

/////////////////////////////////////////////////////////////////////////////
// CManualAssignmentDialog message handlers
/////////////////////////////////////////////////////////////////////////////

BOOL CManualAssignmentDialog::OnInitDialog() 
{
	CDialog::OnInitDialog();
	
	// TODO: Add extra initialization here
	CRect r;

//	CComboBox *pLayoutTypeCombo = (CComboBox *)GetDlgItem(IDC_LAYOUT_TYPE);
//	pLayoutTypeCombo->SetItemHeight(0,2000);
//	pLayoutTypeCombo->SetCurSel(0);
//	pLayoutTypeCombo->GetWindowRect(&r);
//	pLayoutTypeCombo->SetWindowPos(NULL, 0, 0, r.Width(), r.Height()*5, SWP_NOMOVE|SWP_NOZORDER);

	if (controlService.GetApplicationData("SkipCostUpdate", "Dialogs\\ManualAssignment") == "1")
		m_SkipCostUpdate = TRUE;
	else
		m_SkipCostUpdate = FALSE;

	UpdateData(FALSE);

	return TRUE;  // return TRUE unless you set the focus to a control
	              // EXCEPTION: OCX Property Pages should return FALSE
}

void CManualAssignmentDialog::OnQueryProducts() 
{

	CTemporaryResourceOverride tro;
	CProductSheet sheet("Product Query", NULL, 0);
	sheet.m_QueryOnly = TRUE;
	CProductPage productPage;
	CProductContainerPage containerPage;
	CProductOptimizePage optimizePage;
	CUDFPage udfPage;

	sheet.m_psh.dwFlags |= PSH_NOAPPLYNOW;
	sheet.AddPage(&productPage);
	sheet.AddPage(&containerPage);
	sheet.AddPage(&udfPage);
	sheet.AddPage(&optimizePage);

	int rc;
	try {
		rc = sheet.DoModal();
	}
	catch (...) {
		AfxMessageBox("Error running ProductQuery");
	}

	if (rc == IDOK) {

		m_ProductListBox.ResetContent();

		// free the previous list
		for (int i=0; i < m_ProductList.GetSize(); ++i)
			delete m_ProductList[i];

		m_ProductList.RemoveAll();

		CStatic *pProductInfo = (CStatic *)GetDlgItem(IDC_PRODUCT_INFO);
		pProductInfo->SetWindowText("");

		for (i=0; i < sheet.m_Products.GetSize(); ++i) {
			CProductPack *pProduct = new CProductPack(*sheet.m_Products[i]);
			m_ProductList.Add(pProduct);

			CString s;
			s.Format("%s-%s - %s", pProduct->m_WMSProductID, pProduct->m_WMSProductDetailID, pProduct->m_Description);

			int nItem = m_ProductListBox.AddString(s);
			m_ProductListBox.SetItemData(nItem, (unsigned long)pProduct);
		}
	}

	UpdateData(TRUE);

}

void CManualAssignmentDialog::OnSelchangeProductList() 
{
	// Display product information in the box
	CStatic *pProductInfo = (CStatic *)GetDlgItem(IDC_PRODUCT_INFO);
	CString info, levelType;
	int idx;
	CProductPack *p;

	// show the product's prod group, current location, current location's prod group,
	// bay profile, and bay type
	idx = m_ProductListBox.GetCurSel();
	p = (CProductPack *)m_ProductListBox.GetItemData(idx);

	
	CStringArray prodList;

	try {
		productDataService.GetProductByID(p->m_ProductPackDBID, prodList);
	}
	catch (...) {
		controlService.Log("Error getting product information.", "Generic exception in GetProductById.\n");
		return;
	}

	if (prodList.GetSize() > 0)
		p->ParseAll(prodList[0]);

	if (p->m_OptimizedLocationDBID > 0)
		levelType = p->m_OptimizedLevelType;
	else
		levelType = "";

	if (p->m_OptimizedLocation != "") {
		info.Format("Case Pack:  %d\nProduct Group:  %s\nCurrent Loc:  %s (%d)\nLoc Group:  %s\nProfile:\n %-35.35s\nLevel Type:  %s",
			p->m_CasePack, p->m_ProductGroup, p->m_OptimizedLocation, p->m_OptimizedCaseQuantity,
			p->m_OptimizedLocProductGroup, p->m_OptimizedBayProfile, levelType);
	}
	else {
		info.Format("Case Pack:  %d\nProduct Group:  %s\nCurrent Loc:  %s\nLoc Group:  %s\nProfile:\n %-35.35s\nLevel Type:  %s",
			p->m_CasePack, p->m_ProductGroup, p->m_OptimizedLocation,
			p->m_OptimizedLocProductGroup, p->m_OptimizedBayProfile, levelType);
	}

	pProductInfo->SetWindowText(info);
	UpdateData(FALSE);

}

/*
dbproductpackid|prod description|casepack|wmsproductid|wmsproductdetailid|prod dbslottinggroupid|...
prod pg description|prod dblocationid|loc description|bay profile description|loc baytype|...
loc dbslottinggroupid|loc pg description');
*/




void CManualAssignmentDialog::OnSelchangeLocationList() 
{
	// Display location information in the box
	CStatic *pLocationInfo = (CStatic *)GetDlgItem(IDC_LOCATION_INFO);
	CString info, levelType;
	int idx;
	CLocationQueryInfo *l;

	// show the product's prod group, current location, current location's prod group,
	// bay profile, and bay type
	idx = m_LocationListBox.GetCurSel();
	l = &m_LocationList[idx];
	CBayProfile::ConvertBayType(l->m_LevelType, levelType);

//	info.Format("Case Pack:  %d\nProduct Group:  %s\nCurrent Loc:  %s\nLoc Group:  %s\nProfile:\n %-35.35s\nLevel Type:  %s",
//		p->m_CasePack, p->m_ProdPGDescription, p->m_Description,
//		p->m_OptimizedLocationPGDescription, p->m_BayProfileDescription, bayType);
	
	if (l->m_WMSProductID != "") {
		info.Format("Loc Group:  %s\nProfile:\n %-35.35s\nLevelType: %s\nCurrent Prod ID: %s (%d)\n%s\nProd Group: %s",
			l->m_LocPGDescription, l->m_BayProfileDescription, levelType, l->m_WMSProductID, l->m_CaseQuantity,
			l->m_LocationDescription, l->m_ProdPGDescription);
	}
	else {
		info.Format("Loc Group:  %s\nProfile:\n %-35.35s\nLevelType: %s\nCurrent Prod ID: %s\n%s\nProd Group: %s",
			l->m_LocPGDescription, l->m_BayProfileDescription, levelType, l->m_WMSProductID,
			l->m_LocationDescription, l->m_ProdPGDescription);
	}

	pLocationInfo->SetWindowText(info);

	UpdateData(FALSE);
}

void CManualAssignmentDialog::OnQueryLocations() 
{
	CStringArray sgList;
	int i;
	CString s, wmsID, desc;
	CProductGroupDataService productGroupDataService;

	if (m_ProductGroupList.GetSize() == 0) {
		try {
			CWaitCursor cwc;
			productGroupDataService.GetProductGroups(controlService.GetCurrentFacilityDBId(), sgList);
		}
		catch(Ssa_Exception e) {
			utilityHelper.ProcessError("Error getting product group information.", &e);
			return;
		}
		catch(...) {
			utilityHelper.ProcessError("Error getting product group information.");
			return;
		}
		for (i=0; i < sgList.GetSize(); ++i) {
			CStringArray strings;
			utilityHelper.ParseString(sgList[i], "|", strings);
			s.Format("%d|%s", atoi(strings[0]), strings[1]);
			m_QueryLocationsDlg.m_ProductGroupList.Add(s);
			m_ProductGroupList.Add(s);
		}
	}
	else if (m_QueryLocationsDlg.m_ProductGroupList.GetSize() == 0) {
		for (i=0; i < m_ProductGroupList.GetSize(); ++i) {
			m_QueryLocationsDlg.m_ProductGroupList.Add(m_ProductGroupList[i]);
		}
	}
	
	
	int rc = m_QueryLocationsDlg.DoModal();
	if (rc == IDOK) {

		m_LocationListBox.ResetContent();

		// free the previous list
		if (m_LocationCount > 0)
			delete [] m_LocationList;

		m_LocationCount = m_QueryLocationsDlg.m_Results.GetSize();
		m_LocationList = new CLocationQueryInfo[m_LocationCount];
	
		CStatic *pLocationInfo = (CStatic *)GetDlgItem(IDC_LOCATION_INFO);
		pLocationInfo->SetWindowText("");

		for (i=0; i < m_LocationCount; ++i) {
			s = m_QueryLocationsDlg.m_Results[i];
			m_LocationList[i].Parse(s);

			s.Format("%s", m_LocationList[i].m_LocationDescription);

			m_LocationListBox.AddString(s);
		}
	}


	UpdateData(TRUE);

	
}


void CManualAssignmentDialog::OnAssign() 
{
	int prodIdx, locIdx, rc, caseCount, isPrimary;
	CProductPack *pProduct;
	CLocationQueryInfo loc;
	CString temp, prodCostList;

	UpdateData(TRUE);

	CWaitCursor cwc;

	prodIdx = m_ProductListBox.GetCurSel();
	locIdx = m_LocationListBox.GetCurSel();

	// Check that a product was selected
	if (prodIdx == LB_ERR) {
		AfxMessageBox("Please select a product from the list.");
		return;
	}

	// Check that a location was selected
	if (locIdx == LB_ERR) {
		AfxMessageBox("Please select a location from the list.");
		return;
	}

	pProduct = (CProductPack *)m_ProductListBox.GetItemData(prodIdx);
	
	loc = m_LocationList[locIdx];
	
	// See if product already assigned to location
	if (pProduct->m_ProductPackDBID == loc.m_CurrentProductDBID) {
		temp.Format("Product %s is already assigned to location %s",
			pProduct->m_WMSProductID, loc.m_LocationDescription);
		AfxMessageBox(temp);
		return;
	}
	
	// Check the fit - display a message inside the function so we can show more info
	// < 0 means abort; 0 means override; > 0 means ok
	caseCount = GetCaseCount(*pProduct, loc);
	if (caseCount < 0)
		return;
	
	// Check the weight - display a message inside the function so we can show more information
	// < 0 means abort; 0 means override; > 0 means ok
	if (CheckWeight(*pProduct, loc, caseCount) < 0)
		return;
	
	// Check for location profile not in rankings
	rc = CheckRankings(*pProduct, loc);
	if (rc < 0)
		return;

	// Check for product not in product group
	if (pProduct->m_ProductGroupDBID == 0 && m_WarningLevel == 0) {
		temp.Format("The product does not belong to a product group.\nDo you wish to continue?");
		rc = AfxMessageBox(temp, MB_YESNO);
		if (rc == IDNO)
			return;
	}
	
	// Check for location not in product group
	if (loc.m_LocProductGroupDBID == 0 && m_WarningLevel == 0) {
		temp.Format("The location does not belong to a product group.\nDo you wish to continue?");
		rc = AfxMessageBox(temp, MB_YESNO);
		if (rc == IDNO)
			return;
	}
	
	// Check for different product groups
	if (pProduct->m_ProductGroupDBID > 0 && loc.m_LocProductGroupDBID > 0 && pProduct->m_ProductGroupDBID != loc.m_LocProductGroupDBID && m_WarningLevel == 0) {
		temp.Format("The product groups of the location and product are different.\nDo you wish to continue?");
		rc = AfxMessageBox(temp, MB_YESNO);
		if (rc == IDNO)
			return;
	}

	// Check for a previous assignment and whether to add more facings or replace
	// result will be 0 for add facing or 1 for replace primary
	isPrimary = CheckFacings(*pProduct, loc);
	if (isPrimary < 0)
		return;


	// Check for a previous product
	if (loc.m_CurrentProductDBID > 0) {
		temp.Format("Location %s is currently assigned to product %s.\n"
			"Do you wish to replace the current assignment?",
			loc.m_LocationDescription, loc.m_WMSProductID);
		rc = AfxMessageBox(temp, MB_YESNO);
		if (rc == IDNO)
			return;
	}

	// Wait until here to actually do unassignments in case they change their mind
	// after hitting one of the other restrictions

	// Unassign the current location if they chose to replace instead of adding a facing
	if (pProduct->m_OptimizedLocationDBID > 0 && isPrimary) {
		rc = UnassignProduct(*pProduct, 1);	// the 1 means only unassign the primary facing
		if (rc < 0)
			return;

		// Update the location and product information to reflect the unassignment
		for (int i=0; i < m_LocationCount; ++i) {
			if (m_LocationList[i].m_LocationDBID == pProduct->m_OptimizedLocationDBID) {
				UpdateForLocationUnassignment(i);
				OnSelchangeLocationList();
			}
		}
		UpdateForProductUnassignment(prodIdx);
		OnSelchangeProductList();
	}

	// Unassign the current product
	if (loc.m_CurrentProductDBID > 0) {
		temp.Format("%d,", loc.m_CurrentProductDBID);
		prodCostList += temp;

		rc = UnassignLocation(loc);
		if (rc < 0)
			return;

		// Update the product and location information to reflect the unassignment
		for (int i=0; i < m_ProductList.GetSize(); ++i) {
			if (m_ProductList[i]->m_OptimizedLocationDBID == loc.m_LocationDBID) {
				UpdateForProductUnassignment(i);
				OnSelchangeProductList();
			}
		}
		UpdateForLocationUnassignment(locIdx);
		OnSelchangeLocationList();
	}

	rc = CreateAssignment(*pProduct, loc, caseCount, isPrimary);
	if (rc == 0) {

		temp.Format("%d,", pProduct->m_ProductPackDBID);
		prodCostList += temp;
		prodCostList.TrimRight(",");

		temp.Format("Product: %-s successfully assigned to location: %s",
			pProduct->m_WMSProductID, loc.m_LocationDescription);
		AfxMessageBox(temp);

		// Update the list box data to reflect the assignment
		UpdateForAssignment(prodIdx, locIdx, isPrimary, caseCount);

		// Make sure the new information is displayed
		OnSelchangeLocationList();
		OnSelchangeProductList();

		UpdateCost(prodCostList);
	}

}


void CManualAssignmentDialog::OnUnassignProduct() 
{
	// verify that they want to unassign the product
	// if the product is a primary facing ask
	// if they want to unassign all facings; else abort
	CProductPack *pProduct;
	int prodIdx;
	CString temp;

	UpdateData(TRUE);
	CWaitCursor cwc;

	prodIdx = m_ProductListBox.GetCurSel();
	
	// Check that a product was selected
	if (prodIdx == LB_ERR) {
		AfxMessageBox("Please select a product from the list.");
		return;
	}

	pProduct = (CProductPack *)m_ProductListBox.GetItemData(prodIdx);

	if (pProduct->m_OptimizedLocationDBID <= 0) {
		temp.Format("Product %s is not currently assigned to a location.",
			pProduct->m_Description);
		AfxMessageBox(temp);
		return;
	}

	temp.Format("Are you sure you want to unassign product %s from location %s?",
		pProduct->m_WMSProductID, pProduct->m_OptimizedLocation);
	if (AfxMessageBox(temp, MB_YESNO) == IDNO)
		return;

	if (UnassignProduct(*pProduct, 0) == 0) {	// the 0 means unassign all facings
		temp.Format("%d", pProduct->m_ProductPackDBID);


		// First update the location info if it is in the list
		for (int i=0; i < m_LocationCount; ++i) {
			if (m_LocationList[i].m_CurrentProductDBID == pProduct->m_ProductPackDBID) {
				UpdateForLocationUnassignment(i);
				OnSelchangeLocationList();
			}
		}
		UpdateForProductUnassignment(prodIdx);
		OnSelchangeProductList();

		UpdateCost(temp);
	}
	
}

void CManualAssignmentDialog::OnUnassignLocation() 
{
	// verify that they want to unassign the location
	// if the product is a primary facing ask
	// if they want to unassign all facings; else abort
	//temp.Format("Are you sure you want to unassign product %d from location %s?",
	CLocationQueryInfo loc;
	CProductPack product;
	int locIdx, prodIdx;
	CString temp;

	UpdateData(TRUE);
	CWaitCursor cwc;

	locIdx = m_LocationListBox.GetCurSel();

	// Check that a location was selected
	if (locIdx == LB_ERR) {
		AfxMessageBox("Please select a location from the list.");
		return;
	}
	
	loc = m_LocationList[locIdx];
	if (loc.m_CurrentProductDBID <= 0) {
		temp.Format("Location %s does not currently have an assigned product.",
			loc.m_LocationDescription);
		AfxMessageBox(temp);
		return;
	}

	temp.Format("Are you sure you want to unassign product %s from location %s?",
		loc.m_WMSProductID, loc.m_LocationDescription);
	if (AfxMessageBox(temp, MB_YESNO) == IDNO)
		return;
	
	// Find the product in the list
	prodIdx = -1;
	for (int i=0; i < m_ProductList.GetSize(); ++i) {
		// Only update the product if this is the primary location for the product
		if (m_ProductList[i]->m_ProductPackDBID == loc.m_CurrentProductDBID) {
			product = *m_ProductList[i];
			prodIdx = i;
			break;
		}
	}
	if (prodIdx < 0) {
		// need to get the product from the database so we can get the case count
		// and subtract it
		CProductDataService ds;
		CStringArray prodList;
		
		try {
			ds.GetProductByID(loc.m_CurrentProductDBID, prodList);
		}
		catch (...) {
			controlService.Log("Error updating solution case quantity.",
				"Generic exception in GetProductByID.\n");
			return;
		}
		
		if (prodList.GetSize() != 0)
			product.Parse(prodList[0]);
		
	}
	
	if (UnassignLocation(loc) == 0) {
		
		if (product.m_ProductPackDBID > 0) {
			// If the product is in the list and this is the primary location,
			// update the product display information
			if (product.m_OptimizedLocationDBID == loc.m_LocationDBID) {
				if (prodIdx >= 0) {
					UpdateForProductUnassignment(prodIdx);
					OnSelchangeProductList();
				}

				// If this is primary, we must have deleted all the extra facings so
				// we need to update their display too
				for (int j=0; j < m_LocationCount; ++j) {
					if (m_LocationList[j].m_CurrentProductDBID == loc.m_CurrentProductDBID)
						UpdateForLocationUnassignment(j);
				}

			}
			else {		// this is not the primary location
				try {
					// subtract the case count from the total case count for the primary
					// set the warning level so we don't get any error messages
					int saveWarningLevel = m_WarningLevel;
					m_WarningLevel = 0;
					int caseCount = GetCaseCount(product, loc);
					m_WarningLevel = saveWarningLevel;
					if (caseCount > 0)
						solutionDataService.AddToPrimary(product.m_ProductPackDBID, 0 - GetCaseCount(product, loc));
				}
				catch (...) {
					utilityHelper.ProcessError("Error updating primary facing case capacity.");
				}
			}
		}
		temp.Format("%d", product.m_ProductPackDBID);

		// Now update the location info
		UpdateForLocationUnassignment(locIdx);
		OnSelchangeLocationList();
		
		UpdateCost(temp);

	}

	return;
}

int CManualAssignmentDialog::GetCaseCount(CProductPack &product, CLocationQueryInfo &location)
{

	CString temp;
	CStringArray msgList;
	int cases, origCases, maxCases, rc;
	double prodWidth, prodLength, prodHeight;
	BOOL bGetMaxCases = TRUE;

	// determine the number of cases that will fit
	// if none fit, display the dimensions to the user and ask if they
	// want to override

	if (product.m_CaseWidth <= 0 || product.m_CaseLength <= 0 || product.m_CaseHeight <= 0) {
		temp.Format("Invalid product dimensions. (%-4.1f x %-4.1f x %-4.1f).\n",
			product.m_CaseWidth, product.m_CaseLength, product.m_CaseHeight);
		msgList.Add(temp);
	}

	if (location.m_Width <= 0 || location.m_Depth <= 0 || location.m_Height <= 0) {
		temp.Format("Invalid location dimensions. (%-5.1f x %-5.1f x %-5.1f).\n",
			location.m_Width, location.m_Depth, location.m_Height);
		msgList.Add(temp);
	}

	if (location.m_HandlingMethod == PALLET_HANDLING) {

		prodWidth = product.m_Container.m_Width;
		prodLength = product.m_Container.m_Length;
		prodHeight = product.m_Container.m_Hi * product.m_CaseHeight + product.m_Container.m_Height;
		if (product.m_Container.m_IsHeightOverride != 0)
			prodHeight = product.m_Container.m_Height;

		cases = GetPalletHandlingCount(location.m_Width, location.m_Depth, location.m_Height,
			prodWidth, prodLength, prodHeight);
		
		origCases = cases;
		maxCases = cases;

		// only rotate on the z-axis for pallet handling (switch width and length)
		if ( (cases == 0 || bGetMaxCases) && product.m_RotateZAxis && location.m_RotateAllowed) {
			cases = GetPalletHandlingCount(location.m_Width, location.m_Depth, location.m_Height,
				prodLength, prodWidth, prodHeight);
			if (cases > 0 && origCases == 0) {
				temp.Format("The product will not fit unless the width and length are switched.\n");
				msgList.Add(temp);
				
				product.m_RotatedWidth = prodLength;
				product.m_RotatedLength = prodWidth;
				product.m_RotatedHeight = -1.0;
			}

			if (cases > maxCases)
				maxCases = cases;
		}

		cases = maxCases;

		if (product.m_NumberInPallet > 0)
			cases = cases * product.m_NumberInPallet;
		else
			cases = cases * (product.m_Container.m_Ti * product.m_Container.m_Hi);
		
		if (cases <= 0) {
			temp.Format("The product will not fit in the location.\nProduct: %-4.1f x %-4.1f x %-4.1f\n"
				"Location: %-4.1f x %-4.1f x %-4.1f.\nProduct rotation is %s.\nLocation rotation is %s.",
				prodWidth, prodLength, prodHeight, location.m_Width, location.m_Depth, location.m_Height,
				product.m_RotateZAxis ? "on" : "off", 
				location.m_RotateAllowed ? "on" : "off");
			msgList.Add(temp);
		}
		
	}
	else {		// case handling
		
		// Always fit flow type racks using case dimensions
		if (location.m_LevelType == BAYTYPE_CASEFLOW || product.m_UnitOfIssue == UOI_PALLET) {
			prodWidth = product.m_CaseWidth;
			prodLength = product.m_CaseLength;
			prodHeight = product.m_CaseHeight;
		}
		else {
			prodWidth = product.m_CaseWidth;
			prodLength = product.m_CaseLength;
			prodHeight = product.m_CaseHeight;
		}
		
		cases = GetCaseHandlingCount(location.m_Width, location.m_Depth, location.m_Height,
			prodWidth, prodLength, prodHeight, location.m_LevelType);
		
		origCases = cases;
		maxCases = cases;
		
		if ((cases == 0 || bGetMaxCases) && product.m_RotateZAxis && location.m_RotateAllowed) {
			cases = GetCaseHandlingCount(location.m_Width, location.m_Depth, location.m_Height,
				prodLength, prodWidth, prodHeight, location.m_LevelType);

			if (cases > 0 && origCases == 0) {
				temp.Format("The product will not fit unless the width and length are switched.\n");
				msgList.Add(temp);
			}

			if (cases > maxCases)
				maxCases = cases;
		}
		
		if ((cases == 0 || bGetMaxCases) && product.m_RotateXAxis && location.m_RotateAllowed) {
			cases = GetCaseHandlingCount(location.m_Width, location.m_Depth, location.m_Height,
				prodWidth, prodHeight, prodLength, location.m_LevelType);
			if (cases > 0 && origCases == 0) {
				temp.Format("The product will not fit unless the length and height are switched.\n");
				msgList.Add(temp);
			}

			if (cases > maxCases)
				maxCases = cases;
		}
		
		
		if ((cases == 0 || bGetMaxCases) && product.m_RotateYAxis && location.m_RotateAllowed) {
			cases = GetCaseHandlingCount(location.m_Width, location.m_Depth, location.m_Height,
				prodHeight, prodLength, prodWidth, location.m_LevelType);
			if (cases > 0 && origCases == 0) {
				temp.Format("The product will not fit unless the width and height are switched.\n");
				msgList.Add(temp);
			}

			if (cases > maxCases)
				maxCases = cases;
		}
		
		if ((cases == 0 || bGetMaxCases) && product.m_RotateYAxis && product.m_RotateXAxis && product.m_RotateZAxis && location.m_RotateAllowed) {
			cases = GetCaseHandlingCount(location.m_Width, location.m_Depth, location.m_Height,
				prodLength, prodHeight, prodWidth, location.m_LevelType);
			if (cases > 0 && origCases == 0) {
				temp.Format("The product will not fit unless all the dimensions are switched.\n");
				msgList.Add(temp);
			}

			if (cases > maxCases)
				maxCases = cases;
		}
		
		if ((cases == 0 || bGetMaxCases) && product.m_RotateYAxis && product.m_RotateXAxis && product.m_RotateZAxis && location.m_RotateAllowed) {
			cases = GetCaseHandlingCount(location.m_Width, location.m_Depth, location.m_Height,
				prodHeight, prodWidth, prodLength, location.m_LevelType);
			if (cases > 0 && origCases == 0) {
				temp.Format("The product will not fit unless all the dimensions are switched.\n");
				msgList.Add(temp);
			}

			if (cases > maxCases)
				maxCases = cases;
		}

		cases = maxCases;

		// Since flow racks are fitted using case dimensions, we don't have to convert them back to cases
		if ((cases == 0 || bGetMaxCases) && ! (location.m_LevelType == BAYTYPE_CASEFLOW || location.m_LevelType == BAYTYPE_PALLETFLOW) ) {
			switch (product.m_UnitOfIssue) {
			case 0:	// eaches
				cases = cases / product.m_CasePack;
				break;
			case 1:	// inners
				cases = cases / (product.m_CasePack / product.m_InnerPack);
				break;
			case 2:	// cases
			case 3:
				cases = cases;
				break;
			}
			if (cases == 0)
				cases = 1;
		}
		
		if (cases <= 0) {
			temp.Format("The product will not fit in the location.\nProduct: %-4.1f x %-4.1f x %-4.1f\n"
				"Location: %-4.1f x %-4.1f x %-4.1f.\n",
				prodWidth, prodLength, prodHeight, location.m_Width, location.m_Depth, location.m_Height);
			msgList.Add(temp);
		}
	}

	if (cases <= 0 && m_WarningLevel < 2) {
		temp.Format("The following errors have occurred.\n");
		msgList.InsertAt(0, temp);
		temp.Format("\nDo you wish to continue creating the assignment?");
		msgList.Add(temp);
		temp = "";
		for (int i=0; i < msgList.GetSize(); ++i) {
			temp += msgList[i];
			temp += "\n";
		}
		rc = AfxMessageBox(temp, MB_YESNO);
		if (rc == IDYES)
			cases = 1;
		else
			cases = -1;
	}

	return cases;
}

int CManualAssignmentDialog::GetCaseHandlingCount(double lw, double ld, double lh,
												  double pw, double pl, double ph,
												  int levelType)
{

	int wCount, lCount, hCount, cases;
	
	wCount = (int)(lw / pw);
	lCount = (int)(ld / pl);
	hCount = (int)(lh / ph);
	
	if (levelType == BAYTYPE_CASEFLOW || levelType == BAYTYPE_PALLETFLOW) {
		if (hCount > 0)
			hCount = 1;
	}
	
	cases = wCount * lCount * hCount;

	return cases;
}

int CManualAssignmentDialog::GetPalletHandlingCount(double lw, double ld, double lh,
													double pw, double pl, double ph)
{
	int wCount, lCount, hCount, cases;
	
	wCount = (int)(lw / pw);
	lCount = (int)(ld / pl);
	hCount = (int)(lh / ph);
	
	cases = wCount * lCount * hCount;
	
	return cases;
}

int CManualAssignmentDialog::CheckWeight(CProductPack &product, CLocationQueryInfo &location, int cases)
{

	// get all products assigned to this bay (and thus the level)
	// add up the weights; subtract from the bay maximum and the level maximum
	// if this is relative level 1, also need to get the maximum weight
	// by subtracting all the other level weights from the bay maximum weight
	// if the weight is too much, ask the user if they want to override

	CStringArray baySolutions;
	CSolution *solutions;
	CSolution *ss;
	double bayMaxWeight, levelMaxWeight;
	double currentBayWeight, currentLevelWeight;
	double weight, caseWeight;
	int i;
	CString temp;

	currentBayWeight = currentLevelWeight = 0.0f;

	try {
		CWaitCursor cwc;
		bayMaxWeight = facilityDataService.GetBayMaxWeight(location.m_BayDBID);
		levelMaxWeight = facilityDataService.GetLevelMaxWeight(location.m_LevelDBID);
	}
	catch(Ssa_Exception e) {
		utilityHelper.ProcessError("Error getting maximum weight for bay.", &e);
		return -1;
	}
	catch(...) {
		utilityHelper.ProcessError("Error getting maximum weight for bay.");
		return -1;
	}	

	try {
		CWaitCursor cwc;
		solutionDataService.GetSolutionsByBay(location.m_BayDBID, baySolutions);
	}
	catch(Ssa_Exception e) {
		utilityHelper.ProcessError("Error getting current weight information for bay.", &e);
		return -1;
	}
	catch(...) {
		utilityHelper.ProcessError("Error getting current weight information for bay.");
		return -1;
	}

	if (baySolutions.GetSize() > 0) {
		 solutions = new CSolution[baySolutions.GetSize()];
		
		for (i=0; i < baySolutions.GetSize(); ++i) {
			solutions[i].Parse(baySolutions[i]);
			ss = &solutions[i];
			
			switch (ss->m_UnitOfIssue) {
			case 0:		// each
				caseWeight = ss->m_ProductWeight * ss->m_CasePack;
				break;
			case 1:		// inner
				caseWeight = ss->m_ProductWeight * (ss->m_CasePack/ss->m_InnerPack);
				break;
			case 2:
				caseWeight = ss->m_ProductWeight;
				break;
			case 3:
				caseWeight = ss->m_ProductWeight / ss->m_NumberInPallet;
				break;
			}
			
			weight = ss->m_CaseQuantity * caseWeight;
			

			// Increment the weight of the bay 
			currentBayWeight += weight;

			// if this level is the one we are trying to assign, increment the weight
			// already assigned to the level
			if (ss->m_LevelDBID == location.m_LevelDBID) {
				currentLevelWeight += weight;
			}
		}

	}
	else {		// no products assigned to this bay; just check current product weight
		currentBayWeight = 0;
		currentLevelWeight = 0;
	}
	
	
	// calculate the weight of this product
	switch (product.m_UnitOfIssue) {
	case 0:		// each
		caseWeight = product.m_Weight * product.m_CasePack;
		break;
	case 1:		// inner
		caseWeight = product.m_Weight * (product.m_CasePack/product.m_InnerPack);
		break;
	case 2:
		caseWeight = product.m_Weight;
		break;
	case 3:
		caseWeight = product.m_Weight / product.m_NumberInPallet;
		break;
	}
	
	weight = caseWeight * cases;
	
	if (weight + currentLevelWeight > levelMaxWeight && m_WarningLevel == 0) {
		temp.Format("The level can not support the product weight.\n"
			"Level Max: %-.2f, Level Current: %-.2f, Product: %-.2f.",
			levelMaxWeight, currentLevelWeight, weight);
		temp += "\nDo you wish to continue?";
		if (AfxMessageBox(temp, MB_YESNO) == IDNO)
			return -1;
	}
	
	if (weight + currentBayWeight > bayMaxWeight && m_WarningLevel == 0) {
		temp.Format("The bay can not support the product weight.\nBay Max: %8.2f, Bay Current: %8.2f, Product: %8.2f.",
			bayMaxWeight, currentBayWeight, weight);
		temp += "\nDo you wish to continue?";
		if (AfxMessageBox(temp, MB_YESNO) == IDNO)
			return -1;
	}
	

	return 0;

}

int CManualAssignmentDialog::CheckFacings(CProductPack &product, CLocationQueryInfo &location)
{

	// if primary facing doesn't exist for this product, default it to primary; return 1
	// if primary facing already exists for product
	// ask the user if they want this to be an extra facing (warn if not adjacent)
	// if so return 0
	// if not return -1
	int rc;
	CString primaryLocString;
	CLocationQueryInfo primaryLoc;
	CString temp;
	char *str, *ptr;

		// Check for a previous location
	if (product.m_OptimizedLocationDBID > 0) {
		temp.Format("Product %s is currently assigned to location %s.\n"
			"Choose Yes to replace the current assignment or\n"
			"No to add an extra facing.",  
			product.m_WMSProductID, product.m_OptimizedLocation);
		rc = AfxMessageBox(temp, MB_YESNOCANCEL);
		if (rc == IDCANCEL)
			return -1;
		if (rc == IDYES)
			return 1;
	}
	else
		return 1;


	// Now we know that we are going to add an extra facing
	// Get the primary facing of the product and verify that the new location
	// is adjacent

	try {
		CWaitCursor cwc;
		rc = solutionDataService.GetPrimaryFacing(product.m_ProductPackDBID, primaryLocString);
	}
	catch(Ssa_Exception e) {
		utilityHelper.ProcessError("Error getting primary facing for product.", &e);
		return -1;
	}
	catch(...) {
		utilityHelper.ProcessError("Error getting primary facing for product.");
		return -1;
	}

	if (primaryLocString.Compare("") != 0) {
		try {
			// there is a primary facing
			str = primaryLocString.GetBuffer(0);
			ptr = strtok(str, "|");
			primaryLoc.m_LocationDBID = atoi(ptr);
			ptr = strtok(NULL, "|");
			primaryLoc.m_LocationDescription = ptr;
			ptr = strtok(NULL, "|");
			primaryLoc.m_LevelDBID = atoi(ptr);
			primaryLocString.ReleaseBuffer();
		}
		catch(...) {
			utilityHelper.ProcessError("Error parsing primary location information.");
			return -1;
		}

		// Check to make sure the locations are adjacent; warn if they are not
		// Need to add code in here to handle bay spanning
		if (! LocationsAreAdjacent(location, primaryLoc)) {
			temp.Format("Warning!  The primary location %s is not adjacent to this location.\n"
				"Do still wish to create an extra facing assignment?", primaryLoc.m_LocationDescription);
			
			
			if (AfxMessageBox(temp, MB_YESNO) == IDNO)
				return -1;		// -1 means don't do the assignment
			else
				return 0;		// 0 means this is an extra facing
		}
	}
	
		
	return 0;		// 1 means this is the primary location

}


int CManualAssignmentDialog::LocationsAreAdjacent(CLocationQueryInfo &loc1, CLocationQueryInfo &loc2)
{

	// need to add logic here to handle bay spanning
	if (loc1.m_LevelDBID == loc2.m_LevelDBID)
		return 1;
	else
		return 0;
}


int CManualAssignmentDialog::CreateAssignment(CProductPack &product, CLocationQueryInfo &location, int caseCount, int isPrimary)
{

	// build solution
	// dont' forget to update the lists and refresh the screen
	// if not primary; set casequantity to 0; add actual casecount to primary
	
	// solution consists of:
	// productdbid|locdbid|casecount|isprimary|
	// need to add cost so they can run a baseline

	CSolution solution;
	CString temp;
	int rc;

	if (! isPrimary) {
		try {
			CWaitCursor cwc;
			rc = solutionDataService.AddToPrimary(product.m_ProductPackDBID, caseCount);
			if (rc != 0) {
				AfxMessageBox("Error updating primary facing.");
				return -1;
			}
			caseCount = 0;
		}
		catch(Ssa_Exception e) {
			utilityHelper.ProcessError("Error updating primary facing.", &e);
			return -1;
		}
		catch(...) {
			utilityHelper.ProcessError("Error updating primary facing.");
			return -1;
		}
	}

	
	solution.m_ProductDBID = product.m_ProductPackDBID;
	solution.m_RotatedWidth = product.m_RotatedWidth;
	solution.m_RotatedLength = product.m_RotatedLength;
	solution.m_RotatedHeight = product.m_RotatedHeight;

	solution.m_LocationDBID = location.m_LocationDBID;
	solution.m_CaseQuantity = caseCount;
	solution.m_Origin = CSolution::Optimize;

	if (caseCount == 0)
		solution.m_IsPrimary = 0;
	else
		solution.m_IsPrimary = 1;

	try {
		CWaitCursor cwc;
		rc = solutionDataService.StoreSolution(solution);
		if (rc != 0) {
			AfxMessageBox("Error creating assignment.");
			return -1;
		}

	}
	catch(Ssa_Exception e) {
		utilityHelper.ProcessError("Error creating assignment.", &e);
		return -1;
	}
	catch(...) {
		utilityHelper.ProcessError("Error creating assignment.");
		return -1;
	}

	return 0;
}

int CManualAssignmentDialog::UnassignProduct(CProductPack &product, int primaryOnly)
{

	// if there are multiple facings for the product
	// ask if they want to delete all of them
	// if not
	// abort
	// otherwise, delete the assignments
	
	int count, rc;
	CString temp;


	if (primaryOnly) {
		try {
			CWaitCursor cwc;
			rc = solutionDataService.DeleteSolutionsByLocation(product.m_OptimizedLocationDBID);
		}
		catch(Ssa_Exception e) {
			utilityHelper.ProcessError("Error deleting solutions for location.", &e);
			return -1;
		}
		catch(...) {
			utilityHelper.ProcessError("Error deleting solutions for location.");
			return -1;
		}

		if (rc == 0) {
			temp.Format("Assignment successfully deleted.");
			AfxMessageBox(temp);
			return 0;
		}
		else {
			temp.Format("Error deleting product assignment.");
			AfxMessageBox(temp);
			return -1;
		}
	}

	try {
		CWaitCursor cwc;
		count = solutionDataService.GetFacingCountByProduct(product.m_ProductPackDBID);
	}
	catch(Ssa_Exception e) {
		utilityHelper.ProcessError("Error getting product facings.", &e);
		return -1;
	}
	catch(...) {
		utilityHelper.ProcessError("Error getting product facings.");
		return -1;
	}

	if (count > 1) {
		temp.Format("There are multiple facings for this product.\nDo you wish to unassign all of the facings?");
		if (AfxMessageBox(temp, MB_YESNO) == IDNO)
			return -1;
	}

	try {
		CWaitCursor cwc;
		rc = solutionDataService.DeleteSolutionsByProduct(product.m_ProductPackDBID);
		if (rc == 0) {
			if (count == 1)
				temp.Format("%d assignment deleted.", count);
			else
				temp.Format("%d assignments deleted.", count);
			AfxMessageBox(temp);
		}
		else {
			temp.Format("Error deleting product assignment.");
			AfxMessageBox(temp);
			return -1;
		}
	}
	catch(Ssa_Exception e) {
		utilityHelper.ProcessError("Error deleting assignment.", &e);
		return -1;
	}
	catch(...) {
		utilityHelper.ProcessError("Error deleting assignment.");
		return -1;
	}
	
	return 0;
}

int CManualAssignmentDialog::UnassignLocation(CLocationQueryInfo &location)
{

	// if the location is a primary facing and extra facings exist,
	// abort
	// otherwise, delete the assignment

	CString temp;
	int count, rc;
	bool bDeleteByProduct = FALSE;

	if (location.m_IsPrimaryFacing) {
		try {
			CWaitCursor cwc;
			count = solutionDataService.GetFacingCountByProduct(location.m_CurrentProductDBID);
		}
		catch(Ssa_Exception e) {
			utilityHelper.ProcessError("Error getting product facings.", &e);
			return -1;
		}
		catch(...) {
			utilityHelper.ProcessError("Error getting product facings.");
			return -1;
		}

		if (count > 1) {
			temp.Format("This location is a primary facing and other facings exist.");
			temp += "\nDo you wish to unassign all of the facings for this product?";
			if (AfxMessageBox(temp, MB_YESNO) == IDNO)
				return -1;
			else
				bDeleteByProduct = TRUE;
		}
	}

				
	try {
		if (bDeleteByProduct) {
			CWaitCursor cwc;
			rc = solutionDataService.DeleteSolutionsByProduct(location.m_CurrentProductDBID);
			if (rc == 0) {
				temp.Format("%d assignments deleted.", count);
				AfxMessageBox(temp);
			}
			else {
				temp.Format("Error deleting location assignments.");
				AfxMessageBox(temp);
				return -1;
			}
		}
		else {
			CWaitCursor cwc;
			rc = solutionDataService.DeleteSolutionsByLocation(location.m_LocationDBID);
			if (rc == 0) {
				temp.Format("Assignment successfully deleted.");
				AfxMessageBox(temp);
			}
			else {
				temp.Format("Error deleting location assignment.");
				AfxMessageBox(temp);
				return -1;
			}
		}
		

	}
	catch(Ssa_Exception e) {
		utilityHelper.ProcessError("Error deleting assignment.", &e);
		return -1;
	}
	catch(...) {
		utilityHelper.ProcessError("Error deleting assignment.");
		 return -1;
	}


	return 0;
}

void CManualAssignmentDialog::UpdateForAssignment(int prodIdx, int locIdx, int isPrimary, int caseCount)
{
	CProductPack *pProduct;
	CLocationQueryInfo *location;

	//pProduct = m_ProductList[prodIdx];
	pProduct = (CProductPack *)m_ProductListBox.GetItemData(prodIdx);
	location = &m_LocationList[locIdx];

	// if the assignment is an extra facing, the product should
	// already have the primary location associated with it
	if (isPrimary) {
		pProduct->m_OptimizedLocationDBID = location->m_LocationDBID;
		pProduct->m_OptimizedLocation = location->m_LocationDescription;
		pProduct->m_OptimizedBayProfile = location->m_BayProfileDescription;
		pProduct->m_OptimizedLocProductGroup = location->m_LocPGDescription;
		pProduct->m_OptimizedLocProductGroupDBID = location->m_LocProductGroupDBID;
		pProduct->m_OptimizedLevelType = CBayProfile::ConvertBayType(location->m_LevelType);
		pProduct->m_OptimizedCaseQuantity = caseCount;
	}

	location->m_CurrentProductDBID = pProduct->m_ProductPackDBID;
	location->m_ProdPGDescription = pProduct->m_ProductGroup;
	location->m_ProdProductGroupDBID = pProduct->m_ProductGroupDBID;
	location->m_LocationDescription = pProduct->m_OptimizedLocation;
	location->m_WMSProductID = pProduct->m_WMSProductID;
	location->m_WMSProductDetailID = pProduct->m_WMSProductDetailID;
	location->m_IsPrimaryFacing = isPrimary;
	location->m_CaseQuantity = caseCount;

	return;

}

void CManualAssignmentDialog::UpdateForLocationUnassignment(int locIdx)
{
	CLocationQueryInfo *location;

	location = &m_LocationList[locIdx];

	// Now update the list to reflect the new information
	location->m_CurrentProductDBID = 0;
	location->m_ProdPGDescription = "";
	location->m_ProdProductGroupDBID = 0;
	location->m_LocationDescription = "";
	location->m_WMSProductID = "";
	location->m_WMSProductDetailID = "";
	location->m_CaseQuantity = 0;

	return;

}

void CManualAssignmentDialog::UpdateForProductUnassignment(int prodIdx)
{
	CProductPack *product;

	product = (CProductPack *)m_ProductListBox.GetItemData(prodIdx);


	// Now update the list to reflect the new information
	product->m_OptimizedLocationDBID = 0;
	product->m_OptimizedLocation = "";
	product->m_OptimizedBayProfile = "";
	product->m_OptimizedLocProductGroup = "";
	product->m_OptimizedLocProductGroupDBID = 0;
	product->m_OptimizedLevelType = "";
	product->m_OptimizedCaseQuantity = 0;

	return;

}

int CManualAssignmentDialog::CheckRankings(CProductPack &product, CLocationQueryInfo &location)
{

	CStringArray rankings;
	CString msg, temp, levelType, profileDesc;
	int profileDBID, levelTypeID, rc;
	BOOL found;
	char *str, *ptr;
	COptimizationDataService optimizationDataService;

	try {
		// this returns an array of the product rankings ("profileID|profiledesc|baytype|")
		rc = optimizationDataService.GetProductRankings(product.m_ProductPackDBID, rankings);
		if (rc < 0) {
			AfxMessageBox("Error getting ranking information for product.\n");
			return -1;
		}
	}
	catch(Ssa_Exception e) {
		utilityHelper.ProcessError("Error deleting assignment.", &e);
		return -1;
	}
	catch(...) {
		utilityHelper.ProcessError("Error deleting assignment.");
		return -1;
	}

	msg = "Warning! Location type does not match product rankings:\n";

	found = false;
	for (int i=0; i < rankings.GetSize(); i++) {
		try {
			str = rankings[i].GetBuffer(0);
			ptr = strtok(str, "|");
			profileDBID = atoi(ptr);
			ptr = strtok(NULL, "|");
			profileDesc = ptr;
			ptr = strtok(NULL, "|");
			levelTypeID = atoi(ptr);
			CBayProfile::ConvertBayType(levelTypeID, levelType);
			rankings[i].ReleaseBuffer();
		}
		catch (Ssa_Exception e) {
			utilityHelper.ProcessError("Error parsing ranking information.", &e);
			return -1;
		}
		catch (...) {
			utilityHelper.ProcessError("Error parsing ranking information.");
		}

		if (location.m_BayProfileDBID == profileDBID &&
			location.m_LevelType == levelTypeID)
			found = true;

		// Build the display message showing all the rankings
		temp.Format("%s - %s\n", profileDesc, levelType);
		msg += temp;
	}


	if (! found && m_WarningLevel == 0) {
		msg += "Do you wish to continue?";
		rc = AfxMessageBox(msg, MB_YESNO);
		if (rc == IDNO)
			return -1;
		else
			return 0;
	}

	return 0;

}


BOOL CManualAssignmentDialog::OnHelpInfo(HELPINFO* pHelpInfo) 
{
	helpService.ShowFieldHelp(IDD, pHelpInfo->iCtrlId);
	return FALSE;
}

void CManualAssignmentDialog::OnHelp() 
{
	helpService.ShowScreenHelp(IDD);

	return;
}


void CManualAssignmentDialog::OnRButtonDblClk(UINT nFlags, CPoint point) 
{
	if (nFlags & MK_CONTROL) {
		m_WarningLevel++;
		if (m_WarningLevel > 2)
			m_WarningLevel = 0;
		CString temp;
		temp.Format("Warning level set to %d", m_WarningLevel);
		AfxMessageBox(temp);
	}

	CDialog::OnRButtonDblClk(nFlags, point);
}

void CManualAssignmentDialog::UpdateCost(CString &productIDList)
{
	CProcessing *dlg;
	CString cost;
	COptimizationHelper optimizationHelper;

	UpdateData(TRUE);
	if (m_SkipCostUpdate)
		return;

	dlg = new CProcessing;
	dlg->Create(IDD_PROCESSING);

	try {
		dlg->m_StatusText = "Updating facility cost...";
		dlg->UpdateData(FALSE);
		dlg->CenterWindow();
		dlg->ShowWindow(SW_SHOW);
		dlg->UpdateWindow();

		optimizationHelper.CalculateOptimizeCost(cost, controlService.GetCurrentFacilityDBId(), productIDList);

	}
	catch (...) {
		utilityHelper.ProcessError("Error updating cost.");
	}

	dlg->DestroyWindow();

}

void CManualAssignmentDialog::OnSkipCostUpdate() 
{
	UpdateData(TRUE);
	CString temp;
	temp.Format("%d", (m_SkipCostUpdate == 1));

	controlService.SetApplicationData("SkipCostUpdate", temp, "Dialogs\\ManualAssignment");
	
}

void CManualAssignmentDialog::OnDblclkProductList() 
{
	CTemporaryResourceOverride tro;
	CProductSheet sheet("Product Maintenance", this, 0);
	CProductPage productPage;
	CProductContainerPage containerPage;
	CProductOptimizePage optimizePage;
	CUDFPage udfPage;
	int idx;
	CProductPack *p;

	CWaitCursor cwc;
	// show the product's prod group, current location, current location's prod group,
	// bay profile, and bay type
	idx = m_ProductListBox.GetCurSel();
	if (idx < 0)
		return;

	p = (CProductPack *)m_ProductListBox.GetItemData(idx);


	sheet.m_DisplayProductID = p->m_ProductPackDBID;
	sheet.m_AllowUpdate = FALSE;

	sheet.m_psh.dwFlags |= PSH_NOAPPLYNOW;
	sheet.AddPage(&productPage);
	sheet.AddPage(&containerPage);
	sheet.AddPage(&udfPage);
	sheet.AddPage(&optimizePage);

	try {
		sheet.DoModal();
	}
	catch (...) {
		AfxMessageBox("Error running ProductMaintenance");
	}


	// Later add ability to update the list when the product changes
	// Have to take into account prod group, size, case quantity, etc
	UpdateData(FALSE);
	
}

void CManualAssignmentDialog::OnDblclkLocationList() 
{
	qqhSLOTLocation location, tempLocation;
	qqhSLOTBay bay;
	qqhSLOTLevel level;
	int idx;
	CLocationQueryInfo *l;
	TreeElement *locPtr, *levelPtr, *bayPtr;
	double totalWidth = 0;
	CWaitCursor cwc;
	CBTreeHelper bTreeHelper;
	CElementMaintenanceHelper elementMaintenanceHelper;

	// show the product's prod group, current location, current location's prod group,
	// bay profile, and bay type
	idx = m_LocationListBox.GetCurSel();
	l = &m_LocationList[idx];


	locPtr = changesTree.getLocationByDBID(l->m_LocationDBID);
	if (locPtr == NULL) {
		AfxMessageBox("Unable to find location.");
		return;
	}
	levelPtr = locPtr->treeParent;
	if (bTreeHelper.GetBtLevel(levelPtr->fileOffset, level) < 0) {
		AfxGetApp()->DoWaitCursor(-1);
		AfxMessageBox("Unable to read level.");
		return;
	}

	bayPtr = levelPtr->treeParent;
	if (bTreeHelper.GetBtBay(bayPtr->fileOffset, bay) < 0) {
		AfxGetApp()->DoWaitCursor(-1);
		AfxMessageBox("Unable to read bay.");
		return;
	}

	bTreeHelper.GetBtLocation(locPtr->fileOffset, location);
	elementMaintenanceHelper.LocationProperties(location, level, bay, totalWidth, FALSE);	
}

#if !defined(AFX_DATAGRID_H__CE256691_CC7C_11D4_9EC1_00C04FAC149C__INCLUDED_)
#define AFX_DATAGRID_H__CE256691_CC7C_11D4_9EC1_00C04FAC149C__INCLUDED_

#if _MSC_VER > 1000
#pragma once
#endif // _MSC_VER > 1000
// DataGrid.h : header file
//
#include "msflexgrid.h"
#include "DataGridEdit.h"
#include "DataGridAttribute.h"
#include "DataGridListBox.h"


/////////////////////////////////////////////////////////////////////////////
// CDataGrid window

class CDataGrid : public CMSFlexGrid
{
// Construction
public:
	CDataGrid();
	CDataGrid(BOOL pAllowInsertDelete);

// Attributes
public:
	CDataGridEdit *m_Edit;
	CDataGridEdit m_LeftEdit;
	CDataGridEdit m_RightEdit;
	CDataGridListBox m_ListBox;
	long m_lBorderWidth;
	long m_lBorderHeight;
	int m_nLogX;
	int m_nLogY;
//	CObArray m_DataGridAttributes;
	CTypedPtrArray<CObArray, CDataGridAttribute*> m_DataGridAttributes;
// Operations
public:

// Overrides
	// ClassWizard generated virtual function overrides
	//{{AFX_VIRTUAL(CDataGrid)
	protected:
	virtual void PreSubclassWindow();
	//}}AFX_VIRTUAL

// Implementation
public:
	void EndEdit();
	BOOL m_ScaleGridToWindow;
	void ProcessArrow(int direction);
	int m_SortColumn;
	void ShowHelp();
	CString m_MainHelpTopic;
	BOOL m_ResetColumnWidths;
	void (*m_LeaveCellFunction)(void *parent);
	void (*m_EnterCellFunction)(void *parent);
	void (*m_DoubleClickCellFunction)(void *parent);
	void ShowCell(long row, long col);
	void HideCell(long row, long col);
	BOOL m_Initializing;
	BOOL m_AllowInsertDelete;
	virtual ~CDataGrid();
	void ProcessInsert(BOOL bAfterRow = FALSE);
	void ProcessDelete();
	void ProcessTab();
	void LoadAttributes(BOOL bUpdating = FALSE);
	afx_msg void OnLeaveCellGrid();
	afx_msg void OnEnterCellGrid();

	// Generated message map functions
protected:
	//{{AFX_MSG(CDataGrid)
	afx_msg UINT OnGetDlgCode();
	afx_msg void OnSetFocus(CWnd* pOldWnd);
	afx_msg void OnKeyDown(UINT nChar, UINT nRepCnt, UINT nFlags);
	afx_msg void OnKillFocus(CWnd* pNewWnd);
	afx_msg void OnSize(UINT nType, int cx, int cy);
	afx_msg void OnHScroll(UINT nSBCode, UINT nPos, CScrollBar* pScrollBar);
	afx_msg BOOL OnHelpInfo(HELPINFO* pHelpInfo);
	afx_msg void OnVScroll(UINT nSBCode, UINT nPos, CScrollBar* pScrollBar);
	//}}AFX_MSG
	afx_msg void OnKeyDownGrid(short *KeyCode, short Shift);
	afx_msg void OnKeyPressGrid(short FAR* KeyAscii);
	void CDataGrid::OnMouseDownGrid(short Button, short shift, OLE_XPOS_PIXELS x, OLE_YPOS_PIXELS y);
	afx_msg void OnDblClickGrid();
	DECLARE_EVENTSINK_MAP()
	DECLARE_MESSAGE_MAP()
private:
	CString FormatCell(CDataGridAttribute *pAttribute, CString &text);
	long m_PreviousColumn;
	long m_PreviousRow;
	BOOL IsCurrentCellChanged();
	long SortAttributes(BOOL bUpdating = TRUE);
	int m_ParentOldHeight;
	int m_ParentOldWidth;
	CFont m_EditFont;
	CFont m_ListFont;
};

/////////////////////////////////////////////////////////////////////////////

//{{AFX_INSERT_LOCATION}}
// Microsoft Visual C++ will insert additional declarations immediately before the previous line.

#endif // !defined(AFX_DATAGRID_H__CE256691_CC7C_11D4_9EC1_00C04FAC149C__INCLUDED_)

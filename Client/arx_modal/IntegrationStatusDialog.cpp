// IntegrationStatusDialog.cpp : implementation file
//

#include "stdafx.h"
#include <wininet.h>

#include "modal.h"
#include "BusyWaitCursor.h"

#include "IntegrationStatusDialog.h"
#include "IntegrationDataService.h"
#include "ControlService.h"
#include "UtilityHelper.h"
#include "InterfaceHelper.h"
#include "DataAccessService.h"
#include "UserQueryDataService.h"
#include "ProcessingMessage.h"
#include "IntegrationLocationOptionsPage.h"
#include "IntegrationProductOptionsPage.h"
#include "IntegrationAssignmentOptionsPage.h"
#include "IntegrationOptionsPage.h"
#include "ProductGroupAssignmentDialog.h"
#include "ProductGroupDataService.h"
#include "GenerateMovesDialog.h"
#include "SolutionDataService.h"
#include "ThreadParameters.h"
#include "ssa_exception.h"
#include "WMSGroupProperties.h"
#include "HelpService.h"
#include "DataAccessService.h"

#ifdef _DEBUG
#define new DEBUG_NEW
#undef THIS_FILE
static char THIS_FILE[] = __FILE__;
#endif


extern CEvent g_ThreadDone;
#define FLUSH_COUNT 500

/////////////////////////////////////////////////////////////////////////////
// CIntegrationStatusDialog dialog

extern CIntegrationDataService integrationDataService;
extern CControlService controlService;
extern CUtilityHelper utilityHelper;
extern CDataAccessService dataAccessService;
extern CHelpService helpService;
extern CDataAccessService dataAccessService;

CIntegrationStatusDialog::CIntegrationStatusDialog(CWnd* pParent /*=NULL*/)
	: CDialog(CIntegrationStatusDialog::IDD, pParent)
{
	//{{AFX_DATA_INIT(CIntegrationStatusDialog)
	m_Message = _T("");
	m_InterfaceMessage = _T("");
	m_Details = _T("");
	//}}AFX_DATA_INIT
	m_pMQHelper = NULL;
}

CIntegrationStatusDialog::~CIntegrationStatusDialog()
{
	for (int i=0; i < m_GroupList.GetSize(); ++i)
		delete m_GroupList[i];

	delete m_pMQHelper;
}

void CIntegrationStatusDialog::DoDataExchange(CDataExchange* pDX)
{
	CDialog::DoDataExchange(pDX);
	//{{AFX_DATA_MAP(CIntegrationStatusDialog)
	DDX_Control(pDX, IDC_ANIMATE, m_AnimateCtrl);
	DDX_Control(pDX, IDC_GROUP_LIST, m_GroupListCtrl);
	DDX_Control(pDX, IDC_OVERALL_PROGRESS, m_OverallProgressCtrl);
	DDX_Control(pDX, IDC_PROGRESS, m_ProgressCtrl);
	DDX_Text(pDX, IDC_MESSAGE, m_Message);
	DDX_Text(pDX, IDC_INTERFACE_MESSAGE, m_InterfaceMessage);
	DDX_Text(pDX, IDC_DETAIL_EDIT, m_Details);
	//}}AFX_DATA_MAP
}


BEGIN_MESSAGE_MAP(CIntegrationStatusDialog, CDialog)
	//{{AFX_MSG_MAP(CIntegrationStatusDialog)
	ON_BN_CLICKED(IDC_START, OnStart)
	ON_BN_CLICKED(IDC_VIEWERRORS, OnViewErrors)
	ON_BN_CLICKED(IDC_OPTIONS, OnOptions)
	ON_BN_CLICKED(IDC_DETAILS, OnDetails)
	ON_CBN_SELCHANGE(IDC_GROUP_LIST, OnSelchangeGroupList)
	ON_BN_CLICKED(IDC_WMS_PROPERTIES, OnWmsProperties)
	ON_WM_HELPINFO()
	ON_BN_CLICKED(IDHELP, OnHelp)
	//}}AFX_MSG_MAP
END_MESSAGE_MAP()

/////////////////////////////////////////////////////////////////////////////
// CIntegrationStatusDialog message handlers

BOOL CIntegrationStatusDialog::OnInitDialog() 
{
	CDialog::OnInitDialog();

	m_InProgress = FALSE;

	
	CRect r, r2;
	GetWindowRect(&r);
	GetDlgItem(IDC_DETAIL_EDIT)->GetWindowRect(&r2);

	m_NoDetailHeight = r.Height() - r2.Height();

	GetDlgItem(IDC_DETAIL_EDIT)->ShowWindow(SW_HIDE);

	CBusyWaitCursor bwc;
	m_InterfaceMessage.Format("Loading WMS information...");
	UpdateData(FALSE);

	LoadWMSGroupList();

	LoadOptions();

	UpdateData(FALSE);

	return TRUE;  // return TRUE unless you set the focus to a control
	              // EXCEPTION: OCX Property Pages should return FALSE
}

void CIntegrationStatusDialog::OnStart() 
{

	if (m_InProgress)
		return;
	
	m_InProgress = TRUE;

	/*
	m_AnimateCtrl.Open(IDR_DOWNLOAD);
	m_AnimateCtrl.ShowWindow(SW_SHOW);
	m_AnimateCtrl.Play(0, -1, -1);
	*/

	try {
		Start();
	}
	catch (...) {
		;
	}


	m_AnimateCtrl.Stop();
	m_AnimateCtrl.Close();
	m_AnimateCtrl.ShowWindow(SW_HIDE);

}

void CIntegrationStatusDialog::Start()
{
	m_InterfaceMessage.Format("Synchronizing %s", m_pGroup->m_Name);
	m_OverallProgressCtrl.ShowWindow(SW_SHOW);
	m_ProgressCtrl.ShowWindow(SW_SHOW);	

	m_Details += "\r\n---------------------------------\r\nStarting Synchronization.\r\n";
	UpdateData(FALSE);

	m_pXMLReader = NULL;

	HRESULT hr = CoCreateInstance(
                        __uuidof(SAXXMLReader), 
                        NULL, 
                        CLSCTX_ALL, 
                        __uuidof(ISAXXMLReader), 
                        (void **)&m_pXMLReader);

	if (FAILED(hr)) {
		CString temp;
		temp.Format("Error creating m_pXMLReader: %08X\n", hr);
		controlService.Log("Unable to create XML Parser.", temp);
		m_InProgress = FALSE;
		return;
	}
	
	m_pXMLHandler = CSaxContentHandler::CreateInstance();
	if(m_pXMLHandler == NULL ){
		CString temp;
		temp.Format("Error creating XML hanlder\n");
		controlService.Log("Unable to create XML Parser.", temp);
		m_pXMLReader->Release();
		m_InProgress = FALSE;
		return;
	}

	hr = m_pXMLReader->putContentHandler(m_pXMLHandler);
	if (FAILED(hr)) {
		CString temp;
		temp.Format("Error associating handler with reader: %08X\n", hr);
		controlService.Log("Unable to create XML Parser.", temp);
		delete m_pXMLHandler;
		m_pXMLReader->Release();
		m_InProgress = FALSE;
		return;
	}

	hr = m_pXMLReader->putErrorHandler(m_pXMLHandler);
	if (FAILED(hr)) {
		CString temp;
		temp.Format("Error associating error handler with reader: %08X\n", hr);
		controlService.Log("Unable to create XML Parser.", temp);
		m_pXMLReader->putContentHandler(NULL);
		m_pXMLReader->Release();
		m_InProgress = FALSE;
		return;
	}

	/*
	wchar_t *x = L"normalize-line-breaks";
	hr = m_pXMLReader->putFeature(x, true);
	if (FAILED(hr)) {
		AfxMessageBox("Failed to put line breaks\n");
	}
	*/
	
	CBusyWaitCursor bwc;
	
	m_InterfaceMessage.Format("Synchronizing %s - Processing Location Confirmation Inbound", m_pGroup->m_Name);
	m_OverallProgressCtrl.StepIt();
	UpdateData(FALSE);
	if (m_pGroup->m_ExternalSystemName.Find("3.6") == 0) {
		
		if (ProcessInbound(CWMSGroupConnection::LocationConfirmationInterface, m_LocationOptions, 
			"Location Confirmation Inbound") < 0) {
			m_pXMLReader->putContentHandler(NULL);
			m_pXMLReader->Release();
			m_OverallProgressCtrl.SetPos(8);
			m_Details += "Remaining interfaces skipped.\r\n";
			m_InterfaceMessage.Format("Synchronizing %s - Complete", m_pGroup->m_Name);
			m_InProgress = FALSE;
			UpdateData(FALSE);
			return;
		}
		
	}
	bwc.Restore();
	utilityHelper.PeekAndPump(0);
	
	m_InterfaceMessage.Format("Synchronizing %s - Processing Assignment Confirmation Inbound", m_pGroup->m_Name);
	m_OverallProgressCtrl.StepIt();
	UpdateData(FALSE);
	if (m_pGroup->m_ExternalSystemName.Find("3.6") == 0) {
		if (ProcessInbound(CWMSGroupConnection::AssignmentConfirmationInterface, m_AssignmentOptions, 
			"Assignment Confirmation Inbound") < 0) {
			m_pXMLReader->putContentHandler(NULL);
			m_pXMLReader->Release();
			m_OverallProgressCtrl.SetPos(8);
			m_Details += "Remaining interfaces skipped.\r\n";
			m_InterfaceMessage.Format("Synchronizing %s - Complete", m_pGroup->m_Name);
			m_InProgress = FALSE;
			UpdateData(FALSE);
			return;
		}
	}
	
	bwc.Restore();
	utilityHelper.PeekAndPump(0);
	

	m_InterfaceMessage.Format("Synchronizing %s - Processing Location Inbound", m_pGroup->m_Name);
	m_OverallProgressCtrl.StepIt();
	UpdateData(FALSE);
	if (ProcessInbound(CWMSGroupConnection::LocationInterface, m_LocationOptions, "Location Inbound") < 0) {
		m_pXMLReader->putContentHandler(NULL);
		m_pXMLReader->Release();
		m_OverallProgressCtrl.SetPos(8);
		m_Details += "Remaining interfaces skipped.\r\n";
		m_InterfaceMessage.Format("Synchronizing %s - Complete", m_pGroup->m_Name);
		UpdateData(FALSE);
		m_InProgress = FALSE;
		return;
	}

	bwc.Restore();
	utilityHelper.PeekAndPump(0);
	
	m_InterfaceMessage.Format("Synchronizing %s - Processing Product Inbound", m_pGroup->m_Name);
	m_OverallProgressCtrl.StepIt();
	UpdateData(FALSE);
	if (ProcessInbound(CWMSGroupConnection::ProductInterface, m_ProductOptions, "Product Inbound") < 0) {
		m_pXMLReader->putContentHandler(NULL);
		m_pXMLReader->Release();
		m_OverallProgressCtrl.SetPos(8);
		m_Details += "Remaining interfaces skipped.\r\n";
		m_InterfaceMessage.Format("Synchronizing %s - Complete", m_pGroup->m_Name);
		UpdateData(FALSE);
		m_InProgress = FALSE;
		return;
	}

	bwc.Restore();
	utilityHelper.PeekAndPump(0);

	m_InterfaceMessage.Format("Synchronizing %s - Processing Assignment/Move Inbound", m_pGroup->m_Name);
	m_OverallProgressCtrl.StepIt();
	UpdateData(FALSE);
	if (ProcessInbound(CWMSGroupConnection::AssignmentInterface, m_AssignmentOptions, "Assignment/Move Inbound") < 0) {
		m_pXMLReader->putContentHandler(NULL);
		m_pXMLReader->Release();
		m_OverallProgressCtrl.SetPos(8);
		m_Details += "Remaining interfaces skipped.\r\n";
		m_InterfaceMessage.Format("Synchronizing %s - Complete", m_pGroup->m_Name);
		UpdateData(FALSE);
		m_InProgress = FALSE;
		return;
	}

	bwc.Restore();
	utilityHelper.PeekAndPump(0);

	/*
	m_InterfaceMessage.Format("Synchronizing %s - Processing Location Confirmation Outbound", m_pGroup->m_Name);
	m_OverallProgressCtrl.StepIt();
	UpdateData(FALSE);
	if (ProcessLocationConfirmationOutbound() < 0) {
		m_pXMLReader->putContentHandler(NULL);
		m_pXMLReader->Release();
		m_OverallProgressCtrl.SetPos(8);
		m_Details += "Remaining interfaces skipped.\r\n";
		m_InterfaceMessage.Format("Synchronizing %s - Complete", m_pGroup->m_Name);
		UpdateData(FALSE);
		m_InProgress = FALSE;
		return;
	}

	m_InterfaceMessage.Format("Synchronizing %s - Processing Product Confirmation Outbound", m_pGroup->m_Name);
	m_OverallProgressCtrl.StepIt();
	UpdateData(FALSE);
	if (ProcessProductConfirmationOutbound() < 0) {
		m_pXMLReader->putContentHandler(NULL);
		m_pXMLReader->Release();
		m_OverallProgressCtrl.SetPos(8);
		m_Details += "Remaining interfaces skipped.\r\n";
		m_InterfaceMessage.Format("Synchronizing %s - Complete", m_pGroup->m_Name);
		UpdateData(FALSE);
		m_InProgress = FALSE;
		return;
	}

	m_InterfaceMessage.Format("Synchronizing %s - Processing Assignment Confirmation Outbound", m_pGroup->m_Name);
	m_OverallProgressCtrl.StepIt();
	UpdateData(FALSE);
	if (ProcessAssignmentConfirmationOutbound() < 0) {
		m_pXMLReader->putContentHandler(NULL);
		m_pXMLReader->Release();
		m_OverallProgressCtrl.SetPos(8);
		m_Details += "Remaining interfaces skipped.\r\n";
		m_InterfaceMessage.Format("Synchronizing %s - Complete", m_pGroup->m_Name);
		UpdateData(FALSE);
		m_InProgress = FALSE;
		return;
	}
	*/

	bwc.Restore();
	utilityHelper.PeekAndPump(0);
	
	m_InterfaceMessage.Format("Synchronizing %s - Processing Location Outbound", m_pGroup->m_Name);
	m_OverallProgressCtrl.StepIt();
	UpdateData(FALSE);
	if (ProcessLocationOutbound() < 0) {
		m_pXMLReader->putContentHandler(NULL);
		m_pXMLReader->Release();
		m_OverallProgressCtrl.SetPos(8);
		m_Details += "Remaining interfaces skipped.\r\n";
		m_InterfaceMessage.Format("Synchronizing %s - Complete", m_pGroup->m_Name);
		UpdateData(FALSE);
		m_InProgress = FALSE;
		return;
	}

	bwc.Restore();
	utilityHelper.PeekAndPump(0);

	m_InterfaceMessage.Format("Synchronizing %s - Processing Assignment/Move Outbound", m_pGroup->m_Name);
	m_OverallProgressCtrl.StepIt();
	UpdateData(FALSE);
	if (ProcessAssignmentOutbound() < 0) {
		m_pXMLReader->putContentHandler(NULL);
		m_pXMLReader->Release();
		m_OverallProgressCtrl.SetPos(8);
		m_Details += "Remaining interfaces skipped.\r\n";
		m_InterfaceMessage.Format("Synchronizing %s - Complete", m_pGroup->m_Name);
		UpdateData(FALSE);
		m_InProgress = FALSE;
		return;
	}

	bwc.Restore();
	utilityHelper.PeekAndPump(0);

	m_InterfaceMessage.Format("Synchronizing %s - Complete", m_pGroup->m_Name);
	m_OverallProgressCtrl.StepIt();
	m_Details += "Synchronization Complete.\r\n";
	UpdateData(FALSE);

	m_pXMLReader->putContentHandler(NULL);
	m_pXMLReader->Release();

	m_InProgress = FALSE;

}

void CIntegrationStatusDialog::OnOptions() 
{
	if (m_InProgress)
		return;

	CPropertySheet sheet;
	CIntegrationOptionsPage optionsPage;
	CIntegrationLocationOptionsPage locPage;
	CIntegrationProductOptionsPage prodPage;
	CIntegrationAssignmentOptionsPage assgPage;

	sheet.AddPage(&optionsPage);
	sheet.AddPage(&locPage);
	sheet.AddPage(&prodPage);
	sheet.AddPage(&assgPage);

	CString temp;


	optionsPage.m_DetailXMLLogging = (m_GeneralOptions & DetailXMLLogging) != 0;
	optionsPage.m_SkipXMLLogging = (m_GeneralOptions & SkipXMLLogging) != 0;
	optionsPage.m_SaveOptions = (m_GeneralOptions & SaveOptions) != 0;

	locPage.m_AutoConfirm = (m_LocationOptions & AutoConfirm) != 0;
	locPage.m_FullExport = (m_LocationOptions & FullExport) != 0;
	locPage.m_InboundPrompt = (m_LocationOptions & InboundPrompt) != 0;
	locPage.m_OutboundPrompt = (m_LocationOptions & OutboundPrompt) != 0;
	locPage.m_NoUpdate = (m_LocationOptions & SkipStatusUpdate) != 0;
	locPage.m_SkipLocation = (m_LocationOptions & SkipInterface) != 0;

	prodPage.m_SkipPgUpdate = (m_ProductOptions & SkipProductGroupUpdate) != 0;
	prodPage.m_InboundPrompt = (m_ProductOptions & InboundPrompt) != 0;
	prodPage.m_SkipProduct = (m_ProductOptions & SkipInterface) != 0;

	assgPage.m_AutoConfirm = (m_AssignmentOptions & AutoConfirm) != 0;
	assgPage.m_FullExport = (m_AssignmentOptions & FullExport) != 0;
	assgPage.m_InboundPrompt = (m_AssignmentOptions & InboundPrompt) != 0;
	assgPage.m_OutboundPrompt = (m_AssignmentOptions & OutboundPrompt) != 0;
	assgPage.m_SkipAssignment = (m_AssignmentOptions & SkipInterface) != 0;
	assgPage.m_AllowNotIntegrated = (m_AssignmentOptions & AllowNotIntegrated) != 0;

	int rc;
	try {
		rc = sheet.DoModal();
	}
	catch (...) {
		controlService.Log("Error displaying interface options.", "Generic exception in property sheet.\n");
		return;
	}

	if (rc == IDCANCEL)
		return;

	m_LocationOptions = 0;
	m_GeneralOptions = 0;
	m_ProductOptions = 0;
	m_AssignmentOptions = 0;

	if (optionsPage.m_SkipXMLLogging)
		m_GeneralOptions |= SkipXMLLogging;

	if (optionsPage.m_DetailXMLLogging)
		m_GeneralOptions |= DetailXMLLogging;

	if (optionsPage.m_SaveOptions)
		m_GeneralOptions |= SaveOptions;

	if (locPage.m_AutoConfirm)
		m_LocationOptions |= AutoConfirm;

	if (locPage.m_FullExport)
		m_LocationOptions |= FullExport;

	if (locPage.m_InboundPrompt)
		m_LocationOptions |= InboundPrompt;

	if (locPage.m_OutboundPrompt)
		m_LocationOptions |= OutboundPrompt;

	if (locPage.m_NoUpdate)
		m_LocationOptions |= SkipStatusUpdate;

	if (locPage.m_SkipLocation)
		m_LocationOptions |= SkipInterface;

	if (prodPage.m_InboundPrompt)
		m_ProductOptions |= InboundPrompt;

	if (prodPage.m_SkipProduct)
		m_ProductOptions |= SkipInterface;

	if (prodPage.m_SkipPgUpdate)
		m_ProductOptions |= SkipProductGroupUpdate;

	if (assgPage.m_AutoConfirm)
		m_AssignmentOptions |= AutoConfirm;

	if (assgPage.m_FullExport)
		m_AssignmentOptions |= FullExport;

	if (assgPage.m_InboundPrompt)
		m_AssignmentOptions |= InboundPrompt;

	if (assgPage.m_OutboundPrompt)
		m_AssignmentOptions |= OutboundPrompt;

	if (assgPage.m_SkipAssignment)
		m_AssignmentOptions |= SkipInterface;

	if (assgPage.m_AllowNotIntegrated)
		m_AssignmentOptions |= AllowNotIntegrated;

	if (optionsPage.m_SaveOptions) {
		temp.Format("%d|%d|%d|"		// general options
			"%d|%d|%d|%d|%d|%d|"	// location options
			"%d|%d|%d|"				// product options
			"%d|%d|%d|%d|%d|%d|",		// assignment options
			optionsPage.m_SkipXMLLogging, optionsPage.m_DetailXMLLogging, optionsPage.m_SaveOptions,
			locPage.m_SkipLocation, locPage.m_InboundPrompt, locPage.m_OutboundPrompt, 
			locPage.m_FullExport, locPage.m_NoUpdate, locPage.m_AutoConfirm,
			prodPage.m_SkipProduct, prodPage.m_InboundPrompt, prodPage.m_SkipPgUpdate,
			assgPage.m_SkipAssignment, assgPage.m_InboundPrompt, assgPage.m_OutboundPrompt,
			assgPage.m_FullExport, assgPage.m_AutoConfirm, assgPage.m_AllowNotIntegrated);
		controlService.SetApplicationData("IntegrationOptions", temp, "Dialogs");
	}
	else
		controlService.SetApplicationData("IntegrationOptions", "", "Dialogs");
}

int CIntegrationStatusDialog::LoadWMSList()
{
	CStringArray wmsList;

	if (m_pGroup->m_WMSList.GetSize() > 0)
		return 0;

	try {
		integrationDataService.GetWMSList(m_pGroup->m_WMSGroupDBId, wmsList);
	}
	catch (...) {
		controlService.Log("Error getting WMS Section list for WMS Facility.", "Generic exception in GetWMSList.");
		return -1;
	}

	for (int i=0; i < wmsList.GetSize(); ++i) {
		CWMS *pWMS = new CWMS;
		pWMS->Parse(wmsList[i]);
		m_pGroup->m_WMSList.Add(pWMS);
	}

	return 0;
}


int CIntegrationStatusDialog::LoadGroup()
{
	CStringArray connectionList;

	if(m_pGroup->m_ConnectionList.GetSize() == 0) {
		if (LoadConnectionList() < 0)
			return -1;
	}
	
	if (m_pGroup->m_WMSList.GetSize() == 0) {
		if (LoadWMSList() < 0)
			return -1;
		
		if (LoadInboundMapList() < 0)
			return -1;
		
		if (LoadOutboundMapList() < 0)
			return -1;
	}

	m_pGroup->SetConnectionsByType();

	return 0;
}


int CIntegrationStatusDialog::LoadConnectionList()
{
	try {
		integrationDataService.LoadGroupConnectionList(*m_pGroup);
	}
	catch (...) {
		controlService.Log("Error getting connection information for WMS Facility.",
			"Generic exception in GetWMSConnectionList.");
		return -1;
	}

	return 0;

}


int CIntegrationStatusDialog::LoadInboundMapList()
{
	CStringArray mapList;

	for (int i=0; i < m_pGroup->m_WMSList.GetSize(); ++i) {
		CWMS *pWMS = m_pGroup->m_WMSList[i];

		mapList.RemoveAll();

		try {
			integrationDataService.GetWMSImportMap(pWMS->m_WMSDBId, mapList);
		}
		catch (...) {
			controlService.Log("Error loading inbound map list for WMS.",
				"Generic exception in GetWMSImportMap for %d\n", pWMS->m_WMSDBId);
			return -1;
		}

		for (int j=0; j < mapList.GetSize(); ++j) {
			CWMSMap *pMap = new CWMSMap;
			pMap->Parse(mapList[j]);
			pWMS->m_MapList.Add(pMap);
		}
	}

	return 0;
}

int CIntegrationStatusDialog::LoadOutboundMapList()
{
	CStringArray mapList;

	for (int i=0; i < m_pGroup->m_WMSList.GetSize(); ++i) {
		CWMS *pWMS = m_pGroup->m_WMSList[i];

		mapList.RemoveAll();

		try {
			integrationDataService.GetWMSExportMap(pWMS->m_WMSDBId, mapList);
		}
		catch (...) {
			controlService.Log("Error loading outbound map list for WMS.",
				"Generic exception in GetWMSExportMap for %d\n", pWMS->m_WMSDBId);
			return -1;
		}

		for (int j=0; j < mapList.GetSize(); ++j) {
			CWMSMap *pMap = new CWMSMap;
			pMap->Parse(mapList[j]);
			pWMS->m_MapList.Add(pMap);
		}
	}

	return 0;
}

int CIntegrationStatusDialog::ProcessInbound(int interfaceType, int options, const CString &interfaceName)
{
	CString temp;
	char *msg;
	int size = 0, rc;
	CExternalConnection *pConnection;
	int processedProducts = 0;
	CBusyWaitCursor bwc;

	if (options & SkipInterface) {
		temp.Format("%s skipped as specified in Options.\r\n", interfaceName);
		m_Details += temp;
		return 0;
	}

	pConnection = m_pGroup->GetExternalConnection(interfaceType, CWMSGroupConnection::Inbound);
	if (pConnection == NULL) {
		temp.Format("No connection found for %s.\r\n", interfaceName);
		m_Details += temp;
		return 0;
	}

	m_ProgressCtrl.SetRange32(0, 1);
	m_ProgressCtrl.SetPos(0);

	BOOL done = FALSE;

	while (! done) {
		
		done = TRUE;

		if (options & InboundPrompt) {
			rc = ReadUserMessage(interfaceType, &msg, size);
		}
		else {	
			switch (pConnection->m_ConnectionType) {
			case CExternalConnection::MQSeries:
				rc = BeginQueueTransaction(*pConnection);
				if (rc < 0)
					break;
				size = 0;
				rc = ReadQueueMessage(*pConnection, &msg, size);
				if (size > 0)
					done = FALSE;
				break;
			case CExternalConnection::FTP:
				rc = ReadRemoteMessage(*pConnection, &msg, size);
				break;
			case CExternalConnection::Local:
				rc = ReadLocalMessage(*pConnection, &msg, size);
				break;
			case CExternalConnection::Prompt:
				rc = ReadUserMessage(interfaceType, &msg, size);
				break;
			case CExternalConnection::XML_RPC:
				break;
			}
		}
		
		bwc.Restore();
		if (rc == 1) {
			temp.Format("No %s file found.\r\n", interfaceName);
			m_Details += temp;
			return 0;
		}
		else if (rc == 2) {
			temp.Format("Synchronization aborted.\r\n");
			m_Details += temp;
			return -1;
		}
		else if (rc < 0) {
			temp.Format("Unable to read message for %s.\r\n", interfaceName);
			m_Details += temp;
			return -1;
		}

		// Convert ampersands to the xml escape sequence (&amp;)
		// This doesn't seem to be working right now
		int ampCount = 0;

		char *ptr = msg;
		while (*ptr != NULL) {
			if (*ptr == '&')
				ampCount++;
			ptr++;
		}

		ampCount *= 4;
		
		size = size + ampCount;

		char *newMsg = (char *)malloc( size*sizeof(char));
		memset(newMsg, 0, size);
		char *ptr2 = newMsg;

		ptr = msg;
		while (*ptr != NULL) {
			*ptr2++ = *ptr;
			if (*ptr == '&') {
				*ptr2++ = 'a';
				*ptr2++ = 'm';
				*ptr2++ = 'p';
				*ptr2++ = ';';
			}
			ptr++;
		}

		CString fileName = BackupMessage(newMsg, size, interfaceType, FALSE);

		free(newMsg);
		free(msg);

		if (size > 0) {
			rc = ParseMessage(interfaceType, fileName, size, interfaceName);
			if (rc < 0) {
				if (pConnection->m_ConnectionType == CExternalConnection::MQSeries)
					AbortQueueTransaction(*pConnection);
				return -1;
			}
			
			int totalCount = 0, errorCount = 0;

			totalCount = ProcessInboundMessage(options, errorCount);
			
			if (totalCount > errorCount && interfaceType == CWMSGroupConnection::ProductInterface)
				processedProducts += (totalCount - errorCount);


			if (errorCount > 0) {
				CString backupFile = BackupMessage(fileName, interfaceType);
				if (backupFile != "") {
					CString temp;
					if (pConnection->m_ConnectionType == CExternalConnection::MQSeries) {
						AbortQueueTransaction(*pConnection);
						temp.Format("The %s process encountered errors.  The message file has been left on the queue.",
							interfaceName);
						AfxMessageBox(temp);
						bwc.Restore();
						
						temp.Format("The %s process encountered errors.  The message file has been backed up to:\r\n    %s\r\n",
							interfaceName, backupFile);
						m_Details += temp;
						UpdateData(FALSE);
						return -1;
					}
					else {
						temp.Format("The %s process encountered errors.  The message file has been backed up to:\n%s\n"
							"Correct the problem and restart the synchronization to re-process the file.",
							interfaceName, backupFile);
						AfxMessageBox(temp);
						bwc.Restore();
						
						temp.Format("The %s process encountered errors.  The message file has been backed up to:\r\n    %s\r\n"
							"    Correct the problem and restart the synchronization to re-process the file.\r\n",
							interfaceName, backupFile);
						m_Details += temp;
						UpdateData(FALSE);
				}

				}
			}
			temp.Format("%s message complete.  %d record%s processed. %d error%s.\r\n", interfaceName,
				totalCount, (totalCount == 1) ? "" : "s",
				errorCount, (errorCount == 1) ? "" : "s");
			m_Details += temp;

			if (errorCount == 0 &&  ! (options & InboundPrompt)) {
				DeleteInboundMessage(*pConnection);
			}
		}
		
	}


	// Update product group assignments after product inbound
	if (processedProducts > 0 && 
		interfaceType == CWMSGroupConnection::ProductInterface && ! (options & SkipProductGroupUpdate)) {
		CMap<int, int, int, int> facMap;
		for (int i=0; i < m_pGroup->m_WMSList.GetSize(); ++i) {
			CWMS *pWMS = m_pGroup->m_WMSList[i];
			
			for (int j=0; j < pWMS->m_MapList.GetSize(); ++j) {
				if (pWMS->m_MapList[j]->m_Direction == CWMSMap::importToOptimize) {
					
					int dummy;
					// Products are by facility not section so make sure we don't process
					// the same facility more than once
					if (facMap.Lookup(pWMS->m_MapList[j]->m_FacilityDBId, dummy)) {
						continue;
					}
					facMap.SetAt(pWMS->m_MapList[j]->m_FacilityDBId, 1);
					
					UpdateProductGroupAssignments(pWMS->m_MapList[j]->m_FacilityDBId);
				}
			}
		}
		
	}

	switch (interfaceType) {
	case CWMSGroupConnection::ProductInterface:
		m_Message = "Product Inbound Complete";
		break;
	case CWMSGroupConnection::LocationInterface:
		m_Message = "Location Inbound Complete";
		break;
	case CWMSGroupConnection::AssignmentInterface:
		m_Message = "Assignment Inbound Complete";
		break;
	}

	UpdateData(FALSE);


	return rc;

}

int CIntegrationStatusDialog::ParseMessage(int interfaceType, const CString &fileName, int size, const CString& interfaceName)
{
	CString temp;

	temp.Format("Parsing %s message.\r\n", interfaceName);
	m_Details += temp;
	m_Message += "Parsing XML...";

	UpdateData(FALSE);

	m_InboundQueue.RemoveAll();

	if (! (m_GeneralOptions & SkipXMLLogging)) {
		CString dateStr = utilityHelper.GetUTCFileDate();
		m_pXMLHandler->m_LogFileName.Format("%s\\Interface\\XMLLog_%s.dat", 
			controlService.m_ClientHome, dateStr);

		m_pXMLHandler->m_LogDetail = ((m_GeneralOptions & DetailXMLLogging) != 0);
		temp.Format("The following file contains a log of the XML Parser activity:\r\n\t%s\r\n",
			m_pXMLHandler->m_LogFileName);
		m_Details += temp;
	}
	else
		m_pXMLHandler->m_LogFileName = "";

	/*
	char *mbws = "\n\t";
	wchar_t *ws = (wchar_t *)malloc((strlen(mbws)+1)*sizeof(wchar_t));
	mbtowc(ws, mbws, strlen(mbws)+1;
	*/
	wchar_t *ws = L"\n\t";
	m_pXMLHandler->ignorableWhitespace(ws, wcslen(ws));

	m_pXMLHandler->m_pQueue = &m_InboundQueue;
	m_ParseFileName = fileName;

	CThreadParameters parm;
	parm.m_pWnd = this;
	CEvent event;
	parm.m_pEvent = &event;

	CWinThread *pThread = AfxBeginThread(CIntegrationStatusDialog::ParseMessageThread, &parm);

	BOOL bThreadDone = false;
	while (TRUE) {
		if ( ! utilityHelper.PeekAndPump() )
			break;
		
		bThreadDone = parm.m_pEvent->Lock(0);
		if (bThreadDone)
			break;
	}

	m_ParseFileName = "";

	int error = 0;
	int totalCount = 0, errorCount = 0;

	if (m_pXMLHandler->m_Error) {
		error = -1;
		temp.Format("An error occurred while parsing the %s message:\r\n    ", interfaceName);
		m_Details += temp;
		m_Details += m_pXMLHandler->m_ErrorText;
		UpdateData(FALSE);
	}

	if (m_pXMLHandler->m_Error) {
		CString backupFile = BackupMessage(fileName, interfaceType);
		if (backupFile != "") {
			CString temp;
			temp.Format("The %s process encountered errors.  The message file has been backed up to:\n%s\n"
				"Please correct the problem and use the manual inbound process to re-process the file.",
				interfaceName, backupFile);
			AfxMessageBox(temp);

			temp.Format("The %s process encountered errors.  The message file has been backed up to:\r\n    %s\r\n"
				"    Please correct the problem and use the manual inbound process to re-process the file.\r\n",
				interfaceName, backupFile);
			m_Details += temp;
			UpdateData(FALSE);
		}
	}


	return error;
}

UINT CIntegrationStatusDialog::ParseMessageThread(LPVOID pParam)
{
	CThreadParameters &parm = *(CThreadParameters *)pParam;

	CIntegrationStatusDialog *pDlg = (CIntegrationStatusDialog *)parm.m_pWnd;

	char url[1024];
	strcpy(url, "file://");
	strcat(url, pDlg->m_ParseFileName);

	wchar_t *wurl = (wchar_t *)malloc((strlen(url)+1)*sizeof(wchar_t));
	mbstowcs(wurl, url, strlen(url)+1);

	pDlg->m_pXMLReader->parseURL(wurl);

	free(wurl);

	parm.m_pEvent->SetEvent();

	return 0;
}

int CIntegrationStatusDialog::ProcessInboundMessage(int options, int &errorCount)
{
	CInboundQueueRecord *pRecord;
	CString temp;
	CStringArray locList, prodList, assgList;
	CStringArray locConfList, assgConfList;

	CMap<int, int, int, int> facMap, totalFacMap, batchMap;
	CMap<CString, LPCTSTR, int, int> facNotFoundMap;

	BOOL done = FALSE;
	int endOfBatch = 0, endOfFeed = 0;

	int addCount = 0;
	UINT totalCount = 0;
	int recordIdx = 0;
	int eventId = 0;

	errorCount = 0;
	

	m_ProgressCtrl.SetRange32(0, m_InboundQueue.GetSize());
	m_ProgressCtrl.SetStep(1);

	m_Message.Format("Storing items...", recordIdx, m_InboundQueue.GetSize());
	UpdateData(FALSE);

	while (! done) {
	
		CBusyWaitCursor bwc;

		if (recordIdx == m_InboundQueue.GetSize()) {
			// must not have gotten end of batch record
			endOfBatch = endOfFeed = 1;
			m_Details += "Warning: end of batch not found. Some items may not have been processed.\r\n";
			UpdateData(FALSE);
		}
		else {
			pRecord = m_InboundQueue[recordIdx];	
			// The addCount will help speed things up by allowing the inbound process
			// to generate a bunch of keys at once instead of one at a time
			if (pRecord->m_Action == CInterfaceHelper::Add)
				addCount++;
			// Assignment modifies require a new key
			else if (pRecord->m_InterfaceType == CWMSGroupConnection::AssignmentInterface &&
				pRecord->m_Action == CInterfaceHelper::Modify)
				addCount++;

			endOfBatch = pRecord->m_EndOfBatch;
			endOfFeed = pRecord->m_EndOfFeed;

			batchMap.SetAt(pRecord->m_BatchId, 1);

			//delete m_InboundQueue[recordIdx];
			//recordIdx++;
		}

		facMap.RemoveAll();

		if (! endOfBatch && ! endOfFeed) {
			
			if (pRecord->m_InterfaceType == CWMSGroupConnection::AssignmentConfirmationInterface) {
				CConfirmation *pConf = (CConfirmation *)pRecord->m_pRecord;
				temp.Format("%d|%d|%s|", pRecord->m_BatchId, pConf->m_ReasonCode, pConf->m_ReasonText);
				assgConfList.Add(temp);
			}
			
			else if (pRecord->m_InterfaceType == CWMSGroupConnection::LocationConfirmationInterface) {
				CConfirmation *pConf = (CConfirmation *)pRecord->m_pRecord;
				temp.Format("%d|%d|%s|", pRecord->m_BatchId, pConf->m_ReasonCode, pConf->m_ReasonText);
				locConfList.Add(temp);
			}
			else {
				
				totalCount++;
				
				int facStatus;
				if (! facNotFoundMap.Lookup(pRecord->m_WMSId, facStatus)) {
					facNotFoundMap.SetAt(pRecord->m_WMSId, 1);
				}
				else {
					facStatus++;
					facNotFoundMap.SetAt(pRecord->m_WMSId, facStatus);
				}

				for (int i=0; i < m_pGroup->m_WMSList.GetSize(); ++i) {
					
					if (m_pGroup->m_WMSId != pRecord->m_WMSId)
						continue;

					if (facNotFoundMap.Lookup(pRecord->m_WMSId, facStatus)) {
						facStatus--;
						facNotFoundMap.SetAt(pRecord->m_WMSId, facStatus);
					}

					CWMS *pWMS = m_pGroup->m_WMSList[i];
					// Inbound interfaces will never need to be run for the same facility twice
					// because we force locations and products to be unique within a facility, i.e.
					// different sections cannot have the same location or product
					// If they need the same location name in two different warehouses, they
					// have to put the warehouses into different facilities
					/*
					if (pRecord->m_InterfaceType = CWMSGroupConnection::LocationInterface &&
					pWMS->m_WMSId != pRecord->m_WMSDetailId)
					continue;
					*/
					
					// The WMS matches so import into all the facilities that are mapped to it
					for (int j=0; j < pWMS->m_MapList.GetSize(); ++j) {
						if (pWMS->m_MapList[j]->m_Direction == CWMSMap::importToOptimize) {
							
							int dummy;
							// Process each facility once on inbound - see above
							if (facMap.Lookup(pWMS->m_MapList[j]->m_FacilityDBId, dummy)) {
								continue;
							}
							facMap.SetAt(pWMS->m_MapList[j]->m_FacilityDBId, 1);
							
							if (! totalFacMap.Lookup(pWMS->m_MapList[j]->m_FacilityDBId, dummy))
								totalFacMap.SetAt(pWMS->m_MapList[j]->m_FacilityDBId, 1);
							
							// We use keys that are unique within a facility so we don't need to send the section
							temp.Format("%d|%d|%d|", m_pGroup->m_ExternalSystemDBId, pWMS->m_MapList[j]->m_FacilityDBId,
								0); //pWMS->m_MapList[j]->m_SectionDBId);
							
							temp += pRecord->Stream();
							
							switch (pRecord->m_InterfaceType) {
							case CWMSGroupConnection::LocationInterface:
								locList.Add(temp);
								break;
							case CWMSGroupConnection::ProductInterface:
								prodList.Add(temp);
								break;
							case CWMSGroupConnection::AssignmentInterface:
								assgList.Add(temp);
								break;
							}
							
						}
					}
				}
			}
		}

		try {
			int error;
			if ( (locList.GetSize() >= FLUSH_COUNT && endOfBatch) ||
				(locList.GetSize() > 0 && endOfFeed)) {

				if (eventId <= 0)
					eventId = utilityHelper.GetNextEventId(m_InboundQueue.GetSize());

				m_Message = "Updating facility.";
				UpdateData(FALSE);
				CBusyWaitCursor bwc;
				ProcessLocationInbound(locList, error, eventId, addCount);
				errorCount += error;
				addCount = 0;
				locList.RemoveAll();
			}
			
			if ( (prodList.GetSize() >= FLUSH_COUNT && endOfBatch) || 
				(prodList.GetSize() > 0 && endOfFeed)) {
				
				if (eventId <= 0)
					eventId = utilityHelper.GetNextEventId(m_InboundQueue.GetSize());


				m_Message = "Updating facility.";
				UpdateData(FALSE);
				CBusyWaitCursor bwc;
				ProcessProductInbound(prodList, error, eventId, addCount);
				addCount = 0;
				prodList.RemoveAll();
				errorCount += error;
			}
			
			if ( (assgList.GetSize() >= FLUSH_COUNT && endOfBatch) || 
				(assgList.GetSize() > 0 && endOfFeed)) {

				if (eventId <= 0)
					eventId = utilityHelper.GetNextEventId(m_InboundQueue.GetSize());

				m_Message = "Updating facility.";
				UpdateData(FALSE);
				CBusyWaitCursor bwc;
				ProcessAssignmentInbound(assgList, error, eventId, addCount);
				addCount = 0;
				assgList.RemoveAll();
				errorCount += error;

				CStringArray batchList;
				POSITION pos = batchMap.GetStartPosition();
				while (pos != NULL) {
					int batchId, dummy;
					batchMap.GetNextAssoc(pos, batchId, dummy);
					CString batch;
					batch.Format("%d", batchId);
					batchList.Add(batch);
				}

				CSolutionDataService s;
				CStringArray msgList;
				
				try {
					s.UpdateSolutionCaseCount(batchList, msgList);
				}
				catch (...) {
					m_Details += "An error occurred while incoming case counts were being updated.\r\n";
					UpdateData(FALSE);
				}
				
				if (msgList.GetSize() > 0) {
					m_Details += "Warning.  Some products do not fit in the assigned locations:\r\n";
					
					for (int i=0; i < msgList.GetSize(); ++i) {
						msgList[i].Replace("\n", "\r\n");
						msgList[i].Replace("\t", "\t\t");
						m_Details += "\t";
						m_Details += msgList[i];
					}
				}
				
				UpdateData(FALSE);
			}


			if ( (locConfList.GetSize() >= FLUSH_COUNT && endOfBatch) || 
					(locConfList.GetSize() > 0 && endOfFeed)) {
				m_Message = "Updating facility.";
				UpdateData(FALSE);
				CBusyWaitCursor bwc;
				integrationDataService.ProcessLocationConfirmation(locConfList);
				locConfList.RemoveAll();
			}

			if ( (assgConfList.GetSize() >= FLUSH_COUNT && endOfBatch) || 
					(assgConfList.GetSize() > 0 && endOfFeed)) {
				m_Message = "Updating facility.";
				UpdateData(FALSE);
				CBusyWaitCursor bwc;
				integrationDataService.ProcessAssignmentConfirmation(assgConfList);
				assgConfList.RemoveAll();
			}

			delete pRecord;
			recordIdx++;
		}
		catch (... ) {
			for (int i=recordIdx; i < m_InboundQueue.GetSize(); ++i)
				delete m_InboundQueue[i];
			m_InboundQueue.RemoveAll();
			m_Details += "Error processing inbound message.\r\n";
			UpdateData(FALSE);
			done = TRUE;
		}

		if (endOfFeed)
			done = TRUE;
	}
	
	POSITION pos = facNotFoundMap.GetStartPosition();
	while (pos != NULL) {
		int facStatus;
		CString wmsId;
		facNotFoundMap.GetNextAssoc(pos, wmsId, facStatus);
		if (facStatus > 0) {

			CString temp;
			temp.Format("Warning. The WMS %s was not found for %d record%s."
				" Check the Integration Setup to verify that the WMS Id is mapped to  a facility.\r\n",
				wmsId, facStatus, facStatus > 1 ? "s" : "");
			m_Details += temp;
			UpdateData(FALSE);
		}

	}

	m_ProgressCtrl.SetPos(m_InboundQueue.GetSize());

	m_InboundQueue.RemoveAll();

	return totalCount;
}


int CIntegrationStatusDialog::ProcessLocationOutbound()
{

	CString xml, temp;
	CStringArray locList;
	CMapStringToString defaultInfoMap, levelProfileInfoMap, locationInfoMap;
	int totalCount = 0, errorCount = 0;
	int fullExportBatchId = 1;
		
	if (m_LocationOptions & SkipInterface) {
		m_Details += "Location outbound skipped as specified in Options.\r\n";
		UpdateData(FALSE);
		return 0;
	}

	m_ProgressCtrl.SetRange32(0, 1);
	m_ProgressCtrl.SetPos(0);

	CExternalConnection connection;

	if (m_LocationOptions & OutboundPrompt) {
		connection.m_ConnectionType = CExternalConnection::Prompt;
	}
	else {

		if (m_pGroup->m_pLocOutboundConnection == NULL) {
			m_Details += "No connection information for location outbound.\r\n";
			m_ProgressCtrl.SetPos(1);
			UpdateData(FALSE);
			return 0;
		}
		else
			connection = *m_pGroup->m_pLocOutboundConnection->m_pExternalConnection;

	}

	m_Details += "Processing location outbound.\r\n";
	UpdateData(FALSE);


	int nextFeedId = dataAccessService.GetNextKey("DBInterfaceFeed", 1);

	CString dateStr;
	CTime tm = CTime::GetCurrentTime();
	dateStr = tm.FormatGmt("%Y-%m-%dT%H:%M:%S");

	xml.Format("<Feed>\n<ID>%d</ID>\n<Source>Optimize</Source>\n<Date>%s</Date>\n",
		nextFeedId, dateStr);

	// select remaining queue items and format into xml
	for (int i=0; i < m_pGroup->m_WMSList.GetSize(); ++i) {
		CWMS *pWMS = m_pGroup->m_WMSList[i];

		for (int j=0; j < pWMS->m_MapList.GetSize(); ++j) {
			CWMSMap *pMap = pWMS->m_MapList[j];

			if (pMap->m_Direction == CWMSGroupConnection::Inbound)
				continue;

			try {

				// Purify location queue by deleting duplicates and superceded records
				m_Message.Format("Searching outbound location queue for %s...", pWMS->m_Name);
				m_ProgressCtrl.SetRange(0, 100);
				m_ProgressCtrl.StepIt();
				UpdateData(FALSE);
				
				if (! (m_LocationOptions & FullExport)) {
					integrationDataService.PurifyLocationOutboundQueue(pMap->m_FacilityDBId, pMap->m_SectionDBId);
					
					utilityHelper.PeekAndPump(0);
					
					integrationDataService.UpdateBatchForLocationOutbound(pMap);
					
					m_Message.Format("Loading outbound location data for %s...", pWMS->m_Name);
					m_ProgressCtrl.StepIt();
					UpdateData(FALSE);
					
					utilityHelper.PeekAndPump(0);
					
					integrationDataService.GetExternalInfo(m_pGroup->m_ExternalSystemDBId, pMap, defaultInfoMap, 
						levelProfileInfoMap, locationInfoMap);
					
					utilityHelper.PeekAndPump(0);
					
					integrationDataService.GetLocationOutboundData(pWMS, pMap, locList);
					
				}
				else {

					fullExportBatchId = dataAccessService.GetNextKey("DBInterfaceBatch", 1);

					integrationDataService.GetAllExternalInfo(m_pGroup->m_ExternalSystemDBId, pMap->m_FacilityDBId,
						pMap->m_SectionDBId, defaultInfoMap, levelProfileInfoMap, locationInfoMap);
					
					utilityHelper.PeekAndPump(0);
					
					locList.RemoveAll();
					integrationDataService.GetAllLocationOutboundData(pMap->m_FacilityDBId,
						pMap->m_SectionDBId, locList);
					
				}
				
				utilityHelper.PeekAndPump(0);

				m_ProgressCtrl.SetRange32(0, locList.GetSize());
				m_ProgressCtrl.SetStep(1);
				m_ProgressCtrl.SetPos(0);
				m_Message.Format("Formatting outbound location data for %s...", pWMS->m_Name);
				UpdateData(FALSE);

				int batchId, prevBatchId = -1, batchCount = 0;
				CString t;

				totalCount += locList.GetSize();

				for (int i=0; i < locList.GetSize(); ++i) {
					
					CString tempXML("");

					batchId = FormatLocationOutbound(pWMS, locList[i], tempXML, defaultInfoMap, 
						levelProfileInfoMap, locationInfoMap);
					batchCount++;

					if (m_LocationOptions & FullExport) {
						if (prevBatchId < 0) {
							t.Format("<Batch>\n<ID>%d</ID>\n<NumberOfRecords>%d</NumberOfRecords>\n", 
								batchId, locList.GetSize());
							xml += t;
						}
						prevBatchId = fullExportBatchId;
					}
					else {
						
						if (batchId < 0) {
							errorCount++;
							m_ProgressCtrl.StepIt();
							continue;
						}
						
						// Start a new batch
						if (batchId != prevBatchId) {
							if (prevBatchId > 0) {
								xml += "</Batch>\n";
								batchCount = 0;
							}
							t.Format("<Batch>\n<ID>%d</ID>\n<NumberOfRecords>%d</NumberOfRecords>\n", 
								batchId, batchCount);
							xml += t;
							
							prevBatchId = batchId;
						}
					}

					xml += tempXML;
					
					m_ProgressCtrl.StepIt();
				}

				xml += "</Batch>\n";
			}
			catch (...) {
				controlService.Log("Error getting location outbound items.", 
					"Generic exception in ProcessLocationOutbound\n");
				int lower, upper;
				m_ProgressCtrl.GetRange(lower, upper);
				if (upper == 0) {
					m_ProgressCtrl.SetRange32(0, 1);
					upper = 1;
				}
				m_ProgressCtrl.SetPos(upper);
				return -1;
			}
			
		}
	}

	xml += "</Feed>";

	if (totalCount == 0) {
		m_Message.Format("Location outbound complete.");
		m_Details += "Location outbound complete.  No records available to send.\r\n";
		int lower, upper;
		m_ProgressCtrl.GetRange(lower, upper);
		if (upper == 0) {
			m_ProgressCtrl.SetRange(0, 1);
			upper = 1;
		}
		m_ProgressCtrl.SetPos(upper);
		UpdateData(FALSE);
		
		return 0;
	}
	else if (errorCount > 0) {
		m_Message.Format("Location outbound complete.");
		CString temp;
		temp.Format("Location outbound complete.  %d record%s processed. %d error%s occurred.\r\n"
			"All locations will be resent.\r\n",
			totalCount, (totalCount == 1) ? "" : "s",
			errorCount, (errorCount == 1) ? "" : "s");
		m_Details += temp;
		UpdateData(FALSE);

		AfxMessageBox("At least one error occurred during the location outbound.\n"
			"The outbound locations will be resent during the next synchronization.");
	
		return 0;
	}


	m_Message.Format("Sending location outbound message...");
	UpdateData(FALSE);

	int rc;

	switch (connection.m_ConnectionType) {
	case CExternalConnection::MQSeries:
		rc = WriteQueueMessage(connection, xml);
		break;
	case CExternalConnection::FTP:
		rc = WriteRemoteMessage(connection, xml);
		break;
	case CExternalConnection::Local:
		rc = WriteLocalMessage(connection, xml);
		break;
	case CExternalConnection::Prompt:
		rc = WriteUserMessage(CWMSGroupConnection::LocationInterface, xml);
		break;
	case CExternalConnection::XML_RPC:
		break;
	}

	if (rc >= 0) {

		if (m_LocationOptions & SkipStatusUpdate) {
			m_Details += "Skipping location outbound queue status update as specified in Options.\r\n";
			UpdateData(FALSE);
		}
		else {
			m_Details += "Updating location outbound queue status.\r\n";
			UpdateData(FALSE);
			
			for (i=0; i < m_pGroup->m_WMSList.GetSize(); ++i) {
				CWMS *pWMS = m_pGroup->m_WMSList[i];
				
				for (int j=0; j < pWMS->m_MapList.GetSize(); ++j) {
					CWMSMap *pMap = pWMS->m_MapList[j];
					
					if (pMap->m_Direction == CWMSGroupConnection::Inbound)
						continue;
					
					
					try {
						BOOL autoConfirm = (m_LocationOptions & AutoConfirm);
						BOOL fullExport = (m_LocationOptions & FullExport);
						integrationDataService.UpdateStatusForLocationOutbound(pMap, autoConfirm, fullExport);
					}
					catch (...) {
						controlService.Log("Error updating location outbound status.",
							"Generic exception in UpdateStatusForLocationOutbound\n");
					}
				}
			}
		}
	}
	else {
		m_Message.Format("Location outbound aborted.");
		m_Details += "Location outbound aborted.\r\n";

		return 0;
	}

	m_Message.Format("Location outbound complete.");
	temp.Format("Location outbound complete.  %d record%s processed. %d error%s\r\n", totalCount,
		(totalCount == 1) ? "" : "s",
		errorCount, (errorCount == 1) ? "" : "s");
	m_Details += temp;

	UpdateData(FALSE);

	return 0;
}

int CIntegrationStatusDialog::FormatLocationOutbound(CWMS *pWMS, const CString &location, CString &xml,
													 CMapStringToString &defaultInfoMap,
													 CMapStringToString &levelProfileInfoMap,
													 CMapStringToString &locationInfoMap)
{
	CStringArray strings;
	CString name, value, defaultValue;
	int batchId;
	CString t, temp;
	
	strings.RemoveAll();
	utilityHelper.ParseString(location, "|", strings);
	

	batchId = atoi(strings[1]);

	t.Format("<Location>\n<LineNumber>%s</LineNumber>\n", strings[2]);
	xml += t;
	
	xml += utilityHelper.AddXML("Key", strings[29]);		
	
	CString action;
	switch (atoi(strings[0])) {
	case CInterfaceHelper::Add:
		action = "Add";
		break;
	case CInterfaceHelper::Modify:
		action = "Modify";
		break;
	case CInterfaceHelper::Delete:
		action = "Delete";
		break;
	default:
		m_Details += "Invalid action.\r\n";
		UpdateData(FALSE);
		return -1;
	}
	
	xml += utilityHelper.AddXML("Action", action);
	xml += utilityHelper.AddXML("DCID", m_pGroup->m_WMSId);
	xml += utilityHelper.AddXML("WarehouseID", pWMS->m_WMSId);
	xml += utilityHelper.AddXML("WMSID", m_pGroup->m_WMSId);
	xml += utilityHelper.AddXML("WMSDetailID", pWMS->m_WMSId);
	
	if (action != "Delete") {
		
		// Actual location attributes
		BOOL isOverridden = atoi(strings[14]);
		
		xml += utilityHelper.AddXML("Name", strings[4]);
		
		CString rackType;
		
		switch (atoi(strings[33])) {
		case BAYTYPE_BIN:
			rackType = "RK";
			break;
		case BAYTYPE_PALLET:
			rackType = "RK";
			break;
		case BAYTYPE_FLOOR:
			rackType = "FR";
			break;
		case BAYTYPE_DRIVEIN:
			rackType = "DI";
			break;
		case BAYTYPE_CASEFLOW:
			rackType = "FW";
			break;
		case BAYTYPE_PALLETFLOW:
			rackType = "FW";
			break;
		}

		FILE *f = fopen("dscdebug.txt", "a+");

		for (int p = 0; p < 38; p++) {
			fprintf(f, "%d = %s.\n", p, strings[p]);
		}

		fclose (f);
		
		xml += utilityHelper.AddXML("RackType", rackType);
		//int category = (isOverridden) ? atoi(strings[6]) : atoi(strings[31]);
		//xml += utilityHelper.AddXML("Category", category ? "Selection" : "Reserve");

		if (atoi(strings[35]) > 0)
			xml += utilityHelper.AddXML("Category", "Selection");
		else
			xml += utilityHelper.AddXML("Category", "Reserve");
		
		int handling = (isOverridden) ? atoi(strings[5]) : atoi(strings[30]);
		xml += utilityHelper.AddXML("Handling", handling == PALLET_HANDLING ? "Pallet" : "Case");
		
		xml += utilityHelper.AddXML("XCoordinate", strings[11]);
		xml += utilityHelper.AddXML("YCoordinate", strings[12]);
		xml += utilityHelper.AddXML("ZCoordinate", strings[13]);
		xml += utilityHelper.AddXML("SelectionPositions", strings[35]);
		xml += utilityHelper.AddXML("SelectionPositionHeight", strings[9]);
		xml += utilityHelper.AddXML("ReservePositions", strings[36]);
		xml += utilityHelper.AddXML("ReservePositionHeight", strings[37]);
		xml += utilityHelper.AddXML("Depth", strings[8]);
		xml += utilityHelper.AddXML("Width", strings[7]);
		//xml += utilityHelper.AddXML("StackLimit", "0");
		xml += utilityHelper.AddXML("Level", strings[32]);
		xml += utilityHelper.AddXML("SelectionSequence", strings[27]);
		xml += utilityHelper.AddXML("ReplenishmentSequence", strings[28]);
		xml += utilityHelper.AddXML("MaximumLocationWeight", strings[10]);
		
		CString backfillId = strings[18];
		backfillId.Replace("%L", strings[4]);
		backfillId.Replace("%l", strings[4]);
		xml += utilityHelper.AddXML("BackfillID", backfillId);
		
		xml += utilityHelper.AddXML("BackfillXCoordinate", atoi(strings[19]));
		xml += utilityHelper.AddXML("BackfillYCoordinate", atoi(strings[20]));
		xml += utilityHelper.AddXML("BackfillZCoordinate", atoi(strings[21]));
		
		xml += utilityHelper.AddXML("StockerPoint", (strings[22] != " ") ? strings[22] : "");
		xml += utilityHelper.AddXML("StockerXCoordinate", atoi(strings[23]));
		xml += utilityHelper.AddXML("StockerYCoordinate", atoi(strings[24]));
		xml += utilityHelper.AddXML("StockerZCoordinate", atoi(strings[25]));
		
		// Format the external attributes
		CString locationDBId = strings[3];
		CString levelProfileDBId = strings[40];
		
		POSITION pos = defaultInfoMap.GetStartPosition();	
		while (pos != NULL) {
			defaultInfoMap.GetNextAssoc(pos, name, defaultValue);
			
			temp.Format("%s-%s", locationDBId, name);
			if (! locationInfoMap.Lookup(temp, value)) {
				temp.Format("%s-%s", levelProfileDBId, name);
				if (! levelProfileInfoMap.Lookup(temp, value))
					value = defaultValue;
			}
			
			CString tempName(name);
			tempName.Replace(" ", "");
			xml += utilityHelper.AddXML(tempName, value);
		}
	}
	
	t.Format("</Location>\n");
	xml += t;


	return batchId;
}

int CIntegrationStatusDialog::ProcessAssignmentOutbound()
{
	CString xml, temp;
	CStringArray assgList, facChainList;

	int totalCount = 0, errorCount = 0;
		
	if (m_AssignmentOptions & SkipInterface) {
		m_Details += "Assignment/Move outbound skipped as specified in Options.\r\n";
		UpdateData(FALSE);
		return 0;
	}

	m_ProgressCtrl.SetRange32(0, 1);
	m_ProgressCtrl.SetPos(0);

	CExternalConnection connection;

	if (m_AssignmentOptions & OutboundPrompt) {
		connection.m_ConnectionType = CExternalConnection::Prompt;
	}
	else {
		if (m_pGroup->m_pAssgOutboundConnection == NULL) {
			m_Details += "No connection information for assignment outbound.\r\n";
			m_ProgressCtrl.SetPos(1);
			UpdateData(FALSE);
			return 0;
		}
		else
			connection = *m_pGroup->m_pAssgOutboundConnection->m_pExternalConnection;

	}

	m_Details += "Processing assignment/move outbound.\r\n";
	UpdateData(FALSE);


	int nextFeedId = dataAccessService.GetNextKey("DBInterfaceFeed", 1);

	CString dateStr;
	CTime tm = CTime::GetCurrentTime();
	dateStr = tm.FormatGmt("%Y-%m-%dT%H:%M:%S");

	xml.Format("<Feed>\n<ID>%d</ID>\n<Source>Optimize</Source>\n<Date>%s</Date>\n",
		nextFeedId, dateStr);

	// select remaining queue items and format into xml
	CMap<int, int, int, int> facMap;

	for (int i=0; i < m_pGroup->m_WMSList.GetSize(); ++i) {
		CWMS *pWMS = m_pGroup->m_WMSList[i];

		for (int j=0; j < pWMS->m_MapList.GetSize(); ++j) {
			CWMSMap *pMap = pWMS->m_MapList[j];

			if (pMap->m_Direction == CWMSGroupConnection::Inbound)
				continue;

			// Make sure were only process each facility once
			int dummy;
			if (facMap.Lookup(pMap->m_FacilityDBId, dummy))
				continue;

			facMap.SetAt(pMap->m_FacilityDBId, 1);
			
			LoadSearchAnchorList(pMap->m_FacilityDBId);

			try {

				m_Message.Format("Loading outbound assignment/move data for %s...", pWMS->m_Name);
				m_ProgressCtrl.StepIt();
				UpdateData(FALSE);
				
				utilityHelper.PeekAndPump(0);

				assgList.RemoveAll();
				if (m_AssignmentOptions & FullExport) {
					BOOL integratedOnly = TRUE;
					if (m_AssignmentOptions & AllowNotIntegrated)
						integratedOnly = FALSE;

					int batchId = dataAccessService.GetNextKey("DBInterfaceBatch", 1);
					integrationDataService.GetAllAssignmentOutboundData(pMap->m_FacilityDBId, batchId, assgList,
						integratedOnly);
					facChainList.Add("");
				}
				else {
					CString chainList;
					GetMoves(pWMS, pMap, chainList, assgList);
					facChainList.Add(chainList);
				}
				
				utilityHelper.PeekAndPump(0);

				m_ProgressCtrl.SetRange32(0, assgList.GetSize());
				m_ProgressCtrl.SetStep(1);
				m_ProgressCtrl.SetPos(0);
				m_Message.Format("Formatting outbound assignment/move data for %s...", pWMS->m_Name);
				UpdateData(FALSE);

				int batchId, prevBatchId = -1;
				CString t;

				totalCount += assgList.GetSize();

				for (int i=0; i < assgList.GetSize(); ++i) {
					
					CString tempXML("");

					batchId = FormatAssignmentOutbound(pWMS, pMap, assgList[i], i+1, tempXML);
					
					if (batchId < 0) {
						errorCount++;
						m_ProgressCtrl.StepIt();
						continue;
					}

					// Start a new batch
					if (batchId != prevBatchId) {
						if (prevBatchId > 0) {
							xml += "</Batch>\n";
						}
						
						int batchCount = 0;
						for (int j=i; j < assgList.GetSize(); ++j) {
							if (atoi(assgList[j].Left(assgList[j].Find("|"))) == batchId)
								batchCount++;
							else
								break;
						}

						t.Format("<Batch>\n<ID>%d</ID>\n<NumberOfRecords>%d</NumberOfRecords>", 
							batchId, batchCount);
						
						xml += t;
						
						prevBatchId = batchId;
					}

					xml += tempXML;
					
					m_ProgressCtrl.StepIt();
				}

				xml += "</Batch>\n";
			}
			catch (...) {
				controlService.Log("Error getting assignment/move outbound items.", 
					"Generic exception in ProcessAssignmentOutbound\n");
				int lower, upper;
				m_ProgressCtrl.GetRange(lower, upper);
				if (upper == 0) {
					m_ProgressCtrl.SetRange32(0, 1);
					upper = 1;
				}
				m_ProgressCtrl.SetPos(upper);
				return -1;
			}
			
		}
	}

	xml += "</Feed>";

	if (totalCount == 0) {
		m_Message.Format("Assignment/Move outbound complete.");
		m_Details += "Assignment/Move outbound complete.  No records available to send.\r\n";
		int lower, upper;
		m_ProgressCtrl.GetRange(lower, upper);
		if (upper == 0) {
			m_ProgressCtrl.SetRange(0, 1);
			upper = 1;
		}
		m_ProgressCtrl.SetPos(upper);
		UpdateData(FALSE);
		
		return 0;
	}
	else if (errorCount > 0) {
		m_Message.Format("Assignment/Move outbound complete.");
		CString temp;
		temp.Format("Assignment/Move outbound complete.  %d record%s processed. %d error%s occurred.\r\n"
			"No records were sent.\r\n",
			totalCount, (totalCount == 1) ? "" : "s",
			errorCount, (errorCount == 1) ? "" : "s");
		m_Details += temp;
		UpdateData(FALSE);

		AfxMessageBox("At least one error occurred during the assignment/move outbound.\n"
			"No records were sent at this time.");
	
		return 0;
	}


	m_Message.Format("Sending assignment/move outbound message...");
	UpdateData(FALSE);

	int rc;

	switch (connection.m_ConnectionType) {
	case CExternalConnection::MQSeries:
		rc = WriteQueueMessage(connection, xml);
		break;
	case CExternalConnection::FTP:
		rc = WriteRemoteMessage(connection, xml);
		break;
	case CExternalConnection::Local:
		rc = WriteLocalMessage(connection, xml);
		break;
	case CExternalConnection::Prompt:
		rc = WriteUserMessage(CWMSGroupConnection::AssignmentInterface, xml);
		break;
	case CExternalConnection::XML_RPC:
		break;
	}

	if (rc >= 0) {
		
		m_Details += "Updating assignment/move outbound queue status.\r\n";
		UpdateData(FALSE);
		
		CMap<int, int, int, int> facMap;
	
		int facIdx = 0;

		for (i=0; i < m_pGroup->m_WMSList.GetSize(); ++i) {
			CWMS *pWMS = m_pGroup->m_WMSList[i];
			
			for (int j=0; j < pWMS->m_MapList.GetSize(); ++j) {
				CWMSMap *pMap = pWMS->m_MapList[j];
				
				if (pMap->m_Direction == CWMSGroupConnection::Inbound)
					continue;
				
				// Make sure were only process each facility once
				int dummy;
				if (facMap.Lookup(pMap->m_FacilityDBId, dummy))
					continue;
				
				facMap.SetAt(pMap->m_FacilityDBId, 1);


				try {
					BOOL autoConfirm = (m_AssignmentOptions & AutoConfirm);
					BOOL fullExport = (m_AssignmentOptions & FullExport);
					integrationDataService.UpdateAssignmentOutboundStatus(pMap, facChainList[facIdx], autoConfirm, fullExport);
				}
				catch (...) {
					controlService.Log("Error updating assignmment/move outbound status.",
						"Generic exception in UpdateAssignmentOutboundStatus\n");
				}
			}
			facIdx++;
		}
	}
	else {
		m_Message.Format("Assignment/move outbound aborted.");
		m_Details += "Assignment/move outbound aborted.\r\n";

		return 0;
	}

	m_Message.Format("Assignment/move outbound complete.");
	temp.Format("Assignment/move outbound complete.  %d record%s processed. %d error%s\r\n", totalCount,
		(totalCount == 1) ? "" : "s",
		errorCount, (errorCount == 1) ? "" : "s");
	m_Details += temp;

	UpdateData(FALSE);

	return 0;


	return 0;
}

int CIntegrationStatusDialog::MessageType(char *msg, int size)
{
	return 0;
}

int CIntegrationStatusDialog::BeginQueueTransaction(CExternalConnection &connection)
{
	if (m_pMQHelper == NULL) {
		try {
			m_pMQHelper = new CMessageQueueHelper;
		}
		catch (Ssa_Exception e) {
			controlService.Log("Error in Connect library.", &e);
			return -1;
		}
	}

	int rc = m_pMQHelper->OpenQueue(connection.m_Host, connection.m_Port, connection.m_QueueManager,
		connection.m_Queue, connection.m_Channel);
	if (rc < 0) {
		CString temp;
		temp.Format("Error(%d) opening inbound queue: %s", rc, connection.m_Queue);
		controlService.Log("The inbound Connect queue could not be opened.", temp + "\n");
		m_Details += temp;
		m_Details += "\r\n";
		UpdateData(FALSE);
		m_pMQHelper = NULL;
		return -1;
	}

	return 0;
}

int CIntegrationStatusDialog::ReadQueueMessage(CExternalConnection &connection, char **msg, int &size)
{
	if (m_pMQHelper == NULL) {
		return -1;
	}

	int rc = m_pMQHelper->GetMessage(msg);
	if (rc < 0) {
		size = 0;
		CString temp;
		temp.Format("Error(%d) reading from inbound queue: %s", rc, connection.m_Queue);
		controlService.Log("Error reading from inbound queue.", temp + "\n");
		m_Details += temp;
		m_Details += "\r\n";
		UpdateData(FALSE);
		return -1;
	}

	size = strlen(*msg);

	return 0;

}

int CIntegrationStatusDialog::AbortQueueTransaction(CExternalConnection &connection)
{
	if (m_pMQHelper == NULL) {
		return -1;
	}

	m_pMQHelper->Rollback();

	m_pMQHelper->CloseQueue();

	return 0;
}

int CIntegrationStatusDialog::CommitQueueTransaction(CExternalConnection &connection)
{
	if (m_pMQHelper == NULL) {
		return -1;
	}

	m_pMQHelper->Commit();

	m_pMQHelper->CloseQueue();

	return 0;
}


int CIntegrationStatusDialog::ReadRemoteMessage(CExternalConnection &connection, char **msg, int &size)
{
	CBusyWaitCursor bwc;

	HINTERNET hInternet, hConnect, hFile;
	
	hInternet = InternetOpen("Optimize", INTERNET_OPEN_TYPE_DIRECT, NULL, NULL, NULL);
	if (hInternet == NULL) {
		CString temp;
		temp.Format("Error in InternetOpen (%s %s\\%s): %s\n",
			connection.m_Host, connection.m_Path, connection.m_FileName, LastError());
		controlService.Log("Error getting file from host.", temp);
		return -1;
	}
	
	hConnect = InternetConnect(hInternet, connection.m_Host, INTERNET_DEFAULT_FTP_PORT, connection.m_Login,
		connection.m_Password, INTERNET_SERVICE_FTP, NULL, NULL);
	if (hConnect == NULL) {
		CString temp;
		temp.Format("Error in InternetConnect (%s %s\\%s): %s\n",
			connection.m_Host, connection.m_Path, connection.m_FileName, LastError());
		controlService.Log("Error getting file from host.", temp);
		InternetCloseHandle(hInternet);
		return -1;
	}
	
	if (! FtpSetCurrentDirectory(hConnect, connection.m_Path)) {
		CString temp;
		temp.Format("Error in FtpSetCurrentDirectory (%s %s/%s): %s\n",
			connection.m_Host, connection.m_Path, connection.m_FileName, LastError());
		controlService.Log("Error getting file from host.", temp);
		InternetCloseHandle(hConnect);
		InternetCloseHandle(hInternet);
		return -1;
	}
	
	if (connection.m_TriggerName != "") {
		hFile = FtpOpenFile(hConnect, connection.m_TriggerName, GENERIC_READ, FTP_TRANSFER_TYPE_BINARY, NULL);
		if (hFile == NULL) {
			CString temp;
			temp.Format("Unable to find trigger file. FtpOpenFile (%s %s/%s): %s\n",
				connection.m_Host, connection.m_Path, connection.m_TriggerName, LastError());
			controlService.Log("", temp);
			InternetCloseHandle(hConnect);
			InternetCloseHandle(hInternet);
			return 1;
		}
	}
	
	hFile = FtpOpenFile(hConnect, connection.m_FileName, GENERIC_READ, FTP_TRANSFER_TYPE_BINARY, NULL);
	if (hFile == NULL) {
		CString temp;
		temp.Format("Unable to find file.  FtpOpenFile (%s %s/%s): %s\n",
			connection.m_Host, connection.m_Path, connection.m_FileName, LastError());

		InternetCloseHandle(hConnect);
		InternetCloseHandle(hInternet);
		if (connection.m_TriggerName != "") {
			controlService.Log("Trigger file exists but data file does not.", temp);
			return -1;
		}
		else {
			controlService.Log("", temp);
			return 1;
		}
	}
	
	size = GetFileSize(hFile, NULL);
	if (size == 0) {
		CString temp;
		temp.Format("File size is 0 (%s %s/%s)\n",
			connection.m_Host, connection.m_Path, connection.m_FileName);
		controlService.Log("Unable to find interface file on host.", temp);
		InternetCloseHandle(hFile);
		InternetCloseHandle(hConnect);
		InternetCloseHandle(hInternet);
		return -1;
	}
	
	*msg = (char *)malloc(size*sizeof(char));
	if (*msg == NULL) {
		CString temp;
		temp.Format("Unable to allocate %d bytes (%s %s/%s)\n", size,
			connection.m_Host, connection.m_Path, connection.m_FileName);
		controlService.Log("Error getting file from host.", temp);
		InternetCloseHandle(hFile);
		InternetCloseHandle(hConnect);
		InternetCloseHandle(hInternet);
		return -1;
	}
	
	memset(*msg, 0, size);
	unsigned long bytesRead;
	if (! InternetReadFile(hFile, *msg, size, &bytesRead)) {
		CString temp;
		temp.Format("Error in InternetReadFile (%s %s/%s): %s\n",
			connection.m_Host, connection.m_Path, connection.m_FileName, LastError());
		controlService.Log("Error getting file from host.", temp);
		InternetCloseHandle(hFile);
		InternetCloseHandle(hConnect);
		InternetCloseHandle(hInternet);
		free(*msg);
		size = 0;
		return -1;
	}
	
	if (bytesRead != (unsigned long)size) {
		CString temp;
		temp.Format("File size %d - bytes read %d (%s %s/%s): %s\n", size, bytesRead,
			connection.m_Host, connection.m_Path, connection.m_FileName);
		controlService.Log("Error getting file from host.", temp);
		InternetCloseHandle(hFile);
		InternetCloseHandle(hConnect);
		InternetCloseHandle(hInternet);
		free(*msg);
		size = 0;
		return -1;
	}
	
	InternetCloseHandle(hFile);
	
	if (! FtpDeleteFile(hConnect, connection.m_FileName)) {
		CString temp;
		temp.Format("Error in FtpDeleteFile (%s %s/%s): %s",
			connection.m_Host, connection.m_Path, connection.m_FileName, LastError());
		CString temp2;
		temp2.Format("The remote file was received but could not be "
			"deleted.\nIf the file is not deleted manually, it will be reprocessed the "
			"next time the synchronization runs:\n(%s:%s/%s)",
			connection.m_Host, connection.m_Path, connection.m_FileName);
		controlService.Log(temp2, temp + "\n");
		m_Details += temp;
		m_Details += "\r\n";
		UpdateData(FALSE);
	}

	InternetCloseHandle(hConnect);
	InternetCloseHandle(hInternet);
	
	CString temp;
	temp.Format("Message received via ftp from %s:%s/%s\r\n", connection.m_Host, 
		connection.m_Path, connection.m_FileName);
	m_Details += temp;
	UpdateData(FALSE);

	return 0;
}

int CIntegrationStatusDialog::ReadLocalMessage(CExternalConnection &connection, char **msg, int &size)
{
	CBusyWaitCursor bwc;

	CString fileName;
	fileName.Format("%s%s%s", connection.m_Path, (connection.m_Path == "") ? "" : "\\", connection.m_FileName);

	HANDLE hFile = CreateFile(
		fileName,
		GENERIC_READ,
		FILE_SHARE_READ,
		NULL,
		OPEN_EXISTING,
		FILE_FLAG_SEQUENTIAL_SCAN,
		NULL);

	if (hFile ==  INVALID_HANDLE_VALUE) {
		CString temp;
		temp.Format("File not found.  CreateFile(open) for %s: %s\n",
			fileName, LastError());
		controlService.Log("", temp);
		return 1;
	}
	
	size = GetFileSize(hFile, NULL);
	if (size <= 0) {
		controlService.Log("Error processing file. Invalid size.", "File %s has invalid size.\n", fileName);
		CloseHandle(hFile);
		return -1;
	}

	*msg = (char *)malloc((size+1)*sizeof(char));
	if (*msg == NULL) {
		CString temp, temp2;
		temp.Format("There is not enough memory to open the message files %s.", fileName);
		temp2.Format("Error allocating %d bytes for file %s.\n", size, fileName);
		controlService.Log(temp, temp2);
		CloseHandle(hFile);
		return -1;
	}
	memset(*msg, 0, size+1);

	unsigned long bytesRead;
	ReadFile(hFile, *msg, size, &bytesRead, NULL);
	if ((int)bytesRead != size) {
		CString temp, temp2;
		temp.Format("Error reading file %s.", fileName);
		temp2.Format("%s size %d, read %d\n", fileName, size, bytesRead);
		controlService.Log(temp, temp2);
		CloseHandle(hFile);
		free(*msg);
		size = 0;
		return -1;
	}

	CloseHandle(hFile);

	CString temp;
	temp.Format("Message read on local host from %s\\%s\r\n",
		connection.m_Path, connection.m_FileName);
	m_Details += temp;
	UpdateData(FALSE);

	return 0;
}

int CIntegrationStatusDialog::ReadUserMessage(int interfaceType, char **msg, int &size)
{
	CString fileName, title;
	CString prompt;

	switch (interfaceType) {
	case CWMSGroupConnection::LocationInterface:
		prompt.Format("Do you wish to specify a location inbound file to process?");
		title = "Location Inbound File";
		fileName = "Location.xml";
		break;
	case CWMSGroupConnection::ProductInterface:
		prompt.Format("Do you wish to specify a product inbound file to process?");
		title = "Product Inbound File";
		fileName = "Product.xml";
		break;
	case CWMSGroupConnection::AssignmentInterface:
		prompt.Format("Do you wish to specify an assignment/move inbound file to process?");
		title = "Assignment/Move Inbound File";
		fileName = "Move.xml";
		break;
	case CWMSGroupConnection::LocationConfirmationInterface:
		prompt.Format("Do you wish to specify a location confirmation inbound file to process?");
		title = "Location Confirmation Inbound File";
		fileName = "LocationConfirmation.xml";
		break;
	case CWMSGroupConnection::AssignmentConfirmationInterface:
		prompt.Format("Do you wish to specify an assignment/move confirmation inbound file to process?");
		title = "Assignment/Move Confirmation Inbound File";
		fileName = "AssignmentConfirmation.xml";
		break;
	default:
		return -1;
	}

	int rc = ::MessageBox(this->m_hWnd, prompt, "Select Inbound File", MB_YESNOCANCEL);
	if (rc == IDCANCEL)
		return 2;
	else if (rc == IDNO)
		return 1;

	if (GetFile(title, fileName, TRUE) < 0)
		return 1;

	CExternalConnection tempConn;
	tempConn.m_FileName = fileName;
	tempConn.m_ConnectionType = CExternalConnection::Local;

	return ReadLocalMessage(tempConn, msg, size);

}


int CIntegrationStatusDialog::WriteQueueMessage(CExternalConnection &connection, const CString &msg)
{
	CBusyWaitCursor bwc;

	if (m_pMQHelper == NULL) {
		try {
			m_pMQHelper = new CMessageQueueHelper;
		}
		catch (Ssa_Exception e) {
			controlService.Log("Error in Connect library.", &e);
			m_pMQHelper = NULL;
			return -1;
		}
	}

	int rc;

	if ((rc = m_pMQHelper->OpenQueue(connection.m_Host, connection.m_Port, connection.m_QueueManager,
		connection.m_Queue, connection.m_Channel)) < 0) {
		CString temp;
		temp.Format("Error(%d) opening outbound queue: %s", rc, connection.m_Queue);
		controlService.Log("The outbound Connect queue could not be opened.", temp + "\n");
		m_Details += temp;
		m_Details += "\r\n";
		UpdateData(FALSE);
		m_pMQHelper = NULL;
		return -1;
	}


	rc = m_pMQHelper->PutMessage(msg, msg.GetLength());
	if (rc < 0) {
		CString temp;
		temp.Format("Error(%d) putting message on outbound queue: %s", rc, connection.m_Queue);
		controlService.Log("An error occurred while writing to the outbound queue.", temp + "\n");
		m_Details += temp;
		m_Details += "\r\n";
		UpdateData(FALSE);
	}


	m_pMQHelper->CloseQueue();

	CString temp;
	temp.Format("Message written to queue: %s:%s/%s\r\n",
		connection.m_Host, connection.m_QueueManager, connection.m_Queue);
	m_Details += temp;
	UpdateData(FALSE);


	return 0;

}


int CIntegrationStatusDialog::WriteRemoteMessage(CExternalConnection &connection, const CString& msg)
{
	CBusyWaitCursor bwc;

	HINTERNET hInternet, hConnect, hFile;
	
	hInternet = InternetOpen("Optimize", INTERNET_OPEN_TYPE_DIRECT, NULL, NULL, NULL);
	if (hInternet == NULL) {
		CString temp;
		temp.Format("Error in InternetOpen (%s %s\\%s): %s\n",
			connection.m_Host, connection.m_Path, connection.m_FileName, LastError());
		controlService.Log("Error sending file to host.", temp);
		return -1;
	}
	
	hConnect = InternetConnect(hInternet, connection.m_Host, INTERNET_DEFAULT_FTP_PORT, connection.m_Login,
		connection.m_Password, INTERNET_SERVICE_FTP, NULL, NULL);
	if (hConnect == NULL) {
		CString temp;
		temp.Format("Error in InternetConnect (%s %s\\%s): %s\n",
			connection.m_Host, connection.m_Path, connection.m_FileName, LastError());
		controlService.Log("Error sending file to host.", temp);
		InternetCloseHandle(hInternet);
		return -1;
	}
	
	if (! FtpSetCurrentDirectory(hConnect, connection.m_Path)) {
		CString temp;
		temp.Format("Error in FtpSetCurrentDirectory (%s %s\\%s): %s\n",
			connection.m_Host, connection.m_Path, connection.m_FileName, LastError());
		controlService.Log("Error sending file to host.", temp);
		InternetCloseHandle(hConnect);
		InternetCloseHandle(hInternet);
		return -1;
	}
	
	
	hFile = FtpOpenFile(hConnect, connection.m_FileName, GENERIC_WRITE, FTP_TRANSFER_TYPE_BINARY, NULL);
	if (hFile == NULL) {
		CString temp;
		temp.Format("Error in FtpOpenFile (%s %s/%s): %s\n",
			connection.m_Host, connection.m_Path, connection.m_FileName, LastError());
		controlService.Log("Error sending file to host.", temp);
		InternetCloseHandle(hConnect);
		InternetCloseHandle(hInternet);
		return -1;
	}
	
	unsigned long bytesWritten;
	if (! InternetWriteFile(hFile, msg, msg.GetLength(), &bytesWritten)) {
		CString temp;
		temp.Format("Error in InternetWriteFile (%s %s/%s): %s\n",
			connection.m_Host, connection.m_Path, connection.m_FileName, LastError());
		controlService.Log("Error sending file to host.", temp);
		InternetCloseHandle(hFile);
		InternetCloseHandle(hConnect);
		InternetCloseHandle(hInternet);
		return -1;
	}
	
	if (bytesWritten != (unsigned long)msg.GetLength()) {
		CString temp;
		temp.Format("File size %d - bytes written %d (%s %s/%s): %s\n", msg.GetLength(), bytesWritten,
			connection.m_Host, connection.m_Path, connection.m_FileName);
		controlService.Log("Error sending file to host.", temp);
		InternetCloseHandle(hFile);
		InternetCloseHandle(hConnect);
		InternetCloseHandle(hInternet);
		return -1;
	}
	
	InternetCloseHandle(hFile);

	if (! connection.m_TriggerName.IsEmpty()) {
		hFile = FtpOpenFile(hConnect, connection.m_TriggerName, GENERIC_WRITE, FTP_TRANSFER_TYPE_BINARY, NULL);
		if (hFile == NULL) {
			CString temp;
			temp.Format("Error in FtpOpenFile (%s %s/%s): %s\n",
				connection.m_Host, connection.m_Path, connection.m_TriggerName, LastError());
			controlService.Log("Error sending trigger file to host.", temp);
			InternetCloseHandle(hConnect);
			InternetCloseHandle(hInternet);
			return -1;
		}
		
		unsigned long bytesWritten;
		if (! InternetWriteFile(hFile, "", 0, &bytesWritten)) {
			CString temp;
			temp.Format("Error in InternetWriteFile (%s %s/%s): %s\n",
				connection.m_Host, connection.m_Path, connection.m_TriggerName, LastError());
			controlService.Log("Error sending trigger file to host.", temp);
			InternetCloseHandle(hFile);
			InternetCloseHandle(hConnect);
			InternetCloseHandle(hInternet);
			return -1;
		}
		
		if (bytesWritten != (unsigned long)msg.GetLength()) {
			CString temp;
			temp.Format("File size %d - bytes written %d (%s %s/%s): %s\n", 0, 0,
				connection.m_Host, connection.m_Path, connection.m_TriggerName);
			controlService.Log("Error sending trigger file to host.", temp);
			InternetCloseHandle(hFile);
			InternetCloseHandle(hConnect);
			InternetCloseHandle(hInternet);
			return -1;
		}
	
		InternetCloseHandle(hFile);
	}

	InternetCloseHandle(hConnect);
	InternetCloseHandle(hInternet);

	CString temp;
	temp.Format("Message sent via FTP to %s:%s/%s\r\n", connection.m_Host, 
		connection.m_Path, connection.m_FileName);
	m_Details += temp;
	UpdateData(FALSE);
	
	return 0;
}

int CIntegrationStatusDialog::WriteLocalMessage(CExternalConnection &connection, const CString& msg)
{
	CBusyWaitCursor bwc;
	CProcessingMessage pmsg("Saving data to file", this);

	CString fileName;
	fileName.Format("%s%s%s", connection.m_Path, (connection.m_Path == "") ? "" : "\\", connection.m_FileName);

	HANDLE hFile = CreateFile(
		fileName,
		GENERIC_WRITE,
		FILE_SHARE_WRITE,
		NULL,
		CREATE_ALWAYS,
		NULL,
		NULL);

	if (hFile == NULL) {
		CString temp;
		temp.Format("Error in CreateFile(open) for %s: %s\n",
			connection.m_FileName, LastError());
		controlService.Log("Error opening file.", temp);
		return -1;
	}
	

	unsigned long bytesWritten;
	WriteFile(hFile, msg, msg.GetLength(), &bytesWritten, NULL);
	if ((int)bytesWritten != msg.GetLength()) {
		CString temp, temp2;
		temp.Format("Error writing file %s.", fileName);
		temp2.Format("%s size %d, read %d\n", fileName, msg.GetLength(), bytesWritten);
		controlService.Log(temp, temp2);
		CloseHandle(hFile);
		return -1;
	}

	CloseHandle(hFile);


	CString temp;
	temp.Format("Message written to local host at %s\\%s\r\n", 
		connection.m_Path, connection.m_FileName);
	m_Details += temp;
	UpdateData(FALSE);

	if (! connection.m_TriggerName.IsEmpty()) {
		
		fileName.Format("%s%s%s", connection.m_Path, (connection.m_Path == "") ? "" : "\\", connection.m_TriggerName);
		
		HANDLE hFile = CreateFile(
			fileName,
			GENERIC_WRITE,
			FILE_SHARE_WRITE,
			NULL,
			CREATE_ALWAYS,
			NULL,
			NULL);
		
		if (hFile == NULL) {
			CString temp;
			temp.Format("Error in CreateFile(open) for %s: %s\n",
				connection.m_TriggerName, LastError());
			controlService.Log("Error opening trigger file.", temp);
			return -1;
		}
		
		
		unsigned long bytesWritten;
		WriteFile(hFile, "", 0, &bytesWritten, NULL);
		if ((int)bytesWritten != msg.GetLength()) {
			CString temp, temp2;
			temp.Format("Error writing trigger file %s.", fileName);
			temp2.Format("%s size %d, read %d\n", fileName, 0, 0);
			controlService.Log(temp, temp2);
			CloseHandle(hFile);
			return -1;
		}
		
		CloseHandle(hFile);
	}

	return 0;
}


int CIntegrationStatusDialog::WriteUserMessage(int interfaceType, const CString& msg)
{
	CString fileName, title;

	switch (interfaceType) {
	case CWMSGroupConnection::LocationInterface:
		AfxMessageBox("Please specify the location outbound file.");
		title = "Location Outbound File";
		fileName = "Location.xml";
		break;
	case CWMSGroupConnection::ProductInterface:
		AfxMessageBox("Please specify the product outbound file.");
		title = "Product Outbound File";
		fileName = "Product.xml";
		break;
	case CWMSGroupConnection::AssignmentInterface:
		AfxMessageBox("Please specify the assignment/move file.");
		title = "Assignment/Move Outbound File";
		fileName = "Move.xml";
		break;
	case CWMSGroupConnection::LocationConfirmationInterface:
		AfxMessageBox("Please specify the location confirmation file.");
		title = "Location Confirmation Outbound File";
		fileName = "LocationConfirmation.xml";
		break;
	case CWMSGroupConnection::AssignmentConfirmationInterface:
		AfxMessageBox("Please specify the assignment/move confirmation file.");
		title = "Assignment/Move Confirmation File";
		fileName = "AssignmentConfirmation.xml";
		break;
	default:
		return -1;
	}

	if (GetFile(title, fileName, FALSE) < 0)
		return -1;

	CExternalConnection tempConn;
	tempConn.m_Path = "";
	tempConn.m_FileName = fileName;
	tempConn.m_ConnectionType = CExternalConnection::Local;

	return WriteLocalMessage(tempConn, msg);

}

int CIntegrationStatusDialog::GetFile(const CString &title, CString &fileName, BOOL inbound)
{
	CFileDialog dlgFile(inbound);
	CString strFilter;
	CString strDefault;

	CString allFilter;
	VERIFY(allFilter.LoadString(AFX_IDS_ALLFILTER));
	strFilter += CString("Interface Files (*.dat;*.xml)");
	strFilter += (TCHAR)'\0';   
	strFilter += _T("*.dat;*.xml");
	strFilter += (TCHAR)'\0';
	strFilter += allFilter;
	strFilter += (TCHAR)'\0';   
	strFilter += _T("*.*");
	strFilter += (TCHAR)'\0';   

	dlgFile.m_ofn.lpstrFilter = strFilter;
	dlgFile.m_ofn.lpstrDefExt = "dat";
	dlgFile.m_ofn.lpstrTitle = title;
	dlgFile.m_ofn.lpstrFile = fileName.GetBuffer(_MAX_PATH);
	if (inbound)
		dlgFile.m_ofn.Flags |= OFN_FILEMUSTEXIST;

	BOOL bResult = dlgFile.DoModal() == IDOK ? TRUE : FALSE;

	fileName.ReleaseBuffer();

	if (! bResult)
		return -1;
	
	fileName = dlgFile.GetPathName();

	return 0;

}

CString CIntegrationStatusDialog::LastError()
{
	LPVOID lpMsgBuf;
	FormatMessage( 
		FORMAT_MESSAGE_ALLOCATE_BUFFER | 
		FORMAT_MESSAGE_FROM_SYSTEM | 
		FORMAT_MESSAGE_IGNORE_INSERTS,
		NULL,
		GetLastError(),
		MAKELANGID(LANG_NEUTRAL, SUBLANG_DEFAULT), // Default language
		(LPTSTR) &lpMsgBuf,
		0,
		NULL 
		);
	
	CString msg((char *)lpMsgBuf);

	LocalFree( lpMsgBuf );

	return msg;
	
}

void CIntegrationStatusDialog::OnDetails() 
{
	CEdit *pEdit = (CEdit *)GetDlgItem(IDC_DETAIL_EDIT);
	
	CRect r, r2;
	pEdit->GetWindowRect(&r);
	this->ScreenToClient(&r);
	
	this->GetWindowRect(&r2);

	if (pEdit->IsWindowVisible()) {
		this->SetWindowPos(NULL, 0, 0, r2.Width(), m_NoDetailHeight, SWP_NOMOVE|SWP_NOZORDER);
		pEdit->ShowWindow(SW_HIDE);
	}
	else {
		pEdit->SetWindowPos(NULL, r.left, m_NoDetailHeight-20, r.Width(), r2.Height()*.60, SWP_NOZORDER);
		this->SetWindowPos(NULL, 0, 0, r2.Width(), m_NoDetailHeight + r2.Height()*.60 + 20, SWP_NOMOVE|SWP_NOZORDER);
		pEdit->ShowWindow(SW_SHOW);
	}

}

void CIntegrationStatusDialog::OnCancel() 
{
	if (m_InProgress) {
		if (AfxMessageBox("A synchronization is currently in process.\n"
			"Closing the dialog during synchronization may cause unpredictable errors.\n"
			"Are you sure you wish to close?", MB_YESNO) != IDYES)
			return;
	}

	m_KillEvent.SetEvent();
	
	CDialog::OnCancel();
}

CString CIntegrationStatusDialog::BackupMessage(char *msg, int size, int interfaceType, BOOL error)
{
	CString fileName, type;
	CString dateStr = utilityHelper.GetUTCFileDate();
	
	switch (interfaceType) {
	case CWMSGroupConnection::LocationInterface:
		type = "location";
		break;
	case CWMSGroupConnection::ProductInterface:
		type = "product";
		break;
	case CWMSGroupConnection::AssignmentInterface:
		type = "move";
		break;
	case CWMSGroupConnection::LocationConfirmationInterface:
		type = "location_confirmation";
		break;
	case CWMSGroupConnection::AssignmentConfirmationInterface:
		type = "move_confirmation";
		break;
	}

	if (error)
		fileName.Format("%s\\Interface\\%s_error_%s.dat", controlService.m_ClientHome, type, dateStr);
	else
		fileName.Format("%s\\Interface\\%s_processed_%s.dat", controlService.m_ClientHome, type, dateStr);

	HANDLE hFile = CreateFile(
		fileName,
		GENERIC_WRITE,
		FILE_SHARE_WRITE,
		NULL,
		CREATE_ALWAYS,
		NULL,
		NULL);

	if (hFile == NULL) {
		CString temp;
		temp.Format("Error in CreateFile(open) for %s: %s\n",
			fileName, LastError());
		controlService.Log("Error creating backup file.", temp);
		return "";
	}
	

	unsigned long bytesWritten;
	WriteFile(hFile, msg, size, &bytesWritten, NULL);
	if ((int)bytesWritten != size) {
		CString temp, temp2;
		temp.Format("Error writing backup file %s.", fileName);
		temp2.Format("%s size %d, written %d\n", fileName, size, bytesWritten);
		controlService.Log(temp, temp2);
		CloseHandle(hFile);
		return "";
	}

	CloseHandle(hFile);


	char longFile[2048];
#ifdef _AUTOCAD2000
	if (GetLongPathName(fileName, longFile, 2048) > 2048)
#endif
		return fileName;

	CString longName(longFile);

	return longName;
}


CString CIntegrationStatusDialog::BackupMessage(const CString &originalFile, int interfaceType)
{
	CString fileName, type;
	CString dateStr = utilityHelper.GetUTCFileDate();
	
	switch (interfaceType) {
	case CWMSGroupConnection::LocationInterface:
		type = "location";
		break;
	case CWMSGroupConnection::ProductInterface:
		type = "product";
		break;
	case CWMSGroupConnection::AssignmentInterface:
		type = "move";
		break;
	case CWMSGroupConnection::LocationConfirmationInterface:
		type = "location_confirmation";
		break;
	case CWMSGroupConnection::AssignmentConfirmationInterface:
		type = "move_confirmation";
		break;
	}

	fileName.Format("%s\\Interface\\%s_error_%s.dat", controlService.m_ClientHome, type, dateStr);

	MoveFile(originalFile, fileName);

	char longFile[2048];
#ifdef _AUTOCAD2000
	if (GetLongPathName(fileName, longFile, 2048) > 2048)
#endif
		return fileName;

	CString longName(longFile);

	return longName;
}


int CIntegrationStatusDialog::LoadWMSGroupList()
{
	CStringArray groupList;
	CBusyWaitCursor bwc;

	try {
		integrationDataService.GetIntegratedGroupList(groupList);
	}
	catch (...) {
		controlService.Log("Error getting integrated WMS Facility list.", "Generic exception in GetIntegratedGroupList.");
	}

	for (int i=0; i < groupList.GetSize(); ++i) {
		CWMSGroup *pGroup = new CWMSGroup;
		pGroup->Parse(groupList[i]);
		int nItem = m_GroupListCtrl.AddString(pGroup->m_Name);
		m_GroupListCtrl.SetItemData(nItem, (unsigned long)pGroup);
		m_GroupList.Add(pGroup);
	}

	CRect r;
	m_GroupListCtrl.GetWindowRect(&r);
	m_GroupListCtrl.SetWindowPos(NULL, 0, 0, r.Width(), r.Height()*(m_GroupListCtrl.GetCount()+1), SWP_NOMOVE|SWP_NOZORDER);

	if (m_GroupListCtrl.GetCount() > 0) {
		m_GroupListCtrl.SetCurSel(0);
		OnSelchangeGroupList();
	}

	return 0;
}

void CIntegrationStatusDialog::OnSelchangeGroupList() 
{
	CWMSGroup *pGroup = (CWMSGroup *)m_GroupListCtrl.GetItemData(m_GroupListCtrl.GetCurSel());
	
	if (m_pGroup != NULL && m_pGroup->m_WMSGroupDBId == pGroup->m_WMSGroupDBId)
		return;

	m_pGroup = pGroup;

	if (m_LocationOptions > 0 || m_AssignmentOptions > 0 || m_ProductOptions > 0) {
		if (AfxMessageBox("Would you like to reset the interface options to their defaults?", MB_YESNO) == IDYES)
			m_LocationOptions = m_AssignmentOptions = m_ProductOptions = m_GeneralOptions = 0;
	}

							
	CProcessingMessage dlg("Loading WMS Information", this);
	
	m_InterfaceMessage = "Press Start to begin synchronization or Options to change interface options.";
	m_Message = "";

	UpdateData(FALSE);

	if (LoadGroup() < 0)
		return;
	
	m_OverallProgressCtrl.SetRange32(0, 8);
	m_OverallProgressCtrl.SetStep(1);
	m_OverallProgressCtrl.SetPos(0);

	m_OverallProgressCtrl.ShowWindow(SW_HIDE);
	m_ProgressCtrl.ShowWindow(SW_HIDE);

	utilityHelper.PeekAndPump(0);

}


int CIntegrationStatusDialog::UpdateProductGroupAssignments(int facilityDBId)
{

	CProductGroupDataService service;
	CString temp;
	CStringArray results;
	int rc;
	CWinThread *pThread;
	BOOL bThreadDone;
	
	CStringArray pgList;

	m_InterfaceMessage.Format("Synchronizing %s - Updating Product Groups for New Products", m_pGroup->m_Name);

	try {
		service.GetProductGroups(facilityDBId, pgList);
	}
	catch (...) {
		controlService.Log("", "Generic exception in GetProductGroups.\n");
		return -1;
	}

	if (pgList.GetSize() == 0)
		return 0;

	m_ProgressCtrl.SetRange32(0, pgList.GetSize());
	m_ProgressCtrl.SetPos(0);
	m_ProgressCtrl.SetStep(1);
	
	results.SetSize(5);

	CBusyWaitCursor bwc;

	for (int i=pgList.GetSize()-1; i >= 0; --i) {
	
		CStringArray strings;
		utilityHelper.ParseString(pgList[i], "|", strings);

		int dbid = atoi(strings[0]);
		CString desc = strings[1];
		int priority = atoi(strings[2]);

		m_Message.Format("Updating Product Group: %s", desc);
		UpdateData(FALSE);

		results[0].Format("%d", dbid);
		results[1].Format("%d", priority);
		results[2] = "0";		// count only
		results[3] = "0";		// delete only
		results[4].Format("%d", facilityDBId);
		CThreadParameters parm;
		parm.m_pWnd = this;
		CEvent event;
		parm.m_pEvent = &event;
		parm.m_pInList = &results;

		pThread = AfxBeginThread(CProductGroupAssignmentDialog::AssignProductsThread, &parm);
		
		bThreadDone = FALSE;
		while (TRUE) {
			if ( ! utilityHelper.PeekAndPump(0) )
				break;
			
			bThreadDone = event.Lock(0);
			if (bThreadDone)
				break;
		}

		rc = parm.m_ReturnCode;
		if (rc < 0) {
			ads_printf("%s\n", parm.m_ReturnMessage);
			controlService.Log("", "An error occurred during product group assignment deletion.\n");
			return -1;
		}
		
		m_ProgressCtrl.StepIt();
	}

	return 0;
}

int CIntegrationStatusDialog::GetMoves(CWMS *pWMS, CWMSMap *pMap, CString &chainList, CStringArray &assgList)
{

	chainList = "";

	CGenerateMovesDialog dlg;
	
	dlg.m_FacilityDBId = pMap->m_FacilityDBId;
	dlg.m_FacilityName= pMap->m_FacilityName;
	dlg.m_Standalone = FALSE;
	if ((m_AssignmentOptions & AllowNotIntegrated) != 0)
		dlg.m_IntegratedOnly = FALSE;
	else
		dlg.m_IntegratedOnly = TRUE;

	try {
		dlg.DoModal();
	}
	catch (...) {
		controlService.Log("Error displaying moves dialog.", "Generic exception in CGenerateMovesDialog.\n");
		return -1;
	}

	if (dlg.m_ExportChainList == "")
		return 0;

	try {
		integrationDataService.GetAssignmentDataByChain(pMap->m_FacilityDBId, dlg.m_ExportChainList, assgList);
	}
	catch (...) {
		controlService.Log("Error getting moves.", "Generic exception in GetAssignmentDataByChain.\n");
		return -1;
	}

	chainList = dlg.m_ExportChainList;

	return 0;
}
	
// Chain|Sequence|ProductKey|WMSProductId|WMSProductDetailId|FromLocationKey|ToLocationKey|
// IsFromTemp|IsToTemp|IsAddFacing|IsDeleteFacing|SearchAnchorPoint|Cases|IsFloating|fromSectionId|toSectionId
int CIntegrationStatusDialog::FormatAssignmentOutbound(const CWMS *pWMS, const CWMSMap *pMap,
													   const CString &assignment, int sequence, CString &xml)
{
	CStringArray strings;
	CString name, value, defaultValue;
	int batchId;	// chain id
	CString t, temp;
	
	strings.RemoveAll();
	utilityHelper.ParseString(assignment, "|", strings);
	

	batchId = atoi(strings[0]);
	if (batchId == 0)
		batchId = dataAccessService.GetNextKey("DBInterfaceBatch", 1);


	if (strings[1] != "0")
		t.Format("<ProductLocationAssignment>\n<LineNumber>%s</LineNumber>\n", strings[1]);
	else
		t.Format("<ProductLocationAssignment>\n<LineNumber>%d</LineNumber>\n", sequence);

	xml += t;
	
	xml += utilityHelper.AddXML("WMSID", m_pGroup->m_WMSId);

	
	BOOL fromTemp = FALSE, toTemp = FALSE;
	BOOL addFacing = FALSE, deleteFacing = FALSE;

	int moveType = atoi(strings[7]);

	if (moveType == CMove::moveToTemp)
		toTemp = TRUE;

	if (moveType == CMove::moveFromTemp)
		fromTemp = TRUE;

	if (moveType == CMove::addFacing)
		addFacing = TRUE;

	if (moveType == CMove::deleteFacing)
		deleteFacing = TRUE;
	
	
	if (! addFacing && ! fromTemp && strings[14] != "0") {
		int fromSectionId = atoi(strings[14]);
		CString wmsDetailId = GetWMSForSection(pMap->m_FacilityDBId, fromSectionId);
		xml += utilityHelper.AddXML("FromWMSDetailID", wmsDetailId);
	}

	if (! deleteFacing && ! toTemp && strings[15] != "0") {		// to loc
		int toSectionId = atoi(strings[15]);
		CString wmsDetailId = GetWMSForSection(pMap->m_FacilityDBId, toSectionId);
		xml += utilityHelper.AddXML("ToWMSDetailID", wmsDetailId);
	}

	xml += utilityHelper.AddXML("ProductKey", strings[2]);			
	xml += utilityHelper.AddXML("WMSProductID", strings[3]);
	xml += utilityHelper.AddXML("WMSProductDetailID", strings[4]);


	if (strings[13] == "0") {	// not floating
		if (! fromTemp && ! addFacing && strings[5] != "0")
			xml += utilityHelper.AddXML("FromLocationKey", strings[5]);

		if (! toTemp && ! deleteFacing && strings[6] != "0")
			xml += utilityHelper.AddXML("ToLocationKey", strings[6] == "0" ? "" : strings[6]);

		xml += utilityHelper.AddXML("IsFromTemp", fromTemp ? "Yes" : "No");
		xml += utilityHelper.AddXML("IsToTemp", toTemp ? "Yes" : "No");
		xml += utilityHelper.AddXML("IsAddFacing", addFacing ? "Yes" : "No");
		xml += utilityHelper.AddXML("IsDeleteFacing", deleteFacing ? "Yes" : "No");
	}
	else {		// floating
		xml += utilityHelper.AddXML("FromLocationKey", "");
		xml += utilityHelper.AddXML("ToLocationKey", "");
		xml += utilityHelper.AddXML("IsFromTemp", "No");
		xml += utilityHelper.AddXML("IsToTemp", "No");
		xml += utilityHelper.AddXML("IsAddFacing", "No");
		xml += utilityHelper.AddXML("IsDeleteFacing", "No");
	}

	CString searchAnchorPoint = LookupSearchAnchor(strings[11]);

	xml += utilityHelper.AddXML("SearchAnchorPoint", searchAnchorPoint);
	xml += utilityHelper.AddXML("CaseCapacity", atoi(strings[12]));
	
	xml += "</ProductLocationAssignment>\n";

	return batchId;
}

int CIntegrationStatusDialog::LoadSearchAnchorList(int facilityDBId)
{
	CStringArray sapList;

	m_SearchAnchorList.RemoveAll();

	try {
		integrationDataService.GetSearchAnchorList(facilityDBId, sapList);
	}
	catch (...) {
		controlService.Log("Error loading search anchor points", "Generic exception in GetSearchAnchorList.\n");
		return -1;
	}

	searchAnchorStruct s;
	CStringArray strings;

	for (int i=0; i < sapList.GetSize(); ++i) {
		utilityHelper.ParseString(sapList[i], "|", strings);

		s.low = strings[0];
		s.high = strings[1];
		s.searchAnchorPoint = strings[2];

		m_SearchAnchorList.Add(s);
	}

	return 0;

}

CString CIntegrationStatusDialog::LookupSearchAnchor(const CString &location)
{
	CString low, high;
	searchAnchorStruct s;

	for (int i=0; i < m_SearchAnchorList.GetSize(); ++i) {
		s = m_SearchAnchorList[i];

		if (location > s.high)
			return location;

		if (location < s.low)
			continue;

		return s.searchAnchorPoint;
	}

	return location;
}

CString CIntegrationStatusDialog::GetWMSForSection(int facilityId, int sectionId)
{
	CString wms;
	CString facwms("");

	for (int i=0; i < m_pGroup->m_WMSList.GetSize(); ++i) {
		CWMS *pWMS = m_pGroup->m_WMSList[i];

		for (int j=0; j < pWMS->m_MapList.GetSize(); ++j) {
			CWMSMap *pMap = pWMS->m_MapList[j];

			if (pMap->m_Direction == CWMSGroupConnection::Inbound)
				continue;

			if (pMap->m_FacilityDBId != facilityId)
				continue;
			
			// if the entire facility is mapped to the wms, then we only have to 
			// match the facility if the section is 0, but since a non-zero section
			// overrides, save the value and return it only if we don't find a non-zero section match
			if (pMap->m_SectionDBId == 0)
				facwms = pWMS->m_WMSId;

			if (pMap->m_SectionDBId != sectionId)
				continue;

			return pWMS->m_WMSId;
		}
	}

	return facwms;

}

void CIntegrationStatusDialog::LoadOptions()
{
	CString temp = controlService.GetApplicationData("IntegrationOptions", "Dialogs");
	if (temp == "")
		return;

	CStringArray strings;
	utilityHelper.ParseString(temp, "|", strings);

	if (strings[0] == "1")
		m_GeneralOptions |= SkipXMLLogging;

	if (strings[1] == "1")
		m_GeneralOptions |= DetailXMLLogging;
	
	if (strings[2] == "1")
		m_GeneralOptions |= SaveOptions;

	if (strings[3] == "1")
		m_LocationOptions |= SkipInterface;

	if (strings[4] == "1")
		m_LocationOptions |= InboundPrompt;

	if (strings[5] == "1")
		m_LocationOptions |= OutboundPrompt;

	if (strings[6] == "1")
		m_LocationOptions |= FullExport;

	if (strings[7] == "1")
		m_LocationOptions |= SkipStatusUpdate;

	if (strings[8] == "1")
		m_LocationOptions |= AutoConfirm;

	if (strings[9] == "1")
		m_ProductOptions |= SkipInterface;

	if (strings[10] == "1")
		m_ProductOptions |= InboundPrompt;

	if (strings[11] == "1")
		m_ProductOptions |= SkipProductGroupUpdate;

	if (strings[12] == "1")
		m_AssignmentOptions |= SkipInterface;

	if (strings[13] == "1")
		m_AssignmentOptions |= InboundPrompt;

	if (strings[14] == "1")
		m_AssignmentOptions |= OutboundPrompt;

	if (strings[15] == "1")
		m_AssignmentOptions |= FullExport;
	
	if (strings[16] == "1")
		m_AssignmentOptions |= AutoConfirm;

	if (strings[17] == "1")
		m_AssignmentOptions |= AllowNotIntegrated;

	return;

}

void CIntegrationStatusDialog::OnWmsProperties() 
{
	CWMSGroupProperties dlg;
	dlg.m_Group = *m_pGroup;

	if (dlg.DoModal() != IDOK)
		return;

	if (dlg.m_Group == *m_pGroup)
		return;

	try {
		integrationDataService.StoreWMSGroup(dlg.m_Group);
	}
	catch (...) {
		utilityHelper.ProcessError("Error updating WMS Facility.");
		return;
	}


	*m_pGroup = dlg.m_Group;
		
	m_pGroup->SetConnectionsByType();
}

int CIntegrationStatusDialog::DeleteRemoteFile(CExternalConnection &connection)
{
	CBusyWaitCursor bwc;

	HINTERNET hInternet, hConnect;
	
	hInternet = InternetOpen("Optimize", INTERNET_OPEN_TYPE_DIRECT, NULL, NULL, NULL);
	if (hInternet == NULL) {
		CString temp;
		temp.Format("Error in InternetOpen (%s %s\\%s): %s\n",
			connection.m_Host, connection.m_Path, connection.m_FileName, LastError());
		CString temp2;
		temp2.Format("The remote file could not be "
			"deleted.\nIf the file is not deleted manually, it will be reprocessed the "
			"next time the synchronization runs:\n(%s:%s/%s)",
			connection.m_Host, connection.m_Path, connection.m_FileName);
		controlService.Log("", temp + "\n");
		return -1;
	}
	
	hConnect = InternetConnect(hInternet, connection.m_Host, INTERNET_DEFAULT_FTP_PORT, connection.m_Login,
		connection.m_Password, INTERNET_SERVICE_FTP, NULL, NULL);
	if (hConnect == NULL) {
		CString temp;
		temp.Format("Error in InternetConnect (%s %s\\%s): %s\n",
			connection.m_Host, connection.m_Path, connection.m_FileName, LastError());
		CString temp2;
		temp2.Format("The remote file could not be "
			"deleted.\nIf the file is not deleted manually, it will be reprocessed the "
			"next time the synchronization runs:\n(%s:%s/%s)",
			connection.m_Host, connection.m_Path, connection.m_FileName);
		controlService.Log("", temp + "\n");
		InternetCloseHandle(hInternet);
		return -1;
	}
	
	if (! FtpSetCurrentDirectory(hConnect, connection.m_Path)) {
		CString temp;
		temp.Format("Error in FtpSetCurrentDirectory (%s %s/%s): %s\n",
			connection.m_Host, connection.m_Path, connection.m_FileName, LastError());
		CString temp2;
		temp2.Format("The remote file could not be "
			"deleted.\nIf the file is not deleted manually, it will be reprocessed the "
			"next time the synchronization runs:\n(%s:%s/%s)",
			connection.m_Host, connection.m_Path, connection.m_FileName);
		controlService.Log("", temp + "\n");
		InternetCloseHandle(hConnect);
		InternetCloseHandle(hInternet);
		return -1;
	}
	
	if (! FtpDeleteFile(hConnect, connection.m_FileName)) {
		CString temp;
		temp.Format("Error in FtpDeleteFile (%s %s/%s): %s",
			connection.m_Host, connection.m_Path, connection.m_FileName, LastError());
		CString temp2;
		temp2.Format("The remote file could not be "
			"deleted.\nIf the file is not deleted manually, it will be reprocessed the "
			"next time the synchronization runs:\n(%s:%s/%s)",
			connection.m_Host, connection.m_Path, connection.m_FileName);
		controlService.Log("", temp + "\n");
		m_Details += temp;
		m_Details += "\r\n";
		UpdateData(FALSE);
	}

	if (connection.m_TriggerName != "") {
		if (! FtpDeleteFile(hConnect, connection.m_TriggerName)) {
			CString temp;
			temp.Format("Error in FtpDeleteFile (%s %s/%s): %s",
				connection.m_Host, connection.m_Path, connection.m_TriggerName, LastError());
			CString temp2;
			temp2.Format("The remote trigger file could not be "
				"deleted.\nIf the file is not deleted manually, it will be reprocessed the "
				"next time the synchronization runs:\n(%s:%s/%s)",
				connection.m_Host, connection.m_Path, connection.m_FileName);
			controlService.Log("", temp + "\n");
			m_Details += temp;
			m_Details += "\r\n";
			UpdateData(FALSE);
		}
	}

	InternetCloseHandle(hConnect);
	InternetCloseHandle(hInternet);
	
	return 0;
}

void CIntegrationStatusDialog::DeleteInboundMessage(CExternalConnection &connection)
{
	if (connection.m_ConnectionType == CExternalConnection::MQSeries)
		CommitQueueTransaction(connection);
	else if (connection.m_ConnectionType == CExternalConnection::Local) {
		CString fileName(connection.m_Path);
		fileName += "\\";
		fileName += connection.m_FileName;
		if (! DeleteFile(fileName)) {
			CString temp;
			temp.Format("Error in DeleteFile (%s): %s",
				fileName, LastError());
			CString temp2;
			temp2.Format("The local file could not be "
				"deleted.\nIf the file is not deleted manually,\nit will be reprocessed the "
				"next time the synchronization runs:\n(%s)", fileName);
			controlService.Log(temp2, temp + "\n");
			m_Details += temp;
			m_Details += "\r\n";
			UpdateData(FALSE);
		}

		fileName = connection.m_Path;
		fileName += "\\";
		fileName += connection.m_TriggerName;
		if (! DeleteFile(fileName)) {
			CString temp;
			temp.Format("Error in DeleteFile (%s): %s",
				fileName, LastError());
			CString temp2;
			temp2.Format("The local trigger file could not be "
				"deleted.\nIf the file is not deleted manually,\nit will be reprocessed the "
				"next time the synchronization runs:\n(%s)", fileName);
			controlService.Log(temp2, temp + "\n");
			m_Details += temp;
			m_Details += "\r\n";
			UpdateData(FALSE);
		}

	}
	else if (connection.m_ConnectionType == CExternalConnection::FTP) {
		if (! DeleteRemoteFile(connection)) {
			CString temp;
			temp.Format("Error in DeleteRemoteFile (%s:%s/%s): %s",
				connection.m_Host, connection.m_Path, connection.m_FileName, LastError());
			CString temp2;
			temp2.Format("The remote file could not be "
				"deleted.\nIf the file is not deleted manually,\nit will be reprocessed the "
				"next time the synchronization runs:\n(%s:%s/%s)", 
				connection.m_Host, connection.m_Path, connection.m_FileName);
			controlService.Log(temp2, temp + "\n");
			m_Details += temp;
			m_Details += "\r\n";
			UpdateData(FALSE);
		}
	}
}


int CIntegrationStatusDialog::ProcessLocationInbound(CStringArray &locList, int &errorCount, int eventId, int addCount)
{
	CThreadParameters parms;
	parms.m_pInList = &locList;
	parms.m_ReturnCode = addCount;
	CEvent event;
	parms.m_pEvent = &event;
	
	event.ResetEvent();
	m_KillEvent.ResetEvent();

	parms.m_EventId = eventId;

	CWinThread *pThread = AfxBeginThread(CIntegrationDataService::ProcessLocationInboundThread, &parms);

	int eventCount = 0;

	BOOL bThreadDone = false;
	while (TRUE) {
		CBusyWaitCursor bwc;
		
		if (eventCount++ >= 2) {
			int pos = utilityHelper.GetNextEventPosition(eventId);
			if (pos >= 0)
				m_ProgressCtrl.SetPos(pos);
			eventCount = 0;
		}

		if ( ! utilityHelper.PeekAndPump() )
			break;
		
		bThreadDone = parms.m_pEvent->Lock(0);
		if (bThreadDone)
			break;

		bThreadDone = m_KillEvent.Lock(0);
		if (bThreadDone)
			return -1;
	}

	errorCount = atoi(parms.m_ReturnMessage);

	return parms.m_ReturnCode;
}

int CIntegrationStatusDialog::ProcessProductInbound(CStringArray &prodList, int &errorCount, 
													int eventId, int addCount)
{
	CThreadParameters parms;

	parms.m_pInList = &prodList;
	parms.m_ReturnCode = addCount;
	CEvent event;
	parms.m_pEvent = &event;
	parms.m_EventId = eventId;
	
	event.ResetEvent();
	m_KillEvent.ResetEvent();


	CWinThread *pThread = AfxBeginThread(CIntegrationDataService::ProcessProductInboundThread, &parms);
	
	int eventCount = 0;

	BOOL bThreadDone = false;
	while (TRUE) {
		CBusyWaitCursor bwc;

		
		if (eventCount++ >= 2) {
			int pos = utilityHelper.GetNextEventPosition(eventId);
			if (pos >= 0)
				m_ProgressCtrl.SetPos(pos);
			eventCount = 0;
			
			/*
			int lower, upper;
			m_ProgressCtrl.GetRange(lower, upper);
			ads_printf("%d of %d\n", pos, upper);
			*/
			
		}
		

		if ( ! utilityHelper.PeekAndPump() )
			break;
		
		bThreadDone = parms.m_pEvent->Lock(0);
		if (bThreadDone)
			break;

		bThreadDone = m_KillEvent.Lock(0);
		if (bThreadDone)
			return -1;
	}

	errorCount = atoi(parms.m_ReturnMessage);

	return parms.m_ReturnCode;
}



int CIntegrationStatusDialog::ProcessAssignmentInbound(CStringArray &assgList, int &errorCount,
													  int eventId,  int addCount)
{
	CThreadParameters parms;
	parms.m_pInList = &assgList;
	parms.m_ReturnCode = addCount;
	CEvent event;
	parms.m_pEvent = &event;
	parms.m_EventId = eventId;

	event.ResetEvent();
	m_KillEvent.ResetEvent();

	CWinThread *pThread = AfxBeginThread(CIntegrationDataService::ProcessAssignmentInboundThread, &parms);

	int eventCount = 0;

	BOOL bThreadDone = false;
	while (TRUE) {
		CBusyWaitCursor bwc;

		if (eventCount++ >= 2) {
			int pos = utilityHelper.GetNextEventPosition(eventId);
			if (pos >= 0)
				m_ProgressCtrl.SetPos(pos);
			eventCount = 0;
		}

		if ( ! utilityHelper.PeekAndPump() )
			break;
		
		bThreadDone = parms.m_pEvent->Lock(0);
		if (bThreadDone)
			break;

		bThreadDone = m_KillEvent.Lock(0);
		if (bThreadDone)
			return -1;
	}

	errorCount = atoi(parms.m_ReturnMessage);

	return parms.m_ReturnCode;
}

BOOL CIntegrationStatusDialog::OnHelpInfo(HELPINFO* pHelpInfo) 
{
	helpService.ShowFieldHelp(IDD, pHelpInfo->iCtrlId);

	return FALSE;

}

void CIntegrationStatusDialog::OnHelp() 
{
	helpService.ShowScreenHelp(IDD);

	return;

}


void CIntegrationStatusDialog::OnViewErrors()
{

	CDisplayResults dlg;
	CStringArray results;
	CString header;
	CString tempString;
	CString sql;
	int rc;

	dlg.m_Tabs.Add("Products");
	dlg.m_Tabs.Add("Locations");

	dlg.m_WindowCaptions.Add("Inbound Errors - Products");
	dlg.m_WindowCaptions.Add("Inbound Errors - Locations");

	header = "Batch Id|Line Number|Action|Status|Closed|Reason Code|Reason|";
	dlg.m_Headers.Add(header);
	header = "Batch Id|Line Number|Action|Status|Closed|Reason Code|Reason|";
	dlg.m_Headers.Add(header);

	dlg.m_HeaderKeys.Add("L");		// Location
	dlg.m_HeaderKeys.Add("P");		// Product

	dlg.m_OrigColumnSize = 100;

	sql = "select BATCHID,LINENUMBER,ACTION,STATUS,CLOSED,REASONCODE,REASONTEXT from dbprodqueue";

	dataAccessService.ExecuteQuery("none",sql, results);

	for (int i = 0; i < results.GetSize(); i++) {
		tempString = "L|" + results[i];
		results[i] = tempString;
	}

	dlg.m_Data = results;

	try {
		rc = dlg.DoModal();
	}
	catch (...) {
		AfxMessageBox("Error displaying errors.");
	}
}

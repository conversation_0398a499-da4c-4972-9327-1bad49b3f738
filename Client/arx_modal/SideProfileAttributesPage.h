#if !defined(AFX_SIDEPROFILEATTRIBUTESPAGE_H__16E3FF9E_E362_4A59_8CC8_A71C3D8FBD13__INCLUDED_)
#define AFX_SIDEPROFILEATTRIBUTESPAGE_H__16E3FF9E_E362_4A59_8CC8_A71C3D8FBD13__INCLUDED_

#include "SideProfile.h"

#if _MSC_VER > 1000
#pragma once
#endif // _MSC_VER > 1000
// SideProfileAttributesPage.h : header file
//

/////////////////////////////////////////////////////////////////////////////
// CSideProfileAttributesPage dialog

class CSideProfileAttributesPage : public CPropertyPage
{
	DECLARE_DYNCREATE(CSideProfileAttributesPage)

// Construction
public:
	BOOL m_IsFixed;
	CSideProfileAttributesPage();
	~CSideProfileAttributesPage();

// Dialog Data
	//{{AFX_DATA(CSideProfileAttributesPage)
	enum { IDD = IDD_SIDE_PROFILE_ATTRIBUTES };
	CString	m_Length;
	CString	m_Name;
	//}}AFX_DATA


// Overrides
	// ClassWizard generate virtual function overrides
	//{{AFX_VIRTUAL(CSideProfileAttributesPage)
	public:
	virtual BOOL OnSetActive();
	virtual BOOL OnKillActive();
	protected:
	virtual void DoDataExchange(CDataExchange* pDX);    // DDX/DDV support
	virtual BOOL OnNotify(WPARAM wParam, LPARAM lParam, LRESULT* pResult);
	//}}AFX_VIRTUAL

// Implementation
protected:
	// Generated message map functions
	//{{AFX_MSG(CSideProfileAttributesPage)
	afx_msg void OnFixedRadio();
	afx_msg void OnVariableRadio();
	virtual BOOL OnInitDialog();
	afx_msg BOOL OnHelpInfo(HELPINFO* pHelpInfo);
	//}}AFX_MSG
	DECLARE_MESSAGE_MAP()

private:
	CSideProfile *m_pSideProfile;

};

//{{AFX_INSERT_LOCATION}}
// Microsoft Visual C++ will insert additional declarations immediately before the previous line.

#endif // !defined(AFX_SIDEPROFILEATTRIBUTESPAGE_H__16E3FF9E_E362_4A59_8CC8_A71C3D8FBD13__INCLUDED_)

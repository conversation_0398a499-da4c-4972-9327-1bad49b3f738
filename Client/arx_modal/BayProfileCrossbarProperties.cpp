// B/ayProfileCrossbarProperties.cpp : implementation file
//

#include "stdafx.h"
#include "modal.h"
#include "BayProfileCrossbarProperties.h"
#include "HelpService.h"

#ifdef _DEBUG
#define new DEBUG_NEW
#undef THIS_FILE
static char THIS_FILE[] = __FILE__;
#endif

extern CHelpService helpService;
/////////////////////////////////////////////////////////////////////////////
// CBayProfileCrossbarProperties dialog


CBayProfileCrossbarProperties::CBayProfileCrossbarProperties(CWnd* pParent /*=NULL*/)
	: CDialog(CBayProfileCrossbarProperties::IDD, pParent)
{
	//{{AFX_DATA_INIT(CBayProfileCrossbarProperties)
	m_Hidden = FALSE;
	m_Clearance = 0.0;
	m_Position = 0.0;
	m_Overhang = 0.0;
	m_Thickness = 0.0;
	m_WeightCapacity = 0.0;
	//}}AFX_DATA_INIT
}


void CBayProfileCrossbarProperties::DoDataExchange(CDataExchange* pDX)
{
	CDialog::DoDataExchange(pDX);
	//{{AFX_DATA_MAP(CBayProfileCrossbarProperties)
	DDX_Check(pDX, IDC_HIDDEN_CHECKBOX, m_Hidden);
	DDX_Text(pDX, IDC_CLEARANCE, m_Clearance);
	DDV_MinMaxDouble(pDX, m_Clearance, 0., 99999999.);
	DDX_Text(pDX, IDC_CROSSBAR_HEIGHT, m_Position);
	DDV_MinMaxDouble(pDX, m_Position, 0., 999999999.);
	DDX_Text(pDX, IDC_OVERHANG, m_Overhang);
	DDV_MinMaxDouble(pDX, m_Overhang, 0., 999999999.);
	DDX_Text(pDX, IDC_THICKNESS, m_Thickness);
	DDV_MinMaxDouble(pDX, m_Thickness, 0., 999999999.);
	DDX_Text(pDX, IDC_WEIGHT_CAPACITY, m_WeightCapacity);
	DDV_MinMaxDouble(pDX, m_WeightCapacity, 0., 999999999.);
	//}}AFX_DATA_MAP
}


BEGIN_MESSAGE_MAP(CBayProfileCrossbarProperties, CDialog)
	//{{AFX_MSG_MAP(CBayProfileCrossbarProperties)
	ON_BN_CLICKED(IDHELP, OnHelp)
	ON_WM_HELPINFO()
	//}}AFX_MSG_MAP
END_MESSAGE_MAP()

/////////////////////////////////////////////////////////////////////////////
// CBayProfileCrossbarProperties message handlers

BOOL CBayProfileCrossbarProperties::OnInitDialog() 
{
	CDialog::OnInitDialog();
	


	if (m_pBayProfile->m_Active) {
		GetDlgItem(IDC_CROSSBAR_HEIGHT)->EnableWindow(FALSE);
		GetDlgItem(IDC_THICKNESS)->EnableWindow(FALSE);
		GetDlgItem(IDC_HIDDEN_CHECKBOX)->EnableWindow(FALSE);
		GetDlgItem(IDC_OVERHANG)->EnableWindow(FALSE);
		GetDlgItem(IDC_CLEARANCE)->EnableWindow(FALSE);
	}
	else {
		GetDlgItem(IDC_CROSSBAR_HEIGHT)->EnableWindow(TRUE);
		GetDlgItem(IDC_THICKNESS)->EnableWindow(TRUE);
		GetDlgItem(IDC_HIDDEN_CHECKBOX)->EnableWindow(TRUE);
		GetDlgItem(IDC_OVERHANG)->EnableWindow(TRUE);
		GetDlgItem(IDC_CLEARANCE)->EnableWindow(TRUE);
	}

	if (m_CurrentLevel == 0) {
		GetDlgItem(IDC_CROSSBAR_HEIGHT)->EnableWindow(FALSE);
		GetDlgItem(IDC_THICKNESS)->EnableWindow(FALSE);
		GetDlgItem(IDC_HIDDEN_CHECKBOX)->EnableWindow(FALSE);
	}

	return TRUE;  // return TRUE unless you set the focus to a control
	              // EXCEPTION: OCX Property Pages should return FALSE
}

void CBayProfileCrossbarProperties::OnOK() 
{

	UpdateData(TRUE);

	if (m_Position <= 0 && m_CurrentLevel != 0) {
		AfxMessageBox("The position of the crossbar must be above the floor.");
		return;
	}

	if (m_Position > m_pBayProfile->m_UprightHeight) {
		CString temp;
		temp.Format("The crossbar position must be at or below the upright height (%.0f)",
			m_pBayProfile->m_UprightHeight);
		AfxMessageBox(temp);
		return;
	}

	if (m_Thickness <= 0 && m_CurrentLevel != 0) {
		AfxMessageBox("The thickness of the crossbar must be greater than 0.");
		return;
	}
	
	int currentLevel = m_CurrentLevel;
	int adding = 0;
	double totalWeightCapacity = 0;

	if (currentLevel < 0)
		adding = 1;

	if (m_Hidden && m_CurrentLevel == 0) {
		AfxMessageBox("The floor cannot be hidden.");
		return;
	}

	if (! adding) {
		CLevelProfile *pLevelProfile = m_pBayProfile->m_LevelProfileList[m_CurrentLevel];
		if (m_Hidden && pLevelProfile->m_LocationProfileList.GetSize() > 0) {
			AfxMessageBox("Locations cannot be defined above a hidden crossbar.\n"
				"Remove the locations defined for this level before setting it to hidden.");
			return;
		}
	}
	


	for (int i=0; i < m_pBayProfile->m_LevelProfileList.GetSize(); ++i) {
		// If they are editing a level we don't want to check against that level
		if (i == currentLevel)
			continue;
		
		if (m_pBayProfile->m_LevelProfileList[i]->m_Coordinates.m_Z == m_Position) {
			AfxMessageBox("A crossbar already exists at the specified height.");
			return;
		}
		//	if (m_pBayProfile->m_LevelProfileList[i]->m_Coordinates.m_Z > m_Position) {
		//		break;
		//	}
		
		totalWeightCapacity += m_pBayProfile->m_LevelProfileList[i]->m_WeightCapacity;
		
		if (adding == 1)
			currentLevel = i;
		
		
		
		CLevelProfile *pBelow, *pAbove;
		double aboveHeight, belowHeight;
		double belowClearance, aboveThickness;
		
		if (currentLevel < m_pBayProfile->m_LevelProfileList.GetSize()-1) {
			pAbove = m_pBayProfile->m_LevelProfileList[currentLevel+1];
			aboveHeight = pAbove->m_Coordinates.m_Z;
			aboveThickness = pAbove->m_Thickness;

			if (currentLevel == i) {
				if (m_Position > pAbove->m_Coordinates.m_Z)
					continue;
			}
			
			if (m_Position + m_Clearance >= (aboveHeight-aboveThickness)) {
				CString temp;
				temp.Format("A crossbar already exists at %.0f with a thickness of %.0f.\n"
					"The top of the current crossbar (%.0f) including the clearance\n"
					"must not overlap another crossbar.",
					aboveHeight, aboveThickness, (m_Position+m_Clearance));
				AfxMessageBox(temp);
				return;
				
			}
		}
		
		
		if (currentLevel != 0) {
			pBelow = m_pBayProfile->m_LevelProfileList[currentLevel-1];
			belowHeight = pBelow->m_Coordinates.m_Z;
			belowClearance = pBelow->m_Clearance;

			if (currentLevel == i) {
				if (m_Position < pBelow->m_Coordinates.m_Z)
					continue;
			}
			
			if (m_Position - m_Thickness <= (belowHeight+belowClearance)) {
				CString temp;
				temp.Format("A crossbar already exists at %.0f with a clearance of %.0f.\n"
					"The bottom of the current crossbar (%.0f) must not overlap\n"
					"another crossbar.",
					belowHeight, belowClearance, (m_Position-m_Thickness));
				AfxMessageBox(temp);
				return;
			}
		}
		
	}


	if (m_pBayProfile->m_WeightCapacity < (m_WeightCapacity + totalWeightCapacity)) {
		CString temp;
		temp.Format("Because the sum of the weight capacities of the levels "
			"must be less than or equal to the \n"
			"weight capacity of the bay(%.0f), the maximum weight capacity for this level is %.0f.", 
			m_pBayProfile->m_WeightCapacity, m_pBayProfile->m_WeightCapacity-totalWeightCapacity);
		AfxMessageBox(temp);
		CEdit *pEdit = (CEdit *)GetDlgItem(IDC_WEIGHT_CAPACITY);
		pEdit->SetSel(0,-1);
		pEdit->SetFocus();
		return;
	}

	CDialog::OnOK();
}

void CBayProfileCrossbarProperties::OnCancel() 
{
	// TODO: Add extra cleanup here
	
	CDialog::OnCancel();
}

BOOL CBayProfileCrossbarProperties::OnHelpInfo(HELPINFO* pHelpInfo) 
{
	helpService.ShowFieldHelp(IDD, pHelpInfo->iCtrlId);

	return FALSE;

}

void CBayProfileCrossbarProperties::OnHelp() 
{
	helpService.ShowScreenHelp(IDD);

	return;

}

void CBayProfileCrossbarProperties::UpdateCrossbarRanges()
{
	int curSel = 0;

	int lower, upper;

	GetPositionRange(curSel, lower, upper);
	GetThicknessRange(curSel, lower, upper);
	GetClearanceRange(curSel, lower, upper);
}

void CBayProfileCrossbarProperties::GetPositionRange(int currentLevel, int& lower, int& upper)
{

	if (currentLevel == 0) {
		lower = 0;
		upper = 0;
	}
	else {
		// the mininum height is one above the height of the crossbar below minus the clearance
		CLevelProfile *pCurrentLevel = m_pBayProfile->m_LevelProfileList[currentLevel];
		CLevelProfile  *pLevelBelow = m_pBayProfile->m_LevelProfileList[currentLevel-1];
		lower = (int)((pLevelBelow->m_Coordinates.m_Z + 1) + pCurrentLevel->m_Clearance);

		// the maximum height is one below the height of the crossbar above minus its thickness and this clearance
		// or if the top level is selected, the height of the upright minus the clearance
		
		if (currentLevel == m_pBayProfile->m_LevelProfileList.GetSize()-1) {
			upper = (int)(m_pBayProfile->m_UprightHeight - pCurrentLevel->m_Clearance - 1);
		}
		else {
			CLevelProfile *pLevelAbove;
			pLevelAbove = m_pBayProfile->m_LevelProfileList[currentLevel+1];
			upper = (int)(pLevelAbove->m_Coordinates.m_Z - pLevelAbove->m_Thickness - pCurrentLevel->m_Clearance - 1);
		}
	}

}

void CBayProfileCrossbarProperties::GetThicknessRange(int currentLevel, int& lower, int& upper)
{
	if (currentLevel == 0) {
		lower = 0;
		upper = 0;
	}
	else {
		// the lowest thickness is always 1
		lower = 1;
	
		// the upper thickness is the height of the current level minus the clearance
		// minus the height of the level below
		CLevelProfile *pLevelBelow = m_pBayProfile->m_LevelProfileList[currentLevel-1];
		CLevelProfile *pCurrentLevel = m_pBayProfile->m_LevelProfileList[currentLevel];

		upper = (int)(pCurrentLevel->m_Coordinates.m_Z - pCurrentLevel->m_Clearance - pLevelBelow->m_Coordinates.m_Z);
	}

}

void CBayProfileCrossbarProperties::GetClearanceRange(int currentLevel, int& lower, int& upper)
{

	// the minimum clearance value is always 0
	lower = 0;
	
	CLevelProfile *pCurrentLevel = m_pBayProfile->m_LevelProfileList[currentLevel];

	if (currentLevel == m_pBayProfile->m_LevelProfileList.GetSize()-1) {
		// for the highest crossbar, the maximum clearance is the space
		// between the crossbar and the top of the bay
		upper = (int)(m_pBayProfile->m_Height - pCurrentLevel->m_Coordinates.m_Z - 1);
	}
	else {
		// the maximum clearance is one above the height of the crossbar above minus the thickness
		// minus the height of the current crossbar
		CLevelProfile  *pLevelAbove = m_pBayProfile->m_LevelProfileList[currentLevel+1];
		upper = (int)((pLevelAbove->m_Coordinates.m_Z - pCurrentLevel->m_Thickness) - 
			pCurrentLevel->m_Coordinates.m_Z - 1);
	}
			
}




// AisleProfileListDialog.cpp : implementation file
//

#include "stdafx.h"
#include "modal.h"
#include "AisleProfileListDialog.h"
#include "AisleProfileSheet.h"
#include "AisleProfileDataService.h"
#include "SideProfileDataService.h"
#include "ControlService.h"
#include "HelpService.h"

#include "FacilityHelper.h"
#include "UtilityHelper.h"
#include "WizardHelper.h"
#include "Constants.h"

#include "ResourceHelper.h"

#ifdef _DEBUG
#define new DEBUG_NEW
#undef THIS_FILE
static char THIS_FILE[] = __FILE__;
#endif

extern CControlService controlService;
extern CAisleProfileDataService aisleProfileDataService;
extern CSideProfileDataService sideProfileDataService;
extern CUtilityHelper utilityHelper;
extern CFacilityHelper facilityHelper;
extern CHelpService helpService;
/////////////////////////////////////////////////////////////////////////////
// CAisleProfileListDialog dialog


CAisleProfileListDialog::CAisleProfileListDialog(CWnd* pParent /*=NULL*/)
	: CDialog(CAisleProfileListDialog::IDD, pParent)
{
	//{{AFX_DATA_INIT(CAisleProfileListDialog)
	//}}AFX_DATA_INIT
	m_pAisleProfile = NULL;
	m_IsModeless = FALSE;
}


void CAisleProfileListDialog::DoDataExchange(CDataExchange* pDX)
{
	CDialog::DoDataExchange(pDX);
	//{{AFX_DATA_MAP(CAisleProfileListDialog)
	DDX_Control(pDX, IDC_AISLE_PROFILE_TREE, m_ProfileTreeCtrl);
	//}}AFX_DATA_MAP
}


BEGIN_MESSAGE_MAP(CAisleProfileListDialog, CDialog)
	//{{AFX_MSG_MAP(CAisleProfileListDialog)
	ON_NOTIFY(TVN_ENDLABELEDIT, IDC_AISLE_PROFILE_TREE, OnEndlabeleditAisleProfileTree)
	ON_BN_CLICKED(IDC_NEW, OnNew)
	ON_BN_CLICKED(IDC_EDIT, OnEdit)
	ON_BN_CLICKED(IDC_COPY, OnCopy)
	ON_BN_CLICKED(IDC_DELETE, OnDelete)
	ON_WM_CONTEXTMENU()
	ON_BN_CLICKED(IDC_VIEW_DRAWING, OnViewDrawing)
	ON_NOTIFY(NM_DBLCLK, IDC_AISLE_PROFILE_TREE, OnDblclkAisleProfileTree)
	ON_BN_CLICKED(IDHELP, OnHelp)
	ON_BN_CLICKED(IDC_BAY_WIZARD, OnBayWizard)
	ON_BN_CLICKED(IDC_SIDE_WIZARD, OnSideWizard)
	ON_BN_CLICKED(IDC_MAIN_WIZARD, OnMainWizard)
	ON_WM_HELPINFO()
	//}}AFX_MSG_MAP
END_MESSAGE_MAP()

/////////////////////////////////////////////////////////////////////////////
// CAisleProfileListDialog message handlers
BOOL CAisleProfileListDialog::OnInitDialog() 
{
	CDialog::OnInitDialog();
	
	if (m_pAisleProfile == NULL) {
		m_ImageList.Create(16, 16, TRUE, 4, 1);
		m_ImageList.Add(AfxGetApp()->LoadIcon(IDI_FLDOPEN));
		m_ImageList.Add(AfxGetApp()->LoadIcon(IDI_FLDCLS));
		m_ImageList.Add(AfxGetApp()->LoadIcon(IDI_LOCATIONICON));
		
		LoadAisleProfileList();

		m_ProfileTreeCtrl.SetImageList(&m_ImageList, TVSIL_NORMAL);

		BuildAisleProfileTree();
	}	
	else {
		m_ProfileTreeCtrl.SetImageList(&m_ImageList, TVSIL_NORMAL);

		BuildAisleProfileTree();

		HTREEITEM hItem = m_ProfileTreeCtrl.GetChildItem(m_ProfileTreeCtrl.GetChildItem(TVI_ROOT));
		while (hItem != NULL) {
			
			CAisleProfile *pTempProfile = (CAisleProfile *)m_ProfileTreeCtrl.GetItemData(hItem);
			if (m_pAisleProfile->m_AisleProfileDBId == pTempProfile->m_AisleProfileDBId) {
				m_ProfileTreeCtrl.SelectItem(hItem);
				break;
			}

			HTREEITEM hNextItem = m_ProfileTreeCtrl.GetNextItem(hItem, TVGN_NEXT);
			hItem = hNextItem;
		}
	}

	return TRUE;  // return TRUE unless you set the focus to a control
	              // EXCEPTION: OCX Property Pages should return FALSE
}

CAisleProfileListDialog::~CAisleProfileListDialog()
{
	for (int i=0; i < m_AisleProfileList.GetSize(); ++i)
		delete m_AisleProfileList[i];
}

int CAisleProfileListDialog::LoadAisleProfileList()
{
	CStringArray aisleNameList, strings, usedList;
	CMap<int, int, int, int> usedMap;

	CWaitCursor cwc;

	if (m_AisleProfileList.GetSize() > 0)
		return 0;

	try {
		aisleProfileDataService.GetAisleProfileList(aisleNameList);
	}
	catch (...) {
		utilityHelper.ProcessError("Error getting list of aisle profiles.");
		return -1;
	}

	for (int i=0; i < aisleNameList.GetSize(); ++i) {
		CAisleProfile *pAisleProfile = new CAisleProfile();
		pAisleProfile->Parse(aisleNameList[i]);
		m_AisleProfileList.Add(pAisleProfile);
	}

	return 0;

}


void CAisleProfileListDialog::OnNew() 
{
	CTemporaryResourceOverride tro;

	CAisleProfileSheet sheet("New Aisle Profile", this);

	CAisleProfile *pAisleProfile = new CAisleProfile;
	
	pAisleProfile->m_AisleProfileDBId = 0;
	pAisleProfile->m_Description = "New";

	CWaitCursor cwc;

	CWizardHelper wizardHelper;
	if (wizardHelper.ShowAisleWizard(pAisleProfile) < 0) {
		delete pAisleProfile;
		return;
	}

	m_AisleProfileList.Add(pAisleProfile);
	HTREEITEM hItem = m_ProfileTreeCtrl.InsertItem(pAisleProfile->m_Description, 2, 2, TVI_ROOT, TVI_LAST);
	m_ProfileTreeCtrl.SetItemData(hItem, (unsigned long)pAisleProfile);
	m_ProfileTreeCtrl.SortChildren(TVI_ROOT);
	m_ProfileTreeCtrl.SelectItem(hItem);

}

void CAisleProfileListDialog::OnEdit() 
{
	// Hack because tree control doesn't handle return or escape keys properly
	// Since this is the default button, it will get the return message when
	// they finish editing a label
	if (IsTreeCtrlEditMessage(VK_RETURN))
		return;

	CAisleProfile *pAisleProfile;
	
	HTREEITEM hItem = m_ProfileTreeCtrl.GetSelectedItem();
	if (hItem == NULL) {
		AfxMessageBox("Please select the aisle profile you wish to edit.");
		return;
	}

	pAisleProfile = (CAisleProfile *)m_ProfileTreeCtrl.GetItemData(hItem);
	CString oldDesc = pAisleProfile->m_Description;

	CWizardHelper wizardHelper;
	if (wizardHelper.ShowAisleWizard(pAisleProfile) >= 0) {
		if (*pAisleProfile->m_Description != oldDesc) {
			m_ProfileTreeCtrl.SetItemText(hItem, pAisleProfile->m_Description);
			m_ProfileTreeCtrl.SortChildren(TVI_ROOT);
		}	
	}
	
	return;
}

void CAisleProfileListDialog::OnCopy() 
{
	CAisleProfile *pAisleProfile;
	
	HTREEITEM hCopyItem = m_ProfileTreeCtrl.GetSelectedItem();
	if (hCopyItem == NULL) {
		AfxMessageBox("Please select the aisle profile you wish to copy.");
		return;
	}
	pAisleProfile = (CAisleProfile *)m_ProfileTreeCtrl.GetItemData(hCopyItem);

	CWaitCursor cwc;
	if (pAisleProfile->m_pLeftSideProfile == NULL && pAisleProfile->m_pRightSideProfile == NULL) {
		try {
			aisleProfileDataService.GetAisleProfile(pAisleProfile->m_AisleProfileDBId, *pAisleProfile);
		}
		catch (...) {
			utilityHelper.ProcessError("Error loading aisle profile.");
			return;
		}
	}

	CAisleProfile *pNewProfile = new CAisleProfile;
	*pNewProfile = *pAisleProfile;

	int idx = 1;
	CString temp;
	temp.Format("Copy of %s", pAisleProfile->m_Description);

	for (int i = 0; i < m_AisleProfileList.GetSize(); ++i) {	
		if (m_AisleProfileList[i]->m_Description.Find(temp) >= 0)
			idx++;
	}
	
	if (idx > 1)
		pNewProfile->m_Description.Format("%s (%d)", temp, idx);
	else
		pNewProfile->m_Description = temp;

	pNewProfile->m_AisleProfileDBId = 0;
	
	try {
		aisleProfileDataService.StoreAisleProfile(*pNewProfile);
	}
	catch (...) {
		utilityHelper.ProcessError("Error storing new aisle profile.");
		delete pNewProfile;
		return;
	}

	HTREEITEM hItem = m_ProfileTreeCtrl.InsertItem(pNewProfile->m_Description, 2, 2, TVI_ROOT, TVI_LAST);
	m_ProfileTreeCtrl.SetItemData(hItem, (unsigned long)pNewProfile);
	m_ProfileTreeCtrl.SelectItem(hItem);

	m_AisleProfileList.Add(pNewProfile);
	m_ProfileTreeCtrl.SortChildren(TVI_ROOT);
	
	m_ProfileTreeCtrl.EditLabel(hItem);

}


void CAisleProfileListDialog::OnDelete() 
{
	CAisleProfile *pAisleProfile;
	
	HTREEITEM hItem = m_ProfileTreeCtrl.GetSelectedItem();
	if (hItem == NULL) {
		AfxMessageBox("Please select the aisle profile you wish to delete.");
		return;
	}

	pAisleProfile = (CAisleProfile *)m_ProfileTreeCtrl.GetItemData(hItem);

	CString temp;
	temp.Format("Delete %s", pAisleProfile->m_Description);
	if (::MessageBox(this->m_hWnd, "Are you sure you wish to delete the aisle profile?",
		temp, MB_YESNO) != IDYES)
		return;

	CWaitCursor cwc;
	try {
		aisleProfileDataService.DeleteAisleProfile(pAisleProfile->m_AisleProfileDBId);
	}
	catch (...) {
		utilityHelper.ProcessError("Error deleting aisle profile.");
		return;
	}

	pAisleProfile->m_AisleProfileDBId = -1337;

	m_ProfileTreeCtrl.DeleteItem(hItem);

	for (int i=0; i < m_AisleProfileList.GetSize(); ++i) {
		if (m_AisleProfileList[i]->m_AisleProfileDBId == -1337)
			break;
	}
	if (i < m_AisleProfileList.GetSize()) {
		delete m_AisleProfileList[i];
		m_AisleProfileList.RemoveAt(i);
	}

}




void CAisleProfileListDialog::OnDblclkAisleProfileTree(NMHDR* pNMHDR, LRESULT* pResult) 
{
	HTREEITEM hItem = m_ProfileTreeCtrl.GetSelectedItem();
	if (hItem == NULL) {
		return;
	}

	OnEdit();
	
	*pResult = 0;
}

void CAisleProfileListDialog::OnViewDrawing() 
{
	CAisleProfile *pAisleProfile;
	
	HTREEITEM hItem = m_ProfileTreeCtrl.GetSelectedItem();
	if (hItem == NULL) {
		AfxMessageBox("Please select the aisle profile you wish to view.");
		return;
	}

	pAisleProfile = (CAisleProfile *)m_ProfileTreeCtrl.GetItemData(hItem);

	CWaitCursor cwc;
	if (pAisleProfile->m_pLeftSideProfile == NULL && pAisleProfile->m_pRightSideProfile == NULL) {
		try {
			aisleProfileDataService.GetAisleProfile(pAisleProfile->m_AisleProfileDBId, *pAisleProfile);
		}
		catch (...) {
			utilityHelper.ProcessError("Error loading aisle profile.");
			return;
		}
	}

	if (pAisleProfile->m_pLeftSideProfile != NULL &&
		pAisleProfile->m_pLeftSideProfile->m_BayProfileList.GetSize() == 0) {
		try {
			sideProfileDataService.GetSideProfile(pAisleProfile->m_pLeftSideProfile->m_SideProfileDBId,
				*pAisleProfile->m_pLeftSideProfile);
		}
		catch (...) {
			utilityHelper.ProcessError("Error loading left side profile.");
		}
	}

	if (pAisleProfile->m_pRightSideProfile != NULL && 
		pAisleProfile->m_pRightSideProfile->m_BayProfileList.GetSize() == 0) {
		try {
			sideProfileDataService.GetSideProfile(pAisleProfile->m_pRightSideProfile->m_SideProfileDBId,
				*pAisleProfile->m_pRightSideProfile);
		}
		catch (...) {
			utilityHelper.ProcessError("Error loading right side profile.");
		}
	}

	if (! facilityHelper.CheckCurrentFacility()) {
		AfxMessageBox("Error saving current facility.");
		return;
	}
	else if (controlService.IsFacilityOpen()) {
		if (AfxMessageBox("The current facility must be closed to view the aisle profile.\n"
			"Do you wish to close the facility and view the aisle profile?", MB_YESNO) != IDYES)
			return;
	}

	m_pAisleProfile = pAisleProfile;

	EndDialog(WM_VIEW_DRAWING);

}


void CAisleProfileListDialog::BuildAisleProfileTree()
{
	m_ProfileTreeCtrl.DeleteAllItems();

	for (int i=0; i < m_AisleProfileList.GetSize(); ++i) {
		CAisleProfile *pAisleProfile = m_AisleProfileList[i];

		HTREEITEM hItem = m_ProfileTreeCtrl.InsertItem(pAisleProfile->m_Description, 2, 2, TVI_ROOT, TVI_LAST);
		m_ProfileTreeCtrl.SetItemData(hItem, (unsigned long)pAisleProfile);
	}

	HTREEITEM hItem = m_ProfileTreeCtrl.GetNextItem(TVI_ROOT, TVGN_CHILD);
	HTREEITEM hTopItem = hItem;
	while (hItem != NULL) {
		m_ProfileTreeCtrl.Expand(hItem, TVE_EXPAND);
		HTREEITEM hNextItem = m_ProfileTreeCtrl.GetNextItem(hItem, TVGN_NEXT);
		hItem = hNextItem;
	}


	m_ProfileTreeCtrl.EnsureVisible(hTopItem);
}

void CAisleProfileListDialog::OnEndlabeleditAisleProfileTree(NMHDR* pNMHDR, LRESULT* pResult) 
{
	TV_DISPINFO* pTVDispInfo = (TV_DISPINFO*)pNMHDR;
	
	*pResult = 0;

	TVITEM tvItem;

	tvItem = pTVDispInfo->item;

	*pResult = 0;

	CAisleProfile *pAisleProfile = (CAisleProfile *)m_ProfileTreeCtrl.GetItemData(tvItem.hItem);

	if (tvItem.pszText != NULL) {
		CString txt(tvItem.pszText);

		if (txt.FindOneOf(BAD_FILE_CHARACTERS) != -1) {
			CString temp;
			temp.Format("The following characters can not be part of the aisle profile name:\n%s",
				BAD_FILE_CHARACTERS);
			AfxMessageBox(temp);
			m_ProfileTreeCtrl.EditLabel(tvItem.hItem);
			return;
		}

		for (int i=0; i < m_AisleProfileList.GetSize(); ++i) {
			if (m_AisleProfileList[i]->m_AisleProfileDBId == pAisleProfile->m_AisleProfileDBId)
				continue;

			if (m_AisleProfileList[i]->m_Description.Compare(txt) == 0) {
				AfxMessageBox("Please enter a aisle profile name that does not already exist.");
				m_ProfileTreeCtrl.EditLabel(tvItem.hItem);
				return;
			}
		}
		*pResult = 1;
	}
	else {
		*pResult = 0;
		return;
	}

	try {
		CWaitCursor cwc;
		aisleProfileDataService.UpdateAisleProfileName(pAisleProfile->m_AisleProfileDBId, tvItem.pszText);
	}
	catch (...) {
		AfxMessageBox("Error updating aisle profile name.");
		strcpy(tvItem.pszText, pAisleProfile->m_Description);
		*pResult = 0;
	}
	
	pAisleProfile->m_Description = tvItem.pszText;

	return;
}

void CAisleProfileListDialog::OnOK() 
{
	if (m_IsModeless)
		DestroyWindow();
	else
		CDialog::OnOK();
}

BOOL CAisleProfileListDialog::IsTreeCtrlEditMessage(WPARAM keyCode)
{
	BOOL rc = FALSE;

	CWnd*  focus = GetFocus();
	CEdit* edit  = m_ProfileTreeCtrl.GetEditControl();
	if ((CEdit *)focus == edit) {
		edit->SendMessage(WM_KEYDOWN, keyCode);
		rc = TRUE;
	}

	return rc;
}

void CAisleProfileListDialog::OnCancel() 
{
	if (!IsTreeCtrlEditMessage(VK_ESCAPE)) {
		if (m_IsModeless)
			DestroyWindow();
		else
			CDialog::OnCancel();
	}
}

void CAisleProfileListDialog::OnContextMenu(CWnd* pWnd, CPoint point) 
{
	if (pWnd != &m_ProfileTreeCtrl)
		return;

	CMenu menu;
	menu.LoadMenu(IDR_GENERIC_MENU);

	CPoint pt(point);
	m_ProfileTreeCtrl.ScreenToClient(&pt);
	UINT nFlags;
	HTREEITEM hItem = m_ProfileTreeCtrl.HitTest(pt, &nFlags);
	if (hItem == NULL)
		return;
	else {
		m_ProfileTreeCtrl.SelectItem(hItem);

		menu.GetSubMenu(0)->ModifyMenu(0, MF_BYPOSITION|MF_STRING, IDC_NEW, "&New");
		menu.GetSubMenu(0)->ModifyMenu(1, MF_BYPOSITION|MF_STRING, IDC_EDIT, "&Edit");
		menu.GetSubMenu(0)->ModifyMenu(2, MF_BYPOSITION|MF_STRING, IDC_COPY, "&Copy");
		menu.GetSubMenu(0)->AppendMenu(MF_BYPOSITION|MF_STRING, IDC_DELETE, "&Delete");
	}

	menu.GetSubMenu(0)->TrackPopupMenu(TPM_LEFTALIGN|TPM_RIGHTBUTTON, point.x, point.y, this);	
}


void CAisleProfileListDialog::PostNcDestroy() 
{

	if (m_IsModeless)
		delete this;

	CDialog::PostNcDestroy();
}

void CAisleProfileListDialog::OnBayWizard() 
{
	EndDialog(WM_BAY_WIZARD);	
}

void CAisleProfileListDialog::OnSideWizard() 
{
	EndDialog(WM_SIDE_WIZARD);	
}

void CAisleProfileListDialog::OnMainWizard() 
{
	EndDialog(WM_MAIN_WIZARD);	
}

BOOL CAisleProfileListDialog::OnHelpInfo(HELPINFO* pHelpInfo) 
{
	helpService.ShowFieldHelp(IDD, pHelpInfo->iCtrlId);

	return FALSE;

}

void CAisleProfileListDialog::OnHelp() 
{
	helpService.ShowScreenHelp(IDD);

	return;

}
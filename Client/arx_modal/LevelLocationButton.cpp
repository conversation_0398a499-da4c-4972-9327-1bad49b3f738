// LevelLocationButton.cpp : implementation file
//

#include "stdafx.h"
#include "modal.h"
#include "LevelLocationButton.h"
#include "LevelLocationDialog.h"

#ifdef _DEBUG
#define new DEBUG_NEW
#undef THIS_FILE
static char THIS_FILE[] = __FILE__;
#endif

#define ARROWLEN 3
#define DIRECTION_LEFT 0
#define DIRECTION_RIGHT 1
#define DIRECTION_NONE -1
/////////////////////////////////////////////////////////////////////////////
// CLevelLocationButton

CLevelLocationButton::CLevelLocationButton()
{
}

CLevelLocationButton::~CLevelLocationButton()
{
}

BEGIN_MESSAGE_MAP(CLevelLocationButton, CButton)
	//{{AFX_MSG_MAP(CLevelLocationButton)
	ON_WM_LBUTTONDOWN()
	ON_WM_LBUTTONDBLCLK()
	ON_WM_MOUSEMOVE()
	//}}AFX_MSG_MAP
END_MESSAGE_MAP()

/////////////////////////////////////////////////////////////////////////////
// CLevelLocationButton message handlers

void CLevelLocationButton::DrawItem(LPDRAWITEMSTRUCT lpDrawItemStruct) 
{
	UINT uStyle = DFCS_BUTTONPUSH;
	
	// This code only works with buttons.
	ASSERT(lpDrawItemStruct->CtlType == ODT_BUTTON);
	
	// Draw the button frame.
	::DrawFrameControl(lpDrawItemStruct->hDC, &lpDrawItemStruct->rcItem, 
		DFC_BUTTON, uStyle);
	
	CDC cdc;
	cdc.Attach(lpDrawItemStruct->hDC);
	
	SetMapping(cdc);

	CPen pen, *pPrevPen;
	pen.CreatePen(PS_SOLID, 0, RGB(0,0,0));
	pPrevPen = cdc.SelectObject(&pen);

	CSize vpSize = cdc.GetViewportExt();
	CSize winSize = cdc.GetWindowExt();

	CRect r;
	GetClientRect(&r);
	
	// sometimes we want to specify a coordinate in device coordinates so calculate the ratio
	// of logical (bay) coordinates to device coordinates
	double ratio = (m_BayInfo.m_Height*10)/(r.Height()-30);

	DrawHorzDimLine(cdc, CPoint(0, -10*ratio), (m_BayInfo.m_Width*10)/10, 1, "Left", 12*ratio, DIRECTION_LEFT);
	DrawHorzDimLine(cdc, CPoint((int)((m_BayInfo.m_Width*10)-m_BayInfo.m_Width*10/10), -10*ratio), (m_BayInfo.m_Width*10)/10, 1, "Right", 12*ratio, DIRECTION_RIGHT);

	int direction = DIRECTION_NONE;

	// In a two-way pickpath, both sides go the same way (if you're facing the bay)
	if (m_BayInfo.m_PickPathType == CPickPath::ppTwoWay) {
		if (m_BayInfo.m_PickPathDirection == 0)
			direction = DIRECTION_RIGHT;
		else if (m_BayInfo.m_PickPathDirection == 1)
			direction = DIRECTION_LEFT;
	}
	else {
		if (m_BayInfo.m_IsRotated) {
			if (m_BayInfo.m_PickPathDirection == 0)		// pick path starts near origin
				direction = DIRECTION_LEFT;				// aisle goes from right to left   
			else if (m_BayInfo.m_PickPathDirection == 1)// pick path starts opposite origin
				direction = DIRECTION_RIGHT;			//aisle goes from left to right
		}
		else {
			if (m_BayInfo.m_PickPathDirection == 0)
				direction = DIRECTION_RIGHT;			//aisle goes from left to right
			else if (m_BayInfo.m_PickPathDirection == 1)
				direction = DIRECTION_LEFT;				//aisle goes from right to left
		}
	}

	if (direction == DIRECTION_RIGHT)
		DrawHorzDimLine(cdc, CPoint((m_BayInfo.m_Width*10/2), -10*ratio), (m_BayInfo.m_Width*10)/10, 1, "Aisle Direction", 12*ratio, direction);
	else if (direction == DIRECTION_LEFT)
		DrawHorzDimLine(cdc, CPoint((m_BayInfo.m_Width*10/2)-m_BayInfo.m_Width*10/10, -10*ratio), (m_BayInfo.m_Width*10)/10, 1, "Aisle Direction", 12*ratio, direction);


	cdc.MoveTo(0, 0);
	cdc.LineTo(0, (int)(m_BayInfo.m_UprightHeight*10));
	cdc.MoveTo((int)(m_BayInfo.m_Width*10), 0);
	cdc.LineTo((int)(m_BayInfo.m_Width*10), (int)(m_BayInfo.m_UprightHeight*10));

	// assume they are sorted by ascending height
	for (int i=0; i < m_BayInfo.m_LevelList.GetSize(); ++i) {
		CBayLevelInfo *pLevel = m_BayInfo.m_LevelList[i];
		CPen tempPen;
		COLORREF c;
		if (pLevel->m_IsSelected)
			c = RGB(255, 0, 0);
		else
			c = RGB(0, 0, 0);

	 	tempPen.CreatePen(PS_SOLID, 0, c);
		cdc.SelectObject(&tempPen);
		
		// Create the bounding rect which is used to determine when the level is selected
		pLevel->m_BoundingRect.left = 0;
		pLevel->m_BoundingRect.right = (int)m_BayInfo.m_Width*10;
		pLevel->m_BoundingRect.bottom = (int)(pLevel->m_Height*10);
		if (i < m_BayInfo.m_LevelList.GetSize() - 1) {
			pLevel->m_BoundingRect.top = (int)(m_BayInfo.m_LevelList[i+1]->m_Height*10);
		}
		else {
			pLevel->m_BoundingRect.top = (int)(m_BayInfo.m_Height*10);
		}
		

		if (pLevel->m_IsHidden) {
			DrawDashedHorzLine(cdc, CPoint(0, (int)(pLevel->m_Height*10)), 
				CPoint((int)(m_BayInfo.m_Width*10), (int)(pLevel->m_Height*10)));
		}
		else {
			if (pLevel->m_Height != m_BayInfo.m_Height) {
				cdc.MoveTo(0, (int)(pLevel->m_Height*10));//-info.m_Thickness);
				cdc.LineTo((int)(m_BayInfo.m_Width*10), (int)(pLevel->m_Height*10));//-info.m_Thickness);
			}
		}



		// Draw locations
		int locCount = pLevel->m_LocationList.GetSize() / pLevel->m_LocationRowCount;

		int x = 0;

		for (int j=0; j < locCount; ++j) {

			CBayLocationInfo *pLocation;

			// If the bay is on the opposite side of the aisle from the aisle placement point,
			// it is rotated 90 degrees.  We need to display those bays in the opposite order
			// so that the left of the bay is the higher coordinate (relative to the aisle coordinate)
			if (! m_BayInfo.m_IsRotated)
				pLocation = pLevel->m_LocationList[j];
			else
				pLocation = pLevel->m_LocationList[(locCount-1)-j];
				

			CRect locRect;
			locRect.bottom = (int)(pLevel->m_Height*10) + 10;
			
			// Find the first non-hidden bar above this one;
			// Note: see BayProfile->ResetLocationSizes for more info
			// Note2: I decided to show location opening height instead of usable so
			// it know longer subtracts the clearance
			CBayLevelInfo *pAbove = NULL;
			int aboveIdx = i + 1;
			while (aboveIdx < m_BayInfo.m_LevelList.GetSize()) {
				pAbove = m_BayInfo.m_LevelList[aboveIdx];
				if (! pAbove->m_IsHidden)
					break;
				aboveIdx++;
				pAbove = NULL;
			}
			
			if (pAbove != NULL) {
				locRect.top = (int)(pAbove->m_Height*10) -
					(int)(pAbove->m_Thickness*10) - 1;
				//(int)(pLocation->m_Clearance*10);
				//if (pLocation->m_Clearance == 0)
				//	locRect.top--;
			}
			else {
				locRect.top = (int)(m_BayInfo.m_Height*10) - 1; // - (int)(pLocation->m_Clearance*10) - 1;
			}


			if (pLocation->m_LocationSpace > 0)
				x += (int)(pLocation->m_LocationSpace*10);
			else
				x += 0;
			
			locRect.left = x;
			x += (int)(pLocation->m_Width*10);
			locRect.right = x;

			if  (pLocation->m_IsSelected)
				DrawBoxWithText(cdc, locRect, 0, TRUE, pLocation->m_Description, 12*ratio);
			else
				DrawBoxWithText(cdc, locRect, 0, FALSE, pLocation->m_Description, 12*ratio);

			pLocation->m_BoundingRect = locRect;

			if (pLocation->m_LocationSpace > 0)
				x += (int)(pLocation->m_LocationSpace*10);
			else
				x += 0;
		}



		//int locWidth = (m_BayWidth*10 - locCount*2*info.m_LocationSpace*10) / locCount;

		/*
		if (locWidth < info.m_MinimumWidth) {
			locCount = m_BayWidth / (info.m_MinimumWidth+2*info.m_LocationSpace);
			locWidth = (m_BayWidth - locCount*2*info.m_LocationSpace) / locCount;
			if (locWidth <= 0)
				continue;
		}
		*/


		/*
		for (int j=0; j < locCount; ++j) {
			locRect.left = 5*(info.m_LocationSpace < 1) + info.m_LocationSpace*10 + info.m_LocationSpace*j*2*10 + j*locWidth;
			locRect.right = locRect.left + locWidth;
			if (j == locCount-1 && info.m_LocationSpace < 1)
				locRect.right = m_BayWidth*10 - 5;

			//if (locRect.right >= m_BayWidth-2)
			//	locRect.right = (m_BayWidth-2) - info.m_LocationSpace;
			if  (info.m_IsSelected)
				DrawBox(cdc, locRect, 0, TRUE);
			else
				DrawBox(cdc, locRect, 0, FALSE);
		}
		*/
			
	}

	cdc.SelectObject(pPrevPen);
	pen.DeleteObject();
	cdc.Detach();	
}



void CLevelLocationButton::SetMapping(CDC &cdc)
{
	CRect r;
	GetClientRect(&r);

	m_LogicalBayWidth = r.Width();

	cdc.SetMapMode(MM_ANISOTROPIC);
	cdc.SetViewportOrg(10, r.bottom-20);
	cdc.SetWindowExt((int)(m_BayInfo.m_Width*10), (int)(m_BayInfo.m_Height*10));
	cdc.SetViewportExt(r.Width()-20, -(r.Height()-30));
}

void CLevelLocationButton::DrawDashedHorzLine(CDC &cdc, const CPoint &startPt, const CPoint &endPt)
{
	for (int i=0; i < endPt.x - startPt.x; ++i) {
		if (i%2 == 0)
			cdc.MoveTo(startPt.x+i, startPt.y);
		else
			cdc.LineTo(startPt.x+i, startPt.y);
	}

}


void CLevelLocationButton::DrawBox(CDC &cdc, const CRect& r, int width, BOOL bDashed)
{
	CPen pen, *prevPen;

	if (bDashed)
		pen.CreatePen(PS_SOLID, width, RGB(255, 0, 0));
	else
		pen.CreatePen(PS_SOLID, width, RGB(0,0,255));

	prevPen = cdc.SelectObject(&pen);
	
	cdc.MoveTo(r.BottomRight());
	cdc.LineTo(r.right, r.top);
	cdc.LineTo(r.left, r.top);
	cdc.LineTo(r.left, r.bottom);
	cdc.LineTo(r.right, r.bottom);

	cdc.SelectObject(prevPen);
	pen.DeleteObject();	
}


void CLevelLocationButton::OnLButtonDown(UINT nFlags, CPoint point) 
{
	CDC *pCDC = this->GetWindowDC();

	SetMapping(*pCDC);
	
	CString temp;
	CPoint bayPt(point);
	// Convert the window point to the bay point based on the mapping above
	pCDC->DPtoLP(&bayPt, 1);
	temp.Format("Device: %d,%d, Bay: %d,%d", point.x, point.y, bayPt.x, bayPt.y);
	if (bayPt.x < 0 || bayPt.x > m_BayInfo.m_Width*10) {
		CButton::OnLButtonDown(nFlags, point);
		return;
	}
	
	int level = -1, loc = -1;

	CRect r;
	for (int i=0; i < m_BayInfo.m_LevelList.GetSize(); ++i) {
		CBayLevelInfo *pLevel = m_BayInfo.m_LevelList[i];
		
		pLevel->m_IsSelected = FALSE;
		
		r = pLevel->m_BoundingRect;
		r.NormalizeRect();
		
		if (r.PtInRect(bayPt)) {
			level = i;
			
			for (int j=0; j < pLevel->m_LocationList.GetSize(); ++j) {
				CBayLocationInfo *pLocation = pLevel->m_LocationList[j];
				pLocation->m_IsSelected = FALSE;
				
				r = pLocation->m_BoundingRect;
				r.NormalizeRect();
				
				if (r.PtInRect(bayPt)) {
					loc = j;		
					break;
				}
			}
			
			break;
		}
		
	}

	if (loc >= 0)
		SelectLocation(level, loc);
	else if (level >= 0)
		SelectLevel(level);

	GetParent()->PostMessage(WM_SELECT_LEVEL, WPARAM(level), LPARAM(loc));
	
	CButton::OnLButtonDown(nFlags, point);
}


void CLevelLocationButton::OnLButtonDblClk(UINT nFlags, CPoint point) 
{	
	CDC *pCDC = this->GetWindowDC();

	SetMapping(*pCDC);
	
	CString temp;
	CPoint bayPt(point);
	// Convert the window point to the bay point based on the mapping above
	pCDC->DPtoLP(&bayPt, 1);
	temp.Format("Device: %d,%d, Bay: %d,%d", point.x, point.y, bayPt.x, bayPt.y);
	if (bayPt.x < 0 || bayPt.x > m_BayInfo.m_Width*10) {
		CButton::OnLButtonDown(nFlags, point);
		return;
	}
	
	int level = -1, loc = -1;

	CRect r;
	for (int i=0; i < m_BayInfo.m_LevelList.GetSize(); ++i) {
		CBayLevelInfo *pLevel = m_BayInfo.m_LevelList[i];
		
		pLevel->m_IsSelected = FALSE;
		
		r = pLevel->m_BoundingRect;
		r.NormalizeRect();
		
		if (r.PtInRect(bayPt)) {
			level = i;
			
			for (int j=0; j < pLevel->m_LocationList.GetSize(); ++j) {
				CBayLocationInfo *pLocation = pLevel->m_LocationList[j];
				pLocation->m_IsSelected = FALSE;
				
				r = pLocation->m_BoundingRect;
				r.NormalizeRect();
				
				if (r.PtInRect(bayPt)) {
					loc = j;		
					break;
				}
			}
			
			break;
		}
		
	}

	if (loc >= 0)
		SelectLocation(level, loc);
	else if (level >= 0)
		SelectLevel(level);

	GetParent()->PostMessage(WM_DBLCLK_LEVEL, WPARAM(level), LPARAM(loc));

	CButton::OnLButtonDblClk(nFlags, point);
}

void CLevelLocationButton::SelectLevel(int levelIdx)
{
	CBayLevelInfo *pSelLevel = NULL;
	CBayLevelInfo *pOldSelLevel = NULL;

	for (int i=0; i < m_BayInfo.m_LevelList.GetSize(); ++i) {
		CBayLevelInfo *pLevel = m_BayInfo.m_LevelList[i];
		
		if (pLevel->m_IsSelected)
			pOldSelLevel = pLevel;

		if (levelIdx == i) {
			pLevel->m_IsSelected = TRUE;
			pSelLevel = pLevel;
		}
		else
			pLevel->m_IsSelected = FALSE;

		for (int j=0; j < pLevel->m_LocationList.GetSize(); ++j) {
			CBayLocationInfo *pLocation = pLevel->m_LocationList[j];
			pLocation->m_IsSelected = FALSE;
		}

	}

	Invalidate();
	/*
	if (pOldSelLevel != NULL && pOldSelLevel != pSelLevel)
		InvalidateRect(pOldSelLevel->m_BoundingRect);

	if (pSelLevel != NULL)
		InvalidateRect(pSelLevel->m_BoundingRect);
	*/
}

void CLevelLocationButton::SelectLocation(int levelIdx, int locIdx)
{
	CBayLocationInfo *pSelLoc = NULL;
	CBayLocationInfo *pOldSelLoc = NULL;

	for (int i=0; i < m_BayInfo.m_LevelList.GetSize(); ++i) {
		CBayLevelInfo *pLevel = m_BayInfo.m_LevelList[i];
		
		pLevel->m_IsSelected = FALSE;

		for (int j=0; j < pLevel->m_LocationList.GetSize(); ++j) {
			CBayLocationInfo *pLocation = pLevel->m_LocationList[j];
			if (pLocation->m_IsSelected)
				pOldSelLoc = pLocation;

			if (levelIdx == i && locIdx == j) {
				pLocation->m_IsSelected = TRUE;
				pSelLoc = pLocation;
			}
			else
				pLocation->m_IsSelected = FALSE;
		}

	}

	Invalidate();
	/*
	if (pOldSelLoc != NULL && pOldSelLoc != pSelLoc)
		InvalidateRect(pOldSelLoc->m_BoundingRect, TRUE);

	if (pSelLoc != NULL)
		InvalidateRect(pSelLoc->m_BoundingRect, TRUE);
	*/
}


void CLevelLocationButton::DrawBoxWithText(CDC &cdc, const CRect& r, int width, BOOL bDashed,
												const CString &text, int textSize)
{
	CPen pen, *prevPen;
	CFont font, *pOldFont;
	
	try {
		
		if (font.CreateFont(textSize, 0,0,0,FW_BOLD, 0,0,0, ANSI_CHARSET, OUT_DEFAULT_PRECIS,
			CLIP_DEFAULT_PRECIS, DEFAULT_QUALITY, DEFAULT_PITCH|FF_DONTCARE, "Arial")) {			
			pOldFont = cdc.SelectObject(&font);
		}
		else {
			return;
		}
	}
	catch (...) {
		return;
	}
	
	
	CSize size = cdc.GetTextExtent(text);
	int hgt = r.Height();
	int wid = r.Width();
	if (size.cx < abs(r.Width()) && size.cy < abs(r.Height())) {
		cdc.SetTextColor(RGB(0,0, 255));
		cdc.TextOut(r.CenterPoint().x-size.cx/2, r.CenterPoint().y+size.cy/2, text);
		cdc.SetTextColor(RGB(0,0,0));
	}
	
	cdc.SelectObject(pOldFont);
	font.DeleteObject();
	
	if (bDashed)
		pen.CreatePen(PS_SOLID, width, RGB(255,0,0));
	else
		pen.CreatePen(PS_SOLID, width, RGB(0,0,0));
	
	prevPen = cdc.SelectObject(&pen);
	
	cdc.MoveTo(r.BottomRight());
	cdc.LineTo(r.right, r.top);
	cdc.LineTo(r.left, r.top);
	cdc.LineTo(r.left, r.bottom);
	cdc.LineTo(r.right, r.bottom);
	
	
	
	cdc.SelectObject(prevPen);
	pen.DeleteObject();	
}


void CLevelLocationButton::DrawHorzDimLine(CDC &cdc, const CPoint &startPt, int len, int width, const CString &text,
											   int textSize, int direction)
{
	CPen pen, *prevPen;
	CFont font, *pOldFont;

	try {
		
		if (font.CreateFont(textSize, 0,0,0,FW_BOLD, 0,0,0, ANSI_CHARSET, OUT_DEFAULT_PRECIS,
			CLIP_DEFAULT_PRECIS, DEFAULT_QUALITY, DEFAULT_PITCH|FF_DONTCARE, "Arial")) {			
			pOldFont = cdc.SelectObject(&font);
		}
		else {
			return;
		}
	}
	catch (...) {
		return;
	}
	CSize size = cdc.GetTextExtent(text);
	cdc.SetTextColor(RGB(0,0,0));
	if (direction == DIRECTION_LEFT)		// left
		cdc.TextOut(startPt.x+len+3, startPt.y+size.cy/2, text);
	else if (direction == DIRECTION_RIGHT) // right
		cdc.TextOut(startPt.x-3-size.cx, startPt.y+size.cy/2, text);
	else
		cdc.TextOut(startPt.x+len/2-size.cx/2, startPt.y+size.cy/2, text);

	cdc.SetTextColor(RGB(0,0, 0));
	
	CRect r;
	GetClientRect(&r);
	double ratio = (m_BayInfo.m_Height*10)/(r.Height()-30);

	int arrowLen = ARROWLEN * ratio;

	if (size.cx < len || direction == DIRECTION_RIGHT || direction == DIRECTION_LEFT) {
		pen.CreatePen(PS_SOLID, width, RGB(0,0,0));
		prevPen = cdc.SelectObject(&pen);
				
		cdc.MoveTo(startPt);
		
		// Left Arrow
		if (direction != DIRECTION_RIGHT) {
			cdc.LineTo(startPt.x+arrowLen, startPt.y-arrowLen);
			cdc.MoveTo(startPt);
			cdc.LineTo(startPt.x+arrowLen, startPt.y+arrowLen);
		}
		
		// Main line
		cdc.MoveTo(startPt);
		if (direction == DIRECTION_NONE) {
			cdc.LineTo(startPt.x+len/2-size.cx/2-2, startPt.y);
			cdc.MoveTo(startPt.x+len/2+size.cx/2+1, startPt.y);
		}
		else
			cdc.LineTo(startPt.x+len, startPt.y);
		
		// Right Arrow
		if (direction != DIRECTION_LEFT) {
			cdc.LineTo((startPt.x+len)-arrowLen, startPt.y+arrowLen);
			cdc.MoveTo(startPt.x+len, startPt.y);
			cdc.LineTo((startPt.x+len)-arrowLen, startPt.y-arrowLen);
		}
		
		cdc.SelectObject(prevPen);
		pen.DeleteObject();
	}
	cdc.SelectObject(pOldFont);
	font.DeleteObject();
}

void CLevelLocationButton::OnMouseMove(UINT nFlags, CPoint point) 
{
	CDC *pCDC = this->GetWindowDC();

	SetMapping(*pCDC);
	
	CString temp;
	CPoint bayPt(point);
	// Convert the window point to the bay point based on the mapping above
	pCDC->DPtoLP(&bayPt, 1);
	temp.Format("Device: %d,%d, Bay: %d,%d", point.x, point.y, bayPt.x, bayPt.y);
	if (bayPt.x < 0 || bayPt.x > m_BayInfo.m_Width*10) {
		CButton::OnMouseMove(nFlags, point);
		return;
	}
	
	int level = -1, loc = -1;

	CRect r;

	CBayLevelInfo *pSelLevel = NULL;;
	CBayLocationInfo *pSelLoc = NULL;;

	for (int i=0; i < m_BayInfo.m_LevelList.GetSize(); ++i) {
		CBayLevelInfo *pLevel = m_BayInfo.m_LevelList[i];

		r = pLevel->m_BoundingRect;
		r.NormalizeRect();

		if (r.PtInRect(bayPt)) {
			pSelLevel = pLevel;
			level = i;
		}

		for (int j=0; j < pLevel->m_LocationList.GetSize(); ++j) {
			CBayLocationInfo *pLocation = pLevel->m_LocationList[j];
			
			r = pLocation->m_BoundingRect;
			r.NormalizeRect();
			
			if (r.PtInRect(bayPt)) {
				pSelLoc = pLocation;
				loc = j;
			}
		}

	}

	CString msg;
	CLevelLocationDialog *pParent = (CLevelLocationDialog *)GetParent();

	if (loc >= 0) {
		msg.Format(" Location: %s\n Coordinates(X,Y,Z): %.0f, %.0f, %.0f ", 
			pSelLoc->m_Description, pSelLoc->m_Coordinates.m_X,
			pSelLoc->m_Coordinates.m_Y, pSelLoc->m_Coordinates.m_Z);
		pParent->UpdateToolTip(point.x, point.y, msg);
	}
	else if (level >= 0) {
		msg.Format(" Level: %s\n Crossbar Height: %.0f ", 
			pSelLevel->m_Description, pSelLevel->m_Height);
		pParent->UpdateToolTip(point.x, point.y, msg);
	}

	CButton::OnMouseMove(nFlags, point);
}

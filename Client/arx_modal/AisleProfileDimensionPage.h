#if !defined(AFX_AISLEPROFILEDIMENSIONPAGE_H__2C6189CB_EE18_453C_B6A8_7A2033A09474__INCLUDED_)
#define AFX_AISLEPROFILEDIMENSIONPAGE_H__2C6189CB_EE18_453C_B6A8_7A2033A09474__INCLUDED_
#include "AisleProfile.h"
#include "AisleProfileButton.h"

#if _MSC_VER > 1000
#pragma once
#endif // _MSC_VER > 1000
// AisleProfileDimensionPage.h : header file
//

/////////////////////////////////////////////////////////////////////////////
// CAisleProfileDimensionPage dialog

class CAisleProfileDimensionPage : public CPropertyPage
{
	DECLARE_DYNCREATE(CAisleProfileDimensionPage)

// Construction
public:
	CAisleProfileDimensionPage();
	~CAisleProfileDimensionPage();
	CAisleProfile *m_pAisleProfile;

// Dialog Data
	//{{AFX_DATA(CAisleProfileDimensionPage)
	enum { IDD = IDD_AISLE_PROFILE_DIMENSION_PAGE };
	CAisleProfileButton	m_AisleButton;
	CString	m_AisleSpace;
	CString	m_RightSpace;
	CString	m_LeftSpace;
	//}}AFX_DATA


// Overrides
	// ClassWizard generate virtual function overrides
	//{{AFX_VIRTUAL(CAisleProfileDimensionPage)
	public:
	virtual BOOL OnSetActive();
	virtual BOOL OnKillActive();
	protected:
	virtual void DoDataExchange(CDataExchange* pDX);    // DDX/DDV support
	virtual BOOL OnCommand(WPARAM wParam, LPARAM lParam);
	virtual BOOL OnNotify(WPARAM wParam, LPARAM lParam, LRESULT* pResult);
	//}}AFX_VIRTUAL

// Implementation
protected:
	// Generated message map functions
	//{{AFX_MSG(CAisleProfileDimensionPage)
	afx_msg BOOL OnHelpInfo(HELPINFO* pHelpInfo);
	//}}AFX_MSG
	DECLARE_MESSAGE_MAP()

private:

};

//{{AFX_INSERT_LOCATION}}
// Microsoft Visual C++ will insert additional declarations immediately before the previous line.

#endif // !defined(AFX_AISLEPROFILEDIMENSIONPAGE_H__2C6189CB_EE18_453C_B6A8_7A2033A09474__INCLUDED_)

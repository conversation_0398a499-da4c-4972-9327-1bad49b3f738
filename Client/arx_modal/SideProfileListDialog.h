#if !defined(AFX_SIDEPROFILELISTDIALOG_H__7C2DAB95_AE7F_41B2_9CD3_4441DCEDFB4F__INCLUDED_)
#define AFX_SIDEPROFILELISTDIALOG_H__7C2DAB95_AE7F_41B2_9CD3_4441DCEDFB4F__INCLUDED_

#include "SideProfile.h"

#if _MSC_VER > 1000
#pragma once
#endif // _MSC_VER > 1000
// SideProfileListDialog.h : header file
//

/////////////////////////////////////////////////////////////////////////////
// CSideProfileListDialog dialog

class CSideProfileListDialog : public CDialog
{
// Construction
public:
	BOOL m_IsModeless;
	CSideProfile *m_pSideProfile;
	CSideProfileListDialog(CWnd* pParent = NULL);   // standard constructor
	~CSideProfileListDialog();

// Dialog Data
	//{{AFX_DATA(CSideProfileListDialog)
	enum { IDD = IDD_SIDE_PROFILE_LIST };
	CTreeCtrl	m_ProfileTreeCtrl;
	//}}AFX_DATA


// Overrides
	// ClassWizard generated virtual function overrides
	//{{AFX_VIRTUAL(CSideProfileListDialog)
	protected:
	virtual void DoDataExchange(CDataExchange* pDX);    // DDX/DDV support
	virtual void PostNcDestroy();
	//}}AFX_VIRTUAL

// Implementation
protected:

	// Generated message map functions
	//{{AFX_MSG(CSideProfileListDialog)
	virtual BOOL OnInitDialog();
	virtual void OnOK();
	virtual void OnCancel();
	afx_msg void OnEndlabeleditSideProfileTree(NMHDR* pNMHDR, LRESULT* pResult);
	afx_msg void OnNew();
	afx_msg void OnEdit();
	afx_msg void OnCopy();
	afx_msg void OnDelete();
	afx_msg void OnContextMenu(CWnd* pWnd, CPoint point);
	afx_msg void OnViewDrawing();
	afx_msg void OnDblclkSideProfileTree(NMHDR* pNMHDR, LRESULT* pResult);
	afx_msg void OnHelp();
	afx_msg void OnBayWizard();
	afx_msg void OnAisleWizard();
	afx_msg void OnMainWizard();
	afx_msg BOOL OnHelpInfo(HELPINFO* pHelpInfo);
	//}}AFX_MSG
	DECLARE_MESSAGE_MAP()
private:
	BOOL IsTreeCtrlEditMessage(WPARAM keyCode);
	void BuildSideProfileTree();
	CImageList m_ImageList;
	int LoadSideProfileList();
	
	CTypedPtrArray<CObArray, CSideProfile*> m_SideProfileList;

};

//{{AFX_INSERT_LOCATION}}
// Microsoft Visual C++ will insert additional declarations immediately before the previous line.

#endif // !defined(AFX_SIDEPROFILELISTDIALOG_H__7C2DAB95_AE7F_41B2_9CD3_4441DCEDFB4F__INCLUDED_)

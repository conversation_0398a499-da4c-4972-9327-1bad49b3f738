// ProfilePage.cpp : implementation file
//

#include "stdafx.h"
#include "modal.h"
#include "ProfilePage.h"

#ifdef _DEBUG
#define new DEBUG_NEW
#undef THIS_FILE
static char THIS_FILE[] = __FILE__;
#endif

/////////////////////////////////////////////////////////////////////////////
// CProfilePage property page

IMPLEMENT_DYNCREATE(CProfilePage, CPropertyPage)

CProfilePage::CProfilePage() : CPropertyPage(CProfilePage::IDD)
{
	//{{AFX_DATA_INIT(CProfilePage)
		// NOTE: the ClassWizard will add member initialization here
	//}}AFX_DATA_INIT
}

CProfilePage::~CProfilePage()
{
}

void CProfilePage::DoDataExchange(CDataExchange* pDX)
{
	CPropertyPage::DoDataExchange(pDX);
	//{{AFX_DATA_MAP(CProfilePage)
		// NOTE: the ClassWizard will add DDX and DDV calls here
	//}}AFX_DATA_MAP
}


BEGIN_MESSAGE_MAP(CProfilePage, CPropertyPage)
	//{{AFX_MSG_MAP(CProfilePage)
		// NOTE: the ClassWizard will add message map macros here
	//}}AFX_MSG_MAP
END_MESSAGE_MAP()

/////////////////////////////////////////////////////////////////////////////
// CProfilePage message handlers

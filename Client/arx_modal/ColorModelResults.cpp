//////////////////////////////////////////////////////////////////////
// Class Name   :	CColorModelResults
// Description :	This file contains the functions, event handling,
//					and validation of the Color Model Results dialog.         
// Date Created :	05/1998
// Author :			ACS
//////////////////////////////////////////////////////////////////////
// Inputs :			
// Outputs :	
// Explanation :			
//		This class is an user interface class for the Color Model 
//		Results dialog.  It presents to the user the results 
//		accumulated while coloring the facility based on the Pass
//		4 solution set.  It presents each color available and the
//		minimum and maximum values assigned to that color.
//////////////////////////////////////////////////////////////////////
//////////////////////////////////////////////////////////////////////
// Revisions :		1.00.00
//   Date-initials : 
//					11/04/1998	- ACS	- Added comments
//////////////////////////////////////////////////////////////////////
	/*******************************************************
	*
	*                       NOTICE
	*
	*  THIS SOFTWARE IS THE PROPERTY OF AND CONTAINS
	*  CONFIDENTIAL INFORMATION OF SSA GLOBAL 
	*  TECHNOLOGIES, INC., AND SHALL NOT BE COPIED,
	*  USED, NOR DISCLOSED WITHOUT EXPRESS WRITTEN
	*  AUTHORIZATION.  ALTHOUGH PUBLICATION IS NOT
	*  INTENDED, IN THE EVENT OF PUBLICATION, THE
	*  FOLLOWING NOTICE IS APPLICABLE:
	*
	*  (c) COPYRIGHT 2004 SSA GLOBAL TECHNOLOGIES, INC.
	*
	*           SSA GLOBAL TECHNOLOGIES, INC.
	*
	********************************************************/
//////////////////////////////////////////////////////////////////////

// ColorModelResults.cpp : implementation file
//

#include "stdafx.h"
#include "modal.h"
#include "ColorModelResults.h"
#include "HelpService.h"
#include "AutoCADCommands.h"

#ifdef _DEBUG
#define new DEBUG_NEW
#undef THIS_FILE
static char THIS_FILE[] = __FILE__;
#endif

extern CHelpService helpService;
extern CMap<int, int&, float, float&> minResultsMap;
extern CMap<int, int&, float, float&> maxResultsMap;


/////////////////////////////////////////////////////////////////////////////
// CColorModelResults dialog


CColorModelResults::CColorModelResults(CWnd* pParent /*=NULL*/)
	: CDialog(CColorModelResults::IDD, pParent)
{
	//{{AFX_DATA_INIT(CColorModelResults)
		// NOTE: the ClassWizard will add member initialization here
	//}}AFX_DATA_INIT


}


void CColorModelResults::DoDataExchange(CDataExchange* pDX)
{
	CDialog::DoDataExchange(pDX);
	//{{AFX_DATA_MAP(CColorModelResults)
	DDX_Control(pDX, IDC_RESULTS_LIST, m_ctlResultsList);
	//}}AFX_DATA_MAP
}


BEGIN_MESSAGE_MAP(CColorModelResults, CDialog)
	//{{AFX_MSG_MAP(CColorModelResults)
	ON_BN_CLICKED(IDHELP, OnHelp)
	//}}AFX_MSG_MAP
END_MESSAGE_MAP()

/////////////////////////////////////////////////////////////////////////////
// CColorModelResults message handlers


///////////////////////////////////////////////////////////////
//This method is executed at the initialization of the Dialog.
///////////////////////////////////////////////////////////////
BOOL CColorModelResults::OnInitDialog() 
{
	CDialog::OnInitDialog();
	CAutoCADCommands acCmds;

	CString colorData;
	int pos1;
	float maxVal = 999999999.0f;
	float minVal = 0.0;

	//Display the results of Color Model on this modeless dialog.
	//We only display six colors, so loop through each color
	//and display the results of each color
	for (pos1 = 0; pos1 < 6; pos1++)
	{
		float storedVal1;
		float storedVal2;
		int nIndex = pos1;
		CString strColor;

		minResultsMap.Lookup(nIndex, storedVal1);
		maxResultsMap.Lookup(nIndex, storedVal2);

		//Check to see if something was assigned to this color.
		//If a color was not used then nothing was
		//assigned to it and therefore it still has the
		//defaulted values assigned to it at the
		//initialization.
		if (storedVal1 != maxVal || storedVal2 != minVal)
		{
			strColor = acCmds.GetColorFromIndex(nIndex);
			colorData.Format("%-9s   %13.3f  %13.3f", strColor, storedVal1, storedVal2);
			m_ctlResultsList.AddString(colorData);
		}
		else
		{
			//this color was not used.  Just display dashes.
			strColor = acCmds.GetColorFromIndex(nIndex);
			colorData.Format("%-9s               -              -", strColor);
			m_ctlResultsList.AddString(colorData);
		}

	}
	
	return TRUE;  // return TRUE unless you set the focus to a control
	              // EXCEPTION: OCX Property Pages should return FALSE
}






/////////////////////////////////////////////////////////////////
//This method is executed after the user clicks the OK button and
//the dialog is being destroyed.
/////////////////////////////////////////////////////////////////
void CColorModelResults::PostNcDestroy() 
{
	
	CDialog::PostNcDestroy();

	//make sure the current object is destroyed.
	delete this;
}

void CColorModelResults::OnHelp() 
{
	helpService.ShowScreenHelp(IDD);	
}

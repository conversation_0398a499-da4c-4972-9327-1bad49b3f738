// ForteService.h: interface for the CForteService class.
//
//////////////////////////////////////////////////////////////////////

#if !defined(AFX_FORTESERVICE_H__45157ACF_9C93_4ECA_BCEB_59325A2E4269__INCLUDED_)
#define AFX_FORTESERVICE_H__45157ACF_9C93_4ECA_BCEB_59325A2E4269__INCLUDED_

#if _MSC_VER > 1000
#pragma once
#endif // _MSC_VER > 1000

class CForteService  
{
public:
	CForteService();
	virtual ~CForteService();
	int ConnectDatabase(CString userName, CString password, CString database);
	void SendToForteConnection(CStringArray &sendData, CStringArray &recvData,
		CString className, int operationNum);
	int SendToForteConnection2(CStringArray &sendData, CStringArray &recvData,
		CString className, int operationNum);
	int StartForte();
	int StopForte();
	int GetTheNak(int maxTries=50);
};

#endif // !defined(AFX_FORTESERVICE_H__45157ACF_9C93_4ECA_BCEB_59325A2E4269__INCLUDED_)

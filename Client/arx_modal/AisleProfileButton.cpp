// AisleProfileButton.cpp : implementation file
//

#include "stdafx.h"
#include "modal.h"
#include "AisleProfileButton.h"
#include "AisleProfileDimensionPage.h"
#include "UtilityHelper.h"

#ifdef _DEBUG
#define new DEBUG_NEW
#undef THIS_FILE
static char THIS_FILE[] = __FILE__;
#endif

/////////////////////////////////////////////////////////////////////////////
// CAisleProfileButton

extern CUtilityHelper utilityHelper;

CAisleProfileButton::CAisleProfileButton()
{
}

CAisleProfileButton::~CAisleProfileButton()
{
}


BEGIN_MESSAGE_MAP(CAisleProfileButton, CButton)
	//{{AFX_MSG_MAP(CAisleProfileButton)
		// NOTE - the ClassWizard will add and remove mapping macros here.
	//}}AFX_MSG_MAP
END_MESSAGE_MAP()

/////////////////////////////////////////////////////////////////////////////
// CAisleProfileButton message handlers

void CAisleProfileButton::DrawItem(LPDRAWITEMSTRUCT lpDrawItemStruct) 
{
	CDC  frontDC;
	CAisleProfileDimensionPage *pParent = (CAisleProfileDimensionPage *) GetParent();
	CAisleProfile *pAisleProfile = pParent->m_pAisleProfile;

	frontDC.Attach(lpDrawItemStruct->hDC);
	UINT uStyle = DFCS_BUTTONPUSH;
	::DrawFrameControl(lpDrawItemStruct->hDC, &lpDrawItemStruct->rcItem, 
		DFC_BUTTON, uStyle);

	// Create a black pen and select it into current device object //
	CPen  blackPen(PS_SOLID, 3, RGB(0,0,0));
	CPen *prevPen = frontDC.SelectObject(&blackPen);
	// Create a Dashed-pen //
	CPen  dashPen(PS_DASH, 1, RGB(0,0,0));

	RECT r;
	GetClientRect(&r);

	int bkMode = frontDC.SetBkMode(TRANSPARENT);

	//*************************************
	// Draw aisle sides...
	//*************************************
	//left aisle side//
	if (pAisleProfile->m_pLeftSideProfile != NULL)
	{
		//draw the left side rectangular box//
		frontDC.MoveTo(r.right/6, r.bottom/5);
		frontDC.LineTo(5*r.right/6, r.bottom/5);
		frontDC.LineTo(5*r.right/6, 2*r.bottom/5);
		frontDC.LineTo(r.right/6, 2*r.bottom/5);
		frontDC.LineTo(r.right/6, r.bottom/5);
	}
	else
	{
		//draw dash line//
		CPen *tmpPen = frontDC.SelectObject(&dashPen);
		frontDC.MoveTo(r.right/6, 2*r.bottom/5);
		frontDC.LineTo(5*r.right/6, 2*r.bottom/5);
		tmpPen = frontDC.SelectObject(&blackPen);
	}

	//rigth aisle side//
	if (pAisleProfile->m_pRightSideProfile != NULL)
	{
		//draw the right side rectangular box//
		frontDC.MoveTo(r.right/6, 3*r.bottom/5);
		frontDC.LineTo(5*r.right/6, 3*r.bottom/5);
		frontDC.LineTo(5*r.right/6, 4*r.bottom/5);
		frontDC.LineTo(r.right/6, 4*r.bottom/5);
		frontDC.LineTo(r.right/6, 3*r.bottom/5);
	}
	else
	{
		//draw dash line//
		CPen *tmpPen = frontDC.SelectObject(&dashPen);
		frontDC.MoveTo(r.right/6, 3*r.bottom/5);
		frontDC.LineTo(5*r.right/6, 3*r.bottom/5);
		tmpPen = frontDC.SelectObject(&blackPen);
	}

	//*******************************************
	// Draw width information
	//*******************************************

	// Write the text for the width
	CString strAisleWidth;
	if (utilityHelper.IsFloat(pParent->m_AisleSpace)) {
		strAisleWidth.Format("%.2lf", atof(pParent->m_AisleSpace));
		frontDC.TextOut(r.right/2+9,r.bottom/2-7, strAisleWidth);
		frontDC.TextOut(r.right/2-42,r.bottom/2-7, "Width:");
	}

	//draw vertical line//
	frontDC.MoveTo(r.right/2, r.bottom/2 - 14);
	frontDC.LineTo(r.right/2, r.bottom/2 + 14);
	
	// Draw Arrows 
	frontDC.MoveTo(r.right/2, r.bottom/2 - 14);
	frontDC.LineTo(r.right/2-5, r.bottom/2 -9);
	frontDC.MoveTo(r.right/2, r.bottom/2 -14);
	frontDC.LineTo(r.right/2+5, r.bottom/2 -9);
	
	frontDC.MoveTo(r.right/2, r.bottom/2 +14);
	frontDC.LineTo(r.right/2-5, r.bottom/2 +9);
	frontDC.MoveTo(r.right/2, r.bottom/2 +14);
	frontDC.LineTo(r.right/2+5, r.bottom/2 +9);
	
	//*******************************************
	// Draw Space information
	//*******************************************

	// Write the text for the Space
	//left aisle side//
	if (pAisleProfile->m_pLeftSideProfile != NULL)
	{
		CString strAisleSpace;
		if (utilityHelper.IsFloat(pParent->m_LeftSpace)) {
			strAisleSpace.Format("%.2lf", atof(pParent->m_LeftSpace));
			frontDC.TextOut(r.right/2 + 9,r.bottom/5-16, strAisleSpace);
			frontDC.TextOut(r.right/2 - 33,r.bottom/5-16, "Left:");
		}

		//draw vertical line//
		frontDC.MoveTo(r.right/2, r.bottom/5);
		frontDC.LineTo(r.right/2, r.bottom/5 - 14);
	
		// Draw Arrow 
		frontDC.MoveTo(r.right/2, r.bottom/5 - 14);
		frontDC.LineTo(r.right/2-4, r.bottom/5 -9);
		frontDC.MoveTo(r.right/2, r.bottom/5 -14);
		frontDC.LineTo(r.right/2+4, r.bottom/5 -9);
	}
	
	//*******************************************
	// Draw SpacePrime information
	//*******************************************

	// Write the text for the SpacePrime
	//right aisle side//
	if (pAisleProfile->m_pRightSideProfile != NULL)
	{
		CString strAisleSpacePrime;
		if (utilityHelper.IsFloat(pParent->m_RightSpace)) {
			strAisleSpacePrime.Format("%.2lf", atof(pParent->m_RightSpace));
			frontDC.TextOut(r.right/2 + 9,4*r.bottom/5+3, strAisleSpacePrime);
			frontDC.TextOut(r.right/2 - 42,4*r.bottom/5+3, "Right:");
		}

		//draw vertical line//
		frontDC.MoveTo(r.right/2, 4*r.bottom/5);
		frontDC.LineTo(r.right/2, 4*r.bottom/5 +14);
	
		// Draw Arrow 
		frontDC.MoveTo(r.right/2, 4*r.bottom/5 +14);
		frontDC.LineTo(r.right/2-4, 4*r.bottom/5 +9);
		frontDC.MoveTo(r.right/2, 4*r.bottom/5 +14);
		frontDC.LineTo(r.right/2+4, 4*r.bottom/5 +9);
	}

	//*************************************************
	// restore all the attributes of DC
	//*************************************************
	frontDC.SelectObject(prevPen);
	frontDC.SetBkMode(bkMode);

	frontDC.Detach();
}

// ProfileTreePane.cpp : implementation file
//

#include "stdafx.h"
#include "modal.h"
#include "ProfileTreePane.h"
#include "ProfileFrame.h"
#include <dbsymtb.h>

#ifdef _DEBUG
#define new DEBUG_NEW
#undef THIS_FILE
static char THIS_FILE[] = __FILE__;
#endif

extern CProfileFrame *m_ProfileFrame;

/////////////////////////////////////////////////////////////////////////////
// CProfileTreePane dialog

IMPLEMENT_DYNCREATE(CProfileTreePane, CDialog)

CProfileTreePane::CProfileTreePane(CWnd* pParent /*=NULL*/)
	: CDialog(CProfileTreePane::IDD, pParent)
{
	//{{AFX_DATA_INIT(CProfileTreePane)
		// NOTE: the ClassWizard will add member initialization here
	//}}AFX_DATA_INIT
}


void CProfileTreePane::DoDataExchange(CDataExchange* pDX)
{
	CDialog::DoDataExchange(pDX);
	//{{AFX_DATA_MAP(CProfileTreePane)
		// NOTE: the ClassWizard will add DDX and DDV calls here
	//}}AFX_DATA_MAP
}


BEGIN_MESSAGE_MAP(CProfileTreePane, CDialog)
	//{{AFX_MSG_MAP(CProfileTreePane)
	ON_WM_SIZE()
	//}}AFX_MSG_MAP
END_MESSAGE_MAP()

/////////////////////////////////////////////////////////////////////////////
// CProfileTreePane message handlers
BOOL CProfileTreePane::Create(LPCTSTR lpszClassName, LPCTSTR lpszWindowName, DWORD dwStyle, const RECT& rect, CWnd* pParentWnd, UINT nID, CCreateContext* pContext) 
{

	UNREFERENCED_PARAMETER(lpszClassName);
	UNREFERENCED_PARAMETER(lpszWindowName);
	UNREFERENCED_PARAMETER(dwStyle);
	UNREFERENCED_PARAMETER(rect);
	UNREFERENCED_PARAMETER(nID);
	UNREFERENCED_PARAMETER(pContext);

	BOOL bReturn = CDialog::Create(IDD, pParentWnd);

	int id;
	id = m_ProfileFrame->m_Splitter.IdFromRowCol(0, 0);

	if ( bReturn )
		::SetWindowLong ( m_hWnd, GWL_ID, id);
	
	return bReturn;

	return CWnd::Create(lpszClassName, lpszWindowName, dwStyle, rect, pParentWnd, nID, pContext);

}

void CProfileTreePane::OnSize(UINT nType, int cx, int cy) 
{
	CDialog::OnSize(nType, cx, cy);
	
	ads_printf("Drawing Tree\n");
	m_ProfileFrame->Invalidate(TRUE);

	/*
	this->Invalidate(TRUE);
	this->UpdateWindow();
	m_ProfileFrame->m_Splitter.RecalcLayout();
	*/

}

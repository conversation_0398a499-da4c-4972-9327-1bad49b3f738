// InboundQueueRecord.h: interface for the CInboundQueueRecord class.
//
//////////////////////////////////////////////////////////////////////

#if !defined(AFX_INBOUNDQUEUERECORD_H__EA41A15B_2516_4300_90CF_4901A0017B97__INCLUDED_)
#define AFX_INBOUNDQUEUERECORD_H__EA41A15B_2516_4300_90CF_4901A0017B97__INCLUDED_

#if _MSC_VER > 1000
#pragma once
#endif // _MSC_VER > 1000

class CInboundQueueRecord : public CObject  
{
public:
	CInboundQueueRecord();
	CInboundQueueRecord(const CInboundQueueRecord& other);
	CInboundQueueRecord& operator=(const CInboundQueueRecord& other);

	CString Stream();

	virtual ~CInboundQueueRecord();

	int m_FeedId;
	int m_BatchId;
	int m_LineNumber;
	int m_InterfaceType;
	int m_Action;

	int m_EndOfBatch;
	int m_EndOfFeed;

	CString m_WMSId;
	CString m_WMSDetailId;

	void *m_pRecord;

};

#endif // !defined(AFX_INBOUNDQUEUERECORD_H__EA41A15B_2516_4300_90CF_4901A0017B97__INCLUDED_)

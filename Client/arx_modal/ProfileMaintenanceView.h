#if !defined(AFX_PROFILEMAINTENANCEVIEW_H__C08CF92E_26CB_4211_A964_682726FB5501__INCLUDED_)
#define AFX_PROFILEMAINTENANCEVIEW_H__C08CF92E_26CB_4211_A964_682726FB5501__INCLUDED_

#if _MSC_VER > 1000
#pragma once
#endif // _MSC_VER > 1000
// ProfileMaintenanceView.h : header file
//
#include <afxcview.h>
#include "ProfileMaintenanceSheet.h"

/////////////////////////////////////////////////////////////////////////////
// CProfileMaintenanceView view

class CProfileMaintenanceView : public CListView
{
protected:
	CProfileMaintenanceView();           // protected constructor used by dynamic creation
	DECLARE_DYNCREATE(CProfileMaintenanceView)

// Attributes
public:
	CProfileMaintenanceSheet *m_pSheet;
// Operations
public:

// Overrides
	// ClassWizard generated virtual function overrides
	//{{AFX_VIRTUAL(CProfileMaintenanceView)
	protected:
	virtual void OnDraw(CDC* pDC);
	virtual void OnUpdate(CView* pSender, LPARAM lHint, CObject* pHint);
	//}}AFX_VIRTUAL

// Implementation
protected:
	virtual ~CProfileMaintenanceView();

#ifdef _DEBUG
	virtual void AssertValid() const;
	virtual void Dump(CDumpContext& dc) const;
#endif

	// Generated message map functions
protected:
	//{{AFX_MSG(CProfileMaintenanceView)
	afx_msg int OnCreate(LPCREATESTRUCT lpCreateStruct);
	afx_msg void OnSize(UINT nType, int cx, int cy);
	afx_msg void OnDestroy();
	//}}AFX_MSG
	DECLARE_MESSAGE_MAP()
};

/////////////////////////////////////////////////////////////////////////////

//{{AFX_INSERT_LOCATION}}
// Microsoft Visual C++ will insert additional declarations immediately before the previous line.

#endif // !defined(AFX_PROFILEMAINTENANCEVIEW_H__C08CF92E_26CB_4211_A964_682726FB5501__INCLUDED_)

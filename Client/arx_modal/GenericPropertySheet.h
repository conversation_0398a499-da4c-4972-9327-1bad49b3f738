#if !defined(AFX_GENERICPROPERTYSHEET_H__37BA9CCC_D532_4FA9_977E_3227FDCEC86A__INCLUDED_)
#define AFX_GENERICPROPERTYSHEET_H__37BA9CCC_D532_4FA9_977E_3227FDCEC86A__INCLUDED_

#if _MSC_VER > 1000
#pragma once
#endif // _MSC_VER > 1000
// GenericPropertySheet.h : header file
//

/////////////////////////////////////////////////////////////////////////////
// CGenericPropertySheet

class CGenericPropertySheet : public CPropertySheet
{
	DECLARE_DYNAMIC(CGenericPropertySheet)

// Construction
public:
	CGenericPropertySheet();
	CGenericPropertySheet(UINT nIDCaption, CWnd* pParentWnd = NULL, UINT iSelectPage = 0);
	CGenericPropertySheet(LPCTSTR pszCaption, CWnd* pParentWnd = NULL, UINT iSelectPage = 0);

// Attributes
public:

// Operations
public:

// Overrides
	// ClassWizard generated virtual function overrides
	//{{AFX_VIRTUAL(CGenericPropertySheet)
	public:
	virtual BOOL OnInitDialog();
	//}}AFX_VIRTUAL

// Implementation
public:
	BOOL m_AllowUpdate;
	virtual ~CGenericPropertySheet();

	// Generated message map functions
protected:
	//{{AFX_MSG(CGenericPropertySheet)
	afx_msg BOOL OnHelpInfo(HELPINFO* pHelpInfo);
	afx_msg UINT OnGetDlgCode();
	//}}AFX_MSG
	DECLARE_MESSAGE_MAP()
};

/////////////////////////////////////////////////////////////////////////////

//{{AFX_INSERT_LOCATION}}
// Microsoft Visual C++ will insert additional declarations immediately before the previous line.

#endif // !defined(AFX_GENERICPROPERTYSHEET_H__37BA9CCC_D532_4FA9_977E_3227FDCEC86A__INCLUDED_)

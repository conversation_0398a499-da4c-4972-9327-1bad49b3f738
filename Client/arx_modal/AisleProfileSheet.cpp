// AisleProfileSheet.cpp : implementation file
//

#include "stdafx.h"
#include "modal.h"
#include "AisleProfileSheet.h"

#ifdef _DEBUG
#define new DEBUG_NEW
#undef THIS_FILE
static char THIS_FILE[] = __FILE__;
#endif

/////////////////////////////////////////////////////////////////////////////
// CAisleProfileSheet

IMPLEMENT_DYNAMIC(CAisleProfileSheet, CPropertySheet)

CAisleProfileSheet::CAisleProfileSheet(UINT nIDCaption, CWnd* pParentWnd, UINT iSelectPage)
	:CPropertySheet(nIDCaption, pParentWnd, iSelectPage)
{
}

CAisleProfileSheet::CAisleProfileSheet(LPCTSTR pszCaption, CWnd* pParentWnd, UINT iSelectPage)
	:CPropertySheet(pszCaption, pParentWnd, iSelectPage)
{
	AddPage(&m_SidePage);
	AddPage(&m_DimensionPage);
}

CAisleProfileSheet::~CAisleProfileSheet()
{
}


BEGIN_MESSAGE_MAP(CAisleProfileSheet, CPropertySheet)
	//{{AFX_MSG_MAP(CAisleProfileSheet)
	ON_WM_HELPINFO()
	//}}AFX_MSG_MAP
END_MESSAGE_MAP()

/////////////////////////////////////////////////////////////////////////////
// CAisleProfileSheet message handlers

BOOL CAisleProfileSheet::OnHelpInfo(HELPINFO* pHelpInfo) 
{
	// TODO: Add your message handler code here and/or call default
	
	return CPropertySheet::OnHelpInfo(pHelpInfo);
}

BOOL CAisleProfileSheet::OnNotify(WPARAM wParam, LPARAM lParam, LRESULT* pResult) 
{
	// TODO: Add your specialized code here and/or call the base class
	
	return CPropertySheet::OnNotify(wParam, lParam, pResult);
}

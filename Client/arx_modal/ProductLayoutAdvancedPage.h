#if !defined(AFX_PRODUCTLAYOUTADVANCEDPAGE_H__14B83E74_7745_4608_98A3_7822660159E1__INCLUDED_)
#define AFX_PRODUCTLAYOUTADVANCEDPAGE_H__14B83E74_7745_4608_98A3_7822660159E1__INCLUDED_

#if _MSC_VER > 1000
#pragma once
#endif // _MSC_VER > 1000
// ProductLayoutAdvancedPage.h : header file
//

/////////////////////////////////////////////////////////////////////////////
// CProductLayoutAdvancedPage dialog

class CProductLayoutAdvancedPage : public CPropertyPage
{
	DECLARE_DYNCREATE(CProductLayoutAdvancedPage)

// Construction
public:
	BOOL m_FollowPickPath;
	int m_UtilizationDecrement;
	int m_UtilizationStart;
	double m_OptimizationRatio;
	CProductLayoutAdvancedPage();
	~CProductLayoutAdvancedPage();

// Dialog Data
	//{{AFX_DATA(CProductLayoutAdvancedPage)
	enum { IDD = IDD_LAYOUT_PRODUCTS_ADVANCED };
	CComboBox	m_IgnoreRankingsCtrl;
	CComboBox	m_LogModeCtrl;
	CSliderCtrl	m_FactorSliderCtrl;
	int		m_FactorSlider;
	BOOL	m_IgnoreWeight;
	BOOL	m_Overlap;
	CString	m_LogMode;
	int		m_MaxResults;
	int		m_IgnoreRankings;
	//}}AFX_DATA


// Overrides
	// ClassWizard generate virtual function overrides
	//{{AFX_VIRTUAL(CProductLayoutAdvancedPage)
	public:
	virtual BOOL OnKillActive();
	protected:
	virtual void DoDataExchange(CDataExchange* pDX);    // DDX/DDV support
	virtual BOOL OnNotify(WPARAM wParam, LPARAM lParam, LRESULT* pResult);
	//}}AFX_VIRTUAL

// Implementation
protected:
	// Generated message map functions
	//{{AFX_MSG(CProductLayoutAdvancedPage)
	virtual BOOL OnInitDialog();
	afx_msg BOOL OnHelpInfo(HELPINFO* pHelpInfo);
	//}}AFX_MSG
	DECLARE_MESSAGE_MAP()

};

//{{AFX_INSERT_LOCATION}}
// Microsoft Visual C++ will insert additional declarations immediately before the previous line.

#endif // !defined(AFX_PRODUCTLAYOUTADVANCEDPAGE_H__14B83E74_7745_4608_98A3_7822660159E1__INCLUDED_)

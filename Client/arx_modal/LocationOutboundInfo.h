// LocationOutboundInfo.h: interface for the CLevelProfileInfo class.
//
//////////////////////////////////////////////////////////////////////
#include "UDF.h"

#if !defined(AFX_LOCATIONOUTBOUNDINFO_H__4F6F9327_C0CF_11D4_922E_00400542E36B__INCLUDED_)
#define AFX_LOCATIONOUTBOUNDINFO_H__4F6F9327_C0CF_11D4_922E_00400542E36B__INCLUDED_

#if _MSC_VER > 1000
#pragma once
#endif // _MSC_VER > 1000

class CLevelProfileInfo  
{
public:
	CString GetUDFValue(const CString &udfName);
	void AddUDF(CUDF &pUDF);
	int Parse(CString &line);
	CLevelProfileInfo();
	virtual ~CLevelProfileInfo();

	int m_LevelProfileDBID;
	int m_RelativeLevel;
	int m_BayType;
	int m_IsFloating;
	
	CMapStringToOb m_UDFMap;

};

#endif // !defined(AFX_LOCATIONOUTBOUNDINFO_H__4F6F9327_C0CF_11D4_922E_00400542E36B__INCLUDED_)

class CLocationOutboundInfo  
{
public:
	int Parse(CString &line);
	CLocationOutboundInfo();
	virtual ~CLocationOutboundInfo();

	int m_LevelProfileDBID;
	CString m_Description;
	int m_XCoordinate;
	int m_YCoordinate;
	int m_ZCoordinate;
	int m_HandlingMethod;
	int m_IsSelect;
	int m_IsOverridden;
	long m_SectionDBID;

	
};


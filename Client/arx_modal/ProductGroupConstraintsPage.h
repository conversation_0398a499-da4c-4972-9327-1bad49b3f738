#if !defined(AFX_PRODUCTGROUPCONSTRAINTSPAGE_H__C027A262_0456_11D5_9EC8_00C04FAC149C__INCLUDED_)
#define AFX_PRODUCTGROUPCONSTRAINTSPAGE_H__C027A262_0456_11D5_9EC8_00C04FAC149C__INCLUDED_

#if _MSC_VER > 1000
#pragma once
#endif // _MSC_VER > 1000

#include "ProductGroup.h"	// Added by ClassView
#include "FacilityDataService.h"

class CProductGroupConstraintsPage : public CPropertyPage
{
	DECLARE_DYNCREATE(CProductGroupConstraintsPage)

// Construction
public:
	CProductGroup *m_ProductGroup;
	CProductGroupConstraintsPage();
	~CProductGroupConstraintsPage();

// Dialog Data
	//{{AFX_DATA(CProductGroupConstraintsPage)
	enum { IDD = IDD_PRODUCT_GROUP_LAYOUT_CONSTRAINTS };
	CListCtrl	m_NonExclusiveListCtrl;
	CListCtrl	m_ExclusiveListCtrl;
	CComboBox	m_LevelListCtrl;
	CTreeCtrl	m_TreeCtrl;
	BOOL	m_Exclusive;
	//}}AFX_DATA


// Overrides
	// ClassWizard generate virtual function overrides
	//{{AFX_VIRTUAL(CProductGroupConstraintsPage)
	public:
	virtual void OnOK();
	protected:
	virtual void DoDataExchange(CDataExchange* pDX);    // DDX/DDV support
	virtual BOOL OnNotify(WPARAM wParam, LPARAM lParam, LRESULT* pResult);
	//}}AFX_VIRTUAL

// Implementation
protected:
	// Generated message map functions
	//{{AFX_MSG(CProductGroupConstraintsPage)
	virtual BOOL OnInitDialog();
	afx_msg void OnItemexpandedFacilityTree(NMHDR* pNMHDR, LRESULT* pResult);
	afx_msg void OnDblclkFacilityTree(NMHDR* pNMHDR, LRESULT* pResult);
	afx_msg void OnItemexpandingFacilityTree(NMHDR* pNMHDR, LRESULT* pResult);
	afx_msg void OnSelchangedFacilityTree(NMHDR* pNMHDR, LRESULT* pResult);
	afx_msg void OnAddExclusive();
	afx_msg void OnRemoveExclusive();
	afx_msg void OnAddNonexclusive();
	afx_msg void OnRemoveNonexclusive();
	afx_msg BOOL OnHelpInfo(HELPINFO* pHelpInfo);
	//}}AFX_MSG
	DECLARE_MESSAGE_MAP()

private:
	BOOL CompareConstraints(CProductGroupConstraint &constraint1, CProductGroupConstraint &constraint2);
	int FindItemInControl(CListCtrl *pListCtrl, CProductGroupConstraint &constraint);
	int FindItemInList(CListCtrl *pListCtrl);
	void AddConstraint(BOOL isExclusive);
	BOOL CheckConstraint(CProductGroupConstraint &constraint);
	void DisplayConstraint(CProductGroupConstraint &constraint);
	int LoadConstraints();
	void SetLevelList(int maxLevel);
	int GetItemType(HTREEITEM &hItem);
	void LoadTree();

	CFacilityDataService m_FacilityDataService;
};

//{{AFX_INSERT_LOCATION}}
// Microsoft Visual C++ will insert additional declarations immediately before the previous line.

#endif // !defined(AFX_PRODUCTGROUPCONSTRAINTSPAGE_H__C027A262_0456_11D5_9EC8_00C04FAC149C__INCLUDED_)

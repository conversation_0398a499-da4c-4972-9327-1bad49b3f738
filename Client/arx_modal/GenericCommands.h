// GenericCommands.h: interface for the CGenericCommands class.
//
//////////////////////////////////////////////////////////////////////

#if !defined(AFX_GENERICCOMMANDS_H__9BD33942_0216_4B0A_B30B_E92AC5D70638__INCLUDED_)
#define AFX_GENERICCOMMANDS_H__9BD33942_0216_4B0A_B30B_E92AC5D70638__INCLUDED_

#include "Commands.h"

#if _MSC_VER > 1000
#pragma once
#endif // _MSC_VER > 1000

class CGenericCommands : public CCommands
{
public:
	CGenericCommands();
	virtual ~CGenericCommands();
	static void RegisterCommands();
	static void InterfaceMap();
};	

#endif // !defined(AFX_GENERICCOMMANDS_H__9BD33942_0216_4B0A_B30B_E92AC5D70638__INCLUDED_)

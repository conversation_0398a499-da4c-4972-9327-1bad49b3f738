// WMS.h: interface for the CWMS class.
//
//////////////////////////////////////////////////////////////////////

#if !defined(AFX_WMS_H__B62ACD98_21F9_4A0E_B508_8C4312B35F4A__INCLUDED_)
#define AFX_WMS_H__B62ACD98_21F9_4A0E_B508_8C4312B35F4A__INCLUDED_

#if _MSC_VER > 1000
#pragma once
#endif // _MSC_VER > 1000

#include "WMSMap.h"

class CWMS : public CObject  
{
public:
	int Parse(CString &line);
	CWMS();
	CWMS(const CWMS& other);
	CWMS& operator=(const CWMS& other);

	virtual ~CWMS();

	int m_WMSDBId;
	CString m_Name;
	CString m_Description;
	CString m_WMSId;

	int m_GroupDBId;
	CString m_GroupName;

	CTypedPtrArray<CObArray, CWMSMap*> m_MapList;

};

#endif // !defined(AFX_WMS_H__B62ACD98_21F9_4A0E_B508_8C4312B35F4A__INCLUDED_)

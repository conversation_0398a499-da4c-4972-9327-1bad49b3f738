// UDFCommands.h: interface for the CUDFCommands class.
//
//////////////////////////////////////////////////////////////////////

#if !defined(AFX_UDFCOMMANDS_H__B3FFEBFE_4B99_4FBC_8FBE_AE630A6A0F28__INCLUDED_)
#define AFX_UDFCOMMANDS_H__B3FFEBFE_4B99_4FBC_8FBE_AE630A6A0F28__INCLUDED_

#include "Commands.h"

#if _MSC_VER > 1000
#pragma once
#endif // _MSC_VER > 1000

class CUDFCommands : public CCommands
{
public:
	CUDFCommands();
	virtual ~CUDFCommands();
	
	static void RegisterCommands();
	static void UDFMaintenance();

};

#endif // !defined(AFX_UDFCOMMANDS_H__B3FFEBFE_4B99_4FBC_8FBE_AE630A6A0F28__INCLUDED_)

#if !defined(AFX_FACILITYDIALOG_H__B1881143_D14D_11D3_B00C_0080C7FFDED7__INCLUDED_)
#define AFX_FACILITYDIALOG_H__B1881143_D14D_11D3_B00C_0080C7FFDED7__INCLUDED_

#if _MSC_VER > 1000
#pragma once
#endif // _MSC_VER > 1000
// FacilityDialog.h : header file
//
#include "resource.h"
//#include "SSACStringArray.h"	// Added by ClassView
/////////////////////////////////////////////////////////////////////////////
// CFacilityDialog dialog

class CFacilityDialog : public CDialog
{
// Construction
public:
	CString m_SaveAsOptions;
	CStringArray m_SizeList;
	CStringArray m_NotesList;
	CStringArray m_OpenedByList;
	CStringArray m_DateList;
	CToolTipCtrl m_ToolTip;
	void LoadToolTips();
	CBitmap m_Bitmap;
	void LoadButtonBitmaps();
	void FillListCtrl();
	void RenewListCtrl(DWORD dwStyle, BOOL bSetBits);
	CString m_PrevFacilityName;
	~CFacilityDialog();
	CImageList *m_pImageListSmall;
	CImageList *m_pImageListLarge;
	int m_ItemSelected;
	int m_FacilityIdList[255];
	int m_FacilityId;
	CStringArray m_FacilityList;
	int m_Mode;
	CFacilityDialog(CWnd* pParent = NULL);   // standard constructor

// Dialog Data
	//{{AFX_DATA(CFacilityDialog)
	enum { IDD = IDD_FACILITY };
	CEdit	m_NotesCtrl;
	CEdit	m_FacilityNameCtrl;
	CListCtrl	m_FacilityListCtrl;
	CString	m_FacilityName;
	CString	m_FacilityNotes;
	//}}AFX_DATA


// Overrides
	// ClassWizard generated virtual function overrides
	//{{AFX_VIRTUAL(CFacilityDialog)
	public:
	virtual BOOL PreTranslateMessage(MSG* pMsg);
	protected:
	virtual void DoDataExchange(CDataExchange* pDX);    // DDX/DDV support
	//}}AFX_VIRTUAL

// Implementation
protected:

	// Generated message map functions
	//{{AFX_MSG(CFacilityDialog)
	afx_msg void OnChangeFacilityName();
	virtual void OnOK();
	virtual BOOL OnInitDialog();
	afx_msg void OnClickFacilityList(NMHDR* pNMHDR, LRESULT* pResult);
	afx_msg void OnDblclkFacilityList(NMHDR* pNMHDR, LRESULT* pResult);
	afx_msg void OnItemchangedFacilityList(NMHDR* pNMHDR, LRESULT* pResult);
	afx_msg void OnLargeIcons();
	afx_msg void OnList();
	afx_msg void OnSmallIcons();
	afx_msg void OnDetails();
	afx_msg void OnChangeFacilityNotes();
	afx_msg void OnAutoCheck();
	afx_msg BOOL OnHelpInfo(HELPINFO* pHelpInfo);
	afx_msg void OnHelp();
	//}}AFX_MSG
	DECLARE_MESSAGE_MAP()
private:
	int MatchWithCase(CString &str);
	CString m_AutoFacility;
	CString m_AutoDatabase;
	CString m_OrigNotes;
};

//{{AFX_INSERT_LOCATION}}
// Microsoft Visual C++ will insert additional declarations immediately before the previous line.

#endif // !defined(AFX_FACILITYDIALOG_H__B1881143_D14D_11D3_B00C_0080C7FFDED7__INCLUDED_)

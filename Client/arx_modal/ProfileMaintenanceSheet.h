#if !defined(AFX_PROFILEMAINTENANCESHEET_H__DA0E6FD3_FFF6_436A_ADF1_4F1CD4D47201__INCLUDED_)
#define AFX_PROFILEMAINTENANCESHEET_H__DA0E6FD3_FFF6_436A_ADF1_4F1CD4D47201__INCLUDED_

#if _MSC_VER > 1000
#pragma once
#endif // _MSC_VER > 1000
// ProfileMaintenanceSheet.h : header file
//
#include "ProfilePage.h"

/////////////////////////////////////////////////////////////////////////////
// CProfileMaintenanceSheet

class CProfileMaintenanceSheet : public CPropertySheet
{
	DECLARE_DYNAMIC(CProfileMaintenanceSheet)

// Construction
public:
	CProfileMaintenanceSheet(UINT nIDCaption, CWnd* pParentWnd = NULL, UINT iSelectPage = 0);
	CProfileMaintenanceSheet(LPCTSTR pszCaption, CWnd* pParentWnd = NULL, UINT iSelectPage = 0);

// Attributes
public:
	CProfilePage m_Page;

// Operations
public:

// Overrides
	// ClassWizard generated virtual function overrides
	//{{AFX_VIRTUAL(CProfileMaintenanceSheet)
	public:
	protected:
	virtual void PostNcDestroy();
	//}}AFX_VIRTUAL

// Implementation
public:
	virtual ~CProfileMaintenanceSheet();

	// Generated message map functions
protected:
	//{{AFX_MSG(CProfileMaintenanceSheet)
		// NOTE - the ClassWizard will add and remove member functions here.
	//}}AFX_MSG
	DECLARE_MESSAGE_MAP()
};

/////////////////////////////////////////////////////////////////////////////

//{{AFX_INSERT_LOCATION}}
// Microsoft Visual C++ will insert additional declarations immediately before the previous line.

#endif // !defined(AFX_PROFILEMAINTENANCESHEET_H__DA0E6FD3_FFF6_436A_ADF1_4F1CD4D47201__INCLUDED_)

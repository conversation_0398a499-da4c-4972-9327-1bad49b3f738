#if !defined(AFX_BAYPROFILEHANDLINGPROPERTIES_H__FE3B73D0_3CF7_42D7_8003_2CDB6E940A7A__INCLUDED_)
#define AFX_BAYPROFILEHANDLINGPROPERTIES_H__FE3B73D0_3CF7_42D7_8003_2CDB6E940A7A__INCLUDED_

#if _MSC_VER > 1000
#pragma once
#endif // _MSC_VER > 1000
// BayProfileHandlingProperties.h : header file
//
#include "BayProfile.h"
/////////////////////////////////////////////////////////////////////////////
// CBayProfileHandlingProperties dialog

class CBayProfileHandlingProperties : public CDialog
{
// Construction
public:
	int m_CurrentHandlingIdx;
	int m_WorkType;
	CBayProfile * m_pBayProfile;
	int m_CurrentLevel;
	CBayProfileHandlingProperties(CWnd* pParent = NULL);   // standard constructor

// Dialog Data
	//{{AFX_DATA(CBayProfileHandlingProperties)
	enum { IDD = IDD_BAY_PROFILE_HANDLING_PROPERTIES };
	CComboBox	m_WorkTypeListCtrl;
	CString	m_Cube;
	CString	m_Fixed;
	CString	m_Variable;
	//}}AFX_DATA


// Overrides
	// ClassWizard generated virtual function overrides
	//{{AFX_VIRTUAL(CBayProfileHandlingProperties)
	protected:
	virtual void DoDataExchange(CDataExchange* pDX);    // DDX/DDV support
	//}}AFX_VIRTUAL

// Implementation
protected:

	// Generated message map functions
	//{{AFX_MSG(CBayProfileHandlingProperties)
	virtual BOOL OnInitDialog();
	virtual void OnOK();
	afx_msg void OnHelp();
	afx_msg BOOL OnHelpInfo(HELPINFO* pHelpInfo);
	//}}AFX_MSG
	DECLARE_MESSAGE_MAP()
private:
	BOOL ValidateNumeric(int type, int fieldId, const CString &fieldName, CString &fieldValue);
};

//{{AFX_INSERT_LOCATION}}
// Microsoft Visual C++ will insert additional declarations immediately before the previous line.

#endif // !defined(AFX_BAYPROFILEHANDLINGPROPERTIES_H__FE3B73D0_3CF7_42D7_8003_2CDB6E940A7A__INCLUDED_)

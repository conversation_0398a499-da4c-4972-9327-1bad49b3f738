// Confirmation.h: interface for the CConfirmation class.
//
//////////////////////////////////////////////////////////////////////

#if !defined(AFX_CONFIRMATION_H__88F7FD67_F5F3_4B27_AEFD_07142E0D18DC__INCLUDED_)
#define AFX_CONFIRMATION_H__88F7FD67_F5F3_4B27_AEFD_07142E0D18DC__INCLUDED_

#if _MSC_VER > 1000
#pragma once
#endif // _MSC_VER > 1000

class CConfirmation : public CObject  
{
public:
	CConfirmation();
	virtual ~CConfirmation();
	
	int m_InterfaceType;
	int m_BatchId;
	int m_ReasonCode;
	CString m_ReasonText;

};

#endif // !defined(AFX_CONFIRMATION_H__88F7FD67_F5F3_4B27_AEFD_07142E0D18DC__INCLUDED_)

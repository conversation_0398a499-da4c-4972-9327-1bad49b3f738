// UDFCommands.cpp: implementation of the CUDFCommands class.
//
//////////////////////////////////////////////////////////////////////

#include "stdafx.h"
#include "modal.h"
#include "UDFCommands.h"
#include "UDFHelper.h"
#include "ControlService.h"

#ifdef _DEBUG
#undef THIS_FILE
static char THIS_FILE[]=__FILE__;
#define new DEBUG_NEW
#endif

extern CControlService controlService;

//////////////////////////////////////////////////////////////////////
// Construction/Destruction
//////////////////////////////////////////////////////////////////////

CUDFCommands::CUDFCommands()
{

}

CUDFCommands::~CUDFCommands()
{

}

void CUDFCommands::RegisterCommands()
{
	// UDF
	acedRegCmds->addCommand( "SLOTJAVA", "UDFMAINTENANCE", "UDFMAINTENANCE",
		ACRX_CMD_MODAL, &CUDFCommands::UDFMaintenance );
	
	acedRegCmds->addCommand( "SLOTJAVA", "PROFUDFMAINT", "PROFUDFMAINT",
		ACRX_CMD_MODAL, &CUDFCommands::UDFMaintenance );
}

void CUDFCommands::UDFMaintenance()
{
	if (! controlService.ValidateCommand(CCommands::openFacilityMode))
		return;

	CUDFHelper helper;

	helper.UDFMaintenance();

	return;
}
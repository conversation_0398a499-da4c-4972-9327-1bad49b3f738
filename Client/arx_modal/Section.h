// Section.h: interface for the CSection class.
//
//////////////////////////////////////////////////////////////////////

#if !defined(AFX_SECTION_H__DF0C5DA7_17DD_4A22_8744_2FB7D7CCD42E__INCLUDED_)
#define AFX_SECTION_H__DF0C5DA7_17DD_4A22_8744_2FB7D7CCD42E__INCLUDED_

#if _MSC_VER > 1000
#pragma once
#endif // _MSC_VER > 1000

#include "FacilityElement.h"
#include "3DPoint.h"
#include "Aisle.h"
#include "Hotspot.h"

class CSection : public CFacilityElement  
{
public:
	CSection();
	virtual ~CSection();
	
	C3DPoint m_Coordinates;
	CString m_Mask;							// LocationIdMask
	double m_AvgReplenishmentDistance;		// ReplenAvgDist
	double m_ForkTravelTimeVariable;		// ForkDistVar
	double m_ForkTravelTimeFixed;			// ForkDistFixed
	double m_SelectTravelTimeVariable;		// SelectDistVar
	double m_SelectTravelTimeFixed;			// SelectDistFixed
	double m_ForkLaborRate;					// ForkLaborRate
	double m_SelectLaborRate;				// SelectLaborRate
	double m_AvgCubePerPutawayTrip;			// AvgCubePerTrip
	int m_AvgPutawayOrderQuantity;			// AvgOrderQuantity
	int m_AvgPutawayContainerQuantity;		// ContainerQuantity
	int m_AvgOrderCount;					// OrderCount
	BOOL m_ApplyBrokenOrder;				// ApplyBrokenOrder
	double m_TotalSelectDistance;			// TotalSelDistance
	double m_AvgPalletsPerPutawayTrip;		// PalletsPerPtwyTrip
	double m_ForkInsertionTime;				// InsertForkTravel
	double m_ForkExtractionTime;			// PickupForkTravel
	double m_StockerTravelTimeFixed;		// StockerDistFixed
	double m_StockerTravelTimeVariable;		// StockerDistVar
	double m_StockerLaborRate;				// StockerLaborRate

	CHotspot m_PutawayHotspot;
	CHotspot m_SelectionHotspot;

	// New fields
	CString m_WMSId;						// overrides facility
	CString m_WMSDetailId;					// overrides facility

	CTypedPtrArray<CObArray, CAisle*> m_AisleArray;
	CMap<long, long, CAisle*, CAisle*> m_AisleMapById;
	CMap<CString, LPCSTR, CAisle*, CAisle*> m_AisleMapByName;

};

#endif // !defined(AFX_SECTION_H__DF0C5DA7_17DD_4A22_8744_2FB7D7CCD42E__INCLUDED_)

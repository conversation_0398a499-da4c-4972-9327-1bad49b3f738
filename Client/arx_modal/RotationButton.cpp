// RotationButton.cpp : implementation file
//

#include "stdafx.h"
#include "modal.h"
#include "RotationButton.h"
#include "ObjectPlaceDialog.h"
#include "UtilityHelper.h"

#include <math.h>

#ifdef _DEBUG
#define new DEBUG_NEW
#undef THIS_FILE
static char THIS_FILE[] = __FILE__;
#endif

extern CUtilityHelper utilityHelper;
/////////////////////////////////////////////////////////////////////////////
// CRotationButton

CRotationButton::CRotationButton()
{
}

CRotationButton::~CRotationButton()
{
}


BEGIN_MESSAGE_MAP(CRotationButton, CButton)
	//{{AFX_MSG_MAP(CRotationButton)
		// NOTE - the ClassWizard will add and remove mapping macros here.
	//}}AFX_MSG_MAP
END_MESSAGE_MAP()

/////////////////////////////////////////////////////////////////////////////
// CRotationButton message handlers

void CRotationButton::DrawItem(LPDRAWITEMSTRUCT lpDrawItemStruct) 
{
	UINT uStyle = DFCS_BUTTONPUSH;

   ASSERT(lpDrawItemStruct->CtlType == ODT_BUTTON);

   CObjectPlaceDialog *parent = (CObjectPlaceDialog *)GetParent();

   // If drawing selected, add the pushed style to DrawFrameControl.
 //  if (lpDrawItemStruct->itemState & ODS_SELECTED)
 //   uStyle |= DFCS_PUSHED;

   // Draw the button frame.
   ::DrawFrameControl(lpDrawItemStruct->hDC, &lpDrawItemStruct->rcItem, 
 		DFC_BUTTON, uStyle);

	CDC drawnDC;
	drawnDC.Attach(lpDrawItemStruct->hDC);
	RECT r;

	GetClientRect(&r);

	//Need to create a font with escapement of 
	//90 degrees - so that we can write upwards
	CFont  drawingFont;
	BOOL bFontCreated = 
		drawingFont.CreateFont(10, 0,900,900,FW_THIN, 0,0,0, ANSI_CHARSET, OUT_DEFAULT_PRECIS,
				CLIP_DEFAULT_PRECIS, DEFAULT_QUALITY, DEFAULT_PITCH|FF_DONTCARE, "Arial");
	if (!bFontCreated) {
		drawnDC.Detach();
		return;
	}
	drawingFont.DeleteObject();

	// create red pen for object and black pen for axes
	CPen  redPen(PS_SOLID, 20, RGB(255,0,0));
	CPen  blackPen(PS_SOLID, 10, RGB(0,0,0));
	CPen  *prevPen = drawnDC.SelectObject(&blackPen);

	int height, width, wndHeight, wndWidth;
	CString temp;
	int x, y;
	float angle;
	double pi = 22.0/7.0;
	CSize wSize, vSize;
	double xRatio, yRatio;
	
	double rotation, xcoordinate, ycoordinate;
	if (utilityHelper.IsNumeric(parent->m_Rotation))
		rotation = atoi(parent->m_Rotation);
	else
		rotation = 90;

	if (utilityHelper.IsNumeric(parent->m_XCoordinate))
		xcoordinate = atoi(parent->m_XCoordinate);
	else
		xcoordinate = 0;

	if (utilityHelper.IsNumeric(parent->m_YCoordinate))
		ycoordinate = atoi(parent->m_YCoordinate);
	else
		ycoordinate = 0;

	// Convert the angle to radians
	angle = (float)pi/(float)180.0 * (float)rotation;
	
	// Reset the origin to be the middle of the window
	// This is in device units so do it before changing the map mode
	// or make sure to use the client rect units, not the converted logical coordinates
	wndWidth = r.right-r.left;
	wndHeight = r.bottom-r.top;
	drawnDC.SetViewportOrg(wndWidth/2, wndHeight/2);

	// Set the map mode to twips (logical coord = 1/20 of a point, x - right=positive, y - up=positive)
	int mapMode = drawnDC.GetMapMode();
	drawnDC.SetMapMode(MM_TWIPS);

	// Calculate the ratio of logical coordinates to device coordinates
	wSize = drawnDC.GetWindowExt();
	vSize = drawnDC.GetViewportExt();
	xRatio = wSize.cx/vSize.cx;
	yRatio = wSize.cy/vSize.cy;

	// Convert the device coordinates to logical
	wndWidth = (int)((r.right - r.left)*xRatio);
	wndHeight = (int)((r.top - r.bottom)*yRatio);
	
	// Draw the crosshairs
	prevPen = drawnDC.SelectObject(&blackPen);

	// Make the crosshairs 7/8 (7/16*2) of the window size
	height = wndHeight*3/8;
	width = wndWidth*3/8;

	int arrowSize = (int)(5.0 * xRatio);	// arrow is 5 device units 

	// Draw the left x-axis arrow
	drawnDC.MoveTo(-width+arrowSize, 0+arrowSize);
	drawnDC.LineTo(-width, 0);
	drawnDC.LineTo(-width+arrowSize, 0-arrowSize);
	drawnDC.MoveTo(-width, 0);

	// Draw the x-axis
	drawnDC.LineTo(width, 0);
	
	// Draw the right x-axis arrow
	drawnDC.LineTo(width-arrowSize, 0+arrowSize);
	drawnDC.MoveTo(width, 0);
	drawnDC.LineTo(width-arrowSize, 0-arrowSize);

	// Draw the y-axis bottom arrow
	drawnDC.MoveTo(-arrowSize, -height+arrowSize);
	drawnDC.LineTo(0, -height);
	drawnDC.LineTo(arrowSize, -height+arrowSize);

	// Draw the y-axis
	drawnDC.MoveTo(0, -height);
	drawnDC.LineTo(0, height);

	// Draw the y-axis top arrow
	drawnDC.MoveTo(-arrowSize, height-arrowSize);
	drawnDC.LineTo(0, height);
	drawnDC.LineTo(arrowSize, height-arrowSize);

	
	// Draw the object
	drawnDC.SelectObject(&redPen);

	if (parent->m_ObjectType == CObjectPlaceDialog::AddAisle) {	// must be an aisle

		width = wndWidth*11/32;	// make the aisle length 3/8 the window size
		height = wndHeight/8;	// make the aisle width 1/8 the window size
		
		// Move to the middle (which is now the origin)
		x = y = 0;
		RotatePoint(angle, &x, &y);
		drawnDC.MoveTo(x, y);
		
		// Draw the top side
		x = width;
		y = 0;
		RotatePoint(angle, &x, &y);
		drawnDC.LineTo(x, y);
		
		// Draw the right side
		y = -height;
		x = width;
		RotatePoint(angle, &x, &y);
		drawnDC.LineTo(x, y);
		
		// Draw the bottom side
		x = 0;
		y = -height;
		RotatePoint(angle, &x, &y);
		drawnDC.LineTo(x, y);
		
		// Draw the left side
		x = 0;
		y = 0;
		RotatePoint(angle, &x, &y);
		drawnDC.LineTo(x, y);
		
	}
	else {	// draw everything else as a circle
		CRect r2;
		width = wndWidth/8;
		r2.SetRect(-width, width, width, -width);
		//drawnDC.Ellipse(&r2);
		drawnDC.Arc(r2, r2.BottomRight(), r2.BottomRight());

	}

	// Show the coordinates
	CFont font, *oldFont;
	CSize size;
	// There may be a weird problem with creating a font here.
	// I've tested 60 and 100 point on both R14 and 2000i and seem
	// to work, but I think I did get an exception on it once.
	// Okay, I tried using CreatePointFont but sometimes it doesn't work and 
	// I can't figure out why, so I switched to using CreateFont.
	try {
		// We are in twips mode, so a logic unit is 1/20 of a point so multiply by 20 to get
		// the right point size
		if (font.CreateFont(8*20, 0,0,0,FW_THIN, 0,0,0, ANSI_CHARSET, OUT_DEFAULT_PRECIS,
			CLIP_DEFAULT_PRECIS, DEFAULT_QUALITY, DEFAULT_PITCH|FF_DONTCARE, NULL)) {

			oldFont = drawnDC.SelectObject(&font);
			
			temp.Format("(%.0f,%.0f)", xcoordinate, ycoordinate);
			
			size = drawnDC.GetTextExtent(temp);
			drawnDC.TextOut(-size.cx/2, size.cy/2, temp);
			
			height = wndHeight*3/8;
			width = wndWidth*3/8;
		}
		else {
			drawnDC.Detach();
			return;
		}

		drawnDC.SelectObject(oldFont);
		font.DeleteObject();
		if (font.CreateFont(10*20, 0,0,0,FW_THIN, 0,0,0, ANSI_CHARSET, OUT_DEFAULT_PRECIS,
			CLIP_DEFAULT_PRECIS, DEFAULT_QUALITY, DEFAULT_PITCH|FF_DONTCARE, NULL)) {
			temp.Format("Not to scale");
			size = drawnDC.GetTextExtent(temp);
			drawnDC.TextOut((int)(-wndWidth/2+xRatio), (int)(wndHeight/2+xRatio), temp);
			
			temp = "X";
			size = drawnDC.GetTextExtent(temp);
			drawnDC.TextOut(width+size.cx, 0+size.cy/2, temp);
			
			temp = "Y";
			size = drawnDC.GetTextExtent(temp);
			drawnDC.TextOut(-size.cx/2, height+size.cy, temp);
		}
		else {
			drawnDC.Detach();
			return;
		}
	}
	catch (...) {
		// error creating fonts
	}

	drawnDC.SelectObject(prevPen);
	drawnDC.SelectObject(oldFont);
	font.DeleteObject();

	drawnDC.Detach();
	
}

void CRotationButton::RotatePoint(float angle, int *x, int *y)
{
	double origX, origY, angleFloat;
	double newX, newY;
	origX = (double)*x;
	origY = (double)*y;
	angleFloat = (double)angle;

	newX = origX * cos(angleFloat) - origY * sin(angleFloat);
	newY = origX * sin(angleFloat) + origY * cos(angleFloat);

	*x = (int)newX;
	*y = (int)newY;

	return;
}

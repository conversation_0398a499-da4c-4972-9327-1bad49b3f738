// ControlService.cpp: implementation of the CControlService class.
//
//////////////////////////////////////////////////////////////////////

#include "stdafx.h"
#include "modal.h"
#include "ControlService.h"
#include "UtilityHelper.h"
#include "ForteService.h"
#include "DataAccessService.h"
#include "BTreeHelper.h"
#include "ElementMaintenanceHelper.h"
#include "TreeElement.h"
#include "HelpService.h"
#include "FacilityDataService.h"
#include "LocationNumberingService.h"
#include "SolutionDataService.h"
#include "SideProfileDataService.h"
#include "IntegrationDataService.h"
#include "ssa_exception.h"
#include "BayProfileDataService.h"
#include "AisleProfileDataService.h"
#include "Commands.h"
#include "FacilityHelper.h"
#include "ProductDataService.h"

#ifdef _DEBUG
#undef THIS_FILE
static char THIS_FILE[]=__FILE__;
#define new DEBUG_NEW
#endif

//////////////////////////////////////////////////////////////////////
// Construction/Destruction
//////////////////////////////////////////////////////////////////////

const CString ApplicationRegistryPath = "SSA Global\\Optimize";
int BayNum=1;
int numItemsProcessed;
CString AutoLoginParameters = "";
char slotDir[256];

CHelpService helpService;
CUtilityHelper utilityHelper;
CForteService forteService;
CControlService controlService;
CDataAccessService dataAccessService;
CBTreeHelper bTreeHelper;
TreeElement changesTree;
CElementMaintenanceHelper elementMaintenanceHelper;
CLocationNumberingService numberingService;
CFacilityDataService facilityDataService;
CSolutionDataService solutionDataService;
CSideProfileDataService sideProfileDataService;
CIntegrationDataService integrationDataService;
CBayProfileDataService bayProfileDataService;
CAisleProfileDataService aisleProfileDataService;
CFacilityHelper facilityHelper;
CProductDataService productDataService;

#define MAXREGSIZE 4096

CControlService::CControlService()
{	
	m_pBayProfileListDialog = NULL;
	m_pSideProfileListDialog = NULL;
	m_pAisleProfileListDialog = NULL;
	m_Mode = -1;
	m_Divisor = 12 * 12 * 12;
}

CControlService::~CControlService()
{
	if (m_pBayProfileListDialog != NULL)
		delete m_pBayProfileListDialog;

	if (m_pSideProfileListDialog != NULL)
		delete m_pSideProfileListDialog;	

	if (m_pAisleProfileListDialog != NULL)
		delete m_pAisleProfileListDialog;	
}

int CControlService::Initialize()
{
	m_ClientHome = GetApplicationData("Home");

	if (GetApplicationData("Debug") != "")
		m_Debug = TRUE;
	else
		m_Debug = FALSE;
	
	m_MeasurementType = Imperial;
	CString units = GetApplicationData("Units");
	if (units == "Metric") {
		m_Divisor = 100*100*100;
		m_MeasurementType = Metric;
	}
	else
		m_Divisor = 12*12*12;

	m_DefaultWMS = 0;
	if (GetApplicationData("DrawBayArrow") == "1")
		m_DrawBayArrow = TRUE;
	else
		m_DrawBayArrow = FALSE;

	if (m_pBayProfileListDialog != NULL)
		delete m_pBayProfileListDialog;

	if (m_pSideProfileListDialog != NULL)
		delete m_pSideProfileListDialog;	

	if (m_pAisleProfileListDialog != NULL)
		delete m_pAisleProfileListDialog;	

	return 0;
}

CString CControlService::GetApplicationData(const CString &key, const CString &subKey)
{
	CString regPath, data;
	HKEY hRegKey;
	DWORD dwReturnLength;
	DWORD dwType = REG_SZ;
	int rc;

	data = "";

	regPath = "Software\\";
	regPath += ApplicationRegistryPath;
	if (subKey != "") {
		regPath += "\\";
		regPath += subKey;
	}

	if (RegOpenKeyEx(HKEY_LOCAL_MACHINE, regPath, 0, KEY_READ, &hRegKey) != ERROR_SUCCESS) {
		return data;
	}

	dwReturnLength = 0;

	rc = RegQueryValueEx(hRegKey, key, NULL, &dwType, (LPBYTE)data.GetBuffer(0), &dwReturnLength);
	data.ReleaseBuffer();
	if (rc == ERROR_MORE_DATA) {
		rc = RegQueryValueEx(hRegKey, key, NULL, &dwType, (LPBYTE)data.GetBuffer(dwReturnLength), &dwReturnLength);
		data.ReleaseBuffer();
		if (rc != ERROR_SUCCESS)
			data = "";
	}

	RegCloseKey(hRegKey);

	return data;
}

CString CControlService::GetRegistryData(const CString &key, const CString &value)
{
	CString regPath, data;
	HKEY hRegKey;
	DWORD dwReturnLength;
	DWORD dwType = REG_SZ;
	int rc;

	data = "";

	regPath = key;

	if (RegOpenKeyEx(HKEY_LOCAL_MACHINE, regPath, 0, KEY_READ, &hRegKey) != ERROR_SUCCESS) {
		return data;
	}

	dwReturnLength = 0;

	rc = RegQueryValueEx(hRegKey, value, NULL, &dwType, (LPBYTE)data.GetBuffer(0), &dwReturnLength);
	data.ReleaseBuffer();
	if (rc == ERROR_MORE_DATA) {
		rc = RegQueryValueEx(hRegKey, value, NULL, &dwType, (LPBYTE)data.GetBuffer(dwReturnLength), &dwReturnLength);
		data.ReleaseBuffer();
		if (rc != ERROR_SUCCESS)
			data = "";
	}

	RegCloseKey(hRegKey);

	return data;
}

int CControlService::SetApplicationData(const CString &key, const CString &value, const CString &subKey)
{
	CString regPath;
	HKEY hRegKey;
	DWORD dwType = REG_SZ;
	char valueBuffer[MAXREGSIZE];
	long rc;

	regPath = "Software\\";
	regPath += ApplicationRegistryPath;
	if (subKey != "") {
		regPath += "\\";
		regPath += subKey;
	}

	if (RegOpenKeyEx(HKEY_LOCAL_MACHINE, regPath, 0, KEY_SET_VALUE, &hRegKey) != ERROR_SUCCESS) {
		if (subKey != "") {
			if (RegCreateKeyEx(HKEY_LOCAL_MACHINE, regPath, 0, "",
				REG_OPTION_NON_VOLATILE, KEY_ALL_ACCESS, NULL, &hRegKey, NULL) != ERROR_SUCCESS)
				return -1;
		}
	}
	
	if (value == "") {
		rc = RegDeleteValue(hRegKey, key);
		if (rc != ERROR_SUCCESS) {
			RegCloseKey(hRegKey);
			return -1;
		}
	}
	else {
		strncpy(valueBuffer, value, MAXREGSIZE);
		
		rc =  RegSetValueEx(hRegKey, key, 0, dwType, (LPBYTE)valueBuffer, strlen(valueBuffer)+1);
		if (rc != ERROR_SUCCESS) {
			RegCloseKey(hRegKey);
			return -1;
		}
	}
	
	RegCloseKey(hRegKey);

	return 0;
}



int CControlService::GetApplicationData(const CStringArray &keys, CStringArray &values, const CString &subKey)
{
	CString regPath, data;
	HKEY hRegKey;
	DWORD dwReturnLength;
	DWORD dwType = REG_SZ;
	int rc;

	data = "";

	regPath = "Software\\";
	regPath += ApplicationRegistryPath;
	if (subKey != "") {
		regPath += "\\";
		regPath += subKey;
	}

	if (RegOpenKeyEx(HKEY_LOCAL_MACHINE, regPath, 0, KEY_READ, &hRegKey) != ERROR_SUCCESS) {
		return -1;
	}

	for (int i=0; i < keys.GetSize(); ++i) {
		dwReturnLength = 0;
		rc = RegQueryValueEx(hRegKey, keys[i], NULL, &dwType, (LPBYTE)data.GetBuffer(0), &dwReturnLength);
		data.ReleaseBuffer();
		if (rc == ERROR_MORE_DATA) {
			rc = RegQueryValueEx(hRegKey, keys[i], NULL, &dwType, (LPBYTE)data.GetBuffer(dwReturnLength), &dwReturnLength);
			data.ReleaseBuffer();
		}
		if (rc == ERROR_SUCCESS)
			values.Add(data);
		else
			values.Add("");
	
	}

	RegCloseKey(hRegKey);

	return values.GetSize();

}


int CControlService::SetApplicationData(const CStringArray &keys, const CStringArray &values, const CString &subKey)
{
	CString regPath;
	HKEY hRegKey;
	DWORD dwType = REG_SZ;
	char valueBuffer[MAXREGSIZE];
	int rc;

	regPath = "Software\\";
	regPath += ApplicationRegistryPath;
	if (subKey != "") {
		regPath += "\\";
		regPath += subKey;
	}


	if (RegOpenKeyEx(HKEY_LOCAL_MACHINE, regPath, 0, KEY_SET_VALUE, &hRegKey) != ERROR_SUCCESS) {
		if (subKey != "") {
			if (RegCreateKeyEx(HKEY_LOCAL_MACHINE, regPath, 0, "",
				REG_OPTION_NON_VOLATILE, KEY_ALL_ACCESS, NULL, &hRegKey, NULL) != ERROR_SUCCESS)
				return -1;
		}
	}
	
	for (int i=0; i < keys.GetSize(); ++i) {	
		if (values[i] == "") {
			rc = RegDeleteValue(hRegKey, keys[i]);
			if (rc != ERROR_SUCCESS) {
				RegCloseKey(hRegKey);
				return -1;
			}
		}
		else {
			
			strncpy(valueBuffer, values[i], MAXREGSIZE);
			
			if (RegSetValueEx(hRegKey, keys[i], 0, dwType, (LPBYTE)valueBuffer, (DWORD)values[i].GetLength()) != ERROR_SUCCESS) {
				RegCloseKey(hRegKey);
				return -1;
			}
		}
	}

	RegCloseKey(hRegKey);

	return 0;
}


BOOL CControlService::IsFacilityOpen()
{

	if (changesTree.elementDBID > 0)
		return TRUE;

	return FALSE;

}


BOOL CControlService::ValidateCommand(int requiredMode)
{

	if (requiredMode == CCommands::newFacilityMode) {
		if (m_Mode == IDC_USEWIZARD) {
			AfxMessageBox("Please start a new facility or open an existing facility before running this command.");
			return FALSE;
		}
	}

	if (requiredMode == CCommands::openFacilityMode) {
		if (m_Mode == IDC_USEWIZARD) {
			AfxMessageBox("Please open an existing facility before running this command.");
			return FALSE;
		}

		if (m_Mode == IDC_NEWFACILITY) {
			AfxMessageBox("Please save the facility or open an existing facility before running this command.");
			return FALSE;
		}

	}

	if (requiredMode == CCommands::savedFacilityMode) {
		if (m_Mode == IDC_USEWIZARD) {
			AfxMessageBox("Please open an existing facility before running this command.");
			return FALSE;
		}

		if (m_Mode == IDC_NEWFACILITY) {
			AfxMessageBox("Please save the facility or open an existing facility before running this command.");
			return FALSE;
		}

		if (m_Mode == IDC_OPENFACILITY) {
			CFacilityHelper facHelper;
			if (facHelper.FacilityIsModified()) {
				AfxMessageBox("Please save the facility before running this command.");
				return FALSE;
			}
		}
	}

	return TRUE;

}

void CControlService::Log(const CString &displayMsg, const CString &formatStr)
{

	ads_printf(formatStr);
				
	if (displayMsg != "")
		AfxMessageBox(displayMsg);

}

void CControlService::Log(const CString &displayMsg, const CString &formatStr, const CString &arg)
{
	ads_printf(formatStr, arg);
				
	if (displayMsg != "")
		AfxMessageBox(displayMsg);

}

void CControlService::Log(const CString &displayMsg, const CString &formatStr,int arg)
{
	ads_printf(formatStr, arg);

	if (displayMsg != "")
		AfxMessageBox(displayMsg);

}

void CControlService::Log(const CString &displayMsg, const CString &formatStr, double arg)
{
	ads_printf(formatStr, arg);
				
	if (displayMsg != "")
		AfxMessageBox(displayMsg);

}

void CControlService::Log(const CString &displayMsg, Ssa_Exception *exception)
{
	char eMsg[2048];

	if (exception != NULL) {
		// For now assume it's one of our exceptions
		exception->GetMessage(eMsg);
	}
	else
		strcpy(eMsg, "Unknown error message.");

	Log(displayMsg, CString(eMsg));
}

void CControlService::SetMode(int newMode)
{
	if (m_Mode == newMode)
		return;

	m_Mode = newMode;
	
	switch (newMode) {
	case IDC_USEWIZARD:
		SetNewMenuState(CControlService::disableMenu);
		SetOpenMenuState(CControlService::disableMenu);
		SetWizardMenuState(CControlService::enableMenu);
		ads_menucmd("A1=ACAD.AUX1");
		break;
	case IDC_NEWFACILITY:
		SetNewMenuState(CControlService::enableMenu);
		SetOpenMenuState(CControlService::disableMenu);
		SetWizardMenuState(CControlService::enableMenu);
		ads_menucmd("A1=SLOTTING2.AUX1");			// set the mouse menu to ours
		break;
	case IDC_OPENFACILITY:
		SetNewMenuState(CControlService::enableMenu);
		SetOpenMenuState(CControlService::enableMenu);
		SetWizardMenuState(CControlService::enableMenu);
		ads_menucmd("A1=SLOTTING2.AUX1");			// set the mouse menu to ours
		break;
	}
}

void CControlService::SetNewMenuState(int state)
{
	CString value;
	if (state == CControlService::enableMenu)
		value = "";
	else
		value = "~";

	ads_menucmd("GSLOTTING2.M_MAINTAIN=" + value);
	ads_menucmd("GSLOTTING2.M_FACILITY_EXPLORER=" + value);
	ads_menucmd("GSLOTTING2.M_FACILITY_PROPERTIES=" + value);
	ads_menucmd("GSLOTTING2.M_SECTION_PROPERTIES=" + value);
	ads_menucmd("GSLOTTING2.M_AISLEPROPERTIES=" + value);
	ads_menucmd("GSLOTTING2.M_SIDEPROPERTIES=" + value);
	ads_menucmd("GSLOTTING2.M_BAY_PROPERTIES=" + value);
	ads_menucmd("GSLOTTING2.M_LEVELLOC_PROPERTIES=" + value);

	ads_menucmd("GSLOTTING2.M_PICKPATH_PROPERTIES=" + value);
	ads_menucmd("GSLOTTING2.M_CHANGE_BAYPROFILE=" + value);

	ads_menucmd("GSLOTTING2.M_SAVE_FACILITY=" + value);
	ads_menucmd("GSLOTTING2.M_SAVEAS_FACILITY=" + value);

	ads_menucmd("GSLOTTING2.M_INSERT=" + value);
	ads_menucmd("GSLOTTING2.M_ADD_AISLE=" + value);
	ads_menucmd("GSLOTTING2.M_ADD_PICKPATH=" + value);
	ads_menucmd("GSLOTTING2.M_ADD_HOTSPOT=" + value);

	ads_menucmd("GSLOTTING2.M_REMOVE=" + value);
	ads_menucmd("GSLOTTING2.M_DELETE_AISLE=" + value);
	ads_menucmd("GSLOTTING2.M_DELETE_BAY=" + value);
	ads_menucmd("GSLOTTING2.M_DELETE_PICKPATH=" + value);

	
	// Popup
	ads_menucmd("GSLOTTING2.M_POP_FACILITY_EXPLORER=" + value);
	ads_menucmd("GSLOTTING2.M_POP_LEVELLOC_PROPERTIES=" + value);

	ads_menucmd("GSLOTTING2.M_POP_PICKPATH_PROPERTIES=" + value);
	ads_menucmd("GSLOTTING2.M_POP_CHANGE_BAYPROFILE=" + value);

	ads_menucmd("GSLOTTING2.M_POP_SAVE_FACILITY=" + value);

	ads_menucmd("GSLOTTING2.M_POP_INSERT=" + value);
	ads_menucmd("GSLOTTING2.M_POP_ADD_AISLE=" + value);
	ads_menucmd("GSLOTTING2.M_POP_ADD_PICKPATH=" + value);
	ads_menucmd("GSLOTTING2.M_POP_ADD_HOTSPOT=" + value);

	ads_menucmd("GSLOTTING2.M_POP_REMOVE=" + value);
	ads_menucmd("GSLOTTING2.M_POP_DELETE_AISLE=" + value);
	ads_menucmd("GSLOTTING2.M_POP_DELETE_BAY=" + value);
	ads_menucmd("GSLOTTING2.M_POP_DELETE_PICKPATH=" + value);
}

void CControlService::SetOpenMenuState(int state)
{
	CString value;
	if (state == CControlService::enableMenu)
		value = "";
	else
		value = "~";

	ads_menucmd("GSLOTTING2.M_CHECK_FACILITY=" + value);
	ads_menucmd("GSLOTTING2.M_POP_CHECK_FACILITY=" + value);

	ads_menucmd("GSLOTTING2.M_INTERFACES=" + CString(""));
	ads_menucmd("GSLOTTING2.M_WMS_SETUP=" + CString(""));
	ads_menucmd("GSLOTTING2.M_WMS_SYNC=" + CString(""));
	ads_menucmd("GSLOTTING2.M_WMS_CONVERT=" + CString(""));

	ads_menucmd("GSLOTTING2.M_OLD_INTERFACES=" + value);
	ads_menucmd("GSLOTTING2.M_PRODUCT_INBOUND=" + value);
	ads_menucmd("GSLOTTING2.M_LOCATION_OUTBOUND=" + value);
	ads_menucmd("GSLOTTING2.M_SOLUTION_OUTBOUND=" + value);
	ads_menucmd("GSLOTTING2.M_GENERATE_MOVES=" + value);
	ads_menucmd("GSLOTTING2.M_NEW_PRODUCT_LAYOUT=" + value);
	ads_menucmd("GSLOTTING2.M_SEARCH_ANCHOR_MAINTENANCE=" + value);

	ads_menucmd("GSLOTTING2.M_PRODUCTS=" + value);
	ads_menucmd("GSLOTTING2.M_PRODUCT_MAINTENANCE=" + value);
	ads_menucmd("GSLOTTING2.M_DATA_MODELER=" + value);
	ads_menucmd("GSLOTTING2.M_DATA_PURIFICATION=" + value);

	ads_menucmd("GSLOTTING2.M_PRODUCT_GROUPS=" + value);
	ads_menucmd("GSLOTTING2.M_PRODUCT_GROUP_MAINTENANCE=" + value);
	ads_menucmd("GSLOTTING2.M_ASSIGN_PRODUCT_GROUP=" + value);
	ads_menucmd("GSLOTTING2.M_UNASSIGN_PRODUCT_GROUP=" + value);

	ads_menucmd("GSLOTTING2.M_UDF_MAINTENANCE=" + value);

	ads_menucmd("GSLOTTING2.M_OPTIMIZE=" + value);
	ads_menucmd("GSLOTTING2.M_CAPITAL_COST=" + value);
	ads_menucmd("GSLOTTING2.M_PRODUCT_GROUP_LAYOUT=" + value);
	ads_menucmd("GSLOTTING2.M_PRODUCT_LAYOUT=" + value);
	ads_menucmd("GSLOTTING2.M_COST_COMPARISON=" + value);

	ads_menucmd("GSLOTTING2.M_COLORING=" + value);
	ads_menucmd("GSLOTTING2.M_COLOR_MODEL=" + value);
	ads_menucmd("GSLOTTING2.M_COLOR_PRODUCT_GROUP=" + value);
	ads_menucmd("GSLOTTING2.M_COLOR_AISLE=" + value);
	ads_menucmd("GSLOTTING2.M_COLOR_BY_PROFILE=" + value);
	ads_menucmd("GSLOTTING2.M_COLOR_BY_PRODUCT=" + value);

	ads_menucmd("GSLOTTING2.M_CAPITAL_COST_REP=" + value);
	ads_menucmd("GSLOTTING2.M_CAPITAL_COST_SUMMARY_REP=" + value);
	ads_menucmd("GSLOTTING2.M_CAPITAL_COST_DETAIL_REP=" + value);
	ads_menucmd("GSLOTTING2.M_CAPITAL_COST_REJECTIONS_REP=" + value);

	ads_menucmd("GSLOTTING2.M_PRODUCT_GROUPS_REP=" + value);
	ads_menucmd("GSLOTTING2.M_PRODUCT_GROUPS_BY_PRODUCT_REP=" + value);
	ads_menucmd("GSLOTTING2.M_PRODUCT_GROUPS_BY_MOVEMENT_REP=" + value);
	ads_menucmd("GSLOTTING2.M_PRODUCT_GROUPS_BY_BOH_REP=" + value);
	ads_menucmd("GSLOTTING2.M_PRODUCT_GROUPS_BY_UOI_REP=" + value);

	ads_menucmd("GSLOTTING2.M_PRODUCT_GROUP_LAYOUT_REP=" + value);

	ads_menucmd("GSLOTTING2.M_PRODUCT_LAYOUT_REP=" + value);
	ads_menucmd("GSLOTTING2.M_PRODUCT_LAYOUT_FACINGS_REP=" + value);
	ads_menucmd("GSLOTTING2.M_PRODUCT_GROUP_LAYOUT_BY_PRODUCT_REP=" + value);
	ads_menucmd("GSLOTTING2.M_PRODUCT_GROUP_LAYOUT_BY_PRODUCT_REP=" + value);
	ads_menucmd("GSLOTTING2.M_PRODUCT_LAYOUT_VARIABLE_WIDTH_REP=" + value);
	ads_menucmd("GSLOTTING2.M_PRODUCT_LAYOUT_CASE_REORIENTATION_REP=" + value);

	ads_menucmd("GSLOTTING2.M_PRODUCT_MOVES_REP=" + value);
	ads_menucmd("GSLOTTING2.M_LOCATION_OUTBOUND_REP=" + value);
	ads_menucmd("GSLOTTING2.M_PRODUCT_DETAIL_REP=" + value);

	ads_menucmd("GSLOTTING2.M_COST_ANALYSIS_REP=" + value);
	ads_menucmd("GSLOTTING2.M_COST_ANALYSIS_SUMMARY_REP=" + value);
	ads_menucmd("GSLOTTING2.M_COST_ANALYSIS_DETAIL_REP=" + value);
}

void CControlService::SetWizardMenuState(int state)
{
	CString value;
	if (state == CControlService::enableMenu)
		value = "";
	else
		value = "~";

	ads_menucmd("GSLOTTING2.M_NEW_CONNECTION=" + value);
	ads_menucmd("GSLOTTING2.M_WIZARD=" + value);
	ads_menucmd("GSLOTTING2.M_HELP=" + value);
	ads_menucmd("GSLOTTING2.M_CLOSE=" + value);
	ads_menucmd("GSLOTTING2.M_USER_QUERY=" + value);
	ads_menucmd("GSLOTTING2.M_BAYWIZARD=" + value);
	ads_menucmd("GSLOTTING2.M_SIDEWIZARD=" + value);
	ads_menucmd("GSLOTTING2.M_AISLEWIZARD=" + value);
	ads_menucmd("GSLOTTING2.M_NEW_FACILITY=" + value);
	ads_menucmd("GSLOTTING2.M_OPEN_FACILITY=" + value);
	ads_menucmd("GSLOTTING2.M_DELETE_FACILITY=" + value);

	ads_menucmd("GSLOTTING2.M_OPEN_SAVED_REPORT=" + value);

	// Popup
	ads_menucmd("GSLOTTING2.M_POP_WIZARDS=" + value);
	ads_menucmd("GSLOTTING2.M_POP_BAYWIZARD=" + value);
	ads_menucmd("GSLOTTING2.M_POP_SIDEWIZARD=" + value);
	ads_menucmd("GSLOTTING2.M_AISLEWIZARD=" + value);
	ads_menucmd("GSLOTTING2.M_POP_NEW_FACILITY=" + value);
	ads_menucmd("GSLOTTING2.M_POP_OPEN_FACILITY=" + value);



}

int CControlService::GetCurrentFacilityDBId()
{
	return changesTree.elementDBID;
}


int CControlService::GetDivisor()
{
	return m_Divisor;
}

BOOL CControlService::DrawBayArrow()
{

	return m_DrawBayArrow;
}

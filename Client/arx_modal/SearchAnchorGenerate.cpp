// SearchAnchorGenerate.cpp : implementation file
//

#include "stdafx.h"
#include "ssa_exception.h"

#include "SearchAnchorGenerate.h"
#include "HelpService.h"
#include "ProductGroupDataService.h"
#include "UtilityHelper.h"
#include "ControlService.h"

#ifdef _DEBUG
#define new DEBUG_NEW
#undef THIS_FILE
static char THIS_FILE[] = __FILE__;
#endif

/////////////////////////////////////////////////////////////////////////////
// CSearchAnchorGenerate dialog
extern CHelpService helpService;
extern CUtilityHelper utilityHelper;
extern CControlService controlService;

CSearchAnchorGenerate::CSearchAnchorGenerate(CWnd* pParent /*=NULL*/)
	: CDialog(CSearchAnchorGenerate::IDD, pParent)
{
	//{{AFX_DATA_INIT(CSearchAnchorGenerate)
	m_ClearExisting = FALSE;
	//}}AFX_DATA_INIT
}


void CSearchAnchorGenerate::DoDataExchange(CDataExchange* pDX)
{
	CDialog::DoDataExchange(pDX);
	//{{AFX_DATA_MAP(CSearchAnchorGenerate)
	DDX_Check(pDX, IDC_CLEAR_EXISTING, m_ClearExisting);
	//}}AFX_DATA_MAP
}


BEGIN_MESSAGE_MAP(CSearchAnchorGenerate, CDialog)
	//{{AFX_MSG_MAP(CSearchAnchorGenerate)
	ON_BN_CLICKED(IDHELP, OnHelp)
	ON_WM_HELPINFO()
	//}}AFX_MSG_MAP
END_MESSAGE_MAP()

/////////////////////////////////////////////////////////////////////////////
// CSearchAnchorGenerate message handlers

BOOL CSearchAnchorGenerate::OnInitDialog() 
{
	CDialog::OnInitDialog();
	
	CRect r;
	CStringArray pgList;
	int rc;
	CProductGroup pProductGroup;
	CProductGroupDataService service;
	CComboBox *pProductGroupBox = (CComboBox *)GetDlgItem(IDC_PRODUCT_GROUP);

	try {
		rc = service.GetProductGroups(controlService.GetCurrentFacilityDBId(), pgList);
	}
	catch (Ssa_Exception e) {
		utilityHelper.ProcessError("Error getting product groups.", &e);
	}
	catch (...) {
		utilityHelper.ProcessError("Error getting product groups.");
	}

	pProductGroupBox->AddString("All");

	for (int i=0; i < pgList.GetSize(); ++i) {
		pProductGroup.Parse(pgList[i]);
		pProductGroupBox->AddString(pProductGroup.m_Description);
		pProductGroupBox->SetItemData(pProductGroupBox->GetCount()-1, pProductGroup.m_ProductGroupDBID);
	}

	pProductGroupBox->GetWindowRect(&r);
	pProductGroupBox->SetWindowPos(NULL, 0, 0, r.Width(), r.Height()*10, SWP_NOMOVE|SWP_NOZORDER);
	pProductGroupBox->SetCurSel(0);
	
	UpdateData(FALSE);

	return TRUE;  // return TRUE unless you set the focus to a control
	              // EXCEPTION: OCX Property Pages should return FALSE
}

void CSearchAnchorGenerate::OnOK() 
{
	UpdateData(TRUE);

	CComboBox *pComboBox = (CComboBox *)GetDlgItem(IDC_PRODUCT_GROUP);

	if (pComboBox->GetCurSel() > 0)
		m_ProductGroupID = pComboBox->GetItemData(pComboBox->GetCurSel());
	else
		m_ProductGroupID = -1;
	
	CDialog::OnOK();
}

void CSearchAnchorGenerate::OnHelp() 
{
	helpService.ShowScreenHelp(IDD);
}

BOOL CSearchAnchorGenerate::OnHelpInfo(HELPINFO* pHelpInfo) 
{
	helpService.ShowFieldHelp(IDD, pHelpInfo->iCtrlId);

	return TRUE;

}

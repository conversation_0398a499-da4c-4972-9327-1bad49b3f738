// UtilityCommands.h: interface for the CUtilityCommands class.
//
//////////////////////////////////////////////////////////////////////

#if !defined(AFX_UTILITYCOMMANDS_H__4134284F_6510_4C49_8838_85BFA6ED76FC__INCLUDED_)
#define AFX_UTILITYCOMMANDS_H__4134284F_6510_4C49_8838_85BFA6ED76FC__INCLUDED_

#include "Commands.h"

#if _MSC_VER > 1000
#pragma once
#endif // _MSC_VER > 1000

class CUtilityCommands : public CCommands
{
public:
	
	
	CUtilityCommands();
	virtual ~CUtilityCommands();

	static void RegisterCommands();
	static void ZoomByHandle();
	static void ValidateFacility();
	static void ListHandles();
	static void UserQuery();
	static void DumpFacilityTree();
	static void CleanFacility();
	static void ConvertFacility();
	static void ColorDisconnectedPickpaths();
	static void ExportDatabase();

};

#endif // !defined(AFX_UTILITYCOMMANDS_H__4134284F_6510_4C49_8838_85BFA6ED76FC__INCLUDED_)

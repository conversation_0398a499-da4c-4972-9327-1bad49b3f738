// WMSGroupConnection.cpp: implementation of the CWMSGroupConnection class.
//
//////////////////////////////////////////////////////////////////////

#include "stdafx.h"
#include "modal.h"
#include "WMSGroupConnection.h"
#include "UtilityHelper.h"

#ifdef _DEBUG
#undef THIS_FILE
static char THIS_FILE[]=__FILE__;
#define new DEBUG_NEW
#endif

extern CUtilityHelper utilityHelper;

//////////////////////////////////////////////////////////////////////
// Construction/Destruction
//////////////////////////////////////////////////////////////////////

CWMSGroupConnection::CWMSGroupConnection()
{
	m_GroupConnectionDBId = 0;
}

CWMSGroupConnection::~CWMSGroupConnection()
{
	delete m_pExternalConnection;
}

CWMSGroupConnection::CWMSGroupConnection(const CWMSGroupConnection& other)
{
	m_GroupConnectionDBId = other.m_GroupConnectionDBId;
	m_WMSGroupDBId = other.m_WMSGroupDBId;
	m_ExternalConnectionDBId = other.m_ExternalConnectionDBId;
	m_InterfaceType = other.m_InterfaceType;
	m_Direction = other.m_Direction;

	m_GroupName = other.m_GroupName;
	m_ConnectionName = other.m_ConnectionName;
	
	m_pExternalConnection = new CExternalConnection(*other.m_pExternalConnection);
}	

CWMSGroupConnection& CWMSGroupConnection::operator=(const CWMSGroupConnection& other)
{
	m_GroupConnectionDBId = other.m_GroupConnectionDBId;
	m_WMSGroupDBId = other.m_WMSGroupDBId;
	m_ExternalConnectionDBId = other.m_ExternalConnectionDBId;
	m_InterfaceType = other.m_InterfaceType;
	m_Direction = other.m_Direction;

	m_GroupName = other.m_GroupName;
	m_ConnectionName = other.m_ConnectionName;
	m_pExternalConnection = new CExternalConnection(*other.m_pExternalConnection);

	return *this;
}

BOOL CWMSGroupConnection::operator==(const CWMSGroupConnection& other) 
{
	if (m_GroupConnectionDBId != other.m_GroupConnectionDBId) return FALSE;
	if (m_WMSGroupDBId != other.m_WMSGroupDBId) return FALSE;
	if (m_ExternalConnectionDBId != other.m_ExternalConnectionDBId) return FALSE;
	if (m_InterfaceType != other.m_InterfaceType) return FALSE;
	if (m_Direction != other.m_Direction) return FALSE;

	if (m_GroupName != other.m_GroupName) return FALSE;
	if (m_ConnectionName != other.m_ConnectionName) return FALSE;
	
	return TRUE;

}

int CWMSGroupConnection::Parse(const CString &line)
{
	CStringArray strings;

	utilityHelper.ParseString(line, "|", strings);

	for (int i=0; i < strings.GetSize(); ++i) {
		switch (i) {
		case 0:
			m_GroupConnectionDBId = atoi(strings[i]);
			break;
		case 1:
			m_InterfaceType = atoi(strings[i]);
			break;
		case 2:
			m_Direction = atoi(strings[i]);
			break;
		case 3:
			m_ExternalConnectionDBId = atoi(strings[i]);
			break;
		case 4:
			m_WMSGroupDBId = atoi(strings[i]);
			break;
		case 5:
			m_ConnectionName = strings[i];
			break;
		case 6:
			m_GroupName = strings[i];
			break;
		}
	}

	return 0;
}

// ProductGroupHelper.cpp: implementation of the CProductGroupHelper class.
//
//////////////////////////////////////////////////////////////////////

#include "stdafx.h"
#include "modal.h"
#include "ProductGroupHelper.h"

#include "ProductGroupFrame.h"
#include <adscodes.h>
#include <dbsymtb.h>


#ifdef _DEBUG
#undef THIS_FILE
static char THIS_FILE[]=__FILE__;
#define new DEBUG_NEW
#endif

CProductGroupFrame *m_ProductGroupFrame;

//////////////////////////////////////////////////////////////////////
// Construction/Destruction
//////////////////////////////////////////////////////////////////////

CProductGroupHelper::CProductGroupHelper()
{

}

CProductGroupHelper::~CProductGroupHelper()
{

}

void CProductGroupHelper::ProductGroupMaintenance()
{

	HWND hWnd = adsw_acadMainWnd(); 
	CWnd *parent;
	parent = CWnd::FromHandle(hWnd);

	m_ProductGroupFrame = new CProductGroupFrame;
//	m_ProductGroupFrame->LoadFrame(IDR_DUMMY, WS_OVERLAPPED|WS_CAPTION|WS_SYSMENU, parent, NULL);

	CRect r(0, 0, 850, 600);
	m_ProductGroupFrame->Create(NULL, "Product Group Maintenance", WS_OVERLAPPED|WS_CAPTION|WS_SYSMENU, r,
		parent, NULL, 0, NULL);

	m_ProductGroupFrame->SetWindowPos(parent, 0, 0, 0, 0, SWP_NOMOVE|SWP_NOSIZE);

	m_ProductGroupFrame->CenterWindow();
	m_ProductGroupFrame->ShowWindow(SW_SHOW);
	m_ProductGroupFrame->UpdateWindow();

//	utilityHelper.GetParentWindow()->SetActiveWindow();
}

#if !defined(AFX_ROTATIONBUTTON_H__4208423E_A776_48CE_814A_5E3A61B7969D__INCLUDED_)
#define AFX_ROTATIONBUTTON_H__4208423E_A776_48CE_814A_5E3A61B7969D__INCLUDED_

#if _MSC_VER > 1000
#pragma once
#endif // _MSC_VER > 1000
// RotationButton.h : header file
//

/////////////////////////////////////////////////////////////////////////////
// CRotationButton window

class CRotationButton : public CButton
{
// Construction
public:
	CRotationButton();

// Attributes
public:

// Operations
public:

// Overrides
	// ClassWizard generated virtual function overrides
	//{{AFX_VIRTUAL(CRotationButton)
	public:
	virtual void DrawItem(LPDRAWITEMSTRUCT lpDrawItemStruct);
	//}}AFX_VIRTUAL

// Implementation
public:
	virtual ~CRotationButton();

	// Generated message map functions
protected:
	//{{AFX_MSG(CRotationButton)
		// NOTE - the ClassWizard will add and remove member functions here.
	//}}AFX_MSG

	DECLARE_MESSAGE_MAP()
private:
	void RotatePoint(float angle, int *x, int *y);
};

/////////////////////////////////////////////////////////////////////////////

//{{AFX_INSERT_LOCATION}}
// Microsoft Visual C++ will insert additional declarations immediately before the previous line.

#endif // !defined(AFX_ROTATIONBUTTON_H__4208423E_A776_48CE_814A_5E3A61B7969D__INCLUDED_)

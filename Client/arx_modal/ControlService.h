// ControlService.h: interface for the CControlService class.
//
//////////////////////////////////////////////////////////////////////

#if !defined(AFX_CONTROLSERVICE_H__62E8841F_DDBB_4629_8534_11EBA36A313B__INCLUDED_)
#define AFX_CONTROLSERVICE_H__62E8841F_DDBB_4629_8534_11EBA36A313B__INCLUDED_

#include "BayProfileListDialog.h"	// Added by ClassView
#include "SideProfileListDialog.h"
#include "AisleProfileListDialog.h"
#include "ssa_exception.h"

#if _MSC_VER > 1000
#pragma once
#endif // _MSC_VER > 1000

class CControlService  
{
public:
	BOOL DrawBayArrow();
	int GetDivisor();
	int GetCurrentFacilityDBId();

	BOOL m_Debug;
	void SetNewMenuState(int state);
	void SetOpenMenuState(int state);
	void SetWizardMenuState(int state);

	void SetMode(int newMode);
	void Log(const CString &displayMsg, const CString &formatStr, const CString &arg);
	void Log(const CString &displayMsg, const CString &formatStr, int arg);
	void Log(const CString &displayMsg, const CString &formatStr, double arg);
	void Log(const CString &displayMsg, const CString &formatStr);
	void Log(const CString &displayMsg, Ssa_Exception *exception);

	BOOL ValidateCommand(int requiredMode);
	int m_Mode;
	BOOL IsFacilityOpen();
	CBayProfileListDialog *m_pBayProfileListDialog;
	CSideProfileListDialog *m_pSideProfileListDialog;
	CAisleProfileListDialog *m_pAisleProfileListDialog;

	CControlService();
	virtual ~CControlService();

	int Initialize();

	CString GetApplicationData(const CString &key, const CString &subKey = "");
	int SetApplicationData(const CString &key, const CString &value, const CString &subKey = "");
	int GetApplicationData(const CStringArray &keys, CStringArray &values, const CString &subKey = "");
	int SetApplicationData(const CStringArray &keys, const CStringArray &values, const CString &subKey = "");
	CString GetRegistryData(const CString &key, const CString &value);

	int m_Divisor;
	int m_MeasurementType;
	int m_DefaultWMS;
	CString m_ClientHome;
	CString m_CurrentDatabase;
	//CHECK008 
	CString m_CurrentUserID;
	CString m_Password;

	typedef enum {
		enableMenu,
		disableMenu
	} enumMenuState;

	typedef enum {
		Imperial = 0,
		Metric = 1
	};
private:
	BOOL m_DrawBayArrow;
};

#endif // !defined(AFX_CONTROLSERVICE_H__62E8841F_DDBB_4629_8534_11EBA36A313B__INCLUDED_)

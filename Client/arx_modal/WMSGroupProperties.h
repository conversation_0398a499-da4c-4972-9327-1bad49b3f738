#if !defined(AFX_WMSGROUPPROPERTIES_H__32900698_2B0E_43DF_BAC7_7A9E2FBA48EB__INCLUDED_)
#define AFX_WMSGROUPPROPERTIES_H__32900698_2B0E_43DF_BAC7_7A9E2FBA48EB__INCLUDED_

#include "WMSGroup.h"	// Added by ClassView
#if _MSC_VER > 1000
#pragma once
#endif // _MSC_VER > 1000
// WMSGroupProperties.h : header file
//
#include "WMSGroup.h"

/////////////////////////////////////////////////////////////////////////////
// CWMSGroupProperties dialog

class CWMSGroupProperties : public CDialog
{
// Construction
public:
	CWMSGroup m_Group;
	CWMSGroupProperties(CWnd* pParent = NULL);   // standard constructor

// Dialog Data
	//{{AFX_DATA(CWMSGroupProperties)
	enum { IDD = IDD_WMS_GROUP_PROPERTIES };
	CComboBox	m_WMSSystemCtrl;
	CString	m_Description;
	CString	m_Name;
	CString	m_WMSId;
	CString	m_IdText;
	//}}AFX_DATA


// Overrides
	// ClassWizard generated virtual function overrides
	//{{AFX_VIRTUAL(CWMSGroupProperties)
	protected:
	virtual void DoDataExchange(CDataExchange* pDX);    // DDX/DDV support
	//}}AFX_VIRTUAL

// Implementation
protected:

	// Generated message map functions
	//{{AFX_MSG(CWMSGroupProperties)
	virtual BOOL OnInitDialog();
	virtual void OnOK();
	afx_msg BOOL OnHelpInfo(HELPINFO* pHelpInfo);
	afx_msg void OnHelp();
	afx_msg void OnDefineConnections();
	afx_msg void OnSelchangeWmsSystem();
	//}}AFX_MSG
	DECLARE_MESSAGE_MAP()
};

//{{AFX_INSERT_LOCATION}}
// Microsoft Visual C++ will insert additional declarations immediately before the previous line.

#endif // !defined(AFX_WMSGROUPPROPERTIES_H__32900698_2B0E_43DF_BAC7_7A9E2FBA48EB__INCLUDED_)

// ProductGroup.cpp: implementation of the CProductGroup class.
//
//////////////////////////////////////////////////////////////////////

#include "stdafx.h"
#include "modal.h"
#include "ProductGroup.h"
#include "dbsymtb.h"

#ifdef _DEBUG
#undef THIS_FILE
static char THIS_FILE[]=__FILE__;
#define new DEBUG_NEW
#endif

//////////////////////////////////////////////////////////////////////
// Construction/Destruction
//////////////////////////////////////////////////////////////////////

CProductGroup::CProductGroup()
{
	m_Description = "";
	m_Exclusive = FALSE;
	m_IsAssignmentLocked = FALSE;
	m_IsProdGroupLocked = FALSE;
	m_OptimizeAttribute = "";
	m_OptimizeMethod = -1;
	m_PercentOpenLocs = -1;
	m_Priority = -1;
	m_ProductGroupDBID = -1;
	m_ProductCount = 0;

}

CProductGroup::~CProductGroup()
{
	for (int i=0; i < m_QueryList.GetSize(); ++i)
		delete m_QueryList[i];

	for (i=0; i < m_ConstraintList.GetSize(); ++i)
		delete m_ConstraintList[i];
}


CProductGroup& CProductGroup::operator=(const CProductGroup &other)
{
	CProductGroupQuery *pQuery;
	CProductGroupConstraint *pConstraint;

	m_Description = other.m_Description;
	m_IsAssignmentLocked = other.m_IsAssignmentLocked;
	m_IsProdGroupLocked = other.m_IsProdGroupLocked;
	m_OptimizeAttribute = other.m_OptimizeAttribute;
	m_OptimizeMethod = other.m_OptimizeMethod;
	m_PercentOpenLocs = other.m_PercentOpenLocs;
	m_Priority = other.m_Priority;
	m_ProductGroupDBID = other.m_ProductGroupDBID;
	m_Exclusive = other.m_Exclusive;

	for (int i=0; i < m_QueryList.GetSize(); ++i)
		delete m_QueryList[i];

	m_QueryList.RemoveAll();

	for (i=0; i < other.m_QueryList.GetSize(); ++i) {
		pQuery = new CProductGroupQuery;
		*pQuery = *(other.m_QueryList[i]);
		m_QueryList.Add(pQuery);
	}

	for (i=0; i < m_ConstraintList.GetSize(); ++i)
		delete m_ConstraintList[i];
	
	m_ConstraintList.RemoveAll();

	for (i=0; i < other.m_ConstraintList.GetSize(); ++i) {
		pConstraint = new CProductGroupConstraint;
		*pConstraint = *(other.m_ConstraintList[i]);
		m_ConstraintList.Add(pConstraint);
	}

	return *this;
}

int CProductGroup::Parse(CString &line)
{
	char *str;
	char *ptr;
	CString tmp;

	tmp = line;

	try {
		tmp.Replace("||", "| |");
		tmp.Replace("||", "| |");

		str = tmp.GetBuffer(0);
		
		ptr = strtok(str, "|");
		m_ProductGroupDBID = atol(ptr);
		ptr = strtok(NULL, "|");
		m_Description = ptr;
		ptr = strtok(NULL, "|");
		m_Priority = atoi(ptr);
		ptr = strtok(NULL, "|");
		m_PercentOpenLocs = (float)atof(ptr);
		ptr = strtok(NULL, "|");
		m_IsProdGroupLocked = (atoi(ptr) == 1);
		ptr = strtok(NULL, "|");
		m_IsAssignmentLocked = (atoi(ptr) == 1);
		ptr = strtok(NULL, "|");
		m_Exclusive = (atoi(ptr) == 1);
		ptr = strtok(NULL, "|");
		m_OptimizeAttribute = ptr;
		ptr = strtok(NULL, "|");
		m_OptimizeMethod = atoi(ptr);

		tmp.ReleaseBuffer();


	}
	catch (...) {
		tmp.ReleaseBuffer();
		AfxMessageBox("Error processing product group list.\n");
		ads_printf("%s\n", line);
		return -1;
	}

	return 0;
	
}

BOOL CProductGroup::IsEqual(CProductGroup &other)
{
	if (m_Description != other.m_Description) return FALSE;
	if (m_Exclusive != other.m_Exclusive) return FALSE;
	if (m_IsAssignmentLocked != other.m_IsAssignmentLocked) return FALSE;
	if (m_IsProdGroupLocked != other.m_IsProdGroupLocked) return FALSE;
	if (m_OptimizeAttribute != other.m_OptimizeAttribute) return FALSE;
	if (m_OptimizeMethod != other.m_OptimizeMethod) return FALSE;
	if (m_PercentOpenLocs != other.m_PercentOpenLocs) return FALSE;
	if (m_Priority != other.m_Priority) return FALSE;
	
	if (m_ConstraintList.GetSize() != other.m_ConstraintList.GetSize()) return FALSE;
	if (m_QueryList.GetSize() != other.m_QueryList.GetSize()) return FALSE;

	// For now, let's say if the order changes they are not equal; 
	// Later we can actually search the whole list for matches
	for (int i=0; i < m_ConstraintList.GetSize(); ++i) {
		if (! m_ConstraintList[i]->IsEqual(*(other.m_ConstraintList[i])))
			return FALSE;
	}

	for (i=0; i < m_QueryList.GetSize(); ++i) {
		if (! m_QueryList[i]->IsEqual(*(other.m_QueryList[i])))
			return FALSE;
	}

	return TRUE;

}

// ProductDataService.cpp: implementation of the CProductDataService class.
//
//////////////////////////////////////////////////////////////////////

#include "stdafx.h"
#include <afxmt.h>

#include "ssa_exception.h"
#include "ProductDataService.h"
#include "Constants.h"
#include "DataAccessService.h"
#include "ForteService.h"
#include "UtilityHelper.h"
#include "LocationInfo.h"
#include "ManualAssignmentDialog.h"
#include "DisplayResults.h"
#include "ControlService.h"

#include "base.h"
#include "MiscClasses.h"
#include "SLOTSessionMgr.h"
#include "SrvcObjs.h"

extern CDataAccessService dataAccessService;
extern CForteService forteService;
extern CUtilityHelper utilityHelper;
extern CControlService controlService;

extern BOOL DEBUG;
extern CEvent g_ThreadDone;

//////////////////////////////////////////////////////////////////////
// Construction/Destruction
//////////////////////////////////////////////////////////////////////

CProductDataService::CProductDataService()
{
	// store everything in all lower case for lookup
	m_OperatorMap.SetAt("=", "=");
	m_OperatorMap.SetAt("equal", "=");
	m_OperatorMap.SetAt("not equal", "!=");
	m_OperatorMap.SetAt("!=", "!=");
	m_OperatorMap.SetAt("<>", "!=");
	m_OperatorMap.SetAt("less than", "<");
	m_OperatorMap.SetAt("<", "<");
	m_OperatorMap.SetAt(">", ">");
	m_OperatorMap.SetAt("greater than", ">");
	m_OperatorMap.SetAt("between", "between");
	m_OperatorMap.SetAt("not between", "not between");
	m_OperatorMap.SetAt("exists", "exists");
	m_OperatorMap.SetAt("not exists", "not exists");
	m_OperatorMap.SetAt("<=", "<=");
	m_OperatorMap.SetAt(">=", ">=");
	m_OperatorMap.SetAt("in", "in");
	m_OperatorMap.SetAt("not in", "not in");
	m_OperatorMap.SetAt("like", "like");


}

CProductDataService::~CProductDataService()
{
	for (int i=0; i < m_ProductAttributeList.GetSize(); ++i)
		delete m_ProductAttributeList[i];

	for (i=0; i < m_UDFList.GetSize(); ++i)
		delete m_UDFList[i];
}


int CProductDataService::LoadProductAttributes()
{
	CProductAttribute *attribute;
	CString dtint, dtfloat, dtstring, temp;
	CStringArray productAttributeList;
	CUDF *pUDF;

	dtint.Format("%d", DT_INT);
	dtfloat.Format("%d", DT_FLOAT);
	dtstring.Format("%d", DT_STRING);

	if (m_ProductAttributeList.GetSize() > 0)
		return 0;

	InitProductAttributes();
	
	for (int i=0; i < m_ProductAttributeList.GetSize(); ++i) {
		attribute = m_ProductAttributeList[i];
		temp.Format("%d", attribute->m_Type);
		m_AttributeTypeMap.SetAt(attribute->m_ColumnName, temp);
		if (attribute->m_AttributeDBID > 0) {
			pUDF = new CUDF;
			pUDF->m_Name = attribute->m_Name;
			pUDF->m_ListID = attribute->m_AttributeDBID;
			pUDF->m_Type = attribute->m_Type;
			pUDF->m_DefaultValue = attribute->m_Initial;
			pUDF->m_ParentID = controlService.GetCurrentFacilityDBId();
			pUDF->m_ElementType = UDF_PRODUCT;
			pUDF->m_ListValues.Copy(attribute->m_ListValues);
			m_UDFList.Add(pUDF);
		}
	}

	// These attributes are used by product maintenance
	m_AttributeTypeMap.SetAt("BayProfile", dtstring);	
	m_AttributeTypeMap.SetAt("BayProfileDBID", dtint);	
	m_AttributeTypeMap.SetAt("ProductGroup", dtstring);
	m_AttributeTypeMap.SetAt("ProductGroupDBID", dtint);
	m_AttributeTypeMap.SetAt("RotatedHeight", dtfloat);
	m_AttributeTypeMap.SetAt("RotatedLength", dtfloat);
	m_AttributeTypeMap.SetAt("RotatedWidth", dtfloat);
	m_AttributeTypeMap.SetAt("LevelType", dtint);
	m_AttributeTypeMap.SetAt("Location", dtstring);
	m_AttributeTypeMap.SetAt("LocationDBID", dtint);
	m_AttributeTypeMap.SetAt("LocProductGroup", dtstring);
	m_AttributeTypeMap.SetAt("LocProductGroupDBID", dtint);

	m_AttributeTypeMap.SetAt("OptimizedBayProfile", dtstring);	
	m_AttributeTypeMap.SetAt("OptimizedBayProfileDBID", dtint);	
	m_AttributeTypeMap.SetAt("OptimizedLevelType", dtint);
	m_AttributeTypeMap.SetAt("OptimizedLocation", dtstring);
	m_AttributeTypeMap.SetAt("OptimizedLocationDBID", dtint);
	m_AttributeTypeMap.SetAt("OptimizedLocProductGroup", dtstring);
	m_AttributeTypeMap.SetAt("OptimizedLocProductGroupDBID", dtint);

	return 0;
}

int CProductDataService::InitProductAttributes()
{
	int rc, i;
	CStringArray attributes;
	CProductAttribute *attribute;

	if (m_ProductAttributeList.GetSize() > 0)
		return 0;

	try {
		rc = GetProductAttributes(controlService.GetCurrentFacilityDBId(), attributes);
	}
	catch(Ssa_Exception e) {
		throw e;
		return -1;
	}
	catch(...) {
		throw Ssa_Exception("Generic error getting product attributes", __FILE__, __LINE__, 200);
		return -1;
	}

	for (i=0; i < attributes.GetSize(); ++i) {
		attribute = new CProductAttribute;
		attribute->Parse(attributes[i]);
		controlService.Log("", (const CString) attributes[i]);
		m_ProductAttributeList.Add(attribute);
		m_ProductAttributeMap.SetAt(attribute->m_Name, attribute);
	}

	return 0;

}

int CProductDataService::GetProductListByQuery(qqhSLOTQuery &query, CStringArray &productList)
{
	CString sql;
	int rc;

	try {
		rc = BuildQuery(sql, query);
	}
	catch (Ssa_Exception e) {
		throw e;
		return -1;
	}
	catch (...) {
		throw Ssa_Exception("Generic error building query for product.", __FILE__, __LINE__, 200);
		return -1;
	}

	try {
		rc = QueryProductsAll(sql, productList);
	}
	catch (Ssa_Exception e) {
		throw e;
		return -1;
	}
	catch (...) {
		throw Ssa_Exception("Generic error querying for products.", __FILE__, __LINE__, 200);
		return -1;
	}

	return productList.GetSize();

}


int CProductDataService::GetProductByID(long productDBID, CStringArray &productList)
{
	CString sql;
	int rc;

	try {
		rc = BuildQueryByID(sql, productDBID);
	}
	catch (Ssa_Exception e) {
		throw e;
		return -1;
	}
	catch (...) {
		throw Ssa_Exception("Generic error building query for product.", __FILE__, __LINE__, 200);
		return -1;
	}

	try {
		rc = QueryProductsAll(sql, productList);
	}
	catch (Ssa_Exception e) {
		throw e;
		return -1;
	}
	catch (...) {
		throw Ssa_Exception("Generic error querying for products.", __FILE__, __LINE__, 200);
		return -1;
	}

	return productList.GetSize();

}


int CProductDataService::BuildQueryByID(CString &sql, long productID)
{
	CString selectList, fromList, whereList;
	CString tmp;
	int i;

	selectList.Format("select pp.dbproductpackid, pp.description, pp.wmsproductid, "
		"pp.wmsproductdetailid, pp.casepack, pp.innerpack, pp.unitofissue, pp.isassignmentlocked, "
		"pp.movement, pp.balanceonhand, pp.optimizeby, pp.numberofhits, pp.ishazard, "
		"pp.ispicktobelt, pp.rotatexaxis, pp.rotateyaxis, pp.rotatezaxis, "
		"pp.width, pp.length, pp.height, pp.innerwidth, pp.innerlength, pp.innerheight, "
		"pp.eachwidth, pp.eachlength, pp.eachheight, pp.weight, pp.maxstacknumber, pp.numberinpallet, "
		"pp.Status, pp.IsActive, pp.Trace, pp.PreviousMovement, pp.PreviousBalanceOnHand, "
		"pp.CommodityType, pp.CrushFactor, pp.ExternalKey, "
		"pp.lastoptimizeattribute, pp.nestedwidth, pp.nestedlength, pp.nestedheight, "
		"pc.dbprodcontainerid, pc.width, pc.length, pc.height, pc.ti, pc.hi, "
		"pc.isheightoverride");

	fromList.Format(" from dbproductpack pp, dbprodcontainer pc");
	
	whereList.Format(" where pp.dbproductpackid = %d"
		" and pc.dbproductpackid = pp.dbproductpackid", productID);

	for (i=0; i < m_UDFList.GetSize(); ++i) {
		tmp.Format(", 'UDFSTART', UDF%d.description UDF%dname, UDF%d.type UDF%dtype,"
			" VAL%d.value UDF%dvalue, UDF%d.dbprodpkudflistid UDF%dListId,"
			" VAL%d.dbprodpkudfvalid UDF%dValId, UDF%d.DefaultValue UDF%dDefaultValue",
			i, i, i, i, i, i, i, i, i, i, i, i);
		selectList += tmp;

		tmp.Format(", dbprodpkudflist UDF%d, dbprodpkudfval VAL%d", i, i);
		fromList += tmp;

		tmp.Format(" and UDF%d.dbprodpkudflistid = %d"
			" and VAL%d.dbprodpkudflistid = UDF%d.dbprodpkudflistid"
			" and VAL%d.dbproductpackid = pp.dbproductpackid",
			i, m_UDFList[i]->m_ListID, i, i, i);
		whereList += tmp;

	}

	tmp.Format(", l.dblocationid, l.description, ss.casequantity, "
		"prodsg.dbslottinggroupid, prodsg.description, "
		"DBSlottingGroupLoc.dbslottinggroupid, DBSlottingGroupLoc.description, "
		"bp.dbbayprofileid, bp.description, lp.baytype, "
		"ss.rotatedwidth, ss.rotatedlength, ss.rotatedheight ");
	selectList += tmp;

	tmp.Format(", lopt.dblocationid, lopt.description, ssopt.casequantity, "
		"sgopt.dbslottinggroupid, sgopt.description, "
		"bpopt.dbbayprofileid, bpopt.description, lpopt.baytype ");
	selectList += tmp;
	
	tmp.Format(", dbprodslotgroup psg, dbslottinggroup prodsg, dbslotsolution ss, dblocation l, dblevel le, dblevelprofile lp, "
		"dbbayprofile bp, dbslotgrpbay sgb, dbslottinggroup DBSlottingGroupLoc ");
	fromList += tmp;

	tmp.Format(", dbslotsolution ssopt, dblocation lopt, dblevel leopt, dblevelprofile lpopt, "
		"dbbayprofile bpopt, dbslotgrpbay sgbopt, dbslottinggroup sgopt ");
	fromList += tmp;

	// Oracle-Specific
	tmp.Format(" and pp.dbproductpackid = ss.dbproductpackid (+) "
		"and ss.origin (+) = %d "
		"and pp.dbproductpackid = psg.dbproductpackid (+) "
		"and psg.dbslottinggroupid = prodsg.dbslottinggroupid (+) "
		"and ss.dblocationid = l.dblocationid (+) "
		"and ss.isprimary (+) = 1 "
		"and l.dblevelid = le.dblevelid (+) "
		"and le.dblevelprofileid = lp.dblevelprofileid (+) "
		"and lp.dbbayprofileid = bp.dbbayprofileid (+) "
		"and le.dblevelid = sgb.dblevelid (+) "
		"and sgb.dbslottinggroupid = DBSlottingGroupLoc.dbslottinggroupid (+) ", CProductDataService::Baseline);
	whereList += tmp;

	tmp.Format(" and pp.dbproductpackid = ssopt.dbproductpackid (+) "
		"and ssopt.origin (+) = %d "
		"and ssopt.dblocationid = lopt.dblocationid (+) "
		"and ssopt.isprimary (+) = 1 "
		"and lopt.dblevelid = leopt.dblevelid (+) "
		"and leopt.dblevelprofileid = lpopt.dblevelprofileid (+) "
		"and lpopt.dbbayprofileid = bpopt.dbbayprofileid (+) "
		"and leopt.dblevelid = sgbopt.dblevelid (+) "
		"and sgbopt.dbslottinggroupid = sgopt.dbslottinggroupid (+) ", CProductDataService::Optimize);
	whereList += tmp;

	sql = selectList;
	sql += fromList;
	sql += whereList;

	return 0;

}


void CProductDataService::AddQueryAttribute(qqhSLOTQuery &query, const CString &objectName, 
									  const CString &fieldName, const CString &fieldValue, int type)
{
	CString value, oper, operTest, attribute, fv;

	UNREFERENCED_PARAMETER(type);
	int idx, idx2;

	if (fieldValue.GetLength() == 0)
		return;

	// Parse the operator
	// Trim the front
	value = fieldValue;
	value.TrimLeft();
	fv = value;

	if ( (value.Left(2) == "<=" ||
		value.Left(2) == ">=" ||
		value.Left(2) == "!=" ||
		value.Left(2) == "<>") &&
		value.GetAt(2) != ' ') {
		value.Insert(2, " ");
		fv.Insert(2, " ");
	}	
	else if ( (value.Left(1) == "<" ||
		value.Left(1) == ">" ||
		value.Left(1) == "=") &&
		value.GetAt(1) != ' ') {
		value.Insert(1, " ");
		fv.Insert(1, " ");
	}


	idx = value.Find(" ");
	// If there are no spaces, then there isn't an operator so default to =
	if (idx < 0) {
		oper = "=";
		value = fv;
	}
	// Next, look for a two word operator
	else {
		idx2 = value.Find(" ", idx+1);
		if (idx2 < 0) {		
			// There's only one space so check for a single word operator
			operTest = value.Left(idx);
			operTest.MakeLower();
			if (m_OperatorMap.Lookup(operTest, oper)) {
				// Found a single word operator
				value = fv.Mid(idx+1);	// get rid of the operator
			}
			else {
				// The one-word sequence didn't match so default to =
				oper = "=";
				value = fv;
			}
		}
		else {
			// Found a two-word sequence at the front
			operTest = value.Left(idx2);
			operTest.MakeLower();
			if (m_OperatorMap.Lookup(operTest, oper)) {
				// Found a two word operator
				value = fv.Mid(idx2+1);	// get rid of the operator
			}
			else {
				// The two-word sequence didn't match so look for a one-word
				operTest = value.Left(idx);
				operTest.MakeLower();
				if (m_OperatorMap.Lookup(operTest, oper)) {
					// Found a single word operator
					value = fv.Mid(idx+1);	// get rid of the operator
				}
				else {
					// The one-word sequence didn't match so default to =
					oper = "=";
					value = fv;
				}				
			}
		}
	}

	attribute.Format("%s.%s", objectName, fieldName);
	query.AddQueryAttr(attribute, value, "AND", oper);

	return;
}


int CProductDataService::Delete(long productDBID)
{
	int rc;

	try {
		rc = DeleteProductByID(productDBID);
		if (rc < 0)  {
			throw Ssa_Exception("Error deleting product.", __FILE__, __LINE__, 200);
			return rc;
		}
	}
	catch (Ssa_Exception e) {
		throw e;
		return -1;
	}
	catch (...) {
		throw Ssa_Exception("Generic error deleting product.", __FILE__, __LINE__, 200);
		return -1;
	}

	return 0;

}

int CProductDataService::Update(CProductPack &product)
{
	CStringArray statements;
	CString statement;

	statement.Format("update dbproductpack set "
		"description = '%s', weight = %f," "movement = %f," "width = %f,"
		"length = %f," "height = %f," "ishazard = %d," "unitofissue = %d,"
		"ispicktobelt = %d," "optimizeby = %d," "balanceonhand = %f,"
		"numberinpallet = %d," "rotatexaxis = %d," "rotateyaxis = %d,"
		"rotatezaxis = %d," "isassignmentlocked = %d," "maxstacknumber = %d,"
		"eachlength = %f," "eachwidth = %f," "eachheight = %f,"
		"innerlength = %f," "innerwidth = %f," "innerheight = %f,"
		"innerpack = %d," "casepack = %d," "wmsproductid = '%s',"
		"wmsproductdetailid = '%s'," "numberofhits = %f," "changedate = SYSDATE, "
		"lastuserid = 1, status = %d, isactive = %d, trace = %d, "
		"previousmovement = %f, previousbalanceonhand = %f, "
		"commoditytype = '%s', crushfactor = '%s', "
		"nestedwidth = %f, nestedlength = %f, nestedHeight = %f "
		"where dbproductpackid = %d", 
		product.m_Description, product.m_Weight, product.m_Movement, product.m_CaseWidth,
		product.m_CaseLength, product.m_CaseHeight, product.m_IsHazard,
		product.m_UnitOfIssue, product.m_IsPickToBelt, product.m_OptimizeBy,
		product.m_BalanceOnHand, product.m_NumberInPallet, product.m_RotateXAxis,
		product.m_RotateYAxis, product.m_RotateZAxis, product.m_IsAssignmentLocked,
		product.m_MaxStackNumber, product.m_EachLength, product.m_EachWidth,
		product.m_EachHeight, product.m_InnerLength, product.m_InnerWidth,
		product.m_InnerHeight, product.m_InnerPack, product.m_CasePack,
		product.m_WMSProductID, product.m_WMSProductDetailID, product.m_NumberOfHits,
		product.m_Status, product.m_IsActive, product.m_Trace,
		product.m_PreviousMovement, product.m_PreviousBOH, 
		product.m_CommodityType, product.m_CrushFactor,
		product.m_NestedWidth, product.m_NestedLength, product.m_NestedHeight,
		product.m_ProductPackDBID);

	statements.Add(statement);

	statement.Format("update dbprodcontainer set "
		"description = '%s', "
		"width = %f, "
		"length = %f, "
		"height = %f, "
		"ti = %d, "
		"hi = %d, "
		"IsWidthOverride = %d, "
		"IsLengthOverride = %d, "
		"IsHeightOverride = %d, "
		"ChangeDate = SYSDATE, "
		"LastUserID = 1 "
		"where dbprodcontainerid = %d",
		product.m_Description, product.m_Container.m_Width, product.m_Container.m_Length,
		product.m_Container.m_Height, product.m_Container.m_Ti, product.m_Container.m_Hi,
		product.m_Container.m_IsWidthOverride, product.m_Container.m_IsLengthOverride,
		product.m_Container.m_IsHeightOverride, product.m_Container.m_ProductContainerDBID);

	statements.Add(statement);

	for (int i=0; i < product.m_UDFList.GetSize(); ++i) {
		statement.Format("update dbprodpkudfval set "
			"Value = '%s', IntegerValue = %d, FloatValue = %f, "
			"ChangeDate = SYSDATE, "
			"LastUserId = 1 "
			"where dbprodpkudfvalid = %d",
			product.m_UDFList[i]->m_Value,
			product.m_UDFList[i]->m_IntegerValue,
			product.m_UDFList[i]->m_FloatValue,
			product.m_UDFList[i]->m_ValueID);
		statements.Add(statement);
	}

	return dataAccessService.ExecuteStatements("UpdateProduct", statements);

}



// This method is only building the query - it doesn't try to get every field
// only the basic table ones and the ones that are involved in the query
int CProductDataService::BuildQuery(CString &sql, qqhSLOTQuery &query)
{
	CString selectList, fromList, whereList, attrWhereList;
	//CString pgSelectList, pgFromList, pgWhereList;
	//CString locPGSelectList, locPGFromList, locPGWhereList;
	//CString locSelectList, locFromList, locWhereList;
	CString tmp, objectName, attrName, value, typeStr;
	qqhSLOTQueryAttr queryAttr;
	int idx, type, i, k;
	int facilityID = controlService.GetCurrentFacilityDBId();
	unsigned short j;
	BOOL found, isUDF;
	BOOL bAddSolution, bAddProductGroup, bAddLocation, bAddLocProductGroup, bAddBayProfile, bAddLevelProfile;
	BOOL bAddSolutionOpt, bAddLocationOpt, bAddLocProductGroupOpt, bAddBayProfileOpt, bAddLevelProfileOpt;

	CWordArray addedUDFs;
	CString oper, temp;

	bAddSolution = bAddProductGroup = bAddLocation = bAddLocProductGroup = bAddBayProfile = bAddLevelProfile = FALSE;
	bAddSolutionOpt = bAddLocProductGroupOpt = bAddLocationOpt = bAddLocProductGroupOpt = bAddBayProfileOpt = bAddLevelProfileOpt = FALSE;

	// brd - added unique so that multiple facing products won't get multiple rows returned
	selectList.Format("select unique pp.dbproductpackid, pp.description, pp.wmsproductid, "
		"pp.wmsproductdetailid, pp.casepack, pp.innerpack, pp.unitofissue, pp.isassignmentlocked, "
		"pp.movement, pp.balanceonhand, pp.optimizeby, pp.numberofhits, pp.ishazard, "
		"pp.ispicktobelt, pp.rotatexaxis, pp.rotateyaxis, pp.rotatezaxis, "
		"pp.width, pp.length, pp.height, pp.innerwidth, pp.innerlength, pp.innerheight, "
		"pp.eachwidth, pp.eachlength, pp.eachheight, pp.weight, pp.maxstacknumber, pp.numberinpallet, "
		"pp.status, pp.isactive, pp.trace, pp.previousmovement, pp.previousbalanceonhand, "
		"pp.commoditytype, pp.crushfactor, pp.externalkey, "
		"pp.lastoptimizeattribute, pp.nestedwidth, pp.nestedlength, pp.nestedheight, "
		"pc.dbprodcontainerid, pc.width, pc.length, pc.height, pc.ti, pc.hi, "
		"pc.isheightoverride");

	fromList.Format(" from dbproduct p, dbproductpack pp, dbprodcontainer pc");
	
	whereList.Format(" where p.dbfacilityid = %d"
		" and pp.dbproductid = p.dbproductid "
		" and pc.dbproductpackid = pp.dbproductpackid", facilityID);
	
	for (i=0; i < query.getAttrList().GetSize(); ++i) {
		queryAttr = query.getAttrList()[i];
		tmp = queryAttr.getAttrName();
		idx = tmp.Find(".");
		objectName = tmp.Left(idx);
		attrName = tmp.Right(tmp.GetLength()-(idx+1));

		found = FALSE;
		isUDF = FALSE;

		temp = queryAttr.getQueryOperator();
		temp.MakeLower();
		value = queryAttr.getAttrValue();

		// see if the operator is known, if so store it in oper
		if (! m_OperatorMap.Lookup(temp, oper))
			continue;

		if (objectName.CompareNoCase(TB_PRODUCT) == 0) {
			objectName = "pp";
			found = TRUE;
			m_AttributeTypeMap.Lookup(attrName, typeStr);
			type = atoi(typeStr);
		}
		else if (objectName.CompareNoCase(TB_PRODUCTCONTAINER) == 0) {
			objectName = "pc";
			found = TRUE;
			m_AttributeTypeMap.Lookup(attrName, typeStr);
			type = atoi(typeStr);
		}
		else if (objectName.CompareNoCase(TB_PRODUCTGROUP) == 0) {
			found = TRUE;
			type = DT_INT;
			if (oper != "not exists")
				bAddProductGroup = TRUE;
		}
		else if (objectName.CompareNoCase(TB_PRODUCTGROUPLOC) == 0) {
			found = TRUE;
			type = DT_INT;
			if (oper != "not exists") {
				bAddLocation = TRUE;
				bAddLocProductGroup = TRUE;
			}
		}
		else if (objectName.CompareNoCase(TB_LOCATION) == 0) {
			found = TRUE;
			type = DT_STRING;
			if (oper != "not exists")
				bAddLocation = TRUE;
		}
		else if (objectName.CompareNoCase(TB_BAYPROFILE) == 0) {
			found = TRUE;
			type = DT_INT;
			bAddBayProfile = TRUE;
		}
		else if (objectName.CompareNoCase(TB_LEVELPROFILE) == 0) {
			found = TRUE;
			type = DT_INT;
			bAddLevelProfile = TRUE;
		}
		else if (objectName.CompareNoCase(TB_SOLUTION) == 0) {
			found = TRUE;
			type = DT_FLOAT;
			bAddSolution = TRUE;
		}
		else if (objectName.CompareNoCase(TB_PRODUCTGROUPLOC_OPT) == 0) {
			found = TRUE;
			type = DT_INT;
			if (oper != "not exists") {
				bAddLocationOpt = TRUE;
				bAddLocProductGroupOpt = TRUE;
			}
			objectName = "ProductGroupLocOpt";
		}
		else if (objectName.CompareNoCase(TB_LOCATION_OPT) == 0) {
			found = TRUE;
			type = DT_STRING;
			if (oper != "not exists")
				bAddLocationOpt = TRUE;
			objectName = "LocationOpt";
		}
		else if (objectName.CompareNoCase(TB_BAYPROFILE_OPT) == 0) {
			found = TRUE;
			type = DT_INT;
			bAddBayProfileOpt = TRUE;
			objectName = "BayProfileOpt";
		}
		else if (objectName.CompareNoCase(TB_LEVELPROFILE_OPT) == 0) {
			found = TRUE;
			type = DT_INT;
			bAddLevelProfileOpt = TRUE;
			objectName = "LevelProfileOpt";
		}
		else if (objectName.CompareNoCase(TB_SOLUTION_OPT) == 0) {
			found = TRUE;
			type = DT_FLOAT;
			bAddSolutionOpt = TRUE;
			objectName = "SolutionOpt";
		}
		else if (objectName.CompareNoCase(TB_PRODUCTUDFVAL) == 0) {
			isUDF = TRUE;
			for (j=0; j < (unsigned short)m_UDFList.GetSize(); ++j) {
				if (attrName.CompareNoCase(m_UDFList[j]->m_Name) == 0) {
					objectName.Format("VAL%d", j);
					found = TRUE;
					type = m_UDFList[j]->m_Type;
					switch (type) {
					case DT_INT:
						attrName = "IntegerValue";
						break;
					case DT_FLOAT:
						attrName = "FloatValue";
						break;
					default:
						attrName = "Value";
						break;
					}
					BOOL found2 = FALSE;
					for (k=0; k < addedUDFs.GetSize(); ++k) {
						if (addedUDFs[k] == j) {
							found2 = TRUE;
							break;
						}
					}
					if (! found2)
						addedUDFs.Add(j);
			

					break;
				}
			}
		}

		if (! found)
			continue;

		// rotated dimensions are stored as negatives so reverse them here
		if (objectName == TB_SOLUTION_OPT)
			tmp.Format(" and 0 - %s.%s %s %s", objectName, attrName, oper, value);
		else {
			if (oper == "exists")
				tmp = "";  // don't add an actual where, just use the default where
			else if (oper == "not exists") {
				// if looking for a product with no location, we really
				// want to look for no solution
				if (objectName == TB_LOCATION) {
					tmp.Format(" and not exists ( select dbproductpackid "
						"from dbslotsolution "
						"where dbslotsolution.dbproductpackid = pp.dbproductpackid "
						"and dbslotsolution.origin = %d) ",
						CSolution::Baseline);
				}

				else if (objectName == "LocationOpt") {
					tmp.Format(" and not exists ( select dbproductpackid "
						"from dbslotsolution dbslotsolutionopt "
						"where dbslotsolutionopt.dbproductpackid = pp.dbproductpackid "
						"and dbslotsolutionopt.origin = %d) ",
						CSolution::Optimize);
				}

				// special case for location product gruop
				else if (objectName == TB_PRODUCTGROUPLOC) {
					tmp.Format(" and not exists ( select dblevelid "
						"from dbslotgrpbay "
						"where dbslotgrpbay.dblevelid = %s.dblevelid) ", TB_LOCATION);
				}
				else if (objectName == TB_PRODUCTGROUPLOC_OPT) {
					tmp.Format(" and not exists ( select dblevelid "
						"from dbslotgrpbay "
						"where dbslotgrpbay.dblevelid = %s.dblevelid) ", "LocationOpt");
				}
				else if (objectName == TB_PRODUCTGROUP) {
					tmp.Format(" and not exists ( select dbproductpackid "
						"from dbprodslotgroup "
						"where dbprodslotgroup.dbproductpackid = pp.dbproductpackid) ");
				}
				else {
					tmp.Format(" and not exists ( select dbproductpackid "
						"from %s "
						"where %s.dbproductpackid = pp.dbproductpackid) ",
						objectName, objectName);
				}
			}
			else {
				// Right now we require the calling program to add quotes to the
				// between value if needed
				if (oper.CompareNoCase("between") == 0 || oper.CompareNoCase("not between") == 0)
					tmp.Format(" and %s.%s %s %s", objectName, attrName, oper, value);
				// like operator must have quotes around value
				else if (type == DT_STRING || type == DT_LIST || oper.CompareNoCase("like") == 0)
					tmp.Format(" and %s.%s %s '%s'", objectName, attrName, oper, value);
				else
					tmp.Format(" and %s.%s %s %s", objectName, attrName, oper, value);
			}
		}

		attrWhereList += tmp;

	}

	// Add any additional tables that are needed
	if (bAddProductGroup) {
		tmp.Format(", dbprodslotgroup psg, %s ", TB_PRODUCTGROUP);
		fromList += tmp;
		tmp.Format(" and pp.dbproductpackid = psg.dbproductpackid "
			"and psg.dbslottinggroupid = %s.dbslottinggroupid ", TB_PRODUCTGROUP);
		whereList += tmp;
	}
	
	// Baseline solutions
	if (bAddBayProfile) {
		bAddSolution = TRUE;
		bAddLocation = TRUE;
		bAddLevelProfile = TRUE;
		tmp.Format(", %s ", TB_BAYPROFILE);
		fromList += tmp;	
		tmp.Format(" and %s.dbbayprofileid = %s.dbbayprofileid ", TB_LEVELPROFILE, TB_BAYPROFILE);
		whereList += tmp;
	} 
	if (bAddLevelProfile) {
		bAddSolution = TRUE;
		bAddLocation = TRUE;
		tmp.Format(", %s, %s", TB_LEVEL, TB_LEVELPROFILE);
		fromList += tmp;
		tmp.Format(" and %s.dblevelid = %s.dblevelid "
			" and %s.dblevelprofileid = %s.dblevelprofileid ", 
			TB_LEVEL, TB_LOCATION, TB_LEVEL, TB_LEVELPROFILE);
		whereList += tmp;
	}
	if (bAddLocProductGroup) {
		bAddSolution = TRUE;
		bAddLocation = TRUE;
		tmp.Format(", dbslotgrpbay sgb, %s DBSlottingGroupLoc", TB_PRODUCTGROUP);
		fromList += tmp;
		tmp.Format(" and %s.dblevelid = sgb.dblevelid "
			" and sgb.dbslottinggroupid = DBSlottingGroupLoc.dbslottinggroupid ", TB_LOCATION);
		whereList += tmp;
	}
	
	if (bAddLocation) {
		bAddSolution = TRUE;
		tmp.Format(", %s", TB_LOCATION);
		fromList += tmp;
		tmp.Format(" and %s.dblocationid = %s.dblocationid ", TB_SOLUTION, TB_LOCATION);
		whereList += tmp;
	}
	
	if (bAddSolution) {
		tmp.Format(", dbslotsolution %s", TB_SOLUTION);
		fromList += tmp;
		tmp.Format(" and pp.dbproductpackid = %s.dbproductpackid "
			"and %s.origin = %d ", TB_SOLUTION, TB_SOLUTION, CProductDataService::Baseline);
		whereList += tmp;
	}

	// Optimized solutions
	if (bAddBayProfileOpt) {
		bAddSolutionOpt = TRUE;
		bAddLocationOpt = TRUE;
		bAddLevelProfileOpt = TRUE;
		tmp.Format(", %s BayProfileOpt ", TB_BAYPROFILE);
		fromList += tmp;	
		tmp.Format(" and %s.dbbayprofileid = %s.dbbayprofileid ", "LevelProfileOpt", "BayProfileOpt");
		whereList += tmp;
	} 
	if (bAddLevelProfileOpt) {
		bAddSolutionOpt = TRUE;
		bAddLocationOpt = TRUE;
		tmp.Format(", %s LevelOpt, %s LevelProfileOpt", TB_LEVEL, TB_LEVELPROFILE);
		fromList += tmp;
		tmp.Format(" and %s.dblevelid = %s.dblevelid "
			" and %s.dblevelprofileid = %s.dblevelprofileid ", 
			"LevelOpt", "LocationOpt", "LevelOpt", "LevelProfileOpt");
		whereList += tmp;
	}
	if (bAddLocProductGroupOpt) {
		bAddSolutionOpt = TRUE;
		bAddLocationOpt = TRUE;
		tmp.Format(", dbslotgrpbay sgbopt, %s ProductGroupLocOpt ", TB_PRODUCTGROUP);
		fromList += tmp;
		tmp.Format(" and %s.dblevelid = sgbopt.dblevelid "
			" and sgbopt.dbslottinggroupid = ProductGroupLocOpt.dbslottinggroupid ", "LocationOpt");
		whereList += tmp;
	}
	
	if (bAddLocationOpt) {
		bAddSolutionOpt = TRUE;
		tmp.Format(", %s LocationOpt ", TB_LOCATION);
		fromList += tmp;
		tmp.Format(" and %s.dblocationid = %s.dblocationid ", "SolutionOpt", "LocationOpt");
		whereList += tmp;
	}
	
	if (bAddSolutionOpt) {
		tmp.Format(", dbslotsolution %s", "SolutionOpt");
		fromList += tmp;
		tmp.Format(" and pp.dbproductpackid = %s.dbproductpackid "
			"and %s.origin = %d ", "SolutionOpt", "SolutionOpt", CProductDataService::Optimize);
		whereList += tmp;
	}
	// Add any udfs that are involved in the query
	for (i=0; i < addedUDFs.GetSize(); ++i) {
		j = addedUDFs[i];

		tmp.Format(", 'UDFSTART', UDF%d.description UDF%dname, UDF%d.type UDF%dtype,"
			" VAL%d.value UDF%dvalue, UDF%d.dbprodpkudflistid UDF%dListId,"
			" VAL%d.dbprodpkudfvalid UDF%dValId, UDF%d.DefaultValue UDF%dDefaultValue",
			j, j, j, j, j, j, j, j, j, j, j, j);
		selectList += tmp;

		tmp.Format(", dbprodpkudfval VAL%d, dbprodpkudflist UDF%d", j, j);
		fromList += tmp;

		tmp.Format(" and UDF%d.dbprodpkudflistid = %d"
			" and VAL%d.dbprodpkudflistid = UDF%d.dbprodpkudflistid"
			" and VAL%d.dbproductpackid = pp.dbproductpackid",
			j, m_UDFList[j]->m_ListID, j, j, j);
		whereList += tmp;

	}

	whereList += attrWhereList;

	sql = selectList;
	sql += fromList;
	sql += whereList;


	return 0;


}

void CProductDataService::AddQueryAttribute(qqhSLOTQuery &query, const CString &objectName, const CString &fieldName, const CString &fieldValue, const CString &oper)
{
	CString attribute;

	attribute.Format("%s.%s", objectName, fieldName);

	query.AddQueryAttr(attribute, fieldValue, "AND", oper);

}

int CProductDataService::GetBayHandlesForProducts(CStringArray &dbids, CStringArray &handles)
{
	CString sql, temp;

	// Note: the maximum number of items in a list in Oracle is 254
	for (int i=0; i < dbids.GetSize() / 254 + 1; ++i) {
		temp = "";
		for (int j=0; j < 254 && (i*254+j) < dbids.GetSize(); ++j) {
			temp += dbids[i*254+j];
			temp += ",";
		}
		temp.TrimRight(",");

		sql.Format("select unique b.acadhandle "
			"from dbbay b, dblevel le, dblocation l, dbslotsolution ss "
			"where ss.dbproductpackid in (%s) "
			"and ss.dblocationid = l.dblocationid "
			"and l.dblevelid = le.dblevelid "
			"and le.dbbayid = b.dbbayid", temp);

		dataAccessService.ExecuteQuery("GetBayHandlesForProducts", sql, handles);
	}

	return handles.GetSize();

}

int CProductDataService::GetProductFacings(long productId, CStringArray &facings, int origin)
{
	CString sql, originClause;

	sql.Format("select l.description "
		"from dblocation l, dbslotsolution s "
		"where s.dbproductpackid = %d "
		"and s.dblocationid = l.dblocationid "
		"and s.isprimary = 0 "
		"and Origin = %d "
		"order by l.description", productId, origin);


	return dataAccessService.ExecuteQuery("GetProductFacings", sql, facings);

}

int CProductDataService::GetProductAttributes(int facilityDBId, CStringArray &attributes)
{
	CString tempString;
	CSsaStringArray tempSendArray;
	CSsaStringArray tempRecvArray;
	CString sendString;
	int i = 0;
	CString queryText;
	CString attribute;

	/////////////////////////////////////////////////////////////
	// build the data to send to the Forte connection server
	/////////////////////////////////////////////////////////////

	//name, type, min, max, default, listValues, attributeID;
	attribute.Format("%s|%d|%f|%f|%s|%s|%d|%s|%s|%s|", "Description", DT_STRING, 0.0, 0.0, "", "", 0, TB_PRODUCT, "Product Name", "ProductPage_Description");
	attributes.Add(attribute);
	attribute.Format("%s|%d|%f|%f|%s|%s|%d|%s|%s|%s|", "WMSProductID", DT_STRING, 0.0, 0.0, "", "", 0, TB_PRODUCT, "WMS Product ID", "ProductPage_WMSProductID");
	attributes.Add(attribute);
	attribute.Format("%s|%d|%f|%f|%s|%s|%d|%s|%s|%s|", "WMSProductDetailID", DT_STRING, 0.0, 0.0, "", "", 0, TB_PRODUCT, "WMS Product Detail ID", "ProductPage_WMSProductDetailID");
	attributes.Add(attribute);
	attribute.Format("%s|%d|%f|%f|%s|%s|%d|%s|%s|%s|", "Weight", DT_FLOAT, 0.0, 99999999.0, "0", "", 0, TB_PRODUCT, "Weight", "ProductPage_Weight");
	attributes.Add(attribute);
	attribute.Format("%s|%d|%f|%f|%s|%s|%d|%s|%s|%s|", "Movement", DT_FLOAT, 0.0, 99999999.0, "0", "", 0, TB_PRODUCT, "Movement", "ProductPage_Movement");
	attributes.Add(attribute);
	attribute.Format("%s|%d|%f|%f|%s|%s|%d|%s|%s|%s|", "Width", DT_FLOAT, 0.0, 99999999.0, "0", "", 0, TB_PRODUCT, "Case Width", "ProductPage_CaseWidth");
	attributes.Add(attribute);
	attribute.Format("%s|%d|%f|%f|%s|%s|%d|%s|%s|%s|", "Length", DT_FLOAT, 0.0, 99999999.0, "0", "", 0, TB_PRODUCT, "Case Length", "ProductPage_CaseLength");
	attributes.Add(attribute);
	attribute.Format("%s|%d|%f|%f|%s|%s|%d|%s|%s|%s|", "Height", DT_FLOAT, 0.0, 99999999.0, "0", "", 0, TB_PRODUCT, "Case Height", "ProductPage_CaseHeight");
	attributes.Add(attribute);
	attribute.Format("%s|%d|%f|%f|%s|%s|%d|%s|%s|%s|", "IsHazard", DT_LIST, 0.0, 0.0, "0", "False^0,True^1,", 0, TB_PRODUCT, "Hazard Flag", "ProductPage_IsHazard");
	attributes.Add(attribute);
	attribute.Format("%s|%d|%f|%f|%s|%s|%d|%s|%s|%s|", "UnitOfIssue", DT_LIST, 0.0, 0.0, "2", "Each^0,Inner^1,Case^2,Pallet^3,", 0, TB_PRODUCT, "Unit of Issue", "ProductPage_UnitOfIssue");
	attributes.Add(attribute);
	attribute.Format("%s|%d|%f|%f|%s|%s|%d|%s|%s|%s|", "IsPickToBelt", DT_LIST, 0.0, 0.0, "0", "False^0,True^1,", 0, TB_PRODUCT, "Pick-to-Belt Flag", "ProductPage_IsPickToBelt");
	attributes.Add(attribute);
	attribute.Format("%s|%d|%f|%f|%s|%s|%d|%s|%s|%s|", "OptimizeBy", DT_LIST, 0.0, 0.0, "0", "Cube^0,Labor^1,", 0, TB_PRODUCT, "Optimize Method", "ProductPage_OptimizeBy");
	attributes.Add(attribute);
	attribute.Format("%s|%d|%f|%f|%s|%s|%d|%s|%s|%s|", "BalanceOnHand", DT_FLOAT, 0.0, 99999999.0, "0", "", 0, TB_PRODUCT, "Balance on Hand", "ProductPage_BalanceOnHand");
	attributes.Add(attribute);
	attribute.Format("%s|%d|%f|%f|%s|%s|%d|%s|%s|%s|", "NumberInPallet", DT_INT, 0.0, 99999999.0, "0", "", 0, TB_PRODUCTCONTAINER, "Number In Pallet", "ProductPage_NumberInPallet");
	attributes.Add(attribute);
	attribute.Format("%s|%d|%f|%f|%s|%s|%d|%s|%s|%s|", "IsAssignmentLocked", DT_LIST, 0.0, 0.0, "0", "False^0,True^1,", 0, TB_PRODUCT, "Assignment Locked Flag", "ProductPage_IsAssignmentLocked");
	attributes.Add(attribute);
	attribute.Format("%s|%d|%f|%f|%s|%s|%d|%s|%s|%s|", "RotateXAxis", DT_LIST, 0.0, 0.0, "0", "False^0,True^1,", 0, TB_PRODUCT, "Allow Height-Length Swap", "ProductPage_RotateXAxis");
	attributes.Add(attribute);
	attribute.Format("%s|%d|%f|%f|%s|%s|%d|%s|%s|%s|", "RotateYAxis", DT_LIST, 0.0, 0.0, "0", "False^0,True^1,", 0, TB_PRODUCT, "Allow Height-Width Swap", "ProductPage_RotateYAxis");
	attributes.Add(attribute);
	attribute.Format("%s|%d|%f|%f|%s|%s|%d|%s|%s|%s|", "RotateZAxis", DT_LIST, 0.0, 0.0, "0", "False^0,True^1,", 0, TB_PRODUCT, "Allow Width-Length Swap", "ProductPage_RotateZAxis");
	attributes.Add(attribute);
	attribute.Format("%s|%d|%f|%f|%s|%s|%d|%s|%s|%s|", "EachWidth", DT_FLOAT, 0.0, 99999999.0, "0", "", 0, TB_PRODUCT, "Each Width", "ProductPage_EachWidth");
	attributes.Add(attribute);
	attribute.Format("%s|%d|%f|%f|%s|%s|%d|%s|%s|%s|", "EachLength", DT_FLOAT, 0.0, 99999999.0, "0", "", 0, TB_PRODUCT, "Each Length", "ProductPage_EachLength");
	attributes.Add(attribute);
	attribute.Format("%s|%d|%f|%f|%s|%s|%d|%s|%s|%s|", "EachHeight", DT_FLOAT, 0.0, 99999999.0, "0", "", 0, TB_PRODUCT, "Each Height", "ProductPage_EachHeight");
	attributes.Add(attribute);
	attribute.Format("%s|%d|%f|%f|%s|%s|%d|%s|%s|%s|", "InnerWidth", DT_FLOAT, 0.0, 99999999.0, "0", "", 0, TB_PRODUCT, "Inner Width", "ProductPage_InnerWidth");
	attributes.Add(attribute);
	attribute.Format("%s|%d|%f|%f|%s|%s|%d|%s|%s|%s|", "InnerLength", DT_FLOAT, 0.0, 99999999.0, "0", "", 0, TB_PRODUCT, "Inner Length", "ProductPage_InnerLength");
	attributes.Add(attribute);
	attribute.Format("%s|%d|%f|%f|%s|%s|%d|%s|%s|%s|", "InnerHeight", DT_FLOAT, 0.0, 99999999.0, "0", "", 0, TB_PRODUCT, "Inner Height", "ProductPage_InnerHeight");
	attributes.Add(attribute);
	attribute.Format("%s|%d|%f|%f|%s|%s|%d|%s|%s|%s|", "CasePack", DT_INT, 0.0, 99999999.0, "0", "", 0, TB_PRODUCT, "Case Pack", "ProductPage_CasePack");
	attributes.Add(attribute);
	attribute.Format("%s|%d|%f|%f|%s|%s|%d|%s|%s|%s|", "InnerPack", DT_INT, 0.0, 99999999.0, "0", "", 0, TB_PRODUCT, "Inner Pack", "ProductPage_InnerPack");
	attributes.Add(attribute);
	attribute.Format("%s|%d|%f|%f|%s|%s|%d|%s|%s|%s|", "MaxStackNumber", DT_INT, 0.0, 99999999.0, "0", "", 0, TB_PRODUCT, "Max Stack Number", "ProductPage_MaxStackNumber");
	attributes.Add(attribute);
	attribute.Format("%s|%d|%f|%f|%s|%s|%d|%s|%s|%s|", "NumberOfHits", DT_FLOAT, 0.0, 99999999.0, "0", "", 0, TB_PRODUCT, "Number of Hits", "ProductPage_NumberOfHits");
	attributes.Add(attribute);
	attribute.Format("%s|%d|%f|%f|%s|%s|%d|%s|%s|%s|", "Width", DT_FLOAT, 0.0, 99999999.0, "0", "", 0, TB_PRODUCTCONTAINER, "Container Width", "ContainerPage_ContainerWidth");
	attributes.Add(attribute);
	attribute.Format("%s|%d|%f|%f|%s|%s|%d|%s|%s|%s|", "Length", DT_FLOAT, 0.0, 99999999.0, "0", "", 0, TB_PRODUCTCONTAINER, "Container Length", "ContainerPage_ContainerLength");
	attributes.Add(attribute);
	attribute.Format("%s|%d|%f|%f|%s|%s|%d|%s|%s|%s|", "Height", DT_FLOAT, 0.0, 99999999.0, "0", "", 0, TB_PRODUCTCONTAINER, "Container Height", "ContainerPage_ContainerHeight");
	attributes.Add(attribute);
	attribute.Format("%s|%d|%f|%f|%s|%s|%d|%s|%s|%s|", "IsHeightOverride", DT_LIST, 0.0, 0.0, "0", "False^0,True^1,", 0, TB_PRODUCTCONTAINER, "Container Height Override Flag", "ContainerPage_IsHeightOverride");
	attributes.Add(attribute);
	attribute.Format("%s|%d|%f|%f|%s|%s|%d|%s|%s|%s|", "TI", DT_INT, 0.0, 99999999.0, "0", "", 0, TB_PRODUCTCONTAINER, "Storage TI", "ContainerPage_TI");
	attributes.Add(attribute);
	attribute.Format("%s|%d|%f|%f|%s|%s|%d|%s|%s|%s|", "HI", DT_INT, 0.0, 99999999.0, "0", "", 0, TB_PRODUCTCONTAINER, "Storage HI", "ContainerPage_HI");
	attributes.Add(attribute);

	/*
	attribute.Format("%s|%d|%f|%f|%s|%s|%d|%s|%s|%s|", "Status", DT_LIST, 0.0, 0.0, "0", "Integrated^1,Not Integrated^0,", 0, TB_PRODUCT, "Integration Status", "ProductPage_Status");
	attributes.Add(attribute);
	*/
	attribute.Format("%s|%d|%f|%f|%s|%s|%d|%s|%s|%s|", "IsActive", DT_LIST, 0.0, 0.0, "0", "False^0,True^1,", 0, TB_PRODUCT, "Is Active", "ProductPage_IsActive");
	attributes.Add(attribute);
	attribute.Format("%s|%d|%f|%f|%s|%s|%d|%s|%s|%s|", "Trace", DT_LIST, 0.0, 0.0, "0", "False^0,True^1,", 0, TB_PRODUCT, "Trace Enabled", "ProductPage_Trace");
	attributes.Add(attribute);
	/*
	attribute.Format("%s|%d|%f|%f|%s|%s|%d|%s|%s|%s|", "PreviousMovement", DT_FLOAT, 0.0, 99999999.0, "0", "", 0, TB_PRODUCT, "Previous Movement", "ProductPage_PreviousMovement");
	attributes.Add(attribute);
	attribute.Format("%s|%d|%f|%f|%s|%s|%d|%s|%s|%s|", "PreviousBalanceOnHand", DT_FLOAT, 0.0, 99999999.0, "0", "", 0, TB_PRODUCT, "Previous BOH", "ProductPage_PreviousBOH");
	attributes.Add(attribute);
	*/
	attribute.Format("%s|%d|%f|%f|%s|%s|%d|%s|%s|%s|", "CommodityType", DT_STRING, 0.0, 0.0, "", "", 0, TB_PRODUCT, "Commodity Type", "ProductPage_CommodityType");
	attributes.Add(attribute);
	attribute.Format("%s|%d|%f|%f|%s|%s|%d|%s|%s|%s|", "CrushFactor", DT_STRING, 0.0, 0.0, "", "", 0, TB_PRODUCT, "Crush Factor", "ProductPage_CrushFactor");
	attributes.Add(attribute);
/*
	attribute.Format("%s|%d|%f|%f|%s|%s|%d|%s|%s|%s|", "NestedWidth", DT_FLOAT, 0.0, 99999999.0, "0", "", 0, TB_PRODUCT, "Nested Width", "ProductPage_NestedWidth");
	attributes.Add(attribute);
	attribute.Format("%s|%d|%f|%f|%s|%s|%d|%s|%s|%s|", "NestedLength", DT_FLOAT, 0.0, 99999999.0, "0", "", 0, TB_PRODUCT, "Nested Length", "ProductPage_NestedLength");
	attributes.Add(attribute);
	attribute.Format("%s|%d|%f|%f|%s|%s|%d|%s|%s|%s|", "NestedHeight", DT_FLOAT, 0.0, 99999999.0, "0", "", 0, TB_PRODUCT, "Nested Height", "ProductPage_NestedHeight");
	attributes.Add(attribute);
*/

	// Get the udfs
	queryText.Format(" select l.description, l.type, 0, 99999999, l.defaultvalue, l.listvalues, l.dbprodpkudflistid, '%s', l.description, 'ProductUDF_Generic' "
		"from dbprodpkudflist l "
		"where dbfacilityid = %d", TB_PRODUCTUDFVAL, facilityDBId);
	
	dataAccessService.ExecuteQuery("GetProductAttributes", queryText, attributes);

	if ( attributes.GetSize() == 0 )
		return 0;
	else {
		return attributes.GetSize();
	}

}


int CProductDataService::QueryProductsAll(CString &sql, CStringArray &productList)
{
#if 0
	CString tempString;
	int tempInt;
	CSsaStringArray tempSendArray;
	CSsaStringArray tempRecvArray;
	CString sendString;
	int i = 0;


	sendString = "<SIO>SLOTSocketString\n";
	tempSendArray.Add(sendString);
	sendString = "<SAI>QueryProductsAll\n";
	tempSendArray.Add(sendString);
	tempString.Format("<SAI>%s\n",sql);
	tempSendArray.Add(tempString);
	sendString = "<EIO>SLOTSocketString\n";
	tempSendArray.Add(sendString);
	
	forteService.SendToForteConnection(tempSendArray,tempRecvArray,
		CString("SLOTSocketString"), 55555);

	for (i=0; i < tempRecvArray.GetSize(); i++) {
		if ( tempRecvArray[i].Find("<SAI>") != -1 ) {
			tempString = tempRecvArray[i].Mid(5);
			tempInt = tempString.GetLength();
			if (tempString.GetAt(tempInt-1) == '\n' ) {
				tempString.SetAt(tempInt-1,' ');
				tempString.TrimRight();
			}
			productList.Add(tempString);			
		}
	}
#else
	CListstring *res = NULL;
	char strMsg[4096] = {0};

	try
	{
		string *queryTmp = new string;
			*queryTmp = (LPCTSTR)sql;
		bool *b = new bool;
			*b = false;

		SLOTSessionMgr* ptr= getSessionMgrSO();
		res = ptr->RunQueryHelper(queryTmp, b);

		delete queryTmp;
		delete b;
	}
	catch(CException *e)
	{
		e->GetErrorMessage (strMsg, 4096);
		e->Delete();
		return -1;
	}
	for (int i=0;i<res->GetCount();i++)
	{
		CString tmpStr = res->GetAt(res->FindIndex(i)).c_str();
		productList.SetAtGrow(i, tmpStr);
	}
#endif

	if ( productList.GetSize() == 0 )
		return 0;
	else {
		return productList.GetSize();
	}

}

int CProductDataService::DeleteProductByID(int productDBID)
{
#if 0
	CString tempString;
	int i;
	CString sendString;
	CSsaStringArray tempSendArray;
	CSsaStringArray tempRecvArray;
	int tempInt;

	sendString = "<SIO>SLOTSocketString\n";
	tempSendArray.Add(sendString);
	sendString = "<SAI>SLOTProductPack\n";
	tempSendArray.Add(sendString);
	sendString.Format("<SAI>%d\n",productDBID);
	tempSendArray.Add(sendString);
	sendString = "<EIO>SLOTSocketString\n";
	tempSendArray.Add(sendString);

	forteService.SendToForteConnection(tempSendArray,tempRecvArray,
		CString("SLOTSocketString"), 10050);

	for (i=0; i < tempRecvArray.GetSize(); i++) {
		if ( tempRecvArray[i].Find("<SAI>") != -1 ) {
			tempString = tempRecvArray[i].Mid(5);
			tempInt = tempString.GetLength();
			if (tempString.GetAt(tempInt-1) == '\n' ) {
				tempString.SetAt(tempInt-1,' ');
				tempString.TrimRight();
			}
		}
	}

	if ( tempString == "Success" )
		return 0;
	else
		return -1;
#else
	bool ret = getSessionMgrSO()->DeleteItemHelper("SLOTProductPack",productDBID);
	if (ret == true)
		return 0;
	else
		return -1;
#endif
}

int CProductDataService::RunDataModel(int quantity, CStringArray &attributes)
{
	CString tempString;
	CSsaStringArray tempSendArray;
	CSsaStringArray tempRecvArray;
	CString sendString;
	int i = 0;
	CString cmdText;
	CStringArray resultList;
	CForteService forteService;

	/////////////////////////////////////////////////////////////
	// build the data to send to the Forte connection server
	/////////////////////////////////////////////////////////////
#if 0
	int tempInt;
	sendString = "<SIO>SLOTSocketString\n";
	tempSendArray.Add(sendString);
	sendString.Format("<SAI>%s\n", "2");
	tempSendArray.Add(sendString);
	tempString.Format("<SAI>%d\n",quantity);
	tempSendArray.Add(tempString);
	for (i=0; i < attributes.GetSize(); ++i) {
		tempString.Format("<SAI>%s\n", attributes[i]);
		tempSendArray.Add(tempString);
	}
	sendString = "<EIO>SLOTSocketString\n";
	tempSendArray.Add(sendString);
	
	forteService.SendToForteConnection(tempSendArray,tempRecvArray,
		CString("SLOTSocketString"), 2030);

	for (i=0; i < tempRecvArray.GetSize(); i++) {
		if ( tempRecvArray[i].Find("<SAI>") != -1 ) {
			tempString = tempRecvArray[i].Mid(5);
			tempInt = tempString.GetLength();
			if (tempString.GetAt(tempInt-1) == '\n' ) {
				tempString.SetAt(tempInt-1,' ');
				tempString.TrimRight();
			}
			resultList.Add(tempString);			
		}
	}

	if (resultList[0].CompareNoCase("Success") == 0)
		return 0;
	else
		return -1;

#else
	CListstringPtr attributesTmp (new CListstring);
	for (int i=0; i<attributes.GetSize(); ++i)
	{
		string s = (LPCTSTR)attributes.GetAt(i);
		attributesTmp->AddTail(s);
	}
	return getSessionMgrSO()->GenerateRandomHelper(2, attributesTmp, quantity);  // CHECK
#endif
}


int CProductDataService::UpdateProductGroup(long productDBID, long newProductGroupDBID, long oldProductGroupDBID)
{
	CString statement;
	int nextKey;

	nextKey = dataAccessService.GetNextKey("DBProdSlotGroup", 1);
	if (nextKey <= 0) {
		throw Ssa_Exception("Error getting next key for product group assignment table."
			, __FILE__, __LINE__, 200);
		return -1;
	}

	if (oldProductGroupDBID > 0) {
		statement.Format("update dbprodslotgroup "
			"set dbslottinggroupid = %d "
			"where dbproductpackid = %d",
			newProductGroupDBID, productDBID);
	}
	else {
		statement.Format("insert into dbprodslotgroup "
			"(dbprodslotgroupid, createdate, changedate, lastuserid, "
			"dbproductpackid, dbslottinggroupid) "
			"values "
			"(%d, sysdate, sysdate, 1, %d, %d)",
			nextKey, productDBID, newProductGroupDBID);
	}


	return dataAccessService.ExecuteStatement("UpdateProdSlotGroup", statement);

}



UINT CProductDataService::DeleteProductsByFacilityThread(LPVOID pParam)
{
	CString sql;

	int *param = (int *)pParam;
	int facilityId = *param;

	int rc = 0;
	
	try {
		
		sql.Format("delete from dbslotsolution "
			"where dbproductpackid in (select dbproductpackid from dbproductpackf "
			"where dbfacilityid = %d "
			"and dbslotsolution.dbproductpackid = dbproductpackf.dbproductpackid) ", facilityId);
		dataAccessService.ExecuteStatement("DeleteProductsByFacility", sql);
		
		sql.Format("delete from dbprodslotgroup "
			"where dbproductpackid in (select dbproductpackid from dbproductpackf "
			"where dbfacilityid = %d "
			"and dbprodslotgroup.dbproductpackid = dbproductpackf.dbproductpackid) ", facilityId);
		dataAccessService.ExecuteStatement("DeleteProductsByFacility", sql);
		
		sql.Format("delete from dbpass1resavail "
			"where dbproductpackid in (select dbproductpackid from dbproductpackf "
			"where dbfacilityid = %d "
			"and dbpass1resavail.dbproductpackid = dbproductpackf.dbproductpackid) ", facilityId);
		dataAccessService.ExecuteStatement("DeleteProductsByFacility", sql);
		
		sql.Format("delete from dbpass1resideal "
			"where dbproductpackid in (select dbproductpackid from dbproductpackf "
			"where dbfacilityid = %d "
			"and dbpass1resideal.dbproductpackid = dbproductpackf.dbproductpackid) ", facilityId);
		dataAccessService.ExecuteStatement("DeleteProductsByFacility", sql);
		
		sql.Format("delete from dbpass1rejection "
			"where dbproductpackid in (select dbproductpackid from dbproductpackf "
			"where dbfacilityid = %d "
			"and dbpass1rejection.dbproductpackid = dbproductpackf.dbproductpackid) ", facilityId);
		dataAccessService.ExecuteStatement("DeleteProductsByFacility", sql);
		
		sql.Format("delete from dbpass1message "
			"where dbproductpackid in (select dbproductpackid from dbproductpackf "
			"where dbfacilityid = %d "
			"and dbpass1message.dbproductpackid = dbproductpackf.dbproductpackid) ", facilityId);
		dataAccessService.ExecuteStatement("DeleteProductsByFacility", sql);
		
		sql.Format("delete from dbpass4message "
			"where dbproductpackid in (select dbproductpackid from dbproductpackf "
			"where dbfacilityid = %d "
			"and dbpass4message.dbproductpackid = dbproductpackf.dbproductpackid) ", facilityId);
		dataAccessService.ExecuteStatement("DeleteProductsByFacility", sql);
		
		sql.Format("delete from dbprodcontainer "
			"where dbproductpackid in (select dbproductpackid from dbproductpackf "
			"where dbfacilityid = %d "
			"and dbprodcontainer.dbproductpackid = dbproductpackf.dbproductpackid) ", facilityId);
		dataAccessService.ExecuteStatement("DeleteProductsByFacility", sql);
		
		sql.Format("delete from dbmove "
			"where dbproductpackid in (select dbproductpackid from dbproductpackf "
			"where dbfacilityid = %d "
			"and dbmove.dbproductpackid = dbproductpackf.dbproductpackid) ", facilityId);
		dataAccessService.ExecuteStatement("DeleteProductsByFacility", sql);
	
		sql.Format("delete from dbprodpkudfval "
			"where dbproductpackid in (select dbproductpackid from dbproductpackf "
			"where dbfacilityid = %d "
			"and dbprodpkudfval.dbproductpackid = dbproductpackf.dbproductpackid) ", facilityId);
		dataAccessService.ExecuteStatement("DeleteProductsByFacility", sql);
			
		sql.Format("delete from dbproductpackf "
			"where dbfacilityid = %d ", facilityId);
		dataAccessService.ExecuteStatement("DeleteProductsByFacility", sql);
		
		sql.Format("delete from dbprodudfval "
			"where dbproductid in (select dbproductid from dbproduct "
			"where dbfacilityid = %d "
			"and dbprodudfval.dbproductid = dbproduct.dbproductid) ", facilityId);
		dataAccessService.ExecuteStatement("DeleteProductsByFacility", sql);
		
		sql.Format("delete from dbproduct "
			"where dbfacilityid = %d ", facilityId);
		dataAccessService.ExecuteStatement("DeleteProductsByFacility", sql);
	}
	catch (...) {
		rc = 1;
	}
	
	*param = rc;
	
	SetEvent(g_ThreadDone);
	
	return rc;
}


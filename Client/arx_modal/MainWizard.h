#if !defined(AFX_MAINWIZARD_H__531A6CE9_8043_428A_B4D2_6DEDE355AB73__INCLUDED_)
#define AFX_MAINWIZARD_H__531A6CE9_8043_428A_B4D2_6DEDE355AB73__INCLUDED_

#if _MSC_VER > 1000
#pragma once
#endif // _MSC_VER > 1000
// MainWizard.h : header file
//

/////////////////////////////////////////////////////////////////////////////
// CMainWizard dialog

class CMainWizard : public CDialog
{
// Construction
public:
	CMainWizard(CWnd* pParent = NULL);   // standard constructor

// Dialog Data
	//{{AFX_DATA(CMainWizard)
	enum { IDD = IDD_MAIN_WIZARD };
	CString	m_Instructions;
	//}}AFX_DATA


// Overrides
	// ClassWizard generated virtual function overrides
	//{{AFX_VIRTUAL(CMainWizard)
	protected:
	virtual void DoDataExchange(CDataExchange* pDX);    // DDX/DDV support
	//}}AFX_VIRTUAL

// Implementation
protected:

	// Generated message map functions
	//{{AFX_MSG(CMainWizard)
	virtual BOOL OnInitDialog();
	virtual void OnOK();
	virtual void OnCancel();
	afx_msg void OnHelp();
	afx_msg void OnUsewizard();
	afx_msg void OnNewfacility();
	afx_msg void OnOpenfacility();
	afx_msg void OnOptimizehelp();
	//}}AFX_MSG
	DECLARE_MESSAGE_MAP()
};

//{{AFX_INSERT_LOCATION}}
// Microsoft Visual C++ will insert additional declarations immediately before the previous line.

#endif // !defined(AFX_MAINWIZARD_H__531A6CE9_8043_428A_B4D2_6DEDE355AB73__INCLUDED_)

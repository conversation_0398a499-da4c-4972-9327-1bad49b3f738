// ColoringCommands.h: interface for the CColoringCommands class.
//
//////////////////////////////////////////////////////////////////////

#if !defined(AFX_COLORINGCOMMANDS_H__43BB1D64_8FE3_4409_B8DF_A71029BB3773__INCLUDED_)
#define AFX_COLORINGCOMMANDS_H__43BB1D64_8FE3_4409_B8DF_A71029BB3773__INCLUDED_

#include "Commands.h"

#if _MSC_VER > 1000
#pragma once
#endif // _MSC_VER > 1000

class CColoringCommands : public CCommands
{
public:

	CColoringCommands();
	virtual ~CColoringCommands();
	
	static void RegisterCommands();
	static void ColorByHandle();
	static void ColorAisle();
	static void ColorByProfile();
	static void ColorModelDlg();
	static void ColorProductGroups();
	static void ColorProduct();
};

#endif // !defined(AFX_COLORINGCOMMANDS_H__43BB1D64_8FE3_4409_B8DF_A71029BB3773__INCLUDED_)

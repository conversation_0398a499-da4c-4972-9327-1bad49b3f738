// ProductGroupCriteriaValue.h: interface for the CProductGroupCriteriaValue class.
//
//////////////////////////////////////////////////////////////////////

#if !defined(AFX_PRODUCTGROUPCRITERIAVALUE_H__0DBB56D6_077B_11D5_9EC8_00C04FAC149C__INCLUDED_)
#define AFX_PRODUCTGROUPCRITERIAVALUE_H__0DBB56D6_077B_11D5_9EC8_00C04FAC149C__INCLUDED_

#if _MSC_VER > 1000
#pragma once
#endif // _MSC_VER > 1000

class CProductGroupCriteriaValue : public CObject  
{
public:
	BOOL IsEqual(CProductGroupCriteriaValue &other);
	int Parse(CString &line);
	CProductGroupCriteriaValue();
	virtual ~CProductGroupCriteriaValue();
	CProductGroupCriteriaValue& operator=(const CProductGroupCriteriaValue &other);
	long m_CriteriaValueDBID;
	CString m_Description;

};

#endif // !defined(AFX_PRODUCTGROUPCRITERIAVALUE_H__0DBB56D6_077B_11D5_9EC8_00C04FAC149C__INCLUDED_)

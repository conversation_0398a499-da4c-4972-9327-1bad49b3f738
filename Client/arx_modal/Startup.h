#if !defined(AFX_STARTUP_H__CB272F01_06E7_11D2_9BD1_0080C742D9DF__INCLUDED_)
#define AFX_STARTUP_H__CB272F01_06E7_11D2_9BD1_0080C742D9DF__INCLUDED_
#include "resource.h"
#if _MSC_VER >= 1000
#pragma once
#endif // _MSC_VER >= 1000
// Startup.h : header file
//
#include <afxext.h>

#define IDC_UNITSLIST WM_USER+10
#define IDC_WMSLIST WM_USER+11
#define IDC_INSTRUCTSTATIC WM_USER+12
#define IDC_UNITSSTATIC WM_USER+13
#define IDC_WMSSTATIC WM_USER+14
#define IDC_INSTRUCTSTATIC2 WM_USER+15
#define IDC_DESCRIPTIONSTATIC WM_USER+16
#define IDC_DESCRIPTIONSTATIC2 WM_USER+17


/////////////////////////////////////////////////////////////////////////////
// CStartup dialog

class CStartup : public CDialog
{
// Construction
public:
	HACCEL m_hAccel;
	afx_msg LONG OnAcadKeepFocus(UINT, LONG);
	BOOL m_Modeless;
	CStartup(CWnd* pParent = NULL);   // standard constructor
	~CStartup();

// Dialog Data
	//{{AFX_DATA(CStartup)
	enum { IDD = IDD_STARTUP };
	CBitmapButton	m_ctlHelpStart;
	CBitmapButton	m_ctlOpenFacility;
	CBitmapButton	m_ctlNewFacility;
	CBitmapButton	m_ctlInstructions;
	CBitmapButton	m_ctlUseWizard;
	//}}AFX_DATA

	CListBox	*m_ctlUnitsList;
	BOOL		m_UnitsListCreated;
	int         m_Units;
	CStatic 	*m_ctlUnitsStatic;
	CListBox	*m_ctlWMSList;
	int         m_WMS;
	BOOL		m_WMSListCreated;
	CStatic     *m_ctlWMSStatic;
	CStatic		*m_ctlInstruct;
	BOOL		m_InstructCreated;
	CStatic		*m_ctlInstruct2;
	WORD		m_selectedCtlId;
	CFont       *m_startingFont;
	CButton		*m_ctlDescription;
	CStatic		*m_ctlDescription2;
	BOOL		m_DescriptionCreated;
	CStatic		*m_ctlHelp;
	BOOL		m_HelpCreated;
	CStatic		*m_ctlHelp2;

// Overrides
	// ClassWizard generated virtual function overrides
	//{{AFX_VIRTUAL(CStartup)
	public:
	virtual BOOL PreTranslateMessage(MSG* pMsg);
	protected:
	virtual void DoDataExchange(CDataExchange* pDX);    // DDX/DDV support
	//}}AFX_VIRTUAL

	#ifdef _DEBUG
		virtual void AssertValid() const;
		virtual void Dump(CDumpContext& dc) const;
	#endif

// Implementation

protected:

	// Generated message map functions
	//{{AFX_MSG(CStartup)
	virtual BOOL OnInitDialog();
	afx_msg void OnInstructions();
	afx_msg void OnUsewizard();
	afx_msg void OnNewFacility();
	afx_msg void OnOpenFacility();
	virtual void OnOK();
	afx_msg BOOL OnHelpInfo(HELPINFO* pHelpInfo);
	afx_msg void OnSucceedhelp();
	afx_msg void OnDoubleclickedOpenfacility();
	afx_msg void OnDoubleclickedNewfacility();
	afx_msg void OnDoubleclickedUsewizard();
	afx_msg void OnHelp();
	//}}AFX_MSG
	DECLARE_MESSAGE_MAP()

private:
	void CreateUnitsBox();
	void DestroyUnitsBox();
	void CreateWMSBox();
	void DestroyWMSBox();
	void CreateInstructBox();
	void DestroyInstructBox();
	void CreateDescriptionBox();
	void DestroyDescriptionBox();
};

//{{AFX_INSERT_LOCATION}}
// Microsoft Developer Studio will insert additional declarations immediately before the previous line.

#endif // !defined(AFX_STARTUP_H__CB272F01_06E7_11D2_9BD1_0080C742D9DF__INCLUDED_)

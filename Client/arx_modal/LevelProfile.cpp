// LevelProfile.cpp: implementation of the CLevelProfile class.
//
//////////////////////////////////////////////////////////////////////

#include "stdafx.h"
#include "modal.h"
#include "LevelProfile.h"
#include "UtilityHelper.h"

#ifdef _DEBUG
#undef THIS_FILE
static char THIS_FILE[]=__FILE__;
#define new DEBUG_NEW
#endif

extern CUtilityHelper utilityHelper;

//////////////////////////////////////////////////////////////////////
// Construction/Destruction
//////////////////////////////////////////////////////////////////////

CLevelProfile::CLevelProfile()
{
	m_LevelProfileDBId = 0;
	m_BayProfileDBId = 0;
	m_Coordinates.m_X = m_Coordinates.m_Y = m_Coordinates.m_Z = 0;
	m_WeightCapacity = 0;
	m_Thickness = 0;
	m_IsRotateAllowed = FALSE;
	m_IsVariableWidthAllowed = FALSE;
	m_ForkFixedExtraction = m_ForkFixedInsertion = 0;
	m_IsBarHidden = 0;
	m_Overhang = 0;
	m_MinimumLocWidth = 0;
	m_SelectPositions = 0;
	m_ReservePositions = 0;
	m_SelectPositionHeight = 0;
	m_ReservePositionHeight = 0;
	m_BackfillCode = "%LOCATION%";
	m_Clearance = 0;
	m_StackWidth = 0;
	m_StackDepth = 0;
	m_FacingGap = m_FacingSnap = m_ProductGap = m_ProductSnap = 0;
	m_MaximumCaseWeight = 0;
	m_MaximumCaseCount = 0;
	m_LocationRowCount = 1;
	m_FlowDifference = 0;
}

CLevelProfile::~CLevelProfile()
{
	for (int i=0; i < m_LocationProfileList.GetSize(); ++i)
		delete m_LocationProfileList[i];
	
	for (i=0; i < m_LevelLaborProfileList.GetSize(); ++i)
		delete m_LevelLaborProfileList[i];

	for (i=0; i < m_ExternalInfoList.GetSize(); ++i)
		delete m_ExternalInfoList[i];

}


CLevelProfile::CLevelProfile(const CLevelProfile& other)
{
	m_LevelProfileDBId = other.m_LevelProfileDBId;
	m_Description = other.m_Description;
	m_Coordinates.m_X = other.m_Coordinates.m_X;
	m_Coordinates.m_Y = other.m_Coordinates.m_Y;
	m_Coordinates.m_Z = other.m_Coordinates.m_Z;
	m_RelativeLevel = other.m_RelativeLevel;
	m_WeightCapacity = other.m_WeightCapacity;
	m_Thickness = other.m_Thickness;
	m_IsRotateAllowed = other.m_IsRotateAllowed;
	m_IsVariableWidthAllowed = other.m_IsVariableWidthAllowed;
	m_ForkFixedInsertion = other.m_ForkFixedInsertion;
	m_IsBarHidden = other.m_IsBarHidden;
	m_Overhang = other.m_Overhang;
	m_MinimumLocWidth = other.m_MinimumLocWidth;
	m_ProductGap = other.m_ProductGap;
	m_ProductSnap = other.m_ProductSnap;
	m_FacingGap = other.m_FacingGap;
	m_FacingSnap = other.m_FacingSnap;
	m_Baytype = other.m_Baytype;
	m_ForkFixedExtraction = other.m_ForkFixedExtraction;
	m_SelectPositions = other.m_SelectPositions;
	m_ReservePositions = other.m_ReservePositions;
	m_SelectPositionHeight = other.m_SelectPositionHeight;
	m_ReservePositionHeight = other.m_ReservePositionHeight;
	m_BackfillCode = other.m_BackfillCode;
	m_Clearance = other.m_Clearance;
	m_BayProfileDBId = other.m_BayProfileDBId;
	m_StackWidth = other.m_StackWidth;
	m_StackDepth = other.m_StackDepth;
	m_MaximumCaseWeight = other.m_MaximumCaseWeight;
	m_MaximumCaseCount = other.m_MaximumCaseCount;
	m_LocationRowCount = other.m_LocationRowCount;
	m_FlowDifference = other.m_FlowDifference;

	for (int i=0; i < m_LocationProfileList.GetSize(); ++i)
		delete m_LocationProfileList[i];
	m_LocationProfileList.RemoveAll();

	for (i=0; i < other.m_LocationProfileList.GetSize(); ++i)
		m_LocationProfileList.Add(new CLocationProfile(*other.m_LocationProfileList[i]));

	for (i=0; i < m_LevelLaborProfileList.GetSize(); ++i)
		delete m_LevelLaborProfileList[i];
	m_LevelLaborProfileList.RemoveAll();

	for (i=0; i < other.m_LevelLaborProfileList.GetSize(); ++i)
		m_LevelLaborProfileList.Add(new CLevelLaborProfile(*other.m_LevelLaborProfileList[i]));
	
	for (i=0; i < m_ExternalInfoList.GetSize(); ++i)
		delete m_ExternalInfoList[i];
	m_ExternalInfoList.RemoveAll();
	
	for (i=0; i < other.m_ExternalInfoList.GetSize(); ++i)
		m_ExternalInfoList.Add(new CLevelProfileExternalInfo(*other.m_ExternalInfoList[i]));

}


CLevelProfile& CLevelProfile::operator=(const CLevelProfile &other)
{
	m_LevelProfileDBId = other.m_LevelProfileDBId;
	m_Description = other.m_Description;
	m_Coordinates.m_X = other.m_Coordinates.m_X;
	m_Coordinates.m_Y = other.m_Coordinates.m_Y;
	m_Coordinates.m_Z = other.m_Coordinates.m_Z;
	m_RelativeLevel = other.m_RelativeLevel;
	m_WeightCapacity = other.m_WeightCapacity;
	m_Thickness = other.m_Thickness;
	m_IsRotateAllowed = other.m_IsRotateAllowed;
	m_IsVariableWidthAllowed = other.m_IsVariableWidthAllowed;
	m_ForkFixedInsertion = other.m_ForkFixedInsertion;
	m_IsBarHidden = other.m_IsBarHidden;
	m_Overhang = other.m_Overhang;
	m_MinimumLocWidth = other.m_MinimumLocWidth;
	m_ProductGap = other.m_ProductGap;
	m_ProductSnap = other.m_ProductSnap;
	m_FacingGap = other.m_FacingGap;
	m_FacingSnap = other.m_FacingSnap;
	m_Baytype = other.m_Baytype;
	m_ForkFixedExtraction = other.m_ForkFixedExtraction;
	m_SelectPositions = other.m_SelectPositions;
	m_ReservePositions = other.m_ReservePositions;
	m_SelectPositionHeight = other.m_SelectPositionHeight;
	m_ReservePositionHeight = other.m_ReservePositionHeight;
	m_BackfillCode = other.m_BackfillCode;
	m_Clearance = other.m_Clearance;
	m_BayProfileDBId = other.m_BayProfileDBId;
	m_StackWidth = other.m_StackWidth;
	m_StackDepth = other.m_StackDepth;
	m_MaximumCaseWeight = other.m_MaximumCaseWeight;
	m_MaximumCaseCount = other.m_MaximumCaseCount;
	m_LocationRowCount = other.m_LocationRowCount;
	m_FlowDifference = other.m_FlowDifference;

	for (int i=0; i < m_LocationProfileList.GetSize(); ++i)
		delete m_LocationProfileList[i];
	m_LocationProfileList.RemoveAll();

	for (i=0; i < other.m_LocationProfileList.GetSize(); ++i)
		m_LocationProfileList.Add(new CLocationProfile(*other.m_LocationProfileList[i]));

	for (i=0; i < m_LevelLaborProfileList.GetSize(); ++i)
		delete m_LevelLaborProfileList[i];
	m_LevelLaborProfileList.RemoveAll();

	for (i=0; i < other.m_LevelLaborProfileList.GetSize(); ++i)
		m_LevelLaborProfileList.Add(new CLevelLaborProfile(*other.m_LevelLaborProfileList[i]));

	for (i=0; i < m_ExternalInfoList.GetSize(); ++i)
		delete m_ExternalInfoList[i];
	m_ExternalInfoList.RemoveAll();
	
	for (i=0; i < other.m_ExternalInfoList.GetSize(); ++i)
		m_ExternalInfoList.Add(new CLevelProfileExternalInfo(*other.m_ExternalInfoList[i]));


	return *this;
}

BOOL CLevelProfile::operator==(const CLevelProfile& other)
{
	if (m_LevelProfileDBId != other.m_LevelProfileDBId) return FALSE;
	if (m_Description != other.m_Description) return FALSE;
	if (m_Coordinates != other.m_Coordinates) return FALSE;
	if (m_RelativeLevel != other.m_RelativeLevel) return FALSE;
	if (m_WeightCapacity != other.m_WeightCapacity) return FALSE;
	if (m_Thickness != other.m_Thickness) return FALSE;
	if (m_IsRotateAllowed != other.m_IsRotateAllowed) return FALSE;
	if (m_IsVariableWidthAllowed != other.m_IsVariableWidthAllowed) return FALSE;
	if (m_ForkFixedInsertion != other.m_ForkFixedInsertion) return FALSE;
	if (m_IsBarHidden != other.m_IsBarHidden) return FALSE;
	if (m_Overhang != other.m_Overhang) return FALSE;
	if (m_MinimumLocWidth != other.m_MinimumLocWidth) return FALSE;
	if (m_ProductGap != other.m_ProductGap) return FALSE;
	if (m_ProductSnap != other.m_ProductSnap) return FALSE;
	if (m_FacingGap != other.m_FacingGap) return FALSE;
	if (m_FacingSnap != other.m_FacingSnap) return FALSE;
	if (m_Baytype != other.m_Baytype) return FALSE;
	if (m_ForkFixedExtraction != other.m_ForkFixedExtraction) return FALSE;
	if (m_SelectPositions != other.m_SelectPositions) return FALSE;
	if (m_ReservePositions != other.m_ReservePositions) return FALSE;
	if (m_SelectPositionHeight != other.m_SelectPositionHeight) return FALSE;
	if (m_ReservePositionHeight != other.m_ReservePositionHeight) return FALSE;
	if (m_BackfillCode != other.m_BackfillCode) return FALSE;
	if (m_Clearance != other.m_Clearance) return FALSE;
	if (m_BayProfileDBId != other.m_BayProfileDBId) return FALSE;
	if (m_StackDepth != other.m_StackDepth) return FALSE;
	if (m_StackWidth != other.m_StackWidth) return FALSE;
	if (m_MaximumCaseWeight != other.m_MaximumCaseWeight) return FALSE;
	if (m_MaximumCaseCount != other.m_MaximumCaseCount) return FALSE;
	if (m_LocationRowCount != other.m_LocationRowCount) return FALSE;
	if (m_FlowDifference != other.m_FlowDifference) return FALSE;

	if (m_LocationProfileList.GetSize() != other.m_LocationProfileList.GetSize()) return FALSE;

	for (int i=0; i < m_LocationProfileList.GetSize(); ++i) {
		if (*m_LocationProfileList[i] != *other.m_LocationProfileList[i])
			return FALSE;
	}

	if (m_LevelLaborProfileList.GetSize() != other.m_LevelLaborProfileList.GetSize()) return FALSE;

	for (i=0; i < m_LevelLaborProfileList.GetSize(); ++i) {
		if (*m_LevelLaborProfileList[i] != *other.m_LevelLaborProfileList[i])
			return FALSE;
	}

	if (m_ExternalInfoList.GetSize() != other.m_ExternalInfoList.GetSize()) return FALSE;

	for (i=0; i < m_ExternalInfoList.GetSize(); ++i) {
		if (*m_ExternalInfoList[i] != *other.m_ExternalInfoList[i])
			return FALSE;
	}
	
	return TRUE;
}

int CLevelProfile::Parse(CString &line)
{
	CStringArray strings;
	utilityHelper.ParseString(line, "|", strings);

	for (int i=0; i < strings.GetSize(); ++i) {
		switch (i) {
		case 0:
			m_LevelProfileDBId = atoi(strings[i]);
			break;
		case 1:
			m_Description = strings[i];
			break;
		case 2:
			m_Coordinates.m_X = atof(strings[i]);
			break;
		case 3:
			m_Coordinates.m_Y = atof(strings[i]);
			break;
		case 4:
			m_Coordinates.m_Z = atof(strings[i]);
			break;
		case 5:
			m_RelativeLevel = atoi(strings[i]);
			break;
		case 6:
			m_WeightCapacity = atof(strings[i]);
			break;
		case 7:
			m_Thickness = atof(strings[i]);
			break;
		case 8:
			m_IsRotateAllowed = atoi(strings[i]);
			break;
		case 9:
			m_IsVariableWidthAllowed = atoi(strings[i]);
			break;
		case 10:
			m_ForkFixedInsertion = atof(strings[i]);
			break;
		case 11:
			m_IsBarHidden = atoi(strings[i]);
			break;
		case 12:
			m_Overhang = atof(strings[i]);
			break;
		case 13:
			m_MinimumLocWidth = atof(strings[i]);
			break;
		case 14:
			m_ProductGap = atof(strings[i]);
			break;
		case 15:
			m_ProductSnap = atof(strings[i]);
			break;
		case 16:
			m_FacingGap = atof(strings[i]);
			break;
		case 17:
			m_FacingSnap = atof(strings[i]);
			break;
		case 18:
			m_Baytype = atoi(strings[i]);
			break;
		case 19:
			m_ForkFixedExtraction = atof(strings[i]);
			break;
		case 20:
			m_SelectPositions = atoi(strings[i]);
			break;
		case 21:
			m_ReservePositions = atoi(strings[i]);
			break;
		case 22:
			m_SelectPositionHeight = atof(strings[i]);
			break;
		case 23:
			m_ReservePositionHeight = atof(strings[i]);
			break;
		case 24:
			m_StackWidth = atof(strings[i]);
			break;
		case 25:
			m_StackDepth = atof(strings[i]);
			break;
		case 26:
			m_BackfillCode = strings[i];
			break;
		case 27:
			m_Clearance = atof(strings[i]);
			break;
		case 28:
			m_BayProfileDBId = atoi(strings[i]);
			break;
		case 29:
			m_MaximumCaseWeight = atof(strings[i]);
			break;
		case 30:
			m_MaximumCaseCount = atoi(strings[i]);
			break;
		case 31:
			m_LocationRowCount = atoi(strings[i]);
			break;
		case 32:
			m_FlowDifference = atoi(strings[i]);
			break;
		}
	}


	return 0;
}


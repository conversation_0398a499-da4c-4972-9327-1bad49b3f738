// ProductInfo.cpp: implementation of the CProductInfo class.
//
//////////////////////////////////////////////////////////////////////

#include "stdafx.h"
#include "modal.h"
#include "ProductInfo.h"
#include <dbsymtb.h>

#ifdef _DEBUG
#undef THIS_FILE
static char THIS_FILE[]=__FILE__;
#define new DEBUG_NEW
#endif

//////////////////////////////////////////////////////////////////////
// Construction/Destruction
//////////////////////////////////////////////////////////////////////

CProductInfo::CProductInfo()
{
	m_ProductDBID = 0;
	m_ProductDescription = "";
	m_CasePack = 0;
	m_WMSProductID = "";
	m_WMSProductDetailID = "";
	m_ProdProductGroupDBID = 0;
	m_ProdPGDescription = "";
	m_CurrentLocationDBID = 0;
	m_LocationDescription = "";
	m_BayProfileDescription = "";
	m_LevelType = 0;
	m_LocProductGroupDBID = 0;
	m_LocPGDescription = "";
	m_Width = m_Length = m_Height = 0.0f;
	m_CaseWidth = 0.0f;
	m_CaseLength = 0.0f;
	m_CaseHeight = 0.0f;
	m_InnerWidth = 0.0f;
	m_InnerLength = 0.0f;
	m_InnerHeight = 0.0f;
	m_EachWidth = 0.0f;
	m_EachLength = 0.0f;
	m_EachHeight = 0.0f;
	m_UnitOfIssue = 0;
	m_NumberInPallet = 0;
	m_Ti = 0;
	m_Hi = 0;
	m_ContainerWidth = 0.0f;
	m_ContainerLength = 0.0f;
	m_ContainerHeight = 0.0f;
	m_ContainerHeightOverride = 0;
	m_InnerPack = 0;
	m_RotatedWidth = 0.0f;
	m_RotatedLength = 0.0f;
	m_RotatedHeight = 0.0f;
	m_RotateXAxis = 0;
	m_RotateYAxis = 0;
	m_RotateZAxis = 0;
	m_Weight = 0.0f;
	m_CaseQuantity = 0.0;

}

CProductInfo::~CProductInfo()
{

}

CProductInfo& CProductInfo::operator=(const CProductInfo & other)
{
	
	m_ProductDBID = other.m_ProductDBID;
	m_ProductDescription = other.m_ProductDescription;
	m_CasePack = other.m_CasePack;
	m_WMSProductID = other.m_WMSProductID;
	m_WMSProductDetailID = other.m_WMSProductDetailID;
	m_ProdProductGroupDBID = other.m_ProdProductGroupDBID;
	m_ProdPGDescription = other.m_ProdPGDescription;
	m_CurrentLocationDBID = other.m_CurrentLocationDBID;
	m_LocationDescription = other.m_LocationDescription;
	m_BayProfileDescription = other.m_BayProfileDescription;
	m_LevelType = other.m_LevelType;
	m_LocProductGroupDBID = other.m_LocProductGroupDBID;
	m_LocPGDescription = other.m_LocPGDescription;
	m_CaseQuantity = other.m_CaseQuantity;
	m_Width = other.m_Width;
	m_Length = other.m_Length;
	m_Height = other.m_Height;
	m_CaseWidth = other.m_CaseWidth;
	m_CaseLength = other.m_CaseLength;
	m_CaseHeight = other.m_CaseHeight;
	m_InnerWidth = other.m_InnerWidth;
	m_InnerLength = other.m_InnerLength;
	m_InnerHeight = other.m_InnerHeight;
	m_EachWidth = other.m_EachWidth;
	m_EachLength = other.m_EachLength;
	m_EachHeight = other.m_EachHeight;
	m_UnitOfIssue = other.m_UnitOfIssue;
	m_NumberInPallet = other.m_NumberInPallet;
	m_Ti = other.m_Ti;
	m_Hi = other.m_Hi;
	m_ContainerWidth = other.m_ContainerWidth;
	m_ContainerLength = other.m_ContainerLength;
	m_ContainerHeight = other.m_ContainerHeight;
	m_ContainerHeightOverride = other.m_ContainerHeightOverride;
	m_InnerPack = other.m_InnerPack;
	m_RotatedWidth = other.m_RotatedWidth;
	m_RotatedLength = other.m_RotatedLength;
	m_RotatedHeight = other.m_RotatedHeight;
	m_RotateXAxis = other.m_RotateXAxis;
	m_RotateYAxis = other.m_RotateYAxis;
	m_RotateZAxis = other.m_RotateZAxis;
	m_Weight = other.m_Weight;

	return *this;

}


void CProductInfo::Parse(CString line)
{
	char *str;
	char *ptr;
	
	try {
		line.Replace("||", "| |");
		line.Replace("||", "| |");

		str = line.GetBuffer(0);
		
		
		ptr = strtok(str, "|");
		m_ProductDBID = atoi(ptr);
		ptr = strtok(NULL, "|");
		m_ProductDescription = ptr;
		ptr = strtok(NULL, "|");
		m_CasePack = atoi(ptr);
		ptr = strtok(NULL, "|");
		m_WMSProductID = ptr;
		ptr = strtok(NULL, "|");
		m_WMSProductDetailID = ptr;
		ptr = strtok(NULL, "|");
		if (strcmp(ptr, " ") != 0)
			m_ProdProductGroupDBID = atoi(ptr);
		ptr = strtok(NULL, "|");
		if (strcmp(ptr, " ") != 0)
			m_ProdPGDescription = ptr;
		ptr = strtok(NULL, "|");
		if (strcmp(ptr, " ") != 0)
			m_CurrentLocationDBID = atoi(ptr);
		ptr = strtok(NULL, "|");
		if (strcmp(ptr, " ") != 0)
			m_LocationDescription = ptr;
		ptr = strtok(NULL, "|");
		if (strcmp(ptr, " ") != 0)
			m_BayProfileDescription = ptr;
		ptr = strtok(NULL, "|");
		if (strcmp(ptr, " ") != 0)
			m_LevelType = atoi(ptr);
		ptr = strtok(NULL, "|");
		if (strcmp(ptr, " ") != 0)
			m_LocProductGroupDBID = atoi(ptr);
		ptr = strtok(NULL, "|");
		if (strcmp(ptr, " ") != 0)
			m_LocPGDescription = ptr;
		ptr = strtok(NULL, "|");
		m_CaseWidth = atof(ptr);
		ptr = strtok(NULL, "|");
		m_CaseLength = atof(ptr);
		ptr = strtok(NULL, "|");
		m_CaseHeight = atof(ptr);
		ptr = strtok(NULL, "|");
		m_InnerWidth = atof(ptr);
		ptr = strtok(NULL, "|");
		m_InnerLength = atof(ptr);
		ptr = strtok(NULL, "|");
		m_InnerHeight = atof(ptr);
		ptr = strtok(NULL, "|");
		m_EachWidth = atof(ptr);
		ptr = strtok(NULL, "|");
		m_EachLength = atof(ptr);
		ptr = strtok(NULL, "|");
		m_EachHeight = atof(ptr);
		ptr = strtok(NULL, "|");
		m_UnitOfIssue = atoi(ptr);
		ptr = strtok(NULL, "|");
		m_NumberInPallet = atoi(ptr);
		ptr = strtok(NULL, "|");
		m_InnerPack = atoi(ptr);
		ptr = strtok(NULL, "|");
		m_RotateXAxis = atoi(ptr);
		ptr = strtok(NULL, "|");
		m_RotateYAxis = atoi(ptr);
		ptr = strtok(NULL, "|");
		m_RotateZAxis = atoi(ptr);
		ptr = strtok(NULL, "|");
		m_Weight = atof(ptr);
		ptr = strtok(NULL, "|");
		m_Ti = atoi(ptr);
		ptr = strtok(NULL, "|");
		m_Hi = atoi(ptr);
		ptr = strtok(NULL, "|");
		m_ContainerWidth = atof(ptr);
		ptr = strtok(NULL, "|");
		m_ContainerLength = atof(ptr);
		ptr = strtok(NULL, "|");
		m_ContainerHeight = atof(ptr);
		ptr = strtok(NULL, "|");
		m_ContainerHeightOverride = atoi(ptr);
		ptr = strtok(NULL, "|");
		if (strcmp(ptr, " ") != 0)
			m_CaseQuantity = atoi(ptr);
		line.ReleaseBuffer();
	}
	catch (...) {
		line.ReleaseBuffer();
		AfxMessageBox("Error processing product list.\n");
		ads_printf("%s\n", line);
	}

	switch (m_UnitOfIssue) {
	case 0:		// Each
		m_Width = m_EachWidth;
		m_Length = m_EachLength;
		m_Height = m_EachHeight;
		break;
	case 1:		// Inner
		m_Width = m_InnerWidth;
		m_Length = m_InnerLength;
		m_Height = m_InnerHeight;
		break;
	case 2:		// Case
	case 3:		// Pallet
		m_Width = m_CaseWidth;
		m_Length = m_CaseLength;
		m_Height = m_CaseHeight;
		break;
	default:
		m_Width = 0.0f;
		m_Length = 0.0f;
		m_Height = 0.0f;
		break;
	}

	return;


}
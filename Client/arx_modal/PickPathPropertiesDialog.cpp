// PickPathPropertiesDialog.cpp : implementation file
//

#include "stdafx.h"
#include "modal.h"
#include "PickPathPropertiesDialog.h"
#include "HelpService.h"

#include <process.h>

#ifdef _DEBUG
#define new DEBUG_NEW
#undef THIS_FILE
static char THIS_FILE[] = __FILE__;
#endif

extern CHelpService helpService;


/////////////////////////////////////////////////////////////////////////////
// CPickPathPropertiesDialog dialog


CPickPathPropertiesDialog::CPickPathPropertiesDialog(CWnd* pParent /*=NULL*/)
	: CDialog(CPickPathPropertiesDialog::IDD, pParent)
{
	//{{AFX_DATA_INIT(CPickPathPropertiesDialog)
	m_PickPathProperties_PathType_Val = -1;
	m_PickPathProperties_PatternNum = 0;
	m_LBaySchemeVal = -1;
	m_LBayStartVal = _T("");
	m_LLevelBreak = FALSE;
	m_LLevelSchemeVal = -1;
	m_LLevelStart = _T("");
	m_LLocBreakVal = -1;
	m_LLocSchemeVal = -1;
	m_LLocStart = _T("");
	m_RBaySchemeVal = -1;
	m_RBayStart = _T("");
	m_RLevelBreak = FALSE;
	m_RLevelSchemeVal = -1;
	m_RLevelStart = _T("");
	m_RLocBreakVal = -1;
	m_RLocSchemeVal = -1;
	m_RLocStart = _T("");
	m_LBayStepVal = 0;
	m_LLevelStep = 0;
	m_LLocStep = 0;
	m_RBayStep = 0;
	m_RLevelStep = 0;
	m_RLocStep = 0;
	m_LBayPatternVal = _T("");
	m_LLevelPatternVal = _T("");
	m_LLocPatternVal = _T("");
	m_RBayPatternVal = _T("");
	m_RLevelPatternVal = _T("");
	m_RLocPatternVal = _T("");
	//}}AFX_DATA_INIT
	m_LBaySchemeBoxEnabled = TRUE;
	m_LLevelSchemeBoxEnabled = TRUE;
	m_LLocSchemeBoxEnabled = TRUE;
	
	m_RBaySchemeBoxEnabled = TRUE;
	m_RLevelSchemeBoxEnabled = TRUE;
	m_RLocSchemeBoxEnabled = TRUE;
	m_Updateable = TRUE;
}


void CPickPathPropertiesDialog::DoDataExchange(CDataExchange* pDX)
{
	CDialog::DoDataExchange(pDX);
	//{{AFX_DATA_MAP(CPickPathPropertiesDialog)
	DDX_Control(pDX, IDC_RLOCSTEPVAL, m_RLocStepBox);
	DDX_Control(pDX, IDC_RLEVELSTEPVAL, m_RLevelStepBox);
	DDX_Control(pDX, IDC_RBAYSTEPVAL, m_RBayStepBox);
	DDX_Control(pDX, IDC_LLOCSTEPVAL, m_LLocStepBox);
	DDX_Control(pDX, IDC_LBAYSTEPVAL, m_LBayStepBox);
	DDX_Control(pDX, IDC_LLEVELSTEPVAL, m_LLevelStepBox);
	DDX_Control(pDX, IDC_RLOCSTARTVAL, m_RLocStart_Box);
	DDX_Control(pDX, IDC_RLEVELSTARTVAL, m_RLevelStart_Box);
	DDX_Control(pDX, IDC_RBAYSTARTVAL, m_RBayStart_Box);
	DDX_Control(pDX, IDC_LLOCSTARTVAL, m_LLocStart_Box);
	DDX_Control(pDX, IDC_LLEVELSTARTVAL, m_LLevelStart_Box);
	DDX_Control(pDX, IDC_LBAYSTARTVAL, m_LBayStart_Box);
	DDX_Control(pDX, IDC_RLOCSCHEMEVAL, m_RLocShemeBox);
	DDX_Control(pDX, IDC_RLOCBREAKVAL, m_RLocBreakBox);
	DDX_Control(pDX, IDC_RLEVELSCHEMEVAL, m_RLevelSchemeBox);
	DDX_Control(pDX, IDC_RBAYSCHEMEVAL, m_RBaySchemeBox);
	DDX_Control(pDX, IDC_LLOCSCHEMEVAL, m_LLocSchemeBox);
	DDX_Control(pDX, IDC_LLOCBREAKVAL, m_LLocBreakBox);
	DDX_Control(pDX, IDC_LLEVELSCHEMEVAL, m_LLevelSchemeBox);
	DDX_Control(pDX, IDC_LBAYSCHEMEVAL, m_LBaySchemeBox);
	DDX_Control(pDX, IDC_PICKPATH_PROPERTIES_PATTERNNUM, m_PickPathProperties_PatternNum_Box);
	DDX_Control(pDX, IDC_PICKPATH_PROPERTIES_PATHTYPE, m_PickPathProperties_PathType_List);
	DDX_CBIndex(pDX, IDC_PICKPATH_PROPERTIES_PATHTYPE, m_PickPathProperties_PathType_Val);
	DDX_Text(pDX, IDC_PICKPATH_PROPERTIES_PATTERNNUM, m_PickPathProperties_PatternNum);
	DDV_MinMaxInt(pDX, m_PickPathProperties_PatternNum, 1, 2147483647);
	DDX_CBIndex(pDX, IDC_LBAYSCHEMEVAL, m_LBaySchemeVal);
	DDX_Text(pDX, IDC_LBAYSTARTVAL, m_LBayStartVal);
	DDX_Check(pDX, IDC_LLEVELBREAK, m_LLevelBreak);
	DDX_CBIndex(pDX, IDC_LLEVELSCHEMEVAL, m_LLevelSchemeVal);
	DDX_Text(pDX, IDC_LLEVELSTARTVAL, m_LLevelStart);
	DDX_CBIndex(pDX, IDC_LLOCBREAKVAL, m_LLocBreakVal);
	DDX_CBIndex(pDX, IDC_LLOCSCHEMEVAL, m_LLocSchemeVal);
	DDX_Text(pDX, IDC_LLOCSTARTVAL, m_LLocStart);
	DDX_CBIndex(pDX, IDC_RBAYSCHEMEVAL, m_RBaySchemeVal);
	DDX_Text(pDX, IDC_RBAYSTARTVAL, m_RBayStart);
	DDX_Check(pDX, IDC_RLEVELBREAK, m_RLevelBreak);
	DDX_CBIndex(pDX, IDC_RLEVELSCHEMEVAL, m_RLevelSchemeVal);
	DDX_Text(pDX, IDC_RLEVELSTARTVAL, m_RLevelStart);
	DDX_CBIndex(pDX, IDC_RLOCBREAKVAL, m_RLocBreakVal);
	DDX_CBIndex(pDX, IDC_RLOCSCHEMEVAL, m_RLocSchemeVal);
	DDX_Text(pDX, IDC_RLOCSTARTVAL, m_RLocStart);
	DDX_Text(pDX, IDC_LBAYSTEPVAL, m_LBayStepVal);
	DDX_Text(pDX, IDC_LLEVELSTEPVAL, m_LLevelStep);
	DDX_Text(pDX, IDC_LLOCSTEPVAL, m_LLocStep);
	DDX_Text(pDX, IDC_RBAYSTEPVAL, m_RBayStep);
	DDX_Text(pDX, IDC_RLEVELSTEPVAL, m_RLevelStep);
	DDX_Text(pDX, IDC_RLOCSTEPVAL, m_RLocStep);
	DDX_Text(pDX, IDC_LBAYPATTERN, m_LBayPatternVal);
	DDV_MaxChars(pDX, m_LBayPatternVal, 500);
	DDX_Text(pDX, IDC_LLEVELPATTERN, m_LLevelPatternVal);
	DDV_MaxChars(pDX, m_LLevelPatternVal, 500);
	DDX_Text(pDX, IDC_LLOCPATTERN, m_LLocPatternVal);
	DDV_MaxChars(pDX, m_LLocPatternVal, 500);
	DDX_Text(pDX, IDC_RBAYPATTERN, m_RBayPatternVal);
	DDV_MaxChars(pDX, m_RBayPatternVal, 500);
	DDX_Text(pDX, IDC_RLEVELPATTERN, m_RLevelPatternVal);
	DDV_MaxChars(pDX, m_RLevelPatternVal, 500);
	DDX_Text(pDX, IDC_RLOCPATTERN, m_RLocPatternVal);
	DDV_MaxChars(pDX, m_RLocPatternVal, 500);
	//}}AFX_DATA_MAP
}


BEGIN_MESSAGE_MAP(CPickPathPropertiesDialog, CDialog)
	//{{AFX_MSG_MAP(CPickPathPropertiesDialog)
	ON_BN_CLICKED(IDHELP, OnHelp)
	ON_CBN_SELCHANGE(IDC_PICKPATH_PROPERTIES_PATHTYPE, OnSelchangePickPathPropertiesPathtype)
	ON_BN_CLICKED(IDC_PICKPATH_PROPERTIES_GROUP, OnPickpathPropertiesGroup)
	ON_EN_KILLFOCUS(IDC_LBAYSTARTVAL, OnKillfocusLbaystartval)
	ON_EN_KILLFOCUS(IDC_LLEVELSTARTVAL, OnKillfocusLlevelstartval)
	ON_EN_KILLFOCUS(IDC_LLOCSTARTVAL, OnKillfocusLlocstartval)
	ON_EN_KILLFOCUS(IDC_RBAYSTARTVAL, OnKillfocusRbaystartval)
	ON_EN_KILLFOCUS(IDC_RLEVELSTARTVAL, OnKillfocusRlevelstartval)
	ON_EN_KILLFOCUS(IDC_RLOCSTARTVAL, OnKillfocusRlocstartval)
	ON_EN_KILLFOCUS(IDC_LBAYSTEPVAL, OnKillfocusLbaystepval)
	ON_EN_KILLFOCUS(IDC_LLEVELSTEPVAL, OnKillfocusLlevelstepval)
	ON_EN_KILLFOCUS(IDC_LLOCSTEPVAL, OnKillfocusLlocstepval)
	ON_EN_KILLFOCUS(IDC_RBAYSTEPVAL, OnKillfocusRbaystepval)
	ON_EN_KILLFOCUS(IDC_RLEVELSTEPVAL, OnKillfocusRlevelstepval)
	ON_EN_KILLFOCUS(IDC_RLOCSTEPVAL, OnKillfocusRlocstepval)
	ON_WM_HELPINFO()
	ON_EN_KILLFOCUS(IDC_LBAYPATTERN, OnKillfocusLbaypattern)
	ON_EN_KILLFOCUS(IDC_LLEVELPATTERN, OnKillfocusLlevelpattern)
	ON_EN_KILLFOCUS(IDC_LLOCPATTERN, OnKillfocusLlocpattern)
	ON_EN_KILLFOCUS(IDC_RBAYPATTERN, OnKillfocusRbaypattern)
	ON_EN_KILLFOCUS(IDC_RLEVELPATTERN, OnKillfocusRlevelpattern)
	ON_EN_KILLFOCUS(IDC_RLOCPATTERN, OnKillfocusRlocpattern)
	//}}AFX_MSG_MAP
END_MESSAGE_MAP()

/////////////////////////////////////////////////////////////////////////////
// CPickPathPropertiesDialog message handlers

void CPickPathPropertiesDialog::OnHelp() 
{
	helpService.ShowScreenHelp(IDD);
}

void CPickPathPropertiesDialog::OnOK() 
{
	if (this->UpdateData(TRUE) == 0)
		return;

	if ( m_PickPathProperties_PathType_List.GetCurSel() == CB_ERR ) {
		AfxMessageBox("Path Type is required");
		m_PickPathProperties_PathType_List.SetFocus();
		return;
	}
	if (m_LBayStartVal == "") {
		AfxMessageBox("Left Bay Start ID is required");
		m_LBayStart_Box.SetFocus();
		return;
	}
	if ( m_LBaySchemeBox.GetCurSel() == CB_ERR ) {
		AfxMessageBox("Left Bay Scheme must be chosen.");
		m_LBaySchemeBox.SetFocus();
		return;
	}
	if (m_LLevelStart == "") {
		AfxMessageBox("Left Level Start ID is required");
		m_LLevelStart_Box.SetFocus();
		return;
	}
	if (m_LLocStart == "") {
		AfxMessageBox("Left Location Start ID is required");
		m_LLocStart_Box.SetFocus();
		return;
	}
	if (m_LLevelSchemeBox.GetCurSel() == CB_ERR ) {
		AfxMessageBox("Left Level Scheme must be chosen.");
		m_LLevelSchemeBox.SetFocus();
		return;
	}
	if (m_LLocSchemeBox.GetCurSel() == CB_ERR ) {
		AfxMessageBox("Left Location Scheme must be chosen.");
		m_LLocSchemeBox.SetFocus();
		return;
	}
	if (m_LLocBreakBox.GetCurSel() == CB_ERR ) {
		AfxMessageBox("Left Location 'Break' Scheme must be chosen.");
		m_LLocBreakBox.SetFocus();
		return;
	}
	if (m_RBayStart == "") {
		AfxMessageBox("Right Bay Start ID is required");
		m_RBayStart_Box.SetFocus();
		return;
	}
	if ( m_RBaySchemeBox.GetCurSel() == CB_ERR ) {
		AfxMessageBox("Right Bay Scheme must be chosen.");
		m_RBaySchemeBox.SetFocus();
		return;
	}
	if (m_RLevelStart == "") {
		AfxMessageBox("Right Level Start ID is required");
		m_RLevelStart_Box.SetFocus();
		return;
	}
	if (m_RLevelSchemeBox.GetCurSel() == CB_ERR ) {
		AfxMessageBox("Right Level Scheme must be chosen.");
		m_RLevelSchemeBox.SetFocus();
		return;
	}
	if (m_RLocShemeBox.GetCurSel() == CB_ERR ) {
		AfxMessageBox("Right Location Scheme must be chosen.");
		m_RLocShemeBox.SetFocus();
		return;
	}
	if (m_RLocStart == "") {
		AfxMessageBox("Right Location Start ID is required");
		m_RLocStart_Box.SetFocus();
		return;
	}
	if (m_RLocBreakBox.GetCurSel() == CB_ERR ) {
		AfxMessageBox("Right Location 'Break' Scheme must be chosen.");
		m_RLocBreakBox.SetFocus();
		return;
	}
	if (m_LBayPatternVal.IsEmpty())
		m_LBayPatternVal = " ";
	if (m_LLevelPatternVal.IsEmpty())
		m_LLevelPatternVal = " ";
	if (m_LLocPatternVal.IsEmpty())
		m_LLocPatternVal = " ";

	if (m_RBayPatternVal.IsEmpty())
		m_RBayPatternVal = " ";
	if (m_RLevelPatternVal.IsEmpty())
		m_RLevelPatternVal = " ";
	if (m_RLocPatternVal.IsEmpty())
		m_RLocPatternVal = " ";

	EndDialog(IDOK);
}

void CPickPathPropertiesDialog::OnSelchangePickPathPropertiesPathtype() 
{
	int selIndex;
	
	selIndex = m_PickPathProperties_PathType_List.GetCurSel();

	
	if ( selIndex == 4|| selIndex == 5)
		this->m_PickPathProperties_PatternNum_Box.EnableWindow(TRUE);
	else
		this->m_PickPathProperties_PatternNum_Box.EnableWindow(FALSE);
//	if ( selIndex == 2 || selIndex == 1) {
//		this->m_PickPathProperties_NumberScheme_List.SetCurSel(0);
//		this->m_PickPathProperties_NumberScheme_List.EnableWindow(FALSE);
//	}
//	else
//		this->m_PickPathProperties_NumberScheme_List.EnableWindow(TRUE);

}

BOOL CPickPathPropertiesDialog::OnInitDialog() 
{
	CDialog::OnInitDialog();
	UpdateData(FALSE);
	
	if (! this->m_Updateable) {
		CButton *pButton = (CButton *)GetDlgItem(IDOK);
		pButton->EnableWindow(FALSE);
	}

	if ( m_PickPathProperties_PathType_Val == 4 || m_PickPathProperties_PathType_Val == 5)
		this->m_PickPathProperties_PatternNum_Box.EnableWindow(TRUE);
	else
		this->m_PickPathProperties_PatternNum_Box.EnableWindow(FALSE);
	this->m_PickPathProperties_PathType_List.SetFocus();

	if (m_LBayPatternVal != " ") {
		m_LBayStart_Box.EnableWindow(FALSE);
		m_LBayStepBox.EnableWindow(FALSE);
		m_LBaySchemeBox.EnableWindow(FALSE);
	}
	else {
		m_LBayPatternVal = "";
		m_LBayStart_Box.EnableWindow(TRUE);
		m_LBayStepBox.EnableWindow(TRUE);
			m_LBaySchemeBox.EnableWindow(TRUE);
	}
	
	if (m_LLevelPatternVal != " ") {
		m_LLevelStart_Box.EnableWindow(FALSE);
		m_LLevelStepBox.EnableWindow(FALSE);
		m_LLevelSchemeBox.EnableWindow(FALSE);
	}
	else {
		m_LLevelPatternVal = "";
		m_LLevelStart_Box.EnableWindow(TRUE);
		m_LLevelStepBox.EnableWindow(TRUE);
		m_LLevelSchemeBox.EnableWindow(TRUE);
	}
	
	if (m_LLocPatternVal != " ") {
		m_LLocStart_Box.EnableWindow(FALSE);
		m_LLocStepBox.EnableWindow(FALSE);
		m_LLocSchemeBox.EnableWindow(FALSE);
	}
	else {
		m_LLocPatternVal = "";
		m_LLocStart_Box.EnableWindow(TRUE);
		m_LLocStepBox.EnableWindow(TRUE);
		m_LLocSchemeBox.EnableWindow(TRUE);
	}	
	
	
	if (m_RBayPatternVal != " ") {
		m_RBayStart_Box.EnableWindow(FALSE);
		m_RBayStepBox.EnableWindow(FALSE);
		m_RBaySchemeBox.EnableWindow(FALSE);
	}
	else {
		m_RBayPatternVal = "";
		m_RBayStart_Box.EnableWindow(TRUE);
		m_RBayStepBox.EnableWindow(TRUE);
		m_RBaySchemeBox.EnableWindow(TRUE);
	}	
	
	if (m_RLevelPatternVal != " ") {
		m_RLevelStart_Box.EnableWindow(FALSE);
		m_RLevelStepBox.EnableWindow(FALSE);
		m_RLevelSchemeBox.EnableWindow(FALSE);
	}
	else {
		m_RLevelPatternVal = "";
		m_RLevelStart_Box.EnableWindow(TRUE);
		m_RLevelStepBox.EnableWindow(TRUE);
		m_RLevelSchemeBox.EnableWindow(TRUE);
	}
	
	if (m_RLocPatternVal != " ") {
		m_RLocStart_Box.EnableWindow(FALSE);
		m_RLocStepBox.EnableWindow(FALSE);
		m_RLocShemeBox.EnableWindow(FALSE);
	}
	else {
		m_RLocPatternVal = "";
		m_RLocStart_Box.EnableWindow(TRUE);
		m_RLocStepBox.EnableWindow(TRUE);
		m_RLocShemeBox.EnableWindow(TRUE);
	}	

	UpdateData(FALSE);

	return FALSE;  // return TRUE unless you set the focus to a control
	              // EXCEPTION: OCX Property Pages should return FALSE
}

void CPickPathPropertiesDialog::OnPickpathPropertiesGroup() 
{
	
}


void CPickPathPropertiesDialog::OnKillfocusLbaystartval() 
{
	
	char alphabetCap[28]=" ABCDEFGHIJKLMNOPQRSTUVWXYZ";
	char alphabetNoCap[28]=" abcdefghijklmnopqrstuvwxyz";
	int isAlphaID = 0;
	int i;
	int selIndex;
	char invalidChars[100] = "~`!@#$%^&*(),':;.{[}]|+=-_";
	int foundInvalidchar = 0;

	if (this->IsWindowVisible() == 0)
		return;

	strcat(invalidChars,"\"");
	strcat(invalidChars,"\\");
	
	selIndex = m_PickPathProperties_PathType_List.GetCurSel();

	UpdateData(TRUE);

//	if ( m_LBayStartVal.GetLength() == 0 ) {
//		AfxMessageBox("Identifier is required");
//		m_LBayStart_Box.SetFocus();
//	}

	for (i = 0; invalidChars[i] != '\0' && foundInvalidchar == 0; i++) {
		if (strchr(m_LBayStartVal,invalidChars[i]) != NULL )
			foundInvalidchar = 1;
	}
	if (foundInvalidchar == 1) {
		AfxMessageBox("Invalid character in identifier");
		m_LBayStartVal = "";
		UpdateData(FALSE);
		m_LBayStart_Box.SetFocus();
	}

	if ( m_LBayStartVal.GetLength() > 18 ) {
		AfxMessageBox("Identifier too long.  Length must be 18 characters or less.");
		m_LBayStartVal = "";
		UpdateData(FALSE);
		m_LBayStart_Box.SetFocus();
	}

	for (i=1; i < 27 && isAlphaID == 0; i++) {
		if (m_LBayStartVal.Find(alphabetCap[i]) != -1)
			isAlphaID = 1;
	}
	for (i=1; i < 27 && isAlphaID == 0; i++) {
		if ( m_LBayStartVal.Find(alphabetNoCap[i]) != -1)
			isAlphaID = 1;
	}
	if (isAlphaID == 1) {
		m_LBaySchemeBox.SetCurSel(0);
		m_LBaySchemeBox.EnableWindow(FALSE);
		m_LBaySchemeBoxEnabled = FALSE;
	}
	else {
		m_LBaySchemeBox.EnableWindow(TRUE);
		m_LBaySchemeBoxEnabled = TRUE;
	}
	// brd - 6/267/00 - Added 2-way to the list because we want to allow them to start over
	// at the turn
	if (selIndex != 1 && selIndex != 2 && selIndex != 3) {
		m_RBayStart = m_LBayStartVal;
		UpdateData(FALSE);
	}

}

void CPickPathPropertiesDialog::OnKillfocusLlevelstartval() 
{
	char alphabetCap[28]=" ABCDEFGHIJKLMNOPQRSTUVWXYZ";
	char alphabetNoCap[28]=" abcdefghijklmnopqrstuvwxyz";
	int isAlphaID = 0;
	int i;
	char invalidChars[100] = "~`!@#$%^&*(),':;.{[}]|+=-_";
	int foundInvalidchar = 0;

	if (this->IsWindowVisible() == 0)
		return;

	strcat(invalidChars,"\"");
	strcat(invalidChars,"\\");

	UpdateData(TRUE);
//	if ( m_LLevelStart.GetLength() == 0 ) {
//		AfxMessageBox("Identifier is required");
//		m_LLevelStart_Box.SetFocus();
//	}

	for (i = 0; invalidChars[i] != '\0' && foundInvalidchar == 0; i++) {
		if (strchr(m_LLevelStart,invalidChars[i]) != NULL )
			foundInvalidchar = 1;
	}
	if (foundInvalidchar == 1) {
		AfxMessageBox("Invalid character in identifier");
		m_LLevelStart = "";
		UpdateData(FALSE);
		m_LLevelStart_Box.SetFocus();
	}
	if ( m_LLevelStart.GetLength() > 18 ) {
		AfxMessageBox("Identifier too long.  Length must be 18 characters or less.");
		m_LLevelStart = "";
		UpdateData(FALSE);
		m_LLevelStart_Box.SetFocus();
	}
	for (i=1; i < 27 && isAlphaID == 0; i++) {
		if (m_LLevelStart.Find(alphabetCap[i]) != -1)
			isAlphaID = 1;
	}
	for (i=1; i < 27 && isAlphaID == 0; i++) {
		if ( m_LLevelStart.Find(alphabetNoCap[i]) != -1)
			isAlphaID = 1;
	}
	if (isAlphaID == 1) {
		m_LLevelSchemeBox.SetCurSel(0);
		m_LLevelSchemeBox.EnableWindow(FALSE);
		m_LLevelSchemeBoxEnabled = FALSE;
	}
	else {
		m_LLevelSchemeBox.EnableWindow(TRUE);
		m_LLevelSchemeBoxEnabled = TRUE;
	}
	
}

void CPickPathPropertiesDialog::OnKillfocusLlocstartval() 
{
	char alphabetCap[28]=" ABCDEFGHIJKLMNOPQRSTUVWXYZ";
	char alphabetNoCap[28]=" abcdefghijklmnopqrstuvwxyz";
	int isAlphaID = 0;
	int i;
	char invalidChars[100] = "~`!@#$%^&*(),':;.{[}]|+=-_";
	int foundInvalidchar = 0;

	if (this->IsWindowVisible() == 0)
		return;

	strcat(invalidChars,"\"");
	strcat(invalidChars,"\\");

	UpdateData(TRUE);
//	if ( m_LLocStart.GetLength() == 0 ) {
//		AfxMessageBox("Identifier is required");
//		m_LLocStart_Box.SetFocus();
//	}
	for (i = 0; invalidChars[i] != '\0' && foundInvalidchar == 0; i++) {
		if (strchr(m_LLocStart,invalidChars[i]) != NULL )
			foundInvalidchar = 1;
	}
	if (foundInvalidchar == 1) {
		AfxMessageBox("Invalid character in identifier");
		m_LLocStart = "";
		UpdateData(FALSE);
		m_LLocStart_Box.SetFocus();
	}
	if ( m_LLocStart.GetLength() > 18 ) {
		AfxMessageBox("Identifier too long.  Length must be 18 characters or less.");
		m_LLocStart = "";
		UpdateData(FALSE);
		m_LLocStart_Box.SetFocus();
	}
	for (i=1; i < 27 && isAlphaID == 0; i++) {
		if (m_LLocStart.Find(alphabetCap[i]) != -1)
			isAlphaID = 1;
	}
	for (i=1; i < 27 && isAlphaID == 0; i++) {
		if ( m_LLocStart.Find(alphabetNoCap[i]) != -1)
			isAlphaID = 1;
	}
	if (isAlphaID == 1) {
		m_LLocSchemeBox.SetCurSel(0);
		m_LLocSchemeBox.EnableWindow(FALSE);
		m_LLocSchemeBoxEnabled = FALSE;
	}
	else {
		m_LLocSchemeBox.EnableWindow(TRUE);
		m_LLocSchemeBoxEnabled = TRUE;
	}
	
}

void CPickPathPropertiesDialog::OnKillfocusRbaystartval() 
{
	char alphabetCap[28]=" ABCDEFGHIJKLMNOPQRSTUVWXYZ";
	char alphabetNoCap[28]=" abcdefghijklmnopqrstuvwxyz";
	int isAlphaID = 0;
	int i;
	int selIndex;
	char invalidChars[100] = "~`!@#$%^&*(),':;.{[}]|+=-_";
	int foundInvalidchar = 0;

	if (this->IsWindowVisible() == 0)
		return;

	strcat(invalidChars,"\"");
	strcat(invalidChars,"\\");
	
	selIndex = m_PickPathProperties_PathType_List.GetCurSel();

	UpdateData(TRUE);
//	if ( m_RBayStart.GetLength() == 0 ) {
//		AfxMessageBox("Identifier is required");
//		m_RBayStart_Box.SetFocus();
//	}
	for (i = 0; invalidChars[i] != '\0' && foundInvalidchar == 0; i++) {
		if (strchr(m_RBayStart,invalidChars[i]) != NULL )
			foundInvalidchar = 1;
	}
	if (foundInvalidchar == 1) {
		AfxMessageBox("Invalid character in identifier");
		m_RBayStart = "";
		UpdateData(FALSE);
		m_RBayStart_Box.SetFocus();
	}
	if ( m_RBayStart.GetLength() > 18 ) {
		AfxMessageBox("Identifier too long.  Length must be 18 characters or less.");
		m_RBayStart = "";
		UpdateData(FALSE);
		m_RBayStart_Box.SetFocus();
	}
	for (i=1; i < 27 && isAlphaID == 0; i++) {
		if (m_RBayStart.Find(alphabetCap[i]) != -1)
			isAlphaID = 1;
	}
	for (i=1; i < 27 && isAlphaID == 0; i++) {
		if ( m_RBayStart.Find(alphabetNoCap[i]) != -1)
			isAlphaID = 1;
	}
	if (isAlphaID == 1) {
		m_RBaySchemeBox.SetCurSel(0);
		m_RBaySchemeBox.EnableWindow(FALSE);
		m_RBaySchemeBoxEnabled = FALSE;
	}
	else
		m_RBaySchemeBox.EnableWindow(TRUE);

	// brd - 6/27/00 - Added 2-way to the list because we want to
	// allow them to start over at the turn
	if (selIndex != 1 && selIndex != 2 && selIndex != 3) {
		m_LBayStartVal = m_RBayStart;
		m_RBaySchemeBoxEnabled = TRUE;
		UpdateData(FALSE);
	}
	
}

void CPickPathPropertiesDialog::OnKillfocusRlevelstartval() 
{
	char alphabetCap[28]=" ABCDEFGHIJKLMNOPQRSTUVWXYZ";
	char alphabetNoCap[28]=" abcdefghijklmnopqrstuvwxyz";
	int isAlphaID = 0;
	int i;
	char invalidChars[100] = "~`!@#$%^&*(),':;.{[}]|+=-_";
	int foundInvalidchar = 0;

	if (this->IsWindowVisible() == 0)
		return;

	strcat(invalidChars,"\"");
	strcat(invalidChars,"\\");

	UpdateData(TRUE);
//	if ( m_RLevelStart.GetLength() == 0 ) {
//		AfxMessageBox("Identifier is required");
//		m_RLevelStart_Box.SetFocus();
//	}
	for (i = 0; invalidChars[i] != '\0' && foundInvalidchar == 0; i++) {
		if (strchr(m_RLevelStart,invalidChars[i]) != NULL )
			foundInvalidchar = 1;
	}
	if (foundInvalidchar == 1) {
		AfxMessageBox("Invalid character in identifier");
		m_RLevelStart = "";
		UpdateData(FALSE);
		m_RLevelStart_Box.SetFocus();
	}
	if ( m_RLevelStart.GetLength() > 18 ) {
		AfxMessageBox("Identifier too long.  Length must be 18 characters or less.");
		m_RLevelStart = "";
		UpdateData(FALSE);
		m_RLevelStart_Box.SetFocus();
	}
	
	for (i=1; i < 27 && isAlphaID == 0; i++) {
		if (m_RLevelStart.Find(alphabetCap[i]) != -1)
			isAlphaID = 1;
	}
	for (i=1; i < 27 && isAlphaID == 0; i++) {
		if ( m_RLevelStart.Find(alphabetNoCap[i]) != -1)
			isAlphaID = 1;
	}
	if (isAlphaID == 1) {
		m_RLevelSchemeBox.SetCurSel(0);
		m_RLevelSchemeBox.EnableWindow(FALSE);
		m_RLevelSchemeBoxEnabled = FALSE;
	}
	else {
		m_RLevelSchemeBox.EnableWindow(TRUE);
		m_RLevelSchemeBoxEnabled = TRUE;
	}

}

void CPickPathPropertiesDialog::OnKillfocusRlocstartval() 
{
	char alphabetCap[28]=" ABCDEFGHIJKLMNOPQRSTUVWXYZ";
	char alphabetNoCap[28]=" abcdefghijklmnopqrstuvwxyz";
	int isAlphaID = 0;
	int i;
	char invalidChars[100] = "~`!@#$%^&*(),':;.{[}]|+=-_";
	int foundInvalidchar = 0;

	if (this->IsWindowVisible() == 0)
		return;

	strcat(invalidChars,"\"");
	strcat(invalidChars,"\\");

	UpdateData(TRUE);
//	if ( m_LLocStart.GetLength() == 0 ) {
//		AfxMessageBox("Identifier is required");
//		m_LLocStart_Box.SetFocus();
//	}
	for (i = 0; invalidChars[i] != '\0' && foundInvalidchar == 0; i++) {
		if (strchr(m_RLocStart,invalidChars[i]) != NULL )
			foundInvalidchar = 1;
	}
	if (foundInvalidchar == 1) {
		AfxMessageBox("Invalid character in identifier");
		m_RLocStart = "";
		UpdateData(FALSE);
		m_RLocStart_Box.SetFocus();
	}
	if ( m_RLocStart.GetLength() > 18 ) {
		AfxMessageBox("Identifier too long.  Length must be 18 characters or less.");
		m_RLocStart = "";
		UpdateData(FALSE);
		m_RLocStart_Box.SetFocus();
	}
	for (i=1; i < 27 && isAlphaID == 0; i++) {
		if (m_RLocStart.Find(alphabetCap[i]) != -1)
			isAlphaID = 1;
	}
	for (i=1; i < 27 && isAlphaID == 0; i++) {
		if ( m_RLocStart.Find(alphabetNoCap[i]) != -1)
			isAlphaID = 1;
	}
	if (isAlphaID == 1) {
		m_RLocShemeBox.SetCurSel(0);
		m_RLocShemeBox.EnableWindow(FALSE);
		m_RLocSchemeBoxEnabled = FALSE;
	}
	else {
		m_RLocShemeBox.EnableWindow(TRUE);
		m_RLocSchemeBoxEnabled = TRUE;
	}
	
}

void CPickPathPropertiesDialog::OnKillfocusLbaystepval() 
{
	
	char buf[32]="";
	char buf2[32]="";
	char * ptr;

	if (this->IsWindowVisible() == 0)
		return;

	if (UpdateData(TRUE) == 0 ) {
		m_LBayStepBox.SetFocus();
		return;
	}
	if (m_LBayStepVal > 999999999 || m_LBayStepVal < -999999999) {
		AfxMessageBox("Number is out of range.");
		m_LBayStepVal = 0;
		UpdateData(FALSE);
		m_LBayStepBox.SetFocus();
	}
	m_LBayStepBox.GetWindowText(buf,31);
	if ( strchr(buf,'+') != NULL ) {
		ptr = strchr(buf,'+');
		ptr++;
		strcpy(buf2,ptr);
		m_LBayStepBox.SetWindowText(buf2);
	}
	UpdateData(FALSE);
}

void CPickPathPropertiesDialog::OnKillfocusLlevelstepval() 
{
	
	char buf[32]="";
	char buf2[32]="";
	char * ptr;

	if (this->IsWindowVisible() == 0)
		return;

	if (UpdateData(TRUE) == 0 ) {
		m_LLevelStepBox.SetFocus();
		return;
	}
	if (m_LLevelStep > 999999999 || m_LLevelStep < -999999999) {
		AfxMessageBox("Number is out of range.");
		m_LLevelStep = 0;
		UpdateData(FALSE);
		m_LLevelStepBox.SetFocus();
	}
	m_LLevelStepBox.GetWindowText(buf,31);
	if ( strchr(buf,'+') != NULL ) {
		ptr = strchr(buf,'+');
		ptr++;
		strcpy(buf2,ptr);
		m_LLevelStepBox.SetWindowText(buf2);
	}
	UpdateData(FALSE);
	
}

void CPickPathPropertiesDialog::OnKillfocusLlocstepval() 
{
	char buf[32]="";
	char buf2[32]="";
	char * ptr;

	if (this->IsWindowVisible() == 0)
		return;

	if (UpdateData(TRUE) == 0 ) {
		m_LLocStepBox.SetFocus();
		return;
	}
	if (m_LLevelStep > 999999999 || m_LLevelStep < -999999999) {
		AfxMessageBox("Number is out of range.");
		m_LLevelStep = 0;
		UpdateData(FALSE);
		m_LLevelStepBox.SetFocus();
	}
	m_LLevelStepBox.GetWindowText(buf,31);
	if ( strchr(buf,'+') != NULL ) {
		ptr = strchr(buf,'+');
		ptr++;
		strcpy(buf2,ptr);
		m_LLevelStepBox.SetWindowText(buf2);
	}
	UpdateData(FALSE);
	
}

void CPickPathPropertiesDialog::OnKillfocusRbaystepval() 
{
	char buf[32]="";
	char buf2[32]="";
	char * ptr;

	if (this->IsWindowVisible() == 0)
		return;

	if (UpdateData(TRUE) == 0 ) {
		m_RBayStepBox.SetFocus();
		return;
	}
	if (m_RBayStep > 999999999 || m_RBayStep < -999999999) {
		AfxMessageBox("Number is out of range.");
		m_RBayStep = 0;
		UpdateData(FALSE);
		m_RBayStepBox.SetFocus();
	}
	m_RBayStepBox.GetWindowText(buf,31);
	if ( strchr(buf,'+') != NULL ) {
		ptr = strchr(buf,'+');
		ptr++;
		strcpy(buf2,ptr);
		m_RBayStepBox.SetWindowText(buf2);
	}
	UpdateData(FALSE);
	
}

void CPickPathPropertiesDialog::OnKillfocusRlevelstepval() 
{
	char buf[32]="";
	char buf2[32]="";
	char * ptr;

	if (this->IsWindowVisible() == 0)
		return;

	if (UpdateData(TRUE) == 0 ) {
		m_RLevelStepBox.SetFocus();
		return;
	}
	if (m_RLevelStep > 999999999 || m_RLevelStep < -999999999) {
		AfxMessageBox("Number is out of range.");
		m_RLevelStep = 0;
		UpdateData(FALSE);
		m_RLevelStepBox.SetFocus();
	}
	m_RLevelStepBox.GetWindowText(buf,31);
	if ( strchr(buf,'+') != NULL ) {
		ptr = strchr(buf,'+');
		ptr++;
		strcpy(buf2,ptr);
		m_RLevelStepBox.SetWindowText(buf2);
	}
	UpdateData(FALSE);
}

void CPickPathPropertiesDialog::OnKillfocusRlocstepval() 
{
	char buf[32]="";
	char buf2[32]="";
	char * ptr;

	if (this->IsWindowVisible() == 0)
		return;

	if (UpdateData(TRUE) == 0 ) {
		m_RLocStepBox.SetFocus();
		return;
	}
	if (m_RLocStep > 999999999 || m_RLocStep < -999999999) {
		AfxMessageBox("Number is out of range.");
		m_RLocStep = 0;
		UpdateData(FALSE);
		m_RLocStepBox.SetFocus();
	}
	m_RLocStepBox.GetWindowText(buf,31);
	if ( strchr(buf,'+') != NULL ) {
		ptr = strchr(buf,'+');
		ptr++;
		strcpy(buf2,ptr);
		m_RLocStepBox.SetWindowText(buf2);
	}
	UpdateData(FALSE);
}

BOOL CPickPathPropertiesDialog::OnHelpInfo(HELPINFO* pHelpInfo) 
{
	helpService.ShowFieldHelp(IDD, pHelpInfo->iCtrlId);

	return FALSE;
}




void CPickPathPropertiesDialog::OnKillfocusLbaypattern() 
{
	UpdateData(TRUE);

	if (! m_LBayPatternVal.IsEmpty()) {
		m_LBayStart_Box.EnableWindow(FALSE);
		m_LBayStepBox.EnableWindow(FALSE);
		m_LBaySchemeBox.EnableWindow(FALSE);
	}
	else {
		m_LBayStart_Box.EnableWindow(TRUE);
		m_LBayStepBox.EnableWindow(TRUE);
		if (m_LBaySchemeBoxEnabled)
			m_LBaySchemeBox.EnableWindow(TRUE);
	}

	return;

}

void CPickPathPropertiesDialog::OnKillfocusLlevelpattern() 
{
	UpdateData(TRUE);

	if (! m_LLevelPatternVal.IsEmpty()) {
		m_LLevelStart_Box.EnableWindow(FALSE);
		m_LLevelStepBox.EnableWindow(FALSE);
		m_LLevelSchemeBox.EnableWindow(FALSE);
	}
	else {
		m_LLevelStart_Box.EnableWindow(TRUE);
		m_LLevelStepBox.EnableWindow(TRUE);
		if (m_LLevelSchemeBoxEnabled)
			m_LLevelSchemeBox.EnableWindow(TRUE);
	}


	return;

}

void CPickPathPropertiesDialog::OnKillfocusLlocpattern() 
{
	UpdateData(TRUE);

	if (! m_LLocPatternVal.IsEmpty()) {
		m_LLocStart_Box.EnableWindow(FALSE);
		m_LLocStepBox.EnableWindow(FALSE);
		m_LLocSchemeBox.EnableWindow(FALSE);
	}
	else {
		m_LLocStart_Box.EnableWindow(TRUE);
		m_LLocStepBox.EnableWindow(TRUE);
		if (m_LLocSchemeBoxEnabled)
			m_LLocSchemeBox.EnableWindow(TRUE);
	}


	return;	
}

void CPickPathPropertiesDialog::OnKillfocusRbaypattern() 
{
	UpdateData(TRUE);

	if (! m_RBayPatternVal.IsEmpty()) {
		m_RBayStart_Box.EnableWindow(FALSE);
		m_RBayStepBox.EnableWindow(FALSE);
		m_RBaySchemeBox.EnableWindow(FALSE);
	}
	else {
		m_RBayStart_Box.EnableWindow(TRUE);
		m_RBayStepBox.EnableWindow(TRUE);
		if (m_RBaySchemeBoxEnabled)
			m_RBaySchemeBox.EnableWindow(TRUE);
	}		

	return;
}

void CPickPathPropertiesDialog::OnKillfocusRlevelpattern() 
{
	UpdateData(TRUE);

	if (! m_RLevelPatternVal.IsEmpty()) {
		m_RLevelStart_Box.EnableWindow(FALSE);
		m_RLevelStepBox.EnableWindow(FALSE);
		m_RLevelSchemeBox.EnableWindow(FALSE);
	}
	else {
		m_RLevelStart_Box.EnableWindow(TRUE);
		m_RLevelStepBox.EnableWindow(TRUE);
		if (m_RLevelSchemeBoxEnabled)
			m_RLevelSchemeBox.EnableWindow(TRUE);
	}
	

	return;
}

void CPickPathPropertiesDialog::OnKillfocusRlocpattern() 
{
	UpdateData(TRUE);

	if (! m_RLocPatternVal.IsEmpty()) {
		m_RLocStart_Box.EnableWindow(FALSE);
		m_RLocStepBox.EnableWindow(FALSE);
		m_RLocShemeBox.EnableWindow(FALSE);
	}
	else {
		m_RLocStart_Box.EnableWindow(TRUE);
		m_RLocStepBox.EnableWindow(TRUE);
		if (m_RLocSchemeBoxEnabled)
			m_RLocShemeBox.EnableWindow(TRUE);
	}	


	return;
}

void CPickPathPropertiesDialog::OnCancel() 
{
	if (m_LBayPatternVal.IsEmpty())
		m_LBayPatternVal = " ";
	if (m_LLevelPatternVal.IsEmpty())
		m_LLevelPatternVal = " ";
	if (m_LLocPatternVal.IsEmpty())
		m_LLocPatternVal = " ";

	if (m_RBayPatternVal.IsEmpty())
		m_RBayPatternVal = " ";
	if (m_RLevelPatternVal.IsEmpty())
		m_RLevelPatternVal = " ";
	if (m_RLocPatternVal.IsEmpty())
		m_RLocPatternVal = " ";

	CDialog::OnCancel();
}

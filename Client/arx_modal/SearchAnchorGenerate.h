#if !defined(AFX_SEARCHANCHORGENERATE_H__3FE3DF16_E5DE_413B_A34E_E0BE03CF35DE__INCLUDED_)
#define AFX_SEARCHANCHORGENERATE_H__3FE3DF16_E5DE_413B_A34E_E0BE03CF35DE__INCLUDED_

#if _MSC_VER > 1000
#pragma once
#endif // _MSC_VER > 1000
// SearchAnchorGenerate.h : header file
//
#include "Resource.h"

/////////////////////////////////////////////////////////////////////////////
// CSearchAnchorGenerate dialog

class CSearchAnchorGenerate : public CDialog
{
// Construction
public:
	long m_ProductGroupID;
	CSearchAnchorGenerate(CWnd* pParent = NULL);   // standard constructor

// Dialog Data
	//{{AFX_DATA(CSearchAnchorGenerate)
	enum { IDD = IDD_SEARCH_ANCHOR_GENERATE };
	BOOL	m_ClearExisting;
	//}}AFX_DATA


// Overrides
	// ClassWizard generated virtual function overrides
	//{{AFX_VIRTUAL(CSearchAnchorGenerate)
	protected:
	virtual void DoDataExchange(CDataExchange* pDX);    // DDX/DDV support
	//}}AFX_VIRTUAL

// Implementation
protected:

	// Generated message map functions
	//{{AFX_MSG(CSearchAnchorGenerate)
	virtual BOOL OnInitDialog();
	virtual void OnOK();
	afx_msg void OnHelp();
	afx_msg BOOL OnHelpInfo(HELPINFO* pHelpInfo);
	//}}AFX_MSG
	DECLARE_MESSAGE_MAP()
};

//{{AFX_INSERT_LOCATION}}
// Microsoft Visual C++ will insert additional declarations immediately before the previous line.

#endif // !defined(AFX_SEARCHANCHORGENERATE_H__3FE3DF16_E5DE_413B_A34E_E0BE03CF35DE__INCLUDED_)

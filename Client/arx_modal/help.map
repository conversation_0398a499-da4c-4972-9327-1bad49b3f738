1|1|Generic_OK|
1|2|Generic_Cancel|
1|9|Generic_Help|
28|0|PlaceObject_Main|
28|1012|PlaceObject_X|
28|1013|PlaceObject_Y|
28|1014|PlaceObject_Z|
28|1018|PlaceObject_Width|
28|1019|PlaceObject_Length|
28|1021|PlaceObject_Height|
28|1084|PlaceObject_ID|
28|1088|PlaceObject_Increment|
28|1024|PlaceObject_Rotation|
30|0|AddAisle_ChooseAisle|
30|1|AddAisle_ChooseAisle_OK|
30|2|Generic_Cancel|
30|9|Generic_Help|
30|1029|AddAisle_ChooseAisle_AisleProfileList|
30|1087|AddAisle_ChooseAisle_SectionList|
30|1202|AddAisle_ChooseAisle_Number|
30|1012|AddAisle_ObjectPlace_X|
30|1013|AddAisle_ObjectPlace_Y|
30|1014|AddAisle_ObjectPlace_Z|
30|1018|AddAisle_ObjectPlace_Width|
30|1019|AddAisle_ObjectPlace_Length|
30|1021|AddAisle_ObjectPlace_Height|
30|1084|AddAisle_ObjectPlace_ID|
30|1088|AddAisle_ObjectPlace_Increment|
30|1024|AddAisle_ObjectPlace_Rotation|
33|0|PickPathOption_Main|
33|2|Generic_Cancel|
33|9|Generic_Help|
33|4|PickPathOption_New|
33|3|PickPathOption_Connect|
34|0|AddPickPath_Main|
34|1|AddPickPath_OK|
34|2|Generic_Cancel|
34|9|Generic_Help|
34|1057|AddPickPath_Type|
34|1066|AddPickPath_BaysInPattern|
34|1144|AddPickPath_BayStart|
34|1149|AddPickPath_BayStep|
34|1152|AddPickPath_BayScheme|
34|1248|AddPickPath_BayPattern|
34|1155|AddPickPath_LevelStart|
34|1158|AddPickPath_LevelStep|
34|1161|AddPickPath_LevelScheme|
34|1251|AddPickPath_LevelPattern|
34|1162|AddPickPath_LevelBreak|
34|1163|AddPickPath_PositionStart|
34|1165|AddPickPath_PositionStep|
34|1168|AddPickPath_PositionScheme|
34|1255|AddPickPath_PositionPattern|
34|1170|AddPickPath_PositionBreak|
34|1207|AddPickPath_BayStart|
34|1210|AddPickPath_BayStep|
34|1214|AddPickPath_BayScheme|
34|1249|AddPickPath_BayPattern|
34|1178|AddPickPath_LevelStart|
34|1182|AddPickPath_LevelStep|
34|1188|AddPickPath_LevelScheme|
34|1253|AddPickPath_LevelPattern|
34|1190|AddPickPath_LevelBreak|
34|1192|AddPickPath_PositionStart|
34|1195|AddPickPath_PositionStep|
34|1201|AddPickPath_PositionScheme|
34|1256|AddPickPath_PositionPattern|
34|1205|AddPickPath_PositionBreak|
39|0|AssignProductGroup_Main|
39|1|AssignProductGroup_OK|
39|2|Generic_Cancel|
39|9|Generic_Help|
39|1100|AssignProductGroup_ProductGroupList|
40|0|Mode_Main|
40|1|Mode_OK|
40|1102|Mode_Wizard|
40|1104|Mode_NewFacility|
40|1106|Mode_OpenFacility|
40|1109|Mode_Instructions|
40|1113|Mode_GettingStarted|
40|1034|Mode_UnitOfMeasurement|
40|1035|Mode_WMS|
45|0|AddHotspot_Main|
45|1|AddHotspot_OK|
45|2|Generic_Cancel|
45|9|Generic_Help|
45|1092|AddHotspot_Type|
45|1087|AddHotspot_Section|
36|0|ColorModel_Main|
36|1|Coloring_OK|
36|1072|ColorModel_Reset|
36|1073|ColorModel_TrueScale|
36|1074|ColorModel_Attribute|
36|1075|ColorModel_ChooseColor|
36|1076|ColorModel_Range|
36|1077|ColorModel_Direction|
36|1078|ColorModel_Direction|
36|1079|ColorModel_Percentage|
36|1189|ColorModel_StartValue|
36|1191|ColorModel_EndingValue|
36|1209|ColorModel_AutoReset|
41|0|ColorProductGroup_Main|
41|1|Coloring_OK|
41|2|Generic_Cancel|
41|9|Generic_Help|
41|1037|ColorProductGroup_Add|
41|1042|ColorProductGroup_Delete|
41|1075|ColorProductGroup_Color|
41|1103|ColorProductGroup_ProductGroup|
41|1107|ColorProductGroup_Assignments|
41|1211|ColorProductGroup_AutoReset|
42|2|Generic_Cancel|
42|9|Generic_Help|
43|0|LoginDlg_Main|
43|1120|LoginDlg_User|
43|1123|LoginDlg_Password|
43|1232|LoginDlg_Database|
43|1|LoginDlg_OK|
43|2|LoginDlg_Cancel|
43|1431|LoginDlg_AutoCheck|
43|9|Generic_Help|
45|0|AddHotspot_Main|
45|1|AddHotspot_OK|
45|2|Generic_Cancel|
45|9|Generic_Help|
45|1092|AddHotspot_Type|
45|1087|AddHotspot_Section|
46|0|ColorModelResults_Main|
50|0|LevelLocationProperties_Main|
50|1206|LevelLocationProperties_LevelLocTree|
50|1|LevelLocationProperties_OK|
50|2|Generic_Cancel|
50|9|Generic_Help|
37|0|SectionProperties_Main|
52|0|SectionProperties_Page1|
52|1091|SectionProperties_Description|
52|1096|SectionProperties_LocationMask|
52|1154|SectionProperties_OrderCount|
52|1143|SectionProperties_AverageOrderQuantity|
52|1147|SectionProperties_ContainerQuantity|
52|1172|SectionProperties_ApplyBrokenOrder|
52|1301|SectionProperties_TravelDistance|
52|1305|SectionProperties_SelectionHotspotCoordinates|
52|1306|SectionProperties_ForkliftHotspotCoordinates|
54|0|SectionProperties_Page2|
54|1105|SectionProperties_ForkTravelTime|
54|1110|SectionProperties_ForkFixedPerTrip|
54|1115|SectionProperties_ForkLaborRate|
54|1138|SectionProperties_ReplenishAvgDistance|
54|1218|SectionProperties_ForkInsertionTime|
54|1219|SectionProperties_ForkPickupTime|
54|1220|SectionProperties_PalletsPerPutawayTrip|
54|1121|SectionProperties_SelectionTravelTime|
54|1127|SectionProperties_SelectionFixedPerTrip|
54|1133|SectionProperties_SelectionLaborRate|
54|1221|SectionProperties_AverageCubePerTrip|
54|1215|SectionProperties_StockerTravelTime|
54|1216|SectionProperties_StockerFixedPerTrip|
54|1217|SectionProperties_StockerLaborRate|
64|0|Facility_Main|
64|1223|Facility_FacilityList|
64|1222|Facility_FacilityName|
64|1224|Facility_FacilityNotes|
64|1|Facility_OK|
64|2|Facility_Cancel|
64|1246|Facility_ViewButtons|
64|1250|Facility_ViewButtons|
64|1252|Facility_ViewButtons|
64|1254|Facility_ViewButtons|
64|9|Generic_Help|
64|1431|Facility_AutoCheck|
66|0|LayoutProducts_Main|
66|1|LayoutProducts_OK|
66|2|Generic_Cancel|
66|9|Generic_Help|
66|1257|LayoutProducts_AllowVariableWidth|
66|1258|LayoutProducts_AllowCaseReorientation|
66|1259|LayoutProducts_AllowOverhang|
66|1260|LayoutProducts_Mode|
66|1261|LayoutProducts_ConstrainAmount|
66|1262|LayoutProducts_ConstrainType|
68|0|ResultViewer_Main|
68|2|Generic_Cancel|
68|9|ResultViewer_Help|
68|17|ResultViewer_Excel|
71|0|CapitalCostInitialization_Main|
71|1|CapitalCostInitialization_OK|
71|2|Generic_Cancel|
71|9|Generic_OK|
71|1267|CapitalCostInitialization_BreakPallet|
72|1268|CapitalCostInitialization_SortRankings|
72|0|LayoutProductGroups_Main|
72|1|LayoutProductGroups_OK|
72|2|Generic_Cancel|
72|9|Generic_Help|
72|1269|LayoutProductGroups_ConnectedPickPaths|
72|1270|LayoutProductGroups_ClosestToHotspot|
75|0|ManualAssignment_Main|
75|1|Generic_OK|
75|2|Generic_CAncel|
75|9|Generic_Help|
75|1277|ManualAssignment_Products|
75|1276|ManualAssignment_Locations|
75|1289|ManualAssignment_ProductInformation|
75|1290|ManualAssignment_LocationInformation|
75|1278|ManualAssignment_QueryProducts|
75|1275|ManualAssignment_DeleteProductAssignment|
75|1280|ManualAssignment_QueryLocations|
75|1282|ManualAssignment_DeleteLocationAssignment|
75|1273|ManualAssignment_Assign|
76|0|ManualAssignmentQueryProducts_Main|
76|1|ManualAssignmentQueryProducts_OK|
76|2|Generic_Cancel|
76|9|Generic_Help|
76|1288|ManualAssignmentQueryProducts_WMSProductID|
76|1111|ManualAssignmentQueryProducts_Description|
76|1279|ManualAssignmentQueryProducts_LocationStatus|
76|1281|ManualAssignmentQueryProducts_ProductGroupStatus|
77|0|ManualAssignmentQueryLocations_Main|
77|1|ManualAssignmentQueryLocations_OK|
77|2|Generic_Cancel|
77|9|Generic_Help|
77|1284|ManualAssignmentQueryLocations_Description|
77|1285|ManualAssignmentQueryLocations_LevelType|
77|1279|ManualAssignmentQueryLocations_ProductStatus|
77|1281|ManualAssignmentQueryLocations_ProductGroupStatus|
79|0|QueryLibrary_Main|
79|1|Generic_OK|
79|2|Generic_Cancel|
79|9|Generic_Help|
79|1292|QueryLibrary_Name|
79|1111|QueryLibrary_Description|
79|1294|QueryLibrary_Query|
79|1232|QueryLibrary_Database|
79|1293|QueryLibrary_Run|
79|8|QueryLibrary_Update|
79|16|QueryLibrary_Delete|
79|12|QueryLibrary_Import|
79|15|QueryLibrary_Export|
82|0|UDFPage_Main|
83|0|AisleProperties_Main|
83|1111|AisleProperties_Description|
83|1308|AisleProperties_Coordinates|
83|1309|AisleProperties_Rotation|
84|0|BayProperties_Main|
84|1111|BayProperties_Description|
84|1310|BayProperties_Profile|
85|0|SideProperties_Main|
85|1111|SideProperties_Description|
86|0|LevelProperties_Main|
86|1111|LevelProperties_Description|
86|1312|LevelProperties_RotateAllowed|
86|1116|LevelProperties_ForkFixedInsertion|
86|1228|LevelProperties_LevelType|
86|1233|LevelProperties_RelativeLevel|
86|1308|LevelProperties_Coordinates|
86|1311|LevelProperties_VariableWidth|
86|1132|LevelProperties_ProductGap|
86|1129|LevelProperties_FacingGap|
86|1119|LevelProperties_MinLocWidth|
86|1122|LevelProperties_ProductSnap|
86|1125|LevelProperties_FacingSnap|
87|0|LocationProperties_Main|
87|1111|LocationProperties_Description|
87|1314|LocationProperties_Select|
87|1313|LocationProperties_HandlingMethod|
87|1315|LocationProperties_Width|
87|1316|LocationProperties_Depth|
87|1435|LocationProperties_Height|
87|1318|LocationProperties_MaxWeight|
87|1283|LocationProperties_ProductGroup|
87|1319|LocationProperties_WMSID|
87|1236|LocationProperties_ProductDescription|
87|1308|LocationProperties_Coordinates|
88|0|FacilityProperties_Main|
88|1111|FacilityProperties_Description|
88|1320|FacilityProperties_UOM|
88|1321|FacilityProperties_WMSType|
88|1322|FacilityProperties_TimeHorizonUnits|
88|1126|FacilityProperties_TimeHorizonValue|
88|1082|FacilityProperties_Notes|
89|0|RulesDefinition_Main|
89|1324|RulesDefinition_RackTypes|
89|1327|RulesDefinition_PctReserves|
89|1330|RulesDefinition_PctSelUtilization|
89|1329|RulesDefinition_PctRsvUtilization|
89|1328|RulesDefinition_Replenishment|
89|1331|RulesDefinition_AdditionalRsvCube|
89|1332|RulesDefinition_PalletHeight|
89|1333|RulesDefinition_Clearance|
89|1323|RulesDefinition_Facings|
89|1334|RulesDefinition_ApplyCurrent|
89|2|Generic_Cancel|
89|1020|RulesDefinition_Summary|
89|9|Generic_Help|
89|1|Generic_OK|
89|1326|RulesDefinition_AddFacing|
89|1335|RulesDefinition_Recalculate|
91|0|SearchAnchor_Main|
91|1336|SearchAnchor_Grid|
91|1036|SearchAnchor_Generate|
91|2|Generic_Cancel|
91|9|Generic_Help|
91|1|Generic_OK|
91|17|SearchAnchor_Excel|
91|19|SearchAnchor_Sort|
93|0|LocationInterface_Main|
93|1|LocationInterface_CreateFile|
93|2|Generic_Cancel|
93|1337|LocationInterface_WMSFormat|
93|1283|LocationInterface_ProductGroup|
93|1372|LocationInterface_VariableWidth|
93|9|Generic_Help|
93|1284|LocationInterface_Section|
94|0|AssignmentInterface_Main|
94|1|AssignmentInterface_CreateFile|
94|2|Generic_Cancel|
94|9|Generic_Help|
94|1337|AssignmentInterface_WMSFormat|
94|1283|AssignmentInterface_ProductGroup|
94|1373|AssignmentInterface_FullExport|
94|1284|AssignmentInterface_Section|
95|0|DisplayResultsCount_Number|
95|1|Generic_OK|
98|0|DataModel_Main|
98|1|DataModel_OK|
98|2|DataModel_Cancel|
98|9|Generic_Help|
98|1272|DataModel_Quantity|
98|1340|DataModel_DataGrid|
102|0|ProductPage_Main|
102|1111|ProductPage_Description|
102|1343|ProductPage_WMSProductID|
102|1344|ProductPage_WMSProductDetailID|
102|1342|ProductPage_IsAssignmentLocked|
102|1345|ProductPage_CasePack|
102|1346|ProductPage_InnerPack|
102|1347|ProductPage_UnitOfIssue|
102|1351|ProductPage_Movement|
102|1357|ProductPage_BalanceOnHand|
102|1341|ProductPage_OptimizeBy|
102|1352|ProductPage_NumberOfHits|
102|1349|ProductPage_IsHazard|
102|1350|ProductPage_IsPickToBelt|
102|1358|ProductPage_RotateYAxis|
102|1359|ProductPage_RotateZAxis|
102|1360|ProductPage_RotateXAxis|
102|1315|ProductPage_CaseWidth|
102|1243|ProductPage_CaseLength|
102|1435|ProductPage_CaseHeight|
102|1234|ProductPage_InnerWidth|
102|1229|ProductPage_InnerLength|
102|1237|ProductPage_InnerHeight|
102|1245|ProductPage_EachWidth|
102|1244|ProductPage_EachLength|
102|1247|ProductPage_EachHeight|
102|1354|ProductPage_Weight|
102|1241|ProductPage_MaxStackNumber|
102|1239|ProductPage_NumberInPallet|
103|0|ContainerPage_Main|
103|1230|ContainerPage_ContainerWidth|
103|1364|ContainerPage_ContainerLength|
103|1242|ContainerPage_ContainerHeight|
103|1238|ContainerPage_TI|
103|1240|ContainerPage_HI|
103|1361|ContainerPage_IsWidthOverride|
103|1362|ContainerPage_IsLengthOverride|
103|1363|ContainerPage_IsHeightOverride|
104|0|ProductOptimizePage_Main|
104|1283|ProductOptimizePage_ProductGroup|
104|1348|ProductOptimizePage_Location|
104|1365|ProductOptimizePage_LocationProductGroup|
104|1368|ProductOptimizePage_BayProfile|
104|1228|ProductOptimizePage_LevelType|
104|1369|ProductOptimizePage_RotatedWidth|
104|1370|ProductOptimizePage_RotatedLength|
104|1371|ProductOptimizePage_RotatedHeight|
104|1374|ProductOptimizePage_UpdateGroup|
105|0|ProductGroupMaintenance_Main|
105|9|Generic_Help|
105|1375|ProductGroupMaintenance_List|
105|1037|ProductGroupMaintenance_Add|
105|1042|ProductGroupMaintenance_Delete|
105|1408|ProductGroupMaintenance_Properties|
105|1392|ProductGroupMaintenance_MoveUp|
105|1394|ProductGroupMaintenance_MoveDown|
105|4|ProductGroupMaintenance_Apply|
107|0|ProductGroupProperties_Main|
107|1231|ProductGroupProperties_Name|
107|1376|ProductGroupProperties_LockProductGroup|
107|1377|ProductGroupProperties_LockLocations|
107|1379|ProductGroupProperties_PercentOpenLocs|
107|1382|ProductGroupProperties_OptimizeAttribute|
107|1380|ProductGroupProperties_OptimizeMethod|
108|0|ProductGroupLayoutConstraints_Main|
108|1381|ProductGroupLayoutConstraints_FacilityTree|
108|1406|ProductGroupLayoutConstraints_RemoveNonExclusive|
108|1403|ProductGroupLayoutConstraints_AddNonExclusive|
108|1385|ProductGroupLayoutConstraints_RemoveExclusive|
108|1043|ProductGroupLayoutConstraints_AddExclusive|
108|1413|ProductGroupLayoutConstraints_LevelList|
108|1398|ProductGroupLayoutConstraints_NonExclusiveList|
108|1407|ProductGroupLayoutConstraints_ExclusiveList|
108|1383|ProductGroupLayoutConstraints_Exclusive|
109|0|ProductGroupCriteria_Main|
109|9|Generic_Help|
109|4|ProductGroupCriteria_Apply|
109|1386|ProductGroupCriteria_CriteriaGrid|
110|0|CriteriaMaintenance_Main|
110|1387|CriteriaMaintenance_CriteriaList|
110|1408|CriteriaMaintenance_Properties|
110|1037|CriteriaMaintenance_Add|
110|1042|CriteriaMaintenance_Delete|
110|9|Generic_Help|
111|0|CriteriaProperties_Main|
111|1231|CriteriaProperties_Name|
111|1111|CriteriaProperties_Description|
111|1074|CriteriaProperties_Attribute|
111|1410|CriteriaProperties_RadioList|
111|1411|CriteriaProperties_RadioRange|
111|1038|CriteriaProperties_Formual|
111|1430|CriteriaProperties_FormulaCheck|
111|1036|CriteriaProperties_ValidateFormula|
112|0|CriteriaList_Main|
112|1389|CriteriaList_RangeList|
112|1414|CriteriaList_AddElement|
112|1415|CriteriaList_DeleteElement|
112|1391|CriteriaList_Operator|
112|1401|CriteriaList_FromValue|
112|1402|CriteriaList_ToValue|
112|1390|CriteriaList_ValueList|
112|1396|CriteriaList_AddValue|
112|1395|CriteriaList_DeleteValue|
112|1400|CriteriaList_LoadValues|
112|1405|CriteriaList_LoadPossibleValues|
115|0|ProductGroupCriteriaQuery_Main|
115|1391|ProductGroupCriteriaQuery_Operator|
115|1401|ProductGroupCriteriaQuery_FromValue|
115|1402|ProductGroupCriteriaQuery_ToValue|
115|1|Generic_OK|
115|2|Generic_Cancel|
115|9|Generic_Help|
106|0|ProductGroupAssignment_Main|
106|1375|ProductGroupAssignment_ProductGroupList|
106|1040|ProductGroupAssignment_Count|
106|1044|ProductGroupAssignment_Assign|
106|9|Generic_Help|
106|1047|ProductGroupAssignment_ViewProducts|
116|0|Progress_Main|
116|1418|Progress_Progress|
116|1041|Progress_Stop|
96|0|ProductGroupNavigator_Main|
96|1422|ProductGroupNavigator_MaintainGroups|
96|1424|ProductGroupNavigator_MaintainCriteria|
96|1426|ProductGroupNavigator_AssignCriteria|
96|22|ProductGroupNavigator_AssignProducts|
96|127|ProductGroupNavigator_ShowHelp|
96|1429|ProductGroupNavigator_Quit|
143|0|BayWizard_BayProfilePage|
143|1486|BayWizard_BayProfilePage_BayProfileList|
143|1310|BayWizard_BayProfilePage_Name|
143|1566|BayWizard_BayProfilePage_HideExcluded|
143|1521|BayWizard_BayProfilePage_Floating|
143|1518|BayWizard_BayProfilePage_Hazard|
143|1547|BayWizard_BayProfilePage_Exclude|
144|0|BayWizard_DimensionPage|
144|1446|BayWizard_DimensionPage_FrontView|
144|1448|BayWizard_DimensionPage_TopView|
144|1435|BayWizard_DimensionPage_Height|
144|1315|BayWizard_DimensionPage_Width|
144|1316|BayWizard_DimensionPage_Depth|
144|1442|BayWizard_DimensionPage_RackCost|
144|1444|BayWizard_DimensionPage_UprightWidth|
144|1445|BayWizard_DimensionPage_WeightCapacity|
144|1503|BayWizard_DimensionPage_PalletsDeep|
144|1509|BayWizard_DimensionPage_PalletHeight|
144|1505|BayWizard_DimensionPage_FlowDiff|
144|1539|BayWizard_DimensionPage_SpanBays|
149|0|BayWizard_CrossbarPage|
149|1446|BayWizard_CrossbarPage_LevelButton|
149|1451|BayWizard_CrossbarPage_Height|
149|1445|BayWizard_CrossbarPage_WeightCapacity|
149|1452|BayWizard_CrossbarPage_Thickness|
149|1517|BayWizard_CrossbarPage_Hidden|
149|32771|BayWizard_CrossbarPage_Delete|
147|0|BayWizard_LocationPage|
147|1446|BayWizard_LevelButton|
147|1453|BayWizard_LocationPage_Level|
147|1458|BayWizard_LocationPage_Overhang|
147|1520|BayWizard_LocationPage_Rotation|
147|1522|BayWizard_LocationPage_VariableWidth|
147|1454|BayWizard_LocationPage_LocationCount|
147|1455|BayWizard_LocationPage_LocationSpace|
147|1460|BayWizard_LocationPage_ProductGap|
147|1462|BayWizard_LocationPage_ProductSnap|
147|1463|BayWizard_LocationPage_FacingGap|
147|1465|BayWizard_LocationPage_FacingSnap|
147|1466|BayWizard_LocationPage_MinLocWidth|
158|0|BayWizard_LocationInfoPage|
158|1000|BayWizard_LocationInfoPage_IsSelect|
158|1001|BayWizard_LocationInfoPage_HandlingMethod|
158|1002|BayWizard_LocationInfoPage_BayType|
158|1446|BayWizard_LevelButton|
164|0|BayWizard_LaborPage|
164|1446|BayWizard_LevelButton|
164|1542|BayWizard_LaborPage_Level|
164|1543|BayWizard_LaborPage_ForkFixedInsertion|
164|1544|BayWizard_LaborPage_ForkFixedExtraction|
164|1513|BayWizard_LaborPage_SelectionTable|
164|1546|BayWizard_LaborPage_SelectionTable|
157|0|SideWizard_SideProfilePage_Main|
157|1512|SideWizard_SideProfilePage_SideProfileList|
157|1478|SideWizard_SideProfilePage_Name|
157|1479|SideWizard_SideProfilePage_Fixed|
157|1481|SideWizard_SideProfilePage_Units|
157|1480|SideWizard_SideProfilePage_Variable|
154|0|SideWizard_BayPage_Main|
154|1477|SideWizard_BayPage_BayProfileList|
154|1272|SideWizard_BayPage_Number|
154|1474|SideWizard_BayPage_Insert|
154|1475|SideWizard_BayPage_Fill|
154|1042|SideWizard_BayPage_Delete|
154|1566|SideWizard_BayPage_HideExcluded|
154|1472|SideWizard_BayPage_SideView|
160|0|AisleWizard_AisleProfilePage_Main|
160|1529|AisleWizard_AisleProfilePage_AisleProfileList|
160|1491|AisleWizard_AisleProfilePage_Name|
159|0|AisleWizard_SidePage_Main|
159|1483|AisleWizard_SidePage_LeftSide|
159|1484|AisleWizard_SidePage_RightSide|
152|0|AisleWizard_DimensionPage_Main|
152|1470|AisleWizard_DimensionPage_LeftSpace|
152|1471|AisleWizard_DimensionPage_RightSpace|
152|1315|AisleWizard_DimensionPage_Width|
152|1446|AisleWizard_DimensionPage_AisleView|
161|0|DeleteAisleProfile_Main|
161|1|DeleteAisleProfile_OK|
161|2|Generic_Cancel|
161|9|Generic_Help|
161|1533|DeleteAisleProfile_AisleProfileList|
162|0|DeleteBayProfile_Main|
162|1|DeleteBayProfile_OK|
162|2|Generic_Cancel|
162|9|Generic_Help|
162|1535|DeleteBayProfile_BayProfileList|
163|0|DeleteSideProfile_Main|
163|1|DeleteSideProfile_OK|
163|2|Generic_Cancel|
163|9|Generic_Help|
163|1536|DeleteSideProfile_SideProfileList|
165|0|DataPurification_Main|
165|1340|DataPurification_DataGrid|
165|1375|DataPurification_ProductGroupList|
165|1555|DataPurification_AssignmentStatus|
165|1556|DataPurification_AssignmentStatus|
165|1557|DataPurification_AssignmentStatus|
165|1551|DataPurification_View|
165|1553|DataPurification_Reset|
165|2|Generic_Cancel|
165|9|Generic_Help|
166|0|CostComparison_Main|
166|1|Generic_OK|
166|2|Generic_Cancel|
166|9|Generic_Help|
166|1223|CostComparison_FacilityList|
166|1036|CostComparison_RecalculateCost|
166|1126|CostComparison_TimeHorizonValue|
166|1322|CostComparison_TimeHorizonUnits|
166|1045|CostComparison_Refresh|
168|0|GenerateMoves_Main|
168|1559|GenerateMoves_LoadMoves|
168|1560|GenerateMoves_LoadMoves|
168|1223|GenerateMoves_FacilityList|
168|1|GenerateMoves_OK|
168|2|Generic_Cancel|
168|9|Generic_Help|
169|0|UDFMaintenance_Main|
169|1560|UDFMaintenance_UDFType|
169|1412|UDFMaintenance_UDFList|
169|1036|UDFMaintenance_AddUDF|
169|1037|UDFMaintenance_ModifyUDF|
169|1038|UDFMaintenance_DeleteUDF|
169|1|Generic_OK|
169|2|Generic_Cancel|
169|9|Generic_Help|
169|1561|UDFMaintenance_BayProfileList|
170|0|UDFProperties_Main|
170|1231|UDFProperties_Name|
170|1561|UDFProperties_Type|
170|1232|UDFProperties_DefaultValue|
170|1412|UDFProperties_ListElements|
170|1414|UDFProperties_AddElement|
170|1415|UDFProperties_DeleteElement|
170|1|Generic_OK|
170|2|Generic_Cancel|
170|9|Generic_Help|
179|0|SearchAnchorGenerate_Main|
179|1|Generic_OK|
179|2|Generic_Cancel|
179|9|Generic_Help|
179|1283|SearchAnchorGenerate_ProductGroup|
179|15690|SearchAnchorGenerate_ClearExisting|

#if !defined(AFX_PRODUCTGROUPPROPERTIESPAGE_H__B7182541_0457_11D5_9EC8_00C04FAC149C__INCLUDED_)
#define AFX_PRODUCTGROUPPROPERTIESPAGE_H__B7182541_0457_11D5_9EC8_00C04FAC149C__INCLUDED_

#include "ProductGroup.h"	// Added by ClassView
#if _MSC_VER > 1000
#pragma once
#endif // _MSC_VER > 1000
// ProductGroupPropertiesPage.h : header file
//
#include "Resource.h"
#include "ProductAttribute.h"
/////////////////////////////////////////////////////////////////////////////
// CProductGroupPropertiesPage dialog

class CProductGroupPropertiesPage : public CPropertyPage
{
	DECLARE_DYNCREATE(CProductGroupPropertiesPage)

// Construction
public:
	CProductGroup *m_ProductGroup;
	CProductGroupPropertiesPage();
	~CProductGroupPropertiesPage();
	CTypedPtrArray<CObArray, CProductAttribute*> *m_ProductAttributeList;

// Dialog Data
	//{{AFX_DATA(CProductGroupPropertiesPage)
	enum { IDD = IDD_PRODUCT_GROUP_PROPERTIES };
	BOOL	m_LockLocations;
	BOOL	m_LockProductGroup;
	CString	m_Name;
	CString	m_PercentOpenLocs;
	//}}AFX_DATA


// Overrides
	// ClassWizard generate virtual function overrides
	//{{AFX_VIRTUAL(CProductGroupPropertiesPage)
	public:
	virtual void OnOK();
	virtual BOOL OnKillActive();
	protected:
	virtual void DoDataExchange(CDataExchange* pDX);    // DDX/DDV support
	virtual BOOL OnNotify(WPARAM wParam, LPARAM lParam, LRESULT* pResult);
	//}}AFX_VIRTUAL

// Implementation
protected:
	// Generated message map functions
	//{{AFX_MSG(CProductGroupPropertiesPage)
	virtual BOOL OnInitDialog();
	afx_msg void OnLockLocations();
	afx_msg void OnLockProductGroup();
	afx_msg BOOL OnHelpInfo(HELPINFO* pHelpInfo);
	//}}AFX_MSG
	DECLARE_MESSAGE_MAP()

};

//{{AFX_INSERT_LOCATION}}
// Microsoft Visual C++ will insert additional declarations immediately before the previous line.

#endif // !defined(AFX_PRODUCTGROUPPROPERTIESPAGE_H__B7182541_0457_11D5_9EC8_00C04FAC149C__INCLUDED_)

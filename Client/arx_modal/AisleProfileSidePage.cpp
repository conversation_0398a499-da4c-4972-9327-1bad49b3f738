// AisleProfileSidePage.cpp : implementation file
//

#include "stdafx.h"
#include "modal.h"
#include "AisleProfileSidePage.h"
#include "AisleProfileSheet.h"
#include "AisleProfileDataService.h"
#include "SideProfileDataService.h"
#include "WizardHelper.h"
#include "UtilityHelper.h"
#include "Constants.h"
#include "HelpService.h"

#ifdef _DEBUG
#define new DEBUG_NEW
#undef THIS_FILE
static char THIS_FILE[] = __FILE__;
#endif

extern CSideProfileDataService sideProfileDataService;
extern CUtilityHelper utilityHelper;
extern CHelpService helpService;
/////////////////////////////////////////////////////////////////////////////
// CAisleProfileSidePage property page

IMPLEMENT_DYNCREATE(CAisleProfileSidePage, CPropertyPage)

CAisleProfileSidePage::CAisleProfileSidePage() : CPropertyPage(CAisleProfileSidePage::IDD)
{
	//{{AFX_DATA_INIT(CAisleProfileSidePage)
	m_Name = _T("");
	//}}AFX_DATA_INIT
}

CAisleProfileSidePage::~CAisleProfileSidePage()
{
}

void CAisleProfileSidePage::DoDataExchange(CDataExchange* pDX)
{
	CPropertyPage::DoDataExchange(pDX);
	//{{AFX_DATA_MAP(CAisleProfileSidePage)
	DDX_Control(pDX, IDC_RIGHT_SIDE_LIST, m_RightSideListCtrl);
	DDX_Control(pDX, IDC_LEFT_SIDE_LIST, m_LeftSideListCtrl);
	DDX_Text(pDX, IDC_NAME, m_Name);
	//}}AFX_DATA_MAP
}


BEGIN_MESSAGE_MAP(CAisleProfileSidePage, CPropertyPage)
	//{{AFX_MSG_MAP(CAisleProfileSidePage)
	ON_LBN_DBLCLK(IDC_LEFT_SIDE_LIST, OnDblclkLeftSideList)
	ON_LBN_DBLCLK(IDC_RIGHT_SIDE_LIST, OnDblclkRightSideList)
	ON_WM_CONTEXTMENU()
	ON_COMMAND(ID_GENERIC_ADD, OnDblclkLeftSideList)
	ON_COMMAND(ID_GENERIC_PROPERTIES, OnDblclkRightSideList)
	ON_WM_HELPINFO()
	//}}AFX_MSG_MAP
END_MESSAGE_MAP()

/////////////////////////////////////////////////////////////////////////////
// CAisleProfileSidePage message handlers

BOOL CAisleProfileSidePage::OnInitDialog() 
{
	CPropertyPage::OnInitDialog();
	
	CStringArray sideList;

	try {
		sideProfileDataService.GetSideProfileList(sideList);
	}
	catch (...) {
		AfxMessageBox("Error loading side profiles.");
	}

	int dbid;
	CString description;

	int nItem = m_LeftSideListCtrl.AddString("{No Left Side}");
	m_LeftSideListCtrl.SetItemData(nItem, 0);
	nItem = m_RightSideListCtrl.AddString("{No Right Side}");
	m_RightSideListCtrl.SetItemData(nItem, 0);

	for (int i=0; i < sideList.GetSize(); ++i) {
		int idx = sideList[i].Find("|");
		dbid = atoi(sideList[i].Left(idx));
		description = sideList[i].Mid(idx+1);
		description.TrimRight("|");

		int nItem = m_LeftSideListCtrl.AddString(description);
		m_LeftSideListCtrl.SetItemData(nItem, (unsigned long)dbid);
		nItem = m_RightSideListCtrl.AddString(description);
		m_RightSideListCtrl.SetItemData(nItem, (unsigned long)dbid);
	}

	
	return TRUE;  // return TRUE unless you set the focus to a control
	              // EXCEPTION: OCX Property Pages should return FALSE
}

BOOL CAisleProfileSidePage::OnSetActive() 
{
	CAisleProfileSheet *pSheet = (CAisleProfileSheet *)GetParent();
	m_pAisleProfile = pSheet->m_pAisleProfile;

	if (m_pAisleProfile->m_pLeftSideProfile != NULL) {
		for (int i=0; i < m_LeftSideListCtrl.GetCount(); ++i) {
			if (m_LeftSideListCtrl.GetItemData(i) == (unsigned long)m_pAisleProfile->m_pLeftSideProfile->m_SideProfileDBId) {
				m_LeftSideListCtrl.SetCurSel(i);
				break;
			}
		}
	}
	else
		m_LeftSideListCtrl.SetCurSel(0);

	if (m_pAisleProfile->m_pRightSideProfile != NULL) {
		for (int i=0; i < m_RightSideListCtrl.GetCount(); ++i) {
			if (m_RightSideListCtrl.GetItemData(i) == (unsigned long)m_pAisleProfile->m_pRightSideProfile->m_SideProfileDBId) {
				m_RightSideListCtrl.SetCurSel(i);
				break;
			}
		}
	}
	else
		m_RightSideListCtrl.SetCurSel(0);
		
	m_Name = m_pAisleProfile->m_Description;

	UpdateData(FALSE);

	return CPropertyPage::OnSetActive();
}

BOOL CAisleProfileSidePage::OnKillActive() 
{
	UpdateData(TRUE);

	if ( m_Name.FindOneOf(BAD_FILE_CHARACTERS) != -1 ) {
		CString temp;
		temp.Format("The following characters are not allowed in the aisle profile name: \n%s",
			BAD_FILE_CHARACTERS);
		AfxMessageBox(temp);
		utilityHelper.SetEditControlErrorState(this, IDC_NAME);
		return FALSE;
	}


	CAisleProfileDataService service;

	try {
		if (service.IsAisleProfileNameInUse(m_pAisleProfile->m_AisleProfileDBId, m_Name)) {
			AfxMessageBox("The specified Aisle Profile Name is already in use.  Please enter a unique name.");
			utilityHelper.SetEditControlErrorState(this, IDC_NAME);
			return FALSE;
		}
	}
	catch (...) {
		utilityHelper.ProcessError("Error determining if profile name is already in use.");
		return FALSE;
	}


	int leftSel = m_LeftSideListCtrl.GetCurSel();
	int rightSel = m_RightSideListCtrl.GetCurSel();
	int leftId, rightId;
	
	if (leftSel != LB_ERR)
		leftId = m_LeftSideListCtrl.GetItemData(leftSel);
	else
		leftId = 0;

	if (rightSel != LB_ERR)
		rightId = m_RightSideListCtrl.GetItemData(rightSel);
	else
		rightId = 0;


	if (leftId == 0 && rightId == 0) {
		AfxMessageBox("Please select at least one side profile.");
		return FALSE;
	}

	m_pAisleProfile->m_Description = m_Name;

	if (leftId > 0) {	
		if (m_pAisleProfile->m_pLeftSideProfile == NULL)
			m_pAisleProfile->m_pLeftSideProfile = new CSideProfile;
		
		m_pAisleProfile->m_pLeftSideProfile->m_SideProfileDBId = leftId;
	}
	else {
		if (m_pAisleProfile->m_pLeftSideProfile != NULL) {
			delete m_pAisleProfile->m_pLeftSideProfile;
			m_pAisleProfile->m_pLeftSideProfile = NULL;
		}
	}

	if (rightId > 0) {	
		if (m_pAisleProfile->m_pRightSideProfile == NULL)
			m_pAisleProfile->m_pRightSideProfile = new CSideProfile;
		
		m_pAisleProfile->m_pRightSideProfile->m_SideProfileDBId = rightId;
	}
	else {
		if (m_pAisleProfile->m_pRightSideProfile != NULL) {
			delete m_pAisleProfile->m_pRightSideProfile;
			m_pAisleProfile->m_pRightSideProfile = NULL;
		}
	}

	
	return CPropertyPage::OnKillActive();
}

void CAisleProfileSidePage::OnDblclkLeftSideList() 
{
	int curSel = m_LeftSideListCtrl.GetCurSel();
	if (curSel == LB_ERR)
		return;

	CString name;
	m_LeftSideListCtrl.GetText(curSel, name);

	int dbid = m_LeftSideListCtrl.GetItemData(curSel);
	if (curSel == 0)
		return;

	CSideProfile *pSideProfile = new CSideProfile;
	pSideProfile->m_SideProfileDBId = dbid;

	CWizardHelper wizardHelper;

	if (wizardHelper.ShowSideWizard(pSideProfile) < 0) {
		delete pSideProfile;
		return;
	}

	if (name != pSideProfile->m_Description) {
		m_LeftSideListCtrl.DeleteString(curSel);
		int nItem = m_LeftSideListCtrl.InsertString(curSel, pSideProfile->m_Description);
		m_LeftSideListCtrl.SetItemData(nItem, dbid);
		m_LeftSideListCtrl.SetCurSel(nItem);

		m_RightSideListCtrl.DeleteString(curSel);
		nItem = m_RightSideListCtrl.InsertString(curSel, pSideProfile->m_Description);
		m_RightSideListCtrl.SetItemData(nItem, dbid);
	}

	
	delete pSideProfile;

}

void CAisleProfileSidePage::OnDblclkRightSideList() 
{
	int curSel = m_RightSideListCtrl.GetCurSel();
	if (curSel == LB_ERR)
		return;

	CString name;
	m_RightSideListCtrl.GetText(curSel, name);
	int dbid = m_RightSideListCtrl.GetItemData(curSel);
	if (curSel == 0)
		return;

	CSideProfile *pSideProfile = new CSideProfile;
	pSideProfile->m_SideProfileDBId = dbid;

	CWizardHelper wizardHelper;

	if (wizardHelper.ShowSideWizard(pSideProfile) < 0) {
		delete pSideProfile;
		return;
	}


	if (name != pSideProfile->m_Description) {
		m_RightSideListCtrl.DeleteString(curSel);
		int nItem = m_RightSideListCtrl.InsertString(curSel, pSideProfile->m_Description);
		m_RightSideListCtrl.SetItemData(nItem, dbid);
		m_RightSideListCtrl.SetCurSel(nItem);

		m_LeftSideListCtrl.DeleteString(curSel);
		nItem = m_LeftSideListCtrl.InsertString(curSel, pSideProfile->m_Description);
		m_LeftSideListCtrl.SetItemData(nItem, dbid);		
	}

	
	delete pSideProfile;
	
}

void CAisleProfileSidePage::OnContextMenu(CWnd* pWnd, CPoint point) 
{
	if (pWnd != &m_LeftSideListCtrl && pWnd != &m_RightSideListCtrl)
		return;

	CMenu menu;
	menu.LoadMenu(IDR_GENERIC_MENU);

	CPoint pt(point);
	BOOL bOutSide;

	if (pWnd == &m_LeftSideListCtrl) {
		m_LeftSideListCtrl.ScreenToClient(&pt);

		int curSel = m_LeftSideListCtrl.ItemFromPoint(pt, bOutSide);
		if (curSel >= 0 && ! bOutSide) {
			m_LeftSideListCtrl.SetCurSel(curSel);
			menu.GetSubMenu(0)->DeleteMenu(1, MF_BYPOSITION); 
			menu.GetSubMenu(0)->DeleteMenu(1, MF_BYPOSITION);
			menu.GetSubMenu(0)->ModifyMenu(0, MF_BYPOSITION|MF_STRING, ID_GENERIC_ADD, "&Properties");
		}
		else
			return;
	}
	else {
		m_RightSideListCtrl.ScreenToClient(&pt);

		int curSel = m_RightSideListCtrl.ItemFromPoint(pt, bOutSide);
		if (curSel >= 0 && ! bOutSide) {
			m_RightSideListCtrl.SetCurSel(curSel);
			menu.GetSubMenu(0)->DeleteMenu(1, MF_BYPOSITION); 
			menu.GetSubMenu(0)->DeleteMenu(1, MF_BYPOSITION);
			menu.GetSubMenu(0)->ModifyMenu(0, MF_BYPOSITION|MF_STRING, ID_GENERIC_PROPERTIES, "&Properties");
		}
		else
			return;
	}

	menu.GetSubMenu(0)->TrackPopupMenu(TPM_LEFTALIGN|TPM_RIGHTBUTTON, point.x, point.y, this);		
}


BOOL CAisleProfileSidePage::OnHelpInfo(HELPINFO* pHelpInfo) 
{
	helpService.ShowFieldHelp(IDD, pHelpInfo->iCtrlId);
	return FALSE;

}

BOOL CAisleProfileSidePage::OnNotify(WPARAM wParam, LPARAM lParam, LRESULT* pResult) 
{
	NMHDR* pNMHDR = (NMHDR*)lParam;

	if (pNMHDR->code == PSN_HELP) {
		helpService.ShowScreenHelp(IDD);
		return TRUE;
	}
	
	return CPropertyPage::OnNotify(wParam, lParam, pResult);
}
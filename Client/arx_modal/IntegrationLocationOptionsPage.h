#if !defined(AFX_INTEGRATIONLOCATIONOPTIONSPAGE_H__B5F24DFE_5115_4824_A55E_452039946EB9__INCLUDED_)
#define AFX_INTEGRATIONLOCATIONOPTIONSPAGE_H__B5F24DFE_5115_4824_A55E_452039946EB9__INCLUDED_

#if _MSC_VER > 1000
#pragma once
#endif // _MSC_VER > 1000
// IntegrationLocationOptionsPage.h : header file
//

/////////////////////////////////////////////////////////////////////////////
// CIntegrationLocationOptionsPage dialog

class CIntegrationLocationOptionsPage : public CPropertyPage
{
	DECLARE_DYNCREATE(CIntegrationLocationOptionsPage)

// Construction
public:
	CIntegrationLocationOptionsPage();
	~CIntegrationLocationOptionsPage();

// Dialog Data
	//{{AFX_DATA(CIntegrationLocationOptionsPage)
	enum { IDD = IDD_INTEGRATION_LOCATION_OPTIONS };
	BOOL	m_AutoConfirm;
	BOOL	m_FullExport;
	BOOL	m_InboundPrompt;
	BOOL	m_NoUpdate;
	BOOL	m_OutboundPrompt;
	BOOL	m_SkipLocation;
	//}}AFX_DATA


// Overrides
	// ClassWizard generate virtual function overrides
	//{{AFX_VIRTUAL(CIntegrationLocationOptionsPage)
	protected:
	virtual void DoDataExchange(CDataExchange* pDX);    // DDX/DDV support
	virtual BOOL OnNotify(WPARAM wParam, LPARAM lParam, LRESULT* pResult);
	//}}AFX_VIRTUAL

// Implementation
protected:
	// Generated message map functions
	//{{AFX_MSG(CIntegrationLocationOptionsPage)
	virtual BOOL OnInitDialog();
	afx_msg void OnAutoConfirm();
	afx_msg void OnFullExport();
	afx_msg void OnInboundPrompt();
	afx_msg void OnNoUpdate();
	afx_msg void OnOutboundPrompt();
	afx_msg void OnSkip();
	afx_msg BOOL OnHelpInfo(HELPINFO* pHelpInfo);
	//}}AFX_MSG
	DECLARE_MESSAGE_MAP()

};

//{{AFX_INSERT_LOCATION}}
// Microsoft Visual C++ will insert additional declarations immediately before the previous line.

#endif // !defined(AFX_INTEGRATIONLOCATIONOPTIONSPAGE_H__B5F24DFE_5115_4824_A55E_452039946EB9__INCLUDED_)

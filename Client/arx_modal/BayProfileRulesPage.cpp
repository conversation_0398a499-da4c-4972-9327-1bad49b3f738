// BayProfileRulesPage.cpp : implementation file
//

#include "stdafx.h"
#include "modal.h"
#include "BayProfileRulesPage.h"
#include "BayProfileSheet.h"
#include "Prompt.h"
#include "BayProfileEditFacingInfo.h"
#include "HelpService.h"

#ifdef _DEBUG
#define new DEBUG_NEW
#undef THIS_FILE
static char THIS_FILE[] = __FILE__;
#endif

extern CHelpService helpService;
/////////////////////////////////////////////////////////////////////////////
// CBayProfileRulesPage property page

IMPLEMENT_DYNCREATE(CBayProfileRulesPage, CPropertyPage)

CBayProfileRulesPage::CBayProfileRulesPage() : CPropertyPage(CBayProfileRulesPage::IDD)
{
	//{{AFX_DATA_INIT(CBayProfileRulesPage)
	m_PalletHeight = 0.0;
	m_AdditionalRsvCube = 0.0;
	m_PercentReserves = 0.0;
	m_PctRsvUtil = 0.0;
	m_PctSelUtil = 0.0;
	m_DesiredReplenishments = 0.0;
	//}}AFX_DATA_INIT
}

CBayProfileRulesPage::~CBayProfileRulesPage()
{
}

void CBayProfileRulesPage::DoDataExchange(CDataExchange* pDX)
{
	CPropertyPage::DoDataExchange(pDX);
	//{{AFX_DATA_MAP(CBayProfileRulesPage)
	DDX_Control(pDX, IDC_BAYTYPE_LIST, m_BayTypeListCtrl);
	DDX_Control(pDX, IDC_FACING_LIST, m_FacingListCtrl);
	DDX_Text(pDX, IDC_PALLET_HEIGHT, m_PalletHeight);
	DDV_MinMaxDouble(pDX, m_PalletHeight, 0., 99999999.);
	DDX_Text(pDX, IDC_ADDITIONAL_RSV_CUBE, m_AdditionalRsvCube);
	DDV_MinMaxDouble(pDX, m_AdditionalRsvCube, 0., 99999999.);
	DDX_Text(pDX, IDC_PCT_RESERVES, m_PercentReserves);
	DDV_MinMaxDouble(pDX, m_PercentReserves, 0., 100.);
	DDX_Text(pDX, IDC_PCT_RSV_UTILIZATION, m_PctRsvUtil);
	DDV_MinMaxDouble(pDX, m_PctRsvUtil, 0., 100.);
	DDX_Text(pDX, IDC_PCT_SEL_UTILIZATION, m_PctSelUtil);
	DDV_MinMaxDouble(pDX, m_PctSelUtil, 0., 100.);
	DDX_Text(pDX, IDC_REPLENISHMENTS, m_DesiredReplenishments);
	DDV_MinMaxDouble(pDX, m_DesiredReplenishments, 0., 99999999.);
	//}}AFX_DATA_MAP
}


BEGIN_MESSAGE_MAP(CBayProfileRulesPage, CPropertyPage)
	//{{AFX_MSG_MAP(CBayProfileRulesPage)
	ON_CBN_SELCHANGE(IDC_BAYTYPE_LIST, OnSelchangeBaytypeList)
	ON_BN_CLICKED(IDC_RECALCULATE, OnRecalculate)
	ON_BN_CLICKED(IDC_ADD_FACING, OnAddFacing)
	ON_BN_CLICKED(IDC_EDIT, OnEdit)
	ON_WM_HELPINFO()
	//}}AFX_MSG_MAP
END_MESSAGE_MAP()

/////////////////////////////////////////////////////////////////////////////
// CBayProfileRulesPage message handlers

BOOL CBayProfileRulesPage::OnInitDialog() 
{
	CPropertyPage::OnInitDialog();
	
	CRect r;
	for (int i=0; i < m_FacingListCtrl.GetHeaderCtrl()->GetItemCount(); ++i)
		m_FacingListCtrl.DeleteColumn(0);

	m_FacingListCtrl.GetClientRect(&r);
	m_FacingListCtrl.InsertColumn(0, "Facings", LVCFMT_RIGHT, r.Width()/5);
	m_FacingListCtrl.InsertColumn(1, "Extended Cube", LVCFMT_RIGHT, r.Width()*2/5);
	m_FacingListCtrl.InsertColumn(2, "Extended BOH", LVCFMT_RIGHT, r.Width()*2/5);

	
	return TRUE;  // return TRUE unless you set the focus to a control
	              // EXCEPTION: OCX Property Pages should return FALSE
}

BOOL CBayProfileRulesPage::OnSetActive() 
{
	CBayProfileSheet *pSheet = (CBayProfileSheet *)GetParent();

	m_pBayProfile = pSheet->m_pBayProfile;
	
	int oldSelType;

	if (m_BayTypeListCtrl.GetCount() > 0) {
		int oldSel = m_BayTypeListCtrl.GetCurSel();
		oldSelType = m_BayTypeListCtrl.GetItemData(oldSel);
	}


	m_BayTypeListCtrl.ResetContent();
	for (int i=0; i < m_pBayProfile->m_BayRuleList.GetSize(); ++i) {
		CString bayTypeStr;
		int bayType = m_pBayProfile->m_BayRuleList[i]->m_Baytype;

		CBayProfile::ConvertBayType(bayType, bayTypeStr);
		int nItem = m_BayTypeListCtrl.AddString(bayTypeStr);
		m_BayTypeListCtrl.SetItemData(nItem, i);
		if (oldSelType == bayType)
			m_BayTypeListCtrl.SetCurSel(nItem);
	}
	
	CRect r;
	m_BayTypeListCtrl.GetWindowRect(&r);
	m_BayTypeListCtrl.SetWindowPos(NULL, 0, 0, r.Width(), r.Height()*(m_BayTypeListCtrl.GetCount()+1), SWP_NOMOVE|SWP_NOZORDER);

	if (m_BayTypeListCtrl.GetCurSel() < 0 && m_BayTypeListCtrl.GetCount() > 0) {
		m_BayTypeListCtrl.SetCurSel(0);
		m_PreviousSel = 0;
	}
	else
		m_PreviousSel = m_BayTypeListCtrl.GetCurSel();

	UpdateScreenFromBayRule();
	
	return CPropertyPage::OnSetActive();
}

BOOL CBayProfileRulesPage::OnKillActive() 
{
	if (m_BayTypeListCtrl.GetCurSel() >= 0)
		if (UpdateBayRuleFromScreen(m_BayTypeListCtrl.GetCurSel()) < 0)
			return FALSE;


	return CPropertyPage::OnKillActive();
}

void CBayProfileRulesPage::OnSelchangeBaytypeList() 
{
	if (m_PreviousSel >= 0) {

		if (UpdateBayRuleFromScreen(m_PreviousSel) < 0) {
			m_BayTypeListCtrl.SetCurSel(m_PreviousSel);
			return;
		}
	}

	m_PreviousSel = m_BayTypeListCtrl.GetCurSel();

	UpdateScreenFromBayRule();

}

void CBayProfileRulesPage::OnRecalculate() 
{
	int curSel = m_BayTypeListCtrl.GetCurSel();
	if (curSel < 0)
		return;
	
	int ruleIdx = m_BayTypeListCtrl.GetItemData(curSel);
	
	UpdateBayRuleFromScreen(ruleIdx);

	m_pBayProfile->CalculateExtendedValues(FALSE, ruleIdx);

	UpdateScreenFromBayRule();
}

void CBayProfileRulesPage::OnAddFacing() 
{
	CPrompt dlg;
	CString txt;
	int curSel = m_BayTypeListCtrl.GetCurSel();
	if (curSel < 0)
		return;

	int ruleIdx = m_BayTypeListCtrl.GetItemData(curSel);
	CBayRule *pRule = m_pBayProfile->m_BayRuleList[ruleIdx];

	dlg.m_ParameterValue.Format("%d", pRule->m_FacingInfoList.GetSize());
	dlg.m_ParameterName = "Enter the maximum number of facings for this profile:";
	if (dlg.DoModal() != IDOK)
		return;

	int maxFacingCount = atoi(dlg.m_ParameterValue);

	int cntDiff = m_FacingListCtrl.GetItemCount() - maxFacingCount;
	if (cntDiff > 0) {
		for (int i=maxFacingCount; i < m_FacingListCtrl.GetItemCount(); ++i) {
			int facingIdx = m_FacingListCtrl.GetItemData(i);
			delete pRule->m_FacingInfoList[facingIdx];
		}
		pRule->m_FacingInfoList.SetSize(maxFacingCount);
		UpdateScreenFromBayRule();
	}
	else if (cntDiff < 0) {		// new count is greater than old count
		cntDiff = 0 - cntDiff;
		for (int i=0; i < cntDiff; ++i) {
			CFacingInfo *pFacingInfo = new CFacingInfo;
			int facingIdx = pRule->m_FacingInfoList.Add(pFacingInfo);
			pFacingInfo->m_FacingCount = facingIdx+1;
			pFacingInfo->m_Description.Format("%d", pFacingInfo->m_FacingCount);
			m_pBayProfile->CalculateExtendedValues(FALSE, ruleIdx, facingIdx);			
		}
		UpdateScreenFromBayRule();
	}

}

void CBayProfileRulesPage::OnEdit() 
{
	int curSel = m_BayTypeListCtrl.GetCurSel();
	if (curSel < 0)
		return;

	int idx = m_BayTypeListCtrl.GetItemData(curSel);
	CBayRule *pRule = m_pBayProfile->m_BayRuleList[idx];

	POSITION pos = m_FacingListCtrl.GetFirstSelectedItemPosition();
	curSel = -1;
	if (pos != NULL) {
		curSel = m_FacingListCtrl.GetNextSelectedItem(pos);
	}
	if (curSel < 0) {
		AfxMessageBox("Please select an item from the facing list to edit.");
		return;
	}

	int facingIdx = m_FacingListCtrl.GetItemData(curSel);
	CFacingInfo *pFacingInfo = pRule->m_FacingInfoList[facingIdx];

	CBayProfileEditFacingInfo dlg;
	dlg.m_FacingCount.Format("%d", pFacingInfo->m_FacingCount);
	dlg.m_ExtendedCube.Format("%.4f", pFacingInfo->m_ExtendedCube);
	dlg.m_ExtendedBOH.Format("%.4f", pFacingInfo->m_ExtendedBOH);

	try {
		if (dlg.DoModal() != IDOK)
			return;
	}
	catch (...) {
		AfxMessageBox("Error displaying facing info edit screen.");
		return;
	}

	pFacingInfo->m_ExtendedCube = atof(dlg.m_ExtendedCube);
	pFacingInfo->m_ExtendedBOH = atof(dlg.m_ExtendedBOH);

	CString temp;
	temp.Format("%.4f", pFacingInfo->m_ExtendedCube);
	m_FacingListCtrl.SetItemText(curSel, 1, temp);
	temp.Format("%.4f", pFacingInfo->m_ExtendedBOH);
	m_FacingListCtrl.SetItemText(curSel, 2, temp);


}

int CBayProfileRulesPage::UpdateScreenFromBayRule()
{
	int curSel = m_BayTypeListCtrl.GetCurSel();
	if (curSel < 0)
		return -1;

	int idx = m_BayTypeListCtrl.GetItemData(curSel);
	CBayRule *pRule = m_pBayProfile->m_BayRuleList[idx];

	m_AdditionalRsvCube = pRule->m_AdditionalRsvCube;
	m_DesiredReplenishments = pRule->m_DesiredRplnPerWeek;
	m_PalletHeight = pRule->m_PalletHeight;
	m_PctRsvUtil = pRule->m_PctUtilRsvPos;
	m_PctSelUtil = pRule->m_PctUtilSelPos;
	m_PercentReserves = pRule->m_PercentReserves;

	m_FacingListCtrl.DeleteAllItems();

	for (int i=0; i < pRule->m_FacingInfoList.GetSize(); ++i) {
		CFacingInfo *pFacingInfo = pRule->m_FacingInfoList[i];

		CString temp;
		temp.Format("%0d", pFacingInfo->m_FacingCount);
		int nItem = m_FacingListCtrl.InsertItem(i, temp);
		m_FacingListCtrl.SetItemData(nItem, (unsigned long)i);
		temp.Format("%.4f", pFacingInfo->m_ExtendedCube);
		m_FacingListCtrl.SetItemText(nItem, 1, temp);
		temp.Format("%.4f", pFacingInfo->m_ExtendedBOH);
		m_FacingListCtrl.SetItemText(nItem, 2, temp);
	}

	UpdateData(FALSE);

	return 0;
}

int CBayProfileRulesPage::UpdateBayRuleFromScreen(int idx)
{
	if (! UpdateData(TRUE))
		return -1;
	
	CBayRule *pRule = m_pBayProfile->m_BayRuleList[idx];
	
	pRule->m_AdditionalRsvCube = m_AdditionalRsvCube;
	pRule->m_DesiredRplnPerWeek = m_DesiredReplenishments;
	pRule->m_PalletHeight = m_PalletHeight;
	pRule->m_PctUtilRsvPos = m_PctRsvUtil;
	pRule->m_PctUtilSelPos = m_PctSelUtil;
	pRule->m_PercentReserves = m_PercentReserves;
	
	// Facing info is kept up-to-date as the list control is updated

	return 0;
}

BOOL CBayProfileRulesPage::OnHelpInfo(HELPINFO* pHelpInfo) 
{
	helpService.ShowFieldHelp(IDD, pHelpInfo->iCtrlId);
	return FALSE;

}

BOOL CBayProfileRulesPage::OnNotify(WPARAM wParam, LPARAM lParam, LRESULT* pResult) 
{
	NMHDR* pNMHDR = (NMHDR*)lParam;

	if (pNMHDR->code == PSN_HELP) {
		helpService.ShowScreenHelp(IDD);
		return TRUE;
	}
	
	return CPropertyPage::OnNotify(wParam, lParam, pResult);
}
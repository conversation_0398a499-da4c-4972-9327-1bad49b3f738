#if !defined(AFX_LEVELLOCATIONBUTTON_H__7F5897BD_E1A2_4778_8920_BEE163F35151__INCLUDED_)
#define AFX_LEVELLOCATIONBUTTON_H__7F5897BD_E1A2_4778_8920_BEE163F35151__INCLUDED_

#if _MSC_VER > 1000
#pragma once
#endif // _MSC_VER > 1000
// LevelLocationButton.h : header file
//
#include "BayInfo.h"

/////////////////////////////////////////////////////////////////////////////
// CLevelLocationButton window
#define WM_SELECT_LEVEL WM_USER+1
#define	WM_ADD_LEVEL WM_USER+2
#define WM_DBLCLK_LEVEL WM_USER+3

class CLevelLocationButton : public CButton
{
// Construction
public:
	CLevelLocationButton();

// Attributes
public:
	CBayInfo m_BayInfo;


// Operations
public:
// Overrides
	// ClassWizard generated virtual function overrides
	//{{AFX_VIRTUAL(CLevelLocationButton)
	public:
	virtual void DrawItem(LPDRAWITEMSTRUCT lpDrawItemStruct);
	//}}AFX_VIRTUAL

// Implementation
public:
	void SelectLocation(int levelIdx, int locIdx);
	void SelectLevel(int levelIdx);
	virtual ~CLevelLocationButton();

	// Generated message map functions
protected:
	//{{AFX_MSG(CLevelLocationButton)
	afx_msg void OnLButtonDown(UINT nFlags, CPoint point);
	afx_msg void OnLButtonDblClk(UINT nFlags, CPoint point);
	afx_msg void OnMouseMove(UINT nFlags, CPoint point);
	//}}AFX_MSG

	DECLARE_MESSAGE_MAP()

private:
	int m_LogicalBayWidth;
	void SetMapping(CDC &cdc);
	void DrawDashedHorzLine(CDC &cdc, const CPoint& startPt, const CPoint& endPt);
	void DrawBox(CDC &cdc, const CRect& r, int width, BOOL bDashed);
	void DrawBoxWithText(CDC &cdc, const CRect& r, int width, BOOL bDashed,
												const CString &text, int textSize);
	void DrawHorzDimLine(CDC &cdc, const CPoint &startPt, int len, int width, const CString &text,
											   int textSize, int direction);
};

/////////////////////////////////////////////////////////////////////////////

//{{AFX_INSERT_LOCATION}}
// Microsoft Visual C++ will insert additional declarations immediately before the previous line.

#endif // !defined(AFX_LEVELLOCATIONBUTTON_H__7F5897BD_E1A2_4778_8920_BEE163F35151__INCLUDED_)

// SearchAnchorDataService.cpp: implementation of the CSearchAnchorDataService class.
//
//////////////////////////////////////////////////////////////////////

#include "stdafx.h"
#include "modal.h"
#include "DataAccessService.h"
#include "SearchAnchorDataService.h"
#include "TreeElement.h"
#include "SearchAnchor.h"

#ifdef _DEBUG
#undef THIS_FILE
static char THIS_FILE[]=__FILE__;
#define new DEBUG_NEW
#endif

//////////////////////////////////////////////////////////////////////
// Construction/Destruction
//////////////////////////////////////////////////////////////////////

extern TreeElement changesTree;
extern CDataAccessService dataAccessService;

CSearchAnchorDataService::CSearchAnchorDataService()
{

}

CSearchAnchorDataService::~CSearchAnchorDataService()
{

}


int CSearchAnchorDataService::GetSearchAnchorPoints(CStringArray &searchAnchorList)
{
	CString tempString;
	CSsaStringArray tempSendArray;
	CSsaStringArray tempRecvArray;
	CString sendString;
	int i = 0;
	CString queryText;

	/////////////////////////////////////////////////////////////
	// build the data to send to the Forte connection server
	/////////////////////////////////////////////////////////////

	queryText.Format("select StartingLocation, EndingLocation, SearchAnchorPoint, DBSearchAnchorID "
		"from DBSearchAnchor "
		"where DBFacilityID = %d "
		"order by StartingLocation, EndingLocation", changesTree.elementDBID);
			

	return dataAccessService.ExecuteQuery("GetSearchAnchorPoints", queryText, searchAnchorList);

}


int CSearchAnchorDataService::UpdateSearchAnchorPoints(CObArray &searchAnchorList)
{
	CString tempString;
	CSsaStringArray tempSendArray;
	CSsaStringArray tempRecvArray;
	CString sendString;
	int i = 0;
	CString cmdText;
	CSearchAnchor *pSearchAnchor;
	int nextKey;

	/////////////////////////////////////////////////////////////
	// build the data to send to the Forte connection server
	/////////////////////////////////////////////////////////////

	cmdText.Format("Delete from DBSearchAnchor where DBFacilityid = %d", changesTree.elementDBID);
	if (dataAccessService.ExecuteStatement("DeleteSearchAnchorPoints", cmdText) < 0)
		return -1;			

	nextKey = dataAccessService.GetNextKey("DBSearchAnchor", searchAnchorList.GetSize());

	for (i=0; i < searchAnchorList.GetSize(); ++i) {
		pSearchAnchor = (CSearchAnchor *)searchAnchorList[i];
	
		cmdText.Format("insert into DBSearchAnchor "
			"(DBSearchAnchorID, StartingLocation, EndingLocation, SearchAnchorPoint, "
			"CreateDate, ChangeDate, LastUserID, DBFacilityID) "
			"values "
			"(%d, '%s', '%s', '%s', sysdate, sysdate, 1, %d) ",
			nextKey, pSearchAnchor->m_StartingLocation, pSearchAnchor->m_EndingLocation,
			pSearchAnchor->m_SearchAnchorPoint, changesTree.elementDBID);
		
		if (dataAccessService.ExecuteStatement("InsertSearchAnchor", cmdText) != 0)
			return -1;

		nextKey++;
	}

	return 0;

}


int CSearchAnchorDataService::GetSearchAnchorLocations(CStringArray &locList)
{
	CString sql;
	sql.Format("select pg.dbslottinggroupid, l.description "
		"from dbslottinggroup pg, dbslotgrpbay sgb, dblocation l, "
		"dblocationprof lop "
		"where pg.dbfacilityid = %d "
		"and pg.dbslottinggroupid = sgb.dbslottinggroupid "
		"and l.dblevelid = sgb.dblevelid "
		"and lop.dblocationprofid = l.dblocationprofid "
		"and ( (lop.isselect = 1 and l.isoverridden = 0) or "
		" (l.isselect = 1 and l.isoverridden = 1) ) "
		"union "
		"select 0, l.description "
		"from dbsection s, dbaisle a, dbside si, dbbay b, dblevel le, dblocation l, "
		"dblocationprof lop "
		"where s.dbfacilityid = %d "
		"and s.dbsectionid = a.dbsectionid "
		"and a.dbaisleid = si.dbaisleid "
		"and si.dbsideid = b.dbsideid "
		"and b.dbbayid = le.dbbayid "
		"and le.dblevelid = l.dblevelid "
		"and lop.dblocationprofid = l.dblocationprofid "
		"and ( (lop.isselect = 1 and l.isoverridden = 0) or "
		" (l.isselect = 1 and l.isoverridden = 1) ) "
		"and not exists ( select dblevelid from dbslotgrpbay "
		"where dbslotgrpbay.dblevelid = le.dblevelid) "
		"order by 2, 1", changesTree.elementDBID, changesTree.elementDBID);
	return dataAccessService.ExecuteQuery("GetSAPLocations", sql, locList);
}

	
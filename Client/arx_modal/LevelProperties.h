#if !defined(AFX_LEVELPROPERTIES_H__67698861_AA29_11D4_9212_00400542E36B__INCLUDED_)
#define AFX_LEVELPROPERTIES_H__67698861_AA29_11D4_9212_00400542E36B__INCLUDED_

#if _MSC_VER > 1000
#pragma once
#endif // _MSC_VER > 1000
// LevelProperties.h : header file
//
#include "Resource.h"

/////////////////////////////////////////////////////////////////////////////
// CLevelProperties dialog

class CLevelProperties : public CPropertyPage
{
	DECLARE_DYNCREATE(CLevelProperties)

// Construction
public:
	CLevelProperties();
	~CLevelProperties();

// Dialog Data
	//{{AFX_DATA(CLevelProperties)
	enum { IDD = IDD_LEVEL_PROPERTIES };
	CString	m_Coordinates;
	CString	m_Description;
	float	m_FacingGap;
	float	m_FacingSnap;
	float	m_ForkFixedInsertion;
	CString	m_LevelType;
	float	m_MinLocWidth;
	float	m_ProductGap;
	int		m_RelativeLevel;
	BOOL	m_RotateAllowed;
	BOOL	m_VariableWidth;
	float	m_ProductSnap;
	//}}AFX_DATA


// Overrides
	// ClassWizard generate virtual function overrides
	//{{AFX_VIRTUAL(CLevelProperties)
	protected:
	virtual void DoDataExchange(CDataExchange* pDX);    // DDX/DDV support
	virtual BOOL OnNotify(WPARAM wParam, LPARAM lParam, LRESULT* pResult);
	//}}AFX_VIRTUAL

// Implementation
protected:
	// Generated message map functions
	//{{AFX_MSG(CLevelProperties)
	afx_msg void OnVariableWidth();
	virtual BOOL OnInitDialog();
	afx_msg BOOL OnHelpInfo(HELPINFO* pHelpInfo);
	//}}AFX_MSG
	DECLARE_MESSAGE_MAP()

private:
	void ChangeVarWidthState(BOOL bEnabled);
};

//{{AFX_INSERT_LOCATION}}
// Microsoft Visual C++ will insert additional declarations immediately before the previous line.

#endif // !defined(AFX_LEVELPROPERTIES_H__67698861_AA29_11D4_9212_00400542E36B__INCLUDED_)

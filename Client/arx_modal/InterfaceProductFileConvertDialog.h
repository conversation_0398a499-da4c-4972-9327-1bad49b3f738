#if !defined(AFX_INTERFACEPRODUCTFILECONVERTDIALOG_H__E66DDDCB_B2D1_4AD3_921D_1464EC16BAFB__INCLUDED_)
#define AFX_INTERFACEPRODUCTFILECONVERTDIALOG_H__E66DDDCB_B2D1_4AD3_921D_1464EC16BAFB__INCLUDED_

#if _MSC_VER > 1000
#pragma once
#endif // _MSC_VER > 1000
// InterfaceProductFileConvertDialog.h : header file
//

/////////////////////////////////////////////////////////////////////////////
// CInterfaceProductFileConvertDialog dialog

class CInterfaceProductFileConvertDialog : public CDialog
{
// Construction
public:
	CInterfaceProductFileConvertDialog(CWnd* pParent = NULL);   // standard constructor

// Dialog Data
	//{{AFX_DATA(CInterfaceProductFileConvertDialog)
	enum { IDD = IDD_CONVERT_PRODUCT_TO_XML };
	CComboBox	m_TypeListCtrl;
	CString	m_FileName;
	BOOL	m_ValidateUDFs;
	BOOL	m_OldFormat;
	BOOL	m_CreateAssignments;
	//}}AFX_DATA


// Overrides
	// ClassWizard generated virtual function overrides
	//{{AFX_VIRTUAL(CInterfaceProductFileConvertDialog)
	protected:
	virtual void DoDataExchange(CDataExchange* pDX);    // DDX/DDV support
	//}}AFX_VIRTUAL

// Implementation
protected:

	// Generated message map functions
	//{{AFX_MSG(CInterfaceProductFileConvertDialog)
	afx_msg void OnBrowse();
	afx_msg void OnConvert();
	afx_msg BOOL OnHelpInfo(HELPINFO* pHelpInfo);
	afx_msg void OnHelp();
	afx_msg void OnSelchangeTypeList();
	virtual BOOL OnInitDialog();
	//}}AFX_MSG
	DECLARE_MESSAGE_MAP()

private:
	CString ConvertValue(const CString &field, const CString &oldValue);
	CString Upper(const CString &str);
	CMapStringToString m_Map;
	CString GetFile(BOOL inbound, BOOL product);
	void LoadNewFormatMap();
	CStringArray m_DetailList;
	int ConvertHeader(CStringArray &headers);
	
	void LoadOldFormatMap();
	CString LastError();
	int LoadFile(CStringArray &lines);
	int SaveFile(HANDLE &hFile, const CString& fileName, const CString &data);
};

//{{AFX_INSERT_LOCATION}}
// Microsoft Visual C++ will insert additional declarations immediately before the previous line.

#endif // !defined(AFX_INTERFACEPRODUCTFILECONVERTDIALOG_H__E66DDDCB_B2D1_4AD3_921D_1464EC16BAFB__INCLUDED_)

#if !defined(AFX_BAYPROFILEEDITFACINGINFO_H__12787DA9_D951_4D9B_965A_4F50B164D258__INCLUDED_)
#define AFX_BAYPROFILEEDITFACINGINFO_H__12787DA9_D951_4D9B_965A_4F50B164D258__INCLUDED_

#if _MSC_VER > 1000
#pragma once
#endif // _MSC_VER > 1000
// BayProfileEditFacingInfo.h : header file
//

/////////////////////////////////////////////////////////////////////////////
// CBayProfileEditFacingInfo dialog

class CBayProfileEditFacingInfo : public CDialog
{
// Construction
public:
	CBayProfileEditFacingInfo(CWnd* pParent = NULL);   // standard constructor

// Dialog Data
	//{{AFX_DATA(CBayProfileEditFacingInfo)
	enum { IDD = IDD_BAY_PROFILE_EDIT_FACING };
	CString	m_FacingCount;
	CString	m_ExtendedBOH;
	CString	m_ExtendedCube;
	//}}AFX_DATA


// Overrides
	// ClassWizard generated virtual function overrides
	//{{AFX_VIRTUAL(CBayProfileEditFacingInfo)
	protected:
	virtual void DoDataExchange(CDataExchange* pDX);    // DDX/DDV support
	//}}AFX_VIRTUAL

// Implementation
protected:

	// Generated message map functions
	//{{AFX_MSG(CBayProfileEditFacingInfo)
	virtual BOOL OnInitDialog();
	virtual void OnOK();
	afx_msg void OnHelp();
	afx_msg BOOL OnHelpInfo(HELPINFO* pHelpInfo);
	//}}AFX_MSG
	DECLARE_MESSAGE_MAP()
};

//{{AFX_INSERT_LOCATION}}
// Microsoft Visual C++ will insert additional declarations immediately before the previous line.

#endif // !defined(AFX_BAYPROFILEEDITFACINGINFO_H__12787DA9_D951_4D9B_965A_4F50B164D258__INCLUDED_)

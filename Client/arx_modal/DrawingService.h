// DrawingService.h: interface for the CDrawingService class.
//
//////////////////////////////////////////////////////////////////////

#if !defined(AFX_DRAWINGSERVICE_H__8970EE26_4957_4CB0_A124_019924FE0A31__INCLUDED_)
#define AFX_DRAWINGSERVICE_H__8970EE26_4957_4CB0_A124_019924FE0A31__INCLUDED_

#if _MSC_VER > 1000
#pragma once
#endif // _MSC_VER > 1000

class CDrawingService  
{
public:
	CDrawingService();
	virtual ~CDrawingService();
};

#endif // !defined(AFX_DRAWINGSERVICE_H__8970EE26_4957_4CB0_A124_019924FE0A31__INCLUDED_)

#if !defined(AFX_COLORMODELRESULTS_H__379DABB2_6831_11D2_9C55_0080C742D9DF__INCLUDED_)
#define AFX_COLORMODELRESULTS_H__379DABB2_6831_11D2_9C55_0080C742D9DF__INCLUDED_
#include "resource.h"
#if _MSC_VER >= 1000
#pragma once
#endif // _MSC_VER >= 1000
// ColorModelResults.h : header file
//

/////////////////////////////////////////////////////////////////////////////
// CColorModelResults dialog

class CColorModelResults : public CDialog
{
// Construction
public:
	CColorModelResults(CWnd* pParent = NULL);   // standard constructor
	CMap<int, int&, float, float&> minResultsMap;
	CMap<int, int&, float, float&> maxResultsMap;

// Dialog Data
	//{{AFX_DATA(CColorModelResults)
	enum { IDD = IDD_COLORMODELRESULTS };
	CListBox	m_ctlResultsList;
	//}}AFX_DATA
	

// Overrides
	// ClassWizard generated virtual function overrides
	//{{AFX_VIRTUAL(CColorModelResults)
	protected:
	virtual void DoDataExchange(CDataExchange* pDX);    // DDX/DDV support
	virtual void PostNcDestroy();
	//}}AFX_VIRTUAL

// Implementation
protected:

	// Generated message map functions
	//{{AFX_MSG(CColorModelResults)
	virtual BOOL OnInitDialog();
	afx_msg void OnHelp();
	//}}AFX_MSG
	DECLARE_MESSAGE_MAP()

};

//{{AFX_INSERT_LOCATION}}
// Microsoft Developer Studio will insert additional declarations immediately before the previous line.

#endif // !defined(AFX_COLORMODELRESULTS_H__379DABB2_6831_11D2_9C55_0080C742D9DF__INCLUDED_)

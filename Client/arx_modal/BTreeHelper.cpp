// BTreeHelper.cpp: implementation of the CBTreeHelper class.
//
//////////////////////////////////////////////////////////////////////

#include "stdafx.h"
#include "modal.h"
#include "BTreeHelper.h"
#include "qqhclasses.h"
#include "ssaBtree.h"
#include "UtilityHelper.h"
#include "ForteService.h"

#include "base.h"
#include "MiscClasses.h"
#include "SLOTSessionMgr.h"
#include "SrvcObjs.h"

#ifdef _DEBUG
#undef THIS_FILE
static char THIS_FILE[]=__FILE__;
#define new DEBUG_NEW
#endif

extern int BayNum;
extern int numItemsProcessed;
extern int storeNextNumber;


extern CUtilityHelper utilityHelper;
extern CForteService forteService;

//////////////////////////////////////////////////////////////////////
// Construction/Destruction
//////////////////////////////////////////////////////////////////////

CBTreeHelper::CBTreeHelper()
{

}

CBTreeHelper::~CBTreeHelper()
{

}



//////////////////////////////////////////////////////////////////////
// Function Name : AddSectionToFacility
// Classname : None
// Description : Add a section to the facility structure
// Date Created : 10/01/98
// Author : wbh
//////////////////////////////////////////////////////////////////////
// Inputs : facility structure, a new section
// Outputs : the new facility structure, success code
// Explanation : 
//   Add a new section to the btree file structure and update Forte
//   session MGR.
//////////////////////////////////////////////////////////////////////
// Algorithm : None
//////////////////////////////////////////////////////////////////////
// Revisions : 
//   Date-initials : Description_of_revision
//////////////////////////////////////////////////////////////////////
	/*******************************************************
	*
	*                       NOTICE
	*
	*  THIS SOFTWARE IS THE PROPERTY OF AND CONTAINS
	*  CONFIDENTIAL INFORMATION OF SSA GLOBAL 
	*  TECHNOLOGIES, INC., AND SHALL NOT BE COPIED,
	*  USED, NOR DISCLOSED WITHOUT EXPRESS WRITTEN
	*  AUTHORIZATION.  ALTHOUGH PUBLICATION IS NOT
	*  INTENDED, IN THE EVENT OF PUBLICATION, THE
	*  FOLLOWING NOTICE IS APPLICABLE:
	*
	*  (c) COPYRIGHT 2004 SSA GLOBAL TECHNOLOGIES, INC.
	*
	*           SSA GLOBAL TECHNOLOGIES, INC.
	*
	********************************************************/
//////////////////////////////////////////////////////////////////////
int CBTreeHelper::AddSectionToFacility( qqhSLOTSection & theSection, TreeElement & changesTree ) {
	CString tempString;
	int h,i,j,k,l,m;
	int tempLength;
	CString sendString;
	CSsaStringArray tempSendArray;
	CSsaStringArray tempRecvArray;
	CSsaStringArray tempTreeArray;

	int parentSecNum,parentAisleNum,parentSideNum,parentBayNum,parentLevelNum;
	int startNum;
	CSsaStringArray tempArray;
	CString forteRoot = getenv("TEMP");
	CString junk;

	forteRoot += "\\tempfile.btd";
	int openRet = open_datastore(forteRoot.GetBuffer(0));
	forteRoot.ReleaseBuffer();
	if (openRet != 0)
		return -1;

	theSection.StreamAttributes(tempArray);

	tempString = "<PID>1\n";
	for(h = 0; h < tempArray.GetSize(); h++) {
	//	junk = tempArray[h];
		tempString += tempArray[h];
	}

	//////////////////////////////////////////////////////////////////////
	// Update the datastore with the section information, including
	// aisles, sides, etc. if necessary
	//////////////////////////////////////////////////////////////////////
	startNum = storeNextNumber;
	tempLength = tempString.GetLength();
	if ( storeObjStream(storeNextNumber, tempString.GetBuffer(0),tempLength) != 0) return -1;
	tempString.ReleaseBuffer();
	parentSecNum = storeNextNumber;
	storeNextNumber++;
	tempString = "SLOTSection";
	tempTreeArray.Add(tempString);

	for ( i = 0; i < theSection.getChildList().GetSize(); i++) {
		tempString.Format("<PID>%d\n",parentSecNum);
		tempArray.RemoveAll();
		theSection.getChildList()[i].StreamAttributes(tempArray);
		for ( h = 0; h < tempArray.GetSize(); h++ )
			tempString += tempArray[h];
		tempLength = tempString.GetLength();
		if ( storeObjStream(storeNextNumber, tempString.GetBuffer(0),tempLength) != 0) return -1;
		tempString.ReleaseBuffer();
		parentAisleNum = storeNextNumber;
		storeNextNumber++;
		tempString = "SLOTAisle";
		tempTreeArray.Add(tempString);
		for ( j = 0; j < theSection.getChildList()[i].getChildList().GetSize(); j++) {
			tempString.Format("<PID>%d\n",parentAisleNum);
			tempArray.RemoveAll();
			theSection.getChildList()[i].getChildList()[j].StreamAttributes(tempArray);
			for ( h = 0; h < tempArray.GetSize(); h++ )
				tempString += tempArray[h];
			tempLength = tempString.GetLength();
			if ( storeObjStream(storeNextNumber, tempString.GetBuffer(0),tempLength) != 0) return -1;
			tempString.ReleaseBuffer();
			parentSideNum = storeNextNumber;
			storeNextNumber++;
			tempString = "SLOTSide";
			tempTreeArray.Add(tempString);
			for ( k = 0; k < theSection.getChildList()[i].getChildList()[j].getChildList().GetSize(); k++) {
				tempString.Format("<PID>%d\n",parentSideNum);
				tempArray.RemoveAll();
				theSection.getChildList()[i].getChildList()[j].getChildList()[k].StreamAttributes(tempArray);
				for ( h = 0; h < tempArray.GetSize(); h++ )
					tempString += tempArray[h];
				tempLength = tempString.GetLength();
				if ( storeObjStream(storeNextNumber, tempString.GetBuffer(0),tempLength) != 0) return -1;
				tempString.ReleaseBuffer();
				parentBayNum=storeNextNumber;
				storeNextNumber++;
				tempString = "SLOTBay";
				tempTreeArray.Add(tempString);
				for ( l = 0; l < theSection.getChildList()[i].getChildList()[j].getChildList()[k].getChildList().GetSize(); l++) {
					tempString.Format("<PID>%d\n",parentBayNum);
					tempArray.RemoveAll();
					theSection.getChildList()[i].getChildList()[j].getChildList()[k].getChildList()[l].StreamAttributes(tempArray);
					for ( h = 0; h < tempArray.GetSize(); h++ )
						tempString += tempArray[h];
					tempLength = tempString.GetLength();
					if ( storeObjStream(storeNextNumber, tempString.GetBuffer(0),tempLength) != 0) return -1;
					tempString.ReleaseBuffer();
					parentLevelNum = storeNextNumber;
					storeNextNumber++;
					tempString = "SLOTLevel";
					tempTreeArray.Add(tempString);
					for ( m = 0; m < theSection.getChildList()[i].getChildList()[j].getChildList()[k].getChildList()[l].getChildList().GetSize(); m++) {
						tempString.Format("<PID>%d\n",parentLevelNum);
						tempArray.RemoveAll();
						theSection.getChildList()[i].getChildList()[j].getChildList()[k].getChildList()[l].getChildList()[m].StreamAttributes(tempArray);
						for ( h = 0; h < tempArray.GetSize(); h++ )
							tempString += tempArray[h];
						tempLength = tempString.GetLength();
						if ( storeObjStream(storeNextNumber, tempString.GetBuffer(0),tempLength) != 0) return -1;
						tempString.ReleaseBuffer();
						storeNextNumber++;
						tempString = "SLOTLocation";
						tempTreeArray.Add(tempString);
					}
				}
			}
		}
	}

	finalize_datastore();

	//////////////////////////////////////////////////////////////////////
	// Notify the Forte session MGR across the socket of the addition
	//////////////////////////////////////////////////////////////////////
#if 0
	int tempInt;

	sendString = "<SIO>SLOTSocketString\n";
	tempSendArray.Add(sendString);
	sendString.Format("<SAI>%d\n",startNum);
	tempSendArray.Add(sendString);
	sendString = "<SAI>1\n";
	tempSendArray.Add(sendString);
	for (i = 0; i < tempTreeArray.GetSize(); i++) {
		sendString = "<SAI>" + tempTreeArray[i] + "\n";
		tempSendArray.Add(sendString);
	}
	sendString = "<EIO>SLOTSocketString\n";
	tempSendArray.Add(sendString);

	forteService.SendToForteConnection(tempSendArray,tempRecvArray,
		CString("SLOTSocketString"), 3020);

	for (i=0; i < tempRecvArray.GetSize(); i++) {
		if ( tempRecvArray[i].Find("<SAI>") != -1 ) {
			tempString = tempRecvArray[i].Mid(5);
			tempInt = tempString.GetLength();
			if (tempString.GetAt(tempInt-1) == '\n' ) {
				tempString.SetAt(tempInt-1,' ');
				tempString.TrimRight();
			}
		}
	}

	tempArray.RemoveAll();
#else
	CListstringPtr tempTreeArray1(new CListstring);
	for (int i=0;i < tempTreeArray.GetCount();i++)
	{
		CStringArray temp;
		string tmpStr = (LPCTSTR) tempTreeArray[i];
		tempTreeArray1->AddTail( tmpStr );
	}

	///	OLD CODE: SessionMgrSO.AddBranchToTreeHelper(startNum, tempTreeArray1, atoi((LPCTSTR)tempTreeArray[2]));
	
	if (getSessionMgrSO()->AddBranchToTreeHelper(startNum, tempTreeArray1, 1) == false)
		MessageBox(0,"AddBranchToTreeHelper","",0);
#endif
	//////////////////////////////////////////////////////////////////////
	// Update the facility tree structure
	//////////////////////////////////////////////////////////////////////

	if ( this->BuildChangesTree(changesTree,startNum) == 0 )
		return 0;
	else
		return -1;
}

//////////////////////////////////////////////////////////////////////
// Function Name : AddAisleToFacility
// Classname : None
// Description : Add an aisle to the facility structure
// Date Created : 10/01/98
// Author : wbh
//////////////////////////////////////////////////////////////////////
// Inputs : facility structure, section offset, new ailse
// Outputs : the new facility structure, success code
// Explanation : 
//   Add a new aisle to the btree file structure and update Forte
//   session MGR.
//////////////////////////////////////////////////////////////////////
// Algorithm : None
//////////////////////////////////////////////////////////////////////
// Revisions : 
//   Date-initials : Description_of_revision
//////////////////////////////////////////////////////////////////////
	/*******************************************************
	*
	*                       NOTICE
	*
	*  THIS SOFTWARE IS THE PROPERTY OF AND CONTAINS
	*  CONFIDENTIAL INFORMATION OF SSA GLOBAL 
	*  TECHNOLOGIES, INC., AND SHALL NOT BE COPIED,
	*  USED, NOR DISCLOSED WITHOUT EXPRESS WRITTEN
	*  AUTHORIZATION.  ALTHOUGH PUBLICATION IS NOT
	*  INTENDED, IN THE EVENT OF PUBLICATION, THE
	*  FOLLOWING NOTICE IS APPLICABLE:
	*
	*  (c) COPYRIGHT 2004 SSA GLOBAL TECHNOLOGIES, INC.
	*
	*           SSA GLOBAL TECHNOLOGIES, INC.
	*
	********************************************************/
//////////////////////////////////////////////////////////////////////
int CBTreeHelper::AddAisleToFacility( qqhSLOTAisle & theAisle, const int parentSectionOffset, TreeElement & changesTree ) {
	CSsaStringArray tempArray;
	CString tempString,tempTreeString;
	int h,i,j,k,l;
	int tempLength;
	CString sendString;
	CSsaStringArray tempSendArray;
	CSsaStringArray tempRecvArray;
	CSsaStringArray tempTreeArray;
//	int tempInt;
	int startNum;
	int parentAisleNum,parentSideNum,parentBayNum,parentLevelNum;

	//////////////////////////////////////////////////////////////////////
	// Update the datastore with the section information, including
	// sides, bays, etc. if necessary
	//////////////////////////////////////////////////////////////////////
	CString forteRoot = getenv("TEMP");
	forteRoot += "\\tempfile.btd";
	int openRet = open_datastore(forteRoot.GetBuffer(0));
	forteRoot.ReleaseBuffer();
	if (openRet != 0)
		return -1;
	tempString.Format("<PID>%d\n",parentSectionOffset);
	theAisle.StreamAttributes(tempArray);
	for(h = 0; h < tempArray.GetSize(); h++)
		tempString += tempArray[h];
	
	startNum = storeNextNumber;
	tempLength = tempString.GetLength();
	if ( storeObjStream(storeNextNumber, tempString.GetBuffer(0),tempLength) != 0) return -1;
	tempString.ReleaseBuffer();
	parentAisleNum = storeNextNumber;
	storeNextNumber++;
	tempTreeString = "SLOTAisle";
	tempTreeArray.Add(tempTreeString);

	for ( i = 0; i < theAisle.getChildList().GetSize(); i++) {
		tempString.Format("<PID>%d\n",parentAisleNum);
		tempArray.RemoveAll();
		theAisle.getChildList()[i].StreamAttributes(tempArray);
		for ( h = 0; h < tempArray.GetSize(); h++ )
			tempString += tempArray[h];
		tempLength = tempString.GetLength();
		if ( storeObjStream(storeNextNumber, tempString.GetBuffer(0),tempLength) != 0) return -1;
		tempString.ReleaseBuffer();
		parentSideNum = storeNextNumber;
		storeNextNumber++;
		tempTreeString = "SLOTSide";
		tempTreeArray.Add(tempTreeString);
		for ( j = 0; j < theAisle.getChildList()[i].getChildList().GetSize(); j++) {
			tempString.Format("<PID>%d\n",parentSideNum);
			tempArray.RemoveAll();
			theAisle.getChildList()[i].getChildList()[j].StreamAttributes(tempArray);
			for ( h = 0; h < tempArray.GetSize(); h++ )
				tempString += tempArray[h];
			tempLength = tempString.GetLength();
			if ( storeObjStream(storeNextNumber, tempString.GetBuffer(0),tempLength) != 0) return -1;
			tempString.ReleaseBuffer();
			parentBayNum = storeNextNumber;
			storeNextNumber++;
			tempTreeString = "SLOTBay";
			tempTreeArray.Add(tempTreeString);
			for ( k = 0; k < theAisle.getChildList()[i].getChildList()[j].getChildList().GetSize(); k++) {
				tempString.Format("<PID>%d\n",parentBayNum);
				tempArray.RemoveAll();
				theAisle.getChildList()[i].getChildList()[j].getChildList()[k].StreamAttributes(tempArray);
				for ( h = 0; h < tempArray.GetSize(); h++ )
					tempString += tempArray[h];
				tempLength = tempString.GetLength();
				if ( storeObjStream(storeNextNumber, tempString.GetBuffer(0),tempLength) != 0) return -1;
				tempString.ReleaseBuffer();
				parentLevelNum=storeNextNumber;
				storeNextNumber++;
				tempTreeString = "SLOTLevel";
				tempTreeArray.Add(tempTreeString);
				for ( l = 0; l < theAisle.getChildList()[i].getChildList()[j].getChildList()[k].getChildList().GetSize(); l++) {
					tempString.Format("<PID>%d\n",parentLevelNum);
					tempArray.RemoveAll();
					theAisle.getChildList()[i].getChildList()[j].getChildList()[k].getChildList()[l].StreamAttributes(tempArray);
					for ( h = 0; h < tempArray.GetSize(); h++ )
						tempString += tempArray[h];
					tempLength = tempString.GetLength();
					//AfxMessageBox(tempString);
					if ( storeObjStream(storeNextNumber, tempString.GetBuffer(0),tempLength) != 0) return -1;
					tempString.ReleaseBuffer();
					storeNextNumber++;
					tempTreeString = "SLOTLocation";
					tempTreeArray.Add(tempTreeString);
				}
			}
		}
	}
	
	finalize_datastore();
#if 0
	//////////////////////////////////////////////////////////////////////
	// Notify the Forte session MGR across the socket of the addition
	//////////////////////////////////////////////////////////////////////
	sendString = "<SIO>SLOTSocketString\n";
	tempSendArray.Add(sendString);
	sendString.Format("<SAI>%d\n",startNum);
	tempSendArray.Add(sendString);
	sendString.Format("<SAI>%d\n",parentSectionOffset);
	tempSendArray.Add(sendString);
	for (i = 0; i < tempTreeArray.GetSize(); i++) {
		sendString = "<SAI>" + tempTreeArray[i] + "\n";
		tempSendArray.Add(sendString);
	}
	sendString = "<EIO>SLOTSocketString\n";
	tempSendArray.Add(sendString);

	/* brd - temporary for testing
	forteService.SendToForteConnection(tempSendArray,tempRecvArray,
		CString("SLOTSocketString"), 3020);

	for (i=0; i < tempRecvArray.GetSize(); i++) {
		if ( tempRecvArray[i].Find("<SAI>") != -1 ) {
			tempString = tempRecvArray[i].Mid(5);
			tempInt = tempString.GetLength();
			if (tempString.GetAt(tempInt-1) == '\n' ) {
				tempString.SetAt(tempInt-1,' ');
				tempString.TrimRight();
			}
		}
	}
	*/
#else
	/*CListstringPtr tempTreeArray1(new CListstring);
	for (int i=0;i < tempTreeArray.GetCount();i++)
	{
		CStringArray temp;
		string tmpStr = (LPCTSTR) tempTreeArray[i];
		tempTreeArray1->AddTail( tmpStr );
	}

	///	OLD CODE: SessionMgrSO.AddBranchToTreeHelper(startNum, tempTreeArray1, atoi((LPCTSTR)tempTreeArray[2]));
	
	if (getSessionMgrSO()->AddBranchToTreeHelper(startNum, tempTreeArray1, parentSectionOffset) == false)
		MessageBox(0,"AddBranchToTreeHelper","",0);*/
#endif
	//////////////////////////////////////////////////////////////////////
	// Update the facility tree structure
	//////////////////////////////////////////////////////////////////////
	if ( this->BuildChangesTree(changesTree,startNum) == 0 )
		return 0;
	else
		return -1;
}


int CBTreeHelper::DeleteFacilityBranch( CString elementName, int branchOffset ) 
{

#if 0
	CString tempString;
	int i;
	CString sendString;
	CSsaStringArray tempSendArray;
	CSsaStringArray tempRecvArray;
	int tempInt;

	sendString = "<SIO>SLOTSocketString\n";
	tempSendArray.Add(sendString);
	sendString = "<SAI>" + elementName + "\n";
	tempSendArray.Add(sendString);
	sendString.Format("<SAI>%d\n",branchOffset);
	tempSendArray.Add(sendString);
	sendString = "<EIO>SLOTSocketString\n";
	tempSendArray.Add(sendString);

	forteService.SendToForteConnection(tempSendArray,tempRecvArray,
		CString("SLOTSocketString"), 3030);

	for (i=0; i < tempRecvArray.GetSize(); i++) {
		if ( tempRecvArray[i].Find("<SAI>") != -1 ) {
			tempString = tempRecvArray[i].Mid(5);
			tempInt = tempString.GetLength();
			if (tempString.GetAt(tempInt-1) == '\n' ) {
				tempString.SetAt(tempInt-1,' ');
				tempString.TrimRight();
			}
		}
	}
	if ( tempString == "Success" )
		return 0;
	else
		return -1;

#endif
	string elementTmp = (LPCTSTR)elementName;
	return getSessionMgrSO()->DeleteBranchFromTree(elementTmp,branchOffset);

}

//////////////////////////////////////////////////////////////////////
// Function Name : NewFacility
// Classname : None
// Description : Initialize the facility structure
// Date Created : 10/01/98
// Author : wbh
//////////////////////////////////////////////////////////////////////
// Inputs : facility structure, WMS type, WMS units (english, metic)
// Outputs : the new facility structure, success code
// Explanation : 
//   Initialize the btree 
//////////////////////////////////////////////////////////////////////
// Algorithm : None
//////////////////////////////////////////////////////////////////////
// Revisions : 
//   Date-initials : Description_of_revision
//////////////////////////////////////////////////////////////////////
	/*******************************************************
	*
	*                       NOTICE
	*
	*  THIS SOFTWARE IS THE PROPERTY OF AND CONTAINS
	*  CONFIDENTIAL INFORMATION OF SSA GLOBAL 
	*  TECHNOLOGIES, INC., AND SHALL NOT BE COPIED,
	*  USED, NOR DISCLOSED WITHOUT EXPRESS WRITTEN
	*  AUTHORIZATION.  ALTHOUGH PUBLICATION IS NOT
	*  INTENDED, IN THE EVENT OF PUBLICATION, THE
	*  FOLLOWING NOTICE IS APPLICABLE:
	*
	*  (c) COPYRIGHT 2004 SSA GLOBAL TECHNOLOGIES, INC.
	*
	*           SSA GLOBAL TECHNOLOGIES, INC.
	*
	********************************************************/
//////////////////////////////////////////////////////////////////////
int CBTreeHelper::NewFacilityTree(TreeElement & changesTree, int wmsType, int wmsUnits) {
	CString forteRoot = getenv("TEMP");
	qqhSLOTFacility tempFacility;
	qqhSLOTSection tempSection;
	CSsaStringArray tempArray;
	CString tempString;
	int tempLength;
	int h;
	CString sendString;
	CSsaStringArray tempSendArray;
	CSsaStringArray tempRecvArray;
	int tempRet;
#if 0
	sendString = "<SIO>SLOTSocketString\n";
	tempSendArray.Add(sendString);
	sendString = "<SAI>Initialize Facility\n";
	tempSendArray.Add(sendString);
	sendString = "<EIO>SLOTSocketString\n";
	tempSendArray.Add(sendString);

	forteService.SendToForteConnection(tempSendArray,tempRecvArray,
		CString("SLOTSocketString"), 4050);
#else
	///SessionMgrSO.NewFacility();
	getSessionMgrSO()->NewFacility();
#endif

	storeNextNumber = 1;

	changesTree.clear();


	forteRoot += "\\tempfile.btd";
	init_datastore(forteRoot.GetBuffer(0));
	forteRoot.ReleaseBuffer();
	tempFacility.setDescription(CString("New Facility"));
	tempFacility.setIsModified(1);
	tempFacility.setStrCadFileName(CString("NoCadFile"));
	tempFacility.setRegion(1);
	tempFacility.setUnits(wmsUnits);
	tempFacility.setSlotType(wmsType);
	tempFacility.setIsChanged(CString("TRUE"));
	tempFacility.setDuration(1);
	tempFacility.setTimeHorizonUnit(2);
	tempFacility.setClientNameOpened("NOTOPEN");
	tempFacility.setCost(0);
	tempFacility.setBaselineCost(0);

	tempFacility.StreamAttributes(tempArray);
	tempString = "<PID>0\n";
	for(h = 0; h < tempArray.GetSize(); h++)
		tempString += tempArray[h];
	tempLength = tempString.GetLength();
	tempRet = storeObjStream(storeNextNumber, tempString.GetBuffer(0),tempLength);
	tempString.ReleaseBuffer();

	storeNextNumber++;
	strcpy(changesTree.facilityElement,"SLOTFacility");
	BayNum = 1;
	finalize_datastore();
	numItemsProcessed = 0;
	

	return 0;
}


//////////////////////////////////////////////////////////////////////
// Function Name : UpdateBayDescription
// Classname : None
// Description : Update the bay description in the btree file
// Date Created : 10/01/98
// Author : wbh
//////////////////////////////////////////////////////////////////////
// Inputs : new description, fileoffset
// Outputs : success code
// Explanation : 
// 
//////////////////////////////////////////////////////////////////////
// Algorithm : None
//////////////////////////////////////////////////////////////////////
// Revisions : 
//   Date-initials : Description_of_revision
//////////////////////////////////////////////////////////////////////
	/*******************************************************
	*
	*                       NOTICE
	*
	*  THIS SOFTWARE IS THE PROPERTY OF AND CONTAINS
	*  CONFIDENTIAL INFORMATION OF SSA GLOBAL 
	*  TECHNOLOGIES, INC., AND SHALL NOT BE COPIED,
	*  USED, NOR DISCLOSED WITHOUT EXPRESS WRITTEN
	*  AUTHORIZATION.  ALTHOUGH PUBLICATION IS NOT
	*  INTENDED, IN THE EVENT OF PUBLICATION, THE
	*  FOLLOWING NOTICE IS APPLICABLE:
	*
	*  (c) COPYRIGHT 2004 SSA GLOBAL TECHNOLOGIES, INC.
	*
	*           SSA GLOBAL TECHNOLOGIES, INC.
	*
	********************************************************/
//////////////////////////////////////////////////////////////////////
void CBTreeHelper::UpdateBayDescription(CString desc, int storeNum) {

	qqhSLOTBay tempBay;
	CString forteRoot = getenv("TEMP");
	char charBuf[100];
	int tempReturn = -2;
	int tempLength;
	int aMultiplier = 1;
	char *buf = NULL;
	CString tempString;
	CSsaStringArray tempArray;
	int parentID= 0;
	int h;

	forteRoot += "\\tempfile.btd";
	tempLength = 1024 * aMultiplier;
	buf = (char *) (calloc(tempLength,sizeof(char)));

	/////////////////////////////////////////////////////////////
	//  If we do not prepare enough buffer for the read,
	//  the function will return a -2 code.  We then increase
	//  the size of our buffer.  We assume that we will receive a
	//  -2 at first.
	/////////////////////////////////////////////////////////////
	while ( tempReturn == -2 ) {
		tempLength = aMultiplier * 1024;
		tempReturn = retObjStream(storeNum,buf,&tempLength);
		if ( tempReturn == -2 ) {
			free(buf);
			aMultiplier++;
			tempLength = 1024 * aMultiplier;
			buf = (char *)(calloc(tempLength,sizeof(char)));
		}
	}
	/////////////////////////////////////////////////////////////
	//  Successful read.  We need to process the object
	/////////////////////////////////////////////////////////////
	if ( tempReturn == 0 ) {
		tempArray.RemoveAll();
		/////////////////////////////////////////////////////////////
		//  Parse out the buffer read into an array of strings
		/////////////////////////////////////////////////////////////
		tempString = buf;
		utilityHelper.BuildArrayofStrings(tempArray,tempString);
		parentID = atoi(tempArray[0].Mid(5));
		if ( tempArray[1].Find(BayBegSearch) != -1 ) {
			tempBay.BuildFromStream(tempArray);
			tempBay.setDescription(desc);
			tempArray.RemoveAll();
			tempBay.setIsChanged(CString("TRUE"));
			tempBay.StreamAttributes(tempArray);
			sprintf(charBuf,"%d",parentID);
			tempString = "<PID>" + CString(charBuf) + CString("\n");
			for(h = 0; h < tempArray.GetSize(); h++)
				tempString += tempArray[h];
			tempLength = tempString.GetLength();
			if (storeObjStream(storeNum, tempString.GetBuffer(0),tempLength) != 0 ) return;
			tempString.ReleaseBuffer();
		}
	}
	free(buf);
	return;
}

//////////////////////////////////////////////////////////////////////
// Function Name : GetSectionDescripts
// Classname : None
// Description : Get the list of section descriptions
// Date Created : 10/01/98
// Author : wbh
//////////////////////////////////////////////////////////////////////
// Inputs : Array of fileoffsets
// Outputs : Array of section descriptions
// Explanation : 
// 
//////////////////////////////////////////////////////////////////////
// Algorithm : None
//////////////////////////////////////////////////////////////////////
// Revisions : 
//   Date-initials : Description_of_revision
//////////////////////////////////////////////////////////////////////
	/*******************************************************
	*
	*                       NOTICE
	*
	*  THIS SOFTWARE IS THE PROPERTY OF AND CONTAINS
	*  CONFIDENTIAL INFORMATION OF SSA GLOBAL 
	*  TECHNOLOGIES, INC., AND SHALL NOT BE COPIED,
	*  USED, NOR DISCLOSED WITHOUT EXPRESS WRITTEN
	*  AUTHORIZATION.  ALTHOUGH PUBLICATION IS NOT
	*  INTENDED, IN THE EVENT OF PUBLICATION, THE
	*  FOLLOWING NOTICE IS APPLICABLE:
	*
	*  (c) COPYRIGHT 2004 SSA GLOBAL TECHNOLOGIES, INC.
	*
	*           SSA GLOBAL TECHNOLOGIES, INC.
	*
	********************************************************/
//////////////////////////////////////////////////////////////////////
int CBTreeHelper::GetSectionDescriptions(CSsaStringArray & resArray, CArray <int, int&> &sectionList,
						   CArray <int, int&> &newSectionIndexList) {

	int i;
	CString forteRoot = getenv("TEMP");
	CString resNames;
	int tempReturn = -2;
	int tempLength;
	int aMultiplier = 1;
	char *buf = NULL;
	CString tempString;
	CSsaStringArray tempArray;
	int parentID= 0;
	qqhSLOTSection tempSection;
	int j;

	forteRoot += "\\tempfile.btd";
	int openRet = open_datastore(forteRoot.GetBuffer(0));
	forteRoot.ReleaseBuffer(0);
	if (openRet != 0)
		return -1;
	tempLength = 1024 * aMultiplier;
	buf = (char *) (calloc(tempLength,sizeof(char)));

	memset(buf,0,1024);

	resArray.RemoveAll();

	for ( i = 0; i < sectionList.GetSize(); i++ ) {
		/////////////////////////////////////////////////////////////
		//  If we do not prepare enough buffer for the read,
		//  the function will return a -2 code.  We then increase
		//  the size of our buffer.  We assume that we will receive a
		//  -2 at first.
		/////////////////////////////////////////////////////////////
		while ( tempReturn == -2 ) {
			tempLength = aMultiplier * 1024;
			memset(buf,0,aMultiplier * 1024);
			tempReturn = retObjStream(sectionList[i],buf,&tempLength);
			if ( tempReturn == -2 ) {
				free(buf);
				aMultiplier++;
				tempLength = 1024 * aMultiplier;
				buf = (char *)(calloc(tempLength,sizeof(char)));
			}
		}
		/////////////////////////////////////////////////////////////
		//  Successful read.  We need to process the object
		/////////////////////////////////////////////////////////////
		if ( tempReturn == 0 ) {
			tempArray.RemoveAll();
			/////////////////////////////////////////////////////////////
			//  Parse out the buffer read into an array of strings
			/////////////////////////////////////////////////////////////
			tempString = buf;
			utilityHelper.BuildArrayofStrings(tempArray,tempString);
			parentID = atoi(tempArray[0].Mid(5));
			if ( tempArray[1].Find(SectionBegSearch) != -1 ) {
				BOOL inserted = FALSE;
				tempSection.BuildFromStream(tempArray);
				for ( j = 0; j < resArray.GetSize() && (!inserted); j++) {
					if (resArray[j] > tempSection.getDescription()) {
						//resArray.SetSize(resArray.GetSize() + 1);
						resArray.InsertAt(j,tempSection.getDescription());
						inserted = TRUE;
						//newSectionIndexList.SetSize(newSectionIndexList.GetSize() + 1);
						newSectionIndexList.InsertAt(j,i);
					}
				}
				if (!inserted) {
					resArray.Add(tempSection.getDescription());
					newSectionIndexList.Add(i);
				}
				tempSection.getHotSpotList().RemoveAll();
			}
		}
		tempReturn = -2;
	}
//	resArray.SetSize(resArray.GetSize()+1);
	resArray.InsertAt(0,CString("New Section"));
	i = -1;
//	newSectionIndexList.SetSize(newSectionIndexList.GetSize()+1);
	newSectionIndexList.InsertAt(0,i);
	free(buf);
	finalize_datastore();
	return 0;
}


//////////////////////////////////////////////////////////////////////
// Function Name : ChangeAcadFileName
// Classname : None
// Description : Change the autocad file name attribute
// Date Created : 10/01/98
// Author : wbh
//////////////////////////////////////////////////////////////////////
// Inputs : new filename
// Outputs : success code
// Explanation : 
//   change the filename of the current facility in the btree file. 
//////////////////////////////////////////////////////////////////////
// Algorithm : None
//////////////////////////////////////////////////////////////////////
// Revisions : 
//   Date-initials : Description_of_revision
//////////////////////////////////////////////////////////////////////
	/*******************************************************
	*
	*                       NOTICE
	*
	*  THIS SOFTWARE IS THE PROPERTY OF AND CONTAINS
	*  CONFIDENTIAL INFORMATION OF SSA GLOBAL 
	*  TECHNOLOGIES, INC., AND SHALL NOT BE COPIED,
	*  USED, NOR DISCLOSED WITHOUT EXPRESS WRITTEN
	*  AUTHORIZATION.  ALTHOUGH PUBLICATION IS NOT
	*  INTENDED, IN THE EVENT OF PUBLICATION, THE
	*  FOLLOWING NOTICE IS APPLICABLE:
	*
	*  (c) COPYRIGHT 2004 SSA GLOBAL TECHNOLOGIES, INC.
	*
	*           SSA GLOBAL TECHNOLOGIES, INC.
	*
	********************************************************/
//////////////////////////////////////////////////////////////////////
int CBTreeHelper::ChangeAcadFileName(CString cadFileName) {
	int i;
	CString forteRoot = getenv("TEMP");
	int tempReturn = -2;
	int tempLength;
	int aMultiplier = 1;
	char *buf = NULL;
	CString tempString;
	CSsaStringArray tempArray;
	int parentID= 0;
	qqhSLOTFacility tempFacility;

	forteRoot += "\\tempfile.btd";
	int openRet = open_datastore(forteRoot.GetBuffer(0));
	forteRoot.ReleaseBuffer(0);
	if (openRet != 0)
		return -1;
	tempLength = 1024 * aMultiplier;
	buf = (char *) (calloc(tempLength,sizeof(char)));

	/////////////////////////////////////////////////////////////
	//  If we do not prepare enough buffer for the read,
	//  the function will return a -2 code.  We then increase
	//  the size of our buffer.  We assume that we will receive a
	//  -2 at first.
	/////////////////////////////////////////////////////////////
	while ( tempReturn == -2 ) {
		tempLength = aMultiplier * 1024;
		tempReturn = retObjStream(1,buf,&tempLength);
		if ( tempReturn == -2 ) {
			free(buf);
			aMultiplier++;
			tempLength = 1024 * aMultiplier;
			buf = (char *)(calloc(tempLength,sizeof(char)));
		}
	}
	/////////////////////////////////////////////////////////////
	//  Successful read.  We need to process the object
	/////////////////////////////////////////////////////////////
	if ( tempReturn == 0 ) {
		tempArray.RemoveAll();
		/////////////////////////////////////////////////////////////
		//  Parse out the buffer read into an array of strings
		/////////////////////////////////////////////////////////////
		tempString = buf;
		utilityHelper.BuildArrayofStrings(tempArray,tempString);
		parentID = atoi(tempArray[0].Mid(5));
		if ( tempArray[1].Find(FacilityBegSearch) != -1 ) {
			tempFacility.BuildFromStream(tempArray);
			tempFacility.setStrCadFileName(cadFileName);
		}
		tempArray.RemoveAll();
		tempFacility.StreamAttributes(tempArray);
		tempString = "<PID>0\n";
		for(i = 0; i < tempArray.GetSize(); i++) {
			tempString += tempArray[i];
		}
		tempLength = tempString.GetLength();
		tempReturn = storeObjStream(1, tempString.GetBuffer(0),tempLength);
		tempString.ReleaseBuffer();
		if ( tempReturn != 0 )
			printf("Error writing new filename\n");
	}
	finalize_datastore();
	free(buf);
	return tempReturn;
}


//////////////////////////////////////////////////////////////////////
// Function Name : UpdateFacilityDescription
// Classname : None
// Description : Change the facility name
// Date Created : 10/01/98
// Author : wbh
//////////////////////////////////////////////////////////////////////
// Inputs : new description
// Outputs : success code
// Explanation : 
//   change the description of the current facility in the btree file
//////////////////////////////////////////////////////////////////////
// Algorithm : None
//////////////////////////////////////////////////////////////////////
// Revisions : 
//   Date-initials : Description_of_revision
//////////////////////////////////////////////////////////////////////
	/*******************************************************
	*
	*                       NOTICE
	*
	*  THIS SOFTWARE IS THE PROPERTY OF AND CONTAINS
	*  CONFIDENTIAL INFORMATION OF SSA GLOBAL 
	*  TECHNOLOGIES, INC., AND SHALL NOT BE COPIED,
	*  USED, NOR DISCLOSED WITHOUT EXPRESS WRITTEN
	*  AUTHORIZATION.  ALTHOUGH PUBLICATION IS NOT
	*  INTENDED, IN THE EVENT OF PUBLICATION, THE
	*  FOLLOWING NOTICE IS APPLICABLE:
	*
	*  (c) COPYRIGHT 2004 SSA GLOBAL TECHNOLOGIES, INC.
	*
	*           SSA GLOBAL TECHNOLOGIES, INC.
	*
	********************************************************/
//////////////////////////////////////////////////////////////////////
int CBTreeHelper::UpdateFacilityDescription(CString description) {
	int i;
	CString forteRoot = getenv("TEMP");
	int tempReturn = -2;
	int tempLength;
	int aMultiplier = 1;
	char *buf = NULL;
	CString tempString;
	CSsaStringArray tempArray;
	int parentID= 0;
	qqhSLOTFacility tempFacility;

	forteRoot += "\\tempfile.btd";
	int openRet = open_datastore(forteRoot.GetBuffer(0));
	forteRoot.ReleaseBuffer(0);
	if (openRet != 0)
		return -1;
	tempLength = 1024 * aMultiplier;
	buf = (char *) (calloc(tempLength,sizeof(char)));

	/////////////////////////////////////////////////////////////
	//  If we do not prepare enough buffer for the read,
	//  the function will return a -2 code.  We then increase
	//  the size of our buffer.  We assume that we will receive a
	//  -2 at first.
	/////////////////////////////////////////////////////////////
	while ( tempReturn == -2 ) {
		tempLength = aMultiplier * 1024;
		tempReturn = retObjStream(1,buf,&tempLength);
		if ( tempReturn == -2 ) {
			free(buf);
			aMultiplier++;
			tempLength = 1024 * aMultiplier;
			buf = (char *)(calloc(tempLength,sizeof(char)));
		}
	}
	/////////////////////////////////////////////////////////////
	//  Successful read.  We need to process the object
	/////////////////////////////////////////////////////////////
	if ( tempReturn == 0 ) {
		tempArray.RemoveAll();
		/////////////////////////////////////////////////////////////
		//  Parse out the buffer read into an array of strings
		/////////////////////////////////////////////////////////////
		tempString = buf;
		printf("%s\n",tempString);
		utilityHelper.BuildArrayofStrings(tempArray,tempString);
		parentID = atoi(tempArray[0].Mid(5));
		if ( tempArray[1].Find(FacilityBegSearch) != -1 ) {
			tempFacility.BuildFromStream(tempArray);
			tempFacility.setDescription(description);
		}
		tempArray.RemoveAll();
		tempFacility.setIsChanged(CString("TRUE"));
		tempFacility.StreamAttributes(tempArray);
		tempString = "<PID>0\n";
		for(i = 0; i < tempArray.GetSize(); i++) {
			printf("%s",tempArray[i]);
			tempString += tempArray[i];
		}
		tempLength = tempString.GetLength();
//		printf("%s\n",tempString);
		tempReturn = storeObjStream(1, tempString.GetBuffer(0),tempLength);
		tempString.ReleaseBuffer();
		if ( tempReturn != 0 )
			printf("Error writing new filename\n");
	}
	finalize_datastore();
	free(buf);
	return tempReturn;
}

//////////////////////////////////////////////////////////////////////
// Begin Get/Set functions
//////////////////////////////////////////////////////////////////////
// Function Name : Get(Set)Bt(Element) Functions
// Classname : None
// Description : Set/Get facility elements
// Date Created : 10/01/98
// Author : wbh
//////////////////////////////////////////////////////////////////////
// Inputs : Get - fileOffset, Set - fileOffset, new Element
// Outputs : success code, Get - new Element
// Explanation : 
//   Update or read from the btree file a facility element (facility,
//   section, aisle, etc.)  These set of functions are all the same
//   except for the object that is being used.
//////////////////////////////////////////////////////////////////////
// Algorithm : None
//////////////////////////////////////////////////////////////////////
// Revisions : 
//   Date-initials : Description_of_revision
//////////////////////////////////////////////////////////////////////
	/*******************************************************
	*
	*                       NOTICE
	*
	*  THIS SOFTWARE IS THE PROPERTY OF AND CONTAINS
	*  CONFIDENTIAL INFORMATION OF SSA GLOBAL 
	*  TECHNOLOGIES, INC., AND SHALL NOT BE COPIED,
	*  USED, NOR DISCLOSED WITHOUT EXPRESS WRITTEN
	*  AUTHORIZATION.  ALTHOUGH PUBLICATION IS NOT
	*  INTENDED, IN THE EVENT OF PUBLICATION, THE
	*  FOLLOWING NOTICE IS APPLICABLE:
	*
	*  (c) COPYRIGHT 2004 SSA GLOBAL TECHNOLOGIES, INC.
	*
	*           SSA GLOBAL TECHNOLOGIES, INC.
	*
	********************************************************/
//////////////////////////////////////////////////////////////////////
//////////////////////////////////////////////////////////////////////


int CBTreeHelper::GetBtFacility(int fileOffset, qqhSLOTFacility &tempFacility) {
	CString forteRoot = getenv("TEMP");
	int tempReturn = -2;
	int tempLength;
	int aMultiplier = 1;
	char *buf = NULL;
	CString tempString;
	CSsaStringArray tempArray;
	int parentID= 0;

	UNREFERENCED_PARAMETER(fileOffset);

	forteRoot += "\\tempfile.btd";
	int openRet = open_datastore(forteRoot.GetBuffer(0));
	forteRoot.ReleaseBuffer(0);
	if (openRet != 0)
		return -1;
	tempLength = 1024 * aMultiplier;
	buf = (char *) (calloc(tempLength,sizeof(char)));

	/////////////////////////////////////////////////////////////
	//  If we do not prepare enough buffer for the read,
	//  the function will return a -2 code.  We then increase
	//  the size of our buffer.  We assume that we will receive a
	//  -2 at first.
	/////////////////////////////////////////////////////////////
	while ( tempReturn == -2 ) {
		tempLength = aMultiplier * 1024;
		tempReturn = retObjStream(1,buf,&tempLength);
		if ( tempReturn == -2 ) {
			free(buf);
			aMultiplier++;
			tempLength = 1024 * aMultiplier;
			buf = (char *)(calloc(tempLength,sizeof(char)));
		}
	}
	/////////////////////////////////////////////////////////////
	//  Successful read.  We need to process the object
	/////////////////////////////////////////////////////////////
	if ( tempReturn == 0 ) {
		tempArray.RemoveAll();
		/////////////////////////////////////////////////////////////
		//  Parse out the buffer read into an array of strings
		/////////////////////////////////////////////////////////////
		tempString = buf;
		printf("%s\n",tempString);
		utilityHelper.BuildArrayofStrings(tempArray,tempString);
		parentID = atoi(tempArray[0].Mid(5));
		if ( tempArray[1].Find(FacilityBegSearch) != -1 ) {
			tempFacility.BuildFromStream(tempArray);
		}
	}
	finalize_datastore();
	free(buf);
	return tempReturn;
}

int CBTreeHelper::GetBtSection(int fileOffset, qqhSLOTSection & tempSection) 
{
	CString forteRoot = getenv("TEMP");
	int tempReturn = -2;
	int tempLength;
	int aMultiplier = 1;
	char *buf = NULL;
	CString tempString;
	CSsaStringArray tempArray;
	int parentID= 0;

	forteRoot += "\\tempfile.btd";
	int openRet = open_datastore(forteRoot.GetBuffer(0));
	forteRoot.ReleaseBuffer(0);
	if (openRet != 0)
		return -1;
	tempLength = 1024 * aMultiplier;
	buf = (char *) (calloc(tempLength,sizeof(char)));

	/////////////////////////////////////////////////////////////
	//  If we do not prepare enough buffer for the read,
	//  the function will return a -2 code.  We then increase
	//  the size of our buffer.  We assume that we will receive a
	//  -2 at first.
	/////////////////////////////////////////////////////////////
	while ( tempReturn == -2 ) {
		tempLength = aMultiplier * 1024;
		tempReturn = retObjStream(fileOffset,buf,&tempLength);
		if ( tempReturn == -2 ) {
			free(buf);
			aMultiplier++;
			tempLength = 1024 * aMultiplier;
			buf = (char *)(calloc(tempLength,sizeof(char)));
		}
	}
	/////////////////////////////////////////////////////////////
	//  Successful read.  We need to process the object
	/////////////////////////////////////////////////////////////
	if ( tempReturn == 0 ) {
		tempArray.RemoveAll();
		/////////////////////////////////////////////////////////////
		//  Parse out the buffer read into an array of strings
		/////////////////////////////////////////////////////////////
		tempString = buf;
		printf("%s\n",tempString);
		utilityHelper.BuildArrayofStrings(tempArray,tempString);
		parentID = atoi(tempArray[0].Mid(5));
		if ( tempArray[1].Find(SectionBegSearch) != -1 ) {
			tempSection.BuildFromStream(tempArray);
		}
	}
	finalize_datastore();
	free(buf);
	return tempReturn;
}

int CBTreeHelper::GetBtAisle(int fileOffset, qqhSLOTAisle &tempAisle) {
	CString forteRoot = getenv("TEMP");
	int tempReturn = -2;
	int tempLength;
	int aMultiplier = 1;
	char *buf = NULL;
	CString tempString;
	CSsaStringArray tempArray;
	int parentID= 0;

	forteRoot += "\\tempfile.btd";
	int openRet = open_datastore(forteRoot.GetBuffer(0));
	forteRoot.ReleaseBuffer(0);
	if (openRet != 0)
		return -1;
	tempLength = 1024 * aMultiplier;
	buf = (char *) (calloc(tempLength,sizeof(char)));
	
	memset(buf,0,1024 * aMultiplier);
	/////////////////////////////////////////////////////////////
	//  If we do not prepare enough buffer for the read,
	//  the function will return a -2 code.  We then increase
	//  the size of our buffer.  We assume that we will receive a
	//  -2 at first.
	/////////////////////////////////////////////////////////////
	while ( tempReturn == -2 ) {
		tempLength = aMultiplier * 1024;
		tempReturn = retObjStream(fileOffset,buf,&tempLength);
		if ( tempReturn == -2 ) {
			free(buf);
			aMultiplier++;
			tempLength = 1024 * aMultiplier;
			buf = (char *)(calloc(tempLength,sizeof(char)));
			memset(buf,0,1024 * aMultiplier);
		}
	}
	/////////////////////////////////////////////////////////////
	//  Successful read.  We need to process the object
	/////////////////////////////////////////////////////////////
	if ( tempReturn == 0 ) {
		tempArray.RemoveAll();
		/////////////////////////////////////////////////////////////
		//  Parse out the buffer read into an array of strings
		/////////////////////////////////////////////////////////////
		tempString = buf;
		//AfxMessageBox(tempString);
		utilityHelper.BuildArrayofStrings(tempArray,tempString);
		parentID = atoi(tempArray[0].Mid(5));
		if ( tempArray[1].Find(AisleBegSearch) != -1 ) {
			tempAisle.BuildFromStream(tempArray);
		}
	}
	finalize_datastore();
	free(buf);
	return tempReturn;
}

int CBTreeHelper::GetBtSide(int fileOffset, qqhSLOTSide &tempSide) {
	CString forteRoot = getenv("TEMP");
	int tempReturn = -2;
	int tempLength;
	int aMultiplier = 1;
	char *buf = NULL;
	CString tempString;
	CSsaStringArray tempArray;
	int parentID= 0;

	forteRoot += "\\tempfile.btd";
	int openRet = open_datastore(forteRoot.GetBuffer(0));
	forteRoot.ReleaseBuffer(0);
	if (openRet != 0)
		return -1;
	tempLength = 1024 * aMultiplier;
	buf = (char *) (calloc(tempLength,sizeof(char)));

	/////////////////////////////////////////////////////////////
	//  If we do not prepare enough buffer for the read,
	//  the function will return a -2 code.  We then increase
	//  the size of our buffer.  We assume that we will receive a
	//  -2 at first.
	/////////////////////////////////////////////////////////////
	while ( tempReturn == -2 ) {
		tempLength = aMultiplier * 1024;
		tempReturn = retObjStream(fileOffset,buf,&tempLength);
		if ( tempReturn == -2 ) {
			free(buf);
			aMultiplier++;
			tempLength = 1024 * aMultiplier;
			buf = (char *)(calloc(tempLength,sizeof(char)));
		}
	}
	/////////////////////////////////////////////////////////////
	//  Successful read.  We need to process the object
	/////////////////////////////////////////////////////////////
	if ( tempReturn == 0 ) {
		tempArray.RemoveAll();
		/////////////////////////////////////////////////////////////
		//  Parse out the buffer read into an array of strings
		/////////////////////////////////////////////////////////////
		tempString = buf;
		printf("%s\n",tempString);
		utilityHelper.BuildArrayofStrings(tempArray,tempString);
		parentID = atoi(tempArray[0].Mid(5));
		if ( tempArray[1].Find(SideBegSearch) != -1 ) {
			tempSide.BuildFromStream(tempArray);
		}
	}
	finalize_datastore();
	free(buf);
	return tempReturn;
}

int CBTreeHelper::GetBtBay(int fileOffset, qqhSLOTBay &tempBay) {
	CString forteRoot = getenv("TEMP");
	int tempReturn = -2;
	int tempLength;
	int aMultiplier = 1;
	char *buf = NULL;
	CString tempString;
	CSsaStringArray tempArray;
	int parentID= 0;
	
	forteRoot += "\\tempfile.btd";
	int openRet = open_datastore(forteRoot.GetBuffer(0));
	forteRoot.ReleaseBuffer(0);
	if (openRet != 0)
		return -1;
	tempLength = 1024 * aMultiplier;
	buf = (char *) (calloc(tempLength,sizeof(char)));

	/////////////////////////////////////////////////////////////
	//  If we do not prepare enough buffer for the read,
	//  the function will return a -2 code.  We then increase
	//  the size of our buffer.  We assume that we will receive a
	//  -2 at first.
	/////////////////////////////////////////////////////////////
	while ( tempReturn == -2 ) {
		tempLength = aMultiplier * 1024;
		tempReturn = retObjStream(fileOffset,buf,&tempLength);
		if ( tempReturn == -2 ) {
			free(buf);
			aMultiplier++;
			tempLength = 1024 * aMultiplier;
			buf = (char *)(calloc(tempLength,sizeof(char)));
		}
	}
	/////////////////////////////////////////////////////////////
	//  Successful read.  We need to process the object
	/////////////////////////////////////////////////////////////
	if ( tempReturn == 0 ) {
		tempArray.RemoveAll();
		/////////////////////////////////////////////////////////////
		//  Parse out the buffer read into an array of strings
		/////////////////////////////////////////////////////////////
		tempString = buf;
		printf("%s\n",tempString);
		utilityHelper.BuildArrayofStrings(tempArray,tempString);
		parentID = atoi(tempArray[0].Mid(5));
		if ( tempArray[1].Find(BayBegSearch) != -1 ) {
			tempBay.BuildFromStream(tempArray);
		}
	}
	finalize_datastore();
	free(buf);
	return tempReturn;
}

int CBTreeHelper::GetBtLevel(int fileOffset, qqhSLOTLevel &tempLevel) {
	CString forteRoot = getenv("TEMP");
	int tempReturn = -2;
	int tempLength;
	int aMultiplier = 1;
	char *buf = NULL;
	CString tempString;
	CSsaStringArray tempArray;
	int parentID= 0;

	forteRoot += "\\tempfile.btd";
	int openRet = open_datastore(forteRoot.GetBuffer(0));
	forteRoot.ReleaseBuffer(0);
	if (openRet != 0)
		return -1;
	tempLength = 1024 * aMultiplier;
	buf = (char *) (calloc(tempLength,sizeof(char)));

	/////////////////////////////////////////////////////////////
	//  If we do not prepare enough buffer for the read,
	//  the function will return a -2 code.  We then increase
	//  the size of our buffer.  We assume that we will receive a
	//  -2 at first.
	/////////////////////////////////////////////////////////////
	while ( tempReturn == -2 ) {
		tempLength = aMultiplier * 1024;
		tempReturn = retObjStream(fileOffset,buf,&tempLength);
		if ( tempReturn == -2 ) {
			free(buf);
			aMultiplier++;
			tempLength = 1024 * aMultiplier;
			buf = (char *)(calloc(tempLength,sizeof(char)));
		}
	}
	/////////////////////////////////////////////////////////////
	//  Successful read.  We need to process the object
	/////////////////////////////////////////////////////////////
	if ( tempReturn == 0 ) {
		tempArray.RemoveAll();
		/////////////////////////////////////////////////////////////
		//  Parse out the buffer read into an array of strings
		/////////////////////////////////////////////////////////////
		tempString = buf;
		printf("%s\n",tempString);
//		AfxMessageBox(tempString);
		utilityHelper.BuildArrayofStrings(tempArray,tempString);
		parentID = atoi(tempArray[0].Mid(5));
		if ( tempArray[1].Find(LevelBegSearch) != -1 ) {
			tempLevel.BuildFromStream(tempArray);
		}
	}
//	AfxMessageBox("Returning");
	finalize_datastore();
	free(buf);
	return tempReturn;
}

int CBTreeHelper::GetBtLocation(int fileOffset, qqhSLOTLocation  &tempLocation) {
	CString forteRoot = getenv("TEMP");
	int tempReturn = -2;
	int tempLength;
	int aMultiplier = 1;
	char *buf = NULL;
	CString tempString;
	CSsaStringArray tempArray;
	int parentID= 0;

	forteRoot += "\\tempfile.btd";
	int openRet = open_datastore(forteRoot.GetBuffer(0));
	forteRoot.ReleaseBuffer(0);
	if (openRet != 0)
		return -1;
	tempLength = 1024 * aMultiplier;
	buf = (char *) (calloc(tempLength,sizeof(char)));

	/////////////////////////////////////////////////////////////
	//  If we do not prepare enough buffer for the read,
	//  the function will return a -2 code.  We then increase
	//  the size of our buffer.  We assume that we will receive a
	//  -2 at first.
	/////////////////////////////////////////////////////////////
	while ( tempReturn == -2 ) {
		tempLength = aMultiplier * 1024;
		tempReturn = retObjStream(fileOffset,buf,&tempLength);
		if ( tempReturn == -2 ) {
			free(buf);
			aMultiplier++;
			tempLength = 1024 * aMultiplier;
			buf = (char *)(calloc(tempLength,sizeof(char)));
		}
	}
	/////////////////////////////////////////////////////////////
	//  Successful read.  We need to process the object
	/////////////////////////////////////////////////////////////
	if ( tempReturn == 0 ) {
		tempArray.RemoveAll();
		/////////////////////////////////////////////////////////////
		//  Parse out the buffer read into an array of strings
		/////////////////////////////////////////////////////////////
		tempString = buf;
		printf("%s\n",tempString);
		utilityHelper.BuildArrayofStrings(tempArray,tempString);
		parentID = atoi(tempArray[0].Mid(5));
		if ( tempArray[1].Find(LocationBegSearch) != -1 ) {
			tempLocation.BuildFromStream(tempArray);
		}
	}
	finalize_datastore();
	free(buf);
	return tempReturn;
}

int CBTreeHelper::SetBtFacility(int fileOffset, qqhSLOTFacility & newFacility) {
	CString forteRoot = getenv("TEMP");
	int tempReturn = -2;
	int tempLength;
	int aMultiplier = 1;
	char *buf = NULL;
	CString tempString;
	CSsaStringArray tempArray;
	int parentID= 0;
	int h;
	qqhSLOTFacility origFacility;

	forteRoot += "\\tempfile.btd";
	int openRet = open_datastore(forteRoot.GetBuffer(0));
	forteRoot.ReleaseBuffer(0);
	if (openRet != 0)
		return -1;
	tempLength = 1024 * aMultiplier;
	buf = (char *) (calloc(tempLength,sizeof(char)));

	/////////////////////////////////////////////////////////////
	//  If we do not prepare enough buffer for the read,
	//  the function will return a -2 code.  We then increase
	//  the size of our buffer.  We assume that we will receive a
	//  -2 at first.
	/////////////////////////////////////////////////////////////
	while ( tempReturn == -2 ) {
		tempLength = aMultiplier * 1024;
		tempReturn = retObjStream(fileOffset,buf,&tempLength);
		if ( tempReturn == -2 ) {
			free(buf);
			aMultiplier++;
			tempLength = 1024 * aMultiplier;
			buf = (char *)(calloc(tempLength,sizeof(char)));
		}
	}
	/////////////////////////////////////////////////////////////
	//  Successful read.  We need to process the object
	/////////////////////////////////////////////////////////////
	if ( tempReturn == 0 ) {
		tempArray.RemoveAll();
		/////////////////////////////////////////////////////////////
		//  Parse out the buffer read into an array of strings
		/////////////////////////////////////////////////////////////
		tempString = buf;
		//printf("%s\n",tempString);
		utilityHelper.BuildArrayofStrings(tempArray,tempString);
		parentID = atoi(tempArray[0].Mid(5));
		if ( tempArray[1].Find(FacilityBegSearch) != -1 ) {
			origFacility.BuildFromStream(tempArray);
			//sprintf(charBuf,"%d",desc);
			origFacility = newFacility;
			tempArray.RemoveAll();
			origFacility.setIsChanged(CString("TRUE"));
			origFacility.StreamAttributes(tempArray);
			//sprintf(charBuf,"%d",parentID);
			tempString.Format("<PID>%d\n",parentID);
			for(h = 0; h < tempArray.GetSize(); h++)
				tempString += tempArray[h];
			//printf("------\n%s",tempString);
			tempLength = tempString.GetLength();
			if ( storeObjStream(fileOffset, tempString.GetBuffer(0),tempLength) != 0) {
				tempString.ReleaseBuffer();
				return -1;
			}
			tempString.ReleaseBuffer();

		}
	}
	else
		return -1;
	finalize_datastore();
	free(buf);
	return 0;
}

int CBTreeHelper::SetBtSection(int fileOffset, qqhSLOTSection &newSection) {
	CString forteRoot = getenv("TEMP");
	int tempReturn = -2;
	int tempLength;
	int aMultiplier = 1;
	char *buf = NULL;
	CString tempString;
	CSsaStringArray tempArray;
	int parentID= 0;
	int h;
	qqhSLOTSection origSection;

	forteRoot += "\\tempfile.btd";
	int openRet = open_datastore(forteRoot.GetBuffer(0));
	forteRoot.ReleaseBuffer(0);
	if (openRet != 0)
		return -1;
	tempLength = 1024 * aMultiplier;
	buf = (char *) (calloc(tempLength,sizeof(char)));

	/////////////////////////////////////////////////////////////
	//  If we do not prepare enough buffer for the read,
	//  the function will return a -2 code.  We then increase
	//  the size of our buffer.  We assume that we will receive a
	//  -2 at first.
	/////////////////////////////////////////////////////////////
	while ( tempReturn == -2 ) {
		tempLength = aMultiplier * 1024;
		tempReturn = retObjStream(fileOffset,buf,&tempLength);
		if ( tempReturn == -2 ) {
			free(buf);
			aMultiplier++;
			tempLength = 1024 * aMultiplier;
			buf = (char *)(calloc(tempLength,sizeof(char)));
		}
	}
	/////////////////////////////////////////////////////////////
	//  Successful read.  We need to process the object
	/////////////////////////////////////////////////////////////
	if ( tempReturn == 0 ) {
		tempArray.RemoveAll();
		/////////////////////////////////////////////////////////////
		//  Parse out the buffer read into an array of strings
		/////////////////////////////////////////////////////////////
		tempString = buf;
		//printf("%s\n",tempString);
		utilityHelper.BuildArrayofStrings(tempArray,tempString);
		parentID = atoi(tempArray[0].Mid(5));
		if ( tempArray[1].Find(SectionBegSearch) != -1 ) {
			origSection.BuildFromStream(tempArray);
			//sprintf(charBuf,"%d",desc);
			origSection = newSection;
			tempArray.RemoveAll();
			origSection.setIsChanged(CString("TRUE"));
			origSection.StreamAttributes(tempArray);
			//sprintf(charBuf,"%d",parentID);
			tempString.Format("<PID>%d\n",parentID);
			for(h = 0; h < tempArray.GetSize(); h++)
				tempString += tempArray[h];
			//printf("------\n%s",tempString);
			tempLength = tempString.GetLength();
			if ( storeObjStream(fileOffset, tempString.GetBuffer(0),tempLength) != 0) {
				tempString.ReleaseBuffer();
				return -1;
			}
			tempString.ReleaseBuffer();

		}
	}
	else
		return -1;
	finalize_datastore();
	free(buf);
	return 0;
}

int CBTreeHelper::SetBtAisle(int fileOffset, qqhSLOTAisle &newAisle) {
	CString forteRoot = getenv("TEMP");
	int tempReturn = -2;
	int tempLength;
	int aMultiplier = 1;
	char *buf = NULL;
	CString tempString;
	CSsaStringArray tempArray;
	int parentID= 0;
	int h;
	qqhSLOTAisle origAisle;

	forteRoot += "\\tempfile.btd";
	int openRet = open_datastore(forteRoot.GetBuffer(0));
	forteRoot.ReleaseBuffer(0);
	if (openRet != 0)
		return -1;
	tempLength = 1024 * aMultiplier;
	buf = (char *) (calloc(tempLength,sizeof(char)));

	/////////////////////////////////////////////////////////////
	//  If we do not prepare enough buffer for the read,
	//  the function will return a -2 code.  We then increase
	//  the size of our buffer.  We assume that we will receive a
	//  -2 at first.
	/////////////////////////////////////////////////////////////
	while ( tempReturn == -2 ) {
		tempLength = aMultiplier * 1024;
		tempReturn = retObjStream(fileOffset,buf,&tempLength);
		if ( tempReturn == -2 ) {
			free(buf);
			aMultiplier++;
			tempLength = 1024 * aMultiplier;
			buf = (char *)(calloc(tempLength,sizeof(char)));
		}
	}
	/////////////////////////////////////////////////////////////
	//  Successful read.  We need to process the object
	/////////////////////////////////////////////////////////////
	if ( tempReturn == 0 ) {
		tempArray.RemoveAll();
		/////////////////////////////////////////////////////////////
		//  Parse out the buffer read into an array of strings
		/////////////////////////////////////////////////////////////
		tempString = buf;
		//printf("%s\n",tempString);
		utilityHelper.BuildArrayofStrings(tempArray,tempString);
		parentID = atoi(tempArray[0].Mid(5));
		if ( tempArray[1].Find(AisleBegSearch) != -1 ) {
			origAisle.BuildFromStream(tempArray);
			//sprintf(charBuf,"%d",desc);
			origAisle = newAisle;
			tempArray.RemoveAll();
			origAisle.setIsChanged(CString("TRUE"));
			origAisle.StreamAttributes(tempArray);
			//sprintf(charBuf,"%d",parentID);
			tempString.Format("<PID>%d\n",parentID);
			for(h = 0; h < tempArray.GetSize(); h++)
				tempString += tempArray[h];
			//printf("------\n%s",tempString);
			tempLength = tempString.GetLength();
			if ( storeObjStream(fileOffset, tempString.GetBuffer(0),tempLength) != 0) {
				tempString.ReleaseBuffer();
				return -1;
			}
			tempString.ReleaseBuffer();

		}
	}
	else
		return -1;
	finalize_datastore();
	free(buf);
	return 0;
}

int CBTreeHelper::SetBtSide(int fileOffset, qqhSLOTSide &newSide) {
	CString forteRoot = getenv("TEMP");
	int tempReturn = -2;
	int tempLength;
	int aMultiplier = 1;
	char *buf = NULL;
	CString tempString;
	CSsaStringArray tempArray;
	int parentID= 0;
	int h;
	qqhSLOTSide origSide;

	forteRoot += "\\tempfile.btd";
	int openRet = open_datastore(forteRoot.GetBuffer(0));
	forteRoot.ReleaseBuffer(0);
	if (openRet != 0)
		return -1;
	tempLength = 1024 * aMultiplier;
	buf = (char *) (calloc(tempLength,sizeof(char)));

	/////////////////////////////////////////////////////////////
	//  If we do not prepare enough buffer for the read,
	//  the function will return a -2 code.  We then increase
	//  the size of our buffer.  We assume that we will receive a
	//  -2 at first.
	/////////////////////////////////////////////////////////////
	while ( tempReturn == -2 ) {
		tempLength = aMultiplier * 1024;
		tempReturn = retObjStream(fileOffset,buf,&tempLength);
		if ( tempReturn == -2 ) {
			free(buf);
			aMultiplier++;
			tempLength = 1024 * aMultiplier;
			buf = (char *)(calloc(tempLength,sizeof(char)));
		}
	}
	/////////////////////////////////////////////////////////////
	//  Successful read.  We need to process the object
	/////////////////////////////////////////////////////////////
	if ( tempReturn == 0 ) {
		tempArray.RemoveAll();
		/////////////////////////////////////////////////////////////
		//  Parse out the buffer read into an array of strings
		/////////////////////////////////////////////////////////////
		tempString = buf;
		//printf("%s\n",tempString);
		utilityHelper.BuildArrayofStrings(tempArray,tempString);
		parentID = atoi(tempArray[0].Mid(5));
		if ( tempArray[1].Find(SideBegSearch) != -1 ) {
			origSide.BuildFromStream(tempArray);
			//sprintf(charBuf,"%d",desc);
			origSide = newSide;
			tempArray.RemoveAll();
			origSide.setIsChanged(CString("TRUE"));
			origSide.StreamAttributes(tempArray);
			//sprintf(charBuf,"%d",parentID);
			tempString.Format("<PID>%d\n",parentID);
			for(h = 0; h < tempArray.GetSize(); h++)
				tempString += tempArray[h];
			//printf("------\n%s",tempString);
			tempLength = tempString.GetLength();
			if ( storeObjStream(fileOffset, tempString.GetBuffer(0),tempLength) != 0) {
				tempString.ReleaseBuffer();
				return -1;
			}
			tempString.ReleaseBuffer();

		}
	}
	else
		return -1;
	finalize_datastore();
	free(buf);
	return 0;
}

int CBTreeHelper::SetBtBay(int fileOffset, qqhSLOTBay &newBay) {
	CString forteRoot = getenv("TEMP");
	int tempReturn = -2;
	int tempLength;
	int aMultiplier = 1;
	char *buf = NULL;
	CString tempString;
	CSsaStringArray tempArray;
	int parentID= 0;
	int h;
	qqhSLOTBay origBay;

	forteRoot += "\\tempfile.btd";
	int openRet = open_datastore(forteRoot.GetBuffer(0));
	forteRoot.ReleaseBuffer(0);
	if (openRet != 0)
		return -1;
	tempLength = 1024 * aMultiplier;
	buf = (char *) (calloc(tempLength,sizeof(char)));

	/////////////////////////////////////////////////////////////
	//  If we do not prepare enough buffer for the read,
	//  the function will return a -2 code.  We then increase
	//  the size of our buffer.  We assume that we will receive a
	//  -2 at first.
	/////////////////////////////////////////////////////////////
	while ( tempReturn == -2 ) {
		tempLength = aMultiplier * 1024;
		tempReturn = retObjStream(fileOffset,buf,&tempLength);
		if ( tempReturn == -2 ) {
			free(buf);
			aMultiplier++;
			tempLength = 1024 * aMultiplier;
			buf = (char *)(calloc(tempLength,sizeof(char)));
		}
	}
	/////////////////////////////////////////////////////////////
	//  Successful read.  We need to process the object
	/////////////////////////////////////////////////////////////
	if ( tempReturn == 0 ) {
		tempArray.RemoveAll();
		/////////////////////////////////////////////////////////////
		//  Parse out the buffer read into an array of strings
		/////////////////////////////////////////////////////////////
		tempString = buf;
		//printf("%s\n",tempString);
		utilityHelper.BuildArrayofStrings(tempArray,tempString);
		parentID = atoi(tempArray[0].Mid(5));
		if ( tempArray[1].Find(BayBegSearch) != -1 ) {
			origBay.BuildFromStream(tempArray);
			//sprintf(charBuf,"%d",desc);
			origBay = newBay;
			tempArray.RemoveAll();
			origBay.setIsChanged(CString("TRUE"));
			origBay.StreamAttributes(tempArray);
			//sprintf(charBuf,"%d",parentID);
			tempString.Format("<PID>%d\n",parentID);
			for(h = 0; h < tempArray.GetSize(); h++)
				tempString += tempArray[h];
			//printf("------\n%s",tempString);
			tempLength = tempString.GetLength();
			if ( storeObjStream(fileOffset, tempString.GetBuffer(0),tempLength) != 0) {
				tempString.ReleaseBuffer();
				return -1;
			}
			tempString.ReleaseBuffer();

		}
	}
	else
		return -1;
	finalize_datastore();
	free(buf);
	return 0;
}

int CBTreeHelper::SetBtLevel(int fileOffset, qqhSLOTLevel &newLevel) {
	CString forteRoot = getenv("TEMP");
	int tempReturn = -2;
	int tempLength;
	int aMultiplier = 1;
	char *buf = NULL;
	CString tempString;
	CSsaStringArray tempArray;
	int parentID= 0;
	int h;
	qqhSLOTLevel origLevel;

	forteRoot += "\\tempfile.btd";
	int openRet = open_datastore(forteRoot.GetBuffer(0));
	forteRoot.ReleaseBuffer(0);
	if (openRet != 0)
		return -1;
	tempLength = 1024 * aMultiplier;
	buf = (char *) (calloc(tempLength,sizeof(char)));

	/////////////////////////////////////////////////////////////
	//  If we do not prepare enough buffer for the read,
	//  the function will return a -2 code.  We then increase
	//  the size of our buffer.  We assume that we will receive a
	//  -2 at first.
	/////////////////////////////////////////////////////////////
	while ( tempReturn == -2 ) {
		tempLength = aMultiplier * 1024;
		tempReturn = retObjStream(fileOffset,buf,&tempLength);
		if ( tempReturn == -2 ) {
			free(buf);
			aMultiplier++;
			tempLength = 1024 * aMultiplier;
			buf = (char *)(calloc(tempLength,sizeof(char)));
		}
	}
	/////////////////////////////////////////////////////////////
	//  Successful read.  We need to process the object
	/////////////////////////////////////////////////////////////
	if ( tempReturn == 0 ) {
		tempArray.RemoveAll();
		/////////////////////////////////////////////////////////////
		//  Parse out the buffer read into an array of strings
		/////////////////////////////////////////////////////////////
		tempString = buf;
		//printf("%s\n",tempString);
		utilityHelper.BuildArrayofStrings(tempArray,tempString);
		parentID = atoi(tempArray[0].Mid(5));
		if ( tempArray[1].Find(LevelBegSearch) != -1 ) {
			origLevel.BuildFromStream(tempArray);
			//sprintf(charBuf,"%d",desc);
			origLevel = newLevel;
			tempArray.RemoveAll();
			origLevel.setIsChanged(CString("TRUE"));
			origLevel.StreamAttributes(tempArray);
			//sprintf(charBuf,"%d",parentID);
			tempString.Format("<PID>%d\n",parentID);
			for(h = 0; h < tempArray.GetSize(); h++)
				tempString += tempArray[h];
			//printf("------\n%s",tempString);
			tempLength = tempString.GetLength();
			if ( storeObjStream(fileOffset, tempString.GetBuffer(0),tempLength) != 0) {
				tempString.ReleaseBuffer();
				return -1;
			}
			tempString.ReleaseBuffer();

		}
	}
	else
		return -1;
	finalize_datastore();
	free(buf);
	return 0;
}

int CBTreeHelper::SetBtLocation(int fileOffset, qqhSLOTLocation &newLocation) {
	CString forteRoot = getenv("TEMP");
	int tempReturn = -2;
	int tempLength;
	int aMultiplier = 1;
	char *buf = NULL;
	CString tempString;
	CSsaStringArray tempArray;
	int parentID= 0;
	int h;
	qqhSLOTLocation origLocation;

	forteRoot += "\\tempfile.btd";
	int openRet = open_datastore(forteRoot.GetBuffer(0));
	forteRoot.ReleaseBuffer(0);
	if (openRet != 0)
		return -1;
	tempLength = 1024 * aMultiplier;
	buf = (char *) (calloc(tempLength,sizeof(char)));

	/////////////////////////////////////////////////////////////
	//  If we do not prepare enough buffer for the read,
	//  the function will return a -2 code.  We then increase
	//  the size of our buffer.  We assume that we will receive a
	//  -2 at first.
	/////////////////////////////////////////////////////////////
	while ( tempReturn == -2 ) {
		tempLength = aMultiplier * 1024;
		tempReturn = retObjStream(fileOffset,buf,&tempLength);
		if ( tempReturn == -2 ) {
			free(buf);
			aMultiplier++;
			tempLength = 1024 * aMultiplier;
			buf = (char *)(calloc(tempLength,sizeof(char)));
		}
	}
	/////////////////////////////////////////////////////////////
	//  Successful read.  We need to process the object
	/////////////////////////////////////////////////////////////
	if ( tempReturn == 0 ) {
		tempArray.RemoveAll();
		/////////////////////////////////////////////////////////////
		//  Parse out the buffer read into an array of strings
		/////////////////////////////////////////////////////////////
		tempString = buf;
		//printf("%s\n",tempString);
		utilityHelper.BuildArrayofStrings(tempArray,tempString);
		parentID = atoi(tempArray[0].Mid(5));
		if ( tempArray[1].Find(LocationBegSearch) != -1 ) {
			origLocation.BuildFromStream(tempArray);
			//sprintf(charBuf,"%d",desc);
			origLocation = newLocation;
			tempArray.RemoveAll();
			origLocation.setIsChanged(CString("TRUE"));
			origLocation.StreamAttributes(tempArray);
			//sprintf(charBuf,"%d",parentID);
			tempString.Format("<PID>%d\n",parentID);
			for(h = 0; h < tempArray.GetSize(); h++)
				tempString += tempArray[h];
			//printf("------\n%s",tempString);
			tempLength = tempString.GetLength();
			if ( storeObjStream(fileOffset, tempString.GetBuffer(0),tempLength) != 0) {
				tempString.ReleaseBuffer();
				return -1;
			}
			tempString.ReleaseBuffer();

		}
	}
	else
		return -1;
	finalize_datastore();
	free(buf);
	return 0;
}

//////////////////////////////////////////////////////////////////////
// End Get/Set Functions
//////////////////////////////////////////////////////////////////////


//////////////////////////////////////////////////////////////////////
// Function Name : BuildchangesTree
// Classname : None
// Description : Build facility structure
// Date Created : 10/01/98
// Author : wbh
//////////////////////////////////////////////////////////////////////
// Inputs : facility Tree, starting Number
// Outputs : updated facility Tree, success code
// Explanation : 
//   Read BTree File and Build Tree Node Structure.  Must find which
//   type of element we are using, and set up the parent / child
//   relationship within the facility structure
//////////////////////////////////////////////////////////////////////
// Algorithm : None
//////////////////////////////////////////////////////////////////////
// Revisions : 
//   Date-initials : Description_of_revision
//////////////////////////////////////////////////////////////////////
	/*******************************************************
	*
	*                       NOTICE
	*
	*  THIS SOFTWARE IS THE PROPERTY OF AND CONTAINS
	*  CONFIDENTIAL INFORMATION OF SSA GLOBAL 
	*  TECHNOLOGIES, INC., AND SHALL NOT BE COPIED,
	*  USED, NOR DISCLOSED WITHOUT EXPRESS WRITTEN
	*  AUTHORIZATION.  ALTHOUGH PUBLICATION IS NOT
	*  INTENDED, IN THE EVENT OF PUBLICATION, THE
	*  FOLLOWING NOTICE IS APPLICABLE:
	*
	*  (c) COPYRIGHT 2004 SSA GLOBAL TECHNOLOGIES, INC.
	*
	*           SSA GLOBAL TECHNOLOGIES, INC.
	*
	********************************************************/
//////////////////////////////////////////////////////////////////////
int CBTreeHelper::BuildChangesTree(TreeElement & changesTree, int & nextNum) 
{
	int tempReturn = -2;
	int tempLength;
	int aMultiplier = 1;
	char * buf = NULL;
	TreeElement * elementPtr = &(changesTree);
	TreeElement tempTreeElement;
	CString tempString;
	CSsaStringArray tempArray;
	int parentID,currentPID = 0;
//	int parentIndex = 0;
	qqhSLOTFacility tempFac;
	qqhSLOTSection tempSec;
	qqhSLOTAisle tempAisle;
	qqhSLOTSide tempSide;
	qqhSLOTBay	tempBay;
	qqhSLOTLevel tempLevel;
	qqhSLOTLocation tempLocation;
//	int complete = -1;
	int k;
	int insertedElement;

	
	CString forteRoot = getenv("TEMP");
	forteRoot += "\\tempfile.btd";
	int openRet = open_datastore(forteRoot.GetBuffer(0));
	forteRoot.ReleaseBuffer();
	if (openRet != 0)
		return -1;

	tempLength = 1024 * aMultiplier;
	buf = (char *) (calloc(tempLength,sizeof(char)));
	/////////////////////////////////////////////////////////////
	//  Read items 1 to n until we cannot find item "n"
	/////////////////////////////////////////////////////////////
	while ( tempReturn != -1 && tempReturn != -5) {
		/////////////////////////////////////////////////////////////
		//  If we do not prepare enough buffer for the read,
		//  the function will return a -2 code.  We then increase
		//  the size of our buffer.  We assume that we will receive a
		//  -2 at first.
		/////////////////////////////////////////////////////////////
		while ( tempReturn == -2 ) {
			tempLength = aMultiplier * 1024;
			memset(buf,0,aMultiplier * 1024);
			tempReturn = retObjStream(nextNum,buf,&tempLength);
			if ( tempReturn == -2 ) {
				free(buf);
				aMultiplier++;
				tempLength = 1024 * aMultiplier;
				buf = (char *)(calloc(tempLength,sizeof(char)));
			}
		}
		/////////////////////////////////////////////////////////////
		//  Successful read.  We need to process the object
		/////////////////////////////////////////////////////////////
		if ( tempReturn == 0 ) {
			tempString = buf;
			//AfxMessageBox(tempString);
			tempArray.RemoveAll();
			/////////////////////////////////////////////////////////////
			//  Parse out the buffer read into an array of strings
			/////////////////////////////////////////////////////////////
			utilityHelper.BuildArrayofStrings(tempArray,tempString);
			tempTreeElement.treeChildren.RemoveAll();
			/////////////////////////////////////////////////////////////
			// Get the parent offset number of this object
			/////////////////////////////////////////////////////////////
			parentID = atoi(tempArray[0].Mid(5));
			//tempTreeElement = new TreeElement();
			/////////////////////////////////////////////////////////////
			// Determine the type of facility element (facility, section,
			// etc., and build a temporary object from the stream to
			// get the necessary attributes.
			/////////////////////////////////////////////////////////////
			int dummy;

			if (tempArray[1].Find(FacilityBegSearch) != -1 ) 
				tempFac.BuildFromStream(tempArray);
			else if ( tempArray[1].Find(SectionBegSearch) != -1 ) {
				tempSec.BuildFromStream(tempArray);
				tempTreeElement.elementDBID = tempSec.getDBID();

				if (changesTree.DeletedSectionMap.Lookup(tempSec.getDBID(), dummy)) {
					nextNum++;
					tempReturn = -2;
					continue;
				}
					
				tempTreeElement.fileOffset = nextNum;
				strcpy(tempTreeElement.facilityElement,"SLOTSection");
				parentID = 1;
			}
			else if ( tempArray[1].Find(AisleBegSearch) != -1 ) {
				tempAisle.BuildFromStream(tempArray);
				tempTreeElement.elementDBID = tempAisle.getDBID();
				
				if (changesTree.DeletedAisleMap.Lookup(tempAisle.getDBID(), dummy)) {
					nextNum++;
					tempReturn = -2;
					continue;
				}

				tempTreeElement.fileOffset = nextNum;
				strcpy(tempTreeElement.facilityElement,"SLOTAisle");
				strcpy(tempTreeElement.acadHandle,tempAisle.getPickPath().getAcadHandle());
			}
			else if ( tempArray[1].Find(SideBegSearch) != -1 ) {
				tempSide.BuildFromStream(tempArray);
				tempTreeElement.elementDBID = tempSide.getDBID();

				if (changesTree.DeletedSideMap.Lookup(tempSide.getDBID(), dummy)) {
					nextNum++;
					tempReturn = -2;
					continue;
				}

				tempTreeElement.fileOffset = nextNum;
				strcpy(tempTreeElement.facilityElement,"SLOTSide");
				// added sideIndex so we can always insert the sides into the tree in the correct order
				tempTreeElement.sideIndex = atoi(tempSide.getDescription());
			}
			else if ( tempArray[1].Find(BayBegSearch) != -1 ) {
				tempBay.BuildFromStream(tempArray);
				tempTreeElement.elementDBID = tempBay.getDBID();
							
				if (changesTree.DeletedBayMap.Lookup(tempBay.getDBID(), dummy)) {
					nextNum++;
					tempReturn = -2;
					continue;
				}

				tempTreeElement.fileOffset = nextNum;
				strcpy(tempTreeElement.acadHandle,tempBay.getAcadHandle());
				//AfxMessageBox(tempBay.getAcadHandle());
				strcpy(tempTreeElement.facilityElement,"SLOTBay");
				tempTreeElement.xCoord = tempBay.getCoord().getX();
			}
			else if ( tempArray[1].Find(LevelBegSearch) != -1 ) {
				tempLevel.BuildFromStream(tempArray);
				tempTreeElement.elementDBID = tempLevel.getDBID();
				tempTreeElement.fileOffset = nextNum;
				strcpy(tempTreeElement.facilityElement,"SLOTLevel");
			}
			else if ( tempArray[1].Find(LocationBegSearch) != -1 ) {
				tempLocation.BuildFromStream(tempArray);
				tempTreeElement.elementDBID = tempLocation.getDBID();
				tempTreeElement.fileOffset = nextNum;
				strcpy(tempTreeElement.facilityElement,"SLOTLocation");
//				strcpy(tempTreeElement.acadHandle,tempLocation.getAcadHandle());
			}
			else
				printf("%s\n",tempArray[1]);
			/////////////////////////////////////////////////////////////
			//  We have found a new parent offset to attach this
			//  object to.
			/////////////////////////////////////////////////////////////
			// If it's not a section and it has a parent
			if ( strcmp(tempTreeElement.facilityElement,"SLOTSection") != 0 && parentID != 0) {
				elementPtr = changesTree.FindFacilityElement(parentID,tempTreeElement.facilityElement,changesTree);
				
				// brd - changed this to support deleting elements without saving
				if ( elementPtr == NULL )  {
					nextNum = nextNum + 1;
					tempReturn = -2;
					continue;
				//	return -1;
				}
				//
				// added this so that bay elements would be in order of xCoordinate.  This
				// is necessary for pickpath drawing
				//
				if (strcmp(tempTreeElement.facilityElement,"SLOTBay") == 0) {
					insertedElement = 0;
					for ( k = 0; k < elementPtr->treeChildren.GetSize() && insertedElement == 0; k++) {
						if ( elementPtr->treeChildren[k].xCoord > tempTreeElement.xCoord ) {
							elementPtr->treeChildren.InsertAt(k,tempTreeElement);
							elementPtr->treeChildren[k].treeParent = elementPtr;
							insertedElement = 1;
						}
					}
					if (insertedElement == 0) {
						elementPtr->treeChildren.Add(tempTreeElement);
						elementPtr->treeChildren[elementPtr->treeChildren.GetSize()-1].treeParent = elementPtr;
					}
				}
				else {
					// If the element is a side, make sure we put it in the correct order
					// relative to the other side.  Side 1 is always first
					if (strcmp(tempTreeElement.facilityElement,"SLOTSide") == 0) {
						if (elementPtr->treeChildren.GetSize() > 0) {
							if (elementPtr->treeChildren[0].sideIndex < tempTreeElement.sideIndex) {
								elementPtr->treeChildren.Add(tempTreeElement);
								elementPtr->treeChildren[1].treeParent = elementPtr;
							}
							else {
								elementPtr->treeChildren.InsertAt(0, tempTreeElement);
								elementPtr->treeChildren[0].treeParent = elementPtr;
							}
						}
						else {
							elementPtr->treeChildren.Add(tempTreeElement);
							elementPtr->treeChildren[0].treeParent = elementPtr;
						}
						
					}
					else {
						elementPtr->treeChildren.Add(tempTreeElement);
						elementPtr->treeChildren[elementPtr->treeChildren.GetSize()-1].treeParent = elementPtr;
					}
				}
			}
			// Must be a section
			else if (parentID != 0) {
				elementPtr = &(changesTree);
				elementPtr->treeChildren.Add(tempTreeElement);
				elementPtr->treeChildren[elementPtr->treeChildren.GetSize()-1].treeParent = elementPtr;
			}
			// Must be a facility
			else {
				elementPtr = &(changesTree);
				elementPtr->elementDBID = tempFac.getDBID();
				elementPtr->fileOffset = nextNum;
				strcpy(elementPtr->facilityElement,"SLOTFacility");
				elementPtr->treeParent = NULL;
				currentPID = elementPtr->elementDBID;
			}
			
			nextNum = nextNum + 1;
			tempReturn = -2;
		}
	}
	finalize_datastore();
	free(buf);
	elementPtr = NULL;

	/* MFS Debug 12Feb06
	CString debugMsg="Facility Changes Tree before loop:\n";
	ads_printf(debugMsg);
	int ns=changesTree.treeChildren.GetSize();
	debugMsg.Format("There are %d child Sections:\n", ns);
	ads_printf(debugMsg);
	for (int ds=0; ds < changesTree.treeChildren.GetSize(); ++ds) {
		int sid=changesTree.treeChildren[ds].elementDBID;
		int na=changesTree.treeChildren[ds].treeChildren.GetSize();
		debugMsg.Format("Section %d has %d aisles:\n", sid, na);
		ads_printf(debugMsg);
		for (int da=0; da < changesTree.treeChildren.GetSize(); ++da) {
			int aid=changesTree.treeChildren[ds].treeChildren[da].elementDBID;
			debugMsg.Format("Aisle %d  ", aid);
			ads_printf(debugMsg);
		}
		ads_printf("\n");
	}
	// MFS Debug 12Feb06 End */

	// Update parents because stuff may have moved around
	for (int s=0; s < changesTree.treeChildren.GetSize(); ++s) {
		changesTree.treeChildren[s].treeParent = &changesTree;
		for (int a=0; a < changesTree.treeChildren[s].treeChildren.GetSize(); ++a) {
			changesTree.treeChildren[s].treeChildren[a].treeParent = &(changesTree.treeChildren[s]);
			for (int si=0; si < changesTree.treeChildren[s].treeChildren[a].treeChildren.GetSize(); ++si) {
				changesTree.treeChildren[s].treeChildren[a].treeChildren[si].treeParent = &(changesTree.treeChildren[s].treeChildren[a]);
				for (int b=0; b < changesTree.treeChildren[s].treeChildren[a].treeChildren[si].treeChildren.GetSize(); ++b) {
					changesTree.treeChildren[s].treeChildren[a].treeChildren[si].treeChildren[b].treeParent = &(changesTree.treeChildren[s].treeChildren[a].treeChildren[si]);
					for (int l=0; l < changesTree.treeChildren[s].treeChildren[a].treeChildren[si].treeChildren[b].treeChildren.GetSize(); ++l) {
						changesTree.treeChildren[s].treeChildren[a].treeChildren[si].treeChildren[b].treeChildren[l].treeParent = &(changesTree.treeChildren[s].treeChildren[a].treeChildren[si].treeChildren[b]);
						for (int lo=0; lo < changesTree.treeChildren[s].treeChildren[a].treeChildren[si].treeChildren[b].treeChildren[l].treeChildren.GetSize(); ++lo) {
							changesTree.treeChildren[s].treeChildren[a].treeChildren[si].treeChildren[b].treeChildren[l].treeChildren[lo].treeParent = &(changesTree.treeChildren[s].treeChildren[a].treeChildren[si].treeChildren[b].treeChildren[l]);
						}
					}
				}
			}
		}
	}
		
	if ( tempReturn == -5 )
		return tempReturn;
	else
		return 0;
}

int CBTreeHelper::UpdateBTWithAisleForPickPath(CString acadHandle, TreeElement & changesTree, 
								 int * bayIndex, int * aisleIndex, int * sectionIndex, 
								 int * sideIndex) 
{
	SendAislesToForte(changesTree);
#if 0
	CString tempString;
	CSsaStringArray tempSendArray;
	CSsaStringArray tempRecvArray;
#endif
	CSsaStringArray tempArray;
	CString sendString;
	int i,j,k,l = 0;
	int tempInt;
	int foundBay = 0;

	/////////////////////////////////////////////////////////////
	// build the data to send to the Forte connection server
	/////////////////////////////////////////////////////////////
#if 0
	sendString = "<SIO>SLOTSocketString\n";
	tempSendArray.Add(sendString);
	tempSendArray.Add(CString("<SAI>") + acadHandle + CString("\n"));
	sendString = "<EIO>SLOTSocketString\n";
	tempSendArray.Add(sendString);

	forteService.SendToForteConnection(tempSendArray,tempRecvArray,
		CString("SLOTSocketString"), 6060);

	for (i=0; i < tempRecvArray.GetSize(); i++) {
		if ( tempRecvArray[i].Find("<SAI>") != -1 ) {
			tempString = tempRecvArray[i].Mid(5);
			tempInt = tempString.GetLength();
			if (tempString.GetAt(tempInt-1) == '\n' ) {
				tempString.SetAt(tempInt-1,' ');
				tempString.TrimRight();
			}
		}
	}
	tempInt = atoi(tempString.GetBuffer(0));
	tempString.ReleaseBuffer();
#else
	string acadHandleTmp = (LPCTSTR)acadHandle;
	tempInt = getSessionMgrSO()->RetrieveAisleToBTForPickPath(acadHandleTmp);
	// tempInt = (int) getSessionMgrSO()->RetrieveAisleToBTForPickPath(acadHandleTmp);
	// tempInt = (int) SessionMgrSO->RetrieveAisleToBTForPickPath(acadHandleTmp);
#endif
	if ( tempInt == -1 )
		return -1;
	if (tempInt > 0) {
		if (tempInt >= storeNextNumber ) {
			storeNextNumber = tempInt;
//			AfxMessageBox(acadHandle);

			if ( this->BuildChangesTree(changesTree,storeNextNumber) == 0 ) {
				for ( i = 0; i < changesTree.treeChildren.GetSize() && foundBay== 0; i++ ) {
					//Sections
					for ( j = 0; j < changesTree.treeChildren[i].treeChildren.GetSize() && foundBay == 0; j++ ) {
						//Aisles
						for (k = 0; k < changesTree.treeChildren[i].treeChildren[j].treeChildren.GetSize() && foundBay == 0; k++ ) {
							//Sides
							for (l = 0; l < changesTree.treeChildren[i].treeChildren[j].treeChildren[k].treeChildren.GetSize() && foundBay == 0; l++ ) {
								//Bays -- look here
								if ( strcmp(changesTree.treeChildren[i].treeChildren[j].treeChildren[k].treeChildren[l].acadHandle,acadHandle.GetBuffer(0)) == 0 ) {
									foundBay = 1;
									*bayIndex = l;
									*sideIndex = k;
									*aisleIndex = j;
									*sectionIndex = i;
								}
								acadHandle.ReleaseBuffer();
							}
						}
					}
				}
			}
		}
		else {
			for ( i = 0; i < changesTree.treeChildren.GetSize() && foundBay== 0; i++ ) {
				//Sections
				for ( j = 0; j < changesTree.treeChildren[i].treeChildren.GetSize() && foundBay == 0; j++ ) {
					//Aisles
					for (k = 0; k < changesTree.treeChildren[i].treeChildren[j].treeChildren.GetSize() && foundBay == 0; k++ ) {
						//Sides
						for (l = 0; l < changesTree.treeChildren[i].treeChildren[j].treeChildren[k].treeChildren.GetSize() && foundBay == 0; l++ ) {
							//Bays -- look here
							if ( strcmp(changesTree.treeChildren[i].treeChildren[j].treeChildren[k].treeChildren[l].acadHandle,acadHandle.GetBuffer(0)) == 0 ) {
								foundBay = 1;
								*bayIndex = l;
								*sideIndex = k;
								*aisleIndex = j;
								*sectionIndex = i;
							}
							acadHandle.ReleaseBuffer();
						}
					}
				}
			}
		}
		if (foundBay == 1) {
			return 0;
		}
	}
	*bayIndex = 0;
	*sideIndex = 0;
	*aisleIndex = 0;
	*sectionIndex = 0;
	return -1;
}


int CBTreeHelper::UpdateBTWithAisleForPickPath(int DBID, TreeElement & changesTree, 
								 int * aisleIndex, int * sectionIndex) 
{
	SendAislesToForte(changesTree);
#if 0
	CString tempString;
	CSsaStringArray tempSendArray;
	CSsaStringArray tempRecvArray;
	CSsaStringArray tempArray;
	CString sendString;
#endif
	int i,j = 0;
	int tempInt;
	int foundAisle = 0;
#if 0
	/////////////////////////////////////////////////////////////
	// build the data to send to the Forte connection server
	/////////////////////////////////////////////////////////////
	
	sendString = "<SIO>SLOTSocketString\n";
	tempSendArray.Add(sendString);
	tempString.Format("<SAI>%d\n",DBID);
	tempSendArray.Add(tempString);
	sendString = "<EIO>SLOTSocketString\n";
	tempSendArray.Add(sendString);

// need to change the number
	forteService.SendToForteConnection(tempSendArray,tempRecvArray,
		CString("SLOTSocketString"), 10000);

	for (i=0; i < tempRecvArray.GetSize(); i++) {
		if ( tempRecvArray[i].Find("<SAI>") != -1 ) {
			tempString = tempRecvArray[i].Mid(5);
			tempInt = tempString.GetLength();
			if (tempString.GetAt(tempInt-1) == '\n' ) {
				tempString.SetAt(tempInt-1,' ');
				tempString.TrimRight();
			}
		}
	}
	tempInt = atoi(tempString.GetBuffer(0));
	tempString.ReleaseBuffer();
#else
	tempInt = getSessionMgrSO()->RetrieveAisleToBTForPickPathByDBID(DBID);
#endif
printf("UpdateBTWithAisleForPickPath() - tempInt %d",tempInt);		
	if ( tempInt == -1 )
		return -1;
	if (tempInt > 0) {
		if (tempInt >= storeNextNumber ) {
			storeNextNumber = tempInt;

			if ( this->BuildChangesTree(changesTree,storeNextNumber) == 0 ) {
				for ( i = 0; i < changesTree.treeChildren.GetSize() && foundAisle == 0; i++ ) {
					//Sections
					for ( j = 0; j < changesTree.treeChildren[i].treeChildren.GetSize() && foundAisle == 0; j++ ) {
						//Aisles
						if ( changesTree.treeChildren[i].treeChildren[j].elementDBID == DBID ) {
printf("UpdateBTWithAisleForPickPath() - found aisle %d",DBID);		
							foundAisle = 1;
							*aisleIndex = j;
							*sectionIndex = i;
						}
					}
				}
			}
		}
		else {
			for ( i = 0; i < changesTree.treeChildren.GetSize() && foundAisle == 0; i++ ) {
				//Sections
				for ( j = 0; j < changesTree.treeChildren[i].treeChildren.GetSize() && foundAisle == 0; j++ ) {
					//Aisles
					if ( changesTree.treeChildren[i].treeChildren[j].elementDBID == DBID ) {
						foundAisle = 1;
						*aisleIndex = j;
						*sectionIndex = i;
					}
				}
			}
		}
		if (foundAisle == 1) {
			return 0;
		}
	}
	*aisleIndex = 0;
	*sectionIndex = 0;
	return -1;
}


int CBTreeHelper::UpdateBTWithAisleByConPickPathAcadHandle(CString acadHandle, TreeElement & changesTree) 
{
	SendAislesToForte(changesTree);
#if 0
	CString tempString;
	CSsaStringArray tempSendArray;
	CSsaStringArray tempRecvArray;
	CSsaStringArray tempArray;
	CString sendString;
#endif
	int i = 0;
	int tempInt;
#if 0
	/////////////////////////////////////////////////////////////
	// build the data to send to the Forte connection server
	/////////////////////////////////////////////////////////////
	
	sendString = "<SIO>SLOTSocketString\n";
	tempSendArray.Add(sendString);
	tempSendArray.Add(CString("<SAI>") + acadHandle + CString("\n"));
	sendString = "<EIO>SLOTSocketString\n";
	tempSendArray.Add(sendString);

	forteService.SendToForteConnection(tempSendArray,tempRecvArray,
		CString("SLOTSocketString"), 10110);

	for (i=0; i < tempRecvArray.GetSize(); i++) {
		if ( tempRecvArray[i].Find("<SAI>") != -1 ) {
			tempString = tempRecvArray[i].Mid(5);
			tempInt = tempString.GetLength();
			if (tempString.GetAt(tempInt-1) == '\n' ) {
				tempString.SetAt(tempInt-1,' ');
				tempString.TrimRight();
			}
		}
	}
	tempInt = atoi(tempString.GetBuffer(0));
	tempString.ReleaseBuffer();
#else
	string tempAcadHandle = (LPCTSTR)acadHandle;
	tempInt = getSessionMgrSO()->RetrieveAisleToBTByConPickPathAcadHandle(tempAcadHandle);
#endif
	if (tempInt >= storeNextNumber) {

		if ( this->BuildChangesTree(changesTree,tempInt) == 0 ) {
			storeNextNumber = tempInt;
			return (tempInt - 1);
		}
		return -1;
	}
	else
		return tempInt;
}

int CBTreeHelper::UpdateBTWithAisle(CString acadHandle, TreeElement & changesTree) 
{
	SendAislesToForte(changesTree);

#if 0	
	CString tempString;
	CSsaStringArray tempSendArray;
	CSsaStringArray tempRecvArray;
	CSsaStringArray tempArray;
	CString sendString;
#endif
	int i = 0;
	int tempInt;

	/////////////////////////////////////////////////////////////
	// build the data to send to the Forte connection server
	/////////////////////////////////////////////////////////////
#if 0	
	sendString = "<SIO>SLOTSocketString\n";
	tempSendArray.Add(sendString);
	tempSendArray.Add(CString("<SAI>") + acadHandle + CString("\n"));
	sendString = "<EIO>SLOTSocketString\n";
	tempSendArray.Add(sendString);

	forteService.SendToForteConnection(tempSendArray,tempRecvArray,
		CString("SLOTSocketString"), 6070);

	for (i=0; i < tempRecvArray.GetSize(); i++) {
		if ( tempRecvArray[i].Find("<SAI>") != -1 ) {
			tempString = tempRecvArray[i].Mid(5);
			tempInt = tempString.GetLength();
			if (tempString.GetAt(tempInt-1) == '\n' ) {
				tempString.SetAt(tempInt-1,' ');
				tempString.TrimRight();
			}
		}
	}
	tempInt = atoi(tempString.GetBuffer(0));
	tempString.ReleaseBuffer();
#else
	string tempAcadHandle = (LPCTSTR)acadHandle;
	tempInt = getSessionMgrSO()->RetrieveAisleToBT(tempAcadHandle);
#endif
	if ( tempInt == -1 )
		return -1;

	if (tempInt >= storeNextNumber) {

		if ( this->BuildChangesTree(changesTree,tempInt) == 0 ) {
			storeNextNumber = tempInt;
		}
		return (tempInt - 1);
	}
	else
		return tempInt;

}


int CBTreeHelper::UpdateBTWithAisleForPickPathRenumbering(int DBID, TreeElement & changesTree, 
								 int * aisleIndex, int * sectionIndex) 
{
	SendAislesToForte(changesTree);
#if 0
	CString tempString;
	CSsaStringArray tempSendArray;
	CSsaStringArray tempRecvArray;
	CSsaStringArray tempArray;
	CString sendString;
#endif
	int i,j;
	int tempInt;
	int foundAisle = 0;

#if 0
	/////////////////////////////////////////////////////////////
	// build the data to send to the Forte connection server
	/////////////////////////////////////////////////////////////
	
	sendString = "<SIO>SLOTSocketString\n";
	tempSendArray.Add(sendString);
	tempString.Format("<SAI>%d\n",DBID);
	tempSendArray.Add(tempString);
	sendString = "<EIO>SLOTSocketString\n";
	tempSendArray.Add(sendString);

// need to change the number
	forteService.SendToForteConnection(tempSendArray,tempRecvArray,
		CString("SLOTSocketString"), 10000);

	for (i=0; i < tempRecvArray.GetSize(); i++) {
		if ( tempRecvArray[i].Find("<SAI>") != -1 ) {
			tempString = tempRecvArray[i].Mid(5);
			tempInt = tempString.GetLength();
			if (tempString.GetAt(tempInt-1) == '\n' ) {
				tempString.SetAt(tempInt-1,' ');
				tempString.TrimRight();
			}
		}
	}
	tempInt = atoi(tempString.GetBuffer(0));
	tempString.ReleaseBuffer();
#else
	tempInt = getSessionMgrSO()->RetrieveAisleToBTForPickPathByDBID(DBID);
#endif
	
	if ( tempInt == -1 )
		return -1;
	if (tempInt > 0) {
		if (tempInt >= storeNextNumber ) {
			storeNextNumber = tempInt;
			if ( BuildChangesTree(changesTree,storeNextNumber) == 0 ) {
				for ( i = 0; i < changesTree.treeChildren.GetSize() && foundAisle == 0; i++ ) {
					//Sections
					for ( j = 0; j < changesTree.treeChildren[i].treeChildren.GetSize() && foundAisle == 0; j++ ) {
						//Aisles
						if ( changesTree.treeChildren[i].treeChildren[j].elementDBID == DBID ) {

							foundAisle = 1;
							*aisleIndex = j;
							*sectionIndex = i;
						}
					}
				}
			}
		}
		else {
			for ( i = 0; i < changesTree.treeChildren.GetSize() && foundAisle == 0; i++ ) {
				//Sections
				for ( j = 0; j < changesTree.treeChildren[i].treeChildren.GetSize() && foundAisle == 0; j++ ) {
					//Aisles
					if ( changesTree.treeChildren[i].treeChildren[j].elementDBID == DBID ) {
						foundAisle = 1;
						*aisleIndex = j;
						*sectionIndex = i;
					}
				}
			}
		}
		if (foundAisle == 1) {
			return 0;
		}
	}
	*aisleIndex = 0;
	*sectionIndex = 0;
	return -1;
}


int CBTreeHelper::UpdateBTWithAisleByPickPathAcadHandle(CString acadHandle, TreeElement & changesTree) 
{
	SendAislesToForte(changesTree);

#if 0
	CString tempString;
	CSsaStringArray tempSendArray;
	CSsaStringArray tempRecvArray;
	CSsaStringArray tempArray;
	CString sendString;
#endif
	int i = 0;
	int tempInt;

#if 0
	/////////////////////////////////////////////////////////////
	// build the data to send to the Forte connection server
	/////////////////////////////////////////////////////////////
	
	sendString = "<SIO>SLOTSocketString\n";
	tempSendArray.Add(sendString);
	tempSendArray.Add(CString("<SAI>") + acadHandle + CString("\n"));
	sendString = "<EIO>SLOTSocketString\n";
	tempSendArray.Add(sendString);

	forteService.SendToForteConnection(tempSendArray,tempRecvArray,
		CString("SLOTSocketString"), 7020);

	for (i=0; i < tempRecvArray.GetSize(); i++) {
		if ( tempRecvArray[i].Find("<SAI>") != -1 ) {
			tempString = tempRecvArray[i].Mid(5);
			tempInt = tempString.GetLength();
			if (tempString.GetAt(tempInt-1) == '\n' ) {
				tempString.SetAt(tempInt-1,' ');
				tempString.TrimRight();
			}
		}
	}
	tempInt = atoi(tempString.GetBuffer(0));
	tempString.ReleaseBuffer();
#else
	string tempAcadHandle = (LPCTSTR)acadHandle;
	tempInt = getSessionMgrSO()->RetrieveAisleToBTByPickPathAcadHandle(tempAcadHandle);
#endif

	if (tempInt >= storeNextNumber) {
		if ( BuildChangesTree(changesTree,tempInt) == 0 ) {
			storeNextNumber = tempInt;
			return (tempInt - 1);
		}
		return -1;
	}
	else
		return tempInt;
}


int CBTreeHelper::UpdateBTWithBay(CString acadHandle, TreeElement & changesTree) 
{

	SendAislesToForte(changesTree);
#if 0
	CString tempString;
	CSsaStringArray tempSendArray;
	CSsaStringArray tempRecvArray;
	CSsaStringArray tempArray;
	CString sendString;
#endif
	int i,l = 0;
	int tempInt;
	BOOL foundBay = FALSE;

#if 0
	/////////////////////////////////////////////////////////////
	// build the data to send to the Forte connection server
	/////////////////////////////////////////////////////////////
	
	sendString = "<SIO>SLOTSocketString\n";
	tempSendArray.Add(sendString);
	tempSendArray.Add(CString("<SAI>") + acadHandle + CString("\n"));
	sendString = "<EIO>SLOTSocketString\n";
	tempSendArray.Add(sendString);

	forteService.SendToForteConnection(tempSendArray,tempRecvArray,
		CString("SLOTSocketString"), 6090);

	for (i=0; i < tempRecvArray.GetSize(); i++) {
		if ( tempRecvArray[i].Find("<SAI>") != -1 ) {
			tempString = tempRecvArray[i].Mid(5);
			tempInt = tempString.GetLength();
			if (tempString.GetAt(tempInt-1) == '\n' ) {
				tempString.SetAt(tempInt-1,' ');
				tempString.TrimRight();
			}
		}
	}
	tempInt = atoi(tempString.GetBuffer(0));
	tempString.ReleaseBuffer();
#else
	string tempAcadHandle = (LPCTSTR)acadHandle;
	tempInt = getSessionMgrSO()->RetrieveBayToBT(tempAcadHandle);
#endif
	if ( tempInt == -1 )
		return -1;

	if (tempInt >= storeNextNumber) {

		if ( this->BuildChangesTree(changesTree,tempInt) == 0 ) {
			for ( i = 0; i < changesTree.treeChildren.GetSize() && foundBay== 0; i++ ) {
				//Sections
				for ( int j = 0; j < changesTree.treeChildren[i].treeChildren.GetSize() && foundBay == 0; j++ ) {
					//Aisles
					for (int k = 0; k < changesTree.treeChildren[i].treeChildren[j].treeChildren.GetSize() && foundBay == 0; k++ ) {
						//Sides
						for (l = 0; l < changesTree.treeChildren[i].treeChildren[j].treeChildren[k].treeChildren.GetSize() && foundBay == 0; l++ ) {
							//Bays 
							if ( (tempInt - 1) == changesTree.treeChildren[i].treeChildren[j].treeChildren[k].treeChildren[l].fileOffset )
								foundBay = 1;
							for ( int m = 0; m < changesTree.treeChildren[i].treeChildren[j].treeChildren[k].treeChildren[l].treeChildren.GetSize() && foundBay == 0; m++ ) {
								//Levels
								if ( (tempInt - 1) == changesTree.treeChildren[i].treeChildren[j].treeChildren[k].treeChildren[l].treeChildren[m].fileOffset )
									foundBay = 1;
								for ( int n = 0; n < changesTree.treeChildren[i].treeChildren[j].treeChildren[k].treeChildren[l].treeChildren[m].treeChildren.GetSize() && foundBay == 0; n++ ) {
									//Locations
									if ( (tempInt - 1) == changesTree.treeChildren[i].treeChildren[j].treeChildren[k].treeChildren[l].treeChildren[m].treeChildren[n].fileOffset )
										foundBay = 1;
								}
							}
							if ( foundBay == 1 ) {
								storeNextNumber = tempInt;
								return changesTree.treeChildren[i].treeChildren[j].treeChildren[k].treeChildren[l].fileOffset;
							}
						}
					}
				}
			}
			storeNextNumber = tempInt;
			//return (tempInt -1);
		}
		return -1;
	}
	else
		return tempInt;
}



void CBTreeHelper::UpdateLevelDescription(CString desc, int storeNum) 
{

	qqhSLOTLevel tempLevel;
	CString forteRoot = getenv("TEMP");
	char charBuf[100];
	int tempReturn = -2;
	int tempLength;
	int aMultiplier = 1;
	char *buf = NULL;
	CString tempString;
	CSsaStringArray tempArray;
	int parentID= 0;
	int h;
	CUtilityHelper utilityHelper;

	forteRoot += "\\tempfile.btd";

	tempLength = 1024 * aMultiplier;
	buf = (char *) (calloc(tempLength,sizeof(char)));

	/////////////////////////////////////////////////////////////
	//  If we do not prepare enough buffer for the read,
	//  the function will return a -2 code.  We then increase
	//  the size of our buffer.  We assume that we will receive a
	//  -2 at first.
	/////////////////////////////////////////////////////////////
	while ( tempReturn == -2 ) {
		tempLength = aMultiplier * 1024;
		tempReturn = retObjStream(storeNum,buf,&tempLength);
		if ( tempReturn == -2 ) {
			free(buf);
			aMultiplier++;
			tempLength = 1024 * aMultiplier;
			buf = (char *)(calloc(tempLength,sizeof(char)));
		}
	}
	/////////////////////////////////////////////////////////////
	//  Successful read.  We need to process the object
	/////////////////////////////////////////////////////////////
	if ( tempReturn == 0 ) {
		tempArray.RemoveAll();
		/////////////////////////////////////////////////////////////
		//  Parse out the buffer read into an array of strings
		/////////////////////////////////////////////////////////////
		tempString = buf;
		utilityHelper.BuildArrayofStrings(tempArray,tempString);
		parentID = atoi(tempArray[0].Mid(5));
		if ( tempArray[1].Find(LevelBegSearch) != -1 ) {
			tempLevel.BuildFromStream(tempArray);
			//sprintf(charBuf,"%d",desc);
			tempLevel.setDescription(desc); 
			tempArray.RemoveAll();
			tempLevel.setIsChanged(CString("TRUE"));
			tempLevel.StreamAttributes(tempArray);
			sprintf(charBuf,"%d",parentID);
			tempString = "<PID>" + CString(charBuf) + CString("\n");
			for(h = 0; h < tempArray.GetSize(); h++)
				tempString += tempArray[h];
			tempLength = tempString.GetLength();
			//printf("------\n%s",tempString);
			if ( storeObjStream(storeNum, tempString.GetBuffer(0),tempLength) != 0 ) return;
			tempString.ReleaseBuffer();
		}
	}
	free(buf);
	return;
}

//////////////////////////////////////////////////////////////////////
// Function Name : AddBaytoFacility
// Classname : None
// Description : Insert a bay into the facility structure
// Date Created : 10/01/98
// Author : wbh
//////////////////////////////////////////////////////////////////////
// Inputs : a SLOTBay, side fileoffset, indexes into the facility structure
// Outputs : success code
// Explanation : 
//   This functions is used when a bay is deleted or the racktype is
//   changed.  It places a bay within an aisle/side based on the indexes
//   given and updates the facility tree structure.
//////////////////////////////////////////////////////////////////////
// Algorithm : None
//////////////////////////////////////////////////////////////////////
// Revisions : 
//   Date-initials : Description_of_revision
//////////////////////////////////////////////////////////////////////
	/*******************************************************
	*
	*                       NOTICE
	*
	*  THIS SOFTWARE IS THE PROPERTY OF AND CONTAINS
	*  CONFIDENTIAL INFORMATION OF SSA GLOBAL 
	*  TECHNOLOGIES, INC., AND SHALL NOT BE COPIED,
	*  USED, NOR DISCLOSED WITHOUT EXPRESS WRITTEN
	*  AUTHORIZATION.  ALTHOUGH PUBLICATION IS NOT
	*  INTENDED, IN THE EVENT OF PUBLICATION, THE
	*  FOLLOWING NOTICE IS APPLICABLE:
	*
	*  (c) COPYRIGHT 2004 SSA GLOBAL TECHNOLOGIES, INC.
	*
	*           SSA GLOBAL TECHNOLOGIES, INC.
	*
	********************************************************/
//////////////////////////////////////////////////////////////////////
int CBTreeHelper::AddBaytoFacility( qqhSLOTBay & theBay, const int parentSideOffset, TreeElement & changesTree, 
					 const int bayIndex,
					 const int parentSideIndex,
					 const int parentAisleIndex,
					 const int parentSectionIndex) {
	UNREFERENCED_PARAMETER(parentSideIndex);
	UNREFERENCED_PARAMETER(parentAisleIndex);
	UNREFERENCED_PARAMETER(parentSectionIndex);
	UNREFERENCED_PARAMETER(bayIndex);
	CSsaStringArray tempArray;
	CString tempString,tempTreeString;
	int h,i,j;
	int tempLength;
	CString sendString;
	CSsaStringArray tempSendArray;
	CSsaStringArray tempRecvArray;
	CSsaStringArray tempTreeArray;
	int startNum;
	int parentBayNum,parentLevelNum;

	CString forteRoot = getenv("TEMP");
	forteRoot += "\\tempfile.btd";
	int openRet = open_datastore(forteRoot.GetBuffer(0));
	forteRoot.ReleaseBuffer();
	if (openRet != 0)
		return -1;
	tempString.Format("<PID>%d\n",parentSideOffset);
	theBay.StreamAttributes(tempArray);
	for(h = 0; h < tempArray.GetSize(); h++)
		tempString += tempArray[h];
	
	startNum = storeNextNumber;
	tempLength = tempString.GetLength();
	if ( storeObjStream(storeNextNumber, tempString.GetBuffer(0),tempLength) != 0) return -1;
	tempString.ReleaseBuffer();
	parentBayNum = storeNextNumber;
	storeNextNumber++;
	tempTreeString = "SLOTBay";
	tempTreeArray.Add(tempTreeString);

	for ( i = 0; i < theBay.getChildList().GetSize(); i++) {
		tempString.Format("<PID>%d\n",parentBayNum);
		tempArray.RemoveAll();
		theBay.getChildList()[i].StreamAttributes(tempArray);
		for ( h = 0; h < tempArray.GetSize(); h++ )
			tempString += tempArray[h];
		tempLength = tempString.GetLength();
		if ( storeObjStream(storeNextNumber, tempString.GetBuffer(0),tempLength) != 0) return -1;
		tempString.ReleaseBuffer();
		parentLevelNum=storeNextNumber;
		storeNextNumber++;
		tempTreeString = "SLOTLevel";
		tempTreeArray.Add(tempTreeString);
		for ( j = 0; j < theBay.getChildList()[i].getChildList().GetSize(); j++) {
			tempString.Format("<PID>%d\n",parentLevelNum);
			tempArray.RemoveAll();
			theBay.getChildList()[i].getChildList()[j].StreamAttributes(tempArray);
			for ( h = 0; h < tempArray.GetSize(); h++ )
				tempString += tempArray[h];
			tempLength = tempString.GetLength();
			if ( storeObjStream(storeNextNumber, tempString.GetBuffer(0),tempLength) != 0) return -1;
			tempString.ReleaseBuffer();
			storeNextNumber++;
			tempTreeString = "SLOTLocation";
			tempTreeArray.Add(tempTreeString);
		}
	}
	
	finalize_datastore();
#if 1
	//AfxMessageBox("Adding Bay to facility");
	sendString = "<SIO>SLOTSocketString\n";
	tempSendArray.Add(sendString);
	sendString.Format("<SAI>%d\n",startNum);
	tempSendArray.Add(sendString);
	sendString.Format("<SAI>%d\n",parentSideOffset);
	tempSendArray.Add(sendString);
	for (i = 0; i < tempTreeArray.GetSize(); i++) {
		sendString = "<SAI>" + tempTreeArray[i] + "\n";
		tempSendArray.Add(sendString);
	}
	sendString = "<EIO>SLOTSocketString\n";
	tempSendArray.Add(sendString);

	/* brd - temporary for testing
	forteService.SendToForteConnection(tempSendArray,tempRecvArray,
		CString("SLOTSocketString"), 3020);

	for (i=0; i < tempRecvArray.GetSize(); i++) {
		if ( tempRecvArray[i].Find("<SAI>") != -1 ) {
			tempString = tempRecvArray[i].Mid(5);
			tempInt = tempString.GetLength();
			if (tempString.GetAt(tempInt-1) == '\n' ) {
				tempString.SetAt(tempInt-1,' ');
				tempString.TrimRight();
			}
		}
	}
	*/
	tempString = "Success";		// brd


	if ( tempString == "Success" ) {
		//AfxMessageBox("Building from stream");
		if ( BuildChangesTree(changesTree,startNum) == 0 ) {
//			TreeElement tempElement = changesTree.treeChildren[parentSectionIndex].
//									 treeChildren[parentAisleIndex].
//									 treeChildren[parentSideIndex].
//									 treeChildren[changesTree.treeChildren[parentSectionIndex].treeChildren[parentAisleIndex].treeChildren[parentSideIndex].treeChildren.GetSize()-1];
//			changesTree.treeChildren[parentSectionIndex].
//									 treeChildren[parentAisleIndex].
//									 treeChildren[parentSideIndex].
//									 treeChildren.RemoveAt(changesTree.treeChildren[parentSectionIndex].treeChildren[parentAisleIndex].treeChildren[parentSideIndex].treeChildren.GetSize()-1);
//
//			changesTree.treeChildren[parentSectionIndex].
//						 treeChildren[parentAisleIndex].
//						 treeChildren[parentSideIndex].treeChildren.InsertAt(bayIndex,tempElement);
			return 0;
		}
		else
			return -1;
	}
	else
		return -1;

#else
	CListstringPtr tempTreeArray1(new CListstring);
	for (int i=0;i < tempTreeArray.GetCount();i++)
	{
		CStringArray temp;
		string tmpStr = (LPCTSTR) tempTreeArray[i];
		tempTreeArray1->AddTail( tmpStr );
	}

	///	OLD CODE: SessionMgrSO.AddBranchToTreeHelper(startNum, tempTreeArray1, atoi((LPCTSTR)tempTreeArray[2]));
	
	if (getSessionMgrSO()->AddBranchToTreeHelper(startNum, tempTreeArray1, parentSideOffset) == false)
		MessageBox(0,"AddBranchToTreeHelper","",0);
		if ( BuildChangesTree(changesTree,startNum) == 0 )
			return 0;
		else
			return -1;
#endif
}


//////////////////////////////////////////////////////////////////////
// Function Name : UpdateLocationDescription
// Classname : None
// Description : Update the location btree entry with the new desc
// Date Created : 10/01/98
// Author : wbh
//////////////////////////////////////////////////////////////////////
// Inputs : fileoffset, description
// Outputs : success code
// Explanation : 
//   change the btree to reflect the new location description.  Assume
//   the btree file is open.
//////////////////////////////////////////////////////////////////////
// Algorithm : None
//////////////////////////////////////////////////////////////////////
// Revisions : 
//   Date-initials : Description_of_revision
//////////////////////////////////////////////////////////////////////
	/*******************************************************
	*
	*                       NOTICE
	*
	*  THIS SOFTWARE IS THE PROPERTY OF AND CONTAINS
	*  CONFIDENTIAL INFORMATION OF SSA GLOBAL 
	*  TECHNOLOGIES, INC., AND SHALL NOT BE COPIED,
	*  USED, NOR DISCLOSED WITHOUT EXPRESS WRITTEN
	*  AUTHORIZATION.  ALTHOUGH PUBLICATION IS NOT
	*  INTENDED, IN THE EVENT OF PUBLICATION, THE
	*  FOLLOWING NOTICE IS APPLICABLE:
	*
	*  (c) COPYRIGHT 2004 SSA GLOBAL TECHNOLOGIES, INC.
	*
	*           SSA GLOBAL TECHNOLOGIES, INC.
	*
	********************************************************/
//////////////////////////////////////////////////////////////////////
void CBTreeHelper::UpdateLocationDescription(int storeNum, CString desc) 
{

	qqhSLOTLocation tempLocation;
	CString forteRoot = getenv("TEMP");
	char charBuf[100];
	int tempReturn = -2;
	int tempLength;
	int aMultiplier = 1;
	char *buf = NULL;
	CString tempString;
	CSsaStringArray tempArray;
	int parentID= 0;
	int h;
	CUtilityHelper utilityHelper;

	forteRoot += "\\tempfile.btd";
	tempLength = 1024 * aMultiplier;
	buf = (char *) (calloc(tempLength,sizeof(char)));

	/////////////////////////////////////////////////////////////
	//  If we do not prepare enough buffer for the read,
	//  the function will return a -2 code.  We then increase
	//  the size of our buffer.  We assume that we will receive a
	//  -2 at first.
	/////////////////////////////////////////////////////////////
	while ( tempReturn == -2 ) {
		tempLength = aMultiplier * 1024;
		tempReturn = retObjStream(storeNum,buf,&tempLength);
		if ( tempReturn == -2 ) {
			free(buf);
			aMultiplier++;
			tempLength = 1024 * aMultiplier;
			buf = (char *)(calloc(tempLength,sizeof(char)));
		}
	}
	/////////////////////////////////////////////////////////////
	//  Successful read.  We need to process the object
	/////////////////////////////////////////////////////////////
	if ( tempReturn == 0 ) {
		tempArray.RemoveAll();
		/////////////////////////////////////////////////////////////
		//  Parse out the buffer read into an array of strings
		/////////////////////////////////////////////////////////////
		tempString = buf;
		utilityHelper.BuildArrayofStrings(tempArray,tempString);
		parentID = atoi(tempArray[0].Mid(5));
		if ( tempArray[1].Find(LocationBegSearch) != -1 ) {
			tempLocation.BuildFromStream(tempArray);
			//sprintf(charBuf,"%d",desc);

			// Un-numbered locations start out as inactive so activate it when they 
			// add a pick path
			if (tempLocation.getDescription() == "New Location")
				tempLocation.setIsActive(1);

			tempLocation.setDescription(desc);

			// todo: only update these when specified
			tempLocation.setSelectionSequence(desc);
			tempLocation.setReplenishmentSequence(desc);
			
			tempArray.RemoveAll();
			tempLocation.setIsChanged(CString("TRUE"));
			tempLocation.StreamAttributes(tempArray);
			sprintf(charBuf,"%d",parentID);
			tempString = "<PID>" + CString(charBuf) + CString("\n");
			for(h = 0; h < tempArray.GetSize(); h++)
				tempString += tempArray[h];
			tempLength = tempString.GetLength();
			//printf("------\n%s",tempString);
			if ( storeObjStream(storeNum, tempString.GetBuffer(0),tempLength) != 0 ) return;
			tempString.ReleaseBuffer();
		}
	}
	free(buf);
	return;
}



int CBTreeHelper::OpenBTree()
{
	CString forteRoot = getenv("TEMP");
	
	forteRoot += "\\tempfile.btd";
	int openRet = open_datastore(forteRoot.GetBuffer(0));
	forteRoot.ReleaseBuffer();
	if (openRet != 0)
		return openRet;

	return 0;

}


int CBTreeHelper::CloseBTree()
{
	finalize_datastore();

	return 0;

}

CString CBTreeHelper::OpenFacilityTree( const int facility_id, TreeElement & changesTree, int *numBays ) 
{

	qqhSLOTFacility tempFac;
	CSsaStringArray tempSendArray;
	CSsaStringArray tempRecvArray;
	CSsaStringArray tempResArray;
	CString sendString;
	CString tempString;

	/////////////////////////////////////////////////////////////
	// build the data to send to the Forte connection server
	/////////////////////////////////////////////////////////////
	CString forteRoot = getenv("TEMP");
	forteRoot += "\\tempfile.btd";

#if 0
	int tempInt;

	sendString.Format("<SIO>SLOTFacility\n");
	tempSendArray.Add(sendString);
	sendString.Format("<SAI>REG|DBID|%d\n",facility_id);
	tempSendArray.Add(sendString);
	sendString.Format("<EIO>SLOTFacility\n");
	tempSendArray.Add(sendString);
	forteService.SendToForteConnection(tempSendArray,tempRecvArray,
		CString("SLOTFacility"), 2010);

    for (int i=0; i < tempRecvArray.GetSize(); i++) {
		if ( tempRecvArray[i].Find("<SAI>") != -1 ) {
			tempString = tempRecvArray[i].Mid(5);
			tempResArray.Add(tempString);
			tempInt = tempString.GetLength();
			if (tempString.GetAt(tempInt-1) == '\n' ) {
				tempString.SetAt(tempInt-1,' ');
				tempString.TrimRight();
			}
		}
	}
#else
	///OLD CODE: string tempCadFileName = SessionMgrSO.OpenFacility(facility_id, retValTmp, numBaysTmp);

	SLOTSessionMgr *ptr = getSessionMgrSO();
	bool retValTmp;
	typTextDataPtr tempCadFileName = ptr->OpenFacility(facility_id, retValTmp, *numBays);

/*	bool *retValTmp = new bool;
	int *numBaysTmp = new int;
	int *facility_idTmp = new int;
	*facility_idTmp = facility_id;
	// string *tempCadFileName = ptr->OpenFacility(*facility_idTmp, *retValTmp, *numBaysTmp);
	delete retValTmp;
	delete numBaysTmp;*/

/*	if ( tempResArray[0].Find("Opened By") != -1 )
		return tempResArray[0];	*/

	if (tempCadFileName->Value.Find("Opened By") != -1 )
		return tempCadFileName->Value;

	storeNextNumber = 1;
//	*numBays = atoi(tempResArray[1].GetBuffer(0));
//	tempResArray[1].ReleaseBuffer();
	changesTree.treeChildren.RemoveAll();
	numItemsProcessed = 0;
	if ( BuildChangesTree(changesTree,storeNextNumber) == 0 )
	{
		return tempCadFileName->Value;		// CHECK - COMMENTED this line	 - return tempResArray[0];
	}
	else
	{
		return ("NO FILE");
	}
#endif

}

int CBTreeHelper::SaveFacility(TreeElement & changesTree, int saveAs, const CString& saveAsOptions) 
{
	
	CString deleteList;
	int ret;

	if (saveAsOptions.Find("DrawingOnly") < 0) {
		SendAislesToForte(changesTree);
		
		GetDeletedElementList(changesTree, deleteList);
	}

#if 0
	CString sendString;
	CSsaStringArray tempSendArray;
	CSsaStringArray tempRecvArray;
	CString tempString;

	int i;
	int tempInt;

	sendString = "<SIO>SLOTSocketString\n";
	tempSendArray.Add(sendString);
	sendString.Format("<SAI>%d\n",saveAs);
	tempSendArray.Add(sendString);
	sendString.Format("<SAI>%s\n", deleteList);
	tempSendArray.Add(sendString);
	sendString.Format("<SAI>%s\n", saveAsOptions);
	tempSendArray.Add(sendString);
	sendString = "<EIO>SLOTSocketString\n";
	tempSendArray.Add(sendString);

	forteService.SendToForteConnection(tempSendArray,tempRecvArray,
		CString("SLOTSocketString"), 2060);

	for (i=0; i < tempRecvArray.GetSize(); i++) {
		if ( tempRecvArray[i].Find("<SAI>") != -1 ) {
			tempString = tempRecvArray[i].Mid(5);
			tempInt = tempString.GetLength();
			if (tempString.GetAt(tempInt-1) == '\n' ) {
				tempString.SetAt(tempInt-1,' ');
				tempString.TrimRight();
			}
		}
	}
#else
	///OLD CODE: SessionMgrSO.SaveFacilityHelper(saveAs, (LPCTSTR)deleteList, (LPCTSTR)saveAsOptions);
	if (saveAs)
		ret = getSessionMgrSO()->SaveFacilityHelper2(true, deleteList.GetBuffer(), (LPCTSTR)saveAsOptions);
	else
		ret = getSessionMgrSO()->SaveFacilityHelper2(false, deleteList.GetBuffer(), (LPCTSTR)saveAsOptions);
#endif

	if (saveAsOptions.Find("DrawingOnly") < 0) {
		changesTree.clear();
		
		storeNextNumber = 1;
		if ( BuildChangesTree(changesTree,storeNextNumber) != 0 )
			return -1;
		numItemsProcessed = 0;
	}
	
	return ret;
}

int CBTreeHelper::DeleteFacility(int facilityID) 
{
#if 0
	CString sendString;
	CSsaStringArray tempSendArray;
	CSsaStringArray tempRecvArray;
	int i;
	CString tempString;
	int tempInt;

	sendString = "<SIO>SLOTSocketString\n";
	tempSendArray.Add(sendString);
	sendString.Format("<SAI>%d\n",facilityID);
	tempSendArray.Add(sendString);
	sendString = "<EIO>SLOTSocketString\n";
	tempSendArray.Add(sendString);

	forteService.SendToForteConnection(tempSendArray,tempRecvArray,
		CString("SLOTSocketString"), 9080);

	for (i=0; i < tempRecvArray.GetSize(); i++) {
		if ( tempRecvArray[i].Find("<SAI>") != -1 ) {
			tempString = tempRecvArray[i].Mid(5);
			tempInt = tempString.GetLength();
			if (tempString.GetAt(tempInt-1) == '\n' ) {
				tempString.SetAt(tempInt-1,' ');
				tempString.TrimRight();
			}
		}
	}
#else
	getSessionMgrSO()->DeleteFacilityHelper(facilityID);
#endif

	return 0;
}


int CBTreeHelper::ReadNoOpenBtAisle(int fileOffset, qqhSLOTAisle &tempAisle) 
{
	CString forteRoot = getenv("TEMP");
	int tempReturn = -2;
	int tempLength;
	int aMultiplier = 1;
	char *buf = NULL;
	CString tempString;
	CSsaStringArray tempArray;
	int parentID= 0;
	CUtilityHelper utilityHelper;

	tempLength = 1024 * aMultiplier;
	buf = (char *) (calloc(tempLength,sizeof(char)));

	/////////////////////////////////////////////////////////////
	//  If we do not prepare enough buffer for the read,
	//  the function will return a -2 code.  We then increase
	//  the size of our buffer.  We assume that we will receive a
	//  -2 at first.
	/////////////////////////////////////////////////////////////
	while ( tempReturn == -2 ) {
		tempLength = aMultiplier * 1024;
		tempReturn = retObjStream(fileOffset,buf,&tempLength);
		if ( tempReturn == -2 ) {
			free(buf);
			aMultiplier++;
			tempLength = 1024 * aMultiplier;
			buf = (char *)(calloc(tempLength,sizeof(char)));
		}
	}
	/////////////////////////////////////////////////////////////
	//  Successful read.  We need to process the object
	/////////////////////////////////////////////////////////////
	if ( tempReturn == 0 ) {
		tempArray.RemoveAll();
		/////////////////////////////////////////////////////////////
		//  Parse out the buffer read into an array of strings
		/////////////////////////////////////////////////////////////
		tempString = buf;
		printf("%s\n",tempString);
		utilityHelper.BuildArrayofStrings(tempArray,tempString);
		parentID = atoi(tempArray[0].Mid(5));
		if ( tempArray[1].Find(AisleBegSearch) != -1 ) {
			tempAisle.BuildFromStream(tempArray);
		}
	}
	free(buf);
	return tempReturn;
}


int CBTreeHelper::ReadNoOpenBtBay(int fileOffset, qqhSLOTBay &tempBay) 
{
	CString forteRoot = getenv("TEMP");
	int tempReturn = -2;
	int tempLength;
	int aMultiplier = 1;
	char *buf = NULL;
	CString tempString;
	CSsaStringArray tempArray;
	int parentID= 0;
	CUtilityHelper utilityHelper;

	tempLength = 1024 * aMultiplier;
	buf = (char *) (calloc(tempLength,sizeof(char)));

	/////////////////////////////////////////////////////////////
	//  If we do not prepare enough buffer for the read,
	//  the function will return a -2 code.  We then increase
	//  the size of our buffer.  We assume that we will receive a
	//  -2 at first.
	/////////////////////////////////////////////////////////////
	while ( tempReturn == -2 ) {
		tempLength = aMultiplier * 1024;
		tempReturn = retObjStream(fileOffset,buf,&tempLength);
		if ( tempReturn == -2 ) {
			free(buf);
			aMultiplier++;
			tempLength = 1024 * aMultiplier;
			buf = (char *)(calloc(tempLength,sizeof(char)));
		}
	}
	/////////////////////////////////////////////////////////////
	//  Successful read.  We need to process the object
	/////////////////////////////////////////////////////////////
	if ( tempReturn == 0 ) {
		tempArray.RemoveAll();
		/////////////////////////////////////////////////////////////
		//  Parse out the buffer read into an array of strings
		/////////////////////////////////////////////////////////////
		tempString = buf;
		printf("%s\n",tempString);
		utilityHelper.BuildArrayofStrings(tempArray,tempString);
		parentID = atoi(tempArray[0].Mid(5));
		if ( tempArray[1].Find(BayBegSearch) != -1 ) {
			tempBay.BuildFromStream(tempArray);
		}
	}
	free(buf);
	return tempReturn;
}



int CBTreeHelper::UpdateFacilityElementDBIDs(TreeElement & changesTree) 
{
	int tempReturn = -2;
	int tempLength;
	int aMultiplier = 1;
	char * buf = NULL;
	TreeElement * elementPtr = &(changesTree);
	TreeElement tempTreeElement;
	CString tempString;
	CSsaStringArray tempArray;
	int parentID = 0;
//	int currentPID = 0;
//	int parentIndex = 0;
	qqhSLOTFacility tempFac;
	qqhSLOTSection tempSec;
	qqhSLOTAisle tempAisle;
	qqhSLOTSide tempSide;
	qqhSLOTBay	tempBay;
	qqhSLOTLevel tempLevel;
	qqhSLOTLocation tempLocation;
//	int complete = -1;
	int i,j,k,l,m,n;
	int nextNum = 1;
	int maxNum;
	int foundDBID;
	CUtilityHelper utilityHelper;

	maxNum = 1;
	for ( i = 0; i < changesTree.treeChildren.GetSize(); i++) {
		maxNum = changesTree.treeChildren[i].fileOffset;
		for ( j = 0; j < changesTree.treeChildren[i].treeChildren.GetSize(); j++) {
			maxNum = changesTree.treeChildren[i].treeChildren[j].fileOffset;
			for ( k = 0; k < changesTree.treeChildren[i].treeChildren[j].treeChildren.GetSize(); k++) {
				maxNum = changesTree.treeChildren[i].treeChildren[j].treeChildren[k].fileOffset;
				for ( l = 0; l < changesTree.treeChildren[i].treeChildren[j].treeChildren[k].treeChildren.GetSize(); l++) {
					maxNum = changesTree.treeChildren[i].treeChildren[j].treeChildren[k].treeChildren[l].fileOffset;
					for ( m = 0; m < changesTree.treeChildren[i].treeChildren[j].treeChildren[k].treeChildren[l].treeChildren.GetSize(); m++) {
						maxNum = changesTree.treeChildren[i].treeChildren[j].treeChildren[k].treeChildren[l].treeChildren[m].fileOffset;
						for ( n = 0; n < changesTree.treeChildren[i].treeChildren[j].treeChildren[k].treeChildren[l].treeChildren[m].treeChildren.GetSize(); n++)
							maxNum = changesTree.treeChildren[i].treeChildren[j].treeChildren[k].treeChildren[l].treeChildren[m].treeChildren[n].fileOffset;
					}
				}
			}
		}
	}
	
	CString forteRoot = getenv("TEMP");
	forteRoot += "\\tempfile.btd";
	int openRet = open_datastore(forteRoot.GetBuffer(0));
	forteRoot.ReleaseBuffer();
	if (openRet != 0)
		return -1;

	tempLength = 1024 * aMultiplier;
	buf = (char *) (calloc(tempLength,sizeof(char)));
	/////////////////////////////////////////////////////////////
	//  Read items 1 to n until we cannot find item "n"
	/////////////////////////////////////////////////////////////
	while ( nextNum <= maxNum) {
		/////////////////////////////////////////////////////////////
		//  If we do not prepare enough buffer for the read,
		//  the function will return a -2 code.  We then increase
		//  the size of our buffer.  We assume that we will receive a
		//  -2 at first.
		/////////////////////////////////////////////////////////////
		while ( tempReturn == -2 ) {
			tempLength = aMultiplier * 1024;
			tempReturn = retObjStream(nextNum,buf,&tempLength);
			if ( tempReturn == -2 ) {
				free(buf);
				aMultiplier++;
				tempLength = 1024 * aMultiplier;
				buf = (char *)(calloc(tempLength,sizeof(char)));
			}
		}
		/////////////////////////////////////////////////////////////
		//  Successful read.  We need to process the object
		/////////////////////////////////////////////////////////////
		if ( tempReturn == 0 ) {
			tempString = buf;
			tempArray.RemoveAll();
			/////////////////////////////////////////////////////////////
			//  Parse out the buffer read into an array of strings
			/////////////////////////////////////////////////////////////
			utilityHelper.BuildArrayofStrings(tempArray,tempString);

			/////////////////////////////////////////////////////////////
			// Get the parent offset number of this object
			/////////////////////////////////////////////////////////////
			parentID = atoi(tempArray[0].Mid(5));
			//tempTreeElement = new TreeElement();
			/////////////////////////////////////////////////////////////
			// Determine the type of facility element (facility, section,
			// etc., and build a temporary object from the stream to
			// get the necessary attributes.
			/////////////////////////////////////////////////////////////
			if (tempArray[1].Find(FacilityBegSearch) != -1 ) {
				tempFac.BuildFromStream(tempArray);
				changesTree.elementDBID = tempFac.getDBID();
			}
			else if ( tempArray[1].Find(SectionBegSearch) != -1 ) {
				tempSec.BuildFromStream(tempArray);
				foundDBID = 0;
				for ( i = 0; i < changesTree.treeChildren.GetSize() && foundDBID == 0; i++) {
					if ( changesTree.treeChildren[i].fileOffset == nextNum ) {
						changesTree.treeChildren[i].elementDBID = tempSec.getDBID();
						foundDBID = 1;
					}
				}
			}
			else if ( tempArray[1].Find(AisleBegSearch) != -1 ) {
				tempAisle.BuildFromStream(tempArray);
				foundDBID = 0;
				for ( i = 0; i < changesTree.treeChildren.GetSize() && foundDBID == 0; i++) {
					for ( j = 0; j < changesTree.treeChildren[i].treeChildren.GetSize() && foundDBID == 0; j++) {
						if ( changesTree.treeChildren[i].treeChildren[j].fileOffset == nextNum ) {
							changesTree.treeChildren[i].treeChildren[j].elementDBID = tempAisle.getDBID();
							foundDBID = 1;
						}
					}
				}
			}
			else if ( tempArray[1].Find(SideBegSearch) != -1 ) {
				tempSide.BuildFromStream(tempArray);
				foundDBID = 0;
				for ( i = 0; i < changesTree.treeChildren.GetSize() && foundDBID == 0; i++) {
					for ( j = 0; j < changesTree.treeChildren[i].treeChildren.GetSize() && foundDBID == 0; j++) {
						for ( k = 0; k < changesTree.treeChildren[i].treeChildren[j].treeChildren.GetSize() && foundDBID == 0; k++) {
							if ( changesTree.treeChildren[i].treeChildren[j].treeChildren[k].fileOffset == nextNum ) {
								changesTree.treeChildren[i].treeChildren[j].treeChildren[k].elementDBID = tempSide.getDBID();
								foundDBID = 1;
							}
						}
					}
				}
			}
			else if ( tempArray[1].Find(BayBegSearch) != -1 ) {
				tempBay.BuildFromStream(tempArray);
				foundDBID = 0;
				for ( i = 0; i < changesTree.treeChildren.GetSize() && foundDBID == 0; i++) {
					for ( j = 0; j < changesTree.treeChildren[i].treeChildren.GetSize() && foundDBID == 0; j++) {
						for ( k = 0; k < changesTree.treeChildren[i].treeChildren[j].treeChildren.GetSize() && foundDBID == 0; k++) {
							for ( l = 0; l < changesTree.treeChildren[i].treeChildren[j].treeChildren[k].treeChildren.GetSize() && foundDBID == 0; l++) {
								if ( changesTree.treeChildren[i].treeChildren[j].treeChildren[k].treeChildren[l].fileOffset == nextNum ) {
									changesTree.treeChildren[i].treeChildren[j].treeChildren[k].treeChildren[l].elementDBID = tempBay.getDBID();
									foundDBID = 1;
								}
							}
						}
					}
				}
			}
			else if ( tempArray[1].Find(LevelBegSearch) != -1 ) {
				tempLevel.BuildFromStream(tempArray);
				foundDBID = 0;
				for ( i = 0; i < changesTree.treeChildren.GetSize() && foundDBID == 0; i++) {
					for ( j = 0; j < changesTree.treeChildren[i].treeChildren.GetSize() && foundDBID == 0; j++) {
						for ( k = 0; k < changesTree.treeChildren[i].treeChildren[j].treeChildren.GetSize() && foundDBID == 0; k++) {
							for ( l = 0; l < changesTree.treeChildren[i].treeChildren[j].treeChildren[k].treeChildren.GetSize() && foundDBID == 0; l++) {
								for ( m = 0; m < changesTree.treeChildren[i].treeChildren[j].treeChildren[k].treeChildren[l].treeChildren.GetSize() && foundDBID == 0; m++) {
									if ( changesTree.treeChildren[i].treeChildren[j].treeChildren[k].treeChildren[l].treeChildren[m].fileOffset == nextNum ) {
										changesTree.treeChildren[i].treeChildren[j].treeChildren[k].treeChildren[l].treeChildren[m].elementDBID = tempLevel.getDBID();
										foundDBID = 1;
									}
								}
							}
						}
					}
				}
			}
			else if ( tempArray[1].Find(LocationBegSearch) != -1 ) {
				tempLocation.BuildFromStream(tempArray);
				foundDBID = 0;
				for ( i = 0; i < changesTree.treeChildren.GetSize() && foundDBID == 0; i++) {
					for ( j = 0; j < changesTree.treeChildren[i].treeChildren.GetSize() && foundDBID == 0; j++) {
						for ( k = 0; k < changesTree.treeChildren[i].treeChildren[j].treeChildren.GetSize() && foundDBID == 0; k++) {
							for ( l = 0; l < changesTree.treeChildren[i].treeChildren[j].treeChildren[k].treeChildren.GetSize() && foundDBID == 0; l++) {
								for ( m = 0; m < changesTree.treeChildren[i].treeChildren[j].treeChildren[k].treeChildren[l].treeChildren.GetSize() && foundDBID == 0; m++) {
									for ( n = 0; n < changesTree.treeChildren[i].treeChildren[j].treeChildren[k].treeChildren[l].treeChildren[m].treeChildren.GetSize() && foundDBID == 0; n++) {
										if ( changesTree.treeChildren[i].treeChildren[j].treeChildren[k].treeChildren[l].treeChildren[m].treeChildren[n].fileOffset == nextNum ) {
											changesTree.treeChildren[i].treeChildren[j].treeChildren[k].treeChildren[l].treeChildren[m].treeChildren[n].elementDBID = tempLocation.getDBID();
											foundDBID = 1;
										}
									}
								}
							}
						}
					}
				}
			}
			else
				printf("%s\n",tempArray[1]);

			nextNum = nextNum + 1;
		}
	}
	finalize_datastore();
	free(buf);
	elementPtr = NULL;
	return 0;
}

int CBTreeHelper::UnlockFacility( const int facility_id ) 
{

#if 0
	CSsaStringArray tempSendArray;
	CSsaStringArray tempRecvArray;
	CString sendString;
	CString tempString;
	int tempInt;

	/////////////////////////////////////////////////////////////
	// build the data to send to the Forte connection server
	/////////////////////////////////////////////////////////////
	sendString.Format("<SIO>SLOTFacility\n");
	tempSendArray.Add(sendString);
	sendString.Format("<SAI>REG|DBID|%d\n",facility_id);
	tempSendArray.Add(sendString);
	sendString.Format("<EIO>SLOTFacility\n");
	tempSendArray.Add(sendString);

	forteService.SendToForteConnection(tempSendArray,tempRecvArray,
		CString("SLOTFacility"), 5070);

	for (int i=0; i < tempRecvArray.GetSize(); i++) {
		if ( tempRecvArray[i].Find("<SAI>") != -1 ) {
			tempString = tempRecvArray[i].Mid(5);
			tempInt = tempString.GetLength();
			if (tempString.GetAt(tempInt-1) == '\n' ) {
				tempString.SetAt(tempInt-1,' ');
				tempString.TrimRight();
			}
		}
	}
	
	if ( tempString == "Success")
		return 0;
	else
		return -1;
#endif
	return (getSessionMgrSO()->UnlockFacility(facility_id));
}

int CBTreeHelper::UpdateFacilityNotes(CString notes) 
{
	int i;
	CString forteRoot = getenv("TEMP");
	int tempReturn = -2;
	int tempLength;
	int aMultiplier = 1;
	char *buf = NULL;
	CString tempString;
	CSsaStringArray tempArray;
	int parentID= 0;
	qqhSLOTFacility tempFacility;
	CUtilityHelper utilityHelper;

	forteRoot += "\\tempfile.btd";
	int openRet = open_datastore(forteRoot.GetBuffer(0));
	forteRoot.ReleaseBuffer(0);
	if (openRet != 0)
		return -1;
	tempLength = 1024 * aMultiplier;
	buf = (char *) (calloc(tempLength,sizeof(char)));

	/////////////////////////////////////////////////////////////
	//  If we do not prepare enough buffer for the read,
	//  the function will return a -2 code.  We then increase
	//  the size of our buffer.  We assume that we will receive a
	//  -2 at first.
	/////////////////////////////////////////////////////////////
	while ( tempReturn == -2 ) {
		tempLength = aMultiplier * 1024;
		tempReturn = retObjStream(1,buf,&tempLength);
		if ( tempReturn == -2 ) {
			free(buf);
			aMultiplier++;
			tempLength = 1024 * aMultiplier;
			buf = (char *)(calloc(tempLength,sizeof(char)));
		}
	}
	/////////////////////////////////////////////////////////////
	//  Successful read.  We need to process the object
	/////////////////////////////////////////////////////////////
	if ( tempReturn == 0 ) {
		tempArray.RemoveAll();
		/////////////////////////////////////////////////////////////
		//  Parse out the buffer read into an array of strings
		/////////////////////////////////////////////////////////////
		tempString = buf;
		printf("%s\n",tempString);
		utilityHelper.BuildArrayofStrings(tempArray,tempString);
		parentID = atoi(tempArray[0].Mid(5));
		if ( tempArray[1].Find(FacilityBegSearch) != -1 ) {
			tempFacility.BuildFromStream(tempArray);
			tempFacility.setNotes(notes);
		}
		tempArray.RemoveAll();
		tempFacility.setIsChanged(CString("TRUE"));
		tempFacility.StreamAttributes(tempArray);
		tempString = "<PID>0\n";
		for(i = 0; i < tempArray.GetSize(); i++) {
			printf("%s",tempArray[i]);
			tempString += tempArray[i];
		}
		tempLength = tempString.GetLength();
//		printf("%s\n",tempString);
		tempReturn = storeObjStream(1, tempString.GetBuffer(0),tempLength);
		tempString.ReleaseBuffer();
		if ( tempReturn != 0 )
			printf("Error writing new filename\n");
	}
	finalize_datastore();
	free(buf);
	return tempReturn;
}

void CBTreeHelper::SendAislesToForte(TreeElement &changesTree)
{
//	CSsaStringArray tempArray;
	CString tempString,tempTreeString;
	int g, h,i,j,k,l;
	CListstringPtr tmpTreeOrderTypes(new CListstring);

#if 0
	CString sendString;
	CSsaStringArray tempSendArray;
	CSsaStringArray tempRecvArray;
	int tempInt;
#endif

	CSsaStringArray tempTreeArray;
	int startNum, parentSectionOffset;

	TreeElement *sectionPtr, *aislePtr, *sidePtr, *bayPtr, *levelPtr, *locPtr;

	// for each section
	for (g = 0; g < changesTree.treeChildren.GetSize(); ++g) {
		sectionPtr = &changesTree.treeChildren[g];

		startNum = sectionPtr->fileOffset;
		parentSectionOffset = sectionPtr->fileOffset;
		tempTreeArray.RemoveAll();

		for (h=0; h < sectionPtr->treeChildren.GetSize(); ++h) {
			aislePtr = &sectionPtr->treeChildren[h];

			tempTreeString.Format("%s|%d|%d", "SLOTAisle", aislePtr->fileOffset, aislePtr->elementDBID);
			tempTreeArray.Add(tempTreeString);

			for ( i = 0; i < aislePtr->treeChildren.GetSize(); i++) {
				sidePtr = &aislePtr->treeChildren[i];

				tempTreeString.Format("%s|%d|%d", "SLOTSide", sidePtr->fileOffset, sidePtr->elementDBID);
				tempTreeArray.Add(tempTreeString);

				for ( j = 0; j < sidePtr->treeChildren.GetSize(); j++) {
					bayPtr = &sidePtr->treeChildren[j];
					tempTreeString.Format("%s|%d|%d", "SLOTBay", bayPtr->fileOffset, bayPtr->elementDBID);
					tempTreeArray.Add(tempTreeString);

					for ( k = 0; k < bayPtr->treeChildren.GetSize(); k++) {
						levelPtr = &bayPtr->treeChildren[k];
						tempTreeString.Format("%s|%d|%d", "SLOTLevel", levelPtr->fileOffset, levelPtr->elementDBID);
						tempTreeArray.Add(tempTreeString);
						
						for ( l = 0; l <  levelPtr->treeChildren.GetSize(); l++) {
							locPtr = &levelPtr->treeChildren[l];
							tempTreeString.Format("%s|%d|%d", "SLOTLocation", locPtr->fileOffset, locPtr->elementDBID);
							tempTreeArray.Add(tempTreeString);
						}
					}
				}
			}
		}
		
		if (tempTreeArray.GetSize() == 0)
			continue;

#if 0
		tempSendArray.RemoveAll();
		tempRecvArray.RemoveAll();

		sendString = "<SIO>SLOTSocketString\n";
		tempSendArray.Add(sendString);
		sendString.Format("<SAI>%d\n",startNum);
		tempSendArray.Add(sendString);
		sendString.Format("<SAI>%d\n",parentSectionOffset);
		tempSendArray.Add(sendString);
		sendString.Format("<SAI>%d\n",storeNextNumber);
		tempSendArray.Add(sendString);

		for (i = 0; i < tempTreeArray.GetSize(); i++) {
			sendString = "<SAI>" + tempTreeArray[i] + "\n";
			tempSendArray.Add(sendString);
		}
		sendString = "<EIO>SLOTSocketString\n";
		tempSendArray.Add(sendString);
		
		forteService.SendToForteConnection(tempSendArray,tempRecvArray,
			CString("SLOTSocketString"), 3025);

		
		for (i=0; i < tempRecvArray.GetSize(); i++) {
			if ( tempRecvArray[i].Find("<SAI>") != -1 ) {
				tempString = tempRecvArray[i].Mid(5);
				tempInt = tempString.GetLength();
				if (tempString.GetAt(tempInt-1) == '\n' ) {
					tempString.SetAt(tempInt-1,' ');
					tempString.TrimRight();
				}
			}
		}
#else
		///tmpTreeOrderTypes->RemoveAll();
	for (i = 0; i < tempTreeArray.GetSize(); i++) {
			string TreeArrayElementTmp = (LPCTSTR)tempTreeArray[i];
			tmpTreeOrderTypes->AddTail(TreeArrayElementTmp);
		}	
		getSessionMgrSO()->AddBranchToTree2Helper(startNum,tmpTreeOrderTypes,parentSectionOffset,storeNextNumber);
		tmpTreeOrderTypes->RemoveAll(); //Naveen 13Feb06 cleanup
#endif

	}
}

int CBTreeHelper::GetDeletedElementList(TreeElement &changesTree, CString &deleteList)
{
	int key, dbid;
	CString sectionList, aisleList, sideList, bayList;
	CString temp;
		
	POSITION pos = changesTree.DeletedSectionMap.GetStartPosition();
	while (pos != NULL) {
		changesTree.DeletedSectionMap.GetNextAssoc(pos, key, dbid);

		temp.Format("%d", dbid);
		sectionList += temp;
		sectionList += ",";

	}

	pos = changesTree.DeletedAisleMap.GetStartPosition();
	while (pos != NULL) {
		changesTree.DeletedAisleMap.GetNextAssoc(pos, key, dbid);
	
		temp.Format("%d", dbid);
		aisleList += temp;
		aisleList += ",";
	}

	pos = changesTree.DeletedSideMap.GetStartPosition();
	while (pos != NULL) {
		changesTree.DeletedSideMap.GetNextAssoc(pos, key, dbid);
		temp.Format("%d", dbid);
		sideList += temp;
		sideList += ",";
	}

	pos = changesTree.DeletedBayMap.GetStartPosition();
	while (pos != NULL) {
		changesTree.DeletedBayMap.GetNextAssoc(pos, key, dbid);

		temp.Format("%d", dbid);
		bayList += temp;
		bayList += ",";
	}

	
	deleteList.Format("%s|%s|%s|%s", sectionList, aisleList, sideList, bayList);

	return 0;

}

// Hotspot.h: interface for the CHotspot class.
//
//////////////////////////////////////////////////////////////////////

#if !defined(AFX_HOTSPOT_H__F8CC04C3_ACE7_4BA6_A478_316E1C085713__INCLUDED_)
#define AFX_HOTSPOT_H__F8CC04C3_ACE7_4BA6_A478_316E1C085713__INCLUDED_

#if _MSC_VER > 1000
#pragma once
#endif // _MSC_VER > 1000

#include "FacilityElement.h"
#include "3DPoint.h"

class CHotspot : public CFacilityElement  
{
public:
	CHotspot();
	virtual ~CHotspot();
	
	C3DPoint m_Coordinates;
	int m_Type;
	typedef enum {
		hsPutaway = 1,
		hsSelection = 2
	} hsTypeEnum;

};

#endif // !defined(AFX_HOTSPOT_H__F8CC04C3_ACE7_4BA6_A478_316E1C085713__INCLUDED_)

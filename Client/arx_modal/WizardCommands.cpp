// WizardCommands.cpp: implementation of the CWizardCommands class.
//
//////////////////////////////////////////////////////////////////////

#include "stdafx.h"
#include "modal.h"
#include "WizardCommands.h"
#include "WizardHelper.h"
#include "ControlService.h"

#ifdef _DEBUG
#undef THIS_FILE
static char THIS_FILE[]=__FILE__;
#define new DEBUG_NEW
#endif

extern CControlService controlService;

//////////////////////////////////////////////////////////////////////
// Construction/Destruction
//////////////////////////////////////////////////////////////////////

CWizardCommands::CWizardCommands()
{

}

CWizardCommands::~CWizardCommands()
{

}

void CWizardCommands::RegisterCommands()
{
	acedRegCmds->addCommand("SLOTWIZ", "NEWBAY", "NEWBAY",
		ACRX_CMD_MODAL, &CWizardCommands::BayWizard);

	acedRegCmds->addCommand("SLOTWIZ", "BAYWIZARD", "BAYWIZARD",
		ACRX_CMD_MODAL, &CWizardCommands::BayWizard);

	acedRegCmds->addCommand("SLOTGEN", "BW", "BW",
		ACRX_CMD_MODAL, &CWizardCommands::BayWizard);


	acedRegCmds->addCommand("SLOTWIZ", "NEWSIDE", "NEWSIDE",
		ACRX_CMD_MODAL, &CWizardCommands::SideWizard);

	acedRegCmds->addCommand("SLOTWIZ", "SIDEWIZARD", "SIDEWIZARD",
		ACRX_CMD_MODAL, &CWizardCommands::SideWizard);

	acedRegCmds->addCommand("SLOTWIZ", "SW", "SW",
		ACRX_CMD_MODAL, &CWizardCommands::SideWizard);


	acedRegCmds->addCommand("SLOTWIZ", "AISLEWIZARD", "AISLEWIZARD",
		ACRX_CMD_MODAL, &CWizardCommands::AisleWizard);

	acedRegCmds->addCommand("SLOTWIZ", "AW", "AW",
		ACRX_CMD_MODAL, &CWizardCommands::AisleWizard);



	acedRegCmds->addCommand("SLOTGEN", "PROFILEMAINTENANCE", "PROFILEMAINTENANCE",
		ACRX_CMD_MODAL, &CWizardCommands::ProfileMaintenance);

	
	acedRegCmds->addCommand("SLOTGEN", "VIEWBAYPROFILEDRAWING", "VIEWBAYPROFILEDRAWING",
		ACRX_CMD_MODAL, &CWizardCommands::ViewBayProfileDrawing);

	acedRegCmds->addCommand("SLOTGEN", "VIEWSIDEPROFILEDRAWING", "VIEWSIDEPROFILEDRAWING",
		ACRX_CMD_MODAL, &CWizardCommands::ViewSideProfileDrawing);

	acedRegCmds->addCommand("SLOTGEN", "VIEWAISLEPROFILEDRAWING", "VIEWAISLEPROFILEDRAWING",
		ACRX_CMD_MODAL, &CWizardCommands::ViewAisleProfileDrawing);
}

void CWizardCommands::ProfileMaintenance()
{ 
	if (! controlService.ValidateCommand(CCommands::anyMode))
		return;

	CWizardHelper helper;

	helper.ProfileMaintenance();

	return;
}

void CWizardCommands::BayWizard()
{
	if (! controlService.ValidateCommand(CCommands::anyMode))
		return;

	CWizardHelper helper;

	helper.BayProfileWizard();

	return;
}


void CWizardCommands::ViewBayProfileDrawing()
{
	if (! controlService.ValidateCommand(CCommands::anyMode))
		return;

	CWizardHelper helper;

	helper.ViewBayProfileDrawing();

	return;

}

void CWizardCommands::SideWizard()
{
	if (! controlService.ValidateCommand(CCommands::anyMode))
		return;

	CWizardHelper helper;

	helper.SideProfileWizard();

	return;
}


void CWizardCommands::ViewSideProfileDrawing()
{
	if (! controlService.ValidateCommand(CCommands::anyMode))
		return;

	CWizardHelper helper;

	helper.ViewSideProfileDrawing();

	return;

}

void CWizardCommands::AisleWizard()
{
	if (! controlService.ValidateCommand(CCommands::anyMode))
		return;

	CWizardHelper helper;

	helper.AisleProfileWizard();

	return;
}


void CWizardCommands::ViewAisleProfileDrawing()
{
	if (! controlService.ValidateCommand(CCommands::anyMode))
		return;

	CWizardHelper helper;

	helper.ViewAisleProfileDrawing();

	return;

}
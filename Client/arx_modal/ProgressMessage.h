// ProgressMessage.h: interface for the CProgressMessage class.
//
//////////////////////////////////////////////////////////////////////

#if !defined(AFX_PROGRESSMESSAGE_H__B7AD7BA8_87EE_4C07_85C2_1AB4BEB1F826__INCLUDED_)
#define AFX_PROGRESSMESSAGE_H__B7AD7BA8_87EE_4C07_85C2_1AB4BEB1F826__INCLUDED_

#include "Progress.h"

#if _MSC_VER > 1000
#pragma once
#endif // _MSC_VER > 1000

class CProgressMessage : public CObject  
{
public:
	BOOL IsStopping();
	void Hide();
	void Step();
	void UpdateMessage(const CString &message);

	CProgressMessage();
	CProgressMessage(const CString &message, int lower, int upper, int step, CWnd *pParent);
	CProgressMessage(const CString &message, int lower, int upper, int step, CWnd *pParent, BOOL noStop);
	virtual ~CProgressMessage();
	CProgress *m_ProgressDialog;
};

#endif // !defined(AFX_PROGRESSMESSAGE_H__B7AD7BA8_87EE_4C07_85C2_1AB4BEB1F826__INCLUDED_)

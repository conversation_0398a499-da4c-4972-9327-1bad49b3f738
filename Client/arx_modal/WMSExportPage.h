#if !defined(AFX_WMSEXPORTPAGE_H__C111C157_5ADB_42BE_A421_DE6D1DED1D07__INCLUDED_)
#define AFX_WMSEXPORTPAGE_H__C111C157_5ADB_42BE_A421_DE6D1DED1D07__INCLUDED_

#if _MSC_VER > 1000
#pragma once
#endif // _MSC_VER > 1000
// WMSExportPage.h : header file
//
#include "WMSGroup.h"
/////////////////////////////////////////////////////////////////////////////
// CWMSExportPage dialog

class CWMSExportPage : public CPropertyPage
{
	DECLARE_DYNCREATE(CWMSExportPage)

// Construction
public:
	CWMSExportPage();
	~CWMSExportPage();
	void Reload();
	CTypedPtrArray<CObArray, CWMSMap*> m_ExportMapList;
	int LoadMapList();
// Dialog Data
	//{{AFX_DATA(CWMSExportPage)
	enum { IDD = IDD_WMS_EXPORT };
	CTreeCtrl	m_WMSTreeCtrl;
	CTreeCtrl	m_FacilityTreeCtrl;
	//}}AFX_DATA


// Overrides
	// ClassWizard generate virtual function overrides
	//{{AFX_VIRTUAL(CWMSExportPage)
	protected:
	virtual void DoDataExchange(CDataExchange* pDX);    // DDX/DDV support
	virtual BOOL OnNotify(WPARAM wParam, LPARAM lParam, LRESULT* pResult);
	//}}AFX_VIRTUAL

// Implementation

protected:
	// Generated message map functions
	//{{AFX_MSG(CWMSExportPage)
	virtual BOOL OnInitDialog();
	afx_msg void OnAssign();
	afx_msg void OnRemove();
	afx_msg void OnBegindragFacilityTree(NMHDR* pNMHDR, LRESULT* pResult);
	afx_msg void OnBegindragWmsGroupTree(NMHDR* pNMHDR, LRESULT* pResult);
	afx_msg void OnMouseMove(UINT nFlags, CPoint point);
	afx_msg void OnLButtonUp(UINT nFlags, CPoint point);
	afx_msg void OnTimer(UINT nIDEvent);
	afx_msg BOOL OnHelpInfo(HELPINFO* pHelpInfo);
	//}}AFX_MSG
	DECLARE_MESSAGE_MAP()

private:
	BOOL IsFacilityMapped(HTREEITEM &hWMSItem, int facilityDBId);
	BOOL IsSectionMapped(HTREEITEM& hWMSItem, int sectionDBId);
	int GetMappedFacilityId(HTREEITEM& hWMSItem);
	BOOL ValidateAssignment();
	int m_nDelayInterval;
	int m_nScrollMargin;
	int m_nScrollInterval;
	HTREEITEM m_hDropItem;
	BOOL m_bDraggingWMS;
	BOOL m_bDraggingSection;
	CImageList *m_pDragImageList;
	CImageList m_WMSImageList;
	CImageList m_FacilityImageList;
	CMap<int, int, HTREEITEM, HTREEITEM&> m_MapSectionToTree;
	CMap<HTREEITEM, HTREEITEM&, int, int> m_MapTreeToSection;
	CMap<int, int, HTREEITEM, HTREEITEM&> m_MapWMSToTree;
	CMap<HTREEITEM, HTREEITEM&, int, int> m_MapTreeToWMS;
	CMap<HTREEITEM, HTREEITEM&, int, int> m_MapTreeToGroup;


	HTREEITEM m_hDragItem;

	int WMSTreeType(HTREEITEM &hItem);
	int FacTreeType(HTREEITEM &hItem);
	BOOL ValidateWMSDragItem(HTREEITEM &hItem);
	BOOL ValidateSectionDragItem(HTREEITEM &hItem);
	void UpdateWMSTreeText(HTREEITEM &hWMSItem, const CString& wmsName, CWMSMap *pMap);
	int Remove(HTREEITEM &hItem);
	int UpdateMapTreeItem(CWMSMap *pMap);
	int Assign(CWMS *pWMS, int facilityDBId, int sectionDBId);
	int UpdateMapTree();


	HTREEITEM AddWMSToTree(CWMS *pWMS, HTREEITEM hItem);
	HTREEITEM AddGroupToTree(CWMSGroup *pGroup);
	int LoadWMSTree();
	int LoadWMSList();
	int LoadFacilityList();
	int LoadFacilityTree();

	void OnMouseMoveWMS(CPoint point);
	void OnMouseMoveSection(CPoint point);
	HTREEITEM GetFacilityNode(int facilityDBId);

	typedef enum {
		wmsGroupType,
		wmsType,
		wmsMappedSectionType
	} wmsTreeType;

	typedef enum {
		facType,
		sectionType
	} facTreeType;
};

//{{AFX_INSERT_LOCATION}}
// Microsoft Visual C++ will insert additional declarations immediately before the previous line.

#endif // !defined(AFX_WMSEXPORTPAGE_H__C111C157_5ADB_42BE_A421_DE6D1DED1D07__INCLUDED_)

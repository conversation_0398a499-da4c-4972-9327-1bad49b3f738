// UDF.cpp: implementation of the CUDF class.
//
//////////////////////////////////////////////////////////////////////

#include "stdafx.h"
#include "UDF.h"
#include "UtilityHelper.h"

extern CUtilityHelper utilityHelper;
//////////////////////////////////////////////////////////////////////
// Construction/Destruction
//////////////////////////////////////////////////////////////////////

CUDF::CUDF()
{
	m_Name = "";
	m_Value = "";
	m_Type = -1;
	m_ListID = 0;
	m_ValueID = 0;
	m_DefaultValue = "";
	m_ParentID = -1;
	m_ElementType = DT_NONE;
	m_IntegerValue = 0;
	m_FloatValue = 0;
	m_ElementID = 0;

	m_ListValues.RemoveAll();
}

CUDF::~CUDF()
{

}


CUDF::CUDF(CString &name, CString &value, int type, long listId, long valueId)
{
	m_Name = name;
	m_Value = value;
	m_Type = type;
	m_ListID = listId;
	m_ValueID = valueId;
	m_DefaultValue = "";
	m_ListValues.RemoveAll();

}

int CUDF::Parse(CString &udf)
{
	CStringArray fields;
	CString listValues;

	// Everything after list id is optional
	// ListID|Name|Type|DefaultValue|List Values|Parent (facilityid or profileid)|ElementType (i.e. table name)|...
	// |Value ID|String Value|Integer Value|Float Value|

	// Yes this is kind of weird, but it does allow limiting the amount of data retrieved
	// by a query, e.g. you can only select the list or you can select the list and value

	utilityHelper.ParseString(udf, "|", fields);
	for (int i=0; i < fields.GetSize(); ++i) {
		switch (i) {
		case 0:
			m_ListID = atol(fields[i]);
			break;
		case 1:
			m_Name = fields[i];
			break;
		case 2:
			m_Type = atoi(fields[i]);
			break;
		case 3:
			m_DefaultValue = fields[i];
			break;
		case 4:
			listValues = fields[i];
			m_ListValues.RemoveAll();
			if (listValues != "None")
				utilityHelper.ParseString(listValues, ",", m_ListValues);
			break;
		case 5:
			m_ParentID = atol(fields[i]);
			break;
		case 6:
			m_ElementType = atoi(fields[i]);
			break;
		case 7:
			m_ValueID = atol(fields[i]);
			break;
		case 8:
			m_Value = fields[i];
			break;
		case 9:
			m_IntegerValue = atol(fields[i]);
			break;
		case 10:
			m_FloatValue = atof(fields[i]);
			break;
		case 11:
			m_ElementID = atol(fields[i]);
			break;
		}
	}


	return 0;

}

CUDF::CUDF(const CUDF & other)
{
	m_Name = other.m_Name;
	m_Value = other.m_Value;
	m_Type = other.m_Type;
	m_ListID = other.m_ListID;
	m_ValueID = other.m_ValueID;
	m_DefaultValue = other.m_DefaultValue;
	m_ParentID = other.m_ParentID;
	m_ElementType = other.m_ElementType;
	m_IntegerValue = other.m_IntegerValue;
	m_FloatValue = other.m_FloatValue;
	m_ElementID = other.m_ElementID;


	m_ListValues.RemoveAll();
	for (int i=0; i < other.m_ListValues.GetSize(); ++i) {
		m_ListValues.Add(other.m_ListValues[i]);
	}


}
CUDF& CUDF::operator=(const CUDF & other)
{
	m_Name = other.m_Name;
	m_Value = other.m_Value;
	m_Type = other.m_Type;
	m_ListID = other.m_ListID;
	m_ValueID = other.m_ValueID;
	m_DefaultValue = other.m_DefaultValue;
	m_ParentID = other.m_ParentID;
	m_ElementType = other.m_ElementType;
	m_IntegerValue = other.m_IntegerValue;
	m_FloatValue = other.m_FloatValue;
	m_ElementID = other.m_ElementID;

	m_ListValues.RemoveAll();
	for (int i=0; i < other.m_ListValues.GetSize(); ++i) {
		m_ListValues.Add(other.m_ListValues[i]);
	}

	return *this;

}

BOOL CUDF::operator==(const CUDF &other)
{
	if (m_Name != other.m_Name) return FALSE;
	if (m_Value != other.m_Value) return FALSE;
	if (m_Type != other.m_Type) return FALSE;
	if (m_ListID != other.m_ListID) return FALSE;
	if (m_ValueID != other.m_ValueID) return FALSE;
	if (m_DefaultValue != other.m_DefaultValue) return FALSE;
	if (m_ParentID != other.m_ParentID) return FALSE;
	if (m_ElementType != other.m_ElementType) return FALSE;
	if (m_IntegerValue != other.m_IntegerValue) return FALSE;
	if (m_FloatValue != other.m_FloatValue) return FALSE;
	if (m_ElementID != other.m_ElementID) return FALSE;

	if (m_ListValues.GetSize() != other.m_ListValues.GetSize()) return FALSE;

	for (int i=0; i < m_ListValues.GetSize(); ++i) {
		if (m_ListValues[i] != other.m_ListValues[i]) 
			return FALSE;
	}

	return TRUE;

}

CString CUDF::Stream() 
{
	CString stream;
	CString listValues;

	// ListID|Name|Type|DefaultValue|List Values|Parent (facilityid or profileid)|ElementType (i.e. table name)|...
	// |Value ID|String Value|Integer Value|Float Value|

	utilityHelper.BuildDelimitedString(m_ListValues, listValues, ",");
	
	if (listValues == "")
		listValues = "None";

	stream.Format("%d|%s|%d|%s|%s|%d|%d|%d|%s|%d|%f|%d",
		m_ListID, m_Name, m_Type, m_DefaultValue, listValues,
		m_ParentID, m_ElementType, m_ValueID, m_Value, m_IntegerValue, m_FloatValue, m_ElementID);

	return stream;

}

CString CUDF::GetTypeAsString()
{
	CString str;

	switch (m_Type) {
	case DT_INT:
		str = "Integer";
		break;
	case DT_FLOAT:
		str = "Float";
		break;
	case DT_STRING:
		str = "String";
		break;
	case DT_LIST:
		str = "List";
		break;
	default:
		str = "Unknown";
		break;
	}

	return str;

}

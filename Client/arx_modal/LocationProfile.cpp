// LocationProfile.cpp: implementation of the CLocationProfile class.
//
//////////////////////////////////////////////////////////////////////

#include "stdafx.h"
#include "modal.h"
#include "LocationProfile.h"
#include "UtilityHelper.h"

#ifdef _DEBUG
#undef THIS_FILE
static char THIS_FILE[]=__FILE__;
#define new DEBUG_NEW
#endif

extern CUtilityHelper utilityHelper;
//////////////////////////////////////////////////////////////////////
// Construction/Destruction
//////////////////////////////////////////////////////////////////////

CLocationProfile::CLocationProfile()
{
	m_LocationProfileDBId = 0;
	m_LevelProfileDBId = 0;
}

CLocationProfile::~CLocationProfile()
{

}


CLocationProfile::CLocationProfile(const CLocationProfile& other)
{
	m_LocationProfileDBId = other.m_LocationProfileDBId;
	m_Description = other.m_Description;
	m_HandlingMethod = other.m_HandlingMethod;
	m_IsSelect = other.m_IsSelect;
	m_Coordinates = other.m_Coordinates;
	m_Width = other.m_Width;
	m_Depth = other.m_Depth;
	m_Height = other.m_Height;
	m_LocationSpace = other.m_LocationSpace;
	m_WeightCapacity = other.m_WeightCapacity;
	m_LevelProfileDBId = other.m_LevelProfileDBId;
}

CLocationProfile& CLocationProfile::operator=(const CLocationProfile& other)
{
	m_LocationProfileDBId = other.m_LocationProfileDBId;
	m_Description = other.m_Description;
	m_HandlingMethod = other.m_HandlingMethod;
	m_IsSelect = other.m_IsSelect;
	m_Coordinates = other.m_Coordinates;
	m_Width = other.m_Width;
	m_Depth = other.m_Depth;
	m_Height = other.m_Height;
	m_LocationSpace = other.m_LocationSpace;
	m_WeightCapacity = other.m_WeightCapacity;
	m_LevelProfileDBId = other.m_LevelProfileDBId;
	
	return *this;
}

BOOL CLocationProfile::operator==(const CLocationProfile& other)
{
	if (m_LocationProfileDBId != other.m_LocationProfileDBId) return FALSE;
	if (m_Description != other.m_Description) return FALSE;
	if (m_HandlingMethod != other.m_HandlingMethod) return FALSE;
	if (m_IsSelect != other.m_IsSelect) return FALSE;
	if (m_Coordinates != other.m_Coordinates) return FALSE;
	if (m_Width != other.m_Width) return FALSE;
	if (m_Depth != other.m_Depth) return FALSE;
	if (m_Height != other.m_Height) return FALSE;
	if (m_LocationSpace != other.m_LocationSpace) return FALSE;
	if (m_WeightCapacity != other.m_WeightCapacity) return FALSE;
	if (m_LevelProfileDBId != other.m_LevelProfileDBId) return FALSE;
	
	return TRUE;

}

int CLocationProfile::Parse(const CString& line)
{
	CStringArray strings;

	utilityHelper.ParseString(line, "|", strings);

	for (int i=0; i < strings.GetSize(); ++i) {
		switch (i) {
		case 0:
			m_LocationProfileDBId = atoi(strings[i]);
			break;
		case 1:
			m_Description = strings[i];
			break;
		case 2:
			m_HandlingMethod = atoi(strings[i]);
			break;
		case 3:
			m_IsSelect = atoi(strings[i]);
			break;
		case 4:
			m_Coordinates.m_X  = atof(strings[i]);
			break;
		case 5:
			m_Coordinates.m_Y = atof(strings[i]);
			break;
		case 6:
			m_Coordinates.m_Z = atof(strings[i]);
			break;
		case 7:
			m_Width = atof(strings[i]);
			break;
		case 8:
			m_Depth = atof(strings[i]);
			break;
		case 9:
			m_Height = atof(strings[i]);
			break;
		case 10:
			m_LocationSpace = atof(strings[i]);
			break;
		case 11:
			m_WeightCapacity = atof(strings[i]);
			break;
		case 12:
			m_LevelProfileDBId = atoi(strings[i]);
			break;
		}

	}


	return 0;
}


// NewSectionPage1.cpp : implementation file
//

#include "stdafx.h"
#include "modal.h"
#include "NewSectionPage1.h"
#include "HelpService.h"
#include "ControlService.h"
#include "FacilityDataService.h"
#include "UtilityHelper.h"

#ifdef _DEBUG
#define new DEBUG_NEW
#undef THIS_FILE
static char THIS_FILE[] = __FILE__;
#endif

extern CHelpService helpService;
extern CControlService controlService;
extern CFacilityDataService facilityDataService;
extern CUtilityHelper utilityHelper;
/////////////////////////////////////////////////////////////////////////////
// CNewSectionPage1 property page

IMPLEMENT_DYNCREATE(CNewSectionPage1, CPropertyPage)

CNewSectionPage1::CNewSectionPage1() : CPropertyPage(CNewSectionPage1::IDD)
{
	//{{AFX_DATA_INIT(CNewSectionPage1)
	m_NewSection_ApplyBrokenOrder = FALSE;
	m_NewSection_AvgOrdQty = 0;
	m_NewSection_ContainerCount = 0;
	m_NewSection_Description = _T("");
	m_NewSection_LocationMask = _T("");
	m_NewSection_OrderCount = 0;
	m_ForkHotspotCoordinates = _T("");
	m_SelHotspotCoordinates = _T("");
	m_TravelDistance = _T("");
	//}}AFX_DATA_INIT
	m_SectionDBId = 0;
}

CNewSectionPage1::~CNewSectionPage1()
{
}

void CNewSectionPage1::DoDataExchange(CDataExchange* pDX)
{
	CPropertyPage::DoDataExchange(pDX);
	//{{AFX_DATA_MAP(CNewSectionPage1)
	DDX_Control(pDX, IDC_NEWSECTION_ORDERCOUNT, m_NewSection_OrderCount_Box);
	DDX_Control(pDX, IDC_NEWSECTION_LOCATIONMASK, m_NewSectionLocationMask_Box);
	DDX_Control(pDX, IDC_NEWSECTION_DESCRIPTION, m_NewSection_DescriptionBox);
	DDX_Control(pDX, IDC_NEWSECTION_CONTQTY, m_NewSection_ContainerCount_Box);
	DDX_Control(pDX, IDC_NEWSECTION_AVGORDQTY, m_NewSection_AvgOrdQty_Box);
	DDX_Check(pDX, IDC_NEWSECT_APPLYBROKORDER, m_NewSection_ApplyBrokenOrder);
	DDX_Text(pDX, IDC_NEWSECTION_AVGORDQTY, m_NewSection_AvgOrdQty);
	DDX_Text(pDX, IDC_NEWSECTION_CONTQTY, m_NewSection_ContainerCount);
	DDX_Text(pDX, IDC_NEWSECTION_DESCRIPTION, m_NewSection_Description);
	DDX_Text(pDX, IDC_NEWSECTION_LOCATIONMASK, m_NewSection_LocationMask);
	DDX_Text(pDX, IDC_NEWSECTION_ORDERCOUNT, m_NewSection_OrderCount);
	DDX_Text(pDX, IDC_FORK_HOTSPOT_COORDINATES, m_ForkHotspotCoordinates);
	DDX_Text(pDX, IDC_SEL_HOTSPOT_COORDINATES, m_SelHotspotCoordinates);
	DDX_Text(pDX, IDC_TRAVEL_DISTANCE, m_TravelDistance);
	//}}AFX_DATA_MAP
}


BEGIN_MESSAGE_MAP(CNewSectionPage1, CPropertyPage)
	//{{AFX_MSG_MAP(CNewSectionPage1)
	ON_EN_KILLFOCUS(IDC_NEWSECTION_AVGORDQTY, OnKillfocusNewsectionAvgordqty)
	ON_EN_KILLFOCUS(IDC_NEWSECTION_CONTQTY, OnKillfocusNewsectionContqty)
	ON_EN_KILLFOCUS(IDC_NEWSECTION_DESCRIPTION, OnKillfocusNewsectionDescription)
	ON_EN_KILLFOCUS(IDC_NEWSECTION_LOCATIONMASK, OnKillfocusNewsectionLocationmask)
	ON_EN_KILLFOCUS(IDC_NEWSECTION_ORDERCOUNT, OnKillfocusNewsectionOrdercount)
	ON_BN_CLICKED(IDC_NEWSECT_APPLYBROKORDER, OnApplyBrokenOrder)
	ON_WM_HELPINFO()
	//}}AFX_MSG_MAP
END_MESSAGE_MAP()

/////////////////////////////////////////////////////////////////////////////
// CNewSectionPage1 message handlers

void CNewSectionPage1::OnKillfocusNewsectionAvgordqty() 
{
	// TODO: Add your control notification handler code here
	char buf[32]="";
	char buf2[32]="";
	char * ptr;
	
	if (this->IsWindowVisible() == 0)
		return;

	if (UpdateData(TRUE) == 0 ) {
		m_NewSection_AvgOrdQty_Box.SetFocus();
		return;
	}

	if (m_NewSection_AvgOrdQty < 0) {
		AfxMessageBox("Number must be non-negative");
		m_NewSection_AvgOrdQty_Box.SetFocus();
		return;
	}
	if (m_NewSection_AvgOrdQty > 999999999 ) {
		AfxMessageBox("Number is out of range");
		m_NewSection_AvgOrdQty_Box.SetFocus();
		return;
	}

	// TODO: Add your control notification handler code here
	m_NewSection_AvgOrdQty_Box.GetWindowText(buf,31);
	if ( strchr(buf,'+') != NULL ) {
		ptr = strchr(buf,'+');
		ptr++;
		strcpy(buf2,ptr);
		m_NewSection_AvgOrdQty_Box.SetWindowText(buf2);
	}
	UpdateData(FALSE);	
}

void CNewSectionPage1::OnKillfocusNewsectionContqty() 
{
	// TODO: Add your control notification handler code here
	char buf[32]="";
	char buf2[32]="";
	char * ptr;
	
	if (this->IsWindowVisible() == 0)
		return;
	
	if (UpdateData(TRUE) == 0 ) {
		m_NewSection_ContainerCount_Box.SetFocus();
		return;
	}

//	if (m_NewSection_ContainerCount <= 0) {
//		AfxMessageBox("Number must be greater than zero");
//		m_NewSection_ContainerCount_Box.SetFocus();
//		return;
//	}
	if (m_NewSection_ContainerCount > 999999999 ) {
		AfxMessageBox("Number is out of range");
		m_NewSection_ContainerCount_Box.SetFocus();
		return;
	}

	// TODO: Add your control notification handler code here
	m_NewSection_ContainerCount_Box.GetWindowText(buf,31);
	if ( strchr(buf,'+') != NULL ) {
		ptr = strchr(buf,'+');
		ptr++;
		strcpy(buf2,ptr);
		m_NewSection_ContainerCount_Box.SetWindowText(buf2);
	}
	UpdateData(FALSE);
		
}

void CNewSectionPage1::OnKillfocusNewsectionDescription() 
{
	// TODO: Add your control notification handler code here
	char invalidChars[100] = "~`!@#$%^&*(),':;.{[}]|+=";
	int foundInvalidchar = 0;

	if (this->IsWindowVisible() == 0)
		return;

	strcat(invalidChars,"\"");
	strcat(invalidChars,"\\");

	UpdateData(TRUE);

	for (int i = 0; invalidChars[i] != '\0' && foundInvalidchar == 0; i++) {
		if (strchr(m_NewSection_Description,invalidChars[i]) != NULL )
			foundInvalidchar = 1;
	}
	if (foundInvalidchar == 1) {
		AfxMessageBox("Invalid character in identifier");
		m_NewSection_Description = "";
		UpdateData(FALSE);
		m_NewSection_DescriptionBox.SetFocus();
	}
	if ( m_NewSection_Description.GetLength() > 18 ) {
		AfxMessageBox("Identifier too long.  Length must be 18 characters or less.");
		m_NewSection_Description = "";
		UpdateData(FALSE);
		m_NewSection_DescriptionBox.SetFocus();
	}	
}

void CNewSectionPage1::OnKillfocusNewsectionLocationmask() 
{
	// TODO: Add your control notification handler code here
	char invalidChars[100] = "~`!@#$%^&*(),':;.{[}]|+=";
	int foundInvalidchar = 0;

	if (this->IsWindowVisible() == 0)
		return;

	strcat(invalidChars,"\"");
	strcat(invalidChars,"\\");

	UpdateData(TRUE);

	for (int i = 0; invalidChars[i] != '\0' && foundInvalidchar == 0; i++) {
		if (strchr(m_NewSection_LocationMask,invalidChars[i]) != NULL )
			foundInvalidchar = 1;
	}
	if (foundInvalidchar == 1) {
		AfxMessageBox("Invalid character in identifier");
		m_NewSection_LocationMask = "";
		UpdateData(FALSE);
		m_NewSectionLocationMask_Box.SetFocus();
	}
	if ( m_NewSection_LocationMask.GetLength() > 18 ) {
		AfxMessageBox("Identifier too long.  Length must be 18 characters or less.");
		m_NewSection_LocationMask = "";
		UpdateData(FALSE);
		m_NewSection_DescriptionBox.SetFocus();
	}
		
}

void CNewSectionPage1::OnKillfocusNewsectionOrdercount() 
{
	// TODO: Add your control notification handler code here
	char buf[32]="";
	char buf2[32]="";
	char * ptr;
	
	if (this->IsWindowVisible() == 0)
		return;

	if (UpdateData(TRUE) == 0 ) {
		m_NewSection_OrderCount_Box.SetFocus();
		return;
	}

	if (m_NewSection_OrderCount < 0) {
		AfxMessageBox("Number must be non-negative");
		m_NewSection_OrderCount_Box.SetFocus();
		return;
	}
	if (m_NewSection_OrderCount > 999999999 ) {
		AfxMessageBox("Number is out of range");
		m_NewSection_OrderCount_Box.SetFocus();
		return;
	}

	// TODO: Add your control notification handler code here
	m_NewSection_OrderCount_Box.GetWindowText(buf,31);
	if ( strchr(buf,'+') != NULL ) {
		ptr = strchr(buf,'+');
		ptr++;
		strcpy(buf2,ptr);
		m_NewSection_OrderCount_Box.SetWindowText(buf2);
	}
	UpdateData(FALSE);
		
}


BOOL CNewSectionPage1::OnHelpInfo(HELPINFO* pHelpInfo) 
{
	helpService.ShowFieldHelp(IDD, pHelpInfo->iCtrlId);

	return FALSE;

}

BOOL CNewSectionPage1::OnInitDialog() 
{
	CPropertyPage::OnInitDialog();


	m_OriginalName = m_NewSection_Description;

	m_NewSection_DescriptionBox.SetFocus();	
	
	if (m_SectionDBId <= 0) {
		GetDlgItem(IDC_TRAVEL_DISTANCE)->ShowWindow(SW_HIDE);
		GetDlgItem(IDC_SEL_HOTSPOT_COORDINATES)->ShowWindow(SW_HIDE);
		GetDlgItem(IDC_FORK_HOTSPOT_COORDINATES)->ShowWindow(SW_HIDE);
		GetDlgItem(IDC_TRAVEL_DISTANCE_STATIC)->ShowWindow(SW_HIDE);
		GetDlgItem(IDC_SEL_HOTSPOT_STATIC)->ShowWindow(SW_HIDE);
		GetDlgItem(IDC_FORK_HOTSPOT_STATIC)->ShowWindow(SW_HIDE);
		GetDlgItem(IDC_HOTSPOT_STATIC)->ShowWindow(SW_HIDE);
	}
	else {
		GetDlgItem(IDC_TRAVEL_DISTANCE)->ShowWindow(SW_SHOW);
		GetDlgItem(IDC_SEL_HOTSPOT_COORDINATES)->ShowWindow(SW_SHOW);
		GetDlgItem(IDC_FORK_HOTSPOT_COORDINATES)->ShowWindow(SW_SHOW);
		GetDlgItem(IDC_TRAVEL_DISTANCE_STATIC)->ShowWindow(SW_SHOW);
		GetDlgItem(IDC_SEL_HOTSPOT_STATIC)->ShowWindow(SW_SHOW);
		GetDlgItem(IDC_FORK_HOTSPOT_STATIC)->ShowWindow(SW_SHOW);
		GetDlgItem(IDC_HOTSPOT_STATIC)->ShowWindow(SW_SHOW);
	}

	UpdateData(FALSE);

	if (m_NewSection_ApplyBrokenOrder)  {
		m_NewSection_ContainerCount_Box.SetReadOnly(FALSE);
		m_NewSection_AvgOrdQty_Box.SetReadOnly(FALSE);
	}
	else {
		m_NewSection_ContainerCount_Box.SetReadOnly(TRUE);
		m_NewSection_AvgOrdQty_Box.SetReadOnly(TRUE);
	}

	return FALSE;  // return TRUE unless you set the focus to a control
	              // EXCEPTION: OCX Property Pages should return FALSE
}

BOOL CNewSectionPage1::OnNotify(WPARAM wParam, LPARAM lParam, LRESULT* pResult) 
{

	NMHDR* pNMHDR = (NMHDR*)lParam;

	// if they press the help button
	if (pNMHDR->code == PSN_HELP) {
		helpService.ShowScreenHelp(IDD);
		return TRUE;
	}	

	return CPropertyPage::OnNotify(wParam, lParam, pResult);
}

BOOL CNewSectionPage1::OnApply() 
{
	return CPropertyPage::OnApply();

}

BOOL CNewSectionPage1::OnKillActive() 
{
	CPropertySheet *pParent = (CPropertySheet *) GetParent();
	
	if (UpdateData(TRUE) == 0 )
		return FALSE;

	if ( m_NewSection_Description == "" ) {
		AfxMessageBox("Section ID is a required field");
		utilityHelper.SetEditControlErrorState(this, IDC_NEWSECTION_DESCRIPTION);
		return FALSE;
	}
	
	/*
	if (m_OriginalName != m_NewSection_Description) {
		try {
			if (facilityDataService.IsSectionNameInUse(m_NewSection_Description, 
				controlService.GetCurrentFacilityDBId(), m_SectionDBId)) {
				int rc = AfxMessageBox("A section with the same name already exists in the facility.\n"
					"Do you wish to create an additional section with the same name?\n", MB_YESNO);
				if (rc != IDYES) {
					utilityHelper.SetEditControlErrorState(this, IDC_NEWSECTION_DESCRIPTION);
					return FALSE;
				}
			}
		}
		catch (...) {
			controlService.Log("Error determining if section name is unique.",
				"Generic exception in GetSectionNameInUse.\n");
			return FALSE;
		}
	}
	*/

	if ( m_NewSection_LocationMask == "" ) {
		AfxMessageBox("Location mask is a required field.");
		utilityHelper.SetEditControlErrorState(this, IDC_NEWSECTION_LOCATIONMASK);
		return FALSE;
	}
	return CPropertyPage::OnKillActive();
}

BOOL CNewSectionPage1::OnSetActive() 
{
	
	return CPropertyPage::OnSetActive();
}

void CNewSectionPage1::OnCancel() 
{
	
	CPropertyPage::OnCancel();
}

void CNewSectionPage1::OnOK() 
{
		
	CPropertyPage::OnOK();
}

void CNewSectionPage1::OnApplyBrokenOrder()
{
	UpdateData(TRUE);
	if (m_NewSection_ApplyBrokenOrder)  {
		m_NewSection_ContainerCount_Box.SetReadOnly(FALSE);
		m_NewSection_AvgOrdQty_Box.SetReadOnly(FALSE);
	}
	else {
		m_NewSection_ContainerCount_Box.SetReadOnly(TRUE);
		m_NewSection_AvgOrdQty_Box.SetReadOnly(TRUE);
		m_NewSection_AvgOrdQty = 0;
		m_NewSection_ContainerCount = 0;
		UpdateData(FALSE);
	}



}

// ElementMaintenanceHelper.cpp: implementation of the CElementMaintenanceHelper class.
//
//////////////////////////////////////////////////////////////////////

#include "stdafx.h"
#include "modal.h"
#include "ElementMaintenanceHelper.h"
#include "FacilityHelper.h"
#include "BTreeHelper.h"
#include "BayProfile.h"
#include "ControlService.h"

#include "ssa_exception.h"
#include "ChangeRackType.h"

#include "UDF.h"
#include "UDFPage.h"
#include "FacilityProperties.h"
#include "FacilityTools.h"
#include "NewSectionPage1.h"
#include "NewSectionPage2.h"
#include "AisleProperties.h"
#include "SideProperties.h"
#include "BayProperties.h"
#include "LevelProperties.h"
#include "LocationProperties.h"
#include "FacilityElementSheet.h"
#include "AutoCADCommands.h"
#include "UtilityHelper.h"
#include "LocationNumberingService.h"
#include "FacilityDataService.h"
#include "DrawingService.h"
#include "BayProfileDataService.h"
#include "SideProfileDataService.h"
#include "SolutionDataService.h"

#include "resourcehelper.h"
#include "ObjectPlaceDialog.h"
#include "ChooseAisleProfileDialog.h"
#include "PickPathOptionDialog.h"
#include "NewSectionPage1.h"
#include "NewSectionPage2.h"
#include "HotSpotDialog.h"

#include "AisleProfile.h"
#include "ControlService.h"
#include "LocationAttributesPage.h"

#include "LevelLocationDialog.h"
#include "ProgressMessage.h"
#include "ProcessingMessage.h"
#include "HotSpotPropertiesDialog.h"

#include <strstream>
#include <math.h>

#include <adscodes.h>
#include <dbsol3d.h>

using namespace std;

#ifdef _DEBUG
#undef THIS_FILE
static char THIS_FILE[]=__FILE__;
#define new DEBUG_NEW
#endif

extern CUtilityHelper utilityHelper;
extern TreeElement changesTree;
extern TreeElement changesTree;
extern int			numItemsProcessed;
extern char			slotDir[256];
extern int			BayNum;


char oldPickPathHandle[20];

extern CControlService controlService;

extern CBTreeHelper bTreeHelper;
extern CLocationNumberingService numberingService;
extern CFacilityDataService facilityDataService;
extern CFacilityHelper facilityHelper;
extern CSideProfileDataService sideProfileDataService;
extern CBayProfileDataService bayProfileDataService;

//////////////////////////////////////////////////////////////////////
// Construction/Destruction
//////////////////////////////////////////////////////////////////////

CElementMaintenanceHelper::CElementMaintenanceHelper()
{

}

CElementMaintenanceHelper::~CElementMaintenanceHelper()
{

}

void CElementMaintenanceHelper::AddAisle()
{
	qqhSLOTAisle insertedAisle;				//the aisle information to insert
	qqhSLOTSide insertedSide;				//a side object to add to the aisle
	qqhSLOTBay insertedBay;					//a bay to add to the side
	qqhSLOTFacility theFacility;			//a facility to get UDFs for
	CAisleProfile anAisleProfile;
	CSideProfile side1Profile;
	CSideProfile side2Profile;
	qqhSLOTSection newSection;				//a section definition to add if the user wants to
											//add a new section
	
	///////////////////////////////////////////////////////////////
	// dialogs used in this process
	///////////////////////////////////////////////////////////////
	CTemporaryResourceOverride thisResource;
	CChooseAisleProfileDialog AisleDialog(CWnd::FromHandle(adsw_acadMainWnd()));
	CObjectPlaceDialog PlaceAisleDialog(CWnd::FromHandle(adsw_acadMainWnd()));
	

	CString currentBay;						//last bay profile used (for performance)
	ads_point cornerPoint, rotatedBayCorner;	//points used to insert the bay
	ads_point currentBayCenter, rotatedBayCenter,holdBayCenter;
	ads_point holdCornerPoint;
	ads_point bayCenter;


	CArray <float,float&> side1BayWidth;
	CArray <float,float&> side1BayDepth;
	CArray <float,float&> side1BayHeight;
	CArray <float,float&> side2BayWidth;	//arrays of dimensions of bays
	CArray <float,float&> side2BayDepth;	//for aisle insertion purposes
	CArray <float,float&> side2BayHeight;
	CArray <double,double&> side1BayYCoord;
	CArray <float,float&> side1BarWidth;
	CArray <float,float&> side2BarWidth;	CArray <int, int&> tempOffsetArray;
	CArray <int, int&> newIndexArray;
	CSsaStringArray aisleUDFList;			//UDFs to add to the facility
	CSsaStringArray sideUDFList;			//elements
	CSsaStringArray bayUDFList;
	CSsaStringArray locationUDFList;
	CAutoCADCommands acCmds;
	
	int i,j,z=0;
	int sectionSel;
	int tempReturn;

	float tempFloat;

	CString BayLayerName;					//Unique name to insert the bay DWG
	CString previousDesc;
	CString bayDrawingPath;
	CWinApp * currentApp;

	ads_regapp("Slotting");
	currentApp = AfxGetApp();

	///////////////////////////////////////////////////////////
	// Get available sections
	///////////////////////////////////////////////////////////
	for (i=0; i < changesTree.treeChildren.GetSize();i++)
		tempOffsetArray.Add(changesTree.treeChildren[i].fileOffset);

	AisleDialog.m_SectionNameList.RemoveAll();
	
	tempReturn = bTreeHelper.GetSectionDescriptions(AisleDialog.m_SectionNameList,tempOffsetArray,newIndexArray);

	AisleDialog.m_ChooseAisle_SlotDir = slotDir;
	if ( AisleDialog.DoModal() == IDCANCEL )
		return;

	///////////////////////////////////////////////////////////
	// Get UDFs to put on facility elements
	///////////////////////////////////////////////////////////
	bTreeHelper.GetBtFacility(1, theFacility);
	if (theFacility.getDBID() != 0) {

		CWaitCursor cwc;
		
		try {
			facilityDataService.GetAvailableAttributesByFacID(CString("SLOTAisle"), theFacility.getDBID(), aisleUDFList);
			for (i = 0; i < aisleUDFList.GetSize(); i++)
				aisleUDFList[i].SetAt(aisleUDFList[i].GetLength()-1,'\n');
		}
		catch(...) {
			AfxMessageBox("Error getting facility element UDFs");
			return;
		}
		try {
			facilityDataService.GetAvailableAttributesByFacID(CString("SLOTSide"), theFacility.getDBID(), sideUDFList);
			for (i = 0; i < sideUDFList.GetSize(); i++)
				sideUDFList[i].SetAt(sideUDFList[i].GetLength()-1,'\n');
		}
		catch(...) {
			AfxMessageBox("Error getting facility element UDFs");
			return;
		}
		try {
			facilityDataService.GetAvailableAttributesByFacID(CString("SLOTBay"), theFacility.getDBID(), bayUDFList);
			for (i = 0; i < bayUDFList.GetSize(); i++)
				bayUDFList[i].SetAt(bayUDFList[i].GetLength()-1,'\n');
		}
		catch(...) {
			AfxMessageBox("Error getting facility element UDFs");
			return;
		}
		
	}

	insertedAisle.getUDFList().Copy(aisleUDFList);
	insertedSide.getUDFList().Copy(sideUDFList);
	insertedBay.getUDFList().Copy(bayUDFList);

	///////////////////////////////////////////////////////////
	// Get the stock Aisle Profile filename
	///////////////////////////////////////////////////////////





	///////////////////////////////////////////////////////////
	// Get the aisle profile from the DB
	///////////////////////////////////////////////////////////
	int rc;
	try {
		CWaitCursor cwc;
		rc = m_AisleProfileDataService.GetAisleProfile(AisleDialog.m_ChooseAisle_AisleName, anAisleProfile);
	}
	catch (...) {
		rc = -1;
	}
	
	if (rc < 0) {
		AfxMessageBox("Error retrieving aisle profile from database.");
		return;
	}

	///////////////////////////////////////////////////////////
	// Get the side1 profile from the DB
	///////////////////////////////////////////////////////////
	if ( anAisleProfile.m_pLeftSideProfile != NULL) {
		CWaitCursor cwc;
		side1Profile = *anAisleProfile.m_pLeftSideProfile;
		
		try {
			if ( sideProfileDataService.GetSideProfile(side1Profile.m_SideProfileDBId, side1Profile, CBayProfile::loadLevels|CBayProfile::loadLocations) < 0 ) 
			{
				AfxMessageBox("Error retrieving left side profile from database.");
				return;
			}
		}
		catch(...) {
			AfxMessageBox("Error retrieving left side profile from database.");
		}
		
	}
		
	///////////////////////////////////////////////////////////
	// Get the side2 profile from the DB
	///////////////////////////////////////////////////////////

	if ( anAisleProfile.m_pRightSideProfile != NULL) {
		
		CWaitCursor cwc;
		if (side1Profile.m_SideProfileDBId > 0 && 
			side1Profile.m_SideProfileDBId == anAisleProfile.m_pRightSideProfile->m_SideProfileDBId)
			side2Profile = side1Profile;
		else {
			
			side2Profile = *anAisleProfile.m_pRightSideProfile;
			
			
			try {
				if ( sideProfileDataService.GetSideProfile(side2Profile.m_SideProfileDBId, side2Profile, CBayProfile::loadLevels|CBayProfile::loadLocations) < 0 ) 
				{
					AfxMessageBox("Error retrieving right side profile from database.");
					return;
				}
			}
			catch(...) {
				AfxMessageBox("Error retrieving right side profile from database.");
			}
		}
		
	}
	
	///////////////////////////////////////////////////////////
	// Fill in appropriate values
	///////////////////////////////////////////////////////////
	insertedAisle.setAisleSpace(anAisleProfile.m_AisleSpace);
	insertedAisle.getCoord().setX(0);
	insertedAisle.getCoord().setY(0);
	insertedAisle.getCoord().setZ(0);
	insertedAisle.setDescription(CString(""));
	strcpy(insertedAisle.getPickPath().getConAcadHandle(),"XXX");
	strcpy(insertedAisle.getPickPath().getAcadHandle(),"XXX");
	insertedAisle.getChildList().Add(insertedSide);
	insertedAisle.getChildList()[0].getCoord().setX(0);
	insertedAisle.getChildList()[0].getCoord().setY(0);
	insertedAisle.getChildList()[0].getCoord().setZ(0);
	insertedAisle.getChildList()[0].setIsRotated(0);
	insertedAisle.getChildList()[0].setDescription(CString("01"));
	insertedAisle.getChildList().Add(insertedSide);


	double rightXCoord = 0;
	if (side1Profile.m_SideProfileDBId > 0 && side1Profile.m_BayProfileList.GetSize() > 0)
		rightXCoord = side1Profile.m_BayProfileList[0]->m_Depth;

	rightXCoord += anAisleProfile.m_AisleSpace;
	
	if (side2Profile.m_SideProfileDBId > 0 && side2Profile.m_BayProfileList.GetSize() > 0)
		rightXCoord += side2Profile.m_BayProfileList[0]->m_Depth;

	insertedAisle.getChildList()[1].getCoord().setX((int)rightXCoord);

	insertedAisle.getChildList()[1].getCoord().setY((int)(side2Profile.CalculateLength()));
	insertedAisle.getChildList()[1].getCoord().setZ(0);
	insertedAisle.getChildList()[1].setIsRotated(1);
	insertedAisle.getChildList()[1].setDescription(CString("02"));

	for ( i = 0; i < side1Profile.m_BayProfileList.GetSize(); i++) {
		//////////////////////////////////////////////////////////////////////
		// Fill in necessary arrays and populate aisle with initial data
		//////////////////////////////////////////////////////////////////////
		CBayProfile *pBayProfile = new CBayProfile(*side1Profile.m_BayProfileList[i]);

		insertedAisle.getChildList()[0].getChildList().Add(insertedBay);

		double myXCoord = 0;
		if ( i == 0) {
			insertedAisle.getChildList()[0].getChildList()[i].getCoord().setX((int)myXCoord);

		}
		else {
			for ( int x = 0; x < i; x++ ) {
				myXCoord += side1BayWidth[x] + side1BarWidth[x];
			}
			insertedAisle.getChildList()[0].getChildList()[i].getCoord().setX((int)myXCoord);
		}
		insertedAisle.getChildList()[0].getChildList()[i].setBayProfileId(pBayProfile->m_BayProfileDBId);
		insertedAisle.getChildList()[0].getChildList()[i].pBayProfile = pBayProfile;
		insertedAisle.getChildList()[0].getChildList()[i].setDescription(CString("New Bay"));
		if ( i != side1Profile.m_BayProfileList.GetSize() -1 ) {
			insertedAisle.getChildList()[0].getChildList()[i].isEndBay = FALSE;
		}
		else {
			insertedAisle.getChildList()[0].getChildList()[i].isEndBay = TRUE;
		}
		tempFloat = pBayProfile->m_Width;
		side1BayWidth.Add(tempFloat);
		tempFloat = pBayProfile->m_UprightWidth;
		
		// brd - we should use the thicker upright; let's see what the ramifications are
		if (i > 0 && tempFloat < side1Profile.m_BayProfileList[i-1]->m_UprightWidth)
			tempFloat = side1Profile.m_BayProfileList[i-1]->m_UprightWidth;

		side1BarWidth.Add(tempFloat);

		// All bay profiles store the maximum depth in the depth field now so we don't have to calculate it
		tempFloat = pBayProfile->m_Depth;
		side1BayDepth.Add(tempFloat);

		if ( tempFloat > side1Profile.m_MaximumBayDepth)
			side1Profile.m_MaximumBayDepth = tempFloat;

		tempFloat = pBayProfile->m_Height;
		side1BayHeight.Add(tempFloat);
	}

	for ( i = 0; i < side2Profile.m_BayProfileList.GetSize(); i++) {
		//////////////////////////////////////////////////////////////////////
		// Fill in necessary arrays and populate aisle with initial data
		//////////////////////////////////////////////////////////////////////
		CBayProfile *pBayProfile = new CBayProfile(*side2Profile.m_BayProfileList[i]);

		insertedAisle.getChildList()[1].getChildList().Add(insertedBay);
		insertedAisle.getChildList()[1].getChildList()[i].setDescription(CString("New Bay"));
		insertedAisle.getChildList()[1].getChildList()[i].setBayProfileId(pBayProfile->m_BayProfileDBId);
		insertedAisle.getChildList()[1].getChildList()[i].pBayProfile = pBayProfile;

		float myXCoord = 0;
		if ( i == 0) {
			insertedAisle.getChildList()[1].getChildList()[i].getCoord().setX((int)myXCoord);
		}
		else {
			for ( int x = 0; x < i; x++ )
				myXCoord += side2BayWidth[x] + side2BarWidth[x];
			insertedAisle.getChildList()[1].getChildList()[i].getCoord().setX((int)myXCoord);
		}
		if ( i == 0 ) {
			insertedAisle.getChildList()[1].getChildList()[i].isEndBay = TRUE;
		}
		else {
			insertedAisle.getChildList()[1].getChildList()[i].isEndBay = FALSE;
		}
		tempFloat = pBayProfile->m_Width;
		side2BayWidth.Add(tempFloat);
		tempFloat = pBayProfile->m_UprightWidth;

		// brd - we should use the thicker upright; let's see what the ramifications are
		if (i > 0 && tempFloat < side2Profile.m_BayProfileList[i-1]->m_UprightWidth)
			tempFloat = side2Profile.m_BayProfileList[i-1]->m_UprightWidth;

		side2BarWidth.Add(tempFloat);
		tempFloat = pBayProfile->m_Depth;

		side2BayDepth.Add(tempFloat);
		if ( tempFloat > side2Profile.m_MaximumBayDepth)
			side2Profile.m_MaximumBayDepth = tempFloat;
		tempFloat = pBayProfile->m_Height;
		side2BayHeight.Add(tempFloat);
	}

	//////////////////////////////////////////////////////////////////////
	//  If a side is non-existent, then fill in arrays with initialized
	//  data
	//////////////////////////////////////////////////////////////////////
 	if ( side1Profile.m_SideProfileDBId <= 0) {
		float two=0.0;
		float zero=0.0;
		for ( i = 0; i < side2BayWidth.GetSize(); i++ ) {
			side1BayWidth.Add(side2BayWidth[i]);
			side1BayDepth.Add(zero);
			side1BayHeight.Add(zero);
		}
	}
	else if ( side2Profile.m_SideProfileDBId <= 0) {
		float two=0.0;
		float zero=0.0;
		for ( i = 0; i < side1BayWidth.GetSize(); i++ ) {
			side2BayWidth.Add(side1BayWidth[i]);
			side2BayDepth.Add(zero);
			side2BayHeight.Add(zero);
		}
	}

	// Show the section dialog
	sectionSel = newIndexArray[AisleDialog.m_ChooseAisle_SectionVal];

	///////////////////////////////////////////////////////////
	// If we choose "New Section", we must fill in the
	// appropriate values for the new section
	///////////////////////////////////////////////////////////
	BOOL bNewSection = FALSE;
	if ( sectionSel == -1 ) {
		bNewSection = TRUE;
		if (AddNewSection(newSection) < 0)
			return;
		sectionSel = AisleDialog.m_SectionNameList.GetSize() - 1;
	}
	///////////////////////////////////////////////////////////
	// We have chosen a section already defined.  Use the 
	// index into the changeTree structure
	///////////////////////////////////////////////////////////
	else {
		bTreeHelper.GetBtSection(changesTree.treeChildren[sectionSel].fileOffset,newSection);
	}
	
	/////////////////////////////////////////////////////////
	// Display the Aisle Placement dialog and get X,Y,Z and
	// orientation.  (Set up values on dialog first.)
	/////////////////////////////////////////////////////////
	
	PlaceAisleDialog.m_Width.Format("%.0f", side1Profile.GetMaxBayDepth() + side2Profile.GetMaxBayDepth() + 
		anAisleProfile.m_AisleSpace + anAisleProfile.m_LeftSpace + anAisleProfile.m_RightSpace);
	
	PlaceAisleDialog.m_AddingMultiples = (AisleDialog.m_ChooseAisle_NumAisles > 1);

	if (side1Profile.CalculateLength() > side2Profile.CalculateLength() )
		PlaceAisleDialog.m_Length.Format("%.0f",  side1Profile.CalculateLength());
	else
		PlaceAisleDialog.m_Length.Format("%.0f", side2Profile.CalculateLength());
	
	if (side1Profile.CalculateMaxBayHeight() > side2Profile.CalculateMaxBayHeight())
		PlaceAisleDialog.m_Height.Format("%.0f", side1Profile.CalculateMaxBayHeight());
	else
		PlaceAisleDialog.m_Height.Format("%.0f", side2Profile.CalculateMaxBayHeight());

	PlaceAisleDialog.m_Rotation.Format("90");
	PlaceAisleDialog.m_ObjectType = CObjectPlaceDialog::AddAisle;

	if ( PlaceAisleDialog.DoModal() == IDCANCEL ) {
		controlService.Log("", "User cancelled add aisle operation.\n");
		return;
	}


	double rotation = atof(PlaceAisleDialog.m_Rotation);
	int increment = atoi(PlaceAisleDialog.m_Increment);


	// Now that they can't canel anymore, add the section
	if (bNewSection) {
		CWaitCursor cwc;
		try {
			tempReturn = bTreeHelper.AddSectionToFacility(newSection,changesTree );
			if ( tempReturn != 0 ) {
				controlService.Log("Error adding new section.", "bTreeHelper.AddSectionToFacility: %dn", tempReturn);
				return;
			}
		}
		catch(...) {
			controlService.Log("Error adding new section.", "bTreeHelper.AddSectionToFacility: generic exception.\n");
			return;
		}
	}
	

	///////////////////////////////////////////////////////////
	// Get information from dialog once filled in
	///////////////////////////////////////////////////////////
	insertedAisle.setDescription(PlaceAisleDialog.m_Name);
	currentBay = "";
	cornerPoint[X] = atoi(PlaceAisleDialog.m_XCoordinate);
	cornerPoint[Y] = atoi(PlaceAisleDialog.m_YCoordinate);
	cornerPoint[Z] = atoi(PlaceAisleDialog.m_ZCoordinate);

	holdCornerPoint[X] = cornerPoint[X];
	holdCornerPoint[Y] = cornerPoint[Y];
	holdCornerPoint[Z] = cornerPoint[Z];

	int numAisles = AisleDialog.m_ChooseAisle_NumAisles;
	CString progressMsg;
	CProgressMessage progressDlg(progressMsg, 1, numAisles, 1, utilityHelper.GetParentWindow());
	qqhSLOTAisle currentAisle = insertedAisle;

	for ( z = 1; z <= numAisles; z++) {
		progressMsg.Format("Creating aisle %d of %d", z, numAisles);
		progressDlg.UpdateMessage(progressMsg);

		side1BayYCoord.RemoveAll();

		///////////////////////////////////////////////////////////
		// For Multi-Aisle Drops, we update the ID of the Aisle
		// as we go.
		///////////////////////////////////////////////////////////
		previousDesc = currentAisle.getDescription();
		
		currentAisle = insertedAisle;
		if ( z == 1)
			currentAisle.setDescription(numberingService.FindNextID(previousDesc, increment,0,1,newSection.getLocationMask(),4));
		else
			currentAisle.setDescription(numberingService.FindNextID(previousDesc, increment,0,0,newSection.getLocationMask(),4));

		///////////////////////////////////////////////////////////
		// Drop the bays in from the side profile : Side1
		///////////////////////////////////////////////////////////
		if ( side1Profile.m_SideProfileDBId > 0) {

			for (i = 0; i < side1Profile.m_BayProfileList.GetSize(); i++ ) {
				
				if (controlService.m_Debug)
					ads_printf("Drawing: %d\n", i);
				
				CBayProfile *pBayProfile = new CBayProfile(*side1Profile.m_BayProfileList[i]);
				numItemsProcessed++;
				

				///////////////////////////////////////////////////////////
				// Calculations of the coordinates to insert the bay at
				///////////////////////////////////////////////////////////
				
				// assume the coordinates are 0,0,0 which is the center of the bay
				// when we first drop it in

				// adjust the bay so that the front left corner (lower left) is at the origin
				// this means offsetting it by half the width and half the depth
				// we also have to take into account the upright width
				// and the fact that bays in the aisle may have different depths
				// and we want them all to line up at the front
				currentBayCenter[X] = side1BayWidth[i]/2 + side1BarWidth[i]; 
				if (side1BayDepth[i] <= side1BayDepth[0] ) 
					currentBayCenter[Y] = -1*(side1BayDepth[i]/2)-(side1BayDepth[0]-side1BayDepth[i]);
				else
					currentBayCenter[Y] = -1*(side1BayDepth[0]/2) + (side1BayDepth[i] - side1BayDepth[i]/2 - side1BayDepth[0] + side1BayDepth[0]/2);
				
				// If we are adding more than one aisle at a time, move the y over past the previous aisles
				if ( z > 1 )
					currentBayCenter[Y] -= ( (z-1) * anAisleProfile.m_LeftSpace) + 
					( (z-1) * anAisleProfile.m_RightSpace ) +
					( (z-1) * 
					(side1Profile.GetMaxBayDepth() + side2Profile.GetMaxBayDepth() + anAisleProfile.m_AisleSpace) );

				///////////////////////////////////////////////////////////
				// Used to drop right side of the aisle
				///////////////////////////////////////////////////////////
				side1BayYCoord.Add(currentBayCenter[Y]);

				// now adjust the coordinates so to take into account any previous
				// bays that are in the aisle before this one
				// each previous one consists of a bay width and a bar width
				if ( i > 0 ) {
					for (j = i-1; j >= 0; j--)
						currentBayCenter[X] += side1BayWidth[j] + side1BarWidth[j];
				}

				///////////////////////////////////////////////////////////
				// Special processing for floor baytypes -- height is 
				// ignored.
				///////////////////////////////////////////////////////////
				if ( pBayProfile->m_BayType == BAYTYPE_FLOOR) {
					currentBayCenter[Z] = 0.05;
					rotatedBayCenter[Z] = 0.05;
					pBayProfile->m_Height = 0.1;				
				}
				else {
					currentBayCenter[Z] = side1BayHeight[i]/2;
					rotatedBayCenter[Z] = side1BayHeight[i]/2;
				}

				// rotate the bay coordinates around the origin based on the aisle rotation
				CAutoCADCommands::RotatePoint(currentBayCenter,rotatedBayCenter,PI/180* atof(PlaceAisleDialog.m_Rotation));

				///////////////////////////////////////////////////////////
				// Find the edge of the bay to update the aisle corner point
				///////////////////////////////////////////////////////////
				holdBayCenter[X] = currentBayCenter[X];
				holdBayCenter[Y] = currentBayCenter[Y];
				holdBayCenter[Z] = currentBayCenter[Z];
		
				// hold corner point is just the coordinates of the aisle
				// add them to the bay coordinatesi so the coordinates will
				// now be consistent with the aisle
				bayCenter[X] = rotatedBayCenter[X] + holdCornerPoint[X];
				bayCenter[Y] = rotatedBayCenter[Y] + holdCornerPoint[Y];
				bayCenter[Z] = rotatedBayCenter[Z] + holdCornerPoint[Z];

				qqhSLOTBay& bay = currentAisle.getChildList()[0].getChildList()[i];

				// temp change to see if just drawing the upright every time would be easier
				AddBay(*pBayProfile, C3DPoint(bayCenter[X], bayCenter[Y], bayCenter[Z]),
					rotation, 
					bay, side1BarWidth[i], //pBayProfile->m_UprightWidth,
					(i == side1Profile.m_BayProfileList.GetSize()-1) ? pBayProfile->m_UprightWidth : pBayProfile->m_UprightWidth,
					0);

							
				// Update the aisle with the corner point of the first bay
				// brd - this must be relevant only if we are adding multiple aisles
				// because the first aisle should be the same as the object placement coords
				currentBayCenter[X] -= side1BayWidth[i]/2 + side1BarWidth[i];
				currentBayCenter[Y] += side1BayDepth[i]/2;

				if (pBayProfile->m_BayType != BAYTYPE_FLOOR)
					currentBayCenter[Z] -= side1BayHeight[i]/2;

				if ( i == 0 ) {			
					///////////////////////////////////////////////////////////
					// Sweep the corner point along the angle the user chose.
					// rotatedBayCorner is used for the aisle coordinate
					///////////////////////////////////////////////////////////
					CAutoCADCommands::RotatePoint(currentBayCenter, rotatedBayCorner,PI/180*rotation);

					currentAisle.getCoord().setX((int)(rotatedBayCorner[X] + holdCornerPoint[X]));
					currentAisle.getCoord().setY((int)(rotatedBayCorner[Y] + holdCornerPoint[Y]));
					currentAisle.getCoord().setZ((int)(rotatedBayCorner[Z] + holdCornerPoint[Z]));
				}

			}
		}
		else {
			///////////////////////////////////////////////////////////
			// Special processing for "{NONE}" sides for side1
			///////////////////////////////////////////////////////////
			double zero = 0.0;
			for ( i = 0; i < side2BayWidth.GetSize(); i++ ) {
				currentBayCenter[X] = 0;
				currentBayCenter[Y] = 0;
				currentBayCenter[Z] = 0;
				if ( z > 1 ) {
					currentBayCenter[Y] -= ( (z-1)* anAisleProfile.m_LeftSpace ) + 
					( (z-1) * anAisleProfile.m_RightSpace ) + 
					( (z-1) * (side1Profile.GetMaxBayDepth()+side2Profile.GetMaxBayDepth()+
					anAisleProfile.m_AisleSpace) );
				}
				if ( i == 0 ) {
					CAutoCADCommands::RotatePoint(currentBayCenter, rotatedBayCorner,PI/180*rotation);
					currentAisle.getCoord().setX((int)(rotatedBayCorner[X] + holdCornerPoint[X]));
					currentAisle.getCoord().setY((int)(rotatedBayCorner[Y] + holdCornerPoint[Y]));
					currentAisle.getCoord().setZ((int)(rotatedBayCorner[Z] + holdCornerPoint[Z]));
				}
				currentBayCenter[Y] -= (side1BayDepth[i]/2);
				side1BayYCoord.Add(currentBayCenter[Y]);
			}
		}

		cornerPoint[X] = holdCornerPoint[X];
		cornerPoint[Y] = holdCornerPoint[Y];
		cornerPoint[Z] = holdCornerPoint[Z];

		///////////////////////////////////////////////////////////
		// Drop the bays in from the side profile Side2
		///////////////////////////////////////////////////////////
		if (side2Profile.m_SideProfileDBId >= 0) {
			for (i = 0; i < side2Profile.m_BayProfileList.GetSize(); i++ ) {
				CBayProfile *pBayProfile = new CBayProfile(*side2Profile.m_BayProfileList[i]);
				numItemsProcessed++;

				///////////////////////////////////////////////////////////
				// Calculations for right side bay
				///////////////////////////////////////////////////////////
				currentBayCenter[X] = side2BayWidth[i]/2 + side2BarWidth[i];
				if ( i < side1BayYCoord.GetSize() )
					currentBayCenter[Y] = side1BayYCoord[i] - side1BayDepth[i]/2 - side2BayDepth[i]/2 - anAisleProfile.m_AisleSpace;
				else
					currentBayCenter[Y] = side1BayYCoord[side1BayYCoord.GetSize()-1] - side1BayDepth[side1BayYCoord.GetSize()-1]/2 - side2BayDepth[i]/2 - anAisleProfile.m_AisleSpace;
				
				//if ( z > 1 && )
				//	currentBayCenter[Y] -= ( (z-1)* anAisleProfile.getSide1Space() ) + ( (z-1) * anAisleProfile.getSide2Space() ) + ( (z-1) * (side1Profile.getMaxBayDepth()+side2Profile.getMaxBayDepth()+anAisleProfile.getAisleSpace()) );

				if ( i > 0 ) {
					for (j = i-1; j >= 0; j--)
						currentBayCenter[X] += side2BayWidth[j]+ side2BarWidth[j];
				}

				///////////////////////////////////////////////////////////
				// Special processing for floor baytype.  Height is ignored
				///////////////////////////////////////////////////////////
				if ( pBayProfile->m_BayType == BAYTYPE_FLOOR ) {
					currentBayCenter[Z] = 0.05;
					rotatedBayCenter[Z] = 0.05;
					pBayProfile->m_Height = 0.1f;
				}
				else {
					currentBayCenter[Z] = side2BayHeight[i]/2;
					rotatedBayCenter[Z] = side2BayHeight[i]/2;
				}


				///////////////////////////////////////////////////////////
				// Sweep the bay along the angle the user chose
				///////////////////////////////////////////////////////////
				CAutoCADCommands::RotatePoint(currentBayCenter,rotatedBayCenter,PI/180*rotation);

				bayCenter[X] = rotatedBayCenter[X] + holdCornerPoint[X];
				bayCenter[Y] = rotatedBayCenter[Y] + holdCornerPoint[Y];
				bayCenter[Z] = rotatedBayCenter[Z] + holdCornerPoint[Z];

				///////////////////////////////////////////////////////////
				// Insert the bay DWG into the facility
				///////////////////////////////////////////////////////////
		
				qqhSLOTBay& bay = currentAisle.getChildList()[1].getChildList()[i];

				AddBay(*pBayProfile, C3DPoint(bayCenter[X], bayCenter[Y], bayCenter[Z]),
					rotation+180, 
					bay, side2BarWidth[i],	//pBayProfile->m_UprightWidth,
					(i == 0) ? pBayProfile->m_UprightWidth : pBayProfile->m_UprightWidth,
					1);
			}
		}

		////////////////////////////////////////////////////
		//  Make a call to add the aisle in Forte
		////////////////////////////////////////////////////
		if (controlService.m_Debug)
			ads_printf("\nUpdating Facility With New Aisle...\n");

		currentAisle.setRotation(rotation);
		try {
			tempReturn = bTreeHelper.AddAisleToFacility( currentAisle, changesTree.treeChildren[sectionSel].fileOffset, changesTree );
			if (tempReturn != 0 ) {
				controlService.Log("Error adding aisle.", "Error in bTreeHelper.AddAisleToFacility: %d.\n", tempReturn);
				return;
			}
		}
		catch(...) {
			controlService.Log("Error adding aisle.", "Error in bTreeHelper.AddAisleToFacility: generic exception.\n");
			return;
		}
		ads_printf("\nAisle %d of %d Completed\n",z,AisleDialog.m_ChooseAisle_NumAisles);
		if (bNewSection && changesTree.treeChildren.GetSize() == 1) {
			ads_command(RTSTR,"_VPOINT",RTSTR,"0,0,1",RTNONE);
		}
		
		progressDlg.Step();
		
		CAutoCADCommands::Flush();

	}

	progressDlg.Hide();

	///////////////////////////////////////////////////////////
	// Alert to user
	///////////////////////////////////////////////////////////
	if (AisleDialog.m_ChooseAisle_NumAisles > 1) {
		if ( increment == 0 ) {
			AfxMessageBox("An increment was not specified during the add aisles operation.\n"
				"Update the aisle names manually if necessary.");
		}
	}
	
	///////////////////////////////////////////////////////////
	// Allow user to add pickpath.  Can you say Intuit? :-)
	///////////////////////////////////////////////////////////
	if ( AisleDialog.m_ChooseAisle_NumAisles > 1 ) {
		if (AfxMessageBox("Would you like to add a pickpath to these aisles?",MB_YESNO) == IDYES ) {
			//ads_command(RTSTR,"_VPOINT",RTSTR,"0,0,1",RTNONE);
			AddPickPath();
		}
	}
	else {
		if (AfxMessageBox("Would you like to add a pickpath to this aisle?",MB_YESNO) == IDYES ) {
			// brd - change this to only switch on the first aisle of the facility
			// because it's annoying to have the view changed if you've already set it how you 
			// want it; for now just don't do it at all
			//ads_command(RTSTR,"_VPOINT",RTSTR,"0,0,1",RTNONE);
			AddPickPath();
		}
	}
	///////////////////////////////////////////////////////////
	// If user goes a while without saving, let them know.  
	// Can you say Intuit? :-)
	///////////////////////////////////////////////////////////
	if (numItemsProcessed > 100) {
		if (AfxMessageBox("You have made a lot of changes since your last save.  Would you like to save now?",MB_YESNO) == IDYES ) {
			facilityHelper.SaveFacility();
		}
		else
			///////////////////////////////////////////////////////////
			// Allow the user 50 more updates without a save message
			///////////////////////////////////////////////////////////
			numItemsProcessed = 50;
	}
	return;
}


void CElementMaintenanceHelper::AddPickPath() 
{

	CTemporaryResourceOverride thisResource;
	CPickPathOptionDialog PickPathOptionDialog;

	AcDbObjectId aislePathId;
	AcGePoint3d lastPoint;

	int continuePath = 1;
	int tempRet = 0;
	int found2Paths,numPaths, rc;
	qqhSLOTAisle tempAisle;

	found2Paths = 0;
	numPaths = 0;

	if ( BayNum == 1 ) {
		AfxMessageBox("No Aisles available to add a pickpath to.");
		return;
	}
	///////////////////////////////////////////////////////////
	// Display Option Dialog box
	///////////////////////////////////////////////////////////
	rc = PickPathOptionDialog.DoModal();
	if (rc == IDCANCEL)
		return;
	///////////////////////////////////////////////////////////
	// Continue the "New" pickpath until the user is done
	///////////////////////////////////////////////////////////
	strcpy(oldPickPathHandle,"XXX");
	if (rc == WM_USER) {
		while ( continuePath != -1 && tempRet != -1) {
			if ( continuePath == 1 )
				tempRet = this->AddPickPathinAisle(&aislePathId,1,lastPoint);
			else
				tempRet = this->AddPickPathinAisle(&aislePathId,0,lastPoint);

			CAutoCADCommands::Flush();
			if ( tempRet != -1 ) {
				if ( ::MessageBox(NULL, "Do you wish to continue to another aisle?", "Continue pick path?", MB_YESNO) != IDYES )
					continuePath = -1;
				else
					continuePath++;
			}
		}
	}
	else {
		///////////////////////////////////////////////////////////
		// The user chose to connect two pickpaths
		///////////////////////////////////////////////////////////
		this->ConnectPickPaths();
		numItemsProcessed++;
	}

	///////////////////////////////////////////////////////////
	// Warn user as they make a lot of updates
	///////////////////////////////////////////////////////////
	if (numItemsProcessed > 100) {
		if (AfxMessageBox("You have made a lot of changes since your last save.  Would you like to save now?",MB_YESNO) == IDYES ) {
			facilityHelper.SaveFacility();
		}
		else
			numItemsProcessed = 50;
	}

	return;
}


//////////////////////////////////////////////////////////////////////
// Function Name : AddPickPathinAisle
// Classname : None
// Description : Add a pickpath to drawing
// Date Created : 10/01/98
// Author : wbh
//////////////////////////////////////////////////////////////////////
// Inputs : lastObjPtr, firstTimeFlag, lastPoint
// Outputs : none
// Explanation : 
//   Add a new pickpath to the drawing.  If we are continuing a 
//   pickpath from before (lastObjPtr != NULL) then connect them.
//   Also number the bays, levels, and locations according to the 
//   properties of the pickpath.
//////////////////////////////////////////////////////////////////////
// Algorithm : None
//////////////////////////////////////////////////////////////////////
// Revisions : 
//   Date-initials : Description_of_revision
//////////////////////////////////////////////////////////////////////
	/*******************************************************
	*
	*                       NOTICE
	*
	*  THIS SOFTWARE IS THE PROPERTY OF AND CONTAINS
	*  CONFIDENTIAL INFORMATION OF SSA GLOBAL 
	*  TECHNOLOGIES, INC., AND SHALL NOT BE COPIED,
	*  USED, NOR DISCLOSED WITHOUT EXPRESS WRITTEN
	*  AUTHORIZATION.  ALTHOUGH PUBLICATION IS NOT
	*  INTENDED, IN THE EVENT OF PUBLICATION, THE
	*  FOLLOWING NOTICE IS APPLICABLE:
	*
	*  (c) COPYRIGHT 2004 SSA GLOBAL TECHNOLOGIES, INC.
	*
	*           SSA GLOBAL TECHNOLOGIES, INC.
	*
	********************************************************/
//////////////////////////////////////////////////////////////////////
int CElementMaintenanceHelper::AddPickPathinAisle(AcDbObjectId * pathObjIdptr, int firstTime, AcGePoint3d &lastPoint) 
{
	CTemporaryResourceOverride thisResource;
	CPickPathPropertiesDialog PickPathProperties;
	ads_point startPoint;
	ads_point endPoint;
	ads_point selPoint;
	ads_name bayName;
	AcDbHandle bayHandle;
	AcDbObjectId bayObjectId,vertObjectId,pathObjectId;
	AcDbObject * bayObject;
	int getRet;
	int i,j,k,l,m;
	int foundBay = 0;
	int bayIndex, sideIndex, aisleIndex, sectionIndex;
	int aisleStoreId, sectionStoreId;
	int selIndex, bayPattern;
	CString bayStart;
	double deltaX, deltaY, diffX, diffY;
	AcGePoint3dArray lsAislePointList;
	AcGePoint3dArray rsAislePointList;
	AcGePoint3dArray cAislePointList;
	AcGePoint3dArray centerPointSide1List;
	AcGePoint3dArray centerPointSide2List;
	AcGePoint3dArray pickPathPointList;
	AcGePoint3dArray sectionPickPathPointList;
	AcDbObject * pEnt;
	AcDbBlockReference * pBlockRef;
	AcDbHandle newObjHandle;
	CWinApp * currentApp;
	currentApp = AfxGetApp();

	AcGePoint3d tempPointCenter1;
	AcGePoint3d tempPointCenter2;
	AcGePoint3d tempPointlsAisle;
	AcGePoint3d tempPointrsAisle;	AcGePoint3d tempPointcAisle;
	AcGePoint3d tempPoint;
	AcGePoint3d tempPointStart;
	AcGePoint3d tempPointEnd;
	Acad::ErrorStatus errorStatus;

	AcDbEntity * entityPtr;

	qqhSLOTSection tempSection;
	qqhSLOTAisle tempAisle;
		
	AcDb3dPolyline * pickPathLine;

    AcDbBlockTable * blockTable;
    AcDbBlockTableRecord * blockTableRecord;
    AcDbObjectId lineObjId,bayObjId;
	AcDbHandle tempDbHandle;

	double baySpace, bayDepth, bayHeight, rotateAngle;

	char handleBuf[20];
	char temphandleBuf[20];
	int prevIndex = -1;
	int swapMe,rightBay;
	CSsaStringArray fingerHandleArray;
	CArray <int, int&> fingerHandleAisles;
	CArray <int, int&> fingerHandleSides;
	CArray <int, int&> tempBayIndexList;
	CString strData;
	int notDone = 1;
	int count;
	int someNotClosed = 0;
	sectionIndex = -1;
	char newHandle[20];
	double pathLength = 0.0;
	BOOL retrievedFromDB = FALSE;
	int returnCode;

	int startIndex, endIndex, indexStep;

	CString tempHandle;
	
	CMap<int, int, CBayProfile, CBayProfile&> bayProfileMap;
	////////////////////////////////////////////////////////////////
	//  Display the dialog and get the appropriate data.  Use
	//  last information if available.
	////////////////////////////////////////////////////////////////
	LoadPreviousPickPathValues(PickPathProperties);

	///////////////////////////////////////////////////////////
	// Display the pickpath properties dialog and allow the 
	// user to enter the properties of the pickpath.
	///////////////////////////////////////////////////////////
	while (notDone==1) {
		if ( PickPathProperties.DoModal() == IDCANCEL ) {
			return -1;
		}
		else
			notDone = 0;
	}

	selIndex = PickPathProperties.m_PickPathProperties_PathType_Val;
	bayPattern = PickPathProperties.m_PickPathProperties_PatternNum;

	///////////////////////////////////////////////////////////
	// reset history information
	///////////////////////////////////////////////////////////
	SavePickPathValues(PickPathProperties);	
	
	///////////////////////////////////////////////////////////
	// If the user chooses "Finger" pickpath, we need the
	// starting and ending points.
	///////////////////////////////////////////////////////////
	if ( selIndex == 1 ) {
		////////////////////////////////////////////////////////////////
		// Get the starting point of the pick path within the aisle
		////////////////////////////////////////////////////////////////
		getRet = ads_getpoint(NULL, "Please Enter the Starting Point :", startPoint);
		ads_printf("\n");
		if ( getRet == RTCAN )
			return -1;
		if ( getRet == RTERROR ) {
			AfxMessageBox("Error Getting First Point.  Exiting.");
			return -1;
		}

		////////////////////////////////////////////////////////////////
		// Get the ending point of the pick path within the aisle
		////////////////////////////////////////////////////////////////
		getRet = ads_getpoint(startPoint, "Please Enter the Ending Point :", endPoint);
		ads_printf("\n");
		if ( getRet == RTCAN )
			return -1;
		if ( getRet == RTERROR ) {
			ads_printf("Error Getting Point \n");
			return -1;
		}
	}
	///////////////////////////////////////////////////////////
	// Continue until the user chooses a correct bay
	///////////////////////////////////////////////////////////
	rightBay = 0;
	int times = 0;

	while ( rightBay == 0 ) {
		rightBay = 1;
		////////////////////////////////////////////////////////////////
		// Get the bay starting the pick path.  The user did not choose
		// "Finger" pickpath
		////////////////////////////////////////////////////////////////
		if ( selIndex != 1 ) {
			if (times == 0) {
				AfxMessageBox("Please select the first bay in the pick path.");
				times = 1;
			}
			getRet = ads_entsel( "Please select the bay in the aisle starting the path:", bayName, selPoint);
			ads_printf("\n");
			if ( getRet == RTCAN )
				return -1;
			if ( getRet == RTERROR ) {
				AfxMessageBox("Unable to determine the selected bay.  Please try again or press escape to cancel.");
				rightBay = 0;
				ads_printf("Error Getting Bay \n");
				continue;
			}
			///////////////////////////////////////////////////////////
			// Get the unique AutoCad handle of this object
			///////////////////////////////////////////////////////////
			errorStatus = acdbGetObjectId(bayObjectId, bayName);
			if ( errorStatus != Acad::eOk ) {
				AfxMessageBox("Unable to determine the selected bay. Please try again or press escape to cancel.");
				rightBay = 0;
				ads_printf("Error finding object id\n");
				continue;
			}
			errorStatus = acdbOpenAcDbObject(bayObject,bayObjectId,AcDb::kForRead);
			if ( errorStatus != Acad::eOk ) {
				AfxMessageBox("Unable to open the selected bay.  Please try again or press escape to cancel.");
				ads_printf("Error opening object\n");
				rightBay = 0;
				continue;
			}
			bayObject->getAcDbHandle(bayHandle);
			
			bayHandle.getIntoAsciiBuffer(handleBuf);
			CString tempMsg = "Found Bay : " + CString(handleBuf);
			//AfxMessageBox(tempMsg);
		}
		else {
			///////////////////////////////////////////////////////////
			// The user chose "Finger" pickpath
			///////////////////////////////////////////////////////////
			getRet = 0;
			while ( getRet != RTERROR ) {
				AfxMessageBox("Please select the bay in each aisle that is nearest the finger path.  Press ENTER to stop.");
				getRet = ads_entsel( "Please Select the Bay in each Aisle nearest the Finger Path, '[RET]' to stop:", bayName, selPoint);
				ads_printf("\n");
				if ( getRet == RTCAN ) {
					ads_printf("Command Canceled\n");
					return -1;
					///////////////////////////////////////////////////////////
					// Reset bays back to white once all are chosen
					///////////////////////////////////////////////////////////
					for ( i = 0; i < fingerHandleArray.GetSize(); i++) {
						errorStatus = acdbCurDwg()->getAcDbObjectId(bayObjectId, FALSE, fingerHandleArray[i].GetBuffer(0));
						fingerHandleArray[i].ReleaseBuffer();
						if (errorStatus != Acad::eOk)
						{
							AfxMessageBox("Error returned from getAcDbObjectId()!");
							return -1;
						}
						errorStatus = acdbOpenAcDbEntity(entityPtr, bayObjectId, AcDb::kForWrite);
						if (errorStatus != Acad::eOk)
						{
							AfxMessageBox("Error returned from acdbOpenAcDbEntity()!");
							return -1;
						}
						
						entityPtr->setColorIndex(7);
						CString strLayerName = entityPtr->layer();
						AcDbObjectId layerId = entityPtr->layerId();
						if (layerId != AcDbObjectId::kNull)
						{
							AcDbLayerTable *pLayerTable;
							AcDbLayerTableRecord *pLayerRec;
							Acad::ErrorStatus es = acdbCurDwg()->getLayerTable(pLayerTable, AcDb::kForWrite);
							es = pLayerTable->getAt(strLayerName, pLayerRec, AcDb::kForWrite);
							if (es != Acad::eOk)
							{
								AfxMessageBox("Error getting layer!");
								return -1;
							}
							AcCmColor color;
							color.setColorIndex(7);
							pLayerRec->setColor(color);
							pLayerRec->close();
							pLayerTable->close();
						}
						entityPtr->close();
					}
				}
				else if ( getRet != RTERROR ) {
					errorStatus = acdbGetObjectId(bayObjectId, bayName);
					if ( errorStatus != Acad::eOk ) {
						AfxMessageBox("Acad Error : Error Finding Object Id.");
						return -1;
					}
					///////////////////////////////////////////////////////////
					// Get the unique AutoCad handle for this bay.  Add it to
					// an array of strings
					///////////////////////////////////////////////////////////
					errorStatus = acdbOpenAcDbObject(bayObject,bayObjectId,AcDb::kForRead);
					if ( errorStatus != Acad::eOk ) {
						AfxMessageBox("Error Opening Object");
						return -1;
					}
					
					bayObject->getAcDbHandle(bayHandle);
					bayHandle.getIntoAsciiBuffer(handleBuf);
					fingerHandleArray.Add(handleBuf);
					
					bayObject->close();
					
					///////////////////////////////////////////////////////////
					// Open the bay object for Write
					///////////////////////////////////////////////////////////
					errorStatus = acdbCurDwg()->getAcDbObjectId(bayObjectId, FALSE, bayHandle);
					if (errorStatus != Acad::eOk)
					{
						AfxMessageBox("Error returned from getAcDbObjectId()!");
						return -1;
					}
					errorStatus = acdbOpenAcDbEntity(entityPtr, bayObjectId, AcDb::kForWrite);
					if (errorStatus != Acad::eOk)
					{
						AfxMessageBox("Error returned from acdbOpenAcDbEntity()!");
						return -1;
					}
					
					///////////////////////////////////////////////////////////
					// Ease of use enhancement - color bays chosen red as they
					// select them.  Do this by coloring the layer.
					///////////////////////////////////////////////////////////
					entityPtr->setColorIndex(1);
					CString strLayerName = entityPtr->layer();
					AcDbObjectId layerId = entityPtr->layerId();
					if (layerId != AcDbObjectId::kNull)
					{
						AcDbLayerTable *pLayerTable;
						AcDbLayerTableRecord *pLayerRec;
						Acad::ErrorStatus es = acdbCurDwg()->getLayerTable(pLayerTable, AcDb::kForWrite);
						es = pLayerTable->getAt(strLayerName, pLayerRec, AcDb::kForWrite);
						if (es != Acad::eOk)
						{
							AfxMessageBox("Error getting layer!");
							return -1;
						}
						AcCmColor color;
						color.setColorIndex(1);
						pLayerRec->setColor(color);
						pLayerRec->close();
						pLayerTable->close();
					}
					entityPtr->close();
				}
			}
			///////////////////////////////////////////////////////////
			// Reset bays back to white once all are chosen
			///////////////////////////////////////////////////////////
			for ( i = 0; i < fingerHandleArray.GetSize(); i++) {
				errorStatus = acdbCurDwg()->getAcDbObjectId(bayObjectId, FALSE, fingerHandleArray[i].GetBuffer(0));
				fingerHandleArray[i].ReleaseBuffer();
				if (errorStatus != Acad::eOk)
				{
					AfxMessageBox("Error returned from getAcDbObjectId()!");
					return -1;
				}
				errorStatus = acdbOpenAcDbEntity(entityPtr, bayObjectId, AcDb::kForWrite);
				if (errorStatus != Acad::eOk)
				{
					AfxMessageBox("Error returned from acdbOpenAcDbEntity()!");
					return -1;
				}
				
				entityPtr->setColorIndex(7);
				CString strLayerName = entityPtr->layer();
				AcDbObjectId layerId = entityPtr->layerId();
				if (layerId != AcDbObjectId::kNull)
				{
					AcDbLayerTable *pLayerTable;
					AcDbLayerTableRecord *pLayerRec;
					Acad::ErrorStatus es = acdbCurDwg()->getLayerTable(pLayerTable, AcDb::kForWrite);
					es = pLayerTable->getAt(strLayerName, pLayerRec, AcDb::kForWrite);
					if (es != Acad::eOk)
					{
						AfxMessageBox("Error getting layer!");
						return -1;
					}
					AcCmColor color;
					color.setColorIndex(7);
					pLayerRec->setColor(color);
					pLayerRec->close();
					pLayerTable->close();
				}
				entityPtr->close();
			}
		}

		CProcessingMessage processDlg("Adding Pick Path", utilityHelper.GetParentWindow());

		if ( selIndex != 1 ) {
			////////////////////////////////////////////////////////////////
			// Find the Aisle that the bay is in
			////////////////////////////////////////////////////////////////
			foundBay = 0;
			count = 1;
			while (bayObject->close() != Acad::eOk && count < 10 )
				count++;
			if ( count == 10 )
				someNotClosed = 1;
			if ( foundBay == 1 ) {
				i--; j--; k--; l--;
				bayIndex = l;
				sideIndex = k;
				aisleIndex = j;
				sectionIndex = i;
			}
			else {
				///////////////////////////////////////////////////////////
				// Retrieve Aisle from the DB if it is not in the 
				// changes temp file.
				///////////////////////////////////////////////////////////
				try {
					returnCode = bTreeHelper.UpdateBTWithAisleForPickPath(CString(handleBuf), changesTree, &bayIndex, &aisleIndex, &sectionIndex, &sideIndex);
					if (returnCode == 0) {
						retrievedFromDB = TRUE;
						aisleStoreId = changesTree.treeChildren[sectionIndex].treeChildren[aisleIndex].fileOffset;
						sectionStoreId = changesTree.treeChildren[sectionIndex].fileOffset;
						bTreeHelper.GetBtAisle(aisleStoreId,tempAisle);
						///////////////////////////////////////////////////////////
						// If the aisle already has a pickpath in it, we
						// will not allow the user to add another.
						///////////////////////////////////////////////////////////
						if (strcmp(tempAisle.getPickPath().getAcadHandle(),"XXX") != 0 && strcmp(tempAisle.getPickPath().getAcadHandle(),"") != 0) {
							errorStatus = acdbCurDwg()->getAcDbObjectId(pathObjectId,Adesk::kFalse,tempAisle.getPickPath().getAcadHandle());
							if (errorStatus == Acad::eOk) {
								errorStatus = acdbOpenAcDbObject(pEnt, pathObjectId, AcDb::kForRead);
								if (errorStatus == Acad::eOk)
									pEnt->close();
								if ( errorStatus == Acad::eOk) {
									AfxMessageBox("This aisle already has a pick path."
										" Please select another aisle or press escape to cancel.");
									rightBay = 0;
									continue;
								}
							}
						}
					}
					else {
						// This is either a new facility or we have a real error
						if (GetAisleFromTreeByBay(handleBuf, tempAisle, sectionIndex, aisleIndex, sideIndex, bayIndex) < 0) {
							AfxMessageBox("Unable to determine the aisle from the selected object. Please try again or press escape to cancel.");
							rightBay = 0;
							continue;
						}
						retrievedFromDB = FALSE;
						if (strcmp(tempAisle.getPickPath().getAcadHandle(),"XXX") != 0 && strcmp(tempAisle.getPickPath().getAcadHandle(),"") != 0) {
							acdbCurDwg()->getAcDbObjectId(pathObjectId,Adesk::kFalse,tempAisle.getPickPath().getAcadHandle());
							
							errorStatus = acdbOpenAcDbObject(pEnt, pathObjectId, AcDb::kForRead);
							pEnt->close();
							if ( errorStatus == Acad::eOk) {
								AfxMessageBox("This aisle already has a pick path.  Please select another aisle or press escape to cancel. ");
								rightBay = 0;
								continue;
							}
						}				
					}
				}
				catch(...) {
					AfxMessageBox("Unable to determine the aisle from the selected object."
						" Please try again or press escape to cancel.");
					return -1;
				}
			}
			///////////////////////////////////////////////////////////
			// Verify the bay chosen is at the end or beginning of 
			// the aisle
			///////////////////////////////////////////////////////////
			if ( bayIndex != 0 && bayIndex != changesTree.treeChildren[sectionIndex].treeChildren[aisleIndex].treeChildren[sideIndex].treeChildren.GetSize() - 1) {
				ads_printf("Bay Chosen is not at the beginning/ending of the aisle\n");
				AfxMessageBox("The first bay in the pick path must be at the beginning or the end of the aisle. "
					"Please try again or press escape to cancel");
				rightBay = 0;
				continue;
			}
			else {
				///////////////////////////////////////////////////////////
				// Special processing for one-bay aisles
				///////////////////////////////////////////////////////////
				if (bayIndex == 0 && bayIndex == changesTree.treeChildren[sectionIndex].treeChildren[aisleIndex].treeChildren[sideIndex].treeChildren.GetSize() - 1) {
					int oneNum = 1;
					if (AfxMessageBox("Only one bay in Aisle.  Number locations Ascending?",MB_YESNO) == IDNO )
						tempBayIndexList.Add(oneNum);
					else
						tempBayIndexList.Add(bayIndex);
				}
				else
					tempBayIndexList.Add(bayIndex);
			}
		}
		else {
			for ( m = 0; m < fingerHandleArray.GetSize(); m++ ) {
				foundBay = 0;
				for ( i = 0; i < changesTree.treeChildren.GetSize() && foundBay== 0; i++ ) {
					//Sections
					for ( j = 0; j < changesTree.treeChildren[i].treeChildren.GetSize() && foundBay == 0; j++ ) {
						//Aisles
						for (k = 0; k < changesTree.treeChildren[i].treeChildren[j].treeChildren.GetSize() && foundBay == 0; k++ ) {
							//Sides
							for (l = 0; l < changesTree.treeChildren[i].treeChildren[j].treeChildren[k].treeChildren.GetSize() && foundBay == 0; l++ ) {
								//Bays -- look here
								strcpy(temphandleBuf,fingerHandleArray[m].GetBuffer(0));
								fingerHandleArray[m].ReleaseBuffer();
								strcpy(temphandleBuf,changesTree.treeChildren[i].treeChildren[j].treeChildren[k].treeChildren[l].acadHandle);
								///////////////////////////////////////////////////////////
								// Do not allow the user to put more than one pickpath
								// in an aisle.
								///////////////////////////////////////////////////////////
								if ( strcmp(changesTree.treeChildren[i].treeChildren[j].treeChildren[k].treeChildren[l].acadHandle,fingerHandleArray[m].GetBuffer(0)) == 0 ) {
									foundBay = 1;
									aisleStoreId = changesTree.treeChildren[i].treeChildren[j].fileOffset;
									sectionStoreId = changesTree.treeChildren[i].fileOffset;
									bTreeHelper.GetBtAisle(aisleStoreId,tempAisle);	
									if (strcmp(tempAisle.getPickPath().getAcadHandle(),"XXX") != 0  && strcmp(tempAisle.getPickPath().getAcadHandle(),"") != 0 ) {						
										errorStatus = acdbCurDwg()->getAcDbObjectId(pathObjectId,Adesk::kFalse,tempAisle.getPickPath().getAcadHandle());					
										if (errorStatus == Acad::eOk) {							
											errorStatus = acdbOpenAcDbObject(pEnt, pathObjectId, AcDb::kForRead);														
											if ( errorStatus == Acad::eOk) {
												pEnt->close();
												AfxMessageBox("This aisle already has a pick path. Exiting.");
												return -1;
											}
										}
									}								
								}
								fingerHandleArray[m].ReleaseBuffer();
							}
						}
					}
				}
				count = 1;
				while (bayObject->close() != Acad::eOk && count < 10 )
					count++;
				if ( count == 10 )
					someNotClosed = 1;
				if ( foundBay == 1 ) {
					i--; j--; k--; l--;
					bayIndex = l;
					sideIndex = k;
					aisleIndex = j;
					fingerHandleAisles.Add(j);
					fingerHandleSides.Add(k);
					sectionIndex = i;
					
				}
				else {
					///////////////////////////////////////////////////////////
					// Retrieve aisle to temp file from DB if it is not 
					// already there
					///////////////////////////////////////////////////////////
					currentApp->DoWaitCursor(1);
					try {
						returnCode = bTreeHelper.UpdateBTWithAisleForPickPath(fingerHandleArray[m], changesTree, &bayIndex, &aisleIndex, &sectionIndex, &sideIndex);
						if (returnCode == 0) {
							retrievedFromDB = TRUE;
							aisleStoreId = changesTree.treeChildren[sectionIndex].treeChildren[aisleIndex].fileOffset;
							sectionStoreId = changesTree.treeChildren[sectionIndex].fileOffset;
							l = bayIndex;
							bTreeHelper.GetBtAisle(aisleStoreId,tempAisle);
							///////////////////////////////////////////////////////////
							// Do not allow the user to put more than one pickpath
							// in an aisle.
							///////////////////////////////////////////////////////////
							if (strcmp(tempAisle.getPickPath().getAcadHandle(),"XXX") != 0 && strcmp(tempAisle.getPickPath().getAcadHandle(),"") != 0) {
								errorStatus = acdbCurDwg()->getAcDbObjectId(pathObjectId,Adesk::kFalse,tempAisle.getPickPath().getAcadHandle());
								if (errorStatus == Acad::eOk) {
									
									errorStatus = acdbOpenAcDbObject(pEnt, pathObjectId, AcDb::kForRead);
									
									if ( errorStatus == Acad::eOk) {
										pEnt->close();
										AfxMessageBox("This aisle already has a pick path.  Exiting pick path insertion. ");
										return -1;
									}
								}
							}
						}
						else {
							AfxMessageBox("Unable to locate Aisle in facility : 1\n");
							AfxMessageBox(fingerHandleArray[m]);
							return -1;
						}
					}
					catch(...) {
						AfxMessageBox("Unable to locate Aisle in facility : 2\n");
						AfxMessageBox(fingerHandleArray[m]);
						return -1;
					}
					currentApp->DoWaitCursor(-1);
					fingerHandleAisles.Add(aisleIndex);
					fingerHandleSides.Add(sideIndex);
				}
				///////////////////////////////////////////////////////////
				// For "Finger" pickpaths, the aisle chosen must be in
				// the same section
				///////////////////////////////////////////////////////////
				if ( sectionIndex != prevIndex && prevIndex != -1 ) {
					AfxMessageBox("Please select Aisles in the same section for finger pick path");
					rightBay = 0;
					return -1;
				}
				///////////////////////////////////////////////////////////
				// Make sure the bay chosen is at the beginning or ending
				// of the aisle
				///////////////////////////////////////////////////////////
				prevIndex = sectionIndex;
				if ( l != 0 && l != changesTree.treeChildren[sectionIndex].treeChildren[aisleIndex].treeChildren[sideIndex].treeChildren.GetSize() - 1) {
					ads_printf("Bay Chosen is not at the beginning/ending of the aisle\n");
					rightBay = 0;
				}
				else{
					///////////////////////////////////////////////////////////
					// Special processing for single-bay aisles
					///////////////////////////////////////////////////////////
					if (bayIndex == 0 && bayIndex == changesTree.treeChildren[sectionIndex].treeChildren[aisleIndex].treeChildren[sideIndex].treeChildren.GetSize() - 1) {
						int oneNum = 1;
						if (AfxMessageBox("Only one bay in Aisle.  Number locations Ascending?",MB_YESNO) == IDNO )
							tempBayIndexList.Add(oneNum);
						else
							tempBayIndexList.Add(bayIndex);
					}
					else
						tempBayIndexList.Add(bayIndex);
				}
			}
		}
	}

	currentApp->DoWaitCursor(1);

	////////////////////////////////////////////////////////////////
	// Get the center points of the bays and fill the appropriate
	// AcGePoint3dArray.  This is gotten by reading the result
	// buffers that are associated with autocad drawing objects.
	////////////////////////////////////////////////////////////////
	///////////////////////////////////////////////////////////
	// This is only done if the pickpath chosen is not a 
	// "Finger" pickpath.  We already have the point if it is
	///////////////////////////////////////////////////////////

	if ( selIndex != 1 ) {
		////////////////////////////////////////////////////////////////
		// Side 1
		////////////////////////////////////////////////////////////////
		if ( bayIndex == 0 ) {
			startIndex = 0; 
			endIndex = changesTree.treeChildren[sectionIndex].treeChildren[aisleIndex].treeChildren[0].treeChildren.GetSize();
			indexStep = 1;
		}
		else {
			startIndex = changesTree.treeChildren[sectionIndex].treeChildren[aisleIndex].treeChildren[0].treeChildren.GetSize() - 1;
			endIndex = -1;
			indexStep = -1;
		}
		for ( i = startIndex; i != endIndex; i += indexStep ) {

			///////////////////////////////////////////////////////////
			// For each bay in this side, get the coordinates and
			// the extended data to find the center point list of the 
			// aisle
			///////////////////////////////////////////////////////////
			numItemsProcessed++;
			
			CString handle = changesTree.treeChildren[sectionIndex].treeChildren[aisleIndex].treeChildren[0].treeChildren[i].acadHandle;
			tempDbHandle = handle.GetBuffer(0);
			handle.ReleaseBuffer();

			errorStatus = acdbCurDwg()->getAcDbObjectId(bayObjectId,Adesk::kFalse,tempDbHandle);
			if (errorStatus != Acad::eOk) {
				CString msg;
				msg.Format("Unable to find bay object: %s.  Run Check Facility.", handle);
				AfxMessageBox(msg);
				currentApp->DoWaitCursor(-1);
				return -1;
			}
			
			errorStatus = acdbOpenAcDbObject(pEnt, bayObjectId, AcDb::kForRead);
			if ( errorStatus != Acad::eOk) {
				CString msg;
				msg.Format("Unable to open bay object: %s. Run Check Facility.", handle);
				AfxMessageBox(msg);
				currentApp->DoWaitCursor(-1);
				return -1;
			}

			
			qqhSLOTBay bay;
			bTreeHelper.GetBtBay(changesTree.treeChildren[sectionIndex].treeChildren[aisleIndex].treeChildren[0].treeChildren[i].fileOffset, bay);
			if (bay.pBayProfile == NULL) {
				CBayProfile tempBayProfile;
				if (! bayProfileMap.Lookup(bay.getBayProfileId(), tempBayProfile)) {
					try {
						bayProfileDataService.GetBayProfile(bay.getBayProfileId(), tempBayProfile, 0);
					}
					catch (...) {
						controlService.Log("Error getting bay profile information for bay.", 
							"Generic exception in GetBayProfile\n");
						return -1;
					}
					bay.pBayProfile = new CBayProfile(tempBayProfile);
					bayProfileMap.SetAt(bay.pBayProfile->m_BayProfileDBId, tempBayProfile);
				}
				else {
					bay.pBayProfile = new CBayProfile(tempBayProfile);
				}
			}

			bayDepth = bay.pBayProfile->m_Depth;
			bayHeight = bay.pBayProfile->m_UprightHeight;
			baySpace = tempAisle.getAisleSpace();

			///////////////////////////////////////////////////////////
			// Rotation (in radians) and position of bay
			///////////////////////////////////////////////////////////
			pBlockRef = (AcDbBlockReference *)(pEnt);
			rotateAngle = pBlockRef->rotation();
			tempPoint = pBlockRef->position();

			CString msg;
			msg.Format("d-%f h-%f s-%f x-%f y-%f z-%f r-%f", bayDepth, bayHeight, baySpace,
				tempPoint[X], tempPoint[Y], tempPoint[Z], rotateAngle);
			//AfxMessageBox(msg);
			
			tempPoint[Z] = tempPoint[Z] - bayHeight/2;

			// adjust the point out to the front of the bay
			// this is basically rotating the a point but we know that the x coordinate of the point
			// is 0 and the y is (-depth/2) (start with the bay rotated to 0 and subtract out the depth/2, 
			//	ignoring the width) so we skip the x part of the rotation
			tempPoint[X] = tempPoint[X] + bayDepth/2*sin(rotateAngle);	//(sin(90) = 1)
			tempPoint[Y] = tempPoint[Y] - bayDepth/2*cos(rotateAngle);	//(cos(90) = 0)

			centerPointSide1List.append(tempPoint);
			count = 1;
			while (pEnt->close() != Acad::eOk && count < 10 )
				count++;
			if ( count == 10 )
				someNotClosed = 1;
		}
		////////////////////////////////////////////////////////////////
		// Side 2
		////////////////////////////////////////////////////////////////

		if ( bayIndex == 0 ) {
			startIndex = 0; 
			endIndex = changesTree.treeChildren[sectionIndex].treeChildren[aisleIndex].treeChildren[1].treeChildren.GetSize();
			indexStep = 1;
		}
		else {
			startIndex = changesTree.treeChildren[sectionIndex].treeChildren[aisleIndex].treeChildren[1].treeChildren.GetSize() - 1;
			endIndex = -1;
			indexStep = -1;
		}
		for ( i = startIndex; i != endIndex; i += indexStep ) {
			
			///////////////////////////////////////////////////////////
			// For each bay in this side, get the coordinates and
			// the extended data to find the center point list of the 
			// aisle
			///////////////////////////////////////////////////////////
			numItemsProcessed++;
			
			CString handle = changesTree.treeChildren[sectionIndex].treeChildren[aisleIndex].treeChildren[1].treeChildren[i].acadHandle;
			tempDbHandle = handle.GetBuffer(0);
			handle.ReleaseBuffer();

			errorStatus = acdbCurDwg()->getAcDbObjectId(bayObjectId,Adesk::kFalse,tempDbHandle);
			if ( errorStatus != Acad::eOk) {
				CString msg;
				msg.Format("Unable to find bay object in drawing: %s.  Run Check Facility.\n", handle);
				AfxMessageBox(msg);
				currentApp->DoWaitCursor(-1);
				return -1;
			}

			errorStatus = acdbOpenAcDbObject(pEnt, bayObjectId, AcDb::kForRead);
			if ( errorStatus != Acad::eOk) {
				CString msg;
				msg.Format("Unable to open bay object: %s.  Run Check Facility.", handle);
				AfxMessageBox(msg);
				currentApp->DoWaitCursor(-1);
				return -1;
			}


			qqhSLOTBay bay;
			bTreeHelper.GetBtBay(changesTree.treeChildren[sectionIndex].treeChildren[aisleIndex].treeChildren[1].treeChildren[i].fileOffset, bay);
			if (bay.pBayProfile == NULL) {
				CBayProfile tempBayProfile;
				if (! bayProfileMap.Lookup(bay.getBayProfileId(), tempBayProfile)) {
					try {
						bayProfileDataService.GetBayProfile(bay.getBayProfileId(), tempBayProfile, 0);
					}
					catch (...) {
						controlService.Log("Error getting bay profile information for bay.", 
							"Generic exception in GetBayProfile\n");
						return -1;
					}
					bayProfileMap.SetAt(tempBayProfile.m_BayProfileDBId, tempBayProfile);
					bay.pBayProfile = new CBayProfile(tempBayProfile);
				}
				else {
					bay.pBayProfile = new CBayProfile(tempBayProfile);
				}
			}

			bayDepth = bay.pBayProfile->m_Depth;
			bayHeight = bay.pBayProfile->m_UprightHeight;
			baySpace = tempAisle.getAisleSpace();

			///////////////////////////////////////////////////////////
			// Rotation (in radians) and position of bay
			///////////////////////////////////////////////////////////
			pBlockRef = (AcDbBlockReference *)(pEnt);
			rotateAngle = pBlockRef->rotation();
			tempPoint = pBlockRef->position();

			tempPoint[Z] = tempPoint[Z] - bayHeight/2;
			tempPoint[X] = tempPoint[X] - bayDepth/2*sin(rotateAngle-PI);
			tempPoint[Y] = tempPoint[Y] + bayDepth/2*cos(rotateAngle-PI);

			centerPointSide2List.append(tempPoint);
			
			count = 1;
			while (pEnt->close() != Acad::eOk && count < 10 )
				count++;
			if ( count == 10 )
				someNotClosed = 1;
		}
		// for one sided aisles, set the opposite side to somewhere
		// across the aisle
		if ( centerPointSide1List.logicalLength() == 0 ) {
			for ( i = 0; i < centerPointSide2List.logicalLength(); i++ ) {
				tempPoint = centerPointSide2List[i];
				tempPoint[X] = tempPoint[X] - baySpace*sin(rotateAngle - PI);
				tempPoint[Y] = tempPoint[Y] + baySpace*cos(rotateAngle - PI);
				centerPointSide1List.append(tempPoint);
			}
		}
		if ( centerPointSide2List.logicalLength() == 0 ) {
			for ( i = 0; i < centerPointSide1List.logicalLength(); i++ ) {
				tempPoint = centerPointSide1List[i];
				tempPoint[X] = tempPoint[X] + baySpace*sin(rotateAngle);
				tempPoint[Y] = tempPoint[Y] - baySpace*cos(rotateAngle);
				centerPointSide2List.append(tempPoint);
			}
		}

		////////////////////////////////////////////////////////////////
		// Build the "matrix" of points within the aisle to use to map
		// out the pick path.  The points represent the center point of
		// the aisle, the right hand side 1/2 the distance from the
		// center of the aisle to the front of the bay, and left hand
		// side, 1/2 the distance from the center of the aisle to the
		// front of the bay (in the other direction).
		////////////////////////////////////////////////////////////////
		////////////////////////////////////////////////////////////////
		// deltaX and deltaY are used in case a bay ends staggered.  The
		// logical centerline of the aisle side is continued, and is
		// used as a phantom center point of the bay.
		////////////////////////////////////////////////////////////////
		if ( centerPointSide1List.logicalLength() >= 2 ) {
			deltaX = centerPointSide1List[1][X] - centerPointSide1List[0][X];
			deltaY = centerPointSide1List[1][Y] - centerPointSide1List[0][Y];
		}
		else if( centerPointSide2List.logicalLength() >= 2 )  {
			deltaX = centerPointSide2List[1][X] - centerPointSide2List[0][X];
			deltaY = centerPointSide2List[1][Y] - centerPointSide2List[0][Y];
		}
		else {
			deltaX = 0.0;
			deltaY = 0.0;
		}

		for ( i = 0; i < centerPointSide1List.logicalLength() || i < centerPointSide2List.logicalLength(); i++) {
			if ( i < centerPointSide1List.logicalLength() )
				tempPointCenter1 = centerPointSide1List[i];
			else
				tempPointCenter1 = centerPointSide1List[centerPointSide1List.logicalLength()-1];

			if ( i < centerPointSide2List.logicalLength() )
				tempPointCenter2 = centerPointSide2List[i];
			else
				tempPointCenter2 = centerPointSide2List[centerPointSide2List.logicalLength()-1];
			
			/*
			// left side ends before right side
			if (i > centerPointSide1List.logicalLength()-1 ) {
				int skipCount = i - (centerPointSide1List.logicalLength()-1);
				int dx = skipCount*(centerPointSide2List[i][X] - centerPointSide2List[i-1][X]);
				int dy = skipCount*(centerPointSide2List[i][Y] - centerPointSide2List[i-1][Y]);
				tempPointCenter1[X] += dx;
				tempPointCenter1[Y] += dy;
			}

			// right side ends before left side
			if (i > centerPointSide2List.logicalLength()-1 ) {
				int skipCount = i - (centerPointSide2List.logicalLength()-1);
				int dx = skipCount*(centerPointSide1List[i][X] - centerPointSide1List[i-1][X]);
				int dy = skipCount*(centerPointSide1List[i][Y] - centerPointSide1List[i-1][Y]);
				tempPointCenter2[X] += dx;
				tempPointCenter2[Y] += dy;
			}
			*/

			if ( tempPointCenter1[X] != tempPointCenter2[X] ) {
				if ( tempPointCenter1[X] > tempPointCenter2[X] ) {
					diffX = tempPointCenter1[X] - tempPointCenter2[X];
					tempPointcAisle[X] = tempPointCenter2[X] + (diffX/2);
					tempPointrsAisle[X] = tempPointCenter2[X] + (diffX/4);
					tempPointlsAisle[X] = tempPointCenter2[X] + (diffX * 3/4);
				}
				else {
					diffX = tempPointCenter2[X] - tempPointCenter1[X];
					tempPointcAisle[X] = tempPointCenter1[X] + (diffX/2);
					tempPointrsAisle[X] = tempPointCenter1[X] + (diffX * 3/4);
					tempPointlsAisle[X] = tempPointCenter1[X] + (diffX/4);
				}
			}
			else {
				tempPointcAisle[X] = tempPointCenter1[X];
				tempPointrsAisle[X] = tempPointCenter1[X];
				tempPointlsAisle[X] = tempPointCenter1[X];
			}

			if ( tempPointCenter1[Y] != tempPointCenter2[Y] ) {
				if ( tempPointCenter1[Y] > tempPointCenter2[Y] ) {
					diffY = tempPointCenter1[Y] - tempPointCenter2[Y];
					tempPointcAisle[Y] = tempPointCenter1[Y] - (diffY/2);
					tempPointrsAisle[Y] = tempPointCenter1[Y]- (diffY *3/4);
					tempPointlsAisle[Y] = tempPointCenter1[Y]- (diffY/4);
				}
				else {
					diffY = tempPointCenter2[Y] - tempPointCenter1[Y];
					tempPointcAisle[Y] = tempPointCenter2[Y] - (diffY/2);
					tempPointrsAisle[Y] = tempPointCenter2[Y]- (diffY/4);
					tempPointlsAisle[Y] = tempPointCenter2[Y] - (diffY * 3/4);
				}
			}
			else {
				tempPointcAisle[Y] = tempPointCenter1[Y];
				tempPointrsAisle[Y] = tempPointCenter1[Y];
				tempPointlsAisle[Y] = tempPointCenter1[Y];
			}
			tempPointcAisle[Z] = tempPoint[Z];
			tempPointrsAisle[Z] = tempPoint[Z];
			tempPointlsAisle[Z] = tempPoint[Z];
			if ( i == 0 ) {
				tempPointCenter1[X] = tempPointcAisle[X] - deltaX;
				tempPointCenter1[Y] = tempPointcAisle[Y] - deltaY;
				tempPointCenter1[Z] = tempPoint[Z];
				cAislePointList.append(tempPointCenter1);
				tempPointCenter1[X] = tempPointrsAisle[X] - deltaX;
				tempPointCenter1[Y] = tempPointrsAisle[Y] - deltaY;
				tempPointCenter1[Z] = tempPoint[Z];
				rsAislePointList.append(tempPointCenter1);
				tempPointCenter1[X] = tempPointlsAisle[X] - deltaX;
				tempPointCenter1[Y] = tempPointlsAisle[Y] - deltaY;
				tempPointCenter1[Z] = tempPoint[Z];
				lsAislePointList.append(tempPointCenter1);
			}

			lsAislePointList.append(tempPointlsAisle);
			rsAislePointList.append(tempPointrsAisle);
			cAislePointList.append(tempPointcAisle);

			if ( i == centerPointSide1List.logicalLength() - 1) 
			{
				tempPointCenter1[X] = tempPointcAisle[X] + deltaX;
				tempPointCenter1[Y] = tempPointcAisle[Y] + deltaY;
				tempPointCenter1[Z] = tempPoint[Z];
				cAislePointList.append(tempPointCenter1);
				tempPointCenter1[X] = tempPointrsAisle[X] + deltaX;
				tempPointCenter1[Y] = tempPointrsAisle[Y] + deltaY;
				tempPointCenter1[Z] = tempPoint[Z];
				rsAislePointList.append(tempPointCenter1);
				tempPointCenter1[X] = tempPointlsAisle[X] + deltaX;
				tempPointCenter1[Y] = tempPointlsAisle[Y] + deltaY;
				tempPointCenter1[Z] = tempPoint[Z];
				lsAislePointList.append(tempPointCenter1);
			}
		}
	}
	
	ads_printf("Numbering bays, levels, and locations.");
	selIndex = PickPathProperties.m_PickPathProperties_PathType_Val;
	bayPattern = PickPathProperties.m_PickPathProperties_PatternNum;

	int LlevelBreak;
	int RlevelBreak;

	if (PickPathProperties.m_LLevelBreak == TRUE )
		LlevelBreak = 1;
	else
		LlevelBreak = 0;
	if (PickPathProperties.m_RLevelBreak == TRUE )
		RlevelBreak = 1;
	else
		RlevelBreak = 0;


	////////////////////////////////////////////////////////////////
	// Number the bays, levels and locations according to the
	// properties of the pickpath entered
	////////////////////////////////////////////////////////////////
	numberingService.NumberBays(tempBayIndexList,
				selIndex, 
				aisleIndex, 
				fingerHandleAisles,
				sideIndex, 
				PickPathProperties.m_LBayStartVal, PickPathProperties.m_RBayStart,
				PickPathProperties.m_LLevelStart, PickPathProperties.m_RLevelStart,
				PickPathProperties.m_LLocStart, PickPathProperties.m_RLocStart,
				PickPathProperties.m_LBaySchemeVal, PickPathProperties.m_RBaySchemeVal,
				PickPathProperties.m_LLevelSchemeVal, PickPathProperties.m_RLevelSchemeVal,
				PickPathProperties.m_LLocSchemeVal, PickPathProperties.m_RLocSchemeVal,
				PickPathProperties.m_LBayStepVal, PickPathProperties.m_RBayStep,
				PickPathProperties.m_LLevelStep, PickPathProperties.m_RLevelStep,
				PickPathProperties.m_LLocStep, PickPathProperties.m_RLocStep,
				LlevelBreak, RlevelBreak,
				PickPathProperties.m_LLocBreakVal, PickPathProperties.m_RLocBreakVal,
				bayPattern, 
				cAislePointList.logicalLength()-2,
				changesTree, 
				sectionIndex,
				fingerHandleSides,
				PickPathProperties.m_LBayPatternVal, PickPathProperties.m_LLevelPatternVal, PickPathProperties.m_LLocPatternVal,
				PickPathProperties.m_RBayPatternVal, PickPathProperties.m_RLevelPatternVal, PickPathProperties.m_RLocPatternVal);

	////////////////////////////////////////////////////////////////
	//  Add the start point
	////////////////////////////////////////////////////////////////
	if ( selIndex != 1 )
		pickPathPointList.append(cAislePointList[0]);
	else {
 		tempPoint[X] = startPoint[X];
		tempPoint[Y] = startPoint[Y];
		tempPoint[Z] = startPoint[Z];
		pickPathPointList.append(tempPoint);
	}
	switch ( selIndex ) {
		case 0 : 
		{
			////////////////////////////////////////////////////////////////
			//  Cross-Aisle Pick path
			////////////////////////////////////////////////////////////////
			if ( (sideIndex == 1  && bayIndex == 0) || (sideIndex == 1 && bayIndex > 0) ) {
				for ( i = 1; i < cAislePointList.logicalLength()-1; i++ ) {
						pickPathPointList.append(rsAislePointList[i]);
						pickPathPointList.append(lsAislePointList[i]);
				}
			}
			else {
				for ( i = 1; i < cAislePointList.logicalLength()-1; i++ ) {
						pickPathPointList.append(lsAislePointList[i]);
						pickPathPointList.append(rsAislePointList[i]);
				}
			}
			break;
		}
		case 2 :
		{
			////////////////////////////////////////////////////////////////
			//  Straight-Thru
			////////////////////////////////////////////////////////////////
			if ( cAislePointList.logicalLength() > 2)
				pickPathPointList.append(cAislePointList[1]);
			break;
		}
		case 3 : 
		{
			////////////////////////////////////////////////////////////////
			//  Two-way path 
			////////////////////////////////////////////////////////////////
			if ( (sideIndex == 1  && bayIndex == 0) || (sideIndex == 1 && bayIndex > 0) ) {
				for ( i = 1; i < cAislePointList.logicalLength() - 1; i++ )
					pickPathPointList.append(rsAislePointList[i]);				for ( i = cAislePointList.logicalLength() - 2; i >= 1; i-- )
					pickPathPointList.append(lsAislePointList[i]);
				pickPathPointList.append(lsAislePointList[0]);
			}
			else {
				for ( i = 1; i < cAislePointList.logicalLength() - 1; i++ )
					pickPathPointList.append(lsAislePointList[i]);
				for ( i = cAislePointList.logicalLength() - 2; i >= 1; i-- )
					pickPathPointList.append(rsAislePointList[i]);
				pickPathPointList.append(rsAislePointList[0]);
			}
			break;
		}
		case 4 : 
		{
			////////////////////////////////////////////////////////////////
			//  U-Pick path 
			////////////////////////////////////////////////////////////////
			if ( (sideIndex == 1  && bayIndex == 0) || (sideIndex == 1 && bayIndex > 0) ){
				for ( i = bayPattern; i < cAislePointList.logicalLength()-1; i +=bayPattern) {
					for ( j = 0; j < bayPattern; j++ )
						pickPathPointList.append(rsAislePointList[i-j]);
					for ( j = i - j + 1; j <= i; j++ )
						pickPathPointList.append(lsAislePointList[j]);
					if ( (i+bayPattern) >= cAislePointList.logicalLength()-2 && j <= cAislePointList.logicalLength() - 2)
						bayPattern = (cAislePointList.logicalLength()-2) - i;
				}
			}
			else {
				for ( i = bayPattern; i < cAislePointList.logicalLength()-1; i +=bayPattern) {
					for ( j = 0; j < bayPattern; j++ )
						pickPathPointList.append(lsAislePointList[i-j]);
					for ( j = i - j + 1; j <= i; j++ )
						pickPathPointList.append(rsAislePointList[j]);
					if ( (i+bayPattern) >= cAislePointList.logicalLength()-2 && j <= cAislePointList.logicalLength()-2)
						bayPattern = (cAislePointList.logicalLength()-2) - i;
				}
			}
			break;
		}
		case 5 : 
		{
			////////////////////////////////////////////////////////////////
			//  Z-Pick path
			////////////////////////////////////////////////////////////////
			swapMe = 0;
			if ( (sideIndex == 1  && bayIndex == 0) || (sideIndex == 1 && bayIndex > 0) ) {
				for ( i = 1; i < cAislePointList.logicalLength()-1; i+=bayPattern ) {
					if (swapMe == 0) {
						for ( j = 0; j < bayPattern && (i+j) < cAislePointList.logicalLength()-1; j++)
							pickPathPointList.append(rsAislePointList[i+j]);
						for ( j = 0; j < bayPattern && (i+j) < cAislePointList.logicalLength()-1; j++)
							pickPathPointList.append(lsAislePointList[i+j]);
						swapMe = 1;
						if ( (i+bayPattern) >= cAislePointList.logicalLength()-2 && (i+j) <= cAislePointList.logicalLength()-2)
							bayPattern = (cAislePointList.logicalLength()-2) - i;
						if ( bayPattern == 0 )
							bayPattern = cAislePointList.logicalLength();
					}
					else {
						for ( j = 0; j < bayPattern && (i+j) < cAislePointList.logicalLength()-1; j++)
							pickPathPointList.append(lsAislePointList[i+j]);
						for ( j = 0; j < bayPattern && (i+j) < cAislePointList.logicalLength()-1; j++)
							pickPathPointList.append(rsAislePointList[i+j]);
						swapMe = 0;
						if ( (i+bayPattern) >= cAislePointList.logicalLength()-2 && (i+j) <= cAislePointList.logicalLength()-2)
							bayPattern = (cAislePointList.logicalLength()-2) - i;
						if ( bayPattern == 0 )
							bayPattern = cAislePointList.logicalLength();
					}
				}
			}
			else {
				for ( i = 1; i < cAislePointList.logicalLength()-1; i+=bayPattern ) {
					if (swapMe == 0) {
						for ( j = 0; j < bayPattern && (i+j) < cAislePointList.logicalLength()-1; j++)
							pickPathPointList.append(lsAislePointList[i+j]);
						for ( j = 0; j < bayPattern && (i+j) < cAislePointList.logicalLength()-1; j++)
							pickPathPointList.append(rsAislePointList[i+j]);
						swapMe = 1;
						if ( (i+bayPattern) >= cAislePointList.logicalLength()-2 && (i+j) <= cAislePointList.logicalLength()-2)
							bayPattern = (cAislePointList.logicalLength()-2) - i;
						if ( bayPattern == 0 )
							bayPattern = cAislePointList.logicalLength();
					}
					else {
						for ( j = 0; j < bayPattern && (i+j) < cAislePointList.logicalLength()-1; j++)
							pickPathPointList.append(rsAislePointList[i+j]);
						for ( j = 0; j < bayPattern && (i+j) < cAislePointList.logicalLength()-1; j++)
							pickPathPointList.append(lsAislePointList[i+j]);
						swapMe = 0;
						if ( (i+bayPattern) >= cAislePointList.logicalLength()-2 && (i+j) <= cAislePointList.logicalLength()-2)
							bayPattern = (cAislePointList.logicalLength()-2) - i;
						if ( bayPattern == 0 )
							bayPattern = cAislePointList.logicalLength();
					}
				}
			}
			break;
		}
		case 1 : break;
		default : break;
	}
	////////////////////////////////////////////////////////////////
	//  Add the end point
	////////////////////////////////////////////////////////////////
	if ( selIndex != 3 && selIndex != 1)
		pickPathPointList.append(cAislePointList[cAislePointList.logicalLength()-1]);
	else if ( selIndex == 1 ) {
		tempPoint[X] = endPoint[X];
		tempPoint[Y] = endPoint[Y];
		tempPoint[Z] = endPoint[Z];
		pickPathPointList.append(tempPoint);
	}

	////////////////////////////////////////////////////////////////
	// Add the "arrow"
	////////////////////////////////////////////////////////////////
	if ( pickPathPointList.logicalLength() > 2) {
		//tempPoint = pickPathPointList[1];
		pickPathPointList.insertAt(1,cAislePointList[1]);
		pickPathPointList.insertAt(2,rsAislePointList[0]);
		pickPathPointList.insertAt(3,lsAislePointList[0]);
		pickPathPointList.insertAt(4,cAislePointList[1]);
	}
	////////////////////////////////////////////////////////////////
	//  Calculate the pathLength
	////////////////////////////////////////////////////////////////
	tempPointStart = pickPathPointList[0];
	tempPointEnd = pickPathPointList[pickPathPointList.logicalLength()-1];

	pathLength = 0;
	
	for ( i = 4; i < pickPathPointList.logicalLength(); i++) {
		pathLength += sqrt(pow(pickPathPointList[i][Y]-pickPathPointList[i-1][Y],2) + pow(pickPathPointList[i][X]-pickPathPointList[i-1][X],2));
	}
	
	/*
	if (tempPointStart[X] == tempPointEnd[X]) {
		pathLength = tempPointEnd[Y] - tempPointStart[Y];
		if (pathLength < 0)
			pathLength *= -1;
	}
	else if (tempPointStart[Y] == tempPointEnd[Y]) {
		pathLength = tempPointEnd[X] - tempPointStart[X];
		if (pathLength < 0)
			pathLength *= -1;
	}
	else {
		tempPoint[X] = tempPointEnd[X];
		tempPoint[Y] = tempPointStart[Y];
		pathLength = 
	}
	*/

	////////////////////////////////////////////////////////////////
	//  For continuing - find the point to connect with
	////////////////////////////////////////////////////////////////
	if ( firstTime != 1 ) {
		tempPoint[X] = lastPoint[X];
		tempPoint[Y] = lastPoint[Y];
//		tempPoint[X] = tempPointStart[X];
//		tempPoint[Y] = tempPointStart[Y];
		pathLength += sqrt(pow(tempPoint[Y]-lastPoint[Y],2) + pow(tempPoint[X]-lastPoint[X],2));
		pickPathPointList.insertAt(0,tempPoint);
	}

	////////////////////////////////////////////////////////////////
	//  Add the 3dPolyLine AutoCad object for this pickpath
	////////////////////////////////////////////////////////////////
	pickPathLine = new AcDb3dPolyline(AcDb::k3dSimplePoly, pickPathPointList, Adesk::kTrue);
	pickPathLine->setColorIndex(100);
	pickPathLine->makeOpen();
	acdbCurDwg()->getBlockTable(blockTable,AcDb::kForRead);

	////////////////////////////////////////////////////////////////
	// Get a pointer to the MODEL_SPACE BlockTableRecord
	////////////////////////////////////////////////////////////////
	errorStatus = blockTable->getAt(ACDB_MODEL_SPACE, blockTableRecord,AcDb::kForWrite);
	if ( errorStatus != Acad::eOk ) {
		blockTable->close();
		ads_printf("Error! Cannot open Block Table\n");
	}
	////////////////////////////////////////////////////////////////
	// Append the polyline object to the database and
	// obtain its ObjId
	////////////////////////////////////////////////////////////////
	errorStatus = blockTableRecord->appendAcDbEntity(lineObjId,pickPathLine);
	if ( errorStatus != Acad::eOk ) {
		blockTableRecord->close();
		ads_printf("Error!  Cannot append Pick Path\n");
	}

	count = 1;
	while (blockTable->close() != Acad::eOk && count < 10 )
		count++;
	if ( count == 10 )
		someNotClosed = 1;
	count = 1;
	while (blockTableRecord->close() != Acad::eOk && count < 10 )
		count++;
	if ( count == 10 )
		someNotClosed = 1;

	pickPathLine->setLayer("0");
	count = 1;
	while (pickPathLine->close() != Acad::eOk && count < 10 )
		count++;
	if ( count == 10 )
		someNotClosed = 1;

	errorStatus = acdbOpenAcDbObject(pEnt, lineObjId, AcDb::kForRead);
	if ( errorStatus != Acad::eOk ) {
		AfxMessageBox("Cannot open new line entity");
		currentApp->DoWaitCursor(-1);
		return -1;
	}
	////////////////////////////////////////////////////////////////
	//  Get the unique AutoCad handle to put in the Aisle
	////////////////////////////////////////////////////////////////
	pEnt->getAcDbHandle(newObjHandle);
	memset(newHandle,0,20);
	newObjHandle.getIntoAsciiBuffer(newHandle);
	count = 1;
	while (pEnt->close() != Acad::eOk && count < 10 )
		count++;
	if ( count == 10 )
		someNotClosed = 1;

	lastPoint[X] = pickPathPointList[pickPathPointList.logicalLength()-1][X];
	lastPoint[Y] = pickPathPointList[pickPathPointList.logicalLength()-1][Y];
	lastPoint[Z] = pickPathPointList[pickPathPointList.logicalLength()-1][Z];

	pathObjIdptr = NULL;

	tempAisle.getUDFList().RemoveAll();
	tempAisle.getChildList().RemoveAll();


	if ( selIndex != 1 ) {
		//////////////////////////////////////////////////////////////////////
		// fill in aisle with pickpath properties for this aisle
		//////////////////////////////////////////////////////////////////////
		bTreeHelper.GetBtAisle(changesTree.treeChildren[sectionIndex].treeChildren[aisleIndex].fileOffset,tempAisle);
		strcpy(tempAisle.getPickPath().getAcadHandle(),newHandle);
		strcpy(tempAisle.getPickPath().getConAcadHandle(),oldPickPathHandle);
		strcpy(oldPickPathHandle,newHandle);
		strcpy(changesTree.treeChildren[sectionIndex].treeChildren[aisleIndex].acadHandle,newHandle);
		if ( bayIndex == 0 )
			tempAisle.setPickPathDirection(0);
		else
			tempAisle.setPickPathDirection(1);
		tempAisle.getPickPath().setPathLength((float)pathLength);
		tempAisle.setPickPathType(PickPathProperties.m_PickPathProperties_PathType_Val);
		tempAisle.setPickPathStartSide(sideIndex);
		tempAisle.setBaysInPattern(PickPathProperties.m_PickPathProperties_PatternNum);
		tempAisle.setLeftBayStart(PickPathProperties.m_LBayStartVal);
		tempAisle.setRightBayStart(PickPathProperties.m_RBayStart);
		tempAisle.setLeftLevelStart(PickPathProperties.m_LLevelStart); 
		tempAisle.setRightLevelStart(PickPathProperties.m_RLevelStart);
		tempAisle.setLeftLocationStart(PickPathProperties.m_LLocStart); 
		tempAisle.setRightLocationStart(PickPathProperties.m_RLocStart);
		tempAisle.setLeftBayScheme(PickPathProperties.m_LBaySchemeVal); 
		tempAisle.setRightBayScheme(PickPathProperties.m_RBaySchemeVal);
		tempAisle.setLeftLevelScheme(PickPathProperties.m_LLevelSchemeVal); 
		tempAisle.setRightLevelScheme(PickPathProperties.m_RLevelSchemeVal);
		tempAisle.setLeftLocationScheme(PickPathProperties.m_LLocSchemeVal); 
		tempAisle.setRightLocationScheme(PickPathProperties.m_RLocSchemeVal);
		tempAisle.setLeftBayStep(PickPathProperties.m_LBayStepVal); 
		tempAisle.setRightBayStep(PickPathProperties.m_RBayStep);
		tempAisle.setLeftLevelStep(PickPathProperties.m_LLevelStep); 
		tempAisle.setRightLevelStep(PickPathProperties.m_RLevelStep);
		tempAisle.setLeftLocationStep(PickPathProperties.m_LLocStep); 
		tempAisle.setRightLocationStep(PickPathProperties.m_RLocStep);
		tempAisle.setLeftLevelBreak(PickPathProperties.m_LLevelBreak);
		tempAisle.setRightLevelBreak(PickPathProperties.m_RLevelBreak);
		tempAisle.setLeftLocationBreak(PickPathProperties.m_LLocBreakVal);
		tempAisle.setRightLocationBreak(PickPathProperties.m_RLocBreakVal);
		tempAisle.setLeftBayPattern(PickPathProperties.m_LBayPatternVal);
		tempAisle.setLeftLevelPattern(PickPathProperties.m_LLevelPatternVal);
		tempAisle.setLeftLocPattern(PickPathProperties.m_LLocPatternVal);
		tempAisle.setRightBayPattern(PickPathProperties.m_RBayPatternVal);
		tempAisle.setRightLevelPattern(PickPathProperties.m_RLevelPatternVal);
		tempAisle.setRightLocPattern(PickPathProperties.m_RLocPatternVal);
		bTreeHelper.SetBtAisle(changesTree.treeChildren[sectionIndex].treeChildren[aisleIndex].fileOffset,tempAisle);
		bTreeHelper.GetBtSection(changesTree.treeChildren[sectionIndex].fileOffset,tempSection);
		tempSection.setSelDist(tempSection.getSelDist() + (float)pathLength);
		bTreeHelper.SetBtSection(changesTree.treeChildren[sectionIndex].fileOffset,tempSection);
	}
	else {
		for (i = 0; i < fingerHandleAisles.GetSize(); i++) {
			//////////////////////////////////////////////////////////////////////
			// fill in aisle with pickpath properties for this aisle
			//////////////////////////////////////////////////////////////////////
			bTreeHelper.GetBtAisle(changesTree.treeChildren[sectionIndex].treeChildren[fingerHandleAisles[i]].fileOffset,tempAisle);
			strcpy(tempAisle.getPickPath().getConAcadHandle(),oldPickPathHandle);
			strcpy(oldPickPathHandle,newHandle);
			strcpy(tempAisle.getPickPath().getAcadHandle(),newHandle);
			strcpy(changesTree.treeChildren[sectionIndex].treeChildren[fingerHandleAisles[i]].acadHandle,newHandle);
			if ( tempBayIndexList[i] == 0 )
				tempAisle.setPickPathDirection(0);
			else
				tempAisle.setPickPathDirection(1);
			tempAisle.setPickPathType(PickPathProperties.m_PickPathProperties_PathType_Val);
			tempAisle.setPickPathStartSide(fingerHandleSides[i]);
			tempAisle.setBaysInPattern(PickPathProperties.m_PickPathProperties_PatternNum);
			tempAisle.getPickPath().setPathLength((float)pathLength);
			tempAisle.setLeftBayStart(PickPathProperties.m_LBayStartVal);
			tempAisle.setRightBayStart(PickPathProperties.m_RBayStart);
			tempAisle.setLeftLevelStart(PickPathProperties.m_LLevelStart); 
			tempAisle.setRightLevelStart(PickPathProperties.m_RLevelStart);
			tempAisle.setLeftLocationStart(PickPathProperties.m_LLocStart); 
			tempAisle.setRightLocationStart(PickPathProperties.m_RLocStart);
			tempAisle.setLeftBayScheme(PickPathProperties.m_LBaySchemeVal); 
			tempAisle.setRightBayScheme(PickPathProperties.m_RBaySchemeVal);
			tempAisle.setLeftLevelScheme(PickPathProperties.m_LLevelSchemeVal); 
			tempAisle.setRightLevelScheme(PickPathProperties.m_RLevelSchemeVal);
			tempAisle.setLeftLocationScheme(PickPathProperties.m_LLocSchemeVal); 
			tempAisle.setRightLocationScheme(PickPathProperties.m_RLocSchemeVal);
			tempAisle.setLeftBayStep(PickPathProperties.m_LBayStepVal); 
			tempAisle.setRightBayStep(PickPathProperties.m_RBayStep);
			tempAisle.setLeftLevelStep(PickPathProperties.m_LLevelStep); 
			tempAisle.setRightLevelStep(PickPathProperties.m_RLevelStep);
			tempAisle.setLeftLocationStep(PickPathProperties.m_LLocStep); 
			tempAisle.setRightLocationStep(PickPathProperties.m_RLocStep);
			tempAisle.setLeftLevelBreak(PickPathProperties.m_LLevelBreak);
			tempAisle.setRightLevelBreak(PickPathProperties.m_RLevelBreak);
			tempAisle.setLeftLocationBreak(PickPathProperties.m_LLocBreakVal);
			tempAisle.setRightLocationBreak(PickPathProperties.m_RLocBreakVal);
			tempAisle.setLeftBayPattern(PickPathProperties.m_LBayPatternVal);
			tempAisle.setLeftLevelPattern(PickPathProperties.m_LLevelPatternVal);
			tempAisle.setLeftLocPattern(PickPathProperties.m_LLocPatternVal);
			tempAisle.setRightBayPattern(PickPathProperties.m_RBayPatternVal);
			tempAisle.setRightLevelPattern(PickPathProperties.m_RLevelPatternVal);
			tempAisle.setRightLocPattern(PickPathProperties.m_RLocPatternVal);
			bTreeHelper.SetBtAisle(changesTree.treeChildren[sectionIndex].treeChildren[fingerHandleAisles[i]].fileOffset,tempAisle);
		}
		bTreeHelper.GetBtSection(changesTree.treeChildren[sectionIndex].fileOffset,tempSection);
		tempSection.setSelDist(tempSection.getSelDist() + (float)pathLength);
		bTreeHelper.SetBtSection(changesTree.treeChildren[sectionIndex].fileOffset,tempSection);
	}

	CAutoCADCommands::Flush();

	currentApp->DoWaitCursor(-1);
	if (someNotClosed == 1)
		AfxMessageBox("Some Autocad items did not close properly.  It is recommended that you save, exit, and reopen the application");
	
	return 0;
}

//////////////////////////////////////////////////////////////////////
// Function Name : ConnectPickPaths
// Classname : None
// Description : Connect two existing pickpaths
// Date Created : 10/01/98
// Author : wbh
//////////////////////////////////////////////////////////////////////
// Inputs : none
// Outputs : none
// Explanation : 
//   Connect two existing pickpaths
//   
//////////////////////////////////////////////////////////////////////
// Algorithm : None
//////////////////////////////////////////////////////////////////////
// Revisions : 
//   Date-initials : Description_of_revision
//////////////////////////////////////////////////////////////////////
	/*******************************************************
	*
	*                       NOTICE
	*
	*  THIS SOFTWARE IS THE PROPERTY OF AND CONTAINS
	*  CONFIDENTIAL INFORMATION OF SSA GLOBAL 
	*  TECHNOLOGIES, INC., AND SHALL NOT BE COPIED,
	*  USED, NOR DISCLOSED WITHOUT EXPRESS WRITTEN
	*  AUTHORIZATION.  ALTHOUGH PUBLICATION IS NOT
	*  INTENDED, IN THE EVENT OF PUBLICATION, THE
	*  FOLLOWING NOTICE IS APPLICABLE:
	*
	*  (c) COPYRIGHT 2004 SSA GLOBAL TECHNOLOGIES, INC.
	*
	*           SSA GLOBAL TECHNOLOGIES, INC.
	*
	********************************************************/
//////////////////////////////////////////////////////////////////////
int CElementMaintenanceHelper::ConnectPickPaths() 
{
	ads_name path1Name, path2Name;
	AcDbObjectId path1ObjectId, path2ObjectId, vertObjectId;
	AcDbObject * path1Object, * path2Object, * tempObject;
	int getRet,i,j;
	Acad::ErrorStatus errorStatus;
	ads_point selPoint;
	AcDbHandle path1Handle, path2Handle;
	AcDb3dPolyline * path1Polyline, * path2Polyline;
	AcDb3dPolylineVertex * polyLineVert;
	AcDbObjectIterator * polyLineIterator;
	AcGePoint3dArray tempPathPointArray;
	AcGePoint3d tempPoint;
	AcGePoint3d tempPoint2;
	double pathLength;
	int doneAdd;
	qqhSLOTSection tempSection;
	qqhSLOTAisle tempAisle;
	char newHandle[20];
	char newHandle2[20];
	int count, someNotClosed = 0;
	CWinApp * currentApp;
	currentApp = AfxGetApp();

	memset(newHandle,0,20);
	memset(newHandle2,0,20);

	//////////////////////////////////////////////////////////////////////
	// find the two pickpaths that are out there (polylines)
	//////////////////////////////////////////////////////////////////////
	ads_printf("\n");
	BOOL done = FALSE;

	AfxMessageBox("Select the first pick path to connect.");
	int times = 0;

	while (! done) {
		
		if (times == 3) {
			AfxMessageBox("To select a pick path, click on the green line in the center of the aisle "
				"that represents the pick path.");
			times = 0;
		}


		getRet = ads_entsel( "Please select the first path to connect", path1Name, selPoint);
		ads_printf("\n");
		if ( getRet == RTCAN )
			return -1;
		if ( getRet == RTERROR ) {
			AfxMessageBox("Unable to determine the selected pick path.  Please try again or press escape to cancel.");
			ads_printf("Error getting pick path \n");
			continue;
		}
		errorStatus = acdbGetObjectId(path1ObjectId, path1Name);
		if ( errorStatus != Acad::eOk ) {
			ads_printf("Error finding AutoCAD object id\n");
			AfxMessageBox("Unable to determine the selected pick path.  Please try again or press escape to cancel.");
			continue;
		}
		errorStatus = acdbOpenAcDbObject(path1Object,path1ObjectId,AcDb::kForWrite);
		if ( errorStatus != Acad::eOk ) {
			ads_printf("Error opening AutoCAD object\n");
			AfxMessageBox("Unable to determine the selected pick path.  Please try again or press escape to cancel.");
			continue;
		}
		ads_printf("Found first object\n");
		
		path1Object->getAcDbHandle(path1Handle);
		path1Handle.getIntoAsciiBuffer(newHandle);
		path1Object->close();

		// Look to see if it's already in the btree
		int foundAisle = 0;
		for ( i = 0; i < changesTree.treeChildren.GetSize(); i++ ) {
			for ( j = 0; j < changesTree.treeChildren[i].treeChildren.GetSize(); j++ ) {
				if ( strcmp(changesTree.treeChildren[i].treeChildren[j].acadHandle,newHandle) == 0 )
					foundAisle = 1;
			}
		}
		
		// If it's not in the btree, load it from the database
		if (foundAisle == 0) {
			CWaitCursor cwc;
			try {
				if ( bTreeHelper.UpdateBTWithAisleByPickPathAcadHandle(CString(newHandle),changesTree) < 0 ) {
					AfxMessageBox("Error finding the pick path in the database.  Please try again or press escape to cancel.");
					continue;
				}
			}
			catch(...) {
				AfxMessageBox("Error finding the pick path in the database.  Please try again or press escape to cancel.");
				continue;
			}

			foundAisle = 0;
			for ( i = 0; i < changesTree.treeChildren.GetSize(); i++ ) {
				for ( j = 0; j < changesTree.treeChildren[i].treeChildren.GetSize(); j++ ) {
					if ( strcmp(changesTree.treeChildren[i].treeChildren[j].acadHandle,newHandle) == 0 )
						foundAisle = 1;
				}
			}
			
		}

		if (! foundAisle) {
			AfxMessageBox("Error finding the pick path.  Please try again or press escape to cancel.");
			continue;
		}

		// If we make it this far, we mus thave found it
		done = TRUE;
	}

	AfxMessageBox("Select the second pick path to connect.");
	times = 0;
	done = FALSE;

	while (! done) {
		times++;

		if (times == 3) {
			AfxMessageBox("To select a pick path, click on the green line in the center of the aisle "
				"that represents the pick path.");
			times = 0;
		}

		getRet = ads_entsel( "Please select the second path to connect", path2Name, selPoint);
		ads_printf("\n");
		if ( getRet == RTCAN )
			return -1;
		if ( getRet == RTERROR ) {
			AfxMessageBox("Unable to determine the selected pick path.  Please try again or press escape to cancel.");
			ads_printf("Error getting pick path \n");
			continue;
		}
		errorStatus = acdbGetObjectId(path2ObjectId, path2Name);
		if ( errorStatus != Acad::eOk ) {
			ads_printf("Error finding AutoCAD object id\n");
			AfxMessageBox("Unable to determine the selected pick path.  Please try again or press escape to cancel.");
			continue;
		}
		if (path2ObjectId == path1ObjectId) {
			AfxMessageBox("Please select a different pick path.");
			continue;
		}

		errorStatus = acdbOpenAcDbObject(path2Object,path2ObjectId,AcDb::kForWrite);
		if ( errorStatus != Acad::eOk ) {
			ads_printf("Error opening AutoCAD object\n");
			AfxMessageBox("Unable to determine the selected pick path.  Please try again or press escape to cancel.");
			continue;
		}
		ads_printf("Found first object\n");
		
		path2Object->getAcDbHandle(path2Handle);
		path2Handle.getIntoAsciiBuffer(newHandle2);
		path2Object->close();
		
		// Look to see if it's already in the btree
		int foundAisle = 0;
		for ( i = 0; i < changesTree.treeChildren.GetSize(); i++ ) {
			for ( j = 0; j < changesTree.treeChildren[i].treeChildren.GetSize(); j++ ) {
				if ( strcmp(changesTree.treeChildren[i].treeChildren[j].acadHandle,newHandle2) == 0 )
					foundAisle = 1;
			}
		}
		
		// If it's not in the btree, load it from the database
		if (foundAisle == 0) {
			CWaitCursor cwc;
			try {
				if ( bTreeHelper.UpdateBTWithAisleByPickPathAcadHandle(CString(newHandle2),changesTree) < 0 ) {
					AfxMessageBox("Error finding the pick path in the database.  Please try again or press escape to cancel.");
					continue;
				}
			}
			catch(...) {
				AfxMessageBox("Error finding the pick path in the database.  Please try again or press escape to cancel.");
				continue;
			}

			foundAisle = 0;
			for ( i = 0; i < changesTree.treeChildren.GetSize(); i++ ) {
				for ( j = 0; j < changesTree.treeChildren[i].treeChildren.GetSize(); j++ ) {
					if ( strcmp(changesTree.treeChildren[i].treeChildren[j].acadHandle,newHandle2) == 0 )
						foundAisle = 1;
				}
			}
			
		}

		if (! foundAisle) {
			AfxMessageBox("Error finding the pick path.  Please try again or press escape to cancel.");
			continue;
		}

		// If we make it this far, we mus thave found it
		done = TRUE;
	}

	errorStatus = acdbOpenAcDbObject(path1Object,path1ObjectId,AcDb::kForWrite);
	if ( errorStatus != Acad::eOk ) {
		ads_printf("Error opening AutoCAD object\n");
		AfxMessageBox("Unable to open pick path object.");
		return -1;
	}
	
	errorStatus = acdbOpenAcDbObject(path2Object,path2ObjectId,AcDb::kForWrite);
	if ( errorStatus != Acad::eOk ) {
		ads_printf("Error opening AutoCAD object\n");
		AfxMessageBox("Unable to open pick path object.");
		path1Object->close();
		return -1;
	}

	path1Polyline = (AcDb3dPolyline *)(path1Object);
	path2Polyline = (AcDb3dPolyline *)(path2Object);

	//////////////////////////////////////////////////////////////////////
	// Run through all the points in the polyline
	//////////////////////////////////////////////////////////////////////
	polyLineIterator = path2Polyline->vertexIterator();
	polyLineIterator->start();
	while ( polyLineIterator->done() != Adesk::kTrue ) {
		vertObjectId = polyLineIterator->objectId();
		
		errorStatus = acdbOpenAcDbObject(tempObject, vertObjectId,AcDb::kForRead);
		if (errorStatus == Acad::eWasErased) {
			polyLineIterator->step();
			continue;
		}

		if ( errorStatus != Acad::eOk ) {
			ads_printf("Could not open previous line to append to\n");
			return -1;
		}
		polyLineVert = (AcDb3dPolylineVertex *)(tempObject);
		tempPoint = polyLineVert->position();
		tempPathPointArray.append(tempPoint);
		count = 1;
		while (polyLineVert->close() != Acad::eOk && count < 10 )
			count++;
		if ( count == 10 )
			someNotClosed = 1;
		//polyLineVert->close();
		count = 1;
		polyLineIterator->step();
	}

	//////////////////////////////////////////////////////////////////////
	// Add a point to the first line to connect to the second
	//////////////////////////////////////////////////////////////////////
	for ( i = 0; i < 1; i++ ) {
		polyLineVert = new AcDb3dPolylineVertex();
		polyLineVert->setPosition(tempPathPointArray[i]);
		tempPoint2 = tempPathPointArray[i];
		path1Polyline->appendVertex(polyLineVert);
		count = 1;
		while (polyLineVert->close() != Acad::eOk && count < 10 )
			count++;
		if ( count == 10 )
			someNotClosed = 1;
	}

	polyLineIterator = path1Polyline->vertexIterator();
	polyLineIterator->start();
	while ( polyLineIterator->done() != Adesk::kTrue ) {
		vertObjectId = polyLineIterator->objectId();
		errorStatus = acdbOpenAcDbObject(tempObject, vertObjectId,AcDb::kForRead);
		if (errorStatus == Acad::eWasErased) {
			polyLineIterator->step();
			continue;
		}

		if ( errorStatus != Acad::eOk ) {
			ads_printf("Could not open previous line to append to\n");
			return -1;
		}
		polyLineVert = (AcDb3dPolylineVertex *)(tempObject);
		tempPoint = polyLineVert->position();
		tempPathPointArray.append(tempPoint);
		count = 1;
		while (polyLineVert->close() != Acad::eOk && count < 10 )
			count++;
		if ( count == 10 )
			someNotClosed = 1;
		polyLineIterator->step();
	}
	doneAdd = 0;
	//////////////////////////////////////////////////////////////////////
	// Add to the distance of the aisle this last portion that was added
	//////////////////////////////////////////////////////////////////////
	pathLength = sqrt(pow(tempPoint[Y]-tempPoint2[Y],2) + pow(tempPoint[X]-tempPoint2[X],2));
	for ( i = 0 ; i < changesTree.treeChildren.GetSize() && doneAdd == 0; i++ ) {
		for ( j = 0; j < changesTree.treeChildren[i].treeChildren.GetSize(); j++) {
			bTreeHelper.GetBtAisle(changesTree.treeChildren[i].treeChildren[j].fileOffset,tempAisle);
			if ( strcmp(tempAisle.getPickPath().getAcadHandle(),newHandle2) == 0 ) {
				//////////////////////////////////////////////////////////////////////
				// Update the aisle
				//////////////////////////////////////////////////////////////////////
				tempAisle.getPickPath().setPathLength(tempAisle.getPickPath().getPathLength() + (float)pathLength);
				strcpy(tempAisle.getPickPath().getConAcadHandle(),newHandle);
				bTreeHelper.SetBtAisle(changesTree.treeChildren[i].treeChildren[j].fileOffset,tempAisle);
				if ( doneAdd == 0) {
					bTreeHelper.GetBtSection(changesTree.treeChildren[i].fileOffset,tempSection);
					tempSection.setSelDist(tempSection.getSelDist() + (float)pathLength);
					bTreeHelper.SetBtSection(changesTree.treeChildren[i].fileOffset,tempSection);
				}
				doneAdd = 1;
			}
		}
	}
	//////////////////////////////////////////////////////////////////////
	// close all Autocad objects
	//////////////////////////////////////////////////////////////////////
	count = 1;
	while (path2Polyline->close() != Acad::eOk && count < 10 )
		count++;
	if ( count == 10 )
		someNotClosed = 1;
	count = 1;
	while (path1Polyline->close() != Acad::eOk && count < 10 )
		count++;
	if ( count == 10 )
		someNotClosed = 1;
	if (someNotClosed == 1)
		AfxMessageBox("Some Autocad items did not close properly.  It is recommended that you save, exit, and reopen the application");

	return 0;
}

/////////////////////////////////////////////////////////////////////
// Function Name : AddHotSpot
// Classname : None
// Description : Add a hotspot to the drawing and facility structure
// Date Created : 10/01/98
// Author : wbh
//////////////////////////////////////////////////////////////////////
// Inputs : none
// Outputs : none
// Explanation : 
//   Allows the user to add hotspots (fork or selection) to a section.
//   Update the facility structure correspondingly.
//////////////////////////////////////////////////////////////////////
// Algorithm : None
//////////////////////////////////////////////////////////////////////
// Revisions : 
//   Date-initials : Description_of_revision
//////////////////////////////////////////////////////////////////////
	/*******************************************************
	*
	*                       NOTICE
	*
	*  THIS SOFTWARE IS THE PROPERTY OF AND CONTAINS
	*  CONFIDENTIAL INFORMATION OF SSA GLOBAL 
	*  TECHNOLOGIES, INC., AND SHALL NOT BE COPIED,
	*  USED, NOR DISCLOSED WITHOUT EXPRESS WRITTEN
	*  AUTHORIZATION.  ALTHOUGH PUBLICATION IS NOT
	*  INTENDED, IN THE EVENT OF PUBLICATION, THE
	*  FOLLOWING NOTICE IS APPLICABLE:
	*
	*  (c) COPYRIGHT 2004 SSA GLOBAL TECHNOLOGIES, INC.
	*
	*           SSA GLOBAL TECHNOLOGIES, INC.
	*
	********************************************************/
//////////////////////////////////////////////////////////////////////
void CElementMaintenanceHelper::AddHotSpot()
{
	CHotSpotDialog hotspotDialog;
	qqhSLOTSection section;

	if ( hotspotDialog.DoModal() == IDCANCEL )
		return;

	CObjectPlaceDialog dlg;


	TreeElement *sectionPtr = changesTree.getSectionByDBID(hotspotDialog.m_SectionDBId);
	if (sectionPtr == NULL) {
		controlService.Log("Error finding section.", 
			"Error getting section %d from btree.\n", hotspotDialog.m_SectionDBId);
		return;
	}

	bTreeHelper.GetBtSection(sectionPtr->fileOffset, section);

	qqhSLOTHotSpot *pHotspot = NULL;

	for (int i=0; i < section.getHotSpotList().GetSize(); ++i) {
		qqhSLOTHotSpot &hotspot = section.getHotSpotList()[i];

		if (hotspot.getHotSpotType() == hotspotDialog.m_Type) {
			pHotspot = &hotspot;
			break;
		}
	}

	if (pHotspot != NULL) {
		dlg.m_XCoordinate.Format("%d", pHotspot->getCoord().getX());
		dlg.m_YCoordinate.Format("%d", pHotspot->getCoord().getY());
		dlg.m_ZCoordinate.Format("%d", pHotspot->getCoord().getZ());
	}

	dlg.m_ObjectType = CObjectPlaceDialog::AddHotspot;
	dlg.m_Width = "100";
	dlg.m_Length = "100";
	dlg.m_Height = "10";

	if (dlg.DoModal() != IDOK)
		return;

	C3DPoint pt;
	pt.m_X = atoi(dlg.m_XCoordinate);
	pt.m_Y = atoi(dlg.m_YCoordinate);
	pt.m_Z = atoi(dlg.m_ZCoordinate);


	if (pHotspot != NULL) {
		CAutoCADCommands::DeleteDrawingObjectByHandle(pHotspot->getAcadHandle());
		CString handle;
		if (AddHotspotObject(pt, atoi(dlg.m_Width), hotspotDialog.m_Type,  handle) < 0) {
			pHotspot->getCoord().setX(0);
			pHotspot->getCoord().setY(0);
			pHotspot->getCoord().setZ(0);
			pHotspot->setAcadHandle("XXX");
		}
		else {
			pHotspot->getCoord().setX(pt.m_X);
			pHotspot->getCoord().setY(pt.m_Y);
			pHotspot->getCoord().setZ(pt.m_Z);	
			pHotspot->setAcadHandle(handle);
		}
	}
	else {
		
		qqhSLOTHotSpot hotspot;	
		CString handle;
		if (AddHotspotObject(pt, atoi(dlg.m_Width), hotspotDialog.m_Type, handle) < 0)
			return;
		else {
			hotspot.getCoord().setX(pt.m_X);
			hotspot.getCoord().setY(pt.m_Y);
			hotspot.getCoord().setZ(pt.m_Z);
			hotspot.setHotSpotType(hotspotDialog.m_Type);
			hotspot.setAcadHandle(handle);
			section.getHotSpotList().Add(hotspot);
		}
	}
	
	if (bTreeHelper.SetBtSection(sectionPtr->fileOffset, section) != 0) {
		controlService.Log("Error saving section.", "Error in SetBtSection.\n");
	}

	AfxMessageBox("The Hotspot was successfully moved.");

	return;
}

//////////////////////////////////////////////////////////////////////
// Function Name : InsertNewBayLayer
// Classname : None
// Description : Insert a layer for each bay
// Date Created : 10/01/98
// Author : wbh
//////////////////////////////////////////////////////////////////////
// Inputs : none
// Outputs : none
// Explanation : Insert a layer for each bay.  New name for each layer.
//               We do this because coloring the model colors the layer
//               of the block.
//////////////////////////////////////////////////////////////////////
// Algorithm : None
//////////////////////////////////////////////////////////////////////
// Revisions : 
//   Date-initials : Description_of_revision
//////////////////////////////////////////////////////////////////////
	/*******************************************************
	*
	*                       NOTICE
	*
	*  THIS SOFTWARE IS THE PROPERTY OF AND CONTAINS
	*  CONFIDENTIAL INFORMATION OF SSA GLOBAL 
	*  TECHNOLOGIES, INC., AND SHALL NOT BE COPIED,
	*  USED, NOR DISCLOSED WITHOUT EXPRESS WRITTEN
	*  AUTHORIZATION.  ALTHOUGH PUBLICATION IS NOT
	*  INTENDED, IN THE EVENT OF PUBLICATION, THE
	*  FOLLOWING NOTICE IS APPLICABLE:
	*
	*  (c) COPYRIGHT 2004 SSA GLOBAL TECHNOLOGIES, INC.
	*
	*           SSA GLOBAL TECHNOLOGIES, INC.
	*
	********************************************************/
//////////////////////////////////////////////////////////////////////
int CElementMaintenanceHelper::InsertNewBayLayer(CString &BayLayerName) 
{

	int someNotClosed;							//error checking
	Acad::ErrorStatus errorStatus;				//Autocad error status
	AcDbLayerTable *layerTable = NULL;			//pointer to layer table
	AcDbLayerTableRecord *layerRecord = NULL;	//pointer to layer table record
	AcDbBlockTable *blockTable = NULL;			//pointer to block table
	AcDbObjectId layerId;						//Autocad object id of layer
	int count;

	errorStatus = acdbCurDwg()->getLayerTable(layerTable, AcDb::kForWrite);
	// each bay is on a separate layer for coloring purposes
	layerRecord = new AcDbLayerTableRecord();
	if (!layerRecord) {
		AfxMessageBox("Error creating layer record");
		return -1;
	}
	BayLayerName.Format("Bay-%d",BayNum);
	layerRecord->setName(BayLayerName);
	BayNum++;
	while ( layerTable->add(layerId, layerRecord) != Acad::eOk ) {
		BayLayerName.Format("Bay-%d",BayNum);
		layerRecord->setName(BayLayerName);
		BayNum++;
	}
	
	layerRecord->setIsLocked(FALSE);
	count = 1;
	while (layerTable->close() != Acad::eOk && count < 10 )
		count++;
	if ( count == 10 )
		someNotClosed = 1;
	count = 1;
	while (layerRecord->close() != Acad::eOk && count < 10 )
		count++;
	if ( count == 10 )
		someNotClosed = 1;
	return someNotClosed;
}

//////////////////////////////////////////////////////////////////////
// Function Name : AddNewBayBlockToDwg
// Classname : None
// Description : Get the baseline cost amount
// Date Created : 10/01/98
// Author : wbh
//////////////////////////////////////////////////////////////////////
// Inputs : none
// Outputs : none
// Explanation : This adds a bay block to the autocad drawing
//////////////////////////////////////////////////////////////////////
// Algorithm : None
//////////////////////////////////////////////////////////////////////
// Revisions : 
//   Date-initials : Description_of_revision
//////////////////////////////////////////////////////////////////////
	/*******************************************************
	*
	*                       NOTICE
	*
	*  THIS SOFTWARE IS THE PROPERTY OF AND CONTAINS
	*  CONFIDENTIAL INFORMATION OF SSA GLOBAL 
	*  TECHNOLOGIES, INC., AND SHALL NOT BE COPIED,
	*  USED, NOR DISCLOSED WITHOUT EXPRESS WRITTEN
	*  AUTHORIZATION.  ALTHOUGH PUBLICATION IS NOT
	*  INTENDED, IN THE EVENT OF PUBLICATION, THE
	*  FOLLOWING NOTICE IS APPLICABLE:
	*
	*  (c) COPYRIGHT 2004 SSA GLOBAL TECHNOLOGIES, INC.
	*
	*           SSA GLOBAL TECHNOLOGIES, INC.
	*
	********************************************************/
//////////////////////////////////////////////////////////////////////


// sideIndex: 0 = left, 1 = right
// bayIndex: the current bay in the current side
// sideBarWidth: the list of bar widths for the current side
// side1BayYCoord: the list of bay center points on the left side
// sideBayWidth: the list of bay widths for the current side
// baySpace: the aisle space
// side1BayDepth: the list of bay depths for the left side
// sideBayDepth: the list of bay depths for the current side
// holdCornerPoint: the point chosen to place the aisle (the top left corner of the aisle rotated at 0 degrees)
// currentBayCenter: the center point of the current bay
// rotateAngle: the rotation of the aisle
int CElementMaintenanceHelper::UpdateLocationGlobalCoords(qqhSLOTAisle & insertedAisle, int sideIndex, int bayIndex,
							   CArray <float,float&> &sideBarWidth, CArray <double,double&> &side1BayYCoord,
							   CArray <float, float&> &sideBayWidth, float baySpace,
							   CArray <float, float&> &side1BayDepth, CArray <float,float&> &sideBayDepth,
							   ads_point &holdCornerPoint, ads_point &currentBayCenter, float rotateAngle) \
{
	ads_point nextCornerPoint,locationCenterPoint,rotatedLocationPoint, backfillPoint;
	CAutoCADCommands acCmds;
	int j,k,l;
	float tempFloat;
	int tempInt;
	qqhSLOTBay bay;
	qqhSLOTLevel level;
	qqhSLOTLocation loc;
	int locsPerRow,  numRows, rowDepthIncr, rowNum;
	BOOL bMultiDeepLoc;
	qqhSLOTLocation *locPtr;
	qqhSLOTLevel *levPtr;
	qqhSLOTBay *bayPtr;

	CArray <qqhSLOTLevel, qqhSLOTLevel&> levelList;

	bay = insertedAisle.getChildList()[sideIndex].getChildList()[bayIndex];
	bayPtr = &(insertedAisle.getChildList()[sideIndex].getChildList()[bayIndex]);

	// Assume the aisle is rotated to 0 so the X direction is the width of the bays
	// and the Y direction is the depth
	for ( j = 0; j < bay.getChildList().GetSize(); j ++ ) {
		
		level = bay.getChildList()[j];
		levPtr = &(bayPtr->getChildList()[j]);

		// New stuff to allow for multiple rows of locations deep in a bay
		if (level.pLevelProfile->m_LocationRowCount > 1) {
			bMultiDeepLoc = TRUE;
			locsPerRow = level.pLevelProfile->m_LocationProfileList.GetSize()/level.pLevelProfile->m_LocationRowCount;

			//locsPerRow = bay.pBayProfile->m_Width / 
			//	(level.getMinLocWidth() + level.getChildList()[0].getLocationProfile().getLocationSpace()*2);
			//numRows = level.getChildList().GetSize() / locsPerRow;
			numRows = level.pLevelProfile->m_LocationRowCount;
			rowDepthIncr = bay.pBayProfile->m_Depth / numRows;
		}
		else
			bMultiDeepLoc = FALSE;

		if (sideIndex == 1) {			// right side
			nextCornerPoint[X] = sideBarWidth[bayIndex]; // + bay.getBayProfile().getWidth();
			nextCornerPoint[Z] = 0;
			CString tempStr;
			tempStr.Format("%f - %f - %f/2",side1BayYCoord[bayIndex],baySpace,side1BayDepth[bayIndex]);

			// get the front of the bay on the right side
			if ( bayIndex < side1BayYCoord.GetSize() )
				nextCornerPoint[Y] = side1BayYCoord[bayIndex] - baySpace - side1BayDepth[bayIndex]/2;
			else
				nextCornerPoint[Y] = side1BayYCoord[side1BayYCoord.GetSize()-1] - 
					baySpace - side1BayDepth[side1BayYCoord.GetSize()-1]/2;

			if ( bayIndex != 0 ) {
				for (k = bayIndex-1; k >= 0; k--)
					nextCornerPoint[X] += sideBayWidth[k] + sideBarWidth[k];
			}

			backfillPoint[X] = nextCornerPoint[X];
			backfillPoint[Y] = nextCornerPoint[Y] - sideBayDepth[bayIndex];

		}
		else {							// left side
			nextCornerPoint[X] = sideBarWidth[bayIndex];
			nextCornerPoint[Y] = currentBayCenter[Y] - sideBayDepth[bayIndex]/2;
			nextCornerPoint[Z] = 0;
			if ( bayIndex != 0 ) {
				for (k = bayIndex-1; k >= 0; k--)
					nextCornerPoint[X] += sideBayWidth[k] + sideBarWidth[k];
			}

			backfillPoint[X] = nextCornerPoint[X];
			backfillPoint[Y] = nextCornerPoint[Y] + sideBayDepth[bayIndex];
			

		}

		// Note: technically, the order of the location profiles on the right side
		// should be reversed since when you are looking at the bay they first location
		// is always on the left.  But that would screw up our pick path creation
		// and since there is currently nothing unique about each location profile
		// we'll make them the same for now
		// Subnote: I'm not so sure about the previous note since the right aisle is 
		// supposed to be a mirror image of the left aisle, maybe the locations should also
		// be mirror images
		for ( k = 0; k < level.getChildList().GetSize(); k++ ) {
			loc = level.getChildList()[k];
			locPtr = &(levPtr->getChildList()[k]);
			
			//if (sideIndex == 1)
			//	locationCenterPoint[X] = nextCornerPoint[X] - loc.getWidth()/2 - loc.getLocationProfile().getLocationSpace();
			//else 
			locationCenterPoint[X] = nextCornerPoint[X] + loc.getWidth()/2 + loc.pLocationProfile->m_LocationSpace;
			backfillPoint[X] = nextCornerPoint[X] + loc.getWidth()/2 + loc.pLocationProfile->m_LocationSpace;
			locationCenterPoint[Y] = nextCornerPoint[Y];
	

			// Multi-deep locs
			if (bMultiDeepLoc) {
				rowNum = k / locsPerRow;
				// move the y coordinate up by the depth increment for each row
				// the first row is not affected
				if (sideIndex == 1)
					locationCenterPoint[Y] -= rowNum*rowDepthIncr;
				else
					locationCenterPoint[Y] += rowNum *rowDepthIncr;

				// if we are not on the first location in a row,
				// add the width and space for the previous locs in the row
				if ( k % locsPerRow != 0) {
					for (l=rowNum*locsPerRow; l < k; ++l) {
						//if (sideIndex == 1) {
						//	locationCenterPoint[X] -= level.getChildList()[l].getWidth();
						//	locationCenterPoint[X] -= level.getChildList()[l].getLocationProfile().getLocationSpace()*2;
						//}
						//else {
							locationCenterPoint[X] += level.getChildList()[l].getWidth();
							locationCenterPoint[X] += level.getChildList()[l].pLocationProfile->m_LocationSpace*2;
						//}
					}
				}
			}
			else {	
				if ( k != 0 ) {
					for ( l = k-1; l >= 0; l--) {
						//if (sideIndex == 1) {
						//	locationCenterPoint[X] -= level.getChildList()[l].getWidth();
						//	locationCenterPoint[X] -= level.getChildList()[l].getLocationProfile().getLocationSpace()*2;
						//}
						//else {
							locationCenterPoint[X] += level.getChildList()[l].getWidth();
							locationCenterPoint[X] += level.getChildList()[l].pLocationProfile->m_LocationSpace*2;
						//}
					}
				}
			}

			locationCenterPoint[Z] = loc.getCoord().getZ();
		
			backfillPoint[X] = locationCenterPoint[X];
			backfillPoint[Z] = locationCenterPoint[Z] + level.pLevelProfile->m_FlowDifference; //bay.pBayProfile->m_FlowDifference;
		
			rotatedLocationPoint[X] = 0;
			rotatedLocationPoint[Y] = 0;
			rotatedLocationPoint[Z] = 0;

			CAutoCADCommands::RotatePoint(locationCenterPoint,rotatedLocationPoint,PI/180*rotateAngle);
			
			rotatedLocationPoint[X] += holdCornerPoint[X];
			rotatedLocationPoint[Y] += holdCornerPoint[Y];
			rotatedLocationPoint[Z] += holdCornerPoint[Z];

			tempInt = (int)rotatedLocationPoint[X];
			tempFloat = (float)tempInt;
			if (rotatedLocationPoint[X] - tempFloat >= .5)
				tempFloat++;
			locPtr->getCoord().setX((int)tempFloat);

			tempInt = (int)rotatedLocationPoint[Y];
			tempFloat = (float)tempInt;
			if (rotatedLocationPoint[Y] - tempFloat >= .5)
				tempFloat++;
			locPtr->getCoord().setY((int)tempFloat);

			tempInt = (int)rotatedLocationPoint[Z];
			tempFloat = (float)tempInt;
			if (rotatedLocationPoint[Z] - tempFloat >= .5)
				tempFloat++;
			locPtr->getCoord().setZ((int)tempFloat);

			// Set backfill coordinates
			rotatedLocationPoint[X] = 0;
			rotatedLocationPoint[Y] = 0;
			rotatedLocationPoint[Z] = 0;

			CAutoCADCommands::RotatePoint(backfillPoint,rotatedLocationPoint,PI/180*rotateAngle);
			
			rotatedLocationPoint[X] += holdCornerPoint[X];
			rotatedLocationPoint[Y] += holdCornerPoint[Y];
			rotatedLocationPoint[Z] += holdCornerPoint[Z];

			tempInt = (int)rotatedLocationPoint[X];
			tempFloat = (float)tempInt;
			if (rotatedLocationPoint[X] - tempFloat >= .5)
				tempFloat++;
			locPtr->getBackfillCoordinates().setX((int)tempFloat);

			tempInt = (int)rotatedLocationPoint[Y];
			tempFloat = (float)tempInt;
			if (rotatedLocationPoint[Y] - tempFloat >= .5)
				tempFloat++;
			locPtr->getBackfillCoordinates().setY((int)tempFloat);

			tempInt = (int)rotatedLocationPoint[Z];
			tempFloat = (float)tempInt;
			if (rotatedLocationPoint[Z] - tempFloat >= .5)
				tempFloat++;
			locPtr->getBackfillCoordinates().setZ((int)tempFloat);

			//only significant for VW levels. (used in new coordinate calculation)
			levPtr->getCoord().setZ(locPtr->getCoord().getZ());
			levPtr->getCoord().setX(locPtr->getCoord().getX());
			levPtr->getCoord().setY(locPtr->getCoord().getY());
		}
	}
	return 1;
}

//////////////////////////////////////////////////////////////////////
// Function Name : AddBayDataFromProfile
// Classname : None
// Description : Get the baseline cost amount
// Date Created : 10/01/98
// Author : wbh
//////////////////////////////////////////////////////////////////////
// Inputs : none
// Outputs : none
// Explanation : Fill in the bay object based on the profile information
//////////////////////////////////////////////////////////////////////
// Algorithm : None
//////////////////////////////////////////////////////////////////////
// Revisions : 
//   Date-initials : Description_of_revision
//////////////////////////////////////////////////////////////////////
	/*******************************************************
	*
	*                       NOTICE
	*
	*  THIS SOFTWARE IS THE PROPERTY OF AND CONTAINS
	*  CONFIDENTIAL INFORMATION OF SSA GLOBAL 
	*  TECHNOLOGIES, INC., AND SHALL NOT BE COPIED,
	*  USED, NOR DISCLOSED WITHOUT EXPRESS WRITTEN
	*  AUTHORIZATION.  ALTHOUGH PUBLICATION IS NOT
	*  INTENDED, IN THE EVENT OF PUBLICATION, THE
	*  FOLLOWING NOTICE IS APPLICABLE:
	*
	*  (c) COPYRIGHT 2004 SSA GLOBAL TECHNOLOGIES, INC.
	*
	*           SSA GLOBAL TECHNOLOGIES, INC.
	*
	********************************************************/
////////////////////////////////////////////////////////////////////
int CElementMaintenanceHelper::AddBayDataFromProfile(qqhSLOTBay &bay, CBayProfile &bayProfile)
{
	int i, j;

	//////////////////////////////////////////////////////////////////////
	// Bay data
	/////////////////////////////////////////////////////////////////////

	for ( i = 0; i < bayProfile.m_LevelProfileList.GetSize(); i++ ) {
		//////////////////////////////////////////////////////////////////////
		// Level data
		//////////////////////////////////////////////////////////////////////
		qqhSLOTLevel newLevel;
		CLevelProfile *pLevelProfile = new CLevelProfile(*bayProfile.m_LevelProfileList[i]);

		newLevel.pLevelProfile = pLevelProfile;
		newLevel.setLevelProfileId(pLevelProfile->m_LevelProfileDBId);

		newLevel.setDescription(CString("New Level"));

		newLevel.setForkFixedInsertion(pLevelProfile->m_ForkFixedInsertion);
		newLevel.setIsRotateAllowed(pLevelProfile->m_IsRotateAllowed);
		newLevel.setIsVariableLocationsAllowed(pLevelProfile->m_IsVariableWidthAllowed);
		newLevel.setFacingGap(pLevelProfile->m_FacingGap);
		newLevel.setFacingSnap(pLevelProfile->m_FacingSnap);
		newLevel.setProductGap(pLevelProfile->m_ProductGap);
		newLevel.setProductSnap(pLevelProfile->m_ProductSnap);
		newLevel.setMinLocWidth(pLevelProfile->m_MinimumLocWidth);
		newLevel.getCoord().setZ(pLevelProfile->m_Coordinates.m_Z);
		bay.getChildList().Add(newLevel);

		for ( j = 0; j < pLevelProfile->m_LocationProfileList.GetSize(); j++ ) {
			//////////////////////////////////////////////////////////////////////
			// Location data
			//////////////////////////////////////////////////////////////////////
			CLocationProfile *pLocProfile = new CLocationProfile(*pLevelProfile->m_LocationProfileList[j]);

			qqhSLOTLocation newLocation;
			newLocation.pLocationProfile = pLocProfile;
			newLocation.setLocationProfileId(pLocProfile->m_LocationProfileDBId);

			newLocation.setDescription(CString("New Location"));
			newLocation.setMaxWeight(pLocProfile->m_WeightCapacity);
			newLocation.setWidth(pLocProfile->m_Width);
			newLocation.setDepth(pLocProfile->m_Depth);	// overhang already taken into account + bayProfile.getLevelProfileList()[i].getOverhang());
			newLocation.setHeight(pLocProfile->m_Height);
			newLocation.setHandlingMethod(pLocProfile->m_HandlingMethod);
			newLocation.setIsSelect(pLocProfile->m_IsSelect);
			newLocation.setStatus(LOC_STATUS_NOT_INTEGRATED);
			newLocation.setIsActive(0);
			newLocation.getCoord().setX(pLocProfile->m_Coordinates.m_X);
			newLocation.getCoord().setY(pLocProfile->m_Coordinates.m_Y);
			newLocation.getCoord().setZ(pLocProfile->m_Coordinates.m_Z);
			newLocation.setClearance(pLevelProfile->m_Clearance);
			newLocation.setBackfillId(pLevelProfile->m_BackfillCode);
			newLocation.setSelectionSequence(newLocation.getDescription());
			newLocation.setReplenishmentSequence(newLocation.getDescription());

			bay.getChildList()[i].getChildList().Add(newLocation);
		}
	}
	return 0;
}




void CElementMaintenanceHelper::RenumberAisle()
{
	AcDbHandle objHandle;
	int res;
	ads_name Set, E_name;
	long nLength;
	AcDbObjectId objId;
	AcDbEntity * pEnt;
	char objHandleStr[20];
	int  aisleFileOffset,sectionFileOffset;
	int numSections, numAisles, numSides, numBays;
	int i_sec, i_ais, i_sid, i_bay;
	Acad::ErrorStatus eStatus;
	qqhSLOTAisle myAisle;
	qqhSLOTSection mySection;
	CString oldDesc;
	CString forteRoot;
	unsigned int dbid;
	CDWordArray aisleDBIDList;

	strcpy(objHandleStr,"");

	//////////////////////////////////////////////////////////////////////
	// Get the bay the user chose
	//////////////////////////////////////////////////////////////////////

	res = ads_ssget("P", NULL, NULL, NULL, Set);
	if (res != RTNORM) {
		AfxMessageBox("Please select one or more aisles to renumber.");
		return;
	}

	ads_sslength(Set, &nLength);
	if ( nLength < 1 ) {
		AfxMessageBox("Please select at least one bay.");
		return;
	}

	for (int n=0; n < nLength; n++)	{

		res = ads_ssname(Set, n, E_name);
		if (res != RTNORM) 	{
			ads_ssfree(Set);
			return;
		}
		eStatus = acdbGetObjectId(objId, E_name);
		if (eStatus != Acad::eOk) {
			AfxMessageBox("Warning : The autocad object could not be found.\nPlease exit, reopen the facility and try again.");
			return;
		}
		eStatus = acdbOpenAcDbEntity(pEnt, objId, AcDb::kForRead);
		if (eStatus != Acad::eOk) {
			AfxMessageBox("Warning : The autocad object could not be found.\nPlease exit, reopen the facility and try again.");
			return;
		}
		
		pEnt->getAcDbHandle(objHandle);
		objHandle.getIntoAsciiBuffer(objHandleStr);
		
		if (strcmp(objHandleStr, "") == 0) {
			AfxMessageBox("Unable to identify all of the selected objects.");
			return;
		}
		
		pEnt->close();
			
		//////////////////////////////////////////////////////////////////////
		// find corresponding tree element in btree
		//////////////////////////////////////////////////////////////////////
		BOOL found = FALSE;
		numSections = changesTree.treeChildren.GetSize();
		for (i_sec = 0; i_sec < numSections; i_sec++) {
			numAisles = changesTree.treeChildren[i_sec].treeChildren.GetSize();
			for (i_ais = 0; i_ais < numAisles; i_ais++) {
				numSides = changesTree.treeChildren[i_sec].treeChildren[i_ais].treeChildren.GetSize();
				for (i_sid = 0; i_sid < numSides; i_sid++) {
					numBays = changesTree.treeChildren[i_sec].treeChildren[i_ais].treeChildren[i_sid].treeChildren.GetSize();
					for (i_bay = 0; i_bay < numBays; i_bay++) {
						if (strcmp(changesTree.treeChildren[i_sec].treeChildren[i_ais].treeChildren[i_sid].treeChildren[i_bay].acadHandle, objHandleStr) == 0) {
							found = TRUE;
							break;
						}
					}
					if (found)
						break;
				}
				if (found) {
					aisleFileOffset = changesTree.treeChildren[i_sec].treeChildren[i_ais].fileOffset;
					sectionFileOffset = changesTree.treeChildren[i_sec].fileOffset;
					break;
				}
			}
			if (found)
				break;
		}
		
		if (!found) {
			try {
				aisleFileOffset = bTreeHelper.UpdateBTWithAisle(CString(objHandleStr),changesTree);
				if (aisleFileOffset == -1) {
					AfxMessageBox("Error Finding Aisle in Facility");
					return;
				}
			}
			catch(...) {
				AfxMessageBox("Error Finding Aisle in Facility");
				return;
			}
			found = 1;
		}
		
		//////////////////////////////////////////////////////////////////////
		// If we find it, call the appropriate Java screen
		//////////////////////////////////////////////////////////////////////
		if (found) {
			//AfxMessageBox("Found matching handle");
			bTreeHelper.GetBtAisle(aisleFileOffset,myAisle);
			dbid = myAisle.getDBID();
			int addIt = 1;
			for (int i=0; i < aisleDBIDList.GetSize(); ++i) {
				if (aisleDBIDList[i] == dbid) {
					addIt = 0;
					break;
				}
			}
			if (addIt)
				aisleDBIDList.Add(dbid);
		}
	}


	ads_ssfree(Set);
	
	ads_printf("Renumbering...");
	numberingService.RenumberAisles(aisleDBIDList, changesTree);


	AfxMessageBox("Aisles have been renumbered.");

	return;


}

int CElementMaintenanceHelper::GetAisleFromTreeByBay(CString bayHandle, qqhSLOTAisle &pAisle,
						  int &sectionIdx, int &aisleIdx, int &sideIdx, int &bayIdx)
{
	int foundBay, aisleStoreId;
//	char temphandleBuf[20];

	foundBay = 0;

	//Sections
	for ( sectionIdx = 0; sectionIdx < changesTree.getSectionCount() && foundBay== 0; sectionIdx++ ) {
		// Aisles
		for ( aisleIdx = 0; aisleIdx < changesTree.getAisleCountForSection(sectionIdx) && foundBay == 0; aisleIdx++ ) {
			//Sides
			for (sideIdx = 0; sideIdx < changesTree.getSideCountForAisle(sectionIdx, aisleIdx) && foundBay == 0; sideIdx++ ) {
				//Bays
				for (bayIdx = 0; bayIdx < changesTree.getBayCountForSide(sectionIdx, aisleIdx, sideIdx) && foundBay == 0; bayIdx++ ) {
					//strcpy(temphandleBuf,changesTree.getBay(sectionIdx, aisleIdx, sideIdx, bayIdx)->acadHandle);
					//CString tempMsg = "Comparing Bay : " + CString(temphandleBuf);
					//AfxMessageBox(tempMsg);
					if ( strcmp(changesTree.getBay(sectionIdx, aisleIdx, sideIdx, bayIdx)->acadHandle,bayHandle) == 0 ) {
						foundBay = 1;
						aisleStoreId = changesTree.getAisle(sectionIdx, aisleIdx)->fileOffset;
						bTreeHelper.GetBtAisle(aisleStoreId,pAisle);
						return 0;
					}
				}
			}
		}
	}

	return -1;

}



void CElementMaintenanceHelper::PickPathProperties()
{
	int i,j;
	int foundAisle;
	CString handle;

	qqhSLOTAisle tempAisle;

	while (CAutoCADCommands::GetSelectedHandle(handle) == 0) {
		if (AfxMessageBox("Please select a pick path.", MB_OKCANCEL) == IDCANCEL)
			return;
	}


	foundAisle = 0;
	for ( i = 0; i < changesTree.treeChildren.GetSize() && foundAisle == 0; i++ ) {
		for ( j = 0; j < changesTree.treeChildren[i].treeChildren.GetSize() && foundAisle == 0; j++ ) {
			if (strcmp(changesTree.treeChildren[i].treeChildren[j].acadHandle, handle) == 0) {
				bTreeHelper.GetBtAisle(changesTree.treeChildren[i].treeChildren[j].fileOffset,tempAisle);
				foundAisle = 1;
			}
		}
	}
	if (foundAisle == 0) {
		try {
			if ( bTreeHelper.UpdateBTWithAisleByPickPathAcadHandle(handle,changesTree) < 0 ) {
				controlService.Log("An error occurred while retrieving the aisle from the database.",
					"Error in bTreeHelper.UpdateBTWithAisleByPickPathAcadHandle. Handle: %s\n", handle);
				return;
			}
		}
		catch(...) {
			controlService.Log("An error occurred while retrieving the aisle from the database.",
				"Generic excpetion in bTreeHelper.UpdateBTWithAisleByPickPathAcadHandle. Handle: %s\n", handle);
			return;
		}
		
		for ( i = 0; i < changesTree.treeChildren.GetSize() && foundAisle == 0; i++ ) {
			for ( j = 0; j < changesTree.treeChildren[i].treeChildren.GetSize() && foundAisle == 0; j++ ) {
				if (strcmp(changesTree.treeChildren[i].treeChildren[j].acadHandle, handle) == 0) {
					bTreeHelper.GetBtAisle(changesTree.treeChildren[i].treeChildren[j].fileOffset,tempAisle);
					foundAisle = 1;
				}
			}
		}
	}
	
	
	if (! foundAisle) {
		controlService.Log("Unable to find the aisle associated with the selected pick path.",
			"Error finding aisle for handle (%s) in btree.\n", handle);
		return;
	}

	PickPathPropertiesForAisle(tempAisle);
		
}

int CElementMaintenanceHelper::PickPathPropertiesForAisle(qqhSLOTAisle &aisle) 
{
	CPickPathPropertiesDialog PickPathProperties;
	CString tempHandle;
	strcpy(oldPickPathHandle, "XXX");

	////////////////////////////////////////////////////////////////
	//  Display the dialog and get the appropriate data.  Use
	//  last information if available.
	////////////////////////////////////////////////////////////////
	PickPathProperties.m_PickPathProperties_PathType_Val = aisle.getPickPathType();
	PickPathProperties.m_PickPathProperties_PatternNum = aisle.getBaysInPattern();
	PickPathProperties.m_LBayStartVal = aisle.getLeftBayStart();
	PickPathProperties.m_RBayStart = aisle.getRightBayStart();
	PickPathProperties.m_LLevelStart = aisle.getLeftLevelStart(); 
	PickPathProperties.m_RLevelStart = aisle.getRightLevelStart();
	PickPathProperties.m_LLocStart = aisle.getLeftLocationStart(); 
	PickPathProperties.m_RLocStart = aisle.getRightLocationStart();
	PickPathProperties.m_LBaySchemeVal = aisle.getLeftBayScheme(); 
	PickPathProperties.m_RBaySchemeVal = aisle.getRightBayScheme();
	PickPathProperties.m_LLevelSchemeVal = aisle.getLeftLevelScheme(); 
	PickPathProperties.m_RLevelSchemeVal = aisle.getRightLevelScheme();
	PickPathProperties.m_LLocSchemeVal = aisle.getLeftLocationScheme(); 
	PickPathProperties.m_RLocSchemeVal = aisle.getRightLocationScheme();
	PickPathProperties.m_LBayStepVal = aisle.getLeftBayStep(); 
	PickPathProperties.m_RBayStep = aisle.getRightBayStep();
	PickPathProperties.m_LLevelStep = aisle.getLeftLevelStep(); 
	PickPathProperties.m_RLevelStep = aisle.getRightLevelStep();
	PickPathProperties.m_LLocStep = aisle.getLeftLocationStep(); 
	PickPathProperties.m_RLocStep = aisle.getRightLocationStep();
	PickPathProperties.m_LLevelBreak = aisle.getLeftLevelBreak();
	PickPathProperties.m_RLevelBreak = aisle.getRightLevelBreak();
	PickPathProperties.m_LLocBreakVal = aisle.getLeftLocationBreak(); 
	PickPathProperties.m_RLocBreakVal = aisle.getRightLocationBreak();

	PickPathProperties.m_LBayPatternVal = aisle.getLeftBayPattern();
	PickPathProperties.m_LLevelPatternVal = aisle.getLeftLevelPattern();
	PickPathProperties.m_LLocPatternVal = aisle.getLeftLocPattern();
	PickPathProperties.m_RBayPatternVal = aisle.getRightBayPattern();
	PickPathProperties.m_RLevelPatternVal = aisle.getRightLevelPattern();
	PickPathProperties.m_RLocPatternVal = aisle.getRightLocPattern();

	PickPathProperties.m_Updateable = FALSE;

	PickPathProperties.DoModal();
	
	SavePickPathValues(PickPathProperties);

	return 0;

}



void CElementMaintenanceHelper::ChangeRacktype()
{

	ChangeBayProfile();

	return;

	AcDbHandle objHandle;
	CChangeRackType changeRackDlg;
	CString BayLayerName;
	char objHandleStr[20];	
	//Acad::ErrorStatus errorStatus;
	AcDbObjectId layerId;
	int someNotClosed = 0;
	CString iteratHandle;
	CString iteratValue;
	//AcDbBlockReference * pBlockRef;

	int sectionIndex, aisleIndex, sideIndex, bayIndex;

	strcpy(objHandleStr,"");

	CString strData;
	CSsaStringArray locationUDFList;
	double bayWidth, bayDepth, bayHeight, baySpace,rotateAngle = 0.0;
	CArray <double, double&> sideBarWidth;
	CString levelUDFFilename;
	AcGePoint3d tempPoint;
	ads_point unRotPoint;
	ads_point rotPoint;
	ads_point tempPoint2;
	AcDbHandle newObjHandle;

	CMapStringToString mapHandles;
	//CArray<int, int&> delOffsets;
	//CArray<int, int&> parentOffsets;
	CString strHandle, strParams;
	AcDbObjectId blockId;
	AcGeMatrix3d mat;

	qqhSLOTFacility theFacility;
	qqhSLOTAisle aAisle;
	qqhSLOTAisle tempAisle;
	qqhSLOTSide aSide, tempSide;
	qqhSLOTBay aBay,tempBay;
	qqhSLOTLevel aLevel;
	qqhSLOTLocation aLocation;
	BOOL foundBay;
	int i,j,k,l;
	AcDbDatabase *bayBlock = NULL;
	int tempInt;
	float tempFloat;
	int sideID;

	CBayProfile newBayProfile;

	CWinApp * currentApp;
	currentApp = AfxGetApp();
	CStringArray handles, foundHandles;

	bTreeHelper.GetBtFacility(1, theFacility);


	//////////////////////////////////////////////////////////////////////
	// Get the bay that was chosen
	//////////////////////////////////////////////////////////////////////
	while (CAutoCADCommands::GetSelectedHandles(handles) == 0) {
		if (AfxMessageBox("Please select one or more bays to change.", MB_OKCANCEL) == IDCANCEL)
			return;
	}

	if ((changeRackDlg.DoModal()) == IDCANCEL)
		return;

	if (AfxMessageBox("This operation can not be undone.  Do you wish to continue changing the profile?", MB_YESNO) == IDNO)
		return;


	for (i=0; i < handles.GetSize(); ++i) {
		if (changesTree.getBayByHandle(handles[i]) == NULL) {
			CString temp;
			temp.Format("Could not find bay record for handle: %s", handles[i]);
			return;
		}
	}

	// Now we know all the selected bays are in the btree

	//////////////////////////////////////////////////////////////////////
	// Get Profile from DB
	//////////////////////////////////////////////////////////////////////
	CWaitCursor cwc;
	bayProfileDataService.GetBayProfile(changeRackDlg.m_SelectedBayProfileId, newBayProfile, CBayProfile::loadLevels|CBayProfile::loadLocations);

	
	for (i=0; i < handles.GetSize(); ++i) {

		aAisle.getChildList().RemoveAll();
		aAisle.getChildList().Add(aSide);
		aAisle.getChildList()[0].getChildList().Add(aBay);
		
		// Get the necessary data from the old bay block and delete it.
		CAutoCADCommands::GetDrawingObjectCoordinates(handles[i], tempPoint, rotateAngle);

		CString xDataStr = CAutoCADCommands::GetXDataForObject(handles[i]);
		if (xDataStr == "") {
			AfxMessageBox("One or more selected objects is not a bay.");
			return;
		}

		istrstream s(xDataStr.GetBuffer(xDataStr.GetLength()+1));
		xDataStr.ReleaseBuffer();
		s >> bayDepth >> bayHeight >> baySpace;


		//////////////////////////////////////////////////////////////////////


		bayWidth = newBayProfile.m_Width;

		//////////////////////////////////////////////////////////////////////
		// Fill in relevant data to bay
		//////////////////////////////////////////////////////////////////////
		AddBayDataFromProfile(aAisle.getChildList()[0].getChildList()[0], newBayProfile);

		qqhSLOTAisle aisleCopy;
		aisleCopy = aAisle;

		tempPoint2[X] = tempPoint[X];
		tempPoint2[Y] = tempPoint[Y];
		tempPoint2[Z] = tempPoint[Z];
				
		TreeElement *bayPtr, *sidePtr, *aislePtr;
		bayPtr = changesTree.getBayByHandle(handles[i]);
		sidePtr = bayPtr->treeParent;
		aislePtr = sidePtr->treeParent;

		int locsPerRow,  numRows, rowDepthIncr, rowNum;
		BOOL bMultiDeepLoc;
		qqhSLOTLevel level;
		qqhSLOTLocation loc;
		qqhSLOTBay bay;
		
		
		bTreeHelper.GetBtBay(bayPtr->fileOffset, tempBay);
		bTreeHelper.GetBtSide(sidePtr->fileOffset, tempSide);
		bTreeHelper.GetBtAisle(aislePtr->fileOffset, tempAisle);

		for (sectionIndex=0; sectionIndex < changesTree.getSectionCount() ; sectionIndex++) {
			for (aisleIndex=0; aisleIndex < changesTree.getAisleCountForSection(sectionIndex)  ; aisleIndex++) {
				for (sideIndex=0; sideIndex < changesTree.getSideCountForAisle(sectionIndex, aisleIndex) ; sideIndex++) {
					for (bayIndex=0; bayIndex < changesTree.getBayCountForSide(sectionIndex, aisleIndex, sideIndex) ; bayIndex++) {
						bayPtr = changesTree.getBay(sectionIndex, aisleIndex, sideIndex, bayIndex);
						if (handles[i].Compare(bayPtr->acadHandle) != 0 )
							continue;
						
						foundBay = TRUE;
						break;
	
					}
					if (foundBay) break;
				}
				if (foundBay) break;
			}
			if (foundBay) break;
		}

		if (! foundBay) {
			AfxMessageBox("Error finding bay.");
			return;
		}


		// See if the bay is at the end of the aisle
		BOOL isEndBay;
		try {
			if (tempBay.getDBID() != 0)
				isEndBay = facilityDataService.IsEndBay(tempBay);
			else
				isEndBay = tempBay.isEndBay;
		}
		catch (...) {
			ads_printf("Error getting bay end status from database.\n");
		}
		
		// Just in case there are some bays in the bree that aren't in the database
		if (isEndBay) {		
			for (int bayIdx=0; bayIdx < sidePtr->treeChildren.GetSize(); ++bayIdx) {
				qqhSLOTBay chkBay;
				
				bTreeHelper.GetBtBay(sidePtr->treeChildren[bayIdx].fileOffset, chkBay);
				if (chkBay.getCoord().getX() > tempBay.getCoord().getX()) {
					isEndBay = FALSE;
					break;
				}
			}
		}
		//////////////////////////////////////////////////////////////////////
		// Add bay DWG block to Facility
		//////////////////////////////////////////////////////////////////////
		//////////////////////////////////////////////////////////////////////
		// remove old
		//////////////////////////////////////////////////////////////////////
		CAutoCADCommands::DeleteDrawingObjectByHandle(handles[i]);

		//////////////////////////////////////////////////////////////////////
		// Insert new block.  (new layer per bay)
		//////////////////////////////////////////////////////////////////////
		someNotClosed = InsertNewBayLayer(BayLayerName);
		if (someNotClosed == -1)
			return;

		// Read the DWG file and build Acad DB info
		//////////////////////////////////////////////////////////////////////
		if (bayBlock == NULL) {
			bayBlock = new AcDbDatabase(Adesk::kFalse);
			
			CString strPath;
			strPath.Format("%s\\RackTypes\\%s\\%s\\%s%s.dwg", controlService.m_ClientHome,
				controlService.m_CurrentDatabase, newBayProfile.ConvertBayTypeToPath(newBayProfile.m_BayType),
				(isEndBay) ? "." : "", newBayProfile.m_Description);

			newBayProfile.Draw(FALSE, TRUE);

			Acad::ErrorStatus es = bayBlock->readDwgFile(strPath);
			if (es != Acad::eOk) 
			{
				CString strMsg = "Could not open : ";
				strMsg += strPath;
				AfxMessageBox(strMsg);
				return ;
			}
		}

//		someNotClosed = AddNewBayBlockToDwg((float) (rotateAngle / (PI/180)), (float)newBayProfile.m_Depth, 
//			(float)newBayProfile.m_UprightHeight, (float)tempAisle.getAisleSpace(), tempPoint2,
//				BayLayerName, bayBlock, aAisle.getChildList()[0].getChildList()[0].getAcadHandle());
		numItemsProcessed++;
		if (bayBlock != NULL)
			delete bayBlock;

		//////////////////////////////////////////////////////////////////////
		// Update the location global coordinates
		//////////////////////////////////////////////////////////////////////
		// When we add a bay, we:
		// 1) Start with the bay center at 0,0,0
		// 2) Adjust the bay coordinates based on the width, depth, height so that
		//	  the bay upper left corner (which is actually the back left) is at the origin.  
		//	  This is because when we calculate where to put the bay, we are calculating based on the side
		//	  of the bay - not the center
		// 3) Offset the bay by the width of any other bays that are in the same aisle
		//	  closer to the aisle origin than ours.  Now the back of the bay is sitting under the x-axis
		//	  somewhere away from the origin (unless it's the first bay in the aisle).
		// 4) Rotate the bay based on the aisle rotation.  This rotation is around the origin. If the rotation
		//    is 90, the back of the bay will now be against the y-axis.
		// 5) Offset the bay by the aisle coordinates.  
		// 6) The bay is now in its final place.

		// So, to set the location coordinates of the new bay
		// 1)  Start with the final coordinates (6)
		// 2)  Subtract the aisle coordinates (5)
		// 3)  Unrotate the bay (rotate it around the negative aisle rotation (4).
		// 4)  Subtract the bay width, depth, height to get to the lower left (front left)
		//	   corner of the bay (not including the upright) (2 kinda); this is where we will start 
		//	   calculating the locations from.  We skip undoing step 3 above because we want to
		//	   start with the location coords already offset by the other bays.
		// 5)  Now we calculate each of the locations based on the width, depth and space in
		//	   nice 0 rotation numbers;
		// 6)  Rotate the location coordinates back
		// 7)  Offset the location coordinates by the aisle coordinates
		

		
		sideID = atoi(tempSide.getDescription());
		
		// brd - commented this out. Why do we need to update the rotation of the aisle?
		//tempAisle.setRotation((float) (rotateAngle / (PI/180)) );
		//bTreeHelper.SetBtAisle(changesTree.treeChildren[sectionIndex].treeChildren[aisleIndex].fileOffset,tempAisle);
		//AfxMessageBox("Got Bay");
		
		// set the coordinates of the first bay in the aisle; we know that's the right bay because
		// we purposely deleted everything but it from the aisle
		
		aAisle.getChildList()[0].getChildList()[0].setDescription(tempBay.getDescription());
		aAisle.getChildList()[0].getChildList()[0].setBayProfileId(newBayProfile.m_BayProfileDBId);
		aAisle.getChildList()[0].getChildList()[0].pBayProfile = new CBayProfile(newBayProfile);
		aAisle.getChildList()[0].getChildList()[0].getCoord().setX(tempBay.getCoord().getX());
		aAisle.getChildList()[0].getChildList()[0].getCoord().setY(tempBay.getCoord().getY());
		aAisle.getChildList()[0].getChildList()[0].getCoord().setZ(tempBay.getCoord().getZ());
		aAisle.getChildList()[0].getChildList()[0].isEndBay = tempBay.isEndBay;
		bay = aAisle.getChildList()[0].getChildList()[0];
		
		// Start with the bay center point
		rotPoint[X] = tempPoint[X];
		rotPoint[Y] = tempPoint[Y];
		rotPoint[Z] = tempPoint[Z];
		rotPoint[Z] -= bayHeight/2;
		
		// Subtract out the position of the aisle to get the first bay in the aisle back to the origin
		rotPoint[X] -= tempAisle.getCoord().getX();
		rotPoint[Y] -= tempAisle.getCoord().getY();
		rotPoint[Z] -= tempAisle.getCoord().getZ();

		// Un-rotate it so it is up against the x-axis
		CAutoCADCommands::RotatePoint(rotPoint,unRotPoint,-1*rotateAngle);
		// Because of a problem with the order by in the pick path creation
		// we need to make the locations on both sides start closest to the aisle

		// Get the front left point of the bay
		if ( sideID == 1)		// left side
			unRotPoint[X] -= bayWidth/2;
		else
			unRotPoint[X] += bayWidth/2;
		
		//if ( sideID == 1 
		//	unRotPoint[Y] += bayDepth/2;
		//else
		unRotPoint[Y] -= bayDepth/2;
		
		// now unRotPoint contains the front left corner of the bay in 0 rotation
		
		for ( j = 0; j < aAisle.getChildList()[0].getChildList()[0].getChildList().GetSize(); j ++ ) {		
			level = aAisle.getChildList()[0].getChildList()[0].getChildList()[j];
		
			// New stuff to allow for multiple rows of locations deep in a bay
			if (level.pLevelProfile->m_LocationRowCount > 1) {
				bMultiDeepLoc = TRUE;
				//locsPerRow = bay.getBayProfile().getWidth() / 
				//	(level.getMinLocWidth() + level.getChildList()[0].getLocationProfile().getLocationSpace()*2);
				locsPerRow = level.pLevelProfile->m_LocationProfileList.GetSize()/
					level.pLevelProfile->m_LocationRowCount;
				//numRows = level.getChildList().GetSize() / locsPerRow;
				numRows = level.pLevelProfile->m_LocationRowCount;
				rowDepthIncr = bay.pBayProfile->m_Depth / numRows;
			}
			else
				bMultiDeepLoc = FALSE;
			
			for ( k = 0; k < level.getChildList().GetSize(); k++ ) {
				loc = level.getChildList()[k];
				
				if (sideID == 1)		// left side
					tempPoint2[X] = unRotPoint[X] + loc.getWidth()/2 + loc.pLocationProfile->m_LocationSpace;
				else
					tempPoint2[X] = unRotPoint[X] - loc.getWidth()/2 - loc.pLocationProfile->m_LocationSpace;
				
				tempPoint2[Y] = unRotPoint[Y];
				
				// Multi-deep locs
				if (bMultiDeepLoc) {
					rowNum = k / locsPerRow;
					// move the y coordinate up by the depth increment for each row
					// the first row is not affected
					if (sideID == 1)
						tempPoint2[Y] += rowNum*rowDepthIncr;
					else
						tempPoint2[Y] += rowNum*rowDepthIncr;
					
					// if we are not on the first location in a row,
					// add the width and space for the previous locs in the row
					if ( k % locsPerRow != 0) {
						for (l=rowNum*locsPerRow; l < k; ++l) {
							if (sideID == 1) {		// left side
								tempPoint2[X] += level.getChildList()[l].getWidth();
								tempPoint2[X] += level.getChildList()[l].pLocationProfile->m_LocationSpace*2;
							}
							else {
								tempPoint2[X] -= level.getChildList()[l].getWidth();
								tempPoint2[X] -= level.getChildList()[l].pLocationProfile->m_LocationSpace*2;
							}
						}
					}
				}
				else {
					
					if ( k != 0 ) {
						for ( l = k -1; l >= 0; l--) {
							if (sideID == 1) {		// left side
								tempPoint2[X] += aAisle.getChildList()[0].getChildList()[0].getChildList()[j].getChildList()[l].getWidth();
								tempPoint2[X] += aAisle.getChildList()[0].getChildList()[0].getChildList()[j].getChildList()[l].pLocationProfile->m_LocationSpace*2;
							}
							else {
								tempPoint2[X] -= aAisle.getChildList()[0].getChildList()[0].getChildList()[j].getChildList()[l].getWidth();
								tempPoint2[X] -= aAisle.getChildList()[0].getChildList()[0].getChildList()[j].getChildList()[l].pLocationProfile->m_LocationSpace*2;
							}
						}
					}
				}
				if ( j == 0 )
					tempPoint2[Z] = 0;
				else	// brd - changed to use copy of the aisle since we are overwriting as we go
					tempPoint2[Z] = unRotPoint[Z] + aisleCopy.getChildList()[0].getChildList()[0].getChildList()[j].getChildList()[k].getCoord().getZ(); 
				//tempPoint2[Z] = unRotPoint[Z] + aAisle.getChildList()[0].getChildList()[0].getChildList()[j-1].getCoord().getZ(); // + aAisle.getChildList()[0].getChildList()[0].getChildList()[j-1].getShelfThickness();
				
				// Now tempPoint2 has the final unrotated coordinates of the location
				ads_point backfillPoint;
				backfillPoint[X] = tempPoint2[X];
				if (sideID == 1)
					backfillPoint[Y] = tempPoint2[Y] + bay.pBayProfile->m_Depth;
				else
					backfillPoint[Y] = tempPoint2[Y] - bay.pBayProfile->m_Depth;

				backfillPoint[Z] = tempPoint2[Z] + level.pLevelProfile->m_FlowDifference; //bay.pBayProfile->m_FlowDifference;

				qqhSLOTLocation l;
				l = aAisle.getChildList()[0].getChildList()[0].getChildList()[j].getChildList()[k];
				rotPoint[X] = 0;
				rotPoint[Y] = 0;
				rotPoint[Z] = 0;
				CAutoCADCommands::RotatePoint(tempPoint2,rotPoint,rotateAngle);
				rotPoint[X] += (double)tempAisle.getCoord().getX();
				rotPoint[Y] += (double)tempAisle.getCoord().getY();
				rotPoint[Z] += (double)tempAisle.getCoord().getZ();
				tempInt = (int)rotPoint[X];
				tempFloat = (float)tempInt;
				if (rotPoint[X]  - tempFloat >= 0.5)
					tempFloat++;
				// set the level x coordinate
				aAisle.getChildList()[0].getChildList()[0].getChildList()[j].getChildList()[k].getCoord().setX((int)tempFloat);
				// set the level x coordinate
				aAisle.getChildList()[0].getChildList()[0].getChildList()[j].getCoord().setX((int)tempFloat);
				tempInt = (int)rotPoint[Y];
				tempFloat = (float)tempInt;
				if (rotPoint[Y]  - tempFloat >= 0.5)
					tempFloat++;
				// set the location y coordinate
				aAisle.getChildList()[0].getChildList()[0].getChildList()[j].getChildList()[k].getCoord().setY((int)tempFloat);
				// set the level y coordinate
				aAisle.getChildList()[0].getChildList()[0].getChildList()[j].getCoord().setY((int)tempFloat);
				tempInt = (int)rotPoint[Z];
				tempFloat = (float)tempInt;
				if (rotPoint[Z]  - tempFloat >= 0.5)
					tempFloat++;
				// set the location z coordinate
				aAisle.getChildList()[0].getChildList()[0].getChildList()[j].getChildList()[k].getCoord().setZ((int)tempFloat);
				// set the level z coordinate
				aAisle.getChildList()[0].getChildList()[0].getChildList()[j].getCoord().setZ((int)tempFloat);
				
				
				// Set the backfill coordinates
				rotPoint[X] = 0;
				rotPoint[Y] = 0;
				rotPoint[Z] = 0;
				CAutoCADCommands::RotatePoint(backfillPoint,rotPoint,rotateAngle);
				rotPoint[X] += (double)tempAisle.getCoord().getX();
				rotPoint[Y] += (double)tempAisle.getCoord().getY();
				rotPoint[Z] += (double)tempAisle.getCoord().getZ();
				tempInt = (int)rotPoint[X];
				tempFloat = (float)tempInt;
				if (rotPoint[X]  - tempFloat >= 0.5)
					tempFloat++;
				// set the level x coordinate
				aAisle.getChildList()[0].getChildList()[0].getChildList()[j].getChildList()[k].getBackfillCoordinates().setX((int)tempFloat);

				tempInt = (int)rotPoint[Y];
				tempFloat = (float)tempInt;
				if (rotPoint[Y]  - tempFloat >= 0.5)
					tempFloat++;
				// set the location y coordinate
				aAisle.getChildList()[0].getChildList()[0].getChildList()[j].getChildList()[k].getBackfillCoordinates().setY((int)tempFloat);

				tempInt = (int)rotPoint[Z];
				tempFloat = (float)tempInt;
				if (rotPoint[Z]  - tempFloat >= 0.5)
					tempFloat++;
				// set the location z coordinate
				aAisle.getChildList()[0].getChildList()[0].getChildList()[j].getChildList()[k].getBackfillCoordinates().setZ((int)tempFloat);

				
				//////////////////////////////////////////////////////////////////////
				// Delete old bay from facility
				//////////////////////////////////////////////////////////////////////
				bTreeHelper.DeleteFacilityBranch("SLOTBay", changesTree.treeChildren[sectionIndex].treeChildren[aisleIndex].treeChildren[sideIndex].treeChildren[bayIndex].fileOffset);
				changesTree.treeChildren[sectionIndex].treeChildren[aisleIndex].treeChildren[sideIndex].treeChildren.RemoveAt(bayIndex);
				//////////////////////////////////////////////////////////////////////
				// Add new bay to facility structure
				//////////////////////////////////////////////////////////////////////
				bTreeHelper.AddBaytoFacility(aAisle.getChildList()[0].getChildList()[0], changesTree.treeChildren[sectionIndex].treeChildren[aisleIndex].treeChildren[sideIndex].fileOffset, changesTree,bayIndex,sideIndex,aisleIndex,sectionIndex);
				
			}		
		}

	}

	if ( changesTree.elementDBID != 0 ) {
		ads_printf("Saving changes\n");
		facilityHelper.SaveFacility();
	}

	AfxMessageBox("Do not forget to re-lay the pickpath for each aisle in which you changed a bay.");

	return;
}


void CElementMaintenanceHelper::DeleteBay()
{
	CStringArray handles;

	if (CAutoCADCommands::GetSelectedHandles(handles) < 1) {
		do {
			if (AfxMessageBox("Please select one or more bays to delete.", MB_OKCANCEL) == IDCANCEL)
				return;
		} while (CAutoCADCommands::GetSelectedHandlesPrompt(handles) < 1);
	}

	int changedCount = 0, notFoundCount = 0, errorCount = 0;
	
	// todo: start a transaction here
	CString progressMsg;
	CProgressMessage progressDlg(progressMsg, 1, handles.GetSize(), 1, utilityHelper.GetParentWindow());

	for (int i=0; i < handles.GetSize(); ++i) {
		
		progressMsg.Format("Deleting %d of %d", i+1, handles.GetSize());
		progressDlg.UpdateMessage(progressMsg);

		TreeElement *bayPtr = changesTree.getBayByHandle(handles[i]);
		if (bayPtr == NULL) {
			controlService.Log("", "Handle %s is not a bay.\n", handles[i]);
			notFoundCount++;
			progressDlg.Step();
			continue;
		}
		
		// Check to see if the facility is integrated - if it is and the locations have products
		// assigned to them, don't allow the delete
		if (bayPtr->elementDBID > 0) {
			qqhSLOTBay bay;
			bTreeHelper.GetBtBay(bayPtr->fileOffset, bay);
			
			if (facilityDataService.IntegratedBayHasAssignments(bayPtr->elementDBID)) {
				CString temp;
				temp.Format("Bay %s has product assignments.  In a facility that is integrated\n"
					"only bays with no product assignments may be deleted.\n"
					"To delete the bay, set it to in-active, run Assign Products so that no\n"
					"products will be assigned to the bay, and then delete it.", bay.getDescription());
				AfxMessageBox(temp);
				errorCount++;
				progressDlg.Step();
				continue;
			}
		}

		if (CAutoCADCommands::DeleteDrawingObjectByHandle(handles[i]) < 0) {
			controlService.Log("", "Error deleting bay object from drawing for handle: %s\n", handles[i]);
			errorCount++;
			progressDlg.Step();
			continue;
		}

		try {
			
			if (bayPtr->elementDBID > 0) {
				// This will flag the element as deleted in the btree
				changesTree.DeletedBayMap.SetAt(bayPtr->elementDBID, bayPtr->elementDBID);
			}
			
			// This will actually remove the element from the local btree
			for (int j=0; j < bayPtr->treeParent->treeChildren.GetSize(); ++j) {
				if (bayPtr->treeParent->treeChildren[j].fileOffset == bayPtr->fileOffset) {
					bayPtr->treeParent->treeChildren.RemoveAt(j);
					break;
				}
			}
		}
		catch(...) {
			controlService.Log("", "Generic exception in bTreeHelper.DeleteFacilityBranch for handle: %s\n", handles[i]);
			errorCount++;
			progressDlg.Step();
			continue;
		}
		
		changedCount++;

		progressDlg.Step();

	}
	
	progressDlg.Hide();

	CString temp;
	if (handles.GetSize() == 1) {
		if (notFoundCount > 0)
			temp.Format("The bay could not be found.");
		else if (errorCount > 0)
			temp.Format("An error occurred while deleting the bay.");
		else
			temp.Format("The bay was successfully deleted.");
	}
	else {
		temp.Format("Completed Deleting Bays:\nBays selected: %d\nBays deleted: %d\nBays not found: %d\nErrors: %d",
			handles.GetSize(), changedCount, notFoundCount, errorCount);
	}
	
	numItemsProcessed += errorCount + changedCount;

	AfxMessageBox(temp);

	return;
}


void CElementMaintenanceHelper::DeleteAisle()
{

	CStringArray selectedHandleList;
	CStringArray aisleIdList;
	CStringArray bayHandles, tempHandles;
	CString tmpStr;
	TreeElement *aislePtr = NULL;
	BOOL found;
	CArray<int, int> aisleOffsetList;
	int i, j, k, rc;

	int errorCount = 0, count = 0, skipCount = 0;

	// Get user-selected bays
	if (CAutoCADCommands::GetSelectedHandles(selectedHandleList) <= 0) {
		AfxMessageBox("Please select at least one bay within each of the aisles you want to delete.");
		return;
	}

	if (AfxMessageBox("Are you sure you wish to delete the selected aisles?", MB_YESNO) != IDYES)
		return;

	CString progressMsg;
	CProgressMessage progressDlg(progressMsg, 1, selectedHandleList.GetSize(), 1, utilityHelper.GetParentWindow());

	// Build a list of bay handles in the aisles associated with the selected bays
	for (i=0; i < selectedHandleList.GetSize(); ++i) {

		progressMsg.Format("Processing selection %d of %d", i+1, selectedHandleList.GetSize());
		progressDlg.UpdateMessage(progressMsg);

		CWaitCursor cwc;
		// See if this handle is already in the list; we may have already gotten
		// all of the handles for this aisle
		found = FALSE;
		for (j=0; j < bayHandles.GetSize(); ++j) {
			if (strcmp(bayHandles[j], selectedHandleList[i]) == 0) {
				found = TRUE;
				break;
			}
		}

		if (found) {
			progressDlg.Step();
			continue;
		}

		// this will check the btree to see if the aisle is there
		// if not, get it from the database
		aislePtr = changesTree.getAisleByBayHandle(selectedHandleList[i]);
		if (aislePtr == NULL) {
			progressDlg.Step();
			continue;
		}

		// Look for the aisle in the list to see if we've already processed it
		found = FALSE;
		for (j=0; j < aisleOffsetList.GetSize(); ++j) {
			if (aisleOffsetList[j] == aislePtr->elementDBID) {
				found = TRUE;
				break;
			}
		}

		if (found) {			// we've already processed this aisle
			progressDlg.Step();
			continue;
		}

		aisleOffsetList.Add(aislePtr->elementDBID);

//		DumpTree("c:\\temp\\tree2.txt", changesTree);

		tempHandles.RemoveAll();
		// Get all of the bay handles that are currently in the btree
		// for this aisle; add them to the list if they are not already there
		if (changesTree.getBayHandlesByAisle(aislePtr, tempHandles) > 0) {
			for (j=0; j < tempHandles.GetSize(); ++j) {
				found = FALSE;
				for (k=0; k < bayHandles.GetSize(); ++k) {
					if (bayHandles[k] == tempHandles[j]) {
						found = TRUE;
						break;
					}
				}
				if (! found)
					bayHandles.Add(tempHandles[j]);

			}
		}
		// Get the list of handles from the database for this aisle in
		// case the btree was not fully populated

		// If the dbid is 0, this is a new aisle so there's nothing in the database
		if (aislePtr->elementDBID > 0) {
			qqhSLOTAisle aisle;
			bTreeHelper.GetBtAisle(aislePtr->fileOffset, aisle);

			// Check to see if the facility is integrated - if it is and the locations have products
			// assigned to them, don't allow the delete
			if (facilityDataService.IntegratedAisleHasAssignments(aislePtr->elementDBID)) {
				CString temp;
				temp.Format("Aisle %s has product assignments.  In a facility that is integrated\n"
					"only aisles with no product assignments may be deleted.\n"
					"To delete the aisle, set it to in-active, run Assign Products so that no\n"
					"products will be assigned to the aisle, and then delete it.", aisle.getDescription());
				AfxMessageBox(temp);
				progressDlg.Step();
				skipCount++;
				continue;
			}
						
			tempHandles.RemoveAll();
			try {
				if (facilityDataService.GetBayHandlesByAisleId(aislePtr->elementDBID, tempHandles) < 0) {
					progressDlg.Step();
					errorCount++;
					continue;
				}
			}
			catch(Ssa_Exception e) {
				char eMsg[1024];
				e.GetMessage(eMsg);
				controlService.Log("", "%s\n", eMsg);
				errorCount++;
				progressDlg.Step();
				continue;
			}
			catch(...) {
				controlService.Log("", "Generic error deleting aisle: %d\n", aislePtr->elementDBID);
				errorCount++;
				progressDlg.Step();
				continue;
			}	
			
			for (j=0; j < tempHandles.GetSize(); ++j) {
				found = FALSE;
				for (k=0; k < bayHandles.GetSize(); ++k) {
					if (bayHandles[k] == tempHandles[j]) {
						found = TRUE;
						break;
					}
				}
				if (! found)
					bayHandles.Add(tempHandles[j]);
			}
		}
		// Now bayHandles should contain all the handles that are in 
		// both the database (for existing bays) and the btree (for new or
		// changed bays).  Some of these may not be in the drawing so we
		// have to ignore errors when finding the drawing objects
		// Need to add transaction management to this
		for (j=0; j < bayHandles.GetSize(); ++j) {
			CWaitCursor cwc;
			rc = CAutoCADCommands::DeleteDrawingObjectByHandle(bayHandles[j]);
			numItemsProcessed++;
			// ignore errors for now
		}
		
		bayHandles.RemoveAll();
		
		// Delete the pick path object which should have the same handle as
		// the aisle itself
		if (strcmpi(aislePtr->acadHandle, "XXX") != 0) {

			// Delete any connection lines not owned by the current pick path
			qqhSLOTAisle aisle;
			bTreeHelper.GetBtAisle(aislePtr->fileOffset, aisle);
			TreeElement *sectionPtr = aislePtr->treeParent;
			qqhSLOTSection section;
			bTreeHelper.GetBtSection(sectionPtr->fileOffset, section);

			rc = DeletePickPathConnections(section, aisle);

			// The DeletePickPathConnections function loads more aisles into the btree
			// making the pointer into the changes tree invalid, so get it again
			aislePtr = changesTree.getAisleByBayHandle(selectedHandleList[i]);

			rc = CAutoCADCommands::DeleteDrawingObjectByHandle(aislePtr->acadHandle);
			numItemsProcessed++;
		}

		if (aislePtr->elementDBID > 0)
			// This will flag the element as deleted in the btree
			changesTree.DeletedAisleMap.SetAt(aislePtr->elementDBID, aislePtr->elementDBID);
	
		// Remove the aisle from the changes tree
		changesTree.deleteBranch(CString("SLOTAisle"), aislePtr);
		count++;

		CAutoCADCommands::Flush();

		progressDlg.Step();
	}

	progressDlg.Hide();

	if (count + skipCount + errorCount == 1) {
		if (count > 0)
			AfxMessageBox("The aisle was successfully deleted.");
		else if (errorCount > 0)
			AfxMessageBox("An error occurred while deleting the aisle.");
		else
			AfxMessageBox("The aisle was not deleted.");
	}
	else {
		CString temp;
		temp.Format("Completed Deleting Aisles:\n"
			"Aisles deleted: %d\nAisles Skipped: %d\nErrors: %d\n\n",
			count, skipCount, errorCount);
		AfxMessageBox(temp);
	}

	return;

}


void CElementMaintenanceHelper::DeletePickPath()
{
	CWinApp * currentApp;
	currentApp = AfxGetApp();
	
	CStringArray handles;

	if (CAutoCADCommands::GetSelectedHandles(handles) < 1) {
		do {
			if (AfxMessageBox("Please select one or more pick paths to delete.", MB_OKCANCEL) == IDCANCEL)
				return;
		} while (CAutoCADCommands::GetSelectedHandlesPrompt(handles) < 1);
	}
	
	int deleteCount=0, notPickPathCount=0, errorCount=0;

	CString progressMsg;
	CProgressMessage progressDlg(progressMsg, 1, handles.GetSize(), 1, utilityHelper.GetParentWindow());

	for (int hIdx=0; hIdx < handles.GetSize(); ++hIdx) {
		
		progressMsg.Format("Deleting pick path %d of %d", hIdx+1, handles.GetSize());
		progressDlg.UpdateMessage(progressMsg);

		currentApp->DoWaitCursor(1);

		try {
			if (! facilityDataService.IsHandleAPickPath(handles[hIdx])) {
				controlService.Log("", "Handle %s is not a pick path.\n", handles[hIdx]);
				notPickPathCount++;
				continue;
			}
		}
		catch (...) {
			controlService.Log("", "Handle %s is not a pick path.\n", handles[hIdx]);
			notPickPathCount++;
			continue;
		}

		//////////////////////////////////////////////////////////////////////
		// update the facility structure - aisle pathlength must change
		//////////////////////////////////////////////////////////////////////
		TreeElement *aPtr;

		aPtr = changesTree.getAisleByPickPathHandle(handles[hIdx]);
		if (aPtr == NULL) {
			controlService.Log("", "Skipping: %s\n", handles[hIdx]);
			notPickPathCount++;
			continue;
		}

		TreeElement *sPtr = aPtr->treeParent;
		qqhSLOTSection section;
		bTreeHelper.GetBtSection(sPtr->fileOffset, section);

		qqhSLOTAisle aisle;
		bTreeHelper.GetBtAisle(aPtr->fileOffset, aisle);
		
		section.setSelDist(section.getSelDist() - aisle.getPickPath().getPathLength());

		
		// The following code deletes the connection lines between this pick path and the 
		// following or preceding pick paths.
		// Sometimes the connection line is owned by the preceding and sometimes it's owned by the
		// next pick path so we have to look at both
		if (DeletePickPathConnections(section, aisle) < 0) {
			controlService.Log("", "Error deleting connections for handle %s\n", handles[hIdx]);
		}


		aisle.getPickPath().setAcadHandle("XXX");
		aisle.getPickPath().setConAcadHandle("XXX");
		aisle.getPickPath().setPathLength(0);

		// Delete the drawing object first because it's more likely to have an error
		if (CAutoCADCommands::DeleteDrawingObjectByHandle(handles[hIdx]) < 0) {
			controlService.Log("", "Error deleting drawing object.\n");
			errorCount++;
			continue;
		}

		// Get these pointers again because the tree may have changed when we added stuff to it
		aPtr = changesTree.getAisleByPickPathHandle(handles[hIdx]);
		sPtr = aPtr->treeParent;
		strcpy(aPtr->acadHandle, "XXX");

		bTreeHelper.SetBtAisle(aPtr->fileOffset, aisle);
		bTreeHelper.SetBtSection(sPtr->fileOffset, section);
		
		deleteCount++;

		progressDlg.Step();
	}

	progressDlg.Hide();

	numItemsProcessed += (deleteCount + errorCount);
	currentApp->DoWaitCursor(-1);

	CString temp;
	if (handles.GetSize() == 1) {
		if (notPickPathCount > 0)
			temp.Format("The pick path could not be found.");
		else if (errorCount > 0)
			temp.Format("An error occurred while attempting to delete the pick path.");
		else
			temp.Format("The pick path was successfully deleted.");
	}
	else {
		temp.Format("Completed Deleting Pick Paths:\nItems selected: %d\n"
			"Pick paths deleted: %d\nPick paths not found: %d\nErrors: %d",
			handles.GetSize(), deleteCount, notPickPathCount, errorCount);
	}

	
	AfxMessageBox(temp);

	return;
}


/*
int CElementMaintenanceHelper::AddMoveStockItem(const char * itemFileName, int objectType, const char * displayText,
				  float & x, float & y, float & z, char * handle, float width, float depth, float height) 
{
	CTemporaryResourceOverride thisResource;
	CObjectPlaceDialog PlaceObjectDialog(CWnd::FromHandle(adsw_acadMainWnd()));
	Acad::ErrorStatus errorStatus;				//Autocad error status
	AcDbBlockTable *blockTable;					//pointer to Autocad block table
	AcDbDatabase *blockDb = NULL;				//pointer to Autocad block db
	CString strObjectProfileDir;
	AcDbObjectId blockId;						//Autocad blockId
	AcDbBlockReference * blockReference;		//pointer to Autocad block reference
	AcDbObjectId newObjId;						//second Autocad object
	AcDbBlockTableRecord * blockTableRecord;	//pointer to Autocad block table record
	AcGeMatrix3d mat;							//transform matrix
	AcDbEntity * entityPtr;						//pointer to Autocad entity
	AcDbObjectId objId;							//Autocad object id
	AcDbHandle objHandle;						//Autocad block handle
	int count, someNotClosed = 0;

	PlaceObjectDialog.m_Increment = "1";
	someNotClosed = 0;
	PlaceObjectDialog.m_ObjectType = objectType;

	//////////////////////////////////////////////////////////////////////
	// set up the dialog information
	//////////////////////////////////////////////////////////////////////
	PlaceObjectDialog.m_Message = displayText;
	if ( width == -1.0 ) {
		PlaceObjectDialog.m_Width = "1.0";
		PlaceObjectDialog.m_Height = "1.0";
		PlaceObjectDialog.m_Length = "1.0";
	}
	else {
		PlaceObjectDialog.m_Width.Format(".0f", width);
		PlaceObjectDialog.m_Length.Format(".0f", depth);
		PlaceObjectDialog.m_Height.Format(".0f", height);
	}

	if ( PlaceObjectDialog.DoModal() == IDCANCEL ) {
		ads_printf("Operation Cancelled\n");
		return -1;
	}

	CAutoCADCommands::DeleteDrawingObjectByHandle(handle);

	//////////////////////////////////////////////////////////////////////
	// insert the item into the drawing.  These are ARX calls
	// to open appropriate tables, etc. and insert the new block
	//////////////////////////////////////////////////////////////////////
	errorStatus = acdbCurDwg()->getBlockTable(blockTable, AcDb::kForRead);
	if (errorStatus != Acad::eOk) 	{
		controlService.Log("An error occurred while adding the item.", 
			"Error(%d) getting block table.\n", errorStatus);
		return -1;
	}
	
	errorStatus = blockTable->getAt(ACDB_MODEL_SPACE, blockTableRecord, AcDb::kForWrite);
	if (errorStatus != Acad::eOk) {
		blockTable->close();
		controlService.Log("An error occurred while adding the item.", 
			"Error(%d) getting block table record.\n", errorStatus);
		return -1;
	}

	blockTable->close();

	blockDb = new AcDbDatabase(Adesk::kFalse);
	errorStatus = blockDb->readDwgFile(itemFileName);
	if (errorStatus != Acad::eOk) {
		CString strMsg = "Could not open ";
		strMsg += strObjectProfileDir;
		controlService.Log(strMsg, "Error (%d) during readDwgFile.\n", errorStatus);
		return -1;
	}


	//////////////////////////////////////////////////////////////////////
	// inserts the current block into the dwg.
	//////////////////////////////////////////////////////////////////////
	CTime theTime1 = CTime::GetCurrentTime();
	CString t = theTime1.Format("%y%j%H%M%S");
	CString objName = itemFileName + t;
	errorStatus = acdbCurDwg()->insert(blockId, objName.GetBuffer(0),blockDb);
	objName.ReleaseBuffer();
	if (errorStatus != Acad::eOk) 
	{
		
		return -1;
	}
	numItemsProcessed++;
	mat(0, 3) = PlaceObjectDialog.m_ObjPlace_XCoord; 
	mat(1, 3) = PlaceObjectDialog.m_ObjPlace_YCoord;   
	mat(2, 3) = PlaceObjectDialog.m_ObjPlace_ZCoord;
	x = PlaceObjectDialog.m_ObjPlace_XCoord; 
	y = PlaceObjectDialog.m_ObjPlace_YCoord;
	z = PlaceObjectDialog.m_ObjPlace_ZCoord; 
	//////////////////////////////////////////////////////////////////////
	// Create a block reference  and set scale, rotation and transformBy
	//////////////////////////////////////////////////////////////////////
	blockReference = new AcDbBlockReference;
	blockReference->setBlockTableRecord(blockId);
	blockReference->setRotation(0.0);
	blockReference->transformBy(mat);

	AcGeScale3d  scale(PlaceObjectDialog.m_ObjPlace_Width,PlaceObjectDialog.m_ObjPlace_Depth,PlaceObjectDialog.m_ObjPlace_Height);

	blockReference->setScaleFactors(scale);
	//////////////////////////////////////////////////////////////////////
	// Append the reference entity to .dwg
	//////////////////////////////////////////////////////////////////////
	blockTableRecord->appendAcDbEntity(newObjId, blockReference);
	
	count = 0;
	while (blockReference->close() != Acad::eOk && count < 10 )
		count++;
	if ( count == 10 )
		someNotClosed = 1;

	count = 0;
	while (blockTableRecord->close() != Acad::eOk && count < 10 )
		count++;
	if ( count == 10 )
		someNotClosed = 1;

	errorStatus = acdbOpenAcDbEntity(entityPtr, newObjId, AcDb::kForWrite);
	if ( errorStatus != Acad::eOk ) {
		ads_printf("Error Opening Object : %d\n",errorStatus);
		return -1;
	}

	//////////////////////////////////////////////////////////////////////
	// update the AutoCad "handle" name
	//////////////////////////////////////////////////////////////////////
	entityPtr->getAcDbHandle(objHandle);
 

	objHandle.getIntoAsciiBuffer(handle);

	//////////////////////////////////////////////////////////////////////
	// Close the refernce and delete block
	//////////////////////////////////////////////////////////////////////
	count = 0;
	while (entityPtr->close() != Acad::eOk && count < 10 )
		count++;
	if ( count == 10 )
		someNotClosed = 1;

	if (blockDb != NULL)
		delete blockDb;

	CAutoCADCommands::ColorDrawingObjectByHandle(handle, 5);
	//////////////////////////////////////////////////////////////////////
	// check to make sure all objects closed OK.
	//////////////////////////////////////////////////////////////////////
	if (someNotClosed == 1)
		AfxMessageBox("Some Autocad items did not close properly.  It is recommended that you save, exit, and reopen the application");
	return 0;
}
*/

int CElementMaintenanceHelper::ReAddPickPathinAisle(AcDbObjectId * pathObjIdptr, int firstTime, AcGePoint3d &lastPoint, qqhSLOTAisle &aisle) 
{
	CTemporaryResourceOverride thisResource;
	CPickPathPropertiesDialog PickPathProperties;
	ads_point startPoint;
	ads_point endPoint;
	ads_point selPoint;
	ads_name bayName;
	AcDbHandle bayHandle;
	AcDbObjectId bayObjectId,vertObjectId,pathObjectId;
	AcDbObject * bayObject;
	int getRet;
	int i,j,k,l,m;
	int foundBay = 0;
	int bayIndex, sideIndex, aisleIndex, sectionIndex;
	int aisleStoreId, sectionStoreId;
	int selIndex, bayPattern;
	CString bayStart;
	double deltaX, deltaY, diffX, diffY;
	AcGePoint3dArray lsAislePointList;
	AcGePoint3dArray rsAislePointList;
	AcGePoint3dArray cAislePointList;
	AcGePoint3dArray centerPointSide1List;
	AcGePoint3dArray centerPointSide2List;
	AcGePoint3dArray pickPathPointList;
	AcGePoint3dArray sectionPickPathPointList;
	AcDbObject * pEnt;
	AcDbBlockReference * pBlockRef;
	AcDbHandle newObjHandle;
	CWinApp * currentApp;
	currentApp = AfxGetApp();

	AcGePoint3d tempPointCenter1;
	AcGePoint3d tempPointCenter2;
	AcGePoint3d tempPointlsAisle;
	AcGePoint3d tempPointrsAisle;
	AcGePoint3d tempPointcAisle;
	AcGePoint3d tempPoint;
	AcGePoint3d tempPointStart;
	AcGePoint3d tempPointEnd;
	Acad::ErrorStatus errorStatus;

	AcDbEntity * entityPtr;

	qqhSLOTSection tempSection;
	qqhSLOTAisle tempAisle;
		
	AcDb3dPolyline * pickPathLine;

    AcDbBlockTable * blockTable;
    AcDbBlockTableRecord * blockTableRecord;
    AcDbObjectId lineObjId,bayObjId;
	AcDbHandle tempDbHandle;

	double baySpace, bayDepth, bayHeight, rotateAngle;

	char handleBuf[20];
	char temphandleBuf[20];
	int prevIndex = -1;
	int swapMe,rightBay;
	CSsaStringArray fingerHandleArray;
	CArray <int, int&> fingerHandleAisles;
	CArray <int, int&> fingerHandleSides;
	CArray <int, int&> tempBayIndexList;
	CString strData;
	int notDone = 1;
	int count;
	int someNotClosed = 0;
	sectionIndex = -1;
	char newHandle[20];
	double pathLength = 0.0;
	BOOL retrievedFromDB = FALSE;
	int returnCode;

	int startIndex, endIndex, indexStep;

	CMap<int, int, CBayProfile, CBayProfile&> bayProfileMap;

	CString tempHandle;
	strcpy(oldPickPathHandle, "XXX");

	////////////////////////////////////////////////////////////////
	//  Display the dialog and get the appropriate data.  Use
	//  last information if available.
	////////////////////////////////////////////////////////////////
	PickPathProperties.m_PickPathProperties_PathType_Val = aisle.getPickPathType();
	PickPathProperties.m_PickPathProperties_PatternNum = aisle.getBaysInPattern();
	PickPathProperties.m_LBayStartVal = aisle.getLeftBayStart();
	PickPathProperties.m_RBayStart = aisle.getRightBayStart();
	PickPathProperties.m_LLevelStart = aisle.getLeftLevelStart(); 
	PickPathProperties.m_RLevelStart = aisle.getRightLevelStart();
	PickPathProperties.m_LLocStart = aisle.getLeftLocationStart(); 
	PickPathProperties.m_RLocStart = aisle.getRightLocationStart();
	PickPathProperties.m_LBaySchemeVal = aisle.getLeftBayScheme(); 
	PickPathProperties.m_RBaySchemeVal = aisle.getRightBayScheme();
	PickPathProperties.m_LLevelSchemeVal = aisle.getLeftLevelScheme(); 
	PickPathProperties.m_RLevelSchemeVal = aisle.getRightLevelScheme();
	PickPathProperties.m_LLocSchemeVal = aisle.getLeftLocationScheme(); 
	PickPathProperties.m_RLocSchemeVal = aisle.getRightLocationScheme();
	PickPathProperties.m_LBayStepVal = aisle.getLeftBayStep(); 
	PickPathProperties.m_RBayStep = aisle.getRightBayStep();
	PickPathProperties.m_LLevelStep = aisle.getLeftLevelStep(); 
	PickPathProperties.m_RLevelStep = aisle.getRightLevelStep();
	PickPathProperties.m_LLocStep = aisle.getLeftLocationStep(); 
	PickPathProperties.m_RLocStep = aisle.getRightLocationStep();
	PickPathProperties.m_LLevelBreak = aisle.getLeftLevelBreak();
	PickPathProperties.m_RLevelBreak = aisle.getRightLevelBreak();
	PickPathProperties.m_LLocBreakVal = aisle.getLeftLocationBreak(); 
	PickPathProperties.m_RLocBreakVal = aisle.getRightLocationBreak();

	PickPathProperties.m_LBayPatternVal = aisle.getLeftBayPattern();
	PickPathProperties.m_LLevelPatternVal = aisle.getLeftLevelPattern();
	PickPathProperties.m_LLocPatternVal = aisle.getLeftLocPattern();
	PickPathProperties.m_RBayPatternVal = aisle.getRightBayPattern();
	PickPathProperties.m_RLevelPatternVal = aisle.getRightLevelPattern();
	PickPathProperties.m_RLocPatternVal = aisle.getRightLocPattern();
	///////////////////////////////////////////////////////////
	// Display the pickpath properties dialog and allow the 
	// user to enter the properties of the pickpath.
	///////////////////////////////////////////////////////////
	while (notDone==1) {
		if ( PickPathProperties.DoModal() == IDCANCEL ) {
			return -1;
		}
		else
			notDone = 0;
	}

	selIndex = PickPathProperties.m_PickPathProperties_PathType_Val;
	bayPattern = PickPathProperties.m_PickPathProperties_PatternNum;

	///////////////////////////////////////////////////////////
	// reset history information
	///////////////////////////////////////////////////////////
	SavePickPathValues(PickPathProperties);

	///////////////////////////////////////////////////////////
	// If the user chooses "Finger" pickpath, we need the
	// starting and ending points.
	///////////////////////////////////////////////////////////
	if ( selIndex == 1 ) {
		////////////////////////////////////////////////////////////////
		// Get the starting point of the pick path within the aisle
		////////////////////////////////////////////////////////////////
		getRet = ads_getpoint(NULL, "Please Enter the Starting Point :", startPoint);
		ads_printf("\n");
		if ( getRet == RTCAN )
			return -1;
		if ( getRet == RTERROR ) {
			AfxMessageBox("Error Getting First Point.  Exiting.");
			return -1;
		}

		////////////////////////////////////////////////////////////////
		// Get the ending point of the pick path within the aisle
		////////////////////////////////////////////////////////////////
		getRet = ads_getpoint(startPoint, "Please Enter the Ending Point :", endPoint);
		ads_printf("\n");
		if ( getRet == RTCAN )
			return -1;
		if ( getRet == RTERROR ) {
			ads_printf("Error Getting Point \n");
			return -1;
		}
	}
	///////////////////////////////////////////////////////////
	// Continue until the user chooses a correct bay
	///////////////////////////////////////////////////////////
	rightBay = 0;
	while ( rightBay == 0 ) {
		rightBay = 1;
		////////////////////////////////////////////////////////////////
		// Get the bay starting the pick path.  The user did not choose
		// "Finger" pickpath
		////////////////////////////////////////////////////////////////
		if ( selIndex != 1 ) {
			getRet = ads_entsel( "Please Select the Bay in the Aisle starting the Path:", bayName, selPoint);
			ads_printf("\n");
			if ( getRet == RTCAN )
				return -1;
			if ( getRet == RTERROR ) {
				ads_printf("Error Getting Bay \n");
				return -1;
			}
			///////////////////////////////////////////////////////////
			// Get the unique AutoCad handle of this object
			///////////////////////////////////////////////////////////
			errorStatus = acdbGetObjectId(bayObjectId, bayName);
			if ( errorStatus != Acad::eOk ) {
				ads_printf("Error Finding Object Id\n");
				return -1;
			}
			errorStatus = acdbOpenAcDbObject(bayObject,bayObjectId,AcDb::kForRead);
			if ( errorStatus != Acad::eOk ) {
				ads_printf("Error Opening Object\n");
				return -1;
			}
			bayObject->getAcDbHandle(bayHandle);

			bayHandle.getIntoAsciiBuffer(handleBuf);
			CString tempMsg = "Found Bay : " + CString(handleBuf);
			//AfxMessageBox(tempMsg);
		}
		else {
			///////////////////////////////////////////////////////////
			// The user chose "Finger" pickpath
			///////////////////////////////////////////////////////////
			getRet = 0;
			while ( getRet != RTERROR ) {
				getRet = ads_entsel( "Please Select the Bay in each Aisle nearest the Finger Path, '[RET]' to stop:", bayName, selPoint);
				ads_printf("\n");
				if ( getRet == RTCAN ) {
					ads_printf("Command Cancelled\n");
					return -1;
					///////////////////////////////////////////////////////////
					// Reset bays back to white once all are chosen
					///////////////////////////////////////////////////////////
					for ( i = 0; i < fingerHandleArray.GetSize(); i++) {
						errorStatus = acdbCurDwg()->getAcDbObjectId(bayObjectId, FALSE, fingerHandleArray[i].GetBuffer(0));
						fingerHandleArray[i].ReleaseBuffer();
						if (errorStatus != Acad::eOk)
						{
							AfxMessageBox("Error returned from getAcDbObjectId()!");
							return -1;
						}
						errorStatus = acdbOpenAcDbEntity(entityPtr, bayObjectId, AcDb::kForWrite);
						if (errorStatus != Acad::eOk)
						{
							AfxMessageBox("Error returned from acdbOpenAcDbEntity()!");
							return -1;
						}

						entityPtr->setColorIndex(7);
						CString strLayerName = entityPtr->layer();
						AcDbObjectId layerId = entityPtr->layerId();
						if (layerId != AcDbObjectId::kNull)
						{
							AcDbLayerTable *pLayerTable;
							AcDbLayerTableRecord *pLayerRec;
							Acad::ErrorStatus es = acdbCurDwg()->getLayerTable(pLayerTable, AcDb::kForWrite);
							es = pLayerTable->getAt(strLayerName, pLayerRec, AcDb::kForWrite);
							if (es != Acad::eOk)
							{
								AfxMessageBox("Error getting layer!");
								return -1;
							}
							AcCmColor color;
							color.setColorIndex(7);
							pLayerRec->setColor(color);
							pLayerRec->close();
							pLayerTable->close();
						}
						entityPtr->close();
					}
				}
				else if ( getRet != RTERROR ) {
					errorStatus = acdbGetObjectId(bayObjectId, bayName);
					if ( errorStatus != Acad::eOk ) {
						AfxMessageBox("Acad Error : Error Finding Object Id.");
						return -1;
					}
					///////////////////////////////////////////////////////////
					// Get the unique AutoCad handle for this bay.  Add it to
					// an array of strings
					///////////////////////////////////////////////////////////
					errorStatus = acdbOpenAcDbObject(bayObject,bayObjectId,AcDb::kForRead);
					if ( errorStatus != Acad::eOk ) {
						AfxMessageBox("Error Opening Object");
						return -1;
					}

					bayObject->getAcDbHandle(bayHandle);
					bayHandle.getIntoAsciiBuffer(handleBuf);
					fingerHandleArray.Add(handleBuf);

					bayObject->close();

					///////////////////////////////////////////////////////////
					// Open the bay object for Write
					///////////////////////////////////////////////////////////
					errorStatus = acdbCurDwg()->getAcDbObjectId(bayObjectId, FALSE, bayHandle);
					if (errorStatus != Acad::eOk)
					{
						AfxMessageBox("Error returned from getAcDbObjectId()!");
						return -1;
					}
					errorStatus = acdbOpenAcDbEntity(entityPtr, bayObjectId, AcDb::kForWrite);
					if (errorStatus != Acad::eOk)
					{
						AfxMessageBox("Error returned from acdbOpenAcDbEntity()!");
						return -1;
					}

					///////////////////////////////////////////////////////////
					// Ease of use enhancement - color bays chosen red as they
					// select them.  Do this by coloring the layer.
					///////////////////////////////////////////////////////////
					entityPtr->setColorIndex(1);
					CString strLayerName = entityPtr->layer();
					AcDbObjectId layerId = entityPtr->layerId();
					if (layerId != AcDbObjectId::kNull)
					{
						AcDbLayerTable *pLayerTable;
						AcDbLayerTableRecord *pLayerRec;
						Acad::ErrorStatus es = acdbCurDwg()->getLayerTable(pLayerTable, AcDb::kForWrite);
						es = pLayerTable->getAt(strLayerName, pLayerRec, AcDb::kForWrite);
						if (es != Acad::eOk)
						{
							AfxMessageBox("Error getting layer!");
							return -1;
						}
						AcCmColor color;
						color.setColorIndex(1);
						pLayerRec->setColor(color);
						pLayerRec->close();
						pLayerTable->close();
					}
					entityPtr->close();
				}
			}
			///////////////////////////////////////////////////////////
			// Reset bays back to white once all are chosen
			///////////////////////////////////////////////////////////
			for ( i = 0; i < fingerHandleArray.GetSize(); i++) {
				errorStatus = acdbCurDwg()->getAcDbObjectId(bayObjectId, FALSE, fingerHandleArray[i].GetBuffer(0));
				fingerHandleArray[i].ReleaseBuffer();
				if (errorStatus != Acad::eOk)
				{
					AfxMessageBox("Error returned from getAcDbObjectId()!");
					return -1;
				}
				errorStatus = acdbOpenAcDbEntity(entityPtr, bayObjectId, AcDb::kForWrite);
				if (errorStatus != Acad::eOk)
				{
					AfxMessageBox("Error returned from acdbOpenAcDbEntity()!");
					return -1;
				}

				entityPtr->setColorIndex(7);
				CString strLayerName = entityPtr->layer();
				AcDbObjectId layerId = entityPtr->layerId();
				if (layerId != AcDbObjectId::kNull)
				{
					AcDbLayerTable *pLayerTable;
					AcDbLayerTableRecord *pLayerRec;
					Acad::ErrorStatus es = acdbCurDwg()->getLayerTable(pLayerTable, AcDb::kForWrite);
					es = pLayerTable->getAt(strLayerName, pLayerRec, AcDb::kForWrite);
					if (es != Acad::eOk)
					{
						AfxMessageBox("Error getting layer!");
						return -1;
					}
					AcCmColor color;
					color.setColorIndex(7);
					pLayerRec->setColor(color);
					pLayerRec->close();
					pLayerTable->close();
				}
				entityPtr->close();
			}
		}
		if ( selIndex != 1 ) {
			////////////////////////////////////////////////////////////////
			// Find the Aisle that the bay is in
			////////////////////////////////////////////////////////////////
			foundBay = 0;
			/*	 Always retrieve the aisle to the btree because maybe only
				 one bay is retrieved and this test would indicate that the
				 whole aisle is
			for ( i = 0; i < changesTree.treeChildren.GetSize() && foundBay== 0; i++ ) {
				//Sections
				for ( j = 0; j < changesTree.treeChildren[i].treeChildren.GetSize() && foundBay == 0; j++ ) {
					//Aisles
					for (k = 0; k < changesTree.treeChildren[i].treeChildren[j].treeChildren.GetSize() && foundBay == 0; k++ ) {
						//Sides
						for (l = 0; l < changesTree.treeChildren[i].treeChildren[j].treeChildren[k].treeChildren.GetSize() && foundBay == 0; l++ ) {
							//Bays -- look here
							strcpy(temphandleBuf,changesTree.treeChildren[i].treeChildren[j].treeChildren[k].treeChildren[l].acadHandle);
							CString tempMsg = "Comparing Bay : " + CString(temphandleBuf);
							//AfxMessageBox(tempMsg);
							if ( strcmp(changesTree.treeChildren[i].treeChildren[j].treeChildren[k].treeChildren[l].acadHandle,handleBuf) == 0 ) {
								foundBay = 1;
								aisleStoreId = changesTree.treeChildren[i].treeChildren[j].fileOffset;
								bTreeHelper.GetBtAisle(aisleStoreId,tempAisle);
								///////////////////////////////////////////////////////////
								// If the aisle already has a pickpath in it, we
								// will not allow the user to add another.
								///////////////////////////////////////////////////////////
								if (strcmp(tempAisle.getPickPath().getAcadHandle(),"XXX") != 0 && strcmp(tempAisle.getPickPath().getAcadHandle(),"") != 0) {
									acdbCurDwg()->getAcDbObjectId(pathObjectId,Adesk::kFalse,tempAisle.getPickPath().getAcadHandle());
									
									errorStatus = acdbOpenAcDbObject(pEnt, pathObjectId, AcDb::kForRead);
									pEnt->close();
									if ( errorStatus == Acad::eOk) {
										AfxMessageBox("This aisle already has a pick path.  Exiting pick path insertion. ");
										return -1;
									}
								}								
								sectionStoreId = changesTree.treeChildren[i].fileOffset;
							}
						}
					}
				}
			}
			*/
			count = 1;
			while (bayObject->close() != Acad::eOk && count < 10 )
				count++;
			if ( count == 10 )
				someNotClosed = 1;
			if ( foundBay == 1 ) {
				i--; j--; k--; l--;
				bayIndex = l;
				sideIndex = k;
				aisleIndex = j;
				sectionIndex = i;
			}
			else {
				///////////////////////////////////////////////////////////
				// Retrieve Aisle from the DB if it is not in the 
				// changes temp file.
				///////////////////////////////////////////////////////////
 				try {
					returnCode = bTreeHelper.UpdateBTWithAisleForPickPath(CString(handleBuf), changesTree, &bayIndex, &aisleIndex, &sectionIndex, &sideIndex);
					if (returnCode == 0) {
						retrievedFromDB = TRUE;
						aisleStoreId = changesTree.treeChildren[sectionIndex].treeChildren[aisleIndex].fileOffset;
						sectionStoreId = changesTree.treeChildren[sectionIndex].fileOffset;
						bTreeHelper.GetBtAisle(aisleStoreId,tempAisle);
						///////////////////////////////////////////////////////////
						// If the aisle already has a pickpath in it, we
						// will not allow the user to add another.
						///////////////////////////////////////////////////////////
						if (strcmp(tempAisle.getPickPath().getAcadHandle(),"XXX") != 0 && strcmp(tempAisle.getPickPath().getAcadHandle(),"") != 0) {
							errorStatus = acdbCurDwg()->getAcDbObjectId(pathObjectId,Adesk::kFalse,tempAisle.getPickPath().getAcadHandle());
							if (errorStatus == Acad::eOk) {
								errorStatus = acdbOpenAcDbObject(pEnt, pathObjectId, AcDb::kForRead);
								pEnt->close();
								if ( errorStatus == Acad::eOk) {
									AfxMessageBox("This aisle already has a pick path.  Exiting pick path insertion. ");
									return -1;
								}
							}
						}
					}
					else {
						// It must be a new aisle so find it in the btree and assume
						// the whole thing is there
						foundBay = 0;
						for ( i = 0; i < changesTree.treeChildren.GetSize() && foundBay== 0; i++ ) {
							//Sections
							for ( j = 0; j < changesTree.treeChildren[i].treeChildren.GetSize() && foundBay == 0; j++ ) {
								//Aisles
								for (k = 0; k < changesTree.treeChildren[i].treeChildren[j].treeChildren.GetSize() && foundBay == 0; k++ ) {
									//Sides
									for (l = 0; l < changesTree.treeChildren[i].treeChildren[j].treeChildren[k].treeChildren.GetSize() && foundBay == 0; l++ ) {
										//Bays -- look here
										strcpy(temphandleBuf,changesTree.treeChildren[i].treeChildren[j].treeChildren[k].treeChildren[l].acadHandle);
										CString tempMsg = "Comparing Bay : " + CString(temphandleBuf);
										//AfxMessageBox(tempMsg);
										if ( strcmp(changesTree.treeChildren[i].treeChildren[j].treeChildren[k].treeChildren[l].acadHandle,handleBuf) == 0 ) {
											foundBay = 1;
											aisleStoreId = changesTree.treeChildren[i].treeChildren[j].fileOffset;
											bTreeHelper.GetBtAisle(aisleStoreId,tempAisle);
											///////////////////////////////////////////////////////////
											// If the aisle already has a pickpath in it, we
											// will not allow the user to add another.
											///////////////////////////////////////////////////////////
											if (strcmp(tempAisle.getPickPath().getAcadHandle(),"XXX") != 0 && strcmp(tempAisle.getPickPath().getAcadHandle(),"") != 0) {
												acdbCurDwg()->getAcDbObjectId(pathObjectId,Adesk::kFalse,tempAisle.getPickPath().getAcadHandle());
												
												errorStatus = acdbOpenAcDbObject(pEnt, pathObjectId, AcDb::kForRead);
												pEnt->close();
												if ( errorStatus == Acad::eOk) {
													AfxMessageBox("This aisle already has a pick path.  Exiting pick path insertion. ");
													return -1;
												}
											}								
											sectionStoreId = changesTree.treeChildren[i].fileOffset;
										}
									}
								}
							}
						}
						
						if ( foundBay == 1 ) {
							i--; j--; k--; l--;
							bayIndex = l;
							sideIndex = k;
							aisleIndex = j;
							sectionIndex = i;
						}
						else {
							AfxMessageBox("Unable to find the aisle in the facility.");
							return -1;
						}
					}
				}
				catch(...) {
					AfxMessageBox("Unable to locate Aisle in facility : 2\n");
					return -1;
				}
			}
			///////////////////////////////////////////////////////////
			// Verify the bay chosen is at the end or beginning of 
			// the aisle
			///////////////////////////////////////////////////////////
			if ( bayIndex != 0 && bayIndex != changesTree.treeChildren[sectionIndex].treeChildren[aisleIndex].treeChildren[sideIndex].treeChildren.GetSize() - 1) {
				ads_printf("Bay Chosen is not at the beginning/ending of the aisle\n");
				rightBay = 0;
			}
			else {
				///////////////////////////////////////////////////////////
				// Special processing for one-bay aisles
				///////////////////////////////////////////////////////////
				if (bayIndex == 0 && bayIndex == changesTree.treeChildren[sectionIndex].treeChildren[aisleIndex].treeChildren[sideIndex].treeChildren.GetSize() - 1) {
					int oneNum = 1;
					if (AfxMessageBox("Only one bay in Aisle.  Number locations Ascending?",MB_YESNO) == IDNO )
						tempBayIndexList.Add(oneNum);
					else
						tempBayIndexList.Add(bayIndex);
				}
				else
					tempBayIndexList.Add(bayIndex);
			}
		}
		else {
			for ( m = 0; m < fingerHandleArray.GetSize(); m++ ) {
				foundBay = 0;
				for ( i = 0; i < changesTree.treeChildren.GetSize() && foundBay== 0; i++ ) {
					//Sections
					for ( j = 0; j < changesTree.treeChildren[i].treeChildren.GetSize() && foundBay == 0; j++ ) {
						//Aisles
						for (k = 0; k < changesTree.treeChildren[i].treeChildren[j].treeChildren.GetSize() && foundBay == 0; k++ ) {
							//Sides
							for (l = 0; l < changesTree.treeChildren[i].treeChildren[j].treeChildren[k].treeChildren.GetSize() && foundBay == 0; l++ ) {
								//Bays -- look here
								strcpy(temphandleBuf,fingerHandleArray[m].GetBuffer(0));
								fingerHandleArray[m].ReleaseBuffer();
								strcpy(temphandleBuf,changesTree.treeChildren[i].treeChildren[j].treeChildren[k].treeChildren[l].acadHandle);
								///////////////////////////////////////////////////////////
								// Do not allow the user to put more than one pickpath
								// in an aisle.
								///////////////////////////////////////////////////////////
								if ( strcmp(changesTree.treeChildren[i].treeChildren[j].treeChildren[k].treeChildren[l].acadHandle,fingerHandleArray[m].GetBuffer(0)) == 0 ) {
									foundBay = 1;
									aisleStoreId = changesTree.treeChildren[i].treeChildren[j].fileOffset;
									sectionStoreId = changesTree.treeChildren[i].fileOffset;
									bTreeHelper.GetBtAisle(aisleStoreId,tempAisle);
									if (strcmp(tempAisle.getPickPath().getAcadHandle(),"XXX") != 0  && strcmp(tempAisle.getPickPath().getAcadHandle(),"") != 0 ) {
										errorStatus = acdbCurDwg()->getAcDbObjectId(pathObjectId,Adesk::kFalse,tempAisle.getPickPath().getAcadHandle());
										if (errorStatus == Acad::eOk) {
											errorStatus = acdbOpenAcDbObject(pEnt, pathObjectId, AcDb::kForRead);
											pEnt->close();
											if ( errorStatus == Acad::eOk) {
												AfxMessageBox("This aisle already has a pick path.  Exiting pick path insertion. ");
												return -1;
											}
										}
									}								
								}
								fingerHandleArray[m].ReleaseBuffer();
							}
						}
					}
				}
				count = 1;
				while (bayObject->close() != Acad::eOk && count < 10 )
					count++;
				if ( count == 10 )
					someNotClosed = 1;
				if ( foundBay == 1 ) {
					i--; j--; k--; l--;
					bayIndex = l;
					sideIndex = k;
					aisleIndex = j;
					fingerHandleAisles.Add(j);
					fingerHandleSides.Add(k);
					sectionIndex = i;

				}
				else {
					///////////////////////////////////////////////////////////
					// Retrieve aisle to temp file from DB if it is not 
					// already there
					///////////////////////////////////////////////////////////
					currentApp->DoWaitCursor(1);
					try {
						returnCode = bTreeHelper.UpdateBTWithAisleForPickPath(fingerHandleArray[m], changesTree, &bayIndex, &aisleIndex, &sectionIndex, &sideIndex);
						if (returnCode == 0) {
							retrievedFromDB = TRUE;
							aisleStoreId = changesTree.treeChildren[sectionIndex].treeChildren[aisleIndex].fileOffset;
							sectionStoreId = changesTree.treeChildren[sectionIndex].fileOffset;
							l = bayIndex;
							bTreeHelper.GetBtAisle(aisleStoreId,tempAisle);
							///////////////////////////////////////////////////////////
							// Do not allow the user to put more than one pickpath
							// in an aisle.
							///////////////////////////////////////////////////////////
							if (strcmp(tempAisle.getPickPath().getAcadHandle(),"XXX") != 0 && strcmp(tempAisle.getPickPath().getAcadHandle(),"") != 0) {
								errorStatus = acdbCurDwg()->getAcDbObjectId(pathObjectId,Adesk::kFalse,tempAisle.getPickPath().getAcadHandle());
								if (errorStatus == Acad::eOk) {
								
									errorStatus = acdbOpenAcDbObject(pEnt, pathObjectId, AcDb::kForRead);
									pEnt->close();
									if ( errorStatus == Acad::eOk) {
										AfxMessageBox("This aisle already has a pick path.  Exiting pick path insertion. ");
										return -1;
									}
								}
							}
						}
						else {
							AfxMessageBox("Unable to locate Aisle in facility : 1\n");
							AfxMessageBox(fingerHandleArray[m]);
							return -1;
						}
					}
					catch(...) {
						AfxMessageBox("Unable to locate Aisle in facility : 2\n");
						AfxMessageBox(fingerHandleArray[m]);
						return -1;
					}
					currentApp->DoWaitCursor(-1);
					fingerHandleAisles.Add(aisleIndex);
					fingerHandleSides.Add(sideIndex);
				}
				///////////////////////////////////////////////////////////
				// For "Finger" pickpaths, the aisle chosen must be in
				// the same section
				///////////////////////////////////////////////////////////
				if ( sectionIndex != prevIndex && prevIndex != -1 ) {
					AfxMessageBox("Please select Aisles in the same section for finger pick path");
					rightBay = 0;
					return -1;
				}
				///////////////////////////////////////////////////////////
				// Make sure the bay chosen is at the beginning or ending
				// of the aisle
				///////////////////////////////////////////////////////////
				prevIndex = sectionIndex;
				if ( l != 0 && l != changesTree.treeChildren[sectionIndex].treeChildren[aisleIndex].treeChildren[sideIndex].treeChildren.GetSize() - 1) {
					ads_printf("Bay Chosen is not at the beginning/ending of the aisle\n");
					rightBay = 0;
				}
				else{
					///////////////////////////////////////////////////////////
					// Special processing for single-bay aisles
					///////////////////////////////////////////////////////////
					if (bayIndex == 0 && bayIndex == changesTree.treeChildren[sectionIndex].treeChildren[aisleIndex].treeChildren[sideIndex].treeChildren.GetSize() - 1) {
						int oneNum = 1;
						if (AfxMessageBox("Only one bay in Aisle.  Number locations Ascending?",MB_YESNO) == IDNO )
							tempBayIndexList.Add(oneNum);
						else
							tempBayIndexList.Add(bayIndex);
					}
					else
						tempBayIndexList.Add(bayIndex);
				}
			}
		}
	}

	currentApp->DoWaitCursor(1);

	////////////////////////////////////////////////////////////////
	// Get the center points of the bays and fill the appropriate
	// AcGePoint3dArray.  This is gotten by reading the result
	// buffers that are associated with autocad drawing objects.
	////////////////////////////////////////////////////////////////
	///////////////////////////////////////////////////////////
	// This is only done if the pickpath chosen is not a 
	// "Finger" pickpath.  We already have the point if it is
	///////////////////////////////////////////////////////////
	if ( selIndex != 1 ) {
		////////////////////////////////////////////////////////////////
		// Side 1
		////////////////////////////////////////////////////////////////
		if ( bayIndex == 0 ) {
			startIndex = 0; 
			endIndex = changesTree.treeChildren[sectionIndex].treeChildren[aisleIndex].treeChildren[0].treeChildren.GetSize();
			indexStep = 1;
		}
		else {
			startIndex = changesTree.treeChildren[sectionIndex].treeChildren[aisleIndex].treeChildren[0].treeChildren.GetSize() - 1;
			endIndex = -1;
			indexStep = -1;
		}
		for ( i = startIndex; i != endIndex; i += indexStep ) {

			///////////////////////////////////////////////////////////
			// For each bay in this side, get the coordinates and
			// the extended data to find the center point list of the 
			// aisle
			///////////////////////////////////////////////////////////
			numItemsProcessed++;
			
			CString handle = changesTree.treeChildren[sectionIndex].treeChildren[aisleIndex].treeChildren[0].treeChildren[i].acadHandle;
			tempDbHandle = handle.GetBuffer(0);
			handle.ReleaseBuffer();

			errorStatus = acdbCurDwg()->getAcDbObjectId(bayObjectId,Adesk::kFalse,tempDbHandle);
			if ( errorStatus != Acad::eOk) {
				CString msg;
				msg.Format("Unable to find bay object in drawing: %s.  Run Check Facility.\n", handle);
				AfxMessageBox(msg);
				currentApp->DoWaitCursor(-1);
				return -1;
			}

			errorStatus = acdbOpenAcDbObject(pEnt, bayObjectId, AcDb::kForRead);
			if ( errorStatus != Acad::eOk) {
				CString msg;
				msg.Format("Unable to open bay object in drawing: %s.  Run Check Facility.\n", handle);
				AfxMessageBox(msg);
				currentApp->DoWaitCursor(-1);
				return -1;
			}
		
			qqhSLOTBay bay;
			bTreeHelper.GetBtBay(changesTree.treeChildren[sectionIndex].treeChildren[aisleIndex].treeChildren[0].treeChildren[i].fileOffset, bay);
			if (bay.pBayProfile == NULL) {
				CBayProfile tempBayProfile;
				if (! bayProfileMap.Lookup(bay.getBayProfileId(), tempBayProfile)) {
					try {
						bayProfileDataService.GetBayProfile(bay.getBayProfileId(), tempBayProfile, 0);
					}
					catch (...) {
						controlService.Log("Error getting bay profile information for bay.", 
							"Generic exception in GetBayProfile\n");
						return -1;
					}
					bay.pBayProfile = new CBayProfile(tempBayProfile);
					bayProfileMap.SetAt(tempBayProfile.m_BayProfileDBId, tempBayProfile);
				}
				else {
					bay.pBayProfile = new CBayProfile(tempBayProfile);
				}
			}

			bayDepth = bay.pBayProfile->m_Depth;
			bayHeight = bay.pBayProfile->m_UprightHeight;
			baySpace = tempAisle.getAisleSpace();

			///////////////////////////////////////////////////////////
			// Rotation (in radians) and position of bay
			///////////////////////////////////////////////////////////
			pBlockRef = (AcDbBlockReference *)(pEnt);
			rotateAngle = pBlockRef->rotation();
			tempPoint = pBlockRef->position();


			tempPoint[X] = tempPoint[X] + bayDepth/2*sin(rotateAngle);
			tempPoint[Y] = tempPoint[Y] - bayDepth/2*cos(rotateAngle);

			centerPointSide1List.append(tempPoint);
			count = 1;
			while (pEnt->close() != Acad::eOk && count < 10 )
				count++;
			if ( count == 10 )
				someNotClosed = 1;
		}
		////////////////////////////////////////////////////////////////
		// Side 2
		////////////////////////////////////////////////////////////////

		if ( bayIndex == 0 ) {
			startIndex = 0; 
			endIndex = changesTree.treeChildren[sectionIndex].treeChildren[aisleIndex].treeChildren[1].treeChildren.GetSize();
			indexStep = 1;
		}
		else {
			startIndex = changesTree.treeChildren[sectionIndex].treeChildren[aisleIndex].treeChildren[1].treeChildren.GetSize() - 1;
			endIndex = -1;
			indexStep = -1;
		}
		for ( i = startIndex; i != endIndex; i += indexStep ) {
			
			///////////////////////////////////////////////////////////
			// For each bay in this side, get the coordinates and
			// the extended data to find the center point list of the 
			// aisle
			///////////////////////////////////////////////////////////
			numItemsProcessed++;
			
			tempDbHandle = changesTree.treeChildren[sectionIndex].treeChildren[aisleIndex].treeChildren[1].treeChildren[i].acadHandle;
			
			acdbCurDwg()->getAcDbObjectId(bayObjectId,Adesk::kFalse,tempDbHandle);
			
			errorStatus = acdbOpenAcDbObject(pEnt, bayObjectId, AcDb::kForRead);
			if ( errorStatus != Acad::eOk) {
				AfxMessageBox("Cannot Open Bay Object!");
				currentApp->DoWaitCursor(-1);
				return -1;
			}

			///////////////////////////////////////////////////////////
			// Extended Data
			///////////////////////////////////////////////////////////
		
			qqhSLOTBay bay;
			bTreeHelper.GetBtBay(changesTree.treeChildren[sectionIndex].treeChildren[aisleIndex].treeChildren[1].treeChildren[i].fileOffset, bay);
						if (bay.pBayProfile == NULL) {
				CBayProfile tempBayProfile;
				if (! bayProfileMap.Lookup(bay.getBayProfileId(), tempBayProfile)) {
					try {
						bayProfileDataService.GetBayProfile(bay.getBayProfileId(), tempBayProfile, 0);
					}
					catch (...) {
						controlService.Log("Error getting bay profile information for bay.", 
							"Generic exception in GetBayProfile\n");
						return -1;
					}
					bayProfileMap.SetAt(tempBayProfile.m_BayProfileDBId, tempBayProfile);
					bay.pBayProfile = new CBayProfile(tempBayProfile);
				}
				else {
					bay.pBayProfile = new CBayProfile(tempBayProfile);
				}
			}
			bayDepth = bay.pBayProfile->m_Depth;
			bayHeight = bay.pBayProfile->m_UprightHeight;
			baySpace = tempAisle.getAisleSpace();
			///////////////////////////////////////////////////////////
			// Rotation (in radians) and position of bay
			///////////////////////////////////////////////////////////
			pBlockRef = (AcDbBlockReference *)(pEnt);
			rotateAngle = pBlockRef->rotation();
			tempPoint = pBlockRef->position();

			tempPoint[Z] = tempPoint[Z] - bayHeight/2;
			tempPoint[X] = tempPoint[X] - bayDepth/2*sin(rotateAngle-PI);
			tempPoint[Y] = tempPoint[Y] + bayDepth/2*cos(rotateAngle-PI);

			centerPointSide2List.append(tempPoint);
			
			count = 1;
			while (pEnt->close() != Acad::eOk && count < 10 )
				count++;
			if ( count == 10 )
				someNotClosed = 1;
		}
		if ( centerPointSide1List.logicalLength() == 0 ) {
			for ( i = 0; i < centerPointSide2List.logicalLength(); i++ ) {
				tempPoint = centerPointSide2List[i];
				tempPoint[X] = tempPoint[X] - baySpace*sin(rotateAngle - PI);
				tempPoint[Y] = tempPoint[Y] + baySpace*cos(rotateAngle - PI);
				centerPointSide1List.append(tempPoint);
			}
		}
		if ( centerPointSide2List.logicalLength() == 0 ) {
			for ( i = 0; i < centerPointSide1List.logicalLength(); i++ ) {
				tempPoint = centerPointSide1List[i];
				tempPoint[X] = tempPoint[X] + baySpace*sin(rotateAngle);
				tempPoint[Y] = tempPoint[Y] - baySpace*cos(rotateAngle);
				centerPointSide2List.append(tempPoint);
			}
		}

		////////////////////////////////////////////////////////////////
		// Build the "matrix" of points within the aisle to use to map
		// out the pick path.  The points represent the center point of
		// the aisle, the right hand side 1/2 the distance from the
		// center of the aisle to the front of the bay, and left hand
		// side, 1/2 the distance from the center of the aisle to the
		// front of the bay (in the other direction).
		////////////////////////////////////////////////////////////////
		////////////////////////////////////////////////////////////////
		// deltaX and deltaY are used in case a bay ends staggered.  The
		// logical centerline of the aisle side is continued, and is
		// used as a phantom center point of the bay.
		////////////////////////////////////////////////////////////////
		if ( centerPointSide1List.logicalLength() >= 2 ) {
			deltaX = centerPointSide1List[1][X] - centerPointSide1List[0][X];
			deltaY = centerPointSide1List[1][Y] - centerPointSide1List[0][Y];
		}
		else if( centerPointSide2List.logicalLength() >= 2 )  {
			deltaX = centerPointSide2List[1][X] - centerPointSide2List[0][X];
			deltaY = centerPointSide2List[1][Y] - centerPointSide2List[0][Y];
		}
		else {
			deltaX = 0.0;
			deltaY = 0.0;
		}

		for ( i = 0; i < centerPointSide1List.logicalLength() || i < centerPointSide2List.logicalLength(); i++) {
			if ( i < centerPointSide1List.logicalLength() )
				tempPointCenter1 = centerPointSide1List[i];
			else
				tempPointCenter1 = centerPointSide1List[centerPointSide1List.logicalLength()-1];
//				tempPointCenter1.set((tempPointCenter1[X]+deltaX),
//									(tempPointCenter1[Y]+deltaY),
//									tempPointCenter1[Z]);
			if ( i < centerPointSide2List.logicalLength() )
				tempPointCenter2 = centerPointSide2List[i];
			else
				tempPointCenter2 = centerPointSide2List[centerPointSide2List.logicalLength()-1];
//				tempPointCenter2.set((tempPointCenter2[X]+deltaX),
//									(tempPointCenter2[Y]+deltaY),
//									tempPointCenter1[Z]);
			
			if ( tempPointCenter1[X] != tempPointCenter2[X] ) {
				if ( tempPointCenter1[X] > tempPointCenter2[X] ) {
					diffX = tempPointCenter1[X] - tempPointCenter2[X];
					tempPointcAisle[X] = tempPointCenter2[X] + (diffX/2);
					tempPointrsAisle[X] = tempPointCenter2[X] + (diffX/4);
					tempPointlsAisle[X] = tempPointCenter2[X] + (diffX * 3/4);
				}
				else {
					diffX = tempPointCenter2[X] - tempPointCenter1[X];
					tempPointcAisle[X] = tempPointCenter1[X] + (diffX/2);
					tempPointrsAisle[X] = tempPointCenter1[X] + (diffX * 3/4);
					tempPointlsAisle[X] = tempPointCenter1[X] + (diffX/4);
				}
			}
			else {
				tempPointcAisle[X] = tempPointCenter1[X];
				tempPointrsAisle[X] = tempPointCenter1[X];
				tempPointlsAisle[X] = tempPointCenter1[X];
			}

			if ( tempPointCenter1[Y] != tempPointCenter2[Y] ) {
				if ( tempPointCenter1[Y] > tempPointCenter2[Y] ) {
					diffY = tempPointCenter1[Y] - tempPointCenter2[Y];
					tempPointcAisle[Y] = tempPointCenter1[Y] - (diffY/2);
					tempPointrsAisle[Y] = tempPointCenter1[Y]- (diffY *3/4);
					tempPointlsAisle[Y] = tempPointCenter1[Y]- (diffY/4);
				}
				else {
					diffY = tempPointCenter2[Y] - tempPointCenter1[Y];
					tempPointcAisle[Y] = tempPointCenter2[Y] - (diffY/2);
					tempPointrsAisle[Y] = tempPointCenter2[Y]- (diffY/4);
					tempPointlsAisle[Y] = tempPointCenter2[Y] - (diffY * 3/4);
				}
			}
			else {
				tempPointcAisle[Y] = tempPointCenter1[Y];
				tempPointrsAisle[Y] = tempPointCenter1[Y];
				tempPointlsAisle[Y] = tempPointCenter1[Y];
			}
			tempPointcAisle[Z] = tempPoint[Z];
			tempPointrsAisle[Z] = tempPoint[Z];
			tempPointlsAisle[Z] = tempPoint[Z];
			if ( i == 0 ) {
				tempPointCenter1[X] = tempPointcAisle[X] - deltaX;
				tempPointCenter1[Y] = tempPointcAisle[Y] - deltaY;
				tempPointCenter1[Z] = tempPoint[Z];
				cAislePointList.append(tempPointCenter1);
				tempPointCenter1[X] = tempPointrsAisle[X] - deltaX;
				tempPointCenter1[Y] = tempPointrsAisle[Y] - deltaY;
				tempPointCenter1[Z] = tempPoint[Z];
				rsAislePointList.append(tempPointCenter1);
				tempPointCenter1[X] = tempPointlsAisle[X] - deltaX;
				tempPointCenter1[Y] = tempPointlsAisle[Y] - deltaY;
				tempPointCenter1[Z] = tempPoint[Z];
				lsAislePointList.append(tempPointCenter1);
			}
			lsAislePointList.append(tempPointlsAisle);
			rsAislePointList.append(tempPointrsAisle);
			cAislePointList.append(tempPointcAisle);
			if ( i == centerPointSide1List.logicalLength() - 1 ) {
				tempPointCenter1[X] = tempPointcAisle[X] + deltaX;
				tempPointCenter1[Y] = tempPointcAisle[Y] + deltaY;
				tempPointCenter1[Z] = tempPoint[Z];
				cAislePointList.append(tempPointCenter1);
				tempPointCenter1[X] = tempPointrsAisle[X] + deltaX;
				tempPointCenter1[Y] = tempPointrsAisle[Y] + deltaY;
				tempPointCenter1[Z] = tempPoint[Z];
				rsAislePointList.append(tempPointCenter1);
				tempPointCenter1[X] = tempPointlsAisle[X] + deltaX;
				tempPointCenter1[Y] = tempPointlsAisle[Y] + deltaY;
				tempPointCenter1[Z] = tempPoint[Z];
				lsAislePointList.append(tempPointCenter1);
			}
		}
	}
	
	ads_printf("Numbering bays, levels, and locations.");
	selIndex = PickPathProperties.m_PickPathProperties_PathType_Val;
	bayPattern = PickPathProperties.m_PickPathProperties_PatternNum;

	int LlevelBreak;
	int RlevelBreak;

	if (PickPathProperties.m_LLevelBreak == TRUE )
		LlevelBreak = 1;
	else
		LlevelBreak = 0;
	if (PickPathProperties.m_RLevelBreak == TRUE )
		RlevelBreak = 1;
	else
		RlevelBreak = 0;


	////////////////////////////////////////////////////////////////
	// Number the bays, levels and locations according to the
	// properties of the pickpath entered
	////////////////////////////////////////////////////////////////
	numberingService.NumberBays(tempBayIndexList,
				selIndex, 
				aisleIndex, 
				fingerHandleAisles,
				sideIndex, 
				PickPathProperties.m_LBayStartVal, PickPathProperties.m_RBayStart,
				PickPathProperties.m_LLevelStart, PickPathProperties.m_RLevelStart,
				PickPathProperties.m_LLocStart, PickPathProperties.m_RLocStart,
				PickPathProperties.m_LBaySchemeVal, PickPathProperties.m_RBaySchemeVal,
				PickPathProperties.m_LLevelSchemeVal, PickPathProperties.m_RLevelSchemeVal,
				PickPathProperties.m_LLocSchemeVal, PickPathProperties.m_RLocSchemeVal,
				PickPathProperties.m_LBayStepVal, PickPathProperties.m_RBayStep,
				PickPathProperties.m_LLevelStep, PickPathProperties.m_RLevelStep,
				PickPathProperties.m_LLocStep, PickPathProperties.m_RLocStep,
				LlevelBreak, RlevelBreak,
				PickPathProperties.m_LLocBreakVal, PickPathProperties.m_RLocBreakVal,
				bayPattern, 
				cAislePointList.logicalLength()-2,
				changesTree, 
				sectionIndex,
				fingerHandleSides,
				PickPathProperties.m_LBayPatternVal, PickPathProperties.m_LLevelPatternVal, PickPathProperties.m_LLocPatternVal,
				PickPathProperties.m_RBayPatternVal, PickPathProperties.m_RLevelPatternVal, PickPathProperties.m_RLocPatternVal);

	////////////////////////////////////////////////////////////////
	//  Add the start point
	////////////////////////////////////////////////////////////////
	if ( selIndex != 1 )
		pickPathPointList.append(cAislePointList[0]);
	else {
		tempPoint[X] = startPoint[X];
		tempPoint[Y] = startPoint[Y];
		tempPoint[Z] = startPoint[Z];
		pickPathPointList.append(tempPoint);
	}
	switch ( selIndex ) {
		case 0 : 
		{
			////////////////////////////////////////////////////////////////
			//  Cross-Aisle Pick path
			////////////////////////////////////////////////////////////////
			if ( (sideIndex == 1  && bayIndex == 0) || (sideIndex == 1 && bayIndex > 0) ) {
				for ( i = 1; i < cAislePointList.logicalLength()-1; i++ ) {
						pickPathPointList.append(rsAislePointList[i]);
						pickPathPointList.append(lsAislePointList[i]);
				}
			}
			else {
				for ( i = 1; i < cAislePointList.logicalLength()-1; i++ ) {
						pickPathPointList.append(lsAislePointList[i]);
						pickPathPointList.append(rsAislePointList[i]);
				}
			}
			break;
		}
		case 2 :
		{
			////////////////////////////////////////////////////////////////
			//  Straight-Thru
			////////////////////////////////////////////////////////////////
			if ( cAislePointList.logicalLength() > 2)
				pickPathPointList.append(cAislePointList[1]);
			break;
		}
		case 3 : 
		{
			////////////////////////////////////////////////////////////////
			//  Two-way path 
			////////////////////////////////////////////////////////////////
			if ( (sideIndex == 1  && bayIndex == 0) || (sideIndex == 1 && bayIndex > 0) ) {
				for ( i = 1; i < cAislePointList.logicalLength() - 1; i++ )
					pickPathPointList.append(rsAislePointList[i]);				for ( i = cAislePointList.logicalLength() - 2; i >= 1; i-- )
					pickPathPointList.append(lsAislePointList[i]);
				pickPathPointList.append(lsAislePointList[0]);
			}
			else {
				for ( i = 1; i < cAislePointList.logicalLength() - 1; i++ )
					pickPathPointList.append(lsAislePointList[i]);
				for ( i = cAislePointList.logicalLength() - 2; i >= 1; i-- )
					pickPathPointList.append(rsAislePointList[i]);
				pickPathPointList.append(rsAislePointList[0]);
			}
			break;
		}
		case 4 : 
		{
			////////////////////////////////////////////////////////////////
			//  U-Pick path 
			////////////////////////////////////////////////////////////////
			if ( (sideIndex == 1  && bayIndex == 0) || (sideIndex == 1 && bayIndex > 0) ){
				for ( i = bayPattern; i < cAislePointList.logicalLength()-1; i +=bayPattern) {
					for ( j = 0; j < bayPattern; j++ )
						pickPathPointList.append(rsAislePointList[i-j]);
					for ( j = i - j + 1; j <= i; j++ )
						pickPathPointList.append(lsAislePointList[j]);
					if ( (i+bayPattern) >= cAislePointList.logicalLength()-2 && j <= cAislePointList.logicalLength() - 2)
						bayPattern = (cAislePointList.logicalLength()-2) - i;
				}
			}
			else {
				for ( i = bayPattern; i < cAislePointList.logicalLength()-1; i +=bayPattern) {
					for ( j = 0; j < bayPattern; j++ )
						pickPathPointList.append(lsAislePointList[i-j]);
					for ( j = i - j + 1; j <= i; j++ )
						pickPathPointList.append(rsAislePointList[j]);
					if ( (i+bayPattern) >= cAislePointList.logicalLength()-2 && j <= cAislePointList.logicalLength()-2)
						bayPattern = (cAislePointList.logicalLength()-2) - i;
				}
			}
			break;
		}
		case 5 : 
		{
			////////////////////////////////////////////////////////////////
			//  Z-Pick path
			////////////////////////////////////////////////////////////////
			swapMe = 0;
			if ( (sideIndex == 1  && bayIndex == 0) || (sideIndex == 1 && bayIndex > 0) ) {
				for ( i = 1; i < cAislePointList.logicalLength()-1; i+=bayPattern ) {
					if (swapMe == 0) {
						for ( j = 0; j < bayPattern && (i+j) < cAislePointList.logicalLength()-1; j++)
							pickPathPointList.append(rsAislePointList[i+j]);
						for ( j = 0; j < bayPattern && (i+j) < cAislePointList.logicalLength()-1; j++)
							pickPathPointList.append(lsAislePointList[i+j]);
						swapMe = 1;
						if ( (i+bayPattern) >= cAislePointList.logicalLength()-2 && (i+j) <= cAislePointList.logicalLength()-2)
							bayPattern = (cAislePointList.logicalLength()-2) - i;
						if ( bayPattern == 0 )
							bayPattern = cAislePointList.logicalLength();
					}
					else {
						for ( j = 0; j < bayPattern && (i+j) < cAislePointList.logicalLength()-1; j++)
							pickPathPointList.append(lsAislePointList[i+j]);
						for ( j = 0; j < bayPattern && (i+j) < cAislePointList.logicalLength()-1; j++)
							pickPathPointList.append(rsAislePointList[i+j]);
						swapMe = 0;
						if ( (i+bayPattern) >= cAislePointList.logicalLength()-2 && (i+j) <= cAislePointList.logicalLength()-2)
							bayPattern = (cAislePointList.logicalLength()-2) - i;
						if ( bayPattern == 0 )
							bayPattern = cAislePointList.logicalLength();
					}
				}
			}
			else {
				for ( i = 1; i < cAislePointList.logicalLength()-1; i+=bayPattern ) {
					if (swapMe == 0) {
						for ( j = 0; j < bayPattern && (i+j) < cAislePointList.logicalLength()-1; j++)
							pickPathPointList.append(lsAislePointList[i+j]);
						for ( j = 0; j < bayPattern && (i+j) < cAislePointList.logicalLength()-1; j++)
							pickPathPointList.append(rsAislePointList[i+j]);
						swapMe = 1;
						if ( (i+bayPattern) >= cAislePointList.logicalLength()-2 && (i+j) <= cAislePointList.logicalLength()-2)
							bayPattern = (cAislePointList.logicalLength()-2) - i;
						if ( bayPattern == 0 )
							bayPattern = cAislePointList.logicalLength();
					}
					else {
						for ( j = 0; j < bayPattern && (i+j) < cAislePointList.logicalLength()-1; j++)
							pickPathPointList.append(rsAislePointList[i+j]);
						for ( j = 0; j < bayPattern && (i+j) < cAislePointList.logicalLength()-1; j++)
							pickPathPointList.append(lsAislePointList[i+j]);
						swapMe = 0;
						if ( (i+bayPattern) >= cAislePointList.logicalLength()-2 && (i+j) <= cAislePointList.logicalLength()-2)
							bayPattern = (cAislePointList.logicalLength()-2) - i;
						if ( bayPattern == 0 )
							bayPattern = cAislePointList.logicalLength();
					}
				}
			}
			break;
		}
		case 1 : break;
		default : break;
	}
	////////////////////////////////////////////////////////////////
	//  Add the end point
	////////////////////////////////////////////////////////////////
	if ( selIndex != 3 && selIndex != 1)
		pickPathPointList.append(cAislePointList[cAislePointList.logicalLength()-1]);
	else if ( selIndex == 1 ) {
		tempPoint[X] = endPoint[X];
		tempPoint[Y] = endPoint[Y];
		tempPoint[Z] = endPoint[Z];
		pickPathPointList.append(tempPoint);
	}

	////////////////////////////////////////////////////////////////
	// Add the "arrow"
	////////////////////////////////////////////////////////////////
	if ( pickPathPointList.logicalLength() > 2) {
		//tempPoint = pickPathPointList[1];
		pickPathPointList.insertAt(1,cAislePointList[1]);
		pickPathPointList.insertAt(2,rsAislePointList[0]);
		pickPathPointList.insertAt(3,lsAislePointList[0]);
		pickPathPointList.insertAt(4,cAislePointList[1]);
	}
	////////////////////////////////////////////////////////////////
	//  Calculate the pathLength
	////////////////////////////////////////////////////////////////
	tempPointStart = pickPathPointList[0];
	tempPointEnd = pickPathPointList[pickPathPointList.logicalLength()-1];

	pathLength = 0;
	
	for ( i = 4; i < pickPathPointList.logicalLength(); i++) {
		pathLength += sqrt(pow(pickPathPointList[i][Y]-pickPathPointList[i-1][Y],2) + pow(pickPathPointList[i][X]-pickPathPointList[i-1][X],2));
	}
	
	/*
	if (tempPointStart[X] == tempPointEnd[X]) {
		pathLength = tempPointEnd[Y] - tempPointStart[Y];
		if (pathLength < 0)
			pathLength *= -1;
	}
	else if (tempPointStart[Y] == tempPointEnd[Y]) {
		pathLength = tempPointEnd[X] - tempPointStart[X];
		if (pathLength < 0)
			pathLength *= -1;
	}
	else {
		tempPoint[X] = tempPointEnd[X];
		tempPoint[Y] = tempPointStart[Y];
		pathLength = 
	}
	*/

	////////////////////////////////////////////////////////////////
	//  For continuing - find the point to connect with
	////////////////////////////////////////////////////////////////
	if ( firstTime != 1 ) {
		tempPoint[X] = lastPoint[X];
		tempPoint[Y] = lastPoint[Y];
//		tempPoint[X] = tempPointStart[X];
//		tempPoint[Y] = tempPointStart[Y];
		pathLength += sqrt(pow(tempPoint[Y]-lastPoint[Y],2) + pow(tempPoint[X]-lastPoint[X],2));
		pickPathPointList.insertAt(0,tempPoint);
	}

	////////////////////////////////////////////////////////////////
	//  Add the 3dPolyLine AutoCad object for this pickpath
	////////////////////////////////////////////////////////////////
	pickPathLine = new AcDb3dPolyline(AcDb::k3dSimplePoly, pickPathPointList, Adesk::kTrue);
	pickPathLine->setColorIndex(100);
	pickPathLine->makeOpen();
	acdbCurDwg()->getBlockTable(blockTable,AcDb::kForRead);

	////////////////////////////////////////////////////////////////
	// Get a pointer to the MODEL_SPACE BlockTableRecord
	////////////////////////////////////////////////////////////////
	errorStatus = blockTable->getAt(ACDB_MODEL_SPACE, blockTableRecord,AcDb::kForWrite);
	if ( errorStatus != Acad::eOk ) {
		blockTable->close();
		ads_printf("Error! Cannot open Block Table\n");
	}
	////////////////////////////////////////////////////////////////
	// Append the polyline object to the database and
	// obtain its ObjId
	////////////////////////////////////////////////////////////////
	errorStatus = blockTableRecord->appendAcDbEntity(lineObjId,pickPathLine);
	if ( errorStatus != Acad::eOk ) {
		blockTableRecord->close();
		ads_printf("Error!  Cannot append Pick Path\n");
	}

	count = 1;
	while (blockTable->close() != Acad::eOk && count < 10 )
		count++;
	if ( count == 10 )
		someNotClosed = 1;
	count = 1;
	while (blockTableRecord->close() != Acad::eOk && count < 10 )
		count++;
	if ( count == 10 )
		someNotClosed = 1;

	pickPathLine->setLayer("0");
	count = 1;
	while (pickPathLine->close() != Acad::eOk && count < 10 )
		count++;
	if ( count == 10 )
		someNotClosed = 1;

	errorStatus = acdbOpenAcDbObject(pEnt, lineObjId, AcDb::kForRead);
	if ( errorStatus != Acad::eOk ) {
		AfxMessageBox("Cannot open new line entity");
		currentApp->DoWaitCursor(-1);
		return -1;
	}
	////////////////////////////////////////////////////////////////
	//  Get the unique AutoCad handle to put in the Aisle
	////////////////////////////////////////////////////////////////
	pEnt->getAcDbHandle(newObjHandle);
	memset(newHandle,0,20);
	newObjHandle.getIntoAsciiBuffer(newHandle);
	count = 1;
	while (pEnt->close() != Acad::eOk && count < 10 )
		count++;
	if ( count == 10 )
		someNotClosed = 1;

	lastPoint[X] = pickPathPointList[pickPathPointList.logicalLength()-1][X];
	lastPoint[Y] = pickPathPointList[pickPathPointList.logicalLength()-1][Y];
	lastPoint[Z] = pickPathPointList[pickPathPointList.logicalLength()-1][Z];

	pathObjIdptr = NULL;

	tempAisle.getUDFList().RemoveAll();
	tempAisle.getChildList().RemoveAll();
	if ( selIndex != 1 ) {
		//////////////////////////////////////////////////////////////////////
		// fill in aisle with pickpath properties for this aisle
		//////////////////////////////////////////////////////////////////////
		bTreeHelper.GetBtAisle(changesTree.treeChildren[sectionIndex].treeChildren[aisleIndex].fileOffset,tempAisle);
		strcpy(tempAisle.getPickPath().getAcadHandle(),newHandle);
		strcpy(tempAisle.getPickPath().getConAcadHandle(),oldPickPathHandle);
		strcpy(oldPickPathHandle,newHandle);
		strcpy(changesTree.treeChildren[sectionIndex].treeChildren[aisleIndex].acadHandle,newHandle);
		if ( bayIndex == 0 )
			tempAisle.setPickPathDirection(0);
		else
			tempAisle.setPickPathDirection(1);
		tempAisle.getPickPath().setPathLength((float)pathLength);
		tempAisle.setPickPathType(PickPathProperties.m_PickPathProperties_PathType_Val);
		tempAisle.setPickPathStartSide(sideIndex);
		tempAisle.setBaysInPattern(PickPathProperties.m_PickPathProperties_PatternNum);
		tempAisle.setLeftBayStart(PickPathProperties.m_LBayStartVal);
		tempAisle.setRightBayStart(PickPathProperties.m_RBayStart);
		tempAisle.setLeftLevelStart(PickPathProperties.m_LLevelStart); 
		tempAisle.setRightLevelStart(PickPathProperties.m_RLevelStart);
		tempAisle.setLeftLocationStart(PickPathProperties.m_LLocStart); 
		tempAisle.setRightLocationStart(PickPathProperties.m_RLocStart);
		tempAisle.setLeftBayScheme(PickPathProperties.m_LBaySchemeVal); 
		tempAisle.setRightBayScheme(PickPathProperties.m_RBaySchemeVal);
		tempAisle.setLeftLevelScheme(PickPathProperties.m_LLevelSchemeVal); 
		tempAisle.setRightLevelScheme(PickPathProperties.m_RLevelSchemeVal);
		tempAisle.setLeftLocationScheme(PickPathProperties.m_LLocSchemeVal); 
		tempAisle.setRightLocationScheme(PickPathProperties.m_RLocSchemeVal);
		tempAisle.setLeftBayStep(PickPathProperties.m_LBayStepVal); 
		tempAisle.setRightBayStep(PickPathProperties.m_RBayStep);
		tempAisle.setLeftLevelStep(PickPathProperties.m_LLevelStep); 
		tempAisle.setRightLevelStep(PickPathProperties.m_RLevelStep);
		tempAisle.setLeftLocationStep(PickPathProperties.m_LLocStep); 
		tempAisle.setRightLocationStep(PickPathProperties.m_RLocStep);
		tempAisle.setLeftLevelBreak(PickPathProperties.m_LLevelBreak);
		tempAisle.setRightLevelBreak(PickPathProperties.m_RLevelBreak);
		tempAisle.setLeftLocationBreak(PickPathProperties.m_LLocBreakVal);
		tempAisle.setRightLocationBreak(PickPathProperties.m_RLocBreakVal);
		tempAisle.setLeftBayPattern(PickPathProperties.m_LBayPatternVal);
		tempAisle.setLeftLevelPattern(PickPathProperties.m_LLevelPatternVal);
		tempAisle.setLeftLocPattern(PickPathProperties.m_LLocPatternVal);
		tempAisle.setRightBayPattern(PickPathProperties.m_RBayPatternVal);
		tempAisle.setRightLevelPattern(PickPathProperties.m_RLevelPatternVal);
		tempAisle.setRightLocPattern(PickPathProperties.m_RLocPatternVal);
		bTreeHelper.SetBtAisle(changesTree.treeChildren[sectionIndex].treeChildren[aisleIndex].fileOffset,tempAisle);
		bTreeHelper.GetBtSection(changesTree.treeChildren[sectionIndex].fileOffset,tempSection);
		tempSection.setSelDist(tempSection.getSelDist() + (float)pathLength);
		bTreeHelper.SetBtSection(changesTree.treeChildren[sectionIndex].fileOffset,tempSection);
	}
	else {
		for (i = 0; i < fingerHandleAisles.GetSize(); i++) {
			//////////////////////////////////////////////////////////////////////
			// fill in aisle with pickpath properties for this aisle
			//////////////////////////////////////////////////////////////////////
			bTreeHelper.GetBtAisle(changesTree.treeChildren[sectionIndex].treeChildren[fingerHandleAisles[i]].fileOffset,tempAisle);
			strcpy(tempAisle.getPickPath().getConAcadHandle(),oldPickPathHandle);
			strcpy(oldPickPathHandle,newHandle);
			strcpy(tempAisle.getPickPath().getAcadHandle(),newHandle);
			strcpy(changesTree.treeChildren[sectionIndex].treeChildren[fingerHandleAisles[i]].acadHandle,newHandle);
			if ( tempBayIndexList[i] == 0 )
				tempAisle.setPickPathDirection(0);
			else
				tempAisle.setPickPathDirection(1);
			tempAisle.setPickPathType(PickPathProperties.m_PickPathProperties_PathType_Val);
			tempAisle.setPickPathStartSide(fingerHandleSides[i]);
			tempAisle.setBaysInPattern(PickPathProperties.m_PickPathProperties_PatternNum);
			tempAisle.getPickPath().setPathLength((float)pathLength);
			tempAisle.setLeftBayStart(PickPathProperties.m_LBayStartVal);
			tempAisle.setRightBayStart(PickPathProperties.m_RBayStart);
			tempAisle.setLeftLevelStart(PickPathProperties.m_LLevelStart); 
			tempAisle.setRightLevelStart(PickPathProperties.m_RLevelStart);
			tempAisle.setLeftLocationStart(PickPathProperties.m_LLocStart); 
			tempAisle.setRightLocationStart(PickPathProperties.m_RLocStart);
			tempAisle.setLeftBayScheme(PickPathProperties.m_LBaySchemeVal); 
			tempAisle.setRightBayScheme(PickPathProperties.m_RBaySchemeVal);
			tempAisle.setLeftLevelScheme(PickPathProperties.m_LLevelSchemeVal); 
			tempAisle.setRightLevelScheme(PickPathProperties.m_RLevelSchemeVal);
			tempAisle.setLeftLocationScheme(PickPathProperties.m_LLocSchemeVal); 
			tempAisle.setRightLocationScheme(PickPathProperties.m_RLocSchemeVal);
			tempAisle.setLeftBayStep(PickPathProperties.m_LBayStepVal); 
			tempAisle.setRightBayStep(PickPathProperties.m_RBayStep);
			tempAisle.setLeftLevelStep(PickPathProperties.m_LLevelStep); 
			tempAisle.setRightLevelStep(PickPathProperties.m_RLevelStep);
			tempAisle.setLeftLocationStep(PickPathProperties.m_LLocStep); 
			tempAisle.setRightLocationStep(PickPathProperties.m_RLocStep);
			tempAisle.setLeftLevelBreak(PickPathProperties.m_LLevelBreak);
			tempAisle.setRightLevelBreak(PickPathProperties.m_RLevelBreak);
			tempAisle.setLeftLocationBreak(PickPathProperties.m_LLocBreakVal);
			tempAisle.setRightLocationBreak(PickPathProperties.m_RLocBreakVal);
			tempAisle.setLeftBayPattern(PickPathProperties.m_LBayPatternVal);
			tempAisle.setLeftLevelPattern(PickPathProperties.m_LLevelPatternVal);
			tempAisle.setLeftLocPattern(PickPathProperties.m_LLocPatternVal);
			tempAisle.setRightBayPattern(PickPathProperties.m_RBayPatternVal);
			tempAisle.setRightLevelPattern(PickPathProperties.m_RLevelPatternVal);
			tempAisle.setRightLocPattern(PickPathProperties.m_RLocPatternVal);
			bTreeHelper.SetBtAisle(changesTree.treeChildren[sectionIndex].treeChildren[fingerHandleAisles[i]].fileOffset,tempAisle);
		}
		bTreeHelper.GetBtSection(changesTree.treeChildren[sectionIndex].fileOffset,tempSection);
		tempSection.setSelDist(tempSection.getSelDist() + (float)pathLength);
		bTreeHelper.SetBtSection(changesTree.treeChildren[sectionIndex].fileOffset,tempSection);
	}
	currentApp->DoWaitCursor(-1);
	if (someNotClosed == 1)
		AfxMessageBox("Some Autocad items did not close properly.  It is recommended that you save, exit, and reopen the application");
//	if (retrievedFromDB)
		//AfxMessageBox("To improve response of pickpath creation, \nit is recommended you insert the pickpath before\nsaving an aisle.");
	ads_printf("\nCommand : ");
	return 0;
}



void CElementMaintenanceHelper::ReAddPickPath(void)
{
	BOOL skippedBay = FALSE;
	AcDbHandle objHandle;
//	resbuf *rb;
	int res,i,j;
	ads_name Set, E_name;
	long nLength;
	AcDbObjectId objId;
	AcDbEntity * pEnt;
	char objHandleStr[20];
//	int  bayFileOffset;
	Acad::ErrorStatus eStatus;
	strcpy(objHandleStr,"");
	CString strData;
	int doneSubtract = 0;
	qqhSLOTSection tempSection;
	qqhSLOTAisle tempAisle, tempAisle2;
	int count, someNotClosed = 0;
	int foundAisle, conOffset = 0;
	CWinApp * currentApp;
	currentApp = AfxGetApp();

	if ( AfxMessageBox("Are you sure you want to re-add the pickpath?",MB_YESNO) == IDNO )
		return;

	//////////////////////////////////////////////////////////////////////
	// find the object
	//////////////////////////////////////////////////////////////////////
	res = ads_ssget("P", NULL, NULL, NULL, Set);
	if (res != RTNORM)
	{
		AfxMessageBox("No Pickpath Selected!");
		return;
	}
	else
	{
		currentApp->DoWaitCursor(1);
		ads_sslength(Set, &nLength);
		for (int n=0; n < nLength; n++)
		{
			res = ads_ssname(Set, n, E_name);
			if (res != RTNORM)
			{
				ads_ssfree(Set);
				return;
			}
			eStatus = acdbGetObjectId(objId, E_name);
			if (eStatus != Acad::eOk)
			{
				AfxMessageBox("Warning : The autocad object could not be found.\nPlease exit, reopen the facility and try again.");
				return;
			}
			eStatus = acdbOpenAcDbEntity(pEnt, objId, AcDb::kForWrite);
			if (eStatus != Acad::eOk)
			{
				AfxMessageBox("Warning : The autocad object could not be found.\nPlease exit, reopen the facility and try again.");
				return;
			}


			pEnt->getAcDbHandle(objHandle);

			objHandle.getIntoAsciiBuffer(objHandleStr);
			
			//////////////////////////////////////////////////////////////////////
			// update the facility structure - aisle pathlength must change
			//////////////////////////////////////////////////////////////////////
			// look for the selected aisle
			foundAisle = 0;
			for ( i = 0; i < changesTree.treeChildren.GetSize(); i++ ) {
				for ( j = 0; j < changesTree.treeChildren[i].treeChildren.GetSize() && foundAisle == 0; j++ ) {
					if ( strcmp(changesTree.treeChildren[i].treeChildren[j].acadHandle,objHandleStr) == 0 )
						foundAisle = 1;
				}
			}
			if (foundAisle == 0) {
				try {
					if ( bTreeHelper.UpdateBTWithAisleByPickPathAcadHandle(CString(objHandleStr),changesTree) < 0 ) {
						AfxMessageBox("Error Finding Aisle in facility");
						return;
					}
				}
				catch(...) {
					AfxMessageBox("Error Finding Aisle in facility");
					return;
				}
			}
			foundAisle = 0;
			// Update any connected aisles so they no longer connect to this one
			for ( i = 0; i < changesTree.treeChildren.GetSize(); i++ ) {
				for ( j = 0; j < changesTree.treeChildren[i].treeChildren.GetSize() && foundAisle == 0; j++ ) {
					bTreeHelper.GetBtAisle(changesTree.treeChildren[i].treeChildren[j].fileOffset,tempAisle);
					//AfxMessageBox(tempAisle.getPickPath().getConAcadHandle());
					if ( strcmp(tempAisle.getPickPath().getConAcadHandle(), objHandleStr) == 0 ) {
						foundAisle = 1;
						//AfxMessageBox("Found Aisle");
						memset(tempAisle.getPickPath().getConAcadHandle(),0,20);
						tempAisle.getPickPath().setConAcadHandle("XXX");
						//AfxMessageBox("Setting Aisle");
						//AfxMessageBox(tempAisle.getPickPath().getConAcadHandle());
						bTreeHelper.SetBtAisle(changesTree.treeChildren[i].treeChildren[j].fileOffset,tempAisle);
					}
				}
			}
			if (foundAisle == 0) {
				try {
					bTreeHelper.UpdateBTWithAisleByConPickPathAcadHandle(CString(objHandleStr),changesTree);
				}
				catch(...) {
					AfxMessageBox("Error Finding Aisle in facility");
					return;
				}
				for ( i = 0; i < changesTree.treeChildren.GetSize(); i++ ) {
					for ( j = 0; j < changesTree.treeChildren[i].treeChildren.GetSize() && foundAisle == 0; j++ ) {
						bTreeHelper.GetBtAisle(changesTree.treeChildren[i].treeChildren[j].fileOffset,tempAisle);
						if ( strcmp(tempAisle.getPickPath().getConAcadHandle(), objHandleStr) == 0 ) {
							memset(tempAisle.getPickPath().getConAcadHandle(),0,20);
							tempAisle.getPickPath().setConAcadHandle("XXX");
							bTreeHelper.SetBtAisle(changesTree.treeChildren[i].treeChildren[j].fileOffset,tempAisle);
						}
					}
				}
			}


			// Now look for the selected aisle
			for ( i = 0; i < changesTree.treeChildren.GetSize(); i++ ) {
				for ( j = 0; j < changesTree.treeChildren[i].treeChildren.GetSize(); j++ ) {
					if ( strcmp(changesTree.treeChildren[i].treeChildren[j].acadHandle,objHandleStr) == 0 ) {
						bTreeHelper.GetBtAisle(changesTree.treeChildren[i].treeChildren[j].fileOffset,tempAisle);
						memset(tempAisle.getPickPath().getAcadHandle(),0,20);
						strcpy(tempAisle.getPickPath().getAcadHandle(),"XXX");
						memset(tempAisle.getPickPath().getConAcadHandle(),0,20);
						strcpy(tempAisle.getPickPath().getConAcadHandle(),"XXX");
						if ( doneSubtract == 0 ) {
							bTreeHelper.GetBtSection(changesTree.treeChildren[i].fileOffset,tempSection);
							tempSection.setSelDist(tempSection.getSelDist() - tempAisle.getPickPath().getPathLength());
							bTreeHelper.SetBtSection(changesTree.treeChildren[i].fileOffset,tempSection);
						}
						doneSubtract = 1;
						tempAisle.getPickPath().setPathLength(0);
						bTreeHelper.SetBtAisle(changesTree.treeChildren[i].treeChildren[j].fileOffset,tempAisle);
					}
				}
			}

			//////////////////////////////////////////////////////////////////////
			// remove the object
			//////////////////////////////////////////////////////////////////////
			if (strcmp(objHandleStr, "") == 0)
			{
				AfxMessageBox("Problems identifying object.");
				return;
			}
			pEnt->erase();
			count = 1;
			while (pEnt->close() != Acad::eOk && count < 10 )
				count++;
			if ( count == 10 )
				someNotClosed = 1;
			//pEnt->close();


			// now the pickpath is deleted and we have the aisle in the btree
			// run a normal pickpath add, but  fill in the dialog with the values
			// from the aisle
			AcDbObjectId aislePathId;
			AcGePoint3d lastPoint;
			int tempRet = ReAddPickPathinAisle(&aislePathId, 1, lastPoint, tempAisle);

		}
		ads_ssfree(Set);
	}
	currentApp->DoWaitCursor(-1);
	//ads_command(RTSTR,"_REGEN", RTNONE);
	if (someNotClosed == 1)
		AfxMessageBox("Some Autocad items did not close properly.  It is recommended that you save, exit, and reopen the application");
	if ( skippedBay == TRUE )
		AfxMessageBox("Bay Items were Not Deleted");


	return;
}

void CElementMaintenanceHelper::LoadPreviousPickPathValues(CPickPathPropertiesDialog &dialog)
{
	CStringArray pickPathKeys, pickPathValues;
	CControlService controlService;

	pickPathKeys.Add("PathType");
	pickPathKeys.Add("BaysInPattern");
	pickPathKeys.Add("LeftBayStart");
	pickPathKeys.Add("LeftBayStep");
	pickPathKeys.Add("LeftBayScheme");
	pickPathKeys.Add("LeftBayPattern");
	pickPathKeys.Add("LeftLevelStart");
	pickPathKeys.Add("LeftLevelStep");
	pickPathKeys.Add("LeftLevelScheme");
	pickPathKeys.Add("LeftLevelPattern");
	pickPathKeys.Add("LeftLevelBreak");
	pickPathKeys.Add("LeftLocStart");
	pickPathKeys.Add("LeftLocStep");
	pickPathKeys.Add("LeftLocScheme");
	pickPathKeys.Add("LeftLocPattern");
	pickPathKeys.Add("LeftLocBreak");
	pickPathKeys.Add("RightBayStart");
	pickPathKeys.Add("RightBayStep");
	pickPathKeys.Add("RightBayScheme");
	pickPathKeys.Add("RightBayPattern");
	pickPathKeys.Add("RightLevelStart");
	pickPathKeys.Add("RightLevelStep");
	pickPathKeys.Add("RightLevelScheme");
	pickPathKeys.Add("RightLevelPattern");
	pickPathKeys.Add("RightLevelBreak");
	pickPathKeys.Add("RightLocStart");
	pickPathKeys.Add("RightLocStep");
	pickPathKeys.Add("RightLocScheme");
	pickPathKeys.Add("RightLocPattern");
	pickPathKeys.Add("RightLocBreak");
	
	if (controlService.GetApplicationData(pickPathKeys, pickPathValues, "Dialogs\\PickPathProperties") < 0) {
		dialog.m_PickPathProperties_PathType_Val =0;
		dialog.m_PickPathProperties_PatternNum = 2;
		dialog.m_LBayStartVal = "01";
		dialog.m_LBayStepVal = 1;
		dialog.m_LBaySchemeVal = 2; 
		dialog.m_LBayPatternVal = " ";
		dialog.m_LLevelStart = "1"; 
		dialog.m_LLevelStep = 1;
		dialog.m_LLevelSchemeVal = 0;
		dialog.m_LLevelPatternVal = " ";
		dialog.m_LLevelBreak = 1;
		dialog.m_LLocStart = "1";
		dialog.m_LLocStep = 1;
		dialog.m_LLocSchemeVal = 0;
		dialog.m_LLocPatternVal = " ";
		dialog.m_LLocBreakVal = 3;
		dialog.m_RBayStart = "01";
		dialog.m_RBayStep = 1;
		dialog.m_RBaySchemeVal = 1;
		dialog.m_RBayPatternVal = " ";
		dialog.m_RLevelStart = "1";
		dialog.m_RLevelStep = 1;
		dialog.m_RLevelSchemeVal = 0;
		dialog.m_RLevelPatternVal = " ";
		dialog.m_RLevelBreak = 1;
		dialog.m_RLocStart = "1";
		dialog.m_RLocStep = 1;
		dialog.m_RLocSchemeVal = 0;
		dialog.m_RLocBreakVal = 3;
		dialog.m_RLocPatternVal = " ";

	}
	else {		
		dialog.m_PickPathProperties_PathType_Val = atoi(pickPathValues[0]);
		dialog.m_PickPathProperties_PatternNum = atoi(pickPathValues[1]);
		dialog.m_LBayStartVal = pickPathValues[2];
		dialog.m_LBayStepVal = atoi(pickPathValues[3]); 
		dialog.m_LBaySchemeVal = atoi(pickPathValues[4]); 
		dialog.m_LBayPatternVal = pickPathValues[5];
		dialog.m_LLevelStart = pickPathValues[6]; 
		dialog.m_LLevelStep = atoi(pickPathValues[7]); 
		dialog.m_LLevelSchemeVal = atoi(pickPathValues[8]); 
		dialog.m_LLevelPatternVal = pickPathValues[9];
		dialog.m_LLevelBreak = atoi(pickPathValues[10]);
		dialog.m_LLocStart = pickPathValues[11]; 
		dialog.m_LLocStep = atoi(pickPathValues[12]); 
		dialog.m_LLocSchemeVal = atoi(pickPathValues[13]); 
		dialog.m_LLocPatternVal = pickPathValues[14];
		dialog.m_LLocBreakVal = atoi(pickPathValues[15]); 
		dialog.m_RBayStart = pickPathValues[16];
		dialog.m_RBayStep = atoi(pickPathValues[17]);
		dialog.m_RBaySchemeVal = atoi(pickPathValues[18]);
		dialog.m_RBayPatternVal = pickPathValues[19];
		dialog.m_RLevelStart = pickPathValues[20];
		dialog.m_RLevelStep = atoi(pickPathValues[21]);
		dialog.m_RLevelSchemeVal = atoi(pickPathValues[22]);
		dialog.m_RLevelPatternVal = pickPathValues[23];
		dialog.m_RLevelBreak = atoi(pickPathValues[24]);
		dialog.m_RLocStart = pickPathValues[25];
		dialog.m_RLocStep = atoi(pickPathValues[26]);
		dialog.m_RLocSchemeVal = atoi(pickPathValues[27]);
		dialog.m_RLocBreakVal = atoi(pickPathValues[28]);
		dialog.m_RLocPatternVal = pickPathValues[29];
	}

	return;

}


void CElementMaintenanceHelper::SavePickPathValues(CPickPathPropertiesDialog &dialog)
{
	CStringArray pickPathKeys, pickPathValues;
	CControlService controlService;

	pickPathKeys.Add("PathType");
	pickPathKeys.Add("BaysInPattern");
	pickPathKeys.Add("LeftBayStart");
	pickPathKeys.Add("LeftBayStep");
	pickPathKeys.Add("LeftBayScheme");
	pickPathKeys.Add("LeftBayPattern");
	pickPathKeys.Add("LeftLevelStart");
	pickPathKeys.Add("LeftLevelStep");
	pickPathKeys.Add("LeftLevelScheme");
	pickPathKeys.Add("LeftLevelPattern");
	pickPathKeys.Add("LeftLevelBreak");
	pickPathKeys.Add("LeftLocStart");
	pickPathKeys.Add("LeftLocStep");
	pickPathKeys.Add("LeftLocScheme");
	pickPathKeys.Add("LeftLocPattern");
	pickPathKeys.Add("LeftLocBreak");
	pickPathKeys.Add("RightBayStart");
	pickPathKeys.Add("RightBayStep");
	pickPathKeys.Add("RightBayScheme");
	pickPathKeys.Add("RightBayPattern");
	pickPathKeys.Add("RightLevelStart");
	pickPathKeys.Add("RightLevelStep");
	pickPathKeys.Add("RightLevelScheme");
	pickPathKeys.Add("RightLevelPattern");
	pickPathKeys.Add("RightLevelBreak");
	pickPathKeys.Add("RightLocStart");
	pickPathKeys.Add("RightLocStep");
	pickPathKeys.Add("RightLocScheme");
	pickPathKeys.Add("RightLocPattern");
	pickPathKeys.Add("RightLocBreak");

	for (int i=0; i<30; ++i)
		pickPathValues.Add("");

	if (dialog.m_LBayPatternVal == "") dialog.m_LBayPatternVal = " ";
	if (dialog.m_RBayPatternVal == "") dialog.m_RBayPatternVal = " ";
	if (dialog.m_LLevelPatternVal == "") dialog.m_LLevelPatternVal = " ";
	if (dialog.m_RLevelPatternVal == "") dialog.m_RLevelPatternVal = " ";
	if (dialog.m_LLocPatternVal == "") dialog.m_LLocPatternVal = " ";
	if (dialog.m_RLocPatternVal == "") dialog.m_RLocPatternVal = " ";

	pickPathValues[0].Format("%d", dialog.m_PickPathProperties_PathType_Val);
	pickPathValues[1].Format("%d", dialog.m_PickPathProperties_PatternNum);
	pickPathValues[2].Format("%s", dialog.m_LBayStartVal);
	pickPathValues[3].Format("%d", dialog.m_LBayStepVal);
	pickPathValues[4].Format("%d", dialog.m_LBaySchemeVal);
	pickPathValues[5].Format("%s", dialog.m_LBayPatternVal);
	pickPathValues[6].Format("%s", dialog.m_LLevelStart);
	pickPathValues[7].Format("%d", dialog.m_LLevelStep);
	pickPathValues[8].Format("%d", dialog.m_LLevelSchemeVal);
	pickPathValues[9].Format("%s", dialog.m_LLevelPatternVal);
	pickPathValues[10].Format("%d", dialog.m_LLevelBreak);
	pickPathValues[11].Format("%s", dialog.m_LLocStart);
	pickPathValues[12].Format("%d", dialog.m_LLocStep);
	pickPathValues[13].Format("%d", dialog.m_LLocSchemeVal);
	pickPathValues[14].Format("%s", dialog.m_LLocPatternVal);
	pickPathValues[15].Format("%d", dialog.m_LLocBreakVal);
	pickPathValues[16].Format("%s", dialog.m_RBayStart);
	pickPathValues[17].Format("%d", dialog.m_RBayStep);
	pickPathValues[18].Format("%d", dialog.m_RBaySchemeVal);
	pickPathValues[19].Format("%s", dialog.m_RBayPatternVal);
	pickPathValues[20].Format("%s", dialog.m_RLevelStart);
	pickPathValues[21].Format("%d", dialog.m_RLevelStep);
	pickPathValues[22].Format("%d", dialog.m_RLevelSchemeVal);
	pickPathValues[23].Format("%s", dialog.m_RLevelPatternVal);
	pickPathValues[24].Format("%d", dialog.m_RLevelBreak);
	pickPathValues[25].Format("%s", dialog.m_RLocStart);
	pickPathValues[26].Format("%d", dialog.m_RLocStep);
	pickPathValues[27].Format("%d", dialog.m_RLocSchemeVal);
	pickPathValues[28].Format("%d", dialog.m_RLocBreakVal);
	pickPathValues[29].Format("%s", dialog.m_RLocPatternVal);

	controlService.SetApplicationData(pickPathKeys, pickPathValues, "Dialogs\\PickPathProperties");


}

void CElementMaintenanceHelper::FacilityTreeViewer()
{
	CTemporaryResourceOverride thisResource;
	CFacilityTools dlg;

	try {
		dlg.DoModal();
	}
	catch (...) {
		AfxMessageBox("Error displaying facility tools.");
	}
}


/*
void ConvertBayCoordinatesToGlobal(CPoint aisleCoordinate, double aisleRotation, double bayDepth, double aisleSpace,
								   int baySide, CPoint &bayCoordinate)
{
	
	
	// convert degrees to radians and negate
	double radians = 0 - (aisleRotation * PI / 180.0);

	int origX, origY;
	
	origX = bayCoordinate.x;
	origY = bayCoordinate.y;

	// rotate bay coordinates by negative angle
	bayCoordinate.x = origX * cos(radians) - origY * sin(radians);
	bayCoordinate.y = origX * sin(radians) - origY * cos(radians);

	// add the aisle x coord to the bay
	bayCoordinate.x += aisleCoordinate.x;

	// for the y, we have to know which side of the aisle the bay is on
	// side 1 means it's on the left (closest to the origin)
	if (baySide == 1)
		bayCoordinate.y = aisleCoordinate.y - (bayDepth/2);
	else {
		bayCoordinate.y = aisleCoordinate.y - aisleSpace;

	}
	
}
*/



void CElementMaintenanceHelper::FacilityProperties(qqhSLOTFacility &facility)
{
	CTemporaryResourceOverride thisResource;
	CFacilityElementSheet facilitySheet;
	CFacilityProperties facilityPage1;
	CUDFPage facilityPage2;
	CString udfString;
	CUDF *pUDF;
	int i;

	facilitySheet.m_ElementDBId = facility.getDBID();
	facilitySheet.m_ElementType = UDF_FACILITY;

	facilityPage1.m_Description = facility.getDescription();
	facilityPage1.m_Notes = facility.getNotes();
	facilityPage1.m_Notes.Replace("<nl>", "\r\n");
	facilityPage1.m_TimeHorizonUnits = facility.getTimeHorizonUnit() - 1;
	facilityPage1.m_TimeHorizonValue = facility.getDuration();
	facilityPage1.m_UOM = facility.getUnits();	

	try {
		utilityHelper.SortStringArray(facility.getUDFList());
	}
	catch (...) {
		AfxGetApp()->DoWaitCursor(-1);
		return;
	}
	
	for (i=0; i < facility.getUDFList().GetSize(); ++i) {
		udfString = facility.getUDFList()[i];
		pUDF = new CUDF;
		pUDF->Parse(udfString);	
		facilityPage2.m_UDFs.Add(pUDF);
	}


	facilitySheet.m_psh.dwFlags &= ~(PSH_HASHELP);
	facilitySheet.m_psh.dwFlags |= PSH_NOAPPLYNOW;
	facilitySheet.SetTitle("Facility Properties");
	facilitySheet.AddPage(&facilityPage1);
	if (facility.getUDFList().GetSize() > 0)
		facilitySheet.AddPage(&facilityPage2);
	
	if ( facilitySheet.DoModal() == IDCANCEL )
		return;

	CString notes = facilityPage1.m_Notes;
	notes.Replace("\r\n", "<nl>");
	notes.Replace("\n", "<nl>");
	facility.setNotes(notes);
	
	facility.setTimeHorizonUnit(facilityPage1.m_TimeHorizonUnits+1);
	facility.setDuration(facilityPage1.m_TimeHorizonValue);
	facility.setUnits(facilityPage1.m_UOM);

	facility.getUDFList().RemoveAll();
	for (i=0; i < facilityPage2.m_UDFs.GetSize(); ++i) {
		pUDF = (CUDF *)(facilityPage2.m_UDFs[i]);
		facility.getUDFList().Add(pUDF->Stream());		
	}

	return;

}


void CElementMaintenanceHelper::SectionProperties(qqhSLOTSection &section)
{
	CTemporaryResourceOverride thisResource;
	CFacilityElementSheet sectionSheet;
	CNewSectionPage1 sectionPage1;
	CNewSectionPage2 sectionPage2;
	CUDFPage sectionPage3;
	qqhSLOTHotSpot hotspot;
	CString udfString;
	CUDF *pUDF;

	sectionSheet.m_ElementDBId = section.getDBID();
	sectionSheet.m_ElementType = UDF_SECTION;

	sectionPage1.m_NewSection_Description = section.getDescription();
	sectionPage1.m_NewSection_LocationMask = section.getLocationMask();
	sectionPage1.m_NewSection_ContainerCount = section.getContQty();
	sectionPage1.m_NewSection_AvgOrdQty = section.getAvgOrdQty();
	sectionPage1.m_NewSection_OrderCount = section.getOrderCount();
	sectionPage1.m_TravelDistance.Format("%-10.2f", section.getSelDist());

	sectionPage1.m_SelHotspotCoordinates.Format("0, 0, 0");
	sectionPage1.m_ForkHotspotCoordinates.Format("0, 0, 0");

	for (int i=0; i < section.getHotSpotList().GetSize(); ++i) {
		hotspot = section.getHotSpotList().GetAt(i);
		if (hotspot.getHotSpotType() == 2) {
			sectionPage1.m_SelHotspotCoordinates.Format("%d, %d, %d", 
				hotspot.getCoord().getX(),
				hotspot.getCoord().getY(),
				hotspot.getCoord().getZ());
		}
		else {
			sectionPage1.m_ForkHotspotCoordinates.Format("%d, %d, %d", 
				hotspot.getCoord().getX(),
				hotspot.getCoord().getY(),
				hotspot.getCoord().getZ());
		}
	}
	
	if (section.getApplyBrokenOrder().CompareNoCase("TRUE") == 0)
		sectionPage1.m_NewSection_ApplyBrokenOrder = TRUE;
	else
		sectionPage1.m_NewSection_ApplyBrokenOrder = FALSE;

	sectionPage2.m_NewSection_ForkDistFixed = section.getForkDistFixed();
	sectionPage2.m_NewSection_ForkDistVar = section.getForkDistVar();
	sectionPage2.m_NewSection_ForkLaborRate = section.getForkLaborRate();
	sectionPage2.m_NewSection_ReplAvgDist = section.getReplenAvgDist();
	sectionPage2.m_NewSection_ForkInsertion = section.getInsertForkTravel();
	sectionPage2.m_NewSection_ForkPickup = section.getPickupForkTravel();
	sectionPage2.m_NewSection_PalletsPerTrip = section.getPalletsPerPtwyTrip();
	
	sectionPage2.m_NewSection_SelFixedFactor = section.getSelectDistFixed();
	sectionPage2.m_NewSection_SelVarFactor = section.getSelectDistVar();
	sectionPage2.m_NewSection_SelLaborRate = section.getSelectLaborRate();
	sectionPage2.m_NewSection_AvgCubePerTrip = section.getAvgCubePerTrip();
	sectionPage2.m_NewSection_StockerFixed = section.getStockerDistFixed();
	sectionPage2.m_NewSection_StockerVar = section.getStockerDistVar();
	sectionPage2.m_NewSection_StockerLaborRate = section.getStockerLaborRate();

	try {
		utilityHelper.SortStringArray(section.getUDFList());
	}
	catch (...) {
		return;
	}

	for (i=0; i < section.getUDFList().GetSize(); ++i) {
		udfString = section.getUDFList()[i];
		pUDF = new CUDF;
		pUDF->Parse(udfString);

		sectionPage3.m_UDFs.Add(pUDF);

	}
	sectionPage1.m_SectionDBId = section.getDBID();
	
	sectionSheet.m_psh.dwFlags &= ~(PSH_HASHELP);
	sectionSheet.m_psh.dwFlags |= PSH_NOAPPLYNOW;
	sectionSheet.SetTitle("Section Properties");
	sectionSheet.AddPage(&sectionPage1);
	sectionSheet.AddPage(&sectionPage2);
	if (section.getUDFList().GetSize() > 0)
		sectionSheet.AddPage(&sectionPage3);
	
	if ( sectionSheet.DoModal() == IDCANCEL )
		return;
	
	section.setLocationMask(sectionPage1.m_NewSection_LocationMask);
	CLocationNumberingService locationNumberingService;
	section.setDescription(locationNumberingService.FindNextID(sectionPage1.m_NewSection_Description,1,0,1,sectionPage1.m_NewSection_LocationMask,3));
	
	if (sectionPage1.m_NewSection_ApplyBrokenOrder)
		section.setApplyBrokenOrder(CString("TRUE"));
	else
		section.setApplyBrokenOrder(CString("FALSE"));

	section.setContQty(sectionPage1.m_NewSection_ContainerCount);
	section.setAvgOrdQty(sectionPage1.m_NewSection_AvgOrdQty);
	section.setOrderCount(sectionPage1.m_NewSection_OrderCount);

	section.setForkDistFixed(sectionPage2.m_NewSection_ForkDistFixed);
	section.setForkDistVar(sectionPage2.m_NewSection_ForkDistVar);
	section.setForkLaborRate(sectionPage2.m_NewSection_ForkLaborRate);
	section.setReplenAvgDist(sectionPage2.m_NewSection_ReplAvgDist);
	section.setInsertForkTravel(sectionPage2.m_NewSection_ForkInsertion);
	section.setPickupForkTravel(sectionPage2.m_NewSection_ForkPickup);
	section.setPalletsPerPtwyTrip(sectionPage2.m_NewSection_PalletsPerTrip);
	
	section.setSelectDistFixed(sectionPage2.m_NewSection_SelFixedFactor);
	section.setSelectDistVar(sectionPage2.m_NewSection_SelVarFactor);
	section.setSelectLaborRate(sectionPage2.m_NewSection_SelLaborRate);
	section.setAvgCubePerTrip(sectionPage2.m_NewSection_AvgCubePerTrip);
	section.setStockerDistFixed(sectionPage2.m_NewSection_StockerFixed);
	section.setStockerDistVar(sectionPage2.m_NewSection_StockerVar);
	section.setStockerLaborRate(sectionPage2.m_NewSection_StockerLaborRate);
	
	section.getUDFList().RemoveAll();
	for (i=0; i < sectionPage3.m_UDFs.GetSize(); ++i) {
		pUDF = (CUDF *)(sectionPage3.m_UDFs[i]);
		section.getUDFList().Add(pUDF->Stream());		
	}


	return;


}


void CElementMaintenanceHelper::AisleProperties(qqhSLOTAisle &aisle) 
{
	CTemporaryResourceOverride thisResource;
	CFacilityElementSheet aisleSheet;
	CAisleProperties aislePage1;
	CUDFPage aislePage2;
	int i;
	CString udfString;
	CUDF *pUDF;

	aisleSheet.m_ElementDBId = aisle.getDBID();
	aisleSheet.m_ElementType = UDF_AISLE;

	aislePage1.m_Description = aisle.getDescription();
	aislePage1.m_Coordinates.Format("%d, %d, %d", 
		aisle.getCoord().getX(), 
		aisle.getCoord().getY(), 
		aisle.getCoord().getZ());
	aislePage1.m_Rotation = aisle.getRotation();

	try {
		utilityHelper.SortStringArray(aisle.getUDFList());
	}
	catch (...) {
		AfxMessageBox("Error sorting UDFs.");
		return;
	}

	for (i=0; i < aisle.getUDFList().GetSize(); ++i) {
		udfString = aisle.getUDFList()[i];
		pUDF = new CUDF;
		pUDF->Parse(udfString);

		aislePage2.m_UDFs.Add(pUDF);

	}

	aisleSheet.m_psh.dwFlags &= ~(PSH_HASHELP);
	aisleSheet.m_psh.dwFlags |= PSH_NOAPPLYNOW;
	aisleSheet.SetTitle("Aisle Properties");
	aisleSheet.AddPage(&aislePage1);
	if (aisle.getUDFList().GetSize() > 0)
		aisleSheet.AddPage(&aislePage2);

	if ( aisleSheet.DoModal() == IDCANCEL )
		return;
	
	aisle.setDescription(aislePage1.m_Description);

	aisle.getUDFList().RemoveAll();
	for (i=0; i < aislePage2.m_UDFs.GetSize(); ++i) {
		pUDF = (CUDF *)(aislePage2.m_UDFs[i]);
		aisle.getUDFList().Add(pUDF->Stream());		
	}


	return;

}


void CElementMaintenanceHelper::SideProperties(qqhSLOTSide &side)
{
	CTemporaryResourceOverride thisResource;
	CFacilityElementSheet sideSheet;
	CSideProperties sidePage1;
	CUDFPage sidePage2;
	CString handle;
	int i;
	CString udfString;
	CUDF *pUDF;

	sideSheet.m_ElementDBId = side.getDBID();
	sideSheet.m_ElementType = UDF_SIDE;

	sidePage1.m_Description = side.getDescription();

	try {
		utilityHelper.SortStringArray(side.getUDFList());
	}
	catch (...) {
		AfxMessageBox("Error sorting UDFs.");
		return;
	}

	for (i=0; i < side.getUDFList().GetSize(); ++i) {
		udfString = side.getUDFList()[i];
		pUDF = new CUDF;
		pUDF->Parse(udfString);

		sidePage2.m_UDFs.Add(pUDF);

	}

	sideSheet.m_psh.dwFlags &= ~(PSH_HASHELP);
	sideSheet.m_psh.dwFlags |= PSH_NOAPPLYNOW;
	sideSheet.SetTitle("Side Properties");
	sideSheet.AddPage(&sidePage1);
	if (side.getUDFList().GetSize() > 0)
		sideSheet.AddPage(&sidePage2);

	if ( sideSheet.DoModal() == IDCANCEL )
		return;


	side.getUDFList().RemoveAll();
	for (i=0; i < sidePage2.m_UDFs.GetSize(); ++i) {
		pUDF = (CUDF *)(sidePage2.m_UDFs[i]);
		side.getUDFList().Add(pUDF->Stream());		
	}


	return;

}


void CElementMaintenanceHelper::BayProperties(qqhSLOTBay &bay)
{
	CTemporaryResourceOverride thisResource;
	CFacilityElementSheet baySheet;
	CBayProperties bayPage1;
	CUDFPage bayPage2;
	int i;
	CString udfString;
	CUDF *pUDF;
	
	baySheet.m_ElementDBId = bay.getDBID();
	baySheet.m_ElementType = UDF_BAY;

	if (bay.pBayProfile == NULL) {
		int rc;
		try {
			bay.pBayProfile = new CBayProfile;
			rc = bayProfileDataService.GetBayProfile(bay.getBayProfileId(), *bay.pBayProfile, 
				CBayProfile::loadBayOnly);
		}
		catch (...) {
			rc = -1;
		}
		
		if (rc < 0) {
			AfxMessageBox("Error loading bay profile for bay.");
			delete bay.pBayProfile;
			bay.pBayProfile = NULL;
			return;
		}
	}

	bay.pBayProfile->m_Active = TRUE;

	bayPage1.m_Description = bay.getDescription();
	bayPage1.m_Profile.Format("%s\\%s", bay.pBayProfile->ConvertBayType(), 
		bay.pBayProfile->m_Description);
	bayPage1.m_pBayProfile = bay.pBayProfile;

	try {
		utilityHelper.SortStringArray(bay.getUDFList());
	}
	catch (...) {
		AfxMessageBox("Error sorting UDFs.");
		return;
	}

	for (i=0; i < bay.getUDFList().GetSize(); ++i) {
		udfString = bay.getUDFList()[i];
		pUDF = new CUDF;
		pUDF->Parse(udfString);
		bayPage2.m_UDFs.Add(pUDF);
	}

	baySheet.m_psh.dwFlags &= ~(PSH_HASHELP);
	baySheet.m_psh.dwFlags |= PSH_NOAPPLYNOW;
	baySheet.SetTitle("Bay Properties");
	baySheet.AddPage(&bayPage1);

	if (bay.getUDFList().GetSize() > 0)
		baySheet.AddPage(&bayPage2);

	if ( baySheet.DoModal() == IDCANCEL )
		return;

	bay.setDescription(bayPage1.m_Description);

	bay.getUDFList().RemoveAll();
	for (i=0; i < bayPage2.m_UDFs.GetSize(); ++i) {
		pUDF = (CUDF *)(bayPage2.m_UDFs[i]);
		bay.getUDFList().Add(pUDF->Stream());		
	}

	return;

}


void CElementMaintenanceHelper::LevelProperties(qqhSLOTLevel &level)
{
	CTemporaryResourceOverride thisResource;
	CFacilityElementSheet levelSheet;
	CLevelProperties levelPage1;
	CString udfString;

	levelSheet.m_ElementDBId = level.getDBID();
	levelSheet.m_ElementType = UDF_LEVEL;

	if (level.pLevelProfile == NULL) {
		int rc;
		try {
			level.pLevelProfile = new CLevelProfile;
			rc = bayProfileDataService.GetLevelProfile(level.getLevelProfileId(), *level.pLevelProfile);
		}
		catch (...) {
			rc = -1;
		}

		if (rc < 0) {
			AfxMessageBox("Error loading level profile for level.");
			delete level.pLevelProfile;
			level.pLevelProfile = NULL;
			return;
		}
	}
	levelPage1.m_Description = level.getDescription();
	levelPage1.m_Coordinates.Format("%d, %d, %d",
		level.getCoord().getX(), level.getCoord().getY(), level.getCoord().getZ());
	levelPage1.m_FacingGap = level.getFacingGap();
	levelPage1.m_FacingSnap = level.getFacingSnap();
	levelPage1.m_ForkFixedInsertion = level.getForkFixedInsertion();
	CBayProfile::ConvertBayType(level.pLevelProfile->m_Baytype, levelPage1.m_LevelType);
	levelPage1.m_MinLocWidth = level.getMinLocWidth();
	levelPage1.m_ProductGap = level.getProductGap();
	levelPage1.m_ProductSnap = level.getProductSnap();
	levelPage1.m_RelativeLevel = level.pLevelProfile->m_RelativeLevel;
	levelPage1.m_RotateAllowed = level.getIsRotateAllowed();
	levelPage1.m_VariableWidth = level.getIsVariableLocationsAllowed();

	levelSheet.m_psh.dwFlags &= ~(PSH_HASHELP);
	levelSheet.m_psh.dwFlags |= PSH_NOAPPLYNOW;
	levelSheet.SetTitle("Level Properties");
	levelSheet.AddPage(&levelPage1);

	if ( levelSheet.DoModal() == IDCANCEL )
		return;

	level.setDescription(levelPage1.m_Description);
	level.setFacingGap(levelPage1.m_FacingGap);
	level.setFacingSnap(levelPage1.m_FacingSnap);
	level.setForkFixedInsertion(levelPage1.m_ForkFixedInsertion);
	level.setIsRotateAllowed(levelPage1.m_RotateAllowed);
	level.setIsVariableLocationsAllowed(levelPage1.m_VariableWidth);
	level.setMinLocWidth(levelPage1.m_MinLocWidth);
	level.setProductGap(levelPage1.m_ProductGap);
	level.setProductSnap(levelPage1.m_ProductSnap);

	level.setIsOverridden(level.isLevelOverridden(*level.pLevelProfile));


	return;

}




int CElementMaintenanceHelper::LocationProperties(qqhSLOTLocation &location, qqhSLOTLevel &level, qqhSLOTBay &bay, double totalWidth, BOOL bAllowUpdate)
{
	CTemporaryResourceOverride thisResource;
	CFacilityElementSheet locationSheet;
	CLocationProperties locationPage1;
	CLocationAttributesPage locationPage2;

	CSolutionDataService solutionDataService;
	CFacilityDataService facilityDataService;

	locationSheet.m_ElementDBId = location.getDBID();
	locationSheet.m_ElementType = UDF_LOCATION;

	if (bay.pBayProfile == NULL) {
		int rc;
		try {
			bay.pBayProfile = new CBayProfile;
			rc = bayProfileDataService.GetBayProfile(bay.getBayProfileId(), *bay.pBayProfile, CBayProfile::loadBayOnly);
		}
		catch (...) {
			rc = -1;
		}
		
		if (rc < 0) {
			AfxMessageBox("Error loading bay profile for bay.");
			delete bay.pBayProfile;
			bay.pBayProfile = NULL;
			return -1;
		}
	}

	if (level.pLevelProfile == NULL) {
		int rc;
		try {
			level.pLevelProfile = new CLevelProfile;
			rc = bayProfileDataService.GetLevelProfile(level.getLevelProfileId(), *level.pLevelProfile);
		}
		catch (...) {
			rc = -1;
		}

		if (rc < 0) {
			AfxMessageBox("Error loading level profile for level.");
			delete level.pLevelProfile;
			level.pLevelProfile = NULL;
			return -1;
		}
	}

	if (location.pLocationProfile == NULL) {
		int rc;
		try {
			location.pLocationProfile = new CLocationProfile;
			rc = bayProfileDataService.GetLocationProfile(location.getLocationProfileId(), *location.pLocationProfile);
		}
		catch (...) {
			rc = -1;
		}

		if (rc < 0) {
			AfxMessageBox("Error loading location profile for location.");
			delete location.pLocationProfile;
			location.pLocationProfile = NULL;
			return -1;
		}
	}

	int rc;
	try {
		rc = bayProfileDataService.GetLocationExternalInfo(location);
	}
	catch (...) {
		rc = -1;
	}
	
	if (rc < 0) {
		AfxMessageBox("Error loading location external info.");
		return -1;
	}

	locationPage1.m_Description = location.getDescription();
	locationPage1.m_Width = location.getWidth();
	locationPage1.m_Depth = location.getDepth();
	locationPage1.m_Height = location.getHeight();
	locationPage1.m_MaxWeight = location.getMaxWeight();
	locationPage1.m_Status = location.getStatusText();
	locationPage1.m_IsActive = location.getIsActive();
	locationPage1.m_StockerId = location.getStockerId();
	locationPage1.m_Trace = location.getTrace();
	locationPage1.m_BackfillCoordinates = location.getBackfillCoordinates().ToText();
	locationPage1.m_StockerCoordinates = location.getStockerCoordinates().ToText();
	locationPage1.m_Clearance = location.getClearance();
	locationPage1.m_SelectionSequence = location.getSelectionSequence();
	locationPage1.m_ReplenishmentSequence = location.getReplenishmentSequence();

	locationPage1.m_BackfillId = level.pLevelProfile->m_BackfillCode;
	locationPage1.m_BackfillId.Replace("%L", location.getDescription());
	locationPage1.m_BackfillId.Replace("%l", location.getDescription());
	
	if (location.getHandlingMethod() == 3)
		locationPage1.m_HandlingMethod = 1;
	else
		locationPage1.m_HandlingMethod = 0;

	locationPage1.m_IsSelect = (location.getIsSelect() == 1);
	locationPage1.m_Coordinates.Format("%d, %d, %d",
		location.getCoord().getX(), location.getCoord().getY(), location.getCoord().getZ());

	locationPage1.m_LocationDBId = location.getDBID();

	if (location.getDBID() > 0) {
		CString temp, temp1, temp2;
		if (facilityDataService.GetProductGroupForLocation(location.getDBID(), temp) > 0)
			locationPage1.m_ProductGroup = temp;
		else
			locationPage1.m_ProductGroup = "";

		long dbid;
		if (solutionDataService.GetProductForLocation(location.getDBID(), temp, temp1, 
			temp2, dbid, CSolution::Optimize) > 0) {
			locationPage1.m_ProductDescription = temp2;
			locationPage1.m_WMSID.Format("%s-%s", temp, temp1);
			locationPage1.m_ProductDBID = dbid;
		}
		else {
			locationPage1.m_ProductDescription = "";
			locationPage1.m_WMSID = "";
			locationPage1.m_ProductDBID = -1;
		}
	}

	locationPage1.m_TotalWidth = totalWidth;
	locationPage1.m_MaxWidth = bay.pBayProfile->m_Width;
	if (level.getIsVariableLocationsAllowed())
		locationPage1.m_MinWidth = level.getMinLocWidth();
	else
		locationPage1.m_MinWidth = 0;
	locationPage1.m_MaxDepth = bay.pBayProfile->m_Depth + level.pLevelProfile->m_Overhang;
	locationPage1.m_MaxHeight = location.pLocationProfile->m_Height;
	locationPage1.m_OriginalWidth = location.getWidth();

	locationPage2.m_pLocation = &location;

	locationSheet.m_psh.dwFlags &= ~(PSH_HASHELP);
	locationSheet.m_psh.dwFlags |= PSH_NOAPPLYNOW;
	locationSheet.SetTitle("Location Properties");
	locationSheet.AddPage(&locationPage1);
	locationSheet.AddPage(&locationPage2);

	if (! bAllowUpdate)
		locationSheet.m_AllowUpdate = FALSE;

	qqhSLOTLocation oldLocation = location;

	if ( locationSheet.DoModal() == IDCANCEL )
		return -1;
	
	location.setDescription(locationPage1.m_Description);
	location.setWidth(locationPage1.m_Width);
	location.setDepth(locationPage1.m_Depth);
	location.setHeight(locationPage1.m_Height);
	location.setMaxWeight(locationPage1.m_MaxWeight);
	if (locationPage1.m_HandlingMethod == 0)
		location.setHandlingMethod(2);
	else
		location.setHandlingMethod(3);
	location.setIsSelect((locationPage1.m_IsSelect == 1));
	location.setIsActive(locationPage1.m_IsActive);
	
	location.setBackfillId(locationPage1.m_BackfillId);
	qqhSLOTCoordinate tempCoord;
	tempCoord.FromText(locationPage1.m_BackfillCoordinates);
	location.setBackfillCoordinates(tempCoord);
	location.setStockerId(locationPage1.m_StockerId);
	tempCoord.FromText(locationPage1.m_StockerCoordinates);
	location.setStockerCoordinates(tempCoord);
	location.setTrace(locationPage1.m_Trace);
	location.setClearance(locationPage1.m_Clearance);
	location.setSelectionSequence(locationPage1.m_SelectionSequence);
	location.setReplenishmentSequence(locationPage1.m_ReplenishmentSequence);

	location.setIsOverridden(location.isLocationOverridden(*location.pLocationProfile));

	if (! (oldLocation == location))
		return 1;

	return 0;

}


void CElementMaintenanceHelper::FacilityMaintenance()
{
	int facilityFileOffset;
	qqhSLOTFacility facility, oldFacility;


	CWaitCursor cwc;

	facilityFileOffset = changesTree.fileOffset;
	bTreeHelper.GetBtFacility(facilityFileOffset,facility);
	
	oldFacility = facility;
	
	FacilityProperties(facility);

	if (! (facility == oldFacility)) {
		numItemsProcessed++;
		facility.setIsChanged("TRUE");
		if (bTreeHelper.SetBtFacility(facilityFileOffset, facility) < 0) {
			AfxMessageBox("Unable to write facility.");
		}
	}

	return;
}


void CElementMaintenanceHelper::SectionMaintenance()
{
	TreeElement *sectionPtr;
	qqhSLOTSection section, oldSection;
	CString handle;
	CAutoCADCommands acCmds;

	CWaitCursor cwc;

	if (CAutoCADCommands::GetSelectedHandle(handle) <= 0) {
		AfxMessageBox("Please select a bay in the section you wish to modify.");
		return;
	}

	sectionPtr = changesTree.getSectionByBayHandle(handle);
	if (sectionPtr == NULL) {
		AfxMessageBox("Unable to determine the section for the selected object.\n");
		return;
	}

	if (bTreeHelper.GetBtSection(sectionPtr->fileOffset, section) < 0) {
		AfxMessageBox("Unable to read section.");
		return;
	}

	oldSection = section;

	SectionProperties(section);

	if (! (section == oldSection)) {
		numItemsProcessed++;
		bTreeHelper.SetBtSection(sectionPtr->fileOffset,section);
		
		if (oldSection.getDescription() != section.getDescription())
			UpdateLocationsForSectionChange(sectionPtr->fileOffset, section);
	}


	return;	
}



void CElementMaintenanceHelper::AisleMaintenance()
{

	TreeElement *aislePtr, *sectionPtr;
	qqhSLOTAisle aisle, oldAisle;
	CString handle;
	CWaitCursor cwc;

	if (CAutoCADCommands::GetSelectedHandle(handle) <= 0) {
		AfxMessageBox("Please select a bay in the aisle you wish to modify.");
		return;
	}

	aislePtr = changesTree.getAisleByBayHandle(handle);
	if (aislePtr == NULL) {
		AfxMessageBox("Unable to determine the aisle for the selected object.\n");
		return;
	}

	if (bTreeHelper.GetBtAisle(aislePtr->fileOffset, aisle) < 0) {
		AfxMessageBox("Unable to read aisle.");
		return;
	}
	
	oldAisle = aisle;
	sectionPtr = aislePtr->treeParent;

	AisleProperties(aisle);

	if (! (aisle == oldAisle)) {
		aisle.setIsChanged("TRUE");
		bTreeHelper.SetBtAisle(aislePtr->fileOffset,aisle);
		numItemsProcessed++;
		
		if (oldAisle.getDescription() != aisle.getDescription())
			UpdateLocationsForAisleChange(sectionPtr->fileOffset, aislePtr->fileOffset, aisle);
	}

	return;

}



void CElementMaintenanceHelper::SideMaintenance()
{
	TreeElement *sidePtr;
	qqhSLOTSide side, oldSide;
	CString handle;

	CWaitCursor cwc;

	if (CAutoCADCommands::GetSelectedHandle(handle) <= 0) {
		AfxMessageBox("Please select a bay in the side you wish to modify.");
		return;
	}

	sidePtr = changesTree.getSideByBayHandle(handle);
	if (sidePtr == NULL) {
		AfxMessageBox("Unable to determine the side for the selected object.\n");
		return;
	}

	if (bTreeHelper.GetBtSide(sidePtr->fileOffset, side) < 0) {
		AfxMessageBox("Unable to read side.");
		return;
	}

	oldSide = side;

	SideProperties(side);

	if (! (side == oldSide)) {
		numItemsProcessed++;
		side.setIsChanged("TRUE");
		bTreeHelper.SetBtSide(sidePtr->fileOffset,side);
	}

	return;

}



void CElementMaintenanceHelper::BayMaintenance()
{
	TreeElement *bayPtr, *sidePtr, *aislePtr, *sectionPtr;
	qqhSLOTBay bay, oldBay;
	CString handle;


	CWaitCursor cwc;

	if (CAutoCADCommands::GetSelectedHandle(handle) <= 0) {
		AfxMessageBox("Please select the bay you wish to modify.");
		return;
	}

	bayPtr = changesTree.getBayByHandle(handle);
	if (bayPtr == NULL) {
		AfxMessageBox("Unable to determine the bay for the selected object.\n");
		return;
	}

	if (bTreeHelper.GetBtBay(bayPtr->fileOffset, bay) < 0) {
		AfxMessageBox("Unable to read bay.");
		return;
	}
	
	oldBay = bay;

	sidePtr = bayPtr->treeParent;
	aislePtr = sidePtr->treeParent;
	sectionPtr = aislePtr->treeParent;
	
	BayProperties(bay);

	if (! (bay == oldBay)) {
		bay.setIsChanged("TRUE");
		numItemsProcessed++;

		bTreeHelper.SetBtBay(bayPtr->fileOffset,bay);
	
		if (oldBay.getDescription() != bay.getDescription())
			UpdateLocationsForBayChange(sectionPtr->fileOffset, bayPtr->fileOffset, bay);
	}

	return;


}



void CElementMaintenanceHelper::LevelLocationMaintenance()
{
	CString handle;	
	CWaitCursor cwc;

	if (CAutoCADCommands::GetSelectedHandle(handle) <= 0) {
		AfxMessageBox("Please select the bay containing the level or location you wish to modify.");
		return;
	}

	
	CLevelLocationDialog dlg;

	dlg.m_Handle = handle;

	dlg.DoModal();

	return;

}


void CElementMaintenanceHelper::LevelMaintenance(TreeElement *levelPtr)
{
	qqhSLOTLevel oldLevel, level;

	TreeElement *bayPtr, *sidePtr, *aislePtr, *sectionPtr;
	CWaitCursor cwc;

	if (bTreeHelper.GetBtLevel(levelPtr->fileOffset, level) < 0) {
		AfxGetApp()->DoWaitCursor(-1);
		AfxMessageBox("Unable to read level.");
		return;
	}

	bayPtr = levelPtr->treeParent;
	sidePtr = bayPtr->treeParent;
	aislePtr = sidePtr->treeParent;
	sectionPtr = aislePtr->treeParent;

	oldLevel = level;

	LevelProperties(level);

	if (! (level == oldLevel)) {
		numItemsProcessed++;
		
		level.setIsChanged("TRUE");
		bTreeHelper.SetBtLevel(levelPtr->fileOffset, level);
		
		if (oldLevel.getDescription() != level.getDescription())
			UpdateLocationsForLevelChange(sectionPtr->fileOffset, levelPtr->fileOffset, level);
	}

	return;
}



void CElementMaintenanceHelper::LocationMaintenance(TreeElement *locationPtr)
{
	qqhSLOTLocation location, tempLocation, oldLocation;
	qqhSLOTLevel level;
	qqhSLOTBay bay;
	int i;
	double totalWidth;

	AfxGetApp()->DoWaitCursor(1);
	TreeElement *levelPtr, *bayPtr, *sidePtr, *aislePtr, *sectionPtr, *tempLocPtr;

	if (bTreeHelper.GetBtLocation(locationPtr->fileOffset, location) < 0) {
		AfxGetApp()->DoWaitCursor(-1);
		AfxMessageBox("Unable to read location.");
		return;
	}

	oldLocation = location;

	levelPtr = locationPtr->treeParent;
	if (bTreeHelper.GetBtLevel(levelPtr->fileOffset, level) < 0) {
		AfxGetApp()->DoWaitCursor(-1);
		AfxMessageBox("Unable to read level.");
		return;
	}
	bayPtr = levelPtr->treeParent;
	if (bTreeHelper.GetBtBay(bayPtr->fileOffset, bay) < 0) {
		AfxGetApp()->DoWaitCursor(-1);
		AfxMessageBox("Unable to read bay.");
		return;
	}

	sidePtr = bayPtr->treeParent;
	aislePtr = sidePtr->treeParent;
	sectionPtr = aislePtr->treeParent;

	totalWidth = 0;
	for (i=0; i < levelPtr->treeChildren.GetSize(); ++i) {
		tempLocPtr = &(levelPtr->treeChildren[i]);
		if (bTreeHelper.GetBtLocation(tempLocPtr->fileOffset, tempLocation) >= 0) {
			totalWidth += tempLocation.getWidth();
		}
	}

	if (LocationProperties(location, level, bay, totalWidth) == 1) {
		numItemsProcessed++;
		location.setIsChanged("TRUE");
		bTreeHelper.SetBtLocation(locationPtr->fileOffset, location);
	}

	return;
}


int CElementMaintenanceHelper::UpdateLocationsForSectionChange(int sectionOffset, qqhSLOTSection &section)
{

	CArray<int, int> locOffsetList;
	int i;

	// get the list of location offsets for the section offset
	changesTree.getLocationOffsetBySection(sectionOffset, locOffsetList);
	
	if (bTreeHelper.OpenBTree() < 0) {
		AfxMessageBox("Error updating locations.  Unable to open binary tree.");
		return -1;
	}

	for (i=0; i < locOffsetList.GetSize(); ++i) {
		numberingService.UpdateLocationDescriptionPart(locOffsetList[i],
				section.getLocationMask(), 1, section.getDescription());
	}

	if (bTreeHelper.CloseBTree() < 0) {
		AfxMessageBox("Warning.  Unable to close binary tree.");
		return -1;
	}

	return 0;

}


int CElementMaintenanceHelper::UpdateLocationsForAisleChange(int sectionOffset, int aisleOffset, qqhSLOTAisle &aisle)
{

	CArray<int, int> locOffsetList;
	qqhSLOTSection section;
	int i;


	if (bTreeHelper.GetBtSection(sectionOffset, section) < 0) {
		AfxMessageBox("Unable to get section from btree.");
		return -1;
	}

	// get the list of location offsets for the section offset
	changesTree.getLocationOffsetByAisle(aisleOffset, locOffsetList);
	
	if (bTreeHelper.OpenBTree() < 0) {
		AfxMessageBox("Error updating locations.  Unable to open binary tree.");
		return -1;
	}

	for (i=0; i < locOffsetList.GetSize(); ++i) {
		numberingService.UpdateLocationDescriptionPart(locOffsetList[i],
				section.getLocationMask(), 2, aisle.getDescription());
	}

	if (bTreeHelper.CloseBTree() < 0) {
		AfxMessageBox("Warning.  Unable to close binary tree.");
		return -1;
	}

	return 0;

}

int CElementMaintenanceHelper::UpdateLocationsForBayChange(int sectionOffset, int bayOffset, qqhSLOTBay &bay)
{

	CArray<int, int> locOffsetList;
	qqhSLOTSection section;
	int i;

	if (bTreeHelper.GetBtSection(sectionOffset, section) < 0) {
		AfxMessageBox("Unable to get section from btree.");
		return -1;
	}

	// get the list of location offsets for the section offset
	changesTree.getLocationOffsetByBay(bayOffset, locOffsetList);
	
	if (bTreeHelper.OpenBTree() < 0) {
		AfxMessageBox("Error updating locations.  Unable to open binary tree.");
		return -1;
	}

	for (i=0; i < locOffsetList.GetSize(); ++i) {
		numberingService.UpdateLocationDescriptionPart(locOffsetList[i],
				section.getLocationMask(), 3, bay.getDescription());
	}

	if (bTreeHelper.CloseBTree() < 0) {
		AfxMessageBox("Warning.  Unable to close binary tree.");
		return -1;
	}

	return 0;

}

int CElementMaintenanceHelper::UpdateLocationsForLevelChange(int sectionOffset, int levelOffset, qqhSLOTLevel &level)
{

	CArray<int, int> locOffsetList;
	qqhSLOTSection section;
	int i;

	if (bTreeHelper.GetBtSection(sectionOffset, section) < 0) {
		AfxMessageBox("Unable to get section from btree.");
		return -1;
	}

	// get the list of location offsets for the level offset
	changesTree.getLocationOffsetByLevel(levelOffset, locOffsetList);
	
	if (bTreeHelper.OpenBTree() < 0) {
		AfxMessageBox("Error updating locations.  Unable to open binary tree.");
		return -1;
	}

	for (i=0; i < locOffsetList.GetSize(); ++i) {
		numberingService.UpdateLocationDescriptionPart(locOffsetList[i],
				section.getLocationMask(), 4, level.getDescription());
	}

	if (bTreeHelper.CloseBTree() < 0) {
		AfxMessageBox("Warning.  Unable to close binary tree.");
		return -1;
	}

	return 0;

}

int CElementMaintenanceHelper::BuildLevelLocationList(TreeElement *bayPtr, CLevelLocationMaint &levelLocDlg)
{
	TreeElement *locPtr, *levelPtr, *aislePtr, *sectionPtr;
	qqhSLOTLevel level;
	qqhSLOTLocation location;
	qqhSLOTBay bay;
	qqhSLOTAisle aisle;
	qqhSLOTSection section;
	CString handle;

	bTreeHelper.GetBtBay(bayPtr->fileOffset, bay);

	handle = bayPtr->acadHandle;
	aislePtr = changesTree.getAisleByBayHandle(handle);
	bTreeHelper.GetBtAisle(aislePtr->fileOffset, aisle);

	sectionPtr = changesTree.getSectionByBayHandle(handle);
	bTreeHelper.GetBtSection(sectionPtr->fileOffset, section);

	int i, j;
	CString temp;

	for (i=0; i < bayPtr->treeChildren.GetSize(); ++i) {
		levelPtr = &(bayPtr->treeChildren[i]);
		if (bTreeHelper.GetBtLevel(levelPtr->fileOffset, level) < 0) {
			AfxMessageBox("Unable to read level.");
			return -1;
		}

		for (j=0; j < levelPtr->treeChildren.GetSize(); ++j) {
			locPtr = &(levelPtr->treeChildren[j]);

			if (bTreeHelper.OpenBTree() < 0) {
				AfxMessageBox("Error updating locations.  Unable to open binary tree.");
				return -1;
			}
			
			// Need to add some logic here to only do this if descriptions have changed
			numberingService.UpdateLocationDescriptionPart(locPtr->fileOffset,
				section.getLocationMask(), 1, section.getDescription());
			numberingService.UpdateLocationDescriptionPart(locPtr->fileOffset,
				section.getLocationMask(), 2, aisle.getDescription());
			numberingService.UpdateLocationDescriptionPart(locPtr->fileOffset,
				section.getLocationMask(), 3, bay.getDescription());
			numberingService.UpdateLocationDescriptionPart(locPtr->fileOffset,
				section.getLocationMask(), 4, level.getDescription());
			
			if (bTreeHelper.CloseBTree() < 0) {
				AfxMessageBox("Warning.  Unable to close binary tree.");
				return -1;
			}

// brd - check this for error
			if (bTreeHelper.GetBtLocation(locPtr->fileOffset, location) < 0) {
				AfxMessageBox("Unable to read location.");
				return -1;
			}

			temp.Format("%s|%d|%s|%d|", 
				level.getDescription(),
				levelPtr->fileOffset,
				location.getDescription(),
				locPtr->fileOffset);
			
			levelLocDlg.m_levelLocationList.Add(temp);
		}
	}

	return 0;

}

int CElementMaintenanceHelper::AddNewSection(qqhSLOTSection& newSection)
{
	CFacilityElementSheet newSectionSheet;
	CNewSectionPage1 newSectionPage1;
	CNewSectionPage2 newSectionPage2;
	
	// todo: add defaults for section labor values
	CStringArray keys, values, defaultValues;
	keys.Add("OrderCount");
	keys.Add("AverageOrderQuantity");
	keys.Add("ContainerQuantity");
	keys.Add("ApplyBrokenOrder");
	keys.Add("ForkTravelTime");
	keys.Add("ForkFixedPerTrip");
	keys.Add("ForkLaborRate");
	keys.Add("ReplenishAvgDistance");
	keys.Add("ForkInsertionTime");
	keys.Add("ForkPickupTime");
	keys.Add("PalletsPerPutawayTrip");
	keys.Add("SelectionTravelTime");
	keys.Add("SelectFixedPerTrip");
	keys.Add("SelectionLaborRate");
	keys.Add("AverageCubePerTrip");
	keys.Add("StockerTravelTime");
	keys.Add("StockerFixedPerTrip");
	keys.Add("StockerLaborRate");

	defaultValues.Add("250");
	defaultValues.Add("100");
	defaultValues.Add("55");
	defaultValues.Add("1");
	defaultValues.Add(".0023");
	defaultValues.Add(".0671");
	defaultValues.Add("20.75");
	defaultValues.Add("100");
	defaultValues.Add(".6637");
	defaultValues.Add(".2419");
	defaultValues.Add("1.7");
	defaultValues.Add(".0029");
	defaultValues.Add(".0659");
	defaultValues.Add("17.5");
	defaultValues.Add("0");
	defaultValues.Add(".0029");
	defaultValues.Add(".0782");
	defaultValues.Add("17.5");

	controlService.GetApplicationData(keys, values, "Dialogs\\SectionProperties");
	
	CStringArray *pValues;
	if (values.GetSize() < keys.GetSize() || (values.GetSize() > 0 && values[0] == ""))
		pValues = &defaultValues;
	else
		pValues = &values;

	newSectionPage1.m_NewSection_OrderCount = atoi(pValues->GetAt(0));
	newSectionPage1.m_NewSection_AvgOrdQty = atoi(pValues->GetAt(1));
	newSectionPage1.m_NewSection_ContainerCount = atoi(pValues->GetAt(2));
	newSectionPage1.m_NewSection_ApplyBrokenOrder = atoi(pValues->GetAt(3));
	
	newSectionPage2.m_NewSection_ForkDistFixed = atof(pValues->GetAt(4));
	newSectionPage2.m_NewSection_ForkDistVar = atof(pValues->GetAt(5));
	newSectionPage2.m_NewSection_ForkLaborRate = atof(pValues->GetAt(6));
	newSectionPage2.m_NewSection_ReplAvgDist = atof(pValues->GetAt(7));
	newSectionPage2.m_NewSection_ForkInsertion = atof(pValues->GetAt(8));
	newSectionPage2.m_NewSection_ForkPickup = atof(pValues->GetAt(9));
	newSectionPage2.m_NewSection_PalletsPerTrip = atof(pValues->GetAt(10));
	
	newSectionPage2.m_NewSection_SelFixedFactor = atof(pValues->GetAt(11));
	newSectionPage2.m_NewSection_SelVarFactor = atof(pValues->GetAt(12));
	newSectionPage2.m_NewSection_SelLaborRate = atof(pValues->GetAt(13));
	newSectionPage2.m_NewSection_AvgCubePerTrip = atof(pValues->GetAt(14));
	newSectionPage2.m_NewSection_StockerFixed = atof(pValues->GetAt(15));
	newSectionPage2.m_NewSection_StockerVar = atof(pValues->GetAt(16));
	newSectionPage2.m_NewSection_StockerLaborRate = atof(pValues->GetAt(17));
	
	
	newSectionSheet.m_psh.dwFlags &= ~(PSH_HASHELP);
	newSectionSheet.m_psh.dwFlags |= PSH_NOAPPLYNOW;
	newSectionSheet.SetTitle("New Section Properties");
	newSectionSheet.AddPage(&newSectionPage1);
	newSectionSheet.AddPage(&newSectionPage2);
	
	if ( newSectionSheet.DoModal() == IDCANCEL )
		return -1;
	
	newSection.setLocationMask(newSectionPage1.m_NewSection_LocationMask);
	newSection.setDescription(numberingService.FindNextID(newSectionPage1.m_NewSection_Description,1,0,1,newSectionPage1.m_NewSection_LocationMask,3));
	
	if (newSectionPage1.m_NewSection_ApplyBrokenOrder == TRUE) {
		CString trueStr = "TRUE";
		newSection.setApplyBrokenOrder(trueStr);
	}
	else {
		CString falseStr = "FALSE";
		newSection.setApplyBrokenOrder(falseStr);
	}
	newSection.setContQty(newSectionPage1.m_NewSection_ContainerCount);
	newSection.setAvgOrdQty(newSectionPage1.m_NewSection_AvgOrdQty);
	newSection.setOrderCount(newSectionPage1.m_NewSection_OrderCount);
	newSection.setSelDist(0.0);
	
	newSection.setForkDistFixed(newSectionPage2.m_NewSection_ForkDistFixed);
	newSection.setForkDistVar(newSectionPage2.m_NewSection_ForkDistVar);
	newSection.setForkLaborRate(newSectionPage2.m_NewSection_ForkLaborRate);
	newSection.setReplenAvgDist(newSectionPage2.m_NewSection_ReplAvgDist);
	newSection.setInsertForkTravel(newSectionPage2.m_NewSection_ForkInsertion);
	newSection.setPickupForkTravel(newSectionPage2.m_NewSection_ForkPickup);
	newSection.setPalletsPerPtwyTrip(newSectionPage2.m_NewSection_PalletsPerTrip);
	
	newSection.setSelectDistFixed(newSectionPage2.m_NewSection_SelFixedFactor);
	newSection.setSelectDistVar(newSectionPage2.m_NewSection_SelVarFactor);
	newSection.setSelectLaborRate(newSectionPage2.m_NewSection_SelLaborRate);
	newSection.setAvgCubePerTrip(newSectionPage2.m_NewSection_AvgCubePerTrip);
	newSection.setStockerDistFixed(newSectionPage2.m_NewSection_StockerFixed);
	newSection.setStockerDistVar(newSectionPage2.m_NewSection_StockerVar);
	newSection.setStockerLaborRate(newSectionPage2.m_NewSection_StockerLaborRate);
	
	values.RemoveAll();
	values.SetSize(18);
	values[0].Format("%d", newSectionPage1.m_NewSection_OrderCount);
	values[1].Format("%d", newSectionPage1.m_NewSection_AvgOrdQty);
	values[2].Format("%d", newSectionPage1.m_NewSection_ContainerCount);
	values[3].Format("%d", newSectionPage1.m_NewSection_ApplyBrokenOrder);
	
	values[4].Format("%f", newSectionPage2.m_NewSection_ForkDistFixed);
	values[5].Format("%f", newSectionPage2.m_NewSection_ForkDistVar);
	values[6].Format("%f", newSectionPage2.m_NewSection_ForkLaborRate);
	values[7].Format("%f", newSectionPage2.m_NewSection_ReplAvgDist);
	values[8].Format("%f", newSectionPage2.m_NewSection_ForkInsertion);
	values[9].Format("%f", newSectionPage2.m_NewSection_ForkPickup);
	values[10].Format("%f", newSectionPage2.m_NewSection_PalletsPerTrip);
	values[11].Format("%f", newSectionPage2.m_NewSection_SelFixedFactor);
	values[12].Format("%f", newSectionPage2.m_NewSection_SelVarFactor);
	values[13].Format("%f", newSectionPage2.m_NewSection_SelLaborRate);
	values[14].Format("%f", newSectionPage2.m_NewSection_AvgCubePerTrip);
	values[15].Format("%f", newSectionPage2.m_NewSection_StockerFixed);
	values[16].Format("%f", newSectionPage2.m_NewSection_StockerVar);
	values[17].Format("%f", newSectionPage2.m_NewSection_StockerLaborRate);
	
	controlService.SetApplicationData(keys, values, "Dialogs\\SectionProperties");
	return 0;
}

int CElementMaintenanceHelper::AddBayObject(CBayProfile &bayProfile, double rotation, 
										   const C3DPoint &centerPoint, CString &handle, 
										   double leftUprightWidth, double rightUprightWidth)
{
	Acad::ErrorStatus es;

	AcDbBlockTable *pBlockTable;
	
	es = acdbCurDwg()->getBlockTable(pBlockTable, AcDb::kForRead);
	if (es != Acad::eOk) {
		controlService.Log("", "Error(%d) during getBlockTable", es);
		return -1;
	}

	AcDbBlockTableRecord *pModelSpace;
	es = pBlockTable->getAt(ACDB_MODEL_SPACE, pModelSpace, AcDb::kForWrite);
	if (es != Acad::eOk) {
		pBlockTable->close();
		controlService.Log("", "Error(%d) getting model space block table record.\n", es);
		return -1;
	}

	CString blockName;
	AcDbObjectId blockId;

	if (rightUprightWidth == 0)
		blockName.Format("%d-%s", bayProfile.m_BayProfileDBId, bayProfile.ConvertBayTypeToPath());
	else
		blockName.Format("%d-%s-End", bayProfile.m_BayProfileDBId, bayProfile.ConvertBayTypeToPath());

	es = pBlockTable->getAt(blockName, blockId);
	pBlockTable->close();
	if (es != Acad::eOk) {		// must be the first time we've inserted this type of bay
		blockId = bayProfile.CreateAsBlock(blockName, leftUprightWidth, rightUprightWidth);
		if (blockId == NULL) {
			pModelSpace->close();
			controlService.Log("", "Error adding bay to drawing.", "Error creating bay object in AddBayObject.");
			return -1;
		}
	}

	// Now we have the bay block that exists in the drawing

	AcDbBlockReference *pBlockReference = new AcDbBlockReference;

	es = pBlockReference->setBlockTableRecord(blockId);
	if (es !=  Acad::eOk) {
		delete pBlockReference;
		pModelSpace->close();
		controlService.Log("", "Error(%d) setting block reference id.\n", es);
		return -1;
	}

	AcGePoint3d pt;
	pt[X] = centerPoint.m_X;
	pt[Y] = centerPoint.m_Y;
	pt[Z] = centerPoint.m_Z;
	
	pBlockReference->setPosition(pt);
	pBlockReference->setRotation(PI/180 * rotation);

	CAutoCADCommands acCmds;
	CString nextLayer;
	if (acCmds.CreateNextLayer(nextLayer, "BAY") < 0) {
		delete pBlockReference;
		pModelSpace->close();
		controlService.Log("", "Error creating new bay layer.\n");
		return -1;
	}

	es = pBlockReference->setLayer(nextLayer, Adesk::kTrue);
	if ( es != Acad::eOk ) {
		delete pBlockReference;
		pModelSpace->close();
		controlService.Log("", "Error(%d) setting layer for block reference.", es);
		return -1;
	}
	
	AcDbObjectId referenceId;
	es = pModelSpace->appendAcDbEntity(referenceId, pBlockReference);
	if (es !=  Acad::eOk) {
		delete pBlockReference;
		pModelSpace->close();
		controlService.Log("", "Error(%d) appending block reference.\n", es);
		return -1;
	}
	
	BayNum++;

	AcDbHandle blockHandle;
	pBlockReference->getAcDbHandle(blockHandle);

	pBlockReference->close();
	pModelSpace->close();

	//AcDbHandle blockHandle = referenceId.getAcDbHandle();
	char buf[17];
	blockHandle.getIntoAsciiBuffer(buf);

	handle = buf;

	return 0;

}


int CElementMaintenanceHelper::AddBay(CBayProfile &bayProfile, const C3DPoint &centerPoint, double rotation, qqhSLOTBay &bay,
									double leftUprightWidth, double rightUprightWidth, BOOL isRotated)
{
	
	// Create bay, levels, locations from bay profile
	// Set coordinates
	// Add bay to drawing

	// Older profiles may not have the coordinates set correctly so reset them
	bayProfile.ResetLocationSizes();

	bay.setBayProfileId(bayProfile.m_BayProfileDBId);
	bay.pBayProfile = new CBayProfile(bayProfile);

	if (AddBayDataFromProfile(bay, bayProfile) < 0) {
		controlService.Log("", "Error in AddBayDataFromProfile\n");
		return -1;
	}


	// Set the level and location coordinates

	// Use the location coordinates from the location profile
	// Offset the location x by the left upright width
	// Offset the bay center back to the origin
	// Rotate 
	// Add back the bay point as the offset to the final coordinate

	for (int i=0; i < bay.getChildList().GetSize(); ++i) {
		qqhSLOTLevel &level = bay.getChildList()[i];

		C3DPoint levelPt( (bayProfile.m_Width/2+leftUprightWidth), 0, level.getCoord().getZ());
		if (isRotated)
			levelPt.m_Y += bayProfile.m_Depth;

		levelPt.m_X -= (bayProfile.m_Width/2 + leftUprightWidth);
		levelPt.m_Y -= (bayProfile.m_Depth/2);

		if (isRotated)
			levelPt.Rotate(rotation-180);
		else
			levelPt.Rotate(rotation);
		levelPt.m_X += centerPoint.m_X;
		levelPt.m_Y += centerPoint.m_Y;
		levelPt.m_Z += (centerPoint.m_Z - bayProfile.m_UprightHeight/2);

		level.getCoord().setX(levelPt.m_X);
		level.getCoord().setY(levelPt.m_Y);
		level.getCoord().setZ(levelPt.m_Z);


		for (int j=0; j < level.getChildList().GetSize(); ++j) {
			qqhSLOTLocation &location = level.getChildList()[j];

			C3DPoint pt(location.getCoord().getX(), location.getCoord().getY(), location.getCoord().getZ());

			// the coordinates are already set from the profile but we do need to offset the 
			// x coordinate by the bay upright width
			pt.m_X += leftUprightWidth;
			
			if (isRotated) 
				pt.m_Y += bayProfile.m_Depth;

			// Offset the bay so that the center is in the origin (starting from the front left corner being at the origin)
			pt.m_X -= (bayProfile.m_Width/2 + leftUprightWidth);
			pt.m_Y -= (bayProfile.m_Depth/2);
			pt.m_Z -= (centerPoint.m_Z - bayProfile.m_UprightHeight/2);


			// The location profile sets the x coordinate to the corner of the bay - we want the center
			pt.m_X += (location.getWidth()/2);

			C3DPoint backfillPt(pt);
			if (isRotated)
				backfillPt.m_Y -= bayProfile.m_Depth;
			else
				backfillPt.m_Y += bayProfile.m_Depth;
			backfillPt.m_Z += level.pLevelProfile->m_FlowDifference; //bayProfile.m_FlowDifference;

			// Rotate the bay - assume the rotation coming in is the actual rotation of the specific bay
			// so if it is on the right side of the aisle it is 180 degrees more than the left side
			if (isRotated)
				pt.Rotate(rotation-180);
			else
				pt.Rotate(rotation);

			if (isRotated)
				backfillPt.Rotate(rotation-180);
			else
				backfillPt.Rotate(rotation);

			// Add back the offset to the center of the bay
			pt.m_X += centerPoint.m_X;
			pt.m_Y += centerPoint.m_Y;
			pt.m_Z += (centerPoint.m_Z - bayProfile.m_UprightHeight/2);

			backfillPt.m_X += centerPoint.m_X;
			backfillPt.m_Y += centerPoint.m_Y;
			backfillPt.m_Z += (centerPoint.m_Z - bayProfile.m_UprightHeight/2);

			location.getCoord().setX(pt.m_X+.5);
			location.getCoord().setY(pt.m_Y+.5);
			location.getCoord().setZ(pt.m_Z+.5);
		

			location.getBackfillCoordinates().setX(backfillPt.m_X+.5);
			location.getBackfillCoordinates().setY(backfillPt.m_Y+.5);
			location.getBackfillCoordinates().setZ(backfillPt.m_Z+.5);

		}
	}

	CString handle;
	if (AddBayObject(bayProfile, rotation, centerPoint, handle, leftUprightWidth, rightUprightWidth) < 0) {
		controlService.Log("", "Error in AddBayObject.\n");
		return -1;
	}

	bay.setAcadHandle(handle);

	return 0;

}

void CElementMaintenanceHelper::ChangeBayProfile()
{
	CStringArray handles;
	CChangeRackType changeRackDlg;
	qqhSLOTBay bay;
	CMap<int, int, CBayProfile, CBayProfile&> map;

	if (CAutoCADCommands::GetSelectedHandles(handles) < 1) {
		do {
			if (AfxMessageBox("Please select one or more bays to change.", MB_OKCANCEL) == IDCANCEL)
				return;
		} while (CAutoCADCommands::GetSelectedHandlesPrompt(handles) < 1);
	}

	if ((changeRackDlg.DoModal()) == IDCANCEL)
		return;


	CWaitCursor cwc;

	CBayProfile bayProfile;
	try {
		bayProfileDataService.GetBayProfile(changeRackDlg.m_SelectedBayProfileId, bayProfile, CBayProfile::loadLevels|CBayProfile::loadLocations);
	}
	catch (...) {
		controlService.Log("Error retrieving bay profile from database.", "Generic exception in GetBayProfile for %d.\n",
			changeRackDlg.m_SelectedBayProfileId);
		return;
	}

	CBayProfile *pProfile = new CBayProfile(bayProfile);

	map.SetAt(bayProfile.m_BayProfileDBId, *pProfile);

	int notFoundCount = 0, changedCount = 0, errorCount = 0, skipCount = 0;
	CString progressMsg;
	CProgressMessage progressDlg(progressMsg, 1, handles.GetSize(), 1, utilityHelper.GetParentWindow());


	for (int i=0; i < handles.GetSize(); ++i) {
	
		progressMsg.Format("Changing bay %d of %d", i+1, handles.GetSize());
		progressDlg.UpdateMessage(progressMsg);

		TreeElement *bayPtr = changesTree.getBayByHandle(handles[i]);
		if (bayPtr == NULL) {
			notFoundCount++;
			continue;
		}

		bTreeHelper.GetBtBay(bayPtr->fileOffset, bay);
		if (bayPtr->elementDBID > 0) {	
			if (facilityDataService.IntegratedBayHasAssignments(bayPtr->elementDBID)) {
				CString temp;
				temp.Format("Bay %s has product assignments.  In a facility that is integrated\n"
					"only bays with no product assignments may be changed.\n"
					"To change the bay, set it to in-active, run Assign Products so that no\n"
					"products will be assigned to the bay, and then change it.", bay.getDescription());
				AfxMessageBox(temp);
				skipCount++;
				progressDlg.Step();
				continue;
			}
		}
		
		// All this is just to avoid having to get each profile from the database
		CBayProfile oldProfile;
		if (bayProfile.m_BayProfileDBId != bay.getBayProfileId()) {
			if (! map.Lookup(bay.getBayProfileId(), oldProfile)) {
				if (bay.pBayProfile != NULL)
					oldProfile = *bay.pBayProfile;
				else {
					try {
						bayProfileDataService.GetBayProfile(bay.getBayProfileId(), oldProfile);
					}
					catch (...) {
						controlService.Log("Error retrieving bay profile from database.", 
							"Generic error in GetBayProfile for %d.\n", bay.getBayProfileId());
						errorCount++;
						continue;
					}
				}
				map.SetAt(oldProfile.m_BayProfileDBId, oldProfile);
			}
		}
		else 
			oldProfile = bayProfile;

		AcGePoint3d centerPoint;
		double rotation;
		CAutoCADCommands::GetDrawingObjectCoordinates(handles[i], centerPoint, rotation);

		C3DPoint bayCenter(centerPoint[X], centerPoint[Y], centerPoint[Z]);
		// Warn the user if the width is different
		if ((bayProfile.m_Width + bayProfile.m_UprightWidth) > (oldProfile.m_Width + oldProfile.m_UprightWidth)) {
			CString temp;
			temp.Format("The bay width (%.0f) or upright width (%.0f) of the original bay\n"
				"is greater than the width (%.0f) or upright width (%.0f) of the new bay.\n"
				"This may cause the bay to overlap with an adjacent bay.\n"
				"Choose Yes to change this bay, No to skip it, or Cancel to abort.",
				oldProfile.m_Width, oldProfile.m_UprightWidth, bayProfile.m_Width, bayProfile.m_UprightWidth);
			int rc = AfxMessageBox(temp, MB_YESNOCANCEL);
			switch (rc) {
			case IDCANCEL:
				skipCount += handles.GetSize()-i+1;
				break;
			case IDNO:
				skipCount++;
				progressDlg.Step();
				continue;
			}
		}

		// If the depth of the new bay profile is not the same as the old
		// we have to offset the center point + or - to make the front of the
		// new bay line up with the rest of the bays in the aisle
		if (bayProfile.m_Depth != oldProfile.m_Depth) {
			bayCenter.Rotate(-utilityHelper.ConvertRadiansToDegrees(rotation));
			bayCenter.m_Y += ( (bayProfile.m_Depth - oldProfile.m_Depth)/2 );
			bayCenter.Rotate(utilityHelper.ConvertRadiansToDegrees(rotation));
		}

		CAutoCADCommands::DeleteDrawingObjectByHandle(handles[i]);

		qqhSLOTBay newBay;
		qqhSLOTSide side;
		bTreeHelper.GetBtSide(bayPtr->treeParent->fileOffset, side);

		// bay x coordinate is the center of the bay rotated at 0
		newBay.getCoord().setX(bay.getCoord().getX());

		// rotation is in radians so convert it to degrees
		if (AddBay(bayProfile, bayCenter, utilityHelper.ConvertRadiansToDegrees(rotation), 
			newBay, bayProfile.m_UprightWidth, 
			bayProfile.m_UprightWidth, side.getIsRotated()) < 0) {
			controlService.Log("Error adding bay to drawing", "Error in AddBay\n");
			errorCount++;
			progressDlg.Step();
			continue;
		}

		
		newBay.setDescription(bay.getDescription());
		strcpy(bayPtr->acadHandle, newBay.getAcadHandle());

		// This will actually remove the element from the local btree
		for (int j=0; j < bayPtr->treeParent->treeChildren.GetSize(); ++j) {
			if (bayPtr->treeParent->treeChildren[j].fileOffset == bayPtr->fileOffset) {
				bayPtr->treeParent->treeChildren.RemoveAt(j);
				break;
			}
		}

		if (bay.getDBID() > 0)
			changesTree.DeletedBayMap.SetAt(bay.getDBID(), bay.getDBID());

		try {
			if (bTreeHelper.AddBaytoFacility(newBay, bayPtr->treeParent->fileOffset, changesTree, 0, 0, 0, 0) < 0) {
				controlService.Log("Error adding new bay to facility.", "Generic exception in AddBayToFacility.\n");
				errorCount++;
				progressDlg.Step();
				continue;
			}
		}
		catch (...) {
			controlService.Log("Error adding new bay to facility.", "Generic exception in AddBayToFacility.\n");
			errorCount++;
			progressDlg.Step();
			continue;
		}
	
	
		changedCount++;
		progressDlg.Step();
	}

	progressDlg.Hide();

	CString temp;
	if (handles.GetSize() == 1) {
		if (notFoundCount > 0)
			temp.Format("The bay could not be found.");
		else if (errorCount > 0)
			temp.Format("An error occurred while changing the bay profile.");
		else if (skipCount > 0)
			temp.Format("The bay was not changed.");
		else
			temp.Format("The bay was successfully changed.");
	}
	else {
		int sz = handles.GetSize();
		temp.Format("Completed Changing Bay Profiles:\nItems selected: %d\n"
			"Bays changed: %d\nBays not found: %d\nBays Skipped: %d\nErrors: %d\n\n"
			"Please recreate the pick path for this aisle if one exists.",
			sz, changedCount, notFoundCount, skipCount, errorCount);
	}
	
	numItemsProcessed += (errorCount + changedCount);
	
	AfxMessageBox(temp);

	return;

}

int CElementMaintenanceHelper::RedrawPickPathWithoutConnection(CString &handle,int connectionAtEnd)
{
	AcDb3dPolyline *pLine;
	Acad::ErrorStatus es;
	AcDbObjectId objId;
	AcDbHandle objHandle;
	AcDbEntity *pEntity;
	int newPathLength;

	objHandle = handle;

	es = acdbCurDwg()->getAcDbObjectId(objId, FALSE, objHandle);
	if (es != Acad::eOk) {
		CString temp;
		temp.Format("Error (%d) finding connected pick path with handle %s\n", es, handle);
		controlService.Log("", temp);
		return -1;
	}

	es = acdbOpenAcDbEntity(pEntity, objId, AcDb::kForWrite);
	if (es != Acad::eOk) {
		CString temp;
		temp.Format("Error (%d) opening connected pick path with handle %s\n", es, handle);
		controlService.Log("", temp);
		return -1;
	}


	pLine = (AcDb3dPolyline *)(pEntity);

	//////////////////////////////////////////////////////////////////////
	// Run through all the points in the polyline
	//////////////////////////////////////////////////////////////////////
	AcGePoint3dArray vertexArray;
	AcDbObjectIterator *pIterator = pLine->vertexIterator();
	AcDbObjectId vertexId;
	AcDb3dPolylineVertex *pVertex;
	
	pLine->close();

	if (connectionAtEnd) {
		// I want to delete the last point in the polyline.  I really wanted to use a backwards iterator
		// but it kep saying it was done immediately after I created it so instead I count the vertices
		// first and then use a 2nd iterator to delete the last one
		pIterator->start();
		int count = 0;
		
		while ( ! pIterator->done() ) {
			
			vertexId = pIterator->objectId();
			
			es = acdbOpenObject(pVertex, vertexId, AcDb::kForRead);
			if (es == Acad::eWasErased) {
				pIterator->step();
				continue;
			}
			vertexArray.append(pVertex->position());
			
			pIterator->step();
			
			pVertex->close();
			
			count++;
			
		}
		

		pIterator->start();

		int idx = 1;
		while ( ! pIterator->done() ) {
			
			vertexId = pIterator->objectId(); 
			es = acdbOpenObject(pVertex, vertexId, AcDb::kForWrite);
			if (es == Acad::eWasErased) {
				pIterator->step();
				continue;
			}

			pIterator->step();
			
			if (idx > count-1)
				pVertex->erase();

			pVertex->close();
			
			idx++;			
		}

		delete pIterator;
		
	}
	else {
		pIterator->start();
		
		int count = 0;
		
		while ( ! pIterator->done() ) {
			
			vertexId = pIterator->objectId();
			
			acdbOpenObject(pVertex, vertexId, AcDb::kForWrite);
			if (es == Acad::eWasErased) {
				pIterator->step();
				continue;
			}

			vertexArray.append(pVertex->position());

			pIterator->step();
		
			if (count < 1)
				pVertex->erase();

			pVertex->close();

			count++;
			
		}
	
		delete pIterator;
	}



	vertexArray.removeLast();
	vertexArray.removeLast();

	newPathLength = vertexArray.physicalLength();

	return newPathLength;
	
}

BOOL CElementMaintenanceHelper::PickPathOwnsPreviousConnectionLine(const CString &handle, double rotation)
{
	AcDb3dPolyline *pLine;
	Acad::ErrorStatus es;
	AcDbObjectId objId;
	AcDbHandle objHandle;
	AcDbEntity *pEntity;

	objHandle = handle;

	es = acdbCurDwg()->getAcDbObjectId(objId, FALSE, objHandle);
	if (es != Acad::eOk) {
		CString temp;
		temp.Format("Error (%d) finding connected pick path with handle %s\n", es, handle);
		controlService.Log("", temp);
		return -1;
	}

	es = acdbOpenAcDbEntity(pEntity, objId, AcDb::kForWrite);
	if (es != Acad::eOk) {
		CString temp;
		temp.Format("Error (%d) opening connected pick path with handle %s\n", es, handle);
		controlService.Log("", temp);
		return -1;
	}


	pLine = (AcDb3dPolyline *)(pEntity);

	//////////////////////////////////////////////////////////////////////
	// Run through all the points in the polyline
	//////////////////////////////////////////////////////////////////////
	AcGePoint3dArray vertexArray;
	AcDbObjectIterator *pIterator = pLine->vertexIterator();
	AcDbObjectId vertexId;
	AcDb3dPolylineVertex *pVertex;
	
	pIterator->start();
	int count = 0;

	while ( pIterator->done() != Adesk::kTrue ) {
		vertexId = pIterator->objectId();
		 
		es = acdbOpenObject(pVertex, vertexId, AcDb::kForRead);
		if (es == Acad::eWasErased) {
			pIterator->step();
			continue;
		}

		vertexArray.append(pVertex->position());

		pVertex->close();

		pIterator->step();
	}

	delete pIterator;
	pLine->close();

	// This is kind of hokey but we have to do it because unfortunately the connection line may be owned
	// by either the first or second connected pick path

	// We know that a pick path (except for single-bay aisles) has a triangle at the beginning with the
	// first two points being in the center of the aisle and 1st, 3rd, and 4th points perpendicular to the
	// aisle.  So if that relationship is not true, there must be a connection line at the beginning of the path

	if (vertexArray.logicalLength() < 4)
		return TRUE;		// must be a one-bay aisle; currently can't tell if it owns the connection line or not

	C3DPoint pt1(vertexArray[0][X], vertexArray[0][Y], vertexArray[0][Z]);
	C3DPoint pt2(vertexArray[1][X], vertexArray[1][Y], vertexArray[1][Z]);
	C3DPoint pt3(vertexArray[2][X], vertexArray[2][Y], vertexArray[2][Z]);
	C3DPoint pt4(vertexArray[3][X], vertexArray[3][Y], vertexArray[3][Z]);

	pt1.Rotate(-rotation);
	pt2.Rotate(-rotation);
	pt3.Rotate(-rotation);
	pt4.Rotate(-rotation);

	if ( abs(pt1.m_Y - pt2.m_Y) < .1 && abs(pt1.m_X - pt3.m_X) < .1 && abs(pt3.m_X - pt4.m_X) < .1)
		return FALSE;

	return TRUE;

}

int CElementMaintenanceHelper::DeletePickPathConnections(qqhSLOTSection &section, qqhSLOTAisle &aisle)
{
	// Get the aisle that this pick path is connected to (i.e. that connects to the beginning of this pick path)
	// The connected handle of the selected pick path points to the pick path that precedes it
	if (strcmp(aisle.getPickPath().getConAcadHandle(), "XXX") != 0) {
		
		TreeElement *prevAislePtr = changesTree.getAisleByPickPathHandle(aisle.getPickPath().getConAcadHandle());
		if (prevAislePtr != NULL) {
			qqhSLOTAisle prevAisle;
			bTreeHelper.GetBtAisle(prevAislePtr->fileOffset, prevAisle);
			
			// See if the connected handle is in the list of ones to delete; if
			// it is, we are going to delete it anyway so don't bother removing the connection line
			/*
			BOOL found = FALSE;
			for (int j=0; j < handles.GetSize(); ++j) {
				if (strcmp(handles[j], prevAisle.getPickPath().getAcadHandle()) == 0) {
					found = TRUE;
					break;
				}
			}
			*/
			
			// If the selected aisle does not own the pick path connection, then the previous aisle
			// must so delete it from the previous aisle
			if (! PickPathOwnsPreviousConnectionLine(aisle.getPickPath().getAcadHandle(), aisle.getRotation())) {
				CString handle = prevAisle.getPickPath().getAcadHandle();
				int oldLength = prevAisle.getPickPath().getPathLength();
				int newLength = RedrawPickPathWithoutConnection(handle, 1 /* end */);
				
				if (newLength > 0) {
					prevAisle.getPickPath().setPathLength(newLength);
					bTreeHelper.SetBtAisle(prevAislePtr->fileOffset, prevAisle);
					section.setSelDist(section.getSelDist() - oldLength + newLength);
				}
				
			}
		}
	}
	// Now get the aisle that this follows this pick path
	TreeElement *nextAislePtr = changesTree.getAisleByConnectedPickPathHandle(aisle.getPickPath().getAcadHandle());
	if (nextAislePtr != NULL) {
		qqhSLOTAisle nextAisle;
		bTreeHelper.GetBtAisle(nextAislePtr->fileOffset, nextAisle);
		
		// See if the connected handle is in the list of ones to delete; if
		// it is, we are going to delete it anyway so don't bother removing the connection line
		/*
		BOOL found = FALSE;
		for (int j=0; j < handles.GetSize(); ++j) {
			if (strcmp(handles[j], nextAisle.getPickPath().getAcadHandle()) == 0) {
				found = TRUE;
				break;
			}
		}
		*/
		
		// If the next aisle owns the connection delete it from the next aisle
		if (PickPathOwnsPreviousConnectionLine(nextAislePtr->acadHandle, nextAisle.getRotation())) {
			CString handle = nextAislePtr->acadHandle;
			int oldLength = nextAisle.getPickPath().getPathLength();
			int newLength = RedrawPickPathWithoutConnection(handle, 0 /* beginning */);
			
			if (newLength > 0) {
				nextAisle.getPickPath().setPathLength(newLength);
				section.setSelDist(section.getSelDist() - oldLength + newLength);
			}
		}
		nextAisle.getPickPath().setConAcadHandle("XXX");
		bTreeHelper.SetBtAisle(nextAislePtr->fileOffset, nextAisle);
	}

	return 0;
}

int CElementMaintenanceHelper::AddHotspotObject(C3DPoint &centerPoint, double diameter, int type, CString &handle)
{
	Acad::ErrorStatus es;

	AcDbBlockTable *pBlockTable;
	
	es = acdbCurDwg()->getBlockTable(pBlockTable, AcDb::kForWrite);
	if (es != Acad::eOk) {
		controlService.Log("", "Error(%d) during getBlockTable", es);
		return -1;
	}

	AcDbBlockTableRecord *pModelSpace;
	es = pBlockTable->getAt(ACDB_MODEL_SPACE, pModelSpace, AcDb::kForWrite);
	if (es != Acad::eOk) {
		pBlockTable->close();
		controlService.Log("", "Error(%d) getting model space block table record.\n", es);
		return -1;
	}

	CString blockName;
	AcDbObjectId blockId, hotspotId;

	blockName.Format("Hotspot-%d-%.0f", type, diameter);

	es = pBlockTable->getAt(blockName, blockId);
	
	if (es != Acad::eOk) {		// must be the first time we've inserted a hotspot
		
		AcDbBlockTableRecord *pBlockTableRecord = new AcDbBlockTableRecord;
		pBlockTable->add(blockId, pBlockTableRecord);
		pBlockTableRecord->setName(blockName);
		AcDb3dSolid *pHotspot = new AcDb3dSolid();

		if (type == CHotspot::hsSelection)	
			pHotspot->createTorus(diameter/4, diameter/4);
		else
			pHotspot->createSphere(diameter/2);

		es = pBlockTableRecord->appendAcDbEntity(hotspotId, pHotspot);
		if (es != Acad::eOk) {
			controlService.Log("", "Error (%d) appending hotspot block.\n", es);
			delete pHotspot;
			delete pBlockTableRecord;
			pModelSpace->close();
			pBlockTable->close();
			return -1;
		}
		
		pHotspot->close();
		pBlockTableRecord->close();
		
	}

	pBlockTable->close();

	// Now we have the bay block that exists in the drawing

	AcDbBlockReference *pBlockReference = new AcDbBlockReference;

	es = pBlockReference->setBlockTableRecord(blockId);
	if (es !=  Acad::eOk) {
		delete pBlockReference;
		pModelSpace->close();
		controlService.Log("", "Error(%d) setting block reference id.\n", es);
		return -1;
	}

	AcGePoint3d pt;
	pt[X] = centerPoint.m_X;
	pt[Y] = centerPoint.m_Y;
	pt[Z] = centerPoint.m_Z;
	
	pBlockReference->setPosition(pt);
	pBlockReference->setRotation(PI/180 * 90);

	CAutoCADCommands acCmds;
	CString nextLayer;
	if (acCmds.CreateNextLayer(nextLayer, "HOTSPOT") < 0) {
		delete pBlockReference;
		pModelSpace->close();
		controlService.Log("", "Error creating new hotspot layer.\n");
		return -1;
	}

	es = pBlockReference->setLayer(nextLayer, Adesk::kTrue);
	if ( es != Acad::eOk ) {
		delete pBlockReference;
		pModelSpace->close();
		controlService.Log("", "Error(%d) setting layer for block reference.", es);
		return -1;
	}
	
	AcDbObjectId referenceId;
	es = pModelSpace->appendAcDbEntity(referenceId, pBlockReference);
	if (es !=  Acad::eOk) {
		delete pBlockReference;
		pModelSpace->close();
		controlService.Log("", "Error(%d) appending block reference.\n", es);
		return -1;
	}
	

	AcDbHandle blockHandle;
	pBlockReference->getAcDbHandle(blockHandle);

	pBlockReference->close();
	pModelSpace->close();

	char buf[17];
	blockHandle.getIntoAsciiBuffer(buf);

	handle = buf;

	if (type == CHotspot::hsSelection)
		CAutoCADCommands::ColorDrawingObjectByHandle(handle, kRed);
	else
		CAutoCADCommands::ColorDrawingObjectByHandle(handle, kBlue);
	return 0;
}


void CElementMaintenanceHelper::ElementMaintenance()
{
	CString handle;	
	TreeElement *TEptr = NULL;
	TreeElement *bayPtr;
	qqhSLOTAisle tempAisle;
	qqhSLOTSection section;
	int a,b;
	CArray <qqhSLOTHotSpot, qqhSLOTHotSpot&> HotSpotList;
	qqhSLOTHotSpot HotSpot;
	qqhSLOTCoordinate m_Coord;
	
	// Check to make sure it's a valid handle they have selected
	if (CAutoCADCommands::GetSelectedHandle(handle) <= 0) {
		AfxMessageBox("Please Select an Object to get the Properties of!");
		return;
	}


	// Check to see if the handle selected is a Bay in the BTree, if so, call the Bay Dialog
	
	bayPtr = changesTree.getBayByHandle(handle);
	
	if (bayPtr != NULL) {
		CLevelLocationDialog dlg;
		
		dlg.m_Handle = handle;
		
		dlg.DoModal();
		
		return;
	}



	// Check to see if the handle selected is a PickPath, if so, call the PickPath Dialog


	TEptr = changesTree.getAisleByPickPathHandle(handle);
	
	if (TEptr != NULL) {
		bTreeHelper.GetBtAisle(TEptr->fileOffset, tempAisle);
		PickPathProperties();
		return;
	}



	// Check to see if the handle selected is a Hotspot, if so, call the HotSpot Dialog 

    // Add function to loop through all sections in the btree, check the hotspot handles for the hotspot
	// I am looking for, then return the section TreeElement pointer which contains this hotspot.
	for (a = 0; a < changesTree.treeChildren.GetSize(); a++)  {
		bTreeHelper.GetBtSection(changesTree.treeChildren[a].fileOffset, section);
		for (b = 0; b < section.getHotSpotList().GetSize(); b++)  {
			HotSpot = section.getHotSpotList()[b];
			if (HotSpot.getAcadHandle() == handle)  {
				CHotSpotPropertiesDialog dlg1;
				dlg1.m_section = section.getDescription();
				if (HotSpot.getHotSpotType() == CHotspot::hsPutaway)
					dlg1.m_Type = "Putaway";
				else if (HotSpot.getHotSpotType() == CHotspot::hsSelection)
					dlg1.m_Type = "Selection/Stocker";
				else 
					dlg1.m_Type = "Unknown HotSpot Type";
				dlg1.m_Coordinates.Format("%d, %d, %d", 
				HotSpot.getCoord().getX(),
				HotSpot.getCoord().getY(),
				HotSpot.getCoord().getZ());
				dlg1.DoModal();
				return;
			}
		}
	
	}

	AfxMessageBox("This is not a valid Optimize Object - You should delete it and re-add if you think it is a valid object.");


}

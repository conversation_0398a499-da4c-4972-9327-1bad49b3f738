#if !defined(AFX_VALIDATEFACILITY_H__E2939B31_AF1F_11D3_AFFB_0080C7FFDED7__INCLUDED_)
#define AFX_VALIDATEFACILITY_H__E2939B31_AF1F_11D3_AFFB_0080C7FFDED7__INCLUDED_

#if _MSC_VER >= 1000
#pragma once
#endif // _MSC_VER >= 1000
// ValidateFacility.h : header file
//
#include "resource.h"
/////////////////////////////////////////////////////////////////////////////
// CValidateFacility dialog

class CValidateFacility : public CDialog
{
// Construction
public:
	CValidateFacility(CWnd* pParent = NULL);   // standard constructor

// Dialog Data
	//{{AFX_DATA(CValidateFacility)
	enum { IDD = IDD_VALIDATE_FACILITY };
	CListCtrl	m_listCtrl;
	CEdit	m_logCtrl;
	CString	m_logText;
	CString	m_text;
	//}}AFX_DATA


// Overrides
	// ClassWizard generated virtual function overrides
	//{{AFX_VIRTUAL(CValidateFacility)
	protected:
	virtual void DoDataExchange(CDataExchange* pDX);    // DDX/DDV support
	//}}AFX_VIRTUAL

// Implementation
protected:

	// Generated message map functions
	//{{AFX_MSG(CValidateFacility)
	virtual void OnOK();
	//}}AFX_MSG
	DECLARE_MESSAGE_MAP()
};

//{{AFX_INSERT_LOCATION}}
// Microsoft Developer Studio will insert additional declarations immediately before the previous line.

#endif // !defined(AFX_VALIDATEFACILITY_H__E2939B31_AF1F_11D3_AFFB_0080C7FFDED7__INCLUDED_)

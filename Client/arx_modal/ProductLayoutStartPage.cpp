// ProductLayoutStartPage.cpp : implementation file
//

#include "stdafx.h"
#include "modal.h"
#include "ProductLayoutStartPage.h"
#include "OptimizationHelper.h"
#include "HelpService.h"

#ifdef _DEBUG
#define new DEBUG_NEW
#undef THIS_FILE
static char THIS_FILE[] = __FILE__;
#endif

extern CHelpService helpService;
/////////////////////////////////////////////////////////////////////////////
// CProductLayoutStartPage property page

IMPLEMENT_DYNCREATE(CProductLayoutStartPage, CPropertyPage)

CProductLayoutStartPage::CProductLayoutStartPage() : CPropertyPage(CProductLayoutStartPage::IDD)
{
	//{{AFX_DATA_INIT(CProductLayoutStartPage)
	m_ConstrainAmount = 0.0;
	m_ConstrainType = 0;
	m_LayoutType = COptimizationHelper::StrategicMode;
	m_Rotate = FALSE;
	m_VariableWidth = FALSE;
	m_ChangedOnly = FALSE;
	//}}AFX_DATA_INIT
}

CProductLayoutStartPage::~CProductLayoutStartPage()
{
}

void CProductLayoutStartPage::DoDataExchange(CDataExchange* pDX)
{
	CPropertyPage::DoDataExchange(pDX);
	//{{AFX_DATA_MAP(CProductLayoutStartPage)
	DDX_Check(pDX, IDC_SLOTCHECK, m_slotnew);
	DDX_Check(pDX, IDC_RESLOTCHECK, m_reslot);
	DDX_Check(pDX, IDC_SWAPCHECK, m_findswaps);
	DDX_Check(pDX, IDC_REORIENT, m_Rotate);
	DDX_Check(pDX, IDC_VARWIDTH, m_VariableWidth);
	//DDX_Check(pDX, IDC_CHANGED_ONLY, m_ChangedOnly);
	//}}AFX_DATA_MAP
}


BEGIN_MESSAGE_MAP(CProductLayoutStartPage, CPropertyPage)
	//{{AFX_MSG_MAP(CProductLayoutStartPage)
	ON_BN_CLICKED(IDC_STRATEGIC, OnStrategic)
	ON_BN_CLICKED(IDC_GROUP, OnGroup)
	ON_BN_CLICKED(IDC_TACTICAL, OnTactical)
	ON_BN_CLICKED(IDC_MANUAL, OnManual)
	ON_WM_HELPINFO()
	//}}AFX_MSG_MAP
END_MESSAGE_MAP()

/////////////////////////////////////////////////////////////////////////////
// CProductLayoutStartPage message handlers

BOOL CProductLayoutStartPage::OnInitDialog() 
{
	CPropertyPage::OnInitDialog();
	
	CButton *pButton;

	switch (m_LayoutType) {
	case COptimizationHelper::StrategicMode:
		pButton = (CButton *)GetDlgItem(IDC_STRATEGIC);
		break;
	case COptimizationHelper::GroupMode:
		pButton = (CButton *)GetDlgItem(IDC_GROUP);
		break;
	case COptimizationHelper::TacticalMode:
		pButton = (CButton *)GetDlgItem(IDC_TACTICAL);
		break;
	case COptimizationHelper::ManualMode:
		pButton = (CButton *)GetDlgItem(IDC_MANUAL);
		break;
	}

	pButton->SetCheck(1);

	ChangeProductLayoutMode(m_LayoutType);

	//if (m_ConstrainType == 0)
	//	pButton = (CButton *)GetDlgItem(IDC_PERCENTAGE);
	//else
	//	pButton = (CButton *)GetDlgItem(IDC_FIXED);

	//pButton->SetCheck(1);
	
	UpdateData(FALSE);

	return TRUE;  // return TRUE unless you set the focus to a control
	              // EXCEPTION: OCX Property Pages should return FALSE
}

void CProductLayoutStartPage::ChangeProductLayoutMode(int mode) 
{
//	CButton *pButton;
	
	m_LayoutType = mode;


	if (mode == COptimizationHelper::TacticalMode) {
		GetDlgItem(IDC_TACTICALBOX)->ShowWindow(SW_SHOW);
		GetDlgItem(IDC_TACTEXT1)->ShowWindow(SW_SHOW);
		GetDlgItem(IDC_TACTEXT2)->ShowWindow(SW_SHOW);
		GetDlgItem(IDC_TACTEXT3)->ShowWindow(SW_SHOW);
		GetDlgItem(IDC_SLOTCHECK)->ShowWindow(SW_SHOW);
		GetDlgItem(IDC_RESLOTCHECK)->ShowWindow(SW_SHOW);
		GetDlgItem(IDC_SWAPCHECK)->ShowWindow(SW_SHOW);
	}
	else {
		GetDlgItem(IDC_TACTICALBOX)->ShowWindow(SW_HIDE);
		GetDlgItem(IDC_TACTEXT1)->ShowWindow(SW_HIDE);
		GetDlgItem(IDC_TACTEXT2)->ShowWindow(SW_HIDE);
		GetDlgItem(IDC_TACTEXT3)->ShowWindow(SW_HIDE);
		GetDlgItem(IDC_SLOTCHECK)->ShowWindow(SW_HIDE);
		GetDlgItem(IDC_RESLOTCHECK)->ShowWindow(SW_HIDE);
		GetDlgItem(IDC_SWAPCHECK)->ShowWindow(SW_HIDE);
	}


	if (mode == COptimizationHelper::StrategicMode) {
		(CButton *)GetDlgItem(IDC_VARWIDTH)->EnableWindow(TRUE);
		(CButton *)GetDlgItem(IDC_REORIENT)->EnableWindow(TRUE);
	}
	else {
		(CButton *)GetDlgItem(IDC_VARWIDTH)->EnableWindow(FALSE);
		(CButton *)GetDlgItem(IDC_REORIENT)->EnableWindow(FALSE);
	}
	
}

void CProductLayoutStartPage::OnStrategic() 
{
	ChangeProductLayoutMode(COptimizationHelper::StrategicMode);
}

void CProductLayoutStartPage::OnGroup() 
{
	ChangeProductLayoutMode(COptimizationHelper::GroupMode);	
}

void CProductLayoutStartPage::OnTactical() 
{
	ChangeProductLayoutMode(COptimizationHelper::TacticalMode);	
}

void CProductLayoutStartPage::OnManual() 
{
	ChangeProductLayoutMode(COptimizationHelper::ManualMode);	
}

BOOL CProductLayoutStartPage::OnKillActive() 
{
	//CButton *pButton = (CButton *)GetDlgItem(IDC_PERCENTAGE);
	//if (pButton->GetCheck())
	//	m_ConstrainType = 0;
	//else
	//	m_ConstrainType = 1;
	
	return CPropertyPage::OnKillActive();
}

BOOL CProductLayoutStartPage::OnHelpInfo(HELPINFO* pHelpInfo) 
{
	helpService.ShowFieldHelp(IDD, pHelpInfo->iCtrlId);
	return FALSE;

}

BOOL CProductLayoutStartPage::OnNotify(WPARAM wParam, LPARAM lParam, LRESULT* pResult) 
{
	NMHDR* pNMHDR = (NMHDR*)lParam;

	if (pNMHDR->code == PSN_HELP) {
		helpService.ShowScreenHelp(IDD);
		return TRUE;
	}
	
	return CPropertyPage::OnNotify(wParam, lParam, pResult);
}

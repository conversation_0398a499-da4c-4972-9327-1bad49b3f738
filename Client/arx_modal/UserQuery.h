// UserQuery.h: interface for the CUserQuery class.
//
//////////////////////////////////////////////////////////////////////

#if !defined(AFX_USERQUERY_H__CBE25893_9AFE_11D4_9EB9_00C04FAC149C__INCLUDED_)
#define AFX_USERQUERY_H__CBE25893_9AFE_11D4_9EB9_00C04FAC149C__INCLUDED_

#if _MSC_VER > 1000
#pragma once
#endif // _MSC_VER > 1000

class CUserQuery : public CObject  
{
public:
	int m_QueryID;
	void Parse(CString line);
	DECLARE_SERIAL(CUserQuery)
	CUserQuery();
	virtual ~CUserQuery();
	CUserQuery& operator=(const CUserQuery & other);

	CString m_Name;
	CString m_Description;
	CString m_Query;
	CString m_Database;

	void Serialize(CArchive &archive);

};

#endif // !defined(AFX_USERQUERY_H__CBE25893_9AFE_11D4_9EB9_00C04FAC149C__INCLUDED_)

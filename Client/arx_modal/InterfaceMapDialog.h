#if !defined(AFX_INTERFACEMAPDIALOG_H__8F8C75E9_4EC9_4432_9FDD_8CD78CBE449C__INCLUDED_)
#define AFX_INTERFACEMAPDIALOG_H__8F8C75E9_4EC9_4432_9FDD_8CD78CBE449C__INCLUDED_

#if _MSC_VER > 1000
#pragma once
#endif // _MSC_VER > 1000
// InterfaceMapDialog.h : header file
//
#include "InterfaceMap.h"
#include "InterfaceMapDataService.h"

/////////////////////////////////////////////////////////////////////////////
// CInterfaceMapDialog dialog

class CInterfaceMapDialog : public CDialog
{
// Construction
public:
	int m_InterfaceType;
	CInterfaceMapDialog(CWnd* pParent = NULL);   // standard constructor
	~CInterfaceMapDialog();

// Dialog Data
	//{{AFX_DATA(CInterfaceMapDialog)
	enum { IDD = IDD_INTERFACE_MAP };
	CComboBox	m_FormatListCtrl;
	CListCtrl	m_ExternalListCtrl;
	CComboBox	m_MapTypeListCtrl;
	CComboBox	m_MapNameListCtrl;
	CListCtrl	m_InternalListCtrl;
	//}}AFX_DATA


// Overrides
	// ClassWizard generated virtual function overrides
	//{{AFX_VIRTUAL(CInterfaceMapDialog)
	protected:
	virtual void DoDataExchange(CDataExchange* pDX);    // DDX/DDV support
	virtual BOOL OnNotify(WPARAM wParam, LPARAM lParam, LRESULT* pResult);
	//}}AFX_VIRTUAL

// Implementation
protected:

	// Generated message map functions
	//{{AFX_MSG(CInterfaceMapDialog)
	virtual BOOL OnInitDialog();
	virtual void OnOK();
	virtual void OnCancel();
	afx_msg void OnHelp();
	afx_msg void OnContextMenu(CWnd* pWnd, CPoint point);
	afx_msg void OnSave();
	afx_msg void OnCopy();
	afx_msg void OnSelchangeMapList();
	afx_msg void OnMap();
	afx_msg void OnUnmap();
	afx_msg void OnCreateUdf();
	afx_msg void OnAutomap();
	afx_msg void OnAddExternalAttribute();
	afx_msg void OnRemoveExternalAttribute();
	afx_msg void OnLoadAttributes();
	afx_msg void OnSelchangeExternalList();
	afx_msg BOOL OnHelpInfo(HELPINFO* pHelpInfo);
	afx_msg void OnSelchangeMapnameList();
	afx_msg void OnSelchangeMapType();
	afx_msg void OnMapConstant();
	afx_msg void OnEndlabeleditExternalList(NMHDR* pNMHDR, LRESULT* pResult);
	afx_msg void OnAddInternal();
	afx_msg void OnDeleteInternal();
	afx_msg void OnRButtonDblClk(UINT nFlags, CPoint point);
	afx_msg void OnRemoveUdf();
	afx_msg void OnDeleteMap();
	afx_msg void OnDblclkExternalList(NMHDR* pNMHDR, LRESULT* pResult);
	afx_msg void OnMapUdf();
	afx_msg void OnUp();
	//}}AFX_MSG
	DECLARE_MESSAGE_MAP()
private:
	BOOL m_bSuperUser;
	int m_PreviousTypeSel;
	int AddUDF(const CString &defaultName);
	int GetFile(CString &fileName, int formatType);
	int AddExternalAttribute(const CString &text);
	int DisplayAttribute(CInterfaceMapAttribute *pAttr, BOOL bIsSelected=FALSE);
	int DisplayMapAttributes();
	int m_PreviousNameSel;
	int CheckSave(int curSel);
	CInterfaceMap m_CurrentMap;
	int LoadMapAttributes(CInterfaceMap *pMap);
	int LoadMapNameList();
	void InitializeMapList();
	int GetInternalSel();
	int GetExternalSel();
	int SaveMap();

	CTypedPtrArray<CObArray, CInterfaceMap*> m_MapList;
};

//{{AFX_INSERT_LOCATION}}
// Microsoft Visual C++ will insert additional declarations immediately before the previous line.

#endif // !defined(AFX_INTERFACEMAPDIALOG_H__8F8C75E9_4EC9_4432_9FDD_8CD78CBE449C__INCLUDED_)

#if !defined(AFX_PRODUCTGROUPCRITERIAMAINTENANCE_H__5E791C67_041F_11D5_9EC8_00C04FAC149C__INCLUDED_)
#define AFX_PRODUCTGROUPCRITERIAMAINTENANCE_H__5E791C67_041F_11D5_9EC8_00C04FAC149C__INCLUDED_

#if _MSC_VER > 1000
#pragma once
#endif // _MSC_VER > 1000
// ProductGroupCriteriaMaintenance.h : header file
//
#include "Resource.h"

#include "ProductAttribute.h"
#include "ProductGroupDataService.h"

/////////////////////////////////////////////////////////////////////////////
// CProductGroupCriteriaMaintenance dialog

class CProductGroupCriteriaMaintenance : public CDialog
{
// Construction
	DECLARE_DYNCREATE(CProductGroupCriteriaMaintenance)

public:
	BOOL ValidateClose();
	B<PERSON><PERSON> m_SomethingChanged;
	void DisplayCriteria();
	CProductGroupCriteriaMaintenance(CWnd* pParent = NULL);   // standard constructor
	~CProductGroupCriteriaMaintenance();
	CTypedPtrArray<CObArray, CProductGroupCriteria*> *m_CriteriaList;
	CTypedPtrArray<CObArray, CProductGroup*> *m_ProductGroupList;
	CTypedPtrArray<CObArray, CProductAttribute*> *m_ProductAttributeList;
	CProductGroupDataService *m_ProductGroupDataService;

// Dialog Data
	//{{AFX_DATA(CProductGroupCriteriaMaintenance)
	enum { IDD = IDD_CRITERIA_MAINTENANCE };
	CListCtrl	m_CriteriaListCtrl;
	//}}AFX_DATA


// Overrides
	// ClassWizard generated virtual function overrides
	//{{AFX_VIRTUAL(CProductGroupCriteriaMaintenance)
	public:
	virtual BOOL Create(LPCTSTR lpszClassName, LPCTSTR lpszWindowName, DWORD dwStyle, const RECT& rect, CWnd* pParentWnd, UINT nID, CCreateContext* pContext = NULL);
	protected:
	virtual void DoDataExchange(CDataExchange* pDX);    // DDX/DDV support
	virtual void PostNcDestroy();
	//}}AFX_VIRTUAL

// Implementation
protected:

	// Generated message map functions
	//{{AFX_MSG(CProductGroupCriteriaMaintenance)
	afx_msg void OnAdd();
	afx_msg void OnDelete();
	afx_msg void OnProperties();
	virtual void OnCancel();
	afx_msg void OnHelp();
	virtual void OnOK();
	virtual BOOL OnInitDialog();
	afx_msg void OnContextMenu(CWnd* pWnd, CPoint point);
	afx_msg void OnDblclkCriteriaList(NMHDR* pNMHDR, LRESULT* pResult);
	afx_msg BOOL OnHelpInfo(HELPINFO* pHelpInfo);
	//}}AFX_MSG
	DECLARE_MESSAGE_MAP()
private:
	int GetCurSel();
};

//{{AFX_INSERT_LOCATION}}
// Microsoft Visual C++ will insert additional declarations immediately before the previous line.

#endif // !defined(AFX_PRODUCTGROUPCRITERIAMAINTENANCE_H__5E791C67_041F_11D5_9EC8_00C04FAC149C__INCLUDED_)

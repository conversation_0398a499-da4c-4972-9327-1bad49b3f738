// ProductGroupCriteriaMatrix.cpp : implementation file
//

#include "stdafx.h"
#include "ssa_exception.h"
#include "HelpService.h"
#include "UtilityHelper.h"
#include "excel8.h"
#include "font.h"

#include "ProductGroupCriteriaMatrix.h"
#include "ProductGroupCriteriaMaintenance.h"
#include "ProductGroupCriteriaQueryDialog.h"
#include "ProductGroupFrame.h"
#include "ProductGroupNavigator.h"

#include "DataGrid.h"
#include "ProductGroupQuery.h"

#include <dbsymtb.h>
#include <errno.h>

#ifdef _DEBUG
#define new DEBUG_NEW
#undef THIS_FILE
static char THIS_FILE[] = __FILE__;
#endif

extern CHelpService helpService;
extern CUtilityHelper utilityHelper;


#define MAX_ROWS 65500

/////////////////////////////////////////////////////////////////////////////
// CProductGroupCriteriaMatrix dialog


extern CProductGroupFrame *m_ProductGroupFrame;
IMPLEMENT_DYNCREATE(CProductGroupCriteriaMatrix, CDialog)

CProductGroupCriteriaMatrix::CProductGroupCriteriaMatrix(CWnd* pParent /*=NULL*/)
	: CDialog(CProductGroupCriteriaMatrix::IDD, pParent)
{
	//{{AFX_DATA_INIT(CProductGroupCriteriaMatrix)
		// NOTE: the ClassWizard will add member initialization here
	//}}AFX_DATA_INIT
}

CProductGroupCriteriaMatrix::~CProductGroupCriteriaMatrix()
{
	for (int i=0; i < m_ProductGroupListOld.GetSize(); ++i)
		delete m_ProductGroupListOld[i];

}

void CProductGroupCriteriaMatrix::DoDataExchange(CDataExchange* pDX)
{
	CDialog::DoDataExchange(pDX);
	//{{AFX_DATA_MAP(CProductGroupCriteriaMatrix)
	DDX_Control(pDX, IDEXCEL, m_ExcelBtn);
	DDX_Control(pDX, IDC_CRITERIA_GRID, m_Grid);
	//}}AFX_DATA_MAP
}


BEGIN_MESSAGE_MAP(CProductGroupCriteriaMatrix, CDialog)
	//{{AFX_MSG_MAP(CProductGroupCriteriaMatrix)
	ON_BN_CLICKED(IDAPPLY, OnApply)
	ON_BN_CLICKED(IDHELP, OnHelp)
	ON_WM_HELPINFO()
	ON_BN_CLICKED(IDEXCEL, OnExcel)
	//}}AFX_MSG_MAP
END_MESSAGE_MAP()

/////////////////////////////////////////////////////////////////////////////
// CProductGroupCriteriaMatrix message handlers

BOOL CProductGroupCriteriaMatrix::OnInitDialog() 
{
	CDialog::OnInitDialog();
	
	Initialize();
	
	CBitmap bitmap;
	bitmap.LoadBitmap(IDB_EXCEL);
	m_ExcelBtn.SetBitmap((HBITMAP)bitmap.Detach());

	return TRUE;  // return TRUE unless you set the focus to a control
	              // EXCEPTION: OCX Property Pages should return FALSE
}

void CProductGroupCriteriaMatrix::OnApply() 
{
	CProductGroup *pGroup;
	CProductGroupQuery *pQuery;
	CDataGridAttribute *pAttr, *pColAttr;
	CArray<int, int> deleteList;
	CString rangeName;
	int rc, i, j, k, l;
	CProductGroupCriteriaRange *pRange;

	BOOL bFound;
	int cols = m_Grid.GetCols();

	// Need to set up maps for some of this stuff
	CTypedPtrMap<CMapWordToOb, long, CProductGroupCriteria*> map;

	// Set all the ranges to not used; update them to used as we go
	for (i=0; i < m_CriteriaList->GetSize(); ++i) {
		for (j=0; j < m_CriteriaList->GetAt(i)->m_CriteriaRangeList.GetSize(); ++j)
			m_CriteriaList->GetAt(i)->m_CriteriaRangeList[j]->m_InUse = FALSE;
	}

	for (i=0; i < m_ProductGroupList->GetSize(); ++i) {
		pGroup = m_ProductGroupList->GetAt(i);
		
		// For each product group:
		// Loop through the columns (criteria)
		// Look for an existing query that matches the column
		// If found, update the query with the value in the column
		// Otherwise, add a query with the value
		// If the value in the column is undefined, delete the query if there is one
		// Also update the used flag for the criteria ranges
	
		for (j=1; j < cols; ++j) {
			pColAttr = m_Grid.m_DataGridAttributes[j];
			pAttr = m_Grid.m_DataGridAttributes[(i+1)*cols+j];
			
			bFound = FALSE;
			// map criteria dbid to product group query
			for (k=0; k < pGroup->m_QueryList.GetSize(); ++k) {
				pQuery = pGroup->m_QueryList[k];
				if (pColAttr->m_AttributeID == pQuery->m_CriteriaDBID) {
					bFound = TRUE;
					break;
				}
			}

			// Found the query that goes with the column
			if (bFound) {
				// If the cell for the query is undefined (any or blank),
				// add the query dbid to the list of ones to delete
				if ( (pAttr->m_Value == "0" && pAttr->m_DataType == DT_LIST) ||
					pAttr->m_Value == "") {	// 0 = Any = delete the query
					deleteList.Add(pQuery->m_ProductGroupQueryDBID);
				}
				else {
					// If this is a list box, find the range dbid associated with 
					// the value they chose
					if (pAttr->m_DataType == DT_LIST) {
						rangeName = pAttr->m_ListValues[atoi(pAttr->m_Value)];
						// map criteria dbid to to criteria
						for (k=0; k < m_CriteriaList->GetSize(); ++k) {
							if (m_CriteriaList->GetAt(k)->m_CriteriaDBID == pQuery->m_CriteriaDBID) {
								if (! m_CriteriaList->GetAt(k)->m_IsDiscrete) {
									// it's a list but a built-in not user defined
									pQuery->m_CriteriaRangeDBID = m_CriteriaList->GetAt(k)->m_CriteriaRangeList[0]->m_CriteriaRangeDBID;
									pQuery->m_Operator = "=";
									pQuery->m_RangeName = rangeName;
									// Find the product attribute associated with this item
									CProductAttribute *pProdAttr;
									m_ProductAttributeMap.Lookup(m_CriteriaList->GetAt(k)->GetAttribute(), (CObject *&)pProdAttr);
									CString temp;
									int idx = atoi(pAttr->m_Value)-1;

									pQuery->m_Value = rangeName;
									pQuery->m_CriteriaRangeDBID = m_CriteriaList->GetAt(k)->m_CriteriaRangeList[0]->m_CriteriaRangeDBID;
								}
								// it's a user-defined list
								else {
									// map range description to range
									for (l=0; l < m_CriteriaList->GetAt(k)->m_CriteriaRangeList.GetSize(); ++l) {
										if (m_CriteriaList->GetAt(k)->m_CriteriaRangeList[l]->m_Description == rangeName) {
											pRange = m_CriteriaList->GetAt(k)->m_CriteriaRangeList[l];
											pQuery->m_CriteriaRangeDBID = m_CriteriaList->GetAt(k)->m_CriteriaRangeList[l]->m_CriteriaRangeDBID;
											// Use the first query in the ranges just to have something
											// to fill in; when they actually run it, it will come from
											// the CriteriaQuery list
											pQuery->m_Operator = m_CriteriaList->GetAt(k)->m_CriteriaRangeList[l]->m_CriteriaQueryList[0]->m_Operator;
											pQuery->m_Value = m_CriteriaList->GetAt(k)->m_CriteriaRangeList[l]->m_CriteriaQueryList[0]->m_Value;
											pQuery->m_RangeName = rangeName;
											m_CriteriaList->GetAt(k)->m_CriteriaRangeList[l]->m_InUse = TRUE;
											break;
										}
									}
								}
								break;
							}
						}
					}
					// If this is not a list type, use the sql they entered
					else {
						if (m_InternalValues[(i+1)*cols+j] != "") {
							pQuery->m_Value = m_InternalValues[(i+1)*cols+j];
							pQuery->m_Operator = m_InternalOperators[(i+1)*cols+j];
						}
						else {	// is it even possible to get here?
							pQuery->m_Value = pAttr->m_Value;
							//pQuery->m_Operator = ?
						}

						// Find the default range dbid which we need so we can stay normalized
						// map criteria dbid to to criteria
						for (k=0; k < m_CriteriaList->GetSize(); ++k) {
							if (m_CriteriaList->GetAt(k)->m_CriteriaDBID == pQuery->m_CriteriaDBID) {
								pQuery->m_CriteriaRangeDBID = 
									m_CriteriaList->GetAt(k)->m_CriteriaRangeList[0]->m_CriteriaRangeDBID;
								break;
							}
						}
					}
				}
	
			}
			// No query currently goes with this column, i.e. they have never 
			// filled in this cell before so we may need to create a new query
			else {
				// If the cell is empty, we would delete it, but since it doesn't exist
				// yet we can just skip it
				if ( (pAttr->m_DataType == DT_LIST && pAttr->m_Value == "0") ||
					(pAttr->m_Value == "") )
					;	// nothing to do here
				else {
					// If this is a list type, find the range dbid associated with
					// the value they chose
					if (pAttr->m_DataType == DT_LIST) {
						rangeName = pAttr->m_ListValues[atoi(pAttr->m_Value)];
						// map criteria dbid to criteria
						for (k=0; k < m_CriteriaList->GetSize(); ++k) {
							CProductGroupCriteria *pTmpCrit = m_CriteriaList->GetAt(k);

							if (m_CriteriaList->GetAt(k)->m_CriteriaDBID == pColAttr->m_AttributeID) {
								if (! m_CriteriaList->GetAt(k)->m_IsDiscrete) {
									// the attribute is a list type, but it's a builtin list type not a user-defined
									pQuery = new CProductGroupQuery;
									pQuery->m_ProductGroupQueryDBID = 0;
									pQuery->m_ProductGroupDBID = pGroup->m_ProductGroupDBID;
									pQuery->m_CriteriaDBID = m_CriteriaList->GetAt(k)->m_CriteriaDBID;
									// Have to be careful to use the internal operator here
									pQuery->m_Operator = "=";
									pQuery->m_RangeName = rangeName;
									// Find the product attribute associated with this item
									CProductAttribute *pProdAttr;
									m_ProductAttributeMap.Lookup(m_CriteriaList->GetAt(k)->GetAttribute(), (CObject *&)pProdAttr);
									CString temp;
									int idx = atoi(pAttr->m_Value)-1;
									pQuery->m_Value = rangeName;
									pQuery->m_CriteriaRangeDBID = m_CriteriaList->GetAt(k)->m_CriteriaRangeList[0]->m_CriteriaRangeDBID;
									pGroup->m_QueryList.Add(pQuery);	
								}
								// the attribute is a user-defined list type
								else {
									// map range name to criteria range
									for (l=0; l < m_CriteriaList->GetAt(k)->m_CriteriaRangeList.GetSize(); ++l) {
										CProductGroupCriteriaRange *pTmpRange = m_CriteriaList->GetAt(k)->m_CriteriaRangeList[l];
										
										if (m_CriteriaList->GetAt(k)->m_CriteriaRangeList[l]->m_Description == rangeName) {
											pQuery = new CProductGroupQuery;
											pQuery->m_ProductGroupQueryDBID = 0;
											pQuery->m_ProductGroupDBID = pGroup->m_ProductGroupDBID;
											pQuery->m_CriteriaDBID = m_CriteriaList->GetAt(k)->m_CriteriaDBID;
											pQuery->m_CriteriaRangeDBID = m_CriteriaList->GetAt(k)->m_CriteriaRangeList[l]->m_CriteriaRangeDBID;
											pQuery->m_Operator = m_CriteriaList->GetAt(k)->m_CriteriaRangeList[l]->m_CriteriaQueryList[0]->m_Operator;
											pQuery->m_Value = m_CriteriaList->GetAt(k)->m_CriteriaRangeList[l]->m_CriteriaQueryList[0]->m_Value;
											pQuery->m_RangeName = rangeName;
											pGroup->m_QueryList.Add(pQuery);
											m_CriteriaList->GetAt(k)->m_CriteriaRangeList[l]->m_InUse = TRUE;
											break;
										}
									}
								}
								break;
							}	
						}
					}
					else {
						// this is not a list type so use the sql they entered
						pQuery = new CProductGroupQuery;
						pQuery->m_ProductGroupQueryDBID = 0;
						pQuery->m_ProductGroupDBID = pGroup->m_ProductGroupDBID;
						pQuery->m_CriteriaDBID = pColAttr->m_AttributeID;
						if (m_InternalValues[(i+1)*cols+j] != "") {
							pQuery->m_Value = m_InternalValues[(i+1)*cols+j];
							pQuery->m_Operator = m_InternalOperators[(i+1)*cols+j];
						}
						else {
							pQuery->m_Value = pAttr->m_Value;
							//pQuery->m_Operator = ?
						}
						// find the default range dbid for this criteria
						// map criteria dbid to criteria range dbid
						for (k=0; k < m_CriteriaList->GetSize(); ++k) {
							if (m_CriteriaList->GetAt(k)->m_CriteriaDBID == pQuery->m_CriteriaDBID) {
								pQuery->m_CriteriaRangeDBID = m_CriteriaList->GetAt(k)->m_CriteriaRangeList[0]->m_CriteriaRangeDBID;
								pGroup->m_QueryList.Add(pQuery);
								break;
							}
						}
					}
				}
			}

		}

		// Check to see if they made any changes; if not, do not update
		if (pGroup->IsEqual(*m_ProductGroupListOld[i]) && deleteList.GetSize() == 0)
			continue;

		// Update the queries
		CProductGroupDataService service;
		try {
			rc = service.StoreProductGroupQuery(*pGroup, deleteList);
		}
		catch (Ssa_Exception e) {
			utilityHelper.ProcessError("Error updating product group criteria", &e);
		}
		catch (...) {
			utilityHelper.ProcessError("Error updating product group criteria.");
		}
		
		// Now remove the items that were deleted from the list
		if (rc == 0) {
			for (j=0; j < deleteList.GetSize(); ++j) {
				for (k=0; k < pGroup->m_QueryList.GetSize(); ++k) {
					if (pGroup->m_QueryList[k]->m_ProductGroupQueryDBID == deleteList[j]) {
						delete pGroup->m_QueryList[k];
						pGroup->m_QueryList.RemoveAt(k);
						break;
					}
				}
			}
		}
	}

	// Reset the "old" list
	for (i=0; i < m_ProductGroupListOld.GetSize(); ++i) {
		delete m_ProductGroupListOld[i];
	}

	m_ProductGroupListOld.RemoveAll();

	for (i=0; i < m_ProductGroupList->GetSize(); ++i) {
		pGroup = new CProductGroup;
		*pGroup = *m_ProductGroupList->GetAt(i);
		m_ProductGroupListOld.Add(pGroup);
	}


}

void CProductGroupCriteriaMatrix::OnHelp() 
{
	helpService.ShowScreenHelp(IDD);	
}

CProductGroupCriteriaMatrix::LoadGrid()
{
	CDataGridAttribute *pAttr;
	CProductGroup *pGroup;
	COperator *pOperator;

	int row, col, rows, cols, i, j;
	CString value;

	//ads_printf("in loadGrid\n");

	m_Grid.m_ResetColumnWidths = TRUE;

	for (i=0; i < m_Grid.m_DataGridAttributes.GetSize(); ++i) {
		if (m_Grid.m_DataGridAttributes[i] != NULL)
			delete m_Grid.m_DataGridAttributes[i];
	}
	m_Grid.m_DataGridAttributes.RemoveAll();
	m_InternalValues.RemoveAll();
	m_InternalOperators.RemoveAll();

	//ads_printf("after removeall\n");

	rows = (*m_ProductGroupList).GetSize()+1;
	cols = (*m_CriteriaList).GetSize()+1;

	m_Grid.SetCols(cols);
	m_Grid.SetRows(rows);

	if (rows <= 1 || cols <= 1) {
		m_Grid.ShowWindow(SW_HIDE);
		(CStatic *)GetDlgItem(IDC_STATIC_MESSAGE)->ShowWindow(SW_SHOW);
		return FALSE;
	}
	else {
		m_Grid.ShowWindow(SW_SHOW);
		GetDlgItem(IDC_STATIC_MESSAGE)->ShowWindow(SW_HIDE);
	}


	
	m_Grid.m_MainHelpTopic = "ProductGroupCriteria_CriteriaGrid";

	//ads_printf("rows, cols = %d, %d \n", rows, cols);

	// First add all attributes to the grid
	for (row=0; row < rows; ++row) {
		for (col=0; col < cols; ++col) {	
			pAttr = new CDataGridAttribute;
			if (row < m_Grid.GetFixedRows() || col < m_Grid.GetFixedCols()) {
				pAttr->m_Type = AT_FIXED;
			}
			else {
				pAttr->m_Type = AT_VAR;
			}
			m_Grid.m_DataGridAttributes.Add(pAttr);
			m_InternalValues.Add("");
			m_InternalOperators.Add("");
			m_Grid.SetTextMatrix(row, col, "");
		}
	}

	//ads_printf("after add all attributes\n");

	// Column headings = criteria names
	for (col=1; col < cols; ++col) {
		pAttr = m_Grid.m_DataGridAttributes[col];
		pAttr->m_InitialValue = m_CriteriaList->GetAt(col-1)->m_Name;
		pAttr->m_AttributeID = m_CriteriaList->GetAt(col-1)->m_CriteriaDBID;

		if (m_CriteriaList->GetAt(col-1)->m_IsDiscrete)
			pAttr->m_InitialValue += "(List)";
		else
			pAttr->m_InitialValue += "(Range)";
		
		pAttr->m_Value = pAttr->m_InitialValue;
		pAttr->m_DataType = DT_NONE;

		// Load the criteria into the corresponding columns
		for (row=1; row < rows; ++row) {
			pAttr = m_Grid.m_DataGridAttributes[row*cols+col];
			if (m_CriteriaList->GetAt(col-1)->m_IsDiscrete) {
				pAttr->m_DataType = DT_LIST;
				if (m_CriteriaList->GetAt(col-1)->m_CriteriaRangeList.GetSize() == 0) {
					pAttr->m_DataType = DT_STRING;
					pAttr->m_InitialValue = "No Elements";	// Type is list but no ranges defined
					pAttr->m_Value = "No Elements";
					pAttr->m_ReadOnly = TRUE;
				}
				else {
					pAttr->m_ListValues.Add("Any");
					for (i=0; i < m_CriteriaList->GetAt(col-1)->m_CriteriaRangeList.GetSize(); ++i) {
						pAttr->m_ListValues.Add(m_CriteriaList->GetAt(col-1)->m_CriteriaRangeList[i]->m_Description);
					}
				}
			}
			else {
				if (m_CriteriaList->GetAt(col-1)->GetAttributeType() == DT_LIST) {
					pAttr->m_DataType = DT_LIST;
					pAttr->m_ListValues.Add("Any");
					CProductAttribute *pProdAttr;
					m_ProductAttributeMap.Lookup(m_CriteriaList->GetAt(col-1)->GetAttribute(), (CObject *&)pProdAttr);
					for (i=0; i < pProdAttr->m_ListValues.GetSize(); ++i) {
						pAttr->m_ListValues.Add(pProdAttr->m_ListValues[i]);
					}
				}
				else {
					pAttr->m_ReadOnly = TRUE;
					pAttr->m_DataType = DT_STRING;	
				}
			}
		}
	}

	//ads_printf("after add column headings\n");

	// Row headings = product group names
	for (row = 1; row < rows; ++row) {
		pGroup = (*m_ProductGroupList)[row-1];

		pAttr = m_Grid.m_DataGridAttributes[row*cols];
		pAttr->m_InitialValue = m_ProductGroupList->GetAt(row-1)->m_Description;
		pAttr->m_Value = pAttr->m_InitialValue;
		pAttr->m_DataType = DT_NONE;

		// Set the initial values
		for (col = 1; col < cols; ++col) {

			pAttr = m_Grid.m_DataGridAttributes[row*cols+col];
			
			// Find the associated query item for this column
			BOOL found = FALSE;
			for (i=0; i < pGroup->m_QueryList.GetSize(); ++i) {
				if (pGroup->m_QueryList[i]->m_CriteriaDBID == m_CriteriaList->GetAt(col-1)->m_CriteriaDBID) {
					found = TRUE;
					break;
				}
			}
			if (found) {
				if (pAttr->m_DataType == DT_LIST) {
					value = pGroup->m_QueryList[i]->m_RangeName;
					// for built-in lists the range name is "Default"
					// we need to use the actual value

					
					if (value == "Default") {
						value = pGroup->m_QueryList[i]->m_Value;
					}/*
						pAttr->m_InitialValue.Format("%d", atoi(pGroup->m_QueryList[i]->m_Value)+1);
						pAttr->m_Value = pAttr->m_InitialValue;
					}
					else {
					*/
						pAttr->m_InitialValue = "0";
						pAttr->m_Value = "0";
						for (j=0; j < pAttr->m_ListValues.GetSize(); ++j) {
							if (pAttr->m_ListValues[j] == value) {
								pAttr->m_InitialValue.Format("%d", j);
								pAttr->m_Value.Format("%d", j);
								break;
							}
						}
					//}
				}
				else {
					m_InternalValues[row*cols+col] = pGroup->m_QueryList[i]->m_Value;
					m_InternalOperators[row*cols+col] = pGroup->m_QueryList[i]->m_Operator;
				
					pOperator = m_ProductGroupDataService->m_OperatorService.ConvertInternalToDisplay(pGroup->m_QueryList[i]->m_Operator);
					pAttr->m_InitialValue = pOperator->m_Display;
					//if (pOperator->m_Internal == "=")
					//	pAttr->m_InitialValue = "";

					pAttr->m_InitialValue += " ";
					pAttr->m_InitialValue += pGroup->m_QueryList[i]->m_Value;
					if (pOperator->m_OperandCount == 2)
						pAttr->m_InitialValue.Replace("^", " ");
					else
						pAttr->m_InitialValue.Replace("^", ",");
					pAttr->m_Value = pAttr->m_InitialValue;
				}
			}
			else {
				if (pAttr->m_DataType == DT_LIST) {
					pAttr->m_InitialValue = "0";
					pAttr->m_Value = "0";
				}
			}
		}
	}

	//ads_printf("after add row headings\n");

	for (i=0; i < m_Grid.m_DataGridAttributes.GetSize(); ++i) {
		pAttr = m_Grid.m_DataGridAttributes[i];
	}

	//ads_printf("before loadattributes\n");

	m_Grid.LoadAttributes();

	//ads_printf("after loadattributes\n");

	m_Grid.m_DoubleClickCellFunction = CProductGroupCriteriaMatrix::OnDoubleClickGrid;

	//ads_printf("out of load grid\n");

	return FALSE;
}

int CProductGroupCriteriaMatrix::Initialize()
{
	CProductGroup *pGroup;

	for (int i=0; i < m_ProductGroupList->GetSize(); ++i) {
		pGroup = new CProductGroup;
		*pGroup = *(m_ProductGroupList->GetAt(i));
		m_ProductGroupListOld.Add(pGroup);
	}

	for (i=0; i < m_ProductAttributeList->GetSize(); ++i) {
		m_ProductAttributeMap.SetAt(m_ProductAttributeList->GetAt(i)->m_Name, 
			m_ProductAttributeList->GetAt(i));
	}

	if (LoadGrid() < 0)
		return -1;

	return 0;

}

void CProductGroupCriteriaMatrix::OnDoubleClickGrid(void *parent)
{
	CProductGroupCriteriaMatrix *me = (CProductGroupCriteriaMatrix *)parent;
	CDataGridAttribute *pAttr;
	CProductGroupCriteriaQueryDialog dlg;
	CProductGroupCriteria *pCriteria;

	CString temp, displayValue, displayOperator;

	long row, col;
	int idx;
	COperator *pOperator;

	row = me->m_Grid.GetRow();
	col = me->m_Grid.GetCol();

	pCriteria = me->m_CriteriaList->GetAt(col-1);
	dlg.m_Attribute = pCriteria->m_Description;

	idx = row * me->m_Grid.GetCols() + col;
	pAttr = me->m_Grid.m_DataGridAttributes[idx];
	if (pAttr->m_InitialValue != "No Elements") {
		
		dlg.m_ProductGroupDataService = me->m_ProductGroupDataService;
		dlg.m_InternalValue = me->m_InternalValues[idx];
		dlg.m_InternalOperator = me->m_InternalOperators[idx];

		if (dlg.DoModal() == IDOK) {
			if (dlg.m_InternalOperator == "") {	// Any, i.e. no query
				temp = "";
			}
			else {
				pOperator = me->m_ProductGroupDataService->m_OperatorService.ConvertInternalToDisplay(dlg.m_InternalOperator);
				displayOperator = pOperator->m_Display;
				//if (pOperator->m_Internal == "=")
				//	displayOperator = " ";

				displayValue = dlg.m_InternalValue;
				if (pOperator->m_OperandCount == 2)
					displayValue.Replace("^", " ");	
				else
					displayValue.Replace("^", ",");
				if (displayOperator != "")
					temp.Format("%s %s", displayOperator, displayValue);
				else
					temp = "";
			}
			me->m_Grid.m_Edit->SetWindowText(temp);
			pAttr->m_Value = temp;
			me->m_Grid.SetTextMatrix(row, col, temp);
			me->m_InternalValues[idx] = dlg.m_InternalValue;
			me->m_InternalOperators[idx] = dlg.m_InternalOperator;

		}
	}

	return;

}

BOOL CProductGroupCriteriaMatrix::Create(LPCTSTR lpszClassName, LPCTSTR lpszWindowName, DWORD dwStyle, const RECT& rect, CWnd* pParentWnd, UINT nID, CCreateContext* pContext) 
{
	UNREFERENCED_PARAMETER(lpszClassName);
	UNREFERENCED_PARAMETER(lpszWindowName);
	UNREFERENCED_PARAMETER(dwStyle);
	UNREFERENCED_PARAMETER(rect);
	UNREFERENCED_PARAMETER(nID);
	UNREFERENCED_PARAMETER(pContext);
	CProductGroupNavigator *pNavigator;

	pNavigator = (CProductGroupNavigator *)m_ProductGroupFrame->m_Splitter.GetPane(0, 0);
	m_ProductGroupList = &pNavigator->m_ProductGroupList;
	m_CriteriaList = &pNavigator->m_ProductGroupCriteriaList;
	m_ProductAttributeList = &pNavigator->m_ProductAttributeList;
	m_ProductGroupDataService = &pNavigator->m_ProductGroupDataService;

	BOOL bReturn = CDialog::Create(IDD, pParentWnd);

	int id;
	id = m_ProductGroupFrame->m_Splitter.IdFromRowCol(0, 1);

	if ( bReturn )
		::SetWindowLong ( m_hWnd, GWL_ID, id);
	
	return bReturn;
}

void CProductGroupCriteriaMatrix::PostNcDestroy() 
{
	delete this;
}

BOOL CProductGroupCriteriaMatrix::ValidateClose()
{
	int rc;
	CProductGroup *pOldGroup;
	CProductGroupQuery *pQuery;
	CDataGridAttribute *pAttr, *pColAttr;
	int cols = m_Grid.GetCols();
	int idx, i, j, col;
	CString temp;
	COperator *pOperator;

	// Loop through the product groups
	for (i=0; i < m_ProductGroupListOld.GetSize(); ++i) {
		pOldGroup = m_ProductGroupListOld[i];
		
		// Loop through the queries within the product group
		for (j=0; j < pOldGroup->m_QueryList.GetSize(); ++j) {
			pQuery = pOldGroup->m_QueryList[j];
			
			// Find the corresponding criteria in the column headers
			idx = FindAttribute(pQuery->m_CriteriaDBID);

			// Get the actual data cell
			idx = idx + (i+1)*cols;	
			pAttr = m_Grid.m_DataGridAttributes[idx];
			
			// if the type is list compare the old rangename with the new one
			if (pAttr->m_DataType == DT_LIST) {
				if (pQuery->m_RangeName == "Default") {
					// compare the value to the value in the matrix
					if (pQuery->m_Value != pAttr->m_ListValues[atoi(pAttr->m_Value)]) {
						rc = AfxMessageBox("You have made changes to this screen.  "
							"Do you wish to save the changes?", MB_YESNOCANCEL);
						if (rc == IDYES) {
							OnApply();
							return TRUE;
						}
						else if (rc == IDNO)
							return TRUE;
						else
							return FALSE;
					}

				}
				else {
					if (pQuery->m_RangeName != pAttr->m_ListValues[atoi(pAttr->m_Value)]) {
						// something has changed
						rc = AfxMessageBox("You have made changes to this screen.  "
							"Do you wish to save the changes?", MB_YESNOCANCEL);
						if (rc == IDYES) {
							OnApply();
							return TRUE;
						}
						else if (rc == IDNO)
							return TRUE;
						else
							return FALSE;
					}
				}
			}
			else {	// compare the actual sql
				temp = pQuery->m_Operator;
				pOperator = m_ProductGroupDataService->m_OperatorService.ConvertInternalToDisplay(temp);
				temp = pOperator->m_Display;
				temp += " ";
				temp += pQuery->m_Value;
				if (pOperator->m_OperandCount == 2)
					temp.Replace("^", " ");
				else
					temp.Replace("^", ",");
				if (temp != pAttr->m_Value) {
					// something has changed
					rc = AfxMessageBox("You have made changes to this screen.  "
						"Do you wish to save the changes?", MB_YESNOCANCEL);
					if (rc == IDYES) {
						OnApply();
						return TRUE;
					}
					else if (rc == IDNO)
						return TRUE;
					else
						return FALSE;
				}
			}
			
		}

		// Now we have to loop starting with the columns in case they went
		// from not having a query item (ANY) to having one
		for (col=1; col < cols; ++col) {
			pColAttr = m_Grid.m_DataGridAttributes[col];

			// Find the query that goes with this column
			BOOL found = FALSE;
			for (j=0; j < pOldGroup->m_QueryList.GetSize(); ++j) {
				pQuery = pOldGroup->m_QueryList[j];
				if (pColAttr->m_AttributeID == pQuery->m_CriteriaDBID) {
					found = TRUE;
					break;
				}
			}

			// If we find a query for the column, then we have already checked it above
			if (found)
				continue;

			// Get the actual data cell
			pAttr = m_Grid.m_DataGridAttributes[col+(i+1)*cols];
			if (pAttr->m_DataType == DT_LIST) {
				if (atoi(pAttr->m_Value) > 0) {
					// something has changed
					rc = AfxMessageBox("You have made changes to this screen.  "
						"Do you wish to save the changes?", MB_YESNOCANCEL);
					if (rc == IDYES) {
						OnApply();
						return TRUE;
					}
					else if (rc == IDNO)
						return TRUE;
					else
						return FALSE;
				}
			}
			else {
				if (pAttr->m_Value != "") {
					// something has changed
					rc = AfxMessageBox("You have made changes to this screen.  "
						"Do you wish to save the changes?", MB_YESNOCANCEL);
					if (rc == IDYES) {
						OnApply();
						return TRUE;
					}
					else if (rc == IDNO)
						return TRUE;
					else
						return FALSE;
				}
			}

		}

	}

	return TRUE;
}

int CProductGroupCriteriaMatrix::FindAttribute(int criteriaDBID)
{
	CDataGridAttribute *pAttr;
	int cols = m_Grid.GetCols();

	for (int col=1; col < cols; ++col) {
		pAttr = m_Grid.m_DataGridAttributes[col];

		if (pAttr->m_AttributeID == criteriaDBID)
			return col;
	}

	return -1;
}

int CProductGroupCriteriaMatrix::FindCriteria(int criteriaDBID)
{
	for (int i=0; i < m_CriteriaList->GetSize(); ++i) {
		if (m_CriteriaList->GetAt(i)->m_CriteriaDBID == criteriaDBID)
			return i;
	}

	return -1;
}




BOOL CProductGroupCriteriaMatrix::OnHelpInfo(HELPINFO* pHelpInfo) 
{
	helpService.ShowFieldHelp(IDD, pHelpInfo->iCtrlId);
	return FALSE;
}

void CProductGroupCriteriaMatrix::OnExcel() 
{
	CString fileName;
	CString line;
	
	COleSafeArray outer;
	COleSafeArray *inner;
	SAFEARRAYBOUND rgsabound, rgsabound2;
	long index1;
	long index2;
	int fileCtr;

    rgsabound.lLbound = 0;
    rgsabound.cElements = 3;

	rgsabound2.lLbound = 0;
	rgsabound2.cElements = 2;

	DWORD numElements[] = {2};

	outer.Create(VT_VARIANT, 1, &rgsabound);
	
	inner = new COleSafeArray[3];

	// The maximum number of rows in an Excel worksheet is currently
	// 65536.  So if we have more lines than that, we will have to create
	// multiple files, each with 65536 lines until we have used up all the 
	// lines.  Then we do some bizarre Excel stuff to open each file individually
	// and move them all into a single book with multiple sheets.  If there's
	// a better way, please tell me.

	int itemStart = 0;
	int rowCount;

	try {
		FILE *f;
		rowCount = m_Grid.GetRows() - 1;	// subtract one for the header
		for (int sheetCount=1; sheetCount <= rowCount/MAX_ROWS+1; ++sheetCount) {
		
			CString tempF = getenv("TEMP");
			if (tempF.IsEmpty())
				tempF = "c:";

			for (fileCtr=0; fileCtr < 10; ++fileCtr) {
				fileName.Format("%s\\OptimizeResults%d-%d.xls", tempF, fileCtr, sheetCount);
			
				f = fopen(fileName, "w");
				if (f == NULL) {
					if (errno != EACCES) {
						int x = errno;
						AfxMessageBox("Error creating temporary file.");
						ads_printf("%d\n", errno);
						return;
					}
				}
				else
					break;
			}
		
			// write the header
			for (int j=0; j < 3; ++j) {
				//line = pDoc->m_report.m_txtArray[j];
				//fprintf(f, "\"%s\"|", line);
				// This weirdness is to format each cell as text so it won't
				// take leading zeros off, etc
				// Excel expects an array of two-item arrays; the first
				// item is the column number and the second is the format;
				// text format = 2
				if (sheetCount == 1) {
					inner[j].Create(VT_I2, 1, &rgsabound2);			
					index2 = 0;
					inner[j].PutElement(&index2, &j);
					index2 = 1;
					short k = 2;		// text format
					inner[j].PutElement(&index2, &k);
					index1 = j;
					outer.PutElement(&index1, COleVariant(inner[j]));
				}
				
			}
			
			//fprintf(f, "\n");
			// Add a header to every page
			for (int i=0; i < m_Grid.GetCols(); ++i)
				fprintf(f, "%s|", m_Grid.GetTextMatrix(0, i));

			fprintf(f, "\n");
			
			int itemCount = itemStart + MAX_ROWS;
			if (itemCount > rowCount)
				itemCount = rowCount;

			for (i=itemStart; i < itemCount; ++i) {
				for (int j=0; j < m_Grid.GetCols(); ++j)
					fprintf(f, "%s|", m_Grid.GetTextMatrix(i+1, j));

				fprintf(f, "\n");
			}
			itemStart += MAX_ROWS;

			fclose(f);
		}
		

	}
	catch (...) {
		AfxMessageBox("Error creating file for opening in Excel.");
		return;
	}


	try
	{
		_Application app;     // app is an _Application object.
		_Workbook book, mainBook;       // More object declarations.
		_Worksheet sheet, mainSheet;
		Workbooks books;
		Worksheets sheets, mainSheets;
		Range range;          // Used for Microsoft Excel 97 components.
		LPDISPATCH lpDisp;    // Often reused variable.
		Range cols;

		// Common OLE variants. Easy variants to use for calling arguments.
		COleVariant
			covTrue((short)TRUE),
			covFalse((short)FALSE),
			covOptional((long)DISP_E_PARAMNOTFOUND, VT_ERROR);
		
		// Start Microsoft Excel, get _Application object,
		// and attach to app object.
		if(!app.CreateDispatch("Excel.Application"))
		{
			AfxMessageBox("Couldn't CreateDispatch() for Excel");
			return;
		}
		
		app.SetWindowState(-4140);		// -4140 = xlMinimized
		app.SetVisible(TRUE);
		app.SetUserControl(TRUE);

		// Get the Workbooks collection.
		lpDisp = app.GetWorkbooks();     // Get an IDispatch pointer.
		ASSERT(lpDisp);
		books.AttachDispatch(lpDisp);    // Attach the IDispatch pointer
										// to the books object.

		VARIANT sheetVar = {0};

		for (int sheetCount=1; sheetCount <= rowCount/MAX_ROWS+1; ++sheetCount) {
			
			fileName = getenv("TEMP");
			if (fileName.IsEmpty())
				fileName = "c:";
			
			fileName.Format("%s\\OptimizeResults%d-%d.xls", fileName, fileCtr,sheetCount);
			
			// open the file as text specifiying pipe-delimited
			books.OpenText(fileName, 
				covOptional,				// Origin
				covOptional,				// Start row
				COleVariant((short)(1)),	// Datatype
				1,							// TextQualifier 
				covOptional,				// Consecutive delimiter
				covOptional,				// Tab
				covOptional,				// Semicolon
				covOptional,				// Comma
				covOptional,				// Space
				covTrue,					// Other
				COleVariant("|"),			// OtherChar
				COleVariant(outer),			// FieldInfo
				covOptional);				// TextVisualLayout

		
			// Get the book
			if (sheetCount == 1)
				lpDisp = books.GetItem(COleVariant((short)(1)));
			else
				lpDisp = books.GetItem(COleVariant((short)(2)));
			ASSERT(lpDisp);
			book.AttachDispatch(lpDisp);
			
			// Get the sheets
			lpDisp = book.GetSheets();
			ASSERT(lpDisp);
			sheets.AttachDispatch(lpDisp);
				
			// Get the sheet
			lpDisp = sheets.GetItem(COleVariant((short)1));
			//GetItem(const VARIANT &index)
			ASSERT(lpDisp);
			sheet.AttachDispatch(lpDisp);
			
			CString temp;
			temp.Format("Page %d", sheetCount);
			sheet.SetName(temp);
			
			// Change the entire worksheet to be autofitt
			range = sheet.GetRange(COleVariant("A1"), COleVariant("Z1"));


			Font8 font;
			font = range.GetFont();
			font.SetBold(covTrue);

			cols = range.GetEntireColumn();
			cols.AutoFit();
		
			range = sheet.GetRange(COleVariant("A1"), COleVariant("A99"));
			font = range.GetFont();
			font.SetBold(covTrue);

			if (sheetCount == 1) {
				// Get the first book and hold it for use by the others
				lpDisp = books.GetItem(COleVariant((short)1));
				ASSERT(lpDisp);
				mainBook.AttachDispatch(lpDisp);
				
				// Same for the sheets
				lpDisp = mainBook.GetSheets();
				ASSERT(lpDisp);
				mainSheets.AttachDispatch(lpDisp);

			}

			else {
				// Get the previous sheet we inserted in the main book
				lpDisp = mainSheets.GetItem(COleVariant((short)(sheetCount-1)));
				ASSERT(lpDisp);
				mainSheet.AttachDispatch(lpDisp);
				
				// Create a Variant to represent the previous sheet object
				// We will use this to copy the subsequent sheets
				sheetVar.vt = VT_DISPATCH;
				sheetVar.pdispVal = mainSheet.m_lpDispatch;
				mainSheet.m_lpDispatch->AddRef();
					
				// Add an additional sheet to the new workbook so that we can 
				// move the new sheet without getting an error because there 
				// are no sheets left because a book must have a minimum of one 
				// sheet and we are moving one
				
				// Use the default placement which will put it first
				_Worksheet tempSheet;
				tempSheet = sheets.Add(covOptional, covOptional, covOptional, covOptional);

				// Now move the new sheet to the main book
				sheet.Move(covOptional, sheetVar);

				VariantClear(&sheetVar);

				// Now close the new workbook without saving
				book.Close(covFalse, covOptional, covOptional);

			}

		}

		lpDisp = mainSheets.GetItem(COleVariant((short)1));
		ASSERT(lpDisp);
		sheet.AttachDispatch(lpDisp);
		sheet.Activate();

		app.SetWindowState(-4143);		// -4143 = xlNormal
		app.ReleaseDispatch();

      } // End of processing.

	  catch(COleException *e)
      {
		  char buf[1024];     // For the Try...Catch error message.
		  sprintf(buf, "COleException. SCODE: %08lx.", (long)e->m_sc);
		  //::MessageBox(NULL, buf, "COleException", MB_SETFOREGROUND | MB_OK);
      }
	  
      catch(COleDispatchException *e)
      {
		  char buf[1024];     // For the Try...Catch error message.
		  sprintf(buf,
			  "COleDispatchException. SCODE: %08lx, Description: \"%s\".",
			  (long)e->m_wCode,(LPSTR)e->m_strDescription.GetBuffer(512));
		  //::MessageBox(NULL, buf, "COleDispatchException",
		  //MB_SETFOREGROUND | MB_OK);
	  }
	  
      catch(...)
      {
		  //::MessageBox(NULL, "General Exception caught.", "Catch-All",
		  //MB_SETFOREGROUND | MB_OK);
      }

	  // Clean up the format arrays
	  for (int i=0; i < 3; ++i) {
		  //inner[i].Destroy();	
		  inner[i].Detach();	
	  }
	  delete [] inner;
	  outer.Detach();


	  return;	
}

// InterfaceMapDialog.cpp : implementation file
//

#include "stdafx.h"
#include "modal.h"
#include "ssa_exception.h"
#include "InterfaceMapDialog.h"
#include "ProductDataService.h"
#include "TreeElement.h"
#include "Prompt.h"
#include "UtilityHelper.h"
#include "InterfaceMapUDFDialog.h"

#ifdef _DEBUG
#define new DEBUG_NEW
#undef THIS_FILE
static char THIS_FILE[] = __FILE__;
#endif

/////////////////////////////////////////////////////////////////////////////
// CInterfaceMapDialog dialog


extern TreeElement changesTree;

CInterfaceMapDialog::CInterfaceMapDialog(CWnd* pParent /*=NULL*/)
	: CDialog(CInterfaceMapDialog::IDD, pParent)
{
	//{{AFX_DATA_INIT(CInterfaceMapDialog)
	//}}AFX_DATA_INIT
	m_InterfaceType = -1;
}

CInterfaceMapDialog::~CInterfaceMapDialog()
{
	for (int i=0; i < m_MapList.GetSize(); ++i) {
		delete m_MapList[i];
	}

}

void CInterfaceMapDialog::DoDataExchange(CDataExchange* pDX)
{
	CDialog::DoDataExchange(pDX);
	//{{AFX_DATA_MAP(CInterfaceMapDialog)
	DDX_Control(pDX, IDC_FORMAT, m_FormatListCtrl);
	DDX_Control(pDX, IDC_EXTERNAL_LIST, m_ExternalListCtrl);
	DDX_Control(pDX, IDC_MAP_TYPE, m_MapTypeListCtrl);
	DDX_Control(pDX, IDC_MAPNAME_LIST, m_MapNameListCtrl);
	DDX_Control(pDX, IDC_MAP_LIST, m_InternalListCtrl);
	//}}AFX_DATA_MAP
}


BEGIN_MESSAGE_MAP(CInterfaceMapDialog, CDialog)
	//{{AFX_MSG_MAP(CInterfaceMapDialog)
	ON_BN_CLICKED(IDHELP, OnHelp)
	ON_WM_CONTEXTMENU()
	ON_BN_CLICKED(IDC_SAVE, OnSave)
	ON_BN_CLICKED(IDC_COPY, OnCopy)
	ON_CBN_SELCHANGE(IDC_MAP_LIST, OnSelchangeMapList)
	ON_BN_CLICKED(IDC_MAP, OnMap)
	ON_BN_CLICKED(IDC_UNMAP, OnUnmap)
	ON_BN_CLICKED(IDC_CREATE_UDF, OnCreateUdf)
	ON_BN_CLICKED(IDC_AUTOMAP, OnAutomap)
	ON_BN_CLICKED(IDC_ADD_ATTRIBUTE, OnAddExternalAttribute)
	ON_BN_CLICKED(IDC_REMOVE_ATTRIBUTE, OnRemoveExternalAttribute)
	ON_BN_CLICKED(IDC_LOAD_ATTRIBUTES, OnLoadAttributes)
	ON_LBN_SELCHANGE(IDC_EXTERNAL_LIST, OnSelchangeExternalList)
	ON_WM_HELPINFO()
	ON_CBN_SELCHANGE(IDC_MAPNAME_LIST, OnSelchangeMapnameList)
	ON_CBN_SELCHANGE(IDC_MAP_TYPE, OnSelchangeMapType)
	ON_BN_CLICKED(IDC_MAP_CONSTANT, OnMapConstant)
	ON_NOTIFY(LVN_ENDLABELEDIT, IDC_EXTERNAL_LIST, OnEndlabeleditExternalList)
	ON_BN_CLICKED(IDC_ADD_INTERNAL, OnAddInternal)
	ON_BN_CLICKED(IDC_DELETE_INTERNAL, OnDeleteInternal)
	ON_WM_RBUTTONDBLCLK()
	ON_BN_CLICKED(IDC_REMOVE_UDF, OnRemoveUdf)
	ON_BN_CLICKED(IDC_DELETE_MAP, OnDeleteMap)
	ON_NOTIFY(NM_DBLCLK, IDC_EXTERNAL_LIST, OnDblclkExternalList)
	ON_BN_CLICKED(IDC_MAP_UDF, OnMapUdf)
	ON_COMMAND(ID_GENERIC_ADD, OnAddExternalAttribute)
	ON_COMMAND(ID_GENERIC_DELETE, OnRemoveExternalAttribute)
	ON_BN_CLICKED(IDC_UP, OnUp)
	//}}AFX_MSG_MAP
END_MESSAGE_MAP()

/////////////////////////////////////////////////////////////////////////////
// CInterfaceMapDialog message handlers

BOOL CInterfaceMapDialog::OnInitDialog() 
{
	CDialog::OnInitDialog();
	CRect r;
	int nItem;

	m_bSuperUser = FALSE;

	m_MapNameListCtrl.GetWindowRect(&r);
	m_MapNameListCtrl.SetWindowPos(NULL, 0, 0, r.Width(), r.Height()*10, SWP_NOMOVE|SWP_NOZORDER);

	m_FormatListCtrl.GetWindowRect(&r);
	m_FormatListCtrl.SetWindowPos(NULL, 0, 0, r.Width(), r.Height()*4, SWP_NOMOVE|SWP_NOZORDER);

	nItem = m_FormatListCtrl.AddString("Delimited");
	m_FormatListCtrl.SetItemData(nItem, CInterfaceMap::DelimitedFormat);
	nItem = m_FormatListCtrl.AddString("XML");
	m_FormatListCtrl.SetItemData(nItem, CInterfaceMap::XMLFormat);

	nItem = m_MapTypeListCtrl.AddString("Product Inbound");
	m_MapTypeListCtrl.SetItemData(nItem, CInterfaceMap::ProductInboundType);
	nItem = m_MapTypeListCtrl.AddString("Assignment Inbound");
	m_MapTypeListCtrl.SetItemData(nItem, CInterfaceMap::AssignmentInboundType);
	nItem = m_MapTypeListCtrl.AddString("Location Outbound");
	m_MapTypeListCtrl.SetItemData(nItem, CInterfaceMap::LocationOutboundType);
	nItem = m_MapTypeListCtrl.AddString("Assignment Outbound");
	m_MapTypeListCtrl.SetItemData(nItem, CInterfaceMap::AssignmentOutboundType);
	nItem = m_MapTypeListCtrl.AddString("Move Outbound");
	m_MapTypeListCtrl.SetItemData(nItem, CInterfaceMap::MoveOutboundType);
	
	m_MapTypeListCtrl.GetWindowRect(&r);
	m_MapTypeListCtrl.SetWindowPos(NULL, 0, 0, r.Width(), r.Height()*m_MapTypeListCtrl.GetCount()+1, SWP_NOMOVE|SWP_NOZORDER);

	InitializeMapList();
	
	if (m_InterfaceType >= 0) {
		
		for (int i=0; i < m_MapTypeListCtrl.GetCount(); ++i) {
			if (m_MapTypeListCtrl.GetItemData(i) == (unsigned long)m_InterfaceType) {
				m_MapTypeListCtrl.SetCurSel(i);
				m_PreviousTypeSel = i;
				break;
			}
		}

		LoadMapNameList();
	}

	m_PreviousNameSel = -1;

	CWinApp *pWinApp = AfxGetApp();

	CButton *pButton = (CButton *)GetDlgItem(IDC_UP);
	pButton->SetIcon(pWinApp->LoadIcon(IDI_UP));
	pButton = (CButton *)GetDlgItem(IDC_DOWN);
	pButton->SetIcon(pWinApp->LoadIcon(IDI_DOWN) );


	return TRUE;  // return TRUE unless you set the focus to a control
	              // EXCEPTION: OCX Property Pages should return FALSE
}

void CInterfaceMapDialog::OnOK() 
{
	if (! CheckSave(m_MapNameListCtrl.GetCurSel()  ))
		return;
	
	CDialog::OnOK();
}

void CInterfaceMapDialog::OnCancel() 
{
	if (CheckSave(m_MapNameListCtrl.GetCurSel()))
		CDialog::OnCancel();
}

void CInterfaceMapDialog::OnHelp() 
{
		
}

BOOL CInterfaceMapDialog::OnHelpInfo(HELPINFO* pHelpInfo) 
{
		
	return CDialog::OnHelpInfo(pHelpInfo);
}

void CInterfaceMapDialog::OnContextMenu(CWnd* pWnd, CPoint point) 
{
	CMenu menu;
	int curSel;

	if (pWnd == &m_ExternalListCtrl) {
		menu.LoadMenu(IDR_GENERIC_MENU);
		curSel = m_ExternalListCtrl.GetSelectedCount();
		
		menu.GetSubMenu(0)->ModifyMenu(1, MF_BYPOSITION|MF_STRING, 0, CString("Remove"));

		if (curSel <= 0) {
			menu.GetSubMenu(0)->EnableMenuItem(1, MF_BYPOSITION|MF_GRAYED);	// disable delete
			menu.GetSubMenu(0)->RemoveMenu(2, MF_BYPOSITION); // remove properties
		}
		else {
			menu.GetSubMenu(0)->EnableMenuItem(1, MF_BYPOSITION|MF_ENABLED);
			menu.GetSubMenu(0)->RemoveMenu(2, MF_BYPOSITION);
		}

		menu.GetSubMenu(0)->TrackPopupMenu(TPM_LEFTALIGN|TPM_RIGHTBUTTON, point.x, point.y, this);
	}		
}

void CInterfaceMapDialog::OnSave()
{
	SaveMap();

	return;
}


int CInterfaceMapDialog::SaveMap() 
{
	CInterfaceMapDataService interfaceMapDataService;
	int rc, curSel;
	CButton *pButton = (CButton *)GetDlgItem(IDC_FACILITY_DEFAULT);
	CUtilityHelper utilityHelper;
	CInterfaceMap *pMap;
	
	curSel = m_MapTypeListCtrl.GetCurSel();
	if (curSel < 0) {
		AfxMessageBox("Please select a map type from the list.");
		m_MapTypeListCtrl.SetFocus();
		m_MapTypeListCtrl.ShowDropDown();
		return -1;
	}

	curSel = m_MapNameListCtrl.GetCurSel();
	if (curSel < 0) {
		AfxMessageBox("Please select a map from the list.");
		m_MapNameListCtrl.SetFocus();
		m_MapNameListCtrl.ShowDropDown();
		return -1;
	}

	if (m_CurrentMap.m_InterfaceMapDBID > 0 && m_CurrentMap.m_InterfaceMapDBID < 1000 && ! m_bSuperUser) {
		AfxMessageBox("You cannot save the default interface maps. Use copy to create a new "
			"map and then save your changes.");
		return -1;
	}

	BOOL bNewMap = FALSE;
	if (m_CurrentMap.m_InterfaceMapDBID == 0)
		bNewMap = TRUE;

	try {
		rc = interfaceMapDataService.StoreInterfaceMap(m_CurrentMap);
	}
	catch (Ssa_Exception e) {
		utilityHelper.ProcessError("Error saving interface map.", &e);
		return -1;
	}
	catch (...) {
		utilityHelper.ProcessError("Error saving interface map.");
		return -1;
	}

	/*
	if (pButton->GetCheck())
		interfaceMapDataService.SetFacilityDefaultMap(changesTree.elementDBID, m_CurrentMap.m_InterfaceMapDBID, TRUE);
	else
		interfaceMapDataService.SetFacilityDefaultMap(changesTree.elementDBID, m_CurrentMap.m_InterfaceMapDBID, FALSE);
	*/

	if (bNewMap) {
		pMap = new CInterfaceMap;
		m_MapList.Add(pMap);
		m_MapNameListCtrl.SetItemDataPtr(curSel, (void *)pMap);
	}
	else
		pMap = (CInterfaceMap *)m_MapNameListCtrl.GetItemDataPtr(curSel);

	*pMap = m_CurrentMap;
	
	AfxMessageBox("Map successfully saved.");

	return 0;
}

void CInterfaceMapDialog::OnCopy() 
{
	CPrompt dialog;
	int nItem;
	CButton *pButton;

	if (m_MapTypeListCtrl.GetCurSel() < 0) {
		AfxMessageBox("Please select a map type from the list.");
		m_MapTypeListCtrl.SetFocus();
		m_MapTypeListCtrl.ShowDropDown();
		return;
	}

	if (m_MapNameListCtrl.GetCurSel() < 0) {
		AfxMessageBox("Please select a map from the list.");
		m_MapNameListCtrl.SetFocus();
		m_MapNameListCtrl.ShowDropDown();
		return;
	}

	if (! CheckSave(m_MapNameListCtrl.GetCurSel()))
		return;

	dialog.m_ParameterName = "Enter the name for the new interface map:";

	if (dialog.DoModal() != IDOK)
		return;

	// Always start off with it not the default
	pButton = (CButton *)GetDlgItem(IDC_FACILITY_DEFAULT);
	pButton->SetCheck(FALSE);

	m_CurrentMap.m_InterfaceMapDBID = 0;
	m_CurrentMap.m_Name = dialog.m_ParameterValue;
	for (int i=0; i < m_CurrentMap.m_MappedAttributeList.GetSize(); ++i)
		m_CurrentMap.m_MappedAttributeList[i]->m_InterfaceMapAttributeDBID = 0;

	nItem = m_MapNameListCtrl.AddString(m_CurrentMap.m_Name);
	// Do not add the map to the main list until
	// they save it
	m_MapNameListCtrl.SetItemDataPtr(nItem, NULL);
	m_MapNameListCtrl.SetCurSel(nItem);
	m_PreviousNameSel = nItem;

}

void CInterfaceMapDialog::OnSelchangeMapList() 
{
			
}

void CInterfaceMapDialog::OnMap() 
{
	int extCurSel, intCurSel;
	CString externalAttribute, internalAttribute, oldExternalAttribute, temp;
	CInterfaceMapAttribute *pAttr;

	extCurSel = GetExternalSel();
	if (extCurSel < 0) {
		AfxMessageBox("Please select an external attribute to map.");
		return;
	}
	
	intCurSel = GetInternalSel();
	if (intCurSel < 0) {
		AfxMessageBox("Please select an available attribute to map.");
		return;
	}
	internalAttribute = m_InternalListCtrl.GetItemText(intCurSel, 0);


	if (m_InternalListCtrl.GetItemText(intCurSel, 3) == "Variable") {
		oldExternalAttribute = m_InternalListCtrl.GetItemText(intCurSel, 2);

		temp.Format("%s is already mapped to %s.  Do you wish to replace it?", 
			internalAttribute, externalAttribute);
		if (AfxMessageBox(temp, MB_YESNO) != IDYES)
			return;
		
		// Move the old external value back to the external list
		AddExternalAttribute(oldExternalAttribute);
	}

	// Move the new external value to the internal list
	externalAttribute = m_ExternalListCtrl.GetItemText(extCurSel, 0);
	
	m_InternalListCtrl.SetItemText(intCurSel, 2, externalAttribute);
	m_InternalListCtrl.SetItemText(intCurSel, 3, "Variable");

	m_ExternalListCtrl.DeleteItem(extCurSel);

	pAttr = (CInterfaceMapAttribute *)m_InternalListCtrl.GetItemData(intCurSel);
	pAttr->m_ExternalAttribute = externalAttribute;
	pAttr->m_IsConstant = FALSE;

}

int CInterfaceMapDialog::GetInternalSel()
{
	POSITION pos;

	pos = m_InternalListCtrl.GetFirstSelectedItemPosition();

	if (pos == NULL)
		return -1;

	return m_InternalListCtrl.GetNextSelectedItem(pos);

}


int CInterfaceMapDialog::GetExternalSel()
{
	POSITION pos;

	pos = m_ExternalListCtrl.GetFirstSelectedItemPosition();

	if (pos == NULL)
		return -1;

	return m_ExternalListCtrl.GetNextSelectedItem(pos);

}


void CInterfaceMapDialog::OnUnmap() 
{
	int intCurSel;
	CString externalAttribute, internalAttribute, temp;
	POSITION pos;
	CInterfaceMapAttribute *pAttr;

	intCurSel = -1;
	pos = m_InternalListCtrl.GetFirstSelectedItemPosition();
	if (pos == NULL) {
		AfxMessageBox("Please select at least one mapped attribute to un-map.");
		return;
	}

	while (pos != NULL) {
		intCurSel = m_InternalListCtrl.GetNextSelectedItem(pos);

		pAttr = (CInterfaceMapAttribute *)m_InternalListCtrl.GetItemData(intCurSel);

		externalAttribute = pAttr->m_ExternalAttribute;
		
		if (externalAttribute == "None")
			continue;
		
		if (! pAttr->m_IsConstant)
			AddExternalAttribute(externalAttribute);

		m_InternalListCtrl.SetItemText(intCurSel, 2, "");
		m_InternalListCtrl.SetItemText(intCurSel, 3, "");
		
		pAttr->m_ExternalAttribute = "None";
		pAttr->m_IsConstant = TRUE;
	
	}

}

// Pull up the UDF Maintenance screen
// so they can add a udf to the available attributes
void CInterfaceMapDialog::OnCreateUdf() 
{
	AddUDF("");
}


int CInterfaceMapDialog::AddUDF(const CString &defaultName)
{
	CInterfaceMapUDFDialog dialog;
	CInterfaceMapAttribute *pAttr;
	int mapType;
	int nItem;

	if (m_MapTypeListCtrl.GetCurSel() < 0) {
		AfxMessageBox("Please select an interface type from the list.");
		m_MapTypeListCtrl.SetFocus();
		m_MapTypeListCtrl.ShowDropDown();
		return -1;
	}

	if (m_MapNameListCtrl.GetCurSel() < 0) {
		AfxMessageBox("Please select a map name from the list.");
		m_MapNameListCtrl.SetFocus();
		m_MapNameListCtrl.ShowDropDown();
		return -1;
	}

	mapType = m_MapTypeListCtrl.GetItemData(m_MapTypeListCtrl.GetCurSel());
	dialog.m_ValidElementTypes.RemoveAll();

	switch (mapType) {
	case CInterfaceMap::ProductInboundType:				// allow only product
		dialog.m_ValidElementTypes.Add(UDF_PRODUCT);
		break;
	case CInterfaceMap::MoveOutboundType:				// allow product + all location
	case CInterfaceMap::AssignmentOutboundType:			// allow product + all location
		dialog.m_ValidElementTypes.Add(UDF_PRODUCT);
	case CInterfaceMap::LocationOutboundType:			// allow all location
		dialog.m_ValidElementTypes.Add(UDF_LOCATION);
		dialog.m_ValidElementTypes.Add(UDF_LEVEL);
		dialog.m_ValidElementTypes.Add(UDF_BAY);
		dialog.m_ValidElementTypes.Add(UDF_SIDE);
		dialog.m_ValidElementTypes.Add(UDF_AISLE);
		dialog.m_ValidElementTypes.Add(UDF_SECTION);
		dialog.m_ValidElementTypes.Add(UDF_FACILITY);
		dialog.m_ValidElementTypes.Add(UDF_LEVEL_PROFILE);
		break;
	}

	if (dialog.m_ValidElementTypes.GetSize() == 0) {
		AfxMessageBox("This interface does not allow UDFs");
		return -1;
	}
	
	if (defaultName != "")
		dialog.m_Name = defaultName;

	BOOL bDone = FALSE;
	while (! bDone) {

		if (dialog.DoModal() != IDOK)
			return -1;

		BOOL bFound = FALSE;
		for (int i=0; i < m_InternalListCtrl.GetItemCount(); ++i) {
			if (m_InternalListCtrl.GetItemText(i, 0).CompareNoCase(dialog.m_Name) == 0) {
				AfxMessageBox("The UDF name is already being used.  Please specify a unique name.");
				bFound = TRUE;
				break;
			}
		}
		
		if (! bFound)
			bDone = TRUE;
	}


	pAttr = new CInterfaceMapAttribute;
	pAttr->m_InterfaceMapAttributeDBID = 0;
	pAttr->m_InternalAttribute = dialog.m_Name;
	pAttr->m_ExternalAttribute = "None";
	pAttr->m_IsConstant = TRUE;
	pAttr->m_IsUDF = TRUE;
	pAttr->m_DataType = dialog.m_DataType;
	pAttr->m_UDFElementType = dialog.m_ElementType;

	m_CurrentMap.m_MappedAttributeList.Add(pAttr);

	nItem = DisplayAttribute(pAttr, TRUE);
	
	m_InternalListCtrl.EnsureVisible(nItem,FALSE);

	return nItem;

}

// Attempt to map external attributes to available attributes
// First, map identical spellings
// Then map soundex
void CInterfaceMapDialog::OnAutomap() 
{
	int curSel, i, j;
	CString extText, intText, temp;
	int foundCount;
	CUtilityHelper utilityHelper;
	int rc;

	curSel = m_MapNameListCtrl.GetCurSel();
	if (curSel < 0) {
		AfxMessageBox("Please select a map from the list.");
		return;
	}

	rc = AfxMessageBox("Do you wish to remove all of the current mappings?", MB_YESNOCANCEL);
	if (rc == IDCANCEL)
		return;
	
	if (rc == IDYES) {
		
		for (i=0; i < m_CurrentMap.m_MappedAttributeList.GetSize(); ++i) {
			
			if (m_CurrentMap.m_MappedAttributeList[i]->m_ExternalAttribute != "None")
				AddExternalAttribute(m_CurrentMap.m_MappedAttributeList[i]->m_ExternalAttribute);
			
			m_CurrentMap.m_MappedAttributeList[i]->m_ExternalAttribute = "None";
			m_CurrentMap.m_MappedAttributeList[i]->m_IsConstant = FALSE;
		}
		for (i=0; i < m_InternalListCtrl.GetItemCount(); ++i) {
			m_InternalListCtrl.SetItemText(i, 2, "");
			m_InternalListCtrl.SetItemText(i, 3, "");
		}
	}

	foundCount = 0;
	// Loop through each of the internal attributes, try to find a matching
	// name in the external attributes
	for (i=0; i < m_CurrentMap.m_MappedAttributeList.GetSize(); ++i) {
		if (m_CurrentMap.m_MappedAttributeList[i]->m_ExternalAttribute != "None")
			continue;

		intText = m_CurrentMap.m_MappedAttributeList[i]->m_InternalAttribute;
		intText.Remove('-');
		intText.Remove(' ');
		intText.Remove('_');
		for (j=0; j < m_ExternalListCtrl.GetItemCount(); ++j) {
			extText = m_ExternalListCtrl.GetItemText(j, 0);
			extText.Remove('-');
			extText.Remove(' ');
			extText.Remove('_');
			if (intText.CompareNoCase(extText) == 0) {
				m_CurrentMap.m_MappedAttributeList[i]->m_ExternalAttribute = extText;
				m_CurrentMap.m_MappedAttributeList[i]->m_IsConstant = FALSE;
				m_ExternalListCtrl.DeleteItem(j);
				foundCount++;
				break;
			}
		}
	}
	
	// Now find matches where one side contains the other
	for (i=0; i < m_CurrentMap.m_MappedAttributeList.GetSize(); ++i) {
		if (m_CurrentMap.m_MappedAttributeList[i]->m_ExternalAttribute != "None")
			continue;

		intText = m_CurrentMap.m_MappedAttributeList[i]->m_InternalAttribute;
		intText.Remove('-');
		intText.Remove(' ');
		intText.Remove('_');
		for (j=0; j < m_ExternalListCtrl.GetItemCount(); ++j) {
			extText = m_ExternalListCtrl.GetItemText(j, 0);
			extText.Remove('-');
			extText.Remove(' ');
			extText.Remove('_');
			if (intText.Find(extText, 0) >= 0 || extText.Find(intText, 0) >= 0) {
				m_CurrentMap.m_MappedAttributeList[i]->m_ExternalAttribute = extText;
				m_CurrentMap.m_MappedAttributeList[i]->m_IsConstant = FALSE;
				m_ExternalListCtrl.DeleteItem(j);
				foundCount++;
				break;
			}
		}
	}
	
	// Now do the same loop, but this time use soundex
	for (i=0; i < m_CurrentMap.m_MappedAttributeList.GetSize(); ++i) {
		if (m_CurrentMap.m_MappedAttributeList[i]->m_ExternalAttribute != "None")
			continue;

		intText = m_CurrentMap.m_MappedAttributeList[i]->m_InternalAttribute;
		intText.Remove('-');
		intText.Remove(' ');
		intText.Remove('_');
		for (j=0; j < m_ExternalListCtrl.GetItemCount(); ++j) {
			extText = m_ExternalListCtrl.GetItemText(j, 0);
			extText.Remove('-');
			extText.Remove(' ');
			extText.Remove('_');
			if (utilityHelper.Soundex(intText) == utilityHelper.Soundex(extText)) {
				m_CurrentMap.m_MappedAttributeList[i]->m_ExternalAttribute = extText;
				m_CurrentMap.m_MappedAttributeList[i]->m_IsConstant = FALSE;
				m_ExternalListCtrl.DeleteItem(j);
				foundCount++;
				break;
			}
		}
	}

	DisplayMapAttributes();

	temp.Format("Auto-map complete.  %d matches were found.", foundCount);
	AfxMessageBox(temp);
	
}

void CInterfaceMapDialog::OnAddExternalAttribute() 
{
	int nItem;
	nItem = AddExternalAttribute("New Value");
	
	m_ExternalListCtrl.SetFocus();
	m_ExternalListCtrl.EditLabel(nItem);

}

void CInterfaceMapDialog::OnRemoveExternalAttribute() 
{
	POSITION pos;
	int curSel;
	CArray<int,int&> deleteList;

	pos = m_ExternalListCtrl.GetFirstSelectedItemPosition();

	if (pos == NULL) {
		AfxMessageBox("Please select an external attribute to remove.");
		return;
	}

	while (pos != NULL) {
		curSel = m_ExternalListCtrl.GetNextSelectedItem(pos);
		deleteList.Add(curSel);
	}

	for (int i=deleteList.GetSize()-1; i >= 0; --i) {
		m_ExternalListCtrl.DeleteItem(deleteList[i]);
	}


}

// Load the list of external attributes from a flat or xml file
void CInterfaceMapDialog::OnLoadAttributes() 
{
	CString fileName;
	CStdioFile file;
	CString header, delimiter;
	CEdit *pEdit;
	CUtilityHelper utilityHelper;
	CStringArray strings;

	int curSel, formatType;
	
	if (m_MapTypeListCtrl.GetCurSel() < 0) {
		AfxMessageBox("Please select an interface type from the list.");
		m_MapTypeListCtrl.SetFocus();
		m_MapTypeListCtrl.ShowDropDown();
		return;
	}

	if (m_MapNameListCtrl.GetCurSel() < 0) {
		AfxMessageBox("Please select a map name from the list.");
		m_MapNameListCtrl.SetFocus();
		m_MapNameListCtrl.ShowDropDown();
		return;
	}

	curSel = m_FormatListCtrl.GetCurSel();
	if (curSel < 0) {
		AfxMessageBox("Please select a format type from the list.");
		m_FormatListCtrl.SetFocus();
		m_FormatListCtrl.ShowDropDown();
		return;
	}

	pEdit = (CEdit *)GetDlgItem(IDC_DELIMITER);
	pEdit->GetWindowText(delimiter);

	formatType = m_FormatListCtrl.GetItemData(curSel);
	if (formatType == CInterfaceMap::DelimitedFormat &&	delimiter == "") {
		AfxMessageBox("Please specify a delimiter.");
		pEdit->SetFocus();
		return;
	}

	if (formatType == CInterfaceMap::XMLFormat) {
		AfxMessageBox("XML format is not supported at this time.");
		return;
	}

	if (GetFile(fileName, formatType) < 0)
		return;

	if (! file.Open(fileName, CFile::modeRead|CFile::typeText)) {
		AfxMessageBox("Unable to open file.");
		return;
	}

	if (! file.ReadString(header)) {
		AfxMessageBox("Unable to read file header.");
		return;
	}

	file.Close();

	m_ExternalListCtrl.DeleteAllItems();

	utilityHelper.ParseString(header, delimiter, strings);
	
	for (int i=0; i < strings.GetSize(); ++i) {
		CString temp = strings[i];
		temp.TrimRight();
		temp.TrimLeft();
		if (temp == "")
			continue;

		BOOL bFound = FALSE;
		for (int j=0; j < m_CurrentMap.m_MappedAttributeList.GetSize(); ++j) {
			if (m_CurrentMap.m_MappedAttributeList[j]->m_ExternalAttribute == temp) {
				bFound = TRUE;
				break;
			}
		}

		if (bFound)
			continue;


		AddExternalAttribute(temp);
	}

	


}

void CInterfaceMapDialog::OnSelchangeExternalList() 
{
		
}

BOOL CInterfaceMapDialog::OnNotify(WPARAM wParam, LPARAM lParam, LRESULT* pResult) 
{
		
	return CDialog::OnNotify(wParam, lParam, pResult);
}

// Define columns on map list control
void CInterfaceMapDialog::InitializeMapList()
{
	CRect r;
	int n;

	n = m_InternalListCtrl.GetHeaderCtrl()->GetItemCount();
	for (int i=0; i < n; ++i)
		m_InternalListCtrl.DeleteColumn(0);

	m_InternalListCtrl.GetClientRect(&r);
	m_InternalListCtrl.InsertColumn(0, "Internal Attribute", LVCFMT_LEFT, r.Width()*7/24, 0);
	m_InternalListCtrl.InsertColumn(1, "Data Type", LVCFMT_LEFT, r.Width()*6/24, 0);
	m_InternalListCtrl.InsertColumn(2, "External Attribute", LVCFMT_LEFT, r.Width()*7/24, 0);
	m_InternalListCtrl.InsertColumn(3, "Map Type", LVCFMT_LEFT, r.Width()*4/24, 0);
	

	n = m_ExternalListCtrl.GetHeaderCtrl()->GetItemCount();
	for (i=0; i < n; ++i)
		m_ExternalListCtrl.DeleteColumn(0);

	m_InternalListCtrl.GetClientRect(&r);
	m_ExternalListCtrl.InsertColumn(0, "Attribute Name", LVCFMT_LEFT, r.Width(), 0);


	/*
	n = m_ExternalListCtrl.GetHeaderCtrl()->GetItemCount();
	for (int i=0; i < nm; ++i)
		m_ExternalListCtrl.DeleteColumn(0);
	*/
	
}

// Load existing map configurations from database
int CInterfaceMapDialog::LoadMapNameList()
{
	CInterfaceMapDataService interfaceMapDataService;
	CStringArray results;
	int rc;
	CInterfaceMap *pMap;
	CEdit *pEdit;

	for (int i=0; i < m_MapList.GetSize(); ++i)
		delete m_MapList[i];

	m_MapList.RemoveAll();
	m_MapNameListCtrl.ResetContent();
	m_InternalListCtrl.DeleteAllItems();
	m_ExternalListCtrl.DeleteAllItems();

	for (i=0; i < m_CurrentMap.m_MappedAttributeList.GetSize(); ++i)
		delete m_CurrentMap.m_MappedAttributeList[i];
	m_CurrentMap.m_MappedAttributeList.RemoveAll();

	m_FormatListCtrl.SetCurSel(-1);
	pEdit = (CEdit *)GetDlgItem(IDC_DELIMITER);
	pEdit->SetWindowText("");


	rc = interfaceMapDataService.GetInterfaceMapList(m_InterfaceType, results);
	
	for (i=0; i < results.GetSize(); ++i) {
		pMap = new CInterfaceMap;
		pMap->Parse(results[i]);
		m_MapList.Add(pMap);
		int nItem = m_MapNameListCtrl.AddString(pMap->m_Name);
		m_MapNameListCtrl.SetItemDataPtr(nItem, (void *)pMap);
	}

	m_PreviousNameSel = -1;

	return 0;
}

// Load product attributes into map list control
int CInterfaceMapDialog::LoadMapAttributes(CInterfaceMap *pMap)
{
	CInterfaceMapDataService interfaceMapDataService;
	CStringArray results;
	int rc;
	CInterfaceMapAttribute *pMapAttr;

	// We may have already loaded them
	if (pMap->m_MappedAttributeList.GetSize() > 0)
		return 0;

	rc = interfaceMapDataService.GetInterfaceMapAttributes(pMap->m_InterfaceMapDBID, results);

	for (int i=0; i < results.GetSize(); ++i) {
		pMapAttr = new CInterfaceMapAttribute;
		pMapAttr->Parse(results[i]);
		pMap->m_MappedAttributeList.Add(pMapAttr);
	}
		
	return 0;
}

void CInterfaceMapDialog::OnSelchangeMapnameList() 
{
	int curSel;
	CButton *pButton;
	CInterfaceMapDataService interfaceMapDataService;
	CInterfaceMap *pMap;
	CEdit *pEdit;

	curSel = m_MapNameListCtrl.GetCurSel();
	if (curSel < 0 || curSel == m_PreviousNameSel) {
		m_PreviousNameSel = curSel;
		return;
	}

	if (m_PreviousNameSel >= 0) {
		if (! CheckSave(m_PreviousNameSel)) {
			// Need to set the name back to previous
			m_MapNameListCtrl.SetCurSel(m_PreviousNameSel);
			return;
		}
	}


	pMap = (CInterfaceMap *)m_MapNameListCtrl.GetItemDataPtr(curSel);

	LoadMapAttributes(pMap);

	m_CurrentMap = *pMap;

	if (m_CurrentMap.m_IsNamed) {
		pButton = (CButton *)GetDlgItem(IDC_NAMED);
		pButton->SetCheck(TRUE);
	}
	else {
		pButton = (CButton *)GetDlgItem(IDC_POSITIONAL);
		pButton->SetCheck(TRUE);
	}

	m_FormatListCtrl.SetCurSel(-1);

	for (int i=0; i < m_FormatListCtrl.GetCount(); ++i) {
		if (m_FormatListCtrl.GetItemData(i) == (unsigned long)m_CurrentMap.m_FormatType) {
			m_FormatListCtrl.SetCurSel(i);
			break;
		}
	}

	pEdit = (CEdit *)GetDlgItem(IDC_DELIMITER);
	pEdit->SetWindowText(m_CurrentMap.m_Delimiter);

	pButton = (CButton *)GetDlgItem(IDC_FACILITY_DEFAULT);
	pButton->SetCheck(interfaceMapDataService.IsFacilityDefaultMap(changesTree.elementDBID, 
		m_CurrentMap.m_InterfaceMapDBID));


	DisplayMapAttributes();

	m_PreviousNameSel = curSel;
}

void CInterfaceMapDialog::OnSelchangeMapType() 
{
	if (! CheckSave(m_MapNameListCtrl.GetCurSel()) ) {
		m_MapTypeListCtrl.SetCurSel(m_PreviousTypeSel);
		return;
	}

	m_InterfaceType = m_MapTypeListCtrl.GetItemData(m_MapTypeListCtrl.GetCurSel());
	m_PreviousTypeSel = m_MapTypeListCtrl.GetCurSel();

	LoadMapNameList();

}

// Prompt the user for a constant value
void CInterfaceMapDialog::OnMapConstant() 
{
	CPrompt dialog;
	CString temp, oldExternalAttribute;
	CInterfaceMapAttribute *pAttr;
	BOOL bDone;
	CUtilityHelper utilityHelper;

	int intCurSel = GetInternalSel();

	if (intCurSel < 0) {
		AfxMessageBox("Please select an available attribute to map.");
		return;
	}

	pAttr = (CInterfaceMapAttribute *)m_InternalListCtrl.GetItemData(intCurSel);

	switch (pAttr->m_DataType) {
	case DT_INT:
		temp = "integer";
		break;
	case DT_FLOAT:
		temp = "float";
		break;
	case DT_STRING:
		temp = "text";
		break;
	}

	dialog.m_Title = "Map Constant";

	dialog.m_ParameterName.Format("Enter %s constant value:", temp);

	bDone = FALSE;

	while (! bDone) {

		if (dialog.DoModal() == IDCANCEL)
			return;

		// Validate the data type
		temp = dialog.m_ParameterValue;
		if (pAttr->m_DataType == DT_INT && ! utilityHelper.IsInteger(temp))
			AfxMessageBox("Please enter a valid integer.");
		else if (pAttr->m_DataType == DT_FLOAT && ! utilityHelper.IsFloat(temp))
			AfxMessageBox("Please enter a valid floating point number.");
		else
			bDone = TRUE;

	}

	oldExternalAttribute = m_InternalListCtrl.GetItemText(intCurSel, 2);
	if (oldExternalAttribute != "") {
		temp.Format("%s is already mapped to %s.  Do you wish to replace it?", 
			m_InternalListCtrl.GetItemText(intCurSel, 0), oldExternalAttribute);
		if (AfxMessageBox(temp, MB_YESNO) != IDYES)
			return;
		AddExternalAttribute(oldExternalAttribute);
	}

	
	pAttr->m_ExternalAttribute = dialog.m_ParameterValue;
	pAttr->m_IsConstant = TRUE;

	m_InternalListCtrl.SetItemText(intCurSel, 2, pAttr->m_ExternalAttribute);
	m_InternalListCtrl.SetItemText(intCurSel, 3, "Constant");


}



void CInterfaceMapDialog::OnEndlabeleditExternalList(NMHDR* pNMHDR, LRESULT* pResult) 
{
	LV_DISPINFO* pDispInfo = (LV_DISPINFO*)pNMHDR;
	LVITEM lvItem;

	*pResult = 0;
	lvItem = pDispInfo->item;	

	if (lvItem.pszText == NULL) {
		if (m_ExternalListCtrl.GetItemText(lvItem.iItem, 0) == "New Value")
			lvItem.pszText = "New Value";
	}
	
	if (lvItem.pszText != NULL) {
		for (int i=0; i < lvItem.iItem; ++i) {
			if (m_ExternalListCtrl.GetItemText(i, 0).Compare(lvItem.pszText) == 0) {
				AfxMessageBox("Please enter a name that does not already exist.");
				if (m_ExternalListCtrl.GetItemText(lvItem.iItem, 0) == "New Value") {
					m_ExternalListCtrl.DeleteItem(lvItem.iItem);
					OnAddExternalAttribute();
				}
				return;
			}
		}
		*pResult = 1;
	}

	return;
}

int CInterfaceMapDialog::CheckSave(int curSel)
{
	CInterfaceMap *pMap;	

	// Nothing currently selected
	if (curSel < 0)
		return 1;

	pMap = (CInterfaceMap *)m_MapNameListCtrl.GetItemDataPtr(curSel);

	// The current selection hasn't changed and
	// it's not a new entry
	if (pMap != NULL) {
		if (*pMap == m_CurrentMap && m_CurrentMap.m_InterfaceMapDBID > 0)
			return 1;
	}

	switch (AfxMessageBox("The current interface map has changed.\n"
		"Do you wish to save the changes?", MB_YESNOCANCEL)) {
	case IDCANCEL:
		return 0;
	case IDNO:
		// If it's a new map and they don't want to save,
		// get rid of the name in the list
		if (m_CurrentMap.m_InterfaceMapDBID <= 0)
			m_MapNameListCtrl.DeleteString(curSel);
		return 1;
	case IDYES:
		if (SaveMap() < 0)
			return 0;
	}

	return 1;

	
}

void CInterfaceMapDialog::OnAddInternal() 
{
	CPrompt dialog;
	CString name, temp;
	int dataType;
	CInterfaceMapAttribute *pAttr;
	int curSel, nItem;

	curSel = m_MapNameListCtrl.GetCurSel();
	if (curSel < 0) {
		AfxMessageBox("Please select a map from the dropdown list.");
		return;
	}

	dialog.m_ParameterName = "Enter name,data type: ";
	if (dialog.DoModal() !=	IDOK)
		return;

	int idx = dialog.m_ParameterValue.Find(",");
	if (idx < 0) {
		name = dialog.m_ParameterValue;
		// default to text
		dataType = DT_STRING;
	}
	else {
		name = dialog.m_ParameterValue.Left(idx);
		temp = dialog.m_ParameterValue.Mid(idx+1);
		dataType = atoi(temp);
	}

	pAttr = new CInterfaceMapAttribute;
	pAttr->m_DataType = dataType;
	pAttr->m_InternalAttribute = name;
	pAttr->m_ExternalAttribute = "None";
	pAttr->m_IsConstant = TRUE;
	pAttr->m_IsUDF = FALSE;

	m_CurrentMap.m_MappedAttributeList.Add(pAttr);


	nItem = DisplayAttribute(pAttr, TRUE);
	
	m_InternalListCtrl.EnsureVisible(nItem,FALSE);

	return;

}

void CInterfaceMapDialog::OnDeleteInternal() 
{
	int curSel;
	CString text, extText;
	CInterfaceMapAttribute *pAttr;

	curSel = m_MapNameListCtrl.GetCurSel();
	if (curSel < 0) {
		AfxMessageBox("Please select a map from the dropdown list.");
		return;
	}
	
	curSel = GetInternalSel();
	if (curSel < 0) {
		AfxMessageBox("Please select an internal attribute to delete.");
		return;
	}

	if (AfxMessageBox("Warning! Deleting an attribute from a map may make the map unusable.\n"
		"Do you wish to continue?", MB_YESNO) != IDYES)
		return;

	// Add back the mapped attribute before deleting
	pAttr = (CInterfaceMapAttribute *)m_InternalListCtrl.GetItemData(curSel);
	if (! pAttr->m_IsConstant && pAttr->m_ExternalAttribute != "None")
		AddExternalAttribute(pAttr->m_ExternalAttribute);
	
	for (int i=0; i < m_CurrentMap.m_MappedAttributeList.GetSize(); ++i) {
		if (m_CurrentMap.m_MappedAttributeList[i] == pAttr) {
			delete m_CurrentMap.m_MappedAttributeList[i];
			m_CurrentMap.m_MappedAttributeList.RemoveAt(i);
			break;
		}
	}
	
	m_InternalListCtrl.DeleteItem(curSel);

	return;

}

void CInterfaceMapDialog::OnRButtonDblClk(UINT nFlags, CPoint point) 
{
	if (nFlags & MK_CONTROL) {
		if (GetDlgItem(IDC_ADD_INTERNAL)->IsWindowVisible()) {
			GetDlgItem(IDC_ADD_INTERNAL)->ShowWindow(SW_HIDE);
			GetDlgItem(IDC_DELETE_INTERNAL)->ShowWindow(SW_HIDE);
			m_bSuperUser = FALSE;
		}
		else {
			GetDlgItem(IDC_ADD_INTERNAL)->ShowWindow(SW_SHOW);
			GetDlgItem(IDC_DELETE_INTERNAL)->ShowWindow(SW_SHOW);
			m_bSuperUser = TRUE;
		}
	}
	
	CDialog::OnRButtonDblClk(nFlags, point);
}

int CInterfaceMapDialog::DisplayMapAttributes()
{
	CInterfaceMapAttribute *pAttr;

	m_InternalListCtrl.DeleteAllItems();

	for (int i=0; i < m_CurrentMap.m_MappedAttributeList.GetSize(); ++i) {
		pAttr = m_CurrentMap.m_MappedAttributeList[i];
		DisplayAttribute(pAttr, FALSE);
	}

	return 0;
}

int CInterfaceMapDialog::DisplayAttribute(CInterfaceMapAttribute *pAttr, BOOL bIsSelected)
{
	LVITEM lvItem;
	int nItem;
	CString temp;
	CUtilityHelper utilityHelper;
	
	lvItem.mask = LVIF_TEXT|LVIF_PARAM|LVIF_STATE;
	lvItem.pszText = pAttr->m_InternalAttribute.GetBuffer(0);
	pAttr->m_InternalAttribute.ReleaseBuffer();
	lvItem.lParam = (long)pAttr;
	lvItem.iItem = m_InternalListCtrl.GetItemCount();
	lvItem.iSubItem = 0;
	lvItem.stateMask = LVIS_SELECTED;
	if (bIsSelected)
		lvItem.state = LVIS_SELECTED;
	else
		lvItem.state = 0;
	
	nItem = m_InternalListCtrl.InsertItem(&lvItem);
	
	if (pAttr->m_IsUDF) {
		temp = "UDF ";
		temp = "UDF ";
		temp += "(";
		temp += utilityHelper.GetElementTypeAsText(pAttr->m_UDFElementType);
		temp += ") ";
	}
	else
		temp = "Static ";
	
	temp += utilityHelper.GetDataTypeAsText(pAttr->m_DataType);
	m_InternalListCtrl.SetItemText(nItem, 1, temp);

	if (pAttr->m_ExternalAttribute != "None") {
		m_InternalListCtrl.SetItemText(nItem, 2, pAttr->m_ExternalAttribute);
		if (pAttr->m_IsConstant)
			m_InternalListCtrl.SetItemText(nItem, 3, "Constant");
		else
			m_InternalListCtrl.SetItemText(nItem, 3, "Variable");		
	}
	else {
		m_InternalListCtrl.SetItemText(nItem, 2, "");
		m_InternalListCtrl.SetItemText(nItem, 3, "");
	}


	return nItem;
}

void CInterfaceMapDialog::OnRemoveUdf() 
{
	int curSel;
	CString text;
	CInterfaceMapAttribute *pAttr;

	curSel = m_MapNameListCtrl.GetCurSel();
	if (curSel < 0) {
		AfxMessageBox("Please select a map from the dropdown list.");
		return;
	}
	
	curSel = GetInternalSel();
	if (curSel < 0) {
		AfxMessageBox("Please select a UDF attribute to delete.");
		return;
	}

	pAttr = (CInterfaceMapAttribute *)m_InternalListCtrl.GetItemData(curSel);
	if (! pAttr->m_IsUDF) {
		AfxMessageBox("Only UDFs can be deleted from the map.");
		return;
	}

	// Add back the mapped attribute before deleting
	if (! pAttr->m_IsConstant && pAttr->m_ExternalAttribute != "None")
		AddExternalAttribute(pAttr->m_ExternalAttribute);
	
	for (int i=0; i < m_CurrentMap.m_MappedAttributeList.GetSize(); ++i) {
		if (m_CurrentMap.m_MappedAttributeList[i] == pAttr) {
			delete m_CurrentMap.m_MappedAttributeList[i];
			m_CurrentMap.m_MappedAttributeList.RemoveAt(i);
			break;
		}
	}
	
	m_InternalListCtrl.DeleteItem(curSel);





}

int CInterfaceMapDialog::AddExternalAttribute(const CString &text)
{
	LVITEM lvItem;
	int nItem;
	CString temp = text;

	for (int i=0; i < m_ExternalListCtrl.GetItemCount(); ++i) {
		if (text.CompareNoCase(m_ExternalListCtrl.GetItemText(i, 0)) == 0)
			return 0;
	}

	lvItem.mask = LVIF_TEXT | LVIF_STATE;
	lvItem.stateMask = LVIS_SELECTED;
	lvItem.state = 0;
	lvItem.pszText = temp.GetBuffer(0);
	temp.ReleaseBuffer();
	lvItem.iItem = m_ExternalListCtrl.GetItemCount();
	lvItem.iSubItem = 0;
	nItem = m_ExternalListCtrl.InsertItem(&lvItem);

	return nItem;
}

void CInterfaceMapDialog::OnDeleteMap() 
{
	int curSel;
	CInterfaceMap *pMap;

	if (m_MapTypeListCtrl.GetCurSel() < 0) {
		AfxMessageBox("Please select an interface type from the list.");
		m_MapTypeListCtrl.SetFocus();
		m_MapTypeListCtrl.ShowDropDown();
		return;
	}

	curSel = m_MapNameListCtrl.GetCurSel();
	if (curSel < 0) {
		AfxMessageBox("Please select a map name from the list.");
		m_MapNameListCtrl.SetFocus();
		m_MapNameListCtrl.ShowDropDown();
		return;
	}

	if (m_CurrentMap.m_InterfaceMapDBID > 0 && m_CurrentMap.m_InterfaceMapDBID < 1000) {
		AfxMessageBox("You cannot delete the default interface maps.");
		return;
	}

	pMap = (CInterfaceMap *)m_MapNameListCtrl.GetItemDataPtr(curSel);
	// If it's not null, we must have already saved it once so remove it from the
	// database and the master list
	if (pMap != NULL) {

		CInterfaceMapDataService interfaceMapDataService;
		
		try {
			interfaceMapDataService.DeleteInterfaceMap(pMap->m_InterfaceMapDBID);
		}
		catch (...) {
			CUtilityHelper utilityHelper;
			utilityHelper.ProcessError("Error deleting interface map.");
		}

		for (int i=0; i < m_MapList.GetSize(); ++i) {
			if (m_MapList[i] == pMap) {
				m_MapList.RemoveAt(i);
				break;
			}
		}

		// delete pointer
		delete pMap;
	}
	
	// Whether or not we have saved it before, we have to 
	// clear out the screen and the current map object
	m_MapNameListCtrl.SetCurSel(-1);
	m_PreviousNameSel = -1;
	m_MapNameListCtrl.DeleteString(curSel);
	m_ExternalListCtrl.DeleteAllItems();
	m_CurrentMap.Clear();
	m_InternalListCtrl.DeleteAllItems();
	
	CEdit *pEdit = (CEdit *)GetDlgItem(IDC_DELIMITER);
	pEdit->SetWindowText("");
	
	m_FormatListCtrl.SetCurSel(-1);
	
	UpdateData(FALSE);

		
}

int CInterfaceMapDialog::GetFile(CString &fileName, int formatType)
{
	CFileDialog dlgFile(TRUE);
	CString title;
	CString strFilter;
	CString strDefault;

	CString allFilter;
	VERIFY(allFilter.LoadString(AFX_IDS_ALLFILTER));
	if (formatType == CInterfaceMap::DelimitedFormat) {
		strFilter += CString("Interface Data Files (*.dat)");
		strFilter += (TCHAR)'\0';   
		strFilter += _T("*.dat");
		dlgFile.m_ofn.lpstrDefExt = "dat";
	}
	else if (formatType == CInterfaceMap::XMLFormat) {
		strFilter += CString("Interface Data Files (*.xml)");
		strFilter += (TCHAR)'\0';   
		strFilter += _T("*.xml");
		dlgFile.m_ofn.lpstrDefExt = "xml";
	}
	strFilter += (TCHAR)'\0';
	strFilter += allFilter;
	strFilter += (TCHAR)'\0';   
	strFilter += _T("*.*");
	strFilter += (TCHAR)'\0';   

	dlgFile.m_ofn.lpstrFilter = strFilter;
	dlgFile.m_ofn.lpstrTitle = "Load Interface File Attributes";
	dlgFile.m_ofn.lpstrFile = fileName.GetBuffer(_MAX_PATH);
	BOOL bResult = dlgFile.DoModal() == IDOK ? TRUE : FALSE;

	
	fileName.ReleaseBuffer();
	
	if (! bResult)
		return -1;
	else
		return 0;

}

void CInterfaceMapDialog::OnDblclkExternalList(NMHDR* pNMHDR, LRESULT* pResult) 
{
	*pResult = 0;

	OnMap();

}

void CInterfaceMapDialog::OnMapUdf() 
{
	int curSel;
	CString defaultName;

	curSel = GetExternalSel();
	if (curSel < 0) {
		AfxMessageBox("Please select an external field for which to create a UDF.");
		return;
	}

	defaultName = m_ExternalListCtrl.GetItemText(curSel, 0);

	// Make sure no internal attributes are selected
	for (int i=0; i < m_InternalListCtrl.GetItemCount(); ++i)
		m_InternalListCtrl.SetItemState(i, 0, LVIS_SELECTED);

	if (AddUDF(defaultName) < 0)
		return;

	OnMap();


}

void CInterfaceMapDialog::OnUp() 
{
		
}

#if !defined(AFX_BAYPROFILEFLOORPAGE_H__2B24C4BC_383C_4DEF_8EF2_3DFF963BF036__INCLUDED_)
#define AFX_BAYPROFILEFLOORPAGE_H__2B24C4BC_383C_4DEF_8EF2_3DFF963BF036__INCLUDED_

#if _MSC_VER > 1000
#pragma once
#endif // _MSC_VER > 1000
// BayProfileFloorPage.h : header file
//
#include "BayProfileTopViewButton.h"
#include "BayProfileSideViewButton.h"
#include "BayProfile.h"
/////////////////////////////////////////////////////////////////////////////
// CBayProfileFloorPage dialog

class CBayProfileFloorPage : public CPropertyPage
{
	DECLARE_DYNCREATE(CBayProfileFloorPage)

// Construction
public:
	CBayProfileFloorPage();
	~CBayProfileFloorPage();

// Dialog Data
	//{{AFX_DATA(CBayProfileFloorPage)
	enum { IDD = IDD_BAY_PROFILE_FLOOR_ATTRIBUTES };
	CBayProfileTopViewButton	m_TopViewButton;
	CBayProfileSideViewButton	m_SideViewButton;
	double	m_BayHeight;
	double	m_ReserveStackHeight;
	double	m_ReserveStackPositions;
	double	m_SelectStackHeight;
	double	m_SelectStackPositions;
	double	m_StackDepth;
	double	m_StackWidth;
	double	m_WeightCapacity;
	double	m_BayDepth;
	double	m_BayWidth;
	//}}AFX_DATA


// Overrides
	// ClassWizard generate virtual function overrides
	//{{AFX_VIRTUAL(CBayProfileFloorPage)
	public:
	virtual BOOL OnKillActive();
	virtual BOOL OnSetActive();
	protected:
	virtual void DoDataExchange(CDataExchange* pDX);    // DDX/DDV support
	virtual BOOL OnCommand(WPARAM wParam, LPARAM lParam);
	virtual BOOL OnNotify(WPARAM wParam, LPARAM lParam, LRESULT* pResult);
	//}}AFX_VIRTUAL

// Implementation
protected:
	// Generated message map functions
	//{{AFX_MSG(CBayProfileFloorPage)
	virtual BOOL OnInitDialog();
	afx_msg BOOL OnHelpInfo(HELPINFO* pHelpInfo);
	//}}AFX_MSG
	DECLARE_MESSAGE_MAP()

private:
	CBayProfile *m_pBayProfile;
	BOOL Validate();
	BOOL m_Validating;
};

//{{AFX_INSERT_LOCATION}}
// Microsoft Visual C++ will insert additional declarations immediately before the previous line.

#endif // !defined(AFX_BAYPROFILEFLOORPAGE_H__2B24C4BC_383C_4DEF_8EF2_3DFF963BF036__INCLUDED_)

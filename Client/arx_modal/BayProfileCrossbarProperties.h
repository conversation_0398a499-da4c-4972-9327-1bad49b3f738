#if !defined(AFX_BAYPROFILECROSSBARPROPERTIES_H__3D443B93_37FC_4FC5_A8EC_3343F3F595A3__INCLUDED_)
#define AFX_BAYPROFILECROSSBARPROPERTIES_H__3D443B93_37FC_4FC5_A8EC_3343F3F595A3__INCLUDED_

#if _MSC_VER > 1000
#pragma once
#endif // _MSC_VER > 1000
// BayProfileCrossbarProperties.h : header file
//
#include "BayProfile.h"
/////////////////////////////////////////////////////////////////////////////
// CBayProfileCrossbarProperties dialog

class CBayProfileCrossbarProperties : public CDialog
{
// Construction
public:
	CBayProfileCrossbarProperties(CWnd* pParent = NULL);   // standard constructor
	CBayProfile *m_pBayProfile;
	int m_CurrentLevel;
// Dialog Data
	//{{AFX_DATA(CBayProfileCrossbarProperties)
	enum { IDD = IDD_BAY_PROFILE_CROSSBAR_PROPERTIES };
	BOOL	m_Hidden;
	double	m_Clearance;
	double	m_Position;
	double	m_Overhang;
	double	m_Thickness;
	double	m_WeightCapacity;
	//}}AFX_DATA


// Overrides
	// ClassWizard generated virtual function overrides
	//{{AFX_VIRTUAL(CBayProfileCrossbarProperties)
	protected:
	virtual void DoDataExchange(CDataExchange* pDX);    // DDX/DDV support
	//}}AFX_VIRTUAL

// Implementation
protected:

	// Generated message map functions
	//{{AFX_MSG(CBayProfileCrossbarProperties)
	virtual BOOL OnInitDialog();
	virtual void OnOK();
	virtual void OnCancel();
	afx_msg void OnHelp();
	afx_msg BOOL OnHelpInfo(HELPINFO* pHelpInfo);
	//}}AFX_MSG
	DECLARE_MESSAGE_MAP()

private:
	void UpdateCrossbarRanges();
	void GetClearanceRange(int currentLevel, int& lower, int& upper);
	void GetPositionRange(int currentLevel, int& lower, int& upper);
	void GetThicknessRange(int currentLevel, int& lower, int& upper);
};

//{{AFX_INSERT_LOCATION}}
// Microsoft Visual C++ will insert additional declarations immediately before the previous line.

#endif // !defined(AFX_BAYPROFILECROSSBARPROPERTIES_H__3D443B93_37FC_4FC5_A8EC_3343F3F595A3__INCLUDED_)

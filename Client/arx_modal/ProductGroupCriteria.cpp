// ProductGroupCriteria.cpp: implementation of the CProductGroupCriteria class.
//
//////////////////////////////////////////////////////////////////////

#include "stdafx.h"
#include "modal.h"
#include "ProductGroupCriteria.h"

#include "dbsymtb.h"

#ifdef _DEBUG
#undef THIS_FILE
static char THIS_FILE[]=__FILE__;
#define new DEBUG_NEW
#endif

//////////////////////////////////////////////////////////////////////
// Construction/Destruction
//////////////////////////////////////////////////////////////////////

CProductGroupCriteria::CProductGroupCriteria()
{
	m_CriteriaDBID = -1;
	m_Name = "";
	m_Description = "";
	m_IsDiscrete = FALSE;
	m_Attribute = "";
	m_AttributeType = -1;

	m_CriteriaRangeList.RemoveAll();
	m_CriteriaValueList.RemoveAll();
}

CProductGroupCriteria::~CProductGroupCriteria()
{
	for (int i=0; i < m_CriteriaRangeList.GetSize(); ++i) {
		delete m_CriteriaRangeList[i];
	}

	m_CriteriaRangeList.RemoveAll();	
	
	for (i=0; i < m_CriteriaValueList.GetSize(); ++i) {
		delete m_CriteriaValueList[i];
	}
	m_CriteriaValueList.RemoveAll();

	return;

}

CProductGroupCriteria& CProductGroupCriteria::operator=(const CProductGroupCriteria &other)
{
	CProductGroupCriteriaRange *pCriteriaRange;
	CProductGroupCriteriaValue *pCriteriaValue;

	m_CriteriaDBID = other.m_CriteriaDBID;
	m_Name = other.m_Name;
	m_Description = other.m_Description;
	m_IsDiscrete = other.m_IsDiscrete;
	m_Attribute = other.m_Attribute;
	m_AttributeType = other.m_AttributeType;

	for (int i=0; i < m_CriteriaRangeList.GetSize(); ++i)
		delete m_CriteriaRangeList[i];

	m_CriteriaRangeList.RemoveAll();

	for (i=0; i < other.m_CriteriaRangeList.GetSize(); ++i) {
		pCriteriaRange = new CProductGroupCriteriaRange;
		*pCriteriaRange = *(other.m_CriteriaRangeList[i]);
		m_CriteriaRangeList.Add(pCriteriaRange);
	}

	for (i=0; i < m_CriteriaValueList.GetSize(); ++i)
		delete m_CriteriaValueList[i];

	m_CriteriaValueList.RemoveAll();

	for (i=0; i < other.m_CriteriaValueList.GetSize(); ++i) {
		pCriteriaValue = new CProductGroupCriteriaValue;
		*pCriteriaValue = *(other.m_CriteriaValueList[i]);
		m_CriteriaValueList.Add(pCriteriaValue);
	}

	return *this;
}

int CProductGroupCriteria::Parse(CString &line)
{
	char *str;
	char *ptr;
	CString tmp;
	int idx;

	tmp = line;

	try {
		// strtok doesn't interpret double pipes as a field
		idx = tmp.Find("||");
		while (idx >= 0) {
			tmp.Insert(idx+1, " ");
			idx = tmp.Find("||");
		}

		str = tmp.GetBuffer(0);
		
		ptr = strtok(str, "|");
		m_CriteriaDBID = atoi(ptr);
		ptr = strtok(NULL, "|");
		m_Name = ptr;
		ptr = strtok(NULL, "|");
		m_Description = ptr;
		ptr = strtok(NULL, "|");
		m_IsDiscrete = (atoi(ptr) == 1);
		ptr = strtok(NULL, "|");
		m_Attribute = ptr;
		ptr = strtok(NULL, "|");
		m_AttributeType = atoi(ptr);

		tmp.ReleaseBuffer();
	}
	catch (...) {
		tmp.ReleaseBuffer();
		AfxMessageBox("Error processing criteria list.\n");
		ads_printf("%s\n", line);
		return -1;
	}

	return 0;
}

BOOL CProductGroupCriteria::IsEqual(CProductGroupCriteria &other)
{
	if (other.m_CriteriaDBID != m_CriteriaDBID) return FALSE;
	if (other.m_Name != m_Name) return FALSE;
	if (other.m_Description != m_Description) return FALSE;
	if (other.m_IsDiscrete != m_IsDiscrete) return FALSE;
	if (other.m_Attribute != m_Attribute) return FALSE;

	if (other.m_CriteriaRangeList.GetSize() != m_CriteriaRangeList.GetSize()) return FALSE;
	if (other.m_CriteriaValueList.GetSize() != m_CriteriaValueList.GetSize()) return FALSE;

	for (int i=0; i < m_CriteriaRangeList.GetSize(); ++i) {
		if (! other.m_CriteriaRangeList[i]->IsEqual(*(m_CriteriaRangeList[i])))
			return FALSE;
	}

	for (i=0; i < m_CriteriaValueList.GetSize(); ++i) {
		if (! other.m_CriteriaValueList[i]->IsEqual(*(m_CriteriaValueList[i])))
			return FALSE;
	}

	return TRUE;

}

CString CProductGroupCriteria::GetAttribute()
{
	return m_Attribute;
	/*
	CProductGroupCriteriaRange *pRange;
	CProductGroupCriteriaQuery *pQuery;

	pRange = m_CriteriaRangeList[0];
	if (pRange == NULL)
		return "";

	pQuery = pRange->m_CriteriaQueryList[0];
	if (pQuery == NULL)
		return "";

	return pQuery->m_Attribute;
	*/

}

int CProductGroupCriteria::GetAttributeType()
{
	return m_AttributeType;
	/*
	CProductGroupCriteriaRange *pRange;
	CProductGroupCriteriaQuery *pQuery;

	pRange = m_CriteriaRangeList[0];
	if (pRange == NULL)
		return -1;

	pQuery = pRange->m_CriteriaQueryList[0];
	if (pQuery == NULL)
		return -1;

	return pQuery->m_AttributeType;
	*/
}


// Next two functions are temporary while we only allow
// a single attribute per criteria

// the for loops were commented out for some reason
// 01/25/02 - brd - I un-commented these because we
// do need to update the attributes when
// the main criteria attribute is updated
// right now, the attributes are all the same as
// the main attribute
void CProductGroupCriteria::SetAttribute(const CString &attribute)
{
	m_Attribute = attribute;
	
	for (int i=0; i < m_CriteriaRangeList.GetSize(); ++i) {
		for (int j=0; j < m_CriteriaRangeList[i]->m_CriteriaQueryList.GetSize(); ++j) {
			m_CriteriaRangeList[i]->m_CriteriaQueryList[j]->m_Attribute = attribute;
		}
	}
	
}

void CProductGroupCriteria::SetAttributeType(int type)
{
	m_AttributeType = type;
	
	for (int i=0; i < m_CriteriaRangeList.GetSize(); ++i) {
		for (int j=0; j < m_CriteriaRangeList[i]->m_CriteriaQueryList.GetSize(); ++j) {
			m_CriteriaRangeList[i]->m_CriteriaQueryList[j]->m_AttributeType = type;
		}
	}
	
}

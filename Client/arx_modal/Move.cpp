// Move.cpp: implementation of the CMove class.
//
//////////////////////////////////////////////////////////////////////

#include "stdafx.h"
#include "modal.h"
#include "Move.h"
#include "Constants.h"
#include "UtilityHelper.h"

#ifdef _DEBUG
#undef THIS_FILE
static char THIS_FILE[]=__FILE__;
#define new DEBUG_NEW
#endif


//////////////////////////////////////////////////////////////////////
// Construction/Destruction
//////////////////////////////////////////////////////////////////////

CMove::CMove()
{

}

CMove::~CMove()
{

}

int CMove::Parse(CString &line)
{
	CStringArray strings;
	CUtilityHelper utilityHelper;

	utilityHelper.ParseString(line, "|", strings);
	if (strings.GetSize() < 24)
		return -1;

	m_MoveDBID = atol(strings[0]);
	m_ChainId = atol(strings[1]);
	m_ChainSequence = atoi(strings[2]);
	
	m_Product.m_ProductPackDBID = atol(strings[3]);
	m_Product.m_WMSProductID = strings[4];
	m_Product.m_WMSProductDetailID = strings[5];
	m_Product.m_Description = strings[6];

	m_FromLocationDBID = atol(strings[7]);
	m_FromLocation = strings[8];
	m_ToLocationDBID = atol(strings[9]);
	m_ToLocation = strings[10];

	m_FromTotalCost = atof(strings[11]);
	m_ToTotalCost = atof(strings[12]);

	m_ForkHandlingTime = atof(strings[13]);
	m_ForkHandlingCost = atof(strings[14]);
	m_ForkTravelDistance = atof(strings[15]);
	m_ForkTravelTime = atof(strings[16]);
	m_ForkTravelCost = atof(strings[17]);

	m_StockerHandlingTime = atof(strings[18]);
	m_StockerHandlingCost = atof(strings[19]);
	m_StockerTravelDistance = atof(strings[20]);
	m_StockerTravelTime = atof(strings[21]);
	m_StockerTravelCost = atof(strings[22]);

	m_MoveType = atoi(strings[23]);

	if (m_MoveType == MOVE_FROM_TEMP)
		m_FromLocation = "TempLoc";
	else if (m_MoveType == MOVE_ADD_LOC)
		m_FromLocation = "AddFacing";
	else if (m_MoveType == MOVE_TO_TEMP)
		m_ToLocation = "TempLoc";
	else if (m_MoveType == MOVE_DEL_LOC)
		m_ToLocation = "DeleteFacing";

	m_MoveStatus = atoi(strings[24]);
	m_FromLocationStatus = atoi(strings[25]);
	m_ToLocationStatus = atoi(strings[26]);
	m_ProductStatus = atoi(strings[27]);

	return 0;

}

double CMove::GetTotalMoveCost()
{
	return m_ForkHandlingCost + m_StockerHandlingCost + m_ForkTravelCost + m_StockerTravelCost;
}

double CMove::GetTotalMoveTime()
{
	return m_ForkTravelTime + m_StockerTravelTime + m_ForkHandlingTime + m_StockerHandlingTime;
}

double CMove::GetMoveSavings()
{
	return GetFromTotalCost() - GetToTotalCost();

}

double CMove::GetNetMoveSavings()
{
	return GetMoveSavings() - GetTotalMoveCost();
}

double CMove::GetFromTotalCost()
{
	// Cost is stored in cases per weeks so
	// convert it based on the current facility time horizon

	double cost = m_FromTotalCost;

	switch (m_TimeHorizonUnits) {
	case TIME_HORIZON_DAY:
		cost = cost/7 * m_TimeHorizonDuration;
		break;
	case TIME_HORIZON_WEEK:
		cost = cost*m_TimeHorizonDuration;
		break;
	case TIME_HORIZON_MONTH:
		cost = cost * 4.35 * m_TimeHorizonDuration;
		break;
	case TIME_HORIZON_YEAR:
		cost = cost *52.15 * m_TimeHorizonDuration;
		break;
	}
	
	return cost;

}

double CMove::GetToTotalCost()
{
	// Cost is stored in cases per weeks so
	// convert it based on the current facility time horizon

	double cost = m_ToTotalCost;

	switch (m_TimeHorizonUnits) {
	case TIME_HORIZON_DAY:
		cost = cost/7 * m_TimeHorizonDuration;
		break;
	case TIME_HORIZON_WEEK:
		cost = cost*m_TimeHorizonDuration;
		break;
	case TIME_HORIZON_MONTH:
		cost = cost * 4.35 * m_TimeHorizonDuration;
		break;
	case TIME_HORIZON_YEAR:
		cost = cost *52.15 * m_TimeHorizonDuration;
		break;
	}
	
	return cost;
}

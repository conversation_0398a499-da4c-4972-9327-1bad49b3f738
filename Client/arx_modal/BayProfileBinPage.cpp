// BayProfileBinPage.cpp : implementation file
//

#include "stdafx.h"
#include "modal.h"
#include "BayProfileBinPage.h"
#include "BayProfileSheet.h"
#include "Constants.h"
#include "UtilityHelper.h"
#include "HelpService.h"

#ifdef _DEBUG
#define new DEBUG_NEW
#undef THIS_FILE
static char THIS_FILE[] = __FILE__;
#endif

extern CHelpService helpService;
extern CUtilityHelper utilityHelper;
/////////////////////////////////////////////////////////////////////////////
// CBayProfileBinPage property page

IMPLEMENT_DYNCREATE(CBayProfileBinPage, CPropertyPage)

CBayProfileBinPage::CBayProfileBinPage() : CPropertyPage(CBayProfileBinPage::IDD)
{
	//{{AFX_DATA_INIT(CBayProfileBinPage)
	m_UprightHeight = 0.0;
	m_UprightWidth = 0.0;
	m_WeightCapacity = 0.0;
	m_BayDepth = 0.0;
	m_BayHeight = 0.0;
	m_BayWidth = 0.0;
	//}}AFX_DATA_INIT
	m_Validating = FALSE;
	m_SideViewButton.m_DimensionInfo.m_BayType = BAYTYPE_BIN;
	m_TopViewButton.m_DimensionInfo.m_BayType = BAYTYPE_BIN;
}

CBayProfileBinPage::~CBayProfileBinPage()
{
}

void CBayProfileBinPage::DoDataExchange(CDataExchange* pDX)
{
	CPropertyPage::DoDataExchange(pDX);
	//{{AFX_DATA_MAP(CBayProfileBinPage)
	DDX_Control(pDX, IDC_TOP_VIEW_BUTTON, m_TopViewButton);
	DDX_Control(pDX, IDC_SIDE_VIEW_BUTTON, m_SideViewButton);
	DDX_Text(pDX, IDC_UPRIGHT_HEIGHT, m_UprightHeight);
	DDV_MinMaxDouble(pDX, m_UprightHeight, 0., 999999999.);
	DDX_Text(pDX, IDC_UPRIGHT_WIDTH, m_UprightWidth);
	DDV_MinMaxDouble(pDX, m_UprightWidth, 0., 999999999.);
	DDX_Text(pDX, IDC_WEIGHT_CAPACITY, m_WeightCapacity);
	DDV_MinMaxDouble(pDX, m_WeightCapacity, 0., 999999999.);
	DDX_Text(pDX, IDC_DEPTH, m_BayDepth);
	DDV_MinMaxDouble(pDX, m_BayDepth, 0., 999999999.);
	DDX_Text(pDX, IDC_HEIGHT, m_BayHeight);
	DDV_MinMaxDouble(pDX, m_BayHeight, 0., 999999999.);
	DDX_Text(pDX, IDC_WIDTH, m_BayWidth);
	DDV_MinMaxDouble(pDX, m_BayWidth, 0., 999999999.);
	//}}AFX_DATA_MAP

	m_SideViewButton.m_DimensionInfo.m_BayDepth = m_BayDepth;
	m_SideViewButton.m_DimensionInfo.m_BayHeight = m_BayHeight;
	m_SideViewButton.m_DimensionInfo.m_BayWidth = m_BayWidth;
	m_SideViewButton.m_DimensionInfo.m_BayType = BAYTYPE_BIN;
	m_SideViewButton.m_DimensionInfo.m_UprightHeight = m_UprightHeight;
	m_SideViewButton.m_DimensionInfo.m_UprightWidth = m_UprightWidth;
	
	m_TopViewButton.m_DimensionInfo = m_SideViewButton.m_DimensionInfo;


}


BEGIN_MESSAGE_MAP(CBayProfileBinPage, CPropertyPage)
	//{{AFX_MSG_MAP(CBayProfileBinPage)
	ON_WM_HELPINFO()
	//}}AFX_MSG_MAP
END_MESSAGE_MAP()

/////////////////////////////////////////////////////////////////////////////
// CBayProfileBinPage message handlers

BOOL CBayProfileBinPage::OnInitDialog() 
{
	CPropertyPage::OnInitDialog();

	return TRUE;  // return TRUE unless you set the focus to a control
	              // EXCEPTION: OCX Property Pages should return FALSE
}


BOOL CBayProfileBinPage::OnSetActive() 
{
	CBayProfileSheet *pSheet = (CBayProfileSheet *)GetParent();
	m_pBayProfile = pSheet->m_pBayProfile;

	if (m_pBayProfile->m_BayProfileDBId > 0) {	
		m_BayDepth = m_pBayProfile->m_Depth;
		m_BayHeight = m_pBayProfile->m_Height;
		m_BayWidth = m_pBayProfile->m_Width;
		m_UprightHeight = m_pBayProfile->m_UprightHeight;
		m_UprightWidth = m_pBayProfile->m_UprightWidth;
		m_WeightCapacity = m_pBayProfile->m_WeightCapacity;
	}
	
	if (m_pBayProfile->m_Active) {
		GetDlgItem(IDC_DEPTH)->EnableWindow(FALSE);
		GetDlgItem(IDC_WIDTH)->EnableWindow(FALSE);
		GetDlgItem(IDC_HEIGHT)->EnableWindow(FALSE);
		GetDlgItem(IDC_UPRIGHT_WIDTH)->EnableWindow(FALSE);
		GetDlgItem(IDC_UPRIGHT_HEIGHT)->EnableWindow(FALSE);
	}
	else {
		GetDlgItem(IDC_DEPTH)->EnableWindow(TRUE);
		GetDlgItem(IDC_WIDTH)->EnableWindow(TRUE);
		GetDlgItem(IDC_HEIGHT)->EnableWindow(TRUE);
		GetDlgItem(IDC_UPRIGHT_WIDTH)->EnableWindow(TRUE);
		GetDlgItem(IDC_UPRIGHT_HEIGHT)->EnableWindow(TRUE);
	}

	UpdateData(FALSE);
	
	return CPropertyPage::OnSetActive();
}

BOOL CBayProfileBinPage::OnCommand(WPARAM wParam, LPARAM lParam) 
{
	
	if (HIWORD(wParam) == EN_KILLFOCUS && ! m_Validating) {
		m_Validating = TRUE;
		if (! UpdateData(TRUE))
			m_Validating = FALSE;
		else {
			m_SideViewButton.Invalidate(TRUE);
			m_TopViewButton.Invalidate(TRUE);
		}
		m_Validating = FALSE;
	}

	return CPropertyPage::OnCommand(wParam, lParam);
}

BOOL CBayProfileBinPage::OnKillActive() 
{
	UpdateData(TRUE);

	// Have to update the bay before we validate because the validation uses the bay dimensions
	m_pBayProfile->m_Width = m_BayWidth;
	m_pBayProfile->m_Depth = m_BayDepth;
	m_pBayProfile->m_Height = m_BayHeight;
	m_pBayProfile->m_UprightHeight = m_UprightHeight;
	m_pBayProfile->m_UprightWidth = m_UprightWidth;
	m_pBayProfile->m_WeightCapacity = m_WeightCapacity;

	if (! Validate())
		return 0;

	return CPropertyPage::OnKillActive();
}

BOOL CBayProfileBinPage::Validate()
{
	if (m_BayHeight < m_UprightHeight) {
		AfxMessageBox("The maximum stackable height (Bay Height) must not be less than the "
			"upright height.");
		return utilityHelper.SetEditControlErrorState(this, IDC_HEIGHT);
	}

	// Find the highest crossbar (actually the lowest bar that is higher than the uprights)
	// and sum up the level weights while we're at it
	double sumLevelWeights = 0;
	double highestCrossbar = 0;
	for (int i=0; i < m_pBayProfile->m_LevelProfileList.GetSize(); ++i) {
		sumLevelWeights += m_pBayProfile->m_LevelProfileList[i]->m_WeightCapacity;
		double hgt = m_pBayProfile->m_LevelProfileList[i]->m_Coordinates.m_Z;
		if (hgt >= m_UprightHeight && (hgt < highestCrossbar || highestCrossbar == 0)) {			
			highestCrossbar = hgt;
		}
	}

	if (m_WeightCapacity < sumLevelWeights) {
		CString temp;
		temp.Format("The bay Weight Capacity must be greater than or equal to the sum of the "
			"level weight capacities(%.0f)", sumLevelWeights);
		AfxMessageBox(temp);
		return utilityHelper.SetEditControlErrorState(this, IDC_WEIGHT_CAPACITY);
	}

	if ((m_UprightHeight - (highestCrossbar)) < 0) {
		CString temp;
		temp.Format("The Upright Height must be greater than the height of each of the crossbars.\n"
			"Currently there is a crossbar at height %.0f.", 
			highestCrossbar);
		AfxMessageBox(temp);
		return utilityHelper.SetEditControlErrorState(this, IDC_UPRIGHT_HEIGHT);
	}

	for (i=0; i < m_pBayProfile->m_LevelProfileList.GetSize(); ++i) {
		CLevelProfile *pLevel = m_pBayProfile->m_LevelProfileList[i];
		if (pLevel->m_LocationProfileList.GetSize() == 0)
			continue;

		if ( (m_BayDepth + pLevel->m_Overhang)/pLevel->m_LocationRowCount < 1) {
			CString temp;
			temp.Format("The Bay Depth is not large enough to accommodate the "
				"existing locations on level %d.", i+1);
			AfxMessageBox(temp);
			return utilityHelper.SetEditControlErrorState(this, IDC_DEPTH);
		}

		double locSpace = pLevel->m_LocationProfileList[0]->m_LocationSpace;
		int locsAcross = pLevel->m_LocationProfileList.GetSize()/pLevel->m_LocationRowCount;

		double totalLocSpace = locSpace*2*locsAcross;
		if ( (m_BayWidth - totalLocSpace)/locsAcross < 1) {
			CString temp;
			temp.Format("The Bay Width is not large enough to accommodate the "
				"existing locations on level %d.", i+1);
			AfxMessageBox(temp);
			return utilityHelper.SetEditControlErrorState(this, IDC_WIDTH);
		}

	}

	// This method will adjust the location sizes based on the current 
	int rc = m_pBayProfile->ResetLocationSizes();
	// We shouldn't meet any of these error conditions because we already checked them above,
	// but just in case.
	if (rc == -1) {
		AfxMessageBox("The bay height can not accommodate the specified locations.");
		return utilityHelper.SetEditControlErrorState(this, IDC_HEIGHT);
	}
	else if (rc == -2) {
		AfxMessageBox("The bay width can not accommodate the specified locations.");
		return utilityHelper.SetEditControlErrorState(this, IDC_WIDTH);
	}
	else if (rc == -3) {
		AfxMessageBox("The bay depth can not accommodate the specified locations.");
		return utilityHelper.SetEditControlErrorState(this, IDC_DEPTH);
	}

	return TRUE;
}


BOOL CBayProfileBinPage::OnHelpInfo(HELPINFO* pHelpInfo) 
{
	helpService.ShowFieldHelp(IDD, pHelpInfo->iCtrlId);
	return FALSE;

}

BOOL CBayProfileBinPage::OnNotify(WPARAM wParam, LPARAM lParam, LRESULT* pResult) 
{
	NMHDR* pNMHDR = (NMHDR*)lParam;

	if (pNMHDR->code == PSN_HELP) {
		helpService.ShowScreenHelp(IDD);
		return TRUE;
	}
	
	return CPropertyPage::OnNotify(wParam, lParam, pResult);
}
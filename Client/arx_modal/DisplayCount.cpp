// DisplayCount.cpp : implementation file
//

#include "stdafx.h"
#include "modal.h"
#include "DisplayCount.h"
#include "HelpService.h"

#ifdef _DEBUG
#define new DEBUG_NEW
#undef THIS_FILE
static char THIS_FILE[] = __FILE__;
#endif

extern CHelpService helpService;

/////////////////////////////////////////////////////////////////////////////
// CDisplayCount dialog


CDisplayCount::CDisplayCount(CWnd* pParent /*=NULL*/)
	: CDialog(CDisplayCount::IDD, pParent)
{
	//{{AFX_DATA_INIT(CDisplayCount)
	m_CountMsg = _T("");
	m_Number = _T("");
	//}}AFX_DATA_INIT
	m_Start = 0;
	m_End = 10;
	m_Freq = 1;
}


void CDisplayCount::DoDataExchange(CDataExchange* pDX)
{
	CDialog::DoDataExchange(pDX);
	//{{AFX_DATA_MAP(CDisplayCount)
	DDX_Text(pDX, IDC_NUMBER, m_Number);
	//}}AFX_DATA_MAP
}


BEGIN_MESSAGE_MAP(CDisplayCount, CDialog)
	//{{AFX_MSG_MAP(CDisplayCount)
	ON_WM_HSCROLL()
	ON_WM_HELPINFO()
	ON_BN_CLICKED(IDHELP, OnHelp)
	//}}AFX_MSG_MAP
END_MESSAGE_MAP()

/////////////////////////////////////////////////////////////////////////////
// CDisplayCount message handlers

BOOL CDisplayCount::OnInitDialog() 
{
	CDialog::OnInitDialog();
	
	m_pSliderCtrl = new CSliderCtrl;
	CRect r, r2;
	this->GetClientRect(&r);

	r2 = r;
	r2.left += r.Width()/4;
	r2.right -= r.Width()/4;
	r2.top += r.Height()/2 - 20;
	r2.bottom = r2.top + 40;
	

	m_pSliderCtrl->Create(TBS_TOOLTIPS|TBS_TOP|TBS_AUTOTICKS|WS_TABSTOP, r2, this, 0);

	m_pSliderCtrl->SetRange(m_Start, m_End);
	m_pSliderCtrl->SetTicFreq(m_End/10);
	m_pSliderCtrl->SetPageSize(m_End/10);

	m_pSliderCtrl->ShowWindow(SW_SHOW);

	m_Number.Format("%d of %d", m_Start, m_End);

	UpdateData(FALSE);

	return TRUE;  // return TRUE unless you set the focus to a control
	              // EXCEPTION: OCX Property Pages should return FALSE
}

BOOL CDisplayCount::PreTranslateMessage(MSG* pMsg) 
{
	if(pMsg->message== WM_LBUTTONDOWN ||
        pMsg->message== WM_LBUTTONUP ||
        pMsg->message== WM_MOUSEMOVE)
		m_ToolTip.RelayEvent(pMsg);

	return CDialog::PreTranslateMessage(pMsg);
}

CDisplayCount::~CDisplayCount()
{
	delete m_pSliderCtrl;
}

void CDisplayCount::OnOK() 
{
	m_Count = m_pSliderCtrl->GetPos();
	
	CDialog::OnOK();
}

void CDisplayCount::OnHScroll(UINT nSBCode, UINT nPos, CScrollBar* pScrollBar) 
{
	m_Number.Format("%d of %d", m_pSliderCtrl->GetPos(), m_End);
	UpdateData(FALSE);

	CDialog::OnHScroll(nSBCode, nPos, pScrollBar);
}

BOOL CDisplayCount::OnHelpInfo(HELPINFO* pHelpInfo) 
{
	helpService.ShowFieldHelp(IDD, pHelpInfo->iCtrlId);
	return FALSE;
}

void CDisplayCount::OnHelp() 
{
	helpService.ShowScreenHelp(IDD);
	
	return;

}

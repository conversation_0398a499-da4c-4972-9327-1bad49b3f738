// BayProfileSheet.cpp : implementation file
//

#include "stdafx.h"
#include "modal.h"
#include "BayProfileSheet.h"
#include "Constants.h"
#include "HelpService.h"

#ifdef _DEBUG
#define new DEBUG_NEW
#undef THIS_FILE
static char THIS_FILE[] = __FILE__;
#endif

extern CHelpService helpService;
/////////////////////////////////////////////////////////////////////////////
// CBayProfileSheet

IMPLEMENT_DYNAMIC(CBayProfileSheet, CPropertySheet)

CBayProfileSheet::CBayProfileSheet(UINT nIDCaption, CWnd* pParentWnd, UINT iSelectPage)
	:CPropertySheet(nIDCaption, pParentWnd, iSelectPage)
{
}

CBayProfileSheet::CBayProfileSheet(LPCTSTR pszCaption, CWnd* pParentWnd, UINT iSelectPage)
	:CPropertySheet(pszCaption, pParentWnd, iSelectPage)
{
}

CBayProfileSheet::CBayProfileSheet(int bayType, LPCTSTR pszCaption, CWnd* pParentWnd, UINT iSelectPage)
	:CPropertySheet(pszCaption, pParentWnd, iSelectPage)
{
	AddPage(&m_BayAttributesPage);
	switch (bayType) {
	case BAYTYPE_BIN:
		AddPage(&m_BinPage);
		break;
	case BAYTYPE_CASEFLOW:
	case BAYTYPE_PALLETFLOW:
		AddPage(&m_FlowPage);
		break;
	case BAYTYPE_FLOOR:
		AddPage(&m_FloorPage);
		break;
	case BAYTYPE_DRIVEIN:
		AddPage(&m_DriveInPage);
		break;
	case BAYTYPE_PALLET:
		AddPage(&m_PalletPage);
		break;
	}
	
	if (bayType != BAYTYPE_FLOOR && bayType != BAYTYPE_DRIVEIN)
		AddPage(&m_CrossbarPage);

	AddPage(&m_LevelPage);

	if (bayType != BAYTYPE_FLOOR && bayType != BAYTYPE_DRIVEIN)
		AddPage(&m_LocationPage);
	
	AddPage(&m_LaborPage);
	AddPage(&m_ExternalAttributePage);
	AddPage(&m_RulesPage);

	m_SelectedLevel = 0;
}

CBayProfileSheet::~CBayProfileSheet()
{
}


BEGIN_MESSAGE_MAP(CBayProfileSheet, CPropertySheet)
	//{{AFX_MSG_MAP(CBayProfileSheet)
	ON_WM_HELPINFO()
	//}}AFX_MSG_MAP
END_MESSAGE_MAP()

/////////////////////////////////////////////////////////////////////////////
// CBayProfileSheet message handlers




BOOL CBayProfileSheet::OnNotify(WPARAM wParam, LPARAM lParam, LRESULT* pResult) 
{
	// TODO: Add your specialized code here and/or call the base class
	
	return CPropertySheet::OnNotify(wParam, lParam, pResult);
}

BOOL CBayProfileSheet::OnHelpInfo(HELPINFO* pHelpInfo) 
{
	switch (pHelpInfo->iCtrlId) {
	case IDOK:
	case IDCANCEL:
		helpService.ShowFieldHelp(1, pHelpInfo->iCtrlId);
		break;
	case IDHELP:
	default:
		this->PressButton(PSBTN_HELP);
		break;
	}	
	return CPropertySheet::OnHelpInfo(pHelpInfo);
}

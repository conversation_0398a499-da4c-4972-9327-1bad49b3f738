#include "stdafx.h"
#include "ColorModelDlg.h"
#include "HelpService.h"
#include "FacilityDataService.h"
#include "UtilityHelper.h"
#include "ProductDataService.h"
#include "ControlService.h"
#include "ColoringHelper.h"
#include "ColorListBox.h"
#include "AutoCADCommands.h"
#include "Solution.h"
#include "ColorModelAdvancedDialog.h"

extern CHelpService helpService;
extern CFacilityDataService facilityDataService;
extern CControlService controlService;

extern CUtilityHelper utilityHelper;

#ifdef _DEBUG
#define new DEBUG_NEW
#undef THIS_FILE
static char THIS_FILE[] = __FILE__;
#endif

const STRING_TYPE = 2;
int findElem(CString elem_key);
void SortArraysByValue();

/////////////////////////////////////////////////////////////////////////////
// CColorModelDlg dialog


CColorModelDlg::CColorModelDlg(CWnd* pParent /*=NULL*/)
	: CDialog(CColorModelDlg::IDD, pParent)
{
	//{{AFX_DATA_INIT(CColorModelDlg)
	m_StartValue = _T("");
	m_EndValue = _T("");
	m_Reset = FALSE;
	//}}AFX_DATA_INIT

	m_Mode = CColoringHelper::TrueScale;
	m_Origin = CSolution::Optimize;
	m_Reset = TRUE;
	m_CurrentColor.SetColorIndex(kRed);
	m_CurrentColor.SetText("");
	m_Direction = CColoringHelper::HighBright;
	m_MultiType = CColoringHelper::Sum;
	m_RelativeLevel = -1;
	m_MinHeight = -1;
	m_MaxHeight = -1;
	m_pProductAttribute = NULL;
}

CColorModelDlg::~CColorModelDlg()
{
}


void CColorModelDlg::DoDataExchange(CDataExchange* pDX)
{
	CDialog::DoDataExchange(pDX);
	//{{AFX_DATA_MAP(CColorModelDlg)
	DDX_Control(pDX, IDC_COLOR_PICTURE, m_ColorButton);
	DDX_Control(pDX, IDC_ATTRIBUTE, m_AttributeCtrl);
	DDX_Text(pDX, IDC_START_VALUE, m_StartValue);
	DDX_Text(pDX, IDC_END_VALUE, m_EndValue);
	DDX_Check(pDX, IDC_COLORMODEL_AUTORESET, m_Reset);
	//}}AFX_DATA_MAP
}


BEGIN_MESSAGE_MAP(CColorModelDlg, CDialog)
	//{{AFX_MSG_MAP(CColorModelDlg)
	ON_BN_CLICKED(IDHELP, OnHelp)
	ON_BN_CLICKED(IDC_PERCENTAGE, OnPercentage)
	ON_BN_CLICKED(IDC_SPECIFIC, OnSpecific)
	ON_BN_CLICKED(IDC_TRUESCALE, OnTruescale)
	ON_BN_CLICKED(IDC_RESET, OnReset)
	ON_WM_HELPINFO()
	ON_WM_CTLCOLOR()
	ON_BN_CLICKED(IDC_CHOOSE_COLOR, OnChooseColor)
	ON_WM_DESTROY()
	ON_BN_CLICKED(IDC_ADVANCED, OnAdvanced)
	//}}AFX_MSG_MAP
END_MESSAGE_MAP()

/////////////////////////////////////////////////////////////////////////////
// CColorModelDlg message handlers


//////////////////////////////////////////////////////////////////////
//This method is executed during the initialization of the dialog.
//////////////////////////////////////////////////////////////////////
BOOL CColorModelDlg::OnInitDialog() 
{
	CDialog::OnInitDialog();

	m_CurrentColor.SetColorIndex(kRed);
	m_CurrentColor.SetText("");

	m_ColorButton.m_pColorObject = &m_CurrentColor;


	try {
		m_ProductDataService.LoadProductAttributes();
	}
	catch (...) {
		controlService.Log("Error loading product attributes.", "Generic exception in LoadProductAttributes.\n");
	}

	for (int i=0; i < m_ProductDataService.m_ProductAttributeList.GetSize(); ++i) {
		int nItem = m_AttributeCtrl.AddString(m_ProductDataService.m_ProductAttributeList[i]->m_Name);
		m_AttributeCtrl.SetItemData(nItem, (unsigned long)m_ProductDataService.m_ProductAttributeList[i]);
	}

	LoadPreviousValues();

	UpdateData(FALSE);

	return TRUE;  // return TRUE unless you set the focus to a control
	              // EXCEPTION: OCX Property Pages should return FALSE
}

////////////////////////////////////////////////////////////////
//This method is executed after the user selects the OK button.
////////////////////////////////////////////////////////////////
void CColorModelDlg::OnOK() 
{

	UpdateData(TRUE);

	if (m_AttributeCtrl.GetCurSel() < 0)
		m_pProductAttribute = NULL;
	else {
		m_pProductAttribute = (CProductAttribute *)m_AttributeCtrl.GetItemData(m_AttributeCtrl.GetCurSel());
	}

	//Now that we know what mode the user selected, we need to
	//validate the other necessary options.
	switch (m_Mode) {
	case CColoringHelper::Percentage:
	case CColoringHelper::TrueScale:
			
		if (m_AttributeCtrl.GetCurSel() < 0) {
			AfxMessageBox("Select a product attribute to color.");
			m_AttributeCtrl.ShowDropDown(TRUE);
			return;
		}

		if (m_pProductAttribute->m_Type != DT_INT && m_pProductAttribute->m_Type != DT_FLOAT) {
			AfxMessageBox("Only numeric attributes can be chosen when coloring with this method.");
			m_AttributeCtrl.ShowDropDown(TRUE);
			return;
		}

		break;

	case CColoringHelper::Range:

		if (m_AttributeCtrl.GetCurSel() < 0) {
			AfxMessageBox("Select a product attribute to color.");
			m_AttributeCtrl.ShowDropDown(TRUE);
			return;
		}
		
		if (m_StartValue.IsEmpty()) {
			AfxMessageBox("Enter the starting value for the range to be colored.");
			utilityHelper.SetEditControlErrorState(this, IDC_START_VALUE);
			return;
		}
		
	default:
		break;
	}

	CButton *pButton = (CButton *)GetDlgItem(IDC_HIGHER);
	if (pButton->GetCheck())
		m_Direction = CColoringHelper::HighBright;
	else
		m_Direction = CColoringHelper::LowBright;

	
	pButton = (CButton *)GetDlgItem(IDC_BASELINE);
	if (pButton->GetCheck())
		m_Origin = CSolution::Baseline;
	else
		m_Origin = CSolution::Optimize;

	SaveValues();

	CDialog::OnOK();
}



///////////////////////////////////////////////////////
//Executed when the F1 key is selected by the user.  A
//popup message will be displayed based on where the 
//cursor is currently located.
///////////////////////////////////////////////////////
BOOL CColorModelDlg::OnHelpInfo(HELPINFO* pHelpInfo) 
{
	helpService.ShowFieldHelp(IDD, pHelpInfo->iCtrlId);

	return TRUE;	
}


///////////////////////////////////////////////////////////
//Executed when the help button is selected by the user.
///////////////////////////////////////////////////////////
void CColorModelDlg::OnHelp() 
{
	helpService.ShowScreenHelp(IDD);	
}


/////////////////////////////////////////////////////////////////////
//Executed when the Percentage radio button is selected by the user.
/////////////////////////////////////////////////////////////////////
void CColorModelDlg::OnPercentage() 
{
	UpdateData(TRUE);

	CButton *pButton = (CButton *)GetDlgItem(IDC_PERCENTAGE);
	BOOL bChecked = pButton->GetCheck();

	//Disable all other controls
	m_ColorCtrl.EnableWindow(! bChecked);
	GetDlgItem(IDC_START_VALUE)->EnableWindow(! bChecked);
	GetDlgItem(IDC_END_VALUE)->EnableWindow(! bChecked);

	//Enable corresponding controls
	m_AttributeCtrl.EnableWindow(bChecked);
	m_DirectionCtrl.EnableWindow(bChecked);
	GetDlgItem(IDC_BASELINE)->EnableWindow(bChecked);
	GetDlgItem(IDC_OPTIMIZED)->EnableWindow(bChecked);	
	GetDlgItem(IDC_HIGHER)->EnableWindow(bChecked);
	GetDlgItem(IDC_LOWER)->EnableWindow(bChecked);

	if (bChecked)
		m_Mode = CColoringHelper::Percentage;

}


///////////////////////////////////////////////////////////////////
//Executed when the Specific check button is selected by the user.
///////////////////////////////////////////////////////////////////
void CColorModelDlg::OnSpecific() 
{
	
	UpdateData(TRUE);

	CButton *pButton = (CButton *)GetDlgItem(IDC_SPECIFIC);
	BOOL bChecked = pButton->GetCheck();

	m_ColorCtrl.EnableWindow(bChecked);
	GetDlgItem(IDC_START_VALUE)->EnableWindow(bChecked);
	GetDlgItem(IDC_END_VALUE)->EnableWindow(bChecked);
	GetDlgItem(IDC_BASELINE)->EnableWindow(bChecked);
	GetDlgItem(IDC_OPTIMIZED)->EnableWindow(bChecked);

	//Enable corresponding controls
	m_AttributeCtrl.EnableWindow(bChecked);
	m_DirectionCtrl.EnableWindow(! bChecked);
	GetDlgItem(IDC_HIGHER)->EnableWindow(! bChecked);
	GetDlgItem(IDC_LOWER)->EnableWindow(! bChecked);

	if (bChecked)
		m_Mode = CColoringHelper::Range;
}


///////////////////////////////////////////////////////////////////
//Executed when the TrueScale check button is selected by the user.
///////////////////////////////////////////////////////////////////
void CColorModelDlg::OnTruescale() 
{

	UpdateData(TRUE);

	CButton *pButton = (CButton *)GetDlgItem(IDC_TRUESCALE);
	BOOL bChecked = pButton->GetCheck();

	//Disable all other controls
	m_ColorCtrl.EnableWindow(! bChecked);
	GetDlgItem(IDC_START_VALUE)->EnableWindow(! bChecked);
	GetDlgItem(IDC_END_VALUE)->EnableWindow(! bChecked);

	//Enable corresponding controls
	m_AttributeCtrl.EnableWindow(bChecked);
	m_DirectionCtrl.EnableWindow(bChecked);
	GetDlgItem(IDC_BASELINE)->EnableWindow(bChecked);
	GetDlgItem(IDC_OPTIMIZED)->EnableWindow(bChecked);
	GetDlgItem(IDC_HIGHER)->EnableWindow(bChecked);
	GetDlgItem(IDC_LOWER)->EnableWindow(bChecked);

	if (bChecked)
		m_Mode = CColoringHelper::TrueScale;

}


//////////////////////////////////////////////////////////
//Executed when the reset button is selected by the user.
//////////////////////////////////////////////////////////
void CColorModelDlg::OnReset() 
{
	UpdateData(TRUE);

	BOOL bChecked;
	CButton *pReset = (CButton *) GetDlgItem(IDC_RESET);
	bChecked = pReset->GetCheck();

	//Disable all other controls
	m_AttributeCtrl.EnableWindow(! bChecked);
	m_ColorCtrl.EnableWindow(! bChecked);
	m_DirectionCtrl.EnableWindow(! bChecked);
	GetDlgItem(IDC_START_VALUE)->EnableWindow(! bChecked);
	GetDlgItem(IDC_END_VALUE)->EnableWindow(! bChecked);
	GetDlgItem(IDC_BASELINE)->EnableWindow(! bChecked);
	GetDlgItem(IDC_OPTIMIZED)->EnableWindow(! bChecked);
	GetDlgItem(IDC_HIGHER)->EnableWindow(! bChecked);
	GetDlgItem(IDC_LOWER)->EnableWindow(! bChecked);

	if (bChecked)
		m_Mode = CColoringHelper::Reset;
}

HBRUSH CColorModelDlg::OnCtlColor(CDC* pDC, CWnd* pWnd, UINT nCtlColor) 
{
	
	HBRUSH hbr = CDialog::OnCtlColor(pDC, pWnd, nCtlColor);
	/*
	if (pWnd->GetDlgCtrlID() == IDC_COLOR_PICTURE) {
		
		pDC->SetTextColor(RGB(m_CurrentColor.red, m_CurrentColor.blue, m_CurrentColor.green));

		// Set the background mode for text to transparent 
		// so background will show thru.
//		pDC->SetBkMode(TRANSPARENT);
		pDC->SetBkColor(RGB(m_CurrentColor.red, m_CurrentColor.blue, m_CurrentColor.green));

		return (HBRUSH)(m_pColorBrush->GetSafeHandle());

   }
   */

	return hbr;
}

void CColorModelDlg::OnChooseColor() 
{
	int oldColorIdx = m_CurrentColor.colorIndex;

	int colorIdx = CAutoCADCommands::GetColorChoice();
	if (colorIdx < 0)
		colorIdx = oldColorIdx;

	m_CurrentColor.SetColorIndex(colorIdx);
	m_CurrentColor.SetText("");

	m_ColorButton.m_pColorObject = &m_CurrentColor;
	
	m_ColorButton.Invalidate(TRUE);
}

void CColorModelDlg::OnDestroy() 
{
	CDialog::OnDestroy();
}

void CColorModelDlg::LoadPreviousValues()
{
	CStringArray keys, values;
	CString key, value, attribute;
	
	keys.Add("Mode");
	keys.Add("Color");
	keys.Add("Clear");
	keys.Add("Origin");
	keys.Add("Attribute");
	keys.Add("Direction");
	keys.Add("StartValue");
	keys.Add("EndValue");
	keys.Add("MultiType");
	keys.Add("Level");
	keys.Add("MinHeight");
	keys.Add("MaxHeight");
	
	if (controlService.GetApplicationData(keys, values, "Dialogs\\ColorModel") >= 0) {
		if (values.GetSize() == keys.GetSize()) {
			for (int i=0; i < keys.GetSize(); ++i) {
				key = keys[i];
				value = values[i];
				
				if (key == "Mode")
					m_Mode = atoi(value);
				else if (key == "Color") {
					m_CurrentColor.SetColorIndex(atoi(value));
					m_CurrentColor.SetText("");
				}
				else if (key == "Clear")
					m_Reset = atoi(value);
				else if (key == "Origin")
					m_Origin = atoi(value);
				else if (key == "Attribute")
					attribute = value;
				else if (key == "Direction")
					m_Direction = atoi(value);
				else if (key == "StartValue")
					m_StartValue = value;
				else if (key == "EndValue")
					m_EndValue = value;
				else if (key == "MultiType")
					m_MultiType = atoi(value);
				else if (key == "Level")
					m_RelativeLevel = atoi(value);
				else if (key == "MinHeight" && value != "")
					m_MinHeight = atof(value);
				else if (key == "MaxHeight" && value != "")
					m_MaxHeight = atof(value);
			}
		}
	}

	UpdateData(FALSE);

	CButton *pButton;

	switch (m_Mode) {
	case CColoringHelper::TrueScale:
		pButton = (CButton *)GetDlgItem(IDC_TRUESCALE);
		pButton->SetCheck(1);
		OnTruescale();
		break;
	case CColoringHelper::Percentage:
		pButton = (CButton *)GetDlgItem(IDC_PERCENTAGE);
		pButton->SetCheck(1);
		OnPercentage();
		break;
	case CColoringHelper::Range:
		pButton = (CButton *)GetDlgItem(IDC_SPECIFIC);
		pButton->SetCheck(1);
		OnSpecific();
		break;
	case CColoringHelper::Reset:
		pButton = (CButton *)GetDlgItem(IDC_RESET);
		pButton->SetCheck(1);
		OnReset();
		break;
	default:
		pButton = (CButton *)GetDlgItem(IDC_TRUESCALE);
		pButton->SetCheck(1);
		OnTruescale();
		break;
	}

	
	if (m_Origin == CSolution::Baseline)
		pButton = (CButton *)GetDlgItem(IDC_BASELINE);
	else
		pButton = (CButton *)GetDlgItem(IDC_OPTIMIZED);
	pButton->SetCheck(1);

	if (attribute.IsEmpty())
		attribute = "Movement";

	m_AttributeCtrl.SetCurSel(m_AttributeCtrl.FindString(0, attribute));

	if (m_Direction == CColoringHelper::HighBright)
		pButton = (CButton *)GetDlgItem(IDC_HIGHER);
	else
		pButton = (CButton *)GetDlgItem(IDC_LOWER);
	pButton->SetCheck(1);

	UpdateData(FALSE);
}

void CColorModelDlg::SaveValues()
{
	CString temp;
	CStringArray keys, values;

	keys.Add("Mode");
	temp.Format("%d", m_Mode);
	values.Add(temp);

	keys.Add("Color");
	temp.Format("%d", m_CurrentColor.colorIndex);
	values.Add(temp);

	keys.Add("Clear");
	if (m_Reset)
		values.Add("1");
	else
		values.Add("0");

	keys.Add("Origin");
	temp.Format("%d", m_Origin);
	values.Add(temp);

	keys.Add("Attribute");
	values.Add(m_pProductAttribute->m_Name);

	keys.Add("Direction");
	temp.Format("%d", m_Direction);
	values.Add(temp);

	keys.Add("StartValue");
	values.Add(m_StartValue);

	keys.Add("EndValue");
	values.Add(m_EndValue);

	keys.Add("MultiType");
	temp.Format("%d", m_MultiType);
	values.Add(temp);

	keys.Add("Level");
	temp.Format("%d", m_RelativeLevel);
	values.Add(temp);

	keys.Add("MinHeight");
	temp.Format("%.02f", m_MinHeight);
	values.Add(temp);

	keys.Add("MaxHeight");
	temp.Format("%.02f", m_MaxHeight);
	values.Add(temp);

	controlService.SetApplicationData(keys, values, "Dialogs\\ColorModel");

}

void CColorModelDlg::OnAdvanced() 
{
	CColorModelAdvancedDialog dlg;
	
	if (m_RelativeLevel > 0)
		dlg.m_Level.Format("%d", m_RelativeLevel);
	else
		dlg.m_Level = "";

	dlg.m_MultiType = m_MultiType;

	if (m_MinHeight >= 0)
		dlg.m_MinHeight.Format("%.02f", m_MinHeight);
	else
		dlg.m_MinHeight = "";

	if (m_MaxHeight >= 0)
		dlg.m_MaxHeight.Format("%.02f", m_MaxHeight);
	else
		dlg.m_MaxHeight = "";

	try {
		if (dlg.DoModal() != IDOK)
			return;
	}
	catch (...) {
		controlService.Log("Error displaying Advanced Color Model dialog.", 
			"Generic exception in CColorModelAdvancedDialog.\n");
		return;
	}

	m_RelativeLevel = atoi(dlg.m_Level);
	m_MultiType = dlg.m_MultiType;
	if (dlg.m_MinHeight != "")

		m_MinHeight = atof(dlg.m_MinHeight);
	else
		m_MinHeight = -1;

	if (dlg.m_MaxHeight != "")
		m_MaxHeight = atof(dlg.m_MaxHeight);
	else
		m_MaxHeight = -1;
}

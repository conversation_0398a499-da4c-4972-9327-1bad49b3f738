	// ProductGroupDataService.cpp: implementation of the CProductGroupDataService class.
//
//////////////////////////////////////////////////////////////////////

#include "stdafx.h"
#include "modal.h"
#include "ssa_exception.h"

#include "ProductGroupDataService.h"
#include "ProductDataService.h"
#include "ProductGroupQuery.h"
#include "Constants.h"
#include "ForteService.h"
#include "DataAccessService.h"
#include "UtilityHelper.h"
#include "ControlService.h"
#include <winsock2.h>
#include "socket_class.h"

extern int initted;

#ifdef _DEBUG
#undef THIS_FILE
static char THIS_FILE[]=__FILE__;
#define new DEBUG_NEW
#endif

extern CDataAccessService dataAccessService;
extern CUtilityHelper utilityHelper;
extern CControlService controlService;

//////////////////////////////////////////////////////////////////////
// Construction/Destruction
//////////////////////////////////////////////////////////////////////

CProductGroupDataService::CProductGroupDataService()
{
	m_ProductAttributeList.RemoveAll();

}

CProductGroupDataService::~CProductGroupDataService()
{
	for (int i=0; i < m_ProductAttributeList.GetSize(); ++i)
		delete m_ProductAttributeList[i];

	return;

}

/*
int CProductGroupDataService::CreateProductGroupAssignments(int facilityDBId, long productGroupDBID, int priority, 
															BOOL countOnly, BOOL deleteOnly)
{
	CStringArray queryInfoList;
	int udfCount, rc, i, precedence;
	BOOL containerUsed;
	CString oper, value, sql, q, colName, statement, conjunction;
	CString selectClause, fromClause, mainWhereClause, whereClause, temp;
	CString attribute, udfColumn;
	CProductAttribute *pAttribute;
	CStringArray productList;
	long nextKey, keyCount;

	InitProductAttributes(facilityDBId);

	if (GetProductGroupQuery(productGroupDBID, queryInfoList) < 0)
		return -1;


	selectClause = "select count(*) ";
	fromClause = "from dbproduct, dbproductpack";
	mainWhereClause.Format("where dbproduct.dbfacilityid = %d "
		"and dbproduct.dbproductid = dbproductpack.dbproductid ", facilityDBId);
	whereClause = "";

	udfCount = 0;
	containerUsed = FALSE;

	// This is just a sanity check to make sure there are the same number
	// of start and end parentheses.  I only did this because I screwed
	// up the demo data and set the precedence to 1 instead of 0.
	int startPrecedenceCount = 0, endPrecedenceCount = 0;

	for (i=0; i < queryInfoList.GetSize(); ++i) {
		CString qi = queryInfoList[i];
		if (ParseQueryInfo(qi, attribute, oper, value, &precedence, conjunction) < 0)
			return -1;
		
		if (precedence & CQ_COMPOUND_START)
			startPrecedenceCount++;

		if (precedence & CQ_COMPOUND_END)
			endPrecedenceCount++;

	}

	BOOL bIgnorePrecedence = FALSE;
	if (startPrecedenceCount != endPrecedenceCount)
		bIgnorePrecedence = TRUE;

	for (i=0; i < queryInfoList.GetSize(); ++i) {
		
		if (ParseQueryInfo(queryInfoList[i], attribute, oper, value, &precedence, conjunction) < 0)
			return -1;

		if (bIgnorePrecedence)
			precedence = 0;
		
		// always make sure AND is the first conjunction so we don't
		// OR the main where clause with the criteria, e.g. "facilityid = xxx OR weight > 20"
		// Most likely the query creation program won't let them do this anyway but just in case
		if (i == 0)
			conjunction =  "AND";

		if (! m_ProductAttributeMap.Lookup(attribute, (CObject *&)pAttribute)) {
			// Must be a formula; need to parse it; 
			ParseFormula(attribute, fromClause, whereClause);
			sql = "";
			FormatSQL(sql, oper, value);
			whereClause += " ";
			whereClause += conjunction;
			whereClause += " ";
			// Always add an open paren before the first where clause
			if (i == 0)
				whereClause += "(";
			if (precedence & CQ_COMPOUND_START)
				whereClause += " ( ";
			whereClause += attribute;
			whereClause += " ";
			whereClause += sql;
			if (precedence & CQ_COMPOUND_END)
				whereClause += " ) ";
		}
		else {
			sql = "";
			FormatSQL(sql, *pAttribute, oper, value);
			
			if (pAttribute->m_AttributeDBID > 0) {	// is a udf
				if (! m_UDFUsedMap.Lookup(pAttribute->m_Name, udfCount)) {
					udfCount = m_UDFUsedMap.GetCount();
					m_UDFUsedMap.SetAt(pAttribute->m_Name, udfCount);

					temp.Format(", %s udfval%d", pAttribute->m_TableName, udfCount);
					fromClause += temp;
					temp.Format("and udfval%d.dbprodpkudflistid = %d "
						"and udfval%d.dbproductpackid = dbproductpack.dbproductpackid ",
						udfCount, pAttribute->m_AttributeDBID, udfCount);
					mainWhereClause += temp;
				}

				switch(pAttribute->m_Type) {
				case DT_INT:
					udfColumn = "IntegerValue";
					break;
				case DT_FLOAT:
					udfColumn= "FloatValue";
					break;
				default:
					udfColumn = "Value";
					break;
				}
				temp.Format(" %s %s %s udfval%d.%s %s %s ",
					conjunction, 
					(i == 0) ? "(" : "",
					(precedence & CQ_COMPOUND_START) ? "(" : "",
					udfCount,
					udfColumn,
					sql,
					(precedence & CQ_COMPOUND_END) ? ")" : "");
				whereClause += temp;
				udfCount++;
			}
			else {
				if (pAttribute->m_TableName == TB_PRODUCTCONTAINER && ! containerUsed) {
					temp.Format(", %s", pAttribute->m_TableName);
					fromClause += temp;
					temp.Format("and dbprodcontainer.dbproductpackid = dbproductpack.dbproductpackid ");
					mainWhereClause += temp;
					containerUsed = TRUE;
				}
				temp.Format("%s %s %s %s.%s %s %s ", conjunction, 
					(i == 0) ? "(" : "",
					(precedence & CQ_COMPOUND_START) ? "(" : "",
					pAttribute->m_TableName,
					pAttribute->m_ColumnName, 
					sql,
					(precedence & CQ_COMPOUND_END) ? ")" : "");
				whereClause += temp;
			}
		}
		
	}

	sql = selectClause;
	sql += fromClause;
	sql += " ";
	sql += mainWhereClause;
	if (queryInfoList.GetSize() > 0)
		whereClause += ")";
	sql += whereClause;


	try {
		rc = dataAccessService.ExecuteQuery("GetProductAssignments", sql, productList, TRUE);
	}
	catch (Ssa_Exception e) {
		throw e;
		return -1;
	}
	catch (...) {
		throw Ssa_Exception("Generic error getting current assignments.", __FILE__, __LINE__, 200);
		return -1;
	}

	if (productList.GetSize() == 0)
		return -1;

	if (countOnly)
		return atoi(productList[0]);


	keyCount = atol(productList[0]);	// this is the count of products that belong in the group

	try {
		
		// First delete all of the assignments to this group for products that
		// do not belong in this group
		if (deleteOnly) {
			sql.Format("delete from dbprodslotgroup "
				"where dbslottinggroupid = %d "
				"and dbproductpackid not in ( select dbproductpack.dbproductpackid "
				"%s "
				"%s "
				"%s ) ",
				productGroupDBID, fromClause, mainWhereClause, whereClause);
			
			rc = dataAccessService.ExecuteStatement("DeleteProductAssignments", sql);
			return 0;
		}
		
		// Now update any assignments that already exist for products in this group
		sql.Format("update dbprodslotgroup "
			"set dbslottinggroupid = %d "
			"where dbslottinggroupid <> %d "
			"and %d < ( select priority from dbslottinggroup "
			"where dbslottinggroup.dbslottinggroupid = dbprodslotgroup.dbslottinggroupid) "
			"and dbproductpackid = "
			"(select dbproductpack.dbproductpackid %s %s %s "
			"and dbproductpack.dbproductpackid = dbprodslotgroup.dbproductpackid)",
			productGroupDBID, productGroupDBID, priority, fromClause, mainWhereClause, whereClause);
		rc = dataAccessService.ExecuteStatement("UpdateProductGroupAssignments", sql);
		
		// Count the current group assignments
		rc = GetProductCountByProductGroup(productGroupDBID);
	

		// Reserve the primary keys needed
		// If new only, keycount is the count of products that belong in the group but
		// are not in it, otherwise it is the count of all products that belong
		// If not new only, subtract the count of products that
		nextKey = dataAccessService.GetNextKey("DBProdSlotGroup", keyCount-rc);

		// Create assignments for the remaining products that belong in this group
		// Oracle-specific
		sql.Format("insert into dbprodslotgroup "
			"select %d+rownum-1, sysdate, sysdate, 1, dbproductpack.dbproductpackid, %d "
			"%s "
			"%s "
			"%s "
			"and not exists ( select dbproductpackid from dbprodslotgroup "
			//"where dbslottinggroupid = %d "
			"where dbprodslotgroup.dbproductpackid = dbproductpack.dbproductpackid) ",
			nextKey, productGroupDBID, fromClause, mainWhereClause, whereClause, productGroupDBID);
		rc = dataAccessService.ExecuteStatement("GetProductAssignments", sql);

	}
	catch (Ssa_Exception e) {
		throw e;
		return -1;
	}
	catch (...) {
		throw Ssa_Exception("Generic error creating assignments.", __FILE__, __LINE__, 200);;
		return -1;
	}

	return 0;

}
*/

int CProductGroupDataService::CreateProductGroupAssignments(int facilityDBId, long productGroupDBID, int priority, 
															BOOL countOnly, BOOL deleteOnly)
{
	CStringArray queryInfoList;
	int udfCount, rc, i, precedence;
	BOOL containerUsed;
	CString oper, value, sql, q, colName, statement, conjunction;
	CString selectClause, fromClause, mainWhereClause, whereClause, temp;
	CString attribute, udfColumn;
	CProductAttribute *pAttribute;
	CStringArray productList;
	long nextKey, keyCount;

	InitProductAttributes(facilityDBId);

	if (GetProductGroupQuery(productGroupDBID, queryInfoList) < 0)
		return -1;


	selectClause = "select count(*) ";
	fromClause = "from dbproduct, dbproductpack";
	mainWhereClause.Format("where dbproduct.dbfacilityid = %d "
		"and dbproduct.dbproductid = dbproductpack.dbproductid ", facilityDBId);
	whereClause = "";

	udfCount = 0;
	containerUsed = FALSE;

	// This is just a sanity check to make sure there are the same number
	// of start and end parentheses.  I only did this because I screwed
	// up the demo data and set the precedence to 1 instead of 0.
	int startPrecedenceCount = 0, endPrecedenceCount = 0;

	for (i=0; i < queryInfoList.GetSize(); ++i) {
		CString qi = queryInfoList[i];
		if (ParseQueryInfo(qi, attribute, oper, value, &precedence, conjunction) < 0)
			return -1;
		
		if (precedence & CQ_COMPOUND_START)
			startPrecedenceCount++;

		if (precedence & CQ_COMPOUND_END)
			endPrecedenceCount++;

	}

	BOOL bIgnorePrecedence = FALSE;
	if (startPrecedenceCount != endPrecedenceCount)
		bIgnorePrecedence = TRUE;

	for (i=0; i < queryInfoList.GetSize(); ++i) {
		
		if (ParseQueryInfo(queryInfoList[i], attribute, oper, value, &precedence, conjunction) < 0)
			return -1;

		if (bIgnorePrecedence)
			precedence = 0;
		
		// always make sure AND is the first conjunction so we don't
		// OR the main where clause with the criteria, e.g. "facilityid = xxx OR weight > 20"
		// Most likely the query creation program won't let them do this anyway but just in case
		if (i == 0)
			conjunction =  "AND";

		if (! m_ProductAttributeMap.Lookup(attribute, (CObject *&)pAttribute)) {
			// Must be a formula; need to parse it; 
			ParseFormula(attribute, fromClause, whereClause);
			sql = "";
			FormatSQL(sql, oper, value);
			whereClause += " ";
			whereClause += conjunction;
			whereClause += " ";
			// Always add an open paren before the first where clause
			if (i == 0)
				whereClause += "(";
			if (precedence & CQ_COMPOUND_START)
				whereClause += " ( ";
			whereClause += attribute;
			whereClause += " ";
			whereClause += sql;
			if (precedence & CQ_COMPOUND_END)
				whereClause += " ) ";
		}
		else {
			sql = "";
			FormatSQL(sql, *pAttribute, oper, value);
			
			if (pAttribute->m_AttributeDBID > 0) {	// is a udf
				if (! m_UDFUsedMap.Lookup(pAttribute->m_Name, udfCount)) {
					udfCount = m_UDFUsedMap.GetCount();
					m_UDFUsedMap.SetAt(pAttribute->m_Name, udfCount);

					temp.Format(", %s udfval%d", pAttribute->m_TableName, udfCount);
					fromClause += temp;
					temp.Format("and udfval%d.dbprodpkudflistid = %d "
						"and udfval%d.dbproductpackid = dbproductpack.dbproductpackid ",
						udfCount, pAttribute->m_AttributeDBID, udfCount);
					mainWhereClause += temp;
				}

				switch(pAttribute->m_Type) {
				case DT_INT:
					udfColumn = "IntegerValue";
					break;
				case DT_FLOAT:
					udfColumn= "FloatValue";
					break;
				default:
					udfColumn = "Value";
					break;
				}
				temp.Format(" %s %s %s udfval%d.%s %s %s ",
					conjunction, 
					(i == 0) ? "(" : "",
					(precedence & CQ_COMPOUND_START) ? "(" : "",
					udfCount,
					udfColumn,
					sql,
					(precedence & CQ_COMPOUND_END) ? ")" : "");
				whereClause += temp;
				udfCount++;
			}
			else {
				if (pAttribute->m_TableName == TB_PRODUCTCONTAINER && ! containerUsed) {
					temp.Format(", %s", pAttribute->m_TableName);
					fromClause += temp;
					temp.Format("and dbprodcontainer.dbproductpackid = dbproductpack.dbproductpackid ");
					mainWhereClause += temp;
					containerUsed = TRUE;
				}
				temp.Format("%s %s %s %s.%s %s %s ", conjunction, 
					(i == 0) ? "(" : "",
					(precedence & CQ_COMPOUND_START) ? "(" : "",
					pAttribute->m_TableName,
					pAttribute->m_ColumnName, 
					sql,
					(precedence & CQ_COMPOUND_END) ? ")" : "");
				whereClause += temp;
			}
		}
		
	}

	sql = selectClause;
	sql += fromClause;
	sql += " ";
	sql += mainWhereClause;
	if (queryInfoList.GetSize() > 0)
		whereClause += ")";
	sql += whereClause;

	if ( initted == 0 ) {
		SockClass::init_winsock();
		initted = 1;
	}

	char hostname[128]={0};
	gethostname(hostname, 128);
	CString tempTable = hostname;
	tempTable.Insert(0,"temp");
	tempTable.Remove('-');
	tempTable += "_pg";

	try {
		CString dropStmt;
		dropStmt.Format("drop table %s", tempTable);
		dataAccessService.ExecuteStatement("DropTempTable", dropStmt);
	}
	catch (...) {
		// table must not have existed
	}

	try {
		CString stmt;

		stmt.Format("create table %s as select dbproductpack.dbproductpackid %s %s %s ",
			tempTable, fromClause, mainWhereClause, whereClause);
		dataAccessService.ExecuteStatement("CreateTempTable", stmt);
		
		stmt.Format("select count(*) from %s", tempTable);

		rc = dataAccessService.ExecuteQuery("GetProductAssignments", stmt, productList, TRUE);
	}
	catch (Ssa_Exception e) {
		throw e;
		return -1;
	}
	catch (...) {
		throw Ssa_Exception("Generic error getting current assignments.", __FILE__, __LINE__, 200);
		return -1;
	}

	if (productList.GetSize() == 0)
		return -1;

	if (countOnly)
		return atoi(productList[0]);


	keyCount = atol(productList[0]);	// this is the count of products that belong in the group

	try {
		
		CStringArray stmts;
		sql.Format("delete from dbprodslotgroup "
			"where dbslottinggroupid = %d", productGroupDBID);
		stmts.Add(sql);
		
		rc = dataAccessService.ExecuteStatements("DeleteProductAssignments", stmts);
		
		
		nextKey = dataAccessService.GetNextKey("DBProdSlotGroup", keyCount);

		// Create assignments for the products that belong in this group (are not in another group with a higher priority)
		// Oracle-specific
		sql.Format("insert into dbprodslotgroup "
			"select %d+rownum-1, sysdate, sysdate, 1, dbproductpackid, %d "
			"from %s "
			"where dbproductpackid not in ( select dbproductpackid "
			"		from dbprodslotgroup psg, dbslottinggroup sg "
			"		where psg.dbproductpackid = %s.dbproductpackid "
			"		and psg.dbslottinggroupid = sg.dbslottinggroupid "
			"		and sg.priority < %d)", 
			nextKey, productGroupDBID, tempTable, tempTable, priority);

		rc = dataAccessService.ExecuteStatement("CreateProductAssignments", sql);

	}
	catch (Ssa_Exception e) {
		throw e;
		return -1;
	}
	catch (...) {
		throw Ssa_Exception("Generic error creating assignments.", __FILE__, __LINE__, 200);;
		return -1;
	}

	return 0;

}

int CProductGroupDataService::LoadProductGroups(ProductGroupArrayType &productGroupList)
{
	int rc;
	CStringArray groups;
	CProductGroup *productGroup;

	CString table, column;

	try {
		rc = GetProductGroups(controlService.GetCurrentFacilityDBId(), groups);
	}
	catch (Ssa_Exception e) {
		utilityHelper.ProcessError("Error getting list of product groups.", &e);
		return -1;
	}
	catch (...) {
		utilityHelper.ProcessError("Error getting list of product groups.");
		return -1;
	}

	for (int i=0; i < groups.GetSize(); ++i) {
		productGroup = new CProductGroup;
		productGroup->Parse(groups[i]);
		productGroupList.Add(productGroup);
	}

	return 0;

}

int CProductGroupDataService::LoadProductGroupCriteria(CriteriaArrayType &criteriaList)
{
	int rc, i;
	CStringArray list;
	CProductGroupCriteria *pCriteria;

	try {
		rc = GetCriteriaListForCurrentFacility(list);
	}
	catch (Ssa_Exception e) {
		utilityHelper.ProcessError("Error getting criteria list.", &e);
		return -1;
	}
	catch (...) {
		utilityHelper.ProcessError("Error getting criteria list.");
		return -1;
	}
	
	for (i=0; i < list.GetSize(); ++i) {
		pCriteria = new CProductGroupCriteria;
		pCriteria->Parse(list[i]);
		criteriaList.Add(pCriteria);
	}

	return 0;

}

int CProductGroupDataService::LoadCriteriaDetail(CProductGroupCriteria &criteria)
{
	int rc;
	CStringArray rangeList, queryList;
	CProductGroupCriteriaRange *pRange;
	CProductGroupCriteriaQuery *pQuery;
	CMapStringToOb map;
	CString temp;

	try {
		rc = GetCriteriaRangeList(criteria.m_CriteriaDBID, rangeList);
	}
	catch (Ssa_Exception e) {
		utilityHelper.ProcessError("Error loading criteria ranges.", &e);
		return -1;
	}
	catch (...) {
		utilityHelper.ProcessError("Error loading criteria ranges.");
		return -1;
	}

	for (int i=0; i < rangeList.GetSize(); ++i) {
		pRange = new CProductGroupCriteriaRange;
		pRange->Parse(rangeList[i]);
		temp.Format("%d", pRange->m_CriteriaRangeDBID);
		map.SetAt(temp, pRange);
		criteria.m_CriteriaRangeList.Add(pRange);
		try {
			pRange->m_InUse = (GetUsedCriteriaRangeCount(pRange->m_CriteriaRangeDBID) > 0);
		}
		catch (Ssa_Exception e) {
			utilityHelper.ProcessError("Error getting used status.", &e);
			return -1;
		}
		catch (...) {
			utilityHelper.ProcessError("Error getting used status.");
			return -1;
		}
	}

	try {
		rc = GetCriteriaQueryListByFacility(queryList);
	}
	catch (Ssa_Exception e) {
		utilityHelper.ProcessError("Error loading criteria queries.", &e);
		return -1;
	}
	catch (...) {
		utilityHelper.ProcessError("Error loading criteria queries.");
		return -1;
	}

	for (i=0; i < queryList.GetSize(); ++i) {
		pQuery = new CProductGroupCriteriaQuery;
		pQuery->Parse(queryList[i]);
		temp.Format(pQuery->m_CriteriaRangeDBID);
		if (! map.Lookup(temp, (CObject *&)pRange))
			continue;
		pRange->m_CriteriaQueryList.Add(pQuery);
	}

	return 0;

}

int CProductGroupDataService::LoadAllCriteriaDetail(CriteriaArrayType &criteriaList)
{
	int rc;
	CStringArray rangeList, queryList;
	CProductGroupCriteriaRange *pRange;
	CProductGroupCriteriaQuery *pQuery;
	CMapStringToOb map;
	CString temp;

	try {
		rc = GetCriteriaRangeListByFacility(rangeList);
	}
	catch (Ssa_Exception e) {
		utilityHelper.ProcessError("Error loading criteria ranges.", &e);
		return -1;
	}
	catch (...) {
		utilityHelper.ProcessError("Error loading criteria ranges.");
		return -1;
	}

	for (int i=0; i < rangeList.GetSize(); ++i) {

		pRange = new CProductGroupCriteriaRange;
		pRange->Parse(rangeList[i]);
		temp.Format("%d", pRange->m_CriteriaRangeDBID);
		map.SetAt(temp, pRange);

		for (int j=0; j < criteriaList.GetSize(); ++j) {
			if (criteriaList[j]->m_CriteriaDBID == pRange->m_CriteriaDBID) {
				criteriaList[j]->m_CriteriaRangeList.Add(pRange);
				break;
			}
		}

		try {
			pRange->m_InUse = (GetUsedCriteriaRangeCount(pRange->m_CriteriaRangeDBID) > 0);
		}
		catch (Ssa_Exception e) {
			utilityHelper.ProcessError("Error getting used status.", &e);
			return -1;
		}
		catch (...) {
			utilityHelper.ProcessError("Error getting used status.");
			return -1;
		}

	}

	try {
		rc = GetCriteriaQueryListByFacility(queryList);
	}
	catch (Ssa_Exception e) {
		utilityHelper.ProcessError("Error loading criteria queries.", &e);
		return -1;
	}
	catch (...) {
		utilityHelper.ProcessError("Error loading criteria queries.");
		return -1;
	}

	for (i=0; i < queryList.GetSize(); ++i) {
		pQuery = new CProductGroupCriteriaQuery;
		pQuery->Parse(queryList[i]);
		temp.Format("%d", pQuery->m_CriteriaRangeDBID);
		if (! map.Lookup(temp, (CObject *&)pRange))
			continue;
		pRange->m_CriteriaQueryList.Add(pQuery);
	}

	return 0;
}


int CProductGroupDataService::LoadCriteriaValues(CProductGroupCriteria &criteria)
{
	int rc;
	CStringArray valueList;
	CProductGroupCriteriaValue *pValue;

	try {
		rc = GetCriteriaValueList(criteria.m_CriteriaDBID, valueList);
	}
	catch (Ssa_Exception e) {
		utilityHelper.ProcessError("Error loading criteria values.", &e);
		return -1;
	}
	catch (...) {
		utilityHelper.ProcessError("Error loading criteria values.");
		return -1;
	}

	for (int i=0; i < valueList.GetSize(); ++i) {
		pValue = new CProductGroupCriteriaValue;
		pValue->Parse(valueList[i]);
		criteria.m_CriteriaValueList.Add(pValue);
	}

	return 0;

}

int CProductGroupDataService::LoadProductAttributes(CTypedPtrArray<CObArray, CProductAttribute*> &productAttributeList)
{

	CProductAttribute *attribute;

	if (m_ProductAttributeList.GetSize() == 0)
		InitProductAttributes(controlService.GetCurrentFacilityDBId());
	
	for (int i=0; i < m_ProductAttributeList.GetSize(); ++i) {
		attribute = new CProductAttribute;
		*attribute = *m_ProductAttributeList[i];
		productAttributeList.Add(attribute);
	}
	return 0;

}

int CProductGroupDataService::InitProductAttributes(int facilityDBId)
{
	int rc, i;
	CStringArray attributes;
	CProductAttribute *attribute;
	CProductDataService productDataService;

	m_UDFUsedMap.RemoveAll();

	if (m_ProductAttributeList.GetSize() > 0)
		return 0;

	try {
		rc = productDataService.GetProductAttributes(facilityDBId, attributes);
	}
	catch(Ssa_Exception e) {
		throw e;
		return -1;
	}
	catch(...) {
		throw Ssa_Exception("Generic error getting product attributes", __FILE__, __LINE__, 200);
		return -1;
	}

	for (i=0; i < attributes.GetSize(); ++i) {
		attribute = new CProductAttribute;
		attribute->Parse(attributes[i]);
		m_ProductAttributeList.Add(attribute);
		m_ProductAttributeMap.SetAt(attribute->m_Name, attribute);
	}

	return 0;

}

int CProductGroupDataService::LoadProductGroupConstraints(CProductGroup &productGroup)
{
	CStringArray constraintList;
	CProductGroupConstraint *pConstraint;
	int rc, i;

		
	try {
		rc = GetConstraintsByProductGroup(productGroup.m_ProductGroupDBID, constraintList);
	}
	catch (Ssa_Exception e) {
		utilityHelper.ProcessError("Error loading product group constraints.", &e);
		return -1;
	}
	catch (...) {
		utilityHelper.ProcessError("Error loading product group constraints.");
		return -1;
	}
	
	for (i=0; i < constraintList.GetSize(); ++i) {
		pConstraint = new CProductGroupConstraint;
		pConstraint->Parse(constraintList[i]);
		productGroup.m_ConstraintList.Add(pConstraint);
	}

	return 0;

}

int CProductGroupDataService::LoadProductGroupQuery(CProductGroup &productGroup)
{
	CStringArray queryList;
	int rc;
	CProductGroupQuery *pQuery;

	try {
		queryList.RemoveAll();
		rc = GetProductGroupQueriesForProductGroup(productGroup.m_ProductGroupDBID, queryList);
	}
	catch (Ssa_Exception e) {
		utilityHelper.ProcessError("Error getting list of product group criteria.", &e);
		return -1;
	}
	catch (...) {
		utilityHelper.ProcessError("Error getting list of product group criteria.");
		return -1;
	}
	
	for (int j=0; j < queryList.GetSize(); ++j) {
		pQuery = new CProductGroupQuery;
		pQuery->Parse(queryList[j]);
		productGroup.m_QueryList.Add(pQuery);
	}

	return 0;

}

int CProductGroupDataService::LoadAllProductGroupQueries(ProductGroupArrayType &productGroupList)
{
	CStringArray queryList;
	int rc;
	CProductGroupQuery *pQuery;
	
	try {
		rc = GetProductGroupQueriesByFacility(queryList);
	}
	catch (Ssa_Exception e) {
		utilityHelper.ProcessError("Error getting list of product group queries.", &e);
		return -1;
	}
	catch (...) {
		utilityHelper.ProcessError("Error getting list of product group queries.");
		return -1;
	}

	for (int i=0; i < queryList.GetSize(); ++i) {
		pQuery = new CProductGroupQuery;
		pQuery->Parse(queryList[i]);
		for (int j=0; j < productGroupList.GetSize(); ++j) {
			if (productGroupList[j]->m_ProductGroupDBID == pQuery->m_ProductGroupDBID) {
				productGroupList[j]->m_QueryList.Add(pQuery);
				break;
			}
		}
	}


	return 0;
}

int CProductGroupDataService::LoadAllProductGroupConstraints(ProductGroupArrayType &productGroupList)
{
	CStringArray constraintList;
	int rc;
	CProductGroupConstraint *pConstraint;
	
	try {
		rc = GetConstraintsByFacility(constraintList);
	}
	catch (Ssa_Exception e) {
		utilityHelper.ProcessError("Error getting list of product group constraints.", &e);
		return -1;
	}
	catch (...) {
		utilityHelper.ProcessError("Error getting list of product group constraints.");
		return -1;
	}

	for (int i=0; i < constraintList.GetSize(); ++i) {
		pConstraint = new CProductGroupConstraint;
		pConstraint->Parse(constraintList[i]);
		for (int j=0; j < productGroupList.GetSize(); ++j) {
			if (productGroupList[j]->m_ProductGroupDBID == pConstraint->m_ProductGroupDBID) {
				productGroupList[j]->m_ConstraintList.Add(pConstraint);
				break;
			}
		}
	}


	return 0;
}

int CProductGroupDataService::GetProductGroupQuery(long productGroupDBID, CStringArray &queryInfoList)
{
	CString sql;

	sql.Format("select dbcriteriaquery.attribute, dbcriteria.isdiscrete, "
		"dbprodgroupquery.operator, dbprodgroupquery.value,"
		"dbcriteriaquery.operator, dbcriteriaquery.value, dbcriteriaquery.precedence, "
		"dbcriteriaquery.conjunction "
		"from dbprodgroupquery, dbcriteria, dbcriteriarange, dbcriteriaquery "
		"where dbprodgroupquery.dbslottinggroupid = %d "
		"and dbprodgroupquery.dbcriteriarangeid = dbcriteriarange.dbcriteriarangeid "
		"and dbcriteriarange.dbcriteriarangeid = dbcriteriaquery.dbcriteriarangeid "
		"and dbcriteriarange.dbcriteriaid = dbcriteria.dbcriteriaid "
		"order by DBCriteriaQuery.DBCriteriaRangeID, DBCriteriaQuery.Sequence", productGroupDBID);

	return dataAccessService.ExecuteQuery("GetProductGroupQuery", sql, queryInfoList);

}

int CProductGroupDataService::ParseQueryInfo(CString &queryInfo, CString &attribute, 
											 CString &oper, CString &value, int *precedence, 
											 CString &conjunction)
{
	BOOL isDiscrete;
	int idx;
	CString listOperator, listValue, rangeOperator, rangeValue;
	
	if (queryInfo.Right(1) != "|")
		queryInfo += "|";

	// Parse the information
	// Attribute|IsDiscrete|RangeOperator|RangeValue|ListOperator|ListValue
	idx = queryInfo.Find("|");
	if (idx < 0) return -1;
	attribute = queryInfo.Left(idx);

	queryInfo = queryInfo.Mid(idx+1);
	idx = queryInfo.Find("|");
	if (idx < 0) return -1;
	isDiscrete = (atoi(queryInfo.Left(idx)) == 1);

	queryInfo = queryInfo.Mid(idx+1);
	idx = queryInfo.Find("|");
	if (idx < 0) return -1;
	rangeOperator = queryInfo.Left(idx);

	queryInfo = queryInfo.Mid(idx+1);
	idx = queryInfo.Find("|");
	if (idx < 0) return -1;
	rangeValue = queryInfo.Left(idx);

	queryInfo = queryInfo.Mid(idx+1);
	idx = queryInfo.Find("|");
	if (idx < 0) return -1;
	listOperator = queryInfo.Left(idx);

	queryInfo = queryInfo.Mid(idx+1);
	idx = queryInfo.Find("|");
	if (idx < 0) return -1;
	listValue = queryInfo.Left(idx);

	queryInfo = queryInfo.Mid(idx+1);
	idx = queryInfo.Find("|");
	if (idx < 0) return -1;
	*precedence = atoi(queryInfo.Left(idx));

	queryInfo = queryInfo.Mid(idx+1);
	idx = queryInfo.Find("|");
	if (idx < 0) return -1;
	conjunction = queryInfo.Left(idx);

	if (conjunction.Right(1) == "|")
		conjunction.Delete(listValue.GetLength()-1, 1);

	if (isDiscrete) {
		oper = listOperator;
		value = listValue;
	}
	else {
		oper = rangeOperator;
		value = rangeValue;
		conjunction = "and";
		precedence = 0;
	}
	
	return 0;
}




CString CProductGroupDataService::ParseFormula(CString &formula, 
											CString &fromClause, CString &whereClause)
{
	char *ptr, *str;
	CString temp, tempFormula, udfColumn;
	CProductAttribute *pAttribute;
	CString attributes;

	CPtrArray list;
	int udfCount = 0;
	BOOL containerUsed = FALSE;

	// First get the list of attributes, eliminating duplicates
	tempFormula = formula;
	temp = formula;
	str = temp.GetBuffer(0);
	ptr = strtok(str, ",*/-+()");
	while (ptr != NULL) {
		temp = ptr;
		temp.TrimLeft();
		temp.TrimRight();
		// true if found
		if (m_ProductAttributeMap.Lookup(temp, (CObject *&)pAttribute)) {
			list.Add(pAttribute);
			if (pAttribute->m_AttributeDBID > 0) {
				attributes += "UDF: ";
				attributes += temp;
				attributes += "\r\n";

				if (! m_UDFUsedMap.Lookup(pAttribute->m_Name, udfCount)) {
					udfCount = m_UDFUsedMap.GetCount();
					m_UDFUsedMap.SetAt(temp, udfCount);
				}
			}
			else {
				attributes += "Static: ";
				attributes += temp;
				attributes += "\r\n";
			}
		}
		else {
			attributes += "Unknown: ";
			attributes += temp;
			attributes += "\r\n";
		}
		ptr = strtok(NULL, ",*/-+()");
	}
	temp.ReleaseBuffer();

	// Loop through the attributes in the list and add the table to the query
	for (int i=0; i < list.GetSize(); ++i) {
		pAttribute = (CProductAttribute *)list[i];

		if (pAttribute->m_AttributeDBID > 0) {	// is a udf
			temp.Format(", %s udfval%d", pAttribute->m_TableName, udfCount);
			fromClause += temp;
			temp.Format("and udfval%d.dbprodpkudflistid = %d "
				"and udfval%d.dbproductpackid = dbproductpack.dbproductpackid ",
				udfCount, pAttribute->m_AttributeDBID, udfCount, udfCount);
			whereClause += temp;
			switch (pAttribute->m_Type) {
			case DT_INT:
				udfColumn = "IntegerValue";
				break;
			case DT_FLOAT:
				udfColumn = "FloatValue";
				break;
			default:
				udfColumn = "Value";
				break;
			}
			temp.Format("udfval%d.%s", udfCount, udfColumn);
			tempFormula.Replace(pAttribute->m_ColumnName, temp);
			udfCount++;
		}
		else {
			if (pAttribute->m_TableName == TB_PRODUCTCONTAINER) {
				if (! containerUsed) {
					temp.Format(", %s", pAttribute->m_TableName);
					fromClause += temp;
					containerUsed = TRUE;
					temp.Format("and dbprodcontainer.dbproductpackid = dbproductpack.dbproductpackid ");
					whereClause += temp;
				}
			}
			temp.Format("%s.%s", pAttribute->m_TableName, pAttribute->m_ColumnName);
			tempFormula.Replace(pAttribute->m_Name, temp);
		}
	}

	formula = tempFormula;
	fromClause += " ";

	return attributes;
}

void CProductGroupDataService::FormatSQL(CString &sql,  CProductAttribute &attr, 
										 const CString &oper, const CString &value)
{
	CString temp, op, internalValue;
	CStringArray valueList;
	COperator *pOperator = NULL;

	pOperator = m_OperatorService.ConvertInternalToDisplay(oper);
	if (pOperator == NULL)
		return;

	op = pOperator->m_Internal;

	if (pOperator->m_OperandCount > 2) {
		utilityHelper.ParseString(value, "^", valueList);
		sql = op;
		sql += " (";

		for (int i=0; i < valueList.GetSize(); ++i) {

			if (attr.m_Type == DT_STRING)
				temp.Format("'%s'", valueList[i]);
			else if (attr.m_Type == DT_LIST) {
				// Look up the display value to find the internal value
				attr.m_DisplayToInternalMap.Lookup(valueList[i], internalValue);
				temp.Format("'%s'", internalValue);
			}
			else
				temp.Format("%s", valueList[i]);

			if (i > 0)
				sql += ",";
			sql += temp;
		}
		sql += ") ";
	}
	else if (pOperator->m_OperandCount == 2) {		// e.g. stored as "x^and^y"
		utilityHelper.ParseString(value, "^", valueList);
		sql = op;
		sql += " ";

		if (attr.m_Type == DT_STRING)
			temp.Format("'%s' and '%s' ", valueList[0], valueList[2]);
		else if (attr.m_Type == DT_LIST) {
			attr.m_DisplayToInternalMap.Lookup(valueList[0], internalValue);
			temp = internalValue;
			temp += " and ";
			attr.m_DisplayToInternalMap.Lookup(valueList[1], internalValue);
			temp += internalValue;
		}
		else
			temp.Format("%s and %s ", valueList[0], valueList[2]);

		sql += temp;
	}
	else {
		sql = op;
		sql += " ";
		if (attr.m_Type == DT_STRING)
			temp.Format("'%s' ", value);
		else if (attr.m_Type == DT_LIST) {
			attr.m_DisplayToInternalMap.Lookup(value, internalValue);
			temp.Format("'%s' ", internalValue);
		}
		else
			temp = value;

		sql += temp;
	}

	return;	
}

void CProductGroupDataService::FormatSQL(CString &sql, const CString &oper, const CString &value)
{
	CString temp, op, internalValue;
	CStringArray valueList;
	COperator *pOperator = NULL;

	pOperator = m_OperatorService.ConvertInternalToDisplay(oper);
	if (pOperator == NULL)
		return;

	op = pOperator->m_Internal;

	// Use "in" and "not in" instead of =/!= in case they want to use multiple values
	if (pOperator->m_OperandCount > 2) {
		utilityHelper.ParseString(value, "^", valueList);
		sql = op;
		sql += " ";
		for (int i=0; i < valueList.GetSize(); ++i) {
			temp.Format("%s", valueList[i]);

			if (i > 0)
				sql += ",";
			sql += temp;
		}

		sql += ") ";
	}
	else if (pOperator->m_OperandCount == 2) {		// e.g. between, not between
		utilityHelper.ParseString(value, "^", valueList);
		sql.Format("%s %s and %s ", op, valueList[0], valueList[2]);
	}
	else {
		sql = op;
		sql += " ";
		sql += value;
	}

	sql.Replace("\\\"", "'");

	return;
}

int CProductGroupDataService::GetCriteriaValueCount(CProductAttribute &attribute)
{
	CString sql, udfColumn;
	CStringArray results;
	int rc;

	if (attribute.m_TableName == TB_PRODUCTUDFVAL) {
		switch (attribute.m_Type) {
		case DT_INT:
			udfColumn = "IntegerValue";
			break;
		case DT_FLOAT:
			udfColumn = "FloatValue";
			break;
		default:
			udfColumn = "Value";
			break;
		}
		sql.Format("select count(unique v.%s) "
			"from dbprodpkudfval v, dbprodpkudflist l "
			"where l.dbfacilityid = %d "
			"and l.description = '%s' "
			"and l.dbprodpkudflistid = v.dbprodpkudflistid",
			udfColumn, controlService.GetCurrentFacilityDBId(), attribute.m_ColumnName);
	}
	else if (attribute.m_TableName == TB_PRODUCTCONTAINER) {
		sql.Format("select count(unique pc.%s) "
			"from DBProdContainer pc, DBProductPack pp, DBProduct p "
			"where p.dbfacilityid = %d "
			"and pp.dbproductid = p.dbproductid "
			"and pc.dbproductpackid = pp.dbproductpackid",
			attribute.m_ColumnName, controlService.GetCurrentFacilityDBId());
	}
	else {
		sql.Format("select count(unique pp.%s) "
			"from dbproductpack pp, dbproduct p "
			"where p.dbfacilityid = %d "
			"and pp.dbproductid = p.dbproductid",
			attribute.m_ColumnName, controlService.GetCurrentFacilityDBId());
	}

	rc = dataAccessService.ExecuteQuery("GetCriteriaValueCount", sql, results);
	if (rc < 0)
		return -1;
	else {
		if (results[0].GetAt(results[0].GetLength()-1) == '|')
			results[0].Delete(results[0].GetLength()-1, 1);
		return atoi(results[0]);
	}
}

int CProductGroupDataService::GetCriteriaValueCount(CString &formula)
{
	CString sql;
	CStringArray results;
	int rc;
	CString selectClause, fromClause, whereClause, tempFormula;

	fromClause = "from dbproduct, dbproductpack";
	whereClause.Format("where dbproduct.dbfacilityid = %d "
		"and dbproduct.dbproductid = dbproductpack.dbproductid ", controlService.GetCurrentFacilityDBId());

	tempFormula = formula;

	InitProductAttributes(controlService.GetCurrentFacilityDBId());

	ParseFormula(tempFormula, fromClause, whereClause);

	selectClause.Format("select count(unique %s) ", tempFormula);

	sql = selectClause;
	sql += fromClause;
	sql += whereClause;

	rc = dataAccessService.ExecuteQuery("GetCriteriaValueCount", sql, results);
	if (rc < 0)
		return -1;
	else {
		if (results[0].GetAt(results[0].GetLength()-1) == '|')
			results[0].Delete(results[0].GetLength()-1, 1);
		return atoi(results[0]);
	}
}

int CProductGroupDataService::GetUniqueCriteriaValues(CProductAttribute &attribute, CStringArray &valueList)
{
	CString sql, udfColumn;

	if (attribute.m_TableName == TB_PRODUCTUDFVAL) {
		switch (attribute.m_Type) {
		case DT_INT:
			udfColumn = "IntegerValue";
			break;
		case DT_FLOAT:
			udfColumn = "FloatValue";
			break;
		default:
			udfColumn = "Value";
			break;
		}
		sql.Format("select unique v.%s "
			"from dbprodpkudfval v, dbprodpkudflist l "
			"where l.dbfacilityid = %d "
			"and l.description = '%s' "
			"and l.dbprodpkudflistid = v.dbprodpkudflistid",
			udfColumn, controlService.GetCurrentFacilityDBId(), attribute.m_Name);
	}
	else if (attribute.m_TableName == TB_PRODUCTCONTAINER) {
		sql.Format("select unique pc.%s "
			"from DBProdContainer pc, DBProductPack pp, DBProduct p "
			"where p.dbfacilityid = %f "
			"and pp.dbproductid = p.dbproductid "
			"and pc.dbproductpackid = pp.dbproductpackid",
			attribute.m_ColumnName, controlService.GetCurrentFacilityDBId());
	}
	else {
		sql.Format("select unique pp.%s "
			"from dbproductpack pp, dbproduct p "
			"where p.dbfacilityid = %d "
			"and pp.dbproductid = p.dbproductid",
			attribute.m_ColumnName, controlService.GetCurrentFacilityDBId());
	}

	return dataAccessService.ExecuteQuery("GetUniqueCriteriaValues", sql, valueList);
}

int CProductGroupDataService::GetUniqueCriteriaValues(CString &formula, CStringArray &valueList)
{
	CString sql;
	CStringArray results;
	int rc;
	CString selectClause, fromClause, whereClause, tempFormula;

	InitProductAttributes(controlService.GetCurrentFacilityDBId());

	fromClause = "from dbproduct, dbproductpack";
	whereClause.Format("where dbproduct.dbfacilityid = %d "
		"and dbproduct.dbproductid = dbproductpack.dbproductid ", controlService.GetCurrentFacilityDBId());

	tempFormula = formula;

	ParseFormula(tempFormula, fromClause, whereClause);

	selectClause.Format("select unique %s ", tempFormula);

	sql = selectClause;
	sql += fromClause;
	sql += whereClause;

	rc = dataAccessService.ExecuteQuery("GetUniqueCriteriaValues", sql, valueList);
	if (rc < 0)
		return -1;
	else {
		return valueList.GetSize();
	}
}


int CProductGroupDataService::StoreProductGroup(CProductGroup &productGroup)
{
	CString sql;
	CStringArray sqlList;
	int nextKey, i, newDetailCount;
	CProductGroupConstraint *pConstraint;
	CString temp, keyList;
	CString sideID;

	if (productGroup.m_ProductGroupDBID > 0) {
		// Update the product group itself
		sql.Format("update dbslottinggroup "
			"set description = '%s', "
			"priority = %d, "
			"percentopenlocs = %f, "
			"isprodgrouplocked = %d, "
			"isassignmentlocked = %d, "
			"suggestedsectionid = %d, "
			"optimizeattribute = '%s', "
			"optimizemethod = %d, "
			"changedate = sysdate "
			"where dbslottinggroupid = %d",
			productGroup.m_Description, productGroup.m_Priority,
			productGroup.m_PercentOpenLocs, productGroup.m_IsProdGroupLocked,
			productGroup.m_IsAssignmentLocked, productGroup.m_Exclusive,
			productGroup.m_OptimizeAttribute, productGroup.m_OptimizeMethod,
			productGroup.m_ProductGroupDBID);
		sqlList.Add(sql);

		// Update the constraints

		// First delete the ones that are not still in the list
		newDetailCount = 0;

		if (productGroup.m_ConstraintList.GetSize() > 0) {
			// Build a list of current constraints
			for (i=0; i < productGroup.m_ConstraintList.GetSize(); ++i) {
				pConstraint = productGroup.m_ConstraintList[i];
				if (pConstraint->m_ProductGroupConstraintDBID != 0) {
					if (keyList != "")
						keyList += ",";
					temp.Format("%d", pConstraint->m_ProductGroupConstraintDBID);
					keyList += temp; 
				}
				else	// keep track of the new ones so we know how many keys we need
					newDetailCount++;
			}
		}

		sql.Format("delete from dbgroupstolevels "
			"where dbslottinggroupid = %d ", productGroup.m_ProductGroupDBID);
		if (keyList != "") {
			sql += "and dbgroupstolevelsid not in (";
			sql += keyList;
			sql += ")";
		}
		sqlList.Add(sql);

		// Now insert the new constraints
		// Note: there is not way for them to update an existing constraint so don't mess with those
		nextKey = dataAccessService.GetNextKey("DBGroupsToLevels", newDetailCount);
		for (i=0; i < productGroup.m_ConstraintList.GetSize(); ++i) {
			pConstraint = productGroup.m_ConstraintList[i];
			if (pConstraint->m_ProductGroupConstraintDBID == 0) {
				if (pConstraint->m_SideDBID == 0)
					sideID = "0";
				else
					sideID = pConstraint->m_SideDescription;
				sql.Format("insert into dbgroupstolevels "
					"(dbgroupstolevelsid, sectionid, aisleid, side, bayid, relativelevel, "
					"isexclusive, createdate, changedate, lastuserid, dbslottinggroupid) "
					"values (%d, %d, %d, %s, %d, %d, %d, sysdate, sysdate, 1, %d) ",
					nextKey, pConstraint->m_SectionDBID, pConstraint->m_AisleDBID,
					sideID, pConstraint->m_BayDBID,
					pConstraint->m_RelativeLevel, pConstraint->m_IsExclusive, 
					productGroup.m_ProductGroupDBID);
				sqlList.Add(sql);
				nextKey++;
			}
		}
	}
	else {
		productGroup.m_ProductGroupDBID = dataAccessService.GetNextKey("DBSlottingGroup", 1);
		sql.Format("insert into dbslottinggroup "
			"(dbslottinggroupid, description, priority, percentopenlocs, "
			"isprodgrouplocked, isassignmentlocked, suggestedsectionid, "
			"optimizeattribute, optimizemethod, "
			"createdate, changedate, lastuserid, dbfacilityid) "
			"values (%d, '%s', %d, %f, %d, %d, %d, '%s', %d, "
			"sysdate, sysdate, 1, %d)",
			productGroup.m_ProductGroupDBID,
			productGroup.m_Description, productGroup.m_Priority,
			productGroup.m_PercentOpenLocs, productGroup.m_IsProdGroupLocked,
			productGroup.m_IsAssignmentLocked, productGroup.m_Exclusive,
			productGroup.m_OptimizeAttribute, productGroup.m_OptimizeMethod,
			controlService.GetCurrentFacilityDBId());
		sqlList.Add(sql);

		// Insert the constraints
		nextKey = dataAccessService.GetNextKey("DBGroupsToLevels", productGroup.m_ConstraintList.GetSize());

		for (i=0; i < productGroup.m_ConstraintList.GetSize(); ++i) {
			pConstraint = productGroup.m_ConstraintList[i];
			if (pConstraint->m_SideDBID == 0)
				sideID = "0";
			else
				sideID = pConstraint->m_SideDescription;
			sql.Format("insert into dbgroupstolevels "
				"(dbgroupstolevelsid, sectionid, aisleid, side, bayid, relativelevel, "
				"isexclusive, createdate, changedate, lastuserid, dbslottinggroupid) "
				"values (%d, %d, %d, %s, %d, %d, %d, sysdate, sysdate, 1, %d) ",
				nextKey, pConstraint->m_SectionDBID, pConstraint->m_AisleDBID,
				sideID, pConstraint->m_BayDBID,
				pConstraint->m_RelativeLevel, pConstraint->m_IsExclusive, 
				productGroup.m_ProductGroupDBID);
			nextKey++;
			sqlList.Add(sql);
		}

	}

	return dataAccessService.ExecuteStatements("StoreProductGroup", sqlList);

}


int CProductGroupDataService::DeleteProductGroup(long productGroupDBID)
{
	CString sql;
	CStringArray statements;

	sql.Format("delete from dbprodslotgroup "
		"where dbslottinggroupid = %d", productGroupDBID);
	statements.Add(sql);

	sql.Format("delete from dbgroupstolevels "
		"where dbslottinggroupid = %d", productGroupDBID);
	statements.Add(sql);

	sql.Format("delete from dbpass3summary "
		"where dbslottinggroupid = %d", productGroupDBID);
	statements.Add(sql);

	sql.Format("delete from dbprodgroupquery "
		"where dbslottinggroupid = %d", productGroupDBID);
	statements.Add(sql);

	sql.Format("delete from dbqueryattribute "
		"where dbqueryid in ( select dbqueryid "
		"from dbquery where dbslottinggroupid = %d)", productGroupDBID);
	statements.Add(sql);

	sql.Format("delete from dbquery "
		"where dbslottinggroupid = %d", productGroupDBID);
	statements.Add(sql);

	sql.Format("delete from dbslotgrpbay "
		"where dbslottinggroupid = %d", productGroupDBID);
	statements.Add(sql);

	sql.Format("delete from dbsltgrpudfval "
		"where dbslottinggroupid = %d", productGroupDBID);
	statements.Add(sql);

	sql.Format("delete from dbslottinggroup "
		"where dbslottinggroupid = %d", productGroupDBID);
	statements.Add(sql);

	return dataAccessService.ExecuteStatements("DeleteProductGroup", statements);

}

int CProductGroupDataService::GetCriteriaListForCurrentFacility(CStringArray &criteriaList)
{
	CString sql;

	sql.Format("Select unique DBCriteria.DBCriteriaID, Name, dbcriteria.Description, IsDiscrete "
		", Attribute, AttributeType "
		"from DBCriteria "
		", DBCriteriaRange, DBCriteriaQuery "
		"where DBFacilityID = %d "
		"and DBCriteriaRange.DBCriteriaID = DBCriteria.DBCriteriaID "
		"and DBCriteriaQuery.DBCriteriaRangeID = DBCriteriaRange.DBCriteriaRangeID "
		"order by DBCriteria.DBCriteriaid", controlService.GetCurrentFacilityDBId());

	return dataAccessService.ExecuteQuery("GetCriteriaListForFacility", sql, criteriaList);

}


int CProductGroupDataService::GetCriteriaRangeList(long criteriaDBID, CStringArray &rangeList)
{
	CString sql;

	sql.Format("select cr.DBCriteriaRangeid, "
		"cr.description, cr.dbcriteriaid "
		"from DBCriteriaRange cr "
		"where cr.DBCriteriaID = %d "
		"order by cr.DBCriteriaRangeID", criteriaDBID);

	return dataAccessService.ExecuteQuery("GetCriteriaRangeList", sql, rangeList);

}

int CProductGroupDataService::GetCriteriaRangeListByFacility(CStringArray &rangeList)
{
	CString sql;

	sql.Format("select cr.DBCriteriaRangeid,"
		"cr.description, cr.dbcriteriaid "
		"from DBCriteriaRange cr, DBCriteria c "
		"where cr.DBCriteriaID = c.DBCriteriaID "
		"and c.DBFacilityid = %d "
		"order by cr.dbcriteriaid, cr.DBCriteriaRangeID", controlService.GetCurrentFacilityDBId());

	return dataAccessService.ExecuteQuery("GetCriteriaRangeListByFacility", sql, rangeList);

}


int CProductGroupDataService::GetUsedCriteriaRangeCount(long criteriaRangeDBID)
{
	CString sql;
	CStringArray results;
	int rc;

	sql.Format("select count(*) "
		"from dbprodgroupquery "
		"where dbcriteriarangeid = %d", criteriaRangeDBID);
	
	rc = dataAccessService.ExecuteQuery("GetUsedCriteriaRangeCount", sql, results);
	if (rc < 0)
		return -1;
	else {
		if (results[0].GetAt(results[0].GetLength()-1) == '|')
			results[0].Delete(results[0].GetLength()-1, 1);
		return atoi(results[0]);
	}

}

int CProductGroupDataService::GetCriteriaValueList(long criteriaDBID, CStringArray &valueList)
{
	CString sql;
	
	sql.Format("select DBCriteriaValue.* "
		"from DBCriteriaValue "
		"where DBCriteriaID = %d "
		"order by DBCriteriaValue.DBCriteriaValueID", criteriaDBID);

	return dataAccessService.ExecuteQuery("GetCriteriaValueList", sql, valueList);

}

int CProductGroupDataService::StoreCriteria(CProductGroupCriteria &criteria)
{
	CString sql, keyList, queryKeyList, temp;
	CStringArray statements;
	long nextKey;
	long nextDetailKey, nextQueryKey;
	int i, j, newDetailCount, newQueryCount;
	CProductGroupCriteriaRange *pRange;
	CProductGroupCriteriaQuery *pQuery;
	CMap<int, int, int, int> map;

	if (criteria.m_CriteriaDBID > 0) {		// Update

		// Delete all ranges that are not in the list anymore
		newDetailCount = 0;
		newQueryCount = 0;

		if (criteria.m_CriteriaRangeList.GetSize() > 0) {
			keyList = "";

			for (i=0; i < criteria.m_CriteriaRangeList.GetSize(); ++i) {

				pRange = criteria.m_CriteriaRangeList[i];

				// new criteria range
				if (pRange->m_CriteriaRangeDBID <= 0) {	
					// add new ranges to range count
					newDetailCount++;
					
					newQueryCount += pRange->m_CriteriaQueryList.GetSize();

				}
				// existing criteria range
				else {
					temp.Format("%d", pRange->m_CriteriaRangeDBID);
					if (keyList != "")
						keyList += ",";
					keyList += temp;		// get list of ranges that should not be deleted
					
					// get list of existing queries
					queryKeyList = "";

					// Delete all the queries that aren't in the range anymore
					for (j=0; j < pRange->m_CriteriaQueryList.GetSize(); ++j) {
						pQuery = pRange->m_CriteriaQueryList[j];
						if (pQuery->m_CriteriaQueryDBID > 0) {	// existing query
							temp.Format("%d", pQuery->m_CriteriaQueryDBID);
							if (queryKeyList != "")
								queryKeyList += ",";
							queryKeyList += temp;
						}
						else
							newQueryCount++;

					}

					// Delete queries that no longer exist in ranges that do still exist
					sql.Format("delete from DBCriteriaQuery "
						"where DBCriteriaRangeID = %d ", pRange->m_CriteriaRangeDBID);
					if (queryKeyList != "") {
						sql += "and DBCriteriaQueryID not in (";
						sql += queryKeyList;
						sql += ")";
					}
				}
			
			}
		}

		// Delete queries for ranges that no longer
		// exist
		sql.Format("delete from DBCriteriaQuery "
			"where DBCriteriaRangeID in "
			"( select DBCriteriaRangeID "
			"from DBCriteriaRange "
			"where DBCriteriaID = %d ", criteria.m_CriteriaDBID);
		if (keyList != "") {
			sql += "and DBCriteriaRangeID not in (";
			sql += keyList;
			sql += ")";
		}
		sql += ")";
		statements.Add(sql);

		sql.Format("delete from DBCriteriaRange "
			"where DBCriteriaID = %d ", criteria.m_CriteriaDBID);
		if (keyList != "") {
			sql += "and DBCriteriaRangeID not in (";
			sql += keyList;
			sql += ")";
		}
		statements.Add(sql);

		// Update the rest of the ranges;
		if (newDetailCount > 0)
			nextDetailKey = dataAccessService.GetNextKey("DBCriteriaRange", newDetailCount);

		if (newQueryCount > 0)
			nextQueryKey = dataAccessService.GetNextKey("DBCriteriaQuery", newQueryCount);

		for (i=0; i < criteria.m_CriteriaRangeList.GetSize(); ++i) {

			pRange = criteria.m_CriteriaRangeList[i];

			// update existing range
			if (pRange->m_CriteriaRangeDBID > 0) {
				sql.Format("update DBCriteriaRange set "
					"Description = '%s' "
					"where DBCriteriaRangeID = %d",
					pRange->m_Description,
					pRange->m_CriteriaRangeDBID);

			}
			// insert new range
			else {
				pRange->m_CriteriaRangeDBID = nextDetailKey;
				pRange->m_CriteriaDBID = criteria.m_CriteriaDBID;
				sql.Format("insert into DBCriteriaRange "
					"(DBCriteriaRangeID, Description, DBCriteriaID) "
					"values (%d, '%s', %d) ",
					nextDetailKey, pRange->m_Description,
						criteria.m_CriteriaDBID);
				nextDetailKey++;
			}
			statements.Add(sql);

			// Update the queries associated with this range	
			for (j=0; j < pRange->m_CriteriaQueryList.GetSize(); ++j) {
				pQuery = pRange->m_CriteriaQueryList[j];
				
				if (pQuery->m_CriteriaQueryDBID > 0) {
					sql.Format("update DBCriteriaQuery set "
						"Attribute = '%s', "
						"AttributeType = %d,"
						"Operator = '%s',"
						"Value = '%s', "
						"Sequence = %d, "
						"Precedence = %d, "
						"Conjunction = '%s' "
						"where DBCriteriaQueryID = %d",
						pQuery->m_Attribute, pQuery->m_AttributeType,
						pQuery->m_Operator, pQuery->m_Value, pQuery->m_Sequence, pQuery->m_Precedence,
						pQuery->m_Conjunction, pQuery->m_CriteriaQueryDBID);
				}
				else {
					pQuery->m_CriteriaQueryDBID = nextQueryKey;
					pQuery->m_CriteriaRangeDBID = pRange->m_CriteriaRangeDBID;
					
					sql.Format("insert into DBCriteriaQuery "
						"(DBCriteriaQueryID, Attribute, AttributeType, "
						"Operator, Value, Sequence, Precedence, Conjunction, DBCriteriaRangeID) "
						"values ("
						"%d, '%s', %d, '%s', '%s', %d, %d, '%s', %d)",
						pQuery->m_CriteriaQueryDBID, pQuery->m_Attribute,
						pQuery->m_AttributeType, pQuery->m_Operator,
						pQuery->m_Value, pQuery->m_Sequence, pQuery->m_Precedence, 
						pQuery->m_Conjunction, pQuery->m_CriteriaRangeDBID);
					nextQueryKey++;
				}
				statements.Add(sql);
			}
		}

		newDetailCount = 0;

		// Delete all values that are not in the list anymore
		if (criteria.m_CriteriaValueList.GetSize() > 0) {
			keyList = "";
			for (i=0; i < criteria.m_CriteriaValueList.GetSize(); ++i) {
				if (criteria.m_CriteriaValueList[i]->m_CriteriaValueDBID == 0)
					newDetailCount++;
				else {
					temp.Format("%d", criteria.m_CriteriaValueList[i]->m_CriteriaValueDBID);
					if (keyList != "")
						keyList += ",";
					keyList += temp;
				}
			}
		}
		
		sql.Format("delete from DBCriteriaValue "
			"where DBCriteriaID = %d ", criteria.m_CriteriaDBID);
		if (keyList != "") {
			sql += "and DBCriteriaValueID not in (";
			sql += keyList;
			sql += ")";
		}
		statements.Add(sql);

		// Update the rest of the values;
		if (newDetailCount > 0)
			nextDetailKey = dataAccessService.GetNextKey("DBCriteriaValue", newDetailCount);

		for (i=0; i < criteria.m_CriteriaValueList.GetSize(); ++i) {
			if (criteria.m_CriteriaValueList[i]->m_CriteriaValueDBID > 0) {
				sql.Format("update DBCriteriaValue set "
					"Description = '%s' "
					"where DBCriteriaValueID = %d",
					criteria.m_CriteriaValueList[i]->m_Description,
					criteria.m_CriteriaValueList[i]->m_CriteriaValueDBID);
			}
			else {
				criteria.m_CriteriaValueList[i]->m_CriteriaValueDBID = nextDetailKey;
				sql.Format("insert into DBCriteriaValue "
					"(DBCriteriaValueID, Description, DBCriteriaID) "
					"values (%d, '%s', %d) ",
					nextDetailKey, criteria.m_CriteriaValueList[i]->m_Description,
					criteria.m_CriteriaDBID);
				nextDetailKey++;
			}

			statements.Add(sql);
		}

		newDetailCount = 0;


		// Update the Criteria
		sql.Format("update DBCriteria set "
			"Name = '%s', "
			"Description = '%s', "
			"IsDiscrete = %d "
			"where DBCriteriaID = %d",
			criteria.m_Name, criteria.m_Description, criteria.m_IsDiscrete,
			criteria.m_CriteriaDBID);
		statements.Add(sql);

	}

	else {		// Insert

		nextKey = dataAccessService.GetNextKey("DBCriteria", 1);
		criteria.m_CriteriaDBID = nextKey;
		sql.Format("insert into DBCriteria "
			"(DBCriteriaID, Name, Description, IsDiscrete, DBFacilityID) "
			"values (%d, '%s', '%s', %d, %d) ",
			criteria.m_CriteriaDBID, criteria.m_Name, criteria.m_Description,
			criteria.m_IsDiscrete, controlService.GetCurrentFacilityDBId());
		statements.Add(sql);

		if (criteria.m_CriteriaRangeList.GetSize() > 0) {

			nextDetailKey = dataAccessService.GetNextKey("DBCriteriaRange", criteria.m_CriteriaRangeList.GetSize());

			for (i=0; i < criteria.m_CriteriaRangeList.GetSize(); ++i) {
				pRange = criteria.m_CriteriaRangeList[i];
				pRange->m_CriteriaRangeDBID = nextDetailKey;
				pRange->m_CriteriaDBID = criteria.m_CriteriaDBID;
				sql.Format("insert into DBCriteriaRange "
					"(DBCriteriaRangeID, Description, DBCriteriaID) "
					"values (%d, '%s', %d) ",
					pRange->m_CriteriaRangeDBID, pRange->m_Description,
					pRange->m_CriteriaDBID);
				statements.Add(sql);
				nextDetailKey++;

				nextQueryKey = dataAccessService.GetNextKey("DBCriteriaQuery", pRange->m_CriteriaQueryList.GetSize());
				for (j=0; j < pRange->m_CriteriaQueryList.GetSize(); ++j) {
					pQuery = pRange->m_CriteriaQueryList[j];
					pQuery->m_CriteriaQueryDBID = nextQueryKey;
					pQuery->m_CriteriaRangeDBID = pRange->m_CriteriaRangeDBID;

					sql.Format("insert into DBCriteriaQuery "
						"(DBCriteriaQueryID, Attribute, AttributeType, "
						"Operator, Value, Sequence, Precedence, Conjunction, DBCriteriaRangeID) "
						"values (%d, '%s', %d, '%s', '%s', %d, %d, '%s', %d)",
						pQuery->m_CriteriaQueryDBID, pQuery->m_Attribute,
						pQuery->m_AttributeType, pQuery->m_Operator,
						pQuery->m_Value, pQuery->m_Sequence, pQuery->m_Precedence,
						pQuery->m_Conjunction,
						pQuery->m_CriteriaRangeDBID);
					statements.Add(sql);
					nextQueryKey++;
				}
			}
		}


		if (criteria.m_CriteriaValueList.GetSize() > 0) {

			nextDetailKey = dataAccessService.GetNextKey("DBCriteriaValue", criteria.m_CriteriaValueList.GetSize());

			for (i=0; i < criteria.m_CriteriaValueList.GetSize(); ++i) {
				criteria.m_CriteriaValueList[i]->m_CriteriaValueDBID = nextDetailKey;
				sql.Format("insert into DBCriteriaValue "
					"(DBCriteriaValueID, Description, DBCriteriaID) "
					"values (%d, '%s', %d) ",
					nextDetailKey, criteria.m_CriteriaValueList[i]->m_Description,
					nextKey);
				statements.Add(sql);
				nextDetailKey++;
			}

		}

	}


	return dataAccessService.ExecuteStatements("StoreCriteria", statements);

}

int CProductGroupDataService::DeleteCriteria(long criteriaDBID)
{
	CString sql;
	CStringArray statements;

	// we don't let them delete a range if it has any
	// queries but just in case...
	sql.Format("delete from DBProdGroupQuery "
		"where DBCriteriaRangeID in ( select DBCriteriaRangeID "
		"from DBCriteriaRange "
		"where DBCriteriaRange.DBCriteriaID = %d)", criteriaDBID);
	statements.Add(sql);

	sql.Format("delete from DBCriteriaQuery "
		"where DBCriteriaRangeID in ( select DBCriteriaRangeID "
		"from DBCriteriaRange "
		"where DBCriteriaRange.DBCriteriaID = %d)", 
		criteriaDBID);
	statements.Add(sql);

	sql.Format("delete from DBCriteriaRange "
		"where DBCriteriaID = %d", criteriaDBID);
	statements.Add(sql);

	sql.Format("delete from DBCriteriaValue "
		"where DBCriteriaID = %d", criteriaDBID);
	statements.Add(sql);

	sql.Format("delete from DBCriteria "
		"where DBCriteriaID = %d", criteriaDBID);
	statements.Add(sql);

	return dataAccessService.ExecuteStatements("DeleteCriteria", statements);

}


int CProductGroupDataService::GetProductGroupQueriesForProductGroup(long productGroupDBID, CStringArray &queryList)
{
	CString sql;

	sql.Format("select q.dbprodgroupqueryid, q.operator, q.value, q.dbcriteriarangeid, q.dbslottinggroupid, "
		"cr.dbcriteriaid, cr.description "
		"from dbprodgroupquery q, dbcriteriarange cr "
		"where q.dbslottinggroupid = %d "
		"and cr.dbcriteriarangeid = q.dbcriteriarangeid",
		productGroupDBID);

	return dataAccessService.ExecuteQuery("GetProductGroupQueriesForProductGroup", sql, queryList);

}

int CProductGroupDataService::GetProductGroupQueriesByFacility(CStringArray &queryList)
{
	CString sql;

	sql.Format("select q.dbprodgroupqueryid, q.operator, q.value, "
		"q.dbcriteriarangeid, q.dbslottinggroupid, "
		"cr.dbcriteriaid, cr.description "
		"from dbprodgroupquery q, dbcriteriarange cr, dbslottinggroup sg "
		"where cr.dbcriteriarangeid = q.dbcriteriarangeid "
		"and sg.dbslottinggroupid = q.dbslottinggroupid "
		"and sg.dbfacilityid = %d", controlService.GetCurrentFacilityDBId());

	return dataAccessService.ExecuteQuery("GetProductGroupQueriesByFacility", sql, queryList);

}

int CProductGroupDataService::StoreProductGroupQuery(CProductGroup &productGroup, CArray<int, int> &deleteList)
{
	CString sql;
	CStringArray statements;
	CProductGroupQuery *pQuery;
	long nextKey;
	int keyCount;

	for (int i=0; i < deleteList.GetSize(); ++i) {
		sql.Format("delete from dbprodgroupquery "
			"where dbprodgroupqueryid = %d", deleteList[i]);
		statements.Add(sql);
	}

	keyCount = 0;
	for (i=0; i < productGroup.m_QueryList.GetSize(); ++i) {
		if (productGroup.m_QueryList[i]->m_ProductGroupQueryDBID == 0)
			keyCount++;
	}

	nextKey = dataAccessService.GetNextKey("DBProdGroupQuery", keyCount);

	for (i=0; i < productGroup.m_QueryList.GetSize(); ++i) {
		pQuery = productGroup.m_QueryList[i];
		if (pQuery->m_ProductGroupQueryDBID == 0) {
			pQuery->m_ProductGroupQueryDBID = nextKey;
			sql.Format("insert into dbprodgroupquery "
				"(dbprodgroupqueryid, operator, value, dbcriteriarangeid, dbslottinggroupid) "
				"values (%d, '%s', '%s', %d, %d)",
				nextKey, pQuery->m_Operator, pQuery->m_Value, pQuery->m_CriteriaRangeDBID, 
				pQuery->m_ProductGroupDBID);
			statements.Add(sql);
			nextKey++;
		}
		else {
			sql.Format("update dbprodgroupquery "
				"set operator = '%s', "
				"value = '%s', "
				"dbcriteriarangeid = %d "
				"where dbprodgroupqueryid = %d",
				pQuery->m_Operator, pQuery->m_Value, 
				pQuery->m_CriteriaRangeDBID, pQuery->m_ProductGroupQueryDBID);
			statements.Add(sql);
		}
	}

	return dataAccessService.ExecuteStatements("StoreProductGroupQuery", statements);

}

int CProductGroupDataService::GetProductCountByProductGroup(long productGroupDBID)
{
	CString sql;
	CStringArray results;
	int rc;

	sql.Format("select count(*) "
		"from dbprodslotgroup "
		"where dbslottinggroupid = %d", productGroupDBID);

	rc = dataAccessService.ExecuteQuery("GetProductGroupByProductGroup", sql, results);

	if (rc < 0)
		return rc;

	if (results.GetSize() == 0)
		return -1;

	if (results[0].Right(1) == "|")
		results[0].Delete(results[0].GetLength()-1, 1);

	return atoi(results[0]);

}
	

int CProductGroupDataService::GetProductGroupUnassignedCount()
{
	CString sql;
	CStringArray results;
	int rc;

	sql.Format("select count(*) "
		"from dbproductpack, dbproduct "
		"where dbproduct.dbfacilityid = %d "
		"and dbproduct.dbproductid = dbproductpack.dbproductid "
		"and not exists ( select dbproductpackid "
		"from dbprodslotgroup "
		"where dbprodslotgroup.dbproductpackid = dbproductpack.dbproductpackid) ",
		controlService.GetCurrentFacilityDBId());

	rc = dataAccessService.ExecuteQuery("GetProductGroupUnassignedCount", sql, results);

	if (rc < 0)
		return rc;

	if (results.GetSize() == 0)
		return -1;

	if (results[0].Right(1) == "|")
		results[0].Delete(results[0].GetLength()-1, 1);

	return atoi(results[0]);

}

int CProductGroupDataService::GetProductInfoByProductGroup(long productGroupDBID, CStringArray &productList)
{
	CString sql;
	
	if (productGroupDBID > 0) {
		sql.Format("select pp.dbproductpackid, pp.description, pp.wmsproductid, "
			"pp.wmsproductdetailid, pp.casepack, pp.innerpack, pp.unitofissue "
			"from dbproductpack pp, dbprodslotgroup "
			"where dbprodslotgroup.dbslottinggroupid = %d "
			"and dbprodslotgroup.dbproductpackid = pp.dbproductpackid",
			productGroupDBID);
	}
	else {
		sql.Format("select pp.dbproductpackid, pp.description, pp.wmsproductid, "
			"pp.wmsproductdetailid, pp.casepack, pp.innerpack, pp.unitofissue "
			"from dbproductpack pp, dbproduct "
			"where dbproduct.dbfacilityid = %d "
			"and dbproduct.dbproductid = pp.dbproductid "
			"and not exists ( select dbproductpackid from dbprodslotgroup "
			"where dbprodslotgroup.dbproductpackid = pp.dbproductpackid) ",
			controlService.GetCurrentFacilityDBId());
	}

	return dataAccessService.ExecuteQuery("GetProductsByProductGroup", sql, productList);

}


int CProductGroupDataService::GetConstraintsByProductGroup(long productGroupDBID, CStringArray &constraintList)
{
	CString sql;
	
	sql.Format("select gtl.dbgroupstolevelsid, gtl.dbslottinggroupid, "
		"gtl.sectionid, s.description, "
		"gtl.aisleid, a.description, si.dbsideid, gtl.side, "
		"gtl.bayid, b.description, gtl.relativelevel, gtl.isexclusive "
		"from dbgroupstolevels gtl, dbsection s, "
		"dbaisle a, dbside si, dbbay b "
		"where gtl.sectionid = s.dbsectionid "
		"and gtl.aisleid = a.dbaisleid (+) "
		"and gtl.aisleid = si.dbaisleid (+) "
		"and gtl.side = si.description (+) "
		"and gtl.bayid = b.dbbayid (+) "
		"and gtl.dbslottinggroupid = %d", productGroupDBID);

	return dataAccessService.ExecuteQuery("GetConstraintsByProductGroup", sql, constraintList);

}


int CProductGroupDataService::GetConstraintsByFacility(CStringArray &constraintList)
{
	CString sql;
	
	sql.Format("select gtl.dbgroupstolevelsid, gtl.dbslottinggroupid, "
		"gtl.sectionid, s.description, "
		"gtl.aisleid, a.description, si.dbsideid, gtl.side, "
		"gtl.bayid, b.description, gtl.relativelevel, gtl.isexclusive "
		"from dbgroupstolevels gtl, dbslottinggroup sg, dbsection s, "
		"dbaisle a, dbside si, dbbay b "
		"where gtl.sectionid = s.dbsectionid "
		"and gtl.aisleid = a.dbaisleid (+) "
		"and gtl.aisleid = si.dbaisleid (+) "
		"and gtl.side = si.description (+) "
		"and gtl.bayid = b.dbbayid (+) "
		"and gtl.dbslottinggroupid = sg.dbslottinggroupid "
		"and sg.dbfacilityid = %d", controlService.GetCurrentFacilityDBId());

	return dataAccessService.ExecuteQuery("GetConstraintsByFacility", sql, constraintList);

}

int CProductGroupDataService::UpdateProductGroupPriority(long productGroupDBID, int priority)
{
	CString sql;

	sql.Format("update dbslottinggroup set priority = %d "
		"where dbslottinggroupid = %d", priority, productGroupDBID);

	return dataAccessService.ExecuteStatement("UpdateProductGroupPriority", sql);

}

int CProductGroupDataService::GetProductGroups(int facilityDBId, CStringArray &productGroupList)
{
	CString queryText;

	queryText.Format(" select dbslottinggroupid, description, "
		"priority, percentopenlocs, isprodgrouplocked, isassignmentlocked, suggestedsectionid, "
		"optimizeattribute, optimizemethod "
		"from dbslottinggroup "
		"where dbfacilityid = %d "
		"order by priority", facilityDBId);
			

	return dataAccessService.ExecuteQuery("GetProductGroups", queryText, productGroupList);

}

int CProductGroupDataService::GetCriteriaQueryListByFacility(CStringArray &queryList)
{
	CString sql;

	sql.Format("select dbcriteriaqueryid, attribute, attributetype, "
		"operator, value, sequence, precedence, conjunction, cq.dbcriteriarangeid "
		"from dbcriteriaquery cq, dbcriteriarange cr, dbcriteria c "
		"where c.dbfacilityid = %d "
		"and cr.dbcriteriaid = c.dbcriteriaid "
		"and cq.dbcriteriarangeid = cr.dbcriteriarangeid "
		"order by cq.sequence", controlService.GetCurrentFacilityDBId());

	return dataAccessService.ExecuteQuery("GetCriteriaQueryListByFacility", sql, queryList);

}


int CProductGroupDataService::GetBayHandlesByProductGroup(long productGroupDBID, CStringArray &handleList)
{
	CString sql;

	sql.Format("select acadhandle "
		"from dbbay, dbslotgrpbay "
		"where dbslotgrpbay.dbslottinggroupid = %d "
		"and dbslotgrpbay.dbbayid = dbbay.dbbayid ", productGroupDBID);

	return dataAccessService.ExecuteQuery("GetBayHandlesByProductGroup", sql, handleList);

}

BOOL CProductGroupDataService::ValidateFormula(CString &formula, CString &attributes)
{
	CString sql;
	CStringArray results;
	int rc;
	CString selectClause, fromClause, whereClause, tempFormula;

	fromClause = "from dbproduct, dbproductpack";
	whereClause.Format("where dbproduct.dbfacilityid = %d "
		"and dbproduct.dbproductid = dbproductpack.dbproductid "
		"and rownum = 1 ", controlService.GetCurrentFacilityDBId());

	tempFormula = formula;

	InitProductAttributes(controlService.GetCurrentFacilityDBId());

	attributes = ParseFormula(tempFormula, fromClause, whereClause);

	selectClause.Format("select %s ", tempFormula);

	sql = selectClause;
	sql += fromClause;
	sql += whereClause;

	rc = dataAccessService.ExecuteQuery("ValidateFormula", sql, results);
	if (rc < 0)
		return FALSE;
	else {
		return TRUE;
	}
}


int CProductGroupDataService::UpdateUDFWithFormula(CString &formula, int udfDBID)
{
	CString sql;
	CStringArray results, stmts;
	CString selectClause, fromClause, whereClause, tempFormula;

	fromClause = "from dbproductpack";
	whereClause.Format("where dbprodpkudfval.dbproductpackid = dbproductpack.dbproductpackid ");
	tempFormula = formula;

	InitProductAttributes(controlService.GetCurrentFacilityDBId());

	ParseFormula(tempFormula, fromClause, whereClause);

	sql.Format("update dbprodpkudfval set integervalue = 0, floatvalue = 0, value = "
		"(select %s "
		"%s "		// from clause
		"%s)"		// where clause
		"where dbprodpkudfval.dbprodpkudflistid = %d",
		tempFormula, fromClause, whereClause, udfDBID);
	stmts.Add(sql);

	sql.Format("update dbprodpkudfval set floatvalue = "
		"(select %s "
		"%s, dbprodpkudflist l "		// from clause
		"%s "		// where clause
		"and l.dbprodpkudflistid = dbprodpkudfval.dbprodpkudflistid "
		"and l.type in (%d, %d)) "
		"where dbprodpkudfval.dbprodpkudflistid = %d",
		tempFormula, fromClause, whereClause, DT_INT, DT_FLOAT, udfDBID);
	stmts.Add(sql);

	sql.Format("update dbprodpkudfval set integervalue = floatvalue "
		"where dbprodpkudfval.dbprodpkudflistid = %d ", udfDBID, DT_INT, DT_FLOAT);
	stmts.Add(sql);

	try {
		dataAccessService.ExecuteStatements("UpdateUDF", stmts);
	}
	catch (...) {
		utilityHelper.ProcessError("Error updating udfs.");
		return -1;
	}

	return 0;

}

int CProductGroupDataService::StoreProductGroupLevel(CTypedPtrArray<CObArray, CProductGroupLevel*> &list)
{
	CString stmt;
	CStringArray stmts;
	int key;

	key = dataAccessService.GetNextKey("DBSlotGrpBay", list.GetSize());
	for (int i=0; i < list.GetSize(); ++i) {
		stmt.Format("delete from dbslotgrpbay where dblevelid = %d", list[i]->m_LevelDBID);
		stmts.Add(stmt);

		stmt.Format("insert into dbslotgrpbay "
			"(dbslotgrpbayid, dbbayid, dblevelid, dbslottinggroupid, createdate, changedate, lastuserid) "
			"values (%d, %d, %d, %d, sysdate, sysdate, 1)",
			key, list[i]->m_BayDBID, list[i]->m_LevelDBID, list[i]->m_ProductGroupDBID);
		stmts.Add(stmt);
		key++;
	}

	return dataAccessService.ExecuteStatements("StoreProductGroupLevel", stmts);

}

int CProductGroupDataService::GetProductGroupAssignmentCount(CStringArray &results)
{
	CString sql;

	sql.Format("select sg.dbslottinggroupid, count(*) "
		"from dbslottinggroup sg, dbprodslotgroup psg "
		"where sg.dbslottinggroupid = psg.dbslottinggroupid "
		"and sg.dbfacilityid = %d "
		"group by sg.dbslottinggroupid ", controlService.GetCurrentFacilityDBId());

	return dataAccessService.ExecuteQuery("GetProductGroupAssignmentCount", sql, results);

}


int CProductGroupDataService::DeleteSlotGroupBays( CArray <int, int&> &bayList ) 
{
	CString sql;
	CString bayIds;

	for (int i=0; i < bayList.GetSize(); ++i) {
		CString temp;
		temp.Format("%d,", bayList[i]);
		bayIds += temp;
	}

	bayIds.TrimRight(",");

	sql.Format("delete from dbslotgrpbay where dbbayid in (%s)", bayIds);

	return dataAccessService.ExecuteStatement("DeleteSlotGroupBays", sql);

}

int CProductGroupDataService::GetBayHandlesByProductInGroup(int groupId, CStringArray &handles)
{
	CString sql;

	sql.Format("select unique acadhandle "
		"from dbbay, dblevel, dblocation, dbslotsolution, dbprodslotgroup "
		"where dbprodslotgroup.dbslottinggroupid = %d "
		"and dbprodslotgroup.dbproductpackid = dbslotsolution.dbproductpackid "
		"and dbslotsolution.dblocationid = dblocation.dblocationid "
		"and dblocation.dblevelid = dblevel.dblevelid "
		"and dblevel.dbbayid = dbbay.dbbayid ", groupId);

	return dataAccessService.ExecuteQuery("GetBayHandlesByProductInGroup", sql, handles);
}
#if !defined(AFX_BAYPROFILEBAYATTRIBUTESPAGE_H__C98367D2_5956_46BD_A682_20824BF2F1A9__INCLUDED_)
#define AFX_BAYPROFILEBAYATTRIBUTESPAGE_H__C98367D2_5956_46BD_A682_20824BF2F1A9__INCLUDED_

#if _MSC_VER > 1000
#pragma once
#endif // _MSC_VER > 1000
// BayProfileBayAttributesPage.h : header file
//
#include "BayProfile.h"

/////////////////////////////////////////////////////////////////////////////
// CBayProfileBayAttributesPage dialog

class CBayProfileBayAttributesPage : public CPropertyPage
{
	DECLARE_DYNCREATE(CBayProfileBayAttributesPage)

// Construction
public:
	CBayProfileBayAttributesPage();
	~CBayProfileBayAttributesPage();

// Dialog Data
	//{{AFX_DATA(CBayProfileBayAttributesPage)
	enum { IDD = IDD_BAY_PROFILE_BAY_ATTRIBUTES };
	BOOL	m_BaySpanningAllowed;
	BOOL	m_Exclude;
	BOOL	m_Floating;
	BOOL	m_Hazard;
	CString	m_Name;
	CString	m_RackCost;
	CString	m_ActiveNote;
	//}}AFX_DATA


// Overrides
	// ClassWizard generate virtual function overrides
	//{{AFX_VIRTUAL(CBayProfileBayAttributesPage)
	public:
	virtual void OnOK();
	virtual BOOL OnKillActive();
	virtual BOOL OnSetActive();
	protected:
	virtual void DoDataExchange(CDataExchange* pDX);    // DDX/DDV support
	virtual BOOL OnNotify(WPARAM wParam, LPARAM lParam, LRESULT* pResult);
	//}}AFX_VIRTUAL

// Implementation
protected:
	// Generated message map functions
	//{{AFX_MSG(CBayProfileBayAttributesPage)
	virtual BOOL OnInitDialog();
	afx_msg void OnChangeName();
	afx_msg BOOL OnHelpInfo(HELPINFO* pHelpInfo);
	//}}AFX_MSG
	DECLARE_MESSAGE_MAP()

private:
	BOOL Validate();
	CBayProfile *m_pBayProfile;
};

//{{AFX_INSERT_LOCATION}}
// Microsoft Visual C++ will insert additional declarations immediately before the previous line.

#endif // !defined(AFX_BAYPROFILEBAYATTRIBUTESPAGE_H__C98367D2_5956_46BD_A682_20824BF2F1A9__INCLUDED_)

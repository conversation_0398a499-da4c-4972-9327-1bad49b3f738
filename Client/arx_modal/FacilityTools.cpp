// FacilityTools1.cpp : implementation file
//

#include "stdafx.h"
#include "modal.h"
#include "FacilityTools.h"
#include "TreeElement.h"
#include "HelpService.h"
#include "ssa_exception.h"
#include "Constants.h"
#include "Processing.h"
#include <math.h>
#include <afxtempl.h>
#include "BTreeHelper.h"
#include "AutoCADCommands.h"
#include "UtilityHelper.h"
#include "ElementMaintenanceHelper.h"
#include "LocationNumberingService.h"
#include "FacilityDataService.h"
#include "DataAccessService.h"
#include "FacilityHelper.h"

#ifdef _DEBUG
#define new DEBUG_NEW
#undef THIS_FILE
static char THIS_FILE[] = __FILE__;
#endif

/////////////////////////////////////////////////////////////////////////////
// CFacilityTools dialog



extern TreeElement changesTree;
extern CHelpService helpService;
extern CBTreeHelper bTreeHelper;
extern CUtilityHelper utilityHelper;
extern CElementMaintenanceHelper elementMaintenanceHelper;
extern CFacilityDataService facilityDataService;
extern CLocationNumberingService numberingService;
extern CDataAccessService dataAccessService;
extern CFacilityHelper facilityHelper;

extern int numItemsProcessed;

const int TREE_FACILITY = 0;
const int TREE_SECTION = 1;
const int TREE_AISLE = 2;
const int TREE_SIDE = 3;
const int TREE_BAY = 4;
const int TREE_LEVEL = 5;
const int TREE_LOCATION = 6;

CFacilityTools::CFacilityTools(CWnd* pParent /*=NULL*/)
	: CDialog(CFacilityTools::IDD, pParent)
{
	//{{AFX_DATA_INIT(CFacilityTools)
		// NOTE: the ClassWizard will add member initialization here
	//}}AFX_DATA_INIT
}


void CFacilityTools::DoDataExchange(CDataExchange* pDX)
{
	CDialog::DoDataExchange(pDX);
	//{{AFX_DATA_MAP(CFacilityTools)
	DDX_Control(pDX, IDC_FACILITY_TREE, m_TreeCtrl);
	//}}AFX_DATA_MAP
}


BEGIN_MESSAGE_MAP(CFacilityTools, CDialog)
	//{{AFX_MSG_MAP(CFacilityTools)
	ON_BN_CLICKED(IDC_COLOR, OnColor)
	ON_BN_CLICKED(IDC_PROPERTIES, OnProperties)
	ON_BN_CLICKED(IDC_DELETE, OnDelete)
	ON_BN_CLICKED(IDHELP, OnHelp)
	ON_WM_HELPINFO()
	ON_BN_CLICKED(IDC_COLLAPSE, OnCollapse)
	ON_NOTIFY(TVN_ITEMEXPANDING, IDC_FACILITY_TREE, OnItemexpandingFacilityTree)
	ON_BN_CLICKED(IDC_ZOOM, OnZoom)
	ON_NOTIFY(NM_DBLCLK, IDC_FACILITY_TREE, OnDblclkFacilityTree)
	//}}AFX_MSG_MAP
END_MESSAGE_MAP()

/////////////////////////////////////////////////////////////////////////////
// CFacilityTools message handlers

BOOL CFacilityTools::OnInitDialog() 
{
	CDialog::OnInitDialog();
	
	LoadTree();
	
	return TRUE;  // return TRUE unless you set the focus to a control
	              // EXCEPTION: OCX Property Pages should return FALSE
}

void CFacilityTools::OnColor() 
{
	HTREEITEM hItem, hParentItem;
	int type;
	CButton *pButton = (CButton*)GetDlgItem(IDC_RESET_COLORS);
	CStringArray handles;

	hItem = m_TreeCtrl.GetSelectedItem();
	if (hItem == NULL) {
		AfxMessageBox("Please select a facility element.");
		return;
	}

	type = GetItemType(hItem);
	
	switch (type) {
	case TREE_FACILITY:
		GetHandlesByFacility(hItem, handles);
		break;
	case TREE_SECTION:
		GetHandlesBySection(hItem, handles, FALSE);
		break;
	case TREE_AISLE:
		GetHandlesByAisle(hItem, handles, FALSE);
		break;
	case TREE_SIDE:
		GetHandlesBySide(hItem, handles);
		break;
	case TREE_BAY:
		GetHandlesByBay(hItem, handles);
		break;
	case TREE_LEVEL:
		hParentItem = m_TreeCtrl.GetParentItem(hItem);
		GetHandlesByBay(hParentItem, handles);
		break;
	case TREE_LOCATION:
		hParentItem = m_TreeCtrl.GetParentItem(hItem);
		hParentItem = m_TreeCtrl.GetParentItem(hParentItem);
		GetHandlesByBay(hParentItem, handles);
		break;
	}
	
	int colorIdx = CAutoCADCommands::GetColorChoice();

	if (pButton->GetCheck())
		CAutoCADCommands::ColorAllObjects();

	for (int i=0; i < handles.GetSize(); ++i)
		CAutoCADCommands::ColorDrawingObjectByHandle(handles[i], colorIdx);

	CAutoCADCommands::Flush();

}

void CFacilityTools::OnProperties() 
{
	HTREEITEM hItem;
	int type;

	hItem = m_TreeCtrl.GetSelectedItem();
	if (hItem == NULL) {
		AfxMessageBox("Please select a facility element.");
		return;
	}

	type = GetItemType(hItem);
	
	switch (type) {
	case TREE_FACILITY:
		ViewFacilityProperties();
		break;
	case TREE_SECTION:
		ViewSectionProperties();
		break;
	case TREE_AISLE:
		ViewAisleProperties();
		break;
	case TREE_SIDE:
		ViewSideProperties();
		break;
	case TREE_BAY:
		ViewBayProperties();
		break;
	case TREE_LEVEL:
		ViewLevelProperties();
		break;
	case TREE_LOCATION:
		ViewLocationProperties();
		break;
	}

}

void CFacilityTools::OnDelete() 
{
	HTREEITEM hItem;
	int type, dbid;
	CStringArray handles;
	CWaitCursor cwc;

	hItem = m_TreeCtrl.GetSelectedItem();
	if (hItem == NULL) {
		AfxMessageBox("Please select a facility element.");
		return;
	}

	type = GetItemType(hItem);
	dbid = m_TreeCtrl.GetItemData(hItem);

	switch (type) {
	case TREE_FACILITY:
		AfxMessageBox("You can not delete the current facility from within this function.");
		break;
	case TREE_SECTION:
		DelSection(hItem);

		break;
	case TREE_AISLE:
		DelAisle(hItem);
		break;
	case TREE_SIDE:
		DelSide(hItem);
		break;
	case TREE_BAY:
		DelBay(hItem);
		break;
	case TREE_LEVEL:
		DelLevel(hItem);
		break;
	case TREE_LOCATION:
		DelLocation(hItem);
		break;
	}


}

void CFacilityTools::OnHelp() 
{
	helpService.ShowScreenHelp(IDD);
}

BOOL CFacilityTools::OnHelpInfo(HELPINFO* pHelpInfo) 
{
	helpService.ShowFieldHelp(IDD, pHelpInfo->iCtrlId);
	
	return FALSE;
}

void CFacilityTools::OnCollapse() 
{
	HTREEITEM hItem = m_TreeCtrl.GetSelectedItem();

	if (hItem == NULL)
		hItem = m_TreeCtrl.GetChildItem(TVI_ROOT);

	m_TreeCtrl.Expand(hItem, TVE_COLLAPSE);
	
}

void CFacilityTools::LoadTree()
{
	int i;
	CStringArray sectionList;
	long sectionDBID;
	CString sectionDesc;
	qqhSLOTSection section;
	HTREEITEM hItem, hFacilityItem;
	qqhSLOTFacility facility;

	bTreeHelper.GetBtFacility(changesTree.fileOffset, facility);

	
	hFacilityItem = m_TreeCtrl.InsertItem(facility.getDescription().GetBuffer(0), TVI_ROOT, TVI_LAST);
	facility.getDescription().ReleaseBuffer();
	if (facility.getDBID() > 0)
		m_TreeCtrl.SetItemData(hFacilityItem, facility.getDBID());
	else
		m_TreeCtrl.SetItemData(hFacilityItem, 0 - changesTree.fileOffset);


	for (i=0; i < changesTree.treeChildren.GetSize(); ++i) {
		bTreeHelper.GetBtSection(changesTree.treeChildren[i].fileOffset, section);

		if (section.getDBID() > 0)
			sectionDBID = section.getDBID();
		else
			sectionDBID = 0 - changesTree.treeChildren[i].fileOffset;
		sectionDesc = section.getDescription();

		hItem = m_TreeCtrl.InsertItem(sectionDesc.GetBuffer(0), hFacilityItem, TVI_LAST);
		sectionDesc.ReleaseBuffer();
		m_TreeCtrl.SetItemData(hItem, sectionDBID);

		// Insert a dummy aisle item so the + will appear
		// todo: change this to do a count and only put the dummy record in if there are aisles
		hItem = m_TreeCtrl.InsertItem("dummy", hItem, TVI_LAST);
		m_TreeCtrl.SetItemData(hItem, 0);
	}

	m_TreeCtrl.Expand(TVI_ROOT, TVE_EXPAND);
}

void CFacilityTools::OnItemexpandingFacilityTree(NMHDR* pNMHDR, LRESULT* pResult) 
{
	NM_TREEVIEW* pNMTreeView = (NM_TREEVIEW*)pNMHDR;
	HTREEITEM hItem, hChildItem;
	int type, dbid;

	*pResult = 0;

	if (pNMTreeView->action != TVE_EXPAND)
		return;

	hItem = pNMTreeView->itemNew.hItem;
	hChildItem = m_TreeCtrl.GetChildItem(hItem);
	if (m_TreeCtrl.GetItemData(hChildItem) == 0) {	// need to load it up
		dbid = m_TreeCtrl.GetItemData(hItem);
		type = GetItemType(hItem);
		switch (type) {
		case TREE_FACILITY:
			break;
		case TREE_SECTION:
			AddAislesBySection(hItem);
			break;
		case TREE_AISLE:
			AddSidesByAisle(hItem);
			break;
		case TREE_SIDE:
			AddBaysBySide(hItem);
			break;
		case TREE_BAY:
			AddLevelsByBay(hItem);
			break;
		case TREE_LEVEL:
			AddLocationsByLevel(hItem);
			break;
		}
	}
}

int CFacilityTools::GetItemType(HTREEITEM &hItem)
{
	HTREEITEM hParentItem;

	hParentItem = m_TreeCtrl.GetParentItem(hItem);
	if (hParentItem == NULL)
		return TREE_FACILITY;

	hParentItem = m_TreeCtrl.GetParentItem(hParentItem);
	if (hParentItem == NULL)
		return TREE_SECTION;

	hParentItem = m_TreeCtrl.GetParentItem(hParentItem);
	if (hParentItem == NULL)
		return TREE_AISLE;

	hParentItem = m_TreeCtrl.GetParentItem(hParentItem);
	if (hParentItem == NULL)
		return TREE_SIDE;

	hParentItem = m_TreeCtrl.GetParentItem(hParentItem);
	if (hParentItem == NULL)
		return TREE_BAY;
		
	hParentItem = m_TreeCtrl.GetParentItem(hParentItem);
	if (hParentItem == NULL)
		return TREE_LEVEL;


	return TREE_LOCATION;
}

void CFacilityTools::ViewFacilityProperties()
{

	qqhSLOTFacility facility, oldFacility;
	
	bTreeHelper.GetBtFacility(changesTree.fileOffset, facility);

	oldFacility = facility;

	elementMaintenanceHelper.FacilityProperties(facility);

	if (! (oldFacility == facility)) {
		numItemsProcessed++;
		bTreeHelper.SetBtFacility(changesTree.fileOffset, facility);
	}
	
}

void CFacilityTools::ViewSectionProperties()
{
	
	qqhSLOTSection section, oldSection;
	int dbid;
	HTREEITEM hItem;
	
	hItem = m_TreeCtrl.GetSelectedItem();
	dbid = m_TreeCtrl.GetItemData(hItem);

	for (int i=0; i < changesTree.treeChildren.GetSize(); ++i) {
		if (changesTree.treeChildren[i].elementDBID == dbid ||
			changesTree.treeChildren[i].fileOffset == (0 - dbid) ) {
			bTreeHelper.GetBtSection(changesTree.treeChildren[i].fileOffset, section);

			oldSection = section;

			elementMaintenanceHelper.SectionProperties(section);

			if (! (oldSection == section)) {
				numItemsProcessed++;
				bTreeHelper.SetBtSection(changesTree.treeChildren[i].fileOffset, section);
				if (oldSection.getDescription() != section.getDescription()) {
					elementMaintenanceHelper.UpdateLocationsForSectionChange(changesTree.treeChildren[i].fileOffset, section);
					UpdateLocationsInTree(TREE_SECTION, hItem, section.getLocationMask(), section.getDescription());
					m_TreeCtrl.SetItemText(hItem, section.getDescription().GetBuffer(0));
					section.getDescription().ReleaseBuffer();
				}
			}
			
			break;
		}
	}


}

void CFacilityTools::ViewAisleProperties()
{
	TreeElement *aislePtr, *sectionPtr;
	qqhSLOTAisle aisle, oldAisle;
	qqhSLOTSection section;
	CString handle, sql;
	int dbid;
	HTREEITEM hItem;
	CStringArray results;
	
	CWaitCursor cwc;

	hItem = m_TreeCtrl.GetSelectedItem();
	dbid = m_TreeCtrl.GetItemData(hItem);


	if (dbid > 0) {
		sql.Format("select b.acadhandle "
			"from dbbay b, dbside si "
			"where b.dbsideid = si.dbsideid "
			"and si.dbaisleid = %d "
			"and rownum = 1", dbid);		// Oracle-specific
		
		try {
			dataAccessService.ExecuteQuery("GetAisleHandle", sql, results);
		}
		catch (Ssa_Exception e) {
			utilityHelper.ProcessError("Error retrieving aisle from database.", &e);
			return;
		}
		catch (...) {
			utilityHelper.ProcessError("Error retrieving aisle from database.");
			return;
		}
		
		// Need to handle getting item straight out of btree
		// That may be difficult since we don't have a dbid;
		// they will all be set to 0
		
		if (results.GetSize() > 0) {
			handle = results[0];
			handle.TrimRight("|");
			
			
			aislePtr = changesTree.getAisleByBayHandle(handle);
		}
	}
	else {
		aislePtr = changesTree.getAisleByOffset(0-dbid);		// since dbid is the negative file offset
	}
	if (aislePtr == NULL) {
		AfxMessageBox("Unable to determine the aisle for the selected object.\n");
		return;
	}

	if (bTreeHelper.GetBtAisle(aislePtr->fileOffset, aisle) < 0) {
		AfxMessageBox("Unable to read aisle.");
		return;
	}
	
	oldAisle = aisle;
	sectionPtr = aislePtr->treeParent;

	elementMaintenanceHelper.AisleProperties(aisle);

	if (! (aisle == oldAisle)) {
		aisle.setIsChanged("TRUE");
		bTreeHelper.SetBtAisle(aislePtr->fileOffset,aisle);
		numItemsProcessed++;
		
		if (oldAisle.getDescription() != aisle.getDescription()) {
			elementMaintenanceHelper.UpdateLocationsForAisleChange(sectionPtr->fileOffset, aislePtr->fileOffset, aisle);
			bTreeHelper.GetBtSection(sectionPtr->fileOffset, section);
			UpdateLocationsInTree(TREE_AISLE, hItem, section.getLocationMask(), aisle.getDescription());
			m_TreeCtrl.SetItemText(hItem, aisle.getDescription().GetBuffer(0));
			aisle.getDescription().ReleaseBuffer();
		}
	}

	return;


}

void CFacilityTools::ViewSideProperties()
{
	TreeElement *sidePtr;
	qqhSLOTSide side, oldSide;
	CString handle, sql;
	int dbid;
	HTREEITEM hItem;
	CStringArray results;
	
	CWaitCursor cwc;

	hItem = m_TreeCtrl.GetSelectedItem();
	dbid = m_TreeCtrl.GetItemData(hItem);


	if (dbid > 0) {

		sql.Format("select b.acadhandle "
			"from dbbay b, dbside si "
			"where b.dbsideid = %d "
			"and rownum = 1", dbid);		// Oracle-specific

		try {
			dataAccessService.ExecuteQuery("GetSideHandle", sql, results);
			if (results.GetSize() == 0) {
				AfxMessageBox("Error retrieving side from database.");
				return;
			}
		}
		catch (Ssa_Exception e) {
			utilityHelper.ProcessError("Error retrieving side from database.", &e);
			return;
		}
		catch (...) {
			utilityHelper.ProcessError("Error retrieving side from database.");
			return;
		}
		
		if (results.GetSize() > 0) {
			handle = results[0];
			handle.TrimRight("|");
			sidePtr = changesTree.getSideByBayHandle(handle);
		}
	}
	else {
		sidePtr = changesTree.getSideByOffset(0 - dbid);
	}
	
	if (sidePtr == NULL) {
		AfxMessageBox("Unable to determine the side for the selected object.\n");
		return;
	}

	if (bTreeHelper.GetBtSide(sidePtr->fileOffset, side) < 0) {
		AfxMessageBox("Unable to read side.");
		return;
	}

	oldSide = side;

	elementMaintenanceHelper.SideProperties(side);

	if (! (side == oldSide)) {
		numItemsProcessed++;
		side.setIsChanged("TRUE");
		bTreeHelper.SetBtSide(sidePtr->fileOffset,side);
	}

	return;
}

void CFacilityTools::ViewBayProperties()
{
	TreeElement *bayPtr, *sidePtr, *aislePtr, *sectionPtr;
	qqhSLOTBay bay, oldBay;
	qqhSLOTSection section;
	CString handle, sql;
	int dbid;
	HTREEITEM hItem;
	CStringArray results;
	
	CWaitCursor cwc;

	hItem = m_TreeCtrl.GetSelectedItem();
	dbid = m_TreeCtrl.GetItemData(hItem);


	if (dbid > 0) {

			sql.Format("select b.acadhandle "
			"from dbbay b "
			"where b.dbbayid = %d "
			"and rownum = 1", dbid);		// Oracle-specific

		try {
			dataAccessService.ExecuteQuery("GetBayHandle", sql, results);
			if (results.GetSize() == 0) {
				AfxMessageBox("Error retrieving bay from database.");
				return;
			}
		}
		catch (Ssa_Exception e) {
			utilityHelper.ProcessError("Error retrieving bay from database.", &e);
			return;
		}
		catch (...) {
			utilityHelper.ProcessError("Error retrieving bay from database.");
			return;
		}
		
		if (results.GetSize() > 0) {
			handle = results[0];
			handle.TrimRight("|");
			bayPtr = changesTree.getBayByHandle(handle);
		}
	}
	else {
		bayPtr = changesTree.getBayByOffset(0 - dbid);
	}

	if (bayPtr == NULL) {
		AfxMessageBox("Unable to determine the bay for the selected object.\n");
		return;
	}

	if (bTreeHelper.GetBtBay(bayPtr->fileOffset, bay) < 0) {
		AfxMessageBox("Unable to read bay.");
		return;
	}
	
	oldBay = bay;

	sidePtr = bayPtr->treeParent;
	aislePtr = sidePtr->treeParent;
	sectionPtr = aislePtr->treeParent;
	
	elementMaintenanceHelper.BayProperties(bay);

	if (! (bay == oldBay)) {
		bay.setIsChanged("TRUE");
		numItemsProcessed++;

		bTreeHelper.SetBtBay(bayPtr->fileOffset,bay);
	
		if (oldBay.getDescription() != bay.getDescription()) {
			elementMaintenanceHelper.UpdateLocationsForBayChange(sectionPtr->fileOffset, bayPtr->fileOffset, bay);
			bTreeHelper.GetBtSection(sectionPtr->fileOffset, section);
			UpdateLocationsInTree(TREE_BAY, hItem, section.getLocationMask(), bay.getDescription());
			m_TreeCtrl.SetItemText(hItem, bay.getDescription().GetBuffer(0));
			bay.getDescription().ReleaseBuffer();
		}

	}

	return;
}

void CFacilityTools::ViewLevelProperties()
{
	CString handle, sql;
	int dbid;
	HTREEITEM hItem;
	CStringArray results;
	qqhSLOTLevel oldLevel, level;
	qqhSLOTSection section;

	TreeElement *levelPtr, *bayPtr, *sidePtr, *aislePtr, *sectionPtr;
	CWaitCursor cwc;
	

	hItem = m_TreeCtrl.GetSelectedItem();
	dbid = m_TreeCtrl.GetItemData(hItem);


	if (dbid > 0) {

		sql.Format("select b.acadhandle "
			"from dbbay b, dblevel le "
			"where b.dbbayid = le.dbbayid "
			"and le.dblevelid = %d "
			"and rownum = 1", dbid);		// Oracle-specific

		try {
			dataAccessService.ExecuteQuery("GetLevelHandle", sql, results);
			if (results.GetSize() == 0) {
				AfxMessageBox("Error retrieving level from database.");
				return;
			}
		}
		catch (Ssa_Exception e) {
			utilityHelper.ProcessError("Error retrieving level from database.", &e);
			return;
		}
		catch (...) {
			utilityHelper.ProcessError("Error retrieving level from database.");
			return;
		}
		
		if (results.GetSize() > 0) {
			handle = results[0];
			handle.TrimRight("|");
			levelPtr = changesTree.getLevelByHandle(handle, dbid);
		}
	}
	else {
		levelPtr = changesTree.getLevelByOffset(0 - dbid);
	}

	if (levelPtr == NULL) {
		AfxMessageBox("Unable to find level in database.");
		return;
	}

	if (bTreeHelper.GetBtLevel(levelPtr->fileOffset, level) < 0) {
		AfxMessageBox("Unable to read level.");
		return;
	}


	bayPtr = levelPtr->treeParent;
	sidePtr = bayPtr->treeParent;
	aislePtr = sidePtr->treeParent;
	sectionPtr = aislePtr->treeParent;

	oldLevel = level;

	elementMaintenanceHelper.LevelProperties(level);

	if (! (level == oldLevel)) {
		numItemsProcessed++;
		
		level.setIsChanged("TRUE");
		bTreeHelper.SetBtLevel(levelPtr->fileOffset, level);
		
		if (oldLevel.getDescription() != level.getDescription()) {
			elementMaintenanceHelper.UpdateLocationsForLevelChange(sectionPtr->fileOffset, levelPtr->fileOffset, level);
			bTreeHelper.GetBtSection(sectionPtr->fileOffset, section);
			UpdateLocationsInTree(TREE_LEVEL, hItem, section.getLocationMask(), level.getDescription());
			m_TreeCtrl.SetItemText(hItem, level.getDescription().GetBuffer(0));
			level.getDescription().ReleaseBuffer();
		}
	}

}

void CFacilityTools::ViewLocationProperties()
{
	qqhSLOTLocation location, tempLocation, oldLocation;
	qqhSLOTLevel level;
	qqhSLOTBay bay;
	qqhSLOTSection section;
	qqhSLOTAisle aisle;
	int i;
	double totalWidth;
	CString handle, sql;
	int dbid;
	HTREEITEM hItem;
	CStringArray results;
	TreeElement *locationPtr, *levelPtr, *bayPtr, *sidePtr, *aislePtr, *sectionPtr, *tempLocPtr;

	CWaitCursor cwc;

	hItem = m_TreeCtrl.GetSelectedItem();
	dbid = m_TreeCtrl.GetItemData(hItem);


	if (dbid > 0) {
		sql.Format("select b.acadhandle "
			"from dbbay b, dblevel le, dblocation l "
			"where b.dbbayid = le.dbbayid "
			"and le.dblevelid = l.dblevelid "
			"and l.dblocationid = %d "
			"and rownum = 1", dbid);		// Oracle-specific

		try {
			dataAccessService.ExecuteQuery("GetLocationHandle", sql, results);
		}
		catch (Ssa_Exception e) {
			utilityHelper.ProcessError("Error retrieving location from database.", &e);
			return;
		}
		catch (...) {
			utilityHelper.ProcessError("Error retrieving location from database.");
			return;
		}
		
		if (results.GetSize() > 0) {

			handle = results[0];
			handle.TrimRight("|");

			locationPtr = changesTree.getLocationByHandle(handle, dbid);
		}
	}
	else {
		locationPtr = changesTree.getLocationByOffset(0 - dbid);
	}

	if (locationPtr == NULL) {
		AfxMessageBox("Unable to find location.");
		return;
	}

	levelPtr = locationPtr->treeParent;
	if (bTreeHelper.GetBtLevel(levelPtr->fileOffset, level) < 0) {
		AfxMessageBox("Unable to read level.");
		return;
	}
	bayPtr = levelPtr->treeParent;
	if (bTreeHelper.GetBtBay(bayPtr->fileOffset, bay) < 0) {
		AfxMessageBox("Unable to read bay.");
		return;
	}

	sidePtr = bayPtr->treeParent;
	aislePtr = sidePtr->treeParent;
	if (bTreeHelper.GetBtAisle(aislePtr->fileOffset, aisle) < 0) {
		AfxMessageBox("Unable to read aisle.");
		return;
	}
	sectionPtr = aislePtr->treeParent;
	if (bTreeHelper.GetBtSection(sectionPtr->fileOffset, section) < 0) {
		AfxMessageBox("Unable to read section.");
		return;
	}

	if (bTreeHelper.OpenBTree() < 0) {
		AfxMessageBox("Error updating locations.  Unable to open binary tree.");
		return;
	}

	// Need to add some logic here to only do this if descriptions have changed
	numberingService.UpdateLocationDescriptionPart(locationPtr->fileOffset,
		section.getLocationMask(), 1, section.getDescription());
	numberingService.UpdateLocationDescriptionPart(locationPtr->fileOffset,
		section.getLocationMask(), 2, aisle.getDescription());
	numberingService.UpdateLocationDescriptionPart(locationPtr->fileOffset,
		section.getLocationMask(), 3, bay.getDescription());
	numberingService.UpdateLocationDescriptionPart(locationPtr->fileOffset,
		section.getLocationMask(), 4, level.getDescription());
			
	if (bTreeHelper.CloseBTree() < 0) {
		AfxMessageBox("Warning.  Unable to close binary tree.");
		return;
	}

	if (bTreeHelper.GetBtLocation(locationPtr->fileOffset, location) < 0) {
		AfxMessageBox("Unable to read location.");
		return;
	}

	oldLocation = location;


	totalWidth = 0;
	for (i=0; i < levelPtr->treeChildren.GetSize(); ++i) {
		tempLocPtr = &(levelPtr->treeChildren[i]);
		if (bTreeHelper.GetBtLocation(tempLocPtr->fileOffset, tempLocation) >= 0) {
			totalWidth += tempLocation.getWidth();
		}
	}

	elementMaintenanceHelper.LocationProperties(location, level, bay, totalWidth);

	if (! (location == oldLocation)) {
		numItemsProcessed++;
		location.setIsChanged("TRUE");
		bTreeHelper.SetBtLocation(locationPtr->fileOffset, location);
		if (location.getDescription() != oldLocation.getDescription()) {
			m_TreeCtrl.SetItemText(hItem, location.getDescription().GetBuffer(0));
			location.getDescription().ReleaseBuffer();
		}
	}

	return;
}


void CFacilityTools::UpdateLocationsInTree(int type, HTREEITEM &hItem, CString &sectionMask, CString &newPart)
{
	HTREEITEM hNextItem, hAisleItem, hSideItem, hBayItem, hLevelItem, hLocItem;

	switch (type) {
	case TREE_SECTION:
		hAisleItem = m_TreeCtrl.GetChildItem(hItem);
		
		while (hAisleItem != NULL) {
			
			hSideItem = m_TreeCtrl.GetChildItem(hAisleItem);
			while (hSideItem != NULL) {

				hBayItem = m_TreeCtrl.GetChildItem(hSideItem);
				while (hBayItem != NULL) {
					hLevelItem = m_TreeCtrl.GetChildItem(hBayItem);

					while (hLevelItem != NULL) {
						
						hLocItem = m_TreeCtrl.GetChildItem(hLevelItem);
						while (hLocItem != NULL) {
							
							CString oldDesc = m_TreeCtrl.GetItemText(hLocItem);

							numberingService.GetChangedLocationDescription(oldDesc, sectionMask, 1, newPart);
							m_TreeCtrl.SetItemText(hLocItem, oldDesc.GetBuffer(0));
							oldDesc.ReleaseBuffer();

							hNextItem = m_TreeCtrl.GetNextItem(hLocItem, TVGN_NEXT);
							hLocItem = hNextItem;
						}

						hNextItem = m_TreeCtrl.GetNextItem(hLevelItem, TVGN_NEXT);
						hLevelItem = hNextItem;
					}

					hNextItem = m_TreeCtrl.GetNextItem(hBayItem, TVGN_NEXT);
					hBayItem = hNextItem;
				}

				hNextItem = m_TreeCtrl.GetNextItem(hSideItem, TVGN_NEXT);
				hSideItem = hNextItem;
			}

			hNextItem = m_TreeCtrl.GetNextItem(hAisleItem, TVGN_NEXT); 
			hAisleItem = hNextItem;
		}
		break;

	case TREE_AISLE:

		hSideItem = m_TreeCtrl.GetChildItem(hItem);
		while (hSideItem != NULL) {

			hBayItem = m_TreeCtrl.GetChildItem(hSideItem);
			while (hBayItem != NULL) {
				hLevelItem = m_TreeCtrl.GetChildItem(hBayItem);

				while (hLevelItem != NULL) {
					
					hLocItem = m_TreeCtrl.GetChildItem(hLevelItem);
					while (hLocItem != NULL) {
						
						CString oldDesc = m_TreeCtrl.GetItemText(hLocItem);

						numberingService.GetChangedLocationDescription(oldDesc, sectionMask, 2, newPart);
						m_TreeCtrl.SetItemText(hLocItem, oldDesc.GetBuffer(0));
						oldDesc.ReleaseBuffer();

						hNextItem = m_TreeCtrl.GetNextItem(hLocItem, TVGN_NEXT);
						hLocItem = hNextItem;
					}

					hNextItem = m_TreeCtrl.GetNextItem(hLevelItem, TVGN_NEXT);
					hLevelItem = hNextItem;
				}

				hNextItem = m_TreeCtrl.GetNextItem(hBayItem, TVGN_NEXT);
				hBayItem = hNextItem;
			}

			hNextItem = m_TreeCtrl.GetNextItem(hSideItem, TVGN_NEXT);
			hSideItem = hNextItem;
		}

		break;

	case TREE_BAY:

		hLevelItem = m_TreeCtrl.GetChildItem(hItem);

		while (hLevelItem != NULL) {
			
			hLocItem = m_TreeCtrl.GetChildItem(hLevelItem);
			while (hLocItem != NULL) {
				
				CString oldDesc = m_TreeCtrl.GetItemText(hLocItem);

				numberingService.GetChangedLocationDescription(oldDesc, sectionMask, 3, newPart);
				m_TreeCtrl.SetItemText(hLocItem, oldDesc.GetBuffer(0));
				oldDesc.ReleaseBuffer();

				hNextItem = m_TreeCtrl.GetNextItem(hLocItem, TVGN_NEXT);
				hLocItem = hNextItem;
			}

			hNextItem = m_TreeCtrl.GetNextItem(hLevelItem, TVGN_NEXT);
			hLevelItem = hNextItem;
		}

		break;

	case TREE_LEVEL:
			
		hLocItem = m_TreeCtrl.GetChildItem(hItem);
		while (hLocItem != NULL) {
			
			CString oldDesc = m_TreeCtrl.GetItemText(hLocItem);

			numberingService.GetChangedLocationDescription(oldDesc, sectionMask, 4, newPart);
			m_TreeCtrl.SetItemText(hLocItem, oldDesc.GetBuffer(0));
			oldDesc.ReleaseBuffer();

			hNextItem = m_TreeCtrl.GetNextItem(hLocItem, TVGN_NEXT);
			hLocItem = hNextItem;
		}

		break;
	}


	return;
}

void CFacilityTools::AddAislesBySection(HTREEITEM &hItem)
{
	int rc, parentDBID;
	//CMap<int, int, int, int> map;
	CStringArray strings, childList;
	CString description;
	qqhSLOTAisle aisle;
	HTREEITEM hChildItem;
	
	itemStruct *pItem;
	CMap<int, int, itemStruct*, itemStruct*> map;
	CList<itemStruct*, itemStruct*> list;
	CString temp;

	CWaitCursor cwc;

	parentDBID = m_TreeCtrl.GetItemData(hItem);
	
	TreeElement *sPtr, *aPtr;

	// Get aisles that are in the btree
	for (int sIdx=0; sIdx < changesTree.treeChildren.GetSize(); ++sIdx) {
		sPtr = &(changesTree.treeChildren[sIdx]);
		if (sPtr->elementDBID == parentDBID || sPtr->fileOffset == (0-parentDBID)) {
			for (int aIdx=0; aIdx < sPtr->treeChildren.GetSize(); ++aIdx) {
				aPtr = &(sPtr->treeChildren[aIdx]);
				if (aPtr->elementDBID > 0) {
					if (! map.Lookup(aPtr->elementDBID, pItem)) {
						bTreeHelper.GetBtAisle(aPtr->fileOffset, aisle);
						pItem = new itemStruct;
						pItem->dbid = aisle.getDBID();
						pItem->description = aisle.getDescription();
						pItem->strSortKey = aisle.getDescription();
						map.SetAt(pItem->dbid, pItem);
						list.AddTail(pItem);
					}
				}
				else {
					bTreeHelper.GetBtAisle(aPtr->fileOffset, aisle);
					pItem = new itemStruct;
					pItem->dbid = 0 - aPtr->fileOffset;
					pItem->description = aisle.getDescription();
					pItem->strSortKey = aisle.getDescription();
					map.SetAt(pItem->dbid, pItem);
					list.AddTail(pItem);
				}
			}
			break;
		}
	}
	if (parentDBID > 0) {
		
		try {
			rc = facilityDataService.GetAislesBySection(parentDBID, childList);
		}
		catch (Ssa_Exception e) {
			utilityHelper.ProcessError("Error retrieving aisles from database.", &e);
		}
		catch (...) {
			utilityHelper.ProcessError("Error retrieving aisles from database.");
		}
		
		// Add the items from the database that aren't in the btree also
		int dummy;
		for (int i=0; i < childList.GetSize(); ++i) {
			utilityHelper.ParseString(childList[i], "|", strings);
			
			if (changesTree.DeletedAisleMap.Lookup(atol(strings[0]), dummy))
				continue;

			if (! map.Lookup(atol(strings[0]), pItem)) {
				pItem = new itemStruct;		
				pItem->dbid = atol(strings[0]);
				pItem->description = strings[1];
				pItem->strSortKey = strings[1];
				map.SetAt(pItem->dbid, pItem);
				list.AddTail(pItem);
			}
		}
		
	}


	SortListByString(list);
		
	// Delete the dummy item
	hChildItem = m_TreeCtrl.GetChildItem(hItem);
	m_TreeCtrl.DeleteItem(hChildItem);

	POSITION pos;
	for (pos = list.GetHeadPosition(); pos != NULL; ) {
		pItem = list.GetNext(pos);

		hChildItem = m_TreeCtrl.InsertItem(pItem->description.GetBuffer(0), hItem, TVI_LAST);
		pItem->description.ReleaseBuffer();
		m_TreeCtrl.SetItemData(hChildItem, pItem->dbid);
		
		// Insert the dummy item for each child
		hChildItem = m_TreeCtrl.InsertItem("dummy", hChildItem, TVI_LAST);
		m_TreeCtrl.SetItemData(hChildItem, 0);

		delete pItem;
	}

	return;

}


void CFacilityTools::AddSidesByAisle(HTREEITEM &hItem)
{
	int rc, parentDBID;

	CStringArray strings, childList;
	CString description;
	qqhSLOTSide side;
	HTREEITEM hChildItem;
	itemStruct *pItem;
	CMap<int, int, itemStruct*, itemStruct*> map;
	CList<itemStruct*, itemStruct*> list;
	POSITION pos;

	CWaitCursor cwc;

	parentDBID = m_TreeCtrl.GetItemData(hItem);


	TreeElement *sPtr, *aPtr, *siPtr;

	// Now get any that are in the btree; omitting duplicates - these would be new aisles
	for (int sIdx=0; sIdx < changesTree.treeChildren.GetSize(); ++sIdx) {
		sPtr = &(changesTree.treeChildren[sIdx]);
		for (int aIdx=0; aIdx < sPtr->treeChildren.GetSize(); ++aIdx) {
			aPtr = &(sPtr->treeChildren[aIdx]);
			if (aPtr->elementDBID == parentDBID || aPtr->fileOffset == (0-parentDBID)) {
				for (int siIdx=0; siIdx < aPtr->treeChildren.GetSize(); ++siIdx) {
					siPtr = &(aPtr->treeChildren[siIdx]);

					if (siPtr->elementDBID > 0) {
						if (! map.Lookup(siPtr->elementDBID, pItem)) {
							bTreeHelper.GetBtSide(siPtr->fileOffset, side);
							pItem = new itemStruct;
							pItem->dbid = side.getDBID();
							pItem->description = side.getDescription();
							pItem->strSortKey = side.getDescription();
							map.SetAt(pItem->dbid, pItem);
							list.AddTail(pItem);
						}
					}
					else {
						bTreeHelper.GetBtSide(siPtr->fileOffset, side);
						pItem = new itemStruct;
						pItem->dbid = 0 - siPtr->fileOffset;
						pItem->description = side.getDescription();
						pItem->strSortKey = side.getDescription();
						map.SetAt(pItem->dbid, pItem);
						list.AddTail(pItem);
					}
				}
				break;
			}
		}
	}

	if (parentDBID > 0) {
		
		try {
			rc = facilityDataService.GetSidesByAisle(parentDBID, childList);
		}
		catch (Ssa_Exception e) {
			utilityHelper.ProcessError("Error retrieving sides from database.", &e);
		}
		catch (...) {
			utilityHelper.ProcessError("Error retrieving sides from database.");
		}
		
		// Now add the real items
		int dummy;
		for (int i=0; i < childList.GetSize(); ++i) {
			utilityHelper.ParseString(childList[i], "|", strings);
			
			if (changesTree.DeletedSideMap.Lookup(atol(strings[0]), dummy))
				continue;

			if (! map.Lookup(atol(strings[0]), pItem)) {			
				pItem = new itemStruct;
				
				pItem->dbid = atol(strings[0]);
				pItem->description = strings[1];
				pItem->strSortKey = strings[1];
				
				map.SetAt(pItem->dbid, pItem);
				list.AddTail(pItem);
			}
			
		}
	}

	SortListByString(list);

	// Delete the dummy item
	hChildItem = m_TreeCtrl.GetChildItem(hItem);
	m_TreeCtrl.DeleteItem(hChildItem);

	for (pos = list.GetHeadPosition(); pos != NULL; ) {
		pItem = list.GetNext(pos);

		hChildItem = m_TreeCtrl.InsertItem(pItem->description.GetBuffer(0), hItem, TVI_LAST);
		pItem->description.ReleaseBuffer();
		m_TreeCtrl.SetItemData(hChildItem, pItem->dbid);
		
		// Insert the dummy item for each child
		hChildItem = m_TreeCtrl.InsertItem("dummy", hChildItem, TVI_LAST);
		m_TreeCtrl.SetItemData(hChildItem, 0);

		delete pItem;
	}

	return;

}

void CFacilityTools::AddBaysBySide(HTREEITEM &hItem)
{
	int rc, parentDBID;

	CStringArray strings, childList;
	CString description;
	qqhSLOTBay bay;
	HTREEITEM hChildItem;
	itemStruct *pItem;
	CMap<int, int, itemStruct*, itemStruct*> map;
	CList<itemStruct*, itemStruct*> list;
	POSITION pos;

	CWaitCursor cwc;

	parentDBID = m_TreeCtrl.GetItemData(hItem);


	TreeElement *sPtr, *aPtr, *siPtr, *bPtr;

	// Now get any that are in the btree; omitting duplicates - these would be new aisles
	for (int sIdx=0; sIdx < changesTree.treeChildren.GetSize(); ++sIdx) {
		sPtr = &(changesTree.treeChildren[sIdx]);
		for (int aIdx=0; aIdx < sPtr->treeChildren.GetSize(); ++aIdx) {
			aPtr = &(sPtr->treeChildren[aIdx]);
			for (int siIdx=0; siIdx < aPtr->treeChildren.GetSize(); ++siIdx) {
				siPtr = &(aPtr->treeChildren[siIdx]);
				if (siPtr->elementDBID == parentDBID || siPtr->fileOffset == (0-parentDBID)) {
					for (int bIdx=0; bIdx < siPtr->treeChildren.GetSize(); ++bIdx) {
						bPtr = &(siPtr->treeChildren[bIdx]);
						if (bPtr->elementDBID > 0) {			// it's been saved in the database
							bTreeHelper.GetBtBay(bPtr->fileOffset, bay);
							pItem = new itemStruct;
							pItem->dbid = bay.getDBID();
							pItem->description = bay.getDescription();
							pItem->strSortKey = bay.getDescription();
							pItem->intSortKey = bay.getCoord().getX();
							map.SetAt(pItem->dbid, pItem);
							list.AddTail(pItem);
						}
						else {
							bTreeHelper.GetBtBay(bPtr->fileOffset, bay);
							pItem = new itemStruct;
							pItem->dbid = 0 - bPtr->fileOffset;
							pItem->description = bay.getDescription();
							pItem->strSortKey = bay.getDescription();
							pItem->intSortKey = bay.getCoord().getX();
							map.SetAt(pItem->dbid, pItem);
							list.AddTail(pItem);
						}
					}
					break;
				}
			}
		}
	}

	if (parentDBID > 0) {

		try {
			CString sql;
			sql.Format("select b.dbbayid, b.description, b.xcoordinate "
				"from dbbay b "
				"where b.dbsideid = %d", parentDBID);

			rc = dataAccessService.ExecuteQuery("GetBaysBySide", sql, childList);
		}
		catch (Ssa_Exception e) {
			utilityHelper.ProcessError("Error retrieving bays from database.", &e);
		}
		catch (...) {
			utilityHelper.ProcessError("Error retrieving bays from database.");
		}
		
		// Now add the real items
		int dummy;
		for (int i=0; i < childList.GetSize(); ++i) {
			utilityHelper.ParseString(childList[i], "|", strings);
			
			// If the bay has been deleted, skip it
			if (changesTree.DeletedBayMap.Lookup(atol(strings[0]), dummy))
				continue;

			if (! map.Lookup(atol(strings[0]), pItem)) {
				pItem = new itemStruct;
				
				pItem->dbid = atol(strings[0]);
				pItem->description = strings[1];
				pItem->strSortKey = strings[1];
				pItem->intSortKey = atoi(strings[2]);
				
				map.SetAt(pItem->dbid, pItem);
				list.AddTail(pItem);
			}
			
		}
	}

	SortListByString(list);

	// Delete the dummy item
	hChildItem = m_TreeCtrl.GetChildItem(hItem);
	m_TreeCtrl.DeleteItem(hChildItem);

	for (pos = list.GetHeadPosition(); pos != NULL; ) {
		pItem = list.GetNext(pos);

		hChildItem = m_TreeCtrl.InsertItem(pItem->description.GetBuffer(0), hItem, TVI_LAST);
		pItem->description.ReleaseBuffer();
		m_TreeCtrl.SetItemData(hChildItem, pItem->dbid);
		
		// Insert the dummy item for each child
		hChildItem = m_TreeCtrl.InsertItem("dummy", hChildItem, TVI_LAST);
		m_TreeCtrl.SetItemData(hChildItem, 0);

		delete pItem;
	}

	return;

}

	
void CFacilityTools::AddLevelsByBay(HTREEITEM &hItem)
{
	int rc, parentDBID;

	CStringArray strings, childList;
	CString description;
	qqhSLOTLevel level;
	HTREEITEM hChildItem;
	itemStruct *pItem;
	CMap<int, int, itemStruct*, itemStruct*> map;
	CList<itemStruct*, itemStruct*> list;
	POSITION pos;

	CWaitCursor cwc;

	parentDBID = m_TreeCtrl.GetItemData(hItem);

	TreeElement *sPtr, *aPtr, *siPtr, *bPtr, *lPtr;

	// Now get any that are in the btree; omitting duplicates - these would be new aisles
	for (int sIdx=0; sIdx < changesTree.treeChildren.GetSize(); ++sIdx) {
		sPtr = &(changesTree.treeChildren[sIdx]);
		for (int aIdx=0; aIdx < sPtr->treeChildren.GetSize(); ++aIdx) {
			aPtr = &(sPtr->treeChildren[aIdx]);
			for (int siIdx=0; siIdx < aPtr->treeChildren.GetSize(); ++siIdx) {
				siPtr = &(aPtr->treeChildren[siIdx]);
				for (int bIdx=0; bIdx < siPtr->treeChildren.GetSize(); ++bIdx) {
					bPtr = &(siPtr->treeChildren[bIdx]);
					if (bPtr->elementDBID == parentDBID || bPtr->fileOffset == (0-parentDBID)) {
						for (int lIdx=0; lIdx < bPtr->treeChildren.GetSize(); ++lIdx) {
							lPtr = &(bPtr->treeChildren[lIdx]);
							if (lPtr->elementDBID > 0) {	
								bTreeHelper.GetBtLevel(lPtr->fileOffset, level);
								pItem = new itemStruct;
								pItem->dbid = level.getDBID();
								pItem->description = level.getDescription();
								pItem->intSortKey = level.getCoord().getZ();
								map.SetAt(pItem->dbid, pItem);
								list.AddTail(pItem);	
							}
							else {
								bTreeHelper.GetBtLevel(lPtr->fileOffset, level);
								pItem = new itemStruct;
								pItem->dbid = 0 - lPtr->fileOffset;
								pItem->description = level.getDescription();
								pItem->intSortKey = level.getCoord().getZ();
								map.SetAt(pItem->dbid, pItem);
								list.AddTail(pItem);
							}
						}
						break;
					}
				}
			}
		}
	}

	if (parentDBID > 0) {
		try {
			CString sql;
			sql.Format("select le.dblevelid, le.description, lp.relativelevel "
				"from dblevel le, dblevelprofile lp "
				"where le.dbbayid = %d "
				"and lp.dblevelprofileid = le.dblevelprofileid", parentDBID);

			rc = dataAccessService.ExecuteQuery("GetLevelsByBay", sql, childList);
		}
		catch (Ssa_Exception e) {
			utilityHelper.ProcessError("Error retrieving levels from database.", &e);
		}
		catch (...) {
			utilityHelper.ProcessError("Error retrieving levels from database.");
		}

		
		// Now add the real items
		for (int i=0; i < childList.GetSize(); ++i) {
			utilityHelper.ParseString(childList[i], "|", strings);
			if (! map.Lookup(atol(strings[0]), pItem)) {
				pItem = new itemStruct;
				
				pItem->dbid = atol(strings[0]);
				pItem->description = strings[1];
				// for levels, the sort key is the relative level
				pItem->intSortKey = atoi(strings[2]);
				
				map.SetAt(pItem->dbid, pItem);
				list.AddTail(pItem);
			}
			
		}
	}


	SortListByInt(list);

	// Delete the dummy item
	hChildItem = m_TreeCtrl.GetChildItem(hItem);
	m_TreeCtrl.DeleteItem(hChildItem);
		
	for (pos = list.GetHeadPosition(); pos != NULL; ) {
		pItem = list.GetNext(pos);

		hChildItem = m_TreeCtrl.InsertItem(pItem->description.GetBuffer(0), hItem, TVI_LAST);
		pItem->description.ReleaseBuffer();
		m_TreeCtrl.SetItemData(hChildItem, pItem->dbid);
		
		// Insert the dummy item for each child
		hChildItem = m_TreeCtrl.InsertItem("dummy", hChildItem, TVI_LAST);
		m_TreeCtrl.SetItemData(hChildItem, 0);

		delete pItem;
	}

	return;

}


void CFacilityTools::AddLocationsByLevel(HTREEITEM &hItem)
{
	int rc, parentDBID;

	CStringArray strings, childList;
	CString description;
	qqhSLOTLocation location;
	qqhSLOTAisle aisle;
	HTREEITEM hChildItem;
	itemStruct *pItem;
	CMap<int, int, itemStruct*, itemStruct*> map;
	CList<itemStruct*, itemStruct*> list;
	POSITION pos;

	CWaitCursor cwc;

	parentDBID = m_TreeCtrl.GetItemData(hItem);
	

	TreeElement *sPtr, *aPtr, *siPtr, *bPtr, *lPtr, *loPtr;

	// Now get any that are in the btree; omitting duplicates - these would be new aisles
	for (int sIdx=0; sIdx < changesTree.treeChildren.GetSize(); ++sIdx) {
		sPtr = &(changesTree.treeChildren[sIdx]);
		for (int aIdx=0; aIdx < sPtr->treeChildren.GetSize(); ++aIdx) {
			aPtr = &(sPtr->treeChildren[aIdx]);
			for (int siIdx=0; siIdx < aPtr->treeChildren.GetSize(); ++siIdx) {
				siPtr = &(aPtr->treeChildren[siIdx]);
				for (int bIdx=0; bIdx < siPtr->treeChildren.GetSize(); ++bIdx) {
					bPtr = &(siPtr->treeChildren[bIdx]);
					for (int lIdx=0; lIdx < bPtr->treeChildren.GetSize(); ++lIdx) {
						lPtr = &(bPtr->treeChildren[lIdx]);
						if (lPtr->elementDBID == parentDBID || lPtr->fileOffset == (0-parentDBID)) {
							for (int loIdx=0; loIdx < lPtr->treeChildren.GetSize(); ++loIdx) {
								loPtr = &(lPtr->treeChildren[loIdx]);
								if (loPtr->elementDBID > 0) {
									bTreeHelper.GetBtLocation(loPtr->fileOffset, location);
									bTreeHelper.GetBtAisle(aPtr->fileOffset, aisle);
									pItem = new itemStruct;
									pItem->dbid = location.getDBID();
									pItem->description = location.getDescription();
									pItem->intSortKey = (int)(location.getCoord().getX()*cos(-0.0175*aisle.getRotation()) -
										location.getCoord().getY()*sin(-0.0175*aisle.getRotation()));
									map.SetAt(pItem->dbid, pItem);
									list.AddTail(pItem);
								}
								else {
									// if it's a new element, use the negative fileoffset as the dbid
									// since it will be unique
									bTreeHelper.GetBtLocation(loPtr->fileOffset, location);
									bTreeHelper.GetBtAisle(aPtr->fileOffset, aisle);
									pItem = new itemStruct;
									pItem->dbid = 0 - loPtr->fileOffset;
									pItem->description = location.getDescription();
									pItem->intSortKey = (int)(location.getCoord().getX()*cos(-0.0175*aisle.getRotation()) -
											location.getCoord().getY()*sin(-0.0175*aisle.getRotation()));
									map.SetAt(pItem->dbid, pItem);
									list.AddTail(pItem);
								}

							}
							break;
						}
					}
				}
			}
		}
	}

	if (parentDBID > 0) {
		
		try {
			CString sql;
			sql.Format("select l.dblocationid, l.description, lop.description, a.rotation, l.xcoordinate, l.ycoordinate "
				"from dblocation l, dblocationprof lop, dblevel le, dbbay b, dbside si, dbaisle a "
				"where l.dblevelid = %d "
				"and le.dblevelid = l.dblevelid "
				"and b.dbbayid = le.dbbayid "
				"and si.dbsideid = b.dbsideid "
				"and a.dbaisleid = si.dbaisleid "
				"and lop.dblocationprofid = l.dblocationprofid", parentDBID);
			
			rc = dataAccessService.ExecuteQuery("GetLocationsByLevel", sql, childList);
		}
		catch (Ssa_Exception e) {
			utilityHelper.ProcessError("Error retrieving locations from database.", &e);
		}
		catch (...) {
			utilityHelper.ProcessError("Error retrieving locations from database.");
		}
	
		
		// Now add the real items
		for (int i=0; i < childList.GetSize(); ++i) {
			utilityHelper.ParseString(childList[i], "|", strings);
			if (! map.Lookup(atol(strings[0]), pItem)) {
				pItem = new itemStruct;
				
				pItem->dbid = atol(strings[0]);
				pItem->description = strings[1];
				// for locations, the sort key is the location profile description
				//pItem->intSortKey = atoi(strings[2]);
				// the secondary sort key is the rotated xcoordinate
				int x = atoi(strings[4]), y = atoi(strings[5]);
				double rotation = atof(strings[3]);
				pItem->intSortKey = (int)(x*cos(-0.0175*rotation)-y*sin(-0.0175*rotation));
				map.SetAt(pItem->dbid, pItem);
				list.AddTail(pItem);
			}
			
		}
	}

	SortListByString(list);
		
	// Delete the dummy item
	hChildItem = m_TreeCtrl.GetChildItem(hItem);
	m_TreeCtrl.DeleteItem(hChildItem);

	for (pos = list.GetHeadPosition(); pos != NULL; ) {
		pItem = list.GetNext(pos);

		HTREEITEM hSectionItem, hAisleItem, hSideItem, hBayItem;
		hBayItem = m_TreeCtrl.GetParentItem(hItem);
		hSideItem = m_TreeCtrl.GetParentItem(hBayItem);
		hAisleItem = m_TreeCtrl.GetParentItem(hSideItem);
		hSectionItem = m_TreeCtrl.GetParentItem(hAisleItem);
		qqhSLOTSection section;

		int dbid = m_TreeCtrl.GetItemData(hSectionItem);
		
		if (dbid > 0)
			sPtr = changesTree.getSectionByDBID(dbid);
		else
			sPtr = changesTree.getSectionByOffset(0 - dbid);
		
		bTreeHelper.GetBtSection(sPtr->fileOffset, section);

		CString sectionDesc, aisleDesc, bayDesc, levelDesc;
		sectionDesc = section.getDescription();
		aisleDesc = m_TreeCtrl.GetItemText(hAisleItem);
		bayDesc = m_TreeCtrl.GetItemText(hBayItem);
		levelDesc = m_TreeCtrl.GetItemText(hItem);

		numberingService.GetChangedLocationDescription(pItem->description, section.getLocationMask(), 1, sectionDesc);
		numberingService.GetChangedLocationDescription(pItem->description, section.getLocationMask(), 2, aisleDesc);
		numberingService.GetChangedLocationDescription(pItem->description, section.getLocationMask(), 3, bayDesc);
		numberingService.GetChangedLocationDescription(pItem->description, section.getLocationMask(), 4, levelDesc);

		hChildItem = m_TreeCtrl.InsertItem(pItem->description.GetBuffer(0), hItem, TVI_LAST);
		pItem->description.ReleaseBuffer();
		m_TreeCtrl.SetItemData(hChildItem, pItem->dbid);
		
		delete pItem;
	}

	return;

}

void CFacilityTools::SortListByString(CList<itemStruct*, itemStruct*> &list)
{
	itemStruct **structList;

	structList = (itemStruct **)malloc(list.GetCount()*sizeof(itemStruct*));
	int i = 0, count = list.GetCount();

	for (POSITION pos = list.GetHeadPosition(); pos != NULL; ) {
		structList[i] = (itemStruct*)list.GetNext(pos);
		i++;
	}

	qsort(structList, count, sizeof(itemStruct *), (int (*)(const void *, const void *))CFacilityTools::CompareStrings);

	list.RemoveAll();
	for (i=0; i < count; ++i) {
		list.AddTail(structList[i]);
	}
	
	delete structList;
}


void CFacilityTools::SortListByInt(CList<itemStruct*, itemStruct*> &list)
{
	itemStruct **structList;

	structList = (itemStruct **)malloc(list.GetCount()*sizeof(itemStruct*));
	int i = 0, count = list.GetCount();

	for (POSITION pos = list.GetHeadPosition(); pos != NULL; ) {
		structList[i] = (itemStruct*)list.GetNext(pos);
		i++;
	}

	qsort(structList, count, sizeof(itemStruct *), (int (*)(const void *, const void *))CFacilityTools::CompareInts);

	list.RemoveAll();
	for (i=0; i < count; ++i) {
		list.AddTail(structList[i]);
	}

	delete structList;

}

int CFacilityTools::CompareStrings(const void **p1, const void **p2) 
{
	
	itemStruct *c1 = (itemStruct *)*p1;
	itemStruct *c2 = (itemStruct *)*p2;

	if (c1->strSortKey < c2->strSortKey)
		return -1;
	else
		if (c1->strSortKey > c2->strSortKey)
			return 1;
		else
			if (c1->intSortKey < c2->intSortKey)
				return -1;
			else
				return 1;



}

int CFacilityTools::CompareInts(const void **p1, const void **p2) 
{
	
	itemStruct *c1 = (itemStruct *)*p1;
	itemStruct *c2 = (itemStruct *)*p2;

	if (c1->intSortKey < c2->intSortKey)
		return -1;
	else
		return 1;

}


void CFacilityTools::GetHandlesByFacility(HTREEITEM hItem, CStringArray &handles)
{
	int dbid = m_TreeCtrl.GetItemData(hItem);
	CString sql, str;
	CMapStringToString map;
	CStringArray results;

	handles.RemoveAll();

	if (dbid > 0) {
		
		sql.Format("select b.dbbayid, b.acadhandle "
			"from dbbay b, dbside si, dbaisle a, dbsection s "
			"where b.dbsideid = si.dbsideid "
			"and si.dbaisleid = a.dbaisleid "
			"and a.dbsectionid = s.dbsectionid "
			"and s.dbfacilityid = %d", dbid);
		
		try {
			dataAccessService.ExecuteQuery("GetHandlesByFacility", sql, results);
		}
		catch (Ssa_Exception e) {
			utilityHelper.ProcessError("Error getting handles for facility.", &e);
			return;
		}
		catch (...) {
			utilityHelper.ProcessError("Error getting handles for facility.");
			return;
		}
		
		int dummy;
		for (int i=0; i < results.GetSize(); ++i) {
			int dbid = atoi(results[i].Left(results[i].Find("|")));
			if (changesTree.DeletedBayMap.Lookup(dbid, dummy))
				continue;

			results[i].TrimRight("|");
			CString handle = results[i].Mid(results[i].Find("|")+1);
			map.SetAt(handle, str);
			handles.Add(handle);
		}
	}

	TreeElement *sPtr, *aPtr, *siPtr, *bPtr;

	// later come back and eliminate dups
	for (int sIdx=0; sIdx < changesTree.treeChildren.GetSize(); ++sIdx) {
		sPtr = &(changesTree.treeChildren[sIdx]);	
		for (int aIdx=0; aIdx < sPtr->treeChildren.GetSize(); ++aIdx) {
			aPtr = &(sPtr->treeChildren[aIdx]);
			for (int siIdx=0; siIdx < aPtr->treeChildren.GetSize(); ++siIdx) {
				siPtr = &(aPtr->treeChildren[siIdx]);
				for (int bIdx=0; bIdx < siPtr->treeChildren.GetSize(); ++bIdx) {
					bPtr = &(siPtr->treeChildren[bIdx]);
					if (! map.Lookup(bPtr->acadHandle, str))
						handles.Add(bPtr->acadHandle);
				}
			}
			
		}
	}

	return;


}

void CFacilityTools::GetHandlesBySection(HTREEITEM hItem, CStringArray &handles, BOOL bIncludePickPath)
{
	int dbid = m_TreeCtrl.GetItemData(hItem);
	CString sql, str;
	CMapStringToString map;
	CStringArray results;

	handles.RemoveAll();

	if (dbid > 0) {

		sql.Format("select b.dbbayid, b.acadhandle "
			"from dbbay b, dbside si, dbaisle a "
			"where b.dbsideid = si.dbsideid "
			"and si.dbaisleid = a.dbaisleid "
			"and a.dbsectionid = %d", dbid);

		try {
			dataAccessService.ExecuteQuery("GetHandlesBySection", sql, results);
		}
		catch (Ssa_Exception e) {
			utilityHelper.ProcessError("Error getting handles for section.", &e);
			return;
		}
		catch (...) {
			utilityHelper.ProcessError("Error getting handles for section.");
			return;
		}

		int dummy;
		for (int i=0; i < results.GetSize(); ++i) {
			int dbid = atoi(results[i].Left(results[i].Find("|")));
			if (changesTree.DeletedBayMap.Lookup(dbid, dummy))
				continue;

			results[i].TrimRight("|");
			CString handle = results[i].Mid(results[i].Find("|")+1);
			map.SetAt(handle, "whatever");
			handles.Add(handle);
		}
	}

	TreeElement *sPtr, *aPtr, *siPtr, *bPtr;
	int sIdx, aIdx, siIdx, bIdx, i;

	// later come back and eliminate dups
	for (sIdx=0; sIdx < changesTree.treeChildren.GetSize(); ++sIdx) {
		sPtr = &(changesTree.treeChildren[sIdx]);
		if (sPtr->elementDBID == dbid || sPtr->fileOffset == (0-dbid)) {
			for (aIdx=0; aIdx < sPtr->treeChildren.GetSize(); ++aIdx) {
				aPtr = &(sPtr->treeChildren[aIdx]);
				for (siIdx=0; siIdx < aPtr->treeChildren.GetSize(); ++siIdx) {
					siPtr = &(aPtr->treeChildren[siIdx]);
					for (bIdx=0; bIdx < siPtr->treeChildren.GetSize(); ++bIdx) {
						bPtr = &(siPtr->treeChildren[bIdx]);
						if (! map.Lookup(bPtr->acadHandle, str))
							handles.Add(bPtr->acadHandle);
					}
				}
			}
			break;
		}
	}

	if (! bIncludePickPath)
		return;

	if (dbid > 0) {

		sql.Format("select a.dbaisleid, pp.acadhandle "
			"from dbaisle a, dbaislepath ap, dbpickpath pp "
			"where a.dbsectionid = %d "
			"and ap.dbaisleid = a.dbaisleid "
			"and pp.dbpickpathid = ap.dbpickpathid", dbid);

		try {
			results.RemoveAll();
			dataAccessService.ExecuteQuery("GetHandlesBySection", sql, results);
		}
		catch (Ssa_Exception e) {
			utilityHelper.ProcessError("Error getting handles for section.", &e);
			return;
		}
		catch (...) {
			utilityHelper.ProcessError("Error getting handles for section.");
			return;
		}

		int dummy;
		for (i=0; i < results.GetSize(); ++i) {
			int dbid = atoi(results[i].Left(results[i].Find("|")));
			if (changesTree.DeletedAisleMap.Lookup(dbid, dummy))
				continue;

			results[i].TrimRight("|");
			CString handle = results[i].Mid(results[i].Find("|")+1);
			if (handle == "XXX")
				continue;
			map.SetAt(handle, "whatever");
			handles.Add(handle);
		}
	}

	// later come back and eliminate dups
	for (sIdx=0; sIdx < changesTree.treeChildren.GetSize(); ++sIdx) {
		sPtr = &(changesTree.treeChildren[sIdx]);
		if (sPtr->elementDBID == dbid || sPtr->fileOffset == (0-dbid)) {
			for (aIdx=0; aIdx < sPtr->treeChildren.GetSize(); ++aIdx) {
				aPtr = &(sPtr->treeChildren[aIdx]);
				if (strcmp(aPtr->acadHandle, "XXX") != 0) {
					if (! map.Lookup(aPtr->acadHandle, str))
						handles.Add(aPtr->acadHandle);
				}
			}
			break;
		}
	}

	return;
}

void CFacilityTools::GetHandlesByAisle(HTREEITEM hItem, CStringArray &handles, BOOL bIncludePickPath)
{
	int dbid = m_TreeCtrl.GetItemData(hItem);
	CString sql, str;
	CMapStringToString map;
	CStringArray results;
	int sIdx, aIdx, siIdx, bIdx, i;

	handles.RemoveAll();

	if (dbid > 0) {

		sql.Format("select b.dbbayid, b.acadhandle "
			"from dbbay b, dbside si "
			"where b.dbsideid = si.dbsideid "
			"and si.dbaisleid = %d", dbid);

		try {
			dataAccessService.ExecuteQuery("GetHandlesByAisle", sql, results);
		}
		catch (Ssa_Exception e) {
			utilityHelper.ProcessError("Error getting handles for aisle.", &e);
			return;
		}
		catch (...) {
			utilityHelper.ProcessError("Error getting handles for aisle.");
			return;
		}

		int dummy;
		for (i=0; i < results.GetSize(); ++i) {
			int dbid = atoi(results[i].Left(results[i].Find("|")));
			if (changesTree.DeletedBayMap.Lookup(dbid, dummy))
				continue;

			results[i].TrimRight("|");
			CString handle = results[i].Mid(results[i].Find("|")+1);
			map.SetAt(handle, "whatever");
			handles.Add(handle);
		}
	}

	TreeElement *sPtr, *aPtr, *siPtr, *bPtr;

	// later come back and eliminate dups
	for (sIdx=0; sIdx < changesTree.treeChildren.GetSize(); ++sIdx) {
		sPtr = &(changesTree.treeChildren[sIdx]);
		for (aIdx=0; aIdx < sPtr->treeChildren.GetSize(); ++aIdx) {
			aPtr = &(sPtr->treeChildren[aIdx]);
			if (aPtr->elementDBID == dbid || aPtr->fileOffset == (0-dbid)) {
				for (siIdx=0; siIdx < aPtr->treeChildren.GetSize(); ++siIdx) {
					siPtr = &(aPtr->treeChildren[siIdx]);
					for (bIdx=0; bIdx < siPtr->treeChildren.GetSize(); ++bIdx) {
						bPtr = &(siPtr->treeChildren[bIdx]);
						if (! map.Lookup(bPtr->acadHandle, str))
							handles.Add(bPtr->acadHandle);
					}
				}
				break;
			}
		}
	}

	if (! bIncludePickPath)
		return;

	if (dbid > 0) {

		sql.Format("select a.dbaisleid, pp.acadhandle "
			"from dbaisle a, dbaislepath ap, dbpickpath pp "
			"where a.dbaisleid = %d "
			"and ap.dbaisleid = a.dbaisleid "
			"and pp.dbpickpathid = ap.dbpickpathid", dbid);

		try {
			results.RemoveAll();
			dataAccessService.ExecuteQuery("GetPickPathHandlesByAisle", sql, results);
		}
		catch (Ssa_Exception e) {
			utilityHelper.ProcessError("Error getting handles for aisle.", &e);
			return;
		}
		catch (...) {
			utilityHelper.ProcessError("Error getting handles for aisle.");
			return;
		}

		int dummy;
		for (i=0; i < results.GetSize(); ++i) {
			int dbid = atoi(results[i].Left(results[i].Find("|")));
			if (changesTree.DeletedAisleMap.Lookup(dbid, dummy))
				continue;

			results[i].TrimRight("|");
			CString handle = results[i].Mid(results[i].Find("|")+1);
			map.SetAt(handle, "whatever");
			handles.Add(handle);
		}
	}
	
	for (sIdx=0; sIdx < changesTree.treeChildren.GetSize(); ++sIdx) {
		sPtr = &(changesTree.treeChildren[sIdx]);
		for (aIdx=0; aIdx < sPtr->treeChildren.GetSize(); ++aIdx) {
			aPtr = &(sPtr->treeChildren[aIdx]);
			if (aPtr->elementDBID == dbid || aPtr->fileOffset == (0-dbid)) {		
				if (! map.Lookup(aPtr->acadHandle, str))
					handles.Add(aPtr->acadHandle);
				break;
			}
		}
	}

	return;
}

void CFacilityTools::GetHandlesBySide(HTREEITEM hItem, CStringArray &handles)
{
	int dbid = m_TreeCtrl.GetItemData(hItem);
	CString sql, str;
	CMapStringToString map;
	CStringArray results;

	handles.RemoveAll();

	if (dbid > 0) {

		sql.Format("select b.dbbayid, b.acadhandle "
			"from dbbay b "
			"where b.dbsideid = %d", dbid);

		try {
			dataAccessService.ExecuteQuery("GetHandlesBySide", sql, results);
		}
		catch (Ssa_Exception e) {
			utilityHelper.ProcessError("Error getting handles for side.", &e);
			return;
		}
		catch (...) {
			utilityHelper.ProcessError("Error getting handles for side.");
			return;
		}

		int dummy;
		for (int i=0; i < results.GetSize(); ++i) {
			int dbid = atoi(results[i].Left(results[i].Find("|")));
			if (changesTree.DeletedBayMap.Lookup(dbid, dummy))
				continue;

			results[i].TrimRight("|");
			CString handle = results[i].Mid(results[i].Find("|")+1);
			map.SetAt(handle, "whatever");
			handles.Add(handle);
		}
	}

	TreeElement *sPtr, *aPtr, *siPtr, *bPtr;
	
	// later come back and eliminate dups
	for (int sIdx=0; sIdx < changesTree.treeChildren.GetSize(); ++sIdx) {
		sPtr = &(changesTree.treeChildren[sIdx]);
		for (int aIdx=0; aIdx < sPtr->treeChildren.GetSize(); ++aIdx) {
			aPtr = &(sPtr->treeChildren[aIdx]);
			for (int siIdx=0; siIdx < aPtr->treeChildren.GetSize(); ++siIdx) {
				siPtr = &(aPtr->treeChildren[siIdx]);
				if (siPtr->elementDBID == dbid || siPtr->fileOffset == (0-dbid)) {
					siPtr = &(aPtr->treeChildren[siIdx]);
					for (int bIdx=0; bIdx < siPtr->treeChildren.GetSize(); ++bIdx) {
						bPtr = &(siPtr->treeChildren[bIdx]);
						if (! map.Lookup(bPtr->acadHandle, str))
							handles.Add(bPtr->acadHandle);
					}
					return;
				}
			}
			
		}
	}

	return;

}

void CFacilityTools::GetHandlesByBay(HTREEITEM hItem, CStringArray &handles)
{
	int dbid = m_TreeCtrl.GetItemData(hItem);
	CString sql, str;
	CMapStringToString map;
	CStringArray results;

	handles.RemoveAll();
	
	if (dbid > 0) {

		sql.Format("select b.dbbayid, b.acadhandle "
			"from dbbay b "
			"where b.dbbayid = %d", dbid);

		try {
			dataAccessService.ExecuteQuery("GetHandlesByBay", sql, results);
		}
		catch (Ssa_Exception e) {
			utilityHelper.ProcessError("Error getting handles for bay.", &e);
			return;
		}
		catch (...) {
			utilityHelper.ProcessError("Error getting handles for bay.");
			return;
		}

		int dummy;
		for (int i=0; i < results.GetSize(); ++i) {
			int dbid = atoi(results[i].Left(results[i].Find("|")));
			if (changesTree.DeletedBayMap.Lookup(dbid, dummy))
				continue;

			results[i].TrimRight("|");
			CString handle = results[i].Mid(results[i].Find("|")+1);
			map.SetAt(handle, "whatever");
			handles.Add(handle);
		}
	}

	TreeElement *sPtr, *aPtr, *siPtr, *bPtr;
	
	// later come back and eliminate dups
	for (int sIdx=0; sIdx < changesTree.treeChildren.GetSize(); ++sIdx) {
		sPtr = &(changesTree.treeChildren[sIdx]);
		for (int aIdx=0; aIdx < sPtr->treeChildren.GetSize(); ++aIdx) {
			aPtr = &(sPtr->treeChildren[aIdx]);
			for (int siIdx=0; siIdx < aPtr->treeChildren.GetSize(); ++siIdx) {
				siPtr = &(aPtr->treeChildren[siIdx]);
				for (int bIdx=0; bIdx < siPtr->treeChildren.GetSize(); ++bIdx) {
					bPtr = &(siPtr->treeChildren[bIdx]);
					if (bPtr->elementDBID == dbid || bPtr->fileOffset == (0-dbid)) {
						bPtr = &(siPtr->treeChildren[bIdx]);
						if (! map.Lookup(bPtr->acadHandle, str))
							handles.Add(bPtr->acadHandle);
						return;
					}
				}
				
			}
			
		}
	}

	return;
}

void CFacilityTools::GetHandlesByLevel(HTREEITEM hItem, CStringArray &handles)
{
	UNREFERENCED_PARAMETER(hItem);
	UNREFERENCED_PARAMETER(handles);
}

void CFacilityTools::GetHandlesByLocation(HTREEITEM hItem, CStringArray &handles)
{
	UNREFERENCED_PARAMETER(hItem);
	UNREFERENCED_PARAMETER(handles);
}

void CFacilityTools::OnZoom() 
{
	ads_point point1, point2;
	HTREEITEM hItem, hParentItem;
	int type;
	CStringArray handles;

	struct resbuf rb;

	hItem = m_TreeCtrl.GetSelectedItem();
	if (hItem == NULL) {
		AfxMessageBox("Please select a facility element.");
		return;
	}
	
	ads_getvar("EXTMIN", &rb);
	point1[X] = rb.resval.rpoint[X];
	point1[Y] = rb.resval.rpoint[Y];

	ads_getvar("EXTMAX", &rb);
	point2[X] = rb.resval.rpoint[X];
	point2[Y] = rb.resval.rpoint[Y];

	type = GetItemType(hItem);
	switch (type) {
	case TREE_FACILITY:
		ads_command(RTSTR, "ZOOM", RTSTR, "A", RTNONE);
		return;
		break;
	case TREE_SECTION:
		GetSectionExtents(hItem, point1, point2);
		break;
	case TREE_AISLE:
		GetAisleExtents(hItem, point1, point2);
		break;
	case TREE_SIDE:
		GetSideExtents(hItem, point1, point2);
		break;
	case TREE_BAY:
		GetHandlesByBay(hItem, handles);
		if (handles.GetSize() > 0)
			CAutoCADCommands::ZoomExtentsByHandle(handles[0]);
		return;
	case TREE_LEVEL:
		hParentItem = m_TreeCtrl.GetParentItem(hItem);
		GetHandlesByBay(hParentItem, handles);
		if (handles.GetSize() > 0)
			CAutoCADCommands::ZoomExtentsByHandle(handles[0]);
		return;
	case TREE_LOCATION:
		hParentItem = m_TreeCtrl.GetParentItem(hItem);
		hParentItem = m_TreeCtrl.GetParentItem(hParentItem);
		GetHandlesByBay(hParentItem, handles);
		if (handles.GetSize() > 0)
			CAutoCADCommands::ZoomExtentsByHandle(handles[0]);
		return;
	}

	
	point1[X] -= 100;
	point1[Y] -= 100;
	point2[X] += 100;
	point2[Y] += 100;

	ads_command(RTSTR, "ZOOM", RTSTR, "W", RTPOINT, point1, RTPOINT, point2, RTNONE);
	

}

void CFacilityTools::GetSectionExtents(HTREEITEM hItem, ads_point &point1, ads_point &point2)
{
	CString sql;
	int dbid;
	CStringArray results, strings;
	qqhSLOTLocation location;
	BOOL firstTime = TRUE;

	dbid = m_TreeCtrl.GetItemData(hItem);

	if (dbid > 0) {
		sql.Format("select min(l.xcoordinate), min(l.ycoordinate), max(l.xcoordinate), max(l.ycoordinate) "
			"from dbaisle a, dbside si, dbbay b, dblevel le, dblocation l "
			"where a.dbsectionid = %d "
			"and a.dbaisleid = si.dbaisleid "
			"and si.dbsideid = b.dbsideid "
			"and b.dbbayid = le.dbbayid "
			"and le.dblevelid = l.dblevelid ", dbid);
		try {
			dataAccessService.ExecuteQuery("GetSectionExtents", sql, results);
		}
		catch (Ssa_Exception e) {
			utilityHelper.ProcessError("Error getting section extents.", &e);
			return;
		}
		catch (...) {
			utilityHelper.ProcessError("Error getting section extents.");
			return;
		}

		if (results.GetSize() == 0) {
			AfxMessageBox("Unable to get the extents of the section.");
			return;
		}

		utilityHelper.ParseString(results[0], "|", strings);
		point1[X] = atoi(strings[0]);
		point1[Y] = atoi(strings[1]);
		point2[X] = atoi(strings[2]);
		point2[Y] = atoi(strings[3]);
		firstTime = FALSE;

	}

	// Now check the elements in the tree to see if any new locations
	// expand the section
	TreeElement *sPtr, *aPtr, *siPtr, *bPtr, *lPtr, *loPtr;

	// Now get any that are in the btree; omitting duplicates - these would be new aisles
	for (int sIdx=0; sIdx < changesTree.treeChildren.GetSize(); ++sIdx) {
		sPtr = &(changesTree.treeChildren[sIdx]);

		if (sPtr->elementDBID == dbid || sPtr->fileOffset == (0-dbid)) {

			for (int aIdx=0; aIdx < sPtr->treeChildren.GetSize(); ++aIdx) {
				aPtr = &(sPtr->treeChildren[aIdx]);
				for (int siIdx=0; siIdx < aPtr->treeChildren.GetSize(); ++siIdx) {
					siPtr = &(aPtr->treeChildren[siIdx]);
					for (int bIdx=0; bIdx < siPtr->treeChildren.GetSize(); ++bIdx) {
						bPtr = &(siPtr->treeChildren[bIdx]);
						for (int lIdx=0; lIdx < bPtr->treeChildren.GetSize(); ++lIdx) {
							lPtr = &(bPtr->treeChildren[lIdx]);
							for (int loIdx=0; loIdx < lPtr->treeChildren.GetSize(); ++loIdx) {
								loPtr = &(lPtr->treeChildren[loIdx]);
								bTreeHelper.GetBtLocation(loPtr->fileOffset, location);
								int x = location.getCoord().getX();
								int y = location.getCoord().getY();
								if (firstTime) {
									point1[X] = point2[X] = x;
									point1[Y] = point2[Y] = y;
									firstTime = FALSE;
									continue;
								}
								
								if (x < point1[X])
									point1[X] = x;
								if (x > point2[X])
									point2[X] = x;
								if (y < point1[Y])
									point1[Y] = y;
								if (y > point2[Y])
									point2[Y] = y;

							}
						}
					}
				}
			}
		return;		// stop after this section
		}
	}



}

void CFacilityTools::GetAisleExtents(HTREEITEM hItem, ads_point &point1, ads_point &point2)
{
	CString sql;
	int dbid;
	CStringArray results, strings;
	qqhSLOTLocation location;
	BOOL firstTime = TRUE;

	dbid = m_TreeCtrl.GetItemData(hItem);

	if (dbid > 0) {
		sql.Format("select min(l.xcoordinate), min(l.ycoordinate), max(l.xcoordinate), max(l.ycoordinate) "
			"from dbside si, dbbay b, dblevel le, dblocation l "
			"where si.dbaisleid = %d "
			"and si.dbsideid = b.dbsideid "
			"and b.dbbayid = le.dbbayid "
			"and le.dblevelid = l.dblevelid ", dbid);
		try {
			dataAccessService.ExecuteQuery("GetAisleExtents", sql, results);
		}
		catch (Ssa_Exception e) {
			utilityHelper.ProcessError("Error getting aisle extents.", &e);
			return;
		}
		catch (...) {
			utilityHelper.ProcessError("Error getting aisle extents.");
			return;
		}

		if (results.GetSize() == 0) {
			AfxMessageBox("Unable to get the aisle of the section.");
			return;
		}

		utilityHelper.ParseString(results[0], "|", strings);
		point1[X] = atoi(strings[0]);
		point1[Y] = atoi(strings[1]);
		point2[X] = atoi(strings[2]);
		point2[Y] = atoi(strings[3]);
		firstTime = FALSE;

	}

	// Now check the elements in the tree to see if any new locations
	// expand the section
	TreeElement *sPtr, *aPtr, *siPtr, *bPtr, *lPtr, *loPtr;

	// Now get any that are in the btree; omitting duplicates - these would be new aisles
	for (int sIdx=0; sIdx < changesTree.treeChildren.GetSize(); ++sIdx) {
		sPtr = &(changesTree.treeChildren[sIdx]);
		for (int aIdx=0; aIdx < sPtr->treeChildren.GetSize(); ++aIdx) {
			aPtr = &(sPtr->treeChildren[aIdx]);
			if (aPtr->elementDBID == dbid || aPtr->fileOffset == (0-dbid)) {

				for (int siIdx=0; siIdx < aPtr->treeChildren.GetSize(); ++siIdx) {
					siPtr = &(aPtr->treeChildren[siIdx]);
					for (int bIdx=0; bIdx < siPtr->treeChildren.GetSize(); ++bIdx) {
						bPtr = &(siPtr->treeChildren[bIdx]);
						for (int lIdx=0; lIdx < bPtr->treeChildren.GetSize(); ++lIdx) {
							lPtr = &(bPtr->treeChildren[lIdx]);
							for (int loIdx=0; loIdx < lPtr->treeChildren.GetSize(); ++loIdx) {
								loPtr = &(lPtr->treeChildren[loIdx]);
								bTreeHelper.GetBtLocation(loPtr->fileOffset, location);
								int x = location.getCoord().getX();
								int y = location.getCoord().getY();
								if (firstTime) {
									point1[X] = point2[X] = x;
									point1[Y] = point2[Y] = y;
									firstTime = FALSE;
									continue;
								}
								if (x < point1[X])
									point1[X] = x;
								if (x > point2[X])
									point2[X] = x;
								if (y < point1[Y])
									point1[Y] = y;
								if (y > point2[Y])
									point2[Y] = y;

							}
						}
					}
				}
				return;
			}
		}
	}
}

void CFacilityTools::GetSideExtents(HTREEITEM hItem, ads_point &point1, ads_point &point2)
{
	CString sql;
	int dbid;
	CStringArray results, strings;
	qqhSLOTLocation location;
	BOOL firstTime = TRUE;

	dbid = m_TreeCtrl.GetItemData(hItem);

	if (dbid > 0) {
		sql.Format("select min(l.xcoordinate), min(l.ycoordinate), max(l.xcoordinate), max(l.ycoordinate) "
			"from dbbay b, dblevel le, dblocation l "
			"where b.dbsideid = %d "
			"and b.dbbayid = le.dbbayid "
			"and le.dblevelid = l.dblevelid ", dbid);
		try {
			dataAccessService.ExecuteQuery("GetSideExtents", sql, results);
		}
		catch (Ssa_Exception e) {
			utilityHelper.ProcessError("Error getting side extents.", &e);
			return;
		}
		catch (...) {
			utilityHelper.ProcessError("Error getting side extents.");
			return;
		}

		if (results.GetSize() == 0) {
			AfxMessageBox("Unable to get the extents of the side.");
			return;
		}

		utilityHelper.ParseString(results[0], "|", strings);
		point1[X] = atoi(strings[0]);
		point1[Y] = atoi(strings[1]);
		point2[X] = atoi(strings[2]);
		point2[Y] = atoi(strings[3]);
		firstTime = FALSE;

	}

	// Now check the elements in the tree to see if any new locations
	// expand the section
	TreeElement *sPtr, *aPtr, *siPtr, *bPtr, *lPtr, *loPtr;

	// Now get any that are in the btree; omitting duplicates - these would be new aisles
	for (int sIdx=0; sIdx < changesTree.treeChildren.GetSize(); ++sIdx) {
		sPtr = &(changesTree.treeChildren[sIdx]);
		for (int aIdx=0; aIdx < sPtr->treeChildren.GetSize(); ++aIdx) {
			aPtr = &(sPtr->treeChildren[aIdx]);
			for (int siIdx=0; siIdx < aPtr->treeChildren.GetSize(); ++siIdx) {
				siPtr = &(aPtr->treeChildren[siIdx]);
				if (siPtr->elementDBID == dbid || siPtr->fileOffset == (0-dbid)) {

					for (int bIdx=0; bIdx < siPtr->treeChildren.GetSize(); ++bIdx) {
						bPtr = &(siPtr->treeChildren[bIdx]);
						for (int lIdx=0; lIdx < bPtr->treeChildren.GetSize(); ++lIdx) {
							lPtr = &(bPtr->treeChildren[lIdx]);
							for (int loIdx=0; loIdx < lPtr->treeChildren.GetSize(); ++loIdx) {
								loPtr = &(lPtr->treeChildren[loIdx]);
								bTreeHelper.GetBtLocation(loPtr->fileOffset, location);
								int x = location.getCoord().getX();
								int y = location.getCoord().getY();
								if (firstTime) {
									point1[X] = point2[X] = x;
									point1[Y] = point2[Y] = y;
									firstTime = FALSE;
									continue;
								}
								if (x < point1[X])
									point1[X] = x;
								if (x > point2[X])
									point2[X] = x;
								if (y < point1[Y])
									point1[Y] = y;
								if (y > point2[Y])
									point2[Y] = y;
							}
						}
					}
					return;
				}
			}
		}
	}
}

void CFacilityTools::GetBayExtents(HTREEITEM hItem, ads_point &point1, ads_point &point2)
{
	CString sql;
	int dbid;
	CStringArray results, strings;
	qqhSLOTLocation location;
	BOOL firstTime = TRUE;

	dbid = m_TreeCtrl.GetItemData(hItem);

	if (dbid > 0) {
		sql.Format("select min(l.xcoordinate), min(l.ycoordinate), max(l.xcoordinate), max(l.ycoordinate) "
			"from dblevel le, dblocation l "
			"where le.dbbayid = %d "
			"and le.dblevelid = l.dblevelid ", dbid);
		try {
			dataAccessService.ExecuteQuery("GetBayExtents", sql, results);
		}
		catch (Ssa_Exception e) {
			utilityHelper.ProcessError("Error getting bay extents.", &e);
			return;
		}
		catch (...) {
			utilityHelper.ProcessError("Error getting bay extents.");
			return;
		}

		if (results.GetSize() == 0) {
			AfxMessageBox("Unable to get the extents of the bay.");
			return;
		}

		utilityHelper.ParseString(results[0], "|", strings);
		point1[X] = atoi(strings[0]);
		point1[Y] = atoi(strings[1]);
		point2[X] = atoi(strings[2]);
		point2[Y] = atoi(strings[3]);
		firstTime = FALSE;

	}

	// Now check the elements in the tree to see if any new locations
	// expand the section
	TreeElement *sPtr, *aPtr, *siPtr, *bPtr, *lPtr, *loPtr;

	// Now get any that are in the btree; omitting duplicates - these would be new aisles
	for (int sIdx=0; sIdx < changesTree.treeChildren.GetSize(); ++sIdx) {
		sPtr = &(changesTree.treeChildren[sIdx]);
		for (int aIdx=0; aIdx < sPtr->treeChildren.GetSize(); ++aIdx) {
			aPtr = &(sPtr->treeChildren[aIdx]);
			for (int siIdx=0; siIdx < aPtr->treeChildren.GetSize(); ++siIdx) {
				siPtr = &(aPtr->treeChildren[siIdx]);
				for (int bIdx=0; bIdx < siPtr->treeChildren.GetSize(); ++bIdx) {
					bPtr = &(siPtr->treeChildren[bIdx]);
					if (bPtr->elementDBID == dbid || bPtr->fileOffset == (0-dbid)) {

						for (int lIdx=0; lIdx < bPtr->treeChildren.GetSize(); ++lIdx) {
							lPtr = &(bPtr->treeChildren[lIdx]);
							for (int loIdx=0; loIdx < lPtr->treeChildren.GetSize(); ++loIdx) {
								loPtr = &(lPtr->treeChildren[loIdx]);
								bTreeHelper.GetBtLocation(loPtr->fileOffset, location);
								int x = location.getCoord().getX();
								int y = location.getCoord().getY();
								if (firstTime) {
									point1[X] = point2[X] = x;
									point1[Y] = point2[Y] = y;
									firstTime = FALSE;
									continue;
								}
								if (x < point1[X])
									point1[X] = x;
								if (x > point2[X])
									point2[X] = x;
								if (y < point1[Y])
									point1[Y] = y;
								if (y > point2[Y])
									point2[Y] = y;

							}
						}
						return;
					}
				}
			}
		}
	}
}



int CFacilityTools::DelSection(HTREEITEM hItem)
{
	TreeElement *ptr;
	CStringArray handles;
	int dbid = m_TreeCtrl.GetItemData(hItem);
	BOOL found = FALSE;

	if (dbid > 0) {
		ptr = changesTree.getSectionByDBID(dbid);
		if (ptr == NULL) {
			AfxMessageBox("Unable to find section.");
			return -1;
		}
	}
	else {
		for (int sIdx=0; sIdx < changesTree.treeChildren.GetSize(); ++sIdx) {
			ptr = &(changesTree.treeChildren[sIdx]);
			if (ptr->fileOffset == (0-dbid)) {
				found = TRUE;
				break;
			}
		}
		if (! found) {
			AfxMessageBox("Unable to find section.");
			return -1;
		}
	}


	GetHandlesBySection(hItem, handles, TRUE);
	try {
		qqhSLOTSection section;
		bTreeHelper.GetBtSection(ptr->fileOffset, section);

		if (dbid > 0) {		
			// Check to see if the facility is integrated - if it is and the locations have products
			// assigned to them, don't allow the delete
			if (facilityDataService.IntegratedSectionHasAssignments(dbid)) {
				CString temp;
				temp.Format("Section %s has product assignments.  In a facility that is integrated\n"
					"only sections with no product assignments may be deleted.\n"
					"To delete the section, set it to in-active, run Assign Products so that no\n"
					"products will be assigned to the section, and then delete it.", section.getDescription());
				AfxMessageBox(temp);
				return 0;
			}
			changesTree.DeletedSectionMap.SetAt(dbid, dbid);
		}

		for (int i=0; i < section.getHotSpotList().GetSize(); ++i) {
			if (strcmp(section.getHotSpotList()[i].getAcadHandle(), "XXX") != 0)
				handles.Add(section.getHotSpotList()[i].getAcadHandle());
		}

		changesTree.deleteBranch("SLOTSection", ptr);
		m_TreeCtrl.DeleteItem(hItem);
		numItemsProcessed++;
		for (i=0; i < handles.GetSize(); ++i)
			CAutoCADCommands::DeleteDrawingObjectByHandle(handles[i]);
		
	}
	catch (...) {
		AfxMessageBox("Error deleting section.");
		return -1;
	}

	CAutoCADCommands::Flush();

	return 0;
}

int CFacilityTools::DelAisle(HTREEITEM hItem)
{
	CStringArray handles;
	int i, dbid = m_TreeCtrl.GetItemData(hItem);
	TreeElement *sPtr, *aPtr;
	BOOL found = FALSE;
	int sIdx, aIdx;

	sIdx = aIdx = 0;

	for (i=0; i < 2; ++i) {
		
		for (; sIdx < changesTree.treeChildren.GetSize() && ! found; ++sIdx) {
			sPtr = &(changesTree.treeChildren[sIdx]);
			for (; aIdx < sPtr->treeChildren.GetSize() && ! found; ++aIdx) {
				aPtr = &(sPtr->treeChildren[aIdx]);
				if (aPtr->elementDBID == dbid || aPtr->fileOffset == (0 - dbid)) {
						found = TRUE;
				}
			}
		}
		
		
		// the aisle is not in the btree - so bring it in
		if (! found && i == 0) {

			if (dbid <= 0) {
				// we couldn't find the aisle in the btree but
				// this indicates its a new aisle so give up
				AfxMessageBox("Unable to find aisle.");
				return -1;
			}
			bTreeHelper.UpdateBTWithAisleForPickPath(dbid, changesTree, &aIdx, &sIdx);
		}

	}

	if (! found) {
		AfxMessageBox("Unable to find aisle.");
		return -1;
	}
	
	GetHandlesByAisle(hItem, handles, TRUE);
	try {
		qqhSLOTAisle aisle;
		bTreeHelper.GetBtAisle(aPtr->fileOffset, aisle);

		if (dbid > 0) {

			// Check to see if the facility is integrated - if it is and the locations have products
			// assigned to them, don't allow the delete
			if (facilityDataService.IntegratedAisleHasAssignments(dbid)) {
				CString temp;
				temp.Format("Aisle %s has product assignments.  In a facility that is integrated\n"
					"only aisles with no product assignments may be deleted.\n"
					"To delete the aisle, set it to in-active, run Assign Products so that no\n"
					"products will be assigned to the aisle, and then delete it.", aisle.getDescription());
				AfxMessageBox(temp);
				return 0;
			}
			changesTree.DeletedAisleMap.SetAt(dbid, dbid);
		}

		if (strcmpi(aPtr->acadHandle, "XXX") != 0) {

			// Delete any connection lines not owned by the current pick path
			TreeElement *sectionPtr = aPtr->treeParent;
			qqhSLOTSection section;
			bTreeHelper.GetBtSection(sectionPtr->fileOffset, section);

			elementMaintenanceHelper.DeletePickPathConnections(section, aisle);	
		}

		changesTree.deleteBranch("SLOTAisle", aPtr);
		m_TreeCtrl.DeleteItem(hItem);
		numItemsProcessed++;

		for (i=0; i < handles.GetSize(); ++i)
			CAutoCADCommands::DeleteDrawingObjectByHandle(handles[i]);

		CAutoCADCommands::Flush();
	}
	catch (...) {
		AfxMessageBox("Error deleting aisle.");
		return -1;
	}

	return 0;

}

int CFacilityTools::DelSide(HTREEITEM hItem)
{
	CStringArray handles;
	int i, aisledbid, dbid = m_TreeCtrl.GetItemData(hItem);
	TreeElement *sPtr, *aPtr, *siPtr;
	HTREEITEM hAisleItem;
	BOOL found = FALSE;
	int sIdx, aIdx;

	sIdx = aIdx = 0;

	for (i=0; i < 2; ++i) {
		
		for (; sIdx < changesTree.treeChildren.GetSize() && ! found; ++sIdx) {
			sPtr = &(changesTree.treeChildren[sIdx]);
			for (; aIdx < sPtr->treeChildren.GetSize() && ! found; ++aIdx) {
				aPtr = &(sPtr->treeChildren[aIdx]);
				for (int siIdx=0; siIdx < aPtr->treeChildren.GetSize() && ! found; ++siIdx) {
					siPtr = &(aPtr->treeChildren[siIdx]);
					if (siPtr->elementDBID == dbid || siPtr->fileOffset == (0 - dbid)) {
						found = TRUE;
					}
				}
			}
		}
		
		
		
		// the aisle is not in the btree - so bring it in
		if (! found && i == 0) {
			hAisleItem = m_TreeCtrl.GetParentItem(hItem);
			aisledbid = m_TreeCtrl.GetItemData(hAisleItem);
			if (aisledbid <= 0) {
				// we couldn't find the side in the btree but
				// this indicates its a new side so give up
				AfxMessageBox("Unable to find side.");
				return -1;
			}
			bTreeHelper.UpdateBTWithAisleForPickPath(aisledbid, changesTree, &aIdx, &sIdx);
		}

	}

	if (! found) {
		AfxMessageBox("Unable to find side.");
		return -1;
	}
	
	GetHandlesBySide(hItem, handles);

	try {
		if (dbid > 0) {

			qqhSLOTSide side;
			bTreeHelper.GetBtSide(siPtr->fileOffset, side);
			
			// Check to see if the facility is integrated - if it is and the locations have products
			// assigned to them, don't allow the delete
			if (facilityDataService.IntegratedSideHasAssignments(dbid)) {
				CString temp;
				temp.Format("The side has product assignments.  In a facility that is integrated\n"
					"only sides with no product assignments may be deleted.\n"
					"To delete the side, set it to in-active, run Assign Products so that no\n"
					"products will be assigned to the side, and then delete it.");
				AfxMessageBox(temp);
				return 0;
			}
			changesTree.DeletedSideMap.SetAt(dbid, dbid);
		}
		changesTree.deleteBranch("SLOTSide", siPtr);
		m_TreeCtrl.DeleteItem(hItem);
		
		numItemsProcessed++;
		
		for (i=0; i < handles.GetSize(); ++i)
			CAutoCADCommands::DeleteDrawingObjectByHandle(handles[i]);
		
		CAutoCADCommands::Flush();
	}
	catch (...) {
		AfxMessageBox("Error deleting side.");
		return -1;
	}

	
	return 0;

}

int CFacilityTools::DelBay(HTREEITEM hItem)
{
	CStringArray handles;
	int i, aisledbid, dbid = m_TreeCtrl.GetItemData(hItem);
	TreeElement *sPtr, *aPtr, *siPtr, *bPtr;
	HTREEITEM hAisleItem;
	BOOL found = FALSE;
	int sIdx, aIdx;

	sIdx = aIdx = 0;

	for (i=0; i < 2; ++i) {
		
		for (; sIdx < changesTree.treeChildren.GetSize() && ! found; ++sIdx) {
			sPtr = &(changesTree.treeChildren[sIdx]);
			for (; aIdx < sPtr->treeChildren.GetSize() && ! found; ++aIdx) {
				aPtr = &(sPtr->treeChildren[aIdx]);
				for (int siIdx=0; siIdx < aPtr->treeChildren.GetSize() && ! found; ++siIdx) {
					siPtr = &(aPtr->treeChildren[siIdx]);
					for (int bIdx=0; bIdx < siPtr->treeChildren.GetSize() && ! found; ++bIdx) {
						bPtr = &(siPtr->treeChildren[bIdx]);
						if (bPtr->elementDBID == dbid || bPtr->fileOffset == (0 - dbid)) {
							found = TRUE;
						}
					}
				}
			}
		}
		
		
		
		// the aisle is not in the btree - so bring it in
		if (! found && i == 0) {
			hAisleItem = m_TreeCtrl.GetParentItem(m_TreeCtrl.GetParentItem(hItem));
			aisledbid = m_TreeCtrl.GetItemData(hAisleItem);
			if (aisledbid <= 0) {
				// we couldn't find the side in the btree but
				// this indicates its a new side so give up
				AfxMessageBox("Unable to find bay.");
				return -1;
			}
			bTreeHelper.UpdateBTWithAisleForPickPath(aisledbid, changesTree, &aIdx, &sIdx);
		}

	}

	if (! found) {
		AfxMessageBox("Unable to find bay.");
		return -1;
	}
	
	GetHandlesByBay(hItem, handles);
	try {
		if (dbid > 0) {
			qqhSLOTBay bay;
			bTreeHelper.GetBtBay(bPtr->fileOffset, bay);
			
			if (facilityDataService.IntegratedBayHasAssignments(dbid)) {
				CString temp;
				temp.Format("Bay %s has product assignments.  In a facility that is integrated\n"
					"only aisles with no product assignments may be deleted.\n"
					"To delete the bay, set it to in-active, run Assign Products so that no\n"
					"products will be assigned to the bay, and then delete it.", bay.getDescription());
				AfxMessageBox(temp);
				return 0;
			}
		}

			changesTree.DeletedBayMap.SetAt(dbid, dbid);

		changesTree.deleteBranch("SLOTBay", bPtr);
		m_TreeCtrl.DeleteItem(hItem);
		
		numItemsProcessed++;

		for (i=0; i < handles.GetSize(); ++i)
			CAutoCADCommands::DeleteDrawingObjectByHandle(handles[i]);
		
		CAutoCADCommands::Flush();
	
	}
	catch (...) {
		AfxMessageBox("Error deleting bay.");
	}

	return 0;
}

int CFacilityTools::DelLevel(HTREEITEM hItem)
{
	;
	UNREFERENCED_PARAMETER(hItem);

	AfxMessageBox("It is not possible to delete an individual level.  You must delete the entire bay.");
	return -1;
}

int CFacilityTools::DelLocation(HTREEITEM hItem)
{
	UNREFERENCED_PARAMETER(hItem);

	AfxMessageBox("It is not possible to delete an individual location. You must delete the entire bay.");
	return -1;

}

void CFacilityTools::OnCancel() 
{
	CProcessing *pDlg;

	// If they deleted an element, the database is already updated so
	// force a save to update the drawing
	if (m_SaveNeeded && changesTree.elementDBID > 0) {
		pDlg = new CProcessing();
		pDlg->m_StatusText = "Saving changes...";
		pDlg->Create(IDD_PROCESSING, this);
		pDlg->UpdateData(FALSE);
		pDlg->CenterWindow();
		pDlg->ShowWindow(SW_SHOW);
		pDlg->UpdateWindow();

		facilityHelper.QuickSave();

		pDlg->DestroyWindow();

	}
	
	CDialog::OnCancel();
}

void CFacilityTools::OnDblclkFacilityTree(NMHDR* pNMHDR, LRESULT* pResult) 
{
	UNREFERENCED_PARAMETER(pNMHDR);

	OnProperties();
	
	*pResult = 0;
}



// ChooseAisleProfileDialog.cpp : implementation file
//

#include "stdafx.h"
#include "modal.h"
#include "ChooseAisleProfileDialog.h"
#include "HelpService.h"
#include "AisleProfileDataService.h"

#include <process.h>

#ifdef _DEBUG
#define new DEBUG_NEW
#undef THIS_FILE
static char THIS_FILE[] = __FILE__;
#endif

extern CHelpService helpService;
extern char slotDir[256];


/////////////////////////////////////////////////////////////////////////////
// CChooseAisleProfileDialog dialog


CChooseAisleProfileDialog::CChooseAisleProfileDialog(CWnd* pParent /*=NULL*/)
	: CDialog(CChooseAisleProfileDialog::IDD, pParent)
{
	//{{AFX_DATA_INIT(CChooseAisleProfileDialog)
	m_ChooseAisle_SlotDir = _T("");
	m_ChooseAisle_AisleName = _T("");
	m_ChooseAisle_SectionVal = -1;
	m_ChooseAisle_NumAisles = 0;
	//}}AFX_DATA_INIT
}


void CChooseAisleProfileDialog::DoDataExchange(CDataExchange* pDX)
{
	CDialog::DoDataExchange(pDX);
	//{{AFX_DATA_MAP(CChooseAisleProfileDialog)
	DDX_Control(pDX, IDC_CHOOSEAISLE_NUMAISLES, m_ChooseAisle_NumAislesBox);
	DDX_Control(pDX, IDC_CHOOSEAISLE_SECTIONCHOICE, m_ChooseAisle_SectionChoice);
	DDX_Control(pDX, IDC_CHOOSEAISLE_TREE, m_ChooseAisle_Tree);
	DDX_Text(pDX, IDC_CHOOSEAISLE_SLOTDIR, m_ChooseAisle_SlotDir);
	DDX_Text(pDX, IDC_CHOOSEAISLE_AISLENAME, m_ChooseAisle_AisleName);
	DDX_CBIndex(pDX, IDC_CHOOSEAISLE_SECTIONCHOICE, m_ChooseAisle_SectionVal);
	DDX_Text(pDX, IDC_CHOOSEAISLE_NUMAISLES, m_ChooseAisle_NumAisles);
	//}}AFX_DATA_MAP
}


BEGIN_MESSAGE_MAP(CChooseAisleProfileDialog, CDialog)
	//{{AFX_MSG_MAP(CChooseAisleProfileDialog)
	ON_BN_CLICKED(IDHELP, OnHelp)
	ON_NOTIFY(NM_DBLCLK, IDC_CHOOSEAISLE_TREE, OnDblclkChooseAisleTree)
	ON_WM_HELPINFO()
	//}}AFX_MSG_MAP
END_MESSAGE_MAP()

/////////////////////////////////////////////////////////////////////////////
// CChooseAisleProfileDialog message handlers


void CChooseAisleProfileDialog::OnOK() 
{
	UpdateData(TRUE);
	UpdateData(TRUE);
	HTREEITEM hItem;
	hItem = m_ChooseAisle_Tree.GetSelectedItem();
	m_ChooseAisle_AisleName = m_ChooseAisle_Tree.GetItemText(hItem);

	if ( m_ChooseAisle_AisleName == "" ) {
		AfxMessageBox("Please choose an aisle profile.");
		return;
	}
	if ( m_ChooseAisle_SectionVal < 0 ) {
		AfxMessageBox("Please choose a section, or 'New Section'");
		return;
	}

	if ( m_ChooseAisle_NumAisles < 0 ) {
		AfxMessageBox("Number must be non-negative.");
		m_ChooseAisle_NumAislesBox.SetFocus();
		return;
	}

	EndDialog(IDOK);
}

void CChooseAisleProfileDialog::OnHelp() 
{
	helpService.ShowScreenHelp(IDD);

	return;
}

void CChooseAisleProfileDialog::OnDblclkChooseAisleTree(NMHDR* pNMHDR, LRESULT* pResult) 
{
	UNREFERENCED_PARAMETER(pNMHDR);

	*pResult = 0;
	UpdateData(TRUE);
	HTREEITEM hItem;
	hItem = m_ChooseAisle_Tree.GetSelectedItem();
	m_ChooseAisle_AisleName = m_ChooseAisle_Tree.GetItemText(hItem);

	if ( m_ChooseAisle_SectionVal < 0 ) {
		AfxMessageBox("Please choose a section, or 'New Section'");
		return;
	}

	EndDialog(IDOK);
}

BOOL CChooseAisleProfileDialog::OnInitDialog() 
{
	CWinApp * currentApp;
	HICON treeIcon;
	int resImage;
	TV_INSERTSTRUCT TreeCtrlItem;
	CFileFind findFile, findDir;
	CString strDir = m_ChooseAisle_SlotDir;
	CSsaStringArray dirList;
	CString str;
	CString tmpDirName;
	CString strDirName;
	int i;
	
	CDialog::OnInitDialog();
	
	m_ImageList.Create(::GetSystemMetrics(SM_CXICON),
		::GetSystemMetrics(SM_CYICON), TRUE, 20, 3);
	currentApp = AfxGetApp();
	treeIcon = currentApp->LoadIcon(IDI_AISLEICON);
	if ( treeIcon == NULL ) {
		;
	}
	else {
		resImage = m_ImageList.Add(treeIcon);
		::DeleteObject(treeIcon);
	}
	m_ChooseAisle_Tree.SetImageList(&m_ImageList, TVSIL_NORMAL);
	
	TreeCtrlItem.hParent = TVI_ROOT;
	TreeCtrlItem.hInsertAfter = TVI_LAST;
	TreeCtrlItem.item.mask = TVIF_TEXT | TVIF_PARAM;
	TreeCtrlItem.item.pszText = "Aisles";
	TreeCtrlItem.item.lParam = 0;
	TreeCtrlItem.item.iImage = 0;
	TreeCtrlItem.item.iSelectedImage = 0;

	currentApp = AfxGetApp();
	currentApp->DoWaitCursor(1);

	m_AisleProfileNameList.RemoveAll();
	CAisleProfileDataService apdService;

	apdService.GetAisleProfileNameList(m_AisleProfileNameList);

	for (i = 0; i < m_AisleProfileNameList.GetSize(); i++) {

		TreeCtrlItem.hParent = TVI_ROOT;
		TreeCtrlItem.item.pszText = m_AisleProfileNameList[i].GetBuffer(0);
		m_AisleProfileNameList[i].ReleaseBuffer();
		TreeCtrlItem.item.lParam = 1;
		if (i == 0) {
			TreeCtrlItem.item.state |= TVIS_SELECTED ;
			TreeCtrlItem.item.stateMask |= TVIS_SELECTED;
		}
		m_ChooseAisle_Tree.InsertItem(&TreeCtrlItem);
	}
	

	m_ChooseAisle_NumAisles = 1;

	//UpdateData(FALSE);

	m_ChooseAisle_SectionVal = 0;

	for ( i = 0; i < m_SectionNameList.GetSize(); i++ )
		m_ChooseAisle_SectionChoice.AddString(m_SectionNameList[i]);

	CRect r;
	m_ChooseAisle_SectionChoice.GetWindowRect(&r);
	m_ChooseAisle_SectionChoice.SetWindowPos(NULL, 0, 0, r.Width(), r.Height()*(m_SectionNameList.GetSize()+1),
		SWP_NOMOVE|SWP_NOZORDER);

	m_ChooseAisle_SectionVal = 0;
	m_ChooseAisle_Tree.SetFocus();
	//m_ChooseAisle_AisleName = GetAisleName();

	UpdateData(FALSE);

	currentApp->DoWaitCursor(-1);

	return TRUE;  // return TRUE unless you set the focus to a control
	              // EXCEPTION: OCX Property Pages should return FALSE
}

CString CChooseAisleProfileDialog::GetAisleName()
{
	HTREEITEM selectedItem;

	selectedItem = m_ChooseAisle_Tree.GetSelectedItem();
	if ( selectedItem == NULL )
		return CString("");
	
	return m_ChooseAisle_Tree.GetItemText(selectedItem);
	/*
	tempItem.hItem = selectedItem;
	tempItem.pszText = itemBuffer;
	tempItem.cchTextMax = 256;
	tempItem.mask = TVIF_TEXT | TVIF_PARAM;
	if ( m_ChooseAisle_Tree.GetItem(&tempItem) == 0 )
		AfxMessageBox("Item Retrieval Failed");	
	return CString(itemBuffer);
	*/
}

BOOL CChooseAisleProfileDialog::OnHelpInfo(HELPINFO* pHelpInfo) 
{
	helpService.ShowFieldHelp(IDD, pHelpInfo->iCtrlId);

	return FALSE;
}





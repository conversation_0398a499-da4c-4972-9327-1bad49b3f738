// Processing.cpp : implementation file
//

#include "stdafx.h"
#include "modal.h"
#include "Processing.h"
#include  <dbsymtb.h>
#ifdef _DEBUG
#define new DEBUG_NEW
#undef THIS_FILE
static char THIS_FILE[] = __FILE__;
#endif

#include <adscodes.h>

/////////////////////////////////////////////////////////////////////////////
// CProcessing dialog


CProcessing::CProcessing(CWnd* pParent /*=NULL*/)
	: CDialog(CProcessing::IDD, pParent)
{
	//{{AFX_DATA_INIT(CProcessing)
	m_StatusText = _T("");
	//}}AFX_DATA_INIT
}


void CProcessing::DoDataExchange(CDataExchange* pDX)
{
	CDialog::DoDataExchange(pDX);
	//{{AFX_DATA_MAP(CProcessing)
	DDX_Text(pDX, IDC_STATUS_TEXT, m_StatusText);
	//}}AFX_DATA_MAP
}


BEGIN_MESSAGE_MAP(CProcessing, CDialog)
	//{{AFX_MSG_MAP(CProcessing)
	ON_MESSAGE(WM_ACAD_KEEPFOCUS, OnAcadKeepFocus)
	//}}AFX_MSG_MAP
END_MESSAGE_MAP()

/////////////////////////////////////////////////////////////////////////////
// CProcessing message handlers

void CProcessing::PostNcDestroy() 
{
//	ads_printf("Before CProcessing delete\n");
	delete this;
//	ads_printf("After CProcessing delete\n");
	
	//CDialog::PostNcDestroy();
}


afx_msg LONG CProcessing::OnAcadKeepFocus(UINT, LONG)
{

	return TRUE;
}

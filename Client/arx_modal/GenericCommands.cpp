// GenericCommands.cpp: implementation of the CGenericCommands class.
//
//////////////////////////////////////////////////////////////////////

#include "stdafx.h"
#include "modal.h"
#include "GenericCommands.h"
#include "InterfaceHelper.h"

#ifdef _DEBUG
#undef THIS_FILE
static char THIS_FILE[]=__FILE__;
#define new DEBUG_NEW
#endif

//////////////////////////////////////////////////////////////////////
// Construction/Destruction
//////////////////////////////////////////////////////////////////////

CGenericCommands::CGenericCommands()
{

}

CGenericCommands::~CGenericCommands()
{

}

void CGenericCommands::RegisterCommands()
{
	acedRegCmds->addCommand("SLOTGEN", "INTERFACEMAP", "INTERFACEMAP",
		ACRX_CMD_MODAL, &CGenericCommands::InterfaceMap);
}

void CGenericCommands::InterfaceMap()
{
	CInterfaceHelper interfaceHelper;

	interfaceHelper.InterfaceMap();
}
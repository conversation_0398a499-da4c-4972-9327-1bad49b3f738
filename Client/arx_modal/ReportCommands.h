// ReportCommands.h: interface for the CReportCommands class.
//
//////////////////////////////////////////////////////////////////////

#if !defined(AFX_REPORTCOMMANDS_H__4C1D102A_646B_404C_B853_048D8F3076B6__INCLUDED_)
#define AFX_REPORTCOMMANDS_H__4C1D102A_646B_404C_B853_048D8F3076B6__INCLUDED_

#if _MSC_VER > 1000
#pragma once
#endif // _MSC_VER > 1000

#include "Commands.h"

class CReportCommands : public CCommands  
{
public:
	CReportCommands();
	virtual ~CReportCommands();

	static void RegisterCommands();
	static void OpenSavedRep();
	static void RackAssignmentRep();
	static void RackAssignmentDetailRep();
	static void ProductGroupDefineRep();
	static void ProductGroupLayoutRep();
	static void ProductGroupFacingsRep();
	static void ProductsLayoutAssignmentRep();
	static void ProductsLayoutVarWidthLocRep();
	static void ProductsLayoutCaseReOrientRep();
	static void FacilityMoveChainsRep();
	static void LocationOutboundRep();
	static void AssignmentOutboundRep();
	static void ProductDetailRep();
	static void CostAnalysisDetRep();
	static void CostAnalysisSumRep();
	static void ProductsLayoutAssignmentByProductRep();
	static void ProductsLayoutAssignmentByLocationRep();
	static void ProductGroupDefineByMovementRep();
	static void ProductGroupDefineByBOHRep();
	static void ProductGroupDefineByUOIRep();
	static void RackUsageSummaryRep();
	static void UnassignedProductsRep();
	static void CapitalCostRejectionRep();

};

#endif // !defined(AFX_REPORTCOMMANDS_H__4C1D102A_646B_404C_B853_048D8F3076B6__INCLUDED_)

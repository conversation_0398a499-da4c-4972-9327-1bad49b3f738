#if !defined(AFX_SIDEPROFILESHEET_H__A1FC82E3_DBE5_41BC_AC52_F0A518AA8F70__INCLUDED_)
#define AFX_SIDEPROFILESHEET_H__A1FC82E3_DBE5_41BC_AC52_F0A518AA8F70__INCLUDED_

#include "SideProfile.h"	// Added by ClassView
#include "SideProfileAttributesPage.h"
#include "SideProfileBayPage.h"

#if _MSC_VER > 1000
#pragma once
#endif // _MSC_VER > 1000
// SideProfileSheet.h : header file
//

/////////////////////////////////////////////////////////////////////////////
// CSideProfileSheet

class CSideProfileSheet : public CPropertySheet
{
	DECLARE_DYNAMIC(CSideProfileSheet)

// Construction
public:
	CSideProfileSheet(UINT nIDCaption, CWnd* pParentWnd = NULL, UINT iSelectPage = 0);
	CSideProfileSheet(LPCTSTR pszCaption, CWnd* pParentWnd = NULL, UINT iSelectPage = 0);

// Attributes
public:

// Operations
public:

// Overrides
	// ClassWizard generated virtual function overrides
	//{{AFX_VIRTUAL(CSideProfileSheet)
	protected:
	virtual BOOL OnNotify(WPARAM wParam, LPARAM lParam, LRESULT* pResult);
	//}}AFX_VIRTUAL

// Implementation
public:
	CSideProfileAttributesPage m_AttributesPage;
	CSideProfileBayPage m_BayPage;
	CSideProfile *m_pSideProfile;
	virtual ~CSideProfileSheet();

	// Generated message map functions
protected:
	//{{AFX_MSG(CSideProfileSheet)
	afx_msg BOOL OnHelpInfo(HELPINFO* pHelpInfo);
	//}}AFX_MSG
	DECLARE_MESSAGE_MAP()
};

/////////////////////////////////////////////////////////////////////////////

//{{AFX_INSERT_LOCATION}}
// Microsoft Visual C++ will insert additional declarations immediately before the previous line.

#endif // !defined(AFX_SIDEPROFILESHEET_H__A1FC82E3_DBE5_41BC_AC52_F0A518AA8F70__INCLUDED_)

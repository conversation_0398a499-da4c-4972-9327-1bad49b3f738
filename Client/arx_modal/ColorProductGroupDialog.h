#if !defined(AFX_COLORPRODUCTGROUPDIALOG_H__AB3338B0_E3CE_41C8_AD17_81477DF7CDCF__INCLUDED_)
#define AFX_COLORPRODUCTGROUPDIALOG_H__AB3338B0_E3CE_41C8_AD17_81477DF7CDCF__INCLUDED_

#include "Resource.h"
#include "ColorListBox.h"
#include "ProductGroup.h"
#include "SortListBox.h"

#if _MSC_VER > 1000
#pragma once
#endif // _MSC_VER > 1000
// ColorProductGroupDialog.h : header file
//

/////////////////////////////////////////////////////////////////////////////
// CColorProductGroupDialog dialog

#include "ColorListBox.h"

class CColorProductGroupDialog : public CDialog
{
// Construction
public:
	CColorProductGroupDialog(CWnd* pParent = NULL);   // standard constructor
	~CColorProductGroupDialog();
	static int CompareGroups(void *p1, void *p2);
	static CString GetGroupText(void *pItemData);
// Dialog Data
	//{{AFX_DATA(CColorProductGroupDialog)
	enum { IDD = IDD_COLOR_PRODUCT_GROUPS };
	CSortListBox	m_GroupListCtrl;
	CColorListBox	m_ColorListCtrl;
	//}}AFX_DATA


// Overrides
	// ClassWizard generated virtual function overrides
	//{{AFX_VIRTUAL(CColorProductGroupDialog)
	protected:
	virtual void DoDataExchange(CDataExchange* pDX);    // DDX/DDV support
	//}}AFX_VIRTUAL

// Implementation
protected:

	// Generated message map functions
	//{{AFX_MSG(CColorProductGroupDialog)
	virtual BOOL OnInitDialog();
	afx_msg BOOL OnHelpInfo(HELPINFO* pHelpInfo);
	afx_msg void OnHelp();
	virtual void OnOK();
	afx_msg void OnAssign();
	afx_msg void OnRemove();
	afx_msg void OnBegindragProductGroupList(NMHDR* pNMHDR, LRESULT* pResult);
	afx_msg void OnNewColor();
	afx_msg void OnAssignAll();
	afx_msg void OnRemoveAll();
	afx_msg void OnDeleteColor();
	afx_msg void OnApply();
	//}}AFX_MSG
	DECLARE_MESSAGE_MAP()
private:
	void SaveSettings();
	int ColorGroups();
	int LoadGroups();
	int LoadColors();
	void RedrawColorItem(int nItem);
	void AssignColor(int pGroupIdx, int pColorIdx);
	void RemoveColor(int pColorIdx);
	CTypedPtrArray<CObArray, CProductGroup*> m_ProductGroupList;
};

//{{AFX_INSERT_LOCATION}}
// Microsoft Visual C++ will insert additional declarations immediately before the previous line.

#endif // !defined(AFX_COLORPRODUCTGROUPDIALOG_H__AB3338B0_E3CE_41C8_AD17_81477DF7CDCF__INCLUDED_)

#if !defined(AFX_EDITGRID_H__0A076CAE_B00F_11D4_9218_00400542E36B__INCLUDED_)
#define AFX_EDITGRID_H__0A076CAE_B00F_11D4_9218_00400542E36B__INCLUDED_

#if _MSC_VER > 1000
#pragma once
#endif // _MSC_VER > 1000
// EditGrid.h : header file
//

#include "msflexgrid.h"
#include "EditWnd.h"
/////////////////////////////////////////////////////////////////////////////
// CEditGrid window

const int RIGHT_ALIGNMENT = 0;
const int LEFT_ALIGNMENT = 1;

class CEditGrid : public CMSFlexGrid
{
// Construction
public:
	CEditGrid();

// Attributes
public:
	CEditWnd *m_edit;
	CEditWnd m_leftEdit;
	CEditWnd m_rightEdit;
	long m_lBorderWidth;
	long m_lBorderHeight;
	int m_nLogX;
	int m_nLogY;

// Operations
public:

// Overrides
	// ClassWizard generated virtual function overrides
	//{{AFX_VIRTUAL(CEditGrid)
	protected:
	virtual void PreSubclassWindow();
	//}}AFX_VIRTUAL

// Implementation
public:
	int m_SortColumn;
	void ShowHelp();
	CString m_HelpTopic;
	CEditGrid(int pAlignment, BOOL pAllowInsertDelete);
	void ResetAlignment(int pAlignment);
	int m_Alignment;
	void ProcessTab();
	BOOL m_AllowInsertDelete;
	virtual ~CEditGrid();
	afx_msg void OnUpdateGrid(BOOL bLeaving);
	void ProcessInsert();
	void ProcessDelete();

	// Generated message map functions
protected:
	afx_msg void OnKeyPressGrid(short FAR* KeyAscii);
	void CEditGrid::OnMouseDown(short Button, short shift, OLE_XPOS_PIXELS x, OLE_YPOS_PIXELS y);
	afx_msg void OnDblClickGrid();
	DECLARE_EVENTSINK_MAP()

	//{{AFX_MSG(CEditGrid)
	afx_msg void OnSetFocus(CWnd* pOldWnd);
	afx_msg UINT OnGetDlgCode();
	afx_msg void OnShowWindow(BOOL bShow, UINT nStatus);
	afx_msg BOOL OnHelpInfo(HELPINFO* pHelpInfo);
	//}}AFX_MSG
	DECLARE_MESSAGE_MAP()
	


private:
	CFont font;
};

/////////////////////////////////////////////////////////////////////////////

//{{AFX_INSERT_LOCATION}}
// Microsoft Visual C++ will insert additional declarations immediately before the previous line.

#endif // !defined(AFX_EDITGRID_H__0A076CAE_B00F_11D4_9218_00400542E36B__INCLUDED_)

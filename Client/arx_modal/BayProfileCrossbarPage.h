#if !defined(AFX_BAYPROFILECROSSBARPAGE_H__22DA712E_B4BC_42E5_8BF0_EE3BCEDCDE96__INCLUDED_)
#define AFX_BAYPROFILECROSSBARPAGE_H__22DA712E_B4BC_42E5_8BF0_EE3BCEDCDE96__INCLUDED_

#if _MSC_VER > 1000
#pragma once
#endif // _MSC_VER > 1000
// BayProfileCrossbarPage.h : header file
//
#include "BayProfileCrossbarButton.h"
#include "BayProfile.h"
/////////////////////////////////////////////////////////////////////////////
// CBayProfileCrossbarPage dialog

class CBayProfileCrossbarPage : public CPropertyPage
{
	DECLARE_DYNCREATE(CBayProfileCrossbarPage)

// Construction
public:
	void UpdateToolTip(int x, int y, const CString &msg);
	CBayProfileCrossbarPage();
	~CBayProfileCrossbarPage();

// Dialog Data
	//{{AFX_DATA(CBayProfileCrossbarPage)
	enum { IDD = IDD_BAY_PROFILE_CROSSBAR_ATTRIBUTES };
	CComboBox	m_LevelListCtrl;
	CBayProfileCrossbarButton m_CrossbarButton;
	CString	m_Position;
	CString	m_Hidden;
	CString	m_Thickness;
	CString	m_WeightCapacity;
	CString	m_Clearance;
	CString	m_Overhang;
	//}}AFX_DATA


// Overrides
	// ClassWizard generate virtual function overrides
	//{{AFX_VIRTUAL(CBayProfileCrossbarPage)
	public:
	virtual BOOL OnSetActive();
	virtual BOOL OnKillActive();
	virtual BOOL PreTranslateMessage(MSG* pMsg);
	protected:
	virtual void DoDataExchange(CDataExchange* pDX);    // DDX/DDV support
	virtual BOOL OnNotify(WPARAM wParam, LPARAM lParam, LRESULT* pResult);
	//}}AFX_VIRTUAL

// Implementation
protected:
	// Generated message map functions
	//{{AFX_MSG(CBayProfileCrossbarPage)
	virtual BOOL OnInitDialog();
	afx_msg void OnContextMenu(CWnd* pWnd, CPoint point);
	afx_msg void OnMenuAdd();
	afx_msg void OnEdit();
	afx_msg void OnDeleteCrossbar();
	afx_msg void OnSelchangeLevelList();
	afx_msg void OnAddButton();
	afx_msg BOOL OnHelpInfo(HELPINFO* pHelpInfo);
	//}}AFX_MSG
	DECLARE_MESSAGE_MAP()
	afx_msg LRESULT OnSelectLevel(WPARAM wParam, LPARAM lParam);
	afx_msg LRESULT OnAddCrossbar(WPARAM wParam, LPARAM lParam);
	afx_msg LRESULT OnView(WPARAM wParam, LPARAM lParam);
	afx_msg void OnAdd(double height = 0, double thickness = 0);

	CToolTipCtrl m_ToolTip;
	CString m_ToolTipText;

private:
	void RebuildLevelList();
	int GetCurSel();
	BOOL Validate();
	int UpdateScreenFromLevelProfile(int currentLevel);
	int GetLevelByHeight(double height);
	CBayProfile *m_pBayProfile;
};

//{{AFX_INSERT_LOCATION}}
// Microsoft Visual C++ will insert additional declarations immediately before the previous line.

#endif // !defined(AFX_BAYPROFILECROSSBARPAGE_H__22DA712E_B4BC_42E5_8BF0_EE3BCEDCDE96__INCLUDED_)

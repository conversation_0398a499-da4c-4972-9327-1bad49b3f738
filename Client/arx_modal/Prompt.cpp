// Prompt.cpp : implementation file
//

#include "stdafx.h"
#include "modal.h"
#include "Prompt.h"

#ifdef _DEBUG
#define new DEBUG_NEW
#undef THIS_FILE
static char THIS_FILE[] = __FILE__;
#endif

/////////////////////////////////////////////////////////////////////////////
// CPrompt dialog


CPrompt::CPrompt(CWnd* pParent /*=NULL*/)
	: CDialog(CPrompt::IDD, pParent)
{
	//{{AFX_DATA_INIT(CPrompt)
	m_ParameterName = _T("");
	m_ParameterValue = _T("");
	//}}AFX_DATA_INIT
}


void CPrompt::DoDataExchange(CDataExchange* pDX)
{
	CDialog::DoDataExchange(pDX);
	//{{AFX_DATA_MAP(CPrompt)
	DDX_Text(pDX, IDC_PARAMETER_NAME, m_ParameterName);
	DDX_Text(pDX, IDC_PARAMETER_VALUE, m_ParameterValue);
	//}}AFX_DATA_MAP
}


BEGIN_MESSAGE_MAP(CPrompt, CDialog)
	//{{AFX_MSG_MAP(CPrompt)
	//}}AFX_MSG_MAP
END_MESSAGE_MAP()

/////////////////////////////////////////////////////////////////////////////
// CPrompt message handlers

void CPrompt::OnOK() 
{
	CEdit *pValue = (CEdit *)GetDlgItem(IDC_PARAMETER_VALUE);
	CString value;

	UpdateData(TRUE);

	pValue->GetWindowText(value);
	if (value == "") {
		AfxMessageBox("Please enter a value for the parameter.");
		return;
	}

	CDialog::OnOK();
}

BOOL CPrompt::OnInitDialog() 
{
	CDialog::OnInitDialog();
	
	this->SetWindowText(m_Title);

	CStatic *pName = (CStatic *)GetDlgItem(IDC_PARAMETER_NAME);
	CEdit *pValue = (CEdit *)GetDlgItem(IDC_PARAMETER_VALUE);	
	
	//pName->SetWindowText(m_ParameterName);
	UpdateData(FALSE);

	return TRUE;  // return TRUE unless you set the focus to a control
	              // EXCEPTION: OCX Property Pages should return FALSE
}

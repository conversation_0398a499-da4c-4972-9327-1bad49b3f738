//{{AFX_INCLUDES()
#include "DataGrid.h"
//}}AFX_INCLUDES
#if !defined(AFX_PRODUCTGROUPCRITERIAMATRIX_H__0DBB56D1_077B_11D5_9EC8_00C04FAC149C__INCLUDED_)
#define AFX_PRODUCTGROUPCRITERIAMATRIX_H__0DBB56D1_077B_11D5_9EC8_00C04FAC149C__INCLUDED_

#if _MSC_VER > 1000
#pragma once
#endif // _MSC_VER > 1000
// ProductGroupCriteriaMatrix.h : header file
//
#include "Resource.h"
#include "ProductGroupDataService.h"


/////////////////////////////////////////////////////////////////////////////
// CProductGroupCriteriaMatrix dialog

class CProductGroupCriteriaMatrix : public CDialog
{
// Construction
	DECLARE_DYNCREATE(CProductGroupCriteriaMatrix)
public:
	int FindCriteria(int criteriaDBID);
	int FindAttribute(int criteriaDBID);
	BOOL ValidateClose();
	CStringArray m_InternalValues;
	CStringArray m_InternalOperators;
	CProductGroupCriteriaMatrix(CWnd* pParent = NULL);   // standard constructor
	~CProductGroupCriteriaMatrix();
	CTypedPtrArray<CObArray, CProductGroupCriteria*> *m_CriteriaList;
	CProductGroupDataService *m_ProductGroupDataService;
	static void OnDoubleClickGrid(void *parent);

// Dialog Data
	//{{AFX_DATA(CProductGroupCriteriaMatrix)
	enum { IDD = IDD_PRODUCT_GROUP_CRITERIA };
	CButton	m_ExcelBtn;
	CDataGrid	m_Grid;
	//}}AFX_DATA


// Overrides
	// ClassWizard generated virtual function overrides
	//{{AFX_VIRTUAL(CProductGroupCriteriaMatrix)
	public:
	virtual BOOL Create(LPCTSTR lpszClassName, LPCTSTR lpszWindowName, DWORD dwStyle, const RECT& rect, CWnd* pParentWnd, UINT nID, CCreateContext* pContext = NULL);
	protected:
	virtual void DoDataExchange(CDataExchange* pDX);    // DDX/DDV support
	virtual void PostNcDestroy();
	//}}AFX_VIRTUAL

// Implementation
protected:
	CTypedPtrArray<CObArray, CProductGroup*> *m_ProductGroupList;
	CTypedPtrArray<CObArray, CProductAttribute*> *m_ProductAttributeList;
	CTypedPtrArray<CObArray, CProductGroup*> m_ProductGroupListOld;

	// Generated message map functions
	//{{AFX_MSG(CProductGroupCriteriaMatrix)
	afx_msg void OnApply();
	afx_msg void OnHelp();
	virtual BOOL OnInitDialog();
	afx_msg BOOL OnHelpInfo(HELPINFO* pHelpInfo);
	afx_msg void OnExcel();
	//}}AFX_MSG
	DECLARE_MESSAGE_MAP()
private:
	int Initialize();
	int LoadGrid();
	
	CMapStringToOb m_ProductAttributeMap;
};

//{{AFX_INSERT_LOCATION}}
// Microsoft Visual C++ will insert additional declarations immediately before the previous line.

#endif // !defined(AFX_PRODUCTGROUPCRITERIAMATRIX_H__0DBB56D1_077B_11D5_9EC8_00C04FAC149C__INCLUDED_)

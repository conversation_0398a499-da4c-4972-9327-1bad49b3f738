// ProductHelper.cpp: implementation of the CProductHelper class.
//
//////////////////////////////////////////////////////////////////////

#include "stdafx.h"
#include <afxmt.h>

#include "modal.h"
#include "ProductHelper.h"

#include "DataModelDialog.h"
#include "DataPurificationDialog.h"
#include "ProductMaintenance.h"
#include "PopulateUDF.h"
#include "UDFPage.h"
#include "ControlService.h"
#include "UtilityHelper.h"
#include "ProductDataService.h"
#include "ProcessingMessage.h"

#include "ResourceHelper.h"

#ifdef _DEBUG
#undef THIS_FILE
static char THIS_FILE[]=__FILE__;
#define new DEBUG_NEW
#endif

extern CControlService controlService;
extern CUtilityHelper utilityHelper;
extern CProductDataService productDataService;

extern CEvent g_ThreadDone;
//////////////////////////////////////////////////////////////////////
// Construction/Destruction
//////////////////////////////////////////////////////////////////////

CProductHelper::CProductHelper()
{

}

CProductHelper::~CProductHelper()
{

}

void CProductHelper::DataModel()
{
	CDataModelDialog dlg;

	try {
		dlg.DoModal();
	}
	catch (...) {
		AfxMessageBox("Error in data model.");
	}


}

void CProductHelper::DataPurification()
{
	CDataPurificationDialog dlg;

	try {
		dlg.DoModal();
	}
	catch (...) {
		AfxMessageBox("Error in data purification.");
	}

}


void CProductHelper::ProductMaintenance()
{
	CTemporaryResourceOverride tro;

	CProductSheet sheet("Product Maintenance", NULL, 0);
	CProductPage productPage;
	CProductContainerPage containerPage;
	CProductOptimizePage optimizePage;
	CUDFPage udfPage;

	if (controlService.GetCurrentFacilityDBId() == 0) {
		AfxMessageBox("Product Maintenance is only allowed for saved facilities.");
		return;
	}

	sheet.m_psh.dwFlags |= PSH_NOAPPLYNOW;
	sheet.AddPage(&productPage);
	sheet.AddPage(&containerPage);
	sheet.AddPage(&udfPage);
	sheet.AddPage(&optimizePage);

	try {
		sheet.DoModal();
	}
	catch (...) {
		AfxMessageBox("Error running ProductMaintenance");
	}

	return;
}

void CProductHelper::PopulateUDFWithFormula()
{
	CPopulateUDF dlg;

	try {
		dlg.DoModal();
	}
	catch (...) {
		AfxMessageBox("Error in Populate UDF With Formula.");
	}

}

void CProductHelper::DeleteProductsByFacility()
{
	if (AfxMessageBox("Do you want to delete all the products from the current facility?", MB_YESNO) != IDYES)
		return;

	CProcessingMessage dlg("Deleting Products for Current Facility", utilityHelper.GetParentWindow());
	
	int facilityId = controlService.GetCurrentFacilityDBId();

	CWinThread *pThread = AfxBeginThread(CProductDataService::DeleteProductsByFacilityThread, (void *)&facilityId);
	
	BOOL bThreadDone = FALSE;
	g_ThreadDone.ResetEvent();

	while (TRUE) {
		if ( ! utilityHelper.PeekAndPump() )
			break;
		
		bThreadDone = g_ThreadDone.Lock(0);
		if (bThreadDone)
			break;
	}
	
	int rc = facilityId;
	if (rc != 0)
		controlService.Log("Error deleting products.", "Error returned from DeleteProductsByFacilityThread.\n");
	else
		AfxMessageBox("Products successfully deleted.");

}

// ProcessingMessage.cpp: implementation of the CProcessingMessage class.
//
//////////////////////////////////////////////////////////////////////

#include "stdafx.h"
#include "ProcessingMessage.h"
#include "UtilityHelper.h"

#ifdef _DEBUG
#undef THIS_FILE
static char THIS_FILE[]=__FILE__;
#define new DEBUG_NEW
#endif

extern CUtilityHelper utilityHelper;

//////////////////////////////////////////////////////////////////////
// Construction/Destruction
//////////////////////////////////////////////////////////////////////

CProcessingMessage::CProcessingMessage()
{

}

CProcessingMessage::CProcessingMessage(const CString &message, CWnd *pParent)
{

	m_ProcessingDialog = new CProcessing;
	m_ProcessingDialog->Create(IDD_PROCESSING, pParent);
	m_ProcessingDialog->m_StatusText = message;
	m_ProcessingDialog->UpdateData(FALSE);
	m_ProcessingDialog->CenterWindow();
	m_ProcessingDialog->ShowWindow(SW_SHOW);
	m_ProcessingDialog->UpdateWindow();
	
}


CProcessingMessage::~CProcessingMessage()
{
	utilityHelper.GetParentWindow()->SetActiveWindow();

	m_ProcessingDialog->DestroyWindow();
}

void CProcessingMessage::UpdateMessage(const CString &message)
{
	if (m_ProcessingDialog == NULL)
		return;

	m_ProcessingDialog->m_StatusText = message;
	m_ProcessingDialog->UpdateData(FALSE);

}


void CProcessingMessage::Hide()
{
	m_ProcessingDialog->ShowWindow(SW_HIDE);
}

void CProcessingMessage::Show()
{
	m_ProcessingDialog->ShowWindow(SW_SHOW);
}

// FacilityDataService.h: interface for the CFacilityDataService class.
//
//////////////////////////////////////////////////////////////////////

#if !defined(AFX_FACILITYDATASERVICE_H__8DF86CE7_A641_4D36_B495_635E762D4DD7__INCLUDED_)
#define AFX_FACILITYDATASERVICE_H__8DF86CE7_A641_4D36_B495_635E762D4DD7__INCLUDED_

#if _MSC_VER > 1000
#pragma once
#endif // _MSC_VER > 1000

#include "SSACStringArray.h"
#include "qqhclasses.h"

class CFacilityDataService  
{
public:
	int MakeInActive(int facilityId, int sectionId, int aisleId, int sideId, int bayId, int levelId, int locationId);
	int MakeActive(int facilityId, int sectionId, int aisleId, int sideId, int bayId, int levelId, int locationId);
	BOOL IsSectionNameInUse(const CString &name, int facilityId, int sectionId);
	int GetOptimizeBayTypeCount(int facilityId);
	int GetFacility(int facilityId, CFacility &facility);
	BOOL IntegratedBayHasAssignments(int bayDBId);
	BOOL IntegratedAisleHasAssignments(int aisleDBId);
	BOOL IntegratedSideHasAssignments(int sideDBId);
	BOOL IntegratedSectionHasAssignments(int sectionDBId);

	BOOL IsHandleAPickPath(const CString& handle);
	BOOL IsFacilityIntegrated(int facilityDBId);
	int SetIntegrationStatus(BOOL isIntegrated);
	BOOL IsEndBay(qqhSLOTBay& bay);
	int GetFacilitySectionList(CStringArray &results);
	int GetBayHandleList(CStringArray &handleList);
	CFacilityDataService();
	virtual ~CFacilityDataService();

	CString GetCurrentDC();
	CString GetCurrentWarehouse();
	CString GetDCBySection(int facilityId, int sectionId);
	CString GetWarehouseBySection(int facilityId, int sectionId);

	int GetSectionsByFacility(long facilityDBID, CStringArray &sectionList);
	int GetAislesBySection(long sectionDBID, CStringArray &aisleList);
	int GetSidesByAisle(long aisleDBID, CStringArray &sideList);
	int GetBaysBySide(long sideDBID, CStringArray &bayList);
	int GetLevelsByBay(long bayDBID, CStringArray &levelList);
	int	GetLocationsByLevel(long levelDBID, CStringArray &locList);

	int GetMaxRelativeLevelBySection(long sectionDBID);
	int GetMaxRelativeLevelByAisle(long aisleDBID);
	int GetMaxRelativeLevelBySide(long sideDBID);
	int GetMaxRelativeLevelByBay(long bayDBID);
	int GetAvailableAttributes(CString elementType, CSsaStringArray & AttributeList);
	int GetAvailableAttributesByFacID(CString elementType, int facilityID, CSsaStringArray & AttributeList);

	int GetBayProfilesByFacility(CStringArray &bayProfileList);
	int GetBayProfileNameListByFacility(CStringArray &bayList);
	BOOL IsBayProfileInUse(CString & bayProfileName);

	int GetBayHandlesByProfile(int bayProfileDBId, CStringArray &bayHandles);
	int GetBayHandlesByAisleId(int aisleId, CStringArray &bayHandles);

	double GetLevelMaxWeight(int levelDBID);
	double GetBayMaxWeight(int bayDBID);

	int GetDuplicateLocationsByFacility(int facilityDBID, CSsaStringArray &duplicates);
	int GetFacilityDetailList(CStringArray & facilityList);
	int GetFacilityNameList(CStringArray &facilityList);
	int GetFacilityCost(CStringArray &facilityList);

	int GetLevelProfileListByFacility(CStringArray &levelProfileList, long sectionID);
	int GetLevelProfileUDFListByFacility(CStringArray &levelUDFList, long sectionID);

	int GetProductGroupForLocation(int locationDBID, CString &productGroupDescription);
	int QueryLocations(int pAssigned, int pProdGroupID, qqhSLOTQuery &pQuery, int pLevelType, CStringArray &pResults);
};

#endif // !defined(AFX_FACILITYDATASERVICE_H__8DF86CE7_A641_4D36_B495_635E762D4DD7__INCLUDED_)

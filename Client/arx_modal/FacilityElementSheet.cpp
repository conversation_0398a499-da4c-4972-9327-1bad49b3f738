// FacilityElementSheet.cpp : implementation file
//

#include "stdafx.h"
#include "modal.h"
#include "FacilityElementSheet.h"
#include "FacilityDataService.h"
#include "Constants.h"
#include "LocationProperties.h"
#include "ProcessingMessage.h"
#include "HelpService.h"

#ifdef _DEBUG
#define new DEBUG_NEW
#undef THIS_FILE
static char THIS_FILE[] = __FILE__;
#endif

extern CFacilityDataService facilityDataService;
extern CHelpService helpService;
/////////////////////////////////////////////////////////////////////////////
// CFacilityElementSheet

IMPLEMENT_DYNAMIC(CFacilityElementSheet, CPropertySheet)

CFacilityElementSheet::CFacilityElementSheet(UINT nIDCaption, CWnd* pParentWnd, UINT iSelectPage)
	:CPropertySheet(nIDCaption, pParentWnd, iSelectPage)
{
}

CFacilityElementSheet::CFacilityElementSheet(LPCTSTR pszCaption, CWnd* pParentWnd, UINT iSelectPage)
	:CPropertySheet(pszCaption, pParentWnd, iSelectPage)
{
}

CFacilityElementSheet::~CFacilityElementSheet()
{
	if (m_pActivateButton != NULL)
		delete m_pActivateButton;

	if (m_pDeactivateButton != NULL)
		delete m_pDeactivateButton;
}

CFacilityElementSheet::CFacilityElementSheet()
{
	CommonConstruct(NULL, 0);
	m_ElementType = -1;
	m_ElementDBId = 0;
	m_pActivateButton = NULL;
	m_pDeactivateButton = NULL;
	m_AllowUpdate = TRUE;
}

BEGIN_MESSAGE_MAP(CFacilityElementSheet, CPropertySheet)
	//{{AFX_MSG_MAP(CFacilityElementSheet)
	ON_WM_HELPINFO()
	//}}AFX_MSG_MAP
END_MESSAGE_MAP()

/////////////////////////////////////////////////////////////////////////////
// CFacilityElementSheet message handlers

BOOL CFacilityElementSheet::OnInitDialog() 
{
	BOOL bResult = CPropertySheet::OnInitDialog();
	
	CButton *pButton = (CButton *)GetDlgItem(IDOK);
	if (! m_AllowUpdate) {
		pButton->EnableWindow(FALSE);
	}

	CRect r;
	pButton->GetWindowRect(&r);
	CFont *font = pButton->GetFont();
	this->ScreenToClient(&r);

	if (m_ElementDBId > 0) {
		r.right += 5;
		m_pActivateButton = new CButton;
		m_pActivateButton->Create("Make Active", WS_CHILD & ~(BS_DEFPUSHBUTTON), r, this, WM_USER+1);
		m_pActivateButton->SetFont(font);
		m_pActivateButton->MoveWindow(10, r.top, r.Width(), r.Height(), FALSE);
		m_pActivateButton->EnableWindow(TRUE);
		m_pActivateButton->ShowWindow(SW_SHOW);
		
		m_pDeactivateButton = new CButton;
		m_pDeactivateButton->Create("Make In-Active", WS_CHILD & ~(BS_DEFPUSHBUTTON), r, this, WM_USER+2);
		m_pDeactivateButton->SetFont(font);
		m_pDeactivateButton->MoveWindow(10+r.Width()+10, r.top, r.Width(), r.Height(), FALSE);
		m_pDeactivateButton->EnableWindow(TRUE);
		m_pDeactivateButton->ShowWindow(SW_SHOW);
	}


		
	return bResult;
}


BOOL CFacilityElementSheet::OnCommand(WPARAM wParam, LPARAM lParam) 
{
	if (HIWORD(wParam) == BN_CLICKED) {
		if (LOWORD(wParam) == WM_USER+1) {
			OnActivate();
		}
		else if (LOWORD(wParam) == WM_USER+2) {
			OnDeactivate();
		}
	}

	return CPropertySheet::OnCommand(wParam, lParam);
}


void CFacilityElementSheet::OnActivate()
{
	CWaitCursor cwc;
	CProcessingMessage dlg("Updating...", this);

	switch (m_ElementType) {
	case UDF_FACILITY:
		facilityDataService.MakeActive(m_ElementDBId, 0, 0, 0, 0, 0, 0);
		break;
	case UDF_SECTION:
		facilityDataService.MakeActive(0, m_ElementDBId, 0, 0, 0, 0, 0);
		break;
	case UDF_AISLE:
		facilityDataService.MakeActive(0, 0, m_ElementDBId, 0, 0, 0, 0);
		break;
	case UDF_SIDE:
		facilityDataService.MakeActive(0, 0, 0, m_ElementDBId, 0, 0, 0);
		break;
	case UDF_BAY:
		facilityDataService.MakeActive(0, 0, 0, 0, m_ElementDBId, 0, 0);
		break;
	case UDF_LEVEL:
		facilityDataService.MakeActive(0, 0, 0, 0, 0, m_ElementDBId, 0);
		break;
	case UDF_LOCATION:
		// this is hokey but for some reason I get an exception when I try the PostMessage
		// way of doing it
		{
			facilityDataService.MakeActive(0, 0, 0, 0, 0, 0, m_ElementDBId);
			CLocationProperties *pPage = (CLocationProperties *)GetPage(0);
			pPage->m_IsActive = TRUE;
			pPage->UpdateData(FALSE);
		}
		break;
	}

	//this->GetPage(0)->PostMessage(MESSAGE_ACTIVATE, 0, 0);

}

void CFacilityElementSheet::OnDeactivate()
{
	CWaitCursor cwc;
	CProcessingMessage dlg("Updating...", this);

	switch (m_ElementType) {
	case UDF_FACILITY:
		facilityDataService.MakeInActive(m_ElementDBId, 0, 0, 0, 0, 0, 0);
		break;
	case UDF_SECTION:
		facilityDataService.MakeInActive(0, m_ElementDBId, 0, 0, 0, 0, 0);
		break;
	case UDF_AISLE:
		facilityDataService.MakeInActive(0, 0, m_ElementDBId, 0, 0, 0, 0);
		break;
	case UDF_SIDE:
		facilityDataService.MakeInActive(0, 0, 0, m_ElementDBId, 0, 0, 0);
		break;
	case UDF_BAY:
		facilityDataService.MakeInActive(0, 0, 0, 0, m_ElementDBId, 0, 0);
		break;
	case UDF_LEVEL:
		facilityDataService.MakeInActive(0, 0, 0, 0, 0, m_ElementDBId, 0);
		break;
	case UDF_LOCATION:
		{
			facilityDataService.MakeInActive(0, 0, 0, 0, 0, 0, m_ElementDBId);
			CLocationProperties *pPage = (CLocationProperties *)GetPage(0);
			pPage->m_IsActive = FALSE;
			pPage->UpdateData(FALSE);
		}
		break;
	}

	/*
	CWnd *pWnd = this->GetPage(0);
	pWnd->PostMessage(MESSAGE_DEACTIVATE, 0, 0);
	*/
}


BOOL CFacilityElementSheet::OnHelpInfo(HELPINFO* pHelpInfo) 
{
	switch (pHelpInfo->iCtrlId) {
	case IDOK:
		helpService.ShowFieldHelp("FacilityElementMaintenance_OK");
		break;
	case IDCANCEL:
	case IDHELP:
		helpService.ShowFieldHelp(1, pHelpInfo->iCtrlId);
		break;
	case MESSAGE_ACTIVATE:
		helpService.ShowFieldHelp("FacilityElementMaintenance_Activate");
		break;
	case MESSAGE_DEACTIVATE:
		helpService.ShowFieldHelp("FacilityElementMaintenance_Deactivate");
		break;
	default:
		this->PressButton(PSBTN_HELP);
		break;
	}

	return FALSE;
}


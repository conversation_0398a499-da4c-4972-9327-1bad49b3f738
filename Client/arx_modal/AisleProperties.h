#if !defined(AFX_AISLEPROPERTIES_H__6AE8C6F1_A9F1_11D4_9EBD_00C04FAC149C__INCLUDED_)
#define AFX_AISLEPROPERTIES_H__6AE8C6F1_A9F1_11D4_9EBD_00C04FAC149C__INCLUDED_

#if _MSC_VER > 1000
#pragma once
#endif // _MSC_VER > 1000
// AisleProperties.h : header file
//

/////////////////////////////////////////////////////////////////////////////
// CAisleProperties dialog

class CAisleProperties : public CPropertyPage
{
	DECLARE_DYNCREATE(CAisleProperties)

// Construction
public:
	CAisleProperties();
	~CAisleProperties();

// Dialog Data
	//{{AFX_DATA(CAisleProperties)
	enum { IDD = IDD_AISLE_PROPERTIES };
	CString	m_Coordinates;
	CString	m_Description;
	float	m_Rotation;
	//}}AFX_DATA


// Overrides
	// ClassWizard generate virtual function overrides
	//{{AFX_VIRTUAL(CAisleProperties)
	protected:
	virtual void DoDataExchange(CDataExchange* pDX);    // DDX/DDV support
	virtual BOOL OnNotify(WPARAM wParam, LPARAM lParam, LRESULT* pResult);
	//}}AFX_VIRTUAL

// Implementation
protected:
	// Generated message map functions
	//{{AFX_MSG(CAisleProperties)
	afx_msg BOOL OnHelpInfo(HELPINFO* pHelpInfo);
	//}}AFX_MSG
	DECLARE_MESSAGE_MAP()

};

//{{AFX_INSERT_LOCATION}}
// Microsoft Visual C++ will insert additional declarations immediately before the previous line.

#endif // !defined(AFX_AISLEPROPERTIES_H__6AE8C6F1_A9F1_11D4_9EBD_00C04FAC149C__INCLUDED_)

#if !defined(AFX_BAYPROFILETOPVIEWBUTTON_H__EA39E449_130C_4F50_BDF5_5C8E90F6C48C__INCLUDED_)
#define AFX_BAYPROFILETOPVIEWBUTTON_H__EA39E449_130C_4F50_BDF5_5C8E90F6C48C__INCLUDED_

#if _MSC_VER > 1000
#pragma once
#endif // _MSC_VER > 1000
// BayProfileTopViewButton.h : header file
//
#include "BayProfileDimensionInfo.h"

/////////////////////////////////////////////////////////////////////////////
// CBayProfileTopViewButton window

class CBayProfileTopViewButton : public CButton
{
// Construction
public:
	CBayProfileTopViewButton();

// Attributes
public:
	CBayProfileDimensionInfo m_DimensionInfo;
// Operations
public:

// Overrides
	// ClassWizard generated virtual function overrides
	//{{AFX_VIRTUAL(CBayProfileTopViewButton)
	public:
	virtual void DrawItem(LPDRAWITEMSTRUCT lpDrawItemStruct);
	//}}AFX_VIRTUAL

// Implementation
public:
	virtual ~CBayProfileTopViewButton();

	// Generated message map functions
protected:
	//{{AFX_MSG(CBayProfileTopViewButton)
		// NOTE - the ClassWizard will add and remove member functions here.
	//}}AFX_MSG

	DECLARE_MESSAGE_MAP()
private:
	void DrawVertDimLine(CDC &cdc, const CPoint &startPt, int len, int width, const CString &text, int textSize = 14);
	void DrawHorzDimLine(CDC &cdc, const CPoint &startPt, int len, int width, const CString &text, int textSize = 14);
	void DrawVertLine(CDC &cdc, const CPoint &startPt, int len, int width, BOOL bDashed = FALSE);
	void DrawHorzLine(CDC &cdc, const CPoint &startPt, int len, int width, BOOL bDashed = FALSE);
	void DrawBox(CDC &cdc, const CRect& r, int width, BOOL bDashed = FALSE);

	void DrawBin(CDC &cdc);
	void DrawPallet(CDC &cdc);
	void DrawFlow(CDC &cdc);
	void DrawFloor(CDC &cdc);
	void DrawDriveIn(CDC &cdc);
	void DrawBoxWithText(CDC &cdc, const CRect& r, int width, BOOL bDashed,
										const CString &text, int textSize);

};

/////////////////////////////////////////////////////////////////////////////

//{{AFX_INSERT_LOCATION}}
// Microsoft Visual C++ will insert additional declarations immediately before the previous line.

#endif // !defined(AFX_BAYPROFILETOPVIEWBUTTON_H__EA39E449_130C_4F50_BDF5_5C8E90F6C48C__INCLUDED_)

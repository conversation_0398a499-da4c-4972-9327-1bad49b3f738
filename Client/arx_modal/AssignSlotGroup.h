#if !defined(AFX_ASSIGNSLOTGROUP_H__D80FFF0C_1C01_11D2_A47B_00C04FA8700B__INCLUDED_)
#define AFX_ASSIGNSLOTGROUP_H__D80FFF0C_1C01_11D2_A47B_00C04FA8700B__INCLUDED_
#include "resource.h"
#if _MSC_VER >= 1000
#pragma once
#endif // _MSC_VER >= 1000
// AssignSlotGroup.h : header file
//

/////////////////////////////////////////////////////////////////////////////
// CAssignSlotGroup dialog

class CAssignSlotGroup : public CDialog
{
// Construction
public:
	CAssignSlotGroup(CWnd* pParent = NULL);   // standard constructor

// Dialog Data
	//{{AFX_DATA(CAssignSlotGroup)
	enum { IDD = IDD_SLOTGROUP };
	CListBox	m_slotGroupList;
	//}}AFX_DATA


// Overrides
	// ClassWizard generated virtual function overrides
	//{{AFX_VIRTUAL(CAssignSlotGroup)
	protected:
	virtual void DoDataExchange(CDataExchange* pDX);    // DDX/DDV support
	//}}AFX_VIRTUAL

// Implementation
protected:

	// Generated message map functions
	//{{AFX_MSG(CAssignSlotGroup)
	virtual BOOL OnInitDialog();
	virtual void OnOK();
	afx_msg BOOL OnHelpInfo(HELPINFO* pHelpInfo);
	afx_msg void OnHelp();
	afx_msg void OnDblclkSlotgrouplist();
	//}}AFX_MSG
	DECLARE_MESSAGE_MAP()
private:
	CStringArray m_SelectedHandles;
};

//{{AFX_INSERT_LOCATION}}
// Microsoft Developer Studio will insert additional declarations immediately before the previous line.

#endif // !defined(AFX_ASSIGNSLOTGROUP_H__D80FFF0C_1C01_11D2_A47B_00C04FA8700B__INCLUDED_)

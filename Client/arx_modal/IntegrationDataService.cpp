// IntegrationDataService.cpp: implementation of the CIntegrationDataService class.
//
//////////////////////////////////////////////////////////////////////

#include "stdafx.h"
#include "modal.h"
#include "IntegrationDataService.h"
#include "DataAccessService.h"
#include "UtilityHelper.h"
#include "Constants.h"
#include "ForteService.h"
#include "UtilityHelper.h"
#include "InterfaceHelper.h"
#include "ControlService.h"
#include "Solution.h"
#include "Move.h"
#include "BusyWaitCursor.h"

#include "base.h"
#include "MiscClasses.h"
#include "SLOTSessionMgr.h"
#include "SrvcObjs.h"

#ifdef _DEBUG
#undef THIS_FILE
static char THIS_FILE[]=__FILE__;
#define new DEBUG_NEW
#endif

extern CDataAccessService dataAccessService;
extern CUtilityHelper utilityHelper;
extern CForteService forteService;
extern CUtilityHelper utilityHelper;
extern CControlService controlService;

//////////////////////////////////////////////////////////////////////
// Construction/Destruction
//////////////////////////////////////////////////////////////////////

CIntegrationDataService::CIntegrationDataService()
{

}

CIntegrationDataService::~CIntegrationDataService()
{

}

BOOL CIntegrationDataService::WMSExists(CString &name, int dbid)
{
	CString sql;
	CStringArray results;
	CString tempName(name);
	tempName.Replace("'", "''");

	sql.Format("select count(*) from DBWMS "
		"where Name = '%s' ", tempName);
	if (dbid > 0) {
		CString temp;
		temp.Format(" and dbwmsid <> %d", dbid);
		sql += temp;
	}
	
	dataAccessService.ExecuteQuery("WMSExists", sql, results, TRUE);

	if (results.GetSize() > 0)
		return (atoi(results[0]) > 0);

	return FALSE;

}

BOOL CIntegrationDataService::WMSIdExists(CString &wmsId, int groupDBId, int wmsDBId)
{
	CString sql;
	CStringArray results;
	
	sql.Format("select count(*) from DBWMS "
		"where wmsid = '%s' and dbwmsgroupid = %d ", wmsId, groupDBId);
	if (wmsDBId > 0) {
		CString temp;
		temp.Format(" and dbwmsid <> %d", wmsDBId);
		sql += temp;
	}
	
	dataAccessService.ExecuteQuery("WMSIdExists", sql, results, TRUE);

	if (results.GetSize() > 0)
		return (atoi(results[0]) > 0);

	return FALSE;

}


BOOL CIntegrationDataService::WMSGroupExists(CString &name, int dbid)
{
	CString sql;
	CStringArray results;
	CString tempName(name);
	tempName.Replace("'", "''");

	sql.Format("select count(*) from DBWMSGroup "
		"where Name = '%s'", tempName);
	
	if (dbid > 0) {
		CString temp;
		temp.Format(" and dbwmsgroupid <> %d", dbid);
		sql += temp;
	}
	
	dataAccessService.ExecuteQuery("WMSGroupExists", sql, results, TRUE);

	if (results.GetSize() > 0)
		return (atoi(results[0]) > 0);

	return FALSE;

}

BOOL CIntegrationDataService::WMSGroupIdExists(CString &wmsId, int groupDBId)
{
	CString sql;
	CStringArray results;
	CString tempName(wmsId);
	tempName.Replace("'", "''");

	sql.Format("select count(*) from DBWMSGroup "
		"where wmsId = '%s'", tempName);
	
	if (groupDBId > 0) {
		CString temp;
		temp.Format(" and dbwmsgroupid <> %d", groupDBId);
		sql += temp;
	}
	
	dataAccessService.ExecuteQuery("WMSGroupIdExists", sql, results, TRUE);

	if (results.GetSize() > 0)
		return (atoi(results[0]) > 0);

	return FALSE;

}

int CIntegrationDataService::StoreWMS(CWMS &wms)
{
	CString sql;
	CString name(wms.m_Name), description(wms.m_Description);

	name.Replace("'", "''");
	description.Replace("'", "''");

	if (wms.m_WMSDBId > 0) {	// update
		sql.Format("update DBWMS set "
			"Name = '%s', "
			"Description = '%s', "
			"WMSId = '%s', "
			"DBWMSGroupId = %d "
			"where DBWMSId = %d ",
			name, description, wms.m_WMSId,
			wms.m_GroupDBId, wms.m_WMSDBId);
	}
	else {		// insert
		wms.m_WMSDBId = dataAccessService.GetNextKey("DBWMS", 1);
		if (wms.m_WMSDBId <= 0) {
			return -1;
		}
		sql.Format("insert into DBWMS ("
			"DBWMSId, Name, Description, WMSId, DBWMSGroupId) "
			"values (%d, '%s', '%s', '%s', %d)",
			wms.m_WMSDBId, name, description, wms.m_WMSId,
			wms.m_GroupDBId);
	}

	return dataAccessService.ExecuteStatement("StoreWMS", sql);

}

int CIntegrationDataService::GetExternalSystemList(const CString &type, CStringArray &results)
{
	CString sql;

	if (type != "") {
		sql.Format("select DBExternalSystemId, DBExternalSystem.Name, Version "
			"from DBExternalSystem, DBSystemType "
			"where DBExternalSystem.DBSystemTypeId = DBSystemType.DBSystemTypeId "
			"and DBSystemType.Name = '%s'", type);
	}
	else {
		sql.Format("Select DBExternalSystemId, Name "
			"from DBExternalSystem");
	}

	return dataAccessService.ExecuteQuery("GetExternalSystemList", sql, results);

}

int CIntegrationDataService::DeleteWMS(int wmsdbid)
{
	CString sql;
	CStringArray stmts;
	
	sql.Format("delete from DBWMSImportMap where DBWMSId = %d", wmsdbid);
	stmts.Add(sql);

	sql.Format("delete from DBWMSExportMap where DBWMSId = %d", wmsdbid);
	stmts.Add(sql);

	sql.Format("delete from DBWMS where DBWMSId = %d", wmsdbid);
	stmts.Add(sql);

	return dataAccessService.ExecuteStatements("DeleteWMS", stmts);

}

int CIntegrationDataService::DeleteWMSGroup(int groupDBId)
{
	CString sql;
	CStringArray stmts;

	sql.Format("delete from dbwms where dbwmsgroupid = %d", groupDBId);
	stmts.Add(sql);
	
	sql.Format("delete from dbwmsgroupconnection where dbwmsgroupid = %d", groupDBId);
	stmts.Add(sql);

	sql.Format("delete from DBWMSGroup where DBWMSGroupId = %d", groupDBId);
	stmts.Add(sql);

	return dataAccessService.ExecuteStatements("DeleteWMSGroup", stmts);

}

int CIntegrationDataService::GetSectionWMSImportAssignment(int sectionDBId, CWMS& wms)
{
	CString sql;
	CStringArray results;

	sql.Format("select dbwms.* "
		"from dbwms, dbwmsimportmap "
		"where dbwms.dbwmsid = dbwmsimportmap.dbwmsid "
		"and dbwmsimportmap.dbsectionid = %d", sectionDBId);

	try {
		dataAccessService.ExecuteQuery("GetSectionWMSImportAssignment", sql, results);
	}
	catch (...) {
		utilityHelper.ProcessError("Error getting section WMS import assignments from database.");
		return -1;
	}
	
	if (results.GetSize() > 0) {
		wms.Parse(results[0]);
	}

	return 0;
}


int CIntegrationDataService::GetSectionWMSExportAssignment(int sectionDBId, CWMS& wms)
{
	CString sql;
	CStringArray results;

	sql.Format("select dbwms.* "
		"from dbwms, dbwmsexportmap "
		"where dbwms.dbwmsid = dbwmsexportmap.dbwmsid "
		"and dbwmsexportmap.dbsectionid = %d", sectionDBId);

	try {
		dataAccessService.ExecuteQuery("GetSectionWMSExportAssignment", sql, results);
	}
	catch (...) {
		utilityHelper.ProcessError("Error getting section WMS export assignments from database.");
		return -1;
	}
	
	if (results.GetSize() > 0) {
		wms.Parse(results[0]);
	}

	return 0;
}

int CIntegrationDataService::StoreWMSImportMap(CWMSMap &map)
{
	CString sql;
	CStringArray stmts;

	
	if (map.m_WMSMapDBId == 0) {
		map.m_WMSMapDBId = dataAccessService.GetNextKey("DBWMSImportMap", 1);
		
		sql.Format("insert into dbwmsimportmap (dbwmsimportmapid, dbwmsid, dbsectionid, dbfacilityid ) "
			"values (%d, %d, %d, %d)",
			map.m_WMSMapDBId, map.m_WMSDBId, map.m_SectionDBId, map.m_FacilityDBId);
		stmts.Add(sql);
		
	}
	else {
		sql.Format("update dbwmsimportmap set dbwmsid = %d, dbsectionid = %d, dbfacilityid = %d "
			"where dbwmsimportmapid = %d", 
			map.m_WMSDBId, map.m_SectionDBId, map.m_FacilityDBId, map.m_WMSMapDBId);
		stmts.Add(sql);
	}

	dataAccessService.ExecuteStatements("StoreWMSImportMap", stmts);

	return 0;
}

int CIntegrationDataService::StoreWMSExportMap(CWMSMap &map)
{
	CString sql;
	CStringArray stmts;

	
	if (map.m_WMSMapDBId == 0) {
		map.m_WMSMapDBId = dataAccessService.GetNextKey("DBWMSExportMap", 1);
		
		sql.Format("insert into dbwmsExportmap (dbwmsexportmapid, dbwmsid, dbsectionid, dbfacilityid ) "
			"values (%d, %d, %d, %d)",
			map.m_WMSMapDBId, map.m_WMSDBId, map.m_SectionDBId, map.m_FacilityDBId);
		stmts.Add(sql);
		
	}
	else {
		sql.Format("update dbwmsexportmap set dbwmsid = %d, dbsectionid = %d, dbfacilityid = %d "
			"where dbwmsexportmapid = %d", 
			map.m_WMSDBId, map.m_SectionDBId, map.m_FacilityDBId, map.m_WMSMapDBId);
		stmts.Add(sql);
	}
			
	dataAccessService.ExecuteStatements("StoreWMSImportMap", stmts);

	return 0;
}


int CIntegrationDataService::DeleteWMSImportMap(int mapDBId)
{
	CString sql;
	CStringArray stmts;

	sql.Format("delete from dbwmsimportmap where dbwmsimportmapid = %d", mapDBId);
	stmts.Add(sql);


	return dataAccessService.ExecuteStatements("DeleteWMSImportMap", stmts);

}


int CIntegrationDataService::DeleteWMSExportMap(int mapDBId)
{
	CString sql;
	CStringArray stmts;

	sql.Format("delete from dbwmsexportmap where dbwmsexportmapid = %d", mapDBId);
	stmts.Add(sql);


	return dataAccessService.ExecuteStatements("DeleteWMSExportMap", stmts);

}


int CIntegrationDataService::GetWMSList(int groupDBId, CStringArray &wmsList)
{
	CString sql;
	CString temp("");

	if (groupDBId > 0)
		temp.Format("and dbwmsgroup.dbwmsgroupid = %d ", groupDBId);

	sql.Format("select dbwms.*, dbexternalsystem.name, dbwmsgroup.Name "
		"from dbwms, dbexternalsystem, dbwmsgroup "
		"where dbwmsgroup.dbexternalsystemid = dbexternalsystem.dbexternalsystemid "
		"and dbwms.dbwmsgroupid = dbwmsgroup.dbwmsgroupid "
		"%s "
		"order by dbwmsgroup.Name, dbwms.name ", temp);

	return dataAccessService.ExecuteQuery("GetWMSList", sql, wmsList);

}

int CIntegrationDataService::GetWMSImportMap(int wmsDBId, CStringArray &mapList)
{
	CString sql;
	CString temp("");

	if (wmsDBId > 0) {
		temp.Format(" and wms.dbwmsid = %d ", wmsDBId);
	}

	sql.Format("select map.dbwmsimportmapid, map.dbwmsid, map.dbsectionid, "
		"fac.dbfacilityid, wms.name, fac.description, sect.description, 0 direction "
		"from dbwms wms, dbfacility fac, dbsection sect, dbwmsimportmap map "
		"where wms.dbwmsid = map.dbwmsid "
		"and map.dbsectionid = sect.dbsectionid "
		"and sect.dbfacilityid = fac.dbfacilityid "
		"%s "
		"union "
		"select map.dbwmsimportmapid, map.dbwmsid, map.dbsectionid, "
		"fac.dbfacilityid, wms.name, fac.description, 'All Sections', 0 direction "
		"from dbwms wms, dbfacility fac, dbwmsimportmap map "
		"where wms.dbwmsid = map.dbwmsid "
		"and map.dbfacilityid = fac.dbfacilityid "
		"and map.dbsectionid = 0 "
		"%s "
		"order by 5, 6, 7", temp, temp);



	return dataAccessService.ExecuteQuery("GetWMSImportMapList", sql, mapList);

}

int CIntegrationDataService::GetWMSExportMap(int wmsDBId, CStringArray &mapList)
{
	CString sql;

	CString temp("");

	if (wmsDBId > 0) {
		temp.Format(" and wms.dbwmsid = %d ", wmsDBId);
	}

	sql.Format("select map.dbwmsexportmapid, map.dbwmsid, map.dbsectionid, "
		"fac.dbfacilityid, wms.name, fac.description, sect.description, 1 direction "
		"from dbwms wms, dbfacility fac, dbsection sect, dbwmsexportmap map "
		"where wms.dbwmsid = map.dbwmsid "
		"and map.dbsectionid = sect.dbsectionid "
		"and sect.dbfacilityid = fac.dbfacilityid "
		"%s "
		"union "
		"select map.dbwmsexportmapid, map.dbwmsid, 0, "
		"fac.dbfacilityid, wms.name, fac.description, 'All Sections', 1 direction "
		"from dbwms wms, dbfacility fac, dbwmsexportmap map "
		"where wms.dbwmsid = map.dbwmsid "
		"and fac.dbfacilityid = map.dbfacilityid "
		"and map.dbsectionid = 0 "
		"%s "
		"order by 5, 7", temp, temp);


	return dataAccessService.ExecuteQuery("GetWMSExportMapList", sql, mapList);

}

int CIntegrationDataService::StoreWMSGroup(CWMSGroup &group)
{
	CString sql;
	CStringArray stmts;

	CString name(group.m_Name);
	name.Replace("'", "''");
	CString description(group.m_Description);
	description.Replace("'", "''");
	
	if (group.m_WMSGroupDBId <= 0) {
		group.m_WMSGroupDBId = dataAccessService.GetNextKey("DBWMSGroup", 1);
		sql.Format("insert into dbwmsgroup "
			"(dbwmsgroupid, name, description, wmsid, dbexternalsystemid) "
			"values (%d, '%s', '%s', '%s', %d)", group.m_WMSGroupDBId, name, description, group.m_WMSId,
			group.m_ExternalSystemDBId);

		stmts.Add(sql);

		int nextKey = dataAccessService.GetNextKey("DBWMSGroupConnection", group.m_ConnectionList.GetSize());
		for (int i=0; i < group.m_ConnectionList.GetSize(); ++i) {
			CWMSGroupConnection *pConn = group.m_ConnectionList[i];
			// The sync screen creates a dummy connection if there isn't one
			// but we don't want to store it
			if (pConn->m_ExternalConnectionDBId <= 0)
				continue;

			pConn->m_GroupConnectionDBId = nextKey++;
			sql.Format("insert into dbwmsgroupconnection values (%d, %d, %d, %d, %d)",
				pConn->m_GroupConnectionDBId, pConn->m_InterfaceType, pConn->m_Direction,
				pConn->m_ExternalConnectionDBId, group.m_WMSGroupDBId);
			stmts.Add(sql);
		}
	}
	else {
		sql.Format("update dbwmsgroup set name = '%s', description = '%s', wmsid = '%s',"
			"dbexternalsystemid = %d "
			"where dbwmsgroupid = %d", name, description, group.m_WMSId, group.m_ExternalSystemDBId,
			group.m_WMSGroupDBId);

		stmts.Add(sql);

		sql.Format("delete from dbwmsgroupconnection where dbwmsgroupid = %d", group.m_WMSGroupDBId);
		stmts.Add(sql);

		int nextKey = dataAccessService.GetNextKey("DBWMSGroupConnection", group.m_ConnectionList.GetSize());
		for (int i=0; i < group.m_ConnectionList.GetSize(); ++i) {
			CWMSGroupConnection *pConn = group.m_ConnectionList[i];

			// The sync screen creates a dummy connection if there isn't one
			// but we don't want to store it
			if (pConn->m_ExternalConnectionDBId <= 0)
				continue;

			pConn->m_GroupConnectionDBId = nextKey++;
			sql.Format("insert into dbwmsgroupconnection values (%d, %d, %d, %d, %d)",
				pConn->m_GroupConnectionDBId, pConn->m_InterfaceType, pConn->m_Direction,
				pConn->m_ExternalConnectionDBId, group.m_WMSGroupDBId);
			stmts.Add(sql);
		}
	}

	return dataAccessService.ExecuteStatements("StoreWMSGroup", stmts);
}



int CIntegrationDataService::GetWMSGroupList(CStringArray &groupList)
{
	CString sql;

	sql.Format("select dbwmsgroup.*, dbexternalsystem.* from dbwmsgroup, dbexternalsystem "
		"where dbwmsgroup.dbexternalsystemid = dbexternalsystem.dbexternalsystemid "
		"order by dbwmsgroup.name");

	return dataAccessService.ExecuteQuery("GetWMSGroupList", sql, groupList);

}

int CIntegrationDataService::GetWMSMapSummaryList(CStringArray &results)
{
	CString sql;

	sql.Format("select g.name, w.name,  "
		"fi.description, si.description, "
		"fe.description, se.description "
		"from dbwmsgroup g, dbwms w,  "
		"dbwmsimportmap import, dbfacility fi, dbsection si, "
		"dbwmsexportmap export, dbfacility fe, dbsection se "
		"where g.dbwmsgroupid = w.dbwmsgroupid "
		"and import.dbwmsid = w.dbwmsid "
		"and import.dbsectionid = si.dbsectionid "
		"and si.dbfacilityid = fi.dbfacilityid "
		"and export.dbwmsid = w.dbwmsid "
		"and export.dbsectionid = se.dbsectionid "
		"and export.dbfacilityid = fe.dbfacilityid "
		"and import.dbwmsid = export.dbwmsid "
		"and import.dbsectionid = export.dbsectionid "
		"union "
		"select g.name, w.name,  "
		"fi.description, si.description, "
		"' ', ' '"
		"from dbwmsgroup g, dbwms w,  "
		"dbwmsimportmap import, dbfacility fi, dbsection si "
		"where g.dbwmsgroupid = w.dbwmsgroupid "
		"and import.dbwmsid = w.dbwmsid "
		"and import.dbsectionid = si.dbsectionid "
		"and si.dbfacilityid = fi.dbfacilityid "
		"and not exists ( select dbwmsexportmapid from dbwmsexportmap "
		"    where dbwmsexportmap.dbwmsid = import.dbwmsid "
		"    and dbwmsexportmap.dbsectionid = import.dbsectionid) "
		"union "
		"select g.name, w.name,  "
		"' ', ' ', "
		"fe.description, se.description "
		"from dbwmsgroup g, dbwms w,  "
		"dbwmsexportmap export, dbfacility fe, dbsection se "
		"where g.dbwmsgroupid = w.dbwmsgroupid "
		"and export.dbwmsid = w.dbwmsid "
		"and export.dbsectionid = se.dbsectionid "
		"and se.dbfacilityid = fe.dbfacilityid "
		"and not exists ( select dbwmsimportmapid from dbwmsimportmap "
		"    where dbwmsimportmap.dbwmsid = export.dbwmsid "
		"    and dbwmsimportmap.dbsectionid = export.dbsectionid) "
		"order by 1, 2 ");

	return dataAccessService.ExecuteQuery("GetWMSMapSummaryList", sql, results);


}

int CIntegrationDataService::CheckAssignmentIntegrationStatus(int facilityDBId, int sectionDBId)
{
	CString sql;

	if (sectionDBId > 0) {
		sql.Format("select count(*) "
			"from dbslotsolution ss, dblocation l, dblevel le, dbbay b, dbside si, dbaisle a "
			"where ss.origin = 0 "
			"and ss.dblocationid = l.dblocationid "
			"and l.dblevelid = le.dblevelid "
			"and le.dbbayid = b.dbbayid "
			"and b.dbsideid = si.dbsideid "
			"and si.dbaisleid = a.dbaisleid "
			"and a.dbsectionid = %d "
			// Oracle-specific
			"and rownum = 1", sectionDBId);
	}
	else {
		sql.Format("select count(*) "
			"from dbslotsolution ss, dblocation l, dblevel le, dbbay b, dbside si, dbaisle a, dbsection se "
			"where ss.origin = 0 "
			"and ss.dblocationid = l.dblocationid "
			"and l.dblevelid = le.dblevelid "
			"and le.dbbayid = b.dbbayid "
			"and b.dbsideid = si.dbsideid "
			"and si.dbaisleid = a.dbaisleid "
			"and a.dbsectionid = se.dbsectionid "
			"and se.dbfacilityid = %d "
			// Oracle-specific
			"and rownum = 1", facilityDBId);
	}

	CStringArray results;

	dataAccessService.ExecuteQuery("CheckAssignmentIntegrationStatus", sql, results, TRUE);
	if (results.GetSize() > 0)
		return atoi(results[0]);


	return 0;
}

int CIntegrationDataService::CheckLocationIntegrationStatus(int facilityDBId, int sectionDBId)
{
	CString sql;

	if (sectionDBId > 0) {
		sql.Format("select count(*) "
			"from dblocation l, dblevel le, dbbay b, dbside si, dbaisle a "
			"where l.dblevelid = le.dblevelid "
			"and le.dbbayid = b.dbbayid "
			"and b.dbsideid = si.dbsideid "
			"and si.dbaisleid = a.dbaisleid "
			"and a.dbsectionid = %d "
			"and l.status != %d "
			// Oracle-specific
			"and rownum = 1", sectionDBId, LOC_STATUS_NOT_INTEGRATED);
	}
	else {
		sql.Format("select count(*) "
			"from dblocation l, dblevel le, dbbay b, dbside si, dbaisle a, dbsection se "
			"where l.dblevelid = le.dblevelid "
			"and le.dbbayid = b.dbbayid "
			"and b.dbsideid = si.dbsideid "
			"and si.dbaisleid = a.dbaisleid "
			"and a.dbsectionid = se.dbsectionid "
			"and se.dbfacilityid = %d "
			"and l.status != %d "
			// Oracle-specific
			"and rownum = 1", facilityDBId, LOC_STATUS_NOT_INTEGRATED);
	}

	CStringArray results;
	dataAccessService.ExecuteQuery("CheckLocationIntegrationStatus", sql, results, TRUE);
	if (results.GetSize() > 0)
		return atoi(results[0]);


	return 0;
}

int CIntegrationDataService::GetIntegratedGroupList(CStringArray &groupList)
{
	CString sql;
	
	sql.Format("select unique dbwmsgroup.*, dbexternalsystem.name "
		"from dbwms, dbexternalsystem, dbwmsgroup, dbwmsimportmap map "
		"where dbwmsgroup.dbexternalsystemid = dbexternalsystem.dbexternalsystemid "
		"and dbwms.dbwmsgroupid = dbwmsgroup.dbwmsgroupid "
		"and map.dbwmsid = dbwms.dbwmsid "
		"union "
		"select unique dbwmsgroup.*, dbexternalsystem.name "
		"from dbwms, dbexternalsystem, dbwmsgroup, dbwmsexportmap map "
		"where dbwmsgroup.dbexternalsystemid = dbexternalsystem.dbexternalsystemid "
		"and dbwms.dbwmsgroupid = dbwmsgroup.dbwmsgroupid "
		"and map.dbwmsid = dbwms.dbwmsid "		
		"order by 2");

	return dataAccessService.ExecuteQuery("GetIntegratedGroupList", sql, groupList);
}

int CIntegrationDataService::GetExternalConnectionList(CStringArray &connectionList)
{
	CString sql;
	
	sql.Format("select * from dbexternalconnection");
		
	return dataAccessService.ExecuteQuery("GetExternalConnectionList", sql, connectionList);

}

int CIntegrationDataService::LoadGroupConnectionList(CWMSGroup& group)
{
	CString sql;
	CStringArray results;

	sql.Format("select gc.*, 'bla', ec.* "
		"from dbexternalconnection ec, dbwmsgroupconnection gc "
		"where gc.dbwmsgroupid = %d "
		"and gc.dbexternalconnectionid = ec.dbexternalconnectionid ", group.m_WMSGroupDBId);
	
	dataAccessService.ExecuteQuery("GetExternalConnectionList", sql, results);

	for (int i=0; i < results.GetSize(); ++i) {
		CString line;
		int idx = results[i].Find("bla|");
		line = results[i].Left(idx);
		CWMSGroupConnection *pGConn = new CWMSGroupConnection;
		pGConn->Parse(line);
		group.m_ConnectionList.Add(pGConn);
		line = results[i].Mid(idx+4);
		CExternalConnection *pEConn = new CExternalConnection;
		pEConn->Parse(line);
		pGConn->m_pExternalConnection = pEConn;
		pGConn->m_ExternalConnectionDBId = pEConn->m_ExternalConnectionDBId;
		pGConn->m_GroupName = group.m_Name;
		pGConn->m_ConnectionName = pEConn->m_Name;
		pGConn->m_WMSGroupDBId = group.m_WMSGroupDBId;
	}

	return 0;
}

int CIntegrationDataService::ProcessLocationInbound(CStringArray &locList, int &errorCount, int addCount)
{
	CThreadParameters parms;
	parms.m_pInList = &locList;
	parms.m_ReturnCode = addCount;
	CEvent event;
	parms.m_pEvent = &event;

	CWinThread *pThread = AfxBeginThread(CIntegrationDataService::ProcessLocationInboundThread, &parms);

	BOOL bThreadDone = false;
	while (TRUE) {
		CBusyWaitCursor bwc;

		if ( ! utilityHelper.PeekAndPump() )
			break;
		
		bThreadDone = parms.m_pEvent->Lock(0);
		if (bThreadDone)
			break;
	}

	errorCount = atoi(parms.m_ReturnMessage);

	return parms.m_ReturnCode;
}

UINT CIntegrationDataService::ProcessLocationInboundThread(LPVOID pParam)
{
#if 0
	CString tempString;
	int tempInt;
	CSsaStringArray tempSendArray;
	CSsaStringArray tempRecvArray;
	CString sendString;
	CString cmdText;
	CStringArray resultList;
#endif
	CThreadParameters &parm = *(CThreadParameters *)pParam;
#if 0
	sendString = "<SIO>SLOTSocketString\n";
	tempSendArray.Add(sendString);

	sendString.Format("<SAI>%d\n", parm.m_ReturnCode);		// add count
	tempSendArray.Add(sendString);

	sendString.Format("<SAI>%d\n", parm.m_EventId);
	tempSendArray.Add(sendString);

	for (int i=0; i < parm.m_pInList->GetSize(); ++i) {
		sendString.Format("<SAI>%s\n", parm.m_pInList->GetAt(i));
		tempSendArray.Add(sendString);
	}
	sendString = "<EIO>SLOTSocketString\n";
	tempSendArray.Add(sendString);
	
	forteService.SendToForteConnection(tempSendArray,tempRecvArray,
		CString("SLOTSocketString"), 10400);

	for (i=0; i < tempRecvArray.GetSize(); i++) {
		if ( tempRecvArray[i].Find("<SAI>") != -1 ) {
			tempString = tempRecvArray[i].Mid(5);
			tempInt = tempString.GetLength();
			if (tempString.GetAt(tempInt-1) == '\n' ) {
				tempString.SetAt(tempInt-1,' ');
				tempString.TrimRight();
			}
			resultList.Add(tempString);			
		}
	}
	int errorCount = 0;

	if (resultList.GetSize() >= 2) {
		errorCount = atoi(resultList[1]);
	}

	parm.m_ReturnMessage.Format("%d", errorCount);

	if (resultList[0].CompareNoCase("Success") == 0)
		parm.m_ReturnCode = 0;
	else
		parm.m_ReturnCode = 1;

#else
	CListstring* pLocArray = new CListstring;
	for (int i=0; i < parm.m_pInList->GetSize(); ++i) {
		string tmpStr = (LPCTSTR)parm.m_pInList->GetAt(i);
		pLocArray->AddTail(tmpStr);
	}

	CListstringPtr res = getSessionMgrSO()->LocationInboundHelper(parm.m_ReturnCode,
														parm.m_EventId, 
														pLocArray);
	int errorCount = 0;

	if (res->GetCount() >= 2) {
		errorCount = atoi(res->GetAt(res->FindIndex(1)).c_str());
	}

	parm.m_ReturnMessage.Format("%d", errorCount);

	if (res->GetAt(res->FindIndex(0)).compare("Success") == 0)
		parm.m_ReturnCode = 0;
	else
		parm.m_ReturnCode = 1;

#endif
	parm.m_pEvent->SetEvent();

	return parm.m_ReturnCode;
}

int CIntegrationDataService::ProcessProductInbound(CStringArray &prodList, int &errorCount, int addCount)
{
	CThreadParameters parms;

	parms.m_pInList = &prodList;
	parms.m_ReturnCode = addCount;
	CEvent event;
	parms.m_pEvent = &event;

	CWinThread *pThread = AfxBeginThread(CIntegrationDataService::ProcessProductInboundThread, &parms);

	BOOL bThreadDone = false;
	while (TRUE) {
		CBusyWaitCursor bwc;
		if ( ! utilityHelper.PeekAndPump() )
			break;
		
		bThreadDone = parms.m_pEvent->Lock(0);
		if (bThreadDone)
			break;
	}

	errorCount = atoi(parms.m_ReturnMessage);

	return parms.m_ReturnCode;
}



UINT CIntegrationDataService::ProcessProductInboundThread(LPVOID pParam)
{
#if 0
	CString tempString;
	int tempInt;
	CSsaStringArray tempSendArray;
	CSsaStringArray tempRecvArray;
	CString sendString;
	CString cmdText;
	CStringArray resultList;
#endif
	CThreadParameters &parm = *(CThreadParameters *)pParam;
#if 0
	sendString = "<SIO>SLOTSocketString\n";
	tempSendArray.Add(sendString);

	sendString.Format("<SAI>%d\n", parm.m_ReturnCode);
	tempSendArray.Add(sendString);

	sendString.Format("<SAI>%d\n", parm.m_EventId);
	tempSendArray.Add(sendString);

	for (int i=0; i < parm.m_pInList->GetSize(); ++i) {
		sendString.Format("<SAI>%s\n", parm.m_pInList->GetAt(i));
		tempSendArray.Add(sendString);
	}
	sendString = "<EIO>SLOTSocketString\n";
	tempSendArray.Add(sendString);
	
	forteService.SendToForteConnection(tempSendArray,tempRecvArray,
		CString("SLOTSocketString"), 10500);

	for (i=0; i < tempRecvArray.GetSize(); i++) {
		if ( tempRecvArray[i].Find("<SAI>") != -1 ) {
			tempString = tempRecvArray[i].Mid(5);
			tempInt = tempString.GetLength();
			if (tempString.GetAt(tempInt-1) == '\n' ) {
				tempString.SetAt(tempInt-1,' ');
				tempString.TrimRight();
			}
			resultList.Add(tempString);			
		}
	}
	
	int errorCount = 0;

	if (resultList.GetSize() >= 2) {
		errorCount = atoi(resultList[1]);
	}

	parm.m_ReturnMessage.Format("%d", errorCount);

	if (resultList[0].CompareNoCase("Success") == 0)
		parm.m_ReturnCode = 0;
	else
		parm.m_ReturnCode = 1;

#else
	CListstring* pProdArray = new CListstring;
	for (int i=0; i < parm.m_pInList->GetSize(); ++i) {
		string tmpStr = (LPCTSTR)parm.m_pInList->GetAt(i);
		pProdArray->AddTail(tmpStr);
	}

	CListstringPtr res = getSessionMgrSO()->ProductInboundHelper(parm.m_ReturnCode,
														parm.m_EventId, 
														pProdArray);
	int errorCount = 0;

	if (res->GetCount() >= 2) {
		errorCount = atoi(res->GetAt(res->FindIndex(1)).c_str());
	}

	parm.m_ReturnMessage.Format("%d", errorCount);

	if (res->GetAt(res->FindIndex(0)).compare("Success") == 0)
		parm.m_ReturnCode = 0;
	else
		parm.m_ReturnCode = 1;
#endif
	
	parm.m_pEvent->SetEvent();

	return parm.m_ReturnCode;
}


int CIntegrationDataService::ProcessAssignmentInbound(CStringArray &assgList, int &errorCount, int addCount)
{
	CThreadParameters parms;
	parms.m_pInList = &assgList;
	parms.m_ReturnCode = addCount;
	CEvent event;
	parms.m_pEvent = &event;

	CWinThread *pThread = AfxBeginThread(CIntegrationDataService::ProcessAssignmentInboundThread, &parms);

	BOOL bThreadDone = false;
	while (TRUE) {
		CBusyWaitCursor bwc;

		if ( ! utilityHelper.PeekAndPump() )
			break;
		
		bThreadDone = parms.m_pEvent->Lock(0);
		if (bThreadDone)
			break;
	}

	errorCount = atoi(parms.m_ReturnMessage);

	return parms.m_ReturnCode;
}



UINT CIntegrationDataService::ProcessAssignmentInboundThread(LPVOID pParam)
{
#if 0
	CString tempString;
	int tempInt;
	CSsaStringArray tempSendArray;
	CSsaStringArray tempRecvArray;
	CString sendString;
	CString cmdText;
	CStringArray resultList;
#endif
	CThreadParameters &parm = *(CThreadParameters *)pParam;
#if 0
	sendString = "<SIO>SLOTSocketString\n";
	tempSendArray.Add(sendString);

	sendString.Format("<SAI>%d\n", parm.m_ReturnCode);
	tempSendArray.Add(sendString);

	sendString.Format("<SAI>%d\n", parm.m_EventId);
	tempSendArray.Add(sendString);

	for (int i=0; i < parm.m_pInList->GetSize(); ++i) {
		sendString.Format("<SAI>%s\n", parm.m_pInList->GetAt(i));
		tempSendArray.Add(sendString);
	}
	sendString = "<EIO>SLOTSocketString\n";
	tempSendArray.Add(sendString);
	
	forteService.SendToForteConnection(tempSendArray,tempRecvArray,
		CString("SLOTSocketString"), 10600);

	for (i=0; i < tempRecvArray.GetSize(); i++) {
		if ( tempRecvArray[i].Find("<SAI>") != -1 ) {
			tempString = tempRecvArray[i].Mid(5);
			tempInt = tempString.GetLength();
			if (tempString.GetAt(tempInt-1) == '\n' ) {
				tempString.SetAt(tempInt-1,' ');
				tempString.TrimRight();
			}
			resultList.Add(tempString);			
		}
	}
	
	int errorCount = 0;

	if (resultList.GetSize() >= 2) {
		errorCount = atoi(resultList[1]);
	}

	parm.m_ReturnMessage.Format("%d", errorCount);

	if (resultList[0].CompareNoCase("Success") == 0)
		parm.m_ReturnCode = 0;
	else
		parm.m_ReturnCode = 1;
#else
	CListstring* pAssignmentArray = new CListstring;
	for (int i=0; i < parm.m_pInList->GetSize(); ++i) {
		string tmpStr = (LPCTSTR)parm.m_pInList->GetAt(i);
		pAssignmentArray->AddTail(tmpStr);
	}

	CListstringPtr res = getSessionMgrSO()->AssignmentInboundHelper(parm.m_ReturnCode,
														parm.m_EventId, 
														pAssignmentArray);
	int errorCount = 0;

	if (res->GetCount() >= 2) {
		errorCount = atoi(res->GetAt(res->FindIndex(1)).c_str());
	}

	parm.m_ReturnMessage.Format("%d", errorCount);

	if (res->GetAt(res->FindIndex(0)).compare("Success") == 0)
		parm.m_ReturnCode = 0;
	else
		parm.m_ReturnCode = 1;
#endif	
	parm.m_pEvent->SetEvent();

	return parm.m_ReturnCode;
}


// Get rid of duplicate outbound records or ones that have been superceded, e.g.
// an un-sent add followed by a delete does not require anything to be sent
int CIntegrationDataService::PurifyLocationOutboundQueue(int facilityId, int sectionId)
{
	CString sql;
	CStringArray stmts;
	CString temp("");

	if (sectionId > 0)
		temp.Format("and dbsectionid = %d ");

	// Close where a delete was sent after an add that has not been processed
	sql.Format("update dblocqueue set closed = 1, reasontext = 'Location Deleted' "
		"where sender = 0 and exists ( select dblocqueueid from dblocqueue q2 "
		"where sender = 0 and dblocqueue.locationkey = q2.locationkey "
		"and dblocqueue.dbfacilityid = q2.dbfacilityid "
		"and action = %d "			// add
		"and status in (%d, %d)) "	// not sent, rejected
		"and exists ( select dblocqueueid from dblocqueue q3 "
		"where sender = 0 and dblocqueue.locationkey = q3.locationkey "
		"and dblocqueue.dbfacilityid = q3.dbfacilityid "
		"and action = %d) "			// delete
		"and dbfacilityid = %d "
		"%s",
		CInterfaceHelper::Add, CInterfaceHelper::NotSent, CInterfaceHelper::Rejected,
		CInterfaceHelper::Delete, facilityId, temp);

	stmts.Add(sql);


	//close where action = modify and exists action = add and not sent or rejected
	sql.Format("update dblocqueue set closed = 1, reasontext = 'Add Pending' "
		"where sender = 0 and action = %d "
		"and exists ( select dblocqueueid from dblocqueue q2 "
		"where sender = 0 and q2.dbfacilityid = dblocqueue.dbfacilityid "
		"and q2.locationkey = dblocqueue.locationkey "
		"and action = %d "
		"and status in (%d, %d)) "
		"and dbfacilityid = %d "
		"%s ",
		CInterfaceHelper::Modify, CInterfaceHelper::Add, CInterfaceHelper::NotSent,
		CInterfaceHelper::Rejected, facilityId, temp);

	stmts.Add(sql);


	//close where action ! delete and not sent or rejected and exists action = delete
	sql.Format("update dblocqueue set closed = 1, reasontext = 'Location Deleted' "
		"where sender = 0 and action in (%d, %d) "			// delete
		"and status in (%d, %d) "		// not sent, rejected
		"and exists ( select dblocqueueid from dblocqueue q2 "
		"where sender = 0 and dblocqueue.locationkey = q2.locationkey "
		"and dblocqueue.dbfacilityid = q2.dbfacilityid "
		"and action = %d) "			// delete
		"and dbfacilityid = %d "
		"%s ",
		CInterfaceHelper::Add, CInterfaceHelper::Modify, CInterfaceHelper::NotSent, CInterfaceHelper::Rejected,
		CInterfaceHelper::Delete, facilityId, temp);

	stmts.Add(sql);


	//close all but oldest where action = modify and exists action = modify and not sent or rejected
	sql.Format("update dblocqueue set closed = 1, reasontext = 'Modify Pending' "
		"where sender = 0 "
		"and action = %d "		// modify
		"and status in (%d, %d) "
		"and 1 < ( select count(*) from dblocqueue q2 "
		"where sender = 0 and q2.dbfacilityid = dblocqueue.dbfacilityid "
		"and q2.locationkey = dblocqueue.locationkey "
		"and q2.action = %d) "		// modify
		"and dbfacilityid = %d "
		"%s "
		"and dblocqueueid != (select min(dblocqueueid) "
		"from dblocqueue q3 "
		"where sender = 0 and q3.dbfacilityid = dblocqueue.dbfacilityid "
		"and q3.locationkey = dblocqueue.locationkey "
		"and action = %d) ",
		CInterfaceHelper::Modify, CInterfaceHelper::NotSent, CInterfaceHelper::Rejected, 
		CInterfaceHelper::Modify, facilityId, temp, CInterfaceHelper::Modify);
	stmts.Add(sql);

	//close all where section does not exist in wmsmap table (wms mapping was deleted)
	if (sectionId > 0)
		temp.Format("and q2.dbsectionid = %d ", sectionId);

	sql.Format("update dblocqueue set closed = 1, reasontext = 'Connection Deleted' "
		"where sender = 0 and dblocqueueid in ( "
		"select dblocqueueid "
		"from dblocqueue q2 "
		"where sender = 0 and q2.dbfacilityid = %d "
		"%s "
		"and not exists ( select dbwmsexportmapid from dbwmsexportmap "
		"where dbwmsexportmap.dbfacilityid = dblocqueue.dbfacilityid "
		"and (dbsectionid = dblocqueue.dbsectionid or dbsectionid = 0)))",
		facilityId, temp);
	stmts.Add(sql);

	return dataAccessService.ExecuteStatements("PurifyLocationOutboundQueue", stmts);

}

int CIntegrationDataService::GetLocationOutboundData(CWMS *pWMS, CWMSMap *pMap, CStringArray &locList)
{

	CString sql, sectionClause("");
	CStringArray results, strings;

	if (pMap->m_SectionDBId > 0)
		sectionClause.Format("and q.dbsectionid = %d", pMap->m_SectionDBId);

	// Get the actual location data
	sql.Format("select q.action, q.batchid, q.linenumber, "	// 2
		"dblocationid, l.description, l.handlingmethod, l.isselect, l.width, "			// 7
		"l.depth, l.height, l.maxweight, l.xcoordinate, l.ycoordinate, l.zcoordinate, l.isoverridden, " // 14
		"l.status, l.isactive, l.clearance, backfillid, backfillxcoordinate, backfillycoordinate, " // 20
		"backfillzcoordinate, stockerid, stockerxcoordinate, stockerycoordinate, stockerzcoordinate, " // 25
		"l.trace, selectionsequence, replenishmentsequence, l.locationkey, lop.handlingmethod, lop.isselect," //31
		"lp.relativelevel, lp.baytype, lp.clearance, lp.selectpositions, lp.reservepositions, " // 36
		"lp.reservepositionheight, " // 37
		"bp.maximumweight, bp.isfloating, lp.dblevelprofileid "	// 40
		"from dblocqueue q, dblocation l, dblocationprof lop, dblevelprofile lp, dbbayprofile bp "
		"where q.locationkey = l.locationkey "
		"and q.dbfacilityid = %d "
		"%s "
		"and lop.dblocationprofid = l.dblocationprofid "
		"and lop.dblevelprofileid = lp.dblevelprofileid "
		"and lp.dbbayprofileid = bp.dbbayprofileid "
		"and q.status in (%d, %d) "
		"and q.closed = 0 "
		"and q.action in (%d, %d) "
		"union "	// deletes
		"select q.action, q.batchid, q.linenumber, "	// 2
		"0, ' ', 0, 0, 0, "			// 7
		"0, 0, 0, 0, 0, 0, 0, " // 14
		"0, 0, 0, ' ', 0, 0, " // 20
		"0, ' ', 0, 0, 0, " // 25
		"0, ' ',' ', locationkey, 0, 0," //31
		"0, 0, 0, 0, 0, " // 36
		"0, " // 37
		"0, 0, 0 "	// 40
		"from dblocqueue q "
		"where q.dbfacilityid = %d "
		"%s "
		"and q.status in (%d, %d) "
		"and q.closed = 0 "
		"and q.action = %d "

		"order by 2, 3 ",
		pMap->m_FacilityDBId, sectionClause, CInterfaceHelper::NotSent, CInterfaceHelper::Rejected,
		CInterfaceHelper::Add, CInterfaceHelper::Modify,
		pMap->m_FacilityDBId, sectionClause, CInterfaceHelper::NotSent, CInterfaceHelper::Rejected,
		CInterfaceHelper::Delete);

	results.RemoveAll();
	return dataAccessService.ExecuteQuery("GetLocationOutboundData", sql, locList);

}


int CIntegrationDataService::GetAllLocationOutboundData(int facilityDBId, int sectionDBId, CStringArray &locList)
{

	CString sql, sectionClause("");
	CStringArray results, strings;

	if (sectionDBId > 0)
		sectionClause.Format("and se.dbsectionid = %d", sectionDBId);

	// Get the actual location data
	// Oracle-specific

	sql.Format("select %d, 1, rownum, "	// 2
		"dblocationid, l.description, l.handlingmethod, l.isselect, l.width, "			// 7
		"lp.stackdepth, l.height, l.maxweight, l.xcoordinate, l.ycoordinate, l.zcoordinate, l.isoverridden, " // 14
		"l.status, l.isactive, l.clearance, l.backfillid, l.backfillxcoordinate, l.backfillycoordinate, " // 20
		"l.backfillzcoordinate, l.stockerid, l.stockerxcoordinate, l.stockerycoordinate, l.stockerzcoordinate, " // 25
		"l.trace, selectionsequence, replenishmentsequence, l.locationkey, lop.handlingmethod, lop.isselect," //31
		"lp.relativelevel, lp.baytype, lp.clearance, lp.selectpositions, lp.reservepositions, " // 36
		"lp.reservepositionheight, " // 37
		"bp.maximumweight, bp.isfloating, lp.dblevelprofileid "	// 40
		"from dblocation l, dblocationprof lop, dblevelprofile lp, dbbayprofile bp, "
		"dblevel le, dbbay b, dbside si, dbaisle a, dbsection se "
		"where lop.dblocationprofid = l.dblocationprofid "
		"and lop.dblevelprofileid = lp.dblevelprofileid "
		"and lp.dbbayprofileid = bp.dbbayprofileid "
		"and l.dblevelid = le.dblevelid "
		"and le.dbbayid = b.dbbayid "
		"and b.dbsideid = si.dbsideid "
		"and si.dbaisleid = a.dbaisleid "
		"and a.dbsectionid = se.dbsectionid "
		"and se.dbfacilityid = %d "
		"%s "
		"order by 2, 3 ",
		CInterfaceHelper::Add, facilityDBId, sectionClause);

	results.RemoveAll();
	return dataAccessService.ExecuteQuery("GetAllLocationOutboundData", sql, locList);

}


int CIntegrationDataService::UpdateBatchForLocationOutbound(CWMSMap *pMap)
{
	CString sql, sectionClause("");

	int batchId = dataAccessService.GetNextKey("DBInterfaceBatch", 1);

	if (pMap->m_SectionDBId > 0)
		sectionClause.Format("and q.dbsectionid = %d", pMap->m_SectionDBId);

	// Oracle-specific
	// Update the batch and line number for unsent records
	sql.Format("update dblocqueue q set batchid = %d, linenumber = rownum "
		"where q.dbfacilityid = %d "
		"%s "
		"and q.status = %d "
		"and q.closed = 0 "
		"and q.batchid = 0 ",
		batchId, pMap->m_FacilityDBId, sectionClause, CInterfaceHelper::NotSent);
	
	return dataAccessService.ExecuteStatement("UpdateLocQueue", sql);
}


int CIntegrationDataService::GetExternalInfo(int externalSystemDBId, CWMSMap *pMap,
											 CMapStringToString &defaultInfoMap,
											 CMapStringToString &levelProfileInfoMap, 
											 CMapStringToString &locationInfoMap)
{
	CString sql;
	CStringArray results, strings;
	CString sectionClause("");

	// Get the default values for all of the external attributes
	sql.Format("select name, defaultvalue "
		"from dbexternalinfo "
		"where dbexternalinfo.dbexternalsystemid = %d ",
		externalSystemDBId);
	
	results.RemoveAll();
	dataAccessService.ExecuteQuery("GetExternalInfo", sql, results);
	for (int i=0; i < results.GetSize(); ++i) {
		strings.RemoveAll();
		utilityHelper.ParseString(results[i], "|", strings);
		defaultInfoMap.SetAt(strings[0], strings[1]);
	}


	// Get the default values for the level profiles
	sql.Format("select dblevelprofileid, name, dblevelprofileinfo.value "
		"from dblevelprofileinfo, dbexternalinfo "
		"where dblevelprofileinfo.dbexternalinfoid = dbexternalinfo.dbexternalinfoid "
		"and dbexternalinfo.dbexternalsystemid = %d", 
		externalSystemDBId);

	results.RemoveAll();
	dataAccessService.ExecuteQuery("GetLevelProfileExternalInfo", sql, results);
	for (i=0; i < results.GetSize(); ++i) {
		strings.RemoveAll();
		utilityHelper.ParseString(results[i], "|", strings);
		CString temp;
		temp.Format("%s-%s", strings[0], strings[1]);
		levelProfileInfoMap.SetAt(temp, strings[2]);
	}

	if (pMap->m_SectionDBId > 0)
		sectionClause.Format("and q.dbsectionid = %d", pMap->m_SectionDBId);


	// Get the overridden location external attributes
	sql.Format("select l.dblocationid, name, value "
		"from dblocqueue q, dblocationinfo, dbexternalinfo, dblocation l "
		"where q.locationkey = l.locationkey "
		"and q.dbfacilityid = %d "
		"%s "
		"and q.status in (%d, %d) "
		"and q.closed = 0 "
		"and dblocationinfo.dblocationid = l.dblocationid "
		"and dblocationinfo.dbexternalinfoid = dbexternalinfo.dbexternalinfoid "
		"and dbexternalinfo.dbexternalsystemid = %d ",
		pMap->m_FacilityDBId, sectionClause, CInterfaceHelper::NotSent, CInterfaceHelper::Rejected,
		externalSystemDBId);

	results.RemoveAll();
	dataAccessService.ExecuteQuery("GetLocationOutboundInfo", sql, results);

	for (i=0; i < results.GetSize(); ++i) {
		strings.RemoveAll();
		utilityHelper.ParseString(results[i], "|", strings);
		CString temp;
		temp.Format("%s-%s", strings[0], strings[1]);
		locationInfoMap.SetAt(temp, strings[2]);
	}

	return 0;
}

int CIntegrationDataService::GetAllExternalInfo(int externalSystemDBId, int facilityDBId, int sectionDBId,
											 CMapStringToString &defaultInfoMap,
											 CMapStringToString &levelProfileInfoMap, 
											 CMapStringToString &locationInfoMap)
{
	CString sql;
	CStringArray results, strings;
	CString sectionClause("");

	// Get the default values for all of the external attributes
	sql.Format("select name, defaultvalue "
		"from dbexternalinfo "
		"where dbexternalinfo.dbexternalsystemid = %d ", externalSystemDBId);
	
	results.RemoveAll();
	dataAccessService.ExecuteQuery("GetExternalInfo", sql, results);
	for (int i=0; i < results.GetSize(); ++i) {
		strings.RemoveAll();
		utilityHelper.ParseString(results[i], "|", strings);
		defaultInfoMap.SetAt(strings[0], strings[1]);
	}


	// Get the default values for the level profiles
	sql.Format("select dblevelprofileid, name, dblevelprofileinfo.value "
		"from dblevelprofileinfo, dbexternalinfo "
		"where dblevelprofileinfo.dbexternalinfoid = dbexternalinfo.dbexternalinfoid "
		"and dbexternalinfo.dbexternalsystemid = %d", 
		externalSystemDBId);

	results.RemoveAll();
	dataAccessService.ExecuteQuery("GetLevelProfileExternalInfo", sql, results);
	for (i=0; i < results.GetSize(); ++i) {
		strings.RemoveAll();
		utilityHelper.ParseString(results[i], "|", strings);
		CString temp;
		temp.Format("%s-%s", strings[0], strings[1]);
		levelProfileInfoMap.SetAt(temp, strings[2]);
	}

	if (sectionDBId > 0)
		sectionClause.Format("and se.dbsectionid = %d", sectionDBId);


	// Get the overridden location external attributes
	sql.Format("select l.dblocationid, name, value "
		"from dblocationinfo, dbexternalinfo, dblocation l, "
		"dblevel le, dbbay b, dbside si, dbaisle a, dbsection se "
		"where l.dblevelid = le.dblevelid "
		"and le.dbbayid = b.dbbayid "
		"and b.dbsideid = si.dbsideid "
		"and si.dbaisleid = a.dbaisleid "
		"and a.dbsectionid = se.dbsectionid "
		"and se.dbfacilityid = %d "
		"%s "
		"and dblocationinfo.dblocationid = l.dblocationid "
		"and dblocationinfo.dbexternalinfoid = dbexternalinfo.dbexternalinfoid "
		"and dbexternalinfo.dbexternalsystemid = %d ",
		facilityDBId, sectionClause, externalSystemDBId);

	results.RemoveAll();
	dataAccessService.ExecuteQuery("GetAllLocationOutboundInfo", sql, results);

	for (i=0; i < results.GetSize(); ++i) {
		strings.RemoveAll();
		utilityHelper.ParseString(results[i], "|", strings);
		CString temp;
		temp.Format("%s-%s", strings[0], strings[1]);
		locationInfoMap.SetAt(temp, strings[2]);
	}

	return 0;
}

int CIntegrationDataService::UpdateStatusForLocationOutbound(CWMSMap *pMap, BOOL autoConfirm, BOOL fullExport)
{	
	CString sql, sectionClause(""), sectionClause2;
	CStringArray stmts;

	if (pMap->m_SectionDBId > 0) {
		sectionClause.Format("and q.dbsectionid = %d ", pMap->m_SectionDBId);
		sectionClause2.Format("and se.dbsectionid = %d ", pMap->m_SectionDBId);
	}


	// Oracle-specific
	
	if (autoConfirm) {
		
		// Auto-confirm and full export means set everything to integrated
		if (fullExport) {
			sql.Format("update dblocation set status = %d "
				"where exists ( select dblocationid "
				"from dblocation l, dblevel le, dbbay b, dbside si, dbaisle a, dbsection se "
				"where l.dblevelid = le.dblevelid "
				"and le.dbbayid = b.dbbayid "
				"and b.dbsideid = si.dbsideid "
				"and si.dbaisleid = a.dbaisleid "
				"and a.dbsectionid = se.dbsectionid "
				"and se.dbfacilityid = %d "
				"%s "
				"and l.dblocationid = dblocation.dblocationid)",
				CLocation::Integrated,
				pMap->m_FacilityDBId, sectionClause2);
			stmts.Add(sql);
		}
		
		else {

			// Set only current outbound items to integrated

			sql.Format("update dblocation set status = %d "
				"where exists ( select dblocationid "
				"from dblocation l, dblocqueue q, dblevel le, dbbay b, dbside si, dbaisle a, dbsection se "
				"where l.dblevelid = le.dblevelid "
				"and le.dbbayid = b.dbbayid "
				"and b.dbsideid = si.dbsideid "
				"and si.dbaisleid = a.dbaisleid "
				"and a.dbsectionid = se.dbsectionid "
				"and se.dbfacilityid = %d "
				"%s "
				"and q.locationkey = l.locationkey "
				"and q.dbfacilityid = se.dbfacilityid "
				"and q.status in (%d, %d) "
				"and q.closed = 0 "
				"and l.dblocationid = dblocation.dblocationid)",
				CLocation::Integrated,
				pMap->m_FacilityDBId, sectionClause2, CInterfaceHelper::NotSent, CInterfaceHelper::Rejected);
			stmts.Add(sql);
			
			// Update the queue status to confirmed

			sql.Format("update dblocqueue q set status = %d, closed = 1, sendcount = sendcount + 1, "
				"LastDate = SYSDATE, ReasonText = 'Auto-Confirm' "
				"where q.status in (%d, %d) "
				"and q.closed = 0 "
				"and q.dbfacilityid = %d "
				"%s ",
				CInterfaceHelper::Confirmed, 
				CInterfaceHelper::NotSent, CInterfaceHelper::Rejected, pMap->m_FacilityDBId,
				sectionClause);
			stmts.Add(sql);
		}

	}
	else {
		
		// No auto-confirm, set location status to pending

		sql.Format("update dblocation set status = %d "
			"where exists ( select dblocationid "
			"from dblocqueue q, dblocation l, dblevel le, dbbay b, dbside si, dbaisle a, dbsection se "
			"where l.dblevelid = le.dblevelid "
			"and le.dbbayid = b.dbbayid "
			"and b.dbsideid = si.dbsideid "
			"and si.dbaisleid = a.dbaisleid "
			"and a.dbsectionid = se.dbsectionid "
			"and se.dbfacilityid = %d "
			"%s "
			"and q.locationkey = l.locationkey "
			"and q.dbfacilityid = se.dbfacilityid "
			"and q.status in (%d, %d) "
			"and q.closed = 0 "
			"and l.dblocationid = dblocation.dblocationid "
			"and l.status != %d)",
			CLocation::IntegrationPending,
			pMap->m_FacilityDBId, sectionClause2, CInterfaceHelper::NotSent, CInterfaceHelper::Rejected,
			CLocation::Integrated);
		stmts.Add(sql);

		// Set queue status to sent

		sql.Format("update dblocqueue q set status = %d, sendcount = sendcount + 1, "
			"LastDate = SYSDATE "
			"where q.status in (%d, %d) "
			"and q.closed = 0 "
			"and q.dbfacilityid = %d "
			"%s ",
			CInterfaceHelper::Sent, 
			CInterfaceHelper::NotSent, CInterfaceHelper::Rejected, pMap->m_FacilityDBId,
			sectionClause);
		stmts.Add(sql);
	}

	return dataAccessService.ExecuteStatements("UpdateStatusForLocationOutbound", stmts);
}


int CIntegrationDataService::StoreExternalConnection(CExternalConnection &connection)
{
	CString sql;

	if (connection.m_ExternalConnectionDBId > 0) {	// update
		sql.Format("update dbexternalconnection "
			"set name = '%s', connectiontype = %d, "
			"host = '%s', port = %d, queuemanager = '%s', login = '%s',"
			"password = '%s', datapath = '%s', dataname = '%s', triggername = '%s',"
			"channel = '%s' "
			"where dbexternalconnectionid = %d",
			connection.m_Name,
			connection.m_ConnectionType,
			utilityHelper.NonNull(connection.m_Host),
			connection.m_Port,
			utilityHelper.NonNull(connection.m_QueueManager),
			utilityHelper.NonNull(connection.m_Login),
			utilityHelper.NonNull(connection.m_Password),
			utilityHelper.NonNull(connection.m_Path),
			utilityHelper.NonNull(connection.m_FileName),
			utilityHelper.NonNull(connection.m_TriggerName),
			utilityHelper.NonNull(connection.m_Channel),
			connection.m_ExternalConnectionDBId);
	}
	else {		// insert
		connection.m_ExternalConnectionDBId = dataAccessService.GetNextKey("DBExternalConnection", 1);

		sql.Format("insert into dbexternalconnection (DBExternalConnectionId, Name, ConnectionType,"
			"Host, Port, QueueManager, Login, Password, DataPath, DataName, TriggerName,"
			"Channel) values (%d, '%s', %d, '%s', %d, '%s', '%s', '%s', '%s', '%s', '%s', '%s')",
			connection.m_ExternalConnectionDBId,
			connection.m_Name,
			connection.m_ConnectionType,
			utilityHelper.NonNull(connection.m_Host),
			connection.m_Port,
			utilityHelper.NonNull(connection.m_QueueManager),
			utilityHelper.NonNull(connection.m_Login),
			utilityHelper.NonNull(connection.m_Password),
			utilityHelper.NonNull(connection.m_Path),
			utilityHelper.NonNull(connection.m_FileName),
			utilityHelper.NonNull(connection.m_TriggerName),
			utilityHelper.NonNull(connection.m_Channel));
	}

	return dataAccessService.ExecuteStatement("StoreExternalConnection", sql);
}

int CIntegrationDataService::DeleteExternalConnection(int dbid)
{
	CString sql;
	CStringArray stmts;

	sql.Format("delete from dbwmsgroupconnection where dbexternalconnectionid = %d", dbid);
	stmts.Add(sql);
	sql.Format("delete from dbexternalconnection where dbexternalconnectionid = %d", dbid);
	stmts.Add(sql);

	return dataAccessService.ExecuteStatements("DeleteExternalConnection", stmts);

}


int CIntegrationDataService::GetAllAssignmentOutboundData(int facilityDBId, int batchId, CStringArray &assgList,
														  BOOL integratedOnly)
{

	// Chain|Sequence|ProductKey|WMSProductId|WMSProductDetailId|FromLocationKey|ToLocationKey|
	// IsFromTemp|IsToTemp|IsAddFacing|IsDeleteFacing|SearchAnchorPoint|Cases

	CString sql;
	CString locClause, prodClause;
	
	if (integratedOnly) {
		locClause.Format("and l.status = %d ", CLocation::Integrated);
		prodClause.Format("and pp.status = %d ", CProductPack::Integrated);
	}
	// Oracle-specific
	sql.Format("select %d, rownum, pp.externalkey, pp.wmsproductid, pp.wmsproductdetailid, "
		"0, l.locationkey, %d, 0, 0, 0, l.description, ss.casequantity, bp.isfloating, "
		"0 fromsectionid, a.dbsectionid tosectionid "
		"from dbproductpackf pp, dbslotsolution ss, dblocation l, "
		"dblocationprof lop, dblevelprofile lp, dbbayprofile bp, "
		"dblevel le, dbbay b, dbside si, dbaisle a "
		"where pp.dbfacilityid = %d "
		"and pp.dbproductpackid = ss.dbproductpackid "
		"and ss.origin = %d "
		"and ss.dblocationid = l.dblocationid "
		"and l.dblocationprofid = lop.dblocationprofid "
		"and lop.dblevelprofileid = lp.dblevelprofileid "
		"and lp.dbbayprofileid = bp.dbbayprofileid "
		"and le.dblevelid = l.dblevelid "
		"and b.dbbayid = le.dbbayid "
		"and si.dbsideid = b.dbsideid "
		"and a.dbaisleid = si.dbaisleid "
		"%s"
		"%s",
		batchId, CMove::addFacing, facilityDBId, CSolution::Optimize, 
		locClause, prodClause);

	return dataAccessService.ExecuteQuery("GetAllAssignmentOutboundData", sql, assgList);
}


int CIntegrationDataService::GetAssignmentDataByChain(int facilityDBId, const CString &chainList,
													  CStringArray &assgList)
{
	CString sql;

	// todo: add section ids
	// Normal moves
	sql.Format("select chain, chainsequence, pp.externalkey, pp.wmsproductid, pp.wmsproductdetailid, "
		"fl.locationkey, tl.locationkey, movetype, movetype, 0, 0, "
		"tl.description, m.casequantity, bp.isfloating, fa.dbsectionid, ta.dbsectionid "
		"from dbproductpackf pp, dbmove m, dblocation fl, dblocation tl, "
		"dblevel tle, dbbay tb, dbbayprofile bp, "
		"dbside tsi, dbaisle ta, "
		"dblevel fle, dbbay fb, dbside fsi, dbaisle fa "
		"where pp.dbfacilityid = %d "
		"and m.dbproductpackid = pp.dbproductpackid "
		"and chain in (%s) "
		"and fl.dblocationid = m.fromlocationid "
		"and tl.dblocationid = m.tolocationid "
		"and tl.dblevelid = tle.dblevelid "
		"and tle.dbbayid = tb.dbbayid "
		"and bp.dbbayprofileid = tb.dbbayprofileid "
		"and tsi.dbsideid = tb.dbsideid "
		"and ta.dbaisleid = tsi.dbaisleid "
		"and fl.dblevelid = fle.dblevelid "
		"and fle.dbbayid = fb.dbbayid "
		"and fb.dbsideid = fsi.dbsideid "
		"and fsi.dbaisleid = fa.dbaisleid "

		"union "

		// Add Facing (from loc is 0)
		"select chain, chainsequence, pp.externalkey, pp.wmsproductid, pp.wmsproductdetailid, "
		"0, tl.locationkey, movetype, movetype, 1, 0, "			// add facing
		"tl.description, m.casequantity, bp.isfloating, 0, ta.dbsectionid "
		"from dbproductpackf pp, dbmove m, dblocation tl, "
		"dblocationprof lop, dblevelprofile lp, dbbayprofile bp, "
		"dblevel tle, dbbay tb, dbside tsi, dbaisle ta "
		"where pp.dbfacilityid = %d "
		"and m.dbproductpackid = pp.dbproductpackid "
		"and chain in (%s) "
		"and m.fromlocationid <= 0 "
		"and tl.dblocationid = m.tolocationid "
		"and tl.dblevelid = tle.dblevelid "
		"and tle.dbbayid = tb.dbbayid "
		"and bp.dbbayprofileid = tb.dbbayprofileid "
		"and tsi.dbsideid = tb.dbsideid "
		"and ta.dbaisleid = tsi.dbaisleid "

		"union "
		
		// Delete Facing (to loc is 0)
		"select chain, chainsequence, pp.externalkey, pp.wmsproductid, pp.wmsproductdetailid, "
		"fl.locationkey, 0, movetype, movetype, 0, 1, "		// delete facing
		"'' searchanchor, 0 casequantity, 0 isfloating, fa.dbsectionid, 0 "
		"from dbproductpackf pp, dbmove m, dblocation fl, "
		"dblevel fle, dbbay fb, dbside fsi, dbaisle fa "
		"where pp.dbfacilityid = %d "
		"and m.dbproductpackid = pp.dbproductpackid "
		"and chain in (%s) "
		"and m.tolocationid <= 0 "
		"and fl.dblocationid = m.fromlocationid "
		"and fl.dblevelid = fle.dblevelid "
		"and fle.dbbayid = fb.dbbayid "
		"and fb.dbsideid = fsi.dbsideid "
		"and fsi.dbaisleid = fa.dbaisleid "

		"order by 1, 2",

		facilityDBId, chainList, facilityDBId, chainList,facilityDBId, chainList);

	return dataAccessService.ExecuteQuery("GetAssignmentDataByChain", sql, assgList);

}

int CIntegrationDataService::UpdateAssignmentOutboundStatus(const CWMSMap *pMap, const CString &chainList,
															BOOL autoConfirm, BOOL fullExport)
{
	// todo: implement this
	// if full export and auto-confirm, copy all optimize solutions to baseline
	// if not full export and auto-confirm, set all move chains to confirmed; apply moves to baseline
	// if full export and not auto, nothing else to do
	// if not full export and not auto, set move chains to sent

	if (fullExport) {
		if (autoConfirm)
			CopyOptimizedSolutionsToBaseline(pMap->m_FacilityDBId);
	}
	else {
		if (autoConfirm) {
			if (ApplyMovesToBaseline(pMap->m_FacilityDBId, chainList) < 0)
				return -1;
			UpdateMoveStatus(pMap->m_FacilityDBId, chainList, CInterfaceHelper::Confirmed);
		}
		else
			UpdateMoveStatus(pMap->m_FacilityDBId, chainList, CInterfaceHelper::Sent);
	}

	return 0;
}

int CIntegrationDataService::GetSearchAnchorList(int facilityDBId, CStringArray &results)
{
	CString sql;

	sql.Format("select startinglocation, endinglocation, searchanchorpoint "
		"from dbsearchanchor "
		"where dbfacilityid = %d", facilityDBId);

	return dataAccessService.ExecuteQuery("GetSearchAnchorList", sql, results);

}

int CIntegrationDataService::CopyOptimizedSolutionsToBaseline(int facilityId)
{
	CString sql;
	int nextKey, cnt;

	sql.Format("select count(*) from dbslotsolutionf "
		"where dbfacilityid = %d "
		"and origin = %d ", facilityId, CSolution::Optimize);
	CStringArray results;

	dataAccessService.ExecuteQuery("CountOptimizeSolutions", sql, results, TRUE);
	
	if (results.GetSize() == 0)
		return -1;

	cnt = atoi(results[0]);

	nextKey = dataAccessService.GetNextKey("DBSlotSolution", cnt);
	
	CStringArray stmts;

	sql.Format("delete from dbslotsolutionf "
		"where dbfacilityid = %d "
		"and origin = %d ", facilityId, CSolution::Baseline);
	stmts.Add(sql);

	// Oracle-specific
	sql.Format("insert into dbslotsolution "
		"select %d+rownum-1, casequantity, numberptwys,  "
		"numberinpallet, ptwytravelhours, ptwyhandlinghours, ptwycost, numberrplns,  "
		"rplndistance, rplnhours, rplnhandlinghours, rplncost, brokenorderdist,  "
		"brokenordercount, pickdistance, numberorders, selectdistance, selectscale,  "
		"selecttravelhours, selecthandlehours, totaltime, casesperhour, selectcost,  "
		"totalcost, rotatedwidth, rotatedlength, rotatedheight, forkdistance, isprimary,  "
		"stockerhandlehours, forkhandlehours, totalstockercases, totalpalletselects,  "
		"stockercost, forkcost, seltimesmovement, stkrtimesmovement, forktimesmovement,  "
		"sysdate, sysdate, 1, dblocationid, dbproductpackid, %d "
		"from dbslotsolutionf "
		"where dbfacilityid = %d "
		"and origin = %d ",
		nextKey, CSolution::Baseline, facilityId, CSolution::Optimize);

	stmts.Add(sql);

	return dataAccessService.ExecuteStatements("CopyOptimizedSolutionsToBaseline", stmts);

}

int CIntegrationDataService::ApplyMovesToBaseline(int facilityId, const CString &chainList)
{
	// select product, fromloc, toloc, movetype where chain in chainlist
	// for each
	/*
		if move from temp or move to temp continue

		if fromloc != 0
			delete from solution where loc = fromloc and prod = prod and baseline

		if toloc != 0
			if fromloc == 0	// add facing
				insert into solution loc, prod, casecap, tototalcost, non-primary, baseline
			else
				insert into solution loc, prod, casecap, totalcost, primary, baseline
		else	// delete facing
			delete from solution where loc = toloc and prod = prod and baseline
	*/

	CString sql;
	CStringArray deleteStmts, insStmts;

	sql.Format("select dbproductpackid, fromlocationid, tolocationid, movetype, casequantity "
		"from dbmovef "
		"where dbfacilityid = %d "
		"and chain in (%s)", facilityId, chainList);

	CStringArray results;
	dataAccessService.ExecuteQuery("ApplyMovesToBaseline-GetMoves", sql, results);
	
	CStringArray strings;
	for (int i=0; i < results.GetSize(); ++i) {
		utilityHelper.ParseString(results[i], "|", strings);

		int productId = atoi(strings[0]);
		int fromLocId = atoi(strings[1]);
		int toLocId = atoi(strings[2]);
		int moveType = atoi(strings[3]);
		int caseQuantity = atoi(strings[4]);

		if (fromLocId != 0) {
			sql.Format("delete from dbslotsolution "
				"where dblocationid = %d "
				"and dbproductpackid = %d "
				"and origin = %d", fromLocId, productId, CSolution::Baseline);
			deleteStmts.Add(sql);
		}
		
		if (toLocId == 0)
			continue;			// must be a delete facing
		
		int isPrimary;
		
		if (moveType == CMove::addFacing)
			isPrimary = 0;
		else if (moveType == CMove::normalMove)
			isPrimary = 1;
		else
			continue;		// must be delete facing or temp move
		
		sql.Format("insert into dbslotsolution "
			"(dbslotsolutionid, casequantity, numberptwys,  "
			"numberinpallet, ptwytravelhours, ptwyhandlinghours, ptwycost, numberrplns,  "
			"rplndistance, rplnhours, rplnhandlinghours, rplncost, brokenorderdist,  "
			"brokenordercount, pickdistance, numberorders, selectdistance, selectscale,  "
			"selecttravelhours, selecthandlehours, totaltime, casesperhour, selectcost,  "
			"totalcost, rotatedwidth, rotatedlength, rotatedheight, forkdistance, isprimary,  "
			"stockerhandlehours, forkhandlehours, totalstockercases, totalpalletselects,  "
			"stockercost, forkcost, seltimesmovement, stkrtimesmovement, forktimesmovement,  "
			"sysdate, sysdate, 1, dblocationid, dbproductpackid, origin) "
			"values "
			"(%KEY%, %d, 0, "
			"0, 0, 0, 0, 0,"
			"0, 0, 0, 0, 0,  "
			"0, 0, 0, 0, 0, "
			"0, 0, 0, 0, 0, "
			"0, 0, 0, 0, 0, %d, "
			"0, 0, 0, 0, "
			"0, 0, 0, 0, 0, "
			"sysdate, sysdate, 1, %d, %d, %d)",
			caseQuantity, isPrimary, toLocId, productId, CSolution::Baseline);
		insStmts.Add(sql);
	}

	int nextKey = dataAccessService.GetNextKey("DBSlotSolution", insStmts.GetSize());
	CString nextKeyStr;
	for (i=0; i < insStmts.GetSize(); ++i) {
		nextKeyStr.Format("%d", nextKey++);
		insStmts[i].Replace("%KEY%", nextKeyStr);
	}

	deleteStmts.Append(insStmts);

	return dataAccessService.ExecuteStatements("ApplyMovesToBaseline", deleteStmts);
}

int CIntegrationDataService::UpdateMoveStatus(int facilityId, const CString &chainList, int status)
{
	CString sql;

	// todo:  split list in case they exceed Oracle max
	sql.Format("update dbmovef set status = %d "
		"where chain in (%s) "
		"and dbfacilityid = %d ", status, chainList, facilityId);

	return dataAccessService.ExecuteStatement("UpdateMoveStatus", sql);
}

int CIntegrationDataService::ProcessLocationConfirmation(CStringArray &locConfList)
{
	CStringArray strings;
	
	for (int i=0; i < locConfList.GetSize(); ++i) {
		strings.RemoveAll();
		utilityHelper.ParseString(locConfList[i], "|", strings);
		int batchId = atoi(strings[0]);
		int reasonCode = atoi(strings[1]);
		CString reasonText = strings[2];

		int status;
		if (reasonCode < 0)
			status = CInterfaceHelper::Rejected;
		else
			status = CInterfaceHelper::Confirmed;

		// Update dblocqueue for batch
		// Update dblocation for batch
		
		CString sql;
		CStringArray stmts;

		sql.Format("update dblocqueue set reasoncode = %d, reasonText = '%s', lastdate = SYSDATE, "
			"status = %d "
			"where batchid = %d", reasonCode, reasonText, status, batchId);
		stmts.Add(sql);

		if (status == CInterfaceHelper::Confirmed) {
			sql.Format("update dblocation set status = %d "
				"where dblocationid in ( select dblocationid "
				"from dblocationf l, dblocqueue q "
				"where l.dbfacilityid = q.dbfacilityid "
				"and l.locationkey = q.locationkey "
				"and q.batchid = %d) ",
				CLocation::Integrated, batchId);
			stmts.Add(sql);
		}

		dataAccessService.ExecuteStatements("ConfirmLocation", stmts);

	}
			
	return 0;
}

UINT CIntegrationDataService::ProcessLocationConfirmationThread(LPVOID pParam)
{

	return 0;
}


int CIntegrationDataService::ProcessAssignmentConfirmation(CStringArray &assgConfList)
{
	CStringArray strings;
	
	for (int i=0; i < assgConfList.GetSize(); ++i) {
		strings.RemoveAll();
		utilityHelper.ParseString(assgConfList[i], "|", strings);
		int batchId = atoi(strings[0]);
		int reasonCode = atoi(strings[1]);
		CString reasonText = strings[2];

		int status;
		if (reasonCode < 0)
			status = CInterfaceHelper::Rejected;
		else
			status = CInterfaceHelper::Confirmed;

		// Update dbassignmentqueue for batch
		// Update dbmove for batch
		// Apply moves to baseline
		
		CString sql;
		CStringArray stmts;

		sql.Format("select dbfacilityid from dbassignmentqueue "
			"where batchid = %d ", batchId);
		CStringArray results;
		dataAccessService.ExecuteQuery("GetAssignmentQueueFacility", sql, results, TRUE);
		if (results.GetSize() == 0)
			continue;
		int facilityId = atoi(results[0]);

		sql.Format("update dbassignmentqueue set reasoncode = %d, reasonText = '%s', lastdate = SYSDATE, "
			"status = %d "
			"where batchid = %d", reasonCode, reasonText, status, batchId);
		stmts.Add(sql);

		if (status == CInterfaceHelper::Confirmed) {
			sql.Format("update dbmovef set status = %d "
				"where dbmovef.dbfacilityid = %d "
				"and dbmovef.chainid = %d ",
				CMove::Integrated, facilityId, batchId);
			stmts.Add(sql);
		}

		dataAccessService.ExecuteStatements("ConfirmAssignment", stmts);

		CStringArray chainList;
		CString temp;
		temp.Format("%d", batchId);
		chainList.Add(temp);
		
		(facilityId, chainList);

	}

	return 0;
}

UINT CIntegrationDataService::ProcessAssignmentConfirmationThread(LPVOID pParam)
{

	return 0;
}


BOOL CIntegrationDataService::IsConnectionNameUsed(const CString &name, int dbid)
{
	CString sql, temp("");
	
	if (dbid > 0)
		temp.Format("and dbexternalconnectionid != %d ", dbid);

	sql.Format("select count(*) from dbexternalconnection "
		"where name = '%s' "
		"%s", name, temp);

	CStringArray results;
	dataAccessService.ExecuteQuery("IsConnectionNameUsed", sql, results, TRUE);

	if (results.GetSize() == 0)
		return FALSE;

	return (atoi(results[0]) > 0);
}

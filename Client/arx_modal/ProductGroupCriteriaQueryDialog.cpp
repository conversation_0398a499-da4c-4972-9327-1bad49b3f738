// ProductGroupCriteriaQueryDialog.cpp : implementation file
//

#include "stdafx.h"
#include "HelpService.h"

#include "ProductGroupCriteriaQueryDialog.h"

#ifdef _DEBUG
#define new DEBUG_NEW
#undef THIS_FILE
static char THIS_FILE[] = __FILE__;
#endif

extern CHelpService helpService;

/////////////////////////////////////////////////////////////////////////////
// CProductGroupCriteriaQueryDialog dialog


CProductGroupCriteriaQueryDialog::CProductGroupCriteriaQueryDialog(CWnd* pParent /*=NULL*/)
	: CDialog(CProductGroupCriteriaQueryDialog::IDD, pParent)
{
	//{{AFX_DATA_INIT(CProductGroupCriteriaQueryDialog)
	m_FromValue = _T("");
	m_ToValue = _T("");
	//}}AFX_DATA_INIT
}


void CProductGroupCriteriaQueryDialog::DoDataExchange(CDataExchange* pDX)
{
	CDialog::DoDataExchange(pDX);
	//{{AFX_DATA_MAP(CProductGroupCriteriaQueryDialog)
	DDX_Control(pDX, IDC_TO_VALUE, m_ToValueCtrl);
	DDX_Control(pDX, IDC_TO_STATIC, m_ToStaticCtrl);
	DDX_Control(pDX, IDC_OPERATOR, m_OperatorCtrl);
	DDX_Control(pDX, IDC_FROM_VALUE, m_FromValueCtrl);
	DDX_Control(pDX, IDC_FROM_STATIC, m_FromStaticCtrl);
	DDX_Text(pDX, IDC_FROM_VALUE, m_FromValue);
	DDX_Text(pDX, IDC_TO_VALUE, m_ToValue);
	//}}AFX_DATA_MAP
}


BEGIN_MESSAGE_MAP(CProductGroupCriteriaQueryDialog, CDialog)
	//{{AFX_MSG_MAP(CProductGroupCriteriaQueryDialog)
	ON_BN_CLICKED(IDHELP, OnHelp)
	ON_CBN_SELCHANGE(IDC_OPERATOR, OnSelchangeOperator)
	ON_WM_HELPINFO()
	//}}AFX_MSG_MAP
END_MESSAGE_MAP()

/////////////////////////////////////////////////////////////////////////////
// CProductGroupCriteriaQueryDialog message handlers

void CProductGroupCriteriaQueryDialog::OnOK() 
{
	UpdateData(TRUE);

	COperator *pOperator;

	if (m_OperatorCtrl.GetCurSel() <= 0) {
		m_InternalOperator = "";
		m_InternalValue = "";
	}
	else {
		pOperator = (COperator *)m_OperatorCtrl.GetItemDataPtr(m_OperatorCtrl.GetCurSel());

		if (pOperator->m_OperandCount != 2) {
			if (m_FromValue == "") {
				AfxMessageBox("Please enter a comparison value.");
				m_FromValueCtrl.SetFocus();
				return;
			}
		}
		else {
			if (m_FromValue == "") {
				AfxMessageBox("Please enter a from value.");
				m_FromValueCtrl.SetFocus();
				return;
			}
			
			if (m_ToValue == "") {
				AfxMessageBox("Please enter a to value.");
				m_ToValueCtrl.SetFocus();
				return;
			}
		}
		
		m_InternalOperator = pOperator->m_Internal;
		m_InternalValue = m_FromValue;
		if (pOperator->m_OperandCount == 2) {
			m_InternalValue += "^and^";
			m_InternalValue += m_ToValue;
		}
		else if (pOperator->m_OperandCount > 2) {
			m_InternalValue.Replace(",", "^");
		}


	}

	CDialog::OnOK();
}

void CProductGroupCriteriaQueryDialog::OnCancel() 
{
	// TODO: Add extra cleanup here
	
	CDialog::OnCancel();
}

void CProductGroupCriteriaQueryDialog::OnHelp() 
{
	helpService.ShowScreenHelp(IDD);	
}

void CProductGroupCriteriaQueryDialog::OnSelchangeOperator() 
{
	COperator *pOperator;

	if (m_OperatorCtrl.GetCurSel() <= 0) {
		m_FromValueCtrl.ShowWindow(SW_HIDE);
		m_ToValueCtrl.ShowWindow(SW_HIDE);
		m_FromStaticCtrl.ShowWindow(SW_HIDE);
		m_ToStaticCtrl.ShowWindow(SW_HIDE);
	}
	else {
		pOperator = (COperator *)m_OperatorCtrl.GetItemDataPtr(m_OperatorCtrl.GetCurSel());
		
		if (pOperator->m_OperandCount != 2) {
			m_FromValueCtrl.ShowWindow(SW_SHOW);
			m_FromStaticCtrl.SetWindowText("Value:");
			m_FromStaticCtrl.ShowWindow(SW_SHOW);
			m_ToValueCtrl.ShowWindow(SW_HIDE);
			m_ToStaticCtrl.ShowWindow(SW_HIDE);
		}
		else {
			m_FromValueCtrl.ShowWindow(SW_SHOW);
			m_FromStaticCtrl.SetWindowText("From Value:");
			m_FromStaticCtrl.ShowWindow(SW_SHOW);
			m_ToValueCtrl.ShowWindow(SW_SHOW);
			m_ToStaticCtrl.SetWindowText("To Value:");
			m_ToStaticCtrl.ShowWindow(SW_SHOW);
		}
	}

	UpdateData(FALSE);

}

BOOL CProductGroupCriteriaQueryDialog::OnInitDialog() 
{
	CDialog::OnInitDialog();
	int idx;
	CRect r;
	COperator *pOperator;
	int curSel;

	m_FromValueCtrl.ShowWindow(SW_HIDE);
	m_FromStaticCtrl.ShowWindow(SW_HIDE);
	m_ToValueCtrl.ShowWindow(SW_HIDE);
	m_ToStaticCtrl.ShowWindow(SW_HIDE);

	m_OperatorCtrl.GetWindowRect(&r);
	m_OperatorCtrl.SetWindowPos(NULL, 0, 0, r.Width(), r.Height()*7, SWP_NOMOVE|SWP_NOZORDER);

	m_OperatorCtrl.AddString("Any");
	curSel = 0;
	for (int i=0; i < m_ProductGroupDataService->m_OperatorService.m_OperatorDisplayList.GetSize(); ++i) {
		pOperator = m_ProductGroupDataService->m_OperatorService.m_OperatorDisplayList[i];
		m_OperatorCtrl.AddString(pOperator->m_Display);
		m_OperatorCtrl.SetItemDataPtr(i+1, pOperator);
		if (m_InternalOperator == pOperator->m_Internal)
			curSel = i+1;
	}

	m_OperatorCtrl.SetCurSel(curSel);

	if (curSel > 0) {

		pOperator = (COperator *)m_OperatorCtrl.GetItemDataPtr(curSel);

		if (pOperator->m_OperandCount == 2) {	// between
			idx = m_InternalValue.Find("^");
			m_FromValue = m_InternalValue.Left(idx);
			m_InternalValue = m_InternalValue.Mid(idx+5);
			m_ToValue = m_InternalValue;

			m_FromValueCtrl.ShowWindow(SW_SHOW);
			m_ToValueCtrl.ShowWindow(SW_SHOW);
			m_FromStaticCtrl.SetWindowText("Starting Value:");
			m_FromStaticCtrl.ShowWindow(SW_SHOW);
			m_ToStaticCtrl.SetWindowText("Ending Value: ");
			m_ToStaticCtrl.ShowWindow(SW_SHOW);
		}
		else if (pOperator->m_OperandCount > 2) {
			m_FromValue = m_InternalValue;
			m_FromValue.Replace("^", ",");
			m_FromValueCtrl.ShowWindow(SW_SHOW);
			m_FromStaticCtrl.SetWindowText("Value:");
			m_FromStaticCtrl.ShowWindow(SW_SHOW);
		}
		else {
			m_FromValue = m_InternalValue;

			m_FromValueCtrl.ShowWindow(SW_SHOW);
			m_FromStaticCtrl.SetWindowText("Value:");
			m_FromStaticCtrl.ShowWindow(SW_SHOW);
		}
	}

	
	CString temp;
	GetWindowText(temp);
	temp += " - ";
	temp += m_Attribute;
	SetWindowText(temp);

	UpdateData(FALSE);

	return TRUE;  // return TRUE unless you set the focus to a control
	              // EXCEPTION: OCX Property Pages should return FALSE
}



BOOL CProductGroupCriteriaQueryDialog::OnHelpInfo(HELPINFO* pHelpInfo) 
{
	helpService.ShowFieldHelp(IDD, pHelpInfo->iCtrlId);
	return FALSE;
}

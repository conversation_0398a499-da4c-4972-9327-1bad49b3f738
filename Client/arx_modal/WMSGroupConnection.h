// WMSGroupConnection.h: interface for the CWMSGroupConnection class.
//
//////////////////////////////////////////////////////////////////////

#if !defined(AFX_WMSGROUPCONNECTION_H__65F1F36D_4649_43C1_A00F_7CE8D851150E__INCLUDED_)
#define AFX_WMSGROUPCONNECTION_H__65F1F36D_4649_43C1_A00F_7CE8D851150E__INCLUDED_

#include "ExternalConnection.h"

#if _MSC_VER > 1000
#pragma once
#endif // _MSC_VER > 1000

class CWMSGroupConnection : public CObject  
{
public:
	CWMSGroupConnection();
	CWMSGroupConnection(const CWMSGroupConnection& other);
	CWMSGroupConnection& operator=(const CWMSGroupConnection& other);
	BOOL operator==(const CWMSGroupConnection& other);
	BOOL operator!=(const CWMSGroupConnection& other) { return (! (*this == other)); }
	virtual ~CWMSGroupConnection();

	int Parse(const CString &line);


	int m_GroupConnectionDBId;
	int m_WMSGroupDBId;

	CExternalConnection *m_pExternalConnection;
	int m_ExternalConnectionDBId;

	int m_InterfaceType;
	int m_Direction;

	CString m_GroupName;
	CString m_ConnectionName;

	typedef enum {
		LocationInterface,
		ProductInterface,
		AssignmentInterface,
		LocationConfirmationInterface,
		AssignmentConfirmationInterface,
		ProductConfirmationInterface,
		Confirmation,
		All
	} enumInterfaceType;

	typedef enum {
		Inbound,
		Outbound,
		Both
	} enumDirection;

};

#endif // !defined(AFX_WMSGROUPCONNECTION_H__65F1F36D_4649_43C1_A00F_7CE8D851150E__INCLUDED_)

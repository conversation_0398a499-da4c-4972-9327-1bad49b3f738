#if !defined(AFX_POPULATEUDF_H__65554E2A_A4D1_4CC6_89C0_59D30831E0B8__INCLUDED_)
#define AFX_POPULATEUDF_H__65554E2A_A4D1_4CC6_89C0_59D30831E0B8__INCLUDED_

#if _MSC_VER > 1000
#pragma once
#endif // _MSC_VER > 1000
// PopulateUDF.h : header file
//

/////////////////////////////////////////////////////////////////////////////
// CPopulateUDF dialog

class CPopulateUDF : public CDialog
{
// Construction
public:
	CPopulateUDF(CWnd* pParent = NULL);   // standard constructor

// Dialog Data
	//{{AFX_DATA(CPopulateUDF)
	enum { IDD = IDD_POPULATE_UDF };
	CComboBox	m_AttributeListCtrl;
	CComboBox	m_UDFListCtrl;
	CString	m_Formula;
	//}}AFX_DATA


// Overrides
	// ClassWizard generated virtual function overrides
	//{{AFX_VIRTUAL(CPopulateUDF)
	protected:
	virtual void DoDataExchange(CDataExchange* pDX);    // DDX/DDV support
	//}}AFX_VIRTUAL

// Implementation
protected:

	// Generated message map functions
	//{{AFX_MSG(CPopulateUDF)
	virtual BOOL OnInitDialog();
	virtual void OnOK();
	afx_msg BOOL OnHelpInfo(HELPINFO* pHelpInfo);
	afx_msg void OnHelp();
	//}}AFX_MSG
	DECLARE_MESSAGE_MAP()
};

//{{AFX_INSERT_LOCATION}}
// Microsoft Visual C++ will insert additional declarations immediately before the previous line.

#endif // !defined(AFX_POPULATEUDF_H__65554E2A_A4D1_4CC6_89C0_59D30831E0B8__INCLUDED_)

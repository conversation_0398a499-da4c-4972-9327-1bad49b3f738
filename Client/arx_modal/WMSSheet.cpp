// WMSSheet.cpp : implementation file
//

#include "stdafx.h"
#include "modal.h"
#include "WMSSheet.h"
#include "IntegrationDataService.h"
#include "UtilityHelper.h"
#include "FacilityDataService.h"
#include "WMSGroupDialog.h"
#include "ProcessingMessage.h"
#include "DisplayResults.h"
#include "HelpService.h"

#ifdef _DEBUG
#define new DEBUG_NEW
#undef THIS_FILE
static char THIS_FILE[] = __FILE__;
#endif

extern CIntegrationDataService integrationDataService;
extern CUtilityHelper utilityHelper;
extern CFacilityDataService facilityDataService;
extern CHelpService helpService;
/////////////////////////////////////////////////////////////////////////////
// CWMSSheet

IMPLEMENT_DYNAMIC(CWMSSheet, CPropertySheet)

CWMSSheet::CWMSSheet(UINT nIDCaption, CWnd* pParentWnd, UINT iSelectPage)
	:CPropertySheet(nIDCaption, pParentWnd, iSelectPage)
{
}

CWMSSheet::CWMSSheet(LPCTSTR pszCaption, CWnd* pParentWnd, UINT iSelectPage)
	:CPropertySheet(pszCaption, pParentWnd, iSelectPage)
{
}

CWMSSheet::~CWMSSheet()
{

	delete m_GroupButton;
	delete m_ReportButton;

	for (int i=0; i < m_FacilityList.GetSize(); ++i)
		delete m_FacilityList[i];

	for (i=0; i < m_GroupList.GetSize(); ++i)
		delete m_GroupList[i];

//	for (i=0; i < m_WMSList.GetSize(); ++i)
//		delete m_WMSList[i];

}


BEGIN_MESSAGE_MAP(CWMSSheet, CPropertySheet)
	//{{AFX_MSG_MAP(CWMSSheet)
	ON_WM_HELPINFO()
	//}}AFX_MSG_MAP
END_MESSAGE_MAP()

/////////////////////////////////////////////////////////////////////////////
// CWMSSheet message handlers

BOOL CWMSSheet::OnInitDialog() 
{
	BOOL bResult = CPropertySheet::OnInitDialog();
	
	CRect r;
	CButton *pButton = (CButton *)GetDlgItem(IDOK);
	pButton->GetWindowRect(&r);
	CFont *font = pButton->GetFont();
	this->ScreenToClient(&r);

	m_GroupButton = new CButton;
	m_GroupButton->Create("Define WMS", WS_CHILD & ~(BS_DEFPUSHBUTTON), r, this, WM_USER+1);
	m_GroupButton->SetFont(font);
	m_GroupButton->MoveWindow(10, r.top, r.Width(), r.Height(), FALSE);
	m_GroupButton->EnableWindow(SW_SHOW);
	m_GroupButton->ShowWindow(SW_SHOW);

	m_ReportButton = new CButton;
	m_ReportButton->Create("Summary", WS_CHILD & ~(BS_DEFPUSHBUTTON), r, this, WM_USER+2);
	m_ReportButton->SetFont(font);
	m_ReportButton->MoveWindow(10+r.Width()+10, r.top, r.Width(), r.Height(), FALSE);
	m_ReportButton->EnableWindow(SW_SHOW);
	m_ReportButton->ShowWindow(SW_SHOW);

	return bResult;
}

int CWMSSheet::LoadWMSList()
{
	CStringArray groupList, wmsList, mapList;

	if (m_WMSList.GetSize() > 0)
		return 0;

	// Name, FacDesc, FacSect, Master
	try {
		integrationDataService.GetWMSGroupList(groupList);
		for (int i=0; i < groupList.GetSize(); ++i) {
			CWMSGroup *pGroup = new CWMSGroup;
			pGroup->Parse(groupList[i]);
			m_GroupList.Add(pGroup);
			m_GroupMap.SetAt(pGroup->m_WMSGroupDBId, pGroup);
		}

		integrationDataService.GetWMSList(0, wmsList);
		for (i=0; i < wmsList.GetSize(); ++i) {
			CWMS *pWMS = new CWMS;
			pWMS->Parse(wmsList[i]);
			m_WMSList.Add(pWMS);
			m_WMSMap.SetAt(pWMS->m_WMSDBId, pWMS);
			CWMSGroup *pGroup;
			if (m_GroupMap.Lookup(pWMS->m_GroupDBId, pGroup))
				pGroup->m_WMSList.Add(pWMS);
		}
	}
	catch (...) {
		utilityHelper.ProcessError("Error getting WMS Facility list.");
		return -1;
	}

	return 0;
}

int CWMSSheet::LoadFacilityList()
{
	int i;
	CStringArray facilityList, sectionList, strings;
	long facilityDBId, sectionDBId;
	CString facDesc, sectionDesc;
	
	if (m_FacilityList.GetSize() > 0)
		return 0;

	try {
		facilityDataService.GetFacilitySectionList(facilityList);
	}
	catch (...) {
		utilityHelper.ProcessError("Error getting list of facilities.");
		return -1;
	}
	
	facilityDBId = -1;
	CWMSFacilityInfo *pFacInfo;

	for (i=0; i < facilityList.GetSize(); ++i) {
	
		strings.RemoveAll();
		utilityHelper.ParseString(facilityList[i], "|", strings);

		int tempDBId = atol(strings[0]);

		if (tempDBId != facilityDBId) {
			facilityDBId = tempDBId;
			pFacInfo = new CWMSFacilityInfo;
			pFacInfo->m_FacilityDBId = tempDBId;
			pFacInfo->m_FacilityName = strings[1];
			m_FacilityList.Add(pFacInfo);
			//m_FacilityMap.SetAt(tempDBId, pFacInfo);
		}

		sectionDBId = atol(strings[2]);
		if (sectionDBId > 0) {
			sectionDesc = strings[3];
			pFacInfo->m_SectionDBIdList.Add(sectionDBId);
			pFacInfo->m_SectionNameList.Add(sectionDesc);
		}
		//m_SectionMap.SetAt(sectionDBId, pFacInfo);

	}


	return 0;
}

BOOL CWMSSheet::OnCommand(WPARAM wParam, LPARAM lParam) 
{
	if (HIWORD(wParam) == BN_CLICKED) {
		if (LOWORD(wParam) == WM_USER+1) {
			OnWMS();
			return TRUE;
		}
		else if (LOWORD(wParam) == WM_USER+2) {
			OnSummary();
			return TRUE;
		}
	}
	return CPropertySheet::OnCommand(wParam, lParam);
}

void CWMSSheet::OnWMS()
{
	CWMSGroupDialog dialog;

	try {
		dialog.DoModal();
	}
	catch (...) {
		AfxMessageBox("Error running WMS Facility Setup");
	}

	if (! dialog.m_SomethingChanged) 
		return;

	CProcessingMessage msg("Refreshing Lists", this);

	CWMSImportPage *pImportPage = (CWMSImportPage *)this->GetPage(0);
	CWMSExportPage *pExportPage = (CWMSExportPage *)this->GetPage(1);
	
	for (int i=0; i < m_GroupList.GetSize(); ++i)
		delete m_GroupList[i];		// groups also delete wms's

	m_GroupList.RemoveAll();
	m_WMSList.RemoveAll();

	LoadWMSList();

	if (pImportPage->m_hWnd != NULL)	
		pImportPage->Reload();

	if (pExportPage->m_hWnd != NULL)
	pExportPage->Reload();

}

void CWMSSheet::OnSummary()
{
	CDisplayResults dlg;

	dlg.m_AllowMultipleSelections = FALSE;
	dlg.m_AllowSort = TRUE;
	dlg.m_Headers.Add("WMS Facility|WMS Section|From Facility|From Section|To Facility|To Section|");
	dlg.m_Tabs.Add("WMS Summary");
	dlg.m_WindowCaptions.Add("WMS Summary");


	CWMSImportPage *pImportPage = (CWMSImportPage *)GetPage(0);
	CWMSExportPage *pExportPage = (CWMSExportPage *)GetPage(1);

	CTypedPtrArray<CObArray, CWMSMap*> importList;
	importList.Copy(pImportPage->m_ImportMapList);

	if (pExportPage->m_ExportMapList.GetSize() == 0)
		pExportPage->LoadMapList();

	CTypedPtrArray<CObArray, CWMSMap*> &exportList = pExportPage->m_ExportMapList;

	CString temp;
	for (int i=0; i < m_GroupList.GetSize(); ++i) {
		CWMSGroup *pGroup = m_GroupList[i];

		for (int j=0; j < pGroup->m_WMSList.GetSize(); ++j) {

			CWMS *pWMS = pGroup->m_WMSList[j];
			
			for (int k=0; k < exportList.GetSize(); ++k) {
				CWMSMap *pExportMap = exportList[k];
				if (pExportMap->m_WMSDBId == pWMS->m_WMSDBId) {
					temp.Format("%s|%s|%s|%s|", 
						pGroup->m_Name, pWMS->m_Name, pExportMap->m_FacilityName,
						(pExportMap->m_SectionDBId > 0) ? pExportMap->m_SectionName : "All Sections");
					dlg.m_Data.Add(temp);
					BOOL found = FALSE;
					for (int l=0; l < importList.GetSize(); ++l) {
						CWMSMap *pImportMap = importList[l];
						if (pImportMap->m_WMSDBId == pWMS->m_WMSDBId &&
							pImportMap->m_FacilityDBId == pExportMap->m_FacilityDBId &&
							pImportMap->m_SectionDBId == pExportMap->m_SectionDBId) {
							temp.Format("%s|%s|", pImportMap->m_FacilityName,
								(pImportMap->m_SectionDBId > 0) ? pImportMap->m_SectionName : "All Sections");
							dlg.m_Data[dlg.m_Data.GetSize()-1] += temp;
							importList.RemoveAt(l);
							found = TRUE;
							break;
						}
					}

					if (! found) {
						for (int l=0; l < importList.GetSize(); ++l) {
							CWMSMap *pImportMap = importList[l];
							if (pImportMap->m_WMSDBId == pWMS->m_WMSDBId &&
								pImportMap->m_FacilityDBId == pExportMap->m_FacilityDBId) 
							{
								temp.Format("%s|%s|", pImportMap->m_FacilityName,
									(pImportMap->m_SectionDBId > 0) ? pImportMap->m_SectionName : "All Sections");
								dlg.m_Data[dlg.m_Data.GetSize()-1] += temp;
								importList.RemoveAt(l);
								found = TRUE;
								break;
							}
						}

					}

					if (! found) {
						for (int l=0; l < importList.GetSize(); ++l) {
							CWMSMap *pImportMap = importList[l];
							if (pImportMap->m_WMSDBId == pWMS->m_WMSDBId) 
							{
								temp.Format("%s|%s|", pImportMap->m_FacilityName,
									(pImportMap->m_SectionDBId > 0) ? pImportMap->m_SectionName : "All Sections");
								dlg.m_Data[dlg.m_Data.GetSize()-1] += temp;
								importList.RemoveAt(l);
								found = TRUE;
								break;
							}
						}

					}
				}	// wms matches

			}	// foreach export list

			for (k=0; k < importList.GetSize(); ++k) {
				CWMSMap *pImportMap = importList[k];
				if (pImportMap->m_WMSDBId == pWMS->m_WMSDBId) {
					temp.Format("%s|%s|%s|%s|%s|%s|", 
						pGroup->m_Name, pWMS->m_Name, "", "", pImportMap->m_FacilityName,
						(pImportMap->m_SectionDBId > 0) ? pImportMap->m_SectionName : "All Sections");
					dlg.m_Data.Add(temp);
				}
			}	// foreach import list

		}	// foreach wms

	}	// foreach group

	/*
	try {
		integrationDataService.GetWMSMapSummaryList(dlg.m_Data);
	}
	catch (...) {
		utilityHelper.ProcessError("Error loading WMS maps");
		return;
	}
	*/

	dlg.DoModal();

				
}

CString CWMSSheet::GetWMSGroupName(int wmsDBId)
{
	CWMS *pWMS;
	if (m_WMSMap.Lookup(wmsDBId, pWMS))
		return pWMS->m_GroupName;

	return "";
}


BOOL CWMSSheet::OnHelpInfo(HELPINFO* pHelpInfo) 
{
	switch (pHelpInfo->iCtrlId) {
	case IDOK:
	case IDCANCEL:
	case IDHELP:
		helpService.ShowFieldHelp(1, pHelpInfo->iCtrlId);
		break;
	case WM_USER+1:
		helpService.ShowFieldHelp("WMSSetup_DefineWMS");
		break;
	case WM_USER+2:
		helpService.ShowFieldHelp("WMSSetup_Summary");
		break;
	default:
		this->PressButton(PSBTN_HELP);
		break;
	}
	
	return CPropertySheet::OnHelpInfo(pHelpInfo);
}

// ExternalConnection.cpp: implementation of the CExternalConnection class.
//
//////////////////////////////////////////////////////////////////////

#include "stdafx.h"
#include "modal.h"
#include "ExternalConnection.h"
#include "UtilityHelper.h"

#ifdef _DEBUG
#undef THIS_FILE
static char THIS_FILE[]=__FILE__;
#define new DEBUG_NEW
#endif

//////////////////////////////////////////////////////////////////////
// Construction/Destruction
//////////////////////////////////////////////////////////////////////

extern CUtilityHelper utilityHelper;

CExternalConnection::CExternalConnection()
{
	m_ExternalConnectionDBId = 0;
	m_Port = 0;
	m_Channel = "";
	m_Host = "";
	m_FileName = "";
	m_TriggerName = "";
	m_Queue = "";
	m_QueueManager = "";
	m_Name = "";
	m_Path = "";
	m_Login = "";
	m_Password = "";
	m_ConnectionType = -1;

}

CExternalConnection::~CExternalConnection()
{

}

CExternalConnection::CExternalConnection(const CExternalConnection& other)
{
	m_ExternalConnectionDBId = other.m_ExternalConnectionDBId;
	m_Channel = other.m_Channel;
	m_ConnectionType = other.m_ConnectionType;
	m_FileName = other.m_FileName;
	m_Host = other.m_Host;
	m_Login = other.m_Login;
	m_Password = other.m_Password;
	m_Path = other.m_Path;
	m_Port = other.m_Port;
	m_Queue = other.m_Queue;
	m_QueueManager = other.m_QueueManager;
	m_TriggerName = other.m_TriggerName;
	m_Name = other.m_Name;

}


CExternalConnection& CExternalConnection::operator=(const CExternalConnection& other)
{
	m_ExternalConnectionDBId = other.m_ExternalConnectionDBId;
	m_Channel = other.m_Channel;
	m_ConnectionType = other.m_ConnectionType;
	m_FileName = other.m_FileName;
	m_Host = other.m_Host;
	m_Login = other.m_Login;
	m_Password = other.m_Password;
	m_Path = other.m_Path;
	m_Port = other.m_Port;
	m_Queue = other.m_Queue;
	m_QueueManager = other.m_QueueManager;
	m_TriggerName = other.m_TriggerName;
	m_Name = other.m_Name;

	return *this;
}


BOOL CExternalConnection::operator==(const CExternalConnection& other)
{
	if (m_ExternalConnectionDBId != other.m_ExternalConnectionDBId) return FALSE;
	if (m_Channel != other.m_Channel) return FALSE;
	if (m_ConnectionType != other.m_ConnectionType) return FALSE;
	if (m_FileName != other.m_FileName) return FALSE;
	if (m_Host != other.m_Host) return FALSE;
	if (m_Login != other.m_Login) return FALSE;
	if (m_Password != other.m_Password) return FALSE;
	if (m_Path != other.m_Path) return FALSE;
	if (m_Port != other.m_Port) return FALSE;
	if (m_Queue != other.m_Queue) return FALSE;
	if (m_QueueManager != other.m_QueueManager) return FALSE;
	if (m_TriggerName != other.m_TriggerName) return FALSE;
	if (m_Name != other.m_Name) return FALSE;

	return TRUE;
}


int CExternalConnection::Parse(const CString& line)
{
	CStringArray strings;

	utilityHelper.ParseString(line, "|", strings);

	for (int i=0; i < strings.GetSize(); ++i) {
		if (strings[i] == " ")
			strings[i] = "";

		switch (i) {
		case 0:
			m_ExternalConnectionDBId = atoi(strings[i]);
			break;
		case 1:
			m_Name = strings[i];
			break;
		case 2:
			m_ConnectionType = atoi(strings[i]);
			break;
		case 3:
			m_Host = strings[i];
			break;
		case 4:
			m_Port = atoi(strings[i]);
			break;
		case 5:
			m_QueueManager = strings[i];
			break;
		case 6:
			m_Login = strings[i];
			break;
		case 7:
			m_Password = strings[i];
			break;
		case 8:
			m_Path = strings[i];
			break;
		case 9:
			m_Queue = strings[i];
			m_FileName = strings[i];
			break;
		case 10:
			m_TriggerName = strings[i];
			break;
		case 11:
			m_Channel = strings[i];
			break;
		}
	}

	return 0;
}

CString CExternalConnection::ConnectionTypeAsText()
{
	switch (m_ConnectionType) {
	case MQSeries:
		return "MQSeries";
		break;
	case FTP:
		return "Remote(FTP)";
		break;
	case Local:
		return "Local Disk";
		break;
	case Prompt:
		return "Manual (prompt for file)";
		break;
	}

	return "Uknown";
}

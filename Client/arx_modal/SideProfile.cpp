// SideProfile.cpp: implementation of the CSideProfile class.
//
//////////////////////////////////////////////////////////////////////

#include "stdafx.h"
#include "modal.h"
#include "SideProfile.h"
#include "UtilityHelper.h"
#include "Constants.h"

#ifdef _DEBUG
#undef THIS_FILE
static char THIS_FILE[]=__FILE__;
#define new DEBUG_NEW
#endif

extern CUtilityHelper utilityHelper;

//////////////////////////////////////////////////////////////////////
// Construction/Destruction
//////////////////////////////////////////////////////////////////////

CSideProfile::CSideProfile()
{
	m_SideProfileDBId = 0;
	m_TotalLength = m_MaximumBayDepth = m_MaximumBayHeight = 0;
	m_FixedLength = FALSE;
}

CSideProfile::~CSideProfile()
{
	for (int i=0; i < m_BayProfileList.GetSize(); ++i)
		delete m_BayProfileList[i];
}

CSideProfile& CSideProfile::operator=(const CSideProfile &other)
{
	m_SideProfileDBId = other.m_SideProfileDBId;
	m_Description = other.m_Description;
	m_TotalLength = other.m_TotalLength;
	m_MaximumBayDepth = other.m_MaximumBayDepth;
	m_MaximumBayHeight = other.m_MaximumBayHeight;

	for (int i=0; i < m_BayProfileList.GetSize(); ++i)
		delete m_BayProfileList[i];

	m_BayProfileList.RemoveAll();

	for (i=0; i < other.m_BayProfileList.GetSize(); ++i) {
		CBayProfile *pBayProfile = new CBayProfile(*other.m_BayProfileList[i]);
		m_BayProfileList.Add(pBayProfile);
	}

	return *this;
}

CSideProfile::CSideProfile(const CSideProfile &other)
{
	m_SideProfileDBId = other.m_SideProfileDBId;
	m_Description = other.m_Description;
	m_TotalLength = other.m_TotalLength;
	m_MaximumBayDepth = other.m_MaximumBayDepth;
	m_MaximumBayHeight = other.m_MaximumBayHeight;

	for (int i=0; i < m_BayProfileList.GetSize(); ++i)
		delete m_BayProfileList[i];

	m_BayProfileList.RemoveAll();

	for (i=0; i < other.m_BayProfileList.GetSize(); ++i) {
		CBayProfile *pBayProfile = new CBayProfile(*other.m_BayProfileList[i]);
		m_BayProfileList.Add(pBayProfile);
	}
}


BOOL CSideProfile::operator==(const CSideProfile& other)
{
	if (m_SideProfileDBId != other.m_SideProfileDBId) return FALSE;
	if (m_Description != other.m_Description) return FALSE;
	if (m_TotalLength != other.m_TotalLength) return FALSE;
	if (m_MaximumBayDepth != other.m_MaximumBayDepth) return FALSE;
	if (m_MaximumBayHeight != other.m_MaximumBayHeight) return FALSE;

	if (m_BayProfileList.GetSize() != other.m_BayProfileList.GetSize()) return FALSE;

	for (int i=0; i < m_BayProfileList.GetSize(); ++i) {
		if (*m_BayProfileList[i] != *other.m_BayProfileList[i])
			return FALSE;
	}

	return TRUE;
}

int CSideProfile::Parse(CString &line)
{
	CStringArray strings;

	utilityHelper.ParseString(line, "|", strings);
	for (int i=0; i < strings.GetSize(); ++i) {
		switch (i) {
		case 0:
			m_SideProfileDBId = atoi(strings[i]);
			break;
		case 1:
			m_Description = strings[i];
			break;
		case 2:
			m_TotalLength = atof(strings[i]);
			break;
		case 3:
			m_MaximumBayDepth = atof(strings[i]);
			break;
		case 4:
			m_MaximumBayHeight = atof(strings[i]);
			break;
		}
	}

	return 0;

}

int CSideProfile::Draw(BOOL currentDB, BOOL doSave)
{
	if (! currentDB)
		m_pDatabase = new AcDbDatabase();
	else
		m_pDatabase = acdbCurDwg();

	DrawSide();
	
	if (! currentDB)
		delete m_pDatabase;

	return 0;

}

int CSideProfile::DrawByPosition(AcDbDatabase *pDatabase, const C3DPoint &backLeftPoint, 
								 double rotation, int isRightSide)
{
	m_pDatabase = pDatabase;
	
	CBayProfile *pBayProfile, *pNextBay, *pPrevBay;

	int multiplier;
	// draw left aisle
	//multiplier = -1;

	// draw right aisle
	multiplier = 1;

	// The first y position is at the left end of the aisle
	double x, y, z;
	x = y = z = 0;
	
	double leftBarWidth, rightBarWidth;
	C3DPoint newBackLeftPoint(backLeftPoint);

	for (int i=0; i < m_BayProfileList.GetSize(); i++) {

		pBayProfile = m_BayProfileList[i];

		if (i == 0)
		//	newBackLeftPoint.m_Y -= cos(rotation)*pBayProfile->m_Depth;
		;

		if (i < m_BayProfileList.GetSize()-1)
			pNextBay = m_BayProfileList[i+1];
		else
			pNextBay = pBayProfile;

		if (i > 0)
			pPrevBay = m_BayProfileList[i-1];
		else
			pPrevBay = pBayProfile;


		// The widest is upright is always used between two bays
		// Note: The "<"s and ">"s are very important here - be careful
		// about changing them to "<=" or ">="
		// Once an upright width is picked, draw it on the associated bay,


		if (isRightSide) {
			// Check the right upright
			if (pBayProfile->m_UprightWidth >= pPrevBay->m_UprightWidth || i == 0)
				rightBarWidth = pBayProfile->m_UprightWidth;
			else
				rightBarWidth = 0;
			
			
			if (pBayProfile->m_UprightWidth > pNextBay->m_UprightWidth ||
				i == m_BayProfileList.GetSize()-1)
				leftBarWidth = pBayProfile->m_UprightWidth;
			else
				leftBarWidth = 0;	
			
		}
		else {
			
			// Check the left upright
			if (pBayProfile->m_UprightWidth >= pPrevBay->m_UprightWidth || i == 0)
				leftBarWidth = pBayProfile->m_UprightWidth;
			else
				leftBarWidth = 0;
			
			
			if (pBayProfile->m_UprightWidth > pNextBay->m_UprightWidth ||
				i == m_BayProfileList.GetSize()-1)
				rightBarWidth = pBayProfile->m_UprightWidth;
			else
				rightBarWidth = 0;
		}


		if (i == 0)
			y = -pBayProfile->m_Depth/2;
		else {
			if (isRightSide)
				y = -pBayProfile->m_Depth/2;
			else
				y = -m_BayProfileList[0]->m_Depth+pBayProfile->m_Depth/2;
		}

		z = pBayProfile->m_UprightHeight/2;
		if (pBayProfile->m_UprightHeight == 0)
			z = .5;

		if (isRightSide)
			x += rightBarWidth + pBayProfile->m_Width/2;
		else
			x += leftBarWidth + pBayProfile->m_Width/2; // + rightBarWidth) / 2;

		C3DPoint centerPoint(x, y, z);
		centerPoint.Rotate(rotation);
		centerPoint += newBackLeftPoint;


		int rc = pBayProfile->DrawByPosition(m_pDatabase, centerPoint, rotation+(isRightSide*180), leftBarWidth, rightBarWidth);
		if (rc < 0) {
			ads_printf("Error drawing bay: %s\n", pBayProfile->m_Description);
			return Acad::eOk;
		}
		
		if (isRightSide)
			x += pBayProfile->m_Width/2 + leftBarWidth;
		else
			x += pBayProfile->m_Width/2 + rightBarWidth;

		
	}

	return Acad::eOk;
}


Acad::ErrorStatus CSideProfile::DrawSide()
{
	double totalBayWidth = 0;
	
	CBayProfile *pBayProfile, *pNextBay, *pPrevBay;

	int multiplier = 0;
	// draw left aisle
	//multiplier = -1;

	// draw right aisle
	multiplier = 1;

	// The first y position is at the left end of the aisle
	double y = multiplier*CalculateLength()/2;
	double x = 0;
	double frontx = 0;
	double z = 0;
	double bottomz = 0;
	double leftBarWidth, rightBarWidth;

	for (int i=0; i < m_BayProfileList.GetSize(); i++) {

		pBayProfile = m_BayProfileList[i];

		if (i < m_BayProfileList.GetSize()-1)
			pNextBay = m_BayProfileList[i+1];
		else
			pNextBay = pBayProfile;

		if (i > 0)
			pPrevBay = m_BayProfileList[i-1];
		else
			pPrevBay = pBayProfile;

		if (i == 0) {
			frontx = -multiplier*pBayProfile->m_Depth/2;
			x = 0;
			bottomz = -pBayProfile->m_UprightHeight/2;
			z = 0;
		}
		else {
			x = frontx + multiplier*pBayProfile->m_Depth/2;
			if (pBayProfile->m_BayType == BAYTYPE_FLOOR)
				z = bottomz + .5;
			else
				z = bottomz + pBayProfile->m_UprightHeight/2;
		}


		// The widest is upright is always used between two bays
		// Note: The "<"s and ">"s are very important here - be careful
		// about changing them to "<=" or ">="

		// Check the left upright
		if (pBayProfile->m_UprightWidth >= pPrevBay->m_UprightWidth || i == 0)
			leftBarWidth = pBayProfile->m_UprightWidth;
		else
			leftBarWidth = 0;

		
		if (pBayProfile->m_UprightWidth > pNextBay->m_UprightWidth ||
			i == m_BayProfileList.GetSize()-1)
			rightBarWidth = pBayProfile->m_UprightWidth;
		else
			rightBarWidth = 0;

		/*
		// Regardless of the upright width, always draw it on the
		// least deep bay (with the exception that floors don't draw uprights)
		if (pPrevBay->m_Depth < pBayProfile->m_Depth &&
			pPrevBay->m_BayType != BAYTYPE_FLOOR)
			leftBarWidth = 0;

		// If we are not on the last bay,
		// and the next bay's uprigth width is greater
		// and the next one is not a floor
		if (i < m_BayProfileList.GetSize()-1 &&
			pNextBay->m_UprightWidth > pBayProfile->m_UprightWidth)
			rightBarWidth = pNextBay->m_UprightWidth;
		else
			rightBarWidth = pBayProfile->m_UprightWidth;


		// If the next bay depth is less than or equal to this one, 
		// and the next bay is not a floor, draw the right upright on the next bay
		if (i < m_BayProfileList.GetSize()-1 &&
			pNextBay->m_Depth <= pBayProfile->m_Depth &&
			pNextBay->m_BayType != BAYTYPE_FLOOR)
				rightBarWidth = 0;
		*/

		y -= multiplier*leftBarWidth;
		y -= multiplier*pBayProfile->m_Width/2;

		C3DPoint centerPoint(x, y, z);

		int rc = pBayProfile->DrawByPosition(m_pDatabase, centerPoint, -multiplier*90, leftBarWidth, rightBarWidth);
		if (rc < 0) {
			ads_printf("Error drawing bay: %s\n", pBayProfile->m_Description);
			return Acad::eOk;
		}
	
		y -= multiplier*pBayProfile->m_Width/2;
		y -= multiplier*rightBarWidth;

	}

	return Acad::eOk;
}

double CSideProfile::CalculateLength()
{
	double length = 0;

	for (int i=0; i < m_BayProfileList.GetSize(); ++i) {

		CBayProfile *pBayProfile = m_BayProfileList[i];

		if (i == 0)
			length += pBayProfile->m_UprightWidth;

		length += pBayProfile->m_Width;

		if (i < m_BayProfileList.GetSize()-1) {
			CBayProfile *pNextProfile = m_BayProfileList[i+1];
			if (pNextProfile->m_UprightWidth > pBayProfile->m_UprightWidth)
				length += pNextProfile->m_UprightWidth;
			else
				length += pBayProfile->m_UprightWidth;
		}
		else
			length += pBayProfile->m_UprightWidth;
	}

	return length;

}

double CSideProfile::GetMaxBayDepth()
{
	double max = 0;

	for (int i=0; i < m_BayProfileList.GetSize(); ++i) {
		if (m_BayProfileList[i]->m_Depth > max)
			max = m_BayProfileList[i]->m_Depth;
	}

	return max;

}

double CSideProfile::CalculateMaxBayHeight()
{
	double max = 0;

	for (int i=0; i < m_BayProfileList.GetSize(); ++i) {
		if (m_BayProfileList[i]->m_Height > max)
			max = m_BayProfileList[i]->m_Height;
	}

	return max;
}

// UDFProperties.cpp : implementation file
//

#include "stdafx.h"
#include "HelpService.h"
#include "UDFProperties.h"
#include "UtilityHelper.h"

#ifdef _DEBUG
#define new DEBUG_NEW
#undef THIS_FILE
static char THIS_FILE[] = __FILE__;
#endif

extern CHelpService helpService;
extern CUtilityHelper utilityHelper;

/////////////////////////////////////////////////////////////////////////////
// CUDFProperties dialog

CUDFProperties::CUDFProperties(CWnd* pParent /*=NULL*/)
	: CDialog(CUDFProperties::IDD, pParent)
{
	//{{AFX_DATA_INIT(CUDFProperties)
	m_DefaultValue = _T("");
	m_Name = _T("");
	//}}AFX_DATA_INIT
	m_Adding = FALSE;
}


void CUDFProperties::DoDataExchange(CDataExchange* pDX)
{
	CDialog::DoDataExchange(pDX);
	//{{AFX_DATA_MAP(CUDFProperties)
	DDX_Control(pDX, IDC_LIST_ELEMENTS, m_ListElementsCtrl);
	DDX_Control(pDX, IDC_STATIC_LIST, m_StaticListCtrl);
	DDX_Control(pDX, IDC_DELETE_ELEMENT, m_DeleteElementCtrl);
	DDX_Control(pDX, IDC_ADD_ELEMENT, m_AddElementCtrl);
	DDX_Control(pDX, IDC_TYPE, m_TypeCtrl);
	DDX_Text(pDX, IDC_DEFAULTVALUE, m_DefaultValue);
	DDX_Text(pDX, IDC_NAME, m_Name);
	//}}AFX_DATA_MAP
}


BEGIN_MESSAGE_MAP(CUDFProperties, CDialog)
	//{{AFX_MSG_MAP(CUDFProperties)
	ON_BN_CLICKED(IDHELP, OnHelp)
	ON_BN_CLICKED(IDC_ADD_ELEMENT, OnAddElement)
	ON_BN_CLICKED(IDC_DELETE_ELEMENT, OnDeleteElement)
	ON_CBN_SELCHANGE(IDC_TYPE, OnSelchangeType)
	ON_NOTIFY(LVN_ENDLABELEDIT, IDC_LIST_ELEMENTS, OnEndlabeleditListElements)
	ON_COMMAND(ID_GENERIC_ADD, OnAddElement)
	ON_COMMAND(ID_GENERIC_DELETE, OnDeleteElement)
	ON_WM_HELPINFO()
	//}}AFX_MSG_MAP
END_MESSAGE_MAP()

/////////////////////////////////////////////////////////////////////////////
// CUDFProperties message handlers

BOOL CUDFProperties::OnInitDialog() 
{
	CDialog::OnInitDialog();
	LVITEM lvItem;
	CRect r;

	m_TypeCtrl.GetWindowRect(&r);
	m_TypeCtrl.SetWindowPos(NULL, 0, 0, r.Width(), r.Height()*4, SWP_NOMOVE|SWP_NOZORDER);

	m_ListElementsCtrl.ShowWindow(SW_HIDE);
	m_StaticListCtrl.ShowWindow(SW_HIDE);
	m_AddElementCtrl.ShowWindow(SW_HIDE);
	m_DeleteElementCtrl.ShowWindow(SW_HIDE);
	
	m_TypeCtrl.ResetContent();

	m_TypeCtrl.AddString("Integer");
	m_TypeCtrl.AddString("String");
	m_TypeCtrl.AddString("Float");

	if (m_UDF->m_ElementType != UDF_LEVEL_PROFILE)
		m_TypeCtrl.AddString("List");

	if (m_UDF->m_ListID <= 0) {	// add
		m_Name = "";
		m_TypeCtrl.SetCurSel(-1);
		m_DefaultValue = "";
	}
	else {
		m_Name = m_UDF->m_Name;
		m_TypeCtrl.SetCurSel(m_UDF->m_Type-1);
		m_DefaultValue = m_UDF->m_DefaultValue;
		if (m_UDF->m_Type == DT_LIST) {
			m_ListElementsCtrl.ShowWindow(SW_SHOW);
			m_StaticListCtrl.ShowWindow(SW_SHOW);
			m_AddElementCtrl.ShowWindow(SW_SHOW);
			m_DeleteElementCtrl.ShowWindow(SW_SHOW);			
			for (int i=0; i < m_UDF->m_ListValues.GetSize(); ++i) {
				lvItem.pszText = m_UDF->m_ListValues[i].GetBuffer(0);
				m_UDF->m_ListValues[i].ReleaseBuffer();
				lvItem.iItem = i;
				lvItem.iSubItem = 0;
				lvItem.mask = LVIF_TEXT;
				m_ListElementsCtrl.InsertItem(&lvItem);
			}
		}
	}
	
	m_OrigUDF = *m_UDF;

	m_PreviousType = m_UDF->m_Type;

	UpdateData(FALSE);

	return TRUE;  // return TRUE unless you set the focus to a control
	              // EXCEPTION: OCX Property Pages should return FALSE
}

void CUDFProperties::OnOK() 
{
	CString temp;
	BOOL found;
	int i, j;
	int newType;
	CStringArray listValues;

	UpdateData(TRUE);

	if (m_Name == "") {
		AfxMessageBox("Please enter a name for the user defined field.");
		CEdit *pEdit = (CEdit *)GetDlgItem(IDC_NAME);
		pEdit->SetSel(0, -1);
		pEdit->SetFocus();
		return;
	}

	if (m_TypeCtrl.GetCurSel() < 0) {
		AfxMessageBox("Please select a type from the list.");
		m_TypeCtrl.SetFocus();
		m_TypeCtrl.ShowDropDown(TRUE);
		return;
	}

	if (m_DefaultValue == "") {
		AfxMessageBox("Please enter a default value.");
		CEdit *pEdit = (CEdit *)GetDlgItem(IDC_DEFAULTVALUE);
		pEdit->SetSel(0, -1);
		pEdit->SetFocus();
		return;
	}
	
	newType = m_TypeCtrl.GetCurSel() + 1;
	if (newType == DT_LIST) {
		
		if (m_ListElementsCtrl.GetItemCount() == 0) {
			AfxMessageBox("Please add at least one list element.");
			return;
		}
		
		BOOL found = FALSE;
		for (i=0; i < m_ListElementsCtrl.GetItemCount(); ++i) {
			temp = m_ListElementsCtrl.GetItemText(i, 0);
			if (temp == m_DefaultValue) {
				found = TRUE;
				break;
			}
		}
		
		if (! found) {
			AfxMessageBox("Please enter a default value that is equal to one of the list values.");
			CEdit *pEdit = (CEdit *)GetDlgItem(IDC_DEFAULTVALUE);
			pEdit->SetSel(0, -1);
			pEdit->SetFocus();
			return;
		}
	}

	if (newType == DT_INT) {
		if (! utilityHelper.IsInteger(m_DefaultValue)) {
			AfxMessageBox("The default value must be a valid integer.");
			CEdit *pEdit = (CEdit *)GetDlgItem(IDC_DEFAULTVALUE);
			pEdit->SetSel(0, -1);
			pEdit->SetFocus();
			return;
		}
	}

	if (newType == DT_FLOAT) {
		if (! utilityHelper.IsFloat(m_DefaultValue)) {
			AfxMessageBox("The default value must be a valid floating point number.");
			CEdit *pEdit = (CEdit *)GetDlgItem(IDC_DEFAULTVALUE);
			pEdit->SetSel(0, -1);
			pEdit->SetFocus();
			return;
		}
	}

	// If list values missing warn that element values may change
	// If type changes from string/list to int/float warn that values may change
	
	// Type changes from non-list to list, values may not be in list
	if (m_OrigUDF.m_ListID > 0 && m_OrigUDF.m_Type != DT_LIST && newType == DT_LIST) {
		if (AfxMessageBox("If current values exist that are not in the\n"
						  "list, they will be changed to the default value.\n\nDo you wish to continue?",
			MB_YESNO|MB_ICONWARNING) != IDYES)
			return;
	}
	
	// Type changes from string to numeric, values may not convert properly
	if ( (m_OrigUDF.m_Type == DT_LIST || m_OrigUDF.m_Type == DT_STRING)  
		&& (newType == DT_INT || newType == DT_FLOAT)) {
		if (AfxMessageBox("If current values exist that can not be converted\n"
						  "to a number, they will be changed to the default value.\n\nDo "
			"you wish to continue?", MB_YESNO|MB_ICONWARNING) != IDYES)
			return;
	}

	// Type changes from float to int, value may be truncated
	if (m_OrigUDF.m_Type == DT_FLOAT && newType == DT_INT) {
		if (AfxMessageBox("Changing from float type to integer may cause\n"
						  "some values to be truncated.\n\nDo you wish to continue?", MB_YESNO|MB_ICONWARNING) != IDYES)
			return;
	}


	if (newType == DT_LIST) {
		for (i=0; i < m_ListElementsCtrl.GetItemCount(); ++i) {
			temp = m_ListElementsCtrl.GetItemText(i, 0);
			listValues.Add(temp);
		}
	}

	// Type stays list, but list values are removed
	if (m_OrigUDF.m_Type == DT_LIST && newType == DT_LIST) {
		found = TRUE;
		for (i=0; i < m_OrigUDF.m_ListValues.GetSize(); ++i) {
			found = FALSE;
			for (j=0; j < listValues.GetSize(); ++j) {
				if (listValues[j] == m_OrigUDF.m_ListValues[i]) {
					found = TRUE;
					break;
				}
			}
			if (! found)
				break;	// stop as soon as we come to a list value that's not in the new list
		}
		
		if (! found) {
			if (AfxMessageBox("Some list values have been removed.\n"
							  "If current values exist that are no longer in the list,\n"
							  "they will be changed to the default value.\n\nDo you wish to continue?", 
							  MB_YESNO|MB_ICONWARNING) != IDYES)
				return;
		}
	}



	m_UDF->m_Name = m_Name;
	m_UDF->m_Type = newType;
	m_UDF->m_DefaultValue = m_DefaultValue;

	m_UDF->m_ListValues.RemoveAll();	
	m_UDF->m_ListValues.Copy(listValues);

	// Set the udf values so later we can update the existing
	// udf values tables
	switch (m_UDF->m_Type) {
	case DT_INT:
	case DT_FLOAT:
		m_UDF->m_IntegerValue = atoi(m_UDF->m_DefaultValue);
		m_UDF->m_FloatValue = atof(m_UDF->m_DefaultValue);
		m_UDF->m_Value = m_UDF->m_DefaultValue;
		break;
	case DT_STRING:
	case DT_LIST:
		// if the string values is numeric, go ahead
		// and set the int/float values to it
		if (utilityHelper.IsNumeric(m_UDF->m_DefaultValue)) {
			m_UDF->m_IntegerValue = atoi(m_UDF->m_DefaultValue);
			m_UDF->m_FloatValue = atof(m_UDF->m_DefaultValue);
		}
		else {
			m_UDF->m_IntegerValue = 0;
			m_UDF->m_FloatValue = 0;
		}
		m_UDF->m_Value = m_UDF->m_DefaultValue;
		break;
	}
	CDialog::OnOK();
}

void CUDFProperties::OnCancel() 
{
	// TODO: Add extra cleanup here
	
	CDialog::OnCancel();
}

void CUDFProperties::OnHelp() 
{
	helpService.ShowScreenHelp(IDD);	
}

void CUDFProperties::OnAddElement() 
{
	int nItem;
	LVITEM lvItem;

	m_Adding = TRUE;
	
	UpdateData(TRUE);

	lvItem.mask = LVIF_TEXT|LVIF_STATE;
	lvItem.stateMask = LVIS_SELECTED;
	lvItem.state = 0;
	lvItem.pszText = "New Value";
	lvItem.iItem = m_ListElementsCtrl.GetItemCount();
	lvItem.iSubItem = 0;
	nItem = m_ListElementsCtrl.InsertItem(&lvItem);

	m_ListElementsCtrl.SetFocus();
	m_ListElementsCtrl.EditLabel(nItem);
	
	UpdateData(FALSE);

	m_Adding = FALSE;

}

void CUDFProperties::OnDeleteElement() 
{
	POSITION pos;
	int curSel;
	CDWordArray list;

	UpdateData(TRUE);

	pos = m_ListElementsCtrl.GetFirstSelectedItemPosition();
	if (pos == NULL) {
		AfxMessageBox("Please select at least one list element to delete.");
		return;
	}

	while (pos != NULL) {
		curSel = m_ListElementsCtrl.GetNextSelectedItem(pos);
		list.Add(curSel);
	}

	for (int i=list.GetSize()-1; i >= 0; --i)
		m_ListElementsCtrl.DeleteItem(list[i]);

	UpdateData(FALSE);

}

void CUDFProperties::OnSelchangeType() 
{
	int curSel;

	curSel = m_TypeCtrl.GetCurSel();
	if (curSel+1 == DT_LIST && m_PreviousType != DT_LIST) {
		m_ListElementsCtrl.ShowWindow(SW_SHOW);
		m_StaticListCtrl.ShowWindow(SW_SHOW);
		m_AddElementCtrl.ShowWindow(SW_SHOW);
		m_DeleteElementCtrl.ShowWindow(SW_SHOW);
	}
	else {
		m_ListElementsCtrl.ShowWindow(SW_HIDE);
		m_StaticListCtrl.ShowWindow(SW_HIDE);
		m_AddElementCtrl.ShowWindow(SW_HIDE);
		m_DeleteElementCtrl.ShowWindow(SW_HIDE);
	}

	m_PreviousType = curSel+1;
}

void CUDFProperties::OnEndlabeleditListElements(NMHDR* pNMHDR, LRESULT* pResult) 
{
	LV_DISPINFO* pDispInfo = (LV_DISPINFO*)pNMHDR;
	LVITEM lvItem;

	lvItem = pDispInfo->item;

	*pResult = 0;

	if (lvItem.pszText == NULL)
		lvItem.pszText = "New Value";

	if (lvItem.pszText != NULL) {
		for (int i=0; i < m_ListElementsCtrl.GetItemCount(); ++i) {
			if (m_ListElementsCtrl.GetItemText(i, 0).Compare(lvItem.pszText) == 0 && lvItem.iItem != i) {
				AfxMessageBox("Please enter a name that does not already exist.");
				m_ListElementsCtrl.EditLabel(lvItem.iItem);
				return;
			}
		}
		*pResult = 1;
	}

}



BOOL CUDFProperties::OnHelpInfo(HELPINFO* pHelpInfo) 
{
	helpService.ShowFieldHelp(IDD, pHelpInfo->iCtrlId);
	return FALSE;
}

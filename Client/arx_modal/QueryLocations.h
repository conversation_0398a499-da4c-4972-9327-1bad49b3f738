#if !defined(AFX_QUERYLOCATIONS_H__7AA6EFF1_7860_11D4_9EAD_00C04FAC149C__INCLUDED_)
#define AFX_QUERYLOCATIONS_H__7AA6EFF1_7860_11D4_9EAD_00C04FAC149C__INCLUDED_

#if _MSC_VER > 1000
#pragma once
#endif // _MSC_VER > 1000
// QueryLocations.h : header file
//

/////////////////////////////////////////////////////////////////////////////
// CQueryLocations dialog

#include "resource.h"

class CQueryLocations : public CDialog
{
// Construction
public:
	CQueryLocations(CWnd* pParent = NULL);   // standard constructor
	CStringArray m_ProductGroupList;
	CStringArray m_Results;
// Dialog Data
	//{{AFX_DATA(CQueryLocations)
	enum { IDD = IDD_QUERY_LOCATIONS };
	CString	m_Description;
	int		m_ProductGroupID;
	int		m_Assigned;
	int		m_LevelType;
	//}}AFX_DATA


// Overrides
	// ClassWizard generated virtual function overrides
	//{{AFX_VIRTUAL(CQueryLocations)
	protected:
	virtual void DoDataExchange(CDataExchange* pDX);    // DDX/DDV support
	//}}AFX_VIRTUAL

// Implementation
protected:

	// Generated message map functions
	//{{AFX_MSG(CQueryLocations)
	virtual BOOL OnInitDialog();
	virtual void OnOK();
	afx_msg BOOL OnHelpInfo(HELPINFO* pHelpInfo);
	afx_msg void OnHelp();
	//}}AFX_MSG
	DECLARE_MESSAGE_MAP()
};

//{{AFX_INSERT_LOCATION}}
// Microsoft Visual C++ will insert additional declarations immediately before the previous line.

#endif // !defined(AFX_QUERYLOCATIONS_H__7AA6EFF1_7860_11D4_9EAD_00C04FAC149C__INCLUDED_)

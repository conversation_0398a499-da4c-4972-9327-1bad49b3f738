// LocationInfo.cpp: implementation of the CLocationQueryInfo class.
//
//////////////////////////////////////////////////////////////////////

#include "stdafx.h"
#include "modal.h"
#include "LocationInfo.h"
#include <dbsymtb.h>

#ifdef _DEBUG
#undef THIS_FILE
static char THIS_FILE[]=__FILE__;
#define new DEBUG_NEW
#endif

//////////////////////////////////////////////////////////////////////
// Construction/Destruction
//////////////////////////////////////////////////////////////////////

CLocationQueryInfo::CLocationQueryInfo()
{
	m_LocationDBID = 0;
	m_LocationDescription = "";
	m_BayProfileDBID = 0;
	m_BayProfileDescription = "";
	m_LevelType = 0;
	m_LocProductGroupDBID = 0;
	m_LocPGDescription = "";
	m_CurrentProductDBID = 0;
	m_WMSProductID = "";
	m_WMSProductDetailID = "";
	m_ProductDescription = "";
	m_ProdProductGroupDBID = 0;
	m_ProdPGDescription = "";
	m_CaseQuantity = 0;
	m_Width = m_Depth = m_Height = 0.0f;
	m_BayDBID = m_LevelDBID = 0;
	m_HandlingMethod = m_IsPrimaryFacing = -1;
	m_RotateAllowed = 0;
}

CLocationQueryInfo::~CLocationQueryInfo()
{

}

CLocationQueryInfo& CLocationQueryInfo::operator=(const CLocationQueryInfo & other)
{
	m_LocationDBID = other.m_LocationDBID;
	m_LocationDescription = other.m_LocationDescription;
	m_BayProfileDBID = other.m_BayProfileDBID;
	m_BayProfileDescription = other.m_BayProfileDescription;
	m_LevelType = other.m_LevelType;
	m_LocProductGroupDBID = other.m_LocProductGroupDBID;
	m_LocPGDescription = other.m_LocPGDescription;
	m_CurrentProductDBID = other.m_CurrentProductDBID;
	m_WMSProductID = other.m_WMSProductID;
	m_WMSProductDetailID = other.m_WMSProductDetailID;
	m_ProductDescription = other.m_ProductDescription;
	m_ProdProductGroupDBID = other.m_ProdProductGroupDBID;
	m_ProdPGDescription = other.m_ProdPGDescription ;
	m_Width = other.m_Width;
	m_Depth = other.m_Depth;
	m_Height = other.m_Height;
	m_BayDBID = other.m_BayDBID;
	m_LevelDBID = other.m_LevelDBID;
	m_IsPrimaryFacing = other.m_IsPrimaryFacing;
	m_HandlingMethod = other.m_HandlingMethod;
	m_CaseQuantity = other.m_CaseQuantity;
	m_RotateAllowed = other.m_RotateAllowed;

	return *this;
}


void CLocationQueryInfo::Parse(CString line)
{
	char *str;
	char *ptr;
	CString temp;
	temp = line;

	try {
		
		line.Replace("||", "| |");
		line.Replace("||", "| |");
		str = line.GetBuffer(0);
		
		ptr = strtok(str, "|");
		m_LocationDBID = atoi(ptr);
		ptr = strtok(NULL, "|");
		m_LocationDescription = ptr;
		ptr = strtok(NULL, "|");
		m_BayProfileDBID = atoi(ptr);
		ptr = strtok(NULL, "|");
		m_BayProfileDescription = ptr;
		ptr = strtok(NULL, "|");
		m_LevelType = atoi(ptr);
		ptr = strtok(NULL, "|");
		if (strcmp(ptr, " ") != 0)
			m_LocProductGroupDBID = atoi(ptr);
		ptr = strtok(NULL, "|");
		if (strcmp(ptr, " ") != 0)
			m_LocPGDescription = ptr;
		ptr = strtok(NULL, "|");
		if (strcmp(ptr, " ") != 0)
			m_CurrentProductDBID = atoi(ptr);
		ptr = strtok(NULL, "|");
		if (strcmp(ptr, " ") != 0)
			m_WMSProductID = ptr;
		ptr = strtok(NULL, "|");
		if (strcmp(ptr, " ") != 0)
			m_WMSProductDetailID = ptr;
		ptr = strtok(NULL, "|");
		if (strcmp(ptr, " ") != 0)
			m_ProductDescription = ptr;
		ptr = strtok(NULL, "|");
		if (strcmp(ptr, " ") != 0)
			m_ProdProductGroupDBID = atoi(ptr);
		ptr = strtok(NULL, "|");
		if (strcmp(ptr, " ") != 0)
			m_ProdPGDescription = ptr;
		ptr = strtok(NULL, "|");
		m_Width = atof(ptr);
		ptr = strtok(NULL, "|");
		m_Depth = atof(ptr);
		ptr = strtok(NULL, "|");
		m_Height = atof(ptr);
		ptr = strtok(NULL, "|");
		m_HandlingMethod = atoi(ptr);
		ptr = strtok(NULL, "|");
		m_BayDBID = atoi(ptr);
		ptr = strtok(NULL, "|");
		m_LevelDBID = atoi(ptr);
		ptr = strtok(NULL, "|");
		if (strcmp(ptr, " ") != 0)
			m_IsPrimaryFacing = atoi(ptr);
		ptr = strtok(NULL, "|");
		if (strcmp(ptr, " ") != 0)
			m_CaseQuantity = atoi(ptr);
		ptr = strtok(NULL, "|");
		if (strcmp(ptr, " ") != 0)
			m_RotateAllowed = atoi(ptr);
		
	line.ReleaseBuffer();
	}
	catch (...) {
		line.ReleaseBuffer();
		ads_printf("Error processing location list.\n");
		ads_printf("%s\n", line);

	}


	return;

}

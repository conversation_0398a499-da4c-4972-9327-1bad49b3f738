#if !defined(AFX_BAYPROFILEDRIVEINPAGE_H__14D40C67_67DF_4B83_9E72_C675152D57DF__INCLUDED_)
#define AFX_BAYPROFILEDRIVEINPAGE_H__14D40C67_67DF_4B83_9E72_C675152D57DF__INCLUDED_

#if _MSC_VER > 1000
#pragma once
#endif // _MSC_VER > 1000
// BayProfileDriveInPage.h : header file
//
#include "BayProfileTopViewButton.h"
#include "BayProfileSideViewButton.h"
#include "BayProfile.h"

/////////////////////////////////////////////////////////////////////////////
// CBayProfileDriveInPage dialog

class CBayProfileDriveInPage : public CPropertyPage
{
	DECLARE_DYNCREATE(CBayProfileDriveInPage)

// Construction
public:
	CBayProfileDriveInPage();
	~CBayProfileDriveInPage();

// Dialog Data
	//{{AFX_DATA(CBayProfileDriveInPage)
	enum { IDD = IDD_BAY_PROFILE_DRIVEIN_ATTRIBUTES };
	CBayProfileTopViewButton	m_TopViewButton;
	CBayProfileSideViewButton	m_SideViewButton;
	double	m_BayDepth;
	double	m_BayHeight;
	double	m_BayWidth;
	double	m_Clearance;
	double	m_Overhang;
	int		m_SelectStackPositions;
	double	m_StackDepth;
	double	m_StackHeight;
	double	m_StackWidth;
	double	m_UprightHeight;
	double	m_UprightWidth;
	double	m_WeightCapacity;
	//}}AFX_DATA


// Overrides
	// ClassWizard generate virtual function overrides
	//{{AFX_VIRTUAL(CBayProfileDriveInPage)
	public:
	virtual BOOL OnKillActive();
	virtual BOOL OnSetActive();
	protected:
	virtual void DoDataExchange(CDataExchange* pDX);    // DDX/DDV support
	virtual BOOL OnCommand(WPARAM wParam, LPARAM lParam);
	virtual BOOL OnNotify(WPARAM wParam, LPARAM lParam, LRESULT* pResult);
	//}}AFX_VIRTUAL

// Implementation
protected:
	// Generated message map functions
	//{{AFX_MSG(CBayProfileDriveInPage)
	virtual BOOL OnInitDialog();
	afx_msg BOOL OnHelpInfo(HELPINFO* pHelpInfo);
	//}}AFX_MSG
	DECLARE_MESSAGE_MAP()

private:
	BOOL Validate();
	CBayProfile *m_pBayProfile;
	BOOL m_Validating;
};

//{{AFX_INSERT_LOCATION}}
// Microsoft Visual C++ will insert additional declarations immediately before the previous line.

#endif // !defined(AFX_BAYPROFILEDRIVEINPAGE_H__14D40C67_67DF_4B83_9E72_C675152D57DF__INCLUDED_)

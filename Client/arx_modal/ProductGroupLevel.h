// ProductGroupLevel.h: interface for the CProductGroupLevel class.
//
//////////////////////////////////////////////////////////////////////

#if !defined(AFX_PRODUCTGROUPLEVEL_H__0FF17F0A_3BFD_46DB_ACBB_0985991AF581__INCLUDED_)
#define AFX_PRODUCTGROUPLEVEL_H__0FF17F0A_3BFD_46DB_ACBB_0985991AF581__INCLUDED_

#if _MSC_VER > 1000
#pragma once
#endif // _MSC_VER > 1000

class CProductGroupLevel : public CObject  
{
public:
	CProductGroupLevel();
	virtual ~CProductGroupLevel();
	
	long m_ProductGroupLevelDBID;
	long m_ProductGroupDBID;
	long m_BayDBID;
	long m_LevelDBID;

};

#endif // !defined(AFX_PRODUCTGROUPLEVEL_H__0FF17F0A_3BFD_46DB_ACBB_0985991AF581__INCLUDED_)

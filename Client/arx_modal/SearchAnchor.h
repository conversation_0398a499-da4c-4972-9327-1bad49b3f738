// SearchAnchor.h: interface for the CSearchAnchor class.
//
//////////////////////////////////////////////////////////////////////

#if !defined(AFX_SEARCHANCHOR_H__18F84311_BA68_11D4_9EBD_00C04FAC149C__INCLUDED_)
#define AFX_SEARCHANCHOR_H__18F84311_BA68_11D4_9EBD_00C04FAC149C__INCLUDED_

#if _MSC_VER > 1000
#pragma once
#endif // _MSC_VER > 1000

class CSearchAnchor  
{
public:
	CString Stream();
	int m_SearchAnchorDBID;
	int Parse(CString &line);
	CString m_SearchAnchorPoint;
	CString m_EndingLocation;
	CString m_StartingLocation;
	CSearchAnchor();
	virtual ~CSearchAnchor();

};

#endif // !defined(AFX_SEARCHANCHOR_H__18F84311_BA68_11D4_9EBD_00C04FAC149C__INCLUDED_)

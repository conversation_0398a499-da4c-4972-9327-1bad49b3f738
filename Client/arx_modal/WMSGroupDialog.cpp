// WMSGroupDialog.cpp : implementation file
//

#include "stdafx.h"
#include "modal.h"
#include "WMSGroupDialog.h"
#include "WMSGroupProperties.h"
#include "WMSProperties.h"
#include "UtilityHelper.h"
#include "IntegrationDataService.h"
#include "HelpService.h"

#ifdef _DEBUG
#define new DEBUG_NEW
#undef THIS_FILE
static char THIS_FILE[] = __FILE__;
#endif

#define TREELINEHEIGHT 20

/////////////////////////////////////////////////////////////////////////////
// CWMSGroupDialog dialog

extern CUtilityHelper utilityHelper;
extern CIntegrationDataService integrationDataService;
extern CHelpService helpService;

CWMSGroupDialog::CWMSGroupDialog(CWnd* pParent /*=NULL*/)
	: CDialog(CWMSGroupDialog::IDD, pParent)
{
	//{{AFX_DATA_INIT(CWMSGroupDialog)
		// NOTE: the ClassWizard will add member initialization here
	//}}AFX_DATA_INIT
	m_SomethingChanged = FALSE;
}

CWMSGroupDialog::~CWMSGroupDialog()
{
	for (int i=0; i < m_WMSList.GetSize(); ++i)
		delete m_WMSList[i];

	for (i=0; i < m_GroupList.GetSize(); ++i)
		delete m_GroupList[i];

}

void CWMSGroupDialog::DoDataExchange(CDataExchange* pDX)
{
	CDialog::DoDataExchange(pDX);
	//{{AFX_DATA_MAP(CWMSGroupDialog)
	DDX_Control(pDX, IDC_WMS_GROUP_TREE, m_GroupTreeCtrl);
	DDX_Control(pDX, IDC_COMMAND_TREE, m_CommandTreeCtrl);
	//}}AFX_DATA_MAP
}


BEGIN_MESSAGE_MAP(CWMSGroupDialog, CDialog)
	//{{AFX_MSG_MAP(CWMSGroupDialog)
	ON_NOTIFY(TVN_ITEMEXPANDED, IDC_COMMAND_TREE, OnItemexpandedCommandTree)
	ON_NOTIFY(TVN_SELCHANGED, IDC_WMS_GROUP_TREE, OnSelchangedWmsGroupTree)
	ON_WM_CONTEXTMENU()
	ON_COMMAND(ID_GENERIC_ADD, OnGenericAdd)
	ON_COMMAND(ID_GENERIC_DELETE, OnGenericDelete)
	ON_COMMAND(ID_GENERIC_PROPERTIES, OnGenericProperties)
	ON_NOTIFY(TVN_SELCHANGING, IDC_COMMAND_TREE, OnSelchangingCommandTree)
	ON_NOTIFY(NM_DBLCLK, IDC_WMS_GROUP_TREE, OnDblclkWmsGroupTree)
	ON_MESSAGE(MESSAGE_DROP, (LRESULT (AFX_MSG_CALL CWnd::*)(WPARAM, LPARAM))OnDropItem)
	ON_WM_HELPINFO()
	ON_BN_CLICKED(IDHELP, OnHelp)
	//}}AFX_MSG_MAP
END_MESSAGE_MAP()

/////////////////////////////////////////////////////////////////////////////
// CWMSGroupDialog message handlers

BOOL CWMSGroupDialog::OnInitDialog() 
{
	CDialog::OnInitDialog();
	
	m_CmdImageList.Create(16, 16, TRUE, 5, 1);

	m_CmdImageList.Add(AfxGetApp()->LoadIcon(IDI_EXPAND_ARROW_UP));
	m_CmdImageList.Add(AfxGetApp()->LoadIcon(IDI_EXPAND_ARROW_DOWN));
	m_CmdImageList.Add(AfxGetApp()->LoadIcon(IDI_ADD));			// Group Add = 2
	m_CmdImageList.Add(AfxGetApp()->LoadIcon(IDI_ADD_BLUE));	// WMS Add = 3
	m_CmdImageList.Add(AfxGetApp()->LoadIcon(IDI_SUBTRACT));	// Subtract = 4
	m_CmdImageList.Add(AfxGetApp()->LoadIcon(IDI_INFO));		// Info = 5

	m_CommandTreeCtrl.SetImageList(&m_CmdImageList, TVSIL_NORMAL);

	
	m_RootCmdTreeItem = m_CommandTreeCtrl.InsertItem("Actions...", 1, 1, TVI_ROOT, TVI_LAST);
	m_AddGroupCmdTreeItem = m_CommandTreeCtrl.InsertItem("Add a WMS Facility", 2, 2, m_RootCmdTreeItem, TVI_LAST);
	m_AddWMSCmdTreeItem = m_CommandTreeCtrl.InsertItem("Add a WMS Section", 3, 3, m_RootCmdTreeItem, TVI_LAST);

	m_DeleteCmdTreeItem = NULL;
	m_EditCmdTreeItem = NULL;

	// Position the action window so that only one line displays (the 4 is for the border)
	CRect r;
	m_CommandTreeCtrl.GetWindowRect(&r);
	this->ScreenToClient(&r);
	r.bottom = r.top + TREELINEHEIGHT + 4;
	m_CommandTreeCtrl.MoveWindow(&r, FALSE);

	LoadGroups();

	return TRUE;  // return TRUE unless you set the focus to a control
	              // EXCEPTION: OCX Property Pages should return FALSE
}


void CWMSGroupDialog::OnItemexpandedCommandTree(NMHDR* pNMHDR, LRESULT* pResult) 
{
	NM_TREEVIEW* pNMTreeView = (NM_TREEVIEW*)pNMHDR;

	TVITEM tvItem = pNMTreeView->itemNew;

	if (m_CommandTreeCtrl.GetParentItem(tvItem.hItem) == NULL) {
		if (pNMTreeView->action == TVE_EXPAND) {
			ExpandCommandTree();
		}
		else {
			CollapseCommandTree();
		}
	}
	
	*pResult = 0;
}


void CWMSGroupDialog::OnSelchangedWmsGroupTree(NMHDR* pNMHDR, LRESULT* pResult) 
{
	NM_TREEVIEW* pNMTreeView = (NM_TREEVIEW*)pNMHDR;
	HTREEITEM hItem = pNMTreeView->itemNew.hItem;

	int origState = 0;		// collapsed
	if (m_CommandTreeCtrl.GetItemState(m_RootCmdTreeItem, TVIF_STATE) & TVIS_EXPANDED) {
		origState = 1;
	}
	
	if (hItem != NULL) {
		if (m_GroupTreeCtrl.GetParentItem(hItem) == NULL) {
			if (m_DeleteCmdTreeItem != NULL) {
				m_CommandTreeCtrl.SetItemText(m_DeleteCmdTreeItem, "Delete selected facility.");
				m_CommandTreeCtrl.SetItemText(m_EditCmdTreeItem, "View/modify selected facility.");
			}
			else {
				if (origState == 1)
					CollapseCommandTree();
				m_EditCmdTreeItem = m_CommandTreeCtrl.InsertItem("View/modify selected facility", 5,5, m_RootCmdTreeItem, TVI_LAST);
				m_DeleteCmdTreeItem = m_CommandTreeCtrl.InsertItem("Delete selected facility", 4, 4, m_RootCmdTreeItem, TVI_LAST);
				if (origState == 1)
					ExpandCommandTree();
			}

		}
		else {
			if (m_DeleteCmdTreeItem != NULL) {
				m_CommandTreeCtrl.SetItemText(m_DeleteCmdTreeItem, "Delete selected section.");
				m_CommandTreeCtrl.SetItemText(m_EditCmdTreeItem, "View/modify selected section.");
			}
			else {
				if (origState == 1)
					CollapseCommandTree();
				m_EditCmdTreeItem = m_CommandTreeCtrl.InsertItem("View/modify selected section", 5,5, m_RootCmdTreeItem, TVI_LAST);
				m_DeleteCmdTreeItem = m_CommandTreeCtrl.InsertItem("Delete selected section", 4, 4, m_RootCmdTreeItem, TVI_LAST);
				if (origState == 1)
					ExpandCommandTree();
			}

		}
	}
	else {
		if (m_DeleteCmdTreeItem != NULL) {
			m_CommandTreeCtrl.DeleteItem(m_DeleteCmdTreeItem);
			m_CommandTreeCtrl.DeleteItem(m_EditCmdTreeItem);
		}
	}


	*pResult = 0;
}

void CWMSGroupDialog::OnContextMenu(CWnd* pWnd, CPoint point) 
{
	if (pWnd != &m_GroupTreeCtrl)
		return;

	CMenu menu;
	menu.LoadMenu(IDR_GENERIC_MENU);
	CPoint pt = point;

	m_GroupTreeCtrl.ScreenToClient(&pt);

	UINT uFlags;
	HTREEITEM hItem = m_GroupTreeCtrl.HitTest(pt, &uFlags);

	if (hItem != NULL && (uFlags & (TVHT_ONITEM|TVHT_ONITEMRIGHT))) {
		m_GroupTreeCtrl.SelectItem(hItem);
	}
	else {
		return;
	}

	CString temp = m_GroupTreeCtrl.GetItemText(hItem);
	if (hItem != NULL) {
		if (m_GroupTreeCtrl.GetParentItem(hItem) == NULL) {			// Group selected
			menu.GetSubMenu(0)->ModifyMenu(0, MF_BYPOSITION|MF_STRING, ID_GENERIC_ADD, "Add WMS Facility");
			menu.GetSubMenu(0)->ModifyMenu(1, MF_BYPOSITION|MF_STRING, ID_GENERIC_PROPERTIES, "View/modify selected facility");
			menu.GetSubMenu(0)->ModifyMenu(2, MF_BYPOSITION|MF_STRING, ID_GENERIC_DELETE, "Delete selected facility");
		}
		else {		// WMS Selectec
			menu.GetSubMenu(0)->ModifyMenu(0, MF_BYPOSITION|MF_STRING, ID_GENERIC_ADD, "Add WMS Section");
			menu.GetSubMenu(0)->ModifyMenu(1, MF_BYPOSITION|MF_STRING, ID_GENERIC_PROPERTIES, "View/modify selected section");
			menu.GetSubMenu(0)->ModifyMenu(2, MF_BYPOSITION|MF_STRING, ID_GENERIC_DELETE, "Delete selected section");
		}
	}


	menu.GetSubMenu(0)->TrackPopupMenu(TPM_LEFTALIGN|TPM_RIGHTBUTTON, point.x, point.y, this);

}

void CWMSGroupDialog::ExpandCommandTree()
{
	int itemCount = m_CommandTreeCtrl.GetCount();

	m_CommandTreeCtrl.SetItemImage(m_RootCmdTreeItem, 0, 0);
	CRect r;
	m_CommandTreeCtrl.GetWindowRect(&r);
	this->ScreenToClient(&r);
	r.bottom += (TREELINEHEIGHT-4) * (itemCount-1);
	m_CommandTreeCtrl.MoveWindow(&r, TRUE);
	m_GroupTreeCtrl.GetWindowRect(&r);
	this->ScreenToClient(&r);
	r.top += (TREELINEHEIGHT-4) * (itemCount-1);
	m_GroupTreeCtrl.MoveWindow(&r, TRUE);

}

void CWMSGroupDialog::CollapseCommandTree()
{	
	int itemCount = m_CommandTreeCtrl.GetCount();
	
	m_CommandTreeCtrl.SetItemImage(m_RootCmdTreeItem, 1, 1);
	CRect r;
	m_CommandTreeCtrl.GetWindowRect(&r);
	this->ScreenToClient(&r);
	r.bottom -= (TREELINEHEIGHT-4) * (itemCount-1);
	m_CommandTreeCtrl.MoveWindow(&r, TRUE);
	m_GroupTreeCtrl.GetWindowRect(&r);
	this->ScreenToClient(&r);
	r.top -= (TREELINEHEIGHT-4) * (itemCount-1);
	m_GroupTreeCtrl.MoveWindow(&r, TRUE);
}

void CWMSGroupDialog::OnGenericAdd() 
{
	if (SelectedType() == groupSelected)
		AddGroup();
	else if (SelectedType() == wmsSelected)
		AddWMS();
	
}

void CWMSGroupDialog::OnGenericDelete() 
{
	if (SelectedType() == groupSelected)
		DeleteGroup();
	else if (SelectedType() == wmsSelected)
		DeleteWMS();
	
}

void CWMSGroupDialog::OnGenericProperties() 
{
	if (SelectedType() == groupSelected)
		EditGroup();
	else if (SelectedType() == wmsSelected)
		EditWMS();
	
}


void CWMSGroupDialog::AddGroup()
{
	CWMSGroupProperties dlg;

	dlg.m_Group.m_WMSGroupDBId = 0;

	if (dlg.DoModal() != IDOK)
		return;

	try {
		integrationDataService.StoreWMSGroup(dlg.m_Group);
	}
	catch (...) {
		utilityHelper.ProcessError("Error adding WMS Facility.");
		return;
	}

	m_SomethingChanged = TRUE;

	CWMSGroup *pGroup = new CWMSGroup;
	*pGroup = dlg.m_Group;
	HTREEITEM hItem = AddGroupToTree(pGroup);
	m_GroupTreeCtrl.SelectItem(hItem);

}

void CWMSGroupDialog::AddWMS()
{
	CWMSProperties dlg;

	if (m_GroupTreeCtrl.GetCount() == 0) {
		AfxMessageBox("Please add a WMS Facility before adding a WMS Section.");
		return;
	}

	HTREEITEM hItem;
	if (SelectedType() == groupSelected)
		hItem = m_GroupTreeCtrl.GetSelectedItem();
	else if (SelectedType() == wmsSelected)
		hItem = m_GroupTreeCtrl.GetParentItem(m_GroupTreeCtrl.GetSelectedItem());
	else
		hItem = m_GroupTreeCtrl.GetNextItem(NULL, TVGN_CHILD);

	CWMSGroup *pGroup = (CWMSGroup *)m_GroupTreeCtrl.GetItemData(hItem);
	dlg.m_GroupDBId = pGroup->m_WMSGroupDBId;
	if (pGroup->m_ExternalSystemName.Find("5.3") >= 0)
		dlg.m_IdText = "Warehouse Id";
	else if (pGroup->m_ExternalSystemName.Find("3.6") >= 0)
		dlg.m_IdText = "WMS Id";

	if (dlg.DoModal() != IDOK)
		return;

	m_SomethingChanged = TRUE;

	CWMS *pWMS = new CWMS;
	*pWMS = dlg.m_WMS;
	AddWMSToTree(pWMS, hItem);
	
	m_GroupTreeCtrl.Expand(hItem, TVE_EXPAND);

}

void CWMSGroupDialog::EditGroup()
{
	HTREEITEM hItem = m_GroupTreeCtrl.GetSelectedItem();
	if (hItem == NULL)
		return;

	CWMSGroup *pGroup = (CWMSGroup *)m_GroupTreeCtrl.GetItemData(hItem);

	CWMSGroupProperties dlg;
	dlg.m_Group = *pGroup;

	if (dlg.DoModal() != IDOK)
		return;

	try {
		integrationDataService.StoreWMSGroup(dlg.m_Group);
	}
	catch (...) {
		utilityHelper.ProcessError("Error updating WMS Facility.");
		return;
	}

	m_SomethingChanged = TRUE;

	*pGroup = dlg.m_Group;

	m_GroupTreeCtrl.SetItemText(hItem, pGroup->m_Name);

}

void CWMSGroupDialog::EditWMS()
{
	HTREEITEM hItem = m_GroupTreeCtrl.GetSelectedItem();
	if (hItem == NULL)
		return;
	
	HTREEITEM hParentItem = m_GroupTreeCtrl.GetParentItem(hItem);

	CWMSProperties dlg;

	CWMSGroup *pGroup = (CWMSGroup *)m_GroupTreeCtrl.GetItemData(hParentItem);
	dlg.m_GroupDBId = pGroup->m_WMSGroupDBId;
	if (pGroup->m_ExternalSystemName.Find("5.3") >= 0)
		dlg.m_IdText = "Warehouse Id";
	else if (pGroup->m_ExternalSystemName.Find("3.6") >= 0)
		dlg.m_IdText = "WMS Id";


	CWMS *pWMS = (CWMS *)m_GroupTreeCtrl.GetItemData(hItem);


	dlg.m_WMS = *pWMS;

	if (dlg.DoModal() != IDOK)
		return;

	try {
		integrationDataService.StoreWMS(dlg.m_WMS);
	}
	catch (...) {
		utilityHelper.ProcessError("Error updating WMS Section.");
		return;
	}
	
	m_SomethingChanged = TRUE;

	*pWMS = dlg.m_WMS;
	m_GroupTreeCtrl.SetItemText(hItem, pWMS->m_Name);

}

void CWMSGroupDialog::DeleteGroup()
{
	HTREEITEM hItem = m_GroupTreeCtrl.GetSelectedItem();
	if (hItem == NULL)
		return;

	if (m_GroupTreeCtrl.ItemHasChildren(hItem)) {
		AfxMessageBox("A facility that contains a section cannot be deleted.");
		return;
	}

	CWMSGroup *pGroup = (CWMSGroup *)m_GroupTreeCtrl.GetItemData(hItem);

	CString temp;
	temp.Format("Are you sure you wish to delete the WMS Facility: %s?", pGroup->m_Name);
	if (AfxMessageBox(temp, MB_YESNO) != IDYES)
		return;

	try {
		integrationDataService.DeleteWMSGroup(pGroup->m_WMSGroupDBId);
	}
	catch (...) {
		utilityHelper.ProcessError("Error deleting facility from database.");
		return;
	}

	m_SomethingChanged = TRUE;
	m_GroupTreeCtrl.DeleteItem(hItem);

}

void CWMSGroupDialog::DeleteWMS()
{
	HTREEITEM hItem = m_GroupTreeCtrl.GetSelectedItem();
	if (hItem == NULL)
		return;

	CWMS *pWMS = (CWMS *)m_GroupTreeCtrl.GetItemData(hItem);

	CString temp;
	temp.Format("Are you sure you wish to delete the WMS Section: %s?", pWMS->m_Name);
	if (AfxMessageBox(temp, MB_YESNO) != IDYES)
		return;

	try {
		integrationDataService.DeleteWMS(pWMS->m_WMSDBId);
	}
	catch (...) {
		utilityHelper.ProcessError("Error deleting WMS Section from database.");
		return;
	}

	m_SomethingChanged = TRUE;

	m_GroupTreeCtrl.DeleteItem(hItem);
}

int CWMSGroupDialog::SelectedType()
{
	HTREEITEM hItem = m_GroupTreeCtrl.GetSelectedItem();

	if (hItem == NULL)
		return 0;			// nothing selected

	if (m_GroupTreeCtrl.GetParentItem(hItem) == NULL)
		return 1;			// group selected

	return 2;				// WMS selected
}

int CWMSGroupDialog::LoadGroups()
{
	CStringArray wmsList, groupList;

	m_GroupImageList.Create(16, 16, TRUE, 2, 2);
	m_GroupImageList.Add(AfxGetApp()->LoadIcon(IDI_BOX));
	m_GroupImageList.Add(AfxGetApp()->LoadIcon(IDI_FORKLIFT2));
	m_GroupTreeCtrl.SetImageList(&m_GroupImageList, TVSIL_NORMAL);

	try {
		integrationDataService.GetWMSGroupList(groupList);
	}
	catch (...) {
		utilityHelper.ProcessError("Error loading WMS Facilities.");
		return -1;
	}

	for (int i=0; i < groupList.GetSize(); ++i) {
		CWMSGroup *pGroup = new CWMSGroup;
		pGroup->Parse(groupList[i]);
		m_GroupList.Add(pGroup);
	}

	CMap<int, int, HTREEITEM, HTREEITEM&> mapGroupToTree;

	for (i=0; i < m_GroupList.GetSize(); ++i) {
		CWMSGroup *pGroup = m_GroupList[i];
		HTREEITEM hItem = AddGroupToTree(pGroup);
		mapGroupToTree.SetAt(pGroup->m_WMSGroupDBId, hItem);
	}
	
	try {
		integrationDataService.GetWMSList(0, wmsList);
	}
	catch (...) {
		utilityHelper.ProcessError("Error loading WMS Section list.");
		return -1;
	}

	HTREEITEM hGroupItem;

	for (i=0; i < wmsList.GetSize(); ++i) {
		CWMS *pWMS = new CWMS;
		pWMS->Parse(wmsList[i]);
		
		if (mapGroupToTree.Lookup(pWMS->m_GroupDBId, hGroupItem))
			AddWMSToTree(pWMS, hGroupItem);
	}

	return 0;

}

HTREEITEM CWMSGroupDialog::AddGroupToTree(CWMSGroup *pGroup)
{
	HTREEITEM hNewItem;

	hNewItem = m_GroupTreeCtrl.InsertItem(pGroup->m_Name, 0, 0, TVI_ROOT, TVI_LAST);
	m_GroupTreeCtrl.SetItemData(hNewItem, (unsigned long)pGroup);

	return hNewItem;
}

HTREEITEM CWMSGroupDialog::AddWMSToTree(CWMS *pWMS, HTREEITEM hItem)
{
	HTREEITEM hNewItem;

	hNewItem = m_GroupTreeCtrl.InsertItem(pWMS->m_Name, 1, 1, hItem, TVI_LAST);
	m_GroupTreeCtrl.SetItemData(hNewItem, (unsigned long)pWMS);

	return hNewItem;
}

void CWMSGroupDialog::OnSelchangingCommandTree(NMHDR* pNMHDR, LRESULT* pResult) 
{
	NM_TREEVIEW* pNMTreeView = (NM_TREEVIEW*)pNMHDR;
	
	HTREEITEM hItem = pNMTreeView->itemNew.hItem;

	*pResult = 0;
	if (hItem == m_AddGroupCmdTreeItem) {
		AddGroup();
		*pResult = 1;
	}
	else if (hItem == m_AddWMSCmdTreeItem) {
		AddWMS();
		*pResult = 1;
	}
	else if (hItem == m_EditCmdTreeItem) {
		if (SelectedType() == groupSelected)
			EditGroup();
		else if (SelectedType() == wmsSelected)
			EditWMS();
		*pResult = 1;
	}
	else if (hItem == m_DeleteCmdTreeItem) {
		if (SelectedType() == groupSelected)
			DeleteGroup();
		else if (SelectedType() == wmsSelected)
			DeleteWMS();
		*pResult = 1;
	}

	return;
}

int CWMSGroupDialog::MoveWMS(CWMS *pWMS, CWMSGroup *pFromGroup, CWMSGroup *pToGroup)
{
	pWMS->m_GroupDBId = pToGroup->m_WMSGroupDBId;
	pWMS->m_GroupName = pToGroup->m_Name;

	try {
		integrationDataService.StoreWMS(*pWMS);
	}
	catch (...) {
		AfxMessageBox("Error moving section to facility.");
		pWMS->m_GroupDBId = pFromGroup->m_WMSGroupDBId;
		pWMS->m_GroupName = pFromGroup->m_Name;
		return -1;
	}

	for (int i=0; i < pFromGroup->m_WMSList.GetSize(); ++i) {
		if (pFromGroup->m_WMSList[i]->m_WMSDBId == pWMS->m_WMSDBId) {
			pFromGroup->m_WMSList.RemoveAt(i);
			break;
		}
	}

	pToGroup->m_WMSList.Add(pWMS);

	m_SomethingChanged = TRUE;

	return 0;
}


int CWMSGroupDialog::OnDropItem(WPARAM wParam, LPARAM lParam)
{
	HTREEITEM hDest = (HTREEITEM)wParam;
	HTREEITEM hSrc = (HTREEITEM)lParam;


	CString temp;
	temp.Format("src: %s, dest: %s", m_GroupTreeCtrl.GetItemText(hSrc),
		m_GroupTreeCtrl.GetItemText(hDest));
//	AfxMessageBox(temp);

	CWMS *pWMS = (CWMS *)m_GroupTreeCtrl.GetItemData(hSrc);
	CWMSGroup *pFromGroup = (CWMSGroup *)m_GroupTreeCtrl.GetItemData(m_GroupTreeCtrl.GetParentItem(hSrc));
	CWMSGroup *pToGroup = (CWMSGroup *)m_GroupTreeCtrl.GetItemData(hDest);
	
	if (MoveWMS(pWMS, pFromGroup, pToGroup) < 0)
		return 0;

	return 1;

}	



void CWMSGroupDialog::OnDblclkWmsGroupTree(NMHDR* pNMHDR, LRESULT* pResult) 
{
	OnGenericProperties();

	*pResult = 0;
}

BOOL CWMSGroupDialog::OnHelpInfo(HELPINFO* pHelpInfo) 
{
	helpService.ShowFieldHelp(IDD, pHelpInfo->iCtrlId);

	return FALSE;

}

void CWMSGroupDialog::OnHelp() 
{
	helpService.ShowScreenHelp(IDD);

	return;

}
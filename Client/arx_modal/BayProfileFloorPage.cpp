// BayProfileFloorPage.cpp : implementation file
//

#include "stdafx.h"
#include "modal.h"
#include "BayProfileFloorPage.h"
#include "BayProfileSheet.h"
#include "UtilityHelper.h"
#include "Constants.h"
#include "HelpService.h"

#ifdef _DEBUG
#define new DEBUG_NEW
#undef THIS_FILE
static char THIS_FILE[] = __FILE__;
#endif

extern CUtilityHelper utilityHelper;
extern CHelpService helpService;
/////////////////////////////////////////////////////////////////////////////
// CBayProfileFloorPage property page

IMPLEMENT_DYNCREATE(CBayProfileFloorPage, CPropertyPage)

CBayProfileFloorPage::CBayProfileFloorPage() : CPropertyPage(CBayProfileFloorPage::IDD)
{
	//{{AFX_DATA_INIT(CBayProfileFloorPage)
	m_BayHeight = 0.0;
	m_ReserveStackHeight = 0.0;
	m_ReserveStackPositions = 0.0;
	m_SelectStackHeight = 0.0;
	m_SelectStackPositions = 0.0;
	m_StackDepth = 0.0;
	m_StackWidth = 0.0;
	m_WeightCapacity = 0.0;
	m_BayDepth = 0.0;
	m_BayWidth = 0.0;
	//}}AFX_DATA_INIT
	m_Validating = FALSE;
	m_SideViewButton.m_DimensionInfo.m_BayType = BAYTYPE_FLOOR;
	m_TopViewButton.m_DimensionInfo.m_BayType = BAYTYPE_FLOOR;
}

CBayProfileFloorPage::~CBayProfileFloorPage()
{
}

void CBayProfileFloorPage::DoDataExchange(CDataExchange* pDX)
{
	CPropertyPage::DoDataExchange(pDX);
	//{{AFX_DATA_MAP(CBayProfileFloorPage)
	DDX_Control(pDX, IDC_TOP_VIEW_BUTTON, m_TopViewButton);
	DDX_Control(pDX, IDC_SIDE_VIEW_BUTTON, m_SideViewButton);
	DDX_Text(pDX, IDC_BAY_HEIGHT, m_BayHeight);
	DDV_MinMaxDouble(pDX, m_BayHeight, 0., 999999999.);
	DDX_Text(pDX, IDC_RESERVE_STACK_HEIGHT, m_ReserveStackHeight);
	DDV_MinMaxDouble(pDX, m_ReserveStackHeight, 0., 999999999.);
	DDX_Text(pDX, IDC_RESERVE_STACK_POSITIONS, m_ReserveStackPositions);
	DDV_MinMaxDouble(pDX, m_ReserveStackPositions, 0., 999999999.);
	DDX_Text(pDX, IDC_SELECTION_STACK_HEIGHT, m_SelectStackHeight);
	DDV_MinMaxDouble(pDX, m_SelectStackHeight, 0., 999999999.);
	DDX_Text(pDX, IDC_SELECTION_STACK_POSITIONS, m_SelectStackPositions);
	DDV_MinMaxDouble(pDX, m_SelectStackPositions, 0., 999999999.);
	DDX_Text(pDX, IDC_STACK_DEPTH, m_StackDepth);
	DDV_MinMaxDouble(pDX, m_StackDepth, 0., 999999999.);
	DDX_Text(pDX, IDC_STACK_WIDTH, m_StackWidth);
	DDV_MinMaxDouble(pDX, m_StackWidth, 0., 999999999.);
	DDX_Text(pDX, IDC_WEIGHT_CAPACITY, m_WeightCapacity);
	DDV_MinMaxDouble(pDX, m_WeightCapacity, 0., 999999999.);
	DDX_Text(pDX, IDC_BAY_DEPTH, m_BayDepth);
	DDV_MinMaxDouble(pDX, m_BayDepth, 0., 999999999.);
	DDX_Text(pDX, IDC_BAY_WIDTH, m_BayWidth);
	DDV_MinMaxDouble(pDX, m_BayWidth, 0., 999999999.);
	//}}AFX_DATA_MAP
	
	m_SideViewButton.m_DimensionInfo.m_BayDepth = m_BayDepth;
	m_SideViewButton.m_DimensionInfo.m_BayHeight = m_BayHeight;
	m_SideViewButton.m_DimensionInfo.m_BayWidth = m_BayWidth;
	m_SideViewButton.m_DimensionInfo.m_BayType = BAYTYPE_FLOOR;
	m_SideViewButton.m_DimensionInfo.m_StackDepth = m_StackDepth;
	m_SideViewButton.m_DimensionInfo.m_StackWidth = m_StackWidth;
	m_SideViewButton.m_DimensionInfo.m_SelectPositions = m_SelectStackPositions;
	m_SideViewButton.m_DimensionInfo.m_SelectPositionHeight = m_SelectStackHeight;
	m_SideViewButton.m_DimensionInfo.m_ReservePositions = m_ReserveStackPositions;
	m_SideViewButton.m_DimensionInfo.m_ReservePositionHeight = m_ReserveStackHeight;
	
	
	m_TopViewButton.m_DimensionInfo = m_SideViewButton.m_DimensionInfo;

}


BEGIN_MESSAGE_MAP(CBayProfileFloorPage, CPropertyPage)
	//{{AFX_MSG_MAP(CBayProfileFloorPage)
	ON_WM_HELPINFO()
	//}}AFX_MSG_MAP
END_MESSAGE_MAP()

/////////////////////////////////////////////////////////////////////////////
// CBayProfileFloorPage message handlers

BOOL CBayProfileFloorPage::OnInitDialog() 
{
	CPropertyPage::OnInitDialog();
	
	CBayProfileSheet *pSheet = (CBayProfileSheet *)GetParent();
	m_pBayProfile = pSheet->m_pBayProfile;

	if (m_pBayProfile->m_BayProfileDBId > 0) {	
		m_BayDepth = m_pBayProfile->m_Depth;
		m_BayHeight = m_pBayProfile->m_Height;
		m_BayWidth = m_pBayProfile->m_Width;

		if (m_pBayProfile->m_LevelProfileList.GetSize() == 0) {
			AfxMessageBox("Invalid drive-in rack.  No levels specified.");
			AfxThrowUserException();
		}

		m_StackDepth = m_pBayProfile->m_LevelProfileList[0]->m_StackDepth;
		m_StackWidth = m_pBayProfile->m_LevelProfileList[0]->m_StackWidth;
		m_SelectStackPositions = m_pBayProfile->m_LevelProfileList[0]->m_SelectPositions;
		m_SelectStackHeight = m_pBayProfile->m_LevelProfileList[0]->m_SelectPositionHeight;
		m_ReserveStackPositions = m_pBayProfile->m_LevelProfileList[0]->m_ReservePositions;
		m_ReserveStackHeight = m_pBayProfile->m_LevelProfileList[0]->m_ReservePositionHeight;

		m_WeightCapacity = m_pBayProfile->m_WeightCapacity;

		UpdateData(FALSE);
	}
	else
		m_pBayProfile->m_LevelProfileList.Add(new CLevelProfile());
	
	
	return TRUE;  // return TRUE unless you set the focus to a control
	              // EXCEPTION: OCX Property Pages should return FALSE
}

BOOL CBayProfileFloorPage::OnKillActive() 
{
	UpdateData(TRUE);

	m_pBayProfile->m_Width = m_BayWidth;
	m_pBayProfile->m_Depth = m_BayDepth;
	m_pBayProfile->m_Height = m_BayHeight;
	m_pBayProfile->m_WeightCapacity = m_WeightCapacity;
	m_pBayProfile->m_UprightWidth = 0;

	if (m_pBayProfile->m_LevelProfileList.GetSize() == 0) {
		AfxMessageBox("Invalid drive-in rack.  No levels specified.");
		AfxThrowUserException();
	}
	
	m_pBayProfile->m_LevelProfileList[0]->m_StackDepth = m_StackDepth;
	m_pBayProfile->m_LevelProfileList[0]->m_StackWidth = m_StackWidth;
	m_pBayProfile->m_LevelProfileList[0]->m_SelectPositionHeight = m_SelectStackHeight;
	m_pBayProfile->m_LevelProfileList[0]->m_SelectPositions = m_SelectStackPositions;
	m_pBayProfile->m_LevelProfileList[0]->m_ReservePositions = m_ReserveStackPositions;
	m_pBayProfile->m_LevelProfileList[0]->m_ReservePositionHeight = m_ReserveStackHeight;
	m_pBayProfile->m_LevelProfileList[0]->m_WeightCapacity = m_WeightCapacity;

	int totalPositions = m_SelectStackPositions + m_ReserveStackPositions;

	CLocationProfile *pLocProfile;
	if (m_pBayProfile->m_LevelProfileList[0]->m_LocationProfileList.GetSize() == 0) {
		pLocProfile = new CLocationProfile;
		m_pBayProfile->m_LevelProfileList[0]->m_LocationProfileList.Add(pLocProfile);
	}
	else
		pLocProfile = m_pBayProfile->m_LevelProfileList[0]->m_LocationProfileList[0];

	pLocProfile->m_Description = "1";
	pLocProfile->m_Depth = m_StackDepth;
	pLocProfile->m_Width = m_StackWidth;
	pLocProfile->m_Height = m_SelectStackHeight;
	pLocProfile->m_HandlingMethod = PALLET_HANDLING;
	pLocProfile->m_WeightCapacity = m_WeightCapacity/totalPositions;

	if (m_SelectStackPositions > 0)
		pLocProfile->m_IsSelect = 1;
	else
		pLocProfile->m_IsSelect = 0;

	pLocProfile->m_LocationSpace = (m_BayWidth - m_StackWidth)/2;
	pLocProfile->m_Coordinates.m_X = pLocProfile->m_LocationSpace;


	if (! Validate())
		return 0;
	
	return CPropertyPage::OnKillActive();
}


BOOL CBayProfileFloorPage::OnCommand(WPARAM wParam, LPARAM lParam) 
{
	if (HIWORD(wParam) == EN_KILLFOCUS && ! m_Validating) {
		m_Validating = TRUE;
		if (! UpdateData(TRUE))
			m_Validating = FALSE;
		else {
			m_SideViewButton.Invalidate(TRUE);
			m_TopViewButton.Invalidate(TRUE);
		}
		m_Validating = FALSE;
	}
	
	return CPropertyPage::OnCommand(wParam, lParam);
}


BOOL CBayProfileFloorPage::Validate()
{
	if (m_StackWidth > m_BayWidth) {
		AfxMessageBox("The Stack Width must be less than or equal to the Bay Width.");
		return utilityHelper.SetEditControlErrorState(this, IDC_STACK_WIDTH);
	}

	if (m_SelectStackHeight > m_BayHeight) {
		AfxMessageBox("The Selection Stack Height must be less than or equal to the Bay Height.");
		return utilityHelper.SetEditControlErrorState(this, IDC_SELECTION_STACK_HEIGHT);
	}

	if (m_ReserveStackHeight > m_BayHeight) {
		AfxMessageBox("The Reserve Stack Height must be less than or equal to the Bay Height.");
		return utilityHelper.SetEditControlErrorState(this, IDC_RESERVE_STACK_HEIGHT);
	}

	int totalPositions = m_SelectStackPositions + m_ReserveStackPositions;
	if (totalPositions == 0) {
		AfxMessageBox("There must be at least one selection position or one reserve position.");
		return utilityHelper.SetEditControlErrorState(this, IDC_SELECTION_STACK_POSITIONS);
	}

	if (m_StackDepth * totalPositions > m_BayDepth) {
		double maxStackDepth = m_BayDepth / totalPositions;
		CString temp;
		temp.Format("To accommodate the specified number of positions within the \n"
			"depth of the bay, the maximum possible stack depth is %.0f.\n"
			"Please increase the bay depth or decrease the number of positions or stack depth.", maxStackDepth);

		AfxMessageBox(temp);
		return utilityHelper.SetEditControlErrorState(this, IDC_STACK_DEPTH);
	}

	int availablePositions = m_BayDepth / m_StackDepth;

	return TRUE;
}

BOOL CBayProfileFloorPage::OnSetActive() 
{
	if (m_pBayProfile->m_Active) {
		GetDlgItem(IDC_BAY_DEPTH)->EnableWindow(FALSE);
		GetDlgItem(IDC_BAY_WIDTH)->EnableWindow(FALSE);
		GetDlgItem(IDC_BAY_HEIGHT)->EnableWindow(FALSE);
		GetDlgItem(IDC_STACK_WIDTH)->EnableWindow(FALSE);
		GetDlgItem(IDC_STACK_DEPTH)->EnableWindow(FALSE);
		GetDlgItem(IDC_SELECTION_STACK_POSITIONS)->EnableWindow(FALSE);
		GetDlgItem(IDC_RESERVE_STACK_POSITIONS)->EnableWindow(FALSE);
		GetDlgItem(IDC_SELECTION_STACK_HEIGHT)->EnableWindow(FALSE);
		GetDlgItem(IDC_RESERVE_STACK_HEIGHT)->EnableWindow(FALSE);
	}
	else {
		GetDlgItem(IDC_BAY_DEPTH)->EnableWindow(TRUE);
		GetDlgItem(IDC_BAY_WIDTH)->EnableWindow(TRUE);
		GetDlgItem(IDC_BAY_HEIGHT)->EnableWindow(TRUE);
		GetDlgItem(IDC_STACK_WIDTH)->EnableWindow(TRUE);
		GetDlgItem(IDC_STACK_DEPTH)->EnableWindow(TRUE);
		GetDlgItem(IDC_SELECTION_STACK_POSITIONS)->EnableWindow(TRUE);
		GetDlgItem(IDC_RESERVE_STACK_POSITIONS)->EnableWindow(TRUE);
		GetDlgItem(IDC_SELECTION_STACK_HEIGHT)->EnableWindow(TRUE);
		GetDlgItem(IDC_RESERVE_STACK_HEIGHT)->EnableWindow(TRUE);

	}
	
	return CPropertyPage::OnSetActive();
}
BOOL CBayProfileFloorPage::OnHelpInfo(HELPINFO* pHelpInfo) 
{
	helpService.ShowFieldHelp(IDD, pHelpInfo->iCtrlId);
	return FALSE;

}

BOOL CBayProfileFloorPage::OnNotify(WPARAM wParam, LPARAM lParam, LRESULT* pResult) 
{
	NMHDR* pNMHDR = (NMHDR*)lParam;

	if (pNMHDR->code == PSN_HELP) {
		helpService.ShowScreenHelp(IDD);
		return TRUE;
	}
	
	return CPropertyPage::OnNotify(wParam, lParam, pResult);
}

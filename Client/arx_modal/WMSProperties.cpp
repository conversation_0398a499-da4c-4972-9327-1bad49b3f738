// WMSProperties.cpp : implementation file
//

#include "stdafx.h"
#include "modal.h"
#include "WMSProperties.h"
#include "IntegrationDataService.h"
#include "UtilityHelper.h"
#include "HelpService.h"

#ifdef _DEBUG
#define new DEBUG_NEW
#undef THIS_FILE
static char THIS_FILE[] = __FILE__;
#endif

extern CIntegrationDataService integrationDataService;
extern CUtilityHelper utilityHelper;
extern CHelpService helpService;
/////////////////////////////////////////////////////////////////////////////
// CWMSProperties dialog


CWMSProperties::CWMSProperties(CWnd* pParent /*=NULL*/)
	: CDialog(CWMSProperties::IDD, pParent)
{
	//{{AFX_DATA_INIT(CWMSProperties)
	m_Description = _T("");
	m_Name = _T("");
	m_WMSId = _T("");
	m_IdText = _T("");
	//}}AFX_DATA_INIT
	m_WMS.m_WMSDBId = 0;
	m_IdText = "WMS Id";
}


void CWMSProperties::DoDataExchange(CDataExchange* pDX)
{
	CDialog::DoDataExchange(pDX);
	//{{AFX_DATA_MAP(CWMSProperties)
	DDX_Text(pDX, IDC_DESCRIPTION, m_Description);
	DDV_MaxChars(pDX, m_Description, 255);
	DDX_Text(pDX, IDC_NAME, m_Name);
	DDV_MaxChars(pDX, m_Name, 25);
	DDX_Text(pDX, IDC_WMS_ID, m_WMSId);
	//}}AFX_DATA_MAP
}


BEGIN_MESSAGE_MAP(CWMSProperties, CDialog)
	//{{AFX_MSG_MAP(CWMSProperties)
	ON_WM_HELPINFO()
	ON_BN_CLICKED(IDHELP, OnHelp)
	//}}AFX_MSG_MAP
END_MESSAGE_MAP()

/////////////////////////////////////////////////////////////////////////////
// CWMSProperties message handlers

BOOL CWMSProperties::OnInitDialog() 
{
	CDialog::OnInitDialog();

	if (m_WMS.m_WMSDBId > 0) {
		m_Name = m_WMS.m_Name;
		m_Description = m_WMS.m_Description;
		m_GroupDBId = m_WMS.m_GroupDBId;
		m_WMSId = m_WMS.m_WMSId;
	}

	CStatic *pStatic = (CStatic *)GetDlgItem(IDC_WMSID_STATIC);
	pStatic->SetWindowText(m_IdText + ":");

	UpdateData(FALSE);
	
	return TRUE;  // return TRUE unless you set the focus to a control
	              // EXCEPTION: OCX Property Pages should return FALSE
}


void CWMSProperties::OnOK() 
{
	UpdateData(TRUE);
	CEdit *pEdit;

	if (m_Name == "") {
		AfxMessageBox("Enter a name for the WMS Section.");
		pEdit = (CEdit *)GetDlgItem(IDC_NAME);
		pEdit->SetSel(0,-1);
		pEdit->SetFocus();
		return;
	}

	if (m_Description == "") {
		AfxMessageBox("Enter a description for the WMS Section.");
		pEdit = (CEdit *)GetDlgItem(IDC_DESCRIPTION);
		pEdit->SetSel(0,-1);
		pEdit->SetFocus();
		return;
	}

	if (m_WMSId == "") {
		CString temp;
		temp.Format("Enter a value for %s", m_IdText);
		AfxMessageBox(temp);
		pEdit = (CEdit *)GetDlgItem(IDC_WMS_ID);
		pEdit->SetSel(0,-1);
		pEdit->SetFocus();
		return;
	}

	if (m_WMSId.Find("'") >= 0) {
		CString temp;
		temp.Format("Single quotes are not allowed in the %s field.", m_IdText);
		AfxMessageBox(temp);
		pEdit = (CEdit *)GetDlgItem(IDC_WMS_ID);
		pEdit->SetSel(0,-1);
		pEdit->SetFocus();
		return;
	}
	
	// Populate the WMS with the current properties
	m_WMS.m_Name = m_Name;
	m_WMS.m_Description = m_Description;
	m_WMS.m_WMSId = m_WMSId;


	if (m_WMS.m_GroupDBId <= 0)
		m_WMS.m_GroupDBId = m_GroupDBId;

	// Save the CWMS to the database
	try {
		// Make sure the same name does not already exist
		if (integrationDataService.WMSExists(m_WMS.m_Name, m_WMS.m_WMSDBId)) {
			AfxMessageBox("A WMS Section with the same name already exists.");
			CEdit *pEdit = (CEdit *)GetDlgItem(IDC_NAME);
			pEdit->SetSel(0,-1);
			pEdit->SetFocus();
			return;
		}

		if (integrationDataService.WMSIdExists(m_WMS.m_WMSId, m_WMS.m_GroupDBId, m_WMS.m_WMSDBId)) {
			AfxMessageBox("A WMS Section with the same Id already exists in the WMS Facility.");
			CEdit *pEdit = (CEdit *)GetDlgItem(IDC_WMS_ID);
			pEdit->SetSel(0,-1);
			pEdit->SetFocus();
			return;
		}
	
		integrationDataService.StoreWMS(m_WMS);
	}
	catch (...) {
		utilityHelper.ProcessError("Error storing WMS Section to database.");
		return;
	}



	CDialog::OnOK();
}

BOOL CWMSProperties::OnHelpInfo(HELPINFO* pHelpInfo) 
{
	helpService.ShowFieldHelp(IDD, pHelpInfo->iCtrlId);

	return FALSE;

}

void CWMSProperties::OnHelp() 
{
	helpService.ShowScreenHelp(IDD);

	return;

}

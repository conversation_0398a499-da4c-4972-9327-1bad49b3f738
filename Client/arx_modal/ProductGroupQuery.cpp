// ProductGroupQuery.cpp: implementation of the CProductGroupQuery class.
//
//////////////////////////////////////////////////////////////////////

#include "stdafx.h"
#include "modal.h"
#include "ProductGroupQuery.h"

#include <dbsymtb.h>

#ifdef _DEBUG
#undef THIS_FILE
static char THIS_FILE[]=__FILE__;
#define new DEBUG_NEW
#endif

//////////////////////////////////////////////////////////////////////
// Construction/Destruction
//////////////////////////////////////////////////////////////////////

CProductGroupQuery::CProductGroupQuery()
{
	m_ProductGroupDBID = -1;
	m_CriteriaRangeDBID = -1;
	m_CriteriaDBID = -1;
	m_Operator = "";
	m_Value = "";
}	

CProductGroupQuery::~CProductGroupQuery()
{

}

CProductGroupQuery& CProductGroupQuery::operator=(CProductGroupQuery &other)
{
	m_CriteriaDBID = other.m_CriteriaDBID;
	m_CriteriaRangeDBID = other.m_CriteriaRangeDBID;
	m_ProductGroupDBID = other.m_ProductGroupDBID;
	m_ProductGroupQueryDBID = other.m_ProductGroupQueryDBID;
	m_RangeName = other.m_RangeName;
	m_Operator = other.m_Operator;
	m_Value = other.m_Value;

	return *this;
}

int CProductGroupQuery::Parse(CString &line)
{
	char *str;
	char *ptr;
	CString tmp;
	int idx;

	tmp = line;

	try {
		// strtok doesn't interpret double pipes as a field
		//DBID|Operator|Value|RangeDBID|PGDBID|CriteriaDBID|RangeName
		idx = tmp.Find("||");
		while (idx >= 0) {
			tmp.Insert(idx+1, " ");
			idx = tmp.Find("||");
		}

		str = tmp.GetBuffer(0);
		
		ptr = strtok(str, "|");
		m_ProductGroupQueryDBID = atol(ptr);
		ptr = strtok(NULL, "|");
		m_Operator = ptr;
		ptr = strtok(NULL, "|");
		m_Value = ptr;
		ptr = strtok(NULL, "|");
		m_CriteriaRangeDBID = atol(ptr);
		ptr = strtok(NULL, "|");
		m_ProductGroupDBID = atol(ptr);
		ptr = strtok(NULL, "|");
		m_CriteriaDBID = atol(ptr);
		ptr = strtok(NULL, "|");
		m_RangeName = ptr;

		tmp.ReleaseBuffer();
	}
	catch (...) {
		tmp.ReleaseBuffer();
		AfxMessageBox("Error processing product group criteria list.\n");
		ads_printf("%s\n", line);
		return -1;
	}

	return 0;
}

BOOL CProductGroupQuery::IsEqual(CProductGroupQuery &other)
{
	if (m_CriteriaDBID != other.m_CriteriaDBID) return FALSE;
	if (m_CriteriaRangeDBID != other.m_CriteriaRangeDBID) return FALSE;
	if (m_ProductGroupDBID != other.m_ProductGroupDBID) return FALSE;
	if (m_RangeName != other.m_RangeName) return FALSE;
	if (m_Operator != other.m_Operator) return FALSE;
	if (m_Value != other.m_Value) return FALSE;

	return TRUE;

}

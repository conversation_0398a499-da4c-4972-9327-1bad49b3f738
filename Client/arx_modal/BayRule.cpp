// BayRule.cpp: implementation of the CBayRule class.
//
//////////////////////////////////////////////////////////////////////

#include "stdafx.h"
#include "modal.h"
#include "BayRule.h"
#include "UtilityHelper.h"

#ifdef _DEBUG
#undef THIS_FILE
static char THIS_FILE[]=__FILE__;
#define new DEBUG_NEW
#endif

extern CUtilityHelper utilityHelper;

//////////////////////////////////////////////////////////////////////
// Construction/Destruction
//////////////////////////////////////////////////////////////////////

CBayRule::CBayRule()
{
	m_BayRuleDBId = 0;
	m_BayProfileDBId = 0;
	m_Clearance = 0;
}

CBayRule::~CBayRule()
{
	for (int i=0; i < m_FacingInfoList.GetSize(); ++i)
		delete m_FacingInfoList[i];
}

CBayRule::CBayRule(const CBayRule& other)
{
	m_BayRuleDBId = other.m_BayRuleDBId;
	m_Description = other.m_Description;
	m_PalletHeight = other.m_PalletHeight;
	m_PctUtilSelPos = other.m_PctUtilSelPos;
	m_PctUtilRsvPos = other.m_PctUtilRsvPos;
	m_PctRsvToSelPos = other.m_PctRsvToSelPos;
	m_DesiredRplnPerWeek = other.m_DesiredRplnPerWeek;
	m_Clearance = other.m_Clearance;
	m_AdditionalRsvCube = other.m_AdditionalRsvCube;
	m_Baytype = other.m_Baytype;
	m_PercentReserves = other.m_PercentReserves;
	m_BayProfileDBId = other.m_BayProfileDBId;
	
	for (int i=0; i < m_FacingInfoList.GetSize(); ++i)
		delete m_FacingInfoList[i];
	m_FacingInfoList.RemoveAll();

	for (i=0; i < other.m_FacingInfoList.GetSize(); ++i)
		m_FacingInfoList.Add(new CFacingInfo(*other.m_FacingInfoList[i]));

}


CBayRule& CBayRule::operator=(const CBayRule &other)
{	
	m_BayRuleDBId = other.m_BayRuleDBId;
	m_Description = other.m_Description;
	m_PalletHeight = other.m_PalletHeight;
	m_PctUtilSelPos = other.m_PctUtilSelPos;
	m_PctUtilRsvPos = other.m_PctUtilRsvPos;
	m_PctRsvToSelPos = other.m_PctRsvToSelPos;
	m_DesiredRplnPerWeek = other.m_DesiredRplnPerWeek;
	m_Clearance = other.m_Clearance;
	m_AdditionalRsvCube = other.m_AdditionalRsvCube;
	m_Baytype = other.m_Baytype;
	m_PercentReserves = other.m_PercentReserves;
	m_BayProfileDBId = other.m_BayProfileDBId;

	for (int i=0; i < m_FacingInfoList.GetSize(); ++i)
		delete m_FacingInfoList[i];
	m_FacingInfoList.RemoveAll();

	for (i=0; i < other.m_FacingInfoList.GetSize(); ++i)
		m_FacingInfoList.Add(new CFacingInfo(*other.m_FacingInfoList[i]));


	return *this;
}

BOOL CBayRule::operator==(const CBayRule& other)
{
	if (m_BayRuleDBId != other.m_BayRuleDBId) return FALSE;
	if (m_Description != other.m_Description) return FALSE;
	if (m_PalletHeight != other.m_PalletHeight) return FALSE;
	if (m_PctUtilSelPos != other.m_PctUtilSelPos) return FALSE;
	if (m_PctUtilRsvPos != other.m_PctUtilRsvPos) return FALSE;
	if (m_PctRsvToSelPos != other.m_PctRsvToSelPos) return FALSE;
	if (m_DesiredRplnPerWeek != other.m_DesiredRplnPerWeek) return FALSE;
	if (m_Clearance != other.m_Clearance) return FALSE;
	if (m_AdditionalRsvCube != other.m_AdditionalRsvCube) return FALSE;
	if (m_Baytype != other.m_Baytype) return FALSE;
	if (m_PercentReserves != other.m_PercentReserves) return FALSE;
	if (m_BayProfileDBId != other.m_BayProfileDBId) return FALSE;
	
	if (m_FacingInfoList.GetSize() != other.m_FacingInfoList.GetSize()) return FALSE;

	for (int i=0; i < m_FacingInfoList.GetSize(); ++i) {
		if (*m_FacingInfoList[i] != *other.m_FacingInfoList[i])
			return FALSE;
	}

	return TRUE;
}

int CBayRule::Parse(CString &line)
{
	CStringArray strings;
	utilityHelper.ParseString(line, "|", strings);

	for (int i=0; i < strings.GetSize(); ++i) {
		switch (i) {
		case 0:
			m_BayRuleDBId = atoi(strings[i]);
			break;
		case 1:
			m_Description = strings[i];
			break;
		case 2:
			m_PalletHeight = atof(strings[i]);
			break;
		case 3:
			m_PctUtilSelPos = atof(strings[i]);
			break;
		case 4:
			m_PctUtilRsvPos = atof(strings[i]);
			break;
		case 5:
			m_PctRsvToSelPos = atof(strings[i]);
			break;
		case 6:
			m_DesiredRplnPerWeek = atof(strings[i]);
			break;
		case 7:
			m_Clearance = atof(strings[i]);
			break;
		case 8:
			m_AdditionalRsvCube = atof(strings[i]);
			break;
		case 9:
			m_Baytype = atoi(strings[i]);
			break;
		case 10:
			m_PercentReserves = atof(strings[i]);
			break;
		case 11:
			m_BayProfileDBId = atoi(strings[i]);
			break;

		}
	}

	return 0;
}
	


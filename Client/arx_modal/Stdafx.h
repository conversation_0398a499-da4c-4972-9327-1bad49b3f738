// (C) Copyright 1996 by Autodesk, Inc. 
//
// Permission to use, copy, modify, and distribute this software in
// object code form for any purpose and without fee is hereby granted, 
// provided that the above copyright notice appears in all copies and 
// that both that copyright notice and the limited warranty and
// restricted rights notice below appear in all supporting 
// documentation.
//
// AUTODESK PROVIDES THIS PROGRAM "AS IS" AND WITH ALL FAULTS. 
// AUTODESK SPECIFICALLY DISCLAIMS ANY IMPLIED WARRANTY OF
// MERCHANTABILITY OR FITNESS FOR A PARTICULAR USE.  AUTODESK, INC. 
// DOES NOT WARRANT THAT THE OPERATION OF THE PROGRAM WILL BE
// UNINTERRUPTED OR ERROR FREE.
//
// Use, duplication, or disclosure by the U.S. Government is subject to 
// restrictions set forth in FAR 52.227-19 (Commercial Computer
// Software - Restricted Rights) and DFAR 252.227-7013(c)(1)(ii)
// (Rights in Technical Data and Computer Software), as applicable.
//

// stdafx.h : include file for standard system include files,
//  or project specific include files that are used frequently, but
//      are changed infrequently
//


// Check if the build is DEBUG version and it's intended
// to be used with Non-DEBUG AutoCAD.
// In this case, for MFC header files, we need to undefine
// _DEBUG symbol
// Read this project readme.txt for more detail
#if defined( _DEBUG) && !defined(DEBUG_AUTOCAD)
//   #pragma message("Building debug version of modal.arx to be used with non-debug/Prod AutoCAD")
    #define _DEBUG_WAS_DEFINED
    #undef _DEBUG
#endif

#pragma warning(disable : 4100 4244 4786 4201)

#include <afxwin.h>
#include <afxcmn.h>
#include <afxtempl.h>
#include <afxext.h>
#include <afxctl.h>
#include <afxdisp.h>

// Turn on the _DEBUG symbol if it was defined, before including
// non-MFC header files.
//
#ifdef _DEBUG_WAS_DEFINED
    #define _DEBUG
    #undef _DEBUG_WAS_DEFINED
#endif

#define CPP_DLL_EXPORT	__declspec( dllexport )
#define CPP_DLL_IMPORT	__declspec( dllimport )
#include "SSACStringArray.h"
#ifdef _AUTOCAD2000
	#include <dbapserv.h>
	#include <migrtion.h>
#endif

//#import <msxml3.dll> raw_interfaces_only
//#import <msxml3.dll> raw_interfaces_only
#import <msxml3.dll> exclude("IErrorInfo") high_method_prefix("_") raw_method_prefix("") 
// #include "base.h"
// #include "MiscClasses.h"
/*
template class __declspec(dllimport) CStringT<TCHAR, StrTraitMFC<TCHAR, ChTraitsCRT<TCHAR> > >;
template class __declspec(dllimport) CSimpleStringT<TCHAR>;
*/

class SLOTSessionMgr;
_declspec(dllimport) SLOTSessionMgr* getSessionMgrSO();
_declspec(dllimport) SLOTSessionMgr* SessionMgrSO;

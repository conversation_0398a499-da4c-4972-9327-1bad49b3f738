#if !defined(AFX_BAYPROFILESHEET_H__7CAD922C_016F_4F91_B2A4_E0A13D8A0587__INCLUDED_)
#define AFX_BAYPROFILESHEET_H__7CAD922C_016F_4F91_B2A4_E0A13D8A0587__INCLUDED_

#if _MSC_VER > 1000
#pragma once
#endif // _MSC_VER > 1000
// BayProfileSheet.h : header file
//

#include "BayProfileBayAttributesPage.h"
#include "BayProfileLaborPage.h"

#include "BayProfileBinPage.h"
#include "BayProfileDriveInPage.h"
#include "BayProfileFloorPage.h"
#include "BayProfileFlowPage.h"
#include "BayProfilePalletPage.h"

#include "BayProfileCrossbarPage.h"
#include "BayProfileLevelPage.h"
#include "BayProfileLocationPage.h"
#include "BayProfileRulesPage.h"

#include "BayProfile.h"

#include "BayProfileExternalAttributePage.h"
/////////////////////////////////////////////////////////////////////////////
// CBayProfileSheet

class CBayProfileSheet : public CPropertySheet
{
	DECLARE_DYNAMIC(CBayProfileSheet)

// Construction
public:
	CBayProfileSheet(int bayType, LPCTSTR pszCaption, CWnd *pParentWnd = NULL, UINT iSelectPage = 0);
	CBayProfileSheet(UINT nIDCaption, CWnd* pParentWnd = NULL, UINT iSelectPage = 0);
	CBayProfileSheet(LPCTSTR pszCaption, CWnd* pParentWnd = NULL, UINT iSelectPage = 0);

// Attributes
public:
	CBayProfile *m_pBayProfile;

	CBayProfileBayAttributesPage m_BayAttributesPage;
	CBayProfileBinPage m_BinPage;
	CBayProfilePalletPage m_PalletPage;
	CBayProfileFloorPage m_FloorPage;
	CBayProfileDriveInPage m_DriveInPage;
	CBayProfileFlowPage m_FlowPage;
	CBayProfileLevelPage m_LevelPage;
	CBayProfileLocationPage m_LocationPage;
	CBayProfileLaborPage m_LaborPage;
	CBayProfileExternalAttributePage m_ExternalAttributePage;
	CBayProfileCrossbarPage m_CrossbarPage;
	CBayProfileRulesPage m_RulesPage;

	int m_SelectedLevel;
// Operations
public:

// Overrides
	// ClassWizard generated virtual function overrides
	//{{AFX_VIRTUAL(CBayProfileSheet)
	protected:
	virtual BOOL OnNotify(WPARAM wParam, LPARAM lParam, LRESULT* pResult);
	//}}AFX_VIRTUAL

// Implementation
public:
	virtual ~CBayProfileSheet();

	// Generated message map functions
protected:
	//{{AFX_MSG(CBayProfileSheet)
	afx_msg BOOL OnHelpInfo(HELPINFO* pHelpInfo);
	//}}AFX_MSG
	DECLARE_MESSAGE_MAP()
};

/////////////////////////////////////////////////////////////////////////////

//{{AFX_INSERT_LOCATION}}
// Microsoft Visual C++ will insert additional declarations immediately before the previous line.

#endif // !defined(AFX_BAYPROFILESHEET_H__7CAD922C_016F_4F91_B2A4_E0A13D8A0587__INCLUDED_)

// MessageQueueHelper.cpp: implementation of the CMessageQueueHelper class.
//
//////////////////////////////////////////////////////////////////////

#include "stdafx.h"
#include "modal.h"
#include "MessageQueueHelper.h"
#include "ControlService.h"
#include "ssa_exception.h"

#ifdef _DEBUG
#undef THIS_FILE
static char THIS_FILE[]=__FILE__;
#define new DEBUG_NEW
#endif

extern CControlService controlService;

//////////////////////////////////////////////////////////////////////
// Construction/Destruction
//////////////////////////////////////////////////////////////////////

CMessageQueueHelper::CMessageQueueHelper()
{
	m_pConnection = NULL;
	m_Queue = "";
	m_hConnect = NULL;
	m_hConnect = LoadLibrary("ec_connect4.dll");
	if (m_hConnect == NULL)
		throw Ssa_Exception("Error loading Connect library.", __FILE__, __LINE__, 200);
}

CMessageQueueHelper::~CMessageQueueHelper()
{
	if (m_pConnection != NULL) {
		CloseQueue();
	}

	if (m_hConnect != NULL)
		FreeLibrary(m_hConnect);
}

int CMessageQueueHelper::OpenQueue(const CString &host, int port, const CString &queueMgr, 
								   const CString &queue, const CString& channel)
{
	char uri[1024];
	int result;
	
	ssamq_BuildMQSeriesURIFunction buildURI = (ssamq_BuildMQSeriesURIFunction)GetProcAddress(m_hConnect, "ssamq_BuildMQSeriesURI");

	result = (buildURI)(queueMgr, channel, host, port, uri);
	if (result != SSAMQ_NO_ERROR) {
		DisplayError(result);
		return -1;
	}

	
	ssamq_ConnectFunction mqConnect = (ssamq_ConnectFunction)GetProcAddress(m_hConnect, "ssamq_Connect");
	result = (mqConnect)(&m_pConnection, uri);
	if (result != SSAMQ_NO_ERROR) {
		DisplayError(result);
		m_pConnection = NULL;
		return -1;
	}

	m_Queue = queue;

	return 0;
}


int CMessageQueueHelper::CloseQueue()
{
	
	ssamq_DisconnectFunction fp = (ssamq_DisconnectFunction)GetProcAddress(m_hConnect, "ssamq_DisconnectFunction");

	int result = (fp)(m_pConnection);
	if (result != SSAMQ_NO_ERROR) {
		DisplayError(result);
		return -1;
	}

	m_pConnection = NULL;
	m_Queue = "";

	return 0;
}

int CMessageQueueHelper::GetMessage(char **msg)
{

	char *buffer;

	// Check connection
	if (m_pConnection == NULL || m_Queue == "") {
		controlService.Log("", "Not connected to queue.\n");
		return -1;
	}

	// Read message
	ssamq_GetMessageFunction getMessage = (ssamq_GetMessageFunction)GetProcAddress(m_hConnect, "ssamq_GetMessage");
	int result = (getMessage)(m_pConnection, m_Queue, &buffer, 0);
	if (result == SSAMQ_NO_MESSAGES) {
		return 0;
	}

	if (result != SSAMQ_NO_ERROR) {
		DisplayError(result);
		return -1;
	}

	ssamq_FreeStringFunction freeString = (ssamq_FreeStringFunction)GetProcAddress(m_hConnect, "ssamq_FreeString");

	*msg = (char *)malloc(sizeof(char) * strlen(buffer));
	if (msg == NULL) {
		controlService.Log("", "Not enough memory (%d) to read message.\n", (int)strlen(buffer));
		result = (freeString)(buffer);
		if (result != SSAMQ_NO_ERROR)
			DisplayError(result);
		return -1;
	}

	strcpy(*msg, buffer);

	result = (freeString)(buffer);
	if (result != SSAMQ_NO_ERROR)
		DisplayError(result);

	return strlen(*msg);
}

int CMessageQueueHelper::PutMessage(const char *msg, int size)
{
	UNREFERENCED_PARAMETER(size);

	// Check connection
	if (m_pConnection == NULL || m_Queue == "") {
		controlService.Log("", "Not connected to queue.\n");
		return -1;
	}

	ssamq_SendMessageFunction fp = (ssamq_SendMessageFunction)GetProcAddress(m_hConnect, "ssamq_SendMessage");
	int result = (fp)(m_pConnection, msg, m_Queue, 0);
	if (result != SSAMQ_NO_ERROR) {
		DisplayError(result);
		return -1;
	}

	return 0;
}


int CMessageQueueHelper::BeginTransaction()
{
	// Check connection
	if (m_pConnection == NULL || m_Queue == "") {
		controlService.Log("", "Not connected to queue.\n");
		return -1;
	}

	ssamq_BeginTransactionFunction fp = (ssamq_BeginTransactionFunction)GetProcAddress(m_hConnect, "ssamq_BeginTransaction");

	int result = (fp)(m_pConnection);
	if (result != SSAMQ_NO_ERROR) {
		DisplayError(result);
		return -1;
	}

	return 0;
}

int CMessageQueueHelper::Commit()
{
	// Check connection
	if (m_pConnection == NULL || m_Queue == "") {
		controlService.Log("", "Not connected to queue.\n");
		return -1;
	}

	ssamq_CommitTransactionFunction fp = (ssamq_CommitTransactionFunction)GetProcAddress(m_hConnect, "ssamq_CommitTransaction");
	int result = (fp)(m_pConnection);
	if (result != SSAMQ_NO_ERROR) {
		DisplayError(result);
		return -1;
	}

	return 0;
}

int CMessageQueueHelper::Rollback()
{
	// Check connection
	if (m_pConnection == NULL || m_Queue == "") {
		controlService.Log("", "Not connected to queue.\n");
		return -1;
	}

	ssamq_AbortTransactionFunction fp = (ssamq_AbortTransactionFunction)GetProcAddress(m_hConnect, "ssamq_AbortTransaction");

	int result = (fp)(m_pConnection);
	if (result != SSAMQ_NO_ERROR) {
		DisplayError(result);
		return -1;
	}

	return 0;
}

void CMessageQueueHelper::DisplayError(int result)
{
	char *buffer;
	ssamq_AbortTransactionFunction fp = (ssamq_AbortTransactionFunction)GetProcAddress(m_hConnect, "ssamq_AbortTransaction");

	long mqResult = (fp)(m_pConnection);
	ssamq_GetLongErrorDescriptionFunction fp2 = 
		(ssamq_GetLongErrorDescriptionFunction)GetProcAddress(m_hConnect, "ssamq_GetLongErrorDescription");

	(fp2)(result, &buffer);
	CString temp;
	temp.Format("Connect Error %x (%d): %s\n", result, mqResult, buffer);
	controlService.Log("", temp);

	ssamq_FreeStringFunction fp3 = (ssamq_FreeStringFunction)GetProcAddress(m_hConnect, "ssamq_FreeString");
	( fp3)(buffer);
	
	return;
}

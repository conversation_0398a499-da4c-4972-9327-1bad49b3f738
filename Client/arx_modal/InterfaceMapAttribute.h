// InterfaceMapAttribute.h: interface for the CInterfaceMapAttribute class.
//
//////////////////////////////////////////////////////////////////////

#if !defined(AFX_INTERFACEMAPATTRIBUTE_H__5B4113B1_7878_45B8_8145_B76A79803D7B__INCLUDED_)
#define AFX_INTERFACEMAPATTRIBUTE_H__5B4113B1_7878_45B8_8145_B76A79803D7B__INCLUDED_

#if _MSC_VER > 1000
#pragma once
#endif // _MSC_VER > 1000

class CInterfaceMapAttribute : public CObject  
{
public:
	CInterfaceMapAttribute();
	virtual ~CInterfaceMapAttribute();
	CInterfaceMapAttribute& operator=(CInterfaceMapAttribute &other);
	BOOL operator==(CInterfaceMapAttribute &other);

	int Parse(const CString &line);

	long m_InterfaceMapAttributeDBID;
	CString m_InternalAttribute;
	CString m_ExternalAttribute;
	int m_DataType;
	BOOL m_IsConstant;
	BOOL m_IsUDF;
	int m_UDFElementType;
};

#endif // !defined(AFX_INTERFACEMAPATTRIBUTE_H__5B4113B1_7878_45B8_8145_B76A79803D7B__INCLUDED_)

#include "qqhclasses.h"
#include "Constants.h"
#include "UtilityHelper.h"

extern CUtilityHelper utilityHelper;

////////////////////////////////////////
// qqhSLOTCoordinate Member functions
////////////////////////////////////////
qqhSLOTCoordinate::qqhSLOTCoordinate(int xCoord, int yCoord, int zCoord)
{
	x = xCoord;
	y = yCoord;
	z = zCoord;
}

qqhSLOTCoordinate::qqhSLOTCoordinate()
{
	x = 0;
	y = 0;
	z = 0;
}

qqhSLOTCoordinate::qqhSLOTCoordinate(const qqhSLOTCoordinate & other)
{
	this->x = other.x;
	this->y = other.y;
	this->z = other.z;
//	DBID = other.DBID;
}

qqhSLOTCoordinate& qqhSLOTCoordinate::operator=(const qqhSLOTCoordinate &other)
{
	this->x = other.x;
	this->y = other.y;
	this->z = other.z;
//	DBID = other.DBID;
	return *this;
}

BOOL qqhSLOTCoordinate::operator==(const qqhSLOTCoordinate &other)
{
	if (this->x != other.x) return FALSE;
	if (this->y != other.y) return FALSE;
	if (this->z != other.z) return FALSE;

	return TRUE;
}

void qqhSLOTCoordinate::BuildFromStream(CSsaStringArray &bufArray ) {
	char tempBuf[512];
	char tempREGName[100];
	char tempREGVal[256];
	char tempheader[100];
	int i;
	CSsaStringArray tempArray;
	CString tempChkStr;

	for ( i = 1; (bufArray[i]).Find(REGBegSearch) != -1; i++ ) {
		/////////////////////////////////////////////////////////////
		//  REGs are processed here
		/////////////////////////////////////////////////////////////
		strcpy(tempBuf,bufArray[i].GetBuffer(0));
		bufArray[i].ReleaseBuffer();
		tempChkStr = tempBuf;
		if (tempChkStr.GetAt(tempChkStr.GetLength()-1) == '|')
			strcat(tempBuf,"1");
		if (strchr(tempBuf,'\n') == NULL)
			strcat(tempBuf,"\n");
		bufArray[i] = tempBuf;
		strcpy(tempheader,strtok(tempBuf,"|"));
		strcpy(tempREGName,strtok(NULL,"|"));
		if (strstr(bufArray[i].GetBuffer(0),"|\n") == NULL )
			strcpy(tempREGVal,strtok(NULL,"\n"));
		else
			strcpy(tempREGVal,"");
		bufArray[i].ReleaseBuffer();
		if ( strcmp(tempREGName,"XCoord") == 0 ) {
			this->x = atoi(tempREGVal);
		}
		else if ( strcmp(tempREGName,"YCoord") == 0 ) {
			this->y = atoi(tempREGVal);
		}
		else if ( strcmp(tempREGName,"ZCoord") == 0 ) {
			this->z = atoi(tempREGVal);
		}
	}

	return;
}

void qqhSLOTCoordinate::StreamAttributes(CSsaStringArray & attributBuf) {

	CString tempBuf;

	attributBuf.Add(CString(CoordBegSearch) + "\n");

	tempBuf.Format("%sXCoord|%d\n", REGBegSearch, this->x);
	attributBuf.Add(tempBuf);

	tempBuf.Format("%sYCoord|%d\n", REGBegSearch, this->y);
	attributBuf.Add(tempBuf);

	tempBuf.Format("%sZCoord|%d\n", REGBegSearch, this->z);
	attributBuf.Add(tempBuf);

	attributBuf.Add(CString(CoordEndSearch) + "\n");

	return;
}

CString qqhSLOTCoordinate::ToText()
{
	CString txt;

	txt.Format("%d, %d, %d", x, y, z);

	return txt;
}

void qqhSLOTCoordinate::FromText(CString &txt)
{
	CStringArray strings;

	txt.Replace(" ", "");
	if (utilityHelper.ParseString(txt, ",", strings) < 3)
		x = y = z = 0;
	else {
		x = atoi(strings[0]);
		y = atoi(strings[1]);
		z = atoi(strings[2]);
	}

	return;
}


////////////////////////////////////////
// qqhSLOTPickPath Member functions
////////////////////////////////////////
qqhSLOTPickPath::qqhSLOTPickPath() {
	return;
	description = "New PickPath";
	DBID = 0;
	strcpy(acadHandle,"XXX");
	pathLength = 0.0;
	strcpy(conAcadHandle,"XXX");
}

qqhSLOTPickPath::qqhSLOTPickPath(const qqhSLOTPickPath & other) {
	DBID = other.DBID;
	description = other.description;
//	for ( int i = 0; i < other.coordinateList.GetSize(); i++ )
//		coordinateList.Add(other.coordinateList[i]);
	UDFList.Copy(other.UDFList);
	pathLength = other.pathLength;
	strcpy(acadHandle, other.acadHandle);
	strcpy(conAcadHandle, other.conAcadHandle);
	return;
}

qqhSLOTPickPath& qqhSLOTPickPath::operator=(const qqhSLOTPickPath &other) {
	DBID = other.DBID;
	description = other.description;
//	for ( int i = 0; i < other.coordinateList.GetSize(); i++ )
//		coordinateList.Add(other.coordinateList[i]);
	UDFList.Copy(other.UDFList);
	pathLength = other.pathLength;
	strcpy(acadHandle, other.acadHandle);
	strcpy(conAcadHandle, other.conAcadHandle);
	return *this;
}

void qqhSLOTPickPath::BuildFromStream(CSsaStringArray &bufArray ) {
	int i;
	qqhSLOTCoordinate tempCoord;
	CSsaStringArray tempArray;
	char tempBuf[512];
	char tempREGName[100];
	char tempREGVal[256];
	char tempheader[100];
	CString tempChkStr;
 
	for ( i=0; i < bufArray.GetSize(); i++ ) {
		if ( bufArray[i].Find(REGBegSearch) != -1 ) {
			/////////////////////////////////////////////////////////////
			//  REGs are processed here
			/////////////////////////////////////////////////////////////
			strcpy(tempBuf,bufArray[i].GetBuffer(0));
			bufArray[i].ReleaseBuffer();
			tempChkStr = tempBuf;
			if (tempChkStr.GetAt(tempChkStr.GetLength()-1) == '|')
				strcat(tempBuf,"1");
			if (strchr(tempBuf,'\n') == NULL)
				strcat(tempBuf,"\n");
			bufArray[i] = tempBuf;
			strcpy(tempheader,strtok(tempBuf,"|"));
			strcpy(tempREGName,strtok(NULL,"|"));
			if (strstr(bufArray[i].GetBuffer(0),"|\n") == NULL )
				strcpy(tempREGVal,strtok(NULL,"\n"));
			else
				strcpy(tempREGVal,"");
			bufArray[i].ReleaseBuffer();
			if ( strcmp(tempREGName,"Description") == 0 )
				this->description = tempREGVal;
			else if ( strcmp(tempREGName,"DBID") == 0 )
				this->DBID = atoi(tempREGVal);
			else if ( strcmp(tempREGName,"AcadHandle") == 0 )
				strcpy(this->acadHandle,tempREGVal);
			else if ( strcmp(tempREGName,"PathLength") == 0 )
				this->pathLength = (float)atof(tempREGVal);
			else if ( strcmp(tempREGName,"ConnectAcadHandle") == 0 )
				strcpy(this->conAcadHandle,tempREGVal);
		}
/*		if ( bufArray[i].Find(CoordBegSearch) != -1 ) {
			tempArray.RemoveAll();
			while ( bufArray[i].Find(CoordEndSearch) == -1 ) {
				tempArray.Add(bufArray[i]);
				i++;
			}
			tempArray.Add(bufArray[i]);
			tempCoord.BuildFromStream(tempArray);
			this->coordinateList.Add(tempCoord);
		}
*/	}
	return;
}

void qqhSLOTPickPath::StreamAttributes(CSsaStringArray & attributBuf) {
	CString tempBuf;

	attributBuf.Add(CString(PathBegSearch)+"\n");

	tempBuf.Format("%sDBID|%d\n", REGBegSearch, this->DBID );
	attributBuf.Add(tempBuf);

	tempBuf.Format("%sAcadHandle|%s\n", REGBegSearch, this->acadHandle );
	attributBuf.Add(tempBuf);

	tempBuf.Format("%sPathLength|%f\n", REGBegSearch, this->pathLength);
	attributBuf.Add(tempBuf);

	tempBuf.Format("%sConnectAcadHandle|%s\n", REGBegSearch, this->conAcadHandle );
	attributBuf.Add(tempBuf);

	attributBuf.Add(CString(PathEndSearch)+"\n");
	return;
}


////////////////////////////////////////
// qqhSLOTObject Member functions
////////////////////////////////////////
qqhSLOTObject::qqhSLOTObject()
{
	DBID = 0;
}

qqhSLOTObject::qqhSLOTObject(const qqhSLOTObject & other) {
	DBID = other.DBID;
	description = other.description;
	UDFList.Copy(other.UDFList);
}

////////////////////////////////////////
// qqhSLOTHotSpot Member functions
////////////////////////////////////////
qqhSLOTHotSpot::qqhSLOTHotSpot()
{
	hotSpotType = 0;
	strcpy(acadHandle,"");
}

qqhSLOTHotSpot::qqhSLOTHotSpot(const qqhSLOTHotSpot & other)
{
	coord = other.coord;
	hotSpotType = other.hotSpotType;
	strcpy(acadHandle,other.acadHandle);
	UDFList.Copy(other.UDFList);
	DBID = other.DBID;
}

qqhSLOTHotSpot& qqhSLOTHotSpot::operator=(const qqhSLOTHotSpot &other)
{
	coord = other.coord;
	hotSpotType = other.hotSpotType;
	strcpy(acadHandle,other.acadHandle);
	UDFList.Copy(other.UDFList);
	DBID = other.DBID;
	return *this;
}

void qqhSLOTHotSpot::BuildFromStream(CSsaStringArray &bufArray ) {
	char tempBuf[512];
	char tempREGName[100];
	char tempREGVal[256];
	char tempheader[100];
	int i;
	CSsaStringArray tempArray;
	qqhSLOTHotSpot tempAttr;
	CString tempChkStr;

	for ( i = 0; i  < bufArray.GetSize(); i++ ) {
		if ( bufArray[i].Find(REGBegSearch) != -1 ) {
			/////////////////////////////////////////////////////////////
			//  REGs are processed here
			/////////////////////////////////////////////////////////////
			strcpy(tempBuf,bufArray[i].GetBuffer(0));
			bufArray[i].ReleaseBuffer();
			tempChkStr = tempBuf;
			if (tempChkStr.GetAt(tempChkStr.GetLength()-1) == '|')
				strcat(tempBuf,"1");
			if (strchr(tempBuf,'\n') == NULL)
				strcat(tempBuf,"\n");
			bufArray[i] = tempBuf;
			strcpy(tempheader,strtok(tempBuf,"|"));
			strcpy(tempREGName,strtok(NULL,"|"));
			if (strstr(bufArray[i].GetBuffer(0),"|\n") == NULL )
				strcpy(tempREGVal,strtok(NULL,"\n"));
			else
				strcpy(tempREGVal,"");
			bufArray[i].ReleaseBuffer();
			if ( strcmp(tempREGName,"Description") == 0 )
				this->description = tempREGVal;
			else if ( strcmp(tempREGName,"DBID") == 0 )
				this->DBID = atoi(tempREGVal);
			else if ( strcmp(tempREGName,"Type") == 0 )
				this->hotSpotType = atoi(tempREGVal);
			else if ( strcmp(tempREGName,"AcadHandle") == 0 )
				strcpy(this->acadHandle,tempREGVal);
		}
	

		/////////////////////////////////////////////////////////////
		//  UDFs 
		/////////////////////////////////////////////////////////////
		if ( bufArray[i].Find(UDFBegSearch) != -1 ) {
			strcpy(tempBuf,bufArray[i].GetBuffer(0));
			bufArray[i].ReleaseBuffer();
			strcpy(tempheader,strtok(tempBuf,"|"));
			strcpy(tempheader,strtok(NULL,"\n"));
			this->UDFList.Add(tempheader);
		}

		/////////////////////////////////////////////////////////////
		//  Coordinate
		/////////////////////////////////////////////////////////////
		if ( (bufArray[i]).Find(CoordBegSearch) != -1 ) {
			tempArray.RemoveAll();
			tempArray.Add(bufArray[i]);
			i++;
			while ( (bufArray[i]).Find(CoordEndSearch) == -1 ) {
				tempArray.Add(bufArray[i]);
				i++;
			}
			tempArray.Add(bufArray[i]);
			this->coord.BuildFromStream(tempArray);
		}
	}
	return;
}

void qqhSLOTHotSpot::StreamAttributes(CSsaStringArray & attributBuf) {

	CString tempBuf;
	CSsaStringArray tempArray;

	attributBuf.Add(CString(HotSpotBegSearch) + "\n");

	tempBuf.Format("%sDBID|%d\n", REGBegSearch, this->DBID );
	attributBuf.Add(tempBuf);

	tempBuf.Format("%sDescription|%s\n", REGBegSearch, this->description);
	attributBuf.Add(tempBuf);

	tempBuf.Format("%sType|%d\n", REGBegSearch, this->hotSpotType);
	attributBuf.Add(tempBuf);

	tempBuf.Format("%sAcadHandle|%s\n", REGBegSearch, this->acadHandle);
	attributBuf.Add(tempBuf);

	this->coord.StreamAttributes(attributBuf);

	attributBuf.Add(CString(HotSpotEndSearch) + "\n");

	return;
}

////////////////////////////////////////
// qqhSLOTQueryAttr Member functions
////////////////////////////////////////
qqhSLOTQueryAttr::qqhSLOTQueryAttr() {
	attrName = "";
	attrValue = "";
	conjunction = "";
	queryOperator = "";
	type="SLOTQueryAttr";
}

qqhSLOTQueryAttr::qqhSLOTQueryAttr(const qqhSLOTQueryAttr & other) {
	attrName = other.attrName;
	attrValue = other.attrValue;
	conjunction = other.conjunction;
	queryOperator = other.queryOperator;
	type="SLOTQueryAttr";
	UDFList.Copy(other.UDFList);
	DBID = other.DBID;
}

qqhSLOTQueryAttr & qqhSLOTQueryAttr::operator=(const qqhSLOTQueryAttr & other) {
	attrName = other.attrName;
	attrValue = other.attrValue;
	conjunction = other.conjunction;
	queryOperator = other.queryOperator;
	type="SLOTQueryAttr";
	UDFList.Copy(other.UDFList);
	DBID = other.DBID;
	return *this;
}

void qqhSLOTQueryAttr::BuildFromStream(CSsaStringArray &bufArray ) {
	char tempBuf[512];
	char tempREGName[100];
	char tempREGVal[256];
	char tempheader[100];
	int i;
	CSsaStringArray tempArray;
	qqhSLOTQueryAttr tempAttr;
	CString tempChkStr;

	for ( i = 0; i  < bufArray.GetSize(); i++ ) {
		if ( bufArray[i].Find(REGBegSearch) != -1 ) {
			/////////////////////////////////////////////////////////////
			//  REGs are processed here
			/////////////////////////////////////////////////////////////
			strcpy(tempBuf,bufArray[i].GetBuffer(0));
			bufArray[i].ReleaseBuffer();
			tempChkStr = tempBuf;
			if (tempChkStr.GetAt(tempChkStr.GetLength()-1) == '|')
				strcat(tempBuf,"1");
			if (strchr(tempBuf,'\n') == NULL)
				strcat(tempBuf,"\n");
			bufArray[i] = tempBuf;
			strcpy(tempheader,strtok(tempBuf,"|"));
			strcpy(tempREGName,strtok(NULL,"|"));
			if (strstr(bufArray[i].GetBuffer(0),"|\n") == NULL )
				strcpy(tempREGVal,strtok(NULL,"\n"));
			else
				strcpy(tempREGVal,"");
			bufArray[i].ReleaseBuffer();
			if ( strcmp(tempREGName,"Description") == 0 )
				this->description = tempREGVal;
			else if ( strcmp(tempREGName,"DBID") == 0 )
				this->DBID = atoi(tempREGVal);
			else if ( strcmp(tempREGName,"AttributeName") == 0 )
				this->attrName = tempREGVal;
			else if ( strcmp(tempREGName,"AttributeValue") == 0 )
				this->attrValue = tempREGVal;
			else if ( strcmp(tempREGName,"Conjunction") == 0 )
				this->conjunction = tempREGVal;
			else if ( strcmp(tempREGName,"QueryOperator") == 0 )
				this->queryOperator = tempREGVal;
		}
	

		/////////////////////////////////////////////////////////////
		//  UDFs 
		/////////////////////////////////////////////////////////////
		if ( bufArray[i].Find(UDFBegSearch) != -1 ) {
			strcpy(tempBuf,bufArray[i].GetBuffer(0));
			bufArray[i].ReleaseBuffer();
			strcpy(tempheader,strtok(tempBuf,"|"));
			strcpy(tempheader,strtok(NULL,"\n"));
			this->UDFList.Add(tempheader);
		}
	}
	return;
}

void qqhSLOTQueryAttr::StreamAttributes(CSsaStringArray & attributBuf) {

	int i;
	CString tempBuf;
	CSsaStringArray tempArray;

	attributBuf.Add(CString(QueryAttrBegSearch) + "\n");

	tempBuf.Format("%sDBID|%d\n", REGBegSearch, this->DBID );
	attributBuf.Add(tempBuf);

	tempBuf.Format("%sDescription|%s\n", REGBegSearch, this->description);
	
	attributBuf.Add(tempBuf);

	tempBuf.Format("%sAttributeName|%s\n",REGBegSearch,this->attrName);
	attributBuf.Add(tempBuf);

	tempBuf.Format("%sAttributeValue|%s\n",REGBegSearch,this->attrValue);
	attributBuf.Add(tempBuf);
	
	tempBuf.Format("%sConjunction|%s\n",REGBegSearch,this->conjunction);
	attributBuf.Add(tempBuf);

	tempBuf.Format("%sQueryOperator|%s\n",REGBegSearch,this->queryOperator);
	attributBuf.Add(tempBuf);
	for (i = 0; i < UDFList.GetSize(); i++)
		attributBuf.Add(CString(CString(UDFBegSearch) + UDFList[i] + "\n"));

	attributBuf.Add(CString(QueryAttrEndSearch) + "\n");

	return;
}

////////////////////////////////////////
// qqhSLOTQuery Member functions
////////////////////////////////////////
qqhSLOTQuery::qqhSLOTQuery() {
	objName = "";
	type="SLOTQuery";
}

qqhSLOTQuery::qqhSLOTQuery(qqhSLOTQuery & other) {
	for (int i=0; i < other.attrList.GetSize();i++)
		attrList.Add(other.attrList[i]);
	objName = other.objName;
	UDFList.Copy(other.UDFList);
	type="SLOTQuery";
	DBID = other.DBID;
}

qqhSLOTQuery & qqhSLOTQuery::operator=(qqhSLOTQuery & other) {
	for (int i=0; i < other.attrList.GetSize();i++)
		attrList.Add(other.attrList[i]);
	objName = other.objName;
	type="SLOTQuery";
	UDFList.Copy(other.UDFList);
	DBID = other.DBID;
	return *this;
}

void qqhSLOTQuery::AddQueryAttr(CString pAttribute,CString pValue, CString pConjunction, CString pQueryOperator) {
	/////////////////////////////////////////////////////////////
	// builds a queryattr object and adds it to the list        //
	//////////////////////////////////////////////////////////////
	qqhSLOTQueryAttr theQueryAttr;

	if ( pValue != "" ) {
		theQueryAttr.setAttrName(pAttribute);
		theQueryAttr.setAttrValue(pValue);
		theQueryAttr.setConjunction(pConjunction);
		theQueryAttr.setQueryOperator(pQueryOperator);
		this->attrList.Add(theQueryAttr);
	}
	return;
}

void qqhSLOTQuery::BuildFromStream(CSsaStringArray &bufArray ) {
	char tempBuf[512];
	char tempREGName[100];
	char tempREGVal[256];
	char tempheader[100];
	int i;
	CSsaStringArray tempArray;
	qqhSLOTQueryAttr tempAttr;
	CString tempChkStr;

	for ( i = 0; i  < bufArray.GetSize(); i++ ) {
		if ( bufArray[i].Find(REGBegSearch) != -1 ) {
			/////////////////////////////////////////////////////////////
			//  REGs are processed here
			/////////////////////////////////////////////////////////////
			strcpy(tempBuf,bufArray[i].GetBuffer(0));
			bufArray[i].ReleaseBuffer();
			tempChkStr = tempBuf;
			if (tempChkStr.GetAt(tempChkStr.GetLength()-1) == '|')
				strcat(tempBuf,"1");
			if (strchr(tempBuf,'\n') == NULL)
				strcat(tempBuf,"\n");
			bufArray[i] = tempBuf;
			strcpy(tempheader,strtok(tempBuf,"|"));
			strcpy(tempREGName,strtok(NULL,"|"));
			if (strstr(bufArray[i].GetBuffer(0),"|\n") == NULL )
				strcpy(tempREGVal,strtok(NULL,"\n"));
			else
				strcpy(tempREGVal,"");
			bufArray[i].ReleaseBuffer();
			if ( strcmp(tempREGName,"Description") == 0 )
				this->description = tempREGVal;
			else if ( strcmp(tempREGName,"DBID") == 0 )
				this->DBID = atoi(tempREGVal);
			else if ( strcmp(tempREGName,"ObjectName") == 0 )
				this->objName = tempREGVal;
		}
	

		/////////////////////////////////////////////////////////////
		//  UDFs 
		/////////////////////////////////////////////////////////////
		if ( bufArray[i].Find(UDFBegSearch) != -1 ) {
			strcpy(tempBuf,bufArray[i].GetBuffer(0));
			bufArray[i].ReleaseBuffer();
			strcpy(tempheader,strtok(tempBuf,"|"));
			strcpy(tempheader,strtok(NULL,"\n"));
			this->UDFList.Add(tempheader);
		}

		if ( bufArray[i].Find(QueryAttrBegSearch) != -1  ) {
			tempArray.RemoveAll();
			tempArray.Add(bufArray[i]);
			i++;
			while ( (bufArray[i]).Find(QueryAttrEndSearch) == -1 ) {
				tempArray.Add(bufArray[i]);
				i++;
			}
			tempArray.Add(bufArray[i]);
			tempAttr.BuildFromStream(tempArray);
			this->attrList.Add(tempAttr);
		}
	}
	return;
}

void qqhSLOTQuery::StreamAttributes(CSsaStringArray & attributBuf) {

	int i;
	CString tempBuf;
	CSsaStringArray tempArray;

	attributBuf.Add(CString(QueryBegSearch) + "\n");


	tempBuf.Format("%sDBID|%d\n", REGBegSearch, this->DBID );
	attributBuf.Add(tempBuf);

	tempBuf.Format("%sDescription|%s\n", REGBegSearch, this->description);
	
	attributBuf.Add(tempBuf);

	tempBuf.Format("%sObjectName|%s\n",REGBegSearch,this->objName);
	attributBuf.Add(tempBuf);

	for (i = 0; i < UDFList.GetSize(); i++)
		attributBuf.Add(CString(CString(UDFBegSearch) + UDFList[i] + "\n"));
	for (i = 0; i < attrList.GetSize(); i++)
		attrList[i].StreamAttributes(attributBuf);

	attributBuf.Add(CString(QueryEndSearch) + "\n");

	return;
}




/////////////////////////////////////////
// qqhSLOTHolder Member functions
////////////////////////////////////////
qqhSLOTHolder::qqhSLOTHolder() {
	//parent = NULL;
}

qqhSLOTHolder::qqhSLOTHolder(const qqhSLOTHolder & other) {
	UNREFERENCED_PARAMETER(other);
	//parent = other.parent;
}

//void qqhSLOTHolder::SetParent(qqhSLOTObject * aObjptr)
//{
	//this->parent = aObjptr;
//}

////////////////////////////////////////
// qqhSLOTHolder Member functions
////////////////////////////////////////
qqhSLOTLocation::qqhSLOTLocation()
{
	DBID = 0;
	handlingMethod = 1;
	IsSelect = 1;
	IsOverridden = 0;
	setType(CString("SLOTLocation"));
	width = depth = height = maxWeight = 0;
	ChangedInPass = 0;
	isChanged = "TRUE";
	Status = 0;
	IsActive = 0;
	BackfillId = " ";
	StockerId = " ";
	locationProfileId = 0;
	Trace = 0;
	Clearance = 0;
	pLocationProfile = NULL;
	locationKey = 0;
}

qqhSLOTLocation::qqhSLOTLocation(const qqhSLOTLocation& other)
{
	DBID = other.DBID;
	description = other.description;
	coord = other.coord;
	maxWeight = other.maxWeight;
	width = other.width;
	height = other.height;
	depth = other.depth;
	setType(CString("SLOTLocation"));
	handlingMethod = other.handlingMethod;
	IsSelect = other.IsSelect;
	IsOverridden = other.IsOverridden;
	ChangedInPass = other.ChangedInPass;
	isChanged = other.isChanged;
	locationProfileId = other.locationProfileId;
	Status = other.Status;
	IsActive = other.IsActive;
	BackfillId = other.BackfillId;
	BackfillCoordinates = other.BackfillCoordinates;
	StockerId = other.StockerId;
	StockerCoordinates = other.StockerCoordinates;
	Trace = other.Trace;
	Clearance = other.Clearance;
	selectionSequence = other.selectionSequence;
	replenishmentSequence = other.replenishmentSequence;
	locationKey = other.locationKey;
	
	if (other.pLocationProfile != NULL)
		pLocationProfile = new CLocationProfile(*other.pLocationProfile);
	else {
		if (pLocationProfile != NULL) {
			delete pLocationProfile;
			pLocationProfile = NULL;
		}
	}

	for (int i=0; i < m_InfoList.GetSize(); ++i)
		delete m_InfoList[i];

	m_InfoList.RemoveAll();

	for (i=0; i < other.m_InfoList.GetSize(); ++i) {
		CLevelProfileExternalInfo *pInfo = new CLevelProfileExternalInfo(*other.m_InfoList[i]);
		m_InfoList.Add(pInfo);
	}
}

qqhSLOTLocation::~qqhSLOTLocation() 
{ 
	if (pLocationProfile != NULL) 
		delete pLocationProfile; 

	for (int i=0; i < m_InfoList.GetSize(); ++i)
		delete m_InfoList[i];
}

qqhSLOTLocation& qqhSLOTLocation::operator=(const qqhSLOTLocation& other)
{
	DBID = other.DBID;
	description = other.description;
	coord = other.coord;
	maxWeight = other.maxWeight;
	setType(CString("SLOTLocation"));
	width = other.width;
	height = other.height;
	depth = other.depth;
	handlingMethod = other.handlingMethod;
	IsSelect = other.IsSelect;
	IsOverridden = other.IsOverridden;
	ChangedInPass = other.ChangedInPass;
	isChanged = other.isChanged;
	locationProfileId = other.locationProfileId;
	Status = other.Status;
	IsActive = other.IsActive;
	BackfillId = other.BackfillId;
	BackfillCoordinates = other.BackfillCoordinates;
	StockerId = other.StockerId;
	StockerCoordinates = other.StockerCoordinates;
	Trace = other.Trace;
	Clearance = other.Clearance;
	selectionSequence = other.selectionSequence;
	replenishmentSequence = other.replenishmentSequence;
	locationKey = other.locationKey;

	if (other.pLocationProfile != NULL)
		pLocationProfile = new CLocationProfile(*other.pLocationProfile);
	else {
		if (pLocationProfile != NULL) {
			delete pLocationProfile;
			pLocationProfile = NULL;
		}
	}

	for (int i=0; i < m_InfoList.GetSize(); ++i)
		delete m_InfoList[i];

	m_InfoList.RemoveAll();

	for (i=0; i < other.m_InfoList.GetSize(); ++i) {
		CLevelProfileExternalInfo *pInfo = new CLevelProfileExternalInfo(*other.m_InfoList[i]);
		m_InfoList.Add(pInfo);
	}

	return *this;
}

BOOL qqhSLOTLocation::operator==(const qqhSLOTLocation &other)
{
	if (description != other.description) return FALSE;
	if (maxWeight != other.maxWeight) return FALSE;
	if (handlingMethod != other.handlingMethod) return FALSE;
	if (IsSelect != other.IsSelect) return FALSE;
	if (IsOverridden != other.IsOverridden) return FALSE;
	if (width != other.width) return FALSE;
	if (height != other.height) return FALSE;
	if (depth != other.depth) return FALSE;
	if (Status != other.Status) return FALSE;
	if (IsActive != other.IsActive) return FALSE;
	//if (BackfillId != other.BackfillId) return FALSE;
	if (BackfillCoordinates != other.BackfillCoordinates) return FALSE;
	if (StockerId != other.StockerId) return FALSE;
	if (StockerCoordinates != other.StockerCoordinates) return FALSE;
	if (Trace != other.Trace) return FALSE;
	if (Clearance != other.Clearance) return FALSE;
	if (locationProfileId != other.locationProfileId) return FALSE;
	if (selectionSequence != other.selectionSequence) return FALSE;
	if (replenishmentSequence != other.replenishmentSequence) return FALSE;
	if (locationKey != other.locationKey) return FALSE;

	if (m_InfoList.GetSize() != other.m_InfoList.GetSize()) return FALSE;

	for (int i=0; i < m_InfoList.GetSize(); ++i) {
		if (*m_InfoList[i] != *other.m_InfoList[i])
			return FALSE;
	}

	return TRUE;
}

void qqhSLOTLocation::BuildFromStream(CSsaStringArray &bufArray ) {
	char tempBuf[512];
	char tempREGName[100];
	char tempREGVal[256];
	char tempheader[100];
	int i;
	float tempfloat;
	CSsaStringArray tempArray;
	CString tempChkStr;
	
	for (i=0; i < this->m_InfoList.GetSize(); ++i)
		delete m_InfoList[i];

	m_InfoList.RemoveAll();

	for ( i = 0; i  < bufArray.GetSize(); i++ ) {
		if ( bufArray[i].Find(REGBegSearch) != -1 ) {
			/////////////////////////////////////////////////////////////
			//  REGs are processed here
			/////////////////////////////////////////////////////////////
			strcpy(tempBuf,bufArray[i].GetBuffer(0));
			bufArray[i].ReleaseBuffer();
			tempChkStr = tempBuf;
			if (tempChkStr.GetAt(tempChkStr.GetLength()-1) == '|')
				strcat(tempBuf,"1");
			if (strchr(tempBuf,'\n') == NULL)
				strcat(tempBuf,"\n");
			bufArray[i] = tempBuf;
			strcpy(tempheader,strtok(tempBuf,"|"));
			strcpy(tempREGName,strtok(NULL,"|"));
			if (strstr(bufArray[i].GetBuffer(0),"|\n") == NULL )
				strcpy(tempREGVal,strtok(NULL,"\n"));
			else
				strcpy(tempREGVal,"");
			bufArray[i].ReleaseBuffer();
			if ( strcmp(tempREGName,"Description") == 0 )
				this->description = tempREGVal;
			else if ( strcmp(tempREGName,"MaxWeight") == 0 )
				this->maxWeight = (float)atof(tempREGVal);
			else if ( strcmp(tempREGName,"DBID") == 0 )
				this->DBID = atoi(tempREGVal);
			else if ( strcmp(tempREGName,"HandlingMethod") == 0 )
				this->handlingMethod = atoi(tempREGVal);
			else if ( strcmp(tempREGName,"IsSelect") == 0 ) {
				if ((strcmp(tempREGVal,"TRUE") == 0 ) || (strcmp(tempREGVal,"true") == 0 ))
					this->IsSelect = 1;
				else
					this->IsSelect = 0;
			}
			else if ( strcmp(tempREGName,"IsOverridden") == 0 ) {
				if ((strcmp(tempREGVal,"TRUE") == 0 ) || (strcmp(tempREGVal,"true") == 0 ))
					this->IsOverridden = 1;
				else
					this->IsOverridden = 0;
			}
			else if ( strcmp(tempREGName,"ChangedInPass") == 0 ) {
				if ((strcmp(tempREGVal,"TRUE") == 0 ) || (strcmp(tempREGVal,"true") == 0 ))
					this->ChangedInPass = 1;
				else
					this->ChangedInPass = 0;
			}
			else if ( strcmp(tempREGName,"IsChanged") == 0 )
				this->isChanged = tempREGVal;

			else if ( strcmp(tempREGName,"Status") == 0 )
				this->Status = atoi(tempREGVal);

			else if ( strcmp(tempREGName,"IsActive") == 0 )
				this->IsActive = atoi(tempREGVal);

			// Backfill Stuff
			else if ( strcmp(tempREGName,"BackfillId") == 0 )
				this->BackfillId = tempREGVal;
			else if ( strcmp(tempREGName,"BackfillXCoordinate") == 0 )
				this->BackfillCoordinates.setX(atoi(tempREGVal));
			else if ( strcmp(tempREGName,"BackfillYCoordinate") == 0 )
				this->BackfillCoordinates.setY(atoi(tempREGVal));
			else if ( strcmp(tempREGName,"BackfillZCoordinate") == 0 )
				this->BackfillCoordinates.setZ(atoi(tempREGVal));

			// Stocker Stuff
			else if ( strcmp(tempREGName,"StockerId") == 0 )
				this->StockerId = tempREGVal;
			else if ( strcmp(tempREGName,"StockerXCoordinate") == 0 )
				this->StockerCoordinates.setX(atoi(tempREGVal));
			else if ( strcmp(tempREGName,"StockerYCoordinate") == 0 )
				this->StockerCoordinates.setY(atoi(tempREGVal));
			else if ( strcmp(tempREGName,"StockerZCoordinate") == 0 )
				this->StockerCoordinates.setZ(atoi(tempREGVal));

			else if ( strcmp(tempREGName, "Trace") == 0)
				this->Trace = atoi(tempREGVal);
			else if ( strcmp(tempREGName, "Clearance") == 0)
				this->Clearance = atof(tempREGVal);
			else if ( strcmp(tempREGName, "LocationProfileId") == 0)
				this->locationProfileId = atoi(tempREGVal);

			else if ( strcmp(tempREGName, "SelectionSequence") == 0)
				this->selectionSequence = tempREGVal;
			else if ( strcmp(tempREGName, "ReplenishmentSequence") == 0)
				this->replenishmentSequence = tempREGVal;
			else if ( strcmp(tempREGName, "LocationKey") == 0)
				this->locationKey = atoi(tempREGVal);

			else if ( strcmp(tempREGName, "ExternalInfo") == 0) {
				CLevelProfileExternalInfo *pInfo = new CLevelProfileExternalInfo;
				CString temp = tempREGVal;
				int idx = temp.Find("^");
				pInfo->m_ExternalInfoDBId = atoi(temp.Left(idx));
				pInfo->m_Value = temp.Mid(idx+1);
				this->m_InfoList.Add(pInfo);
			}
		}
	
		/////////////////////////////////////////////////////////////
		//  Holder Coordinate
		/////////////////////////////////////////////////////////////
		if ( (bufArray[i]).Find(CoordBegSearch) != -1 ) {
			tempArray.RemoveAll();
			tempArray.Add(bufArray[i]);
			i++;
			while ( (bufArray[i]).Find(CoordEndSearch) == -1 ) {
				tempArray.Add(bufArray[i]);
				i++;
			}
			tempArray.Add(bufArray[i]);
			this->coord.BuildFromStream(tempArray);
		}

		/////////////////////////////////////////////////////////////
		//  Dimension
		/////////////////////////////////////////////////////////////
		if ( bufArray[i].Find(DimenBegSearch) != -1  ) {
			i++;
			while ( (bufArray[i].Find(DimenEndSearch) == -1 ) ) {
				strcpy(tempBuf,bufArray[i]);
				strcpy(tempheader,strtok(tempBuf,"|"));
				strcpy(tempREGName,strtok(NULL,"|"));
				strcpy(tempREGVal,strtok(NULL,"\n"));
				if ( strcmp(tempREGName,"Width") == 0 ) {
					tempfloat = (float) (atof(tempREGVal));
					this->width = tempfloat;
				}
				else if ( strcmp(tempREGName,"Depth") == 0 ) {
					tempfloat = (float) (atof(tempREGVal));
					this->depth = tempfloat;
				}
				else if ( strcmp(tempREGName,"Height") == 0 ) {
					tempfloat = (float) (atof(tempREGVal));
					this->height = tempfloat;
				}
				i++;
			}
		}
	}
	return;
}

void qqhSLOTLocation::StreamAttributes(CSsaStringArray & attributBuf) {

	CString tempBuf;

	attributBuf.Add(CString(LocationBegSearch) + "\n");


	tempBuf.Format("%sDBID|%d\n", REGBegSearch, this->DBID );
	attributBuf.Add(tempBuf);

	tempBuf.Format("%sDescription|%s\n", REGBegSearch, this->description);
	
	attributBuf.Add(tempBuf);

	tempBuf.Format("%sType|%s\n", REGBegSearch, this->type);
	attributBuf.Add(tempBuf);

	tempBuf.Format("%sMaxWeight|%f\n", REGBegSearch, (float)this->maxWeight);
	attributBuf.Add(tempBuf);

	tempBuf.Format("%sHandlingMethod|%d\n", REGBegSearch, this->handlingMethod);
	attributBuf.Add(tempBuf);

	if (this->IsSelect == 1)
		tempBuf.Format("%sIsSelect|TRUE\n",REGBegSearch);
	else
		tempBuf.Format("%sIsSelect|FALSE\n",REGBegSearch);
	attributBuf.Add(tempBuf);

	if (this->IsOverridden == 1)
		tempBuf.Format("%sIsOverridden|TRUE\n",REGBegSearch);
	else
		tempBuf.Format("%sIsOverridden|FALSE\n",REGBegSearch);
	attributBuf.Add(tempBuf);

	if (this->ChangedInPass == 1)
		tempBuf.Format("%sChangedInPass|TRUE\n",REGBegSearch);
	else
		tempBuf.Format("%sChangedInPass|FALSE\n",REGBegSearch);
	attributBuf.Add(tempBuf);

	tempBuf.Format("%sIsChanged|%s\n", REGBegSearch, this->isChanged);
	attributBuf.Add(tempBuf);

	tempBuf.Format("%sStatus|%d\n", REGBegSearch, this->Status);
	attributBuf.Add(tempBuf);

	tempBuf.Format("%sIsActive|%d\n", REGBegSearch, this->IsActive);
	attributBuf.Add(tempBuf);

	// Backfill Stuff
	tempBuf.Format("%sBackfillId|%s\n", REGBegSearch, this->BackfillId);
	attributBuf.Add(tempBuf);
	tempBuf.Format("%sBackfillXCoordinate|%d\n", REGBegSearch, this->BackfillCoordinates.getX());
	attributBuf.Add(tempBuf);
	tempBuf.Format("%sBackfillYCoordinate|%d\n", REGBegSearch, this->BackfillCoordinates.getY());
	attributBuf.Add(tempBuf);
	tempBuf.Format("%sBackfillZCoordinate|%d\n", REGBegSearch, this->BackfillCoordinates.getZ());
	attributBuf.Add(tempBuf);

	// Stocker Stuff
	tempBuf.Format("%sStockerId|%s\n", REGBegSearch, this->StockerId);
	attributBuf.Add(tempBuf);
	tempBuf.Format("%sStockerXCoordinate|%d\n", REGBegSearch, this->StockerCoordinates.getX());
	attributBuf.Add(tempBuf);
	tempBuf.Format("%sStockerYCoordinate|%d\n", REGBegSearch, this->StockerCoordinates.getY());
	attributBuf.Add(tempBuf);
	tempBuf.Format("%sStockerZCoordinate|%d\n", REGBegSearch, this->StockerCoordinates.getZ());
	attributBuf.Add(tempBuf);

	tempBuf.Format("%sTrace|%d\n", REGBegSearch, this->Trace);
	attributBuf.Add(tempBuf);

	tempBuf.Format("%sClearance|%f\n", REGBegSearch, this->Clearance);
	attributBuf.Add(tempBuf);

	tempBuf.Format("%sSelectionSequence|%s\n", REGBegSearch, this->selectionSequence);
	attributBuf.Add(tempBuf);

	tempBuf.Format("%sReplenishmentSequence|%s\n", REGBegSearch, this->replenishmentSequence);
	attributBuf.Add(tempBuf);

	tempBuf.Format("%sLocationProfileId|%d\n", REGBegSearch, this->locationProfileId);
	attributBuf.Add(tempBuf);

	tempBuf.Format("%sLocationKey|%d\n", REGBegSearch, this->locationKey);
	attributBuf.Add(tempBuf);

	// Dimension
	attributBuf.Add(CString(DimenBegSearch)+"\n");

	tempBuf.Format("%sWidth|%f\n", REGBegSearch, this->width);
	attributBuf.Add(tempBuf);

	tempBuf.Format("%sDepth|%f\n", REGBegSearch, this->depth);
	attributBuf.Add(tempBuf);

	tempBuf.Format("%sHeight|%f\n", REGBegSearch, this->height);
	attributBuf.Add(tempBuf);

	attributBuf.Add(CString(DimenEndSearch)+"\n");

	this->coord.StreamAttributes(attributBuf);

	// External attributes are a one-way trip.  We will send them to the database
	// whenever we save but we don't read them unless we specifically need them such
	// as in location properties
	for (int i=0; i < this->m_InfoList.GetSize(); ++i) {
		CLevelProfileExternalInfo *pInfo = m_InfoList[i];
		if (pInfo->m_Value != pInfo->m_DefaultValue) {
			tempBuf.Format("%sExternalInfo|%d^%s\n", REGBegSearch, pInfo->m_ExternalInfoDBId, 
				pInfo->m_Value);
			attributBuf.Add(tempBuf);
		}
	}

	attributBuf.Add(CString(LocationEndSearch) + "\n");

	return;
}

CString qqhSLOTLocation::getStatusText()
{
	// todo: convert these to read from somewhere
	switch (Status) {
	case LOC_STATUS_NOT_INTEGRATED:
		return "Not Integrated";
		break;
	case LOC_STATUS_INTEGRATED:
		return "Integrated";
		break;
	case LOC_STATUS_INTEGRATION_PENDING:
		return "Integration Pending";
		break;
	default:
		return "Unknown";
		break;
	}
}

////////////////////////////////////////
// qqhSLOTLevel Member functions
////////////////////////////////////////
qqhSLOTLevel::~qqhSLOTLevel() {
	
	childList.RemoveAll(); 

	if (pLevelProfile != NULL) 
		delete pLevelProfile; 

}

qqhSLOTLevel::qqhSLOTLevel()
{
	setType(CString("SLOTLevel"));
	DBID = 0;
	description = "";
	forkFixedInsertion = 0.0;
	isChanged = "TRUE";
	facingGap = facingSnap = minLocWidth = productGap = productSnap = 0.0;
	isOverridden = isRotateAllowed = isVariableLocationsAllowed = FALSE;
	levelProfileId = 0;
	pLevelProfile = NULL;
}

qqhSLOTLevel::qqhSLOTLevel(const qqhSLOTLevel& other)
{
	DBID = other.DBID;
	description = other.description;
	coord = other.coord;
	childList.Copy(other.childList);
	setType(CString("SLOTLevel"));
	UDFList.Copy(other.UDFList);
	forkFixedInsertion = other.forkFixedInsertion;
	isChanged = other.isChanged;
	isRotateAllowed = other.isRotateAllowed;
	isVariableLocationsAllowed = other.isVariableLocationsAllowed;
	facingGap = other.facingGap;
	facingSnap = other.facingSnap;
	minLocWidth = other.minLocWidth;
	productGap = other.productGap;
	productSnap = other.productSnap;
	isOverridden = other.isOverridden;
	levelProfileId = other.levelProfileId;

	if (other.pLevelProfile != NULL)
		pLevelProfile = new CLevelProfile(*other.pLevelProfile);
	else {
		if (pLevelProfile != NULL) {
			delete pLevelProfile;
			pLevelProfile = NULL;
		}
	}

}

qqhSLOTLevel& qqhSLOTLevel::operator=(const qqhSLOTLevel& other)
{
	DBID = other.DBID;
	description = other.description;
	coord = other.coord;
	childList.Copy(other.childList);
	setType(CString("SLOTLevel"));
	UDFList.Copy(other.UDFList);
	forkFixedInsertion = other.forkFixedInsertion;
	isChanged = other.isChanged;
	isRotateAllowed = other.isRotateAllowed;
	isVariableLocationsAllowed = other.isVariableLocationsAllowed;
	facingGap = other.facingGap;
	facingSnap = other.facingSnap;
	minLocWidth = other.minLocWidth;
	productGap = other.productGap;
	productSnap = other.productSnap;
	isOverridden = other.isOverridden;
	levelProfileId = other.levelProfileId;

	if (other.pLevelProfile != NULL)
		pLevelProfile = new CLevelProfile(*other.pLevelProfile);
	else {
		if (pLevelProfile != NULL) {
			delete pLevelProfile;
			pLevelProfile = NULL;
		}
	}

	return *this;
}

BOOL qqhSLOTLevel::operator==(const qqhSLOTLevel &other)
{
	if (description != other.description) return FALSE;
	if (forkFixedInsertion != other.forkFixedInsertion) return FALSE;
	if (isRotateAllowed != other.isRotateAllowed) return FALSE;
	if (isVariableLocationsAllowed != other.isVariableLocationsAllowed) return FALSE;
	if (facingGap != other.facingGap) return FALSE;
	if (facingSnap != other.facingSnap) return FALSE;
	if (minLocWidth != other.minLocWidth) return FALSE;
	if (productGap != other.productGap) return FALSE;
	if (productSnap != other.productSnap) return FALSE;
	if (isOverridden != other.isOverridden) return FALSE;
	if (levelProfileId != other.levelProfileId) return FALSE;

	return TRUE;
}

/////////////////////////////////////////////////////////////
//  Take a streamed level and make a level object
/////////////////////////////////////////////////////////////
void qqhSLOTLevel::BuildFromStream(CSsaStringArray &bufArray ) {
	char tempBuf[512];
	char tempREGName[100];
	char tempREGVal[256];
	char tempheader[100];
	int i;
	CSsaStringArray tempArray;
	CString tempChkStr;

	UDFList.RemoveAll();

	for ( i = 0; i < bufArray.GetSize(); i++ ) {
		if ( bufArray[i].Find(REGBegSearch) != -1 ) {
			/////////////////////////////////////////////////////////////
			//  REGs are processed here
			/////////////////////////////////////////////////////////////
			strcpy(tempBuf,bufArray[i].GetBuffer(0));
			bufArray[i].ReleaseBuffer();
			tempChkStr = tempBuf;
			if (tempChkStr.GetAt(tempChkStr.GetLength()-1) == '|')
				strcat(tempBuf,"1");
			if (strchr(tempBuf,'\n') == NULL)
				strcat(tempBuf,"\n");
			bufArray[i] = tempBuf;
			strcpy(tempheader,strtok(tempBuf,"|"));
			strcpy(tempREGName,strtok(NULL,"|"));
			if (strstr(bufArray[i].GetBuffer(0),"|\n") == NULL )
				strcpy(tempREGVal,strtok(NULL,"\n"));
			else
				strcpy(tempREGVal,"");
			bufArray[i].ReleaseBuffer();
			if ( strcmp(tempREGName,"Description") == 0 )
				this->description = tempREGVal;
			else if ( strcmp(tempREGName,"DBID") == 0 )
				this->DBID = atoi(tempREGVal);
			else if ( strcmp(tempREGName,"ForkFixedInsertion") == 0 )
				this->forkFixedInsertion = atof(tempREGVal);
			else if ( strcmp(tempREGName,"IsChanged") == 0 )
				this->isChanged = tempREGVal;
			else if ( strcmp(tempREGName,"IsRotateAllowed") == 0 ) {
				if ((strcmp(tempREGVal,"TRUE") == 0 ) || (strcmp(tempREGVal,"true") == 0 ))
					this->isRotateAllowed = TRUE;
				else
					this->isRotateAllowed = FALSE;
			}
			else if ( strcmp(tempREGName,"IsVarLocAllowed") == 0 ) {
				if ((strcmp(tempREGVal,"TRUE") == 0 ) || (strcmp(tempREGVal,"true") == 0 ))
					this->isVariableLocationsAllowed = TRUE;
				else
					this->isVariableLocationsAllowed = FALSE;
			}
			else if ( strcmp(tempREGName,"FacingGap") == 0 )
				this->facingGap = (float) atof(tempREGVal);
			else if ( strcmp(tempREGName,"FacingSnap") == 0 )
				this->facingSnap = (float) atof(tempREGVal);
			else if ( strcmp(tempREGName,"MinimumLocWidth") == 0 )
				this->minLocWidth = (float) atof(tempREGVal);
			else if ( strcmp(tempREGName,"ProductGap") == 0 )
				this->productGap = (float) atof(tempREGVal);
			else if ( strcmp(tempREGName,"ProductSnap") == 0 )
				this->productSnap = (float) atof(tempREGVal);
			else if ( strcmp(tempREGName,"LevelProfileId") == 0 )
				this->levelProfileId = atoi(tempREGVal);
			else if ( strcmp(tempREGName,"IsOverridden") == 0 ) {
				if ((strcmp(tempREGVal,"TRUE") == 0 ) || (strcmp(tempREGVal,"true") == 0 ))
					this->isOverridden = TRUE;
				else
					this->isOverridden = FALSE;
			}
		}
	
		/////////////////////////////////////////////////////////////
		//  Holder Coordinate
		/////////////////////////////////////////////////////////////
		if ( (bufArray[i]).Find(CoordBegSearch) != -1 ) {
			tempArray.RemoveAll();
			tempArray.Add(bufArray[i]);
			i++;
			while ( (bufArray[i]).Find(CoordEndSearch) == -1 ) {
				tempArray.Add(bufArray[i]);
				i++;
			}
			tempArray.Add(bufArray[i]);
			this->coord.BuildFromStream(tempArray);
			tempArray.RemoveAll();
		}
	}

	return;
}

void qqhSLOTLevel::StreamAttributes(CSsaStringArray & attributBuf) {

	CString tempBuf;

	attributBuf.Add(CString(LevelBegSearch) + "\n");

	tempBuf.Format("%sDBID|%d\n", REGBegSearch, this->DBID );
	attributBuf.Add(tempBuf);
	
	tempBuf.Format("%sDescription|%s\n", REGBegSearch, this->description);
	attributBuf.Add(tempBuf);

	tempBuf.Format("%sType|%s\n", REGBegSearch, this->type);
	attributBuf.Add(tempBuf);

	tempBuf.Format("%sForkFixedInsertion|%f\n", REGBegSearch, (float)this->forkFixedInsertion);
	attributBuf.Add(tempBuf);

	tempBuf.Format("%sIsChanged|%s\n", REGBegSearch, this->isChanged);
	attributBuf.Add(tempBuf);

	if (isRotateAllowed)
		tempBuf.Format("%sIsRotateAllowed|TRUE\n", REGBegSearch);
	else
		tempBuf.Format("%sIsRotateAllowed|FALSE\n", REGBegSearch);
	attributBuf.Add(tempBuf);

	if (isVariableLocationsAllowed)
		tempBuf.Format("%sIsVarLocAllowed|TRUE\n", REGBegSearch);
	else
		tempBuf.Format("%sIsVarLocAllowed|FALSE\n", REGBegSearch);
	attributBuf.Add(tempBuf);

	tempBuf.Format("%sFacingGap|%f\n", REGBegSearch, (float)this->facingGap);
	attributBuf.Add(tempBuf);

	tempBuf.Format("%sFacingSnap|%f\n", REGBegSearch, (float)this->facingSnap);
	attributBuf.Add(tempBuf);

	tempBuf.Format("%sMinimumLocWidth|%f\n", REGBegSearch, (float)this->minLocWidth);
	attributBuf.Add(tempBuf);

	tempBuf.Format("%sProductGap|%f\n", REGBegSearch, (float)this->productGap);
	attributBuf.Add(tempBuf);

	tempBuf.Format("%sProductSnap|%f\n", REGBegSearch, (float)this->productSnap);
	attributBuf.Add(tempBuf);

	tempBuf.Format("%sLevelProfileId|%d\n", REGBegSearch, this->levelProfileId);
	attributBuf.Add(tempBuf);

	if (isOverridden)
		tempBuf.Format("%sIsOverridden|TRUE\n", REGBegSearch);
	else
		tempBuf.Format("%sIsOverridden|FALSE\n", REGBegSearch);
	attributBuf.Add(tempBuf);


	this->coord.StreamAttributes(attributBuf);


	attributBuf.Add(CString(LevelEndSearch) + "\n");

	return;
}

////////////////////////////////////////
// qqhSLOTBay Member functions
////////////////////////////////////////

qqhSLOTBay::qqhSLOTBay()
{
	DBID = 0;
//	barWidth = 0.0;
	//rackTypeID = 1;
	setType(CString("SLOTBay"));
	strcpy(acadHandle,"No Handle");
	isChanged = "TRUE";
	bayProfileId = 0;
	pBayProfile = NULL;
}

qqhSLOTBay::qqhSLOTBay(const qqhSLOTBay& other)
{
	DBID = other.DBID;
	//rackTypeID = other.rackTypeID;
	description = other.description;
	coord = other.coord;
	childList.Copy(other.childList);
//	barWidth = other.barWidth;
	strcpy(acadHandle,other.acadHandle);
	setType(CString("SLOTBay"));
	UDFList.Copy(other.UDFList);
	isChanged = other.isChanged;
	bayProfileId = other.bayProfileId;

	if (other.pBayProfile != NULL)
		pBayProfile = new CBayProfile(*other.pBayProfile);
	else {
		if (pBayProfile != NULL) {
			delete pBayProfile;
			pBayProfile = NULL;
		}
	}
}

qqhSLOTBay::~qqhSLOTBay() 
{
	childList.RemoveAll();  

	if (pBayProfile != NULL) 
		delete pBayProfile; 
}

qqhSLOTBay& qqhSLOTBay::operator=(const qqhSLOTBay& other)
{
	DBID = other.DBID;
	//rackTypeID = other.rackTypeID;
	description = other.description;
	coord = other.coord;
	childList.Copy(other.childList);
	strcpy(acadHandle,other.acadHandle);
	setType(CString("SLOTBay"));
	UDFList.Copy(other.UDFList);
	isChanged = other.isChanged;
	bayProfileId = other.bayProfileId;
	if (other.pBayProfile != NULL)
		pBayProfile = new CBayProfile(*other.pBayProfile);
	else {
		if (pBayProfile != NULL) {
			delete pBayProfile;
			pBayProfile = NULL;
		}
	}


	return *this;
}

BOOL qqhSLOTBay::operator==(const qqhSLOTBay &other)
{
	if (description != other.description) return FALSE;
	if (bayProfileId != other.bayProfileId) return FALSE;
	if (UDFList.GetSize() != other.UDFList.GetSize()) return FALSE;

	for (int i=0; i < UDFList.GetSize(); ++i) {
		if (UDFList[i] != other.UDFList[i]) return FALSE;
	}

	return TRUE;
}

void qqhSLOTBay::BuildFromStream(CSsaStringArray &bufArray ) {

	char tempBuf[512];
	char tempREGName[100];
	char tempREGVal[256];
	char tempheader[100];
	int i;
	CSsaStringArray tempArray;
	CString tempChkStr;

	UDFList.RemoveAll();

	////FILE * fp = fopen("C:\\junk.txt","w");
	for ( i = 0; i < bufArray.GetSize(); i++ ) {
		if ( bufArray[i].Find(REGBegSearch) != -1 ) {
			/////////////////////////////////////////////////////////////
			//  REGs are processed here
			/////////////////////////////////////////////////////////////
			strcpy(tempBuf,bufArray[i].GetBuffer(0));
			////fprintf(fp,"%s\n",tempBuf);
			bufArray[i].ReleaseBuffer();
			// to avoid strtok errors when no value after '|' sign
			tempChkStr = tempBuf;
			if (tempChkStr.GetAt(tempChkStr.GetLength()-1) == '|')
				strcat(tempBuf,"1");
			if (strchr(tempBuf,'\n') == NULL)
				strcat(tempBuf,"\n");
			bufArray[i] = tempBuf;
			strcpy(tempheader,strtok(tempBuf,"|"));
			strcpy(tempREGName,strtok(NULL,"|"));
			if (strstr(bufArray[i].GetBuffer(0),"|\n") == NULL )
				strcpy(tempREGVal,strtok(NULL,"\n"));
			else
				strcpy(tempREGVal,"");
			bufArray[i].ReleaseBuffer();
			if ( strcmp(tempREGName,"Description") == 0 )
				this->description = tempREGVal;
			else if (strcmp(tempREGName,"DBID") == 0 )
				this->DBID = atoi(tempREGVal);
			else if (strcmp(tempREGName,"AcadHandle") == 0 )
				strcpy(this->acadHandle,tempREGVal);
			else if ( strcmp(tempREGName,"IsChanged") == 0 )
				this->isChanged = tempREGVal;
			else if (strcmp(tempREGName,"BayProfileId") == 0 )
				this->bayProfileId = atoi(tempREGVal);
			else if (strcmp(tempREGName,"IsEndBay") == 0 )
				this->isEndBay = atoi(tempREGVal);
		}
		/////////////////////////////////////////////////////////////
		//  UDFs 
		/////////////////////////////////////////////////////////////
		if ( bufArray[i].Find(UDFBegSearch) != -1 ) {
			strcpy(tempBuf,bufArray[i].GetBuffer(0));
			bufArray[i].ReleaseBuffer();
			strcpy(tempheader,strtok(tempBuf,"|"));
			strcpy(tempheader,strtok(NULL,"\n"));
			this->UDFList.Add(tempheader);
		}

		/////////////////////////////////////////////////////////////
		//  Holder Coordinate
		/////////////////////////////////////////////////////////////
		if ( (bufArray[i]).Find(CoordBegSearch) != -1 ) {
			tempArray.RemoveAll();
			tempArray.Add(bufArray[i]);
			i++;
			while ( (bufArray[i]).Find(CoordEndSearch) == -1 ) {
				tempArray.Add(bufArray[i]);
				i++;
			}
			tempArray.Add(bufArray[i]);
			this->coord.BuildFromStream(tempArray);
			tempArray.RemoveAll();
		}

	}

	return;
}

void qqhSLOTBay::StreamAttributes(CSsaStringArray & attributBuf) {

	int i;
	CString tempBuf;

	attributBuf.Add(CString(BayBegSearch) + "\n");

	tempBuf.Format("%sDBID|%d\n", REGBegSearch, this->DBID );
	attributBuf.Add(tempBuf);

	tempBuf.Format("%sDescription|%s\n", REGBegSearch, this->description);
	attributBuf.Add(tempBuf);

	tempBuf.Format("%sType|%s\n", REGBegSearch, this->type);
	attributBuf.Add(tempBuf);

	tempBuf.Format("%sAcadHandle|%s\n", REGBegSearch, this->acadHandle);
	attributBuf.Add(tempBuf);

	tempBuf.Format("%sIsChanged|%s\n", REGBegSearch, this->isChanged);
	attributBuf.Add(tempBuf);

	tempBuf.Format("%sBayProfileId|%d\n", REGBegSearch, this->bayProfileId );
	attributBuf.Add(tempBuf);

	tempBuf.Format("%sIsEndBay|%d\n", REGBegSearch, this->isEndBay );
	attributBuf.Add(tempBuf);

	for (i = 0; i < UDFList.GetSize(); i++)
		attributBuf.Add(CString(CString(UDFBegSearch) + UDFList[i] + "\n"));

	this->coord.StreamAttributes(attributBuf);

	attributBuf.Add(CString(BayEndSearch) + "\n");

	return;
}
////////////////////////////////////////
// qqhSLOTSide Member functions
////////////////////////////////////////

qqhSLOTSide::qqhSLOTSide()
{
	setType(CString("SLOTSide"));
	isChanged = "TRUE";
}

CArray <qqhSLOTBay, qqhSLOTBay&>& qqhSLOTSide::GetChildList()
{
	return childList;
}

qqhSLOTSide::qqhSLOTSide(const qqhSLOTSide& other)
{
	DBID = other.DBID;
	description = other.description;
	coord = other.coord;
	childList.Copy(other.childList);
	IsRotated = other.IsRotated;
	setType(CString("SLOTSide"));
	UDFList.Copy(other.UDFList);
	isChanged = other.isChanged;
}

qqhSLOTSide& qqhSLOTSide::operator=(const qqhSLOTSide& other)
{
	DBID = other.DBID;
	description = other.description;
	coord = other.coord;
	childList.Copy(other.childList);
	IsRotated = other.IsRotated;
	setType(CString("SLOTSide"));
	UDFList.Copy(other.UDFList);
	isChanged = other.isChanged;
	return *this;
}

BOOL qqhSLOTSide::operator==(const qqhSLOTSide &other)
{
	if (description != other.description) return FALSE;
	if (UDFList.GetSize() != other.UDFList.GetSize()) return FALSE;

	for (int i=0; i < UDFList.GetSize(); ++i) {
		if (UDFList[i] != other.UDFList[i]) return FALSE;
	}

	return TRUE;

}


void qqhSLOTSide::SetIsRotated(int rot)
{
	IsRotated = rot;
}

int qqhSLOTSide::GetIsRotated()
{
	return IsRotated;
}

void qqhSLOTSide::BuildFromStream(CSsaStringArray &bufArray ) {
	char tempBuf[512];
	char tempREGName[100];
	char tempREGVal[256];
	char tempheader[100];
	int i;
	CSsaStringArray tempArray;
	CString tempChkStr;

	UDFList.RemoveAll();
	
	for ( i = 0; i < bufArray.GetSize(); i++ ) {
		if ( bufArray[i].Find(REGBegSearch) != -1 ) {
			/////////////////////////////////////////////////////////////
			//  REGs are processed here
			/////////////////////////////////////////////////////////////
			strcpy(tempBuf,bufArray[i].GetBuffer(0));
			bufArray[i].ReleaseBuffer();
			tempChkStr = tempBuf;
			if (tempChkStr.GetAt(tempChkStr.GetLength()-1) == '|')
				strcat(tempBuf,"1");
			if (strchr(tempBuf,'\n') == NULL)
				strcat(tempBuf,"\n");
			bufArray[i] = tempBuf;
			strcpy(tempheader,strtok(tempBuf,"|"));
			strcpy(tempREGName,strtok(NULL,"|"));
			if (strstr(bufArray[i].GetBuffer(0),"|\n") == NULL )
				strcpy(tempREGVal,strtok(NULL,"\n"));
			else
				strcpy(tempREGVal,"");
			bufArray[i].ReleaseBuffer();
			if ( strcmp(tempREGName,"Description") == 0 )
				this->description = tempREGVal;
			else if ( strcmp(tempREGName,"DBID") == 0 ) 
				this->DBID = atoi(tempREGVal);
			else if ( strcmp(tempREGName,"IsRotated") == 0 )  {
				if ((strcmp(tempREGVal,"TRUE") == 0 ) || (strcmp(tempREGVal,"true") == 0 ))
					this->IsRotated = 1;
				else
					this->IsRotated = 0;
			}
			else if ( strcmp(tempREGName,"IsChanged") == 0 )
				this->isChanged = tempREGVal;
		}
	
		/////////////////////////////////////////////////////////////
		//  UDFs 
		/////////////////////////////////////////////////////////////
		if ( bufArray[i].Find(UDFBegSearch) != -1 ) {
			strcpy(tempBuf,bufArray[i].GetBuffer(0));
			bufArray[i].ReleaseBuffer();
			strcpy(tempheader,strtok(tempBuf,"|"));
			strcpy(tempheader,strtok(NULL,"\n"));
			this->UDFList.Add(tempheader);
		}
		/////////////////////////////////////////////////////////////
		//  Holder Coordinate
		/////////////////////////////////////////////////////////////
		if ( (bufArray[i]).Find(CoordBegSearch) != -1 ) {
			tempArray.RemoveAll();
			tempArray.Add(bufArray[i]);
			i++;
			while ( (bufArray[i]).Find(CoordEndSearch) == -1 ) {
				tempArray.Add(bufArray[i]);
				i++;
			}
			tempArray.Add(bufArray[i]);
			this->coord.BuildFromStream(tempArray);
			tempArray.RemoveAll();
		}

//		/////////////////////////////////////////////////////////////
//		//  Bay Objects
//		/////////////////////////////////////////////////////////////
//		if ( (bufArray[i]).Find(BayBegSearch) != -1 ) {
//			tempArray.RemoveAll();
//			tempArray.Add(bufArray[i]);
//			i++;
//			while ( (bufArray[i]).Find(BayEndSearch) == -1 ) {
//				tempArray.Add(bufArray[i]);
//				i++;
//			}
//			tempArray.Add(bufArray[i]);
//			i++;
//			this->.GetChildList().Add(BuildBayFromStream(tempArray));
//		}
	}
	return;
}

void qqhSLOTSide::StreamAttributes(CSsaStringArray & attributBuf) {

	int i;
	CString tempBuf;

	attributBuf.Add(CString(SideBegSearch) + "\n");

	tempBuf.Format("%sDBID|%d\n", REGBegSearch, this->DBID );
	attributBuf.Add(tempBuf);
	
	tempBuf.Format("%sDescription|%s\n", REGBegSearch, this->description);
	attributBuf.Add(tempBuf);

	tempBuf.Format("%sType|%s\n", REGBegSearch, this->type);
	attributBuf.Add(tempBuf);

	if (this->IsRotated == 1 )
		tempBuf.Format("%sIsRotated|TRUE\n", REGBegSearch);
	else
		tempBuf.Format("%sIsRotated|FALSE\n", REGBegSearch);
	attributBuf.Add(tempBuf);

	tempBuf.Format("%sIsChanged|%s\n", REGBegSearch, this->isChanged);
	attributBuf.Add(tempBuf);

	for (i = 0; i < UDFList.GetSize(); i++)
		attributBuf.Add(CString(CString(UDFBegSearch) + UDFList[i] + "\n"));

	this->coord.StreamAttributes(attributBuf);

//	for ( i = 0; i < this->childList.GetSize(); i++ ) {
//		StreamBayAttributes(this->childList[i], attributBuf);
//	}

	attributBuf.Add(CString(SideEndSearch) + "\n");
	return;
}

////////////////////////////////////////
// qqhSLOTAisle Member functions
////////////////////////////////////////

qqhSLOTAisle::qqhSLOTAisle()
{
	DBID = 0;
	setType(CString("SLOTAisle"));
	pickPath.setAcadHandle("XXX");
//	strcpy(pickPath.acadHandle,"XXX");
	isChanged = "TRUE";
	leftBayStart = "1";
	leftBayStep = 0;
	leftBayScheme = 0;
	rightBayStart = "1";
	rightBayStep = 0;
	rightBayScheme = 0;
	leftLevelStart = "1";
	leftLevelStep = 0;
	leftLevelScheme = 0;
	leftLevelBreak = 0;
	rightLevelStart = "1";
	rightLevelStep = 0;
	rightLevelScheme = 0;
	rightLevelBreak = 0;
	leftLocationStart = "1";
	leftLocationStep = 0;
	leftLocationScheme = 0;
	leftLocationBreak = 0;
	rightLocationStart = "1";
	rightLocationStep = 0;
	rightLocationScheme = 0;
	rightLocationBreak = 0;
	pickPathDirection = 0;
	pickPathType = 0;
	pickPathStartSide = 0;
	baysInPattern = 0;
	rotation = 0;
	rightBayPattern = " ";
	rightLevelPattern = " ";
	rightLocPattern = " ";
	leftBayPattern = " ";
	leftLevelPattern = " ";
	leftLocPattern = " ";
	aisleSpace = 0;
}

qqhSLOTAisle::qqhSLOTAisle(const qqhSLOTAisle& other)
{
	DBID = other.DBID;
	description = other.description;
	coord = other.coord;
	childList.Copy(other.childList);
	pickPath = other.pickPath;
	setType(CString("SLOTAisle"));
	UDFList.Copy(other.UDFList);
	isChanged = other.isChanged;
	leftBayStart = other.leftBayStart;
	leftBayStep = other.leftBayStep;
	leftBayScheme = other.leftBayScheme;
	rightBayStart = other.rightBayStart;
	rightBayStep = other.rightBayStep;
	rightBayScheme = other.rightBayScheme;
	leftLevelStart = other.leftLevelStart;
	leftLevelStep = other.leftLevelStep;
	leftLevelScheme = other.leftLevelScheme;
	leftLevelBreak = other.leftLevelBreak;
	rightLevelStart = other.rightLevelStart;
	rightLevelStep = other.rightLevelStep;
	rightLevelScheme = other.rightLevelScheme;
	rightLevelBreak = other.rightLevelBreak;
	leftLocationStart = other.leftLocationStart;
	leftLocationStep = other.leftLocationStep;
	leftLocationScheme = other.leftLocationScheme;
	leftLocationBreak = other.leftLocationBreak;
	rightLocationStart = other.rightLocationStart;
	rightLocationStep = other.rightLocationStep;
	rightLocationScheme = other.rightLocationScheme;
	rightLocationBreak = other.rightLocationBreak;
	pickPathDirection = other.pickPathDirection;
	pickPathType = other.pickPathType;
	pickPathStartSide = other.pickPathStartSide;
	baysInPattern = other.baysInPattern;
	rotation = other.rotation;
	rightBayPattern = other.rightBayPattern;
	rightLevelPattern = other.rightLevelPattern;
	rightLocPattern = other.rightLocPattern;
	leftBayPattern = other.leftBayPattern;
	leftLevelPattern = other.leftLevelPattern;
	leftLocPattern = other.leftLocPattern;
	aisleSpace = other.aisleSpace;
}

qqhSLOTAisle& qqhSLOTAisle::operator=(const qqhSLOTAisle& other)
{
	DBID = other.DBID;
	description = other.description;
	coord = other.coord;
	childList.Copy(other.childList);
	pickPath = other.pickPath;
	setType(CString("SLOTAisle"));
	UDFList.Copy(other.UDFList);
	isChanged = other.isChanged;
	leftBayStart = other.leftBayStart;
	leftBayStep = other.leftBayStep;
	leftBayScheme = other.leftBayScheme;
	rightBayStart = other.rightBayStart;
	rightBayStep = other.rightBayStep;
	rightBayScheme = other.rightBayScheme;
	leftLevelStart = other.leftLevelStart;
	leftLevelStep = other.leftLevelStep;
	leftLevelScheme = other.leftLevelScheme;
	leftLevelBreak = other.leftLevelBreak;
	rightLevelStart = other.rightLevelStart;
	rightLevelStep = other.rightLevelStep;
	rightLevelScheme = other.rightLevelScheme;
	rightLevelBreak = other.rightLevelBreak;
	leftLocationStart = other.leftLocationStart;
	leftLocationStep = other.leftLocationStep;
	leftLocationScheme = other.leftLocationScheme;
	leftLocationBreak = other.leftLocationBreak;
	rightLocationStart = other.rightLocationStart;
	rightLocationStep = other.rightLocationStep;
	rightLocationScheme = other.rightLocationScheme;
	rightLocationBreak = other.rightLocationBreak;
	pickPathDirection = other.pickPathDirection;
	pickPathType = other.pickPathType;
	pickPathStartSide = other.pickPathStartSide;
	baysInPattern = other.baysInPattern;
	rotation = other.rotation;
	rightBayPattern = other.rightBayPattern;
	rightLevelPattern = other.rightLevelPattern;
	rightLocPattern = other.rightLocPattern;
	leftBayPattern = other.leftBayPattern;
	leftLevelPattern = other.leftLevelPattern;
	leftLocPattern = other.leftLocPattern;
	aisleSpace = other.aisleSpace;

	return *this;
}

BOOL qqhSLOTAisle::operator==(const qqhSLOTAisle& other)
{
	if (description != other.description) return FALSE;
	if (leftBayStart != other.leftBayStart) return FALSE;
	if (leftBayStep != other.leftBayStep) return FALSE;
	if (leftBayScheme != other.leftBayScheme) return FALSE;
	if (rightBayStart != other.rightBayStart) return FALSE;
	if (rightBayStep != other.rightBayStep) return FALSE;
	if (rightBayScheme != other.rightBayScheme) return FALSE;
	if (leftLevelStart != other.leftLevelStart) return FALSE;
	if (leftLevelStep != other.leftLevelStep) return FALSE;
	if (leftLevelScheme != other.leftLevelScheme) return FALSE;
	if (leftLevelBreak != other.leftLevelBreak) return FALSE;
	if (rightLevelStart != other.rightLevelStart) return FALSE;
	if (rightLevelStep != other.rightLevelStep) return FALSE;
	if (rightLevelScheme != other.rightLevelScheme) return FALSE;
	if (rightLevelBreak != other.rightLevelBreak) return FALSE;
	if (leftLocationStart != other.leftLocationStart) return FALSE;
	if (leftLocationStep != other.leftLocationStep) return FALSE;
	if (leftLocationScheme != other.leftLocationScheme) return FALSE;
	if (leftLocationBreak != other.leftLocationBreak) return FALSE;
	if (rightLocationStart != other.rightLocationStart) return FALSE;
	if (rightLocationStep != other.rightLocationStep) return FALSE;
	if (rightLocationScheme != other.rightLocationScheme) return FALSE;
	if (rightLocationBreak != other.rightLocationBreak) return FALSE;
	if (pickPathDirection != other.pickPathDirection) return FALSE;
	if (pickPathType != other.pickPathType) return FALSE;
	if (pickPathStartSide != other.pickPathStartSide) return FALSE;
	if (baysInPattern != other.baysInPattern) return FALSE;
	if (rotation != other.rotation) return FALSE;
	if (rightBayPattern != other.rightBayPattern) return FALSE;
	if (rightLevelPattern != other.rightLevelPattern) return FALSE;
	if (rightLocPattern != other.rightLocPattern) return FALSE;
	if (leftBayPattern != other.leftBayPattern) return FALSE;
	if (leftLevelPattern != other.leftLevelPattern) return FALSE;
	if (leftLocPattern != other.leftLocPattern) return FALSE;
	if (aisleSpace != other.aisleSpace) return FALSE;

	if (UDFList.GetSize() != other.UDFList.GetSize()) return FALSE;
	for (int i=0; i < UDFList.GetSize(); ++i) {
		if (UDFList[i] != other.UDFList[i]) return FALSE;
	}
	
	return TRUE;
}

void qqhSLOTAisle::BuildFromStream( CSsaStringArray & bufArray ) {
	char tempBuf[512];
	char tempREGName[100];
	char tempREGVal[1000];
	char tempheader[100];
	int i;
	CSsaStringArray tempArray;
	CString tempChkStr;
	CStringArray parsedStrings;

	UDFList.RemoveAll();
	FILE *f = fopen("debug.txt", "w");

	for ( i = 0; i < bufArray.GetSize(); i++ ) {
		if ( bufArray[i].Find(REGBegSearch) != -1 ) {
			/////////////////////////////////////////////////////////////
			//  REGs are processed here
			/////////////////////////////////////////////////////////////
			fprintf(f,"%s\n", bufArray[i]);
			fflush(f);

			tempChkStr = bufArray[i].GetBuffer(0);

			if (tempChkStr.GetAt(tempChkStr.GetLength() - 1) == '\n')
				tempChkStr.SetAt(tempChkStr.GetLength() - 1, '|');
			utilityHelper.ParseString(tempChkStr, "|", parsedStrings);
			if (parsedStrings.GetSize() < 3) {
				ads_printf("parse error: %s\n", tempChkStr);
				AfxMessageBox("Generic Facility Error: Contact Support.");
				return;
			}
			strcpy(tempheader, parsedStrings[0]);
			strcpy(tempREGName, parsedStrings[1]);
			strcpy(tempREGVal, parsedStrings[2]);

			//evil code fixed 09/15/2003 do not uncomment
			/*strcpy(tempBuf,bufArray[i].GetBuffer(0));
			bufArray[i].ReleaseBuffer();
			tempChkStr = tempBuf;
			if (tempChkStr.GetAt(tempChkStr.GetLength()-1) == '|')
				strcat(tempBuf,"1");
			if (strchr(tempBuf,'\n') == NULL)
				strcat(tempBuf,"\n");
			bufArray[i] = tempBuf;
			strcpy(tempheader,strtok(tempBuf,"|"));
			strcpy(tempREGName,strtok(NULL,"|"));
			if (!strcmp(tempREGName,"RightLocPattern"))
				int a = 0;
			if (strstr(bufArray[i].GetBuffer(0),"|\n") == NULL )
				strcpy(tempREGVal,strtok(NULL,"\n"));
			else
				strcpy(tempREGVal,"");
			bufArray[i].ReleaseBuffer(); */
			
			

			if ( strcmp(tempREGName,"Description") == 0 )
				this->description = tempREGVal;
			else if ( strcmp(tempREGName,"DBID") == 0 )
				this->DBID = atoi(tempREGVal);
			else if ( strcmp(tempREGName,"LeftBayScheme") == 0 )
				this->leftBayScheme = atoi(tempREGVal);
			else if ( strcmp(tempREGName,"RightBayScheme") == 0 )
				this->rightBayScheme = atoi(tempREGVal);
			else if ( strcmp(tempREGName,"LeftBayStep") == 0 )
				this->leftBayStep = atoi(tempREGVal);
			else if ( strcmp(tempREGName,"RightBayStep") == 0 )
				this->rightBayStep = atoi(tempREGVal);
			else if ( strcmp(tempREGName,"LeftLevelScheme") == 0 )
				this->leftLevelScheme = atoi(tempREGVal);
			else if ( strcmp(tempREGName,"LeftLocScheme") == 0 )
				this->leftLocationScheme = atoi(tempREGVal);
			else if ( strcmp(tempREGName,"RightLevelScheme") == 0 )
				this->rightLevelScheme = atoi(tempREGVal);
			else if ( strcmp(tempREGName,"RightLocScheme") == 0 )
				this->rightLocationScheme = atoi(tempREGVal);
			else if ( strcmp(tempREGName,"LeftLevelStep") == 0 )
				this->leftLevelStep = atoi(tempREGVal);
			else if ( strcmp(tempREGName,"RightLevelStep") == 0 )
				this->rightLevelStep = atoi(tempREGVal);
			else if ( strcmp(tempREGName,"LeftLocStep") == 0 )
				this->leftLocationStep = atoi(tempREGVal);
			else if ( strcmp(tempREGName,"RightLocStep") == 0 )
				this->rightLocationStep = atoi(tempREGVal);
			else if ( strcmp(tempREGName,"LeftLevelBreak") == 0 )
				this->leftLevelBreak = atoi(tempREGVal);
			else if ( strcmp(tempREGName,"RightLevelBreak") == 0 )
				this->rightLevelBreak = atoi(tempREGVal);
			else if ( strcmp(tempREGName,"LeftLocBreak") == 0 )
				this->leftLocationBreak = atoi(tempREGVal);
			else if ( strcmp(tempREGName,"RightLocBreak") == 0 )
				this->rightLocationBreak = atoi(tempREGVal);
			else if ( strcmp(tempREGName,"RightBayStart") == 0 )
				this->rightBayStart = tempREGVal;
			else if ( strcmp(tempREGName,"LeftBayStart") == 0 )
				this->leftBayStart = tempREGVal;
			else if ( strcmp(tempREGName,"RightLevelStart") == 0 )
				this->rightLevelStart = tempREGVal;
			else if ( strcmp(tempREGName,"LeftLevelStart") == 0 )
				this->leftLevelStart = tempREGVal;
			else if ( strcmp(tempREGName,"RightLocStart") == 0 )
				this->rightLocationStart = tempREGVal;
			else if ( strcmp(tempREGName,"LeftLocStart") == 0 )
				this->leftLocationStart = tempREGVal;
			else if ( strcmp(tempREGName,"IsChanged") == 0 )
				this->isChanged = tempREGVal;
			else if ( strcmp(tempREGName,"PickPathDirection") == 0 )
				this->pickPathDirection = atoi(tempREGVal);
			else if ( strcmp(tempREGName,"PickPathType") == 0 )
				this->pickPathType = atoi(tempREGVal);
			else if ( strcmp(tempREGName,"PickPathStartSide") == 0 )
				this->pickPathStartSide = atoi(tempREGVal);
			else if ( strcmp(tempREGName,"BaysInPattern") == 0 )
				this->baysInPattern = atoi(tempREGVal);
			else if ( strcmp(tempREGName,"Rotation") == 0 )
				this->rotation = (float) atof(tempREGVal);
			else if ( strcmp(tempREGName,"RightBayPattern") == 0 )
				this->rightBayPattern = tempREGVal;
			else if ( strcmp(tempREGName,"RightLevelPattern") == 0 )
				this->rightLevelPattern = tempREGVal;
			else if ( strcmp(tempREGName,"RightLocPattern") == 0 )
				this->rightLocPattern = tempREGVal;
			else if ( strcmp(tempREGName,"LeftBayPattern") == 0 )
				this->leftBayPattern = tempREGVal;
			else if ( strcmp(tempREGName,"LeftLevelPattern") == 0 )
				this->leftLevelPattern = tempREGVal;
			else if ( strcmp(tempREGName,"LeftLocPattern") == 0 )
				this->leftLocPattern = tempREGVal;
			else if ( strcmp(tempREGName,"AisleSpace") == 0 )
				this->aisleSpace = atof(tempREGVal);
		}
	
		/////////////////////////////////////////////////////////////
		//  UDFs 
		/////////////////////////////////////////////////////////////
		if ( bufArray[i].Find(UDFBegSearch) != -1 ) {
			strcpy(tempBuf,bufArray[i].GetBuffer(0));
			bufArray[i].ReleaseBuffer();
			strcpy(tempheader,strtok(tempBuf,"|"));
			strcpy(tempheader,strtok(NULL,"\n"));
			this->UDFList.Add(tempheader);
		}
		/////////////////////////////////////////////////////////////
		//  Holder Coordinate
		/////////////////////////////////////////////////////////////
		if ( (bufArray[i]).Find(CoordBegSearch) != -1 ) {
			tempArray.RemoveAll();
			tempArray.Add(bufArray[i]);
			i++;
			while ( (bufArray[i]).Find(CoordEndSearch) == -1 ) {
				tempArray.Add(bufArray[i]);
				i++;
			}
			tempArray.Add(bufArray[i]);
			this->coord.BuildFromStream(tempArray);
			tempArray.RemoveAll();
		}
		if ( bufArray[i].Find(PathBegSearch) != -1 ) {
			tempArray.RemoveAll();
			tempArray.Add(bufArray[i]);
			i++;
			while ( (bufArray[i].Find(PathEndSearch) == -1 ) ) {
				tempArray.Add(bufArray[i]);
				i++;
			}
			this->pickPath.BuildFromStream(tempArray);
			tempArray.RemoveAll();
		}


//		/////////////////////////////////////////////////////////////
//		//  Side Objects
//		/////////////////////////////////////////////////////////////
//		if ( (bufArray[i]).Find(SideBegSearch) != -1 ) {
//			tempArray.RemoveAll();
//			tempArray.Add(bufArray[i]);
//			i++;
//			while ( (bufArray[i]).Find(SideEndSearch) == -1 ) {
//				tempArray.Add(bufArray[i]);
//				i++;
//			}
//			tempArray.Add(bufArray[i]);
//			i++;
//			this->GetChildList().Add(BuildSideFromStream(tempArray));
//		}
	}
	fclose(f);
	return;
}

void qqhSLOTAisle::StreamAttributes(CSsaStringArray & attributBuf) {

	int i;
	CString tempBuf;

	attributBuf.Add(CString(AisleBegSearch) + "\n");

	tempBuf.Format("%sDBID|%d\n", REGBegSearch, this->DBID );
	attributBuf.Add(tempBuf);

	tempBuf.Format("%sRightBayStep|%d\n", REGBegSearch, this->rightBayStep );
	attributBuf.Add(tempBuf);

	tempBuf.Format("%sLeftBayStep|%d\n", REGBegSearch, this->leftBayStep );
	attributBuf.Add(tempBuf);

	tempBuf.Format("%sRightLevelStep|%d\n", REGBegSearch, this->rightLevelStep );
	attributBuf.Add(tempBuf);

	tempBuf.Format("%sLeftLevelStep|%d\n", REGBegSearch, this->leftLevelStep );
	attributBuf.Add(tempBuf);

	tempBuf.Format("%sRightLocStep|%d\n", REGBegSearch, this->rightLocationStep );
	attributBuf.Add(tempBuf);

	tempBuf.Format("%sLeftLocStep|%d\n", REGBegSearch, this->leftLocationStep );
	attributBuf.Add(tempBuf);

	tempBuf.Format("%sRightBayScheme|%d\n", REGBegSearch, this->rightBayScheme );
	attributBuf.Add(tempBuf);

	tempBuf.Format("%sLeftBayScheme|%d\n", REGBegSearch, this->leftBayScheme );
	attributBuf.Add(tempBuf);

	tempBuf.Format("%sRightBayScheme|%d\n", REGBegSearch, this->rightBayScheme );
	attributBuf.Add(tempBuf);

	tempBuf.Format("%sLeftLevelScheme|%d\n", REGBegSearch, this->leftLevelScheme );
	attributBuf.Add(tempBuf);

	tempBuf.Format("%sRightLevelScheme|%d\n", REGBegSearch, this->rightLevelScheme );
	attributBuf.Add(tempBuf);

	tempBuf.Format("%sLeftLocScheme|%d\n", REGBegSearch, this->leftLocationScheme );
	attributBuf.Add(tempBuf);

	tempBuf.Format("%sRightLocScheme|%d\n", REGBegSearch, this->rightLocationScheme );
	attributBuf.Add(tempBuf);

	tempBuf.Format("%sPickPathDirection|%d\n", REGBegSearch, this->pickPathDirection );
	attributBuf.Add(tempBuf);

	tempBuf.Format("%sPickPathType|%d\n", REGBegSearch, this->pickPathType );
	attributBuf.Add(tempBuf);

	tempBuf.Format("%sPickPathStartSide|%d\n", REGBegSearch, this->pickPathStartSide );
	attributBuf.Add(tempBuf);

	tempBuf.Format("%sBaysInPattern|%d\n", REGBegSearch, this->baysInPattern );
	attributBuf.Add(tempBuf);

	tempBuf.Format("%sLeftLevelBreak|%d\n", REGBegSearch, this->leftLevelBreak );
	attributBuf.Add(tempBuf);

	tempBuf.Format("%sRightLevelBreak|%d\n", REGBegSearch, this->rightLevelBreak );
	attributBuf.Add(tempBuf);

	tempBuf.Format("%sLeftLocBreak|%d\n", REGBegSearch, this->leftLocationBreak );
	attributBuf.Add(tempBuf);

	tempBuf.Format("%sRightLocBreak|%d\n", REGBegSearch, this->rightLocationBreak );
	attributBuf.Add(tempBuf);

	tempBuf.Format("%sLeftBayStart|%s\n", REGBegSearch, this->leftBayStart);
	attributBuf.Add(tempBuf);

	tempBuf.Format("%sRightBayStart|%s\n", REGBegSearch, this->rightBayStart);
	attributBuf.Add(tempBuf);
	
	tempBuf.Format("%sLeftLevelStart|%s\n", REGBegSearch, this->leftLevelStart);
	attributBuf.Add(tempBuf);
	
	tempBuf.Format("%sRightLevelStart|%s\n", REGBegSearch, this->rightLevelStart);
	attributBuf.Add(tempBuf);
	
	tempBuf.Format("%sLeftLocStart|%s\n", REGBegSearch, this->leftLocationStart);
	attributBuf.Add(tempBuf);
	
	tempBuf.Format("%sRightLocStart|%s\n", REGBegSearch, this->rightLocationStart);
	attributBuf.Add(tempBuf);
	
	tempBuf.Format("%sDescription|%s\n", REGBegSearch, this->description);
	attributBuf.Add(tempBuf);

	tempBuf.Format("%sType|%s\n", REGBegSearch, this->type);
	attributBuf.Add(tempBuf);

	tempBuf.Format("%sIsChanged|%s\n", REGBegSearch, this->isChanged);
	attributBuf.Add(tempBuf);

	tempBuf.Format("%sRotation|%f\n", REGBegSearch, this->rotation);
	attributBuf.Add(tempBuf);

	tempBuf.Format("%sRightBayPattern|%s\n", REGBegSearch, this->rightBayPattern);
	attributBuf.Add(tempBuf);
	
	tempBuf.Format("%sRightLevelPattern|%s\n", REGBegSearch, this->rightLevelPattern);
	attributBuf.Add(tempBuf);
	
	tempBuf.Format("%sRightLocPattern|%s\n", REGBegSearch, this->rightLocPattern);
	attributBuf.Add(tempBuf);
	
	tempBuf.Format("%sLeftBayPattern|%s\n", REGBegSearch, this->leftBayPattern);
	attributBuf.Add(tempBuf);
	
	tempBuf.Format("%sLeftLevelPattern|%s\n", REGBegSearch, this->leftLevelPattern);
	attributBuf.Add(tempBuf);
	
	tempBuf.Format("%sLeftLocPattern|%s\n", REGBegSearch, this->leftLocPattern);
	attributBuf.Add(tempBuf);

	tempBuf.Format("%sAisleSpace|%f\n", REGBegSearch, this->aisleSpace);
	attributBuf.Add(tempBuf);	
	
	for (i = 0; i < UDFList.GetSize(); i++)
		attributBuf.Add(CString(CString(UDFBegSearch) + UDFList[i] + "\n"));

	this->coord.StreamAttributes(attributBuf);
	this->pickPath.StreamAttributes(attributBuf);

//	for ( i = 0; i < this->childList.GetSize(); i++ ) {
//		StreamSideAttributes(this->childList[i], attributBuf);
//	}

	attributBuf.Add(CString(AisleEndSearch) + "\n");
	return;
}

////////////////////////////////////////
// qqhSLOTSection Member functions
////////////////////////////////////////

qqhSLOTSection::qqhSLOTSection()
{
	DBID = 0;
	setType("SLOTSection");
	locationMask = "";
	avgCubePerTrip = forkDistFixed = forkDistVar =
	forkLaborRate = replenAvgDist = selectDistFixed =
	selectDistVar = selectLaborRate = 0.0;
	hotSpotList.RemoveAll();
	selDist = 0.0;
	avgOrdQty = 0;
	contQty = 0;
	orderCount = 0;
	applyBrokenOrder = "FALSE";
	isChanged = "TRUE";
	palletsPerPtwyTrip = insertForkTravel = pickupForkTravel =
	stockerDistFixed = stockerDistVar = stockerLaborRate = 0.0;
	WMSSectionId = " ";
}

CArray <qqhSLOTAisle, qqhSLOTAisle&>& qqhSLOTSection::GetChildList()
{
	return childList;
}

qqhSLOTSection::qqhSLOTSection(const qqhSLOTSection& other)
{
	DBID = other.DBID;
	description = other.description;
	coord = other.coord;
	childList.Copy(other.childList);
	setType("SLOTSection");
//	pickPath = other.pickPath;
	locationMask = other.locationMask;
	avgCubePerTrip = other.avgCubePerTrip;
	forkDistFixed = other.forkDistFixed;
	forkDistVar = other.forkDistVar;
	forkLaborRate = other.forkLaborRate;
	replenAvgDist = other.replenAvgDist;
	selectDistFixed = other.selectDistFixed;
	selectDistVar = other.selectDistVar;
	selectLaborRate = other.selectLaborRate;
	hotSpotList.Copy(other.hotSpotList);
	UDFList.Copy(other.UDFList);
	selDist = other.selDist;
	avgOrdQty = other.avgOrdQty;
	contQty = other.contQty;
	orderCount = other.orderCount;
	applyBrokenOrder = other.applyBrokenOrder;
	isChanged = other.isChanged;
	palletsPerPtwyTrip = other.palletsPerPtwyTrip;
	insertForkTravel = other.insertForkTravel;
	pickupForkTravel = other.pickupForkTravel;
	stockerDistFixed = other.stockerDistFixed;
	stockerDistVar = other.stockerDistVar;
	stockerLaborRate = other.stockerLaborRate;
	WMSSectionId = other.WMSSectionId;
}

qqhSLOTSection& qqhSLOTSection::operator=(const qqhSLOTSection& other)
{
	DBID = other.DBID;
	description = other.description;
	coord = other.coord;
	childList.Copy(other.childList);
	setType("SLOTSection");
//	pickPath = other.pickPath;
	locationMask = other.locationMask;
	avgCubePerTrip = other.avgCubePerTrip;
	forkDistFixed = other.forkDistFixed;
	forkDistVar = other.forkDistVar;
	forkLaborRate = other.forkLaborRate;
	replenAvgDist = other.replenAvgDist;
	selectDistFixed = other.selectDistFixed;
	selectDistVar = other.selectDistVar;
	selectLaborRate = other.selectLaborRate;
	hotSpotList.Copy(other.hotSpotList);
	UDFList.Copy(other.UDFList);
	selDist = other.selDist;
	avgOrdQty = other.avgOrdQty;
	contQty = other.contQty;
	orderCount = other.orderCount;
	applyBrokenOrder = other.applyBrokenOrder;
	isChanged = other.isChanged;
	palletsPerPtwyTrip = other.palletsPerPtwyTrip;
	insertForkTravel = other.insertForkTravel;
	pickupForkTravel = other.pickupForkTravel;
	stockerDistFixed = other.stockerDistFixed;
	stockerDistVar = other.stockerDistVar;
	stockerLaborRate = other.stockerLaborRate;
	WMSSectionId = other.WMSSectionId;

	return *this;
}

BOOL qqhSLOTSection::operator==(const qqhSLOTSection& other)
{
	if (description != other.description) return FALSE;
	if (locationMask != other.locationMask) return FALSE;
	if (avgCubePerTrip != other.avgCubePerTrip) return FALSE;
	if (forkDistFixed != other.forkDistFixed) return FALSE;
	if (forkDistVar != other.forkDistVar) return FALSE;
	if (forkLaborRate != other.forkLaborRate) return FALSE;
	if (replenAvgDist != other.replenAvgDist) return FALSE;
	if (selectDistFixed != other.selectDistFixed) return FALSE;
	if (selectDistVar != other.selectDistVar) return FALSE;
	if (selectLaborRate != other.selectLaborRate) return FALSE;
	if (selDist != other.selDist) return FALSE;
	if (avgOrdQty != other.avgOrdQty) return FALSE;
	if (contQty != other.contQty) return FALSE;
	if (orderCount != other.orderCount) return FALSE;
	if (applyBrokenOrder != other.applyBrokenOrder) return FALSE;
	if (palletsPerPtwyTrip != other.palletsPerPtwyTrip) return FALSE;
	if (insertForkTravel != other.insertForkTravel) return FALSE;
	if (pickupForkTravel != other.pickupForkTravel) return FALSE;
	if (stockerDistFixed != other.stockerDistFixed) return FALSE;
	if (stockerDistVar != other.stockerDistVar) return FALSE;
	if (stockerLaborRate != other.stockerLaborRate) return FALSE;
	if (WMSSectionId != other.WMSSectionId) return FALSE;

	if (UDFList.GetSize() != other.UDFList.GetSize()) return FALSE;
	for (int i=0; i < UDFList.GetSize(); ++i) {
		if (UDFList[i] != other.UDFList[i]) return FALSE;
	}

	return TRUE;

}


void qqhSLOTSection::BuildFromStream(CSsaStringArray &bufArray ) {
	char tempBuf[512];
	char tempREGName[100];
	char tempREGVal[256];
	char tempheader[100];
	int i;
	qqhSLOTHotSpot tempHotSpot;
	CSsaStringArray tempArray;
	CString tempChkStr;

	UDFList.RemoveAll();
	
	for ( i = 0; i < bufArray.GetSize(); i++ ) {
		if ( bufArray[i].Find(REGBegSearch) != -1 ) {
			/////////////////////////////////////////////////////////////
			//  REGs are processed here
			/////////////////////////////////////////////////////////////
			strcpy(tempBuf,bufArray[i].GetBuffer(0));
			bufArray[i].ReleaseBuffer();
			tempChkStr = tempBuf;
			if (tempChkStr.GetAt(tempChkStr.GetLength()-1) == '|')
				strcat(tempBuf,"1");
			if (strchr(tempBuf,'\n') == NULL)
				strcat(tempBuf,"\n");
			bufArray[i] = tempBuf;
			strcpy(tempheader,strtok(tempBuf,"|"));
			strcpy(tempREGName,strtok(NULL,"|"));
			if (strstr(bufArray[i].GetBuffer(0),"|\n") == NULL )
				strcpy(tempREGVal,strtok(NULL,"\n"));
			else
				strcpy(tempREGVal,"");
			bufArray[i].ReleaseBuffer();
			if ( strcmp(tempREGName,"Description") == 0 )
				this->description = tempREGVal;
			else if ( strcmp(tempREGName,"DBID") == 0 )
				this->DBID = atoi(tempREGVal);
			else if ( strcmp(tempREGName,"LocationIDMask") == 0 )
				this->locationMask=tempREGVal;
			else if ( strcmp(tempREGName,"AvgCubePerTrip") == 0 )
				this->avgCubePerTrip=(float)atof(tempREGVal);
			else if ( strcmp(tempREGName,"ForkDistFixed") == 0 )
				this->forkDistFixed=(float)atof(tempREGVal);
			else if ( strcmp(tempREGName,"ForkDistVar") == 0 )
				this->forkDistVar=(float)atof(tempREGVal);
			else if ( strcmp(tempREGName,"ForkLaborRate") == 0 )
				this->forkLaborRate=(float)atof(tempREGVal);
			else if ( strcmp(tempREGName,"ReplenAvgDist") == 0 )
				this->replenAvgDist=(float)atof(tempREGVal);
			else if ( strcmp(tempREGName,"SelectDistFixed") == 0 )
				this->selectDistFixed=(float)atof(tempREGVal);
			else if ( strcmp(tempREGName,"SelectDistVar") == 0 )
				this->selectDistVar=(float)atof(tempREGVal);
			else if ( strcmp(tempREGName,"SelectLaborRate") == 0 )
				this->selectLaborRate=(float)atof(tempREGVal);
			else if ( strcmp(tempREGName,"ApplyBrokenOrder") == 0 )
				this->applyBrokenOrder= tempREGVal;
			else if ( strcmp(tempREGName,"AverageOrderQuantity") == 0 )
				this->avgOrdQty = atoi(tempREGVal);
			else if ( strcmp(tempREGName,"ContainerQuantity") == 0 )
				this->contQty = atoi(tempREGVal);
			else if ( strcmp(tempREGName,"OrderCount") == 0 )
				this->orderCount = atoi(tempREGVal);
			else if ( strcmp(tempREGName,"TotalSelectionDistance") == 0 )
				this->selDist = (float)atof(tempREGVal);
			else if ( strcmp(tempREGName,"IsChanged") == 0 )
				this->isChanged = tempREGVal;

			else if ( strcmp(tempREGName,"PalletsPerPtwyTrip") == 0 )
				this->palletsPerPtwyTrip=(float)atof(tempREGVal);
			else if ( strcmp(tempREGName,"InsertForkTravel") == 0 )
				this->insertForkTravel=(float)atof(tempREGVal);
			else if ( strcmp(tempREGName,"PickupForkTravel") == 0 )
				this->pickupForkTravel=(float)atof(tempREGVal);
			else if ( strcmp(tempREGName,"StockerDistFixed") == 0 )
				this->stockerDistFixed=(float)atof(tempREGVal);
			else if ( strcmp(tempREGName,"StockerDistVar") == 0 )
				this->stockerDistVar=(float)atof(tempREGVal);
			else if ( strcmp(tempREGName,"StockerLaborRate") == 0 )
				this->stockerLaborRate=(float)atof(tempREGVal);
			else if ( strcmp(tempREGName, "WMSSectionId") == 0) 
				this->WMSSectionId = tempREGVal;
		}
	
		/////////////////////////////////////////////////////////////
		//  UDFs 
		/////////////////////////////////////////////////////////////
		if ( bufArray[i].Find(UDFBegSearch) != -1 ) {
			strcpy(tempBuf,bufArray[i].GetBuffer(0));
			bufArray[i].ReleaseBuffer();
			strcpy(tempheader,strtok(tempBuf,"|"));
			strcpy(tempheader,strtok(NULL,"\n"));
			this->UDFList.Add(tempheader);
		}
		/////////////////////////////////////////////////////////////
		//  Holder Coordinate
		/////////////////////////////////////////////////////////////
		if ( (bufArray[i]).Find(CoordBegSearch) != -1 ) {
			tempArray.RemoveAll();
			tempArray.Add(bufArray[i]);
			i++;
			while ( (bufArray[i]).Find(CoordEndSearch) == -1 ) {
				tempArray.Add(bufArray[i]);
				i++;
			}
			tempArray.Add(bufArray[i]);
			this->coord.BuildFromStream(tempArray);
			tempArray.RemoveAll();
		}

		/////////////////////////////////////////////////////////////
		//  Hot Spot
		/////////////////////////////////////////////////////////////
		if ( (bufArray[i].Find(HotSpotBegSearch) != -1 )) {
			tempArray.RemoveAll();
			tempArray.Add(bufArray[i]);
			i++;
			while ( (bufArray[i].Find(HotSpotEndSearch) == -1) ) {
				tempArray.Add(bufArray[i]);
				i++;
			}
			tempArray.Add(bufArray[i]);
			tempHotSpot.BuildFromStream(tempArray);
			this->hotSpotList.Add(tempHotSpot);
			tempArray.RemoveAll();
		}
	}
	return;
}

void qqhSLOTSection::StreamAttributes(CSsaStringArray & attributBuf) {

	int i;

	CString tempBuf;

	attributBuf.Add(CString(SectionBegSearch) + "\n");

	tempBuf.Format("%sDBID|%d\n", REGBegSearch, this->DBID );
	attributBuf.Add(tempBuf);

	tempBuf.Format("%sDescription|%s\n", REGBegSearch, this->description);
	attributBuf.Add(tempBuf);

	tempBuf.Format("%sType|%s\n", REGBegSearch, this->type);
	attributBuf.Add(tempBuf);

	tempBuf.Format("%sLocationIDMask|%s\n", REGBegSearch, this->locationMask);
	attributBuf.Add(tempBuf);

	tempBuf.Format("%sAvgCubePerTrip|%f\n", REGBegSearch, this->avgCubePerTrip);
	attributBuf.Add(tempBuf);
	
	tempBuf.Format("%sForkDistFixed|%f\n", REGBegSearch, this->forkDistFixed);
	attributBuf.Add(tempBuf);

	tempBuf.Format("%sForkDistVar|%f\n", REGBegSearch, this->forkDistVar);
	attributBuf.Add(tempBuf);
	
	tempBuf.Format("%sForkLaborRate|%f\n", REGBegSearch, this->forkLaborRate);
	attributBuf.Add(tempBuf);
	
	tempBuf.Format("%sReplenAvgDist|%f\n", REGBegSearch, this->replenAvgDist);
	attributBuf.Add(tempBuf);
	
	tempBuf.Format("%sSelectDistFixed|%f\n", REGBegSearch, this->selectDistFixed);
	attributBuf.Add(tempBuf);
	
	tempBuf.Format("%sSelectDistVar|%f\n", REGBegSearch, this->selectDistVar);
	attributBuf.Add(tempBuf);
	
	tempBuf.Format("%sSelectLaborRate|%f\n", REGBegSearch, this->selectLaborRate);
	attributBuf.Add(tempBuf);

	tempBuf.Format("%sApplyBrokenOrder|%s\n", REGBegSearch, this->applyBrokenOrder);
	attributBuf.Add(tempBuf);

	tempBuf.Format("%sAverageOrderQuantity|%d\n", REGBegSearch, this->avgOrdQty);
	attributBuf.Add(tempBuf);

	tempBuf.Format("%sContainerQuantity|%d\n", REGBegSearch, this->contQty);
	attributBuf.Add(tempBuf);

	tempBuf.Format("%sTotalSelectionDistance|%f\n", REGBegSearch, this->selDist);
	attributBuf.Add(tempBuf);
			
	tempBuf.Format("%sOrderCount|%d\n", REGBegSearch, this->orderCount);
	attributBuf.Add(tempBuf);

	tempBuf.Format("%sIsChanged|%s\n", REGBegSearch, this->isChanged);
	attributBuf.Add(tempBuf);

	tempBuf.Format("%sPalletsPerPtwyTrip|%f\n", REGBegSearch, this->palletsPerPtwyTrip);
	attributBuf.Add(tempBuf);
	
	tempBuf.Format("%sInsertForkTravel|%f\n", REGBegSearch, this->insertForkTravel);
	attributBuf.Add(tempBuf);
	
	tempBuf.Format("%sPickupForkTravel|%f\n", REGBegSearch, this->pickupForkTravel);
	attributBuf.Add(tempBuf);
	
	tempBuf.Format("%sStockerDistFixed|%f\n", REGBegSearch, this->stockerDistFixed);
	attributBuf.Add(tempBuf);
	
	tempBuf.Format("%sStockerDistVar|%f\n", REGBegSearch, this->stockerDistVar);
	attributBuf.Add(tempBuf);
	
	tempBuf.Format("%sStockerLaborRate|%f\n", REGBegSearch, this->stockerLaborRate);
	attributBuf.Add(tempBuf);

	tempBuf.Format("%sWMSSectionId|%s\n", REGBegSearch, this->WMSSectionId);
	attributBuf.Add(tempBuf);

	for (i = 0; i < UDFList.GetSize(); i++)
		attributBuf.Add(CString(CString(UDFBegSearch) + UDFList[i] + "\n"));

	this->coord.StreamAttributes(attributBuf);

//	this->pickPath.StreamAttributes(attributBuf);

	for ( i = 0; i < this->hotSpotList.GetSize(); i++)
		hotSpotList[i].StreamAttributes(attributBuf);

	attributBuf.Add(CString(SectionEndSearch) + "\n");

	return;
}

////////////////////////////////////////
// qqhSLOTFacility Member functions
////////////////////////////////////////

qqhSLOTFacility::qqhSLOTFacility()
{
	DBID = 0;
	region = units = slotType = 1;
	strCadFileName = "";
	IsModified = 0;
	setType(CString("SLOTFacility"));
	cost = 0.0;
	units = 0;
	duration = timeHorizonUnit = originalFacilityId = 0;
	clientNameOpened = "";
	notes = " ";
	isChanged = "TRUE";
	baselineCost = 0;
}

qqhSLOTFacility::qqhSLOTFacility(const qqhSLOTFacility& other)
{
	DBID = other.DBID;
	region = other.region;
	units = other.units;
	duration = other.duration;
	slotType = other.slotType;
	description = other.description;
	coord = other.coord;
	childList.Copy(other.childList);
	strCadFileName = other.strCadFileName;
	setType(CString("SLOTFacility"));
	IsModified = other.IsModified;
	cost = other.cost;
	timeHorizonUnit = other.timeHorizonUnit;
	region = other.region;
	slotType = other.slotType;
	originalFacilityId = other.originalFacilityId;
	clientNameOpened = other.clientNameOpened;
	UDFList.Copy(other.UDFList);
	isChanged = other.isChanged;
	notes = other.notes;
	baselineCost = other.baselineCost;
}

qqhSLOTFacility::qqhSLOTFacility(const char *strName)
{
	DBID = 0;
	strCadFileName = strName;
	setType(CString("SLOTFacility"));
	IsModified = 0;
	region = units = slotType = 1;
	cost = 0.0;
	units = 0;
	duration = timeHorizonUnit = originalFacilityId = 0;
	clientNameOpened = "";
	isChanged = "TRUE";
	notes = " ";
	baselineCost = 0;
}


qqhSLOTFacility& qqhSLOTFacility::operator=(const qqhSLOTFacility& other)
{
	DBID = other.DBID;
	IsModified = other.IsModified;
	description = other.description;
	coord = other.coord;
	childList.Copy(other.childList);
	strCadFileName = other.strCadFileName;
	setType(CString("SLOTFacility"));
	cost = other.cost;
	duration = other.duration;
	timeHorizonUnit = other.timeHorizonUnit;
	region = other.region;
	units = other.units;
	slotType = other.slotType;
	originalFacilityId = other.originalFacilityId;
	clientNameOpened = other.clientNameOpened;
	UDFList.Copy(other.UDFList);
	notes = other.notes;
	baselineCost = other.baselineCost;

	return *this;
}

BOOL qqhSLOTFacility::operator==(const qqhSLOTFacility& other)
{
	// for now just mess with the things they can modify manually
	if (description != other.description) return FALSE;
	if (cost != other.cost) return FALSE;
	if (duration != other.duration) return FALSE;
	if (timeHorizonUnit != other.timeHorizonUnit) return FALSE;
	if (region != other.region) return FALSE;
	if (units != other.units) return FALSE;
	if (slotType != other.slotType) return FALSE;
	if (UDFList.GetSize() != other.UDFList.GetSize()) return FALSE;

	if (notes != other.notes) return FALSE;
	if (baselineCost != other.baselineCost) return FALSE;

	for (int i=0; i < UDFList.GetSize(); ++i) {
		if (UDFList[i] != other.UDFList[i]) return FALSE;
	}

	return TRUE;

}


void qqhSLOTFacility::BuildFromStream(CSsaStringArray &bufArray ) {
	char tempBuf[1024];
	char tempREGName[100];
	char tempREGVal[1024];
	char tempheader[100];
	int i;
	CSsaStringArray tempArray;
	qqhSLOTPickPath tempPath;
	CString tempChkStr;

	UDFList.RemoveAll();

	for ( i = 0; i < bufArray.GetSize(); i++ ) {
		/////////////////////////////////////////////////////////////
		//  REGs are processed here
		/////////////////////////////////////////////////////////////
		if ( bufArray[i].Find(REGBegSearch) != -1 ) {
		
			strcpy(tempBuf,bufArray[i].GetBuffer(0));
			bufArray[i].ReleaseBuffer();
			tempChkStr = tempBuf;
			if (tempChkStr.GetAt(tempChkStr.GetLength()-1) == '|')
				strcat(tempBuf,"1");
			if (strchr(tempBuf,'\n') == NULL)
				strcat(tempBuf,"\n");
			bufArray[i] = tempBuf;
			strcpy(tempheader,strtok(tempBuf,"|"));
			strcpy(tempREGName,strtok(NULL,"|"));
			if (strstr(bufArray[i].GetBuffer(0),"|\n") == NULL )
				strcpy(tempREGVal,strtok(NULL,"\n"));
			else
				strcpy(tempREGVal,"");
			bufArray[i].ReleaseBuffer();
			if ( strcmp(tempREGName,"Description") == 0 )
				this->description = tempREGVal;
			else if ( strcmp(tempREGName,"CadFileName") == 0 )
				this->strCadFileName = tempREGVal;
			else if ( strcmp(tempREGName,"DBID") == 0 ) 
				this->DBID = atoi(tempREGVal);
			else if ( strcmp(tempREGName,"TimeHorizonValue") == 0 ) 
				this->duration = atoi(tempREGVal);
			else if ( strcmp(tempREGName,"TimeHorizonUnit") == 0 ) 
				this->timeHorizonUnit = atoi(tempREGVal);
			else if ( strcmp(tempREGName,"Units") == 0 ) 
				this->units = atoi(tempREGVal);
			else if ( strcmp(tempREGName,"Cost") == 0 ) 
				this->cost = (double)atof(tempREGVal);
			else if ( strcmp(tempREGName,"BaselineCost") == 0 ) 
				this->baselineCost = (double)atof(tempREGVal);
			else if ( strcmp(tempREGName,"OriginalFacilityID") == 0 ) 
				this->originalFacilityId = atoi(tempREGVal);
			else if ( strcmp(tempREGName,"Region") == 0 ) 
				this->region = atoi(tempREGVal);
			else if ( strcmp(tempREGName,"SlotType") == 0 ) 
				this->slotType = atoi(tempREGVal);
			else if ( strcmp(tempREGName,"ClientNameOpened") == 0 ) 
				this->clientNameOpened = tempREGVal;
			else if ( strcmp(tempREGName,"IsChanged") == 0 )
				this->isChanged = tempREGVal;
			else if ( strcmp(tempREGName,"Notes") == 0 )
				this->notes = tempREGVal;

		}
	
		/////////////////////////////////////////////////////////////
		//  UDFs 
		/////////////////////////////////////////////////////////////
		if ( bufArray[i].Find(UDFBegSearch) != -1 ) {
			strcpy(tempBuf,bufArray[i].GetBuffer(0));
			bufArray[i].ReleaseBuffer();
			strcpy(tempheader,strtok(tempBuf,"|"));
			strcpy(tempheader,strtok(NULL,"\n"));
			this->UDFList.Add(tempheader);
		}
		/////////////////////////////////////////////////////////////
		//  Holder Coordinate
		/////////////////////////////////////////////////////////////
		if ( (bufArray[i]).Find(CoordBegSearch) != -1 ) {
			tempArray.RemoveAll();
			tempArray.Add(bufArray[i]);
			i++;
			while ( (bufArray[i]).Find(CoordEndSearch) == -1 ) {
				tempArray.Add(bufArray[i]);
				i++;
			}
			tempArray.Add(bufArray[i]);
			this->coord.BuildFromStream(tempArray);
			tempArray.RemoveAll();
		}

		/////////////////////////////////////////////////////////////
		//  Pick Path List
		/////////////////////////////////////////////////////////////
		if ( (bufArray[i]).Find(PathBegSearch) != -1 ) {
			tempArray.RemoveAll();
			tempArray.Add(bufArray[i]);
			i++;
			while ( (bufArray[i]).Find(PathEndSearch) == -1 ) {
				tempArray.Add(bufArray[i]);
				i++;
			}
			tempArray.Add(bufArray[i]);
			tempPath.BuildFromStream(tempArray);
			this->pickPathList.Add(tempPath);
//			i++;
			tempArray.RemoveAll();
		}

		/////////////////////////////////////////////////////////////
		//  Section Objects
		/////////////////////////////////////////////////////////////
//		if ( (bufArray[i]).Find(SectionBegSearch) != -1 ) {
//			tempArray.RemoveAll();
//			tempArray.Add(bufArray[i]);
//			i++;
//			while ( (bufArray[i]).Find(SectionEndSearch) == -1 ) {
//				tempArray.Add(bufArray[i]);
//				i++;
//			}
//			tempArray.Add(bufArray[i]);
//			i++;
//			this->GetChildList().Add(BuildSectionFromStream(tempArray));
//		}
	}
	return;
}

void qqhSLOTFacility::StreamAttributes(CSsaStringArray & attributBuf) {

	int i;
	CString tempBuf;

	tempBuf.Format("%s\n", FacilityBegSearch);
	attributBuf.Add(tempBuf);

	tempBuf.Format("%sDBID|%d\n", REGBegSearch, this->DBID );
	attributBuf.Add(tempBuf);

	tempBuf.Format("%sDescription|%s\n", REGBegSearch, this->description );
	attributBuf.Add(tempBuf);

	tempBuf.Format("%sType|%s\n", REGBegSearch, this->type);
	attributBuf.Add(tempBuf);

	tempBuf.Format("%sCadFileName|%s\n", REGBegSearch, this->strCadFileName);
	attributBuf.Add(tempBuf);

	tempBuf.Format("%sSlotType|%d\n", REGBegSearch, this->slotType);
	attributBuf.Add(tempBuf);

	tempBuf.Format("%sUnits|%d\n", REGBegSearch, this->units);
	attributBuf.Add(tempBuf);

	tempBuf.Format("%sRegion|%d\n", REGBegSearch, this->region);
	attributBuf.Add(tempBuf);

	tempBuf.Format("%sIsModified|%d\n", REGBegSearch, this->IsModified);
	attributBuf.Add(tempBuf);

	tempBuf.Format("%sTimeHorizonValue|%d\n", REGBegSearch, this->duration);
	attributBuf.Add(tempBuf);
	
	tempBuf.Format("%sTimeHorizonUnit|%d\n", REGBegSearch, this->timeHorizonUnit);
	attributBuf.Add(tempBuf);

	tempBuf.Format("%sCost|%f\n", REGBegSearch, this->cost);
	attributBuf.Add(tempBuf);

	tempBuf.Format("%sBaselineCost|%f\n", REGBegSearch, this->baselineCost);
	attributBuf.Add(tempBuf);

	tempBuf.Format("%sClientNameOpened|%s\n", REGBegSearch, this->clientNameOpened);
	attributBuf.Add(tempBuf);

	tempBuf.Format("%sIsChanged|%s\n", REGBegSearch, this->isChanged);
	attributBuf.Add(tempBuf);

	tempBuf.Format("%sNotes|%s\n", REGBegSearch, this->notes );
	attributBuf.Add(tempBuf);

	for (i = 0; i < UDFList.GetSize(); i++)
		attributBuf.Add(CString(CString(UDFBegSearch) + UDFList[i] + "\n"));

	this->coord.StreamAttributes(attributBuf);
	
	for ( i = 0; i < pickPathList.GetSize(); i++ )
		this->pickPathList[i].StreamAttributes(attributBuf);

//	for ( i = 0; i < this->childList.GetSize(); i++ ) {
//		StreamSectionAttributes(this->childList[i], attributBuf);
//	}

	attributBuf.Add(CString(FacilityEndSearch) + "\n");

	return;
}


BOOL qqhSLOTLevel::isLevelOverridden(const CLevelProfile& other)
{
	if (this->facingGap != other.m_FacingGap) return TRUE;
	if (this->facingSnap != other.m_FacingSnap) return TRUE;
	if (this->forkFixedInsertion != other.m_ForkFixedInsertion) return TRUE;
	if (this->isRotateAllowed != other.m_IsRotateAllowed) return TRUE;
	if (this->isVariableLocationsAllowed != other.m_IsVariableWidthAllowed) return TRUE;
	if (this->minLocWidth != other.m_MinimumLocWidth) return TRUE;
	if (this->productGap != other.m_ProductGap) return TRUE;
	if (this->productSnap != other.m_ProductSnap) return TRUE;

	return FALSE;
}

BOOL qqhSLOTLocation::isLocationOverridden(const CLocationProfile& other)
{
	if (this->handlingMethod != other.m_HandlingMethod) return TRUE;
	if (this->IsSelect != other.m_IsSelect) return TRUE;
	if (this->width != other.m_Width) return TRUE;
	if (this->depth != other.m_Depth) return TRUE;
	if (this->height != other.m_Height) return TRUE;
	if (this->maxWeight != other.m_WeightCapacity) return TRUE;

	return FALSE;

}




void qqhSLOTFacility::ConvertToFacility(CFacility &facility)
{
	facility.m_DBId = this->getDBID();
	facility.m_Coordinates.m_X = this->getCoord().getX();
	facility.m_Coordinates.m_Y = this->getCoord().getY();
	facility.m_Coordinates.m_Z = this->getCoord().getZ();
	facility.m_Cost = this->getCost();
	facility.m_Description = this->getDescription();
	facility.m_DrawingName = this->getStrCadFileName();
	facility.m_Notes = this->getNotes();
	facility.m_TimeHorizonDuration = this->getDuration();
	facility.m_TimeHorizonUnits = this->getTimeHorizonUnit();
	facility.m_UnitOfMeasurement = this->getUnits();
	facility.m_ClientNameOpened = this->getClientNameOpened();
	facility.m_BaselineCost = this->getBaselineCost();
}

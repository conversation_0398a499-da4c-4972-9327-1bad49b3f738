// InterfaceCommands.cpp: implementation of the CInterfaceCommands class.
//
//////////////////////////////////////////////////////////////////////

#include "stdafx.h"
#include "modal.h"
#include "InterfaceCommands.h"
#include "InterfaceHelper.h"
#include "ControlService.h"

#include "base.h"
#include "MiscClasses.h"
#include "SLOTSessionMgr.h"
#include "SrvcObjs.h"


#ifdef _DEBUG
#undef THIS_FILE
static char THIS_FILE[]=__FILE__;
#define new DEBUG_NEW
#endif

extern CControlService controlService;
//////////////////////////////////////////////////////////////////////
// Construction/Destruction
//////////////////////////////////////////////////////////////////////

CInterfaceCommands::CInterfaceCommands()
{

}

CInterfaceCommands::~CInterfaceCommands()
{

}

void CInterfaceCommands::RegisterCommands()
{
	// Interfaces
	acedRegCmds->addCommand( "SLOTFAC", "PRODINBOUND", "PRODINBOUND",
		ACRX_CMD_MODAL, &CInterfaceCommands::ProductInbound );
	acedRegCmds->addCommand( "SLOTFAC", "LOCOUTBOUND", "LOCOUTBOUND",
		ACRX_CMD_MODAL, &CInterfaceCommands::LocationOutbound );
	acedRegCmds->addCommand( "SLOTFAC", "LOCATIONOUTBOUND", "LOCATIONOUTBOUND",
		ACRX_CMD_MODAL, &CInterfaceCommands::LocationOutbound );
	acedRegCmds->addCommand( "SLOTFAC", "ASSIGNMENTOUTBOUND", "ASSIGNMENTOUTBOUND",
		ACRX_CMD_MODAL, &CInterfaceCommands::AssignmentOutbound );
	acedRegCmds->addCommand( "SLOTFAC", "SOLUTIONOUTBOUND", "SOLUTIONOUTBOUND",
		ACRX_CMD_MODAL, &CInterfaceCommands::AssignmentOutbound );

	acedRegCmds->addCommand( "SLOTJAVA", "GENERATEMOVES", "GENERATEMOVES",
		ACRX_CMD_MODAL, &CInterfaceCommands::GenerateMoves );	
	acedRegCmds->addCommand( "SLOTJAVA", "SEARCHANCHORMAINTENANCE", "SEARCHANCHORMAINTENANCE",
		ACRX_CMD_MODAL, &CInterfaceCommands::SearchAnchorMaintenance);
	acedRegCmds->addCommand( "SLOTJAVA", "SAM", "SAM",
		ACRX_CMD_MODAL, &CInterfaceCommands::SearchAnchorMaintenance);

	acedRegCmds->addCommand( "SLOTFAC", "WMSSetup", "WMSSetup",
		ACRX_CMD_MODAL, &CInterfaceCommands::WMSSetup);
	acedRegCmds->addCommand( "SLOTFAC", "WMSGroup", "WMSGroup",
		ACRX_CMD_MODAL, &CInterfaceCommands::WMSGroup);

	acedRegCmds->addCommand( "SLOTFAC", "WMSSync", "WMSSync",
		ACRX_CMD_MODAL, &CInterfaceCommands::WMSSync);

	acedRegCmds->addCommand( "SLOTFAC", "ConvertProductFileToXML", "ConvertProductFileToXML",
		ACRX_CMD_MODAL, &CInterfaceCommands::ConvertProductFileToXML);
}


void CInterfaceCommands::ProductInbound()
{
	if (! controlService.ValidateCommand(CCommands::openFacilityMode))
		return;

	CInterfaceHelper helper;

	helper.ProductInbound();

	///getSessionMgrSO()->ProductInboundHelper();

	return;

}

void CInterfaceCommands::LocationOutbound()
{
	if (! controlService.ValidateCommand(CCommands::savedFacilityMode))
		return;

	CInterfaceHelper helper;

	helper.LocationOutbound();

	return;
}

void CInterfaceCommands::AssignmentOutbound()
{
	if (! controlService.ValidateCommand(CCommands::openFacilityMode))
		return;

	CInterfaceHelper helper;

	helper.AssignmentOutbound();

	return;
}

void CInterfaceCommands::GenerateMoves()
{
	if (! controlService.ValidateCommand(CCommands::savedFacilityMode))
		return;

	CInterfaceHelper helper;

	helper.GenerateMoves();

	return;
}

void CInterfaceCommands::SearchAnchorMaintenance()
{
	if (! controlService.ValidateCommand(CCommands::openFacilityMode))
		return;

	CInterfaceHelper helper;

	helper.SearchAnchorMaintenance();

	return;

}


void CInterfaceCommands::NewProductLayout()
{
	if (! controlService.ValidateCommand(CCommands::savedFacilityMode))
		return;

	CInterfaceHelper helper;

	helper.RunNewProductLayout();
	
	return;
}

void CInterfaceCommands::WMSSetup()
{
	if (! controlService.ValidateCommand(CCommands::anyMode))
		return;

	CInterfaceHelper helper;

	helper.WMSSetup();

	return;
}


void CInterfaceCommands::WMSGroup()
{
	if (! controlService.ValidateCommand(CCommands::anyMode))
		return;

	CInterfaceHelper helper;

	helper.WMSGroup();

	return;
}

void CInterfaceCommands::WMSSync()
{
	if (! controlService.ValidateCommand(CCommands::anyMode))
		return;

	CInterfaceHelper helper;

	helper.WMSSync();

	return;
}

void CInterfaceCommands::ConvertProductFileToXML()
{
	if (! controlService.ValidateCommand(CCommands::anyMode))
		return;

	CInterfaceHelper helper;

	helper.ConvertProductFileToXML();

}

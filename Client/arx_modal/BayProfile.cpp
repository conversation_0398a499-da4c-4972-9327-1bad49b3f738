// BayProfile.cpp: implementation of the CBayProfile class.
//
//////////////////////////////////////////////////////////////////////

#include "stdafx.h"
#include <gemat3d.h>
#include <dbsol3d.h>
#include <dbsymtb.h>

#include "modal.h"
#include "BayProfile.h"
#include "UtilityHelper.h"
#include "Constants.h"
#include "ControlService.h"
#include <errno.h>

#ifdef _DEBUG
#undef THIS_FILE
static char THIS_FILE[]=__FILE__;
#define new DEBUG_NEW
#endif

extern CControlService controlService;
extern CUtilityHelper utilityHelper;
//////////////////////////////////////////////////////////////////////
// Construction/Destruction
//////////////////////////////////////////////////////////////////////

CBayProfile::CBayProfile()
{
	m_BayProfileDBId = 0;
	m_Active = 0;
	m_AllowBaySpanning = 1;
	m_Coordinates.m_X = m_Coordinates.m_Y = m_Coordinates.m_Z = 0;
	m_Depth = m_Width = m_Height = 0;
	m_ExcludeFromOptimization = 0;
	m_FlowDifference = 0;
	m_PalletDepth = 1;
	m_PalletHeight = m_PalletSpace = 0;
	m_RackCost = m_UprightHeight = m_UprightWidth = 0;
	m_WeightCapacity = 0;
	m_IsHazard = m_IsFloating = 0;
}

CBayProfile::~CBayProfile()
{
	for (int i=0; i < m_LevelProfileList.GetSize(); ++i)
		delete m_LevelProfileList[i];

	for (i=0; i < m_BayRuleList.GetSize(); ++i)
		delete m_BayRuleList[i];
}

CBayProfile::CBayProfile(const CBayProfile& other)
{
	m_BayProfileDBId = other.m_BayProfileDBId;
	m_Description = other.m_Description;
	m_BayType = other.m_BayType;
	m_Coordinates = other.m_Coordinates;
	m_Width = other.m_Width;
	m_Depth = other.m_Depth;
	m_Height = other.m_Height;	
	m_BayType = other.m_BayType;
	m_PalletHeight = other.m_PalletHeight;
	m_PalletHeight = other.m_PalletHeight;
	m_PalletDepth = other.m_PalletDepth;
	m_FlowDifference = other.m_FlowDifference;
	m_WeightCapacity = other.m_WeightCapacity;
	m_RackCost = other.m_RackCost;
	m_UprightWidth = other.m_UprightWidth;
	m_IsHazard = other.m_IsHazard;
	m_AllowBaySpanning = other.m_AllowBaySpanning;
	m_ExcludeFromOptimization = other.m_ExcludeFromOptimization;
	m_IsFloating = other.m_IsFloating;
	m_UprightHeight = other.m_UprightHeight;
	m_PalletSpace = other.m_PalletSpace;
	m_Active = other.m_Active;

	for (int i=0; i < m_LevelProfileList.GetSize(); ++i)
		delete m_LevelProfileList[i];
	m_LevelProfileList.RemoveAll();

	for (i=0; i < other.m_LevelProfileList.GetSize(); ++i)
		m_LevelProfileList.Add(new CLevelProfile(*other.m_LevelProfileList[i]));

	for (i=0; i < m_BayRuleList.GetSize(); ++i)
		delete m_BayRuleList[i];
	m_BayRuleList.RemoveAll();

	for (i=0; i < other.m_BayRuleList.GetSize(); ++i)
		m_BayRuleList.Add(new CBayRule(*other.m_BayRuleList[i]));
}

CBayProfile& CBayProfile::operator=(const CBayProfile &other)
{
	m_BayProfileDBId = other.m_BayProfileDBId;
	m_Description = other.m_Description;
	m_BayType = other.m_BayType;
	m_Coordinates = other.m_Coordinates;
	m_Width = other.m_Width;
	m_Depth = other.m_Depth;
	m_Height = other.m_Height;	
	m_BayType = other.m_BayType;
	m_PalletHeight = other.m_PalletHeight;
	m_PalletHeight = other.m_PalletHeight;
	m_PalletDepth = other.m_PalletDepth;
	m_FlowDifference = other.m_FlowDifference;
	m_WeightCapacity = other.m_WeightCapacity;
	m_RackCost = other.m_RackCost;
	m_UprightWidth = other.m_UprightWidth;
	m_IsHazard = other.m_IsHazard;
	m_AllowBaySpanning = other.m_AllowBaySpanning;
	m_ExcludeFromOptimization = other.m_ExcludeFromOptimization;
	m_IsFloating = other.m_IsFloating;
	m_UprightHeight = other.m_UprightHeight;
	m_PalletSpace = other.m_PalletSpace;
	m_Active = other.m_Active;

	for (int i=0; i < m_LevelProfileList.GetSize(); ++i)
		delete m_LevelProfileList[i];
	m_LevelProfileList.RemoveAll();

	for (i=0; i < other.m_LevelProfileList.GetSize(); ++i)
		m_LevelProfileList.Add(new CLevelProfile(*other.m_LevelProfileList[i]));

	for (i=0; i < m_BayRuleList.GetSize(); ++i)
		delete m_BayRuleList[i];
	m_BayRuleList.RemoveAll();

	for (i=0; i < other.m_BayRuleList.GetSize(); ++i)
		m_BayRuleList.Add(new CBayRule(*other.m_BayRuleList[i]));

	return *this;
}

BOOL CBayProfile::operator==(const CBayProfile& other)
{
	if (m_BayProfileDBId != other.m_BayProfileDBId) return FALSE;
	if (m_Description != other.m_Description) return FALSE;
	if (m_BayType != other.m_BayType) return FALSE;
	if (m_Coordinates != other.m_Coordinates) return FALSE;
	if (m_Width != other.m_Width) return FALSE;
	if (m_Depth != other.m_Depth) return FALSE;
	if (m_Height != other.m_Height) return FALSE;	
	if (m_BayType != other.m_BayType) return FALSE;
	if (m_PalletHeight != other.m_PalletHeight) return FALSE;
	if (m_PalletHeight != other.m_PalletHeight) return FALSE;
	if (m_PalletDepth != other.m_PalletDepth) return FALSE;
	if (m_FlowDifference != other.m_FlowDifference) return FALSE;
	if (m_WeightCapacity != other.m_WeightCapacity) return FALSE;
	if (m_RackCost != other.m_RackCost) return FALSE;
	if (m_UprightWidth != other.m_UprightWidth) return FALSE;
	if (m_IsHazard != other.m_IsHazard) return FALSE;
	if (m_AllowBaySpanning != other.m_AllowBaySpanning) return FALSE;
	if (m_ExcludeFromOptimization != other.m_ExcludeFromOptimization) return FALSE;
	if (m_IsFloating != other.m_IsFloating) return FALSE;
	if (m_UprightHeight != other.m_UprightHeight) return FALSE;
	if (m_PalletSpace != other.m_PalletSpace) return FALSE;

	if (m_LevelProfileList.GetSize() != other.m_LevelProfileList.GetSize()) return FALSE;

	for (int i=0; i < m_LevelProfileList.GetSize(); ++i) {
		if (*m_LevelProfileList[i] != *other.m_LevelProfileList[i])
			return FALSE;
	}

	if (m_BayRuleList.GetSize() != other.m_BayRuleList.GetSize()) return FALSE;

	for (i=0; i < m_BayRuleList.GetSize(); ++i) {
		if (*m_BayRuleList[i] != *other.m_BayRuleList[i])
			return FALSE;
	}

	return TRUE;
	
}

int CBayProfile::Parse(CString &line)
{
	CStringArray strings;

	utilityHelper.ParseString(line, "|", strings);

	for (int i=0; i < strings.GetSize(); ++i) {
		switch (i) {
		case 0:
			m_BayProfileDBId = atol(strings[i]);
			break;
		case 1:
			m_Description = strings[i];
			break;
		case 2:
			m_Coordinates.m_X = atof(strings[i]);
			break;
		case 3:
			m_Coordinates.m_Y = atof(strings[i]);
			break;
		case 4:
			m_Coordinates.m_Z = atof(strings[i]);
			break;
		case 5:
			m_Width = atof(strings[i]);
			break;
		case 6:
			m_Depth = atof(strings[i]);
			break;
		case 7:
			m_Height = atof(strings[i]);
			break;
		case 8:
			m_BayType = atoi(strings[i]);
			break;
		case 9:
			m_PalletHeight = atoi(strings[i]);
			break;
		case 10:
			m_PalletDepth = atoi(strings[i]);
			break;
		case 11:
			m_FlowDifference = atoi(strings[i]);
			break;
		case 12:
			m_WeightCapacity = atof(strings[i]);
			break;
		case 13:
			m_RackCost = atof(strings[i]);
			break;
		case 14:
			m_UprightWidth = atoi(strings[i]);
			break;
		case 15:
			m_IsHazard = atoi(strings[i]);
			break;
		case 16:
			m_AllowBaySpanning = atoi(strings[i]);
			break;
		case 17:
			m_ExcludeFromOptimization = atoi(strings[i]);
			break;
		case 18:
			m_IsFloating = atoi(strings[i]);
			break;
		case 19:
			m_UprightHeight = atof(strings[i]);
			break;
		case 20:
			m_PalletSpace = atof(strings[i]);
			break;
		}
	}

	int idx = m_Description.Find("\\");
	if (idx >= 0)
		m_Description = m_Description.Mid(idx+1);
	
	return 0;
}


void CBayProfile::ConvertBayType(int bayType, CString &description)
{
	switch (bayType) {
	case 1:
		description = "Bin";
		break;
	case 2:
		description = "Drive-In";
		break;
	case 3:
		description = "Floor";
		break;
	case 4:
		description = "CaseFlow";
		break;
	case 5:
		description = "Pallet";
		break;
	case 6:
		description = "PIR";
		break;
	case 7:
		description = "Carousel";
		break;
	case 8:
		description = "PalletFlow";
		break;
	default:
		description = "Invalid Type";
		break;
	}

	return;

}

/*static */CString CBayProfile::ConvertBayType(int bayType)
{
	CString str;

	ConvertBayType(bayType, str);

	return str;

}


CString CBayProfile::ConvertBayType()
{
	return ConvertBayType(m_BayType);
}

/* static */int CBayProfile::ConvertBayType(CString &description)
{
	
	if (description.CompareNoCase("Bin") == 0)
		return 1;
	else if (description.CompareNoCase("Drive-In") == 0 || 
		description.CompareNoCase("Drive In") == 0 ||
		description.CompareNoCase("Drivein") == 0)
		return 2;
	else if (description.CompareNoCase("Floor") == 0)
		return 3;
	else if (description.CompareNoCase("CaseFlow") == 0 ||
		description.CompareNoCase("Case Flow") == 0 ||
		description.CompareNoCase("Case-Flow") == 0)
		return 4;
	else if (description.CompareNoCase("Pallet") == 0)
		return 5;
	else if (description.CompareNoCase("PIR") == 0)
		return 6;
	else if (description.CompareNoCase("Carousel") == 0)
		return 7;
	else if (description.CompareNoCase("PalletFlow") == 0 ||
		description.CompareNoCase("Pallet-Flow") == 0 ||
		description.CompareNoCase("Pallet Flow") == 0)
		return 8;
	
	return -1;

}

CString CBayProfile::ConvertBayTypeToPath(int bayType)
{
	CString description;

	switch (bayType) {
	case 1:
		description = "bin";
		break;
	case 2:
		description = "drivein";
		break;
	case 3:
		description = "floor";
		break;
	case 4:
		description = "caseflow";
		break;
	case 5:
		description = "pallet";
		break;
	case 6:
		description = "PIR";
		break;
	case 7:
		description = "carousel";
		break;
	case 8:
		description = "palletflow";
		break;
	default:
		description = "";
		break;
	}

	return description;
}

CString CBayProfile::ConvertBayTypeToPath()
{
	return ConvertBayTypeToPath(m_BayType);
}


// This stupidness is because for some reason it was decided that the information
// stored with a crossbar would refer to the attributes of the space below the
// crossbar instead of the more intuitive way which would be for it to refer to
// the crossbar itself (e.g. weight capacity)
// So this will be used to convert the levels from the format stored in the database
// to the intuitive format and back again
void CBayProfile::ResetLevels(BOOL bIn)
{
	if (m_LevelProfileList.GetSize() == 0)
		return;

	if (bIn) {
		
		for (int i=m_LevelProfileList.GetSize()-1; i > 0; i--) {
			m_LevelProfileList[i]->m_Coordinates.m_Z = m_LevelProfileList[i-1]->m_Coordinates.m_Z;
			m_LevelProfileList[i]->m_Thickness = m_LevelProfileList[i-1]->m_Thickness;
			m_LevelProfileList[i]->m_IsBarHidden = m_LevelProfileList[i-1]->m_IsBarHidden;
		}
		
		m_LevelProfileList[0]->m_Coordinates.m_Z = 0;
		m_LevelProfileList[0]->m_Thickness = 0;
		m_LevelProfileList[0]->m_IsBarHidden = 0;

		if (m_LevelProfileList[0]->m_WeightCapacity == 0) {
			m_LevelProfileList[0]->m_WeightCapacity = m_WeightCapacity;
			for (i=1; i < m_LevelProfileList.GetSize(); ++i)
				m_LevelProfileList[0]->m_WeightCapacity -= m_LevelProfileList[i]->m_WeightCapacity;

			if (m_LevelProfileList[0]->m_WeightCapacity < 0)
				m_LevelProfileList[0]->m_WeightCapacity = 0;
		}
	}
	else {
		for (int i=0; i < m_LevelProfileList.GetSize()-1; ++i) {
			m_LevelProfileList[i]->m_Coordinates.m_Z = m_LevelProfileList[i+1]->m_Coordinates.m_Z;
			m_LevelProfileList[i]->m_Thickness = m_LevelProfileList[i+1]->m_Thickness;
			m_LevelProfileList[i]->m_IsBarHidden = m_LevelProfileList[i+1]->m_IsBarHidden;
		}

		if (m_BayType == BAYTYPE_FLOOR || m_BayType == BAYTYPE_DRIVEIN)
			m_LevelProfileList[m_LevelProfileList.GetSize()-1]->m_Coordinates.m_Z = 0;
		else
			m_LevelProfileList[m_LevelProfileList.GetSize()-1]->m_Coordinates.m_Z = m_Height;
		m_LevelProfileList[m_LevelProfileList.GetSize()-1]->m_Thickness = 0;
		m_LevelProfileList[m_LevelProfileList.GetSize()-1]->m_IsBarHidden = 0;
	}

}

int CBayProfile::ResetLocationSizes()
{
	// Do it twice, the first time only validate that the dimensions are okay
	// the 2nd time actually do it
	for (int times=0; times < 2; ++times) {
		
		for (int i=0; i < m_LevelProfileList.GetSize(); ++i) {
			CLevelProfile *pLevelProfile = m_LevelProfileList[i];
			if (pLevelProfile->m_LocationProfileList.GetSize() == 0)
				continue;
			
			int locsDeep = pLevelProfile->m_LocationRowCount;
			if (locsDeep == 0)
				locsDeep = 1;
			int locCount = pLevelProfile->m_LocationProfileList.GetSize();
			int locsAcross = locCount / locsDeep;
			double locSpace = pLevelProfile->m_LocationProfileList[0]->m_LocationSpace;
			
			double locHeight, locWidth, locDepth;
			
			if ((m_BayType == BAYTYPE_DRIVEIN) || (m_BayType == BAYTYPE_FLOOR)) {
				locHeight = pLevelProfile->m_SelectPositionHeight;
				locDepth = pLevelProfile->m_StackDepth;
				locWidth = pLevelProfile->m_StackWidth;
			}
			else {
				locDepth = m_Depth + pLevelProfile->m_Overhang;
				if (m_BayType == BAYTYPE_PALLET) {
					if (m_PalletDepth > 1)
						locDepth -= m_PalletSpace;
				}

				// To get the height, find the first crossbar above this location that
				// is not hidden; if this one is the top non-hidden one, use the bay height
				int aboveIdx = i+1;
				CLevelProfile *pAbove = NULL;
				while (aboveIdx < m_LevelProfileList.GetSize()) {
					pAbove = m_LevelProfileList[aboveIdx];
					if (! pAbove->m_IsBarHidden)
						break;
					aboveIdx++;
					pAbove = NULL;
				}

				if (pAbove != NULL)
					locHeight = (pAbove->m_Coordinates.m_Z - pAbove->m_Thickness) - 
						pLevelProfile->m_Coordinates.m_Z;
				else
					locHeight = m_Height - pLevelProfile->m_Coordinates.m_Z;
			}
			
			if (locHeight < 1)
				return -1;
			
			double totalLocSpace = 2*locsAcross*locSpace;
			BOOL bMultiRows = FALSE;
			
			
			locWidth = (m_Width-totalLocSpace)/locsAcross;
			if (locWidth < 1) {
				return -2;
			}
			
			// Variable-Depth logic used for bin carousel
			locDepth = (double)(m_Depth+pLevelProfile->m_Overhang) / (double)locsDeep;
			
			if (locDepth < 1) {
				return -3;
			}		
			
			if (times == 1) {
				int xCoordinate = 0;		
				int rowNum;
				
				for (int j=0; j < locCount; ++j) {
					// add one location
					CLocationProfile *pLocProfile = pLevelProfile->m_LocationProfileList[j];
					
					pLocProfile->m_Height = locHeight;
					pLocProfile->m_Depth = locDepth;
					pLocProfile->m_Width = locWidth;
					
					if (locsDeep > 1) {				// we are using multiple depth
						rowNum = i / locsAcross;
						if (i % locsAcross == 0)	// we are starting a new row so reset the xcoordinate back to 0
							xCoordinate = 0;
					}
					else
						rowNum = 0;
					
					// add the space to the right
					xCoordinate += int(locSpace);		// todo: double-check this
					pLocProfile->m_Coordinates.m_X = xCoordinate;
					
					// add the location width and the space to the left
					xCoordinate += int(locWidth + locSpace);
					
					// If there is overhang on a multi-deep, technically the front of the first
					// row of locations will be -overhang but set them to 0 instead
					// then offset the rest of the rows by the overhang
					if (rowNum == 0)
						pLocProfile->m_Coordinates.m_Y = 0;
					else
						pLocProfile->m_Coordinates.m_Y = locDepth*rowNum - pLevelProfile->m_Overhang;

					pLocProfile->m_Coordinates.m_Z = pLevelProfile->m_Coordinates.m_Z;
				}
			}
			
		}
		
	}

	return 0;


}

int CBayProfile::CalculateExtendedValues(BOOL checkOnly, int ruleIdx, int facingIdx)
{
	double selectCube, reserveCube;
	double xCube, xBOH;
	
	
	for (int i=0; i < m_BayRuleList.GetSize(); ++i) {
		
		if (ruleIdx >= 0 && i != ruleIdx)
			continue;

		CBayRule *pRule = m_BayRuleList[i];
		
		CalculateCube(i, selectCube, reserveCube);

		if (selectCube <=0) 
			selectCube = 0;
		
		for (int j=0; j < pRule->m_FacingInfoList.GetSize(); ++j) {
			if (facingIdx >= 0 && j != facingIdx)
				continue;

			CFacingInfo *pFacing = pRule->m_FacingInfoList[j];
			int facingCount = pFacing->m_FacingCount;
			if (this->m_IsFloating == 1) {
				xCube = (selectCube * pRule->m_PctUtilSelPos/100);
				xBOH = xCube * facingCount;
			}
			else {
				xCube = (selectCube * pRule->m_PctUtilSelPos/100 * pRule->m_DesiredRplnPerWeek) * facingCount;
				xBOH = ((selectCube * pRule->m_PctUtilSelPos/100) +
					((reserveCube + pRule->m_AdditionalRsvCube) * pRule->m_PctUtilRsvPos/100)) * (facingCount);
			}
			
			if (checkOnly && (pFacing->m_ExtendedBOH != xCube || pFacing->m_ExtendedBOH != xBOH) ) {
				return -1;
			}
			else if (! checkOnly) {
				pFacing->m_ExtendedCube = xCube;
				pFacing->m_ExtendedBOH = xBOH;
			}
			
		}
	}
	
	return 0;
}

int CBayProfile::CalculateCube(int ruleIdx, double &selectCube, double &reserveCube)
{
	int i;
	double width, depth, height, palletWoodHeight, palletDepth, overhang;
	int locsInLevel, selectLocsInLevel, reserveLocsInLevel, 
		selectLocsInBay, reserveLocsInBay;

	double levelCube, totalSelectCube, totalReserveCube, selectCubeAllTypes, selectRatio, clearance;

	locsInLevel = selectLocsInLevel = reserveLocsInLevel = selectLocsInBay = reserveLocsInBay = 0;
	levelCube = totalSelectCube = totalReserveCube = selectCubeAllTypes = selectRatio = 0;
	clearance = 0;

	CBayRule *pRule = m_BayRuleList[ruleIdx];

	for (i=0; i < m_LevelProfileList.GetSize(); ++i) {

		CLevelProfile *pLevelProfile = m_LevelProfileList[i];
		
		// Skip levels with no locations
		if (pLevelProfile->m_LocationProfileList.GetSize() == 0)
			continue;


		// Skip levels that are above or below hidden crossbars
		// Note: the crossbar associated with a level record
		// it the one above the record, not below it
		if (pLevelProfile->m_IsBarHidden)
			continue;

		BOOL bFoundHidden = FALSE;
		if (pLevelProfile->m_RelativeLevel > 1) {
			// Check the crossbar below the level; if it is hidden, skip it
			for (int j=0; j < m_LevelProfileList.GetSize(); ++j) {
				// Dont' check the same level we are on
				if (j == i)
					continue;
				
				CLevelProfile *pTmpLevel = m_LevelProfileList[j];
				if (pLevelProfile->m_RelativeLevel-1 == pTmpLevel->m_RelativeLevel) {
					if (pTmpLevel->m_IsBarHidden) {
						bFoundHidden = TRUE;
						break;
					}
				}
			}
		}

		if (bFoundHidden)
			continue;

		// Since all locations are the same, use the first one
		CLocationProfile *pLocProfile = pLevelProfile->m_LocationProfileList[0];

		// Get dimensions

		if (pLevelProfile->m_Baytype == BAYTYPE_FLOOR ||
			pLevelProfile->m_Baytype == BAYTYPE_DRIVEIN) {

			width = pLevelProfile->m_StackWidth;
			depth = pLevelProfile->m_StackDepth;
			
			selectLocsInLevel = pLevelProfile->m_SelectPositions;
			selectLocsInBay = 1;
			reserveLocsInLevel = pLevelProfile->m_ReservePositions;
			reserveLocsInBay += reserveLocsInLevel;

			palletWoodHeight = pRule->m_PalletHeight;
			palletDepth = 1;
			overhang = 0;
			clearance = pLevelProfile->m_Clearance;
			
			if ((selectLocsInLevel > 0) && (pLevelProfile->m_SelectPositionHeight > 0)) {
				
				levelCube = selectLocsInLevel * 
					(pLevelProfile->m_SelectPositionHeight - palletWoodHeight - clearance) * 
					width * (depth+overhang);
			}
			else {
				levelCube = 0;
			}
			
			if (pLocProfile->m_IsSelect) {
				selectCubeAllTypes += levelCube;	
				if (pLevelProfile->m_Baytype == pRule->m_Baytype)
					totalSelectCube += levelCube;
			}

			// For floor locs, figure out may reserve pallets per select pallet
			// and multiply the pallet height by that number so we get one pallet
			// wood height per reserve pallet
			if (pLevelProfile->m_SelectPositionHeight > 0) {
				if (pLevelProfile->m_Baytype == BAYTYPE_FLOOR)
					palletWoodHeight *= (pLevelProfile->m_ReservePositionHeight/pLevelProfile->m_SelectPositionHeight);
			}

			levelCube = reserveLocsInLevel * (pLevelProfile->m_ReservePositionHeight - palletWoodHeight) * 
				width * (depth+overhang);
			totalReserveCube += levelCube;


		}
		else {		// Bin, CaseFlow, Pallet, PalletFlow, Carousel
			
			depth = pLocProfile->m_Depth;
			height = pLocProfile->m_Height;
			if (pLevelProfile->m_IsVariableWidthAllowed) {
				// If the minimum width is more than half of the location
				// set the location width to the total level width and assume
				// one location in this level;
				// otherwise use the minimum widht as the location width and
				// divide that into the level width to get the number of locations
				if (pLevelProfile->m_MinimumLocWidth * 2 > pLocProfile->m_Width) {
					width = pLocProfile->m_Width;
					locsInLevel = 1;
				}
				else {
					width = pLevelProfile->m_MinimumLocWidth;
					locsInLevel = (int)(pLocProfile->m_Width/pLevelProfile->m_MinimumLocWidth);
				}
			}
			else {
				width = pLocProfile->m_Width;
				locsInLevel = pLevelProfile->m_LocationProfileList.GetSize();
			}
			
			if (pLocProfile->m_IsSelect)
				selectLocsInBay += locsInLevel;	
			else
				reserveLocsInBay += locsInLevel;

			palletWoodHeight = pRule->m_PalletHeight;
			if (pLevelProfile->m_Baytype == BAYTYPE_PALLET)
				palletDepth = this->m_PalletDepth;
			else
				palletDepth = 1;

			overhang = pLevelProfile->m_Overhang;
			clearance = pLevelProfile->m_Clearance;

			levelCube = locsInLevel * (height - palletWoodHeight - clearance) * width * (depth+overhang);
			
			if (pLocProfile->m_IsSelect) {
				selectCubeAllTypes += levelCube;
				if (pLevelProfile->m_Baytype == pRule->m_Baytype)
					totalSelectCube += levelCube;
			}
			else
				totalReserveCube += levelCube;

		}

		
	}

	if (selectLocsInBay > 0)
		selectCube = (totalSelectCube / selectLocsInBay) / controlService.GetDivisor();
	else
		selectCube = 0;

	double dylandouble = controlService.GetDivisor();

	if (reserveLocsInBay > 0 && selectLocsInBay > 0) {
		reserveCube = (totalReserveCube / selectLocsInBay) / controlService.GetDivisor();
		reserveCube = reserveCube * pRule->m_PercentReserves/100;
	}
	else
		reserveCube = 0;

	// adjust reserve cube if some of the select levels are different bay types
	if (selectCubeAllTypes > totalSelectCube) {
		selectRatio = totalSelectCube / selectCubeAllTypes;
		reserveCube = reserveCube * selectRatio;
	}

	return 0;
}

int CBayProfile::ResetRules()
{
	CMap<int, int, int, int> bayTypeMap;
	int rc = 0;
	CBayRule *pTemplateRule;
	pTemplateRule = new CBayRule;

	if (m_BayRuleList.GetSize() > 0)
		*pTemplateRule = *m_BayRuleList[0];
	else {
		// These are just some default values based on our built-in rack types
		pTemplateRule->m_PctRsvToSelPos = 0;
		pTemplateRule->m_PctUtilRsvPos = 66.67;
		pTemplateRule->m_PctUtilSelPos = 52;
		pTemplateRule->m_PercentReserves = 100;
		pTemplateRule->m_DesiredRplnPerWeek = .5;
		pTemplateRule->m_PalletHeight = 0;
		pTemplateRule->m_AdditionalRsvCube = 24.44;

		// todo: convert some of these values to centimeters if using metric
		switch (m_BayType) {
		case BAYTYPE_CASEFLOW:
			pTemplateRule->m_DesiredRplnPerWeek = 1;
			break;
		case BAYTYPE_PALLETFLOW:
			pTemplateRule->m_PalletHeight = 5;
			pTemplateRule->m_AdditionalRsvCube = 722.22;
			pTemplateRule->m_DesiredRplnPerWeek = 5;
			pTemplateRule->m_PctUtilRsvPos = 92;
			break;
		case BAYTYPE_FLOOR:
		case BAYTYPE_DRIVEIN:
			pTemplateRule->m_PalletHeight = 5;
			pTemplateRule->m_AdditionalRsvCube = 0;
			pTemplateRule->m_PctUtilRsvPos = 92;
			pTemplateRule->m_DesiredRplnPerWeek = 5;
			break;
		case BAYTYPE_PALLET:
			pTemplateRule->m_PalletHeight = 5;
			pTemplateRule->m_AdditionalRsvCube = 0;
			pTemplateRule->m_PctUtilRsvPos = 92;
			pTemplateRule->m_DesiredRplnPerWeek = 2;
			break;
		}

		pTemplateRule->m_Description = m_Description;
		CFacingInfo *pFacingInfo = new CFacingInfo;
		pTemplateRule->m_FacingInfoList.Add(pFacingInfo);
		pFacingInfo->m_Description = "1";
		pFacingInfo->m_FacingCount = 1;
	}

	pTemplateRule->m_BayRuleDBId = 0;
	pTemplateRule->m_BayProfileDBId = m_BayProfileDBId;
	for (int i=0; i < pTemplateRule->m_FacingInfoList.GetSize(); ++i) {
		pTemplateRule->m_FacingInfoList[i]->m_FacingInfoDBId = 0;
		pTemplateRule->m_FacingInfoList[i]->m_BayRuleDBId = 0;
	}

	for (i=0; i < m_LevelProfileList.GetSize(); ++i) {
		int bayType;

		if (bayTypeMap.Lookup(m_LevelProfileList[i]->m_Baytype, bayType))
			continue;

		bayType = m_LevelProfileList[i]->m_Baytype;

		bayTypeMap.SetAt(bayType, bayType);

		BOOL found = FALSE;
		for (int j=0; j < m_BayRuleList.GetSize(); ++j) {
			if (m_BayRuleList[j]->m_Baytype == bayType) {
				found = TRUE;
				break;
			}
		}

		// If a rule already exists for the level type, use it and go on
		if (found)
			continue;

		// Otherwise, we need to create one
		rc = 1;			// indicates that we created a rule
		CBayRule *pNewRule = new CBayRule;
		*pNewRule = *pTemplateRule;
		pNewRule->m_Baytype = bayType;
		m_BayRuleList.Add(pNewRule);
	}
	

	delete pTemplateRule;

	i = m_BayRuleList.GetSize()-1;

	while (i >= 0) {
		int baytype;
		if (! bayTypeMap.Lookup(m_BayRuleList[i]->m_Baytype, baytype)) {
			delete m_BayRuleList[i];
			m_BayRuleList.RemoveAt(i);
		}
		i--;
	}

	return rc;
}


int CBayProfile::Draw(BOOL currentDB, BOOL doSave)
{
	Acad::ErrorStatus es;

	if (doSave) {
		if (CreatePath() < 0)
			return -1;
	}

	if (! currentDB)
		m_pDatabase = new AcDbDatabase();
	else
		m_pDatabase = acdbCurDwg();

	es = m_pDatabase->getBlockTable(m_pBlockTable, AcDb::kForRead);
	if (es != Acad::eOk) {
		ads_printf("Error(%d) getting block table for database.\n", es);
		if (! currentDB)
			delete m_pDatabase;
		return -1;
	}

	es = m_pBlockTable->getAt(ACDB_MODEL_SPACE, m_pBlockTableRecord, AcDb::kForWrite);
	if (es !=  Acad::eOk) {
		ads_printf("Error(%d) getting block table record.\n", es);
		m_pBlockTable->close();
		if (! currentDB)
			delete m_pDatabase;
		return -1;
	}
	
	if (controlService.DrawBayArrow()) {
		
		AcDb3dPolyline *pDirLine;
		AcDbObjectId dirLineId;
		AcGePoint3dArray vertices;
		
		AcGePoint3d point1, point2, point3, point4, point5, point6;
		point1.set(0, -m_Depth/2, 0);
		vertices.append(point1);
		point2.set(point1[X]-5, point1[Y]+5, 0);
		vertices.append(point2);
		point3.set(point2[X]+5, point2[Y]-5, 0);
		vertices.append(point3);
		point4.set(point3[X]+5, point3[Y]+5, 0);
		vertices.append(point4);
		point5.set(point4[X]-5, point4[Y]-5, 0);
		vertices.append(point5);
		point6.set(point5[X], point5[Y]+15, 0);
		vertices.append(point6);
		
		pDirLine = new AcDb3dPolyline(AcDb::k3dSimplePoly,vertices,Adesk::kFalse);
		es = m_pBlockTableRecord->appendAcDbEntity(dirLineId, pDirLine);
		
		pDirLine->close();
	}

	switch (m_BayType) {
	case BAYTYPE_BIN:
		es = DrawBin(FALSE);
		break;
	case BAYTYPE_CASEFLOW:
	case BAYTYPE_PALLETFLOW:
		es = DrawFlow(FALSE);
		break;
	case BAYTYPE_FLOOR:
		es = DrawFloor();
		break;
	case BAYTYPE_DRIVEIN:
		es = DrawDriveIn(FALSE);
		break;
	case BAYTYPE_PALLET:
		es = DrawPallet(FALSE);
		break;
	}

	if (es != Acad::eOk) {
		m_pBlockTable->close();
		m_pBlockTableRecord->close();
		if (! currentDB)
			delete m_pDatabase;
		return -1;
	}

	CString path;
	if (doSave) {
		path.Format("%s\\RackTypes\\%s\\%s\\%s.dwg",
			controlService.m_ClientHome, controlService.m_CurrentDatabase, 
			ConvertBayTypeToPath(m_BayType), m_Description);
		
		es = m_pBlockTableRecord->close();
		if (es != Acad::eOk) {
			ads_printf("Error(%d) closing block table record.\n", es);
			m_pBlockTable->close();
			if (! currentDB)
				delete m_pDatabase;
			return -1;
		}
		
		es = m_pDatabase->saveAs(path);
		if (es != Acad::eOk) {
			ads_printf("Error(%d) saving drawing to: %s.\n", es, path);
			m_pBlockTable->close();
			if (! currentDB)
				delete m_pDatabase;
			return -1;
		}
		
		es = m_pBlockTable->getAt(ACDB_MODEL_SPACE, m_pBlockTableRecord, AcDb::kForWrite);
		if (es !=  Acad::eOk) {
			ads_printf("Error(%d) getting block table record.\n", es);
			m_pBlockTable->close();
			if (! currentDB)
				delete m_pDatabase;
			return -1;
		}
	}

	switch (m_BayType) {
	case BAYTYPE_BIN:
		DrawBin(TRUE);
		break;
	case BAYTYPE_CASEFLOW:
	case BAYTYPE_PALLETFLOW:
		DrawFlow(TRUE);
		break;
	case BAYTYPE_DRIVEIN:
		DrawDriveIn(TRUE);
		break;
	case BAYTYPE_PALLET:
		DrawPallet(TRUE);
		break;
	}

	if (es != Acad::eOk) {
		m_pBlockTable->close();
		m_pBlockTableRecord->close();
		if (! currentDB)
			delete m_pDatabase;
		return -1;
	}

	es = m_pBlockTableRecord->close();
	if (es != Acad::eOk) {
		ads_printf("Error(%d) closing block table record.\n", es);
		m_pBlockTable->close();
		if (! currentDB)
			delete m_pDatabase;
		return -1;
	}
	
	if (doSave) {		
		path.Format("%s\\RackTypes\\%s\\%s\\.%s.dwg",
			controlService.m_ClientHome, controlService.m_CurrentDatabase, 
			ConvertBayTypeToPath(m_BayType), m_Description);
		
		es = m_pDatabase->saveAs(path);
		if (es != Acad::eOk) {
			ads_printf("Error(%d) saving drawing to: %s.\n", es, path);
			m_pBlockTable->close();
			if (! currentDB)
				delete m_pDatabase;
			return -1;
		}
	}

	es = m_pBlockTable->close();
	if (es != Acad::eOk) {
		ads_printf("Error(%d) closing block table record (3).\n", es);
		m_pBlockTable->close();
		if (! currentDB)
			delete m_pDatabase;
		return -1;
	}

	if (! currentDB)
		delete m_pDatabase;

	return 0;
}



int CBayProfile::CreatePath()
{
	CString s = controlService.m_ClientHome;
	s += "\\RackTypes\\";
	s += controlService.m_CurrentDatabase;
	
	if (_mkdir(s) != 0) {
		if (errno != EEXIST) {
			ads_printf("Error(%d) creating bay drawing directory: %s\n", errno, s);
			AfxMessageBox("Error creating bay drawing directory.");
			return -1;
		}
	}
	
	s += "\\";
	s += ConvertBayTypeToPath(m_BayType);

	if (_mkdir(s) != 0) {
		if (errno != EEXIST) {
			ads_printf("Error(%d) creating bay drawing directory: %s\n", errno, s);
			AfxMessageBox("Error creating bay drawing directory.");
			return -1;
		}
	}

	return 0;
}

Acad::ErrorStatus CBayProfile::DrawBin(BOOL bEndUprights)
{
	Acad::ErrorStatus es;

	if (! bEndUprights) {
		
		es = DrawUprights(FALSE);
		if (es !=  Acad::eOk) {
			ads_printf("Error(%d) drawing uprights.\n", es);
			return es;
		}
		
		
		es = DrawLevels();
		if (es !=  Acad::eOk) {
			ads_printf("Error(%d) drawing levels.\n", es);
			return es;
		}
		
		/*
		es = DrawCrossbars(FALSE);
		if (es !=  Acad::eOk) {
			ads_printf("Error(%d) drawing crossbars.\n", es);
			return es;
		}
		*/
	}

	else {
		es = DrawUprights(TRUE);
		if (es !=  Acad::eOk) {
			ads_printf("Error(%d) drawing end uprights.\n", es);
			return es;
		}
	}
	
	return Acad::eOk;
}


Acad::ErrorStatus CBayProfile::DrawFlow(BOOL bEndUprights)
{
	Acad::ErrorStatus es;

	if (! bEndUprights) {
		
		es = DrawUprights(FALSE);
		if (es !=  Acad::eOk) {
			ads_printf("Error(%d) drawing uprights.\n", es);
			return es;
		}
			
		es = DrawLevels();
		if (es !=  Acad::eOk) {
			ads_printf("Error(%d) drawing levels.\n", es);
			return es;
		}
	
	}

	else {
		es = DrawUprights(TRUE);
		if (es !=  Acad::eOk) {
			ads_printf("Error(%d) drawing end uprights.\n", es);
			return es;
		}
	}

	return Acad::eOk;
}


Acad::ErrorStatus CBayProfile::DrawFloor()
{
	Acad::ErrorStatus es;
	AcDbObjectId boxId = AcDbObjectId::kNull;
	
	if (m_LevelProfileList.GetSize() == 0) {
		ads_printf("Error: floor location must have one level.\n");
		return Acad::eUserBreak;
	}

	CLevelProfile *pLevelProfile = m_LevelProfileList[0];
	double paintWidth = 0;

	int numPositions = pLevelProfile->m_SelectPositions + pLevelProfile->m_ReservePositions;
	int oneInCenter;

	if ( (numPositions % 2) == 1 )
		oneInCenter = 1;
	else
		oneInCenter = 0;

	int numUnder = numPositions / 2;
	double tempNum, oneDepth;
	oneDepth = m_Depth/numPositions;

	for (int n = 0; n < numPositions; n++) {

		AcGeMatrix3d mat;
		
		mat(0, 3) = 0;
		if (oneInCenter == 1) {
			mat(1,3) = -1* (numUnder * oneDepth);
			numUnder--;
		}
		else {		
			if (numUnder > 0) {
				tempNum = -1 * oneDepth/2;
				tempNum +=  -1 * (numUnder-1) * oneDepth;
			}
			else {
				tempNum = (oneDepth/2);
				tempNum += (-1*numUnder-1) * oneDepth;
			}
			numUnder--;
			mat(1,3) = tempNum;
			if (numUnder == 0)
				numUnder = -1;
		}

		mat(2,3) = 0;
		
		AcDb3dSolid *pFloor = new AcDb3dSolid();
		es = pFloor->setLinetype("DOT");
		es = pFloor->setLayer("0");
		pFloor->createBox(m_Width, oneDepth, 0.1);
		pFloor->transformBy(mat);

		es = m_pBlockTableRecord->appendAcDbEntity(boxId, pFloor);
		if (es != Acad::eOk) {
			ads_printf("Error(%d) drawing floor.\n", es);
			pFloor->close();
			return es;
		}

		pFloor->close();
	}
	
	// Draw the stacks
	int totalSpace = m_Depth - (numPositions * pLevelProfile->m_StackDepth);
	double depthLineSpace = totalSpace / (numPositions+1);
	double widthLineSpace = (m_Width - pLevelProfile->m_StackWidth)/2;
	

	if ( (numPositions % 2) == 1 )
		oneInCenter = 1;
	else
		oneInCenter = 0;

	numUnder = numPositions / 2;

	
	AcDbLayerTable *pLayerTable = NULL;			//pointer to layer table
	AcDbLayerTableRecord *pLayerRecord = NULL;	//pointer to layer table record
	AcDbBlockTable *blockTable = NULL;			//pointer to block table
	AcDbObjectId layerId;						//Autocad object id of layer
	CString layerName;
/*
	es = m_pDatabase->getLayerTable(pLayerTable, AcDb::kForWrite);
	if (es != Acad::eOk) {
		return es;
	}

	if (pLayerTable->getAt("Paint", layerId) != Acad::eOk) {
		
		pLayerRecord = new AcDbLayerTableRecord();
		if (! pLayerRecord) {
			ads_printf("Error creating layer for drawing.\n");
			return Acad::eUserBreak;
		}
		
		layerName.Format("Paint");
		pLayerRecord->setName(layerName);
		
		es = pLayerTable->add(layerId, pLayerRecord);
		if (es != Acad::eOk) {
			ads_printf("Error(%d) adding layer record for drawing.\n", es);
			pLayerTable->close();
			pLayerRecord->close();
			return Acad::eUserBreak;
		}
		
		pLayerRecord->setIsLocked(FALSE);
	
		es = m_pDatabase->loadLineTypeFile("DOT", "acad.lin");
		AcDbLinetypeTable *pLinetypeTbl;
		AcDbObjectId ltId;
		//m_pDatabase->getSymbolTable(pLinetypeTbl, AcDb::kForRead);
		m_pDatabase->getLinetypeTable(pLinetypeTbl, AcDb::kForRead);
		if ((pLinetypeTbl->getAt("DOT", ltId)) != Acad::eOk)
			pLinetypeTbl->getAt("CONTINUOUS", ltId);
		pLinetypeTbl->close();
		
		pLayerRecord->setLinetypeObjectId(ltId);
		
		es = pLayerRecord->close();
		if (es != Acad::eOk) {
			ads_printf("Error(%d) closing layer record.\n", es);
			return Acad::eUserBreak;
		}
		
	}

	es = pLayerTable->close();
	if (es != Acad::eOk) {
		ads_printf("Error(%d) closing layer table.\n", es);
		pLayerRecord->close();
		return Acad::eUserBreak;
	}
*/
	for (n = 0; n < numPositions; n++) {

		AcGeMatrix3d mat;
		
		mat(0, 3) = 0;
		if (oneInCenter == 1) {
			mat(1,3) = -1* (numUnder * oneDepth);
			numUnder--;
		}
		else {		
			if (numUnder > 0) {
				tempNum = -1 * oneDepth/2;
				tempNum +=  -1 * (numUnder-1) * oneDepth;
			}
			else {
				tempNum = (oneDepth/2);
				tempNum += (-1*numUnder-1) * oneDepth;
			}
			numUnder--;
			mat(1,3) = tempNum;
			if (numUnder == 0)
				numUnder = -1;
		}

		mat(2,3) = 0;
		
		AcDb3dSolid *pFloor = new AcDb3dSolid();
		//es = pFloor->setLayer(layerId);
		//pFloor->setLinetypeScale(5);
		pFloor->createBox(pLevelProfile->m_StackWidth, pLevelProfile->m_StackDepth, 0.1);
		pFloor->transformBy(mat);
		es = m_pBlockTableRecord->appendAcDbEntity(boxId, pFloor);
		if (es != Acad::eOk) {
			ads_printf("Error(%d) drawing floor.\n", es);
			pFloor->close();
			return es;
		}

		pFloor->close();
	}

	return Acad::eOk;
}


Acad::ErrorStatus CBayProfile::DrawDriveIn(BOOL bEndUprights)
{
	Acad::ErrorStatus es;

	if (! bEndUprights) {
		
		es = DrawUprights(FALSE);
		if (es !=  Acad::eOk) {
			ads_printf("Error(%d) drawing uprights.\n", es);
			return es;
		}
		
		es = DrawDriveInLevels();
		if (es !=  Acad::eOk) {
			ads_printf("Error(%d) drawing levels.\n", es);
			return es;
		}

	}

	else {

		es = DrawUprights(TRUE);
		if (es !=  Acad::eOk) {
			ads_printf("Error(%d) drawing end uprights.\n", es);
			return es;
		}
	}
	
	return Acad::eOk;
}


Acad::ErrorStatus CBayProfile::DrawPallet(BOOL bEndUprights)
{
	Acad::ErrorStatus es;

	if (! bEndUprights) {
		
		es = DrawUprights(FALSE);
		if (es !=  Acad::eOk) {
			ads_printf("Error(%d) drawing uprights.\n", es);
			return es;
		}
		
		es = DrawPalletLevels();
		if (es !=  Acad::eOk) {
			ads_printf("Error(%d) drawing levels.\n", es);
			return es;
		}

		es = DrawCrossbars(FALSE);
		if (es !=  Acad::eOk) {
			ads_printf("Error(%d) drawing end crossbars.\n", es);
			return es;
		}

	}

	else {
		es = DrawCrossbars(TRUE);
		if (es !=  Acad::eOk) {
			ads_printf("Error(%d) drawing end crossbars.\n", es);
			return es;
		}

		es = DrawUprights(TRUE);
		if (es !=  Acad::eOk) {
			ads_printf("Error(%d) drawing end uprights.\n", es);
			return es;
		}
	}
	
	return Acad::eOk;
}



Acad::ErrorStatus CBayProfile::DrawUprights(BOOL bEnd)
{
	AcDbObjectId boxId = AcDbObjectId::kNull;
	AcGeMatrix3d mat;
	AcDb3dPolyline *pBars[2];
	AcGePoint3d  point1;
	AcGePoint3d  point2;
	AcGePoint3d  point3;
	AcGePoint3d  point4;
	AcGePoint3d  point5;
	AcGePoint3d  point6;
	AcGePoint3d  point7;
	AcGePoint3d  point8;
	AcGePoint3d  point9;
	AcGePoint3d  point10;
	AcGePoint3d  point11;
	AcGePoint3d  point12;
	AcGePoint3d  point13;
	AcGePoint3d  point14;
	AcGePoint3d  point15;
	AcGePoint3d  point16;
	AcGePoint3dArray vertices;
	
	if (m_UprightWidth == 0)
		return Acad::eOk;

	double x, y, z, backY;
	int i, j, k;
	
	if (m_BayType != BAYTYPE_PALLET) {
		m_PalletDepth = 1;
		m_PalletSpace = 0;
	}
	
	double depth = (m_Depth-(m_PalletSpace*(m_PalletDepth-1)))/m_PalletDepth;

	// Pallet depth will be 1 for everything but double (or more) deeps			
	backY = ((m_PalletDepth*depth) + ((m_PalletDepth-1)*m_PalletSpace))/2;
	
	// for each pallet
	for (k=0; k< (int)m_PalletDepth; k++)
	{
		// for each upright
		for (i=0; i<2; ++i) {
			
			//set the starting coordinates depending if it the front or
			//back upright.

			switch (i) {
			case 0:
				if (bEnd)
					x = m_Width/2;
				else
					x = -m_Width/2-m_UprightWidth;
				y = backY - depth;
				z = -m_UprightHeight/2;
				point1.set(x, y, z);
				break;
			case 1:
				if (bEnd)
					x = m_Width/2;
				else
					x = -m_Width/2-m_UprightWidth;
				y = backY - m_UprightWidth;
				z = -m_UprightHeight/2;
				point1.set(x, y, z);
				backY = backY - depth - m_PalletSpace;
				break;
			}
			
			//create a box by setting the points forming the box.
			point2.set(x, y+m_UprightWidth, z);
			point3.set(x+m_UprightWidth, y+m_UprightWidth, z);
			point4.set(x+m_UprightWidth, y, z);
			point5.set(x, y, z);
			point6.set(x, y, z+m_UprightHeight);
			point7.set(x, y+m_UprightWidth, z+m_UprightHeight);
			point8.set(x, y+m_UprightWidth, z);
			point9.set(x, y+m_UprightWidth, z+m_UprightHeight);
			point10.set(x+m_UprightWidth, y+m_UprightWidth, z+m_UprightHeight);
			point11.set(x+m_UprightWidth, y+m_UprightWidth, z);
			point12.set(x+m_UprightWidth, y+m_UprightWidth, z+m_UprightHeight);
			point13.set(x+m_UprightWidth, y, z+m_UprightHeight);
			point14.set(x+m_UprightWidth, y, z);
			point15.set(x+m_UprightWidth, y, z+m_UprightHeight);
			point16.set(x, y, z+m_UprightHeight);

			
			vertices.append(point1);
			vertices.append(point2);
			vertices.append(point3);
			vertices.append(point4);
			vertices.append(point5);
			vertices.append(point6);
			vertices.append(point7);
			vertices.append(point8);
			vertices.append(point9);
			vertices.append(point10);
			vertices.append(point11);
			vertices.append(point12);
			vertices.append(point13);
			vertices.append(point14);
			vertices.append(point15);
			vertices.append(point16);
			
			pBars[i] = new AcDb3dPolyline(AcDb::k3dSimplePoly,vertices,Adesk::kFalse);
			//pBars[i]->setLayer("BAY_LAYER");
			
			Acad::ErrorStatus es = m_pBlockTableRecord->appendAcDbEntity(boxId, pBars[i]);
			if (es != Acad::eOk) { 
				pBars[i]->close();
				for (j = 15; j >= 0; j--)
					vertices.removeAt(j);
				return es;
			}
			pBars[i]->close();
			
			//clean vertices
			for (j = 15; j >= 0; j--)
				vertices.removeAt(j);
		}
	}


	return Acad::eOk;
}

Acad::ErrorStatus CBayProfile::DrawCrossbars(BOOL bEnd)
{
	if (m_UprightHeight == 0)
		return Acad::eOk;

	double locZ = -m_UprightHeight/2;
	double x[4], y[4], z[4];
	AcDbObjectId boxId = AcDbObjectId::kNull;
	BOOL bFirstShelf = TRUE;

	if (m_BayType != BAYTYPE_PALLET) {
		m_PalletDepth = 1;
		m_PalletSpace = 0;
	}

	double depth = (m_Depth-(m_PalletSpace*(m_PalletDepth-1)))/m_PalletDepth;

	for (int i=0; i < m_LevelProfileList.GetSize(); i++) {
		CLevelProfile *pLevelProfile = m_LevelProfileList[i];
		if (pLevelProfile->m_Coordinates.m_Z == 0)
			continue;

		if (!bFirstShelf) {
			AcDbFace *pFace = new AcDbFace();
			//pFace->setLayer("BAY_LAYER");
			
			y[3] = (m_PalletDepth*depth)/2;
			if (bEnd)
				x[3] = +m_Width/2+m_UprightWidth/2;
			else
				x[3] = -m_Width/2-m_UprightWidth/2;
			z[3] = locZ+0.1;

			y[2] = (m_PalletDepth*depth)/2;
			if (bEnd)
				x[2] = m_Width/2+m_UprightWidth/2;
			else
				x[2] = -m_Width/2-m_UprightWidth/2;
			z[2] = locZ;
			
			AcGePoint3d  point1(x[0],y[0],z[0]);
			pFace->setVertexAt(0, point1);
			AcGePoint3d  point2(x[1],y[1],z[1]);
			pFace->setVertexAt(1, point2);
			AcGePoint3d  point3(x[2],y[2],z[2]);
			pFace->setVertexAt(2, point3);
			AcGePoint3d  point4(x[3],y[3],z[3]);
			pFace->setVertexAt(3, point4);
			
			Acad::ErrorStatus es = m_pBlockTableRecord->appendAcDbEntity(boxId, pFace);
			
			if (es != Acad::eOk) {
				pFace->close();
				return es;
			}
			pFace->close();
			
		}
		else
			bFirstShelf = FALSE;
		
		y[0] = -(m_PalletDepth*depth)/2;
		if (bEnd)
			x[0] = +m_Width/2+m_UprightWidth/2;
		else
			x[0] = -m_Width/2-m_UprightWidth/2;

		z[0] = locZ+0.1;
		y[1] = -(m_PalletDepth*depth)/2;

		if (bEnd)
			x[1] = +m_Width/2+m_UprightWidth/2;
		else
			x[1] = -m_Width/2-m_UprightWidth/2;

		z[1] = locZ;
		locZ = pLevelProfile->m_Coordinates.m_Z-m_UprightHeight/2;
	} 
	
	if (!bFirstShelf) {
		AcDbFace *pFace = new AcDbFace();
		//pFace->setLayer("BAY_LAYER");
		
		y[3] = (m_PalletDepth*depth)/2;

		if (bEnd)
			x[3] = +m_Width/2+m_UprightWidth/2;
		else
			x[3] = -m_Width/2-m_UprightWidth/2;

		z[3] = locZ+0.1;

		y[2] = (m_PalletDepth*depth)/2;

		if (bEnd)
			x[2] = +m_Width/2+m_UprightWidth/2;
		else
			x[2] = -m_Width/2-m_UprightWidth/2;

		z[2] = locZ;
		
		//**********************************************************************************************************
		// Set the verices that make up a 3d surface.
		//**********************************************************************************************************
		AcGePoint3d  point1(x[0],y[0],z[0]);
		pFace->setVertexAt(0, point1);
		AcGePoint3d  point2(x[1],y[1],z[1]);
		pFace->setVertexAt(1, point2);
		AcGePoint3d  point3(x[2],y[2],z[2]);
		pFace->setVertexAt(2, point3);
		AcGePoint3d  point4(x[3],y[3],z[3]);
		pFace->setVertexAt(3, point4);

		Acad::ErrorStatus es = m_pBlockTableRecord->appendAcDbEntity(boxId, pFace);
		if (es != Acad::eOk) {
			pFace->close();
			return es;
		}
		pFace->close();
	}
	
	return Acad::eOk;
		
}



Acad::ErrorStatus CBayProfile::DrawLevels()
{
	AcDbObjectId boxId = AcDbObjectId::kNull;
	AcGeMatrix3d mat;
	AcGePoint3d  point1;
	AcGePoint3d  point2;
	AcGePoint3d  point3;
	AcGePoint3d  point4;
	AcGePoint3d  point5;
	AcGePoint3d  point6;
	AcGePoint3d  point7;
	AcGePoint3d  point8;
	AcGePoint3d  point9;
	AcGePoint3d  point10;
	AcGePoint3d  point11;
	AcGePoint3d  point12;
	AcGePoint3d  point13;
	AcGePoint3d  point14;
	AcGePoint3d  point15;
	AcGePoint3d  point16;
	AcGePoint3dArray vertices;
	
	double flowDifference;

	for (int i=0; i < m_LevelProfileList.GetSize(); i++) {
		CLevelProfile *pLevel = m_LevelProfileList[i];

		if (pLevel->m_Baytype != BAYTYPE_CASEFLOW && pLevel->m_Baytype != BAYTYPE_PALLETFLOW)
			flowDifference = 0;
		else
			flowDifference = pLevel->m_FlowDifference;

		// Skip the floor
		if (pLevel->m_Coordinates.m_Z == 0)
			continue;

		AcDb3dPolyline *pShelf; 
		//x = width; y = depth; z = height
		double x, y, z;
		double m_Thickness = pLevel->m_Thickness;
		
		//No need to draw the level if it is k
		if (pLevel->m_IsBarHidden)
			continue;
		
		//set the starting coordinates
		x = -m_Width/2;
		y = -m_Depth/2+m_UprightWidth;
		z = pLevel->m_Coordinates.m_Z-m_UprightHeight/2;
		point1.set(x, y, z);
		
		mat(0, 3) = 0; 
		mat(1, 3) = 0;   
		mat(2, 3) = pLevel->m_Coordinates.m_Z-m_UprightHeight/2;
		
		//draw box
		point2.set(x, y+m_Depth-m_UprightWidth*2, z+flowDifference);
		point3.set(x, y+m_Depth-m_UprightWidth*2, z+m_Thickness+flowDifference);
		point4.set(x, y, z+m_Thickness);
		point5.set(x, y, z);
		point6.set(x+m_Width, y, z);
		point7.set(x+m_Width, y+m_Depth-m_UprightWidth*2, z+flowDifference);
		point8.set(x, y+m_Depth-m_UprightWidth*2, z+flowDifference);
		point9.set(x+m_Width, y+m_Depth-m_UprightWidth*2, z+flowDifference);
		point10.set(x+m_Width, y+m_Depth-m_UprightWidth*2, z+m_Thickness+flowDifference);
		point11.set(x, y+m_Depth-m_UprightWidth*2, z+m_Thickness+flowDifference);
		point12.set(x+m_Width, y+m_Depth-m_UprightWidth*2, z+m_Thickness+flowDifference);
		point13.set(x+m_Width, y, z+m_Thickness);
		point14.set(x, y, z+m_Thickness);
		point15.set(x+m_Width, y, z+m_Thickness);
		point16.set(x+m_Width, y, z);
		
		
		vertices.append(point1);
		vertices.append(point2);
		vertices.append(point3);
		vertices.append(point4);
		vertices.append(point5);
		vertices.append(point6);
		vertices.append(point7);
		vertices.append(point8);
		vertices.append(point9);
		vertices.append(point10);
		vertices.append(point11);
		vertices.append(point12);
		vertices.append(point13);
		vertices.append(point14);
		vertices.append(point15);
		vertices.append(point16);
		
		pShelf = new AcDb3dPolyline(AcDb::k3dSimplePoly,vertices,Adesk::kFalse);
		//pShelf->setLayer("BAY_LAYER");
		
		Acad::ErrorStatus es = m_pBlockTableRecord->appendAcDbEntity(boxId, pShelf);
		if (es != Acad::eOk) {
			pShelf->close();
			for (int j = 15; j >= 0; j--)
				vertices.removeAt(j);
			return es;
		}

		pShelf->close();
		
		for (int j = 15; j >= 0; j--)
			vertices.removeAt(j);
	}
	
	return Acad::eOk;
	
}




Acad::ErrorStatus CBayProfile::DrawPalletLevels()
{
	AcDbObjectId boxId = AcDbObjectId::kNull;
	AcGePoint3d  point1;
	AcGePoint3d  point2;
	AcGePoint3d  point3;
	AcGePoint3d  point4;
	AcGePoint3d  point5;
	AcGePoint3d  point6;
	AcGePoint3d  point7;
	AcGePoint3d  point8;
	AcGePoint3d  point9;
	AcGePoint3d  point10;
	AcGePoint3d  point11;
	AcGePoint3d  point12;
	AcGePoint3d  point13;
	AcGePoint3d  point14;
	AcGePoint3d  point15;
	AcGePoint3d  point16;
	AcGePoint3dArray vertices;
	AcDb3dPolyline *pShelf1; 
	
	double x, y, z, thickness, backY;
	int i, j, k;
	
	// Do not draw the top shelf which is the top level...
	for (i=0; i < m_LevelProfileList.GetSize(); i++) {
		
		CLevelProfile *pLevelProfile = m_LevelProfileList[i];
		
		if (pLevelProfile->m_Coordinates.m_Z == 0)
			continue;
		
		
		thickness = pLevelProfile->m_Thickness;
		
		// start at the very back Y
		double depth = (m_Depth-(m_PalletSpace*(m_PalletDepth-1)))/m_PalletDepth;

		backY = (depth*m_PalletDepth + ((m_PalletDepth-1)*m_PalletSpace))/2;
		
		// for each pallet deep
		for (k=0; k < (int)m_PalletDepth; ++k) {
			
			if (pLevelProfile->m_IsBarHidden)
				continue;
			
			
			x = -m_Width/2;
			//y = m_Depth/2-m_Thickness;
			y = backY - thickness;
			
			z = pLevelProfile->m_Coordinates.m_Z-m_UprightHeight/2-thickness/2;
			point1.set(x, y, z);
			point2.set(x, y+thickness, z);
			point3.set(x, y+thickness, z+thickness);
			point4.set(x, y, z+thickness);
			point5.set(x, y, z);
			point6.set(x+m_Width, y, z);
			point7.set(x+m_Width, y+thickness, z);
			point8.set(x, y+thickness, z);
			point9.set(x+m_Width, y+thickness, z);
			point10.set(x+m_Width, y+thickness, z+thickness);
			point11.set(x, y+thickness, z+thickness);
			point12.set(x+m_Width, y+thickness, z+thickness);
			point13.set(x+m_Width, y, z+thickness);
			point14.set(x, y, z+thickness);
			point15.set(x+m_Width, y, z+thickness);
			point16.set(x+m_Width, y, z);
			
			vertices.append(point1);
			vertices.append(point2);
			vertices.append(point3);
			vertices.append(point4);
			vertices.append(point5);
			vertices.append(point6);
			vertices.append(point7);
			vertices.append(point8);
			vertices.append(point9);
			vertices.append(point10);
			vertices.append(point11);
			vertices.append(point12);
			vertices.append(point13);
			vertices.append(point14);
			vertices.append(point15);
			vertices.append(point16);
			
			Acad::ErrorStatus es;
			
			pShelf1 = new AcDb3dPolyline(AcDb::k3dSimplePoly,vertices,Adesk::kFalse);
			//pShelf1->setLayer("BAY_LAYER");
			
			es = m_pBlockTableRecord->appendAcDbEntity(boxId, pShelf1);
			if (es != Acad::eOk) {
				pShelf1->close();
				for (j = 15; j >= 0; j--)
					vertices.removeAt(j);
				return es;
			}
			
			pShelf1->close();
			
			for (j = 15; j >= 0; j--)
				vertices.removeAt(j);
		
			
			x = -m_Width/2;
			//y = m_Depth/2-thickness;
			y = backY - depth;
			backY = backY - depth - m_PalletSpace;
			
			z = pLevelProfile->m_Coordinates.m_Z-m_UprightHeight/2-thickness/2;
			point1.set(x, y, z);
			point2.set(x, y+thickness, z);
			point3.set(x, y+thickness, z+thickness);
			point4.set(x, y, z+thickness);
			point5.set(x, y, z);
			point6.set(x+m_Width, y, z);
			point7.set(x+m_Width, y+thickness, z);
			point8.set(x, y+thickness, z);
			point9.set(x+m_Width, y+thickness, z);
			point10.set(x+m_Width, y+thickness, z+thickness);
			point11.set(x, y+thickness, z+thickness);
			point12.set(x+m_Width, y+thickness, z+thickness);
			point13.set(x+m_Width, y, z+thickness);
			point14.set(x, y, z+thickness);
			point15.set(x+m_Width, y, z+thickness);
			point16.set(x+m_Width, y, z);
			
			vertices.append(point1);
			vertices.append(point2);
			vertices.append(point3);
			vertices.append(point4);
			vertices.append(point5);
			vertices.append(point6);
			vertices.append(point7);
			vertices.append(point8);
			vertices.append(point9);
			vertices.append(point10);
			vertices.append(point11);
			vertices.append(point12);
			vertices.append(point13);
			vertices.append(point14);
			vertices.append(point15);
			vertices.append(point16);
			
			
			pShelf1 = new AcDb3dPolyline(AcDb::k3dSimplePoly,vertices,Adesk::kFalse);
			//pShelf1->setLayer("BAY_LAYER");
			
			es = m_pBlockTableRecord->appendAcDbEntity(boxId, pShelf1);
			if (es != Acad::eOk) {
				pShelf1->close();
				for (j = 15; j >= 0; j--)
					vertices.removeAt(j);
				return es;
			}
			
			
			pShelf1->close();
			
			for (j = 15; j >= 0; j--)
				vertices.removeAt(j);
			
			
		}	// end for each pallet deep
		
	}	// end for each shelf
		
		
	return Acad::eOk;
}

Acad::ErrorStatus CBayProfile::DrawDriveInLevels()
{
	Acad::ErrorStatus es;
	AcDbObjectId boxId = AcDbObjectId::kNull;
	AcGeMatrix3d mat;
	AcGePoint3d  point1;
	AcGePoint3d  point2;
	AcGePoint3d  point3;
	AcGePoint3d  point4;
	AcGePoint3d  point5;
	AcGePoint3d  point6;
	AcGePoint3d  point7;
	AcGePoint3d  point8;
	AcGePoint3d  point9;
	AcGePoint3d  point10;
	AcGePoint3d  point11;
	AcGePoint3d  point12;
	AcGePoint3d  point13;
	AcGePoint3d  point14;
	AcGePoint3d  point15;
	AcGePoint3d  point16;
	AcGePoint3dArray vertices;
	
	if (m_LevelProfileList.GetSize() == 0) {
		ads_printf("Error: drive in bay must have exactly one level.");
		return Acad::eUserBreak;
	}

	CLevelProfile *pLevelProfile = m_LevelProfileList[0];

	int levels = m_Height / (pLevelProfile->m_SelectPositionHeight)+1;

	for (int i=1; i < levels; i++) {

		AcDb3dPolyline *pShelf1; 
		double x, y, z;
		double thickness = 4;
		
		//Create the right side bar
		//set starting coordinates
		x = -m_Width/2;
		y = -m_Depth/2+m_UprightWidth;
		
		int shelfHeight = i*(pLevelProfile->m_SelectPositionHeight);

		z = shelfHeight - m_UprightHeight/2 - thickness;
		//z = shelfHeights[i]-uprightsDimensions.height/2-thickness;
		point1.set(x, y, z);
		
		
		//create box by setting the vertices of the box
		point2.set(x, y+m_Depth-m_UprightWidth*2, z);
		point3.set(x, y+m_Depth-m_UprightWidth*2, z+thickness);
		point4.set(x, y, z+thickness);
		point5.set(x, y, z);
		point6.set(x+thickness, y, z);
		point7.set(x+thickness, y+m_Depth-m_UprightWidth*2, z);
		point8.set(x, y+m_Depth-m_UprightWidth*2, z);
		point9.set(x+thickness, y+m_Depth-m_UprightWidth*2, z);
		point10.set(x+thickness, y+m_Depth-m_UprightWidth*2, z+thickness);
		point11.set(x, y+m_Depth-m_UprightWidth*2, z+thickness);
		point12.set(x+thickness, y+m_Depth-m_UprightWidth*2, z+thickness);
		point13.set(x+thickness, y, z+thickness);
		point14.set(x, y, z+thickness);
		point15.set(x+thickness, y, z+thickness);
		point16.set(x+thickness, y, z);
		
		vertices.append(point1);
		vertices.append(point2);
		vertices.append(point3);
		vertices.append(point4);
		vertices.append(point5);
		vertices.append(point6);
		vertices.append(point7);
		vertices.append(point8);
		vertices.append(point9);
		vertices.append(point10);
		vertices.append(point11);
		vertices.append(point12);
		vertices.append(point13);
		vertices.append(point14);
		vertices.append(point15);
		vertices.append(point16);
		
		pShelf1 = new AcDb3dPolyline(AcDb::k3dSimplePoly,vertices,Adesk::kFalse);		
		es = m_pBlockTableRecord->appendAcDbEntity(boxId, pShelf1);
		if (es != Acad::eOk) {
			ads_printf("Error during append.\n");
			pShelf1->close();
			for (int j = 15; j >= 0; j--)
				vertices.removeAt(j);
		}

		pShelf1->close();
		for (int j = 15; j >= 0; j--)
			vertices.removeAt(j);
		
		AcDb3dPolyline *pShelf2; 
		
		//Create the left side bar
		//set starting coordinates
		x = m_Width/2-thickness;
		y = -m_Depth/2+m_UprightWidth;
	
		shelfHeight = i*(pLevelProfile->m_SelectPositionHeight);
		z = shelfHeight - m_UprightHeight/2 - thickness;

		point1.set(x, y, z);
		
		//create box by setting the vertices of the box
		point2.set(x, y+m_Depth-m_UprightWidth*2, z);
		point3.set(x, y+m_Depth-m_UprightWidth*2, z+thickness);
		point4.set(x, y, z+thickness);
		point5.set(x, y, z);
		point6.set(x+thickness, y, z);
		point7.set(x+thickness, y+m_Depth-m_UprightWidth*2, z);
		point8.set(x, y+m_Depth-m_UprightWidth*2, z);
		point9.set(x+thickness, y+m_Depth-m_UprightWidth*2, z);
		point10.set(x+thickness, y+m_Depth-m_UprightWidth*2, z+thickness);
		point11.set(x, y+m_Depth-m_UprightWidth*2, z+thickness);
		point12.set(x+thickness, y+m_Depth-m_UprightWidth*2, z+thickness);
		point13.set(x+thickness, y, z+thickness);
		point14.set(x, y, z+thickness);
		point15.set(x+thickness, y, z+thickness);
		point16.set(x+thickness, y, z);
		
		vertices.append(point1);
		vertices.append(point2);
		vertices.append(point3);
		vertices.append(point4);
		vertices.append(point5);
		vertices.append(point6);
		vertices.append(point7);
		vertices.append(point8);
		vertices.append(point9);
		vertices.append(point10);
		vertices.append(point11);
		vertices.append(point12);
		vertices.append(point13);
		vertices.append(point14);
		vertices.append(point15);
		vertices.append(point16);
		
		pShelf2 = new AcDb3dPolyline(AcDb::k3dSimplePoly,vertices,Adesk::kFalse);
		
		es = m_pBlockTableRecord->appendAcDbEntity(boxId, pShelf2);
		if (es != Acad::eOk) {
			ads_printf("Error during append(2).\n");
			pShelf2->close();
			for (int j = 15; j >= 0; j--)
				vertices.removeAt(j);
			return es;
		}

		pShelf2->close();
		
		for (j = 15; j >= 0; j--)
			vertices.removeAt(j);
		
	}

	return Acad::eOk;
}


int CBayProfile::DrawByPosition(AcDbDatabase *pDatabase, const C3DPoint &centerPoint, double rotation,
								double leftBarWidth, double rightBarWidth)
{
	Acad::ErrorStatus es;

	m_pDatabase = pDatabase;

	es = m_pDatabase->getBlockTable(m_pBlockTable, AcDb::kForWrite);
	if (es != Acad::eOk) {
		ads_printf("Error(%d) getting block table for database.\n", es);
		return -1;
	}

	m_pBlockTableRecord = new AcDbBlockTableRecord;
	m_pBlockTable->add(m_BlockId, m_pBlockTableRecord);
	
	if (controlService.DrawBayArrow()) {
		
		AcDb3dPolyline *pDirLine;
		AcDbObjectId dirLineId;
		AcGePoint3dArray vertices;
		
		AcGePoint3d point1, point2, point3, point4, point5, point6;
		point1.set(0, -m_Depth/2, 0);
		vertices.append(point1);
		point2.set(point1[X]-5, point1[Y]+5, 0);
		vertices.append(point2);
		point3.set(point2[X]+5, point2[Y]-5, 0);
		vertices.append(point3);
		point4.set(point3[X]+5, point3[Y]+5, 0);
		vertices.append(point4);
		point5.set(point4[X]-5, point4[Y]-5, 0);
		vertices.append(point5);
		point6.set(point5[X], point5[Y]+15, 0);
		vertices.append(point6);
		
		pDirLine = new AcDb3dPolyline(AcDb::k3dSimplePoly,vertices,Adesk::kFalse);
		es = m_pBlockTableRecord->appendAcDbEntity(dirLineId, pDirLine);
		
		pDirLine->close();
	}

	double saveUprightWidth = m_UprightWidth;
	if (leftBarWidth < 0)
		leftBarWidth = m_UprightWidth;
	m_UprightWidth = leftBarWidth;

	switch (m_BayType) {
	case BAYTYPE_BIN:
		es = DrawBin(FALSE);
		break;
	case BAYTYPE_CASEFLOW:
	case BAYTYPE_PALLETFLOW:
		es = DrawFlow(FALSE);
		break;
	case BAYTYPE_FLOOR:
		es = DrawFloor();
		break;
	case BAYTYPE_DRIVEIN:
		es = DrawDriveIn(FALSE);
		break;
	case BAYTYPE_PALLET:
		es = DrawPallet(FALSE);
		break;
	}

	if (rightBarWidth < 0)
		rightBarWidth = m_UprightWidth;
	m_UprightWidth = rightBarWidth;
	
	switch (m_BayType) {
	case BAYTYPE_BIN:
		DrawBin(TRUE);
		break;
	case BAYTYPE_CASEFLOW:
	case BAYTYPE_PALLETFLOW:
		DrawFlow(TRUE);
		break;
	case BAYTYPE_DRIVEIN:
		DrawDriveIn(TRUE);
		break;
	case BAYTYPE_PALLET:
		DrawPallet(TRUE);
		break;
	}
	
	m_UprightWidth = saveUprightWidth;


	m_pBlockTableRecord->close();

	AcDbBlockReference *pBlockReference = new AcDbBlockReference;
	pBlockReference->setBlockTableRecord(m_BlockId);
	
	AcGePoint3d pt;
	pt[X] = centerPoint.m_X;
	pt[Y] = centerPoint.m_Y;
	pt[Z] = centerPoint.m_Z;

	pBlockReference->setPosition(pt);
	pBlockReference->setRotation(PI/180 * rotation);

	AcDbBlockTableRecord *pModelSpace;

	es = m_pBlockTable->getAt(ACDB_MODEL_SPACE, pModelSpace, AcDb::kForWrite);
	if (es !=  Acad::eOk) {
		ads_printf("Error(%d) getting block table record.\n", es);
		m_pBlockTable->close();
		return -1;
	}

	m_pBlockTable->close();
	AcDbObjectId referenceId;
	pModelSpace->appendAcDbEntity(referenceId, pBlockReference);
	pModelSpace->close();
	pBlockReference->close();

	return 0;

}


AcDbObjectId CBayProfile::CreateAsBlock(const CString &blockName, double leftUprightWidth, double rightUprightWidth)
{
	Acad::ErrorStatus es;
	
	m_pDatabase = acdbCurDwg();

	es = acdbCurDwg()->getBlockTable(m_pBlockTable, AcDb::kForWrite);
	if (es != Acad::eOk) {
		controlService.Log("", "Error(%d) getting block table for database.\n", es);
		return NULL;
	}

	m_pBlockTableRecord = new AcDbBlockTableRecord;
	m_pBlockTable->add(m_BlockId, m_pBlockTableRecord);
	
	m_pBlockTable->close();
	m_pBlockTableRecord->setName(blockName);
	
	if (controlService.DrawBayArrow()) {
		
		// Temporary - draw an error to show where the front of the bay is
		AcDb3dPolyline *pDirLine;
		AcDbObjectId dirLineId;
		AcGePoint3dArray vertices;
		
		AcGePoint3d point1, point2, point3, point4, point5, point6;
		point1.set(0, -m_Depth/2+m_Depth/10, 0);
		vertices.append(point1);
		point2.set(point1[X]-5, point1[Y]+5, 0);
		vertices.append(point2);
		point3.set(point2[X]+5, point2[Y]-5, 0);
		vertices.append(point3);
		point4.set(point3[X]+5, point3[Y]+5, 0);
		vertices.append(point4);
		point5.set(point4[X]-5, point4[Y]-5, 0);
		vertices.append(point5);
		point6.set(point5[X], point5[Y]+15, 0);
		vertices.append(point6);
		
		pDirLine = new AcDb3dPolyline(AcDb::k3dSimplePoly,vertices,Adesk::kFalse);
		es = m_pBlockTableRecord->appendAcDbEntity(dirLineId, pDirLine);
		if (es != Acad::eOk) {
			controlService.Log("", "Error (%d) drawing arrow line in bay block.\n", es);
			delete pDirLine;
			// go ahead and keep going since this isn't critical
		}
		else
			pDirLine->close();
	}


	double saveUprightWidth = m_UprightWidth;
	if (leftUprightWidth < 0)
		leftUprightWidth = m_UprightWidth;
	m_UprightWidth = leftUprightWidth;

	switch (m_BayType) {
	case BAYTYPE_BIN:
		es = DrawBin(FALSE);
		break;
	case BAYTYPE_CASEFLOW:
	case BAYTYPE_PALLETFLOW:
		es = DrawFlow(FALSE);
		break;
	case BAYTYPE_FLOOR:
		es = DrawFloor();
		break;
	case BAYTYPE_DRIVEIN:
		es = DrawDriveIn(FALSE);
		break;
	case BAYTYPE_PALLET:
		es = DrawPallet(FALSE);
		break;
	}

	if (rightUprightWidth != 0) {
		if (rightUprightWidth < 0)
			rightUprightWidth = m_UprightWidth;
		m_UprightWidth = rightUprightWidth;
		
		switch (m_BayType) {
		case BAYTYPE_BIN:
			es = DrawBin(TRUE);
			break;
		case BAYTYPE_CASEFLOW:
		case BAYTYPE_PALLETFLOW:
			es = DrawFlow(TRUE);
			break;
		case BAYTYPE_DRIVEIN:
			es = DrawDriveIn(TRUE);
			break;
		case BAYTYPE_PALLET:
			es = DrawPallet(TRUE);
			break;
		}
	}
	m_UprightWidth = saveUprightWidth;

	if (es != Acad::eOk) {
		controlService.Log("", "Error(%d) drawing bay as block.\n", es);
		m_pBlockTableRecord->close();
		return NULL;
	}

	m_pBlockTableRecord->close();

	return m_BlockId;

}

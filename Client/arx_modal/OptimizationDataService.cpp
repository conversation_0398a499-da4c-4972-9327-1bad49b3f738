// OptimizationDataService.cpp: implementation of the COptimizationDataService class.
//
//////////////////////////////////////////////////////////////////////

#include "stdafx.h"
#include "modal.h"
#include "DataAccessService.h"
#include "OptimizationDataService.h"

#ifdef _DEBUG
#undef THIS_FILE
static char THIS_FILE[]=__FILE__;
#define new DEBUG_NEW
#endif

//////////////////////////////////////////////////////////////////////
// Construction/Destruction
//////////////////////////////////////////////////////////////////////
extern CDataAccessService dataAccessService;

COptimizationDataService::COptimizationDataService()
{

}

COptimizationDataService::~COptimizationDataService()
{

}


int COptimizationDataService::GetProductRankings(int productDBID, CStringArray &rankings)
{
	CString tempString;
//	int tempInt;
	CSsaStringArray tempSendArray;
	CSsaStringArray tempRecvArray;
	CString sendString;
	int i = 0;
	CString queryText;

	/////////////////////////////////////////////////////////////
	// build the data to send to the Forte connection server
	/////////////////////////////////////////////////////////////

	queryText.Format("select pa.dbbayprofileid, bp.description, pa.baytype "
		"from dbpass1resavail pa, dbbayprofile bp "
		"where pa.dbproductpackid = %d "
		"and pa.dbbayprofileid = bp.dbbayprofileid "
		"order by ranking", productDBID);
		

	return dataAccessService.ExecuteQuery("GetProductRankings", queryText, rankings);

}


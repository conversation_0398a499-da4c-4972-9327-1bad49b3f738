// MainWizard.cpp : implementation file
//

#include "stdafx.h"
#include "modal.h"
#include "MainWizard.h"
#include "HelpService.h"
#include "ControlService.h"

#ifdef _DEBUG
#define new DEBUG_NEW
#undef THIS_FILE
static char THIS_FILE[] = __FILE__;
#endif

/////////////////////////////////////////////////////////////////////////////
// CMainWizard dialog

extern CHelpService helpService;

CMainWizard::CMainWizard(CWnd* pParent /*=NULL*/)
	: CDialog(CMainWizard::IDD, pParent)
{
	//{{AFX_DATA_INIT(CMainWizard)
	m_Instructions = _T("");
	//}}AFX_DATA_INIT
}


void CMainWizard::DoDataExchange(CDataExchange* pDX)
{
	CDialog::DoDataExchange(pDX);
	//{{AFX_DATA_MAP(CMainWizard)
	DDX_Text(pDX, IDC_INSTRUCTIONS, m_Instructions);
	//}}AFX_DATA_MAP
}


BEGIN_MESSAGE_MAP(CMainWizard, CDialog)
	//{{AFX_MSG_MAP(CMainWizard)
	ON_BN_CLICKED(IDHELP, OnHelp)
	ON_BN_CLICKED(IDC_USEWIZARD, OnUsewizard)
	ON_BN_CLICKED(IDC_NEWFACILITY, OnNewfacility)
	ON_BN_CLICKED(IDC_OPENFACILITY, OnOpenfacility)
	ON_BN_CLICKED(IDC_OPTIMIZEHELP, OnOptimizehelp)
	//}}AFX_MSG_MAP
END_MESSAGE_MAP()

/////////////////////////////////////////////////////////////////////////////
// CMainWizard message handlers

BOOL CMainWizard::OnInitDialog() 
{
	CDialog::OnInitDialog();
	
	CButton *pButton = (CButton *)GetDlgItem(IDC_USEWIZARD);
	pButton->SetCheck(1);
	CBitmap bitmap;
	bitmap.LoadBitmap(IDB_USEWIZARDD);
	pButton->SetBitmap((HBITMAP)bitmap.Detach());

	pButton = (CButton *)GetDlgItem(IDC_NEWFACILITY);
	bitmap.LoadBitmap(IDB_NEWFACILITYD);
	pButton->SetBitmap((HBITMAP)bitmap.Detach());

	pButton = (CButton *)GetDlgItem(IDC_OPENFACILITY);
	bitmap.LoadBitmap(IDB_OPENFACILITYD);
	pButton->SetBitmap((HBITMAP)bitmap.Detach());

	pButton = (CButton *)GetDlgItem(IDC_OPTIMIZEHELP);
	bitmap.LoadBitmap(IDB_INSTRUCTIONSD);
	pButton->SetBitmap((HBITMAP)bitmap.Detach());

	return TRUE;  // return TRUE unless you set the focus to a control
	              // EXCEPTION: OCX Property Pages should return FALSE
}

void CMainWizard::OnOK() 
{
	// TODO: Add extra validation here
	
	CDialog::OnOK();
}

void CMainWizard::OnCancel() 
{
	// TODO: Add extra cleanup here
	
	CDialog::OnCancel();
}

void CMainWizard::OnHelp() 
{
		
}

void CMainWizard::OnUsewizard() 
{
	// TODO: Add your control notification handler code here
	
}

void CMainWizard::OnNewfacility() 
{
	// TODO: Add your control notification handler code here
	
}

void CMainWizard::OnOpenfacility() 
{
	// TODO: Add your control notification handler code here
	
}

void CMainWizard::OnOptimizehelp() 
{
	helpService.ShowScreenHelp("What_is_EXceed_Optimize");
	
}

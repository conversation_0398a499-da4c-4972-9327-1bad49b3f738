// BayProfileCrossbarInfo.cpp: implementation of the CBayProfileCrossbarInfo class.
//
//////////////////////////////////////////////////////////////////////

#include "stdafx.h"
#include "modal.h"
#include "BayProfileCrossbarInfo.h"

#ifdef _DEBUG
#undef THIS_FILE
static char THIS_FILE[]=__FILE__;
#define new DEBUG_NEW
#endif

//////////////////////////////////////////////////////////////////////
// Construction/Destruction
//////////////////////////////////////////////////////////////////////

CBayProfileCrossbarInfo::CBayProfileCrossbarInfo()
{
	m_Height = 0;
	m_Thickness = 0;
	m_LocationCount = 0;
	m_LocationSpace = 0;
	m_Clearance = 0;
	m_MinimumWidth = 0;
	m_LocationRowCount = 0;
}

CBayProfileCrossbarInfo::~CBayProfileCrossbarInfo()
{

}


CBayProfileCrossbarInfo::CBayProfileCrossbarInfo(const CBayProfileCrossbarInfo& other)
{
	m_Height = other.m_Height;
	m_IsHidden = other.m_IsHidden;
	m_Thickness = other.m_Thickness;
	m_IsSelected = other.m_IsSelected;
	m_LocationCount = other.m_LocationCount;
	m_LocationSpace = other.m_LocationSpace;
	m_Clearance = other.m_Clearance;
	m_MinimumWidth = other.m_MinimumWidth;
	m_LocationRowCount = other.m_LocationRowCount;
}


CBayProfileCrossbarInfo& CBayProfileCrossbarInfo::operator=(const CBayProfileCrossbarInfo& other)
{
	m_Height = other.m_Height;
	m_IsHidden = other.m_IsHidden;
	m_Thickness = other.m_Thickness;
	m_IsSelected = other.m_IsSelected;
	m_LocationCount = other.m_LocationCount;
	m_LocationSpace = other.m_LocationSpace;
	m_Clearance = other.m_Clearance;
	m_MinimumWidth = other.m_MinimumWidth;
	m_LocationRowCount = other.m_LocationRowCount;

	return *this;
}

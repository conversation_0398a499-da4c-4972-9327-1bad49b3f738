#if !defined(AFX_LEVELLOCATIONDIALOG_H__7F951C22_2091_485B_80CB_6A76A4823EB8__INCLUDED_)
#define AFX_LEVELLOCATIONDIALOG_H__7F951C22_2091_485B_80CB_6A76A4823EB8__INCLUDED_


#include "LevelLocationButton.h"
#include "BayProfile.h"

#if _MSC_VER > 1000
#pragma once
#endif // _MSC_VER > 1000
// LevelLocationDialog.h : header file
//

/////////////////////////////////////////////////////////////////////////////
// CLevelLocationDialog dialog

class CLevelLocationDialog : public CDialog
{
// Construction
public:
	CBayProfile *m_pBayProfile;
	CString m_Handle;
	CLevelLocationDialog(CWnd* pParent = NULL);   // standard constructor	
	virtual ~CLevelLocationDialog();
	void UpdateToolTip(int x, int y, const CString &msg);

// Dialog Data
	//{{AFX_DATA(CLevelLocationDialog)
	enum { IDD = IDD_LEVEL_LOCATION_MAINTENANCE };
	CComboBox	m_ElementListCtrl;
	CButton	m_ViewElementCtrl;
	CButton	m_ViewLocationCtrl;
	CButton	m_ViewLevelCtrl;
	CComboBox	m_LocationListCtrl;
	CComboBox	m_LevelListCtrl;
	CLevelLocationButton	m_LevelButton;
	CString	m_Mask;
	//}}AFX_DATA


// Overrides
	// ClassWizard generated virtual function overrides
	//{{AFX_VIRTUAL(CLevelLocationDialog)
	public:
	virtual BOOL PreTranslateMessage(MSG* pMsg);
	protected:
	virtual void DoDataExchange(CDataExchange* pDX);    // DDX/DDV support
	//}}AFX_VIRTUAL


// Implementation
protected:

	// Generated message map functions
	//{{AFX_MSG(CLevelLocationDialog)
	virtual BOOL OnInitDialog();
	afx_msg BOOL OnHelpInfo(HELPINFO* pHelpInfo);
	afx_msg void OnSize(UINT nType, int cx, int cy);
	afx_msg void OnMove(int x, int y);
	afx_msg void OnViewLevel();
	afx_msg void OnViewLocation();
	afx_msg void OnHelp();
	afx_msg void OnSelchangeLevelList();
	afx_msg void OnSelchangeLocationList();
	afx_msg void OnViewElement();
	//}}AFX_MSG
	DECLARE_MESSAGE_MAP()
	afx_msg int OnDblClkLevel(WPARAM wParam, LPARAM lParam);
	afx_msg int OnSelectLevel(WPARAM wParam, LPARAM lParam);
	
	CToolTipCtrl m_ToolTip;
	CString m_ToolTipText;

	afx_msg void OnViewSection();
	afx_msg void OnViewAisle();
	afx_msg void OnViewBay();
	afx_msg void OnViewFacility();
	afx_msg void OnViewSide();

private:
	void BuildLocationList();
	int LoadBay();
	void BuildLevelList();
};

//{{AFX_INSERT_LOCATION}}
// Microsoft Visual C++ will insert additional declarations immediately before the previous line.

#endif // !defined(AFX_LEVELLOCATIONDIALOG_H__7F951C22_2091_485B_80CB_6A76A4823EB8__INCLUDED_)

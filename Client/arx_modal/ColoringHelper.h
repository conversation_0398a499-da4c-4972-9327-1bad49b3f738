// ColoringHelper.h: interface for the CColoringHelper class.
//
//////////////////////////////////////////////////////////////////////

#if !defined(AFX_COLORINGHELPER_H__63D95C37_1ED1_47EA_AE4B_4B2FA60B8D85__INCLUDED_)
#define AFX_COLORINGHELPER_H__63D95C37_1ED1_47EA_AE4B_4B2FA60B8D85__INCLUDED_

#if _MSC_VER > 1000
#pragma once
#endif // _MSC_VER > 1000

#include "ColorModelDlg.h"
#include "DisplayResults.h"

class CColoringHelper  
{
public:
	CColoringHelper();
	virtual ~CColoringHelper();

	void ColorByHandle();
	void ColorAisle();
	void ColorByProfile();
	void ColorModel();
	void ColorProductGroup();
	void ColorProduct();

	int DoColorModal(CColorModelDlg &dlg, CStringArray &ranges);
	int findElem(CString elem_key, CArray <CString, CString&> &acadArray);
	void SortArraysByValueDes(CArray <CString, CString&> &acadArray,
										   CArray <float, float&> &valueArray);
	int GetIndexFromColor(CString strColor);

	typedef enum {
		TrueScale,
		Percentage,
		Range,
		Reset
	} enumColorModelMode;

	typedef struct {
		CString handle;
		CString value;
		int colorIndex;
	} structColor;
	
	typedef enum {
		HighBright,
		LowBright
	} enumDirection;

	typedef enum {
		Sum,
		Min,
		Max,
		Avg
	} enumMultiType;

	CMapStringToString m_MinResultsMap;
	CMapStringToString m_MaxResultsMap;

	CDisplayResults *m_pColorResults;

};

#endif // !defined(AFX_COLORINGHELPER_H__63D95C37_1ED1_47EA_AE4B_4B2FA60B8D85__INCLUDED_)

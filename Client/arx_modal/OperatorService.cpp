// OperatorService.cpp: implementation of the COperatorService class.
//
//////////////////////////////////////////////////////////////////////

#include "stdafx.h"
#include "modal.h"
#include "OperatorService.h"

#ifdef _DEBUG
#undef THIS_FILE
static char THIS_FILE[]=__FILE__;
#define new DEBUG_NEW
#endif

//////////////////////////////////////////////////////////////////////
// Construction/Destruction
//////////////////////////////////////////////////////////////////////

COperatorService::COperatorService()
{
	COperator *pOperator;

	// store everything in all lower case for lookup
	m_OperatorList.Add(new COperator("=", "=", 1));
	m_OperatorList.Add(new COperator("Equal", "=", 1));
	m_OperatorList.Add(new COperator("Equal to", "=", 1));
	m_OperatorList.Add(new COperator("Not equal to", "!=", 1));
	m_OperatorList.Add(new COperator("!=", "!=", 1));
	m_OperatorList.Add(new COperator("<>", "!=", 1));
	m_OperatorList.Add(new COperator("Less than", "<", 1));
	m_OperatorList.Add(new COperator("<", "<", 1));
	m_OperatorList.Add(new COperator(">", ">", 1));
	m_OperatorList.Add(new COperator("Greater than", ">", 1));
	m_OperatorList.Add(new COperator("Between", "between", 2));
	m_OperatorList.Add(new COperator("Not between", "not between", 2));
	m_OperatorList.Add(new COperator("Exists", "exists", 0));
	m_OperatorList.Add(new COperator("Not exists", "not exists", 0));
	m_OperatorList.Add(new COperator("<=", "<=", 1));
	m_OperatorList.Add(new COperator("Less than or equal to", "<=", 1));
	m_OperatorList.Add(new COperator(">=", ">=", 1));
	m_OperatorList.Add(new COperator("Greater than or equal to", ">=", 1));
	m_OperatorList.Add(new COperator("In", "in", 3));
	m_OperatorList.Add(new COperator("Not in", "not in", 3));
	m_OperatorList.Add(new COperator("Not equal", "!=", 1));
	m_OperatorList.Add(new COperator("One of", "in", 3));
	m_OperatorList.Add(new COperator("Not one of", "not in", 3));
	m_OperatorList.Add(new COperator("Like", "like", 1));
	m_OperatorList.Add(new COperator("Not Like", "not like", 1));
	
	for (int i=0; i < m_OperatorList.GetSize(); ++i) {
		m_DisplayToInternalMap.SetAt(m_OperatorList[i]->m_Display, m_OperatorList[i]);
	}
	
	// These are the ones we will pull into list boxes
	m_DisplayToInternalMap.Lookup("Equal to", (CObject *&)pOperator);
	m_OperatorDisplayList.Add(pOperator);
	m_InternalToDisplayMap.SetAt(pOperator->m_Internal, pOperator);

	m_DisplayToInternalMap.Lookup("Not equal to", (CObject *&)pOperator);
	m_OperatorDisplayList.Add(pOperator);
	m_InternalToDisplayMap.SetAt(pOperator->m_Internal, pOperator);

	m_DisplayToInternalMap.Lookup("Like", (CObject *&)pOperator);
	m_OperatorDisplayList.Add(pOperator);
	m_InternalToDisplayMap.SetAt(pOperator->m_Internal, pOperator);
	
	m_DisplayToInternalMap.Lookup("Not Like", (CObject *&)pOperator);
	m_OperatorDisplayList.Add(pOperator);
	m_InternalToDisplayMap.SetAt(pOperator->m_Internal, pOperator);

	m_DisplayToInternalMap.Lookup("Less than", (CObject *&)pOperator);
	m_OperatorDisplayList.Add(pOperator);
	m_InternalToDisplayMap.SetAt(pOperator->m_Internal, pOperator);

	m_DisplayToInternalMap.Lookup("Less than or equal to", (CObject *&)pOperator);
	m_OperatorDisplayList.Add(pOperator);
	m_InternalToDisplayMap.SetAt(pOperator->m_Internal, pOperator);

	m_DisplayToInternalMap.Lookup("Greater than", (CObject *&)pOperator);
	m_OperatorDisplayList.Add(pOperator);
	m_InternalToDisplayMap.SetAt(pOperator->m_Internal, pOperator);

	m_DisplayToInternalMap.Lookup("Greater than or equal to", (CObject *&)pOperator);
	m_OperatorDisplayList.Add(pOperator);
	m_InternalToDisplayMap.SetAt(pOperator->m_Internal, pOperator);

	m_DisplayToInternalMap.Lookup("Between", (CObject *&)pOperator);
	m_OperatorDisplayList.Add(pOperator);
	m_InternalToDisplayMap.SetAt(pOperator->m_Internal, pOperator);

	m_DisplayToInternalMap.Lookup("Not between", (CObject *&)pOperator);
	m_OperatorDisplayList.Add(pOperator);
	m_InternalToDisplayMap.SetAt(pOperator->m_Internal, pOperator);
	
	m_DisplayToInternalMap.Lookup("One of", (CObject *&)pOperator);
	m_OperatorDisplayList.Add(pOperator);
	m_InternalToDisplayMap.SetAt(pOperator->m_Internal, pOperator);
	
	m_DisplayToInternalMap.Lookup("Not one of", (CObject *&)pOperator);
	m_OperatorDisplayList.Add(pOperator);
	m_InternalToDisplayMap.SetAt(pOperator->m_Internal, pOperator);
}

COperatorService::~COperatorService()
{
	for (int i=0; i < m_OperatorList.GetSize(); ++i)
		delete m_OperatorList[i];


}

COperator* COperatorService::ConvertInternalToDisplay(const CString &internal)
{
	CString temp = internal;
	COperator *display;
//	temp.MakeLower();

	if (! m_InternalToDisplayMap.Lookup(temp, (CObject *&)display))
		return NULL;

	return display;
}

COperator* COperatorService::ConvertDisplayToInternal(const CString &display)
{	
	CString temp = display;
	COperator *internal;
//	temp.MakeLower();

	if (! m_DisplayToInternalMap.Lookup(temp, (CObject *&)internal))
		return NULL;

	return internal;
}

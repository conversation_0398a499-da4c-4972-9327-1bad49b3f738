// BayProfileCrossbarInfo.h: interface for the CBayProfileCrossbarInfo class.
//
//////////////////////////////////////////////////////////////////////

#if !defined(AFX_BAYPROFILECROSSBARINFO_H__F40541EE_215A_4479_97A9_FB6E61694663__INCLUDED_)
#define AFX_BAYPROFILECROSSBARINFO_H__F40541EE_215A_4479_97A9_FB6E61694663__INCLUDED_

#if _MSC_VER > 1000
#pragma once
#endif // _MSC_VER > 1000

class CBayProfileCrossbarInfo : public CObject  
{
public:
	CBayProfileCrossbarInfo();
	CBayProfileCrossbarInfo(const CBayProfileCrossbarInfo& other);
	CBayProfileCrossbarInfo& operator=(const CBayProfileCrossbarInfo& other);
	virtual ~CBayProfileCrossbarInfo();
	
	double m_Height;
	double m_Thickness;
	BOOL m_IsHidden;
	BOOL m_IsSelected;
	int m_LocationCount;
	double m_LocationSpace;
	double m_Clearance;	
	double m_MinimumWidth;
	int m_LocationRowCount;
};

#endif // !defined(AFX_BAYPROFILECROSSBARINFO_H__F40541EE_215A_4479_97A9_FB6E61694663__INCLUDED_)

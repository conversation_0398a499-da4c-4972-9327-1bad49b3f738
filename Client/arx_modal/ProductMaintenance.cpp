// ProductMaintenance.cpp : implementation file
//

#include "stdafx.h"
#include "ProductMaintenance.h"
#include "UDF.h"
#include "UDFPage.h"
#include "Constants.h"
#include "DisplayResults.h"
#include "BayProfile.h"
#include "ssa_exception.h"
#include "ProductGroupDataService.h"
#include "HelpService.h"
#include "UtilityHelper.h"
#include "AutoCADCommands.h"
#include "FacilityDataService.h"
#include "WizardHelper.h"
#include "ControlService.h"

#include <aced.h>
#include <actrans.h>

#ifdef _DEBUG
#define new DEBUG_NEW
#undef THIS_FILE
static char THIS_FILE[] = __FILE__;
#endif

extern CHelpService helpService;
extern CUtilityHelper utilityHelper;
extern CFacilityDataService facilityDataService;
extern CControlService controlService;

/////////////////////////////////////////////////////////////////////////////
// ProductSheet

/////////////////////////////////////////////////////////////////////////////
// ProductSheet message handlers
/////////////////////////////////////////////////////////////////////////////
// CProductSheet

IMPLEMENT_DYNAMIC(CProductSheet, CPropertySheet)

CProductSheet::CProductSheet(UINT nIDCaption, CWnd* pParentWnd, UINT iSelectPage)
	:CPropertySheet(nIDCaption, pParentWnd, iSelectPage)
{
	m_UpdateButton = NULL;
	m_DeleteButton = NULL;
	m_ClearButton = NULL;
	m_ProductListDialog = NULL;
	m_Products.RemoveAll();
	m_CurrentProductIdx = -1;
	m_DisplayProductID = -1;
	m_AllowUpdate = TRUE;
}

CProductSheet::CProductSheet(LPCTSTR pszCaption, CWnd* pParentWnd, UINT iSelectPage)
	:CPropertySheet(pszCaption, pParentWnd, iSelectPage)
{
	m_UpdateButton = NULL;
	m_DeleteButton = NULL;
	m_ClearButton = NULL;
	m_ProductListDialog = NULL;
	m_Products.RemoveAll();
	m_CurrentProductIdx = -1;
	m_AllowUpdate = TRUE;
	m_QueryOnly = FALSE;
	m_DisplayProductID = -1;
}

CProductSheet::~CProductSheet()
{
	if (m_UpdateButton != NULL)
		delete m_UpdateButton;

	if (m_DeleteButton != NULL)
		delete m_DeleteButton;

	if (m_ClearButton != NULL)
		delete m_ClearButton;

	for (int i=0; i < m_Products.GetSize(); ++i) {
		delete m_Products[i];
	}

	if (m_ProductListDialog != NULL) {
		/*
		CString saveRect;

		if (m_DisplayListRect.Width() > 0 && m_DisplayListRect.Height() > 0) {
			saveRect.Format("%d|%d|%d|%d|", 
				m_DisplayListRect.top, m_DisplayListRect.bottom, m_DisplayListRect.left, m_DisplayListRect.right);
			SetApplicationData("ListRect", saveRect, "Dialogs\\ProductMaintenance");
		}
		
		if (m_MainWindowRect.Width() > 0 && m_MainWindowRect.Height() > 0) {
				saveRect.Format("%d|%d|%d|%d|", 
					m_MainWindowRect.top, m_MainWindowRect.bottom, m_MainWindowRect.left, m_MainWindowRect.right);
			SetApplicationData("MainRect", saveRect, "Dialogs\\ProductMaintenance");
		}
		*/

		m_ProductListDialog->DestroyWindow();
	}

}


BEGIN_MESSAGE_MAP(CProductSheet, CPropertySheet)
	ON_MESSAGE(WM_CHANGESELECTION, (LRESULT (AFX_MSG_CALL CWnd::*)(WPARAM, LPARAM)) OnChangeSelection)
	ON_MESSAGE(WM_CLOSEDISPLAY, (LRESULT (AFX_MSG_CALL CWnd::*)(WPARAM, LPARAM)) OnCloseProductListDialog)
	ON_MESSAGE(WM_DISPLAY_RESULTS_SIZE, (LRESULT (AFX_MSG_CALL CWnd::*)(WPARAM, LPARAM)) OnSizeProductListDialog)
	ON_MESSAGE(WM_DISPLAY_RESULTS_BUTTON1, (LRESULT (AFX_MSG_CALL CWnd::*)(WPARAM, LPARAM)) OnColorProducts)
	//{{AFX_MSG_MAP(CProductSheet)
	ON_WM_HELPINFO()
	ON_WM_MOVE()
	//}}AFX_MSG_MAP
END_MESSAGE_MAP()

/////////////////////////////////////////////////////////////////////////////
// CProductSheet message handlers
/////////////////////////////////////////////////////////////////////////////
// CProductPage property page

IMPLEMENT_DYNCREATE(CProductPage, CPropertyPage)

CProductPage::CProductPage() : CPropertyPage(CProductPage::IDD)
{
	//{{AFX_DATA_INIT(CProductPage)
	m_Description = _T("");
	m_WMSProductDetailID = _T("");
	m_WMSProductID = _T("");
	m_CasePack = _T("");
	m_EachHeight = _T("");
	m_EachLength = _T("");
	m_EachWidth = _T("");
	m_InnerHeight = _T("");
	m_InnerLength = _T("");
	m_InnerPack = _T("");
	m_InnerWidth = _T("");
	m_MaxStackNumber = _T("");
	m_Movement = _T("");
	m_NumberOfHits = _T("");
	m_Weight = _T("");
	m_BalanceOnHand = _T("");
	m_CaseHeight = _T("");
	m_CaseLength = _T("");
	m_CaseWidth = _T("");
	m_IsAssignmentLocked = 2;
	m_IsHazard = 2;
	m_IsPickToBelt = 2;
	m_RotateXAxis = 2;
	m_RotateYAxis = 2;
	m_RotateZAxis = 2;
	m_Test = 0.0f;
	m_Active = 2;
	m_PreviousBOH = _T("");
	m_PreviousMovement = _T("");
	m_Trace = 2;
	m_CommodityType = _T("");
	m_CrushFactor = _T("");
	//}}AFX_DATA_INIT
}

CProductPage::~CProductPage()
{
}

void CProductPage::DoDataExchange(CDataExchange* pDX)
{
	int x = IDC_HEIGHT;

	CPropertyPage::DoDataExchange(pDX);
	//{{AFX_DATA_MAP(CProductPage)
	DDX_Control(pDX, IDC_UNITOFISSUE, m_UnitOfIssue);
	DDX_Control(pDX, IDC_OPTIMIZEBY, m_OptimizeBy);
	DDX_Text(pDX, IDC_DESCRIPTION, m_Description);
	DDV_MaxChars(pDX, m_Description, 255);
	DDX_Text(pDX, IDC_WMSPRODUCTDETAILID, m_WMSProductDetailID);
	DDX_Text(pDX, IDC_WMSPRODUCTID, m_WMSProductID);
	DDX_Text(pDX, IDC_CASEPACK, m_CasePack);
	DDX_Text(pDX, IDC_EACHHEIGHT, m_EachHeight);
	DDX_Text(pDX, IDC_EACHLENGTH, m_EachLength);
	DDX_Text(pDX, IDC_EACHWIDTH, m_EachWidth);
	DDX_Text(pDX, IDC_INNERHEIGHT, m_InnerHeight);
	DDX_Text(pDX, IDC_INNERLENGTH, m_InnerLength);
	DDX_Text(pDX, IDC_INNERPACK, m_InnerPack);
	DDX_Text(pDX, IDC_INNERWIDTH, m_InnerWidth);
	DDX_Text(pDX, IDC_MAXSTACKNUMBER, m_MaxStackNumber);
	DDX_Text(pDX, IDC_MOVEMENT, m_Movement);
	DDX_Text(pDX, IDC_NUMBEROFHITS, m_NumberOfHits);
	DDX_Text(pDX, IDC_WEIGHT, m_Weight);
	DDX_Text(pDX, IDC_BALANCEONHAND, m_BalanceOnHand);
	DDX_Text(pDX, IDC_HEIGHT, m_CaseHeight);
	DDX_Text(pDX, IDC_LENGTH, m_CaseLength);
	DDX_Text(pDX, IDC_WIDTH, m_CaseWidth);
	DDX_Check(pDX, IDC_ISASSIGNMENTLOCKED, m_IsAssignmentLocked);
	DDX_Check(pDX, IDC_ISHAZARD, m_IsHazard);
	DDX_Check(pDX, IDC_ISPICKTOBELT, m_IsPickToBelt);
	DDX_Check(pDX, IDC_ROTATEXAXIS, m_RotateXAxis);
	DDX_Check(pDX, IDC_ROTATEYAXIS, m_RotateYAxis);
	DDX_Check(pDX, IDC_ROTATEZAXIS, m_RotateZAxis);
	DDX_Check(pDX, IDC_ACTIVE, m_Active);
	DDX_Text(pDX, IDC_PREVIOUS_BOH, m_PreviousBOH);
	DDX_Text(pDX, IDC_PREVIOUS_MOVEMENT, m_PreviousMovement);
	DDX_Check(pDX, IDC_TRACE, m_Trace);
	DDX_Text(pDX, IDC_COMMODITY_TYPE, m_CommodityType);
	DDX_Text(pDX, IDC_CRUSH_FACTOR, m_CrushFactor);
	//}}AFX_DATA_MAP
}


BEGIN_MESSAGE_MAP(CProductPage, CPropertyPage)
	//{{AFX_MSG_MAP(CProductPage)
	ON_WM_HELPINFO()
	//}}AFX_MSG_MAP
END_MESSAGE_MAP()

/////////////////////////////////////////////////////////////////////////////
// CProductPage message handlers
/////////////////////////////////////////////////////////////////////////////
/////////////////////////////////////////////////////////////////////////////
// CProductContainerPage property page

IMPLEMENT_DYNCREATE(CProductContainerPage, CPropertyPage)

CProductContainerPage::CProductContainerPage() : CPropertyPage(CProductContainerPage::IDD)
{
	//{{AFX_DATA_INIT(CProductContainerPage)
	m_ContainerHeight = _T("");
	m_ContainerLength = _T("");
	m_ContainerWidth = _T("");
	m_Hi = _T("");
	m_Ti = _T("");
	m_IsHeightOverride = 2;
	m_NumberInPallet = _T("");
	//}}AFX_DATA_INIT

}

CProductContainerPage::~CProductContainerPage()
{
}

void CProductContainerPage::DoDataExchange(CDataExchange* pDX)
{
	CPropertyPage::DoDataExchange(pDX);
	//{{AFX_DATA_MAP(CProductContainerPage)
	DDX_Text(pDX, IDC_CONTAINER_HEIGHT, m_ContainerHeight);
	DDX_Text(pDX, IDC_CONTAINER_LENGTH, m_ContainerLength);
	DDX_Text(pDX, IDC_CONTAINER_WIDTH, m_ContainerWidth);
	DDX_Text(pDX, IDC_HI, m_Hi);
	DDX_Text(pDX, IDC_TI, m_Ti);
	DDX_Check(pDX, IDC_ISHEIGHTOVERRIDE, m_IsHeightOverride);
	DDX_Text(pDX, IDC_NUMBERINPALLET, m_NumberInPallet);
	//}}AFX_DATA_MAP
}


BEGIN_MESSAGE_MAP(CProductContainerPage, CPropertyPage)
	//{{AFX_MSG_MAP(CProductContainerPage)
	ON_WM_HELPINFO()
	//}}AFX_MSG_MAP
END_MESSAGE_MAP()

/////////////////////////////////////////////////////////////////////////////
// CProductContainerPage message handlers

BOOL CProductSheet::OnInitDialog() 
{
	BOOL bResult = CPropertySheet::OnInitDialog();
	CRect r, parentRect;
	CUDF *pUDF;
	CStringArray udfList;
	CUDFPage *page;
	int rc;
	CString savedRect;
	CRect mainRect;
	CStringArray strings;

	CButton *pButton = (CButton *)GetDlgItem(IDOK);
	pButton->SetWindowText("Query");
	if (m_DisplayProductID <= 0) {
		pButton->ShowWindow(SW_SHOW);
	}
	else {
		pButton->EnableWindow(FALSE);
		//pButton->ShowWindow(SW_HIDE);
	}

	pButton->GetWindowRect(&r);
	CFont *font = pButton->GetFont();
	this->ScreenToClient(&r);

	if (! m_QueryOnly) {
		m_UpdateButton = new CButton;
		m_UpdateButton->Create("Update", WS_CHILD & ~(BS_DEFPUSHBUTTON), r, this, WM_USER+1);
		m_UpdateButton->SetFont(font);
		m_UpdateButton->MoveWindow(10, r.top, r.Width(), r.Height(), FALSE);
		m_UpdateButton->EnableWindow(m_AllowUpdate ? SW_SHOW : SW_HIDE);
		m_UpdateButton->ShowWindow(SW_SHOW);
		if (m_DisplayProductID > 0)
			m_UpdateButton->SetButtonStyle(BS_DEFPUSHBUTTON, FALSE);
		
		m_DeleteButton = new CButton;
		m_DeleteButton->Create("Delete", WS_CHILD & ~(BS_DEFPUSHBUTTON), r, this, WM_USER+2);
		m_DeleteButton->SetFont(font);
		m_DeleteButton->MoveWindow(10+r.Width()+10, r.top, r.Width(), r.Height(), FALSE);
		m_DeleteButton->ShowWindow(SW_SHOW);
		m_DeleteButton->EnableWindow(m_AllowUpdate ? SW_SHOW : SW_HIDE);
	}
	
	m_ClearButton = new CButton;
	m_ClearButton->Create("Clear", WS_CHILD & ~(BS_DEFPUSHBUTTON), r, this, WM_USER+3);
	m_ClearButton->SetFont(font);
	m_ClearButton->MoveWindow(10+(r.Width()*2)+20, r.top, r.Width(), r.Height(), FALSE);
	m_ClearButton->EnableWindow((m_DisplayProductID > 0) ? FALSE : TRUE);
	m_ClearButton->ShowWindow(SW_SHOW);
	



	page = (CUDFPage *)this->GetPage(2);

	try {
		// This will load the attributes including the UDF's, which is
		// really all this program needs since the attributes are
		// hard-coded to make the screen design better
		rc = m_ProductDataService.LoadProductAttributes();
	}
	catch (Ssa_Exception e) {
		utilityHelper.ProcessError("Error getting product attributes", &e);
	}
	catch (...) {
		utilityHelper.ProcessError("Error getting product attributes");
	}

	// Everyone is in charge of deleting their own UDFs
	for (int i=0; i < m_ProductDataService.m_UDFList.GetSize(); ++i) {
		pUDF = new CUDF;
		*pUDF = *(m_ProductDataService.m_UDFList[i]);
		page->m_UDFs.Add(pUDF);
		pUDF = new CUDF;
		*pUDF = *(m_ProductDataService.m_UDFList[i]);
		m_CurrentProduct.m_UDFList.Add(pUDF);
	}

	if (m_DisplayProductID > 0) {
		CProductPack *product = new CProductPack;
		product->m_IsProxy = TRUE;
		product->m_ProductPackDBID = m_DisplayProductID;
		m_Products.Add(product);
		OnChangeSelection(0, 0);
	}

	return bResult;
}

BOOL CProductSheet::OnCommand(WPARAM wParam, LPARAM lParam) 
{

	if (HIWORD(wParam) == BN_CLICKED) {
		if (LOWORD(wParam) == IDOK) {
			OnQuery();
			return TRUE;
		}
		else if (LOWORD(wParam) == WM_USER+1) {
			OnUpdate();
		}
		else if (LOWORD(wParam) == WM_USER+2) {
			OnDelete();
		}
		else if (LOWORD(wParam) == WM_USER+3) {
			OnClear();
			return TRUE;
		}
		else if (LOWORD(wParam) == IDCANCEL) {
			//AfxMessageBox("Cancel clicked");
		}
		else if (LOWORD(wParam) == IDHELP) {
			//AfxMessageBox("Help clicked");
		}
	}
	else if (HIWORD(wParam) == WM_USER) {
		//AfxMessageBox("got here");
	}


	return CPropertySheet::OnCommand(wParam, lParam);
}

void CProductSheet::OnQuery()
{
	qqhSLOTQuery query;
	CProductPage *productPage = (CProductPage *)this->GetPage(0);
	CProductContainerPage *containerPage = (CProductContainerPage *)this->GetPage(1);
	CUDFPage *udfPage = (CUDFPage *)this->GetPage(2);
	CProductOptimizePage *optimizePage = (CProductOptimizePage *)this->GetPage(3);
	CUDF *pUDF;
	CString tmp;
	int i;

	query.setObjName(TB_PRODUCT);

	GetActivePage()->UpdateData(TRUE);

	m_ProductDataService.AddQueryAttribute(query, TB_PRODUCT, "Description", productPage->m_Description, DT_STRING);
	m_ProductDataService.AddQueryAttribute(query, TB_PRODUCT, "CasePack", productPage->m_CasePack, DT_INT);
	m_ProductDataService.AddQueryAttribute(query, TB_PRODUCT, "InnerPack", productPage->m_InnerPack, DT_INT);

	if (productPage->m_UnitOfIssue.GetCurSel() >= 0) {
		tmp.Format("%d", productPage->m_UnitOfIssue.GetCurSel());
		m_ProductDataService.AddQueryAttribute(query, TB_PRODUCT, "UnitOfIssue", tmp, DT_INT);
	}
	m_ProductDataService.AddQueryAttribute(query, TB_PRODUCT, "WMSProductDetailID", productPage->m_WMSProductDetailID, DT_STRING);
	m_ProductDataService.AddQueryAttribute(query, TB_PRODUCT, "WMSProductID", productPage->m_WMSProductID, DT_STRING);
	
	if (productPage->m_IsAssignmentLocked != 2) {
		tmp.Format("%d", productPage->m_IsAssignmentLocked);
		m_ProductDataService.AddQueryAttribute(query, TB_PRODUCT, "IsAssignmentLocked", tmp, DT_INT);
	}

	m_ProductDataService.AddQueryAttribute(query, TB_PRODUCT, "Movement", productPage->m_Movement, DT_FLOAT);
	m_ProductDataService.AddQueryAttribute(query, TB_PRODUCT, "BalanceOnHand", productPage->m_BalanceOnHand, DT_FLOAT);
	m_ProductDataService.AddQueryAttribute(query, TB_PRODUCT, "NumberOfHits", productPage->m_NumberOfHits, DT_FLOAT);

	m_ProductDataService.AddQueryAttribute(query, TB_PRODUCT, "CommodityType", productPage->m_CommodityType, DT_STRING);
	m_ProductDataService.AddQueryAttribute(query, TB_PRODUCT, "CrushFactor", productPage->m_CrushFactor, DT_STRING);
	m_ProductDataService.AddQueryAttribute(query, TB_PRODUCT, "PreviousMovement", productPage->m_PreviousMovement, DT_FLOAT);
	//m_ProductDataService.AddQueryAttribute(query, TB_PRODUCT, "PreviousBOH", productPage->m_PreviousBOH, DT_FLOAT);


	if (productPage->m_OptimizeBy.GetCurSel() >= 0) {
		tmp.Format("%d", productPage->m_OptimizeBy.GetCurSel());
		m_ProductDataService.AddQueryAttribute(query, TB_PRODUCT, "OptimizeBy", tmp, DT_INT);
	}
	
	if (productPage->m_IsHazard != 2) {
		tmp.Format("%d", productPage->m_IsHazard);
		m_ProductDataService.AddQueryAttribute(query, TB_PRODUCT, "IsHazard", tmp, DT_INT);
	}

	if (productPage->m_IsHazard != 2) {
		tmp.Format("%d", productPage->m_IsHazard);
		m_ProductDataService.AddQueryAttribute(query, TB_PRODUCT, "IsHazard", tmp, DT_INT);
	}

	if (productPage->m_Active != 2) {
		tmp.Format("%d", productPage->m_Active);
		m_ProductDataService.AddQueryAttribute(query, TB_PRODUCT, "IsActive", tmp, DT_INT);
	}

	if (productPage->m_Trace != 2) {
		tmp.Format("%d", productPage->m_Trace);
		m_ProductDataService.AddQueryAttribute(query, TB_PRODUCT, "Trace", tmp, DT_INT);
	}

	if (productPage->m_IsPickToBelt != 2) {
		tmp.Format("%d", productPage->m_IsPickToBelt);
		m_ProductDataService.AddQueryAttribute(query, TB_PRODUCT, "IsPickToBelt", tmp, DT_INT);
	}

	if (productPage->m_RotateXAxis != 2) {
		tmp.Format("%d", productPage->m_RotateXAxis);
		m_ProductDataService.AddQueryAttribute(query, TB_PRODUCT, "RotateXAxis", tmp, DT_INT);
	}
	
	if (productPage->m_RotateYAxis != 2) {
		tmp.Format("%d", productPage->m_RotateYAxis);
		m_ProductDataService.AddQueryAttribute(query, TB_PRODUCT, "RotateYAxis", tmp, DT_INT);
	}

	if (productPage->m_RotateZAxis != 2) {
		tmp.Format("%d", productPage->m_RotateZAxis);
		m_ProductDataService.AddQueryAttribute(query, TB_PRODUCT, "RotateZAxis", tmp, DT_INT);
	}
				
	m_ProductDataService.AddQueryAttribute(query, TB_PRODUCT, "Width", productPage->m_CaseWidth, DT_FLOAT);
	m_ProductDataService.AddQueryAttribute(query, TB_PRODUCT, "Length", productPage->m_CaseLength, DT_FLOAT);
	m_ProductDataService.AddQueryAttribute(query, TB_PRODUCT, "Height", productPage->m_CaseHeight, DT_FLOAT);
	
	m_ProductDataService.AddQueryAttribute(query, TB_PRODUCT, "EachWidth", productPage->m_EachWidth, DT_FLOAT);
	m_ProductDataService.AddQueryAttribute(query, TB_PRODUCT, "EachLength", productPage->m_EachLength, DT_FLOAT);
	m_ProductDataService.AddQueryAttribute(query, TB_PRODUCT, "EachHeight", productPage->m_EachHeight, DT_FLOAT);
	
	m_ProductDataService.AddQueryAttribute(query, TB_PRODUCT, "InnerWidth", productPage->m_InnerWidth, DT_FLOAT);
	m_ProductDataService.AddQueryAttribute(query, TB_PRODUCT, "InnerLength", productPage->m_InnerLength, DT_FLOAT);
	m_ProductDataService.AddQueryAttribute(query, TB_PRODUCT, "InnerHeight", productPage->m_InnerHeight, DT_FLOAT);
	
	m_ProductDataService.AddQueryAttribute(query, TB_PRODUCT, "MaxStackNumber", productPage->m_MaxStackNumber, DT_INT);
	m_ProductDataService.AddQueryAttribute(query, TB_PRODUCT, "Weight", productPage->m_Weight, DT_FLOAT);

	for (i=0; i < udfPage->m_UDFs.GetSize(); ++i) {
		pUDF = udfPage->m_UDFs[i];
		m_ProductDataService.AddQueryAttribute(query, TB_PRODUCTUDFVAL, pUDF->m_Name, pUDF->m_Value, pUDF->m_Type);
	}
	//m_ProductDataService.AddQueryAttribute(query, TB_PRODUCTCONTAINER, "NumberInPallet", containerPage->m_NumberInPallet, DT_INT);	
	m_ProductDataService.AddQueryAttribute(query, TB_PRODUCTCONTAINER, "Width", containerPage->m_ContainerWidth, DT_FLOAT);
	m_ProductDataService.AddQueryAttribute(query, TB_PRODUCTCONTAINER, "Length", containerPage->m_ContainerLength, DT_FLOAT);
	m_ProductDataService.AddQueryAttribute(query, TB_PRODUCTCONTAINER, "Height", containerPage->m_ContainerHeight, DT_FLOAT);
	m_ProductDataService.AddQueryAttribute(query, TB_PRODUCTCONTAINER, "Ti", containerPage->m_Ti, DT_INT);
	m_ProductDataService.AddQueryAttribute(query, TB_PRODUCTCONTAINER, "Hi", containerPage->m_Hi, DT_INT);


	//if (containerPage->m_IsHeightOverride.GetState() != 2) {
	if (containerPage->m_IsHeightOverride != 2) {
		//tmp.Format("%d", containerPage->m_IsHeightOverride.GetState());
		tmp.Format("%d", containerPage->m_IsHeightOverride);
		m_ProductDataService.AddQueryAttribute(query, TB_PRODUCTCONTAINER, "IsHeightOverride", tmp, DT_INT);
	}

	if (optimizePage->m_ProductGroup.CompareNoCase("Assigned") == 0)
		m_ProductDataService.AddQueryAttribute(query, TB_PRODUCTGROUP, "dbslottinggroupid", " ", "exists");
	else if (optimizePage->m_ProductGroup.CompareNoCase("Not Assigned") == 0)
		m_ProductDataService.AddQueryAttribute(query, TB_PRODUCTGROUP, "dbslottinggroupid", " ", "not exists");
	else if (optimizePage->m_ProductGroup.CompareNoCase("") != 0) {
		tmp.Format("%d", optimizePage->m_ProductGroupDBID);
		m_ProductDataService.AddQueryAttribute(query, TB_PRODUCTGROUP, "dbslottinggroupid", tmp, DT_INT);
	}

	BOOL bUsingLoc = FALSE;
	if (optimizePage->m_Location.CompareNoCase("Assigned") == 0) {
		bUsingLoc = TRUE;
		m_ProductDataService.AddQueryAttribute(query, TB_LOCATION, "dblocationid", " ", "exists");
	}
	else if (optimizePage->m_Location.CompareNoCase("Not Assigned") == 0)
		m_ProductDataService.AddQueryAttribute(query, TB_LOCATION, "dblocationid", " ", "not exists");
	else {
		tmp = optimizePage->m_Location;
		tmp.TrimLeft();
		tmp.TrimRight();
		if (tmp.CompareNoCase("") != 0) {
			bUsingLoc = TRUE;
			m_ProductDataService.AddQueryAttribute(query, TB_LOCATION, "description", tmp, DT_STRING);
		}
		else
			bUsingLoc = TRUE;
	}

	if (bUsingLoc) {
		if (optimizePage->m_LocationProductGroup.CompareNoCase("Assigned") == 0)
			m_ProductDataService.AddQueryAttribute(query, TB_PRODUCTGROUPLOC, "dbslottinggroupid", " ", "exists");
		else if(optimizePage->m_LocationProductGroup.CompareNoCase("Not Assigned") == 0)
			m_ProductDataService.AddQueryAttribute(query, TB_PRODUCTGROUPLOC, "dbslottinggroupid", " ", "not exists");
		else if(optimizePage->m_LocationProductGroup.CompareNoCase("") != 0) {
			tmp.Format("%d", optimizePage->m_LocationProductGroupDBID);
			m_ProductDataService.AddQueryAttribute(query, TB_PRODUCTGROUPLOC, "dbslottinggroupid", tmp, DT_INT);
		}
		
		if (optimizePage->m_BayProfile.CompareNoCase("") != 0) {
			tmp.Format("%d", optimizePage->m_BayProfileDBID);
			m_ProductDataService.AddQueryAttribute(query, TB_BAYPROFILE, "dbbayprofileid", tmp, DT_INT);
		}

		if (optimizePage->m_LevelType.CompareNoCase("") != 0) {
			tmp.Format("%d", CBayProfile::ConvertBayType(optimizePage->m_LevelType));
			m_ProductDataService.AddQueryAttribute(query, TB_LEVELPROFILE, "baytype", tmp, DT_INT);
		}
	}

	BOOL bUsingLocOpt = FALSE;
	if (optimizePage->m_OptimizedLocation.CompareNoCase("Assigned") == 0) {
		bUsingLocOpt = TRUE;
		m_ProductDataService.AddQueryAttribute(query, TB_LOCATION_OPT, "dblocationid", " ", "exists");
	}
	else if (optimizePage->m_OptimizedLocation.CompareNoCase("Not Assigned") == 0)
		m_ProductDataService.AddQueryAttribute(query, TB_LOCATION_OPT, "dblocationid", " ", "not exists");
	else {
		tmp = optimizePage->m_OptimizedLocation;
		tmp.TrimLeft();
		tmp.TrimRight();
		if (tmp.CompareNoCase("") != 0) {
			bUsingLoc = TRUE;
			m_ProductDataService.AddQueryAttribute(query, TB_LOCATION_OPT, "description", tmp, DT_STRING);
		}
		else
			bUsingLocOpt = TRUE;
	}

	if (bUsingLocOpt) {
		if (optimizePage->m_OptimizedLocationProductGroup.CompareNoCase("Assigned") == 0)
			m_ProductDataService.AddQueryAttribute(query, TB_PRODUCTGROUPLOC_OPT, "dbslottinggroupid", " ", "exists");
		else if(optimizePage->m_OptimizedLocationProductGroup.CompareNoCase("Not Assigned") == 0)
			m_ProductDataService.AddQueryAttribute(query, TB_PRODUCTGROUPLOC_OPT, "dbslottinggroupid", " ", "not exists");
		else if(optimizePage->m_OptimizedLocationProductGroup.CompareNoCase("") != 0) {
			tmp.Format("%d", optimizePage->m_OptimizedLocationProductGroupDBID);
			m_ProductDataService.AddQueryAttribute(query, TB_PRODUCTGROUPLOC_OPT, "dbslottinggroupid", tmp, DT_INT);
		}
		
		if (optimizePage->m_OptimizedBayProfile.CompareNoCase("") != 0) {
			tmp.Format("%d", optimizePage->m_OptimizedBayProfileDBID);
			m_ProductDataService.AddQueryAttribute(query, TB_BAYPROFILE_OPT, "dbbayprofileid", tmp, DT_INT);
		}

		if (optimizePage->m_OptimizedLevelType.CompareNoCase("") != 0) {
			tmp.Format("%d", CBayProfile::ConvertBayType(optimizePage->m_OptimizedLevelType));
			m_ProductDataService.AddQueryAttribute(query, TB_LEVELPROFILE_OPT, "baytype", tmp, DT_INT);
		}

		if (optimizePage->m_RotatedWidth.CompareNoCase("") != 0) {
			m_ProductDataService.AddQueryAttribute(query, TB_SOLUTION_OPT, "rotatedwidth", optimizePage->m_RotatedWidth, DT_FLOAT);
		}
		
		if (optimizePage->m_RotatedLength.CompareNoCase("") != 0) {
			m_ProductDataService.AddQueryAttribute(query, TB_SOLUTION_OPT, "rotatedlength", optimizePage->m_RotatedLength, DT_FLOAT);
		}
		
		if (optimizePage->m_RotatedHeight.CompareNoCase("") != 0) {
			m_ProductDataService.AddQueryAttribute(query, TB_SOLUTION_OPT, "rotatedheight", optimizePage->m_RotatedHeight, DT_FLOAT);
		}
	}

	int rc;
	CStringArray productList;
	
	try {
		CWaitCursor cwc;
		rc = m_ProductDataService.GetProductListByQuery(query, productList);
	}
	catch(Ssa_Exception e) {
		utilityHelper.ProcessError("Error querying for products.", &e);
		return;
	}
	catch(...) {
		utilityHelper.ProcessError("Error querying for products.");
		return;
	}

	if (productList.GetSize() <= 0) {
		AfxMessageBox("No products were found that match the query criteria.");
		return;
	}
	
	if (productList.GetSize() > 1) {
		tmp.Format("%d product%s selected.", productList.GetSize(), productList.GetSize() == 1 ? "" : "s");	
		AfxMessageBox(tmp);
	}

	// Delete the products from the previous query
	for (i=0; i < m_Products.GetSize(); ++i)
		delete m_Products[i];

	m_Products.RemoveAll();

	if (m_QueryOnly) {
		for (int i=0; i < productList.GetSize(); ++i) {
			CProductPack *pProduct = new CProductPack;
			pProduct->ParseAll(productList[i]);
			
			m_Products.Add(pProduct);
		}
		EndDialog(IDOK);
		return;
	}


	if (productList.GetSize() > 1) {
		CWaitCursor cwc;


		BOOL bNewDialog = FALSE;

		if (m_ProductListDialog == NULL) {
			bNewDialog = TRUE;
			m_ProductListDialog = new CDisplayResults;
			m_ProductListDialog->m_IsModeless = TRUE;
			m_ProductListDialog->m_Headers.Add("");
			m_ProductListDialog->m_Tabs.Add("Selected Products");
			m_ProductListDialog->m_WindowCaptions.Add("Product Maintenance");
			m_ProductListDialog->m_OrigColumnSize = 150;
			m_ProductListDialog->m_MessageReceiver = this;
			m_ProductListDialog->m_MainHelpTopic = "ProductMaintenanceResults_Main";
			m_ProductListDialog->m_HelpTopics.Add("ProductMaintenanceResults_Main");
			m_ProductListDialog->m_AllowMultipleSelections = TRUE;
			m_ProductListDialog->m_NextCaption = "Color Products";
			m_ProductListDialog->m_NextClosesWindow = FALSE;
			m_ProductListDialog->m_NextHelp1 = "ProductMaintenanceResults_Color";
		}

		m_ProductListDialog->m_Data.RemoveAll();

		CProductPack *pProduct;

		BuildHeaderLine(m_ProductListDialog->m_Headers[0]);
		
		for (i=0; i < productList.GetSize(); ++i) {
			pProduct = new CProductPack;
			pProduct->ParseAll(productList[i]);
			m_Products.Add(pProduct);
			BuildDisplayLine(tmp, *pProduct);
			m_ProductListDialog->m_Data.Add(tmp);
		}

		
		CRect rect;
		if (bNewDialog) {
			m_ProductListDialog->Create(IDD_DISPLAY_RESULTS, NULL);
			
			CRect mainR, dispR, acadR, deskR;

			this->GetWindowRect(&mainR);
			m_ProductListDialog->GetWindowRect(&dispR);
			utilityHelper.GetParentWindow()->GetWindowRect(&acadR);
			GetDesktopWindow()->GetWindowRect(&deskR);
			
			CPoint acadCenter, deskCenter;
			acadCenter.x = acadR.left + acadR.Width()/2;
			acadCenter.y = acadR.top + acadR.Height()/2;
			
			deskCenter.x = deskR.left + deskR.Width()/2;
			deskCenter.y = deskR.top + deskR.Height()/2;

			if (mainR.Width() * 2 < acadR.Width()) {
				int w = mainR.Width();
				mainR.left = acadCenter.x - mainR.Width();
				mainR.right = mainR.left + w;

				dispR.left = mainR.right+1;
				dispR.right = dispR.left + w;
				dispR.top = mainR.top;
				dispR.bottom = mainR.bottom;
			}
			else if (mainR.Width()*2 < deskR.Width()) {
				int w = mainR.Width();
				mainR.left = deskCenter.x - mainR.Width();
				mainR.right = mainR.left + w;

				dispR.left = mainR.right;
				dispR.right = dispR.left + w;
				dispR.top = mainR.top;
				dispR.bottom = mainR.bottom;
			}
			else {
				int w = mainR.Width();
				mainR.left = deskR.left;
				mainR.right = mainR.left + w;

				dispR.left = mainR.right;
				dispR.right = deskR.right;
				dispR.top = mainR.top;
				dispR.bottom = mainR.bottom;
			}

			this->SetWindowPos(NULL, mainR.left, mainR.top, 0, 0, SWP_NOZORDER|SWP_NOSIZE);
			m_ProductListDialog->SetWindowPos(NULL, dispR.left, dispR.top, dispR.Width(), dispR.Height(), SWP_NOZORDER);
		}
		else
			m_ProductListDialog->ReloadPage();

		m_ProductListDialog->ShowWindow(SW_SHOW);
	}
	else {
		if (m_ProductListDialog != NULL)
			m_ProductListDialog->DestroyWindow();
		CProductPack *pProduct = new CProductPack;
		pProduct->ParseAll(productList[0]);
		m_Products.Add(pProduct);
		m_CurrentProductIdx = 0;
		m_CurrentProduct = *pProduct;
		GetProductByID();
		LoadScreenFromProduct();
	}



}

void CProductSheet::OnUpdate()
{
	CString tmp;
	int rc;

	if (m_CurrentProductIdx < 0) {
		AfxMessageBox("No product has been selected to be updated.");
		return;
	}

	LoadProductFromScreen();
	if (! ValidateUpdate())
		return;

	try {
		rc = m_ProductDataService.Update(m_CurrentProduct);
	}
	catch (Ssa_Exception &e) {
		utilityHelper.ProcessError("Error updating product.", &e);
		return;
	}
	catch (...) {
		utilityHelper.ProcessError("Error updating product.");
		return;
	}
	
	if (rc >= 0) {
		*m_Products[m_CurrentProductIdx] = m_CurrentProduct;
		
		if (m_ProductListDialog != NULL) {
			BuildDisplayLine(&m_CurrentProduct, tmp);
			m_ProductListDialog->ModifyItem(m_CurrentProductIdx, tmp);
		}
		AfxMessageBox("Product successfully updated.");
	}

	return;

}

void CProductSheet::OnDelete()
{
	int rc;

	if (m_CurrentProductIdx < 0) {
		AfxMessageBox("No product has been selected to be deleted.");
		return;
	}

	LoadProductFromScreen();
	
	rc = m_ProductDataService.Delete(m_CurrentProduct.m_ProductPackDBID);

	if (rc >= 0) {
		if (m_ProductListDialog != NULL) {
			m_Products.RemoveAt(m_CurrentProductIdx);
			m_ProductListDialog->DeleteItem(m_CurrentProductIdx);
		}
		OnClear();
		AfxMessageBox("Product successfully deleted.");
		if (m_DisplayProductID > 0)
			OnCancel();
	}

}


void CProductSheet::LoadProductFromScreen()
{
	CProductPage *productPage = (CProductPage *)this->GetPage(0);
	CProductContainerPage *containerPage = (CProductContainerPage *)this->GetPage(1);
	CUDFPage *udfPage = (CUDFPage *)this->GetPage(2);
	CProductOptimizePage *optimizePage = (CProductOptimizePage *)this->GetPage(3);
	CUDF *pUDF;
	CProductContainer container;

	// Product Attributes

	GetActivePage()->UpdateData(TRUE);

	m_CurrentProduct.m_IsAssignmentLocked = productPage->m_IsAssignmentLocked;

	m_CurrentProduct.m_Description = productPage->m_Description;

	if (productPage->m_BalanceOnHand != "")
		m_CurrentProduct.m_BalanceOnHand = (float)atof(productPage->m_BalanceOnHand);

	if (productPage->m_CasePack != "")
		m_CurrentProduct.m_CasePack = atoi(productPage->m_CasePack);

	if (productPage->m_CaseLength != "")
		m_CurrentProduct.m_CaseLength = (float)atof(productPage->m_CaseLength);

	if (productPage->m_EachHeight != "")
		m_CurrentProduct.m_EachHeight = (float)atof(productPage->m_EachHeight);

	if (productPage->m_EachLength != "")
		m_CurrentProduct.m_EachLength = (float)atof(productPage->m_EachLength);

	if (productPage->m_EachWidth != "")
		m_CurrentProduct.m_EachWidth = (float)atof(productPage->m_EachWidth);

	if (productPage->m_CaseHeight != "")
		m_CurrentProduct.m_CaseHeight = (float)atof(productPage->m_CaseHeight);

	if (productPage->m_InnerHeight != "")
		m_CurrentProduct.m_InnerHeight = (float)atof(productPage->m_InnerHeight);

	if (productPage->m_InnerLength != "")
		m_CurrentProduct.m_InnerLength = (float)atof(productPage->m_InnerLength);

	if (productPage->m_InnerPack != "")
		m_CurrentProduct.m_InnerPack = atoi(productPage->m_InnerPack);

	if (productPage->m_InnerWidth != "")
		m_CurrentProduct.m_InnerWidth = (float)atof(productPage->m_InnerWidth);

	m_CurrentProduct.m_IsHazard = productPage->m_IsHazard;

	m_CurrentProduct.m_IsPickToBelt = productPage->m_IsPickToBelt;

	if (productPage->m_MaxStackNumber != "")
		m_CurrentProduct.m_MaxStackNumber = atoi(productPage->m_MaxStackNumber);
	else
		m_CurrentProduct.m_MaxStackNumber = SLOT_NIL_INTEGER;

	if (productPage->m_Movement != "")
		m_CurrentProduct.m_Movement = (float)atof(productPage->m_Movement);

	if (productPage->m_NumberOfHits != "")
		m_CurrentProduct.m_NumberOfHits = (float)atof(productPage->m_NumberOfHits);
	
	if (productPage->m_OptimizeBy.GetCurSel() >= 0)
		m_CurrentProduct.m_OptimizeBy = productPage->m_OptimizeBy.GetCurSel();

	m_CurrentProduct.m_RotateXAxis = productPage->m_RotateXAxis;
	m_CurrentProduct.m_RotateYAxis = productPage->m_RotateYAxis;
	m_CurrentProduct.m_RotateZAxis = productPage->m_RotateZAxis;

	if (productPage->m_UnitOfIssue.GetCurSel() >= 0)
		m_CurrentProduct.m_UnitOfIssue = productPage->m_UnitOfIssue.GetCurSel();
	else
		m_CurrentProduct.m_UnitOfIssue = 2;		// default to case

	if (productPage->m_Weight != "")
		m_CurrentProduct.m_Weight = (float)atof(productPage->m_Weight);

	if (productPage->m_CaseWidth != "")
		m_CurrentProduct.m_CaseWidth = (float)atof(productPage->m_CaseWidth);

	m_CurrentProduct.m_WMSProductDetailID = productPage->m_WMSProductDetailID;

	m_CurrentProduct.m_WMSProductID = productPage->m_WMSProductID;

	m_CurrentProduct.m_IsActive = productPage->m_Active;
	m_CurrentProduct.m_Trace = productPage->m_Trace;
	
	if (productPage->m_CommodityType != "")
		m_CurrentProduct.m_CommodityType = productPage->m_CommodityType;
	else
		m_CurrentProduct.m_CommodityType = " ";

	if (productPage->m_CrushFactor != "")
		m_CurrentProduct.m_CrushFactor = productPage->m_CrushFactor;
	else
		m_CurrentProduct.m_CrushFactor = " ";

	if (productPage->m_PreviousBOH != "")
		m_CurrentProduct.m_PreviousBOH = atof(productPage->m_PreviousBOH);
	else
		m_CurrentProduct.m_PreviousBOH = m_CurrentProduct.m_BalanceOnHand;

	if (productPage->m_PreviousMovement != "")
		m_CurrentProduct.m_PreviousMovement = atof(productPage->m_PreviousMovement);
	else
		m_CurrentProduct.m_PreviousMovement = m_CurrentProduct.m_Movement;

	// Container Attributes
	if (containerPage->m_NumberInPallet != "")
		m_CurrentProduct.m_NumberInPallet = atoi(containerPage->m_NumberInPallet);

	if (containerPage->m_ContainerLength != "")
		m_CurrentProduct.m_Container.m_Length = (float)atof(containerPage->m_ContainerLength);

	if (containerPage->m_ContainerHeight != "")
		m_CurrentProduct.m_Container.m_Height = (float)atof(containerPage->m_ContainerHeight);

	if (containerPage->m_ContainerWidth != "")
		m_CurrentProduct.m_Container.m_Width = (float)atof(containerPage->m_ContainerWidth);

	if (containerPage->m_Ti != "")
		m_CurrentProduct.m_Container.m_Ti = atoi(containerPage->m_Ti);

	if (containerPage->m_Hi != "")
		m_CurrentProduct.m_Container.m_Hi = atoi(containerPage->m_Hi);

	m_CurrentProduct.m_Container.m_IsWidthOverride = 0;
	m_CurrentProduct.m_Container.m_IsLengthOverride = 0;
	m_CurrentProduct.m_Container.m_IsHeightOverride = containerPage->m_IsHeightOverride;


	// Product UDFs
	for (int i=0; i < udfPage->m_UDFs.GetSize(); ++i) {
		pUDF = udfPage->m_UDFs[i];
		CUDF *pProdUDF;
		for (int j=0; j < m_CurrentProduct.m_UDFList.GetSize(); ++j) {
			pProdUDF = m_CurrentProduct.m_UDFList[j];
			if (pProdUDF->m_Name.CompareNoCase(pUDF->m_Name) == 0) {
				if (pUDF->m_Type == DT_STRING && pUDF->m_Value == "")
					pProdUDF->m_Value = "BLANK";
				else
					pProdUDF->m_Value = pUDF->m_Value;
				if (utilityHelper.IsNumeric(pUDF->m_Value)) {
					pProdUDF->m_IntegerValue = atoi(pProdUDF->m_Value);
					pProdUDF->m_FloatValue = atof(pProdUDF->m_Value);
				}
				else {
					pProdUDF->m_IntegerValue = 0;
					pProdUDF->m_FloatValue = 0.0;
				}
				break;
			}
		}
	}

	optimizePage->m_ProductGroupCtl.GetWindowText(m_CurrentProduct.m_ProductGroup);
	optimizePage->m_LocationCtl.GetWindowText(m_CurrentProduct.m_Location);
	optimizePage->m_LocationProductGroupCtl.GetWindowText(m_CurrentProduct.m_LocProductGroup);
	optimizePage->m_BayProfileCtl.GetWindowText(m_CurrentProduct.m_BayProfile);
	optimizePage->m_LevelTypeCtl.GetWindowText(m_CurrentProduct.m_LevelType);

	optimizePage->m_OptimizedLocationCtl.GetWindowText(m_CurrentProduct.m_OptimizedLocation);
	optimizePage->m_OptimizedLocationProductGroupCtl.GetWindowText(m_CurrentProduct.m_OptimizedLocProductGroup);
	optimizePage->m_OptimizedBayProfileCtl.GetWindowText(m_CurrentProduct.m_OptimizedBayProfile);
	optimizePage->m_OptimizedLevelTypeCtl.GetWindowText(m_CurrentProduct.m_OptimizedLevelType);
	
	m_CurrentProduct.m_RotatedWidth = atof(optimizePage->m_RotatedWidth);
	m_CurrentProduct.m_RotatedLength = atof(optimizePage->m_RotatedLength);
	m_CurrentProduct.m_RotatedHeight = atof(optimizePage->m_RotatedHeight);

}



void CProductSheet::LoadScreenFromProduct()
{
	CProductPage *productPage = (CProductPage *)this->GetPage(0);
	CProductContainerPage *containerPage = (CProductContainerPage *)this->GetPage(1);
	CUDFPage *udfPage = (CUDFPage *)this->GetPage(2);
	CProductOptimizePage *optimizePage = (CProductOptimizePage *)this->GetPage(3);
	CUDF *prodUDF, *screenUDF;

	// Product Attributes

	productPage->m_Description = m_CurrentProduct.m_Description;
	productPage->m_WMSProductID = m_CurrentProduct.m_WMSProductID;
	productPage->m_WMSProductDetailID = m_CurrentProduct.m_WMSProductDetailID;
	productPage->m_UnitOfIssue.SetCurSel(m_CurrentProduct.m_UnitOfIssue);
	productPage->m_CasePack.Format("%-d", m_CurrentProduct.m_CasePack);
	productPage->m_InnerPack.Format("%-d", m_CurrentProduct.m_InnerPack);
	productPage->m_IsAssignmentLocked = m_CurrentProduct.m_IsAssignmentLocked;
	productPage->m_Active = m_CurrentProduct.m_IsActive;
	productPage->m_Trace = m_CurrentProduct.m_Trace;

	productPage->m_Movement.Format("%-.3f", m_CurrentProduct.m_Movement);
	productPage->m_BalanceOnHand.Format("%-.3f", m_CurrentProduct.m_BalanceOnHand);
	productPage->m_PreviousMovement.Format("%-.3f", m_CurrentProduct.m_PreviousMovement);
	productPage->m_PreviousBOH.Format("%-.3f", m_CurrentProduct.m_PreviousBOH);
	productPage->m_OptimizeBy.SetCurSel(m_CurrentProduct.m_OptimizeBy);
	productPage->m_NumberOfHits.Format("%-.3f", m_CurrentProduct.m_NumberOfHits);
	productPage->m_IsHazard = m_CurrentProduct.m_IsHazard;
	productPage->m_IsPickToBelt = m_CurrentProduct.m_IsPickToBelt;
	productPage->m_RotateXAxis = m_CurrentProduct.m_RotateXAxis;
	productPage->m_RotateYAxis = m_CurrentProduct.m_RotateYAxis;
	productPage->m_RotateZAxis = m_CurrentProduct.m_RotateZAxis;
	productPage->m_CommodityType = m_CurrentProduct.m_CommodityType;
	productPage->m_CrushFactor = m_CurrentProduct.m_CrushFactor;

	productPage->m_CaseWidth.Format("%-.3f", m_CurrentProduct.m_CaseWidth);
	productPage->m_CaseLength.Format("%-.3f", m_CurrentProduct.m_CaseLength);
	productPage->m_CaseHeight.Format("%-.3f", m_CurrentProduct.m_CaseHeight);
	productPage->m_InnerWidth.Format("%-.3f", m_CurrentProduct.m_InnerWidth);
	productPage->m_InnerLength.Format("%-.3f", m_CurrentProduct.m_InnerLength);
	productPage->m_InnerHeight.Format("%-.3f", m_CurrentProduct.m_InnerHeight);
	productPage->m_EachWidth.Format("%-.3f", m_CurrentProduct.m_EachWidth);
	productPage->m_EachLength.Format("%-.3f", m_CurrentProduct.m_EachLength);
	productPage->m_EachHeight.Format("%-.3f", m_CurrentProduct.m_EachHeight);

	productPage->m_Weight.Format("%-.3f", m_CurrentProduct.m_Weight);
	if (m_CurrentProduct.m_MaxStackNumber == SLOT_NIL_INTEGER)
		productPage->m_MaxStackNumber = "";
	else
		productPage->m_MaxStackNumber.Format("%-d", m_CurrentProduct.m_MaxStackNumber);
	
	if (m_CurrentProduct.m_CommodityType == " ")
		productPage->m_CommodityType = "";
	else
		productPage->m_CommodityType = m_CurrentProduct.m_CommodityType;

	if (m_CurrentProduct.m_CrushFactor == " ")
		productPage->m_CrushFactor = "";
	else
		productPage->m_CrushFactor = m_CurrentProduct.m_CrushFactor;

	// Container Attributes
	containerPage->m_NumberInPallet.Format("%-d", m_CurrentProduct.m_NumberInPallet);
	containerPage->m_ContainerWidth.Format("%-.3f", m_CurrentProduct.m_Container.m_Width);
	containerPage->m_ContainerLength.Format("%-.3f", m_CurrentProduct.m_Container.m_Length);
	containerPage->m_ContainerHeight.Format("%-.3f", m_CurrentProduct.m_Container.m_Height);
	containerPage->m_Ti.Format("%-d", m_CurrentProduct.m_Container.m_Ti);
	containerPage->m_Hi.Format("%-d", m_CurrentProduct.m_Container.m_Hi);
	containerPage->m_IsHeightOverride = m_CurrentProduct.m_Container.m_IsHeightOverride;

	// Product UDFs
	for (int i=0; i < m_CurrentProduct.m_UDFList.GetSize(); ++i) {
		prodUDF = m_CurrentProduct.m_UDFList[i];
		for (int j=0; j < udfPage->m_UDFs.GetSize(); ++j) {
			screenUDF = udfPage->m_UDFs[j];
			if (screenUDF->m_Name.CompareNoCase(prodUDF->m_Name) == 0) {
				screenUDF->m_Value = prodUDF->m_Value;
				screenUDF->m_IntegerValue = prodUDF->m_IntegerValue;
				screenUDF->m_FloatValue = prodUDF->m_FloatValue;
				if (screenUDF->m_Value == "BLANK")
					screenUDF->m_Value = "";
				break;
			}
		}
	}
	

	optimizePage->m_IntegrationStatus = m_CurrentProduct.ConvertStatusToText();

	if (m_CurrentProduct.m_ProductGroupDBID > 0) {
		optimizePage->m_ProductGroupDBID = m_CurrentProduct.m_ProductGroupDBID;
		optimizePage->m_ProductGroup = m_CurrentProduct.m_ProductGroup;
	}
	else {
		optimizePage->m_ProductGroupDBID = 0;
		optimizePage->m_ProductGroup = "Not Assigned";
	}

	if (m_CurrentProduct.m_LocationDBID > 0) {
		optimizePage->m_Location = m_CurrentProduct.m_Location;
		if (m_CurrentProduct.m_LocProductGroupDBID > 0) {
			optimizePage->m_LocationProductGroupDBID = m_CurrentProduct.m_LocProductGroupDBID;
			optimizePage->m_LocationProductGroup = m_CurrentProduct.m_LocProductGroup;
		}
		else {
			optimizePage->m_LocationProductGroupDBID = 0;
			optimizePage->m_LocationProductGroup = "Not Assigned";
		}
		optimizePage->m_BayProfile = m_CurrentProduct.m_BayProfile;
		optimizePage->m_BayProfileDBID = m_CurrentProduct.m_BayProfileDBID;
		optimizePage->m_LevelType = m_CurrentProduct.m_LevelType;
	}
	else {
		optimizePage->m_Location = "Not Assigned";
		optimizePage->m_LocationProductGroup = "";
		optimizePage->m_LocationProductGroupDBID = 0;
		optimizePage->m_BayProfile = "";
		optimizePage->m_BayProfileDBID = 0;
		optimizePage->m_LevelType = "";
		optimizePage->m_LevelTypeNum = -1;
	}

	if (m_CurrentProduct.m_OptimizedLocationDBID > 0) {
		optimizePage->m_OptimizedLocation = m_CurrentProduct.m_OptimizedLocation;
		if (m_CurrentProduct.m_OptimizedLocProductGroupDBID > 0) {
			optimizePage->m_OptimizedLocationProductGroupDBID = m_CurrentProduct.m_OptimizedLocProductGroupDBID;
			optimizePage->m_OptimizedLocationProductGroup = m_CurrentProduct.m_OptimizedLocProductGroup;
		}
		else {
			optimizePage->m_OptimizedLocationProductGroupDBID = 0;
			optimizePage->m_OptimizedLocationProductGroup = "Not Assigned";
		}
		optimizePage->m_OptimizedBayProfile = m_CurrentProduct.m_OptimizedBayProfile;
		optimizePage->m_OptimizedBayProfileDBID = m_CurrentProduct.m_OptimizedBayProfileDBID;
		optimizePage->m_OptimizedLevelType = m_CurrentProduct.m_OptimizedLevelType;
	}
	else {
		optimizePage->m_OptimizedLocation = "Not Assigned";
		optimizePage->m_OptimizedLocationProductGroup = "";
		optimizePage->m_OptimizedLocationProductGroupDBID = 0;
		optimizePage->m_OptimizedBayProfile = "";
		optimizePage->m_OptimizedBayProfileDBID = 0;
		optimizePage->m_OptimizedLevelType = "";
		optimizePage->m_OptimizedLevelTypeNum = -1;
	}

	if (m_CurrentProduct.m_RotatedWidth == SLOT_NIL_FLOAT)
		optimizePage->m_RotatedWidth = "";
	else
		optimizePage->m_RotatedWidth.Format("%-0.3f",  0-m_CurrentProduct.m_RotatedWidth);

	if (m_CurrentProduct.m_RotatedLength == SLOT_NIL_FLOAT)
		optimizePage->m_RotatedLength = "";
	else
		optimizePage->m_RotatedLength.Format("%-0.3f", 0-m_CurrentProduct.m_RotatedLength);

	if (m_CurrentProduct.m_RotatedHeight == SLOT_NIL_FLOAT)	
		optimizePage->m_RotatedHeight = "";
	else
		optimizePage->m_RotatedHeight.Format("%-0.3f", 0-m_CurrentProduct.m_RotatedHeight);

	GetActivePage()->UpdateData(FALSE);

	return;

}



void CProductSheet::OnClear()
{
	CProductPage *productPage = (CProductPage *)this->GetPage(0);
	CProductContainerPage *containerPage = (CProductContainerPage *)this->GetPage(1);
	CUDFPage *udfPage = (CUDFPage *)this->GetPage(2);
	CProductOptimizePage *optimizePage = (CProductOptimizePage *)this->GetPage(3);

	CUDF udf, *pUDF;

	// Product Attributes
	productPage->m_Description = "";
	productPage->m_WMSProductID = "";
	productPage->m_WMSProductDetailID = "";
	productPage->m_UnitOfIssue.SetCurSel(-1);
	productPage->m_CasePack = "";
	productPage->m_InnerPack = "";
	productPage->m_IsAssignmentLocked = 2;

	productPage->m_Movement = "";
	productPage->m_BalanceOnHand = "";
	productPage->m_OptimizeBy.SetCurSel(-1);
	productPage->m_NumberOfHits = "";
	productPage->m_IsHazard = 2;
	productPage->m_IsPickToBelt = 2;
	productPage->m_RotateXAxis = 2;
	productPage->m_RotateYAxis = 2;
	productPage->m_RotateZAxis = 2;

	productPage->m_CaseWidth = "";
	productPage->m_CaseLength = "";
	productPage->m_CaseHeight = "";
	productPage->m_InnerWidth = "";
	productPage->m_InnerLength = "";
	productPage->m_InnerHeight = "";
	productPage->m_EachWidth = "";
	productPage->m_EachLength = "";
	productPage->m_EachHeight = "";

	productPage->m_Weight = "";
	productPage->m_MaxStackNumber = "";
	
	productPage->m_Active = 2;
	productPage->m_Trace = 2;
	productPage->m_CommodityType = "";
	productPage->m_CrushFactor = "";
	productPage->m_PreviousBOH = "";
	productPage->m_PreviousMovement = "";


	// Container attributes
	containerPage->m_NumberInPallet = "";		// this attribute lives on the product
	containerPage->m_ContainerWidth = "";
	containerPage->m_ContainerLength = "";
	containerPage->m_ContainerHeight = "";
	containerPage->m_Ti = "";
	containerPage->m_Hi = "";

	containerPage->m_IsHeightOverride = 2;


	// Product UDFs
	for (int i=0; i < udfPage->m_UDFs.GetSize(); ++i) {
		pUDF = (CUDF *)udfPage->m_UDFs[i];
		pUDF->m_Value = "";
	}
	
	optimizePage->m_Location = "";
	optimizePage->m_ProductGroup = "";
	optimizePage->m_LocationProductGroup = "";
	optimizePage->m_BayProfile = "";
	optimizePage->m_LevelType = "";
	optimizePage->m_RotatedWidth = "";
	optimizePage->m_RotatedLength = "";
	optimizePage->m_RotatedHeight = "";

	optimizePage->m_OptimizedLocation = "";
	optimizePage->m_OptimizedLocationProductGroup = "";
	optimizePage->m_OptimizedBayProfile = "";
	optimizePage->m_OptimizedLevelType = "";
	GetActivePage()->UpdateData(FALSE);

	m_CurrentProductIdx = -1;

	return;

}


BOOL CProductPage::OnInitDialog() 
{
	CPropertyPage::OnInitDialog();
	
	CRect r;
	CComboBox *pComboBox = (CComboBox *)GetDlgItem(IDC_UNITOFISSUE);
	pComboBox->SetItemHeight(0,2000);
	pComboBox->GetWindowRect(&r);
	pComboBox->SetWindowPos(NULL, 0, 0, r.Width(), r.Height()*4, SWP_NOMOVE|SWP_NOZORDER);

	pComboBox = (CComboBox *)GetDlgItem(IDC_OPTIMIZEBY);
	pComboBox->SetItemHeight(0,2000);
	pComboBox->GetWindowRect(&r);
	pComboBox->SetWindowPos(NULL, 0, 0, r.Width(), r.Height()*3, SWP_NOMOVE|SWP_NOZORDER);

	return TRUE;  // return TRUE unless you set the focus to a control
	              // EXCEPTION: OCX Property Pages should return FALSE
}

void CProductSheet::OnCancel()
{
	CProductPack product;


	// May add some code like the following later to stop the user from
	// cancelling after making changes but have to remember to do the same
	// thing if they hit clear or attempt to load another product

	/*
	LoadProductFromScreen();

	if (! m_CurrentProduct.IsEqual(&product)) {
		int rc = AfxMessageBox("The current product has changed.  Do you wish to save the changes?",
			MB_YESNOCANCEL);
		if (rc == IDYES) {
			EndDialog(IDOK);
		}
		else if (rc == IDCANCEL) {
			return;
		}
		else {
			EndDialog(IDCANCEL);
		}
	}
	*/
	EndDialog(IDCANCEL);

}

void CProductSheet::OnChangeSelection(WPARAM wParam, LPARAM lParam)
{
	UNREFERENCED_PARAMETER(lParam);

	m_CurrentProductIdx = wParam;
	m_CurrentProduct = *m_Products[wParam];
	GetProductByID();

	LoadScreenFromProduct();

}

void CProductSheet::OnCloseProductListDialog(WPARAM wParam, LPARAM lParam)
{
	UNREFERENCED_PARAMETER(lParam);
	UNREFERENCED_PARAMETER(wParam);

	m_ProductListDialog = NULL;
		
	this->CenterWindow(utilityHelper.GetParentWindow());
}

void CProductSheet::OnSizeProductListDialog(WPARAM wParam, LPARAM lParam)
{
	UNREFERENCED_PARAMETER(wParam);
	UNREFERENCED_PARAMETER(lParam);


}

void CProductSheet::BuildDisplayLine(CProductPack *product, CString &line)
{
	CString uoi;
	switch (product->m_UnitOfIssue) {
	case UOI_EACH:
		uoi = "Each";
		break;
	case UOI_INNER:
		uoi = "Inner";
		break;
	case UOI_CASE:
		uoi = "Case";
		break;
	default:
		uoi = "Pallet";
		break;
	}

	line.Format("%s|%s|%s|%s|%d|%d|",
		product->m_WMSProductID,
		product->m_WMSProductDetailID,
		product->m_Description,
		uoi, product->m_CasePack, product->m_InnerPack);

	return;

}

BOOL CProductSheet::ValidateUpdate()
{
	CProductPage *pProductPage = (CProductPage *)GetPage(0);
	CProductContainerPage *pContainerPage = (CProductContainerPage *)GetPage(1);
	CUDFPage *pUDFPage = (CUDFPage *)GetPage(2);

	if (! ValidateItem("Description", pProductPage->m_Description, DT_STRING, 0, 255, 0, IDC_DESCRIPTION, TRUE))
		return FALSE;

	if (! ValidateItem("WMS Product ID", pProductPage->m_WMSProductID, DT_STRING, 0, 255, 0, IDC_WMSPRODUCTID, TRUE))
		return FALSE;
	
	if (! ValidateItem("Commodity Type", pProductPage->m_CommodityType, DT_STRING, 0, 255, 0, IDC_COMMODITY_TYPE, FALSE))
		return FALSE;
			
	if (! ValidateItem("Crush Factor", pProductPage->m_CrushFactor, DT_STRING, 0, 255, 0, IDC_CRUSH_FACTOR, FALSE))
		return FALSE;

	if (! ValidateItem("WMS Detail ID", pProductPage->m_WMSProductDetailID, DT_STRING, 0, 255, 0, IDC_WMSPRODUCTDETAILID, TRUE))
		return FALSE;
	
	if (! ValidateItem("Case Pack", pProductPage->m_CasePack, DT_INT, 0, 99999999, 0, IDC_CASEPACK, TRUE))
		return FALSE;

	if (! ValidateItem("Inner Pack", pProductPage->m_InnerPack, DT_INT, 0, 99999999, 0, IDC_INNERPACK, TRUE))
		return FALSE;

	if (! ValidateItem("Movement", pProductPage->m_Movement, DT_FLOAT, 0, 99999999, 0, IDC_MOVEMENT, TRUE))
		return FALSE;

	if (! ValidateItem("Balance on Hand", pProductPage->m_BalanceOnHand, DT_FLOAT, 0, 99999999, 0, IDC_BALANCEONHAND, TRUE))
		return FALSE;

	if (! ValidateItem("Number of Hits", pProductPage->m_NumberOfHits, DT_FLOAT, 0, 99999999, 0, IDC_NUMBEROFHITS, TRUE))
		return FALSE;
	
	if (! ValidateItem("Case Width", pProductPage->m_CaseWidth, DT_FLOAT, 0, 99999999, 0, IDC_WIDTH, TRUE))
		return FALSE;

	if (! ValidateItem("Case Length", pProductPage->m_CaseLength, DT_FLOAT, 0, 99999999, 0, IDC_LENGTH, TRUE))
		return FALSE;

	if (! ValidateItem("Case Height", pProductPage->m_CaseHeight, DT_FLOAT, 0, 99999999, 0, IDC_HEIGHT, TRUE))
		return FALSE;

	if (! ValidateItem("Inner Width", pProductPage->m_InnerWidth, DT_FLOAT, 0, 99999999, 0, IDC_INNERWIDTH, TRUE))
		return FALSE;

	if (! ValidateItem("Inner Length", pProductPage->m_InnerLength, DT_FLOAT, 0, 99999999, 0, IDC_INNERLENGTH, TRUE))
		return FALSE;

	if (! ValidateItem("Inner Height", pProductPage->m_InnerHeight, DT_FLOAT, 0, 99999999, 0, IDC_INNERHEIGHT, TRUE))
		return FALSE;

	if (! ValidateItem("Each Width", pProductPage->m_EachWidth, DT_FLOAT, 0, 99999999, 0, IDC_EACHWIDTH, TRUE))
		return FALSE;

	if (! ValidateItem("Each Length", pProductPage->m_EachLength, DT_FLOAT, 0, 99999999, 0, IDC_EACHLENGTH, TRUE))
		return FALSE;

	if (! ValidateItem("Each Height", pProductPage->m_EachHeight, DT_FLOAT, 0, 99999999, 0, IDC_EACHHEIGHT, TRUE))
		return FALSE;

	if (! ValidateItem("Weight", pProductPage->m_Weight, DT_FLOAT, 0, 99999999, 0, IDC_WEIGHT, TRUE))
		return FALSE;

	if (! ValidateItem("Max Stack Number", pProductPage->m_MaxStackNumber, DT_INT, 0, 99999999, 0, IDC_MAXSTACKNUMBER, FALSE))
		return FALSE;

	if (! ValidateItem("Previous Movement", pProductPage->m_PreviousMovement, DT_FLOAT, 0, 99999999, 0, IDC_PREVIOUS_MOVEMENT, FALSE))
		return FALSE;	

	if (! ValidateItem("Previous BOH", pProductPage->m_PreviousBOH, DT_FLOAT, 0, 99999999, 0, IDC_PREVIOUS_BOH, FALSE))
		return FALSE;

	if (pProductPage->m_IsAssignmentLocked == 2) {
		AfxMessageBox("Assignment Locked: Checkbox must be on or off");
		SetActivePage(0);
		return FALSE;
	}

	if (pProductPage->m_IsHazard == 2) {
		AfxMessageBox("Hazard: Checkbox must be on or off");
		SetActivePage(0);
		return FALSE;
	}

	if (pProductPage->m_IsPickToBelt == 2) {
		AfxMessageBox("Pick to Belt: Checkbox must be on or off");
		SetActivePage(0);
		return FALSE;
	}

	if (pProductPage->m_RotateXAxis == 2) {
		AfxMessageBox("Allow Rotate on X Axis: Checkbox must be on or off");
		SetActivePage(0);
		return FALSE;
	}
	
	if (pProductPage->m_RotateYAxis == 2) {
		AfxMessageBox("Allow Rotate on Y Axis: Checkbox must be on or off");
		SetActivePage(0);
		return FALSE;
	}
	
	if (pProductPage->m_RotateZAxis == 2) {
		AfxMessageBox("Allow Rotate on Z Axis: Checkbox must be on or off");
		SetActivePage(0);
		return FALSE;
	}
	
	if (pProductPage->m_Active == 2) {
		AfxMessageBox("Active: Checkbox must be on or off");
		SetActivePage(0);
		return FALSE;
	}
	
	if (pProductPage->m_Trace == 2) {
		AfxMessageBox("Trace Enabled: Checkbox must be on or off");
		SetActivePage(0);
		return FALSE;
	}

	if (! ValidateItem("Number in Pallet", pContainerPage->m_NumberInPallet, DT_INT, 0, 99999999, 0, IDC_NUMBERINPALLET, TRUE))
		return FALSE;

	if (! ValidateItem("Container Width", pContainerPage->m_ContainerWidth, DT_FLOAT, 0, 99999999, 1, IDC_CONTAINER_WIDTH, TRUE))
		return FALSE;

	if (! ValidateItem("Container Length", pContainerPage->m_ContainerLength, DT_FLOAT, 0, 99999999, 1, IDC_CONTAINER_LENGTH, TRUE))
		return FALSE;
	
	if (! ValidateItem("Container Height", pContainerPage->m_ContainerHeight, DT_FLOAT, 0, 99999999, 1, IDC_CONTAINER_HEIGHT, TRUE))
		return FALSE;

	if (! ValidateItem("Ti", pContainerPage->m_Ti, DT_INT, 0, 99999999, 1, IDC_TI, TRUE))
		return FALSE;

	if (! ValidateItem("Hi", pContainerPage->m_Hi, DT_INT, 0, 99999999, 1, IDC_HI, TRUE))
		return FALSE;

	if (pContainerPage->m_IsHeightOverride == 2) {
		AfxMessageBox("Container Height Overrides: Checkbox must be on or off");
		SetActivePage(1);
		return FALSE;
	}
	
	CUDF *pUDF;
	for (int i=0; i < pUDFPage->m_UDFs.GetSize(); ++i) {
		pUDF = (CUDF *)pUDFPage->m_UDFs[i];
		if (pUDF->m_Type == DT_STRING) {
			if (! ValidateItem(pUDF->m_Name, pUDF->m_Value, pUDF->m_Type, 0, 255, 2, WM_USER+i, FALSE))
				return FALSE;
		}
		else {
			if (! ValidateItem(pUDF->m_Name, pUDF->m_Value, pUDF->m_Type, 0, 99999999, 2, WM_USER+i, TRUE))
				return FALSE;
		}
	}

	return TRUE;

}

BOOL CProductSheet::ValidateItem(const CString &name, const CString &value, int type, double min, double max,
								 int page, int dialogID, BOOL mandatory)
{
	CString tmp;
	CEdit *pEdit;
	CProductPage *pProductPage = (CProductPage *)GetPage(0);
	CProductContainerPage *pContainerPage = (CProductContainerPage *)GetPage(1);
	CUDFPage *pUDFPage = (CUDFPage *)GetPage(2);
	CString str = value;

	switch (type) {
	case DT_STRING:
		if (str == "" && mandatory) {
			tmp.Format("%s is a required field", name);
			AfxMessageBox(tmp);
			SetActivePage(page);
			pEdit = (CEdit *)GetPage(page)->GetDlgItem(dialogID);
			pEdit->SetSel(0, -1);
			pEdit->SetFocus();
			return FALSE;
		}

		if (str.GetLength() > max) {
			tmp.Format("%s: Length must be less than or equal to %-d", 
				name, max);
			AfxMessageBox(tmp);
			SetActivePage(page);
			pEdit = (CEdit *)GetPage(page)->GetDlgItem(dialogID);
			pEdit->SetSel(0, -1);
			pEdit->SetFocus();
			return FALSE;
		}

		if (str.GetLength() < min) {
			tmp.Format("%s: Length must be greater than or equal to %-d",
				name, min);
			AfxMessageBox(tmp);
			SetActivePage(page);
			pEdit = (CEdit *)GetPage(page)->GetDlgItem(dialogID);
			pEdit->SetSel(0, -1);
			pEdit->SetFocus();
			return FALSE;
		}

		break;

	case DT_INT:
		str.TrimLeft();		
		str.TrimRight();

		if (str == "" && mandatory) {
			tmp.Format("%s is a required field", name);
			AfxMessageBox(tmp);
			SetActivePage(page);
			pEdit = (CEdit *)GetPage(page)->GetDlgItem(dialogID);
			pEdit->SetSel(0, -1);
			pEdit->SetFocus();
			return FALSE;
		}

		if (! utilityHelper.IsInteger(str)) {
			tmp.Format("%s: Must be a valid integer", name);
			AfxMessageBox(tmp);
			SetActivePage(page);
			pEdit = (CEdit *)GetPage(page)->GetDlgItem(dialogID);
			pEdit->SetSel(0, -1);
			pEdit->SetFocus();
			return FALSE;
		}

		if (atoi(str) < min) {
			tmp.Format("%s: Must be greater than or equal to %-.0f", 
				name, min);
			AfxMessageBox(tmp);
			SetActivePage(page);
			pEdit = (CEdit *)GetPage(page)->GetDlgItem(dialogID);
			pEdit->SetSel(0, -1);
			pEdit->SetFocus();
			return FALSE;
		}

		if (atoi(str) > max) {
			tmp.Format("%s: Must be less than or equal to %-.0f", 
				name, max);
			SetActivePage(page);
			pEdit = (CEdit *)GetPage(page)->GetDlgItem(dialogID);
			pEdit->SetSel(0, -1);
			pEdit->SetFocus();
			AfxMessageBox(tmp);
			return FALSE;
		}
		
		break;

	case DT_FLOAT:
		
		str.TrimLeft();		
		str.TrimRight();

		if (str == "" && mandatory) {
			tmp.Format("%s is a required field", name);
			AfxMessageBox(tmp);
			SetActivePage(page);
			pEdit = (CEdit *)GetPage(page)->GetDlgItem(dialogID);
			pEdit->SetSel(0, -1);
			pEdit->SetFocus();
			return FALSE;
		}

		if (! utilityHelper.IsFloat(str)) {
			tmp.Format("%s: Must be a valid floating point number", name);
			AfxMessageBox(tmp);
			SetActivePage(page);
			pEdit = (CEdit *)GetPage(page)->GetDlgItem(dialogID);
			pEdit->SetSel(0, -1);
			pEdit->SetFocus();
			return FALSE;
		}

		if (atoi(str) < min) {
			tmp.Format("%s: Must be greater than or equal to %-0.0f", 
				name, min);
			AfxMessageBox(tmp);
			SetActivePage(page);
			pEdit = (CEdit *)GetPage(page)->GetDlgItem(dialogID);
			pEdit->SetSel(0, -1);
			pEdit->SetFocus();
			return FALSE;
		}

		if (atoi(str) > max) {
			tmp.Format("%s: Must be less than or equal to %-0.0f", 
				name, max);
			AfxMessageBox(tmp);
			SetActivePage(page);
			pEdit = (CEdit *)GetPage(page)->GetDlgItem(dialogID);
			pEdit->SetSel(0, -1);
			pEdit->SetFocus();
			return FALSE;
		}
		
		break;
	}

	return TRUE;

}
/////////////////////////////////////////////////////////////////////////////
// CProductOptimizePage property page

IMPLEMENT_DYNCREATE(CProductOptimizePage, CPropertyPage)

CProductOptimizePage::CProductOptimizePage() : CPropertyPage(CProductOptimizePage::IDD)
{
	//{{AFX_DATA_INIT(CProductOptimizePage)
	m_RotatedHeight = _T("");
	m_RotatedLength = _T("");
	m_RotatedWidth = _T("");
	m_Location = _T("");
	m_IntegrationStatus = _T("");
	m_OptimizedLocation = _T("");
	m_ProductGroup = _T("");
	//}}AFX_DATA_INIT
	m_ProductGroup = _T("");
	m_LocationProductGroup = _T("");
	m_BayProfile = _T("");
	m_LevelType = _T("");
	m_OptimizedLocationProductGroup = _T("");
	m_OptimizedBayProfile = _T("");
	m_OptimizedLevelType = _T("");
}

CProductOptimizePage::~CProductOptimizePage()
{
}

void CProductOptimizePage::DoDataExchange(CDataExchange* pDX)
{
	CPropertyPage::DoDataExchange(pDX);
	//{{AFX_DATA_MAP(CProductOptimizePage)
	DDX_Control(pDX, IDC_OPTIMIZED_LOCATION, m_OptimizedLocationCtl);
	DDX_Control(pDX, IDC_OPTIMIZED_LOCATION_PRODUCT_GROUP, m_OptimizedLocationProductGroupCtl);
	DDX_Control(pDX, IDC_OPTIMIZED_LEVEL_TYPE, m_OptimizedLevelTypeCtl);
	DDX_Control(pDX, IDC_OPTIMIZED_BAY_PROFILE, m_OptimizedBayProfileCtl);
	DDX_Control(pDX, IDC_PRODUCT_GROUP, m_ProductGroupCtl);
	DDX_Control(pDX, IDC_LOCATION_PRODUCT_GROUP, m_LocationProductGroupCtl);
	DDX_Control(pDX, IDC_LOCATION, m_LocationCtl);
	DDX_Control(pDX, IDC_LEVEL_TYPE, m_LevelTypeCtl);
	DDX_Control(pDX, IDC_BAY_PROFILE, m_BayProfileCtl);
	DDX_Text(pDX, IDC_ROTATED_HEIGHT, m_RotatedHeight);
	DDX_Text(pDX, IDC_ROTATED_LENGTH, m_RotatedLength);
	DDX_Text(pDX, IDC_ROTATED_WIDTH, m_RotatedWidth);
	DDX_CBString(pDX, IDC_LOCATION, m_Location);
	DDX_Text(pDX, IDC_INTEGRATION_STATUS, m_IntegrationStatus);
	DDX_CBString(pDX, IDC_OPTIMIZED_LOCATION, m_OptimizedLocation);
	DDX_CBString(pDX, IDC_PRODUCT_GROUP, m_ProductGroup);
	//}}AFX_DATA_MAP

	CComboBox *pProductGroupBox, *pLocationGroupBox, *pProfileBox, *pTypeBox;
	CComboBox *pOptLocationGroupBox, *pOptProfileBox, *pOptTypeBox;

	pProductGroupBox = (CComboBox *)GetDlgItem(IDC_PRODUCT_GROUP);
	pLocationGroupBox = (CComboBox *)GetDlgItem(IDC_LOCATION_PRODUCT_GROUP);
	pProfileBox = (CComboBox *)GetDlgItem(IDC_BAY_PROFILE);
	pTypeBox = (CComboBox *)GetDlgItem(IDC_LEVEL_TYPE);

	pOptLocationGroupBox = (CComboBox *)GetDlgItem(IDC_OPTIMIZED_LOCATION_PRODUCT_GROUP);
	pOptProfileBox = (CComboBox *)GetDlgItem(IDC_OPTIMIZED_BAY_PROFILE);
	pOptTypeBox = (CComboBox *)GetDlgItem(IDC_OPTIMIZED_LEVEL_TYPE);


	if (! pDX->m_bSaveAndValidate) {
		pProductGroupBox->SetCurSel(pProductGroupBox->FindString(0, m_ProductGroup));
		pLocationGroupBox->SetCurSel(pLocationGroupBox->FindString(0, m_LocationProductGroup));
		//pLocationBox->SetCurSel(-1);
		pProfileBox->SetCurSel(pProfileBox->FindString(0, m_BayProfile));
		pTypeBox->SetCurSel(pTypeBox->FindString(0, m_LevelType));

		pOptLocationGroupBox->SetCurSel(pOptLocationGroupBox->FindString(0, m_OptimizedLocationProductGroup));
		pOptProfileBox->SetCurSel(pOptProfileBox->FindString(0, m_OptimizedBayProfile));
		pOptTypeBox->SetCurSel(pOptTypeBox->FindString(0, m_OptimizedLevelType));

		SetLocationControls();
	}
	else {
		int n;
		n = pProductGroupBox->GetCurSel();
		if (n >= 0) {
			pProductGroupBox->GetWindowText(m_ProductGroup);
			m_ProductGroupDBID = pProductGroupBox->GetItemData(n);
		}
		else {
			m_ProductGroup = "";
			m_ProductGroupDBID = -1;
		}

		n = pLocationGroupBox->GetCurSel();
		if (n >= 0) {
			pLocationGroupBox->GetWindowText(m_LocationProductGroup);
			m_LocationProductGroupDBID = pLocationGroupBox->GetItemData(n);
		}
		else {
			m_LocationProductGroup = "";
			m_LocationProductGroupDBID = -1;
		}

		n = pProfileBox->GetCurSel();
		if (n >= 0) {
			pProfileBox->GetWindowText(m_BayProfile);
			m_BayProfileDBID = pProfileBox->GetItemData(n);
		}
		else {
			m_BayProfile = "";
			m_BayProfileDBID = -1;
		}

		n = pTypeBox->GetCurSel();
		if (n >= 0) {
			pTypeBox->GetWindowText(m_LevelType);
			m_LevelTypeNum = n+1;
		}
		else {
			m_LevelType = "";
			m_LevelTypeNum = -1;
		}

		n = pOptLocationGroupBox->GetCurSel();
		if (n >= 0) {
			pOptLocationGroupBox->GetWindowText(m_OptimizedLocationProductGroup);
			m_OptimizedLocationProductGroupDBID = pOptLocationGroupBox->GetItemData(n);
		}
		else {
			m_OptimizedLocationProductGroup = "";
			m_OptimizedLocationProductGroupDBID = -1;
		}

		n = pOptProfileBox->GetCurSel();
		if (n >= 0) {
			pOptProfileBox->GetWindowText(m_OptimizedBayProfile);
			m_OptimizedBayProfileDBID = pOptProfileBox->GetItemData(n);
		}
		else {
			m_OptimizedBayProfile = "";
			m_OptimizedBayProfileDBID = -1;
		}

		n = pOptTypeBox->GetCurSel();
		if (n >= 0) {
			pOptTypeBox->GetWindowText(m_OptimizedLevelType);
			m_OptimizedLevelTypeNum = n+1;
		}
		else {
			m_OptimizedLevelType = "";
			m_OptimizedLevelTypeNum = -1;
		}

		SetLocationControls();
	}
}


BEGIN_MESSAGE_MAP(CProductOptimizePage, CPropertyPage)
	//{{AFX_MSG_MAP(CProductOptimizePage)
	ON_CBN_SELCHANGE(IDC_LOCATION, OnSelchangeLocation)
	ON_CBN_EDITCHANGE(IDC_LOCATION, OnEditchangeLocation)
	ON_BN_CLICKED(IDC_UPDATE_GROUP, OnUpdateGroup)
	ON_WM_HELPINFO()
	ON_BN_CLICKED(IDC_VIEW_FACINGS, OnViewFacings)
	ON_CBN_EDITCHANGE(IDC_OPTIMIZED_LOCATION, OnEditchangeOptimizedLocation)
	ON_CBN_SELCHANGE(IDC_OPTIMIZED_LOCATION, OnSelchangeOptimizedLocation)
	ON_BN_CLICKED(IDC_VIEW_OPTIMIZED_FACINGS, OnViewOptimizedFacings)
	//}}AFX_MSG_MAP
END_MESSAGE_MAP()

/////////////////////////////////////////////////////////////////////////////
// CProductOptimizePage message handlers

BOOL CProductOptimizePage::OnInitDialog() 
{
	CPropertyPage::OnInitDialog();
	
	CComboBox *pProductGroupBox, *pLocationGroupBox, *pLocationBox, *pProfileBox;
	CComboBox *pOptLocationGroupBox, *pOptLocationBox, *pOptProfileBox;
	CStringArray pgList, bpList;
	int rc;
	CProductGroup pProductGroup;
	CBayProfile bayProfile;
	CRect r;
	CProductGroupDataService service;

	pProductGroupBox = (CComboBox *)GetDlgItem(IDC_PRODUCT_GROUP);
	pLocationGroupBox = (CComboBox *)GetDlgItem(IDC_LOCATION_PRODUCT_GROUP);
	pOptLocationGroupBox = (CComboBox *)GetDlgItem(IDC_OPTIMIZED_LOCATION_PRODUCT_GROUP);

	try {
		rc = service.GetProductGroups(controlService.GetCurrentFacilityDBId(), pgList);
	}
	catch (Ssa_Exception e) {
		utilityHelper.ProcessError("Error getting product groups.", &e);
	}
	catch (...) {
		utilityHelper.ProcessError("Error getting product groups.");
	}

	for (int i=0; i < pgList.GetSize(); ++i) {
		//pProductGroup = new CProductGroup;
		pProductGroup.Parse(pgList[i]);
		//m_ProductGroups.Add(pProductGroup);
		int nItem = pProductGroupBox->AddString(pProductGroup.m_Description);
		pProductGroupBox->SetItemData(nItem, pProductGroup.m_ProductGroupDBID);
		
		nItem = pLocationGroupBox->AddString(pProductGroup.m_Description);
		pLocationGroupBox->SetItemData(nItem, pProductGroup.m_ProductGroupDBID);

		nItem = pOptLocationGroupBox->AddString(pProductGroup.m_Description);
		pOptLocationGroupBox->SetItemData(nItem, pProductGroup.m_ProductGroupDBID);

	}

	pProductGroupBox->SetItemHeight(0, 2000);
	pProductGroupBox->GetWindowRect(&r);
	pProductGroupBox->SetWindowPos(NULL, 0, 0, r.Width(), r.Height()*10, SWP_NOMOVE|SWP_NOZORDER);

	pLocationGroupBox->SetItemHeight(0, 2000);
	pLocationGroupBox->GetWindowRect(&r);
	pLocationGroupBox->SetWindowPos(NULL, 0, 0, r.Width(), r.Height()*10, SWP_NOMOVE|SWP_NOZORDER);

	pOptLocationGroupBox->SetItemHeight(0, 2000);
	pOptLocationGroupBox->GetWindowRect(&r);
	pOptLocationGroupBox->SetWindowPos(NULL, 0, 0, r.Width(), r.Height()*10, SWP_NOMOVE|SWP_NOZORDER);

	pLocationBox = (CComboBox *)GetDlgItem(IDC_LOCATION);
	pLocationBox->SetItemHeight(0, 2000);
	pLocationBox->GetWindowRect(&r);
	pLocationBox->SetWindowPos(NULL, 0, 0, r.Width(), r.Height()*(pLocationBox->GetCount()+1), SWP_NOMOVE|SWP_NOZORDER);

	pOptLocationBox = (CComboBox *)GetDlgItem(IDC_OPTIMIZED_LOCATION);
	pOptLocationBox->SetItemHeight(0, 2000);
	pOptLocationBox->GetWindowRect(&r);
	pOptLocationBox->SetWindowPos(NULL, 0, 0, r.Width(), r.Height()*(pOptLocationBox->GetCount()+1), SWP_NOMOVE|SWP_NOZORDER);


	try {
		rc = facilityDataService.GetBayProfilesByFacility(bpList);
	}
	catch (Ssa_Exception e) {
		utilityHelper.ProcessError("Error getting bay profiless.", &e);
	}
	catch (...) {
		utilityHelper.ProcessError("Error getting bay profiles.");
	}

	pProfileBox = (CComboBox *)GetDlgItem(IDC_BAY_PROFILE);
	pOptProfileBox = (CComboBox *)GetDlgItem(IDC_OPTIMIZED_BAY_PROFILE);
	for (i=0; i < bpList.GetSize(); ++i) {
		bayProfile.Parse(bpList[i]);
		int nItem = pProfileBox->AddString(bayProfile.m_Description);
		pProfileBox->SetItemData(nItem, bayProfile.m_BayProfileDBId);

		nItem = pOptProfileBox->AddString(bayProfile.m_Description);
		pOptProfileBox->SetItemData(nItem, bayProfile.m_BayProfileDBId);

	}

	pProfileBox->SetItemHeight(0, 2000);
	pProfileBox->GetWindowRect(&r);
	pProfileBox->SetWindowPos(NULL, 0, 0, r.Width(), r.Height()*10, SWP_NOMOVE|SWP_NOZORDER);

	pOptProfileBox->SetItemHeight(0, 2000);
	pOptProfileBox->GetWindowRect(&r);
	pOptProfileBox->SetWindowPos(NULL, 0, 0, r.Width(), r.Height()*10, SWP_NOMOVE|SWP_NOZORDER);

	CWizardHelper wizHelper;

	wizHelper.LoadBayTypeComboBox(m_LevelTypeCtl);
	wizHelper.LoadBayTypeComboBox(m_OptimizedLevelTypeCtl);

	SetLocationControls();

	UpdateData(FALSE);

	return TRUE;  // return TRUE unless you set the focus to a control
	              // EXCEPTION: OCX Property Pages should return FALSE
}

void CProductOptimizePage::OnSelchangeLocation() 
{

	UpdateData(TRUE);

	if ( (m_LocationCtl.GetCurSel() == -1) ||
		(m_LocationCtl.GetCurSel() == 1) ) {
		m_BayProfileCtl.EnableWindow(FALSE);
		m_LocationProductGroupCtl.EnableWindow(FALSE);
		m_LevelTypeCtl.EnableWindow(FALSE);
	}
	else {
		m_BayProfileCtl.EnableWindow(TRUE);
		m_LocationProductGroupCtl.EnableWindow(TRUE);
		m_LevelTypeCtl.EnableWindow(TRUE);
	}

}

void CProductOptimizePage::OnEditchangeLocation() 
{
	UpdateData(TRUE);
	SetLocationControls();
}

void CProductOptimizePage::SetLocationControls()
{
	CEdit *pEdit;
	if ( (m_Location.CompareNoCase("Not Assigned") == 0)
		|| (m_Location.CompareNoCase("") == 0) ) {
		m_BayProfileCtl.EnableWindow(FALSE);
		m_LocationProductGroupCtl.EnableWindow(FALSE);
		m_LevelTypeCtl.EnableWindow(FALSE);
	}
	else {
		m_BayProfileCtl.EnableWindow(TRUE);
		m_LocationProductGroupCtl.EnableWindow(TRUE);
		m_LevelTypeCtl.EnableWindow(TRUE);
	}

	if ( (m_OptimizedLocation.CompareNoCase("Not Assigned") == 0)
		|| (m_OptimizedLocation.CompareNoCase("") == 0) ) {
		m_OptimizedBayProfileCtl.EnableWindow(FALSE);
		m_OptimizedLocationProductGroupCtl.EnableWindow(FALSE);
		m_OptimizedLevelTypeCtl.EnableWindow(FALSE);
		pEdit = (CEdit *)GetDlgItem(IDC_ROTATED_WIDTH);
		pEdit->EnableWindow(FALSE);
		pEdit = (CEdit *)GetDlgItem(IDC_ROTATED_LENGTH);
		pEdit->EnableWindow(FALSE);
		pEdit = (CEdit *)GetDlgItem(IDC_ROTATED_HEIGHT);
		pEdit->EnableWindow(FALSE);
	}
	else {
		m_OptimizedBayProfileCtl.EnableWindow(TRUE);
		m_OptimizedLocationProductGroupCtl.EnableWindow(TRUE);
		m_OptimizedLevelTypeCtl.EnableWindow(TRUE);
		pEdit = (CEdit *)GetDlgItem(IDC_ROTATED_WIDTH);
		pEdit->EnableWindow(TRUE);
		pEdit = (CEdit *)GetDlgItem(IDC_ROTATED_LENGTH);
		pEdit->EnableWindow(TRUE);
		pEdit = (CEdit *)GetDlgItem(IDC_ROTATED_HEIGHT);
		pEdit->EnableWindow(TRUE);
	}
}

int CProductSheet::GetProductByID()
{
	CString sql;
	int rc;
	CStringArray productList;
	CUDFPage *pUDFPage = (CUDFPage *)GetPage(2);
	CUDF *pUDF;

	//m_CurrentProduct = *m_Products[m_CurrentProductIdx];
	if (! m_CurrentProduct.m_IsProxy)
		return 0;

	// The proxy object may have udfs on it if the user queried by udf
	// so delete those, then add all the udfs from the udf display page
	for (int i=0; i < m_CurrentProduct.m_UDFList.GetSize(); ++i)
		delete m_CurrentProduct.m_UDFList[i];
	m_CurrentProduct.m_UDFList.RemoveAll();


	for (i=0; i < pUDFPage->m_UDFs.GetSize(); ++i) {
		pUDF = new CUDF;
		*pUDF = *(pUDFPage->m_UDFs[i]);
		m_CurrentProduct.m_UDFList.Add(pUDF);
	}

	try {
		CWaitCursor cwc;
		rc = m_ProductDataService.GetProductByID(m_CurrentProduct.m_ProductPackDBID, productList);
	}
	catch (Ssa_Exception e) {
		utilityHelper.ProcessError("Error querying for selected product.", &e);
		return -1;
	}
	catch (...) {
		utilityHelper.ProcessError("Error querying for selected product.");
		return -1;
	}

	if (productList.GetSize() <= 0) {
		AfxMessageBox("Error finding selected product.");
		return -1;
	}

	m_CurrentProduct.ParseAll(productList[0]);
	
	*m_Products[m_CurrentProductIdx] = m_CurrentProduct;

	return 0;

}

void CProductOptimizePage::OnUpdateGroup() 
{
	UpdateData(TRUE);

	CProductSheet *pParent = (CProductSheet *)GetParent();

	CString productGroup;
	long productGroupDBID;
	int rc;

	if (pParent->m_CurrentProductIdx < 0) {
		AfxMessageBox("There is no current product to update.");
		return;
	}
	
	if (m_ProductGroupCtl.GetCurSel() < 2) {
		AfxMessageBox("Current product group is not valid.");
		m_ProductGroupCtl.SetFocus();
		return;
	}

	m_ProductGroupCtl.GetWindowText(productGroup);
	productGroupDBID = m_ProductGroupCtl.GetItemData(m_ProductGroupCtl.GetCurSel());

	if (productGroupDBID <= 0) {
		AfxMessageBox("Error getting product group information.");
		return;
	}

	if (productGroupDBID == pParent->m_CurrentProduct.m_ProductGroupDBID) {
		AfxMessageBox("Product is already assigned to that group.");
		return;
	}
	
	try {
		rc = pParent->m_ProductDataService.UpdateProductGroup(pParent->m_CurrentProduct.m_ProductPackDBID,
			productGroupDBID, pParent->m_CurrentProduct.m_ProductGroupDBID);
	}
	catch (Ssa_Exception e) {
		utilityHelper.ProcessError("Error updating product group.", &e);
		rc = -1;
	}
	catch (...) {
		utilityHelper.ProcessError("Error updating product group.");
		rc = -1;
	}


	if (rc >= 0) {
		AfxMessageBox("The product was successfully assigned to the group.\n"
			"Note:  The product group assignment may be changed by the\n"
			"Define Product Groups screen if the product attributes do not\n"
			"match the product group query.");
		pParent->m_CurrentProduct.m_ProductGroup = productGroup;
		pParent->m_CurrentProduct.m_ProductGroupDBID = productGroupDBID;
		pParent->m_Products[pParent->m_CurrentProductIdx]->m_ProductGroup = productGroup;
		pParent->m_Products[pParent->m_CurrentProductIdx]->m_ProductGroupDBID = productGroupDBID;
	}


	return;
	
}

BOOL CProductContainerPage::OnNotify(WPARAM wParam, LPARAM lParam, LRESULT* pResult) 
{
	NMHDR* pNMHDR = (NMHDR*)lParam;
	if (pNMHDR->code == PSN_HELP) {
		helpService.ShowScreenHelp(IDD);
		return TRUE;
	}		
	return CPropertyPage::OnNotify(wParam, lParam, pResult);
}

BOOL CProductOptimizePage::OnNotify(WPARAM wParam, LPARAM lParam, LRESULT* pResult) 
{

	NMHDR* pNMHDR = (NMHDR*)lParam;

	if (pNMHDR->code == PSN_HELP) {
		helpService.ShowScreenHelp(IDD);
		return TRUE;
	}		
	return CPropertyPage::OnNotify(wParam, lParam, pResult);
}

BOOL CProductOptimizePage::OnHelpInfo(HELPINFO* pHelpInfo) 
{
	helpService.ShowFieldHelp(IDD, pHelpInfo->iCtrlId);
	return FALSE;
}

BOOL CProductPage::OnNotify(WPARAM wParam, LPARAM lParam, LRESULT* pResult) 
{
	NMHDR* pNMHDR = (NMHDR*)lParam;
	if (pNMHDR->code == PSN_HELP) {
		helpService.ShowScreenHelp(IDD);
		return TRUE;
	}		
	return CPropertyPage::OnNotify(wParam, lParam, pResult);
}

BOOL CProductPage::OnHelpInfo(HELPINFO* pHelpInfo) 
{
	helpService.ShowFieldHelp(IDD, pHelpInfo->iCtrlId);
	return FALSE;
}

BOOL CProductContainerPage::OnHelpInfo(HELPINFO* pHelpInfo) 
{
	helpService.ShowFieldHelp(IDD, pHelpInfo->iCtrlId);
	return FALSE;
}

BOOL CProductSheet::OnHelpInfo(HELPINFO* pHelpInfo) 
{
	switch (pHelpInfo->iCtrlId) {
	case IDOK:
		helpService.ShowFieldHelp("ProductMaintenance_Query");
		break;
	case IDCANCEL:
	case IDHELP:
		helpService.ShowFieldHelp(1, pHelpInfo->iCtrlId);
		break;
	case WM_USER+1:
		helpService.ShowFieldHelp("ProductMaintenance_Update");
		break;
	case WM_USER+2:
		helpService.ShowFieldHelp("ProductMaintenance_Delete");
		break;
	case WM_USER+3:
		helpService.ShowFieldHelp("ProductMaintenance_Clear");
		break;
	default:
		this->PressButton(PSBTN_HELP);
		break;
	}

	return FALSE;
}


// Build the header for the product list
// Put the basic info, wms id, description in front,
// followed by anything they queried by, followed
// by the rest of the data
void CProductSheet::BuildHeaderLine(CString &header)
{
	CString temp, temp2;
	CMapStringToString map;		// this is used to keep track of what we've already displayed
	CProductAttribute *pAttr;

	header.Format("WMS Product ID|WMS Product Detail ID|Product Name|");
	for (int i=0; i < m_UsedAttributeList.GetSize(); ++i) {
		// m_UsedAttributeList contains display names of columns used in query
		
		// Don't display these again - we always show them first
		if (m_UsedAttributeList[i].CompareNoCase("WMS Product ID") == 0 ||
			m_UsedAttributeList[i].CompareNoCase("WMS Product Detail ID") == 0 ||
			m_UsedAttributeList[i].CompareNoCase("Product Name") == 0)
			continue;

		// Try to find the product attribute based on the display name
		if (! m_ProductDataService.m_ProductAttributeMap.Lookup(m_UsedAttributeList[i], (CObject *&)pAttr))
			continue;

		if (pAttr->m_AttributeDBID > 0) {	// it's a UDF
			header += pAttr->m_Name;
			header += "|";
			temp.Format("UDF:%s", pAttr->m_Name);
			map.SetAt(temp, "dummy");
		}
		else {
			header += m_UsedAttributeList[i];
			header += "|";
			map.SetAt(m_UsedAttributeList[i], "dummy");
		}
	}

	// Add the rest of the attributes if they are not in the attribute list
	if (! map.Lookup("Weight", temp)) { 
		temp.Format("Weight|"); 
		header += temp; 
	}

	if (! map.Lookup("Movement", temp)) { 
		temp.Format("Movement|"); 
		header += temp; 
	}

	if (! map.Lookup("Case Width", temp)) { 
		temp.Format("Case Width|"); 
		header += temp; 
	}

	if (! map.Lookup("Case Length", temp)) { 
		temp.Format("Case Length|"); 
		header += temp; 
	}
	if (! map.Lookup("Case Height", temp)) { 
		temp.Format("Case Height|"); 
		header += temp; 
	}
	if (! map.Lookup("Hazard Flag", temp)) { 
		temp.Format("Hazard Flag|"); 
		header += temp; 
	}
	if (! map.Lookup("Unit of Issue", temp)) { 
		temp.Format("Unit of Issue|"); 
		header += temp; 
	}
	if (! map.Lookup("Pick-to-Belt Flag", temp)) { 
		temp.Format("Pick-to-Belt Flag|"); 
		header += temp; 
	}
	if (! map.Lookup("Optimize Method", temp)) { 
		temp.Format("Optimize Method|"); 
		header += temp; 
	}
	if (! map.Lookup("Balance on Hand", temp)) { 
		temp.Format("Balance on Hand|"); 
		header += temp; 
	}
	if (! map.Lookup("Number In Pallet", temp)) { 
		temp.Format("Number In Pallet|"); 
		header += temp; 
	}
	if (! map.Lookup("Allow  Height-Length Swap", temp)) { 
		temp.Format("Allow  Height-Length Swap|"); 
		header += temp; 
	}
	if (! map.Lookup("Allow Height-Width Swap", temp)) { 
		temp.Format("Allow Height-Width Swap|"); 
		header += temp; 
	}
	if (! map.Lookup("Allow Width-Length Swap", temp)) { 
		temp.Format("Allow Width-Length Swap|"); 
		header += temp;
	}
	if (! map.Lookup("Each Width", temp)) { 
		temp.Format("Each Width|"); 
		header += temp; 
	}
	if (! map.Lookup("Each Length", temp)) { 
		temp.Format("Each Length|"); 
		header += temp; 
	}
	if (! map.Lookup("Each Height", temp)) { 
		temp.Format("Each Height|"); 
		header += temp; 
	}
	if (! map.Lookup("Inner Width", temp)) {
		temp.Format("Inner Width|"); 
		header += temp; 
	}
	if (! map.Lookup("Inner Length", temp)) { 
		temp.Format("Inner Length|");
		header += temp;
	}
	if (! map.Lookup("Inner Height", temp)) { 
		temp.Format("Inner Height|");
		header += temp; 
	}
	if (! map.Lookup("Case Pack", temp)) {
		temp.Format("Case Pack|"); 
		header += temp; 
	}
	if (! map.Lookup("Inner Pack", temp)) {
		temp.Format("Inner Pack|");
		header += temp;
	}
	if (! map.Lookup("Number of Hits", temp)) { 
		temp.Format("Number of Hits|");
		header += temp; 
	}
	if (! map.Lookup("Container Width", temp)) {
		temp.Format("Container Width|");
		header += temp;
	}
	if (! map.Lookup("Container Length", temp)) {
		temp.Format("Container Length|"); 
		header += temp;
	}
	if (! map.Lookup("Container Height", temp)) { 
		temp.Format("Container Height|");
		header += temp; 
	}

	if (! map.Lookup("Container Height Override Flag", temp)) { 
		temp.Format("Container Height Override Flag|"); 
		header += temp; 
	}
	if (! map.Lookup("Storage TI", temp)) { 
		temp.Format("Storage TI|"); 
		header += temp;
	}
	if (! map.Lookup("Storage HI", temp)) {
		temp.Format("Storage HI|"); 
		header += temp; 
	}

	return;

}

void CProductSheet::BuildDisplayLine(CString &line, CProductPack &product)
{
	CString temp, temp2, columnName;
	CProductAttribute *pAttr;
	CMapStringToString map;

	line.Format("WMS Product ID|WMS Product Detail ID|Product Name|");
	line.Format("%s|%s|%s|", product.m_WMSProductID, product.m_WMSProductDetailID,
		product.m_Description);

	for (int i=0; i < m_UsedAttributeList.GetSize(); ++i) {

		// Don't display these again - we always show them first
		if (m_UsedAttributeList[i].CompareNoCase("WMS Product ID") == 0 ||
			m_UsedAttributeList[i].CompareNoCase("WMS Product Detail ID") == 0 ||
			m_UsedAttributeList[i].CompareNoCase("Product Name") == 0)
			continue;

		if (! m_ProductDataService.m_ProductAttributeMap.Lookup(m_UsedAttributeList[i], (CObject *&)pAttr))
			continue;

		if (pAttr->m_AttributeDBID > 0) {	// it's a UDF
			for (int j=0; j < product.m_UDFList.GetSize(); ++j) {
				if (product.m_UDFList[j]->m_ListID == pAttr->m_AttributeDBID) {
					line += product.m_UDFList[j]->m_Value;
					line += "|";
					temp.Format("UDF:%s", pAttr->m_Name);
					map.SetAt(temp, "dummy");
					break;
				}
			}
		}
		else {
			product.GetValueByName(temp2, pAttr->m_Name);
			if (pAttr->m_Type == DT_LIST) {
				pAttr->m_InternalToDisplayMap.Lookup(temp, temp2);
			}
			line += temp2;
			line += "|";
			map.SetAt(m_UsedAttributeList[i], "dummy");
		}
	}

	// Add the rest of the attributes if they are not in the attribute list
	if (! map.Lookup("Weight", temp)) { 
		temp.Format("%9.2f|", product.m_Weight); 
		line += temp; 
	}
	if (! map.Lookup("Movement", temp)) { 
		temp.Format("%9.2f|", product.m_Movement); 
		line += temp; 
	}
	if (! map.Lookup("Case Width", temp)) { 
		temp.Format("%9.2f|", product.m_CaseWidth); 
		line += temp; 
	}
	if (! map.Lookup("Case Length", temp)) { 
		temp.Format("%9.2f|", product.m_CaseLength); 
		line += temp; 
	}
	if (! map.Lookup("Case Height", temp)) { 
		temp.Format("%9.2f|", product.m_CaseHeight); 
		line += temp; 
	}
	if (! map.Lookup("Hazard Flag", temp))
		line += (product.m_IsHazard) ? "True|" : "False|";


	if (! map.Lookup("Unit of Issue", temp)) {
		switch (product.m_UnitOfIssue) {
		case UOI_EACH:
			line += "Each|";
			break;
		case UOI_INNER:
			line += "Inner|";
			break;
		case UOI_CASE:
			line += "Case|";
			break;
		case UOI_PALLET:
			line += "Pallet|";
			break;
		}
	}

	if (! map.Lookup("Pick-to-Belt Flag", temp))
		line += (product.m_IsPickToBelt) ? "True|" : "False|";
		
	if (! map.Lookup("Optimize Method", temp))
		line += (product.m_OptimizeBy == 0) ? "Cube|" : "Labor|";

	if (! map.Lookup("Balance on Hand", temp)) { 
		temp.Format("%9.2f|", product.m_BalanceOnHand); 
		line += temp; 
	}
	if (! map.Lookup("Number In Pallet", temp)) { 
		temp.Format("%d|", product.m_NumberInPallet); 
		line += temp; 
	}
	if (! map.Lookup("Allow  Height-Length Swap", temp)) 
		line += (product.m_RotateXAxis) ? "True|" : "False|";

	if (! map.Lookup("Allow Height-Width Swap", temp))
		line += (product.m_RotateYAxis) ? "True|" : "False|";

	if (! map.Lookup("Allow Width-Length Swap", temp))
		line += (product.m_RotateZAxis) ? "True|" : "False|";

	if (! map.Lookup("Each Width", temp)) { 
		temp.Format("%9.2f|", product.m_EachWidth); 
		line += temp; 
	}
	if (! map.Lookup("Each Length", temp)) { 
		temp.Format("%9.2f|", product.m_EachLength); 
		line += temp; 
	}
	if (! map.Lookup("Each Height", temp)) { 
		temp.Format("%9.2f|", product.m_EachHeight); 
		line += temp; 
	}
	if (! map.Lookup("Inner Width", temp)) {
		temp.Format("%9.2f|", product.m_InnerWidth); 
		line += temp; 
	}
	if (! map.Lookup("Inner Length", temp)) { 
		temp.Format("%9.2f|", product.m_InnerLength);
		line += temp;
	}
	if (! map.Lookup("Inner Height", temp)) { 
		temp.Format("%9.2f|", product.m_InnerHeight);
		line += temp; 
	}
	if (! map.Lookup("Case Pack", temp)) {
		temp.Format("%d|",product.m_CasePack); 
		line += temp; 
	}
	if (! map.Lookup("Inner Pack", temp)) {
		temp.Format("%d|", product.m_InnerPack);
		line += temp;
	}
	if (! map.Lookup("Number of Hits", temp)) { 
		temp.Format("%9.2f|", product.m_NumberOfHits);
		line += temp; 
	}
	if (! map.Lookup("Container Width", temp)) {
		temp.Format("%9.2f|", product.m_Container.m_Width);
		line += temp;
	}
	if (! map.Lookup("Container Length", temp)) {
		temp.Format("%9.2f|", product.m_Container.m_Length); 
		line += temp;
	}
	if (! map.Lookup("Container Height", temp)) { 
		temp.Format("%9.2f|", product.m_Container.m_Height);
		line += temp; 
	}

	if (! map.Lookup("Container Height Override Flag", temp))
		line += (product.m_Container.m_IsHeightOverride) ? "True|" : "False|";

	if (! map.Lookup("Storage TI", temp)) { 
		temp.Format("%d|", product.m_Container.m_Ti); 
		line += temp;
	}
	if (! map.Lookup("Storage HI", temp)) {
		temp.Format("%d|", product.m_Container.m_Hi); 
		line += temp; 
	}

}

void CProductSheet::OnColorProducts(WPARAM wParam, LPARAM lParam)
{
	UNREFERENCED_PARAMETER(wParam);

	CStringArray dbids;
	CString temp;
	CStringArray handles;

	if (lParam == 0) {
		AfxMessageBox("Please select at least on product to color.");
		return;
	}

	for (int i=0; i < m_ProductListDialog->m_SelectionList.GetSize(); ++i) {
		int curSel = m_ProductListDialog->m_SelectionList[i];
		temp.Format("%d", m_Products.GetAt(curSel)->m_ProductPackDBID);
		dbids.Add(temp);
	}

	m_ProductDataService.GetBayHandlesForProducts(dbids, handles);
	
	int colorIdx = CAutoCADCommands::GetColorChoice();

	CAutoCADCommands::ColorAllObjects();

	for (i=0; i < handles.GetSize(); ++i) {
		handles[i].TrimRight("|");
		CAutoCADCommands::ColorDrawingObjectByHandle(handles[i], colorIdx);
	}

	actrTransactionManager->flushGraphics();
	acedUpdateDisplay();
}


void CProductSheet::OnMove(int x, int y) 
{
	CPropertySheet::OnMove(x, y);
	/*
	CRect r;
	CString saveRect;
	this->GetWindowRect(&r);
	saveRect.Format("%d|%d|%d|%d|",
		r.top, r.bottom, r.left, r.right);

	if (m_ProductListDialog != NULL)
		SetApplicationData("MainRectList", saveRect, "Dialogs\\ProductMaintenance");
	else
		SetApplicationData("MainRectNoList", saveRect, "Dialogs\\ProductMaintenance");
	*/
}



void CProductOptimizePage::OnViewFacings() 
{
	CProductDataService productDataService;
	CStringArray facingList;
	UpdateData(TRUE);

	CProductSheet *pParent = (CProductSheet *)GetParent();


	if (pParent->m_CurrentProductIdx < 0) {
		AfxMessageBox("Please select a product by using the Query button.");
		return;
	}
	
	productDataService.GetProductFacings(pParent->m_CurrentProduct.m_ProductPackDBID, facingList,
		CProductDataService::Baseline);

	if (facingList.GetSize() == 0) {
		AfxMessageBox("This product has no additional facings.");
		return;
	}

	CString display;
	for (int i=0; i < facingList.GetSize(); ++i) {
		facingList[i].TrimRight("|");
		display += facingList[i];
		display += "\r\n";
	}

	::MessageBox(this->m_hWnd, display, "Non-primary Facings", 0);
}

void CProductOptimizePage::OnViewOptimizedFacings() 
{
	CProductDataService productDataService;
	CStringArray facingList;
	UpdateData(TRUE);

	CProductSheet *pParent = (CProductSheet *)GetParent();


	if (pParent->m_CurrentProductIdx < 0) {
		AfxMessageBox("Please select a product by using the Query button.");
		return;
	}
	
	productDataService.GetProductFacings(pParent->m_CurrentProduct.m_ProductPackDBID, facingList, 
		CProductDataService::Optimize);

	if (facingList.GetSize() == 0) {
		AfxMessageBox("This product has no additional facings.");
		return;
	}

	CString display;
	for (int i=0; i < facingList.GetSize(); ++i) {
		facingList[i].TrimRight("|");
		display += facingList[i];
		display += "\r\n";
	}

	::MessageBox(this->m_hWnd, display, "Non-primary Facings", 0);
}


void CProductOptimizePage::OnEditchangeOptimizedLocation() 
{
	UpdateData(TRUE);
	SetLocationControls();	
}

void CProductOptimizePage::OnSelchangeOptimizedLocation() 
{
	CEdit *pEdit;

	UpdateData(TRUE);

	if ( (m_OptimizedLocationCtl.GetCurSel() == -1) ||
		(m_OptimizedLocationCtl.GetCurSel() == 1) ) {
		m_OptimizedBayProfileCtl.EnableWindow(FALSE);
		m_OptimizedLocationProductGroupCtl.EnableWindow(FALSE);
		m_OptimizedLevelTypeCtl.EnableWindow(FALSE);
		pEdit = (CEdit *)GetDlgItem(IDC_ROTATED_WIDTH);
		pEdit->EnableWindow(FALSE);
		pEdit = (CEdit *)GetDlgItem(IDC_ROTATED_LENGTH);
		pEdit->EnableWindow(FALSE);
		pEdit = (CEdit *)GetDlgItem(IDC_ROTATED_HEIGHT);
		pEdit->EnableWindow(FALSE);
	}
	else {
		m_OptimizedBayProfileCtl.EnableWindow(TRUE);
		m_OptimizedLocationProductGroupCtl.EnableWindow(TRUE);
		m_OptimizedLevelTypeCtl.EnableWindow(TRUE);
		pEdit = (CEdit *)GetDlgItem(IDC_ROTATED_WIDTH);
		pEdit->EnableWindow(TRUE);
		pEdit = (CEdit *)GetDlgItem(IDC_ROTATED_LENGTH);
		pEdit->EnableWindow(TRUE);
		pEdit = (CEdit *)GetDlgItem(IDC_ROTATED_HEIGHT);
		pEdit->EnableWindow(TRUE);
	}	
}


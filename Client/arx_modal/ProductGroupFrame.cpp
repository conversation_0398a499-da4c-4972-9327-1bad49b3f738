// ProductGroupFrame.cpp : implementation file
//

#include "stdafx.h"
#include "modal.h"
#include "ProductGroupFrame.h"
#include "ProductGroupDialog.h"
#include "ProductGroupNavigator.h"
#include "ProductGroupCriteriaMaintenance.h"
#include "ProductGroupCriteriaMatrix.h"
#include "ProductGroupAssignmentDialog.h"

#ifdef _DEBUG
#define new DEBUG_NEW
#undef THIS_FILE
static char THIS_FILE[] = __FILE__;
#endif

/////////////////////////////////////////////////////////////////////////////
// CProductGroupFrame

IMPLEMENT_DYNCREATE(CProductGroupFrame, CFrameWnd)

CProductGroupFrame::CProductGroupFrame()
{
}

CProductGroupFrame::~CProductGroupFrame()
{
}


BEGIN_MESSAGE_MAP(CProductGroupFrame, CFrameWnd)
	//{{AFX_MSG_MAP(CProductGroupFrame)
		// NOTE - the ClassWizard will add and remove mapping macros here.
	//}}AFX_MSG_MAP
END_MESSAGE_MAP()

/////////////////////////////////////////////////////////////////////////////
// CProductGroupFrame message handlers

BOOL CProductGroupFrame::OnCreateClient(LPCREATESTRUCT lpcs, CCreateContext* pContext) 
{
	
	UNREFERENCED_PARAMETER(lpcs);
	UNREFERENCED_PARAMETER(pContext);
	CRect rect;

	if (! m_Splitter.CreateStatic(this, 1, 2)) {
		AfxMessageBox("Could not create splitter.");
		return FALSE;
	}

	if (! m_Splitter.CreateView(0, 0, RUNTIME_CLASS(CProductGroupNavigator), CSize(0, 0), NULL)) {
		AfxMessageBox("Could not create first view.");
		return FALSE;
	}

	
	if (! m_Splitter.CreateView(0, 1, RUNTIME_CLASS(CProductGroupDialog), CSize(0, 0), NULL)) {
		AfxMessageBox("Could not create second view.");
		return FALSE;
	}

	/*
	if (! m_Splitter.CreateView(0, 2, RUNTIME_CLASS(CProductGroupCriteriaMaintenance), CSize(0, 0), NULL)) {
		AfxMessageBox("Could not create third view.");
		return FALSE;
	}

	if (! m_Splitter.CreateView(0, 3, RUNTIME_CLASS(CProductGroupCriteriaMatrix), CSize(0, 0), NULL)) {
		AfxMessageBox("Could not create third view.");
		return FALSE;
	}
	
	if (! m_Splitter.CreateView(0, 4, RUNTIME_CLASS(CProductGroupAssignmentDialog), CSize(0, 0), NULL)) {
		AfxMessageBox("Could not create third view.");
		return FALSE;
	}
	*/


	GetWindowRect( &rect );
	m_Splitter.SetColumnInfo(0, rect.Width()/4, 10);
	m_Splitter.SetColumnInfo(1, rect.Width()*3/4, 10);
	/*
	m_Splitter.SetColumnInfo(2, 0, 0);
	m_Splitter.SetColumnInfo(3, 0, 0);
	m_Splitter.SetColumnInfo(4, 0, 0);
	*/
	m_Splitter.RecalcLayout();

//	return CFrameWnd::OnCreateClient(lpcs, pContext);
	return TRUE;
}

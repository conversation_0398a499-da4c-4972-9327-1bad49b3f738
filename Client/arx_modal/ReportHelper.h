// ReportHelper.h: interface for the CReportHelper class.
//
//////////////////////////////////////////////////////////////////////

#if !defined(AFX_REPORTHELPER_H__A4A36DE3_B9D2_4210_87AF_27854229BBF2__INCLUDED_)
#define AFX_REPORTHELPER_H__A4A36DE3_B9D2_4210_87AF_27854229BBF2__INCLUDED_

#if _MSC_VER > 1000
#pragma once
#endif // _MSC_VER > 1000

class CReportHelper  
{
public:
	CReportHelper();
	virtual ~CReportHelper();

	void ExecuteReports(int repNum);
	void OpenSavedRep();
	void RackAssignmentRep();
	void RackAssignmentDetailRep();
	void ProductGroupDefineRep();
	void ProductGroupLayoutRep();
	void ProductGroupFacingsRep();
	void ProductsLayoutAssignmentRep();
	void ProductsLayoutVarWidthLocRep();
	void ProductsLayoutCaseReOrientRep();
	void FacilityMoveChainsRep();
	void LocationOutboundRep();
	void AssignmentOutboundRep();
	void ProductDetailRep();
	void CostAnalysisDetRep();
	void CostAnalysisSumRep();
	void ProductsLayoutAssignmentByProductRep();
	void ProductsLayoutAssignmentByLocationRep();
	void ProductGroupDefineByMovementRep();
	void ProductGroupDefineByBOHRep();
	void ProductGroupDefineByUOIRep();
	void RackUsageSummaryRep();
	void UnassignedProductsRep();
	void CapitalCostRejectionRep();
};

#endif // !defined(AFX_REPORTHELPER_H__A4A36DE3_B9D2_4210_87AF_27854229BBF2__INCLUDED_)

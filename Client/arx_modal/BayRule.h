// BayRule.h: interface for the CBayRule class.
//
//////////////////////////////////////////////////////////////////////

#if !defined(AFX_BAYRULE_H__D67FADD8_0016_424D_BD09_CB7FAACB6210__INCLUDED_)
#define AFX_BAYRULE_H__D67FADD8_0016_424D_BD09_CB7FAACB6210__INCLUDED_

#if _MSC_VER > 1000
#pragma once
#endif // _MSC_VER > 1000

#include "FacingInfo.h"

class CBayRule : public CObject  
{
public:
	CBayRule();
	CBayRule(const CBayRule& other);
	CBayRule& operator=(const CBayRule &other);
	BOOL operator==(const CBayRule& other);
	BOOL operator!=(const CBayRule& other) { return !(*this == other);};
	virtual ~CBayRule();

	int Parse(CString &line);
	
	int m_BayRuleDBId;
	CString m_Description;
	double m_PalletHeight;
	double m_PctUtilSelPos;
	double m_PctUtilRsvPos;
	double m_PctRsvToSelPos;
	double m_DesiredRplnPerWeek;
	double m_Clearance;
	double m_AdditionalRsvCube;
	int m_Baytype;
	double m_PercentReserves;
	int m_BayProfileDBId;

	CTypedPtrArray<CObArray, CFacingInfo*> m_FacingInfoList;

};

#endif // !defined(AFX_BAYRULE_H__D67FADD8_0016_424D_BD09_CB7FAACB6210__INCLUDED_)

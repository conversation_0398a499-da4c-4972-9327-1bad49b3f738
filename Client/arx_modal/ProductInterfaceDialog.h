#if !defined(AFX_PRODUCTINTERFACEDIALOG_H__DCF6764A_CA49_487D_BCC6_3BC844338C20__INCLUDED_)
#define AFX_PRODUCTINTERFACEDIALOG_H__DCF6764A_CA49_487D_BCC6_3BC844338C20__INCLUDED_

#if _MSC_VER > 1000
#pragma once
#endif // _MSC_VER > 1000
// ProductInterfaceDialog.h : header file
//

/////////////////////////////////////////////////////////////////////////////
// CProductInterfaceDialog dialog

class CProductInterfaceDialog : public CDialog
{
// Construction
public:
	CProductInterfaceDialog(CWnd* pParent = NULL);   // standard constructor

// Dialog Data
	//{{AFX_DATA(CProductInterfaceDialog)
	enum { IDD = IDD_PRODUCT_INTERFACE };
	CComboBox	m_MapNameListCtrl;
	CString	m_FileName;
	//}}AFX_DATA


// Overrides
	// ClassWizard generated virtual function overrides
	//{{AFX_VIRTUAL(CProductInterfaceDialog)
	protected:
	virtual void DoDataExchange(CDataExchange* pDX);    // DDX/DDV support
	//}}AFX_VIRTUAL

// Implementation
protected:

	// Generated message map functions
	//{{AFX_MSG(CProductInterfaceDialog)
	virtual BOOL OnInitDialog();
	virtual void OnOK();
	virtual void OnCancel();
	afx_msg void OnHelp();
	afx_msg BOOL OnHelpInfo(HELPINFO* pHelpInfo);
	afx_msg void OnDefineMapping();
	afx_msg void OnBrowse();
	afx_msg void OnValidate();
	//}}AFX_MSG
	DECLARE_MESSAGE_MAP()
};

//{{AFX_INSERT_LOCATION}}
// Microsoft Visual C++ will insert additional declarations immediately before the previous line.

#endif // !defined(AFX_PRODUCTINTERFACEDIALOG_H__DCF6764A_CA49_487D_BCC6_3BC844338C20__INCLUDED_)

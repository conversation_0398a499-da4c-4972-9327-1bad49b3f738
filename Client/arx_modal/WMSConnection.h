// WMSConnection.h: interface for the CWMSConnection class.
//
//////////////////////////////////////////////////////////////////////

#if !defined(AFX_WMSCONNECTION_H__68FF7D90_0F1F_48F9_857F_C91290354480__INCLUDED_)
#define AFX_WMSCONNECTION_H__68FF7D90_0F1F_48F9_857F_C91290354480__INCLUDED_

#if _MSC_VER > 1000
#pragma once
#endif // _MSC_VER > 1000

class CWMSConnection : public CObject  
{
public:
	CString ConnectionTypeAsText();
	CWMSConnection();
	CWMSConnection(const CWMSConnection& other);
	CWMSConnection& operator=(const CWMSConnection& other);
	BOOL operator==(const CWMSConnection& other);
	BOOL operator!=(const CWMSConnection&other) { return (! (*this == other)); }
	virtual ~CWMSConnection();

	int Parse(const CString& line);
	
	int m_WMSConnectionDBId;
	CString m_Name;

	int m_ConnectionType;		// Prompt, MQSeries, FTP, Disk, XML-RPC(later)
	
	CString m_Host;				// For ftp, MQ, and xml-rpc
	int m_Port;					// For MQ and xml-rpc

	// MQSeries Only
	CString m_QueueManager;
	CString m_Queue;
	int m_Channel;

	// Ftp Only
	CString m_Login;
	CString m_Password;

	// Ftp, disk
	CString m_Path;
	CString m_FileName;
	CString m_TriggerName;


	typedef enum {
		Prompt,
		Local,
		FTP,
		MQSeries,
		XML_RPC
	} enumConnectionType;

	typedef enum {
		Add,
		Modify,
		Delete,
		ConfirmAdd,
		ConfirmModify,
		ConfirmDelete
	} enumAction;

	typedef enum {
		NotSent,
		Sent,
		Confirmed,
		Rejected
	} enumStatus;
};

#endif // !defined(AFX_WMSCONNECTION_H__68FF7D90_0F1F_48F9_857F_C91290354480__INCLUDED_)

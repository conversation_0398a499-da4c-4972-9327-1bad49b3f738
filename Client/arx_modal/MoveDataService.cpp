// MoveDataService.cpp: implementation of the CMoveDataService class.
//
//////////////////////////////////////////////////////////////////////

#include "stdafx.h"
#include "modal.h"
#include "DataAccessService.h"
#include "MoveDataService.h"
#include "ControlService.h"
#include "UtilityHelper.h"


#ifdef _DEBUG
#undef THIS_FILE
static char THIS_FILE[]=__FILE__;
#define new DEBUG_NEW
#endif

//////////////////////////////////////////////////////////////////////
// Construction/Destruction
//////////////////////////////////////////////////////////////////////

extern CControlService controlService;
extern CDataAccessService dataAccessService;
extern CUtilityHelper utilityHelper;

CMoveDataService::CMoveDataService()
{

}

CMoveDataService::~CMoveDataService()
{

}


int CMoveDataService::GetOutboundMoves(int facilityId, CString &chainList, CStringArray &moveList)
{
	CString sql, temp;
	CStringArray chains;


	utilityHelper.ParseString(chainList, ",", chains);
	
	for (int i = 0; i < ((chains.GetSize() /254) + 1); ++i) {
		temp = "";
		for (int j = 0; (j < 254) && ((i*254+j) < chains.GetSize()); ++j) {
			temp += chains[i*254+j];
			temp += ",";
		}
		temp.TrimRight(",");
		
		chainList = "";
		if (temp != "")
			chainList.Format("and chain in (%s) ", temp);

		sql = "";
		
		// moves where the from and to exist and are valid locations
		sql.Format("select chain, chainsequence, wmsproductid, wmsproductdetailid, "
			"fl.description, tl.description, movetype, isfloating, dbaisle.dbsectionid "
			"from dbproduct, dbproductpack, dbmove, dblocation fl, dblocationf tl, "
			"dblevel, dbbay, dbbayprofile, dbside, dbaisle "
			"where dbproduct.dbfacilityid = %d "
			"and dbproductpack.dbproductid = dbproduct.dbproductid "
			"and dbmove.dbproductpackid = dbproductpack.dbproductpackid "
			"and fl.dblocationid = dbmove.fromlocationid "
			"and tl.dblocationid = dbmove.tolocationid "
			"and tl.dbfacilityid = dbproduct.dbfacilityid "
			"and tl.dblevelid = dblevel.dblevelid "
			"and dblevel.dbbayid = dbbay.dbbayid "
			"and dbbay.dbbayprofileid = dbbayprofile.dbbayprofileid "
			"and dbbay.dbsideid = dbside.dbsideid "
			"and dbside.dbaisleid = dbaisle.dbaisleid "
			"%s "
			"UNION "
			// moves where the tolocation is less than 0; these are delete facings
			"select chain, chainsequence, wmsproductid, wmsproductdetailid, "
			"fl.description, 'DeleteFacing', movetype, isfloating, dbaisle.dbsectionid "
			"from dbproduct, dbproductpack, dbmove, dblocation fl, "
			"dblevel, dbbay, dbbayprofile, dbside, dbaisle "
			"where dbproduct.dbfacilityid = %d "
			"and dbproductpack.dbproductid = dbproduct.dbproductid "
			"and dbmove.dbproductpackid = dbproductpack.dbproductpackid "
			"and fl.dblocationid = dbmove.fromlocationid "
			//"and not exists ( select dblocationid from dblocation tl where tl.dblocationid = dbmove.tolocationid) "
			"and dbmove.tolocationid < 0 "
			"and fl.dblevelid = dblevel.dblevelid "
			"and dblevel.dbbayid = dbbay.dbbayid "
			"and dbbay.dbsideid = dbside.dbsideid "
			"and dbside.dbaisleid = dbaisle.dbaisleid "
			"and dbbay.dbbayprofileid = dbbayprofile.dbbayprofileid "
			"%s "
			"UNION "
			// moves where the from loc is less than zero
			// these are add facings
			"select chain, chainsequence, wmsproductid, wmsproductdetailid, "
			"'AddFacing', tl.description, movetype, isfloating, dbaisle.dbsectionid "
			"from dbproduct, dbproductpack, dbmove, dblocation tl, "
			"dblevel, dbbay, dbbayprofile, dbside, dbaisle "
			"where dbproduct.dbfacilityid = %d "
			"and dbproductpack.dbproductid = dbproduct.dbproductid "
			"and dbmove.dbproductpackid = dbproductpack.dbproductpackid "
			"and tl.dblocationid = dbmove.tolocationid "
			//"and not exists ( select dblocationid from dblocation fl where fl.dblocationid = dbmove.fromlocationid) "
			"and dbmove.fromlocationid < 0 "
			"and tl.dblevelid = dblevel.dblevelid "
			"and dblevel.dbbayid = dbbay.dbbayid "
			"and dbbay.dbbayprofileid = dbbayprofile.dbbayprofileid "
			"and dbbay.dbsideid = dbside.dbsideid "
			"and dbside.dbaisleid = dbaisle.dbaisleid "
			"%s "
			/*  The following may not be able to happen now that we are using the same facility for from and to
			"UNION "
			// moves where the to loc is a valid loc but it does not exist in the from facility
			// because it is a new location		
			"select chain, chainsequence, wmsproductid, wmsproductdetailid, "	
			"fl.description, concat(fl2.description, '-NewLoc'), movetype, isfloating, dbaisle.dbsectionid "
			"from dbproduct, dbproductpack, dbmove, dblocation fl, dblocation fl2, "
			"dblevel, dbbay, dbbayprofile, dbside, dbaisle "
			"where dbproduct.dbfacilityid = %d "
			"and dbproductpack.dbproductid = dbproduct.dbproductid "
			"and dbmove.dbproductpackid = dbproductpack.dbproductpackid "
			"and fl.dblocationid = dbmove.fromlocationid "
			"and not exists ( select dblocationid from dblocationf tl "
			"    where tl.dblocationid = dbmove.tolocationid "
			"    and tl.dbfacilityid = %d) "
			"and dbmove.tolocationid > 0 "
			"and fl2.dblocationid = dbmove.tolocationid "
			"and fl.dblevelid = dblevel.dblevelid "
			"and dblevel.dbbayid = dbbay.dbbayid "
			"and dbbay.dbsideid = dbside.dbsideid "
			"and dbside.dbaisleid = dbaisle.dbaisleid "
			"and dbbay.dbbayprofileid = dbbayprofile.dbbayprofileid "
			"%s "
			*/
			"order by 1, 2", 
			facilityId, chainList, facilityId, chainList, facilityId, chainList); //,
		//facilityId, facilityId, temp);
		
		dataAccessService.ExecuteQuery("GetMoves", sql, moveList);
		
	}

	
	return moveList.GetSize();
}

int CMoveDataService::GetMoves(int facilityId, CStringArray &moveList)
{
	CString sql;

	// Get the moves with both a from and a to location unioned with moves with only a from location
	// unioned with only a to location.  If both from and to are not there, ignore

	// normal moves where the from and to both exist in the baseline facility
	sql.Format("select dbmoveid, chain, chainsequence, dbmove.dbproductpackid, wmsproductid, wmsproductdetailid, "
		"dbproductpack.description, fl.dblocationid, fl.description, tl.dblocationid, tl.description, "
		"fromtotalcost, tototalcost, "
		"forkhandletime, forkhandlecost, forktraveldist, forktraveltime, forktravelcost, "
		"stockerhandletime, stockerhandlecost, stockertraveldist, stockertraveltime, stockertravelcost, "
		"movetype, dbmove.status, fl.status, tl.status, dbproductpack.status "
		"from dbmove, dbproductpack, dbproduct, dblocation fl, dblocation tl "
		"where dbproduct.dbfacilityid = %d "
		"and dbproductpack.dbproductid = dbproduct.dbproductid "
		"and dbmove.dbproductpackid = dbproductpack.dbproductpackid "
		"and fl.dblocationid = dbmove.fromlocationid "
		"and tl.dblocationid = dbmove.tolocationid "
		" "
		"UNION "
		// Moves where the to location is less than 0 meaning it is a delete facing
		"select dbmoveid, chain, chainsequence, dbmove.dbproductpackid, wmsproductid, wmsproductdetailid, "
		"dbproductpack.description, fl.dblocationid, fl.description, 0, 'DeleteFacing', "
		"fromtotalcost, 0, "
		"forkhandletime, forkhandlecost, forktraveldist, forktraveltime, forktravelcost, "
		"stockerhandletime, stockerhandlecost, stockertraveldist, stockertraveltime, stockertravelcost, "
		"movetype, dbmove.status, fl.status, -1, dbproductpack.status "
		"from dbmove, dbproductpack, dbproduct, dblocation fl "
		"where dbproduct.dbfacilityid = %d "
		"and dbproductpack.dbproductid = dbproduct.dbproductid "
		"and dbmove.dbproductpackid = dbproductpack.dbproductpackid "
		"and fl.dblocationid = dbmove.fromlocationid "
		"and dbmove.tolocationid < 0 "
		""
		"UNION "
		// moves where the from location is less than 0; these are add facings
		"select dbmoveid, chain, chainsequence, dbmove.dbproductpackid, wmsproductid, wmsproductdetailid, "
		"dbproductpack.description, 0, 'AddFacing', tl.dblocationid, tl.description, "
		"0, tototalcost, "
		"forkhandletime, forkhandlecost, forktraveldist, forktraveltime, forktravelcost, "
		"stockerhandletime, stockerhandlecost, stockertraveldist, stockertraveltime, stockertravelcost, "
		"movetype, dbmove.status, -1, tl.status, dbproductpack.status "
		"from dbmove, dbproductpack, dbproduct, dblocation tl "
		"where dbproduct.dbfacilityid = %d "
		"and dbproductpack.dbproductid = dbproduct.dbproductid "
		"and dbmove.dbproductpackid = dbproductpack.dbproductpackid "
		"and tl.dblocationid = dbmove.tolocationid "
		"and fromlocationid < 0 "
		/*
		"UNION "
		// Moves where the to location is valid but does not exist in the baseline facility
		// these are new locations
		"select dbmoveid, chain, chainsequence, dbmove.dbproductpackid, wmsproductid, wmsproductdetailid, "
		"dbproductpack.description, fl.dblocationid, fl.description, 0, Concat(fl2.description, '-NewLoc'), "
		"fromtotalcost, 0, "
		"forkhandletime, forkhandlecost, forktraveldist, forktraveltime, forktravelcost, "
		"stockerhandletime, stockerhandlecost, stockertraveldist, stockertraveltime, stockertravelcost, "
		"movetype "
		"from dbmove, dbproductpack, dbproduct, dblocation fl, dblocation fl2 "
		"where dbproduct.dbfacilityid = %d "
		"and dbproductpack.dbproductid = dbproduct.dbproductid "
		"and dbmove.dbproductpackid = dbproductpack.dbproductpackid "
		"and fl.dblocationid = dbmove.fromlocationid "
		"and dbmove.tolocationid > 0 "
		"and dbmove.tolocationid = fl2.dblocationid "
		"and not exists (select dblocationid "
		"	from dblocationf tl "
		"	where tl.dblocationid = dbmove.tolocationid "
		"   and tl.dbfacilityid = %d) "
		*/
		" order by 2,3 ", 
		facilityId, facilityId, facilityId); //, facilityId,
		//facilityId);

	return dataAccessService.ExecuteQuery("GetMoves", sql, moveList);

}
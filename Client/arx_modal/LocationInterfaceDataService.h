// LocationInterfaceDataService.h: interface for the CLocationInterfaceDataService class.
//
//////////////////////////////////////////////////////////////////////

#if !defined(AFX_LOCATIONINTERFACEDATASERVICE_H__EBD14FD2_087B_4ADF_9AFE_32C8568DA80A__INCLUDED_)
#define AFX_LOCATIONINTERFACEDATASERVICE_H__EBD14FD2_087B_4ADF_9AFE_32C8568DA80A__INCLUDED_

#if _MSC_VER > 1000
#pragma once
#endif // _MSC_VER > 1000

class CLocationInterfaceDataService  
{
public:
	CLocationInterfaceDataService();
	virtual ~CLocationInterfaceDataService();
	int GetLocationOutboundListBySection(int sectionID, CStringArray &locationList,
		long productGroupID, BO<PERSON> vwOnly);
};

#endif // !defined(AFX_LOCATIONINTERFACEDATASERVICE_H__EBD14FD2_087B_4ADF_9AFE_32C8568DA80A__INCLUDED_)

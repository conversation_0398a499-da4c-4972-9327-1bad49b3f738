// DefineConnectionDialog.cpp : implementation file
//

#include "stdafx.h"
#include "modal.h"
#include "DefineConnectionDialog.h"
#include "ExternalConnection.h"
#include "IntegrationDataService.h"
#include "ControlService.h"
#include "UtilityHelper.h"
#include "HelpService.h"

#ifdef _DEBUG
#define new DEBUG_NEW
#undef THIS_FILE
static char THIS_FILE[] = __FILE__;
#endif

extern CHelpService helpService;
extern CIntegrationDataService integrationDataService;
extern CControlService controlService;
extern CUtilityHelper utilityHelper;
/////////////////////////////////////////////////////////////////////////////
// CDefineConnectionDialog dialog


CDefineConnectionDialog::CDefineConnectionDialog(CWnd* pParent /*=NULL*/)
	: CDialog(CDefineConnectionDialog::IDD, pParent)
{
	//{{AFX_DATA_INIT(CDefineConnectionDialog)
	m_Channel = _T("");
	m_ConfirmPassword = _T("");
	m_FileName = _T("");
	m_Host = _T("");
	m_Login = _T("");
	m_Password = _T("");
	m_Path = _T("");
	m_Port = _T("");
	m_QueueManager = _T("");
	m_TriggerName = _T("");
	m_Name = _T("");
	m_Queue = _T("");
	//}}AFX_DATA_INIT
	m_pConnection = NULL;
}


void CDefineConnectionDialog::DoDataExchange(CDataExchange* pDX)
{
	CDialog::DoDataExchange(pDX);
	//{{AFX_DATA_MAP(CDefineConnectionDialog)
	DDX_Control(pDX, IDC_TRANSPORT, m_TransportCtrl);
	DDX_Text(pDX, IDC_CHANNEL, m_Channel);
	DDX_Text(pDX, IDC_CONFIRM_PASSWORD, m_ConfirmPassword);
	DDX_Text(pDX, IDC_FILE_NAME, m_FileName);
	DDX_Text(pDX, IDC_HOST, m_Host);
	DDX_Text(pDX, IDC_LOGIN, m_Login);
	DDX_Text(pDX, IDC_PASSWORD, m_Password);
	DDX_Text(pDX, IDC_PATH, m_Path);
	DDX_Text(pDX, IDC_PORT, m_Port);
	DDX_Text(pDX, IDC_QUEUE_MANAGER, m_QueueManager);
	DDX_Text(pDX, IDC_TRIGGER_NAME, m_TriggerName);
	DDX_Text(pDX, IDC_CONNECTION_NAME, m_Name);
	DDX_Text(pDX, IDC_QUEUE_NAME, m_Queue);
	//}}AFX_DATA_MAP
}


BEGIN_MESSAGE_MAP(CDefineConnectionDialog, CDialog)
	//{{AFX_MSG_MAP(CDefineConnectionDialog)
	ON_CBN_SELCHANGE(IDC_TRANSPORT, OnSelchangeTransport)
	ON_BN_CLICKED(IDC_TEST, OnTest)
	ON_WM_HELPINFO()
	ON_BN_CLICKED(IDHELP, OnHelp)
	//}}AFX_MSG_MAP
END_MESSAGE_MAP()

/////////////////////////////////////////////////////////////////////////////
// CDefineConnectionDialog message handlers

BOOL CDefineConnectionDialog::OnInitDialog() 
{
	CDialog::OnInitDialog();
	
	HideMQ();
	HideFTP();
	HideLocal();
	GetDlgItem(IDC_TEST)->ShowWindow(SW_HIDE);

	int nItem = m_TransportCtrl.AddString("MQSeries");
	m_TransportCtrl.SetItemData(nItem, CExternalConnection::MQSeries);
	nItem = m_TransportCtrl.AddString("Remote(FTP)");
	m_TransportCtrl.SetItemData(nItem, CExternalConnection::FTP);
	nItem = m_TransportCtrl.AddString("Local Disk");
	m_TransportCtrl.SetItemData(nItem, CExternalConnection::Local);
	nItem = m_TransportCtrl.AddString("Manual (prompt for file)");
	m_TransportCtrl.SetItemData(nItem, CExternalConnection::Prompt);

	CRect r;
	m_TransportCtrl.GetWindowRect(&r);
	m_TransportCtrl.SetWindowPos(NULL, 0, 0, r.Width(), r.Height()*(m_TransportCtrl.GetCount()+1), SWP_NOMOVE|SWP_NOZORDER);

	m_TransportCtrl.SetCurSel(-1);


	if (m_pConnection != NULL) {
		for (int i=0; i < m_TransportCtrl.GetCount(); ++i) {
			if (m_pConnection->m_ConnectionType == (int)m_TransportCtrl.GetItemData(i)) {
				m_TransportCtrl.SetCurSel(i);
				break;
			}
		}

		m_Channel = m_pConnection->m_Channel;
		m_ConfirmPassword = m_pConnection->m_Password;
		m_FileName = m_pConnection->m_FileName;
		m_Host = m_pConnection->m_Host;
		m_Login = m_pConnection->m_Login;
		m_Name = m_pConnection->m_Name;
		m_Password = m_pConnection->m_Password;
		m_Path = m_pConnection->m_Path;
		m_Port.Format("%d", m_pConnection->m_Port);
		m_Queue = m_pConnection->m_Queue;
		m_QueueManager = m_pConnection->m_QueueManager;
		m_TriggerName = m_pConnection->m_TriggerName;

		OnSelchangeTransport();

		UpdateData(FALSE);
	}

	return TRUE;  // return TRUE unless you set the focus to a control
	              // EXCEPTION: OCX Property Pages should return FALSE
}

void CDefineConnectionDialog::OnSelchangeTransport() 
{
	int curSel = m_TransportCtrl.GetCurSel();
	if (curSel < 0)
		return;

	int type = m_TransportCtrl.GetItemData(curSel);

	switch (type) {
	case CExternalConnection::MQSeries:
		ShowMQ();
		break;
	case CExternalConnection::FTP:
		ShowFTP();
		break;
	case CExternalConnection::Local:
		ShowLocal();
		break;
	case CExternalConnection::Prompt:
		ShowPrompt();
		break;
	}
}

void CDefineConnectionDialog::OnTest() 
{
	
}

void CDefineConnectionDialog::OnOK() 
{
	UpdateData(TRUE);

	// Validate
	// Name not blank, name not equal to other
	// MQ
	// Channel, Host, Port, QMgr, Queue not blank
	// Channel, Port valid integers

	// FTP
	// Host, Login, Password, Path, FileName not blank
	// Password = confirm password

	// Local
	// Path, FileName not blank

	if (m_Name == "") {
		AfxMessageBox("Specify a name for the connection.");
		utilityHelper.SetEditControlErrorState(this, IDC_NAME);
		return;
	}

	try {
		integrationDataService.IsConnectionNameUsed(m_Name, 
			(m_pConnection != NULL) ? m_pConnection->m_ExternalConnectionDBId : 0);
	}
	catch (...) {
		controlService.Log("Error checking for duplicate connection name.",
			"Generic exception in IsConnectionNameUsed.\n");
		return;
	}

	int curSel = m_TransportCtrl.GetCurSel();
	if (curSel < 0) {
		AfxMessageBox("You must specify a transport type.");
		return;
	}

	int type = m_TransportCtrl.GetItemData(curSel);
	
	if (type == CExternalConnection::MQSeries) {

		if (m_Host == "") {
			AfxMessageBox("The host on which the queue manager is running must be specified.");
			utilityHelper.SetEditControlErrorState(this, IDC_HOST);
			return;
		}

		if (m_Channel == "") {
			AfxMessageBox("A channel associated with the queue must be specified.");
			utilityHelper.SetEditControlErrorState(this, IDC_CHANNEL);
			return;
		}
		
		if (! utilityHelper.IsInteger(m_Channel)) {
			AfxMessageBox("Channel must be a positive integer.");
			utilityHelper.SetEditControlErrorState(this, IDC_CHANNEL);
			return;
		}

		if (m_QueueManager == "") {
			AfxMessageBox("A queue manager must be specified.");
			utilityHelper.SetEditControlErrorState(this, IDC_QUEUE_MANAGER);
			return;
		}

		if (m_Queue == "") {
			AfxMessageBox("A queue name must be specified.");
			utilityHelper.SetEditControlErrorState(this, IDC_QUEUE_NAME);
			return;
		}

		if (m_Port == "") {
			AfxMessageBox("The port on which the queue is listening must be specified.");
			utilityHelper.SetEditControlErrorState(this, IDC_PORT);
			return;
		}

		if (! utilityHelper.IsInteger(m_Port)) {
			AfxMessageBox("Port must be a positive integer.");
			utilityHelper.SetEditControlErrorState(this, IDC_PORT);
			return;
		}
	}

	else if (type == CExternalConnection::FTP) {

		if (m_Host == "") {
			AfxMessageBox("The remote host must be specified.");
			utilityHelper.SetEditControlErrorState(this, IDC_HOST);
			return;
		}

		if (m_Login == "") {
			AfxMessageBox("The login to the remote host must be specified.");
			utilityHelper.SetEditControlErrorState(this, IDC_LOGIN);
			return;
		}
		
		if (m_Password == "") {
			AfxMessageBox("The password associated with the login must be specified.");
			utilityHelper.SetEditControlErrorState(this, IDC_PASSWORD);
			return;
		}

		if (m_ConfirmPassword == "") {
			AfxMessageBox("A confirmation password must be specified.");
			utilityHelper.SetEditControlErrorState(this, IDC_CONFIRM_PASSWORD);
			return;
		}

		if (m_Path == "") {
			AfxMessageBox("The path on the remote host must be specified.");
			utilityHelper.SetEditControlErrorState(this, IDC_PATH);
			return;
		}

		if (m_FileName == "") {
			AfxMessageBox("The filename on the remote host must be specified.");
			utilityHelper.SetEditControlErrorState(this, IDC_FILE_NAME);
			return;
		}

		if (m_Password != m_ConfirmPassword) {
			AfxMessageBox("The passwords do not match.");
			utilityHelper.SetEditControlErrorState(this, IDC_CONFIRM_PASSWORD);
			return;
		}

	}
	else if (type == CExternalConnection::Local) {
		if (m_Path == "") {
			AfxMessageBox("The path on the local machine (or a mapped drive) must be specified.");
			utilityHelper.SetEditControlErrorState(this, IDC_PATH);
			return;
		}
		
		if (m_FileName == "") {
			AfxMessageBox("The filename on the local machine must be specified.");
			utilityHelper.SetEditControlErrorState(this, IDC_FILE_NAME);
			return;
		}
	}

	m_pConnection->m_Channel = m_Channel;
	m_pConnection->m_FileName = m_FileName;
	m_pConnection->m_Host = m_Host;
	m_pConnection->m_Login = m_Login;
	m_pConnection->m_Name = m_Name;
	m_pConnection->m_Password = m_Password;
	m_pConnection->m_Path = m_Path;
	if (m_Port != "")
		m_pConnection->m_Port = atoi(m_Port);
	m_pConnection->m_Queue = m_Queue;
	m_pConnection->m_QueueManager = m_QueueManager;
	m_pConnection->m_TriggerName = m_TriggerName;
	m_pConnection->m_ConnectionType = m_TransportCtrl.GetItemData(m_TransportCtrl.GetCurSel());

	if (m_pConnection->m_ConnectionType == CExternalConnection::MQSeries)
		m_pConnection->m_FileName = m_Queue;

	try {
		integrationDataService.StoreExternalConnection(*m_pConnection);
	}
	catch (...) {
		controlService.Log("Error storing connection.", "Generic exception in StoreWMSConnection.\n");
		return;
	}

	CDialog::OnOK();
}

void CDefineConnectionDialog::HideMQ()
{
	GetDlgItem(IDC_HOST)->ShowWindow(SW_HIDE);
	GetDlgItem(IDC_PORT)->ShowWindow(SW_HIDE);
	GetDlgItem(IDC_QUEUE_MANAGER)->ShowWindow(SW_HIDE);
	GetDlgItem(IDC_CHANNEL)->ShowWindow(SW_HIDE);
	GetDlgItem(IDC_QUEUE_NAME)->ShowWindow(SW_HIDE);

	GetDlgItem(IDC_HOST_STATIC)->ShowWindow(SW_HIDE);
	GetDlgItem(IDC_PORT_STATIC)->ShowWindow(SW_HIDE);
	GetDlgItem(IDC_QUEUE_MANAGER_STATIC)->ShowWindow(SW_HIDE);
	GetDlgItem(IDC_CHANNEL_STATIC)->ShowWindow(SW_HIDE);
	GetDlgItem(IDC_QUEUE_NAME_STATIC)->ShowWindow(SW_HIDE);

}

void CDefineConnectionDialog::HideFTP()
{
	GetDlgItem(IDC_HOST)->ShowWindow(SW_HIDE);
	GetDlgItem(IDC_LOGIN)->ShowWindow(SW_HIDE);
	GetDlgItem(IDC_PASSWORD)->ShowWindow(SW_HIDE);
	GetDlgItem(IDC_PATH)->ShowWindow(SW_HIDE);
	GetDlgItem(IDC_FILE_NAME)->ShowWindow(SW_HIDE);
	GetDlgItem(IDC_TRIGGER_NAME)->ShowWindow(SW_HIDE);
	GetDlgItem(IDC_CONFIRM_PASSWORD)->ShowWindow(SW_HIDE);

	GetDlgItem(IDC_HOST_STATIC)->ShowWindow(SW_HIDE);
	GetDlgItem(IDC_LOGIN_STATIC)->ShowWindow(SW_HIDE);
	GetDlgItem(IDC_PASSWORD_STATIC)->ShowWindow(SW_HIDE);
	GetDlgItem(IDC_PATH_STATIC)->ShowWindow(SW_HIDE);
	GetDlgItem(IDC_FILE_NAME_STATIC)->ShowWindow(SW_HIDE);
	GetDlgItem(IDC_TRIGGER_NAME_STATIC)->ShowWindow(SW_HIDE);
	GetDlgItem(IDC_CONFIRM_PASSWORD_STATIC)->ShowWindow(SW_HIDE);
}

void CDefineConnectionDialog::HideLocal()
{
	GetDlgItem(IDC_PATH)->ShowWindow(SW_HIDE);
	GetDlgItem(IDC_FILE_NAME)->ShowWindow(SW_HIDE);
	GetDlgItem(IDC_TRIGGER_NAME)->ShowWindow(SW_HIDE);

	GetDlgItem(IDC_PATH_STATIC)->ShowWindow(SW_HIDE);
	GetDlgItem(IDC_FILE_NAME_STATIC)->ShowWindow(SW_HIDE);
	GetDlgItem(IDC_TRIGGER_NAME_STATIC)->ShowWindow(SW_HIDE);
}

void CDefineConnectionDialog::ShowMQ()
{
	HideFTP();
	HideLocal();

	GetDlgItem(IDC_HOST)->ShowWindow(SW_SHOW);
	GetDlgItem(IDC_PORT)->ShowWindow(SW_SHOW);
	GetDlgItem(IDC_QUEUE_MANAGER)->ShowWindow(SW_SHOW);
	GetDlgItem(IDC_CHANNEL)->ShowWindow(SW_SHOW);
	GetDlgItem(IDC_QUEUE_NAME)->ShowWindow(SW_SHOW);
	GetDlgItem(IDC_TEST)->ShowWindow(SW_SHOW);

	GetDlgItem(IDC_HOST_STATIC)->ShowWindow(SW_SHOW);
	GetDlgItem(IDC_PORT_STATIC)->ShowWindow(SW_SHOW);
	GetDlgItem(IDC_QUEUE_MANAGER_STATIC)->ShowWindow(SW_SHOW);
	GetDlgItem(IDC_CHANNEL_STATIC)->ShowWindow(SW_SHOW);
	GetDlgItem(IDC_QUEUE_NAME_STATIC)->ShowWindow(SW_SHOW);

}

void CDefineConnectionDialog::ShowFTP()
{
	HideMQ();
	HideLocal();

	CRect r, r2;

	GetDlgItem(IDC_QUEUE_MANAGER)->GetWindowRect(&r2);
	this->ScreenToClient(&r2);
	
	GetDlgItem(IDC_LOGIN)->GetWindowRect(&r);
	this->ScreenToClient(&r);

	int offset = r2.top - r.top;
	
	OffsetControl(IDC_LOGIN, offset);
	OffsetControl(IDC_PASSWORD, offset);
	OffsetControl(IDC_PATH, offset);
	OffsetControl(IDC_FILE_NAME, offset);
	OffsetControl(IDC_TRIGGER_NAME, offset);
	OffsetControl(IDC_CONFIRM_PASSWORD, offset);
	OffsetControl(IDC_LOGIN_STATIC, offset);
	OffsetControl(IDC_PASSWORD_STATIC, offset);
	OffsetControl(IDC_PATH_STATIC, offset);
	OffsetControl(IDC_FILE_NAME_STATIC, offset);
	OffsetControl(IDC_TRIGGER_NAME_STATIC, offset);
	OffsetControl(IDC_CONFIRM_PASSWORD_STATIC, offset);

	GetDlgItem(IDC_HOST)->ShowWindow(SW_SHOW);
	GetDlgItem(IDC_LOGIN)->ShowWindow(SW_SHOW);
	GetDlgItem(IDC_PASSWORD)->ShowWindow(SW_SHOW);
	GetDlgItem(IDC_PATH)->ShowWindow(SW_SHOW);
	GetDlgItem(IDC_FILE_NAME)->ShowWindow(SW_SHOW);
	GetDlgItem(IDC_TRIGGER_NAME)->ShowWindow(SW_SHOW);
	GetDlgItem(IDC_CONFIRM_PASSWORD)->ShowWindow(SW_SHOW);

	GetDlgItem(IDC_HOST_STATIC)->ShowWindow(SW_SHOW);
	GetDlgItem(IDC_LOGIN_STATIC)->ShowWindow(SW_SHOW);
	GetDlgItem(IDC_PASSWORD_STATIC)->ShowWindow(SW_SHOW);
	GetDlgItem(IDC_PATH_STATIC)->ShowWindow(SW_SHOW);
	GetDlgItem(IDC_FILE_NAME_STATIC)->ShowWindow(SW_SHOW);
	GetDlgItem(IDC_TRIGGER_NAME_STATIC)->ShowWindow(SW_SHOW);
	GetDlgItem(IDC_CONFIRM_PASSWORD_STATIC)->ShowWindow(SW_SHOW);

	GetDlgItem(IDC_TEST)->ShowWindow(SW_SHOW);


}

void CDefineConnectionDialog::ShowLocal()
{
	HideFTP();
	HideMQ();
	
	CRect r, r2;

	GetDlgItem(IDC_QUEUE_MANAGER)->GetWindowRect(&r2);
	this->ScreenToClient(&r2);
	
	GetDlgItem(IDC_PATH)->GetWindowRect(&r);
	this->ScreenToClient(&r);

	int offset = r2.top - r.top;
	
	OffsetControl(IDC_PATH, offset);
	OffsetControl(IDC_FILE_NAME, offset);
	OffsetControl(IDC_TRIGGER_NAME, offset);

	OffsetControl(IDC_PATH_STATIC, offset);
	OffsetControl(IDC_FILE_NAME_STATIC, offset);
	OffsetControl(IDC_TRIGGER_NAME_STATIC, offset);

	GetDlgItem(IDC_PATH)->ShowWindow(SW_SHOW);
	GetDlgItem(IDC_FILE_NAME)->ShowWindow(SW_SHOW);
	GetDlgItem(IDC_TRIGGER_NAME)->ShowWindow(SW_SHOW);

	GetDlgItem(IDC_PATH_STATIC)->ShowWindow(SW_SHOW);
	GetDlgItem(IDC_FILE_NAME_STATIC)->ShowWindow(SW_SHOW);
	GetDlgItem(IDC_TRIGGER_NAME_STATIC)->ShowWindow(SW_SHOW);

	GetDlgItem(IDC_TEST)->ShowWindow(SW_HIDE);

}

void CDefineConnectionDialog::ShowPrompt()
{
	HideMQ();
	HideFTP();
	HideLocal();

	GetDlgItem(IDC_TEST)->ShowWindow(SW_HIDE);

}

void CDefineConnectionDialog::OffsetControl(int id, int offset)
{
	CRect r;

	GetDlgItem(id)->GetWindowRect(&r);
	this->ScreenToClient(&r);
	GetDlgItem(id)->SetWindowPos(NULL, r.left, r.top+offset, 0, 0, SWP_NOZORDER|SWP_NOSIZE);

	GetWindowRect(&r);
	this->ScreenToClient(&r);
}

BOOL CDefineConnectionDialog::OnHelpInfo(HELPINFO* pHelpInfo) 
{
	helpService.ShowFieldHelp(IDD, pHelpInfo->iCtrlId);

	return FALSE;

}

void CDefineConnectionDialog::OnHelp() 
{
	helpService.ShowScreenHelp(IDD);

	return;

}

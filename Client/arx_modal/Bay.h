// Bay.h: interface for the CBay class.
//
//////////////////////////////////////////////////////////////////////

#if !defined(AFX_BAY_H__1F18BC24_2102_420B_985A_2BBA0A48EB3F__INCLUDED_)
#define AFX_BAY_H__1F18BC24_2102_420B_985A_2BBA0A48EB3F__INCLUDED_

#if _MSC_VER > 1000
#pragma once
#endif // _MSC_VER > 1000

#include "FacilityElement.h"
#include "Level.h"
#include "3DPoint.h"

class CBay : public CFacilityElement  
{
public:
	CBay();
	virtual ~CBay();

	C3DPoint m_Coordinate;
	CString m_DrawingHandle;		// AcadHandle

	int m_BayProfileDBID;

	CTypedPtrArray<CObArray, CLevel*> m_LevelArray;
	CMap<long, long, CLevel*, CLevel*> m_LevelMapById;
	CMap<CString, LPCSTR, CLevel*, CLevel*> m_LevelMapByName;

};

#endif // !defined(AFX_BAY_H__1F18BC24_2102_420B_985A_2BBA0A48EB3F__INCLUDED_)

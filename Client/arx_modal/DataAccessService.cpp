// DataAccessService.cpp: implementation of the CDataAccessService class.
//
//////////////////////////////////////////////////////////////////////

#include "stdafx.h"
#include "modal.h"
#include "DataAccessService.h"
#include "ForteService.h"
#include "ssa_exception.h"

#include "base.h"
#include "MiscClasses.h"
#include "SLOTSessionMgr.h"
#include "SrvcObjs.h"


#include <afxmt.h>

#ifdef _DEBUG
#undef THIS_FILE
static char THIS_FILE[]=__FILE__;
#define new DEBUG_NEW
#endif

#ifndef ODBC
#define ODBC
#define ALIGNSIZE 4
#define ALIGNBUF (Length) Length % ALIGNSIZE ? \
Length + ALIGNSIZE - Length % ALIGNSIZE) : Length
#endif


//////////////////////////////////////////////////////////////////////
// Construction/Destruction
//////////////////////////////////////////////////////////////////////
extern CEvent g_ThreadDone;
extern CForteService forteService;

CDataAccessService::CDataAccessService()
{

}

CDataAccessService::~CDataAccessService()
{

}

int CDataAccessService::ExecuteStatement(const CString &statementName, const CString &sqlText)
{
#if 0
	CString tempString;
	int tempInt;
	CStringArray tempSendArray;
	CStringArray tempRecvArray;
	CString sendString;
	int i = 0;
	CString cmdText;
	CStringArray resultList;

	/////////////////////////////////////////////////////////////
	// build the data to send to the Forte connection server
	/////////////////////////////////////////////////////////////

	// For now assume only one sql statement; later add support for multiple
	// statements to be run within a transaction


	sendString = "<SIO>SLOTSocketString\n";
	tempSendArray.Add(sendString);
	sendString.Format("<SAI>%s\n", statementName);
	tempSendArray.Add(sendString);
	tempString.Format("<SAI>%s\n",sqlText);
	tempSendArray.Add(tempString);
	sendString = "<EIO>SLOTSocketString\n";
	tempSendArray.Add(sendString);
	
	forteService.SendToForteConnection(tempSendArray,tempRecvArray,
		CString("SLOTSocketString"), 44444);

	for (i=0; i < tempRecvArray.GetSize(); i++) {
		if ( tempRecvArray[i].Find("<SAI>") != -1 ) {
			tempString = tempRecvArray[i].Mid(5);
			tempInt = tempString.GetLength();
			if (tempString.GetAt(tempInt-1) == '\n' ) {
				tempString.SetAt(tempInt-1,' ');
				tempString.TrimRight();
			}
			resultList.Add(tempString);			
		}
	}
	if (resultList[0].CompareNoCase("Success") == 0)
		return 0;
	else
		return -1;
#else
	CListstring* sqlArr = new CListstring;
	string sqlTextTmp = (LPCTSTR)sqlText;
	sqlArr->AddTail(sqlTextTmp);
	int ret = getSessionMgrSO()->ExecuteStatementHelper(sqlArr);
	delete sqlArr;
	return ret;
#endif
}



int CDataAccessService::ExecuteStatements(const CString &statementName, CStringArray &sqlArray)
{
#if 0
	CString tempString;
	int tempInt;
	CStringArray tempSendArray;
	CStringArray tempRecvArray;
	CString sendString;
	int i = 0;
	CString cmdText;
	CStringArray resultList;

	/////////////////////////////////////////////////////////////
	// build the data to send to the Forte connection server
	/////////////////////////////////////////////////////////////

	/*
	// Until we fix Forte
	for (i=0; i < sqlArray.GetSize(); ++i) {
		tempInt = dataAccessService.ExecuteStatement(statementName, sqlArray[i]);
	}

	return tempInt;
	*/

	// For now assume only one sql statement; later add support for multiple
	// statements to be run within a transaction
	cmdText = sqlArray[0];

	sendString = "<SIO>SLOTSocketString\n";
	tempSendArray.Add(sendString);
	sendString.Format("<SAI>%s\n", statementName);
	tempSendArray.Add(sendString);
	for (i=0; i < sqlArray.GetSize(); ++i) {
		tempString.Format("<SAI>%s\n",sqlArray[i]);
		tempSendArray.Add(tempString);
	}
	sendString = "<EIO>SLOTSocketString\n";
	tempSendArray.Add(sendString);
	
	forteService.SendToForteConnection(tempSendArray,tempRecvArray,
		CString("SLOTSocketString"), 44444);

	for (i=0; i < tempRecvArray.GetSize(); i++) {
		if ( tempRecvArray[i].Find("<SAI>") != -1 ) {
			tempString = tempRecvArray[i].Mid(5);
			tempInt = tempString.GetLength();
			if (tempString.GetAt(tempInt-1) == '\n' ) {
				tempString.SetAt(tempInt-1,' ');
				tempString.TrimRight();
			}
			resultList.Add(tempString);			
		}
	}
	if (resultList[0].CompareNoCase("Success") == 0)
		return 0;
	else
		return -1;
#else
	string sqlTextTmp;
	CListstring* sqlArr = new CListstring;
	for (int i=0; i<sqlArray.GetCount(); i++)
	{
		sqlTextTmp = (LPCTSTR)sqlArray[i];
		sqlArr->AddTail(sqlTextTmp);
	}
	int ret = getSessionMgrSO()->ExecuteStatementHelper(sqlArr);
	delete sqlArr;
	return ret;
#endif
}


int CDataAccessService::ExecuteQuery(const CString &queryName, const CString &query, CStringArray &results,
									 BOOL trimTrailingDelimiter)
{
try
{
	CString tempString;
	CStringArray tempSendArray;
	CStringArray tempRecvArray;
	CString sendString;
	int i = 0;

	/////////////////////////////////////////////////////////////
	// build the data to send to the Forte connection server
	////////////////////////////////////////////////////////////	
#if 0
	int tempInt;

	sendString = "<SIO>SLOTSocketString\n";
	tempSendArray.Add(sendString);
	sendString.Format("<SAI>%s\n", queryName);
	tempSendArray.Add(sendString);
	tempString.Format("<SAI>%s\n",query);
	tempSendArray.Add(tempString);
	tempString.Format("<SAI>False\n");	// Do not include headers in the results
	tempSendArray.Add(tempString);
	sendString = "<EIO>SLOTSocketString\n";
	tempSendArray.Add(sendString);
	
	forteService.SendToForteConnection(tempSendArray,tempRecvArray,
		CString("SLOTSocketString"), 55555);

	for (i=0; i < tempRecvArray.GetSize(); i++) {
		if ( tempRecvArray[i].Find("<SAI>") != -1 ) {
			tempString = tempRecvArray[i].Mid(5);
			tempInt = tempString.GetLength();
			if (tempString.GetAt(tempInt-1) == '\n' ) {
				tempString.SetAt(tempInt-1,' ');
				tempString.TrimRight();
			}
			if (trimTrailingDelimiter)
				tempString.TrimRight("|");
			results.Add(tempString);			
		}
	}
#endif

	CListstring *res = NULL;

	char strMsg[4096] = {0};

	try
	{
		string *queryTmp = new string;
			*queryTmp = (LPCTSTR)query;
		bool *b = new bool;
			*b = false;

		SLOTSessionMgr* ptr= getSessionMgrSO();
		res = ptr->RunQueryHelper(queryTmp, b);

		delete queryTmp;
		delete b;
	}
	catch(CException *e)
	{
		e->GetErrorMessage (strMsg, 4096);
		e->Delete();
		return -1;
	}
	catch(...)
	{
		return -1;
	}

	// MFS 8Feb06
	// SetAtGrow() below needs to recognize the existing array count.
	// We hope we are doing no harm by insuring that new elements are
	// appended rather than overwriting the first rows of the exisitng
	// array.
	int newIdx = results.GetSize();

	try
	{
		for (i=0;i<res->GetCount();i++)
		{
			CString tmpStr = res->GetAt(res->FindIndex(i)).c_str();
			if (trimTrailingDelimiter)
				tmpStr.TrimRight("|");
			results.SetAtGrow(newIdx++, tmpStr);
			// results[i] = tmpStr;		// WRONG - check all...
		}
	}
	catch(CException *e)
	{
		MessageBox(0,"Ex","A",0);
		e->Delete();
		return -1;
	}
	catch(...)
	{
		MessageBox(0,"Ex","A",0);
		return -1;
	}

	if ( results.GetSize() == 0 )
		return 0;
	else {
		return results.GetSize();
	}
}
catch(...)
{
	return -1;
}
}


int CDataAccessService::GetNextKey(const CString &tableName, int pNumberOfKeys)
{
#if 0
	CString tempString;
	int tempInt;
	CStringArray tempSendArray;
	CStringArray tempRecvArray;
	CString sendString;
	int i = 0;
	CString queryText;
	CStringArray resultList;

	/////////////////////////////////////////////////////////////
	// build the data to send to the Forte connection server
	/////////////////////////////////////////////////////////////

	sendString = "<SIO>SLOTSocketString\n";
	tempSendArray.Add(sendString);
	sendString = "<SAI>dataAccessService.GetNextKey\n";
	tempSendArray.Add(sendString);
	tempString.Format("<SAI>%s\n",tableName);
	tempSendArray.Add(tempString);
	tempString.Format("<SAI>%d\n", pNumberOfKeys);
	tempSendArray.Add(tempString);
	sendString = "<EIO>SLOTSocketString\n";
	tempSendArray.Add(sendString);
	
	forteService.SendToForteConnection(tempSendArray,tempRecvArray,
		CString("SLOTSocketString"), 33333);

	for (i=0; i < tempRecvArray.GetSize(); i++) {
		if ( tempRecvArray[i].Find("<SAI>") != -1 ) {
			tempString = tempRecvArray[i].Mid(5);
			tempInt = tempString.GetLength();
			if (tempString.GetAt(tempInt-1) == '\n' ) {
				tempString.SetAt(tempInt-1,' ');
				tempString.TrimRight();
			}
			resultList.Add(tempString);			
		}
	}
	if ( resultList.GetSize() == 0 )
		return 1000;
	else {
		if (resultList[0].GetAt(resultList[0].GetLength()-1) == '|')
			resultList[0].Delete(resultList[0].GetLength()-1);
		return atoi(resultList[0]);
	}

#else
	string *tableNameTmp = new string;
	*tableNameTmp = (LPCTSTR)tableName;
	int nextKey = getSessionMgrSO()->GetNextKeyHelper(tableNameTmp, pNumberOfKeys);
	delete tableNameTmp;
	if (nextKey == -1)
		return 1000;
	else
		return nextKey;
#endif
}


UINT CDataAccessService::ExecuteStatementsThread(LPVOID pParam)
{
	CStringArray *pArray;
	int rc= 0;
	CDataAccessService dataAccessService;

	pArray = (CStringArray *)pParam;
	try {
		rc = dataAccessService.ExecuteStatements("dataAccessService.ExecuteStatementsThread", *pArray);
		(*pArray)[0].Format("%d", rc);
	}
	catch (Ssa_Exception e) {
		char msgBuf[1024];
		e.GetMessage(msgBuf);
		if (pArray->GetSize() < 2)
			pArray->SetSize(2);
		(*pArray)[0].Format("%d", -1);
		(*pArray)[1].Format("%s", msgBuf);
	}
	catch (...) {
		if (pArray->GetSize() < 2)
			pArray->SetSize(2);
		(*pArray)[0].Format("%d", -1);
		(*pArray)[1].Format("Generic error.");
	}

	g_ThreadDone.SetEvent();

	return rc;

}


UINT CDataAccessService::ExecuteQueryThread(LPVOID pParam)
{
	CStringArray *pArray;
	CString sql, name;
	int rc= 0;
	
	CDataAccessService dataAccessService;

	pArray = (CStringArray *)pParam;
	if (pArray->GetSize() == 0)
		return 0;

	try {
		sql = (*pArray)[0];
		name = (*pArray)[1];

		pArray->RemoveAll();
		pArray->SetSize(0);

		rc = dataAccessService.ExecuteQuery(name, sql, *pArray);
		if (rc < 0) {
			pArray->SetSize(2);
			(*pArray)[0].Format("%d", rc);
			(*pArray)[1] = "Error running query.";
		}
	}
	catch (Ssa_Exception e) {
		char msgBuf[1024];
		e.GetMessage(msgBuf);
		if (pArray->GetSize() < 2)
			pArray->SetSize(2);
		(*pArray)[0].Format("%d", -1);
		(*pArray)[1].Format("%s", msgBuf);
	}
	catch (...) {
		if (pArray->GetSize() < 2)
			pArray->SetSize(2);
		(*pArray)[0].Format("%d", -1);
		(*pArray)[1].Format("Generic error running query.");
	}

	g_ThreadDone.SetEvent();

	return 0;

}




int CDataAccessService::ExecuteODBCQuery(const CString &queryName, const CString &query, CStringArray &results)
{
	// Executes a generic query via ODBC connection.  Packages results in &results

    // Fix by Manohar - commented 4 lines
/*	SQLSMALLINT NumCols, *CTypeArray, i;
	SQLINTEGER *ColLenArray, *OffsetArray, SQLType, *DataPtr;
	SQLRETURN rc;
	SQLHSTMT hstmt; */

	//SQLExecDirect(hstmt, query, SQL_NTS);




    return 0;
}

// CapitalCostDialog.cpp : implementation file
//

#include "stdafx.h"
#include "modal.h"
#include "CapitalCostDialog.h"
#include "HelpService.h"
#include "ControlService.h"
#include "UtilityHelper.h"
#include "FacilityDataService.h"

#ifdef _DEBUG
#define new DEBUG_NEW
#undef THIS_FILE
static char THIS_FILE[] = __FILE__;
#endif

/////////////////////////////////////////////////////////////////////////////
// CCapitalCostDialog dialog

extern CHelpService helpService;
extern CControlService controlService;
extern CUtilityHelper utilityHelper;
extern CFacilityDataService facilityDataService;

CCapitalCostDialog::CCapitalCostDialog(CWnd* pParent /*=NULL*/)
	: CDialog(CCapitalCostDialog::IDD, pParent)
{
	//{{AFX_DATA_INIT(CCapitalCostDialog)
	m_BreakPallet = FALSE;
	m_RankHeight = FALSE;
	m_Ranking = 4;
	m_LogMode = _T("None");
	m_MaxResults = 5000;
	//}}AFX_DATA_INIT
	m_Advanced = FALSE;
	m_BayTypeCount = -1;
}


void CCapitalCostDialog::DoDataExchange(CDataExchange* pDX)
{
	CDialog::DoDataExchange(pDX);
	//{{AFX_DATA_MAP(CCapitalCostDialog)
	DDX_Control(pDX, IDC_RANKING_SLIDER, m_SliderCtrl);
	DDX_Control(pDX, IDC_LOG_MODE, m_LogModeCtrl);
	DDX_Check(pDX, IDC_BREAK_PALLET, m_BreakPallet);
	DDX_Check(pDX, IDC_RANK_HEIGHT, m_RankHeight);
	DDX_Slider(pDX, IDC_RANKING_SLIDER, m_Ranking);
	DDX_CBString(pDX, IDC_LOG_MODE, m_LogMode);
	DDX_Text(pDX, IDC_MAX_RESULTS, m_MaxResults);
	//}}AFX_DATA_MAP
}


BEGIN_MESSAGE_MAP(CCapitalCostDialog, CDialog)
	//{{AFX_MSG_MAP(CCapitalCostDialog)
	ON_WM_HELPINFO()
	ON_BN_CLICKED(IDHELP, OnHelp)
	ON_BN_CLICKED(IDC_ADVANCED, OnAdvanced)
	//}}AFX_MSG_MAP
END_MESSAGE_MAP()

/////////////////////////////////////////////////////////////////////////////
// CCapitalCostDialog message handlers
BOOL CCapitalCostDialog::OnInitDialog() 
{
	CDialog::OnInitDialog();

	CString options;
	options = controlService.GetApplicationData("Options", "Dialogs\\CapitalCostStartup");
	
	CWaitCursor cwc;
		m_BayTypeCount = facilityDataService.GetOptimizeBayTypeCount(controlService.GetCurrentFacilityDBId());
		if (m_BayTypeCount < 8)
			m_BayTypeCount = 8;
		if (m_BayTypeCount < m_Ranking)
			m_BayTypeCount = m_Ranking;

		m_SliderCtrl.SetRange(1, m_BayTypeCount, TRUE);

	if (options != "") {
		CStringArray optList;
		utilityHelper.ParseString(options, "|", optList);
		for (int i=0; i < optList.GetSize(); ++i) {
			switch (i) {
			case 0:
				m_BreakPallet = atoi(optList[i]);
				break;
			case 1:
				m_RankHeight = atoi(optList[i]);
				break;
			case 2:
				m_LogMode = optList[i];
				break;
			case 3:
				m_Ranking = atoi(optList[i]);
				break;
			case 4:
				m_MaxResults = atoi(optList[i]);
				break;
			}
		}
	}
	m_SliderCtrl.SetPos(m_Ranking);

	m_LogModeCtrl.AddString("None");
	m_LogModeCtrl.AddString("User");
	m_LogModeCtrl.AddString("Expert");
	
	CRect r;
	m_LogModeCtrl.GetWindowRect(&r);

	m_LogModeCtrl.SetWindowPos(NULL, 0, 0, r.Width(), r.Height()*4, SWP_NOMOVE|SWP_NOZORDER);
	
	int curSel = m_LogModeCtrl.FindString(-1, m_LogMode);
	if (curSel >= 0)
		m_LogModeCtrl.SetCurSel(curSel);
	else
		m_LogModeCtrl.SetCurSel(0);

	int offset = 150;


	GetDlgItem(IDOK)->GetWindowRect(&r);
	this->ScreenToClient(&r);
	GetDlgItem(IDOK)->SetWindowPos(NULL, r.left, r.top-offset, 0, 0, SWP_NOZORDER|SWP_NOSIZE);
	
	GetDlgItem(IDCANCEL)->GetWindowRect(&r);
	this->ScreenToClient(&r);
	GetDlgItem(IDCANCEL)->SetWindowPos(NULL, r.left, r.top-offset, 0, 0, SWP_NOZORDER|SWP_NOSIZE);
	
	GetDlgItem(IDHELP)->GetWindowRect(&r);
	this->ScreenToClient(&r);
	GetDlgItem(IDHELP)->SetWindowPos(NULL, r.left, r.top-offset, 0, 0, SWP_NOZORDER|SWP_NOSIZE);
	
	this->GetWindowRect(&r);
	this->SetWindowPos(NULL, 0, 0, r.Width(), r.Height()-offset, SWP_NOMOVE|SWP_NOZORDER);

	UpdateData(FALSE);

	return TRUE;  // return TRUE unless you set the focus to a control
	              // EXCEPTION: OCX Property Pages should return FALSE
}


BOOL CCapitalCostDialog::OnHelpInfo(HELPINFO* pHelpInfo) 
{
	helpService.ShowFieldHelp(IDD, pHelpInfo->iCtrlId);

	return FALSE;

}

void CCapitalCostDialog::OnHelp() 
{
	// helpService.ShowFieldHelp(IDD);
	helpService.ShowScreenHelp(IDD);		// Fix by Manohar
/*	Other options
		if (m_SelectAllHelp != "")
			helpService.ShowFieldHelp(m_SelectAllHelp);
		else if (m_MainHelpTopic != "")
			helpService.ShowScreenHelp(m_MainHelpTopic);
*/
	return;

}

void CCapitalCostDialog::OnAdvanced() 
{
	int offset = 150;

	CRect r;
	if (m_Advanced) {
		m_Advanced = FALSE;
		GetDlgItem(IDC_RANKING_STATIC)->ShowWindow(SW_HIDE);
		GetDlgItem(IDC_BEST_STATIC)->ShowWindow(SW_HIDE);
		GetDlgItem(IDC_MINIMIZE_STATIC)->ShowWindow(SW_HIDE);
		GetDlgItem(IDC_RANKING_SLIDER)->ShowWindow(SW_HIDE);
		GetDlgItem(IDC_LOGGING_STATIC)->ShowWindow(SW_HIDE);
		GetDlgItem(IDC_LOG_MODE)->ShowWindow(SW_HIDE);
		GetDlgItem(IDC_MAX_RESULTS)->ShowWindow(SW_HIDE);
		GetDlgItem(IDC_MAX_RESULTS_STATIC)->ShowWindow(SW_HIDE);

		GetDlgItem(IDOK)->GetWindowRect(&r);
		this->ScreenToClient(&r);
		GetDlgItem(IDOK)->SetWindowPos(NULL, r.left, r.top-offset, 0, 0, SWP_NOZORDER|SWP_NOSIZE);

		GetDlgItem(IDCANCEL)->GetWindowRect(&r);
		this->ScreenToClient(&r);
		GetDlgItem(IDCANCEL)->SetWindowPos(NULL, r.left, r.top-offset, 0, 0, SWP_NOZORDER|SWP_NOSIZE);

		GetDlgItem(IDHELP)->GetWindowRect(&r);
		this->ScreenToClient(&r);
		GetDlgItem(IDHELP)->SetWindowPos(NULL, r.left, r.top-offset, 0, 0, SWP_NOZORDER|SWP_NOSIZE);

		this->GetWindowRect(&r);
		this->SetWindowPos(NULL, 0, 0, r.Width(), r.Height()-offset, SWP_NOMOVE|SWP_NOZORDER);
	}
	else {
		m_Advanced = TRUE;
		GetDlgItem(IDC_RANKING_STATIC)->ShowWindow(SW_SHOW);
		GetDlgItem(IDC_BEST_STATIC)->ShowWindow(SW_SHOW);
		GetDlgItem(IDC_MINIMIZE_STATIC)->ShowWindow(SW_SHOW);
		GetDlgItem(IDC_RANKING_SLIDER)->ShowWindow(SW_SHOW);
		GetDlgItem(IDC_LOGGING_STATIC)->ShowWindow(SW_SHOW);
		GetDlgItem(IDC_LOG_MODE)->ShowWindow(SW_SHOW);
		GetDlgItem(IDC_MAX_RESULTS)->ShowWindow(SW_SHOW);
		GetDlgItem(IDC_MAX_RESULTS_STATIC)->ShowWindow(SW_SHOW);

		GetDlgItem(IDOK)->GetWindowRect(&r);
		this->ScreenToClient(&r);
		GetDlgItem(IDOK)->SetWindowPos(NULL, r.left, r.top+offset, 0, 0, SWP_NOZORDER|SWP_NOSIZE);

		GetDlgItem(IDCANCEL)->GetWindowRect(&r);
		this->ScreenToClient(&r);
		GetDlgItem(IDCANCEL)->SetWindowPos(NULL, r.left, r.top+offset, 0, 0, SWP_NOZORDER|SWP_NOSIZE);

		GetDlgItem(IDHELP)->GetWindowRect(&r);
		this->ScreenToClient(&r);
		GetDlgItem(IDHELP)->SetWindowPos(NULL, r.left, r.top+offset, 0, 0, SWP_NOZORDER|SWP_NOSIZE);
		
		this->GetWindowRect(&r);
		this->SetWindowPos(NULL, 0, 0, r.Width(), r.Height()+offset, SWP_NOMOVE|SWP_NOZORDER);

	}
}



void CCapitalCostDialog::OnOK() 
{
	UpdateData(TRUE);

	// Always set skip used to true - maybe later we can add an option
	// to allow them to set it to false
	if (m_Ranking > m_SliderCtrl.GetRangeMax()/2)
		m_SkipUsed = TRUE;
	else
		m_SkipUsed = TRUE;

	CString temp;
	temp.Format("%d|%d|%s|%d|%d|",
		m_BreakPallet, m_RankHeight, m_LogMode, m_Ranking, m_MaxResults);
	controlService.SetApplicationData("Options", temp, "Dialogs\\CapitalCostStartup");

	CDialog::OnOK();
}

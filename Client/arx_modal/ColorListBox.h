#if !defined(AFX_COLORLISTBOX_H__EFEA872E_7072_4D26_8174_EF0762896BDC__INCLUDED_)
#define AFX_COLORLISTBOX_H__EFEA872E_7072_4D26_8174_EF0762896BDC__INCLUDED_

#if _MSC_VER > 1000
#pragma once
#endif // _MSC_VER > 1000
// ColorListBox.h : header file
//

/////////////////////////////////////////////////////////////////////////////
// CColorListBox window

class CColorObject {
public:
	void SetText(const CString &text);
	void SetColorIndex(int colorIdx);
	CColorObject() { red = green = blue = 0; colorIndex = -1; text = ""; pItem = NULL; };
	CColorObject(int colorIndex, CString text = "");
	int red, green, blue, colorIndex;
	CString text;
	void *pItem;
};

class CColorListBox : public CListBox
{
// Construction
public:
	CColorListBox();

// Attributes
public:

// Operations
public:

// Overrides
	// ClassWizard generated virtual function overrides
	//{{AFX_VIRTUAL(CColorListBox)
	public:
	virtual void DrawItem(LPDRAWITEMSTRUCT lpDrawItemStruct);
	//}}AFX_VIRTUAL

// Implementation
public:
	virtual ~CColorListBox();

	// Generated message map functions
protected:
	//{{AFX_MSG(CColorListBox)
	//}}AFX_MSG

	DECLARE_MESSAGE_MAP()
};

/////////////////////////////////////////////////////////////////////////////

//{{AFX_INSERT_LOCATION}}
// Microsoft Visual C++ will insert additional declarations immediately before the previous line.

#endif // !defined(AFX_COLORLISTBOX_H__EFEA872E_7072_4D26_8174_EF0762896BDC__INCLUDED_)

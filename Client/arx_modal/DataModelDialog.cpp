// DataModelDialog.cpp : implementation file
//

#include "stdafx.h"
#include "DataModelDialog.h"
#include "ssa_exception.h"
#include "ProductAttribute.h"
#include "ProductDataService.h"
#include "HelpService.h"
#include "UtilityHelper.h"

#include <dbsymtb.h>

#ifdef _DEBUG
#define new DEBUG_NEW
#undef THIS_FILE
static char THIS_FILE[] = __FILE__;
#endif

extern CHelpService helpService;
extern CUtilityHelper utilityHelper;
/////////////////////////////////////////////////////////////////////////////
// CDataModelDialog dialog

extern FILE *flog;

static void OnLeaveCellDataGrid(void *parent);
//static void OnEnterCellDataGrid(void *parent);


CDataModelDialog::CDataModelDialog(CWnd* pParent /*=NULL*/)
	: CDialog(CDataModelDialog::IDD, pParent)
{
	//{{AFX_DATA_INIT(CDataModelDialog)
	m_Quantity = _T("");
	//}}AFX_DATA_INIT
	m_ParentOldWidth = 0;
	m_ParentOldHeight = 0;
}

void CDataModelDialog::DoDataExchange(CDataExchange* pDX)
{
	CDialog::DoDataExchange(pDX);
	//{{AFX_DATA_MAP(CDataModelDialog)
	DDX_Text(pDX, IDC_QUANTITY, m_Quantity);
	DDX_Control(pDX, IDC_DATA_GRID, m_DataGrid);
	//}}AFX_DATA_MAP
}


BEGIN_MESSAGE_MAP(CDataModelDialog, CDialog)
	//{{AFX_MSG_MAP(CDataModelDialog)
	ON_WM_SIZE()
	ON_WM_HELPINFO()
	ON_BN_CLICKED(IDHELP, OnHelp)
	//}}AFX_MSG_MAP
END_MESSAGE_MAP()

/////////////////////////////////////////////////////////////////////////////
// CDataModelDialog message handlers

BOOL CDataModelDialog::OnInitDialog() 
{

	CDialog::OnInitDialog();

	CRect r;
	GetClientRect(&r);
	m_ParentOldWidth = r.Width();
	m_ParentOldHeight = r.Height();

	m_Quantity = "1";
	UpdateData(FALSE);

	LoadGrid();

	return TRUE;  // return TRUE unless you set the focus to a control
	              // EXCEPTION: OCX Property Pages should return FALSE
}


BEGIN_EVENTSINK_MAP(CDataModelDialog, CDialog)
    //{{AFX_EVENTSINK_MAP(CDataModelDialog)
	//ON_EVENT(CDataModelDialog, IDC_DATA_GRID, 72 /* LeaveCell */, OnLeaveCellDataGrid, VTS_NONE)
	//}}AFX_EVENTSINK_MAP
END_EVENTSINK_MAP() \


void CDataModelDialog::LoadGrid()
{
	CDataGridAttribute *pAttr;
	long row, col;
	CStringArray attributes;
	CString attribute;
	CString;

	m_ProductDataService.LoadProductAttributes();

//	m_DataGrid.m_LeaveCellFunction = &OnLeaveCellDataGrid;

	m_DataGrid.m_MainHelpTopic = "DataModel_DataGrid";
	m_DataGrid.SetRows(m_ProductDataService.m_ProductAttributeList.GetSize()+1);
	m_DataGrid.SetCols(6);
	
	row = m_DataGrid.GetRows();
	col = m_DataGrid.GetCols();

	m_DataGrid.SetFixedRows(1);
	m_DataGrid.SetFixedCols(2);

	row = m_DataGrid.GetFixedRows();
	col = m_DataGrid.GetFixedCols();


	// First add all attributes to the grid
	for (row=0; row < m_DataGrid.GetRows(); ++row) {
		for (col=0; col < m_DataGrid.GetCols(); ++col) {	
			pAttr = new CDataGridAttribute;
			if (row < m_DataGrid.GetFixedRows() || col < m_DataGrid.GetFixedCols()) {
				pAttr->m_Type = AT_FIXED;
			}
			else {
				pAttr->m_Type = AT_VAR;
			}
			m_DataGrid.m_DataGridAttributes.Add(pAttr);
		}
	}

	

	int i;
	// Load the column attributes from the hard-coded values
	for (i=0; i < m_DataGrid.GetCols(); ++i) {
		pAttr = m_DataGrid.m_DataGridAttributes[i];
		pAttr->m_InitialValue = m_DataGrid.GetTextMatrix(0, i);
		pAttr->m_DataType = DT_NONE;
		pAttr->m_Value = m_DataGrid.GetTextMatrix(0, i);
		pAttr->m_AttributeID = 0;
	}

	row = 0;

	for (i=0; i < m_ProductDataService.m_ProductAttributeList.GetSize(); ++i) {
		CProductAttribute *pAttr = m_ProductDataService.m_ProductAttributeList[i];
		// SetAttribute(name, type, min, max, initial, list)
		SetAttribute(++row, pAttr->m_Name, pAttr->m_Type, pAttr->m_MinimumValue, pAttr->m_MaximumValue,
			pAttr->m_Initial, pAttr->m_ListValues, pAttr->m_AttributeDBID, pAttr->m_HelpTopic);
	}

	//m_DataGrid.m_ResetColumnWidths = TRUE;

	m_DataGrid.LoadAttributes();

	m_DataGrid.m_LeaveCellFunction = OnLeaveCellDataGrid;

	return;

}

void CDataModelDialog::SetAttribute(int row, CString name, int type, double min, double max, 
									CString initial, CStringArray &listValues, int attributeID,
									CString helpTopic)
{
	BOOL found = FALSE;
	CDataGridAttribute *pAttr;
	int idx;

	// Add the name to the 1st column
	idx = row * m_DataGrid.GetCols();
	pAttr = m_DataGrid.m_DataGridAttributes[idx];
	pAttr->m_InitialValue = name;
	pAttr->m_DataType = DT_NONE;
	pAttr->m_Value = name;
	pAttr->m_AttributeID = attributeID;
	pAttr->m_HelpTopic = helpTopic;

	// Add the data type to the 2nd column
	idx++;
	pAttr = m_DataGrid.m_DataGridAttributes[idx];
	switch(type) {
	case DT_INT:
		pAttr->m_InitialValue = "Integer";
		break;
	case DT_FLOAT:
		pAttr->m_InitialValue = "Float";
		break;
	case DT_STRING:
		pAttr->m_InitialValue = "String";
		break;
	case DT_LIST:
		pAttr->m_InitialValue = "List";
		break;
	}
	pAttr->m_Value = pAttr->m_InitialValue;
	pAttr->m_DataType = DT_NONE;


	// Handle the distribution column (skip it for strings and lists)
	idx++;
	pAttr = m_DataGrid.m_DataGridAttributes[idx];
	if (type == DT_STRING || type == DT_LIST) {
		pAttr->m_DataType = DT_NONE;
		pAttr->m_InitialValue = "";
		pAttr->m_Value = "";
	}
	else {
		pAttr->m_DataType = DT_LIST;
		pAttr->m_InitialValue = "0";
		pAttr->m_Value = "0";
		pAttr->m_ListValues.Add("None");
		pAttr->m_ListValues.Add("Uniform");
		pAttr->m_ListValues.Add("Gaussian");
	}


	// Handle the minimum value for the distribution
	idx++;
	pAttr = m_DataGrid.m_DataGridAttributes[idx];
	if (type == DT_STRING || type == DT_LIST) {
		pAttr->m_DataType = DT_NONE;
		pAttr->m_InitialValue = "";
		pAttr->m_Value = "";
	}
	else {
		pAttr->m_DataType = type;
		pAttr->m_InitialValue.Format("%.0f", min);
		pAttr->m_Value = pAttr->m_InitialValue;
		pAttr->m_Min = min;
		pAttr->m_Max = max;
	}
	pAttr->m_ReadOnly = TRUE;

	// Handle the maximum value for the distribution
	idx++;
	pAttr = m_DataGrid.m_DataGridAttributes[idx];
	if (type == DT_STRING || type == DT_LIST) {
		pAttr->m_DataType = DT_NONE;
		pAttr->m_InitialValue = "";
		pAttr->m_Value = "";
	}
	else {
		pAttr->m_DataType = type;
		pAttr->m_InitialValue.Format("%.0f", max);
		pAttr->m_Value = pAttr->m_InitialValue;
		pAttr->m_Min = min;
		pAttr->m_Max = max;
	}
	pAttr->m_ReadOnly = TRUE;

	// Handle the actual value
	idx++;
	pAttr = m_DataGrid.m_DataGridAttributes[idx];
	pAttr->m_DataType = type;
	if (type == DT_INT || type == DT_FLOAT) {
			pAttr->m_Min = min;
			pAttr->m_Max = max;
	}
	pAttr->m_InitialValue = initial;
	pAttr->m_Value = initial;
	if (type == DT_LIST) {
		for (idx=0; idx < listValues.GetSize(); ++idx) {
			pAttr->m_ListValues.Add(listValues[idx]);
		}
	}

}

// The purpose of this function is to allow us to do stuff in the parent
// class after we leave a cell so we can re-use the child class (CDataGrid).
// I had trouble passing it to CDataGrid as a member function so I made it
// a global.  CDataGrid will pass in it's parent which happens to be this class.
// That way we can still access all of the member attributes.  Kinda weird but
// it works.  The same thing can be done for EnterCell.
void OnLeaveCellDataGrid(void *parent)
{
	long row, col;
	int idx;
	CDataGridAttribute *pAttr;

	CDataModelDialog *me = (CDataModelDialog *)parent;

	row = me->m_DataGrid.GetRow();
	col = me->m_DataGrid.GetCol();

	if (me->m_DataGrid.m_Initializing)
		return;

	if (flog != NULL) {
		fprintf(flog, "OnLeaveCellDataGrid: %d - %d\n", row, col);
		fflush(flog);
	}

	idx = row * me->m_DataGrid.GetCols() + col;
	
	// When leaving column 2 (the distribution type), if the attribute is numeric,
	// set the distribution min/max to readonly if the distribution type is none,
	// otherwise set the value to readonly
	if (col == 2) {
		pAttr = me->m_DataGrid.m_DataGridAttributes[idx+3];
		
		if (pAttr->m_DataType == DT_INT || pAttr->m_DataType == DT_FLOAT) {

			if (me->m_DataGrid.GetTextMatrix(row, col).CompareNoCase("None") == 0) {
				pAttr = me->m_DataGrid.m_DataGridAttributes[idx+1];
				pAttr->m_ReadOnly = TRUE;
				pAttr = me->m_DataGrid.m_DataGridAttributes[idx+2];
				pAttr->m_ReadOnly = TRUE;
				pAttr = me->m_DataGrid.m_DataGridAttributes[idx+3];
				pAttr->m_ReadOnly = FALSE;
			}
			else {
				pAttr = me->m_DataGrid.m_DataGridAttributes[idx+1];
				pAttr->m_ReadOnly = FALSE;
				pAttr = me->m_DataGrid.m_DataGridAttributes[idx+2];
				pAttr->m_ReadOnly = FALSE;
				pAttr = me->m_DataGrid.m_DataGridAttributes[idx+3];
				pAttr->m_ReadOnly = TRUE;
			}
		}
	}
}	

void CDataModelDialog::OnOK() 
{

	UpdateData(TRUE);
	if (atoi(m_Quantity) <= 0) {
		AfxMessageBox("Quantity must be greater than 0.");
		return;
	}


	// Make sure all the values are within their ranges
	if (! ValidateCells())
		return;

	ModelProducts();

	//CDialog::OnOK();
}


BOOL CDataModelDialog::ValidateCells()
{
	CDataGridAttribute *pAttr, *pAttr2, *pAttr3;
	int i, idx;
	CString tmp;

	for (i=1; i < m_DataGrid.GetRows(); ++i) {
		idx = m_DataGrid.GetCols() * i;
		pAttr3 = m_DataGrid.m_DataGridAttributes[idx];
		pAttr = m_DataGrid.m_DataGridAttributes[idx+5];
		
		if (pAttr->m_DataType == DT_STRING) {
			if (pAttr->m_Value == "") {
				tmp.Format("The value for attribute %s must not be blank.", pAttr3->m_InitialValue);
				AfxMessageBox(tmp);
				m_DataGrid.SetFocus();					
				m_DataGrid.SetRow(i);
				m_DataGrid.SetCol(5);
				return FALSE;
			}
		}
		else if (pAttr->m_DataType == DT_INT) {		// integer type

			pAttr2 = m_DataGrid.m_DataGridAttributes[idx+2];
			if (pAttr2->m_Value.CompareNoCase("0") == 0) {	
				// no distribution so check the actual value
				// check for numeric
				if (! utilityHelper.IsNumeric(pAttr->m_Value) || pAttr->m_Value == "") {
					tmp.Format("The value for attribute %s must be a valid integer.", pAttr3->m_InitialValue);
					AfxMessageBox(tmp);
					m_DataGrid.SetFocus();					
					m_DataGrid.SetRow(i);
					m_DataGrid.SetCol(5);
					return FALSE;
				}
				// check for greater than minimum
				else if (atol(pAttr->m_Value) < pAttr->m_Min) {
					tmp.Format("The value for attribute %s of %.0f can not be less than %.0f.", 
						pAttr3->m_InitialValue, atof(pAttr->m_Value), pAttr->m_Min);
					AfxMessageBox(tmp);
					m_DataGrid.SetFocus();
					m_DataGrid.SetRow(i);
					m_DataGrid.SetCol(5);
					return FALSE;
				}
				// check for less than maximum
				else if (atol(pAttr->m_Value) > pAttr->m_Max) {
					tmp.Format("The value for attribute %s of %.0f can not be more than %.0f.", 
						pAttr3->m_InitialValue, atof(pAttr->m_Value), pAttr->m_Max);
					AfxMessageBox(tmp);
					m_DataGrid.SetFocus();
					m_DataGrid.SetRow(i);
					m_DataGrid.SetCol(5);
					return FALSE;
				}			
			}
			else {
				// there is a distribution so check the min/max distribution values
				// check minimum distribution value
				pAttr = m_DataGrid.m_DataGridAttributes[idx+3];
				// check for numeric
				if (! utilityHelper.IsNumeric(pAttr->m_Value) || pAttr->m_Value == "") {
					tmp.Format("The minimum distribution value for attribute %s must be a valid integer.", 
						pAttr3->m_InitialValue);
					AfxMessageBox(tmp);
					m_DataGrid.SetFocus();
					m_DataGrid.SetRow(i);
					m_DataGrid.SetCol(5);
					return FALSE;
				}
				// check minimum
				else if (atol(pAttr->m_Value) < pAttr->m_Min) {
					tmp.Format("The minimum distribution value for attribute %s of %.0f can not be less than %.0f.", 
						pAttr3->m_InitialValue, atof(pAttr->m_Value), pAttr->m_Min);
					AfxMessageBox(tmp);
					m_DataGrid.SetFocus();
					m_DataGrid.SetRow(i);
					m_DataGrid.SetCol(3);
					return FALSE;
				}
				// check maximum
				else if (atol(pAttr->m_Value) > pAttr->m_Max) {
					tmp.Format("The minimum distribution value for attribute %s of %.0f can not be more than %.0f.", 
						pAttr3->m_InitialValue, atof(pAttr->m_Value), pAttr->m_Max);
					AfxMessageBox(tmp);
					m_DataGrid.SetFocus();
					m_DataGrid.SetRow(i);
					m_DataGrid.SetCol(3);
					return FALSE;
				}
				// check maximum distribution value
				pAttr = m_DataGrid.m_DataGridAttributes[idx+4];
				// check for numeric
				if (! utilityHelper.IsNumeric(pAttr->m_Value) || pAttr->m_Value == "") {
					tmp.Format("The maximum distribution value for attribute %s must be a valid integer.", 
						pAttr3->m_InitialValue);
					AfxMessageBox(tmp);
					m_DataGrid.SetFocus();
					m_DataGrid.SetRow(i);
					m_DataGrid.SetCol(5);
					return FALSE;
				}
				// check minimum
				else if (atol(pAttr->m_Value) < pAttr->m_Min) {
					tmp.Format("The maximum distribution value for attribute %s of %.0f can not be less than %.0f.", 
						pAttr3->m_InitialValue, atof(pAttr->m_Value), pAttr->m_Min);
					AfxMessageBox(tmp);
					m_DataGrid.SetFocus();
					m_DataGrid.SetRow(i);
					m_DataGrid.SetCol(4);
					return FALSE;
				}
				// check maximum
				else if (atol(pAttr->m_Value) > pAttr->m_Max) {
					tmp.Format("The maximum distribution value for attribute %s of %.0f can not be more than %.0f.", 
						pAttr3->m_InitialValue, atof(pAttr->m_Value), pAttr->m_Max);
					AfxMessageBox(tmp);
					m_DataGrid.SetFocus();
					m_DataGrid.SetRow(i);
					m_DataGrid.SetCol(4);
					return FALSE;
				}						
			}
		}
		else if (pAttr->m_DataType == DT_FLOAT) {		// float type

			pAttr2 = m_DataGrid.m_DataGridAttributes[idx+2];
			if (pAttr2->m_Value.CompareNoCase("0") == 0) {
				if (! utilityHelper.IsNumeric(pAttr->m_Value) || pAttr->m_Value == "") {
					tmp.Format("The value for attribute %s must be a valid floating point number.", 
						pAttr3->m_InitialValue);
					AfxMessageBox(tmp);
					m_DataGrid.SetFocus();
					m_DataGrid.SetRow(i);
					m_DataGrid.SetCol(5);
					return FALSE;
				}
				else if (atol(pAttr->m_Value) < pAttr->m_Min) {
					tmp.Format("The value for attribute %s of %.0f can not be less than %.0f.", 
						pAttr3->m_InitialValue, atof(pAttr->m_Value), pAttr->m_Min);
					AfxMessageBox(tmp);
					m_DataGrid.SetFocus();
					m_DataGrid.SetRow(i);
					m_DataGrid.SetCol(5);
					return FALSE;
				}
				else if (atol(pAttr->m_Value) > pAttr->m_Max) {
					tmp.Format("The value for attribute %s of %.0f can not be more than %.0f.", 
						pAttr3->m_InitialValue, atof(pAttr->m_Value), pAttr->m_Max);
					AfxMessageBox(tmp);
					m_DataGrid.SetFocus();
					m_DataGrid.SetRow(i);
					m_DataGrid.SetCol(5);
					return FALSE;
				}			
			}
			else {
				pAttr = m_DataGrid.m_DataGridAttributes[idx+3];
				if (! utilityHelper.IsNumeric(pAttr->m_Value) || pAttr->m_Value == "") {
					tmp.Format("The minimum distribution value for attribute %s must be a valid floating point number.", 
						pAttr3->m_InitialValue);
					AfxMessageBox(tmp);
					m_DataGrid.SetFocus();
					m_DataGrid.SetRow(i);
					m_DataGrid.SetCol(5);
					return FALSE;
				}
				else if (atol(pAttr->m_Value) < pAttr->m_Min) {
					tmp.Format("The minimum distribution value for attribute %s of %.0f can not be less than %.0f.", 
						pAttr3->m_InitialValue, atof(pAttr->m_Value), pAttr->m_Min);
					AfxMessageBox(tmp);
					m_DataGrid.SetFocus();
					m_DataGrid.SetRow(i);
					m_DataGrid.SetCol(3);
					return FALSE;
				}
				else if (atol(pAttr->m_Value) > pAttr->m_Max) {
					tmp.Format("The minimum distribution value for attribute %s of %.0f can not be more than %.0f.", 
						pAttr3->m_InitialValue, atof(pAttr->m_Value), pAttr->m_Max);
					AfxMessageBox(tmp);
					m_DataGrid.SetFocus();
					m_DataGrid.SetRow(i);
					m_DataGrid.SetCol(3);
					return FALSE;
				}
				
				pAttr = m_DataGrid.m_DataGridAttributes[idx+4];
				if (! utilityHelper.IsNumeric(pAttr->m_Value) || pAttr->m_Value == "") {
					tmp.Format("The maximum distribution value for attribute %s must be a valid floating point number.", 
						pAttr3->m_InitialValue);
					AfxMessageBox(tmp);
					m_DataGrid.SetFocus();
					m_DataGrid.SetRow(i);
					m_DataGrid.SetCol(4);
					return FALSE;
				}
				else if (atol(pAttr->m_Value) < pAttr->m_Min) {
					tmp.Format("The maximum distribution value for attribute %s of %.0f can not be less than %.0f.", 
						pAttr3->m_InitialValue, atof(pAttr->m_Value), pAttr->m_Min);
					AfxMessageBox(tmp);
					m_DataGrid.SetFocus();
					m_DataGrid.SetRow(i);
					m_DataGrid.SetCol(4);
					return FALSE;
				}
				else if (atol(pAttr->m_Value) > pAttr->m_Max) {
					tmp.Format("The maximum distribution value for attribute %s of %.0f can not be more than %.0f.", 
						pAttr3->m_InitialValue, atof(pAttr->m_Value), pAttr->m_Max);
					AfxMessageBox(tmp);
					m_DataGrid.SetFocus();
					m_DataGrid.SetRow(i);
					m_DataGrid.SetCol(4);
					return FALSE;
				}						
			}
		}
		
	}

	return TRUE;

}

void CDataModelDialog::ModelProducts()
{
	CStringArray attributes;
	CString attribute;
	CDataGridAttribute *pAttr;
	int idx;
	CString name, value, dist, displayValue, columnName;
	CString min, max;
	int attributeID, type;
	int rc;
	CProductAttribute *prodAttr;

	for (int i=1; i < m_DataGrid.GetRows(); ++i) {
		idx = m_DataGrid.GetCols() * i;	

		pAttr = m_DataGrid.m_DataGridAttributes[idx];
		name = pAttr->m_InitialValue;
		if (! m_ProductDataService.m_ProductAttributeMap.Lookup(name, (CObject *&)prodAttr)) {
			ads_printf("Unable to find attribute: %s\n", name);
			continue;
		}
		attributeID = pAttr->m_AttributeID;

		pAttr = m_DataGrid.m_DataGridAttributes[idx+1];

		pAttr = m_DataGrid.m_DataGridAttributes[idx+2];
		// distribution will be 0 if the datatype is float or int and they 
		// specify none; blank if the datatype is list or string
		if (pAttr->m_Value != "0" && pAttr->m_Value != "") {
			dist = pAttr->m_ListValues[atoi(pAttr->m_Value)];
			if (dist.CompareNoCase("0") == 0)
				dist = "";
		}
		else
			dist = "";

		// Get the minimum value if they specified a distribution type
		pAttr = m_DataGrid.m_DataGridAttributes[idx+3];
		if (dist == "")
			min = "";
		else
			min = pAttr->m_Value;

		// Get the maximum value if they specified a distribution type
		pAttr = m_DataGrid.m_DataGridAttributes[idx+4];
		if (dist == "")
			max = "";
		else
			max = pAttr->m_Value;

		// Get the actual value - this will be overridden by the dist values
		pAttr = m_DataGrid.m_DataGridAttributes[idx+5];
		type = pAttr->m_DataType;

		if (type == DT_LIST) {
			displayValue = pAttr->m_ListValues[atoi(pAttr->m_Value)];
			prodAttr->m_DisplayToInternalMap.Lookup(displayValue, value);
		}
		else
			value = pAttr->m_Value;
		
		columnName = prodAttr->m_ColumnName;
		if (prodAttr->m_TableName == TB_PRODUCTCONTAINER &&
			(prodAttr->m_ColumnName == "Width" ||
			prodAttr->m_ColumnName == "Length" ||
			prodAttr->m_ColumnName == "Height") ) {
			columnName = "Container";
			columnName += prodAttr->m_ColumnName;
		}
		/*
		else if (prodAttr->m_TableName == TB_PRODUCTCONTAINER &&
			(prodAttr->m_ColumnName == "TI" ||
			prodAttr->m_ColumnName == "HI")) {
			columnName = "Storage";
			columnName += prodAttr->m_ColumnName;
		}
		*/

		attribute.Format("%s;%s;%s;%s;%s;%d;%d;",
			columnName, min, max, dist, value, type, attributeID);

		attributes.Add(attribute);

	}

	try {
		CProductDataService productDataService;
		rc = productDataService.RunDataModel(atoi(m_Quantity), attributes);
	}
	catch(Ssa_Exception e) {
		utilityHelper.ProcessError("Error modeling products.", &e);
		return;
	}
	catch(...) {
		utilityHelper.ProcessError("Error modeling products.");
		return;
	}

	if (rc < 0)
		AfxMessageBox("Error modeling products.");
	else {
		CString tmp;
		if (atoi(m_Quantity) == 1)
			tmp.Format("1 product successfully created.");
		else
			tmp.Format("%d products successfully created.", atoi(m_Quantity));
		AfxMessageBox(tmp);
	}


	return;

}

void CDataModelDialog::OnSize(UINT nType, int cx, int cy) 
{
	CRect r;

	CDialog::OnSize(nType, cx, cy);
	
	// Ability to resize anything with a grid control is still a work in progress.
	/*
	if (nType == SIZE_MINIMIZED)
		return;

	if (m_DataGrid.m_hWnd != NULL) {
		int newWidth, newHeight;
		m_DataGrid.GetWindowRect(&r);	// screen coordinates
		ScreenToClient(&r);				// relative to parent
		newWidth = r.Width() + (cx - m_ParentOldWidth);
		newHeight = r.Height() + (cy - m_ParentOldHeight);
		m_DataGrid.MoveWindow(r.left, r.top, newWidth, newHeight);
	}
	
	CButton *pButton;
	pButton = (CButton *)GetDlgItem(IDOK);
	if (pButton != NULL) {
		if (pButton->m_hWnd != NULL) {
			pButton->GetWindowRect(&r);
			ScreenToClient(&r);
			float newX, newY;
			newX = (float)((cx - m_ParentOldWidth)/2) + r.left;
			newY = (float)(cy - m_ParentOldHeight) + r.top;
			pButton->SetWindowPos(NULL, (int)newX, (int)newY, 0, 0, SWP_NOSIZE);
		}
		
		pButton = (CButton *)GetDlgItem(IDCANCEL);
		if (pButton->m_hWnd != NULL) {
			pButton->GetWindowRect(&r);
			ScreenToClient(&r);
			float newX, newY;
			newX = (float)((cx - m_ParentOldWidth)/2) + r.left;
			newY = (float)(cy - m_ParentOldHeight) + r.top;
			pButton->SetWindowPos(NULL, (int)newX, (int)newY, 0, 0, SWP_NOSIZE);
		}
		
		pButton = (CButton *)GetDlgItem(IDHELP);
		if (pButton->m_hWnd != NULL) {
			pButton->GetWindowRect(&r);
			ScreenToClient(&r);
			float newX, newY;
			newX = (float)((cx - m_ParentOldWidth)/2) + r.left;
			newY = (float)(cy - m_ParentOldHeight) + r.top;
			pButton->SetWindowPos(NULL, (int)newX, (int)newY, 0, 0, SWP_NOSIZE);
		}
	}


	m_ParentOldWidth = cx;
	m_ParentOldHeight = cy;
	
	// Added this because the buttons weren't being redrawn correctly; probably
	// need to change it to only invalidate the area around the buttons
	this->Invalidate(TRUE);
	*/

}

BOOL CDataModelDialog::OnHelpInfo(HELPINFO* pHelpInfo) 
{
	helpService.ShowFieldHelp(IDD, pHelpInfo->iCtrlId);
	return FALSE;
}

void CDataModelDialog::OnHelp() 
{
	helpService.ShowScreenHelp(IDD);	
}

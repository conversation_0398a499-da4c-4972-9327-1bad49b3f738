// BayProfileHandlingProperties.cpp : implementation file
//

#include "stdafx.h"
#include "modal.h"
#include "BayProfileHandlingProperties.h"
#include "UtilityHelper.h"
#include "BayProfileSheet.h"
#include "HelpService.h"

#ifdef _DEBUG
#define new DEBUG_NEW
#undef THIS_FILE
static char THIS_FILE[] = __FILE__;
#endif

extern CUtilityHelper utilityHelper;
extern CHelpService helpService;
/////////////////////////////////////////////////////////////////////////////
// CBayProfileHandlingProperties dialog


CBayProfileHandlingProperties::CBayProfileHandlingProperties(CWnd* pParent /*=NULL*/)
	: CDialog(CBayProfileHandlingProperties::IDD, pParent)
{
	//{{AFX_DATA_INIT(CBayProfileHandlingProperties)
	m_Cube = _T("");
	m_Fixed = _T("");
	m_Variable = _T("");
	//}}AFX_DATA_INIT
}


void CBayProfileHandlingProperties::DoDataExchange(CDataExchange* pDX)
{
	CDialog::DoDataExchange(pDX);
	//{{AFX_DATA_MAP(CBayProfileHandlingProperties)
	DDX_Control(pDX, IDC_WORK_TYPE_LIST, m_WorkTypeListCtrl);
	DDX_Text(pDX, IDC_CUBE, m_Cube);
	DDX_Text(pDX, IDC_FIXED, m_Fixed);
	DDX_Text(pDX, IDC_VARIABLE, m_Variable);
	//}}AFX_DATA_MAP
}


BEGIN_MESSAGE_MAP(CBayProfileHandlingProperties, CDialog)
	//{{AFX_MSG_MAP(CBayProfileHandlingProperties)
	ON_BN_CLICKED(IDHELP, OnHelp)
	ON_WM_HELPINFO()
	//}}AFX_MSG_MAP
END_MESSAGE_MAP()

/////////////////////////////////////////////////////////////////////////////
// CBayProfileHandlingProperties message handlers

BOOL CBayProfileHandlingProperties::OnInitDialog() 
{
	CDialog::OnInitDialog();
	
	CRect r;
	m_WorkTypeListCtrl.GetWindowRect(&r);

	int nItem = m_WorkTypeListCtrl.AddString("Selection");
	m_WorkTypeListCtrl.SetItemData(nItem, CLevelLaborProfile::workTypeSelection);
	if (m_WorkType == CLevelLaborProfile::workTypeSelection)
		m_WorkTypeListCtrl.SetCurSel(nItem);

	nItem = m_WorkTypeListCtrl.AddString("Stocker");
	m_WorkTypeListCtrl.SetItemData(nItem, CLevelLaborProfile::workTypeStocker);
	if (m_WorkType == CLevelLaborProfile::workTypeStocker)
		m_WorkTypeListCtrl.SetCurSel(nItem);

	m_WorkTypeListCtrl.SetWindowPos(NULL, 0, 0, r.Width(), r.Height()*3, SWP_NOMOVE|SWP_NOZORDER);

	CBayProfileSheet *pSheet = (CBayProfileSheet *)GetParent();
	m_pBayProfile = pSheet->m_pBayProfile;

	CLevelProfile *pLevelProfile = m_pBayProfile->m_LevelProfileList[m_CurrentLevel];
	CLevelLaborProfile *pLaborProfile;

	if (m_CurrentHandlingIdx >= 0) {
		pLaborProfile = pLevelProfile->m_LevelLaborProfileList[m_CurrentHandlingIdx];
		m_Cube.Format("%.4f", pLaborProfile->m_Cube);
		m_Fixed.Format("%.4f", pLaborProfile->m_FixedFactor);
		m_Variable.Format("%.4f", pLaborProfile->m_VariableFactor);
	}
	else {
		m_Cube.Format("%.4f", 0);
		m_Fixed.Format("%.4f", 0);
		m_Variable.Format("%.4f", 0);
	}

	UpdateData(FALSE);

	return TRUE;  // return TRUE unless you set the focus to a control
	              // EXCEPTION: OCX Property Pages should return FALSE
}

void CBayProfileHandlingProperties::OnOK() 
{
	UpdateData(TRUE);

	if (! ValidateNumeric(DT_FLOAT, IDC_CUBE, "Cube", m_Cube))
		return;

	if (! ValidateNumeric(DT_FLOAT, IDC_FIXED, "Fixed Handling Time", m_Fixed))
		return;
		
	if (! ValidateNumeric(DT_FLOAT, IDC_FIXED, "Variable Handling Time", m_Variable))
		return;

	m_WorkType = m_WorkTypeListCtrl.GetItemData(m_WorkTypeListCtrl.GetCurSel());

	CLevelProfile *pLevelProfile = m_pBayProfile->m_LevelProfileList[m_CurrentLevel];
	for (int i=0; i < pLevelProfile->m_LevelLaborProfileList.GetSize(); ++i) {
		CLevelLaborProfile *pLaborProfile = pLevelProfile->m_LevelLaborProfileList[i];
		if (i == m_CurrentHandlingIdx)
			continue;

		if (pLaborProfile->m_WorkType != m_WorkType)
			continue;

		if (pLaborProfile->m_Cube == atof(m_Cube)) {
			AfxMessageBox("The cube value specified already exists. Please enter a unique cube value.");
			CEdit *pEdit = (CEdit *)GetDlgItem(IDC_CUBE);
			pEdit->SetSel(0,-1);
			pEdit->SetFocus();
			return;
		}
	}

	CDialog::OnOK();
}


BOOL CBayProfileHandlingProperties::OnHelpInfo(HELPINFO* pHelpInfo) 
{
	helpService.ShowFieldHelp(IDD, pHelpInfo->iCtrlId);

	return FALSE;

}

void CBayProfileHandlingProperties::OnHelp() 
{
	helpService.ShowScreenHelp(IDD);

	return;

}


BOOL CBayProfileHandlingProperties::ValidateNumeric(int type, int fieldId, const CString &fieldName, CString &fieldValue)
{
	if (type == DT_INT) {
		if (! utilityHelper.IsInteger(fieldValue)) {
			CString temp;
			temp.Format("Please enter a valid integer value for %s", fieldName);
			AfxMessageBox(temp);
			CEdit *pEdit = (CEdit *)GetDlgItem(fieldId);
			pEdit->SetSel(0,-1);
			pEdit->SetFocus();
			return FALSE;
		}
	}
	else if (type == DT_FLOAT) {
		if (! utilityHelper.IsFloat(fieldValue)) {
			CString temp;
			temp.Format("Please enter a valid decimal value for %s", fieldName);
			AfxMessageBox(temp);
			CEdit *pEdit = (CEdit *)GetDlgItem(fieldId);
			pEdit->SetSel(0,-1);
			pEdit->SetFocus();
			return FALSE;
		}
	}
	
	return TRUE;

}
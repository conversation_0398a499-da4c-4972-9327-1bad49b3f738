// LocationNumberingService.cpp: implementation of the CLocationNumberingService class.
//
//////////////////////////////////////////////////////////////////////

#include "stdafx.h"
#include "modal.h"
#include "LocationNumberingService.h"
#include "BTreeHelper.h"
#include "ssabtree.h"
#include "UtilityHelper.h"
#include "ssa_exception.h"

#include <math.h>

#ifdef _DEBUG
#undef THIS_FILE
static char THIS_FILE[]=__FILE__;
#define new DEBUG_NEW
#endif

WORD leftBayPatternPos, leftLevelPatternPos, leftLocPatternPos;
WORD rightBayPatternPos, rightLevelPatternPos, rightLocPatternPos;

extern CUtilityHelper utilityHelper;

//////////////////////////////////////////////////////////////////////
// Construction/Destruction
//////////////////////////////////////////////////////////////////////

CLocationNumberingService::CLocationNumberingService()
{

}

CLocationNumberingService::~CLocationNumberingService()
{

}


void CLocationNumberingService::BuildPatternList(CStringArray &patternList, CString pattern)
{
	int idx;
	
	patternList.RemoveAll();

	if (pattern == " " || pattern.IsEmpty())
		return;

	if (pattern.Find(",") == -1) {		// not comma delimited
		for (int i = 0; i < pattern.GetLength(); ++i)
			patternList.Add(pattern.GetAt(i));
	}
	else {								// comma delimited
		idx = pattern.Find(",");
		while (idx >= 0) {
			patternList.Add(pattern.Left(idx));
			pattern = pattern.Mid(idx+1);
			idx = pattern.Find(",");
		}
		patternList.Add(pattern);
	}

	return;

}


void CLocationNumberingService::NumberBays(CArray <int, int&> &bayIndexes, 
				int selIndex, 
				int aisleIndex, 
				CArray <int,int&> &aisleIndexList, 
				int sideSel, 
				CString &leftBayStart, CString &rightBayStart,
				CString &leftLevelStart, CString &rightLevelStart,
				CString &leftLocationStart, CString &rightLocationStart,
				int leftBayScheme, int rightBayScheme,
				int leftLevelScheme, int rightLevelScheme,
				int leftLocationScheme, int rightLocationScheme,
				int bayleftStep, int bayrightStep,
				int levelleftStep, int levelrightStep,
				int locationleftStep, int locationrightStep,
				int levelleftBreak, int levelrightBreak,
				int locationleftBreak, int locationrightBreak,
				int bayPattern, 
				int maxBayLength, 
				TreeElement & changesTree, 
				int sectionIndex,
				CArray <int, int&> &sideSelArray,
				CString &leftBayPattern,
				CString &leftLevelPattern,
				CString &leftLocPattern,
				CString &rightBayPattern,
				CString &rightLevelPattern,
				CString &rightLocPattern) 
{

	CBTreeHelper bTreeHelper;

	int sideIndex[2];
	int firstTimeRight, firstTimeLeft;
	int i,j,l;
	int incr;
	int direction;
	int start_i, stop_i;
	qqhSLOTSection tempSection;
	qqhSLOTAisle tempAisle;

//	int tempSideInt = sideSelArray.GetSize();

	CSsaStringArray levelleftHoldIDs;
	CSsaStringArray levelrightHoldIDs;

	CWordArray levelLeftPatternHolds;
	CWordArray levelRightPatternHolds;
	
	CString previousleftLevelID, nextleftLevelID;
	CString previousleftLocationID, nextleftLocationID;
	CString previousleftBayID, nextleftBayID;
	CString previousrightLevelID, nextrightLevelID;
	CString previousrightLocationID, nextrightLocationID;
	CString previousrightBayID, nextrightBayID;

	CStringArray rightBayPatternList, rightLevelPatternList, rightLocPatternList;
	CStringArray leftBayPatternList, leftLevelPatternList, leftLocPatternList;
	int z;

	////////////////////////////////////////////////////////////////
	// Number the bays and update the descriptions of the bays 
	// stored in secondary storage.
	////////////////////////////////////////////////////////////////
	


	bTreeHelper.GetBtSection(changesTree.treeChildren[sectionIndex].fileOffset,tempSection); 
	bTreeHelper.GetBtAisle( changesTree.treeChildren[sectionIndex].treeChildren[aisleIndex].fileOffset,tempAisle);
	
	int bayCount = 0;
	
	if (changesTree.treeChildren[sectionIndex].treeChildren[aisleIndex].treeChildren.GetSize() > 0)
		bayCount += changesTree.treeChildren[sectionIndex].treeChildren[aisleIndex].treeChildren[0].treeChildren.GetSize();
	
	if (changesTree.treeChildren[sectionIndex].treeChildren[aisleIndex].treeChildren.GetSize() > 1)
		bayCount += changesTree.treeChildren[sectionIndex].treeChildren[aisleIndex].treeChildren[1].treeChildren.GetSize();


	CString forteRoot = getenv("TEMP");

	forteRoot += "\\tempfile.btd";
	int openRet = open_datastore(forteRoot.GetBuffer(0));
	forteRoot.ReleaseBuffer();
	if (openRet != 0) {
//		ads_printf("Cannot open Facility storage\n");
		return;
	}

	leftBayPatternPos = leftLevelPatternPos = leftLocPatternPos = 0;
	rightBayPatternPos = rightLevelPatternPos = rightLocPatternPos = 0;

	BuildPatternList(leftBayPatternList, leftBayPattern);
	BuildPatternList(leftLevelPatternList, leftLevelPattern);
	BuildPatternList(leftLocPatternList, leftLocPattern);
	BuildPatternList(rightBayPatternList, rightBayPattern);
	BuildPatternList(rightLevelPatternList, rightLevelPattern);
	BuildPatternList(rightLocPatternList, rightLocPattern);

	switch ( selIndex ) {
		case 0 : {
			//////////////////////////////////////////////////////////
			// Cross Aisle Numbering
			//////////////////////////////////////////////////////////
			////////////////////////////////////////////////////////////////
			// set up indexes based on whether they are traveling up or 
			// down
			////////////////////////////////////////////////////////////////
			if ( bayIndexes[0] == 0 ) {
				start_i = 0;
				stop_i = maxBayLength;
				incr = 1;
				direction = 1;
				if ( sideSel == 0 ) {
					sideIndex[0] = 0;
					sideIndex[1] = 1;
				}
				else {
					sideIndex[0] = 1;
					sideIndex[1] = 0;
				}
			}
			else {
				start_i = maxBayLength - 1;
				stop_i = -1;
				incr = -1;
				direction = -1;
				if ( sideSel == 0 ) {
					sideSel = 1;
					sideIndex[0] = 0;
					sideIndex[1] = 1;
				}
				else {
					sideSel = 0;
					sideIndex[0] = 1;
					sideIndex[1] = 0;
				}
			}

			int side0count = changesTree.treeChildren[sectionIndex].treeChildren[aisleIndex].treeChildren[sideIndex[0]].treeChildren.GetSize();
			int side1count = changesTree.treeChildren[sectionIndex].treeChildren[aisleIndex].treeChildren[sideIndex[1]].treeChildren.GetSize();
			int side0diff = 0, side1diff = 0;
			if (start_i != 0) {
				if (side0count > side1count)
					side1diff = side0count - side1count;
				if (side1count > side0count)
					side0diff = side1count - side0count;
			}

			////////////////////////////////////////////////////////////////
			// Left side first selected - left then right
			////////////////////////////////////////////////////////////////
			if ( sideSel == 0 ) {
				previousleftBayID = leftBayStart;
				previousrightBayID = rightBayStart;
				firstTimeRight = firstTimeLeft = 1;
				for ( i = start_i; i != stop_i; i+= incr) {
					int curBay = i - side0diff;

					if ( curBay < side0count && curBay >= 0 ) {

						if ( previousleftBayID == leftBayStart && firstTimeLeft == 1) {
							numberLeftBay(nextleftBayID, previousleftBayID,bayleftStep,
									tempSection.getLocationMask(),levelleftBreak,previousleftLevelID,
									locationleftBreak,leftLevelStart,leftLocationStart,
									previousleftLocationID,
									nextleftLevelID, nextleftLocationID,leftLevelScheme,leftLocationScheme,
									leftBayScheme, tempAisle.getDescription(), tempSection.getDescription(), levelleftHoldIDs, 1,
									levelleftStep, direction, locationleftStep,
									changesTree.treeChildren[sectionIndex].treeChildren[aisleIndex].treeChildren[sideIndex[0]].treeChildren[curBay],
									leftBayPatternList,leftLevelPatternList,leftLocPatternList, levelLeftPatternHolds);
							firstTimeLeft = 0;
						}
						else
							numberLeftBay(nextleftBayID, previousleftBayID,bayleftStep,
									tempSection.getLocationMask(),levelleftBreak,previousleftLevelID,
									locationleftBreak,leftLevelStart,leftLocationStart,
									previousleftLocationID,
									nextleftLevelID, nextleftLocationID,leftLevelScheme,leftLocationScheme,
									leftBayScheme, tempAisle.getDescription(), tempSection.getDescription(), levelleftHoldIDs, 0,
									levelleftStep, direction, locationleftStep,
									changesTree.treeChildren[sectionIndex].treeChildren[aisleIndex].treeChildren[sideIndex[0]].treeChildren[i],
									leftBayPatternList,leftLevelPatternList,leftLocPatternList, levelLeftPatternHolds);
					}

					previousrightBayID = previousleftBayID;
					previousrightLevelID = previousleftLevelID;
					previousrightLocationID = previousleftLocationID;
					for (z=0; z < levelleftHoldIDs.GetSize(); ++z) {
						if (levelrightHoldIDs.GetSize() > z)
							levelrightHoldIDs[z] = levelleftHoldIDs[z];
						else {
							levelrightHoldIDs.Add(levelleftHoldIDs[z]);
							levelRightPatternHolds.Add(0);
						}
					}
					curBay = i - side1diff;

					if ( curBay < side1count && curBay >= 0 )
						numberRightBay(nextrightBayID, previousrightBayID, bayrightStep,
								tempSection.getLocationMask(), levelrightBreak, previousrightLevelID,
								locationrightBreak, rightLevelStart, rightLocationStart,
								previousrightLocationID,
								nextrightLevelID, nextrightLocationID, rightLevelScheme, rightLocationScheme,
								rightBayScheme, tempAisle.getDescription(), tempSection.getDescription(), levelrightHoldIDs, 0,
								levelrightStep, direction, locationrightStep,
								changesTree.treeChildren[sectionIndex].treeChildren[aisleIndex].treeChildren[sideIndex[1]].treeChildren[curBay],
								rightBayPatternList, rightLevelPatternList, rightLocPatternList, levelRightPatternHolds);
					previousleftBayID = previousrightBayID;
					previousleftLevelID = previousrightLevelID;
					previousleftLocationID = previousrightLocationID;
					for (z=0; z < levelrightHoldIDs.GetSize(); ++z) {
						if (levelleftHoldIDs.GetSize() > z)
							levelleftHoldIDs[z] = levelrightHoldIDs[z];
						else {
							levelleftHoldIDs.Add(levelrightHoldIDs[z]);
							levelLeftPatternHolds.Add(0);
						}
					}
				}
			}
			////////////////////////////////////////////////////////////////
			// Right side first selected - right then left
			////////////////////////////////////////////////////////////////
			else {
				previousrightBayID = rightBayStart;
				previousleftBayID = leftBayStart;
				firstTimeRight = firstTimeLeft = 1;
				for ( i = start_i; i != stop_i; i+= incr) {
					int curBay = i - side0diff;

					if ( curBay < side0count && curBay >= 0 ) {
						if ( previousrightBayID == rightBayStart && firstTimeRight == 1) {
							numberRightBay(nextrightBayID, previousrightBayID, bayrightStep,
									tempSection.getLocationMask(), levelrightBreak, previousrightLevelID,
									locationrightBreak, rightLevelStart, rightLocationStart,
									previousrightLocationID,
									nextrightLevelID, nextrightLocationID, rightLevelScheme, rightLocationScheme,
									rightBayScheme, tempAisle.getDescription(), tempSection.getDescription(), levelrightHoldIDs, 1,
									levelrightStep, direction, locationrightStep,
									changesTree.treeChildren[sectionIndex].treeChildren[aisleIndex].treeChildren[sideIndex[0]].treeChildren[curBay],
									rightBayPatternList, rightLevelPatternList, rightLocPatternList, levelRightPatternHolds);
							firstTimeRight = 0;
						}
						else
							numberRightBay(nextrightBayID, previousrightBayID, bayrightStep,
									tempSection.getLocationMask(), levelrightBreak, previousrightLevelID,
									locationrightBreak, rightLevelStart, rightLocationStart,
									previousrightLocationID,
									nextrightLevelID, nextrightLocationID, rightLevelScheme, rightLocationScheme,
									rightBayScheme, tempAisle.getDescription(), tempSection.getDescription(), levelrightHoldIDs, 0,
									levelrightStep, direction, locationrightStep,
									changesTree.treeChildren[sectionIndex].treeChildren[aisleIndex].treeChildren[sideIndex[0]].treeChildren[curBay],
									rightBayPatternList, rightLevelPatternList, rightLocPatternList, levelRightPatternHolds);
					}
					previousleftBayID = previousrightBayID;
					previousleftLevelID = previousrightLevelID;
					previousleftLocationID = previousrightLocationID;
					for (z=0; z < levelrightHoldIDs.GetSize(); ++z) {
						if (levelleftHoldIDs.GetSize() > z)
							levelleftHoldIDs[z] = levelrightHoldIDs[z];
						else {
							levelleftHoldIDs.Add(levelrightHoldIDs[z]);
							levelLeftPatternHolds.Add(0);
						}
					}
					curBay = i - side1diff;

					if ( curBay < side1count && curBay >= 0 ) {
						numberLeftBay(nextleftBayID, previousleftBayID,bayleftStep,
								tempSection.getLocationMask(),levelleftBreak,previousleftLevelID,
								locationleftBreak,leftLevelStart,leftLocationStart,
								previousleftLocationID,
								nextleftLevelID, nextleftLocationID,leftLevelScheme,leftLocationScheme,
								leftBayScheme, tempAisle.getDescription(), tempSection.getDescription(), levelleftHoldIDs, 0,
								levelleftStep, direction, locationleftStep,
								changesTree.treeChildren[sectionIndex].treeChildren[aisleIndex].treeChildren[sideIndex[1]].treeChildren[curBay],
								leftBayPatternList,leftLevelPatternList,leftLocPatternList, levelLeftPatternHolds);
					}
					previousrightBayID = previousleftBayID;
					previousrightLevelID = previousleftLevelID;
					previousrightLocationID = previousleftLocationID;
					for (z=0; z < levelleftHoldIDs.GetSize(); ++z) {
						if (levelrightHoldIDs.GetSize() > z)
							levelrightHoldIDs[z] = levelleftHoldIDs[z];
						else {
							levelrightHoldIDs.Add(levelleftHoldIDs[z]);
							levelRightPatternHolds.Add(0);
						}
					}
				}
			}
			break;
		}
		case 1 : {
			//////////////////////////////////////////////////////////
			// Finger Numbering
			//////////////////////////////////////////////////////////
			////////////////////////////////////////////////////////////////
			// set up indexes based on whether they are traveling up or 
			// down
			////////////////////////////////////////////////////////////////
			for (l = 0; l < aisleIndexList.GetSize(); l++ ) {
				bTreeHelper.ReadNoOpenBtAisle(changesTree.treeChildren[sectionIndex].treeChildren[aisleIndexList[l]].fileOffset,tempAisle);
				if ( bayIndexes[l] == 0 ) {
					start_i = 0;
					incr = 1;
					direction = 1;
					//if ( sideSelArray[l] == 0 ) {
						sideIndex[0] = 0;
						sideIndex[1] = 1;
					//}
					//else {
					//	sideIndex[0] = 1;
					//	sideIndex[1] = 0;
					//}
					if ( changesTree.treeChildren[sectionIndex].treeChildren[aisleIndexList[l]].treeChildren[sideIndex[0]].treeChildren.GetSize() >
						changesTree.treeChildren[sectionIndex].treeChildren[aisleIndexList[l]].treeChildren[sideIndex[1]].treeChildren.GetSize() )
						stop_i = changesTree.treeChildren[sectionIndex].treeChildren[aisleIndexList[l]].treeChildren[sideIndex[0]].treeChildren.GetSize();
					else
						stop_i = changesTree.treeChildren[sectionIndex].treeChildren[aisleIndexList[l]].treeChildren[sideIndex[1]].treeChildren.GetSize();
				}
				else {
					stop_i = -1;
					incr = -1;
					direction = -1;
				//	if ( sideSelArray[l] == 0 ) {
				//		sideSelArray[l] = 1;
				//		sideIndex[0] = 1;
				//		sideIndex[1] = 0;
				//	}
				//	else {
				//		sideSel = 0;
				//		sideIndex[0] = 0;
				//		sideIndex[1] = 1;
				//	}
					sideIndex[0] = 1;
					sideIndex[1] = 0;
					if ( changesTree.treeChildren[sectionIndex].treeChildren[aisleIndexList[l]].treeChildren[sideIndex[0]].treeChildren.GetSize() >
						changesTree.treeChildren[sectionIndex].treeChildren[aisleIndexList[l]].treeChildren[sideIndex[1]].treeChildren.GetSize() )
						start_i = changesTree.treeChildren[sectionIndex].treeChildren[aisleIndexList[l]].treeChildren[sideIndex[0]].treeChildren.GetSize()-1;
					else
						start_i = changesTree.treeChildren[sectionIndex].treeChildren[aisleIndexList[l]].treeChildren[sideIndex[1]].treeChildren.GetSize()-1;
				}
				previousleftBayID = leftBayStart;
				previousrightBayID = rightBayStart;
				////////////////////////////////////////////////////////////////
				// for each aisle, check whether they chose left or right side.
				// Number accordingly.
				////////////////////////////////////////////////////////////////
//				if ( sideSelArray[l] == 0 ) 
//				{
					firstTimeRight = firstTimeLeft = 1;
					for ( i = start_i; i != stop_i; i+= incr) 
					{
						///////////////////////////////////////////////////////	
						// 
						///////////////////////////////////////////////////////
						if ( i < changesTree.treeChildren[sectionIndex].treeChildren[aisleIndexList[l]].treeChildren[sideIndex[0]].treeChildren.GetSize()) {
							if ( previousleftBayID == leftBayStart && firstTimeLeft == 1) {
								numberLeftBay(nextleftBayID, previousleftBayID,bayleftStep,
										tempSection.getLocationMask(),levelleftBreak,previousleftLevelID,
										locationleftBreak,leftLevelStart,leftLocationStart,
										previousleftLocationID,
										nextleftLevelID, nextleftLocationID,leftLevelScheme,leftLocationScheme,
										leftBayScheme, tempAisle.getDescription(), tempSection.getDescription(), levelleftHoldIDs, 1,
										levelleftStep, direction, locationleftStep,
										changesTree.treeChildren[sectionIndex].treeChildren[aisleIndexList[l]].treeChildren[sideIndex[0]].treeChildren[i],
										leftBayPatternList,leftLevelPatternList,leftLocPatternList, levelLeftPatternHolds);
								firstTimeLeft = 0;
							}
							else
								numberLeftBay(nextleftBayID, previousleftBayID,bayleftStep,
										tempSection.getLocationMask(),levelleftBreak,previousleftLevelID,
										locationleftBreak,leftLevelStart,leftLocationStart,
										previousleftLocationID,
										nextleftLevelID, nextleftLocationID,leftLevelScheme,leftLocationScheme,
										leftBayScheme, tempAisle.getDescription(), tempSection.getDescription(), levelleftHoldIDs, 0,
										levelleftStep, direction, locationleftStep,
										changesTree.treeChildren[sectionIndex].treeChildren[aisleIndexList[l]].treeChildren[sideIndex[0]].treeChildren[i],
										leftBayPatternList,leftLevelPatternList,leftLocPatternList, levelLeftPatternHolds);
						}
						if ( i < changesTree.treeChildren[sectionIndex].treeChildren[aisleIndexList[l]].treeChildren[sideIndex[1]].treeChildren.GetSize()) {
							if ( previousrightBayID == rightBayStart && firstTimeRight == 1) {
								numberRightBay(nextrightBayID, previousrightBayID, bayrightStep,
										tempSection.getLocationMask(), levelrightBreak, previousrightLevelID,
										locationrightBreak, rightLevelStart, rightLocationStart,
										previousrightLocationID,
										nextrightLevelID, nextrightLocationID, rightLevelScheme, rightLocationScheme,
										rightBayScheme, tempAisle.getDescription(), tempSection.getDescription(), levelrightHoldIDs, 1,
										levelrightStep, direction, locationrightStep,
										changesTree.treeChildren[sectionIndex].treeChildren[aisleIndexList[l]].treeChildren[sideIndex[1]].treeChildren[i],
										rightBayPatternList, rightLevelPatternList, rightLocPatternList, levelRightPatternHolds);
								firstTimeRight = 0;
							}
							else
								numberRightBay(nextrightBayID, previousrightBayID, bayrightStep,
										tempSection.getLocationMask(), levelrightBreak, previousrightLevelID,
										locationrightBreak, rightLevelStart, rightLocationStart,
										previousrightLocationID,
										nextrightLevelID, nextrightLocationID, rightLevelScheme, rightLocationScheme,
										rightBayScheme, tempAisle.getDescription(), tempSection.getDescription(), levelrightHoldIDs, 0,
										levelrightStep, direction, locationrightStep,
										changesTree.treeChildren[sectionIndex].treeChildren[aisleIndexList[l]].treeChildren[sideIndex[1]].treeChildren[i],
										rightBayPatternList, rightLevelPatternList, rightLocPatternList, levelRightPatternHolds);
						}
					}
			}
			break;
		}
		case 2 : {
			//////////////////////////////////////////////////////////
			// Straight-thru Numbering
			//////////////////////////////////////////////////////////
			////////////////////////////////////////////////////////////////
			// set up indexes based on whether they are traveling up or 
			// down
			////////////////////////////////////////////////////////////////
			if ( bayIndexes[0] == 0 ) {
				start_i = 0;
				stop_i = maxBayLength;
				incr = 1;
				direction = 1;
				//if ( sideSel == 0 ) {
					sideIndex[0] = 0;
					sideIndex[1] = 1;
				//}
				//else {
				//	sideIndex[0] = 1;
				//	sideIndex[1] = 0;
				//}
			}
			else {
				start_i = maxBayLength - 1;
				stop_i = -1;
				incr = -1;
				direction = -1;
				//if ( sideSel == 0 ) {
				//	sideSel = 1;
				//	sideIndex[0] = 1;
				//	sideIndex[1] = 0;
				//}
				//else {
				//	sideSel = 0;
				//	sideIndex[0] = 0;
				//	sideIndex[1] = 1;
				//}
				sideIndex[0] = 1;
				sideIndex[1] = 0;

			}
			previousleftBayID = leftBayStart;
			previousrightBayID = rightBayStart;
			firstTimeRight = firstTimeLeft = 1;
				for ( i = start_i; i != stop_i; i+= incr) {
					///////////////////////////////////////////////////////	
					// Does not matter whether right or left side was
					// chosen, just number according to the profile
					///////////////////////////////////////////////////////
					if ( i < changesTree.treeChildren[sectionIndex].treeChildren[aisleIndex].treeChildren[sideIndex[0]].treeChildren.GetSize()) {
						if ( previousleftBayID == leftBayStart && firstTimeLeft == 1) {
							numberLeftBay(nextleftBayID, previousleftBayID,bayleftStep,
									tempSection.getLocationMask(),levelleftBreak,previousleftLevelID,
									locationleftBreak,leftLevelStart,leftLocationStart,
									previousleftLocationID,
									nextleftLevelID, nextleftLocationID,leftLevelScheme,leftLocationScheme,
									leftBayScheme, tempAisle.getDescription(), tempSection.getDescription(), levelleftHoldIDs, 1,
									levelleftStep, direction, locationleftStep,
									changesTree.treeChildren[sectionIndex].treeChildren[aisleIndex].treeChildren[sideIndex[0]].treeChildren[i],
									leftBayPatternList,leftLevelPatternList,leftLocPatternList, levelLeftPatternHolds);
							firstTimeLeft = 0;
						}
						else
							numberLeftBay(nextleftBayID, previousleftBayID,bayleftStep,
									tempSection.getLocationMask(),levelleftBreak,previousleftLevelID,
									locationleftBreak,leftLevelStart,leftLocationStart,
									previousleftLocationID,
									nextleftLevelID, nextleftLocationID,leftLevelScheme,leftLocationScheme,
									leftBayScheme, tempAisle.getDescription(), tempSection.getDescription(), levelleftHoldIDs, 0,
									levelleftStep, direction, locationleftStep,
									changesTree.treeChildren[sectionIndex].treeChildren[aisleIndex].treeChildren[sideIndex[0]].treeChildren[i],
									leftBayPatternList,leftLevelPatternList,leftLocPatternList, levelLeftPatternHolds);
					}
					if ( i < changesTree.treeChildren[sectionIndex].treeChildren[aisleIndex].treeChildren[sideIndex[1]].treeChildren.GetSize()) {
						if ( previousrightBayID == rightBayStart && firstTimeRight == 1) {
							numberRightBay(nextrightBayID, previousrightBayID, bayrightStep,
									tempSection.getLocationMask(), levelrightBreak, previousrightLevelID,
									locationrightBreak, rightLevelStart, rightLocationStart,
									previousrightLocationID,
									nextrightLevelID, nextrightLocationID, rightLevelScheme, rightLocationScheme,
									rightBayScheme, tempAisle.getDescription(), tempSection.getDescription(), levelrightHoldIDs, 1,
									levelrightStep, direction, locationrightStep,
									changesTree.treeChildren[sectionIndex].treeChildren[aisleIndex].treeChildren[sideIndex[1]].treeChildren[i],
									rightBayPatternList, rightLevelPatternList, rightLocPatternList, levelRightPatternHolds);
							firstTimeRight = 0;
						}
						else
							numberRightBay(nextrightBayID, previousrightBayID, bayrightStep,
									tempSection.getLocationMask(), levelrightBreak, previousrightLevelID,
									locationrightBreak, rightLevelStart, rightLocationStart,
									previousrightLocationID,
									nextrightLevelID, nextrightLocationID, rightLevelScheme, rightLocationScheme,
									rightBayScheme, tempAisle.getDescription(), tempSection.getDescription(), levelrightHoldIDs, 0,
									levelrightStep, direction, locationrightStep,
									changesTree.treeChildren[sectionIndex].treeChildren[aisleIndex].treeChildren[sideIndex[1]].treeChildren[i],
									rightBayPatternList, rightLevelPatternList, rightLocPatternList, levelRightPatternHolds );
					}
				}
			break;
		}
		case 3 : {
			//////////////////////////////////////////////////////////
			// Two-Way Numbering
			//////////////////////////////////////////////////////////
			////////////////////////////////////////////////////////////////
			// set up indexes based on whether they are traveling up or 
			// down
			////////////////////////////////////////////////////////////////
			if ( bayIndexes[0] == 0 ) {		// they chose the first bay in the aisle
				direction = 1;				// bottom to top or left to right
				if ( sideSel == 0 ) {		// the user chose the left side
					sideIndex[0] = 0;		// set the first side to the left side
					sideIndex[1] = 1;
				}
				else {						// the user chose the right side
					sideIndex[0] = 1;		// set the first side to the right side
					sideIndex[1] = 0;
				}
				start_i = 0;				// start at the first bay
											// end at the last bay on the first side
				stop_i = changesTree.treeChildren[sectionIndex].treeChildren[aisleIndex].treeChildren[sideIndex[0]].treeChildren.GetSize();
				incr = 1;
			}
			else {
				direction = -1;			// top to bottom or right to left
				if ( sideSel == 0 ) {	// the user chose the left side
					sideSel = 1;		// since we are going backwards, the left side on the screen
					sideIndex[0] = 0;	// is actually the right side when walking the path	
					sideIndex[1] = 1;	// so set the first side to the screen left side and sidesel to right
				}
				else {					// the user chose the right side
					sideSel = 0;		// since we are going backwards, the right side on the screen
					sideIndex[0] = 1;	// is actually the left side when walking the path
					sideIndex[1] = 0;	// so set the first side to the screen right side and sidesel to left
				}
				// start at the last bay on the first side
				start_i = changesTree.treeChildren[sectionIndex].treeChildren[aisleIndex].treeChildren[sideIndex[0]].treeChildren.GetSize() - 1;
				stop_i = -1;			// end at the beginning
				incr = -1;
			}
			////////////////////////////////////////////////////////////////
			// left side chosen - left then right numbering
			////////////////////////////////////////////////////////////////
			if ( sideSel == 0 ) {
				previousleftBayID = leftBayStart;
				previousrightBayID = rightBayStart;
				firstTimeRight = firstTimeLeft = 1;
				for ( i = start_i; i != stop_i; i+= incr) {
					if ( previousleftBayID == leftBayStart && firstTimeLeft == 1) {
						numberLeftBay(nextleftBayID, previousleftBayID,bayleftStep,
								tempSection.getLocationMask(),levelleftBreak,previousleftLevelID,
								locationleftBreak,leftLevelStart,leftLocationStart,
								previousleftLocationID,
								nextleftLevelID, nextleftLocationID,leftLevelScheme,leftLocationScheme,
								leftBayScheme, tempAisle.getDescription(), tempSection.getDescription(), levelleftHoldIDs, 1,
								levelleftStep, direction, locationleftStep,
								changesTree.treeChildren[sectionIndex].treeChildren[aisleIndex].treeChildren[sideIndex[0]].treeChildren[i],
								leftBayPatternList,leftLevelPatternList,leftLocPatternList, levelLeftPatternHolds);
						firstTimeLeft = 0;
					}
					else
						numberLeftBay(nextleftBayID, previousleftBayID,bayleftStep,
								tempSection.getLocationMask(),levelleftBreak,previousleftLevelID,
								locationleftBreak,leftLevelStart,leftLocationStart,
								previousleftLocationID,
								nextleftLevelID, nextleftLocationID,leftLevelScheme,leftLocationScheme,
								leftBayScheme, tempAisle.getDescription(), tempSection.getDescription(), levelleftHoldIDs, 0,
								levelleftStep, direction, locationleftStep,
								changesTree.treeChildren[sectionIndex].treeChildren[aisleIndex].treeChildren[sideIndex[0]].treeChildren[i],
								leftBayPatternList,leftLevelPatternList,leftLocPatternList, levelLeftPatternHolds );
				}
				////////////////////////////////////////////////////////////////
				// reset indexes - traveling in opposite direction
				////////////////////////////////////////////////////////////////
				if ( bayIndexes[0] == 0 ) {
					if ( sideSel == 0 ) {
						sideIndex[0] = 0;
						sideIndex[1] = 1;
					}
					else {
						sideIndex[0] = 1;
						sideIndex[1] = 0;
					}
					start_i = changesTree.treeChildren[sectionIndex].treeChildren[aisleIndex].treeChildren[sideIndex[1]].treeChildren.GetSize() - 1;
					stop_i = -1;
					incr = -1;
				}
				else {
					if ( sideSel == 1 ) {		// changed this from 0 to 1 since we've already reversed sidesel above
						sideIndex[0] = 0;
						sideIndex[1] = 1;
					}
					else {
						sideIndex[0] = 1;
						sideIndex[1] = 0;
					}
					start_i = 0;
					stop_i = changesTree.treeChildren[sectionIndex].treeChildren[aisleIndex].treeChildren[sideIndex[1]].treeChildren.GetSize();
					incr = 1;
				}
				//rightBayStart = nextleftBayID;
				//rightLevelStart = previousleftLevelID;
				//rightLocationStart = previousleftLocationID;
				if (rightBayStart.CompareNoCase("Left") == 0) {
					rightBayStart = nextleftBayID;
					previousrightBayID = nextleftBayID;
					firstTimeRight = 0;
				}

				for ( i = start_i; i != stop_i; i+= incr) {
					if ( previousrightBayID == rightBayStart && firstTimeRight == 1) {
						numberRightBay(nextrightBayID, previousrightBayID, bayrightStep,
								tempSection.getLocationMask(), levelrightBreak, previousrightLevelID,
								locationrightBreak, rightLevelStart, rightLocationStart,
								previousrightLocationID,
								nextrightLevelID, nextrightLocationID, rightLevelScheme, rightLocationScheme,
								rightBayScheme, tempAisle.getDescription(), tempSection.getDescription(), levelrightHoldIDs, 1,
								levelrightStep, direction, locationrightStep,
								changesTree.treeChildren[sectionIndex].treeChildren[aisleIndex].treeChildren[sideIndex[1]].treeChildren[i],
								rightBayPatternList, rightLevelPatternList, rightLocPatternList, levelRightPatternHolds);
						firstTimeRight = 0;
					}
					else
						numberRightBay(nextrightBayID, previousrightBayID, bayrightStep,
								tempSection.getLocationMask(), levelrightBreak, previousrightLevelID,
								locationrightBreak, rightLevelStart, rightLocationStart,
								previousrightLocationID,
								nextrightLevelID, nextrightLocationID, rightLevelScheme, rightLocationScheme,
								rightBayScheme, tempAisle.getDescription(), tempSection.getDescription(), levelrightHoldIDs, 0,
								levelrightStep, direction, locationrightStep,
								changesTree.treeChildren[sectionIndex].treeChildren[aisleIndex].treeChildren[sideIndex[1]].treeChildren[i],
								rightBayPatternList, rightLevelPatternList, rightLocPatternList, levelRightPatternHolds);
				}
			}
			////////////////////////////////////////////////////////////////
			// right side chosen - right then left numbering
			////////////////////////////////////////////////////////////////
			else {
				previousleftBayID = leftBayStart;
				previousrightBayID = rightBayStart;
				firstTimeRight = firstTimeLeft = 1;
				for ( i = start_i; i != stop_i; i+= incr) {
					if ( previousrightBayID == rightBayStart && firstTimeRight == 1) {
						numberRightBay(nextrightBayID, previousrightBayID, bayrightStep,
								tempSection.getLocationMask(), levelrightBreak, previousrightLevelID,
								locationrightBreak, rightLevelStart, rightLocationStart,
								previousrightLocationID,
								nextrightLevelID, nextrightLocationID, rightLevelScheme, rightLocationScheme,
								rightBayScheme, tempAisle.getDescription(), tempSection.getDescription(), levelrightHoldIDs, 1,
								levelrightStep, direction, locationrightStep,
								changesTree.treeChildren[sectionIndex].treeChildren[aisleIndex].treeChildren[sideIndex[0]].treeChildren[i],
								rightBayPatternList, rightLevelPatternList, rightLocPatternList, levelRightPatternHolds );
						firstTimeRight = 0;
					}
					else
						numberRightBay(nextrightBayID, previousrightBayID, bayrightStep,
								tempSection.getLocationMask(), levelrightBreak, previousrightLevelID,
								locationrightBreak, rightLevelStart, rightLocationStart,
								previousrightLocationID,
								nextrightLevelID, nextrightLocationID, rightLevelScheme, rightLocationScheme,
								rightBayScheme, tempAisle.getDescription(), tempSection.getDescription(), levelrightHoldIDs, 0,
								levelrightStep, direction, locationrightStep,
								changesTree.treeChildren[sectionIndex].treeChildren[aisleIndex].treeChildren[sideIndex[0]].treeChildren[i],
								rightBayPatternList, rightLevelPatternList, rightLocPatternList, levelRightPatternHolds);
				}
				////////////////////////////////////////////////////////////////
				// reset indexes - traveling in opposite direction
				////////////////////////////////////////////////////////////////
				if ( bayIndexes[0] == 0 ) {
					direction = 1;
					if ( sideSel == 0 ) {
						sideIndex[0] = 0;
						sideIndex[1] = 1;
					}
					else {
						sideIndex[0] = 1;
						sideIndex[1] = 0;
					}
					start_i = changesTree.treeChildren[sectionIndex].treeChildren[aisleIndex].treeChildren[sideIndex[1]].treeChildren.GetSize() - 1;
					stop_i = -1;
					incr = -1;
				}
				else {
					direction = -1;
					if ( sideSel == 1 ) {		// changed from 0 to 1 since we reversed it above
						sideIndex[0] = 0;
						sideIndex[1] = 1;
					}
					else {
						sideIndex[0] = 1;
						sideIndex[1] = 0;
					}
					start_i = 0;
					stop_i = changesTree.treeChildren[sectionIndex].treeChildren[aisleIndex].treeChildren[sideIndex[1]].treeChildren.GetSize();
					incr = 1;
				}
				//leftBayStart = nextrightBayID;
				//leftLevelStart = previousrightLevelID;
				//leftLocationStart = previousrightLocationID;
				if (leftBayStart.CompareNoCase("Right") == 0) {
					leftBayStart = nextrightBayID;
					previousleftBayID = nextrightBayID;
					firstTimeLeft = 0;
				}


				for ( i = start_i; i != stop_i; i+= incr) {
					if ( previousleftBayID == leftBayStart && firstTimeLeft == 1) {
						numberLeftBay(nextleftBayID, previousleftBayID,bayleftStep,
								tempSection.getLocationMask(),levelleftBreak,previousleftLevelID,
								locationleftBreak,leftLevelStart,leftLocationStart,
								previousleftLocationID,
								nextleftLevelID, nextleftLocationID,leftLevelScheme,leftLocationScheme,
								leftBayScheme, tempAisle.getDescription(), tempSection.getDescription(), levelleftHoldIDs, 1,
								levelleftStep, direction, locationleftStep,
								changesTree.treeChildren[sectionIndex].treeChildren[aisleIndex].treeChildren[sideIndex[1]].treeChildren[i],
								leftBayPatternList,leftLevelPatternList,leftLocPatternList, levelLeftPatternHolds);
						firstTimeLeft = 0;
					}
					else
						numberLeftBay(nextleftBayID, previousleftBayID,bayleftStep,
								tempSection.getLocationMask(),levelleftBreak,previousleftLevelID,
								locationleftBreak,leftLevelStart,leftLocationStart,
								previousleftLocationID,
								nextleftLevelID, nextleftLocationID,leftLevelScheme,leftLocationScheme,
								leftBayScheme, tempAisle.getDescription(), tempSection.getDescription(), levelleftHoldIDs, 0,
								levelleftStep, direction, locationleftStep,
								changesTree.treeChildren[sectionIndex].treeChildren[aisleIndex].treeChildren[sideIndex[1]].treeChildren[i],
								leftBayPatternList,leftLevelPatternList,leftLocPatternList, levelLeftPatternHolds);

				}
			}
			break;
		}
		case 4 : {
			//////////////////////////////////////////////////////////
			// U Path Numbering
			//////////////////////////////////////////////////////////
			////////////////////////////////////////////////////////////////
			// set up indexes based on whether they are traveling up or 
			// down
			////////////////////////////////////////////////////////////////
			if ( bayIndexes[0] == 0 ) {
				start_i = bayPattern - 1;
				stop_i = maxBayLength;
				incr = bayPattern;
				direction = 1;
				if ( sideSel == 0 ) {
					sideIndex[0] = 0;
					sideIndex[1] = 1;
				}
				else {
					sideIndex[0] = 0;
					sideIndex[1] = 1;
				}
			}
			else {
				start_i = maxBayLength - bayPattern;
				stop_i = 0;
				incr = -1 * bayPattern;
				direction = -1;
				if ( sideSel == 0 ) {
					sideSel = 1;
					sideIndex[0] = 1;
					sideIndex[1] = 0;
				}
				else {
					sideSel = 0;
					sideIndex[0] = 1;
					sideIndex[1] = 0;
				}
			}
			////////////////////////////////////////////////////////////////
			// left side chosen - left then right numbering
			////////////////////////////////////////////////////////////////
			if ( sideSel == 0 ) 
			{
				previousleftBayID = leftBayStart;
				previousrightBayID = rightBayStart;
				firstTimeRight = firstTimeLeft = 1;
				
				for ( i = start_i; (direction == 1 && i < stop_i) || (direction == -1 && i >= stop_i); i += incr ) 
				{
					for ( j = 0; j < bayPattern; j++) 
					{
						if ( i + (direction*-1*j) < changesTree.treeChildren[sectionIndex].treeChildren[aisleIndex].treeChildren[sideIndex[0]].treeChildren.GetSize() && i + (direction*-1*j) >= 0) 
						{
							if ( previousleftBayID == leftBayStart && firstTimeLeft == 1) {
								numberLeftBay(nextleftBayID, previousleftBayID,bayleftStep,
										tempSection.getLocationMask(),levelleftBreak,previousleftLevelID,
										locationleftBreak,leftLevelStart,leftLocationStart,
										previousleftLocationID,
										nextleftLevelID, nextleftLocationID,leftLevelScheme,leftLocationScheme,
										leftBayScheme, tempAisle.getDescription(), tempSection.getDescription(), levelleftHoldIDs, 1,
										levelleftStep, 0-direction, locationleftStep,
										changesTree.treeChildren[sectionIndex].treeChildren[aisleIndex].treeChildren[sideIndex[0]].treeChildren[i + (direction*-1*j)],
										leftBayPatternList,leftLevelPatternList,leftLocPatternList, levelLeftPatternHolds);
								firstTimeLeft = 0;
							}
							else
								numberLeftBay(nextleftBayID, previousleftBayID,bayleftStep,
										tempSection.getLocationMask(),levelleftBreak,previousleftLevelID,
										locationleftBreak,leftLevelStart,leftLocationStart,
										previousleftLocationID,
										nextleftLevelID, nextleftLocationID,leftLevelScheme,leftLocationScheme,
										leftBayScheme, tempAisle.getDescription(), tempSection.getDescription(), levelleftHoldIDs, 0,
										levelleftStep, 0-direction, locationleftStep,
										changesTree.treeChildren[sectionIndex].treeChildren[aisleIndex].treeChildren[sideIndex[0]].treeChildren[i + (direction*-1*j)],
										leftBayPatternList,leftLevelPatternList,leftLocPatternList, levelLeftPatternHolds);
						}
					}
					previousrightBayID = previousleftBayID;
					previousrightLevelID = previousleftLevelID;
					previousrightLocationID = previousleftLocationID;
					for (z=0; z < levelleftHoldIDs.GetSize(); ++z) {
						if (levelrightHoldIDs.GetSize() > z)
							levelrightHoldIDs[z] = levelleftHoldIDs[z];
						else {
							levelrightHoldIDs.Add(levelleftHoldIDs[z]);
							levelRightPatternHolds.Add(0);
						}
					}
					for ( j = bayPattern - 1; j >= 0; j-- ) 
					{
						if ( i + (direction*-1*j) < changesTree.treeChildren[sectionIndex].treeChildren[aisleIndex].treeChildren[sideIndex[1]].treeChildren.GetSize() && i + (direction*-1*j) >= 0) 
						{
							if ( previousrightBayID == rightBayStart && firstTimeRight == 1) {
								numberRightBay(nextrightBayID, previousrightBayID, bayrightStep,
										tempSection.getLocationMask(), levelrightBreak, previousrightLevelID,
										locationrightBreak, rightLevelStart, rightLocationStart,
										previousrightLocationID,
										nextrightLevelID, nextrightLocationID, rightLevelScheme, rightLocationScheme,
										rightBayScheme, tempAisle.getDescription(), tempSection.getDescription(), levelrightHoldIDs, 1,
										levelrightStep, direction, locationrightStep,
										changesTree.treeChildren[sectionIndex].treeChildren[aisleIndex].treeChildren[sideIndex[1]].treeChildren[i + (direction*-1*j)],
										rightBayPatternList, rightLevelPatternList, rightLocPatternList, levelRightPatternHolds);
								firstTimeRight = 0;
							}
							else
								numberRightBay(nextrightBayID, previousrightBayID, bayrightStep,
										tempSection.getLocationMask(), levelrightBreak, previousrightLevelID,
										locationrightBreak, rightLevelStart, rightLocationStart,
										previousrightLocationID,
										nextrightLevelID, nextrightLocationID, rightLevelScheme, rightLocationScheme,
										rightBayScheme, tempAisle.getDescription(), tempSection.getDescription(), levelrightHoldIDs, 0,
										levelrightStep, direction, locationrightStep,
										changesTree.treeChildren[sectionIndex].treeChildren[aisleIndex].treeChildren[sideIndex[1]].treeChildren[i + (direction*-1*j)],
										rightBayPatternList, rightLevelPatternList, rightLocPatternList, levelRightPatternHolds);
						}
					}
					previousleftBayID = previousrightBayID;
					previousleftLevelID = previousrightLevelID;
					previousleftLocationID = previousrightLocationID;
					for (z=0; z < levelrightHoldIDs.GetSize(); ++z) {
						if (levelleftHoldIDs.GetSize() > z)
							levelleftHoldIDs[z] = levelrightHoldIDs[z];
						else {
							levelleftHoldIDs.Add(levelrightHoldIDs[z]);
							levelLeftPatternHolds.Add(0);
						}
					}
					if ( direction == 1 ) {
						if ( (i+bayPattern) >= maxBayLength && i < maxBayLength-1) {
							incr = maxBayLength - i - 1;
							bayPattern = incr;
						}
					}
					else {
						if ( (i <= bayPattern-1) && i > 0) {
							incr = -1*i;
							bayPattern = -1 * incr;
						}
					}
				}
			}
			////////////////////////////////////////////////////////////////
			// right side chosen - right then left numbering
			////////////////////////////////////////////////////////////////
			else
			{
				previousleftBayID = leftBayStart;
				previousrightBayID = rightBayStart;
				firstTimeRight = firstTimeLeft = 1;

				for ( i = start_i; (direction == 1 && i < stop_i) || (direction == -1 && i >= stop_i); i += incr ) 
				{
					for ( j = 0; j < bayPattern; j++) 
					{
						if ( i + (direction*-1*j) < changesTree.treeChildren[sectionIndex].treeChildren[aisleIndex].treeChildren[sideIndex[1]].treeChildren.GetSize() && i + (direction*-1*j) >= 0) 
						{
							if ( previousrightBayID == rightBayStart && firstTimeRight == 1) {
								numberRightBay(nextrightBayID, previousrightBayID, bayrightStep,
										tempSection.getLocationMask(), levelrightBreak, previousrightLevelID,
										locationrightBreak, rightLevelStart, rightLocationStart,
										previousrightLocationID,
										nextrightLevelID, nextrightLocationID, rightLevelScheme, rightLocationScheme,
										rightBayScheme, tempAisle.getDescription(), tempSection.getDescription(), levelrightHoldIDs, 1,
										levelrightStep, 0-direction, locationrightStep,
										changesTree.treeChildren[sectionIndex].treeChildren[aisleIndex].treeChildren[sideIndex[1]].treeChildren[i + (direction*-1*j)],
										rightBayPatternList, rightLevelPatternList, rightLocPatternList, levelRightPatternHolds);
								firstTimeRight = 0;
							}
							else
								numberRightBay(nextrightBayID, previousrightBayID, bayrightStep,
										tempSection.getLocationMask(), levelrightBreak, previousrightLevelID,
										locationrightBreak, rightLevelStart, rightLocationStart,
										previousrightLocationID,
										nextrightLevelID, nextrightLocationID, rightLevelScheme, rightLocationScheme,
										rightBayScheme, tempAisle.getDescription(), tempSection.getDescription(), levelrightHoldIDs, 0,
										levelrightStep, 0-direction, locationrightStep,
										changesTree.treeChildren[sectionIndex].treeChildren[aisleIndex].treeChildren[sideIndex[1]].treeChildren[i + (direction*-1*j)],
										rightBayPatternList, rightLevelPatternList, rightLocPatternList, levelRightPatternHolds);
						}
					}
					previousleftBayID = previousrightBayID;
					previousleftLevelID = previousrightLevelID;
					previousleftLocationID = previousrightLocationID;
					for (z=0; z < levelrightHoldIDs.GetSize(); ++z) {
						if (levelleftHoldIDs.GetSize() > z)
							levelleftHoldIDs[z] = levelrightHoldIDs[z];
						else {
							levelleftHoldIDs.Add(levelrightHoldIDs[z]);
							levelLeftPatternHolds.Add(0);
						}
					}
					for ( j = bayPattern - 1; j >= 0; j-- ) 
					{
						if ( i + (direction*-1*j) < changesTree.treeChildren[sectionIndex].treeChildren[aisleIndex].treeChildren[sideIndex[0]].treeChildren.GetSize() && i + (direction*-1*j) >= 0) 
						{
							if ( previousleftBayID == leftBayStart && firstTimeLeft == 1) {
								numberLeftBay(nextleftBayID, previousleftBayID,bayleftStep,
										tempSection.getLocationMask(),levelleftBreak,previousleftLevelID,
										locationleftBreak,leftLevelStart,leftLocationStart,
										previousleftLocationID,
										nextleftLevelID, nextleftLocationID,leftLevelScheme,leftLocationScheme,
										leftBayScheme, tempAisle.getDescription(), tempSection.getDescription(), levelleftHoldIDs, 1,
										levelleftStep, direction, locationleftStep,
										changesTree.treeChildren[sectionIndex].treeChildren[aisleIndex].treeChildren[sideIndex[0]].treeChildren[i + (direction*-1*j)],
										leftBayPatternList,leftLevelPatternList,leftLocPatternList, levelLeftPatternHolds);
								firstTimeLeft = 0;
							}
							else
								numberLeftBay(nextleftBayID, previousleftBayID,bayleftStep,
										tempSection.getLocationMask(),levelleftBreak,previousleftLevelID,
										locationleftBreak,leftLevelStart,leftLocationStart,
										previousleftLocationID,
										nextleftLevelID, nextleftLocationID,leftLevelScheme,leftLocationScheme,
										leftBayScheme, tempAisle.getDescription(), tempSection.getDescription(), levelleftHoldIDs, 0,
										levelleftStep, direction, locationleftStep,
										changesTree.treeChildren[sectionIndex].treeChildren[aisleIndex].treeChildren[sideIndex[0]].treeChildren[i + (direction*-1*j)],
										leftBayPatternList,leftLevelPatternList,leftLocPatternList, levelLeftPatternHolds);
						}
					}
					previousrightBayID = previousleftBayID;
					previousrightLevelID = previousleftLevelID;
					previousrightLocationID = previousleftLocationID;
					for (z=0; z < levelleftHoldIDs.GetSize(); ++z) {
						if (levelrightHoldIDs.GetSize() > z)
							levelrightHoldIDs[z] = levelleftHoldIDs[z];
						else {
							levelrightHoldIDs.Add(levelleftHoldIDs[z]);
							levelRightPatternHolds.Add(0);
						}
					}
					if ( direction == 1 ) {
						if ( (i+bayPattern) >= maxBayLength && i < maxBayLength-1) {
							incr = maxBayLength - i - 1;
							bayPattern = incr;
						}
					}
					else {
						if ( (i <= bayPattern-1) && i > 0) {
							incr = -1*i;
							bayPattern = -1 * incr;
						}
					}
				}
			}
			break;
		}
		case 5 : {
			//////////////////////////////////////////////////////////
			// Z Path Numbering
			//////////////////////////////////////////////////////////
			////////////////////////////////////////////////////////////////
			// set up indexes based on whether they are traveling up or 
			// down
			////////////////////////////////////////////////////////////////
			if ( bayIndexes[0] == 0 ) {
				start_i = 0;
				stop_i = maxBayLength;
				incr = bayPattern;
				direction = 1;
				if ( sideSel == 0 ) {
					sideIndex[0] = 0;
					sideIndex[1] = 1;
				}
				else {
					sideIndex[0] = 0;
					sideIndex[1] = 1;
				}
			}
			else {
				start_i = maxBayLength - 1;
				stop_i = 0;
				incr = -1*bayPattern;
				direction = -1;
				if ( sideSel == 0 ) {
					sideSel = 1;
					sideIndex[0] = 1;
					sideIndex[1] = 0;
				}
				else {
					sideSel = 0;
					sideIndex[0] = 1;
					sideIndex[1] = 0;
				}
			}
		
			////////////////////////////////////////////////////////////////
			// left side chosen - left then right numbering
			////////////////////////////////////////////////////////////////
			if ( sideSel == 0 ) 
			{
				previousleftBayID = leftBayStart;
				previousrightBayID = rightBayStart;
				firstTimeRight = firstTimeLeft = 1;
				for ( i = start_i; (direction == 1 && i < stop_i) || (direction == -1 && i >= stop_i); i += incr ) 
				{
					for ( j = 0; j < bayPattern; j++) 
					{
						if ( i + (direction*j) < changesTree.treeChildren[sectionIndex].treeChildren[aisleIndex].treeChildren[sideIndex[0]].treeChildren.GetSize() && i + (direction*j) >= 0) 
						{
							if ( previousleftBayID == leftBayStart && firstTimeLeft == 1) {
								numberLeftBay(nextleftBayID, previousleftBayID,bayleftStep,
										tempSection.getLocationMask(),levelleftBreak,previousleftLevelID,
										locationleftBreak,leftLevelStart,leftLocationStart,
										previousleftLocationID,
										nextleftLevelID, nextleftLocationID,leftLevelScheme,leftLocationScheme,
										leftBayScheme, tempAisle.getDescription(), tempSection.getDescription(), levelleftHoldIDs, 1,
										levelleftStep, direction, locationleftStep,
										changesTree.treeChildren[sectionIndex].treeChildren[aisleIndex].treeChildren[sideIndex[0]].treeChildren[i + (direction*j)],
										leftBayPatternList,leftLevelPatternList,leftLocPatternList, levelLeftPatternHolds);
								firstTimeLeft = 0;
							}
							else
								numberLeftBay(nextleftBayID, previousleftBayID,bayleftStep,
										tempSection.getLocationMask(),levelleftBreak,previousleftLevelID,
										locationleftBreak,leftLevelStart,leftLocationStart,
										previousleftLocationID,
										nextleftLevelID, nextleftLocationID,leftLevelScheme,leftLocationScheme,
										leftBayScheme, tempAisle.getDescription(), tempSection.getDescription(), levelleftHoldIDs, 0,
										levelleftStep, direction, locationleftStep,
										changesTree.treeChildren[sectionIndex].treeChildren[aisleIndex].treeChildren[sideIndex[0]].treeChildren[i + (direction*j)],
										leftBayPatternList,leftLevelPatternList,leftLocPatternList, levelLeftPatternHolds);
						}
					}
					previousrightBayID = previousleftBayID;
					previousrightLevelID = previousleftLevelID;
					previousrightLocationID = previousleftLocationID;
					/*
					if (i == start_i) {
						levelrightHoldIDs.Add(previousleftLocationID);
						levelRightPatternHolds.Add(0);
					}
					*/
						//rightLocationStart = previousleftLocationID;
					for (z=0; z < levelleftHoldIDs.GetSize(); ++z) {
						if (levelrightHoldIDs.GetSize() > z)
							levelrightHoldIDs[z] = levelleftHoldIDs[z];
						else {
							levelrightHoldIDs.Add(levelleftHoldIDs[z]);
							levelRightPatternHolds.Add(0);
						}
					}

					for ( j = 0; j < bayPattern; j++ ) 
					{
						if ( i + (direction*j) < changesTree.treeChildren[sectionIndex].treeChildren[aisleIndex].treeChildren[sideIndex[1]].treeChildren.GetSize() && i + (direction*j) >= 0) 
						{
							if ( previousrightBayID == rightBayStart && firstTimeRight == 1) {
								numberRightBay(nextrightBayID, previousrightBayID, bayrightStep,
										tempSection.getLocationMask(), levelrightBreak, previousrightLevelID,
										locationrightBreak, rightLevelStart, rightLocationStart,
										previousrightLocationID,
										nextrightLevelID, nextrightLocationID, rightLevelScheme, rightLocationScheme,
										rightBayScheme, tempAisle.getDescription(), tempSection.getDescription(), levelrightHoldIDs, 1,
										levelrightStep, direction, locationrightStep,
										changesTree.treeChildren[sectionIndex].treeChildren[aisleIndex].treeChildren[sideIndex[1]].treeChildren[i + (direction*j)],
										rightBayPatternList, rightLevelPatternList, rightLocPatternList, levelRightPatternHolds);
								firstTimeRight = 0;
							}
							else
								numberRightBay(nextrightBayID, previousrightBayID, bayrightStep,
										tempSection.getLocationMask(), levelrightBreak, previousrightLevelID,
										locationrightBreak, rightLevelStart, rightLocationStart,
										previousrightLocationID,
										nextrightLevelID, nextrightLocationID, rightLevelScheme, rightLocationScheme,
										rightBayScheme, tempAisle.getDescription(), tempSection.getDescription(), levelrightHoldIDs, 0,
										levelrightStep, direction, locationrightStep,
										changesTree.treeChildren[sectionIndex].treeChildren[aisleIndex].treeChildren[sideIndex[1]].treeChildren[i + (direction*j)],
										rightBayPatternList, rightLevelPatternList, rightLocPatternList, levelRightPatternHolds);
						}
					}
					if ( direction == 1 ) {
						if ( (i+bayPattern) >= maxBayLength && i + j <= maxBayLength-1) {
							incr = maxBayLength - i - 1;
							bayPattern = incr;
						}
					}
					else {
						if ( (i <= bayPattern-1) && i-j > 0) {
							incr = -1*i;
							bayPattern = -1 * incr;
						}
					}

					i += incr;
					for ( j = 0; j < bayPattern; j++ ) 
					{
						if ( i + (direction*j) < changesTree.treeChildren[sectionIndex].treeChildren[aisleIndex].treeChildren[sideIndex[1]].treeChildren.GetSize() && i + (direction*j) >= 0) 
						{
							if ( previousrightBayID == rightBayStart && firstTimeRight == 1) {
								numberRightBay(nextrightBayID, previousrightBayID, bayrightStep,
										tempSection.getLocationMask(), levelrightBreak, previousrightLevelID,
										locationrightBreak, rightLevelStart, rightLocationStart,
										previousrightLocationID,
										nextrightLevelID, nextrightLocationID, rightLevelScheme, rightLocationScheme,
										rightBayScheme, tempAisle.getDescription(), tempSection.getDescription(), levelrightHoldIDs, 1,
										levelrightStep, direction, locationrightStep,
										changesTree.treeChildren[sectionIndex].treeChildren[aisleIndex].treeChildren[sideIndex[1]].treeChildren[i + (direction*j)],
										rightBayPatternList, rightLevelPatternList, rightLocPatternList, levelRightPatternHolds);
								firstTimeRight = 0;
							}
							else
								numberRightBay(nextrightBayID, previousrightBayID, bayrightStep,
										tempSection.getLocationMask(), levelrightBreak, previousrightLevelID,
										locationrightBreak, rightLevelStart, rightLocationStart,
										previousrightLocationID,
										nextrightLevelID, nextrightLocationID, rightLevelScheme, rightLocationScheme,
										rightBayScheme, tempAisle.getDescription(), tempSection.getDescription(), levelrightHoldIDs, 0,
										levelrightStep, direction, locationrightStep,
										changesTree.treeChildren[sectionIndex].treeChildren[aisleIndex].treeChildren[sideIndex[1]].treeChildren[i + (direction*j)],
										rightBayPatternList, rightLevelPatternList, rightLocPatternList, levelRightPatternHolds );
						}
					}
					previousleftBayID = previousrightBayID;
					previousleftLevelID = previousrightLevelID;
					previousleftLocationID = previousrightLocationID;
					for (z=0; z < levelrightHoldIDs.GetSize(); ++z) {
						if (levelleftHoldIDs.GetSize() > z)
							levelleftHoldIDs[z] = levelrightHoldIDs[z];
						else {
							levelleftHoldIDs.Add(levelrightHoldIDs[z]);
							levelLeftPatternHolds.Add(0);
						}
					}
					for ( j = 0; j < bayPattern; j++) 
					{
						if ( i + (direction*j) < changesTree.treeChildren[sectionIndex].treeChildren[aisleIndex].treeChildren[sideIndex[0]].treeChildren.GetSize() && i + (direction*j) >= 0) 
						{
							if ( previousleftBayID == leftBayStart && firstTimeLeft == 1) {
								numberLeftBay(nextleftBayID, previousleftBayID,bayleftStep,
										tempSection.getLocationMask(),levelleftBreak,previousleftLevelID,
										locationleftBreak,leftLevelStart,leftLocationStart,
										previousleftLocationID,
										nextleftLevelID, nextleftLocationID,leftLevelScheme,leftLocationScheme,
										leftBayScheme, tempAisle.getDescription(), tempSection.getDescription(), levelleftHoldIDs, 1,
										levelleftStep, direction, locationleftStep,
										changesTree.treeChildren[sectionIndex].treeChildren[aisleIndex].treeChildren[sideIndex[0]].treeChildren[i + (direction*j)],
										leftBayPatternList,leftLevelPatternList,leftLocPatternList, levelLeftPatternHolds);
								firstTimeLeft = 0;
							}
							else
								numberLeftBay(nextleftBayID, previousleftBayID,bayleftStep,
										tempSection.getLocationMask(),levelleftBreak,previousleftLevelID,
										locationleftBreak,leftLevelStart,leftLocationStart,
										previousleftLocationID,
										nextleftLevelID, nextleftLocationID,leftLevelScheme,leftLocationScheme,
										leftBayScheme, tempAisle.getDescription(), tempSection.getDescription(), levelleftHoldIDs, 0,
										levelleftStep, direction, locationleftStep,
										changesTree.treeChildren[sectionIndex].treeChildren[aisleIndex].treeChildren[sideIndex[0]].treeChildren[i + (direction*j)],
										leftBayPatternList,leftLevelPatternList,leftLocPatternList, levelLeftPatternHolds);
						}
					}
					if ( direction == 1 ) {
						if ( (i+bayPattern) >= maxBayLength && i + j <= maxBayLength-1) {
							incr = maxBayLength - i - 1;
							bayPattern = incr;
						}
					}
					else {
						if ( (i <= bayPattern-1) && i-j > 0) {
							incr = -1*i;
							bayPattern = -1 * incr;
						}
					}
				}
			}
			////////////////////////////////////////////////////////////////
			// right side chosen - right then left numbering
			////////////////////////////////////////////////////////////////
			else
			{
				previousleftBayID = leftBayStart;
				previousrightBayID = rightBayStart;
				firstTimeRight = firstTimeLeft = 1;
				for ( i = start_i; (direction == 1 && i < stop_i) || (direction == -1 && i >= stop_i); i += incr ) 
				{
					for ( j = 0; j < bayPattern; j++) 
					{
						if ( i + (direction*j) < changesTree.treeChildren[sectionIndex].treeChildren[aisleIndex].treeChildren[sideIndex[1]].treeChildren.GetSize() && i + (direction*j) >= 0) 
						{
							if ( previousrightBayID == rightBayStart && firstTimeRight == 1) {
								numberRightBay(nextrightBayID, previousrightBayID, bayrightStep,
										tempSection.getLocationMask(), levelrightBreak, previousrightLevelID,
										locationrightBreak, rightLevelStart, rightLocationStart,
										previousrightLocationID,
										nextrightLevelID, nextrightLocationID, rightLevelScheme, rightLocationScheme,
										rightBayScheme, tempAisle.getDescription(), tempSection.getDescription(), levelrightHoldIDs, 1,
										levelrightStep, direction, locationrightStep,
										changesTree.treeChildren[sectionIndex].treeChildren[aisleIndex].treeChildren[sideIndex[1]].treeChildren[i + (direction*j)],
										rightBayPatternList, rightLevelPatternList, rightLocPatternList, levelRightPatternHolds);
								firstTimeRight = 0;
							}
							else
								numberRightBay(nextrightBayID, previousrightBayID, bayrightStep,
										tempSection.getLocationMask(), levelrightBreak, previousrightLevelID,
										locationrightBreak, rightLevelStart, rightLocationStart,
										previousrightLocationID,
										nextrightLevelID, nextrightLocationID, rightLevelScheme, rightLocationScheme,
										rightBayScheme, tempAisle.getDescription(), tempSection.getDescription(), levelrightHoldIDs, 0,
										levelrightStep, direction, locationrightStep,
										changesTree.treeChildren[sectionIndex].treeChildren[aisleIndex].treeChildren[sideIndex[1]].treeChildren[i + (direction*j)],
										rightBayPatternList, rightLevelPatternList, rightLocPatternList, levelRightPatternHolds);
						}
					}
					previousleftBayID = previousrightBayID;
					previousleftLevelID = previousrightLevelID;
					previousleftLocationID = previousrightLocationID;
					if (i == start_i) {
						levelleftHoldIDs.Add(previousrightLocationID);
						levelLeftPatternHolds.Add(0);
					}
						//leftLocationStart = previousrightLocationID;
					for (z=0; z < levelrightHoldIDs.GetSize(); ++z) {
						if (levelleftHoldIDs.GetSize() > z)
							levelleftHoldIDs[z] = levelrightHoldIDs[z];
						else {
							levelleftHoldIDs.Add(levelrightHoldIDs[z]);
							levelLeftPatternHolds.Add(0);
						}
					}
					for ( j = 0; j < bayPattern; j++ ) 
					{
						if ( i + (direction*j) < changesTree.treeChildren[sectionIndex].treeChildren[aisleIndex].treeChildren[sideIndex[0]].treeChildren.GetSize() && i + (direction*j) >= 0) 
						{
							if ( previousleftBayID == leftBayStart && firstTimeLeft == 1) {
								numberLeftBay(nextleftBayID, previousleftBayID,bayleftStep,
										tempSection.getLocationMask(),levelleftBreak,previousleftLevelID,
										locationleftBreak,leftLevelStart,leftLocationStart,
										previousleftLocationID,
										nextleftLevelID, nextleftLocationID,leftLevelScheme,leftLocationScheme,
										leftBayScheme, tempAisle.getDescription(), tempSection.getDescription(), levelleftHoldIDs, 1,
										levelleftStep, direction, locationleftStep,
										changesTree.treeChildren[sectionIndex].treeChildren[aisleIndex].treeChildren[sideIndex[0]].treeChildren[i + (direction*j)],
										leftBayPatternList,leftLevelPatternList,leftLocPatternList, levelLeftPatternHolds);
								firstTimeLeft = 0;
							}
							else
								numberLeftBay(nextleftBayID, previousleftBayID,bayleftStep,
										tempSection.getLocationMask(),levelleftBreak,previousleftLevelID,
										locationleftBreak,leftLevelStart,leftLocationStart,
										previousleftLocationID,
										nextleftLevelID, nextleftLocationID,leftLevelScheme,leftLocationScheme,
										leftBayScheme, tempAisle.getDescription(), tempSection.getDescription(), levelleftHoldIDs, 0,
										levelleftStep, direction, locationleftStep,
										changesTree.treeChildren[sectionIndex].treeChildren[aisleIndex].treeChildren[sideIndex[0]].treeChildren[i + (direction*j)],
										leftBayPatternList,leftLevelPatternList,leftLocPatternList, levelLeftPatternHolds);
						}
					}
					if ( direction == 1 ) {
						if ( (i+bayPattern) >= maxBayLength && i + j <= maxBayLength-1) {
							incr = maxBayLength - i - 1;
							bayPattern = incr;
						}
					}
					else {
						if ( (i <= bayPattern-1) && i-j > 0) {
							incr = -1*i;
							bayPattern = -1 * incr;
						}
					}

					i += incr;

					for ( j = 0; j < bayPattern; j++ ) 
					{
						if ( i + (direction*j) < changesTree.treeChildren[sectionIndex].treeChildren[aisleIndex].treeChildren[sideIndex[0]].treeChildren.GetSize() && i + (direction*j) >= 0) 
						{
							if ( previousleftBayID == leftBayStart && firstTimeLeft == 1) {
								numberLeftBay(nextleftBayID, previousleftBayID,bayleftStep,
										tempSection.getLocationMask(),levelleftBreak,previousleftLevelID,
										locationleftBreak,leftLevelStart,leftLocationStart,
										previousleftLocationID,
										nextleftLevelID, nextleftLocationID,leftLevelScheme,leftLocationScheme,
										leftBayScheme, tempAisle.getDescription(), tempSection.getDescription(), levelleftHoldIDs, 1,
										levelleftStep, direction, locationleftStep,
										changesTree.treeChildren[sectionIndex].treeChildren[aisleIndex].treeChildren[sideIndex[0]].treeChildren[i + (direction*j)],
										leftBayPatternList,leftLevelPatternList,leftLocPatternList, levelLeftPatternHolds);
								firstTimeLeft = 0;
							}
							else
								numberLeftBay(nextleftBayID, previousleftBayID,bayleftStep,
										tempSection.getLocationMask(),levelleftBreak,previousleftLevelID,
										locationleftBreak,leftLevelStart,leftLocationStart,
										previousleftLocationID,
										nextleftLevelID, nextleftLocationID,leftLevelScheme,leftLocationScheme,
										leftBayScheme, tempAisle.getDescription(), tempSection.getDescription(), levelleftHoldIDs, 0,
										levelleftStep, direction, locationleftStep,
										changesTree.treeChildren[sectionIndex].treeChildren[aisleIndex].treeChildren[sideIndex[0]].treeChildren[i + (direction*j)],
										leftBayPatternList,leftLevelPatternList,leftLocPatternList, levelLeftPatternHolds);
						}
					}
					previousrightBayID = previousleftBayID;
					previousrightLevelID = previousleftLevelID;
					previousrightLocationID = previousleftLocationID;
					for (z=0; z < levelleftHoldIDs.GetSize(); ++z) {
						if (levelrightHoldIDs.GetSize() > z)
							levelrightHoldIDs[z] = levelleftHoldIDs[z];
						else {
							levelrightHoldIDs.Add(levelleftHoldIDs[z]);
							levelRightPatternHolds.Add(0);
						}
					}
					for ( j = 0; j < bayPattern; j++) 
					{
						if ( i + (direction*j) < changesTree.treeChildren[sectionIndex].treeChildren[aisleIndex].treeChildren[sideIndex[1]].treeChildren.GetSize() && i + (direction*j) >= 0) 
						{
							if ( previousrightBayID == rightBayStart && firstTimeRight == 1) {
								numberRightBay(nextrightBayID, previousrightBayID, bayrightStep,
										tempSection.getLocationMask(), levelrightBreak, previousrightLevelID,
										locationrightBreak, rightLevelStart, rightLocationStart,
										previousrightLocationID,
										nextrightLevelID, nextrightLocationID, rightLevelScheme, rightLocationScheme,
										rightBayScheme, tempAisle.getDescription(), tempSection.getDescription(), levelrightHoldIDs, 1,
										levelrightStep, direction, locationrightStep,
										changesTree.treeChildren[sectionIndex].treeChildren[aisleIndex].treeChildren[sideIndex[1]].treeChildren[i + (direction*j)],
										rightBayPatternList, rightLevelPatternList, rightLocPatternList, levelRightPatternHolds);
								firstTimeRight = 0;
							}
							else
								numberRightBay(nextrightBayID, previousrightBayID, bayrightStep,
										tempSection.getLocationMask(), levelrightBreak, previousrightLevelID,
										locationrightBreak, rightLevelStart, rightLocationStart,
										previousrightLocationID,
										nextrightLevelID, nextrightLocationID, rightLevelScheme, rightLocationScheme,
										rightBayScheme, tempAisle.getDescription(), tempSection.getDescription(), levelrightHoldIDs, 0,
										levelrightStep, direction, locationrightStep,
										changesTree.treeChildren[sectionIndex].treeChildren[aisleIndex].treeChildren[sideIndex[1]].treeChildren[i + (direction*j)],
										rightBayPatternList, rightLevelPatternList, rightLocPatternList, levelRightPatternHolds);
						}
					}
					if ( direction == 1 ) {
						if ( (i+bayPattern) >= maxBayLength && i + j <= maxBayLength-1) {
							incr = maxBayLength - i - 1;
							bayPattern = incr;
						}
					}
					else {
						if ( (i <= bayPattern-1) && i-j > 0) {
							incr = -1*i;
							bayPattern = -1 * incr;
						}
					}
				}
			}
			break;
		}
	}
	finalize_datastore();

	return;
}


void CLocationNumberingService::RenumberAisles(CDWordArray &aisleDBIDList, TreeElement & changesTree) 
{

	TreeElement tempRenumberTree;
	int i;
	qqhSLOTAisle newAisle;
	CArray <int, int&> bayIndexes;
	CArray <int, int&> nullSideSelArray;
	CArray <int, int&> nullAisleIndexList;
	int aisleIndex, sectionIndex = 0;
	int maxBayCount = 0, zeroBayCount = 0;
	CBTreeHelper btHelper;

//	storeNextNumber = 1;

//	BuildChangesTree(tempRenumberTree, storeNextNumber);


//	SaveFacility(tempRenumberTree, 0);

	for ( i = 0; i < aisleDBIDList.GetSize(); i++ ) {

		btHelper.UpdateBTWithAisleForPickPathRenumbering(aisleDBIDList[i], changesTree, &aisleIndex, &sectionIndex);
		btHelper.GetBtAisle(changesTree.treeChildren[sectionIndex].treeChildren[aisleIndex].fileOffset,newAisle);

		// Finger aisles and straight-throughs are numbered the same
		if ( newAisle.getPickPathType() == 1 )
			newAisle.setPickPathType(2);

		maxBayCount = changesTree.treeChildren[sectionIndex].treeChildren[aisleIndex].treeChildren[0].treeChildren.GetSize();

		if ( maxBayCount < changesTree.treeChildren[sectionIndex].treeChildren[aisleIndex].treeChildren[1].treeChildren.GetSize() )
			maxBayCount = changesTree.treeChildren[sectionIndex].treeChildren[aisleIndex].treeChildren[1].treeChildren.GetSize();
		
		
		bayIndexes.RemoveAll();
		if ( newAisle.getPickPathDirection() == 0 )
			bayIndexes.Add(zeroBayCount);
		else
			bayIndexes.Add(maxBayCount);


		this->NumberBays(bayIndexes, newAisle.getPickPathType(), aisleIndex, nullAisleIndexList,
			newAisle.getPickPathStartSide(), newAisle.getLeftBayStart(), newAisle.getRightBayStart(),
			newAisle.getLeftLevelStart(), newAisle.getRightLevelStart(), newAisle.getLeftLocationStart(),
			newAisle.getRightLocationStart(), newAisle.getLeftBayScheme(), newAisle.getRightBayScheme(),
			newAisle.getLeftLevelScheme(), newAisle.getRightLevelScheme(), newAisle.getLeftLocationScheme(),
			newAisle.getRightLocationScheme(), newAisle.getLeftBayStep(), newAisle.getRightBayStep(),
 			newAisle.getLeftLevelStep(), newAisle.getRightLevelStep(), newAisle.getLeftLocationStep(),
			newAisle.getRightLocationStep(), newAisle.getLeftLevelBreak(), newAisle.getRightLevelBreak(),
			newAisle.getLeftLocationBreak(), newAisle.getRightLocationBreak(), newAisle.getBaysInPattern(),
			maxBayCount,changesTree, sectionIndex, nullSideSelArray,
			newAisle.getLeftBayPattern(), newAisle.getLeftLevelPattern(), newAisle.getLeftLocPattern(),
			newAisle.getRightBayPattern(), newAisle.getRightLevelPattern(), newAisle.getRightLocPattern());

		
		btHelper.SaveFacility(changesTree,0);

	}


	return;
}


//////////////////////////////////////////////////////////////////////
// Function Name : FormatLocationAndUpdate
// Classname : None
// Description : Update the location name
// Date Created : 10/01/98
// Author : wbh
//////////////////////////////////////////////////////////////////////
// Inputs : facility Tree, starting Number
// Outputs : updated facility Tree, success code
// Explanation : 
//   Update the location name based on the location mask
//////////////////////////////////////////////////////////////////////
// Algorithm : None
//////////////////////////////////////////////////////////////////////
// Revisions : 
//   Date-initials : Description_of_revision
//////////////////////////////////////////////////////////////////////
	/*******************************************************
	*
	*                       NOTICE
	*
	*  THIS SOFTWARE IS THE PROPERTY OF AND CONTAINS
	*  CONFIDENTIAL INFORMATION OF SSA GLOBAL 
	*  TECHNOLOGIES, INC., AND SHALL NOT BE COPIED,
	*  USED, NOR DISCLOSED WITHOUT EXPRESS WRITTEN
	*  AUTHORIZATION.  ALTHOUGH PUBLICATION IS NOT
	*  INTENDED, IN THE EVENT OF PUBLICATION, THE
	*  FOLLOWING NOTICE IS APPLICABLE:
	*
	*  (c) COPYRIGHT 2004 SSA GLOBAL TECHNOLOGIES, INC.
	*
	*           SSA GLOBAL TECHNOLOGIES, INC.
	*
	********************************************************/
//////////////////////////////////////////////////////////////////////
void CLocationNumberingService::FormatLocationAndUpdate(CString locationPart,int fileOffset, CString locationMask,
						CString sectionDesc, CString aisleDesc, CString bayDesc, CString levelDesc )
{
	int k = 0;
	int i;
	BOOL aisleNumeric, sectionNumeric, bayNumeric, levelNumeric, locationNumeric;
	char * locationDesc;
	int secIndex, bayIndex, aisleIndex, levelIndex, locationIndex;
	char * sectionMask;
	char alphabetCap[28]=" ABCDEFGHIJKLMNOPQRSTUVWXYZ";
	char alphabetNoCap[28]=" abcdefghijklmnopqrstuvwxyz";
	CBTreeHelper btHelper;

	//AfxMessageBox("In format location");
	sectionNumeric = aisleNumeric = bayNumeric = levelNumeric = locationNumeric = TRUE;
	for (i=1; i < 27 && sectionNumeric; i++) {
		if (sectionDesc.Find(alphabetCap[i]) != -1 || sectionDesc.Find(alphabetNoCap[i]) != -1)
			sectionNumeric = FALSE;
	}
	for (i=1; i < 27 && aisleNumeric; i++) {
		if (aisleDesc.Find(alphabetCap[i]) != -1 || aisleDesc.Find(alphabetNoCap[i]) != -1)
			aisleNumeric = FALSE;
	}
	for (i=1; i < 27 && bayNumeric; i++) {
		if (bayDesc.Find(alphabetCap[i]) != -1 || bayDesc.Find(alphabetNoCap[i]) != -1)
			bayNumeric = FALSE;
	}
	for (i=1; i < 27 && levelNumeric; i++) {
		if (levelDesc.Find(alphabetCap[i]) != -1 || levelDesc.Find(alphabetNoCap[i]) != -1)
			levelNumeric = FALSE;
	}
	for (i=1; i < 27 && locationNumeric; i++) {
		if (locationPart.Find(alphabetCap[i]) != -1 || locationPart.Find(alphabetNoCap[i]) != -1)
			locationNumeric = FALSE;
	}

	secIndex = bayIndex = aisleIndex = 0;
	
	sectionMask = new char[locationMask.GetLength()+1];
	locationDesc = new char[locationMask.GetLength()+1];

	CString locationString;

	strcpy(sectionMask,locationMask.GetBuffer(0));
	locationMask.ReleaseBuffer();

	memset(locationDesc,0,locationMask.GetLength()+1);
	secIndex = 0;
	aisleIndex = 0;
	bayIndex = 0;
	levelIndex = 0;
	locationIndex = 0;

	for ( k = 0; sectionMask[k] != '\0'; k++ ) {
		switch (sectionMask[k]) {
			case '\\' : case '/': {
				locationDesc[k] = sectionMask[k];
				if (k < (int)(strlen(sectionMask)-1)) {
					locationDesc[k+1] = sectionMask[k+1];
					k++;
				}
				break;
			}
			case 's' : case 'S' :  { 
				if ( secIndex < sectionDesc.GetLength())
					locationDesc[k] = sectionDesc.GetAt(secIndex);
				else {
					if (sectionNumeric)
						locationDesc[k] = '0';
					else
						locationDesc[k] = ' ';
				}
				secIndex++;
				break;
			}
			case 'a' : case 'A' : {
				if ( aisleIndex < aisleDesc.GetLength())
					locationDesc[k] = aisleDesc.GetAt(aisleIndex);
				else {
					if (aisleNumeric)
						locationDesc[k] = '0';
					else
						locationDesc[k] = ' ';
				}
				aisleIndex++;
				break;
			}
			case 'b' : case 'B' : {
				if ( bayIndex < bayDesc.GetLength())
					locationDesc[k] = bayDesc.GetAt(bayIndex);
				else {
					if (bayNumeric)
						locationDesc[k] = '0';
					else
						locationDesc[k] = ' ';
				}
				bayIndex++;
				break;
			}
			case 'l' : case 'L' : {
				if ( levelIndex < levelDesc.GetLength())
					locationDesc[k] = levelDesc.GetAt(levelIndex);
				else {
					if (levelNumeric)
						locationDesc[k] = '0';
					else
						locationDesc[k] = ' ';
				}
				levelIndex++;
				break;
			}
			case 'p' : case 'P' : {
				if ( locationIndex < locationPart.GetLength())
					locationDesc[k] = locationPart.GetAt(locationIndex);
				else {
					if (locationNumeric)
						locationDesc[k] = '0';
					else
						locationDesc[k] = ' ';
				}
				locationIndex++;
				break;
			}
			default : {
				locationDesc[k] = sectionMask[k];
				break;
			}
		}
	}
	locationString = locationDesc;
	// slash and backslash are used to escape the characters that are normally part of the mask
	// I can't remember what @ is used for - something to do with patterns - maybe to skip a number or something
	locationString.Remove('@');
	locationString.Remove('\\');
	locationString.Remove('/');

	btHelper.UpdateLocationDescription(fileOffset,locationString);
	delete locationDesc;
	delete sectionMask;
	return;
}

//////////////////////////////////////////////////////////////////////
// Function Name : UpdateLocationDescriptionPart
// Classname : None
// Description : Update the location name once a parent element changes
// Date Created : 10/01/98
// Author : wbh
//////////////////////////////////////////////////////////////////////
// Inputs : facility Tree, starting Number
// Outputs : updated facility Tree, success code
// Explanation : 
//   This is called when a bay, aisle, etc. has its description changed.
//////////////////////////////////////////////////////////////////////
// Algorithm : None
//////////////////////////////////////////////////////////////////////
// Revisions : 
//   Date-initials : Description_of_revision
//////////////////////////////////////////////////////////////////////
	/*******************************************************
	*
	*                       NOTICE
	*
	*  THIS SOFTWARE IS THE PROPERTY OF AND CONTAINS
	*  CONFIDENTIAL INFORMATION OF SSA GLOBAL 
	*  TECHNOLOGIES, INC., AND SHALL NOT BE COPIED,
	*  USED, NOR DISCLOSED WITHOUT EXPRESS WRITTEN
	*  AUTHORIZATION.  ALTHOUGH PUBLICATION IS NOT
	*  INTENDED, IN THE EVENT OF PUBLICATION, THE
	*  FOLLOWING NOTICE IS APPLICABLE:
	*
	*  (c) COPYRIGHT 2004 SSA GLOBAL TECHNOLOGIES, INC.
	*
	*           SSA GLOBAL TECHNOLOGIES, INC.
	*
	********************************************************/
//////////////////////////////////////////////////////////////////////
void CLocationNumberingService::UpdateLocationDescriptionPart(int locationOffset,
							  CString locationMask,
							  int nameType,
							  CString partName ) 
{
	char * sectionMask;
	BOOL partNumeric;
	char alphabetCap[28]=" ABCDEFGHIJKLMNOPQRSTUVWXYZ";
	char alphabetNoCap[28]=" abcdefghijklmnopqrstuvwxyz";
	int i;
	int secIndex, aisleIndex, bayIndex, levelIndex = 0;
	char * locationDesc;
	qqhSLOTLocation tempLocation;
	int k;
	int tempLength,tempReturn;
	char * buf;
	int aMultiplier = 1;
	CSsaStringArray tempArray;
	CString tempString;
	CUtilityHelper utilityHelper;
	CBTreeHelper btHelper;
	
	partNumeric = TRUE;
	for (i=1; i < 27 && partNumeric; i++) {
		if (partName.Find(alphabetCap[i]) != -1 || partName.Find(alphabetNoCap[i]) != -1)
			partNumeric = FALSE;
	}
	sectionMask = new char[locationMask.GetLength()+1];
	locationDesc = new char[locationMask.GetLength()+1];
	memset(locationDesc,0,locationMask.GetLength()+1);
	memset(sectionMask,0,locationMask.GetLength()+1);
	strcpy(sectionMask,locationMask.GetBuffer(0));
	locationMask.ReleaseBuffer();
	
	tempLength = 1024 * aMultiplier;
	buf = (char *) (calloc(tempLength,sizeof(char)));

	tempReturn = -2;
	/////////////////////////////////////////////////////////////
	//  If we do not prepare enough buffer for the read,
	//  the function will return a -2 code.  We then increase
	//  the size of our buffer.  We assume that we will receive a
	//  -2 at first.
	/////////////////////////////////////////////////////////////
	while ( tempReturn == -2 ) {
		tempLength = aMultiplier * 1024;
		tempReturn = retObjStream(locationOffset,buf,&tempLength);
		if ( tempReturn == -2 ) {
			free(buf);
			aMultiplier++;
			tempLength = 1024 * aMultiplier;
			buf = (char *)(calloc(tempLength,sizeof(char)));
		}
	}
	/////////////////////////////////////////////////////////////
	//  Successful read.  We need to process the object
	/////////////////////////////////////////////////////////////
	if ( tempReturn == 0 ) {
		tempArray.RemoveAll();
		/////////////////////////////////////////////////////////////
		//  Parse out the buffer read into an array of strings
		/////////////////////////////////////////////////////////////
		tempString = buf;
		utilityHelper.BuildArrayofStrings(tempArray,tempString);
		if ( tempArray[1].Find(LocationBegSearch) != -1 ) {
			tempLocation.BuildFromStream(tempArray);
		}
	}
	else
		return;
	
	// Don't bother to update the description if they haven't put
	// in a pick path yet
	if (tempLocation.getDescription() == "New Location")
		return;

	secIndex = aisleIndex = bayIndex = levelIndex = 0;


	strcpy(locationDesc, tempLocation.getDescription().GetBuffer(0));
	tempLocation.getDescription().ReleaseBuffer();

	for ( k = 0; sectionMask[k] != '\0'; k++ ) {
		switch (sectionMask[k]) {
			case '\\' : case '/' : {
				locationDesc[k] = sectionMask[k];
				if (k < (int)(strlen(sectionMask)-1)) {
					k++;
					locationDesc[k] = sectionMask[k];
				}
				break;
			}
			case 's' : case 'S' :  { 
				if ( nameType == 1 ) {
					if ( secIndex < partName.GetLength())
						locationDesc[k] = partName.GetAt(secIndex);
					else {
						if (partNumeric)
							locationDesc[k] = '0';
						else
							locationDesc[k] = ' ';
					}
					secIndex++;
				}
				break;
			}
			case 'a' : case 'A' : {
				if ( nameType == 2 ) {
					if ( aisleIndex < partName.GetLength())
						locationDesc[k] = partName.GetAt(aisleIndex);
					else {
						if (partNumeric)
							locationDesc[k] = '0';
						else
							locationDesc[k] = ' ';
					}
					aisleIndex++;
				}
				break;
			}
			case 'b' : case 'B' : {
				if ( nameType == 3 ) {
					if ( bayIndex < partName.GetLength())
						locationDesc[k] = partName.GetAt(bayIndex);
					else {
						if (partNumeric)
							locationDesc[k] = '0';
						else
							locationDesc[k] = ' ';
					}
					bayIndex++;
				}
				break;
			}
			case 'l' : case 'L' : {
				if ( nameType == 4 ) {
					if ( levelIndex < partName.GetLength())
						locationDesc[k] = partName.GetAt(levelIndex);
					else {
						if (partNumeric)
							locationDesc[k] = '0';
						else
							locationDesc[k] = ' ';
					}
					levelIndex++;
				}
				break;
			}
			default : {
				locationDesc[k] = locationDesc[k];
				break;
			}
		}
	}

	CString tempDesc = locationDesc;
	tempDesc.Remove('\\');
	tempDesc.Remove('/');
		
	if (tempLocation.getDescription() != tempDesc) {
		tempLocation.setDescription(tempDesc);
		btHelper.UpdateLocationDescription(locationOffset,tempLocation.getDescription());
	}
	free(buf);
	delete locationDesc;
	delete sectionMask;
	return;
}


//////////////////////////////////////////////////////////////////////
// Function Name : FindNextID
// Classname : None
// Description : Number an element based on start, step, and scheme
// Date Created : 10/01/98
// Author : wbh
//////////////////////////////////////////////////////////////////////
// Inputs : previousID, step, scheme, first time, 
// Outputs : updated facility Tree, success code
// Explanation : 
//   Used when numbering locations, bays, etc.  This function allows
//   for alphanumeric and numeric numbering of facility elements.
//////////////////////////////////////////////////////////////////////
// Algorithm : None
//////////////////////////////////////////////////////////////////////
// Revisions : 
//   Date-initials : Description_of_revision
//////////////////////////////////////////////////////////////////////
	/*******************************************************
	*
	*                       NOTICE
	*
	*  THIS SOFTWARE IS THE PROPERTY OF AND CONTAINS
	*  CONFIDENTIAL INFORMATION OF SSA GLOBAL 
	*  TECHNOLOGIES, INC., AND SHALL NOT BE COPIED,
	*  USED, NOR DISCLOSED WITHOUT EXPRESS WRITTEN
	*  AUTHORIZATION.  ALTHOUGH PUBLICATION IS NOT
	*  INTENDED, IN THE EVENT OF PUBLICATION, THE
	*  FOLLOWING NOTICE IS APPLICABLE:
	*
	*  (c) COPYRIGHT 2004 SSA GLOBAL TECHNOLOGIES, INC.
	*
	*           SSA GLOBAL TECHNOLOGIES, INC.
	*
	********************************************************/
//////////////////////////////////////////////////////////////////////
CString CLocationNumberingService::FindNextID(CString &previousID, int stepValue,
				   int schemeValue, int firstFlag,
				   CString &locationMask, int facilityMember) 
{
	CStringArray dummy;
	dummy.RemoveAll();
	return FindNextID(previousID, stepValue, schemeValue,
		firstFlag, locationMask, facilityMember,
		dummy, 0);
}

CString CLocationNumberingService::FindNextID(CString &previousID, int stepValue,
				   int schemeValue, int firstFlag,
				   CString &locationMask, int facilityMember, 
				   CStringArray &patternList, int patternPos) 
{

	char alphabetCap[28]=" ABCDEFGHIJKLMNOPQRSTUVWXYZ";
	char alphabetNoCap[28]=" abcdefghijklmnopqrstuvwxyz";
	char numberArray[12] = " 0123456789";
	int isAlphaID = 0;
	int i,j;
	int numChars=0;
	int previousNum=0;
	int nextNumOdd=0;
	int nextNumEven=0;
	int nextNum=0;
//	int loc=0;
	CString returnID;
	int carry=0;
	int tempNum=0;
	CArray <int, int&> alphaIndexes;
	CArray <int, int&> numberIndexes;
	CArray <int, int&> alphaNumSwitchList;
	char *ptr1, *ptr2;
	int zero = 0;
	int minusOne = -1;
	int one = 1;
	int twentySix = 26;
	int ten = 10;
	int tempNum2;
	int subtractor;
	int moretoborrow,doneborrow=0;
	///////////////////////////////////////////////////////////
	//facilityMember : 0(bay), 1(level), 2(location), 4(aisle) 3(section)
	//firstFlag : 0(No), 1(Yes)
	//shemeValue : 0(None), 1(Even), 2(Odd)
	///////////////////////////////////////////////////////////

	//AfxMessageBox("In NextID");

	if (previousID == "")
		previousID = "0";

	for (i=1; i < 27 && isAlphaID == 0; i++) {
		if (previousID.Find(alphabetCap[i]) != -1)
			isAlphaID = 1;
	}
	for (i=1; i < 27; i++) {
		int newLoc;
		newLoc = previousID.Find(alphabetNoCap[i]);
		if (newLoc != -1) {
			for ( j = 0; j < previousID.GetLength(); j++) {
				if ( previousID.GetAt(j) == alphabetNoCap[i] )
					previousID.SetAt(j,alphabetCap[i]);
			}
			isAlphaID = 1;
		}
	}

	//AfxMessageBox("In NextID 2");
	//AfxMessageBox(previousID);
	if (isAlphaID != 1) {
		previousNum = atoi(previousID.GetBuffer(0));
		previousID.ReleaseBuffer();
		if ( firstFlag != 1 )
			nextNum = previousNum + stepValue;
		else
			nextNum = previousNum;
		if (( nextNum % 2 ) == 0 ) {

			if (stepValue >= 0) {
				nextNumEven = nextNum;
				nextNumOdd = nextNum + 1;
			}
			else {
				nextNumEven = nextNum;
				nextNumOdd = nextNum - 1;
			}
		}
		else {
			if (stepValue >= 0) {
				nextNumOdd = nextNum;
				nextNumEven = nextNum + 1;
			}
			else {
				nextNumOdd = nextNum;
				nextNumEven = nextNum -1;
			}
		}
		if ( schemeValue == 1 )
			nextNum = nextNumEven;
		else if (schemeValue == 2)
			nextNum = nextNumOdd;
		returnID.Format("%d",nextNum);
	}
	else {
		if ( firstFlag != 1 ) {
			for (i=previousID.GetLength()-1; i >= 0; i--) {
				ptr1 = alphabetCap;
				ptr2 = strchr(alphabetCap,previousID.GetAt(i));
				if (ptr2 != NULL) {
					tempNum = ptr2-ptr1;
					alphaIndexes.Add(tempNum);
					numberIndexes.Add(minusOne);
					alphaNumSwitchList.Add(zero);
				}
				else {
					ptr1 = numberArray;
					ptr2 = strchr(numberArray,previousID.GetAt(i));
					if (ptr2 != NULL ) {
						tempNum = ptr2-ptr1;
						numberIndexes.Add(tempNum);
						alphaIndexes.Add(minusOne);
						alphaNumSwitchList.Add(one);
					}
					else {
						char msg[1024] = "Invalid Character";
						throw Ssa_Exception(msg, __FILE__, __LINE__, 200);
					}
				}
			}
			i = 0;
			carry = stepValue;
			if ( carry >= 0 ) {
				while ( carry > 0 ) {
					if ( i < alphaNumSwitchList.GetSize() ){
						if ( alphaNumSwitchList[i] == 0 ) {
							tempNum = alphaIndexes[i] + carry;
							if ( (tempNum % 26) == 0 )
								alphaIndexes[i] = 26;
							else
								alphaIndexes[i] = tempNum % 26;
							if ( tempNum > 26 )
								carry = tempNum / 26;
							else
								carry = 0;
						}
						else {
							tempNum = numberIndexes[i] + carry;
							if ( (tempNum % 10) == 0 )
								numberIndexes[i] = 10;
							else
								numberIndexes[i] = tempNum % 10;
							if ( tempNum > 10 )
								carry = tempNum / 10;
							else
								carry = 0;
						}
					}
					else {
						if ( i != 0 ) {
							if ( alphaNumSwitchList[i-1] == 0 ) {
								tempNum = carry;
								if ( (tempNum % 26) == 0 )
									alphaIndexes.Add(twentySix);
								else {
									tempNum2 = tempNum % 26;
									alphaIndexes.Add(tempNum2);
								}
								if ( tempNum > 26 )
									carry = tempNum / 26;
								else
									carry = 0;
								alphaNumSwitchList.Add(zero);
								numberIndexes.Add(minusOne);
							}
							else {
								tempNum = carry;
								if ( (tempNum % 10) == 0 )
									numberIndexes.Add(ten);
								else {
									tempNum2 = tempNum % 10 ;
									numberIndexes.Add(tempNum2);
								}
								alphaNumSwitchList.Add(one);
								alphaIndexes.Add(minusOne);
								if ( tempNum > 10 )
									carry = tempNum / 10;
								else
									carry = 0;
							}
						}
						else {
							char msg[1024] = "Invalid Data";
							throw Ssa_Exception(msg, __FILE__, __LINE__, 200);
						}
					}
					i++;
				}
			}
			else {
				subtractor = carry;
				moretoborrow = 1;
				while ( subtractor < 0 && moretoborrow == 1) {
					doneborrow = 0;
					for ( i = 0; i < alphaNumSwitchList.GetSize() && doneborrow == 0 && moretoborrow == 1; i++ ) {
						subtractor++;
						if ( alphaNumSwitchList[i] == 0 ) {
							if ( alphaIndexes[i] == 0 )
								moretoborrow = 0;
							else {
								alphaIndexes[i]--;
								if ( alphaIndexes[i] == 0 && subtractor <= 0)
									alphaIndexes[i] = 26;
								else
									doneborrow = 1;
							}
						}
						else {
							if ( numberIndexes[i] == 0 )
								moretoborrow = 0;
							else {
								numberIndexes[i]--;
								if ( numberIndexes[i] == 0 && subtractor <= 0)
									numberIndexes[i] = 10;
								else
									doneborrow = 1;
							}
						}
					}
				}
				if ( moretoborrow == 0 ) {
					alphaNumSwitchList.RemoveAll();
					alphaIndexes.RemoveAll();
					numberIndexes.RemoveAll();
				}
			}
			returnID = "";
			for ( i=0; i < alphaNumSwitchList.GetSize(); i++ ) {
				if ( alphaNumSwitchList[i] == 0 )
					returnID = alphabetCap[alphaIndexes[i]] + returnID;
				else
					returnID = numberArray[numberIndexes[i]] + returnID;
			}
		}
		else
			returnID = previousID;
	}

	if (patternList.GetSize() > 0) {
		returnID = patternList.GetAt(patternPos);
	}
	
	CString locMask = "";

	for (i=0; i < locationMask.GetLength(); ++i) {
		if (locationMask.GetAt(i) == '\\' ||
			locationMask.GetAt(i) == '/') {
			i++;
		}
		else
			locMask += locationMask.GetAt(i);
	}

	if (facilityMember == 0) {
		for ( i = 0; i < locMask.GetLength(); i++ ) {			
			if (locMask.GetAt(i) == 'B' ||
				locMask.GetAt(i) == 'b' )
				numChars++;
		}
	}
	if (facilityMember == 3) {
		for ( i = 0; i < locMask.GetLength(); i++ ) {
			if (locMask.GetAt(i) == 'S' ||
				locMask.GetAt(i) == 's' )
				numChars++;
		}
	}
	else if (facilityMember == 1) {
		for ( i = 0; i < locMask.GetLength(); i++ ) {
			if (locMask.GetAt(i) == 'L' ||
				locMask.GetAt(i) == 'l' )
				numChars++;
		}
	}
	else if (facilityMember == 2) {
		for ( i = 0; i < locMask.GetLength(); i++ ) {
			if (locMask.GetAt(i) == 'P' ||
				locMask.GetAt(i) == 'p' )
				numChars++;
		}
	}
	else if (facilityMember == 4) {
		for ( i = 0; i < locMask.GetLength(); i++ ) {
			if (locMask.GetAt(i) == 'A' ||
				locMask.GetAt(i) == 'a' )
				numChars++;
		}
	}

	if (patternList.GetSize() > 0) {
		if (returnID[0] == '@') {
			while (returnID.GetLength() < numChars )
				returnID = '@' + returnID;
		}
		else {
			isAlphaID = 0;
			for (i=1; i < 27 && isAlphaID == 0; i++) {
				if ((returnID.Find(alphabetCap[i]) != -1)
					|| (returnID.Find(alphabetNoCap[i]) != -1))
					isAlphaID = 1;
			}
			if (isAlphaID) {
				while (returnID.GetLength() < numChars)
					returnID = ' ' + returnID;
			}
			else {
				while (returnID.GetLength() < numChars)
					returnID = '0' + returnID;
			}
		}
		
		
	}
	else if ( isAlphaID != 1 ) {
		while (returnID.GetLength() < numChars )
			returnID = '0' + returnID;
	}
	else {
		int capIndex = 0;
		int startMask = -1;

		CString tempStr;
		tempStr.Format("Fac mem : %d\n",facilityMember);
		//AfxMessageBox(tempStr);
		for (i = 0; startMask == -1 && i < locMask.GetLength(); i++) {
			if (facilityMember == 2 && (locMask[i] == 'P')) 
				startMask = i;
			if (facilityMember == 2 && (locMask[i] == 'p'))
				startMask = i;
			if (facilityMember == 0 && (locMask[i] == 'B'))
				startMask = i;
			if (facilityMember == 0 && (locMask[i] == 'b' ))
				startMask = i;
			if (facilityMember == 1 && (locMask[i] == 'L'))
				startMask = i;
			if (facilityMember == 1 && (locMask[i] == 'l'))
				startMask = i;
			if (facilityMember == 3 && (locMask[i] == 'S'))
				startMask = i;
			if (facilityMember == 3 && (locMask[i] == 's' ))
				startMask = i;
			if (facilityMember == 4 && (locMask[i] == 'A'))
				startMask = i;
			if (facilityMember == 4 && (locMask[i] == 'a' ))
				startMask = i;
		}
		tempStr.Format("startMask : %d, %c\n",startMask,locMask[startMask]);
		//AfxMessageBox(tempStr);
		//AfxMessageBox(locMask);

		while (returnID.GetLength() < numChars )
			returnID = ' ' + returnID;
		//AfxMessageBox(returnID);
		for (int z = 0; z < returnID.GetLength(); z++) {
			isAlphaID = 0;
			for (i=1; i < 27 && isAlphaID == 0; i++) {
				if (returnID.GetAt(z) == alphabetCap[i]) {
					isAlphaID = 1;
					capIndex = i;
				}
			}
			for (i=1; i < 27 && isAlphaID == 0; i++) {
				if ( returnID.GetAt(z) == alphabetNoCap[i] ) {
					isAlphaID = 1;
					capIndex = i;
				}
			}
			if ( isAlphaID == 1 && startMask != -1) {
				tempStr.Format("IsAlpha, capIndex : %d, z : %d\n",capIndex, z);
				//AfxMessageBox(tempStr);
				if (facilityMember == 0) {
					if (locMask.GetAt(startMask+z) == 'B')
						returnID.SetAt(z,alphabetCap[capIndex]);
					else
						returnID.SetAt(z,alphabetNoCap[capIndex]);
					//AfxMessageBox(returnID);
				}
				else if (facilityMember == 1) {
					if (locMask.GetAt(startMask+z) == 'L')
						returnID.SetAt(z,alphabetCap[capIndex]);
					else
						returnID.SetAt(z,alphabetNoCap[capIndex]);
				}
				else if (facilityMember == 2) {
					if (locMask.GetAt(startMask+z) == 'P')
						returnID.SetAt(z,alphabetCap[capIndex]);
					else
						returnID.SetAt(z,alphabetNoCap[capIndex]);
				}
				else if (facilityMember == 3) {
					if (locMask.GetAt(startMask+z) == 'S')
						returnID.SetAt(z,alphabetCap[capIndex]);
					else
						returnID.SetAt(z,alphabetNoCap[capIndex]);
				}
				else if (facilityMember == 4) {
					if (locMask.GetAt(startMask+z) == 'A')
						returnID.SetAt(z,alphabetCap[capIndex]);
					else
						returnID.SetAt(z,alphabetNoCap[capIndex]);
				}
			}
		}
	}
	//AfxMessageBox("Returning ID");
	return returnID;
}


void CLocationNumberingService::numberRightBay(CString & nextrightBayID, CString & previousrightBayID, int bayrightStep,
		CString & locationMask, int levelrightBreak, CString & previousrightLevelID,
		int locationrightBreak, CString & rightLevelStart, CString & rightLocationStart,
		CString & previousrightLocationID,
		CString & nextrightLevelID, CString & nextrightLocationID, int rightLevelScheme, int rightLocationScheme,
		int rightBayScheme, CString & aisleDesc, CString & sectionDesc, CSsaStringArray & levelrightHoldIDs, int startInd,
		int levelrightStep, int direction, int locationrightStep,
		TreeElement & bayTree,
		CStringArray &rightBayPatternList, CStringArray &rightLevelPatternList, CStringArray &rightLocPatternList,
		CWordArray &levelRightPatternHolds) {

	int j,k;
	CBTreeHelper btHelper;

	nextrightBayID = FindNextID(previousrightBayID, bayrightStep,
		rightBayScheme, startInd, locationMask, 0, 
		rightBayPatternList, rightBayPatternPos);
	
	rightBayPatternPos ++;
	if (rightBayPatternPos == rightBayPatternList.GetSize())
		rightBayPatternPos = 0;

	// if this is the first bay
	if ( startInd == 1 ) {
		previousrightLocationID = rightLocationStart;
		previousrightLevelID = rightLevelStart;
		rightLevelPatternPos = 0;
		rightLocPatternPos = 0;
	}

	btHelper.UpdateBayDescription(nextrightBayID,bayTree.fileOffset);

	// if level breaks on bay
	if ( levelrightBreak == 1 ) {
		previousrightLevelID = rightLevelStart;
		rightLevelPatternPos = 0;
	}

	// if location breaks on bay
	if ( locationrightBreak == 1 ) {
		previousrightLocationID = rightLocationStart;
		rightLocPatternPos = 0;
	}

	// loop through levels
	for ( j = 0; j < bayTree.treeChildren.GetSize(); j++) {

		// if level breaks on bay and we're on the first level of the bay
		// or level breaks on nothing and we're on the very first level
		if ( (levelrightBreak == 1 && j == 0) ||
			 (levelrightBreak == 0 && previousrightLevelID == rightLevelStart) )
				// get next level without adding the step value
			nextrightLevelID = FindNextID(previousrightLevelID, levelrightStep,
				rightLevelScheme, 1, locationMask, 1, 
				rightLevelPatternList, rightLevelPatternPos);
		else	// get next level adding the step value
			nextrightLevelID = FindNextID(previousrightLevelID, levelrightStep,
				rightLevelScheme, 0, locationMask, 1,
				rightLevelPatternList, rightLevelPatternPos);

		rightLevelPatternPos++;
		if (rightLevelPatternPos == rightLevelPatternList.GetSize())
			rightLevelPatternPos = 0;

		btHelper.UpdateLevelDescription(nextrightLevelID,bayTree.treeChildren[j].fileOffset);
		
		// if location breaks on level only
		if (locationrightBreak == 2 ) {
			if ( j > levelrightHoldIDs.GetSize() - 1 ) {
				levelrightHoldIDs.Add(rightLocationStart);
				levelRightPatternHolds.Add(0);
			}
			previousrightLocationID = levelrightHoldIDs[j];
			rightLocPatternPos = levelRightPatternHolds[j];
		}

		// if location breaks on both bay and level
		else if ( locationrightBreak == 3 ) {
			previousrightLocationID = rightLocationStart;
			rightLocPatternPos = 0;
		}

		if ( direction == 1) {
			for ( k = 0; k < bayTree.treeChildren[j].treeChildren.GetSize(); k++ ) {

				// if location break is none and first bay, level, location in aisle
				// or location break is bay and first level and location in bay
				// or location break is level and first level in aisle
				// or location break is both and first location in level
				if ( (locationrightBreak == 0 && startInd == 1 && j == 0 && k == 0) ||
					 (locationrightBreak == 1 && j == 0 && k == 0) ||
					 (locationrightBreak == 2 && k == 0 && startInd == 1) ||
					 (locationrightBreak == 3 && k == 0) )
					nextrightLocationID = FindNextID(previousrightLocationID, locationrightStep,
						rightLocationScheme, 1, locationMask, 2,
						rightLocPatternList, rightLocPatternPos);
				else
					nextrightLocationID = FindNextID(previousrightLocationID, locationrightStep,
						rightLocationScheme, 0, locationMask, 2,
						rightLocPatternList, rightLocPatternPos);

				FormatLocationAndUpdate(nextrightLocationID,bayTree.treeChildren[j].treeChildren[k].fileOffset,
					locationMask, sectionDesc, aisleDesc,
					nextrightBayID, nextrightLevelID);
				previousrightLocationID = nextrightLocationID;
				
				rightLocPatternPos++;
				if (rightLocPatternPos == rightLocPatternList.GetSize())
					rightLocPatternPos = 0;
				
				// if location breaks on level only
				if (locationrightBreak == 2) {
					levelrightHoldIDs[j] = nextrightLocationID;
					levelRightPatternHolds[j] = rightLocPatternPos;
				}
			}
		}
		else {
			for ( k = bayTree.treeChildren[j].treeChildren.GetSize() -1 ; k >= 0 ; k-- ) {
				if ( (locationrightBreak == 0 && j == 0 && k == bayTree.treeChildren[j].treeChildren.GetSize() -1 && startInd == 1) ||
					 (locationrightBreak == 1 && j == 0 && k == bayTree.treeChildren[j].treeChildren.GetSize() -1) ||
					 (locationrightBreak == 2 && k == bayTree.treeChildren[j].treeChildren.GetSize() -1 && startInd == 1) ||
					 (locationrightBreak == 3 && k == bayTree.treeChildren[j].treeChildren.GetSize() -1) )
					nextrightLocationID = FindNextID(previousrightLocationID, locationrightStep,
						rightLocationScheme, 1, locationMask, 2,
						rightLocPatternList, rightLocPatternPos);
				else
					nextrightLocationID = FindNextID(previousrightLocationID, locationrightStep,
						rightLocationScheme, 0, locationMask, 2,
						rightLocPatternList, rightLocPatternPos);

				FormatLocationAndUpdate(nextrightLocationID,bayTree.treeChildren[j].treeChildren[k].fileOffset,
					locationMask, sectionDesc, aisleDesc,
					nextrightBayID, nextrightLevelID);
				previousrightLocationID = nextrightLocationID;

				rightLocPatternPos++;
				if (rightLocPatternPos == rightLocPatternList.GetSize())
					rightLocPatternPos = 0;
				
				// if location breaks on level only
				if (locationrightBreak == 2) {
					levelrightHoldIDs[j] = nextrightLocationID;
					levelRightPatternHolds[j] = rightLocPatternPos;
				}

			}
		}				
		previousrightLevelID = nextrightLevelID;
	}
	
	previousrightBayID = nextrightBayID;

	return;
}

void CLocationNumberingService::numberLeftBay(CString & nextleftBayID, CString & previousleftBayID, int bayleftStep,
		CString & locationMask, int levelleftBreak, CString & previousleftLevelID,
		int locationleftBreak, CString & leftLevelStart, CString & leftLocationStart,
		CString & previousleftLocationID,
		CString & nextleftLevelID, CString & nextleftLocationID, int leftLevelScheme, int leftLocationScheme,
		int leftBayScheme, CString & aisleDesc, CString & sectionDesc, CSsaStringArray & levelleftHoldIDs, int startInd,
		int levelleftStep, int direction, int locationleftStep,
		TreeElement & bayTree,
		CStringArray &leftBayPatternList, CStringArray &leftLevelPatternList, CStringArray &leftLocPatternList,
		CWordArray &levelLeftPatternHolds) {


	int j,k;
	CBTreeHelper btHelper;

	nextleftBayID = FindNextID(previousleftBayID, bayleftStep,		
		leftBayScheme, startInd, locationMask, 0,
		leftBayPatternList, leftBayPatternPos);

	/*
	ads_printf("bay: ");
	ads_printf(nextleftBayID);
	ads_printf("\n");
	*/

	leftBayPatternPos++;
	if (leftBayPatternPos == leftBayPatternList.GetSize())
		leftBayPatternPos = 0;

	if ( startInd == 1 ) {
		previousleftLocationID = leftLocationStart;
		previousleftLevelID = leftLevelStart;
		leftLevelPatternPos = 0;
		leftLocPatternPos = 0;
	}

	btHelper.UpdateBayDescription(nextleftBayID,bayTree.fileOffset);

	if ( levelleftBreak == 1 ) {
		previousleftLevelID = leftLevelStart;
		leftLevelPatternPos = 0;
	}

	if ( locationleftBreak == 1 ) {
		previousleftLocationID = leftLocationStart;
		leftLocPatternPos = 0;
	}

	for ( j = 0; j < bayTree.treeChildren.GetSize(); j++) {
		if ( (levelleftBreak == 1 && j == 0) ||
			 (levelleftBreak == 0 && previousleftLevelID == leftLevelStart) )
			nextleftLevelID = FindNextID(previousleftLevelID, levelleftStep,
				leftLevelScheme, 1, locationMask, 1,
				leftLevelPatternList, leftLevelPatternPos);
		else
			nextleftLevelID = FindNextID(previousleftLevelID, levelleftStep,
				leftLevelScheme, 0, locationMask, 1,
				leftLevelPatternList, leftLevelPatternPos);

		leftLevelPatternPos++;
		if (leftLevelPatternPos == leftLevelPatternList.GetSize())
			leftLevelPatternPos = 0;

		/*
		ads_printf("level: ");
		ads_printf(nextleftLevelID);
		ads_printf("\n");
		*/

		btHelper.UpdateLevelDescription(nextleftLevelID,bayTree.treeChildren[j].fileOffset);

		if (locationleftBreak == 2 ) {
			if ( j > levelleftHoldIDs.GetSize() - 1 ) {
				levelleftHoldIDs.Add(leftLocationStart);
				levelLeftPatternHolds.Add(0);
			}
			previousleftLocationID = levelleftHoldIDs[j];
			leftLocPatternPos = levelLeftPatternHolds[j];
		}

		else if ( locationleftBreak == 3 ) {
			previousleftLocationID = leftLocationStart;
			leftLocPatternPos = 0;
		}

		if ( direction == 1) {
			for ( k = 0; k < bayTree.treeChildren[j].treeChildren.GetSize(); k++ ) {
				if ( (locationleftBreak == 0 && startInd == 1 && j == 0 && k == 0) ||
					 (locationleftBreak == 1 && k == 0 && j == 0) ||
					 (locationleftBreak == 2 && k == 0 && startInd == 1) ||
					 (locationleftBreak == 3 && k == 0 ) )
					nextleftLocationID = FindNextID(previousleftLocationID, locationleftStep,
						leftLocationScheme, 1, locationMask, 2,
						leftLocPatternList, leftLocPatternPos);
				else
					nextleftLocationID = FindNextID(previousleftLocationID, locationleftStep,
						leftLocationScheme, 0, locationMask, 2,
						leftLocPatternList, leftLocPatternPos);

				/*
				ads_printf("loc: ");
				ads_printf(nextleftLocationID);
				ads_printf("\n");
				*/

				FormatLocationAndUpdate(nextleftLocationID,bayTree.treeChildren[j].treeChildren[k].fileOffset,
					locationMask, sectionDesc, aisleDesc,
					nextleftBayID, nextleftLevelID);
				previousleftLocationID = nextleftLocationID;

				leftLocPatternPos++;
				if (leftLocPatternPos == leftLocPatternList.GetSize())
					leftLocPatternPos = 0;

				if (locationleftBreak == 2) {
					levelleftHoldIDs[j] = nextleftLocationID;
					levelLeftPatternHolds[j] = leftLocPatternPos;
				}
			}
		}
		else {
			for ( k = bayTree.treeChildren[j].treeChildren.GetSize() -1 ; k >= 0 ; k-- ) {
				if ( (locationleftBreak == 0 && startInd == 1 && j == 0 && k == bayTree.treeChildren[j].treeChildren.GetSize() -1) ||
					 (locationleftBreak == 1 && j == 0 && k == bayTree.treeChildren[j].treeChildren.GetSize() -1) ||
					 (locationleftBreak == 2 && k == bayTree.treeChildren[j].treeChildren.GetSize() -1 && startInd == 1) ||
					 (locationleftBreak == 3 && k == bayTree.treeChildren[j].treeChildren.GetSize() -1) )
					nextleftLocationID = FindNextID(previousleftLocationID, locationleftStep,
						leftLocationScheme, 1, locationMask, 2,
						leftLocPatternList, leftLocPatternPos);
				else
					nextleftLocationID = FindNextID(previousleftLocationID, locationleftStep,
						leftLocationScheme, 0, locationMask, 2,
						leftLocPatternList, leftLocPatternPos);

				/*
				ads_printf("loc: ");
				ads_printf(nextleftLocationID);
				ads_printf("\n");
				*/


				FormatLocationAndUpdate(nextleftLocationID,bayTree.treeChildren[j].treeChildren[k].fileOffset,
					locationMask, sectionDesc, aisleDesc,
					nextleftBayID, nextleftLevelID);
				previousleftLocationID = nextleftLocationID;

				leftLocPatternPos++;
				if (leftLocPatternPos == leftLocPatternList.GetSize())
					leftLocPatternPos = 0;

				if (locationleftBreak == 2) {
					levelleftHoldIDs[j] = nextleftLocationID;
					levelLeftPatternHolds[j] = leftLocPatternPos;
				}
			}
		}				
		previousleftLevelID = nextleftLevelID;
	}

	previousleftBayID = nextleftBayID;

	return;
}


void CLocationNumberingService::GetChangedLocationDescription(CString &oldDescription,
							  CString locationMask,
							  int nameType,
							  CString partName ) 
{
	char * sectionMask;
	BOOL partNumeric;
	char alphabetCap[28]=" ABCDEFGHIJKLMNOPQRSTUVWXYZ";
	char alphabetNoCap[28]=" abcdefghijklmnopqrstuvwxyz";
	int i, k;
	int secIndex, aisleIndex, bayIndex, levelIndex = 0;
	char * locationDesc;

	/*
	qqhSLOTLocation tempLocation;
	int k;
	int tempLength,tempReturn;
	char * buf;
	int aMultiplier = 1;
	CSsaStringArray tempArray;
	CString tempString;
*/
	partNumeric = TRUE;
	for (i=1; i < 27 && partNumeric; i++) {
		if (partName.Find(alphabetCap[i]) != -1 || partName.Find(alphabetNoCap[i]) != -1)
			partNumeric = FALSE;
	}
	sectionMask = new char[locationMask.GetLength()+1];
	locationDesc = new char[oldDescription.GetLength()+1];
	memset(locationDesc,0,locationMask.GetLength()+1);
	memset(sectionMask,0,locationMask.GetLength()+1);
	strcpy(sectionMask,locationMask.GetBuffer(0));
	locationMask.ReleaseBuffer();
	
	if (oldDescription == "New Location")
		return;
	

	secIndex = aisleIndex = bayIndex = levelIndex = 0;


	strcpy(locationDesc, oldDescription.GetBuffer(0));
	oldDescription.ReleaseBuffer();

	for ( k = 0; sectionMask[k] != '\0'; k++ ) {
		switch (sectionMask[k]) {
		case '\\' : case '/' : {
			// skip this one and the next one if it's escaped
			if (k < (int)(strlen(sectionMask)-1)) {
				k++;
				continue;
			}
					}	
		case 's' : case 'S' :  { 
			if ( nameType == 1 ) {
				if ( secIndex < partName.GetLength())
					locationDesc[k] = partName.GetAt(secIndex);
				else {
					if (partNumeric)
						locationDesc[k] = '0';
					else
						locationDesc[k] = ' ';
				}
				secIndex++;
			}
			break;
				   }
		case 'a' : case 'A' : {
			if ( nameType == 2 ) {
				if ( aisleIndex < partName.GetLength())
					locationDesc[k] = partName.GetAt(aisleIndex);
				else {
					if (partNumeric)
						locationDesc[k] = '0';
					else
						locationDesc[k] = ' ';
				}
				aisleIndex++;
			}
			break;
				   }
		case 'b' : case 'B' : {
			if ( nameType == 3 ) {
				if ( bayIndex < partName.GetLength())
					locationDesc[k] = partName.GetAt(bayIndex);
				else {
					if (partNumeric)
						locationDesc[k] = '0';
					else
						locationDesc[k] = ' ';
				}
				bayIndex++;
			}
			break;
				   }
		case 'l' : case 'L' : {
			if ( nameType == 4 ) {
				if ( levelIndex < partName.GetLength())
					locationDesc[k] = partName.GetAt(levelIndex);
				else {
					if (partNumeric)
						locationDesc[k] = '0';
					else
						locationDesc[k] = ' ';
				}
				levelIndex++;
			}
			break;
				   }
		default : {
			locationDesc[k] = locationDesc[k];
			break;
				  }
		}
	}
	oldDescription = locationDesc;

	oldDescription.Remove('\\');
	oldDescription.Remove('/');

	delete locationDesc;
	delete sectionMask;

	return;
}

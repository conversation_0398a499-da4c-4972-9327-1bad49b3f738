// BayProfilePropertyPage.cpp : implementation file
//

#include "stdafx.h"
#include "modal.h"
#include "BayProfilePropertyPage.h"

#ifdef _DEBUG
#define new DEBUG_NEW
#undef THIS_FILE
static char THIS_FILE[] = __FILE__;
#endif

/////////////////////////////////////////////////////////////////////////////
// CBayProfilePropertyPage property page

IMPLEMENT_DYNCREATE(CBayProfilePropertyPage, CPropertyPage)

CBayProfilePropertyPage::CBayProfilePropertyPage()
{
	//{{AFX_DATA_INIT(CBayProfilePropertyPage)
		// NOTE: the ClassWizard will add member initialization here
	//}}AFX_DATA_INIT
}

CBayProfilePropertyPage::~CBayProfilePropertyPage()
{
}



BEGIN_MESSAGE_MAP(CBayProfilePropertyPage, CPropertyPage)
	//{{AFX_MSG_MAP(CBayProfilePropertyPage)
	ON_WM_HELPINFO()
	//}}AFX_MSG_MAP
END_MESSAGE_MAP()

/////////////////////////////////////////////////////////////////////////////
// CBayProfilePropertyPage message handlers

BOOL CBayProfilePropertyPage::OnNotify(WPARAM wParam, LPARAM lParam, LRESULT* pResult) 
{
	// TODO: Add your specialized code here and/or call the base class
	
	return CPropertyPage::OnNotify(wParam, lParam, pResult);
}

BOOL CBayProfilePropertyPage::OnHelpInfo(HELPINFO* pHelpInfo) 
{
	// TODO: Add your message handler code here and/or call default
	
	return CPropertyPage::OnHelpInfo(pHelpInfo);
}

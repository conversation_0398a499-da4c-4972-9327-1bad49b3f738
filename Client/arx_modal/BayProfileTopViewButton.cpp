// BayProfileTopViewButton.cpp : implementation file
//

#include "stdafx.h"
#include "modal.h"
#include "BayProfileTopViewButton.h"
#include "Constants.h"

#ifdef _DEBUG
#define new DEBUG_NEW
#undef THIS_FILE
static char THIS_FILE[] = __FILE__;
#endif

#define ARROWLEN 4

/////////////////////////////////////////////////////////////////////////////
// CBayProfileTopViewButton

CBayProfileTopViewButton::CBayProfileTopViewButton()
{
}

CBayProfileTopViewButton::~CBayProfileTopViewButton()
{
}


BEGIN_MESSAGE_MAP(CBayProfileTopViewButton, CButton)
	//{{AFX_MSG_MAP(CBayProfileTopViewButton)
		// NOTE - the ClassWizard will add and remove mapping macros here.
	//}}AFX_MSG_MAP
END_MESSAGE_MAP()

/////////////////////////////////////////////////////////////////////////////
// CBayProfileTopViewButton message handlers


void CBayProfileTopViewButton::DrawItem(LPDRAWITEMSTRUCT lpDrawItemStruct) 
{
	UINT uStyle = DFCS_BUTTONPUSH;
	
	// This code only works with buttons.
	ASSERT(lpDrawItemStruct->CtlType == ODT_BUTTON);
	
	
	// If drawing selected, add the pushed style to DrawFrameControl.
	if (lpDrawItemStruct->itemState & ODS_SELECTED)
		uStyle |= DFCS_PUSHED;
	
	// Draw the button frame.
	::DrawFrameControl(lpDrawItemStruct->hDC, &lpDrawItemStruct->rcItem, 
		DFC_BUTTON, uStyle);
	
	// Get the button's text.
	CString strText;
	GetWindowText(strText);
	
	// Draw the button text using the text color red.
	COLORREF crOldColor = ::SetTextColor(lpDrawItemStruct->hDC, RGB(255,0,0));
	::DrawText(lpDrawItemStruct->hDC, strText, strText.GetLength(), 
		&lpDrawItemStruct->rcItem, DT_SINGLELINE|DT_VCENTER|DT_CENTER);
	::SetTextColor(lpDrawItemStruct->hDC, crOldColor);
	
	CDC cdc;
	cdc.Attach(lpDrawItemStruct->hDC);
	
	CRect r;

	GetClientRect(&r);

	cdc.SetMapMode(MM_ANISOTROPIC);
	cdc.SetViewportOrg(r.left, r.bottom);
	cdc.SetWindowExt(1, 1);
	cdc.SetViewportExt(1, -1);

	switch (m_DimensionInfo.m_BayType) {
	case BAYTYPE_BIN:
		DrawBin(cdc);
		break;
	case BAYTYPE_PALLET:
		DrawPallet(cdc);
		break;
	case BAYTYPE_CASEFLOW:
	case BAYTYPE_PALLETFLOW:
		DrawFlow(cdc);
		break;
	case BAYTYPE_FLOOR:
		DrawFloor(cdc);
		break;
	case BAYTYPE_DRIVEIN:
		DrawDriveIn(cdc);
		break;
	}

	// Draw Information Lines
	CPen pen, *prevPen;
	CFont font, *pOldFont;

	try {
		if (font.CreateFont(12, 0,0,0,FW_NORMAL, 0,0,0, ANSI_CHARSET, OUT_DEFAULT_PRECIS,
			CLIP_DEFAULT_PRECIS, DEFAULT_QUALITY, DEFAULT_PITCH|FF_DONTCARE, "Arial")) {			
			pOldFont = cdc.SelectObject(&font);
		}
		else {
			return;
		}
	}
	catch (...) {
		return;
	}
	
	pen.CreatePen(PS_SOLID, 1, RGB(0,0,0));
	prevPen = cdc.SelectObject(&pen);
	CSize sz = cdc.GetTextExtent("Width");
	cdc.TextOut(3, 10+sz.cy/2, "Width");
	
	cdc.MoveTo(sz.cx+5, 10);
	cdc.LineTo(sz.cx+5+r.Width()/8, 10);
	cdc.LineTo(cdc.GetCurrentPosition().x-ARROWLEN, cdc.GetCurrentPosition().y-ARROWLEN);
	cdc.MoveTo(sz.cx+5+r.Width()/8, 10);
	cdc.LineTo(cdc.GetCurrentPosition().x-ARROWLEN, cdc.GetCurrentPosition().y+ARROWLEN);
	
	cdc.SelectObject(pOldFont);
	font.DeleteObject();
	
	try {
		if (font.CreateFont(12, 0, 2700, 2700, FW_NORMAL, 0,0,0, ANSI_CHARSET, OUT_DEFAULT_PRECIS,
			CLIP_DEFAULT_PRECIS, DEFAULT_QUALITY, DEFAULT_PITCH|FF_DONTCARE, "Arial")) {			
			pOldFont = cdc.SelectObject(&font);
		}
		else {
			return;
		}
	}
	catch (...) {
		return;
	}
	
	sz = cdc.GetTextExtent("Depth");
	cdc.TextOut(7-sz.cy/2, r.Width()/8, "Depth");
	
	cdc.MoveTo(7, r.Width()/8+sz.cx+2);
	cdc.LineTo(7, r.Width()/8+sz.cx+2+r.Width()/8);
	cdc.LineTo(cdc.GetCurrentPosition().x-ARROWLEN, cdc.GetCurrentPosition().y-ARROWLEN);
	cdc.MoveTo(7,  r.Width()/8+sz.cx+2+r.Width()/8);
	cdc.LineTo(cdc.GetCurrentPosition().x+ARROWLEN, cdc.GetCurrentPosition().y-ARROWLEN);
	
	cdc.SelectObject(prevPen);
	pen.DeleteObject();
	cdc.SelectObject(pOldFont);
	font.DeleteObject();
	
	cdc.Detach();
}


void CBayProfileTopViewButton::DrawBin(CDC &cdc)
{

	CRect r;

	GetClientRect(&r);



	CString temp;
	double len = r.Height()*.6;

	// Left side
	DrawVertLine(cdc, CPoint(r.Width()/4, r.Height()*.25), len, 2, FALSE);
	
	// Depth Dimension Line
	temp.Format("%.0f", m_DimensionInfo.m_BayDepth);
	DrawVertDimLine(cdc, CPoint(r.Width()/4-15, r.Height()*.25), len, 1, temp);

	// Right side
	DrawVertLine(cdc, CPoint(r.Width()*3/4, r.Height()*.25), len, 2, FALSE);


	// Crossbar
	//DrawHorzLine(cdc, CPoint(r.Width()/3, urLen), r.Width()/3, 2, FALSE);

	// Width Dimension Line
	temp.Format("%.0f", m_DimensionInfo.m_BayWidth);
	DrawHorzDimLine(cdc, CPoint(r.Width()/4+1, r.Height()*3/4+10), r.Width()/2-3, 1, temp);

	// Width (including uprights) Dimension Line
	temp.Format("%.0f", m_DimensionInfo.m_BayWidth + m_DimensionInfo.m_UprightWidth*2);
	DrawHorzDimLine(cdc, CPoint(r.Width()/4-1, r.Height()/4-10), r.Width()/2, 1, temp);

}

void CBayProfileTopViewButton::DrawPallet(CDC &cdc)
{
	DrawBin(cdc);

	if (m_DimensionInfo.m_PositionsDeep <= 0)
		return;

	CRect r;
	this->GetClientRect(&r);

	CRect boxRect;
	double rDepth = (r.Height()*.6)/m_DimensionInfo.m_PositionsDeep;
	boxRect.left = r.Width()*3/8;
	boxRect.right = r.Width()*5/8;
	boxRect.top = boxRect.bottom+rDepth;
	double startBottom = r.Height()*.25;

	for (int i=0; i < m_DimensionInfo.m_PositionsDeep; ++i) {
		boxRect.bottom = startBottom + i*(rDepth);
		boxRect.top = boxRect.bottom + rDepth;

		DrawBox(cdc, boxRect, 1, TRUE);
	}

}


void CBayProfileTopViewButton::DrawFlow(CDC &cdc)
{
	DrawBin(cdc);

	if (m_DimensionInfo.m_PositionsDeep <= 0)
		return;

	CRect r;
	this->GetClientRect(&r);

	CRect boxRect;
	double rDepth = (r.Height()*.6)/m_DimensionInfo.m_PositionsDeep;
	boxRect.left = r.Width()*3/8;
	boxRect.right = r.Width()*5/8;
	boxRect.top = boxRect.bottom+rDepth;
	double startBottom = r.Height()*.25;

	for (int i=0; i < m_DimensionInfo.m_PositionsDeep; ++i) {
		boxRect.bottom = startBottom + i*(rDepth);
		boxRect.top = boxRect.bottom + rDepth;

		DrawBox(cdc, boxRect, 1, TRUE);
	}

}


void CBayProfileTopViewButton::DrawFloor(CDC &cdc)
{
	CRect r;

	GetClientRect(&r);

	CString temp;
	double len = r.Height()*.6;

	// Left side
	DrawVertLine(cdc, CPoint(r.Width()/4, r.Height()*.25), len, 2, FALSE);
	
	// Depth Dimension Line
	temp.Format("%.0f", m_DimensionInfo.m_BayDepth);
	DrawVertDimLine(cdc, CPoint(r.Width()/4-15, r.Height()*.25), len, 1, temp);

	// Right side
	DrawVertLine(cdc, CPoint(r.Width()*3/4, r.Height()*.25), len, 2, FALSE);


	// Crossbar
	//DrawHorzLine(cdc, CPoint(r.Width()/3, urLen), r.Width()/3, 2, FALSE);

	// Width Dimension Line
	temp.Format("%.0f", m_DimensionInfo.m_BayWidth);
	DrawHorzDimLine(cdc, CPoint(r.Width()/4, r.Height()*.25 + r.Height()*.6+10), r.Width()/2, 1, temp);

	// Stack Width Dimension Line
	temp.Format("%.0f", m_DimensionInfo.m_StackWidth);
	DrawHorzDimLine(cdc, CPoint(r.Width()*3/8, r.Height()/4-10), r.Width()/4, 1, temp);

	// Now draw the positions
	int palletsDeep = m_DimensionInfo.m_SelectPositions +  m_DimensionInfo.m_ReservePositions;

	if (m_DimensionInfo.m_BayDepth == 0 ||
		m_DimensionInfo.m_StackDepth == 0 ||
		palletsDeep <= 0)
		return;

//	int palletsDeep = (m_DimensionInfo.m_BayDepth) /
//		m_DimensionInfo.m_StackDepth;


	double stackDepth = m_DimensionInfo.m_StackDepth;
	if (stackDepth > m_DimensionInfo.m_BayDepth/palletsDeep)
		stackDepth = m_DimensionInfo.m_BayDepth/palletsDeep;

	double totalLocSpace = m_DimensionInfo.m_BayDepth - (palletsDeep * stackDepth);


	double bayBottom = r.Height()/4;
	double bayTop = bayBottom + r.Height()*.6;
	double depth = bayTop - bayBottom;
	double totalBoxSpace = depth*(totalLocSpace/m_DimensionInfo.m_BayDepth);
	double boxDepth = (depth-totalBoxSpace) / palletsDeep;
	double boxSpace = (double)(depth-(boxDepth*palletsDeep))/(palletsDeep+1);

	double boxWidth = r.Width()/4;

	int numSelect = m_DimensionInfo.m_SelectPositions;

	for (int i=0; i < palletsDeep; ++i) {
		CRect boxRect;
		boxRect.bottom = bayBottom + boxDepth*i + (i+1)*boxSpace;
		boxRect.top = boxRect.bottom + boxDepth;
		boxRect.left = r.Width()*3/8;
		boxRect.right = boxRect.left + boxWidth;
		
		if (numSelect > 0) {
			DrawBoxWithText(cdc, boxRect, 1, TRUE, "S", 12);	
			numSelect--;
		}
		else
			DrawBoxWithText(cdc, boxRect, 1, TRUE, "R", 12);
	}

}


void CBayProfileTopViewButton::DrawDriveIn(CDC &cdc)
{
	DrawBin(cdc);

	CRect r;
	this->GetClientRect(&r);

	// Now draw the positions

	if (m_DimensionInfo.m_BayHeight == 0 ||
		m_DimensionInfo.m_SelectPositionHeight == 0 ||
		m_DimensionInfo.m_BayDepth == 0 ||
		m_DimensionInfo.m_StackDepth == 0)
		return;

	int palletsDeep = (m_DimensionInfo.m_BayDepth+m_DimensionInfo.m_Overhang) /
		m_DimensionInfo.m_StackDepth;

	double bayBottom = r.Height()/4;
	double bayTop = bayBottom + r.Height()*.6;
	double depth = bayTop - bayBottom;
	double boxDepth = depth / palletsDeep;


	double bayLeft = r.Width()/4;
	double bayRight = r.Width()*3/4;
	double width = bayRight - bayLeft;
	double boxWidth = r.Width()/4;

	for (int i=0; i < palletsDeep; ++i) {
		CRect boxRect;
		boxRect.bottom = bayBottom + boxDepth*i;
		boxRect.top = boxRect.bottom + boxDepth;
		boxRect.left = r.Width()*3/8;
		boxRect.right = boxRect.left + boxWidth;
		
		DrawBox(cdc, boxRect, 1, TRUE);	
	}

}

void CBayProfileTopViewButton::DrawVertLine(CDC &cdc, const CPoint &startPt, int len, int width, BOOL bDashed)
{
	CPen pen, *prevPen;

	if (bDashed)
		pen.CreatePen(PS_DOT, width, RGB(255,0,0));
	else
		pen.CreatePen(PS_SOLID, width, RGB(0,0,0));

	prevPen = cdc.SelectObject(&pen);
	cdc.MoveTo(startPt);
	cdc.LineTo(startPt.x, startPt.y+len);

	cdc.SelectObject(prevPen);
	pen.DeleteObject();	
}

void CBayProfileTopViewButton::DrawHorzLine(CDC &cdc, const CPoint &startPt, int len, int width, BOOL bDashed)
{
	CPen pen, *prevPen;

	if (bDashed)
		pen.CreatePen(PS_DOT, width, RGB(255,0,0));
	else
		pen.CreatePen(PS_SOLID, width, RGB(0,0,0));

	prevPen = cdc.SelectObject(&pen);
	cdc.MoveTo(startPt);
	cdc.LineTo(startPt.x+len, startPt.y);

	cdc.SelectObject(prevPen);
	pen.DeleteObject();	
}


void CBayProfileTopViewButton::DrawHorzDimLine(CDC &cdc, const CPoint &startPt, int len, int width, const CString &text,
											   int textSize)
{
	CPen pen, *prevPen;
	CFont font, *pOldFont;

	try {
		
		if (font.CreateFont(textSize, 0,0,0,FW_BOLD, 0,0,0, ANSI_CHARSET, OUT_DEFAULT_PRECIS,
			CLIP_DEFAULT_PRECIS, DEFAULT_QUALITY, DEFAULT_PITCH|FF_DONTCARE, "Arial")) {			
			pOldFont = cdc.SelectObject(&font);
		}
		else {
			return;
		}
	}
	catch (...) {
		return;
	}
	CSize size = cdc.GetTextExtent(text);
	cdc.SetTextColor(RGB(0,0, 255));
	cdc.TextOut(startPt.x+len/2-size.cx/2, startPt.y+size.cy/2, text);
	cdc.SetTextColor(RGB(0,0, 0));
	
	if (size.cx < len) {
		pen.CreatePen(PS_SOLID, width, RGB(0,0,0));
		prevPen = cdc.SelectObject(&pen);
		
		// Left Arrow
		cdc.MoveTo(startPt);
		cdc.LineTo(startPt.x+ARROWLEN, startPt.y-ARROWLEN);
		cdc.MoveTo(startPt);
		cdc.LineTo(startPt.x+ARROWLEN, startPt.y+ARROWLEN);

		// Main line
		cdc.MoveTo(startPt);
		cdc.LineTo(startPt.x+len/2-size.cx/2-2, startPt.y);
		cdc.MoveTo(startPt.x+len/2+size.cx/2+1, startPt.y);
		cdc.LineTo(startPt.x+len, startPt.y);
		
		// Right Arrow
		cdc.LineTo((startPt.x+len)-ARROWLEN, startPt.y+ARROWLEN);
		cdc.MoveTo(startPt.x+len, startPt.y);
		cdc.LineTo((startPt.x+len)-ARROWLEN, startPt.y-ARROWLEN);
		
		cdc.MoveTo(startPt.x-1, startPt.y+ARROWLEN+1);
		cdc.LineTo(startPt.x-1, (startPt.y-ARROWLEN));
		
		cdc.MoveTo(startPt.x+len+1, startPt.y+ARROWLEN+1);
		cdc.LineTo(startPt.x+len+1, (startPt.y-ARROWLEN));
		
		
		cdc.SelectObject(prevPen);
		pen.DeleteObject();
	}
	cdc.SelectObject(pOldFont);
	font.DeleteObject();
}

void CBayProfileTopViewButton::DrawVertDimLine(CDC &cdc, const CPoint &startPt, int len, int width, const CString &text,
											   int textSize)
{
	CPen pen, *prevPen;
	CFont font, *pOldFont;

	try {
		
		if (font.CreateFont(textSize, 0,0,0,FW_BOLD, 0,0,0, ANSI_CHARSET, OUT_DEFAULT_PRECIS,
			CLIP_DEFAULT_PRECIS, DEFAULT_QUALITY, DEFAULT_PITCH|FF_DONTCARE, "Arial")) {			
			pOldFont = cdc.SelectObject(&font);
		}
		else {
			return;
		}
	}
	catch (...) {
		return;
	}

	CSize size = cdc.GetTextExtent(text);
	cdc.SetTextColor(RGB(0,0, 255));
	cdc.TextOut(startPt.x-size.cx/2, startPt.y+len/2+size.cy/2, text);
	cdc.SetTextColor(RGB(0,0, 0));
	
	if (size.cy < len) {
		pen.CreatePen(PS_SOLID, width, RGB(0,0,0));
		prevPen = cdc.SelectObject(&pen);
		
		cdc.MoveTo(startPt);
		cdc.LineTo(startPt.x-ARROWLEN, startPt.y+ARROWLEN);
		cdc.MoveTo(startPt);
		cdc.LineTo(startPt.x+ARROWLEN, startPt.y+ARROWLEN);
		cdc.MoveTo(startPt);
		
		cdc.LineTo(startPt.x, (startPt.y+len/2-size.cy/2-1));
		cdc.MoveTo(startPt.x, (startPt.y+len/2+size.cy/2+1));
		cdc.LineTo(startPt.x, startPt.y+len);
		
		cdc.LineTo(startPt.x-ARROWLEN, startPt.y+len-ARROWLEN);
		cdc.MoveTo(startPt.x, startPt.y+len);
		cdc.LineTo(startPt.x+ARROWLEN, startPt.y+len-ARROWLEN);
		
		cdc.MoveTo((startPt.x-ARROWLEN), startPt.y-1);
		cdc.LineTo(startPt.x+ARROWLEN+1, startPt.y-1);
		
		cdc.MoveTo((startPt.x-ARROWLEN), startPt.y+len+1);
		cdc.LineTo(startPt.x+ARROWLEN+1, startPt.y+len+1);
		
		
		cdc.SelectObject(prevPen);
		pen.DeleteObject();
	}
	cdc.SelectObject(pOldFont);
	font.DeleteObject();
}

void CBayProfileTopViewButton::DrawBox(CDC &cdc, const CRect& r, int width, BOOL bDashed)
{
	CPen pen, *prevPen;

	if (bDashed)
		pen.CreatePen(PS_SOLID, width, RGB(255,0,0));
	else
		pen.CreatePen(PS_SOLID, width, RGB(0,0,0));

	prevPen = cdc.SelectObject(&pen);
	
	cdc.MoveTo(r.BottomRight());
	cdc.LineTo(r.right, r.top);
	cdc.LineTo(r.left, r.top);
	cdc.LineTo(r.left, r.bottom);
	cdc.LineTo(r.right, r.bottom);



	cdc.SelectObject(prevPen);
	pen.DeleteObject();	
}

void CBayProfileTopViewButton::DrawBoxWithText(CDC &cdc, const CRect& r, int width, BOOL bDashed,
												const CString &text, int textSize)
{
	CPen pen, *prevPen;
	CFont font, *pOldFont;
	
	try {
		
		if (font.CreateFont(textSize, 0,0,0,FW_BOLD, 0,0,0, ANSI_CHARSET, OUT_DEFAULT_PRECIS,
			CLIP_DEFAULT_PRECIS, DEFAULT_QUALITY, DEFAULT_PITCH|FF_DONTCARE, "Arial")) {			
			pOldFont = cdc.SelectObject(&font);
		}
		else {
			return;
		}
	}
	catch (...) {
		return;
	}
	
	
	CSize size = cdc.GetTextExtent(text);
	int hgt = r.Height();
	int wid = r.Width();
	if (size.cx < abs(r.Width()) && size.cy < abs(r.Height())) {
		cdc.SetTextColor(RGB(0,0, 255));
		cdc.TextOut(r.CenterPoint().x-size.cx/2, r.CenterPoint().y+size.cy/2, text);
		cdc.SetTextColor(RGB(0,0,0));
	}
	
	cdc.SelectObject(pOldFont);
	font.DeleteObject();
	
	if (bDashed)
		pen.CreatePen(PS_SOLID, width, RGB(255,0,0));
	else
		pen.CreatePen(PS_SOLID, width, RGB(0,0,0));
	
	prevPen = cdc.SelectObject(&pen);
	
	cdc.MoveTo(r.BottomRight());
	cdc.LineTo(r.right, r.top);
	cdc.LineTo(r.left, r.top);
	cdc.LineTo(r.left, r.bottom);
	cdc.LineTo(r.right, r.bottom);
	
	
	
	cdc.SelectObject(prevPen);
	pen.DeleteObject();	
}

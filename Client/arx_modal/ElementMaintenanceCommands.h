// ElementMaintenanceCommands.h: interface for the CElementMaintenanceCommands class.
//
//////////////////////////////////////////////////////////////////////

#if !defined(AFX_ELEMENTMAINTENANCECOMMANDS_H__54FDC0B7_3F98_491B_A4F5_0DCCB4C6938B__INCLUDED_)
#define AFX_ELEMENTMAINTENANCECOMMANDS_H__54FDC0B7_3F98_491B_A4F5_0DCCB4C6938B__INCLUDED_

#include "Commands.h"

#if _MSC_VER > 1000
#pragma once
#endif // _MSC_VER > 1000

class CElementMaintenanceCommands : public CCommands
{
public:
	

	CElementMaintenanceCommands();
	virtual ~CElementMaintenanceCommands();

	static void RegisterCommands();
	static void FacilityMaintenance();
	static void SectionMaintenance();
	static void AisleMaintenance();
	static void SideMaintenance();
	static void BayMaintenance();
	static void LevelLocationMaintenance();
	static void FacilityTreeViewer();
	static void AddAisle();
	static void AddPickPath();
	static void DeleteBay();
	static void DeleteAisle();
	static void DeletePickPath();
	static void AddHotSpot();
	static void PickPathProperties();
	static void RenumberAisle();
	static void ReAddPickPath();
	static void ChangeRacktype();
	static void ElementMaintenance();
};

#endif // !defined(AFX_ELEMENTMAINTENANCECOMMANDS_H__54FDC0B7_3F98_491B_A4F5_0DCCB4C6938B__INCLUDED_)

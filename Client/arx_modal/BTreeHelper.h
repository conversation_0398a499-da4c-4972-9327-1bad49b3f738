// BTreeHelper.h: interface for the CBTreeHelper class.
//
//////////////////////////////////////////////////////////////////////

#if !defined(AFX_BTREEHELPER_H__F331DDB6_709D_4CC3_809A_BE8763870289__INCLUDED_)
#define AFX_BTREEHELPER_H__F331DDB6_709D_4CC3_809A_BE8763870289__INCLUDED_

#if _MSC_VER > 1000
#pragma once
#endif // _MSC_VER > 1000

#include "TreeElement.h"

class CBTreeHelper  
{
public:
	int GetDeletedElementList(TreeElement &changesTree, CString &deleteList);
	void SendAislesToForte(TreeElement &changesTree);
	CBTreeHelper();
	virtual ~CBTreeHelper();
	int BuildChangesTree(TreeElement & changesTree, int & nextNum);
	int DeleteFacilityBranch( CString elementName, int branchOffset );
	int NewFacilityTree(TreeElement & changesTree, int wmsType, int wmsUnits);

	int GetSectionDescriptions(CSsaStringArray & resArray, CArray <int, int&> &sectionList,
		CArray <int, int&> &newSectionIndexList);
	int ChangeAcadFileName(CString cadFileName);
	int UpdateFacilityDescription(CString description);
	int CBTreeHelper::UpdateFacilityNotes(CString notes);
	int GetBtFacility(int fileOffset, qqhSLOTFacility &tempFacility);
	int GetBtSection(int fileOffset, qqhSLOTSection & tempSection);
	int GetBtAisle(int fileOffset, qqhSLOTAisle &tempAisle);
	int GetBtSide(int fileOffset, qqhSLOTSide &tempSide);
	int GetBtBay(int fileOffset, qqhSLOTBay &tempBay);
	int GetBtLevel(int fileOffset, qqhSLOTLevel &tempLevel);
	int GetBtLocation(int fileOffset, qqhSLOTLocation  &tempLocation);

	int SetBtFacility(int fileOffset, qqhSLOTFacility & newFacility);
	int SetBtSection(int fileOffset, qqhSLOTSection &newSection);
	int SetBtAisle(int fileOffset, qqhSLOTAisle &newAisle);
	int SetBtSide(int fileOffset, qqhSLOTSide &newSide);
	int SetBtBay(int fileOffset, qqhSLOTBay &newBay);
	int SetBtLevel(int fileOffset, qqhSLOTLevel &newLevel);
	int SetBtLocation(int fileOffset, qqhSLOTLocation &newLocation);

	int UpdateBTWithAisleForPickPath(CString acadHandle, TreeElement & changesTree, 
		int * bayIndex, int * aisleIndex, int * sectionIndex, 
		int * sideIndex);
	int UpdateBTWithAisleForPickPath(int DBID, TreeElement & changesTree, 
		int * aisleIndex, int * sectionIndex);
	int UpdateBTWithAisleForPickPathRenumbering(int DBID, TreeElement & changesTree, 
								 int * aisleIndex, int * sectionIndex);
	int UpdateBTWithAisleByConPickPathAcadHandle(CString acadHandle, TreeElement & changesTree);
	int UpdateBTWithAisle(CString acadHandle, TreeElement & changesTree);
	int UpdateBTWithBay(CString acadHandle, TreeElement & changesTree);
	int UpdateBTWithAisleByPickPathAcadHandle(CString acadHandle, TreeElement & changesTree);

	int UpdateFacilityElementDBIDs(TreeElement &changesTree);

	void UpdateBayDescription(CString desc, int storeNum);
	void UpdateLevelDescription(CString desc, int storeNum);
	void UpdateLocationDescription(int storeNum, CString desc);

	int AddSectionToFacility( qqhSLOTSection & theSection, TreeElement & changesTree );
	int AddAisleToFacility( qqhSLOTAisle & theAisle, const int parentSectionOffset, TreeElement & changesTree );
	int AddBaytoFacility( qqhSLOTBay & theBay, const int parentSideOffset, TreeElement & changesTree, 
					 const int bayIndex, const int parentSideIndex, const int parentAisleIndex,
					 const int parentSectionIndex);
	
	int OpenBTree();
	int CloseBTree();

	int ReadNoOpenBtAisle(int fileOffset, qqhSLOTAisle &tempAisle);
	int ReadNoOpenBtBay(int fileOffset, qqhSLOTBay &tempBay);

	CString OpenFacilityTree( const int facility_id, TreeElement & changesTree, int * numBays );
	int SaveFacility(TreeElement & changesTree, int saveAs, const CString &saveAsOptions = "");
	int DeleteFacility(int facilityID);
	int UnlockFacility( const int facility_id );
};

#endif // !defined(AFX_BTREEHELPER_H__F331DDB6_709D_4CC3_809A_BE8763870289__INCLUDED_)

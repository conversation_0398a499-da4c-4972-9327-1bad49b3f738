// ExternalConnection.h: interface for the CExternalConnection class.
//
//////////////////////////////////////////////////////////////////////

#if !defined(AFX_EXTERNALCONNECTION_H__68FF7D90_0F1F_48F9_857F_C91290354480__INCLUDED_)
#define AFX_EXTERNALCONNECTION_H__68FF7D90_0F1F_48F9_857F_C91290354480__INCLUDED_

#if _MSC_VER > 1000
#pragma once
#endif // _MSC_VER > 1000

class CExternalConnection : public CObject  
{
public:
	CString ConnectionTypeAsText();
	CExternalConnection();
	CExternalConnection(const CExternalConnection& other);
	CExternalConnection& operator=(const CExternalConnection& other);
	BOOL operator==(const CExternalConnection& other);
	BOOL operator!=(const CExternalConnection&other) { return (! (*this == other)); }
	virtual ~CExternalConnection();

	int Parse(const CString& line);
	
	int m_ExternalConnectionDBId;
	CString m_Name;

	int m_ConnectionType;		// Prompt, MQSeries, FTP, Disk, XML-RPC(later)
	
	CString m_Host;				// For ftp, MQ, and xml-rpc
	int m_Port;					// For MQ and xml-rpc

	// MQSeries Only
	CString m_QueueManager;
	CString m_Queue;
	CString m_Channel;

	// Ftp Only
	CString m_Login;
	CString m_Password;

	// Ftp, disk
	CString m_Path;
	CString m_FileName;
	CString m_TriggerName;


	typedef enum {
		Prompt,
		Local,
		FTP,
		MQSeries,
		XML_RPC
	} enumConnectionType;


};

#endif // !defined(AFX_EXTERNALCONNECTION_H__68FF7D90_0F1F_48F9_857F_C91290354480__INCLUDED_)

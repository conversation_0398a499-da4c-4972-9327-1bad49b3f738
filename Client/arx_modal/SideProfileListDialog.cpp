// SideProfileListDialog.cpp : implementation file
//

#include "stdafx.h"
#include "modal.h"
#include "SideProfileListDialog.h"
#include "SideProfileSheet.h"
#include "SideProfileDataService.h"
#include "FacilityHelper.h"
#include "UtilityHelper.h"
#include "WizardHelper.h"
#include "ControlService.h"
#include "Constants.h"
#include "HelpService.h"
#include "ResourceHelper.h"

#ifdef _DEBUG
#define new DEBUG_NEW
#undef THIS_FILE
static char THIS_FILE[] = __FILE__;
#endif

extern CControlService controlService;
extern CSideProfileDataService sideProfileDataService;
extern CUtilityHelper utilityHelper;
extern CFacilityHelper facilityHelper;
extern CHelpService helpService;
/////////////////////////////////////////////////////////////////////////////
// CSideProfileListDialog dialog


CSideProfileListDialog::CSideProfileListDialog(CWnd* pParent /*=NULL*/)
	: CDialog(CSideProfileListDialog::IDD, pParent)
{
	//{{AFX_DATA_INIT(CSideProfileListDialog)
	//}}AFX_DATA_INIT
	m_pSideProfile = NULL;
	m_IsModeless = FALSE;
}


void CSideProfileListDialog::DoDataExchange(CDataExchange* pDX)
{
	CDialog::DoDataExchange(pDX);
	//{{AFX_DATA_MAP(CSideProfileListDialog)
	DDX_Control(pDX, IDC_SIDE_PROFILE_TREE, m_ProfileTreeCtrl);
	//}}AFX_DATA_MAP
}


BEGIN_MESSAGE_MAP(CSideProfileListDialog, CDialog)
	//{{AFX_MSG_MAP(CSideProfileListDialog)
	ON_NOTIFY(TVN_ENDLABELEDIT, IDC_SIDE_PROFILE_TREE, OnEndlabeleditSideProfileTree)
	ON_BN_CLICKED(IDC_NEW, OnNew)
	ON_BN_CLICKED(IDC_EDIT, OnEdit)
	ON_BN_CLICKED(IDC_COPY, OnCopy)
	ON_BN_CLICKED(IDC_DELETE, OnDelete)
	ON_WM_CONTEXTMENU()
	ON_BN_CLICKED(IDC_VIEW_DRAWING, OnViewDrawing)
	ON_NOTIFY(NM_DBLCLK, IDC_SIDE_PROFILE_TREE, OnDblclkSideProfileTree)
	ON_BN_CLICKED(IDHELP, OnHelp)
	ON_BN_CLICKED(IDC_BAY_WIZARD, OnBayWizard)
	ON_BN_CLICKED(IDC_AISLE_WIZARD, OnAisleWizard)
	ON_BN_CLICKED(IDC_MAIN_WIZARD, OnMainWizard)
	ON_WM_HELPINFO()
	//}}AFX_MSG_MAP
END_MESSAGE_MAP()

/////////////////////////////////////////////////////////////////////////////
// CSideProfileListDialog message handlers
BOOL CSideProfileListDialog::OnInitDialog() 
{
	CDialog::OnInitDialog();
	
	if (m_pSideProfile == NULL) {
		m_ImageList.Create(16, 16, TRUE, 4, 1);
		m_ImageList.Add(AfxGetApp()->LoadIcon(IDI_FLDOPEN));
		m_ImageList.Add(AfxGetApp()->LoadIcon(IDI_FLDCLS));
		m_ImageList.Add(AfxGetApp()->LoadIcon(IDI_LOCATIONICON));
		
		LoadSideProfileList();

		m_ProfileTreeCtrl.SetImageList(&m_ImageList, TVSIL_NORMAL);

		BuildSideProfileTree();
	}	
	else {
		m_ProfileTreeCtrl.SetImageList(&m_ImageList, TVSIL_NORMAL);

		BuildSideProfileTree();

		HTREEITEM hItem = m_ProfileTreeCtrl.GetChildItem(m_ProfileTreeCtrl.GetChildItem(TVI_ROOT));
		while (hItem != NULL) {

			CSideProfile *pTempProfile = (CSideProfile *)m_ProfileTreeCtrl.GetItemData(hItem);
			if (m_pSideProfile->m_SideProfileDBId == pTempProfile->m_SideProfileDBId) {
				m_ProfileTreeCtrl.SelectItem(hItem);
				break;
			}

			HTREEITEM hNextItem = m_ProfileTreeCtrl.GetNextItem(hItem, TVGN_NEXT);
			hItem = hNextItem;
		}
	}

	return TRUE;  // return TRUE unless you set the focus to a control
	              // EXCEPTION: OCX Property Pages should return FALSE
}

CSideProfileListDialog::~CSideProfileListDialog()
{
	for (int i=0; i < m_SideProfileList.GetSize(); ++i)
		delete m_SideProfileList[i];
}

int CSideProfileListDialog::LoadSideProfileList()
{
	CStringArray sideNameList, strings, usedList;
	CMap<int, int, int, int> usedMap;

	CWaitCursor cwc;

	if (m_SideProfileList.GetSize() > 0)
		return 0;

	try {
		sideProfileDataService.GetSideProfileList(sideNameList);
	}
	catch (...) {
		utilityHelper.ProcessError("Error getting list of side profiles.");
		return -1;
	}

	for (int i=0; i < sideNameList.GetSize(); ++i) {
		CSideProfile *pSideProfile = new CSideProfile();
		pSideProfile->Parse(sideNameList[i]);
		m_SideProfileList.Add(pSideProfile);
	}

	return 0;

}


void CSideProfileListDialog::OnNew() 
{
	CTemporaryResourceOverride tro;

	CSideProfileSheet sheet("New Side Profile", this);

	CSideProfile *pSideProfile = new CSideProfile;
	
	pSideProfile->m_SideProfileDBId = 0;
	pSideProfile->m_Description = "New";
	pSideProfile->m_TotalLength = 0;
	pSideProfile->m_MaximumBayDepth = 0;
	pSideProfile->m_MaximumBayHeight = 0;

	CWaitCursor cwc;

	CWizardHelper wizardHelper;
	if (wizardHelper.ShowSideWizard(pSideProfile) < 0) {
		delete pSideProfile;
		return;
	}

	m_SideProfileList.Add(pSideProfile);
	HTREEITEM hItem = m_ProfileTreeCtrl.InsertItem(pSideProfile->m_Description, 2, 2, TVI_ROOT, TVI_LAST);
	m_ProfileTreeCtrl.SetItemData(hItem, (unsigned long)pSideProfile);
	m_ProfileTreeCtrl.SortChildren(TVI_ROOT);
	m_ProfileTreeCtrl.SelectItem(hItem);

}

void CSideProfileListDialog::OnEdit() 
{
	// Hack because tree control doesn't handle return or escape keys properly
	// Since this is the default button, it will get the return message when
	// they finish editing a label
	if (IsTreeCtrlEditMessage(VK_RETURN))
		return;

	CSideProfile *pSideProfile;
	
	HTREEITEM hItem = m_ProfileTreeCtrl.GetSelectedItem();
	if (hItem == NULL) {
		AfxMessageBox("Please select the side profile you wish to edit.");
		return;
	}

	pSideProfile = (CSideProfile *)m_ProfileTreeCtrl.GetItemData(hItem);
	CString oldDesc = pSideProfile->m_Description;

	CWizardHelper wizardHelper;
	if (wizardHelper.ShowSideWizard(pSideProfile) >= 0) {
		if (*pSideProfile->m_Description != oldDesc) {
			m_ProfileTreeCtrl.SetItemText(hItem, pSideProfile->m_Description);
			m_ProfileTreeCtrl.SortChildren(TVI_ROOT);
		}	
	}
	
	return;
}

void CSideProfileListDialog::OnCopy() 
{
	CSideProfile *pSideProfile;
	
	HTREEITEM hCopyItem = m_ProfileTreeCtrl.GetSelectedItem();
	if (hCopyItem == NULL) {
		AfxMessageBox("Please select the side profile you wish to copy.");
		return;
	}
	pSideProfile = (CSideProfile *)m_ProfileTreeCtrl.GetItemData(hCopyItem);

	CWaitCursor cwc;

	if (pSideProfile->m_BayProfileList.GetSize() == 0) {	// hasn't been loaded yet
		try {
			sideProfileDataService.GetSideProfile(pSideProfile->m_SideProfileDBId, *pSideProfile);
		}
		catch (...) {
			AfxMessageBox("Error loading side profile.");
			return;
		}
	}

	CSideProfile *pNewProfile = new CSideProfile;
	*pNewProfile = *pSideProfile;

	int idx = 1;
	CString temp;
	temp.Format("Copy of %s", pSideProfile->m_Description);

	for (int i = 0; i < m_SideProfileList.GetSize(); ++i) {	
		if (m_SideProfileList[i]->m_Description.Find(temp) >= 0)
			idx++;
	}
	
	if (idx > 1)
		pNewProfile->m_Description.Format("%s (%d)", temp, idx);
	else
		pNewProfile->m_Description = temp;

	pNewProfile->m_SideProfileDBId = 0;
	
	try {
		sideProfileDataService.StoreSideProfile(*pNewProfile);
	}
	catch (...) {
		utilityHelper.ProcessError("Error storing new side profile.");
		delete pNewProfile;
		return;
	}

	HTREEITEM hItem = m_ProfileTreeCtrl.InsertItem(pNewProfile->m_Description, 2, 2, TVI_ROOT, TVI_LAST);
	m_ProfileTreeCtrl.SetItemData(hItem, (unsigned long)pNewProfile);
	m_ProfileTreeCtrl.SelectItem(hItem);

	m_SideProfileList.Add(pNewProfile);
	m_ProfileTreeCtrl.SortChildren(TVI_ROOT);
	
	m_ProfileTreeCtrl.EditLabel(hItem);

}


void CSideProfileListDialog::OnDelete() 
{
	CSideProfile *pSideProfile;
	
	HTREEITEM hItem = m_ProfileTreeCtrl.GetSelectedItem();
	if (hItem == NULL) {
		AfxMessageBox("Please select the side profile you wish to delete.");
		return;
	}

	pSideProfile = (CSideProfile *)m_ProfileTreeCtrl.GetItemData(hItem);

	CStringArray aisleNames;
	try {
		sideProfileDataService.GetAisleProfileNamesBySideProfile(pSideProfile->m_SideProfileDBId, aisleNames);
	}
	catch (...) {
		controlService.Log("Error determining if side profile is used in aisle profile.", 
			"Generic exception in GetAisleProfileNamesBySideProfile.\n");
		return;
	}

	if (aisleNames.GetSize() > 0) {
		CString temp;
		temp.Format("A side profile cannot be deleted if it is being used by an aisle profile.\n"
			"The selected side profile is being used by the following aisle profiles:\n");
		for (int i=0; i < aisleNames.GetSize(); ++i) {
			temp += "\t";
			temp += aisleNames[i];
			temp += "\n";
		}
		AfxMessageBox(temp);
		return;
	}

	CString temp;
	temp.Format("Delete %s", pSideProfile->m_Description);
	if (::MessageBox(this->m_hWnd, "Are you sure you wish to delete the side profile?",
		temp, MB_YESNO) != IDYES)
		return;

	CWaitCursor cwc;
	try {
		sideProfileDataService.DeleteSideProfile(pSideProfile->m_SideProfileDBId);
	}
	catch (...) {
		utilityHelper.ProcessError("Error deleting side profile.");
		return;
	}

	pSideProfile->m_SideProfileDBId = -1337;

	m_ProfileTreeCtrl.DeleteItem(hItem);

	for (int i=0; i < m_SideProfileList.GetSize(); ++i) {
		if (m_SideProfileList[i]->m_SideProfileDBId == -1337)
			break;
	}
	if (i < m_SideProfileList.GetSize()) {
		delete m_SideProfileList[i];
		m_SideProfileList.RemoveAt(i);
	}

}




void CSideProfileListDialog::OnDblclkSideProfileTree(NMHDR* pNMHDR, LRESULT* pResult) 
{
	HTREEITEM hItem = m_ProfileTreeCtrl.GetSelectedItem();
	if (hItem == NULL) {
		return;
	}

	OnEdit();
	
	*pResult = 0;
}

void CSideProfileListDialog::OnViewDrawing() 
{
	CSideProfile *pSideProfile;
	
	HTREEITEM hItem = m_ProfileTreeCtrl.GetSelectedItem();
	if (hItem == NULL) {
		AfxMessageBox("Please select the side profile you wish to view.");
		return;
	}

	pSideProfile = (CSideProfile *)m_ProfileTreeCtrl.GetItemData(hItem);

	CWaitCursor cwc;

	if (pSideProfile->m_BayProfileList.GetSize() == 0) {	// hasn't been loaded yet
		try {
			sideProfileDataService.GetSideProfile(pSideProfile->m_SideProfileDBId, *pSideProfile);
		}
		catch (...) {
			AfxMessageBox("Error loading side profile.");
			return;
		}
	}

	if (! facilityHelper.CheckCurrentFacility()) {
		AfxMessageBox("Error saving current facility.");
		return;
	}
	else if (controlService.IsFacilityOpen()) {
		if (AfxMessageBox("The current facility must be closed to view the side profile.\n"
			"Do you wish to close the facility and view the side profile?", MB_YESNO) != IDYES)
			return;
	}

	m_pSideProfile = pSideProfile;

	EndDialog(WM_VIEW_DRAWING);

}


void CSideProfileListDialog::BuildSideProfileTree()
{
	m_ProfileTreeCtrl.DeleteAllItems();

	for (int i=0; i < m_SideProfileList.GetSize(); ++i) {
		CSideProfile *pSideProfile = m_SideProfileList[i];

		HTREEITEM hItem = m_ProfileTreeCtrl.InsertItem(pSideProfile->m_Description, 2, 2, TVI_ROOT, TVI_LAST);
		m_ProfileTreeCtrl.SetItemData(hItem, (unsigned long)pSideProfile);
	}

	HTREEITEM hItem = m_ProfileTreeCtrl.GetNextItem(TVI_ROOT, TVGN_CHILD);
	HTREEITEM hTopItem = hItem;
	while (hItem != NULL) {
		m_ProfileTreeCtrl.Expand(hItem, TVE_EXPAND);
		HTREEITEM hNextItem = m_ProfileTreeCtrl.GetNextItem(hItem, TVGN_NEXT);
		hItem = hNextItem;
	}


	m_ProfileTreeCtrl.EnsureVisible(hTopItem);
}

void CSideProfileListDialog::OnEndlabeleditSideProfileTree(NMHDR* pNMHDR, LRESULT* pResult) 
{
	TV_DISPINFO* pTVDispInfo = (TV_DISPINFO*)pNMHDR;
	
	*pResult = 0;

	TVITEM tvItem;

	tvItem = pTVDispInfo->item;

	*pResult = 0;

	CSideProfile *pSideProfile = (CSideProfile *)m_ProfileTreeCtrl.GetItemData(tvItem.hItem);

	if (tvItem.pszText != NULL) {
		CString txt(tvItem.pszText);

		if (txt.FindOneOf(BAD_FILE_CHARACTERS) != -1) {
			CString temp;
			temp.Format("The following characters can not be part of the side profile name:\n%s",
				BAD_FILE_CHARACTERS);
			AfxMessageBox(temp);
			m_ProfileTreeCtrl.EditLabel(tvItem.hItem);
			return;
		}

		for (int i=0; i < m_SideProfileList.GetSize(); ++i) {
			if (m_SideProfileList[i]->m_SideProfileDBId == pSideProfile->m_SideProfileDBId)
				continue;

			if (m_SideProfileList[i]->m_Description.Compare(txt) == 0) {
				AfxMessageBox("Please enter a side profile name that does not already exist.");
				m_ProfileTreeCtrl.EditLabel(tvItem.hItem);
				return;
			}
		}
		*pResult = 1;
	}
	else {
		*pResult = 0;
		return;
	}

	try {
		CWaitCursor cwc;
		sideProfileDataService.UpdateSideProfileName(pSideProfile->m_SideProfileDBId, tvItem.pszText);
	}
	catch (...) {
		AfxMessageBox("Error updating side profile name.");
		strcpy(tvItem.pszText, pSideProfile->m_Description);
		*pResult = 0;
	}
	
	pSideProfile->m_Description = tvItem.pszText;

	return;
}

void CSideProfileListDialog::OnOK() 
{
	if (m_IsModeless)
		DestroyWindow();
	else
		CDialog::OnOK();
}

BOOL CSideProfileListDialog::IsTreeCtrlEditMessage(WPARAM keyCode)
{
	BOOL rc = FALSE;

	CWnd*  focus = GetFocus();
	CEdit* edit  = m_ProfileTreeCtrl.GetEditControl();
	if ((CEdit *)focus == edit) {
		edit->SendMessage(WM_KEYDOWN, keyCode);
		rc = TRUE;
	}

	return rc;
}

void CSideProfileListDialog::OnCancel() 
{
	if (!IsTreeCtrlEditMessage(VK_ESCAPE)) {
		if (m_IsModeless)
			DestroyWindow();
		else
			CDialog::OnCancel();
	}
}

void CSideProfileListDialog::OnContextMenu(CWnd* pWnd, CPoint point) 
{
	if (pWnd != &m_ProfileTreeCtrl)
		return;

	CMenu menu;
	menu.LoadMenu(IDR_GENERIC_MENU);

	CPoint pt(point);
	m_ProfileTreeCtrl.ScreenToClient(&pt);
	UINT nFlags;
	HTREEITEM hItem = m_ProfileTreeCtrl.HitTest(pt, &nFlags);
	if (hItem == NULL)
		return;
	else
		m_ProfileTreeCtrl.SelectItem(hItem);

	menu.GetSubMenu(0)->ModifyMenu(0, MF_BYPOSITION|MF_STRING, IDC_NEW, "&New");
	menu.GetSubMenu(0)->ModifyMenu(1, MF_BYPOSITION|MF_STRING, IDC_EDIT, "&Edit");
	menu.GetSubMenu(0)->ModifyMenu(2, MF_BYPOSITION|MF_STRING, IDC_COPY, "&Copy");
	menu.GetSubMenu(0)->AppendMenu(MF_BYPOSITION|MF_STRING, IDC_DELETE, "&Delete");


	menu.GetSubMenu(0)->TrackPopupMenu(TPM_LEFTALIGN|TPM_RIGHTBUTTON, point.x, point.y, this);	
}


void CSideProfileListDialog::PostNcDestroy() 
{

	if (m_IsModeless)
		delete this;

	CDialog::PostNcDestroy();
}

BOOL CSideProfileListDialog::OnHelpInfo(HELPINFO* pHelpInfo) 
{
	helpService.ShowFieldHelp(IDD, pHelpInfo->iCtrlId);

	return FALSE;

}

void CSideProfileListDialog::OnHelp() 
{
	helpService.ShowScreenHelp(IDD);

	return;

}

void CSideProfileListDialog::OnBayWizard() 
{
	EndDialog(WM_BAY_WIZARD);	
}

void CSideProfileListDialog::OnAisleWizard() 
{
	EndDialog(WM_AISLE_WIZARD);	
}

void CSideProfileListDialog::OnMainWizard() 
{
	EndDialog(WM_MAIN_WIZARD);	
}


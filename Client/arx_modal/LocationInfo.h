// LocationInfo.h: interface for the CLocationQueryInfo class.
//
//////////////////////////////////////////////////////////////////////

#if !defined(AFX_LOCATIONINFO_H__BB503D75_7D41_11D4_91D6_00400542E36B__INCLUDED_)
#define AFX_LOCATIONINFO_H__BB503D75_7D41_11D4_91D6_00400542E36B__INCLUDED_

#if _MSC_VER > 1000
#pragma once
#endif // _MSC_VER > 1000

class CLocationQueryInfo  
{
public:
	CLocationQueryInfo();
	virtual ~CLocationQueryInfo();
	CLocationQueryInfo& operator=(const CLocationQueryInfo & other);
	void Parse(CString line);

	int m_LocationDBID;
	CString m_LocationDescription;
	int m_BayProfileDBID;
	CString m_BayProfileDescription;
	int m_LevelType;
	int m_LocProductGroupDBID;
	CString m_LocPGDescription;
	int m_CurrentProductDBID;
	CString m_WMSProductID;
	CString m_WMSProductDetailID;
	CString m_ProductDescription;
	int m_ProdProductGroupDBID;
	CString m_ProdPGDescription;
	int m_CaseQuantity;
	double m_Width;
	double m_Depth;
	double m_Height;
	int m_HandlingMethod;
	int m_BayDBID;
	int m_LevelDBID;
	int m_IsPrimaryFacing;
	int m_RotateAllowed;



};

#endif // !defined(AFX_LOCATIONINFO_H__BB503D75_7D41_11D4_91D6_00400542E36B__INCLUDED_)

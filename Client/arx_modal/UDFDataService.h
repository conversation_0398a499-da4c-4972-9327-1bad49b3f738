// UDFDataService.h: interface for the CUDFDataService class.
//
//////////////////////////////////////////////////////////////////////

#if !defined(AFX_UDFDATASERVICE_H__B6A6ACFA_1AD6_436F_A020_CBF280A11B81__INCLUDED_)
#define AFX_UDFDATASERVICE_H__B6A6ACFA_1AD6_436F_A020_CBF280A11B81__INCLUDED_

#if _MSC_VER > 1000
#pragma once
#endif // _MSC_VER > 1000

#include "UDF.h"
#include "qqhclasses.h"

class CUDFDataService  
{
public:
	CUDFDataService();
	virtual ~CUDFDataService();

	int GetUDFList(int elementType, long parentID, CStringArray &udfList);
	int StoreUDF(CUDF *pUDF);
	void AddUDFToTree(CUDF *pUDF);
	void UpdateUDFInTree(CUDF *pUDF);
	void UpdateObjectUDF(CUDF *pUDF, qqhSLOTObject &element);
	void UpdateTreeElementUDF(qqhSLOTObject &element, CUDF *pUDF);
	CString GetUDFPrefix(int elementType);
	CString GetUDFElementTable(int elementType);
	void BuildUDFElementQuery(int elementType, long parentID, CString &elementQuery);
	int DeleteUDF(long elementType, long listID);
	void DeleteUDFFromTree(long elementType, long listID);
	void DeleteObjectUDF(qqhSLOTObject &element, long listID);
};

#endif // !defined(AFX_UDFDATASERVICE_H__B6A6ACFA_1AD6_436F_A020_CBF280A11B81__INCLUDED_)

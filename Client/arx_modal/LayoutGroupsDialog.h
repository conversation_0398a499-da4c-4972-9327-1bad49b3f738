#if !defined(AFX_LAYOUTGROUPSDIALOG_H__68004149_60B5_11D4_9198_00400542E36B__INCLUDED_)
#define AFX_LAYOUTGROUPSDIALOG_H__68004149_60B5_11D4_9198_00400542E36B__INCLUDED_

#if _MSC_VER > 1000
#pragma once
#endif // _MSC_VER > 1000
// LayoutGroupsDialog.h : header file
//
#include "resource.h"
/////////////////////////////////////////////////////////////////////////////
// CLayoutGroupsDialog dialog

class CLayoutGroupsDialog : public CDialog
{
// Construction
public:
	BOOL m_Advanced;
	typedef enum {
		LayoutByPickPath = 1,
		LayoutByHotspot = 2
	} enumLayoutType;

	int m_Layout;
	int m_Contiguous;
	CLayoutGroupsDialog(CWnd* pParent = NULL);   // standard constructor

// Dialog Data
	//{{AFX_DATA(CLayoutGroupsDialog)
	enum { IDD = IDD_LAYOUT_GROUPS_DIALOG };
	CComboBox	m_LogModeCtrl;
	CString	m_LogMode;
	int		m_MaxResults;
	BOOL	m_IgnoreRankings;
	//}}AFX_DATA


// Overrides
	// ClassWizard generated virtual function overrides
	//{{AFX_VIRTUAL(CLayoutGroupsDialog)
	protected:
	virtual void DoDataExchange(CDataExchange* pDX);    // DDX/DDV support
	//}}AFX_VIRTUAL

// Implementation
protected:

	// Generated message map functions
	//{{AFX_MSG(CLayoutGroupsDialog)
	virtual BOOL OnInitDialog();
	afx_msg BOOL OnHelpInfo(HELPINFO* pHelpInfo);
	afx_msg void OnHelp();
	afx_msg void OnAdvanced();
	virtual void OnOK();
	afx_msg void OnContiguous();
	//}}AFX_MSG
	DECLARE_MESSAGE_MAP()
};

//{{AFX_INSERT_LOCATION}}
// Microsoft Visual C++ will insert additional declarations immediately before the previous line.

#endif // !defined(AFX_LAYOUTGROUPSDIALOG_H__68004149_60B5_11D4_9198_00400542E36B__INCLUDED_)

// SAXErrorHandlerImpl.h: interface for the SAXErrorHandlerImpl class.
//
//////////////////////////////////////////////////////////////////////

#if !defined(AFX_SAXERRORHANDLERIMPL_H__66A5CA47_EE8C_4966_AAAE_08EB7B4E5926__INCLUDED_)
#define AFX_SAXERRORHANDLERIMPL_H__66A5CA47_EE8C_4966_AAAE_08EB7B4E5926__INCLUDED_

#if _MSC_VER > 1000
#pragma once
#endif // _MSC_VER > 1000

#include "stdafx.h"

using namespace MSXML2;

class SAXErrorHandlerImpl : public ISAXErrorHandler  
{
public:
	SAXErrorHandlerImpl();
	virtual ~SAXErrorHandlerImpl();

    long __stdcall QueryInterface(const struct _GUID &,void ** );
    unsigned long __stdcall AddRef(void);
    unsigned long __stdcall Release(void);

	
	virtual HRESULT STDMETHODCALLTYPE error(
		/* [in] */ ISAXLocator * pLocator, 
		/* [in] */ wchar_t * pwchErrorMessage,
		/* [in] */ HRESULT hrErrorCode);
		
	virtual HRESULT STDMETHODCALLTYPE fatalError(
		/* [in] */ ISAXLocator * pLocator, 
		/* [in] */ wchar_t * pwchErrorMessage,
		/* [in] */ HRESULT hrErrorCode);
		
	virtual HRESULT STDMETHODCALLTYPE ignorableWarning(
		/* [in] */ ISAXLocator * pLocator, 
		/* [in] */ wchar_t * pwchErrorMessage,
		/* [in] */ HRESULT hrErrorCode);

private:
	ULONG m_refCnt;
};

#endif // !defined(AFX_SAXERRORHANDLERIMPL_H__66A5CA47_EE8C_4966_AAAE_08EB7B4E5926__INCLUDED_)

#if !defined(AFX_UDFMAINTENANCEDIALOG_H__007B5314_2F6A_11D5_9ED0_00C04FAC149C__INCLUDED_)
#define AFX_UDFMAINTENANCEDIALOG_H__007B5314_2F6A_11D5_9ED0_00C04FAC149C__INCLUDED_

#if _MSC_VER > 1000
#pragma once
#endif // _MSC_VER > 1000
// UDFMaintenanceDialog.h : header file
//
#include "UDF.h"
#include "UDFProperties.h"
#include "BayProfile.h"
/////////////////////////////////////////////////////////////////////////////
// CUDFMaintenanceDialog dialog

class CUDFMaintenanceDialog : public CDialog
{
// Construction
public:
	CUDFMaintenanceDialog(CWnd* pParent = NULL);   // standard constructor
	static UINT StoreUDFThread(LPVOID pParam);
	static UINT DeleteUDFThread(LPVOID pParam);
// Dialog Data
	//{{AFX_DATA(CUDFMaintenanceDialog)
	enum { IDD = IDD_UDF_MAINTENANCE };
	CStatic	m_BayProfileStaticCtrl;
	CComboBox	m_BayProfileListCtrl;
	CComboBox	m_UDFTypeCtrl;
	CListCtrl	m_UDFListCtrl;
	//}}AFX_DATA


// Overrides
	// ClassWizard generated virtual function overrides
	//{{AFX_VIRTUAL(CUDFMaintenanceDialog)
	protected:
	virtual void DoDataExchange(CDataExchange* pDX);    // DDX/DDV support
	//}}AFX_VIRTUAL

// Implementation
protected:

	// Generated message map functions
	//{{AFX_MSG(CUDFMaintenanceDialog)
	virtual BOOL OnInitDialog();
	virtual void OnOK();
	virtual void OnCancel();
	afx_msg void OnHelp();
	afx_msg void OnAddUdf();
	afx_msg void OnDeleteUdf();
	afx_msg void OnModifyUdf();
	afx_msg void OnSelchangeUdfType();
	afx_msg void OnEndlabeleditUdfList(NMHDR* pNMHDR, LRESULT* pResult);
	afx_msg void OnSelchangeBayProfileList();
	afx_msg void OnContextMenu(CWnd* pWnd, CPoint point);
	afx_msg void OnDblclkUdfList(NMHDR* pNMHDR, LRESULT* pResult);
	afx_msg BOOL OnHelpInfo(HELPINFO* pHelpInfo);
	//}}AFX_MSG
	DECLARE_MESSAGE_MAP()

private:
	void DeleteUDFFromList(int idx);
	int LoadUDFs(int elementType, int parentID);
	int LoadBayProfiles();
	void AddUDFToList(CUDF *pUDF, int idx);
	CTypedPtrArray<CObArray, CUDF*> m_UDFList;
	int m_PreviousUDFType;
	DWORD m_PreviousBayProfile;
	CTypedPtrArray<CObArray, CBayProfile*> m_BayProfileList;
};

//{{AFX_INSERT_LOCATION}}
// Microsoft Visual C++ will insert additional declarations immediately before the previous line.

#endif // !defined(AFX_UDFMAINTENANCEDIALOG_H__007B5314_2F6A_11D5_9ED0_00C04FAC149C__INCLUDED_)

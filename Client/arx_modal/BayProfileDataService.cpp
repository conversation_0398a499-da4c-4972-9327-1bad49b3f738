// BayProfileDataService.cpp: implementation of the CBayProfileDataService class.
//
//////////////////////////////////////////////////////////////////////

#include "stdafx.h"
#include "DataAccessService.h"
#include "modal.h"
#include "BayProfileDataService.h"
#include "ForteService.h"
#include "UtilityHelper.h"
#include "ControlService.h"

#ifdef _DEBUG
#undef THIS_FILE
static char THIS_FILE[]=__FILE__;
#define new DEBUG_NEW
#endif

extern CForteService forteService;
extern CDataAccessService dataAccessService;
extern CUtilityHelper utilityHelper;
extern CControlService controlService;
//////////////////////////////////////////////////////////////////////
// Construction/Destruction
//////////////////////////////////////////////////////////////////////

CBayProfileDataService::CBayProfileDataService()
{

}

CBayProfileDataService::~CBayProfileDataService()
{

}

int CBayProfileDataService::GetBayProfileNameList(CStringArray &bayNameList) 
{

	CString sql;

	sql.Format("select dbbayprofileid, description, baytype "
		"from dbbayprofile "
		"order by description");

	return dataAccessService.ExecuteQuery("GetBayProfileNameList", sql, bayNameList);

}

int CBayProfileDataService::GetBayProfileList(CStringArray &bayNameList) 
{

	CString sql;

	sql.Format("select unique dbbayprofile.dbbayprofileid, dbbayprofile.description, "
		"baytype, excludefromopt, 1 "
		"from dbbayprofile, dbbay "
		"where dbbayprofile.dbbayprofileid = dbbay.dbbayprofileid "
		"union "
		"select dbbayprofileid, description, baytype, excludefromopt, 0 "
		"from dbbayprofile "
		"where not exists ( select dbbayprofileid from dbbay "
		"where dbbay.dbbayprofileid = dbbayprofile.dbbayprofileid) "
		"order by 2");

	return dataAccessService.ExecuteQuery("GetBayProfileList", sql, bayNameList);

}


int CBayProfileDataService::GetBayProfiles(CStringArray &profileList)
{
	CString sql;

	sql.Format("select bp.dbbayprofileid, bp.description, bp.baytype "
		"from dbbayprofile bp "
		"order by bp.description");

	return dataAccessService.ExecuteQuery("GetBayProfiles", sql, profileList);

}

int CBayProfileDataService::StoreBayProfile(CBayProfile &bayProfile)
{
	bayProfile.ResetLevels(FALSE);

	int rc;
	try {
	if (bayProfile.m_BayProfileDBId <= 0)
		rc = InsertBayProfile(bayProfile);
	else
		rc = UpdateBayProfile(bayProfile);
	}
	catch (...) {
		bayProfile.ResetLevels(TRUE);
		throw;
	}

	bayProfile.ResetLevels(TRUE);

	return rc;
}

int CBayProfileDataService::UpdateBayProfile(CBayProfile &bayProfile)
{
	CStringArray stmts;
	
	GetBayProfileDeleteStmts(bayProfile, stmts);

	GetBayProfileUpdateStmts(bayProfile, stmts);

	return dataAccessService.ExecuteStatements("UpdateBayProfile", stmts);
}

int CBayProfileDataService::InsertBayProfile(CBayProfile &bayProfile)
{
	CStringArray stmts;

	GetBayProfileInsertStmts(bayProfile, stmts);

	return dataAccessService.ExecuteStatements("InsertBayProfile", stmts);
}

int CBayProfileDataService::DeleteBayProfile(int bayProfileDBId)
{
	CStringArray stmts;

	GetBayProfileDeleteStmts(bayProfileDBId, stmts);

	return dataAccessService.ExecuteStatements("DeleteBayProfile", stmts);

}

int CBayProfileDataService::GetBayProfileDeleteStmts(int bayProfileDBId, CStringArray &stmts)
{

	CString sql;

	sql.Format("delete from dblevellaborprof "
		"where dblevelprofileid in ( select dblevelprofileid "
		"from dblevelprofile where dbbayprofileid = %d)", bayProfileDBId);
	stmts.Add(sql);

	sql.Format("delete from dblocationprof "
		"where dblevelprofileid in ( select dblevelprofileid "
		"from dblevelprofile where dbbayprofileid = %d)", bayProfileDBId);
	stmts.Add(sql);

	sql.Format("delete from dblevelprofileinfo "
		"where dblevelprofileid in ( select dblevelprofileid "
		"from dblevelprofile where dbbayprofileid = %d)", bayProfileDBId);
	stmts.Add(sql);

	sql.Format("delete from dblevelprofile "
		"where dbbayprofileid = %d", bayProfileDBId);
	stmts.Add(sql);

	sql.Format("delete from dbpass1resideal "
		"where dbbayprofileid = %d", bayProfileDBId);
	stmts.Add(sql);

	sql.Format("delete from dbpass1rackusage "
		"where dbbayprofileid = %d", bayProfileDBId);
	stmts.Add(sql);

	sql.Format("delete from dbpass1rejection "
		"where dbbayprofileid = %d", bayProfileDBId);
	stmts.Add(sql);

	sql.Format("delete from dbpass1resavail "
		"where dbbayprofileid = %d", bayProfileDBId);
	stmts.Add(sql);

	sql.Format("delete from dbbaydrawing "
		"where dbbayprofileid = %d", bayProfileDBId);
	stmts.Add(sql);

	sql.Format("delete from dbfacinginfo "
		"where dbbayruleid in ( select dbbayruleid "
		"from dbbayrule where dbbayprofileid = %d)", bayProfileDBId);
	stmts.Add(sql);

	sql.Format("delete from dbbayrule "
		"where dbbayprofileid = %d", bayProfileDBId);
	stmts.Add(sql);
	
	sql.Format("delete from dbbayprofile "
		"where dbbayprofileid = %d", bayProfileDBId);
	stmts.Add(sql);

	return 0;
}


int CBayProfileDataService::GetBayProfileDeleteStmts(CBayProfile& bayProfile, CStringArray &stmts)
{

	CString sql;

	CString locIds, laborIds, levelIds, infoIds, ruleIds, facingIds;
	int i,j;

	for (i=0; i < bayProfile.m_LevelProfileList.GetSize(); ++i) {
		CLevelProfile *pLevel = bayProfile.m_LevelProfileList[i];
		if (pLevel->m_LevelProfileDBId > 0) {
			CString temp;
			temp.Format("%d,", pLevel->m_LevelProfileDBId);
			levelIds += temp;
		}

		for (j=0; j < pLevel->m_LocationProfileList.GetSize(); ++j) {
			if (pLevel->m_LocationProfileList[j]->m_LocationProfileDBId > 0) {
				CString temp;
				temp.Format("%d,", pLevel->m_LocationProfileList[j]->m_LocationProfileDBId);
				locIds += temp;
			}
		}

		for (j=0; j < pLevel->m_LevelLaborProfileList.GetSize(); ++j) {
			if (pLevel->m_LevelLaborProfileList[j]->m_LevelLaborProfileDBId > 0) {
				CString temp;
				temp.Format("%d,", pLevel->m_LevelLaborProfileList[j]->m_LevelLaborProfileDBId);
				laborIds += temp;
			}
		}

		for (j=0; j < pLevel->m_ExternalInfoList.GetSize(); ++j) {
			if (pLevel->m_ExternalInfoList[j]->m_LevelProfileInfoDBId > 0) {
				CString temp;
				temp.Format("%d,", pLevel->m_ExternalInfoList[j]->m_LevelProfileInfoDBId);
				infoIds += temp;
			}
		}
	}

	for (i=0; i < bayProfile.m_BayRuleList.GetSize(); ++i) {
		CBayRule *pRule = bayProfile.m_BayRuleList[i];
		if (pRule->m_BayRuleDBId > 0) {
			CString temp;
			temp.Format("%d,", pRule->m_BayRuleDBId);
			ruleIds += temp;
		}

		for (j=0; j < pRule->m_FacingInfoList.GetSize(); ++j) {
			if (pRule->m_FacingInfoList[j]->m_FacingInfoDBId > 0) {
				CString temp;
				temp.Format("%d,", pRule->m_FacingInfoList[j]->m_FacingInfoDBId);
				facingIds += temp;
			}
		}
	}

	levelIds.TrimRight(",");
	locIds.TrimRight(",");
	laborIds.TrimRight(",");
	infoIds.TrimRight(",");
	ruleIds.TrimRight(",");
	facingIds.TrimRight(",");

	if (laborIds != "") {
		sql.Format("delete from dblevellaborprof "
			"where dblevelprofileid in ( select dblevelprofileid "
			"from dblevelprofile where dbbayprofileid = %d) "
			"and dblevellaborprofid not in (%s)", bayProfile.m_BayProfileDBId, laborIds);
		stmts.Add(sql);
	}
	else {
		sql.Format("delete from dblevellaborprof "
			"where dblevelprofileid in ( select dblevelprofileid "
			"from dblevelprofile where dbbayprofileid = %d) ", bayProfile.m_BayProfileDBId);
		stmts.Add(sql);
	}
	
	if (locIds != "") {
		sql.Format("delete from dblocationprof "
			"where dblevelprofileid in ( select dblevelprofileid "
			"from dblevelprofile where dbbayprofileid = %d) "
			"and dblocationprofid not in (%s)", bayProfile.m_BayProfileDBId, locIds);
		stmts.Add(sql);
	}
	else {
		sql.Format("delete from dblocationprof "
			"where dblevelprofileid in ( select dblevelprofileid "
			"from dblevelprofile where dbbayprofileid = %d) ", bayProfile.m_BayProfileDBId);
		stmts.Add(sql);
	}
	
	if (infoIds != "") {
		sql.Format("delete from dblevelprofileinfo "
			"where dblevelprofileid in ( select dblevelprofileid "
			"from dblevelprofile where dbbayprofileid = %d) "
			"and dblevelprofileinfoid not in (%s)", bayProfile.m_BayProfileDBId, infoIds);
		stmts.Add(sql);
	}
	else {
		sql.Format("delete from dblevelprofileinfo "
			"where dblevelprofileid in ( select dblevelprofileid "
			"from dblevelprofile where dbbayprofileid = %d) ", bayProfile.m_BayProfileDBId);
		stmts.Add(sql);
	}
	
	if (levelIds != "") {
		sql.Format("delete from dblevelprofile "
			"where dbbayprofileid = %d "
			"and dblevelprofileid not in (%s)", bayProfile.m_BayProfileDBId, levelIds);
		stmts.Add(sql);
	}
	else {
		sql.Format("delete from dblevelprofile "
			"where dbbayprofileid = %d ", bayProfile.m_BayProfileDBId);
		stmts.Add(sql);
	}
	
	if (facingIds != "") {
		sql.Format("delete from dbfacinginfo "
			"where dbbayruleid in ( select dbbayruleid "
			"from dbbayrule where dbbayprofileid = %d) "
			"and dbfacinginfoid not in (%s)", bayProfile.m_BayProfileDBId, facingIds);
		stmts.Add(sql);
	}
	else {
		sql.Format("delete from dbfacinginfo "
			"where dbbayruleid in ( select dbbayruleid "
			"from dbbayrule where dbbayprofileid = %d) ", bayProfile.m_BayProfileDBId);
		stmts.Add(sql);
	}
	
	if (ruleIds != "") {
		sql.Format("delete from dbbayrule "
			"where dbbayprofileid = %d "
			"and dbbayruleid not in (%s)", bayProfile.m_BayProfileDBId, ruleIds);
		stmts.Add(sql);
	}
	else {
		sql.Format("delete from dbbayrule "
			"where dbbayprofileid = %d ", bayProfile.m_BayProfileDBId);
		stmts.Add(sql);
	}

	return 0;
}


int CBayProfileDataService::GetBayProfileInsertStmts(CBayProfile &bayProfile, CStringArray &stmts)
{
	CString sql;
	int i, j;

	// Reuse dbids when possible
	if (bayProfile.m_BayProfileDBId <= 0)
		bayProfile.m_BayProfileDBId = dataAccessService.GetNextKey("DBBayProfile", 1);

	sql.Format("insert into dbbayprofile "
		"(DBBayProfileId, Description, XCoordinate, YCoordinate, ZCoordinate, "
		"Width, Depth, Height, BayType, PalletHeight, PalletDepth, "
		"FlowDifference, MaximumWeight, RackCost, BarWidth, IsHazardRack, "
		"AllowBaySpanning, ExcludeFromOpt, IsFloating, UprightHeight, PalletSpace, "
		"createdate, changedate, lastuserid) "
		"values "
		"(%d, '%s', %d, %d, %d, "
		"%f, %f, %f, %d, %d, %d, "
		"%d, %f, %f, %f, %d, "
		"%d, %d, %d, %f, %f, SYSDATE, SYSDATE, 1) ",
		bayProfile.m_BayProfileDBId, bayProfile.m_Description,
		(int)bayProfile.m_Coordinates.m_X,
		(int)bayProfile.m_Coordinates.m_Y,
		(int)bayProfile.m_Coordinates.m_Z,
		bayProfile.m_Width,
		bayProfile.m_Depth,
		bayProfile.m_Height,
		bayProfile.m_BayType,
		bayProfile.m_PalletHeight,
		bayProfile.m_PalletDepth,
		bayProfile.m_FlowDifference,
		bayProfile.m_WeightCapacity,
		bayProfile.m_RackCost,
		bayProfile.m_UprightWidth,
		bayProfile.m_IsHazard,
		bayProfile.m_AllowBaySpanning,
		bayProfile.m_ExcludeFromOptimization,
		bayProfile.m_IsFloating,
		bayProfile.m_UprightHeight,
		bayProfile.m_PalletSpace);

	stmts.Add(sql);

	// BayRule, FacingInfo
	int bayRuleCount = 0;
	int facingCount = 0;
	for ( i=0; i < bayProfile.m_BayRuleList.GetSize(); ++i) {
		if (bayProfile.m_BayRuleList[i]->m_BayRuleDBId <= 0)
			bayRuleCount++;

		for (int j=0; j < bayProfile.m_BayRuleList[i]->m_FacingInfoList.GetSize(); ++j) {
			if (bayProfile.m_BayRuleList[i]->m_FacingInfoList[j]->m_FacingInfoDBId <= 0)
				facingCount++;
		}
	}

	int nextBayRuleKey = dataAccessService.GetNextKey("DBBayRule", bayRuleCount);
	int nextFacingInfoKey = dataAccessService.GetNextKey("DBFacingInfo", facingCount);

	for (i=0; i < bayProfile.m_BayRuleList.GetSize(); ++i) {
		CBayRule *pBayRule = bayProfile.m_BayRuleList[i];
		pBayRule->m_BayProfileDBId = bayProfile.m_BayProfileDBId;
		if (pBayRule->m_BayRuleDBId <= 0)
			pBayRule->m_BayRuleDBId = nextBayRuleKey++;

		sql.Format("insert into dbbayrule (DBBayRuleId, Description, PalletHeight, PctUtilSelPos, "
			"PctUtilRsvPos, PctRsvToSelPos, DesiredRplnPerWeek, "
			"Clearance, AdditionalRsvCube, Baytype, "
			"PercentReserves, DBBayProfileId, CreateDate, ChangeDate, LastUserId) "
			"values "
			"(%d, '%s', %f, %f, %f, %f, %f, %f, %f, %d, %f, %d, SYSDATE, SYSDATE, 1)",
			pBayRule->m_BayRuleDBId, pBayRule->m_Description, pBayRule->m_PalletHeight,
			pBayRule->m_PctUtilSelPos, pBayRule->m_PctUtilRsvPos, pBayRule->m_PctRsvToSelPos,
			pBayRule->m_DesiredRplnPerWeek,	pBayRule->m_Clearance, pBayRule->m_AdditionalRsvCube,
			pBayRule->m_Baytype, pBayRule->m_PercentReserves, pBayRule->m_BayProfileDBId);
		stmts.Add(sql);
		
		for  (int j=0; j < pBayRule->m_FacingInfoList.GetSize(); ++j) {
			CFacingInfo *pFacingInfo = pBayRule->m_FacingInfoList[j];
			pFacingInfo->m_BayRuleDBId = pBayRule->m_BayRuleDBId;
			if (pFacingInfo->m_FacingInfoDBId <= 0)
				pFacingInfo->m_FacingInfoDBId = nextFacingInfoKey++;

			sql.Format("insert into dbfacinginfo ( "
				"DBFacingInfoId, Description, ExtendedCube, ExtendedBOH, "
				"FacingCount, DBBayRuleId, CreateDate, ChangeDate, LastUserId) "
				"values "
				"(%d, '%s', %f, %f, %d, %d, SYSDATE, SYSDATE, 1)",
				pFacingInfo->m_FacingInfoDBId, pFacingInfo->m_Description,
				pFacingInfo->m_ExtendedCube, pFacingInfo->m_ExtendedBOH,
				pFacingInfo->m_FacingCount, pFacingInfo->m_BayRuleDBId);
			stmts.Add(sql);

		}
	}

	// LevelProfile, LocationProf, LevelLaborProf
	int levelProfileCount = 0;
	int locProfileCount = 0;
	int laborProfileCount = 0;
	int infoCount = 0;

	for (i=0; i < bayProfile.m_LevelProfileList.GetSize(); ++i) {
		if (bayProfile.m_LevelProfileList[i]->m_LevelProfileDBId <= 0)
			levelProfileCount++;

		for (j=0; j < bayProfile.m_LevelProfileList[i]->m_LocationProfileList.GetSize(); ++j) {
			if (bayProfile.m_LevelProfileList[i]->m_LocationProfileList[j]->m_LocationProfileDBId <= 0)
				locProfileCount++;
		}
		for (j=0; j < bayProfile.m_LevelProfileList[i]->m_LevelLaborProfileList.GetSize(); ++j) {
			if (bayProfile.m_LevelProfileList[i]->m_LevelLaborProfileList[j]->m_LevelLaborProfileDBId <= 0)
				laborProfileCount++;
		}

		for (j=0; j < bayProfile.m_LevelProfileList[i]->m_ExternalInfoList.GetSize(); ++j) {
			if (bayProfile.m_LevelProfileList[i]->m_ExternalInfoList[j]->m_LevelProfileInfoDBId <= 0)
				infoCount++;
		}
	}

	int nextLevelProfileKey = dataAccessService.GetNextKey("DBLevelProfile", levelProfileCount);
	int nextLevelLaborKey = dataAccessService.GetNextKey("DBLevelLaborProf", laborProfileCount);
	int nextLocProfileKey = dataAccessService.GetNextKey("DBLocationProf", locProfileCount);
	int nextInfoKey = dataAccessService.GetNextKey("DBLevelProfileInfo", infoCount);

	for (i=0; i < bayProfile.m_LevelProfileList.GetSize(); ++i) {
		CLevelProfile *pLevelProfile = bayProfile.m_LevelProfileList[i];
		pLevelProfile->m_BayProfileDBId = bayProfile.m_BayProfileDBId;
		if (pLevelProfile->m_LevelProfileDBId <= 0)
			pLevelProfile->m_LevelProfileDBId = nextLevelProfileKey++;

		sql.Format("insert into DBLevelProfile "
			"(DBLevelProfileId, Description, XCoordinate, YCoordinate, ZCoordinate, "
			"RelativeLevel, MaximumWeight, Thickness, IsRotateAllowed, "
			"IsVarLocAllowed, ForkFixedInsertion, IsBarHidden, "
			"Overhang, MinimumLocWidth, ProductGap, ProductSnap, "
			"FacingGap, FacingSnap, Baytype, ForkFixedExtraction, "
			"SelectPositions, ReservePositions, SelectPositionHeight, "
			"ReservePositionHeight, "
			"StackWidth, StackDepth, "
			"BackfillCode, Clearance, MaximumCaseWeight, MaximumCaseCount, LocationRowCount, "
			"FlowDifference, DBBayProfileId, CreateDate, ChangeDate, LastUserId) "
			"values "
			"(%d, '%s', %d, %d, %d, "
			"%d, %f, %f, %d, "
			"%d, %f, %d, "
			"%f, %f, %f, %f, "
			"%f, %f, %d, %f, "
			"%d, %d, %f, "
			"%f, "
			"%f, %f, "
			"'%s', %f, %f, %d, %d, "
			"%f, %d, SYSDATE, SYSDATE, 1)",
			
			pLevelProfile->m_LevelProfileDBId, pLevelProfile->m_Description, (int)pLevelProfile->m_Coordinates.m_X,
			(int)pLevelProfile->m_Coordinates.m_Y, (int)pLevelProfile->m_Coordinates.m_Z,
			pLevelProfile->m_RelativeLevel, pLevelProfile->m_WeightCapacity, pLevelProfile->m_Thickness,
			pLevelProfile->m_IsRotateAllowed, pLevelProfile->m_IsVariableWidthAllowed,
			pLevelProfile->m_ForkFixedInsertion, pLevelProfile->m_IsBarHidden, pLevelProfile->m_Overhang,
			pLevelProfile->m_MinimumLocWidth, pLevelProfile->m_ProductGap, pLevelProfile->m_ProductSnap,
			pLevelProfile->m_FacingGap, pLevelProfile->m_FacingSnap, pLevelProfile->m_Baytype,
			pLevelProfile->m_ForkFixedExtraction, pLevelProfile->m_SelectPositions,
			pLevelProfile->m_ReservePositions, pLevelProfile->m_SelectPositionHeight,
			pLevelProfile->m_ReservePositionHeight, 
			pLevelProfile->m_StackWidth, pLevelProfile->m_StackDepth,
			pLevelProfile->m_BackfillCode,
			pLevelProfile->m_Clearance, 
			pLevelProfile->m_MaximumCaseWeight, pLevelProfile->m_MaximumCaseCount,
			pLevelProfile->m_LocationRowCount, pLevelProfile->m_FlowDifference, pLevelProfile->m_BayProfileDBId);
		stmts.Add(sql);

		for (int j=0; j < pLevelProfile->m_LocationProfileList.GetSize(); ++j) {
			CLocationProfile *pLocProfile = pLevelProfile->m_LocationProfileList[j];
			pLocProfile->m_LevelProfileDBId = pLevelProfile->m_LevelProfileDBId;
			if (pLocProfile->m_LocationProfileDBId <= 0)
				pLocProfile->m_LocationProfileDBId = nextLocProfileKey++;
			
			sql.Format("insert into DBLocationProf ("
				"DBLocationProfId, Description, HandlingMethod, IsSelect, "
				"XCoordinate, YCoordinate, ZCoordinate, "
				"Width, Depth, Height, LocationSpace, "
				"MaxWeight, DBLevelProfileId, CreateDate, ChangeDate, LastUserId) "
				"values "
				"(%d, '%s', %d, %d, "
				"%d, %d, %d, "
				"%f, %f, %f, %f, "
				"%f, %d, SYSDATE, SYSDATE, 1)",
				pLocProfile->m_LocationProfileDBId, pLocProfile->m_Description,
				pLocProfile->m_HandlingMethod, pLocProfile->m_IsSelect, 
				(int)pLocProfile->m_Coordinates.m_X, (int)pLocProfile->m_Coordinates.m_Y,
				(int)pLocProfile->m_Coordinates.m_Z,
				pLocProfile->m_Width, pLocProfile->m_Depth, pLocProfile->m_Height,
				pLocProfile->m_LocationSpace, pLocProfile->m_WeightCapacity, pLocProfile->m_LevelProfileDBId);

			stmts.Add(sql);

		}

		for (int j=0; j < pLevelProfile->m_LevelLaborProfileList.GetSize(); ++j) {
			CLevelLaborProfile *pLaborProfile = pLevelProfile->m_LevelLaborProfileList[j];
			pLaborProfile->m_LevelProfileDBId = pLevelProfile->m_LevelProfileDBId;
			if (pLaborProfile->m_LevelLaborProfileDBId <= 0)
				pLaborProfile->m_LevelLaborProfileDBId = nextLevelLaborKey++;

			sql.Format("insert into DBLevelLaborProf "
				"(DBLevelLaborProfId, Description, Cube, "
				"FixedFactor, VariableFactor, WorkType, DBLevelProfileId, "
				"CreateDate, ChangeDate, LastUserId) "
				"values "
				"(%d, '%s', %f, %f, %f, %d, %d, SYSDATE, SYSDATE, 1)",
				pLaborProfile->m_LevelLaborProfileDBId, pLaborProfile->m_Description,
				pLaborProfile->m_Cube, pLaborProfile->m_FixedFactor, pLaborProfile->m_VariableFactor, 
				pLaborProfile->m_WorkType, pLaborProfile->m_LevelProfileDBId);

			stmts.Add(sql);

		}

		for (int j=0; j < pLevelProfile->m_ExternalInfoList.GetSize(); ++j) {
			CLevelProfileExternalInfo *pInfo = pLevelProfile->m_ExternalInfoList[j];
			if (pInfo->m_Value == pInfo->m_DefaultValue)
				continue;
			pInfo->m_LevelProfileDBId = pLevelProfile->m_LevelProfileDBId;
			if (pInfo->m_LevelProfileInfoDBId <= 0)
				pInfo->m_LevelProfileInfoDBId = nextInfoKey++;

			sql.Format("insert into DBLevelProfileInfo "
				"(DBLevelProfileInfoId, Value, DBLevelProfileId, DBExternalInfoId) "
				"values (%d, '%s', %d, %d)",
				pInfo->m_LevelProfileInfoDBId, pInfo->m_Value,
				pInfo->m_LevelProfileDBId, pInfo->m_ExternalInfoDBId);
			stmts.Add(sql);
		}

	}

	return 0;
}


int CBayProfileDataService::GetBayProfileUpdateStmts(CBayProfile &bayProfile, CStringArray &stmts)
{
	CString sql;
	int i, j;

	// Reuse dbids when possible
	if (bayProfile.m_BayProfileDBId <= 0) {
		bayProfile.m_BayProfileDBId = dataAccessService.GetNextKey("DBBayProfile", 1);

		sql.Format("insert into dbbayprofile "
			"(DBBayProfileId, Description, XCoordinate, YCoordinate, ZCoordinate, "
			"Width, Depth, Height, BayType, PalletHeight, PalletDepth, "
			"FlowDifference, MaximumWeight, RackCost, BarWidth, IsHazardRack, "
			"AllowBaySpanning, ExcludeFromOpt, IsFloating, UprightHeight, PalletSpace, "
			"createdate, changedate, lastuserid) "
			"values "
			"(%d, '%s', %d, %d, %d, "
			"%f, %f, %f, %d, %d, %d, "
			"%d, %f, %f, %f, %d, "
			"%d, %d, %d, %f, %f, SYSDATE, SYSDATE, 1) ",
			bayProfile.m_BayProfileDBId, bayProfile.m_Description,
			(int)bayProfile.m_Coordinates.m_X,
			(int)bayProfile.m_Coordinates.m_Y,
			(int)bayProfile.m_Coordinates.m_Z,
			bayProfile.m_Width,
			bayProfile.m_Depth,
			bayProfile.m_Height,
			bayProfile.m_BayType,
			bayProfile.m_PalletHeight,
			bayProfile.m_PalletDepth,
			bayProfile.m_FlowDifference,
			bayProfile.m_WeightCapacity,
			bayProfile.m_RackCost,
			bayProfile.m_UprightWidth,
			bayProfile.m_IsHazard,
			bayProfile.m_AllowBaySpanning,
			bayProfile.m_ExcludeFromOptimization,
			bayProfile.m_IsFloating,
			bayProfile.m_UprightHeight,
			bayProfile.m_PalletSpace);
	}
	else {
		sql.Format("update dbbayprofile "
			"set Description = '%s', "
			"XCoordinate = %d, YCoordinate = %d, ZCoordinate = %d, "
			"Width = %f, Depth = %f, Height = %f, BayType = %d, "
			"PalletHeight = %d, PalletDepth = %d, "
			"FlowDifference = %d, MaximumWeight = %f, RackCost = %f, BarWidth = %f, "
			"IsHazardRack = %d, "
			"AllowBaySpanning = %d, ExcludeFromOpt = %d, IsFloating = %d, "
			"UprightHeight = %f, PalletSpace = %f, "
			"changedate = SYSDATE, lastuserid = 1 "
			"where dbbayprofileid = %d",
			bayProfile.m_Description,
			(int)bayProfile.m_Coordinates.m_X,
			(int)bayProfile.m_Coordinates.m_Y,
			(int)bayProfile.m_Coordinates.m_Z,
			bayProfile.m_Width,
			bayProfile.m_Depth,
			bayProfile.m_Height,
			bayProfile.m_BayType,
			bayProfile.m_PalletHeight,
			bayProfile.m_PalletDepth,
			bayProfile.m_FlowDifference,
			bayProfile.m_WeightCapacity,
			bayProfile.m_RackCost,
			bayProfile.m_UprightWidth,
			bayProfile.m_IsHazard,
			bayProfile.m_AllowBaySpanning,
			bayProfile.m_ExcludeFromOptimization,
			bayProfile.m_IsFloating,
			bayProfile.m_UprightHeight,
			bayProfile.m_PalletSpace, bayProfile.m_BayProfileDBId);
	}
	stmts.Add(sql);

	// BayRule, FacingInfo
	int bayRuleCount = 0;
	int facingCount = 0;
	for ( i=0; i < bayProfile.m_BayRuleList.GetSize(); ++i) {
		if (bayProfile.m_BayRuleList[i]->m_BayRuleDBId <= 0)
			bayRuleCount++;

		for (int j=0; j < bayProfile.m_BayRuleList[i]->m_FacingInfoList.GetSize(); ++j) {
			if (bayProfile.m_BayRuleList[i]->m_FacingInfoList[j]->m_FacingInfoDBId <= 0)
				facingCount++;
		}
	}

	int nextBayRuleKey = dataAccessService.GetNextKey("DBBayRule", bayRuleCount);
	int nextFacingInfoKey = dataAccessService.GetNextKey("DBFacingInfo", facingCount);

	for (i=0; i < bayProfile.m_BayRuleList.GetSize(); ++i) {
		CBayRule *pBayRule = bayProfile.m_BayRuleList[i];
		pBayRule->m_BayProfileDBId = bayProfile.m_BayProfileDBId;
		if (pBayRule->m_BayRuleDBId <= 0) {
			pBayRule->m_BayRuleDBId = nextBayRuleKey++;
			
			sql.Format("insert into dbbayrule (DBBayRuleId, Description, PalletHeight, PctUtilSelPos, "
				"PctUtilRsvPos, PctRsvToSelPos, DesiredRplnPerWeek, "
				"Clearance, AdditionalRsvCube, Baytype, "
				"PercentReserves, DBBayProfileId, CreateDate, ChangeDate, LastUserId) "
				"values "
				"(%d, '%s', %f, %f, %f, %f, %f, %f, %f, %d, %f, %d, SYSDATE, SYSDATE, 1)",
				pBayRule->m_BayRuleDBId, pBayRule->m_Description, pBayRule->m_PalletHeight,
				pBayRule->m_PctUtilSelPos, pBayRule->m_PctUtilRsvPos, pBayRule->m_PctRsvToSelPos,
				pBayRule->m_DesiredRplnPerWeek,	pBayRule->m_Clearance, pBayRule->m_AdditionalRsvCube,
				pBayRule->m_Baytype, pBayRule->m_PercentReserves, pBayRule->m_BayProfileDBId);
		}
		else {
			sql.Format("update dbbayrule set "
				"Description = '%s', PalletHeight = %f, PctUtilSelPos = %f, "
				"PctUtilRsvPos = %f, PctRsvToSelPos = %f, DesiredRplnPerWeek = %f, "
				"Clearance = %f, AdditionalRsvCube = %f, Baytype = %d, "
				"PercentReserves = %f, DBBayProfileId = %d, ChangeDate = SYSDATE, LastUserId = 1 "
				"where dbbayruleid = %d",
				pBayRule->m_Description, pBayRule->m_PalletHeight,
				pBayRule->m_PctUtilSelPos, pBayRule->m_PctUtilRsvPos, pBayRule->m_PctRsvToSelPos,
				pBayRule->m_DesiredRplnPerWeek,	pBayRule->m_Clearance, pBayRule->m_AdditionalRsvCube,
				pBayRule->m_Baytype, pBayRule->m_PercentReserves, pBayRule->m_BayProfileDBId,
				pBayRule->m_BayRuleDBId);			
		}
		stmts.Add(sql);
		
		for  (int j=0; j < pBayRule->m_FacingInfoList.GetSize(); ++j) {
			CFacingInfo *pFacingInfo = pBayRule->m_FacingInfoList[j];
			pFacingInfo->m_BayRuleDBId = pBayRule->m_BayRuleDBId;
			if (pFacingInfo->m_FacingInfoDBId <= 0) {
				pFacingInfo->m_FacingInfoDBId = nextFacingInfoKey++;
				
				sql.Format("insert into dbfacinginfo ( "
					"DBFacingInfoId, Description, ExtendedCube, ExtendedBOH, "
					"FacingCount, DBBayRuleId, CreateDate, ChangeDate, LastUserId) "
					"values "
					"(%d, '%s', %f, %f, %d, %d, SYSDATE, SYSDATE, 1)",
					pFacingInfo->m_FacingInfoDBId, pFacingInfo->m_Description,
					pFacingInfo->m_ExtendedCube, pFacingInfo->m_ExtendedBOH,
					pFacingInfo->m_FacingCount, pFacingInfo->m_BayRuleDBId);
			}
			else {
				sql.Format("update dbfacinginfo set "
					"Description = '%s', ExtendedCube = %f, ExtendedBOH = %f, "
					"FacingCount = %d, DBBayRuleId = %d, ChangeDate = SYSDATE, LastUserId = 1 "
					"where dbfacinginfoid = %d",
					pFacingInfo->m_Description,
					pFacingInfo->m_ExtendedCube, pFacingInfo->m_ExtendedBOH,
					pFacingInfo->m_FacingCount, pFacingInfo->m_BayRuleDBId,
					pFacingInfo->m_FacingInfoDBId);				
			}

			stmts.Add(sql);

		}
	}

	// LevelProfile, LocationProf, LevelLaborProf
	int levelProfileCount = 0;
	int locProfileCount = 0;
	int laborProfileCount = 0;
	int infoCount = 0;

	for (i=0; i < bayProfile.m_LevelProfileList.GetSize(); ++i) {
		if (bayProfile.m_LevelProfileList[i]->m_LevelProfileDBId <= 0)
			levelProfileCount++;

		for (j=0; j < bayProfile.m_LevelProfileList[i]->m_LocationProfileList.GetSize(); ++j) {
			if (bayProfile.m_LevelProfileList[i]->m_LocationProfileList[j]->m_LocationProfileDBId <= 0)
				locProfileCount++;
		}
		for (j=0; j < bayProfile.m_LevelProfileList[i]->m_LevelLaborProfileList.GetSize(); ++j) {
			if (bayProfile.m_LevelProfileList[i]->m_LevelLaborProfileList[j]->m_LevelLaborProfileDBId <= 0)
				laborProfileCount++;
		}

		for (j=0; j < bayProfile.m_LevelProfileList[i]->m_ExternalInfoList.GetSize(); ++j) {
			if (bayProfile.m_LevelProfileList[i]->m_ExternalInfoList[j]->m_LevelProfileInfoDBId <= 0)
				infoCount++;
		}
	}

	int nextLevelProfileKey = dataAccessService.GetNextKey("DBLevelProfile", levelProfileCount);
	int nextLevelLaborKey = dataAccessService.GetNextKey("DBLevelLaborProf", laborProfileCount);
	int nextLocProfileKey = dataAccessService.GetNextKey("DBLocationProf", locProfileCount);
	int nextInfoKey = dataAccessService.GetNextKey("DBLevelProfileInfo", infoCount);

	for (i=0; i < bayProfile.m_LevelProfileList.GetSize(); ++i) {
		CLevelProfile *pLevelProfile = bayProfile.m_LevelProfileList[i];
		pLevelProfile->m_BayProfileDBId = bayProfile.m_BayProfileDBId;
		if (pLevelProfile->m_LevelProfileDBId <= 0) {
			pLevelProfile->m_LevelProfileDBId = nextLevelProfileKey++;

			sql.Format("insert into DBLevelProfile "
				"(DBLevelProfileId, Description, XCoordinate, YCoordinate, ZCoordinate, "
				"RelativeLevel, MaximumWeight, Thickness, IsRotateAllowed, "
				"IsVarLocAllowed, ForkFixedInsertion, IsBarHidden, "
				"Overhang, MinimumLocWidth, ProductGap, ProductSnap, "
				"FacingGap, FacingSnap, Baytype, ForkFixedExtraction, "
				"SelectPositions, ReservePositions, SelectPositionHeight, "
				"ReservePositionHeight, "
				"StackWidth, StackDepth, "
				"BackfillCode, Clearance, MaximumCaseWeight, MaximumCaseCount, LocationRowCount, "
				"FlowDifference, DBBayProfileId, CreateDate, ChangeDate, LastUserId) "
				"values "
				"(%d, '%s', %d, %d, %d, "
				"%d, %f, %f, %d, "
				"%d, %f, %d, "
				"%f, %f, %f, %f, "
				"%f, %f, %d, %f, "
				"%d, %d, %f, "
				"%f, "
				"%f, %f, "
				"'%s', %f, %f, %d, %d, "
				"%f, %d, SYSDATE, SYSDATE, 1)",
				
				pLevelProfile->m_LevelProfileDBId, pLevelProfile->m_Description, (int)pLevelProfile->m_Coordinates.m_X,
				(int)pLevelProfile->m_Coordinates.m_Y, (int)pLevelProfile->m_Coordinates.m_Z,
				pLevelProfile->m_RelativeLevel, pLevelProfile->m_WeightCapacity, pLevelProfile->m_Thickness,
				pLevelProfile->m_IsRotateAllowed, pLevelProfile->m_IsVariableWidthAllowed,
				pLevelProfile->m_ForkFixedInsertion, pLevelProfile->m_IsBarHidden, pLevelProfile->m_Overhang,
				pLevelProfile->m_MinimumLocWidth, pLevelProfile->m_ProductGap, pLevelProfile->m_ProductSnap,
				pLevelProfile->m_FacingGap, pLevelProfile->m_FacingSnap, pLevelProfile->m_Baytype,
				pLevelProfile->m_ForkFixedExtraction, pLevelProfile->m_SelectPositions,
				pLevelProfile->m_ReservePositions, pLevelProfile->m_SelectPositionHeight,
				pLevelProfile->m_ReservePositionHeight, 
				pLevelProfile->m_StackWidth, pLevelProfile->m_StackDepth,
				pLevelProfile->m_BackfillCode,
				pLevelProfile->m_Clearance, 
				pLevelProfile->m_MaximumCaseWeight, pLevelProfile->m_MaximumCaseCount,
				pLevelProfile->m_LocationRowCount, pLevelProfile->m_FlowDifference,
				pLevelProfile->m_BayProfileDBId);
		}
		else {
			sql.Format("update DBLevelProfile set "
				"Description = '%s', XCoordinate = %d, YCoordinate = %d, ZCoordinate = %d, "
				"RelativeLevel = %d, MaximumWeight = %f, Thickness = %f, IsRotateAllowed = %d, "
				"IsVarLocAllowed = %d, ForkFixedInsertion = %f, IsBarHidden = %d, "
				"Overhang = %f, MinimumLocWidth = %f, ProductGap = %f, ProductSnap = %f, "
				"FacingGap = %f, FacingSnap = %f, Baytype = %d, ForkFixedExtraction = %f, "
				"SelectPositions = %d, ReservePositions = %d, SelectPositionHeight = %f, "
				"ReservePositionHeight = %f, "
				"StackWidth = %f, StackDepth = %f, "
				"BackfillCode = '%s', Clearance = %f, MaximumCaseWeight = %f, MaximumCaseCount = %d, LocationRowCount = %d, "
				"FlowDifference = %f, DBBayProfileId = %d, ChangeDate = SYSDATE, LastUserId = 1 "
				"where dblevelprofileid = %d",
				pLevelProfile->m_Description, (int)pLevelProfile->m_Coordinates.m_X,
				(int)pLevelProfile->m_Coordinates.m_Y, (int)pLevelProfile->m_Coordinates.m_Z,
				pLevelProfile->m_RelativeLevel, pLevelProfile->m_WeightCapacity, pLevelProfile->m_Thickness,
				pLevelProfile->m_IsRotateAllowed, pLevelProfile->m_IsVariableWidthAllowed,
				pLevelProfile->m_ForkFixedInsertion, pLevelProfile->m_IsBarHidden, pLevelProfile->m_Overhang,
				pLevelProfile->m_MinimumLocWidth, pLevelProfile->m_ProductGap, pLevelProfile->m_ProductSnap,
				pLevelProfile->m_FacingGap, pLevelProfile->m_FacingSnap, pLevelProfile->m_Baytype,
				pLevelProfile->m_ForkFixedExtraction, pLevelProfile->m_SelectPositions,
				pLevelProfile->m_ReservePositions, pLevelProfile->m_SelectPositionHeight,
				pLevelProfile->m_ReservePositionHeight, 
				pLevelProfile->m_StackWidth, pLevelProfile->m_StackDepth,
				pLevelProfile->m_BackfillCode,
				pLevelProfile->m_Clearance, 
				pLevelProfile->m_MaximumCaseWeight, pLevelProfile->m_MaximumCaseCount,
				pLevelProfile->m_LocationRowCount, pLevelProfile->m_FlowDifference, 
				pLevelProfile->m_BayProfileDBId, pLevelProfile->m_LevelProfileDBId);
			
		}
		stmts.Add(sql);

		for (int j=0; j < pLevelProfile->m_LocationProfileList.GetSize(); ++j) {
			CLocationProfile *pLocProfile = pLevelProfile->m_LocationProfileList[j];
			pLocProfile->m_LevelProfileDBId = pLevelProfile->m_LevelProfileDBId;
			if (pLocProfile->m_LocationProfileDBId <= 0) {
				pLocProfile->m_LocationProfileDBId = nextLocProfileKey++;
			
				sql.Format("insert into DBLocationProf ("
					"DBLocationProfId, Description, HandlingMethod, IsSelect, "
					"XCoordinate, YCoordinate, ZCoordinate, "
					"Width, Depth, Height, LocationSpace, "
					"MaxWeight, DBLevelProfileId, CreateDate, ChangeDate, LastUserId) "
					"values "
					"(%d, '%s', %d, %d, "
					"%d, %d, %d, "
					"%f, %f, %f, %f, "
					"%f, %d, SYSDATE, SYSDATE, 1)",
					pLocProfile->m_LocationProfileDBId, pLocProfile->m_Description,
					pLocProfile->m_HandlingMethod, pLocProfile->m_IsSelect, 
					(int)pLocProfile->m_Coordinates.m_X, (int)pLocProfile->m_Coordinates.m_Y,
					(int)pLocProfile->m_Coordinates.m_Z,
					pLocProfile->m_Width, pLocProfile->m_Depth, pLocProfile->m_Height,
					pLocProfile->m_LocationSpace, pLocProfile->m_WeightCapacity, pLocProfile->m_LevelProfileDBId);
			}
			else {
				sql.Format("update DBLocationProf set "
					"Description = '%s', HandlingMethod = %d, IsSelect = %d, "
					"XCoordinate = %d, YCoordinate = %d, ZCoordinate = %d, "
					"Width = %f, Depth = %f, Height = %f, LocationSpace = %f, "
					"MaxWeight = %f, DBLevelProfileId = %d, ChangeDate = SYSDATE, LastUserId = 1 "
					"where dblocationprofid = %d",
					pLocProfile->m_Description,
					pLocProfile->m_HandlingMethod, pLocProfile->m_IsSelect, 
					(int)pLocProfile->m_Coordinates.m_X, (int)pLocProfile->m_Coordinates.m_Y,
					(int)pLocProfile->m_Coordinates.m_Z,
					pLocProfile->m_Width, pLocProfile->m_Depth, pLocProfile->m_Height,
					pLocProfile->m_LocationSpace, pLocProfile->m_WeightCapacity, 
					pLocProfile->m_LevelProfileDBId, pLocProfile->m_LocationProfileDBId);
			}
			stmts.Add(sql);

		}

		for (int j=0; j < pLevelProfile->m_LevelLaborProfileList.GetSize(); ++j) {
			CLevelLaborProfile *pLaborProfile = pLevelProfile->m_LevelLaborProfileList[j];
			pLaborProfile->m_LevelProfileDBId = pLevelProfile->m_LevelProfileDBId;
			if (pLaborProfile->m_LevelLaborProfileDBId <= 0) {
				pLaborProfile->m_LevelLaborProfileDBId = nextLevelLaborKey++;

				sql.Format("insert into DBLevelLaborProf "
					"(DBLevelLaborProfId, Description, Cube, "
					"FixedFactor, VariableFactor, WorkType, DBLevelProfileId, "
					"CreateDate, ChangeDate, LastUserId) "
					"values "
					"(%d, '%s', %f, %f, %f, %d, %d, SYSDATE, SYSDATE, 1)",
					pLaborProfile->m_LevelLaborProfileDBId, pLaborProfile->m_Description,
					pLaborProfile->m_Cube, pLaborProfile->m_FixedFactor, pLaborProfile->m_VariableFactor, 
					pLaborProfile->m_WorkType, pLaborProfile->m_LevelProfileDBId);
			}
			else {	
				sql.Format("update DBLevelLaborProf set "
					"Description = '%s', Cube = %f, "
					"FixedFactor = %f, VariableFactor = %f, WorkType = %d, DBLevelProfileId = %d, "
					"ChangeDate = SYSDATE, LastUserId = 1 "
					"where dblevellaborprofid = %d",
					pLaborProfile->m_Description,
					pLaborProfile->m_Cube, pLaborProfile->m_FixedFactor, pLaborProfile->m_VariableFactor, 
					pLaborProfile->m_WorkType, pLaborProfile->m_LevelProfileDBId,
					pLaborProfile->m_LevelLaborProfileDBId);
			}
			stmts.Add(sql);

		}

		int changed = 0;
		for (int j=0; j < pLevelProfile->m_ExternalInfoList.GetSize(); ++j) {
			CLevelProfileExternalInfo *pInfo = pLevelProfile->m_ExternalInfoList[j];
			if (pInfo->m_Value != pInfo->m_DefaultValue)
				changed = 1;
		}

		if (changed == 1) {
			for (int j=0; j < pLevelProfile->m_ExternalInfoList.GetSize(); ++j) {
				CLevelProfileExternalInfo *pInfo = pLevelProfile->m_ExternalInfoList[j];
				pInfo->m_LevelProfileDBId = pLevelProfile->m_LevelProfileDBId;
				if (pInfo->m_LevelProfileInfoDBId <= 0) {
					pInfo->m_LevelProfileInfoDBId = nextInfoKey++;
					
					sql.Format("insert into DBLevelProfileInfo "
						"(DBLevelProfileInfoId, Value, DBLevelProfileId, DBExternalInfoId) "
						"values (%d, '%s', %d, %d)",
						pInfo->m_LevelProfileInfoDBId, pInfo->m_Value,
						pInfo->m_LevelProfileDBId, pInfo->m_ExternalInfoDBId);
				}
				else {
					sql.Format("update DBLevelProfileInfo set "
						"Value = '%s', DBLevelProfileId = %d, DBExternalInfoId = %d "
						"where dblevelprofileinfoid = %d",
						pInfo->m_Value,
						pInfo->m_LevelProfileDBId, pInfo->m_ExternalInfoDBId,
						pInfo->m_LevelProfileInfoDBId);
				}
				stmts.Add(sql);
			}
		}

	}

	return 0;
}

int CBayProfileDataService::GetBayProfile(int bayProfileDBId, CBayProfile &bayProfile, int loadFlags /* = 0*/)
{
	CString sql;
	CStringArray results;
	int i;

	// Bay Profile
	sql.Format("select "
		"DBBayProfileId, Description, XCoordinate, YCoordinate, "
		"ZCoordinate, Width, Depth, Height, BayType, PalletHeight, "
		"PalletDepth, FlowDifference, MaximumWeight, RackCost, "
		"BarWidth, IsHazardRack, AllowBaySpanning, ExcludeFromOpt, "
		"IsFloating, UprightHeight, PalletSpace "
		"from dbbayprofile "
		"where dbbayprofileid = %d", bayProfileDBId);

	dataAccessService.ExecuteQuery("GetBayProfile", sql, results);

	if (results.GetSize() == 0)
		return -1;

	bayProfile.Parse(results[0]);
	
	for (i=0; i < bayProfile.m_LevelProfileList.GetSize(); ++i)
		delete bayProfile.m_LevelProfileList[i];

	bayProfile.m_LevelProfileList.RemoveAll();

	for (i=0; i < bayProfile.m_BayRuleList.GetSize(); ++i)
		delete bayProfile.m_BayRuleList[i];

	bayProfile.m_BayRuleList.RemoveAll();

	if (loadFlags & CBayProfile::loadLevels) {
		
		// Level Profiles
		sql.Format("select DBLevelProfileId, Description, XCoordinate, YCoordinate, "
			"ZCoordinate, RelativeLevel, MaximumWeight, Thickness, "
			"IsRotateAllowed, IsVarLocAllowed, ForkFixedInsertion, "
			"IsBarHidden, Overhang, MinimumLocWidth, ProductGap, ProductSnap, "
			"FacingGap, FacingSnap, Baytype, ForkFixedExtraction, SelectPositions, "
			"ReservePositions, SelectPositionHeight, ReservePositionHeight, "
			"StackWidth, StackDepth, BackfillCode, Clearance, DBBayProfileId, MaximumCaseWeight, "
			"MaximumCaseCount, LocationRowCount, FlowDifference "
			"from dblevelprofile where dbbayprofileid = %d "
			"order by zcoordinate", bayProfile.m_BayProfileDBId);
		
		results.RemoveAll();
		dataAccessService.ExecuteQuery("GetLevelProfile", sql, results);
		
		CTypedPtrMap<CMapWordToPtr, int, CLevelProfile*> levelMap;
		
		for (i=0; i < results.GetSize(); ++i) {
			CLevelProfile *pLevelProfile = new CLevelProfile;
			pLevelProfile->Parse(results[i]);
			bayProfile.m_LevelProfileList.Add(pLevelProfile);
			levelMap.SetAt(pLevelProfile->m_LevelProfileDBId, pLevelProfile);
		}
		
		
		if (loadFlags & CBayProfile::loadInfo) {
			
			sql.Format("select DBLevelProfileInfoId, Value, DBLevelProfileId, "
				"DBLevelProfileInfo.DBExternalInfoId, Name, DataType, Length, Sequence, "
				"DefaultValue, ListValues, DBExternalSystemId "
				"from DBLevelProfileInfo, DBExternalInfo "
				"where DBLevelProfileInfo.DBExternalInfoId = DBExternalInfo.DBExternalInfoId "
				"and DBLevelProfileInfo.DBLevelProfileId in ( select DBLevelProfileId "
				"from DBLevelProfile where DBBayProfileId = %d) "
				"union "
				
				"select 0, DefaultValue, 0, "
				"DBExternalInfoId, Name, DataType, Length, Sequence, "
				"DefaultValue, ListValues, DBExternalSystemId "
				"from DBExternalInfo "
				"where not exists "
				"( select dblevelprofileid "
				"from dblevelprofileinfo "
				"where dblevelprofileinfo.dbexternalinfoid = dbexternalinfo.dbexternalinfoid "
				"and dblevelprofileinfo.dblevelprofileid in ( select dblevelprofileid "
				"from dblevelprofile where dbbayprofileid = %d)) "
				"order by 1, 5", 
				bayProfile.m_BayProfileDBId, bayProfile.m_BayProfileDBId);
			
			
	//FILE *f;
	//f = fopen("c:\\temp\\sql.out", "w");
	//fprintf(f, "%s\n", sql);
	//fclose(f);

			results.RemoveAll();
			dataAccessService.ExecuteQuery("GetExternalInfo", sql, results);
			
			for (i=0; i < results.GetSize(); ++i) {
				CLevelProfileExternalInfo *pExternalInfo = new CLevelProfileExternalInfo;
				pExternalInfo->Parse(results[i]);
				
				if (pExternalInfo->m_LevelProfileDBId > 0) {
					CLevelProfile *pLevelProfile;
					if (levelMap.Lookup(pExternalInfo->m_LevelProfileDBId, pLevelProfile)) {
						pLevelProfile->m_ExternalInfoList.Add(pExternalInfo);
					}
				}
				else {
					// These must be ones that are the default so they are not stored on the level profile
					// Put the existing one on the first level and then create additional ones for each level
					bayProfile.m_LevelProfileList[0]->m_ExternalInfoList.Add(pExternalInfo);
					for (int j=1; j < bayProfile.m_LevelProfileList.GetSize(); ++j) {
						CLevelProfileExternalInfo *pExternalInfo = new CLevelProfileExternalInfo;
						pExternalInfo->Parse(results[i]);
						bayProfile.m_LevelProfileList[j]->m_ExternalInfoList.Add(pExternalInfo);
					}
				}
			}
			
		}

		if (loadFlags & CBayProfile::loadLocations) {
			
			// Location Profiles
			sql.Format("select DBLocationProfId, Description, HandlingMethod, IsSelect, "
				"XCoordinate, YCoordinate, ZCoordinate, Width, Depth, Height, "
				"LocationSpace, MaxWeight, DBLevelProfileId "
				"from dblocationprof "
				"where dblevelprofileid in ( select dblevelprofileid "
				"from dblevelprofile where dbbayprofileid = %d) "
				"order by dblevelprofileid, xcoordinate", bayProfile.m_BayProfileDBId);
			
			results.RemoveAll();
			dataAccessService.ExecuteQuery("GetLocationProfile", sql, results);
			
			for (i=0; i < results.GetSize(); ++i) {
				CLocationProfile *pLocProfile = new CLocationProfile;
				pLocProfile->Parse(results[i]);
				
				CLevelProfile *pLevelProfile;
				if (levelMap.Lookup(pLocProfile->m_LevelProfileDBId, pLevelProfile))
					pLevelProfile->m_LocationProfileList.Add(pLocProfile);
			}
		}
		
		if (loadFlags & CBayProfile::loadLabor) {
			
			// Level Labor Profiles
			sql.Format("select dblevellaborprofid, description, "
				"cube, fixedfactor, variablefactor, worktype, "
				"dblevelprofileid "
				"from dblevellaborprof "
				"where dblevelprofileid in ( select dblevelprofileid "
				"from dblevelprofile where dbbayprofileid = %d) "
				"order by dblevelprofileid, cube ", bayProfile.m_BayProfileDBId);
			
			results.RemoveAll();
			dataAccessService.ExecuteQuery("GetLevelLaborProfile", sql, results);
			
			for (i=0; i < results.GetSize(); ++i) {
				CLevelLaborProfile *pLaborProfile = new CLevelLaborProfile;
				pLaborProfile->Parse(results[i]);
				
				CLevelProfile *pLevelProfile;
				if (levelMap.Lookup(pLaborProfile->m_LevelProfileDBId, pLevelProfile))
					pLevelProfile->m_LevelLaborProfileList.Add(pLaborProfile);
			}
		}
	}


	// Bay Rule
	if (loadFlags & CBayProfile::loadRules) {
		
		sql.Format("select DBBayRuleId, Description, PalletHeight, PctUtilSelPos, "
			"PctUtilRsvPos, PctRsvToSelPos, DesiredRplnPerWeek, Clearance, "
			"AdditionalRsvCube, Baytype, PercentReserves, DBBayProfileId "
			"from dbbayrule "
			"where dbbayprofileid = %d", bayProfile.m_BayProfileDBId);
		
		results.RemoveAll();
		dataAccessService.ExecuteQuery("GetBayRule", sql, results);
		
		CTypedPtrMap<CMapWordToPtr, int, CBayRule*> ruleMap;
		
		for (i=0; i < results.GetSize(); ++i) {
			CBayRule *pBayRule = new CBayRule;
			pBayRule->Parse(results[i]);
			bayProfile.m_BayRuleList.Add(pBayRule);
			ruleMap.SetAt(pBayRule->m_BayRuleDBId, pBayRule);
		}
		
		sql.Format("select DBFacingInfoId, Description, "
			"ExtendedCube, ExtendedBOH, FacingCount, DBBayRuleId "
			"from DBFacingInfo "
			"where DBBayRuleId in ( select dbbayruleid from dbbayrule "
			"where dbbayprofileid = %d) "
			"order by DBBayRuleId, FacingCount", bayProfile.m_BayProfileDBId);
		
		results.RemoveAll();
		dataAccessService.ExecuteQuery("GetFacingInfo", sql, results);
		for (i=0; i < results.GetSize(); ++i) {
			CFacingInfo *pFacingInfo = new CFacingInfo;
			pFacingInfo->Parse(results[i]);
			CBayRule *pBayRule;
			if (ruleMap.Lookup(pFacingInfo->m_BayRuleDBId, pBayRule))
				pBayRule->m_FacingInfoList.Add(pFacingInfo);
		}
		
	}

	bayProfile.ResetLevels(TRUE);

	return 0;

}

int CBayProfileDataService::GetExternalAttributeList(int externalSystemId, CStringArray &results)
{
	CString sql;

	sql.Format("select DBExternalInfoId, Name, DataType, Length, Sequence, "
		"DefaultValue, ListValues, DBExternalSystemId "
		"from DBExternalInfo "
		"where DBExternalSystemId = %d "
		"order by name", externalSystemId);

	results.RemoveAll();
	return dataAccessService.ExecuteQuery("GetExternalAttributeList", sql, results);
}


int CBayProfileDataService::GetExternalSystemList(CStringArray &results)
{
	CString sql;
	sql.Format("select DBExternalSystemId, Vendor, DBExternalSystem.Name, "
		"Version, DBSystemType.DBSystemTypeId, DBSystemType.Name "
		"from DBExternalSystem, DBSystemType "
		"where DBExternalSystem.DBSystemTypeId = DBSystemType.DBSystemTypeId");

	results.RemoveAll();
	return dataAccessService.ExecuteQuery("GetExternalSystemList", sql, results);
}

int CBayProfileDataService::GetLevelProfileExternalAttributes(CLevelProfile &levelProfile)
{
	CString sql;
	CStringArray results;

	sql.Format("select 0, DefaultValue, 0, "
		"DBExternalInfoId, Name, DataType, Length, Sequence, "
		"DefaultValue, ListValues, DBExternalSystemId "
		"from DBExternalInfo "
		"order by name");

	results.RemoveAll();
	try {
		dataAccessService.ExecuteQuery("GetExternalInfo", sql, results);
	}
	catch (...) {
		AfxMessageBox("Error loading level external attributes.");
		return -1;
	}
	
	for (int i=0; i < results.GetSize(); ++i) {
		CLevelProfileExternalInfo *pExternalInfo = new CLevelProfileExternalInfo;
		pExternalInfo->Parse(results[i]);
		pExternalInfo->m_LevelProfileDBId = levelProfile.m_LevelProfileDBId;
		levelProfile.m_ExternalInfoList.Add(pExternalInfo);
	}

	return 0;
}

BOOL CBayProfileDataService::IsBayProfileNameInUse(int bayProfileDBId, const CString &name)
{
	CString sql;
	CStringArray results;

	sql.Format("select count(*) from dbbayprofile "
		"where description = '%s' "
		"and dbbayprofileid != %d", name, bayProfileDBId);
	
	dataAccessService.ExecuteQuery("IsBayProfileNameInuse", sql, results, TRUE);

	if (results.GetSize() > 0)
		return (atoi(results[0]) != 0);

	return FALSE;
}

BOOL CBayProfileDataService::IsBayProfileInUse(int bayProfileDBId)
{
	CString sql;
	CStringArray results;

	// Oracle-Specific
	sql.Format("select dbbayprofileid from dbbay "
		"where dbbayprofileid = %d and rownum = 1", bayProfileDBId);
	
	dataAccessService.ExecuteQuery("IsBayProfileInUse", sql, results, TRUE);

	return (results.GetSize() > 0);
}

int CBayProfileDataService::GetBayProfileUsedList(CStringArray &bayProfileList)
{
	CString sql;

	sql.Format("select unique dbbayprofileid from dbbay ");
	
	try {
		dataAccessService.ExecuteQuery("GetBayProfileUsedList", sql, bayProfileList);
	}
	catch (...) {
		utilityHelper.ProcessError("Error getting active bay profile list.");
		return -1;
	}

	return 0;
}

int CBayProfileDataService::UpdateBayProfileName(int bayProfileDBId, const CString &name)
{
	CString sql;

	sql.Format("Update dbbayprofile set description = '%s' "
		"where dbbayprofileid = %d", name, bayProfileDBId);

	return dataAccessService.ExecuteStatement("UpdateBayProfileName", sql);
}

int CBayProfileDataService::SetBayProfileToExcluded(int bayProfileDBId, int flag)
{
	CString sql;

	sql.Format("update dbbayprofile set excludefromopt = %d "
		"where dbbayprofileid = %d", flag, bayProfileDBId);

	return dataAccessService.ExecuteStatement("SetBayProfileToExcluded", sql);
}

int CBayProfileDataService::GetLevelProfile(int levelProfileDBId, CLevelProfile &levelProfile)
{
	CString sql;
	CStringArray results;
	
	sql.Format("select DBLevelProfileId, Description, XCoordinate, YCoordinate, "
		"ZCoordinate, RelativeLevel, MaximumWeight, Thickness, "
		"IsRotateAllowed, IsVarLocAllowed, ForkFixedInsertion, "
		"IsBarHidden, Overhang, MinimumLocWidth, ProductGap, ProductSnap, "
		"FacingGap, FacingSnap, Baytype, ForkFixedExtraction, SelectPositions, "
		"ReservePositions, SelectPositionHeight, ReservePositionHeight, "
		"StackWidth, StackDepth, BackfillCode, Clearance, DBBayProfileId, MaximumCaseWeight, "
		"MaximumCaseCount, LocationRowCount, FlowDifference "
		"from dblevelprofile where dblevelprofileid = %d "
		"order by zcoordinate", levelProfileDBId);
	
	results.RemoveAll();
	dataAccessService.ExecuteQuery("GetLevelProfile", sql, results);
	
	CTypedPtrMap<CMapWordToPtr, int, CLevelProfile*> levelMap;
	
	if (results.GetSize() == 0)
		return -1;
	
	levelProfile.Parse(results[0]);
	
	return 0;
}

int CBayProfileDataService::GetLocationProfile(int locProfileDBId, CLocationProfile &locProfile)
{
	CString sql;
	CStringArray results;
	
	sql.Format("select DBLocationProfId, Description, HandlingMethod, IsSelect, "
		"XCoordinate, YCoordinate, ZCoordinate, Width, Depth, Height, "
		"LocationSpace, MaxWeight, DBLevelProfileId "
		"from dblocationprof "
		"where dblocationprofid = %d ",locProfileDBId);
	
	results.RemoveAll();
	dataAccessService.ExecuteQuery("GetLocationProfile", sql, results);
	
	if (results.GetSize() == 0)
		return -1;
	
	locProfile.Parse(results[0]);
				
	return 0;
				
}

int CBayProfileDataService::GetLocationExternalInfo(qqhSLOTLocation &location)
{
	CString sql;
	CStringArray results;

	sql.Format("select DBLocationInfoId, Value, DBLocationId, "
		"DBLocationInfo.DBExternalInfoId, Name, DataType, Length, Sequence, "
		"DefaultValue, ListValues, DBExternalSystemId "
		"from DBLocationInfo, DBExternalInfo "
		"where DBLocationInfo.DBExternalInfoId = DBExternalInfo.DBExternalInfoId "
		"and DBLocationInfo.DBLocationId = %d "
		"union "

		"select 0, Value, 0, "
		"DBLevelProfileInfo.DBExternalInfoId, DBExternalInfo.Name, DBExternalInfo.DataType, "
		"DBExternalInfo.Length, DBExternalInfo.Sequence, "
		"DefaultValue, ListValues, DBExternalSystemId "
		"from DBLevelProfileInfo, DBExternalInfo, DBLocationProf "
		"where DBLevelProfileInfo.DBExternalInfoId = DBExternalInfo.DBExternalInfoId "
		"and DBLevelProfileInfo.DBLevelProfileID = DBLocationProf.DBLevelProfileID "
		"and DBLocationProf.DBLocationProfId = %d "
		"and not exists ( select dblocationid "
		"from dblocationinfo "
		"where dblocationinfo.dblocationid = %d "
		"and dblocationinfo.dbexternalinfoid = dbexternalinfo.dbexternalinfoid) "
		"union "
		
		"select 0, DefaultValue, 0, "
		"DBExternalInfoId, Name, DataType, Length, Sequence, "
		"DefaultValue, ListValues, DBExternalSystemId "
		"from DBExternalInfo \n"
		"where not exists ( select dblocationid "
		"from dblocationinfo "
		"where dblocationinfo.dblocationid = %d "
		"and dblocationinfo.dbexternalinfoid = dbexternalinfo.dbexternalinfoid) "
		"and not exists ( select DBLevelProfileInfoID "
		"from DBLevelProfileInfo "
		"where DBLevelProfileInfo.DBExternalInfoId = DBExternalInfo.DBExternalInfoId "
		"and DBLevelProfileID = %d ) " 
		"order by 5", 
		location.getDBID(), location.getLocationProfileId(),location.getDBID(),
		location.getDBID(),location.getLocationProfileId());
	
	//FILE *f;
	//f = fopen("c:\\temp\\sql.out", "w");
	//fprintf(f, "%s\n", sql);
	//fclose(f);

	if (dataAccessService.ExecuteQuery("GetLocationExternalInfo", sql, results) < 0)
		return -1;

	// the location may already have some overridden values on it but they
	// won't have the full info record (just the info id and the value)
	CMap<int, int, CLevelProfileExternalInfo*, CLevelProfileExternalInfo*> map;
	for (int i=0; i < location.m_InfoList.GetSize(); ++i) {
		map.SetAt(location.m_InfoList[i]->m_ExternalInfoDBId, location.m_InfoList[i]);
	}

	for (i=0; i < results.GetSize(); ++i) {
		CLevelProfileExternalInfo *pInfo = new CLevelProfileExternalInfo;
		pInfo->Parse(results[i]);
		CLevelProfileExternalInfo *pTempInfo;
		if (map.Lookup(pInfo->m_ExternalInfoDBId, pTempInfo)) {
			pInfo->m_Value = pTempInfo->m_Value;
			*pTempInfo = *pInfo;
			delete pInfo;
		}
		else
			location.m_InfoList.Add(pInfo);
	}

	return results.GetSize();
}


int CBayProfileDataService::GetSideProfileNamesByBayProfile(int bayProfileDBId, CStringArray &sideNames)
{
	CString sql;

	sql.Format("select unique sp.description "
		"from dbsideprofile sp, dbsidebayprof sbp "
		"where sbp.dbbayprofileid = %d "
		"and sbp.dbsideprofileid = sp.dbsideprofileid "
		"order by sp.description", bayProfileDBId);

	return dataAccessService.ExecuteQuery("GetSideProfileNamesByBayProfile", sql, sideNames, TRUE);
}

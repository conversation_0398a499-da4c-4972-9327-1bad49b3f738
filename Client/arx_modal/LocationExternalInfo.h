// LocationExternalInfo.h: interface for the CLocationExternalInfo class.
//
//////////////////////////////////////////////////////////////////////

#if !defined(AFX_LOCATIONEXTERNALINFO_H__C0EBED44_54A8_42DC_B9D8_F1A3FB0E334B__INCLUDED_)
#define AFX_LOCATIONEXTERNALINFO_H__C0EBED44_54A8_42DC_B9D8_F1A3FB0E334B__INCLUDED_

#if _MSC_VER > 1000
#pragma once
#endif // _MSC_VER > 1000

class CLocationExternalInfo : public CObject  
{
public:
	CLocationExternalInfo();
	CLocationExternalInfo(const CLocationExternalInfo& other);
	CLocationExternalInfo& operator=(const CLocationExternalInfo& other);

	virtual ~CLocationExternalInfo();
	
	int Parse(const CString &line);

	int m_LocationInfoDBId;		// primary key of the DBLocationInfo table
	CString m_Value;			// non-default value
	CString m_DefaultValue;		// the default value
	int m_LocationDBId;			// primary key of the location
	int m_ExternalInfoDBId;		// primary key of the external attribute
};

#endif // !defined(AFX_LOCATIONEXTERNALINFO_H__C0EBED44_54A8_42DC_B9D8_F1A3FB0E334B__INCLUDED_)

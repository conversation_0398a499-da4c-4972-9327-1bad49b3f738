// BayProfileBayAttributesPage.cpp : implementation file
//

#include "stdafx.h"
#include "modal.h"
#include "BayProfileBayAttributesPage.h"
#include "BayProfileSheet.h"
#include "BayProfileDataService.h"
#include "UtilityHelper.h"
#include "HelpService.h"

#ifdef _DEBUG
#define new DEBUG_NEW
#undef THIS_FILE
static char THIS_FILE[] = __FILE__;
#endif

extern CUtilityHelper utilityHelper;
extern CHelpService helpService;
/////////////////////////////////////////////////////////////////////////////
// CBayProfileBayAttributesPage property page

IMPLEMENT_DYNCREATE(CBayProfileBayAttributesPage, CPropertyPage)

CBayProfileBayAttributesPage::CBayProfileBayAttributesPage() : CPropertyPage(CBayProfileBayAttributesPage::IDD)
{
	//{{AFX_DATA_INIT(CBayProfileBayAttributesPage)
	m_BaySpanningAllowed = FALSE;
	m_Exclude = FALSE;
	m_Floating = FALSE;
	m_Hazard = FALSE;
	m_Name = _T("");
	m_RackCost = _T("");
	m_ActiveNote = _T("");
	//}}AFX_DATA_INIT
}

CBayProfileBayAttributesPage::~CBayProfileBayAttributesPage()
{
}

void CBayProfileBayAttributesPage::DoDataExchange(CDataExchange* pDX)
{
	CPropertyPage::DoDataExchange(pDX);
	//{{AFX_DATA_MAP(CBayProfileBayAttributesPage)
	DDX_Check(pDX, IDC_BAY_SPANNING_CHECKBOX, m_BaySpanningAllowed);
	DDX_Check(pDX, IDC_EXCLUDE_CHECKBOX, m_Exclude);
	DDX_Check(pDX, IDC_FLOATING_CHECKBOX, m_Floating);
	DDX_Check(pDX, IDC_HAZARD_CHECKBOX, m_Hazard);
	DDX_Text(pDX, IDC_NAME, m_Name);
	DDX_Text(pDX, IDC_RACK_COST, m_RackCost);
	DDX_Text(pDX, IDC_ACTIVE_STATIC, m_ActiveNote);
	//}}AFX_DATA_MAP
}


BEGIN_MESSAGE_MAP(CBayProfileBayAttributesPage, CPropertyPage)
	//{{AFX_MSG_MAP(CBayProfileBayAttributesPage)
	ON_EN_CHANGE(IDC_NAME, OnChangeName)
	ON_WM_HELPINFO()
	//}}AFX_MSG_MAP
END_MESSAGE_MAP()

/////////////////////////////////////////////////////////////////////////////
// CBayProfileBayAttributesPage message handlers

void CBayProfileBayAttributesPage::OnOK() 
{
	// TODO: Add your specialized code here and/or call the base class
	
	CPropertyPage::OnOK();
}

BOOL CBayProfileBayAttributesPage::OnInitDialog() 
{
	CPropertyPage::OnInitDialog();
	
	return TRUE;  // return TRUE unless you set the focus to a control
	              // EXCEPTION: OCX Property Pages should return FALSE
}

BOOL CBayProfileBayAttributesPage::OnKillActive() 
{
	UpdateData(TRUE);
	if (! Validate())
		return 0;

	m_pBayProfile->m_Description = m_Name;
	m_pBayProfile->m_AllowBaySpanning = m_BaySpanningAllowed;
	m_pBayProfile->m_ExcludeFromOptimization = m_Exclude;
	m_pBayProfile->m_IsFloating = m_Floating;
	m_pBayProfile->m_IsHazard = m_Hazard;
	m_pBayProfile->m_RackCost = atof(m_RackCost);

	return CPropertyPage::OnKillActive();
}

BOOL CBayProfileBayAttributesPage::Validate()
{
	
	if ( m_Name.FindOneOf(BAD_FILE_CHARACTERS) != -1 ) {
		CString temp;
		temp.Format("The following characters are not allowed in the bay profile name: \n%s",
			BAD_FILE_CHARACTERS);
		AfxMessageBox(temp);
		utilityHelper.SetEditControlErrorState(this, IDC_NAME);
		return FALSE;
	}

	if (! utilityHelper.IsFloat(m_RackCost)) {
		AfxMessageBox("Please enter a valid decimal number for Rack Cost.");
		return utilityHelper.SetEditControlErrorState(this, IDC_RACK_COST);
	}
	try {
		CBayProfileDataService service;
		if (service.IsBayProfileNameInUse(m_pBayProfile->m_BayProfileDBId, m_Name)) {
			AfxMessageBox("The bay profile name is already in use. Please enter a unique name "
				"for the bay profile.");
			return utilityHelper.SetEditControlErrorState(this, IDC_NAME);
		}
	}
	catch (...) {
		utilityHelper.ProcessError("Error determining if profile name is in use.");
		return FALSE;
	}

	return TRUE;
}

void CBayProfileBayAttributesPage::OnChangeName() 
{
	UpdateData(TRUE);

	CString bayTypeStr;
	CBayProfile::ConvertBayType(m_pBayProfile->m_BayType, bayTypeStr);
	CString temp;
	temp.Format("Bay Profile Maintenance - [%s\\%s] [%s]",
		bayTypeStr, m_Name, (m_pBayProfile->m_Active) ? "Active" : "Inactive");
	
	CBayProfileSheet *pSheet = (CBayProfileSheet *)GetParent();
	pSheet->SetTitle(temp);
}

BOOL CBayProfileBayAttributesPage::OnSetActive() 
{
	CBayProfileSheet *pSheet = (CBayProfileSheet *)GetParent();
	m_pBayProfile = pSheet->m_pBayProfile;

	m_Name = m_pBayProfile->m_Description;
	
	if (m_pBayProfile->m_BayProfileDBId > 0) {
		m_BaySpanningAllowed = m_pBayProfile->m_AllowBaySpanning;
		m_Exclude = m_pBayProfile->m_ExcludeFromOptimization;
		m_Floating = m_pBayProfile->m_IsFloating;
		m_Hazard = m_pBayProfile->m_IsHazard;
		m_RackCost.Format("%.2f",  m_pBayProfile->m_RackCost);
	}
	
	if (m_pBayProfile->m_Active) {
		m_ActiveNote.Format("This bay profile is currently active in a facility. "
			"All fields that affect the physical characteristics of the bay, levels, "
			"or locations will be disabled.  To change the physical attributes of an "
			"existing bay, you must copy the bay profile, make the appropriate changes, "
			"and use the Change Bay Profile function to associate the bay with the new bay profile.");
		GetDlgItem(IDC_ACTIVE_STATIC)->ShowWindow(SW_SHOW);
		GetDlgItem(IDC_NOTE_BOX)->ShowWindow(SW_SHOW);
	}
	else {
		GetDlgItem(IDC_ACTIVE_STATIC)->ShowWindow(SW_HIDE);
		GetDlgItem(IDC_NOTE_BOX)->ShowWindow(SW_HIDE);
	}

	UpdateData(FALSE);

	return CPropertyPage::OnSetActive();
}

BOOL CBayProfileBayAttributesPage::OnHelpInfo(HELPINFO* pHelpInfo) 
{
	helpService.ShowFieldHelp(IDD, pHelpInfo->iCtrlId);
	return FALSE;

}

BOOL CBayProfileBayAttributesPage::OnNotify(WPARAM wParam, LPARAM lParam, LRESULT* pResult) 
{
	NMHDR* pNMHDR = (NMHDR*)lParam;

	if (pNMHDR->code == PSN_HELP) {
		helpService.ShowScreenHelp(IDD);
		return TRUE;
	}
	
	return CPropertyPage::OnNotify(wParam, lParam, pResult);
}

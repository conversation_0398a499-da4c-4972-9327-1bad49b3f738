// BayProfileDriveInPage.cpp : implementation file
//

#include "stdafx.h"
#include "modal.h"
#include "BayProfileDriveInPage.h"
#include "BayProfileSheet.h"
#include "UtilityHelper.h"
#include "Constants.h"
#include "HelpService.h"

#ifdef _DEBUG
#define new DEBUG_NEW
#undef THIS_FILE
static char THIS_FILE[] = __FILE__;
#endif

extern CUtilityHelper utilityHelper;
extern CHelpService helpService;
/////////////////////////////////////////////////////////////////////////////
// CBayProfileDriveInPage property page

IMPLEMENT_DYNCREATE(CBayProfileDriveInPage, CPropertyPage)

CBayProfileDriveInPage::CBayProfileDriveInPage() : CPropertyPage(CBayProfileDriveInPage::IDD)
{
	//{{AFX_DATA_INIT(CBayProfileDriveInPage)
	m_BayDepth = 0.0;
	m_BayHeight = 0.0;
	m_BayWidth = 0.0;
	m_Clearance = 0.0;
	m_Overhang = 0.0;
	m_SelectStackPositions = 0;
	m_StackDepth = 0.0;
	m_StackHeight = 0.0;
	m_StackWidth = 0.0;
	m_UprightHeight = 0.0;
	m_UprightWidth = 0.0;
	m_WeightCapacity = 0.0;
	//}}AFX_DATA_INIT
	m_Validating = FALSE;
	m_SideViewButton.m_DimensionInfo.m_BayType = BAYTYPE_DRIVEIN;
	m_TopViewButton.m_DimensionInfo.m_BayType = BAYTYPE_DRIVEIN;
}

CBayProfileDriveInPage::~CBayProfileDriveInPage()
{
}

void CBayProfileDriveInPage::DoDataExchange(CDataExchange* pDX)
{
	CPropertyPage::DoDataExchange(pDX);
	//{{AFX_DATA_MAP(CBayProfileDriveInPage)
	DDX_Control(pDX, IDC_TOP_VIEW_BUTTON, m_TopViewButton);
	DDX_Control(pDX, IDC_SIDE_VIEW_BUTTON, m_SideViewButton);
	DDX_Text(pDX, IDC_BAY_DEPTH, m_BayDepth);
	DDV_MinMaxDouble(pDX, m_BayDepth, 0., 999999999.);
	DDX_Text(pDX, IDC_BAY_HEIGHT, m_BayHeight);
	DDV_MinMaxDouble(pDX, m_BayHeight, 0., 999999999.);
	DDX_Text(pDX, IDC_BAY_WIDTH, m_BayWidth);
	DDV_MinMaxDouble(pDX, m_BayWidth, 0., 999999999.);
	DDX_Text(pDX, IDC_CLEARANCE, m_Clearance);
	DDV_MinMaxDouble(pDX, m_Clearance, 0., 999999999.);
	DDX_Text(pDX, IDC_OVERHANG, m_Overhang);
	DDV_MinMaxDouble(pDX, m_Overhang, 0., 999999999.);
	DDX_Text(pDX, IDC_SELECTION_STACK_POSITIONS, m_SelectStackPositions);
	DDV_MinMaxInt(pDX, m_SelectStackPositions, 0, 999999999);
	DDX_Text(pDX, IDC_STACK_DEPTH, m_StackDepth);
	DDV_MinMaxDouble(pDX, m_StackDepth, 0., 999999999.);
	DDX_Text(pDX, IDC_STACK_HEIGHT, m_StackHeight);
	DDV_MinMaxDouble(pDX, m_StackHeight, 0., 999999999.);
	DDX_Text(pDX, IDC_STACK_WIDTH, m_StackWidth);
	DDV_MinMaxDouble(pDX, m_StackWidth, 0., 999999999.);
	DDX_Text(pDX, IDC_UPRIGHT_HEIGHT, m_UprightHeight);
	DDV_MinMaxDouble(pDX, m_UprightHeight, 0., 999999999.);
	DDX_Text(pDX, IDC_UPRIGHT_WIDTH, m_UprightWidth);
	DDV_MinMaxDouble(pDX, m_UprightWidth, 0., 999999999.);
	DDX_Text(pDX, IDC_WEIGHT_CAPACITY, m_WeightCapacity);
	DDV_MinMaxDouble(pDX, m_WeightCapacity, 0., 999999999.);
	//}}AFX_DATA_MAP

	m_SideViewButton.m_DimensionInfo.m_BayDepth = m_BayDepth;
	m_SideViewButton.m_DimensionInfo.m_BayHeight = m_BayHeight;
	m_SideViewButton.m_DimensionInfo.m_BayWidth = m_BayWidth;
	m_SideViewButton.m_DimensionInfo.m_BayType = BAYTYPE_DRIVEIN;
	m_SideViewButton.m_DimensionInfo.m_UprightHeight = m_UprightHeight;
	m_SideViewButton.m_DimensionInfo.m_UprightWidth = m_UprightWidth;
	//m_SideViewButton.m_DimensionInfo.m_PositionsDeep = m_PalletsDeep;
	m_SideViewButton.m_DimensionInfo.m_Overhang = m_Overhang;
	m_SideViewButton.m_DimensionInfo.m_Clearance = m_Clearance;
	m_SideViewButton.m_DimensionInfo.m_StackDepth = m_StackDepth;
	m_SideViewButton.m_DimensionInfo.m_StackWidth = m_StackWidth;
	m_SideViewButton.m_DimensionInfo.m_SelectPositionHeight = m_StackHeight;
	m_SideViewButton.m_DimensionInfo.m_SelectPositions = m_SelectStackPositions;
	
	m_TopViewButton.m_DimensionInfo = m_SideViewButton.m_DimensionInfo;


}


BEGIN_MESSAGE_MAP(CBayProfileDriveInPage, CPropertyPage)
	//{{AFX_MSG_MAP(CBayProfileDriveInPage)
	ON_WM_HELPINFO()
	//}}AFX_MSG_MAP
END_MESSAGE_MAP()

/////////////////////////////////////////////////////////////////////////////
// CBayProfileDriveInPage message handlers
BOOL CBayProfileDriveInPage::OnInitDialog() 
{
	CPropertyPage::OnInitDialog();
	
	CBayProfileSheet *pSheet = (CBayProfileSheet *)GetParent();
	m_pBayProfile = pSheet->m_pBayProfile;

	if (m_pBayProfile->m_BayProfileDBId > 0) {	
		m_BayDepth = m_pBayProfile->m_Depth;
		m_BayHeight = m_pBayProfile->m_Height;
		m_BayWidth = m_pBayProfile->m_Width;
		m_UprightHeight = m_pBayProfile->m_UprightHeight;
		m_UprightWidth = m_pBayProfile->m_UprightWidth;

		if (m_pBayProfile->m_LevelProfileList.GetSize() == 0) {
			AfxMessageBox("Invalid drive-in rack.  No levels specified.");
			AfxThrowUserException();
		}

		m_Overhang = m_pBayProfile->m_LevelProfileList[0]->m_Overhang;
		m_Clearance = m_pBayProfile->m_LevelProfileList[0]->m_Clearance;
		m_StackDepth = m_pBayProfile->m_LevelProfileList[0]->m_StackDepth;
		m_StackWidth = m_pBayProfile->m_LevelProfileList[0]->m_StackWidth;
		m_StackHeight = m_pBayProfile->m_LevelProfileList[0]->m_SelectPositionHeight;
		m_SelectStackPositions = m_pBayProfile->m_LevelProfileList[0]->m_SelectPositions;

		m_WeightCapacity = m_pBayProfile->m_WeightCapacity;

	}
	
	UpdateData(FALSE);
		
	return TRUE;  // return TRUE unless you set the focus to a control
	              // EXCEPTION: OCX Property Pages should return FALSE
}

BOOL CBayProfileDriveInPage::OnKillActive() 
{
	UpdateData(TRUE);

	m_pBayProfile->m_Width = m_BayWidth;
	m_pBayProfile->m_Depth = m_BayDepth;
	m_pBayProfile->m_Height = m_BayHeight;
	m_pBayProfile->m_UprightHeight = m_UprightHeight;
	m_pBayProfile->m_UprightWidth = m_UprightWidth;
	m_pBayProfile->m_WeightCapacity = m_WeightCapacity;

	if (m_pBayProfile->m_LevelProfileList.GetSize() == 0) {
		AfxMessageBox("Invalid drive-in rack.  No levels specified.");
		AfxThrowUserException();
	}
	
	m_pBayProfile->m_LevelProfileList[0]->m_Overhang = m_Overhang;
	m_pBayProfile->m_LevelProfileList[0]->m_Clearance = m_Clearance;
	m_pBayProfile->m_LevelProfileList[0]->m_StackDepth = m_StackDepth;
	m_pBayProfile->m_LevelProfileList[0]->m_StackWidth = m_StackWidth;
	m_pBayProfile->m_LevelProfileList[0]->m_SelectPositionHeight = m_StackHeight;
	m_pBayProfile->m_LevelProfileList[0]->m_SelectPositions = m_SelectStackPositions;
	m_pBayProfile->m_LevelProfileList[0]->m_WeightCapacity = m_WeightCapacity;

	int totalPositions = (int)(m_BayHeight/m_StackHeight) * 
		(int)(( m_BayDepth + m_Overhang) / m_StackDepth);
	m_pBayProfile->m_LevelProfileList[0]->m_ReservePositions = totalPositions - m_SelectStackPositions;
	m_pBayProfile->m_LevelProfileList[0]->m_ReservePositionHeight = m_StackHeight;
	m_pBayProfile->m_LevelProfileList[0]->m_Thickness = 0;

	CLocationProfile *pLocProfile;
	if (m_pBayProfile->m_LevelProfileList[0]->m_LocationProfileList.GetSize() == 0) {
		pLocProfile = new CLocationProfile;
		m_pBayProfile->m_LevelProfileList[0]->m_LocationProfileList.Add(pLocProfile);
	}
	else
		pLocProfile = m_pBayProfile->m_LevelProfileList[0]->m_LocationProfileList[0];

	pLocProfile->m_Description = "1";
	pLocProfile->m_Depth = m_StackDepth;
	pLocProfile->m_Width = m_StackWidth;
	pLocProfile->m_Height = m_StackHeight;
	pLocProfile->m_HandlingMethod = PALLET_HANDLING;
	pLocProfile->m_WeightCapacity = m_WeightCapacity/totalPositions;

	if (m_SelectStackPositions > 0)
		pLocProfile->m_IsSelect = 1;
	else
		pLocProfile->m_IsSelect = 0;

	pLocProfile->m_LocationSpace = (m_BayWidth - m_StackWidth)/2;
	pLocProfile->m_Coordinates.m_X = pLocProfile->m_LocationSpace;


	if (! Validate())
		return 0;

	return CPropertyPage::OnKillActive();
}


BOOL CBayProfileDriveInPage::OnCommand(WPARAM wParam, LPARAM lParam) 
{
	if (HIWORD(wParam) == EN_KILLFOCUS && ! m_Validating) {
		m_Validating = TRUE;
		if (! UpdateData(TRUE))
			m_Validating = FALSE;
		else {
			m_SideViewButton.Invalidate(TRUE);
			m_TopViewButton.Invalidate(TRUE);
		}
		m_Validating = FALSE;
	}

	return CPropertyPage::OnCommand(wParam, lParam);
}


BOOL CBayProfileDriveInPage::Validate()
{
	int positionsHigh;
	int positionsDeep;

	if (m_StackWidth > m_BayWidth) {
		AfxMessageBox("The Stack Width must be less than or equal to the Bay Width.");
		return utilityHelper.SetEditControlErrorState(this, IDC_STACK_WIDTH);
	}

	if ((m_StackHeight + m_Clearance) > m_BayHeight) {
		AfxMessageBox("The Stack Height plus the Clearance must be less than or equal to the Bay Height.");
		return utilityHelper.SetEditControlErrorState(this, IDC_STACK_HEIGHT);
	}

	if (m_StackDepth > (m_BayDepth + m_Overhang)) {
		AfxMessageBox("The Stack Depth must be less than the Bay Depth plus the Overhang");
		return utilityHelper.SetEditControlErrorState(this, IDC_STACK_DEPTH);
	}

	positionsHigh = m_BayHeight/m_StackHeight;
	positionsDeep = m_SelectStackPositions/positionsHigh;
	if (m_StackDepth * positionsDeep > m_BayDepth) {
		AfxMessageBox("The Bay Depth must be greater than the Stack Depth multiplied by "
			"the the number of positions.");
		return utilityHelper.SetEditControlErrorState(this, IDC_BAY_DEPTH);
	}

	return TRUE;

}

BOOL CBayProfileDriveInPage::OnSetActive() 
{
	if (m_pBayProfile->m_Active) {
		GetDlgItem(IDC_BAY_DEPTH)->EnableWindow(FALSE);
		GetDlgItem(IDC_BAY_WIDTH)->EnableWindow(FALSE);
		GetDlgItem(IDC_BAY_HEIGHT)->EnableWindow(FALSE);
		GetDlgItem(IDC_UPRIGHT_WIDTH)->EnableWindow(FALSE);
		GetDlgItem(IDC_UPRIGHT_HEIGHT)->EnableWindow(FALSE);
		GetDlgItem(IDC_STACK_WIDTH)->EnableWindow(FALSE);
		GetDlgItem(IDC_STACK_DEPTH)->EnableWindow(FALSE);
		GetDlgItem(IDC_CLEARANCE)->EnableWindow(FALSE);
		GetDlgItem(IDC_OVERHANG)->EnableWindow(FALSE);
		GetDlgItem(IDC_STACK_HEIGHT)->EnableWindow(FALSE);
		GetDlgItem(IDC_SELECTION_STACK_POSITIONS)->EnableWindow(FALSE);
	}
	else {
		GetDlgItem(IDC_BAY_DEPTH)->EnableWindow(TRUE);
		GetDlgItem(IDC_BAY_WIDTH)->EnableWindow(TRUE);
		GetDlgItem(IDC_BAY_HEIGHT)->EnableWindow(TRUE);
		GetDlgItem(IDC_UPRIGHT_WIDTH)->EnableWindow(TRUE);
		GetDlgItem(IDC_UPRIGHT_HEIGHT)->EnableWindow(TRUE);
		GetDlgItem(IDC_STACK_WIDTH)->EnableWindow(TRUE);
		GetDlgItem(IDC_STACK_DEPTH)->EnableWindow(TRUE);
		GetDlgItem(IDC_CLEARANCE)->EnableWindow(TRUE);
		GetDlgItem(IDC_OVERHANG)->EnableWindow(TRUE);
		GetDlgItem(IDC_STACK_HEIGHT)->EnableWindow(TRUE);
		GetDlgItem(IDC_SELECTION_STACK_POSITIONS)->EnableWindow(TRUE);

	}
	
	UpdateData(FALSE);
	
	return CPropertyPage::OnSetActive();
}

BOOL CBayProfileDriveInPage::OnHelpInfo(HELPINFO* pHelpInfo) 
{
	helpService.ShowFieldHelp(IDD, pHelpInfo->iCtrlId);
	return FALSE;

}

BOOL CBayProfileDriveInPage::OnNotify(WPARAM wParam, LPARAM lParam, LRESULT* pResult) 
{
	NMHDR* pNMHDR = (NMHDR*)lParam;

	if (pNMHDR->code == PSN_HELP) {
		helpService.ShowScreenHelp(IDD);
		return TRUE;
	}
	
	return CPropertyPage::OnNotify(wParam, lParam, pResult);
}
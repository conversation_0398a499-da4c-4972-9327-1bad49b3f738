#if !defined(AFX_PRODUCTMAINTENANCE_H__C02AB969_CFAE_11D4_9EC1_00C04FAC149C__INCLUDED_)
#define AFX_PRODUCTMAINTENANCE_H__C02AB969_CFAE_11D4_9EC1_00C04FAC149C__INCLUDED_

#include "DisplayResults.h"	// Added by ClassView
#if _MSC_VER > 1000
#pragma once
#endif // _MSC_VER > 1000
// ProductMaintenance.h : header file
//
#include "qqhclasses.h"
#include "ProductPack.h"
#include "ProductContainer.h"
#include "ProductGroup.h"
#include "ProductDataService.h"
/////////////////////////////////////////////////////////////////////////////
// CProductSheet

class CProductSheet : public CPropertySheet
{
	DECLARE_DYNAMIC(CProductSheet)

// Construction
public:
	CProductSheet(UINT nIDCaption, CWnd* pParentWnd = NULL, UINT iSelectPage = 0);
	CProductSheet(LPCTSTR pszCaption, CWnd* pParentWnd = NULL, UINT iSelectPage = 0);

// Attributes
public:

// Operations
public:

// Overrides
	// ClassWizard generated virtual function overrides
	//{{AFX_VIRTUAL(CProductSheet)
	public:
	virtual BOOL OnInitDialog();
	protected:
	virtual BOOL OnCommand(WPARAM wParam, LPARAM lParam);
	//}}AFX_VIRTUAL

// Implementation
public:
	BOOL m_AllowUpdate;
	void OnColorProducts(WPARAM wParam, LPARAM lParam);
	int m_DisplayProductID;
	BOOL m_QueryOnly;
	BOOL ValidateItem(const CString &name, const CString &value, int type, double min, double max,
		int page, int dialogID, BOOL mandatory);
	BOOL ValidateUpdate();
	void BuildDisplayLine(CProductPack *product, CString &line);
	CDisplayResults *m_ProductListDialog;
	void OnChangeSelection(WPARAM wParam, LPARAM lParam);
	void OnCloseProductListDialog(WPARAM wParam, LPARAM lParam);
	void OnSizeProductListDialog(WPARAM wParam, LPARAM lParam);
	void OnCancel();
	void OnDelete();
	void OnUpdate();
	void OnQuery();
	CButton *m_UpdateButton;
	CButton *m_DeleteButton;
	CButton *m_ClearButton;
	CRect m_DisplayListRect;
	CRect m_MainWindowRect;
	virtual ~CProductSheet();
	int m_CurrentProductIdx;
	CProductPack m_CurrentProduct;
	CTypedPtrArray<CObArray, CProductPack*> m_Products;
	CStringArray m_UsedAttributeList;
	void BuildHeaderLine(CString &header);
	void BuildDisplayLine(CString &line, CProductPack &product);
	CProductDataService m_ProductDataService;
	// Generated message map functions
protected:
	//{{AFX_MSG(CProductSheet)
	afx_msg BOOL OnHelpInfo(HELPINFO* pHelpInfo);
	afx_msg void OnMove(int x, int y);
	//}}AFX_MSG
	DECLARE_MESSAGE_MAP()
private:
	int GetProductByID();
	void OnClear();
	void LoadScreenFromProduct();
	void LoadProductFromScreen();

};

/////////////////////////////////////////////////////////////////////////////
/////////////////////////////////////////////////////////////////////////////
// CProductPage dialog

class CProductPage : public CPropertyPage
{
	DECLARE_DYNCREATE(CProductPage)

// Construction
public:
	CProductPage();
	~CProductPage();

// Dialog Data
	//{{AFX_DATA(CProductPage)
	enum { IDD = IDD_PRODUCT_PAGE };
	CComboBox	m_UnitOfIssue;
	CComboBox	m_OptimizeBy;
	CString	m_Description;
	CString	m_Location;
	CString	m_WMSProductDetailID;
	CString	m_WMSProductID;
	CString	m_CasePack;
	CString	m_EachHeight;
	CString	m_EachLength;
	CString	m_EachWidth;
	CString	m_InnerHeight;
	CString	m_InnerLength;
	CString	m_InnerPack;
	CString	m_InnerWidth;
	CString	m_MaxStackNumber;
	CString	m_Movement;
	CString	m_NumberOfHits;
	CString	m_Weight;
	CString	m_BalanceOnHand;
	CString	m_CaseHeight;
	CString	m_CaseLength;
	CString	m_CaseWidth;
	int		m_IsAssignmentLocked;
	int		m_IsHazard;
	int		m_IsPickToBelt;
	int		m_RotateXAxis;
	int		m_RotateYAxis;
	int		m_RotateZAxis;
	float	m_Test;
	int		m_Active;
	CString	m_PreviousBOH;
	CString	m_PreviousMovement;
	int		m_Trace;
	CString	m_CommodityType;
	CString	m_CrushFactor;
	//}}AFX_DATA


// Overrides
	// ClassWizard generate virtual function overrides
	//{{AFX_VIRTUAL(CProductPage)
	protected:
	virtual void DoDataExchange(CDataExchange* pDX);    // DDX/DDV support
	virtual BOOL OnNotify(WPARAM wParam, LPARAM lParam, LRESULT* pResult);
	//}}AFX_VIRTUAL

// Implementation
protected:
	// Generated message map functions
	//{{AFX_MSG(CProductPage)
	virtual BOOL OnInitDialog();
	afx_msg BOOL OnHelpInfo(HELPINFO* pHelpInfo);
	//}}AFX_MSG
	DECLARE_MESSAGE_MAP()

};

/////////////////////////////////////////////////////////////////////////////
// CProductContainerPage dialog

class CProductContainerPage : public CPropertyPage
{
	DECLARE_DYNCREATE(CProductContainerPage)

// Construction
public:
	CProductContainerPage();
	~CProductContainerPage();

// Dialog Data
	//{{AFX_DATA(CProductContainerPage)
	enum { IDD = IDD_PRODUCT_CONTAINER_PAGE };
	CString	m_ContainerHeight;
	CString	m_ContainerLength;
	CString	m_ContainerWidth;
	CString	m_Hi;
	CString	m_Ti;
	int		m_IsHeightOverride;
	CString	m_NumberInPallet;
	//}}AFX_DATA


// Overrides
	// ClassWizard generate virtual function overrides
	//{{AFX_VIRTUAL(CProductContainerPage)
	protected:
	virtual void DoDataExchange(CDataExchange* pDX);    // DDX/DDV support
	virtual BOOL OnNotify(WPARAM wParam, LPARAM lParam, LRESULT* pResult);
	//}}AFX_VIRTUAL

// Implementation
protected:
	// Generated message map functions
	//{{AFX_MSG(CProductContainerPage)
	afx_msg BOOL OnHelpInfo(HELPINFO* pHelpInfo);
	//}}AFX_MSG
	DECLARE_MESSAGE_MAP()

};
/////////////////////////////////////////////////////////////////////////////
// CProductOptimizePage dialog

class CProductOptimizePage : public CPropertyPage
{
	DECLARE_DYNCREATE(CProductOptimizePage)

// Construction
public:
	CProductOptimizePage();
	~CProductOptimizePage();
	CTypedPtrArray<CObArray, CProductGroup*> m_ProductGroups;

// Dialog Data
	//{{AFX_DATA(CProductOptimizePage)
	enum { IDD = IDD_PRODUCT_OPTIMIZE_PAGE };
	CComboBox	m_OptimizedLocationCtl;
	CComboBox	m_OptimizedLocationProductGroupCtl;
	CComboBox	m_OptimizedLevelTypeCtl;
	CComboBox	m_OptimizedBayProfileCtl;
	CComboBox	m_ProductGroupCtl;
	CComboBox	m_LocationProductGroupCtl;
	CComboBox	m_LocationCtl;
	CComboBox	m_LevelTypeCtl;
	CComboBox	m_BayProfileCtl;
	CString	m_RotatedHeight;
	CString	m_RotatedLength;
	CString	m_RotatedWidth;
	CString	m_Location;
	CString	m_IntegrationStatus;
	CString	m_OptimizedLocation;
	CString	m_ProductGroup;
	//}}AFX_DATA
	int m_ProductGroupDBID;
	CString m_LocationProductGroup;
	int m_LocationProductGroupDBID;
	CString m_BayProfile;
	int m_BayProfileDBID;
	CString m_LevelType;
	int m_LevelTypeNum;

	CString m_OptimizedLocationProductGroup;
	int m_OptimizedLocationProductGroupDBID;
	CString m_OptimizedBayProfile;
	int m_OptimizedBayProfileDBID;
	CString m_OptimizedLevelType;
	int m_OptimizedLevelTypeNum;

// Overrides
	// ClassWizard generate virtual function overrides
	//{{AFX_VIRTUAL(CProductOptimizePage)
	protected:
	virtual void DoDataExchange(CDataExchange* pDX);    // DDX/DDV support
	virtual BOOL OnNotify(WPARAM wParam, LPARAM lParam, LRESULT* pResult);
	//}}AFX_VIRTUAL

// Implementation
protected:
	// Generated message map functions
	//{{AFX_MSG(CProductOptimizePage)
	virtual BOOL OnInitDialog();
	afx_msg void OnSelchangeLocation();
	afx_msg void OnEditchangeLocation();
	afx_msg void OnUpdateGroup();
	afx_msg BOOL OnHelpInfo(HELPINFO* pHelpInfo);
	afx_msg void OnViewFacings();
	afx_msg void OnEditchangeOptimizedLocation();
	afx_msg void OnSelchangeOptimizedLocation();
	afx_msg void OnViewOptimizedFacings();
	//}}AFX_MSG
	DECLARE_MESSAGE_MAP()

private:
	void SetLocationControls();
};
//{{AFX_INSERT_LOCATION}}
// Microsoft Visual C++ will insert additional declarations immediately before the previous line.

#endif // !defined(AFX_PRODUCTMAINTENANCE_H__C02AB969_CFAE_11D4_9EC1_00C04FAC149C__INCLUDED_)

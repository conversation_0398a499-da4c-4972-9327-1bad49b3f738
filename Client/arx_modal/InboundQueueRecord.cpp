// InboundQueueRecord.cpp: implementation of the CInboundQueueRecord class.
//
//////////////////////////////////////////////////////////////////////

#include "stdafx.h"
#include "modal.h"
#include "InboundQueueRecord.h"
#include "WMSGroupConnection.h"
#include "Location.h"
#include "ProductPack.h"
#include "Assignment.h"
#include "InterfaceHelper.h"

#ifdef _DEBUG
#undef THIS_FILE
static char THIS_FILE[]=__FILE__;
#define new DEBUG_NEW
#endif

//////////////////////////////////////////////////////////////////////
// Construction/Destruction
//////////////////////////////////////////////////////////////////////

CInboundQueueRecord::CInboundQueueRecord()
{
	m_EndOfBatch = m_EndOfFeed = 0;
	m_pRecord = NULL;
	m_Action = CInterfaceHelper::Add;
}

CInboundQueueRecord::CInboundQueueRecord(const CInboundQueueRecord& other)
{
	m_FeedId = other.m_FeedId;
	m_BatchId = other.m_BatchId;
	m_LineNumber = other.m_LineNumber;
	m_InterfaceType = other.m_InterfaceType;
	m_pRecord = other.m_pRecord;
	m_WMSId = other.m_WMSId;
	m_WMSDetailId = other.m_WMSDetailId;
	m_Action = other.m_Action;
	m_EndOfBatch = other.m_EndOfBatch;
	m_EndOfFeed = other.m_EndOfFeed;
}

CInboundQueueRecord& CInboundQueueRecord::operator=(const CInboundQueueRecord& other)
{
	m_FeedId = other.m_FeedId;
	m_BatchId = other.m_BatchId;
	m_LineNumber = other.m_LineNumber;
	m_InterfaceType = other.m_InterfaceType;
	m_pRecord = other.m_pRecord;
	m_WMSId = other.m_WMSId;
	m_WMSDetailId = other.m_WMSDetailId;
	m_Action = other.m_Action;
	m_EndOfBatch = other.m_EndOfBatch;
	m_EndOfFeed = other.m_EndOfFeed;

	return *this;
}


CInboundQueueRecord::~CInboundQueueRecord()
{
	if (m_pRecord != NULL)
		delete m_pRecord;
}

CString CInboundQueueRecord::Stream()
{
	CString temp;
	CLocation *pLocation;
	CProductPack *pProductPack;
	CAssignment *pAssignment;

	temp.Format("%d|%d|%d|", m_Action, m_BatchId, m_LineNumber);

	switch (m_InterfaceType) {
	case CWMSGroupConnection::LocationInterface:
		pLocation = (CLocation *)m_pRecord;
		temp += pLocation->Stream();
		break;
	case CWMSGroupConnection::ProductInterface:
		pProductPack = (CProductPack *)m_pRecord;
		temp += pProductPack->Stream();
		break;
	case CWMSGroupConnection::AssignmentInterface:
		pAssignment = (CAssignment *)m_pRecord;
		temp += pAssignment->Stream();
		break;
	}

	return temp;
}

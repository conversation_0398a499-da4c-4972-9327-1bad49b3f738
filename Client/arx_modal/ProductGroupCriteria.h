// ProductGroupCriteria.h: interface for the CProductGroupCriteria class.
//
//////////////////////////////////////////////////////////////////////

#if !defined(AFX_PRODUCTGROUPCRITERIA_H__0DBB56D4_077B_11D5_9EC8_00C04FAC149C__INCLUDED_)
#define AFX_PRODUCTGROUPCRITERIA_H__0DBB56D4_077B_11D5_9EC8_00C04FAC149C__INCLUDED_

#if _MSC_VER > 1000
#pragma once
#endif // _MSC_VER > 1000

#include "ProductGroupCriteriaRange.h"
#include "ProductGroupCriteriaValue.h"
#include "ProductGroupCriteriaQuery.h"

typedef CTypedPtrArray<CObArray, CProductGroupCriteriaRange*> CriteriaRangeType;
typedef CTypedPtrArray<CObArray, CProductGroupCriteriaValue*> CriteriaValueType;

class CProductGroupCriteria : public CObject  
{
public:
	void SetAttributeType(int type);
	void SetAttribute(const CString &attribute);
	int GetAttributeType();
	CString GetAttribute();
	BOOL IsEqual(CProductGroupCriteria &other);
	CProductGroupCriteria();
	CProductGroupCriteria& operator=(const CProductGroupCriteria &other);
	virtual ~CProductGroupCriteria();
	int Parse(CString &line);
	long m_CriteriaDBID;
	CString m_Name;
	CString m_Description;
	BOOL m_IsDiscrete;
	CString m_Attribute;	// temporary until we allow attributes by range
	int m_AttributeType;	// ditto
	CriteriaRangeType m_CriteriaRangeList;
	CriteriaValueType m_CriteriaValueList;

};

#endif // !defined(AFX_PRODUCTGROUPCRITERIA_H__0DBB56D4_077B_11D5_9EC8_00C04FAC149C__INCLUDED_)

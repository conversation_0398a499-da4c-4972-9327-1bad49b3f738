// EditGrid.cpp : implementation file
//

#include "stdafx.h"
#include "EditGrid.h"
#include "font.h"
#include "HelpService.h"

#ifdef _DEBUG
#define new DEBUG_NEW
#undef THIS_FILE
static char THIS_FILE[] = __FILE__;
#endif

extern CHelpService helpService;

/////////////////////////////////////////////////////////////////////////////
// CEditGrid

CEditGrid::CEditGrid()
{
	m_AllowInsertDelete = FALSE;
	m_Alignment = RIGHT_ALIGNMENT;	// right
	m_SortColumn = -1;
}


CEditGrid::~CEditGrid()
{
}


BEGIN_MESSAGE_MAP(CEditGrid, CWnd)
	//{{AFX_MSG_MAP(CEditGrid)
	ON_WM_SETFOCUS()
	ON_WM_GETDLGCODE()
	ON_WM_SHOWWINDOW()
	ON_WM_KEYDOWN()
	ON_WM_HELPINFO()
	//}}AFX_MSG_MAP
END_MESSAGE_MAP()

BEGIN_EVENTSINK_MAP(CEditGrid, CMSFlexGrid)
// {{AFX_EVENTSINK_MAP(CEditGrid)
// }}AFX_EVENTSINK_MAP
ON_EVENT_REFLECT(CEditGrid, -603 /* KeyPress */, OnKeyPressGrid,
				 VTS_PI2)
ON_EVENT_REFLECT(CEditGrid, -605 /* MouseDown */, OnMouseDown,
				 VTS_I2 VTS_I2 VTS_XPOS_PIXELS VTS_YPOS_PIXELS)
ON_EVENT_REFLECT(CEditGrid, -601 /* DblClick */, OnDblClickGrid,
				 VTS_NONE)
ON_EVENT_REFLECT(CEditGrid, 72 /* LeaveCell */, OnUpdateGrid,
				 VTS_BOOL)
ON_EVENT_REFLECT(CEditGrid, 71 /* EnterCell */, OnDblClickGrid,
				 VTS_NONE)
END_EVENTSINK_MAP()

void CEditGrid::OnDblClickGrid()
{
	short i = 13;
	OnKeyPressGrid(&i); // Simulate a return.
}

void CEditGrid::OnMouseDown(short Button, short shift, OLE_XPOS_PIXELS x, OLE_YPOS_PIXELS y)
{
	UNREFERENCED_PARAMETER(Button);
	UNREFERENCED_PARAMETER(shift);
	UNREFERENCED_PARAMETER(x);
	UNREFERENCED_PARAMETER(y);
	short i = 13;
	OnKeyPressGrid(&i);

}
void CEditGrid::OnKeyPressGrid(short FAR* KeyAscii)
{
	long row, col, mouseRow, mouseCol;

	row = this->GetRow();
	col = this->GetCol();
	mouseRow = this->GetMouseRow();
	mouseCol = this->GetMouseCol();

//	if (mouseRow < this->GetFixedRows() || mouseCol < this->GetFixedCols())
	if (row < this->GetFixedRows() || col < this->GetFixedCols())
		return;

	ASSERT (KeyAscii != NULL);
	
	if (m_edit->m_hWnd == NULL)
		return;

	m_edit->SetWindowText(GetText());
	
	if (*KeyAscii == 13)
		m_edit->SetSel(0,-1);
	else
	{
		char buf[] = " ";
		buf[0] = (char)*KeyAscii;
		m_edit->SetSel(-1,-1);
		m_edit->ReplaceSel(buf);
	}
	
	// Adjust for border height and convert from twips to screen
	// units.
	m_edit->MoveWindow(((GetCellLeft() - m_lBorderWidth) *
		m_nLogX)/1440,
		((GetCellTop() - m_lBorderHeight) * m_nLogY)/1440,
		(GetCellWidth()* m_nLogX)/1440,
		(GetCellHeight()* m_nLogY)/1440, FALSE);
	

	m_edit->ShowWindow(SW_SHOW);
	m_edit->SetFocus();

	CRect r;
	this->GetClientRect(&r);
	InvalidateRect(&r, TRUE);

}

void CEditGrid::OnUpdateGrid(BOOL bLeaving = FALSE)
{
	// Check to see if edit is visible.
	BOOL bVisible = ::GetWindowLong(m_edit->GetSafeHwnd(), GWL_STYLE)
		& WS_VISIBLE;
	if (bVisible) {
		long row, col, mouseRow, mouseCol;
		row = this->GetRow();
		col = this->GetCol();
		mouseRow = this->GetMouseRow();
		mouseCol = this->GetMouseCol();

		if (row >= this->GetFixedRows() && col >= this->GetFixedCols()) {
			CString cStr;
			m_edit->GetWindowText(cStr);
			SetText(cStr);
		}

		if (mouseRow == row && mouseCol == col) {
			//m_edit->ShowWindow(SW_SHOW);
			if (! bLeaving) {
				m_edit->SetSel(-1, -1);
				m_edit->SetFocus();
			}
		}
		else
			m_edit->ShowWindow(SW_HIDE);
	}
}


/////////////////////////////////////////////////////////////////////////////
// CEditGrid message handlers

void CEditGrid::PreSubclassWindow() 
{

	
	// Create invisible edit control
	m_rightEdit.Create(WS_CHILD|ES_WANTRETURN|ES_RIGHT|ES_MULTILINE,
		CRect(0,0,0,0), this, GetDlgCtrlID());

	m_leftEdit.Create(WS_CHILD|ES_WANTRETURN|ES_LEFT|ES_MULTILINE,
		CRect(0,0,0,0), this, GetDlgCtrlID());

	font.CreatePointFont((int)this->GetCellFontSize(), this->GetCellFontName(), NULL);
	m_leftEdit.SetFont(&font);
	m_rightEdit.SetFont(&font);

	if (m_Alignment == RIGHT_ALIGNMENT)
		m_edit = &m_rightEdit;
	else 
		m_edit = &m_leftEdit;

	// Calculate border size.
	SetRow(0);
	SetCol(0);
	m_lBorderWidth = GetCellLeft();
	m_lBorderHeight = GetCellTop();
	
	for (int i=0; i < this->GetFixedCols(); ++i)
		SetColAlignment(i, 4);

	for (i=this->GetFixedCols(); i < this->GetCols(); ++i) {
		if (m_Alignment == RIGHT_ALIGNMENT)	// right
			SetColAlignment(i, 7);
		else
			SetColAlignment(i, 1);
	}

	// To convert grid rect from twips to DC units you need
	// pixels per inch.
	CDC* pDC = GetDC();
	m_nLogX = pDC->GetDeviceCaps(LOGPIXELSX);
	m_nLogY = pDC->GetDeviceCaps(LOGPIXELSY);
	ReleaseDC(pDC);

}

void CEditGrid::OnSetFocus(CWnd* pOldWnd)
{
	CMSFlexGrid::OnSetFocus(pOldWnd);
	
	OnUpdateGrid(FALSE);
}



void CEditGrid::ProcessInsert()
{
	if (! m_AllowInsertDelete)
		return;

	long rows, currentRow;

	rows = this->GetRows();
	currentRow = this->GetRow();

	// Save the value in the current cell
	OnUpdateGrid();

	// Insert a new row
	currentRow++;
	this->AddItem("", COleVariant(currentRow));

	this->SetRow(currentRow);
	// Set the column to the first non-fixed column;
	// (remember, columns are 0-based)
	this->SetCol(this->GetFixedCols());

	// Start editing the new row
	OnDblClickGrid();


	return;


}

void CEditGrid::ProcessDelete()
{
	if (! m_AllowInsertDelete)
		return;

	long currentRow = this->GetRow();
	long rows = this->GetRows();

	if (rows == 2) {
		// If there's only one non-fixed row, just clear it out instead of removing it
		long cols = this->GetCols();
		for (int i=0; i < cols; ++i)
			this->SetTextMatrix(1, i, "");
	}
	else {
		this->RemoveItem(currentRow);
		if (currentRow == rows-1)
			this->SetRow(currentRow-1);
	}

	OnDblClickGrid();

}

UINT CEditGrid::OnGetDlgCode() 
{
	return DLGC_WANTTAB|DLGC_WANTALLKEYS|DLGC_WANTARROWS;
	//return DLGC_WANTALLKEYS;
}

void CEditGrid::ProcessTab()
{
	long currentRow, currentCol, rows, cols;
	
	rows = this->GetRows();
	cols = this->GetCols();
	currentRow = this->GetRow();
	currentCol = this->GetCol();
	
	// Save the value in the current cell
	OnUpdateGrid();

	if (currentRow == rows-1) {		// last row
		if (currentCol == cols-1)	// last column
			ProcessInsert();
		else {
			this->SetCol(currentCol+1);
			OnDblClickGrid();
		}
	}
	else {
		if (currentCol == cols-1) {		// last column
			this->SetRow(currentRow+1);
			this->SetCol(this->GetFixedCols());	// 1st non-fixed column
			OnDblClickGrid();
		}
		else {
			this->SetCol(currentCol+1);
			OnDblClickGrid();
		}
	}

	return;
}

void CEditGrid::OnShowWindow(BOOL bShow, UINT nStatus) 
{
	CMSFlexGrid::OnShowWindow(bShow, nStatus);
	
	
}

void CEditGrid::ResetAlignment(int pAlignment)
{

	m_Alignment = pAlignment;

	for (int i=0; i < this->GetFixedCols(); ++i)
		SetColAlignment(i, 4);

	for (i=this->GetFixedCols(); i < this->GetCols(); ++i) {
		if (m_Alignment == 0)	// right
			SetColAlignment(i, 7);
		else
			SetColAlignment(i, 1);
	}
	
	if (m_Alignment == RIGHT_ALIGNMENT)
		m_edit = &m_rightEdit;
	else
		m_edit = &m_leftEdit;

}

CEditGrid::CEditGrid(int pAlignment, BOOL pAllowInsertDelete)
{
	m_Alignment = pAlignment;
	m_AllowInsertDelete = pAllowInsertDelete;
	m_SortColumn = -1;
}

BOOL CEditGrid::OnHelpInfo(HELPINFO* pHelpInfo) 
{
	ShowHelp();
	
	return FALSE;
}

void CEditGrid::ShowHelp()
{
	helpService.ShowScreenHelp(m_HelpTopic);
}

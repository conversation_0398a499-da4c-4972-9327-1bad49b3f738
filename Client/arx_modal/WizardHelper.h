// WizardHelper.h: interface for the CWizardHelper class.
//
//////////////////////////////////////////////////////////////////////

#if !defined(AFX_WIZARDHELPER_H__58B7DE17_E44B_460D_9E23_7CF28CE9DCD7__INCLUDED_)
#define AFX_WIZARDHELPER_H__58B7DE17_E44B_460D_9E23_7CF28CE9DCD7__INCLUDED_

#if _MSC_VER > 1000
#pragma once
#endif // _MSC_VER > 1000

#include "BayProfile.h"
#include "SideProfile.h"
#include "AisleProfile.h"

#define WM_VIEW_DRAWING WM_USER+1
#define WM_BAY_WIZARD WM_USER+2
#define WM_SIDE_WIZARD WM_USER+3
#define WM_AISLE_WIZARD WM_USER+4
#define WM_MAIN_WIZARD WM_USER+5

class CWizardHelper  
{
public:
	void LoadBayTypeComboBox(CComboBox &pBox);
	int ShowBayWizard(CBayProfile *pBayProfile);
	int ShowSideWizard(CSideProfile *pSideProfile);
	int ShowAisleWizard(CAisleProfile *pAisleProfile);

	void ViewBayProfileDrawing();
	void ViewSideProfileDrawing();
	void ViewAisleProfileDrawing();

	CWizardHelper();
	virtual ~CWizardHelper();

	void BayProfileWizard();
	void SideProfileWizard();
	void AisleProfileWizard();



	
	


	
	void ProfileMaintenance();


};

#endif // !defined(AFX_WIZARDHELPER_H__58B7DE17_E44B_460D_9E23_7CF28CE9DCD7__INCLUDED_)

#if !defined(AFX_BAYPROFILELISTDIALOG_H__96F48323_51C9_44C1_96D7_B34827563DFF__INCLUDED_)
#define AFX_BAYPROFILELISTDIALOG_H__96F48323_51C9_44C1_96D7_B34827563DFF__INCLUDED_

#if _MSC_VER > 1000
#pragma once
#endif // _MSC_VER > 1000
// BayProfileListDialog.h : header file
//
#include "BayProfile.h"
#include "Resource.h"
/////////////////////////////////////////////////////////////////////////////
// CBayProfileListDialog dialog

class CBayProfileListDialog : public CDialog
{
// Construction
public:
	CBayProfile * m_pBayProfile;
	BOOL m_IsModeless;
	CBayProfileListDialog(CWnd* pParent = NULL);   // standard constructor
	~CBayProfileListDialog();

// Dialog Data
	//{{AFX_DATA(CBayProfileListDialog)
	enum { IDD = IDD_BAY_PROFILE_LIST };
	CTreeCtrl	m_ProfileTreeCtrl;
	BOOL	m_HideInActive;
	BOOL	m_HideExcluded;
	//}}AFX_DATA


// Overrides
	// ClassWizard generated virtual function overrides
	//{{AFX_VIRTUAL(CBayProfileListDialog)
	protected:
	virtual void DoDataExchange(CDataExchange* pDX);    // DDX/DDV support
	virtual void PostNcDestroy();
	//}}AFX_VIRTUAL

// Implementation
protected:

	// Generated message map functions
	//{{AFX_MSG(CBayProfileListDialog)
	afx_msg void OnNew();
	afx_msg void OnEdit();
	afx_msg void OnCopy();
	afx_msg void OnDelete();
	afx_msg void OnExclude();
	virtual BOOL OnInitDialog();
	afx_msg void OnDblclkBayProfileTree(NMHDR* pNMHDR, LRESULT* pResult);
	afx_msg void OnViewDrawing();
	afx_msg void OnExcludedCheckbox();
	afx_msg void OnActiveCheckbox();
	afx_msg void OnEndlabeleditBayProfileTree(NMHDR* pNMHDR, LRESULT* pResult);
	virtual void OnOK();
	virtual void OnCancel();
	afx_msg void OnContextMenu(CWnd* pWnd, CPoint point);
	afx_msg void OnBeginlabeleditBayProfileTree(NMHDR* pNMHDR, LRESULT* pResult);
	afx_msg void OnItemexpandedBayProfileTree(NMHDR* pNMHDR, LRESULT* pResult);
	afx_msg void OnSideWizard();
	afx_msg void OnAisleWizard();
	afx_msg void OnMainWizard();
	afx_msg BOOL OnHelpInfo(HELPINFO* pHelpInfo);
	afx_msg void OnHelp();
	//}}AFX_MSG
	DECLARE_MESSAGE_MAP()
private:
	BOOL IsTreeCtrlEditMessage(WPARAM keyCode);
	void BuildBayProfileTree();
	CImageList m_ImageList;
	int LoadBayTypeList();
	int LoadBayProfileList();
	
	CMap<int, int, HTREEITEM, HTREEITEM&> m_MapBayTypeToTree;
	CTypedPtrArray<CObArray, CBayProfile*> m_BayProfileList;
};

//{{AFX_INSERT_LOCATION}}
// Microsoft Visual C++ will insert additional declarations immediately before the previous line.

#endif // !defined(AFX_BAYPROFILELISTDIALOG_H__96F48323_51C9_44C1_96D7_B34827563DFF__INCLUDED_)

// BayProfileLevelButton.cpp : implementation file
//

#include "stdafx.h"
#include "modal.h"
#include "BayProfileLevelButton.h"

#ifdef _DEBUG
#define new DEBUG_NEW
#undef THIS_FILE
static char THIS_FILE[] = __FILE__;
#endif

/////////////////////////////////////////////////////////////////////////////
// CBayProfileLevelButton


CBayProfileLevelButton::CBayProfileLevelButton()
{
}

CBayProfileLevelButton::~CBayProfileLevelButton()
{
}


BEGIN_MESSAGE_MAP(CBayProfileLevelButton, CButton)
	//{{AFX_MSG_MAP(CBayProfileLevelButton)
	ON_WM_LBUTTONDOWN()
	ON_WM_LBUTTONDBLCLK()
	//}}AFX_MSG_MAP
END_MESSAGE_MAP()

/////////////////////////////////////////////////////////////////////////////
// CBayProfileLevelButton message handlers

void CBayProfileLevelButton::DrawItem(LPDRAWITEMSTRUCT lpDrawItemStruct) 
{
	UINT uStyle = DFCS_BUTTONPUSH;
	
	// This code only works with buttons.
	ASSERT(lpDrawItemStruct->CtlType == ODT_BUTTON);
	
	// Draw the button frame.
	::DrawFrameControl(lpDrawItemStruct->hDC, &lpDrawItemStruct->rcItem, 
		DFC_BUTTON, uStyle);
	
	CDC cdc;
	cdc.Attach(lpDrawItemStruct->hDC);
	
	SetMapping(cdc);

	CPen pen, *pPrevPen;
	pen.CreatePen(PS_SOLID, 0, RGB(0,0,0));
	pPrevPen = cdc.SelectObject(&pen);

	CSize vpSize = cdc.GetViewportExt();
	CSize winSize = cdc.GetWindowExt();

	cdc.MoveTo(0, 0);
	cdc.LineTo(0, m_UprightHeight*10);
	cdc.MoveTo(m_BayWidth*10, 0);
	cdc.LineTo(m_BayWidth*10, m_UprightHeight*10);

	// assume they are sorted by ascending height
	for (int i=0; i < m_CrossbarList.GetSize(); ++i) {
		CBayProfileCrossbarInfo info = m_CrossbarList[i];
		CPen tempPen;
		COLORREF c;
		if (info.m_IsSelected)
			c = RGB(255, 0, 0);
		else
			c = RGB(0, 0, 0);

	 	tempPen.CreatePen(PS_SOLID, 0, c);
		cdc.SelectObject(&tempPen);

		if (info.m_IsHidden) {
			DrawDashedHorzLine(cdc, CPoint(0, info.m_Height*10), CPoint(m_BayWidth*10, info.m_Height*10));
		}
		else {
			if (info.m_Height != m_BayHeight) {
				cdc.MoveTo(0, info.m_Height*10);//-info.m_Thickness);
				cdc.LineTo(m_BayWidth*10, info.m_Height*10);//-info.m_Thickness);
			}
		}

		// Draw locations
		int locCount = info.m_LocationCount / info.m_LocationRowCount;

		int locWidth = (m_BayWidth*10 - locCount*2*info.m_LocationSpace*10) / locCount;
		/*
		if (locWidth < info.m_MinimumWidth) {
			locCount = m_BayWidth / (info.m_MinimumWidth+2*info.m_LocationSpace);
			locWidth = (m_BayWidth - locCount*2*info.m_LocationSpace) / locCount;
			if (locWidth <= 0)
				continue;
		}
		*/

		CRect locRect;
		locRect.bottom = info.m_Height*10 + 10;

		CBayProfileCrossbarInfo above;
		int aboveIdx = i+1;

		// Find the first non-hidden crossbar above this one or the top of the bay
		while (aboveIdx < m_CrossbarList.GetSize()) {
			above = m_CrossbarList[aboveIdx];
			if (! above.m_IsHidden)
				break;
			aboveIdx++;
		}

		if (aboveIdx < m_CrossbarList.GetSize()) {
			locRect.top = above.m_Height*10 - above.m_Thickness*10 - 1; //- info.m_Clearance*10 - 1;
			//if (info.m_Clearance == 0)
			//	locRect.top--;
		}
		else
			locRect.top = m_BayHeight*10 -1 ; //- info.m_Clearance*10 - 1;

		for (int j=0; j < locCount; ++j) {
			locRect.left = 5*(info.m_LocationSpace < 1) + info.m_LocationSpace*10 + info.m_LocationSpace*j*2*10 + j*locWidth;
			locRect.right = locRect.left + locWidth;
			if (j == locCount-1 && info.m_LocationSpace < 1)
				locRect.right = m_BayWidth*10 - 5;

			//if (locRect.right >= m_BayWidth-2)
			//	locRect.right = (m_BayWidth-2) - info.m_LocationSpace;
			if  (info.m_IsSelected)
				DrawBox(cdc, locRect, 0, TRUE);
			else
				DrawBox(cdc, locRect, 0, FALSE);
		}
			
	}

	cdc.SelectObject(pPrevPen);
	pen.DeleteObject();
	cdc.Detach();
	
}


void CBayProfileLevelButton::OnLButtonDown(UINT nFlags, CPoint point) 
{
	CDC *pCDC = this->GetWindowDC();

	SetMapping(*pCDC);
	
	CString temp;
	CPoint bayPt(point);
	// Convert the window point to the bay point based on the mapping above
	pCDC->DPtoLP(&bayPt, 1);
	temp.Format("Device: %d,%d, Bay: %d,%d", point.x, point.y, bayPt.x, bayPt.y);
	if (bayPt.x < 0 || bayPt.x > m_BayWidth*10) {
		CButton::OnLButtonDown(nFlags, point);
		return;
	}
	
	int level = -1;

	for (int i=0; i < m_CrossbarList.GetSize(); ++i) {
		if (bayPt.y < m_CrossbarList[i].m_Height*10) {
			level = i-1;
			break;
		}
	}
	
	if (level < 0)
		level = m_CrossbarList.GetSize()-1;

	if (SelectLevel(level) < 0)
		return;

	CButton::OnLButtonDown(nFlags, point);
}


void CBayProfileLevelButton::SetMapping(CDC &cdc)
{
	CRect r;
	GetClientRect(&r);

	m_LogicalBayWidth = r.Width();

	cdc.SetMapMode(MM_ANISOTROPIC);
	cdc.SetViewportOrg(10, r.bottom-10);
	cdc.SetWindowExt(m_BayWidth*10, m_BayHeight*10);
	cdc.SetViewportExt(r.Width()-20, -(r.Height()-20));
}

void CBayProfileLevelButton::DrawDashedHorzLine(CDC &cdc, const CPoint &startPt, const CPoint &endPt)
{
	for (int i=0; i < endPt.x - startPt.x; ++i) {
		if (i%2 == 0)
			cdc.MoveTo(startPt.x+i, startPt.y);
		else
			cdc.LineTo(startPt.x+i, startPt.y);
	}

}


void CBayProfileLevelButton::DrawBox(CDC &cdc, const CRect& r, int width, BOOL bDashed)
{
	CPen pen, *prevPen;

	if (bDashed)
		pen.CreatePen(PS_SOLID, width, RGB(255, 0, 0));
	else
		pen.CreatePen(PS_SOLID, width, RGB(0,0,255));

	prevPen = cdc.SelectObject(&pen);
	
	cdc.MoveTo(r.BottomRight());
	cdc.LineTo(r.right, r.top);
	cdc.LineTo(r.left, r.top);
	cdc.LineTo(r.left, r.bottom);
	cdc.LineTo(r.right, r.bottom);

	cdc.SelectObject(prevPen);
	pen.DeleteObject();	
}

int CBayProfileLevelButton::SelectLevel(int level)
{
	int oldSel, newSel;

	for (int i=0; i < m_CrossbarList.GetSize(); ++i) {
		if (i == level)
			newSel = i;
		if (m_CrossbarList[i].m_IsSelected)
			oldSel = i;
	}

	if (GetParent()->SendMessage(WM_SELECT_LEVEL, WPARAM(newSel), 0) < 0)
		return -1;

	m_CrossbarList[oldSel].m_IsSelected = FALSE;
	m_CrossbarList[newSel].m_IsSelected = TRUE;

	Invalidate(TRUE);

	return 0;

}


void CBayProfileLevelButton::OnLButtonDblClk(UINT nFlags, CPoint point) 
{	
	CDC *pCDC = this->GetWindowDC();

	SetMapping(*pCDC);
	
	CString temp;
	CPoint bayPt(point);
	// Convert the window point to the bay point based on the mapping above
	pCDC->DPtoLP(&bayPt, 1);
	temp.Format("Device: %d,%d, Bay: %d,%d", point.x, point.y, bayPt.x, bayPt.y);
	if (bayPt.x < 0 || bayPt.x > m_BayWidth*10) {
		CButton::OnLButtonDown(nFlags, point);
		return;
	}
	
	int level = -1;

	for (int i=0; i < m_CrossbarList.GetSize(); ++i) {
		if (bayPt.y < m_CrossbarList[i].m_Height*10) {
			level = i-1;
			break;
		}
	}
	
	if (level < 0)
		level = m_CrossbarList.GetSize()-1;

	if (SelectLevel(level) == 0)
		GetParent()->PostMessage(WM_DBLCLK_LEVEL, WPARAM(level), 0);

	CButton::OnLButtonDblClk(nFlags, point);
}

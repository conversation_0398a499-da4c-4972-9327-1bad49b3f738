#if !defined(AFX_GENERATEMOVESDIALOG_H__8127A1A3_2D0F_11D5_9ECC_00C04FAC149C__INCLUDED_)
#define AFX_GENERATEMOVESDIALOG_H__8127A1A3_2D0F_11D5_9ECC_00C04FAC149C__INCLUDED_

#include "DisplayResults.h"	// Added by ClassView
#include <afxtempl.h>
#include "Move.h"
#if _MSC_VER > 1000
#pragma once
#endif // _MSC_VER > 1000
// GenerateMovesDialog.h : header file
//

/////////////////////////////////////////////////////////////////////////////
// CGenerateMovesDialog dialog
class CChainCostSummary : public CObject {
public:
	int chainId;
	int count;
	double savings;
	double moveCost;
	double netSavings;
	double moveTime;
	CString status;
};

class CGenerateMovesDialog : public CDialog
{
// Construction
public:
	BOOL m_IntegratedOnly;

	BOOL m_Standalone;
	int m_FacilityDBId;
	CString m_FacilityName;

	CString m_ExportChainList;

	static UINT GetExistingMovesThread(LPVOID pParam);
	static UINT GenerateMovesThread(LPVOID pParam);
	static UINT GetOutboundMovesThread(LPVOID pParam);

	void ExportMoves();
	CGenerateMovesDialog(CWnd* pParent = NULL);   // standard constructor
	~CGenerateMovesDialog();
	static int Compare(const void **p1, const void **p2);
// Dialog Data
	//{{AFX_DATA(CGenerateMovesDialog)
	enum { IDD = IDD_GENERATE_MOVES };
	//}}AFX_DATA


// Overrides
	// ClassWizard generated virtual function overrides
	//{{AFX_VIRTUAL(CGenerateMovesDialog)
	protected:
	virtual void DoDataExchange(CDataExchange* pDX);    // DDX/DDV support
	//}}AFX_VIRTUAL

// Implementation
protected:

	// Generated message map functions
	//{{AFX_MSG(CGenerateMovesDialog)
	virtual void OnOK();
	virtual BOOL OnInitDialog();
	virtual void OnCancel();
	afx_msg BOOL OnHelpInfo(HELPINFO* pHelpInfo);
	afx_msg void OnHelp();
	//}}AFX_MSG
	DECLARE_MESSAGE_MAP()
	void OnExportMoves(WPARAM wParam, LPARAM lParam);
	void OnViewMoves(WPARAM wParam, LPARAM lParam);
	void OnCloseDisplay(WPARAM wParam, LPARAM lParam);
	void OnColorMoves(WPARAM wParam, LPARAM lParam);
private:
	int CheckForMultipleProdsInLocation();
	int CheckForDuplicateLocations();
	//void FormatExistingMoves(CStringArray &moveList);
	CMap<int, int, int, int> m_ResultSelectionMap;
	CMap<long, long, CChainCostSummary*, CChainCostSummary*> m_ChainCostMap;
	CMap<long, long, CTypedPtrArray<CObArray, CMove*>*, CTypedPtrArray<CObArray, CMove*>* > m_MoveMap;

	CTypedPtrArray<CObArray, CMove*> m_MoveList;
	void Format(CString &move);
	void ShowResults(CStringArray &moveList, int count);
	int GetFile();
	void DisplayMoves(CStringArray &moveList);
	CString m_FileName;
	CDisplayResults *m_ChainListDialog;

	BOOL m_InProcess;
	CMap<int, int, CString, CString> m_SectionDCMap;
	CMap<int, int, CString, CString> m_SectionWhseMap;
	CString m_DefaultDC;
	CString m_DefaultWhse;
	int m_TimeHorizonUnits;
	int m_TimeHorizonDuration;
};

//{{AFX_INSERT_LOCATION}}
// Microsoft Visual C++ will insert additional declarations immediately before the previous line.

#endif // !defined(AFX_GENERATEMOVESDIALOG_H__8127A1A3_2D0F_11D5_9ECC_00C04FAC149C__INCLUDED_)

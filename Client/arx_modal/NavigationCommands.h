// NavigationCommands.h: interface for the CNavigationCommands class.
//
//////////////////////////////////////////////////////////////////////

#if !defined(AFX_NAVIGATIONCOMMANDS_H__8E1EB053_DAEA_436B_AF37_D9C47961EBF6__INCLUDED_)
#define AFX_NAVIGATIONCOMMANDS_H__8E1EB053_DAEA_436B_AF37_D9C47961EBF6__INCLUDED_

#include "Commands.h"

#if _MSC_VER > 1000
#pragma once
#endif // _MSC_VER > 1000

class CNavigationCommands : public CCommands
{
public:
	static void TestFunction();

	
	CNavigationCommands();
	virtual ~CNavigationCommands();

	static void RegisterCommands();

	static void ShowWizard();
	static void DisplayCursorMenu();
	static void SucceedHelp();
	static void ProcessLogin();
	static void NewConnection();
	static void ModifyDrawing();

	// Obsolete
	//static void ChangeSlottingMode();
};

#endif // !defined(AFX_NAVIGATIONCOMMANDS_H__8E1EB053_DAEA_436B_AF37_D9C47961EBF6__INCLUDED_)

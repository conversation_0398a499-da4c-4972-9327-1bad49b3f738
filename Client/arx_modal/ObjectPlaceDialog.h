#if !defined(AFX_OBJECTPLACEDIALOG_H__BE6E9E81_FEF6_11D1_9BCB_0080C781D9DF__INCLUDED_)
#define AFX_OBJECTPLACEDIALOG_H__BE6E9E81_FEF6_11D1_9BCB_0080C781D9DF__INCLUDED_
#include "resource.h"
#include "RotationButton.h"
#if _MSC_VER >= 1000
#pragma once
#endif // _MSC_VER >= 1000
// ObjectPlaceDialog.h : header file
//

/////////////////////////////////////////////////////////////////////////////
// CObjectPlaceDialog dialog

class CObjectPlaceDialog : public CDialog
{
// Construction
public:
	int m_ObjectType;
	BOOL m_AddingMultiples;
	CObjectPlaceDialog(CWnd* pParent = NULL);   // standard constructor

	typedef enum {
		AddAisle,
		AddHotspot
	} enumObjectType;

// Dialog Data
	//{{AFX_DATA(CObjectPlaceDialog)
	enum { IDD = IDD_OBJPLACE_DIALOG };
	CRotationButton	m_RotationButton;
	CString	m_XCoordinate;
	CString	m_YCoordinate;
	CString	m_ZCoordinate;
	CString	m_Rotation;
	CString	m_Message;
	CString	m_Increment;
	CString	m_Name;
	CString	m_Width;
	CString	m_Length;
	CString	m_Height;
	//}}AFX_DATA


// Overrides
	// ClassWizard generated virtual function overrides
	//{{AFX_VIRTUAL(CObjectPlaceDialog)
	protected:
	virtual void DoDataExchange(CDataExchange* pDX);    // DDX/DDV support
	virtual BOOL OnCommand(WPARAM wParam, LPARAM lParam);
	//}}AFX_VIRTUAL

// Implementation
protected:

	// Generated message map functions
	//{{AFX_MSG(CObjectPlaceDialog)
	afx_msg void OnHelp();
	afx_msg void OnCancel();
	virtual BOOL OnInitDialog();
	afx_msg BOOL OnHelpInfo(HELPINFO* pHelpInfo);
	virtual void OnOK();
	//}}AFX_MSG
	DECLARE_MESSAGE_MAP()
};

//{{AFX_INSERT_LOCATION}}
// Microsoft Developer Studio will insert additional declarations immediately before the previous line.

#endif // !defined(AFX_OBJECTPLACEDIALOG_H__BE6E9E81_FEF6_11D1_9BCB_0080C781D9DF__INCLUDED_)

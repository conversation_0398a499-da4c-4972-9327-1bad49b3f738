#if !defined(AFX_PRODUCTGROUPASSIGNMENTDIALOG_H__808B9993_0F20_11D5_9EC8_00C04FAC149C__INCLUDED_)
#define AFX_PRODUCTGROUPASSIGNMENTDIALOG_H__808B9993_0F20_11D5_9EC8_00C04FAC149C__INCLUDED_

#if _MSC_VER > 1000
#pragma once
#endif // _MSC_VER > 1000
// ProductGroupAssignmentDialog.h : header file
//
#include "Resource.h"
#include "ProductGroup.h"
#include "ProductPack.h"

/////////////////////////////////////////////////////////////////////////////
// CProductGroupAssignmentDialog dialog

class CProductGroupAssignmentDialog : public CDialog
{
// Construction
	DECLARE_DYNCREATE(CProductGroupAssignmentDialog)

public:
	BOOL ValidateClose();
	CProductGroupAssignmentDialog(CWnd* pParent = NULL);   // standard constructor
	CTypedPtrArray<CObArray, CProductGroup*> *m_ProductGroupList;
	CMap<long, long, CString, CString> *m_ActualCountMap;
	CMap<long, long, CString, CString> *m_MaximumCountMap;
	
	static UINT AssignProductsThread(LPVOID pParam);
	static UINT CountAssignmentsThread(LPVOID pParam);
	static UINT GetUnassignedCountThread(LPVOID pParam);

// Dialog Data
	//{{AFX_DATA(CProductGroupAssignmentDialog)
	enum { IDD = IDD_PRODUCT_GROUP_ASSIGNMENT };
	CListCtrl	m_ProductGroupListCtrl;
	//}}AFX_DATA


// Overrides
	// ClassWizard generated virtual function overrides
	//{{AFX_VIRTUAL(CProductGroupAssignmentDialog)
	public:
	virtual BOOL Create(LPCTSTR lpszClassName, LPCTSTR lpszWindowName, DWORD dwStyle, const RECT& rect, CWnd* pParentWnd, UINT nID, CCreateContext* pContext = NULL);
	protected:
	virtual void DoDataExchange(CDataExchange* pDX);    // DDX/DDV support
	virtual void PostNcDestroy();
	//}}AFX_VIRTUAL

// Implementation
protected:

	// Generated message map functions
	//{{AFX_MSG(CProductGroupAssignmentDialog)
	virtual BOOL OnInitDialog();
	afx_msg void OnAssign();
	afx_msg void OnCount();
	afx_msg void OnViewProducts();
	afx_msg void OnContextMenu(CWnd* pWnd, CPoint point);
	afx_msg BOOL OnHelpInfo(HELPINFO* pHelpInfo);
	afx_msg void OnHelp();
	//}}AFX_MSG
	DECLARE_MESSAGE_MAP()
private:
	void BuildDisplayLine(CProductPack *product, CString &line);
	void DisplayCurrentCount(int stopIdx);
	BOOL m_FirstTime;
};

//{{AFX_INSERT_LOCATION}}
// Microsoft Visual C++ will insert additional declarations immediately before the previous line.

#endif // !defined(AFX_PRODUCTGROUPASSIGNMENTDIALOG_H__808B9993_0F20_11D5_9EC8_00C04FAC149C__INCLUDED_)

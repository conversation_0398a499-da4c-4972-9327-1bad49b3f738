// Report
//
//////////////////////////////////////////////////////////////////////

#include "stdafx.h"
#include "modal.h"
#include "ReportHelper.h"

#include "ControlService.h"

#ifdef _DEBUG
#undef THIS_FILE
static char THIS_FILE[]=__FILE__;
#define new DEBUG_NEW
#endif

extern CControlService controlService;

//////////////////////////////////////////////////////////////////////
// Construction/Destruction
//////////////////////////////////////////////////////////////////////

CReportHelper::CReportHelper()
{

}

CReportHelper::~CReportHelper()
{

}

void CReportHelper::OpenSavedRep() 
{ 
	ExecuteReports(0);

	return; 
}

void CReportHelper::RackAssignmentRep() 
{ 
	ExecuteReports(1);
	
	return; 
}

void CReportHelper::RackAssignmentDetailRep() 
{ 
	ExecuteReports(2);
	
	return; 
}

void CReportHelper::ProductGroupDefineRep() 
{ 
	ExecuteReports(3);
	
	return; 
}

void CReportHelper::ProductGroupLayoutRep() 
{ 
	ExecuteReports(4);
	
	return;	
}

void CReportHelper::ProductGroupFacingsRep() 
{ 
	ExecuteReports(5);
	
	return; 
}

void CReportHelper::ProductsLayoutAssignmentRep() 
{ 
	ExecuteReports(6);
	
	return;	
}

void CReportHelper::ProductsLayoutVarWidthLocRep() 
{ 
	ExecuteReports(7);
	
	return;
}

void CReportHelper::ProductsLayoutCaseReOrientRep() 
{ 
	ExecuteReports(8);
	
	return;
}

void CReportHelper::FacilityMoveChainsRep() 
{ 
	ExecuteReports(9);
	
	return;
}

void CReportHelper::LocationOutboundRep() 
{ 
	ExecuteReports(10);
	
	return;	
}

void CReportHelper::AssignmentOutboundRep() 
{ 
	ExecuteReports(11);
	
	return;	
}

void CReportHelper::ProductDetailRep() 
{ 
	ExecuteReports(12);
	
	return;	
}

void CReportHelper::CostAnalysisDetRep() 
{ 
	ExecuteReports(13);
	
	return;	
}

void CReportHelper::CostAnalysisSumRep() 
{ 
	ExecuteReports(14);
	
	return;
}

void CReportHelper::ProductsLayoutAssignmentByProductRep() 
{ 
	ExecuteReports(15);
	
	return;	
}

void CReportHelper::ProductsLayoutAssignmentByLocationRep() 
{ 
	ExecuteReports(16);
	
	return;
}

void CReportHelper::ProductGroupDefineByMovementRep() 
{ 
	ExecuteReports(17);
	
	return;
}

void CReportHelper::ProductGroupDefineByBOHRep() 
{ 
	ExecuteReports(18);
	
	return;
}

void CReportHelper::ProductGroupDefineByUOIRep() 
{ 
	ExecuteReports(19);
	
	return;
}

void CReportHelper::RackUsageSummaryRep() 
{ 
	ExecuteReports(20);
	
	return;
}

void CReportHelper::UnassignedProductsRep() 
{ 
	ExecuteReports(21);
	
	return;	
}

void CReportHelper::CapitalCostRejectionRep() 
{ 
	ExecuteReports(22);
	
	return;
}

void CReportHelper::ExecuteReports(int repNum)
{
	static STARTUPINFO si;
	static PROCESS_INFORMATION pi;
	CString reports_dir;
	
	si.cb = sizeof(si);                           // size of the structure for versioning
	si.lpReserved = NULL;						  // should be NULL
	si.lpDesktop = NULL;						  // Desktop
	si.lpTitle = "Optimize Report";           // Title of the window
	
    si.dwX =  si.dwY = STARTF_USEPOSITION;		  // Use default start up position
	si.dwXSize = si.dwYSize = STARTF_USESIZE;     // Use default start up size.
    si.dwXCountChars = si.dwYCountChars = STARTF_USECOUNTCHARS; // Console window buffer lengths
    si.dwFillAttribute = STARTF_USEFILLATTRIBUTE;  // use default background colors etc.
	si.dwFlags =  STARTF_USESHOWWINDOW;            // use show window to set the window state as in wShowWindow
    si.cbReserved2 = 0;							   // Should be 0
    si.lpReserved2 = NULL;						   // Should be NULL
	si.wShowWindow = SW_SHOW;
	static char command[256]; //Naveen 8Feb06 Increased the buffer size
	static char start_dir[255];  // MFS 4Feb06 Use char

	reports_dir.Format("%s\\bin", controlService.m_ClientHome);
	int dirLength = reports_dir.GetLength();
	dirLength++;  // MFS 4Feb06 Try passing correct length to GetBuffer()
	sprintf(start_dir, "%s", reports_dir.GetBuffer(dirLength));
	//reports_dir.ReleaseBuffer();

	sprintf(command,
			"%s\\OptimizeReports.exe /r%d /f%d /u%s /p%s /d%s", reports_dir, repNum, 
			controlService.GetCurrentFacilityDBId(),
			controlService.m_CurrentUserID,
			controlService.m_Password, 
			controlService.m_CurrentDatabase);	
		
	BOOL bCreated = CreateProcess(NULL, command, NULL, NULL, TRUE,
		NORMAL_PRIORITY_CLASS, NULL, start_dir, &si, &pi);
	//reports_dir.ReleaseBuffer();
	if (!bCreated) {
		AfxMessageBox("Report command failed!");
	}
	else {
		CloseHandle(pi.hProcess);
		CloseHandle(pi.hThread);
	}
	
}


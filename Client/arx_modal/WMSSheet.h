#if !defined(AFX_WMSSHEET_H__EA06C855_6D81_416A_987B_160952A1A2BC__INCLUDED_)
#define AFX_WMSSHEET_H__EA06C855_6D81_416A_987B_160952A1A2BC__INCLUDED_

#if _MSC_VER > 1000
#pragma once
#endif // _MSC_VER > 1000
// WMSSheet.h : header file
//
#include "WMSGroup.h"
#include "WMSImportPage.h"
#include "WMSExportPage.h"
#include "WMSFacilityInfo.h"

/////////////////////////////////////////////////////////////////////////////
// CWMSSheet

class CWMSSheet : public CPropertySheet
{
	DECLARE_DYNAMIC(CWMSSheet)

// Construction
public:
	CWMSSheet(UINT nIDCaption, CWnd* pParentWnd = NULL, UINT iSelectPage = 0);
	CWMSSheet(LPCTSTR pszCaption, CWnd* pParentWnd = NULL, UINT iSelectPage = 0);

// Attributes
public:
	CTypedPtrArray<CObArray, CWMSGroup*> m_GroupList;
	CMap<int, int, CWMSGroup*, CWMSGroup*> m_GroupMap;
	CTypedPtrArray<CObArray, CWMS*> m_WMSList;
	CMap<int, int, CWMS*, CWMS*> m_WMSMap;
//	CWMSImportPage *m_ImportPage;
//	CWMSExportPage *m_ExportPage;

	CTypedPtrArray<CObArray, CWMSFacilityInfo*> m_FacilityList;
	CMap<int, int, CWMSFacilityInfo*, CWMSFacilityInfo*> m_FacilityMap;
	CMap<int, int, CWMSFacilityInfo*, CWMSFacilityInfo*> m_SectionMap;


// Operations
public:

// Overrides
	// ClassWizard generated virtual function overrides
	//{{AFX_VIRTUAL(CWMSSheet)
	public:
	virtual BOOL OnInitDialog();
	protected:
	virtual BOOL OnCommand(WPARAM wParam, LPARAM lParam);
	//}}AFX_VIRTUAL

// Implementation
public:
	CString GetWMSGroupName(int wmsDBId);
	CButton *m_GroupButton;
	CButton *m_ReportButton;
	int LoadFacilityList();
	int LoadWMSList();
	virtual ~CWMSSheet();

	// Generated message map functions
protected:
	void OnWMS();
	void OnSummary();
	//{{AFX_MSG(CWMSSheet)
	afx_msg BOOL OnHelpInfo(HELPINFO* pHelpInfo);
	//}}AFX_MSG
	DECLARE_MESSAGE_MAP()
};

/////////////////////////////////////////////////////////////////////////////

//{{AFX_INSERT_LOCATION}}
// Microsoft Visual C++ will insert additional declarations immediately before the previous line.

#endif // !defined(AFX_WMSSHEET_H__EA06C855_6D81_416A_987B_160952A1A2BC__INCLUDED_)

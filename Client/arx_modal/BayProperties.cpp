// BayProperties.cpp : implementation file
//

#include "stdafx.h"
#include "modal.h"
#include "BayProperties.h"
#include "HelpService.h"
#include "WizardHelper.h"

#ifdef _DEBUG
#define new DEBUG_NEW
#undef THIS_FILE
static char THIS_FILE[] = __FILE__;
#endif

extern CHelpService helpService;

/////////////////////////////////////////////////////////////////////////////
// CBayProperties property page

IMPLEMENT_DYNCREATE(CBayProperties, CPropertyPage)

CBayProperties::CBayProperties() : CPropertyPage(CBayProperties::IDD)
{
	//{{AFX_DATA_INIT(CBayProperties)
	m_Description = _T("");
	m_Profile = _T("");
	//}}AFX_DATA_INIT
}

CBayProperties::~CBayProperties()
{
}

void CBayProperties::DoDataExchange(CDataExchange* pDX)
{
	CPropertyPage::DoDataExchange(pDX);
	//{{AFX_DATA_MAP(CBayProperties)
	DDX_Text(pDX, IDC_DESCRIPTION, m_Description);
	DDX_Text(pDX, IDC_PROFILE, m_Profile);
	//}}AFX_DATA_MAP
}


BEGIN_MESSAGE_MAP(CBayProperties, CPropertyPage)
	//{{AFX_MSG_MAP(CBayProperties)
	ON_WM_HELPINFO()
	ON_BN_CLICKED(IDC_VIEW_PROFILE, OnViewProfile)
	//}}AFX_MSG_MAP
END_MESSAGE_MAP()

/////////////////////////////////////////////////////////////////////////////
// CBayProperties message handlers

BOOL CBayProperties::OnHelpInfo(HELPINFO* pHelpInfo) 
{
	helpService.ShowFieldHelp(IDD, pHelpInfo->iCtrlId);
	return FALSE;
}

BOOL CBayProperties::OnNotify(WPARAM wParam, LPARAM lParam, LRESULT* pResult) 
{
	NMHDR* pNMHDR = (NMHDR*)lParam;

	if (pNMHDR->code == PSN_HELP) {
		helpService.ShowScreenHelp(IDD);
		return TRUE;
	}
	return CPropertyPage::OnNotify(wParam, lParam, pResult);
}

void CBayProperties::OnViewProfile() 
{
	CWizardHelper wizardHelper;

	wizardHelper.ShowBayWizard(m_pBayProfile);
}

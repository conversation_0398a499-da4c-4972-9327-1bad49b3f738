// BayProfileDimensionInfo.h: interface for the CBayProfileDimensionInfo class.
//
//////////////////////////////////////////////////////////////////////

#if !defined(AFX_BAYPROFILEDIMENSIONINFO_H__354C2A22_DE6B_4172_9864_76D578ABFEB2__INCLUDED_)
#define AFX_BAYPROFILEDIMENSIONINFO_H__354C2A22_DE6B_4172_9864_76D578ABFEB2__INCLUDED_

#if _MSC_VER > 1000
#pragma once
#endif // _MSC_VER > 1000

class CBayProfileDimensionInfo : public CObject  
{
public:
	CBayProfileDimensionInfo();
	virtual ~CBayProfileDimensionInfo();
	void operator=(const CBayProfileDimensionInfo& other);
	int m_BayType;
	double m_BayWidth;
	double m_BayDepth;
	double m_BayHeight;
	double m_PalletHeight;
	int m_PositionsDeep;
	double m_FlowDifference;
	double m_UprightWidth;
	double m_UprightHeight;
	int m_SelectPositions;
	int m_ReservePositions;
	double m_SelectPositionHeight;
	double m_ReservePositionHeight;
	double m_StackWidth;
	double m_StackDepth;
	double m_Overhang;
	double m_PalletSpace;
	double m_Clearance;
};

#endif // !defined(AFX_BAYPROFILEDIMENSIONINFO_H__354C2A22_DE6B_4172_9864_76D578ABFEB2__INCLUDED_)

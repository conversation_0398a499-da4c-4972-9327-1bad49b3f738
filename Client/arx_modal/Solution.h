// Solution.h: interface for the CSolution class.
//
//////////////////////////////////////////////////////////////////////

#if !defined(AFX_SOLUTION_H__6675291B_8401_11D4_91DF_00400542E36B__INCLUDED_)
#define AFX_SOLUTION_H__6675291B_8401_11D4_91DF_00400542E36B__INCLUDED_

#if _MSC_VER > 1000
#pragma once
#endif // _MSC_VER > 1000

class CSolution  
{
public:
	void Parse(CString line);
	CSolution();
	virtual ~CSolution();
	CSolution& operator=(const CSolution & other);

	int m_SolutionDBID;
	int m_ProductDBID;
	int m_LocationDBID;
	int m_LevelDBID;
	int m_CaseQuantity;
	double m_ProductWeight;
	int m_UnitOfIssue;
	int m_NumberInPallet;
	int m_CasePack;
	int m_InnerPack;
	int m_IsPrimary;
	double m_RotatedWidth;
	double m_RotatedLength;
	double m_RotatedHeight;
	int m_Origin;

	typedef enum {
		Baseline = 0,
		Optimize = 1
	} enumOrigin;
};

#endif // !defined(AFX_SOLUTION_H__6675291B_8401_11D4_91DF_00400542E36B__INCLUDED_)

// BusyWaitCursor.cpp: implementation of the CBusyWaitCursor class.
//
//////////////////////////////////////////////////////////////////////

#include "stdafx.h"
#include "modal.h"
#include "BusyWaitCursor.h"

#ifdef _DEBUG
#undef THIS_FILE
static char THIS_FILE[]=__FILE__;
#define new DEBUG_NEW
#endif

//////////////////////////////////////////////////////////////////////
// Construction/Destruction
//////////////////////////////////////////////////////////////////////

CBusyWaitCursor::CBusyWaitCursor()
{
	HCURSOR hCursor = ::LoadCursor(NULL, MAKEINTRESOURCE(IDC_APPSTARTING));
	if (hCursor != NULL)
		::SetCursor(hCursor);
}

CBusyWaitCursor::~CBusyWaitCursor()
{
	HCURSOR hCursor = ::LoadCursor(NULL, MAKEINTRESOURCE(IDC_ARROW));
	if (hCursor != NULL)
		::SetCursor(hCursor);
}

void CBusyWaitCursor::Restore()
{
	HCURSOR hCursor = ::LoadCursor(NULL, MAKEINTRESOURCE(IDC_APPSTARTING));
	if (hCursor != NULL)
		::SetCursor(hCursor);
}

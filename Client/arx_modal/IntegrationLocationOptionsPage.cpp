// IntegrationLocationOptionsPage.cpp : implementation file
//

#include "stdafx.h"
#include "modal.h"
#include "IntegrationLocationOptionsPage.h"
#include "HelpService.h"

#ifdef _DEBUG
#define new DEBUG_NEW
#undef THIS_FILE
static char THIS_FILE[] = __FILE__;
#endif

extern CHelpService helpService;
/////////////////////////////////////////////////////////////////////////////
// CIntegrationLocationOptionsPage property page

IMPLEMENT_DYNCREATE(CIntegrationLocationOptionsPage, CPropertyPage)

CIntegrationLocationOptionsPage::CIntegrationLocationOptionsPage() : CPropertyPage(CIntegrationLocationOptionsPage::IDD)
{
	//{{AFX_DATA_INIT(CIntegrationLocationOptionsPage)
	m_AutoConfirm = FALSE;
	m_FullExport = FALSE;
	m_InboundPrompt = FALSE;
	m_NoUpdate = FALSE;
	m_OutboundPrompt = FALSE;
	m_SkipLocation = FALSE;
	//}}AFX_DATA_INIT
}

CIntegrationLocationOptionsPage::~CIntegrationLocationOptionsPage()
{
}

void CIntegrationLocationOptionsPage::DoDataExchange(CDataExchange* pDX)
{
	CPropertyPage::DoDataExchange(pDX);
	//{{AFX_DATA_MAP(CIntegrationLocationOptionsPage)
	DDX_Check(pDX, IDC_AUTO_CONFIRM, m_AutoConfirm);
	DDX_Check(pDX, IDC_FULL_EXPORT, m_FullExport);
	DDX_Check(pDX, IDC_INBOUND_PROMPT, m_InboundPrompt);
	DDX_Check(pDX, IDC_NO_UPDATE, m_NoUpdate);
	DDX_Check(pDX, IDC_OUTBOUND_PROMPT, m_OutboundPrompt);
	DDX_Check(pDX, IDC_SKIP, m_SkipLocation);
	//}}AFX_DATA_MAP
}


BEGIN_MESSAGE_MAP(CIntegrationLocationOptionsPage, CPropertyPage)
	//{{AFX_MSG_MAP(CIntegrationLocationOptionsPage)
	ON_BN_CLICKED(IDC_AUTO_CONFIRM, OnAutoConfirm)
	ON_BN_CLICKED(IDC_FULL_EXPORT, OnFullExport)
	ON_BN_CLICKED(IDC_INBOUND_PROMPT, OnInboundPrompt)
	ON_BN_CLICKED(IDC_NO_UPDATE, OnNoUpdate)
	ON_BN_CLICKED(IDC_OUTBOUND_PROMPT, OnOutboundPrompt)
	ON_BN_CLICKED(IDC_SKIP, OnSkip)
	ON_WM_HELPINFO()
	//}}AFX_MSG_MAP
END_MESSAGE_MAP()

/////////////////////////////////////////////////////////////////////////////
// CIntegrationLocationOptionsPage message handlers

BOOL CIntegrationLocationOptionsPage::OnInitDialog() 
{
	CPropertyPage::OnInitDialog();

	OnSkip();

	UpdateData(FALSE);

	return TRUE;  // return TRUE unless you set the focus to a control
	              // EXCEPTION: OCX Property Pages should return FALSE
}

void CIntegrationLocationOptionsPage::OnAutoConfirm() 
{
	// TODO: Add your control notification handler code here
	
}

void CIntegrationLocationOptionsPage::OnFullExport() 
{
	// TODO: Add your control notification handler code here
	
}

void CIntegrationLocationOptionsPage::OnInboundPrompt() 
{
	// TODO: Add your control notification handler code here
	
}

void CIntegrationLocationOptionsPage::OnNoUpdate() 
{
	UpdateData(TRUE);
	if (m_NoUpdate)
		GetDlgItem(IDC_AUTO_CONFIRM)->EnableWindow(FALSE);
	else
		GetDlgItem(IDC_AUTO_CONFIRM)->EnableWindow(TRUE);

}

void CIntegrationLocationOptionsPage::OnOutboundPrompt() 
{
	// TODO: Add your control notification handler code here
	
}

void CIntegrationLocationOptionsPage::OnSkip() 
{
	UpdateData(TRUE);
	
	if (m_SkipLocation) {
		GetDlgItem(IDC_AUTO_CONFIRM)->EnableWindow(FALSE);
		GetDlgItem(IDC_FULL_EXPORT)->EnableWindow(FALSE);
		GetDlgItem(IDC_INBOUND_PROMPT)->EnableWindow(FALSE);
		GetDlgItem(IDC_NO_UPDATE)->EnableWindow(FALSE);
		GetDlgItem(IDC_OUTBOUND_PROMPT)->EnableWindow(FALSE);
	}
	else {
		GetDlgItem(IDC_AUTO_CONFIRM)->EnableWindow(TRUE);
		GetDlgItem(IDC_FULL_EXPORT)->EnableWindow(TRUE);
		GetDlgItem(IDC_INBOUND_PROMPT)->EnableWindow(TRUE);
		GetDlgItem(IDC_NO_UPDATE)->EnableWindow(TRUE);
		GetDlgItem(IDC_OUTBOUND_PROMPT)->EnableWindow(TRUE);
	}
}

BOOL CIntegrationLocationOptionsPage::OnHelpInfo(HELPINFO* pHelpInfo) 
{
	helpService.ShowFieldHelp(IDD, pHelpInfo->iCtrlId);
	return FALSE;

}

BOOL CIntegrationLocationOptionsPage::OnNotify(WPARAM wParam, LPARAM lParam, LRESULT* pResult) 
{
	NMHDR* pNMHDR = (NMHDR*)lParam;

	if (pNMHDR->code == PSN_HELP) {
		helpService.ShowScreenHelp(IDD);
		return TRUE;
	}
	
	return CPropertyPage::OnNotify(wParam, lParam, pResult);
}

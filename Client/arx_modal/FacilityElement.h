// FacilityElement.h: interface for the CFacilityElement class.
//
//////////////////////////////////////////////////////////////////////

#if !defined(AFX_FACILITYELEMENT_H__D0DF6249_F366_44BA_A319_139B6E1B0AC2__INCLUDED_)
#define AFX_FACILITYELEMENT_H__D0DF6249_F366_44BA_A319_139B6E1B0AC2__INCLUDED_

#if _MSC_VER > 1000
#pragma once
#endif // _MSC_VER > 1000

class CFacilityElement : public CObject  
{
public:
	//virtual int Parse(CString &line) = 0;
	CFacilityElement();
	virtual ~CFacilityElement();

	long m_DBId;
	CString m_Description;
	int m_Status;			// 1 = In Database; 2 = Loaded; 4 = Deleted
	int m_ParentIndex;		// index into the parent-level array (depends on parent)
	int m_TopIndex;			// index into the top-level array (always facility)

	CFacilityElement *m_Parent;

	typedef enum {
		feNewElement = 0,
		feElementInDatabase = 1,
		feElementLoaded = 2,
		feElementDeleted = 3
	} feElementStatusEnum;

	
};

#endif // !defined(AFX_FACILITYELEMENT_H__D0DF6249_F366_44BA_A319_139B6E1B0AC2__INCLUDED_)

// BayProfileLocationProperties.cpp : implementation file
//

#include "stdafx.h"
#include "modal.h"
#include "BayProfileLocationProperties.h"
#include "BayProfileLocationPage.h"
#include "Constants.h"
#include "UtilityHelper.h"
#include "BayProfileSheet.h"
#include "HelpService.h"

#ifdef _DEBUG
#define new DEBUG_NEW
#undef THIS_FILE
static char THIS_FILE[] = __FILE__;
#endif

extern CHelpService helpService;
extern CUtilityHelper utilityHelper;
/////////////////////////////////////////////////////////////////////////////
// CBayProfileLocationProperties dialog


CBayProfileLocationProperties::CBayProfileLocationProperties(CWnd* pParent /*=NULL*/)
	: CDialog(CBayProfileLocationProperties::IDD, pParent)
{
	//{{AFX_DATA_INIT(CBayProfileLocationProperties)
	m_FacingGap = _T("");
	m_FacingSnap = _T("");
	m_MinimumWidth = _T("");
	m_ProductGap = _T("");
	m_ProductSnap = _T("");
	m_LocationSpace = _T("");
	m_LocationsAcross = _T("");
	m_LocationsDeep = _T("");
	//}}AFX_DATA_INIT
}


void CBayProfileLocationProperties::DoDataExchange(CDataExchange* pDX)
{
	CDialog::DoDataExchange(pDX);
	//{{AFX_DATA_MAP(CBayProfileLocationProperties)
	DDX_Control(pDX, IDC_LOCATION_USAGE_LIST, m_LocationUsageListCtrl);
	DDX_Control(pDX, IDC_HANDLING_METHOD_LIST, m_HandlingListCtrl);
	DDX_Text(pDX, IDC_FACING_GAP, m_FacingGap);
	DDX_Text(pDX, IDC_FACING_SNAP, m_FacingSnap);
	DDX_Text(pDX, IDC_MINIMUM_WIDTH, m_MinimumWidth);
	DDX_Text(pDX, IDC_PRODUCT_GAP, m_ProductGap);
	DDX_Text(pDX, IDC_PRODUCT_SNAP, m_ProductSnap);
	DDX_Text(pDX, IDC_LOCATION_SPACE, m_LocationSpace);
	DDX_Text(pDX, IDC_LOCATIONS_ACROSS, m_LocationsAcross);
	DDX_Text(pDX, IDC_LOCATIONS_DEEP, m_LocationsDeep);
	//}}AFX_DATA_MAP
}


BEGIN_MESSAGE_MAP(CBayProfileLocationProperties, CDialog)
	//{{AFX_MSG_MAP(CBayProfileLocationProperties)
	ON_BN_CLICKED(IDHELP, OnHelp)
	ON_WM_HELPINFO()
	//}}AFX_MSG_MAP
END_MESSAGE_MAP()

/////////////////////////////////////////////////////////////////////////////
// CBayProfileLocationProperties message handlers

BOOL CBayProfileLocationProperties::OnInitDialog() 
{
	CDialog::OnInitDialog();
	
	CBayProfileSheet *pSheet = (CBayProfileSheet *)GetParent();
	m_pBayProfile = pSheet->m_pBayProfile;

	CLevelProfile *pLevelProfile = m_pBayProfile->m_LevelProfileList[m_CurrentLevel];

	BuildHandlingList();
	BuildLocationUsageList();

	m_FacingGap.Format("%.2f", pLevelProfile->m_FacingGap);
	m_FacingSnap.Format("%.2f", pLevelProfile->m_FacingSnap);
	m_ProductGap.Format("%.2f", pLevelProfile->m_ProductGap);
	m_ProductSnap.Format("%.2f", pLevelProfile->m_ProductSnap);

	m_LocationsDeep.Format("%d", pLevelProfile->m_LocationRowCount);
	m_LocationsAcross.Format("%d", 
		pLevelProfile->m_LocationProfileList.GetSize()/pLevelProfile->m_LocationRowCount);
	m_MinimumWidth.Format("%.2f", pLevelProfile->m_MinimumLocWidth);

	if (pLevelProfile->m_LocationProfileList.GetSize() > 0) {
		CLocationProfile *pLocProfile = pLevelProfile->m_LocationProfileList[0];
		m_LocationSpace.Format("%.2f", pLocProfile->m_LocationSpace);
		m_LocationUsageListCtrl.SetCurSel(pLocProfile->m_IsSelect);
		if (pLocProfile->m_HandlingMethod == PALLET_HANDLING)
			m_HandlingListCtrl.SetCurSel(1);
		else
			m_HandlingListCtrl.SetCurSel(0);
	}
	else {
		m_LocationSpace = "0.00";
		m_LocationUsageListCtrl.SetCurSel(0);
		if (pLevelProfile->m_Baytype == BAYTYPE_BIN || pLevelProfile->m_Baytype == BAYTYPE_CASEFLOW)
			m_HandlingListCtrl.SetCurSel(0);
		else
			m_HandlingListCtrl.SetCurSel(1);
	}
		 
	SetControlStates();

	UpdateData(FALSE);

	return TRUE;  // return TRUE unless you set the focus to a control
	              // EXCEPTION: OCX Property Pages should return FALSE
}

void CBayProfileLocationProperties::OnOK() 
{
	/* WARNING! Anything you change in here may need to be changed in the following places:
		1) BayProfileLocationProperties->OnOk
		2) BayProfileLevelButton->DrawItem
		3) LevelLocationButton->DrawItem

		Great code reuse, huh?
	*/

	UpdateData(TRUE);

	CWaitCursor cwc;

	CLevelProfile *pLevelProfile = m_pBayProfile->m_LevelProfileList[m_CurrentLevel];
	CString txt;
	txt = (pLevelProfile->m_Baytype == BAYTYPE_BIN) ? "Number of Locations Across" : "Number of Locations";

	if (! ValidateNumeric(DT_INT, IDC_LOCATIONS_ACROSS, txt, m_LocationsAcross))
		return;

	if (atoi(m_LocationsAcross) < 0) {
		AfxMessageBox("Please enter a value greater than 0 for the " + txt + " field.");
		CEdit *pEdit = (CEdit *)GetDlgItem(IDC_LOCATIONS_ACROSS);
		pEdit->SetSel(0, -1);
		pEdit->SetFocus();
		return;
	}

	if (! ValidateNumeric(DT_INT, IDC_LOCATIONS_DEEP, "Number of Locations Deep", m_LocationsDeep))
		return;

	if (atoi(m_LocationsDeep) < 0) {
		AfxMessageBox("Please enter a value greater than 0 for the Number of Locations Deep field.");
		CEdit *pEdit = (CEdit *)GetDlgItem(IDC_LOCATIONS_DEEP);
		pEdit->SetSel(0, -1);
		pEdit->SetFocus();
		return;
	}

	if (! ValidateNumeric(DT_FLOAT, IDC_MINIMUM_WIDTH, "Minimum Location Width", m_MinimumWidth))
		return;

	if (! ValidateNumeric(DT_FLOAT, IDC_LOCATION_SPACE, "Surrounding Space", m_LocationSpace))
		return;

	if (pLevelProfile->m_IsVariableWidthAllowed) {
		if (! ValidateNumeric(DT_FLOAT, IDC_PRODUCT_GAP, "Product Gap", m_ProductGap))
			return;
		
		if (! ValidateNumeric(DT_FLOAT, IDC_PRODUCT_SNAP, "Product Snap", m_ProductSnap))
			return;	
		
		if (! ValidateNumeric(DT_FLOAT, IDC_FACING_GAP, "Facing Gap", m_FacingGap))
			return;
		
		if (! ValidateNumeric(DT_FLOAT, IDC_FACING_SNAP, "Facing Snap", m_FacingSnap))
			return;
	}

	double minWidth = atof(m_MinimumWidth);
	pLevelProfile->m_MinimumLocWidth = minWidth;
	pLevelProfile->m_FacingGap = atof(m_FacingGap);
	pLevelProfile->m_FacingSnap = atof(m_FacingSnap);
	pLevelProfile->m_ProductGap = atof(m_ProductGap);
	pLevelProfile->m_ProductSnap = atof(m_ProductSnap);


	int locsDeep = atoi(m_LocationsDeep);
	int locsAcross = atoi(m_LocationsAcross);
	pLevelProfile->m_LocationRowCount = locsDeep;
	int locCount = locsDeep * locsAcross;
	double locSpace = atof(m_LocationSpace);

	double locHeight, locWidth, locDepth;
	//if drivein or floor, then location height is the select position height
	// of course we'll never get here because we don't show this page for floor or drivein currently
	if ((m_pBayProfile->m_BayType == BAYTYPE_DRIVEIN) || (m_pBayProfile->m_BayType == BAYTYPE_FLOOR)) {
		locHeight = pLevelProfile->m_SelectPositionHeight;
		locDepth = pLevelProfile->m_StackDepth;
		locWidth = pLevelProfile->m_StackWidth;
	}
	else {
		locDepth = m_pBayProfile->m_Depth + pLevelProfile->m_Overhang;
		if (m_pBayProfile->m_BayType == BAYTYPE_PALLET) {
			if (m_pBayProfile->m_PalletDepth > 1)
				locDepth -= m_pBayProfile->m_PalletSpace;
		}
		// To get the height, find the first crossbar above this location that
		// is not hidden; if this one is the top non-hidden one, use the bay height
		int aboveIdx = m_CurrentLevel+1;
		CLevelProfile *pAbove = NULL;
		while (aboveIdx < m_pBayProfile->m_LevelProfileList.GetSize()) {
			pAbove = m_pBayProfile->m_LevelProfileList[aboveIdx];
			if (! pAbove->m_IsBarHidden)
				break;
			aboveIdx++;
			pAbove = NULL;
		}
		
		if (pAbove != NULL)
			locHeight = (pAbove->m_Coordinates.m_Z - pAbove->m_Thickness) - 
			pLevelProfile->m_Coordinates.m_Z;
		else
			locHeight = m_pBayProfile->m_Height - pLevelProfile->m_Coordinates.m_Z;
	}
	
	double totalLocSpace = 2*locsAcross*locSpace;
	BOOL bMultiRows = FALSE;
	
	if (locCount > 0) {
		
		locWidth = (m_pBayProfile->m_Width-totalLocSpace)/locsAcross;
		if (locWidth < 1) {
			AfxMessageBox("The specified number of locations cannot fit in the width of the bay.  "
				"Decrease the location space or the number of locations across.");
			CEdit *pEdit = (CEdit *)GetDlgItem(IDC_LOCATIONS_ACROSS);
			pEdit->SetSel(0,-1);
			pEdit->SetFocus();
			return;
		}
		
		// Variable-Depth logic used for bin carousel
		locDepth = (double)(m_pBayProfile->m_Depth+pLevelProfile->m_Overhang) / (double)locsDeep;
		
		if (locDepth <= 1) {
			AfxMessageBox("The bay is not deep enough to hold the specified number of locations.  "
				"Decrease the number of locations deep.");
			CEdit *pEdit = (CEdit *)GetDlgItem(IDC_LOCATIONS_DEEP);
			pEdit->SetSel(0,-1);
			pEdit->SetFocus();
			return;
		}		
	}

	for (int i=0; i < pLevelProfile->m_LocationProfileList.GetSize(); ++i)
		delete pLevelProfile->m_LocationProfileList[i];

	pLevelProfile->m_LocationProfileList.RemoveAll();
	int xCoordinate = 0;

	int rowNum;

	for (i=0; i < locCount; ++i) {
		// add one location
		CLocationProfile *pLocProfile = new CLocationProfile();
		pLevelProfile->m_LocationProfileList.Add(pLocProfile);

		pLocProfile->m_LocationSpace = locSpace;
		pLocProfile->m_Description.Format("%d", i+1);
		pLocProfile->m_HandlingMethod = m_HandlingListCtrl.GetItemData(m_HandlingListCtrl.GetCurSel());
		pLocProfile->m_IsSelect = m_LocationUsageListCtrl.GetItemData(m_LocationUsageListCtrl.GetCurSel());
		pLocProfile->m_LevelProfileDBId = pLevelProfile->m_LevelProfileDBId;
		pLocProfile->m_WeightCapacity = pLevelProfile->m_WeightCapacity/locCount;

		pLocProfile->m_Height = locHeight;
		pLocProfile->m_Depth = locDepth;
		pLocProfile->m_Width = locWidth;
		
		if (locsDeep > 1) {				// we are using multiple depth
			rowNum = i / locsAcross;
			if (i % locsAcross == 0)	// we are starting a new row so reset the xcoordinate back to 0
				xCoordinate = 0;
		}
		else
			rowNum = 0;
		
		// add the space to the right
		xCoordinate += int(locSpace);		// todo: double-check this
		pLocProfile->m_Coordinates.m_X = xCoordinate;
		
		// add the location width and the space to the left
		xCoordinate += int(locWidth + locSpace);
		pLocProfile->m_Coordinates.m_Y = locDepth*rowNum;
		pLocProfile->m_Coordinates.m_Z = pLevelProfile->m_Coordinates.m_Z;
	}
	

	CDialog::OnOK();
}


void CBayProfileLocationProperties::BuildLocationUsageList()
{
	m_LocationUsageListCtrl.ResetContent();
	int nItem = m_LocationUsageListCtrl.AddString("Reserve");
	m_LocationUsageListCtrl.SetItemData(nItem, 0);
	nItem = m_LocationUsageListCtrl.AddString("Select");
	m_LocationUsageListCtrl.SetItemData(nItem, 1);

	CRect r;
	m_LocationUsageListCtrl.GetWindowRect(&r);
	m_LocationUsageListCtrl.SetWindowPos(NULL, 0, 0, r.Width(), r.Height()*3, SWP_NOMOVE|SWP_NOZORDER);
}

void CBayProfileLocationProperties::BuildHandlingList()
{
	m_HandlingListCtrl.ResetContent();

	// Can you have pallet handling into a bin or case flow?
	// For now allow all combinations

	int nItem = m_HandlingListCtrl.AddString("Case");
	m_HandlingListCtrl.SetItemData(nItem, CASE_HANDLING);
	nItem = m_HandlingListCtrl.AddString("Pallet");
	m_HandlingListCtrl.SetItemData(nItem, PALLET_HANDLING);

	CRect r;
	m_HandlingListCtrl.GetWindowRect(&r);
	m_HandlingListCtrl.SetWindowPos(NULL, 0, 0, r.Width(), r.Height()*3, SWP_NOMOVE|SWP_NOZORDER);
}


void CBayProfileLocationProperties::SetControlStates()
{
	CLevelProfile *pLevelProfile = m_pBayProfile->m_LevelProfileList[m_CurrentLevel];

	if (pLevelProfile->m_IsVariableWidthAllowed) {
		// Enable gaps and snaps		
		GetDlgItem(IDC_MINIMUM_WIDTH)->ShowWindow(SW_SHOW);
		GetDlgItem(IDC_MINIMUM_WIDTH_STATIC)->ShowWindow(SW_SHOW);

		GetDlgItem(IDC_PRODUCT_GAP)->ShowWindow(SW_SHOW);
		GetDlgItem(IDC_PRODUCT_SNAP)->ShowWindow(SW_SHOW);
		GetDlgItem(IDC_FACING_GAP)->ShowWindow(SW_SHOW);
		GetDlgItem(IDC_FACING_SNAP)->ShowWindow(SW_SHOW);

		GetDlgItem(IDC_PRODUCT_STATIC)->ShowWindow(SW_SHOW);
		GetDlgItem(IDC_SNAP_STATIC)->ShowWindow(SW_SHOW);
		GetDlgItem(IDC_GAP_STATIC)->ShowWindow(SW_SHOW);
		GetDlgItem(IDC_FACING_STATIC)->ShowWindow(SW_SHOW);
		GetDlgItem(IDC_DIVIDER_STATIC)->ShowWindow(SW_SHOW);
	}
	else {
		GetDlgItem(IDC_MINIMUM_WIDTH)->ShowWindow(SW_HIDE);
		GetDlgItem(IDC_MINIMUM_WIDTH_STATIC)->ShowWindow(SW_HIDE);

		GetDlgItem(IDC_PRODUCT_GAP)->ShowWindow(SW_HIDE);
		GetDlgItem(IDC_PRODUCT_SNAP)->ShowWindow(SW_HIDE);
		GetDlgItem(IDC_FACING_GAP)->ShowWindow(SW_HIDE);
		GetDlgItem(IDC_FACING_SNAP)->ShowWindow(SW_HIDE);
		
		GetDlgItem(IDC_GAP_STATIC)->ShowWindow(SW_HIDE);
		GetDlgItem(IDC_SNAP_STATIC)->ShowWindow(SW_HIDE);
		GetDlgItem(IDC_FACING_STATIC)->ShowWindow(SW_HIDE);
		GetDlgItem(IDC_PRODUCT_STATIC)->ShowWindow(SW_HIDE);
		GetDlgItem(IDC_DIVIDER_STATIC)->ShowWindow(SW_HIDE);
	}

	if (pLevelProfile->m_Baytype != BAYTYPE_BIN) {
		m_LocationsDeep = "1";
		GetDlgItem(IDC_LOCATIONS_DEEP)->ShowWindow(SW_HIDE);
		GetDlgItem(IDC_LOCATIONS_DEEP_STATIC)->ShowWindow(SW_HIDE);
		CStatic *pStatic = (CStatic *)GetDlgItem(IDC_LOCATIONS_ACROSS_STATIC);
		pStatic->SetWindowText("Number of Locations");
	}
	else {
		GetDlgItem(IDC_LOCATIONS_DEEP_STATIC)->ShowWindow(SW_SHOW);
		GetDlgItem(IDC_LOCATIONS_DEEP)->ShowWindow(SW_SHOW);
		CStatic *pStatic = (CStatic *)GetDlgItem(IDC_LOCATIONS_ACROSS_STATIC);
		pStatic->SetWindowText("Number of Locations Across");
	}

	if (m_pBayProfile->m_Active) {
		GetDlgItem(IDC_MINIMUM_WIDTH)->EnableWindow(FALSE);

		GetDlgItem(IDC_PRODUCT_GAP)->EnableWindow(FALSE);
		GetDlgItem(IDC_PRODUCT_SNAP)->EnableWindow(FALSE);
		GetDlgItem(IDC_FACING_GAP)->EnableWindow(FALSE);
		GetDlgItem(IDC_FACING_SNAP)->EnableWindow(FALSE);
		
		GetDlgItem(IDC_LOCATIONS_DEEP)->EnableWindow(FALSE);
		GetDlgItem(IDC_LOCATIONS_ACROSS)->EnableWindow(FALSE);
		GetDlgItem(IDC_LOCATION_SPACE)->EnableWindow(FALSE);
	}
	else {
		GetDlgItem(IDC_MINIMUM_WIDTH)->EnableWindow(TRUE);

		GetDlgItem(IDC_PRODUCT_GAP)->EnableWindow(TRUE);
		GetDlgItem(IDC_PRODUCT_SNAP)->EnableWindow(TRUE);
		GetDlgItem(IDC_FACING_GAP)->EnableWindow(TRUE);
		GetDlgItem(IDC_FACING_SNAP)->EnableWindow(TRUE);
		
		GetDlgItem(IDC_LOCATIONS_DEEP)->EnableWindow(TRUE);
		GetDlgItem(IDC_LOCATIONS_ACROSS)->EnableWindow(TRUE);
		GetDlgItem(IDC_LOCATION_SPACE)->EnableWindow(TRUE);
	}

}

BOOL CBayProfileLocationProperties::ValidateNumeric(int type, int fieldId, const CString &fieldName, CString &fieldValue)
{
	if (type == DT_INT) {
		if (! utilityHelper.IsInteger(fieldValue)) {
			CString temp;
			temp.Format("Please enter a valid integer value for %s", fieldName);
			AfxMessageBox(temp);
			CEdit *pEdit = (CEdit *)GetDlgItem(fieldId);
			pEdit->SetSel(0,-1);
			pEdit->SetFocus();
			return FALSE;
		}
	}
	else if (type == DT_FLOAT) {
		if (! utilityHelper.IsFloat(fieldValue)) {
			CString temp;
			temp.Format("Please enter a valid decimal value for %s", fieldName);
			AfxMessageBox(temp);
			CEdit *pEdit = (CEdit *)GetDlgItem(fieldId);
			pEdit->SetSel(0,-1);
			pEdit->SetFocus();
			return FALSE;
		}
	}
	
	return TRUE;

}

BOOL CBayProfileLocationProperties::OnHelpInfo(HELPINFO* pHelpInfo) 
{
	helpService.ShowFieldHelp(IDD, pHelpInfo->iCtrlId);

	return FALSE;

}

void CBayProfileLocationProperties::OnHelp() 
{
	helpService.ShowScreenHelp(IDD);

	return;

}
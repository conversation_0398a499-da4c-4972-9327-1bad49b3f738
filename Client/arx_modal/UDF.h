// UDF.h: interface for the CUDF class.
//
//////////////////////////////////////////////////////////////////////

#if !defined(AFX_UDF_H__373DB633_A9C2_11D4_9EBD_00C04FAC149C__INCLUDED_)
#define AFX_UDF_H__373DB633_A9C2_11D4_9EBD_00C04FAC149C__INCLUDED_

#if _MSC_VER > 1000
#pragma once
#endif // _MSC_VER > 1000

#include "Constants.h"

class CUDF : public CObject  
{
public:
	CString GetTypeAsString();
	CUDF();
	CUDF(const CUDF& other);
	CUDF(const CString &name, const CString &value, int type, long listId);
	CUDF(CString &name, CString &value, int type, long m_ListId, long valueId);
	virtual ~CUDF();
	CUDF& operator=(const CUDF & other);
	BOOL operator==(const CUDF & other);
	BOOL operator!=(const CUDF & other) { return (! (*this == other)); }

	int Parse(CString &udf);
	CString Stream();

	CString m_Name;
	CString m_Value;		// string value
	long m_IntegerValue;
	double m_FloatValue;
	int m_Type;
	long m_ListID;
	long m_ValueID;
	long m_ParentID;
	int m_ElementType;
	CString m_DefaultValue;
	CStringArray m_ListValues;
	long m_ElementID;

};

#endif // !defined(AFX_UDF_H__373DB633_A9C2_11D4_9EBD_00C04FAC149C__INCLUDED_)

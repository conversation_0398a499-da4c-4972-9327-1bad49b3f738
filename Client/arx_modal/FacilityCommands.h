// FacilityCommands.h: interface for the CFacilityCommands class.
//
//////////////////////////////////////////////////////////////////////

#if !defined(AFX_FACILITYCOMMANDS_H__1F948DA0_7C68_4412_AD25_A9E5F51532C9__INCLUDED_)
#define AFX_FACILITYCOMMANDS_H__1F948DA0_7C68_4412_AD25_A9E5F51532C9__INCLUDED_

#include "Commands.h"

#if _MSC_VER > 1000
#pragma once
#endif // _MSC_VER > 1000

class CFacilityCommands : public CCommands
{
public:

	CFacilityCommands();
	virtual ~CFacilityCommands();
	static void RegisterCommands();

	static void OpenDrawing();
	static void OpenFacility();
	static void NewFacility();
	static void SaveFacility();
	static void SaveAsFacility();
	static void DeleteFacility();
	static void ExportFacility();
	static void ImportFacility();
	static void DoForteSave();
	static void ConvertDrawing();
};

#endif // !defined(AFX_FACILITYCOMMANDS_H__1F948DA0_7C68_4412_AD25_A9E5F51532C9__INCLUDED_)

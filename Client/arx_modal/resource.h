//{{NO_DEPENDENCIES}}
// Microsoft Developer Studio generated include file.
// Used by modal.rc
//
#define IDS_REGISTRY_PATH               1
#define IDC_START                       3
#define IDC_CONNECT                     3
#define IDC_LOAD                        3
#define IDC_ADD_INTERNAL                3
#define IDC_ADVANCED                    3
#define IDC_OPTIONS                     4
#define IDAPPLY                         4
#define IDC_NEW                         4
#define IDC_DELETE_DRAWING_BAY          4
#define IDC_DEFINE_MAPPING              4
#define IDC_DELETE_INTERNAL             4
#define IDC_COLOR_DRAWING_BAY           5
#define IDC_BROWSE                      5
#define IDC_EDIT                        5
#define IDC_WMS_PROPERTIES              5
#define IDC_DELETE_DB_BAY               6
#define IDC_VALIDATE                    6
#define IDUPDATE                        8
#define IDNEXT                          11
#define IDIMPORT                        12
#define IDNEXT2                         12
#define IDNEXT3                         13
#define ID                              15
#define IDDELETE                        16
#define IDEXCEL                         17
#define IDSORT                          19
#define IDC_ASSIGN_PRODUCTS             22
#define ID_ASSIGNMENTMENU_VIEW_PRODUCTS 25
#define IDD_OBJPLACE_DIALOG             28
#define IDD_CHOOSEAISLE_DIALOG          30
#define IDI_AISLEICON                   32
#define IDD_PICKPATH_OPTION             33
#define IDD_PICKPATH_PROPERTIES_DIALOG  34
#define IDD_COLORMODEL                  36
#define IDD_SLOTGROUP                   39
#define IDD_STARTUP                     40
#define IDD_STARTUP_ACC                 40
#define IDD_CHANGERACKTYPE              42
#define IDD_LOGINDLG                    43
#define IDB_SORT_BITMAP                 44
#define IDD_HOTSPOT_DIALOG              45
#define IDD_COLORMODELRESULTS           46
#define IDB_SPLASH                      47
#define IDD_SPLASH                      48
#define IDD_LEVLOCMAINT_DLG             50
#define IDI_LEVELICON                   51
#define IDD_SECTION_PAGE1               52
#define IDI_LOCATIONICON                53
#define IDD_SECTION_PAGE2               54
#define IDI_SUCCEEDICON                 55
#define IDD_VALIDATE_FACILITY           56
#define IDB_FLDCLS                      57
#define IDB_FLDOPN                      58
#define IDB_DOC                         59
#define IDB_LARGE_ICONS                 60
#define IDB_SMALL_ICONS                 61
#define IDB_LIST                        62
#define IDB_DETAILS                     63
#define IDD_FACILITY                    64
#define IDD_LAYOUT_PRODUCTS             66
#define IDD_DISPLAY_RESULTS             68
#define IDB_EXCEL                       70
#define IDD_CAPITAL_COST_DIALOG         71
#define IDD_LAYOUT_GROUPS_DIALOG        72
#define IDD_MANUAL_ASSIGNMENT           75
#define IDD_QUERY_PRODUCTS              76
#define IDD_QUERY_LOCATIONS             77
#define IDD_PROCESSING                  78
#define IDD_USER_QUERY                  79
#define IDD_PROMPT                      80
#define IDD_UDF                         82
#define IDD_AISLE_PROPERTIES            83
#define IDD_BAY_PROPERTIES              84
#define IDD_SIDE_PROPERTIES             85
#define IDD_LEVEL_PROPERTIES            86
#define IDD_LOCATION_PROPERTIES         87
#define IDD_FACILITY_PROPERTIES         88
#define IDD_SEARCH_ANCHOR               91
#define IDD_LOCATION_INTERFACE          93
#define IDD_ASSIGNMENT_INTERFACE        94
#define IDD_NUMBER_OF_RESULTS           95
#define IDD_PRODUCT_GROUP_NAVIGATOR     96
#define IDD_DATA_MODEL                  98
#define IDD_PRODUCT_CONTAINER_PAGE      99
#define IDD_PRODUCT_PAGE                102
#define IDD_PRODUCT_OPTIMIZE_PAGE       104
#define IDD_PRODUCT_GROUP_MAINTENANCE   105
#define IDD_PRODUCT_GROUP_ASSIGNMENT    106
#define IDD_PRODUCT_GROUP_PROPERTIES    107
#define IDD_PRODUCT_GROUP_LAYOUT_CONSTRAINTS 108
#define IDD_PRODUCT_GROUP_CRITERIA      109
#define IDD_CRITERIA_MAINTENANCE        110
#define IDD_CRITERIA_PROPERTIES         111
#define IDD_CRITERIA_LIST               112
#define IDR_CRITERIA_MENU               113
#define IDR_PRODUCTGROUP_MENU           114
#define IDD_PRODUCT_GROUP_CRITERIA_QUERY 115
#define IDD_PROGRESS                    116
#define IDB_RACK_USAGE                  118
#define IDB_MAINTAIN_CRITERIA           120
#define IDB_ASSIGN_PRODUCTS             121
#define IDB_MAINTAIN_GROUPS             122
#define IDB_ASSIGN_CRITERIA             123
#define IDB_EXIT                        124
#define IDB_HELP                        125
#define IDR_DUMMY                       126
#define IDC_SHOW_HELP                   127
#define IDS_REPEATBAY                   128
#define IDS_CREATEAISLESIDE             129
#define IDS_REPEATAISLESIDE             133
#define IDS_CREATEAISLE                 137
#define IDS_CREATEBAYSTR                138
#define IDS_REPEATAISLESTR              139
#define IDI_FLDCLS                      155
#define IDI_FLDOPEN                     156
#define IDD_DATA_PURIFICATION           165
#define IDD_COST_COMPARISON             166
#define IDD_GENERATE_MOVES              168
#define IDD_UDF_MAINTENANCE             169
#define IDD_UDF_PROPERTIES              170
#define IDR_GENERIC_MENU                173
#define IDD_SEARCH_ANCHOR_GENERATE      179
#define IDD_CLEAN_FACILITY              187
#define IDD_FACILITY_TOOLS              188
#define IDD_COLOR_PRODUCT_GROUPS        190
#define IDD_INTERFACE_MAP               192
#define IDD_INTERFACE_MAP_UDF           193
#define IDD_PRODUCT_INTERFACE           194
#define IDI_DOWN                        197
#define IDI_UP                          198
#define IDD_SAVEAS_OPTIONS              201
#define IDD_PASSWORD                    202
#define IDD_WMS_IMPORT                  203
#define IDD_WMS_PROPERTIES              204
#define IDD_POPULATE_UDF                206
#define IDD_WMS_EXPORT                  207
#define IDD_WMS_GROUPS                  208
#define IDD_WMS_GROUP_PROPERTIES        209
#define IDI_EXPAND_ARROW_DOWN           212
#define IDI_EXPAND_ARROW_UP             213
#define IDI_ADD                         214
#define IDI_FORKLIFT                    215
#define IDI_DC                          216
#define IDI_BOX                         217
#define IDI_FORKLIFT2                   219
#define IDI_SUBTRACT                    220
#define IDI_INFO                        228
#define IDI_ADD_BLUE                    229
#define IDI_FACILITY                    231
#define IDI_SECTION                     232
#define IDI_SECTION_INVERSE             233
#define IDI_FACILITY_INVERSE            234
#define IDI_BOX_CHECKED                 235
#define IDD_PROFILE_TREE_PANE           235
#define IDD_PROFILE_LIST_PANE           236
#define IDI_SIDE                        236
#define IDD_BAY_PROFILE_FLOW_ATTRIBUTES 237
#define IDD_BAY_PROFILE_FLOOR_ATTRIBUTES 238
#define IDD_BAY_PROFILE_DRIVEIN_ATTRIBUTES 239
#define IDD_BAY_PROFILE_PALLET_ATTRIBUTES 240
#define IDD_BAY_PROFILE_BIN_ATTRIBUTES  241
#define IDD_BAY_PROFILE_LOCATION_ATTRIBUTES 242
#define IDD_BAY_PROFILE_CROSSBAR_ATTRIBUTES 244
#define IDD_BAY_PROFILE_CROSSBAR_PROPERTIES 246
#define IDD_BAY_PROFILE_LABOR_ATTRIBUTES 248
#define IDD_BAY_PROFILE_BAY_ATTRIBUTES  250
#define IDD_BAY_PROFILE_EXTERNAL_ATTRIBUTES 251
#define IDD_BAY_PROFILE_LIST            252
#define IDD_CHECK_MESSAGE               253
#define IDD_BAY_PROFILE_LOCATION_PROPERTIES 254
#define IDD_BAY_PROFILE_LEVEL_ATTRIBUTES 258
#define IDD_BAY_PROFILE_HANDLING_PROPERTIES 260
#define IDD_BAY_PROFILE_RULE_PAGE       261
#define IDD_BAY_PROFILE_EDIT_FACING     262
#define IDD_SIDE_PROFILE_LIST           263
#define IDD_SIDE_PROFILE_ATTRIBUTES     264
#define IDD_SIDE_PROFILE_BAY_PAGE       265
#define IDI_PROPERTIES                  265
#define IDD_AISLE_PROFILE_LIST          266
#define IDD_AISLE_PROFILE_DIMENSION_PAGE 267
#define IDD_AISLE_PROFILE_SIDE_PAGE     268
#define IDB_PROPERTIES                  268
#define IDD_LOCATION_ATTRIBUTES         269
#define IDB_GO                          269
#define IDD_LEVEL_LOCATION_MAINTENANCE  270
#define IDI_GO                          271
#define IDD_INTEGRATION_STATUS          278
#define IDD_INTEGRATION_LOCATION_OPTIONS 282
#define IDD_INTEGRATION_CONNECTION      283
#define IDD_INTEGRATION_ASSIGN_CONNECTION 284
#define IDD_INTEGRATION_OPTIONS         285
#define IDD_INTEGRATION_PRODUCT_OPTIONS 286
#define IDD_CONVERT_PRODUCT_TO_XML      287
#define IDD_INTEGRATION_ASSIGNMENT_OPTIONS 288
#define IDD_LAYOUT_PRODUCTS_ADVANCED    289
#define IDD_MAIN_WIZARD                 292
#define IDD_INTEGRATION_WIZARD_SYSTEM_PAGE 293
#define IDD_INTEGRATION_WIZARD_MAP_SECTION_PAGE 294
#define IDD_COLOR_MODEL_ADVANCED        295
#define IDD_INTEGRATION_WIZARD_CONNECTION_PAGE 296
#define IDD_INTEGRATION_WIZARD_MAPTYPE_PAGE 297
#define IDD_INTEGRATION_WIZARD_MAP_FACILITY_PAGE 298
#define IDB_USEWIZARDD                  301
#define IDB_INSTRUCTIONSD               302
#define IDB_INSTRUCTIONSU               303
#define IDB_NEWFACILITYD                304
#define IDB_NEWFACILITYU                305
#define IDB_OPENFACILITYD               306
#define IDB_OPENFACILITYU               307
#define IDB_USEWIZARDU                  308
#define IDI_MOVEDOWN                    310
#define IDI_MOVEUP                      311
#define IDD_DIALOG1                     312
#define IDD_HOTSPOTPROPERTIES           312
#define IDD_COST_COMPARISON2            314
#define IDD_EXPORT_FACILITY             315
#define IDC_XCOORDINATE                 1012
#define IDC_YCOORDINATE                 1013
#define IDC_ZCOORDINATE                 1014
#define IDC_CHOOSEAISLE_TREE            1029
#define IDC_CHOOSEAISLE_TREEGROUP       1031
#define IDC_CHOOSEAISLE_TEXT1           1033
#define IDC_CHOOSEAISLE_AISLENAME       1034
#define IDC_CHOOSEAISLE_SLOTDIR         1035
#define IDC_BUTTON1                     1036
#define IDC_RECALCULATE_COST            1036
#define IDC_ADD_UDF                     1036
#define IDC_ROTATION_DISPLAY            1036
#define IDC_VALIDATE_FORMULA            1036
#define IDC_GENERATE                    1036
#define IDC_VIEW_PRODUCT                1036
#define IDC_MAP                         1036
#define IDC_SIDE_WIZARD                 1036
#define IDC_VIEW_PROFILE                1036
#define IDC_TEST                        1036
#define IDC_DEFINE_CONNECTIONS          1036
#define IDC_ADD                         1037
#define IDC_MODIFY_UDF                  1037
#define IDC_SAVE                        1037
#define IDC_AISLE_WIZARD                1037
#define IDC_FORMULA                     1038
#define IDC_DELETE_UDF                  1038
#define IDC_COPY                        1038
#define IDC_MODIFY                      1038
#define IDC_LOAD_ATTRIBUTES             1039
#define IDC_MAIN_WIZARD                 1039
#define IDC_COUNT                       1040
#define IDC_CREATE_UDF                  1040
#define IDC_STOP                        1041
#define IDC_MAP_CONSTANT                1041
#define IDC_DELETE                      1042
#define IDC_REMOVE_UDF                  1042
#define IDC_ADD_EXCLUSIVE               1043
#define IDC_DELETE_MAP                  1043
#define IDC_VIEW_DRAWING                1043
#define IDC_ASSIGN                      1044
#define IDC_NEW_MAP                     1044
#define IDC_EXCLUDE                     1044
#define IDC_REFRESH                     1045
#define IDC_ASSIGN_ALL                  1045
#define IDC_UNMAP                       1045
#define IDC_DELETE_CROSSBAR             1045
#define IDC_AUTOMAP                     1046
#define IDC_ADD_CROSSBAR                1046
#define IDC_VIEW_PRODUCTS               1047
#define IDC_MAP_UDF                     1047
#define IDC_EDIT_CROSSBAR               1047
#define IDC_ADD_ATTRIBUTE               1048
#define IDC_VIEW_LEVEL                  1048
#define IDC_REMOVE_ATTRIBUTE            1049
#define IDC_VIEW_LOCATION               1049
#define IDC_VIEW_ELEMENT                1050
#define IDC_PICKPATH_PROPERTIES_PATHTYPE 1057
#define IDC_PICKPATH_PROPERTIES_PATHTYPE_PROMPT 1060
#define IDC_PICKPATH_PROPERTIES_GROUP   1063
#define IDC_PICKPATH_PROPERTIES_PATTERNNUM 1066
#define IDC_PICKPATH_PROPERTIES_PATTERNNUM_PROMPT 1067
#define IDC_RESET                       1072
#define IDC_TRUESCALE                   1073
#define IDC_COLOR_BY_PRODUCT            1073
#define IDC_ATTRIBUTE                   1074
#define IDC_COLOR                       1075
#define IDC_SPECIFIC                    1076
#define IDC_COLLAPSE                    1076
#define IDC_ZOOM                        1077
#define IDC_FILENAME                    1080
#define IDC_NOTES                       1082
#define IDC_CHOOSEAISLE_SECTIONCHOICE   1087
#define IDC_INCREMENT                   1088
#define IDC_CHOOISEAISLE_SECTION_PROMPT 1089
#define IDC_NEWSECTION_DESCRIPTION      1091
#define IDC_CHOOISEAISLE_NUMAISLES_PROMPT 1093
#define IDC_NEWSECT_DESC_PROMPT         1094
#define IDC_NEWSECTION_LOCATIONMASK     1096
#define IDC_NEWSECT_MASK_PROMPT         1097
#define IDC_SLOTGROUPLIST               1100
#define IDC_USEWIZARD                   1102
#define IDC_NEWFACILITY                 1104
#define IDC_NEWSECTION_FORKDISTVAR      1105
#define IDC_OPENFACILITY                1106
#define IDC_NEWSECT_FORKDISTVAR_PROMPT  1108
#define IDC_INSTRUCTIONS                1109
#define IDC_NEWSECTION_FORKDISTFIXED    1110
#define IDC_DESCRIPTION                 1111
#define IDC_NEWSECT_FORKDISTFIXED_PROMPT 1112
#define IDC_BACKFILL_ID                 1112
#define IDC_SUCCEEDHELP                 1113
#define IDC_STOCKER_ID                  1113
#define IDC_RACKTREE                    1114
#define IDC_NEWSECTION_FORKLABORRATE    1115
#define IDC_FORK_FIXED_INSERTION        1116
#define IDC_SELECTSTATIC                1117
#define IDC_NEWSECT_FORKLABORRATTE_PROMPT 1118
#define IDC_MIN_LOC_WIDTH               1119
#define IDC_USER                        1120
#define IDC_NEWSECTION_SELVARFACTOR     1121
#define IDC_PRODUCT_SNAP                1122
#define IDC_PASSWORD                    1123
#define IDC_NEWSECT_SELVAR_PROMPT       1124
#define IDC_PASSWORD_CONFIRM            1124
#define IDC_CONFIRM_PASSWORD            1124
#define IDC_FACING_SNAP                 1125
#define IDC_TIME_HORIZON_VALUE          1126
#define IDC_NEWSECTION_SELFIXEDFACTOR   1127
#define IDC_SURROUNDING_SPACE           1127
#define IDC_FACING_GAP                  1129
#define IDC_NEWSECT_SELFIXED_PROMPT     1130
#define IDC_PRODUCT_GAP                 1132
#define IDC_NEWSECTION_SELLABORRATE     1133
#define IDC_LEFT_TEXT                   1136
#define IDC_NEWSECTION_REPLAVGDIST      1138
#define IDC_RIGHT_TEXT                  1139
#define IDC_NEWSECT_REPLAVGDIST_PROMPT  1140
#define IDC_GROUP2                      1141
#define IDC_GROUP3                      1142
#define IDC_NEWSECTION_AVGORDQTY        1143
#define IDC_LBAYSTARTVAL                1144
#define IDC_NEWSECTAVGORDQTY_PROMPT     1145
#define IDC_NEWSECTION_CONTQTY          1147
#define IDC_NEWSECT_SELLABORRATTE_PROMPT2 1148
#define IDC_LBAYSTEPVAL                 1149
#define IDC_NEWSECT_CONTQTY_PROMPT      1150
#define IDC_LBAYSCHEMEVAL               1152
#define IDC_NEWSECTION_ORDERCOUNT       1154
#define IDC_LLEVELSTARTVAL              1155
#define IDC_NEWSECT_ORDERCOUNT_PROMPT   1156
#define IDC_LLEVELSTEPVAL               1158
#define IDC_LLEVELSCHEMEVAL             1161
#define IDC_LLEVELBREAK                 1162
#define IDC_LLOCSTARTVAL                1163
#define IDC_LLOCSTEPVAL                 1165
#define IDC_LLOCSCHEMEVAL               1168
#define IDC_LLOCBREAKVAL                1170
#define IDC_NEWSECT_APPLYBROKORDER      1172
#define IDC_RLEVELSTARTVAL              1178
#define IDC_STATIC_MODE                 1179
#define IDC_RLEVELSTEPVAL               1182
#define IDC_STATIC_START                1185
#define IDC_STATIC_END                  1187
#define IDC_RLEVELSCHEMEVAL             1188
#define IDC_START_VALUE                 1189
#define IDC_RLEVELBREAK                 1190
#define IDC_END_VALUE                   1191
#define IDC_RLOCSTARTVAL                1192
#define IDC_RESULTS_LIST                1194
#define IDC_RLOCSTEPVAL                 1195
#define IDC_STATIC_VER                  1196
#define IDC_STATIC_VERNUM               1198
#define IDC_BUILDNUM                    1200
#define IDC_RLOCSCHEMEVAL               1201
#define IDC_CHOOSEAISLE_NUMAISLES       1202
#define IDC_RLOCBREAKVAL                1205
#define IDC_LEVELLOC_TREE               1206
#define IDC_RBAYSTARTVAL                1207
#define IDC_CHOOSESTATIC                1208
#define IDC_COLORMODEL_AUTORESET        1209
#define IDC_RBAYSTEPVAL                 1210
#define IDC_RBAYSCHEMEVAL               1214
#define IDC_NEWSECTION_STOCKERVAR       1215
#define IDC_NEWSECTION_STOCKERFIXED     1216
#define IDC_NEWSECTION_STOCKERLABORRATE 1217
#define IDC_NEWSECTION_FORKINSERTION    1218
#define IDC_NEWSECTION_FORKPICKUP       1219
#define IDC_NEWSECTION_PALLETS_PER_TRIP 1220
#define IDC_NEWSECTION_AVGCUBEPERTRIP   1221
#define IDC_FACILITY_NAME               1222
#define IDC_FACILITY_LIST               1223
#define IDC_FACILITY_NOTES              1224
#define IDC_EDIT1                       1225
#define IDC_EDIT2                       1226
#define IDC_LOCATION_SPACE              1226
#define IDC_FIXED                       1226
#define IDC_XBOH                        1226
#define IDC_HOST                        1226
#define IDC_BACKFILL_CODE               1227
#define IDC_VARIABLE                    1227
#define IDC_QUEUE_MANAGER               1227
#define IDC_LEVEL_TYPE                  1228
#define IDC_MINIMUM_WIDTH               1228
#define IDC_MAXIMUM_CASE_WEIGHT         1228
#define IDC_CHANNEL                     1228
#define IDC_INNERLENGTH                 1229
#define IDC_MAXIMUM_CASE_COUNT          1229
#define IDC_CONNECTION_NAME             1229
#define IDC_OPTIMIZED_LEVEL_TYPE        1229
#define IDC_HOST2                       1229
#define IDC_CONTAINER_WIDTH             1230
#define IDC_QUEUE_NAME                  1230
#define IDC_NAME                        1231
#define IDC_PATH                        1231
#define IDC_QUEUE_MANAGER2              1231
#define IDC_DATABASE                    1232
#define IDC_DEFAULTVALUE                1232
#define IDC_CHANNEL2                    1232
#define IDC_RELATIVE_LEVEL              1233
#define IDC_TRIGGER_NAME                1233
#define IDC_QUEUE_NAME2                 1233
#define IDC_INNERWIDTH                  1234
#define IDC_LOGIN                       1234
#define IDC_PRODUCT_DESCRIPTION         1236
#define IDC_INNERHEIGHT                 1237
#define IDC_TI                          1238
#define IDC_NUMBERINPALLET              1239
#define IDC_HI                          1240
#define IDC_MAXSTACKNUMBER              1241
#define IDC_CONTAINER_HEIGHT            1242
#define IDC_LENGTH                      1243
#define IDC_EACHLENGTH                  1244
#define IDC_EACHWIDTH                   1245
#define IDC_LARGE_ICONS                 1246
#define IDC_EACHHEIGHT                  1247
#define IDC_LBAYPATTERN                 1248
#define IDC_RBAYPATTERN                 1249
#define IDC_SMALL_ICONS                 1250
#define IDC_LLEVELPATTERN               1251
#define IDC_LIST                        1252
#define IDC_RLEVELPATTERN               1253
#define IDC_DETAILS                     1254
#define IDC_LLOCPATTERN                 1255
#define IDC_RLOCPATTERN                 1256
#define IDC_VARWIDTH                    1257
#define IDC_REORIENT                    1258
#define IDC_OVERLAP                     1259
#define IDC_CHANGED_ONLY                1259
#define IDC_CONSTRAIN_AMOUNT            1262
#define IDC_RESULTS_TAB                 1266
#define IDC_BREAK_PALLET                1267
#define IDC_RANK_HEIGHT                 1268
#define IDC_LAYOUT_PICKPATHS            1269
#define IDC_LAYOUT_HOTSPOT              1270
#define IDC_CONTIGUOUS                  1271
#define IDC_QUANTITY                    1272
#define IDC_NOT_CONTIGUOUS              1272
#define IDASSIGN                        1273
#define IDUNASSIGNPRODUCT               1275
#define IDC_LOCATION_LIST               1276
#define IDC_PRODUCT_LIST                1277
#define IDQUERYPRODUCTS                 1278
#define IDC_ASSIGNMENT_BOX              1279
#define IDQUERYLOCATIONS                1280
#define IDC_PRODUCT_GROUP_BOX           1281
#define IDUNASSIGNLOCATION              1282
#define IDC_PRODUCT_GROUP               1283
#define IDC_LOC_DESC_EDIT               1284
#define IDC_SECTION                     1284
#define IDC_LOCTYPE_BOX                 1285
#define IDC_PRODUCT_GROUPBOX            1286
#define IDC_LOCATION_GROUPBOX           1287
#define IDC_PRODUCT_ID                  1288
#define IDC_PRODUCT_INFO                1289
#define IDC_LOCATION_INFO               1290
#define IDC_STATUS_TEXT                 1291
#define IDC_QUERY_LIST                  1292
#define IDRUN                           1293
#define IDC_QUERY                       1294
#define IDC_STATIC_DATABASE             1295
#define IDC_STATIC_QUERY                1296
#define IDC_PARAMETER_NAME              1297
#define IDC_PARAMETER_VALUE             1298
#define IDC_SCROLLBAR                   1299
#define IDC_TRAVEL_DISTANCE             1301
#define IDC_SEL_HOTSPOT_STATIC          1302
#define IDC_FORK_HOTSPOT_STATIC         1303
#define IDC_HOTSPOT_STATIC              1304
#define IDC_SEL_HOTSPOT_COORDINATES     1305
#define IDC_FORK_HOTSPOT_COORDINATES    1306
#define IDC_TRAVEL_DISTANCE_STATIC      1307
#define IDC_COORDINATES                 1308
#define IDC_ROTATION                    1309
#define IDC_STATUS                      1309
#define IDC_PROFILE                     1310
#define IDC_BACKFILL_COORDINATES        1310
#define IDC_VARIABLE_WIDTH              1311
#define IDC_STOCKER_COORDINATES         1311
#define IDC_ROTATE_ALLOWED              1312
#define IDC_HANDLING_METHOD             1313
#define IDC_SELECT                      1314
#define IDC_WIDTH                       1315
#define IDC_DEPTH                       1316
#define IDC_ACTIVE                      1317
#define IDC_STACK_WIDTH                 1317
#define IDC_MAX_WEIGHT                  1318
#define IDC_WMS_ID                      1319
#define IDC_FORK_EXTRACTION_TIME        1319
#define IDC_UOM                         1320
#define IDC_FORK_INSERTION_TIME         1320
#define IDC_TRACE                       1321
#define IDC_TIME_HORIZON_UNITS          1322
#define IDC_FLOW_DIFFERENCE             1322
#define IDC_SELECTION_SEQUENCE          1322
#define IDC_REPLENISHMENT_SEQUENCE      1323
#define IDC_ADD_FACING                  1326
#define IDC_PCT_RESERVES                1327
#define IDC_REPLENISHMENTS              1328
#define IDC_PCT_RSV_UTILIZATION         1329
#define IDC_PCT_SEL_UTILIZATION         1330
#define IDC_ADDITIONAL_RSV_CUBE         1331
#define IDC_PALLET_HEIGHT               1332
#define IDC_CLEARANCE                   1333
#define IDC_RECALCULATE                 1335
#define IDC_GRID                        1336
#define IDC_NUMBER                      1338
#define IDC_DATA_GRID                   1340
#define IDC_OPTIMIZEBY                  1341
#define IDC_ISASSIGNMENTLOCKED          1342
#define IDC_WMSPRODUCTID                1343
#define IDC_WMSPRODUCTDETAILID          1344
#define IDC_CASEPACK                    1345
#define IDC_INNERPACK                   1346
#define IDC_UNITOFISSUE                 1347
#define IDC_LOCATION                    1348
#define IDC_COMMODITY_TYPE              1348
#define IDC_OPTIMIZED_LOCATION          1349
#define IDC_ISHAZARD                    1349
#define IDC_ISPICKTOBELT                1350
#define IDC_MOVEMENT                    1351
#define IDC_NUMBEROFHITS                1352
#define IDC_PREVIOUS_MOVEMENT           1353
#define IDC_WEIGHT                      1354
#define IDC_CRUSH_FACTOR                1355
#define IDC_BALANCEONHAND               1357
#define IDC_ROTATEYAXIS                 1358
#define IDC_ROTATEZAXIS                 1359
#define IDC_ROTATEXAXIS                 1360
#define IDC_PREVIOUS_BOH                1361
#define IDC_ISHEIGHTOVERRIDE            1363
#define IDC_CONTAINER_LENGTH            1364
#define IDC_LOCATION_PRODUCT_GROUP      1365
#define IDC_OPTIMIZED_LOCATION_PRODUCT_GROUP 1366
#define IDC_BAY_PROFILE                 1368
#define IDC_ROTATED_WIDTH               1369
#define IDC_ROTATED_LENGTH              1370
#define IDC_ROTATED_HEIGHT              1371
#define IDC_INTEGRATION_STATUS          1372
#define IDC_FULL_EXPORT                 1373
#define IDC_OPTIMIZED_BAY_PROFILE       1373
#define IDC_UPDATE_GROUP                1374
#define IDC_INBOUND_PROMPT              1374
#define IDC_PRODUCT_GROUP_LIST          1375
#define IDC_VIEW_FACINGS                1375
#define IDC_OUTBOUND_PROMPT             1375
#define IDC_LOCK_PRODUCT_GROUP          1376
#define IDC_COLOR_LIST                  1376
#define IDC_NO_UPDATE                   1376
#define IDC_VIEW_OPTIMIZED_FACINGS      1376
#define IDC_ALLOW_NOT_INTEGRATED        1376
#define IDC_LOCK_LOCATIONS              1377
#define IDC_AUTO_CONFIRM                1377
#define IDC_SKIP                        1378
#define IDC_PERCENT_OPEN_LOCS           1379
#define IDC_OPTIMIZE_METHOD             1380
#define IDC_FACILITY_TREE               1381
#define IDC_OPTIMIZE_ATTRIBUTE          1382
#define IDC_EXCLUSIVE                   1383
#define IDC_REMOVE                      1384
#define IDC_REMOVE_EXCLUSIVE            1385
#define IDC_NEW_COLOR                   1385
#define IDC_CRITERIA_GRID               1386
#define IDC_REMOVE_ALL                  1386
#define IDC_CRITERIA_LIST               1387
#define IDC_DELETE_COLOR                1387
#define IDC_RANGE_LIST                  1389
#define IDC_VALUE_LIST                  1390
#define IDC_OPERATOR                    1391
#define IDC_MOVE_UP                     1392
#define IDC_MOVE_DOWN                   1394
#define IDC_DELETE_VALUE                1395
#define IDC_ADD_VALUE                   1396
#define IDC_NONEXCLUSIVE_LIST           1398
#define IDC_LOAD_VALUES                 1400
#define IDC_FROM_VALUE                  1401
#define IDC_TO_VALUE                    1402
#define IDC_ADD_NONEXCLUSIVE            1403
#define IDC_LOAD_POSSIBLE_VALUES        1405
#define IDC_REMOVE_NONEXCLUSIVE         1406
#define IDC_EXCLUSIVE_LIST              1407
#define IDC_PROPERTIES                  1408
#define IDC_LIST1                       1412
#define IDC_UDF_LIST                    1412
#define IDC_LIST_ELEMENTS               1412
#define IDC_DRAWING_LIST                1412
#define IDC_MAP_LIST                    1412
#define IDC_PROFILE_LIST                1412
#define IDC_FACING_LIST                 1412
#define IDC_CONNECTION_LIST             1412
#define IDC_LEVEL_LIST                  1413
#define IDC_DB_LIST                     1413
#define IDC_GROUP_LIST                  1413
#define IDC_ATTRIBUTE_LIST              1413
#define IDC_ADD_ELEMENT                 1414
#define IDC_ELEMENT_LIST                1414
#define IDC_DELETE_ELEMENT              1415
#define IDC_FROM_STATIC                 1416
#define IDC_TO_STATIC                   1417
#define IDC_PROGRESS                    1418
#define IDC_OVERALL_PROGRESS            1419
#define IDC_MAINTAIN_GROUPS             1422
#define IDC_MAINTAIN_CRITERIA           1424
#define IDC_ASSIGN_CRITERIA             1426
#define IDC_QUIT                        1429
#define IDC_FORMULA_CHECK               1430
#define IDC_AUTO_CHECK                  1431
#define IDC_HEIGHT                      1435
#define IDC_THICKNESS                   1452
#define IDC_OVERHANG                    1458
#define IDC_INSERT                      1474
#define IDC_FILL                        1475
#define IDC_LEVEL                       1542
#define IDC_MIN_HEIGHT                  1543
#define IDC_MAX_HEIGHT                  1544
#define IDVIEW                          1551
#define IDRESET                         1553
#define IDC_ASSIGNED                    1555
#define IDC_NOT_ASSIGNED                1556
#define IDC_BOTH                        1557
#define IDC_LOAD_MOVES                  1559
#define IDC_GENERATE_MOVES              1560
#define IDC_UDF_TYPE                    1560
#define IDC_TYPE                        1561
#define IDC_BAY_PROFILE_LIST            1561
#define IDC_STATIC_LIST                 1562
#define IDC_ELEMENT_TYPE                1562
#define IDC_STATIC_BAY_PROFILE          1563
#define IDC_SELECT_ALL                  1564
#define IDC_STATIC_MESSAGE              1565
#define IDC_HIDE_EXCLUDED               1566
#define IDC_CLEAR_EXISTING              1569
#define IDC_SKIP_COST_UPDATE            1570
#define IDC_DELETE_EMPTY_AISLE          1571
#define IDC_MAPNAME_LIST                1571
#define IDC_MAP_TYPE                    1572
#define IDC_NAMED                       1573
#define IDC_RESET_COLORS                1573
#define IDC_POSITIONAL                  1574
#define IDC_FACILITY_DEFAULT            1575
#define IDC_EXTERNAL_LIST               1576
#define IDC_FORMAT                      1577
#define IDC_DELIMITER                   1578
#define IDC_PRODUCT_CHECK               1579
#define IDC_UP                          1580
#define IDC_GROUP_CHECK                 1580
#define IDC_DOWN                        1581
#define IDC_RESULTS_CHECK               1581
#define IDC_PASSWORD_MSG                1581
#define IDC_STATIC_CONFIRM              1582
#define IDC_COMBO1                      1583
#define IDC_WMS_SYSTEM                  1583
#define IDC_LOCATION_USAGE_LIST         1583
#define IDC_WORK_TYPE_LIST              1583
#define IDC_BAYTYPE_LIST                1583
#define IDC_TRANSPORT                   1583
#define IDC_LOG_MODE                    1583
#define IDC_SYSTEM_LIST                 1583
#define IDC_COMBO3                      1584
#define IDC_HANDLING_METHOD_LIST        1584
#define IDC_FACILITY_MAP_TREE           1586
#define IDC_LEVEL_TYPE_LIST             1586
#define IDC_WMS_MAP_TREE                1587
#define IDC_WMS_GROUP_TREE              1588
#define IDC_COMMAND_TREE                1590
#define IDC_TREE1                       1591
#define IDC_TOP_VIEW_BUTTON             1592
#define IDC_SIDE_VIEW_BUTTON            1593
#define IDC_HIDDEN                      1596
#define IDC_HIDDEN_CHECKBOX             1596
#define IDC_LEVEL_BUTTON                1600
#define IDC_VARIABLE_WIDTH_CHECKBOX     1602
#define IDC_FIXED_RADIO                 1603
#define IDC_COST_SEQUENCE               1603
#define IDC_PERCENTAGE                  1603
#define IDC_ROTATION_CHECKBOX           1604
#define IDC_SKIP_CHECKBOX               1604
#define IDC_VARIABLE_RADIO              1604
#define IDC_SHOW_CHECK                  1604
#define IDC_VALIDATE_UDFS               1604
#define IDC_BAY_PROFILE_TREE            1605
#define IDC_FLOATING_CHECKBOX           1605
#define IDC_OLD_FORMAT                  1605
#define IDC_IGNORE_WEIGHT               1605
#define IDC_EXCLUDED_CHECKBOX           1606
#define IDC_BAY_SPANNING_CHECKBOX       1606
#define IDC_CREATE_ASSIGNMENTS          1606
#define IDC_IGNORE_RANKINGS             1606
#define IDC_EXCLUDE_CHECKBOX            1607
#define IDC_ACTIVE_CHECKBOX             1607
#define IDC_UPRIGHT_HEIGHT              1608
#define IDC_WEIGHT_CAPACITY             1609
#define IDC_UPRIGHT_WIDTH               1610
#define IDC_BAY_DEPTH                   1611
#define IDC_BAY_HEIGHT                  1612
#define IDC_STACK_DEPTH                 1613
#define IDC_SELECTION_STACK_POSITIONS   1616
#define IDC_RESERVE_STACK_POSITIONS     1617
#define IDC_SELECTION_STACK_HEIGHT      1618
#define IDC_RESERVE_STACK_HEIGHT        1619
#define IDC_STACKABLE_HEIGHT            1622
#define IDC_BAY_WIDTH                   1623
#define IDC_NUMBER_OF_PALLETS           1624
#define IDC_EXTERNAL_SYSTEM_LIST        1625
#define IDC_PALLET_SPACE                1625
#define IDC_CROSSBAR_BUTTON             1626
#define IDC_CROSSBAR_HEIGHT             1628
#define IDC_RACK_COST                   1630
#define IDC_HAZARD_CHECKBOX             1631
#define IDC_STACK_HEIGHT                1632
#define IDC_CROSSBAR_POSITION           1635
#define IDC_MESSAGE                     1636
#define IDC_INTERFACE_MESSAGE           1637
#define IDC_DIMENSIONS                  1639
#define IDC_LOCATION_USAGE              1640
#define IDC_LOCATION_COUNT              1641
#define IDC_LOCATIONS_DEEP              1642
#define IDC_DIVIDER_STATIC              1644
#define IDC_PRODUCT_STATIC              1645
#define IDC_FACING_STATIC               1646
#define IDC_GAP_STATIC                  1647
#define IDC_SNAP_STATIC                 1648
#define IDC_PRODUCT_GAP_STATIC          1650
#define IDC_FACING_GAP_STATIC           1651
#define IDC_PRODUCT_SNAP_STATIC         1652
#define IDC_FACING_SNAP_STATIC          1653
#define IDC_VARIABLE_WIDTH_STATIC       1654
#define IDC_LOCATIONS_ACROSS            1655
#define IDC_MINIMUM_WIDTH_STATIC        1656
#define IDC_LOCATIONS_DEEP_STATIC       1657
#define IDC_LOCATIONS_ACROSS_STATIC     1658
#define IDC_HANDLING_LIST               1659
#define IDC_CUBE                        1660
#define IDC_GROUP_BOX                   1661
#define IDC_ACTIVE_STATIC               1662
#define IDC_NOTE_BOX                    1663
#define IDC_XCUBE                       1664
#define IDC_FACING_COUNT_STATIC         1665
#define IDC_BAY_LIST                    1667
#define IDC_SIDE_BUTTON                 1668
#define IDC_SIDE_PROFILE_TREE           1669
#define IDC_AISLE_PROFILE_TREE          1670
#define IDC_LEFT_SPACE                  1671
#define IDC_AISLE_SPACE                 1672
#define IDC_RIGHT_SPACE                 1673
#define IDC_AISLE_BUTTON                1674
#define IDC_LEFT_SIDE_LIST              1675
#define IDC_RIGHT_SIDE_LIST             1676
#define IDC_BAY_WIZARD                  1677
#define IDC_MASK                        1680
#define IDC_DETAIL_EDIT                 1686
#define IDC_FILE_NAME                   1687
#define IDC_PORT                        1688
#define IDC_HOST_STATIC                 1689
#define IDC_QUEUE_MANAGER_STATIC        1690
#define IDC_LOGIN_STATIC                1691
#define IDC_PORT2                       1691
#define IDC_PATH_STATIC                 1692
#define IDC_HOST_STATIC2                1692
#define IDC_FILE_NAME_STATIC            1693
#define IDC_QUEUE_MANAGER_STATIC2       1693
#define IDC_TRIGGER_NAME_STATIC         1694
#define IDC_PORT_STATIC                 1695
#define IDC_CHANNEL_STATIC              1696
#define IDC_PASSWORD_STATIC             1697
#define IDC_PORT_STATIC2                1697
#define IDC_CONFIRM_PASSWORD_STATIC     1698
#define IDC_CHANNEL_STATIC2             1698
#define IDC_INTERFACE_TREE              1699
#define IDC_QUEUE_NAME_STATIC           1699
#define IDC_SKIP_XML_LOGGING            1700
#define IDC_QUEUE_NAME_STATIC2          1700
#define IDC_DETAIL_XML_LOGGING          1701
#define IDC_BASELINE_PRODUCT            1702
#define IDC_SAVE_OPTIONS                1702
#define IDC_OPTIMIZED_PRODUCT           1703
#define IDC_SKIP_PG_UPDATE              1703
#define IDC_CONVERT                     1705
#define IDC_RANKING_SLIDER              1706
#define IDC_BEST_STATIC                 1707
#define IDC_MINIMIZE_STATIC             1708
#define IDC_RANKING_STATIC              1709
#define IDC_LOGGING_STATIC              1710
#define IDC_MAX_RESULTS_STATIC          1711
#define IDC_MAX_RESULTS                 1712
#define IDC_FACTOR_SLIDER               1713
#define IDC_PICKPATH_SEQUENCE           1714
#define IDC_STRATEGIC                   1715
#define IDC_GROUP                       1716
#define IDC_TACTICAL                    1717
#define IDC_MANUAL                      1718
#define IDC_CONSTRAIN_STATIC            1719
#define IDC_TACTICAL_STATIC             1720
#define IDC_RADIO1                      1721
#define IDC_RADIO4                      1722
#define IDC_BASELINE                    1723
#define IDC_MIN                         1723
#define IDC_OPTIMIZED                   1724
#define IDC_MAX                         1724
#define IDC_CHOOSE_COLOR                1726
#define IDC_HIGHER                      1728
#define IDC_LOWER                       1729
#define IDC_COLOR_PICTURE               1732
#define IDC_SUM                         1733
#define IDC_AVG                         1734
#define IDC_ANIMATE                     1735
#define IDC_SECTION_LIST                1736
#define IDC_FORKLIFT                    1738
#define IDC_SELECTION                   1739
#define IDC_INCREMENT_STATIC            1740
#define IDC_NAME_STATIC                 1741
#define IDC_ROTATION_STATIC             1742
#define IDC_WMSID_STATIC                1743
#define IDC_TYPE_LIST                   1745
#define IDC_RADIO2                      1746
#define IDC_TACN1                       1746
#define IDC_OPTIMIZEHELP                1747
#define IDC_TEXT                        1748
#define IDC_IGNORE_RANKINGS_STATIC      1752
#define IDC_HOTSPOTPROPERTIES_TYPE      1758
#define ID_HOTSPOTPROPERTIES_CLOSE      1759
#define IDC_HOTSPOTPROPERTIES_SECTION   1760
#define IDC_HOTSPOTPROPERTIES_COORDINATES 1761
#define IDC_SHOW_DETAILS                1762
#define IDC_COST_DATA1                  1774
#define IDC_COST_DATA2                  1775
#define IDC_COST_DATA3                  1776
#define IDC_COST_DATA4                  1777
#define IDC_COST_DATA5                  1778
#define IDC_COST_DATA6                  1779
#define IDC_COST_DATA7                  1780
#define IDC_COST_DATA8                  1781
#define IDC_COST_DATA9                  1782
#define IDC_COST_DATA10                 1783
#define IDC_COST_DATA11                 1784
#define IDC_COST_DATA12                 1785
#define IDC_DATABASE_LIST               1786
#define ID_EXPORT                       1787
#define IDEXPORT                        1788
#define IDC_TEXT1                       1789
#define IDC_TACY1                       1790
#define IDC_TACY2                       1791
#define IDC_TACY3                       1792
#define IDC_TACN3                       1793
#define IDC_TACTICALBOX                 1794
#define IDC_TACTEXT1                    1795
#define IDC_TACTEXT2                    1796
#define IDC_TACTEXT3                    1797
#define IDC_TACN2                       1798
#define IDC_SLOTCHECK                   1799
#define IDC_RESLOTCHECK                 1800
#define IDC_SWAPCHECK                   1801
#define IDC_VIEWERRORS                  1802
#define ID_RANGEMENU_ADD                32772
#define ID_RANGEMENU_DELETE             32773
#define ID_VALUEMENU_ADD                32774
#define ID_VALUEMENU_DELETE             32775
#define ID_VALUEMENU_LOADVALUES         32776
#define ID_CRITERIAMENU_ADD             32777
#define ID_CRITERIAMENU_DELETE          32778
#define ID_CRITERIAMENU_PROPERTIES      32779
#define ID_PRODUCTGROUPMENU_ADD         32780
#define ID_PRODUCTGROUPMENU_DELETE      32781
#define ID_PRODUCTGROUPMENU_PROPERTIES  32782
#define ID_ASSIGNMENTMENU_UPDATE_COUNT  32784
#define ID_MENUITEM32801                32785
#define ID_ASSIGNMENTMENU_ASSIGN_PRODUTS 32786
#define ID_GENERIC_ADD                  32787
#define ID_GENERIC_DELETE               32788
#define ID_GENERIC_PROPERTIES           32789
#define IDR_PROFILE_WIZARD              32792
#define IDR_NEW_FACILITY                -32743
#define IDR_OPEN_FACILITY               -32742

// Next default values for new objects
// 
#ifdef APSTUDIO_INVOKED
#ifndef APSTUDIO_READONLY_SYMBOLS
#define _APS_NEXT_RESOURCE_VALUE        316
#define _APS_NEXT_COMMAND_VALUE         32795
#define _APS_NEXT_CONTROL_VALUE         1803
#define _APS_NEXT_SYMED_VALUE           101
#endif
#endif

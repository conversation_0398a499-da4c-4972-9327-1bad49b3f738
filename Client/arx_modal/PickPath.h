// PickPath.h: interface for the CPickPath class.
//
//////////////////////////////////////////////////////////////////////

#if !defined(AFX_PICKPATH_H__A8175053_5A32_4B98_BC6D_00885567FC06__INCLUDED_)
#define AFX_PICKPATH_H__A8175053_5A32_4B98_BC6D_00885567FC06__INCLUDED_

#if _MSC_VER > 1000
#pragma once
#endif // _MSC_VER > 1000

#include "FacilityElement.h"

class CPickPath : public CFacilityElement  
{
public:
	CPickPath();
	virtual ~CPickPath();

	int m_BaysInPattern;
	int m_Type;
	int m_StartSide;
	int m_Direction;

	CString m_DrawingHandle;

	CString m_LeftBayStart;
	int m_LeftBayStep;
	int m_LeftBayScheme;
	CString m_LeftBayPattern;

	CString m_LeftLevelStart;
	int m_LeftLevelStep;
	int m_LeftLevelScheme;
	int m_LeftLevelBreak;
	CString m_LeftLevelPattern;

	CString m_LeftLocStart;
	int m_LeftLocStep;
	int m_LeftLocScheme;
	int m_LeftLocBreak;
	CString m_LeftLocPattern;

	CString m_RightBayStart;
	int m_RightBayStep;
	int m_RightBayScheme;
	CString m_RightBayPattern;

	CString m_RightLevelStart;
	int m_RightLevelStep;
	int m_RightLevelScheme;
	int m_RightLevelBreak;
	CString m_RightLevelPattern;

	CString m_RightLocStart;
	int m_RightLocStep;
	int m_RightLocScheme;
	int m_RightLocBreak;
	CString m_RightLocPattern;

	typedef enum {
		ppCrossAisle = 0,
		ppFingerAisle = 1,
		ppStraightThrough = 2,
		ppTwoWay = 3,
		ppUPath = 4,
		ppZPath = 5
	} ppPickPathType;

	typedef enum {
		ppNoBreak = 0,
		ppBay = 1,
		ppLevel = 2,
		ppBoth = 3
	} ppBreakType;

	typedef enum {
		ppNoScheme = 0,
		ppEven = 1,
		ppOdd = 2
	} ppScheme;



};

#endif // !defined(AFX_PICKPATH_H__A8175053_5A32_4B98_BC6D_00885567FC06__INCLUDED_)

// DisplayResults.cpp : implementation file
//

#include "stdafx.h"
#include <stdlib.h>
#include "modal.h"
#include "DisplayResults.h"
#include "excel8.h"
#include "font.h"
#include "HelpService.h"
#include "UtilityHelper.h"
#include <errno.h>

#include <dbents.h>
#include "ValidateFacility.h"

#ifdef _DEBUG
#define new DEBUG_NEW
#undef THIS_FILE
static char THIS_FILE[] = __FILE__;
#endif



/////////////////////////////////////////////////////////////////////////////
// CDisplayResults dialog


int gSortToggle;
int gLastHeader;

const int MAX_ROWS = 65500;	// actual limit is 65536 but allow some for user-additions

extern CHelpService helpService;
extern CUtilityHelper utilityHelper;

extern char slotDir[256];

CDisplayResults::CDisplayResults(CWnd* pParent /*=NULL*/)
	: CDialog(CDisplayResults::IDD, pParent)
{
	//{{AFX_DATA_INIT(CDisplayResults)
	//}}AFX_DATA_INIT
	m_ListCtrlArray.RemoveAll();
	m_Data.RemoveAll();
	m_Headers.RemoveAll();
	gSortToggle = -1;
	m_NextCaption = "";
	m_NextCaption2 = "";
	m_NextCaption3 = "";
	m_NextClosesWindow = m_NextClosesWindow2 = m_NextClosesWindow3 = TRUE;
	m_WindowCaptions.RemoveAll();
	m_OrigColumnSize = -1;
	m_SelectionIdx = -1;
	m_MessageReceiver = NULL;
	m_IsModeless = FALSE;
	m_AllowSort = TRUE;
	m_AllowMultipleSelections = FALSE;
	
}


CDisplayResults::~CDisplayResults() 
{
	CWaitCursor cwc;
	//ads_printf("Before delete loop\n");
	for (int i = 0; i < m_ListCtrlArray.GetSize(); i++)
		delete m_ListCtrlArray[i];

	//ads_printf("After delete loop\n");

	m_ListCtrlArray.RemoveAll();

	//ads_printf("After ~CDisplayResults\n");
}

void CDisplayResults::DoDataExchange(CDataExchange* pDX)
{
	CDialog::DoDataExchange(pDX);
	//{{AFX_DATA_MAP(CDisplayResults)
	DDX_Control(pDX, IDC_SELECT_ALL, m_SelectAllButton);
	DDX_Control(pDX, IDNEXT3, m_NextBtn3);
	DDX_Control(pDX, IDNEXT2, m_NextBtn2);
	DDX_Control(pDX, IDEXCEL, m_ExcelBtn);
	DDX_Control(pDX, IDC_RESULTS_TAB, m_TabCtrl);
	DDX_Control(pDX, IDNEXT, m_NextBtn);
	DDX_Control(pDX, IDHELP, m_HelpBtn);
	DDX_Control(pDX, IDCANCEL, m_CloseBtn);
	//}}AFX_DATA_MAP
}


BEGIN_MESSAGE_MAP(CDisplayResults, CDialog)
	//{{AFX_MSG_MAP(CDisplayResults)
	ON_NOTIFY(TCN_SELCHANGE, IDC_RESULTS_TAB, OnSelchangeResultsTab)
	ON_BN_CLICKED(IDNEXT, OnNext)
	ON_BN_CLICKED(IDHELP, OnHelp)
	ON_WM_SIZE()
	ON_BN_CLICKED(IDEXCEL, OnExcel)
	ON_WM_HELPINFO()
	ON_BN_CLICKED(IDNEXT2, OnNext2)
	ON_BN_CLICKED(IDNEXT3, OnNext3)
	ON_BN_CLICKED(IDC_SELECT_ALL, OnSelectAll)
	ON_WM_MOVE()
	ON_WM_LBUTTONDBLCLK()
	ON_WM_RBUTTONDBLCLK()
	//}}AFX_MSG_MAP
END_MESSAGE_MAP()

/////////////////////////////////////////////////////////////////////////////
// CDisplayResults message handlers

void CDisplayResults::OnSelchangeResultsTab(NMHDR* pNMHDR, LRESULT* pResult) 
{
	UNREFERENCED_PARAMETER(pNMHDR);

	int newTab = m_TabCtrl.GetCurSel();
	m_ListCtrlArray[m_CurrentTab]->ShowWindow(SW_HIDE);
	m_ListCtrlArray[newTab]->ShowWindow(SW_SHOW);
	
	if (m_WindowCaptions.GetSize()-1 >= newTab)
		SetWindowText(m_WindowCaptions[newTab]);

	m_CurrentTab = newTab;

	
	*pResult = 0;
}

void CDisplayResults::OnCancel() 
{
	CListCtrl *pListCtrl = (CListCtrl *)m_ListCtrlArray[m_CurrentTab];
	POSITION pos;
	CWaitCursor cwc;

	pos = pListCtrl->GetFirstSelectedItemPosition();
	if (pos != NULL) {
		m_SelectionIdx = pListCtrl->GetNextSelectedItem(pos);
	}
	else {
		m_SelectionIdx = -1;
	}

	if (m_IsModeless) {
		//ads_printf("Before destroy window\n");
		DestroyWindow();
		//ads_printf("After destroy window\n");
	}
	else {
		//ads_printf("Before EndDialog\n");
		EndDialog(IDCANCEL);
		//ads_printf("After EndDialog\n");
	}
}


BOOL CDisplayResults::OnInitDialog() 
{
	CDialog::OnInitDialog();
	
	
	CRect r, r2;
	int i, count;
	CListCtrl *listCtrl;
	CButton *pButton;

	GetClientRect(&r2);
	m_ParentOldWidth = r2.Width();
	m_ParentOldHeight = r2.Height();

	m_TabCtrl.GetWindowRect(&r);
	ScreenToClient(&r);

	pButton = (CButton *)GetDlgItem(IDHELP);
	pButton->GetWindowRect(&r2);
	ScreenToClient(&r2);
	m_ButtonOrigXDiff = r.right - r2.left;
	m_ButtonOrigYDiff = r2.top - r.bottom;
	m_ButtonOrigWidth = r2.Width();
	m_ButtonOrigHeight = r2.Height();

	m_TabOrigWidth = r.Width();
	m_TabOrigHeight = r.Height();

	r.right = r.Width() - 10;
	r.left = 10;
	r.bottom = r.Height() - 10;
	r.top = 30;
	
	count = 0;

	


	if (m_NextCaption == "")
		m_NextBtn.ShowWindow(SW_HIDE);
	else {
		m_NextBtn.SetWindowText(m_NextCaption);
		m_NextBtn.ShowWindow(SW_SHOW);
	}

	if (m_NextCaption2 == "")
		m_NextBtn2.ShowWindow(SW_HIDE);
	else {
		m_NextBtn2.SetWindowText(m_NextCaption2);
		m_NextBtn2.ShowWindow(SW_SHOW);
	}
	
	if (m_NextCaption3 == "")
		m_NextBtn3.ShowWindow(SW_HIDE);
	else {
		m_NextBtn3.SetWindowText(m_NextCaption3);
		m_NextBtn3.ShowWindow(SW_SHOW);
	}

	for (i=0; i < m_Tabs.GetSize(); ++i) {
		m_TabCtrl.InsertItem(i, m_Tabs[i]);
		listCtrl = new CListCtrl;
		DWORD dwStyle;
		dwStyle = WS_CHILD|WS_VISIBLE|WS_TABSTOP|LVS_REPORT|WS_BORDER|LVS_SHOWSELALWAYS;
		if (! m_AllowMultipleSelections)
			dwStyle |= LVS_SINGLESEL;

		dwStyle &= ~(LVS_NOSCROLL);
		listCtrl->Create(dwStyle, r, &m_TabCtrl, WM_USER+100+i);
		listCtrl->ShowWindow(SW_HIDE);
		m_ListCtrlArray.Add(listCtrl);
		BuildHeaders(i);
		m_PageDataCount.Add(count);
	}
	
	if (m_WindowCaptions.GetSize() != 0)
		SetWindowText(m_WindowCaptions[0]);
	else {
		SetWindowText("Results");
		for (int i=0; i < m_Tabs.GetSize(); ++i)
			m_WindowCaptions.Add("Results");
	}

	LoadData();

	CBitmap bitmap;
	bitmap.LoadBitmap(IDB_EXCEL);
	m_ExcelBtn.SetBitmap((HBITMAP)bitmap.Detach());

	LoadToolTips();

	m_CurrentTab = 0;
	m_ListCtrlArray[0]->ShowWindow(SW_SHOW);
	
	if (m_AllowMultipleSelections)
		m_SelectAllButton.ShowWindow(SW_SHOW);
	else
		m_SelectAllButton.ShowWindow(SW_HIDE);

	return TRUE;  // return TRUE unless you set the focus to a control
	              // EXCEPTION: OCX Property Pages should return FALSE
}

void CDisplayResults::OnSize(UINT nType, int cx, int cy) 
{

	CDialog::OnSize(nType, cx, cy);

	if (nType == SIZE_MINIMIZED)
		return;

	int tabNewWidth, tabNewHeight;
	CRect r, tabRect;


//	if ( abs(cy-m_ParentOldHeight) < 300)
//		return;

	if (m_TabCtrl.m_hWnd != NULL) {
		m_TabCtrl.GetWindowRect(&r);	// screen coordinates
		ScreenToClient(&r);				// relative to parent
//		tabNewWidth = r.Width() + (cx - m_ParentOldWidth);
//		tabNewHeight = r.Height() + (cy - m_ParentOldHeight);
		
		tabNewWidth = cx - 20;
		tabNewHeight = cy - 102;

		m_TabCtrl.MoveWindow(r.left, r.top, tabNewWidth, tabNewHeight);
		CString temp;
		temp.Format("OPW: %d, OPH: %d, cx: %d, cy: %d, NW: %d, NH: %d, L: %d, R: %d, T: %d, B: %d",
			m_ParentOldWidth, m_ParentOldHeight, cx, cy, tabNewWidth, tabNewHeight, r.left, r.right, r.top, r.bottom);
		
	}

	m_TabCtrl.GetWindowRect(&tabRect);
	ScreenToClient(&tabRect);

	for (int i = 0; i < m_ListCtrlArray.GetSize(); ++i) {
		/*
		int oldHeaderWidth, newHeaderWidth;
		CRect r3;
		m_ListCtrlArray[i]->GetClientRect(&r3);
		oldHeaderWidth = r3.Width();
		double ratio;
		*/
		m_ListCtrlArray[i]->MoveWindow(10, 30, tabNewWidth-20, tabNewHeight-40);
		
		/*
		// Get the current ratio of header sizes
		CHeaderCtrl *h = m_ListCtrlArray[i]->GetHeaderCtrl();
		int c = h->GetItemCount();
		CRect r2;
		m_ListCtrlArray[i]->GetClientRect(&r2);
		newHeaderWidth = r2.Width();
		

		int w = r2.Width() / c;
	
		m_ListCtrlArray[i]->ShowWindow(SW_HIDE);
		for (int j = 0; j < c; j++) {
			int oldWidth = m_ListCtrlArray[i]->GetColumnWidth(j);
			ratio = ((double)oldWidth/(double)oldHeaderWidth);
			int newWidth = (int)(ratio * (double)newHeaderWidth + .5f);
			
			m_ListCtrlArray[i]->SetColumnWidth(j, newWidth);
		}

		
		if (i == m_CurrentTab)
			m_ListCtrlArray[i]->ShowWindow(SW_SHOW);
		*/
	}	
	
	float tabWidthRatio = (float)tabRect.Width()/(float)m_TabOrigWidth;
	//float tabHeightRatio = (float)tabRect.Height()/(float)m_TabOrigHeight;
	float tabHeightRatio = 1;
	float newX, newY, newCX, newCY;

	if (tabWidthRatio > 1)
		tabWidthRatio = 1;

	if (tabHeightRatio > 1)
		tabHeightRatio = 1;

	if (m_HelpBtn.m_hWnd != NULL) {
		m_HelpBtn.GetWindowRect(&r);
		ScreenToClient(&r);
		newCX = (float)m_ButtonOrigWidth * tabWidthRatio;
		newCY = (float)m_ButtonOrigHeight * tabHeightRatio;
		newX = tabRect.right - newCX;
		newY = tabRect.bottom + (float)m_ButtonOrigYDiff*tabHeightRatio;
		m_HelpBtn.SetWindowPos(NULL, (int)newX, (int)newY, (int)newCX, (int)newCY, 0);
	}

	if (m_CloseBtn.m_hWnd != NULL) {
		m_CloseBtn.GetWindowRect(&r);
		ScreenToClient(&r);
		newCX = (float)m_ButtonOrigWidth * tabWidthRatio;
		newCY = (float)m_ButtonOrigHeight * tabHeightRatio;
		newX = tabRect.right - newCX*2 - 10;
		newY = tabRect.bottom + (float)m_ButtonOrigYDiff*tabHeightRatio;
		m_CloseBtn.SetWindowPos(NULL, (int)newX, (int)newY, (int)newCX, (int)newCY, 0);
	}

	if (m_NextBtn.m_hWnd != NULL) {
		m_NextBtn.GetWindowRect(&r);
		ScreenToClient(&r);
		newCX = (float)m_ButtonOrigWidth * tabWidthRatio;
		newCY = (float)m_ButtonOrigHeight * tabHeightRatio;
		newX = tabRect.right - newCX*3 - 20;
		newY = tabRect.bottom + (float)m_ButtonOrigYDiff*tabHeightRatio;
		m_NextBtn.SetWindowPos(NULL, (int)newX, (int)newY, (int)newCX, (int)newCY, 0);
	}
	
	if (m_NextBtn2.m_hWnd != NULL) {
		m_NextBtn2.GetWindowRect(&r);
		ScreenToClient(&r);
		newCX = (float)m_ButtonOrigWidth * tabWidthRatio;
		newCY = (float)m_ButtonOrigHeight * tabHeightRatio;
		newX = tabRect.right - newCX*4 - 30;
		newY = tabRect.bottom + (float)m_ButtonOrigYDiff*tabHeightRatio;
		m_NextBtn2.SetWindowPos(NULL, (int)newX, (int)newY, (int)newCX, (int)newCY, 0);
	}
	
	if (m_NextBtn3.m_hWnd != NULL) {
		m_NextBtn3.GetWindowRect(&r);
		ScreenToClient(&r);
		newCX = (float)m_ButtonOrigWidth * tabWidthRatio;
		newCY = (float)m_ButtonOrigHeight * tabHeightRatio;
		newX = tabRect.right - newCX*5 - 40;
		newY = tabRect.bottom + (float)m_ButtonOrigYDiff*tabHeightRatio;
		m_NextBtn3.SetWindowPos(NULL, (int)newX, (int)newY, (int)newCX, (int)newCY, 0);
	}

	if (m_SelectAllButton.m_hWnd != NULL) {
		m_SelectAllButton.GetWindowRect(&r);
		ScreenToClient(&r);
		//newCX = (float)m_ButtonOrigWidth * tabWidthRatio;
		//newCY = (float)m_ButtonOrigHeight * tabHeightRatio;
		newX = (float)tabRect.left;
		newY = tabRect.bottom + (float)m_ButtonOrigYDiff*tabHeightRatio;
		m_SelectAllButton.SetWindowPos(NULL, (int)newX, (int)newY, (int)newCX, (int)newCY, 0);
	}

	m_ParentOldWidth = cx;
	m_ParentOldHeight = cy;

	// Added this because the buttons weren't being redrawn correctly; probably
	// need to change it to only invalidate the area around the buttons
	this->Invalidate(TRUE);

	if (m_MessageReceiver != NULL) {
		CRect r;
		this->GetWindowRect(&r);
		m_MessageReceiver->SendMessage(WM_DISPLAY_RESULTS_SIZE, (LPARAM)MAKELPARAM(r.top, r.bottom),
				(LPARAM)MAKELPARAM(r.left, r.right));
	}

}

BOOL CDisplayResults::OnNotify(WPARAM wParam, LPARAM lParam, LRESULT* pResult) 
{
	// Handle the user selecting a different item
	if (wParam == (unsigned int)(WM_USER+100+m_CurrentTab)) {
		NMLISTVIEW *plist = (LPNMLISTVIEW)lParam;
		if (plist->hdr.code == LVN_ITEMCHANGED  && ! m_Loading) {
			if (plist->uNewState & LVIS_SELECTED) {			// if they selected it
				// originalPosition is the position of the item in the list when it
				// was first displayed; they may have sorted it so we can't go by the
				// current position
				DWORD originalPosition = m_ListCtrlArray[m_CurrentTab]->GetItemData(plist->iItem);
				
				// the calling window can be passed in as the message receiver to get notified
				// when they change the selection; this is only relevant in non-modal mode.
				if (m_ListCtrlArray[m_CurrentTab]->GetSelectedCount() == 1) {
					if (m_MessageReceiver != NULL)
						m_MessageReceiver->SendMessage(WM_CHANGESELECTION, originalPosition, 0);
				}
			}
		}
	}
	else {
		NMHEADER *phdr = (LPNMHEADER)lParam;
		if (phdr->hdr.code == HDN_ITEMCLICK) {
			if (m_AllowSort) {
				CString temp;
				temp.Format("Clicked on %d with mouse button: %d", phdr->iItem, phdr->iButton);
				if (phdr->iItem == gLastHeader) {
					gSortToggle = 0 - gSortToggle;
				}
				else
					gSortToggle = 1;
				gLastHeader = phdr->iItem;
				SortList(m_TabCtrl.GetCurSel(), phdr->iItem);
			}
		}
	}

	return CDialog::OnNotify(wParam, lParam, pResult);
}


void CDisplayResults::BuildHeaders(int page)
{
	CRect rect;
	CStringArray headerList;
	CString headers, header;
	int idx, i, headerCount, colWidth;

	headers = m_Headers[page];

	m_ListCtrlArray[page]->GetWindowRect(&rect);

	idx = headers.Find("|");
	while (idx > 0) {
		header = headers.Left(idx);
		headerList.Add(header);
		headers = headers.Right(headers.GetLength()-(idx+1));
		idx = headers.Find("|");
	}

	headerCount = headerList.GetSize();
	for (i=0; i < headerCount; ++i) {
		if (m_OrigColumnSize == -1)
			colWidth = rect.Width()/headerCount;
		else
			colWidth = m_OrigColumnSize;
			
		m_ListCtrlArray[page]->InsertColumn(i, headerList.GetAt(i), LVCFMT_LEFT, colWidth, i);
	}

}


void CDisplayResults::LoadData()
{
	LVITEM lvItem;
	int nItem;
	CString line, cell;
	int idx, curPage;
	int i, j, k;
	CWaitCursor cwc;

	m_Loading = TRUE;

	lvItem.mask = LVIF_TEXT;

	for (i=0; i < m_Data.GetSize(); ++i) {
		line = m_Data[i];
		line.Replace("||", "| |");
		line.Replace("||", "| |");

		curPage = -1;
		if (m_HeaderKeys.GetSize() == 0) {
			curPage = 0;
		}
		else {
			idx = line.Find("|");
			if (idx > 0) {
				cell = line.Left(idx);		// the first column in the line is the code
				line = line.Right(line.GetLength()-(idx+1));	// the rest of the columns
				// check to see which page the field is in based on its code
				for (j=0; j < m_HeaderKeys.GetSize(); ++j) {
					if (cell.CompareNoCase(m_HeaderKeys[j]) == 0) {
						curPage = j;
						break;
					}
				}
			}
		}
		
		if (curPage < 0)
			continue;
	
		// Keep track of the original positions if this is a single page tab
		if (curPage == 0)
			m_PositionMap.SetAt(i, i);

		// Now we know which page this row goes on
		// Parse the data and add it to the list control
		idx = line.Find("|");
		k = 0;
		while (idx >= 0) {
			cell = line.Left(idx);
			lvItem.pszText = cell.GetBuffer(0);
			cell.ReleaseBuffer();
			lvItem.iSubItem = k;

			// the first column (not counting the code) is the parent for that line
			if (k == 0) {
				lvItem.iItem = m_PageDataCount[curPage];
				nItem = m_ListCtrlArray[curPage]->InsertItem(&lvItem);
				m_ListCtrlArray[curPage]->SetItemData(nItem, lvItem.iItem);
				m_PageDataCount[curPage]++;
			}
			// the rest of the columns get associated with the first column
			else {
				lvItem.iItem = nItem;
				m_ListCtrlArray[curPage]->SetItem(&lvItem);
			}
			k++;
			if (k >= m_ListCtrlArray[curPage]->GetHeaderCtrl()->GetItemCount())
				break;
			line = line.Right(line.GetLength()-(idx+1));
			idx = line.Find("|");
		}
	}


	// Update Page Count Information
	CString tmp;
	for (i=0; i < m_Tabs.GetSize(); ++i) {
		tmp.Format("%s - %d records", m_WindowCaptions[i], m_PageDataCount[i]);
		m_WindowCaptions[i] = tmp;
	}

	SetWindowText(m_WindowCaptions[0]);

	m_Loading = FALSE;

	return;

}




void CDisplayResults::SortList(int page, int sortColumn)
{

	CListCtrl *pListCtrl = m_ListCtrlArray[page];

	CString temp;
	CString temp2;
	CStringArray tempArray;
	int maxlen = -1;

	CWaitCursor cwc;

	// first move the items from the list control into a pipe-delimited array
	for (int i=0; i < pListCtrl->GetItemCount(); ++i) {
		temp = "";
		for (int j=0; j < pListCtrl->GetHeaderCtrl()->GetItemCount(); ++j) {
			// put the sort column at the beginning of the line so it will
			// be compared first; it will still be at its normal position in the
			// line for display; we will pull the extra first field out before
			// we redisplay
			// also put the original position (stored in item data) so if the user
			// selects one we can pass that back to the calling program even if
			// they sorted it differently
			if (j == sortColumn) {
				DWORD lParam = pListCtrl->GetItemData(i);
				temp2.Format("%s|%d|%s", pListCtrl->GetItemText(i, j), lParam, temp);
				temp = temp2;
			}
			
			// add the column value to the line
			temp += pListCtrl->GetItemText(i, j);
			temp += "|";
		}
		//temp += "|";

		// keep track of the maximum length of a line; we need this for memory allocation
		if (temp.GetLength() > maxlen)
			maxlen = temp.GetLength();

		temp2 += temp;
		temp2 += "\r\n";
		tempArray.Add(temp);
	}

	//AfxMessageBox(temp2);

	char **pointers;
	int size;
		
	size = tempArray.GetSize();

	// allocate memory for all of the strings
	pointers = (char **)malloc(size*sizeof(char *));
	for (int k=0; k < size; ++k) {
		*(pointers+k) = (char *)malloc(maxlen+1);
		strcpy(*(pointers+k), tempArray[k].GetBuffer(0));
		//strcpy(pointers[k], tempArray[k].GetBuffer(0));
		tempArray[k].ReleaseBuffer();
	}
	
	// sort the list
	qsort(pointers, size, sizeof(char *),  (int (*)(const void *, const void *))Compare);
	ads_printf("Sort completed.\n");

	tempArray.RemoveAll();
	temp2.Empty();
	
	for (int l=0; l < size; ++l) {
		temp = *(pointers+l);
		//strcpy(temp.GetBuffer(0), *(pointers+l));
		//strcpy(temp.GetBuffer(0), pointers[l]);
		//temp.ReleaseBuffer();
		tempArray.Add(temp);
		temp2 += pointers[l];
		temp2 += "\r\n";
	}
	//AfxMessageBox(temp2);

	// free the memory
	for (int m=0; m < size; ++m)
		free(*(pointers+m));

	free(pointers);

	LVITEM lvItem;
	int nItem;
	CString line, cell;
	int idx;
	DWORD originalPosition;

	lvItem.mask = LVIF_TEXT;
	m_PageDataCount[page] = 0;
	m_ListCtrlArray[page]->DeleteAllItems();

	// re-build the list control page
	for (i=0; i < size; ++i) {
		line = tempArray[i];

		idx = line.Find("|");
		// skip the first one - it was our sort key
		line = line.Right(line.GetLength()-(idx+1));
		idx = line.Find("|");
		// the next one is the original position in the list
		originalPosition = atoi(line.Left(idx));
		m_PositionMap.SetAt(originalPosition, i);

		line = line.Right(line.GetLength()-(idx+1));
		idx = line.Find("|");
		k = 0;
		while (idx > 0) {
			cell = line.Left(idx);
			lvItem.pszText = cell.GetBuffer(0);
			cell.ReleaseBuffer();
			lvItem.iSubItem = k;
			if (k == 0) {
				lvItem.iItem = m_PageDataCount[page];
				nItem = m_ListCtrlArray[page]->InsertItem(&lvItem);
				m_ListCtrlArray[page]->SetItemData(nItem, originalPosition);
				m_PageDataCount[page]++;
			}
			else {
				lvItem.iItem = nItem;
				m_ListCtrlArray[page]->SetItem(&lvItem);
			}
			k++;
			line = line.Right(line.GetLength()-(idx+1));
			idx = line.Find("|");
		}
	}

	return;
}


int CDisplayResults::Compare(const void **p1, const void **p2) 
{
	
	char *c1 = (char *)*p1;
	char *c2 = (char *)*p2;

	if (strncmp(c1, "Totals:", 7) == 0)
		return 1;

	if (strncmp(c2, "Totals:", 7) == 0)
		return -1;

	return gSortToggle*strcmp(c1, c2);

}

void CDisplayResults::DisplayItems(int page)
{

	CListCtrl *pListCtrl = m_ListCtrlArray[page];

	CString temp;
	CString temp2;
	CStringArray tempArray;
	int max = -1;

	for (int i=0; i < pListCtrl->GetItemCount(); ++i) {
		temp = "";
		for (int j=0; j < pListCtrl->GetHeaderCtrl()->GetItemCount(); ++j) {
			temp += pListCtrl->GetItemText(i, j);
			temp += "|";
		}
		//temp += "|";

		if (temp.GetLength() > max)
			max = temp.GetLength();
		temp2 += temp;
		temp2 += "\r\n";

	}


}

void CDisplayResults::OnExcel() 
{

	HDITEM hdi;
	
	hdi.mask = HDI_TEXT;
	char lpBuffer[256];
	hdi.pszText = lpBuffer;
	hdi.cchTextMax = 256;
	
	CListCtrl *pListCtrl = m_ListCtrlArray[m_CurrentTab];
	CString fileName, directory;
	int fileCtr;
	
	COleSafeArray outer;
	COleSafeArray *inner;
	SAFEARRAYBOUND rgsabound, rgsabound2;
	long index1;
	long index2;

    rgsabound.lLbound = 0;
    rgsabound.cElements = pListCtrl->GetHeaderCtrl()->GetItemCount();
	rgsabound2.lLbound = 0;
	rgsabound2.cElements = 2;

	DWORD numElements[] = {2};

	outer.Create(VT_VARIANT, 1, &rgsabound);
	
	//outer.CreateOneDim(VT_VARIANT, pListCtrl->GetHeaderCtrl()->GetItemCount());

	inner = new COleSafeArray[pListCtrl->GetHeaderCtrl()->GetItemCount()];
	CWaitCursor cwc;

	// The maximum number of rows in an Excel worksheet is currently
	// 65536.  So if we have more lines than that, we will have to create
	// multiple files, each with 65536 lines until we have used up all the 
	// lines.  Then we do some bizarre Excel stuff to open each file individually
	// and move them all into a single book with multiple sheets.  If there's
	// a better way, please tell me.

	int itemStart = 0;

	try {
		FILE *f;
		
		for (int sheetCount=1; sheetCount <= pListCtrl->GetItemCount()/MAX_ROWS+1; ++sheetCount) {
		
			directory = getenv("TEMP");
			if (directory.IsEmpty())
				directory = "c:";

			for (fileCtr=0; fileCtr < 10; ++fileCtr) {
				fileName.Format("%s\\OptimizeResults%d-%d.xls", directory, fileCtr, sheetCount);
			
				f = fopen(fileName, "w");
				if (f == NULL) {
					if (errno != EACCES) {
						int x = errno;
						AfxMessageBox("Error creating temporary file.");
						ads_printf("%d\n", errno);
						return;
					}
				}
				else
					break;
			}

			if (f == NULL) {
				AfxMessageBox("Error creating temporary file.  Maximum number of files are open.");
				return;
			}

			CStringArray strings;
			if (m_ColumnFormat.GetSize() > m_CurrentTab) {
				utilityHelper.ParseString(m_ColumnFormat[m_CurrentTab], "|", strings);
			}

			// write the header
			for (int j=0; j < pListCtrl->GetHeaderCtrl()->GetItemCount(); ++j) {			
				//pListCtrl->GetHeaderCtrl()->GetItem(j,&hdi);
				//fprintf(f, "\"%s\"|", hdi.pszText);
				// This weirdness is to format each cell as text so it won't
				// take leading zeros off, etc
				// Excel expects an array of two-item arrays; the first
				// item is the column number and the second is the format;
				// text format = 2
				if (sheetCount == 1) {
					inner[j].Create(VT_I2, 1, &rgsabound2);			
					index2 = 0;
					inner[j].PutElement(&index2, &j);
					index2 = 1;
					short k;
					//if (strings.GetSize() > j)
					//	k = (short)atoi(strings[j]);
					//else
					k = 2;		// default to text format
					inner[j].PutElement(&index2, &k);
					index1 = j;
					outer.PutElement(&index1, COleVariant(inner[j]));
				}
				
			}
			for (int i=0; i< pListCtrl->GetHeaderCtrl()->GetItemCount() ;i++)
			{
				pListCtrl->GetHeaderCtrl()->GetItem(i,&hdi);
				fprintf(f, "%s|", hdi.pszText);
			}
			fprintf(f, "\n");
			
			
			int itemCount = itemStart + MAX_ROWS;
			if (itemCount > pListCtrl->GetItemCount())
				itemCount = pListCtrl->GetItemCount();

			for (int i=itemStart; i < itemCount; ++i) {
				for (int j=0; j < pListCtrl->GetHeaderCtrl()->GetItemCount(); ++j) {
					fprintf(f, "%s|", pListCtrl->GetItemText(i,j));
				}
				fprintf(f, "\n");
			}
			itemStart += MAX_ROWS;

			fclose(f);
		}
		

	}
	catch (...) {
		AfxMessageBox("Error creating file for opening in Excel.");
		return;
	}


	try
	{
		_Application app;     // app is an _Application object.
		_Workbook book, mainBook;       // More object declarations.
		_Worksheet sheet, mainSheet;
		Workbooks books;
		Worksheets sheets, mainSheets;
		Range range;          // Used for Microsoft Excel 97 components.
		LPDISPATCH lpDisp;    // Often reused variable.
		Range cols;

		// Common OLE variants. Easy variants to use for calling arguments.
		COleVariant
			covTrue((short)TRUE),
			covFalse((short)FALSE),
			covOptional((long)DISP_E_PARAMNOTFOUND, VT_ERROR);
		
		// Start Microsoft Excel, get _Application object,
		// and attach to app object.
		if(!app.CreateDispatch("Excel.Application")) {
			AfxMessageBox("Could not start Excel.");
			return;
		}
		
		app.SetWindowState(-4140);		// -4140 = xlMinimized
		app.SetVisible(TRUE);
		app.SetUserControl(TRUE);

		// Get the Workbooks collection.
		lpDisp = app.GetWorkbooks();     // Get an IDispatch pointer.
		ASSERT(lpDisp);
		books.AttachDispatch(lpDisp);    // Attach the IDispatch pointer
										// to the books object.

		VARIANT sheetVar = {0};

		for (int sheetCount=1; sheetCount <= pListCtrl->GetItemCount()/MAX_ROWS+1; ++sheetCount) {
			
			fileName = getenv("TEMP");
			if (fileName.IsEmpty())
				fileName = "c:";
			
			fileName.Format("%s\\OptimizeResults%d-%d.xls", directory, fileCtr, sheetCount);
			
			// open the file as text specifiying pipe-delimited
			books.OpenText(fileName, 
				covOptional,				// Origin
				covOptional,				// Start row
				COleVariant((short)(1)),	// Datatype
				1,							// TextQualifier 
					covOptional,				// Consecutive delimiter
				covOptional,				// Tab
				covOptional,				// Semicolon
				covOptional,				// Comma
				covOptional,				// Space
				covTrue,					// Other
				COleVariant("|"),			// OtherChar
				COleVariant(outer),			// FieldInfo
				covOptional);				// TextVisualLayout

		
			// Get the book
			if (sheetCount == 1)
				lpDisp = books.GetItem(COleVariant((short)(1)));
			else
				lpDisp = books.GetItem(COleVariant((short)(2)));
			ASSERT(lpDisp);
			book.AttachDispatch(lpDisp);
			
			// Get the sheets
			lpDisp = book.GetSheets();
			ASSERT(lpDisp);
			sheets.AttachDispatch(lpDisp);
				
			// Get the sheet
			lpDisp = sheets.GetItem(COleVariant((short)1));
			//GetItem(const VARIANT &index)
			ASSERT(lpDisp);
			sheet.AttachDispatch(lpDisp);
			
			CString temp;
			temp.Format("Page %d", sheetCount);
			sheet.SetName(temp);
			
			// Change the entire worksheet to be autofitt
			range = sheet.GetRange(COleVariant("A1"), COleVariant("Z1"));
			Font8 font;
			font = range.GetFont();
			font.SetBold(covTrue);

			cols = range.GetEntireColumn();
			cols.AutoFit();
		
			if (sheetCount == 1) {
				// Get the first book and hold it for use by the others
				lpDisp = books.GetItem(COleVariant((short)1));
				ASSERT(lpDisp);
				mainBook.AttachDispatch(lpDisp);
				
				// Same for the sheets
				lpDisp = mainBook.GetSheets();
				ASSERT(lpDisp);
				mainSheets.AttachDispatch(lpDisp);

			}

			else {
				// Get the previous sheet we inserted in the main book
				lpDisp = mainSheets.GetItem(COleVariant((short)(sheetCount-1)));
				ASSERT(lpDisp);
				mainSheet.AttachDispatch(lpDisp);
				
				// Create a Variant to represent the previous sheet object
				// We will use this to copy the subsequent sheets
				sheetVar.vt = VT_DISPATCH;
				sheetVar.pdispVal = mainSheet.m_lpDispatch;
				mainSheet.m_lpDispatch->AddRef();
					
				// Add an additional sheet to the new workbook so that we can 
				// move the new sheet without getting an error because there 
				// are no sheets left because a book must have a minimum of one 
				// sheet and we are moving one
				
				// Use the default placement which will put it first
				_Worksheet tempSheet;
				tempSheet = sheets.Add(covOptional, covOptional, covOptional, covOptional);

				// Now move the new sheet to the main book
				sheet.Move(covOptional, sheetVar);

				VariantClear(&sheetVar);

				// Now close the new workbook without saving
				book.Close(covFalse, covOptional, covOptional);

			}

		}

		lpDisp = mainSheets.GetItem(COleVariant((short)1));
		ASSERT(lpDisp);
		sheet.AttachDispatch(lpDisp);
		sheet.Activate();

		app.SetWindowState(-4143);		// -4143 = xlNormal
		app.ReleaseDispatch();

      } // End of processing.

	  catch(COleException *e)
      {
		  char buf[1024];     // For the Try...Catch error message.
		  sprintf(buf, "COleException. SCODE: %08lx.  %x", (long)e->m_sc, COleException::Process(e));
		  //::MessageBox(NULL, buf, "COleException", MB_SETFOREGROUND | MB_OK);
		  ads_printf("COleExceptions: %s\n", buf);
      }
	  
      catch(COleDispatchException *e)
      {
		  char buf[1024];     // For the Try...Catch error message.
		  sprintf(buf,
			  "COleDispatchException. SCODE: %08lx, Description: \"%s\".",
			  (long)e->m_wCode,(LPSTR)e->m_strDescription.GetBuffer(512));
		  //::MessageBox(NULL, buf, "COleDispatchException",
		  //MB_SETFOREGROUND | MB_OK);
		  ads_printf("COleDispatchException: %s\n", buf);
	  }
	  
      catch(...)
      {
		  //::MessageBox(NULL, "General Exception caught.", "Catch-All",
		  //MB_SETFOREGROUND | MB_OK);
		  ads_printf("General exception caught executing Excel");
      }

	  // Clean up the format arrays
	  for (int i=0; i < pListCtrl->GetHeaderCtrl()->GetItemCount(); ++i) {
		  //inner[i].Destroy();	
		  inner[i].Detach();	
	  }
	  delete [] inner;
	  outer.Detach();


	  return;

}


void CDisplayResults::LoadToolTips()
{

	EnableToolTips(TRUE);

	m_ToolTip.Create(this, TTF_IDISHWND);
	m_ToolTip.Activate(TRUE);
	
	m_ToolTip.AddTool(GetDlgItem(IDEXCEL),"View in Excel");
	m_ToolTip.AddTool(GetDlgItem(IDCANCEL),"Close");
	m_ToolTip.AddTool(GetDlgItem(IDHELP),"Help");
	m_ToolTip.AddTool(GetDlgItem(IDNEXT),m_NextCaption);
	m_ToolTip.AddTool(GetDlgItem(IDNEXT+1), m_NextCaption2);
	m_ToolTip.AddTool(GetDlgItem(IDNEXT+2), m_NextCaption3);

}


BOOL CDisplayResults::PreTranslateMessage(MSG* pMsg) 
{
	
	if(pMsg->message== WM_LBUTTONDOWN ||
        pMsg->message== WM_LBUTTONUP ||
        pMsg->message== WM_MOUSEMOVE)
		m_ToolTip.RelayEvent(pMsg);


	return CDialog::PreTranslateMessage(pMsg);
}



BOOL CDisplayResults::OnHelpInfo(HELPINFO* pHelpInfo) 
{
	
	// See if they want context help for the list box
	if (pHelpInfo->iCtrlId >= WM_USER+100
		&& pHelpInfo->iCtrlId < WM_USER+100+m_Tabs.GetSize()) {
		if (m_ListHelpTopics.GetSize() > m_CurrentTab)
			helpService.ShowFieldHelp(m_ListHelpTopics[m_CurrentTab]);
		else if (m_MainHelpTopic != "")
			helpService.ShowScreenHelp(m_MainHelpTopic);
		else
			helpService.ShowScreenHelp(IDD);
	}
	else {
		switch (pHelpInfo->iCtrlId)
		{
		case IDCANCEL:
		case IDEXCEL:
		case IDHELP:
			helpService.ShowFieldHelp(IDD, pHelpInfo->iCtrlId);
			break;
		case IDC_SELECT_ALL:
			if (m_SelectAllHelp != "")
				helpService.ShowFieldHelp(m_SelectAllHelp);
			else
				helpService.ShowFieldHelp(IDD, pHelpInfo->iCtrlId);
			break;
		case IDNEXT:
			if (m_NextHelp1 != "")
				helpService.ShowFieldHelp(m_NextHelp1);
			else if (m_MainHelpTopic != "")
				helpService.ShowScreenHelp(m_MainHelpTopic);
			else
				helpService.ShowFieldHelp(IDD, pHelpInfo->iCtrlId);
			break;
		case IDNEXT2:
			if (m_NextHelp2 != "")
				helpService.ShowFieldHelp(m_NextHelp2);
			else if (m_MainHelpTopic != "")
				helpService.ShowScreenHelp(m_MainHelpTopic);
			else
				helpService.ShowFieldHelp(IDD, pHelpInfo->iCtrlId);
			break;
		case IDNEXT3:
			if (m_NextHelp3 != "")
				helpService.ShowFieldHelp(m_NextHelp3);
			else if (m_MainHelpTopic != "")
				helpService.ShowScreenHelp(m_MainHelpTopic);
			else
				helpService.ShowFieldHelp(IDD, pHelpInfo->iCtrlId);
			break;
			
		default:
			if (m_HelpTopics.GetSize() > m_CurrentTab)
				helpService.ShowScreenHelp(m_HelpTopics[m_CurrentTab]);
			else if (m_MainHelpTopic != "")
				helpService.ShowScreenHelp(m_MainHelpTopic);
			else
				helpService.ShowScreenHelp(IDD);
		}
	}

	return TRUE;	

}


void CDisplayResults::OnHelp() 
{
	if (m_HelpTopics.GetSize() > m_CurrentTab)
		helpService.ShowScreenHelp(m_HelpTopics[m_CurrentTab]);
	else if (m_MainHelpTopic != "")
		helpService.ShowScreenHelp(m_MainHelpTopic);
	else
		helpService.ShowScreenHelp(IDD);


}

void CDisplayResults::PostNcDestroy() 
{
	if (m_MessageReceiver != NULL)
		m_MessageReceiver->SendMessage(WM_CLOSEDISPLAY, (unsigned int)this, 0);
	
	if (m_IsModeless) {
		//ads_printf("Before delete\n");
		delete this;	
		//ads_printf("After delete\n");
	}
	else {
		//ads_printf("Before PostNcDestroy\n");
		CDialog::PostNcDestroy();
		//ads_printf("After PostNcDestroy\n");
	}
}

void CDisplayResults::ReloadPage()
{
	LVITEM lvItem;
	int nItem;
	CString line, cell;
	CString headerKey, tmp;
	int idx, i, k;

	int page = m_CurrentTab;

	m_Loading = TRUE;

	lvItem.mask = LVIF_TEXT;
	m_PageDataCount[page] = 0;
	m_ListCtrlArray[page]->DeleteAllItems();
	if (m_HeaderKeys.GetSize() == 0)
		headerKey = "";
	else
		headerKey = m_HeaderKeys[page];

	// re-build the list control page
	for (i=0; i < m_Data.GetSize(); ++i) {
		line = m_Data[i];

		idx = line.Find("|");
		if (headerKey != "") {
			tmp = line.Left(idx);
			if (tmp.CompareNoCase(headerKey) != 0)
				continue;
			line = line.Right(line.GetLength() - (idx+1));
			idx = line.Find("|");
		}
		k = 0;
		while (idx > 0) {
			cell = line.Left(idx);
			lvItem.pszText = cell.GetBuffer(0);
			cell.ReleaseBuffer();
			lvItem.iSubItem = k;
			if (k == 0) {
				CString tmp;
				tmp.Format("-%d", m_PageDataCount[page]);
				strcat(lvItem.pszText, tmp);

				lvItem.iItem = m_PageDataCount[page];
				nItem = m_ListCtrlArray[page]->InsertItem(&lvItem);
				m_ListCtrlArray[page]->SetItemData(nItem, i);
				m_PageDataCount[page]++;
			}
			else {
				lvItem.iItem = nItem;
				m_ListCtrlArray[page]->SetItem(&lvItem);
			}
			k++;
			line = line.Right(line.GetLength()-(idx+1));
			idx = line.Find("|");
		}
	}

	m_Loading = FALSE;
	return;

}

void CDisplayResults::OnNext() 
{
	POSITION pos;
	int curSel = -1;
	DWORD originalPosition = NO_SELECTION;
	
	m_SelectionList.RemoveAll();

	pos = m_ListCtrlArray[m_CurrentTab]->GetFirstSelectedItemPosition();

	while (pos != NULL) {
		curSel = m_ListCtrlArray[m_CurrentTab]->GetNextSelectedItem(pos);
		
		if (curSel >= 0) {
			originalPosition = m_ListCtrlArray[m_CurrentTab]->GetItemData(curSel);
			m_SelectionList.Add(originalPosition);
		}
	}

	// Send back the first selection; if they want more they can look at the list
	if (m_MessageReceiver != NULL) {
		if (m_SelectionList.GetSize() > 0)
			m_MessageReceiver->SendMessage(WM_DISPLAY_RESULTS_BUTTON1, m_SelectionList[0], m_SelectionList.GetSize());
		else
			m_MessageReceiver->SendMessage(WM_DISPLAY_RESULTS_BUTTON1, NULL, 0);
	}

	if (m_IsModeless) {
		if (m_NextClosesWindow)
			DestroyWindow();
	}
	else {
		if (m_NextClosesWindow)
			EndDialog(IDC_DISPLAY_RESULTS_NEXT1);
	}

}

void CDisplayResults::OnNext2() 
{
	POSITION pos;
	int curSel = -1;
	DWORD originalPosition = NO_SELECTION;

	m_SelectionList.RemoveAll();

	pos = m_ListCtrlArray[m_CurrentTab]->GetFirstSelectedItemPosition();

	while (pos != NULL) {
		curSel = m_ListCtrlArray[m_CurrentTab]->GetNextSelectedItem(pos);

		if (curSel >= 0) {
			originalPosition = m_ListCtrlArray[m_CurrentTab]->GetItemData(curSel);
			m_SelectionList.Add(originalPosition);
		}
	}

	if (m_MessageReceiver != NULL) {
		if (m_SelectionList.GetSize() > 0)
			m_MessageReceiver->SendMessage(WM_DISPLAY_RESULTS_BUTTON2, m_SelectionList[0], m_SelectionList.GetSize());
		else
			m_MessageReceiver->SendMessage(WM_DISPLAY_RESULTS_BUTTON2, NULL, 0);
	}

	if (m_IsModeless) {
		if (m_NextClosesWindow2)
			DestroyWindow();
	}
	else {
		if (m_NextClosesWindow2)
			EndDialog(IDC_DISPLAY_RESULTS_NEXT2);
	}
	
}

void CDisplayResults::OnNext3() 
{
	POSITION pos;
	int curSel = -1;
	DWORD originalPosition = NO_SELECTION;

	m_SelectionList.RemoveAll();
	
	pos = m_ListCtrlArray[m_CurrentTab]->GetFirstSelectedItemPosition();
	while (pos != NULL) {
		curSel = m_ListCtrlArray[m_CurrentTab]->GetNextSelectedItem(pos);
		
		if (curSel >= 0) {
			originalPosition = m_ListCtrlArray[m_CurrentTab]->GetItemData(curSel);
			m_SelectionList.Add(originalPosition);
		}
	}

	if (m_MessageReceiver != NULL) {
		if (m_SelectionList.GetSize() > 0)
			m_MessageReceiver->SendMessage(WM_DISPLAY_RESULTS_BUTTON3, m_SelectionList[0], m_SelectionList.GetSize());
		else
			m_MessageReceiver->SendMessage(WM_DISPLAY_RESULTS_BUTTON3, NULL, 0);
	}

	if (m_IsModeless) {
		if (m_NextClosesWindow3)
			DestroyWindow();
	}
	else {
		if (m_NextClosesWindow3)
			EndDialog(IDC_DISPLAY_RESULTS_NEXT3);
	}
	
}

// Change an item.  The calling program only knows about the original
// position of the item.  Since it may have been re-sorted since then,
// use that to find the current position
void CDisplayResults::ModifyItem(int originalPosition, CString &newData)
{
	CString line, cell;
	CString headerKey, tmp;
	int idx, k, currentPosition;

	if (! m_PositionMap.Lookup(originalPosition, currentPosition))
		return;
	
	int page = m_CurrentTab;
	
	m_Loading = TRUE;
		
	if (m_HeaderKeys.GetSize() == 0)
		headerKey = "";
	else
		headerKey = m_HeaderKeys[page];
	
	line = newData;

	m_Data[originalPosition] = line;

	idx = line.Find("|");
	if (headerKey != "") {
		tmp = line.Left(idx);
		if (tmp.CompareNoCase(headerKey) != 0)
			return;		 // not a match
		line = line.Right(line.GetLength() - (idx+1));
		idx = line.Find("|");
	}

	k = 0;
	while (idx > 0) {
		cell = line.Left(idx);
		m_ListCtrlArray[page]->SetItemText(currentPosition, k, cell.GetBuffer(0));
		cell.ReleaseBuffer();
		k++;
		line = line.Right(line.GetLength()-(idx+1));
		idx = line.Find("|");
	}
	
	m_Loading = FALSE;
}

// This function deletes an item - we have to be careful because
// if they re-sort the list, the calling program no
// longer knows where the item is. So we keep track of the
// original position
void CDisplayResults::DeleteItem(int originalPosition)
{
	int currentPosition;
	int newOriginalPosition;
	int oldOriginalPosition;
	CString tmp1, tmp2;

	if (! m_PositionMap.Lookup(originalPosition, currentPosition))
		return;
	
	m_Data.RemoveAt(originalPosition);
	m_ListCtrlArray[m_CurrentTab]->DeleteItem(currentPosition);
	m_PageDataCount[m_CurrentTab]--;
	
	m_PositionMap.RemoveAll();

	// Re-build the position map
	for (int i=0; i < m_ListCtrlArray[m_CurrentTab]->GetItemCount(); ++i) {
		// this is where the item was in the original list
		oldOriginalPosition = m_ListCtrlArray[m_CurrentTab]->GetItemData(i);

		// if the item was before the one we deleted
		// then it's original position hasn't changed
		if (oldOriginalPosition < originalPosition)
			newOriginalPosition = oldOriginalPosition;
		// if the item was after the one we deleted
		// then it's actual old position is one less than before
		else
			newOriginalPosition = oldOriginalPosition - 1;

		m_PositionMap.SetAt(newOriginalPosition, i);
		
		m_ListCtrlArray[m_CurrentTab]->SetItemData(i, newOriginalPosition);

	}

	// Update the caption
	tmp1.Format("%d records", m_PageDataCount[m_CurrentTab]+1);
	tmp2.Format("%d records", m_PageDataCount[m_CurrentTab]);
	m_WindowCaptions[m_CurrentTab].Replace(tmp1, tmp2);
	SetWindowText(m_WindowCaptions[m_CurrentTab]);
}

void CDisplayResults::OnSelectAll() 
{
	UpdateData(TRUE);

	for (int i=0; i < m_ListCtrlArray[m_CurrentTab]->GetItemCount(); ++i) {
		if (m_SelectAllButton.GetCheck())
			m_ListCtrlArray[m_CurrentTab]->SetItemState(i, LVIS_SELECTED, LVIS_SELECTED);
		else
			m_ListCtrlArray[m_CurrentTab]->SetItemState(i, 0, LVIS_SELECTED);
	}


	return;
}

void CDisplayResults::OnMove(int x, int y) 
{
	CDialog::OnMove(x, y);

	if (m_MessageReceiver != NULL) {
		CRect r;
		this->GetWindowRect(&r);
		m_MessageReceiver->SendMessage(WM_DISPLAY_RESULTS_SIZE, (LPARAM)MAKELPARAM(r.top, r.bottom),
			(LPARAM)MAKELPARAM(r.left, r.right));
	}
	
}

void CDisplayResults::OnLButtonDblClk(UINT nFlags, CPoint point) 
{
	m_ListCtrlArray[m_CurrentTab]->ShowWindow(SW_HIDE);
	CHeaderCtrl *h = m_ListCtrlArray[m_CurrentTab]->GetHeaderCtrl();
	int c = h->GetItemCount();
	CRect r;
	h->GetClientRect(&r);

	for (int j = 0; j < c; j++) {
		int newWidth = (int)(r.Width()/c);	
		m_ListCtrlArray[m_CurrentTab]->SetColumnWidth(j, newWidth);
	}
	
	
	m_ListCtrlArray[m_CurrentTab]->ShowWindow(SW_SHOW);
	
	CDialog::OnLButtonDblClk(nFlags, point);
}

void CDisplayResults::OnRButtonDblClk(UINT nFlags, CPoint point) 
{

	m_ListCtrlArray[m_CurrentTab]->ShowWindow(SW_HIDE);
	CHeaderCtrl *h = m_ListCtrlArray[m_CurrentTab]->GetHeaderCtrl();
	int c = h->GetItemCount();
	CRect r;
	h->GetClientRect(&r);

	for (int j = 0; j < c; j++) {
		HDITEM hdi;
		hdi.mask = HDI_TEXT;
		TCHAR lpBuffer[256];
		hdi.pszText = lpBuffer;
		hdi.cchTextMax = 256;

		h->GetItem(j, &hdi);

		int newWidth = m_ListCtrlArray[m_CurrentTab]->GetStringWidth(hdi.pszText);
		
		m_ListCtrlArray[m_CurrentTab]->SetColumnWidth(j, LVSCW_AUTOSIZE_USEHEADER);
	}
	
	
	m_ListCtrlArray[m_CurrentTab]->ShowWindow(SW_SHOW);

	CDialog::OnRButtonDblClk(nFlags, point);
}

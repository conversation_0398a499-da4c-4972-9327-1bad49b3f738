// SideProfileButton.cpp : implementation file
//

#include "stdafx.h"
#include "modal.h"
#include "SideProfileButton.h"
#include "SideProfileBayPage.h"

#ifdef _DEBUG
#define new DEBUG_NEW
#undef THIS_FILE
static char THIS_FILE[] = __FILE__;
#endif

/////////////////////////////////////////////////////////////////////////////
// CSideProfileButton

CSideProfileButton::CSideProfileButton()
{
}

CSideProfileButton::~CSideProfileButton()
{
}


BEGIN_MESSAGE_MAP(CSideProfileButton, CButton)
	//{{AFX_MSG_MAP(CSideProfileButton)
	ON_WM_LBUTTONDOWN()
	ON_WM_LBUTTONDBLCLK()
	ON_WM_MOUSEMOVE()
	//}}AFX_MSG_MAP
END_MESSAGE_MAP()

/////////////////////////////////////////////////////////////////////////////
// CSideProfileButton message handlers

void CSideProfileButton::DrawItem(LPDRAWITEMSTRUCT lpDrawItemStruct) 
{
	CDC drawnDC;
	drawnDC.Attach(lpDrawItemStruct->hDC);
	CRect r;
	GetClientRect(&r);
	int viewWidth = r.Width()-10;

	UINT uStyle = DFCS_BUTTONPUSH;
	::DrawFrameControl(lpDrawItemStruct->hDC, &lpDrawItemStruct->rcItem, 
		DFC_BUTTON, uStyle);

	CSideProfileBayPage *pParent = (CSideProfileBayPage *)GetParent();
	CSideProfile *pSideProfile = pParent->m_pSideProfile;

	//Need to create a font with escapement of 
	//90 degrees - so that we can write upwards
	CFont  drawingFont;
	BOOL bFontCreated = 
		drawingFont.CreateFont(10, 0,900,900,FW_THIN, 0,0,0, ANSI_CHARSET, OUT_DEFAULT_PRECIS,
				CLIP_DEFAULT_PRECIS, DEFAULT_QUALITY, DEFAULT_PITCH|FF_DONTCARE, "Arial");
	if (! bFontCreated) {
		drawnDC.Detach();
		return;
	}

	// create red pen for selection and black pen for other
	CPen  redPen(PS_SOLID, 1, RGB(255,0,0));
	CPen  blackPen(PS_SOLID, 1, RGB(0,0,0));
	CPen bluePen(PS_SOLID, 1, RGB(0,0,255));
	CPen  *prevPen = drawnDC.SelectObject(&blackPen);

	// Draw the bay ends...
	CFont *prevFont = drawnDC.SelectObject(&drawingFont);
	int x = 5;
	int increment;

	//********************************************************
	// Draw each bay and bay description on the drawing box
	//********************************************************

	for(int i=0; i < pSideProfile->m_BayProfileList.GetSize(); i++)	{
		CBayProfile *pBayProfile = pSideProfile->m_BayProfileList[i];
		double totalBayWidth;

		//if it is the first bay in the side then add two uprights
		//else only add one upright to the bay width
		if (i == 0)
			totalBayWidth = pBayProfile->m_Width + (pBayProfile->m_UprightWidth * 2);
		else {
			if (i < pSideProfile->m_BayProfileList.GetSize()-1) {
				CBayProfile *pNextProfile = pSideProfile->m_BayProfileList[i+1];

				if (pNextProfile->m_UprightWidth > pBayProfile->m_UprightWidth)
					totalBayWidth = pBayProfile->m_Width + pNextProfile->m_UprightWidth;
				else
					totalBayWidth = pBayProfile->m_Width + pBayProfile->m_UprightWidth;
			}
			else
				totalBayWidth = pBayProfile->m_Width + pBayProfile->m_UprightWidth;
		}

		CBayProfile *pTempProfile;

		if (pParent->m_SelectedMap.Lookup(i, pTempProfile)) {	
			drawnDC.SelectObject(&redPen);
			drawnDC.SetTextColor(RGB(255, 0, 0));
		}
		else {
			drawnDC.SelectObject(&blackPen);
			drawnDC.SetTextColor(RGB(0, 0, 0));
		}

		//calculated width of bay in the drawing
		if (! pSideProfile->m_FixedLength)
			increment = (int)((totalBayWidth/pParent->m_CurrentLength)*viewWidth);
		else
			increment = (int)((totalBayWidth/pSideProfile->m_TotalLength) * viewWidth);

		//Draw each crossbar in the bay
		//Do not draw the top shelf which inserted by the bay wizard...
		for (int level = 0; level < pBayProfile->m_LevelProfileList.GetSize(); level++) {
			CLevelProfile *pLevelProfile = pBayProfile->m_LevelProfileList[level];

			// Now map the coordinate to Window coordinates...
			if (pBayProfile->m_UprightHeight == 0)
				continue;

			int y =  (int)(r.bottom/2 +
				(1.0 - pLevelProfile->m_Coordinates.m_Z/pBayProfile->m_UprightHeight)*r.bottom/2);
			drawnDC.MoveTo(x, y);
			drawnDC.LineTo(x+increment, y);
		}

		//draw the two uprights
		drawnDC.MoveTo(x, r.bottom/2+5);
		drawnDC.LineTo(x, r.bottom);
		drawnDC.MoveTo(x+increment, r.bottom/2+5);
		drawnDC.LineTo(x+increment, r.bottom);

		//write text with name of bay profile
		CString str;
		CString bayTypeStr;
		pBayProfile->ConvertBayType(pBayProfile->m_BayType, bayTypeStr);
		str.Format("%s\\%s", bayTypeStr, pBayProfile->m_Description);

		drawnDC.TextOut(x+increment/2, r.bottom-10, str );
	
		//move x to the beginning of the next bay
		x += increment;
	}


	// Draw Dimension lines for aisle side length

	//int xExtent = pSideProfile->m_FixedLength ? (viewWidth) : x;
	int xExtent = viewWidth;
	CString strLength;
	if (pSideProfile->m_FixedLength) {
		if (pSideProfile->m_TotalLength == 0)
			strLength = "";
		else
			strLength.Format("%.2lf", pSideProfile->m_TotalLength);
	}
	else {
		if (pParent->m_CurrentLength == 0)
			strLength = "";
		else
			strLength.Format("%.2lf", pParent->m_CurrentLength);
	}

	if (strLength != "") {
		drawnDC.SetTextColor(RGB(0, 0, 255));

		drawnDC.SelectObject(prevPen);
		drawnDC.SelectObject(prevFont);
		// Draw dimension lines...
		drawnDC.MoveTo(5, r.bottom/8);
		drawnDC.LineTo(xExtent, r.bottom/8);
		drawnDC.MoveTo(5, r.bottom/8);
		drawnDC.LineTo(11, r.bottom/8-5);
		drawnDC.MoveTo(5, r.bottom/8);
		drawnDC.LineTo(11, r.bottom/8+5);
		drawnDC.MoveTo(xExtent, r.bottom/8);
		drawnDC.LineTo(xExtent-6, r.bottom/8-5);
		drawnDC.MoveTo(xExtent, r.bottom/8);
		drawnDC.LineTo(xExtent-6, r.bottom/8+5);

		drawnDC.TextOut(r.right/2-25, r.bottom/8+5, strLength);
	}

	drawnDC.SelectObject(prevFont);
	drawnDC.SelectObject(prevPen);

	drawnDC.Detach();	
}

void CSideProfileButton::OnLButtonDown(UINT nFlags, CPoint point) 
{
	CSideProfileBayPage *pParent = (CSideProfileBayPage *)GetParent();
	CSideProfile *pSideProfile = pParent->m_pSideProfile;

	//if the user clicked with the current bay...
	int bayIdx = GetCurrentBayIndex(point);
	if (bayIdx >= 0) {
		CBayProfile *pTempProfile;
		if (pParent->m_SelectedMap.Lookup(bayIdx, pTempProfile))
			pParent->m_SelectedMap.RemoveKey(bayIdx);
		else				
			pParent->m_SelectedMap.SetAt(bayIdx, pSideProfile->m_BayProfileList[bayIdx]);
		
	}
		
	CButton::OnLButtonDown(nFlags, point);
}


void CSideProfileButton::OnLButtonDblClk(UINT nFlags, CPoint point) 
{
	int bayIdx = GetCurrentBayIndex(point);
	if (bayIdx >= 0)
		GetParent()->PostMessage(WM_BAY_PROPERTIES, bayIdx, 0);

	CButton::OnLButtonDown(nFlags, point);
}

void CSideProfileButton::OnMouseMove(UINT nFlags, CPoint point) 
{
	int bayIdx = GetCurrentBayIndex(point);
	if (bayIdx >= 0) {
		CSideProfileBayPage *pParent = (CSideProfileBayPage *)GetParent();
		pParent->UpdateToolTip(point.x, point.y, bayIdx);
	}
	
	CButton::OnMouseMove(nFlags, point);
}

int CSideProfileButton::GetCurrentBayIndex(CPoint &point)
{
	CRect r;
	GetClientRect(&r);
	int viewWidth = r.Width()-10;

	CSideProfileBayPage *pParent = (CSideProfileBayPage *)GetParent();
	CSideProfile *pSideProfile = pParent->m_pSideProfile;
	
	int increment;
	int xBay = 5;
	
	if (pSideProfile->m_BayProfileList.GetSize() == 0)
		return -1;
	
	//For each bay, find out is it is the one selected.
	//If so add it to list 
	double totalBayWidth;
	double bayWidth;
	double barWidth;
	for(int i=0; i < pSideProfile->m_BayProfileList.GetSize(); i++) {
		
		CBayProfile *pBayProfile = pSideProfile->m_BayProfileList[i];
		
		bayWidth = pBayProfile->m_Width;
		barWidth = pBayProfile->m_UprightWidth;
		
		if (i == 0)
			totalBayWidth = pBayProfile->m_Width + (pBayProfile->m_UprightWidth * 2);
		else {
			if (i < pSideProfile->m_BayProfileList.GetSize()-1) {
				CBayProfile *pNextProfile = pSideProfile->m_BayProfileList[i+1];

				if (pNextProfile->m_UprightWidth > pBayProfile->m_UprightWidth)
					totalBayWidth = pBayProfile->m_Width + pNextProfile->m_UprightWidth;
				else
					totalBayWidth = pBayProfile->m_Width + pBayProfile->m_UprightWidth;
			}
			else
				totalBayWidth = pBayProfile->m_Width + pBayProfile->m_UprightWidth;
		}

		if (! pSideProfile->m_FixedLength)
			increment = (int)((totalBayWidth/pParent->m_CurrentLength) * viewWidth);
		else
			increment = (int)((totalBayWidth/pSideProfile->m_TotalLength) * viewWidth);
		
		//if the user clicked with the current bay...
		if ((point.x >= xBay) && (point.x <= xBay+increment)) {
			return i;
		}
		
		xBay += increment;
	}

	return -1;
}

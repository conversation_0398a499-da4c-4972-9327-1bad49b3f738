#include "stdafx.h"
#include "Commands.h"
#include "Resource.h"

#include "NavigationCommands.h"
#include "AutoCADCommands.h"
#include "FacilityCommands.h"
#include "ColoringCommands.h"
#include "UtilityCommands.h"
#include "ElementMaintenanceCommands.h"
#include "InterfaceCommands.h"
#include "OptimizationCommands.h"
#include "ProductCommands.h"
#include "ProductGroupCommands.h"
#include "UDFCommands.h"
#include "UtilityCommands.h"
#include "ReportCommands.h"
#include "WizardCommands.h"
#include "GenericCommands.h"


//////////////////////////////////////////////////////////////////////
// CCommands Class
//////////////////////////////////////////////////////////////////////

//////////////////////////////////////////////////////////////////////
// Construction/Destruction
//////////////////////////////////////////////////////////////////////

CCommands::CCommands()
{

}

CCommands::~CCommands()
{

}

void CCommands::RegisterCommands()
{
	CNavigationCommands::RegisterCommands();
	CGenericCommands::RegisterCommands();
	CUtilityCommands::RegisterCommands();
	CFacilityCommands::RegisterCommands();
	CElementMaintenanceCommands::RegisterCommands();
	CWizardCommands::RegisterCommands();
	CColoringCommands::RegisterCommands();
	CInterfaceCommands::RegisterCommands();
	COptimizationCommands::RegisterCommands();
	CProductCommands::RegisterCommands();
	CProductGroupCommands::RegisterCommands();
	CReportCommands::RegisterCommands();
	CUDFCommands::RegisterCommands();
}

void CCommands::RegisterCommonCommands()
{
	ads_printf("Registering common commands...\n");

	CCommands::UnRegisterAllCommands();

	CNavigationCommands::RegisterCommands();
	CGenericCommands::RegisterCommands();
	CUtilityCommands::RegisterCommands();

}

void CCommands::RegisterNewFacilityCommands()
{
	ads_printf("Registering new facility commands...\n");
	CCommands::RegisterCommonCommands();

	CFacilityCommands::RegisterCommands();
	CElementMaintenanceCommands::RegisterCommands();
	CWizardCommands::RegisterCommands();

}

void CCommands::RegisterSavedFacilityCommands()
{

	ads_printf("Registering saved facility commands...\n");

	CCommands::RegisterNewFacilityCommands();

	CColoringCommands::RegisterCommands();
	CInterfaceCommands::RegisterCommands();
	COptimizationCommands::RegisterCommands();
	CProductCommands::RegisterCommands();
	CProductGroupCommands::RegisterCommands();
	CReportCommands::RegisterCommands();
	CUDFCommands::RegisterCommands();

}

void CCommands::UnRegisterAllCommands()
{
	ads_printf("Unregistering all commands...\n");

	acedRegCmds->removeGroup("SLOTJAVA");
	acedRegCmds->removeGroup("SLOTGEN");
	acedRegCmds->removeGroup("SLOTFAC");
	acedRegCmds->removeGroup("SLOTREP");
	acedRegCmds->removeGroup("SLOTWIZ");
}

void CCommands::RegisterCommands(int mode)
{
	switch (mode) {
	case IDC_USEWIZARD:
		CCommands::RegisterNewFacilityCommands();
		break;
	case IDC_OPENFACILITY:
		CCommands::RegisterSavedFacilityCommands();
		break;
	default:
		CCommands::RegisterNewFacilityCommands();
		break;
	}
}

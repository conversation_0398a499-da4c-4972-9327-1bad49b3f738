// NavigationCommands.cpp: implementation of the CNavigationCommands class.
//
//////////////////////////////////////////////////////////////////////

#include "stdafx.h"
#include "modal.h"
#include "NavigationCommands.h"
#include "NavigationHelper.h"
#include "ControlService.h"

#ifdef _DEBUG
#undef THIS_FILE
static char THIS_FILE[]=__FILE__;
#define new DEBUG_NEW
#endif


extern CControlService controlService;
//////////////////////////////////////////////////////////////////////
// Construction/Destruction
//////////////////////////////////////////////////////////////////////

CNavigationCommands::CNavigationCommands()
{

}

CNavigationCommands::~CNavigationCommands()
{

}


void CNavigationCommands::RegisterCommands()
{
	// Navigation
	acedRegCmds->addCommand( "SLOTGEN", "SHOWWIZARD", "SHOWWIZARD",
		ACRX_CMD_MODAL, &CNavigationCommands::ShowWizard);

	acedRegCmds->addCommand( "SLOTGEN", "CURSORMENU", "CURSORMENU",
		ACRX_CMD_MODAL, &CNavigationCommands::DisplayCursorMenu );

	acedRegCmds->addCommand( "SLOTGEN", "SUCCEEDHELP", "SUCCEEDHELP",
		ACRX_CMD_MODAL, &CNavigationCommands::SucceedHelp );

	acedRegCmds->addCommand( "SLOTGEN", "NEWCONNECTION", "NEWCONNECTION",
		ACRX_CMD_MODAL, &CNavigationCommands::NewConnection );

	acedRegCmds->addCommand( "SLOTGEN", "NEWCONNECTION2", "NEWCONNECTION2",		// backwards compatibile
		ACRX_CMD_MODAL, &CNavigationCommands::ProcessLogin );

	acedRegCmds->addCommand( "SLOTGEN", "PROCESSLOGIN", "PROCESSLOGIN",
		ACRX_CMD_MODAL, &CNavigationCommands::ProcessLogin );

	acedRegCmds->addCommand( "SLOTFAC", "MODIFYDRAWING", "MODIFYDRAWING",
		ACRX_CMD_MODAL, &CNavigationCommands::ModifyDrawing);

	acedRegCmds->addCommand( "SLOTFAC", "TESTGRID", "TESTGRID",
		ACRX_CMD_MODAL, &CNavigationCommands::TestFunction);

}



void CNavigationCommands::ShowWizard()
{
	if (! controlService.ValidateCommand(IDC_USEWIZARD))
		return;

	CNavigationHelper navHelper;

	navHelper.ShowWizard();

	return;

}

void CNavigationCommands::DisplayCursorMenu()
{
	if (! controlService.ValidateCommand(IDC_USEWIZARD))
		return;

	CNavigationHelper navHelper;

	navHelper.DisplayCursorMenu();

	return;

}


void CNavigationCommands::SucceedHelp() 
{
	if (! controlService.ValidateCommand(IDC_USEWIZARD))
		return;

	CNavigationHelper navHelper;

	navHelper.SucceedHelp();

	return;

}


void CNavigationCommands::NewConnection()
{
	if (! controlService.ValidateCommand(IDC_USEWIZARD))
		return;

	CNavigationHelper navHelper;

	navHelper.NewConnection();

	return;

}

void CNavigationCommands::ProcessLogin()
{
	if (! controlService.ValidateCommand(IDC_USEWIZARD))
		return;
		
	CNavigationHelper navHelper;
	
	navHelper.ProcessLogin();

	return;

}

void CNavigationCommands::ModifyDrawing()
{
	if (! controlService.ValidateCommand(IDC_USEWIZARD))
		return;

	CNavigationHelper navHelper;

	navHelper.ModifyDrawing();

	return;

}


void CNavigationCommands::TestFunction()
{
	
	/*
	CTestGrid testGrid;
	CGenericPropertySheet sheet;
	sheet.AddPage(&testGrid);


	try {
		sheet.DoModal();
	}
	catch (...) {
		AfxMessageBox("Error displaying test grid.");
	}
	*/

}


#if !defined(AFX_BAYPROFILEEXTERNALATTRIBUTEPAGE_H__468A24BB_CEAC_46EF_9C06_59248D7A63BA__INCLUDED_)
#define AFX_BAYPROFILEEXTERNALATTRIBUTEPAGE_H__468A24BB_CEAC_46EF_9C06_59248D7A63BA__INCLUDED_

#if _MSC_VER > 1000
#pragma once
#endif // _MSC_VER > 1000
// BayProfileExternalAttributePage.h : header file
//
#include "BayProfileLevelButton.h"
#include "BayProfile.h"
#include "ExternalSystem.h"
/////////////////////////////////////////////////////////////////////////////
// CBayProfileExternalAttributePage dialog

class CBayProfileExternalAttributePage : public CPropertyPage
{
	DECLARE_DYNCREATE(CBayProfileExternalAttributePage)

// Construction
public:
	CBayProfileExternalAttributePage();
	~CBayProfileExternalAttributePage();
	CTypedPtrArray<CObArray, CStatic*> m_Labels;
	CTypedPtrArray<CObArray, CWnd*> m_InputControls;
// Dialog Data
	//{{AFX_DATA(CBayProfileExternalAttributePage)
	enum { IDD = IDD_BAY_PROFILE_EXTERNAL_ATTRIBUTES };
	CScrollBar	m_ScrollBar;
	CButton	m_GroupButton;
	CComboBox	m_LevelListCtrl;
	CComboBox	m_ExternalListCtrl;
	CBayProfileLevelButton	m_LevelButton;
	//}}AFX_DATA


// Overrides
	// ClassWizard generate virtual function overrides
	//{{AFX_VIRTUAL(CBayProfileExternalAttributePage)
	public:
	virtual BOOL OnSetActive();
	virtual BOOL OnKillActive();
	protected:
	virtual void DoDataExchange(CDataExchange* pDX);    // DDX/DDV support
	virtual BOOL OnNotify(WPARAM wParam, LPARAM lParam, LRESULT* pResult);
	//}}AFX_VIRTUAL

// Implementation
protected:
	// Generated message map functions
	//{{AFX_MSG(CBayProfileExternalAttributePage)
	virtual BOOL OnInitDialog();
	afx_msg void OnSelchangeLevelList();
	afx_msg void OnSelchangeExternalSystemList();
	afx_msg void OnVScroll(UINT nSBCode, UINT nPos, CScrollBar* pScrollBar);
	afx_msg BOOL OnHelpInfo(HELPINFO* pHelpInfo);
	//}}AFX_MSG
	DECLARE_MESSAGE_MAP()
private:
	BOOL Validate();
	void LoadLevelAttributes(int currentLevel);
	void SetScrollSizes();
	int m_ScrollPos;
	int CreateAttributeDisplay(int currentLevel);
	int m_CurrentSystemId;
	int UpdateLevelProfileFromScreen(int currentLevel);
	int UpdateScreenFromLevelProfile(int currentLevel);
	CBayProfile *m_pBayProfile;
	void RebuildLevelList();
	int OnSelectLevel(WPARAM wParam, LPARAM lParam);

	CTypedPtrArray<CObArray, CExternalSystem*> m_ExternalSystemList;

};

//{{AFX_INSERT_LOCATION}}
// Microsoft Visual C++ will insert additional declarations immediately before the previous line.

#endif // !defined(AFX_BAYPROFILEEXTERNALATTRIBUTEPAGE_H__468A24BB_CEAC_46EF_9C06_59248D7A63BA__INCLUDED_)

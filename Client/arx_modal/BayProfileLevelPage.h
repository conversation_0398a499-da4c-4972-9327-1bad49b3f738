#if !defined(AFX_BAYPROFILELEVELPAGE_H__F3B7E95B_1F8B_46D1_B803_E3BF344FDF0F__INCLUDED_)
#define AFX_BAYPROFILELEVELPAGE_H__F3B7E95B_1F8B_46D1_B803_E3BF344FDF0F__INCLUDED_

#if _MSC_VER > 1000
#pragma once
#endif // _MSC_VER > 1000
// BayProfileLevelPage.h : header file
//
#include "BayProfileLevelButton.h"
#include "BayProfile.h"

/////////////////////////////////////////////////////////////////////////////
// CBayProfileLevelPage dialog

class CBayProfileLevelPage : public CPropertyPage
{
	DECLARE_DYNCREATE(CBayProfileLevelPage)

// Construction
public:
	CBayProfileLevelPage();
	~CBayProfileLevelPage();

// Dialog Data
	//{{AFX_DATA(CBayProfileLevelPage)
	enum { IDD = IDD_BAY_PROFILE_LEVEL_ATTRIBUTES };
	CComboBox	m_LevelTypeListCtrl;
	CComboBox	m_LevelListCtrl;
	CBayProfileLevelButton	m_LevelButton;
	CString	m_BackfillCode;
	BOOL	m_Rotation;
	BOOL	m_VariableWidth;
	CString	m_MaximumCaseCount;
	CString	m_MaximumCaseWeight;
	CString	m_FlowDifference;
	//}}AFX_DATA


// Overrides
	// ClassWizard generate virtual function overrides
	//{{AFX_VIRTUAL(CBayProfileLevelPage)
	public:
	virtual BOOL OnSetActive();
	virtual BOOL OnKillActive();
	protected:
	virtual void DoDataExchange(CDataExchange* pDX);    // DDX/DDV support
	virtual BOOL OnNotify(WPARAM wParam, LPARAM lParam, LRESULT* pResult);
	//}}AFX_VIRTUAL

// Implementation
protected:
	// Generated message map functions
	//{{AFX_MSG(CBayProfileLevelPage)
	virtual BOOL OnInitDialog();
	afx_msg void OnSelchangeLevelList();
	afx_msg void OnSelchangeLevelTypeList();
	afx_msg BOOL OnHelpInfo(HELPINFO* pHelpInfo);
	//}}AFX_MSG
	DECLARE_MESSAGE_MAP()
private:
	BOOL Validate();
	int UpdateLevelProfileFromScreen(int currentLevel);
	void BuildLevelTypeList();
	int UpdateScreenFromLevelProfile(int currentLevel);
	CBayProfile *m_pBayProfile;
	void RebuildLevelList();
	int OnSelectLevel(WPARAM wParam, LPARAM lParam);
};

//{{AFX_INSERT_LOCATION}}
// Microsoft Visual C++ will insert additional declarations immediately before the previous line.

#endif // !defined(AFX_BAYPROFILELEVELPAGE_H__F3B7E95B_1F8B_46D1_B803_E3BF344FDF0F__INCLUDED_)

// ProductGroupQuery.h: interface for the CProductGroupQuery class.
//
//////////////////////////////////////////////////////////////////////

#if !defined(AFX_PRODUCTGROUPQUERY_H__618F8055_5228_4523_936B_0F37531970EA__INCLUDED_)
#define AFX_PRODUCTGROUPQUERY_H__618F8055_5228_4523_936B_0F37531970EA__INCLUDED_

#if _MSC_VER > 1000
#pragma once
#endif // _MSC_VER > 1000

class CProductGroupQuery : public CObject  
{
public:
	BOOL IsEqual(CProductGroupQuery &other);
	int Parse(CString &line);
	long m_ProductGroupQueryDBID;
	long m_ProductGroupDBID;
	long m_CriteriaRangeDBID;
	CString m_RangeName;
	long m_CriteriaDBID;
	CString m_Operator;
	CString m_Value;
	CProductGroupQuery();
	virtual ~CProductGroupQuery();
	CProductGroupQuery& operator=(CProductGroupQuery &other);

};

#endif // !defined(AFX_PRODUCTGROUPQUERY_H__618F8055_5228_4523_936B_0F37531970EA__INCLUDED_)

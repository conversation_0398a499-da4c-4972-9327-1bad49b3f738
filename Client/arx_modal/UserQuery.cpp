// UserQuery.cpp: implementation of the CUserQuery class.
//
//////////////////////////////////////////////////////////////////////

#include "stdafx.h"
#include "modal.h"
#include "UserQuery.h"
#include <dbsymtb.h>

#ifdef _DEBUG
#undef THIS_FILE
static char THIS_FILE[]=__FILE__;
#define new DEBUG_NEW
#endif

//////////////////////////////////////////////////////////////////////
// Construction/Destruction
//////////////////////////////////////////////////////////////////////
IMPLEMENT_SERIAL( CUserQuery, CObject, 1 )

CUserQuery::CUserQuery()
{

	m_Name = "";
	m_Description = "";
	m_Query = "";
	m_Database = "";

}

CUserQuery::~CUserQuery()
{

}

CUserQuery& CUserQuery::operator=(const CUserQuery & other)
{


	m_QueryID = other.m_QueryID;
	m_Name = other.m_Name;
	m_Description = other.m_Description;
	m_Query = other.m_Query;
	m_Database = other.m_Database;

	return *this;
}


void CUserQuery::Serialize(CArchive &archive)
{
	CObject::Serialize(archive);
	if (archive.IsStoring()) {
		archive << m_Name << m_Description << m_Query << m_Database;
	}
	else {
		archive >> m_Name >> m_Description >> m_Query >> m_Database;
	}

}

void CUserQuery::Parse(CString line)
{

	char *str;
	char *ptr;
	CString temp;
	temp = line;
	
	try {
		
		line.Replace("||", "| |");
		line.Replace("||", "| |");
		str = line.GetBuffer(0);
		
		ptr = strtok(str, "|");
		m_QueryID = atoi(ptr);
		ptr = strtok(NULL, "|");
		m_Name = ptr;
		ptr = strtok(NULL, "|");
		m_Description = ptr;
		ptr = strtok(NULL, "|");
		m_Query = ptr;
		ptr = strtok(NULL, "|");
		m_Database = ptr;
		
		line.ReleaseBuffer();
	}
	catch (...) {
		AfxMessageBox("Error processing user query list.");
		ads_printf("%s\n", line);
	}
	
}

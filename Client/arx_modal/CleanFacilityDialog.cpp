// CleanFacilityDialog.cpp : implementation file
//

#include "stdafx.h"
#include "modal.h"
#include "CleanFacilityDialog.h"
#include "TreeElement.h"
#include "HelpService.h"
#include "Constants.h"
#include "ssa_exception.h"
#include "AutoCADCommands.h"
#include "DataAccessService.h"
#include "UtilityHelper.h"
#include "ProcessingMessage.h"

#include <adscodes.h>
#include <aced.h>
#include <actrans.h>

#ifdef _DEBUG
#define new DEBUG_NEW
#undef THIS_FILE
static char THIS_FILE[] = __FILE__;
#endif

/////////////////////////////////////////////////////////////////////////////
// CCleanFacilityDialog dialog

extern TreeElement changesTree;
extern CHelpService helpService;
extern CUtilityHelper utilityHelper;
extern CDataAccessService dataAccessService;

CCleanFacilityDialog::CCleanFacilityDialog(CWnd* pParent /*=NULL*/)
	: CDialog(CCleanFacilityDialog::IDD, pParent)
{
	//{{AFX_DATA_INIT(CCleanFacilityDialog)
		// NOTE: the ClassWizard will add member initialization here
	//}}AFX_DATA_INIT
}


void CCleanFacilityDialog::DoDataExchange(CDataExchange* pDX)
{
	CDialog::DoDataExchange(pDX);
	//{{AFX_DATA_MAP(CCleanFacilityDialog)
	DDX_Control(pDX, IDC_DB_LIST, m_DBListCtrl);
	DDX_Control(pDX, IDC_DRAWING_LIST, m_DrawingListCtrl);
	//}}AFX_DATA_MAP
}


BEGIN_MESSAGE_MAP(CCleanFacilityDialog, CDialog)
	//{{AFX_MSG_MAP(CCleanFacilityDialog)
	ON_BN_CLICKED(IDC_DELETE_DRAWING_BAY, OnDeleteDrawingBay)
	ON_BN_CLICKED(IDC_DELETE_DB_BAY, OnDeleteDbBay)
	ON_BN_CLICKED(IDHELP, OnHelp)
	ON_WM_HELPINFO()
	ON_BN_CLICKED(IDC_LOAD, OnLoad)
	ON_BN_CLICKED(IDC_COLOR_DRAWING_BAY, OnColorDrawingBay)
    ON_MESSAGE(WM_ACAD_KEEPFOCUS, OnAcadKeepFocus)
	//}}AFX_MSG_MAP
END_MESSAGE_MAP()

/////////////////////////////////////////////////////////////////////////////
// CCleanFacilityDialog message handlers

BOOL CCleanFacilityDialog::OnInitDialog() 
{
	CDialog::OnInitDialog();
	CRect r;
	int n;

	if (changesTree.elementDBID <= 0) {
		AfxMessageBox("Please save the facility before running this command.");
		EndDialog(IDCANCEL);
	}

	n = m_DrawingListCtrl.GetHeaderCtrl()->GetItemCount();
	for (int i=0; i < n; ++i)
		m_DrawingListCtrl.DeleteColumn(0);

	m_DrawingListCtrl.GetClientRect(&r);

	m_DrawingListCtrl.InsertColumn(0, "Handle", LVCFMT_LEFT, r.Width()/2, 0);
	m_DrawingListCtrl.InsertColumn(1, "Position (x,y)", LVCFMT_LEFT, r.Width()/2, 0);

	n = m_DBListCtrl.GetHeaderCtrl()->GetItemCount();
	for (i=0; i < n; ++i)
		m_DBListCtrl.DeleteColumn(0);
	
	m_DBListCtrl.InsertColumn(0, "Handle", LVCFMT_LEFT, r.Width()/4, 0);
	m_DBListCtrl.InsertColumn(1, "Bay", LVCFMT_LEFT, r.Width()/4, 0);
	m_DBListCtrl.InsertColumn(2, "Section", LVCFMT_LEFT, r.Width()/4, 0);
	m_DBListCtrl.InsertColumn(3, "Aisle", LVCFMT_LEFT, r.Width()/4, 0);
	m_DBListCtrl.InsertColumn(4, "Aisle Missing", LVCFMT_LEFT, r.Width()/4, 0);

	
	
	return TRUE;  // return TRUE unless you set the focus to a control
	              // EXCEPTION: OCX Property Pages should return FALSE
}

void CCleanFacilityDialog::OnDeleteDrawingBay() 
{
	CString handle;


	for (int i=0; i < m_DrawingListCtrl.GetItemCount(); ++i) {
		handle = m_DrawingListCtrl.GetItemText(i, 0);
		CAutoCADCommands::DeleteDrawingObjectByHandle(handle);
	}


	actrTransactionManager->flushGraphics();
	acedUpdateDisplay();

	OnLoad();

}

void CCleanFacilityDialog::OnDeleteDbBay() 
{
	CString handles, temp, aisleIds, sideIds, pickpathIds;
	CStringArray statements, results;
	CString sql;
	
	for (int i=0; i < m_DBListCtrl.GetItemCount(); ++i) {
		temp.Format("'%s',", m_DBListCtrl.GetItemText(i, 0));
		handles += temp;
		if (i > 0 && i % 253 == 0) {
			handles.TrimRight(",");
			AddStatements(statements, handles);
			
			handles = "";
		}
	}
	
	if (handles != "") {
		handles.TrimRight(",");
		AddStatements(statements, handles);
	}
	
	
	CProcessingMessage processDlg("Deleting Missing Bays", this);
	try {
		
		if (statements.GetSize() > 0)
			dataAccessService.ExecuteStatements("DeleteBayItemsByBayHandle", statements);
		

		statements.RemoveAll();
		
		
		CButton *pButton = (CButton *)GetDlgItem(IDC_DELETE_EMPTY_AISLE);
		if (! pButton->GetCheck()) {
			processDlg.UpdateMessage("Re-Checking for Errors");
			OnLoad();
			return;
		}
		
		processDlg.UpdateMessage("Deleting Orphaned Aisles");

		sql.Format("select dbsideid "
			"from dbside, dbaisle, dbsection "
			"where dbsection.dbfacilityid = %d "
			"and dbsection.dbsectionid = dbaisle.dbsectionid "
			"and dbaisle.dbaisleid = dbside.dbaisleid "
			"and not exists ( select dbsideid from dbbay b "
			"where b.dbsideid = dbside.dbsideid)", changesTree.elementDBID);
		
		results.RemoveAll();
		dataAccessService.ExecuteQuery("GetSidesWithNoBays", sql, results);
		
		if (results.GetSize() > 0) {
			for (i=0; i < results.GetSize(); ++i) {
				results[i].TrimRight("|");
				sideIds += results[i];
				sideIds += ",";
			}
			sideIds.TrimRight(",");
			
			sql.Format("delete from dbsideudfval "
				"where dbsideid in (%s)", sideIds);
			statements.Add(sql);
			
			sql.Format("delete from dbside "
				"where dbsideid in (%s)", sideIds);
			statements.Add(sql);
			
			if (statements.GetSize() > 0)
				dataAccessService.ExecuteStatements("DeleteSidesWithNoBays", statements);
			statements.RemoveAll();
		}
		
		
		sql.Format("select dbaisleid "
			"from dbaisle, dbsection "
			"where dbsection.dbfacilityid = %d "
			"and dbsection.dbsectionid = dbaisle.dbsectionid "
			"and not exists ( select dbaisleid "
			"from dbside "
			"where dbside.dbaisleid = dbaisle.dbaisleid) ", changesTree.elementDBID);
		
		results.RemoveAll();
		dataAccessService.ExecuteQuery("GetAislesWithNoSides", sql, results);
		
		if (results.GetSize() > 0) {
			for (i=0; i < results.GetSize(); ++i) {
				results[i].TrimRight("|");
				aisleIds += results[i];
				aisleIds += ",";
			}
			aisleIds.TrimRight(",");
			
			
			sql.Format("delete from dbaislepath "
				"where dbaisleid in (%s) ", aisleIds);
			statements.Add(sql);
			
			
			sql.Format("select dbpickpathid "
				"from dbaislepath "
				"where dbaisleid in (%s)", aisleIds);
			
			results.RemoveAll();
			
			dataAccessService.ExecuteQuery("GetPickPath", sql, results);
			
			if (results.GetSize() > 0) {
				for (i=0; i < results.GetSize(); ++i) {
					results[i].TrimRight("|");
					pickpathIds += results[i];
					pickpathIds += ",";
				}
				pickpathIds.TrimRight(",");
				
				
				sql.Format("delete from dbpickpath "
					"where dbpickpathid in (%s)", pickpathIds);
				statements.Add(sql);
			}
			
			sql.Format("delete from dbaisleudfval "
				"where dbaisleid in (%s)", aisleIds);
			statements.Add(sql);
			
			sql.Format("delete from dbaisle "
				"where dbaisleid in (%s)", aisleIds);
			statements.Add(sql);
			
			if (statements.GetSize() > 0)
				dataAccessService.ExecuteStatements("DeleteAislesWithNoSides", statements);
		}
		
	}
	catch (Ssa_Exception e) {
		utilityHelper.ProcessError("Error deleting bays from database.", &e);
	}
	catch (...) {
		utilityHelper.ProcessError("Error deleting bays from database.");
	}
	
	processDlg.UpdateMessage("Re-Checking for Errors");
	OnLoad();
	
	return;
}

void CCleanFacilityDialog::AddStatements(CStringArray &statements, CString &handles)
{

	CString sql;

	sql.Format("delete from dbmove "
		"where fromlocationid in ( select dblocationid "
		"from dblocation l, dblevel le, dbbayf b "
		"where b.acadhandle in (%s) "
		"and b.dbfacilityid = %d "
		"and b.dbbayid = le.dbbayid "
		"and le.dblevelid = l.dblevelid "
		"and l.dblocationid = dbmove.fromlocationid) ", handles, changesTree.elementDBID);
	statements.Add(sql);
	
	sql.Format("delete from dbmove "
		"where tolocationid in ( select dblocationid "
		"from dblocation l, dblevel le, dbbayf b "
		"where b.acadhandle in (%s) "
		"and b.dbfacilityid = %d "
		"and b.dbbayid = le.dbbayid "
		"and le.dblevelid = l.dblevelid "
		"and l.dblocationid = dbmove.tolocationid) ", handles, changesTree.elementDBID);
	statements.Add(sql);
	
	sql.Format("delete from dbslotsolution "
		"where dblocationid in ( select dblocationid "
		"from dblocation l, dblevel le, dbbayf b "
		"where b.acadhandle in (%s) " 
		"and b.dbfacilityid = %d "
		"and b.dbbayid = le.dbbayid "
		"and le.dblevelid = l.dblevelid "
		"and l.dblocationid = dbslotsolution.dblocationid) ", handles, changesTree.elementDBID);
	statements.Add(sql);
	
	sql.Format("delete from dblocationinfo "
		"where dblocationid in ( select dblocationid "
		"from dblocation l, dblevel le, dbbayf b "
		"where b.acadhandle in (%s) "
		"and b.dbfacilityid = %d "
		"and b.dbbayid = le.dbbayid "
		"and le.dblevelid = l.dblevelid "
		"and l.dblocationid = dblocationinfo.dblocationid) ", handles, changesTree.elementDBID);
	statements.Add(sql);
	
	sql.Format("delete from dbslotsolution "
		"where dblocationid in ( select dblocationid "
		"from dblocation l, dblevel le, dbbayf b "
		"where b.acadhandle in (%s) "
		"and b.dbfacilityid = %d "
		"and b.dbbayid = le.dbbayid "
		"and le.dblevelid = l.dblevelid "
		"and l.dblocationid = dbslotsolution.dblocationid) ", handles, changesTree.elementDBID);
	statements.Add(sql);
	
	sql.Format("delete from dblocation "
		"where dblocationid in ( select dblocationid "
		"from dblocation l, dblevel le, dbbayf b "
		"where b.acadhandle in (%s) "
		"and b.dbfacilityid = %d "
		"and b.dbbayid = le.dbbayid "
		"and le.dblevelid = l.dblevelid "
		"and l.dblocationid = dblocation.dblocationid) ", handles, changesTree.elementDBID);
	statements.Add(sql);
	
	
	sql.Format("delete from dbslotgrpbay "
		"where dblevelid in ( select dblevelid "
		"from dblevel le, dbbayf b "
		"where b.acadhandle in (%s) "
		"and b.dbfacilityid = %d "
		"and le.dbbayid = b.dbbayid "
		"and le.dblevelid = dbslotgrpbay.dblevelid) ", handles, changesTree.elementDBID);
	statements.Add(sql);
	
	
	sql.Format("delete from dblevel "
		"where dblevelid in ( select dblevelid "
		"from dblevel le, dbbayf b "
		"where b.acadhandle in (%s) "
		"and b.dbfacilityid = %d "
		"and le.dbbayid = b.dbbayid "
		"and le.dblevelid = dblevel.dblevelid) ", handles, changesTree.elementDBID);
	statements.Add(sql);
	
	sql.Format("delete from dbgroupstolevels where bayid in (select dbbayid "
		"from dbbay, dbside, dbaisle, dbsection "
		"where dbbay.acadhandle in (%s) "
		"and dbbay.dbsideid = dbside.dbsideid "
		"and dbside.dbaisleid = dbaisle.dbaisleid "
		"and dbaisle.dbsectionid = dbsection.dbsectionid "
		"and dbsection.dbfacilityid = %d)", handles, changesTree.elementDBID);
	statements.Add(sql);
	
	sql.Format("delete from dbbayudfval "
		"where dbbayid in ( select dbbayid "
		"from dbbayf b "
		"where b.acadhandle in (%s) "
		"and b.dbfacilityid = %d "
		"and b.dbbayid = dbbayudfval.dbbayid) ", handles, changesTree.elementDBID);
	statements.Add(sql);
	
	sql.Format("delete from dbbayf b "
		"where acadhandle in (%s) "
		"and b.dbfacilityid = %d", handles, changesTree.elementDBID);
	statements.Add(sql);
	
	return;		
		
}

void CCleanFacilityDialog::OnHelp() 
{
	helpService.ShowScreenHelp(IDD);
}

BOOL CCleanFacilityDialog::OnHelpInfo(HELPINFO* pHelpInfo) 
{
	helpService.ShowFieldHelp(IDD, pHelpInfo->iCtrlId);
	
	return FALSE;

}

void CCleanFacilityDialog::OnLoad() 
{

	CStringArray drawingList, dbList, strings, dbHandleList;

	typedef struct {
		CString handle;
		CString bay;
		CString section;
		CString aisle;
		CString position;
		int aisleEmpty;
	} bayStruct;

	bayStruct *pBay, *pBay2;

	LVITEM lvItem;
	int nItem;
	CString temp, handle;
	CMapStringToPtr dbMap, drawingMap;
	CPtrList dbMissingList, drawingMissingList;
	POSITION pos;
	CWaitCursor cwc;

	m_DrawingListCtrl.DeleteAllItems();
	m_DBListCtrl.DeleteAllItems();
	
	temp.Format("select acadhandle, b.description, se.description, a.description, "
		"b.xcoordinate, b.ycoordinate, b.dbbayid "
		"from dbsection se, dbbay b, dbaisle a, dbside si "
		"where se.dbfacilityid = %d "
		"and se.dbsectionid = a.dbsectionid "
		"and a.dbaisleid = si.dbaisleid "
		"and si.dbsideid = b.dbsideid ", changesTree.elementDBID);

	try {
		dataAccessService.ExecuteQuery("GetAllBayHandles", temp, dbList);
	}
	catch (...) {
		utilityHelper.ProcessError("Unable to get list of bays from database.");
	}

	
	int dummy;
	for (int i=0; i < dbList.GetSize(); ++i) {
		utilityHelper.ParseString(dbList[i], "|", strings);
		// Ignore bays that have been deleted since the last save
		if (changesTree.DeletedBayMap.Lookup(atoi(strings[6]), dummy))
			continue;
		pBay = new bayStruct;
		pBay->handle = strings[0];
		pBay->bay = strings[1];
		pBay->section = strings[2];
		pBay->aisle = strings[3];
		pBay->position.Format("%s,%s", strings[4], strings[5]);

		dbMap.SetAt(pBay->handle, pBay);
		dbHandleList.Add(pBay->handle);

	}

	try {
		CAutoCADCommands::GetAllDrawingBays(drawingList);
	}
	catch (...) {
		utilityHelper.ProcessError("Unable to get list of bays from drawing.");
	}

	for (i=0; i < drawingList.GetSize(); ++i) {
		utilityHelper.ParseString(drawingList[i], "|", strings);
		pBay = new bayStruct;
		pBay->handle = strings[0];
		pBay->position.Format("%s,%s", strings[1], strings[2]);

		drawingMap.SetAt(pBay->handle, pBay);

		if (! dbMap.Lookup(pBay->handle, (void *&)pBay2))
			drawingMissingList.AddTail(pBay);

	}


	for (pos = dbMap.GetStartPosition(); pos != NULL; ) {
		dbMap.GetNextAssoc(pos, handle, (void *&)pBay);

		if (! drawingMap.Lookup(handle, (void *&)pBay2))
			dbMissingList.AddTail(pBay);
	}

	for (pos = drawingMissingList.GetHeadPosition(); pos != NULL; ) {
		pBay = (bayStruct *)drawingMissingList.GetNext(pos);

		lvItem.mask = LVIF_TEXT;
		lvItem.iItem = i;
		lvItem.iSubItem = 0;
		lvItem.pszText = pBay->handle.GetBuffer(0);
		pBay->handle.ReleaseBuffer();
		nItem = m_DrawingListCtrl.InsertItem(&lvItem);

		lvItem.iItem = nItem;
		lvItem.iSubItem = 1;
		temp.Format("%s, %s", strings[1], strings[2]);
		lvItem.pszText = pBay->position.GetBuffer(0);
		pBay->position.ReleaseBuffer();
		m_DrawingListCtrl.SetItem(&lvItem);
	}

	for (pos = dbMissingList.GetHeadPosition(); pos != NULL;) {
		pBay = (bayStruct *)dbMissingList.GetNext(pos);

		lvItem.mask = LVIF_TEXT;
		lvItem.iItem = i;
		lvItem.iSubItem = 0;
		lvItem.pszText = pBay->handle.GetBuffer(0);
		pBay->handle.ReleaseBuffer();
		nItem = m_DBListCtrl.InsertItem(&lvItem);

		lvItem.iItem = nItem;
		lvItem.iSubItem = 1;
		lvItem.pszText = pBay->bay.GetBuffer(0);
		pBay->bay.ReleaseBuffer();
		m_DBListCtrl.SetItem(&lvItem);

		lvItem.iItem = nItem;
		lvItem.iSubItem = 2;
		lvItem.pszText = pBay->section.GetBuffer(0);
		pBay->section.ReleaseBuffer();
		m_DBListCtrl.SetItem(&lvItem);

		lvItem.iItem = nItem;
		lvItem.iSubItem = 3;
		lvItem.pszText = pBay->aisle.GetBuffer(0);
		pBay->aisle.ReleaseBuffer();
		m_DBListCtrl.SetItem(&lvItem);
	
	}

	for (pos = dbMap.GetStartPosition(); pos != NULL;) {
		dbMap.GetNextAssoc(pos, handle, (void *&)pBay);
		delete pBay;
	}

	for (pos = drawingMap.GetStartPosition(); pos != NULL;) {
		drawingMap.GetNextAssoc(pos, handle, (void *&)pBay);
		delete pBay;
	}

	if (drawingMissingList.GetCount() > 0 || dbMissingList.GetCount() > 0)
		AfxMessageBox("Errors were found.  Delete the items that do not exist "
		"in both the drawing and the database.");
	else
		AfxMessageBox("No errors were found.");
}	

void CCleanFacilityDialog::OnColorDrawingBay() 
{

	CString handle;

	CAutoCADCommands::ColorAllObjects();

	for (int i=0; i < m_DrawingListCtrl.GetItemCount(); ++i) {
		handle = m_DrawingListCtrl.GetItemText(i, 0);
		CAutoCADCommands::ColorDrawingObjectByHandle(handle, kRed);
	}


	actrTransactionManager->flushGraphics();
	acedUpdateDisplay();


}

void CCleanFacilityDialog::OnCancel() 
{
	this->DestroyWindow();
}

void CCleanFacilityDialog::PostNcDestroy() 
{
	
	delete this;
}

afx_msg LONG CCleanFacilityDialog::OnAcadKeepFocus(UINT, LONG)
{

	return TRUE;
}

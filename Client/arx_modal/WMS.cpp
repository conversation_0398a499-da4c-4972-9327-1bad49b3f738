// WMS.cpp: implementation of the CWMS class.
//
//////////////////////////////////////////////////////////////////////

#include "stdafx.h"
#include "modal.h"
#include "WMS.h"
#include "UtilityHelper.h"
	
#ifdef _DEBUG
#undef THIS_FILE
static char THIS_FILE[]=__FILE__;
#define new DEBUG_NEW
#endif

//////////////////////////////////////////////////////////////////////
// Construction/Destruction
//////////////////////////////////////////////////////////////////////

CWMS::CWMS()
{
	m_WMSDBId = 0;
	m_GroupDBId = 0;
}

CWMS::CWMS(const CWMS& other)
{
	m_WMSDBId = other.m_WMSDBId;
	m_Name = other.m_Name;
	m_Description = other.m_Description;
	m_WMSId = other.m_WMSId;
	m_GroupDBId = other.m_GroupDBId;
	m_GroupName = other.m_GroupName;

	// Just in case we are reusing one
	for (int i=0; i < m_MapList.GetSize(); ++i)
		delete m_MapList[i];
	m_MapList.RemoveAll();

	for (i=0; i < other.m_MapList.GetSize(); ++i) {
		CWMSMap *pMap = new CWMSMap(*other.m_MapList[i]);
		m_MapList.Add(pMap);
	}

}

CWMS& CWMS::operator=(const CWMS& other)
{
	m_WMSDBId = other.m_WMSDBId;
	m_Name = other.m_Name;
	m_Description = other.m_Description;
	m_WMSId = other.m_WMSId;
	m_GroupDBId = other.m_GroupDBId;
	m_GroupName = other.m_GroupName;

	// Just in case we are reusing one
	for (int i=0; i < m_MapList.GetSize(); ++i)
		delete m_MapList[i];
	m_MapList.RemoveAll();

	for (i=0; i < other.m_MapList.GetSize(); ++i) {
		CWMSMap *pMap = new CWMSMap(*other.m_MapList[i]);
		m_MapList.Add(pMap);
	}

	return *this;
}


CWMS::~CWMS()
{
	for (int i=0; i < m_MapList.GetSize(); ++i)
		delete m_MapList[i];
}

int CWMS::Parse(CString &line)
{
	CStringArray strings;
	CUtilityHelper utilityHelper;

	utilityHelper.ParseString(line, "|", strings);
	for (int i=0; i < strings.GetSize(); ++i) {
		switch (i) {
		case 0:
			m_WMSDBId = atol(strings[i]);
			break;
		case 1:
			m_Name = strings[i];
			break;
		case 2:
			m_Description = strings[i];
			break;
		case 3:
			m_WMSId = strings[i];
			break;
		case 4:
			m_GroupDBId = atoi(strings[i]);
			break;
		case 5:
			;	// external system name
			break;
		case 6:
			m_GroupName = strings[i];
			break;
		}
	}

	return 0;

}

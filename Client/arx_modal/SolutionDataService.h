// SolutionDataService.h: interface for the CSolutionDataService class.
//
//////////////////////////////////////////////////////////////////////

#if !defined(AFX_SOLUTIONDATASERVICE_H__65257A56_82F6_4916_8C0E_2CDD0E60E9AD__INCLUDED_)
#define AFX_SOLUTIONDATASERVICE_H__65257A56_82F6_4916_8C0E_2CDD0E60E9AD__INCLUDED_

#if _MSC_VER > 1000
#pragma once
#endif // _MSC_VER > 1000

#include "Solution.h"
#include "ProductPack.h"
#include "Location.h"
#include "SSACStringArray.h"
#include "ProductAttribute.h"

class CSolutionDataService  
{
public:
	CSolutionDataService();
	virtual ~CSolutionDataService();
	int GetMultipleProductsInLocations(int facilityDBID, CStringArray &results);
	int GetProductForLocation(int locationDBID, CString &productWMSID, CString &productWMSDetailID, 
		CString &productDescription, long &prodDBID, int origin);
	int AddToPrimary(int productDBID, int caseCount);
	int GetSolutionsByBay(int bayDBID, CStringArray &baySolutions);
	int GetPrimaryFacing(int productDBID, CString &primaryLocString);
	int GetFacingCountByProduct(int productDBID);
	int StoreSolution(CSolution &solution);
	int DeleteSolutionsByProduct(int productDBID);
	int DeleteSolutionsByLocation(int locationDBID);

	int GetSolutionLocationsByAttribute(int facilityId, const CProductAttribute *pAttr, int multiType, 
		int level, int origin, double minHeight, double maxHeight, CStringArray& results);
	
	int GetSolutionLocationsByRange(int facilityId, const CProductAttribute *pAttr, 
		int level, int origin, double minHeight, double maxHeight, const CString &startValue, const CString &endValue, 
		CStringArray& results);


	int GetAssignments(CStringArray &assignmentList, long productGroupID, BOOL fullExport, long sectionID);
	int GetProductForLocation(int locationDBID, CString &productWMSID, 
						  CString &productWMSDetailID, CString &productDescription, int origin);
	int UpdateSolutionCaseCount(int facilityId, int origin, int override, CStringArray &msgList, CStringArray &notFitList);
	int UpdateSolutionCaseCount(const CStringArray &batchList, CStringArray &msgList);

	int GetCaseCount(CProductPack &product, const CLocation &location, int bayType, 
		int rotateAllowed, CStringArray &msgList);
	int GetCaseHandlingCount(double lw, double ld, double lh,
		double pw, double pl, double ph, int levelType);
	int GetPalletHandlingCount(double lw, double ld, double lh,	double pw, double pl, double ph);

	typedef enum {
		Baseline = 0,
		Optimize = 1
	} enumOrigin;

};

#endif // !defined(AFX_SOLUTIONDATASERVICE_H__65257A56_82F6_4916_8C0E_2CDD0E60E9AD__INCLUDED_)

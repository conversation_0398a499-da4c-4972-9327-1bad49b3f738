// ColoringHelper.cpp: implementation of the CColoringHelper class.
//
//////////////////////////////////////////////////////////////////////

#include "stdafx.h"
#include "modal.h"
#include "ColoringHelper.h"
#include "AutoCADCommands.h"
#include "SolutionDataService.h"
#include "FacilityDataService.h"
#include "DataAccessService.h"

#include "ColorModelDlg.h"

#include "ColorProductGroupDialog.h"
#include "TreeElement.h"
#include "ssa_exception.h"
#include "ChangeRackType.h"
#include "Prompt.h"
#include "ControlService.h"
#include "UtilityHelper.h"

#include "ResourceHelper.h"
#include <aced.h>
#include <adscodes.h>

#ifdef _DEBUG
#undef THIS_FILE
static char THIS_FILE[]=__FILE__;
#define new DEBUG_NEW
#endif


extern TreeElement changesTree;
extern CDataAccessService dataAccessService;
extern CControlService controlService;
extern CUtilityHelper utilityHelper;



//////////////////////////////////////////////////////////////////////
// Construction/Destruction
//////////////////////////////////////////////////////////////////////

CColoringHelper::CColoringHelper()
{
	m_pColorResults = NULL;
}

CColoringHelper::~CColoringHelper()
{
	if (m_pColorResults != NULL) {
		m_pColorResults->DestroyWindow();
		m_pColorResults = NULL;
	}
}


void CColoringHelper::ColorByHandle()
{
	char handle[132];
	char colorBuf[132];
	int colorIndex;
	CString strColor;

	if (! (ads_getstring(1, "Enter acad handle: ", handle) == RTNORM))
		return;

	if (! (ads_getstring(1, "Enter color: ", colorBuf) == RTNORM))
		return;
	
	strColor = colorBuf;
	colorIndex = CAutoCADCommands::GetColorIndexByName(strColor);

	CAutoCADCommands::ColorDrawingObjectByHandle(handle, colorIndex);

	return;
}


void CColoringHelper::ColorAisle()
{
	CString handle;
	int colorIndex;
	CString strColor;
	CStringArray selectedHandleList;
	CStringArray aisleIdList;
	CStringArray bayHandles, tempHandles;
	CString tmpStr;
	TreeElement *aislePtr = NULL;
	BOOL found;
	CDWordArray aisleOffsetList;
	int i, j, k;

//	DumpTree("c:\\temp\\tree1.txt", changesTree);

	// Get user-selected bays
	if (CAutoCADCommands::GetSelectedHandles(selectedHandleList) <= 0) {
		AfxMessageBox("Please select at least one bay within each aisle you want to color.");
		return;
	}
	


	// Build a list of bay handles in the aisles associated with the selected bays
	for (i=0; i < selectedHandleList.GetSize(); ++i) {
		CWaitCursor cwc;
		// See if this handle is already in the list; we may have already gotten
		// all of the handles for this aisle
		found = FALSE;
		for (j=0; j < bayHandles.GetSize(); ++j) {
			if (strcmp(bayHandles[j], selectedHandleList[i]) == 0) {
				found = TRUE;
				break;
			}
		}

		if (found)
			continue;

		// this will check the btree to see if the aisle is there
		// if not, get it from the database
		aislePtr = changesTree.getAisleByBayHandle(selectedHandleList[i]);
		if (aislePtr == NULL)
			continue;

		// Look for the aisle in the list to see if we've already processed it
		found = FALSE;
		for (j=0; j < aisleOffsetList.GetSize(); ++j) {
			if (aisleOffsetList[j] == (DWORD)aislePtr) {
				found = TRUE;
				break;
			}
		}

		if (found)			// we've already processed this aisle
			continue;

		aisleOffsetList.Add((DWORD)aislePtr);

//		DumpTree("c:\\temp\\tree2.txt", changesTree);
		// Get all of the bay handles that are currently in the btree
		// for this aisle; add them to the list if they are not already there
		if (changesTree.getBayHandlesByAisle(aislePtr, tempHandles) > 0) {
			for (j=0; j < tempHandles.GetSize(); ++j) {
				found = FALSE;
				for (k=0; k < bayHandles.GetSize(); ++k) {
					if (bayHandles[k] == tempHandles[j]) {
						found = TRUE;
						break;
					}
				}
				if (! found)
					bayHandles.Add(tempHandles[j]);

			}
		}
		// Get the list of handles from the database for this aisle in
		// case the btree was not fully populated

		// If the dbid is 0, this is a new aisle so there's nothing in the database
		if (aislePtr->elementDBID <= 0)
			continue;

		tempHandles.RemoveAll();
		try {
			CFacilityDataService facilityDataService;
			if (facilityDataService.GetBayHandlesByAisleId(aislePtr->elementDBID, tempHandles) <= 0)
				continue;
		}
		catch(Ssa_Exception e) {
			char eMsg[1024];
			e.GetMessage(eMsg);
			ads_printf("%s\n", eMsg);
		}
		catch(...) {
			ads_printf("Error getting handles from database.\n");
		}	

		for (j=0; j < tempHandles.GetSize(); ++j) {
			found = FALSE;
			for (k=0; k < bayHandles.GetSize(); ++k) {
				if (bayHandles[k] == tempHandles[j]) {
					found = TRUE;
					break;
				}
			}
			if (! found)
				bayHandles.Add(tempHandles[j]);
		}


	}

	colorIndex = CAutoCADCommands::GetColorChoice();

	for (i=0; i < bayHandles.GetSize(); ++i)
		CAutoCADCommands::ColorDrawingObjectByHandle(bayHandles[i], colorIndex);


	return;


}

void CColoringHelper::ColorByProfile()
{

	CChangeRackType dlg;
	CStringArray handles;
	int i, colorIndex;

	dlg.m_bCurrentFacilityOnly = TRUE;
	dlg.m_Title = "Color Profile";

	if (dlg.DoModal() != IDOK)
		return;

	try {
		CFacilityDataService facilityDataService;
		facilityDataService.GetBayHandlesByProfile(dlg.m_SelectedBayProfileId, handles);
	}
	catch(Ssa_Exception e) {
		char eMsg[1024];
		e.GetMessage(eMsg);
		ads_printf("%s\n", eMsg);
		AfxMessageBox("Error getting bay profile from database.");
		return;
	}
	catch(...) {
		AfxMessageBox("Error getting bay profile from database.");
		return;
	}

	colorIndex = CAutoCADCommands::GetColorChoice();

	CWaitCursor cwc;
	for (i=0; i < handles.GetSize(); ++i) {
		CAutoCADCommands::ColorDrawingObjectByHandle(handles[i], colorIndex);
	}

	return;


}


void CColoringHelper::ColorModel()
{

	CTemporaryResourceOverride tro;

	CColorModelDlg dlg;
	int rc = dlg.DoModal();

	if (rc != IDOK)
		return;

	CStringArray ranges;
	if (DoColorModal(dlg, ranges) != 0)
		return;
	
	if (dlg.m_Mode == Reset)
		return;

	if (m_pColorResults != NULL) {
		delete m_pColorResults;
		m_pColorResults = NULL;
	}
	
	
	CDisplayResults resultsDlg;
	
	resultsDlg.m_Headers.Add("Color|Minimum Value|Maximum Value|Number of Bays|");
	resultsDlg.m_Tabs.Add("Coloring Results");

	CString temp;
	temp.Format("Coloring Results for %s", dlg.m_pProductAttribute->m_Name);
	resultsDlg.m_WindowCaptions.Add(temp);

	resultsDlg.m_HelpTopics.Add("ColorModelResults_Main");
	resultsDlg.m_ListHelpTopics.Add("ColorModelResults_Main");
	resultsDlg.m_OrigColumnSize = 105;
	
	resultsDlg.m_IsModeless = FALSE;
	resultsDlg.m_MainHelpTopic = "ColorModelResults_Main";
	
	/*
	BOOL bSuccess = m_pColorResults->Create(IDD_DISPLAY_RESULTS, utilityHelper.GetParentWindow());
	if (!bSuccess) {
		controlService.Log("Error displaying coloring results dialog.", 
			"Error in Create.\n");
		return;
	}
	
	m_pColorResults->CenterWindow();
	m_pColorResults->ShowWindow(SW_SHOW);
	*/
	
	resultsDlg.m_Data.Copy(ranges);

	CAutoCADCommands::Flush();
	resultsDlg.DoModal();
}


void CColoringHelper::ColorProductGroup()
{

	try {
		CColorProductGroupDialog dlg;

		dlg.DoModal();
	}
	catch (...) {
		AfxMessageBox("Error displaying color product groups dialog.");
	}

	return;

}


int CColoringHelper::DoColorModal(CColorModelDlg &dlg, CStringArray &ranges)
{
	float newValue = 0.0;
	float storedValue = 0.0;
	CString minValue;
	CString maxValue;
	CStringArray results;
	int totalNum = 0;
	int nIndex = -1;
	int numBaysPerColor = 0;
	float prevValue = 0.0;
	int leftoverBays = 0;
	BOOL skip = FALSE;

	m_MinResultsMap.RemoveAll();
	m_MaxResultsMap.RemoveAll();

	CWaitCursor cwc;

	if (dlg.m_Mode == CColoringHelper::Reset) {
		//User wants to reset the facility to normal color.
		CAutoCADCommands::ColorAllObjects();	// default is white
		return 0;
	}

	
	if (dlg.m_Reset)
		CAutoCADCommands::ColorAllObjects();

	//Now if we reach this part, we are doing TrueScale, Percentage
	//or Specific

	//First, lets get the Solution set by doing a call to Forte
	//	AfxMessageBox("Before call to forte");
	CSolutionDataService sdService;

	try {
		if (dlg.m_Mode != Range)
			sdService.GetSolutionLocationsByAttribute(controlService.GetCurrentFacilityDBId(), 
				dlg.m_pProductAttribute, dlg.m_MultiType, dlg.m_RelativeLevel, dlg.m_Origin, 
				dlg.m_MinHeight, dlg.m_MaxHeight, results);
		else
			sdService.GetSolutionLocationsByRange(controlService.GetCurrentFacilityDBId(), 
				dlg.m_pProductAttribute, dlg.m_RelativeLevel, dlg.m_Origin, dlg.m_MinHeight, dlg.m_MaxHeight,
				dlg.m_StartValue, dlg.m_EndValue, results);
	}
	catch (...) {
		controlService.Log("Error getting product location assignments to color.",
			"Generic exception in GetSolutionLocationsByAttribute.\n");
		return -1;
	}

	//lets parse the solution set and store the data in arrays.
	//Since the data comes on a location basis we want to sum it
	//up on a bay basis.
	CStringArray strings;
	CString handle, value;


	CArray<structColor, structColor&> colorArray;

	BOOL isString = TRUE;
	if (dlg.m_pProductAttribute->m_Type == DT_INT || dlg.m_pProductAttribute->m_Type == DT_FLOAT)
		isString = FALSE;

	for (int i = 0; i < results.GetSize(); i++) {
		strings.RemoveAll();
		// Handle | Value
		utilityHelper.ParseString(results[i], "|", strings);

		structColor s;
		s.handle = strings[0];
		s.value = strings[1];
		s.colorIndex = 0;
		
		colorArray.Add(s);
		
		if (i == 0) {
			if (! isString)
				minValue.Format("%f", atof(s.value));
			else
				minValue = s.value;
		}

		
		if (isString) {
			minValue = min(minValue, s.value);
			maxValue = max(maxValue, s.value);
		}
		else {
			minValue.Format("%f", min(atof(minValue), atof(s.value)));
			maxValue.Format("%f", max(atof(maxValue), atof(s.value)));
		}
			 
	}


	// Based on the AutoCAD color spectrum - define the color shades...
	int colorCount = 0;
	int *spectrum;
//	int YellowSpectrum[] = {50,51,52,53,54,55,56,57,58,59,60};
//	int CyanSpectrum[] = {130,131,132,133,134,135,136,137,138,139,140};
//	int MagentaSpectrum[] = {210,220,212,222,214,224,216,226,218,228};
//	int GreenSpectrum[] = { 81,91,101,70,80,90,100,72,82,92,102,74,84,94,104,76,86,96,106};
//	int BlueSpectrum[] = { 151,161,171,150,160,170,180,152,162,172,182,154,164,174,184,156,166,176,186};
//	int RedSpectrum[] = {231,11,21,230,240,10,20,232,242,12,22,234,244,14,24,236,246,16,26};
	int aSpectrum[] = {10,30,50,80,162,202};
	char *colorNames[6] = {"Red", "Orange", "Yellow", "Cyan", "Blue", "Magenta"};

	// Red, Orange, Yellow, Cyan, Green, Blue, Magenta
 	colorCount = sizeof(aSpectrum)/sizeof(int);
	spectrum = aSpectrum;

	if (dlg.m_Mode == CColoringHelper::Range)
		nIndex = dlg.m_CurrentColor.colorIndex;

	float slope = 0.0;
	float alp = 0.0;
	//if we are doing TrueScale, then calculate the slope and alpha
	//that we are going to need to determine the color index of each
	//value.
	if (dlg.m_Mode == CColoringHelper::TrueScale) {
		if (dlg.m_Direction == LowBright) {
			//calculate slope
			slope = (colorCount - 0) / (atof(maxValue) - atof(minValue));
			//calculate alpha
			alp = 0 - (atof(minValue) * slope);
		}
		else {
			//calculate slope
			slope = (0 - colorCount) / (atof(maxValue) - atof(minValue));
			//calculate alpha
			alp = colorCount - (atof(minValue) * slope);
		}
	}

	if (dlg.m_Mode == CColoringHelper::Percentage) {

		double t1 = double (colorArray.GetSize());
		double t2 = double (colorCount);
		numBaysPerColor = (int)(t1/t2);
		int tmpNum = int (numBaysPerColor);
		t1 = numBaysPerColor - tmpNum;
		leftoverBays = totalNum % colorCount;
	}


	//initialize results maps
	CString minNum = "";
	CString maxNum = "";
	for (i = 0; i < colorCount; i++) {
		m_MinResultsMap.SetAt(colorNames[i], maxNum);
		m_MaxResultsMap.SetAt(colorNames[i], minNum);
	}

	int numBays = 0;
	if (dlg.m_Mode == CColoringHelper::Percentage || dlg.m_Mode == CColoringHelper::TrueScale) {
		if (dlg.m_Direction == LowBright)
			nIndex = colorCount-1;
		else 
			nIndex = 0;
	}

	CMap<CString, LPCTSTR, int, int> countMap;

	//loop through each summed value of the solution set and
	//color the corresponding bay
	for (i = 0; i < colorArray.GetSize(); i++) {
		structColor s = colorArray[i];

		switch (dlg.m_Mode) {
		case CColoringHelper::Range:		

			if (! isString) {
				if (atof(s.value) < atof(dlg.m_StartValue) || 
					atof(s.value) > atof(dlg.m_EndValue))
					continue;
			}
			else {	// string
				if (s.value < dlg.m_StartValue ||
					s.value > dlg.m_EndValue)
					continue;
			}
			
			break;

		case CColoringHelper::TrueScale:		//TrueScale
			//calculate the color index
			nIndex = (int)((atof(s.value) * slope) + alp);
			break;
			
		case CColoringHelper::Percentage:		//Percentage

			numBays++;
			if (numBays > numBaysPerColor) {
				//make sure we keep same values with the same
				//color index
				if (atof(s.value) != prevValue) {
					if ((leftoverBays > 0) && (! skip)) {
						leftoverBays--;
						skip = TRUE;
						
					}
					else {
						numBays = 1;
						skip = FALSE;
						if (dlg.m_Direction == LowBright)
							nIndex--;
						else
							nIndex++;
					}
					
				}
				else {
					//add one more bay to this color in order
					//to start eliminating some of the leftover bays
					//before we change to the next color.
					if ((leftoverBays > 0) && (! skip)) {
						leftoverBays--;
						//skip = TRUE;
					}
				}
			}
			else {
				if ( (numBaysPerColor <= 0) && (leftoverBays > 0)
					&& (atof(s.value) != prevValue) ) {
					if (dlg.m_Direction == LowBright)
						nIndex--;
					else
						nIndex++;
				}
			}

			prevValue = atof(s.value);

			break;
		}

		if ( nIndex > colorCount-1 )
			nIndex = colorCount-1;
		else if ( nIndex < 0 )
			nIndex = 0;


		// Store the minimum and maximum values for each color
		if (dlg.m_Mode == TrueScale || dlg.m_Mode == Percentage) {
			
			int cnt;
			if (countMap.Lookup(colorNames[nIndex], cnt))
				cnt++;
			else
				cnt = 1;
			countMap.SetAt(colorNames[nIndex], cnt);

			CString res;
			m_MinResultsMap.Lookup(colorNames[nIndex], res);
			if (res == "")
				res = s.value;

			if (isString)
				res = res < s.value ? res : s.value;
			else
				res.Format("%.02f", min(atof(res), atof(s.value)));

			m_MinResultsMap.SetAt(colorNames[nIndex], res);

			m_MaxResultsMap.Lookup(colorNames[nIndex], res);
			
			if (res == "")
				res = s.value;

			if (isString)
				res = max(res, s.value);
			else
				res.Format("%.02f", max(atof(res), atof(s.value)));

			m_MaxResultsMap.SetAt(colorNames[nIndex], res);
		}

		if (s.handle == "")
			continue;

		if (dlg.m_Mode == Range)
			CAutoCADCommands::ColorDrawingObjectByHandle(s.handle, dlg.m_CurrentColor.colorIndex);
		else
			CAutoCADCommands::ColorDrawingObjectByHandle(s.handle, spectrum[nIndex]);
	}

	if (dlg.m_Mode == Range) {
		CString temp;
		temp.Format("%s|%s|%s|%d|", dlg.m_CurrentColor.text, minValue, maxValue, 
			results.GetSize());
		ranges.Add(temp);
	}
	else {
		
		for (i=0; i < colorCount; ++i) {
			CString color, min, max;
			color = colorNames[i];
			int cnt;
			if (! m_MinResultsMap.Lookup(color, min))
				min = "";
			
			if (! m_MaxResultsMap.Lookup(color, max))
				max = "";
			
			if (! countMap.Lookup(color, cnt))
				cnt = 0;
			
			if (min.IsEmpty() && max.IsEmpty())
				continue;
			
			CString temp;
			temp.Format("%s|%s|%s|%d|", color, min, max, cnt);
			ranges.Add(temp);
		}
	}


	return 0;

}


void CColoringHelper::ColorProduct()
{
	CString prodId;
	int colorIndex;
	CString sql, wmsID;
	CStringArray results;
	CPrompt dlg;

	dlg.m_ParameterName = "Enter WMS Product Id: ";

	if (dlg.DoModal() == IDOK) {
		prodId = dlg.m_ParameterValue;

		sql.Format("select b.acadhandle "
			"from dbbay b, dblevel le, dblocation l, dbslotsolution ss, dbproductpack pp, "
			"dbproduct p "
			"where p.dbfacilityid = %d "
			"and p.dbproductid = pp.dbproductid "
			"and pp.wmsproductid = '%s' "
			"and ss.dbproductpackid = pp.dbproductpackid "
			"and ss.dblocationid = l.dblocationid "
			"and l.dblevelid = le.dblevelid "
			"and le.dbbayid = b.dbbayid ", changesTree.elementDBID, prodId);

		try {
			if (dataAccessService.ExecuteQuery("GetHandleByProduct", sql, results) < 0)
				return;
		}
		catch (...) {
			AfxMessageBox("Error retrieving assigned handles from database.");
			return;
		}

		colorIndex = CAutoCADCommands::GetColorChoice();
		
		for (int i=0; i < results.GetSize(); ++i)  {
			results[i].TrimRight("|");
			CAutoCADCommands::ColorDrawingObjectByHandle(results[i], colorIndex);
		}
	}
	return;

}

////////////////////////////////////////////////////////////////
//Find the input element key (an autocad handle) in the
//AutoCad Handle array.   If found, then return corresponding
//index in array.  If not found, return -1.
////////////////////////////////////////////////////////////////
int CColoringHelper::findElem(CString elem_key, CArray <CString, CString&> &acadArray)
{

	int sz = acadArray.GetSize();
	int pos;
	int indx = -1;
	for (pos = 0; pos < sz; pos++)
	{
		if (elem_key == acadArray[pos])
		{
			indx = pos;
			break;
		}
	}

	return indx;
}



/////////////////////////////////////////////////////////
//Sort the AutoCad Handle Array and the Value Array
//in descending order.
////////////////////////////////////////////////////////
void CColoringHelper::SortArraysByValueDes(CArray <CString, CString&> &acadArray,
										   CArray <float, float&> &valueArray)
{
	int i, j;
	float tempVal;
	int sz;
	CString tempStr;

	sz = valueArray.GetSize();

	for (i = 0; i < sz; i++)
	{
		for (j = i+1; j < sz; j++)
		{
			if (valueArray[i] < valueArray[j])
			{
				tempVal = valueArray.GetAt(i);
				valueArray.SetAt(i, valueArray[j]);
				valueArray.SetAt(j, tempVal);

				//do the same with the acadArray
				tempStr = acadArray.GetAt(i);
				acadArray.SetAt(i, acadArray[j]);
				acadArray.SetAt(j, tempStr);
			}
		}
	}

}



/////////////////////////////////////////////////////////////
//Convert a color string to its corresponding index in the
//spectrum array.
/////////////////////////////////////////////////////////////
int CColoringHelper::GetIndexFromColor(CString strColor)
{
	int nIndex;

	// these look wrong to me - brd
	if (strColor == "Red")
		nIndex = 0;		// color 10
	if (strColor == "Orange")
		nIndex = 1;		// color 30
	if (strColor == "Yellow")
		nIndex = 2;		//color 50
	if (strColor == "Green")
		nIndex = 3;		//color 80
	if (strColor == "Blue")
		nIndex = 4;		//color 162
//	if (strColor == "Indigo")
//		nIndex = ;
	if (strColor == "Violet")
		nIndex = 5;		//color 202

	return nIndex;
}
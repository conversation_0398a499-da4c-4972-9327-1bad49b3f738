// LocationProperties.cpp : implementation file
//

#include "stdafx.h"
#include "LocationProperties.h"
#include "HelpService.h"
#include "ProductMaintenance.h"
#include "UDFPage.h"
#include "ResourceHelper.h"
#include "UtilityHelper.h"
#include "ssa_exception.h"
#include "Solution.h"
#include "SolutionDataService.h"
#include "FacilityElementSheet.h"

#ifdef _DEBUG
#define new DEBUG_NEW
#undef THIS_FILE
static char THIS_FILE[] = __FILE__;
#endif

extern CHelpService helpService;
extern CUtilityHelper utilityHelper;
extern CSolutionDataService solutionDataService;

/////////////////////////////////////////////////////////////////////////////
// CLocationProperties property page

IMPLEMENT_DYNCREATE(CLocationProperties, CPropertyPage)

CLocationProperties::CLocationProperties() : CPropertyPage(CLocationProperties::IDD)
{
	//{{AFX_DATA_INIT(CLocationProperties)
	m_Coordinates = _T("");
	m_Description = _T("");
	m_HandlingMethod = -1;
	m_MaxWeight = 0.0;
	m_ProductDescription = _T("");
	m_ProductGroup = _T("");
	m_IsSelect = FALSE;
	m_WMSID = _T("");
	m_Depth = 0.0;
	m_Height = 0.0;
	m_Width = 0.0;
	m_Status = _T("");
	m_IsActive = FALSE;
	m_BackfillCoordinates = _T("");
	m_BackfillId = _T("");
	m_StockerId = _T("");
	m_Trace = FALSE;
	m_Clearance = 0.0;
	m_StockerCoordinates = _T("");
	m_SelectionSequence = _T("");
	m_ReplenishmentSequence = _T("");
	//}}AFX_DATA_INIT
}

CLocationProperties::~CLocationProperties()
{
}

void CLocationProperties::DoDataExchange(CDataExchange* pDX)
{
	CPropertyPage::DoDataExchange(pDX);
	//{{AFX_DATA_MAP(CLocationProperties)
	DDX_Text(pDX, IDC_COORDINATES, m_Coordinates);
	DDX_Text(pDX, IDC_DESCRIPTION, m_Description);
	DDX_CBIndex(pDX, IDC_HANDLING_METHOD, m_HandlingMethod);
	DDX_Text(pDX, IDC_MAX_WEIGHT, m_MaxWeight);
	DDX_Text(pDX, IDC_PRODUCT_DESCRIPTION, m_ProductDescription);
	DDX_Text(pDX, IDC_PRODUCT_GROUP, m_ProductGroup);
	DDX_Check(pDX, IDC_SELECT, m_IsSelect);
	DDX_Text(pDX, IDC_WMS_ID, m_WMSID);
	DDX_Text(pDX, IDC_DEPTH, m_Depth);
	DDX_Text(pDX, IDC_HEIGHT, m_Height);
	DDX_Text(pDX, IDC_WIDTH, m_Width);
	DDX_Text(pDX, IDC_STATUS, m_Status);
	DDX_Check(pDX, IDC_ACTIVE, m_IsActive);
	DDX_Text(pDX, IDC_BACKFILL_COORDINATES, m_BackfillCoordinates);
	DDX_Text(pDX, IDC_BACKFILL_ID, m_BackfillId);
	DDX_Text(pDX, IDC_STOCKER_ID, m_StockerId);
	DDX_Check(pDX, IDC_TRACE, m_Trace);
	DDX_Text(pDX, IDC_CLEARANCE, m_Clearance);
	DDV_MinMaxDouble(pDX, m_Clearance, 0., 999999.);
	DDX_Text(pDX, IDC_STOCKER_COORDINATES, m_StockerCoordinates);
	DDX_Text(pDX, IDC_SELECTION_SEQUENCE, m_SelectionSequence);
	DDX_Text(pDX, IDC_REPLENISHMENT_SEQUENCE, m_ReplenishmentSequence);
	//}}AFX_DATA_MAP
}


BEGIN_MESSAGE_MAP(CLocationProperties, CPropertyPage)
	//{{AFX_MSG_MAP(CLocationProperties)
	ON_WM_HELPINFO()
	ON_BN_CLICKED(IDC_VIEW_PRODUCT, OnViewProduct)
	ON_BN_CLICKED(IDC_BASELINE_PRODUCT, OnBaselineProduct)
	ON_BN_CLICKED(IDC_OPTIMIZED_PRODUCT, OnOptimizedProduct)
	ON_MESSAGE(MESSAGE_DEACTIVATE, (LRESULT (AFX_MSG_CALL CWnd::*)(WPARAM, LPARAM))OnDeactivate)
	ON_MESSAGE(MESSAGE_ACTIVATE, (LRESULT (AFX_MSG_CALL CWnd::*)(WPARAM, LPARAM))OnActivate)
	//}}AFX_MSG_MAP
END_MESSAGE_MAP()

/////////////////////////////////////////////////////////////////////////////
// CLocationProperties message handlers

BOOL CLocationProperties::OnKillActive() 
{
	double totalWidth;
	CEdit *pEdit;

	UpdateData(TRUE);

	totalWidth = m_TotalWidth - m_OriginalWidth + m_Width;
	m_TotalWidth = totalWidth;

	if (totalWidth > m_MaxWidth) {
		if (AfxMessageBox("Warning.  The total width of all locations in this bay exceeds the width of the bay. Continue?", MB_YESNO) == IDNO) {
			pEdit = (CEdit *)GetDlgItem(IDC_WIDTH);
			pEdit->SetSel(0,-1);
			pEdit->SetFocus();
			return FALSE;
		}
	}

	if (m_Width < m_MinWidth) {
		if (AfxMessageBox("Warning.  The location width is less than the minimum width for the level.  Continue?", MB_YESNO) == IDNO) {
			pEdit = (CEdit *)GetDlgItem(IDC_WIDTH);
			pEdit->SetSel(0,-1);
			pEdit->SetFocus();
			return FALSE;
		}
	}
	
	if (m_Depth > m_MaxDepth) {
		if (AfxMessageBox("Warning.  The location depth is greater than the depth of the bay. Continue?", MB_YESNO) == IDNO) {
			pEdit = (CEdit *)GetDlgItem(IDC_DEPTH);
			pEdit->SetSel(0,-1);
			pEdit->SetFocus();
			return FALSE;
		}
	}

	if (m_Clearance >= m_MaxHeight) {
		if (AfxMessageBox("Warning.  The clearance is greater than or equal to the level height.  Continue?", MB_YESNO) == IDNO) {
			pEdit = (CEdit *)GetDlgItem(IDC_DEPTH);
			pEdit->SetSel(0,-1);
			pEdit->SetFocus();
			return FALSE;
		}
	}

	if (m_Height > m_MaxHeight) {
		if (AfxMessageBox("Warning.  The location height plus the clearance is greater than the level height. Continue?", MB_YESNO) == IDNO) {
			pEdit = (CEdit *)GetDlgItem(IDC_HEIGHT)	;
			pEdit->SetSel(0,-1);
			pEdit->SetFocus();
			return FALSE;
		}
	}
	
	CStringArray strings;
	bool okay = true;
	if (utilityHelper.ParseString(m_BackfillCoordinates, ",", strings) < 3)
		okay = false;
	else {
		if (! utilityHelper.IsInteger(strings[0]) ||
			! utilityHelper.IsInteger(strings[1]) ||
			! utilityHelper.IsInteger(strings[2]))
			okay = false;
	}

	if (! okay) {
		AfxMessageBox("The backfill coordinates are not formatted properly.\n"
			"The coordinates must be integers separated by commas.");
		pEdit = (CEdit *)GetDlgItem(IDC_BACKFILL_COORDINATES);
		pEdit->SetSel(0, -1);
		pEdit->SetFocus();
		return FALSE;
	}

	if (utilityHelper.ParseString(m_StockerCoordinates, ",", strings) < 3)
		okay = false;
	else {
		if (! utilityHelper.IsInteger(strings[0]) ||
			! utilityHelper.IsInteger(strings[1]) ||
			! utilityHelper.IsInteger(strings[2]))
			okay = false;
	}

	if (! okay) {
		AfxMessageBox("The stocker coordinates are not formatted properly.\n"
			"The coordinates must be integers separated by commas.");
		pEdit = (CEdit *)GetDlgItem(IDC_BACKFILL_COORDINATES);
		pEdit->SetSel(0, -1);
		pEdit->SetFocus();
		return FALSE;
	}

	if (m_BackfillId == "")
		m_BackfillId = " ";

	if (m_StockerId == "")
		m_StockerId = " ";

	UpdateData(FALSE);

	return CPropertyPage::OnKillActive();
}

BOOL CLocationProperties::OnInitDialog() 
{
	CPropertyPage::OnInitDialog();
	
	CRect r;
	CComboBox *pComboBox = (CComboBox *)GetDlgItem(IDC_HANDLING_METHOD);
	pComboBox->SetItemHeight(0,2000);
	pComboBox->GetWindowRect(&r);
	pComboBox->SetWindowPos(NULL, 0, 0, r.Width(), r.Height()*3, SWP_NOMOVE|SWP_NOZORDER);
	
	CButton *pButton = (CButton *)GetDlgItem(IDC_VIEW_PRODUCT);
	if (m_ProductDBID > 0)
		pButton->EnableWindow(TRUE);
	else
		pButton->EnableWindow(FALSE);

	if (m_BackfillId == " ")
		m_BackfillId = "";

	if (m_StockerId == " ")
		m_StockerId = "";

	pButton = (CButton *)GetDlgItem(IDC_BASELINE_PRODUCT);
	pButton->SetCheck(0);
	pButton = (CButton *)GetDlgItem(IDC_OPTIMIZED_PRODUCT);
	pButton->SetCheck(1);

	UpdateData(FALSE);

	return TRUE;  // return TRUE unless you set the focus to a control
	              // EXCEPTION: OCX Property Pages should return FALSE
}

BOOL CLocationProperties::OnNotify(WPARAM wParam, LPARAM lParam, LRESULT* pResult) 
{
	NMHDR* pNMHDR = (NMHDR*)lParam;
	if (pNMHDR->code == PSN_HELP) {
		helpService.ShowScreenHelp(IDD);
		return TRUE;
	}
	return CPropertyPage::OnNotify(wParam, lParam, pResult);
}

BOOL CLocationProperties::OnHelpInfo(HELPINFO* pHelpInfo) 
{
	helpService.ShowFieldHelp(IDD, pHelpInfo->iCtrlId);
	return FALSE;
}

void CLocationProperties::OnViewProduct() 
{
	CTemporaryResourceOverride tro;
	CProductSheet sheet("Product Maintenance", this, 0);
	CProductPage productPage;
	CProductContainerPage containerPage;
	CProductOptimizePage optimizePage;
	CUDFPage udfPage;
	CStringArray productList;
	CProductPack product;
	CString line;
	CProductDataService productDataService;
	int rc;


	if (m_ProductDBID <= 0) {
		AfxMessageBox("No product is currently assigned to this location.");
		return;
	}

	sheet.m_DisplayProductID = m_ProductDBID;

	sheet.m_psh.dwFlags |= PSH_NOAPPLYNOW;
	sheet.AddPage(&productPage);
	sheet.AddPage(&containerPage);
	sheet.AddPage(&udfPage);
	sheet.AddPage(&optimizePage);

	try {
		sheet.DoModal();
	}
	catch (...) {
		AfxMessageBox("Error running ProductMaintenance");
	}


	// Re-select the product after maintenance in case, they 
	// modified or deleted it
	try {
		rc = productDataService.GetProductByID(m_ProductDBID, productList);
	}
	catch (Ssa_Exception e) {
		utilityHelper.ProcessError("Error re-selecting product after maintenance.", &e);
		return;
	}
	catch (...) {
		utilityHelper.ProcessError("Generic error re-selecting product after maintenance.");
		return;
	}

	if (productList.GetSize() == 0) {		// the product must have been deleted
		m_WMSID = "";
		m_ProductDescription = "";
		m_ProductDBID = -1;
	}
	else {
		product.ParseAll(productList[0]);
		m_WMSID = product.m_WMSProductID;
		m_ProductDescription = product.m_Description;
	}

	CButton *pButton = (CButton *)GetDlgItem(IDC_VIEW_PRODUCT);
	if (m_ProductDBID > 0)
		pButton->EnableWindow(TRUE);
	else
		pButton->EnableWindow(FALSE);

	UpdateData(FALSE);

}


void CLocationProperties::OnBaselineProduct() 
{
	long dbid;
	CString temp, temp1, temp2;

	if (solutionDataService.GetProductForLocation(m_LocationDBId, temp, temp1, 
		temp2, dbid, CSolution::Baseline) > 0) {
		m_ProductDescription = temp2;
		m_WMSID.Format("%s-%s", temp, temp1);
		m_ProductDBID = dbid;
	}
	else {
		m_ProductDescription = "";
		m_WMSID = "";
		m_ProductDBID = -1;
	}
	
	CButton *pButton = (CButton *)GetDlgItem(IDC_VIEW_PRODUCT);
	if (m_ProductDBID > 0)
		pButton->EnableWindow(TRUE);
	else
		pButton->EnableWindow(FALSE);

	UpdateData(FALSE);	
}

void CLocationProperties::OnOptimizedProduct() 
{
	CString temp, temp1, temp2;
	long dbid;

	if (solutionDataService.GetProductForLocation(m_LocationDBId, temp, temp1, 
		temp2, dbid, CSolution::Optimize) > 0) {
		m_ProductDescription = temp2;
		m_WMSID.Format("%s-%s", temp, temp1);
		m_ProductDBID = dbid;
	}
	else {
		m_ProductDescription = "";
		m_WMSID = "";
		m_ProductDBID = -1;
	}
	
	CButton *pButton = (CButton *)GetDlgItem(IDC_VIEW_PRODUCT);
	if (m_ProductDBID > 0)
		pButton->EnableWindow(TRUE);
	else
		pButton->EnableWindow(FALSE);

	UpdateData(FALSE);	
}

void CLocationProperties::OnActivate()
{
	m_IsActive = TRUE;
	UpdateData(FALSE);
}

void CLocationProperties::OnDeactivate()
{
//	m_IsActive = FALSE;
//	UpdateData(FALSE);
}

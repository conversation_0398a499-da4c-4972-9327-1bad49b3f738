#if !defined(AFX_ROWCURSOR_H__F7CA4EAB_AF75_11D4_9EBD_00C04FAC149C__INCLUDED_)
#define AFX_ROWCURSOR_H__F7CA4EAB_AF75_11D4_9EBD_00C04FAC149C__INCLUDED_

#if _MSC_VER > 1000
#pragma once
#endif // _MSC_VER > 1000
// Machine generated IDispatch wrapper class(es) created by Microsoft Visual C++

// NOTE: Do not modify the contents of this file.  If this class is regenerated by
//  Microsoft Visual C++, your modifications will be overwritten.

/////////////////////////////////////////////////////////////////////////////
// CRowCursor wrapper class

class CRowCursor : public COleDispatchDriver
{
public:
	CRowCursor() {}		// Calls COleDispatchDriver default constructor
	CRowCursor(LPDISPATCH pDispatch) : COleDispatchDriver(pDispatch) {}
	CRowCursor(const CRowCursor& dispatchSrc) : COleDispatchDriver(dispatchSrc) {}

// Attributes
public:

// Operations
public:
};

//{{AFX_INSERT_LOCATION}}
// Microsoft Visual C++ will insert additional declarations immediately before the previous line.

#endif // !defined(AFX_ROWCURSOR_H__F7CA4EAB_AF75_11D4_9EBD_00C04FAC149C__INCLUDED_)

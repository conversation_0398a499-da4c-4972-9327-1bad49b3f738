// IntegrationAssignmentOptionsPage.cpp : implementation file
//

#include "stdafx.h"
#include "modal.h"
#include "IntegrationAssignmentOptionsPage.h"
#include "HelpService.h"

#ifdef _DEBUG
#define new DEBUG_NEW
#undef THIS_FILE
static char THIS_FILE[] = __FILE__;
#endif

extern CHelpService helpService;
/////////////////////////////////////////////////////////////////////////////
// CIntegrationAssignmentOptionsPage property page

IMPLEMENT_DYNCREATE(CIntegrationAssignmentOptionsPage, CPropertyPage)

CIntegrationAssignmentOptionsPage::CIntegrationAssignmentOptionsPage() : CPropertyPage(CIntegrationAssignmentOptionsPage::IDD)
{
	//{{AFX_DATA_INIT(CIntegrationAssignmentOptionsPage)
	m_AutoConfirm = FALSE;
	m_FullExport = FALSE;
	m_InboundPrompt = FALSE;
	m_OutboundPrompt = FALSE;
	m_SkipAssignment = FALSE;
	m_AllowNotIntegrated = FALSE;
	//}}AFX_DATA_INIT
}

CIntegrationAssignmentOptionsPage::~CIntegrationAssignmentOptionsPage()
{
}

void CIntegrationAssignmentOptionsPage::DoDataExchange(CDataExchange* pDX)
{
	CPropertyPage::DoDataExchange(pDX);
	//{{AFX_DATA_MAP(CIntegrationAssignmentOptionsPage)
	DDX_Check(pDX, IDC_AUTO_CONFIRM, m_AutoConfirm);
	DDX_Check(pDX, IDC_FULL_EXPORT, m_FullExport);
	DDX_Check(pDX, IDC_INBOUND_PROMPT, m_InboundPrompt);
	DDX_Check(pDX, IDC_OUTBOUND_PROMPT, m_OutboundPrompt);
	DDX_Check(pDX, IDC_SKIP, m_SkipAssignment);
	DDX_Check(pDX, IDC_ALLOW_NOT_INTEGRATED, m_AllowNotIntegrated);
	//}}AFX_DATA_MAP
}


BEGIN_MESSAGE_MAP(CIntegrationAssignmentOptionsPage, CPropertyPage)
	//{{AFX_MSG_MAP(CIntegrationAssignmentOptionsPage)
	ON_BN_CLICKED(IDC_SKIP, OnSkip)
	ON_WM_HELPINFO()
	//}}AFX_MSG_MAP
END_MESSAGE_MAP()

/////////////////////////////////////////////////////////////////////////////
// CIntegrationAssignmentOptionsPage message handlers

void CIntegrationAssignmentOptionsPage::OnSkip() 
{
	UpdateData(TRUE);
	
	if (m_SkipAssignment) {
		GetDlgItem(IDC_INBOUND_PROMPT)->EnableWindow(FALSE);
		GetDlgItem(IDC_OUTBOUND_PROMPT)->EnableWindow(FALSE);
		GetDlgItem(IDC_AUTO_CONFIRM)->EnableWindow(FALSE);
		GetDlgItem(IDC_FULL_EXPORT)->EnableWindow(FALSE);
		GetDlgItem(IDC_ALLOW_NOT_INTEGRATED)->EnableWindow(FALSE);
	}
	else {
		GetDlgItem(IDC_INBOUND_PROMPT)->EnableWindow(TRUE);
		GetDlgItem(IDC_OUTBOUND_PROMPT)->EnableWindow(TRUE);
		GetDlgItem(IDC_AUTO_CONFIRM)->EnableWindow(TRUE);
		GetDlgItem(IDC_FULL_EXPORT)->EnableWindow(TRUE);
		GetDlgItem(IDC_ALLOW_NOT_INTEGRATED)->EnableWindow(TRUE);

	}
}

BOOL CIntegrationAssignmentOptionsPage::OnInitDialog() 
{
	CPropertyPage::OnInitDialog();
	
	OnSkip();
	
	return TRUE;  // return TRUE unless you set the focus to a control
	              // EXCEPTION: OCX Property Pages should return FALSE
}

BOOL CIntegrationAssignmentOptionsPage::OnHelpInfo(HELPINFO* pHelpInfo) 
{
	helpService.ShowFieldHelp(IDD, pHelpInfo->iCtrlId);
	return FALSE;

}

BOOL CIntegrationAssignmentOptionsPage::OnNotify(WPARAM wParam, LPARAM lParam, LRESULT* pResult) 
{
	NMHDR* pNMHDR = (NMHDR*)lParam;

	if (pNMHDR->code == PSN_HELP) {
		helpService.ShowScreenHelp(IDD);
		return TRUE;
	}
	
	return CPropertyPage::OnNotify(wParam, lParam, pResult);
}
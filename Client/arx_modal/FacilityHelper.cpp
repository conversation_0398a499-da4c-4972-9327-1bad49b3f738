// FacilityHelper.cpp: implementation of the CFacilityHelper class.
//
//////////////////////////////////////////////////////////////////////

#include "stdafx.h"
#include "modal.h"
#include "FacilityHelper.h"
#include "BTreeHelper.h"
#include "ForteService.h"
#include "ControlService.h"

#include "AutoCADCommands.h"
#include "NavigationCommands.h"
#include "NavigationHelper.h"
#include "UtilityHelper.h"
#include "FacilityDataService.h"

#include "ssa_exception.h"
#include "Processing.h"

#include "TreeElement.h"
#include "FacilityDialog.h"

#include "ResourceHelper.h"
#include "ProcessingMessage.h"

#include <errno.h>
#include <direct.h>

#include "base.h"
#include "MiscClasses.h"
#include "SLOTSessionMgr.h"
#include "SrvcObjs.h"

#ifdef _DEBUG
#undef THIS_FILE
static char THIS_FILE[]=__FILE__;
#define new DEBUG_NEW
#endif

CString facilityToOpenFileName;

extern TreeElement	changesTree;
;
extern CString AutoLoginParameters;
extern int			BayNum;
extern BOOL DEBUG;
extern int			numItemsProcessed;

extern CForteService forteService;
extern CBTreeHelper bTreeHelper;
extern CControlService controlService;
extern CUtilityHelper utilityHelper;
extern CFacilityDataService facilityDataService;

//////////////////////////////////////////////////////////////////////
// Construction/Destruction
//////////////////////////////////////////////////////////////////////

CFacilityHelper::CFacilityHelper()
{

}

CFacilityHelper::~CFacilityHelper()
{

}


void CFacilityHelper::OpenDrawing()
{
	Acad::ErrorStatus errorStatus;
	CString acadFileName;
	CFacilityDialog dialog;
	CString scriptFile;
	CString msg;
	CBTreeHelper btHelper;

	if (facilityToOpenFileName == "")
		return;

	CWaitCursor cwc;

	CProcessingMessage message("Opening Drawing", utilityHelper.GetParentWindow());

	acadFileName = controlService.m_ClientHome;
	acadFileName += "\\";
	acadFileName += "facility\\";
	acadFileName += controlService.m_CurrentDatabase;
	acadFileName += "\\";
	acadFileName += facilityToOpenFileName;
	acadFileName += ".dwg";

	ads_printf("Opening drawing: %s.\n", acadFileName);

	// Open the drawing file
	errorStatus = acedSyncFileOpen((LPCSTR)acadFileName);
	if (errorStatus) {
		controlService.Log("Unable to open facility", "Error(%d) opening facility.\n", errorStatus);

		// Unlock the facility
		try {
			if (btHelper.UnlockFacility(changesTree.elementDBID) < 0) {
				AfxMessageBox("Error unlocking the facility.");
				return;
			}
		}
		catch (Ssa_Exception e) {
			utilityHelper.ProcessError("Error unlocking the facility.");
			return;
		}
		
		// Clear out the facility tree
		if ( btHelper.NewFacilityTree(changesTree, controlService.m_DefaultWMS, controlService.m_MeasurementType) != 0 ) {
			ads_printf("Error building blank facility\n");
			AfxMessageBox("Unable to create a new facility.");
			return;
		}
	}

	controlService.SetMode(IDC_OPENFACILITY);

	// MFS Jan 13, 2005.  This lists all the handles in the Facility.
	// CAutoCADCommands::ListHandles();

	return;

}


void CFacilityHelper::OpenFacility()
{
	CString acadFileName;
	CFacilityDialog dialog;
	int idx;
	int facilityToOpenDBID, previousFacilityID;
	CString scriptFile;
	CString msg;
	CBTreeHelper btHelper;

	// Check for changes to current facility
 	try {
		if (! this->CheckCurrentFacility())
			return;
	}
	catch (...) {
		AfxMessageBox("Error saving current facility.");
		return;
	}

	facilityToOpenDBID = 0;

	// Check for auto-open
	if (AutoLoginParameters != "") {
		idx = AutoLoginParameters.Find("|");
		if (idx >= 0 && idx < AutoLoginParameters.GetLength()-1)
			facilityToOpenDBID = atoi(AutoLoginParameters.Mid(idx+1));
		AutoLoginParameters = "";
	}

	// If no auto-open then display the open dialog
	if (facilityToOpenDBID == 0) {
		dialog.m_Mode = 2;
		if (dialog.DoModal() != IDOK) {
			// At this point we haven't closed the old facility yet,
			// so do not create a new one
			return;
		}
		facilityToOpenDBID = dialog.m_FacilityId;
	}

	previousFacilityID = changesTree.elementDBID;

	// Clear out the current facility tree
	if ( btHelper.NewFacilityTree(changesTree, controlService.m_DefaultWMS, controlService.m_MeasurementType) != 0 ) {
		ads_printf("Error building blank facility\n");
		AfxMessageBox("Unable to create a new facility.");
		// We can't be sure that the old facility btree is valid
		// so clean out the drawing just in case
		RunNewFacilityScript();
		return;
	}
	
	CWaitCursor cwc;

	CProcessingMessage message("Opening Facility", utilityHelper.GetParentWindow());


	// Up to this point, they could have gone back to the old facility
	// Now there's no turning back so unlock it
	if (changesTree.elementDBID > 0) {
		try {
			if (btHelper.UnlockFacility(previousFacilityID) < 0) {
				AfxMessageBox("Error unlocking current facility.");
				RunNewFacilityScript();
				return;
			}
		}
		catch (Ssa_Exception e) {
			utilityHelper.ProcessError("Error unlocking current facility.");
			RunNewFacilityScript();
			return;
		}
	}
	
	// Open the facility tree and get the drawing name
	ads_printf("Opening facility tree...\n");
	try {
		CWaitCursor cwc;
		facilityToOpenFileName = btHelper.OpenFacilityTree(facilityToOpenDBID, changesTree, &BayNum);
	}
	catch (Ssa_Exception e) {
		utilityHelper.ProcessError("Error opening facility.", &e);
		RunNewFacilityScript();
		return;
	}
	
	// Facility is already open.  Ask to unlock.
	if ( facilityToOpenFileName.Find("Opened By") != -1 ) {
		msg.Format("Facility %s\n\nMaking changes to a facility while it is in use by "
			"another user may cause the facility to become corrupt.\n"
			"Do you wish to unlock the facility?", facilityToOpenFileName);
		if (MessageBox(NULL, msg, "Unlock Facility?", MB_YESNO) != IDYES) {
			return;
		}
		
		try {
			CWaitCursor cwc;
			if (btHelper.UnlockFacility(facilityToOpenDBID) < 0) {
				AfxMessageBox("Unable to unlock the facility.");
				RunNewFacilityScript();
				return;
			}
			facilityToOpenFileName = btHelper.OpenFacilityTree(facilityToOpenDBID, changesTree, &BayNum);
		}
		catch (Ssa_Exception e) {
			utilityHelper.ProcessError("Error Unlocking/Opening Facility");
			RunNewFacilityScript();
			return;
		}
		
	}

	int rc = facilityDataService.IsFacilityIntegrated(facilityToOpenDBID);
	if (rc <= 0)
		facilityDataService.SetIntegrationStatus(FALSE);
	else
		facilityDataService.SetIntegrationStatus(TRUE);

	if (! DEBUG)
		Sleep(1000);

	controlService.SetMode(IDC_OPENFACILITY);

	// Now the facility is unlocked and the drawing file is ready to be opened
	// Run a script to do the actual drawing open so the prompts can be answered
	CStringArray script;
	CAutoCADCommands acCmds;

	script.Add(".new");
	script.Add("yes");
	script.Add(".");
	script.Add("OpenDrawing");

	qqhSLOTFacility fac;
	bTreeHelper.GetBtFacility(changesTree.fileOffset, fac);

	if (fac.getRegion() == 0) {
		script.Add("ConvertDrawing");
	}


	acCmds.ModifyDrawing();
	acCmds.RunScript(script, "OpenFacilityDrawing");

	return;
}

void CFacilityHelper::ConvertDrawing()
{
	CProcessingMessage message("Converting Facility to New Format", utilityHelper.GetParentWindow());

	if (utilityHelper.ConvertXData() == 0) {
		qqhSLOTFacility fac;
		bTreeHelper.GetBtFacility(changesTree.fileOffset, fac);
		fac.setRegion(1);
		bTreeHelper.SetBtFacility(changesTree.fileOffset, fac);
		QuickSave();
	}
}


void CFacilityHelper::NewFacility()
{
	CBTreeHelper btHelper;

	try {
		if (! this->CheckCurrentFacility())
			return;
	}
	catch (...) {
		AfxMessageBox("Error saving current facility.");
		return;
	}

	// Unlock the previous facility
	if (changesTree.elementDBID > 0) {
		try {
			if (btHelper.UnlockFacility(changesTree.elementDBID) < 0) {
				AfxMessageBox("Error unlocking the facility.");
				return;
			}
		}
		catch (Ssa_Exception e) {
			utilityHelper.ProcessError("Error unlocking the facility.");
			return;
		}
	}

	RunNewFacilityScript();

	return;

}


void CFacilityHelper::SaveFacility()
{

	qqhSLOTFacility theFacility;
	CString scriptFile;
	
	//////////////////////////////////////////////////////////////////////
	// get the facility name
	//////////////////////////////////////////////////////////////////////
	bTreeHelper.GetBtFacility(1, theFacility);
	if ( theFacility.getStrCadFileName() == "NoCadFile" || theFacility.getDBID() == 0 ) {
		SaveAsFacility();
		return;
	}

	CProcessingMessage msg("Saving Facility", utilityHelper.GetParentWindow());

	// First save the database portion since that is the most likely to crash
	// If that fails the drawing and db are still in sync.
	// Then save the drawing locally.  If that fails the drawing and db are out of sync
	// Then save the drawing to the server if necessary.  If that fails, the local copy
	// is in sync but the remote copy isn't

	try {
		if (bTreeHelper.SaveFacility(changesTree, 0, "NoDrawing") < 0) {
			controlService.Log("Error saving facility to database."
				"Please contact support.", "Error in SaveFacility");
			return;
		}
	}
	catch (Ssa_Exception e) {
		char buf[1024];
		e.GetMessage(buf);
		controlService.Log("Error saving facility to database.", "Exception in SaveFacility: %s\n", buf);
		return;
	}
	catch (...) {
		controlService.Log("Error saving facility to database.", "Generic Exception in SaveFacility: %s\n");
	}

	ads_command(RTSTR, ".QSAVE", RTNONE);
	if (CAutoCADCommands::Get_Int_Var("DBMOD") > 0) {
		controlService.Log("The drawing could not be saved.\n"
			"Please verify that there is sufficient disk space on the local hard drive.\n"
			"If the problem persists, re-open the facility and use the Check Facility function"
			"to verify the consistency of the drawing.",
			"Error in qsave\n");
		return;
	}

	try {
		//CHECK007 No more server operations.... may be commented.... 
		bTreeHelper.SaveFacility(changesTree, 0, "DrawingOnly");
	}
	catch(...) {
		controlService.Log("The drawing could not be copied to the server.\n"
			"Please check the network connection and the free disk space on the server and try again.\n"
			"If the problem persists, contact customer support.", "Generic exception in SaveFacility(DrawingOnly)");
		return;
	}

	controlService.SetMode(IDC_OPENFACILITY);
	/*

	fName = theFacility.getStrCadFileName();

	// if they aren't saving it for the first time, they must have opened it
	// so we can assume the directory already exists
	acadName = controlService.m_ClientHome;
	acadName += "\\Facility\\";
	acadName += controlService.m_CurrentDatabase;
	acadName += "\\";
	acadName += fName;
	backupName = acadName;
	acadName += ".dwg";
	backupName += ".bck";


	// Should copy the dwg file to the backup file;
	// then if the db save fails, we can copy it back
	// but not sure how to do all this while the drawing is open
	// and part of the db save is to copy the drawing file to
	// the server so we would have to undo that too
	// Can't do the saveas in a transaction because of that
	// also.  Stupid client/server.

	errorStatus = acdbCurDwg()->saveAs(acadName);
	if (errorStatus != Acad::eOk) {
		AfxMessageBox("Unable to save drawing to disk.  Please check the available disk space and try again.");
		return;
	}

	// Now flush the btree
	try {
		if (SaveFacility(changesTree, 0) < 0) {
			AfxMessageBox("Unable to save the facility information to the database.");
			return;
		}
	}
	catch (Ssa_Exception e) {
		ProcessError("Error saving facility to database.", &e);
	}
	catch (...) {
		ProcessError("Error saving facility to database.");
	}
	*/

	// We should already be in this mode, but just in case

	//DoForteSave();

	return;
}


void CFacilityHelper::SaveAsFacility()
{
	CTemporaryResourceOverride tro;
	CFacilityDialog aDlg;
	CString acadName, temp;
	qqhSLOTFacility myFacility;
	CWinApp * currentApp;
	CBTreeHelper btHelper;

	currentApp = AfxGetApp();

	BOOL done = FALSE;
	int rc;
	
	btHelper.GetBtFacility(1,myFacility);

	aDlg.m_FacilityId = 0;
	aDlg.m_Mode = 1;
	aDlg.m_FacilityNotes = myFacility.getNotes();

	//////////////////////////////////////////////////////////////////////
	// Make sure not overwriting an existing facility
	//////////////////////////////////////////////////////////////////////
	while ( ! done) {
		rc = aDlg.DoModal();
		if (rc == IDOK) {
			btHelper.GetBtFacility(1,myFacility);
			if ( myFacility.getDBID() != aDlg.m_FacilityId && aDlg.m_FacilityId != 0 ) {
				AfxMessageBox("Please select a facility that does not already exist.");
			}
			else
				done = TRUE;
		}
		else
			return;	// they cancelled
	}

	btHelper.GetBtFacility(1, myFacility);

	//If the facility already has a DBID, we need to unlock it before we save the new one
	if (myFacility.getDBID() != 0) {
		if (numItemsProcessed > 0) {
			if (AfxMessageBox("You have made changes to the current facility.\n"
				"Those changes will be applied to the new facility and will not\n"
				"be saved in this facility.  Do you wish to continue?", MB_YESNO) == IDNO)
				return;
		}

		try {
			if (btHelper.UnlockFacility(changesTree.elementDBID) < 0) {
				AfxMessageBox("Error unlocking current facility. Save aborted.");
				return;
			}
		}
		catch (Ssa_Exception e) {
			utilityHelper.ProcessError("Error unlocking current facility. Save aborted.");
			return;
		}
	}

	acadName = controlService.m_ClientHome;
	acadName += "\\";
	acadName += "facility\\";
	acadName += controlService.m_CurrentDatabase;
	
	if (_mkdir(acadName) != 0) {
		if (errno != EEXIST) {
			temp.Format("Unable to create the facility directory: \n%s\nSave aborted.", acadName);
			AfxMessageBox(temp);
			return;
		}
	}
	
	
	acadName += "\\";
	acadName += aDlg.m_FacilityName;
	acadName += ".dwg";
	

	CProcessingMessage msg("Saving Facility", utilityHelper.GetParentWindow());

	TreeElement backupTree = changesTree;
	CString forteRoot = getenv("TEMP");
	forteRoot += "\\tempfile.btd";
	CString backupFile = forteRoot;
	backupFile += ".bak";
	CopyFile(forteRoot, backupFile, FALSE);

	// think about copying the btd file and then restoring it if the save fails

	// Update the btree with the current name, description, and notes
	btHelper.ChangeAcadFileName(aDlg.m_FacilityName);
	btHelper.UpdateFacilityDescription(aDlg.m_FacilityName);
	aDlg.m_FacilityNotes.Replace("\r\n", "<nl>");
	aDlg.m_FacilityNotes.Replace("\n", "<nl>");
	btHelper.UpdateFacilityNotes(aDlg.m_FacilityNotes);
	
	ads_printf("Saving Facility to Database...");
	
	CWaitCursor cwc;
	
	try {
		if ( aDlg.m_FacilityName == myFacility.getDescription() || myFacility.getDescription() == CString("New Facility"))
			btHelper.SaveFacility(changesTree, 0, "NoDrawing");
		else {
			aDlg.m_SaveAsOptions += "|";
			aDlg.m_SaveAsOptions += "NoDrawing";
			btHelper.SaveFacility(changesTree, 1, aDlg.m_SaveAsOptions);
		}
	}	
	catch(Ssa_Exception e) {
		char buf[1024];
		e.GetMessage(buf);
		controlService.Log("Unable to save the facility to the database.", "Exception in SaveFacility: %s\n", buf);
		changesTree = backupTree;
		CopyFile(backupFile, forteRoot, FALSE);
		return;
	}
	catch(...) {
		controlService.Log("Unable to save the facility to the database.", "Generic exception in SaveFacility");
		changesTree = backupTree;
		CopyFile(backupFile, forteRoot, FALSE);
		return;
	}

	Acad::ErrorStatus es;

	es = acdbCurDwg()->saveAs(acadName);
	if (es != Acad::eOk ) {
		controlService.Log("Unable to save the drawing file.\nPlease verify that their is sufficient disk space\n"
			"and that a drawing file with the new facility name does not already exist.\n",
			"Error(%d) in saveAs\n", es);
		try {
			btHelper.DeleteFacility(changesTree.elementDBID);
		}
		catch (Ssa_Exception e) {
			char buf[1024];
			e.GetMessage(buf);
			controlService.Log("The new facility could not be deleted.", 
				"Exception in DeleteFacility: %s\n", buf);
		}
		catch (...) {
			controlService.Log("The new facility could not be deleted.", 
				"Generic exception in DeleteFacility\n");

		}
		changesTree = backupTree;
		CopyFile(backupFile, forteRoot, FALSE);
		return;
	}

	try {
		if ( aDlg.m_FacilityName == myFacility.getDescription() || myFacility.getDescription() == CString("New Facility"))
			btHelper.SaveFacility(changesTree, 0, "DrawingOnly");
		else {
			btHelper.SaveFacility(changesTree, 1, "DrawingOnly");
		}
	}
	catch(...) {
		AfxMessageBox("The drawing could not be copied to the server.\n"
			"Please check the network connection and the free disk space on the server and try again.\n"
			"If the problem persists, contact customer support.");
	}


	controlService.SetMode(IDC_OPENFACILITY);

	return;
}



void CFacilityHelper::DeleteFacility()
{

	CFacilityDialog   aDlg;
	CString    acadName;
	CString    commentsName;
	CStdioFile commentsFile;
	qqhSLOTFacility myFacility;
	CBTreeHelper btHelper;
	CControlService controlService;

	int notDone = 1;
	int rc;
	CWinApp * currentApp;
	currentApp = AfxGetApp();

	aDlg.m_FacilityId = 0;
	aDlg.m_Mode = 3;
	while ( notDone == 1) {
		rc = aDlg.DoModal();
		if (rc == IDOK) {
			btHelper.GetBtFacility(1,myFacility);
			if ( myFacility.getDBID() == aDlg.m_FacilityId && aDlg.m_FacilityId != 0 ) {
				AfxMessageBox("Please select a facility that is not currently open.");
			}
			else
				notDone = 0;
		}
		else
			notDone = 0;
	}
	if (rc == IDOK) {
		
		
		if (facilityDataService.IsFacilityIntegrated(aDlg.m_FacilityId))
			rc = MessageBox(adsw_acadMainWnd(),"The selected facility is currently integrated with a WMS.\n"
			"If it is deleted, the integration will no longer be valid\nAre you sure you wish to delete this facility?",
			"Delete facility " + aDlg.m_FacilityName + "?",
			MB_YESNO|MB_ICONEXCLAMATION|MB_DEFBUTTON2);
		else
			rc = MessageBox(adsw_acadMainWnd(),"Are you sure you wish to delete this facility?",
			"Delete facility " + aDlg.m_FacilityName + "?",
			MB_YESNO|MB_ICONEXCLAMATION|MB_DEFBUTTON2);
			
		if (rc == IDYES) {
			
			CProcessingMessage msg("Deleting Facility", utilityHelper.GetParentWindow());
			CWaitCursor cwc;

			try {
				btHelper.DeleteFacility(aDlg.m_FacilityId);
			}
			catch(...) {
				currentApp->DoWaitCursor(-1);
				AfxMessageBox("Error deleting facility");
				return;
			}
			currentApp->DoWaitCursor(-1);
			
			AfxMessageBox("Facility deleted successfully");
			CStringArray autoStrings;
			CString autoParameter, temp;

			CControlService controlService;
			utilityHelper.ParseString(controlService.GetApplicationData("AutoLoginParameters"), "|", autoStrings);
			if (autoStrings.GetSize() == 2) {
				if (autoStrings[1] != "" && 
					(autoStrings[0] == "" || autoStrings[0] == controlService.m_CurrentDatabase)) {
					if (atoi(autoStrings[1]) == aDlg.m_FacilityId) {
						autoStrings.RemoveAt(1);
						utilityHelper.BuildDelimitedString(autoStrings, autoParameter, "|");
						controlService.SetApplicationData("AutoLoginParameters", autoParameter);
					}
				}
			}

			temp.Format("%s|%d", controlService.m_CurrentDatabase, aDlg.m_FacilityId);
			if (controlService.GetApplicationData(temp, "ColorGroups") != "")
				controlService.SetApplicationData(temp, "", "ColorGroups");

			


		}
	}

	ads_printf("\nCommand: ");

	return;
}


void CFacilityHelper::ExportFacility()
{
	CFacilityDialog   aDlg;
	qqhSLOTFacility myFacility;
	CFileDialog dlgFile(FALSE);
	CString fileName;
	CString title;
	CString strFilter;
	CString strDefault;
	CFile fImport;
	CString allFilter;
	CBTreeHelper btHelper;

	int notDone = 1;
	int rc;
	CWinApp * currentApp;
	currentApp = AfxGetApp();

	aDlg.m_FacilityId = 0;
	aDlg.m_Mode = 4;
	while ( notDone == 1) {
		rc = aDlg.DoModal();
		if (rc == IDOK) {
			btHelper.GetBtFacility(1,myFacility);
			if ( myFacility.getDBID() == aDlg.m_FacilityId && aDlg.m_FacilityId != 0 ) {
				AfxMessageBox("Cannot export a facility that is currently open!");
			}
			else
				notDone = 0;
		}
		else
			notDone = 0;
	}

	if (rc == IDOK) {

		VERIFY(allFilter.LoadString(AFX_IDS_ALLFILTER));
		strFilter += CString("Succeed Facility Files (*.scd)");
		strFilter += (TCHAR)'\0';   
		strFilter += _T("*.scd");
		strFilter += (TCHAR)'\0';
		strFilter += allFilter;
		strFilter += (TCHAR)'\0';   
		strFilter += _T("*.*");
		strFilter += (TCHAR)'\0';   
		
		dlgFile.m_ofn.lpstrFilter = strFilter;
		dlgFile.m_ofn.lpstrDefExt = "scd";
		dlgFile.m_ofn.lpstrTitle = "Export Facility";
		fileName = aDlg.m_FacilityName;
		fileName += ".scd";
		dlgFile.m_ofn.lpstrFile = fileName.GetBuffer(_MAX_PATH);
		BOOL bResult = dlgFile.DoModal() == IDOK ? TRUE : FALSE;
		
		
		fileName.ReleaseBuffer();
		
		if (! bResult)
			return;

		currentApp->DoWaitCursor(1);
		try {
			ExportFacility(aDlg.m_FacilityId, fileName);
		}
		catch(...) {
			currentApp->DoWaitCursor(-1);
			AfxMessageBox("Error Exporting Facility");
			return;
		}
		currentApp->DoWaitCursor(-1);
		
		AfxMessageBox("Facility Exported Successfully");
	}

	ads_printf("\nCommand: ");
	return;
}

void CFacilityHelper::ImportFacility()
{
	CString    acadName;
	qqhSLOTFacility myFacility;
	CString newName, origName;

	CFileDialog dlgFile(TRUE);
	CString fileName;
	CString title;
	CString strFilter;
	CString strDefault;

	CFile fImport;

	CString allFilter;
	VERIFY(allFilter.LoadString(AFX_IDS_ALLFILTER));
	strFilter += CString("Succeed Facility Files (*.scd)");
	strFilter += (TCHAR)'\0';   
	strFilter += _T("*.scd");
	strFilter += (TCHAR)'\0';
	strFilter += allFilter;
	strFilter += (TCHAR)'\0';   
	strFilter += _T("*.*");
	strFilter += (TCHAR)'\0';   

	dlgFile.m_ofn.lpstrFilter = strFilter;
	dlgFile.m_ofn.lpstrDefExt = "scd";
	dlgFile.m_ofn.lpstrTitle = "Import Facility";
	dlgFile.m_ofn.lpstrFile = fileName.GetBuffer(_MAX_PATH);
	BOOL bResult = dlgFile.DoModal() == IDOK ? TRUE : FALSE;

	
	fileName.ReleaseBuffer();
	
	if (! bResult)
		return;

	if (! fImport.Open(fileName, CFile::modeRead)) {
		CString msg;
		msg = "Error opening file: ";
		msg += fileName;
		AfxMessageBox(msg);
		return;
	}

	fImport.Close();

	CWinApp * currentApp;
	currentApp = AfxGetApp();
	
	currentApp->DoWaitCursor(1);
	try {
		ImportFacility(fileName, origName, newName);
	}
	catch(...) {
		currentApp->DoWaitCursor(-1);
		AfxMessageBox("Error importing facility");
		return;
	}
	currentApp->DoWaitCursor(-1);
	
	CString msg;	
	if (origName.Compare(newName) != 0)  {

		msg.Format("Facility %s already exists.  Facility imported as %s.",
			origName, newName);
	}
	else
		msg.Format("Facility %s imported successfully.", origName);

	AfxMessageBox(msg);
	
	ads_printf("\nCommand: ");

	return;
}


BOOL CFacilityHelper::CheckCurrentFacility()
{
	int rc;

	//if (Get_Int_Var("DBMOD") > 0 || numItemsProcessed > 0) {
	// Change this to see if dbmod and objects exists in the drawing
	// or numItemsProcessed > 0
	if (numItemsProcessed > 0) {
		rc = AfxMessageBox("The current facility has been modified.\n"
			"Do you wish to save your changes?", MB_YESNOCANCEL);
		if (rc == IDYES) {
			if (this->QuickSave() < 0)
				return FALSE;
		}
		else if (rc == IDCANCEL)
			return FALSE;
		else
			numItemsProcessed = 0;

	}

	return TRUE;

}




// Clears out the current facility in both AutoCAD and the btree
// Make sure to run this as the last step before returning
// control to AutoCAD
void CFacilityHelper::RunNewFacilityScript(const CString &command)
{
	CStringArray script;
	CAutoCADCommands acCmds;
	CBTreeHelper btHelper;

	// Clear out the facility tree
	if ( btHelper.NewFacilityTree(changesTree, controlService.m_DefaultWMS, controlService.m_MeasurementType) != 0 ) {
		ads_printf("Error building blank facility\n");
		AfxMessageBox("Unable to create a new facility.");
		return;
	}

	controlService.SetMode(IDC_NEWFACILITY);

	script.Add(".new");
	script.Add("yes");
	script.Add(".");
	if (command != "")
		script.Add(command);

	acCmds.ModifyDrawing();
	CString scriptName("NewFacility");
	if (command != "")
		scriptName = command;

	scriptName.Replace(" ", "");

	acCmds.RunScript(script, scriptName);

	return;

}

int CFacilityHelper::QuickSave()
{
	// Realized the quicksave wasn't so I went back to the old way.
	SaveFacility();

	return 0;

	/*
	CString    fName;
	CString    acadName;
	qqhSLOTFacility theFacility;
	CString scriptFile;
	CBTreeHelper btHelper;

	//////////////////////////////////////////////////////////////////////
	// get the facility name
	//////////////////////////////////////////////////////////////////////
	btHelper.GetBtFacility(1, theFacility);
	if ( theFacility.getStrCadFileName() == "NoCadFile" || theFacility.getDBID() == 0 ) {
		return this->QuickSaveAs();
	}
	fName = theFacility.getStrCadFileName();

	// if they aren't saving it for the first time, they must have opened it
	// so we can assume the directory already exists
	acadName = controlService.m_ClientHome;
	acadName += "\\facility\\";
	acadName += controlService.m_CurrentDatabase;
	acadName += "\\";
	acadName += fName;
	acadName += ".dwg";

	CWaitCursor cwc;
	if (acdbCurDwg()->saveAs(acadName) !=  Acad::eOk) {
		AfxMessageBox("Unable to save drawing to disk.  Please check the available disk space and try again.");
		return -1;
	}

	// Now flush the btree
	try {
	if (btHelper.SaveFacility(changesTree, 0) < 0) {
		AfxMessageBox("Unable to save the facility information to the database.");
		return -1;
	}
	}
	catch (Ssa_Exception e) {
		utilityHelper.ProcessError("Error saving facility to database.", &e);
		return -1;
	}
	catch (...) {
		utilityHelper.ProcessError("Error saving facility to database.");
		return -1;
	}

	return 0;
	*/
}


int CFacilityHelper::QuickSaveAs() 
{
	CTemporaryResourceOverride tro;
	CFacilityDialog aDlg;
	CString acadName, temp;
	qqhSLOTFacility myFacility;
	CWinApp * currentApp;
	CBTreeHelper btHelper;

	currentApp = AfxGetApp();

	BOOL done = FALSE;
	int rc;
	
	btHelper.GetBtFacility(1,myFacility);

	aDlg.m_FacilityId = 0;
	aDlg.m_Mode = 1;
	aDlg.m_FacilityNotes = myFacility.getNotes();

	//////////////////////////////////////////////////////////////////////
	// Make sure not overwriting an existing facility
	//////////////////////////////////////////////////////////////////////
	while ( ! done) {
		rc = aDlg.DoModal();
		if (rc == IDOK) {
			btHelper.GetBtFacility(1,myFacility);
			if ( myFacility.getDBID() != aDlg.m_FacilityId && aDlg.m_FacilityId != 0 ) {
				AfxMessageBox("Please select a facility that does not already exist.");
			}
			else
				done = TRUE;
		}
		else
			return -1;	// they cancelled
	}

	btHelper.GetBtFacility(1, myFacility);

	//If the facility already has a DBID, we need to unlock it before we save the new one
	if (myFacility.getDBID() != 0) {
		try {
			if (btHelper.UnlockFacility(changesTree.elementDBID) < 0) {
				AfxMessageBox("Error unlocking current facility. Save aborted.");
				return -1;
			}
		}
		catch (Ssa_Exception e) {
			utilityHelper.ProcessError("Error unlocking current facility. Save aborted.");
			return -1;
		}
	}

	acadName = controlService.m_ClientHome;
	acadName += "\\facility\\";
	acadName += controlService.m_CurrentDatabase;
	
	if (_mkdir(acadName) != 0) {
		if (errno != EEXIST) {
			temp.Format("Unable to create the facility directory: \n%s\nSave aborted.", acadName);
			AfxMessageBox(temp);
			return -1;
		}
	}
	
	
	acadName += "\\";
	acadName += aDlg.m_FacilityName;
	acadName += ".dwg";
	
	if ( acdbCurDwg()->saveAs(acadName) != Acad::eOk ) {
		AfxMessageBox("Unable to save the drawing file.\nPlease check disk space availability.\nSave aborted.");
		return -1;
	}
	
	// Update the btree with the current name, description, and notes
	btHelper.ChangeAcadFileName(aDlg.m_FacilityName);
	btHelper.UpdateFacilityDescription(aDlg.m_FacilityName);
	btHelper.UpdateFacilityNotes(aDlg.m_FacilityNotes);
	
	ads_printf("Saving Facility to Database...");
	
	CWaitCursor cwc;
	
	try {
		if ( aDlg.m_FacilityName == myFacility.getDescription() || myFacility.getDescription() == CString("New Facility"))
			return btHelper.SaveFacility(changesTree, 0);
		else
			return btHelper.SaveFacility(changesTree, 1, aDlg.m_SaveAsOptions);
	}
	catch(...) {
		AfxMessageBox("Unable to save the facility to the database. Save aborted.");
		return -1;
	}

	controlService.SetMode(IDC_OPENFACILITY);

	return 0;
}



void CFacilityHelper::ClearDrawing()
{
	CStringArray script;
	CAutoCADCommands acCmds;
	
	controlService.SetMode(IDC_NEWFACILITY);

	script.Add(".new");
	script.Add("yes");
	script.Add(".");

	acCmds.ModifyDrawing();
	acCmds.RunScript(script, "ClearDrawing");

	
}

CString CFacilityHelper::ExportFacility(int facilityID, CString fileName) 
{
	CString tempString;
#if 0
	CSsaStringArray tempSendArray;
	CSsaStringArray tempRecvArray;
	CSsaStringArray tempArray;
	CString sendString;
#endif
	int i = 0;
#if 0
	int tempInt;
	/////////////////////////////////////////////////////////////
	// build the data to send to the Forte connection server
	/////////////////////////////////////////////////////////////
	sendString = "<SIO>SLOTSocketString\n";
	tempSendArray.Add(sendString);
	sendString.Format("<SAI>%d\n",facilityID);
	tempSendArray.Add(sendString);
	sendString.Format("<SAI>%s\n",fileName);
	tempSendArray.Add(sendString);
	sendString = "<EIO>SLOTSocketString\n";
	tempSendArray.Add(sendString);
	
	forteService.SendToForteConnection(tempSendArray,tempRecvArray,
		CString("SLOTSocketString"), 10300);

	for (i=0; i < tempRecvArray.GetSize(); i++) {
		if ( tempRecvArray[i].Find("<SAI>") != -1 ) {
			tempString = tempRecvArray[i].Mid(5);
			tempInt = tempString.GetLength();
			if (tempString.GetAt(tempInt-1) == '\n' ) {
				tempString.SetAt(tempInt-1,' ');
				tempString.TrimRight();
			}
		}
	}

#else
	string fileNameTmp = fileName;
	try{
		getSessionMgrSO()->ExportFacilityHelper( facilityID, fileNameTmp);
		tempString = "true";
	}
	catch (CException *e)
	{
		tempString = "failure";
		e->Delete();
	}
#endif
	return tempString;
}


int CFacilityHelper::ImportFacility(CString fileName, CString &origName, CString &newName) 
{
	CString tempString;
#if 0
	CSsaStringArray tempSendArray;
	CSsaStringArray tempRecvArray;
	CSsaStringArray tempArray;
	CString sendString;
#endif
	int i = 0;
#if 0
	int tempInt;
	/////////////////////////////////////////////////////////////
	// build the data to send to the Forte connection server
	/////////////////////////////////////////////////////////////
	sendString = "<SIO>SLOTSocketString\n";
	tempSendArray.Add(sendString);
	sendString.Format("<SAI>%s\n",fileName);
	tempSendArray.Add(sendString);
	sendString = "<EIO>SLOTSocketString\n";
	tempSendArray.Add(sendString);
	
	forteService.SendToForteConnection(tempSendArray,tempRecvArray,
		CString("SLOTSocketString"), 10310);

	for (i=0; i < tempRecvArray.GetSize(); i++) {
		if ( tempRecvArray[i].Find("<SAI>") != -1 ) {
			tempString = tempRecvArray[i].Mid(5);
			tempInt = tempString.GetLength();
			if (tempString.GetAt(tempInt-1) == '\n' ) {
				tempString.SetAt(tempInt-1,' ');
				tempString.TrimRight();
				AfxMessageBox(tempString);
			}
		}
	}
#else
	string fileNameTmp = (LPCTSTR)fileName;
	string	res = getSessionMgrSO()->ImportFacilityHelper(fileNameTmp);
	tempString = res.c_str();
	int idx = tempString.Find("|");
	origName = tempString.Left(idx);
	newName = tempString.Mid(idx+1);
#endif
	return 0;
}

void CFacilityHelper::DoForteSave()
{
	try {
		if (bTreeHelper.SaveFacility(changesTree, 0) < 0) {
			AfxMessageBox("Unable to save the facility to the database.  "
				"Please contact support.");
			return;
		}
	}
	catch (Ssa_Exception e) {
		utilityHelper.ProcessError("Error saving facility to database.", &e);
	}
	catch (...) {
		utilityHelper.ProcessError("Error saving facility to database.");
	}

	return;

}


BOOL CFacilityHelper::FacilityIsModified()
{
	// We don't really care about the drawing in this function - we are only
	// concerned about the database
	if (numItemsProcessed > 0)
		return TRUE;


	return FALSE;
}

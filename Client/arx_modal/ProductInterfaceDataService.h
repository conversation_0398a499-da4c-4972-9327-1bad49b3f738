// ProductInterfaceDataService.h: interface for the CProductInterfaceDataService class.
//
//////////////////////////////////////////////////////////////////////

#if !defined(AFX_PRODUCTINTERFACEDATASERVICE_H__484E1461_88B8_4284_B9B7_C79DB3C8E01B__INCLUDED_)
#define AFX_PRODUCTINTERFACEDATASERVICE_H__484E1461_88B8_4284_B9B7_C79DB3C8E01B__INCLUDED_

#if _MSC_VER > 1000
#pragma once
#endif // _MSC_VER > 1000

class CProductInterfaceDataService  
{
public:
	CProductInterfaceDataService();
	virtual ~CProductInterfaceDataService();
	int ProductInbound(CStringArray &records);
};

#endif // !defined(AFX_PRODUCTINTERFACEDATASERVICE_H__484E1461_88B8_4284_B9B7_C79DB3C8E01B__INCLUDED_)

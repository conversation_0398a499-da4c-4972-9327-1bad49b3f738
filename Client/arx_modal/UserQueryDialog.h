#if !defined(AFX_USERQUERYDIALOG_H__CBE25894_9AFE_11D4_9EB9_00C04FAC149C__INCLUDED_)
#define AFX_USERQUERYDIALOG_H__CBE25894_9AFE_11D4_9EB9_00C04FAC149C__INCLUDED_

#if _MSC_VER > 1000
#pragma once
#endif // _MSC_VER > 1000
// UserQueryDialog.h : header file
//

/////////////////////////////////////////////////////////////////////////////
// CUserQueryDialog dialog
#include "UserQuery.h"
#include "Resource.h"
#include "UserQueryDataService.h"

class CUserQueryDialog : public CDialog
{
// Construction
public:
	int m_QueryCount;
	CUserQueryDialog(CWnd* pParent = NULL);   // standard constructor
	~CUserQueryDialog();

// Dialog Data
	//{{AFX_DATA(CUserQueryDialog)
	enum { IDD = IDD_USER_QUERY };
	CComboBox	m_QueryListBox;
	//}}AFX_DATA


// Overrides
	// ClassWizard generated virtual function overrides
	//{{AFX_VIRTUAL(CUserQueryDialog)
	protected:
	virtual void DoDataExchange(CDataExchange* pDX);    // DDX/DDV support
	//}}AFX_VIRTUAL

// Implementation
protected:

	// Generated message map functions
	//{{AFX_MSG(CUserQueryDialog)
	virtual BOOL OnInitDialog();
	afx_msg void OnSelchangeQueryList();
	afx_msg void OnRun();
	afx_msg void OnRButtonDblClk(UINT nFlags, CPoint point);
	afx_msg void OnUpdate();
	afx_msg void OnEditchangeQueryList();
	afx_msg void OnExport();
	afx_msg void OnImport();
	afx_msg void OnDelete();
	afx_msg BOOL OnHelpInfo(HELPINFO* pHelpInfo);
	afx_msg void OnHelp();
	//}}AFX_MSG
	DECLARE_MESSAGE_MAP()
private:
	CUserQuery *m_QueryArray;
	int GetParameters(CString &query);
	BOOL m_SuperUser;
	CUserQueryDataService m_UserQueryDataService;
};

//{{AFX_INSERT_LOCATION}}
// Microsoft Visual C++ will insert additional declarations immediately before the previous line.

#endif // !defined(AFX_USERQUERYDIALOG_H__CBE25894_9AFE_11D4_9EB9_00C04FAC149C__INCLUDED_)

// SideProfileDataService.cpp: implementation of the CSideProfileDataService class.
//
//////////////////////////////////////////////////////////////////////

#include "stdafx.h"
#include "modal.h"
#include "SideProfileDataService.h"
#include "BayProfileDataService.h"
#include "DataAccessService.h"
#include "ForteService.h"
#include "ssa_exception.h"
#include "UtilityHelper.h"

#ifdef _DEBUG
#undef THIS_FILE
static char THIS_FILE[]=__FILE__;
#define new DEBUG_NEW
#endif

extern CForteService forteService;
extern CDataAccessService dataAccessService;
extern CUtilityHelper utilityHelper;

//////////////////////////////////////////////////////////////////////
// Construction/Destruction
//////////////////////////////////////////////////////////////////////

CSideProfileDataService::CSideProfileDataService()
{

}

CSideProfileDataService::~CSideProfileDataService()
{

}


int CSideProfileDataService::GetSideProfileNameList(CStringArray &sideNameList) 
{
	CString tempString;
	CSsaStringArray tempSendArray;
	CSsaStringArray tempRecvArray;
	CString sendString;
	int i;
	int tempInt;

	/////////////////////////////////////////////////////////////
	// Build the data and send to Forte connection
	/////////////////////////////////////////////////////////////
	sendString = "<SIO>SLOTSocketString\n";
	tempSendArray.Add(sendString);

	tempSendArray.Add(CString("<SAI>GetList\n"));

	sendString = "<EIO>SLOTSocketString\n";
	tempSendArray.Add(sendString);

	try {
		forteService.SendToForteConnection(tempSendArray,tempRecvArray,
			CString("SLOTSocketString"), 9020);
	}
	catch(...) {
		return -1;
	}

	for (i=0; i < tempRecvArray.GetSize(); i++) {
		if ( tempRecvArray[i].Find("<SAI>") != -1 ) {
			tempString = tempRecvArray[i].Mid(5);
			tempInt = tempString.GetLength();
			if (tempString.GetAt(tempInt-1) == '\n' ) {
				tempString.SetAt(tempInt-1,' ');
				tempString.TrimRight();
			}
			sideNameList.Add(tempString);
		}
	}
	return 0;
}



BOOL CSideProfileDataService::IsSideProfileNameInUse(int dbid, const CString &name)
{
	CString sql;
	CStringArray results;

	sql.Format("select count(*) from dbsideprofile "
		"where description = '%s' "
		"and dbsideprofileid != %d", name, dbid);
	
	dataAccessService.ExecuteQuery("IsSideProfileNameInuse", sql, results, TRUE);

	if (results.GetSize() > 0)
		return (atoi(results[0]) != 0);

	return FALSE;
}

int CSideProfileDataService::UpdateSideProfileName(int sideProfileDBId, const CString &name)
{
	CString sql;

	sql.Format("update dbsideprofile set description = '%s' where dbsideprofileid = %d",
		name, sideProfileDBId);

	return dataAccessService.ExecuteStatement("UpdateSideProfileName", sql);
}


int CSideProfileDataService::GetSideProfileList(CStringArray &sideNameList)
{
	CString sql;

	sql.Format("select dbsideprofileid, description "
		"from dbsideprofile "
		"order by description");

	return dataAccessService.ExecuteQuery("GetSideProfileList", sql, sideNameList);
}

int CSideProfileDataService::GetSideProfile(int sideProfileDBId, CSideProfile& sideProfile, int loadBayOptions)
{
	CString sql;
	CStringArray results;

	sql.Format("select dbsideprofileid, description, sidetotalwidth, "
		"maximumbaydepth, maximumbayheight "
		"from dbsideprofile "
		"where dbsideprofileid = %d", sideProfileDBId);
	
	dataAccessService.ExecuteQuery("GetSideProfile", sql, results);
	if (results.GetSize() == 0)
		return -1;
	sideProfile.Parse(results[0]);
	
	results.RemoveAll();

	sql.Format("select dbbayprofile.dbbayprofileid, dbbayprofile.description "
		"from dbsidebayprof, dbbayprofile "
		"where dbsidebayprof.dbsideprofileid = %d "
		"and dbsidebayprof.dbbayprofileid = dbbayprofile.dbbayprofileid "
		"order by dbsidebayprof.dbsidebayprofid", sideProfileDBId);
	dataAccessService.ExecuteQuery("GetSideBayProfile", sql, results, TRUE);

	CBayProfileDataService bayProfileDataService;
	CMap<int, int, CBayProfile*, CBayProfile*> map;

	for (int i=0; i < results.GetSize(); ++i) {
		CBayProfile *pBayProfile = new CBayProfile;
		pBayProfile->Parse(results[i]);
		sideProfile.m_BayProfileList.Add(pBayProfile);
		CBayProfile *pTempProfile;
		if (map.Lookup(pBayProfile->m_BayProfileDBId, pTempProfile))
			*pBayProfile = *pTempProfile;
		else {
			bayProfileDataService.GetBayProfile(pBayProfile->m_BayProfileDBId, *pBayProfile,
				loadBayOptions);
			map.SetAt(pBayProfile->m_BayProfileDBId, pBayProfile);
		}
	}

	return 0;
}

int CSideProfileDataService::DeleteSideProfile(int sideProfileDBId)
{
	CString sql;
	CStringArray stmts;

	sql.Format("delete from dbsidebayprof where dbsideprofileid = %d", sideProfileDBId);
	stmts.Add(sql);
	sql.Format("delete from dbsideprofile where dbsideprofileid = %d", sideProfileDBId);
	stmts.Add(sql);

	return dataAccessService.ExecuteStatements("DeleteSideProfile", stmts);
}

int CSideProfileDataService::StoreSideProfile(CSideProfile& sideProfile)
{
	CString sql;
	CStringArray stmts;

	if (sideProfile.m_SideProfileDBId <= 0) {
		sideProfile.m_SideProfileDBId = dataAccessService.GetNextKey("DBSideProfile", 1);
		sql.Format("insert into dbsideprofile (dbsideprofileid, description, "
			"sidetotalwidth, maximumbaydepth, maximumbayheight, "
			"createdate, changedate, lastuserid) "
			"values (%d, '%s', %f, %f, %f, sysdate, sysdate, 1)",
			sideProfile.m_SideProfileDBId, sideProfile.m_Description,
			sideProfile.m_TotalLength, sideProfile.m_MaximumBayDepth,
			sideProfile.m_MaximumBayHeight);
		stmts.Add(sql);
		
		int nextKey = dataAccessService.GetNextKey("DBSideBayProf", sideProfile.m_BayProfileList.GetSize());

		for (int i=0; i < sideProfile.m_BayProfileList.GetSize(); ++i) {
			sql.Format("insert into dbsidebayprof (dbsidebayprofid, "
				"createdate, changedate, lastuserid, dbsideprofileid, dbbayprofileid) "
				"values (%d, sysdate, sysdate, 1, %d, %d)",
				nextKey++, sideProfile.m_SideProfileDBId, sideProfile.m_BayProfileList[i]->m_BayProfileDBId);
			stmts.Add(sql);
		}
	}
	else {		// update
		sql.Format("update dbsideprofile set description = '%s', "
			"sidetotalwidth = %f, maximumbaydepth = %f, maximumbayheight = %f "
			"where dbsideprofileid = %d", sideProfile.m_Description,
			sideProfile.m_TotalLength, sideProfile.m_MaximumBayDepth, 
			sideProfile.m_MaximumBayHeight, sideProfile.m_SideProfileDBId);
		stmts.Add(sql);

		sql.Format("delete from dbsidebayprof where dbsideprofileid = %d", sideProfile.m_SideProfileDBId);
		stmts.Add(sql);

		int nextKey = dataAccessService.GetNextKey("DBSideBayProf", sideProfile.m_BayProfileList.GetSize());

		for (int i=0; i < sideProfile.m_BayProfileList.GetSize(); ++i) {
			sql.Format("insert into dbsidebayprof (dbsidebayprofid, "
				"createdate, changedate, lastuserid, dbsideprofileid, dbbayprofileid) "
				"values (%d, sysdate, sysdate, 1, %d, %d)",
				nextKey++, sideProfile.m_SideProfileDBId, sideProfile.m_BayProfileList[i]->m_BayProfileDBId);
			stmts.Add(sql);
		}
	}

	return dataAccessService.ExecuteStatements("StoreSideProfile", stmts);
}


int CSideProfileDataService::GetAisleProfileNamesBySideProfile(int sideProfileDBId, CStringArray &aisleNames)
{	
	CString sql;

	sql.Format("select unique dbaisleprofile.description from dbaisleprofile, dbaislesideprof "
		"where dbaisleprofile.dbaisleprofileid = dbaislesideprof.dbaisleprofileid "
		"and dbaislesideprof.dbsideprofileid = %d", sideProfileDBId);

	return dataAccessService.ExecuteQuery("GetAisleProfileNamesBySideProfile", sql, aisleNames, TRUE);
}

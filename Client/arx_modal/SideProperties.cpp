// SideProperties.cpp : implementation file
//

#include "stdafx.h"
#include "SideProperties.h"
#include "HelpService.h"

#ifdef _DEBUG
#define new DEBUG_NEW
#undef THIS_FILE
static char THIS_FILE[] = __FILE__;
#endif

extern CHelpService helpService;

/////////////////////////////////////////////////////////////////////////////
// CSideProperties property page

IMPLEMENT_DYNCREATE(CSideProperties, CPropertyPage)

CSideProperties::CSideProperties() : CPropertyPage(CSideProperties::IDD)
{
	//{{AFX_DATA_INIT(CSideProperties)
	m_Description = _T("");
	//}}AFX_DATA_INIT
}

CSideProperties::~CSideProperties()
{
}

void CSideProperties::DoDataExchange(CDataExchange* pDX)
{
	CPropertyPage::DoDataExchange(pDX);
	//{{AFX_DATA_MAP(CSideProperties)
	DDX_Text(pDX, IDC_DESCRIPTION, m_Description);
	//}}AFX_DATA_MAP
}


BEGIN_MESSAGE_MAP(CSideProperties, CPropertyPage)
	//{{AFX_MSG_MAP(CSideProperties)
	ON_WM_HELPINFO()
	//}}AFX_MSG_MAP
END_MESSAGE_MAP()

/////////////////////////////////////////////////////////////////////////////
// CSideProperties message handlers

BOOL CSideProperties::OnNotify(WPARAM wParam, LPARAM lParam, LRESULT* pResult) 
{
	NMHDR* pNMHDR = (NMHDR*)lParam;
	if (pNMHDR->code == PSN_HELP) {
		helpService.ShowScreenHelp(IDD);
		return TRUE;
	}		
	return CPropertyPage::OnNotify(wParam, lParam, pResult);
}

BOOL CSideProperties::OnHelpInfo(HELPINFO* pHelpInfo) 
{
	helpService.ShowFieldHelp(IDD, pHelpInfo->iCtrlId);
	return FALSE;
}

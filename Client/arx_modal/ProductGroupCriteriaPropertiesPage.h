#if !defined(AFX_PRODUCTGROUPCRITERIAPROPERTIESPAGE_H__C027A261_0456_11D5_9EC8_00C04FAC149C__INCLUDED_)
#define AFX_PRODUCTGROUPCRITERIAPROPERTIESPAGE_H__C027A261_0456_11D5_9EC8_00C04FAC149C__INCLUDED_

#include "ProductGroupCriteria.h"	// Added by ClassView
#include "ProductAttribute.h"
#include "ProductGroupCriteriaListPage.h"	// Added by ClassView
#include "ProductGroupDataService.h"	// Added by ClassView

#if _MSC_VER > 1000
#pragma once
#endif // _MSC_VER > 1000
// ProductGroupCriteriaPropertiesPage.h : header file
//

/////////////////////////////////////////////////////////////////////////////
// CProductGroupCriteriaPropertiesPage dialog

class CProductGroupCriteriaPropertiesPage : public CPropertyPage
{
	DECLARE_DYNCREATE(CProductGroupCriteriaPropertiesPage)

// Construction
public:
	CProductGroupDataService *m_ProductGroupDataService;
	CProductGroupCriteriaListPage *m_ListPage;
	CProductGroupCriteria *m_Criteria;
	CProductGroupCriteriaPropertiesPage();
	~CProductGroupCriteriaPropertiesPage();
	

// Dialog Data
	//{{AFX_DATA(CProductGroupCriteriaPropertiesPage)
	enum { IDD = IDD_CRITERIA_PROPERTIES };
	CEdit	m_FormulaCtrl;
	CButton	m_FormulaCheckCtrl;
	CEdit	m_DescriptionCtrl;
	CEdit	m_NameCtrl;
	CComboBox	m_AttributeCtrl;
	CString	m_Attribute;
	CString	m_Description;
	CString	m_Name;
	CString	m_Formula;
	BOOL	m_ListCheckBox;
	//}}AFX_DATA


// Overrides
	// ClassWizard generate virtual function overrides
	//{{AFX_VIRTUAL(CProductGroupCriteriaPropertiesPage)
	public:
	virtual void OnOK();
	virtual BOOL OnKillActive();
	protected:
	virtual void DoDataExchange(CDataExchange* pDX);    // DDX/DDV support
	virtual BOOL OnNotify(WPARAM wParam, LPARAM lParam, LRESULT* pResult);
	//}}AFX_VIRTUAL

// Implementation
protected:
	// Generated message map functions
	//{{AFX_MSG(CProductGroupCriteriaPropertiesPage)
	virtual BOOL OnInitDialog();
	afx_msg void OnSelchangeAttribute();
	afx_msg void OnEditchangeAttribute();
	afx_msg void OnFormulaCheck();
	afx_msg void OnChangeFormula();
	afx_msg void OnValidateFormula();
	afx_msg BOOL OnHelpInfo(HELPINFO* pHelpInfo);
	afx_msg void OnList();
	//}}AFX_MSG
	DECLARE_MESSAGE_MAP()

private:
	void ClearList();
	int m_PreviousSelection;
};

//{{AFX_INSERT_LOCATION}}
// Microsoft Visual C++ will insert additional declarations immediately before the previous line.

#endif // !defined(AFX_PRODUCTGROUPCRITERIAPROPERTIESPAGE_H__C027A261_0456_11D5_9EC8_00C04FAC149C__INCLUDED_)

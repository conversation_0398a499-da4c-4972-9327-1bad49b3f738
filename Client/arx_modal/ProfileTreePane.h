#if !defined(AFX_PROFILETREEPANE_H__6AB297A6_9B37_47E5_8E9D_AE695129083B__INCLUDED_)
#define AFX_PROFILETREEPANE_H__6AB297A6_9B37_47E5_8E9D_AE695129083B__INCLUDED_

#if _MSC_VER > 1000
#pragma once
#endif // _MSC_VER > 1000
// ProfileTreePane.h : header file
//

/////////////////////////////////////////////////////////////////////////////
// CProfileTreePane dialog

class CProfileTreePane : public CDialog
{
// Construction
	DECLARE_DYNCREATE(CProfileTreePane)

public:
	BOOL Create(LPCTSTR lpszClassName, LPCTSTR lpszWindowName, DWORD dwStyle, const RECT& rect, CWnd* pParentWnd, UINT nID, CCreateContext* pContext);
	CProfileTreePane(CWnd* pParent = NULL);   // standard constructor

// Dialog Data
	//{{AFX_DATA(CProfileTreePane)
	enum { IDD = IDD_PROFILE_TREE_PANE };
		// NOTE: the ClassWizard will add data members here
	//}}AFX_DATA


// Overrides
	// ClassWizard generated virtual function overrides
	//{{AFX_VIRTUAL(CProfileTreePane)
	protected:
	virtual void DoDataExchange(CDataExchange* pDX);    // DDX/DDV support
	//}}AFX_VIRTUAL

// Implementation
protected:

	// Generated message map functions
	//{{AFX_MSG(CProfileTreePane)
	afx_msg void OnSize(UINT nType, int cx, int cy);
	//}}AFX_MSG
	DECLARE_MESSAGE_MAP()
};

//{{AFX_INSERT_LOCATION}}
// Microsoft Visual C++ will insert additional declarations immediately before the previous line.

#endif // !defined(AFX_PROFILETREEPANE_H__6AB297A6_9B37_47E5_8E9D_AE695129083B__INCLUDED_)

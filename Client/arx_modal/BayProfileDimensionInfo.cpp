// BayProfileDimensionInfo.cpp: implementation of the CBayProfileDimensionInfo class.
//
//////////////////////////////////////////////////////////////////////

#include "stdafx.h"
#include "modal.h"
#include "BayProfileDimensionInfo.h"

#ifdef _DEBUG
#undef THIS_FILE
static char THIS_FILE[]=__FILE__;
#define new DEBUG_NEW
#endif

//////////////////////////////////////////////////////////////////////
// Construction/Destruction
//////////////////////////////////////////////////////////////////////

CBayProfileDimensionInfo::CBayProfileDimensionInfo()
{
	m_BayType = 0;
	m_BayWidth = 0;
	m_BayDepth = 0;
	m_BayHeight = 0;
	m_PalletHeight = 0;
	m_PositionsDeep = 0;
	m_FlowDifference = 0;
	m_UprightWidth = 0;
	m_UprightHeight = 0;
	m_SelectPositions = 0;
	m_ReservePositions = 0;
	m_SelectPositionHeight = 0;
	m_ReservePositionHeight = 0;
	m_StackWidth = 0;
	m_StackDepth = 0;
	m_Overhang = 0;
	m_PalletSpace = 0;
	m_Clearance = 0;
}

CBayProfileDimensionInfo::~CBayProfileDimensionInfo()
{

}

void CBayProfileDimensionInfo::operator=(const CBayProfileDimensionInfo& other)
{
	m_BayType = other.m_BayType;
	m_BayWidth = other.m_BayWidth;
	m_BayDepth = other.m_BayDepth;
	m_BayHeight = other.m_BayHeight;
	m_PalletHeight = other.m_PalletHeight;
	m_PositionsDeep = other.m_PositionsDeep;
	m_FlowDifference = other.m_FlowDifference;
	m_UprightWidth = other.m_UprightWidth;
	m_UprightHeight = other.m_UprightHeight;
	m_SelectPositions = other.m_SelectPositions;
	m_ReservePositions = other.m_ReservePositions;
	m_SelectPositionHeight = other.m_SelectPositionHeight;
	m_ReservePositionHeight = other.m_ReservePositionHeight;
	m_StackWidth = other.m_StackWidth;
	m_StackDepth = other.m_StackDepth;
	m_Overhang = other.m_Overhang;
	m_PalletSpace = other.m_PalletSpace;
	m_Clearance = other.m_Clearance;
	
}
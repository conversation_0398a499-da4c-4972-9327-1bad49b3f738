// SearchAnchor.cpp: implementation of the CSearchAnchor class.
//
//////////////////////////////////////////////////////////////////////

#include "stdafx.h"
#include "modal.h"
#include "SearchAnchor.h"
#include "dbsymtb.h"

#ifdef _DEBUG
#undef THIS_FILE
static char THIS_FILE[]=__FILE__;
#define new DEBUG_NEW
#endif

//////////////////////////////////////////////////////////////////////
// Construction/Destruction
//////////////////////////////////////////////////////////////////////

CSearchAnchor::CSearchAnchor()
{
	m_SearchAnchorDBID = -1;
	m_StartingLocation = "";
	m_EndingLocation = "";
	m_SearchAnchorPoint = "";
}

CSearchAnchor::~CSearchAnchor()
{

}

int CSearchAnchor::Parse(CString &line)
{
	char *str, *ptr;
	CString tmpLine;

	try {
		line.Replace("||", "| |");
		line.Replace("||", "| |");
	
		tmpLine = line;
		str = tmpLine.GetBuffer(0);
		ptr = strtok(str, "|");

		m_StartingLocation = ptr;
		m_StartingLocation.TrimRight();
		ptr = strtok(NULL, "|");
		m_EndingLocation = ptr;
		m_EndingLocation.TrimRight();
		ptr = strtok(NULL, "|");
		m_SearchAnchorPoint = ptr;
		m_SearchAnchorPoint.TrimRight();
		ptr = strtok(NULL, "|");
		m_SearchAnchorDBID = atoi(ptr);


		tmpLine.ReleaseBuffer();

	}
	catch (...) {
		tmpLine.ReleaseBuffer();
		AfxMessageBox("Error processing product list.\n");
		ads_printf("%s\n", line);
		return -1;
	}

	return 0;

}

CString CSearchAnchor::Stream()
{
	CString txt;
	txt.Format("%s|%s|%s|%d|",
		this->m_StartingLocation,
		this->m_EndingLocation,
		this->m_SearchAnchorPoint,
		this->m_SearchAnchorDBID);

	return txt;
}

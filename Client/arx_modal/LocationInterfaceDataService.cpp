// LocationInterfaceDataService.cpp: implementation of the CLocationInterfaceDataService class.
//
//////////////////////////////////////////////////////////////////////

#include "stdafx.h"
#include "modal.h"
#include "DataAccessService.h"
#include "LocationInterfaceDataService.h"

#ifdef _DEBUG
#undef THIS_FILE
static char THIS_FILE[]=__FILE__;
#define new DEBUG_NEW
#endif

//////////////////////////////////////////////////////////////////////
// Construction/Destruction
//////////////////////////////////////////////////////////////////////
extern CDataAccessService dataAccessService;

CLocationInterfaceDataService::CLocationInterfaceDataService()
{

}

CLocationInterfaceDataService::~CLocationInterfaceDataService()
{

}


int CLocationInterfaceDataService::GetLocationOutboundListBySection(int sectionID, CStringArray &locationList,
									 long productGroupID, BOOL vwOnly)
{
	CString queryText, tempString;

	/////////////////////////////////////////////////////////////
	// build the data to send to the Forte connection server
	/////////////////////////////////////////////////////////////

	queryText.Format(" select le.dblevelprofileid, l.description, l.xcoordinate, l.ycoordinate, l.zcoordinate, "
		"l.handlingmethod, l.isselect, l.isoverridden, lop.handlingmethod, lop.isselect, a.dbsectionid "
		"from dbaisle a, dbside si, dbbay b, dblevel le, dblocation l, dblocationprof lop ");
	if (productGroupID > 0)
		queryText += ", dbslotgrpbay sgb ";
	if (vwOnly)
		queryText += ", dblevelprofile lp ";

	tempString.Format("where a.dbsectionid = %d ", sectionID);
	queryText += tempString;
	queryText += "and a.dbaisleid = si.dbaisleid ";
	queryText += "and si.dbsideid = b.dbsideid ";
	queryText += "and b.dbbayid = le.dbbayid ";
	queryText += "and le.dblevelid = l.dblevelid ";
	queryText += "and lop.dblocationprofid = l.dblocationprofid ";
	if (productGroupID > 0) {
		tempString.Format("and sgb.dblevelid = le.dblevelid "
			"and sgb.dbslottinggroupid = %d ", productGroupID);
		queryText += tempString;
	}
	if (vwOnly) {
		tempString.Format("and lp.dblevelprofileid = le.dblevelprofileid "
			"and lp.isvarlocallowed = 1 ");
		queryText += tempString;
	}

	return dataAccessService.ExecuteQuery("GetLocationOutboundListBySection", queryText, locationList);
}

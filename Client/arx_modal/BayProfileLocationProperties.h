#if !defined(AFX_BAYPROFILELOCATIONPROPERTIES_H__2EF8614B_4572_42CA_AF9F_B8103000031F__INCLUDED_)
#define AFX_BAYPROFILELOCATIONPROPERTIES_H__2EF8614B_4572_42CA_AF9F_B8103000031F__INCLUDED_

#if _MSC_VER > 1000
#pragma once
#endif // _MSC_VER > 1000
// BayProfileLocationProperties.h : header file
//
#include "BayProfile.h"
/////////////////////////////////////////////////////////////////////////////
// CBayProfileLocationProperties dialog

class CBayProfileLocationProperties : public CDialog
{
// Construction
public:
	CBayProfileLocationProperties(CWnd* pParent = NULL);   // standard constructor

// Dialog Data
	//{{AFX_DATA(CBayProfileLocationProperties)
	enum { IDD = IDD_BAY_PROFILE_LOCATION_PROPERTIES };
	CComboBox	m_LocationUsageListCtrl;
	CComboBox	m_HandlingListCtrl;
	CString	m_FacingGap;
	CString	m_FacingSnap;
	CString	m_MinimumWidth;
	CString	m_ProductGap;
	CString	m_ProductSnap;
	CString	m_LocationSpace;
	CString	m_LocationsAcross;
	CString	m_LocationsDeep;
	//}}AFX_DATA


// Overrides
	// ClassWizard generated virtual function overrides
	//{{AFX_VIRTUAL(CBayProfileLocationProperties)
	protected:
	virtual void DoDataExchange(CDataExchange* pDX);    // DDX/DDV support
	//}}AFX_VIRTUAL

// Implementation
public:
	CBayProfile *m_pBayProfile;
	int m_CurrentLevel;
protected:

	// Generated message map functions
	//{{AFX_MSG(CBayProfileLocationProperties)
	virtual BOOL OnInitDialog();
	virtual void OnOK();
	afx_msg void OnHelp();
	afx_msg BOOL OnHelpInfo(HELPINFO* pHelpInfo);
	//}}AFX_MSG
	DECLARE_MESSAGE_MAP()

private:
	BOOL ValidateNumeric(int type, int fieldId, const CString &fieldName, CString &fieldValue);
	void BuildLocationUsageList();
	void BuildHandlingList();
	void SetControlStates();
};

//{{AFX_INSERT_LOCATION}}
// Microsoft Visual C++ will insert additional declarations immediately before the previous line.

#endif // !defined(AFX_BAYPROFILELOCATIONPROPERTIES_H__2EF8614B_4572_42CA_AF9F_B8103000031F__INCLUDED_)

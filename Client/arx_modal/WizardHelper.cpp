// WizardHelper.cpp: implementation of the CWizardHelper class.
//
//////////////////////////////////////////////////////////////////////

#include "stdafx.h"
#include <adscodes.h>

#include "modal.h"
#include "WizardHelper.h"
#include "AutoCADCommands.h"
#include "ResourceHelper.h"

#include "SideProfileDataService.h"
#include "AisleProfileDataService.h"
#include "BayProfileDataService.h"
#include "BayProfileSheet.h"
#include "SideProfileSheet.h"
#include "AisleProfileSheet.h"
#include "FacilityHelper.h"
#include "NavigationHelper.h"

#include "ControlService.h"
#include "UtilityHelper.h"

#include "ProfileFrame.h"
#include "ProfileMaintenanceSheet.h"
#include "ProfilePage.h"

#include "BayProfileListDialog.h"
#include "SideProfileListDialog.h"

#include "ProcessingMessage.h"

#ifdef _DEBUG
#undef THIS_FILE
static char THIS_FILE[]=__FILE__;
#define new DEBUG_NEW
#endif

CProfileFrame *m_ProfileFrame;

extern CSideProfileDataService sideProfileDataService;
extern CBayProfileDataService bayProfileDataService;
extern CAisleProfileDataService aisleProfileDataService;

extern CUtilityHelper utilityHelper;
extern CControlService controlService;
//////////////////////////////////////////////////////////////////////
// Construction/Destruction
//////////////////////////////////////////////////////////////////////

CWizardHelper::CWizardHelper()
{

}

CWizardHelper::~CWizardHelper()
{

}


void CWizardHelper::ProfileMaintenance()
{

	HWND hWnd = adsw_acadMainWnd(); 
	CWnd *parent;
	parent = CWnd::FromHandle(hWnd);


	m_ProfileFrame = new CProfileFrame;
//	m_ProductGroupFrame->LoadFrame(IDR_DUMMY, WS_OVERLAPPED|WS_CAPTION|WS_SYSMENU, parent, NULL);

	CRect r(0, 0, 850, 600);
	m_ProfileFrame->Create(NULL, "Profile Maintenance", WS_OVERLAPPED|WS_CAPTION|WS_SYSMENU, r,
		parent, NULL, 0, NULL);

	m_ProfileFrame->SetWindowPos(parent, 0, 0, 0, 0, SWP_NOMOVE|SWP_NOSIZE);

	m_ProfileFrame->CenterWindow();
	m_ProfileFrame->ShowWindow(SW_SHOW);
	m_ProfileFrame->UpdateWindow();
	

}


void CWizardHelper::BayProfileWizard()
{
	CTemporaryResourceOverride tro;

	if (controlService.m_pBayProfileListDialog == NULL) {
		controlService.m_pBayProfileListDialog = new CBayProfileListDialog;
		controlService.m_pBayProfileListDialog->m_IsModeless = FALSE;
		/*
		HWND hWnd = adsw_acadMainWnd(); 
		CWnd *parent;
		parent = CWnd::FromHandle(hWnd);
		controlService.m_pBayProfileListDialog->Create(IDD_BAY_PROFILE_LIST, parent);
		*/
	}
	
	//acedCommand(RTSTR, "_VPOINT", RTSTR, "-1,-1,1", RTNONE);

	CBayProfileListDialog dlg;
	try
	{
		int rc = controlService.m_pBayProfileListDialog->DoModal();
		if (rc == WM_VIEW_DRAWING) {
		
			CFacilityHelper facHelper;
			facHelper.RunNewFacilityScript("ViewBayProfileDrawing");

			return;
		}
		else {
			delete controlService.m_pBayProfileListDialog;
			controlService.m_pBayProfileListDialog = NULL;
			if (rc == WM_SIDE_WIZARD)
				SideProfileWizard();
			else if (rc == WM_AISLE_WIZARD)
				AisleProfileWizard();
			else if (rc == WM_MAIN_WIZARD) {
				CNavigationHelper navHelper;
				navHelper.ShowWizard();
			}
		}
		//controlService.m_pBayProfileListDialog->ShowWindow(SW_SHOW);	
	}
	catch (CException *e)
	{
		char strMsg[4096];
		e->GetErrorMessage(strMsg, 4096);
		e->Delete();
		utilityHelper.ProcessError(strMsg);
		utilityHelper.ProcessError("Error running bay wizard");
		delete controlService.m_pBayProfileListDialog;
		controlService.m_pBayProfileListDialog = NULL;
	}
	catch (...) {
		utilityHelper.ProcessError("Error running bay wizard");
		delete controlService.m_pBayProfileListDialog;
		controlService.m_pBayProfileListDialog = NULL;
	}

	return;
}


void CWizardHelper::SideProfileWizard()
{
	CTemporaryResourceOverride tro;

	if (controlService.m_pSideProfileListDialog == NULL) {
		controlService.m_pSideProfileListDialog = new CSideProfileListDialog;
		//controlService.m_pSideProfileListDialog->m_IsModeless = FALSE;
	}
	
	try {
		int rc = controlService.m_pSideProfileListDialog->DoModal();
		if (rc == WM_VIEW_DRAWING) {
			CFacilityHelper facHelper;
			facHelper.RunNewFacilityScript("ViewSideProfileDrawing");
			return;
		}
		else {
			delete controlService.m_pSideProfileListDialog;
			controlService.m_pSideProfileListDialog = NULL;
			if (rc == WM_BAY_WIZARD)
				BayProfileWizard();
			else if (rc == WM_AISLE_WIZARD)
				AisleProfileWizard();
			else if (rc == WM_MAIN_WIZARD) {
				CNavigationHelper navHelper;
				navHelper.ShowWizard();
			}
		}
	}
	catch (...) {
		utilityHelper.ProcessError("Error running side wizard.");
		delete controlService.m_pSideProfileListDialog;
		controlService.m_pSideProfileListDialog = NULL;
	}

	return;
}

void CWizardHelper::AisleProfileWizard()
{
	CTemporaryResourceOverride tro;

	if (controlService.m_pAisleProfileListDialog == NULL) {
		controlService.m_pAisleProfileListDialog = new CAisleProfileListDialog;
		//controlService.m_pAisleProfileListDialog->m_IsModeless = FALSE;
	}
	
	try {
		int rc = controlService.m_pAisleProfileListDialog->DoModal();
		if (rc == WM_VIEW_DRAWING) {
			CFacilityHelper facHelper;
			facHelper.RunNewFacilityScript("ViewAisleProfileDrawing");

			return;
		}
		else {
			delete controlService.m_pAisleProfileListDialog;
			controlService.m_pAisleProfileListDialog = NULL;
			if (rc == WM_BAY_WIZARD)
				BayProfileWizard();
			else if (rc == WM_SIDE_WIZARD)
				SideProfileWizard();
			else if (rc == WM_MAIN_WIZARD) {
				CNavigationHelper navHelper;
				navHelper.ShowWizard();
			}
		}
	}
	catch (...) {
		utilityHelper.ProcessError("Error running aisle wizard.");
		delete controlService.m_pAisleProfileListDialog;
		controlService.m_pAisleProfileListDialog = NULL;
	}

	return;
}

void CWizardHelper::ViewBayProfileDrawing()
{
	if (controlService.m_pBayProfileListDialog == NULL)
		return;
	
	controlService.SetMode(IDC_USEWIZARD);


	try {
		controlService.m_pBayProfileListDialog->m_pBayProfile->DrawByPosition(acdbCurDwg(), C3DPoint(0,0,0), 90);
	}
	catch (...) {
		AfxMessageBox("Error displaying bay profile.");
		delete controlService.m_pBayProfileListDialog;
		controlService.m_pBayProfileListDialog = NULL;
	}

	ads_command(RTSTR, "_VPOINT", RTSTR, "-1,-1,1", RTNONE);
	//char dummy[256];
	if (AfxMessageBox("Return to Bay Wizard?", MB_YESNO|MB_ICONQUESTION) == IDYES) {
	//if (ads_getstring(1, "Press return to continue...", dummy) == RTNORM) {
		ads_printf("\n");
		BayProfileWizard();
	}
	else {
		delete controlService.m_pBayProfileListDialog;
		controlService.m_pBayProfileListDialog = NULL;
		ads_printf("\n");
	}
}

void CWizardHelper::ViewSideProfileDrawing()
{
	if (controlService.m_pSideProfileListDialog == NULL)
		return;

	controlService.SetMode(IDC_USEWIZARD);
	
	try {
		controlService.m_pSideProfileListDialog->m_pSideProfile->DrawByPosition(acdbCurDwg(), C3DPoint(0,0,0), 90, 0);
	}
	catch (...) {
		AfxMessageBox("Error displaying side profile.");
		delete controlService.m_pSideProfileListDialog;
		controlService.m_pSideProfileListDialog = NULL;
		return;
	}

	ads_command(RTSTR, "_VPOINT", RTSTR, "-1,-1,1", RTNONE);
	//char dummy[256];
	if (AfxMessageBox("Return to Side Wizard?", MB_YESNO|MB_ICONQUESTION) == IDYES) {
	//if (ads_getstring(1, "Press return to continue...", dummy) == RTNORM) {
		ads_printf("\n");
		SideProfileWizard();
	}
	else {
		delete controlService.m_pSideProfileListDialog;
		controlService.m_pSideProfileListDialog = NULL;
		ads_printf("\n");
	}
}

void CWizardHelper::ViewAisleProfileDrawing()
{
	if (controlService.m_pAisleProfileListDialog == NULL)
		return;
	
	controlService.SetMode(IDC_USEWIZARD);

	try {
		controlService.m_pAisleProfileListDialog->m_pAisleProfile->DrawByPosition(acdbCurDwg(), C3DPoint(0,0,0), 0);
	}
	catch (...) {
		AfxMessageBox("Error displaying aisle profile.");
		delete controlService.m_pAisleProfileListDialog;
		controlService.m_pAisleProfileListDialog = NULL;
		return;
	}

	ads_command(RTSTR, "_VPOINT", RTSTR, "-1,-1,1", RTNONE);
	//char dummy[256];
	//if (ads_getstring(1, "Press return to continue...", dummy) == RTNORM) {
	if (AfxMessageBox("Return to Aisle Wizard?", MB_YESNO|MB_ICONQUESTION) == IDYES) {
		ads_printf("\n");
		AisleProfileWizard();
	}
	else {
		delete controlService.m_pAisleProfileListDialog;
		controlService.m_pAisleProfileListDialog = NULL;
		ads_printf("\n");
	}
}

int CWizardHelper::ShowBayWizard(CBayProfile *pBayProfile)
{
	int rc;
	CTemporaryResourceOverride tro;

	if (pBayProfile->m_LevelProfileList.GetSize() == 0 ||
		pBayProfile->m_BayRuleList.GetSize() == 0) {	// hasn't been loaded yet
		try {
			CWaitCursor cwc;
			bayProfileDataService.GetBayProfile(pBayProfile->m_BayProfileDBId, *pBayProfile);
		}
		catch (...) {
			AfxMessageBox("Error loading bay profile.");
			return -1;
		}
	}

	CBayProfile tempBayProfile;
	tempBayProfile = *pBayProfile;

	CBayProfileSheet sheet(tempBayProfile.m_BayType, "Bay Profile");
	sheet.m_psh.dwFlags |= PSH_NOAPPLYNOW;
	sheet.m_pBayProfile = &tempBayProfile;
	CString bayTypeStr = tempBayProfile.ConvertBayType();
	CString temp;
	temp.Format("Bay Profile Maintenance - [%s\\%s] [%s]",
		bayTypeStr, tempBayProfile.m_Description, (tempBayProfile.m_Active) ? "Active" : "Inactive");
	sheet.SetTitle(temp);
	
	BOOL done = FALSE;
	
	while (! done) {
		rc = sheet.DoModal();
		done = TRUE;
		
		if (rc != IDCANCEL && tempBayProfile != *pBayProfile) {
			try {
				CProcessingMessage msgDlg("Saving Bay Profile", &sheet);
				CWaitCursor cwc;

				// For new profiles, add rule records if necessary
				if (pBayProfile->m_BayProfileDBId <= 0) {
					if (pBayProfile->ResetRules())
						// If a rule was added, recalcuate the extended values
						pBayProfile->CalculateExtendedValues(FALSE);
				}

				bayProfileDataService.StoreBayProfile(tempBayProfile);
				tempBayProfile.Draw(FALSE, TRUE);			
				*pBayProfile = tempBayProfile;
			}
			catch (...) {
				done = FALSE;
				utilityHelper.ProcessError("Error storing bay profile.");
			}
			

		}
		else
			rc = -1;
	}

	return rc;		
}


int CWizardHelper::ShowSideWizard(CSideProfile *pSideProfile)
{
	int rc;
	CTemporaryResourceOverride tro;

	if (pSideProfile->m_BayProfileList.GetSize() == 0) {	// hasn't been loaded yet
		try {
			CWaitCursor cwc;
			sideProfileDataService.GetSideProfile(pSideProfile->m_SideProfileDBId, *pSideProfile);
		}
		catch (...) {
			AfxMessageBox("Error loading side profile.");
			return -1;
		}
	}

	CSideProfile tempSideProfile;
	tempSideProfile = *pSideProfile;

	CSideProfileSheet sheet("Side Profile");
	sheet.m_pSideProfile = &tempSideProfile;
	sheet.m_psh.dwFlags |= PSH_NOAPPLYNOW;
	CString temp;
	temp.Format("Side Profile Maintenance - [%s]",
		tempSideProfile.m_Description);
	sheet.SetTitle(temp);
	
	BOOL done = FALSE;
	
	while (! done) {
		rc = sheet.DoModal();
		done = TRUE;
		
		if (rc != IDCANCEL && tempSideProfile != *pSideProfile) {
			try {
				CProcessingMessage msgDlg("Saving Side Profile", &sheet);
				CWaitCursor cwc;
				sideProfileDataService.StoreSideProfile(tempSideProfile);
				*pSideProfile = tempSideProfile;
			}
			catch (...) {
				done = FALSE;
				utilityHelper.ProcessError("Error storing side profile.");
			}				
		}
		else
			rc = -1;
	}

	return rc;		
}

int CWizardHelper::ShowAisleWizard(CAisleProfile *pAisleProfile)
{
	int rc;
	CTemporaryResourceOverride tro;

	if (pAisleProfile->m_pLeftSideProfile == NULL && pAisleProfile->m_pRightSideProfile == NULL) {
		try {
			aisleProfileDataService.GetAisleProfile(pAisleProfile->m_AisleProfileDBId, *pAisleProfile);
		}
		catch (...) {
			utilityHelper.ProcessError("Error loading aisle profile.");
			return -1;
		}
	}

	CAisleProfile tempAisleProfile;
	tempAisleProfile = *pAisleProfile;

	CAisleProfileSheet sheet("Aisle Profile");
	sheet.m_pAisleProfile = &tempAisleProfile;
	sheet.m_psh.dwFlags |= PSH_NOAPPLYNOW;
	CString temp;
	temp.Format("Aisle Profile Maintenance - [%s]",
		tempAisleProfile.m_Description);
	sheet.SetTitle(temp);
	
	BOOL done = FALSE;
	
	while (! done) {
		rc = sheet.DoModal();
		done = TRUE;
		
		if (rc != IDCANCEL && tempAisleProfile != *pAisleProfile) {
			try {
				CWaitCursor cwc;
				CProcessingMessage msgDlg("Saving Aisle Profile", &sheet);
				aisleProfileDataService.StoreAisleProfile(tempAisleProfile);
				*pAisleProfile = tempAisleProfile;
			}
			catch (...) {
				done = FALSE;
				utilityHelper.ProcessError("Error storing aisle profile.");
			}
		}
		else
			rc = -1;
	}

	return rc;		
}

void CWizardHelper::LoadBayTypeComboBox(CComboBox &pBox)
{
	int nItem;

	nItem = pBox.AddString(CBayProfile::ConvertBayType(BAYTYPE_BIN));
	pBox.SetItemData(nItem, BAYTYPE_BIN);
	nItem = pBox.AddString(CBayProfile::ConvertBayType(BAYTYPE_CASEFLOW));
	pBox.SetItemData(nItem, BAYTYPE_CASEFLOW);
	nItem = pBox.AddString(CBayProfile::ConvertBayType(BAYTYPE_DRIVEIN));
	pBox.SetItemData(nItem, BAYTYPE_DRIVEIN);
	nItem = pBox.AddString(CBayProfile::ConvertBayType(BAYTYPE_FLOOR));
	pBox.SetItemData(nItem, BAYTYPE_FLOOR);
	nItem = pBox.AddString(CBayProfile::ConvertBayType(BAYTYPE_PALLET));
	pBox.SetItemData(nItem, BAYTYPE_PALLET);
	nItem = pBox.AddString(CBayProfile::ConvertBayType(BAYTYPE_PALLETFLOW));
	pBox.SetItemData(nItem, BAYTYPE_PALLETFLOW);

	CRect r;
	pBox.GetWindowRect(&r);
	pBox.SetWindowPos(NULL, 0, 0, r.Width(), r.Height()*(pBox.GetCount()+1), SWP_NOMOVE|SWP_NOZORDER);


}

#if !defined(AFX_INTEGRATIONOPTIONSPAGE_H__E2E447C8_15C4_4648_8F07_82BDAAF64ED3__INCLUDED_)
#define AFX_INTEGRATIONOPTIONSPAGE_H__E2E447C8_15C4_4648_8F07_82BDAAF64ED3__INCLUDED_

#if _MSC_VER > 1000
#pragma once
#endif // _MSC_VER > 1000
// IntegrationOptionsPage.h : header file
//

/////////////////////////////////////////////////////////////////////////////
// CIntegrationOptionsPage dialog

class CIntegrationOptionsPage : public CPropertyPage
{
	DECLARE_DYNCREATE(CIntegrationOptionsPage)

// Construction
public:
	CIntegrationOptionsPage();
	~CIntegrationOptionsPage();

// Dialog Data
	//{{AFX_DATA(CIntegrationOptionsPage)
	enum { IDD = IDD_INTEGRATION_OPTIONS };
	BO<PERSON>	m_DetailXMLLogging;
	BOOL	m_SkipXMLLogging;
	BOOL	m_SaveOptions;
	//}}AFX_DATA


// Overrides
	// ClassWizard generate virtual function overrides
	//{{AFX_VIRTUAL(CIntegrationOptionsPage)
	protected:
	virtual void DoDataExchange(CDataExchange* pDX);    // DDX/DDV support
	virtual BOOL OnNotify(WPARAM wParam, LPARAM lParam, LRESULT* pResult);
	//}}AFX_VIRTUAL

// Implementation
protected:
	// Generated message map functions
	//{{AFX_MSG(CIntegrationOptionsPage)
	afx_msg void OnDetailXmlLogging();
	afx_msg void OnSkipXmlLogging();
	virtual BOOL OnInitDialog();
	afx_msg BOOL OnHelpInfo(HELPINFO* pHelpInfo);
	//}}AFX_MSG
	DECLARE_MESSAGE_MAP()

};

//{{AFX_INSERT_LOCATION}}
// Microsoft Visual C++ will insert additional declarations immediately before the previous line.

#endif // !defined(AFX_INTEGRATIONOPTIONSPAGE_H__E2E447C8_15C4_4648_8F07_82BDAAF64ED3__INCLUDED_)

// ProductGroupCriteriaRange.cpp: implementation of the CProductGroupCriteriaRange class.
//
//////////////////////////////////////////////////////////////////////

#include "stdafx.h"
#include "modal.h"
#include "ProductGroupCriteriaRange.h"

#include <dbsymtb.h>

#ifdef _DEBUG
#undef THIS_FILE
static char THIS_FILE[]=__FILE__;
#define new DEBUG_NEW
#endif

//////////////////////////////////////////////////////////////////////
// Construction/Destruction
//////////////////////////////////////////////////////////////////////

CProductGroupCriteriaRange::CProductGroupCriteriaRange()
{
	m_CriteriaRangeDBID = -1;
	m_CriteriaDBID = -1;
	m_Description = "";
	m_InUse = FALSE;
}

CProductGroupCriteriaRange::~CProductGroupCriteriaRange()
{
	for (int i=0; i < m_CriteriaQueryList.GetSize(); ++i)
		delete m_CriteriaQueryList[i];

	m_CriteriaQueryList.RemoveAll();

}


CProductGroupCriteriaRange& CProductGroupCriteriaRange::operator=(const CProductGroupCriteriaRange &other)
{
	CProductGroupCriteriaQuery *pQuery;

	m_CriteriaRangeDBID = other.m_CriteriaRangeDBID;
	m_CriteriaDBID = other.m_CriteriaDBID;
	m_Description = other.m_Description;
	m_InUse = other.m_InUse;
	m_CriteriaDBID = other.m_CriteriaDBID;

	for (int i=0; i < m_CriteriaQueryList.GetSize(); ++i)
		delete m_CriteriaQueryList[i];

	m_CriteriaQueryList.RemoveAll();

	for (i=0; i < other.m_CriteriaQueryList.GetSize(); ++i) {
		pQuery = new CProductGroupCriteriaQuery;
		*pQuery = *(other.m_CriteriaQueryList[i]);
		m_CriteriaQueryList.Add(pQuery);
	}
		
	return *this;
}


int CProductGroupCriteriaRange::Parse(CString &line)
{
	char *str;
	char *ptr;
	CString tmp;
	int idx;

	tmp = line;

	try {
		// strtok doesn't interpret double pipes as a field
		idx = tmp.Find("||");
		while (idx >= 0) {
			tmp.Insert(idx+1, " ");
			idx = tmp.Find("||");
		}

		str = tmp.GetBuffer(0);
		
		ptr = strtok(str, "|");
		m_CriteriaRangeDBID = atoi(ptr);
		ptr = strtok(NULL, "|");
		m_Description = ptr;
		ptr = strtok(NULL, "|");
		m_CriteriaDBID = atoi(ptr);

		tmp.ReleaseBuffer();
	}
	catch (...) {
		tmp.ReleaseBuffer();
		AfxMessageBox("Error processing criteria range list.\n");
		ads_printf("%s\n", line);
		return -1;
	}

	return 0;
}

BOOL CProductGroupCriteriaRange::IsEqual(CProductGroupCriteriaRange &other)
{
	if (other.m_CriteriaDBID != m_CriteriaDBID) return FALSE;
	if (other.m_CriteriaRangeDBID != m_CriteriaRangeDBID) return FALSE;
	
	if (other.m_CriteriaQueryList.GetSize() != m_CriteriaQueryList.GetSize()) return FALSE;

	for (int i=0; i < other.m_CriteriaQueryList.GetSize(); ++i) {
		if (! (other.m_CriteriaQueryList[i]->IsEqual(*(m_CriteriaQueryList[i])) ) )
			return FALSE;
	}

	return TRUE;

}

// ProductCommands.cpp: implementation of the CProductCommands class.
//
//////////////////////////////////////////////////////////////////////

#include "stdafx.h"
#include "modal.h"
#include "ProductCommands.h"
#include "ProductHelper.h"
#include "ControlService.h"

#ifdef _DEBUG
#undef THIS_FILE
static char THIS_FILE[]=__FILE__;
#define new DEBUG_NEW
#endif

extern CControlService controlService;

//////////////////////////////////////////////////////////////////////
// Construction/Destruction
//////////////////////////////////////////////////////////////////////

CProductCommands::CProductCommands()
{

}

CProductCommands::~CProductCommands()
{

}

void CProductCommands::RegisterCommands()
{
	
	// Product
	acedRegCmds->addCommand( "SLOTJAVA", "DATAMODELER", "DATAMODELER",
		ACRX_CMD_MODAL, &CProductCommands::DataModel );
	acedRegCmds->addCommand( "SLOTJAVA", "DATAMODEL", "DATAMODEL",
		ACRX_CMD_MODAL, &DataModel );
	acedRegCmds->addCommand( "SLOTJAVA", "DATAPURIFICATION", "DATAPURIFICATION",
		ACRX_CMD_MODAL, &CProductCommands::DataPurification);
	acedRegCmds->addCommand( "SLOTJAVA", "PRODUCTMAINTENANCE", "PRODUCTMAINTENANCE",
		ACRX_CMD_MODAL, &CProductCommands::ProductMaintenance );
	acedRegCmds->addCommand( "SLOTJAVA", "UDFFORMULA", "UDFFORMULA",
		ACRX_CMD_MODAL, &CProductCommands::PopulateUDFWithFormula );
	acedRegCmds->addCommand( "SLOTJAVA", "DELETEPRODUCTSBYFACILITY", "DELETEPRODUCTSBYFACILITY",
		ACRX_CMD_MODAL, &CProductCommands::DeleteProductsByFacility );

}

void CProductCommands::DataModel()
{
	if (! controlService.ValidateCommand(CCommands::openFacilityMode))
		return;

	CProductHelper helper;

	helper.DataModel();

	return;
}

void CProductCommands::DataPurification()
{
	if (! controlService.ValidateCommand(CCommands::openFacilityMode))
		return;

	CProductHelper helper;

	helper.DataPurification();

	return;
}

void CProductCommands::ProductMaintenance()
{
	if (! controlService.ValidateCommand(CCommands::openFacilityMode))
		return;

	CProductHelper helper;

	helper.ProductMaintenance();

	return;

}

void CProductCommands::PopulateUDFWithFormula()
{
	if (! controlService.ValidateCommand(CCommands::openFacilityMode))
		return;

	CProductHelper helper;
	
	helper.PopulateUDFWithFormula();

	return;

}

void CProductCommands::DeleteProductsByFacility()
{
	if (! controlService.ValidateCommand(CCommands::openFacilityMode))
		return;

	CProductHelper helper;
	
	helper.DeleteProductsByFacility();

	return;

}

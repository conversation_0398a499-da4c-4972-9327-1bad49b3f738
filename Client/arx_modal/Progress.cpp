// Progress.cpp : implementation file
//

#include "stdafx.h"
#include "modal.h"
#include "Progress.h"

#ifdef _DEBUG
#define new DEBUG_NEW
#undef THIS_FILE
static char THIS_FILE[] = __FILE__;
#endif

/////////////////////////////////////////////////////////////////////////////
// CProgress dialog


CProgress::CProgress(CWnd* pParent /*=NULL*/)
	: CDialog(CProgress::IDD, pParent)
{
	//{{AFX_DATA_INIT(CProgress)
	//}}AFX_DATA_INIT
	m_Stopping = FALSE;
}


void CProgress::DoDataExchange(CDataExchange* pDX)
{
	CDialog::DoDataExchange(pDX);
	//{{AFX_DATA_MAP(CProgress)
	DDX_Control(pDX, IDC_STATUS_TEXT, m_StatusTextCtrl);
	DDX_Control(pDX, IDC_PROGRESS, m_ProgressCtrl);
	//}}AFX_DATA_MAP
}


BEGIN_MESSAGE_MAP(CProgress, CDialog)
	//{{AFX_MSG_MAP(CProgress)
	ON_BN_CLICKED(IDC_STOP, OnStop)
	//}}AFX_MSG_MAP
END_MESSAGE_MAP()

/////////////////////////////////////////////////////////////////////////////
// CProgress message handlers

void CProgress::OnStop() 
{
	m_Stopping = TRUE;
	m_StatusTextCtrl.SetWindowText("Stopping...");
	UpdateData(FALSE);

}

void CProgress::PostNcDestroy() 
{
	delete this;
}



BOOL CProgress::OnInitDialog() 
{
	CDialog::OnInitDialog();
	
	if (m_NoStop)
		GetDlgItem(IDC_STOP)->ShowWindow(SW_HIDE);
	else
		GetDlgItem(IDC_STOP)->ShowWindow(SW_SHOW);

	m_Stopping = FALSE;

	return TRUE;  // return TRUE unless you set the focus to a control
	              // EXCEPTION: OCX Property Pages should return FALSE
}

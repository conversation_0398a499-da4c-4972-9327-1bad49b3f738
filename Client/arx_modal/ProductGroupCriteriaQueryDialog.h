#if !defined(AFX_PRODUCTGROUPCRITERIAQUERYDIALOG_H__A0D6B0A3_0E5B_11D5_9EC8_00C04FAC149C__INCLUDED_)
#define AFX_PRODUCTGROUPCRITERIAQUERYDIALOG_H__A0D6B0A3_0E5B_11D5_9EC8_00C04FAC149C__INCLUDED_

#if _MSC_VER > 1000
#pragma once
#endif // _MSC_VER > 1000
// ProductGroupCriteriaQueryDialog.h : header file
//
#include "Resource.h"
#include "ProductGroupDataService.h"

/////////////////////////////////////////////////////////////////////////////
// CProductGroupCriteriaQueryDialog dialog

class CProductGroupCriteriaQueryDialog : public CDialog
{
// Construction
public:
	CString m_Attribute;
	CString m_InternalOperator;
	CString m_InternalValue;
	CProductGroupCriteriaQueryDialog(CWnd* pParent = NULL);   // standard constructor
	CProductGroupDataService *m_ProductGroupDataService;

// Dialog Data
	//{{AFX_DATA(CProductGroupCriteriaQueryDialog)
	enum { IDD = IDD_PRODUCT_GROUP_CRITERIA_QUERY };
	CEdit	m_ToValueCtrl;
	CStatic	m_ToStaticCtrl;
	CComboBox	m_OperatorCtrl;
	CEdit	m_FromValueCtrl;
	CStatic	m_FromStaticCtrl;
	CString	m_FromValue;
	CString	m_ToValue;
	//}}AFX_DATA


// Overrides
	// ClassWizard generated virtual function overrides
	//{{AFX_VIRTUAL(CProductGroupCriteriaQueryDialog)
	protected:
	virtual void DoDataExchange(CDataExchange* pDX);    // DDX/DDV support
	//}}AFX_VIRTUAL

// Implementation
protected:

	// Generated message map functions
	//{{AFX_MSG(CProductGroupCriteriaQueryDialog)
	virtual void OnOK();
	virtual void OnCancel();
	afx_msg void OnHelp();
	afx_msg void OnSelchangeOperator();
	virtual BOOL OnInitDialog();
	afx_msg BOOL OnHelpInfo(HELPINFO* pHelpInfo);
	//}}AFX_MSG
	DECLARE_MESSAGE_MAP()

private:
	CMapStringToString m_OperatorReverseMap;
};

//{{AFX_INSERT_LOCATION}}
// Microsoft Visual C++ will insert additional declarations immediately before the previous line.

#endif // !defined(AFX_PRODUCTGROUPCRITERIAQUERYDIALOG_H__A0D6B0A3_0E5B_11D5_9EC8_00C04FAC149C__INCLUDED_)

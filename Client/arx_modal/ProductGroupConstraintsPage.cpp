// ProductGroupConstraintsPage.cpp : implementation file
//

#include "stdafx.h"
#include "Resource.h"
#include "ssa_exception.h"
#include "ProductGroupConstraintsPage.h"
#include "TreeElement.h"
#include "HelpService.h"
#include "FacilityDataService.h"
#include "UtilityHelper.h"

#ifdef _DEBUG
#define new DEBUG_NEW
#undef THIS_FILE
static char THIS_FILE[] = __FILE__;
#endif

/////////////////////////////////////////////////////////////////////////////
// CProductGroupConstraintsPage property page
extern TreeElement changesTree;
extern CHelpService helpService;
extern CFacilityDataService facilityDataService;
extern CUtilityHelper utilityHelper;

const int TREE_SECTION = 0;
const int TREE_AISLE = 1;
const int TREE_SIDE = 2;
const int TREE_BAY = 3;

IMPLEMENT_DYNCREATE(CProductGroupConstraintsPage, CPropertyPage)

CProductGroupConstraintsPage::CProductGroupConstraintsPage() : CPropertyPage(CProductGroupConstraintsPage::IDD)
{
	//{{AFX_DATA_INIT(CProductGroupConstraintsPage)
	m_Exclusive = FALSE;
	//}}AFX_DATA_INIT
}

CProductGroupConstraintsPage::~CProductGroupConstraintsPage()
{
}

void CProductGroupConstraintsPage::DoDataExchange(CDataExchange* pDX)
{
	CPropertyPage::DoDataExchange(pDX);
	//{{AFX_DATA_MAP(CProductGroupConstraintsPage)
	DDX_Control(pDX, IDC_NONEXCLUSIVE_LIST, m_NonExclusiveListCtrl);
	DDX_Control(pDX, IDC_EXCLUSIVE_LIST, m_ExclusiveListCtrl);
	DDX_Control(pDX, IDC_LEVEL_LIST, m_LevelListCtrl);
	DDX_Control(pDX, IDC_FACILITY_TREE, m_TreeCtrl);
	DDX_Check(pDX, IDC_EXCLUSIVE, m_Exclusive);
	//}}AFX_DATA_MAP
}


BEGIN_MESSAGE_MAP(CProductGroupConstraintsPage, CPropertyPage)
	//{{AFX_MSG_MAP(CProductGroupConstraintsPage)
	ON_NOTIFY(TVN_ITEMEXPANDED, IDC_FACILITY_TREE, OnItemexpandedFacilityTree)
	ON_NOTIFY(NM_DBLCLK, IDC_FACILITY_TREE, OnDblclkFacilityTree)
	ON_NOTIFY(TVN_ITEMEXPANDING, IDC_FACILITY_TREE, OnItemexpandingFacilityTree)
	ON_NOTIFY(TVN_SELCHANGED, IDC_FACILITY_TREE, OnSelchangedFacilityTree)
	ON_BN_CLICKED(IDC_ADD_EXCLUSIVE, OnAddExclusive)
	ON_BN_CLICKED(IDC_REMOVE_EXCLUSIVE, OnRemoveExclusive)
	ON_BN_CLICKED(IDC_ADD_NONEXCLUSIVE, OnAddNonexclusive)
	ON_BN_CLICKED(IDC_REMOVE_NONEXCLUSIVE, OnRemoveNonexclusive)
	ON_WM_HELPINFO()
	//}}AFX_MSG_MAP
END_MESSAGE_MAP()

/////////////////////////////////////////////////////////////////////////////
// CProductGroupConstraintsPage message handlers

BOOL CProductGroupConstraintsPage::OnInitDialog() 
{
	CPropertyPage::OnInitDialog();
	CRect r;

	for (int i=0; i < m_ExclusiveListCtrl.GetHeaderCtrl()->GetItemCount(); ++i)
		m_ExclusiveListCtrl.DeleteColumn(i);

	for (i=0; i < m_NonExclusiveListCtrl.GetHeaderCtrl()->GetItemCount(); ++i)
		m_NonExclusiveListCtrl.DeleteColumn(i);

	m_ExclusiveListCtrl.GetClientRect(&r);
	m_ExclusiveListCtrl.InsertColumn(0, "Section", LVCFMT_LEFT, r.Width()/5, 0);
	m_ExclusiveListCtrl.InsertColumn(1, "Aisle", LVCFMT_LEFT, r.Width()/5, 0);
	m_ExclusiveListCtrl.InsertColumn(2, "Side", LVCFMT_LEFT, r.Width()/5, 0);
	m_ExclusiveListCtrl.InsertColumn(3, "Bay", LVCFMT_LEFT, r.Width()/5, 0);
	m_ExclusiveListCtrl.InsertColumn(4, "Level", LVCFMT_LEFT, r.Width()/5, 0);

	m_NonExclusiveListCtrl.GetClientRect(&r);
	m_NonExclusiveListCtrl.InsertColumn(0, "Section", LVCFMT_LEFT, r.Width()/5, 0);
	m_NonExclusiveListCtrl.InsertColumn(1, "Aisle", LVCFMT_LEFT, r.Width()/5, 0);
	m_NonExclusiveListCtrl.InsertColumn(2, "Side", LVCFMT_LEFT, r.Width()/5, 0);
	m_NonExclusiveListCtrl.InsertColumn(3, "Bay", LVCFMT_LEFT, r.Width()/5, 0);
	m_NonExclusiveListCtrl.InsertColumn(4, "Level", LVCFMT_LEFT, r.Width()/5, 0);

	LoadTree();

	m_Exclusive = (m_ProductGroup->m_Exclusive == 1);

	m_LevelListCtrl.GetWindowRect(&r);
	m_LevelListCtrl.SetWindowPos(NULL, 0, 0, r.Width(), r.Height()*5, SWP_NOMOVE|SWP_NOZORDER);


	LoadConstraints();

	UpdateData(FALSE);

	return TRUE;  // return TRUE unless you set the focus to a control
	              // EXCEPTION: OCX Property Pages should return FALSE
}

void CProductGroupConstraintsPage::OnItemexpandedFacilityTree(NMHDR* pNMHDR, LRESULT* pResult) 
{
	NM_TREEVIEW* pNMTreeView = (NM_TREEVIEW*)pNMHDR;

	*pResult = 0;
}

void CProductGroupConstraintsPage::OnDblclkFacilityTree(NMHDR* pNMHDR, LRESULT* pResult) 
{
	UNREFERENCED_PARAMETER(pNMHDR);

	*pResult = 0;
}

void CProductGroupConstraintsPage::LoadTree()
{
	int rc, i, idx;
	CStringArray sectionList;
	long sectionDBID;
	CString section;
	HTREEITEM hItem;

	try {
		rc = facilityDataService.GetSectionsByFacility(changesTree.elementDBID, sectionList);
	}
	catch (Ssa_Exception e) {
		utilityHelper.ProcessError("Error getting sections from database.", &e);
		return;
	}
	catch (...) {
		utilityHelper.ProcessError("Error getting sections from database.");
		return;
	}

	for (i=0; i < sectionList.GetSize(); ++i) {
		if (sectionList[i].Right(1) == "|")
			sectionList[i].Delete(sectionList[i].GetLength()-1);

		idx = sectionList[i].Find("|");
		sectionDBID = atol(sectionList[i].Left(idx));
		section = sectionList[i].Mid(idx+1);
		hItem = m_TreeCtrl.InsertItem(section.GetBuffer(0), TVI_ROOT, TVI_LAST);
		section.ReleaseBuffer();
		m_TreeCtrl.SetItemData(hItem, sectionDBID);
		// Insert a dummy aisle item
		hItem = m_TreeCtrl.InsertItem("dummy", hItem, TVI_LAST);
		m_TreeCtrl.SetItemData(hItem, 0);
	}

	m_TreeCtrl.Expand(TVI_ROOT, TVE_EXPAND);

}

void CProductGroupConstraintsPage::OnItemexpandingFacilityTree(NMHDR* pNMHDR, LRESULT* pResult) 
{
	NM_TREEVIEW* pNMTreeView = (NM_TREEVIEW*)pNMHDR;
	HTREEITEM hItem, hChildItem;
	int type, dbid, rc, i, idx;
	CStringArray childList;
	CString description;

	*pResult = 0;

	if (pNMTreeView->action != TVE_EXPAND)
		return;

	hItem = pNMTreeView->itemNew.hItem;
	hChildItem = m_TreeCtrl.GetChildItem(hItem);
	if (m_TreeCtrl.GetItemData(hChildItem) == 0) {	// need to load it up
		dbid = m_TreeCtrl.GetItemData(hItem);
		type = GetItemType(hItem);
		switch (type) {
		case TREE_SECTION:
			rc = facilityDataService.GetAislesBySection(dbid, childList);
			if (childList.GetSize() == 0) {
				*pResult = 1;
				return;
			}
			// First delete the dummy item
			m_TreeCtrl.DeleteItem(hChildItem);

			// Now add the real items
			for (i=0; i < childList.GetSize(); ++i) {
				if (childList[i].Right(1) == "|")
					childList[i].Delete(childList[i].GetLength()-1);

				idx = childList[i].Find("|");
				dbid = atol(childList[i].Left(idx));
				description = childList[i].Mid(idx+1);
				hChildItem = m_TreeCtrl.InsertItem(description.GetBuffer(0), hItem, TVI_LAST);
				description.ReleaseBuffer();
				m_TreeCtrl.SetItemData(hChildItem, dbid);

				// Insert the dummy item for each aisle
				hChildItem = m_TreeCtrl.InsertItem("dummy", hChildItem, TVI_LAST);
				m_TreeCtrl.SetItemData(hChildItem, 0);
			}
			break;

		case TREE_AISLE:
			rc = facilityDataService.GetSidesByAisle(dbid, childList);
			if (childList.GetSize() == 0) {
				*pResult = 1;
				return;
			}
			// First delete the dummy item
			m_TreeCtrl.DeleteItem(hChildItem);

			// Now add the real items
			for (i=0; i < childList.GetSize(); ++i) {
				if (childList[i].Right(1) == "|")
					childList[i].Delete(childList[i].GetLength()-1);

				idx = childList[i].Find("|");
				dbid = atol(childList[i].Left(idx));
				description = childList[i].Mid(idx+1);
				hChildItem = m_TreeCtrl.InsertItem(description.GetBuffer(0), hItem, TVI_LAST);
				description.ReleaseBuffer();
				m_TreeCtrl.SetItemData(hChildItem, dbid);

				// Insert the dummy item for each side
				hChildItem = m_TreeCtrl.InsertItem("dummy", hChildItem, TVI_LAST);
				m_TreeCtrl.SetItemData(hChildItem, 0);
			}
			break;

		case TREE_SIDE:
			rc = facilityDataService.GetBaysBySide(dbid, childList);
			if (childList.GetSize() == 0) {
				*pResult = 1;
				return;
			}
			// First delete the dummy item
			m_TreeCtrl.DeleteItem(hChildItem);

			// Now add the real items
			for (i=0; i < childList.GetSize(); ++i) {
				if (childList[i].Right(1) == "|")
					childList[i].Delete(childList[i].GetLength()-1);

				idx = childList[i].Find("|");
				dbid = atol(childList[i].Left(idx));
				description = childList[i].Mid(idx+1);
				hChildItem = m_TreeCtrl.InsertItem(description.GetBuffer(0), hItem, TVI_LAST);
				description.ReleaseBuffer();
				m_TreeCtrl.SetItemData(hChildItem, dbid);
			}
			break;
		}
	}

}

int CProductGroupConstraintsPage::GetItemType(HTREEITEM &hItem)
{
	HTREEITEM hParentItem;

	hParentItem = m_TreeCtrl.GetParentItem(hItem);
	if (hParentItem == NULL)
		return TREE_SECTION;

	hParentItem = m_TreeCtrl.GetParentItem(hParentItem);
	if (hParentItem == NULL)
		return TREE_AISLE;

	hParentItem = m_TreeCtrl.GetParentItem(hParentItem);
	if (hParentItem == NULL)
		return TREE_SIDE;

	return TREE_BAY;

}

void CProductGroupConstraintsPage::SetLevelList(int maxLevel)
{
	CRect r;
	CString temp;
	int curSel;

	curSel = m_LevelListCtrl.GetCurSel();

	m_LevelListCtrl.GetWindowRect(&r);
	m_LevelListCtrl.SetWindowPos(NULL, 0, 0, r.Width(), r.Height()*(maxLevel+1), SWP_NOMOVE|SWP_NOZORDER);
	
		

	m_LevelListCtrl.ResetContent();
	m_LevelListCtrl.AddString("Any");
	for (int i=1; i <= maxLevel; ++i) {
		temp.Format("%d", i);
		m_LevelListCtrl.AddString(temp);
	}

	if (curSel > maxLevel || curSel < 0)
		curSel = 0;
	m_LevelListCtrl.SetCurSel(curSel);

	return;

}


void CProductGroupConstraintsPage::OnSelchangedFacilityTree(NMHDR* pNMHDR, LRESULT* pResult) 
{
	NM_TREEVIEW* pNMTreeView = (NM_TREEVIEW*)pNMHDR;

	int maxLevel = 0;
	HTREEITEM hItem, hChildItem;
	int type, dbid;
	int rc, i, idx;
	CStringArray childList;
	CString description;

	hItem = pNMTreeView->itemNew.hItem;
	dbid = m_TreeCtrl.GetItemData(hItem);
	type = GetItemType(hItem);

	hChildItem = m_TreeCtrl.GetChildItem(hItem);
	if (m_TreeCtrl.GetItemData(hChildItem) == 0) {	// need to load it up
		dbid = m_TreeCtrl.GetItemData(hItem);
		type = GetItemType(hItem);
		switch (type) {
		case TREE_SECTION:
			rc = facilityDataService.GetAislesBySection(dbid, childList);
			if (childList.GetSize() > 0) {
				
				// First delete the dummy item
				m_TreeCtrl.DeleteItem(hChildItem);
				
				// Now add the real items
				for (i=0; i < childList.GetSize(); ++i) {
					if (childList[i].Right(1) == "|")
						childList[i].Delete(childList[i].GetLength()-1);
					
					idx = childList[i].Find("|");
					dbid = atol(childList[i].Left(idx));
					description = childList[i].Mid(idx+1);
					hChildItem = m_TreeCtrl.InsertItem(description.GetBuffer(0), hItem, TVI_LAST);
					description.ReleaseBuffer();
					m_TreeCtrl.SetItemData(hChildItem, dbid);
					
					// Insert the dummy item for each aisle
					hChildItem = m_TreeCtrl.InsertItem("dummy", hChildItem, TVI_LAST);
					m_TreeCtrl.SetItemData(hChildItem, 0);
				}
			}
			break;

		case TREE_AISLE:
			rc = facilityDataService.GetSidesByAisle(dbid, childList);
			if (childList.GetSize() > 0) {
				
				// First delete the dummy item
				m_TreeCtrl.DeleteItem(hChildItem);
				
				// Now add the real items
				for (i=0; i < childList.GetSize(); ++i) {
					if (childList[i].Right(1) == "|")
						childList[i].Delete(childList[i].GetLength()-1);
					
					idx = childList[i].Find("|");
					dbid = atol(childList[i].Left(idx));
					description = childList[i].Mid(idx+1);
					hChildItem = m_TreeCtrl.InsertItem(description.GetBuffer(0), hItem, TVI_LAST);
					description.ReleaseBuffer();
					m_TreeCtrl.SetItemData(hChildItem, dbid);
					
					// Insert the dummy item for each side
					hChildItem = m_TreeCtrl.InsertItem("dummy", hChildItem, TVI_LAST);
					m_TreeCtrl.SetItemData(hChildItem, 0);
				}
			}
			break;

		case TREE_SIDE:
			rc = facilityDataService.GetBaysBySide(dbid, childList);
			if (childList.GetSize() > 0) {
				// First delete the dummy item
				m_TreeCtrl.DeleteItem(hChildItem);
				
				// Now add the real items
				for (i=0; i < childList.GetSize(); ++i) {
					if (childList[i].Right(1) == "|")
						childList[i].Delete(childList[i].GetLength()-1);
					
					idx = childList[i].Find("|");
					dbid = atol(childList[i].Left(idx));
					description = childList[i].Mid(idx+1);
					hChildItem = m_TreeCtrl.InsertItem(description.GetBuffer(0), hItem, TVI_LAST);
					description.ReleaseBuffer();
					m_TreeCtrl.SetItemData(hChildItem, dbid);
				}
			}
			break;
		}
	}

	hItem = pNMTreeView->itemNew.hItem;
	dbid = m_TreeCtrl.GetItemData(hItem);
	type = GetItemType(hItem);
	
	if (! m_TreeCtrl.ItemHasChildren(hItem) || m_TreeCtrl.GetItemText(m_TreeCtrl.GetChildItem(hItem)) == "dummy") {
		maxLevel = 0;
	}
	else {
		switch (type) {
		case TREE_SECTION:
			maxLevel = facilityDataService.GetMaxRelativeLevelBySection(dbid);
			break;
		case TREE_AISLE:
			maxLevel = facilityDataService.GetMaxRelativeLevelByAisle(dbid);
			break;
		case TREE_SIDE:
			maxLevel = facilityDataService.GetMaxRelativeLevelBySide(dbid);
			break;
		case TREE_BAY:
			maxLevel = facilityDataService.GetMaxRelativeLevelByBay(dbid);
			break;
		}
	}

	SetLevelList(maxLevel);
	
	*pResult = 0;
}

void CProductGroupConstraintsPage::OnAddExclusive() 
{
	AddConstraint(TRUE);
}

void CProductGroupConstraintsPage::OnRemoveExclusive() 
{
	int nItem, idx;
	POSITION pos;

	pos = m_ExclusiveListCtrl.GetFirstSelectedItemPosition();
	if (pos == NULL)
		return;

	nItem = m_ExclusiveListCtrl.GetNextSelectedItem(pos);

	// Find the matching selection in the constraints list
	idx = FindItemInList(&m_ExclusiveListCtrl);
	if (idx < 0)	// This shouldn't happen
		return;

	m_ExclusiveListCtrl.DeleteItem(nItem);
	delete m_ProductGroup->m_ConstraintList[idx];
	m_ProductGroup->m_ConstraintList.RemoveAt(idx);


}

void CProductGroupConstraintsPage::OnAddNonexclusive() 
{
	AddConstraint(FALSE);
}

void CProductGroupConstraintsPage::OnRemoveNonexclusive() 
{
	int nItem, idx;
	POSITION pos;

	pos = m_NonExclusiveListCtrl.GetFirstSelectedItemPosition();
	if (pos == NULL)
		return;

	nItem = m_NonExclusiveListCtrl.GetNextSelectedItem(pos);

	// Find the matching selection in the constraints list
	idx = FindItemInList(&m_NonExclusiveListCtrl);
	if (idx < 0)	// This shouldn't happen
		return;

	m_NonExclusiveListCtrl.DeleteItem(nItem);
	delete m_ProductGroup->m_ConstraintList[idx];
	m_ProductGroup->m_ConstraintList.RemoveAt(idx);
	
}

int CProductGroupConstraintsPage::LoadConstraints()
{
	// Constraints are loaded by the parent now
	for (int i=0; i < m_ProductGroup->m_ConstraintList.GetSize(); ++i)
		DisplayConstraint(*(m_ProductGroup->m_ConstraintList[i]));

	return 0;

}

void CProductGroupConstraintsPage::DisplayConstraint(CProductGroupConstraint &constraint)
{
	LVITEM lvItem;
	CListCtrl *pListCtrl;
	int nItem;
	CString temp;

	if (constraint.m_IsExclusive)
		pListCtrl = &m_ExclusiveListCtrl;
	else
		pListCtrl = &m_NonExclusiveListCtrl;

	lvItem.iItem = pListCtrl->GetItemCount();
	lvItem.mask = LVIF_TEXT|LVIF_PARAM;
	
	lvItem.iSubItem = 0;
	lvItem.pszText = constraint.m_SectionDescription.GetBuffer(0);
	constraint.m_SectionDescription.ReleaseBuffer();
	lvItem.lParam = constraint.m_SectionDBID;
	nItem = pListCtrl->InsertItem(&lvItem);

	pListCtrl->SetItemText(nItem, 1, constraint.m_AisleDescription);
	pListCtrl->SetItemText(nItem, 2, constraint.m_SideDescription);
	pListCtrl->SetItemText(nItem, 3, constraint.m_BayDescription);
	if (constraint.m_RelativeLevel == 0)
		pListCtrl->SetItemText(nItem, 4, "Any");
	else {
		temp.Format("%d", constraint.m_RelativeLevel);
		pListCtrl->SetItemText(nItem, 4, temp);
	}

	return;

}

void CProductGroupConstraintsPage::AddConstraint(BOOL isExclusive)
{
	HTREEITEM hItem;
	CProductGroupConstraint *pConstraint;

	hItem = m_TreeCtrl.GetSelectedItem();
	
	pConstraint = new CProductGroupConstraint;
	pConstraint->m_ProductGroupConstraintDBID = 0;
	pConstraint->m_IsExclusive = isExclusive;
	
	pConstraint->m_BayDBID = 0;
	pConstraint->m_BayDescription = "Any";
	pConstraint->m_SideDBID = 0;
	pConstraint->m_SideDescription = "Any";
	pConstraint->m_AisleDBID = 0;
	pConstraint->m_AisleDescription = "Any";

	pConstraint->m_RelativeLevel = m_LevelListCtrl.GetCurSel();

	switch (GetItemType(hItem)) {
	case TREE_SECTION:
		pConstraint->m_SectionDBID = m_TreeCtrl.GetItemData(hItem);
		pConstraint->m_SectionDescription = m_TreeCtrl.GetItemText(hItem);
		break;
	case TREE_AISLE:
		pConstraint->m_AisleDBID = m_TreeCtrl.GetItemData(hItem);
		pConstraint->m_AisleDescription = m_TreeCtrl.GetItemText(hItem);
		hItem = m_TreeCtrl.GetParentItem(hItem);
		pConstraint->m_SectionDBID = m_TreeCtrl.GetItemData(hItem);
		pConstraint->m_SectionDescription = m_TreeCtrl.GetItemText(hItem);
		break;
	case TREE_SIDE:
		pConstraint->m_SideDBID = m_TreeCtrl.GetItemData(hItem);
		pConstraint->m_SideDescription = m_TreeCtrl.GetItemText(hItem);
		hItem = m_TreeCtrl.GetParentItem(hItem);
		pConstraint->m_AisleDBID = m_TreeCtrl.GetItemData(hItem);
		pConstraint->m_AisleDescription = m_TreeCtrl.GetItemText(hItem);
		hItem = m_TreeCtrl.GetParentItem(hItem);
		pConstraint->m_SectionDBID = m_TreeCtrl.GetItemData(hItem);
		pConstraint->m_SectionDescription = m_TreeCtrl.GetItemText(hItem);
		break;
	case TREE_BAY:
		pConstraint->m_BayDBID = m_TreeCtrl.GetItemData(hItem);
		pConstraint->m_BayDescription = m_TreeCtrl.GetItemText(hItem);
		hItem = m_TreeCtrl.GetParentItem(hItem);
		pConstraint->m_SideDBID = m_TreeCtrl.GetItemData(hItem);
		pConstraint->m_SideDescription = m_TreeCtrl.GetItemText(hItem);
		hItem = m_TreeCtrl.GetParentItem(hItem);
		pConstraint->m_AisleDBID = m_TreeCtrl.GetItemData(hItem);
		pConstraint->m_AisleDescription = m_TreeCtrl.GetItemText(hItem);
		hItem = m_TreeCtrl.GetParentItem(hItem);
		pConstraint->m_SectionDBID = m_TreeCtrl.GetItemData(hItem);
		pConstraint->m_SectionDescription = m_TreeCtrl.GetItemText(hItem);
		break;
	}

	if (! CheckConstraint(*pConstraint))
		delete pConstraint;
	else {
		m_ProductGroup->m_ConstraintList.Add(pConstraint);
		DisplayConstraint(*pConstraint);
	}


}

BOOL CProductGroupConstraintsPage::CheckConstraint(CProductGroupConstraint &constraint)
{
	CProductGroupConstraint *other;
	CArray <int, int> deleteList;
	int nItem;

	for (int i=0; i < m_ProductGroup->m_ConstraintList.GetSize(); ++i) {
		other = m_ProductGroup->m_ConstraintList[i];

		if (constraint.m_IsExclusive == other->m_IsExclusive) {	// same boxes
			if (CompareConstraints(constraint, *other) ) {	// New one is same as existing
				AfxMessageBox("The selection is already included in the list.");
				return FALSE;	// new entry is not necessary
			}
			else if (other->IsParent(constraint)) {		// new one is child
				AfxMessageBox("This selection is contained within a more generic selection that is "
					"already included in the list.");
				return FALSE;
			}
			else if (constraint.IsParent(*other))	// When a parent is added, remove all the children
				deleteList.Add(i);
		}
		else {
			if (CompareConstraints(constraint, *other)) {
				if (constraint.m_IsExclusive) {
					AfxMessageBox("The selection is already included in the non-exclusive list.");
					return FALSE;
				}
				else {
					AfxMessageBox("The selection is already included in the exclusive list.");
					return FALSE;
				}
			}

		}
	}

	// Now subsume any children
	for (i=deleteList.GetSize()-1; i >= 0; --i) {
		other = m_ProductGroup->m_ConstraintList[deleteList[i]];
		if (other->m_IsExclusive) {
			nItem = FindItemInControl(&m_ExclusiveListCtrl, *other);
			m_ExclusiveListCtrl.DeleteItem(nItem);
		}
		else {
			nItem = FindItemInControl(&m_NonExclusiveListCtrl, *other);
			m_NonExclusiveListCtrl.DeleteItem(nItem);
		}
		delete other;
		m_ProductGroup->m_ConstraintList.RemoveAt(deleteList[i]);
	}

			

	return TRUE;

}


int CProductGroupConstraintsPage::FindItemInControl(CListCtrl *pListCtrl, CProductGroupConstraint &constraint)
{
	CString temp;

	for (int i=0; i < pListCtrl->GetItemCount(); ++i) {
		if (pListCtrl->GetItemText(i, 0) != constraint.m_SectionDescription) continue;
		if (pListCtrl->GetItemText(i, 1) != constraint.m_AisleDescription) continue;
		if (pListCtrl->GetItemText(i, 2) != constraint.m_SideDescription) continue;
		if (pListCtrl->GetItemText(i, 3) != constraint.m_BayDescription) continue;
		temp = pListCtrl->GetItemText(i, 4);
		if (temp == "Any")
			temp = "0";
		if (constraint.m_RelativeLevel != atoi(temp)) continue;

		return i;
	}

	return -1;
		
}

int CProductGroupConstraintsPage::FindItemInList(CListCtrl *pListCtrl)
{
	int curSel;
	POSITION pos;
	CProductGroupConstraint constraint1, constraint2;
	CString temp;

	pos = pListCtrl->GetFirstSelectedItemPosition();
	if (pos == NULL)
		return -1;

	curSel = pListCtrl->GetNextSelectedItem(pos);
	if (curSel < 0)
		return -1;
	
	constraint1.m_SectionDescription = pListCtrl->GetItemText(curSel, 0);
	constraint1.m_AisleDescription = pListCtrl->GetItemText(curSel, 1);
	constraint1.m_SideDescription = pListCtrl->GetItemText(curSel, 2);
	constraint1.m_BayDescription = pListCtrl->GetItemText(curSel, 3);
	temp = pListCtrl->GetItemText(curSel, 4);
	if (temp == "Any")
		temp = "0";
	constraint1.m_RelativeLevel = atoi(temp);

	if (pListCtrl == &m_ExclusiveListCtrl)
		constraint1.m_IsExclusive = TRUE;
	else
		constraint1.m_IsExclusive = FALSE;

	for (int i=0; i < m_ProductGroup->m_ConstraintList.GetSize(); ++i) {
		constraint2 = *(m_ProductGroup->m_ConstraintList[i]);

		if (constraint1.m_SectionDescription != constraint2.m_SectionDescription) continue;
		if (constraint1.m_AisleDescription != constraint2.m_AisleDescription) continue;
		if (constraint1.m_SideDescription != constraint2.m_SideDescription) continue;
		if (constraint1.m_BayDescription != constraint2.m_BayDescription) continue;
		if (constraint1.m_RelativeLevel != constraint2.m_RelativeLevel) continue;
		if (constraint1.m_IsExclusive != constraint2.m_IsExclusive) continue;

		return i;
	}

	return -1;

}


void CProductGroupConstraintsPage::OnOK() 
{
	UpdateData(TRUE);
	
	if (m_Exclusive)
		m_ProductGroup->m_Exclusive = 1;
	else
		m_ProductGroup->m_Exclusive = 0;

	CPropertyPage::OnOK();
}

BOOL CProductGroupConstraintsPage::CompareConstraints(CProductGroupConstraint &constraint1, 
													  CProductGroupConstraint &constraint2)
{
	
	if (constraint1.m_RelativeLevel != constraint2.m_RelativeLevel) return FALSE;
	if (constraint1.m_BayDBID != constraint2.m_BayDBID) return FALSE;
	if (constraint1.m_SideDBID != constraint2.m_SideDBID) return FALSE;
	if (constraint1.m_AisleDBID != constraint2.m_AisleDBID) return FALSE;
	if (constraint1.m_SectionDBID != constraint2.m_SectionDBID) return FALSE;

	return TRUE;

}

BOOL CProductGroupConstraintsPage::OnHelpInfo(HELPINFO* pHelpInfo) 
{
	helpService.ShowFieldHelp(IDD, pHelpInfo->iCtrlId);
	return FALSE;
}

BOOL CProductGroupConstraintsPage::OnNotify(WPARAM wParam, LPARAM lParam, LRESULT* pResult) 
{
	NMHDR* pNMHDR = (NMHDR*)lParam;
	if (pNMHDR->code == PSN_HELP) {
		helpService.ShowScreenHelp(IDD);
		return TRUE;
	}		
	return CPropertyPage::OnNotify(wParam, lParam, pResult);
}

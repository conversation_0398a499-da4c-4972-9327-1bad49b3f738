// InterfaceMapUDFDialog.cpp : implementation file
//

#include "stdafx.h"
#include "modal.h"
#include "InterfaceMapUDFDialog.h"
#include "UtilityHelper.h"
#include "Constants.h"

#ifdef _DEBUG
#define new DEBUG_NEW
#undef THIS_FILE
static char THIS_FILE[] = __FILE__;
#endif

/////////////////////////////////////////////////////////////////////////////
// CInterfaceMapUDFDialog dialog


CInterfaceMapUDFDialog::CInterfaceMapUDFDialog(CWnd* pParent /*=NULL*/)
	: CDialog(CInterfaceMapUDFDialog::IDD, pParent)
{
	//{{AFX_DATA_INIT(CInterfaceMapUDFDialog)
	m_Name = _T("");
	//}}AFX_DATA_INIT
	m_ElementType = -1;
	m_DataType = -1;
}


void CInterfaceMapUDFDialog::DoDataExchange(CDataExchange* pDX)
{
	CDialog::DoDataExchange(pDX);	//{{AFX_DATA_MAP(CInterfaceMapUDFDialog)
	DDX_Control(pDX, IDC_TYPE, m_TypeListCtrl);
	DDX_Control(pDX, IDC_ELEMENT_TYPE, m_ElementListCtrl);
	DDX_Text(pDX, IDC_NAME, m_Name);
	//}}AFX_DATA_MAP
}


BEGIN_MESSAGE_MAP(CInterfaceMapUDFDialog, CDialog)
	//{{AFX_MSG_MAP(CInterfaceMapUDFDialog)
	ON_BN_CLICKED(IDHELP, OnHelp)
	ON_WM_HELPINFO()
	ON_CBN_SELCHANGE(IDC_ELEMENT_TYPE, OnSelchangeElementType)
	//}}AFX_MSG_MAP
END_MESSAGE_MAP()

/////////////////////////////////////////////////////////////////////////////
// CInterfaceMapUDFDialog message handlers

BOOL CInterfaceMapUDFDialog::OnInitDialog() 
{
	CDialog::OnInitDialog();
	CRect r;
	CUtilityHelper utilityHelper;
	int n;

	m_ElementListCtrl.GetWindowRect(&r);
	m_ElementListCtrl.SetWindowPos(NULL, 0, 0, r.Width(), r.Height()*10, SWP_NOMOVE|SWP_NOZORDER);

	m_TypeListCtrl.GetWindowRect(&r);
	m_TypeListCtrl.SetWindowPos(NULL, 0, 0, r.Width(), r.Height()*4, SWP_NOMOVE|SWP_NOZORDER);

	for (int i=0; i < m_ValidElementTypes.GetSize(); ++i) {
		n = m_ElementListCtrl.AddString(utilityHelper.GetElementTypeAsText(m_ValidElementTypes[i]));
		m_ElementListCtrl.SetItemData(n, m_ValidElementTypes[i]);
		if (m_ElementType == m_ValidElementTypes[i])
			m_ElementListCtrl.SetCurSel(n);
	}

	if (m_ValidElementTypes.GetSize() == 1)
		m_ElementListCtrl.SetCurSel(0);
		
	if (m_ElementListCtrl.GetCurSel() >= 0) {
		LoadDataTypeList();
		if (m_DataType > 0 && m_TypeListCtrl.GetCount() > 0) {
			for (int j=0; j < m_TypeListCtrl.GetCount(); ++j) {
				if (m_TypeListCtrl.GetItemData(j) == (unsigned long)m_DataType) {
					m_TypeListCtrl.SetCurSel(j);
					break;
				}
			}
		}

		CEdit *pEdit = (CEdit *)GetDlgItem(IDC_NAME);
		pEdit->SetFocus();
		pEdit->SetSel(0, -1);
	
	}
	return FALSE;  // return TRUE unless you set the focus to a control
	              // EXCEPTION: OCX Property Pages should return FALSE
}

void CInterfaceMapUDFDialog::OnOK() 
{

	UpdateData(TRUE);
	int curSel;

	curSel = m_ElementListCtrl.GetCurSel();
	if (curSel < 0) {
		AfxMessageBox("Please select an element type.");
		m_ElementListCtrl.SetFocus();
		m_ElementListCtrl.ShowDropDown();
		return;
	}

	m_ElementType = m_ElementListCtrl.GetItemData(curSel);

	curSel = m_TypeListCtrl.GetCurSel();
	if (curSel < 0) {
		AfxMessageBox("Please select a data type.");
		m_TypeListCtrl.SetFocus();
		m_TypeListCtrl.ShowDropDown();
		return;
	}

	m_DataType = m_TypeListCtrl.GetItemData(curSel);

	if (m_Name == "") {
		AfxMessageBox("Please enter a name.");
		GetDlgItem(IDC_NAME)->SetFocus();
	}


	CDialog::OnOK();
}

void CInterfaceMapUDFDialog::OnCancel() 
{
	// TODO: Add extra cleanup here
	
	CDialog::OnCancel();
}

void CInterfaceMapUDFDialog::OnHelp() 
{
	// TODO: Add your control notification handler code here
	
}

BOOL CInterfaceMapUDFDialog::OnHelpInfo(HELPINFO* pHelpInfo) 
{
	// TODO: Add your message handler code here and/or call default
	
	return CDialog::OnHelpInfo(pHelpInfo);
}

void CInterfaceMapUDFDialog::OnSelchangeElementType() 
{
	int curSel = m_ElementListCtrl.GetCurSel();
	CUtilityHelper utilityHelper;

	if (curSel < 0)
		return;

	LoadDataTypeList();

}

void CInterfaceMapUDFDialog::LoadDataTypeList()
{
	int n, curSel;
	CUtilityHelper utilityHelper;
	
	curSel = m_ElementListCtrl.GetCurSel();
	if (curSel < 0)
		return;

	m_TypeListCtrl.ResetContent();
	n =	m_TypeListCtrl.AddString(utilityHelper.GetDataTypeAsText(DT_INT));
	m_TypeListCtrl.SetItemData(n, DT_INT);
	
	n =	m_TypeListCtrl.AddString(utilityHelper.GetDataTypeAsText(DT_FLOAT));
	m_TypeListCtrl.SetItemData(n, DT_FLOAT);
	
	n =	m_TypeListCtrl.AddString(utilityHelper.GetDataTypeAsText(DT_STRING));
	m_TypeListCtrl.SetItemData(n, DT_STRING);

	if (m_ElementListCtrl.GetItemData(curSel) != UDF_LEVEL_PROFILE) {
		n =	m_TypeListCtrl.AddString(utilityHelper.GetDataTypeAsText(DT_LIST));
		m_TypeListCtrl.SetItemData(n, DT_LIST);
	}
}

// BayProfileListDialog.cpp : implementation file
//

#include "stdafx.h"
#include "modal.h"
#include <adscodes.h>

#include "BayProfileListDialog.h"
#include "FacilityHelper.h"
#include "AutoCADCommands.h"
#include "ControlService.h"
#include "Constants.h"
#include "BayProfileSheet.h"
#include "BayProfileDataService.h"
#include "UtilityHelper.h"
#include "ResourceHelper.h"
#include "WizardHelper.h"
#include "HelpService.h"

#ifdef _DEBUG
#define new DEBUG_NEW
#undef THIS_FILE
static char THIS_FILE[] = __FILE__;
#endif

/////////////////////////////////////////////////////////////////////////////
// CBayProfileListDialog dialog

extern CControlService controlService;
extern CBayProfileDataService bayProfileDataService;
extern CUtilityHelper utilityHelper;
extern CFacilityHelper facilityHelper;
extern CHelpService helpService;

CBayProfileListDialog::CBayProfileListDialog(CWnd* pParent /*=NULL*/)
	: CDialog(CBayProfileListDialog::IDD, pParent)
{
	//{{AFX_DATA_INIT(CBayProfileListDialog)
	m_HideInActive = FALSE;
	m_HideExcluded = FALSE;
	//}}AFX_DATA_INIT
	m_IsModeless = FALSE;
	m_pBayProfile = NULL;
}

CBayProfileListDialog::~CBayProfileListDialog()
{
	for (int i=0; i < m_BayProfileList.GetSize(); ++i)
		delete m_BayProfileList[i];

}

void CBayProfileListDialog::DoDataExchange(CDataExchange* pDX)
{
	CDialog::DoDataExchange(pDX);
	//{{AFX_DATA_MAP(CBayProfileListDialog)
	DDX_Control(pDX, IDC_BAY_PROFILE_TREE, m_ProfileTreeCtrl);
	DDX_Check(pDX, IDC_ACTIVE_CHECKBOX, m_HideInActive);
	DDX_Check(pDX, IDC_EXCLUDED_CHECKBOX, m_HideExcluded);
	//}}AFX_DATA_MAP
}


BEGIN_MESSAGE_MAP(CBayProfileListDialog, CDialog)
	//{{AFX_MSG_MAP(CBayProfileListDialog)
	ON_BN_CLICKED(IDC_NEW, OnNew)
	ON_BN_CLICKED(IDC_EDIT, OnEdit)
	ON_BN_CLICKED(IDC_COPY, OnCopy)
	ON_BN_CLICKED(IDC_DELETE, OnDelete)
	ON_BN_CLICKED(IDC_EXCLUDE, OnExclude)
	ON_NOTIFY(NM_DBLCLK, IDC_BAY_PROFILE_TREE, OnDblclkBayProfileTree)
	ON_BN_CLICKED(IDC_VIEW_DRAWING, OnViewDrawing)
	ON_BN_CLICKED(IDC_EXCLUDED_CHECKBOX, OnExcludedCheckbox)
	ON_BN_CLICKED(IDC_ACTIVE_CHECKBOX, OnActiveCheckbox)
	ON_NOTIFY(TVN_ENDLABELEDIT, IDC_BAY_PROFILE_TREE, OnEndlabeleditBayProfileTree)
	ON_WM_CONTEXTMENU()
	ON_NOTIFY(TVN_BEGINLABELEDIT, IDC_BAY_PROFILE_TREE, OnBeginlabeleditBayProfileTree)
	ON_NOTIFY(TVN_ITEMEXPANDED, IDC_BAY_PROFILE_TREE, OnItemexpandedBayProfileTree)
	ON_BN_CLICKED(IDC_SIDE_WIZARD, OnSideWizard)
	ON_BN_CLICKED(IDC_AISLE_WIZARD, OnAisleWizard)
	ON_BN_CLICKED(IDC_MAIN_WIZARD, OnMainWizard)
	ON_WM_HELPINFO()
	ON_BN_CLICKED(IDHELP, OnHelp)
	//}}AFX_MSG_MAP
END_MESSAGE_MAP()

/////////////////////////////////////////////////////////////////////////////
// CBayProfileListDialog message handlers

BOOL CBayProfileListDialog::OnInitDialog() 
{
	CDialog::OnInitDialog();
	

	if (m_pBayProfile == NULL) {
		LoadBayProfileList();
		BuildBayProfileTree();
	}
	else {
		BuildBayProfileTree();

		HTREEITEM hParentItem;
		
		if (m_MapBayTypeToTree.Lookup(m_pBayProfile->m_BayType, hParentItem)) {
			
			HTREEITEM hItem = m_ProfileTreeCtrl.GetChildItem(hParentItem);
			while (hItem != NULL) {
				
				CBayProfile *pTempProfile = (CBayProfile *)m_ProfileTreeCtrl.GetItemData(hItem);
				
				if (m_pBayProfile->m_BayProfileDBId == pTempProfile->m_BayProfileDBId) {
					m_ProfileTreeCtrl.SelectItem(hItem);
					break;
				}
				
				HTREEITEM hNextItem = m_ProfileTreeCtrl.GetNextItem(hItem, TVGN_NEXT);
				hItem = hNextItem;
			}
		}
	}

	return TRUE;  // return TRUE unless you set the focus to a control
	              // EXCEPTION: OCX Property Pages should return FALSE
}

void CBayProfileListDialog::OnNew() 
{

	HTREEITEM hItem = m_ProfileTreeCtrl.GetSelectedItem();
	if (hItem == NULL) {
		AfxMessageBox("Please select a bay type item to use as the template for the new profile.");
		return;
	}

	HTREEITEM hParentItem = m_ProfileTreeCtrl.GetParentItem(hItem);
	int bayType;
	if (hParentItem == NULL) {
		bayType = m_ProfileTreeCtrl.GetItemData(hItem);
		hParentItem = hItem;
	}
	else
		bayType = m_ProfileTreeCtrl.GetItemData(hParentItem);

	CBayProfile *pBayProfile = new CBayProfile;
	
	pBayProfile->m_BayType = bayType;
	pBayProfile->m_BayProfileDBId = 0;
	pBayProfile->m_Description = "New";

	// Every bay has at least the floor as a level
	CLevelProfile *pLevelProfile = new CLevelProfile();
	pLevelProfile->m_Coordinates.m_Z = 0;
	pLevelProfile->m_Description = "0";
	pLevelProfile->m_Baytype = bayType;
	pLevelProfile->m_IsBarHidden = 0;
	pLevelProfile->m_Thickness = 0;
	pBayProfile->m_LevelProfileList.Add(pLevelProfile);

	CWizardHelper wizardHelper;

	if (wizardHelper.ShowBayWizard(pBayProfile) < 0) {
		delete pBayProfile;
		return;
	}

	m_BayProfileList.Add(pBayProfile);
	hItem = m_ProfileTreeCtrl.InsertItem(pBayProfile->m_Description, 2, 2, hParentItem, TVI_LAST);
	m_ProfileTreeCtrl.SetItemData(hItem, (unsigned long)pBayProfile);
	m_ProfileTreeCtrl.SortChildren(hParentItem);

}

void CBayProfileListDialog::OnEdit() 
{
	// Hack because tree control doesn't handle return or escape keys properly
	// Since this is the default button, it will get the return message when
	// they finish editing a label
	if (IsTreeCtrlEditMessage(VK_RETURN))
		return;

	CBayProfile *pBayProfile;
	
	HTREEITEM hItem = m_ProfileTreeCtrl.GetSelectedItem();
	if (hItem == NULL) {
		AfxMessageBox("Please select the bay profile you wish to edit.");
		return;
	}
	HTREEITEM hParentItem = m_ProfileTreeCtrl.GetParentItem(hItem);
	if (hParentItem == NULL) {
		AfxMessageBox("Please select a specific bay profile to edit.");
		return;
	}
	else {
		pBayProfile = (CBayProfile *)m_ProfileTreeCtrl.GetItemData(hItem);
	}

	CString oldDesc = pBayProfile->m_Description;

	CWizardHelper wizardHelper;
	if (wizardHelper.ShowBayWizard(pBayProfile) < 0)
		return;
		
	if (pBayProfile->m_Description != oldDesc) {
		m_ProfileTreeCtrl.SetItemText(hItem, pBayProfile->m_Description);
		m_ProfileTreeCtrl.SortChildren(hParentItem);
	}
	
	return;
}

void CBayProfileListDialog::OnCopy() 
{
	CBayProfile *pBayProfile;
	
	HTREEITEM hCopyItem = m_ProfileTreeCtrl.GetSelectedItem();
	if (hCopyItem == NULL) {
		AfxMessageBox("Please select the bay profile you wish to copy.");
		return;
	}
	HTREEITEM hParentItem = m_ProfileTreeCtrl.GetParentItem(hCopyItem);
	if (hParentItem == NULL) {
		AfxMessageBox("Please select a specific bay profile to copy.");
		return;
	}
	else {
		pBayProfile = (CBayProfile *)m_ProfileTreeCtrl.GetItemData(hCopyItem);
	}
	CWaitCursor cwc;

	if (pBayProfile->m_LevelProfileList.GetSize() == 0) {	// hasn't been loaded yet
		try {
			bayProfileDataService.GetBayProfile(pBayProfile->m_BayProfileDBId, *pBayProfile);
		}
		catch (...) {
			AfxMessageBox("Error loading bay profile.");
			return;
		}
	}

	CBayProfile *pNewProfile = new CBayProfile;
	*pNewProfile = *pBayProfile;

	int idx = 1;
	CString temp;
	temp.Format("Copy of %s", pBayProfile->m_Description);

	for (int i = 0; i < m_BayProfileList.GetSize(); ++i) {	
		if (m_BayProfileList[i]->m_Description.Find(temp) >= 0)
			idx++;
	}
	
	if (idx > 1)
		pNewProfile->m_Description.Format("%s (%d)", temp, idx);
	else
		pNewProfile->m_Description = temp;

	pNewProfile->m_BayProfileDBId = 0;
	pNewProfile->m_Active = 0;
	
	// Reset all dbids to 0
	int j;
	for (i=0; i < pNewProfile->m_LevelProfileList.GetSize(); ++i) {
		pNewProfile->m_LevelProfileList[i]->m_LevelProfileDBId = 0;
		pNewProfile->m_LevelProfileList[i]->m_BayProfileDBId = 0;

		for (j=0; j < pNewProfile->m_LevelProfileList[i]->m_LocationProfileList.GetSize(); ++j) {
			pNewProfile->m_LevelProfileList[i]->m_LocationProfileList[j]->m_LocationProfileDBId = 0;
			pNewProfile->m_LevelProfileList[i]->m_LocationProfileList[j]->m_LevelProfileDBId = 0;
		}

		for (j=0; j < pNewProfile->m_LevelProfileList[i]->m_LevelLaborProfileList.GetSize(); ++j) {
			pNewProfile->m_LevelProfileList[i]->m_LevelLaborProfileList[j]->m_LevelLaborProfileDBId = 0;
			pNewProfile->m_LevelProfileList[i]->m_LevelLaborProfileList[j]->m_LevelProfileDBId = 0;
		}

		for (j=0; j < pNewProfile->m_LevelProfileList[i]->m_ExternalInfoList.GetSize(); ++j) {
			pNewProfile->m_LevelProfileList[i]->m_ExternalInfoList[j]->m_LevelProfileDBId = 0;
			pNewProfile->m_LevelProfileList[i]->m_ExternalInfoList[j]->m_LevelProfileInfoDBId = 0;
		}
	}

	for (i=0; i < pNewProfile->m_BayRuleList.GetSize(); ++i) {
		pNewProfile->m_BayRuleList[i]->m_BayRuleDBId = 0;
		pNewProfile->m_BayRuleList[i]->m_BayProfileDBId = 0;
		pNewProfile->m_BayRuleList[i]->m_Description = pNewProfile->m_Description;

		for (j=0; j < pNewProfile->m_BayRuleList[i]->m_FacingInfoList.GetSize(); ++j) {
			pNewProfile->m_BayRuleList[i]->m_FacingInfoList[j]->m_FacingInfoDBId = 0;
			pNewProfile->m_BayRuleList[i]->m_FacingInfoList[j]->m_BayRuleDBId = 0;
		}
	}

	try {
		bayProfileDataService.StoreBayProfile(*pNewProfile);
	}
	catch (...) {
		utilityHelper.ProcessError("Error storing new bay profile.");
		delete pNewProfile;
		return;
	}

	HTREEITEM hItem = m_ProfileTreeCtrl.InsertItem(pNewProfile->m_Description, 2, 2, hParentItem, TVI_LAST);
	m_ProfileTreeCtrl.SetItemData(hItem, (unsigned long)pNewProfile);
	m_ProfileTreeCtrl.SelectItem(hItem);

	m_BayProfileList.Add(pNewProfile);
	m_ProfileTreeCtrl.SortChildren(hParentItem);
	
	m_ProfileTreeCtrl.EditLabel(hItem);

}


void CBayProfileListDialog::OnDelete() 
{
	CBayProfile *pBayProfile;
	
	HTREEITEM hItem = m_ProfileTreeCtrl.GetSelectedItem();
	if (hItem == NULL) {
		AfxMessageBox("Please select the bay profile you wish to delete.");
		return;
	}
	HTREEITEM hParentItem = m_ProfileTreeCtrl.GetParentItem(hItem);
	if (hParentItem == NULL) {
		AfxMessageBox("Please select a specific bay profile to delete.");
		return;
	}
	else {
		pBayProfile = (CBayProfile *)m_ProfileTreeCtrl.GetItemData(hItem);
	}

	if (pBayProfile->m_Active) {
		AfxMessageBox("The selected bay profile is active in a facility.\n"
			"Only bay profiles that are inactive may be deleted.");
		return;
	}

	CStringArray sideNames;
	try {
		bayProfileDataService.GetSideProfileNamesByBayProfile(pBayProfile->m_BayProfileDBId, sideNames);
	}
	catch (...) {
		controlService.Log("Error determining if bay profile is used in side profile.", 
			"Generic exception in GetSideProfileNamesByBayProfile.\n");
		return;
	}

	if (sideNames.GetSize() > 0) {
		CString temp;
		temp.Format("A bay profile cannot be deleted if it is being used by a side profile.\n"
			"The selected bay profile is being used by the following side profiles:\n");
		for (int i=0; i < sideNames.GetSize(); ++i) {
			temp += "\t";
			temp += sideNames[i];
			temp += "\n";
		}
		AfxMessageBox(temp);
		return;
	}

	CString temp;
	temp.Format("Delete %s", pBayProfile->m_Description);
	if (::MessageBox(this->m_hWnd, "Are you sure you wish to delete the bay profile?",
		temp, MB_YESNO) != IDYES)
		return;

	CWaitCursor cwc;
	try {
		bayProfileDataService.DeleteBayProfile(pBayProfile->m_BayProfileDBId);
	}
	catch (...) {
		utilityHelper.ProcessError("Error deleting bay profile.");
		return;
	}

	pBayProfile->m_BayProfileDBId = -1337;

	m_ProfileTreeCtrl.DeleteItem(hItem);

	for (int i=0; i < m_BayProfileList.GetSize(); ++i) {
		if (m_BayProfileList[i]->m_BayProfileDBId == -1337)
			break;
	}
	if (i < m_BayProfileList.GetSize()) {
		delete m_BayProfileList[i];
		m_BayProfileList.RemoveAt(i);
	}

}

void CBayProfileListDialog::OnExclude() 
{
	CBayProfile *pBayProfile;
	
	HTREEITEM hItem = m_ProfileTreeCtrl.GetSelectedItem();
	if (hItem == NULL) {
		AfxMessageBox("Please select the bay profile you wish to exclude from optimization.");
		return;
	}
	HTREEITEM hParentItem = m_ProfileTreeCtrl.GetParentItem(hItem);
	if (hParentItem == NULL) {
		AfxMessageBox("Please select a specific bay profile to exclude from optimization.");
		return;
	}
	else {
		pBayProfile = (CBayProfile *)m_ProfileTreeCtrl.GetItemData(hItem);
	}	

	CWaitCursor cwc;
	try {
		bayProfileDataService.SetBayProfileToExcluded(pBayProfile->m_BayProfileDBId, TRUE);
	}
	catch (...) {
		utilityHelper.ProcessError("Error updating excluded flag.");
		return;
	}

	if (m_HideExcluded) {
		m_ProfileTreeCtrl.DeleteItem(hItem);
	}

}


int CBayProfileListDialog::LoadBayProfileList()
{
	CStringArray bayNameList, strings, usedList;
	CMap<int, int, int, int> usedMap;

	CWaitCursor cwc;
	int count=0;
	if (m_BayProfileList.GetSize() > 0)
		return 0;

	bool bException = false;
	try
	{
		count = bayProfileDataService.GetBayProfileList(bayNameList);
	}
	catch (CException *e)
	{
		// MessageBox("Exception:","Occurred",0);
		utilityHelper.ProcessError("Error getting list of bay profiles.1");
		e->Delete();
		bException = true;
		return -1;
	}
	catch (...)
	{
		utilityHelper.ProcessError("Error getting list of bay profiles.2");
		bException = true;
		return -1;
	}

	if (!bException)
	{
		for (int i=0; i < bayNameList.GetSize(); ++i)
		{
			strings.RemoveAll();
			utilityHelper.ParseString(bayNameList[i], "|", strings);
			CBayProfile *pBayProfile = new CBayProfile();

			pBayProfile->m_BayProfileDBId = atoi(strings[0]);
			pBayProfile->m_Description = strings[1];
			if (pBayProfile->m_Description.Find("\\") >= 0)
				pBayProfile->m_Description.Delete(0, pBayProfile->m_Description.Find("\\")+1);

			pBayProfile->m_BayType = atoi(strings[2]);
			pBayProfile->m_ExcludeFromOptimization = atoi(strings[3]);
			pBayProfile->m_Active = atoi(strings[4]);
			m_BayProfileList.Add(pBayProfile);
		}
	}
	return 0;

}

int CBayProfileListDialog::LoadBayTypeList()
{
	m_ImageList.Create(16, 16, TRUE, 4, 1);
	m_ImageList.Add(AfxGetApp()->LoadIcon(IDI_FLDCLS));
	m_ImageList.Add(AfxGetApp()->LoadIcon(IDI_FLDOPEN));
	m_ImageList.Add(AfxGetApp()->LoadIcon(IDI_LOCATIONICON));

	m_ProfileTreeCtrl.SetImageList(&m_ImageList, TVSIL_NORMAL);

	HTREEITEM hItem;

	hItem = m_ProfileTreeCtrl.InsertItem("Bin", 1, 1, TVI_ROOT, TVI_LAST);
	m_ProfileTreeCtrl.SetItemData(hItem, BAYTYPE_BIN);
	m_MapBayTypeToTree.SetAt(BAYTYPE_BIN, hItem);

	hItem = m_ProfileTreeCtrl.InsertItem("Case Flow", 1, 1, TVI_ROOT, TVI_LAST);
	m_ProfileTreeCtrl.SetItemData(hItem, BAYTYPE_CASEFLOW);
	m_MapBayTypeToTree.SetAt(BAYTYPE_CASEFLOW, hItem);

	hItem = m_ProfileTreeCtrl.InsertItem("Drive In", 1, 1, TVI_ROOT, TVI_LAST);
	m_ProfileTreeCtrl.SetItemData(hItem, BAYTYPE_DRIVEIN);
	m_MapBayTypeToTree.SetAt(BAYTYPE_DRIVEIN, hItem);

	hItem = m_ProfileTreeCtrl.InsertItem("Floor", 1, 1, TVI_ROOT, TVI_LAST);
	m_ProfileTreeCtrl.SetItemData(hItem, BAYTYPE_FLOOR);
	m_MapBayTypeToTree.SetAt(BAYTYPE_FLOOR, hItem);

	hItem = m_ProfileTreeCtrl.InsertItem("Pallet", 1, 1, TVI_ROOT, TVI_LAST);
	m_ProfileTreeCtrl.SetItemData(hItem, BAYTYPE_PALLET);
	m_MapBayTypeToTree.SetAt(BAYTYPE_PALLET, hItem);

	hItem = m_ProfileTreeCtrl.InsertItem("Pallet Flow", 1, 1, TVI_ROOT, TVI_LAST);
	m_ProfileTreeCtrl.SetItemData(hItem, BAYTYPE_PALLETFLOW);
	m_MapBayTypeToTree.SetAt(BAYTYPE_PALLETFLOW, hItem);

	return 0;

}

void CBayProfileListDialog::OnDblclkBayProfileTree(NMHDR* pNMHDR, LRESULT* pResult) 
{
	HTREEITEM hItem = m_ProfileTreeCtrl.GetSelectedItem();
	if (hItem == NULL)
		return;

	HTREEITEM hParentItem = m_ProfileTreeCtrl.GetParentItem(hItem);
	if (hParentItem == NULL)
		return;

	OnEdit();
	
	*pResult = 1;
}

void CBayProfileListDialog::OnViewDrawing() 
{
	CBayProfile *pBayProfile;
	
	HTREEITEM hItem = m_ProfileTreeCtrl.GetSelectedItem();
	if (hItem == NULL) {
		AfxMessageBox("Please select the bay profile you wish to view.");
		return;
	}
	HTREEITEM hParentItem = m_ProfileTreeCtrl.GetParentItem(hItem);
	if (hParentItem == NULL) {
		AfxMessageBox("Please select a specific bay profile to view.");
		return;
	}
	else {
		pBayProfile = (CBayProfile *)m_ProfileTreeCtrl.GetItemData(hItem);
	}

	CWaitCursor cwc;

	if (pBayProfile->m_LevelProfileList.GetSize() == 0) {	// hasn't been loaded yet
		try {
			bayProfileDataService.GetBayProfile(pBayProfile->m_BayProfileDBId, *pBayProfile);
		}
		catch (...) {
			AfxMessageBox("Error loading bay profile.");
			return;
		}
	}

	if (! facilityHelper.CheckCurrentFacility()) {
		AfxMessageBox("Error saving current facility.");
		return;
	}
	else if (controlService.IsFacilityOpen()) {
		if (AfxMessageBox("The current facility must be closed to view the bay profile.\n"
			"Do you wish to close the facility and view the bay profile?", MB_YESNO) != IDYES)
			return;
	}
	
	m_pBayProfile = pBayProfile;
	
	EndDialog(WM_VIEW_DRAWING);

}

void CBayProfileListDialog::OnExcludedCheckbox() 
{
	UpdateData(TRUE);
	BuildBayProfileTree();
}

void CBayProfileListDialog::OnActiveCheckbox() 
{
	UpdateData(TRUE);
	BuildBayProfileTree();
}

void CBayProfileListDialog::BuildBayProfileTree()
{
	m_ProfileTreeCtrl.DeleteAllItems();

	LoadBayTypeList();

	HTREEITEM hTypeItem;
	for (int i=0; i < m_BayProfileList.GetSize(); ++i) {
		CBayProfile *pBayProfile = m_BayProfileList[i];
		if (m_HideExcluded && pBayProfile->m_ExcludeFromOptimization)
			continue;
		if (m_HideInActive && ! pBayProfile->m_Active)
			continue;

		if (m_MapBayTypeToTree.Lookup(pBayProfile->m_BayType, hTypeItem)) {
			CString temp = pBayProfile->m_Description;
			if (pBayProfile->m_Active)
				temp += " (Active)";

			HTREEITEM hItem = m_ProfileTreeCtrl.InsertItem(temp, 2, 2, hTypeItem, TVI_LAST);
			m_ProfileTreeCtrl.SetItemData(hItem, (unsigned long)pBayProfile);
		}
	}

	HTREEITEM hItem = m_ProfileTreeCtrl.GetNextItem(TVI_ROOT, TVGN_CHILD);
	HTREEITEM hTopItem = hItem;
	while (hItem != NULL) {
		m_ProfileTreeCtrl.Expand(hItem, TVE_EXPAND);
		HTREEITEM hNextItem = m_ProfileTreeCtrl.GetNextItem(hItem, TVGN_NEXT);
		hItem = hNextItem;
	}


	m_ProfileTreeCtrl.EnsureVisible(hTopItem);
}

void CBayProfileListDialog::OnEndlabeleditBayProfileTree(NMHDR* pNMHDR, LRESULT* pResult) 
{
	TV_DISPINFO* pTVDispInfo = (TV_DISPINFO*)pNMHDR;
	
	*pResult = 0;

	TVITEM tvItem;

	tvItem = pTVDispInfo->item;

	*pResult = 0;

	CBayProfile *pBayProfile = (CBayProfile *)m_ProfileTreeCtrl.GetItemData(tvItem.hItem);

	if (tvItem.pszText != NULL) {
		CString txt(tvItem.pszText);

		if (txt.FindOneOf(BAD_FILE_CHARACTERS) != -1) {
			CString temp;
			temp.Format("The following characters can not be part of the bay profile name:\n%s",
				BAD_FILE_CHARACTERS);
			AfxMessageBox(temp);
			m_ProfileTreeCtrl.EditLabel(tvItem.hItem);
			return;
		}

		for (int i=0; i < m_BayProfileList.GetSize(); ++i) {
			if (m_BayProfileList[i]->m_BayProfileDBId == pBayProfile->m_BayProfileDBId)
				continue;

			if (m_BayProfileList[i]->m_Description.Compare(txt) == 0) {
				AfxMessageBox("Please enter a bay profile name that does not already exist.");
				m_ProfileTreeCtrl.EditLabel(tvItem.hItem);
				return;
			}
		}
		*pResult = 1;
	}
	else {
		*pResult = 0;
		return;
	}

	try {
		CWaitCursor cwc;
		bayProfileDataService.UpdateBayProfileName(pBayProfile->m_BayProfileDBId, tvItem.pszText);
	}
	catch (...) {
		AfxMessageBox("Error updating bay profile name.");
		strcpy(tvItem.pszText, pBayProfile->m_Description);
		*pResult = 0;
	}
	
	pBayProfile->m_Description = tvItem.pszText;

	return;
}

void CBayProfileListDialog::OnOK() 
{
	if (m_IsModeless)
		DestroyWindow();
	else
		CDialog::OnOK();
}

BOOL CBayProfileListDialog::IsTreeCtrlEditMessage(WPARAM keyCode)
{
	BOOL rc = FALSE;

	CWnd*  focus = GetFocus();
	CEdit* edit  = m_ProfileTreeCtrl.GetEditControl();
	if ((CEdit *)focus == edit) {
		edit->SendMessage(WM_KEYDOWN, keyCode);
		rc = TRUE;
	}

	return rc;
}

void CBayProfileListDialog::OnCancel() 
{
	if (!IsTreeCtrlEditMessage(VK_ESCAPE)) {
		if (m_IsModeless)
			DestroyWindow();
		else
			CDialog::OnCancel();
	}
}

void CBayProfileListDialog::OnContextMenu(CWnd* pWnd, CPoint point) 
{
	if (pWnd != &m_ProfileTreeCtrl)
		return;

	CMenu menu;
	menu.LoadMenu(IDR_GENERIC_MENU);

	CPoint pt(point);
	m_ProfileTreeCtrl.ScreenToClient(&pt);
	UINT nFlags;
	HTREEITEM hItem = m_ProfileTreeCtrl.HitTest(pt, &nFlags);
	if (hItem == NULL)
		return;
	else
		m_ProfileTreeCtrl.SelectItem(hItem);

	HTREEITEM hParentItem = m_ProfileTreeCtrl.GetParentItem(hItem);
	if (hParentItem == NULL) {			// must have selected the top level
		menu.GetSubMenu(0)->ModifyMenu(0, MF_BYPOSITION|MF_STRING, IDC_NEW, "&New");
		menu.GetSubMenu(0)->DeleteMenu(1, MF_BYPOSITION); 
		menu.GetSubMenu(0)->DeleteMenu(1, MF_BYPOSITION);	
	}
	else {
		menu.GetSubMenu(0)->ModifyMenu(0, MF_BYPOSITION|MF_STRING, IDC_NEW, "&New");
		menu.GetSubMenu(0)->ModifyMenu(1, MF_BYPOSITION|MF_STRING, IDC_EDIT, "&Edit");
		menu.GetSubMenu(0)->ModifyMenu(2, MF_BYPOSITION|MF_STRING, IDC_COPY, "&Copy");
		menu.GetSubMenu(0)->AppendMenu(MF_BYPOSITION|MF_STRING, IDC_DELETE, "&Delete");
	}

	menu.GetSubMenu(0)->TrackPopupMenu(TPM_LEFTALIGN|TPM_RIGHTBUTTON, point.x, point.y, this);	
}


void CBayProfileListDialog::PostNcDestroy() 
{

//	if (m_IsModeless)
//		delete this;

	CDialog::PostNcDestroy();
}

void CBayProfileListDialog::OnBeginlabeleditBayProfileTree(NMHDR* pNMHDR, LRESULT* pResult) 
{
	TV_DISPINFO* pTVDispInfo = (TV_DISPINFO*)pNMHDR;

	HTREEITEM hItem = pTVDispInfo->item.hItem;
	HTREEITEM hParentItem = m_ProfileTreeCtrl.GetParentItem(hItem);
	if (hParentItem == NULL)
		*pResult = 1;
	else
		*pResult = 0;
}

void CBayProfileListDialog::OnItemexpandedBayProfileTree(NMHDR* pNMHDR, LRESULT* pResult) 
{
	NM_TREEVIEW* pNMTreeView = (NM_TREEVIEW*)pNMHDR;
	
	HTREEITEM hItem = pNMTreeView->itemNew.hItem;
	if (m_ProfileTreeCtrl.GetParentItem(hItem) == NULL) {
		if (pNMTreeView->action == TVE_EXPAND)
			m_ProfileTreeCtrl.SetItemImage(hItem, 1, 1);
		else
			m_ProfileTreeCtrl.SetItemImage(hItem, 0, 0);
	}

	*pResult = 0;
}

void CBayProfileListDialog::OnSideWizard() 
{
	EndDialog(WM_SIDE_WIZARD);	
}

void CBayProfileListDialog::OnAisleWizard() 
{
	EndDialog(WM_AISLE_WIZARD);	
}

void CBayProfileListDialog::OnMainWizard() 
{
	EndDialog(WM_MAIN_WIZARD);	
}

BOOL CBayProfileListDialog::OnHelpInfo(HELPINFO* pHelpInfo) 
{
	helpService.ShowFieldHelp(IDD, pHelpInfo->iCtrlId);

	return FALSE;

}

void CBayProfileListDialog::OnHelp() 
{
	helpService.ShowScreenHelp(IDD);

	return;

}

#if !defined(AFX_INTEGRATIONASSIGNMENTOPTIONSPAGE_H__F913F80A_82BB_429C_92E2_27CD22BDBBAF__INCLUDED_)
#define AFX_INTEGRATIONASSIGNMENTOPTIONSPAGE_H__F913F80A_82BB_429C_92E2_27CD22BDBBAF__INCLUDED_

#if _MSC_VER > 1000
#pragma once
#endif // _MSC_VER > 1000
// IntegrationAssignmentOptionsPage.h : header file
//

/////////////////////////////////////////////////////////////////////////////
// CIntegrationAssignmentOptionsPage dialog

class CIntegrationAssignmentOptionsPage : public CPropertyPage
{
	DECLARE_DYNCREATE(CIntegrationAssignmentOptionsPage)

// Construction
public:
	CIntegrationAssignmentOptionsPage();
	~CIntegrationAssignmentOptionsPage();

// Dialog Data
	//{{AFX_DATA(CIntegrationAssignmentOptionsPage)
	enum { IDD = IDD_INTEGRATION_ASSIGNMENT_OPTIONS };
	BOOL	m_AutoConfirm;
	BOOL	m_FullExport;
	BOOL	m_InboundPrompt;
	BOOL	m_OutboundPrompt;
	BOOL	m_SkipAssignment;
	BOOL	m_AllowNotIntegrated;
	//}}AFX_DATA


// Overrides
	// ClassWizard generate virtual function overrides
	//{{AFX_VIRTUAL(CIntegrationAssignmentOptionsPage)
	protected:
	virtual void DoDataExchange(CDataExchange* pDX);    // DDX/DDV support
	virtual BOOL OnNotify(WPARAM wParam, LPARAM lParam, LRESULT* pResult);
	//}}AFX_VIRTUAL

// Implementation
protected:
	// Generated message map functions
	//{{AFX_MSG(CIntegrationAssignmentOptionsPage)
	afx_msg void OnSkip();
	virtual BOOL OnInitDialog();
	afx_msg BOOL OnHelpInfo(HELPINFO* pHelpInfo);
	//}}AFX_MSG
	DECLARE_MESSAGE_MAP()

};

//{{AFX_INSERT_LOCATION}}
// Microsoft Visual C++ will insert additional declarations immediately before the previous line.

#endif // !defined(AFX_INTEGRATIONASSIGNMENTOPTIONSPAGE_H__F913F80A_82BB_429C_92E2_27CD22BDBBAF__INCLUDED_)

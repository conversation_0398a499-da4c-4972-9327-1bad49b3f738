// GenericPropertySheet.cpp : implementation file
//

#include "stdafx.h"
#include "modal.h"
#include "GenericPropertySheet.h"
#include "HelpService.h"

#ifdef _DEBUG
#define new DEBUG_NEW
#undef THIS_FILE
static char THIS_FILE[] = __FILE__;
#endif

extern CHelpService helpService;

/////////////////////////////////////////////////////////////////////////////
// CGenericPropertySheet

IMPLEMENT_DYNAMIC(CGenericPropertySheet, CPropertySheet)

CGenericPropertySheet::CGenericPropertySheet()
{
	CommonConstruct(NULL, 0);
	m_AllowUpdate = TRUE;
}

CGenericPropertySheet::CGenericPropertySheet(UINT nIDCaption, CWnd* pParentWnd, UINT iSelectPage)
	:CPropertySheet(nIDCaption, pParentWnd, iSelectPage)
{
	m_AllowUpdate = TRUE;
}

CGenericPropertySheet::CGenericPropertySheet(LPCTSTR pszCaption, CWnd* pParentWnd, UINT iSelectPage)
	:CPropertySheet(pszCaption, pParentWnd, iSelectPage)
{
	m_AllowUpdate = TRUE;
}

CGenericPropertySheet::~CGenericPropertySheet()
{
}


BEGIN_MESSAGE_MAP(CGenericPropertySheet, CPropertySheet)
	//{{AFX_MSG_MAP(CGenericPropertySheet)
	ON_WM_HELPINFO()
	ON_WM_GETDLGCODE()
	//}}AFX_MSG_MAP
END_MESSAGE_MAP()

/////////////////////////////////////////////////////////////////////////////
// CGenericPropertySheet message handlers

BOOL CGenericPropertySheet::OnHelpInfo(HELPINFO* pHelpInfo) 
{

	if (pHelpInfo->iCtrlId == IDOK ||
		pHelpInfo->iCtrlId == IDCANCEL ||
		pHelpInfo->iCtrlId == IDHELP)
		// show the generic help for the buttons
		helpService.ShowFieldHelp(1, pHelpInfo->iCtrlId);
	else {
		this->PressButton(PSBTN_HELP);
	}

	return FALSE;
}

BOOL CGenericPropertySheet::OnInitDialog() 
{
	BOOL bResult = CPropertySheet::OnInitDialog();
	
	CButton *pButton = (CButton *)GetDlgItem(IDOK);
	if (! m_AllowUpdate) {
		pButton->EnableWindow(FALSE);
	}
	
	return bResult;
}

UINT CGenericPropertySheet::OnGetDlgCode() 
{
	
	UINT x = CPropertySheet::OnGetDlgCode();
	
	return 0;
	//return CPropertySheet::OnGetDlgCode();

}

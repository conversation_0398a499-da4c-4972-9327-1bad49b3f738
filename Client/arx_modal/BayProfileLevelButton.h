#if !defined(AFX_BAYPROFILELEVELBUTTON_H__0D464310_91DE_4F5B_9EDD_746249CA1624__INCLUDED_)
#define AFX_BAYPROFILELEVELBUTTON_H__0D464310_91DE_4F5B_9EDD_746249CA1624__INCLUDED_

#if _MSC_VER > 1000
#pragma once
#endif // _MSC_VER > 1000
// BayProfileLevelButton.h : header file
//
#include "BayProfileCrossbarInfo.h"
/////////////////////////////////////////////////////////////////////////////
// CBayProfileLevelButton window

#define WM_SELECT_LEVEL WM_USER+1
#define	WM_ADD_LEVEL WM_USER+2
#define WM_DBLCLK_LEVEL WM_USER+3

class CBayProfileLevelButton : public CButton
{
// Construction
public:
	CBayProfileLevelButton();

// Attributes
public:
	CArray<CBayProfileCrossbarInfo, CBayProfileCrossbarInfo> m_CrossbarList;
	double m_UprightHeight;
	double m_BayHeight;
	double m_BayWidth;

// Operations
public:
	int SelectLevel(int level);
// Overrides
	// ClassWizard generated virtual function overrides
	//{{AFX_VIRTUAL(CBayProfileLevelButton)
	public:
	virtual void DrawItem(LPDRAWITEMSTRUCT lpDrawItemStruct);
	//}}AFX_VIRTUAL

// Implementation
public:
	virtual ~CBayProfileLevelButton();

	// Generated message map functions
protected:
	//{{AFX_MSG(CBayProfileLevelButton)
	afx_msg void OnLButtonDown(UINT nFlags, CPoint point);
	afx_msg void OnLButtonDblClk(UINT nFlags, CPoint point);
	//}}AFX_MSG

	DECLARE_MESSAGE_MAP()
private:
	int m_LogicalBayWidth;
	void SetMapping(CDC &cdc);
	void DrawDashedHorzLine(CDC &cdc, const CPoint& startPt, const CPoint& endPt);
	void DrawBox(CDC &cdc, const CRect& r, int width, BOOL bDashed);
};

/////////////////////////////////////////////////////////////////////////////

//{{AFX_INSERT_LOCATION}}
// Microsoft Visual C++ will insert additional declarations immediately before the previous line.

#endif // !defined(AFX_BAYPROFILELEVELBUTTON_H__0D464310_91DE_4F5B_9EDD_746249CA1624__INCLUDED_)

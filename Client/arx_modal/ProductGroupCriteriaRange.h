// ProductGroupCriteriaRange.h: interface for the CProductGroupCriteriaRange class.
//
//////////////////////////////////////////////////////////////////////

#if !defined(AFX_PRODUCTGROUPCRITERIARANGE_H__0DBB56D5_077B_11D5_9EC8_00C04FAC149C__INCLUDED_)
#define AFX_PRODUCTGROUPCRITERIARANGE_H__0DBB56D5_077B_11D5_9EC8_00C04FAC149C__INCLUDED_

#if _MSC_VER > 1000
#pragma once
#endif // _MSC_VER > 1000

#include "ProductGroupCriteriaQuery.h"

typedef	CTypedPtrArray<CObArray, CProductGroupCriteriaQuery*> CriteriaQueryType;

class CProductGroupCriteriaRange : public  CObject  
{
public:
	BOOL IsEqual(CProductGroupCriteriaRange &other);
	int Parse(CString &line);
	CProductGroupCriteriaRange();
	CProductGroupCriteriaRange& operator=(const CProductGroupCriteriaRange &other);
	virtual ~CProductGroupCriteriaRange();
	long m_CriteriaRangeDBID;
	long m_CriteriaDBID;
	CString m_Description;
	BOOL m_InUse;
	CriteriaQueryType m_CriteriaQueryList;
};

#endif // !defined(AFX_PRODUCTGROUPCRITERIARANGE_H__0DBB56D5_077B_11D5_9EC8_00C04FAC149C__INCLUDED_)

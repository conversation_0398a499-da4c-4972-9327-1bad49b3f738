// LevelProfile.h: interface for the CLevelProfile class.
//
//////////////////////////////////////////////////////////////////////

#if !defined(AFX_LEVELPROFILE_H__A022E2FC_437F_4C0A_89ED_33B19FBDAC55__INCLUDED_)
#define AFX_LEVELPROFILE_H__A022E2FC_437F_4C0A_89ED_33B19FBDAC55__INCLUDED_

#if _MSC_VER > 1000
#pragma once
#endif // _MSC_VER > 1000

#include "3DPoint.h"
#include "LocationProfile.h"
#include "LevelLaborProfile.h"
#include "LevelProfileExternalInfo.h"

class CLevelProfile : public CObject  
{
public:
	CLevelProfile();
	CLevelProfile(const CLevelProfile& other);
	CLevelProfile& operator=(const CLevelProfile &other);
	BOOL operator==(const CLevelProfile& other);
	BOOL operator!=(const CLevelProfile& other) { return !(*this == other);};
	virtual ~CLevelProfile();

	int Parse(CString &line);
	
	int m_LevelProfileDBId;
	CString m_Description;
	C3DPoint m_Coordinates;
	int m_RelativeLevel;
	double m_WeightCapacity;
	double m_Thickness;
	int m_IsRotateAllowed;
	int m_IsVariableWidthAllowed;
	double m_ForkFixedInsertion;
	int m_IsBarHidden;
	double m_Overhang;
	double m_MinimumLocWidth;
	double m_ProductGap;
	double m_ProductSnap;
	double m_FacingGap;
	double m_FacingSnap;
	int m_Baytype;
	double m_ForkFixedExtraction;
	int m_SelectPositions;
	int m_ReservePositions;
	double m_SelectPositionHeight;
	double m_ReservePositionHeight;
	CString m_BackfillCode;
	double m_Clearance;
	double m_StackWidth;
	double m_StackDepth;
	
	double m_MaximumCaseWeight;
	int m_MaximumCaseCount;
	int m_LocationRowCount;

	double m_FlowDifference;

	int m_BayProfileDBId;

	CTypedPtrArray<CObArray, CLocationProfile*> m_LocationProfileList;
	CTypedPtrArray<CObArray, CLevelLaborProfile*> m_LevelLaborProfileList;
	CTypedPtrArray<CObArray, CLevelProfileExternalInfo*> m_ExternalInfoList;
	CMap<int, int, CLevelProfileExternalInfo*, CLevelProfileExternalInfo*> m_ExternalInfoMap;

};

#endif // !defined(AFX_LEVELPROFILE_H__A022E2FC_437F_4C0A_89ED_33B19FBDAC55__INCLUDED_)

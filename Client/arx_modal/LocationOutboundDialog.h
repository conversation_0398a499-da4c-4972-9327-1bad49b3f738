#if !defined(AFX_LocationOutboundDIALOG_H__DE31CA4E_BF3A_11D4_9EC1_00C04FAC149C__INCLUDED_)
#define AFX_LocationOutboundDIALOG_H__DE31CA4E_BF3A_11D4_9EC1_00C04FAC149C__INCLUDED_

#if _MSC_VER > 1000
#pragma once
#endif // _MSC_VER > 1000
// LocationOutboundDialog.h : header file
//
#include "LocationOutboundInfo.h"
#include "FacilityDataService.h"

/////////////////////////////////////////////////////////////////////////////
// CLocationOutboundDialog dialog

class CLocationOutboundDialog : public CDialog
{
// Construction
public:
	long m_SectionID;
	CArray<long, long> m_SectionIDList;
	CString ConvertBayType(int bayType, int isFloating);
	CLocationOutboundDialog(CWnd* pParent = NULL);   // standard constructor
	~CLocationOutboundDialog();
	
	CString FormatLocationOutbound(int facilityId, int sectionId, const CString& location,
													 CMapStringToString &defaultInfoMap,
													 CMapStringToString &levelProfileInfoMap,
													 CMapStringToString &locationInfoMap);

	static UINT GetLocationDataThread(LPVOID pParam);

	int m_ThreadCode;
	CString m_ThreadMessage;
	CString m_FileName;

// Dialog Data
	//{{AFX_DATA(CLocationOutboundDialog)
	enum { IDD = IDD_LOCATION_INTERFACE };
	//}}AFX_DATA


// Overrides
	// ClassWizard generated virtual function overrides
	//{{AFX_VIRTUAL(CLocationOutboundDialog)
	protected:
	virtual void DoDataExchange(CDataExchange* pDX);    // DDX/DDV support
	//}}AFX_VIRTUAL

// Implementation
protected:

	// Generated message map functions
	//{{AFX_MSG(CLocationOutboundDialog)
	virtual void OnOK();
	virtual BOOL OnInitDialog();
	afx_msg void OnHelp();
	afx_msg BOOL OnHelpInfo(HELPINFO* pHelpInfo);
	//}}AFX_MSG
	DECLARE_MESSAGE_MAP()
private:
	void ShowResults(int count);
	int GetFile();

};

//{{AFX_INSERT_LOCATION}}
// Microsoft Visual C++ will insert additional declarations immediately before the previous line.

#endif // !defined(AFX_LocationOutboundDIALOG_H__DE31CA4E_BF3A_11D4_9EC1_00C04FAC149C__INCLUDED_)

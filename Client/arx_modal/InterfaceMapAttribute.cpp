// InterfaceMapAttribute.cpp: implementation of the CInterfaceMapAttribute class.
//
//////////////////////////////////////////////////////////////////////

#include "stdafx.h"
#include "modal.h"
#include "InterfaceMapAttribute.h"
#include "UtilityHelper.h"

#ifdef _DEBUG
#undef THIS_FILE
static char THIS_FILE[]=__FILE__;
#define new DEBUG_NEW
#endif

//////////////////////////////////////////////////////////////////////
// Construction/Destruction
//////////////////////////////////////////////////////////////////////

CInterfaceMapAttribute::CInterfaceMapAttribute()
{

}

CInterfaceMapAttribute::~CInterfaceMapAttribute()
{

}

CInterfaceMapAttribute& CInterfaceMapAttribute::operator=(CInterfaceMapAttribute &other)
{
	m_InterfaceMapAttributeDBID = other.m_InterfaceMapAttributeDBID;
	m_InternalAttribute = other.m_InternalAttribute;
	m_ExternalAttribute = other.m_ExternalAttribute;
	m_IsConstant = other.m_IsConstant;
	m_DataType = other.m_DataType;
	m_IsUDF = other.m_IsUDF;
	m_UDFElementType = other.m_UDFElementType;

	return *this;
}

BOOL CInterfaceMapAttribute::operator==(CInterfaceMapAttribute &other)
{
	if (m_InternalAttribute != other.m_InternalAttribute) return FALSE;
	if (m_ExternalAttribute != other.m_ExternalAttribute) return FALSE;
	if (m_IsConstant != other.m_IsConstant) return FALSE;
	if (m_DataType != other.m_DataType) return FALSE;
	if (m_IsUDF != other.m_IsUDF) return FALSE;
	if (m_UDFElementType != other.m_UDFElementType) return FALSE;

	return TRUE;
	
}


int CInterfaceMapAttribute::Parse(const CString &line)
{
	CUtilityHelper utilityHelper;
	CStringArray strings;
	
	try {	
		utilityHelper.ParseString(line, "|", strings);
		
		
		m_InterfaceMapAttributeDBID = atol(strings[0]);
		m_InternalAttribute = strings[1];
		m_ExternalAttribute = strings[2];
		m_IsConstant = (atoi(strings[3]) == 1);
		m_DataType = atoi(strings[4]);
		m_IsUDF = (atoi(strings[5]) == 1);
		m_UDFElementType = atoi(strings[6]);

	}
	catch (...) {
		return -1;
	}

	return 0;

}

// ProductInterfaceDialog.cpp : implementation file
//

#include "stdafx.h"
#include "modal.h"
#include "ProductInterfaceDialog.h"

#ifdef _DEBUG
#define new DEBUG_NEW
#undef THIS_FILE
static char THIS_FILE[] = __FILE__;
#endif

/////////////////////////////////////////////////////////////////////////////
// CProductInterfaceDialog dialog


CProductInterfaceDialog::CProductInterfaceDialog(CWnd* pParent /*=NULL*/)
	: CDialog(CProductInterfaceDialog::IDD, pParent)
{
	//{{AFX_DATA_INIT(CProductInterfaceDialog)
	m_FileName = _T("");
	//}}AFX_DATA_INIT
}


void CProductInterfaceDialog::DoDataExchange(CDataExchange* pDX)
{
	CDialog::DoDataExchange(pDX);
	//{{AFX_DATA_MAP(CProductInterfaceDialog)
	DDX_Control(pDX, IDC_MAPNAME_LIST, m_MapNameListCtrl);
	DDX_Text(pDX, IDC_FILENAME, m_FileName);
	//}}AFX_DATA_MAP
}


BEGIN_MESSAGE_MAP(CProductInterfaceDialog, CDialog)
	//{{AFX_MSG_MAP(CProductInterfaceDialog)
	ON_BN_CLICKED(IDHELP, OnHelp)
	ON_WM_HELPINFO()
	ON_BN_CLICKED(IDC_DEFINE_MAPPING, OnDefineMapping)
	ON_BN_CLICKED(IDC_BROWSE, OnBrowse)
	ON_BN_CLICKED(IDC_VALIDATE, OnValidate)
	//}}AFX_MSG_MAP
END_MESSAGE_MAP()

/////////////////////////////////////////////////////////////////////////////
// CProductInterfaceDialog message handlers

BOOL CProductInterfaceDialog::OnInitDialog() 
{
	CDialog::OnInitDialog();
	
	// TODO: Add extra initialization here
	
	return TRUE;  // return TRUE unless you set the focus to a control
	              // EXCEPTION: OCX Property Pages should return FALSE
}

void CProductInterfaceDialog::OnOK() 
{
	// TODO: Add extra validation here
	
	CDialog::OnOK();
}

void CProductInterfaceDialog::OnCancel() 
{
	// TODO: Add extra cleanup here
	
	CDialog::OnCancel();
}

void CProductInterfaceDialog::OnHelp() 
{
	// TODO: Add your control notification handler code here
	
}

BOOL CProductInterfaceDialog::OnHelpInfo(HELPINFO* pHelpInfo) 
{
	// TODO: Add your message handler code here and/or call default
	
	return CDialog::OnHelpInfo(pHelpInfo);
}

void CProductInterfaceDialog::OnDefineMapping() 
{
	// TODO: Add your control notification handler code here
	
}

void CProductInterfaceDialog::OnBrowse() 
{
	// TODO: Add your control notification handler code here
	
}

void CProductInterfaceDialog::OnValidate() 
{
	// TODO: Add your control notification handler code here
	
}

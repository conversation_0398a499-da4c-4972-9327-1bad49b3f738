// InterfaceMap.cpp: implementation of the CInterfaceMap class.
//
//////////////////////////////////////////////////////////////////////

#include "stdafx.h"
#include "modal.h"
#include "InterfaceMap.h"
#include "UtilityHelper.h"

#ifdef _DEBUG
#undef THIS_FILE
static char THIS_FILE[]=__FILE__;
#define new DEBUG_NEW
#endif

//////////////////////////////////////////////////////////////////////
// Construction/Destruction
//////////////////////////////////////////////////////////////////////

CInterfaceMap::CInterfaceMap()
{
	m_Delimiter = "";
}

CInterfaceMap::~CInterfaceMap()
{
	for (int i=0; i < m_MappedAttributeList.GetSize(); ++i)
		delete m_MappedAttributeList[i];

}

CInterfaceMap& CInterfaceMap::operator=(CInterfaceMap &other)
{
	CInterfaceMapAttribute *pAttribute;

	m_InterfaceMapDBID = other.m_InterfaceMapDBID;
	m_IsNamed = other.m_IsNamed;
	m_Name = other.m_Name;
	m_FormatType = other.m_FormatType;
	m_Delimiter = other.m_Delimiter;

	m_InterfaceMapTypeDBID = other.m_InterfaceMapTypeDBID;

	for (int i=0; i < m_MappedAttributeList.GetSize(); ++i)
		delete m_MappedAttributeList[i];

	m_MappedAttributeList.RemoveAll();

	for (i=0; i < other.m_MappedAttributeList.GetSize(); ++i) {
		pAttribute = new CInterfaceMapAttribute;
		*pAttribute = *(other.m_MappedAttributeList[i]);
		m_MappedAttributeList.Add(pAttribute);
	}
	
	return *this;

}

BOOL CInterfaceMap::operator==(CInterfaceMap &other)
{
	if (m_Name != other.m_Name) return FALSE;
	if (m_IsNamed != other.m_IsNamed) return FALSE;
	if (m_FormatType != other.m_FormatType) return FALSE;
	if (m_Delimiter != other.m_Delimiter) return FALSE;

	if (m_InterfaceMapTypeDBID != other.m_InterfaceMapTypeDBID) return FALSE;

	if (m_MappedAttributeList.GetSize() != other.m_MappedAttributeList.GetSize()) return FALSE;

	for (int i=0; i < m_MappedAttributeList.GetSize(); ++i) {
		if (! ( *(m_MappedAttributeList[i]) == *(other.m_MappedAttributeList[i]) ) ) return FALSE;
	}

	return TRUE;

}


int CInterfaceMap::Parse(const CString &line)
{
	CUtilityHelper utilityHelper;
	CStringArray strings;

	try {

		utilityHelper.ParseString(line, "|", strings);

		m_InterfaceMapDBID = atol(strings[0]);
		m_Name = strings[1];
		m_IsNamed = (atoi(strings[2]) == 1);
		m_FormatType = atoi(strings[3]);
		m_Delimiter = strings[4];
		if (m_Delimiter == "pipe")
			m_Delimiter = "|";
		m_InterfaceMapTypeDBID = atol(strings[5]);
	}
	catch (...) {
		return -1;
	}

	return 0;

}

void CInterfaceMap::Clear()
{
	m_Delimiter = "";
	m_FormatType = -1;
	m_InterfaceMapDBID = -1;
	m_IsNamed = FALSE;
	
	for (int i=0; i < m_MappedAttributeList.GetSize(); ++i)
		delete m_MappedAttributeList[i];

	m_MappedAttributeList.RemoveAll();
	m_Name = "";

}

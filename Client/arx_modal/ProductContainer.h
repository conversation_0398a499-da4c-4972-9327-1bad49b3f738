// ProductContainer.h: interface for the CProductContainer class.
//
//////////////////////////////////////////////////////////////////////

#if !defined(AFX_PRODUCTCONTAINER_H__5A7F0544_D1F6_11D4_9EC1_00C04FAC149C__INCLUDED_)
#define AFX_PRODUCTCONTAINER_H__5A7F0544_D1F6_11D4_9EC1_00C04FAC149C__INCLUDED_

#if _MSC_VER > 1000
#pragma once
#endif // _MSC_VER > 1000

class CProductContainer  : public CObject
{
public:
	CProductContainer();
	CProductContainer(const CProductContainer& other);
	virtual ~CProductContainer();
	CProductContainer& operator=(const CProductContainer &other);
	BOOL operator==(const CProductContainer &other);
	BOOL operator!=(const CProductContainer &other) { return (! (*this == other)); }

	int Parse(const CString& line);
	CString Stream();

	long m_ProductContainerDBID;
	CString m_Description;
	double m_Width;
	double m_Length;
	double m_Height;
	int m_Ti;
	int m_Hi;
	BOOL m_IsWidthOverride;
	BOOL m_IsWidthOverrideIncludeInQuery;
	BOOL m_IsLengthOverride;
	BOOL m_IsLengthOverrideIncludeInQuery;
	BOOL m_IsHeightOverride;
	BOOL m_IsHeightOverrideIncludeInQuery;

};

#endif // !defined(AFX_PRODUCTCONTAINER_H__5A7F0544_D1F6_11D4_9EC1_00C04FAC149C__INCLUDED_)

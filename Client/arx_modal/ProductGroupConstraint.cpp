// ProductGroupConstraint.cpp: implementation of the CProductGroupConstraint class.
//
//////////////////////////////////////////////////////////////////////

#include "stdafx.h"
#include "modal.h"
#include "ProductGroupConstraint.h"
#include <dbsymtb.h>

#ifdef _DEBUG
#undef THIS_FILE
static char THIS_FILE[]=__FILE__;
#define new DEBUG_NEW
#endif

//////////////////////////////////////////////////////////////////////
// Construction/Destruction
//////////////////////////////////////////////////////////////////////

CProductGroupConstraint::CProductGroupConstraint()
{
	m_ProductGroupConstraintDBID = -1;
	m_ProductGroupDBID = -1;
	m_SectionDBID = -1;
	m_SectionDescription = "";
	m_AisleDBID = -1;
	m_AisleDescription = "";
	m_SideDBID = -1;
	m_SideDescription = "";
	m_BayDBID = -1;
	m_BayDescription = "";
	m_RelativeLevel = -1;
	m_IsExclusive = FALSE;
}

CProductGroupConstraint::~CProductGroupConstraint()
{

}

CProductGroupConstraint &CProductGroupConstraint::operator=(const CProductGroupConstraint &other)
{

	m_ProductGroupConstraintDBID = other.m_ProductGroupConstraintDBID;
	m_ProductGroupDBID = other.m_ProductGroupDBID;
	m_SectionDBID = other.m_SectionDBID;
	m_SectionDescription = other.m_SectionDescription;
	m_AisleDBID = other.m_AisleDBID;
	m_AisleDescription = other.m_AisleDescription;
	m_SideDBID = other.m_SideDBID;
	m_SideDescription = other.m_SideDescription;
	m_BayDBID = other.m_BayDBID;
	m_BayDescription = other.m_BayDescription;
	m_RelativeLevel = other.m_RelativeLevel;
	m_IsExclusive = other.m_IsExclusive;

	return *this;

}

int CProductGroupConstraint::Parse(CString &line)
{
	char *str;
	char *ptr;
	CString tmp;

	tmp = line;

	try {
		tmp.Replace("||", "| |");
		tmp.Replace("||", "| |");

		str = tmp.GetBuffer(0);
		
		ptr = strtok(str, "|");
		m_ProductGroupConstraintDBID = atol(ptr);
		ptr = strtok(NULL, "|");
		m_ProductGroupDBID = atol(ptr);
		ptr = strtok(NULL, "|");
		m_SectionDBID = atol(ptr);
		ptr = strtok(NULL, "|");
		m_SectionDescription = ptr;
		ptr = strtok(NULL, "|");
		if (strcmp(ptr, " ") == 0)
			m_AisleDBID = 0;
		else
			m_AisleDBID = atol(ptr);
		ptr = strtok(NULL, "|");
		if (m_AisleDBID == 0)
			m_AisleDescription = "Any";
		else
			m_AisleDescription = ptr;
		ptr = strtok(NULL, "|");
		if (strcmp(ptr, " ") == 0)
			m_SideDBID = 0;
		else
			m_SideDBID = atol(ptr);
		ptr = strtok(NULL, "|");
		if (m_SideDBID == 0)
			m_SideDescription = "Any";
		else
			m_SideDescription = ptr;
		ptr = strtok(NULL, "|");
		if (strcmp(ptr, " ") == 0)
			m_BayDBID = 0;
		else
			m_BayDBID = atol(ptr);
		ptr = strtok(NULL, "|");
		if (m_BayDBID == 0)
			m_BayDescription = "Any";
		else
			m_BayDescription = ptr;
		ptr = strtok(NULL, "|");
		m_RelativeLevel = atoi(ptr);
		ptr = strtok(NULL, "|");
		m_IsExclusive = (atoi(ptr) == 1);
	
		tmp.ReleaseBuffer();
	}
	catch (...) {
		tmp.ReleaseBuffer();
		AfxMessageBox("Error processing product group constraint list.\n");
		ads_printf("%s\n", line);
		return -1;
	}

	return 0;
}


BOOL CProductGroupConstraint::IsEqual(CProductGroupConstraint &other)
{
	
	if (m_RelativeLevel != other.m_RelativeLevel) return FALSE;
	if (m_BayDBID != other.m_BayDBID) return FALSE;
	if (m_SideDBID != other.m_SideDBID) return FALSE;
	if (m_AisleDBID != other.m_AisleDBID) return FALSE;
	if (m_SectionDBID != other.m_SectionDBID) return FALSE;
	if (m_IsExclusive != other.m_IsExclusive) return FALSE;

	return TRUE;

}

BOOL CProductGroupConstraint::IsParent(CProductGroupConstraint &other)
{

	BOOL sectionIsParent, aisleIsParent, sideIsParent, bayIsParent, levelIsParent;

	sectionIsParent = aisleIsParent = sideIsParent = bayIsParent = levelIsParent = FALSE;

	if (m_SectionDBID == 0 || m_SectionDBID == other.m_SectionDBID)
		sectionIsParent = TRUE;

	if (m_AisleDBID == 0 || m_AisleDBID == other.m_AisleDBID)
		aisleIsParent = TRUE;

	if (m_SideDBID == 0 || m_SideDBID == other.m_SideDBID)
		sideIsParent = TRUE;

	if (m_BayDBID == 0 || m_BayDBID == other.m_BayDBID)
		bayIsParent = TRUE;

	if (m_RelativeLevel == 0 || m_RelativeLevel == other.m_RelativeLevel)
		levelIsParent = TRUE;

	if (sectionIsParent && aisleIsParent && sideIsParent && bayIsParent && levelIsParent)
		return TRUE;
	else
		return FALSE;
	
}

BOOL CProductGroupConstraint::IsChild(CProductGroupConstraint &other)
{
	UNREFERENCED_PARAMETER(other);

	return FALSE;	
}

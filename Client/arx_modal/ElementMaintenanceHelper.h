// ElementMaintenanceHelper.h: interface for the CElementMaintenanceHelper class.
//
//////////////////////////////////////////////////////////////////////

#if !defined(AFX_ELEMENTMAINTENANCEHELPER_H__0997BC05_E46A_4C05_A541_1799EBBBACD2__INCLUDED_)
#define AFX_ELEMENTMAINTENANCEHELPER_H__0997BC05_E46A_4C05_A541_1799EBBBACD2__INCLUDED_

#if _MSC_VER > 1000
#pragma once
#endif // _MSC_VER > 1000

#include "TreeElement.h"
#include "LevelLocationMaint.h"
#include "qqhclasses.h"

#include <dbents.h>
#include <dbsymtb.h>
#include <gemat3d.h>
#include <codes.h>
#include "qqhclasses.h"
#include "AisleProfileDataService.h"
#include "PickPathPropertiesDialog.h"

class CElementMaintenanceHelper  
{
public:
	void ElementMaintenance();
	int AddHotspotObject(C3DPoint &centerPoint, double diameter, int type, CString &handle);
	int DeletePickPathConnections(qqhSLOTSection &section, qqhSLOTAisle &aisle);
	BOOL PickPathOwnsPreviousConnectionLine(const CString& handle, double rotation);
	int RedrawPickPathWithoutConnection(CString &handle, int connectionAtEnd);
	void ChangeBayProfile();
	int AddBay(CBayProfile& bayProfile, const C3DPoint &centerPoint, double rotation, qqhSLOTBay &bay,
		double leftUprightWidth, double rightUprightWidth, BOOL isRotated);

	int AddBayObject(CBayProfile &bayProfile, double rotation,const C3DPoint &centerPoint, CString &handle,
		double leftUprightWidth, double rightUprightWidth);
	CElementMaintenanceHelper();
	virtual ~CElementMaintenanceHelper();
	
	void FacilityMaintenance();
	void SectionMaintenance();
	void AisleMaintenance();
	void SideMaintenance();
	void BayMaintenance();
	void LevelLocationMaintenance();

	void FacilityProperties(qqhSLOTFacility &facility);
	void SectionProperties(qqhSLOTSection &section);
	void AisleProperties(qqhSLOTAisle &aisle);
	void SideProperties(qqhSLOTSide &side);
	void BayProperties(qqhSLOTBay &bay);
	void LevelProperties(qqhSLOTLevel &level);
	int LocationProperties(qqhSLOTLocation &location, qqhSLOTLevel &level, qqhSLOTBay &bay, double totalWidth, BOOL bAllowUpdate = TRUE);

	void FacilityTreeViewer();

	void AddAisle();
	void AddPickPath();
	void DeleteBay();
	void DeleteAisle();
	void DeletePickPath();
	void AddHotSpot();
	void PickPathProperties();
	void ChangeRacktype();
	void ReAddPickPath();
	void RenumberAisle();

	int UpdateLocationsForSectionChange(int sectionOffset, qqhSLOTSection &section);
	int UpdateLocationsForAisleChange(int sectionOffset, int aisleOffset, qqhSLOTAisle &aisle);
	int UpdateLocationsForBayChange(int sectionOffset, int bayOffset, qqhSLOTBay &bay);
	int UpdateLocationsForLevelChange(int sectionOffset, int levelOffset, qqhSLOTLevel &level);
	void LevelMaintenance(TreeElement *levelPtr);
	void LocationMaintenance(TreeElement *locationPtr);
private:
	int AddNewSection(qqhSLOTSection &newSection);
	int BuildLevelLocationList(TreeElement *bayPtr, CLevelLocationMaint &levelLocDlg);

	int AddPickPathinAisle(AcDbObjectId * pathObjIdptr, int firstTime, AcGePoint3d &lastPoint);
	int CElementMaintenanceHelper::ConnectPickPaths();
	int InsertNewBayLayer(CString &BayLayerName);
	int  UpdateLocationGlobalCoords(qqhSLOTAisle & insertedAisle, int sideIndex, int bayIndex,
		CArray <float,float&> &sideBarWidth, CArray <double,double&> &side1BayYCoord,
		CArray <float, float&> &sideBayWidth, float baySpace,
		CArray <float, float&> &side1BayDepth, CArray <float,float&> &sideBayDepth,
		ads_point &holdCornerPoint, ads_point &currentBayCenter, float rotateAngle);
	int AddBayDataFromProfile(qqhSLOTBay &bay, CBayProfile &bayProfile);
	int ReAddPickPathinAisle(AcDbObjectId * pathObjIdptr, int firstTime, AcGePoint3d &lastPoint, qqhSLOTAisle &aisle);
	int PickPathPropertiesForAisle(qqhSLOTAisle &aisle);
	int GetAisleFromTreeByBay(CString bayHandle, qqhSLOTAisle &pAisle,
						  int &sectionIdx, int &aisleIdx, int &sideIdx, int &bayIdx);
//	int AddMoveStockItem(const char * itemFileName, int hideRotate, const char * displayText,
//		float & x, float & y, float & z, char * handle, float width, float depth, float height);

	void LoadPreviousPickPathValues(CPickPathPropertiesDialog &dialog);
	void SavePickPathValues(CPickPathPropertiesDialog &dialog);

	CAisleProfileDataService m_AisleProfileDataService;

};

#endif // !defined(AFX_ELEMENTMAINTENANCEHELPER_H__0997BC05_E46A_4C05_A541_1799EBBBACD2__INCLUDED_)

// UDFHelper.cpp: implementation of the CUDFHelper class.
//
//////////////////////////////////////////////////////////////////////

#include "stdafx.h"
#include "modal.h"
#include "UDFHelper.h"
#include "UDFMaintenanceDialog.h"

#ifdef _DEBUG
#undef THIS_FILE
static char THIS_FILE[]=__FILE__;
#define new DEBUG_NEW
#endif

//////////////////////////////////////////////////////////////////////
// Construction/Destruction
//////////////////////////////////////////////////////////////////////

CUDFHelper::CUDFHelper()
{

}

CUDFHelper::~CUDFHelper()
{

}


void CUDFHelper::UDFMaintenance()
{
	try {
		CUDFMaintenanceDialog dlg;
		dlg.DoModal();
	}
	catch (...) {
		AfxMessageBox("Error running UDF Maintenance.");
	}

}

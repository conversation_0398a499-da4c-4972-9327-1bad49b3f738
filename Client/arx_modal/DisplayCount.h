#if !defined(AFX_DISPLAYCOUNT_H__4ECBB223_C47C_11D4_9EC1_00C04FAC149C__INCLUDED_)
#define AFX_DISPLAYCOUNT_H__4ECBB223_C47C_11D4_9EC1_00C04FAC149C__INCLUDED_

#if _MSC_VER > 1000
#pragma once
#endif // _MSC_VER > 1000
// DisplayCount.h : header file
//

/////////////////////////////////////////////////////////////////////////////
// CDisplayCount dialog

class CDisplayCount : public CDialog
{
// Construction
public:
	int m_Count;
	~CDisplayCount();
	CSliderCtrl *m_pSliderCtrl;
	int m_Start;
	int m_End;
	int m_Freq;
	CToolTipCtrl m_ToolTip;
	CDisplayCount(CWnd* pParent = NULL);   // standard constructor

// Dialog Data
	//{{AFX_DATA(CDisplayCount)
	enum { IDD = IDD_NUMBER_OF_RESULTS };
	CSliderCtrl	m_Slider;
	CString	m_CountMsg;
	CString	m_Number;
	//}}AFX_DATA


// Overrides
	// ClassWizard generated virtual function overrides
	//{{AFX_VIRTUAL(CDisplayCount)
	public:
	virtual BOOL PreTranslateMessage(MSG* pMsg);
	protected:
	virtual void DoDataExchange(CDataExchange* pDX);    // DDX/DDV support
	//}}AFX_VIRTUAL

// Implementation
protected:

	// Generated message map functions
	//{{AFX_MSG(CDisplayCount)
	virtual BOOL OnInitDialog();
	virtual void OnOK();
	afx_msg void OnHScroll(UINT nSBCode, UINT nPos, CScrollBar* pScrollBar);
	afx_msg BOOL OnHelpInfo(HELPINFO* pHelpInfo);
	afx_msg void OnHelp();
	//}}AFX_MSG
	DECLARE_MESSAGE_MAP()
};

//{{AFX_INSERT_LOCATION}}
// Microsoft Visual C++ will insert additional declarations immediately before the previous line.

#endif // !defined(AFX_DISPLAYCOUNT_H__4ECBB223_C47C_11D4_9EC1_00C04FAC149C__INCLUDED_)

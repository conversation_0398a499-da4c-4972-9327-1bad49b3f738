#if !defined(AFX_BAYPROFILELOCATIONPAGE_H__CC9075E5_266E_4750_82E9_9CBF41B4AAD2__INCLUDED_)
#define AFX_BAYPROFILELOCATIONPAGE_H__CC9075E5_266E_4750_82E9_9CBF41B4AAD2__INCLUDED_

#if _MSC_VER > 1000
#pragma once
#endif // _MSC_VER > 1000
// BayProfileLocationPage.h : header file
//
#include "BayProfileLevelButton.h"
#include "BayProfile.h"
/////////////////////////////////////////////////////////////////////////////
// CBayProfileLocationPage dialog

class CBayProfileLocationPage : public CPropertyPage
{
	DECLARE_DYNCREATE(CBayProfileLocationPage)

// Construction
public:
	CBayProfileLocationPage();
	~CBayProfileLocationPage();

// Dialog Data
	//{{AFX_DATA(CBayProfileLocationPage)
	enum { IDD = IDD_BAY_PROFILE_LOCATION_ATTRIBUTES };
	CComboBox	m_LevelListCtrl;
	CBayProfileLevelButton	m_LevelButton;
	CString	m_Dimensions;
	CString	m_FacingGap;
	CString	m_FacingSnap;
	CString	m_MinimumWidth;
	CString	m_LocationsAcross;
	CString	m_LocationsDeep;
	CString	m_ProductGap;
	CString	m_ProductSnap;
	CString	m_LocationSpace;
	CString	m_LocationUsage;
	CString	m_HandlingMethod;
	CString	m_LocationCount;
	//}}AFX_DATA


// Overrides
	// ClassWizard generate virtual function overrides
	//{{AFX_VIRTUAL(CBayProfileLocationPage)
	public:
	virtual BOOL OnSetActive();
	virtual BOOL OnKillActive();
	protected:
	virtual void DoDataExchange(CDataExchange* pDX);    // DDX/DDV support
	virtual BOOL OnNotify(WPARAM wParam, LPARAM lParam, LRESULT* pResult);
	//}}AFX_VIRTUAL

// Implementation
public:
	CBayProfile *m_pBayProfile;
protected:
	// Generated message map functions
	//{{AFX_MSG(CBayProfileLocationPage)
	virtual BOOL OnInitDialog();
	afx_msg void OnContextMenu(CWnd* pWnd, CPoint point);
	afx_msg void OnSelchangeLevelList();
	afx_msg void OnProperties();
	afx_msg BOOL OnHelpInfo(HELPINFO* pHelpInfo);
	//}}AFX_MSG
	DECLARE_MESSAGE_MAP()
	int OnSelectLevel(WPARAM wParam, LPARAM lParam);
	int OnDblClkLevel(WPARAM wParam, LPARAM lParam);
private:
	void SetControlStates(int currentLevel);
	BOOL Validate();
	BOOL m_Validating;
	int UpdateScreenFromLevelProfile(int currentLevel);

	void RebuildLevelList();
};

//{{AFX_INSERT_LOCATION}}
// Microsoft Visual C++ will insert additional declarations immediately before the previous line.

#endif // !defined(AFX_BAYPROFILELOCATIONPAGE_H__CC9075E5_266E_4750_82E9_9CBF41B4AAD2__INCLUDED_)

// LevelProperties.cpp : implementation file
//

#include "stdafx.h"
#include "LevelProperties.h"
#include "HelpService.h"

#ifdef _DEBUG
#define new DEBUG_NEW
#undef THIS_FILE
static char THIS_FILE[] = __FILE__;
#endif

extern CHelpService helpService;

/////////////////////////////////////////////////////////////////////////////
// CLevelProperties property page

IMPLEMENT_DYNCREATE(CLevelProperties, CPropertyPage)

CLevelProperties::CLevelProperties() : CPropertyPage(CLevelProperties::IDD)
{
	//{{AFX_DATA_INIT(CLevelProperties)
	m_Coordinates = _T("");
	m_Description = _T("");
	m_FacingGap = 0.0f;
	m_FacingSnap = 0.0f;
	m_ForkFixedInsertion = 0.0f;
	m_LevelType = _T("");
	m_MinLocWidth = 0.0f;
	m_ProductGap = 0.0f;
	m_RelativeLevel = 0;
	m_RotateAllowed = FALSE;
	m_VariableWidth = FALSE;
	m_ProductSnap = 0.0f;
	//}}AFX_DATA_INIT
}

CLevelProperties::~CLevelProperties()
{
}

void CLevelProperties::DoDataExchange(CDataExchange* pDX)
{
	CPropertyPage::DoDataExchange(pDX);
	//{{AFX_DATA_MAP(CLevelProperties)
	DDX_Text(pDX, IDC_COORDINATES, m_Coordinates);
	DDX_Text(pDX, IDC_DESCRIPTION, m_Description);
	DDX_Text(pDX, IDC_FACING_GAP, m_FacingGap);
	DDX_Text(pDX, IDC_FACING_SNAP, m_FacingSnap);
	DDX_Text(pDX, IDC_FORK_FIXED_INSERTION, m_ForkFixedInsertion);
	DDX_Text(pDX, IDC_LEVEL_TYPE, m_LevelType);
	DDX_Text(pDX, IDC_MIN_LOC_WIDTH, m_MinLocWidth);
	DDX_Text(pDX, IDC_PRODUCT_GAP, m_ProductGap);
	DDX_Text(pDX, IDC_RELATIVE_LEVEL, m_RelativeLevel);
	DDX_Check(pDX, IDC_ROTATE_ALLOWED, m_RotateAllowed);
	DDX_Check(pDX, IDC_VARIABLE_WIDTH, m_VariableWidth);
	DDX_Text(pDX, IDC_PRODUCT_SNAP, m_ProductSnap);
	//}}AFX_DATA_MAP
}


BEGIN_MESSAGE_MAP(CLevelProperties, CPropertyPage)
	//{{AFX_MSG_MAP(CLevelProperties)
	ON_BN_CLICKED(IDC_VARIABLE_WIDTH, OnVariableWidth)
	ON_WM_HELPINFO()
	//}}AFX_MSG_MAP
END_MESSAGE_MAP()

/////////////////////////////////////////////////////////////////////////////
// CLevelProperties message handlers

void CLevelProperties::OnVariableWidth() 
{
	UpdateData(TRUE);
	ChangeVarWidthState(m_VariableWidth);
}

void CLevelProperties::ChangeVarWidthState(BOOL bEnabled)
{
	CEdit *pEdit;
	pEdit = (CEdit *)GetDlgItem(IDC_MIN_LOC_WIDTH);
	pEdit->EnableWindow(bEnabled);
	pEdit = (CEdit *)GetDlgItem(IDC_PRODUCT_GAP);
	pEdit->EnableWindow(bEnabled);
	pEdit = (CEdit *)GetDlgItem(IDC_PRODUCT_SNAP);
	pEdit->EnableWindow(bEnabled);
	pEdit = (CEdit *)GetDlgItem(IDC_FACING_GAP);
	pEdit->EnableWindow(bEnabled);
	pEdit = (CEdit *)GetDlgItem(IDC_FACING_SNAP);
	pEdit->EnableWindow(bEnabled);

	return;
}

BOOL CLevelProperties::OnInitDialog() 
{
	CPropertyPage::OnInitDialog();
	
	ChangeVarWidthState(m_VariableWidth);

	return TRUE;  // return TRUE unless you set the focus to a control
	              // EXCEPTION: OCX Property Pages should return FALSE
}

BOOL CLevelProperties::OnHelpInfo(HELPINFO* pHelpInfo) 
{
	helpService.ShowFieldHelp(IDD, pHelpInfo->iCtrlId);
	return FALSE;
}

BOOL CLevelProperties::OnNotify(WPARAM wParam, LPARAM lParam, LRESULT* pResult) 
{
	NMHDR* pNMHDR = (NMHDR*)lParam;
	if (pNMHDR->code == PSN_HELP) {
		helpService.ShowScreenHelp(IDD);
		return TRUE;
	}	
	return CPropertyPage::OnNotify(wParam, lParam, pResult);
}

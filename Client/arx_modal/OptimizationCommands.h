// OptimizationCommands.h: interface for the COptimizationCommands class.
//
//////////////////////////////////////////////////////////////////////

#if !defined(AFX_OPTIMIZATIONCOMMANDS_H__AD1F777F_1235_400C_B186_4B6E6537F54B__INCLUDED_)
#define AFX_OPTIMIZATIONCOMMANDS_H__AD1F777F_1235_400C_B186_4B6E6537F54B__INCLUDED_

#include "Commands.h"

#if _MSC_VER > 1000
#pragma once
#endif // _MSC_VER > 1000

class COptimizationCommands : public CCommands 
{
public:
	COptimizationCommands();
	virtual ~COptimizationCommands();
	
	static void RegisterCommands();
	static void AssignProductGroup();
	static void RunBaselineCost();
	static void UnassignProductGroup();
	static void RunManualLayout();
	static void LayoutProductPass();
	static void CapitalCostPass();
	static void LayoutProductGroupPass();
	static void CostComparison();
	static void ResetCaseCounts();
};

#endif // !defined(AFX_OPTIMIZATIONCOMMANDS_H__AD1F777F_1235_400C_B186_4B6E6537F54B__INCLUDED_)

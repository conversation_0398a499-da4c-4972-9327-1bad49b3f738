// LocationOutboundDialog.cpp : implementation file
//

#include "stdafx.h"
#include <afxmt.h>


#include "modal.h"
#include "LocationOutboundDialog.h"
#include "Constants.h"
#include "DisplayResults.h"
#include "ProcessingMessage.h"
#include "ssa_exception.h"
#include "DisplayCount.h"
#include "ControlService.h"
#include "HelpService.h"
#include "FacilityDataService.h"
#include "UtilityHelper.h"
#include "DataAccessService.h"
#include "IntegrationDataService.h"

#include "dbsymtb.h"

#ifdef _DEBUG
#define new DEBUG_NEW
#undef THIS_FILE
static char THIS_FILE[] = __FILE__;
#endif

extern CEvent g_ThreadDone;
extern CHelpService helpService;
extern CFacilityDataService facilityDataService;
extern CUtilityHelper utilityHelper;
extern CControlService controlService;
extern CDataAccessService dataAccessService;
extern CIntegrationDataService integrationDataService;

/////////////////////////////////////////////////////////////////////////////
// CLocationOutboundDialog dialog

CLocationOutboundDialog::CLocationOutboundDialog(CWnd* pParent /*=NULL*/)
	: CDialog(CLocationOutboundDialog::IDD, pParent)
{
	//{{AFX_DATA_INIT(CLocationOutboundDialog)
	//}}AFX_DATA_INIT
}

CLocationOutboundDialog::~CLocationOutboundDialog()
{
}


void CLocationOutboundDialog::DoDataExchange(CDataExchange* pDX)
{
	CDialog::DoDataExchange(pDX);
	//{{AFX_DATA_MAP(CLocationOutboundDialog)
	//}}AFX_DATA_MAP
}


BEGIN_MESSAGE_MAP(CLocationOutboundDialog, CDialog)
	//{{AFX_MSG_MAP(CLocationOutboundDialog)
	ON_BN_CLICKED(IDHELP, OnHelp)
	ON_WM_HELPINFO()
	//}}AFX_MSG_MAP
END_MESSAGE_MAP()

/////////////////////////////////////////////////////////////////////////////
// CLocationOutboundDialog message handlers

BOOL CLocationOutboundDialog::OnInitDialog() 
{
	CDialog::OnInitDialog();
	CRect r;
	CStringArray strings;
	CStringArray sectionList;
	int nItem;
	CString sectionDesc;


	CComboBox *pComboBox = (CComboBox *)GetDlgItem(IDC_SECTION);
	pComboBox->GetClientRect(&r);
	pComboBox->SetWindowPos(NULL, 0, 0, r.Width(), r.Height()*12, SWP_NOMOVE|SWP_NOZORDER);

	try {
		facilityDataService.GetSectionsByFacility(controlService.GetCurrentFacilityDBId(), sectionList);
	}
	catch (Ssa_Exception e) {
		utilityHelper.ProcessError("Error getting list of sections.", &e);
	}
	catch (...) {
		utilityHelper.ProcessError("Error getting list of sections.");
	}

	nItem = pComboBox->AddString("All");
	pComboBox->SetItemData(nItem, 0);

	for (int i=0; i < sectionList.GetSize(); ++i) {
		utilityHelper.ParseString(sectionList[i], "|", strings);
		m_SectionID = atol(strings[0]);
		sectionDesc = strings[1];
		nItem = pComboBox->AddString(sectionDesc);
		pComboBox->SetItemData(nItem, m_SectionID);
		m_SectionIDList.Add(m_SectionID);
	}

	pComboBox->SetCurSel(0);

	UpdateData(FALSE);

	m_FileName = controlService.m_ClientHome;
	m_FileName += "\\Interface\\";
	m_FileName += "dsciloc.dat";

	return TRUE;  // return TRUE unless you set the focus to a control
	              // EXCEPTION: OCX Property Pages should return FALSE
}

void CLocationOutboundDialog::OnOK() 
{
	UpdateData(TRUE);

	CComboBox *pComboBox = (CComboBox *)GetDlgItem(IDC_SECTION);

	m_SectionID = pComboBox->GetItemData(pComboBox->GetCurSel());

	if (GetFile() < 0)
		return;


	CProcessingMessage dlg("Running Location Outbound", this);

	try {
		g_ThreadDone.ResetEvent();
		CWinThread *pThread = AfxBeginThread(CLocationOutboundDialog::GetLocationDataThread, this);
		HANDLE hThread = pThread->m_hThread;

		BOOL bThreadDone = false;
		while (TRUE) {
			if ( !utilityHelper.PeekAndPump() )
				break;
			
			bThreadDone = g_ThreadDone.Lock(0);
			if (bThreadDone)
				break;
		}

	}
	catch(Ssa_Exception e) 
	{
		char eMsg[1024];
		e.GetMessage(eMsg);
		ads_printf("%s\n", eMsg);
		this->MessageBox("An error occurred while processing the location outbound.", "Error", MB_ICONERROR);
		return;
	}	
	
	if (m_ThreadMessage != "") {
		this->MessageBox(m_ThreadMessage, "Error", MB_ICONERROR);
		return;
	}

	int count = m_ThreadCode;

	if (count == 0) {
		this->MessageBox("No locations were found.", "No Records Found", MB_OK);
		return;
	}

	CDisplayCount dcDlg(this);
	dcDlg.m_Start = 0;
	dcDlg.m_End = count;

	dcDlg.DoModal();

	count = dcDlg.m_Count;
	if (count > 0)
		ShowResults(count);
}


int CLocationOutboundDialog::GetFile()
{
	CFileDialog dlgFile(FALSE);
	CString title;
	CString strFilter;
	CString strDefault;

	CString allFilter;
	VERIFY(allFilter.LoadString(AFX_IDS_ALLFILTER));
	strFilter += CString("Interface Files (*.dat)");
	strFilter += (TCHAR)'\0';   
	strFilter += _T("*.dat");
	strFilter += (TCHAR)'\0';
	strFilter += allFilter;
	strFilter += (TCHAR)'\0';   
	strFilter += _T("*.*");
	strFilter += (TCHAR)'\0';   

	dlgFile.m_ofn.lpstrFilter = strFilter;
	dlgFile.m_ofn.lpstrDefExt = "dat";
	dlgFile.m_ofn.lpstrTitle = "Location Outbound Interface File";
	dlgFile.m_ofn.lpstrFile = m_FileName.GetBuffer(_MAX_PATH);
	BOOL bResult = dlgFile.DoModal() == IDOK ? TRUE : FALSE;

	
	m_FileName.ReleaseBuffer();
	
	if (! bResult)
		return -1;
	else
		return 0;

}


CString CLocationOutboundDialog::ConvertBayType(int bayType, int isFloating)
{
	if (isFloating == 1)
		return "PR";

	switch (bayType) {
	case BAYTYPE_BIN:
		return "SF";
		break;
	case BAYTYPE_DRIVEIN:
		return "DI";
		break;
	case BAYTYPE_FLOOR:
		return "FR";
		break;
	case BAYTYPE_CASEFLOW:
		return "FW";
		break;
	case BAYTYPE_PALLET:
		return "RK";
		break;
	case BAYTYPE_PIR:
		return "PR";
		break;
	case BAYTYPE_CAROUSEL:
		return "RK";
		break;
	case BAYTYPE_PALLETFLOW:
		return "FW";
		break;
	}

	return "";


}



void CLocationOutboundDialog::ShowResults(int count)
{
	CDisplayResults dlg;
	CString tmp;
	CStdioFile f;

	AfxGetApp()->BeginWaitCursor();

	if (! f.Open(m_FileName, CFile::modeRead|CFile::typeText)) {
		tmp.Format("Unable to open file: %s\n", m_FileName);
		AfxMessageBox(tmp);
		AfxGetApp()->EndWaitCursor();
		return;
	}

	tmp = "DC|Warehouse|Location|X Coordinate|Y Coordinate|Z Coordinate|Sel Stack Pos|Sel Pos Height|Resv Stack Pos|Resv Pos Height|"
		"Stack Pos Depth|Stack Pos Width|Relative Level|Handling Method|Category|Description|Commingle Flag|"
		"Commingle Direction|Backfill|Backfill X|Backfill Y|Backfill Z|Stocker Staging|Stocker X|Stocker Y|Stocker Z|"
		"Stack Limit|Location Usage Percent|";
	dlg.m_Headers.Add(tmp);
	tmp.Format("Location Outbound Results - %s", m_FileName);
	dlg.m_WindowCaptions.Add(tmp);
	dlg.m_Tabs.Add("Locations");
	dlg.m_OrigColumnSize = 100;


	for (int i=0; i < count; ++i) {
		f.ReadString(tmp);
		tmp += "|";
		dlg.m_Data.Add(tmp);
	}

	f.Close();


	try {
		dlg.DoModal();
	}
	catch (...) {
		AfxMessageBox("Error displaying locations.");
	}

	AfxGetApp()->EndWaitCursor();


	return;

}



UINT CLocationOutboundDialog::GetLocationDataThread(LPVOID pParam)
{
	CFile file;
	CString line;
	CLocationOutboundDialog *pDlg = (CLocationOutboundDialog *)pParam;
	CStringArray locList;
	CMapStringToString defaultInfoMap, levelProfileInfoMap, locationInfoMap;
	int externalSystemId;


	try {
		CStringArray results;
		dataAccessService.ExecuteQuery("GetExternalSystemId", "select dbexternalsystemid "
			"from dbexternalsystem where name = 'SSA WM' and version in ( '2000','4000')", results, TRUE);
		if (results.GetSize() == 0) {
			pDlg->m_ThreadMessage = "Error getting WMS information.";
			pDlg->m_ThreadCode = -1;
			g_ThreadDone.SetEvent();
			return pDlg->m_ThreadCode;
		}
		externalSystemId = atoi(results[0]);
	}
	catch (...) {
		pDlg->m_ThreadCode = -1;
		pDlg->m_ThreadMessage = "Error getting WMS information.";
		g_ThreadDone.SetEvent();
		return pDlg->m_ThreadCode;
	}

	try {
		integrationDataService.GetAllExternalInfo(externalSystemId, 
			controlService.GetCurrentFacilityDBId(), pDlg->m_SectionID, defaultInfoMap,
			levelProfileInfoMap, locationInfoMap);
		
		locList.RemoveAll();
		
		integrationDataService.GetAllLocationOutboundData(controlService.GetCurrentFacilityDBId(),
			pDlg->m_SectionID, locList);
	}
	catch (...) {
		pDlg->m_ThreadCode = -1;
		pDlg->m_ThreadMessage = "Error getting location information.";
		g_ThreadDone.SetEvent();
		return pDlg->m_ThreadCode;
	}

	if (! file.Open(pDlg->m_FileName, CFile::modeCreate|CFile::modeWrite)) {
		CString msg;
		pDlg->m_ThreadMessage = "Error opening file: ";
		pDlg->m_ThreadMessage += pDlg->m_FileName;
		pDlg->m_ThreadCode = -1;
		g_ThreadDone.SetEvent();
		return pDlg->m_ThreadCode;
	}

	CString tmp = facilityDataService.GetDCBySection(controlService.GetCurrentFacilityDBId(), pDlg->m_SectionID);
	if (tmp == "")
		tmp = facilityDataService.GetCurrentDC();

	int dc = atoi(tmp);
	
	tmp = facilityDataService.GetWarehouseBySection(controlService.GetCurrentFacilityDBId(), pDlg->m_SectionID);
	if (tmp == "")
		tmp = facilityDataService.GetCurrentWarehouse();
	int warehouse = atoi(tmp);



	for (int i=0; i < locList.GetSize(); ++i) {

		CString line = pDlg->FormatLocationOutbound(dc, warehouse, locList[i],
													 defaultInfoMap, levelProfileInfoMap, locationInfoMap);

		file.Write(line, line.GetLength());
		file.Write("\r\n", 2);
	}


	file.Close();

	pDlg->m_ThreadMessage = "";
	pDlg->m_ThreadCode = locList.GetSize();

	g_ThreadDone.SetEvent();

	return 0;
}

void CLocationOutboundDialog::OnHelp() 
{
	helpService.ShowScreenHelp(IDD);	
}

BOOL CLocationOutboundDialog::OnHelpInfo(HELPINFO* pHelpInfo) 
{
	
	helpService.ShowFieldHelp(IDD, pHelpInfo->iCtrlId);

	return TRUE;
}


CString CLocationOutboundDialog::FormatLocationOutbound(int dc, int warehouse, const CString& location,
													 CMapStringToString &defaultInfoMap,
													 CMapStringToString &levelProfileInfoMap,
													 CMapStringToString &locationInfoMap)
{
	CStringArray strings;
	CString name, value, defaultValue;
	CString t, temp, line;
	
	strings.RemoveAll();
	utilityHelper.ParseString(location, "|", strings);
	
	CString locationDBId = strings[3];
	CString levelProfileDBId = strings[40];
	
	CString comingleFlag, comingleDirection, locUsagePct, stackLimit;
	
	POSITION pos = defaultInfoMap.GetStartPosition();	
	while (pos != NULL) {
		defaultInfoMap.GetNextAssoc(pos, name, defaultValue);
		
		temp.Format("%s-%s", locationDBId, name);
		if (! locationInfoMap.Lookup(temp, value)) {
			temp.Format("%s-%s", levelProfileDBId, name);
			if (! levelProfileInfoMap.Lookup(temp, value))
				value = defaultValue;
		}
		
		CString tempName(name);
		tempName.Replace(" ", "");
		if (tempName == "ComingleAllowed")
			comingleFlag = value;
		else if (tempName == "ComingleDimension")
			comingleDirection = value;
		else if (tempName == "LocationUsagePercent")
			locUsagePct = value;
		else if (tempName == "StackLimit")
			stackLimit = value;
		
	}

	CString tmp;

	line = "";

	tmp.Format("%02d|", dc);
	line += tmp;

	tmp.Format("%02d|", warehouse);
	line += tmp;
	
	BOOL isOverridden = atoi(strings[14]);
		
	tmp.Format("%8.8s|", strings[4]);		// name
	line += tmp;

	// coordinates
	tmp.Format("%05d|%05d|%05d|", atoi(strings[11]), atoi(strings[12]), atoi(strings[13]));
	line += tmp;

	tmp.Format("%04d|", atoi(strings[35]));		// selection positions
	line += tmp;
	
	if (atoi(strings[35]) == 0) { // if zero selection positions, zero out the height as well
		tmp.Format("0000|");
		line += tmp;
	}
	else {
		tmp.Format("%04f|", atof(strings[9]));		// select position height
		line += tmp;
	}

	tmp.Format("%04f|", atof(strings[36]));		// reserve positions
	line += tmp;

	if (atoi(strings[36]) == 0) { // if zero reserve positions, zero out the height as well
		tmp.Format("0000|");
		line += tmp;
	}
	else {
		tmp.Format("%04d|", atoi(strings[37]));		// reserve position height
		line += tmp;
	}

	tmp.Format("%04d|", atoi(strings[8]));		// stack-pos-depth
	line += tmp;

	tmp.Format("%04d|", atoi(strings[7]));		// stack-pos-width
	line += tmp;

	tmp.Format("%02d|", atoi(strings[32]));		// relative level
	line += tmp;

	int handling = (isOverridden) ? atoi(strings[5]) : atoi(strings[30]);

	if (handling == PALLET_HANDLING)
		tmp = "P|";
	else
		tmp = "C|";
	line += tmp;

	int category = (isOverridden) ? atoi(strings[6]) : atoi(strings[31]);
	if (category == 1)
		tmp = "S|";
	else
		tmp = "R|";
	line += tmp;

	tmp.Format("%2.2s|", ConvertBayType(atoi(strings[33]), atoi(strings[39]))); // bay type, is floating
	line += tmp;

	if (comingleFlag == "No")
		tmp = "N|";
	else
		tmp = "Y|";
	line += tmp;

	if (comingleDirection == "Height")
		tmp = "H|";
	else if (comingleDirection == "Width")
		tmp = "W|";
	else if (comingleDirection == "Depth")
		tmp = "D|";
	else
		tmp = "H|";
	line += tmp;

	CString backfillId = strings[18];
	backfillId.Replace("%L", strings[4]);
	backfillId.Replace("%l", strings[4]);

	tmp.Format("%-8.8s|", backfillId);
	line += tmp;

	if (backfillId[0] == ' ' || backfillId[0] == '\n') {

		tmp.Format("|");	// backfill x
		line += tmp;
		
		tmp.Format("|");	// backfill y
		line += tmp;
		
		tmp.Format("|");	// backfill z
		line += tmp;
			
	}
	else  {  // make these blank if backfill id is null 

		tmp.Format("%05d|", atoi(strings[19]));	// backfill x
		line += tmp;
		
		tmp.Format("%05d|", atoi(strings[20]));	// backfill y
		line += tmp;
		
		tmp.Format("%05d|", atoi(strings[21]));	// backfill z
		line += tmp;

	}

	CString stockerId = strings[22];
	stockerId.Replace("%L", strings[4]);
	stockerId.Replace("%l", strings[4]);
	//tmp.Format("%8.8s|", strings[22] != " " ? strings[22] : "");	// stocker point
	tmp.Format("%-8.8s|", stockerId);
	line += tmp;

	if (tmp[0] == ' ' || tmp[0] == '\n') {

		tmp.Format("|");	// stocker x
		line += tmp;
		
		tmp.Format("|");	// stocker y
		line += tmp;
		
		tmp.Format("|");	// stocker z
		line += tmp;
	
	}
	else {
		
		tmp.Format("%05d|", atoi(strings[23]));	// stocker x
		line += tmp;
		
		tmp.Format("%05d|", atoi(strings[24]));	// stocker y
		line += tmp;
		
		tmp.Format("%05d|", atoi(strings[25]));	// stocker z
		line += tmp;

	}

	tmp.Format("%02d|",atoi(stackLimit));		// stack limit
	line += tmp;

	tmp.Format("%03d", atoi(locUsagePct));
	line += tmp;

	return line;
}

// ProcessingMessage.h: interface for the CProcessingMessage class.
//
//////////////////////////////////////////////////////////////////////

#if !defined(AFX_PROCESSINGMESSAGE_H__1B9B8883_A26E_4E76_9B66_DC1B96C2A958__INCLUDED_)
#define AFX_PROCESSINGMESSAGE_H__1B9B8883_A26E_4E76_9B66_DC1B96C2A958__INCLUDED_

#if _MSC_VER > 1000
#pragma once
#endif // _MSC_VER > 1000
#include "Processing.h"

class CProcessingMessage  
{
public:
	void Show();
	void Hide();
	void UpdateMessage(const CString &message);
	CProcessingMessage();
	CProcessingMessage(const CString &message, CWnd *pParent);
	virtual ~CProcessingMessage();
	CProcessing *m_ProcessingDialog;
};

#endif // !defined(AFX_PROCESSINGMESSAGE_H__1B9B8883_A26E_4E76_9B66_DC1B96C2A958__INCLUDED_)

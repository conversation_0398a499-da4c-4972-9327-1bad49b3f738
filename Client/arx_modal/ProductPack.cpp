// ProductPack.cpp: implementation of the CProductPack class.
//
//////////////////////////////////////////////////////////////////////

#include "stdafx.h"
#include "ProductPack.h"
#include <dbsymtb.h>
#include "Constants.h"
#include "ssa_exception.h"
#include "UtilityHelper.h"
#include "BayProfile.h"

//////////////////////////////////////////////////////////////////////
// Construction/Destruction
//////////////////////////////////////////////////////////////////////

extern CUtilityHelper utilityHelper;

CProductPack::CProductPack()
{
	m_ProductPackDBID = -1;
	m_Description = "";
	m_WMSProductID = "";
	m_WMSProductDetailID = "";
	m_CasePack = 1;
	m_InnerPack = 1;
	m_UnitOfIssue = UOI_CASE;
	m_IsAssignmentLocked = FALSE;

	m_ProductGroupDBID = -1;
	m_ProductGroup = "";
	m_LocationDBID = -1;
	m_Location = "";
	m_CaseQuantity = 0;
	m_BayProfile = "";
	m_BayProfileDBID = -1;
	m_LevelType = "";
	m_LocProductGroupDBID = -1;
	m_LocProductGroup = "";

	m_OptimizedLocationDBID = -1;
	m_OptimizedLocation = "";
	m_OptimizedCaseQuantity = 0;
	m_OptimizedBayProfile = "";
	m_OptimizedBayProfileDBID = -1;
	m_OptimizedLevelType = "";
	m_OptimizedLocProductGroupDBID = -1;
	m_OptimizedLocProductGroup = "";


	m_Movement = 0;
	m_BalanceOnHand = 0;
	m_OptimizeBy = OptimizeByLabor;
	m_NumberOfHits = 0;
	m_IsHazard = FALSE;
	m_IsPickToBelt = FALSE;
	m_RotateXAxis = FALSE;
	m_RotateYAxis = FALSE;
	m_RotateZAxis = FALSE;

	m_CaseWidth = 0;
	m_CaseLength = 0;
	m_CaseHeight = 0;
	m_InnerWidth = 0;
	m_InnerLength = 0;
	m_InnerHeight = 0;
	m_EachWidth = 0;
	m_EachLength = 0;
	m_EachHeight = 0;

	m_NestedWidth = 0;
	m_NestedLength = 0;
	m_NestedHeight = 0;
	m_LastOptimizeAttribute = "";

	m_Weight = 0;
	m_MaxStackNumber = 0;
	m_NumberInPallet = 0;

	m_RotatedWidth = 0;
	m_RotatedLength = 0;
	m_RotatedHeight = 0;

	m_Status = 0;
	m_IsActive = 1;
	m_Trace = 0;
	m_PreviousMovement = 0;
	m_PreviousBOH = 0;
	m_CommodityType = "";
	m_CrushFactor = "";
	m_ProductKey = -1;
}

CProductPack::~CProductPack()
{
	for (int i=0; i < m_UDFList.GetSize(); ++i) {
		delete m_UDFList[i];
	}
}

CProductPack::CProductPack(const CProductPack& other)
{	
	m_ProductPackDBID = other.m_ProductPackDBID;
	m_IsProxy = other.m_IsProxy;
	m_Description = other.m_Description;
	m_WMSProductID = other.m_WMSProductID;
	m_WMSProductDetailID = other.m_WMSProductDetailID;
	m_CasePack = other.m_CasePack;
	m_InnerPack = other.m_InnerPack;
	m_UnitOfIssue = other.m_UnitOfIssue;
	m_IsAssignmentLocked = other.m_IsAssignmentLocked;

	m_ProductGroupDBID = other.m_ProductGroupDBID;
	m_ProductGroup = other.m_ProductGroup;
	m_LocationDBID = other.m_LocationDBID;
	m_Location = other.m_Location;
	m_CaseQuantity = other.m_CaseQuantity;
	m_BayProfile = other.m_BayProfile;
	m_BayProfileDBID = other.m_BayProfileDBID;
	m_LevelType = other.m_LevelType;
	m_LocProductGroupDBID = other.m_LocProductGroupDBID;
	m_LocProductGroup = other.m_LocProductGroup;

	m_OptimizedLocationDBID = other.m_OptimizedLocationDBID;
	m_OptimizedLocation = other.m_OptimizedLocation;
	m_OptimizedCaseQuantity = other.m_OptimizedCaseQuantity;
	m_OptimizedBayProfile = other.m_OptimizedBayProfile;
	m_OptimizedBayProfileDBID = other.m_OptimizedBayProfileDBID;
	m_OptimizedLevelType = other.m_OptimizedLevelType;
	m_OptimizedLocProductGroupDBID = other.m_OptimizedLocProductGroupDBID;
	m_OptimizedLocProductGroup = other.m_OptimizedLocProductGroup;

	m_Movement = other.m_Movement;
	m_BalanceOnHand = other.m_BalanceOnHand;
	m_OptimizeBy = other.m_OptimizeBy;
	m_NumberOfHits = other.m_NumberOfHits;
	m_IsHazard = other.m_IsHazard;
	m_IsPickToBelt = other.m_IsPickToBelt;
	m_RotateXAxis = other.m_RotateXAxis;
	m_RotateYAxis = other.m_RotateYAxis;
	m_RotateZAxis = other.m_RotateZAxis;

	m_CaseWidth = other.m_CaseWidth;
	m_CaseLength = other.m_CaseLength;
	m_CaseHeight = other.m_CaseHeight;
	m_InnerWidth = other.m_InnerWidth;
	m_InnerLength = other.m_InnerLength;
	m_InnerHeight = other.m_InnerHeight;
	m_EachWidth = other.m_EachWidth;
	m_EachLength = other.m_EachLength;
	m_EachHeight = other.m_EachHeight;
	
	m_NestedWidth = other.m_NestedWidth;
	m_NestedLength = other.m_NestedLength;
	m_NestedHeight = other.m_NestedHeight;

	m_LastOptimizeAttribute = other.m_LastOptimizeAttribute;

	m_Weight = other.m_Weight;
	m_MaxStackNumber = other.m_MaxStackNumber;
	m_NumberInPallet = other.m_NumberInPallet;

	m_Container = other.m_Container;

	m_RotatedWidth = other.m_RotatedWidth;
	m_RotatedLength = other.m_RotatedLength;
	m_RotatedHeight = other.m_RotatedHeight;

	m_Status = other.m_Status;
	m_IsActive = other.m_IsActive;
	m_Trace = other.m_Trace;
	m_PreviousMovement = other.m_PreviousMovement;
	m_PreviousBOH = other.m_PreviousBOH;
	m_CommodityType = other.m_CommodityType;
	m_CrushFactor = other.m_CrushFactor;
	m_ProductKey = other.m_ProductKey;

	for (int i=0; i < m_UDFList.GetSize(); ++i)
		delete m_UDFList[i];

	m_UDFList.RemoveAll();

	for (i = 0; i < other.m_UDFList.GetSize(); ++i) {
		CUDF *pNewUDF, *pUDF;
		pUDF = (CUDF *)other.m_UDFList[i];
		pNewUDF = new CUDF;
		*pNewUDF = *pUDF;
		this->m_UDFList.Add(pNewUDF);
	}

}

CProductPack& CProductPack::operator=(const CProductPack &other)
{	
	m_ProductPackDBID = other.m_ProductPackDBID;
	m_IsProxy = other.m_IsProxy;
	m_Description = other.m_Description;
	m_WMSProductID = other.m_WMSProductID;
	m_WMSProductDetailID = other.m_WMSProductDetailID;
	m_CasePack = other.m_CasePack;
	m_InnerPack = other.m_InnerPack;
	m_UnitOfIssue = other.m_UnitOfIssue;
	m_IsAssignmentLocked = other.m_IsAssignmentLocked;

	m_ProductGroupDBID = other.m_ProductGroupDBID;
	m_ProductGroup = other.m_ProductGroup;
	m_LocationDBID = other.m_LocationDBID;
	m_Location = other.m_Location;
	m_CaseQuantity = other.m_CaseQuantity;
	m_BayProfile = other.m_BayProfile;
	m_BayProfileDBID = other.m_BayProfileDBID;
	m_LevelType = other.m_LevelType;
	m_LocProductGroupDBID = other.m_LocProductGroupDBID;
	m_LocProductGroup = other.m_LocProductGroup;

	m_OptimizedLocationDBID = other.m_OptimizedLocationDBID;
	m_OptimizedLocation = other.m_OptimizedLocation;
	m_OptimizedCaseQuantity = other.m_OptimizedCaseQuantity;
	m_OptimizedBayProfile = other.m_OptimizedBayProfile;
	m_OptimizedBayProfileDBID = other.m_OptimizedBayProfileDBID;
	m_OptimizedLevelType = other.m_OptimizedLevelType;
	m_OptimizedLocProductGroupDBID = other.m_OptimizedLocProductGroupDBID;
	m_OptimizedLocProductGroup = other.m_OptimizedLocProductGroup;

	m_Movement = other.m_Movement;
	m_BalanceOnHand = other.m_BalanceOnHand;
	m_OptimizeBy = other.m_OptimizeBy;
	m_NumberOfHits = other.m_NumberOfHits;
	m_IsHazard = other.m_IsHazard;
	m_IsPickToBelt = other.m_IsPickToBelt;
	m_RotateXAxis = other.m_RotateXAxis;
	m_RotateYAxis = other.m_RotateYAxis;
	m_RotateZAxis = other.m_RotateZAxis;

	m_CaseWidth = other.m_CaseWidth;
	m_CaseLength = other.m_CaseLength;
	m_CaseHeight = other.m_CaseHeight;
	m_InnerWidth = other.m_InnerWidth;
	m_InnerLength = other.m_InnerLength;
	m_InnerHeight = other.m_InnerHeight;
	m_EachWidth = other.m_EachWidth;
	m_EachLength = other.m_EachLength;
	m_EachHeight = other.m_EachHeight;

	m_NestedWidth = other.m_NestedWidth;
	m_NestedLength = other.m_NestedLength;
	m_NestedHeight = other.m_NestedHeight;

	m_LastOptimizeAttribute = other.m_LastOptimizeAttribute;
	
	m_Weight = other.m_Weight;
	m_MaxStackNumber = other.m_MaxStackNumber;
	m_NumberInPallet = other.m_NumberInPallet;

	m_Container = other.m_Container;

	m_RotatedWidth = other.m_RotatedWidth;
	m_RotatedLength = other.m_RotatedLength;
	m_RotatedHeight = other.m_RotatedHeight;

	m_Status = other.m_Status;
	m_IsActive = other.m_IsActive;
	m_Trace = other.m_Trace;
	m_PreviousMovement = other.m_PreviousMovement;
	m_PreviousBOH = other.m_PreviousBOH;
	m_CommodityType = other.m_CommodityType;
	m_CrushFactor = other.m_CrushFactor;
	m_ProductKey = other.m_ProductKey;

	for (int i=0; i < m_UDFList.GetSize(); ++i)
		delete m_UDFList[i];

	m_UDFList.RemoveAll();

	for (i = 0; i < other.m_UDFList.GetSize(); ++i) {
		CUDF *pNewUDF, *pUDF;
		pUDF = (CUDF *)other.m_UDFList[i];
		pNewUDF = new CUDF;
		*pNewUDF = *pUDF;
		this->m_UDFList.Add(pNewUDF);
	}

	return *this;

}

BOOL CProductPack::operator==(const CProductPack& other)
{
	if (m_ProductPackDBID != other.m_ProductPackDBID) return FALSE;
	if (m_IsProxy != other.m_IsProxy) return FALSE;
	if (m_Description != other.m_Description) return FALSE;
	if (m_WMSProductID != other.m_WMSProductID) return FALSE;
	if (m_WMSProductDetailID != other.m_WMSProductDetailID) return FALSE;
	if (m_CasePack != other.m_CasePack) return FALSE;
	if (m_InnerPack != other.m_InnerPack) return FALSE;
	if (m_UnitOfIssue != other.m_UnitOfIssue) return FALSE;
	if (m_IsAssignmentLocked != other.m_IsAssignmentLocked) return FALSE;

	if (m_ProductGroupDBID != other.m_ProductGroupDBID) return FALSE;
	if (m_ProductGroup != other.m_ProductGroup) return FALSE;
	if (m_LocationDBID != other.m_LocationDBID) return FALSE;
	if (m_Location != other.m_Location) return FALSE;
	if (m_BayProfile != other.m_BayProfile) return FALSE;
	if (m_BayProfileDBID != other.m_BayProfileDBID) return FALSE;
	if (m_LevelType != other.m_LevelType) return FALSE;
	if (m_LocProductGroupDBID != other.m_LocProductGroupDBID) return FALSE;
	if (m_LocProductGroup != other.m_LocProductGroup) return FALSE;

	if (m_OptimizedLocationDBID != other.m_OptimizedLocationDBID) return FALSE;
	if (m_OptimizedLocation != other.m_OptimizedLocation) return FALSE;
	if (m_OptimizedBayProfile != other.m_OptimizedBayProfile) return FALSE;
	if (m_OptimizedBayProfileDBID != other.m_OptimizedBayProfileDBID) return FALSE;
	if (m_OptimizedLevelType != other.m_OptimizedLevelType) return FALSE;
	if (m_OptimizedLocProductGroupDBID != other.m_OptimizedLocProductGroupDBID) return FALSE;
	if (m_OptimizedLocProductGroup != other.m_OptimizedLocProductGroup) return FALSE;


	if (m_Movement != other.m_Movement) return FALSE;
	if (m_BalanceOnHand != other.m_BalanceOnHand) return FALSE;
	if (m_OptimizeBy != other.m_OptimizeBy) return FALSE;
	if (m_NumberOfHits != other.m_NumberOfHits) return FALSE;
	if (m_IsHazard != other.m_IsHazard) return FALSE;
	if (m_IsPickToBelt != other.m_IsPickToBelt) return FALSE;
	if (m_RotateXAxis != other.m_RotateXAxis) return FALSE;
	if (m_RotateYAxis != other.m_RotateYAxis) return FALSE;
	if (m_RotateZAxis != other.m_RotateZAxis) return FALSE;

	if (m_CaseWidth != other.m_CaseWidth) return FALSE;
	if (m_CaseLength != other.m_CaseLength) return FALSE;
	if (m_CaseHeight != other.m_CaseHeight) return FALSE;
	if (m_InnerWidth != other.m_InnerWidth) return FALSE;
	if (m_InnerLength != other.m_InnerLength) return FALSE;
	if (m_InnerHeight != other.m_InnerHeight) return FALSE;
	if (m_EachWidth != other.m_EachWidth) return FALSE;
	if (m_EachLength != other.m_EachLength) return FALSE;
	if (m_EachHeight != other.m_EachHeight) return FALSE;

	if (m_NestedWidth != other.m_NestedWidth) return FALSE;
	if (m_NestedLength != other.m_NestedLength) return FALSE;
	if (m_NestedHeight != other.m_NestedHeight) return FALSE;

	if (m_LastOptimizeAttribute != other.m_LastOptimizeAttribute) return FALSE;

	if (m_Weight != other.m_Weight) return FALSE;
	if (m_MaxStackNumber != other.m_MaxStackNumber) return FALSE;
	if (m_NumberInPallet != other.m_NumberInPallet) return FALSE;

	if (m_Container != other.m_Container) return FALSE;

	if (m_RotatedWidth != other.m_RotatedWidth) return FALSE;
	if (m_RotatedLength != other.m_RotatedLength) return FALSE;
	if (m_RotatedHeight != other.m_RotatedHeight) return FALSE;

	if (m_Status != other.m_Status) return FALSE;
	if (m_IsActive != other.m_IsActive) return FALSE;
	if (m_Trace != other.m_Trace) return FALSE;
	if (m_PreviousMovement != other.m_PreviousMovement) return FALSE;
	if (m_PreviousBOH != other.m_PreviousBOH) return FALSE;
	if (m_CommodityType != other.m_CommodityType) return FALSE;
	if (m_CrushFactor != other.m_CrushFactor) return FALSE;
	if (m_ProductKey != other.m_ProductKey) return FALSE;

	if (m_Container != other.m_Container) return FALSE;

	if (m_UDFList.GetSize() != other.m_UDFList.GetSize()) return FALSE;

	for (int i=0; i < other.m_UDFList.GetSize(); ++i) {
		if (*m_UDFList[i] != *other.m_UDFList[i])
			return FALSE;
	}

	return TRUE;
}

int CProductPack::ParseAll(const CString &line)
{
	char *str;
	char *ptr;
	CString tmp;
	int idx;

	// Format is:
	// DBID|Description|...|[container attributes|[UDFSTART|UDF attributes|][UDFSTART|UDF attributes]...|[other attributes]|
	tmp = line;

	try {

		idx = tmp.Find("||");
		while (idx >= 0) {
			tmp.Insert(idx+1, " ");
			idx = tmp.Find("||");
		}
		
		str = tmp.GetBuffer(0);
		
		m_IsProxy = TRUE;

		ptr = strtok(str, "|");
		m_ProductPackDBID = atoi(ptr);
		ptr = strtok(NULL, "|");
		m_Description = ptr;
		ptr = strtok(NULL, "|");
		m_WMSProductID = ptr;
		ptr = strtok(NULL, "|");
		m_WMSProductDetailID = ptr;
		ptr = strtok(NULL, "|");
		m_CasePack = atoi(ptr);
		ptr = strtok(NULL, "|");
		m_InnerPack = atoi(ptr);
		ptr = strtok(NULL, "|");
		m_UnitOfIssue = atoi(ptr);
		ptr = strtok(NULL, "|");
		if (ptr == NULL) {
			tmp.ReleaseBuffer();
			return 0;
		}

		m_IsAssignmentLocked = (atoi(ptr) == 1);

		ptr = strtok(NULL, "|");
		m_Movement = atof(ptr);
		ptr = strtok(NULL, "|");
		m_BalanceOnHand = atof(ptr);
		ptr = strtok(NULL, "|");
		m_OptimizeBy = atoi(ptr);
		ptr = strtok(NULL, "|");
		m_NumberOfHits = atof(ptr);
		ptr = strtok(NULL, "|");
		m_IsHazard = (atoi(ptr) == 1);
		ptr = strtok(NULL, "|");
		m_IsPickToBelt = (atoi(ptr) == 1);
		ptr = strtok(NULL, "|");
		m_RotateXAxis = (atoi(ptr) == 1);
		ptr = strtok(NULL, "|");
		m_RotateYAxis = (atoi(ptr) == 1);
		ptr = strtok(NULL, "|");
		m_RotateZAxis = (atoi(ptr) == 1);

		ptr = strtok(NULL, "|");
		m_CaseWidth = atof(ptr);
		ptr = strtok(NULL, "|");
		m_CaseLength = atof(ptr);
		ptr = strtok(NULL, "|");
		m_CaseHeight = atof(ptr);
		ptr = strtok(NULL, "|");
		m_InnerWidth = atof(ptr);
		ptr = strtok(NULL, "|");
		m_InnerLength = atof(ptr);
		ptr = strtok(NULL, "|");
		m_InnerHeight = atof(ptr);
		ptr = strtok(NULL, "|");
		m_EachWidth = atof(ptr);
		ptr = strtok(NULL, "|");
		m_EachLength = atof(ptr);
		ptr = strtok(NULL, "|");
		m_EachHeight = atof(ptr);
		ptr = strtok(NULL, "|");
		m_Weight = atof(ptr);
		ptr = strtok(NULL, "|");
		m_MaxStackNumber = atoi(ptr);
		ptr = strtok(NULL, "|");
		m_NumberInPallet = atoi(ptr);

		ptr = strtok(NULL, "|");
		m_Status = atoi(ptr);
		ptr = strtok(NULL, "|");
		m_IsActive = atoi(ptr);
		ptr = strtok(NULL, "|");
		m_Trace = atoi(ptr);
		ptr = strtok(NULL, "|");
		m_PreviousMovement = atof(ptr);
		ptr = strtok(NULL, "|");
		m_PreviousBOH = atof(ptr);
		ptr = strtok(NULL, "|");
		m_CommodityType = ptr;
		ptr = strtok(NULL, "|");
		m_CrushFactor = ptr;
		ptr = strtok(NULL, "|");
		m_ProductKey = atoi(ptr);
		ptr = strtok(NULL, "|");
		m_LastOptimizeAttribute = ptr;
		ptr = strtok(NULL, "|");
		m_NestedWidth = atof(ptr);
		ptr = strtok(NULL, "|");
		m_NestedLength = atof(ptr);
		ptr = strtok(NULL, "|");
		m_NestedHeight = atof(ptr);

		// The rest of the information is optional
		
		// Container Attributes
		ptr = strtok(NULL, "|");
		if (ptr == NULL) {
			tmp.ReleaseBuffer();
			return 0;
		}
		m_Container.m_ProductContainerDBID = atoi(ptr);
		ptr = strtok(NULL, "|");
		m_Container.m_Width = atof(ptr);
		ptr = strtok(NULL, "|");
		m_Container.m_Length = atof(ptr);
		ptr = strtok(NULL, "|");
		m_Container.m_Height = atof(ptr);
		ptr = strtok(NULL, "|");
		m_Container.m_Ti = atoi(ptr);
		ptr = strtok(NULL, "|");
		m_Container.m_Hi = atoi(ptr);
		/*
		ptr = strtok(NULL, "|");
		m_Container.m_IsWidthOverride = atoi(ptr);
		ptr = strtok(NULL, "|");
		m_Container.m_IsLengthOverride = atoi(ptr);
		*/
		ptr = strtok(NULL, "|");
		m_Container.m_IsHeightOverride = atoi(ptr);

		// UDFs
		ptr = strtok(NULL, "|");
		if (ptr == NULL) {
			tmp.ReleaseBuffer();
			return 0;
		}
		CUDF *pUDF;

		while (strcmp(ptr, "UDFSTART") == 0) {
			ptr = strtok(NULL, "|");
			CString udfName;
			udfName = ptr;
			BOOL found = FALSE;
			for (int i=0; i < m_UDFList.GetSize(); ++i) {
				if (m_UDFList[i]->m_Name.CompareNoCase(udfName) == 0) {
					found = TRUE;
					pUDF = m_UDFList[i];
					break;
				}
			}
			if (! found) {
				pUDF = new CUDF;
				pUDF->m_Name = udfName;
				m_UDFList.Add(pUDF);
			}

			ptr = strtok(NULL, "|");
			pUDF->m_Type = atoi(ptr);
			ptr = strtok(NULL, "|");
			pUDF->m_Value = ptr;
			if (utilityHelper.IsNumeric(pUDF->m_Value)) {
				pUDF->m_IntegerValue = atoi(pUDF->m_Value);
				pUDF->m_FloatValue = atof(pUDF->m_Value);
			}
			else {
				pUDF->m_IntegerValue = 0;
				pUDF->m_FloatValue = 0.0;
			}
			ptr = strtok(NULL, "|");
			pUDF->m_ListID = atoi(ptr);
			ptr = strtok(NULL, "|");
			pUDF->m_ValueID = atoi(ptr);
			ptr = strtok(NULL, "|");
			pUDF->m_DefaultValue = ptr;
			ptr = strtok(NULL, "|");
			if (ptr == NULL) {
				tmp.ReleaseBuffer();
				return 0;
			}
		}

		// Location Attributes
		if (strcmp(ptr, " ") != 0)
			m_LocationDBID = atoi(ptr);
		else
			m_LocationDBID = -1;
		ptr = strtok(NULL, "|");
		if (strcmp(ptr, " ") != 0)
			m_Location = ptr;
		else
			m_Location = "";
		
		ptr = strtok(NULL, "|");
		if (strcmp(ptr, " ") != 0)
			m_CaseQuantity = atoi(ptr);
		else
			m_CaseQuantity = 0;

		// Product Group Attributes
		ptr = strtok(NULL, "|");
		if (ptr == NULL) {
			tmp.ReleaseBuffer();
			return 0;
		}
		if (strcmp(ptr, " ") != 0)
			m_ProductGroupDBID = atoi(ptr);
		else
			m_ProductGroupDBID = -1;
		ptr = strtok(NULL, "|");
		if (strcmp(ptr, " ") != 0)
			m_ProductGroup = ptr;
		else
			m_ProductGroup = "";
		
		// Location Product Group Attributes
		ptr = strtok(NULL, "|");
		if (ptr == NULL) {
			tmp.ReleaseBuffer();
			return 0;
		}
		if (strcmp(ptr, " ") != 0)
			m_LocProductGroupDBID = atoi(ptr);
		else 
			m_LocProductGroupDBID = -1;
		ptr = strtok(NULL, "|");
		if (strcmp(ptr, " ") != 0)
			m_LocProductGroup = ptr;
		else
			m_LocProductGroup = "";

		// Bay Profile Attributes
		ptr = strtok(NULL, "|");
		if (ptr == NULL) {
			tmp.ReleaseBuffer();
			return 0;
		}
		if (strcmp(ptr, " ") != 0)
			m_BayProfileDBID = atoi(ptr);
		else
			m_BayProfileDBID = -1;

		ptr = strtok(NULL, "|");
		if (strcmp(ptr, " ") != 0)
			m_BayProfile = ptr;
		else
			m_BayProfile = "";

		ptr = strtok(NULL, "|");
		if (strcmp(ptr, " ") != 0) {
			int levelType = atoi(ptr);
			CBayProfile::ConvertBayType(levelType, m_LevelType);
		}
		else
			m_LevelType = "";

		// Rotated attributes
		ptr = strtok(NULL, "|");
		if (ptr == NULL) {
			tmp.ReleaseBuffer();
			return 0;
		}
		if (strcmp(ptr, " ") != 0)
			m_RotatedWidth = atof(ptr);
		else
			m_RotatedWidth = SLOT_NIL_FLOAT;
		ptr = strtok(NULL, "|");
		if (strcmp(ptr, " ") != 0)
			m_RotatedLength = atof(ptr);
		else
			m_RotatedLength = SLOT_NIL_FLOAT;
		ptr = strtok(NULL, "|");
		if (strcmp(ptr, " ") != 0)
			m_RotatedHeight = atof(ptr);
		else
			m_RotatedHeight = SLOT_NIL_FLOAT;

		m_IsProxy = FALSE;

		// Location Attributes
		ptr = strtok(NULL, "|");
		if (strcmp(ptr, " ") != 0)
			m_OptimizedLocationDBID = atoi(ptr);
		else
			m_OptimizedLocationDBID = -1;

		ptr = strtok(NULL, "|");
		if (strcmp(ptr, " ") != 0)
			m_OptimizedLocation = ptr;
		else
			m_OptimizedLocation = "";

		ptr = strtok(NULL, "|");
		if (strcmp(ptr, " ") != 0)
			m_OptimizedCaseQuantity = atoi(ptr);
		else
			m_OptimizedCaseQuantity = 0;

		
		// Location Product Group Attributes
		ptr = strtok(NULL, "|");
		if (ptr == NULL) {
			tmp.ReleaseBuffer();
			return 0;
		}
		if (strcmp(ptr, " ") != 0)
			m_OptimizedLocProductGroupDBID = atoi(ptr);
		else 
			m_OptimizedLocProductGroupDBID = -1;
		ptr = strtok(NULL, "|");
		if (strcmp(ptr, " ") != 0)
			m_OptimizedLocProductGroup = ptr;
		else
			m_OptimizedLocProductGroup = "";

		// Bay Profile Attributes
		ptr = strtok(NULL, "|");
		if (ptr == NULL) {
			tmp.ReleaseBuffer();
			return 0;
		}
		if (strcmp(ptr, " ") != 0)
			m_OptimizedBayProfileDBID = atoi(ptr);
		else
			m_OptimizedBayProfileDBID = -1;

		ptr = strtok(NULL, "|");
		if (strcmp(ptr, " ") != 0)
			m_OptimizedBayProfile = ptr;
		else
			m_OptimizedBayProfile = "";

		ptr = strtok(NULL, "|");
		if (strcmp(ptr, " ") != 0) {
			int levelType = atoi(ptr);
			CBayProfile::ConvertBayType(levelType, m_OptimizedLevelType);
		}
		else
			m_OptimizedLevelType = "";

		tmp.ReleaseBuffer();
	}
	catch (...) {
		tmp.ReleaseBuffer();
		AfxMessageBox("Error processing product list.\n");
		ads_printf("%s\n", line);
		return -1;
	}

	return 0;

}





CString CProductPack::Stream()
{
	CString stream;

	stream.Format("%d|%s|%f|%f|%f|%f|%f|"
		"%d|%d|%d|%d|%f|%d|"
		"%d|%d|%d|%d|%d|"
		"%f|%f|%f|%f|%f|%f|"
		"%d|%d|%s|%s|%f|"
		"%d|%d|%d|%f|%f|"
		"%s|%s|%d|%s|%f|%f|%f|",
		m_ProductPackDBID, m_Description, m_Weight, m_Movement, m_CaseWidth, m_CaseLength, m_CaseHeight,
		m_IsHazard, m_UnitOfIssue, m_IsPickToBelt, m_OptimizeBy, m_BalanceOnHand, m_NumberInPallet,
		m_RotateXAxis, m_RotateYAxis, m_RotateZAxis, m_IsAssignmentLocked, m_MaxStackNumber,
		m_EachWidth, m_EachLength, m_EachHeight, m_InnerWidth, m_InnerLength, m_InnerHeight,
		m_InnerPack, m_CasePack, m_WMSProductID, m_WMSProductDetailID, m_NumberOfHits, 
		m_Status, m_IsActive, m_Trace, m_PreviousMovement, m_PreviousBOH, 
		m_CommodityType, m_CrushFactor, m_ProductKey, m_LastOptimizeAttribute,
		m_NestedWidth, m_NestedLength, m_NestedHeight);

	stream += m_Container.Stream();

	CString temp;

	for (int i=0; i < m_UDFList.GetSize(); ++i) {
		CUDF *pUDF = m_UDFList[i];
		temp.Format("%s^%s|", 
			pUDF->m_Name, pUDF->m_Value);
		stream += temp;
	}

	return stream;

}


void CProductPack::GetValueByName(CString &value, CString &name)
{
	if (name.CompareNoCase("ProductPackDBID") == 0) {
		value.Format("%d", m_ProductPackDBID);
		return;
	}
	if (name.CompareNoCase("Product Name") == 0) {
		value.Format("%s", m_Description);
		return;
	}
	if (name.CompareNoCase("WMS Product ID") == 0) {
		value.Format("%s", m_WMSProductID);
		return;
	}
	if (name.CompareNoCase("WMS ProductDetail ID") == 0) {
		value.Format("%s", m_WMSProductDetailID);
		return;
	}
	if (name.CompareNoCase("Case Pack") == 0) {
		value.Format("%d", m_CasePack);
		return;
	}
	if (name.CompareNoCase("Inner Pack") == 0) {
		value.Format("%d", m_InnerPack);
		return;
	}
	if (name.CompareNoCase("Unit Of Issue") == 0) {
		switch (m_UnitOfIssue) {
		case UOI_EACH:
			value = "Each";
			break;
		case UOI_INNER:
			value = "Inner";
			break;
		case UOI_CASE:
			value = "Case";
			break;
		case UOI_PALLET:
			value = "Pallet";
			break;
		}
		return;
	}
	if (name.CompareNoCase("Assignment Locked Flag") == 0) {
		value.Format("%s", (m_IsAssignmentLocked ? "True" : "False"));
		return;
	}
	if (name.CompareNoCase("Movement") == 0) {
		value.Format("%f", m_Movement);
		return;
	}
	if (name.CompareNoCase("Balance On Hand") == 0) {
		value.Format("%f", m_BalanceOnHand);
		return;
	}
	if (name.CompareNoCase("Optimize Method") == 0) {
		value.Format("%s", ( (m_OptimizeBy == 0) ? "Cube" : "Labor") );
		return;
	}
	if (name.CompareNoCase("Number Of Hits") == 0) {
		value.Format("%f", m_NumberOfHits);
		return;
	}
	if (name.CompareNoCase("Hazard Flag") == 0) {
		value.Format("%s", (m_IsHazard ? "True" : "False") );
		return;
	}
	if (name.CompareNoCase("Pick-to-Belt Flag") == 0) {
		value.Format("%s", (m_IsPickToBelt ? "True" : "False") );
		return;
	}
	if (name.CompareNoCase("Allow Height-Length Swap") == 0) {
		value.Format("%s", (m_RotateXAxis ? "True" : "False") );
		return;
	}
	if (name.CompareNoCase("Allow Height-Width Swap") == 0) {
		value.Format("%s", (m_RotateYAxis ? "True" : "False") );
		return;
	}
	if (name.CompareNoCase("Allow Width-Length Swap") == 0) {
		value.Format("%s", (m_RotateZAxis ? "True" : "False") );
		return;
	}
	if (name.CompareNoCase("Case Width") == 0) {
		value.Format("%f", m_CaseWidth);
		return;
	}
	if (name.CompareNoCase("Case Length") == 0) {
		value.Format("%f", m_CaseLength);
		return;
	}
	if (name.CompareNoCase("Case Height") == 0) {
		value.Format("%f", m_CaseHeight);
		return;
	}
	if (name.CompareNoCase("Inner Width") == 0) {
		value.Format("%f", m_InnerWidth);
		return;
	}
	if (name.CompareNoCase("Inner Length") == 0) {
		value.Format("%f", m_InnerLength);
		return;
	}
	if (name.CompareNoCase("Inner Height") == 0) {
		value.Format("%f", m_InnerHeight);
		return;
	}
	if (name.CompareNoCase("Each Width") == 0) {
		value.Format("%f", m_EachWidth);
		return;
	}
	if (name.CompareNoCase("Each Length") == 0) {
		value.Format("%f", m_EachLength);
		return;
	}
	if (name.CompareNoCase("Each Height") == 0) {
		value.Format("%f", m_EachHeight);
		return;
	}
	if (name.CompareNoCase("Weight") == 0) {
		value.Format("%f", m_Weight);
		return;
	}
	if (name.CompareNoCase("Max Stack Number") == 0) {
		value.Format("%d", m_MaxStackNumber);
		return;
	}

	if (name.CompareNoCase("Number In Pallet") == 0) {
		value.Format("%d", m_NumberInPallet);
		return;
	}

	if (name.CompareNoCase("Status") == 0) {
		value.Format("%d", m_Status);
		return;
	}

	if (name.CompareNoCase("Is Active") == 0) {
		value.Format("%s", (m_IsActive ? "True" : "False") );
		return;
	}

	if (name.CompareNoCase("Trace") == 0) {
		value.Format("%s", (m_Trace ? "True" : "False") );
		return;
	}

	if (name.CompareNoCase("Previous Movement") == 0) {
		value.Format("%f", m_PreviousMovement);
		return;
	}
	if (name.CompareNoCase("Previous BOH") == 0) {
		value.Format("%f", m_PreviousBOH);
		return;
	}

	if (name.CompareNoCase("Commodity Type") == 0) {
		value.Format("%s", m_CommodityType);
		return;
	}

	if (name.CompareNoCase("Crush Factor") == 0) {
		value.Format("%s", m_CrushFactor);
		return;
	}

	if (name.CompareNoCase("Product Key") == 0) {
		value.Format("%d", m_ProductKey);
		return;
	}

	if (name.CompareNoCase("Container Width") == 0) {
		value.Format("%f", m_Container.m_Width);
		return;
	}
	if (name.CompareNoCase("Container Length") == 0) {
		value.Format("%f", m_Container.m_Length);
		return;
	}
	if (name.CompareNoCase("Container Height") == 0) {
		value.Format("%f", m_Container.m_Height);
		return;
	}

	if (name.CompareNoCase("Storage HI") == 0) {
		value.Format("%d", m_Container.m_Hi);
		return;
	}
	if (name.CompareNoCase("Storage TI") == 0) {
		value.Format("%d", m_Container.m_Ti);
		return;
	}
	if (name.CompareNoCase("Container Width Override Flag") == 0) {
		value.Format("%s", (m_Container.m_IsWidthOverride) ? "True" : "False");
		return;
	}
	if (name.CompareNoCase("Container Length Override Flag") == 0) {
		value.Format("%s", (m_Container.m_IsLengthOverride) ? "True" : "False");
		return;
	}
		if (name.CompareNoCase("Container Height Override Flag") == 0) {
		value.Format("%s", (m_Container.m_IsHeightOverride) ? "True" : "False");
		return;
	}
}

void CProductPack::SetValueByName(CString &value, CString &name)
{
	if (name.CompareNoCase("ProductPackDBID") == 0) {
		m_ProductPackDBID = atol(value);
		return;
	}
	if (name.CompareNoCase("Product Name") == 0) {
		m_Description = value;
		return;
	}
	if (name.CompareNoCase("WMS Product ID") == 0) {
		m_WMSProductID = value;
		return;
	}
	if (name.CompareNoCase("WMS ProductDetail ID") == 0) {
		m_WMSProductDetailID = value;
		return;
	}
	if (name.CompareNoCase("Case Pack") == 0) {
		m_CasePack = atoi(value);
		return;
	}
	if (name.CompareNoCase("Inner Pack") == 0) {
		m_InnerPack = atoi(value);
		return;
	}
	if (name.CompareNoCase("Unit Of Issue") == 0) {
		m_UnitOfIssue = atoi(value);
		return;
	}
	if (name.CompareNoCase("Assignment Locked Flag") == 0) {
		m_IsAssignmentLocked = atoi(value);
		return;
	}
	if (name.CompareNoCase("Movement") == 0) {
		m_Movement = atof(value);
		return;
	}
	if (name.CompareNoCase("Balance On Hand") == 0) {
		m_BalanceOnHand = atof(value);
		return;
	}
	if (name.CompareNoCase("Optimize Method") == 0) {
		m_OptimizeBy = atoi(value);
		return;
	}
	if (name.CompareNoCase("Number Of Hits") == 0) {
		m_NumberOfHits = atof(value);
		return;
	}
	if (name.CompareNoCase("Hazard Flag") == 0) {
		m_IsHazard = atoi(value);
		return;
	}
	if (name.CompareNoCase("Pick-To-Belt Flag") == 0) {
		m_IsPickToBelt = atoi(value);
		return;
	}
	if (name.CompareNoCase("Allow Height-Length Swap") == 0) {
		m_RotateXAxis = atoi(value);
		return;
	}
	if (name.CompareNoCase("Allow Height-Width Swap") == 0) {
		m_RotateYAxis = atoi(value);
		return;
	}
	if (name.CompareNoCase("Allow Width-Length Swap") == 0) {
		m_RotateZAxis = atoi(value);
		return;
	}
	if (name.CompareNoCase("Case Width") == 0) {
		m_CaseWidth = atof(value);
		return;
	}
	if (name.CompareNoCase("Case Length") == 0) {
		m_CaseLength = atof(value);
		return;
	}
	if (name.CompareNoCase("Case Height") == 0) {
		m_CaseHeight = atof(value);
		return;
	}
	if (name.CompareNoCase("Inner Width") == 0) {
		m_InnerWidth = atof(value);
		return;
	}
	if (name.CompareNoCase("Inner Length") == 0) {
		m_InnerLength = atof(value);
		return;
	}
	if (name.CompareNoCase("Inner Height") == 0) {
		m_InnerHeight = atof(value);
		return;
	}
	if (name.CompareNoCase("Each Width") == 0) {
		m_EachWidth = atof(value);
		return;
	}
	if (name.CompareNoCase("Each Length") == 0) {
		m_EachLength = atof(value);
		return;
	}
	if (name.CompareNoCase("Each Height") == 0) {
		m_EachHeight = atof(value);
		return;
	}
	if (name.CompareNoCase("Weight") == 0) {
		m_Weight = atof(value);
		return;
	}
	if (name.CompareNoCase("Max Stack Number") == 0) {
		m_MaxStackNumber = atoi(value);
		return;
	}
	if (name.CompareNoCase("Number In Pallet") == 0) {
		m_NumberInPallet = atoi(value);
		return;
	}

	if (name.CompareNoCase("Status") == 0) {
		m_Status = atoi(value);
		return;
	}

	if (name.CompareNoCase("Is Active") == 0) {
		m_IsActive = atoi(value);
		return;
	}

	if (name.CompareNoCase("Trace") == 0) {
		m_Trace = atoi(value);
		return;
	}

	if (name.CompareNoCase("Previous Movement") == 0) {
		m_PreviousMovement = atof(value);
		return;
	}
	if (name.CompareNoCase("Previous BOH") == 0) {
		m_PreviousBOH = atof(value);
		return;
	}

	if (name.CompareNoCase("Commodity Type") == 0) {
		m_CommodityType = value;
		return;
	}

	if (name.CompareNoCase("Crush Factor") == 0) {
		m_CrushFactor = value;
		return;
	}

	if (name.CompareNoCase("Product Key") == 0) {
		m_ProductKey = atoi(value);
		return;
	}
	
	if (name.CompareNoCase("Nested Width") == 0) {
		m_NestedWidth = atof(value);
		return;
	}

	if (name.CompareNoCase("Nested Length") == 0) {
		m_NestedLength = atof(value);
		return;
	}

	if (name.CompareNoCase("Nested Height") == 0) {
		m_NestedHeight = atof(value);
		return;
	}

	if (name.CompareNoCase("Container Width") == 0) {
		m_Container.m_Width = atof(value);
		return;
	}
	if (name.CompareNoCase("Container Length") == 0) {
		m_Container.m_Length = atof(value);
		return;
	}
	if (name.CompareNoCase("Container Height") == 0) {
		m_Container.m_Height = atof(value);
		return;
	}

	if (name.CompareNoCase("Storage Hi") == 0) {
		m_Container.m_Hi = atoi(value);
		return;
	}
	if (name.CompareNoCase("Storage Ti") == 0) {
		m_Container.m_Ti = atoi(value);
		return;
	}
	if (name.CompareNoCase("Container Width Override Flag") == 0) {
		m_Container.m_IsWidthOverride = atoi(value);
		return;
	}
	if (name.CompareNoCase("Container Length Override Flag") == 0) {
		m_Container.m_IsLengthOverride = atoi(value);
		return;
	}
	if (name.CompareNoCase("Container Height Override Flag") == 0) {
		m_Container.m_IsHeightOverride = atoi(value);
		return;
	}
}

CString CProductPack::ConvertStatusToText(int status)
{
	if (status == NotIntegrated)
		return "Not Integrated";
	else if (status == Integrated)
		return "Integrated";
	else
		return "Unknown";
	

}

CString CProductPack::ConvertStatusToText()
{
	if (m_Status == NotIntegrated)
		return "Not Integrated";
	else
		return "Integrated";

}

int CProductPack::Parse(const CString &line)
{
	CStringArray strings;

	utilityHelper.ParseString(line, "|", strings);

	for (int i=0; i < strings.GetSize(); ++i) {
		if (strings[i] == "-32767")
			strings[i] = "0";

		switch (i) {
		case 0:
			m_ProductPackDBID = atoi(strings[i]);
			break;
		case 1:
			m_Description = strings[i];
			break;
		case 2:
			m_Weight = atof(strings[i]);
			break;
		case 3:
			m_Movement = atof(strings[i]);
			break;
		case 4:
			m_CaseWidth = atof(strings[i]);
			break;
		case 5:
			m_CaseLength = atof(strings[i]);
			break;
		case 6:
			m_CaseHeight = atof(strings[i]);
			break;
		case 7:
			m_IsHazard = atoi(strings[i]);
			break;
		case 8:
			m_UnitOfIssue = atoi(strings[i]);
			break;
		case 9:
			m_IsPickToBelt = atoi(strings[i]);
			break;
		case 10:
			m_OptimizeBy = atoi(strings[i]);
			break;
		case 11:
			m_BalanceOnHand = atof(strings[i]);
			break;
		case 12:
			m_NumberInPallet = atoi(strings[i]);
			break;
		case 13:
			m_RotateXAxis = atoi(strings[i]);
			break;
		case 14:
			m_RotateYAxis = atoi(strings[i]);
			break;
		case 15:
			m_RotateZAxis = atoi(strings[i]);
			break;
		case 16:
			//skip changedinpass = atoi(strings[i]);
			break;
		case 17:
			m_IsAssignmentLocked = atoi(strings[i]);
			break;
		case 18:
			m_MaxStackNumber = atoi(strings[i]);
			break;
		case 19:
			m_EachLength = atof(strings[i]);
			break;
		case 20:
			m_EachWidth = atof(strings[i]);
			break;
		case 21:
			m_EachHeight = atof(strings[i]);
			break;
		case 22:
			m_InnerLength = atof(strings[i]);
			break;
		case 23:
			m_InnerWidth = atof(strings[i]);
			break;
		case 24:
			m_InnerHeight = atof(strings[i]);
			break;
		case 25:
			m_InnerPack = atoi(strings[i]);
			break;
		case 26:
			m_CasePack = atoi(strings[i]);
			break;
		case 27:
			m_WMSProductID = strings[i];
			break;
		case 28:
			m_WMSProductDetailID = strings[i];
			break;
		case 29:
			m_NumberOfHits = atof(strings[i]);
			break;
			// skip dates, user, product
		case 34:
			m_Status = atoi(strings[i]);
			break;
		case 35:
			m_IsActive = atoi(strings[i]);
			break;
		case 36:
			m_Trace = atoi(strings[i]);
			break;
		case 37:
			m_PreviousMovement = atof(strings[i]);
			break;
		case 38:
			m_PreviousBOH = atof(strings[i]);
			break;
		case 39:
			m_CommodityType = strings[i];
			break;
		case 40:
			m_CrushFactor = strings[i];
			break;
		case 41:
			m_ProductKey = atoi(strings[i]);
			break;
		case 42:
			m_LastOptimizeAttribute = strings[i];
			break;
		case 43:
			m_NestedWidth = atof(strings[i]);
			break;
		case 44:
			m_NestedLength = atof(strings[i]);
			break;
		case 45:
			m_NestedHeight = atof(strings[i]);
			break;
		}
		
	}
	
	return 0;

}

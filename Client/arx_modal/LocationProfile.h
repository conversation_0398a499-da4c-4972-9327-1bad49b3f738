// LocationProfile.h: interface for the CLocationProfile class.
//
//////////////////////////////////////////////////////////////////////

#if !defined(AFX_LOCATIONPROFILE_H__E419F809_E302_4EB0_8FBB_D423C05D296A__INCLUDED_)
#define AFX_LOCATIONPROFILE_H__E419F809_E302_4EB0_8FBB_D423C05D296A__INCLUDED_

#if _MSC_VER > 1000
#pragma once
#endif // _MSC_VER > 1000
#include "3DPoint.h"

class CLocationProfile : public CObject  
{
public:
	CLocationProfile();
	CLocationProfile(const CLocationProfile& other);
	CLocationProfile& operator=(const CLocationProfile& other);
	BOOL operator==(const CLocationProfile& other);
	BOOL operator!=(const CLocationProfile& other) { return !(*this == other); };
	virtual ~CLocationProfile();
	
	int Parse(const CString &line);

	int m_LocationProfileDBId;
	CString m_Description;
	int m_HandlingMethod;
	int m_IsSelect;
	C3DPoint m_Coordinates;
	double m_Width;
	double m_Depth;
	double m_Height;
	double m_LocationSpace;
	double m_WeightCapacity;
	int m_LevelProfileDBId;

};

#endif // !defined(AFX_LOCATIONPROFILE_H__E419F809_E302_4EB0_8FBB_D423C05D296A__INCLUDED_)


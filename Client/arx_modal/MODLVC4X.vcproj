<?xml version="1.0" encoding = "Windows-1252"?>
<VisualStudioProject
	ProjectType="Visual C++"
	Version="7.00"
	Name="modal"
	ProjectGUID="{561D1DD6-294A-4D79-9DE6-8B57DF79B3A4}"
	SccProjectName="&quot;$/Optimize/OPTIMIZEBASE4/Client/arx_modal&quot;, EAAAAAAA"
	SccAuxPath=""
	SccLocalPath="."
	SccProvider="MSSCCI:Microsoft Visual SourceSafe"
	Keyword="MFCProj">
	<Platforms>
		<Platform
			Name="Win32"/>
	</Platforms>
	<Configurations>
		<Configuration
			Name="Release|Win32"
			OutputDirectory="..\..\Obj.Release"
			IntermediateDirectory="$(outdir)/Client/modal"
			ConfigurationType="2"
			UseOfMFC="2"
			ATLMinimizesCRunTimeLibraryUsage="FALSE"
			CharacterSet="2">
			<Tool
				Name="VCCLCompilerTool"
				InlineFunctionExpansion="1"
				AdditionalIncludeDirectories=".,..\..\thirdparty\objectarx2005\inc;..\..\Server;..\..\Common\core; ..\..\Interfaces\BTree;"
				PreprocessorDefinitions="NDEBUG;WIN32;_WINDOWS;_AUTOCAD2000;OPTI_SOCK"
				StringPooling="TRUE"
				RuntimeLibrary="2"
				EnableFunctionLevelLinking="TRUE"
				UsePrecompiledHeader="2"
				PrecompiledHeaderThrough="stdafx.h"
				PrecompiledHeaderFile="$(outdir)/Client/modal/MODLVC4X.pch"
				AssemblerListingLocation="$(outdir)/Client/modal/"
				ObjectFile="$(outdir)/Client/modal/"
				ProgramDataBaseFileName="$(outdir)/Client/modal/"
				WarningLevel="4"
				SuppressStartupBanner="TRUE"
				CompileAs="0"/>
			<Tool
				Name="VCCustomBuildTool"
				CommandLine="..\..\..\Program Files\EXE Technologies\Optimize\Bin
"/>
			<Tool
				Name="VCLinkerTool"
				AdditionalOptions="/MACHINE:I386"
				AdditionalDependencies="atls.lib axdb16.lib acdb16.lib acge16.lib acad.lib rxapi.lib acedapi.lib wsock32.lib wininet.lib optiserver.lib slbtrieve.lib ssagraphsession.lib"
				OutputFile="$(OutDir)/bin/modal.arx"
				LinkIncremental="2"
				SuppressStartupBanner="TRUE"
				AdditionalLibraryDirectories="$(outdir)\Libraries;..\..\thirdparty\objectarx2005\lib;."
				ModuleDefinitionFile=".\Modal.def"
				ProgramDatabaseFile="$(outdir)/Client/modal/modal.pdb"
				GenerateMapFile="TRUE"
				MapFileName="$(outdir)/Client/modal/modal.map"
				SubSystem="2"
				HeapReserveSize="102400000"
				BaseAddress=""
				ImportLibrary="$(outdir)/libraries/modal.lib"/>
			<Tool
				Name="VCMIDLTool"
				PreprocessorDefinitions="NDEBUG"
				MkTypLibCompatible="TRUE"
				SuppressStartupBanner="TRUE"
				TargetEnvironment="1"
				TypeLibraryName=".\WinRel/MODLVC4X.tlb"/>
			<Tool
				Name="VCPostBuildEventTool"/>
			<Tool
				Name="VCPreBuildEventTool"/>
			<Tool
				Name="VCPreLinkEventTool"/>
			<Tool
				Name="VCResourceCompilerTool"
				PreprocessorDefinitions="_AFXDLL;NDEBUG"
				Culture="1033"/>
			<Tool
				Name="VCWebServiceProxyGeneratorTool"/>
			<Tool
				Name="VCWebDeploymentTool"/>
		</Configuration>
		<Configuration
			Name="Release R14|Win32"
			OutputDirectory=".\WinRelR14"
			IntermediateDirectory=".\WinRelR14"
			ConfigurationType="2"
			UseOfMFC="2"
			ATLMinimizesCRunTimeLibraryUsage="FALSE"
			CharacterSet="2">
			<Tool
				Name="VCCLCompilerTool"
				InlineFunctionExpansion="1"
				AdditionalIncludeDirectories=".,c:\program files\objectarxR14\inc,c:\files\arx\inc"
				PreprocessorDefinitions="NDEBUG;WIN32;_WINDOWS"
				StringPooling="TRUE"
				RuntimeLibrary="2"
				EnableFunctionLevelLinking="TRUE"
				UsePrecompiledHeader="2"
				PrecompiledHeaderThrough="stdafx.h"
				PrecompiledHeaderFile=".\WinRelR14/MODLVC4X.pch"
				AssemblerListingLocation=".\WinRelR14/"
				ObjectFile=".\WinRelR14/"
				ProgramDataBaseFileName=".\WinRelR14/"
				WarningLevel="4"
				SuppressStartupBanner="TRUE"
				CompileAs="0"/>
			<Tool
				Name="VCCustomBuildTool"/>
			<Tool
				Name="VCLinkerTool"
				AdditionalOptions="/MACHINE:I386"
				AdditionalDependencies="libacge.lib ws2_32.lib wsock32.lib acad.lib rxapi.lib acedapi.lib wininet.lib"
				OutputFile=".\modal.arx"
				LinkIncremental="1"
				SuppressStartupBanner="TRUE"
				AdditionalLibraryDirectories="c:\program files\objectarxR14\lib,.,c:\files\arx\lib"
				ModuleDefinitionFile=".\Modal.def"
				ProgramDatabaseFile=".\WinRelR14/modal.pdb"
				GenerateMapFile="TRUE"
				MapFileName=".\WinRelR14/modal.map"
				SubSystem="2"
				HeapReserveSize="102400000"
				EntryPointSymbol="DllEntryPoint@12"
				BaseAddress="0x1c000000"
				ImportLibrary=".\WinRelR14/modal.lib"/>
			<Tool
				Name="VCMIDLTool"
				PreprocessorDefinitions="NDEBUG"
				MkTypLibCompatible="TRUE"
				SuppressStartupBanner="TRUE"
				TargetEnvironment="1"
				TypeLibraryName=".\WinRelR14/MODLVC4X.tlb"/>
			<Tool
				Name="VCPostBuildEventTool"/>
			<Tool
				Name="VCPreBuildEventTool"/>
			<Tool
				Name="VCPreLinkEventTool"/>
			<Tool
				Name="VCResourceCompilerTool"
				PreprocessorDefinitions="_AFXDLL;NDEBUG"
				Culture="1033"/>
			<Tool
				Name="VCWebServiceProxyGeneratorTool"/>
			<Tool
				Name="VCWebDeploymentTool"/>
		</Configuration>
		<Configuration
			Name="Debug|Win32"
			OutputDirectory="..\..\Obj.Debug"
			IntermediateDirectory="$(outdir)/Client/modal"
			ConfigurationType="2"
			UseOfMFC="2"
			ATLMinimizesCRunTimeLibraryUsage="FALSE"
			CharacterSet="2">
			<Tool
				Name="VCCLCompilerTool"
				Optimization="0"
				AdditionalIncludeDirectories=".,..\..\thirdparty\objectarx2005\inc;..\..\Server;..\..\Common\core; ..\..\Interfaces\BTree;"
				PreprocessorDefinitions="_DEBUG;WIN32;_WINDOWS;_AUTOCAD2000;OPTI_SOCK"
				RuntimeLibrary="3"
				UsePrecompiledHeader="2"
				PrecompiledHeaderThrough="stdafx.h"
				PrecompiledHeaderFile="$(outdir)/Client/modal/MODLVC4X.pch"
				AssemblerListingLocation="$(outdir)/Client/modal/"
				ObjectFile="$(outdir)/Client/modal/"
				ProgramDataBaseFileName="$(outdir)/Client/modal/"
				WarningLevel="4"
				SuppressStartupBanner="TRUE"
				DebugInformationFormat="4"
				CompileAs="0"/>
			<Tool
				Name="VCCustomBuildTool"
				CommandLine="copy &quot;$(OutDir)\modal.arx&quot; &quot;c:\program files\ssa global\optimize\bin&quot; /y
"/>
			<Tool
				Name="VCLinkerTool"
				AdditionalOptions="/MACHINE:I386"
				AdditionalDependencies="atlsd.lib axdb16.lib acdb16.lib acge16.lib acad.lib rxapi.lib acedapi.lib wsock32.lib wininet.lib optiserver.lib slbtrieve.lib SsaGraphSession.lib"
				OutputFile="$(outdir)/bin/modal.arx"
				LinkIncremental="2"
				SuppressStartupBanner="TRUE"
				AdditionalLibraryDirectories="$(outdir)\Libraries;..\..\thirdparty\objectarx2005\lib;."
				IgnoreAllDefaultLibraries="FALSE"
				IgnoreDefaultLibraryNames=""
				ModuleDefinitionFile=".\Modal.def"
				GenerateDebugInformation="TRUE"
				ProgramDatabaseFile="$(outdir)/Client/modal/modal.pdb"
				SubSystem="2"
				BaseAddress=""
				ImportLibrary="$(outdir)/libraries/modal.lib"/>
			<Tool
				Name="VCMIDLTool"
				PreprocessorDefinitions="_DEBUG"
				MkTypLibCompatible="TRUE"
				SuppressStartupBanner="TRUE"
				TargetEnvironment="1"
				TypeLibraryName=".\WinDebug/MODLVC4X.tlb"/>
			<Tool
				Name="VCPostBuildEventTool"/>
			<Tool
				Name="VCPreBuildEventTool"/>
			<Tool
				Name="VCPreLinkEventTool"/>
			<Tool
				Name="VCResourceCompilerTool"
				PreprocessorDefinitions="_AFXDLL;_DEBUG"
				Culture="1033"/>
			<Tool
				Name="VCWebServiceProxyGeneratorTool"/>
			<Tool
				Name="VCWebDeploymentTool"/>
		</Configuration>
		<Configuration
			Name="Debug R14|Win32"
			OutputDirectory=".\WinDebugR14"
			IntermediateDirectory=".\WinDebugR14"
			ConfigurationType="2"
			UseOfMFC="2"
			ATLMinimizesCRunTimeLibraryUsage="FALSE"
			CharacterSet="2">
			<Tool
				Name="VCCLCompilerTool"
				Optimization="0"
				AdditionalIncludeDirectories="c:\program files\objectarxR14\inc,.,c:\files\arx\inc"
				PreprocessorDefinitions="_DEBUG;WIN32;_WINDOWS"
				RuntimeLibrary="3"
				UsePrecompiledHeader="2"
				PrecompiledHeaderThrough="stdafx.h"
				PrecompiledHeaderFile=".\WinDebugR14/MODLVC4X.pch"
				AssemblerListingLocation=".\WinDebugR14/"
				ObjectFile=".\WinDebugR14/"
				ProgramDataBaseFileName=".\WinDebugR14/"
				WarningLevel="4"
				SuppressStartupBanner="TRUE"
				DebugInformationFormat="4"
				CompileAs="0"/>
			<Tool
				Name="VCCustomBuildTool"/>
			<Tool
				Name="VCLinkerTool"
				AdditionalOptions="/MACHINE:I386"
				AdditionalDependencies="libacge.lib ws2_32.lib wsock32.lib acad.lib rxapi.lib acedapi.lib wininet.lib"
				OutputFile=".\modal.arx"
				LinkIncremental="2"
				SuppressStartupBanner="TRUE"
				AdditionalLibraryDirectories="c:\program files\objectarxR14\lib,.,c:\files\arx\lib"
				IgnoreDefaultLibraryNames="mfcs42d.lib"
				ModuleDefinitionFile=".\Modal.def"
				GenerateDebugInformation="TRUE"
				ProgramDatabaseFile=".\WinDebugR14/modal.pdb"
				SubSystem="2"
				EntryPointSymbol="DllEntryPoint@12"
				BaseAddress="0x1c000000"
				ImportLibrary=".\WinDebugR14/modal.lib"/>
			<Tool
				Name="VCMIDLTool"
				PreprocessorDefinitions="_DEBUG"
				MkTypLibCompatible="TRUE"
				SuppressStartupBanner="TRUE"
				TargetEnvironment="1"
				TypeLibraryName=".\WinDebugR14/MODLVC4X.tlb"/>
			<Tool
				Name="VCPostBuildEventTool"/>
			<Tool
				Name="VCPreBuildEventTool"/>
			<Tool
				Name="VCPreLinkEventTool"/>
			<Tool
				Name="VCResourceCompilerTool"
				PreprocessorDefinitions="_AFXDLL;_DEBUG"
				Culture="1033"/>
			<Tool
				Name="VCWebServiceProxyGeneratorTool"/>
			<Tool
				Name="VCWebDeploymentTool"/>
		</Configuration>
	</Configurations>
	<Files>
		<Filter
			Name="Source Files"
			Filter="cpp;c;cxx;rc;def;r;odl;hpj;bat;for;f90">
			<File
				RelativePath="3DPoint.cpp">
				<FileConfiguration
					Name="Debug|Win32">
					<Tool
						Name="VCCLCompilerTool"
						ObjectFile="$(IntDir)/$(InputName)1.obj"/>
				</FileConfiguration>
			</File>
			<File
				RelativePath=".\Aisle.cpp">
			</File>
			<File
				RelativePath=".\AisleDataService.cpp">
			</File>
			<File
				RelativePath=".\AisleProfile.cpp">
			</File>
			<File
				RelativePath=".\AisleProfileButton.cpp">
			</File>
			<File
				RelativePath=".\AisleProfileDataService.cpp">
			</File>
			<File
				RelativePath=".\AisleProfileDimensionPage.cpp">
			</File>
			<File
				RelativePath=".\AisleProfileListDialog.cpp">
			</File>
			<File
				RelativePath=".\AisleProfileSheet.cpp">
			</File>
			<File
				RelativePath=".\AisleProfileSidePage.cpp">
			</File>
			<File
				RelativePath=".\AisleProperties.cpp">
			</File>
			<File
				RelativePath=".\AssignConnectionDialog.cpp">
			</File>
			<File
				RelativePath=".\AssignSlotGroup.cpp">
			</File>
			<File
				RelativePath=".\Assignment.cpp">
			</File>
			<File
				RelativePath=".\AssignmentOutboundDialog.cpp">
			</File>
			<File
				RelativePath=".\AutoCADCommands.cpp">
			</File>
			<File
				RelativePath="BTreeHelper.cpp">
			</File>
			<File
				RelativePath=".\Bay.cpp">
			</File>
			<File
				RelativePath=".\BayInfo.cpp">
			</File>
			<File
				RelativePath=".\BayLevelInfo.cpp">
			</File>
			<File
				RelativePath=".\BayLocationInfo.cpp">
			</File>
			<File
				RelativePath=".\BayProfile.cpp">
			</File>
			<File
				RelativePath=".\BayProfileBayAttributesPage.cpp">
			</File>
			<File
				RelativePath=".\BayProfileBinPage.cpp">
			</File>
			<File
				RelativePath=".\BayProfileCrossbarButton.cpp">
			</File>
			<File
				RelativePath=".\BayProfileCrossbarInfo.cpp">
			</File>
			<File
				RelativePath=".\BayProfileCrossbarPage.cpp">
			</File>
			<File
				RelativePath=".\BayProfileCrossbarProperties.cpp">
			</File>
			<File
				RelativePath=".\BayProfileDataService.cpp">
			</File>
			<File
				RelativePath=".\BayProfileDimensionInfo.cpp">
			</File>
			<File
				RelativePath=".\BayProfileDriveInPage.cpp">
			</File>
			<File
				RelativePath=".\BayProfileEditFacingInfo.cpp">
			</File>
			<File
				RelativePath=".\BayProfileExternalAttributePage.cpp">
			</File>
			<File
				RelativePath=".\BayProfileFloorPage.cpp">
			</File>
			<File
				RelativePath=".\BayProfileFlowPage.cpp">
			</File>
			<File
				RelativePath=".\BayProfileHandlingProperties.cpp">
			</File>
			<File
				RelativePath=".\BayProfileLaborPage.cpp">
			</File>
			<File
				RelativePath=".\BayProfileLevelButton.cpp">
			</File>
			<File
				RelativePath=".\BayProfileLevelButton.h">
			</File>
			<File
				RelativePath=".\BayProfileLevelPage.cpp">
			</File>
			<File
				RelativePath=".\BayProfileListDialog.cpp">
			</File>
			<File
				RelativePath=".\BayProfileLocationPage.cpp">
			</File>
			<File
				RelativePath=".\BayProfileLocationProperties.cpp">
			</File>
			<File
				RelativePath=".\BayProfilePalletPage.cpp">
			</File>
			<File
				RelativePath=".\BayProfileRulesPage.cpp">
			</File>
			<File
				RelativePath=".\BayProfileSheet.cpp">
			</File>
			<File
				RelativePath=".\BayProfileSideViewButton.cpp">
			</File>
			<File
				RelativePath=".\BayProfileTopViewButton.cpp">
			</File>
			<File
				RelativePath=".\BayProperties.cpp">
			</File>
			<File
				RelativePath=".\BayRule.cpp">
			</File>
			<File
				RelativePath=".\BusyWaitCursor.cpp">
			</File>
			<File
				RelativePath=".\CapitalCostDialog.cpp">
			</File>
			<File
				RelativePath=".\ChangeRackType.cpp">
			</File>
			<File
				RelativePath=".\CheckMessageDialog.cpp">
			</File>
			<File
				RelativePath=".\ChooseAisleProfileDialog.cpp">
			</File>
			<File
				RelativePath=".\CleanFacilityDialog.cpp">
			</File>
			<File
				RelativePath=".\ColorButton.cpp">
			</File>
			<File
				RelativePath=".\ColorListBox.cpp">
			</File>
			<File
				RelativePath=".\ColorModelAdvancedDialog.cpp">
			</File>
			<File
				RelativePath=".\ColorModelDlg.cpp">
			</File>
			<File
				RelativePath=".\ColorModelResults.cpp">
			</File>
			<File
				RelativePath=".\ColorProductGroupDialog.cpp">
			</File>
			<File
				RelativePath=".\ColoringCommands.cpp">
			</File>
			<File
				RelativePath=".\ColoringHelper.cpp">
			</File>
			<File
				RelativePath=".\Commands.cpp">
			</File>
			<File
				RelativePath=".\Confirmation.cpp">
			</File>
			<File
				RelativePath=".\ControlService.cpp">
			</File>
			<File
				RelativePath=".\CostComparisonDialog.cpp">
			</File>
			<File
				RelativePath=".\DataAccessService.cpp">
			</File>
			<File
				RelativePath=".\DataGrid.cpp">
			</File>
			<File
				RelativePath=".\DataGridAttribute.cpp">
			</File>
			<File
				RelativePath=".\DataGridEdit.cpp">
			</File>
			<File
				RelativePath=".\DataGridListBox.cpp">
			</File>
			<File
				RelativePath=".\DataModelDialog.cpp">
			</File>
			<File
				RelativePath=".\DataPurificationDialog.cpp">
			</File>
			<File
				RelativePath=".\DefineConnectionDialog.cpp">
			</File>
			<File
				RelativePath=".\DisplayCount.cpp">
			</File>
			<File
				RelativePath=".\DisplayResults.cpp">
			</File>
			<File
				RelativePath=".\DragDropTreeCtrl.cpp">
			</File>
			<File
				RelativePath=".\DrawingService.cpp">
			</File>
			<File
				RelativePath=".\EditGrid.cpp">
			</File>
			<File
				RelativePath=".\EditWnd.cpp">
			</File>
			<File
				RelativePath=".\ElementMaintenanceCommands.cpp">
			</File>
			<File
				RelativePath=".\ElementMaintenanceHelper.cpp">
			</File>
			<File
				RelativePath=".\ExportFacility.cpp">
			</File>
			<File
				RelativePath=".\ExternalConnection.cpp">
			</File>
			<File
				RelativePath=".\ExternalSystem.cpp">
			</File>
			<File
				RelativePath=".\Facility.cpp">
			</File>
			<File
				RelativePath=".\FacilityCommands.cpp">
			</File>
			<File
				RelativePath=".\FacilityDataService.cpp">
			</File>
			<File
				RelativePath=".\FacilityDialog.cpp">
			</File>
			<File
				RelativePath=".\FacilityElement.cpp">
			</File>
			<File
				RelativePath=".\FacilityElementSheet.cpp">
			</File>
			<File
				RelativePath=".\FacilityHelper.cpp">
			</File>
			<File
				RelativePath=".\FacilityProperties.cpp">
			</File>
			<File
				RelativePath=".\FacilityTools.cpp">
			</File>
			<File
				RelativePath=".\FacingInfo.cpp">
			</File>
			<File
				RelativePath=".\ForteService.cpp">
			</File>
			<File
				RelativePath=".\GenerateMovesDialog.cpp">
			</File>
			<File
				RelativePath=".\GenericCommands.cpp">
			</File>
			<File
				RelativePath=".\GenericPropertySheet.cpp">
			</File>
			<File
				RelativePath=".\HelpService.cpp">
			</File>
			<File
				RelativePath=".\HotSpotDialog.cpp">
			</File>
			<File
				RelativePath=".\HotSpotPropertiesDialog.cpp">
			</File>
			<File
				RelativePath=".\Hotspot.cpp">
			</File>
			<File
				RelativePath=".\InboundQueueRecord.cpp">
			</File>
			<File
				RelativePath=".\IntegrationAssignmentOptionsPage.cpp">
			</File>
			<File
				RelativePath=".\IntegrationDataService.cpp">
			</File>
			<File
				RelativePath=".\IntegrationLocationOptionsPage.cpp">
			</File>
			<File
				RelativePath=".\IntegrationOptionsPage.cpp">
			</File>
			<File
				RelativePath=".\IntegrationProductOptionsPage.cpp">
			</File>
			<File
				RelativePath=".\IntegrationStatusDialog.cpp">
			</File>
			<File
				RelativePath=".\InterfaceCommands.cpp">
			</File>
			<File
				RelativePath=".\InterfaceHelper.cpp">
			</File>
			<File
				RelativePath=".\InterfaceMap.cpp">
			</File>
			<File
				RelativePath=".\InterfaceMapAttribute.cpp">
			</File>
			<File
				RelativePath=".\InterfaceMapDataService.cpp">
			</File>
			<File
				RelativePath=".\InterfaceMapDialog.cpp">
			</File>
			<File
				RelativePath=".\InterfaceMapUDFDialog.cpp">
			</File>
			<File
				RelativePath=".\InterfaceProductFileConvertDialog.cpp">
			</File>
			<File
				RelativePath=".\LayoutGroupsDialog.cpp">
			</File>
			<File
				RelativePath=".\Level.cpp">
			</File>
			<File
				RelativePath=".\LevelLaborProfile.cpp">
			</File>
			<File
				RelativePath=".\LevelLocationButton.cpp">
			</File>
			<File
				RelativePath=".\LevelLocationDialog.cpp">
			</File>
			<File
				RelativePath=".\LevelProfile.cpp">
			</File>
			<File
				RelativePath=".\LevelProfileExternalInfo.cpp">
			</File>
			<File
				RelativePath=".\LevelProperties.cpp">
			</File>
			<File
				RelativePath=".\Location.cpp">
			</File>
			<File
				RelativePath=".\LocationAttributesPage.cpp">
			</File>
			<File
				RelativePath=".\LocationExternalInfo.cpp">
			</File>
			<File
				RelativePath=".\LocationInfo.cpp">
			</File>
			<File
				RelativePath=".\LocationInterfaceDataService.cpp">
			</File>
			<File
				RelativePath=".\LocationNumberingService.cpp">
			</File>
			<File
				RelativePath=".\LocationOutboundDialog.cpp">
			</File>
			<File
				RelativePath=".\LocationOutboundInfo.cpp">
			</File>
			<File
				RelativePath=".\LocationProfile.cpp">
			</File>
			<File
				RelativePath=".\LocationProperties.cpp">
			</File>
			<File
				RelativePath=".\LoginDlg.cpp">
			</File>
			<File
				RelativePath=".\ManualAssignmentDialog.cpp">
			</File>
			<File
				RelativePath=".\MessageQueueHelper.cpp">
			</File>
			<File
				RelativePath=".\Modal.def">
			</File>
			<File
				RelativePath=".\Move.cpp">
			</File>
			<File
				RelativePath=".\MoveDataService.cpp">
			</File>
			<File
				RelativePath=".\NavigationCommands.cpp">
			</File>
			<File
				RelativePath=".\NavigationHelper.cpp">
			</File>
			<File
				RelativePath=".\NewSectionPage1.cpp">
			</File>
			<File
				RelativePath=".\NewSectionPage2.cpp">
			</File>
			<File
				RelativePath=".\ObjectPlaceDialog.cpp">
			</File>
			<File
				RelativePath=".\Operator.cpp">
			</File>
			<File
				RelativePath=".\OperatorService.cpp">
			</File>
			<File
				RelativePath=".\OptimizationCommands.cpp">
			</File>
			<File
				RelativePath=".\OptimizationDataService.cpp">
			</File>
			<File
				RelativePath=".\OptimizationHelper.cpp">
			</File>
			<File
				RelativePath=".\PasswordDialog.cpp">
			</File>
			<File
				RelativePath=".\PickPath.cpp">
			</File>
			<File
				RelativePath=".\PickPathOptionDialog.cpp">
			</File>
			<File
				RelativePath=".\PickPathPropertiesDialog.cpp">
			</File>
			<File
				RelativePath=".\PopulateUDF.cpp">
			</File>
			<File
				RelativePath=".\Processing.cpp">
			</File>
			<File
				RelativePath=".\ProcessingMessage.cpp">
			</File>
			<File
				RelativePath=".\ProductAttribute.cpp">
			</File>
			<File
				RelativePath=".\ProductCommands.cpp">
			</File>
			<File
				RelativePath=".\ProductContainer.cpp">
			</File>
			<File
				RelativePath=".\ProductDataService.cpp">
			</File>
			<File
				RelativePath=".\ProductGroup.cpp">
			</File>
			<File
				RelativePath=".\ProductGroupAssignmentDialog.cpp">
			</File>
			<File
				RelativePath=".\ProductGroupCommands.cpp">
			</File>
			<File
				RelativePath=".\ProductGroupConstraint.cpp">
			</File>
			<File
				RelativePath=".\ProductGroupConstraintsPage.cpp">
			</File>
			<File
				RelativePath=".\ProductGroupCriteria.cpp">
			</File>
			<File
				RelativePath=".\ProductGroupCriteriaListPage.cpp">
			</File>
			<File
				RelativePath=".\ProductGroupCriteriaMaintenance.cpp">
			</File>
			<File
				RelativePath=".\ProductGroupCriteriaMatrix.cpp">
			</File>
			<File
				RelativePath=".\ProductGroupCriteriaPropertiesPage.cpp">
			</File>
			<File
				RelativePath=".\ProductGroupCriteriaQuery.cpp">
			</File>
			<File
				RelativePath=".\ProductGroupCriteriaQueryDialog.cpp">
			</File>
			<File
				RelativePath=".\ProductGroupCriteriaRange.cpp">
			</File>
			<File
				RelativePath=".\ProductGroupCriteriaValue.cpp">
			</File>
			<File
				RelativePath=".\ProductGroupDataService.cpp">
			</File>
			<File
				RelativePath=".\ProductGroupDialog.cpp">
			</File>
			<File
				RelativePath=".\ProductGroupFrame.cpp">
			</File>
			<File
				RelativePath=".\ProductGroupHelper.cpp">
			</File>
			<File
				RelativePath=".\ProductGroupLevel.cpp">
			</File>
			<File
				RelativePath=".\ProductGroupNavigator.cpp">
			</File>
			<File
				RelativePath=".\ProductGroupPropertiesPage.cpp">
			</File>
			<File
				RelativePath=".\ProductGroupQuery.cpp">
			</File>
			<File
				RelativePath=".\ProductHelper.cpp">
			</File>
			<File
				RelativePath=".\ProductInfo.cpp">
			</File>
			<File
				RelativePath=".\ProductInterfaceDataService.cpp">
			</File>
			<File
				RelativePath=".\ProductInterfaceDialog.cpp">
			</File>
			<File
				RelativePath=".\ProductLayoutAdvancedPage.cpp">
			</File>
			<File
				RelativePath=".\ProductLayoutStartPage.cpp">
			</File>
			<File
				RelativePath=".\ProductMaintenance.cpp">
			</File>
			<File
				RelativePath=".\ProductPack.cpp">
			</File>
			<File
				RelativePath=".\ProfileFrame.cpp">
			</File>
			<File
				RelativePath=".\ProfileMaintenanceSheet.cpp">
			</File>
			<File
				RelativePath=".\ProfileMaintenanceView.cpp">
			</File>
			<File
				RelativePath=".\ProfilePage.cpp">
			</File>
			<File
				RelativePath=".\ProfileTreePane.cpp">
			</File>
			<File
				RelativePath=".\Progress.cpp">
			</File>
			<File
				RelativePath=".\ProgressMessage.cpp">
			</File>
			<File
				RelativePath=".\Prompt.cpp">
			</File>
			<File
				RelativePath=".\QueryLocations.cpp">
			</File>
			<File
				RelativePath=".\ReportCommands.cpp">
			</File>
			<File
				RelativePath=".\ReportHelper.cpp">
			</File>
			<File
				RelativePath=".\ResourceHelper.cpp">
			</File>
			<File
				RelativePath=".\RotationButton.cpp">
			</File>
			<File
				RelativePath=".\SAXContentHandlerImpl.cpp">
			</File>
			<File
				RelativePath=".\SAXErrorHandlerImpl.cpp">
			</File>
			<File
				RelativePath=".\SaxContentHandler.cpp">
			</File>
			<File
				RelativePath=".\SearchAnchor.cpp">
			</File>
			<File
				RelativePath=".\SearchAnchorDataService.cpp">
			</File>
			<File
				RelativePath=".\SearchAnchorDialog.cpp">
			</File>
			<File
				RelativePath=".\SearchAnchorGenerate.cpp">
			</File>
			<File
				RelativePath=".\Section.cpp">
			</File>
			<File
				RelativePath=".\Side.cpp">
			</File>
			<File
				RelativePath=".\SideProfile.cpp">
			</File>
			<File
				RelativePath=".\SideProfileAttributesPage.cpp">
			</File>
			<File
				RelativePath=".\SideProfileBayPage.cpp">
			</File>
			<File
				RelativePath=".\SideProfileButton.cpp">
			</File>
			<File
				RelativePath=".\SideProfileDataService.cpp">
			</File>
			<File
				RelativePath=".\SideProfileListDialog.cpp">
			</File>
			<File
				RelativePath=".\SideProfileSheet.cpp">
			</File>
			<File
				RelativePath=".\SideProperties.cpp">
			</File>
			<File
				RelativePath=".\Solution.cpp">
			</File>
			<File
				RelativePath=".\SolutionDataService.cpp">
			</File>
			<File
				RelativePath=".\SortListBox.cpp">
			</File>
			<File
				RelativePath=".\SplashWnd.cpp">
			</File>
			<File
				RelativePath=".\Startup.cpp">
			</File>
			<File
				RelativePath=".\ThreadParameters.cpp">
			</File>
			<File
				RelativePath=".\TreeElement.cpp">
			</File>
			<File
				RelativePath=".\UDF.cpp">
			</File>
			<File
				RelativePath=".\UDFCommands.cpp">
			</File>
			<File
				RelativePath=".\UDFDataService.cpp">
			</File>
			<File
				RelativePath=".\UDFHelper.cpp">
			</File>
			<File
				RelativePath=".\UDFMaintenanceDialog.cpp">
			</File>
			<File
				RelativePath=".\UDFPage.cpp">
			</File>
			<File
				RelativePath=".\UDFProperties.cpp">
			</File>
			<File
				RelativePath=".\UserQuery.cpp">
			</File>
			<File
				RelativePath=".\UserQueryDataService.cpp">
			</File>
			<File
				RelativePath=".\UserQueryDialog.cpp">
			</File>
			<File
				RelativePath=".\UtilityCommands.cpp">
			</File>
			<File
				RelativePath=".\UtilityHelper.cpp">
			</File>
			<File
				RelativePath=".\ValidateFacility.cpp">
			</File>
			<File
				RelativePath=".\WMS.cpp">
			</File>
			<File
				RelativePath=".\WMSExportPage.cpp">
			</File>
			<File
				RelativePath=".\WMSFacilityInfo.cpp">
			</File>
			<File
				RelativePath=".\WMSGroup.cpp">
			</File>
			<File
				RelativePath=".\WMSGroupConnection.cpp">
			</File>
			<File
				RelativePath=".\WMSGroupDialog.cpp">
			</File>
			<File
				RelativePath=".\WMSGroupProperties.cpp">
			</File>
			<File
				RelativePath=".\WMSImportPage.cpp">
			</File>
			<File
				RelativePath=".\WMSMap.cpp">
			</File>
			<File
				RelativePath=".\WMSProperties.cpp">
			</File>
			<File
				RelativePath=".\WMSSheet.cpp">
			</File>
			<File
				RelativePath=".\WizardCommands.cpp">
			</File>
			<File
				RelativePath=".\WizardHelper.cpp">
			</File>
			<File
				RelativePath=".\arxmfctmpl.cpp">
			</File>
			<File
				RelativePath=".\excel8.cpp">
			</File>
			<File
				RelativePath=".\font.cpp">
			</File>
			<File
				RelativePath=".\msflexgrid.cpp">
			</File>
			<File
				RelativePath=".\picture.cpp">
			</File>
			<File
				RelativePath=".\qqhclasses.cpp">
			</File>
			<File
				RelativePath=".\rowcursor.cpp">
			</File>
			<File
				RelativePath=".\socket_class.cpp">
			</File>
			<File
				RelativePath="ssaBtree.cpp">
				<FileConfiguration
					Name="Debug|Win32">
					<Tool
						Name="VCCLCompilerTool"
						ObjectFile="$(IntDir)/$(InputName)1.obj"/>
				</FileConfiguration>
			</File>
			<File
				RelativePath=".\stdafx.cpp">
				<FileConfiguration
					Name="Debug|Win32">
					<Tool
						Name="VCCLCompilerTool"
						UsePrecompiledHeader="1"/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug R14|Win32">
					<Tool
						Name="VCCLCompilerTool"
						UsePrecompiledHeader="1"/>
				</FileConfiguration>
			</File>
		</Filter>
		<Filter
			Name="Header Files"
			Filter="h;hpp;hxx;hm;inl;fi;fd">
			<File
				RelativePath=".\3DPoint.h">
			</File>
			<File
				RelativePath=".\Aisle.h">
			</File>
			<File
				RelativePath=".\AisleDataService.h">
			</File>
			<File
				RelativePath=".\AisleProfile.h">
			</File>
			<File
				RelativePath=".\AisleProfileButton.h">
			</File>
			<File
				RelativePath=".\AisleProfileDataService.h">
			</File>
			<File
				RelativePath=".\AisleProfileDimensionPage.h">
			</File>
			<File
				RelativePath=".\AisleProfileListDialog.h">
			</File>
			<File
				RelativePath=".\AisleProfileSheet.h">
			</File>
			<File
				RelativePath=".\AisleProfileSidepage.h">
			</File>
			<File
				RelativePath=".\AisleProperties.h">
			</File>
			<File
				RelativePath=".\AssignConnectionDialog.h">
			</File>
			<File
				RelativePath=".\AssignSlotGroup.h">
			</File>
			<File
				RelativePath=".\Assignment.h">
			</File>
			<File
				RelativePath=".\AssignmentOutboundDialog.h">
			</File>
			<File
				RelativePath=".\AutoCADCommands.h">
			</File>
			<File
				RelativePath=".\BTreeHelper.h">
			</File>
			<File
				RelativePath=".\Bay.h">
			</File>
			<File
				RelativePath=".\BayInfo.h">
			</File>
			<File
				RelativePath=".\BayLevelInfo.h">
			</File>
			<File
				RelativePath=".\BayLocationInfo.h">
			</File>
			<File
				RelativePath=".\BayProfile.h">
			</File>
			<File
				RelativePath=".\BayProfileBayAttributesPage.h">
			</File>
			<File
				RelativePath=".\BayProfileBinPage.h">
			</File>
			<File
				RelativePath=".\BayProfileCrossbarButton.h">
			</File>
			<File
				RelativePath=".\BayProfileCrossbarInfo.h">
			</File>
			<File
				RelativePath=".\BayProfileCrossbarPage.h">
			</File>
			<File
				RelativePath=".\BayProfileCrossbarProperties.h">
			</File>
			<File
				RelativePath=".\BayProfileDataService.h">
			</File>
			<File
				RelativePath=".\BayProfileDimensionInfo.h">
			</File>
			<File
				RelativePath=".\BayProfileDriveInPage.h">
			</File>
			<File
				RelativePath=".\BayProfileEditFacingInfo.h">
			</File>
			<File
				RelativePath=".\BayProfileExternalAttributePage.h">
			</File>
			<File
				RelativePath=".\BayProfileFloorPage.h">
			</File>
			<File
				RelativePath=".\BayProfileFlowPage.h">
			</File>
			<File
				RelativePath=".\BayProfileHandlingProperties.h">
			</File>
			<File
				RelativePath=".\BayProfileLaborPage.h">
			</File>
			<File
				RelativePath=".\BayProfileLevelPage.h">
			</File>
			<File
				RelativePath=".\BayProfileListDialog.h">
			</File>
			<File
				RelativePath=".\BayProfileLocationPage.h">
			</File>
			<File
				RelativePath=".\BayProfileLocationProperties.h">
			</File>
			<File
				RelativePath=".\BayProfilePalletPage.h">
			</File>
			<File
				RelativePath=".\BayProfileRulesPage.h">
			</File>
			<File
				RelativePath=".\BayProfileSheet.h">
			</File>
			<File
				RelativePath=".\BayProfileSideViewButton.h">
			</File>
			<File
				RelativePath=".\BayProfileTopViewButton.h">
			</File>
			<File
				RelativePath=".\BayProperties.h">
			</File>
			<File
				RelativePath=".\BayRule.h">
			</File>
			<File
				RelativePath=".\BusyWaitCursor.h">
			</File>
			<File
				RelativePath=".\CapitalCostDialog.h">
			</File>
			<File
				RelativePath=".\ChangeRackType.h">
			</File>
			<File
				RelativePath=".\CheckMessageDialog.h">
			</File>
			<File
				RelativePath=".\ChooseAisleProfileDialog.h">
			</File>
			<File
				RelativePath=".\CleanFacilityDialog.h">
			</File>
			<File
				RelativePath=".\ColorButton.h">
			</File>
			<File
				RelativePath=".\ColorListBox.h">
			</File>
			<File
				RelativePath=".\ColorModelAdvancedDialog.h">
			</File>
			<File
				RelativePath=".\ColorModelDlg.h">
			</File>
			<File
				RelativePath=".\ColorModelResults.h">
			</File>
			<File
				RelativePath=".\ColorProductGroupDialog.h">
			</File>
			<File
				RelativePath=".\ColoringCommands.h">
			</File>
			<File
				RelativePath=".\ColoringHelper.h">
			</File>
			<File
				RelativePath=".\Commands.h">
			</File>
			<File
				RelativePath=".\Confirmation.h">
			</File>
			<File
				RelativePath=".\ControlService.h">
			</File>
			<File
				RelativePath=".\CostComparisonDialog.h">
			</File>
			<File
				RelativePath=".\DataAccessService.h">
			</File>
			<File
				RelativePath=".\DataGrid.h">
			</File>
			<File
				RelativePath=".\DataGridAttribute.h">
			</File>
			<File
				RelativePath=".\DataGridEdit.h">
			</File>
			<File
				RelativePath=".\DataGridListBox.h">
			</File>
			<File
				RelativePath=".\DataModelDialog.h">
			</File>
			<File
				RelativePath=".\DataPurificationDialog.h">
			</File>
			<File
				RelativePath=".\DefineConnectionDialog.h">
			</File>
			<File
				RelativePath=".\DisplayCount.h">
			</File>
			<File
				RelativePath=".\DisplayResults.h">
			</File>
			<File
				RelativePath=".\DragDropTreeCtrl.h">
			</File>
			<File
				RelativePath=".\DrawingService.h">
			</File>
			<File
				RelativePath=".\EditGrid.h">
			</File>
			<File
				RelativePath=".\EditWnd.h">
			</File>
			<File
				RelativePath=".\ElementMaintenanceCommands.h">
			</File>
			<File
				RelativePath=".\ElementMaintenanceHelper.h">
			</File>
			<File
				RelativePath=".\ExportFacility.h">
			</File>
			<File
				RelativePath=".\ExternalConnection.h">
			</File>
			<File
				RelativePath=".\ExternalSystem.h">
			</File>
			<File
				RelativePath=".\Facility.h">
			</File>
			<File
				RelativePath=".\FacilityCommands.h">
			</File>
			<File
				RelativePath=".\FacilityDataService.h">
			</File>
			<File
				RelativePath=".\FacilityDialog.h">
			</File>
			<File
				RelativePath=".\FacilityElement.h">
			</File>
			<File
				RelativePath=".\FacilityElementSheet.h">
			</File>
			<File
				RelativePath=".\FacilityHelper.h">
			</File>
			<File
				RelativePath=".\FacilityProperties.h">
			</File>
			<File
				RelativePath=".\FacilityTools.h">
			</File>
			<File
				RelativePath=".\FacingInfo.h">
			</File>
			<File
				RelativePath=".\ForteService.h">
			</File>
			<File
				RelativePath=".\GenerateMovesDialog.h">
			</File>
			<File
				RelativePath=".\GenericCommands.h">
			</File>
			<File
				RelativePath=".\GenericPropertySheet.h">
			</File>
			<File
				RelativePath=".\HelpService.h">
			</File>
			<File
				RelativePath=".\HotSpotDialog.h">
			</File>
			<File
				RelativePath=".\HotSpotPropertiesDialog.h">
			</File>
			<File
				RelativePath=".\Hotspot.h">
			</File>
			<File
				RelativePath=".\InboundQueueRecord.h">
			</File>
			<File
				RelativePath=".\IntegrationAssignmentOptionsPage.h">
			</File>
			<File
				RelativePath=".\IntegrationDataService.h">
			</File>
			<File
				RelativePath=".\IntegrationLocationOptionsPage.h">
			</File>
			<File
				RelativePath=".\IntegrationOptionsPage.h">
			</File>
			<File
				RelativePath=".\IntegrationProductOptionsPage.h">
			</File>
			<File
				RelativePath=".\IntegrationStatusDialog.h">
			</File>
			<File
				RelativePath=".\InterfaceCommands.h">
			</File>
			<File
				RelativePath=".\InterfaceHelper.h">
			</File>
			<File
				RelativePath=".\InterfaceMap.h">
			</File>
			<File
				RelativePath=".\InterfaceMapAttribute.h">
			</File>
			<File
				RelativePath=".\InterfaceMapDataService.h">
			</File>
			<File
				RelativePath=".\InterfaceMapDialog.h">
			</File>
			<File
				RelativePath=".\InterfaceMapUDFDialog.h">
			</File>
			<File
				RelativePath=".\InterfaceProductFileConvertDialog.h">
			</File>
			<File
				RelativePath=".\LayoutGroupsDialog.h">
			</File>
			<File
				RelativePath=".\Level.h">
			</File>
			<File
				RelativePath=".\LevelLaborProfile.h">
			</File>
			<File
				RelativePath=".\LevelLocationButton.h">
			</File>
			<File
				RelativePath=".\LevelLocationDialog.h">
			</File>
			<File
				RelativePath=".\LevelProfile.h">
			</File>
			<File
				RelativePath=".\LevelProfileExternalInfo.h">
			</File>
			<File
				RelativePath=".\LevelProperties.h">
			</File>
			<File
				RelativePath=".\Location.h">
			</File>
			<File
				RelativePath=".\LocationAttributesPage.h">
			</File>
			<File
				RelativePath=".\LocationExternalInfo.h">
			</File>
			<File
				RelativePath=".\LocationInfo.h">
			</File>
			<File
				RelativePath=".\LocationInterfaceDataService.h">
			</File>
			<File
				RelativePath=".\LocationNumberingService.h">
			</File>
			<File
				RelativePath=".\LocationOutboundDialog.h">
			</File>
			<File
				RelativePath=".\LocationOutboundInfo.h">
			</File>
			<File
				RelativePath=".\LocationProfile.h">
			</File>
			<File
				RelativePath=".\LocationProperties.h">
			</File>
			<File
				RelativePath=".\LoginDlg.h">
			</File>
			<File
				RelativePath=".\ManualAssignmentDialog.h">
			</File>
			<File
				RelativePath=".\MessageQueueHelper.h">
			</File>
			<File
				RelativePath=".\Move.h">
			</File>
			<File
				RelativePath=".\MoveDataService.h">
			</File>
			<File
				RelativePath=".\NavigationCommands.h">
			</File>
			<File
				RelativePath=".\NavigationHelper.h">
			</File>
			<File
				RelativePath=".\NewSectionPage1.h">
			</File>
			<File
				RelativePath=".\NewSectionPage2.h">
			</File>
			<File
				RelativePath=".\NumberBays.h">
			</File>
			<File
				RelativePath=".\ObjectPlaceDialog.h">
			</File>
			<File
				RelativePath=".\Operator.h">
			</File>
			<File
				RelativePath=".\OperatorService.h">
			</File>
			<File
				RelativePath=".\OptimizationCommands.h">
			</File>
			<File
				RelativePath=".\OptimizationDataService.h">
			</File>
			<File
				RelativePath=".\OptimizationHelper.h">
			</File>
			<File
				RelativePath=".\PasswordDialog.h">
			</File>
			<File
				RelativePath=".\PickPath.h">
			</File>
			<File
				RelativePath=".\PickPathOptionDialog.h">
			</File>
			<File
				RelativePath=".\PickPathPropertiesDialog.h">
			</File>
			<File
				RelativePath=".\PopulateUDF.h">
			</File>
			<File
				RelativePath=".\Processing.h">
			</File>
			<File
				RelativePath=".\ProcessingMessage.h">
			</File>
			<File
				RelativePath=".\ProductAttribute.h">
			</File>
			<File
				RelativePath=".\ProductCommands.h">
			</File>
			<File
				RelativePath=".\ProductContainer.h">
			</File>
			<File
				RelativePath=".\ProductDataService.h">
			</File>
			<File
				RelativePath=".\ProductGroup.h">
			</File>
			<File
				RelativePath=".\ProductGroupAssignmentDialog.h">
			</File>
			<File
				RelativePath=".\ProductGroupCommands.h">
			</File>
			<File
				RelativePath=".\ProductGroupConstraint.h">
			</File>
			<File
				RelativePath=".\ProductGroupConstraintsPage.h">
			</File>
			<File
				RelativePath=".\ProductGroupCriteria.h">
			</File>
			<File
				RelativePath=".\ProductGroupCriteriaListPage.h">
			</File>
			<File
				RelativePath=".\ProductGroupCriteriaMaintenance.h">
			</File>
			<File
				RelativePath=".\ProductGroupCriteriaMatrix.h">
			</File>
			<File
				RelativePath=".\ProductGroupCriteriaPropertiesPage.h">
			</File>
			<File
				RelativePath=".\ProductGroupCriteriaQuery.h">
			</File>
			<File
				RelativePath=".\ProductGroupCriteriaQueryDialog.h">
			</File>
			<File
				RelativePath=".\ProductGroupCriteriaRange.h">
			</File>
			<File
				RelativePath=".\ProductGroupCriteriaValue.h">
			</File>
			<File
				RelativePath=".\ProductGroupDataService.h">
			</File>
			<File
				RelativePath=".\ProductGroupDialog.h">
			</File>
			<File
				RelativePath=".\ProductGroupFrame.h">
			</File>
			<File
				RelativePath=".\ProductGroupHelper.h">
			</File>
			<File
				RelativePath=".\ProductGroupLevel.h">
			</File>
			<File
				RelativePath=".\ProductGroupNavigator.h">
			</File>
			<File
				RelativePath=".\ProductGroupPropertiesPage.h">
			</File>
			<File
				RelativePath=".\ProductGroupQuery.h">
			</File>
			<File
				RelativePath=".\ProductHelper.h">
			</File>
			<File
				RelativePath=".\ProductInfo.h">
			</File>
			<File
				RelativePath=".\ProductInterfaceDataService.h">
			</File>
			<File
				RelativePath=".\ProductInterfaceDialog.h">
			</File>
			<File
				RelativePath=".\ProductLayoutAdvancedPage.h">
			</File>
			<File
				RelativePath=".\ProductLayoutStartPage.h">
			</File>
			<File
				RelativePath=".\ProductMaintenance.h">
			</File>
			<File
				RelativePath=".\ProductPack.h">
			</File>
			<File
				RelativePath=".\ProfileFrame.h">
			</File>
			<File
				RelativePath=".\ProfileMaintenanceSheet.h">
			</File>
			<File
				RelativePath=".\ProfileMaintenanceView.h">
			</File>
			<File
				RelativePath=".\ProfilePage.h">
			</File>
			<File
				RelativePath=".\ProfileTreePane.h">
			</File>
			<File
				RelativePath=".\Progress.h">
			</File>
			<File
				RelativePath=".\ProgressMessage.h">
			</File>
			<File
				RelativePath=".\Prompt.h">
			</File>
			<File
				RelativePath=".\QueryLocations.h">
			</File>
			<File
				RelativePath="..\..\Server\RTClassName.h">
			</File>
			<File
				RelativePath="..\..\Server\RackUsageClass.h">
			</File>
			<File
				RelativePath=".\ReportCommands.h">
			</File>
			<File
				RelativePath=".\ReportHelper.h">
			</File>
			<File
				RelativePath=".\RotationButton.h">
			</File>
			<File
				RelativePath=".\SAXContentHandlerImpl.h">
			</File>
			<File
				RelativePath=".\SAXErrorHandlerImpl.h">
			</File>
			<File
				RelativePath=".\SSACStringArray.h">
			</File>
			<File
				RelativePath=".\SaxContentHandler.h">
			</File>
			<File
				RelativePath=".\SearchAnchor.h">
			</File>
			<File
				RelativePath=".\SearchAnchorDataService.h">
			</File>
			<File
				RelativePath=".\SearchAnchorDialog.h">
			</File>
			<File
				RelativePath=".\SearchAnchorGenerate.h">
			</File>
			<File
				RelativePath=".\Section.h">
			</File>
			<File
				RelativePath=".\Side.h">
			</File>
			<File
				RelativePath=".\SideProfile.h">
			</File>
			<File
				RelativePath=".\SideProfileAttributesPage.h">
			</File>
			<File
				RelativePath=".\SideProfileBayPage.h">
			</File>
			<File
				RelativePath=".\SideProfileButton.h">
			</File>
			<File
				RelativePath=".\SideProfileDataService.h">
			</File>
			<File
				RelativePath=".\SideProfileListDialog.h">
			</File>
			<File
				RelativePath=".\SideProfileSheet.h">
			</File>
			<File
				RelativePath=".\SideProperties.h">
			</File>
			<File
				RelativePath=".\Solution.h">
			</File>
			<File
				RelativePath=".\SolutionDataService.h">
			</File>
			<File
				RelativePath=".\SortListBox.h">
			</File>
			<File
				RelativePath=".\SplashWnd.h">
			</File>
			<File
				RelativePath=".\Startup.h">
			</File>
			<File
				RelativePath=".\ThreadParameters.h">
			</File>
			<File
				RelativePath=".\TreeElement.h">
			</File>
			<File
				RelativePath=".\UDF.h">
			</File>
			<File
				RelativePath=".\UDFCommands.h">
			</File>
			<File
				RelativePath=".\UDFDataService.h">
			</File>
			<File
				RelativePath=".\UDFHelper.h">
			</File>
			<File
				RelativePath=".\UDFMaintenanceDialog.h">
			</File>
			<File
				RelativePath=".\UDFPage.h">
			</File>
			<File
				RelativePath=".\UDFProperties.h">
			</File>
			<File
				RelativePath=".\UserQuery.h">
			</File>
			<File
				RelativePath=".\UserQueryDataService.h">
			</File>
			<File
				RelativePath=".\UserQueryDialog.h">
			</File>
			<File
				RelativePath=".\UtilityCommands.h">
			</File>
			<File
				RelativePath=".\UtilityHelper.h">
			</File>
			<File
				RelativePath=".\ValidateFacility.h">
			</File>
			<File
				RelativePath=".\WMS.h">
			</File>
			<File
				RelativePath=".\WMSExportPage.h">
			</File>
			<File
				RelativePath=".\WMSFacilityInfo.h">
			</File>
			<File
				RelativePath=".\WMSGroup.h">
			</File>
			<File
				RelativePath=".\WMSGroupConnection.h">
			</File>
			<File
				RelativePath=".\WMSGroupDialog.h">
			</File>
			<File
				RelativePath=".\WMSGroupProperties.h">
			</File>
			<File
				RelativePath=".\WMSImportPage.h">
			</File>
			<File
				RelativePath=".\WMSMap.h">
			</File>
			<File
				RelativePath=".\WMSProperties.h">
			</File>
			<File
				RelativePath=".\WMSSheet.h">
			</File>
			<File
				RelativePath=".\WizardCommands.h">
			</File>
			<File
				RelativePath=".\WizardHelper.h">
			</File>
			<File
				RelativePath=".\codes.h">
			</File>
			<File
				RelativePath=".\constants.h">
			</File>
			<File
				RelativePath=".\debug.h">
			</File>
			<File
				RelativePath=".\excel8.h">
			</File>
			<File
				RelativePath=".\font.h">
			</File>
			<File
				RelativePath=".\modal.h">
			</File>
			<File
				RelativePath=".\msflexgrid.h">
			</File>
			<File
				RelativePath=".\picture.h">
			</File>
			<File
				RelativePath=".\qqhclasses.h">
			</File>
			<File
				RelativePath=".\resource.h">
			</File>
			<File
				RelativePath=".\resourcehelper.h">
			</File>
			<File
				RelativePath=".\rowcursor.h">
			</File>
			<File
				RelativePath="ssaBtree.h">
			</File>
			<File
				RelativePath="ssa_exception.h">
			</File>
			<File
				RelativePath=".\stdafx.h">
			</File>
		</Filter>
		<Filter
			Name="Resource Files"
			Filter="ico;cur;bmp;dlg;rc2;rct;bin;cnt;rtf;gif;jpg;jpeg;jpe">
			<File
				RelativePath=".\ARW08DN.ICO">
			</File>
			<File
				RelativePath=".\ARW08UP.ICO">
			</File>
			<File
				RelativePath=".\AssignCriteria.bmp">
			</File>
			<File
				RelativePath=".\AssignProducts.bmp">
			</File>
			<File
				RelativePath=".\Details.bmp">
			</File>
			<File
				RelativePath=".\Doc.bmp">
			</File>
			<File
				RelativePath=".\ExpandArrowDown.ico">
			</File>
			<File
				RelativePath=".\ExpandArrowUp.ico">
			</File>
			<File
				RelativePath=".\Foldcls.bmp">
			</File>
			<File
				RelativePath=".\FolderClose.ico">
			</File>
			<File
				RelativePath=".\FolderOpen.ico">
			</File>
			<File
				RelativePath=".\Foldopen.bmp">
			</File>
			<File
				RelativePath=".\INFO.ICO">
			</File>
			<File
				RelativePath=".\LargeIcons.bmp">
			</File>
			<File
				RelativePath=".\List.bmp">
			</File>
			<File
				RelativePath=".\MaintainCriteria.bmp">
			</File>
			<File
				RelativePath=".\MaintainGroups.bmp">
			</File>
			<File
				RelativePath=".\RackUsage.bmp">
			</File>
			<File
				RelativePath=".\SmallIcons.bmp">
			</File>
			<File
				RelativePath=".\SortSAP.bmp">
			</File>
			<File
				RelativePath=".\Succeed.ico">
			</File>
			<File
				RelativePath=".\add.ico">
			</File>
			<File
				RelativePath=".\add_blue.ico">
			</File>
			<File
				RelativePath=".\aisle.ico">
			</File>
			<File
				RelativePath=".\box.ico">
			</File>
			<File
				RelativePath=".\box1.ico">
			</File>
			<File
				RelativePath=".\dc.ico">
			</File>
			<File
				RelativePath=".\excel.bmp">
			</File>
			<File
				RelativePath=".\exit.bmp">
			</File>
			<File
				RelativePath=".\facility.ico">
			</File>
			<File
				RelativePath=".\facility_inverse.ico">
			</File>
			<File
				RelativePath=".\forklift.ico">
			</File>
			<File
				RelativePath=".\forklift2.ico">
			</File>
			<File
				RelativePath=".\go.bmp">
			</File>
			<File
				RelativePath=".\go.ico">
			</File>
			<File
				RelativePath=".\help.bmp">
			</File>
			<File
				RelativePath=".\instructionsd.bmp">
			</File>
			<File
				RelativePath=".\instructionsu.bmp">
			</File>
			<File
				RelativePath=".\level.ico">
			</File>
			<File
				RelativePath=".\location.ico">
			</File>
			<File
				RelativePath=".\modal.rc">
			</File>
			<File
				RelativePath=".\movedown.ico">
			</File>
			<File
				RelativePath=".\moveup.ico">
			</File>
			<File
				RelativePath=".\newfacilityd.bmp">
			</File>
			<File
				RelativePath=".\newfacilityu.bmp">
			</File>
			<File
				RelativePath=".\openfacilityd.bmp">
			</File>
			<File
				RelativePath=".\openfacilityu.bmp">
			</File>
			<File
				RelativePath=".\properties.bmp">
			</File>
			<File
				RelativePath=".\properties.ico">
			</File>
			<File
				RelativePath=".\section.ico">
			</File>
			<File
				RelativePath=".\section1.ico">
			</File>
			<File
				RelativePath=".\section_inverse.ico">
			</File>
			<File
				RelativePath=".\side.ico">
			</File>
			<File
				RelativePath=".\splash_screen.bmp">
			</File>
			<File
				RelativePath=".\subtract.ico">
			</File>
			<File
				RelativePath=".\succeedhelpd.bmp">
			</File>
			<File
				RelativePath=".\succeedhelpu.bmp">
			</File>
			<File
				RelativePath=".\usewizardd.bmp">
			</File>
			<File
				RelativePath=".\usewizardu.bmp">
			</File>
		</Filter>
	</Files>
	<Globals>
	</Globals>
</VisualStudioProject>

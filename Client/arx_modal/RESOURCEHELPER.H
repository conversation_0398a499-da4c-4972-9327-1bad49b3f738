#ifndef __RESOURCE_HELPER__H__
#define __RESOURCE_HELPER__H__

#include "stdafx.h"

// Autocad 2000 update
#ifdef _AUTOCAD2000
	#include <AcExtensionModule.h>
#endif

class CTemporaryResourceOverride
{
public:
    CTemporaryResourceOverride(HINSTANCE hInstNew);
    CTemporaryResourceOverride(); // default construction

    virtual ~CTemporaryResourceOverride();

    static void SetDefaultResource(HINSTANCE hInstNew);

private:
    void   CommonConstruction(HINSTANCE);
    static HINSTANCE m_hInstanceDefault;
    HINSTANCE m_hInstanceOld;
#ifdef _AUTOCAD2000
	CAcModuleResourceOverride resOverride;
#endif
};

#endif // __RESOURCE_HELPER__H__

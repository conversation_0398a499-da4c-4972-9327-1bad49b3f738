// BayProfileCrossbarButton.cpp : implementation file
//

#include "stdafx.h"
#include "modal.h"
#include "BayProfileCrossbarButton.h"
#include "BayProfileCrossbarPage.h"

#include <dbsymtb.h>

#ifdef _DEBUG
#define new DEBUG_NEW
#undef THIS_FILE
static char THIS_FILE[] = __FILE__;
#endif
#define MAX_PROXIMITY 0

/////////////////////////////////////////////////////////////////////////////
// CBayProfileCrossbarButton

CBayProfileCrossbarButton::CBayProfileCrossbarButton()
{
}


CBayProfileCrossbarButton::~CBayProfileCrossbarButton()
{
}


BEGIN_MESSAGE_MAP(CBayProfileCrossbarButton, CButton)
	//{{AFX_MSG_MAP(CBayProfileCrossbarButton)
	ON_WM_LBUTTONDOWN()
	ON_WM_MOUSEMOVE()
	ON_WM_RBUTTONDOWN()
	ON_WM_LBUTTONDBLCLK()
	//}}AFX_MSG_MAP
END_MESSAGE_MAP()

/////////////////////////////////////////////////////////////////////////////
// CBayProfileCrossbarButton message handlers

void CBayProfileCrossbarButton::DrawItem(LPDRAWITEMSTRUCT lpDrawItemStruct) 
{
	UINT uStyle = DFCS_BUTTONPUSH;
	
	// This code only works with buttons.
	ASSERT(lpDrawItemStruct->CtlType == ODT_BUTTON);
	
	// If drawing selected, add the pushed style to DrawFrameControl.
//	if (lpDrawItemStruct->itemState & ODS_SELECTED)
//		uStyle |= DFCS_PUSHED;
	
	// Draw the button frame.
	::DrawFrameControl(lpDrawItemStruct->hDC, &lpDrawItemStruct->rcItem, 
		DFC_BUTTON, uStyle);
	
	CDC cdc;
	cdc.Attach(lpDrawItemStruct->hDC);
	
	SetMapping(cdc);

	CPen pen, *pPrevPen;
	pen.CreatePen(PS_SOLID, 0, RGB(0,0,0));
	pPrevPen = cdc.SelectObject(&pen);

	CSize vpSize = cdc.GetViewportExt();
	CSize winSize = cdc.GetWindowExt();

	cdc.MoveTo(0, 0);
	cdc.LineTo(0, (int)m_UprightHeight);
	cdc.MoveTo(m_LogicalBayWidth, 0);
	cdc.LineTo(m_LogicalBayWidth, (int)m_UprightHeight);

	for (int i=0; i < m_CrossbarList.GetSize(); ++i) {
		CBayProfileCrossbarInfo info = m_CrossbarList[i];
		CPen tempPen, *tempPrevPen;
		COLORREF c;
		if (info.m_IsSelected)
			c = RGB(255, 0, 0);
		else
			c = RGB(0, 0, 0);

		if (info.m_Height == 0 || info.m_IsHidden)
			tempPen.CreatePen(PS_DOT, 0, c);
		else
			tempPen.CreatePen(PS_SOLID, 0, c);

		tempPrevPen = cdc.SelectObject(&tempPen);

		DrawCrossbar(cdc, i);

		cdc.SelectObject(tempPrevPen);
		tempPen.DeleteObject();
	}

	cdc.SelectObject(pPrevPen);
	pen.DeleteObject();
	cdc.Detach();

}


void CBayProfileCrossbarButton::OnLButtonDown(UINT nFlags, CPoint point) 
{
	CRect r;
	GetClientRect(&r);
	if (! r.PtInRect(point)) {
		CButton::OnLButtonDown(nFlags, point);
		return;
	}

	if ((nFlags & MK_SHIFT) || (nFlags & MK_CONTROL)) {
		int closestLevel = SnapToClosest(point);
		
		if (closestLevel >= 0)
			SelectCrossbar(closestLevel);
		return;
	}

	CDC *pCDC = this->GetWindowDC();

	SetMapping(*pCDC);
	
	CString temp;
	CPoint bayPt(point);
	// Convert the window point to the bay point based on the mapping above
	pCDC->DPtoLP(&bayPt, 1);
	temp.Format("Device: %d,%d, Bay: %d,%d", point.x, point.y, bayPt.x, bayPt.y);
	if (bayPt.x < 0 || bayPt.x > m_LogicalBayWidth || bayPt.y > m_UprightHeight) {
		CButton::OnLButtonDown(nFlags, point);
		return;
	}


	int maxThickness;
	int selectedCrossbar = CheckForCrossbar(bayPt.y, maxThickness);
	if (selectedCrossbar >= 0) {
		SelectCrossbar(selectedCrossbar);
	}
		
	pCDC->Detach();	

	CButton::OnLButtonDown(nFlags, point);

}


int CBayProfileCrossbarButton::CheckForCrossbar(int height, int &thickness)
{

	// If they click below the floor, automatically choose the floor
	if (height <= 0)
		return 0;

	// height is the top of the crossbar
	// we want to find the maximum thickness (up to 4) that
	// the new crossbar can be without overlapping an existing
	// crossbar.  Assume that we have to space them out by
	// at least one unit.  So the maximum thickness a new bar
	// can be is equal to the minimum possible thickness that
	// can separate it from an existing bar

	// Start with the min thickness set to the height of the bar
	// If there are no other bars, this will stay
	int minThickness = height;
	int th;

	for (int i=0; i < m_CrossbarList.GetSize(); ++i) {
		CBayProfileCrossbarInfo info = m_CrossbarList[i];
		
		if (height == info.m_Height)
			return i;
		
		if (height < info.m_Height) {
			th = (int)(info.m_Height-info.m_Thickness-MAX_PROXIMITY-height);
		}
		else {
			th = (int)(height - (info.m_Height+MAX_PROXIMITY));
		}

		if (th <= 0)
			return i;

		if (th < minThickness)
			minThickness = th;
	}

	thickness = minThickness;

	return -1;
}

void CBayProfileCrossbarButton::DrawDashedHorzLine(CDC &cdc, const CPoint &startPt, const CPoint &endPt)
{
	for (int i=0; i < endPt.x - startPt.x; ++i) {
		if (i%2 == 0)
			cdc.MoveTo(startPt.x+i, startPt.y);
		else
			cdc.LineTo(startPt.x+i, startPt.y);
	}

}

void CBayProfileCrossbarButton::OnMouseMove(UINT nFlags, CPoint point) 
{

	CDC *pCDC = GetWindowDC();

	SetMapping(*pCDC);
	
	CPoint bayPt(point);
	// Convert the window point to the bay point based on the mapping above
	pCDC->DPtoLP(&bayPt, 1);

	CBayProfileCrossbarPage *pParent = (CBayProfileCrossbarPage *)GetParent();
	if (bayPt.x < 0 || bayPt.x > m_LogicalBayWidth ||
		bayPt.y < 0 || bayPt.y > m_UprightHeight)
		pParent->UpdateToolTip(0, 0, "");
	else {
		CString temp;
		temp.Format("%d", bayPt.y);
		pParent->UpdateToolTip(point.x, point.y, temp);
	}

	CButton::OnMouseMove(nFlags, point);
}

int CBayProfileCrossbarButton::SnapToClosest(CPoint& point) 
{

	CDC *pCDC = GetWindowDC();

	SetMapping(*pCDC);
	
	CPoint bayPt(point);
	// Convert the window point to the bay point based on the mapping above
	pCDC->DPtoLP(&bayPt, 1);

	int height = bayPt.y;
	int closest = height;
	int closestLevel = 0;

	for (int i=0; i < m_CrossbarList.GetSize(); ++i) {
		CBayProfileCrossbarInfo info = m_CrossbarList[i];
		if (height == info.m_Height)
			return i;

		int dist;
		if (height < info.m_Height) {
			dist = (int)(info.m_Height-info.m_Thickness-height);
		}
		else {
			dist = (int)(height - info.m_Height);
		}
		if (dist < closest) {
			closest = dist;
			closestLevel = i;
		}
	}
	
	return closestLevel;

}

int CBayProfileCrossbarButton::SelectCrossbar(int currentLevel)
{
	int oldSel = -1, newSel = -1;

	for (int i=0; i < m_CrossbarList.GetSize(); ++i) {
		if (i == currentLevel)
			newSel = i;
		if (m_CrossbarList[i].m_IsSelected)
			oldSel = i;
	}

	if (GetParent()->SendMessage(WM_SELECT_LEVEL, newSel, 0) < 0)
		return -1;

	if (oldSel >= 0)
		m_CrossbarList[oldSel].m_IsSelected = FALSE;
	
	if (newSel >= 0)
		m_CrossbarList[newSel].m_IsSelected = TRUE;

	Invalidate(TRUE);

	return 0;
}



void CBayProfileCrossbarButton::SetMapping(CDC &cdc)
{
	CRect r;
	GetClientRect(&r);
	m_LogicalBayWidth = r.Width();

	cdc.SetMapMode(MM_ANISOTROPIC);
	cdc.SetViewportOrg(10, r.bottom-10);
	if (m_UprightHeight == 0)
		cdc.SetWindowExt(m_LogicalBayWidth, 1);
	else
		cdc.SetWindowExt(m_LogicalBayWidth, (int)m_UprightHeight);
	cdc.SetViewportExt(r.Width()-20, -(r.Height()-20));
}


void CBayProfileCrossbarButton::DrawCrossbar(CDC &cdc, int currentLevel)
{
	CBayProfileCrossbarInfo &info = m_CrossbarList[currentLevel];

	cdc.MoveTo(1, (int)info.m_Height);
	cdc.LineTo(m_LogicalBayWidth-1, (int)info.m_Height);

	/* experimental - should be able to draw crossbars to scale
	if (info.m_Thickness == 0) {	// floor thickness is 0
		cdc.MoveTo(1, info.m_Height);
		cdc.LineTo(m_LogicalBayWidth-1, info.m_Height);
	}
	else {	
		for (int i=0; i < info.m_Thickness; ++i) {
			cdc.MoveTo(1, info.m_Height-i);
			cdc.LineTo(m_LogicalBayWidth-1, info.m_Height-i);
		}
	}
	*/

}

void CBayProfileCrossbarButton::OnRButtonDown(UINT nFlags, CPoint point) 
{

	if ((nFlags & MK_SHIFT) || (nFlags & MK_CONTROL)) {
		int closestLevel = SnapToClosest(point);
		
		if (closestLevel >= 0)
			SelectCrossbar(closestLevel);
		return;
	}

	CDC *pCDC = this->GetWindowDC();

	SetMapping(*pCDC);
	
	CString temp;
	CPoint bayPt(point);
	// Convert the window point to the bay point based on the mapping above
	pCDC->DPtoLP(&bayPt, 1);
	temp.Format("Device: %d,%d, Bay: %d,%d", point.x, point.y, bayPt.x, bayPt.y);
	if (bayPt.x < 0 || bayPt.x > m_LogicalBayWidth || bayPt.y > m_UprightHeight) {
		CButton::OnRButtonDown(nFlags, point);
		return;
	}


	int maxThickness;
	int selectedCrossbar = CheckForCrossbar(bayPt.y, maxThickness);
	
	if ( selectedCrossbar >= 0)
		SelectCrossbar(selectedCrossbar);

	ClientToScreen(&point);

	LPARAM lParam = MAKELPARAM(point.x, point.y);

	GetParent()->PostMessage(WM_CONTEXTMENU, WPARAM(this->m_hWnd), lParam);

	CButton::OnRButtonDown(nFlags, point);
}

int CBayProfileCrossbarButton::GetSelectedLevel(CPoint &point)
{
	CDC *pCDC = this->GetWindowDC();

	SetMapping(*pCDC);
	
	CPoint bayPt(point);
	// Convert the window point to the bay point based on the mapping above
	pCDC->DPtoLP(&bayPt, 1);
	if (bayPt.x < 0 || bayPt.x > m_LogicalBayWidth || bayPt.y > m_UprightHeight) {
		return -1;
	}

	int thickness;

	return CheckForCrossbar(bayPt.y, thickness);


}

double CBayProfileCrossbarButton::GetBayHeightFromPoint(CPoint &pt)
{
	CDC *pCDC = this->GetWindowDC();

	SetMapping(*pCDC);
	
	CPoint bayPt(pt);
	// Convert the window point to the bay point based on the mapping above
	pCDC->DPtoLP(&bayPt, 1);

	return bayPt.y;

}


void CBayProfileCrossbarButton::OnLButtonDblClk(UINT nFlags, CPoint point) 
{
	CRect r;
	GetClientRect(&r);
	if (! r.PtInRect(point)) {
		CButton::OnLButtonDblClk(nFlags, point);
		return;
	}

	if ((nFlags & MK_SHIFT) || (nFlags & MK_CONTROL)) {
		int closestLevel = SnapToClosest(point);
		
		if (closestLevel >= 0) {
			SelectCrossbar(closestLevel);
			GetParent()->SendMessage(WM_VIEW_LEVEL, closestLevel, 0);
		}
		return;
	}

	CDC *pCDC = this->GetWindowDC();

	SetMapping(*pCDC);
	
	CString temp;
	CPoint bayPt(point);
	// Convert the window point to the bay point based on the mapping above
	pCDC->DPtoLP(&bayPt, 1);
	temp.Format("Device: %d,%d, Bay: %d,%d", point.x, point.y, bayPt.x, bayPt.y);
	if (bayPt.x < 0 || bayPt.x > m_LogicalBayWidth || bayPt.y > m_UprightHeight) {
		CButton::OnLButtonDblClk(nFlags, point);
		return;
	}


	int maxThickness;
	int selectedCrossbar = CheckForCrossbar(bayPt.y, maxThickness);
	if (selectedCrossbar < 0) {
		// They didn't select a crossbar so add a new one
		CBayProfileCrossbarPage *pParent = (CBayProfileCrossbarPage *)GetParent();
		if (GetParent()->SendMessage(WM_ADD_LEVEL, bayPt.y, (maxThickness > 4) ? 4 : maxThickness) == 0) {
			this->Invalidate(TRUE);
		}
	}
	else {
		SelectCrossbar(selectedCrossbar);
		GetParent()->SendMessage(WM_VIEW_LEVEL, selectedCrossbar, 0);
	}
		

	pCDC->Detach();

	CButton::OnLButtonDblClk(nFlags, point);
}

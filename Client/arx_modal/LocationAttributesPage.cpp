// LocationAttributesPage.cpp : implementation file
//

#include "stdafx.h"
#include "modal.h"
#include "LocationAttributesPage.h"
#include "BayProfileDataService.h"
#include "UtilityHelper.h"
#include "Constants.h"
#include "HelpService.h"

#ifdef _DEBUG
#define new DEBUG_NEW
#undef THIS_FILE
static char THIS_FILE[] = __FILE__;
#endif

extern 	CBayProfileDataService bayProfileDataService;
extern CUtilityHelper utilityHelper;
extern CHelpService helpService;
/////////////////////////////////////////////////////////////////////////////
// CLocationAttributesPage property page

IMPLEMENT_DYNCREATE(CLocationAttributesPage, CPropertyPage)

CLocationAttributesPage::CLocationAttributesPage() : CPropertyPage(CLocationAttributesPage::IDD)
{
	//{{AFX_DATA_INIT(CLocationAttributesPage)
	//}}AFX_DATA_INIT
}

CLocationAttributesPage::~CLocationAttributesPage()
{
	for (int i=0; i < m_ExternalSystemList.GetSize(); ++i)
		delete m_ExternalSystemList[i];
}

void CLocationAttributesPage::DoDataExchange(CDataExchange* pDX)
{
	CPropertyPage::DoDataExchange(pDX);
	//{{AFX_DATA_MAP(CLocationAttributesPage)
	DDX_Control(pDX, IDC_SCROLLBAR, m_ScrollBar);
	DDX_Control(pDX, IDC_GROUP_BOX, m_GroupButton);
	DDX_Control(pDX, IDC_EXTERNAL_SYSTEM_LIST, m_ExternalListCtrl);
	//}}AFX_DATA_MAP
}


BEGIN_MESSAGE_MAP(CLocationAttributesPage, CPropertyPage)
	//{{AFX_MSG_MAP(CLocationAttributesPage)
	ON_CBN_SELCHANGE(IDC_EXTERNAL_SYSTEM_LIST, OnSelchangeExternalSystemList)
	ON_WM_VSCROLL()
	ON_WM_HELPINFO()
	//}}AFX_MSG_MAP
END_MESSAGE_MAP()

/////////////////////////////////////////////////////////////////////////////
// CLocationAttributesPage message handlers
BOOL CLocationAttributesPage::OnInitDialog() 
{
	CPropertyPage::OnInitDialog();
	
	m_CurrentSystemId = -1;
	CStringArray systems;

	try {
		bayProfileDataService.GetExternalSystemList(systems);
	}
	catch (...) {
		utilityHelper.ProcessError("Error getting list of external systems.");
	}

	for (int i=0; i < systems.GetSize(); ++i) {
		CExternalSystem *pSystem = new CExternalSystem;
		pSystem->Parse(systems[i]);
		m_ExternalSystemList.Add(pSystem);
		CString temp;
		temp.Format("%s %s", pSystem->m_Name, pSystem->m_Version);
		int nItem = m_ExternalListCtrl.AddString(temp);
		m_ExternalListCtrl.SetItemData(nItem, (unsigned long)pSystem);
	}
	
	if (systems.GetSize() > 0) {
		m_ExternalListCtrl.SetCurSel(0);
		m_CurrentSystemId = m_ExternalListCtrl.GetItemData(0);
	}

	CRect r;
	m_ExternalListCtrl.GetWindowRect(&r);
	m_ExternalListCtrl.SetWindowPos(NULL, 0, 0, r.Width(), r.Height()*(systems.GetSize()+1), SWP_NOMOVE|SWP_NOZORDER);
	
	int dwStyle = GetWindowLong(m_GroupButton.m_hWnd, GWL_EXSTYLE);
	dwStyle |= WS_EX_CONTROLPARENT;
	SetWindowLong(m_GroupButton.m_hWnd, GWL_EXSTYLE, dwStyle);
	
	if (m_pLocation->m_InfoList.GetSize() == 0)
		GetDlgItem(IDC_STATIC_MESSAGE)->ShowWindow(SW_SHOW);
	else
		GetDlgItem(IDC_STATIC_MESSAGE)->ShowWindow(SW_HIDE);

	CStringArray infoList;
	for (i=0; i < m_pLocation->m_InfoList.GetSize(); ++i) {
		infoList.Add(m_pLocation->m_InfoList[i]->Stream());
		delete m_pLocation->m_InfoList[i];
	}

	m_pLocation->m_InfoList.RemoveAll();

	utilityHelper.SortStringArray(infoList);

	for (i=0; i < infoList.GetSize(); ++i) {
		CString temp = infoList[i].Mid(infoList[i].Find("|")+1);
		CLevelProfileExternalInfo *pInfo = new CLevelProfileExternalInfo;
		pInfo->Parse(temp);
		m_pLocation->m_InfoList.Add(pInfo);
	}

	return TRUE;  // return TRUE unless you set the focus to a control
	              // EXCEPTION: OCX Property Pages should return FALSE
}


BOOL CLocationAttributesPage::OnSetActive() 
{
	UpdateScreenFromLocation();
	
	return CPropertyPage::OnSetActive();
}

BOOL CLocationAttributesPage::OnKillActive() 
{
	if (UpdateLocationFromScreen() < 0)
		return FALSE;
	
	return CPropertyPage::OnKillActive();
}

void CLocationAttributesPage::OnSelchangeExternalSystemList() 
{
	UpdateLocationFromScreen();

	CreateAttributeDisplay();
	
}


int CLocationAttributesPage::CreateAttributeDisplay()
{
	int curSel = m_ExternalListCtrl.GetCurSel();
	if (curSel < 0)
		return -1;

	CExternalSystem *pSystem = (CExternalSystem *)m_ExternalListCtrl.GetItemData(curSel);

	CRect clientRect, r, r2;
	CString tmp, str;
	CStatic *pStatic;
	CEdit *pEdit;
	CComboBox *pComboBox;
	CFont *font = this->GetFont();
	CWnd *pWnd;
	CString temp;

	//m_GroupButton.SetButtonStyle(BS_GROUPBOX);

	m_ScrollPos = 0;

	for (int i=0; i < m_InputControls.GetSize(); ++i)
		delete m_InputControls[i];

	for (i=0; i < m_Labels.GetSize(); ++i)
		delete m_Labels[i];

	

	m_GroupButton.GetClientRect(&clientRect);
	clientRect.top += 5;
	// r is the rectangle holding the static label
	r.left = 2;							// starting x position within property page
	r.top = 15;							// starting y position within property page
	r.right = r.left + clientRect.Width()/3 - 4;
	r.bottom = r.top + 25;				// the size of each field is 30


	m_InputControls.RemoveAll();
	m_Labels.RemoveAll();

	for (i=0; i < m_pLocation->m_InfoList.GetSize(); ++i) {
		CLevelProfileExternalInfo *pInfo = m_pLocation->m_InfoList[i];
		if (pInfo->m_ExternalSystemDBId != pSystem->m_ExternalSystemDBId)
			continue;

		// Create the static label
		pStatic = new CStatic;
		r.top += 3;		// to make more centered
		str = pInfo->m_Name;
		str += ":";
		pStatic->Create(str, WS_VISIBLE|SS_RIGHT|WS_CHILD, r, &m_GroupButton, 0);

		pStatic->SetFont(font);
		m_Labels.Add(pStatic);


		// Create the edit or combo box
		r.top -= 5;
		// r2 is the rectangle holding the input controls
		r2.left = clientRect.Width()/3+2;
		r2.top = r.top;
		CRect sr;
		m_ScrollBar.GetWindowRect(&sr);
		ScreenToClient(&sr);
		r2.right = clientRect.Width()*3/4; // - sr.Width() - 15;
		r2.bottom = r2.top + 25;

		if (pInfo->m_DataType != DT_LIST) {
			pEdit = new CEdit;
			pEdit->CreateEx( WS_EX_CLIENTEDGE, "EDIT", "", 
				WS_VISIBLE|WS_CHILD|WS_TABSTOP|ES_AUTOHSCROLL|WS_BORDER, r2, &m_GroupButton, WM_USER+i);
			pEdit->SetFont(font);
			if (pInfo->m_Value != "") {
				if (pInfo->m_DataType == DT_INT)
					temp.Format("%d", atoi(pInfo->m_Value));
				else if (pInfo->m_DataType == DT_FLOAT)
					temp.Format("%0.3f", atof(pInfo->m_Value));
				else
					temp = pInfo->m_Value;
			}

			pEdit->SetWindowText(temp);
			pEdit->EnableWindow(TRUE);
			if (pInfo->m_Length > 0)
				pEdit->SetLimitText(pInfo->m_Length);

			m_InputControls.Add((CWnd *)pEdit);
		}
		else {
			pComboBox = new CComboBox;
			pComboBox->Create(WS_VISIBLE|WS_CHILD|WS_TABSTOP|WS_BORDER|CBS_DROPDOWNLIST, 
				r2, &m_GroupButton, WM_USER+i);
			pComboBox->SetFont(font);
			pComboBox->EnableWindow(TRUE);
			for (int j=0; j < pInfo->m_ListValues.GetSize(); ++j) {
				int nItem = pComboBox->AddString(pInfo->m_ListDisplayValues[j]);
				pComboBox->SetItemData(nItem, j);
				if (pInfo->m_Value == pInfo->m_ListValues[j])
					pComboBox->SetCurSel(nItem);
			}

			m_InputControls.Add((CWnd *)pComboBox);

			pComboBox->SetWindowPos(NULL, 0, 0, r2.Width(), 
				r2.Height()*pInfo->m_ListValues.GetSize()+1, SWP_NOMOVE|SWP_NOZORDER);
		}

		// Move down to the position of the next control
		r.top = r.top + 25 + 10;
		r.bottom = r.top + 25;

	}

	if (m_InputControls.GetSize() > 0) {
		pWnd = m_InputControls[0];
		pWnd->SetFocus();
	}

	SetScrollSizes();


	UpdateData(FALSE);

	return 0;
}


void CLocationAttributesPage::SetScrollSizes()
{
	SCROLLINFO si;
	
	CRect r;
	m_GroupButton.GetClientRect(&r);
	CRect bottomRect;

	if (m_InputControls.GetSize() > 0) {
		m_InputControls[m_InputControls.GetSize()-1]->GetWindowRect(&bottomRect);
		m_GroupButton.ScreenToClient(&bottomRect);
		bottomRect.bottom += 15;
	}
	else
		bottomRect.bottom = 0;

	si.fMask = SIF_RANGE | SIF_PAGE | SIF_POS;
	si.cbSize = sizeof(si);
	si.nMin = 5;
	if (r.Height() < bottomRect.bottom) {
		si.nMax = bottomRect.bottom ;
		si.nPage = r.Height();				// I chose this value at random
		m_ScrollPos = min(m_ScrollPos, (bottomRect.bottom)-r.Height());
		m_ScrollBar.ShowScrollBar(TRUE);
	}
	else {
		m_ScrollPos = si.nMax = si.nPage = 0;
		m_ScrollBar.ShowScrollBar(FALSE);
	}

	si.nPos = m_ScrollPos;

	m_ScrollBar.SetScrollInfo(&si, TRUE);
}



int CLocationAttributesPage::UpdateScreenFromLocation()
{
	int curSel = m_ExternalListCtrl.GetCurSel();
	if (curSel < 0)
		return 0;

	if (m_pLocation->m_InfoList.GetSize() > 0 && m_InputControls.GetSize() == 0)
		CreateAttributeDisplay();

	for (int i=0; i < m_InputControls.GetSize(); ++i) {
		int id = m_InputControls[i]->GetDlgCtrlID() - WM_USER;

		CLevelProfileExternalInfo *pLocInfo = m_pLocation->m_InfoList[id];
		if (pLocInfo->m_DataType == DT_LIST) {
			CComboBox *pBox = (CComboBox *)m_InputControls[i];
			for (int j=0; j < pLocInfo->m_ListValues.GetSize(); ++j) {
				if (pLocInfo->m_ListValues[j] == pLocInfo->m_Value)
					break;
			}
			if (j >= pLocInfo->m_ListValues.GetSize())
				j = 0;

			for (int k=0; (int)k < pBox->GetCount(); ++k) {
				if ((int)pBox->GetItemData(k) == j) {
					pBox->SetCurSel(k);
					break;
				}
			}
		}
		else {
			CEdit *pEdit = (CEdit *)m_InputControls[i];
			pEdit->SetWindowText(pLocInfo->m_Value);
		}
	}	

	UpdateData(FALSE);

	return 0;
}


int CLocationAttributesPage::UpdateLocationFromScreen()
{
	UpdateData(TRUE);
	
	CEdit *pEdit;
	CComboBox *pBox;

	int curSel = m_ExternalListCtrl.GetCurSel();
	if (curSel < 0)
		return 0;

	for (int i=0; i < m_InputControls.GetSize(); ++i) {
		int id = m_InputControls[i]->GetDlgCtrlID() - WM_USER;
		
		CLevelProfileExternalInfo *pLocInfo = m_pLocation->m_InfoList[id];
		CString str;
		m_InputControls[i]->GetWindowText(str);
		switch (pLocInfo->m_DataType) {
		case DT_LIST:
			if (pLocInfo->m_DataType == DT_LIST) {
				pBox = (CComboBox *)m_InputControls[i];
				pLocInfo->m_Value = pLocInfo->m_ListValues[pBox->GetItemData(pBox->GetCurSel())];
			}
			break;
		case DT_FLOAT:
			if (! utilityHelper.IsFloat(str)) {
				CString temp;
				temp.Format("Please enter a valid decimal number for %s", pLocInfo->m_Name);
				AfxMessageBox(temp);
				pEdit = (CEdit *)m_InputControls[i];
				pEdit->SetSel(0, -1);
				pEdit->SetFocus();
				return -1;
			}
			pLocInfo->m_Value = str;	
			break;
		case DT_INT:
			if (! utilityHelper.IsInteger(str)) {
				CString temp;
				temp.Format("Please enter a valid integer for %s", pLocInfo->m_Name);
				AfxMessageBox(temp);
				pEdit = (CEdit *)m_InputControls[i];
				pEdit->SetSel(0, -1);
				pEdit->SetFocus();
				return -1;
			}
			pLocInfo->m_Value = str;	
			break;
		case DT_FORMULA:
			// todo: figure out valid formulas
			pLocInfo->m_Value = str;
			break;
		default:
			pLocInfo->m_Value = str;
			break;
		}
	}


	return 0;
}



void CLocationAttributesPage::OnVScroll(UINT nSBCode, UINT nPos, CScrollBar* pScrollBar) 
{
	if (pScrollBar != &m_ScrollBar)
		return;

	static int xPos;        // current horizontal scrolling position 
	static int yPos;        // current vertical scrolling position 
	
	SCROLLINFO si;
	// Get all the vertial scroll bar information
	si.cbSize = sizeof (si);
	si.fMask  = SIF_ALL;
	pScrollBar->GetScrollInfo(&si);
	
	// Save the position for comparison later on
	yPos = si.nPos;
	
	switch (nSBCode)
	{
		// user clicked the HOME keyboard key
	case SB_TOP:
		si.nPos = si.nMin;
		break;
		
		// user clicked the END keyboard key
	case SB_BOTTOM:
		si.nPos = si.nMax;
		break;
		
		// user clicked the top arrow
	case SB_LINEUP:
		si.nPos -= 1;
		break;
		
		// user clicked the bottom arrow
	case SB_LINEDOWN:
		si.nPos += 1;
		break;
		
		// user clicked the shaft above the scroll box
	case SB_PAGEUP:
		si.nPos -= si.nPage;
		break;
		
		// user clicked the shaft below the scroll box
	case SB_PAGEDOWN:
		si.nPos += si.nPage;
		break;
		
		// user dragged the scroll box
	case SB_THUMBTRACK:
		si.nPos = si.nTrackPos;
		break;
		
	default:
		break; 
	}
	
	// Set the position and then retrieve it.  Due to adjustments
	//   by Windows it may not be the same as the value set.
	si.fMask = SIF_POS;
	pScrollBar->SetScrollInfo(&si, TRUE);
	pScrollBar->GetScrollInfo(&si);
	
	// If the position has changed, scroll window and update it
	if (si.nPos != yPos)
	{     
		m_GroupButton.ScrollWindowEx(0, (yPos-si.nPos), NULL, NULL, NULL, NULL, SW_SCROLLCHILDREN);
		/*
		for (int i=0; i < m_Labels.GetSize(); ++i) {
			CRect r;
			m_Labels[i]->GetWindowRect(&r);
			m_GroupButton.ScreenToClient(&r);
			r.top += 1 * (yPos-si.nPos);
			r.bottom += 1 * (yPos-si.nPos);
			m_Labels[i]->MoveWindow(&r, TRUE);
			m_Labels[i]->Invalidate(TRUE);
		}

		for (i=0; i < m_InputControls.GetSize(); ++i) {
			CRect r;
			m_InputControls[i]->GetWindowRect(&r);
			m_GroupButton.ScreenToClient(&r);
			r.top += 1 * (yPos-si.nPos);
			r.bottom += 1 * (yPos-si.nPos);
			m_InputControls[i]->MoveWindow(&r, TRUE);
			m_InputControls[i]->Invalidate(TRUE);
		}
		*/
	}
	
//	CRect r;
//	m_GroupButton.GetWindowRect(&r);
//	ScreenToClient(&r);
//	this->InvalidateRect(&r, TRUE);

	m_GroupButton.Invalidate(TRUE);

	CPropertyPage::OnVScroll(nSBCode, nPos, pScrollBar);
}


BOOL CLocationAttributesPage::OnHelpInfo(HELPINFO* pHelpInfo) 
{
	helpService.ShowFieldHelp(IDD, pHelpInfo->iCtrlId);
	return FALSE;

}

BOOL CLocationAttributesPage::OnNotify(WPARAM wParam, LPARAM lParam, LRESULT* pResult) 
{
	NMHDR* pNMHDR = (NMHDR*)lParam;

	if (pNMHDR->code == PSN_HELP) {
		helpService.ShowScreenHelp(IDD);
		return TRUE;
	}
	
	return CPropertyPage::OnNotify(wParam, lParam, pResult);
}
// UtilityHelper.cpp: implementation of the CUtilityHelper class.
//
//////////////////////////////////////////////////////////////////////

#include "stdafx.h"
#include "modal.h"
#include "UtilityHelper.h"

#include "AutoCADCommands.h"

#include "UserQueryDialog.h"
#include "ValidateFacility.h"

#include "BTreeHelper.h"

#include "ssa_exception.h"
#include "qqhclasses.h"
#include "FacilityDataService.h"
#include "Constants.h"
#include "CleanFacilityDialog.h"
#include "CheckMessageDialog.h"

#include "ResourceHelper.h"
#include "NavigationHelper.h"

#include "DataAccessService.h"
#include "ControlService.h"
#include "ForteService.h"

#include "ElementMaintenanceHelper.h"
#include "BayProfile.h"
#include "BayProfileDataService.h"
#include "ProgressMessage.h"
#include "ProcessingMessage.h"
#include "FacilityHelper.h"

#include <strstream>


#include <dbents.h>
#include <dbsymtb.h>
#include <adscodes.h>
#include <stdlib.h>
#include <vector>

#include "base.h"
#include "MiscClasses.h"
#include "SLOTSessionMgr.h"
#include "SrvcObjs.h"


#ifdef _DEBUG
#undef THIS_FILE
static char THIS_FILE[]=__FILE__;
#define new DEBUG_NEW
#endif

using namespace std;

extern TreeElement changesTree;

extern CForteService forteService;
extern CControlService controlService;
extern CDataAccessService dataAccessService;
//////////////////////////////////////////////////////////////////////
// Construction/Destruction
//////////////////////////////////////////////////////////////////////

CUtilityHelper::CUtilityHelper()
{

}

CUtilityHelper::~CUtilityHelper()
{

}


void CUtilityHelper::ZoomByHandle()
{

	char result[132], magfactor[132];
	if (ads_getstring(1, "Enter handle: ", result) != RTNORM)
		return;

	if (ads_getstring(1, "Enter magnification factor: ", magfactor) != RTNORM)
		return;

	CAutoCADCommands::ZoomToHandle(result, atoi(magfactor));
}


void CUtilityHelper::DumpFacilityTree()
{
	char result[132];

	if (ads_getstring(1, "Enter path name: ", result) == RTNORM) {
		if (strlen(result) == 0) {
			strcpy(result, "c:\\temp\\tree.out");
		}
		DumpTree(result, changesTree);	
		ads_printf("Dump is complete.\n");
		ads_printf("Command: ");
	}

	return;

}


void CUtilityHelper::DumpTree(CString file, TreeElement &tree)
{

	CString temp;
	void *pParent;
	CBTreeHelper btHelper;

	FILE *f = fopen(file, "w");
	if (f == NULL)
		return;

	int fo;
	qqhSLOTLocation tempLoc;
	qqhSLOTSection section;
	qqhSLOTAisle aisle;
	qqhSLOTSide side;
	qqhSLOTBay bay;
	qqhSLOTLevel level;


	TreeElement *sPtr, *aPtr, *siPtr, *bPtr, *lPtr, *loPtr;

	for (int s=0; s < tree.treeChildren.GetSize(); ++s) {
		pParent = (void *)(tree.treeChildren[s].treeParent);
		sPtr = &tree.treeChildren[s];
		btHelper.GetBtSection(sPtr->fileOffset, section);	
		fprintf(f, "Section: %s - %d (%d) - Parent: %x, Ptr: %x\n", section.getDescription(),
			sPtr->elementDBID, sPtr->fileOffset, pParent, sPtr);

		for (int a=0; a < sPtr->treeChildren.GetSize(); ++a) {
			aPtr = &sPtr->treeChildren[a];
			btHelper.GetBtAisle(aPtr->fileOffset, aisle);
			fprintf(f, "\tAisle: %s - %d(%d) - %s - Parent: %x, Ptr: %x\n",
				aisle.getDescription(),
				aPtr->elementDBID,
				aPtr->fileOffset,
				aPtr->acadHandle,
				aPtr->treeParent,
				aPtr);

			for (int si=0; si < aPtr->treeChildren.GetSize(); ++si) {
				siPtr = &aPtr->treeChildren[si];
				btHelper.GetBtSide(siPtr->fileOffset, side);
				fprintf(f, "\t\tSide: %s - %d(%d) - Parent: %x, Ptr: %x\n", 
					side.getDescription(),
					siPtr->elementDBID,
					siPtr->fileOffset,
					siPtr->treeParent,
					siPtr);

				for (int b=0; b < siPtr->treeChildren.GetSize(); ++b) {
					bPtr = &siPtr->treeChildren[b];
					btHelper.GetBtBay(bPtr->fileOffset, bay);
					fprintf(f, "\t\t\tBay: %s - %d(%d) - %s - Parent: %x, Ptr: %x - X-Coord: %d\n", 
						bay.getDescription(),
						bPtr->elementDBID,
						bPtr->fileOffset,
						bPtr->acadHandle,
						bPtr->treeParent,
						bPtr, bPtr->xCoord);

					for (int le=0; le < bPtr->treeChildren.GetSize(); ++le) {
						lPtr = &bPtr->treeChildren[le];
						btHelper.GetBtLevel(lPtr->fileOffset, level);
						fprintf(f, "\t\t\t\tLevel: %s - %d(%d)\n", 
							level.getDescription(),
							lPtr->elementDBID,
							lPtr->fileOffset);

						for (int l=0; l < lPtr->treeChildren.GetSize(); ++l) {
							loPtr = &lPtr->treeChildren[l];
							fo = loPtr->fileOffset;
							btHelper.GetBtLocation(fo, tempLoc);
							fprintf(f, "\t\t\t\t\tLocation: %s - %d(%d)(%d, %d, %d)\n", 
								tempLoc.getDescription(),
								loPtr->elementDBID,
								fo,
								tempLoc.getCoord().getX(), tempLoc.getCoord().getY(), tempLoc.getCoord().getZ());
						}
					}
				}
			}
		}
	}

	POSITION pos;
	int key, value;

	fprintf(f, "\n\nDeleted Sections:\n");
	pos = tree.DeletedSectionMap.GetStartPosition();
	while (pos != NULL) {
		tree.DeletedSectionMap.GetNextAssoc(pos, key, value);
		fprintf(f, "\t%d\n", value);
	}

	fprintf(f, "\n\nDeleted Aisles:\n");
	pos = tree.DeletedAisleMap.GetStartPosition();
	while (pos != NULL) {
		tree.DeletedAisleMap.GetNextAssoc(pos, key, value);
		fprintf(f, "\t%d\n", value);
	}

	fprintf(f, "\n\nDeleted Sides:\n");
	pos = tree.DeletedSideMap.GetStartPosition();
	while (pos != NULL) {
		tree.DeletedSideMap.GetNextAssoc(pos, key, value);
		fprintf(f,"\t%d\n", value);
	}
	
	fprintf(f, "\n\nDeleted Bays:\n");
	pos = tree.DeletedBayMap.GetStartPosition();
	while (pos != NULL) {
		tree.DeletedBayMap.GetNextAssoc(pos, key, value);
		fprintf(f, "\t%d\n", value);
	}

	fclose(f);

	return;			
						
}


void CUtilityHelper::ListHandles()
{
	CAutoCADCommands::ListHandles();

}



void CUtilityHelper::UserQuery()
{
	CUserQueryDialog dlg;

	dlg.DoModal();
	
	return;

}


void CUtilityHelper::ValidateFacility(void)
{

	AcDbDatabase *pDb;
	pDb = acdbCurDwg();
	AcDbHandle objHandle;
	AcDbObjectId objId;
	char objHandleStr[20];
	AcDbEntity *pEnt;
	CString strHandle;
	CString fileName;
	CSsaStringArray bayHandlesList;
	CStringArray notInDBList, notInAcadList, acadList, notInDBHandleList;
	CString bayHandle, handle, tmpStr;
	BOOL found;
	resbuf *resBuffer;
	AcDbBlockReference * pBlockRef;
	AcGePoint3d tempPoint;

	FILE *fp = fopen("c:\\temp\\validatefacility.txt","w");

	
	try {
		CFacilityDataService facilityDataService;
		facilityDataService.GetBayHandleList(bayHandlesList);
	}
	catch (...) {
		AfxMessageBox("Error getting list of bay handles from the database.");
		return;
	}

	CValidateFacility *dlg = new CValidateFacility(NULL);

	AcDbBlockTable *pBlkTbl;
	pDb->getBlockTable(pBlkTbl, AcDb::kForRead);
	
	AcDbBlockTableRecord *pBlkTblRcd;
	
	pBlkTbl->getAt(ACDB_MODEL_SPACE, pBlkTblRcd, AcDb::kForRead);
	
	pBlkTbl->close();
	
	AcDbBlockTableRecordIterator *pBlkTblRcdItr;
	pBlkTblRcd->newIterator(pBlkTblRcdItr);
	
	//   AcDbEntity *pEnt;
	for (pBlkTblRcdItr->start(); !pBlkTblRcdItr->done(); pBlkTblRcdItr->step())
	{
		pBlkTblRcdItr->getEntity(pEnt, AcDb::kForRead);
		//ads_printf("classname: %s\n", (pEnt->isA())->name());

		// make sure it's a bay
		resBuffer = pEnt->xData();
		if (resBuffer) {
			if ( CString(resBuffer->rbnext->resval.rstring) == "" )
				continue;
		}
		else
			continue;
		
		pEnt->getAcDbHandle(objHandle);
		objHandle.getIntoAsciiBuffer(objHandleStr);
		ads_printf("%s\n", objHandleStr);
		fprintf(fp,"%s\n",objHandleStr);
		acadList.Add(objHandleStr);

		found = FALSE;
		for (int i=0; i < bayHandlesList.GetSize(); ++i) {
			bayHandle = bayHandlesList[i];
			if (bayHandle.Find(CString(objHandleStr)) > -1) {
				found = TRUE;
				break;
			}
		}
		if (! found) {
			pBlockRef = (AcDbBlockReference *)(pEnt);
			tempPoint = pBlockRef->position();
			tmpStr.Format("Handle: %7.7s   Position: (%.2f,%.2f)",objHandleStr,tempPoint.x,tempPoint.y);
			notInDBList.Add(tmpStr);
			notInDBHandleList.Add(objHandleStr);
		}


		pEnt->close();

	}
	pBlkTblRcd->close();
	
	delete pBlkTblRcdItr;

	CAutoCADCommands::ColorAllObjects();

	for (int i=0; i < notInDBHandleList.GetSize(); ++i)
		CAutoCADCommands::ColorDrawingObjectByHandle(notInDBHandleList[i], kRed);

		
	int idx;
	CString bayID, bayName, aisleName, xCoord, yCoord;
	for (i=0; i < bayHandlesList.GetSize(); ++i) {
		bayHandle = bayHandlesList[i];
		idx = bayHandle.Find("|");
		handle = bayHandle.Left(idx);
		bayHandle = bayHandle.Right(bayHandle.GetLength()-(idx+1));



		found = FALSE;
		for (int j=0; j < acadList.GetSize(); ++j) {
			if (acadList[j] == handle) {
				found = TRUE;
				break;
			}
		}
		if (! found) {
			idx = bayHandle.Find("|");
			bayName = bayHandle.Left(idx);
			bayHandle = bayHandle.Right(bayHandle.GetLength()-(idx+1));
			
			idx = bayHandle.Find("|");
			bayID = bayHandle.Left(idx);
			bayHandle = bayHandle.Right(bayHandle.GetLength()-(idx+1));
			
			idx = bayHandle.Find("|");
			aisleName = bayHandle.Left(idx);
			bayHandle = bayHandle.Right(bayHandle.GetLength()-(idx+1));
			
			idx = bayHandle.Find("|");
			xCoord = bayHandle.Left(idx);
			bayHandle = bayHandle.Right(bayHandle.GetLength()-(idx+1));
			
			idx = bayHandle.Find("|");
			yCoord = bayHandle.Left(idx);
			tmpStr.Format("Handle: %7.7s   Bay: %s   Bay ID: %s   Aisle: %s   Position: (%d,%d)", 
				handle, bayName, bayID, aisleName, atoi(xCoord), atoi(yCoord));
			notInAcadList.Add(tmpStr);
		}
	}

	tmpStr.Format("Total Handles in Autocad: %d", acadList.GetSize());
	dlg->m_text += tmpStr + "\r\n";
	tmpStr.Format("Total Handles in Database: %d", bayHandlesList.GetSize());
	dlg->m_text += tmpStr + "\r\n";

	dlg->m_text += "\r\n\r\n";
	dlg->m_text += "Handles that are in Autocad but not in the database\r\n";
	dlg->m_text += "---------------------------------------------------\r\n";
	for (int k=0; k < notInDBList.GetSize(); ++k) {
		dlg->m_text += notInDBList[k] + "\r\n";
	}

	dlg->m_text += "\r\n\r\n";
	dlg->m_text += "Handles that are in the database but not in Autocad\r\n";
	dlg->m_text += "---------------------------------------------------\r\n";

	for (int l=0; l < notInAcadList.GetSize(); ++l) {
		dlg->m_text += notInAcadList[l] + "\r\n";
	}


	fclose(fp);

	dlg->DoModal();
	
	delete dlg;

	return;
}


//////////////////////////////////////////////////////////////////////
// Function Name : BuildArrayofStrings
// Classname : None
// Description : Parse a CString
// Date Created : 10/01/98
// Author : wbh
//////////////////////////////////////////////////////////////////////
// Inputs : CString
// Outputs : Array of CStrings
// Explanation : 
//  Take a CString and parse it out into a CSsaStringArray
//  based on tokenizing by a '\n'
//////////////////////////////////////////////////////////////////////
// Algorithm : None
//////////////////////////////////////////////////////////////////////
// Revisions : 
//   Date-initials : Description_of_revision
//////////////////////////////////////////////////////////////////////
	/*******************************************************
	*
	*                       NOTICE
	*
	*  THIS SOFTWARE IS THE PROPERTY OF AND CONTAINS
	*  CONFIDENTIAL INFORMATION OF SSA GLOBAL 
	*  TECHNOLOGIES, INC., AND SHALL NOT BE COPIED,
	*  USED, NOR DISCLOSED WITHOUT EXPRESS WRITTEN
	*  AUTHORIZATION.  ALTHOUGH PUBLICATION IS NOT
	*  INTENDED, IN THE EVENT OF PUBLICATION, THE
	*  FOLLOWING NOTICE IS APPLICABLE:
	*
	*  (c) COPYRIGHT 2004 SSA GLOBAL TECHNOLOGIES, INC.
	*
	*           SSA GLOBAL TECHNOLOGIES, INC.
	*
	********************************************************/
//////////////////////////////////////////////////////////////////////
void CUtilityHelper::BuildArrayofStrings(CSsaStringArray &tempArray, CString & buf) 
{
	CString tempSegment;
	int lastOffset = 0;
	int thisOffset = 0;
	int length = buf.GetLength();

	while ( thisOffset < length ) {
		if ( buf.GetAt(thisOffset) == '\n' ) {
			tempSegment = buf.Mid(lastOffset,(thisOffset-lastOffset)) ;
			tempArray.Add(tempSegment.GetBuffer(0));
			tempSegment.ReleaseBuffer();
			lastOffset = thisOffset + 1;
		}
		thisOffset++;
	}

	return;
}

CString CUtilityHelper::BuildStringBuf(CSsaStringArray & bufArray) 
{
	CString * tempString;
	int i;
	
	tempString = new CString();
	for ( i = 0; i < bufArray.GetSize(); i++ ) {
		(*tempString) += bufArray[i];
	}

	return (*tempString);
}

int CUtilityHelper::CompareStrings(const void **p1, const void **p2) 
{
	
	char *c1 = (char *)*p1;
	char *c2 = (char *)*p2;

	//ads_printf("%s - %s\n", c1, c2);

	return stricmp(c1, c2);

}


BOOL CUtilityHelper::IsFloat(CString str)
{
	double testDouble;
	char *testString, *endString;

	testString = str.GetBuffer(0);

	testDouble = strtod(testString, &endString);

	if (endString == NULL || endString == testString + strlen(testString)) {
		str.ReleaseBuffer();
		return TRUE;
	}
	else {
		str.ReleaseBuffer();
		return FALSE;
	}

}

BOOL CUtilityHelper::IsInteger(CString str)
{
	long testLong;
	char *testString, *endString;

	testString = str.GetBuffer(0);

	testLong = strtol(testString, &endString, 10);

	if (endString == NULL || endString == testString + strlen(testString)) {
		str.ReleaseBuffer();
		return TRUE;
	}
	else {
		str.ReleaseBuffer();
		return FALSE;
	}
}

BOOL CUtilityHelper::IsNumeric(CString str)
{
	if (! IsInteger(str) && ! IsFloat(str))
		return FALSE;
	else
		return TRUE;
}



int CUtilityHelper::ParseString(const CString &string, const CString &delimiter, CStringArray &strings)
{
	int idx;
	CString temp = string;

	strings.RemoveAll();

	idx = temp.Find(delimiter);
	if (idx < 0) {
		strings.Add(temp);
		return 1;
	}

	while (idx >= 0) {
		strings.Add(temp.Left(idx));
		temp = temp.Mid(idx+delimiter.GetLength());
		idx = temp.Find(delimiter);
	}

	if (temp.GetLength() > 0)
		strings.Add(temp);

	return strings.GetSize();

}

void CUtilityHelper::BuildDelimitedString(const CStringArray &fields, CString &string, const CString &delimiter, 
						  int numFields)
{
	string = "";
	if (numFields == 0)
		numFields = fields.GetSize();
	else if (numFields > fields.GetSize())
		numFields = fields.GetSize();

	for (int i=0; i < numFields; ++i) {
		string += fields[i];
		string += delimiter;
	}

	return;
}

CString CUtilityHelper::ParseField(CString field, int fieldNumber, CString delimiter)
{
	CString temp;
	int idx;

	if (fieldNumber <= 0)
		return "";

	temp = field;
	for (int i=1; i < fieldNumber; ++i) {
		idx = temp.Find(delimiter);
		if (idx < 0) {
			return "";
		}
		// found the delimiter
		temp = temp.Right(temp.GetLength()-(idx+1));
	}

	idx = temp.Find(delimiter);
	if (idx < 0)
		return temp;
	else
		return temp.Left(idx);
}

BOOL CUtilityHelper::PeekAndPump(int sleepTime)
{
	MSG msg;
	while (::PeekMessage(&msg, NULL, 0, 0, PM_NOREMOVE)) {
		//fprintf(f, "In PeekAndPump loop\n");
		//fflush(f);
		if (!AfxGetApp()->PumpMessage()) {
			//fprintf(f, "Posting quit message\n");
			//fflush(f);
			::PostQuitMessage(0);
			return FALSE;
		}
	}
	
	LONG lIdle = 0;
	while (AfxGetApp()->OnIdle(lIdle++)) {
		//fprintf(f, "In OnIdle loop\n");
		//fflush(f);
	}

	//fprintf(f, "After OnIdle Loop\n");
	//fflush(f);

	Sleep(sleepTime);

	return TRUE;

}




void CUtilityHelper::ProcessError(CString displayMsg, void *exception)
{
	char eMsg[1024];
	Ssa_Exception *e;

	if (exception != NULL) {
		// For now assume it's one of our exceptions
		e = (Ssa_Exception *)exception;
		e->GetMessage(eMsg);
		ads_printf("%s\n", eMsg);
	}
	else
		ads_printf("%s\n", "Unknown error message.");

	AfxMessageBox(displayMsg);

	return;

}
	
void CUtilityHelper::ShowLastError()
{
	LPVOID lpMsgBuf;
	FormatMessage( 
		FORMAT_MESSAGE_ALLOCATE_BUFFER | 
		FORMAT_MESSAGE_FROM_SYSTEM | 
		FORMAT_MESSAGE_IGNORE_INSERTS,
		NULL,
		GetLastError(),
		MAKELANGID(LANG_NEUTRAL, SUBLANG_DEFAULT), // Default language
		(LPTSTR) &lpMsgBuf,
		0,
		NULL 
		);
	// Process any inserts in lpMsgBuf.
	// ...
	// Display the string.
	MessageBox( NULL, (LPCTSTR)lpMsgBuf, "Error", MB_OK | MB_ICONINFORMATION );
	// Free the buffer.
	LocalFree( lpMsgBuf );
	
}

void CUtilityHelper::SortStringArray(CSsaStringArray &array)
{

	char **pointers;
	int i, maxlen, size;

	size = array.GetSize();

	pointers = (char **)malloc(size * sizeof(char *));

	maxlen = 0;
	for (i=0; i < size; ++i) {
		if (maxlen < array[i].GetLength())
			maxlen = array[i].GetLength();
	}

	maxlen++;

	for (i=0; i < size; ++i) {
		pointers[i] = (char *)malloc(maxlen * sizeof(char));
		strcpy(pointers[i], array[i].GetBuffer(0));
		array[i].ReleaseBuffer();
	}

	qsort(pointers, size, sizeof(char *),  (int (*)(const void *, const void *))CUtilityHelper::CompareStrings);

	array.RemoveAll();
	
	for (i=0; i < size; ++i) {
		array.Add(pointers[i]);
		free(pointers[i]);
	}

	free(pointers);

	return;
	
}

void CUtilityHelper::DumpArray(CStringArray &array)
{
	char result[132];
	FILE *f;
	int i;
	if (ads_getstring(1, "Enter path name: ", result) == RTNORM) {
		if (strlen(result) == 0)
			return;
		f = fopen(result, "w");
		if (f == NULL) {
			ads_printf("Error creating file\n");
			return;
		}

		for (i=0; i < array.GetSize(); ++i) {
			fprintf(f, "%s\n", array[i]);
		}

		fclose(f);

		ads_printf("Dump is complete.\n");
		ads_printf("Command: ");
	}

	return;


}


int CUtilityHelper::FindStringInArray(const CString &string, CStringArray &array)
{
	for (int i = 0; i < array.GetSize(); i++) {
		if (string == array[i])
			return i;
	}

	return -1;
}

CString CUtilityHelper::Soundex(const CString &string)
{
	CString soundex, stringCopy;
	char c;
	int idx;
	int maxLen = 5;

	stringCopy = string;
	stringCopy.MakeLower();

	soundex = stringCopy.GetAt(0);
	idx = 0;
	
	while (soundex.GetLength() < maxLen && idx < stringCopy.GetLength()) {
		idx++;
		switch (stringCopy.GetAt(idx)) {
			case 'b':
			case 'f':
			case 'p':
			case 'v':
				c = '1';
				break;
			case 'c':
			case 'g':
			case 'j':
			case 'k': 
			case 'q':
			case 's':
			case 'x':
			case 'z':
				c = '2';
				break;
			case 'd':
			case 't':
				c = '3';
				break;
			case 'l':
				c = '4';
				break;
			case 'm':
			case 'n':
				c = '5';
				break;
			case 'r':
				c = '6';
				break;
			default:
				continue;
		}
		if (c == soundex.GetAt(soundex.GetLength()-1))
			continue;
		
		soundex += c;
	}
	
	while (soundex.GetLength() < maxLen)
		soundex += '0';

	return soundex;

}

CString CUtilityHelper::GetDataTypeAsText(int dataType)
{
	CString typeAsText;

	switch (dataType) {
	case DT_INT:
		typeAsText = "Integer";
		break;
	case DT_FLOAT:
		typeAsText = "Float";
		break;
	case DT_STRING:
		typeAsText = "Text";
		break;
	case DT_LIST:
		typeAsText = "List";
		break;
	default:
		typeAsText = "Unknown";
		break;
	}

	return typeAsText;

}

CString CUtilityHelper::GetElementTypeAsText(int elementType)
{
	CString text;

	switch (elementType) {
	case UDF_PRODUCT:
		text = "Product";
		break;
	case UDF_PRODUCT_GROUP:
		text = "Product Group";
		break;
	case UDF_FACILITY:
		text = "Facility";
		break;
	case UDF_SECTION:
		text = "Section";
		break;
	case UDF_AISLE:
		text = "Aisle";
		break;
	case UDF_SIDE:
		text = "Side";
		break;
	case UDF_BAY:
		text = "Bay";
		break;
	case UDF_LEVEL:
		text = "Level";
		break;
	case UDF_LOCATION:
		text = "Location";
		break;
	case UDF_LEVEL_PROFILE:
		text = "Bay Profile";
		break;
	default:
		text = "Unknown";
		break;
	}

	return text;
}

void CUtilityHelper::CleanFacility()
{
	CCleanFacilityDialog *pDlg;

	//pDlg.DoModal();
	
	HWND hWnd = adsw_acadMainWnd(); 
	CWnd *parent;
	parent = CWnd::FromHandle(hWnd);
	
	
	try {
		pDlg = new CCleanFacilityDialog(parent);	
		if (pDlg->Create(IDD_CLEAN_FACILITY)) {
			pDlg->UpdateData(FALSE);
			pDlg->CenterWindow();
			pDlg->ShowWindow(SW_SHOW);
		}
	}
	catch (...) {
		AfxMessageBox("Error displaying clean facility dialog.");
	}
	

}

void CUtilityHelper::SortStringArray (CStringArray &array)
{
	qsort(array.GetData(), array.GetSize(), sizeof(CString), (int (*)(const void *, const void *))CUtilityHelper::CompareCStrings);
}

int CUtilityHelper::CompareCStrings(const void *p1, const void *p2) 
{
	
	CString *c1 = (CString *)p1;
	CString *c2 = (CString *)p2;


	//ads_printf("%s - %s\n", c1, c2);

	return c1->Compare(*c2);

}

BOOL CUtilityHelper::GetShiftKeyState()
{
	SHORT state;
	
	state = GetKeyState(VK_SHIFT);

	return (state < 0);
}

int CUtilityHelper::SkipMessageBox(const CString &message, const CString &caption, 
								   int options, int defaultOption, const CString &messageCode)
{
	CCheckMessageDialog dlg;

	dlg.m_Options = options;
	dlg.m_Caption = caption;
	dlg.m_Message = message;
	dlg.m_MessageCode = messageCode;
	dlg.m_DefaultOption = defaultOption;

	return dlg.DoModal();

}

BOOL CUtilityHelper::SetEditControlErrorState(CWnd *pDlg, int controlId)
{
	CEdit *pEdit = (CEdit *)pDlg->GetDlgItem(controlId);
	pEdit->SetSel(0,-1);
	pEdit->SetFocus();

	return FALSE;
}

CWnd* CUtilityHelper::GetParentWindow()
{
	HWND hWnd = adsw_acadMainWnd(); 
	CWnd *parent;
	parent = CWnd::FromHandle(hWnd);

	return parent;

}

double CUtilityHelper::ConvertRadiansToDegrees(double radians)
{
	double degrees = radians * 180/PI;

	return degrees;
}

double CUtilityHelper::ConvertDegreesToRadians(double degrees)
{
	double radians = PI/180 * degrees;
	
	return radians;
}


CString CUtilityHelper::AddXML(const CString &tag, const CString &value)
{
	CString temp;

	temp.Format("<%s>%s</%s>\n", tag, value, tag);

	return temp;
	
}

CString CUtilityHelper::AddXML(const CString &tag, int value)
{
	CString temp;

	temp.Format("<%s>%d</%s>\n", tag, value, tag);

	return temp;
	
}

CString CUtilityHelper::AddXML(const CString &tag, double value, int precision)
{
	CString temp;
	CString fmt;

	fmt.Format("<%%s>%%.0%df</%%s>\n", precision);

	temp.Format(fmt, tag, value, tag);

	return temp;
	
}

CString CUtilityHelper::GetUTCDate()
{
	CString dateStr;
	CTime tm = CTime::GetCurrentTime();
	dateStr = tm.FormatGmt("%Y-%m-%dT%H:%M:%S");

	return dateStr;
}

CString CUtilityHelper::GetUTCFileDate()
{
	CString dateStr;
	CTime tm = CTime::GetCurrentTime();
	dateStr = tm.FormatGmt("%Y-%m-%d_%H%M%S");

	return dateStr;
}

int CUtilityHelper::ConvertXData()
{
	CString sql;
	CStringArray results, stmts;

	sql.Format("select acadhandle, a.dbaisleid "
		"from dbbay b, dbside si, dbaisle a, dbsection se "
		"where b.dbsideid = si.dbsideid "
		"and si.dbaisleid = a.dbaisleid "
		"and a.dbsectionid = se.dbsectionid "
		"and se.dbfacilityid = %d "
		"and b.dbbayid = ( select max(dbbayid) "
		"from dbbay b2, dbside si2 "
		"where b2.dbsideid = si2.dbsideid "
		"and si2.dbaisleid = a.dbaisleid)", controlService.GetCurrentFacilityDBId());

	try {
		dataAccessService.ExecuteQuery("GetBayHandlesByAisle", sql, results, TRUE);
	}
	catch (...) {
		controlService.Log("Error converting facility to new format.", "Error in GetBayHandlesByAisle.\n");
		return -1;
	}

	for (int i=0; i < results.GetSize(); ++i) {
		CString handle = results[i].Left(results[i].Find("|"));
		CString aisleId = results[i].Mid(results[i].Find("|")+1);

		CString xData = CAutoCADCommands::GetXDataForObject(handle);
		if (xData == "")
			continue;
		double bayDepth, bayHeight, baySpace;
		istrstream s(xData.GetBuffer(xData.GetLength()+1));
		xData.ReleaseBuffer();
		s >> bayDepth >> bayHeight >> baySpace;
		
		if (baySpace > 0) {
			sql.Format("Update dbaisle set aislespace = %f "
				"where dbaisleid = %s", baySpace, aisleId);

			stmts.Add(sql);
		}
	}

	if (stmts.GetSize() > 0) {	

		try {
			dataAccessService.ExecuteStatements("UpdateAisleSpace", stmts);
		}
		catch (...) {
			controlService.Log("Error converting facility to new format.", "Error in UpdateAisleSpace.\n");
			return -1;
		}

	}


	/*
	// Delete all xdata
	CStringArray handles;

	CAutoCADCommands::GetAllDrawingBays(handles);

	for (i=0; i < handles.GetSize(); ++i) {
		CString handle = handles[i].Left(handles[i].Find("|"));
		CAutoCADCommands::DeleteXData(handle);
	}
	*/

	return 0;

}

int CUtilityHelper::GetNextEventId(int totalCount)
{
	CString sql;
	int eventId;
	
	eventId = dataAccessService.GetNextKey("DBEventMonitor", 1);

	sql.Format("insert into dbeventmonitor values (%d, %d, %d)",
		eventId, totalCount, 0);
	try {
		dataAccessService.ExecuteStatement("InsertEventMonitor", sql);
	}
	catch (...) {
		return -1;
	}

	return eventId;

}

int CUtilityHelper::GetNextEventPosition(int eventId)
{
	CString sql;
	CStringArray results;
#if 0
	CString tempString;
	int tempInt;
	CSsaStringArray tempSendArray;
	CSsaStringArray tempRecvArray;
	CString sendString;
	int i = 0;

	/////////////////////////////////////////////////////////////
	// build the data to send to the Forte connection server
	////////////////////////////////////////////////////////////	

	sendString = "<SIO>SLOTSocketString\n";
	tempSendArray.Add(sendString);
	sendString.Format("<SAI>%d\n", eventId);
	tempSendArray.Add(sendString);
	sendString = "<EIO>SLOTSocketString\n";
	tempSendArray.Add(sendString);
	
	forteService.SendToForteConnection(tempSendArray,tempRecvArray,
		CString("SLOTSocketString"), 10700);

	for (i=0; i < tempRecvArray.GetSize(); i++) {
		if ( tempRecvArray[i].Find("<SAI>") != -1 ) {
			tempString = tempRecvArray[i].Mid(5);
			tempInt = tempString.GetLength();
			if (tempString.GetAt(tempInt-1) == '\n' ) {
				tempString.SetAt(tempInt-1,' ');
				tempString.TrimRight();
			}
			results.Add(tempString);			
		}
	}
	if (results.GetSize() > 0)
		return atoi(results[0]);
	return -1;
#else
	return getSessionMgrSO()->GetEventPositionHelper(eventId);
#endif
}
void CUtilityHelper::ConvertDrawing()
{


//	AfxMessageBox("This command is only valid in AutoCAD 2000i or later.");
//	return;

	CFacilityHelper facilityHelper;
	if (facilityHelper.FacilityIsModified()) {
		AfxMessageBox("Please save the facility before running this command.");
		return;
	}

	if (AfxMessageBox("This process will delete and recreate each bay in the facility.\n"
		"It is recommended that you make a backup of a facility before running this process.\n"
		"It is also recommended that you run Check Facility and resolve any discrepancies prior\n"
		"to running this process.\n"
		"Do you wish to continue?", MB_YESNO) != IDYES)
		return;

	CProcessingMessage process("Reading bays from database...", GetParentWindow());

	CAutoCADCommands::ColorAllObjects(kRed);

	CString sql;
	sql.Format("select acadhandle, dbbayprofileid, isrotated from dbaisle, dbbayf, dbside "
		"where dbfacilityid = %d "
		"and dbside.dbsideid = dbbayf.dbsideid "
		"and dbaisle.dbaisleid = dbside.dbaisleid "
		"order by dbaisle.xcoordinate, dbbayf.dbsideid, dbbayf.xcoordinate",
		controlService.GetCurrentFacilityDBId());
	CStringArray results;
	dataAccessService.ExecuteQuery("GetHandles", sql, results, TRUE);

	Acad::ErrorStatus es;
	AcDbObjectId refObjectId, blockObjectId;
	AcDbBlockReference *pBlkRef;
	AcDbBlockTableRecord *pBlkTableRecord;
	AcGePoint3d position;
	AcDbHandle acdbHandle;
	AcDbObjectId layerId;
//	AcDbLayerTable *pLayerTable;
	AcDbLayerTableRecord *pLayer;
	AcDbEntity *pEntity;
	AcDbObject *pObject;
	double prevUprightWidth = -1;
	int l;

//	double rotation;		// Fix by Manohar, unreferenced local variable
	CElementMaintenanceHelper helper;
	C3DPoint point;
	CMap<int, int, CBayProfile*, CBayProfile*> map;
	CBayProfile *pBayProfile;
	CBayProfileDataService bayProfileDataService;
	CStringArray stmts;

	process.Hide();
	CProgressMessage progress("Converting bays...", 0, results.GetSize()-1, 1, GetParentWindow(), TRUE);
	AcDbObjectIdArray blockIds, layerIds;

/*	for (int i=0; i < results.GetSize(); ++i) {
		progress.Step();

		if (i % 20 == 0)
			PeekAndPump(100);

		CStringArray strings;
		ParseString(results[i], "|", strings);

		CString handle = strings[0];
		int bayProfileId = atoi(strings[1]);
		int isRotated = atoi(strings[2]);
	
		// get the object id from the handle
		acdbHandle = handle;
		es = acdbCurDwg()->getAcDbObjectId(refObjectId, FALSE, acdbHandle);
		if (es != Acad::eOk) {
			ads_printf("Warning - could not find object: %s\n", handle);
			continue;
		}

		// open the block reference for the object id
		es = acdbOpenAcDbEntity(pEntity, refObjectId, AcDb::kForWrite);
		if (es != Acad::eOk) {
			ads_printf("Warning - could not open object: %s\n", handle);
			continue;
		}
		pBlkRef = AcDbBlockReference::cast(pEntity);

		// get the block table record for the reference
		blockObjectId = pBlkRef->blockTableRecord();

		// open the block record
		es = acdbOpenAcDbObject(pObject, blockObjectId, AcDb::kForWrite);
		if (es != Acad::eOk) {
			ads_printf("Warning - could not open block: %s\n", handle);
			pBlkRef->close();
			continue;
		}

		pBlkTableRecord = AcDbBlockTableRecord::cast(pObject);

		position = pBlkRef->position();
		rotation = pBlkRef->rotation();
		
		point.m_X = position[X];
		point.m_Y = position[Y];
		point.m_Z = position[Z];
		
		
		AcDbObject *pLayerObject;
		AcDbObjectId layerId = pBlkRef->layerId();
		es = acdbOpenAcDbObject(pLayerObject, layerId, AcDb::kForWrite);
		if (es != Acad::eOk) {
			ads_printf("Could not open layer.\n");
			continue;
		}
		pLayer = AcDbLayerTableRecord::cast(pLayerObject);
		
		char *layerName;
		pLayer->getName(layerName);
		pLayerObject->close();
		layerIds.append(layerId);

		pBlkRef->erase();
		if (es != Acad::eOk) {
			ads_printf("Error erasing block reference: %s\n", handle);
			pBlkRef->close();
			pBlkTableRecord->close();
			continue;
		}

		pBlkRef->close();

	
#ifdef _AUTOCAD2000

		AcDbObjectIdArray ids;
		if (ids.length() > 0)
			ids.removeSubArray(0, ids.length()-1);

		pBlkTableRecord->getBlockReferenceIds(ids, true, false);
		l = ids.length();
		
#else
	
		l = 1;


#endif
	
		if (l > 0) {
			ads_printf("Block can not be deleted (yet): %s\n", handle);	
		}
		else {
			es = pBlkTableRecord->erase();
			if (es != Acad::eOk) {
				ads_printf("Error erasing block: %s\n", handle);
			}
		}


		pBlkTableRecord->close();
		

		if (! map.Lookup(bayProfileId, pBayProfile)) {
			pBayProfile = new CBayProfile;
			bayProfileDataService.GetBayProfile(bayProfileId, *pBayProfile, CBayProfile::loadLevels|CBayProfile::loadLocations);
			map.SetAt(bayProfileId, pBayProfile);
		}

		CString newHandle;
		rotation = ConvertRadiansToDegrees(rotation);


		helper.AddBayObject(*pBayProfile, rotation, point, newHandle,
			prevUprightWidth > pBayProfile->m_UprightWidth ? prevUprightWidth : pBayProfile->m_UprightWidth, 
			pBayProfile->m_UprightWidth);
		

		prevUprightWidth = pBayProfile->m_UprightWidth;

		sql.Format("update dbbayf set acadhandle = '%s' where acadhandle = '%s' and dbfacilityid = %d",
			newHandle, handle, controlService.GetCurrentFacilityDBId());
		stmts.Add(sql);
		

	}*/

	vector<ref_struct> refs;

	ref_struct oneRef;

	// cycle through results storing data about each block and each reference (bay)

	for (int i=0; i < results.GetSize(); ++i) {
		progress.Step();

		if (i % 20 == 0)
			PeekAndPump(100);

		CStringArray strings;
		ParseString(results[i], "|", strings);

		CString handle = strings[0];
		int bayProfileId = atoi(strings[1]);
		int isRotated = atoi(strings[2]);
	
		// get the object id from the handle
		acdbHandle = handle;

		// populate our oneRef structure

		oneRef.handle = handle;

		acdbHandle = handle;
		es = acdbCurDwg()->getAcDbObjectId(refObjectId, FALSE, acdbHandle);
		if (es != Acad::eOk) {
			ads_printf("Warning - could not find object: %s\n", handle);
			continue;
		}

		// open the block reference for the object id
		es = acdbOpenAcDbEntity(pEntity, refObjectId, AcDb::kForWrite);
		if (es != Acad::eOk) {
			ads_printf("Warning - could not open object: %s\n", handle);
			continue;
		}

		pBlkRef = AcDbBlockReference::cast(pEntity);

		position = pBlkRef->position();
		pBlkRef->close();
		point.m_X = position[X];
		point.m_Y = position[Y];
		point.m_Z = position[Z];
		oneRef.point  = point;
		oneRef.rotation = ConvertRadiansToDegrees(pBlkRef->rotation());

		// gets pointer to bay profile
		if (! map.Lookup(bayProfileId, pBayProfile)) {
			pBayProfile = new CBayProfile;
			bayProfileDataService.GetBayProfile(bayProfileId, *pBayProfile, CBayProfile::loadLevels|CBayProfile::loadLocations);
			map.SetAt(bayProfileId, pBayProfile);
		}

		oneRef.bayprofile = pBayProfile;
		oneRef.uprightWidth = pBayProfile->m_UprightWidth;

		refs.push_back(oneRef);

	}

	// process all our references and remove the blocks for those that are empty

	for (unsigned int j = 0; j < refs.size(); ++j) {
		// open the block reference for the object id
		// get the object id from the handle
		acdbHandle = refs[j].handle;
		es = acdbCurDwg()->getAcDbObjectId(refObjectId, FALSE, acdbHandle);
		if (es != Acad::eOk) {
			ads_printf("Warning - could not find object: %s\n", refs[j].handle);
			continue;
		}

		// open the block reference for the object id
		es = acdbOpenAcDbEntity(pEntity, refObjectId, AcDb::kForWrite);
		if (es != Acad::eOk) {
			ads_printf("Warning - could not open object: %s\n", refs[j].handle);
			continue;
		}
		pBlkRef = AcDbBlockReference::cast(pEntity);

		// get the block table record for the reference
		blockObjectId = pBlkRef->blockTableRecord();

		// open the block record
		es = acdbOpenAcDbObject(pObject, blockObjectId, AcDb::kForWrite);
		if (es != Acad::eOk) {
			ads_printf("Warning - could not open block: %s\n", refs[j].handle);
			pBlkRef->close();
			continue;
		}

		pBlkTableRecord = AcDbBlockTableRecord::cast(pObject);
		
		
		AcDbObject *pLayerObject;
		AcDbObjectId layerId = pBlkRef->layerId();
		es = acdbOpenAcDbObject(pLayerObject, layerId, AcDb::kForWrite);
		if (es != Acad::eOk) {
			ads_printf("Could not open layer.\n");
			continue;
		}
		pLayer = AcDbLayerTableRecord::cast(pLayerObject);
		
		char *layerName;
		pLayer->getName(layerName);
		pLayerObject->close();
		layerIds.append(layerId);

		pBlkRef->erase();
		if (es != Acad::eOk) {
			ads_printf("Error erasing block reference: %s\n", refs[i].handle);
			pBlkRef->close();
			pBlkTableRecord->close();
			continue;
		}

		pBlkRef->close();

		AcDbObjectIdArray ids;

		pBlkTableRecord->getBlockReferenceIds(ids, true, false);
		l = ids.length();
	
		if (l == 0) {
			es = pBlkTableRecord->erase();
			if (es != Acad::eOk) {
				ads_printf("Error erasing block: %s\n", refs[i].handle);
			}
		}
		pBlkTableRecord->close();
		
	}

	// cycle through our vector of bays and add them
	CString newHandle;

	for (j = 0; j < refs.size(); j++) {
		oneRef = refs[j];
		pBayProfile = oneRef.bayprofile;
		//helper.AddBayObject(*pBayProfile, refs[j].rotation, refs[j].point, newHandle, refs[j].uprightWidth, refs[j].uprightWidth);

		helper.AddBayObject(*pBayProfile, refs[j].rotation, refs[j].point, newHandle,
			prevUprightWidth > pBayProfile->m_UprightWidth ? prevUprightWidth : pBayProfile->m_UprightWidth, 
			pBayProfile->m_UprightWidth);
		

		prevUprightWidth = pBayProfile->m_UprightWidth;

		sql.Format("update dbbayf set acadhandle = '%s' where acadhandle = '%s' and dbfacilityid = %d",
			newHandle, refs[j].handle, controlService.GetCurrentFacilityDBId());
		stmts.Add(sql);
	}

	l = layerIds.length();
	acdbCurDwg()->purge(layerIds);
	if (l != layerIds.length()) {
		ads_printf("%d layers could not be purged.\n", l-layerIds.length());
	}
	for (i=0; i < layerIds.length(); ++i) {
		es = acdbOpenAcDbObject(pObject, layerIds[i], AcDb::kForWrite);
		if (es != Acad::eOk) {
			ads_printf("Could not open layer.\n");
			continue;
		}
		
		pLayer = AcDbLayerTableRecord::cast(pObject);

		es = pLayer->erase();
		if (es != Acad::eOk) {
			ads_printf("Could not erase layer.\n");
		}
		pLayer->close();
	}

	progress.Hide();
	PeekAndPump(500);
	
	process.Show();
	process.UpdateMessage("Updating Database");

	if (stmts.GetSize() > 0) {
		try {
			dataAccessService.ExecuteStatements("UpdateHandles", stmts);
		}
		catch (...) {
			AfxMessageBox("The database was not updated.  Close the facility without saving\n"
				"and reopen it to try again.");
			return;
		}
	}


#ifdef _AUTOCAD2000
	ads_command(RTSTR, "-purge", RTSTR, "a", RTSTR, "", RTSTR, "n", RTNONE);
#else
	ads_command(RTSTR, "purge", RTSTR, "a", RTSTR, "*", RTSTR, "n", RTNONE);
#endif

	ads_command(RTSTR, "audit", RTSTR, "y", RTNONE);
	ads_command(RTSTR, ".qsave", RTNONE);

	AfxMessageBox("You must save the facility before using it.");

	POSITION pos = map.GetStartPosition();
	while (pos != NULL) {
		int dummy;
		map.GetNextAssoc(pos, dummy, pBayProfile);
		delete pBayProfile;
	}

}

void CUtilityHelper::ColorDisconnectedPickpaths()
{
	CString sql;

	sql.Format("select pp.acadhandle, pp.conacadhand "
		"from dbpickpath pp, dbaislepath ap, dbaisle a, dbsection se "
		"where se.dbfacilityid = %d "
		"and se.dbsectionid = a.dbsectionid "
		"and a.dbaisleid = ap.dbaisleid "
		"and ap.dbpickpathid = pp.dbpickpathid ", controlService.GetCurrentFacilityDBId());

	CStringArray results;
	try {
		dataAccessService.ExecuteQuery("GetDisconnectedPickpaths", sql, results, TRUE);
	}
	catch (...) {
		controlService.Log("Error getting disconnected pickpaths.", "");
		return;
	}

	for (int i=0; i < results.GetSize(); ++i) {
		CString handle = results[i].Left(results[i].Find("|"));
		CString conHandle = results[i].Mid(results[i].Find("|")+1);

		if (conHandle == "XXX")
			CAutoCADCommands::ColorDrawingObjectByHandle(handle, kRed);
		else
			CAutoCADCommands::ColorDrawingObjectByHandle(handle, kGreen);
	}
	

}

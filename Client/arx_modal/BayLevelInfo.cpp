// BayLevelInfo.cpp: implementation of the CBayLevelInfo class.
//
//////////////////////////////////////////////////////////////////////

#include "stdafx.h"
#include "modal.h"
#include "BayLevelInfo.h"

#ifdef _DEBUG
#undef THIS_FILE
static char THIS_FILE[]=__FILE__;
#define new DEBUG_NEW
#endif

//////////////////////////////////////////////////////////////////////
// Construction/Destruction
//////////////////////////////////////////////////////////////////////

CBayLevelInfo::CBayLevelInfo()
{

}

CBayLevelInfo::~CBayLevelInfo()
{
	for (int i=0; i < m_LocationList.GetSize(); ++i)
		delete m_LocationList[i];
}

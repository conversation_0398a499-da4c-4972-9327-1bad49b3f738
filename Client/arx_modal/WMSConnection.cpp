// WMSConnection.cpp: implementation of the CWMSConnection class.
//
//////////////////////////////////////////////////////////////////////

#include "stdafx.h"
#include "modal.h"
#include "WMSConnection.h"
#include "UtilityHelper.h"

#ifdef _DEBUG
#undef THIS_FILE
static char THIS_FILE[]=__FILE__;
#define new DEBUG_NEW
#endif

//////////////////////////////////////////////////////////////////////
// Construction/Destruction
//////////////////////////////////////////////////////////////////////

extern CUtilityHelper utilityHelper;

CWMSConnection::CWMSConnection()
{
	m_WMSConnectionDBId = 0;
	m_Port = m_Channel = 0;
}

CWMSConnection::~CWMSConnection()
{

}

CWMSConnection::CWMSConnection(const CWMSConnection& other)
{
	m_WMSConnectionDBId = other.m_WMSConnectionDBId;
	m_Channel = other.m_Channel;
	m_ConnectionType = other.m_ConnectionType;
	m_FileName = other.m_FileName;
	m_Host = other.m_Host;
	m_Login = other.m_Login;
	m_Password = other.m_Password;
	m_Path = other.m_Path;
	m_Port = other.m_Port;
	m_Queue = other.m_Queue;
	m_QueueManager = other.m_QueueManager;
	m_TriggerName = other.m_TriggerName;

}


CWMSConnection& CWMSConnection::operator=(const CWMSConnection& other)
{
	m_WMSConnectionDBId = other.m_WMSConnectionDBId;
	m_Channel = other.m_Channel;
	m_ConnectionType = other.m_ConnectionType;
	m_FileName = other.m_FileName;
	m_Host = other.m_Host;
	m_Login = other.m_Login;
	m_Password = other.m_Password;
	m_Path = other.m_Path;
	m_Port = other.m_Port;
	m_Queue = other.m_Queue;
	m_QueueManager = other.m_QueueManager;
	m_TriggerName = other.m_TriggerName;
	
	return *this;
}


BOOL CWMSConnection::operator==(const CWMSConnection& other)
{
	if (m_WMSConnectionDBId != other.m_WMSConnectionDBId) return FALSE;
	if (m_Channel != other.m_Channel) return FALSE;
	if (m_ConnectionType != other.m_ConnectionType) return FALSE;
	if (m_FileName != other.m_FileName) return FALSE;
	if (m_Host != other.m_Host) return FALSE;
	if (m_Login != other.m_Login) return FALSE;
	if (m_Password != other.m_Password) return FALSE;
	if (m_Path != other.m_Path) return FALSE;
	if (m_Port != other.m_Port) return FALSE;
	if (m_Queue != other.m_Queue) return FALSE;
	if (m_QueueManager != other.m_QueueManager) return FALSE;
	if (m_TriggerName != other.m_TriggerName) return FALSE;
	
	return TRUE;
}


int CWMSConnection::Parse(const CString& line)
{
	CStringArray strings;

	utilityHelper.ParseString(line, "|", strings);

	for (int i=0; i < strings.GetSize(); ++i) {
		if (strings[i] == " ")
			strings[i] = "";

		switch (i) {
		case 0:
			m_WMSConnectionDBId = atoi(strings[i]);
			break;
		case 1:
			m_ConnectionType = atoi(strings[i]);
			break;
		case 2:
			m_Host = strings[i];
			break;
		case 3:
			m_Port = atoi(strings[i]);
			break;
		case 4:
			m_QueueManager = strings[i];
			break;
		case 5:
			m_Login = strings[i];
			break;
		case 6:
			m_Password = strings[i];
			break;
		case 7:
			m_Path = strings[i];
			break;
		case 8:
			m_Queue = strings[i];
			m_FileName = strings[i];
			break;
		case 9:
			m_TriggerName = strings[i];
			break;
		case 10:
			m_Channel = atoi(strings[i]);
			break;
		}
	}

	return 0;
}

CString CWMSConnection::ConnectionTypeAsText()
{
	switch (m_ConnectionType) {
	case MQSeries:
		return "MQSeries";
		break;
	case FTP:
		return "Remote(FTP)";
		break;
	case Local:
		return "Local Disk";
		break;
	case Prompt:
		return "Manual (prompt for file)";
		break;
	}

	return "Uknown";
}

//{{AFX_INCLUDES()
//}}AFX_INCLUDES
#if !defined(AFX_BAYPROFILELABORPAGE_H__056386C0_30C1_4F17_AC66_67939E09A35A__INCLUDED_)
#define AFX_BAYPROFILELABORPAGE_H__056386C0_30C1_4F17_AC66_67939E09A35A__INCLUDED_

#if _MSC_VER > 1000
#pragma once
#endif // _MSC_VER > 1000
// BayProfileLaborPage.h : header file
//
#include "BayProfile.h"
#include "BayProfileLevelButton.h"
#include "DataGrid.h"
/////////////////////////////////////////////////////////////////////////////
// CBayProfileLaborPage dialog

class CBayProfileLaborPage : public CPropertyPage
{
	DECLARE_DYNCREATE(CBayProfileLaborPage)

// Construction
public:
	static int CALLBACK SortHandlingList(LPARAM lParam1, LPARAM lParam2, LPARAM column);
	CBayProfileLaborPage();
	~CBayProfileLaborPage();

// Dialog Data
	//{{AFX_DATA(CBayProfileLaborPage)
	enum { IDD = IDD_BAY_PROFILE_LABOR_ATTRIBUTES };
	CListCtrl	m_HandlingListCtrl;
	CComboBox	m_LevelListCtrl;
	CBayProfileLevelButton	m_LevelButton;
	CString	m_ForkExtractionTime;
	CString	m_ForkInsertionTime;
	//}}AFX_DATA


// Overrides
	// ClassWizard generate virtual function overrides
	//{{AFX_VIRTUAL(CBayProfileLaborPage)
	public:
	virtual BOOL OnSetActive();
	virtual BOOL OnKillActive();
	protected:
	virtual void DoDataExchange(CDataExchange* pDX);    // DDX/DDV support
	virtual BOOL OnNotify(WPARAM wParam, LPARAM lParam, LRESULT* pResult);
	//}}AFX_VIRTUAL

// Implementation
protected:
	// Generated message map functions
	//{{AFX_MSG(CBayProfileLaborPage)
	virtual BOOL OnInitDialog();
	afx_msg void OnSelchangeLevelList();
	afx_msg void OnAdd();
	afx_msg void OnModify();
	afx_msg void OnDelete();
	afx_msg void OnColumnclickHandlingList(NMHDR* pNMHDR, LRESULT* pResult);
	afx_msg BOOL OnHelpInfo(HELPINFO* pHelpInfo);
	//}}AFX_MSG
	DECLARE_MESSAGE_MAP()
	int OnSelectLevel(WPARAM wParam, LPARAM lParam);

private:
	BOOL Validate();
	int m_LastWorkType;
	int UpdateLevelProfileFromScreen(int currentLevel);
	int UpdateScreenFromLevelProfile(int currentLevel);
	void RebuildLevelList();
	CBayProfile *m_pBayProfile;
};

//{{AFX_INSERT_LOCATION}}
// Microsoft Visual C++ will insert additional declarations immediately before the previous line.

#endif // !defined(AFX_BAYPROFILELABORPAGE_H__056386C0_30C1_4F17_AC66_67939E09A35A__INCLUDED_)

// WMSMap.cpp: implementation of the CWMSMap class.
//
//////////////////////////////////////////////////////////////////////

#include "stdafx.h"
#include "modal.h"
#include "WMSMap.h"
#include "UtilityHelper.h"

#ifdef _DEBUG
#undef THIS_FILE
static char THIS_FILE[]=__FILE__;
#define new DEBUG_NEW
#endif


//////////////////////////////////////////////////////////////////////
// Construction/Destruction
//////////////////////////////////////////////////////////////////////

CWMSMap::CWMSMap()
{
	m_WMSMapDBId = 0;
	m_WMSDBId = 0;
	m_FacilityDBId = 0;
	m_SectionDBId = 0;
}

CWMSMap::CWMSMap(CWMSMap& other)
{
	m_WMSMapDBId = other.m_WMSMapDBId;
	m_WMSDBId = other.m_WMSDBId;
	m_WMSName = other.m_WMSName;
	m_FacilityDBId = other.m_FacilityDBId;
	m_FacilityName = other.m_FacilityName;
	m_SectionDBId = other.m_SectionDBId;
	m_SectionName = other.m_SectionName;

}

void CWMSMap::operator=(CWMSMap& other)
{
	m_WMSMapDBId = other.m_WMSMapDBId;
	m_WMSDBId = other.m_WMSDBId;
	m_WMSName = other.m_WMSName;
	m_FacilityDBId = other.m_FacilityDBId;
	m_FacilityName = other.m_FacilityName;
	m_SectionDBId = other.m_SectionDBId;
	m_SectionName = other.m_SectionName;
}

CWMSMap::~CWMSMap()
{

}

int CWMSMap::Parse(CString &line)
{
	CUtilityHelper utilityHelper;
	CStringArray strings;

	utilityHelper.ParseString(line, "|", strings);

	for (int i=0; i < strings.GetSize(); ++i) {
		switch (i) {
		case 0:
			m_WMSMapDBId = atol(strings[i]);
			break;
		case 1:
			m_WMSDBId = atol(strings[i]);
			break;
		case 2:
			m_SectionDBId = atol(strings[i]);
			break;
		case 3:
			m_FacilityDBId = atol(strings[i]);
			break;
		case 4:
			m_WMSName = strings[i];
			break;
		case 5:
			m_FacilityName = strings[i];
			break;
		case 6:
			m_SectionName = strings[i];
			break;
		case 7:
			m_Direction = atoi(strings[i]);
			break;
		}
	}

	return 0;

}

// ProductGroupDataService.h: interface for the CProductGroupDataService class.
//
//////////////////////////////////////////////////////////////////////

#if !defined(AFX_PRODUCTGROUPDATASERVICE_H__808B9994_0F20_11D5_9EC8_00C04FAC149C__INCLUDED_)
#define AFX_PRODUCTGROUPDATASERVICE_H__808B9994_0F20_11D5_9EC8_00C04FAC149C__INCLUDED_

#if _MSC_VER > 1000
#pragma once
#endif // _MSC_VER > 1000

#include "ProductAttribute.h"
#include "ProductGroup.h"
#include "ProductGroupCriteria.h"
#include "ProductGroupCriteriaRange.h"
#include "ProductGroupCriteriaValue.h"
#include "ProductGroupCriteriaQuery.h"
#include "ProductGroupLevel.h"
#include "OperatorService.h"
#include "qqhclasses.h"

typedef CTypedPtrArray<CObArray, CProductGroup*> ProductGroupArrayType;
typedef CTypedPtrArray<CObArray, CProductGroupCriteria*> CriteriaArrayType;
/*
typedef CTypedPtrArray<CObArray, CProductGroupCriteriaRange*> CriteriaRangeArrayType;
typedef CTypedPtrArray<CObArray, CProductGroupCriteriaValue*> CriteriaValueArrayType;
typedef CTypedPtrArray<CObArray, CProductGroupCriteriaQuery*> CriteriaQueryArrayType;
*/

class CProductGroupDataService : public CObject  
{
public:
	
	CProductGroupDataService();
	virtual ~CProductGroupDataService();

	int GetBayHandlesByProductInGroup(int groupId, CStringArray &handles);
	int UpdateUDFWithFormula(CString &formula, int udfDBID);
	int DeleteSlotGroupBays( CArray <int, int&> &bayList );
	int GetProductGroupAssignmentCount(CStringArray &results);
	int StoreProductGroupLevel(CTypedPtrArray<CObArray, CProductGroupLevel*> &list);
	BOOL ValidateFormula(CString &formula, CString &attributes);
	int GetBayHandlesByProductGroup(long productGroupDBID, CStringArray &handleList);
	int GetUniqueCriteriaValues(CString &formula, CStringArray &valueList);
	int GetUniqueCriteriaValues(CProductAttribute &attribute, CStringArray &valueListx);
	int GetCriteriaValueCount(CString &formula);
	int GetCriteriaValueCount(CProductAttribute &attribute);

	int CreateProductGroupAssignments(int facilityDBId, long productGroupDBID, 
		int priority, BOOL countOnly, BOOL deleteOnly);

	int LoadProductAttributes(CTypedPtrArray<CObArray, CProductAttribute*> &productAttributeList);
	int InitProductAttributes(int facilityDBId);
	int LoadProductGroups(ProductGroupArrayType &productGroupList);
	int LoadProductGroupCriteria(CriteriaArrayType &criteriaList);
	int LoadProductGroupQuery(CProductGroup &productGroup);
	int LoadAllProductGroupQueries(ProductGroupArrayType &productGroupList);
	int LoadCriteriaDetail(CProductGroupCriteria &criteria);
	int LoadAllCriteriaDetail(CriteriaArrayType &criteriaList);
	int LoadCriteriaValues(CProductGroupCriteria &criteria);
	int LoadProductGroupConstraints(CProductGroup &productGroup);
	int LoadAllProductGroupConstraints(ProductGroupArrayType &productGroupList);

	int GetProductInfoByProductGroup(long productGroupDBID, CStringArray &productList);
	int GetProductCountByProductGroup(long productGroupDBID);
	int GetProductGroupUnassignedCount();
	int StoreProductGroupQuery(CProductGroup &productGroup, CArray<int, int> &deleteList);

	int StoreProductGroup(CProductGroup &productGroup);
	int DeleteProductGroup(long productGroupDBID);
	int UpdateProductGroupPriority(long productGroupDBID, int priority);

	int GetProductGroups(int facilityDBId, CStringArray &productGroupList);

	int StoreCriteria(CProductGroupCriteria &criteria);
	int DeleteCriteria(long criteriaDBID);

	CTypedPtrArray<CObArray, CProductAttribute*> m_ProductAttributeList;
	CMapStringToOb m_ProductAttributeMap;
	COperatorService m_OperatorService;

private:
	void FormatSQL(CString &sql, const CString &oper, const CString &value);
	CString ParseFormula(CString &formula, CString &fromClause, CString &whereClause);
	void FormatSQL(CString &sql, CProductAttribute &attr, const CString &oper, const CString &value);
	int ParseQueryInfo(CString &queryInfo, CString &attribute, CString &oper, CString &value,
		int *precedence, CString &conjunction);

	int GetProductGroupQuery(long productGroupDBID, CStringArray &queryInfoList);

	int GetCriteriaListForCurrentFacility(CStringArray &criteriaList);
	int GetCriteriaRangeList(long criteriaDBID, CStringArray &rangeList);
	int GetCriteriaRangeListByFacility(CStringArray &rangeList);
	int GetUsedCriteriaRangeCount(long criteriaRangeDBID);
	int GetCriteriaValueList(long criteriaDBID, CStringArray &valueList);

	int GetProductGroupQueriesForProductGroup(long productGroupDBID, CStringArray &queryList);
	int GetProductGroupQueriesByFacility(CStringArray &queryList);

	int GetConstraintsByProductGroup(long productGroupDBID, CStringArray &constraintList);
	int GetConstraintsByFacility(CStringArray &constraintList);

	int GetCriteriaQueryListByFacility(CStringArray &queryList);
	CMap<CString, LPCSTR, int, int> m_UDFUsedMap;

};

#endif // !defined(AFX_PRODUCTGROUPDATASERVICE_H__808B9994_0F20_11D5_9EC8_00C04FAC149C__INCLUDED_)

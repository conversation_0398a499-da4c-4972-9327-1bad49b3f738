// ProductGroupCriteriaPropertiesPage.cpp : implementation file
//

#include "stdafx.h"
#include "ssa_exception.h"
#include "ProductGroupCriteriaPropertiesPage.h"
#include "ProductAttribute.h"
#include "Constants.h"
#include "HelpService.h"

#ifdef _DEBUG
#define new DEBUG_NEW
#undef THIS_FILE
static char THIS_FILE[] = __FILE__;
#endif

extern CHelpService helpService;

/////////////////////////////////////////////////////////////////////////////
// CProductGroupCriteriaPropertiesPage property page

IMPLEMENT_DYNCREATE(CProductGroupCriteriaPropertiesPage, CPropertyPage)

CProductGroupCriteriaPropertiesPage::CProductGroupCriteriaPropertiesPage() : CPropertyPage(CProductGroupCriteriaPropertiesPage::IDD)
{
	//{{AFX_DATA_INIT(CProductGroupCriteriaPropertiesPage)
	m_Attribute = _T("");
	m_Description = _T("");
	m_Name = _T("");
	m_Formula = _T("");
	m_ListCheckBox = FALSE;
	//}}AFX_DATA_INIT
}

CProductGroupCriteriaPropertiesPage::~CProductGroupCriteriaPropertiesPage()
{
}

void CProductGroupCriteriaPropertiesPage::DoDataExchange(CDataExchange* pDX)
{
	CPropertyPage::DoDataExchange(pDX);
	//{{AFX_DATA_MAP(CProductGroupCriteriaPropertiesPage)
	DDX_Control(pDX, IDC_FORMULA, m_FormulaCtrl);
	DDX_Control(pDX, IDC_FORMULA_CHECK, m_FormulaCheckCtrl);
	DDX_Control(pDX, IDC_DESCRIPTION, m_DescriptionCtrl);
	DDX_Control(pDX, IDC_NAME, m_NameCtrl);
	DDX_Control(pDX, IDC_ATTRIBUTE, m_AttributeCtrl);
	DDX_CBString(pDX, IDC_ATTRIBUTE, m_Attribute);
	DDX_Text(pDX, IDC_DESCRIPTION, m_Description);
	DDX_Text(pDX, IDC_NAME, m_Name);
	DDX_Text(pDX, IDC_FORMULA, m_Formula);
	DDX_Check(pDX, IDC_LIST, m_ListCheckBox);
	//}}AFX_DATA_MAP
}


BEGIN_MESSAGE_MAP(CProductGroupCriteriaPropertiesPage, CPropertyPage)
	//{{AFX_MSG_MAP(CProductGroupCriteriaPropertiesPage)
	ON_CBN_SELCHANGE(IDC_ATTRIBUTE, OnSelchangeAttribute)
	ON_CBN_EDITCHANGE(IDC_ATTRIBUTE, OnEditchangeAttribute)
	ON_BN_CLICKED(IDC_FORMULA_CHECK, OnFormulaCheck)
	ON_EN_CHANGE(IDC_FORMULA, OnChangeFormula)
	ON_BN_CLICKED(IDC_VALIDATE_FORMULA, OnValidateFormula)
	ON_WM_HELPINFO()
	ON_BN_CLICKED(IDC_LIST, OnList)
	//}}AFX_MSG_MAP
END_MESSAGE_MAP()

/////////////////////////////////////////////////////////////////////////////
// CProductGroupCriteriaPropertiesPage message handlers

BOOL CProductGroupCriteriaPropertiesPage::OnInitDialog() 
{

	int rc;
	CStringArray attributes;
	CProductAttribute *attribute;
	CRect r;
	CComboBox *pAttributeBox = (CComboBox *)GetDlgItem(IDC_ATTRIBUTE);
	CButton *pButton;

	CPropertyPage::OnInitDialog();
	
	m_AttributeCtrl.GetWindowRect(&r);
	m_AttributeCtrl.SetWindowPos(NULL, 0, 0, r.Width(), r.Height()*10, SWP_NOMOVE|SWP_NOZORDER);
	
	for (int i=0; i < m_ProductGroupDataService->m_ProductAttributeList.GetSize(); ++i) {
		attribute = m_ProductGroupDataService->m_ProductAttributeList[i];
		rc = m_AttributeCtrl.AddString(attribute->m_Name);
		m_AttributeCtrl.SetItemData(rc, i);
	}
	
	m_PreviousSelection = -1;

	if (m_Criteria != NULL) {
		m_Name = m_Criteria->m_Name;
		m_Description = m_Criteria->m_Description;
		if (m_Criteria->GetAttributeType() == DT_FORMULA) {
			m_AttributeCtrl.SetCurSel(-1);
			m_Formula = m_Criteria->GetAttribute();
			m_FormulaCheckCtrl.SetCheck(1);
			m_FormulaCtrl.ShowWindow(SW_SHOW);
			m_AttributeCtrl.ShowWindow(SW_HIDE);
			GetDlgItem(IDC_VALIDATE_FORMULA)->ShowWindow(SW_SHOW);
		}
		else {
			m_FormulaCheckCtrl.SetCheck(0);
			m_FormulaCtrl.ShowWindow(SW_HIDE);
			m_AttributeCtrl.ShowWindow(SW_SHOW);
			GetDlgItem(IDC_VALIDATE_FORMULA)->ShowWindow(SW_HIDE);
			m_AttributeCtrl.SetCurSel(pAttributeBox->FindString(0, m_Criteria->GetAttribute()));
			m_AttributeCtrl.GetWindowText(m_Attribute);
		}
		m_PreviousSelection = m_AttributeCtrl.GetCurSel();

		pButton = (CButton *)GetDlgItem(IDC_LIST);
		m_ListCheckBox = m_Criteria->m_IsDiscrete;
		/*
		if (m_Criteria->m_IsDiscrete)
			m_
		else
			pButton->SetCheck(0);
		*/
				
	}

	UpdateData(FALSE);

	return TRUE;  // return TRUE unless you set the focus to a control
	              // EXCEPTION: OCX Property Pages should return FALSE
}

void CProductGroupCriteriaPropertiesPage::OnOK() 
{

	CPropertyPage::OnOK();
}


void CProductGroupCriteriaPropertiesPage::OnSelchangeAttribute() 
{
	CString title;
	//CProductAttribute *oldAttr, *newAttr;

	UpdateData(TRUE);
	int curSel = m_AttributeCtrl.GetCurSel();

	CButton *pButton = (CButton *)GetDlgItem(IDC_LIST);
	CPropertySheet *pParent = (CPropertySheet *)GetParent();

	if (pButton->GetCheck() && curSel >= 0) {
		if (pParent->GetPageCount() == 1)
			pParent->AddPage(m_ListPage);

		if (m_PreviousSelection >= 0) {	
			if (m_PreviousSelection != curSel) {
				ClearList();
			}
		}

	}
	else {
		if (pParent->GetPageCount() == 2)
			pParent->RemovePage(m_ListPage);
	}

	m_PreviousSelection = curSel;

	title = "Product Group Criteria Properties";
	title += "- ";
	title += m_Attribute;
	pParent->SetTitle(title);

	return;

}

BOOL CProductGroupCriteriaPropertiesPage::OnKillActive() 
{
	UpdateData(TRUE);

	CButton *pButton = (CButton *)GetDlgItem(IDC_LIST);
	int curSel = m_AttributeCtrl.GetCurSel();

	if (m_Name == "") {
		AfxMessageBox("Please enter a name for the criteria.");
		m_NameCtrl.SetFocus();
		return FALSE;
	}

	if (m_Description == "") {
		AfxMessageBox("Please enter a description for the criteria.");
		m_DescriptionCtrl.SetFocus();
		return FALSE;
	}

	if (m_FormulaCheckCtrl.GetCheck()) {
		if (m_Formula == "") {
			AfxMessageBox("Please enter a formula.");
			m_FormulaCtrl.SetFocus();
			return FALSE;
		}
	}
	else {
		if (curSel < 0) {
			AfxMessageBox("Please select an attribute for the criteria.");
			m_AttributeCtrl.SetFocus();
			m_AttributeCtrl.ShowDropDown(TRUE);
			return FALSE;
		}
	}

	m_Criteria->m_Name = m_Name;
	m_Criteria->m_Description = m_Description;
	if (m_FormulaCheckCtrl.GetCheck()) {
		m_Criteria->SetAttribute(m_Formula);
		m_Criteria->SetAttributeType(DT_FORMULA);
	}
	else {
		m_Criteria->SetAttribute(m_Attribute);
		if (curSel >= 0) {
			int attrIdx = m_AttributeCtrl.GetItemData(curSel);
			m_Criteria->SetAttributeType(m_ProductGroupDataService->m_ProductAttributeList[attrIdx]->m_Type);

		}
	}

	m_Criteria->m_IsDiscrete = (pButton->GetCheck() == 1);

	return CPropertyPage::OnKillActive();
}

void CProductGroupCriteriaPropertiesPage::OnEditchangeAttribute() 
{
	CString title;
	CPropertySheet *pParent = (CPropertySheet *)GetParent();

	title = "Product Group Criteria Properties";
	title += "- ";
	title += m_Attribute;
	pParent->SetTitle(title);
	
}

void CProductGroupCriteriaPropertiesPage::OnFormulaCheck() 
{
	UpdateData(TRUE);

	if (m_PreviousSelection >= 0)
		ClearList();

	if (m_FormulaCheckCtrl.GetCheck()) {
		m_AttributeCtrl.ShowWindow(SW_HIDE);
		m_FormulaCtrl.ShowWindow(SW_SHOW);
		GetDlgItem(IDC_VALIDATE_FORMULA)->ShowWindow(SW_SHOW);
	}
	else {
		m_FormulaCtrl.ShowWindow(SW_HIDE);
		m_AttributeCtrl.ShowWindow(SW_SHOW);
		GetDlgItem(IDC_VALIDATE_FORMULA)->ShowWindow(SW_HIDE);
	}

}

void CProductGroupCriteriaPropertiesPage::ClearList()
{
	if (m_Criteria->m_CriteriaRangeList.GetSize() > 0 || m_Criteria->m_CriteriaValueList.GetSize() > 0) {
		for (int i=0; i < m_Criteria->m_CriteriaRangeList.GetSize(); ++i) {
			if (m_Criteria->m_CriteriaRangeList[i]->m_InUse) {
				AfxMessageBox("Warning! At least one of the current list elements\n"
							  "is being used by a product group. The new attribute\n"
							  "will be only be associated with a product group if\n"
							  "if products are re-assigned to that group.");
				return;
			}
		}
		if (AfxMessageBox("Do you wish to clear the list information?", MB_YESNO) == IDYES) {
			for (i=0; i < m_Criteria->m_CriteriaRangeList.GetSize(); ++i)
				delete m_Criteria->m_CriteriaRangeList[i];
			for (i=0; i < m_Criteria->m_CriteriaValueList.GetSize(); ++i)
				delete m_Criteria->m_CriteriaValueList[i];
			m_Criteria->m_CriteriaRangeList.RemoveAll();
			m_Criteria->m_CriteriaValueList.RemoveAll();
			m_ListPage->m_FromValueCtrl.SetWindowText("");
			m_ListPage->m_ToValueCtrl.SetWindowText("");
			m_ListPage->m_RangeListCtrl.DeleteAllItems();
			m_ListPage->m_ValueListCtrl.DeleteAllItems();
		}
	}

	return;
}

void CProductGroupCriteriaPropertiesPage::OnChangeFormula() 
{
	CString title, temp;
	CPropertySheet *pParent = (CPropertySheet *)GetParent();

	title = "Product Group Criteria Properties";
	title += "- ";
	m_FormulaCtrl.GetWindowText(temp);
	title += temp;
	pParent->SetTitle(title);
	
}



void CProductGroupCriteriaPropertiesPage::OnValidateFormula() 
{
	UpdateData(TRUE);
	BOOL rc;
	CString attributes, temp;

	try {
		rc = m_ProductGroupDataService->ValidateFormula(m_Formula, attributes);
	}
	catch (...) {
		rc = FALSE;
	}
	
	if (! rc) {
		temp.Format("The formula is not valid.  The following items were found:\n%s", attributes);\
		AfxMessageBox(temp, MB_ICONERROR);
	}
	else
		AfxMessageBox("The formula is valid.", MB_ICONINFORMATION);


	return;

}

BOOL CProductGroupCriteriaPropertiesPage::OnNotify(WPARAM wParam, LPARAM lParam, LRESULT* pResult) 
{
	NMHDR* pNMHDR = (NMHDR*)lParam;
	if (pNMHDR->code == PSN_HELP) {
		helpService.ShowScreenHelp(IDD);
		return TRUE;
	}		
	return CPropertyPage::OnNotify(wParam, lParam, pResult);
}

BOOL CProductGroupCriteriaPropertiesPage::OnHelpInfo(HELPINFO* pHelpInfo) 
{
	helpService.ShowFieldHelp(IDD, pHelpInfo->iCtrlId);
	return FALSE;
}


void CProductGroupCriteriaPropertiesPage::OnList() 
{
	UpdateData(TRUE);
	CPropertySheet *pParent = (CPropertySheet *)GetParent();

	if (m_ListCheckBox) {
		
		if (pParent->GetPageCount() == 1)
			pParent->AddPage(m_ListPage);
		
		if (m_Criteria->m_CriteriaRangeList.GetSize() > 0) {
			if (m_Criteria->m_CriteriaRangeList[0]->m_Description == "Default") {
				delete m_Criteria->m_CriteriaRangeList[0];
				m_Criteria->m_CriteriaRangeList.RemoveAll();
			}
		}

	}
	else {
		if (pParent->GetPageCount() == 2)
			pParent->RemovePage(m_ListPage);
		
	}
	return;
	
}

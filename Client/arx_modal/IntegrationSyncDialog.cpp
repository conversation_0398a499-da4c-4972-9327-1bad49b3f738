// IntegrationSyncDialog.cpp : implementation file
//

#include "stdafx.h"
#include "modal.h"
#include "IntegrationSyncDialog.h"
#include "IntegrationDataService.h"
#include "UtilityHelper.h"
#include "ControlService.h"
#include "IntegrationStatusDialog.h"

#ifdef _DEBUG
#define new DEBUG_NEW
#undef THIS_FILE
static char THIS_FILE[] = __FILE__;
#endif

extern CControlService controlService;
extern CUtilityHelper utilityHelper;
extern CIntegrationDataService integrationDataService;

/////////////////////////////////////////////////////////////////////////////
// CIntegrationSyncDialog dialog


CIntegrationSyncDialog::CIntegrationSyncDialog(CWnd* pParent /*=NULL*/)
	: CDialog(CIntegrationSyncDialog::IDD, pParent)
{
	//{{AFX_DATA_INIT(CIntegrationSyncDialog)
	//}}AFX_DATA_INIT
}


void CIntegrationSyncDialog::DoDataExchange(CDataExchange* pDX)
{
	CDialog::DoDataExchange(pDX);
	//{{AFX_DATA_MAP(CIntegrationSyncDialog)
	DDX_Control(pDX, IDC_WMS_LIST, m_WMSListCtrl);
	//}}AFX_DATA_MAP
}

CIntegrationSyncDialog::~CIntegrationSyncDialog()
{
	POSITION pos = m_WMSGroupMap.GetStartPosition();
	while (pos != NULL) {
		int dummy;
		CWMS *pWMS;
		m_WMSGroupMap.GetNextAssoc(pos, dummy, pWMS);
		delete pWMS;
	}

}

BEGIN_MESSAGE_MAP(CIntegrationSyncDialog, CDialog)
	//{{AFX_MSG_MAP(CIntegrationSyncDialog)
	ON_WM_HELPINFO()
	//}}AFX_MSG_MAP
END_MESSAGE_MAP()

/////////////////////////////////////////////////////////////////////////////
// CIntegrationSyncDialog message handlers

BOOL CIntegrationSyncDialog::OnInitDialog() 
{
	CDialog::OnInitDialog();
	
	CStringArray group;
	CWaitCursor cwc;

	try {
		integrationDataService.GetIntegratedGroupList(groupList);
	}
	catch (...) {
		controlService.Log("Error getting integrated WMS Group list.", "Generic exception in GetIntegratedGroupList.");
	}

	/* todo: later add ability to do all at once
	if (wmsList.GetSize() > 0) {
		int nItem = m_WMSListCtrl.AddString("All");
		m_WMSListCtrl.SetItemData(nItem, 0);
	}
	*/
	for (int i=0; i < groupList.GetSize(); ++i) {
		CWMSGroup wms;
		CWMS *pWMS;
		wms.Parse(wmsList[i]);
		if (! m_WMSGroupMap.Lookup(wms.m_GroupDBId, pWMS)) {
			pWMS = new CWMS(wms);
			m_WMSGroupMap.SetAt(pWMS->m_GroupDBId, pWMS);
			CString temp;
			temp.Format("%s", pWMS->m_GroupName);
			int nItem = m_WMSListCtrl.AddString(temp);
			m_WMSListCtrl.SetItemData(nItem, (unsigned long)pWMS);
		}
	}

	CRect r;
	m_WMSListCtrl.GetWindowRect(&r);
	m_WMSListCtrl.SetWindowPos(NULL, 0, 0, r.Width(), r.Height()*(m_WMSListCtrl.GetCount()+1), SWP_NOMOVE|SWP_NOZORDER);

	if (m_WMSListCtrl.GetCount() > 0)
		m_WMSListCtrl.SetCurSel(0);

	UpdateData(FALSE);

	return TRUE;  // return TRUE unless you set the focus to a control
	              // EXCEPTION: OCX Property Pages should return FALSE
}

void CIntegrationSyncDialog::OnOK() 
{
	CIntegrationStatusDialog dlg;

	if (m_WMSListCtrl.GetCurSel() < 0) {
		AfxMessageBox("Please select a WMS Group to synchronize.");
		return;
	}


	CWMS *pWMS = (CWMS *)m_WMSListCtrl.GetItemData(m_WMSListCtrl.GetCurSel());
	dlg.m_WMSGroupDBId = pWMS->m_GroupDBId;
	dlg.m_GroupName = pWMS->m_GroupName;

	//this->ShowWindow(SW_HIDE);
	try {
		dlg.DoModal();
	}
	catch (...) {
		controlService.Log("Error running WMS Synchronization.", "Generic exception in CIntegrationStatusDialog.");
	}
	this->ShowWindow(SW_SHOW);

}

BOOL CIntegrationSyncDialog::OnHelpInfo(HELPINFO* pHelpInfo) 
{
	// TODO: Add your message handler code here and/or call default
	
	return CDialog::OnHelpInfo(pHelpInfo);
}

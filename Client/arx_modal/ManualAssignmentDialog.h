#if !defined(AFX_MANUALASSIGNMENTDIALOG_H__D949D8A3_7843_11D4_9EAD_00C04FAC149C__INCLUDED_)
#define AFX_MANUALASSIGNMENTDIALOG_H__D949D8A3_7843_11D4_9EAD_00C04FAC149C__INCLUDED_

#if _MSC_VER > 1000
#pragma once
#endif // _MSC_VER > 1000
// ManualAssignmentDialog.h : header file
//
#include "qqhclasses.h"
#include "QueryLocations.h"
#include "ProductPack.h"
#include "LocationInfo.h"
#include "Solution.h"
#include <ssa_exception.h>

/////////////////////////////////////////////////////////////////////////////
// CManualAssignmentDialog dialog


class CManualAssignmentDialog : public CDialog
{
// Construction
public:
	int m_WarningLevel;
	int GetCaseCount(CProductPack &product, CLocationQueryInfo &location);
	CManualAssignmentDialog(CWnd* pParent = NULL);   // standard constructor
	~CManualAssignmentDialog();

// Dialog Data
	//{{AFX_DATA(CManualAssignmentDialog)
	enum { IDD = IDD_MANUAL_ASSIGNMENT };
	CListBox	m_LocationListBox;
	CListBox	m_ProductListBox;
	BOOL	m_SkipCostUpdate;
	//}}AFX_DATA


// Overrides
	// ClassWizard generated virtual function overrides
	//{{AFX_VIRTUAL(CManualAssignmentDialog)
	protected:
	virtual void DoDataExchange(CDataExchange* pDX);    // DDX/DDV support
	//}}AFX_VIRTUAL

// Implementation
protected:

	// Generated message map functions
	//{{AFX_MSG(CManualAssignmentDialog)
	virtual BOOL OnInitDialog();
	afx_msg void OnQueryProducts();
	afx_msg void OnSelchangeProductList();
	afx_msg void OnSelchangeLocationList();
	afx_msg void OnQueryLocations();
	afx_msg void OnAssign();
	afx_msg void OnUnassignProduct();
	afx_msg void OnUnassignLocation();
	afx_msg BOOL OnHelpInfo(HELPINFO* pHelpInfo);
	afx_msg void OnHelp();
	afx_msg void OnRButtonDblClk(UINT nFlags, CPoint point);
	afx_msg void OnSkipCostUpdate();
	afx_msg void OnDblclkProductList();
	afx_msg void OnDblclkLocationList();
	//}}AFX_MSG
	DECLARE_MESSAGE_MAP()

private:
	void UpdateCost(CString &productIDList);
	int CheckRankings(CProductPack &product, CLocationQueryInfo &location);
	void UpdateForProductUnassignment(int prodIdx);
	void UpdateForLocationUnassignment(int locIdx);
	void UpdateForAssignment(int prodIdx, int locIdx, int isPrimary, int caseCount);
	int UnassignLocation(CLocationQueryInfo &location);
	int UnassignProduct(CProductPack &product, int primaryOnly);
	int CreateAssignment(CProductPack &product, CLocationQueryInfo &location, int caseCount, int isPrimary);
	int CheckFacings(CProductPack &product, CLocationQueryInfo &location);
	int CheckWeight(CProductPack &product, CLocationQueryInfo &location, int cases);
	CStringArray m_ProductGroupList;
	CQueryLocations m_QueryLocationsDlg;
	int GetPalletHandlingCount(double lw, double ld, double lh,
													double pw, double pl, double ph);
	int GetCaseHandlingCount(double lw, double ld, double lh,
												  double pw, double pl, double ph,
												  int levelType);
	int LocationsAreAdjacent(CLocationQueryInfo &loc1, CLocationQueryInfo &loc2);
	CTypedPtrArray<CObArray, CProductPack*> m_ProductList;

	CLocationQueryInfo *m_LocationList;
	int m_LocationCount;
};

#endif //!defined(AFX_MANUALASSIGNMENTDIALOG_H__D949D8A3_7843_11D4_9EAD_00C04FAC149C__INCLUDED_)

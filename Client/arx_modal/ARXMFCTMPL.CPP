//////////////////////////////////////////////////////////////////////
// Module Name : arxmfctmpl.cpp
// Classname : None
// Description : Main function of the facility Autocad interface
// Date Created : 10/01/98
// Author : acs
//////////////////////////////////////////////////////////////////////
// Revisions :
//   Date(mm-dd-yyyy) : initials : Description_of_revision
//   03-02-2006       : SMB      : Add Prolog and
//                                 Fix ARX Intilization warnings
//////////////////////////////////////////////////////////////////////
/*******************************************************
*
*                       NOTICE
*
*  THIS SOFTWARE IS THE PROPERTY OF AND CONTAINS
*  CONFIDENTIAL INFORMATION OF SSA GLOBAL
*  TECHNOLOGIES, INC., AND SHALL NOT BE COPIED,
*  USED, NOR DISCLOSED WITHOUT EXPRESS WRITTEN
*  AUTHORIZATION.  ALTHOUGH PUBLICATION IS NOT
*  INTENDED, IN THE EVENT OF PUBLICATION, THE
*  FOLLOWING NOTICE IS APPLICABLE:
*
*  (c) COPYRIGHT 2004 SSA GLOBAL TECHNOLOGIES, INC.
*
*           SSA GLOBAL TECHNOLOGIES, INC.
*
********************************************************/

//////////////////////////////////////////////////////////////////////
// Includes
//////////////////////////////////////////////////////////////////////
#include "stdafx.h"
#include "ResourceHelper.h"
#include "NavigationHelper.h"

#include <aced.h>
#include <adscodes.h>
#include <rxregsvc.h>

extern "C" HINSTANCE _hdllInstance;

#ifndef _AUTOCAD2000
   static AFX_EXTENSION_MODULE arxmfcDLL;
#else
   AC_IMPLEMENT_EXTENSION_MODULE(theArxDLL);
#endif //END #ifndef _AUTOCAD2000

//////////////////////////////////////////////////////////////
// Function Prototypes
//////////////////////////////////////////////////////////////
BOOL InitModule(HINSTANCE, DWORD, LPVOID);

//////////////////////////////////////////////////////////////////////
// Function Name : acrxEntryPoint
// Classname : NONE
// Description :
// Date Created : 10/01/98
// Author : acs
//////////////////////////////////////////////////////////////////////
// Inputs : AcRx::AppMsgCode msg -
//          void* appId
// Outputs : AcRx::AppRetCode
//           0 - ERROR
//           1 - OK
//
// Explanation :
//////////////////////////////////////////////////////////////////////
// Revisions :
//   Date(mm-dd-yyyy) : initials : Description_of_revision
//   03-02-2006       : SMB      : Fix ARX Intilization warnings
//////////////////////////////////////////////////////////////////////
#ifndef _AUTOCAD2000
extern "C" AcRx::AppRetCode acrxEntryPoint( AcRx::AppMsgCode msg, void* appId)
#else
AcRx::AppRetCode acrxEntryPoint( AcRx::AppMsgCode msg, void* appId)
#endif //END #ifndef _AUTOCAD2000
{
	CNavigationHelper navHelper;
	switch( msg ) {
    case AcRx::kInitAppMsg:
		acrxUnlockApplication(appId);
		acrxRegisterAppNotMDIAware(appId);

        #ifndef _AUTOCAD2000
		// Initialize MFC
		ads_printf("Intializing MFC\n");
		InitModule(_hdllInstance, DLL_PROCESS_ATTACH, NULL);
        #endif //END #ifndef _AUTOCAD2000

		ads_printf("Intializing Optimize\n");
		navHelper.InitApp();
		break;
    case AcRx::kUnloadAppMsg:
		navHelper.UnloadApp();

        #ifndef _AUTOCAD2000
		//End MFC
		InitModule(_hdllInstance, DLL_PROCESS_DETACH, NULL);
        #endif //END #ifndef _AUTOCAD2000

		break;
	case AcRx::kLoadDwgMsg:
		break;
	case AcRx::kUnloadDwgMsg:
		break;
	case AcRx::kPreQuitMsg:
		break;
    default:
		break;
	}//END switch( msg )

	return AcRx::kRetOK;

} //END AcRx::AppRetCode acrxEntryPoint( AcRx::AppMsgCode msg, void* appId)

#ifndef _AUTOCAD2000
//////////////////////////////////////////////////////////////////////
// Function Name : InitModule
// Classname : NONE
// Description :
// Date Created : 10/01/98
// Author : acs
//////////////////////////////////////////////////////////////////////
// Inputs : HINSTANCE hInstance -
//          DWORD dwReason
//          LPVOID
// Outputs : AcRx::AppRetCode
//           0 - ERROR
//           1 - OK
//
// Explanation : This Function is available if "#ifndef _AUTOCAD2000"
//
//////////////////////////////////////////////////////////////////////
// Revisions :
//   Date(mm-dd-yyyy) : initials : Description_of_revision
//   03-02-2006       : SMB      : Add Prolog
//////////////////////////////////////////////////////////////////////
extern BOOL InitModule(HINSTANCE hInstance, DWORD dwReason, LPVOID)
{
	if (dwReason == DLL_PROCESS_ATTACH) {
		// Extension DLL one-time initialization
		if (!AfxInitExtensionModule(
			arxmfcDLL, hInstance))
		{
			return 0;
		}//END if (!AfxInitExtensionModule(
	     //arxmfcDLL, hInstance))

		// CTemporaryResourceOverride initialization
		CTemporaryResourceOverride::SetDefaultResource(_hdllInstance);

		// Insert this DLL into current process state.
		new CDynLinkLibrary(arxmfcDLL);

	}//END if (dwReason == DLL_PROCESS_ATTACH)
	else if (dwReason == DLL_PROCESS_DETACH) {
		// Extension DLL per-process termination
		AfxTermExtensionModule(arxmfcDLL);
	}//END else if (dwReason == DLL_PROCESS_DETACH)

	return 1;   // ok
}//END extern BOOL InitModule(HINSTANCE hInstance, DWORD dwReason, LPVOID)

#else
//////////////////////////////////////////////////////////////////////
// Function Name : DllMain
// Classname : NONE
// Description :
// Date Created : 10/01/98
// Author : acs
//////////////////////////////////////////////////////////////////////
// Inputs : HINSTANCE hInstance -
//          DWORD dwReason
//          LPVOID lpReserved
// Outputs : BOOL
//           0 - ERROR
//           1 - OK
//
// Explanation :
//////////////////////////////////////////////////////////////////////
// Revisions :
//   Date(mm-dd-yyyy) : initials : Description_of_revision
//   03-02-2006       : SMB      : Add Prolog
//////////////////////////////////////////////////////////////////////
extern "C" int APIENTRY DllMain(HINSTANCE hInstance, DWORD dwReason, LPVOID lpReserved)
{
    // Remove this if you use lpReserved
    UNREFERENCED_PARAMETER(lpReserved);

    if (dwReason == DLL_PROCESS_ATTACH) {
        theArxDLL.AttachInstance(hInstance);
		CTemporaryResourceOverride::SetDefaultResource(hInstance);
    }//END if (dwReason == DLL_PROCESS_ATTACH)
    else if (dwReason == DLL_PROCESS_DETACH) {
        theArxDLL.DetachInstance();
    }//END else if (dwReason == DLL_PROCESS_DETACH)

    return 1;   // ok
}//END DllMain(HINSTANCE hInstance, DWORD dwReason, LPVOID lpReserved)

#endif //END #ifndef _AUTOCAD2000


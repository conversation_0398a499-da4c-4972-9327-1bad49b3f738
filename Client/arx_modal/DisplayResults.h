#if !defined(AFX_DISPLAYRESULTS_H__C632216D_6013_11D4_9197_00400542E36B__INCLUDED_)
#define AFX_DISPLAYRESULTS_H__C632216D_6013_11D4_9197_00400542E36B__INCLUDED_

#if _MSC_VER > 1000
#pragma once
#endif // _MSC_VER > 1000
// DisplayResults.h : header file
//

/////////////////////////////////////////////////////////////////////////////
// CDisplayResults dialog
#include <afxtempl.h>
#include "SSACStringArray.h"
#include "resource.h"

#define IDC_DISPLAY_RESULTS_NEXT1 WM_USER+1
#define IDC_DISPLAY_RESULTS_NEXT2 WM_USER+2
#define IDC_DISPLAY_RESULTS_NEXT3 WM_USER+3

const int WM_CHANGESELECTION  = WM_USER+9;
const int WM_CLOSEDISPLAY = WM_USER+10;
const int WM_DISPLAY_RESULTS_BUTTON1 = WM_USER+11;
const int WM_DISPLAY_RESULTS_BUTTON2 =  WM_USER+12;
const int WM_DISPLAY_RESULTS_BUTTON3 = WM_USER+13;
const int WM_DISPLAY_RESULTS_SIZE = WM_USER+14;
const int NO_SELECTION = 99999999;

class CDisplayResults : public CDialog
{
// Construction
public:
	static int Compare(const void **p1, const void **p2);
	CStringArray m_ColumnFormat;
	void DeleteItem(int originalPosition);
	void ModifyItem(int originalPosition, CString &newData);
	void ReloadPage();
	CDWordArray m_SelectionList;
	BOOL m_AllowMultipleSelections;
	BOOL m_AllowSort;
	CWnd *m_MessageReceiver;
	BOOL m_IsModeless;
	int m_SelectionIdx;
	int m_OrigColumnSize;
	CString m_MainHelpTopic;
	CStringArray m_HelpTopics;
	CStringArray m_ListHelpTopics;
	CDisplayResults(CWnd* pParent = NULL);   // standard constructor
	~CDisplayResults();
	CStringArray m_WindowCaptions;
	CString m_NextCaption;
	CString m_NextCaption2;
	CString m_NextCaption3;
	BOOL m_NextClosesWindow;
	BOOL m_NextClosesWindow2;
	BOOL m_NextClosesWindow3;
	CString m_NextHelp1;
	CString m_NextHelp2;
	CString m_NextHelp3;
	void DisplayItems(int page);
	void SortList(int page, int sortColumn);
	CStringArray m_HeaderKeys;
	CDWordArray m_PageDataCount;
	void LoadData();
	void BuildHeaders(int page);
	CStringArray m_Tabs;
	CStringArray m_Headers;
	int m_CurrentTab;
	CTypedPtrArray<CObArray,CListCtrl*> m_ListCtrlArray;
	CSsaStringArray m_Data;
	int m_ParentOldWidth;
	int m_ParentOldHeight;
	int m_ButtonOrigXDiff;
	int m_ButtonOrigYDiff;
	int m_ButtonOrigWidth;
	int m_ButtonOrigHeight;
	int m_TabOrigWidth;
	int m_TabOrigHeight;
	CToolTipCtrl m_ToolTip;
	void LoadToolTips();
	CString m_SelectAllHelp;
// Dialog Data
	//{{AFX_DATA(CDisplayResults)
	enum { IDD = IDD_DISPLAY_RESULTS };
	CButton	m_SelectAllButton;
	CButton	m_NextBtn3;
	CButton	m_NextBtn2;
	CButton	m_ExcelBtn;
	CTabCtrl	m_TabCtrl;
	CButton	m_NextBtn;
	CButton	m_HelpBtn;
	CButton	m_CloseBtn;
	//}}AFX_DATA


// Overrides
	// ClassWizard generated virtual function overrides
	//{{AFX_VIRTUAL(CDisplayResults)
	public:
	virtual BOOL PreTranslateMessage(MSG* pMsg);
	protected:
	virtual void DoDataExchange(CDataExchange* pDX);    // DDX/DDV support
	virtual BOOL OnNotify(WPARAM wParam, LPARAM lParam, LRESULT* pResult);
	virtual void PostNcDestroy();
	//}}AFX_VIRTUAL

// Implementation
protected:

	// Generated message map functions
	//{{AFX_MSG(CDisplayResults)
	afx_msg void OnSelchangeResultsTab(NMHDR* pNMHDR, LRESULT* pResult);
	afx_msg void OnCancel();
	afx_msg void OnNext();
	afx_msg void OnHelp();
	virtual BOOL OnInitDialog();
	afx_msg void OnSize(UINT nType, int cx, int cy);
	afx_msg void OnExcel();
	afx_msg BOOL OnHelpInfo(HELPINFO* pHelpInfo);
	afx_msg void OnNext2();
	afx_msg void OnNext3();
	afx_msg void OnSelectAll();
	afx_msg void OnMove(int x, int y);
	afx_msg void OnLButtonDblClk(UINT nFlags, CPoint point);
	afx_msg void OnRButtonDblClk(UINT nFlags, CPoint point);
	//}}AFX_MSG
	DECLARE_MESSAGE_MAP()
private:
	BOOL m_Loading;
	CMap<int, int, int, int> m_PositionMap;
};

//{{AFX_INSERT_LOCATION}}
// Microsoft Visual C++ will insert additional declarations immediately before the previous line.

#endif // !defined(AFX_DISPLAYRESULTS_H__C632216D_6013_11D4_9197_00400542E36B__INCLUDED_)

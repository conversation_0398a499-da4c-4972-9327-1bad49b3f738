#if !defined(AFX_INTEGRATIONPRODUCTOPTIONSPAGE_H__773A937D_DC05_4DF8_A056_E588B02576BA__INCLUDED_)
#define AFX_INTEGRATIONPRODUCTOPTIONSPAGE_H__773A937D_DC05_4DF8_A056_E588B02576BA__INCLUDED_

#if _MSC_VER > 1000
#pragma once
#endif // _MSC_VER > 1000
// IntegrationProductOptionsPage.h : header file
//

/////////////////////////////////////////////////////////////////////////////
// CIntegrationProductOptionsPage dialog

class CIntegrationProductOptionsPage : public CPropertyPage
{
	DECLARE_DYNCREATE(CIntegrationProductOptionsPage)

// Construction
public:
	CIntegrationProductOptionsPage();
	~CIntegrationProductOptionsPage();

// Dialog Data
	//{{AFX_DATA(CIntegrationProductOptionsPage)
	enum { IDD = IDD_INTEGRATION_PRODUCT_OPTIONS };
	BOOL	m_SkipProduct;
	BOOL	m_InboundPrompt;
	BOOL	m_SkipPgUpdate;
	//}}AFX_DATA


// Overrides
	// ClassWizard generate virtual function overrides
	//{{AFX_VIRTUAL(CIntegrationProductOptionsPage)
	protected:
	virtual void DoDataExchange(CDataExchange* pDX);    // DDX/DDV support
	virtual BOOL OnNotify(WPARAM wParam, LPARAM lParam, LRESULT* pResult);
	//}}AFX_VIRTUAL

// Implementation
protected:
	// Generated message map functions
	//{{AFX_MSG(CIntegrationProductOptionsPage)
	virtual BOOL OnInitDialog();
	afx_msg void OnInboundPrompt();
	afx_msg void OnSkip();
	afx_msg void OnSkipPgUpdate();
	afx_msg BOOL OnHelpInfo(HELPINFO* pHelpInfo);
	//}}AFX_MSG
	DECLARE_MESSAGE_MAP()

};

//{{AFX_INSERT_LOCATION}}
// Microsoft Visual C++ will insert additional declarations immediately before the previous line.

#endif // !defined(AFX_INTEGRATIONPRODUCTOPTIONSPAGE_H__773A937D_DC05_4DF8_A056_E588B02576BA__INCLUDED_)


// ElementMaintenanceCommands.cpp: implementation of the CElementMaintenanceCommands class.
//
//////////////////////////////////////////////////////////////////////

#include "stdafx.h"
#include "modal.h"
#include "ElementMaintenanceCommands.h"
#include "ElementMaintenanceHelper.h"
#include "ControlService.h"

#ifdef _DEBUG
#undef THIS_FILE
static char THIS_FILE[]=__FILE__;
#define new DEBUG_NEW
#endif

extern CControlService controlService;
//////////////////////////////////////////////////////////////////////
// Construction/Destruction
//////////////////////////////////////////////////////////////////////

CElementMaintenanceCommands::CElementMaintenanceCommands()
{

}

CElementMaintenanceCommands::~CElementMaintenanceCommands()
{

}

void CElementMaintenanceCommands::RegisterCommands()
{
	
	// Element Maintenance
	acedRegCmds->addCommand( "SLOTGEN", "FACILITYHOLDER", "FACILITYHOLDER",
		ACRX_CMD_MODAL, &CElementMaintenanceCommands::FacilityMaintenance );
	acedRegCmds->addCommand( "SLOTGEN", "SECTIONHOLDER", "SECTIONHOLDER",
		ACRX_CMD_MODAL, &CElementMaintenanceCommands::SectionMaintenance );
	acedRegCmds->addCommand( "SLOTGEN", "AISLEHOLDER", "AISLEHOLDER",
		ACRX_CMD_MODAL, &CElementMaintenanceCommands::AisleMaintenance );
	acedRegCmds->addCommand( "SLOTGEN", "SIDEHOLDER", "SIDEHOLDER",
		ACRX_CMD_MODAL, &CElementMaintenanceCommands::SideMaintenance );
	acedRegCmds->addCommand( "SLOTGEN", "BAYHOLDER", "BAYHOLDER",
		ACRX_CMD_MODAL, &CElementMaintenanceCommands::BayMaintenance );
	acedRegCmds->addCommand( "SLOTGEN", "LEVELLOCHOLDER", "LEVELLOCHOLDER",
		ACRX_CMD_MODAL, &CElementMaintenanceCommands::ElementMaintenance );

	acedRegCmds->addCommand( "SLOTFAC", "FACILITYTOOLS", "FACILITYTOOLS",
		ACRX_CMD_MODAL, &CElementMaintenanceCommands::FacilityTreeViewer);
	acedRegCmds->addCommand( "SLOTFAC", "FACILITYTREE", "FACILITYTREE",
		ACRX_CMD_MODAL, &CElementMaintenanceCommands::FacilityTreeViewer);	
	acedRegCmds->addCommand( "SLOTFAC", "FT", "FT",
		ACRX_CMD_MODAL, &CElementMaintenanceCommands::FacilityTreeViewer);
	
	acedRegCmds->addCommand( "SLOTFAC", "ADDAISLE", "ADDAISLE",
		ACRX_CMD_MODAL, &CElementMaintenanceCommands::AddAisle );
	acedRegCmds->addCommand( "SLOTFAC", "ADDPICKPATH", "ADDPICKPATH",
		ACRX_CMD_MODAL, &CElementMaintenanceCommands::AddPickPath ); 
	acedRegCmds->addCommand( "SLOTFAC", "DELETEBAY", "DELETEBAY",
		ACRX_CMD_MODAL, &CElementMaintenanceCommands::DeleteBay );
	acedRegCmds->addCommand( "SLOTFAC", "DELETEAISLE", "DELETEAISLE",
		ACRX_CMD_MODAL, &CElementMaintenanceCommands::DeleteAisle );
	acedRegCmds->addCommand( "SLOTFAC", "DELETEPICKPATH", "DELETEPICKPATH",
		ACRX_CMD_MODAL, &CElementMaintenanceCommands::DeletePickPath );
	acedRegCmds->addCommand( "SLOTFAC", "ADDHOTSPOT", "ADDHOTSPOT",
		ACRX_CMD_MODAL, &CElementMaintenanceCommands::AddHotSpot );
	acedRegCmds->addCommand( "SLOTFAC", "PICKPATHPROPERTIES", "PICKPATHPROPERTIES",
		ACRX_CMD_MODAL, &CElementMaintenanceCommands::PickPathProperties );
	
	acedRegCmds->addCommand( "SLOTFAC", "RENUMBER", "RENUMBER",
		ACRX_CMD_MODAL, &CElementMaintenanceCommands::RenumberAisle );
	acedRegCmds->addCommand( "SLOTFAC", "READDPICKPATH", "READDPICKPATH",
		ACRX_CMD_MODAL, &CElementMaintenanceCommands::ReAddPickPath );

	acedRegCmds->addCommand( "SLOTGEN", "CHANGERACKTYPE", "CHANGERACKTYPE",
		ACRX_CMD_MODAL, &CElementMaintenanceCommands::ChangeRacktype );
}



void CElementMaintenanceCommands::FacilityMaintenance()
{
	if (! controlService.ValidateCommand(CCommands::newFacilityMode))
		return;

	CElementMaintenanceHelper helper;

	helper.FacilityMaintenance();

	return;

}


void CElementMaintenanceCommands::SectionMaintenance()
{
	if (! controlService.ValidateCommand(CCommands::newFacilityMode))
		return;

	CElementMaintenanceHelper helper;

	helper.SectionMaintenance();

	return;
}


void CElementMaintenanceCommands::AisleMaintenance()
{
	if (! controlService.ValidateCommand(CCommands::newFacilityMode))
		return;

	CElementMaintenanceHelper helper;

	helper.AisleMaintenance();

	return;

}


void CElementMaintenanceCommands::SideMaintenance()
{
	if (! controlService.ValidateCommand(CCommands::newFacilityMode))
		return;

	CElementMaintenanceHelper helper;

	helper.SideMaintenance();

	return;
}


void CElementMaintenanceCommands::BayMaintenance()
{
	if (! controlService.ValidateCommand(CCommands::newFacilityMode))
		return;

	CElementMaintenanceHelper helper;

	helper.BayMaintenance();

	return;
}


void CElementMaintenanceCommands::LevelLocationMaintenance()
{
	if (! controlService.ValidateCommand(CCommands::newFacilityMode))
		return;


	CElementMaintenanceHelper helper;

	helper.LevelLocationMaintenance();

	return;
}


void CElementMaintenanceCommands::AddAisle()
{
	if (! controlService.ValidateCommand(CCommands::newFacilityMode))
		return;

	CElementMaintenanceHelper helper;

	helper.AddAisle();

	return;
}

void CElementMaintenanceCommands::AddPickPath()
{	
	if (! controlService.ValidateCommand(CCommands::newFacilityMode))
		return;

	CElementMaintenanceHelper helper;

	helper.AddPickPath();

	return;
}

void CElementMaintenanceCommands::DeleteBay()
{	
	if (! controlService.ValidateCommand(CCommands::newFacilityMode))
		return;

	CElementMaintenanceHelper helper;

	helper.DeleteBay();

	return;
}

void CElementMaintenanceCommands::DeleteAisle()
{
	if (! controlService.ValidateCommand(CCommands::newFacilityMode))
		return;

	CElementMaintenanceHelper helper;

	helper.DeleteAisle();

	return;

}

void CElementMaintenanceCommands::DeletePickPath()
{	
	if (! controlService.ValidateCommand(CCommands::newFacilityMode))
		return;

	CElementMaintenanceHelper helper;

	helper.DeletePickPath();

	return;

}

void CElementMaintenanceCommands::AddHotSpot()
{	
	if (! controlService.ValidateCommand(CCommands::newFacilityMode))
		return;

	CElementMaintenanceHelper helper;

	helper.AddHotSpot();

	return;

}

void CElementMaintenanceCommands::PickPathProperties()
{	
	if (! controlService.ValidateCommand(CCommands::newFacilityMode))
		return;

	CElementMaintenanceHelper helper;

	helper.PickPathProperties();

	return;

}

void CElementMaintenanceCommands::RenumberAisle()
{
	if (! controlService.ValidateCommand(CCommands::newFacilityMode))
		return;

	CElementMaintenanceHelper helper;

	helper.RenumberAisle();

	return;

}


void CElementMaintenanceCommands::ReAddPickPath()
{
	if (! controlService.ValidateCommand(CCommands::newFacilityMode))
		return;

	CElementMaintenanceHelper helper;

	helper.ReAddPickPath();

	return;

}

void CElementMaintenanceCommands::ChangeRacktype()
{
	if (! controlService.ValidateCommand(CCommands::newFacilityMode))
		return;

	CElementMaintenanceHelper helper;

	helper.ChangeRacktype();

	return;

}


void CElementMaintenanceCommands::FacilityTreeViewer()
{
	if (! controlService.ValidateCommand(CCommands::newFacilityMode))
		return;

	CElementMaintenanceHelper helper;

	helper.FacilityTreeViewer();

	return;
}

void CElementMaintenanceCommands::ElementMaintenance()
{
	if (! controlService.ValidateCommand(CCommands::newFacilityMode))
		return;


	CElementMaintenanceHelper helper;

	helper.ElementMaintenance();

	return;
}

#if !defined(AFX_SIDEPROPERTIES_H__67698860_AA29_11D4_9212_00400542E36B__INCLUDED_)
#define AFX_SIDEPROPERTIES_H__67698860_AA29_11D4_9212_00400542E36B__INCLUDED_

#if _MSC_VER > 1000
#pragma once
#endif // _MSC_VER > 1000
// SideProperties.h : header file
//
#include "Resource.h"

/////////////////////////////////////////////////////////////////////////////
// CSideProperties dialog

class CSideProperties : public CPropertyPage
{
	DECLARE_DYNCREATE(CSideProperties)

// Construction
public:
	CSideProperties();
	~CSideProperties();

// Dialog Data
	//{{AFX_DATA(CSideProperties)
	enum { IDD = IDD_SIDE_PROPERTIES };
	CString	m_Description;
	//}}AFX_DATA


// Overrides
	// ClassWizard generate virtual function overrides
	//{{AFX_VIRTUAL(CSideProperties)
	protected:
	virtual void DoDataExchange(CDataExchange* pDX);    // DDX/DDV support
	virtual BOOL OnNotify(WPARAM wParam, LPARAM lParam, LRESULT* pResult);
	//}}AFX_VIRTUAL

// Implementation
protected:
	// Generated message map functions
	//{{AFX_MSG(CSideProperties)
	afx_msg BOOL OnHelpInfo(HELPINFO* pHelpInfo);
	//}}AFX_MSG
	DECLARE_MESSAGE_MAP()

};

//{{AFX_INSERT_LOCATION}}
// Microsoft Visual C++ will insert additional declarations immediately before the previous line.

#endif // !defined(AFX_SIDEPROPERTIES_H__67698860_AA29_11D4_9212_00400542E36B__INCLUDED_)

// Facility.h: interface for the CFacility class.
//
//////////////////////////////////////////////////////////////////////

#if !defined(AFX_FACILITY_H__B01BCA38_92A6_40A1_B69E_2B5708201264__INCLUDED_)
#define AFX_FACILITY_H__B01BCA38_92A6_40A1_B69E_2B5708201264__INCLUDED_

#if _MSC_VER > 1000
#pragma once
#endif // _MSC_VER > 1000

#include "FacilityElement.h"
#include "3DPoint.h"
#include "Section.h"

class CFacility : public CFacilityElement  
{
public:
	CFacility();
	virtual ~CFacility();

	int Parse(const CString &line);

	CString m_DrawingName;			// CADFileName
	C3DPoint m_Coordinates;
	int m_UnitOfMeasurement;		// Units: 0 = English; 1 = Metric
	int m_TimeHorizonDuration;		// TimeHorizonValue
	int m_TimeHorizonUnits;			// TimeHorizonUnits: 1 = Day, 2 = Week, 3 = Month, 4 = Year
	double m_Cost;					// Cost
	double m_BaselineCost;			// BaselineCost
	CString m_ClientNameOpened;		// ClientNameOpened
	CString m_Notes;				// Notes

	// New fields
	int m_ProductSetDBID;			// ProductSetId
	int m_BaselineSolutionSetDBID;	// BaselineSetId
	int m_OptimizedSolutionSetDBId;	// OptimizedId

	CTypedPtrArray<CObArray, CSection*> m_SectionArray;
	CMap<long, long, CSection*, CSection*> m_SectionMapById;
	CMap<CString, LPCSTR, CSection*, CSection*> m_SectionMapByName;

	CMap<CString, LPCSTR, CBay*, CBay*> m_BayMapByHandle;
};

#endif // !defined(AFX_FACILITY_H__B01BCA38_92A6_40A1_B69E_2B5708201264__INCLUDED_)

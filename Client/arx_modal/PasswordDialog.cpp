// PasswordDialog.cpp : implementation file
//

#include "stdafx.h"
#include "modal.h"
#include "PasswordDialog.h"

#ifdef _DEBUG
#define new DEBUG_NEW
#undef THIS_FILE
static char THIS_FILE[] = __FILE__;
#endif

/////////////////////////////////////////////////////////////////////////////
// CPasswordDialog dialog


CPasswordDialog::CPasswordDialog(CWnd* pParent /*=NULL*/)
	: CDialog(CPasswordDialog::IDD, pParent)
{
	//{{AFX_DATA_INIT(CPasswordDialog)
	m_Password = _T("");
	m_PasswordMsg = _T("");
	m_ConfirmedPassword = _T("");
	//}}AFX_DATA_INIT
}


void CPasswordDialog::DoDataExchange(CDataExchange* pDX)
{
	CDialog::DoDataExchange(pDX);
	//{{AFX_DATA_MAP(CPasswordDialog)
	DDX_Text(pDX, IDC_PASSWORD, m_Password);
	DDX_Text(pDX, IDC_PASSWORD_MSG, m_PasswordMsg);
	DDX_Text(pDX, IDC_PASSWORD_CONFIRM, m_ConfirmedPassword);
	//}}AFX_DATA_MAP
}


BEGIN_MESSAGE_MAP(CPasswordDialog, CDialog)
	//{{AFX_MSG_MAP(CPasswordDialog)
	//}}AFX_MSG_MAP
END_MESSAGE_MAP()

/////////////////////////////////////////////////////////////////////////////
// CPasswordDialog message handlers

BOOL CPasswordDialog::OnInitDialog() 
{
	CDialog::OnInitDialog();
	
	UINT show = SW_SHOW;
	if (m_Validating)	// validating a previous password vs. creating a new one
		show = SW_HIDE;

	(CEdit *)GetDlgItem(IDC_PASSWORD_CONFIRM)->ShowWindow(show);
	(CStatic *)GetDlgItem(IDC_STATIC_CONFIRM)->ShowWindow(show);

	return TRUE;  // return TRUE unless you set the focus to a control
	              // EXCEPTION: OCX Property Pages should return FALSE
}

void CPasswordDialog::OnOK() 
{	
	UpdateData(TRUE);

	if (! m_Validating) {
		if (m_Password == "") {
			AfxMessageBox("The password must not be blank.");
			CEdit *pEdit = (CEdit *)GetDlgItem(IDC_PASSWORD);
			pEdit->SetSel(0, -1);
			pEdit->SetFocus();
			return;
		}

		if (m_Password != m_ConfirmedPassword) {
			AfxMessageBox("The passwords entered do not match.");
			CEdit *pEdit = (CEdit *)GetDlgItem(IDC_PASSWORD_CONFIRM);
			pEdit->SetSel(0, -1);
			pEdit->SetFocus();
			return;
		}
	}
	else {
		if (m_Password != m_ConfirmedPassword) {
			AfxMessageBox("Incorrect password entered.");
			CEdit *pEdit = (CEdit *)GetDlgItem(IDC_PASSWORD);
			pEdit->SetSel(0,-1);
			pEdit->SetFocus();
			return;
		}
	}

	CDialog::OnOK();
}

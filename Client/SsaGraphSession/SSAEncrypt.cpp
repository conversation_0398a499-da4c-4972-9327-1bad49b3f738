#include "stdafx.h"
#include "..\..\common\core\ssaencrypt.h"


/*
SSAEncrypt::SSAEncrypt(void)
{
	sTemp[0] = '\0';
	ret = NULL;
}

SSAEncrypt::~SSAEncrypt(void)
{
}
*/

LPSTR makeSessionEnSO(LPSTR  sStr) {
	SSAEncrypt ss;
	strcpy(sStr, ss.getSessionEnSO(sStr));
	return sStr;
}

LPSTR SSAEncrypt::getSessionEnSO(LPSTR  sStr)
{
	char cChar;
	char str[100], sPass[100];
	int k, iTemp;

	sTemp[0] = '\0';
	ret = NULL;	
	
	strcpy(str, sStr);
	strcpy(sTemp, "");
	
	for (k = 0; k < (int)strlen(sStr); k++) {
		cChar = str[k];
		iTemp = cChar + 123;
		sprintf(sPass, "%d", iTemp);
		strcat(sTemp, sPass);
	}
	ret = sTemp;
	return ret;
}


LPSTR SSAEncrypt::getSessionDeSO(LPSTR  sStr)
{
	char cChar;
	char str[100], temp[100];
	int k, iLen, iTemp;

	sTemp[0] = '\0';
	ret = NULL;
	
	strcpy(temp, sStr);
		
	iLen=0;
	for (k = 0; k < (int)strlen(temp); k=k+3) {
		str[0]=temp[k];
		str[1]=temp[k+1];
		str[2]=temp[k+2];
		str[3] = '\0';
		iTemp = atoi(str) - 123;
		cChar = iTemp;
		sprintf(str, "%c", iTemp);
		sTemp[iLen++] = cChar;
	}
		
	sTemp[iLen]='\0';	
		
	ret = sTemp;
	//AfxMessageBox(ret);
	return ret;	
}

LPSTR SSAEncrypt::getGetRegi(LPSTR  sStr1, LPSTR  sStr2) {
	HKEY hRegKey;
	static char tempStrValue[2048];
	bool readMe = true;

	sTemp[0] = '\0';
	ret = NULL;

	memset(tempStrValue,0,2048);

	DWORD dwType = REG_SZ;
	DWORD dwReturnLength;
	ret = NULL;
	//"Software\\SSA Global\\Optimize"
			
    if (RegOpenKeyEx(HKEY_LOCAL_MACHINE, sStr1, 0, KEY_READ, &hRegKey) == ERROR_SUCCESS) {
		int numTimes = 0;
		while (RegQueryValueEx(hRegKey, sStr2, NULL, &dwType, (LPBYTE)tempStrValue, &dwReturnLength) != ERROR_SUCCESS && numTimes <= 100)
			numTimes++;

		if (numTimes > 100)
			readMe = FALSE;	

		if (readMe)
			ret = tempStrValue;
	}

	RegCloseKey(hRegKey);
	return ret;
}



LPSTR SSAEncrypt:: guessGraphSO (LPSTR  sStr) {return "123124125126127128129";} 
LPSTR SSAEncrypt:: guessSessionSO (LPSTR  sStr) {return "123124125126127128129";}                                                                               
LPSTR SSAEncrypt:: minGraphSO (LPSTR  sStr) {return "123124125126127128129";}   
LPSTR SSAEncrypt:: minSessionSO (LPSTR  sStr) {return "123124125126127128129";} 
LPSTR SSAEncrypt:: maxGraphSO (LPSTR  sStr) {return "123124125126127128129";}   
LPSTR SSAEncrypt:: maxSessionSO (LPSTR  sStr) {return "123124125126127128129";} 
LPSTR SSAEncrypt:: tuneGraphSO (LPSTR  sStr) {return "123124125126127128129";}  
LPSTR SSAEncrypt:: tuneSessionSO (LPSTR  sStr) {return "123124125126127128129";}
LPSTR SSAEncrypt:: getGraphEnSO (LPSTR  sStr) {return "123124125126127128129";} 
LPSTR SSAEncrypt:: getGraphDeSO (LPSTR  sStr) {return "123124125126127128129";} 
LPSTR SSAEncrypt:: putGraphEnSO (LPSTR  sStr) {return "123124125126127128129";} 
LPSTR SSAEncrypt:: putGraphDeSO (LPSTR  sStr) {return "123124125126127128129";} 
LPSTR SSAEncrypt:: gMakeGraphEnSO (LPSTR  sStr) {return "123124125126127128129";}                                                                               
LPSTR SSAEncrypt:: gMakeGraphDeSO (LPSTR  sStr) {return "123124125126127128129";}                                                                               
LPSTR SSAEncrypt:: pMakeGraphEnSO (LPSTR  sStr) {return "123124125126127128129";}                                                                               
LPSTR SSAEncrypt:: pMakeGraphDeSO (LPSTR  sStr) {return "123124125126127128129";}                                                                               
LPSTR SSAEncrypt:: gMakeSessionEnSO (LPSTR  sStr) {return "123124125126127128129";}                                                                             
LPSTR SSAEncrypt:: gMakeSessionDeSO (LPSTR  sStr) {return "123124125126127128129";}                                                                             
LPSTR SSAEncrypt:: gMakeGetRegi (LPSTR  sStr1, LPSTR  sStr2) {return "123124125126127128129";} 
LPSTR SSAEncrypt:: putGetRegi (LPSTR  sStr1, LPSTR  sStr2) {return "123124125126127128129";}   
LPSTR SSAEncrypt:: getPutRegi (LPSTR  sStr1, LPSTR  sStr2) {return "123124125126127128129";}   
LPSTR SSAEncrypt:: putPutRegi (LPSTR  sStr1, LPSTR  sStr2) {return "123124125126127128129";}   
LPSTR SSAEncrypt:: putSessionEnSO (LPSTR  sStr) {return "123124125126127128129";}                                                                               
LPSTR SSAEncrypt:: putSessionDeSO (LPSTR  sStr) {return "123124125126127128129";}   
LPSTR SSAEncrypt:: pMakeGetRegi (LPSTR  sStr1, LPSTR  sStr2) {return "123124125126127128129";} 
LPSTR SSAEncrypt:: gMakePutRegi (LPSTR  sStr1, LPSTR  sStr2) {return "123124125126127128129";} 
LPSTR SSAEncrypt:: pMakePutRegi (LPSTR  sStr1, LPSTR  sStr2) {return "123124125126127128129";} 
LPSTR SSAEncrypt:: pMakeSessionEnSO (LPSTR  sStr) {return "123124125126127128129";}                                                                             
LPSTR SSAEncrypt:: pMakeSessionDeSO (LPSTR  sStr) {return "123124125126127128129";}                                                                             
LPSTR SSAEncrypt:: pickGraphSO (LPSTR  sStr) {return "123124125126127128129";}  
LPSTR SSAEncrypt:: pickSessionSO (LPSTR  sStr) {return "123124125126127128129";}
LPSTR SSAEncrypt:: routeGraphSO (LPSTR  sStr) {return "123124125126127128129";} 
LPSTR SSAEncrypt:: routeSessionSO (LPSTR  sStr) {return "123124125126127128129";}                                                                               
LPSTR SSAEncrypt:: stopGraphSO (LPSTR  sStr) {return "123124125126127128129";}  
LPSTR SSAEncrypt:: stopSessionSO (LPSTR  sStr) {return "123124125126127128129";}
LPSTR SSAEncrypt:: startGraphSO (LPSTR  sStr) {return "123124125126127128129";} 
LPSTR SSAEncrypt:: startSessionSO (LPSTR  sStr) {return "123124125126127128129";}                                                                               
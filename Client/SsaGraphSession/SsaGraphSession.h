// SsaGraphSession.h : main header file for the SsaGraphSession DLL
//

#pragma once

#ifndef __AFXWIN_H__
	#error include 'stdafx.h' before including this file for PCH
#endif

#include "resource.h"		// main symbols


// CSsaGraphSessionApp
// See SsaGraphSession.cpp for the implementation of this class
//

class CSsaGraphSessionApp : public CWinApp
{
public:
	CSsaGraphSessionApp();

// Overrides
public:
	virtual BOOL InitInstance();

	DECLARE_MESSAGE_MAP()
};

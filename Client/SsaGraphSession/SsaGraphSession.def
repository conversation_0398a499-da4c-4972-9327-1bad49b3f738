; SsaGraphSession.def : Declares the module parameters for the DLL.

LIBRARY      "SsaGraphSession"

EXPORTS
     guessGraphSO
	 guessSessionSO
	 minGraphSO
	 minSessionSO
	 maxGraphSO
	 maxSessionSO
	 tuneGraphSO
	 tuneSessionSO
	 getGraphEnSO
	 getGraphDeSO
	 putGraphEnSO
	 putGraphDeSO
	 gMakeGraphEnSO
	 gMakeGraphDeSO
	 pMakeGraphEnSO
	 pMakeGraphDeSO
	 getSessionEnSO
	 getSessionDeSO
	 makeSessionEnSO
	 getGetRegi
	 gMakeSessionEnSO
	 gMakeSessionDeSO
	 gMakeGetRegi
	 putGetRegi
	 getPutRegi
	 putPutRegi
	 putSessionEnSO
	 putSessionDeSO
	 pMakeGetRegi
	 gMakePutRegi
	 pMakePutRegi
	 pMakeSessionEnSO
	 pMakeSessionDeSO
	 pickGraphSO
	 pickSessionSO
	 routeGraphSO
	 routeSessionSO
	 stopGraphSO
	 stopSessionSO
	 startGraphSO
	 startSessionSO
	
	
	
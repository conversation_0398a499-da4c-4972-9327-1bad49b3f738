#include "StdAfx.h"
#include "SLOTPass1Manager.h"
#include "SrvcObjs.h"
#include "SLOTBay.h"
#include "SLOTSide.h"
#include "SLOTBayProfile.h"
#include "SLOTGroupsToLevels.h"
#include "SLOTPass1ResultsThin.h"
#include "RackUsageClass.h"
#include "DataStream.h"
#include "p1process.h"
// init
SLOTPass1Manager::SLOTPass1Manager(void)
{
}

SLOTPass1Manager::~SLOTPass1Manager(void)
{
}

/* TBR CListstringPtr SLOTPass1Manager::EvaluateHelper(__int32 pFacilityId, string pDatabase, __int32 pUserID, 
					string pClientName, string pOptions, __int32 pMaxResults)
{
	CListCStringPtr pCStringList = Evaluate(pFacilityId, pDatabase, pUserID, 
					pClientName, pOptions.c_str(), pMaxResults);
	CListstringPtr pstringList (new CListstring);
	if (pCStringList.get() != NULL)
	{
		CString theCStr;
		POSITION posCSL = pCStringList->GetHeadPosition();
		for (__int32 i=0; i<pCStringList->GetCount(); i++)
		{
			theCStr = pCStringList->GetNext(posCSL);
			pstringList->AddTail(string((LPCTSTR)theCStr));
		}
	}
	return pstringList;
}
TBR */

// This function starts the execution of Pass 1	(Rack Assignment).
// It is launched as a separate task from SLOTSessionMgr::StartPass1().
// RunPass() will call a sub-method to store the list of RackType
// Assignments for the Products in the database.  The return array,
// aRackCountList, contains statistical data on how many Products were
// assigned to which RackTypes.  This will be displayed to the user on
// screen.
CListCStringPtr SLOTPass1Manager::Evaluate(__int32 pFacilityId, 
										   string pDatabase, 
										   __int32 pUserID, 
										   string pClientName, 
										   CString pOptions, 
										   __int32 pMaxResults)
{
	TimeStamp("Begin Pass1.Evaluate");
	CListCStringPtr aRackCountList (new CListCString);
	try
	{
		aRackCountList = RunPass(pFacilityId, pDatabase, pUserID, pClientName, 
									pOptions, pMaxResults);

		TimeStamp("End Pass1.Evaluate");
		return aRackCountList;
	}
	catch (GenericException *e)
	{	
		gCaughtError(__FILE__, __LINE__, e);
		e->Delete();
		throw;
	}
	catch (CancelException *e)
	{
		gCaughtError(__FILE__, __LINE__, e);
		e->Delete();
		return CListCStringPtr(NULL);
	}
}

void SLOTPass1Manager::EventLoop()
{

}

double SLOTPass1Manager::Max(double value1, double value2)
{
	if (value1 > value2)
		return value1;
	else
		return value2;

}
double SLOTPass1Manager::Min(double value1, double value2)
{
	if (value1 < value2)
		return value1;
	else
		return value2;
}

// Passes product information to the Engine and gets back Racking Assignments.
// Inputs:	pProductPackList:CListCStringPtr,
//			pClientThread:SLOTClientThread
// Outputs:	Returns: LargeArray of SLOTPass1ResultsThin
//			Additional output: pRTUCountList:Array of CString
// This method actually processes the call to the slotting Engine for
// Pass 1.  It receives a list of products and a list of rack types,
// and iteratively calls the slotting engine to process a small portion
// of the product list until all products are processed.
//
// As the product list is processed, the method posts an event on the
// progress of the run.
//
// The actual call to the Engine is pEngineMgr.EvalPass1ProdList().
// A list of Pass1ResultsThin is the actual return value of this method,
// but there is also the output parameter pRackUsageCountList, which is
// summary data on Rack Usage.  The summary data appears on the user's
// screen when Pass 1 finishes.
// #pragma optimize( "agpswy", off )
// #pragma optimize( "agpswy", off )
void SLOTPass1Manager::ProcessEngine(CListCStringPtr &pProductPackList,
				ExternalConnection pConnection, CListCStringPtr &pRTUCountList,
				SLOTFacilityPtr &pFacility, string pDatabase, __int32 pUserID,
                string pClientName, __int32 pMaxResults)
{
	string snewKeyTmp;
	Object *pObjectTmp = NULL;

	TimeStamp("Begin Pass1.ProcessEngine");
	__int32 aNumProductPacks = 0;									// The number of products passed in
	__int32 aNumProductPacksEvaluated = 0;							// Products evaluated so far

	CListCStringPtr aTempProductPackList;							// The big list is broken down into this
	CString aTempText;

	__int32 aStartIndex = 1;
	__int32 aEndIndex = 0;
	// float aPercentDone;								// COMMMENTED - Number sent back to update progress bar


	hashtable <int, Object*, true> ProfileCostHT;
/*	ProfileCostHF : HashFuncs = new(keyType = SP_KT_INTEGER);
	ProfileCostHT.Setup(size = 1000, functions = ProfileCostHF, uniqueKey = true);	*/
	hashtable <string, Object*, false> ProfileFixedHT;
/*	ProfileFixedHF : HashFuncs = new(keyType = SP_KT_STRING);
	ProfileFixedHT.Setup(size = 1000, functions = ProfileFixedHF, uniqueKey = true);	*/
	hashtable <string, Object*, false> ProfileLinealHT;
/*	ProfileLinealHF : HashFuncs = new(keyType = SP_KT_STRING);
	ProfileLinealHT.Setup(size = 1000, functions = ProfileLinealHF, uniqueKey = true);	*/

	CListpObjectPtr ProfileArray;
	SLOTBayProfile aBayProfile;
	float aCost=0.0;
	typDoubleData aCountInt;
	__int32 idx = 0;
	CString newKey;
	bool foundOne=false;

	hashtable <string, Object*, false> ProfileIdealLinHT;
/*	ProfileIdealHF : HashFuncs = new(keyType = SP_KT_STRING);
	ProfileIdealLinHT.Setup(size = 1000, functions = ProfileIdealHF, uniqueKey = true);	*/

	typDoubleData aIdealInt;
	hashtable <string, Object*, false> ProfileIdealCntHT;
/*	ProfileIdealCntHF : HashFuncs = new(keyType = SP_KT_STRING);
	ProfileIdealCntHT.Setup(size = 1000, functions = ProfileIdealCntHF, uniqueKey = true);	*/

	hashtable <string, Object*, false> ProfileMaxGapHT;
/*	ProfileMaxGapHF : HashFuncs = new(keyType = SP_KT_STRING);
	ProfileMaxGapHT.Setup(size = 1000, functions = ProfileMaxGapHF, uniqueKey = true);	*/
	double curGap=0.0;

	// typDoubleData aAvailInt;			// COMMENTED - Unused variable

	CListSLOTPass1ResultsIdealThinPtr aIdealRackArray (new CListSLOTPass1ResultsIdealThin);
	CListSLOTPass1ResultsAvailThinPtr aAvailRackArray (new CListSLOTPass1ResultsAvailThin);
	CListSLOTPass1ResultsProfilesUsedPtr aFacingResArray (new CListSLOTPass1ResultsProfilesUsed);
	CListSLOTPass1RejectionPtr aRejectionArray (new CListSLOTPass1Rejection);
	CListpObjectPtr aObjArray;

	__int32 numNotAvail = 0;

	CListIntegerData IdealBPArray;

	SLOTPass1Summary p1summary;
	CListSLOTPass1SummaryPtr p1summarylist (new CListSLOTPass1Summary);
	CString t;

	__int32 tempOfsset = 0;
	__int32 tempLength = 0;
	float fixedCount;
	float linealCount;

	float tempFloat;
	float tempFloat2;
	__int32 tempInteger = 0;

	// If nothing was passed in, exit.
	if (pProductPackList.get() == NULL)
	{
		return;
	}

	///////////////////////////////////////////////////////////////////////
	// Build Hash Table of Bay Profile Costs
	///////////////////////////////////////////////////////////////////////
	TimeStamp("Begin Pass1.GetDBItemList");
	ProfileArray = SLOTDataMgrSO.GetDBItemList(pDatabase,&aBayProfile,false,true);
	TimeStamp("End Pass1.GetDBItemList");
	///////////////////////////////////////////////////////////////////////
	// all bay profiles in the DB
	///////////////////////////////////////////////////////////////////////
	POSITION pos = ProfileArray->GetHeadPosition();
	Object *nextBayProfile;
	for (int i=0; i<ProfileArray->GetCount(); i++)
	// for nextBayProfile in ProfileArray do
	{
		nextBayProfile = ProfileArray->GetNext(pos);

		///////////////////////////////////////////////////////////////////////
		// Hold the cost of the bay profile
		///////////////////////////////////////////////////////////////////////
		SLOTBayProfile *ptheSLOTBayProfileTmp = (SLOTBayProfile *) nextBayProfile;
		typDoubleData *typDoubleDataTmp = new typDoubleData;
		typDoubleDataTmp->Value	= ( ptheSLOTBayProfileTmp->GetRackCost());
		ProfileCostHT[ptheSLOTBayProfileTmp->GetDBID()] = typDoubleDataTmp;
		// ProfileCostHT.Enter( *pObjectTmp, ptheSLOTBayProfileTmp->GetDBID());
		// ProfileCostHT.Enter(element = DoubleData(value = (SLOTBayProfile)(nextBayProfile).GetRackCost()),
		//									key=(SLOTBayProfile)(nextBayProfile).GetDBID());

		CListSLOTLevelProfile *theSLOTLevelProfileListTmp = ptheSLOTBayProfileTmp->GetLevelProfileList();

		POSITION posSLPL = theSLOTLevelProfileListTmp->GetHeadPosition();
		SLOTLevelProfile aLevelProfile;
		for (int j=0; j<theSLOTLevelProfileListTmp->GetCount(); j++)
		// for aLevelProfile in (SLOTBayProfile)(nextBayProfile).GetLevelProfileList() do
		{
			aLevelProfile = theSLOTLevelProfileListTmp->GetNext(posSLPL);
			fixedCount = 0;
			linealCount = 0;
		
			CListSLOTLocationProfile *theSLOTLocationProfileListTmp = aLevelProfile.GetLocationProfileList();

			POSITION posSLRPL = theSLOTLocationProfileListTmp->GetHeadPosition();
			SLOTLocationProfile aLocProfile;
			for (int k=0; k<theSLOTLocationProfileListTmp->GetCount(); k++)
			// for aLocProfile in aLevelProfile.GetLocationProfileList() do
			{
				aLocProfile = theSLOTLocationProfileListTmp->GetNext(posSLRPL);

				if ( aLocProfile.GetIsSelect() )
				{
					if (aLevelProfile.GetIsVarLocAllowed())
					{
						// brd - added back the location space temporarily since the data passed to the engine includes it
						// eventually, need to use gaps and snaps to determine actual amount
						linealCount = linealCount + 
										(aLocProfile.GetLocationDimension())->GetWidth() + 
										(2*aLocProfile.GetLocationSpace());
					}
					else
					{
						fixedCount = fixedCount + 1;
					}
				}
			}
			///////////////////////////////////////////////////////////////////////
			// for each level type, we need to sum up the variable and fixed facings
			///////////////////////////////////////////////////////////////////////
			newKey = "";
			SLOTBayProfile *p = (SLOTBayProfile *) nextBayProfile;
			newKey.AppendFormat(_T("%d"), p->GetDBID());
			// newKey += (p->GetDBID());
			newKey += ("+");
			newKey.AppendFormat(_T("%d"), aLevelProfile.GetBayType());
			// newKey += (aLevelProfile.GetBayType());
			///////////////////////////////////////////////////////////////////////
			// add to if already there, otherwise start a new one
			///////////////////////////////////////////////////////////////////////
			///////////////////////////////////////////////////////////////////////
			// fixed faces
			///////////////////////////////////////////////////////////////////////
			pObjectTmp = NULL;
			if ( ProfileFixedHT.find( string((LPCTSTR)newKey)) == ProfileFixedHT.end() )
			{
				typDoubleData *typDoubleDataTmp = new typDoubleData;
				typDoubleDataTmp->Value = fixedCount;

				ProfileFixedHT[string((LPCTSTR)newKey)] = typDoubleDataTmp;
				// ProfileFixedHT.Enter( *pObjectTmp, string((LPCTSTR)newKey) );		// CHECK008
			}
			else
			{
				pObjectTmp = ProfileFixedHT.find( string((LPCTSTR)newKey))->second;			
				aIdealInt = *( (typDoubleData *)(pObjectTmp) );

				aIdealInt.Value = aIdealInt.Value + fixedCount;
			}
			///////////////////////////////////////////////////////////////////////
			// variable faces
			///////////////////////////////////////////////////////////////////////
			pObjectTmp = NULL;
			if ( ProfileLinealHT.find( string((LPCTSTR)newKey) ) == ProfileLinealHT.end() )
			{
				typDoubleData *typDoubleDataTmp = new typDoubleData;
				typDoubleDataTmp->Value = linealCount;

				ProfileLinealHT[string((LPCTSTR)newKey)] = typDoubleDataTmp;
				// ProfileLinealHT.Enter(*pObjectTmp, string((LPCTSTR)newKey) );
			}
			else
			{
				pObjectTmp = ProfileLinealHT.find( string((LPCTSTR)newKey) )->second;
				aIdealInt = *( (typDoubleData *)(pObjectTmp) );

				aIdealInt.Value = aIdealInt.Value + linealCount;
			}

			// Variable width gap
			snewKeyTmp = (LPCTSTR)newKey;
			typDoubleData *gap = NULL;
			///gap = (typDoubleData *)(ProfileMaxGapHT.find(snewKeyTmp)->second);
			pObjectTmp = NULL;
			curGap = aLevelProfile.GetProductGap() + aLevelProfile.GetFacingGap();
			if (ProfileMaxGapHT.find(snewKeyTmp) == ProfileMaxGapHT.end())
			{
				snewKeyTmp = (LPCTSTR)newKey;

				typDoubleData *typDoubleDataTmp = new typDoubleData;
				typDoubleDataTmp->Value = curGap;

				ProfileMaxGapHT[snewKeyTmp] = typDoubleDataTmp;
			}
			else
			{
				pObjectTmp = ProfileMaxGapHT.find(snewKeyTmp)->second;
				gap = (typDoubleData *)pObjectTmp;	

				gap->Value = Max(gap->Value, curGap);
			}
		}
	}
	aNumProductPacks = (__int32)pProductPackList->GetCount(); // CHECK1

	log->putline("ProcessEngine: Before Sending Products");


	///////////////////////////////////////////////////////////////////////
	// The actual call to the Engine.
	///////////////////////////////////////////////////////////////////////
	TimeStamp("Begin Pass1.SendProducts");
	SendPass1Products(pProductPackList,
						pConnection,
						aIdealRackArray,
						aAvailRackArray,
						aFacingResArray,
						aRejectionArray,
						pDatabase,
						pUserID);
	TimeStamp("End Pass1.SendProducts");

	///////////////////////////////////////////////////////////////////////
	// close the socket
	///////////////////////////////////////////////////////////////////////
	log->putline("Before StopEnginePass");
	TimeStamp("Begin Pass1.EndPass");
	EndPass(pConnection);	// CHECK1
	TimeStamp("End Pass1.EndPass");

	///////////////////////////////////////////////////////////////////////
	// Now, build the result list to send back to the User
	///////////////////////////////////////////////////////////////////////

	///////////////////////////////////////////////////////////////////////
	// Do the Detail Array
	///////////////////////////////////////////////////////////////////////
	__int32 numRanks = 0;
	bool First = true;

	// POSITION pos;
	pos = aAvailRackArray->GetHeadPosition();
	SLOTPass1ResultsAvailThin anAvailRack;
	for (int i=0; i<aAvailRackArray->GetCount(); i++)
	// for anAvailRack in aAvailRackArray do
	{
		anAvailRack = aAvailRackArray->GetNext(pos);

		if ( anAvailRack.GetRanking() == 1 )
		{
			if ( ! First )
				break;		// from loop
			else
				First = false;
		}
		numRanks = numRanks + 1;
	}

	__int32 maxRecCount = 0;
	pRTUCountList = CListCStringPtr (new CListCString);
	idx = 0;
	if ( aAvailRackArray->GetCount() > pMaxResults )
	{
		aTempText="";
		maxRecCount = pMaxResults / numRanks;
		aTempText += ("Z");
		aTempText += (STREAM_DELIMITER);
		aTempText += ("Too Many Detail Records Returned.");
		aTempText += (STREAM_DELIMITER);
		aTempText += ("  Showing only the first ");
		CString strTemp;
		strTemp.Format(_T("%d"), maxRecCount * numRanks);
        aTempText += strTemp;
		aTempText += (" records.");
		aTempText += (STREAM_DELIMITER);
		aTempText += ("  Please see Capital Cost Report for complete listing.");
		aTempText += (STREAM_DELIMITER);
		pRTUCountList->AddTail(aTempText);
		// pRTUCountList.AppendRow(aTempText);
		log->putline("Added max count message line");
	}
	else
	{
		maxRecCount = (__int32)aAvailRackArray->GetCount();		// CHECK1
	}

	// POSITION pos;
	pos = aAvailRackArray->GetHeadPosition();
	// SLOTPass1ResultsAvailThin anAvailRack;
	for (int i=0; i<aAvailRackArray->GetCount(); i++)
	// for anAvailRack in aAvailRackArray do
	{
		anAvailRack = aAvailRackArray->GetNext(pos);

		aTempText = "";
		if ( anAvailRack.GetRanking() == 1 )
		{
			if ( anAvailRack.GetAvailProfileID() == 0 )
			{
				numNotAvail = numNotAvail + 1;
			}
			idx = idx + 1;
			if ( idx > maxRecCount )
			{
				break; // from loop
			}
		}
		///////////////////////////////////////////////////////////////////////
		// best available - list all rankings
		///////////////////////////////////////////////////////////////////////
		aTempText += ("D");
		aTempText += (STREAM_DELIMITER);
		aTempText += ("B");
		aTempText += (STREAM_DELIMITER);
		CString strTemp;
		strTemp = anAvailRack.GetProductPackDesc().c_str();
		aTempText += (strTemp);
		aTempText += (STREAM_DELIMITER);
		strTemp = anAvailRack.GetAvailWMSProdID().c_str();
		aTempText += (strTemp);
		aTempText += (STREAM_DELIMITER);
		strTemp = anAvailRack.GetAvailWMSProdDetID().c_str();
		aTempText += (strTemp);
		aTempText += (STREAM_DELIMITER);
		strTemp = anAvailRack.GetAvailProfileDescription().c_str();
		aTempText += (strTemp);
		aTempText += (STREAM_DELIMITER);
				switch (anAvailRack.GetAvailBayType())
				{
			 		case 1:
			 			aTempText += ("Bin");
						aTempText += (STREAM_DELIMITER);
			 		case 2:
			 			aTempText += ("Drive-In");
						aTempText += (STREAM_DELIMITER);
			 		case 3:
			 			aTempText += ("Floor");
						aTempText += (STREAM_DELIMITER);
			 		case 4:
			 			aTempText += ("CaseFlow");
						aTempText += (STREAM_DELIMITER);
			 		case 5:
			 			aTempText += ("Pallet");
						aTempText += (STREAM_DELIMITER);
			 		case 6:
			 			aTempText += ("Pick-In-Reserve");
						aTempText += (STREAM_DELIMITER);
			 		case 7:
			 			aTempText += ("Carousel");
						aTempText += (STREAM_DELIMITER);
			 		case 8:
			 			aTempText += ("PalletFlow");
						aTempText += (STREAM_DELIMITER);
					default:
			 			aTempText += ("Not Known");
						aTempText += (STREAM_DELIMITER);
				}
				strTemp.Format(_T("%d"), anAvailRack.GetRanking());
				aTempText += (strTemp);
				aTempText += (STREAM_DELIMITER);
				strTemp.Format(_T("%d"), anAvailRack.GetAvailFacingCount());
				aTempText += (strTemp);
				aTempText += (STREAM_DELIMITER);

				if (anAvailRack.GetOrigHandling() == 3 )
				{
			 		aTempText += ("Pallet");
					aTempText += (STREAM_DELIMITER);
				}
				else
				{
			 		aTempText += ("Case");
					aTempText += (STREAM_DELIMITER);
				}
				 
 				aTempText += (anAvailRack.GetFit());
				aTempText += (STREAM_DELIMITER);

				pRTUCountList->AddTail(aTempText);
				// pRTUCountList.AppendRow(aTempText);

				///////////////////////////////////////////////////////////////////////
				// Do the ideal - One for each rank "1" of the best available
				///////////////////////////////////////////////////////////////////////
				if ( anAvailRack.GetRanking() == 1 )
				{
                    CString strTemp;

					SLOTPass1ResultsIdealThin ptheSLOTPass1ResultsIdealThin = aIdealRackArray->GetAt(aIdealRackArray->FindIndex(idx-1));

					aTempText = "";
					aTempText += ("D");
					aTempText += (STREAM_DELIMITER);
					aTempText += ("I");
					aTempText += (STREAM_DELIMITER);
					strTemp = anAvailRack.GetProductPackDesc().c_str();
					aTempText += (strTemp);
					aTempText += (STREAM_DELIMITER);
					strTemp = ptheSLOTPass1ResultsIdealThin.GetIdealWMSProdID().c_str();
					aTempText += (strTemp);
					aTempText += (STREAM_DELIMITER);
					strTemp = ptheSLOTPass1ResultsIdealThin.GetIdealWMSProdDetID().c_str();
					aTempText += (strTemp);
					aTempText += (STREAM_DELIMITER);
					strTemp = ptheSLOTPass1ResultsIdealThin.GetIdealProfileDescription().c_str();
					aTempText += (strTemp);
					aTempText += (STREAM_DELIMITER);

					switch (ptheSLOTPass1ResultsIdealThin.GetIdealBayType())
					// switch (aIdealRackArray[idx].GetIdealBayType())
					{
				 		case 1:
				 			aTempText += ("Bin");
							aTempText += (STREAM_DELIMITER);
				 		case 2:
				 			aTempText += ("Drive-In");
							aTempText += (STREAM_DELIMITER);
				 		case 3:
				 			aTempText += ("Floor");
							aTempText += (STREAM_DELIMITER);
				 		case 4:
				 			aTempText += ("CaseFlow");
							aTempText += (STREAM_DELIMITER);
				 		case 5:
				 			aTempText += ("Pallet");
							aTempText += (STREAM_DELIMITER);
				 		case 6:
				 			aTempText += ("Pick-In-Reserve");
							aTempText += (STREAM_DELIMITER);
				 		case 7:
				 			aTempText += ("Carousel");
							aTempText += (STREAM_DELIMITER);
						case 8:
				 			aTempText += ("PalletFlow");
							aTempText += (STREAM_DELIMITER);
						default:
				 			aTempText += (" ");
							aTempText += (STREAM_DELIMITER);
					}

					strTemp.Format(_T("%d"), ptheSLOTPass1ResultsIdealThin.GetIdealFacingCount());
					aTempText += strTemp;
					// aTempText += (aIdealRackArray[idx].GetIdealFacingCount());

					aTempText += (STREAM_DELIMITER);
					strTemp.Format(_T("%d"),0);
					aTempText += strTemp;
					aTempText += (STREAM_DELIMITER);
					strTemp.Format(_T("%d"),0);
					aTempText += strTemp;
					aTempText += (STREAM_DELIMITER);
					pRTUCountList->AddTail(aTempText);
					// pRTUCountList.AppendRow(aTempText);
				}
	}

	///////////////////////////////////////////////////////////////////////
	// Next, do the ideal Array Summary
	///////////////////////////////////////////////////////////////////////
	// POSITION pos;
	pos = aIdealRackArray->GetHeadPosition();
	SLOTPass1ResultsIdealThin anIdealRack;
	for (int i=0; i<aIdealRackArray->GetCount(); i++)
	// for anIdealRack in aIdealRackArray do
	{
		anIdealRack = aIdealRackArray->GetNext(pos);

		foundOne = false;

		POSITION posIBPA = IdealBPArray.GetHeadPosition();
		typIntegerData anInt;
		for (int i=0; i<IdealBPArray.GetCount(); i++)
		// for anInt in IdealBPArray do
		{
			anInt = IdealBPArray.GetNext(posIBPA);

			if ( anInt.Value == anIdealRack.GetIdealProfileID() )
			{
				foundOne = true;
				break;
			}
		}
		///////////////////////////////////////////////////////////////////////
		// save the ones that have products assigned to them
		///////////////////////////////////////////////////////////////////////
		if ( !foundOne )
		{
			typIntegerData typIDTmp;
			typIDTmp.Value = anIdealRack.GetIdealProfileID();
			IdealBPArray.AddTail(typIDTmp);
			// IdealBPArray.AppendRow(IntegerData(value=anIdealRack.GetIdealProfileID()));
		}

		///////////////////////////////////////////////////////////////////////
		// Sum up the number of facings for the ideal assignements
		///////////////////////////////////////////////////////////////////////
		///////////////////////////////////////////////////////////////////////
		// key is combination of bay profile and level type
		///////////////////////////////////////////////////////////////////////
		newKey = "";
		CString strTemp;
		strTemp.Format(_T("%d"), anIdealRack.GetIdealProfileID());
		newKey += (strTemp);
		newKey += ("+");
		strTemp.Format(_T("%d"), anIdealRack.GetIdealBayType());
		newKey += (strTemp);

		///////////////////////////////////////////////////////////////////////
		// Variable faces count for Ideal
		///////////////////////////////////////////////////////////////////////

		snewKeyTmp = (LPCTSTR)newKey;
		pObjectTmp = NULL;
		if (ProfileIdealLinHT.find(snewKeyTmp) == ProfileIdealLinHT.end())				// CHECK1
		// if ( aIdealInt = NIL )
		{
			typDoubleData *typDoubleDataTmp = new typDoubleData;
			typDoubleDataTmp->Value = anIdealRack.GetIdealLinealFacing();

			snewKeyTmp = (LPCTSTR)newKey;
			ProfileIdealLinHT[snewKeyTmp] = typDoubleDataTmp;
			// ProfileIdealLinHT.Enter(typDoubleDataTmp, snewKeyTmp);	// CHECK1
		}
		else
		{
			pObjectTmp = ProfileIdealLinHT.find(snewKeyTmp)->second;
			aIdealInt = *( (typDoubleData *)pObjectTmp );

			aIdealInt.Value =  aIdealInt.Value + anIdealRack.GetIdealLinealFacing();
		}

		///////////////////////////////////////////////////////////////////////
		// Fixed faces count for Ideal
		///////////////////////////////////////////////////////////////////////
		snewKeyTmp = (LPCTSTR)newKey;
		pObjectTmp = NULL;

		if ( ProfileIdealCntHT.find(snewKeyTmp) == ProfileIdealCntHT.end() )	// CHECK1
		{
			typDoubleData *typDoubleDataTmp = new typDoubleData;
			typDoubleDataTmp->Value = anIdealRack.GetIdealFacingCount();

			snewKeyTmp = (LPCTSTR)newKey;
			ProfileIdealCntHT[snewKeyTmp] = typDoubleDataTmp;	// CHECK1
			// ProfileIdealCntHT.Enter(typDoubleDataTmp, snewKeyTmp);	// CHECK1
		}
		else
		{
			pObjectTmp = ProfileIdealCntHT.find(snewKeyTmp)->second;
			aIdealInt = *( (typDoubleData *)pObjectTmp );

			aIdealInt.Value = aIdealInt.Value + anIdealRack.GetIdealFacingCount();
		}
	}

	float holdFloat;
	float holdFloat2;
	float holdCount;
	Object *pObjectTmp1;
	///////////////////////////////////////////////////////////////////////
	// Determine Capital Cost of Ideal assignments
	///////////////////////////////////////////////////////////////////////
	POSITION posPA = ProfileArray->GetHeadPosition();
	Object *nextBayProfile2;
	for (int i=0; i<ProfileArray->GetCount(); i++)
	// for nextBayProfile2 in ProfileArray do
	{
		nextBayProfile2 = ProfileArray->GetNext(posPA);

		foundOne = false;
		///////////////////////////////////////////////////////////////////////
		// only show those we have results for
		///////////////////////////////////////////////////////////////////////
		POSITION posIBPA = IdealBPArray.GetHeadPosition();
		typIntegerData anInt;
		for (int i=0; i<IdealBPArray.GetCount(); i++)
 		// for anInt in IdealBPArray do
		{
			anInt = IdealBPArray.GetNext(posIBPA);

			SLOTBayProfile *ptheSLOTBayProfile = (SLOTBayProfile *) nextBayProfile2;
 			if ( anInt.Value == ptheSLOTBayProfile->GetDBID() )
			{
 				foundOne = true;
 				break;
			}
		}
 		if ( foundOne )
		{
 			holdFloat = 0.0;
 			holdFloat2 = 0.0;
 			holdCount = 0.0;

 			///////////////////////////////////////////////////////////////////////
 			// Sum up the cost of the faces assigned to all 8 level types
 			///////////////////////////////////////////////////////////////////////
			for( anInt.Value=1; anInt.Value<=8; anInt.Value++ )
 			// for anInt in 1 to 8 do  // there are 8 bay types
			{
 				newKey = "";

				SLOTBayProfile *ptheSLOTBayProfile = (SLOTBayProfile *) nextBayProfile2;
				CString strTemp;
				strTemp.Format(_T("%d"), ptheSLOTBayProfile->GetDBID());
				newKey += (strTemp);
				newKey += ("+");
				strTemp.Format(_T("%d"), anInt.Value);
				newKey += (strTemp);
 				///////////////////////////////////////////////////////////////////////
 				// key is bayprofile id and level type
 				///////////////////////////////////////////////////////////////////////
				// CHECK start
				snewKeyTmp = (LPCTSTR)newKey;
				pObjectTmp = NULL;
				
				if ( ProfileIdealLinHT.find(snewKeyTmp) != ProfileIdealLinHT.end() )			// CHECK1
 				// if ( aIdealInt != NIL )
				{
					pObjectTmp = ProfileIdealLinHT.find(snewKeyTmp)->second;
					aIdealInt = *( (typDoubleData *)pObjectTmp ); /// This might be unnecessary

					snewKeyTmp = (LPCTSTR)newKey;
					pObjectTmp1 = ProfileIdealCntHT.find(snewKeyTmp)->second;
					aCountInt = *( (typDoubleData *)pObjectTmp1 );

					snewKeyTmp = (LPCTSTR)newKey;
					pObjectTmp1 = ProfileLinealHT.find(snewKeyTmp)->second;
					linealCount = (float) (*( (typDoubleData *)pObjectTmp1 )).Value;

					snewKeyTmp = (LPCTSTR)newKey;
					pObjectTmp1 = ProfileFixedHT.find(snewKeyTmp)->second;
					fixedCount = (float) (*( (typDoubleData *)pObjectTmp1 )).Value;

					ptheSLOTBayProfile = (SLOTBayProfile *) nextBayProfile2;
					pObjectTmp1 = ProfileCostHT.find( ptheSLOTBayProfile->GetDBID() )->second;
					aCost = (float) (*( (typDoubleData *)pObjectTmp1 )).Value;


 					holdCount = holdCount + (float)aCountInt.Value;
 					///////////////////////////////////////////////////////////////////////
 					// all fixed facings
 					///////////////////////////////////////////////////////////////////////	
					if ( linealCount == 0 )     			// all fixed facings
					{
						tempFloat = (float) aCountInt.Value / fixedCount; // CHECK1
						///////////////////////////////////////////////////////////////////////
						// this is done because if we already have more bays assigned from a previous
						// level type than this leveltype says it needs, we take then we still 
						// use the number that are already assigned.
						///////////////////////////////////////////////////////////////////////
						if ( holdFloat > tempFloat )
						{
							tempFloat = holdFloat;
						}
						else
						{
							tempFloat = holdFloat + ( tempFloat - holdFloat );
						}
						
						if ( tempFloat < 1 )
						{
							tempFloat = 1;
						}
						tempInteger = (__int32) tempFloat;		// CHECK1
						if ( tempFloat > tempInteger )
						{
							tempFloat = (float) (tempInteger + 1); // CHECK1
						}
						holdFloat = tempFloat;
						tempFloat2 = 0.0;
					}
					///////////////////////////////////////////////////////////////////////
					// all variable facings
					///////////////////////////////////////////////////////////////////////
					else if ( fixedCount == 0 )      		// all lineal facings
					{
						tempFloat = (float) aIdealInt.Value / linealCount; // CHECK1
						///////////////////////////////////////////////////////////////////////
						// this is done because if we already have more bays assigned from a previous
						// level type than this leveltype says it needs, we take then we still 
						// use the number that are already assigned.
						///////////////////////////////////////////////////////////////////////
						if ( holdFloat > tempFloat )
							tempFloat = holdFloat;
						else
							tempFloat = holdFloat + ( tempFloat - holdFloat );

						if ( tempFloat < 1 )
							tempFloat = 1;

						tempInteger = (__int32) tempFloat; // CHECK1
						if ( tempFloat > tempInteger )
							tempFloat = (float) tempInteger + 1; // CHECK1

						tempFloat2 = 0.0;
						holdFloat = tempFloat;
					}
					///////////////////////////////////////////////////////////////////////
					// mixture : split cost between fixed and variable
					///////////////////////////////////////////////////////////////////////
					else								// mixture of fixed and variable - split cost
					{
						tempFloat =  ( (float)aIdealInt.Value / linealCount ) / 2; // CHECK1
						///////////////////////////////////////////////////////////////////////
						// this is done because if we already have more bays assigned from a previous
						// level type than this leveltype says it needs, we take then we still 
						// use the number that are already assigned.
						///////////////////////////////////////////////////////////////////////
						if ( holdFloat > tempFloat )
						{
							tempFloat = holdFloat;
						}
						else
						{
							tempFloat = holdFloat + ( tempFloat - holdFloat );
						}

						if ( tempFloat < 1 )
						{
							tempFloat = 1;
						}

						tempInteger = (__int32) tempFloat; // CHECK1
						if ( tempFloat > tempInteger )
						{
							tempFloat = (float) tempInteger + 1; // CHECK1
						}

						tempFloat2 = ( (float) aCountInt.Value / fixedCount ) / 2; // CHECK1
						///////////////////////////////////////////////////////////////////////
						// this is done because if we already have more bays assigned from a previous
						// level type than this leveltype says it needs, we take then we still 
						// use the number that are already assigned.
						///////////////////////////////////////////////////////////////////////
						if ( holdFloat2 > tempFloat2 )
						{
							tempFloat2 = holdFloat2;
						}
						else
						{
							tempFloat2 = holdFloat2 + ( tempFloat2 - holdFloat2 );
						}

						if ( tempFloat2 < 1 )
						{
							tempFloat2 = 1;
						}
						tempInteger = (__int32) tempFloat2; // CHECK1
						if ( tempFloat2 > tempInteger )
						{
							tempFloat2 = (float) tempInteger + 1; // CHECK1
						}

						holdFloat = tempFloat;
						holdFloat2 = tempFloat2;
					}
					///////////////////////////////////////////////////////////////////////
					// sum up the cost
					///////////////////////////////////////////////////////////////////////
					aCost = (aCost * tempFloat) + (aCost * tempFloat2);
				}
			}
			CString strTemp;
	 		aTempText = "";
			// p1summary = new;		// 1st time new
			p1summary.SetType(string("I"));
			p1summary.SetCount(holdCount);
			p1summary.SetCost(aCost);
			SLOTBayProfile *ptheSLOTBayProfile = (SLOTBayProfile *) nextBayProfile2;
			p1summary.SetBayProfileDesc(ptheSLOTBayProfile->GetDescription());
			p1summary.SetBayCount(tempFloat+tempFloat2);
			p1summarylist->AddTail(p1summary);
			// p1summarylist.appendrow(p1summary);

			aTempText += ("S");
			aTempText += (STREAM_DELIMITER);
			aTempText += ("I");
			aTempText += (STREAM_DELIMITER);
			//.Concat((SLOTBayProfile)(nextBayProfile2).GetDBID()).Concat(STREAM_DELIMITER)
			ptheSLOTBayProfile = (SLOTBayProfile *) nextBayProfile2;
			strTemp = ptheSLOTBayProfile->GetDescription().c_str();
			aTempText += (strTemp);
			aTempText += (STREAM_DELIMITER);
				strTemp.Format(_T("%.0f"), holdCount);
			aTempText += (strTemp);
			aTempText += (STREAM_DELIMITER);

			strTemp.Format(_T("%.2f"), tempFloat+tempFloat2);
			aTempText += (strTemp);
			aTempText += (STREAM_DELIMITER);
				strTemp.Format(_T("%.2f"), aCost);
			aTempText += (strTemp);
			aTempText += (STREAM_DELIMITER);

			pRTUCountList->AddTail(aTempText);
			// pRTUCountList.AppendRow(aTempText);
		}
	}

	TimeStamp("Begin Pass1.GetAvgProdCaseWidthByFacility");
	float avgProdCaseWidth = SLOTDataMgrSO.GetAvgProdCaseWidthByFacility(pFacility,pDatabase);
	TimeStamp("End Pass1.GetAvgProdCaseWidthByFacility");
	hashtable <int, Object*, true> RackUsageHash;
/*	RackUsageHashFunc : HashFuncs = new(keyType = SP_KT_INTEGER);
	RackUsageHash.Setup(size = 1000, functions = RackUsageHashFunc, uniqueKey = true);	*/

	RackUsageClass *rack = NULL;
	bool brackDynAllocated = false;
	SLOTPass1ResultsProfilesUsed rackData;
	double vwUnitsPerLevelType = 0.0;
	double FixedFacingsPerLevelType = 0.0;
	CString key = "";

	// Calculate the best available summary and the rack usage results
	// aFacingResArray contains the Y|L lines from the engine
	// which represent each bay profile/level type

	POSITION posPSPA = aFacingResArray->GetHeadPosition();
	// SLOTPass1ResultsProfilesUsed rackData;
	for (int i=0; i<aFacingResArray->GetCount(); i++)
	// for rackData in aFacingResArray do
	{
			rackData = aFacingResArray->GetNext(posPSPA);

			/* rackData contains the following fields:
				BayStartLinealFacings - the total number of vw units in the warehouse for this bp/lt (bay profile/level type)
				BayStartFixedFacings - the total number of non-vw (fixed) facings in the warehouse for this bp/lt
				BayAvailLinealFacings - the number of vw units remaining - this may be negative if we ran out
				BayAvailFixedFacings - the number of fixed facings remaining - may be negative
				
			other information we will use
			vwUnitsPerLevelType - the total number of vw units in one bp/lt (stored in a hash table)
			FixedFacingsPerLevelType - the total number of non-vw facings in one bp/lt
			curGap - the maximum facing gap plus product gap for a bp/lt (used to approximate space taken by a vw facing)
			avgProdCaseWidth - the average case width of a product in the facility (used to approximate space taken by a vw facing)
			*/

			// See if the bay profile has already been processed with a different level type
			// CHECK1
			pObjectTmp = NULL;
			if ( RackUsageHash.find(rackData.GetBayProfileID()) == RackUsageHash.end())
			{
				try
				{
					RackUsageClass *r1 = new RackUsageClass;
					rack = new RackUsageClass;
				}
				catch(CException *e)
				{	gCaughtError(__FILE__,__LINE__, e,"Error in RackUsageClass - 1.1");	
				}
				brackDynAllocated = true;

				rack->BayProfileDBID = rackData.GetBayProfileID();
				RackUsageHash[rack->BayProfileDBID] = rack;

				rack->Description = rackData.GetBayProfileDescription().c_str();
				// Since we want the minimum available bays, we have to start this value out high
				rack->AvailBays = 9999999999;
			}
			else
			{
				pObjectTmp = RackUsageHash.find(rackData.GetBayProfileID())->second;		// CHECK1
				rack = (RackUsageClass *) (pObjectTmp) ;
			}

			// Load bp/lt values from the pre-loaded hash tables
			key = "";
			CString strTemp;
			strTemp.Format(_T("%d"), rack->BayProfileDBID);
			key += (strTemp);
			key += ("+");
			strTemp.Format(_T("%d"), rackData.GetBayType());
			key += (strTemp);

			// CHECK1
			string skeyTmp = (LPCTSTR)key;
			pObjectTmp = NULL;
			if (ProfileLinealHT.find(skeyTmp) != ProfileLinealHT.end())
			{
				pObjectTmp = ProfileLinealHT.find(skeyTmp)->second;
				vwUnitsPerLevelType = ( (typDoubleData *)pObjectTmp )->Value;
			}
			if (vwUnitsPerLevelType == 0)
			{
				vwUnitsPerLevelType = 1;
			}

			// CHECK1
			skeyTmp = (LPCTSTR)key;
			pObjectTmp = NULL;
			if (ProfileFixedHT.find(skeyTmp) != ProfileFixedHT.end())
			{
				pObjectTmp = ProfileFixedHT.find(skeyTmp)->second;
				FixedFacingsPerLevelType = ( (typDoubleData *)pObjectTmp )->Value;
			}
			if ( FixedFacingsPerLevelType == 0 )
			{
				FixedFacingsPerLevelType = 1;
			}

			// CHECK1
			skeyTmp = (LPCTSTR)key;
			pObjectTmp = NULL;
			if (ProfileMaxGapHT.find(skeyTmp) != ProfileMaxGapHT.end())
			{
				pObjectTmp = ProfileMaxGapHT.find(skeyTmp)->second;
				curGap = ( (typDoubleData *)pObjectTmp )->Value;
			}
			// For the rack usage report
			if (rackData.GetBayStartLinealFacings() > 0)
			{
				rack->IsVariableWidth = true;
			}

			// add up the start vw and fixed values; we only have to do this because we are combining all the level types into one bay profile line
			rack->StartVWUnits = rack->StartVWUnits + rackData.GetBayStartLinealFacings();
			rack->StartFixedFacings = rack->StartFixedFacings + rackData.GetBayStartFixedFacings();

			// Determine the starting number of bays by using the maximum of the start lineal or fixed facings
			// across each level type and dividing by the number of lineal or fixed per bp/lt
			rack->StartBays = Max(rack->StartBays, RoundUp(rackData.GetBayStartLinealFacings()/vwUnitsPerLevelType));
			rack->StartBays = Max(rack->StartBays, RoundUp(rackData.GetBayStartFixedFacings()/FixedFacingsPerLevelType));

			// If we did not use all of the vw units, subtract the remaining from the starting to get the used
			if (rackData.GetBayAvailLinealFacings() > 0)
			{
				rack->UsedVWUnits = rack->UsedVWUnits + (rackData.GetBayStartLinealFacings() - rackData.GetBayAvailLinealFacings());
				// Calculate the used bays; use the maximum value across the level types	
				rack->UsedBays = Max(rack->UsedBays, RoundUp((rackData.GetBayStartLinealFacings() - rackData.GetBayAvailLinealFacings())/vwUnitsPerLevelType));
			}
			else
			{
				// We ran out of vw units;  set used to the entire starting value; we will set available to a negative value later
				rack->UsedVWUnits = rack->UsedVWUnits + rackData.GetBayStartLinealFacings();
				rack->UsedBays = Max(rack->UsedBays, RoundUp(rackData.GetBayStartLinealFacings()/vwUnitsPerLevelType));
			}

			// If we did not use all of the fixed units, subtract the remaining from the starting to get the used
			if (rackData.GetBayAvailFixedFacings() > 0)
			{
				rack->UsedFixedFacings = rack->UsedFixedFacings + (rackData.GetBayStartFixedFacings() - rackData.GetBayAvailFixedFacings());
				// Use the maximum used bay value across level types
				rack->UsedBays = Max(rack->UsedBays, RoundUp((rackData.GetBayStartFixedFacings() - rackData.GetBayAvailFixedFacings())/FixedFacingsPerLevelType) );
			}
			else
			{
				// We ran out of fixed facings; set used to the starting value; we will set available to negative later
				rack->UsedFixedFacings = rack->UsedFixedFacings + rackData.GetBayStartFixedFacings();
				rack->UsedBays = Max(rack->UsedBays, RoundUp(rackData.GetBayStartFixedFacings()/FixedFacingsPerLevelType));
			}

			// Set the available vw units and fixed facings; these may be negative
			rack->AvailVWUnits = rack->AvailVWUnits + rackData.GetBayAvailLinealFacings();
			rack->AvailFixedFacings = rack->AvailFixedFacings + rackData.GetBayAvailFixedFacings();
			// Calculate the available bays; use the minimum value since they may be negative
			// and we want to err on the side of caution
			if ( (rackData.GetBayStartLinealFacings()) > 0 )
			{
				rack->AvailBays = Min(rack->AvailBays, RoundDown(rackData.GetBayAvailLinealFacings()/vwUnitsPerLevelType));
			}
			
			if (rackData.GetBayStartFixedFacings() > 0)
			{
				rack->AvailBays = Min(rack->AvailBays, RoundDown(rackData.GetBayAvailFixedFacings()/FixedFacingsPerLevelType));
			}

			// Add up the total used facings counting both fixed and lineal
			if (rackData.GetBayAvailLinealFacings() > 0)
			{
				// If we did not used all the vw; subtract what we used from what we started with;
				// then divide by the average product with plus the maximum possible gap values (this is an approximation
				// since we don't know exactly how many gaps and snaps) to convert into facings
				rack->TotalUsedFacings = rack->TotalUsedFacings + RoundUp( (rackData.GetBayStartLinealFacings() - rackData.GetBayAvailLinealFacings()) / (avgProdCaseWidth+curGap));
			}
			else
			{
				// We ran out of vw so set the total used to the start value (converted into facings)
				rack->TotalUsedFacings = rack->TotalUsedFacings + RoundUp(rackData.GetBayStartLinealFacings()/(avgProdCaseWidth+curGap));
				// Calculate the not enough available facings
				rack->TotalNeededFacings = rack->TotalNeededFacings + RoundDown( (0 - rackData.GetBayAvailLinealFacings())/(avgProdCaseWidth+curGap));
			}

			if (rackData.GetBayAvailFixedFacings() > 0 )
			{
				rack->TotalUsedFacings = rack->TotalUsedFacings + (rackData.GetBayStartFixedFacings() - rackData.GetBayAvailFixedFacings());
			}
			else
			{
				rack->TotalUsedFacings = rack->TotalUsedFacings + rackData.GetBayStartFixedFacings();
				rack->TotalNeededFacings = rack->TotalNeededFacings + (0 - rackData.GetBayAvailFixedFacings());
			}

			// Used and needed bays will always be the maximum across level types so set total used to it each time
			rack->TotalUsedBays = rack->UsedBays;
			if ( rack->AvailBays >= 0 )
			{
				rack->TotalNeededBays = 0;
			}
			else
			{
				rack->TotalNeededBays = 0 - rack->AvailBays;
			}

			// the bay cost should be the same across level types so just set it each time
			// CHECK1
			pObjectTmp = NULL;
			if (ProfileCostHT.find(rack->BayProfileDBID) != ProfileCostHT.end())
			{
				pObjectTmp = ProfileCostHT.find(rack->BayProfileDBID)->second;
				rack->BayCost = ( (typDoubleData *)pObjectTmp )->Value;
			}
			if (brackDynAllocated)
			{
		///		delete rack;
				brackDynAllocated = false;
			}
	}

	// Now add result lines for avail summary and rack usage
	// CHECK1

	CListRackUsageClassPtr rackArray (new CListRackUsageClass);
	hashtable <int, Object*, true>::iterator Itr1;
	for (Itr1=RackUsageHash.begin(); Itr1!=RackUsageHash.end( ); Itr1++)
	{
		rackArray->AddTail( *((RackUsageClass*) Itr1->second) );
	}
	// rackArray = (Array of RackUsageClass)(RackUsageHash.FillArray());

	POSITION posRA = rackArray->GetHeadPosition();
	RackUsageClass rack2;
	for (int i=0; i<rackArray->GetCount(); i++)
	// for rack in rackArray do
	{
		rack2 = rackArray->GetNext(posRA);
		// the rack usage line
		aTempText = "";
		// aTempText = new;
		aTempText += ("S");
		aTempText += (STREAM_DELIMITER);
		aTempText += ("R");
		aTempText += (STREAM_DELIMITER);
		aTempText += (rack2.Description);
		aTempText += (STREAM_DELIMITER);
		aTempText += (rack2.IsVariableWidth)? "true" :"false";
		aTempText += (STREAM_DELIMITER);
		CString strTemp;
		strTemp.Format(_T("%.0f"), rack2.StartVWUnits);
		aTempText += (strTemp);
		aTempText += (STREAM_DELIMITER);
		strTemp.Format(_T("%.0f"), rack2.StartFixedFacings);
		aTempText += (strTemp);
		aTempText += (STREAM_DELIMITER);
		strTemp.Format(_T("%.2f"), rack2.StartBays);
		aTempText += (strTemp);
		aTempText += (STREAM_DELIMITER);
		strTemp.Format(_T("%.0f"), rack2.UsedVWUnits);
		aTempText += (strTemp);
		aTempText += (STREAM_DELIMITER);
		strTemp.Format(_T("%.0f"), rack2.UsedFixedFacings);
		aTempText += (strTemp);
		aTempText += (STREAM_DELIMITER);
		strTemp.Format(_T("%.2f"), rack2.UsedBays);
		aTempText += (strTemp);
		aTempText += (STREAM_DELIMITER);
		strTemp.Format(_T("%.0f"), rack2.AvailVWUnits);
		aTempText += (strTemp);
		aTempText += (STREAM_DELIMITER);
		strTemp.Format(_T("%.0f"), rack2.AvailFixedFacings);
		aTempText += (strTemp);
		aTempText += (STREAM_DELIMITER);
		strTemp.Format(_T("%.2f"), rack2.AvailBays);
		aTempText += (strTemp);
		aTempText += (STREAM_DELIMITER);
		pRTUCountList->AddTail(aTempText);
		// pRTUCountList.AppendRow(aTempText);
		
		// the avail summary line
		aTempText = "";
		aTempText += ("S");
		aTempText += (STREAM_DELIMITER);
		aTempText += ("B");
		aTempText += (STREAM_DELIMITER);
		aTempText += (rack2.Description);
		aTempText += (STREAM_DELIMITER);
		strTemp.Format(_T("%.0f"), rack2.TotalUsedFacings);
		aTempText += (strTemp);
		aTempText += (STREAM_DELIMITER);
		strTemp.Format(_T("%.2f"), rack2.TotalUsedBays);
		aTempText += (strTemp);
		aTempText += (STREAM_DELIMITER);
		strTemp.Format(_T("%.2f"), rack2.BayCost * rack2.TotalUsedBays);
		aTempText += (strTemp);
		aTempText += (STREAM_DELIMITER);
		pRTUCountList->AddTail(aTempText);
		// pRTUCountList.AppendRow(aTempText);
		
		SLOTPass1Summary p1summary2;
		// p1summary = new;
		p1summary2.SetType(string("B"));	// CHECK1
		p1summary2.SetCount((float)rack2.TotalUsedFacings);	// CHECK1
		p1summary2.SetCost((float) (rack2.BayCost * rack2.TotalUsedBays) );	// CHECK1
		p1summary2.SetBayProfileDesc(rack2.Description);
		p1summary2.SetBayCount((float)rack2.TotalUsedBays);	// CHECK1
		p1summarylist->AddTail(p1summary2);
		// p1summarylist.appendrow(p1summary2);
			
		// add the not enough available line
		if (rack2.TotalNeededFacings > 0)
		{
			aTempText = "";
			aTempText += ("S");
			aTempText += (STREAM_DELIMITER);
			aTempText += ("B");
			aTempText += (STREAM_DELIMITER);
			aTempText += ("Not Enough Available - ");
			aTempText += (rack2.Description);
			aTempText += (STREAM_DELIMITER);
			strTemp.Format(_T("%.0f"), rack2.TotalNeededFacings);
			aTempText += (strTemp);
			aTempText += (STREAM_DELIMITER);
			strTemp.Format(_T("%.2f"), rack2.TotalNeededBays);
			aTempText += (strTemp);
			aTempText += (STREAM_DELIMITER);
			strTemp.Format(_T("%.2f"), rack2.BayCost * rack2.TotalNeededBays);
			aTempText += (strTemp);
			aTempText += (STREAM_DELIMITER);
			pRTUCountList->AddTail(aTempText);
			// pRTUCountList.AppendRow(aTempText);
			
			SLOTPass1Summary p1summary3;
			// p1Summary = new;
			p1summary3.SetType(string("B"));	// CHECK1
			p1summary3.SetCount((float)rack2.TotalNeededFacings);	// CHECK1
			CString t;
			t += ("Not Enough Available - ");
			t += (rack2.Description);
			p1summary3.SetBayProfileDesc(t);
			p1summary3.SetBayCount((float) rack2.TotalNeededBays);	// CHECK1
			p1summary3.SetCost((float) (rack2.BayCost * rack2.TotalNeededBays));	// CHECK1
			p1summarylist->AddTail(p1summary3);
			// p1summarylist.appendrow(p1summary3);
		}
	}
	
	/*
	aCountFixed : __int32;
	aCountLin : __int32;
	aCountFace : __int32;
	prevProfileID : __int32 = 0;
	prevProfDesc : CString = new();
	twoLines : bool = false;
	prevBayCount : float=0;
	prevBayCount2 : float=0;
	prevFaceCount : float=0;
	prevFaceCount2 : float=0;
	prevCost : float=0;
	prevCost2 : float=0;
	prevFixedFaceStart : __int32 = 0;
	prevVWFaceStart : float = 0;
	prevFixedFaceUsed : __int32 = 0;
	prevVWFaceUsed : float = 0;
	prevFixedAvail : __int32 = 0;
	prevVWAvail : float = 0;
	prevBaysAvailable : float = 0;

	///////////////////////////////////////////////////////////////////////
	// Now the summary of the Best Available.  This is done by going 
	// through all of the Best Available results one at a time, and summing
	// up the faces of all level types of each bay profile.  This depends
	// on the results to be grouped by the bayprofile.  The engine sends them
	// that way back, so it should work fine.  We sum up the faces as we go, 
	// and once we find a new one, we report the summed-up data.
	///////////////////////////////////////////////////////////////////////
	if (aFacingResArray[1] != nil) then
		prevProfDesc.SetValue(aFacingResArray[1].GetBayProfileDescription());
		prevProfileID = aFacingResArray[1].GetBayProfileID();
	else
		prevProfDesc.SetValue("No Rows");
	end if;

	holdFloat = 0.0;
	holdFloat2 = 0.0;
	prevCost2 = 0;
	prevCost = 0;
	prevFaceCount = 0;
	prevFaceCount2 = 0;
	prevBayCount = 0;
	prevBayCount2 = 0;
	prevFixedFaceStart = 0;
	prevVWFaceStart  = 0;
	prevFixedFaceUsed = 0;
	prevVWFaceUsed = 0;
	prevFixedAvail = 0;
	prevVWAvail = 0;



	// to avoid division by 0 later
	if (avgProdCaseWidth == 0) then
		avgProdCaseWidth = 1;
	end if;
	numStartBays : __int32 = 1000;
	boolVWFlag : bool = false;

	for nextFacingRes in aFacingResArray do
		if ( prevProfileID != nextFacingRes.GetBayProfileID() ) then
			///////////////////////////////////////////////////////////////////////
			// print information when we find a new one
			///////////////////////////////////////////////////////////////////////
			if ( prevProfDesc.Value != "" ) then
				p1summary = new;
				p1summary.settype("B");
				p1summary.setcount(prevFaceCount);
				p1summary.setcost(prevCost);
				p1summary.setbayprofiledesc(prevProfDesc.Value);
				p1summary.setbaycount(prevBayCount);
				p1summarylist.appendrow(p1summary);
			
				aTempText = "";
				aTempText += ("S").Concat(STREAM_DELIMITER).Concat("B").Concat(STREAM_DELIMITER)
						.Concat(prevProfDesc).Concat(STREAM_DELIMITER)
						.Concat(prevFaceCount).Concat(STREAM_DELIMITER)
						.Concat(typDoubleData(value=prevBayCount).IntegerValue).Concat(STREAM_DELIMITER)
						.Concat(prevCost).Concat(STREAM_DELIMITER);
				pRTUCountList.AppendRow(aTempText);
				
				aTempText = "";

//				if ( prevFixedAvail <= 0 OR prevVWAvail <= 0 ) then
//					aTempText += ("S").Concat(STREAM_DELIMITER).Concat("R").Concat(STREAM_DELIMITER)
//							.Concat(prevProfDesc).Concat(STREAM_DELIMITER)
//							.Concat(boolVWFlag).Concat(STREAM_DELIMITER)
//							.Concat(prevVWFaceStart/avgProdCaseWidth).Concat(STREAM_DELIMITER)
//							.Concat(prevFixedFaceStart).Concat(STREAM_DELIMITER)
//							.Concat(numStartBays).Concat(STREAM_DELIMITER)
//							.Concat(prevVWFaceUsed/avgProdCaseWidth).Concat(STREAM_DELIMITER)
//							.Concat(prevFixedFaceUsed).Concat(STREAM_DELIMITER)
//							.Concat(typDoubleData(value=prevBayCount).IntegerValue).Concat(STREAM_DELIMITER)
//							.Concat("0").Concat(STREAM_DELIMITER)
//							.Concat("0").Concat(STREAM_DELIMITER)
//							.Concat("0").Concat(STREAM_DELIMITER);
//				else

					aTempText += ("S").Concat(STREAM_DELIMITER).Concat("R").Concat(STREAM_DELIMITER)
							.Concat(prevProfDesc).Concat(STREAM_DELIMITER)
							.Concat(boolVWFlag).Concat(STREAM_DELIMITER)
							.Concat(prevVWFaceStart/avgProdCaseWidth).Concat(STREAM_DELIMITER)
							.Concat(prevFixedFaceStart).Concat(STREAM_DELIMITER)
							.Concat(numStartBays).Concat(STREAM_DELIMITER)
							.Concat(prevVWFaceUsed/avgProdCaseWidth).Concat(STREAM_DELIMITER)
							.Concat(prevFixedFaceUsed).Concat(STREAM_DELIMITER)
							.Concat(typDoubleData(value=prevBayCount).IntegerValue).Concat(STREAM_DELIMITER)
							.Concat(prevVWAvail/avgProdCaseWidth).Concat(STREAM_DELIMITER)
							.Concat(prevFixedAvail).Concat(STREAM_DELIMITER)
							.Concat(prevBaysAvailable).Concat(STREAM_DELIMITER);
							//.Concat(numStartBays-(typDoubleData(value=prevBayCount).IntegerValue)).Concat(STREAM_DELIMITER);
				//end if;
	//			// task.part.logmgr.putline(aTempText);
				pRTUCountList.AppendRow(aTempText);
			end if;

			///////////////////////////////////////////////////////////////////////
			// When we have a "not enough" situation
			///////////////////////////////////////////////////////////////////////
			if ( twoLines ) then
				p1summary = new;
				p1summary.settype("B");
				p1summary.setcount(prevFaceCount2);
				p1summary.setbaycount(prevBayCount2);
				p1summary.setcost(prevCost2);
				t = new;
				t.Concat("Not Enough Available - ").Concat(prevProfDesc);
				p1summary.setbayprofiledesc(t.value);
				p1summarylist.appendrow(p1summary);
			
				aTempText = "";
				aTempText += ("S").Concat(STREAM_DELIMITER).Concat("B").Concat(STREAM_DELIMITER)
						.Concat("Not Enough Available - ").Concat(prevProfDesc).Concat(STREAM_DELIMITER)
						.Concat(prevFaceCount2).Concat(STREAM_DELIMITER)
						.Concat(typDoubleData(value=prevBayCount2).IntegerValue).Concat(STREAM_DELIMITER)
						.Concat(prevCost2).Concat(STREAM_DELIMITER);
				pRTUCountList.AppendRow(aTempText);
			end if;
			
			///////////////////////////////////////////////////////////////////////
			// reinitialize the "hold" values
			///////////////////////////////////////////////////////////////////////
			holdFloat = 0.0;
			holdFloat2 = 0.0;
			prevCost2 = 0;
			prevCost = 0;
			prevFaceCount = 0;
			prevFaceCount2 = 0;
			prevBayCount = 0;
			prevBayCount2 = 0;
			prevFixedFaceStart = 0;
			prevVWFaceStart  = 0;
			prevFixedFaceUsed = 0;
			prevVWFaceUsed = 0;
			prevFixedAvail = 0;
			prevVWAvail = 0;
			prevProfileID = nextFacingRes.GetBayProfileID();
			prevProfDesc = new;
			prevProfDesc.SetValue(nextFacingRes.GetBayProfileDescription());
			boolVWFlag = false;
			prevBaysAvailable = 0;
		end if;

		///////////////////////////////////////////////////////////////////////
		// key for the hash table is profileid and leveltype
		///////////////////////////////////////////////////////////////////////
		newKey = "";
		newKey += (nextFacingRes.GetBayProfileID()).Concat("+").Concat(nextFacingRes.GetBayType());

		///////////////////////////////////////////////////////////////////////
		// Not enough of the bayprofiles
		///////////////////////////////////////////////////////////////////////
		if ( nextFacingRes.GetBayFacingNeededCount() > 0 ) then

			twoLines = true;
			
			// we need more of this type
			aCost = (typDoubleData)(ProfileCostHT.Find(key=nextFacingRes.GetBayProfileID())).Value;

			aTempText=new;

			aCountFace = nextFacingRes.GetTotalFacings();
			//aCountFixed = ( nextFacingRes.GetBayStartFixedFacings() - nextFacingRes.GetBayAvailFixedFacings() );
			// brd - since we know that we have less than we need; set the used bay count to what we have
			aCountFixed = nextFacingRes.GetBayStartFixedFacings();
			
			//aCountLin = ( nextFacingRes.GetBayStartLinealFacings() - nextFacingRes.GetBayAvailLinealFacings() );
			aCountLin = nextFacingRes.GetBayStartLinealFacings();
			
			linealCount = (typDoubleData)(ProfileLinealHT.Find(key=newKey.value)).Value;
			fixedCount = (typDoubleData)(ProfileFixedHT.Find(key=newKey.value)).Value;

			///////////////////////////////////////////////////////////////////////
			// all fixed faces
			///////////////////////////////////////////////////////////////////////
			if ( linealCount == 0 ) then
			
				// brd - these were swapped
				tempFloat = aCountFixed / fixedCount;
				numStartBays = nextFacingRes.GetBayStartFixedFacings() / fixedCount;
				
				if ( tempFloat < 1 ) then
					tempFloat = 1;
				end if; 
				tempInteger = tempFloat;
				if ( tempFloat > tempInteger ) then
					tempFloat = tempInteger + 1;
				end if;

				///////////////////////////////////////////////////////////////////////
				// this is done because if we already have more bays assigned from a previous
				// level type than this leveltype says it needs, we take then we still 
				// use the number that are already assigned.
				///////////////////////////////////////////////////////////////////////
				if ( holdFloat > tempFloat ) then
					tempFloat = holdFloat;
				else
					tempFloat = holdFloat + ( tempFloat - holdFloat );
				end if;

				aCost = aCost * tempFloat;
				holdFloat = tempFloat;
				tempFloat2 = 0.0;
			///////////////////////////////////////////////////////////////////////
			// all variable faces
			///////////////////////////////////////////////////////////////////////
			elseif ( fixedCount = 0 ) then 
			
				// brd - tempFloat and numStartBays were swapped
				tempFloat = aCountLin / LinealCount;
				
				boolVWFlag = true;
				numStartBays = nextFacingRes.GetBayStartLinealFacings() / linealCount;
				
				if ( tempFloat < 1 ) then
					tempFloat = 1;
				end if;
				tempInteger = tempFloat;
				if ( tempFloat > tempInteger ) then
					tempFloat = tempInteger + 1;
				end if;

				///////////////////////////////////////////////////////////////////////
				// this is done because if we already have more bays assigned from a previous
				// level type than this leveltype says it needs, we take then we still 
				// use the number that are already assigned.
				///////////////////////////////////////////////////////////////////////
				if ( holdFloat > tempFloat ) then
					tempFloat = holdFloat;
				else
					tempFloat = holdFloat + ( tempFloat - holdFloat );
				end if;

				aCost = aCost * tempFloat;
				holdFloat = tempFloat;
				tempFloat2 = 0.0;
			///////////////////////////////////////////////////////////////////////
			// mixture : split cost between fixed and variable
			///////////////////////////////////////////////////////////////////////
			else

				tempFloat = (aCountFixed / fixedCount)/2;
				tempFloat2 = (aCountLin / linealCount)/2;
				
				// the starting number of bays should be the same whether
				// we count using fixed or lineal
				numStartBays = (nextFacingRes.GetBayStartFixedFacings()/fixedCount);
				
				boolVWFlag = true;

				tempFloat = tempFloat + tempFloat2;
				if ( tempFloat < 1 ) then
					tempFloat = 1;
				end if;
				tempInteger = tempFloat;
				if ( tempFloat > tempInteger ) then
					tempFloat = tempInteger + 1;
				end if;
							
				///////////////////////////////////////////////////////////////////////
				// this is done because if we already have more bays assigned from a previous
				// level type than this leveltype says it needs, we take then we still 
				// use the number that are already assigned.
				///////////////////////////////////////////////////////////////////////
				if ( holdFloat > tempFloat ) then
					tempFloat = holdFloat;
				else
					tempFloat = holdFloat + ( tempFloat - holdFloat );
				end if;
				holdFloat = tempFloat;
				aCost = (aCost * tempFloat);
				
			end if;
			///////////////////////////////////////////////////////////////////////
			// sum up values
			///////////////////////////////////////////////////////////////////////
			prevCost = aCost;
			prevBayCount = tempFloat;
			prevFaceCount = prevFaceCount + ( aCountFace - nextFacingRes.GetBayFacingNeededCount());
			prevProfDesc = new;
			prevProfDesc = nextFacingRes.GetBayProfileDescription();
					 
			prevFixedFaceStart = prevFixedFaceStart + nextFacingRes.GetBayStartFixedFacings();
			prevVWFaceStart = prevVWFaceStart + nextFacingRes.GetBayStartLinealFacings();
			prevFixedFaceUsed = prevFixedFaceUsed + aCountFixed;
			prevVWFaceUsed = prevVWFaceUsed + aCountLin;
			prevFixedAvail = prevFixedAvail + nextFacingRes.GetBayAvailFixedFacings();
			prevVWAvail = prevVWAvail + nextFacingRes.GetBayAvailLinealFacings();

			aCost = (typDoubleData)(ProfileCostHT.Find(key=nextFacingRes.GetBayProfileID())).Value;
			
			///////////////////////////////////////////////////////////////////////
			// Now get the numbers that were needed but not available
			///////////////////////////////////////////////////////////////////////
			if ( linealCount == 0 ) then

				numStartBays = nextFacingRes.GetBayStartFixedFacings() / fixedCount;

				tempFloat = ( -1*(nextFacingRes.GetBayAvailFixedFacings()) / fixedCount );
				if ( tempFloat < 1 ) then
					tempFloat = 1;
				end if; 
				tempInteger = tempFloat;
				if ( tempFloat > tempInteger ) then
					tempFloat = tempInteger + 1;
				end if;
				///////////////////////////////////////////////////////////////////////
				// this is done because if we already have more bays assigned from a previous
				// level type than this leveltype says it needs, we take then we still 
				// use the number that are already assigned.
				///////////////////////////////////////////////////////////////////////
				if ( holdFloat2 > tempFloat ) then
					tempFloat = holdFloat2;
				else
					tempFloat = holdFloat2 + ( tempFloat - holdFloat2 );
				end if;
				holdFloat2 = tempFloat;
				aCost = aCost * tempFloat;
			///////////////////////////////////////////////////////////////////////
			// all variable faces
			///////////////////////////////////////////////////////////////////////
			elseif ( fixedCount = 0 ) then 

				numStartBays = nextFacingRes.GetBayStartLinealFacings() / LinealCount;

				boolVWFlag = true;
				tempFloat = ( -1*(nextFacingRes.GetBayAvailLinealFacings()) / linealCount);
				if ( tempFloat < 1 ) then
					tempFloat = 1;
				end if;
				tempInteger = tempFloat;
				if ( tempFloat > tempInteger ) then
					tempFloat = tempInteger + 1;
				end if;
				///////////////////////////////////////////////////////////////////////
				// this is done because if we already have more bays assigned from a previous
				// level type than this leveltype says it needs, we take then we still 
				// use the number that are already assigned.
				///////////////////////////////////////////////////////////////////////
				if ( holdFloat2 > tempFloat ) then
					tempFloat = holdFloat2;
				else
					tempFloat = holdFloat2 + ( tempFloat - holdFloat2 );
				end if;
				holdFloat2 = tempFloat;
				aCost = aCost * tempFloat;
			///////////////////////////////////////////////////////////////////////
			// mixture : split cost between fixed and variable
			///////////////////////////////////////////////////////////////////////
			else

				numStartBays = nextFacingRes.GetBayStartFixedFacings() / fixedCount;

				boolVWFlag = true;
				tempFloat =  ( -1*(nextFacingRes.GetBayAvailFixedFacings()) / fixedCount );
				tempFloat2 = -1*(nextFacingRes.GetBayAvailLinealFacings()) / linealCount;

				tempFloat = tempFloat + tempFloat2;
				if ( tempFloat < 1 ) then
					tempFloat = 1;
				end if;
				tempInteger = tempFloat;
				if ( tempFloat > tempInteger ) then
					tempFloat = tempInteger + 1;
				end if;
				///////////////////////////////////////////////////////////////////////
				// this is done because if we already have more bays assigned from a previous
				// level type than this leveltype says it needs, we take then we still 
				// use the number that are already assigned.
				///////////////////////////////////////////////////////////////////////
				if ( holdFloat2 > tempFloat ) then
					tempFloat = holdFloat2;
				else
					tempFloat = holdFloat2 + ( tempFloat - holdFloat2 );
				end if;
				holdFloat2 = tempFloat;
				aCost = (aCost * tempFloat);
			end if;
			// what we need more

			prevCost2 = aCost;
			prevBayCount2 = tempFloat;		// the number of bays needed but not available
			prevFaceCount2 = prevFaceCount2 + nextFacingRes.GetBayFacingNeededCount();

			// prevBayCount2 = facings needed / facings per bay	  26/4 = 7
			prevBaysAvailable = 0 - prevBayCount2;
					
		else		
			///////////////////////////////////////////////////////////////////////
			// We have enough
			///////////////////////////////////////////////////////////////////////
			twoLines = false;
			
			// we used less than or equal to all of this type
			aCost = (typDoubleData)(ProfileCostHT.Find(key=nextFacingRes.GetBayProfileID())).Value;
			
			aTempText=new;

			aCountFace = nextFacingRes.GetTotalFacings(); 
			aCountFixed = ( nextFacingRes.GetBayStartFixedFacings() - nextFacingRes.GetBayAvailFixedFacings() );
			aCountLin = ( nextFacingRes.GetBayStartLinealFacings() - nextFacingRes.GetBayAvailLinealFacings() );

			linealCount = (typDoubleData)(ProfileLinealHT.Find(key=newKey.Value)).Value;
			fixedCount = (typDoubleData)(ProfileFixedHT.Find(key=newKey.Value)).Value;

			///////////////////////////////////////////////////////////////////////
			// all fixed faces
			///////////////////////////////////////////////////////////////////////
			if ( linealCount == 0 ) then

				numStartBays = nextFacingRes.GetBayStartFixedFacings() / fixedCount;

				tempFloat = ( aCountFixed / fixedCount );
				if ( tempFloat < 1 ) then
					tempFloat = 1;
				end if; 
				tempInteger = tempFloat;
				if ( tempFloat > tempInteger ) then
					tempFloat = tempInteger + 1;
				end if;
				///////////////////////////////////////////////////////////////////////
				// this is done because if we already have more bays assigned from a previous
				// level type than this leveltype says it needs, we take then we still 
				// use the number that are already assigned.
				///////////////////////////////////////////////////////////////////////
				if ( holdFloat > tempFloat ) then
					tempFloat = holdFloat;
				else
					tempFloat = holdFloat + ( tempFloat - holdFloat );
				end if;
				holdFloat = tempFloat;
				aCost = aCost * tempFloat;
			///////////////////////////////////////////////////////////////////////
			// all variable faces
			///////////////////////////////////////////////////////////////////////
			elseif ( fixedCount = 0 ) then 

				numStartBays = nextFacingRes.GetBayStartFixedFacings() / LinealCount;

				boolVWFlag = true;
				tempFloat = aCountLin / linealCount;
				if ( tempFloat < 1 ) then
					tempFloat = 1;
				end if;
				tempInteger = tempFloat;
				if ( tempFloat > tempInteger ) then
					tempFloat = tempInteger + 1;
				end if;
				///////////////////////////////////////////////////////////////////////
				// this is done because if we already have more bays assigned from a previous
				// level type than this leveltype says it needs, we take then we still 
				// use the number that are already assigned.
				///////////////////////////////////////////////////////////////////////
				if ( holdFloat > tempFloat ) then
					tempFloat = holdFloat;
				else
					tempFloat = holdFloat + ( tempFloat - holdFloat );
				end if;
				holdFloat = tempFloat;
				aCost = aCost * tempFloat;
			///////////////////////////////////////////////////////////////////////
			// mixture : split cost between fixed and variable
			///////////////////////////////////////////////////////////////////////
			else

				numStartBays = nextFacingRes.GetBayStartFixedFacings() / fixedCount;

				boolVWFlag = true;
				tempFloat =  aCountLin / linealCount;
				tempFloat2 = ( aCountFixed / fixedCount);
				
				tempFloat = tempFloat + tempFloat2;
				
				if ( tempFloat < 1 ) then
					tempFloat = 1;
				end if;
				tempInteger = tempFloat;
				if ( tempFloat > tempInteger ) then
					tempFloat = tempInteger + 1;
				end if;
				///////////////////////////////////////////////////////////////////////
				// this is done because if we already have more bays assigned from a previous
				// level type than this leveltype says it needs, we take then we still 
				// use the number that are already assigned.
				///////////////////////////////////////////////////////////////////////
				if ( holdFloat > tempFloat ) then
					tempFloat = holdFloat;
				else
					tempFloat = holdFloat + ( tempFloat - holdFloat );
				end if;
				holdFloat = tempFloat;			
				aCost = (aCost * tempFloat);
			end if;

			///////////////////////////////////////////////////////////////////////
			// sum up costs
			///////////////////////////////////////////////////////////////////////		
			prevCost = aCost;
			prevBayCount = tempFloat;			// the number of bays used
			prevBaysAvailable = numStartBays - prevBayCount;
			prevFaceCount = prevFaceCount + aCountFace;
			prevProfDesc = new;
			prevProfDesc = nextFacingRes.GetBayProfileDescription();
			
			prevFixedFaceStart = prevFixedFaceStart + nextFacingRes.GetBayStartFixedFacings();
			prevVWFaceStart = prevVWFaceStart + nextFacingRes.GetBayStartLinealFacings();
			prevFixedFaceUsed = prevFixedFaceUsed + aCountFixed;
			prevVWFaceUsed = prevVWFaceUsed + aCountLin;
			prevFixedAvail = prevFixedAvail + nextFacingRes.GetBayAvailFixedFacings();
			prevVWAvail = prevVWAvail + nextFacingRes.GetBayAvailLinealFacings();
		end if;
	end for;
	aTempText = "";

	///////////////////////////////////////////////////////////////////////
	// print information always at the end because we always have one at 
	// the end
	///////////////////////////////////////////////////////////////////////
	if ( prevProfDesc.Value != "" ) then

		p1summary = new;
		p1summary.settype("B");
		p1summary.setcount(prevFaceCount);
		p1summary.setcost(prevCost);
		p1summary.setbayprofiledesc(prevProfDesc.value);
		p1summary.setbaycount(prevBayCount);
		p1summarylist.appendrow(p1summary);
		
		aTempText += ("S").Concat(STREAM_DELIMITER).Concat("B").Concat(STREAM_DELIMITER)
			.Concat(prevProfDesc).Concat(STREAM_DELIMITER)
			.Concat(prevFaceCount).Concat(STREAM_DELIMITER)
			.Concat(typDoubleData(value=prevBayCount).IntegerValue).Concat(STREAM_DELIMITER)
			.Concat(prevCost).Concat(STREAM_DELIMITER);
		pRTUCountList.AppendRow(aTempText);
		aTempText = "";

//		if ( prevFixedAvail <= 0 OR prevVWAvail <= 0 ) then
//			aTempText += ("S").Concat(STREAM_DELIMITER).Concat("R").Concat(STREAM_DELIMITER)
//					.Concat(prevProfDesc).Concat(STREAM_DELIMITER)
//					.Concat(boolVWFlag).Concat(STREAM_DELIMITER)
//					.Concat(prevVWFaceStart/avgProdCaseWidth).Concat(STREAM_DELIMITER)
//					.Concat(prevFixedFaceStart).Concat(STREAM_DELIMITER)
//					.Concat(numStartBays).Concat(STREAM_DELIMITER)
//					.Concat(prevVWFaceUsed/avgProdCaseWidth).Concat(STREAM_DELIMITER)
//					.Concat(prevFixedFaceUsed).Concat(STREAM_DELIMITER)
//					.Concat(typDoubleData(value=prevBayCount).IntegerValue).Concat(STREAM_DELIMITER)
//					.Concat("0").Concat(STREAM_DELIMITER)
//					.Concat("0").Concat(STREAM_DELIMITER)
//					.Concat("0").Concat(STREAM_DELIMITER);
//		else

			aTempText += ("S").Concat(STREAM_DELIMITER).Concat("R").Concat(STREAM_DELIMITER)
					.Concat(prevProfDesc).Concat(STREAM_DELIMITER)
					.Concat(boolVWFlag).Concat(STREAM_DELIMITER)
					.Concat(prevVWFaceStart/avgProdCaseWidth).Concat(STREAM_DELIMITER)
					.Concat(prevFixedFaceStart).Concat(STREAM_DELIMITER)
					.Concat(numStartBays).Concat(STREAM_DELIMITER)
					.Concat(prevVWFaceUsed/avgProdCaseWidth).Concat(STREAM_DELIMITER)
					.Concat(prevFixedFaceUsed).Concat(STREAM_DELIMITER)
					.Concat(typDoubleData(value=prevBayCount).IntegerValue).Concat(STREAM_DELIMITER)
					.Concat(prevVWAvail/avgProdCaseWidth).Concat(STREAM_DELIMITER)
					.Concat(prevFixedAvail).Concat(STREAM_DELIMITER)
					.Concat(prevBaysAvailable).Concat(STREAM_DELIMITER);
		//end if;
	//	// task.part.logmgr.putline(aTempText);
		pRTUCountList.AppendRow(aTempText);
	end if;


	///////////////////////////////////////////////////////////////////////
	// When we have a "not enough" situation
	///////////////////////////////////////////////////////////////////////
	if ( twoLines ) then
		p1summary = new;
		p1summary.settype("B");
		p1summary.setcount(prevFaceCount2);
		p1summary.setbaycount(prevBayCount2);
		p1summary.setcost(prevCost2);
		t = new;
		t.Concat("Not Enough Available - ").Concat(prevProfDesc);
		p1summary.setbayprofiledesc(t.value);
		p1summarylist.appendrow(p1summary);

		aTempText = "";
		aTempText += ("S").Concat(STREAM_DELIMITER).Concat("B").Concat(STREAM_DELIMITER)
				.Concat("Not Enough Available - ").Concat(prevProfDesc).Concat(STREAM_DELIMITER)
				.Concat(prevFaceCount2).Concat(STREAM_DELIMITER)
				.Concat(typDoubleData(value=prevBayCount2).IntegerValue).Concat(STREAM_DELIMITER)
				.Concat(prevCost2).Concat(STREAM_DELIMITER);
		pRTUCountList.AppendRow(aTempText);
	end if;
	*/
	if ( numNotAvail > 0 )
	{
		aTempText = "";
		aTempText += ("S");
		aTempText += (STREAM_DELIMITER);
		aTempText += ("B");
		aTempText += (STREAM_DELIMITER);
		aTempText += ("No Profile Found For Product");
		aTempText += (STREAM_DELIMITER);

		CString strTemp;
		strTemp.Format(_T("%d"), numNotAvail);
		aTempText += (strTemp);
		aTempText += (STREAM_DELIMITER);
		strTemp.Format(_T("%d"), 0);
		aTempText += (strTemp);
		aTempText += (STREAM_DELIMITER);
		strTemp.Format(_T("%d"), 0);
		aTempText += (strTemp);
		aTempText += (STREAM_DELIMITER);
		pRTUCountList->AddTail(aTempText);	
		// pRTUCountList.AppendRow(aTempText);	
		
		SLOTPass1Summary p1summary4;
		// p1summary = new;
		p1summary4.SetType(string("B")); // CHECK1
		p1summary4.SetCount((float)numNotAvail); // CHECK1
		p1summary4.SetCost(0);
		p1summary4.SetBayProfileDesc(string("No Profile Found For Product"));		// CHECK1
		p1summary4.SetBayCount(0);
		p1summarylist->AddTail(p1summary4);
		// p1summarylist.appendrow(p1summary4);
	}

	StoreData(pFacility,aIdealRackArray,aAvailRackArray,p1summarylist, pDatabase, pUserID, aRejectionArray);
	TimeStamp("End Pass1.ProcessEngine");
}

// This method uses the Data Manager to retrieve the Product list to be 
// processed for Pass 1. The query returns raw objects which must be cast
// to SLOTProductPacks.
CListCStringPtr SLOTPass1Manager::RetrieveData(SLOTFacilityPtr &pFacility, string pDatabase)
{
	TimeStamp("Begin Pass1.RetrieveData");
	/// CListCStringPtr tempArray ;
	TimeStamp("End Pass1.RetrieveData");
	return SLOTDataMgrSO.GetP1ProductPackList(pDatabase, pFacility);
}

double SLOTPass1Manager::RoundDown(double value)
{
	__int32 i = (__int32)value;
	
	if (value > 0)
		value = i;
	else
	{
		if (value != i)
			value = i - 1;
	}
	return value;
}
double SLOTPass1Manager::RoundUp(double value)
{
	__int32 i = (__int32)value;

	if (value < 0)
		value = i;
	else
	{
		if (value != i)
			value = i + 1;
	}
	return value;
}

// This executes through the individual steps of the Pass 1 process.
CListCStringPtr SLOTPass1Manager::RunPass(__int32 pFacilityId,
											string pDatabase,
											__int32 pUserID,
											string pClientName,
											CString pOptions,
											__int32 pMaxResults)
{
	bool connectionOpen  = false;
	ExternalConnection aConnection;
	try
	{
		bool connectionOpen  = false;
		///SetVendorNodeName(NodeNameValue);  //code not reqd
		///SetVendorPort(314159);
		///SetPassNum(1);

		SLOTFacilityPtr facility (new SLOTFacility);
		facility->SetDBID(pFacilityId);

		CListpObjectPtr a  = SLOTDataMgrSO.GetDBItemList(pDatabase, facility.get(), false, false);	//CHECK1
		if (a->GetCount() == 0) 
		{
			GenericException *exc= new GenericException();
			CString pFacilityIdTmp;
			pFacilityIdTmp.Format(_T("%d"),pFacilityId);
			CString msg = "SP_ER_USER: Unable to find facility " + pFacilityIdTmp;
			///exc->SetWithParams(SP_ER_USER, "Unable to find facility %1.", IntegerData(Value = pFacilityId));
			exc->SetWithParams(msg);
			throw exc;
		}

		*facility = *((SLOTFacility *) (a->GetAt(a->FindIndex(0))));
		if (pFacilityId == 0)
		{
			GenericException *exc= new GenericException();
			CString msg = "SP_ER_USER: No saved facility is currently open.";
			exc->SetWithParams(msg);
			//exc.SetWithParams(SP_ER_USER, "No saved facility is currently open.");

			throw exc;
			//raise exc;
		}			//CHECK1

		CListCStringPtr aProductPackList(NULL);						// All the Product Packs in the current
																	// Facility, ordered by Extended Cube
																	// (((H x W x L)/1728) x Movement).
																	// Retrieved as TextData.

		CListpObjectPtr theObjectArray(NULL);						// Accepts the return values for queries as
																	// uncast objects.

		CListSLOTP1RackDataPtr theP1RackDataList(NULL);				// Contains data on how much space in the
																	// current Facility is occupied by each Rack
																	// Type.  The Engine will use this information
																	// to decide which Products get which types
																	// as their Ideal type and which have to settle
																	// for the next best.

		CListSLOTP1RackTypeUsagePtr theRackTypeUsageList(NULL);		// These rows represent the real 'rules' for
																	// Pass 1.  They contain values for Extended
																	// Cube and BOH which, compared to the data for
																	// Products, determines ideal racking.

		CListSLOTPass1ResultsThinPtr aPass1ResultsList(NULL);		// Holds an Ideal and a Best Available Rack
																	// Usage for each Product in the Facility.

		CListCStringPtr aCountList (new CListCString);				// The return value - a summary list of basic
																	// Rack Types and the number of each used.
																	
		//ExternalConnection aConnection;

		////////////////////////////////////////////////////////////////////////////
		//
		// Step 1 : Retrieve necessary data.  pProductList is the output parameter.
		// Rack Type data is no longer retrieved via this method.
		//
		////////////////////////////////////////////////////////////////////////////
		TimeStamp("Begin Pass1.RunPass");
		TimeStamp("Begin Pass1.RetrieveData");
		aProductPackList = RetrieveData(facility, pDatabase);
		// quit if no products in facility
		if ((aProductPackList.get() == NULL) || (aProductPackList->GetCount() <= 0)) 
		{
			//aCountList[1] = new;					// Observation: Code not reqd
			//aCountList[1] = 'E|No products found in facility.';
			CString tmpText = "E|No products found in facility.";
			aCountList->AddTail(tmpText);

			return aCountList;
		}

		TimeStamp("End Pass1.RetrieveData");

		////////////////////////////////////////////////////////////////////////////
		//
		// 步骤2: 加载规则 - Pass1算法的规则配置阶段
		//
		// LoadRules() 调用 pEngineMgr.LoadPassRules()，该函数简单地返回
		// aConnection（到引擎的连接）。
		// 这一步骤负责加载Pass1算法运行所需的各种规则和配置参数。
		//
		////////////////////////////////////////////////////////////////////////////

		TimeStamp("Begin Pass1.InitPassToEngine");
		///***-> 我们不再需要建立到引擎的连接。但需要仔细检查
		///***-> 分析引擎是否需要进行某些初始化操作。
		///aConnection = InitPassToEngine();		// 检查：原始代码中注释掉的初始化！
		///connectionOpen = true;
		TimeStamp("End Pass1.InitPassToEngine");

		////////////////////////////////////////////////////////////////////////////
		//
		// 步骤3: 初始化货架类型 - 仅在Pass1中使用
		// 将基本货架类型和货架类型使用情况的数据传递给引擎
		// 进行预处理。
		// 这一步骤建立了Pass1算法所需的货架类型数据基础。
		//
		////////////////////////////////////////////////////////////////////////////

		// Use the Data Manager to retrieve the rows as raw Objects
		log->putline("Before GetDBItemList for Rack Types");
		log->put("Facility = ");
		log->putline(facility->GetDBID());
		log->put("Facility Units = ");
		log->putline(facility->GetUnits());

		TimeStamp("Begin GetP1RackDataList");
		theObjectArray = SLOTDataMgrSO.GetP1RackDataList(pDatabase, facility);
		TimeStamp("End GetP1RackDataList");

		// Convert the objects into P1RackData objects
		//theP1RackDataList = new;

		theP1RackDataList = CListSLOTP1RackDataPtr(new CListSLOTP1RackData);
		//for (aObject in theObjectArray )
		POSITION pos;
		pos = theObjectArray->GetHeadPosition();
		Object *aObject;
		for (__int32 i=0;i<theObjectArray->GetCount();i++)
		{
			aObject = theObjectArray->GetNext(pos);
			SLOTP1RackData *rD = (SLOTP1RackData *) aObject;
			theP1RackDataList->AddTail(*rD);
		}

		// Use the Data Manager to retrieve the rows as raw Objects
		TimeStamp("Begin Pass1.GetP1RackTypeUsageList");
		theObjectArray = SLOTDataMgrSO.GetP1RackTypeUsageList(pDatabase);
		TimeStamp("End Pass1.GetP1RackTypeUsageList");

		// Convert the objects into RackTypeUsage objects
		///theRackTypeUsageList = new;
		theRackTypeUsageList= CListSLOTP1RackTypeUsagePtr(new CListSLOTP1RackTypeUsage);
		pos = theObjectArray->GetHeadPosition();
		for (__int32 i=0;i<theObjectArray->GetCount();i++)
		{
			aObject = theObjectArray->GetNext(pos);
			SLOTP1RackTypeUsage *pRT = (SLOTP1RackTypeUsage *) aObject;
			theRackTypeUsageList->AddTail(*pRT);
		}

		CListpObjectPtr theSlottingGroupList(NULL);
		CListpObjectPtr theGroupsToLevelsList(NULL);
		CListpObjectPtr theBayProfileList(NULL);
		CListpObjectPtr theSideList(NULL);

		SLOTBayProfilePtr aBayProfile (new SLOTBayProfile);
		SLOTBayPtr aBay (new SLOTBay);
		CListpObjectPtr theBayList(NULL);
		SLOTSlottingGroupPtr aSlottingGroup (new SLOTSlottingGroup);
		SLOTGroupsToLevels aGroupToLevel;
		CListCStringPtr theDriveList(new CListCString);
		CString pDriveText;

		TimeStamp("Begin Get Product Groups");

		theSlottingGroupList = SLOTDataMgrSO.GetDBItemListByFacility(pDatabase, facility, *aSlottingGroup);
		
		POSITION posSlotGroup = theSlottingGroupList->GetHeadPosition();
		Object *nextSlottingGroup;
		for (__int32 i=0; i < theSlottingGroupList->GetCount(); i++)
		{
			nextSlottingGroup = theSlottingGroupList->GetNext(posSlotGroup);

			aSlottingGroup = SLOTSlottingGroupPtr((SLOTSlottingGroup *) nextSlottingGroup);
			//SLOTSlottingGroup *pSG = (SLOTSlottingGroup *) nextSlottingGroup;
			//aSlottingGroup = *pSG;

			theGroupsToLevelsList = SLOTDataMgrSO.GetGroupsToLevelsBySlottingGroup(pDatabase, *aSlottingGroup);
		
			POSITION posGL = theGroupsToLevelsList->GetHeadPosition();
			Object *nextGroupToLevel;
			for (__int32 j=0; j < theGroupsToLevelsList->GetCount(); j++)
			{
				nextGroupToLevel = theGroupsToLevelsList->GetNext(posGL);
				aGroupToLevel = *((SLOTGroupsToLevels *) nextGroupToLevel);
				//SLOTGroupsToLevels *pGL = (SLOTGroupsToLevels) nextGroupToLevel;
				//aGroupToLevel = *pGL;

				if ( aGroupToLevel.GetBayID() != 0 ) 
				{
					aBay->SetDBID(aGroupToLevel.GetBayID());
					theBayList = SLOTDataMgrSO.GetDBItemList(pDatabase, aBay.get(), true,false);
					if ( theBayList.get() != NULL ) 
					{
						if (theBayList->GetCount() > 0 ) 
						{
							///aBayProfile.SetDBID((SLOTBay)(theBayList[1]).GetBayProfileID());
							////the above line is implemented by the following three lines of code.
							SLOTBay *theBay;
							theBay = (SLOTBay *) (theBayList->GetAt(theBayList->FindIndex(0)));
							aBayProfile->SetDBID(theBay->GetBayProfileId());
							theBayProfileList = SLOTDataMgrSO.GetDBItemList(pDatabase, aBayProfile.get(), true, false);

							if ( theBayProfileList.get() != NULL ) 
							{
								if ( theBayProfileList->GetCount() > 0 ) 
								{
									pDriveText.Format(_T("%d"), aSlottingGroup->GetDBID());
									pDriveText += STREAM_DELIMITER;
									//pDriveText += (SLOTBayProfile)(theBayProfileList[1]).GetDBID());
									SLOTBayProfile *theBayProfile;
									theBayProfile = (SLOTBayProfile *) (theBayProfileList->GetAt(theBayProfileList->FindIndex(0)));
									pDriveText.AppendFormat(_T("%d"), theBayProfile->GetDBID());
									pDriveText += STREAM_DELIMITER;
									if ( aGroupToLevel.GetIsExclusive() ) 
									{
										pDriveText += "1";
										pDriveText += STREAM_DELIMITER;
									}
									else
									{
										pDriveText += "0";
										pDriveText += STREAM_DELIMITER;
									}
									theDriveList->AddTail(pDriveText);
								}
							}
						}
					}
				}

				else if ( aGroupToLevel.GetSide() != 0 ) 
				{
					theSideList = SLOTDataMgrSO.GetHolderByAisle(pDatabase,"SLOTSide",aGroupToLevel.GetAisleID());
					POSITION posSL = theSideList->GetHeadPosition();
					Object *nextSide;
					for(int k=0; k < theSideList->GetCount(); k++)
					{
						nextSide = theSideList->GetNext(posSL);
						SLOTSide *theSide;
						theSide = (SLOTSide *) (nextSide);
						if ( ( (aGroupToLevel.GetSide() == 1) && (theSide->GetDescription() == "01") ) ||
							 ( (aGroupToLevel.GetSide() == 2) && (theSide->GetDescription() == "02") )
							 )
						{
							theBayProfileList = SLOTDataMgrSO.GetBayListBySide(pDatabase,theSide->GetDBID());

							POSITION posBP = theBayProfileList->GetHeadPosition();
							Object *aBayProfileID;						
							for (__int32 l=0; l < theBayProfileList->GetCount(); l++)
							{
								aBayProfileID = theBayProfileList->GetNext(posBP);
								pDriveText.Format(_T("%d"), aSlottingGroup->GetDBID());
								pDriveText += STREAM_DELIMITER;
								//pDriveText += ((IntegerData)(aBayProfileID));
								typIntegerData *iD = (typIntegerData *)(aBayProfileID);
								pDriveText.AppendFormat(_T("%d"), iD->Value);
								pDriveText += STREAM_DELIMITER;
								if ( aGroupToLevel.GetIsExclusive()) 
								{
									pDriveText += "1";
									pDriveText += STREAM_DELIMITER;
								}
								else
								{
									pDriveText += "0";
									pDriveText += STREAM_DELIMITER;
								}
								theDriveList->AddTail(pDriveText);
							}///end for
						}
					}///end for;
				}
				else if ( aGroupToLevel.GetAisleID() != 0 ) 
				{
					theBayProfileList = SLOTDataMgrSO.GetBayListByAisle(pDatabase, aGroupToLevel.GetAisleID());

					POSITION posBP = theBayProfileList->GetHeadPosition();
					Object *aBayProfileID;
					for (__int32 l=0; l < theBayProfileList->GetCount(); l++)
					{
						aBayProfileID = theBayProfileList->GetNext(posBP);
						pDriveText.Format(_T("%d"), aSlottingGroup->GetDBID());
						pDriveText += STREAM_DELIMITER;
						//pDriveText += (IntegerData)(aBayProfileID);
						typIntegerData *iD = (typIntegerData *)(aBayProfileID);
						pDriveText.AppendFormat(_T("%d"),iD->Value);
						pDriveText += STREAM_DELIMITER;
						if ( aGroupToLevel.GetIsExclusive() ) 
						{
							pDriveText += "1";
							pDriveText += STREAM_DELIMITER;
						}
						else
						{
							pDriveText += "0";
							pDriveText += STREAM_DELIMITER;
						}
						theDriveList->AddTail(pDriveText);
					}///end for
				}
				else if ( aGroupToLevel.GetSectionID() != 0 ) 
				{
					theBayProfileList = SLOTDataMgrSO.GetBayListBySection(pDatabase,aGroupToLevel.GetSectionID());

					POSITION posBP = theBayProfileList->GetHeadPosition();
					Object *aBayProfileID;
					for (__int32 l=0; l < theBayProfileList->GetCount(); l++)
					{
						aBayProfileID = theBayProfileList->GetNext(posBP);
						pDriveText.Format(_T("%d"), aSlottingGroup->GetDBID());
						pDriveText += STREAM_DELIMITER;
						//pDriveText += (IntegerData)(aBayProfileID);
						typIntegerData *iD = (typIntegerData *)(aBayProfileID);
						pDriveText.AppendFormat(_T("%d"),iD->Value);
						pDriveText += STREAM_DELIMITER;
						if ( aGroupToLevel.GetIsExclusive() ) 
						{
							pDriveText += "1";
							pDriveText += STREAM_DELIMITER;
						}
						else
						{
							pDriveText += "0";
							pDriveText += STREAM_DELIMITER;
						}
						theDriveList->AddTail(pDriveText);
					}///end for
				}
			}/// end for

		} /// end for
		TimeStamp("End Get Product Groups");
								

		////////////////////////////////////////////////////////////////////////////
		// InitRackTypes() calls pEngineMgr.InitPass1RackTypes() and InitP1RackTypeUsages(),
		// which actually contain the code that sends the Rack data to the Engine.
		////////////////////////////////////////////////////////////////////////////
		TimeStamp("Begin Pass1.SendPass1BayInformation");
		SendPass1BayInformation(theRackTypeUsageList, 
									theP1RackDataList, 
									aConnection,
									theDriveList,
									pClientName,
									pOptions);
		TimeStamp("End Pass1.SendPass1BayInformation");

		////////////////////////////////////////////////////////////////////////////
		//
		// 步骤4: 调用引擎 - Pass1算法的核心执行阶段
		//
		// ProcessEngine() 调用 pEngineMgr.EvalPass1ProdList()。
		// 该方法的实际返回值是Pass1Results列表，同时还有输出参数
		// pRTUCountList，它包含货架类型使用情况的汇总数据。
		//
		// 这是Pass1算法的核心执行步骤，对每个产品进行：
		// 1. 超级组选择
		// 2. 回归分析和热区调整
		// 3. 货架类型匹配
		// 4. 理想和可用货架类型选择
		//
		////////////////////////////////////////////////////////////////////////////
		TimeStamp("Begin Pass1.ProcessEngine");
		ProcessEngine(	aProductPackList,		// 产品包列表
							aConnection,		// 引擎连接
							aCountList,			// 计数列表
							facility,			// 设施信息
							pDatabase,			// 数据库连接
							pUserID,			// 用户ID
							pClientName,		// 客户端名称
							pMaxResults);		// 最大结果数
		TimeStamp("End Pass1.ProcessEngine");

		////////////////////////////////////////////////////////////////////////////
		//
		// 步骤5: 停止引擎 - Pass1算法的清理阶段
		//
		// 这会向引擎发送流结束协议（通过EngineInterfaceMgr），
		// 告诉引擎停止处理，终止其线程并返回到监听模式。
		// 同时关闭到引擎的连接。
		//
		////////////////////////////////////////////////////////////////////////////
		TimeStamp("End Pass1.RunPass");
		return aCountList;
	}
	catch (GenericException *e)
	{
		gCaughtError(__FILE__, __LINE__, e);
		e->Delete();
		if (connectionOpen)
		{
			aConnection.Close();
		}
		throw e;
	}
}

void SLOTPass1Manager::SendPass1BayInformation(CListSLOTP1RackTypeUsagePtr &pRackTypeUsageList,
		CListSLOTP1RackDataPtr &pRackDataList, ExternalConnection pConnection, CListCStringPtr &pDriveList,
		string pClientName, CString pOptions)
{
	SendPass1Parameters(pConnection,pClientName, pOptions);
	SendPass1Profiles(pRackDataList, pConnection);
	SendPass1ProfileRules(pConnection, pRackTypeUsageList);
	SendPass1ProductGroupDriveParams(pConnection, pDriveList);
}

void SLOTPass1Manager::SendPass1Parameters(ExternalConnection pConnection, 
										   string pClientName, 
										   CString pOptions)
{
	MemoryStream aStream;// : MemoryStream = new;		// What you write to the Connection
	typBinaryDataPtr aFeedData (new typBinaryData);		// What you feed into the Stream
	CString aTempTextData;		// The string you build from the Rack data
	// Work variables
	// __int32 aLength;				// COMMENTED - Unused variable
	
	
	// Start-of-Stream protocol tag
	aTempTextData = "";
	aTempTextData += "<SSO>\n";

	// Loop through the list of Rack Types
	aTempTextData += "L|";
	aTempTextData += pClientName.c_str();
	aTempTextData += STREAM_DELIMITER;
	aTempTextData += pOptions;
	aTempTextData += STREAM_DELIMITER;
	aTempTextData += "\n";

	// End-of-Stream protocol tag
	aTempTextData += "<EOS>";
	aTempTextData += STREAM_DELIMITER;
	aTempTextData += "\n";

	log->putline(aTempTextData);
	ASSERT(aTempTextData != "");
	DataStream *ss = gfnGetDataStream();
	ss->ssData << (char *)(LPCTSTR)aTempTextData;
/***>>>
	stringstream* ss = gfnGetDataStream();
	*ss << (char *)(LPCTSTR)aTempTextData;
	ss->write((LPCTSTR)aTempTextData, aTempTextData.GetLength());
<<<***/
/***>>> socket code... TBR
	// Move text into binary object
	aFeedData->SetValue(aTempTextData);
	aLength = aFeedData->ActualSize;


	// Stream the binary data
	aStream.Open(SP_AM_READ_WRITE);
	aStream.UseData(aFeedData->Value, aLength);
	// Send it
	pConnection.Write(aStream, aLength);
<<<***/
}

void SLOTPass1Manager::SendPass1ProductGroupDriveParams(ExternalConnection pConnection, 
														CListCStringPtr &pDriveList)
{
	MemoryStream aStream;		// What you write to the Connection
	typBinaryDataPtr aFeedData (new typBinaryData);		// What you feed into the Stream
	CString aTempTextData;		// The string you build from the Rack data

	// Work variables
	// __int32 aLength;			// COMMENTED - Unused variable

	// Start-of-Stream protocol tag
	aTempTextData = "";
	aTempTextData += "<SSO>\n";

	// Loop through the list of Rack Types
	aTempTextData += "M|";
	aTempTextData.AppendFormat(_T("%d"), pDriveList->GetCount());
	aTempTextData += STREAM_DELIMITER;
	aTempTextData += "\n";

	POSITION posDL;
	posDL = pDriveList->GetHeadPosition();
	CString aP1DriveInfo;
	for (int i = 0; i < pDriveList->GetCount(); i++)
	{
		aP1DriveInfo = pDriveList->GetNext(posDL);
		aTempTextData += "D";
		aTempTextData += STREAM_DELIMITER;
		aTempTextData += aP1DriveInfo;
		aTempTextData += "\n";
	}

	// End-of-Stream protocol tag
	aTempTextData += "<EOS>";
	aTempTextData += STREAM_DELIMITER;
	aTempTextData += "\n";
	DataStream* ss = gfnGetDataStream();
	ss->ssData << (LPCTSTR)aTempTextData;
/***>>>
	// Move text into binary object
	aFeedData->SetValue(aTempTextData);
	aLength = aFeedData->ActualSize;

	// Stream the binary data
	aStream.Open(SP_AM_READ_WRITE);
	aStream.UseData(aFeedData->Value, aLength);
	//aStream.UseData(data = (pointer to char)(aFeedData.Value), length = aLength);

	// Send it
	pConnection.Write(aStream, aLength);
<<<***/
}

void SLOTPass1Manager::SendPass1Products(CListCStringPtr &pProductPackList, ExternalConnection pConnection,
	CListSLOTPass1ResultsIdealThinPtr &pIdealRackArray, CListSLOTPass1ResultsAvailThinPtr &pAvailRackArray,
	CListSLOTPass1ResultsProfilesUsedPtr &pFacingResArray, CListSLOTPass1RejectionPtr &pRejectionArray,
	string pDatabase, __int32 pUserDBID)
{
	DataStream* ds;
	MemoryStream aStream;							// The object which holds the Product data
													// which we will pass to the connection
	typBinaryDataPtr aFeedData (new typBinaryData);	// A binary version of aTempTextData which
													// can be 'loaded' into aStream
	CString aTempTextData;							// The string we will concatenate to pass
													// to the Engine
																	
	// __int32 aLength;				// COMMENTED - Unused variable
	bool aReadDone;
	CString aResultData="";							// Raw text of Pass 1 solutions from Engine
	CListCStringPtr aResultArray (new CListCString);// A list of raw-text Pass 1 solutions
	__int32 lastOffset;
	CListSLOTPass1ResultsThinPtr aPass1ResultsArray (new CListSLOTPass1ResultsThin);	// A list of Pass 1 solutions
	SLOTPass1ResultsThin aPass1Results;				// An individual Pass 1 solution
	CString aRowType;
	CString aTempType;
	SLOTPass1RejectionPtr aPass1Rejection(NULL);
	__int32 fit;
	log->putline("EvalPass1ProdList:  Start");

	// Step 1 : Construct the product stream

	aTempTextData = "";										// init the string

	/* *************************************************************************** */
	/* Send the 'Start sub list' message to the engine to indicate that we are     */
	/* going to send a round of data, but that there could be more.                */
	/* *************************************************************************** */
	aTempTextData += "<SSL>";
	aTempTextData += "\n";						// 'Start-of-SubList' protocol tag

	TimeStamp("Before the For Loop");

	aTempTextData += "M|";
	aTempTextData.AppendFormat(_T("%d"), pProductPackList->GetCount());
	aTempTextData += STREAM_DELIMITER;
	aTempTextData += "\n";

	//////////////////////////////////////////////////////////////////////
	// For performance, Product information is mapped directly out of the
	// database into text string objects ready to send to the Engine.
	// Each record contains the following data:  DBID, Description, Height,
	// Width, Depth, BalanceOnHand, HazardFlag, UnitOfIssue, PickToBeltFlag,
	// OptimizeByCubeorLabor, Cube and Movement, 
	//////////////////////////////////////////////////////////////////////
	POSITION posPPL;
	posPPL = pProductPackList->GetHeadPosition();
	CString aPack;
	for (int i = 0; i < pProductPackList->GetCount(); i++)
	{
		aPack = pProductPackList->GetNext(posPPL);
		aTempTextData += aPack;
		aTempTextData += "\n";					// Build the line
	}		// All Product Packs

	TimeStamp("After the For Loop");

	/* *************************************************************************** */
	/* Send the 'end sub list' message to the engine to indicate that we are done  */
	/* with this round of data, but that there could be more.                      */
	/* *************************************************************************** */
	aTempTextData += "<ESL>";
	aTempTextData += STREAM_DELIMITER;
	aTempTextData += "\n";	// 'End-of-SubList' tag

	///CHECK999 ***>>>> the following code was not in the original forte code base
	/// Due to the change in the architecture from multi-user to single-user
	/// the concept of sending sublists of products may not be applicable anymore...
	// End-of-Stream protocol tag
	aTempTextData += "<EOS>";
	aTempTextData += STREAM_DELIMITER;
	aTempTextData += "\n";

	//step 2 : Write the stream data to the port
	ds = gfnGetDataStream();
	ds->ssData << (LPCTSTR)aTempTextData;
/**>>> socket code.... TBR... 
	aFeedData->SetValue(aTempTextData);						// First convert text to binary
	aLength = aFeedData->ActualSize;

	log->putline("SCmsg: Opening the aStream");
	aStream.Open(SP_AM_READ_WRITE);						// Open stream and insert data
	aStream.UseData(aFeedData->Value, aLength);
	//aStream.UseData(data=(pointer to char)(aFeedData->Value), aLength);

	log->putline("SCmsg: Writing to the pConnection");

	pConnection.Write(aStream, aLength);		// Send it
*/

/***>>>>>>>>>>>>>> call Pass1Process->Execute here...... <<<<<<<<<<<<<<< ****/
	Pass1Process p(NULL);
	p.Execute();


	//step 3 : Wait for data to come back
	aReadDone = false;												// Flag to exit loop
	aTempTextData = "";
	__int32 aPosTmp;
	char line[MAX_INPUT_DATA_LEN];


	TimeStamp("Before the read while Loop");
	while (!(aReadDone))
	{
		///aLength = 50000;
/***>>> socket code.... TBR... 
		pConnection.Read(aStream, aLength);	// Get some data from the socket
		aStream.ReadText(aTempTextData, aLength);		// Move data into TextData objects
<<<***/
		memset(line, 0, MAX_INPUT_DATA_LEN);
		ds->ssData.getline(line,MAX_INPUT_DATA_LEN);

		aResultData.Append(line);
		aResultData.Append("\n");
		//aResultData.Offset = 0;
		aPosTmp = aResultData.Find("<EOS>");
		if (aPosTmp != -1)
		{
			aReadDone = true;
		}
		 
		//aResultData.Offset = 0;
		aPosTmp = aResultData.Find("Error in Engine");
		if (aPosTmp != -1)
		{
			log->putline("Error in Engine causing return from process engine.");
			GenericException *e = new GenericException();
			e->SetWithParams("SP_ER_USER Error in Engine causing return.");
			throw e;
		}
	}
	TimeStamp("After the read While Loop");

	// Now we break the long string down into a list of TextData objects
	lastOffset = 0;
	__int32 OffsetTmp = 0;
	//aResultData.Offset = 0;
	CString tempstring;
	__int32 templength = aResultData.GetLength();
	OffsetTmp = aResultData.Find("\n");
	while ((OffsetTmp != -1) && (lastOffset < templength))
	{
		tempstring = aResultData.Mid(lastOffset, OffsetTmp - lastOffset);
		aResultArray->AddTail(tempstring);
		lastOffset = OffsetTmp + 1;
		OffsetTmp++;
		OffsetTmp = aResultData.Find("\n", OffsetTmp);
	}
	/*while ( aResultData.MoveToChar('\n') AND lastOffset < templength) do
		tempstring = aResultData.CopyRange(lastOffset,aResultData.Offset);
		aResultArray.AppendRow(tempstring);
		lastOffset = aResultData.Offset + 1;
		aResultData.Offset = aResultData.Offset + 1;
	end while;*/

	TimeStamp("Before the process results loop");
	SLOTPass1ResultsAvailThinPtr aAvailResult (new SLOTPass1ResultsAvailThin);
	SLOTPass1ResultsIdealThinPtr aIdealResult (new SLOTPass1ResultsIdealThin);
	SLOTPass1ResultsProfilesUsedPtr aFacingResult (new SLOTPass1ResultsProfilesUsed);
	__int32 aLineType=0;
	//bool foundOne;						// COMMENTED - Unused variable
	SLOTPass1MessagePtr aPass1Message (new SLOTPass1Message);
	__int32 aProdID=0;

	POSITION posRA;
	posRA = aResultArray->GetHeadPosition();
	CString aTextObj;
	for (int i = 0; i < aResultArray->GetCount(); i++)
	{
	//for aTextObj in aResultArray do
		aTextObj = aResultArray->GetNext(posRA);
		aPosTmp = aTextObj.Find("<EOS>");
		if (aPosTmp != -1)
		{
		//if (aTextObj.MoveToString("<EOS>")) then
			break;
		}
		
		aPosTmp = aTextObj.Find("Y|I|");
		if (aPosTmp != -1)
		{
		//if ( aTextObj.MoveToString("Y|I|") ) then
			aLineType = 1;
		}
		
        //aTextObj.Offset = 0;
		aPosTmp = aTextObj.Find("Y|B|");
		if (aPosTmp != -1)
		{
		//if ( aTextObj.MoveToString("Y|B|") ) then
			aLineType = 2;
		}
		
		//aTextObj.Offset = 0;
		aPosTmp = aTextObj.Find("Y|L|");
		if (aPosTmp != -1)
		{
		//if ( aTextObj.MoveToString("Y|L|") ) then
			aLineType = 3;
		}

		//aTextObj.Offset = 0;
		aPosTmp = aTextObj.Find("Y|M|");
		if (aPosTmp != -1)
		{
		//if ( aTextObj.MoveToString("Y|M|") ) then		// Pass1Message
			aLineType = 4;
		}
		
		//aTextObj.Offset = 0;
		aPosTmp = aTextObj.Find("Y|R|");
		if (aPosTmp != -1)
		{
		//if ( aTextObj.MoveToString("Y|R") ) then		// Pass1Rejection
			aLineType = 5;
		}
		
		//aTextObj.Offset = 0;
		OffsetTmp = 0;
		if (aLineType == 1)
		{
			aIdealResult = SLOTPass1ResultsIdealThinPtr (new SLOTPass1ResultsIdealThin);
			OffsetTmp = aTextObj.Find(STREAM_DELIMITER);
			OffsetTmp++;
			OffsetTmp = aTextObj.Find(STREAM_DELIMITER, OffsetTmp);
			lastOffset = OffsetTmp + 1;
			OffsetTmp++;
			OffsetTmp = aTextObj.Find(STREAM_DELIMITER, OffsetTmp);
			//aTextObj.MoveToChar(STREAM_DELIMITER);
			//aTextObj.Offset = aTextObj.Offset + 1;
			//aTextObj.MoveToChar(STREAM_DELIMITER);
			//lastOffset = aTextObj.Offset + 1;
			//aTextObj.Offset = aTextObj.Offset + 1;
			//aTextObj.MoveToChar(STREAM_DELIMITER);
			
			aIdealResult->SetProductPackID(atoi(aTextObj.Mid(lastOffset, OffsetTmp - lastOffset)));
			//aIdealResult.SetProductPackID(aTextObj.CopyRange(lastOffset, aTextObj.Offset).IntegerValue);
			lastOffset = OffsetTmp + 1;
			OffsetTmp++;
			OffsetTmp = aTextObj.Find(STREAM_DELIMITER, OffsetTmp);
			//lastOffset = aTextObj.Offset + 1;
			//aTextObj.Offset = aTextObj.Offset + 1;
			//aTextObj.MoveToChar(STREAM_DELIMITER);
			
			aIdealResult->SetProductPackDesc(string(aTextObj.Mid(lastOffset, OffsetTmp - lastOffset)));
			//aIdealResult.SetProductPackDesc(aTextObj.CopyRange(lastOffset, aTextObj.Offset).Value);
			lastOffset = OffsetTmp + 1;
			OffsetTmp++;
			OffsetTmp = aTextObj.Find(STREAM_DELIMITER, OffsetTmp);
			//lastOffset = aTextObj.Offset + 1;
			//aTextObj.Offset = aTextObj.Offset + 1;
			//aTextObj.MoveToChar(STREAM_DELIMITER);
			
			aIdealResult->SetIdealFacingCount(atoi(aTextObj.Mid(lastOffset, OffsetTmp - lastOffset)));
			//aIdealResult.SetIdealFacingCount(aTextObj.CopyRange(lastOffset, aTextObj.Offset).IntegerValue);
			lastOffset = OffsetTmp + 1;
			OffsetTmp++;
			OffsetTmp = aTextObj.Find(STREAM_DELIMITER, OffsetTmp);
			//lastOffset = aTextObj.Offset + 1;
			//aTextObj.Offset = aTextObj.Offset + 1;
			//aTextObj.MoveToChar(STREAM_DELIMITER);
			
			aIdealResult->SetIdealProfileID(atoi(aTextObj.Mid(lastOffset, OffsetTmp - lastOffset)));
			//aIdealResult.SetIdealProfileID(aTextObj.CopyRange(lastOffset, aTextObj.Offset).IntegerValue);
			lastOffset = OffsetTmp + 1;
			OffsetTmp++;
			OffsetTmp = aTextObj.Find(STREAM_DELIMITER, OffsetTmp);
			//lastOffset = aTextObj.Offset + 1;
			//aTextObj.Offset = aTextObj.Offset + 1;
			//aTextObj.MoveToChar(STREAM_DELIMITER);
			
			aIdealResult->SetIdealBayType(atoi(aTextObj.Mid(lastOffset, OffsetTmp - lastOffset)));
			//aIdealResult.SetIdealBayType(aTextObj.CopyRange(lastOffset, aTextObj.Offset).IntegerValue);
			lastOffset = OffsetTmp + 1;
			OffsetTmp++;
			OffsetTmp = aTextObj.Find(STREAM_DELIMITER, OffsetTmp);
			//lastOffset = aTextObj.Offset + 1;
			//aTextObj.Offset = aTextObj.Offset + 1;
			//aTextObj.MoveToChar(STREAM_DELIMITER);
			
			aIdealResult->SetIdealProfileDescription(string(aTextObj.Mid(lastOffset, OffsetTmp - lastOffset)));
			//aIdealResult.SetIdealProfileDescription(aTextObj.CopyRange(lastOffset, aTextObj.Offset).Value);
			lastOffset = OffsetTmp + 1;
			OffsetTmp++;
			OffsetTmp = aTextObj.Find(STREAM_DELIMITER, OffsetTmp);
			//lastOffset = aTextObj.Offset + 1;
			//aTextObj.Offset = aTextObj.Offset + 1;
			//aTextObj.MoveToChar(STREAM_DELIMITER);
			
			aIdealResult->SetIdealLinealFacing(atoi(aTextObj.Mid(lastOffset, OffsetTmp - lastOffset)));
			//aIdealResult.SetIdealLinealFacing(aTextObj.CopyRange(lastOffset, aTextObj.Offset).IntegerValue);
			lastOffset = OffsetTmp + 1;
			OffsetTmp++;
			OffsetTmp = aTextObj.Find(STREAM_DELIMITER, OffsetTmp);
			//lastOffset = aTextObj.Offset + 1;
			//aTextObj.Offset = aTextObj.Offset + 1;
			//aTextObj.MoveToChar(STREAM_DELIMITER);
			
			aIdealResult->SetIdealWMSProdID(string(aTextObj.Mid(lastOffset, OffsetTmp - lastOffset)));
			//aIdealResult.SetIdealWMSProdID(aTextObj.CopyRange(lastOffset, aTextObj.Offset).Value);
			lastOffset = OffsetTmp + 1;
			OffsetTmp++;
			OffsetTmp = aTextObj.Find(STREAM_DELIMITER, OffsetTmp);
			//lastOffset = aTextObj.Offset + 1;
			//aTextObj.Offset = aTextObj.Offset + 1;
			//aTextObj.MoveToChar(STREAM_DELIMITER);
			
			aIdealResult->SetIdealWMSProdDetID(string(aTextObj.Mid(lastOffset, OffsetTmp - lastOffset)));
			//aIdealResult.SetIdealWMSProdDetID(aTextObj.CopyRange(lastOffset, aTextObj.Offset).Value);
			lastOffset = OffsetTmp + 1;
			OffsetTmp++;
			//lastOffset = aTextObj.Offset + 1;
			//aTextObj.Offset = aTextObj.Offset + 1;

			pIdealRackArray->AddTail(*aIdealResult);
		}
		else if ( aLineType == 2 )
		{
			aAvailResult = SLOTPass1ResultsAvailThinPtr (new SLOTPass1ResultsAvailThin);
		
			OffsetTmp = aTextObj.Find(STREAM_DELIMITER);
			OffsetTmp++;
			OffsetTmp = aTextObj.Find(STREAM_DELIMITER, OffsetTmp);
			lastOffset = OffsetTmp + 1;
			OffsetTmp++;
			OffsetTmp = aTextObj.Find(STREAM_DELIMITER, OffsetTmp);
			//aTextObj.MoveToChar(STREAM_DELIMITER);
			//aTextObj.Offset = aTextObj.Offset + 1;
			//aTextObj.MoveToChar(STREAM_DELIMITER);
			//lastOffset = aTextObj.Offset + 1;
			//aTextObj.Offset = aTextObj.Offset + 1;
			//aTextObj.MoveToChar(STREAM_DELIMITER);
			
			(atoi(aTextObj.Mid(lastOffset, OffsetTmp - lastOffset)));
			aAvailResult->SetProductPackID(atoi(aTextObj.Mid(lastOffset, OffsetTmp - lastOffset)));
			//aAvailResult.SetProductPackID(aTextObj.CopyRange(lastOffset, aTextObj.Offset).IntegerValue);
			lastOffset = OffsetTmp + 1;
			OffsetTmp++;
			OffsetTmp = aTextObj.Find(STREAM_DELIMITER, OffsetTmp);
			/*lastOffset = aTextObj.Offset + 1;
			aTextObj.Offset = aTextObj.Offset + 1;
			aTextObj.MoveToChar(STREAM_DELIMITER);*/
			
			aAvailResult->SetProductPackDesc(string(aTextObj.Mid(lastOffset, OffsetTmp - lastOffset)));
			lastOffset = OffsetTmp + 1;
			OffsetTmp++;
			OffsetTmp = aTextObj.Find(STREAM_DELIMITER, OffsetTmp);
			//lastOffset = aTextObj.Offset + 1;
			//aTextObj.Offset = aTextObj.Offset + 1;
			//aTextObj.MoveToChar(STREAM_DELIMITER);
			
			aAvailResult->SetAvailFacingCount(atoi(aTextObj.Mid(lastOffset, OffsetTmp - lastOffset)));
			lastOffset = OffsetTmp + 1;
			OffsetTmp++;
			OffsetTmp = aTextObj.Find(STREAM_DELIMITER, OffsetTmp);
			//lastOffset = aTextObj.Offset + 1;
			//aTextObj.Offset = aTextObj.Offset + 1;
			//aTextObj.MoveToChar(STREAM_DELIMITER);
			
			aAvailResult->SetAvailProfileID(atoi(aTextObj.Mid(lastOffset, OffsetTmp - lastOffset)));
			lastOffset = OffsetTmp + 1;
			OffsetTmp++;
			OffsetTmp = aTextObj.Find(STREAM_DELIMITER, OffsetTmp);
			//lastOffset = aTextObj.Offset + 1;
			//aTextObj.Offset = aTextObj.Offset + 1;
			//aTextObj.MoveToChar(STREAM_DELIMITER);
			
			aAvailResult->SetAvailBayType(atoi(aTextObj.Mid(lastOffset, OffsetTmp - lastOffset)));
			lastOffset = OffsetTmp + 1;
			OffsetTmp++;
			OffsetTmp = aTextObj.Find(STREAM_DELIMITER, OffsetTmp);
			//lastOffset = aTextObj.Offset + 1;
			//aTextObj.Offset = aTextObj.Offset + 1;
			//aTextObj.MoveToChar(STREAM_DELIMITER);
			
			aAvailResult->SetAvailLinealFacing(atoi(aTextObj.Mid(lastOffset, OffsetTmp - lastOffset)));
			lastOffset = OffsetTmp + 1;
			OffsetTmp++;
			OffsetTmp = aTextObj.Find(STREAM_DELIMITER, OffsetTmp);
			//lastOffset = aTextObj.Offset + 1;
			//aTextObj.Offset = aTextObj.Offset + 1;
			//aTextObj.MoveToChar(STREAM_DELIMITER);
			
			aAvailResult->SetAvailLinealWidth(atoi(aTextObj.Mid(lastOffset, OffsetTmp - lastOffset)));
			lastOffset = OffsetTmp + 1;
			OffsetTmp++;
			OffsetTmp = aTextObj.Find(STREAM_DELIMITER, OffsetTmp);
			//lastOffset = aTextObj.Offset + 1;
			//aTextObj.Offset = aTextObj.Offset + 1;
			//aTextObj.MoveToChar(STREAM_DELIMITER);
			
			aAvailResult->SetRanking(atoi(aTextObj.Mid(lastOffset, OffsetTmp - lastOffset)));
			lastOffset = OffsetTmp + 1;
			OffsetTmp++;
			OffsetTmp = aTextObj.Find(STREAM_DELIMITER, OffsetTmp);
			//lastOffset = aTextObj.Offset + 1;
			//aTextObj.Offset = aTextObj.Offset + 1;
			//aTextObj.MoveToChar(STREAM_DELIMITER);

			aAvailResult->SetAvailProfileDescription(string(aTextObj.Mid(lastOffset, OffsetTmp - lastOffset)));
			lastOffset = OffsetTmp + 1;
			OffsetTmp++;
			OffsetTmp = aTextObj.Find(STREAM_DELIMITER, OffsetTmp);
			//lastOffset = aTextObj.Offset + 1;
			//aTextObj.Offset = aTextObj.Offset + 1;
			//aTextObj.MoveToChar(STREAM_DELIMITER);

			lastOffset = OffsetTmp + 1;
			OffsetTmp++;
			OffsetTmp = aTextObj.Find(STREAM_DELIMITER, OffsetTmp);
			//lastOffset = aTextObj.Offset + 1;
			//aTextObj.Offset = aTextObj.Offset + 1;
			//aTextObj.MoveToChar(STREAM_DELIMITER);
			
			aAvailResult->SetAvailWMSProdID(string(aTextObj.Mid(lastOffset, OffsetTmp - lastOffset)));
			lastOffset = OffsetTmp + 1;
			OffsetTmp++;
			OffsetTmp = aTextObj.Find(STREAM_DELIMITER, OffsetTmp);
			//lastOffset = aTextObj.Offset + 1;
			//aTextObj.Offset = aTextObj.Offset + 1;
			//aTextObj.MoveToChar(STREAM_DELIMITER);
			
			aAvailResult->SetAvailWMSProdDetID(string(aTextObj.Mid(lastOffset, OffsetTmp - lastOffset)));
			lastOffset = OffsetTmp + 1;
			OffsetTmp++;
			OffsetTmp = aTextObj.Find(STREAM_DELIMITER, OffsetTmp);
			//lastOffset = aTextObj.Offset + 1;
			//aTextObj.Offset = aTextObj.Offset + 1;
			//aTextObj.MoveToChar(STREAM_DELIMITER);
			
			aAvailResult->SetOrigHandling(atoi(aTextObj.Mid(lastOffset, OffsetTmp - lastOffset)));
			lastOffset = OffsetTmp + 1;
			OffsetTmp++;
			OffsetTmp = aTextObj.Find(STREAM_DELIMITER, OffsetTmp);
			//lastOffset = aTextObj.Offset + 1;
			//aTextObj.Offset = aTextObj.Offset + 1;
			//aTextObj.MoveToChar(STREAM_DELIMITER);
			
			aAvailResult->SetActualHandling(atoi(aTextObj.Mid(lastOffset, OffsetTmp - lastOffset)));
			lastOffset = OffsetTmp + 1;
			OffsetTmp++;
			OffsetTmp = aTextObj.Find(STREAM_DELIMITER, OffsetTmp);
			//lastOffset = aTextObj.Offset + 1;
			//aTextObj.Offset = aTextObj.Offset + 1;
			//aTextObj.MoveToChar(STREAM_DELIMITER);
			
			fit = atoi(aTextObj.Mid(lastOffset, OffsetTmp - lastOffset));
			if (fit == 1)
			{
				aAvailResult->SetFit("Yes");
			}
			else if (fit == -1)
			{
				aAvailResult->SetFit("Case");
			}
			else
			{
				aAvailResult->SetFit("No");
			}
			
			lastOffset = OffsetTmp + 1;
			OffsetTmp++;
			//lastOffset = aTextObj.Offset + 1;
			//aTextObj.Offset = aTextObj.Offset + 1;
				
			pAvailRackArray->AddTail(*aAvailResult);
		}

		else if (aLineType == 3)
		{
			aFacingResult = SLOTPass1ResultsProfilesUsedPtr (new SLOTPass1ResultsProfilesUsed);
			
			OffsetTmp = aTextObj.Find(STREAM_DELIMITER);
			OffsetTmp++;
			OffsetTmp = aTextObj.Find(STREAM_DELIMITER, OffsetTmp);
			lastOffset = OffsetTmp + 1;
			OffsetTmp++;
			OffsetTmp = aTextObj.Find(STREAM_DELIMITER, OffsetTmp);
            //aTextObj.MoveToChar(STREAM_DELIMITER);
			//aTextObj.Offset = aTextObj.Offset + 1;
			//aTextObj.MoveToChar(STREAM_DELIMITER);
			//lastOffset = aTextObj.Offset + 1;
			//aTextObj.Offset = aTextObj.Offset + 1;
			//aTextObj.MoveToChar(STREAM_DELIMITER);
			
			aFacingResult->SetBayProfileID(atoi(aTextObj.Mid(lastOffset, OffsetTmp - lastOffset)));
			lastOffset = OffsetTmp + 1;
			OffsetTmp++;
			OffsetTmp = aTextObj.Find(STREAM_DELIMITER, OffsetTmp);
			//lastOffset = aTextObj.Offset + 1;
			//aTextObj.Offset = aTextObj.Offset + 1;
			//aTextObj.MoveToChar(STREAM_DELIMITER);
			
			aFacingResult->SetBayType(atoi(aTextObj.Mid(lastOffset, OffsetTmp - lastOffset)));
			lastOffset = OffsetTmp + 1;
			OffsetTmp++;
			OffsetTmp = aTextObj.Find(STREAM_DELIMITER, OffsetTmp);
			//lastOffset = aTextObj.Offset + 1;
			//aTextObj.Offset = aTextObj.Offset + 1;
			//aTextObj.MoveToChar(STREAM_DELIMITER);
			
			aFacingResult->SetBayProfileDescription(string(aTextObj.Mid(lastOffset, OffsetTmp - lastOffset)));
			lastOffset = OffsetTmp + 1;
			OffsetTmp++;
			OffsetTmp = aTextObj.Find(STREAM_DELIMITER, OffsetTmp);
			//lastOffset = aTextObj.Offset + 1;
			//aTextObj.Offset = aTextObj.Offset + 1;
			//aTextObj.MoveToChar(STREAM_DELIMITER);
			
			aFacingResult->SetBayStartLinealFacings(atoi(aTextObj.Mid(lastOffset, OffsetTmp - lastOffset)));
			lastOffset = OffsetTmp + 1;
			OffsetTmp++;
			OffsetTmp = aTextObj.Find(STREAM_DELIMITER, OffsetTmp);
			//lastOffset = aTextObj.Offset + 1;
			//aTextObj.Offset = aTextObj.Offset + 1;
			//aTextObj.MoveToChar(STREAM_DELIMITER);
			
			aFacingResult->SetBayAvailLinealFacings(atoi(aTextObj.Mid(lastOffset, OffsetTmp - lastOffset)));
			lastOffset = OffsetTmp + 1;
			OffsetTmp++;
			OffsetTmp = aTextObj.Find(STREAM_DELIMITER, OffsetTmp);
			//lastOffset = aTextObj.Offset + 1;
			//aTextObj.Offset = aTextObj.Offset + 1;
			//aTextObj.MoveToChar(STREAM_DELIMITER);
			
			aFacingResult->SetBayStartFixedFacings(atoi(aTextObj.Mid(lastOffset, OffsetTmp - lastOffset)));
			lastOffset = OffsetTmp + 1;
			OffsetTmp++;
			OffsetTmp = aTextObj.Find(STREAM_DELIMITER, OffsetTmp);
			//lastOffset = aTextObj.Offset + 1;
			//aTextObj.Offset = aTextObj.Offset + 1;
			//aTextObj.MoveToChar(STREAM_DELIMITER);
			
			aFacingResult->SetBayAvailFixedFacings(atoi(aTextObj.Mid(lastOffset, OffsetTmp - lastOffset)));
			lastOffset = OffsetTmp + 1;
			OffsetTmp++;
			OffsetTmp = aTextObj.Find(STREAM_DELIMITER, OffsetTmp);
			//lastOffset = aTextObj.Offset + 1;
			//aTextObj.Offset = aTextObj.Offset + 1;
			//aTextObj.MoveToChar(STREAM_DELIMITER);
			
			aFacingResult->SetBayFacingNeededCount(atoi(aTextObj.Mid(lastOffset, OffsetTmp - lastOffset)));
			lastOffset = OffsetTmp + 1;
			OffsetTmp++;
			OffsetTmp = aTextObj.Find(STREAM_DELIMITER, OffsetTmp);
			//lastOffset = aTextObj.Offset + 1;
			//aTextObj.Offset = aTextObj.Offset + 1;
			//aTextObj.MoveToChar(STREAM_DELIMITER);
			
			aFacingResult->SetTotalFacings(atoi(aTextObj.Mid(lastOffset, OffsetTmp - lastOffset)));
			lastOffset = OffsetTmp + 1;
			OffsetTmp++;
			//lastOffset = aTextObj.Offset + 1;
			//aTextObj.Offset = aTextObj.Offset + 1;

			if ((aFacingResult->GetBayStartFixedFacings() != aFacingResult->GetBayAvailFixedFacings())
			|| (aFacingResult->GetBayStartLinealFacings() != aFacingResult->GetBayAvailLinealFacings()))
			{
				pFacingResArray->AddTail(*aFacingResult);
			}
		}
		else if (aLineType == 4)
		{
	// Store the message text from the pass
			OffsetTmp = aTextObj.Find(STREAM_DELIMITER);
			OffsetTmp++;
			OffsetTmp = aTextObj.Find(STREAM_DELIMITER, OffsetTmp);
			lastOffset = OffsetTmp + 1;
			OffsetTmp++;
			OffsetTmp = aTextObj.Find(STREAM_DELIMITER, OffsetTmp);
			//aTextObj.MoveToChar(STREAM_DELIMITER);
			//aTextObj.Offset = aTextObj.Offset + 1;
			//aTextObj.MoveToChar(STREAM_DELIMITER);
			//lastOffset = aTextObj.Offset + 1;
			//aTextObj.Offset = aTextObj.Offset + 1;
			//aTextObj.MoveToChar(STREAM_DELIMITER);
			
			aProdID = atoi(aTextObj.Mid(lastOffset, OffsetTmp - lastOffset));
			lastOffset = OffsetTmp + 1;
			OffsetTmp++;
			OffsetTmp = aTextObj.Find(STREAM_DELIMITER, OffsetTmp);
			//lastOffset = aTextObj.Offset + 1;
			//aTextObj.Offset = aTextObj.Offset + 1;
			//aTextObj.MoveToChar(STREAM_DELIMITER);

			// MFS 01Mar06 The string cast wasn't working as written.
			string tempString = (string) aTextObj.Mid(lastOffset, OffsetTmp - lastOffset);
			aPass1Message->SetText(tempString);
			// aPass1Message->SetText(string(aTextObj.Mid(lastOffset, OffsetTmp - lastOffset)));

			lastOffset = OffsetTmp + 1;
			OffsetTmp++;
			//lastOffset = aTextObj.Offset + 1;
			//aTextObj.Offset = aTextObj.Offset + 1;
		}
		else if ( aLineType == 5 )
		{
			aPass1Rejection = SLOTPass1RejectionPtr (new SLOTPass1Rejection);
		
			OffsetTmp = aTextObj.Find(STREAM_DELIMITER);
			OffsetTmp++;
			OffsetTmp = aTextObj.Find(STREAM_DELIMITER, OffsetTmp);
			lastOffset = OffsetTmp + 1;
			OffsetTmp++;
			OffsetTmp = aTextObj.Find(STREAM_DELIMITER, OffsetTmp);
			//aTextObj.MoveToChar(STREAM_DELIMITER);
			//aTextObj.Offset = aTextObj.Offset + 1;
			//aTextObj.MoveToChar(STREAM_DELIMITER);
			//lastOffset = aTextObj.Offset + 1;
			//aTextObj.Offset = aTextObj.Offset + 1;
			//aTextObj.MoveToChar(STREAM_DELIMITER);
			
			aPass1Rejection->SetType(aTextObj.Mid(lastOffset, OffsetTmp - lastOffset));
			lastOffset = OffsetTmp + 1;
			OffsetTmp++;
			OffsetTmp = aTextObj.Find(STREAM_DELIMITER, OffsetTmp);
			//lastOffset = aTextObj.Offset + 1;
			//aTextObj.Offset = aTextObj.Offset + 1;
			//aTextObj.MoveToChar(STREAM_DELIMITER);
			
			aPass1Rejection->SetLevelType(atoi(aTextObj.Mid(lastOffset, OffsetTmp - lastOffset)));
			lastOffset = OffsetTmp + 1;
			OffsetTmp++;
			OffsetTmp = aTextObj.Find(STREAM_DELIMITER, OffsetTmp);
			//lastOffset = aTextObj.Offset + 1;
			//aTextObj.Offset = aTextObj.Offset + 1;
			//aTextObj.MoveToChar(STREAM_DELIMITER);
			
			aPass1Rejection->SetProductPackID(atoi(aTextObj.Mid(lastOffset, OffsetTmp - lastOffset)));
			lastOffset = OffsetTmp + 1;
			OffsetTmp++;
			OffsetTmp = aTextObj.Find(STREAM_DELIMITER, OffsetTmp);
			//lastOffset = aTextObj.Offset + 1;
			//aTextObj.Offset = aTextObj.Offset + 1;
			//aTextObj.MoveToChar(STREAM_DELIMITER);
			
			aPass1Rejection->SetBayProfileID(atoi(aTextObj.Mid(lastOffset, OffsetTmp - lastOffset)));
			lastOffset = OffsetTmp + 1;
			OffsetTmp++;
			//lastOffset = aTextObj.Offset + 1;
			//aTextObj.Offset = aTextObj.Offset + 1;

			pRejectionArray->AddTail(*aPass1Rejection);
		}
	}

	TimeStamp("After the process results loop");
}

void SLOTPass1Manager::SendPass1ProfileRules(ExternalConnection pConnection, 
											 CListSLOTP1RackTypeUsagePtr &pRackTypeUsagesList)
{
	MemoryStream aStream;		// What you write to the Connection
	typBinaryDataPtr aFeedData (new typBinaryData);		// What you feed into the Stream
	CString aTempTextData;		// The string you build from the Rack data

	// Work variables
	// __int32 aLength;			// COMMENTED - Unused variable

	// Start-of-Stream protocol tag
	aTempTextData = "";
	aTempTextData += "<SSO>\n";

	aTempTextData += "M|";
	aTempTextData.AppendFormat(_T("%d"), pRackTypeUsagesList->GetCount());
	aTempTextData += STREAM_DELIMITER;
	aTempTextData += "\n";

	// Loop through the list of Rack Types
	POSITION posRTL;
	posRTL = pRackTypeUsagesList->GetHeadPosition();
	SLOTP1RackTypeUsage aRackTypeUsage;
	for (int i = 0; i < pRackTypeUsagesList->GetCount(); i++)
	{
		aRackTypeUsage = pRackTypeUsagesList->GetNext(posRTL);
		aTempTextData += "R";
		aTempTextData += STREAM_DELIMITER;
		aTempTextData.AppendFormat(_T("%d"), aRackTypeUsage.GetBayProfileID());
		aTempTextData += STREAM_DELIMITER;
		aTempTextData.AppendFormat(_T("%d"), aRackTypeUsage.GetBayType());
		aTempTextData += STREAM_DELIMITER;
		aTempTextData.AppendFormat(_T("%d"), aRackTypeUsage.GetBayFacingInfoID());
		aTempTextData += STREAM_DELIMITER;
		aTempTextData.AppendFormat(_T("%d"), aRackTypeUsage.GetSuperGroup());
		aTempTextData += STREAM_DELIMITER;
		aTempTextData.AppendFormat(_T("%d"), aRackTypeUsage.GetFacingCount());
		aTempTextData += STREAM_DELIMITER;
		aTempTextData.AppendFormat(_T("%.4f"), aRackTypeUsage.GetExtendedCube());
		aTempTextData += STREAM_DELIMITER;
		aTempTextData.AppendFormat(_T("%.4f"), aRackTypeUsage.GetExtendedBOH());
		aTempTextData += STREAM_DELIMITER;
		aTempTextData += "\n";
	}

	// End-of-Stream protocol tag
	aTempTextData += "<EOS>";
	aTempTextData += "\n";

	DataStream* ss = gfnGetDataStream();
	ss->ssData << (LPCTSTR)aTempTextData;
/***>>> Socket Code... TBR
	// Move text into binary object
	aFeedData->SetValue(aTempTextData);
	aLength = aFeedData->ActualSize;

	// Stream the binary data
	aStream.Open(SP_AM_READ_WRITE);
	aStream.UseData(aFeedData->Value, aLength);
	//aStream.UseData(data = (pointer to char)(aFeedData.Value), aLength);
	// Send it
	pConnection.Write(aStream, aLength);
<<<***/
}

void SLOTPass1Manager::SendPass1Profiles(CListSLOTP1RackDataPtr &pP1RackDataList, 
										 ExternalConnection pConnection)
{
	MemoryStream aStream;		// What you write to the Connection
	typBinaryDataPtr aFeedData (new typBinaryData);	// What you feed into the Stream
	CString aTempTextData;		// The string you build from the Rack data

	// Work variables
	// __int32 aLength;			// COMMENTED - Unused variable

	// Start-of-Stream protocol tag
	aTempTextData = "";
	aTempTextData += "<SSO>\n";

	// Loop through the list of Rack Types
	aTempTextData += "M|";
	aTempTextData.AppendFormat(_T("%d"), pP1RackDataList->GetCount());
	aTempTextData += STREAM_DELIMITER;
	aTempTextData += "\n";

	POSITION posRDL;
	posRDL = pP1RackDataList->GetHeadPosition();
	SLOTP1RackData aP1RackData;
	for (int i = 0; i < pP1RackDataList->GetCount(); i++)
	{
		aP1RackData = pP1RackDataList->GetNext(posRDL);
		aTempTextData += "T";
		aTempTextData += STREAM_DELIMITER;
		aTempTextData.AppendFormat(_T("%d"), aP1RackData.GetBayProfileID());
		aTempTextData += STREAM_DELIMITER;
		aTempTextData.AppendFormat(_T("%d"), aP1RackData.GetBayType());
		aTempTextData += STREAM_DELIMITER;
		aTempTextData += aP1RackData.GetBayProfileDescription().c_str();
		aTempTextData += STREAM_DELIMITER;
		aTempTextData.AppendFormat(_T("%d"), aP1RackData.GetAvailLinealUnits());
		aTempTextData += STREAM_DELIMITER;
		aTempTextData.AppendFormat(_T("%d"), aP1RackData.GetAvailFixedFacings());
		aTempTextData += STREAM_DELIMITER;
		aTempTextData.AppendFormat(_T("%.3f"), aP1RackData.GetMaxWidth());
		aTempTextData += STREAM_DELIMITER;
		aTempTextData.AppendFormat(_T("%.3f"), aP1RackData.GetMaxDepth());
		aTempTextData += STREAM_DELIMITER;
		aTempTextData.AppendFormat(_T("%.3f"), aP1RackData.GetMaxHeight());
		aTempTextData += STREAM_DELIMITER;
		aTempTextData.AppendFormat(_T("%.3f"), aP1RackData.GetWeightCapacity());
		aTempTextData += STREAM_DELIMITER;
		if (aP1RackData.GetIsInFacility())
		{
			aTempTextData += "1";
			aTempTextData += STREAM_DELIMITER;
		}
		else
		{
			aTempTextData += "0";
			aTempTextData += STREAM_DELIMITER;
		}
		aTempTextData.AppendFormat(_T("%d"), aP1RackData.GetHandlingMethod());
		aTempTextData += STREAM_DELIMITER;
		aTempTextData.AppendFormat(_T("%.2f"), aP1RackData.GetProductGap());
		aTempTextData += STREAM_DELIMITER;
		aTempTextData.AppendFormat(_T("%.2f"), aP1RackData.GetProductSnap());
		aTempTextData += STREAM_DELIMITER;
		aTempTextData.AppendFormat(_T("%.2f"), aP1RackData.GetFacingGap());
		aTempTextData += STREAM_DELIMITER;
		aTempTextData.AppendFormat(_T("%.2f"), aP1RackData.GetFacingSnap());
		aTempTextData += STREAM_DELIMITER;
		aTempTextData += "\n";
	}

	// End-of-Stream protocol tag
	aTempTextData += "<EOS>";
	aTempTextData += STREAM_DELIMITER;
	aTempTextData += "\n";

	DataStream* ss = gfnGetDataStream();
	ss->ssData << (LPCTSTR)aTempTextData;
/***>>> Socket Code... TBR
	// Move text into binary object
	aFeedData->SetValue(aTempTextData);
	aLength = aFeedData->ActualSize;

	// Stream the binary data
	aStream.Open(SP_AM_READ_WRITE);
	aStream.UseData(aFeedData->Value, aLength);
	//aStream.UseData((pointer to char)(aFeedData->Value), aLength);
	// Send it
	pConnection.Write(aStream, aLength);
<<<***/
}


// CHECK1: Commented original forte code removed from this func
void SLOTPass1Manager::StoreData(SLOTFacilityPtr &pFacility, CListSLOTPass1ResultsIdealThinPtr &pIdealArray,
	CListSLOTPass1ResultsAvailThinPtr &pAvailArray, CListSLOTPass1SummaryPtr &pSummaryList, string pDatabase,
	__int32 pUserID, CListSLOTPass1RejectionPtr &pRejectionArray)
{
	TimeStamp("Begin Pass1.StoreData");

	TimeStamp("Before DeletePass1ResAvail");
	SLOTDataMgrSO.DeletePass1ResAvailForPass1(pFacility->GetDBID(), pDatabase);

	TimeStamp("Before DeletePass1ResIdeal");
	SLOTDataMgrSO.DeletePass1ResIdealForPass1(pFacility->GetDBID(), pDatabase);

	TimeStamp("Before DeletePass1Summary");
	SLOTDataMgrSO.DeletePass1SummaryByFacility(pDatabase,pFacility);

	TimeStamp("Before DeletePass1Message");
	SLOTDataMgrSO.DeleteP1MessageForPass1(pFacility->GetDBID(),pDatabase);

	TimeStamp("Before DeletePass1Rejection");
	SLOTDataMgrSO.DeletePass1RejectionForPass1(pFacility->GetDBID(), pDatabase);

	///SLOTPass1ResIdeal aIdealObj;		// COMMENTED - Unused variable
	///SLOTPass1ResAvail aAvailObj;		// COMMENTED - Unused variable
	///aIdealArray : LargeArray of SLOTPass1ResIdeal=new;  // COMMENTED - Unused variable
	///aAvailArray : LargeArray of SLOTPass1ResAvail=new;  // COMMENTED - Unused variable

	TimeStamp("Before StorePass1ResIdeal");
	SLOTDataMgrSO.StorePass1ResIdeal(pDatabase,pIdealArray, pUserID);

	TimeStamp("Before StorePass1ResAvail");
	__int32 i = 0;
	__int32 max  = (__int32)pAvailArray->GetCount();
	while (i < max) 
	{
		//if (pAvailArray[i].GetAvailBayType() = 0)
		SLOTPass1ResultsAvailThin theAvailThin = pAvailArray->GetAt(pAvailArray->FindIndex(i));
		if (theAvailThin.GetAvailBayType() == 0)
		{
			pAvailArray->RemoveAt(pAvailArray->FindIndex(i));
			max--;
		}
		else
		{
			i++;
		}
	}

	SLOTDataMgrSO.StorePass1ResAvail(pDatabase,pAvailArray, pUserID);

	TimeStamp("Before StorePass1Summary");
	SLOTDataMgrSO.StorePass1Summary(pDatabase,pFacility->GetDBID(), pSummaryList, pUserID);

	TimeStamp("Before StorePass1Rejections");
	log->put((__int32)pRejectionArray->GetCount());
	log->putline(" items to store.");
	SLOTDataMgrSO.StorePass1Rejections(pDatabase, pRejectionArray, pUserID);

	TimeStamp("End Pass1.StoreData");
}

void SLOTPass1Manager::TimeStamp(CString pText)
{
	typDateFormatPtr dtfmt (new typDateFormat);

	dtfmt->Template = ("%H:%M:%S");

	typDateTimeDataPtr dt (new typDateTimeData);
	dt->Value = CTime::GetCurrentTime();

	log->put(pText);
	log->put(" - ");
	log->putline(dt->Value.Format(dtfmt->Template));
}

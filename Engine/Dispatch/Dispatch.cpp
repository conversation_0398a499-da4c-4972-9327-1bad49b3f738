// Dispatch.cpp : Defines the entry point for the DLL application.
//

//#include "stdafx.h"
#include <windows.h>
#include "Dispatch.h"
#include "DataStream.h"

///CHECK007  is the shared segment needed?
///#pragma data_seg(".Share")
//the following fails due to a .NET debug binary problem... __data returns a bad ptr
//stringstream* __data = new stringstream(stringstream::in | stringstream::out);
//***>>> so wrap the stringstream object into a class
DataStream* __data = new DataStream;

///#pragma data_seg()
///#pragma comment(linker, "/section:.Share,rws")

/*BOOL APIENTRY DllMain( HANDLE hModule, 
                       DWORD  ul_reason_for_call, 
                       LPVOID lpReserved
					 )
{
    switch (ul_reason_for_call)
	{
		case DLL_PROCESS_ATTACH:
		case DLL_THREAD_ATTACH:
		case DLL_THREAD_DETACH:
		case DLL_PROCESS_DETACH:
			break;
    }
    return TRUE;
}
*/
DataStream* gfnGetDataStream()
{
	/*if (!__data)
		__data = new stringstream(stringstream::in | stringstream::out);
	*/
	return __data;
}
/**
stringstream* gfnGetDataStream()
{
	//*if (!__data)
	//	__data = new stringstream(stringstream::in | stringstream::out);
	
	//return __data;
}
**/
// This is the constructor of a class that has been exported.
// see Dispatch.h for the class definition
CDispatch::CDispatch()
{ 
	return; 
}


<?xml version="1.0" encoding="Windows-1252"?>
<VisualStudioProject
	ProjectType="Visual C++"
	Version="7.10"
	Name="Dispatch"
	ProjectGUID="{640250BB-A5EE-417C-A6C8-F7559392CD86}"
	SccProjectName=""
	SccLocalPath="">
	<Platforms>
		<Platform
			Name="Win32"/>
	</Platforms>
	<Configurations>
		<Configuration
			Name="Release|Win32"
			OutputDirectory="..\..\Obj.Release"
			IntermediateDirectory="$(outdir)/Engine"
			ConfigurationType="2"
			UseOfMFC="0"
			ATLMinimizesCRunTimeLibraryUsage="FALSE"
			CharacterSet="2">
			<Tool
				Name="VCCLCompilerTool"
				Optimization="2"
				InlineFunctionExpansion="1"
				AdditionalIncludeDirectories="..\..\common\core"
				PreprocessorDefinitions="WIN32;NDEBUG;_WINDOWS;_USRDLL;DISPATCH_EXPORTS"
				StringPooling="TRUE"
				RuntimeLibrary="0"
				EnableFunctionLevelLinking="TRUE"
				UsePrecompiledHeader="2"
				PrecompiledHeaderFile="$(outdir)/Engine/Dispatch.pch"
				AssemblerListingLocation="$(outdir)/Engine/"
				ObjectFile="$(outdir)/Engine/"
				ProgramDataBaseFileName="$(outdir)/Engine/"
				BrowseInformation="1"
				WarningLevel="3"
				SuppressStartupBanner="TRUE"/>
			<Tool
				Name="VCCustomBuildTool"/>
			<Tool
				Name="VCLinkerTool"
				AdditionalDependencies="nafxcw.lib odbc32.lib odbccp32.lib ws2_32.lib"
				OutputFile="$(OutDir)/bin/Dispatch.dll"
				LinkIncremental="2"
				SuppressStartupBanner="TRUE"
				IgnoreDefaultLibraryNames="nafxcw.lib"
				ModuleDefinitionFile=".\Dispatch.def"
				ProgramDatabaseFile="$(OutDir)/Engine/Dispatch.pdb"
				ImportLibrary="$(outdir)/libraries\Dispatch.lib"
				TargetMachine="1"/>
			<Tool
				Name="VCMIDLTool"
				PreprocessorDefinitions="NDEBUG"
				MkTypLibCompatible="TRUE"
				SuppressStartupBanner="TRUE"
				TargetEnvironment="1"
				TypeLibraryName=".\Release/Dispatch.tlb"
				HeaderFileName=""/>
			<Tool
				Name="VCPostBuildEventTool"/>
			<Tool
				Name="VCPreBuildEventTool"/>
			<Tool
				Name="VCPreLinkEventTool"/>
			<Tool
				Name="VCResourceCompilerTool"
				PreprocessorDefinitions="NDEBUG"
				Culture="1033"/>
			<Tool
				Name="VCWebServiceProxyGeneratorTool"/>
			<Tool
				Name="VCXMLDataGeneratorTool"/>
			<Tool
				Name="VCWebDeploymentTool"/>
			<Tool
				Name="VCManagedWrapperGeneratorTool"/>
			<Tool
				Name="VCAuxiliaryManagedWrapperGeneratorTool"/>
		</Configuration>
		<Configuration
			Name="Debug|Win32"
			OutputDirectory="..\..\Obj.Debug"
			IntermediateDirectory="$(outdir)/Engine"
			ConfigurationType="2"
			UseOfMFC="0"
			ATLMinimizesCRunTimeLibraryUsage="FALSE"
			CharacterSet="2">
			<Tool
				Name="VCCLCompilerTool"
				Optimization="0"
				AdditionalIncludeDirectories="..\..\common\core"
				PreprocessorDefinitions="WIN32;_DEBUG;_WINDOWS;_USRDLL;DISPATCH_EXPORTS"
				BasicRuntimeChecks="3"
				RuntimeLibrary="1"
				UsePrecompiledHeader="2"
				PrecompiledHeaderFile="$(outdir)/Engine/Dispatch.pch"
				AssemblerListingLocation="$(outdir)/Engine/"
				ObjectFile="$(outdir)/Engine/"
				ProgramDataBaseFileName="$(outdir)/Engine/"
				BrowseInformation="1"
				WarningLevel="3"
				SuppressStartupBanner="TRUE"
				DebugInformationFormat="4"/>
			<Tool
				Name="VCCustomBuildTool"/>
			<Tool
				Name="VCLinkerTool"
				AdditionalDependencies="nafxcwd.lib odbc32.lib odbccp32.lib ws2_32.lib"
				OutputFile="$(OutDir)/bin/Dispatch.dll"
				LinkIncremental="2"
				SuppressStartupBanner="TRUE"
				IgnoreDefaultLibraryNames="nafxcwd.lib"
				ModuleDefinitionFile=".\Dispatch.def"
				GenerateDebugInformation="TRUE"
				ProgramDatabaseFile="$(outdir)/Engine\Dispatch.pdb"
				ImportLibrary="$(outdir)/libraries\Dispatch.lib"
				TargetMachine="1"/>
			<Tool
				Name="VCMIDLTool"
				PreprocessorDefinitions="_DEBUG"
				MkTypLibCompatible="TRUE"
				SuppressStartupBanner="TRUE"
				TargetEnvironment="1"
				TypeLibraryName=".\Debug/Dispatch.tlb"
				HeaderFileName=""/>
			<Tool
				Name="VCPostBuildEventTool"/>
			<Tool
				Name="VCPreBuildEventTool"/>
			<Tool
				Name="VCPreLinkEventTool"/>
			<Tool
				Name="VCResourceCompilerTool"
				PreprocessorDefinitions="_DEBUG"
				Culture="1033"/>
			<Tool
				Name="VCWebServiceProxyGeneratorTool"/>
			<Tool
				Name="VCXMLDataGeneratorTool"/>
			<Tool
				Name="VCWebDeploymentTool"/>
			<Tool
				Name="VCManagedWrapperGeneratorTool"/>
			<Tool
				Name="VCAuxiliaryManagedWrapperGeneratorTool"/>
		</Configuration>
	</Configurations>
	<References>
	</References>
	<Files>
		<Filter
			Name="Source Files"
			Filter="cpp;c;cxx;rc;def;r;odl;idl;hpj;bat">
			<File
				RelativePath="pass4\baselinefcn.cpp">
				<FileConfiguration
					Name="Release|Win32">
					<Tool
						Name="VCCLCompilerTool"
						Optimization="2"
						PreprocessorDefinitions="WIN32;NDEBUG;_WINDOWS;_MBCS;_USRDLL;DISPATCH_EXPORTS;$(NoInherit)"
						BrowseInformation="1"/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug|Win32">
					<Tool
						Name="VCCLCompilerTool"
						Optimization="0"
						PreprocessorDefinitions="WIN32;_DEBUG;_WINDOWS;_MBCS;_USRDLL;DISPATCH_EXPORTS;$(NoInherit)"
						BasicRuntimeChecks="3"
						BrowseInformation="1"/>
				</FileConfiguration>
			</File>
			<File
				RelativePath="pass4\baselineprocess.cpp">
				<FileConfiguration
					Name="Release|Win32">
					<Tool
						Name="VCCLCompilerTool"
						Optimization="2"
						PreprocessorDefinitions="WIN32;NDEBUG;_WINDOWS;_MBCS;_USRDLL;DISPATCH_EXPORTS;$(NoInherit)"
						BrowseInformation="1"/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug|Win32">
					<Tool
						Name="VCCLCompilerTool"
						Optimization="0"
						PreprocessorDefinitions="WIN32;_DEBUG;_WINDOWS;_MBCS;_USRDLL;DISPATCH_EXPORTS;$(NoInherit)"
						BasicRuntimeChecks="3"
						BrowseInformation="1"/>
				</FileConfiguration>
			</File>
			<File
				RelativePath="Common\Coordinate.cpp">
				<FileConfiguration
					Name="Release|Win32">
					<Tool
						Name="VCCLCompilerTool"
						Optimization="2"
						PreprocessorDefinitions="WIN32;NDEBUG;_WINDOWS;_MBCS;_USRDLL;DISPATCH_EXPORTS;$(NoInherit)"
						BrowseInformation="1"/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug|Win32">
					<Tool
						Name="VCCLCompilerTool"
						Optimization="0"
						PreprocessorDefinitions="WIN32;_DEBUG;_WINDOWS;_MBCS;_USRDLL;DISPATCH_EXPORTS;$(NoInherit)"
						BasicRuntimeChecks="3"
						BrowseInformation="1"/>
				</FileConfiguration>
			</File>
			<File
				RelativePath=".\DataStream.cpp">
			</File>
			<File
				RelativePath="Dispatch.cpp">
				<FileConfiguration
					Name="Release|Win32">
					<Tool
						Name="VCCLCompilerTool"
						Optimization="2"
						PreprocessorDefinitions="WIN32;NDEBUG;_WINDOWS;_MBCS;_USRDLL;DISPATCH_EXPORTS;$(NoInherit)"
						BrowseInformation="1"/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug|Win32">
					<Tool
						Name="VCCLCompilerTool"
						Optimization="0"
						PreprocessorDefinitions="WIN32;_DEBUG;_WINDOWS;_MBCS;_USRDLL;DISPATCH_EXPORTS;$(NoInherit)"
						BasicRuntimeChecks="3"
						BrowseInformation="1"/>
				</FileConfiguration>
			</File>
			<File
				RelativePath="Dispatch.def">
			</File>
			<File
				RelativePath="Common\LaborCalc.cpp">
				<FileConfiguration
					Name="Release|Win32">
					<Tool
						Name="VCCLCompilerTool"
						Optimization="2"
						PreprocessorDefinitions="WIN32;NDEBUG;_WINDOWS;_MBCS;_USRDLL;DISPATCH_EXPORTS;$(NoInherit)"
						BrowseInformation="1"/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug|Win32">
					<Tool
						Name="VCCLCompilerTool"
						Optimization="0"
						PreprocessorDefinitions="WIN32;_DEBUG;_WINDOWS;_MBCS;_USRDLL;DISPATCH_EXPORTS;$(NoInherit)"
						BasicRuntimeChecks="3"
						BrowseInformation="1"/>
				</FileConfiguration>
			</File>
			<File
				RelativePath="Common\LevelLabor.cpp">
				<FileConfiguration
					Name="Release|Win32">
					<Tool
						Name="VCCLCompilerTool"
						Optimization="2"
						PreprocessorDefinitions="WIN32;NDEBUG;_WINDOWS;_MBCS;_USRDLL;DISPATCH_EXPORTS;$(NoInherit)"
						BrowseInformation="1"/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug|Win32">
					<Tool
						Name="VCCLCompilerTool"
						Optimization="0"
						PreprocessorDefinitions="WIN32;_DEBUG;_WINDOWS;_MBCS;_USRDLL;DISPATCH_EXPORTS;$(NoInherit)"
						BasicRuntimeChecks="3"
						BrowseInformation="1"/>
				</FileConfiguration>
			</File>
			<File
				RelativePath="Common\Move.cpp">
				<FileConfiguration
					Name="Release|Win32">
					<Tool
						Name="VCCLCompilerTool"
						Optimization="2"
						PreprocessorDefinitions="WIN32;NDEBUG;_WINDOWS;_MBCS;_USRDLL;DISPATCH_EXPORTS;$(NoInherit)"
						BrowseInformation="1"/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug|Win32">
					<Tool
						Name="VCCLCompilerTool"
						Optimization="0"
						PreprocessorDefinitions="WIN32;_DEBUG;_WINDOWS;_MBCS;_USRDLL;DISPATCH_EXPORTS;$(NoInherit)"
						BasicRuntimeChecks="3"
						BrowseInformation="1"/>
				</FileConfiguration>
			</File>
			<File
				RelativePath="Common\MoveCost.cpp">
				<FileConfiguration
					Name="Release|Win32">
					<Tool
						Name="VCCLCompilerTool"
						Optimization="2"
						PreprocessorDefinitions="WIN32;NDEBUG;_WINDOWS;_MBCS;_USRDLL;DISPATCH_EXPORTS;$(NoInherit)"
						BrowseInformation="1"/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug|Win32">
					<Tool
						Name="VCCLCompilerTool"
						Optimization="0"
						PreprocessorDefinitions="WIN32;_DEBUG;_WINDOWS;_MBCS;_USRDLL;DISPATCH_EXPORTS;$(NoInherit)"
						BasicRuntimeChecks="3"
						BrowseInformation="1"/>
				</FileConfiguration>
			</File>
			<File
				RelativePath="pass1\p1process.cpp">
				<FileConfiguration
					Name="Release|Win32">
					<Tool
						Name="VCCLCompilerTool"
						Optimization="2"
						PreprocessorDefinitions="WIN32;NDEBUG;_WINDOWS;_MBCS;_USRDLL;DISPATCH_EXPORTS;$(NoInherit)"
						BrowseInformation="1"/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug|Win32">
					<Tool
						Name="VCCLCompilerTool"
						Optimization="0"
						PreprocessorDefinitions="WIN32;_DEBUG;_WINDOWS;_MBCS;_USRDLL;DISPATCH_EXPORTS;$(NoInherit)"
						BasicRuntimeChecks="3"
						BrowseInformation="1"/>
				</FileConfiguration>
			</File>
			<File
				RelativePath="pass3\p3process.cpp">
				<FileConfiguration
					Name="Release|Win32">
					<Tool
						Name="VCCLCompilerTool"
						Optimization="2"
						PreprocessorDefinitions="WIN32;NDEBUG;_WINDOWS;_MBCS;_USRDLL;DISPATCH_EXPORTS;$(NoInherit)"
						BrowseInformation="1"/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug|Win32">
					<Tool
						Name="VCCLCompilerTool"
						Optimization="0"
						PreprocessorDefinitions="WIN32;_DEBUG;_WINDOWS;_MBCS;_USRDLL;DISPATCH_EXPORTS;$(NoInherit)"
						BasicRuntimeChecks="3"
						BrowseInformation="1"/>
				</FileConfiguration>
			</File>
			<File
				RelativePath="pass4\p4process.cpp">
				<FileConfiguration
					Name="Release|Win32">
					<Tool
						Name="VCCLCompilerTool"
						Optimization="2"
						PreprocessorDefinitions="WIN32;NDEBUG;_WINDOWS;_MBCS;_USRDLL;DISPATCH_EXPORTS;$(NoInherit)"
						BrowseInformation="1"/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug|Win32">
					<Tool
						Name="VCCLCompilerTool"
						Optimization="0"
						PreprocessorDefinitions="WIN32;_DEBUG;_WINDOWS;_MBCS;_USRDLL;DISPATCH_EXPORTS;$(NoInherit)"
						BasicRuntimeChecks="3"
						BrowseInformation="1"/>
				</FileConfiguration>
			</File>
			<File
				RelativePath="pass1\pass1fcn.cpp">
				<FileConfiguration
					Name="Release|Win32">
					<Tool
						Name="VCCLCompilerTool"
						Optimization="2"
						PreprocessorDefinitions="WIN32;NDEBUG;_WINDOWS;_MBCS;_USRDLL;DISPATCH_EXPORTS;$(NoInherit)"
						BrowseInformation="1"/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug|Win32">
					<Tool
						Name="VCCLCompilerTool"
						Optimization="0"
						PreprocessorDefinitions="WIN32;_DEBUG;_WINDOWS;_MBCS;_USRDLL;DISPATCH_EXPORTS;$(NoInherit)"
						BasicRuntimeChecks="3"
						BrowseInformation="1"/>
				</FileConfiguration>
			</File>
			<File
				RelativePath="pass3\pass3fcn.cpp">
				<FileConfiguration
					Name="Release|Win32">
					<Tool
						Name="VCCLCompilerTool"
						Optimization="2"
						PreprocessorDefinitions="WIN32;NDEBUG;_WINDOWS;_MBCS;_USRDLL;DISPATCH_EXPORTS;$(NoInherit)"
						BrowseInformation="1"/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug|Win32">
					<Tool
						Name="VCCLCompilerTool"
						Optimization="0"
						PreprocessorDefinitions="WIN32;_DEBUG;_WINDOWS;_MBCS;_USRDLL;DISPATCH_EXPORTS;$(NoInherit)"
						BasicRuntimeChecks="3"
						BrowseInformation="1"/>
				</FileConfiguration>
			</File>
			<File
				RelativePath="pass4\pass4fcn.cpp">
				<FileConfiguration
					Name="Release|Win32">
					<Tool
						Name="VCCLCompilerTool"
						Optimization="2"
						PreprocessorDefinitions="WIN32;NDEBUG;_WINDOWS;_MBCS;_USRDLL;DISPATCH_EXPORTS;$(NoInherit)"
						BrowseInformation="1"/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug|Win32">
					<Tool
						Name="VCCLCompilerTool"
						Optimization="0"
						PreprocessorDefinitions="WIN32;_DEBUG;_WINDOWS;_MBCS;_USRDLL;DISPATCH_EXPORTS;$(NoInherit)"
						BasicRuntimeChecks="3"
						BrowseInformation="1"/>
				</FileConfiguration>
			</File>
			<File
				RelativePath="Common\ProductLabor.cpp">
				<FileConfiguration
					Name="Release|Win32">
					<Tool
						Name="VCCLCompilerTool"
						Optimization="2"
						PreprocessorDefinitions="WIN32;NDEBUG;_WINDOWS;_MBCS;_USRDLL;DISPATCH_EXPORTS;$(NoInherit)"
						BrowseInformation="1"/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug|Win32">
					<Tool
						Name="VCCLCompilerTool"
						Optimization="0"
						PreprocessorDefinitions="WIN32;_DEBUG;_WINDOWS;_MBCS;_USRDLL;DISPATCH_EXPORTS;$(NoInherit)"
						BasicRuntimeChecks="3"
						BrowseInformation="1"/>
				</FileConfiguration>
			</File>
			<File
				RelativePath="..\..\Common\core\socket_class.cpp">
			</File>
			<File
				RelativePath="..\..\Common\core\threadwrapper.cpp">
			</File>
			<File
				RelativePath="Common\TravelDistances.cpp">
				<FileConfiguration
					Name="Release|Win32">
					<Tool
						Name="VCCLCompilerTool"
						Optimization="2"
						PreprocessorDefinitions="WIN32;NDEBUG;_WINDOWS;_MBCS;_USRDLL;DISPATCH_EXPORTS;$(NoInherit)"
						BrowseInformation="1"/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug|Win32">
					<Tool
						Name="VCCLCompilerTool"
						Optimization="0"
						PreprocessorDefinitions="WIN32;_DEBUG;_WINDOWS;_MBCS;_USRDLL;DISPATCH_EXPORTS;$(NoInherit)"
						BasicRuntimeChecks="3"
						BrowseInformation="1"/>
				</FileConfiguration>
			</File>
			<File
				RelativePath="Common\UtilityHelper.cpp">
				<FileConfiguration
					Name="Release|Win32">
					<Tool
						Name="VCCLCompilerTool"
						Optimization="2"
						PreprocessorDefinitions="WIN32;NDEBUG;_WINDOWS;_MBCS;_USRDLL;DISPATCH_EXPORTS;$(NoInherit)"
						BrowseInformation="1"/>
				</FileConfiguration>
				<FileConfiguration
					Name="Debug|Win32">
					<Tool
						Name="VCCLCompilerTool"
						Optimization="0"
						PreprocessorDefinitions="WIN32;_DEBUG;_WINDOWS;_MBCS;_USRDLL;DISPATCH_EXPORTS;$(NoInherit)"
						BasicRuntimeChecks="3"
						BrowseInformation="1"/>
				</FileConfiguration>
			</File>
		</Filter>
		<Filter
			Name="Header Files"
			Filter="h;hpp;hxx;hm;inl">
			<File
				RelativePath="pass4\baselinefcn.h">
			</File>
			<File
				RelativePath="pass4\baselineprocess.h">
			</File>
			<File
				RelativePath="Common\constants.h">
			</File>
			<File
				RelativePath="Common\Coordinate.h">
			</File>
			<File
				RelativePath=".\DataStream.h">
			</File>
			<File
				RelativePath="core\debug.h">
			</File>
			<File
				RelativePath="F:\Dispatch.h">
			</File>
			<File
				RelativePath="core\exceptions.h">
			</File>
			<File
				RelativePath="Common\LaborCalc.h">
			</File>
			<File
				RelativePath="Common\LevelLabor.h">
			</File>
			<File
				RelativePath="Common\Move.h">
			</File>
			<File
				RelativePath="Common\MoveCost.h">
			</File>
			<File
				RelativePath="pass1\p1.h">
			</File>
			<File
				RelativePath="pass1\p1process.h">
			</File>
			<File
				RelativePath="pass1\p1struct.h">
			</File>
			<File
				RelativePath="pass3\p3process.h">
			</File>
			<File
				RelativePath="pass4\p4process.h">
			</File>
			<File
				RelativePath="pass1\pass1fcn.h">
			</File>
			<File
				RelativePath="pass3\pass3fcn.h">
			</File>
			<File
				RelativePath="pass4\pass4fcn.h">
			</File>
			<File
				RelativePath="Common\ProductLabor.h">
			</File>
			<File
				RelativePath="resource.h">
			</File>
			<File
				RelativePath="..\..\Common\core\socket_class.h">
			</File>
			<File
				RelativePath="..\..\Common\core\threadwrapper.h">
			</File>
			<File
				RelativePath="Common\TravelDistances.h">
			</File>
			<File
				RelativePath="Common\UtilityHelper.h">
			</File>
		</Filter>
		<Filter
			Name="Resource Files"
			Filter="ico;cur;bmp;dlg;rc2;rct;bin;rgs;gif;jpg;jpeg;jpe">
		</Filter>
		<File
			RelativePath="ReadMe.txt">
		</File>
	</Files>
	<Globals>
	</Globals>
</VisualStudioProject>

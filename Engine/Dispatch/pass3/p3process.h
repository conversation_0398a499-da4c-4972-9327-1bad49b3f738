//////////////////////////////////////////////////////////////////////
// Function Name :	p3process.h
// Classname :		Pass3Process, rackTypeReq, SlottingGroup, pass3Bay,
//					pass3Rack; UNUSED: udf, fuzzyVar, ruleSet
// Description :	Header for p3process.cpp.
// Date Created :	~5/1/98
// Author : 		mfs
//////////////////////////////////////////////////////////////////////
// Inputs :			None
// Outputs :		None
// Explanation :	Header for p3process.cpp.
//////////////////////////////////////////////////////////////////////
// Revisions : 
//   November 6, 1998-mfs : Standard header comments added
//////////////////////////////////////////////////////////////////////
/*******************************************************
   *
   *                       NOTICE
   *
   *  THIS SOFTWARE IS THE PROPERTY OF AND CONTAINS
   *  CONFIDENTIAL INFORMATION OF SSA GLOBAL 
   *  TECHNOLOGIES, INC., AND SHALL NOT BE COPIED,
   *  USED, NOR DISCLOSED WITHOUT EXPRESS WRITTEN
   *  AUTHORIZATION.  ALTHOUGH PUBLICATION IS NOT
   *  INTENDED, IN THE EVENT OF PUBLICATION, THE
   *  FOLLOWING NOTICE IS APPLICABLE:
   *
   *  (c) COPYRIGHT 2004 SSA GLOBAL TECHNOLOGIES, INC.
   *
   *           SSA GLOBAL TECHNOLOGIES, INC.
   *
   ********************************************************/
//////////////////////////////////////////////////////////////////////

// Each Slotting Group will have a list of these requirements for different
// amounts (facings) of different basic Rack Types.

#ifndef RACK_REQUIRED
#define RACK_REQUIRED

typedef struct {
	int		profileID;
	int		levelType;
	int		facingCount;
	int		linealFacingCount;
	int		linealFacingWidth;
	int		ranking;
	double	ratioToPrimary;
	double	widthPerFace;
	int		origFacingCount;	
} rackTypeReq;

#endif RACK_REQUIRED Defined 

#ifndef DRIVE_SLOTGROUP
#define DRIVE_SLOTGROUP

typedef struct {
	int		sectionID;
	int		aisleID;
	int		sideDesc;
	int		bayID;
	int		relativeLevel;
	int		isExclusive;
} driveSlotGroup;
#endif 

// One of the prioritized groups of Products to be Slotted
#ifndef SLOTTING_GROUP
#define SLOTTING_GROUP

typedef struct {
	int			dbID;			// Forte DBID
	char		desc[251];		// for debugging
	int			totReqProfiles;
	int			totDriveParams;
	int			numProfiles;
	int			sectionID;		// to drive pg to section
	rackTypeReq	*rackReq;		// Basic Rack Types needed
	driveSlotGroup *driveParams;
	int			lookOutsideConstraints;
} SlottingGroup;

#endif /* SLOTTING_GROUP Defined */

// One of the Bays into which Products will be Slotted.
#ifndef PASS_3_BAY
#define PASS_3_BAY

typedef struct {
	int		sectionID;			//to allow driving into sections
	int		sectionXCoord;		//sort sections by coord of section
	int		sectionZCoord;
	int		aisleID;			//to allow driving into aisles
	int		sideID;				//to allow driving into sides
	int		sideDesc;
	int		bayID;				//to allow driving into bays
	int		levelID;
	int		relativeLevel;		//to allow driving into relative levels
	int		levelType;
	double	bayRelXCoord;
	double	aisleXCoord;
	double	aisleYCoord;
	double	aisleZCoord;
	double	aisleRotation;
	char	sectDesc[251];
	char	aisleDesc[251];
	char	bayDesc[251];
	int		profileID;
	int		bayIncrementValue;
	double	bayDistFromHS;
	double	aisleDistFromHS;
	double	hotSpot1XCoord;
	double	hotSpot1YCoord;
	double	hotSpot1ZCoord;
	double	hotSpot2XCoord;
	double	hotSpot2YCoord;
	double	hotSpot2ZCoord;
	int		linealFacings;		//lineal units (width) of bay
	int		fixedFacings;		//non-variable locations of bay
	int		origFixedFacings;
	int		origLinealFacings;
	char	curAcadHandle[20];	//this pickpath
	char	conAcadHandle[20];	//connected-to pickpath
	int		neededFacings;
	int		assgGroup;
	char	minShipSequence[20];
} pass3Bay;

#endif /* PASS_3_BAY Defined */

typedef struct {
	int aisleID;
	int haveChecked;
} pass3CheckedAisles;

// Used for finding the next best Rack Usage in the same Super Group when
// the Best Available Rack Usage determined in Pass 1 turns out to be not
// Available after all.

#ifndef PASS_3_RACK
#define PASS_3_RACK
/*
typedef struct {
	int		rackGroup;
	int		rackID;
} pass3Rack;
*/
#endif /* PASS_3_RACK Defined */


// Already defined for Pass 1
#define MAX_NUM_UDFS		50
#define	DOUBLE_DATA_LEN		10
#define	INT_DATA_LEN		10
#define	UDF_NAME_LEN		251
#define	UDF_CHAR_LEN		251
#define DATA_BUFFER_LEN		1024
#define MAX_INPUT_DATA_LEN	1000
#define DATA_LINE_LEN		128

#define	DATA_DELIMITER		"|"		// Pipe symbol
#define START_OF_STREAM "<SSO>"		// Communication protocols
#define END_OF_STREAM "<EOS>"
#define START_OF_SUBLIST "<SSL>"
#define END_OF_SUBLIST "<ESL>"
#define PROTOCOL_TAG_LENGTH 5


#ifndef PASS_3_PROCESS
#define PASS_3_PROCESS
#include "..\..\..\common\core\socket_class.h"
#include "..\dispatch.h"

class DISPATCH_API Pass3Process {

	public:

		// Instantiates a Pass 3 object which listens to the port for
		// its Rule Set and product data
		Pass3Process(SockClass *P3Socket); 
		void GetMidPoint(int x1, int y1, int x2, int y2, int &x3, int &y3);
		
		void ResetAisleDistanceFromHotspot();	
		virtual ~Pass3Process();
		int RoundUp(double value);
		int RoundDown(double value);
		int RoundClosest(double value);

		void Execute(void);

		void loadSlotGroups(void);	// Retrieve Slotting Group data from socket connection
		void buildGroupRec(int n);	// Move input data into Slotting Group structure

		void loadBays(void);		// Retrieve Bay data from socket connection
		void buildBayRec(int n);	// Move input data into Pass3Bay structure

//		void loadRackUsages(void);	// Retrieve Rack Usage data from socket connection
//		void buildRackRec(int n);	// Move input data into Basic RackUsage structure

		void assignGroup(int n);	// Find matches for Slotting Group rack requirements
									// by looking through the available racking in the Bays
		void assignGroupContiguous(int n);
//		int getRackIndex(int m, int n);	// Returns the index into p3Rack where rackGroup==m
										// and rackID==n.
		void sortBays(void);		// Sort the baylist according to Aisle/Bay number
		void copyBay(pass3Bay *destBay, pass3Bay *sourceBay);
		int	 checkAislesForMore();
		int  getNextAisleIndex(int layoutOption, int *increment, int *aisleID, int *endIndex);
		int  checkLayoutLimitationLimited(int slotGroupIndex, pass3Bay * currentBayLevel);
		int  checkLayoutLimitationNonLimited(int slotGroupIndex, pass3Bay * currentBayLevel);
		int	 aisleWasChecked(int aisleID);
		int GetUserFlags(void);
		int isParentConstraint(driveSlotGroup *parent, driveSlotGroup *child);
	protected:

		SlottingGroup	*slotGp;		// the list of Slotting Groups
		pass3Bay		*p3BayLevels;	// the list of Bays in the Facility
		pass3CheckedAisles *p3CheckAisles;
		SockClass		*dataSocket;	// data communication socket

		int				numGroups;
		int				numBays;
		int				numAisles;

		char			errMsg[DATA_LINE_LEN];

};

#endif // Pass3Process defined

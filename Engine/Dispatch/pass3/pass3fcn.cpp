//////////////////////////////////////////////////////////////////////
// Function Name :	Pass3fcn.cpp
// Classname :		
// Description :	Starts an independent thread for a Pass 3
//					(Assign Bays to Product Groups) process.
// Date Created :	~5/1/98
// Author : 		sc
//////////////////////////////////////////////////////////////////////
// Inputs :			Null pointer to the socket address
// Outputs :		Null pointer
// Explanation :	This and similar function for the other Passes
//					provide multi-threading capability for Succeed's
//					Engine.
//////////////////////////////////////////////////////////////////////
// Revisions : 
//   November 6, 1998-mfs : Standard header comments added
//////////////////////////////////////////////////////////////////////
/*******************************************************
   *
   *                       NOTICE
   *
   *  THIS SOFTWARE IS THE PROPERTY OF AND CONTAINS
   *  CONFIDENTIAL INFORMATION OF SSA GLOBAL 
   *  TECHNOLOGIES, INC., AND SHALL NOT BE COPIED,
   *  USED, NOR DISCLOSED WITHOUT EXPRESS WRITTEN
   *  AUTHORIZATION.  ALTHOUGH PUBLICATION IS NOT
   *  INTENDED, IN THE EVENT OF PUBLICATION, THE
   *  FOLLOWING NOTICE IS APPLICABLE:
   *
   *  (c) COPYRIGHT 2004 SSA GLOBAL TECHNOLOGIES, INC.
   *
   *           SSA GLOBAL TECHNOLOGIES, INC.
   *
   ********************************************************/
//////////////////////////////////////////////////////////////////////

#include <stdlib.h>
#include <stdio.h>
#include <string.h>
#include "P3Process.h"

#include "pass3fcn.h"
#include "..\..\..\common\core\socket_class.h"
#include "..\..\..\common\core\debug.h"
#include "..\DataStream.h"

void *pass3threadstart(void *arg)
{
	/* ************************************************ */
	/* This is the 'main' function for pass3.  It will  */
	/* be launched on a thread and we will be required  */
	/* to do the rest.  We will monitor a socket for    */
	/* all of the information that we need, and when we */
	/* are done, we will cleanup the thread and exit.   */
	/* ************************************************ */

	Pass3Process *aP3;
	SockClass *finalSock;
	SockClass *Pass3Socket;
	char buf[512];
	char hostname[128];
	int err;

	Pass3Socket = (SockClass *)arg;

	/* ************************************************ */
	/* Start the pass3 process off with the             */
	/* communications socket that will provide all of   */
	/* the information.                                 */
	/* ************************************************ */
	aP3 = new Pass3Process( Pass3Socket );

	/* ************************************************ */
	/* This method contains all of the functionality    */
	/* that is necessary for pass four to operate.      */
	/* ************************************************ */
	aP3->Execute();

	if ( SLOT_DEBUG ) printf("Done executing\n");

	delete aP3;

	err = gethostname(hostname, 128);
	if(err == SOCKET_ERROR){
		if ( SLOT_DEBUG ) {
			printf("Error getting local host name\n");
		}
		return(void *)NULL;
	}
	finalSock = new SockClass(hostname, 314159);
	memset(buf, 0, 512);
	sprintf(buf, "freeing-%d\n", (int)Pass3Socket);
	if ( SLOT_DEBUG ) {
		printf("Sending to localhost to free this thread\n");
	}
	/***>>>
	finalSock->SendData(buf, strlen(buf));
	<<<***/
	gfnGetDataStream()->ssData << buf;
	delete finalSock;

	return (void *)NULL;

}

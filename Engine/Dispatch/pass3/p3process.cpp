//////////////////////////////////////////////////////////////////////
// File Name :	P3Process.cpp
// Classname :
// Description :	The Pass 3 process.  Assigns Bay ranges to
//					Product Groups based on their Ideal and Best
//					Available Rack Types as determined in Pass 1.
// Date Created :	~4/1/98
// Author : 		mfs
//////////////////////////////////////////////////////////////////////
// Inputs :			None
// Outputs :		None
// Explanation :	The Execute function passes no parameters in
//					or out as such.  Instead, Rack Usage, Bay and
//					Product Group data arrive through a socket
//					connection to the Succeed session module.
//					The Product Groups are then analyzed in priority
//					order.  If the optimal Racking they require is
//					available, Bays are assigned.  Otherwise,
//					substitutes are found.
//////////////////////////////////////////////////////////////////////
// Revisions :
//   November 6, 1998-mfs : Standard header comments added
//   03-09-2006, 2006-SMB : Fixed bad array index
//////////////////////////////////////////////////////////////////////
/*******************************************************
   *
   *                       NOTICE
   *
   *  THIS SOFTWARE IS THE PROPERTY OF AND CONTAINS
   *  CONFIDENTIAL INFORMATION OF SSA GLOBAL
   *  TECHNOLOGIES, INC., AND SHALL NOT BE COPIED,
   *  USED, NOR DISCLOSED WITHOUT EXPRESS WRITTEN
   *  AUTHORIZATION.  ALTHOUGH PUBLICATION IS NOT
   *  INTENDED, IN THE EVENT OF PUBLICATION, THE
   *  FOLLOWING NOTICE IS APPLICABLE:
   *
   *  (c) COPYRIGHT 2004 SSA GLOBAL TECHNOLOGIES, INC.
   *
   *           SSA GLOBAL TECHNOLOGIES, INC.
   *
   ********************************************************/
//////////////////////////////////////////////////////////////////////

#include <stdlib.h>
#include <stdio.h>
#include <direct.h>
#include <errno.h>

#include <map>
#include <vector>
#include <string>

#include "../Common/UtilityHelper.h"
#include "P3Process.h"
#include "..\..\..\common\core\debug.h"
#include "..\..\..\common\core\exceptions.h"
#include <math.h>
#include "..\DataStream.h"


#define PI 3.1415926535
#define SHOW_TRACE 0
#pragma warning(disable:4786)

extern CUtilityHelper utilityHelper;

using namespace std;

typedef map<int, double, less<int> > IntdoubleMap;

int sectionCompare(const void * p1, const void * p2);
int convertToNumber(char *str);

int layoutFlag;				// 1=by HotSpot, 2=by Connected Pickpaths
FILE * p3TraceFile;
char P3LogUsr[256];
extern int userLog;
extern int debugLog;

int useContiguous = 0;
int reallyContiguous = 0;


Pass3Process::Pass3Process(SockClass *P3Socket)
{
	// Open the socket connection.
	dataSocket = P3Socket;

	// Init index/counters
	numGroups = 0;
	numBays = 0;

}

Pass3Process::~Pass3Process()
{

	// Free allocated memory for...

	// Socket
	delete dataSocket;

	//  Groups - which have sub-pointers to Racktype lists
	free(slotGp);

	// Bays
	free(p3BayLevels);

	if ( p3CheckAisles != NULL )
		free(p3CheckAisles);

	// Racks
//	free(p3Rack);

	if ( p3TraceFile != NULL && p3TraceFile != stdout ) {
		fprintf(p3TraceFile, "Closing.\n");
		fflush(p3TraceFile);
		fclose(p3TraceFile);
	}

}

void Pass3Process::Execute(void)
{
	char	line[DATA_LINE_LEN];
	char	returnData[DATA_BUFFER_LEN];
	char	errBuffer[DATA_BUFFER_LEN];

	int		i,j,k;
	unsigned int bytesSent;
	int numRanks = 0;
	int linFaces;

	fprintf(stdout, "Starting Product Group Layout Optimization.\n");

	numAisles = 0;
	try {
		/* *************************************************** */
		/* Indicate to the caller that we are ready to accept  */
		/* data on this new socket.                            */
		/* *************************************************** */
		//CHECK is the following code needed? We no more use sockets... No need to handshake...
		///memset(returnData, 0, DATA_BUFFER_LEN);
		///sprintf(returnData, "Ready For Data...\n<EOS>\n");
		///TBR bytesSent = dataSocket->SendData(returnData, strlen(returnData));
		///gfnGetDataStream()->ssData << returnData;
		/*** TBR
		if(bytesSent != strlen(returnData)){
			throw EngineException("Error returning AOK signal.\n",
				__FILE__, __LINE__, 200);
		}*/
		//if (SLOT_DEBUG) printf( "Connection established for Pass 3.\n" );


		GetUserFlags();
		if ( userLog || debugLog ) {
			p3TraceFile = NULL;
			char fileName[256];

			// Look up window registry to find the default first
			HKEY hRegKey;
			fileName[0] = 0;
			DWORD dwType = REG_SZ;
			DWORD dwReturnLength;

			if (RegOpenKeyEx(HKEY_LOCAL_MACHINE, "Software\\SSA Global\\Optimize", 0, KEY_READ, &hRegKey) == ERROR_SUCCESS) {
				int i = 0;
				while ((RegQueryValueEx(hRegKey, "LogFilePath", NULL, &dwType,(LPBYTE)fileName, &dwReturnLength) != ERROR_SUCCESS ) && i<=100)
						i++;
				if (i>100)
					fileName[0] = 0;
			}
			RegCloseKey(hRegKey);
			
			if (fileName[0] == 0)
				strcpy(fileName, ".\\Log");					// MFS 2Mar06 Value missing, default to \Optimize\Log
			
			if (_mkdir(fileName) != 0) {					// MFS 2Mar06 Try to create/open directory
				if (errno != EEXIST) {						// If that doesn't work...
					if (_stricmp(fileName, ".\\Log")==0)	// Try default unless we already tried it.
						strcpy(fileName, ".");				// Use current dir. as final fallback.
					else
						strcpy(fileName, ".\\Log");
				}
			}

			strcat(fileName, "\\");
			strcat(fileName, P3LogUsr);
			if (SHOW_TRACE)
				p3TraceFile = stdout;
			else {
				p3TraceFile = fopen(fileName,"w");
				if ( p3TraceFile == NULL ) {
					printf("Cannot create trace file: %s.  Check disk space on device.\n", fileName);
					printf("No logging will be printed.\n");
					debugLog = userLog = 0;
				}
				else {
					setvbuf(p3TraceFile, NULL, _IONBF, 0);
					char fullPath[256];
					if (_fullpath(fullPath, fileName, 256) != NULL)
						fprintf(stdout, "Trace file: %s\n", fullPath);
					else
						fprintf(stdout, "Trace file: %s\n",fileName);
				}
			}
		}
		else
			p3TraceFile = stdout;

		// Read in data for Bays.
		if ( debugLog )
			fprintf(p3TraceFile,"<DBGMSG> Before loadBays()\n");

		loadBays();

		if ( debugLog )
			fprintf(p3TraceFile,"<DBGMSG> After loadBays()\n");

		// Sorting bays by aisle/bay description
		//if (SLOT_DEBUG) printf( "\n" );

		//if (SLOT_DEBUG) printf("Sorting Bays by aisle/bay descriptions\n");

		if ( debugLog )
			fprintf(p3TraceFile,"<DBGMSG> Before sortBays()\n");

		sortBays();

		if ( debugLog )
			fprintf(p3TraceFile,"<DBGMSG> After sortBays()\n");


		// Read in data for Slotting Groups
		if ( debugLog )
			fprintf(p3TraceFile,"<DBGMSG> Before loadSlotGroups()\n");

		loadSlotGroups();

		if ( debugLog )
			fprintf(p3TraceFile,"<DBGMSG> After loadSlotGroups()\n");

		// Assign Slotting Groups to Bays
		for (i=0;i<numGroups;i++) {
			if ( debugLog )
				fprintf(p3TraceFile,"<DBGMSG> Before assignGroup(%d)\n",i);
			if (slotGp[i].numProfiles <= 0)
				continue;

			if (useContiguous)
				assignGroupContiguous(i);
			else
				assignGroup(i);
			if ( debugLog )
				fprintf(p3TraceFile,"<DBGMSG> After assignGroup(%d)\n",i);

		}

		// Report results to Host.
		//if (SLOT_DEBUG) printf("\nBay / Slotting Group Assignments:\n");
		memset(returnData, 0, DATA_BUFFER_LEN);
		for (i=0;i<numBays;i++) {

			if (p3BayLevels[i].bayID <= 0)
				continue;
			//////////////////////////////////////////////////////////////////////
			// Construct result line to send back to Forte. Ex: "G|0|A1|01|12345|\n",
			// where A1 is the Aisle, 01 is the Bay and 12345 is the dbID of the
			// Slotting Group assigned (0 means none).
			//////////////////////////////////////////////////////////////////////
			double mWidthPerFace = 1;
			memset(line, 0, DATA_LINE_LEN);
			if ( p3BayLevels[i].origLinealFacings != 0 ) {
				for ( j = 0; j < numGroups; j++)
				{
					if ( slotGp[j].dbID == p3BayLevels[i].assgGroup ) {
						for ( k = 0; k <  slotGp[j].numProfiles; k++ )
						{
							if ( p3BayLevels[i].profileID == slotGp[j].rackReq[k].profileID &&
								 p3BayLevels[i].levelType == slotGp[j].rackReq[k].levelType ) {
								mWidthPerFace = slotGp[j].rackReq[k].widthPerFace;
								break;
							}
						}
						break;
					}
				}
			}


			sprintf( line, "G%s0%s%d%s%d%s%d%s%d%s%d%s%s%s%s%s%2d%s%s%s%d%s%d%s\n", DATA_DELIMITER, DATA_DELIMITER,
							p3BayLevels[i].bayID, DATA_DELIMITER,
							p3BayLevels[i].levelID,DATA_DELIMITER,
							p3BayLevels[i].assgGroup, DATA_DELIMITER,
							p3BayLevels[i].origFixedFacings, DATA_DELIMITER,
							(int)(p3BayLevels[i].origLinealFacings), DATA_DELIMITER,
							p3BayLevels[i].sectDesc,DATA_DELIMITER,
							p3BayLevels[i].aisleDesc,DATA_DELIMITER,
							p3BayLevels[i].sideDesc,DATA_DELIMITER,
							p3BayLevels[i].bayDesc,DATA_DELIMITER,
							p3BayLevels[i].relativeLevel,DATA_DELIMITER,
							p3BayLevels[i].neededFacings,DATA_DELIMITER);

			if ( debugLog )
				fprintf(p3TraceFile,"<DBGMSG> Bay/Level Assignment Line Sent : %s",line);

			//if (SLOT_DEBUG) printf( "Aisle: %d, Bay #%d Level #%d (ProfID %d) Assg: %d\n",
			//				p3BayLevels[i].aisleID, p3BayLevels[i].bayID, p3BayLevels[i].levelID, p3BayLevels[i].profileID, p3BayLevels[i].assgGroup);

			//////////////////////////////////////////////////////////////////////
			// Check to see if the next line will make the stream longer than the
			// send buffer.  If so, send the existing stream and re-init the buffer
			// before copying the line in.
			//////////////////////////////////////////////////////////////////////
			if (strlen(returnData)+strlen(line)>=DATA_BUFFER_LEN) {
				gfnGetDataStream()->ssData << returnData;
				strcpy( returnData, "" );	// reInit return stream
			}
			strcat( returnData, line );
		}

		//////////////////////////////////////////////////////////////////////
		//  Show the product groups that still have requirements left
		//////////////////////////////////////////////////////////////////////
		for ( i = 0; i < numGroups; i++ ) {
			numRanks = 0;
			for ( j=1;  j < slotGp[i].numProfiles && slotGp[i].rackReq[j].ranking != 1; j++)
				;
			numRanks = j;
			for ( j = 0; j < slotGp[i].totReqProfiles; j++ ) {
				// Changed to only report unassigned if ranking 1
				if (slotGp[i].rackReq[j*numRanks].ranking != 1)
					continue;

				//////////////////////////////////////////////////////////////////////
				// only those that have facing requirements left
				//////////////////////////////////////////////////////////////////////
				// brd - send them no matter what, we will get rid of zeros later
				//if ( slotGp[i].rackReq[j*numRanks].linealFacingWidth > 0 || slotGp[i].rackReq[j*numRanks].facingCount > 0 ) {
					//////////////////////////////////////////////////////////////////////
					// approximate the number of variable width faces still needed
					//////////////////////////////////////////////////////////////////////
					if ( slotGp[i].rackReq[j*numRanks].linealFacingWidth > 0 )
						linFaces = (int)RoundClosest(slotGp[i].rackReq[j*numRanks].linealFacingWidth / slotGp[i].rackReq[j*numRanks].widthPerFace);
					else
						linFaces = 0;
					sprintf( line, "U%s%d%s%d%s%d%s%d%s%d%s%d%s\n", DATA_DELIMITER,
						slotGp[i].dbID, DATA_DELIMITER,
						slotGp[i].rackReq[j*numRanks].facingCount, DATA_DELIMITER,
						linFaces, DATA_DELIMITER,
						slotGp[i].rackReq[j*numRanks].profileID,DATA_DELIMITER,
						slotGp[i].rackReq[j*numRanks].levelType,DATA_DELIMITER,
						slotGp[i].rackReq[j*numRanks].origFacingCount, DATA_DELIMITER);
					if (strlen(returnData)+strlen(line)>=DATA_BUFFER_LEN) {
						gfnGetDataStream()->ssData << returnData;
						strcpy( returnData, "" );	// reInit return stream
					}
					strcat( returnData, line );
					if ( debugLog )
						fprintf(p3TraceFile,"<DBGMSG> Unfulfilled Requirements Line Sent : %s",line);
				//}
			}
		}

		//if (SLOT_DEBUG) printf("\n");

		// End-of-Stream protocol tag
		strcat( returnData, END_OF_STREAM );
		strcat( returnData, DATA_DELIMITER );
		strcat( returnData, "\n" );

		// Send last sub-stream of results back through socket.
		gfnGetDataStream()->ssData << returnData;
		//if (SLOT_DEBUG) printf("Data sent.\n");


	} catch(EngineException ee) {

		if (p3TraceFile != NULL)
			fflush(p3TraceFile);

		fprintf(stdout, "Product Group Layout Optimization failed.\n");
		// Log error to socket connection (and screen if debugging)
//		if (SLOT_DEBUG) {
//			ee.GetAllMessage(errBuffer);
//			printf("%s", errBuffer);
//		}
		if ( debugLog ) {
			fprintf(p3TraceFile,"Exception Caught During Processing.\n");
			ee.GetAllMessage(errBuffer);
			fprintf(p3TraceFile,"%s\n",errBuffer);
		}
		memset(errBuffer, 0, DATA_BUFFER_LEN);
		sprintf(errBuffer, "Error in Engine\n<EOS>\n");
		///TBR bytesSent = dataSocket->SendData(errBuffer, strlen(errBuffer));
		gfnGetDataStream()->ssData << errBuffer;
		/* TBR
		if(bytesSent < strlen(errBuffer))
			throw EngineException("Error Sending ERROR Signal",
				__FILE__, __LINE__, 200);
		*/

		// Don't exit- just go back to Listen
//		if (SLOT_DEBUG) printf( "\nAborting Pass 3.\n" );
		return;

	} catch(...){
		if (p3TraceFile != NULL)
			fflush(p3TraceFile);

		fprintf(stdout, "Product Group Layout Optimization failed.\n");
		printf("\n----------\nCaught an unhandled exception!\n----------\n");
		memset(errBuffer, 0, DATA_BUFFER_LEN);
		sprintf(errBuffer, "Error in Engine\n<EOS>\n");
		/// TBR bytesSent = dataSocket->SendData(errBuffer, strlen(errBuffer));
		gfnGetDataStream()->ssData << errBuffer;
		/** TBR
			if(bytesSent < strlen(errBuffer))
			throw EngineException("Error Sending ERROR Signal",
				 __FILE__, __LINE__, 200);
		*/
		// Don't exit- just go back to Listen
		//if (SLOT_DEBUG) printf( "\nAborting Pass 3.\n" );
		if ( debugLog ) {
			fprintf(p3TraceFile,"Exception Caught During Processing.\n");
		}
		return;
	}
	if ( debugLog ) {
		fprintf(p3TraceFile,"<DBGMSG> Finished Assigning Bay/Levels.\n");
	}

	fprintf(stdout, "Product Group Layout Optimization completed successfully.\n");
	return;
}

void Pass3Process::loadBays(void)
{
	int			errCode = 0, currAisle = 0;

	char		line[MAX_INPUT_DATA_LEN];
	char		*subString;
	char		aisleDescr[251];
	int			MemAmount=0;
	int			i,j;
	int			prevAisle;

	int EndOfData = 0;
	// Get data from the socket
	while( EndOfData == 0 )
	{
		// Read a line of data.
		memset(line, 0, MAX_INPUT_DATA_LEN);
		if( gfnGetDataStream()->ssData.getline( line, MAX_INPUT_DATA_LEN ) == 0 ) {
			throw EngineException( "Data file closed.\n" ,
				__FILE__, __LINE__, 200);
		}
		//if (SLOT_DEBUG) printf( "Bay data read: [%s]\n", line );

		// Check beginning code for line type

		if ( debugLog )
			fprintf(p3TraceFile,"<DBGMSG> Bay Data Received : %s",line);

		subString = strtok(line, DATA_DELIMITER);
		switch ( *subString ) {
			case 'M':
				subString = strtok(NULL, DATA_DELIMITER);
				MemAmount = (int)atoi( subString );
				// Allocate memory for Bay data from Forte
				if ( debugLog ) {
					fprintf(p3TraceFile,"<DBGMSG> Allocating Bay/Level Memory : %d",MemAmount);
				}
				p3BayLevels = (pass3Bay *)malloc( sizeof(pass3Bay) * (MemAmount+1));
				if ( p3BayLevels == NULL )
					throw EngineException( "Insufficient memory available for Rack Types.\n",
						__FILE__, __LINE__, 200);
				memset(p3BayLevels,0,sizeof(pass3Bay)*(MemAmount+1));
				break;
			case 'E':								// Section
				//////////////////////////////////////////////////////////////////////
				// section fields
				//////////////////////////////////////////////////////////////////////
				subString = strtok(NULL, DATA_DELIMITER);
				p3BayLevels[numBays].sectionID = (int)atoi( subString );
				subString = strtok(NULL, DATA_DELIMITER);
				memcpy( &p3BayLevels[numBays].sectDesc, subString, ( strlen(subString) + 1) );
				subString = strtok(NULL, DATA_DELIMITER);
				p3BayLevels[numBays].sectionXCoord = (int)atoi( subString );
				subString = strtok(NULL, DATA_DELIMITER);
				p3BayLevels[numBays].sectionZCoord = (int)atoi( subString );
				subString = strtok(NULL, DATA_DELIMITER);
				if ( subString != NULL )
					p3BayLevels[numBays].hotSpot1XCoord = (double)atof( subString );
				else
					p3BayLevels[numBays].hotSpot1XCoord = 0;
				subString = strtok(NULL, DATA_DELIMITER);
				if ( subString != NULL )
					p3BayLevels[numBays].hotSpot1YCoord = (double)atof( subString );
				else
					p3BayLevels[numBays].hotSpot1YCoord = 0;
				subString = strtok(NULL, DATA_DELIMITER);
				if ( subString != NULL )
					p3BayLevels[numBays].hotSpot1ZCoord = (double)atof( subString );
				else
					p3BayLevels[numBays].hotSpot1YCoord = 0;
				subString = strtok(NULL, DATA_DELIMITER);
				if ( subString != NULL )
					p3BayLevels[numBays].hotSpot2XCoord = (double)atof( subString );
				else
					p3BayLevels[numBays].hotSpot2XCoord = 0;
				subString = strtok(NULL, DATA_DELIMITER);
				if (subString != NULL)
					p3BayLevels[numBays].hotSpot2YCoord = (double)atof( subString );
				else
					p3BayLevels[numBays].hotSpot2YCoord = 0;
				subString = strtok(NULL, DATA_DELIMITER);
				if (subString != NULL)
					p3BayLevels[numBays].hotSpot2ZCoord = (double)atof( subString );
				else
					p3BayLevels[numBays].hotSpot2ZCoord = 0;
				break;
			case 'A':								// Aisle Info
				//////////////////////////////////////////////////////////////////////
				// aisle fields
				//////////////////////////////////////////////////////////////////////
				subString = strtok(NULL, DATA_DELIMITER);
				p3BayLevels[numBays].aisleID = (int)atoi( subString );
				subString = strtok(NULL, DATA_DELIMITER);
				memcpy( &p3BayLevels[numBays].aisleDesc, subString, ( strlen(subString) + 1) );
				memcpy( &aisleDescr, subString, ( strlen(subString) + 1) );
				subString = strtok(NULL, DATA_DELIMITER);
				memcpy( &p3BayLevels[numBays].curAcadHandle, subString, ( strlen(subString) + 1) );
				subString = strtok(NULL, DATA_DELIMITER);
				memcpy( &p3BayLevels[numBays].conAcadHandle, subString, ( strlen(subString) + 1) );
				currAisle = p3BayLevels[numBays].aisleID;
				subString = strtok(NULL, DATA_DELIMITER);
				p3BayLevels[numBays].aisleXCoord = (double)atof( subString );
				subString = strtok(NULL, DATA_DELIMITER);
				p3BayLevels[numBays].aisleYCoord = (double)atof( subString );
				subString = strtok(NULL, DATA_DELIMITER);
				p3BayLevels[numBays].aisleZCoord = (double)atof( subString );
				subString = strtok(NULL, DATA_DELIMITER);
				p3BayLevels[numBays].aisleRotation = (double)atof( subString );
				subString = strtok(NULL, DATA_DELIMITER);
				p3BayLevels[numBays].bayIncrementValue = (int)atoi( subString );
				numAisles++;
				break;
			case 'S':			// Side
				//////////////////////////////////////////////////////////////////////
				// side fields
				//////////////////////////////////////////////////////////////////////
				subString = strtok(NULL, DATA_DELIMITER);
				p3BayLevels[numBays].sideID = (int)atoi( subString );
				subString = strtok(NULL, DATA_DELIMITER);
				p3BayLevels[numBays].sideDesc = (int)atoi( subString );

				break;

			case 'B':								// Bay

				if ( numBays+1 < MemAmount) {

					//////////////////////////////////////////////////////////////////////
					// copy previous section, aisle, and side data
					//////////////////////////////////////////////////////////////////////
					copyBay(&p3BayLevels[numBays+1],&p3BayLevels[numBays]);

				}

				//////////////////////////////////////////////////////////////////////
				// bay fields
				//////////////////////////////////////////////////////////////////////
				buildBayRec(numBays);

				numBays++;


				break;

			case '<':
				if		( strstr( subString, START_OF_STREAM ) != NULL )
					break;	// Loop again for more data
				else if ( strstr( subString, START_OF_SUBLIST ) != NULL ) {
					break;	// Loop again for more data
				}
				else if ( strstr( subString, END_OF_SUBLIST ) != NULL ) {
					//if (SLOT_DEBUG) printf( "\nError:  End of sublist received during Bay transmission.\n" );
					break;	// Loop again for more data
				}
				else if( strstr( subString, END_OF_STREAM ) != NULL ) {
					//if (SLOT_DEBUG) printf( "End of Data.\n\n" );
					EndOfData = 1;
					break;	// EOS
				}
				else {
					sprintf(errMsg, "\nInvalid protocol tag [%s] read!\n", subString);
					throw EngineException(errMsg, __FILE__, __LINE__, 200);
				}

			//default:								// Unknown Type
				//if (SLOT_DEBUG) printf( "Unrecognized input data line read:\n%s\n", line );

		}		// End of switch stmt

	}			// End of while loop = EOS

	if ( debugLog )
		fprintf(p3TraceFile,"<DBGMSG> Allocating CheckedAisles Space %d\n",numAisles);

	p3CheckAisles = (pass3CheckedAisles*) malloc( sizeof(pass3CheckedAisles) * numAisles );
	if ( p3CheckAisles == NULL )
		throw EngineException( "Insufficient memory available for Aisle List.\n" ,
			__FILE__, __LINE__, 200);
	else {
		memset(p3CheckAisles,0,sizeof(pass3CheckedAisles)*numAisles);
		j = 0;
		prevAisle = 0;
		for ( i = 0; i < numBays; i++ ) {
			if ( p3BayLevels[i].aisleID != prevAisle ) {
				p3CheckAisles[j].aisleID = p3BayLevels[i].aisleID;
				p3CheckAisles[j].haveChecked = 0;
				j++;
				prevAisle = p3BayLevels[i].aisleID;
			}
		}
	}

	return;	// No errors

}

//////////////////////////////////////////////////////////////////////
// Move data from input into a Bay Level structure
//////////////////////////////////////////////////////////////////////
void Pass3Process::buildBayRec(int n)
{
	char		*subString;
	double		slope;
	double		tempBayX;
	double		tempBayY;
	double		initX, initY;

	// Build a Bay structure

	// Copy the stream input into a Table structure

	subString = strtok(NULL, DATA_DELIMITER);
	p3BayLevels[n].bayID = (int)atoi( subString );

	subString = strtok(NULL, DATA_DELIMITER);
	p3BayLevels[n].levelID = (int)atoi( subString );

	subString = strtok(NULL, DATA_DELIMITER);
	p3BayLevels[n].relativeLevel = (int)atoi( subString );

	subString = strtok(NULL, DATA_DELIMITER);
	memcpy( &p3BayLevels[n].bayDesc, subString, ( strlen(subString) + 1) );

	subString = strtok(NULL, DATA_DELIMITER);
	p3BayLevels[n].profileID = (int)atoi( subString );

	subString = strtok(NULL, DATA_DELIMITER);
	p3BayLevels[n].levelType = (int)atoi( subString );

	subString = strtok(NULL, DATA_DELIMITER);
	p3BayLevels[n].bayRelXCoord = (double)atof( subString );

	subString = strtok(NULL, DATA_DELIMITER);
	memcpy( &p3BayLevels[n].minShipSequence, subString, ( strlen(subString) + 1) );

	subString = strtok(NULL, DATA_DELIMITER);
	p3BayLevels[n].fixedFacings = (int)atoi( subString );

	subString = strtok(NULL, DATA_DELIMITER);
	p3BayLevels[n].linealFacings = (int)atoi( subString );

	p3BayLevels[n].origLinealFacings = p3BayLevels[n].linealFacings;
	p3BayLevels[n].origFixedFacings = p3BayLevels[n].fixedFacings;

	// Init Slotting Group assignment field
	p3BayLevels[n].assgGroup = 0;

	//////////////////////////////////////////////////////////////////////
	// calculate distance from HS on aisle and bays.  If two HSs are
	// in the sec\tion and they are in different locations, find the midpoint
	//////////////////////////////////////////////////////////////////////
	if ( p3BayLevels[n].hotSpot1XCoord == 0 )
		p3BayLevels[n].hotSpot1XCoord = 1;
	if ( p3BayLevels[n].hotSpot1YCoord == 0 )
		p3BayLevels[n].hotSpot1YCoord = 1;
	if ( p3BayLevels[n].hotSpot1ZCoord == 0 )
		p3BayLevels[n].hotSpot1ZCoord = 1;
	if ( p3BayLevels[n].hotSpot2XCoord == 0 )
		p3BayLevels[n].hotSpot2XCoord = 1;
	if ( p3BayLevels[n].hotSpot2YCoord == 0 )
		p3BayLevels[n].hotSpot2YCoord = 1;
	if ( p3BayLevels[n].hotSpot2ZCoord == 0 )
		p3BayLevels[n].hotSpot2ZCoord = 1;

	if ( p3BayLevels[n].hotSpot1XCoord == 1 &&
		 p3BayLevels[n].hotSpot1YCoord == 1 &&
		 p3BayLevels[n].hotSpot1ZCoord == 1 &&
		 p3BayLevels[n].hotSpot2XCoord != 1 &&
		 p3BayLevels[n].hotSpot2YCoord != 1 &&
		 p3BayLevels[n].hotSpot2ZCoord != 1 ) {
		p3BayLevels[n].hotSpot1XCoord = p3BayLevels[n].hotSpot2XCoord;
		p3BayLevels[n].hotSpot1XCoord = p3BayLevels[n].hotSpot2YCoord;
		p3BayLevels[n].hotSpot1XCoord = p3BayLevels[n].hotSpot2ZCoord;
	}

	if ( p3BayLevels[n].hotSpot1XCoord != 1 &&
		 p3BayLevels[n].hotSpot1YCoord != 1 &&
		 p3BayLevels[n].hotSpot1ZCoord != 1 &&
		 p3BayLevels[n].hotSpot2XCoord == 1 &&
		 p3BayLevels[n].hotSpot2YCoord == 1 &&
		 p3BayLevels[n].hotSpot2ZCoord == 1 ) {
		p3BayLevels[n].hotSpot2XCoord = p3BayLevels[n].hotSpot1XCoord;
		p3BayLevels[n].hotSpot2XCoord = p3BayLevels[n].hotSpot1YCoord;
		p3BayLevels[n].hotSpot2XCoord = p3BayLevels[n].hotSpot1ZCoord;
	}

	if ( p3BayLevels[n].hotSpot1XCoord != p3BayLevels[n].hotSpot2XCoord || p3BayLevels[n].hotSpot1YCoord != p3BayLevels[n].hotSpot2YCoord ||
		p3BayLevels[n].hotSpot1ZCoord != p3BayLevels[n].hotSpot2ZCoord ) {

		int x, y;
		GetMidPoint(p3BayLevels[n].hotSpot1XCoord, p3BayLevels[n].hotSpot1YCoord,
			p3BayLevels[n].hotSpot2XCoord, p3BayLevels[n].hotSpot2YCoord,
			x, y);

		if ( (p3BayLevels[n].hotSpot1XCoord - p3BayLevels[n].hotSpot2XCoord) != 0 )
			slope = ((p3BayLevels[n].hotSpot1YCoord - p3BayLevels[n].hotSpot2YCoord)/(p3BayLevels[n].hotSpot1XCoord - p3BayLevels[n].hotSpot2XCoord));
		else
			slope = ((p3BayLevels[n].hotSpot1YCoord - p3BayLevels[n].hotSpot2YCoord)/1);

		// find the point in the middle of the p3BayLevels[n].hotSpots if they are in different places

		p3BayLevels[n].hotSpot1XCoord = x;
		p3BayLevels[n].hotSpot1YCoord = y;

		if (debugLog) {
			fprintf(p3TraceFile, "Midpoint of hotspots: (%d, %d)\n", x, y);
		}

	}
	//////////////////////////////////////////////////////////////////////
	// aisle Distance from HS
	//////////////////////////////////////////////////////////////////////
	p3BayLevels[n].aisleDistFromHS =(double)( sqrt( ( p3BayLevels[n].aisleXCoord - p3BayLevels[n].hotSpot1XCoord ) * ( p3BayLevels[n].aisleXCoord - p3BayLevels[n].hotSpot1XCoord ) +
							 ( p3BayLevels[n].aisleYCoord - p3BayLevels[n].hotSpot1YCoord ) * ( p3BayLevels[n].aisleYCoord - p3BayLevels[n].hotSpot1YCoord ) ) );

	//////////////////////////////////////////////////////////////////////
	// Bay distance from HS - based on relative X coordinate and rotation
	// and aisle coordinate
	//////////////////////////////////////////////////////////////////////
	tempBayX = p3BayLevels[n].bayRelXCoord;
	tempBayY = 0;

	initX = tempBayX;
	initY = tempBayY;

	tempBayX = (double)(initX * cos(PI/180*p3BayLevels[n].aisleRotation) - initY * sin(PI/180*p3BayLevels[n].aisleRotation));
	tempBayY = (double)(initX * sin(PI/180*p3BayLevels[n].aisleRotation) + initY * cos(PI/180*p3BayLevels[n].aisleRotation));

	tempBayX += p3BayLevels[n].aisleXCoord;
	tempBayY += p3BayLevels[n].aisleYCoord;

	p3BayLevels[n].bayDistFromHS = (double)(sqrt( ( tempBayX - p3BayLevels[n].hotSpot1XCoord ) * ( tempBayX - p3BayLevels[n].hotSpot1XCoord ) +
						   ( tempBayY - p3BayLevels[n].hotSpot1YCoord ) * ( tempBayY - p3BayLevels[n].hotSpot1YCoord ) ) );

	if ( debugLog )
		fprintf(p3TraceFile,"<DBGMSG> Aisle DHS=%10.4f, Bay DHS=%10.4f\n",p3BayLevels[n].aisleDistFromHS,p3BayLevels[n].bayDistFromHS);

	return;	// No errors

}	// End of buildBayRec()

void Pass3Process::loadSlotGroups(void)
{
	int			errCode = 0;

	char		line[MAX_INPUT_DATA_LEN];
	char		*subString;
	int			MemAmount=0;

	// Get data from the socket
	while( 1 )
	{
		// Read a line of data.
		memset(line, 0, MAX_INPUT_DATA_LEN);
		if( gfnGetDataStream()->ssData.getline( line, MAX_INPUT_DATA_LEN ) == 0 ) {
			throw EngineException( "Data file closed.\n",
				__FILE__, __LINE__, 200);
		}
		//if (SLOT_DEBUG) printf( "Slotting Group data read: [%s]\n", line );

		// Check beginning code for line type
		if ( debugLog )
			fprintf(p3TraceFile,"<DBGMSG> Slotting Group Main Data Received : %s",line);

		subString = strtok(line, DATA_DELIMITER);
		switch ( *subString ) {
			case 'M':
				subString = strtok(NULL, DATA_DELIMITER);
				MemAmount = (int)atoi( subString );

				if ( debugLog )
					fprintf(p3TraceFile,"<DBGMSG> Allocating SlottingGroup Memory %d\n",MemAmount);

				slotGp = (SlottingGroup *)malloc( sizeof(SlottingGroup) * MemAmount );
				if ( slotGp == NULL )
					throw EngineException( "Insufficient memory available for Slotting Groups.\n" ,
						__FILE__, __LINE__, 200);
				break;
			case 'G':								// Product Group

				if ( debugLog )
					fprintf(p3TraceFile,"<DBGMSG> Before BuildGroupRec(%d)\n",numGroups);

				buildGroupRec(numGroups);

				if ( debugLog )
					fprintf(p3TraceFile,"<DBGMSG> After BuildGroupRec(%d)\n",numGroups);

				numGroups++;

				break;

			case '<':	// Protocol Tag
				if		( strstr( subString, START_OF_STREAM ) != NULL )
					break;	// Loop again for more data
				else if ( strstr( subString, START_OF_SUBLIST ) != NULL ) {
					break;	// Loop again for more data
				}
				else if ( strstr( subString, END_OF_SUBLIST ) != NULL ) {
					//if (SLOT_DEBUG) printf( "\nEnd of sublist of %d rows.\n", numGroups );
					break;	// Loop again for more data
				}
				else if( strstr( subString, END_OF_STREAM ) != NULL ) {
					//if (SLOT_DEBUG) printf( "End of Data.\n\n" );
					return;	// EOS
				}
				else {
					sprintf(errMsg, "\nInvalid protocol tag [%s] read!\n", subString);
					throw EngineException(errMsg, __FILE__, __LINE__, 200);
				}

			//default:								// Unknown Type
				//if (SLOT_DEBUG) printf( "Unrecognized input data line read:\n%s\n", line );
		}		// End of switch stmt

	}			// End of while loop = EOF

	return;	// No errors

} // End of loadSlotGroups()

//////////////////////////////////////////////////////////////////////
// Move input data line into Slotting Group data struct
//////////////////////////////////////////////////////////////////////
void Pass3Process::buildGroupRec(int n)
{
	char		line[MAX_INPUT_DATA_LEN];
	char		*subString;
	int			i;
	double tempfl, tempfl2;
	int			MemAmount=0;
	int			lineType=0;
	int			x;

	//////////////////////////////////////////////////////////////////////
	// Build a Slotting Group structure
	//////////////////////////////////////////////////////////////////////
	subString = strtok(NULL, DATA_DELIMITER);
	slotGp[n].dbID = (int)atoi( subString );
	//if (SLOT_DEBUG) printf( "* Slot Grp #%d: ", slotGp[n].dbID );

	subString = strtok(NULL, DATA_DELIMITER);
	slotGp[n].numProfiles = (int)atoi( subString );

	subString = strtok(NULL, DATA_DELIMITER);
	slotGp[n].totReqProfiles = (int)atoi( subString );

	//subString = strtok(NULL, DATA_DELIMITER);  // just added to each PG data, but is global
	//layoutFlag = (int)atoi( subString );	   //
	//int bla = (int)atoi(subString);

	subString = strtok(NULL, DATA_DELIMITER);
	slotGp[n].lookOutsideConstraints = (int)atoi(subString);

//	subString = strtok(NULL, DATA_DELIMITER);  // just added to each PG data, but is global

	for ( x = 0; x < 2; x++ ) // two sets of data for each product group
	{
		memset(line,0,MAX_INPUT_DATA_LEN);
		if( gfnGetDataStream()->ssData.getline( line, MAX_INPUT_DATA_LEN ) == 0) {
			throw EngineException( "gfnGetDataStream()->ssData.getline() error!\n",
				__FILE__, __LINE__, 200);
		}

		if ( debugLog )
			fprintf(p3TraceFile,"<DBGMSG> Slotting Group Detail Data Received : %s",line);

		subString = strtok(line, DATA_DELIMITER);
		switch ( *subString ) {
			case 'M':
				subString = strtok(NULL, DATA_DELIMITER);
				MemAmount = (int)atoi( subString );

				if ( debugLog )
					fprintf(p3TraceFile,"<DBGMSG> Allocating RackReq Memory %d\n",MemAmount);

				slotGp[n].rackReq = (rackTypeReq *)malloc(sizeof(rackTypeReq) * MemAmount);
				if ( slotGp[n].rackReq == NULL ) {
					throw EngineException( "Insufficient memory available for Rack Requirement list.\n",
						__FILE__, __LINE__, 200);
				}
				lineType = 1;
				break;
			case 'N':
				subString = strtok(NULL, DATA_DELIMITER);
				MemAmount = (int)atoi( subString );
				if ( debugLog )
					fprintf(p3TraceFile,"<DBGMSG> Allocating DriveParameters Memory %d\n",MemAmount);

				slotGp[n].driveParams = (driveSlotGroup *)malloc(sizeof(driveSlotGroup) * MemAmount);
				if ( slotGp[n].driveParams == NULL ) {
					throw EngineException( "Insufficient memory available for Rack Requirement list.\n",
						__FILE__, __LINE__, 200);
				}
				lineType = 2;
				slotGp[n].totDriveParams = MemAmount;
				break;
			//default:
				//if (SLOT_DEBUG) printf( "Unrecognized input data line read:\n%s\n", line );
		}

		//////////////////////////////////////////////////////////////////////
		// The next slotGp[n].rackReqCnt lines should be the Product's Racktype
		// Requirement rows
		//////////////////////////////////////////////////////////////////////
		if ( lineType == 1 ) {

			for (i=0;i<slotGp[n].numProfiles;i++) {

				// Read the next line
				memset(line,0,MAX_INPUT_DATA_LEN);
				if( gfnGetDataStream()->ssData.getline( line, MAX_INPUT_DATA_LEN ) == 0) {
					throw EngineException( "gfnGetDataStream()->ssData.getline() error!\n",
						__FILE__, __LINE__, 200);
				}

				// It has to be a rackReq line
				if ( debugLog )
					fprintf(p3TraceFile,"<DBGMSG> Slotting RackReq Data Received : %s",line);

				subString = strtok(line, DATA_DELIMITER);
				switch ( *subString ) {
					case 'Q':
						// OK
						break;
					default:
						//if (SLOT_DEBUG) printf( "Unrecognized input data line read:\n%s\n", line );
						continue;
				}

				// Copy the stream input into the rackReq list
				subString = strtok(NULL, DATA_DELIMITER);
				slotGp[n].rackReq[i].profileID = (int)atoi( subString );
				//if (SLOT_DEBUG) printf( "Req: %s,", subString );

				subString = strtok(NULL, DATA_DELIMITER);
				slotGp[n].rackReq[i].levelType = (int)atoi( subString );
				//if (SLOT_DEBUG) printf( "Req: %s,", subString );

				subString = strtok(NULL, DATA_DELIMITER);
				slotGp[n].rackReq[i].facingCount = (int)atoi( subString );
				slotGp[n].rackReq[i].origFacingCount = slotGp[n].rackReq[i].facingCount;
				//if (SLOT_DEBUG) printf( " %s LFcngs,", subString );

				subString = strtok(NULL, DATA_DELIMITER);
				slotGp[n].rackReq[i].linealFacingCount = (int)atoi( subString );
				//if (SLOT_DEBUG) printf( " %s LFcngs,", subString );

				subString = strtok(NULL, DATA_DELIMITER);
				slotGp[n].rackReq[i].linealFacingWidth = (int)atoi( subString );
				//if (SLOT_DEBUG) printf( " %s LFcngs,", subString );

				subString = strtok(NULL, DATA_DELIMITER);
				slotGp[n].rackReq[i].ranking = (int)atoi( subString );
				//if (SLOT_DEBUG) printf( " Rnk %s\n", subString );

				if (slotGp[n].rackReq[i].ranking != 1) {
					/*
					tempfl = (double)slotGp[n].rackReq[i-(slotGp[n].rackReq[i].ranking-1)].facingCount;
					if ( tempfl == 0.0 )
						tempfl = (double)slotGp[n].rackReq[i-(slotGp[n].rackReq[i].ranking-1)].linealFacingCount;
					tempfl2 = (double)slotGp[n].rackReq[i].facingCount;
					if ( tempfl2 == 0.0 )
						tempfl2 = (double)slotGp[n].rackReq[i].linealFacingCount;
					*/
					// brd - we've already subtracted lineal facing count from facing count for rank1
					// so we need to add them back together to get the total facing count which
					// we then used to calculate the ratio. before it was calculating the ratio
					// using either the fixed or lineal instead of the sum
					// the current ranking has subtracted lineal from total so we use the total by itself
					tempfl = (double)slotGp[n].rackReq[i-(slotGp[n].rackReq[i].ranking-1)].facingCount +
						(double)slotGp[n].rackReq[i-(slotGp[n].rackReq[i].ranking-1)].linealFacingCount;
					tempfl2 = (double)slotGp[n].rackReq[i].facingCount;

					if ( tempfl2 != 0 )
						slotGp[n].rackReq[i].ratioToPrimary = tempfl / tempfl2;
					else
						slotGp[n].rackReq[i].ratioToPrimary = 1;
				}
				else
					slotGp[n].rackReq[i].ratioToPrimary = 1;

				if ( slotGp[n].rackReq[i].linealFacingCount != 0 ) {
					// brd - I changed pass 1 to add in gaps so we don't need to fudge it here
					//slotGp[n].rackReq[i].linealFacingWidth += slotGp[n].rackReq[i].linealFacingCount * 10; // to allow for gaps and snaps
					slotGp[n].rackReq[i].widthPerFace = (double)((double)slotGp[n].rackReq[i].linealFacingWidth / (double)slotGp[n].rackReq[i].linealFacingCount);
					if (slotGp[n].rackReq[i].widthPerFace <= 0)
						slotGp[n].rackReq[i].widthPerFace = 1;
				}
				else
					slotGp[n].rackReq[i].widthPerFace = 0;

				if ( slotGp[n].rackReq[i].facingCount != 0 )
					slotGp[n].rackReq[i].facingCount -= slotGp[n].rackReq[i].linealFacingCount;
			}
		}
		else if ( lineType == 2 )
		{
			for (i=0;i<slotGp[n].totDriveParams;i++) {

				// Read the next line
				memset(line,0,MAX_INPUT_DATA_LEN);
				if( gfnGetDataStream()->ssData.getline( line, MAX_INPUT_DATA_LEN ) == 0) {
					throw EngineException( "gfnGetDataStream()->ssData.getline() error!\n",
						__FILE__, __LINE__, 200);
				}

				// It has to be a rackReq line
				if ( debugLog )
					fprintf(p3TraceFile,"<DBGMSG> Slotting DriveParam Data Received : %s",line);

				subString = strtok(line, DATA_DELIMITER);
				switch ( *subString ) {
					case 'D':
						// OK
						break;
					default:
						//if (SLOT_DEBUG) printf( "Unrecognized input data line read:\n%s\n", line );
						continue;
				}

				// Copy the stream input into the rackReq list
				subString = strtok(NULL, DATA_DELIMITER);
				slotGp[n].driveParams[i].sectionID = (int)atoi( subString );

				subString = strtok(NULL, DATA_DELIMITER);
				slotGp[n].driveParams[i].aisleID = (int)atoi( subString );

				subString = strtok(NULL, DATA_DELIMITER);
				slotGp[n].driveParams[i].sideDesc = (int)atoi( subString );

				subString = strtok(NULL, DATA_DELIMITER);
				slotGp[n].driveParams[i].bayID = (int)atoi( subString );

				subString = strtok(NULL, DATA_DELIMITER);
				slotGp[n].driveParams[i].relativeLevel = (int)atoi( subString );

				subString = strtok(NULL, DATA_DELIMITER);
				slotGp[n].driveParams[i].isExclusive = (int)atoi( subString );
			}
		}
		//if (SLOT_DEBUG) printf("\n");
	}
	return;

}	// End of buildGroupRec()

// Assign Bays for one Slotting Group
void Pass3Process::assignGroup(int n)
{
	int i,j,k;
	int numRanks = 0;
	int prevAisleID = 0;
	int lastHi = 0;
	int indexIncrement;
	int currentAisleID;
	int needMorePG;
	int nextAisleIndex = 0;
	int endIndex = 0;
	int times, maxTimes;

	// find number of rankings
	for ( i=1; i < slotGp[n].numProfiles && slotGp[n].rackReq[i].ranking != 1; i++)
		;
	numRanks = i;


	needMorePG = 1;

	//p3TraceFile = stdout;

	if (slotGp[n].lookOutsideConstraints)
		maxTimes = 4;
	else
		maxTimes = 3;

	// if there are no constraints then skip the first three loops
	if (slotGp[n].totDriveParams == 0) {
		times = 3;
		if (maxTimes == 3) {
			if (debugLog)
				fprintf(p3TraceFile, "No constraints and not looking outside constraints\n");
		}
	}

	for ( times = 0; times < maxTimes && (needMorePG == 1 || times < 2); times++ ) {
		//////////////////////////////////////////////////////////////////////
		// First pass - check to see if limitation on layout for the "suggested"
		// spot.  Second pass is "everything else"
		//////////////////////////////////////////////////////////////////////
		if ( debugLog ) {
			if ( times == 0 )
				fprintf(p3TraceFile,"<DBGMSG> First Pass - Checking Mandatory Drive Area\n");
			else if (times == 1)
				fprintf(p3TraceFile, "<DBGMSG> Second Pass - Filling mandatory automatically\n");
			else if (times == 2)
				fprintf(p3TraceFile,"<DBGMSG> Third Pass - Checking Suggested Area\n");
			else
				fprintf(p3TraceFile, "<DBGMSG> Fourth Pass - Checking All Other Areas\n");
		}

		for ( k = 0; k < numRanks && (needMorePG == 1 || times < 2); k++ ) {

			if (debugLog) {
				fprintf(p3TraceFile, "<DBGMSG> ------------------------------------------------------------------\n");
				fprintf(p3TraceFile, "<DBGMSG> Checking Ranking: %d\n", k);
				fprintf(p3TraceFile, "<DBGMSG> ------------------------------------------------------------------\n");
			}
			//////////////////////////////////////////////////////////////////////
			// mark all aisles as free to look in
			//////////////////////////////////////////////////////////////////////
			for ( i = 0; i < numAisles; i++ )
				p3CheckAisles[i].haveChecked = 0;

			needMorePG = 1;
			currentAisleID = 0;

			//////////////////////////////////////////////////////////////////////
			// continue until we have fulfilled the PG or have checked all aisles
			//////////////////////////////////////////////////////////////////////
			while ( checkAislesForMore() == 1 && (needMorePG == 1 || times < 2)) {

				//////////////////////////////////////////////////////////////////////
				// get next aisle to start at based on the layout option and the
				// previous aisle checked
				//////////////////////////////////////////////////////////////////////
				if ( debugLog )
					fprintf(p3TraceFile,"<DBGMSG> Done with aisle: %d)\n",currentAisleID);

				nextAisleIndex = getNextAisleIndex(layoutFlag, &indexIncrement, &currentAisleID, &endIndex);

				if ( debugLog ) {
					fprintf(p3TraceFile, "<DBGMSG> ------------------------------------------------------------------\n");
					fprintf(p3TraceFile,"<DBGMSG> Starting aisle: %d, Layout Flag: %d, Start: %d, Inc: %d, End: %d\n",
						currentAisleID, layoutFlag, nextAisleIndex, indexIncrement, endIndex);
				}

				if ( nextAisleIndex == -1 ) {
					for ( i = 0; i < numAisles; i++)
						p3CheckAisles[i].haveChecked = 1;			//all aisles have been checked
					break;
				}

				//////////////////////////////////////////////////////////////////////
				// Go through all bay levels in this aisle
				//////////////////////////////////////////////////////////////////////
				for ( j = nextAisleIndex;  j != endIndex; j+= indexIncrement ) {

					if (p3BayLevels[j].aisleID != currentAisleID)
						break;

					//////////////////////////////////////////////////////////////////////
					// already assigned - skip
					//////////////////////////////////////////////////////////////////////
					if ( p3BayLevels[j].assgGroup != 0 ) continue;

					if (p3BayLevels[j].bayID <= 0)
						continue;

					if ( times == 0 ) {
						// The first time, check for a "must have" level, if found process as normal
						// skip all non-must-haves
						if ( checkLayoutLimitationLimited(n,&p3BayLevels[j]) != 1 ) {
							if ( debugLog ) {
								fprintf(p3TraceFile,"<DBGMSG> Skipping %d Because Not Suggested (exclusive)\n",
									p3BayLevels[j].bayID);
							}
							continue;
						}

					}
					else if (times == 1) {
						if ( checkLayoutLimitationLimited(n,&p3BayLevels[j]) != 1 )
							continue;

						// This time, again check for "must haves", but if we find any, we know that either
						// the group didn't need any more facings or the level did not match any of the
						// requirements, so we can just assign the whole thing whether we need it or not
						//printf("Auto assigning: %d\n", p3BayLevels[j].levelID);
						p3BayLevels[j].assgGroup = slotGp[n].dbID;
						p3BayLevels[j].neededFacings += p3BayLevels[j].fixedFacings;
						p3BayLevels[j].neededFacings += p3BayLevels[j].linealFacings;
						p3BayLevels[j].fixedFacings = 0;
						p3BayLevels[j].linealFacings = 0;
						// don't mess with the rack requirements because we really didn't find anything
						continue;
					}
					// see if the bay is in the "can have" location, if not skip
					else if (times == 2) {
						//////////////////////////////////////////////////////////////////////
						// second pass - check the leftovers
						//////////////////////////////////////////////////////////////////////
						//if ( debugLog )
							//fprintf(p3TraceFile,"<DBGMSG> Before checkLayoutLimitationNonLimited()\n");

						if ( checkLayoutLimitationNonLimited(n,&p3BayLevels[j]) != 1 ) {
							if ( debugLog )
								fprintf(p3TraceFile,"<DBGMSG> Skipping %d Because Not Suggested (non-exclusive)\n",
									p3BayLevels[j].bayID);
							continue;
							if ( debugLog ) {
							//fprintf(p3TraceFile,"<DBGMSG> After checkLayoutLimitationNonLimited()\n");
							}
						}
					}

					//////////////////////////////////////////////////////////////////////
					// Check all requirements on this product group or until we use up
					// the facings for this bay level
					//////////////////////////////////////////////////////////////////////
					if ( debugLog ) {
						fprintf(p3TraceFile,"<DBGMSG> Checking Bay: %d(%s/%s/%s), Level: %d, Profile: %d, Fixed: %d, Lineal: %d\n",
							p3BayLevels[j].bayID, p3BayLevels[j].sectDesc, p3BayLevels[j].aisleDesc,
							p3BayLevels[j].bayDesc, p3BayLevels[j].levelID, p3BayLevels[j].profileID,
							p3BayLevels[j].fixedFacings, p3BayLevels[j].linealFacings);
					}

					// loop through each set of requirements for the current ranking
					for ( i = 0; i < slotGp[n].totReqProfiles && ( p3BayLevels[j].linealFacings > 0 || p3BayLevels[j].fixedFacings > 0); i++ ) {
						if ( debugLog ) {
							fprintf(p3TraceFile, "<DBGMSG> Ranking: %d, Set: %d.  Looking for profile: %d, type: %d, fixed: %d, lineal count: %d, lineal width: %d, width per face: %f, ratio: %f\n",
								k, i,
								slotGp[n].rackReq[i*numRanks+k].profileID, slotGp[n].rackReq[i*numRanks+k].levelType,
								slotGp[n].rackReq[i*numRanks+k].facingCount, slotGp[n].rackReq[i*numRanks+k].linealFacingCount,
								slotGp[n].rackReq[i*numRanks+k].linealFacingWidth,
								slotGp[n].rackReq[i*numRanks].widthPerFace, slotGp[n].rackReq[i*numRanks+k].ratioToPrimary);
						}

						//////////////////////////////////////////////////////////////////////
						// if this bay meets a requirement (within the ranks) and the primary
						// requirement has not been completely fulfilled
						//////////////////////////////////////////////////////////////////////
						if (slotGp[n].rackReq[i*numRanks+k].profileID == p3BayLevels[j].profileID &&
							slotGp[n].rackReq[i*numRanks+k].levelType == p3BayLevels[j].levelType &&
							( slotGp[n].rackReq[i*numRanks].linealFacingWidth > 0 || slotGp[n].rackReq[i*numRanks].facingCount > 0 )) {

							//////////////////////////////////////////////////////////////////////
							// NOTE : ratioToPrimary is used to scale requirements on secondary
							// ranking requirements to the primary.
							//////////////////////////////////////////////////////////////////////
							double *ratio, *widthPerFace;
							int *reqLinealWidth, *reqFixedCount, *bayLinealAvail, *bayFixedAvail, *bayFixedNeeded;

							// ranking 1 needed lineal width in units
							reqLinealWidth = &(slotGp[n].rackReq[i*numRanks].linealFacingWidth);
							// ranking 1 needed fixed facings
							reqFixedCount = &(slotGp[n].rackReq[i*numRanks].facingCount);
							// ratio between current ranking and ranking (r1/r2)
							ratio = &(slotGp[n].rackReq[i*numRanks+k].ratioToPrimary);
							// the number of lineal units per fixed facing
							widthPerFace = &(slotGp[n].rackReq[i*numRanks+k].widthPerFace);

							// the lineal units remaining on the bay
							bayLinealAvail = &(p3BayLevels[j].linealFacings);
							// the fixed facings left on the bay
							bayFixedAvail = &(p3BayLevels[j].fixedFacings);
							// the number of fixed facings needed so far on the bay
							bayFixedNeeded = &(p3BayLevels[j].neededFacings);




							// Use fixed facings on bay to meet fixed requirements
							if (*reqFixedCount > 0 && *bayFixedAvail > 0) {

								if (*bayFixedAvail >= *reqFixedCount / *ratio) {

									*bayFixedAvail -= RoundUp(*reqFixedCount / *ratio);
									*bayFixedNeeded += *reqFixedCount; //RoundDown(*reqFixedCount / *ratio);
									*reqFixedCount = 0;

								}
								else {

									*reqFixedCount -= RoundDown(*bayFixedAvail * *ratio);
									*bayFixedNeeded += RoundDown(*bayFixedAvail * *ratio); //*bayFixedAvail;
									*bayFixedAvail = 0;

								}
							}

							// Use lineal facings on bay to meet lineal requirements
							if (*reqLinealWidth > 0 && *bayLinealAvail > 0) {

								if (*bayLinealAvail >= *reqLinealWidth / *ratio) {

									*bayLinealAvail -= RoundUp(*reqLinealWidth / *ratio);
									*bayFixedNeeded += RoundDown(*reqLinealWidth / *widthPerFace); //RoundDown((*reqLinealWidth / *ratio) / *widthPerFace);
									*reqLinealWidth = 0;

								}
								else {

									*reqLinealWidth -= RoundDown(*bayLinealAvail * *ratio);
									*bayFixedNeeded += RoundDown( (*bayLinealAvail * *ratio) / *widthPerFace); //RoundDown(*bayLinealAvail / *widthPerFace);
									*bayLinealAvail = 0;

								}
							}

							// If we still need fixed, try to use lineal
							if (*reqFixedCount > 0 && *bayLinealAvail > 0) {

								if (*bayLinealAvail >= (*reqFixedCount / *ratio) * *widthPerFace) {

									*bayLinealAvail -= RoundUp((*reqFixedCount / *ratio) * *widthPerFace);
									*bayFixedNeeded += *reqFixedCount; //RoundDown(*reqFixedCount / *ratio);
									*reqFixedCount = 0;

								}
								else {

									if (*bayLinealAvail * *ratio >= *widthPerFace) {
										*reqFixedCount -= RoundDown((*bayLinealAvail * *ratio) / *widthPerFace);
										*bayFixedNeeded += RoundDown((*bayLinealAvail * *ratio) / *widthPerFace);; //RoundDown(*bayLinealAvail / *widthPerFace);
										*bayLinealAvail = 0;
									}

								}
							}



							// If we still need lineal, try to use fixed
							if (*reqLinealWidth > 0 && *bayFixedAvail > 0) {

								if (*bayFixedAvail >= (*reqLinealWidth / *ratio) / *widthPerFace) {

									*bayFixedAvail -= RoundUp((*reqLinealWidth / *ratio) / *widthPerFace);
									*bayFixedNeeded += RoundDown(*reqLinealWidth / *widthPerFace); //RoundDown((*reqLinealWidth / *ratio) / *widthPerFace);
									*reqLinealWidth = 0;

								}
								else {

									*reqLinealWidth -= RoundDown((*bayFixedAvail * *ratio) * *widthPerFace);
									*bayFixedNeeded += RoundDown((*bayFixedAvail * *ratio) * *widthPerFace); //*bayFixedAvail;
									*bayFixedAvail = 0;

								}
							}

							if (*bayFixedAvail < 0)
								*bayFixedAvail = 0;

							if (*bayLinealAvail < 0)
								*bayLinealAvail = 0;

							if (*reqFixedCount < 0)
								*reqFixedCount = 0;

							if (*reqLinealWidth < 0)
								*reqLinealWidth = 0;

							//////////////////////////////////////////////////////////////////////
							// Mark bay level as assigned
							//////////////////////////////////////////////////////////////////////
							p3BayLevels[j].assgGroup = slotGp[n].dbID;
							// Convert original lineal facings on bay type into fixed facings
							// by dividing by the width per face value
							if ( p3BayLevels[j].origLinealFacings != 0 )
								p3BayLevels[j].origLinealFacings = (int) (p3BayLevels[j].origLinealFacings/slotGp[n].rackReq[i*numRanks+k].widthPerFace);

							if ( debugLog ) {
								fprintf(p3TraceFile,"<DBGMSG> Found match. Ranking: %d, Set: %d,  Profile: %d,  LevType: %d,  "
									"New Fixed: %d, New Lineal: %d Needed: %d\n",
									k, i, slotGp[n].rackReq[i*numRanks+k].profileID,slotGp[n].rackReq[i*numRanks+k].levelType,
									slotGp[n].rackReq[i*numRanks].facingCount, slotGp[n].rackReq[i*numRanks].linealFacingWidth,
									*bayFixedNeeded);
							}

							//if (SLOT_DEBUG) printf( " SlGp %d uses Aisle %d(%10.10s), Bay %d(%10.10s), Type %d\n",
							//	slotGp[n].dbID, p3BayLevels[j].aisleID, p3BayLevels[j].aisleDesc,p3BayLevels[j].bayID,p3BayLevels[j].bayDesc, p3BayLevels[j].profileID);
						}	// end if profile
					}
				}

				//////////////////////////////////////////////////////////////////////
				// mark this aisle as checked
				//////////////////////////////////////////////////////////////////////
				if ( debugLog )
					fprintf(p3TraceFile,"<DBGMSG> Marking Aisle as checked\n");
				for ( j = 0; j < numAisles; j++ ) {
					if ( p3CheckAisles[j].aisleID == currentAisleID )  {
						p3CheckAisles[j].haveChecked = 1;
						break;
					}
				}

				//////////////////////////////////////////////////////////////////////
				// check to see if we have more requirements
				//////////////////////////////////////////////////////////////////////
				if ( debugLog )
					fprintf(p3TraceFile,"<DBGMSG> Checking if Requirements Fulfilled\n");
				if (times < 2)
					needMorePG = 1;
				else {
					needMorePG = 0;
					for ( i = 0; i < slotGp[n].totReqProfiles && needMorePG == 0; i++ )  {
						if ( slotGp[n].rackReq[i*numRanks].linealFacingWidth > 0 || slotGp[n].rackReq[i*numRanks].facingCount > 0 )
							needMorePG = 1;
					}
				}
			}
		}

	}

	if ( debugLog ) {
		if ( needMorePG == 1 )
			fprintf(p3TraceFile,"<DBGMSG> Completed Processing - Requirements Remaining in Product Group\n");
		else
			fprintf(p3TraceFile,"<DBGMSG> Completed Processing - Requirements Fulfilled in Product Group\n");
	}
	return;

}// End of assignGroup()

int Pass3Process::RoundUp(double value)
{
	int valueint = (int)value;

	if (valueint < value)
		return valueint+1;
	else
		return valueint;

}

int Pass3Process::RoundDown(double value)
{

	int valueint = (int)value;

	return valueint;
}

int Pass3Process::RoundClosest(double value)
{
	value = value+.5f;
	return RoundDown(value);

}


void Pass3Process::sortBays(void)
{
	ResetAisleDistanceFromHotspot();

	//Use the qsort function to do all of the sorting.
	qsort(p3BayLevels,numBays,sizeof(pass3Bay),sectionCompare);
	int i;
	if ( debugLog ) {
		for ( i = 0; i < numBays; i++ ) {
			fprintf(p3TraceFile,"<DBGMSG> Sorted Bay : ADHS-%10.4f BDHS-%10.4f %d-%s %d-%s %d-%s\n", p3BayLevels[i].aisleDistFromHS,p3BayLevels[i].bayDistFromHS, p3BayLevels[i].sectionID,p3BayLevels[i].sectDesc, p3BayLevels[i].aisleID, p3BayLevels[i].aisleDesc, p3BayLevels[i].bayID, p3BayLevels[i].bayDesc);
		}
	}

	return;
} // end of sortBays;

int sectionCompare(const void * p1, const void * p2) {
	int first, second;

	///////////////////////////////////////////////////////////
	// first the sections are grouped by coordinate, then
	// description
	///////////////////////////////////////////////////////////
	if ( ((pass3Bay*)p1)->sectionZCoord < ((pass3Bay*)p2)->sectionZCoord )
		return -1;
	else if ( ((pass3Bay*)p1)->sectionZCoord > ((pass3Bay*)p2)->sectionZCoord )
		return 1;

	if ( ((pass3Bay*)p1)->sectionXCoord < ((pass3Bay*)p2)->sectionXCoord )
		return -1;
	else if ( ((pass3Bay*)p1)->sectionXCoord > ((pass3Bay*)p2)->sectionXCoord )
		return 1;

	if ( ((pass3Bay*)p1)->sectionID > ((pass3Bay*)p2)->sectionID )
		return 1;
	else if ( ((pass3Bay*)p1)->sectionID < ((pass3Bay*)p2)->sectionID )
		return -1;

	// handle if two different sections have the same description
	if (strcmp(((pass3Bay*)p1)->sectDesc,((pass3Bay*)p2)->sectDesc) == 0) {
		if ( ((pass3Bay*)p1)->sectionID > ((pass3Bay*)p2)->sectionID )
			return 1;
		else if ( ((pass3Bay*)p1)->sectionID < ((pass3Bay*)p2)->sectionID )
			return -1;
	}
	else {
		first = convertToNumber(((pass3Bay*)p1)->sectDesc);
		second = convertToNumber(((pass3Bay*)p2)->sectDesc);

		if ( first == 0 || second == 0 ) {
			return strcmp(((pass3Bay*)p1)->sectDesc,((pass3Bay*)p2)->sectDesc);
		}
		else {
			if ( first < second )
				return -1;
			if ( first > second )
				return 1;
		}
	}
	///////////////////////////////////////////////////////////
	// Next, sort the aisles by distance from hotspot, then
	// by description
	///////////////////////////////////////////////////////////
	if ( ((pass3Bay*)p1)->aisleDistFromHS > ((pass3Bay*)p2)->aisleDistFromHS )
		return 1;
	else if ( ((pass3Bay*)p1)->aisleDistFromHS < ((pass3Bay*)p2)->aisleDistFromHS )
		return -1;

	// handle if two different aisles have the same description
	if (strcmp(((pass3Bay*)p1)->aisleDesc,((pass3Bay*)p2)->aisleDesc) == 0) {
		if ( ((pass3Bay*)p1)->aisleID > ((pass3Bay*)p2)->aisleID )
			return 1;
		else if ( ((pass3Bay*)p1)->aisleID < ((pass3Bay*)p2)->aisleID )
			return -1;
	}
	else {		// aisles have different descriptions
		first = convertToNumber(((pass3Bay*)p1)->aisleDesc);
		second = convertToNumber(((pass3Bay*)p2)->aisleDesc);

		if ( first == 0 || second == 0 ) {
			return strcmp(((pass3Bay*)p1)->aisleDesc,((pass3Bay*)p2)->aisleDesc);
		}
		else {
			if ( first < second )
				return -1;
			if ( first > second )
				return 1;
		}
	}

	///////////////////////////////////////////////////////////
	// Next, sort by the name of the bay -- IE, follow the
	// pickpath
	///////////////////////////////////////////////////////////

	if (((pass3Bay*)p1)->bayID == ((pass3Bay*)p2)->bayID) {
		///////////////////////////////////////////////////////////
		// Lastly, sort by level
		///////////////////////////////////////////////////////////
		if ( ((pass3Bay*)p1)->relativeLevel > ((pass3Bay*)p2)->relativeLevel )
			return 1;
		else if ( ((pass3Bay*)p1)->relativeLevel < ((pass3Bay*)p2)->relativeLevel )
			return -1;
		else
			return 0;
	}


	first = convertToNumber(((pass3Bay*)p1)->bayDesc);
	second = convertToNumber(((pass3Bay*)p2)->bayDesc);

	if ( first == 0 || second == 0 ) {
		if (strcmp(((pass3Bay*)p1)->bayDesc,((pass3Bay*)p2)->bayDesc) != 0) {
			if ( ((pass3Bay*)p1)->bayIncrementValue < 0 )
				return -1 * strcmp(((pass3Bay*)p1)->bayDesc,((pass3Bay*)p2)->bayDesc);
			else
				return strcmp(((pass3Bay*)p1)->bayDesc,((pass3Bay*)p2)->bayDesc);
		}
	}
	else {
		if ( first != second ) {
			if ( first < second ) {
				if ( ((pass3Bay*)p1)->bayIncrementValue < 0 )
					return 1;
				else
					return -1;
			}
			if ( first > second ) {
				if ( ((pass3Bay*)p1)->bayIncrementValue < 0 )
					return -1;
				else
					return 1;
			}
		}
	}

	// Sort by min location id
	int n = strcmp( ((pass3Bay*)p1)->minShipSequence, ((pass3Bay*)p2)->minShipSequence);
	if ( n < 0 ) {
		if ( ((pass3Bay*)p1)->bayIncrementValue < 0 )
			return 1;
		else
			return -1;
	}

	if ( n > 0 ) {
		if ( ((pass3Bay*)p1)->bayIncrementValue < 0 )
			return -1;
		else
			return 1;
	}

	return 0;

}


int convertToNumber(char *str)
{
	char *chkstr;
	int isNumber = 1;

	chkstr = str;
	while (*chkstr != '\0') {
		if (! isdigit(*chkstr)) {
			isNumber = 0;
			break;
		}
		chkstr++;
	}

	if (! isNumber)
		return 0;
	else
		return atoi(str);

}


void Pass3Process::copyBay(pass3Bay *destBay, pass3Bay *sourceBay) {
	memcpy(destBay,sourceBay,sizeof(pass3Bay));
	return;
}

int Pass3Process::checkAislesForMore() {
	int i;

	for ( i = 0; i < numAisles; i++ ) {
		if ( p3CheckAisles[i].haveChecked == 0 )
			return 1;
	}
	return 0; // all aisles finished
}

//////////////////////////////////////////////////////////////////////
// Function Name : getNextAisleIndex
// Classname : Pass3Process
// Description : The key to assigning bays to the PG.  Get the next
//               aisle that we should try to assign.  This is based
//               on either laying out by connected pickpaths, or
//                bays closest to the hotspot.
// Date Created :	~4/1/98
// Author : 		mfs
//////////////////////////////////////////////////////////////////////
// Inputs : int layoutOption
//          int *increment
//          int *aisleID
//          int *endIndex
// Outputs : int
//
// Explanation :
//////////////////////////////////////////////////////////////////////
// Revisions :
//   November 6, 1998-mfs : Standard header comments added
//   03-09-2006, 2006-SMB : Fixed bad array index
//////////////////////////////////////////////////////////////////////
int Pass3Process::getNextAisleIndex(int layoutOption, int *increment, int *aisleID, int *endIndex) {
	int i           = 0;
	int j           = 0;
	int prevAcadIdx = 0;
	int prevSecIdx  = 0;
	double lowDist1 = 999999999999.0f;
	int	lowDistIdx1 = 0;
	int foundStart  = 0;

	if ( (*aisleID) != 0 ) {
		for ( i = 0; i < numBays && (*aisleID) != p3BayLevels[i].aisleID; i++ ){
			;
		}//END for ( i = 0; i < numBays && (*aisleID) != p3BayLevels[i].aisleID; i++ )

		lowDist1 = p3BayLevels[i].aisleDistFromHS;
		prevAcadIdx = i;
		prevSecIdx = i;
	}//END if ( (*aisleID) != 0 )
	else {
		prevSecIdx = 0;
		prevAcadIdx = -1;
	}//END else

	//layoutOption = 2;
	//////////////////////////////////////////////////////////////////////
	// connected pickpaths
	//////////////////////////////////////////////////////////////////////
	//////////////////////////////////////////////////////////////////////
	// determine the "conected" pickpath by following the conAcadhandle
	// and curAcadHandle.  Pickpaths always have the connected autocad
	// handle as the pickpath that is connected to the start of any other
	// pickpath.  Therefore, the starting pickpath will have no connected
	// pickpath, or "XXX" for the handle.  Start at that pickpath, and
	// follow the connections.
	//////////////////////////////////////////////////////////////////////
	if ( layoutOption == 1 ) { //connected pickpaths
		(*increment) = 1; //already sorted by pickpath correctly
		(*endIndex) = numBays;
		//////////////////////////////////////////////////////////////////////
		// first time
		//////////////////////////////////////////////////////////////////////
		if ( (*aisleID) == 0 ) {
			foundStart = 0;
			prevAcadIdx = -1;
			for ( i = 0; i < numBays && foundStart == 0; i++ ) {
				if ( strcmp(p3BayLevels[i].conAcadHandle,"XXX" ) == 0 && aisleWasChecked(p3BayLevels[i].aisleID) != 1 ) {
					foundStart = 1;
					prevAcadIdx = i;
				}//END if ( strcmp(p3BayLevels[i].conAcadHandle,"XXX" ) == 0 && aisleWasChecked(p3BayLevels[i].aisleID) != 1 )
			}//END for ( i = 0; i < numBays && foundStart == 0; i++ )
			if ( foundStart == 1 ) {
				(*aisleID) = p3BayLevels[i-1].aisleID;
				return i-1;
			}//END if ( foundStart == 1 )
			else {
				(*aisleID) = p3BayLevels[0].aisleID;
				return 0;
			}//END else
		}//END if ( (*aisleID) == 0 )
		//////////////////////////////////////////////////////////////////////
		// we have been in an aisle before
		//////////////////////////////////////////////////////////////////////
		else {
			for ( i = 0; i < numBays && strcmp(p3BayLevels[i].conAcadHandle,p3BayLevels[prevAcadIdx].curAcadHandle) != 0; i++ ){
				; //Do Noting
			}// for ( i = 0; i < numBays && strcmp(p3BayLevels[i].conAcadHandle,p3BayLevels[prevAcadIdx].curAcadHandle) != 0; i++ )
			if ( i == numBays ) { //End of pickpath -- didn't find it
				foundStart = 0;
				for ( i = 0; i < numBays && foundStart == 0; i++ ) {
					if ( aisleWasChecked(p3BayLevels[i].aisleID) == 1 ) continue;

					if ( strcmp(p3BayLevels[i].conAcadHandle,"XXX" ) == 0 ) {
						foundStart = 1;
						prevAcadIdx = i;
					}//END if ( strcmp(p3BayLevels[i].conAcadHandle,"XXX" )
				}//END for ( i = 0; i < numBays && foundStart == 0; i++ )
				if ( foundStart == 0 ) { //all aisles checked (maybe)
					for ( i = 0; i < numBays && foundStart == 0; i++ ) {
						if ( aisleWasChecked(p3BayLevels[i].aisleID) == 1 ){
							continue;
						}//END if ( aisleWasChecked(p3BayLevels[i].aisleID) == 1 )

						foundStart = 1;

					}//END for ( i = 0; i < numBays && foundStart == 0; i++ )

					if ( foundStart == 0 ) {
						(*aisleID) = 0;
						return -1;
					}//END if ( foundStart == 0 )
					else {
						i--;
						(*aisleID) = p3BayLevels[i].aisleID;
						return i;
					}//END else

				}//END if ( foundStart == 0 )
				else {
					(*aisleID) = p3BayLevels[prevAcadIdx].aisleID;
					return prevAcadIdx;
				}//END else

			}//END if ( i == numBays )
			else { // continue previous path

				if ( aisleWasChecked(p3BayLevels[i].aisleID) != 1 ) {
					(*aisleID) = p3BayLevels[i].aisleID;
					return i;
				}//END if ( aisleWasChecked(p3BayLevels[i].aisleID) != 1 )
				else { // just find the first unchecked aisle (1.1 backward compatability)
					foundStart = 0;

					for ( i = 0; i < numBays && foundStart == 0; i++ ) {

						if ( aisleWasChecked(p3BayLevels[i].aisleID) != 1 ) {
							foundStart = 1;
							prevAcadIdx = i;
						}//END if ( aisleWasChecked(p3BayLevels[i].aisleID) != 1 )
					}//END for ( i = 0; i < numBays && foundStart == 0; i++ )

					if ( foundStart == 1 ) {
						(*aisleID) = p3BayLevels[i-1].aisleID;
						return i-1;
					}//END if ( foundStart == 1 )
					else {
						(*aisleID) = 0;
						return -1;
					}//END else
				}//END else
			}//END else
		}//END else
	}//END if ( layoutOption == 1 )
	//////////////////////////////////////////////////////////////////////
	// bays closest to HS option
	//////////////////////////////////////////////////////////////////////
	//////////////////////////////////////////////////////////////////////
	// We find the first bay that is closest to the HS and start at that
	// end of the aisle.  We continue in that aisle until we use that
	// aisle up or meet that requirement.  After that, we find the next
	// closest bay to the HS, and start at that end of the aisle
	//////////////////////////////////////////////////////////////////////
	else {

		if ( (*aisleID) == 0 ){
			j = 0;
		}//END if ( (*aisleID) == 0 )
		else {
			j = prevAcadIdx;
		}//END else

		for ( i = j; i >= 0 && p3BayLevels[i].sectionID == p3BayLevels[j].sectionID; i--){
			;//DO Nothing
		}//END for ( i = j; i >= 0 && p3BayLevels[i].sectionID == p3BayLevels[j].sectionID; i--)

		i++;

		//////////////////////////////////////////////////////////////////////
		// find the next bay that we can use
		//////////////////////////////////////////////////////////////////////
		for ( ; i < numBays && p3BayLevels[i].assgGroup != 0 || aisleWasChecked(p3BayLevels[i].aisleID) == 1; i++ ){
			;//DO Nothing
		}//END for ( ; i < numBays && p3BayLevels[i].assgGroup != 0 || aisleWasChecked(p3BayLevels[i].aisleID) == 1; i++ )

		if ( i == numBays ){
			return -1;
		}//END if ( i == numBays )
		//////////////////////////////////////////////////////////////////////
		// save its distance from HS
		//////////////////////////////////////////////////////////////////////
		j = i;
		lowDist1 = p3BayLevels[i].bayDistFromHS;
		lowDistIdx1 = i;

		//////////////////////////////////////////////////////////////////////
		// keep going within that section until we find the closest bay
		//////////////////////////////////////////////////////////////////////
		for ( i=j; i < numBays && p3BayLevels[i].sectionID == p3BayLevels[lowDistIdx1].sectionID; i++) {
			if ( p3BayLevels[i].bayDistFromHS < lowDist1 && aisleWasChecked(p3BayLevels[i].aisleID) == 0) {
				lowDist1 = p3BayLevels[i].bayDistFromHS;
				lowDistIdx1 = i;
			}//END if ( p3BayLevels[i].bayDistFromHS < lowDist1 && aisleWasChecked(p3BayLevels[i].aisleID) == 0)
		}//END for ( i=j; i < numBays && p3BayLevels[i].sectionID == p3BayLevels[lowDistIdx1].sectionID; i++)

		//////////////////////////////////////////////////////////////////////
		// found that bay and the aisle we will use, now start at the beginning
		// of the aisle
		//////////////////////////////////////////////////////////////////////
		for ( i = lowDistIdx1; i >= 0 && p3BayLevels[i].aisleID == p3BayLevels[lowDistIdx1].aisleID; i-- ){
			; // at beginning of aisle
		}//END for ( i = lowDistIdx1; i >= 0 && p3BayLevels[i].aisleID == p3BayLevels[lowDistIdx1].aisleID; i-- )

		if ( i == 0 ){
			i = -1;
		}//END if ( i == 0 )

		lowDistIdx1 = i+1;
		lowDist1 = p3BayLevels[lowDistIdx1].bayDistFromHS;

		for ( i = i+1; i < numBays && p3BayLevels[i].aisleID == p3BayLevels[lowDistIdx1].aisleID; i++){
			;//DO Nothing
		}//END for ( i = i+1; i < numBays && p3BayLevels[i].aisleID == p3BayLevels[lowDistIdx1].aisleID; i++)
		i--;
		//////////////////////////////////////////////////////////////////////
		// compare the distance to HS at the beginning and ending of the aisle
		// and return the start of that aisle based on the comparison
		//////////////////////////////////////////////////////////////////////
		if ( p3BayLevels[i].bayDistFromHS < p3BayLevels[lowDistIdx1].bayDistFromHS ) {
			if ( aisleWasChecked(p3BayLevels[i].aisleID) != 1 ) {
				(*increment) = -1;
				(*aisleID) = p3BayLevels[i].aisleID;
				(*endIndex) = -1;
				return i;
			}//END if ( aisleWasChecked(p3BayLevels[i].aisleID) != 1 )
			else {
				foundStart = 0;
				for ( i = 0; i < numBays && foundStart == 0; i++ ) {
					if ( aisleWasChecked(p3BayLevels[i].aisleID) != 1 ) {
						foundStart = 1;
						prevAcadIdx = i;
					}//END if ( aisleWasChecked(p3BayLevels[i].aisleID) != 1 )
				}//END for ( i = 0; i < numBays && foundStart == 0; i++ )
				if ( foundStart == 1 ) {
					(*aisleID) = p3BayLevels[i-1].aisleID;
					return i-1;
				}//END if ( foundStart == 1 )
				else {
					(*aisleID) = 0;
					return -1;
				}//END else
			}//END else
		}//END if ( p3BayLevels[i].bayDistFromHS < p3BayLevels[lowDistIdx1].bayDistFromHS )
		else {
			if ( aisleWasChecked(p3BayLevels[lowDistIdx1].aisleID) != 1 ) {
				(*increment) = 1;
				(*aisleID) = p3BayLevels[lowDistIdx1].aisleID;
				(*endIndex) = numBays;
				return lowDistIdx1;
			}//END if ( aisleWasChecked(p3BayLevels[lowDistIdx1].aisleID) != 1 )
			else {
				foundStart = 0;
				for ( i = 0; i < numBays && foundStart == 0; i++ ) {
					if ( aisleWasChecked(p3BayLevels[i].aisleID) != 1 ) {
						foundStart = 1;
						prevAcadIdx = i;
					}//END if ( aisleWasChecked(p3BayLevels[i].aisleID) != 1 )
				}//END for ( i = 0; i < numBays && foundStart == 0; i++ )
				if ( foundStart == 1 ) {
					(*aisleID) = p3BayLevels[i-1].aisleID;
					return i-1;
				}//END if ( foundStart == 1 )
				else {
					(*aisleID) = 0;
					return -1;
				}//END else
			}//END else
		}//END else
	}//END else
	return -1;
}//END int Pass3Process::getNextAisleIndex(int layoutOption, int *increment, int *aisleID, int *endIndex) {

// Check to see if the bay is in the "must use" group, i.e. isExclusive = 1
int Pass3Process::checkLayoutLimitationLimited(int slotGroupIndex, pass3Bay * currentBayLevel) {

	int i, j;
	driveSlotGroup mustHave, canHave;

	if ( slotGp[slotGroupIndex].totDriveParams == 0 )
		return 0;


	for ( i = 0; i < slotGp[slotGroupIndex].totDriveParams; i++ ) {

		if (slotGp[slotGroupIndex].driveParams[i].isExclusive == 0)
			continue;

		if (  ( slotGp[slotGroupIndex].driveParams[i].sectionID == currentBayLevel->sectionID ||
			    slotGp[slotGroupIndex].driveParams[i].sectionID == 0 ) &&
			  ( slotGp[slotGroupIndex].driveParams[i].aisleID == currentBayLevel->aisleID ||
			    slotGp[slotGroupIndex].driveParams[i].aisleID == 0 ) &&
			  ( slotGp[slotGroupIndex].driveParams[i].sideDesc == currentBayLevel->sideDesc ||
			    slotGp[slotGroupIndex].driveParams[i].sideDesc == 0 ) &&
			  ( slotGp[slotGroupIndex].driveParams[i].bayID == currentBayLevel->bayID ||
			    slotGp[slotGroupIndex].driveParams[i].bayID == 0 ) &&
			  ( slotGp[slotGroupIndex].driveParams[i].relativeLevel == currentBayLevel->relativeLevel ||
			  slotGp[slotGroupIndex].driveParams[i].relativeLevel == 0 ) ) {

			// we've determined that this area is in the "must have" group
			// but we need to make sure it is not also in the "can have" group in a more
			// more restricted area; the more detailed constraint overrides the more general
			mustHave = slotGp[slotGroupIndex].driveParams[i];

			for (j = 0; j < slotGp[slotGroupIndex].totDriveParams; ++j) {
				if (slotGp[slotGroupIndex].driveParams[j].isExclusive == 1)
					continue;

				canHave = slotGp[slotGroupIndex].driveParams[j];
				if (isParentConstraint(&mustHave, &canHave))
					return 0;
			}

			return 1;
		}
	}
	return 0;
}

int Pass3Process::isParentConstraint(driveSlotGroup *parent, driveSlotGroup *child)
{
	int sectionIsParent, aisleIsParent, sideIsParent, bayIsParent, levelIsParent;

	sectionIsParent = aisleIsParent = sideIsParent = bayIsParent = levelIsParent = 0;

	if (parent->sectionID == 0 || parent->sectionID == child->sectionID)
		sectionIsParent = 1;

	if (parent->aisleID == 0 || parent->aisleID == child->aisleID)
		aisleIsParent = 1;

	if (parent->sideDesc == 0 || parent->sideDesc == child->sideDesc)
		sideIsParent = 1;

	if (parent->bayID == 0 || parent->bayID == child->bayID)
		bayIsParent = 1;

	if (parent->relativeLevel == 0 || parent->relativeLevel == child->relativeLevel)
		levelIsParent = 1;

	if (sectionIsParent && aisleIsParent && sideIsParent && bayIsParent && levelIsParent)
		return 1;
	else
		return 0;
}

// Check to see if the bay is in the "can use" group, i.e. isExclusive = 0
int Pass3Process::checkLayoutLimitationNonLimited(int slotGroupIndex, pass3Bay * currentBayLevel) {
	int i;

	if ( slotGp[slotGroupIndex].totDriveParams == 0 )
		return 1;

	for ( i = 0; i < slotGp[slotGroupIndex].totDriveParams; i++ ) {

		if (slotGp[slotGroupIndex].driveParams[i].isExclusive == 1)
			continue;

		if ( ( slotGp[slotGroupIndex].driveParams[i].sectionID == currentBayLevel->sectionID ||
			    slotGp[slotGroupIndex].driveParams[i].sectionID == 0 ) &&
			  ( slotGp[slotGroupIndex].driveParams[i].aisleID == currentBayLevel->aisleID ||
			    slotGp[slotGroupIndex].driveParams[i].aisleID == 0 ) &&
			  ( slotGp[slotGroupIndex].driveParams[i].sideDesc == currentBayLevel->sideDesc ||
			    slotGp[slotGroupIndex].driveParams[i].sideDesc == 0 ) &&
			  ( slotGp[slotGroupIndex].driveParams[i].bayID == currentBayLevel->bayID ||
			    slotGp[slotGroupIndex].driveParams[i].bayID == 0 ) &&
			  ( slotGp[slotGroupIndex].driveParams[i].relativeLevel == currentBayLevel->relativeLevel ||
			    slotGp[slotGroupIndex].driveParams[i].relativeLevel == 0 ) )
			return 1;
	}
	return 0;
}

//////////////////////////////////////////////////////////////////////
// has the aisle been checked to assign?
//////////////////////////////////////////////////////////////////////
int Pass3Process::aisleWasChecked(int aisleID) {
	int i;

	for ( i = 0; i < numAisles; i++ ) {
		if ( aisleID == p3CheckAisles[i].aisleID ) {
			if (p3CheckAisles[i].haveChecked == 1)
				return 1;
			else
				return 0;
		}
	}
	return 0;
}

int Pass3Process::GetUserFlags(void)
{
	int			errCode = 0;
	int			MemAmount = 0;
	char		line[MAX_INPUT_DATA_LEN];
	char		*subString;
	std::string strLine;

	// Get data from the socket
	while( 1 )
	{
		// Read a line of data.
		memset(line, 0, MAX_INPUT_DATA_LEN);
		if( gfnGetDataStream()->ssData.getline( line, MAX_INPUT_DATA_LEN ) == 0 )
			throw EngineException("Data file closed.\n", __FILE__, __LINE__, 200);
		strLine = line;
		// Check beginning code for line type
		subString = strtok(line, DATA_DELIMITER);
		switch ( *subString ) {
			case 'L':								// Product

			{
					std::vector<std::string> parmList;

					// Format: Header|ClientName|layoutFlag|useContiguous|logmode|reallyContiguous
					utilityHelper.ParseString(strLine, "|", parmList);

					strcpy(P3LogUsr, parmList[1].c_str());
					strcat(P3LogUsr,"P3Trace.log");

					layoutFlag = atoi(parmList[2].c_str());
					useContiguous = atoi(parmList[3].c_str());

					std::string LogMode = parmList[4];
					if (LogMode == "None") {
						userLog = 0;
						debugLog = 0;
					}
					else if (LogMode == "User") {
						userLog = 1;
						debugLog = 0;
					}
					else if (LogMode == "Expert") {
						userLog = 1;
						debugLog = 1;
					}
					else {
						userLog = 0;
						debugLog = 0;
					}

					if (parmList.size() >= 6)
						reallyContiguous = atoi(parmList[5].c_str());

					printf("Parameters: \nClient: %s\nLayout Type: %d\nUse Contiguous: %d\n"
						"LogMode: %s\nReally Contiguous: %d\n",
						P3LogUsr, layoutFlag, useContiguous, LogMode.c_str(), reallyContiguous);

					break;
				}


			case '<':	// Protocol Tag
				if		( strstr( subString, START_OF_STREAM ) != NULL )
					break;	// Loop again for more data
				else if ( strstr( subString, START_OF_SUBLIST ) != NULL ) {
					break;	// Loop again for more data
				}
				else if ( strstr( subString, END_OF_SUBLIST ) != NULL ) {
					//if (SLOT_DEBUG) printf( "\nEnd of sublist of %d rows.\n", numProds );
					return 0;	// Loop again for more data
				}
				else if( strstr( subString, END_OF_STREAM ) != NULL ) {
					//if (SLOT_DEBUG) printf( "End of Data.\n\n" );
					return 1;	// EOS
				}
				else {
					sprintf(errMsg, "\nInvalid protocol tag '%s' read!\n", subString);
					throw EngineException(errMsg, __FILE__, __LINE__, 200);
				}

//			default:								// Unknown Type
				//if (SLOT_DEBUG) printf( "Unrecognized input data line read:\n%s\n", line);

		}		// End of switch stmt

	}			// End of while loop = EOF

//	strcpy(P3LogUsr,"BRIANHP3Trace.log");
}



void Pass3Process::ResetAisleDistanceFromHotspot()
{
	IntdoubleMap map;
	IntdoubleMap::iterator theIterator;

	for (int i=0; i < numBays; ++i) {
		theIterator = map.find(p3BayLevels[i].aisleID);
		if (theIterator == map.end()) {
			map.insert(IntdoubleMap::value_type(p3BayLevels[i].aisleID, p3BayLevels[i].bayDistFromHS));
		}
		else {
			if ((*theIterator).second > p3BayLevels[i].bayDistFromHS)
				(*theIterator).second = p3BayLevels[i].bayDistFromHS;
		}
	}

	for (i=0; i < numBays; ++i) {
		theIterator = map.find(p3BayLevels[i].aisleID);
		if (theIterator != map.end())
			p3BayLevels[i].aisleDistFromHS = (*theIterator).second;
	}

}

void Pass3Process::GetMidPoint(int x1, int y1, int x2, int y2, int &x3, int &y3)
{
	x3 = (x1 + x2)/2;
	y3 = (y1 + y2)/2;

}


void Pass3Process::assignGroupContiguous(int n)
{
	int i,j,k;
	int numRanks = 0;
	int prevAisleID = 0;
	int lastHi = 0;
	int indexIncrement;
	int currentAisleID;
	int needMorePG;
	int nextAisleIndex = 0;
	int endIndex = 0;
	int times, maxTimes;

	// find number of rankings
	for ( i=1; slotGp[n].rackReq[i].ranking != 1 && i < slotGp[n].numProfiles; i++)
		;
	numRanks = i;


	needMorePG = 1;

	//p3TraceFile = stdout;

	if (slotGp[n].lookOutsideConstraints)
		maxTimes = 4;
	else
		maxTimes = 3;

	// if there are no constraints then skip the first three loops
	if (slotGp[n].totDriveParams == 0) {
		times = 3;
		if (maxTimes == 3) {
			if (debugLog)
				fprintf(p3TraceFile, "No constraints and not looking outside constraints\n");
		}
	}

	for ( times = 0; times < maxTimes && (needMorePG == 1 || times < 2); times++ ) {
		//////////////////////////////////////////////////////////////////////
		// First pass - check to see if limitation on layout for the "suggested"
		// spot.  Second pass is "everything else"
		//////////////////////////////////////////////////////////////////////
		if ( debugLog ) {
			if ( times == 0 )
				fprintf(p3TraceFile,"<DBGMSG> First Pass - Checking Mandatory Drive Area\n");
			else if (times == 1)
				fprintf(p3TraceFile,"<DBGMSG> Second Pass - Checking Suggested Area\n");
			else
				fprintf(p3TraceFile, "<DBGMSG> Third Pass - Checking All Other Areas\n");
		}

			//////////////////////////////////////////////////////////////////////
			// mark all aisles as free to look in
			//////////////////////////////////////////////////////////////////////
			for ( i = 0; i < numAisles; i++ )
				p3CheckAisles[i].haveChecked = 0;

			needMorePG = 1;
			currentAisleID = 0;

			//////////////////////////////////////////////////////////////////////
			// continue until we have fulfilled the PG or have checked all aisles
			//////////////////////////////////////////////////////////////////////
			while ( checkAislesForMore() == 1 && (needMorePG == 1 || times < 2)) {

				//////////////////////////////////////////////////////////////////////
				// get next aisle to start at based on the layout option and the
				// previous aisle checked
				//////////////////////////////////////////////////////////////////////
				if ( debugLog )
					fprintf(p3TraceFile,"<DBGMSG> Done with aisle: %d)\n",currentAisleID);

				nextAisleIndex = getNextAisleIndex(layoutFlag, &indexIncrement, &currentAisleID, &endIndex);

				if ( debugLog ) {
					fprintf(p3TraceFile, "<DBGMSG> ------------------------------------------------------------------\n");
					fprintf(p3TraceFile,"<DBGMSG> Starting aisle: %d, Layout Flag: %d, Start: %d, Inc: %d, End: %d\n",
						currentAisleID, layoutFlag, nextAisleIndex, indexIncrement, endIndex);
				}

			 	if ( nextAisleIndex == -1 ) {
					for ( i = 0; i < numAisles; i++)
						p3CheckAisles[i].haveChecked = 1;			//all aisles have been checked
					break;
				}

				//////////////////////////////////////////////////////////////////////
				// Go through all bay levels in this aisle
				//////////////////////////////////////////////////////////////////////
				for ( j = nextAisleIndex;  j != endIndex && (needMorePG == 1 || times < 2); j+= indexIncrement ) {

					if (p3BayLevels[j].aisleID != currentAisleID)
						break;

					//////////////////////////////////////////////////////////////////////
					// already assigned - skip
					//////////////////////////////////////////////////////////////////////
					if ( p3BayLevels[j].assgGroup != 0 ) continue;

					if (p3BayLevels[j].bayID <= 0)
						continue;



					if ( times == 0 ) {
						// The first time, check for a "must have" level, if found process as normal
						// skip all non-must-haves
						if ( checkLayoutLimitationLimited(n,&p3BayLevels[j]) != 1 ) {
							if ( debugLog ) {
								fprintf(p3TraceFile,"<DBGMSG> Skipping %d Because Not Suggested (exclusive)\n",
									p3BayLevels[j].bayID);
							}
							continue;
						}

					}
					else if (times == 1) {
						if ( checkLayoutLimitationLimited(n,&p3BayLevels[j]) != 1 )
							continue;

						// This time, again check for "must haves", but if we find any, we know that either
						// the group didn't need any more facings or the level did not match any of the
						// requirements, so we can just assign the whole thing whether we need it or not
						//printf("Auto assigning: %d\n", p3BayLevels[j].levelID);
						p3BayLevels[j].assgGroup = slotGp[n].dbID;
						p3BayLevels[j].neededFacings += p3BayLevels[j].fixedFacings;
						p3BayLevels[j].neededFacings += p3BayLevels[j].linealFacings;
						p3BayLevels[j].fixedFacings = 0;
						p3BayLevels[j].linealFacings = 0;
						// don't mess with the rack requirements because we really didn't find anything
						continue;
					}
					// see if the bay is in the "can have" location, if not skip
					else if (times == 2) {
						//////////////////////////////////////////////////////////////////////
						// second pass - check the leftovers
						//////////////////////////////////////////////////////////////////////
						//if ( debugLog )
							//fprintf(p3TraceFile,"<DBGMSG> Before checkLayoutLimitationNonLimited()\n");

						if ( checkLayoutLimitationNonLimited(n,&p3BayLevels[j]) != 1 ) {
							if ( debugLog )
								fprintf(p3TraceFile,"<DBGMSG> Skipping %d Because Not Suggested (non-exclusive)\n",
									p3BayLevels[j].bayID);
							continue;
							if ( debugLog ) {
							//fprintf(p3TraceFile,"<DBGMSG> After checkLayoutLimitationNonLimited()\n");
							}
						}
					}

					//////////////////////////////////////////////////////////////////////
					// Check all requirements on this product group or until we use up
					// the facings for this bay level
					//////////////////////////////////////////////////////////////////////
					if ( debugLog ) {
						fprintf(p3TraceFile,"<DBGMSG> Checking Bay: %d(%s/%s/%s), Level: %d, Profile: %d, Fixed: %d, Lineal: %d\n",
							p3BayLevels[j].bayID, p3BayLevels[j].sectDesc, p3BayLevels[j].aisleDesc,
							p3BayLevels[j].bayDesc, p3BayLevels[j].levelID, p3BayLevels[j].profileID,
							p3BayLevels[j].fixedFacings, p3BayLevels[j].linealFacings);
					}
					for ( k = 0; k < numRanks && (needMorePG == 1 || times < 2); k++ ) {

					if (debugLog) {
						fprintf(p3TraceFile, "<DBGMSG> ------------------------------------------------------------------\n");
						fprintf(p3TraceFile, "<DBGMSG> Checking Ranking: %d\n", k);
						fprintf(p3TraceFile, "<DBGMSG> ------------------------------------------------------------------\n");
					}
					// loop through each set of requirements for the current ranking
					for ( i = 0; i < slotGp[n].totReqProfiles && ( p3BayLevels[j].linealFacings > 0 || p3BayLevels[j].fixedFacings > 0); i++ ) {
						if ( debugLog ) {
							fprintf(p3TraceFile, "<DBGMSG> Ranking: %d, Set: %d.  Looking for profile: %d, type: %d, fixed: %d, lineal count: %d, lineal width: %d, width per face: %f, ratio: %f\n",
								k, i,
								slotGp[n].rackReq[i*numRanks+k].profileID, slotGp[n].rackReq[i*numRanks+k].levelType,
								slotGp[n].rackReq[i*numRanks+k].facingCount, slotGp[n].rackReq[i*numRanks+k].linealFacingCount,
								slotGp[n].rackReq[i*numRanks+k].linealFacingWidth,
								slotGp[n].rackReq[i*numRanks].widthPerFace, slotGp[n].rackReq[i*numRanks+k].ratioToPrimary);
						}

						//////////////////////////////////////////////////////////////////////
						// if this bay meets a requirement (within the ranks) and the primary
						// requirement has not been completely fulfilled
						//////////////////////////////////////////////////////////////////////
						if ( ( (slotGp[n].rackReq[i*numRanks+k].profileID == p3BayLevels[j].profileID &&
							  slotGp[n].rackReq[i*numRanks+k].levelType == p3BayLevels[j].levelType) ||
							  (k == 0 && reallyContiguous) ) &&
							( slotGp[n].rackReq[i*numRanks].linealFacingWidth > 0 ||
							  slotGp[n].rackReq[i*numRanks].facingCount > 0 ) ) {

							//////////////////////////////////////////////////////////////////////
							// NOTE : ratioToPrimary is used to scale requirements on secondary
							// ranking requirements to the primary.
							//////////////////////////////////////////////////////////////////////
							double *ratio, *widthPerFace;
							int *reqLinealWidth, *reqFixedCount, *bayLinealAvail, *bayFixedAvail, *bayFixedNeeded;

							// ranking 1 needed lineal width in units
							reqLinealWidth = &(slotGp[n].rackReq[i*numRanks].linealFacingWidth);
							// ranking 1 needed fixed facings
							reqFixedCount = &(slotGp[n].rackReq[i*numRanks].facingCount);
							// ratio between current ranking and ranking (r1/r2)
							ratio = &(slotGp[n].rackReq[i*numRanks+k].ratioToPrimary);
							// the number of lineal units per fixed facing
							widthPerFace = &(slotGp[n].rackReq[i*numRanks+k].widthPerFace);

							// the lineal units remaining on the bay
							bayLinealAvail = &(p3BayLevels[j].linealFacings);
							// the fixed facings left on the bay
							bayFixedAvail = &(p3BayLevels[j].fixedFacings);
							// the number of fixed facings needed so far on the bay
							bayFixedNeeded = &(p3BayLevels[j].neededFacings);


							// Use fixed facings on bay to meet fixed requirements
							if (*reqFixedCount > 0 && *bayFixedAvail > 0) {

								if (*bayFixedAvail >= *reqFixedCount / *ratio) {

									*bayFixedAvail -= RoundUp(*reqFixedCount / *ratio);
									*bayFixedNeeded += *reqFixedCount; //RoundDown(*reqFixedCount / *ratio);
									*reqFixedCount = 0;

								}
								else {

									*reqFixedCount -= RoundDown(*bayFixedAvail * *ratio);
									*bayFixedNeeded += RoundDown(*bayFixedAvail * *ratio); //*bayFixedAvail;
									*bayFixedAvail = 0;

								}
							}

							// Use lineal facings on bay to meet lineal requirements
							if (*reqLinealWidth > 0 && *bayLinealAvail > 0) {

								if (*bayLinealAvail >= *reqLinealWidth / *ratio) {

									*bayLinealAvail -= RoundUp(*reqLinealWidth / *ratio);
									*bayFixedNeeded += RoundDown(*reqLinealWidth / *widthPerFace); //RoundDown((*reqLinealWidth / *ratio) / *widthPerFace);
									*reqLinealWidth = 0;

								}
								else {

									*reqLinealWidth -= RoundDown(*bayLinealAvail * *ratio);
									*bayFixedNeeded += RoundDown( (*bayLinealAvail * *ratio) / *widthPerFace); //RoundDown(*bayLinealAvail / *widthPerFace);
									*bayLinealAvail = 0;

								}
							}

							// If we still need fixed, try to use lineal
							if (*reqFixedCount > 0 && *bayLinealAvail > 0) {

								if (*bayLinealAvail >= (*reqFixedCount / *ratio) * *widthPerFace) {

									*bayLinealAvail -= RoundUp((*reqFixedCount / *ratio) * *widthPerFace);
									*bayFixedNeeded += *reqFixedCount; //RoundDown(*reqFixedCount / *ratio);
									*reqFixedCount = 0;

								}
								else {

									if (*bayLinealAvail * *ratio >= *widthPerFace) {
										*reqFixedCount -= RoundDown((*bayLinealAvail * *ratio) / *widthPerFace);
										*bayFixedNeeded += RoundDown((*bayLinealAvail * *ratio) / *widthPerFace);; //RoundDown(*bayLinealAvail / *widthPerFace);
										*bayLinealAvail = 0;
									}

								}
							}



							// If we still need lineal, try to use fixed
							if (*reqLinealWidth > 0 && *bayFixedAvail > 0) {

								if (*bayFixedAvail >= (*reqLinealWidth / *ratio) / *widthPerFace) {

									*bayFixedAvail -= RoundUp((*reqLinealWidth / *ratio) / *widthPerFace);
									*bayFixedNeeded += RoundDown(*reqLinealWidth / *widthPerFace); //RoundDown((*reqLinealWidth / *ratio) / *widthPerFace);
									*reqLinealWidth = 0;

								}
								else {

									*reqLinealWidth -= RoundDown((*bayFixedAvail * *ratio) * *widthPerFace);
									*bayFixedNeeded += RoundDown((*bayFixedAvail * *ratio) * *widthPerFace); //*bayFixedAvail;
									*bayFixedAvail = 0;

								}
							}

							if (*bayFixedAvail < 0)
								*bayFixedAvail = 0;

							if (*bayLinealAvail < 0)
								*bayLinealAvail = 0;

							if (*reqFixedCount < 0)
								*reqFixedCount = 0;

							if (*reqLinealWidth < 0)
								*reqLinealWidth = 0;

							//////////////////////////////////////////////////////////////////////
							// Mark bay level as assigned
							//////////////////////////////////////////////////////////////////////
							p3BayLevels[j].assgGroup = slotGp[n].dbID;
							// Convert original lineal facings on bay type into fixed facings
							// by dividing by the width per face value
							if ( p3BayLevels[j].origLinealFacings != 0 )
								p3BayLevels[j].origLinealFacings = (int) (p3BayLevels[j].origLinealFacings/slotGp[n].rackReq[i*numRanks+k].widthPerFace);

							if ( debugLog ) {
								fprintf(p3TraceFile,"<DBGMSG> Found match. Ranking: %d, Set: %d,  Profile: %d,  LevType: %d,  "
									"New Fixed: %d, New Lineal: %d Needed: %d\n",
									k, i, slotGp[n].rackReq[i*numRanks+k].profileID,slotGp[n].rackReq[i*numRanks+k].levelType,
									slotGp[n].rackReq[i*numRanks].facingCount, slotGp[n].rackReq[i*numRanks].linealFacingWidth,
									*bayFixedNeeded);
							}

							//if (SLOT_DEBUG) printf( " SlGp %d uses Aisle %d(%10.10s), Bay %d(%10.10s), Type %d\n",
							//	slotGp[n].dbID, p3BayLevels[j].aisleID, p3BayLevels[j].aisleDesc,p3BayLevels[j].bayID,p3BayLevels[j].bayDesc, p3BayLevels[j].profileID);
						}	// end if profile
					}	// end for each set
				}	// end for each ranking



			}	// end bays

				//////////////////////////////////////////////////////////////////////
				// mark this aisle as checked
				//////////////////////////////////////////////////////////////////////
				if ( debugLog )
					fprintf(p3TraceFile,"<DBGMSG> Marking Aisle as checked\n");
				for ( j = 0; j < numAisles; j++ ) {
					if ( p3CheckAisles[j].aisleID == currentAisleID )  {
						p3CheckAisles[j].haveChecked = 1;
						break;
					}
				}
				//////////////////////////////////////////////////////////////////////
				// check to see if we have more requirements
				//////////////////////////////////////////////////////////////////////
				if ( debugLog )
					fprintf(p3TraceFile,"<DBGMSG> Checking if Requirements Fulfilled\n");
				if (times < 2)
					needMorePG = 1;
				else {
					needMorePG = 0;
					for ( i = 0; i < slotGp[n].totReqProfiles && needMorePG == 0; i++ )  {
						if ( slotGp[n].rackReq[i*numRanks].linealFacingWidth > 0 || slotGp[n].rackReq[i*numRanks].facingCount > 0 )
							needMorePG = 1;
					}
				}

		}	// end aisles

	}	// end numtimes

	if ( debugLog ) {
		if ( needMorePG == 1 )
			fprintf(p3TraceFile,"<DBGMSG> Completed Processing - Requirements Remaining in Product Group\n");
		else
			fprintf(p3TraceFile,"<DBGMSG> Completed Processing - Requirements Fulfilled in Product Group\n");
	}
	return;

}// End of assignGroup()
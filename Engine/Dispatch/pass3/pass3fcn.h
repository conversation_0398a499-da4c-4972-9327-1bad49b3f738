//////////////////////////////////////////////////////////////////////
// Function Name :	pass3fcn.h
// Classname :		
// Description :	Header for pass3fcn.cpp.
// Date Created :	~5/1/98
// Author : 		sc
//////////////////////////////////////////////////////////////////////
// Inputs :			None
// Outputs :		None
// Explanation :	Header for pass3fcn.cpp.
//////////////////////////////////////////////////////////////////////
// Revisions : 
//   November 6, 1998-mfs : Standard header comments added
//////////////////////////////////////////////////////////////////////
/*******************************************************
   *
   *                       NOTICE
   *
   *  THIS SOFTWARE IS THE PROPERTY OF AND CONTAINS
   *  CONFIDENTIAL INFORMATION OF SSA GLOBAL 
   *  TECHNOLOGIES, INC., AND SHALL NOT BE COPIED,
   *  USED, NOR DISCLOSED WITHOUT EXPRESS WRITTEN
   *  AUTHORIZATION.  ALTHOUGH PUBLICATION IS NOT
   *  INTENDED, IN THE EVENT OF PUBLICATION, THE
   *  FOLLOWING NOTICE IS APPLICABLE:
   *
   *  (c) COPYRIGHT 2004 SSA GLOBAL TECHNOLOGIES, INC.
   *
   *           SSA GLOBAL TECHNOLOGIES, INC.
   *
   ********************************************************/
//////////////////////////////////////////////////////////////////////

#ifndef PASS3FCN
#define PASS3FCN

/* ************************************************************* */
/* This file defines the exported functions that pass4 offers to */
/* the dispatch unit.                                            */
/* ************************************************************* */

/* ***************************************************** */
/* The threadstart function is the one that the dispatch */
/* module will call when someone asks for an instance of */
/* pass4.  This will act as the 'main' function for a    */
/* standalone running of pass1.                          */
/* ***************************************************** */
void *pass3threadstart(void *args);


#endif // PASS1FCN defined.

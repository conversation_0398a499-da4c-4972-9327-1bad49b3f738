#ifndef BASELINEPROCESS
#define BASELINEPROCESS

#include "..\..\..\common\core\socket_class.h"
#include "..\dispatch.h"
//#define BUFSIZE 1024

#define MIN_TO_HOUR 1.0f/60.0f
#define HOUR_TO_MIN 60.0f
#define BAY_TYPE_FLOW 4

typedef struct {
	// Prod Vars:
	char    ProdDesc[250];
	double   height;
	double   width;
	double   length;
	double   movement;
	int     racktype;
	int     NumInPallet;
	double   weight;
	double   cube;
	// Loc Vars:
	int     SecID;
	int     BayType;
	int     RelLev;
	double   LevTime;
	char    Loc_desc[250];
	double     Loc_w;
	double     Loc_d;
	double     Loc_h;
	int     Loc_x;
	int     Loc_y;
	int     Loc_z;
	int		BayProfID;
	// added 8/19/99 - UO<PERSON> changes
	int		SolutionID;
	int		unitOfIssue;
	int		caseQuantity;
	int		productTi;
	int		productHi;
	int		handlingMethod;
	double	innerWidth;
	double	innerLength;
	double	innerHeight;
	double	innerWeight;
	double	eachWidth;
	double	eachLength;
	double	eachHeight;
	double	eachWeight;
	double	caseWidth;
	double	caseLength;
	double	caseHeight;
	double	caseWeight;
	double	caseMovement;
	double	palletMovement;
	double	palletWeight;
	double	casePack;
	double	caseCube;
	double	innerPack;
	double	forkFixedInsertion;
	double	forkFixedExtraction;
	double	containerWidth;
	double	containerLength;
	double	containerHeight;
	char	WMSProdID[256];
	char	WMSProdDetID[256];
	int prodDBID;
	int locDBID;
	int isPrimary;
	double rotatedWidth;
	double rotatedLength;
	double rotatedHeight;
} baselineProdLoc;

typedef struct {
	int     loc_dbID;
	char    loc_desc[250];
	int     prod_dbID;
	char    prod_desc[250];
	double   cost;
} baselineResult;

typedef struct {
	int     SecID;
	char    Sec_desc[250];
	double   ForkFxd;
	double   ForkVar;
	double   ForkRate;
	double   SelFxd;
	double   SelVar;
	double   SelRate;
	double   AvgReplenDist;
	double   TotalMovement;
	int     ForkHotX;
	int     ForkHotY;
	int     ForkHotZ;
	int     SelHotX;
	int     SelHotY;
	int     SelHotZ;
	double   CubeConversion; // (12*12*12) = english (1000000) = metric
	int     SelDist;
	int     AvgOrdQty;
	int     ContainerQty;
	int     OrderCount;
	int     ApplyBrokenOrder;  // 0 = False, 1 = True
	int     ave_x;
	int     ave_y;
	int     loc_count;
	// added 8/19/99 - UOI changes
	double	numPutsPerTrip;
	double	pickForkTrav;
	double	insertForkTrav;
	double	totalExtendedCube;
	double	stockerRate;

} ssaSection;


typedef struct {
	int     RTid;
	int		ProfId;
	int     RelLev;
	double   Cube;
	double   Var;
	double   Fxd;
} ssaLaborLevel;	

typedef struct {
	char   desc[250];
	double  cube;
	int    numInPallet;
	double  movement;
	double  weight;
	int    BayProfID;
	//added 8/20/99 - UOI changes
	double	caseMovement;
	double	palletMovement;
	double	caseCube;
	int		unitOfIssue;
	int		productTi;
	int		productHi;
	int		NumInPallet;
	//added 10/22/99 - brd
	double   caseWeight;
	char	WMSProdID[256];
	char	WMSProdDetID[256];
	int prodDBID;
	double rotatedWidth;
	double rotatedLength;
	double rotatedHeight;
} baselineLaborProd;

typedef struct {
	// Loc Vars:
	char   desc[250];
	int    x;
	int    y;
	int    z;
	double    w;
	double    d;
	double    h;
	// Level Vars:
	int    level;
	double  fork_fixed_insertion;
	// Section Vars:
	double  avg_replen_dist;
	double  fork_dist_var;
	double  fork_dist_fxd;
	double  fork_rate;
	double  sel_dist_var;
	double  sel_dist_fxd;
	double  sel_rate;
	double  TotalMovement;
	// Hot Spots for the section:
	int    fork_hot_x;
	int    fork_hot_y;
	int    fork_hot_z;
	int    sel_hot_x;
	int    sel_hot_y;
	int    sel_hot_z;  
	int    section_idx;
	int	   BayProfID;
	// added 10/22/99 - brd
	int    BayType;
	// added 8/19/99 - UOI changes
	double  numPutsPerTrip;
	double  pickForkTrav;
	double  insertForkTrav;
	double  totalExtendedCube;
	int	   handlingMethod;	
	double  stockerRate;
	double  forkFixedInsertion;
	double  forkFixedExtraction;
	int	   solutionID;
	int locDBID;
	int isPrimary;
} baselineLaborLoc;



/* *************************************************************** */
/* This is the header definition for the class that encapsulates   */
/* the baseline functionality.  Everything is started by instantiating*/
/* a copy of this class and then calling the execute method.  The  */
/* class handles everything else from there.                       */
/* *************************************************************** */


class DISPATCH_API BaselineProcess {
	public:
		BaselineProcess(SockClass *BaselineSock);
		virtual ~BaselineProcess();
	
		void Execute(void);


	protected:
		void GetInitialization(void);
		void GetOneSection(int i);
		void GetOneLaborSelect(int i);
		void GetOneLaborStocker(int i);
		int GetProducts(void);
		void GetOneProd(int idx);
		double RunComp(void);
		void SendData(void);
//		double CalcLabor(baselineLaborProd *p, baselineLaborLoc *l);
		double CalcLabor(baselineLaborProd *p, baselineLaborLoc *l, int casesInPick);
		int  FindRTLevCubeSelect(int ProfID, int Lev, double cube);
		int  FindRTLevCubeStocker(int ProfID, int Lev, double cube);
		int  GetSectionIdx(int SecID);
		double GetCost(int prodidx, int locidx);

		SockClass *BaselineSock;

		baselineProdLoc *mem_locs;
		int     mem_loc_count;

		ssaSection *mem_sections;
		int         mem_section_count;
		
		ssaLaborLevel *mem_labor_select;
		int            mem_labor_select_count;

		ssaLaborLevel *mem_labor_stocker;
		int            mem_labor_stocker_count;

		int mem_avg_x;
		int mem_avg_y;
		int mem_current_section_id;
		char logPath[256];

private:
	double distance(double x1, double y1, double x2, double y2);
}; 



#endif // BASELINEPROCESS defined

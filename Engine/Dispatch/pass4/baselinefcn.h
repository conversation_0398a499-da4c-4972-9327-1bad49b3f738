//////////////////////////////////////////////////////////////////////
// Function Name :	BaselineFcn.h
// Classname :		
// Description :	Header for BaselineFcn.cpp
// Date Created :	~9/1/98
// Author : 		sc
//////////////////////////////////////////////////////////////////////
// Inputs :			None
// Outputs :		None
// Explanation :	Header for BaselineFcn.cpp.
//////////////////////////////////////////////////////////////////////
// Revisions : 
//   November 6, 1998-mfs : Standard header comments added
//////////////////////////////////////////////////////////////////////
/*******************************************************
   *
   *                       NOTICE
   *
   *  THIS SOFTWARE IS THE PROPERTY OF AND CONTAINS
   *  CONFIDENTIAL INFORMATION OF SSA GLOBAL 
   *  TECHNOLOGIES, INC., AND SHALL NOT BE COPIED,
   *  USED, NOR DISCLOSED WITHOUT EXPRESS WRITTEN
   *  AUTHORIZATION.  ALTHOUGH PUBLICATION IS NOT
   *  INTENDED, IN THE EVENT OF PUBLICATION, THE
   *  FOLLOWING NOTICE IS APPLICABLE:
   *
   *  (c) COPYRIGHT 2004 SSA GLOBAL TECHNOLOGIES, INC.
   *
   *           SSA GLOBAL TECHNOLOGIES, INC.
   *
   ********************************************************/
//////////////////////////////////////////////////////////////////////

#ifndef BASELINEFCN
#define BASELINEFCN

/* ************************************************************* */
/* This file defines the exported functions that pass4 offers to */
/* the dispatch unit.                                            */
/* ************************************************************* */

/* ***************************************************** */
/* The threadstart function is the one that the dispatch */
/* module will call when someone asks for an instance of */
/* baseline. This will act as the 'main' function for a  */
/* standalone running of baseline.                       */
/* ***************************************************** */
void *baselinethreadstart(void *args);


#endif // BASELINEFCN defined.

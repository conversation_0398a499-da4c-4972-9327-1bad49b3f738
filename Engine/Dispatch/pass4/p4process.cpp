/////////////////////////////////////////////////////////////////////
// Function Name :	P4Process.cpp
// Classname :		
// Description :	The Pass 4 process.  Assigns Products within a
//					Product Group to Locations within the Bay
//					range(s) assigned to it in Pass 3.
// Date Created :	~5/1/98
// Author : 		sc
//////////////////////////////////////////////////////////////////////
// Inputs :			None
// Outputs :		None
// Explanation :	The Execute function passes no parameters in
//					or out as such.  Instead, Rack Labor, Product
//					and Bay/Level/Location data arrive through a
//					socket connection to the Succeed session module.
/* ******************************************************** */
/* Here is the order of operations:                         */
/*    1) Anounce via the socket that we are ready.          */
/*    2) Receive the data that we need:                     */
/*         List of Products.                                */
/*         List of Bay/Level/Location structures.           */
/*    3) Potentially receive product ranking rule.          */
/*    4) Order the products and order the locations,        */
/*    5) Run through products and locs, filling the later   */
/*       with the former until all products or locs have    */
/*       been used.                                         */
/*    6) Return the data back to the caller.                */
/* ******************************************************** */
//////////////////////////////////////////////////////////////////////
// Revisions : 
//   November 6, 1998-mfs : Standard header comments added
//   February - March 1999 - mfs : Add Case Reorientation and Variable
//                                 Width Locations for Succeed version
//                                 1.1
//////////////////////////////////////////////////////////////////////
/*******************************************************
   *
   *                       NOTICE
   *
   *  THIS SOFTWARE IS THE PROPERTY OF AND CONTAINS
   *  CONFIDENTIAL INFORMATION OF SSA GLOBAL 
   *  TECHNOLOGIES, INC., AND SHALL NOT BE COPIED,
   *  USED, NOR DISCLOSED WITHOUT EXPRESS WRITTEN
   *  AUTHORIZATION.  ALTHOUGH PUBLICATION IS NOT
   *  INTENDED, IN THE EVENT OF PUBLICATION, THE
   *  FOLLOWING NOTICE IS APPLICABLE:
   *
   *  (c) COPYRIGHT 2004 SSA GLOBAL TECHNOLOGIES, INC.
   *
   *           SSA GLOBAL TECHNOLOGIES, INC.
   *
   ********************************************************/
//////////////////////////////////////////////////////////////////////

// disable warning C4786: symbol greater than 255 character,
// okay to ignore
#pragma warning(disable: 4786)


#include <stdlib.h>
#include <stdio.h>
#include <math.h>
#include <errno.h>
#include <direct.h>
#include <map>
#include <time.h>
#include <algorithm>

#include "P4process.h"
#include "..\..\..\common\core\debug.h"
#include "..\..\..\common\core\exceptions.h"

#include "../common/UtilityHelper.h"

#include "..\DataStream.h"


using namespace std;

#define SHOW_TRACE 0


#define CONSTRAIN_BY_PCT 0
#define CONSTRAIN_BY_AMT 1
#define TIMESTAMP 0

const COST_BUFFER_SIZE = 2560;


extern int debugLog;
extern int userLog;
extern CUtilityHelper utilityHelper;

int pgFacingCount;

double optRatio = 1;
int followPickPath = 0;

double p4_inches_to_foot = 12.0f;

int prodNumericSortAscend( const void *p1, const void *p2 );
int prodNumericSortDescend( const void *p1, const void *p2 );
int prodAlphaSortAscend( const void *p1, const void *p2 );
int prodAlphaSortDescend( const void *p1, const void *p2 );
int LevelLaborSort( const void *p1, const void *p2 );
int weightSort(const void *l1, const void *l2);

int bayComp(const void *b1, const void *b2);
int vwComp(const void *loc1, const void *loc2);
int numAvailRanks=1;

int numEach, numInner, numCase, numPallet, dominantUOI = 0;
double avgWidth,avgLength,avgHeight,avgWeight,avgMovement,avgNumInPallet,avgCaseCube,avgCaseMovement = 0.0;
double avgPalletMovement = 0.0;
double holdExtendedCube = 0.0;
map<int, int, less<int> > locFacingMap;

int gProdIdx, gLocIdx;


Pass4Process::Pass4Process(SockClass *sock)
{
	/* ******************************************************** */
	/* This is the socket that we will use to get our data.     */
	/* ******************************************************** */
	p4sock = sock;
	
	/* ********************************************* */
	/* Allocate memory to hold the Sections.         */
	/* ********************************************* */
	
	mem_loc_count = 0;
	mem_prod_count = 0;
	mem_result_count = 0;
	//	mem_result_old_count = 0;
	mem_result_max = 0;
	vw_loc_count = 0;
	vw_loc_max = 0;
	vw_aisle_count = 0;
	vw_aisle_max = 0;
	variableWidthAllowed = FALSE;
	caseReorientAllowed = FALSE;
	optType = 0;
	sortOptFlag = 0;
	memset(optMsgString,0,257);
	tacticalSlotting = false;
	mem_groupassignment_count = 0;
	mem_groupassignment_max = 0;
	mem_tacticalinfo_count = 0;
	mem_tacticalinfo_max = 0;
	mem_bayweight_count = 0;
	mem_levelweight_count = 0;
	mem_aisleentryexit_count = 0;
	
	
}

Pass4Process::~Pass4Process()
{
	
	/* ******************************************************** */
	/* Make sure that we clean up the memory and objects that   */
	/* we have used.  Clean up the socket as well.              */
	/* ******************************************************** */
	
	delete p4sock;
	if(mem_section_count > 0){
		free(mem_sections);
	}
	
	if(mem_labor_select_count > 0){
		free(mem_labor_select);
	}
	
	
	if(mem_labor_stocker_count > 0){
		free(mem_labor_stocker);
	}
	
	if(mem_result_count > 0){
		free(mem_result);
	}
	
	if(vw_loc_count > 0){
		free(vwLoc);
	}
	
	if(vw_aisle_count > 0){
		free(vwAisle);
	}
	
	if (mem_bayweight_count >= 0) {
		free(mem_bay_weights);
	}
	
	if (mem_levelweight_count >= 0) {
		free(mem_level_weights);
	}
	
	
	if (mem_groupassignment_count > 0) {
		free(mem_group_assignments);
	}
	
	if (mem_tacticalinfo_count > 0) {
		free(mem_tactical_info);
	}
	
	if (mem_aisleentryexit_count > 0) {
		free(mem_aisleentryexits);
	}
	
	
}

void Pass4Process::Execute(void)
{
	char buffer[BUFSIZE];
	unsigned int  err;
	int i;
	double CostSum;
	
	/* ******************************************************** */
	/* Here is the order of operations:                         */
	/*    1) Anounce via the socket that we are ready.          */
	/*    2) Receive the data that we need:                     */
	/*         List of Products.                                */
	/*         List of Bay/Level/Location structures.           */
	/*    3) Potentially receive product ranking rule.          */
	/*    4) Order the products and order the locations,        */
	/*    5) Run through products and locs, filling the later   */
	/*       with the former until all products or locs have    */
	/*       been used.                                         */
	/*    6) Return the data back to the caller.                */
	/* ******************************************************** */
	
	/* ******************************************************** */
	/* Announcement to the caller that we are ready.            */
	/* ******************************************************** */
	
	fprintf(stdout, "Starting Product Layout Optimization.\n");
	TimeStamp("Starting Optimization\n");
	
	CostSum = 0;
	// Look up window registry to find the default first
	HKEY hRegKey;
	logPath[0] = 0;
	DWORD dwType = REG_SZ;
	DWORD dwReturnLength;

	if (RegOpenKeyEx(HKEY_LOCAL_MACHINE, "Software\\SSA Global\\Optimize", 0, KEY_READ, &hRegKey) == ERROR_SUCCESS) {
		int i = 0;
		while ((RegQueryValueEx(hRegKey, "LogFilePath", NULL, &dwType,(LPBYTE)logPath, &dwReturnLength) != ERROR_SUCCESS ) && i<=100)
				i++;
		if (i>100)
			logPath[0] = 0;
	}
	RegCloseKey(hRegKey);

	if ( logPath[0] == 0 )
		strcpy(logPath, ".\\Log");					// MFS 2Mar06 Value missing, default to \Optimize\Log

	if (_mkdir(logPath) != 0) {						// MFS 2Mar06 Try to create/open directory
		if (errno != EEXIST) {						// If that doesn't work...
			if (_stricmp(logPath, ".\\Log")==0)		// Try default unless we already tried it.
				strcpy(logPath, ".");				// Use current dir. as final fallback.
			else
				strcpy(logPath, ".\\Log");
		}
	}

	fileName[0]=0;
	strcpy(fileName, logPath);
	strcat(fileName, "\\");
	strcat(fileName, "P4DummyProducts.out");
	dummy_file = fopen(fileName, "w");
	if (dummy_file == NULL)
		fprintf(stdout, "Cannot create file: %s.  Check disk space on device.\n", fileName);
	else {
		char fullPath[1024];
		if (_fullpath(fullPath, fileName, 1024) != NULL)
			fprintf(stdout, "Cost Analysis input file: %s\n", fullPath);
		else
			fprintf(stdout, "Cost Analysis input file: %s\n",fileName);
	}
	
	//CHECK007 	We don't use socket any more... No need to handshake...
	/// memset(buffer, 0, BUFSIZE);
	/// sprintf(buffer,"Ready for data...\n<EOS>\n");

	/***>>>
	err = p4sock->SendData(buffer, strlen(buffer));
	<<<***/
	/// gfnGetDataStream()->ssData << buffer;
	/***>>>
	if(err < strlen(buffer))
		throw EngineException("Error Sending AOK Signal",
		__FILE__, __LINE__, 200);
	<<<***/
	
	try {
		
		variableWidthAllowed = FALSE;
		caseReorientAllowed = FALSE;
		
		TimeStamp("Before initialization\n");
		// GetInitialization(); //called in OptiServer right after filling the buffer with the
		// initialization data.
		TimeStamp("Before main while loop\n");
		
		while (1) {
			
			if ( debugLog )
				fprintf(p4TraceFile,"\n\n--------------- New Product Group --------------\n");
			
			mem_loc_count = 0;
			mem_prod_count = 0;
			mem_result_count = 0;
			///////////////////////////////////////////////////////////////////////
			// <TACTICAL>
			///////////////////////////////////////////////////////////////////////
			//			mem_result_old_count = 0;
			mem_result_max = 0;
			vw_loc_count = 0;
			vw_loc_max = 0;
			vw_aisle_count = 0;
			vw_aisle_max = 0;
			
			maxLocHeight = 0;
			maxLocWidth = 0;
			maxLocDepth = 0;
			maxPalLocHeight = 0;
			maxPalLocWidth = 0;
			maxPalLocDepth = 0;
			
			
			p4Messages = NULL;
			numEach = 0;
			numInner = 0;
			numCase = 0;
			numPallet = 0;
			avgHeight = 0.0;
			avgWidth = 0.0;
			avgLength = 0.0;
			avgWeight = 0.0;
			avgMovement = 0.0;
			avgNumInPallet = 0.0;
			avgPalletMovement = 0.0;
			holdExtendedCube = 0.0;
			avgCaseCube = 0.0;
			avgCaseMovement = 0.0;
			
			/* ************************************************ */
			/* Receive data that will drive this calculation    */
			/* ************************************************ */
			if ( debugLog )
				fprintf(p4TraceFile,"Getting products from database\n");
			
			TimeStamp("Before GetProducts\n");
			if (GetProducts() < 0)
				break;
			
			if ( debugLog )
				fprintf(p4TraceFile,"Received %d products.\n", mem_prod_count);
			
			if (mem_prod_count == 0)
				continue;
			
			//////////////////////////////////////////////////////////////////////
			// determine major UOI passed and accumulate the dimensions, etc.
			// based on the major UOI so that we can prioritize the locations with
			// the "dummy" product
			//////////////////////////////////////////////////////////////////////
			if ( numEach >= numInner && numEach >= numCase && numEach >= numPallet )
				dominantUOI = 0;
			else if ( numInner >= numEach && numInner >= numCase && numInner >= numPallet )
				dominantUOI = 1;
			else if ( numCase >= numEach && numCase >= numInner && numCase >= numPallet )
				dominantUOI = 2;
			else
				dominantUOI = 3;
			
			if ( debugLog )
				fprintf(p4TraceFile,"Most common UOI = %d\n",dominantUOI);
			
			//////////////////////////////////////////////////////////////////////
			// accumulate values
			//////////////////////////////////////////////////////////////////////
			numAvailRanks = 1;
			TimeStamp("Looping through products\n");
			
			for ( i = 1;  i < mem_prod_count && mem_prods[i].ranking != 1; i++)
				numAvailRanks++;
			
			numMessages = (int)mem_prod_count/numAvailRanks+1;
			
			if ( debugLog )
				fprintf(p4TraceFile, "Maximum number of rankings: %d\n",
				numAvailRanks);
			
			p4Messages = (P4Message*)(malloc(sizeof(P4Message) * (numMessages)));
			if ( p4Messages == NULL ) {
				throw EngineException("Error Allocating Message Buffer",
					__FILE__, __LINE__, 200);
			}
			memset(p4Messages,0,sizeof(P4Message)*numMessages);
			
			
			
			for ( i = 0; i < mem_prod_count; i++ ) {
				avgNumInPallet += mem_prods[i].NumInPallet;
				avgCaseCube += mem_prods[i].caseWidth * mem_prods[i].caseLength * mem_prods[i].caseHeight;
				avgCaseMovement += mem_prods[i].caseMovement;
				avgPalletMovement += mem_prods[i].palletMovement;
				switch ( dominantUOI )
				{
				case 0 : // each
					{
						avgWidth += mem_prods[i].eachWidth;
						avgLength += mem_prods[i].eachLength;
						avgHeight += mem_prods[i].eachHeight;
						avgMovement += mem_prods[i].caseMovement * mem_prods[i].casePack;
						avgWeight += mem_prods[i].eachWeight;
						break;
					}
				case 1 : // inner
					{
						avgWidth += mem_prods[i].innerWidth;
						avgLength += mem_prods[i].innerLength;
						avgHeight += mem_prods[i].innerHeight;
						avgMovement += mem_prods[i].caseMovement * mem_prods[i].casePack / mem_prods[i].innerPack;
						avgWeight += mem_prods[i].innerWeight;
						break;
					}
				case 2 : // case
					{
						avgWidth += mem_prods[i].caseWidth;
						avgLength += mem_prods[i].caseLength;
						avgHeight += mem_prods[i].caseHeight;
						avgMovement += mem_prods[i].caseMovement;
						avgWeight += mem_prods[i].caseWeight;
						break;
					}
				case 3 : // pallet
					{
						avgWidth += mem_prods[i].containerWidth;
						avgLength += mem_prods[i].containerLength;
						if ( mem_prods[i].contOverrideHeight == 1 )
							avgHeight += mem_prods[i].containerHeight;
						else
							avgHeight += mem_prods[i].containerHeight + ( mem_prods[i].caseHeight * mem_prods[i].productHi );
						avgMovement += mem_prods[i].palletMovement;
						avgWeight += mem_prods[i].palletWeight;
						break;
					}
				}
				
			}
			
			
			//////////////////////////////////////////////////////////////////////
			// average out the dimensions, etc.
			//////////////////////////////////////////////////////////////////////
			// added 8/19/99 - UOI changes
			avgWidth /= mem_prod_count;
			avgLength /= mem_prod_count;
			avgHeight /= mem_prod_count;
			avgWeight /= mem_prod_count;
			avgMovement /= mem_prod_count;
			avgNumInPallet /= mem_prod_count;
			avgCaseMovement /= mem_prod_count;
			avgCaseCube /= mem_prod_count;
			avgPalletMovement /= mem_prod_count;
			holdExtendedCube = avgCaseCube * avgCaseMovement;
			
			if ( debugLog ) {
				fprintf(p4TraceFile,"[AvgWidth=%f] [AvgLength=%f] [AvgHeight=%f]\n",
					avgWidth, avgLength, avgHeight);
				fprintf(p4TraceFile,"[AvgMvmt=%f] [AvgNumInPal=%f] [AvgCaseMvmt=%f]\n",
					avgMovement, avgNumInPallet, avgCaseMovement);
				fprintf(p4TraceFile,"[AvgCaseCube=%f] [AvgPalletMvmt=%f] [HoldXCube=%f]\n",
					avgCaseCube, avgPalletMovement, holdExtendedCube);
				fprintf(p4TraceFile,"[AvgWeight=%f]\n",avgWeight);
			}
			
			if ( debugLog )
				fprintf(p4TraceFile,"\nGetting locations from database\n");
			
			TimeStamp("Before GetAisleLoc\n");
			if (GetAisleLoc() <= 0){
				memset(buffer, 0, BUFSIZE);
				sprintf(buffer, "<EOS>\n", CostSum);

				/***>>>
				err = p4sock->SendData(buffer, strlen(buffer));
				<<<***/
				gfnGetDataStream()->ssData << buffer;
				/***>>>
				if(err < strlen(buffer))
					throw EngineException("Error Sending Success Signal",
					__FILE__, __LINE__, 200);
				<<<***/

				continue;
			}
			if ( debugLog )
				fprintf(p4TraceFile,"Received %d locations.\n", mem_loc_count);
			
			
			if (optimizeType == TACTICAL_LAYOUT) {
				GetAisleEntryExit();
			}
			
			/* ************************************************ */
			/* Get the information that allows overlapping      */
			/* products, and optimization by either cost or     */
			/* cube then cost                                   */
			/* ************************************************ */
			
			TimeStamp("Before GetOptimizationFlags\n");
			GetOptimizationFlags();
			
			/* ************************************************ */
			/* Ensure ordering is correct.                      */
			/* ************************************************ */
			TimeStamp("Before ResetLists\n");
			ResetLists();
			
			fflush(p4TraceFile);
			
			if (mem_prod_count > 0 && mem_loc_count > 0) {
				printf("Estimated open locations,  percentage:%d,  %.02f\n", 
					(mem_loc_count-pgFacingCount), 
					(double)(mem_loc_count-pgFacingCount)/(double)mem_loc_count * 100);
			}
			
			
			
			/* ************************************************ */
			/* Do the actual computation.                       */
			/* ************************************************ */
			
			switch (optimizeType) {
			case STRATEGIC_LAYOUT:
				TimeStamp("Before RunComp\n");
				if (ignoreRankingsFlag == 2)
					ignoreRankings = 2;
				else
					ignoreRankings = 0;
				CostSum += RunComp();
				
				if (ignoreRankingsFlag == 1) {
					ignoreRankings = 1;
					CostSum += RunComp();
				}
				
				TimeStamp("After RunComp\n");
				break;
			case GROUP_LAYOUT:
				ProcessSolutions();
				CostSum += GroupRunComp();
				break;
			case TACTICAL_LAYOUT:
				// MFS 21Mar06 Too late to make Tactical work for 3.0.  The function
				// ExecuteP4Query(), which is used in two places, attempts to use the
				// socket connection which no longer exists.  That attempt corrupts
				// the stream in the new version, causing a crash.  All nine of the
				// subprocesses of Tactical are unique from the other pass 4 routines.
				// Estimate 2-4 person weeks development work to revive.
				// ProcessSolutions();
				// GetExtendedValues();
				// CostSum += TacticalRunComp();
				break;
			case NEW_PRODUCT_LAYOUT:
				ProcessSolutions();
				CostSum += GroupRunComp();
			default:
				break;
			}
			
			if ( debugLog )
				fprintf(p4TraceFile,"Completed assignments.  Cost is %.02f\n", CostSum);
			
			int assignedCount = 0;
			for (i=0; i < mem_loc_count; ++i) {
				if (mem_locs[i].Assigned)
					assignedCount++;
			}
			
			printf("Actual open locations,  percentage: %d,  %.02f\n", (mem_loc_count-assignedCount), 
				(double)(mem_loc_count-assignedCount)/(double)mem_loc_count * 100);
			
			
			if ( debugLog )		
				fprintf(p4TraceFile,"Sending results to database\n");
			TimeStamp("Before SendData\n");
			SendData();
			TimeStamp("After SendData\n");
			if ( debugLog ) {
				fprintf(p4TraceFile,"Finished sending results\n");
				fprintf(p4TraceFile, "Sending %d messages\n", numMessages);
			}
			
			for ( i = 0; i < numMessages && p4Messages[i].prodPkDBID != 0; i++ ) {
				
				sprintf(buffer,"M|%d|%s|%s|%s|%s|\n",p4Messages[i].prodPkDBID,p4Messages[i].prodDesc,
					p4Messages[i].WMSProdID, p4Messages[i].WMSProdDetID,p4Messages[i].msg);
				
				if (debugLog)
					fprintf(p4TraceFile, "Sending data: %s\n", buffer);
				
				/***>>>
				err = p4sock->SendData(buffer, strlen(buffer));
				<<<***/
				
				gfnGetDataStream()->ssData << buffer;

				/***>>>
				if(err != strlen(buffer)){
					throw EngineException("Error sending Message Info",
						__FILE__, __LINE__, 200);
				}
				<<<***/
			}
			memset(buffer, 0, BUFSIZE);
			sprintf(buffer, "<EOS>\n");
			/***>>>
			err = p4sock->SendData(buffer, strlen(buffer));
			<<<***/
			gfnGetDataStream()->ssData << buffer;
			/***>>>
			if(err != strlen(buffer)){
				throw EngineException("Error sending results",
					__FILE__, __LINE__, 200);
			}
			<<<***/
			
			/* ************************************************ */
			/* Re-initialize all of our variables.              */
			/* ************************************************ */
			
			if(mem_loc_count > 0)
				free (mem_locs);
			
			if(mem_prod_count > 0)
				free (mem_prods);
			
			if(mem_result_count > 0){
				free(mem_result);
				mem_result_max = 0;
				mem_result_count = 0;
			}
			
			if(vw_loc_count > 0){
				free(vwLoc);
				vw_loc_max = 0;
				vw_loc_count = 0;
			}
			
			if(vw_aisle_count > 0){
				free(vwAisle);
				vw_aisle_count = 0;
				vw_aisle_max = 0;
			}
			
			if(mem_solution_count > 0) {
				free(mem_solutions);
				mem_solution_count = 0;
			}
			
			if ( p4Messages != NULL ) {
				free(p4Messages);
			}
			
			
			if ( debugLog ) {
				fprintf(p4TraceFile,"Freed temporary memory\n");
				fflush(p4TraceFile);
			}
		}
	} catch(EngineException ee) {
		printf("Caught an exception\n");
		ee.GetAllMessage(buffer);
		printf("%s", buffer);
		
		if (p4TraceFile != NULL)
			fflush(p4TraceFile);
		
		fprintf(stdout,"Product Layout Optimization failed (1).\n");
		
		if(mem_loc_count > 0) {
			mem_loc_count = 0;
			free (mem_locs);
		}
		
		if(mem_prod_count > 0) {
			free (mem_prods);
			mem_prod_count = 0;
		}
		
		if(mem_result_count > 0) {
			free(mem_result);
			mem_result_count = 0;
		}
		
		///////////////////////////////////////////////////////////////////////
		// <TACTICAL>
		///////////////////////////////////////////////////////////////////////
		//		if(mem_result_old_count > 0) {
		//			free(mem_result_old);
		//			mem_result_old_count = 0;
		//		}
		
		if(vw_loc_count > 0) {
			free(vwLoc);
			vw_loc_count = 0;
		}
		
		if(vw_aisle_count > 0) {
			free(vwAisle);
			vw_aisle_count = 0;
		}
		
		if(mem_solution_count > 0) {
			free(mem_solutions);
			mem_solution_count = 0;
		}
		char msgText[256];
		ee.GetMessage(msgText);
		memset(buffer, 0, BUFSIZE);
		sprintf(buffer, "Error in Engine: %s\n<EOS>\n", msgText);
		/***>>>
		err = p4sock->SendData(buffer, strlen(buffer));
		<<<***/
		gfnGetDataStream()->ssData << buffer;
		/***>>>
		if(err < strlen(buffer))
			throw EngineException("Error Sending ERROR Signal",
			__FILE__, __LINE__, 200);
		<<<***/
		return;
	} catch(...){
		if (p4TraceFile != NULL)
			fflush(p4TraceFile);
		
		if (dummy_file != NULL)
			fflush(dummy_file);
		
		fprintf(stdout, "Product Layout Optimization failed. (2)\n");
		if(mem_loc_count > 0) {
			mem_loc_count = 0;
			free (mem_locs);
		}
		
		if(mem_prod_count > 0) {
			mem_prod_count = 0;
			free (mem_prods);
		}
		
		if(mem_result_count > 0) {
			mem_result_count = 0;
			free(mem_result);
		}
		
		///////////////////////////////////////////////////////////////////////
		// <TACTICAL>
		///////////////////////////////////////////////////////////////////////
		if(mem_solution_count > 0) {
			free(mem_solutions);
			mem_solution_count = 0;
		}
		
		if(vw_loc_count > 0) {
			free(vwLoc);
			vw_loc_count = 0;
		}
		
		if(vw_aisle_count > 0) {
			free(vwAisle);
			vw_aisle_count = 0;
		}
		
		memset(buffer, 0, BUFSIZE);
		sprintf(buffer, "Error in Engine\n<EOS>\n");
		/***>>>
		err = p4sock->SendData(buffer, strlen(buffer));
		<<<***/

		gfnGetDataStream()->ssData << buffer;

		/***>>>
		if(err < strlen(buffer))
			throw EngineException("Error Sending ERROR Signal",
			__FILE__, __LINE__, 200);
		<<<***/
		return;
	}
	
	memset(buffer, 0, BUFSIZE);
	sprintf(buffer, "SuccessfulPass4|%f|\n<EOS>\n", CostSum);
	/***>>>
	err = p4sock->SendData(buffer, strlen(buffer));
	<<<***/
	gfnGetDataStream()->ssData << buffer;
	/***>>>
	if(err < strlen(buffer))
		throw EngineException("Error Sending Success Signal",
		__FILE__, __LINE__, 200);
	<<<***/
	if (dummy_file != NULL)
		fclose(dummy_file);
	
	//fclose(msg_file);
	if ( debugLog )
		fprintf(p4TraceFile,"End of Layout Products Optimization\n");
	if ( p4TraceFile != NULL && p4TraceFile != stdout)
		fclose(p4TraceFile);
	
	fprintf(stdout, "Product Layout Optimization completed successfully.\n");
	
}

/* **************************************************************** */
/* This function will go through the process of grabbing all of the */
/* initialization information that this process will need.  It will */
/* consist of a list of sections and their information, and then a  */
/* list of racktypelabor information.  Both of these array's will   */
/* later be accessed by the pass4 process to look up information.   */
/* **************************************************************** */

void Pass4Process::GetInitialization(void)
{
	char buffer[BUFSIZE];
	unsigned int  err;
	int i;
	
	fprintf(stdout, "\nReceiving options...\n");
	
	optimizeType = STRATEGIC_LAYOUT;
	variableWidthAllowed = caseReorientAllowed = 0;
	constraintType = 0;
	constraintAmount = 0;
	changedOnly = followPickPath = ignoreRankingsFlag = ignoreWeight = overlapProds = 0;
	optRatio = 0;
	std::string logMode = "None";
	utilStart = 0;
	utilDecrement = 0;
	timeHorizonUnits = 0;
	timeHorizonValue = 0;
	p4TraceFile = stdout;
	
	do {
		memset(buffer, 0, BUFSIZE);
		if ( gfnGetDataStream()->ssData.getline(buffer, BUFSIZE) == 0) {
		
			throw EngineException("Error receiving on stream",
				__FILE__, __LINE__, 200);
		}
	} while (strncmp(buffer, "OPTIONS", 7) != 0);
	
	std::string str = buffer+8;	// include one for the pipe
	std::vector<std::string> parmList;
	utilityHelper.ParseString(str, "|", parmList);
	
	// clientName|mode|VarWidth|Rotate|ConstrainType|ConstrainAmount|ChangedOnly|
	// OptRatio|FollowPickPath|IgnoreRankings|IgnoreWeight|Overlap|LogMode|MaxResults|
	// UtilStart|UtilDecrement|TimeHorizonUnit|TimeHorizonValue
	
	strcpy(P4LogUsr, parmList[0].c_str());
	optimizeType = atoi(parmList[1].c_str());
	variableWidthAllowed = atoi(parmList[2].c_str());
	caseReorientAllowed = atoi(parmList[3].c_str());
	m_slotnew = atoi(parmList[4].c_str());
	m_reslot = atoi(parmList[5].c_str());
	m_findswaps = atoi(parmList[6].c_str());
	optRatio = atof(parmList[7].c_str());
	followPickPath = atoi(parmList[8].c_str());
	ignoreRankingsFlag = atoi(parmList[9].c_str());
	ignoreWeight = atoi(parmList[10].c_str());
	overlapProds = atoi(parmList[11].c_str());
	logMode = parmList[12];
	utilStart = atof(parmList[14].c_str());
	utilDecrement = atof(parmList[15].c_str());
	timeHorizonUnits = atoi(parmList[16].c_str());
	timeHorizonValue = atoi(parmList[17].c_str());

	
	if (logMode == "None") {
		userLog = 0;
		debugLog = 0;
	}
	else if (logMode == "User") {
		userLog = 1;
		debugLog = 0;
	}
	else if (logMode == "Expert") {
		userLog = 1;
		debugLog = 1;
	}
	else {
		userLog = 0;
		debugLog = 0;
	}
	
	strcat(P4LogUsr,"P4Trace.log");
	
	if ( userLog || debugLog ) {
		p4TraceFile = NULL;
		// MFS 13Mar06 Look up log path if not yet set.
		if (strlen(logPath)==0) {
			HKEY hRegKey;
			DWORD dwType = REG_SZ;
			DWORD dwReturnLength;

			if (RegOpenKeyEx(HKEY_LOCAL_MACHINE, "Software\\SSA Global\\Optimize", 0, KEY_READ, &hRegKey) == ERROR_SUCCESS) {
				int i = 0;
				while ((RegQueryValueEx(hRegKey, "LogFilePath", NULL, &dwType,(LPBYTE)logPath, &dwReturnLength) != ERROR_SUCCESS ) && i<=100)
						i++;
				if (i>100)
					logPath[0] = 0;
			}
			RegCloseKey(hRegKey);
		}

		strcpy(fileName, logPath);
		strcat(fileName, "\\");
		strcat(fileName, P4LogUsr);
		p4TraceFile = fopen(fileName,"w");
		if (SHOW_TRACE)
			p4TraceFile = stdout;
		if ( p4TraceFile == NULL ) {
			fprintf(stdout, "Cannot create trace file: .  Check disk space on device.\n", fileName);
			fprintf(stdout, "No logging will be printed.\n");
			P4LogModeFlag = 0;
			userLog = debugLog = 0;
		}
		else {
			setvbuf(p4TraceFile, NULL, _IONBF, 0);
			if (! SHOW_TRACE) {
				char fullPath[1024];
				if (_fullpath(fullPath, fileName, 1024) != NULL)
					fprintf(stdout, "Trace file: %s\n", fullPath);
				else
					fprintf(stdout, "Trace file: %s\n",fileName);
			}
		}
	}
	else
		p4TraceFile = stdout;
	
	fprintf(p4TraceFile, "Options\n--------\nUser: %s\nVariable Width: %d\n"
		"Rotation: %d\nConstrain Type: %d\nConstrain Amount: %.02f\n"
		"Changed Only: %d\nOptimization Ratio: %.02f\nPick Path Sequence: %d\n"
		"Ignore Rankings: %d\nIgnore Weight: %d\nOverlap: %d\nLog Mode: %s\n"
		"Utilization Min. Start: %.02f\nUtilization Decrement: %.02f\n"
		"Time Horizon Units: %d   Value: %d\n", 
		parmList[0].c_str(),	variableWidthAllowed, caseReorientAllowed, constraintType,
		constraintAmount, changedOnly, optRatio, followPickPath, ignoreRankingsFlag, ignoreWeight,
		overlapProds, logMode.c_str(), utilStart, utilDecrement, timeHorizonUnits, timeHorizonValue);
	
	/* ********************************************* */
	/* Wait for the start of the Section stream      */
	/* ********************************************* */
	do {
		memset(buffer, 0, BUFSIZE);
		if ( gfnGetDataStream()->ssData.getline(buffer, BUFSIZE) == 0) {
		
			throw EngineException("Error receiving on stream",
				__FILE__, __LINE__, 200);
		}
		if ( debugLog )
			fprintf(p4TraceFile,"\nReceived data: %s",buffer);
	} while (strncmp(buffer, "INITIALIZATION_SECTION", 22) != 0);
	
	mem_section_count = atoi(buffer+23);		
	
	/* ********************************************* */
	/* Allocate memory to hold the Sections.         */
	/* ********************************************* */
	if ( debugLog )
		fprintf(p4TraceFile,"\nReceiving %d sections\n",mem_section_count);
	
	mem_sections = (ssaSection *)malloc(mem_section_count * sizeof(ssaSection));
	if(mem_sections == NULL){
		throw EngineException("Error Allocating memory for section list",
			__FILE__, __LINE__, 300);
	}
	
	memset(mem_sections,0, mem_section_count * sizeof(ssaSection));
	
	for(i=0;i<mem_section_count;i++) {
		GetOneSection(i);	
	}
	// Set Cube Conversion factors
	if ( mem_section_count > 0) {
		if ( mem_sections[0].CubeConversion == 1728.0f)
			p4_inches_to_foot = 12.0f;
		else if ( mem_sections[0].CubeConversion == 1000000.0f)
			p4_inches_to_foot = 100.0f;
		else {
			mem_sections[0].CubeConversion = 1728.0f;
			p4_inches_to_foot = 12.0f;
		}
	} else {
		mem_sections[0].CubeConversion = 1728.0f;
		p4_inches_to_foot = 12.0f;
	}
	if ( debugLog )
		fprintf(p4TraceFile,"Cube Conversion = %10.4f\n",p4_inches_to_foot);
	
	// Selection labor values
	/* ********************************************* */
	/* Wait for the start of the RackType stream     */
	/* ********************************************* */
	do {
		memset(buffer, 0, BUFSIZE);
		if ( gfnGetDataStream()->ssData.getline(buffer, BUFSIZE) == 0) {
		
			throw EngineException("Error receiving on stream",
				__FILE__, __LINE__, 200);
		}
		if ( debugLog )
			fprintf(p4TraceFile,"\nReceived data: %s",buffer);
	} while (strncmp(buffer, "INITIALIZATION_RACKLABORSELECT", 24) != 0);
	
	mem_labor_select_count = atoi(buffer+31);		
	
	/* ********************************************* */
	/* Allocate memory to hold the RackTypes.        */
	/* ********************************************* */
	if ( debugLog )
		fprintf(p4TraceFile,"Received %d select labor items.\n",mem_labor_select_count);
	
	mem_labor_select = (ssaLaborLevel *)malloc(mem_labor_select_count * sizeof(ssaLaborLevel));
	if(mem_labor_select == NULL){
		throw EngineException("Error Allocating memory for labor list",
			__FILE__, __LINE__, 300);
	}
	
	memset(mem_labor_select,0,mem_labor_select_count*sizeof(ssaLaborLevel));
	
	for(i=0;i<mem_labor_select_count;i++) {
		GetOneLaborSelect(i);	
	}
	
	qsort(mem_labor_select,mem_labor_select_count, sizeof(ssaLaborLevel), LevelLaborSort);
	
	
	// Stocker labor values
	/* ********************************************* */
	/* Wait for the start of the RackType stream     */
	/* ********************************************* */
	do {
		memset(buffer, 0, BUFSIZE);
		if ( gfnGetDataStream()->ssData.getline(buffer, BUFSIZE) == 0) {
		
			throw EngineException("Error receiving on stream",
				__FILE__, __LINE__, 200);
		}
		if ( debugLog )
			fprintf(p4TraceFile,"\nReceived data: %s",buffer);
	} while (strncmp(buffer, "INITIALIZATION_RACKLABORSTOCKER", 24) != 0);
	
	mem_labor_stocker_count = atoi(buffer+32);		
	
	/* ********************************************* */
	/* Allocate memory to hold the RackTypes.        */
	/* ********************************************* */
	if ( debugLog )
		fprintf(p4TraceFile,"\nReceived %d stocker labor items.\n",mem_labor_stocker_count);
	
	mem_labor_stocker = (ssaLaborLevel *)malloc(mem_labor_stocker_count * sizeof(ssaLaborLevel));
	if(mem_labor_stocker == NULL){
		throw EngineException("Error Allocating memory for labor list",
			__FILE__, __LINE__, 300);
	}
	
	memset(mem_labor_stocker, 0, mem_labor_stocker_count * sizeof(ssaLaborLevel));
	
	for(i=0;i<mem_labor_stocker_count;i++) {
		GetOneLaborStocker(i);	
	}
	
	qsort(mem_labor_stocker,mem_labor_stocker_count, sizeof(ssaLaborLevel), LevelLaborSort);
	
	
	do {
		memset(buffer, 0, BUFSIZE);
		if ( gfnGetDataStream()->ssData.getline(buffer, BUFSIZE) == 0) {
		
			throw EngineException("Error receiving on stream",
				__FILE__, __LINE__, 200);
		}
		if ( debugLog )
			fprintf(p4TraceFile,"\nReceived data: %s",buffer);
	} while (strncmp(buffer, "TOTAL_BAY_COUNT", 15) != 0);
	
	mem_bayweight_count = (int)atoi(buffer+16);
	
	do {
		memset(buffer, 0, BUFSIZE);
		if ( gfnGetDataStream()->ssData.getline(buffer, BUFSIZE) == 0) {
		
			throw EngineException("Error receiving on stream",
				__FILE__, __LINE__, 200);
		}
		if ( debugLog )
			fprintf(p4TraceFile,"\nReceived data: %s",buffer);
	} while (strncmp(buffer, "TOTAL_LEVEL_COUNT", 17) != 0);
	
	mem_levelweight_count = (int)atoi(buffer+18);
	
	// get all the current product assignments
	GetSolutions();
	
	bay_weight_counter = 0;
	level_weight_counter = 0;
	
	// set up the bay and level weight arrays
	ProcessPreviousBayWeights();
	
}

void Pass4Process::GetOneSection(int idx)
{
	char buffer[BUFSIZE];
	unsigned int  err;
	char *ptr;
	
	/* ******************************************** */
	/* Read the socket information                  */
	/* ******************************************** */
	memset(buffer, 0, BUFSIZE);
	if ( gfnGetDataStream()->ssData.getline(buffer, BUFSIZE) == 0) {
	
		throw EngineException("Error receiving on stream",
			__FILE__, __LINE__, 200);
	}
	
	if ( debugLog )
		fprintf(p4TraceFile,"\nReceived data: %s",buffer);
	
	if(strncmp(buffer, "SECTION", 7) != 0)
		throw EngineException("Corrupt data when expecting section", 
		__FILE__, __LINE__, 200);
	
	std::string s(buffer);
	
	int indx = s.find("||");
	while (indx >= 0) {
		s.insert(indx+1, " ");
		indx = s.find("||");
	}
	
	strcpy(buffer, s.c_str());
	
	ptr = strtok(buffer+8, "|");
	mem_sections[idx].SecID = atoi(ptr);
	ptr = strtok(NULL, "|");
	strcpy(mem_sections[idx].SecDesc, ptr);
	ptr = strtok(NULL, "|");
	mem_sections[idx].ForkFxd = (double)atof(ptr);
	ptr = strtok(NULL, "|");
	mem_sections[idx].ForkVar= (double)atof(ptr);
	ptr = strtok(NULL, "|");
	mem_sections[idx].ForkRate = (double)atof(ptr);
	ptr = strtok(NULL, "|");
	mem_sections[idx].SelFxd = (double)atof(ptr);
	ptr = strtok(NULL, "|");
	mem_sections[idx].SelVar = (double)atof(ptr);
	ptr = strtok(NULL, "|");
	mem_sections[idx].SelRate = (double)atof(ptr);
	ptr = strtok(NULL, "|");
	mem_sections[idx].AvgReplenDist = (double)atof(ptr);
	ptr = strtok(NULL, "|");
	mem_sections[idx].TotalMovement = (double)atof(ptr);
	ptr = strtok(NULL, "|");
	mem_sections[idx].ForkHotX = atoi(ptr);
	ptr = strtok(NULL, "|");
	mem_sections[idx].ForkHotY = atoi(ptr);
	ptr = strtok(NULL, "|");
	mem_sections[idx].ForkHotZ = atoi(ptr);
	ptr = strtok(NULL, "|");
	mem_sections[idx].SelHotX = atoi(ptr);
	ptr = strtok(NULL, "|");
	mem_sections[idx].SelHotY = atoi(ptr);
	ptr = strtok(NULL, "|");
	mem_sections[idx].SelHotZ = atoi(ptr);
	ptr = strtok(NULL, "|");
	mem_sections[idx].CubeConversion = (double)atof(ptr);
	ptr = strtok(NULL, "|");
	mem_sections[idx].SelDist = atoi(ptr);
	ptr = strtok(NULL, "|");
	mem_sections[idx].AvgOrdQty = atoi(ptr);
	ptr = strtok(NULL, "|");
	mem_sections[idx].ContainerQty = atoi(ptr);
	ptr = strtok(NULL, "|");
	mem_sections[idx].OrderCount = atoi(ptr);
	ptr = strtok(NULL, "|");
	mem_sections[idx].ApplyBrokenOrder = atoi(ptr);
	ptr = strtok(NULL, "|");
	mem_sections[idx].ave_x = atoi(ptr);
	ptr = strtok(NULL, "|");
	mem_sections[idx].ave_y = atoi(ptr);
	
	// added 8/21/99 - UOI changes
	
	ptr = strtok(NULL, "|");
	mem_sections[idx].numPutsPerTrip = (double)atof(ptr);
	ptr = strtok(NULL, "|");
	mem_sections[idx].pickForkTrav = (double)atof(ptr);
	ptr = strtok(NULL, "|");
	mem_sections[idx].insertForkTrav = (double)atof(ptr);
	ptr = strtok(NULL, "|");
	mem_sections[idx].totalExtendedCube = (double)atof(ptr);
	ptr = strtok(NULL, "|");
	mem_sections[idx].stockerRate = (double)atof(ptr);
	ptr = strtok(NULL, "|");
	mem_sections[idx].StockerDistanceFixed = atof(ptr);
	ptr = strtok(NULL, "|");
	mem_sections[idx].StockerDistanceVariable = atof(ptr);
	
	if(mem_sections[idx].ContainerQty <= 0)
		mem_sections[idx].ContainerQty = 1;
}

void Pass4Process::GetOneLaborSelect(int idx)
{
	char buffer[BUFSIZE];
	unsigned int  err;
	char *ptr;
	
	/* ******************************************** */
	/* Read the socket information                  */
	/* ******************************************** */
	memset(buffer, 0, BUFSIZE);
	if ( gfnGetDataStream()->ssData.getline(buffer, BUFSIZE) == 0) {
	
		throw EngineException("Error receiving on stream",
			__FILE__, __LINE__, 200);
	}
	
	if ( debugLog )
		fprintf(p4TraceFile,"\nReceived data: %s",buffer);
	
	if(strncmp(buffer, "RACKLABOR", 9) != 0)
		throw EngineException("Corrupt data when expecting Labor", 
		__FILE__, __LINE__, 200);
	
	ptr = strtok(buffer+10, "|");
	
	mem_labor_select[idx].ProfId = atoi(ptr);	// brd - used to be RTid; swapped with below
	ptr = strtok(NULL, "|");
	mem_labor_select[idx].RTid = atoi(ptr);		// brd - this is really the levelprofile id
	ptr = strtok(NULL, "|");
	mem_labor_select[idx].RelLev = atoi(ptr);
	ptr = strtok(NULL, "|");
	mem_labor_select[idx].Cube = (double)atof(ptr);
	ptr = strtok(NULL, "|");
	mem_labor_select[idx].Var = (double)atof(ptr);
	ptr = strtok(NULL, "|");
	mem_labor_select[idx].Fxd = (double)atof(ptr);
}

void Pass4Process::GetOneLaborStocker(int idx)
{
	char buffer[BUFSIZE];
	unsigned int  err;
	char *ptr;
	
	/* ******************************************** */
	/* Read the socket information                  */
	/* ******************************************** */
	memset(buffer, 0, BUFSIZE);
	if ( gfnGetDataStream()->ssData.getline(buffer, BUFSIZE) == 0) {
	
		throw EngineException("Error receiving on stream",
			__FILE__, __LINE__, 200);
	}
	
	if ( debugLog )
		fprintf(p4TraceFile,"\nReceived data: %s",buffer);
	if(strncmp(buffer, "RACKLABOR", 9) != 0)
		throw EngineException("Corrupt data when expecting Labor", 
		__FILE__, __LINE__, 200);
	
	
	ptr = strtok(buffer+10, "|");
	mem_labor_stocker[idx].ProfId = atoi(ptr);		// brd - used to be rtid; swapped with below
	ptr = strtok(NULL, "|");
	mem_labor_stocker[idx].RTid = atoi(ptr);		// brd - really level profile id
	ptr = strtok(NULL, "|");
	mem_labor_stocker[idx].RelLev = atoi(ptr);
	ptr = strtok(NULL, "|");
	mem_labor_stocker[idx].Cube = (double)atof(ptr);
	ptr = strtok(NULL, "|");
	mem_labor_stocker[idx].Var = (double)atof(ptr);
	ptr = strtok(NULL, "|");
	mem_labor_stocker[idx].Fxd = (double)atof(ptr);
}


int Pass4Process::GetProducts(void)
{
	char buffer[BUFSIZE];
	unsigned int  err;
	int i;
	
	
	if (debugLog)
		fprintf(p4TraceFile, "\n------------------- Product Data -------------------------\n");
	
	/* ********************************************* */
	/* Wait for the start of the product stream      */
	/* ********************************************* */
	do {
		memset(buffer, 0, BUFSIZE);
		if ( gfnGetDataStream()->ssData.getline(buffer, BUFSIZE) == 0) {
		
			printf("buffer = %s.\n", buffer);
			throw EngineException("Error receiving on stream",
				__FILE__, __LINE__, 200);
		}
		if ( debugLog )
			fprintf(p4TraceFile,"\nReceived data: %s",buffer);
	} while ((strncmp(buffer, "P4PRODS", 7) != 0) && 
		(strncmp(buffer, "<ENDP4>", 7) != 0));
	
	if(strncmp(buffer, "<ENDP4>", 7) == 0)
		return -1;
	
	mem_prod_count = atoi(buffer+8);		
	
	/* ********************************************* */
	/* Allocate memory to hold the products.         */
	/* ********************************************* */
	if ( debugLog )
		fprintf(p4TraceFile,"\nReceiving %d products.",mem_prod_count);
	
	mem_prods = (p4ProdPack *)malloc(mem_prod_count * sizeof(p4ProdPack));
	if(mem_prods == NULL){
		throw EngineException("Error Allocating memory for prod list",
			__FILE__, __LINE__, 300);
	}
	
	memset(mem_prods,0,mem_prod_count * sizeof(p4ProdPack));
	
	pgFacingCount = 0;
	for(i=0;i<mem_prod_count;i++) {
		GetOneProd(i);	
		if (mem_prods[i].ranking == 1)
			pgFacingCount += mem_prods[i].facings;
	}
	
	
	optType = 2;
	for ( i=0;i<mem_prod_count && optType == 2; i++)
	{
		if ( mem_prods[i].prioritizeNum != (double)(0) )
			optType = 1;
	}
	
	return mem_prod_count;
	
}

void Pass4Process::GetOneProd(int idx)
{
	char buffer[BUFSIZE];
	unsigned int  err;
	char *ptr;
	int innersPerCase;
	
	/* ******************************************** */
	/* Read the socket information                  */
	/* ******************************************** */
	do {
		memset(buffer, 0, BUFSIZE);
		if ( gfnGetDataStream()->ssData.getline(buffer, BUFSIZE) == 0) {
		
			throw EngineException("Error receiving on stream",
				__FILE__, __LINE__, 200);
		}
		if ( debugLog )
			fprintf(p4TraceFile,"\nReceived data: %s",buffer);
	} while (strncmp(buffer, "PROD", 4) != 0);
	
	
	mem_prods[idx].currentAssignedRanking = -1;
	
	ptr = strtok(buffer+5, "|");
	mem_prods[idx].prodGroupDBID = atoi(ptr);
	ptr = strtok(NULL, "|");
	mem_prods[idx].dbID = atoi(ptr);
	ptr = strtok(NULL, "|");
	strcpy(mem_prods[idx].desc, ptr);
	ptr = strtok(NULL, "|");
	mem_prods[idx].caseHeight = (double)atof(ptr);
	ptr = strtok(NULL, "|");
	mem_prods[idx].caseWidth= (double)atof(ptr);
	ptr = strtok(NULL, "|");
	mem_prods[idx].caseLength = (double)atof(ptr);
	ptr = strtok(NULL, "|");
	mem_prods[idx].movement = (double)atof(ptr);
	ptr = strtok(NULL, "|");
	mem_prods[idx].facings = atoi(ptr);
	ptr = strtok(NULL, "|");
	mem_prods[idx].bayProfID = atoi(ptr);
	ptr = strtok(NULL, "|");
	mem_prods[idx].NumInPallet = atoi(ptr);
	ptr = strtok(NULL, "|");
	mem_prods[idx].weight = (double)atof(ptr);
	ptr = strtok(NULL, "|");
	mem_prods[idx].containerWidth = (double)atof(ptr);
	ptr = strtok(NULL, "|");
	mem_prods[idx].containerLength = (double)atof(ptr);
	ptr = strtok(NULL, "|");
	mem_prods[idx].containerHeight = (double)atof(ptr);
	ptr = strtok(NULL, "|");
	mem_prods[idx].contOverrideHeight = atoi(ptr);
	ptr = strtok(NULL, "|");
	mem_prods[idx].productHi = atoi(ptr);
	ptr = strtok(NULL, "|");
	mem_prods[idx].unitOfIssue = atoi(ptr);
	ptr = strtok(NULL, "|");
	mem_prods[idx].rotateXAxis = atoi(ptr);
	ptr = strtok(NULL, "|");
	mem_prods[idx].rotateYAxis = atoi(ptr);
	ptr = strtok(NULL, "|");
	mem_prods[idx].rotateZAxis = atoi(ptr);
	ptr = strtok(NULL, "|");
	mem_prods[idx].ranking = atoi(ptr);
	ptr = strtok(NULL, "|");
	mem_prods[idx].levelType = atoi(ptr);
	
	// UOI changes
	ptr = strtok(NULL, "|");
	mem_prods[idx].productTi = atoi(ptr);
	ptr = strtok(NULL, "|");
	mem_prods[idx].innerWidth = (double)atof(ptr);
	ptr = strtok(NULL, "|");
	mem_prods[idx].innerLength = (double)atof(ptr);
	ptr = strtok(NULL, "|");
	mem_prods[idx].innerHeight = (double)atof(ptr);
	ptr = strtok(NULL, "|");
	mem_prods[idx].eachWidth = (double)atof(ptr);
	ptr = strtok(NULL, "|");
	mem_prods[idx].eachLength = (double)atof(ptr);
	ptr = strtok(NULL, "|");
	mem_prods[idx].eachHeight = (double)atof(ptr);
	ptr = strtok(NULL, "|");
	mem_prods[idx].nestWidth = (double)atof(ptr);
	ptr = strtok(NULL, "|");
	mem_prods[idx].nestLength = (double)atof(ptr);
	ptr = strtok(NULL, "|");
	mem_prods[idx].nestHeight = (double)atof(ptr);
	ptr = strtok(NULL, "|");
	mem_prods[idx].casePack = (double)atof(ptr);
	ptr = strtok(NULL, "|");
	mem_prods[idx].innerPack = (double)atof(ptr);
	ptr = strtok(NULL, "|");
	//
	mem_prods[idx].prioritizeNum = (double)atof(ptr);
	ptr = strtok(NULL, "|");
	strcpy(mem_prods[idx].prioritizeChar,"");
	strcpy(mem_prods[idx].prioritizeChar,ptr);
	
	if ( strcmp(mem_prods[idx].prioritizeChar,"") != 0 )
		optType = 2;
	else
		optType = 1;
	
	ptr = strtok(NULL, "|");
	memcpy( mem_prods[idx].WMSProdID, ptr, (strlen( ptr ) + 1) );
	
	ptr = strtok(NULL, "|");
	memcpy( mem_prods[idx].WMSProdDetID, ptr, (strlen( ptr ) + 1) );
	
	ptr = strtok(NULL, "|");
	memcpy(mem_prods[idx].prodGroupDesc, ptr, (strlen( ptr ) + 1) );
	
	ptr = strtok(NULL, "|");
	memcpy(mem_prods[idx].lastOptimizeAttribute, ptr, (strlen(ptr)+1));
	
	ptr = strtok(NULL, "|");
	mem_prods[idx].trace = atoi(ptr);
	
	
	if (mem_prods[idx].trace) {
		if (! userLog && ! debugLog) {
			
			strcpy(fileName, logPath);
			strcat(fileName, "\\");
			strcat(fileName, P4LogUsr);
			
			if (SHOW_TRACE)
				p4TraceFile = stdout;
			else
				p4TraceFile = fopen(fileName,"w");
			
			if ( p4TraceFile == NULL ) {
				fprintf(stdout, "Cannot Create Trace File %s.  Check disk space on device.\n",P4LogUsr);
				fprintf(stdout, "No logging will be printed.\n");
				userLog = 0;
				debugLog = 0;
				p4TraceFile = stdout;
			}
			else {
				if (mem_prods[idx].ranking == 1) {
					char fullPath[1024];
					if (_fullpath(fullPath, fileName, 1024) != NULL)
						fprintf(stdout, "Trace file: %s\n", fullPath);
					else
						fprintf(stdout, "Trace file: %s\n", P4LogUsr);
				}
			}
		}
		
		fprintf(p4TraceFile, "Tracing Product: %s-%s(%d) - %s\n", 
			mem_prods[idx].WMSProdID, mem_prods[idx].WMSProdDetID,
			mem_prods[idx].dbID,	mem_prods[idx].desc);
		fprintf(p4TraceFile, "\tWidth: %.02f, Length: %.02f, Height: %.02f\n",
			mem_prods[idx].width, mem_prods[idx].length, mem_prods[idx].height);
		fprintf(p4TraceFile, "\tContainer Width: %.02f, Length: %.02f, Height; %.02f, Overridden Height: %.02f\n",
			mem_prods[idx].containerWidth, mem_prods[idx].containerLength, mem_prods[idx].containerHeight,
			mem_prods[idx].contOverrideHeight);
		fprintf(p4TraceFile, "\tTixHi: %dx%d\n", mem_prods[idx].productTi, mem_prods[idx].productHi);
		fprintf(p4TraceFile, "\tUnit of Issue: %s\n",
			mem_prods[idx].unitOfIssue == 0 ? "Each" : mem_prods[idx].unitOfIssue == 1 ? "Inner" : mem_prods[idx].unitOfIssue == 2 ? "Case" : "Pallet");
		
	}
	
	//////////////////////////////////////////////////////////////////////
	// make sure we don't get bogus values for alternate dimensions
	//////////////////////////////////////////////////////////////////////
	if ( mem_prods[idx].innerWidth == 0 )
		mem_prods[idx].innerWidth = mem_prods[idx].caseWidth;
	if ( mem_prods[idx].innerLength == 0 )
		mem_prods[idx].innerLength = mem_prods[idx].caseLength;
	if ( mem_prods[idx].innerHeight == 0 )
		mem_prods[idx].innerHeight = mem_prods[idx].caseHeight;
	if ( mem_prods[idx].eachWidth == 0 )
		mem_prods[idx].eachWidth = mem_prods[idx].caseWidth;
	if ( mem_prods[idx].eachLength == 0 )
		mem_prods[idx].eachLength = mem_prods[idx].caseLength;
	if ( mem_prods[idx].eachHeight == 0 )
		mem_prods[idx].eachHeight = mem_prods[idx].caseHeight;
	if ( mem_prods[idx].innerPack == 0 )
		mem_prods[idx].innerPack = 1;
	if ( mem_prods[idx].casePack == 0 )
		mem_prods[idx].casePack = 1;
	
	// Turn of Rotate permissions if Global flag is off.
	if (! caseReorientAllowed) {
		mem_prods[idx].rotateXAxis = FALSE;
		mem_prods[idx].rotateYAxis = FALSE;
		mem_prods[idx].rotateZAxis = FALSE;
	}
	
	// added 8/19/99 - UOI changes
	// brd - 10/20/99 - use numinpallet unless it's 0, else use tixhi
	//	if ( ( mem_prods[idx].productHi * mem_prods[idx].productTi ) != mem_prods[idx].NumInPallet ||
	//		 mem_prods[idx].NumInPallet == 0 ) {
	if (mem_prods[idx].NumInPallet == 0) {
		mem_prods[idx].NumInPallet = mem_prods[idx].productHi * mem_prods[idx].productTi;
		if ( mem_prods[idx].NumInPallet == 0 )
			mem_prods[idx].NumInPallet = 1;
	}
	
	//////////////////////////////////////////////////////////////////////
	// scale movement, weight, etc. based on the UOI that is passed for
	// this product
	//////////////////////////////////////////////////////////////////////
	// innerPack = number of eaches in an inner
	// casePack = number of eaches in a case
	// NumInPallet = number of cases in a pallet
	
	innersPerCase = (int) (mem_prods[idx].casePack / mem_prods[idx].innerPack);
	
	switch(mem_prods[idx].unitOfIssue) {
	case 0 : //each
		{
			mem_prods[idx].cube =
				(mem_prods[idx].eachWidth * mem_prods[idx].eachHeight * mem_prods[idx].eachLength) /
				mem_sections[0].CubeConversion;
			mem_prods[idx].caseCube =
				(mem_prods[idx].caseWidth * mem_prods[idx].caseHeight * mem_prods[idx].caseLength) /
				mem_sections[0].CubeConversion;
			mem_prods[idx].caseMovement = mem_prods[idx].movement / mem_prods[idx].casePack;
			mem_prods[idx].caseWeight = mem_prods[idx].weight * mem_prods[idx].casePack;
			
			mem_prods[idx].width = mem_prods[idx].eachWidth;
			mem_prods[idx].length = mem_prods[idx].eachLength;
			mem_prods[idx].height = mem_prods[idx].eachHeight;
			
			numEach++;
			break;
		}
	case 1 : //inner
		{
			mem_prods[idx].cube =
				(mem_prods[idx].innerWidth * mem_prods[idx].innerHeight * mem_prods[idx].innerLength) /
				mem_sections[0].CubeConversion;
			mem_prods[idx].caseCube =
				(mem_prods[idx].caseWidth * mem_prods[idx].caseHeight * mem_prods[idx].caseLength) /
				mem_sections[0].CubeConversion;
			mem_prods[idx].caseMovement = mem_prods[idx].movement / innersPerCase;
			mem_prods[idx].caseWeight = mem_prods[idx].weight * innersPerCase;
			
			mem_prods[idx].width = mem_prods[idx].innerWidth;
			mem_prods[idx].length = mem_prods[idx].innerLength;
			mem_prods[idx].height = mem_prods[idx].innerHeight;
			
			numInner++;
			break;
		}
	case 2 : //case
		{
			mem_prods[idx].cube =
				(mem_prods[idx].caseWidth * mem_prods[idx].caseHeight * mem_prods[idx].caseLength) /
				mem_sections[0].CubeConversion;
			mem_prods[idx].caseCube = mem_prods[idx].cube;
			mem_prods[idx].caseMovement = mem_prods[idx].movement;
			mem_prods[idx].caseWeight = mem_prods[idx].weight;
			
			mem_prods[idx].width = mem_prods[idx].caseWidth;
			mem_prods[idx].length = mem_prods[idx].caseLength;
			mem_prods[idx].height = mem_prods[idx].caseHeight;
			
			numCase++;
			break;
		}
	case 3 : //pallet
		{
			mem_prods[idx].cube =
				(mem_prods[idx].containerWidth * ( mem_prods[idx].caseHeight * mem_prods[idx].productHi + mem_prods[idx].containerHeight) * mem_prods[idx].containerLength ) /
				mem_sections[0].CubeConversion;
			mem_prods[idx].caseCube =
				(mem_prods[idx].caseWidth * mem_prods[idx].caseHeight * mem_prods[idx].caseLength) /
				mem_sections[0].CubeConversion;
			mem_prods[idx].caseMovement = mem_prods[idx].movement * mem_prods[idx].NumInPallet;
			mem_prods[idx].caseWeight = mem_prods[idx].weight / mem_prods[idx].NumInPallet;
			
			mem_prods[idx].width = mem_prods[idx].caseWidth;
			mem_prods[idx].length = mem_prods[idx].caseLength;
			mem_prods[idx].height = mem_prods[idx].caseHeight;
			
			numPallet++;
			break;
		}
		
	}
	
	mem_prods[idx].eachWeight = mem_prods[idx].caseWeight / mem_prods[idx].casePack;
	mem_prods[idx].innerWeight = mem_prods[idx].caseWeight / innersPerCase;
	mem_prods[idx].palletWeight = mem_prods[idx].caseWeight * mem_prods[idx].NumInPallet;
	mem_prods[idx].palletMovement = mem_prods[idx].caseMovement / mem_prods[idx].NumInPallet;
	
	mem_prods[idx].expandedFacings = mem_prods[idx].facings;
	mem_prods[idx].reducedFacings = mem_prods[idx].facings;
	
	mem_prods[idx].rotatedHeight = -1.0;
	mem_prods[idx].rotatedWidth = -1.0;
	mem_prods[idx].rotatedLength = -1.0;
	mem_prods[idx].ChangedInPass = FALSE;
	mem_prods[idx].Assigned = 0;
	
	// Row is usable unless there is an earlier row with fewer facings
	// for the same Bay Profile.
	mem_prods[idx].usable = TRUE;
	mem_prods[idx].prevLocIdx = -1;
	mem_prods[idx].prevFacingCount = 0;
	mem_prods[idx].newLocIdx = -1;
	
	
}

int Pass4Process::GetAisleLoc(void)
{
	char buffer[BUFSIZE];
	unsigned int  err;
	int i;
	
	if (debugLog)
		fprintf(p4TraceFile, "\n------------------- Location Data -------------------------\n");
	
	/* ********************************************* */
	/* Wait for the start of the Bay stream          */
	/* ********************************************* */
	do {
		memset(buffer, 0, BUFSIZE);
		if ( gfnGetDataStream()->ssData.getline(buffer, BUFSIZE) == 0) {
		
			throw EngineException("Error receiving on stream",
				__FILE__, __LINE__, 200);
		}
		if ( debugLog )
			fprintf(p4TraceFile,"\nReceived data: %s",buffer);
		
	} while (strncmp(buffer, "P4INFO", 6) != 0);
	
	mem_loc_count = atoi(buffer+7);		
	if(mem_loc_count == 0)
		return 0;
	
	/* ********************************************* */
	/* Allocate memory to hold the Locations.        */
	/* ********************************************* */
	if ( debugLog )
		fprintf(p4TraceFile,"\nReceiving %d locations.\n",mem_loc_count);
	
	mem_locs = (p4Location *)malloc(mem_loc_count * sizeof(p4Location));
	if(mem_locs == NULL){
		throw EngineException("Error Allocating memory for Bay list",
			__FILE__, __LINE__, 300);
	}
	
	memset(mem_locs,0,mem_loc_count * sizeof(p4Location));
	
	mem_avg_x = 0;
	mem_avg_y = 0;
	
	for(i=0;i<mem_loc_count;i++) {
		GetOneRow(i);	
	}
	
	mem_avg_x = (int)((double)mem_avg_x / (double)mem_loc_count);
	mem_avg_y = (int)((double)mem_avg_y / (double)mem_loc_count);
	
	return mem_loc_count;
}

// Sort locations by descending relative level
int weightSort(const void *l1, const void *l2)
{
	p4Location *loc1, *loc2;
	
	
	loc1 = (p4Location *)l1;
	loc2 = (p4Location *)l2;
	
	if(loc1->BayID < loc2->BayID)		
		return -1;
	
	if(loc1->BayID > loc2->BayID)		
		return 1;
	
	if(loc1->RelLev < loc2->RelLev)
		return 1;
	
	if (loc1->RelLev > loc2->RelLev)
		return -1;
	
	return 0;
}

void Pass4Process::GetOneRow(int idx)
{
	char buffer[BUFSIZE];
	unsigned int  err;
	char *ptr;
	
	
	/* ******************************************** */
	/* Wait for the start of the Bay stream         */
	/* ******************************************** */
	do {
		memset(buffer, 0, BUFSIZE);
		if ( gfnGetDataStream()->ssData.getline(buffer, BUFSIZE) == 0) {
		
			throw EngineException("Error receiving on stream",
				__FILE__, __LINE__, 200);
		}
		if ( debugLog )
			fprintf(p4TraceFile,"\nReceived data: %s",buffer);
	} while (strncmp(buffer, "INFO", 4) != 0);
	
	
	ptr = strtok(buffer+5, "|");
	mem_locs[idx].prodGroupDBID = atoi(ptr);
	ptr = strtok(NULL, "|");
	mem_locs[idx].SecID= atoi(ptr);
	ptr = strtok(NULL, "|");
	mem_locs[idx].BayID = atoi(ptr);
	ptr = strtok(NULL, "|");
	mem_locs[idx].BayProfID = atoi(ptr);
	ptr = strtok(NULL, "|");
	mem_locs[idx].BayType = atoi(ptr);
	ptr = strtok(NULL, "|");
	mem_locs[idx].RelLev= atoi(ptr);
	ptr = strtok(NULL, "|");
	mem_locs[idx].LevTime = (double)atof(ptr);
	ptr = strtok(NULL, "|");
	mem_locs[idx].isSelect = atoi(ptr);
	ptr = strtok(NULL, "|");
	mem_locs[idx].Loc_dbid = atoi(ptr);
	ptr = strtok(NULL, "|");
	strcpy(mem_locs[idx].Loc_desc, ptr);
	ptr = strtok(NULL, "|");
	mem_locs[idx].Loc_w = (double)atof(ptr);
	ptr = strtok(NULL, "|");
	mem_locs[idx].Loc_d = (double)atof(ptr);
	ptr = strtok(NULL, "|");
	mem_locs[idx].Loc_h = (double)atof(ptr);
	ptr = strtok(NULL, "|");
	mem_locs[idx].Loc_x = atoi(ptr);
	ptr = strtok(NULL, "|");
	mem_locs[idx].Loc_y = atoi(ptr);
	ptr = strtok(NULL, "|");
	mem_locs[idx].Loc_z = atoi(ptr);
	ptr = strtok(NULL, "|");
	mem_locs[idx].handlingMethod = atoi(ptr);
	ptr = strtok(NULL, "|");
	mem_locs[idx].trace = atoi(ptr);
	ptr = strtok(NULL, "|");
	mem_locs[idx].clearance = atof(ptr);
	ptr = strtok(NULL, "|");
	mem_locs[idx].VarWidth = atoi(ptr);
	
	// Reset flag if Variable Width is turned off Globally.
	//if (!VariableWidthAllowed) mem_locs[idx].VarWidth = FALSE;
	
	///////////////////////////////////////////////////////////////////////
	// <TACTICAL>
	// alternate flag for when they decide to take locs as-is, but still need to 
	// know this was at one time, a variable width location.  This allows us to still
	// try to slot all facings in one location.  We need to modify the MaxCaseFit method
	// to do a check on the "2" case and determine if X facings can fit in the location before
	// returning the number of cases.  If they can't, then we need to return 0
	///////////////////////////////////////////////////////////////////////
	if ( mem_locs[idx].VarWidth == 1 && !variableWidthAllowed )
		mem_locs[idx].VarWidth = 2;
	
	ptr = strtok(NULL, "|");
	mem_locs[idx].prodGap = atoi(ptr);
	ptr = strtok(NULL, "|");
	mem_locs[idx].prodSnap = atoi(ptr);
	ptr = strtok(NULL, "|");
	mem_locs[idx].facingGap = atoi(ptr);
	ptr = strtok(NULL, "|");
	mem_locs[idx].facingSnap = atoi(ptr);
	ptr = strtok(NULL, "|");
	mem_locs[idx].minLocWidth = atoi(ptr);
	ptr = strtok(NULL, "|");
	mem_locs[idx].Level_dbid = atoi(ptr);
	ptr = strtok(NULL, "|");
	mem_locs[idx].Aisle_dbid = atoi(ptr);
	ptr = strtok(NULL, "|");
	mem_locs[idx].Aisle_rotation = (double)atof(ptr);
	ptr = strtok(NULL, "|");
	mem_locs[idx].pickPathDirection = atoi(ptr);
	ptr = strtok(NULL, "|");
	mem_locs[idx].caseReorientAllowed = atoi(ptr);
	ptr = strtok(NULL, "|");
	mem_locs[idx].levelType = atoi(ptr);
	
	mem_locs[idx].forkFixedInsertion = mem_locs[idx].LevTime;
	ptr = strtok(NULL, "|");
	mem_locs[idx].forkFixedExtraction = (double)atof(ptr);
	
	ptr = strtok(NULL, "|");
	mem_locs[idx].baySpanAllowed = atoi(ptr);
	
	ptr = strtok(NULL, "|");
	if ( ptr != NULL )
		mem_locs[idx].Side_dbid = atoi(ptr);
	else
		mem_locs[idx].Side_dbid = 0;
	
	ptr = strtok(NULL, "|");
	if ( ptr != NULL )
		mem_locs[idx].bayMaxWeight = (double)atof(ptr);
	else
		mem_locs[idx].bayMaxWeight = 0.0;
	
	ptr = strtok(NULL, "|");
	if ( ptr != NULL )
		mem_locs[idx].levelMaxWeight = (double)atof(ptr);
	else
		mem_locs[idx].levelMaxWeight = 0.0;
	
	
	// Tallies for max values in the array
	if ( mem_locs[idx].handlingMethod != 3 ) {
		if ( (mem_locs[idx].Loc_h-mem_locs[idx].clearance) > maxLocHeight)
			maxLocHeight = (mem_locs[idx].Loc_h - mem_locs[idx].clearance);
		if (mem_locs[idx].Loc_w > maxLocWidth)
			maxLocWidth = mem_locs[idx].Loc_w;
		if (mem_locs[idx].Loc_d > maxLocDepth)
			maxLocDepth = mem_locs[idx].Loc_d;
	}
	else {
		if ( (mem_locs[idx].Loc_h - mem_locs[idx].clearance) > maxPalLocHeight)
			maxPalLocHeight = (mem_locs[idx].Loc_h - mem_locs[idx].clearance);
		if (mem_locs[idx].Loc_w > maxPalLocWidth)
			maxPalLocWidth = mem_locs[idx].Loc_w;
		if (mem_locs[idx].Loc_d > maxPalLocDepth)
			maxPalLocDepth = mem_locs[idx].Loc_d;
	}
	
	// Set up tallies for VW locations.
	
	// Initialize width used by product to one product gap 
	// each subsequent product will add one more product gap
	//mem_locs[idx].prodWidth = mem_locs[idx].prodGap;
	mem_locs[idx].prodWidth = 0;
	mem_locs[idx].vwLocCount = 0;
	
	mem_locs[idx].Assigned = 0;
	
	mem_avg_x += mem_locs[idx].Loc_x;
	mem_avg_y += mem_locs[idx].Loc_y;
	
	if(mem_locs[idx].isSelect != 1)
		mem_locs[idx].BayType = -314;
	
	if ( mem_locs[idx].baySpanAllowed == 1 ) {
		//printf("Allowing Spanning\n");
		mem_locs[idx].breakOnBay = FALSE;
	}
	else
		mem_locs[idx].breakOnBay = TRUE;
	
	// Warning:  Hard-Coding!  We allow overlapping to take place
	// only for certain bay types
	if (mem_locs[idx].levelType==BAY_TYPE_BIN ||
		mem_locs[idx].levelType==BAY_TYPE_FLOW ||
		mem_locs[idx].levelType==BAY_TYPE_PALLET ||
		mem_locs[idx].levelType==BAY_TYPE_PIR ||
		mem_locs[idx].levelType==BAY_TYPE_FLOOR || 
		mem_locs[idx].levelType==BAY_TYPE_PALLET_FLOW ||
		mem_locs[idx].levelType==BAY_TYPE_CAROUSEL)
		mem_locs[idx].overlapAllowed = TRUE;
	else
		mem_locs[idx].overlapAllowed = FALSE;
	
	mem_locs[idx].prevProdIdx = -1;
	mem_locs[idx].newProdIdx = -1;
	
	mem_locs[idx].bayCurrentWeight = 0;
	mem_locs[idx].levelCurrentWeight = 0;
	
	SetSortKey(&mem_locs[idx]);
}


void Pass4Process::GetOptimizationFlags(void)
{
	char buffer[BUFSIZE];
	unsigned int  err;
	char tmp[BUFSIZE];
	char *ptr, *ptr2;
	
	/* ******************************************** */
	/* Wait for the start of the Ranking stream     */
	/* ******************************************** */
	memset(tmp,0,BUFSIZE);
	do {
		memset(buffer, 0, BUFSIZE);
		if ( gfnGetDataStream()->ssData.getline(buffer, BUFSIZE) == 0) {
		
			throw EngineException("Error receiving on stream",
				__FILE__, __LINE__, 200);
		}
		strcat(tmp,buffer);
		if ( debugLog )
			fprintf(p4TraceFile,"\nReceived data: %s",buffer);
	} while (strstr(tmp, "<OPTEND>") == NULL);
	
	ptr2 = tmp;
	ptr = strtok(ptr2, ",");
	strcpy(optMsgString,ptr);
	ptr = strtok(NULL, "<");
	sortOptFlag = atoi(ptr);
	
}


void Pass4Process::ResetLists(void)
{
	int i;
	
	/* ******************************************************** */
	/* This method will handle the re-ordering of the product   */
	/* and bay lists.                                           */
	/* ******************************************************** */
	
	// Pre-sort dump for the products.
	/*	FILE * junkfp0 = fopen("PreSortProds.out","w");
	for (i=0;i<mem_prod_count;i++)
	fprintf(junkfp0,"%d-\tProd %d, Bay Prof=%d, Facings=%d, Rank=%d, Mvmt=%f, Prio=%f\n", i+1,
	mem_prods[i].dbID,
	mem_prods[i].bayProfID,
	mem_prods[i].facings,
	mem_prods[i].ranking,
	mem_prods[i].movement,
	mem_prods[i].prioritizeNum);
	fclose(junkfp0);
	*/
	//printf("OptType %d, SortOptFlag %d\n",optType,sortOptFlag);
	if ( debugLog ) {
		fprintf(p4TraceFile,"Optimization Method : ");
		if ( optType == 1 ) 
			fprintf(p4TraceFile,"Numeric, ");
		else
			fprintf(p4TraceFile,"Alphanumeric, ");
		if ( sortOptFlag == 1 )
			fprintf(p4TraceFile,"Ascending\n");
		else if ( sortOptFlag == 2 )
			fprintf(p4TraceFile,"Descending\n");
		else
			fprintf(p4TraceFile,"Uniform\n");
	}
	
	if ( optType == 1 && sortOptFlag == 1) // numeric sort
		qsort(mem_prods, mem_prod_count, sizeof(p4ProdPack), prodNumericSortAscend);
	else if ( optType == 1 && sortOptFlag == 2)
		qsort(mem_prods, mem_prod_count, sizeof(p4ProdPack), prodNumericSortDescend);
	else if ( optType == 1 && sortOptFlag == 3) {
		qsort(mem_prods, mem_prod_count, sizeof(p4ProdPack), prodNumericSortAscend);
		MakeProdsUniform();
	}
	else if ( optType == 2 && sortOptFlag == 1) // numeric sort
		qsort(mem_prods, mem_prod_count, sizeof(p4ProdPack), prodAlphaSortAscend);
	else if ( optType == 2 && sortOptFlag == 2)
		qsort(mem_prods, mem_prod_count, sizeof(p4ProdPack), prodAlphaSortDescend);
	else if ( optType == 2 && sortOptFlag == 3) {
		qsort(mem_prods, mem_prod_count, sizeof(p4ProdPack), prodAlphaSortAscend);
		MakeProdsUniform();
	}
	else
		qsort(mem_prods, mem_prod_count, sizeof(p4ProdPack), prodNumericSortAscend);
	
	PopulateAdjacentLocs();
	
	qsort(mem_locs, mem_loc_count, sizeof(p4Location), bayComp);
	
	
	GroupOnBay();
	
	// Add a dump for the grouped locs.
	if ( debugLog ) {
		for (i=0;i<mem_loc_count;i++)
			fprintf(p4TraceFile,"Sorted Location : %s\tBayProfID %d\tSecID %d\tBayID %d\tLvl %d\tCost %f\tW=%6.2f,L=%6.2f,H=%6.2f\n",	
			mem_locs[i].Loc_desc,
			mem_locs[i].BayProfID,
			mem_locs[i].SecID,
			mem_locs[i].BayID,
			mem_locs[i].RelLev,
			mem_locs[i].SortKey,
			mem_locs[i].Loc_w,
			mem_locs[i].Loc_d,
			mem_locs[i].Loc_h);
	}

	fileName[0]=0;
	strcpy(fileName, logPath);
	strcat(fileName, "\\");

	// Add a dump for the products.
	char prodFile[1024];
	prodFile[0]=0;
	strcpy(prodFile, logPath);
	strcat(prodFile, "\\P4SortedProds.out");
	FILE * junkfp1 = fopen(prodFile,"w");
	double hgt;
	
	for (i=0;i<mem_prod_count;i++) {
		if ( mem_prods[i].ranking == 1 && debugLog ) {
			
			if (mem_prods[i].contOverrideHeight != 0)
				hgt = (double)mem_prods[i].contOverrideHeight;
			else
				hgt = mem_prods[i].containerHeight + (mem_prods[i].productHi * mem_prods[i].caseHeight);
			
			fprintf(p4TraceFile,"%d-\tProd %d[WMSID=%20.20s], Bay Prof=%d, Mvmt=%f, Priority(num)=%f, Priority(char)=%s, Pallet Height: %f\n", i+1,
				mem_prods[i].dbID,
				mem_prods[i].WMSProdID,
				mem_prods[i].bayProfID,
				mem_prods[i].movement,
				mem_prods[i].prioritizeNum,
				mem_prods[i].prioritizeChar,
				hgt);
			
			fprintf(junkfp1,"%d-\tProd %d[WMSID=%20.20s], Bay Prof=%d, Mvmt=%f, Priority(num)=%f, Priority(char)=%s, Pallet Height: %f\n", i+1,
				mem_prods[i].dbID,
				mem_prods[i].WMSProdID,
				mem_prods[i].bayProfID,
				mem_prods[i].movement,
				mem_prods[i].prioritizeNum,
				mem_prods[i].prioritizeChar);
			fflush(p4TraceFile);
			fflush(junkfp1);
		}
	}
	fclose(junkfp1);
	
	// now go back and add in any levels or bays that 
	// were missing from the list of solutions
	
	if (debugLog) {
		fprintf(p4TraceFile, "Before SetMaxWeights\n");
		fflush(p4TraceFile);
	}
	SetMaxWeights();
	if (debugLog) {
		fprintf(p4TraceFile, "After SetMaxWeights\n");
		fflush(p4TraceFile);
	}
	
}


void Pass4Process::SetMaxWeights()
{
	
	int i, j, bayIdx, levelIdx;
	int prevBayID, prevLevelID;
	
	prevBayID = prevLevelID = levelIdx = -1;
	
	for (i=0; i < mem_loc_count; ++i) {
		if (prevBayID != mem_locs[i].BayID) {
			prevBayID = mem_locs[i].BayID;
			bayIdx = FindBay(prevBayID);
			if (bayIdx < 0) {
				bay_weight_counter++;
				mem_bay_weights[bay_weight_counter].bayDBID = prevBayID;
				mem_bay_weights[bay_weight_counter].currentBayWeight = 0;
				mem_bay_weights[bay_weight_counter].bayMaxWeight = mem_locs[i].bayMaxWeight;
			}
		}
		
		if (prevLevelID != mem_locs[i].Level_dbid) {
			prevLevelID = mem_locs[i].Level_dbid;
			levelIdx = FindLevel(prevLevelID);
			if (levelIdx < 0) {
				level_weight_counter++;
				mem_level_weights[level_weight_counter].levelDBID = prevLevelID;
				mem_level_weights[level_weight_counter].bayDBID = prevBayID;
				mem_level_weights[level_weight_counter].currentLevelWeight = 0;
				mem_level_weights[level_weight_counter].levelMaxWeight = mem_locs[i].levelMaxWeight;
				mem_level_weights[level_weight_counter].relativeLevel = mem_locs[i].RelLev;
			}
		}
	}
	
	// now adjust the weights on the first levels to be
	// the bay max weight - the sum of the other levels
	
	double sumWeight;
	
	for (i=0; i < bay_weight_counter; ++i) {
		// find all the non-level1's for this bay
		sumWeight = 0;
		for (j=0; j < level_weight_counter; ++j) {
			if ((mem_level_weights[j].bayDBID == mem_bay_weights[i].bayDBID) &&
				(mem_level_weights[j].relativeLevel != 1))
				sumWeight += mem_level_weights[j].levelMaxWeight;
		}
		
		// find the level 1
		for (j=0; j < level_weight_counter; ++j) {
			if ((mem_level_weights[j].bayDBID == mem_bay_weights[i].bayDBID) &&
				(mem_level_weights[j].relativeLevel == 1))
				mem_level_weights[j].levelMaxWeight = 
				mem_bay_weights[i].bayMaxWeight - sumWeight;
		}
		
	}
	
	
	return;
	
}


int bayComp(const void *b1, const void *b2)
{
	p4Location *inf1, *inf2;
	/* ******************************************** */
	/* We order the locations in terms of their     */
	/* relative labor costs.  This labor cost was   */
	/* set during the location load process.        */
	/* ******************************************** */
	
	inf1 = (p4Location *)b1;
	inf2 = (p4Location *)b2;
	
	if (followPickPath) {
		int comp = strcmp(inf1->Loc_desc, inf2->Loc_desc);
		if (comp != 0)
			return comp;
	}
	
	if(inf1->SortKey > inf2->SortKey)		
		return 1;
	
	if(inf1->SortKey < inf2->SortKey)		
		return -1;
	
	if(inf1->SortKey == inf2->SortKey) 
		return strcmp(inf1->Loc_desc,inf2->Loc_desc);
	
	return 0;
}



// sort the products by the sort priority defined in the product group
// in ascending order
//  -1 indicates that p1 comes before p2 in the list (i.e. p1 < p2)
//   1 indicates that p2 comes before p1 in the list (i.e. p2 < p1)
int prodNumericSortAscend( const void *p1, const void *p2 ) {
	
	double pn1, pn2;		// short-cut for prioritizeNum
	double ph1, ph2;		// short-cut for product height (pallet height)
	p4ProdPack *pp1;		// short-cut for product 1
	p4ProdPack *pp2;		// short-cut for product 2
	
	
	pp1 = (p4ProdPack *)p1;
	pp2 = (p4ProdPack *)p2;
	
	pn1 = ((p4ProdPack *)p1)->prioritizeNum;
	if (pn1 <= 0) pn1 = 0.0001f;
	pn2 = ((p4ProdPack *)p2)->prioritizeNum;
	if (pn2 <= 0) pn2 = 0.0001f;
	
	// calculate product 1 pallet height
	if (pp1->contOverrideHeight != 0)
		ph1 = (double)pp1->contOverrideHeight;
	else
		ph1 = pp1->containerHeight + (pp1->productHi * pp1->caseHeight);
	
	if (ph1 <= 0) ph1 = 0.0001f;
	
	// calculate product 2 pallet height
	if (pp2->contOverrideHeight != 0)
		ph2 = (double)pp2->contOverrideHeight;
	else
		ph2 = pp2->containerHeight + (pp2->productHi * pp2->caseHeight);
	
	if (ph2 <= 0) ph2 = 0.0001f;
	
	if (pn1 < pn2) {	// prod 1 should come before prod 2
		
		if (ph1 >= ph2)	// prod 1 is taller		
			return -1;	// prod 1 wins because priority field is less and height is greater
		
		if (ph1 < ph2) {	// prod 2 is taller
			
			// since  prod 1 wins the priority battle but prod 2 wins the height battle,
			// we compare the adjusted priority field against the adjusted height
			// optRatio is a number between 0 and 1 that indicates which is more
			// significant: priority or height.  0 means priority is insignificant
			// 1 means height is insignificant
			
			if (optRatio*pn2/pn1 < (1-optRatio)*ph2/ph1 )
				return 1;	// prod 2 wins because it is taller and the difference
			// in height outweighs the difference in priority
			
			if ( optRatio*pn2/pn1 > (1-optRatio)*ph2/ph1 )
				return -1;	// prod 1 wins because even though it is shorter,
			// the difference in priority outweighs the 
			// difference in height
			
			
		}
	}
	
	
	if (pn1 > pn2) {		// prod 1 should come after prod 2
		
		if (ph1 <= ph2)		// and prod 2 is also taller
			return 1;		// so prod 2 wins
		
		if (ph1 > ph2) {	// prod 1 is taller so we have to check the adjusted ratios
			
			if ( optRatio*pn1/pn2 < (1-optRatio)*ph1/ph2 )
				return -1;	// prod 1 wins because height is more significant
			
			if ( optRatio*pn1/pn2 > (1-optRatio)*ph1/ph2 )
				return 1;	// prod 2 wins because priority is more significant
			
		}
		
	}
	
	
	// descending order only applies to priority fields (and always to height)
	// so put the lowest product number first
	// brd - changed to use wms id as the next criteria so the sort will
	// be the same when the facility is copied and the dbids are in a different
	// order
	//if (pp1->dbID < pp2->dbID)
	if (strcmp(pp1->WMSProdID, pp2->WMSProdID) < 0)
		return -1;			
	
	if (strcmp(pp1->WMSProdID, pp2->WMSProdID) > 0)
		return 1;
	
	if (strcmp(pp1->WMSProdDetID, pp2->WMSProdDetID) < 0)
		return -1;			
	
	if (strcmp(pp1->WMSProdDetID, pp2->WMSProdDetID) > 0)
		return 1;
	
	if (pp1->dbID < pp2->dbID)
		return -1;			
	
	if (pp1->dbID > pp2->dbID)
		return 1;
	
	// if products are same, sort by ranking putting the lowest ranking first
	if (pp1->ranking < pp2->ranking)
		return -1;						// prod 1 wins
	
	if (pp1->ranking > pp2->ranking)	
		return 1;						// prod 2 wins
	
	
	return 0;							// they are the same
	
										/*
										if(((p4ProdPack *)p1)->prioritizeNum < ((p4ProdPack *)p2)->prioritizeNum)
										return -1;
										else if(((p4ProdPack *)p1)->prioritizeNum > ((p4ProdPack *)p2)->prioritizeNum)
										return 1;
										else {
										if(((p4ProdPack *)p1)->dbID < ((p4ProdPack *)p2)->dbID)
										return -1;
										else if(((p4ProdPack *)p1)->dbID > ((p4ProdPack *)p2)->dbID)
										return 1;
										else {
										if(((p4ProdPack *)p1)->ranking < ((p4ProdPack *)p2)->ranking)
										return -1;
										else if(((p4ProdPack *)p1)->ranking > ((p4ProdPack *)p2)->ranking)
										return 1;
										else
										return 0;
										}
										}
	*/
}

// sort the products by the sort priority defined in the product group
// in descending order
// -1 indicates that p1 comes before p2 in the list (i.e. p1 > p2)
//  1 indicates that p2 comes before p1 in the list (i.e. p2 > p1)
int prodNumericSortDescend( const void *p1, const void *p2 ) {
	
	double pn1, pn2;		// short-cut for prioritizeNum
	double ph1, ph2;		// short-cut for product height (pallet height)
	p4ProdPack *pp1;		// short-cut for product 1
	p4ProdPack *pp2;		// short-cut for product 2
	
	pp1 = (p4ProdPack *)p1;
	pp2 = (p4ProdPack *)p2;
	
	pn1 = ((p4ProdPack *)p1)->prioritizeNum;
	if (pn1 <= 0) pn1 = 0.0001f;
	pn2 = ((p4ProdPack *)p2)->prioritizeNum;
	if (pn2 <= 0) pn2 = 0.0001f;
	
	// calculate product 1 pallet height
	if (pp1->contOverrideHeight != 0)
		ph1 = (double)pp1->contOverrideHeight;
	else
		ph1 = pp1->containerHeight + (pp1->productHi * pp1->caseHeight);
	
	if (ph1 <= 0) ph1 = 0.0001f;
	
	// calculate product 2 pallet height
	if (pp2->contOverrideHeight != 0)
		ph2 = (double)pp2->contOverrideHeight;
	else
		ph2 = pp2->containerHeight + (pp2->productHi * pp2->caseHeight);
	
	if (ph2 <= 0) ph2 = 0.0001f;
	
	if (pn1 < pn2) {	// prod 2 should come before prod 1 in the list		
		if (ph1 <= ph2)	// and prod 2 is taller		
			return 1;	// so prod 2 wins because both priority and height are greater
		
		if (ph1 > ph2) {	// prod 1 is taller
			
			// since  prod 2 wins the priority battle but prod 1 wins the height battle,
			// we compare the adjusted priority field against the adjusted height
			// optRatio is a number between 0 and 1 that indicates which is more
			// significant: priority or height.  0 means priority is insignificant
			// 1 means height is insignificant
			
			if (optRatio*pn2/pn1 < (1-optRatio)*ph1/ph2 )
				return -1;	// prod 1 wins because height is more significant
			
			if ( optRatio*pn2/pn1 > (1-optRatio)*ph1/ph2 )
				return 1;	// prod 2 wins because priority is more significant
			
			
		}
	}
	
	
	if (pn1 > pn2) {		// prod 1 should come first
		
		if (ph1 >= ph2)		// and prod 1 is taller
			return -1;		// so prod 1 wins because both priority and and height are greater
		
		if (ph1 < ph2) {	// prod 2 is taller
			
			if ( optRatio*pn1/pn2 < (1-optRatio)*ph2/ph1 )
				return 1;	// prod 2 wins because height is more significant
			
			if ( optRatio*pn1/pn2 > (1-optRatio)*ph2/ph1 )
				return -1;	// prod 1 wins because priority is more significant
			
		}
		
	}
	
	
	// descending order only applies to priority fields (and always to height)
	// so put the lowest product number first
	// brd - changed to use wms id as the next criteria so the sort will
	// be the same when the facility is copied and the dbids are in a different
	// order
	//if (pp1->dbID < pp2->dbID)
	if (strcmp(pp1->WMSProdID, pp2->WMSProdID) < 0)
		return -1;			
	
	if (strcmp(pp1->WMSProdID, pp2->WMSProdID) > 0)
		return 1;
	
	if (strcmp(pp1->WMSProdDetID, pp2->WMSProdDetID) < 0)
		return -1;			
	
	if (strcmp(pp1->WMSProdDetID, pp2->WMSProdDetID) > 0)
		return 1;
	
	if (pp1->dbID < pp2->dbID)
		return -1;
	
	if (pp1->dbID > pp2->dbID)
		return 1;
	
	// if products are same, sort by ranking putting the lowest ranking first
	if (pp1->ranking < pp2->ranking)
		return -1;						// prod 1 wins
	
	if (pp1->ranking > pp2->ranking)	
		return 1;						// prod 2 wins
	
	
	return 0;							// they are the same
	
										/*		old way
										if(((p4ProdPack *)p1)->prioritizeNum < ((p4ProdPack *)p2)->prioritizeNum)
										return 1;
										else if(((p4ProdPack *)p1)->prioritizeNum > ((p4ProdPack *)p2)->prioritizeNum)
										return -1;
										else {
										if(((p4ProdPack *)p1)->dbID < ((p4ProdPack *)p2)->dbID)
										return -1;
										else if(((p4ProdPack *)p1)->dbID > ((p4ProdPack *)p2)->dbID)
										return 1;
										else {
										if(((p4ProdPack *)p1)->ranking < ((p4ProdPack *)p2)->ranking)
										return -1;
										else if(((p4ProdPack *)p1)->ranking > ((p4ProdPack *)p2)->ranking)
										return 1;
										else
										return 0;
										}
										}
	*/
	
}

int prodAlphaSortAscend( const void *p1, const void *p2 ) 
{
	
	p4ProdPack *pp1;		// short-cut for product 1
	p4ProdPack *pp2;		// short-cut for product 2
	
	pp1 = (p4ProdPack *)p1;
	pp2 = (p4ProdPack *)p2;
	
	
	if (strcmp(((p4ProdPack *)p1)->prioritizeChar,((p4ProdPack *)p2)->prioritizeChar) < 0)
		return -1;
	else if (strcmp(((p4ProdPack *)p1)->prioritizeChar,((p4ProdPack *)p2)->prioritizeChar) > 0)
		return 1;
	
	// brd - changed to use wms id as the next criteria so the sort will
	// be the same when the facility is copied and the dbids are in a different
	// order
	//if (pp1->dbID < pp2->dbID)
	if (strcmp(pp1->WMSProdID, pp2->WMSProdID) < 0)
		return -1;			
	
	if (strcmp(pp1->WMSProdID, pp2->WMSProdID) > 0)
		return 1;
	
	if (strcmp(pp1->WMSProdDetID, pp2->WMSProdDetID) < 0)
		return -1;			
	
	if (strcmp(pp1->WMSProdDetID, pp2->WMSProdDetID) > 0)
		return 1;
	
	
	// strings are the same
	if(((p4ProdPack *)p1)->dbID < ((p4ProdPack *)p2)->dbID)
		return -1;
	else if(((p4ProdPack *)p1)->dbID > ((p4ProdPack *)p2)->dbID)
		return 1;
	
	// products are the same
	if(((p4ProdPack *)p1)->ranking < ((p4ProdPack *)p2)->ranking)
		return -1;
	else if(((p4ProdPack *)p1)->ranking > ((p4ProdPack *)p2)->ranking)
		return 1;
	
	
	return 0;
	
}

int prodAlphaSortDescend( const void *p1, const void *p2 )
{
	
	p4ProdPack *pp1;		// short-cut for product 1
	p4ProdPack *pp2;		// short-cut for product 2
	
	pp1 = (p4ProdPack *)p1;
	pp2 = (p4ProdPack *)p2;
	
	
	if (strcmp(((p4ProdPack *)p1)->prioritizeChar,((p4ProdPack *)p2)->prioritizeChar) > 0)
		return -1;
	else if (strcmp(((p4ProdPack *)p1)->prioritizeChar,((p4ProdPack *)p2)->prioritizeChar) < 0)
		return 1;
	
	// brd - changed to use wms id as the next criteria so the sort will
	// be the same when the facility is copied and the dbids are in a different
	// order
	//if (pp1->dbID < pp2->dbID)
	if (strcmp(pp1->WMSProdID, pp2->WMSProdID) < 0)
		return -1;			
	
	if (strcmp(pp1->WMSProdID, pp2->WMSProdID) > 0)
		return 1;
	
	if (strcmp(pp1->WMSProdDetID, pp2->WMSProdDetID) < 0)
		return -1;			
	
	if (strcmp(pp1->WMSProdDetID, pp2->WMSProdDetID) > 0)
		return 1;
	
	// strings are the same
	if (((p4ProdPack *)p1)->dbID < ((p4ProdPack *)p2)->dbID)
		return -1;
	else if (((p4ProdPack *)p1)->dbID > ((p4ProdPack *)p2)->dbID)
		return 1;
	
	if(((p4ProdPack *)p1)->ranking < ((p4ProdPack *)p2)->ranking)
		return -1;
	else if(((p4ProdPack *)p1)->ranking > ((p4ProdPack *)p2)->ranking)
		return 1;
	
	return 0;
}

// Sort the new Variable Width Locations so that the Locations
// in the same Bay Level are together and in order.
int vwComp(const void *loc1, const void *loc2)
{
	varWidthLoc *vwl1, *vwl2;
	
	vwl1 = (varWidthLoc *)loc1;
	vwl2 = (varWidthLoc *)loc2;
	return strcmp(vwl1->locDesc, vwl2->locDesc);
	
}

/* **************************************************** */
/* The location list is assumed to be sorted on cost.   */
/* This function will go through that list and group    */
/* all of the bay information together.  That way we    */
/* don't end up with a producut being assigned to a     */
/* level in a bay in one aisle, and then a level in a   */
/* bay in a different aisle simply because those levels */
/* cost the same amount.                                */
/* Note, there is probably a lot of room for optimization */
/* of this algorithm.                                     */
/* **************************************************** */
void Pass4Process::GroupOnBay(void)
{
	int i,j,k,l,m;
	p4Location tmpLoc;
	
	for(i=0;i<mem_loc_count;i++){
		for(j=i+1;j<mem_loc_count;j++){
			if((mem_locs[j].BayID != mem_locs[i].BayID) ||
				(mem_locs[j].RelLev != mem_locs[i].RelLev))
			{
				l = -1;
				for(k=j+1;k<mem_loc_count;k++){
					if((mem_locs[k].BayID == mem_locs[i].BayID) &&
						(mem_locs[k].RelLev == mem_locs[i].RelLev))
					{
						l = k;	
						break;
					}
				}
				if(l>-1){ // Found a match
					memcpy(&tmpLoc, &(mem_locs[l]), sizeof(p4Location));
					for(m=l-1;m>=j;m--){
						memcpy(&(mem_locs[m+1]), &(mem_locs[m]), sizeof(p4Location));
					}
					memcpy(&(mem_locs[j]), &tmpLoc, sizeof(p4Location));
				} else {
					break;
				}
			}
		}
	}				
}


double Pass4Process::RunComp(void)
{
	int i, k, l, p, assigned=0, badDimensions=0, flag;
	int found_open_loc, caseCount, widthUsed, curProd=-1;
	double CostSum, tmpCost, prodCube=0.0, locCube=0.0;
	char costbuf[COST_BUFFER_SIZE];
	int msgIdx = 0;
	int bayIdx, levelIdx;
	vector<int> facingIndexList;
	
	memset(costbuf,0,COST_BUFFER_SIZE);
	
	/* ******************************************** */
	/* We have a list of locations that are ordered */
	/* correctly.  We also have a list of products  */
	/* that are ordered correctly.  What this will  */
	/* do is to take the two lists and match them   */
	/* up so that all of our products get locations */
	/* We do this while following a set of criteria */
	/* for matching products to locations:          */
	/* 1) The racktype of the location must match   */
	/*    the racktype of the product.              */
	/* 2) If a product needs multiple facings, all  */
	/*    facings should be on one level.           */
	/* 3) The max dimension of the product should   */
	/*    not exceed the max dimension of the loc.  */
	/* ******************************************** */
	
	/* ******************************************** */
	/* As we are running through the facility, we   */
	/* will also be keeping a running tally of the  */
	/* current labor cost for the facility.         */
	/* ******************************************** */
	CostSum = 0;
	
	int numTimes;
	
	
	if (utilStart == 0 || utilDecrement <= 0 || ignoreRankings == 1) {
		utilStart = 0;
		utilDecrement = 0;
		numTimes = 1;
	}
	else {
		
		numTimes = (int)(utilStart/utilDecrement);
		if (numTimes * utilDecrement < utilStart)
			numTimes++;
		
		numTimes++;
	}
	
	TimeStamp("Before numTimes loop\n");	
	for (int utilLoop = 0; utilLoop < numTimes; ++utilLoop) {
		
		utilMinimum = utilStart - (utilLoop * utilDecrement);
		if (utilMinimum < 0)
			utilMinimum = 0;
		
		myCurrentRanking = utilLoop + 1;
		
		if (debugLog) {
			fprintf(p4TraceFile, "Minimum Utilization: %3.2f\n", utilMinimum);
		}
		
		for(i=0;i<mem_prod_count;i++) {
			
			char y[50];
			sprintf(y, "Processing Product: %d\n", i);
			TimeStamp(y);
			
			if (mem_prods[i].trace && mem_prods[i].ranking == 1) {
				fprintf(p4TraceFile, "------------------------------------------------------\n");
				fprintf(p4TraceFile, "Processing Product: %d - %s (%d-%d)\n",
					mem_prods[i].dbID, mem_prods[i].desc, mem_prods[i].WMSProdID,
					mem_prods[i].WMSProdDetID);
				fprintf(p4TraceFile, "Ignore Rankings: %d\n", ignoreRankings);
			}
			
			if (mem_prods[i].Assigned == 1) {
				if (mem_prods[i].trace && mem_prods[i].ranking == 1)
					fprintf(p4TraceFile, "Product already assigned.\n");
				continue;
			}
			
			if (mem_prods[i].dbID == curProd) {
				// Skip on ahead if we already found facings for this Product
				if (assigned==1 || badDimensions==1)
					continue;
			} 
			else {
				curProd = mem_prods[i].dbID;
				badDimensions = 0;
			}
			
			//////////////////////////////////////////////////////////////////////////
			// 1.1:  There may be more than one row for this Product and Bay Profile
			// with different numbers of facings.  If one has already been tried
			// unsuccessfully, no need to waste time on this row.
			//////////////////////////////////////////////////////////////////////////
			if (mem_prods[i].usable==FALSE) {
				if (mem_prods[i].trace)
					fprintf(p4TraceFile, "Product already processed.\n");
				continue;
			}
			
			if ( debugLog || mem_prods[i].trace) {
				fprintf(p4TraceFile, "Product %d[WMSID=%20.20s], %20.20s\tW-%5.1f  L-%5.1f  H-%5.1f\n",
					mem_prods[i].dbID, mem_prods[i].WMSProdID, mem_prods[i].desc, mem_prods[i].caseWidth,
					mem_prods[i].caseLength, mem_prods[i].caseHeight);
			}
			
			// Bad Product dimensions.
			if (mem_prods[i].width == 0 || 
				mem_prods[i].length == 0 || 
				mem_prods[i].height == 0) { 
				if ( debugLog || mem_prods[i].trace) {
					fprintf(p4TraceFile, "Skipping product because of bad dimensions (%.0fx%.0fx%.0f\n",
						mem_prods[i].width, mem_prods[i].length, mem_prods[i].height);
				}
				
				badDimensions=1;
				
				if (utilLoop == (numTimes - 1) && (ignoreRankingsFlag != 1 || ignoreRankings == 1) ) {
					sprintf(msg_buf,"Product has invalid dimensions. %dx%dx%d",
						mem_prods[i].width, mem_prods[i].length, mem_prods[i].height);
					strcpy(p4Messages[msgIdx].msg,msg_buf);
					strcpy(p4Messages[msgIdx].prodDesc,mem_prods[i].desc);
					p4Messages[msgIdx].prodPkDBID = mem_prods[i].dbID;
					strcpy(p4Messages[msgIdx].WMSProdID,mem_prods[i].WMSProdID);
					strcpy(p4Messages[msgIdx].WMSProdDetID,mem_prods[i].WMSProdDetID);
					msgIdx++;
				}
				continue;
			}
			
			caseCount=0;
			assigned = 0;
			found_open_loc = 0;
			
			/* ****************************** */
			/* Grab as many facings as we can */
			/* ****************************** */
			// k is the index of the first facing
			k = GetFacings(0, i, &caseCount,&found_open_loc, facingIndexList);
			
			assigned = 1; // to prevent going through the same product multiple times.
			
			if(k < 0) {
				
				if ( debugLog || mem_prods[i].trace ) {
					sprintf(msg_buf, "Could not find facings for product (%20.20s) [WMSID=%20.20s]\n",
						mem_prods[i].desc, mem_prods[i].WMSProdID); 
					fprintf(p4TraceFile,"%s", msg_buf);
					if ( found_open_loc == 0 )
						fprintf(p4TraceFile,"No available location in product group\n");
					else if ( found_open_loc == 1 )
						fprintf(p4TraceFile,"No location of appropriate profile.\n");
					else if (found_open_loc == 2)
						fprintf(p4TraceFile, "No location big enough for product.\n");
					else if (found_open_loc == 3)
						fprintf(p4TraceFile, "No bay or level with available weight.\n");
					else if (found_open_loc == 999)
						fprintf(p4TraceFile, "Utilization %3.2 not met\n", utilMinimum);
					else
						fprintf(p4TraceFile,"Unknown reason.\n");
				}
				
				////////////////// Unsuccessful Assignment
				if ( found_open_loc == 0 ) {
					sprintf(msg_buf,"No more available locations in product group");
					//fprintf(msg_file, msg_buf);
				}
				else if ( found_open_loc == 1) {
					sprintf(msg_buf,"No available locations match product profile requirements");
					//fprintf(msg_file, msg_buf);
				}
				else if ( found_open_loc == 2) {
					sprintf(msg_buf, "Product will not fit in any available location.");
				}
				else if ( found_open_loc == 3) {
					sprintf(msg_buf, "No bay or level with enough weight for product.");
				}
				else {
					// we should never get here
					sprintf(msg_buf,"Unknown reason.");
				}
				
				if (utilLoop == (numTimes - 1) && (ignoreRankingsFlag != 1 || ignoreRankings == 1) ) {
					strcpy(p4Messages[msgIdx].msg,msg_buf);
					strcpy(p4Messages[msgIdx].prodDesc,mem_prods[i].desc);
					p4Messages[msgIdx].prodPkDBID = mem_prods[i].dbID;
					strcpy(p4Messages[msgIdx].WMSProdID,mem_prods[i].WMSProdID);
					strcpy(p4Messages[msgIdx].WMSProdDetID,mem_prods[i].WMSProdDetID);
					msgIdx++;
				}
				continue;
			}
			//////////////////////////////////////////////////////////////////
			// If the 'Location' we are using is a Variable-Width Bay Level,
			// we have to create its new Location before doing the Product
			// assignment.
			//////////////////////////////////////////////////////////////////
			
			p = NextProdIndex(i, k);
			if ( p < 0 ) {
				if (ignoreRankings > 0)
					p = i;
				else {
					sprintf(msg_buf,"Unknown reason.");
					if (utilLoop == (numTimes - 1) && (ignoreRankingsFlag != 1 || ignoreRankings == 1)  ) {
						strcpy(p4Messages[msgIdx].msg,msg_buf);
						strcpy(p4Messages[msgIdx].prodDesc,mem_prods[i].desc);
						p4Messages[msgIdx].prodPkDBID = mem_prods[i].dbID;
						strcpy(p4Messages[msgIdx].WMSProdID,mem_prods[i].WMSProdID);
						strcpy(p4Messages[msgIdx].WMSProdDetID,mem_prods[i].WMSProdDetID);
						msgIdx++;
					}
					continue;
				}
			}
			
			
			if (mem_locs[k].VarWidth==1) {
				
				if (mem_prods[i].trace)
					fprintf(p4TraceFile, "Assigning product to variable width location.\n");
				
				// Update Bay Level with new Location information.
				widthUsed = UpdateVarWidthBay(k, p);
				
				// Calc cost
				tmpCost = GetCost(p, k, caseCount,costbuf);
				
				if (mem_prods[i].trace)
					fprintf(p4TraceFile, "Cost: %.02f\n", tmpCost);
				
				if(tmpCost < 0.00001f)
					tmpCost = 0.0f;
				
				// Create Result row and report.
				AddResult(	p, k, tmpCost, caseCount, costbuf);
				
				// add the weight to the bay and level
				bayIdx = FindBay(mem_locs[k].BayID);
				if (bayIdx >= 0) {
					mem_bay_weights[bayIdx].currentBayWeight +=
						caseCount * mem_prods[p].caseWeight;
				}
				
				levelIdx = FindLevel(mem_locs[k].Level_dbid);
				if (levelIdx >= 0) {
					mem_level_weights[levelIdx].currentLevelWeight += 
						caseCount * mem_prods[p].caseWeight;
				}
				
				/* ******************************************************* */
				/* Print a successful message to the log file              */
				/* ******************************************************* */
				if ( debugLog || mem_prods[i].trace ) {
					sprintf(msg_buf,"VW Location Created %d, %s\t%W-%d L-%d H-%d\tBayProfID= %d\n",
						mem_locs[k].Loc_dbid, vwLoc[vw_loc_count-1].locDesc, widthUsed,
						mem_locs[k].Loc_d, mem_locs[k].Loc_h, mem_locs[k].BayProfID);
					fprintf(p4TraceFile,"%s", msg_buf);
					fprintf(p4TraceFile,"Successful VW Assignment\n");
				}
				
				
				////////////////// Successful VW Assignment
				sprintf(msg_buf,"Successfully Assigned");
				strcpy(p4Messages[msgIdx].msg,msg_buf);
				strcpy(p4Messages[msgIdx].prodDesc,mem_prods[i].desc);
				p4Messages[msgIdx].prodPkDBID = mem_prods[p].dbID;
				strcpy(p4Messages[msgIdx].WMSProdID,mem_prods[i].WMSProdID);
				strcpy(p4Messages[msgIdx].WMSProdDetID,mem_prods[i].WMSProdDetID);
				msgIdx++;
				
				if ( debugLog || mem_prods[i].trace) { 
					if (mem_locs[k].Assigned == TRUE) {
						sprintf(msg_buf,"Bay Level has been filled.\n");
					} 
					else {
						sprintf(msg_buf,"Remaining width in Bay = %10.4f\n",
							(mem_locs[k].Loc_w - mem_locs[k].prodWidth));
					}
					fprintf(p4TraceFile,"%s", msg_buf);
				}
				
				assigned = 1;
				CostSum += tmpCost;	
				
			} 
			else {
				
				if (mem_prods[i].trace)
					fprintf(p4TraceFile, "Assigning product to fixed width location.\n");
				
				// Assign Prods to Standard Locations
				flag = 0;
				
				sprintf(msg_buf,"Successfully Assigned");
				strcpy(p4Messages[msgIdx].msg,msg_buf);
				strcpy(p4Messages[msgIdx].prodDesc,mem_prods[i].desc);
				p4Messages[msgIdx].prodPkDBID = mem_prods[p].dbID;
				strcpy(p4Messages[msgIdx].WMSProdID,mem_prods[i].WMSProdID);
				strcpy(p4Messages[msgIdx].WMSProdDetID,mem_prods[i].WMSProdDetID);
				msgIdx++;
				
				for(l = 0; l < facingIndexList.size(); ++l) {
					
					int locIndex = facingIndexList[l];
					
					// only calculate cost the first time through since the cost
					// is really on the primary facing
					
					if(flag == 0) {
						tmpCost = GetCost(p, locIndex, caseCount, costbuf);
						if(tmpCost < 0.00001f)
							tmpCost = 0.0f;
						flag = 1;
					} 
					else {
						tmpCost = 0.0f;
						caseCount=0;
					}
					
					TimeStamp("Before AddResult");
					//printf("Assigning: %d\n", locIndex);
					
					AddResult(	p, locIndex, tmpCost, caseCount, costbuf);
					TimeStamp("After AddResult\n");
					
					/* ******************************************************* */
					/* Print a successful message to the log file              */
					/* ******************************************************* */
					if ( debugLog || mem_prods[i].trace) {
						sprintf(msg_buf,"Product %20.20s Assigned to Location %d, %20.20s\t%W-%10.4f L-%10.4f H-%10.4f\tBayProfID= %d\n",
							mem_prods[p].WMSProdID, mem_locs[locIndex].Loc_dbid, mem_locs[locIndex].Loc_desc, 
							mem_locs[locIndex].Loc_w, mem_locs[locIndex].Loc_d,
							mem_locs[locIndex].Loc_h, mem_locs[k].BayProfID);
						fprintf(p4TraceFile,"%s", msg_buf);
					}
					
					// add the weight to the bay and level
					bayIdx = FindBay(mem_locs[locIndex].BayID);
					if (bayIdx >= 0) {
						mem_bay_weights[bayIdx].currentBayWeight += 
							(caseCount/facingIndexList.size())
							* mem_prods[p].caseWeight;
					}
					
					levelIdx = FindLevel(mem_locs[locIndex].Level_dbid);
					if (levelIdx >= 0) {
						mem_level_weights[levelIdx].currentLevelWeight += 
							(caseCount/facingIndexList.size())
							* mem_prods[p].caseWeight;
					}
					
					mem_locs[locIndex].Assigned = 1;
					assigned = 1;
					CostSum += tmpCost;	
				}	// end for each facing
				
			}	// end if not variable width
		}	// end for each product
	}	// end number of times to try loop
	
	TimeStamp("After numTimes loop\n");
	
	return CostSum;
	
}

int Pass4Process::NextProdIndex(int prodIndex, int locIndex)
{
	int i;
	
	for (i=prodIndex;i<mem_prod_count;i++) {
		if (mem_prods[i].dbID != mem_prods[prodIndex].dbID) return -1;
		
		if((mem_locs[locIndex].BayProfID == mem_prods[i].bayProfID) &&
			(mem_locs[locIndex].levelType == mem_prods[i].levelType) &&
			(mem_locs[locIndex].Assigned == 0)) return i;
	}
	
	return -1;
}

/////////////////////////////////////////////////////////////////////////////////
// Returns maximum number of Product cases that will fit in the Location.
// Returns 0 if any Product or Container dimensions are 0.
// Pallet Handling:	Find number of pallets that will fit in the Location and
//					return that number times the number of cases per pallet.
// Case Handling:	Check for optimum case orientation and return number of cases
//					fit using that orientation.
// Replaces old method doesItFit().
/////////////////////////////////////////////////////////////////////////////////
int Pass4Process::MaxCaseFit(p4ProdPack *prod, p4Location *loc)
{
	if (prod->trace) { 
		fprintf(p4TraceFile, "Checking product against location %s\n"
			"\tProduct dimensions: %.02f x %.02f x %.02f\n"
			"\tContainer dimensions: %.02f x %.02f x %.02f\n"
			"\tTi x Hi: %d x %d\n"
			"\tLocation dimensions: %.02f x %.02f x %.02f\n"
			"\tUnit of Issue: %d, Handling Method: %d\n",
			loc->Loc_desc,
			prod->width, prod->length, prod->height,
			prod->containerWidth, prod->containerLength, prod->containerHeight,
			prod->productTi, prod->productHi,
			loc->Loc_w, loc->Loc_d, loc->Loc_h,
			prod->unitOfIssue, loc->handlingMethod);
	}
	
	int cases=0;
	//int palletsHigh, palletsWide, palletsDeep;
	
	// Trap divide by zero.
	if (prod->width==0 || prod->length==0 || prod->height==0)
		return 0;
	
	/////////////////////////////////////////////////////////////////////////////
	// Determine Pallet or Case Handling
	/////////////////////////////////////////////////////////////////////////////
	if ( loc->handlingMethod == 3) {
		
		// Trap divide by zero.
		if (prod->containerWidth==0 || prod->containerLength==0 || prod->containerHeight==0)
			return 0;
		
		p4ProdPack tempProd;
		
		//////////////////////////////////////////////////////////////////////////
		// Pallet rotation.  This is very scary.  The user really needs to be
		// careful when marking a product for rotation when it goes into a pallet
		// location.
		//////////////////////////////////////////////////////////////////////////
		ProdCopy(&tempProd, prod);
		tempProd.width = prod->containerWidth;
		tempProd.length = prod->containerLength;
		if (prod->contOverrideHeight == 0)
			tempProd.height = (prod->caseHeight * prod->productHi) + prod->containerHeight;
		else
			tempProd.height = prod->containerHeight;
		
		if ( loc->caseReorientAllowed == 1 ) {
			if (tempProd.rotateXAxis==TRUE && tempProd.rotateYAxis==TRUE && tempProd.rotateZAxis==TRUE) {
				cases = RotateAnyAxis(&tempProd, loc);
			} 
			else if (tempProd.rotateXAxis==TRUE) {
				cases = RotateXAxis(&tempProd, loc);
			} 
			else if (tempProd.rotateYAxis==TRUE) {
				cases = RotateYAxis(&tempProd, loc);
			} 
			else if (tempProd.rotateZAxis==TRUE) {
				cases = RotateZAxis(&tempProd, loc);
			} 
			else {
				// Locked in all directions.  Calculate cases using existing dimensions.
				cases = NoRotation(&tempProd, loc);
			}
			
			prod->rotatedHeight = tempProd.rotatedHeight;
			prod->rotatedWidth = tempProd.rotatedWidth;
			prod->rotatedLength = tempProd.rotatedLength;
		}
		else {
			cases = NoRotation(&tempProd, loc);
		}
		// cases is actually number of pallets
		
		utilization = 100 * tempProd.height / loc->Loc_h;
		
		cases *= prod->NumInPallet;
		
	} else {
		
		/////////////////////////////////////////////////////////////////////////
		// CASE:  We assume the user interface asks for a single dimension to be
		// locked, e.g. Height, which locks two of the axes.  This limits the
		// possiblities to:  1) no axes locked, 2) any pair of axes locked = any
		// single axis unlocked, and 3) all axes locked.
		/////////////////////////////////////////////////////////////////////////
		double tempWidth,tempHeight,tempLength = 0.0;
		
		if ( prod->unitOfIssue == 3 ) { // placing UOI of pallet into a case location
			
			tempWidth = prod->width;
			tempHeight = prod->height;
			tempLength = prod->length;
			
			prod->width = prod->caseWidth;
			prod->height = prod->caseHeight;
			prod->length = prod->caseLength;
		}
		
		if ( loc->caseReorientAllowed == 1 ) {
			if (prod->rotateXAxis==TRUE && prod->rotateYAxis==TRUE && prod->rotateZAxis==TRUE) {
				cases = RotateAnyAxis(prod, loc);
			} 
			else if (prod->rotateXAxis==TRUE) {
				cases = RotateXAxis(prod, loc);
			} 
			else if (prod->rotateYAxis==TRUE) {
				cases = RotateYAxis(prod, loc);
			} 
			else if (prod->rotateZAxis==TRUE) {
				cases = RotateZAxis(prod, loc);
			} 
			else {
				// Locked in all directions.  Calculate cases using existing dimensions.
				cases = NoRotation(prod, loc);
			}
		}
		else {
			cases = NoRotation(prod, loc);
		}
		if ( prod->unitOfIssue == 3 ) { // placing UOI of pallet into a case location
			prod->width = tempWidth;
			prod->height = tempHeight;
			prod->length = tempLength;
		}
		
		// cases are fit using the unit of issue so
		// we need to convert them to real cases here
		// exception: caseflow types are always fit using cases
		// so do not convert them
		if (cases > 0) {
			if (loc->levelType != BAY_TYPE_FLOW) {
				switch ( prod->unitOfIssue ) {
				case 0 : 
					cases = cases / (int)(prod->casePack);
					break;
				case 1 : 
					cases = cases * (int)(prod->casePack) / (int)(prod->innerPack);
					break;
				case 2 : case 3 :
					cases = cases;
					break;
				}
			}
			if (cases==0) cases = 1;
		}
		
		utilization = 100 * prod->caseHeight / loc->Loc_h;
	} 
	
	/*
	double pl, pw, ph, locCube;
	if ( l->handlingMethod == 3) {	
	if (p->contOverrideHeight != 0)
	ph = p->contOverrideHeight;
	else
	ph = p->caseHeight * p->productHi + p->containerHeight;
	
	  pw = p->containerWidth;
	  pl = p->containerLength;
	  
		//	prodCube = pw * pl * ph;
		
		  }
		  else {		// case handling
		  ph = p->caseHeight;
		  pl = p->caseLength;
		  pw = p->caseWidth;
		  
			prodCube = pw * pl * ph * cases;
			}
			locCube = l->Loc_w * l->Loc_d * (l->Loc_h - l->clearance);
			
			  //utilization = prodCube / locCube * 100;
	*/
	
	
	return cases;
	
}

int Pass4Process::DoesDepthHeightFit(p4ProdPack *p, p4Location *l)
{
	
	if(	(p->length <= l->Loc_d) &&
		(p->height <= (l->Loc_h - l->clearance) )) 
	{
		return 1;
	}
	return 0;
}

void Pass4Process::AddResult(int prodIndex, int locIndex, double cost, int caseCount, char * costbuffer)
{
	void *tmp;
	
	if(mem_result_max == 0){
		mem_result_max = RESULT_MEM_BLOCK;
		mem_result = (ssaResult *)malloc(mem_result_max * sizeof(ssaResult));
		if(mem_result == NULL)
			throw EngineException("Error allocating memory for results",
			__FILE__, __LINE__, 200);
		
		memset(mem_result,0, mem_result_max * sizeof(ssaResult));
	}
	
	if(mem_result_max <= mem_result_count){
		mem_result_max += RESULT_MEM_BLOCK;
		tmp = realloc(mem_result, (mem_result_max * sizeof(ssaResult)));
		if(tmp == NULL)
			throw EngineException("Error resizing result array",__FILE__, __LINE__, 200);
		mem_result = (ssaResult *)tmp;
	}
	
	memset(&mem_result[mem_result_count],0,sizeof(ssaResult));
	
	mem_result[mem_result_count].prod_dbID = mem_prods[prodIndex].dbID;
	strcpy(mem_result[mem_result_count].prod_desc, mem_prods[prodIndex].desc);
	
	mem_result[mem_result_count].loc_dbID = mem_locs[locIndex].Loc_dbid;
	
	/////////////////////////////////////////////////////////////////////////////
	// Use the unique Variable Width description we created for the new Locations.
	/////////////////////////////////////////////////////////////////////////////
	if (mem_locs[locIndex].VarWidth==1) {
		strcpy(mem_result[mem_result_count].loc_desc, vwLoc[vw_loc_count-1].locDesc);
	} else {
		strcpy(mem_result[mem_result_count].loc_desc, mem_locs[locIndex].Loc_desc);
	}
	
	mem_result[mem_result_count].cost = cost;
	mem_result[mem_result_count].caseCount = caseCount;
	mem_result[mem_result_count].locIndex = locIndex;
	mem_result[mem_result_count].prodIndex = prodIndex;
	/////////////////////////////////////////////////////////////////////////////
	// Send negative values for unchanged dimensions, positive for changed.
	/////////////////////////////////////////////////////////////////////////////
	if (mem_prods[prodIndex].rotatedHeight == -1.0)
		mem_result[mem_result_count].newHeight = (double)(0.0 - mem_prods[prodIndex].height);
	else
		mem_result[mem_result_count].newHeight = mem_prods[prodIndex].rotatedHeight;
	
	if (mem_prods[prodIndex].rotatedWidth == -1.0)
		mem_result[mem_result_count].newWidth = (double)(0.0 - mem_prods[prodIndex].width);
	else
		mem_result[mem_result_count].newWidth = mem_prods[prodIndex].rotatedWidth;
	
	if (mem_prods[prodIndex].rotatedLength == -1.0)
		mem_result[mem_result_count].newLength = (double)(0.0 - mem_prods[prodIndex].length);
	else
		mem_result[mem_result_count].newLength = mem_prods[prodIndex].rotatedLength;
	
	mem_result[mem_result_count].varWidth = mem_locs[locIndex].VarWidth;
	
	strcpy(mem_result[mem_result_count].costbuffer,costbuffer);
	
	mem_result_count++;
	
	// brd - 09/27/2000 - New code for the utilization test
	// prevents 2nd utilization pass from trying indexes for products that are already assigned
	if (optimizeType != TACTICAL_LAYOUT) {
		int currentRanking = mem_prods[prodIndex].ranking - 1;
		int rankingIdx = prodIndex - currentRanking;
		while (rankingIdx < mem_prod_count) {
			if (mem_prods[rankingIdx].dbID != mem_prods[prodIndex].dbID)
				break;
			mem_prods[rankingIdx].Assigned = 1;
			rankingIdx++;
		}
	}
	
	if (optimizeType == GROUP_LAYOUT) {
		// Do group assignment stuff
		if(mem_groupassignment_max == 0){
			mem_groupassignment_max = RESULT_MEM_BLOCK;
			mem_group_assignments = (p4GroupAssignment *)malloc(mem_groupassignment_max * sizeof(p4GroupAssignment));
			if(mem_group_assignments == NULL)
				throw EngineException("Error allocating memory for group results",
				__FILE__, __LINE__, 200);
			
			memset(mem_group_assignments,0, mem_groupassignment_max * sizeof(p4GroupAssignment));
		}
		
		if(mem_groupassignment_max <= mem_groupassignment_count){
			mem_groupassignment_max += RESULT_MEM_BLOCK;
			tmp = realloc(mem_group_assignments, (mem_groupassignment_max * sizeof(p4GroupAssignment)));
			if(tmp == NULL)
				throw EngineException("Error resizing group result array",__FILE__, __LINE__, 200);
			mem_group_assignments = (p4GroupAssignment *)tmp;
		}
		
		mem_group_assignments[mem_groupassignment_count].NewLocationDBID = mem_locs[locIndex].Loc_dbid;
		strcpy(mem_group_assignments[mem_groupassignment_count].NewLocationDescription, mem_locs[locIndex].Loc_desc);
		mem_group_assignments[mem_groupassignment_count].ProductDBID = mem_prods[prodIndex].dbID;
		strcpy(mem_group_assignments[mem_groupassignment_count].ProductDescription, mem_prods[prodIndex].desc);
		mem_group_assignments[mem_groupassignment_count].ProductGroupDBID = mem_prods[prodIndex].prodGroupDBID;
		strcpy(mem_group_assignments[mem_groupassignment_count].WMSProductID,  mem_prods[prodIndex].WMSProdID);
		int oldLocIdx = mem_prods[prodIndex].prevLocIdx;
		if (oldLocIdx >= 0) {
			mem_group_assignments[mem_groupassignment_count].OldLocationDBID = 
				mem_locs[oldLocIdx].Loc_dbid;
			strcpy(mem_group_assignments[mem_groupassignment_count].OldLocationDescription, mem_locs[oldLocIdx].Loc_desc);
		}
		else {
			mem_group_assignments[mem_groupassignment_count].OldLocationDBID = 0;
			strcpy(mem_group_assignments[mem_groupassignment_count].OldLocationDescription, "");
		}
		strcpy(mem_group_assignments[mem_groupassignment_count].ProductGroupDescription, mem_prods[prodIndex].prodGroupDesc);
		
		mem_groupassignment_count++;
	}
 }
 
 
 void Pass4Process::SendData(void)
 {
	 int i, usedInBay;
	 unsigned int err;
	 char buffer[BUFSIZE];
	 char tempLocDesc[250];
	 
	 /* ************************************************************** */
	 /* This guy simply packages up all of the results and sends them  */
	 /* back on the socket to the caller.                              */
	 /* ************************************************************** */
	 
	 ////////////////////////////////////////////////////////////////////
	 // Version 1.1
	 // The user's Pass 4 Results screen now has 3 tabbed sub-screens.
	 // The first shows Product/Location assignments as before.
	 // The second shows any case reorientations, and the third shows
	 // how the Locations were laid out in Variable-Width Bay Levels.
	 // Also, rather than try to duplicate the algorithm for Location
	 // numbering here in the Engine, we will record the DBIDs of Aisles
	 // containing Variable Width Locations to send back to the Succeed
	 // Session.
	 ////////////////////////////////////////////////////////////////////
	 
	 ////////////////////////////////////////////////////////////////////
	 // First, send all the locations in Variable Width Bay Levels.  The
	 // Bay Level was sent as one large Location.  Now that we have
	 // figured out how we want to subdivide it, we send that information
	 // first so that the new Locations can be created before we need to
	 // assign Products to them.
	 // The Session's Pick Path algorithm needs the DBIDs of the new
	 // Locations to be in sequence within each Bay Level, so we sort the
	 // array before sending it.  This also allows us to scan the array
	 // and determine which Bay Levels were not completely utilized and
	 // add another Location representing the left-over space.
	 ////////////////////////////////////////////////////////////////////
	 
	 if (vw_loc_count > 0) {
		 qsort(vwLoc, vw_loc_count, sizeof(varWidthLoc), vwComp);
		 
		 usedInBay = 0;
		 for(i=0;i<vw_loc_count;i++){
			 
			 // Check for change of Bay Level after the first row.
			 if (i > 0) {
				 
				 // If this row starts a new Bay Level...
				 if (vwLoc[i].locIndex != vwLoc[i-1].locIndex) {
					 
					 // Check to see if the previous Bay Level was completely
					 // used.  If not, build a return data line for the
					 // Location which will represent the left-over space.
					 if (usedInBay < (int)mem_locs[vwLoc[i-1].locIndex].Loc_w) {
						 
						 // The temporary name consists of the original Location
						 // description and a position number one greater than
						 // the previous one.
						 memset(tempLocDesc, 0, 250);
						 sprintf(tempLocDesc, "%s-VWPos-%d",	mem_locs[vwLoc[i-1].locIndex].Loc_desc,
							 (vwLoc[i-1].order+1));
						 // Send it.
						 memset(buffer, 0, BUFSIZE);
						 sprintf(buffer, "VWL|%d|%d|%d|%s|%5.2f|%d|%d|%d|%d|\n",
							 mem_locs[vwLoc[i-1].locIndex].Loc_dbid,
							 mem_locs[vwLoc[i-1].locIndex].Level_dbid,
							 (int)(mem_locs[vwLoc[i-1].locIndex].Loc_w - usedInBay),
							 tempLocDesc,
							 mem_locs[vwLoc[i-1].locIndex].Aisle_rotation,
							 mem_locs[vwLoc[i-1].locIndex].pickPathDirection,
							 vwLoc[i-1].levelWidth,
							 vwLoc[i-1].levelX,
							 vwLoc[i-1].levelY);
						 if ( debugLog )
							 fprintf(p4TraceFile, "Data Sent : %s",buffer);
						 /***>>>
						 err = p4sock->SendData(buffer, strlen(buffer));
						 <<<***/
						 gfnGetDataStream()->ssData << buffer;
						 /***>>>
						 if(err != strlen(buffer)){
							 throw EngineException("Error sending Variable Width Results",
								 __FILE__, __LINE__, 200);
						 }
						 <<<***/
					 }
					 // Restart the tally for the new Bay Level
					 usedInBay = vwLoc[i].width;
				 } else
					 // Continue the tally for the current Bay Level
					 usedInBay += vwLoc[i].width;
			 } else
				 // This is the first row.  Add the width to 0.
				 usedInBay += vwLoc[i].width;
			 
			 memset(buffer, 0, BUFSIZE);
			 sprintf(buffer, "VWL|%d|%d|%d|%s|%5.2f|%d|%d|%d|%d|\n",
				 mem_locs[vwLoc[i].locIndex].Loc_dbid,
				 mem_locs[vwLoc[i].locIndex].Level_dbid,
				 vwLoc[i].width,
				 vwLoc[i].locDesc,
				 mem_locs[vwLoc[i].locIndex].Aisle_rotation,
				 mem_locs[vwLoc[i].locIndex].pickPathDirection,
				 vwLoc[i].levelWidth,
				 vwLoc[i].levelX,
				 vwLoc[i].levelY);
			 if ( debugLog )
				 fprintf(p4TraceFile,"Data Sent: %s", buffer);
			 /***>>>
			 err = p4sock->SendData(buffer, strlen(buffer));
			 <<<***/
			 gfnGetDataStream()->ssData << buffer;
			 /***>>>
			 if(err != strlen(buffer)){
				 throw EngineException("Error sending Variable Width Results",
					 __FILE__, __LINE__, 200);
			 }
			 <<<***/
		 }
		 
		 // Check to see if the last Bay Level was completely
		 // used.  If not, build a return data line for the
		 // Location which will represent the left-over space.
		 if (usedInBay < (int)mem_locs[vwLoc[i-1].locIndex].Loc_w) {
			 
			 // The temporary name consists of the original Location
			 // description and a position number one greater than
			 // the previous one.
			 memset(tempLocDesc, 0, 250);
			 sprintf(tempLocDesc, "%s-VWPos-%d",	mem_locs[vwLoc[i-1].locIndex].Loc_desc,
				 (vwLoc[i-1].order+1));
			 // Send it.
			 memset(buffer, 0, BUFSIZE);
			 sprintf(buffer, "VWL|%d|%d|%d|%s|%5.2f|%d|%d|%d|%d|\n",
				 mem_locs[vwLoc[i-1].locIndex].Loc_dbid,
				 mem_locs[vwLoc[i-1].locIndex].Level_dbid,
				 (int)(mem_locs[vwLoc[i-1].locIndex].Loc_w - usedInBay),
				 tempLocDesc,
				 mem_locs[vwLoc[i-1].locIndex].Aisle_rotation,
				 mem_locs[vwLoc[i-1].locIndex].pickPathDirection,
				 vwLoc[i-1].levelWidth,
				 vwLoc[i-1].levelX,
				 vwLoc[i-1].levelY);
			 if ( debugLog )
				 fprintf(p4TraceFile,"Data Sent : %s", buffer);
			 /***>>>
			 err = p4sock->SendData(buffer, strlen(buffer));
			 <<<***/
			 gfnGetDataStream()->ssData << buffer;
			 /***>>>
			 if(err != strlen(buffer)){
				 throw EngineException("Error sending Variable Width Results",
					 __FILE__, __LINE__, 200);
			 }
			 <<<***/
		 }
		 
		 ////////////////////////////////////////////////////////////////////
		 // Now send the list of Aisle that may need new Pick Paths.
		 ////////////////////////////////////////////////////////////////////
		 for(i=0;i<vw_aisle_count;i++){
			 memset(buffer, 0, BUFSIZE);
			 sprintf(buffer, "A|%d|\n", vwAisle[i]);
			 if ( debugLog )
				 fprintf(p4TraceFile,"Data Sent : %s", buffer);
			 /***>>>
			 err = p4sock->SendData(buffer, strlen(buffer));
			 <<<***/
			 gfnGetDataStream()->ssData << buffer;
			 /***>>>
			 if(err != strlen(buffer)){
				 throw EngineException("Error sending Variable Width Aisle",
					 __FILE__, __LINE__, 200);
			 }
			 <<<***/
		 }
	}
	
	// Next, the basic Results data.  This also contains the reorientation data.
	for(i=0;i<mem_result_count;i++){
		memset(buffer, 0, BUFSIZE);
		sprintf(buffer, "RES|%d|%s|%d|%s|%f|%d|%8.3f|%8.3f|%8.3f|%s|%s|%s\n",
			mem_result[i].prod_dbID, mem_result[i].prod_desc,
			mem_result[i].loc_dbID, mem_result[i].loc_desc,
			mem_result[i].cost, mem_result[i].caseCount,
			mem_result[i].newHeight, mem_result[i].newWidth,
			mem_result[i].newLength,
			mem_prods[mem_result[i].prodIndex].WMSProdID, 
			mem_prods[mem_result[i].prodIndex].WMSProdDetID,
			mem_result[i].costbuffer);
		if ( debugLog ) {
			fprintf(p4TraceFile,"Data Sent : %s", buffer); 
			fflush(p4TraceFile);
		}
		/***>>>
		err = p4sock->SendData(buffer, strlen(buffer));
		<<<***/
		gfnGetDataStream()->ssData << buffer;
		/***>>>
		if(err != strlen(buffer)){
			throw EngineException("Error sending Results",
				__FILE__, __LINE__, 200);
		}
		<<<***/
	}
	
	// Create the prodloc records which will be used by the cost calculation
	for (i=0; i < mem_result_count; ++i) {
		memset(buffer, 0, BUFSIZE);
		p4ProdPack *p = &mem_prods[mem_result[i].prodIndex];
		p4Location *l = &mem_locs[mem_result[i].locIndex];
		
		sprintf(buffer, "PRODLOC|0|%s|%f|%f|%f|%f|%d|%d|%f|%d|%d|"
			"%d|%f|%s|%f|%f|%f|%d|%d|%d|%d|%f|%f|%f|%f|%f|%f|"
			"%f|%f|%f|%d|%d|%d|%f|%f|%f|%d|%d|%s|%s|%d|%d|%f|%f|%f|%f|%d|%s\n",		
			p->desc, p->caseHeight, p->caseWidth, p->caseLength,
			p->movement, l->BayProfID, p->NumInPallet, p->weight,
			l->SecID, l->levelType, l->RelLev, l->LevTime,
			mem_result[i].loc_desc,l->Loc_w,l->Loc_d, l->Loc_h,
			l->Loc_x, l->Loc_y, l->Loc_z, l->BayProfID,
			l->forkFixedExtraction,
			p->innerHeight, p->innerWidth, p->innerLength,
			p->eachHeight, p->eachWidth, p->eachLength,
			p->casePack, p->innerPack, p->productTi,
			p->productHi, p->unitOfIssue, p->containerWidth,
			p->containerLength, p->containerHeight,
			mem_result[i].caseCount, l->handlingMethod,
			p->WMSProdID, p->WMSProdDetID,
			p->dbID,
			((mem_result[i].caseCount > 0) ? 1 : 0),
			mem_result[i].newWidth, mem_result[i].newLength,
			mem_result[i].newHeight, p->caseMovement, l->Loc_dbid, mem_result[i].loc_desc);
		
		if ( debugLog ) {
			fprintf(p4TraceFile,"Data Sent : %s", buffer); 
			fflush(p4TraceFile);
		}
		/***>>>
		err = p4sock->SendData(buffer, strlen(buffer));
		<<<***/
		gfnGetDataStream()->ssData << buffer;
		/***>>>
		if(err != strlen(buffer)){
			throw EngineException("Error sending Results",
				__FILE__, __LINE__, 200);
		}
		<<<***/
	}
	
	if (optimizeType == GROUP_LAYOUT) {
		// If the constrain type is group, send back the group information
		for(i=0;i<mem_groupassignment_count;i++){
			memset(buffer, 0, BUFSIZE);
			sprintf(buffer, "G|%s|%s|%s|%s|%s|%d|%d|%d|%d|\n",
				mem_group_assignments[i].WMSProductID, mem_group_assignments[i].ProductDescription, 
				mem_group_assignments[i].ProductGroupDescription,
				mem_group_assignments[i].OldLocationDescription, mem_group_assignments[i].NewLocationDescription, 
				mem_group_assignments[i].ProductDBID, mem_group_assignments[i].ProductGroupDBID, 
				mem_group_assignments[i].OldLocationDBID, mem_group_assignments[i].NewLocationDBID);
			if ( debugLog ) {
				fprintf(p4TraceFile,"Group Data Sent : %s", buffer); 
				fflush(p4TraceFile);
			}
			
			/***>>>
			err = p4sock->SendData(buffer, strlen(buffer));
			<<<***/
			gfnGetDataStream()->ssData << buffer;
			/***>>>
			if(err != strlen(buffer)){
				throw EngineException("Error sending group data",
					__FILE__, __LINE__, 200);
			}
			<<<***/
		}
	}
	
	if (optimizeType == TACTICAL_LAYOUT) {
		// If the constrain type is tactical, send back the group information
		p4TacticalInfo *p;
		
		for(i=0;i<mem_tacticalinfo_count;i++){
			memset(buffer, 0, BUFSIZE);
			p = &mem_tactical_info[i];
			
			if (strcmp(p->FromWMSProductID, "101043") == 0)
				int z = 0;
			
			sprintf(buffer, "T|%s|%s|%s|%d|%d|%s|%d|%d|%s|%s|%10.3f|%10.3f|%10.3f|%d|%d|\n", 
				p->FromWMSProductID,
				p->FromProductDesc,
				p->FromProductOrigLoc,
				p->FromProductOrigFacings,
				p->FromProductOrigBayProfileDBID,
				p->FromProductNewLoc,
				p->FromProductNewFacings,
				p->FromProductNewBayProfileDBID,
				p->ToWMSProductID,
				p->ToProductDesc,
				p->FromCost,
				p->ToCost,
				p->MoveCost, p->FromProductDBID, p->ToProductDBID);
			
			if ( debugLog ) {
				fprintf(p4TraceFile,"Tactical Data Sent : %s", buffer); 
				fflush(p4TraceFile);
			}
			/***>>>
			err = p4sock->SendData(buffer, strlen(buffer));
			<<<***/
			gfnGetDataStream()->ssData << buffer;
			/***>>>
			if(err != strlen(buffer)){
				throw EngineException("Error sending tactical data",
					__FILE__, __LINE__, 200);
			}
			<<<***/
		}
	}
}


/* ************************************************************************ */
/* This function will go through the process of computing the cost for the  */
/* placement of a particular product in a particular location.  Here is the */
/* algorithm behind the process.                                            */
/* cost = ((travel to/from loc) + (product handling)) * Freq.               */
/*   (travel to/from loc) = (Fork Travel Costs) + (Selection Travel Costs)  */
/*      (Fork Costs) = (putaway costs) + (replenishment costs)              */
/*         (putaway) = (dist * var + fxd) * MTH * Rate                      */
/*            (Dist) = (ProdMmovement/numinpal) * dist to hot * 2           */
/*         (replen)  = (dist * var + fxd) * MTH * Rate                      */
/*            (dist) = total_replens * avg_replen_dist * 2                  */
/*               (total_replens) =(prodMovement/numinpal)*(pal_cube/loc_cube) */
/*         (MTH) = MIN_TO_HOUR                                              */
/*      (Selection Costs) = ((Dist) * (scale) * (Var) + (Fxd)) * MTH * Rate */
/*         (Dist) = Total Distance for moveing through the section.         */
/*                = Total Pick path distance for section + Broken order travel */
/*           (TotalPickPath) = Length of all pick path segments in section. */
/*                                * Number of Orders                        */
/*           (Broken Order Travel) = Dist To Hot spot from center of section */
/*                                       * Number of Broken Orders          */
/*             (NumberBroken) = (avg order qty)/(container qty)             */
/*         (scale) = prodMovement / (TotalMovementInSection)                */
/*         (MTH) = MIN_TO_HOUR                                              */
/*   (product handling) = (Case handling) + (Fork handling)                 */
/*      (Case) = (A[c] * prodWeight) + B[c])                                */
/*         A[c] = Case switch for appropriate Cube of product.              */
/*         B[c] = Case switch for appropriate Cube of product.              */
/*      (Fork) = (fixedInsertionTime / NumOnPal);                           */
/*   Freq = Movement;                                                       */
/* ************************************************************************ */    
double Pass4Process::CalcLabor(ssaLaborProd *p, ssaLaborLoc *l, int casesInPick, char * buffer)
{
	double SScale;
	double PtwyCost, ReplenCost;
	double Cost;
	double BrokenOrderDist;
	double BrokenOrderCount;
	double tempdouble=0.0;
	int i,j;
	
	memset(buffer, 0, COST_BUFFER_SIZE);
	
	sprintf(buffer, "%s|%s|%s|%d|%d", p->desc, l->desc,
		mem_sections[l->section_idx].SecDesc,
		mem_sections[l->section_idx].SecID,
		p->bayProfID);
	
	
	sprintf(buffer, "%s|%10.5f", buffer, p->movement);
	
	// This is where the putaway calculation begins for the forklift cost.
	/* *************************************************** */
	/* Putaway calculation:                                */
	/* *************************************************** */
	
	////////////////////////////////////////////////////////////////////////////
	// First, calculate the distance from the Forklift hotspot to the current
	// location.  Do this with a line of sight calculation.
	////////////////////////////////////////////////////////////////////////////
	
	double putawayDistance = distance(l->x, l->y, l->fork_hot_x, l->fork_hot_y);
	
	
	// convert from inches/centimeters to feet/meters
	putawayDistance /= p4_inches_to_foot;
	
	
	////////////////////////////////////////////////////////////////////////////
	// Calculate the number of putaways.  This is done by dividing the movement
	// of the current product (given in cases per week) by the number of cases
	// in a pallet which already yielded palletMovement.  This will tell us how many 
	// pallets per week we will have to deal with.  Hence, the number of putaways.
	
	if ( l->numPutsPerTrip == 0 )
		l->numPutsPerTrip = 1;
	
	//holdPutAwayVal = ((double)p->palletMovement);
	double putaways = p->palletMovement;
	double putawayTrips = ((double)p->palletMovement / l->numPutsPerTrip);
	
	sprintf(buffer, "%s|%10.5f", buffer, 2*putawayDistance*putawayTrips);
	
	// dex: we should display number of putaways, not number of trips
	sprintf(buffer, "%s|%10.5f|%d", buffer, putaways, p->NumInPallet);  // Number of ptwy's
	
	
	////////////////////////////////////////////////////////////////////////////
	// Forklift handling time is given by the number of putaways multiplied by the
	// engineered values for the time it takes to accomplish a putaway.
	////////////////////////////////////////////////////////////////////////////
	
	// pickForkTrav is the time (minutes) it takes to pick up the pallet from the dock
	// insertForkTrav is the time (minutes) it takes to put the pallet into the location
	
	// we have to insert every pallet in a different location so we use palletMovement but we only 
	// have to go to the dock once to pick up all the pallets for the trip so the extra
	// time for multiple pallets is negligible.
	
	// dex:  num pallets * insertion + num trips * pick up
	//ForkHandle = (l->pickForkTrav+l->insertForkTrav) * numPutAways;	
	double putawayForkHandlingTime = putawayTrips * l->pickForkTrav + p->palletMovement * l->insertForkTrav;
	
	// Convert minutes to hours
	putawayForkHandlingTime *= MIN_TO_HOUR;
	
	////////////////////////////////////////////////////////////////////////////
	// Here we take the number of putaways, multiply by the forklift travel
	// distance by two to get the total putaway travel since we have to to and
	// from the dock.  We then convert this distance into a time value.
	////////////////////////////////////////////////////////////////////////////
	
	// fork_dist_var is the travel rate in feet/meters per minute
	// fork_dist_fxd is the amount of startup time 
	// the startup time is used for each trip; the variable time is based on the total distance
	
	// dex:  ((trips * distance * 2 * variable) + (trips * 2 * fixed))  /p4_inches_to_foot
	//double putawayTravelTime = (numPutAways * ForkDist * 2) * (l->fork_dist_var / p4_inches_to_foot) + l->fork_dist_fxd;
	
	double putawayStartupTime = 2 * putawayTrips * l->fork_dist_fxd;
	
	// we already converted distance into feet
	double putawayTravelTime = 2 * putawayTrips * putawayDistance * l->fork_dist_var;
	
	// add the startup time to the travel time to get the total travel time
	double putawayTotalTravelTime = putawayStartupTime + putawayTravelTime;
	
	// convert from minutes to hours
	putawayTravelTime *= MIN_TO_HOUR;
	
	sprintf(buffer, "%s|%10.5f", buffer, putawayTravelTime);  // Travel Hours
	
	sprintf(buffer, "%s|%10.5f", buffer, putawayForkHandlingTime); // Handling Hours
	
	////////////////////////////////////////////////////////////////////////////
	// Multiply both the travel time (PtwyCost) and the Handling time by the
	// forklift hourly rate to convert the fork putaway time into a dollar value.
	////////////////////////////////////////////////////////////////////////////
	
	// Total time is the travel time added to the handling time
	double putawayTotalTime = putawayTravelTime + putawayForkHandlingTime;
	
	// Cost is total time multiplied by the hourly pay
	PtwyCost = putawayTotalTime * l->fork_rate;
	sprintf(buffer, "%s|%10.5f",  buffer, PtwyCost); // Ptwy cost.
	
	
	
	// The Forklift Replenishment calculation begins here:
	/* *************************************************** */
	/* Replenishment Calculation:                          */
	/* *************************************************** */
	
	////////////////////////////////////////////////////////////////////////////
	// Sanity check on width, depth, and height of location because we will be
	// using these as divisors in a future calculation.
	////////////////////////////////////////////////////////////////////////////
	if (l->w==0) l->w=1;
	if (l->d==0) l->d=1;
	if (l->h==0) l->h=1;
	
	////////////////////////////////////////////////////////////////////////////
	// If this is the dummy product calculation (casesInPick == -1), or if the
	// Location holds a full pallet or more, the number of Replenishments needed
	// over the time horizon will be the same as the number of Put-Aways.
	// Otherwise, calculate the ratio of Replenishments to Put-Aways.
	////////////////////////////////////////////////////////////////////////////
	double replensToPWs;
	if ((casesInPick >= p->NumInPallet) || (casesInPick == -1) || (casesInPick == 0))
		replensToPWs = 1.0;
	else
		replensToPWs = (double)(p->NumInPallet / casesInPick);
	
	double replenishments = replensToPWs * putaways;
	
	// No replenishments for full pallet selects
	if ( l->handlingMethod == 3 && p->unitOfIssue == 3 )
		replenishments = 0;
	
	sprintf(buffer, "%s|%10.5f", buffer, replenishments); // Number of Replens.
	
	////////////////////////////////////////////////////////////////////////////
	// Calculate replenishement travel distance.
	////////////////////////////////////////////////////////////////////////////
	// avg_replen_dist is the average distance to go from the replenishment area
	// to the selection area; the hotspot is not involved because they are not
	// going to the dock; assume they have to go both directions
	double replenishmentDistance = replenishments * l->avg_replen_dist * 2;
	sprintf(buffer, "%s|%10.5f", buffer, replenishmentDistance);
	
	// Convert replenishment distance from inches/centimeters to feet/meters
	// brd - distance should already be in feet
	//replenishmentDistance /= p4_inches_to_foot;
	
	////////////////////////////////////////////////////////////////////////////
	// Turn the distance into a time by using the calculated engineering standards.
	////////////////////////////////////////////////////////////////////////////
	// dex: time = (distance * variable + numreplens * fixed)/p4inchestofoot
	double replenishmentTravelTime = replenishmentDistance * l->fork_dist_var;
	double replenishmentStartupTime = replenishments *  l->fork_dist_fxd;
	double replenishmentTotalTravelTime = replenishmentStartupTime + replenishmentTravelTime;
	
	
	// Convert time to hours
	replenishmentTotalTravelTime = replenishmentTotalTravelTime * MIN_TO_HOUR;
	sprintf(buffer, "%s|%10.5f", buffer, replenishmentTotalTravelTime); // Travel Hours
	
	////////////////////////////////////////////////////////////////////////////
	// Our Forklift handling time is simply the insertion time for a pallet on
	// this level times the number of replenishments per week that we handle.
	////////////////////////////////////////////////////////////////////////////
	// fork_fixed_insertion - time (minutes) it takes to put a pallet in this location
	double replenishmentHandlingTime = l->fork_fixed_insertion * replenishments;
	
	// Convert from minutes to hours
	replenishmentHandlingTime *= MIN_TO_HOUR;
	sprintf(buffer, "%s|%10.5f", buffer, replenishmentHandlingTime); // Handling Hours
	
	////////////////////////////////////////////////////////////////////////////
	// Multiply both the handling time and the replenishment travel time by the
	// hourly forklift rate to get a dollar amount for replenishment cost.
	////////////////////////////////////////////////////////////////////////////
	double totalReplenishmentTime = replenishmentHandlingTime + replenishmentTotalTravelTime;
	ReplenCost = totalReplenishmentTime * l->fork_rate;
	sprintf(buffer, "%s|%10.5f", buffer, ReplenCost); // Replen Cost.
	
	////////////////////////////////////////////////////////////////////////////
	// Total forklift cost is Putaway Cost plus Replenishment cost.
	////////////////////////////////////////////////////////////////////////////
	double ForkTotalCost = PtwyCost + ReplenCost;
	
	// Selection cost calculations start here.
	/* ********************************************************* */
	/* Selection Costs                                           */
	/* ********************************************************* */
	
	////////////////////////////////////////////////////////////////////////////
	// Check to see what our broken order distance will be.  If the current
	// section utilizes broken order distances, the distance is calculated as a
	// straight line from the center of the section to the selection hot spot.
	// If the current section does not utilize broken order travel, just set the
	// distance to zero and the rest of the calculations will not have to change.  
	////////////////////////////////////////////////////////////////////////////
	if(mem_sections[l->section_idx].ApplyBrokenOrder == 1){
		//	BrokenOrderDist = distance(mem_sections[l->section_idx]., mem_sections[l->section_idx].ave_y,
		//		l->sel_hot_x, l->sel_hot_y);
		BrokenOrderDist = distance(l->x, l->y,l->sel_hot_x, l->sel_hot_y);	// inches
	} else {
		BrokenOrderDist = 0;
	}
	
	//	if (strcmp(l->desc, "LV121") == 0 || strcmp(l->desc, "MC001") == 0)
	//		printf("break here\n");
	
	////////////////////////////////////////////////////////////////////////////
	// Broken order travel is two ways.  Multiply by two to take this into account. 
	////////////////////////////////////////////////////////////////////////////
	BrokenOrderDist *=2;
	sprintf(buffer, "%s|%10.5f", buffer, BrokenOrderDist / p4_inches_to_foot);	// feet
	
	////////////////////////////////////////////////////////////////////////////
	// Sanity check on the ContainerQty.
	////////////////////////////////////////////////////////////////////////////
	if (mem_sections[l->section_idx].ContainerQty==0)
		mem_sections[l->section_idx].ContainerQty=1;
	
	////////////////////////////////////////////////////////////////////////////
	// We figure out how many broken order travel segments to allow for by the
	// size of the containers used in this section, and the average quantity for
	// each order. 
	////////////////////////////////////////////////////////////////////////////
	BrokenOrderCount = ((double)(mem_sections[l->section_idx].AvgOrdQty) /
		(double)(mem_sections[l->section_idx].ContainerQty));
	
	
	sprintf(buffer, "%s|%10.5f", buffer, BrokenOrderCount);
	
	// This is the pick path distance	
	double SelDist = mem_sections[l->section_idx].SelDist;
	
	// Add in one trip from the center to the hotspot to account for 
	// getting in and out of the section
	SelDist += distance(mem_sections[l->section_idx].ave_x, mem_sections[l->section_idx].ave_y,
		l->sel_hot_x, l->sel_hot_y);	// inches
	
	sprintf(buffer, "%s|%10.5f", buffer, SelDist / p4_inches_to_foot);		// feet
	sprintf(buffer, "%s|%d", buffer, mem_sections[l->section_idx].OrderCount);
	
	////////////////////////////////////////////////////////////////////////////
	// Total selection distance is made up of pickpath distance plus broken
	// order travel, both multiplied by the order count for that section.  This
	// is the total travel distance for all orders for the week in this section.
	// If these are full pallet selects, the travel is just the distance from the
	// location to the selection hotspot
	////////////////////////////////////////////////////////////////////////////
	
	
	double selectionTotalTravelTime = 0;
	double fullPalletTotalTravelTime = 0;
	
	// This is a full pallet select
	if ( ( l->handlingMethod == 3 && p->unitOfIssue == 3 ) ) { 
		// dex: (full pallet travel) = same as Putaway travel except use selection hotspot (avg dist from loc to selection hotspot)
		double fullPalletDistance = distance(l->x, l->y, l->sel_hot_x, l->sel_hot_y);
		
		// Convert to feet/meters
		fullPalletDistance /= p4_inches_to_foot;		// inches
		
		SScale = (double)((p->caseCube * p->caseMovement) / l->totalExtendedCube);
		sprintf(buffer, "%s|%10.5f", buffer, fullPalletDistance);		// feet
		sprintf(buffer, "%s|%10.5f", buffer, SScale);
		
		
		// ? for dex - do we care about scale here?
		double fullPalletStartupTime = 2 * p->palletMovement * l->fork_dist_fxd;
		
		double fullPalletTravelTime = 2 * p->palletMovement * fullPalletDistance * l->fork_dist_var;
		
		// add the startup time to the travel time to get the total travel time
		fullPalletTotalTravelTime = fullPalletStartupTime + fullPalletTravelTime;
		
		// convert from minutes to hours
		fullPalletTotalTravelTime *= MIN_TO_HOUR;
		
		sprintf(buffer, "%s|%10.5f", buffer, fullPalletTotalTravelTime);  // Travel Hours
	}
	
	// Not full pallet 
	else {
		
		double selectionTravelDistance = (SelDist + (BrokenOrderDist * BrokenOrderCount)) 
			* mem_sections[l->section_idx].OrderCount;		// inches
		
		
		// Convert distance from inches/centimeters to feet/meters
		selectionTravelDistance /= p4_inches_to_foot;
		
		////////////////////////////////////////////////////////////////////////////
		// Sanity check on movement as it is central to the rest of the calculations
		// that we will make.
		////////////////////////////////////////////////////////////////////////////
		if ( l->totalExtendedCube < 1 )
			l->totalExtendedCube = holdExtendedCube; // default to the total XCube for this PG
		
		////////////////////////////////////////////////////////////////////////////
		// Calculate a scale that will indicate what percentage of work one product
		// is compared to the total in this section.  Thus the scale is the
		// individual product's extended cube divided by the section's extended cube
		// We use this because otherwise every selection would have equal weight as
		// far as the travel when in reality the ones that move the most should
		// be weighted higher
		////////////////////////////////////////////////////////////////////////////
		SScale = (double)((p->caseCube * p->caseMovement) / l->totalExtendedCube);
		sprintf(buffer, "%s|%10.5f", buffer, selectionTravelDistance*SScale);	// feet
		sprintf(buffer, "%s|%10.5f", buffer, SScale);
		
		////////////////////////////////////////////////////////////////////////////
		// Convert the selection distance into a time by using the engineered values
		// for this section.
		////////////////////////////////////////////////////////////////////////////
		// dex: (dist * scale * var + dist * brokenordercount * 2)
		//SelTCost = (SelDist * SScale * l->sel_dist_var / p4_inches_to_foot + l->sel_dist_fxd);
		
		// the distance times the rate of travel times the scael
		double selectionTravelTime = selectionTravelDistance * SScale * l->sel_dist_var;
		
		// broken order count is at least one; this tells us how many times they had to
		// get going
		double selectionStartupTime = 2 * BrokenOrderCount * l->sel_dist_fxd;
		
		selectionTotalTravelTime = selectionTravelTime + selectionStartupTime;
		
		// Convert minutes to hours
		selectionTotalTravelTime *= MIN_TO_HOUR;
		
		sprintf(buffer, "%s|%10.5f", buffer, selectionTotalTravelTime); // Travel Hours
	}
	
	
	////////////////////////////////////////////////////////////////////////////
	// Find the correct level engineering values by using the level index and
	// the cube value for the current product.  This will give us the variable
	// and the fixed factors.
	////////////////////////////////////////////////////////////////////////////
	
	double unitMovement = p->movement;
	double unitWeight = p->weight;
	
	double fullPalletHandlingTime, selectionPickHandlingTime, selectionStockerHandlingTime;
	
	//////////////////////////////////////////////////////////////////////
	// pallet product going to pallet location
	//////////////////////////////////////////////////////////////////////
	if ( ( l->handlingMethod == 3 && p->unitOfIssue == 3 ) ) { 
		fullPalletHandlingTime = l->forkFixedExtraction;
		selectionPickHandlingTime = 0;
		selectionStockerHandlingTime = 0; 
	}
	//////////////////////////////////////////////////////////////////////
	// pallet product going to case location - use case movement, 
	// dimensions, weight, etc.
	//////////////////////////////////////////////////////////////////////
	else if ( p->unitOfIssue == 3 && l->handlingMethod == 1 ) { 
		//p->cube = p->cube / ( p->NumInPallet );  // scale down to the case cube
		unitMovement = p->movement / ( p->NumInPallet );
		unitWeight = p->weight / ( p->NumInPallet );
		
		i = FindRTLevCubeSelect(l->BayProfID, l->level,p->caseCube);
		j = FindRTLevCubeStocker(l->BayProfID, l->level,p->caseCube);
		
		fullPalletHandlingTime = 0;
		if ( i > -1 )
			selectionPickHandlingTime = (mem_labor_select[i].Var * p->weight + mem_labor_select[i].Fxd);
		else
			selectionPickHandlingTime = 0;
		if ( j > -1 )
			selectionStockerHandlingTime = (mem_labor_stocker[j].Var * p->weight + mem_labor_stocker[j].Fxd);
		else
			selectionStockerHandlingTime = 0;
		
	}
	//////////////////////////////////////////////////////////////////////
	// Product going to a case handling location, use UOI values for
	// dimensions, weight, etc.
	//////////////////////////////////////////////////////////////////////
	else { 
		i = FindRTLevCubeSelect(l->BayProfID, l->level,p->cube);
		// brd - use case cube to get stocker values on case flow locations
		if (l->BayType == BAY_TYPE_FLOW)
			j = FindRTLevCubeStocker(l->BayProfID, l->level,p->caseCube);
		else
			j = FindRTLevCubeStocker(l->BayProfID, l->level,p->cube);
		
		fullPalletHandlingTime = 0;
		if ( i > -1 )
			selectionPickHandlingTime = (mem_labor_select[i].Var * p->weight + mem_labor_select[i].Fxd);
		else
			selectionPickHandlingTime = 0;
		if ( j > -1 )
			// brd - use case cube to get stocker handling times on case flow locations
			if (l->BayType == BAY_TYPE_FLOW)
				selectionStockerHandlingTime = (mem_labor_stocker[j].Var * p->caseWeight + mem_labor_stocker[j].Fxd);
			else
				selectionStockerHandlingTime = (mem_labor_stocker[j].Var * p->weight + mem_labor_stocker[j].Fxd);
			else
				selectionStockerHandlingTime = 0;
	}	
	
	////////////////////////////////////////////////////////////////////////////
	// Get the handling time by using the variable and fixed factors that
	// we just looked up, combined with the weight of the current product.
	////////////////////////////////////////////////////////////////////////////
	selectionPickHandlingTime *= MIN_TO_HOUR;
	sprintf(buffer, "%s|%10.5f", buffer, selectionPickHandlingTime); // Hours per touch
	
	// UOI handle * movement
	// brd - changed this to use UOI movement
	// selectionPickHandlingTime = selectionPickHandlingTime * p->caseMovement;
	selectionPickHandlingTime = selectionPickHandlingTime * unitMovement;
	sprintf(buffer, "%s|%10.5f", buffer, selectionPickHandlingTime); // Handle Hours per week
	
	// Pick cost
	double selectionPickHandlingCost = selectionPickHandlingTime * l->sel_rate;
	
	sprintf(buffer, "%s|%10.5f", buffer, selectionPickHandlingCost); // Handle cost
	
	// Stocker handle
	selectionStockerHandlingTime *= MIN_TO_HOUR;
	sprintf(buffer, "%s|%10.5f", buffer, selectionStockerHandlingTime); // Hours per touch
	
	// Stocker handle * movement
	// brd - use case movement for stocker in case flow locations
	if (l->BayType == BAY_TYPE_FLOW)
		selectionStockerHandlingTime = selectionStockerHandlingTime * p->caseMovement;
	else
		selectionStockerHandlingTime = selectionStockerHandlingTime * unitMovement;
	sprintf(buffer, "%s|%10.5f", buffer, selectionStockerHandlingTime); // Handle Hours per week
	
	// Stocker cost
	double selectionStockerHandlingCost = selectionStockerHandlingTime * l->stockerRate;
	sprintf(buffer, "%s|%10.5f", buffer, selectionStockerHandlingCost);
	
	
	// Fork (PS) handle
	fullPalletHandlingTime *= MIN_TO_HOUR;
	sprintf(buffer, "%s|%10.5f", buffer, fullPalletHandlingTime); // Hours per touch
	
	// Fork (PS) handle * movement
	fullPalletHandlingTime = fullPalletHandlingTime * p->palletMovement;
	sprintf(buffer, "%s|%10.5f", buffer, fullPalletHandlingTime); // Handle Hours per week
	
	// Fork (PS) cost
	double fullPalletSelectCost = fullPalletHandlingTime * l->fork_rate;
	sprintf(buffer, "%s|%10.5f", buffer, fullPalletSelectCost); // Handle Hours per week
	
	////////////////////////////////////////////////////////////////////////////
	// Total hours:  Add the Travel hours together with the handling hours to
	// get the total hours used for this product for selection activity.
	// Multiply this by the hourly rate to get a dollar value.
	////////////////////////////////////////////////////////////////////////////
	double allSelectionTravelTime = selectionTotalTravelTime + fullPalletTotalTravelTime;
	double allSelectionHandlingTime = selectionPickHandlingTime + selectionStockerHandlingTime + fullPalletHandlingTime;
	
	double allSelectionTime = allSelectionTravelTime + allSelectionHandlingTime;
	sprintf(buffer, "%s|%10.5f", buffer, allSelectionTime);
	
	// Cases per hour
	double casesPerHour = 0;
	
	double nonForkTime = selectionTotalTravelTime + selectionPickHandlingTime + selectionStockerHandlingTime;
	
	if ( nonForkTime > 0 )
		casesPerHour = p->caseMovement / nonForkTime;
	
	double forkTime = fullPalletTotalTravelTime + fullPalletHandlingTime;
	
	if ( forkTime > 0 )
		casesPerHour += p->palletMovement * p->NumInPallet / forkTime;
	
	sprintf(buffer, "%s|%10.5f", buffer, casesPerHour); //cases per hour
	
	// selection cost
	double SelectionTotalCost = (selectionTotalTravelTime * l->sel_rate) + ( selectionPickHandlingTime * l->sel_rate )  
		+ ( selectionStockerHandlingTime * l->stockerRate ) 
		+ (fullPalletTotalTravelTime * l->fork_rate)
		+ (fullPalletHandlingTime * l->fork_rate);
	
	sprintf(buffer, "%s|%10.5f", buffer, SelectionTotalCost);
	
	////////////////////////////////////////////////////////////////////////////
	// Total cost is forklift cost plus selection cost.  Add them together, log
	// and return the result.
	////////////////////////////////////////////////////////////////////////////
	Cost = ForkTotalCost + SelectionTotalCost;
	//	sprintf(buffer, "%s|%10.5f|\n", buffer, cost);
	
	sprintf(buffer, "%s|%10.5f", buffer, Cost);
	
	// number of cases for stocker handling
	if (selectionStockerHandlingTime > 0)
		sprintf(buffer, "%s|%10.5f",buffer, p->caseMovement);
	else
		sprintf(buffer, "%s|%10.5f", buffer, 0.0);
	
	// number of full pallet selects
	if ( l->handlingMethod == 3 && p->unitOfIssue == 3 )
		sprintf(buffer, "%s|%10.5f\n",buffer, p->palletMovement);
	else
		sprintf(buffer, "%s|%10.5f\n",buffer, 0.0);
	
	
	//	sprintf(buffer, "%s|%s|%s|\n",buffer,p->WMSProdID,p->WMSProdDetID);
	
	if (optimizeType == STRATEGIC_LAYOUT) {
		if (dummy_file != NULL) {
			fprintf(dummy_file, buffer);
			fflush(dummy_file);
		}
		if (debugLog) {
			fprintf(p4TraceFile, "Cost: %f\n", Cost);
			fflush(p4TraceFile);
		}
	}
	
	return Cost;
}

int Pass4Process::FindRTLevCubeSelect(int ProfID, int Lev, double cube)
{
	int i;
	
	/* ********************************************** */
	/* Scan to find the correct RT and Lev.           */
	/* ********************************************** */
	//printf("Prof %d Lev %d Cube %f",ProfID, Lev,cube);
	
	for(i=0;i<mem_labor_select_count;i++)
		if( (mem_labor_select[i].ProfId == ProfID) &&
			(mem_labor_select[i].RelLev == Lev))
			break;
		
		for(;i<mem_labor_select_count;i++){
			if( (mem_labor_select[i].ProfId != ProfID) ||
				(mem_labor_select[i].RelLev != Lev)) {
				return i-1;
			}
			if(cube < mem_labor_select[i].Cube) {
				return i-1;
			}
		}
		
		return mem_labor_select_count-1;
}

int Pass4Process::FindRTLevCubeStocker(int ProfID, int Lev, double cube)
{
	int i;
	
	/* ********************************************** */
	/* Scan to find the correct RT and Lev.           */
	/* ********************************************** */
	//printf("Prof %d Lev %d Cube %f",ProfID, Lev,cube);
	
	for(i=0;i<mem_labor_stocker_count;i++)
		if( (mem_labor_stocker[i].ProfId == ProfID) &&
			(mem_labor_stocker[i].RelLev == Lev))
			break;
		if ( i == mem_labor_stocker_count )
			return -1;
		
		for(;i<mem_labor_stocker_count;i++){
			if( (mem_labor_stocker[i].ProfId != ProfID) ||
				(mem_labor_stocker[i].RelLev != Lev)) {
				return i-1;
			}
			if(cube < mem_labor_stocker[i].Cube) {
				return i-1;
			}
		}
		
		return mem_labor_stocker_count-1;
}

int Pass4Process::GetSectionIdx(int idx)
{
	int i;
	for(i=0;i<mem_section_count;i++)
		if(mem_sections[i].SecID == idx)
			return i;
		return 0;
}

double Pass4Process::GetCost(int pi, int li, int casesInPick, char * buffer)
{
	ssaLaborProd p;
	ssaLaborLoc  l;
	int i;
	double c1;
	
	/* ******************************************** */
	/* Populate two structures with info, and then  */
	/* call the calc routine.                       */
	/* ******************************************** */
	
	if (mem_locs[li].VarWidth==1) {
		// Use the temporary unique Variable Width description
		// we created for the new Locs.
		strcpy(l.desc, vwLoc[vw_loc_count-1].locDesc);
	} else {
		strcpy(l.desc, mem_locs[li].Loc_desc);
	}
	//strcpy(l.desc, mem_locs[li].Loc_desc);
	
	l.x = mem_locs[li].Loc_x;
	l.y = mem_locs[li].Loc_y;
	l.z = mem_locs[li].Loc_z;
	l.w = mem_locs[li].Loc_w;
	l.d = mem_locs[li].Loc_d;
	l.h = mem_locs[li].Loc_h;
	l.clearance = mem_locs[li].clearance;
	l.BayProfID = mem_locs[li].BayProfID;
	// brd - use level type instead of bay type
	l.BayType = mem_locs[li].levelType;
	
	l.level = mem_locs[li].RelLev;
	l.fork_fixed_insertion = mem_locs[li].LevTime;
	i = GetSectionIdx(mem_locs[li].SecID);
	l.section_idx = i;
	l.avg_replen_dist = mem_sections[i].AvgReplenDist;
	l.fork_dist_var = mem_sections[i].ForkVar;
	l.fork_dist_fxd = mem_sections[i].ForkFxd;
	l.fork_rate = mem_sections[i].ForkRate;
	l.sel_dist_var = mem_sections[i].SelVar;
	l.sel_dist_fxd = mem_sections[i].SelFxd;
	l.sel_rate = mem_sections[i].SelRate;
	l.fork_hot_x = mem_sections[i].ForkHotX;
	l.fork_hot_y = mem_sections[i].ForkHotY;
	l.fork_hot_z = mem_sections[i].ForkHotZ;
	l.sel_hot_x = mem_sections[i].SelHotX;
	l.sel_hot_y = mem_sections[i].SelHotY;
	l.sel_hot_z = mem_sections[i].SelHotZ;
	l.TotalMovement = mem_sections[i].TotalMovement;
	
	l.numPutsPerTrip = mem_sections[i].numPutsPerTrip;
	l.pickForkTrav = mem_sections[i].pickForkTrav;
	l.insertForkTrav = mem_sections[i].insertForkTrav;
	l.totalExtendedCube = mem_sections[i].totalExtendedCube;
	l.handlingMethod = mem_locs[li].handlingMethod;
	l.stockerRate = mem_sections[i].stockerRate;
	l.forkFixedInsertion = mem_locs[li].forkFixedInsertion;
	l.forkFixedExtraction = mem_locs[li].forkFixedExtraction;
	
	/* ************************************************** */
	/* The locations above are real, because that is what */
	/* we are sorting.  However, we want to compare them  */
	/* as if they had the exact same product in them. Set */
	/* up a dummy product to compare with them both.      */
	/* NOTE: Bay Profile comes from each location.        */
	/* ************************************************** */
	strcpy(p.desc, mem_prods[pi].desc);
	p.cube = mem_prods[pi].cube;
	p.NumInPallet = mem_prods[pi].NumInPallet;
	p.movement = mem_prods[pi].movement;
	p.weight = mem_prods[pi].weight;
	p.bayProfID = mem_prods[pi].bayProfID;
	p.caseMovement = mem_prods[pi].caseMovement;
	p.palletMovement = mem_prods[pi].palletMovement;
	p.caseCube = mem_prods[pi].caseCube;
	p.unitOfIssue = mem_prods[pi].unitOfIssue;
	p.productTi = mem_prods[pi].productTi;
	p.productHi = mem_prods[pi].productHi;
	
	// brd
	p.caseWeight = mem_prods[pi].caseWeight;
	strcpy(p.WMSProdID,mem_prods[pi].WMSProdID);
	strcpy(p.WMSProdDetID,mem_prods[pi].WMSProdDetID);
	
	if (optimizeType != STRATEGIC_LAYOUT)
		c1 = CalcLabor(&p, &l, casesInPick, buffer );
	else
		c1 = 0;
	
	return c1;
}

int Pass4Process::GetFacings(int li, int prodIndex, int *casesFit, int *found_open_loc, 	
							 vector<int> &facingIndexList)
{
	/* ************************************************** */
	/* This function will start at the current loc idx    */
	/* and scan through the loc list for a level that has */
	/* the appropriate number of facings.  It will return */
	/* the start position of the location that will have  */
	/* the most available facings.                        */
	/* ************************************************** */
	
	int facecount=mem_prods[prodIndex].facings, i, j, p;
	int origFaceCount = facecount;
	int availFacingCount;
	int count = 0;
	int tempCasesFit = 0;
	p4ProdPack tempProd;
	int assignedProd = 0;
	int iteration, numIterations;
	int starti;
	
	
	map<int, int, less<int> >::iterator locFacingIterator;
	
	for (locFacingIterator = locFacingMap.begin(); locFacingIterator != locFacingMap.end(); locFacingIterator++)
		(*locFacingIterator).second = 0;
	
	
	int maxFacings = -1;
	
	for (i=prodIndex; i < mem_prod_count; ++i) {
		if (mem_prods[i].dbID != mem_prods[prodIndex].dbID)
			break;
		if (mem_prods[i].facings > maxFacings)
			maxFacings = mem_prods[i].facings;
	}
	
	*casesFit = 0;	// Tally total cases of Product
	
	
	starti = li;
	p = -1;
	// we will loop so each possible ranking will be divided in half each time
	numIterations = (log((double)maxFacings)/log(2.0))+1;
	if (numIterations < 1)
		numIterations = 1;
	
	if (mem_prods[prodIndex].trace)
		fprintf(p4TraceFile, "Max facings: %d, number of iterations: %d\n", maxFacings, numIterations);
	
	// start by assuming no locations found
	*found_open_loc = 0;
	
	for (iteration=0; iteration < numIterations; ++iteration) {
		
		if (debugLog || mem_prods[prodIndex].trace)
			fprintf(p4TraceFile, "Iteration: %d\n", iteration);
		
		count = 0;
		p = -1;
		
		for(i = starti; i < mem_loc_count; i++) {		// only finding the first facing here
			
			// clear out the list of facings
			facingIndexList.erase(facingIndexList.begin(), facingIndexList.end());
			
			count=0;
			
			if ( mem_locs[i].Assigned == 1 )
				continue;
			
			if (iteration > 0) {
				availFacingCount = locFacingMap[i];
				if (availFacingCount <= 0) {
					
					if (mem_prods[prodIndex].trace) {
						fprintf(p4TraceFile, "Location %s already checked and failed test.\n", 
							mem_locs[i].Loc_desc);
					}
					
					continue;
				}
			}
			
			if (mem_prods[prodIndex].trace)
				fprintf(p4TraceFile, "Found a possible location: %s\n", mem_locs[i].Loc_desc);
			
			
			// we've found a location; 1 means no bay profile
			// set it now so that will be the error message if nothing else happens
			// but only if we haven't already set it to a higher value which would
			// imply a more specific error
			if (*found_open_loc < 1)
				*found_open_loc = 1;
			
			p = NextProdIndex(prodIndex, i);
			
			if (p < 0) {
				
				if (mem_prods[prodIndex].trace)
					fprintf(p4TraceFile, "Location does not match product rankings.\n");
				
				// Rack type doesn't match
				if (ignoreRankings > 0) {
					if (mem_prods[prodIndex].trace)
						fprintf(p4TraceFile, "Ignore rankings flag is set so using location.\n");
					
					// Since the location isn't one of the rankings, find the highest ranked
					// ranking that matches the location level type.  Use that number of
					// facings; if none match, use one facing
					int x;
					for (x=prodIndex; x < prodIndex + numAvailRanks; ++x) {
						if (mem_prods[x].levelType == mem_locs[i].levelType)
							break;
					}
					
					if (x < prodIndex + numAvailRanks) {
						p = x;
						facecount = (int)(  (double)mem_prods[p].facings / pow(2, iteration) );
						
						if (mem_prods[prodIndex].trace) {
							fprintf(p4TraceFile, "Location type matches ranking: %d.  Original facings: %d, "
								"Adjusted facings: %d\n",
								mem_prods[p].ranking, mem_prods[p].facings, facecount);
						}
					}
					else {
						p = prodIndex;
						facecount = mem_prods[prodIndex].facings;
						if (mem_prods[prodIndex].trace) {
							fprintf(p4TraceFile, "Location type does not match rankings.  Using rank 1 facings.");
						}
					}
					
					
					
					// we've found an appropriate profile
					// set the value to 2 to indicate no fit
					// if it isn't already a higher number
					if (*found_open_loc < 2)
						*found_open_loc = 2;
					
				}
				else
					continue;
			}
			else {
				// we've found an appropriate profile
				// set the value to 2 to indicate no fit
				// if it isn't already a higher number
				if (*found_open_loc < 2)
					*found_open_loc = 2;
				
				if (ignoreRankings == 1) {
					if (mem_prods[prodIndex].trace)
						fprintf(p4TraceFile, "Ignore rankings pass - product matches ranking so skipping.\n");
					continue;
				}
				
				facecount = (int)(  (double)mem_prods[p].facings / pow(2, iteration) );
				if (mem_prods[prodIndex].trace) {
					fprintf(p4TraceFile, "Location matches product ranking %d.  Original facings: %d, "
						"Adjusted facings: %d\n",
						mem_prods[p].ranking, mem_prods[p].facings, facecount);
				}
			}
			
			
			if ( facecount <= 0 || (iteration > 0 && facecount > availFacingCount) ) {
				if (mem_prods[prodIndex].trace)
					fprintf(p4TraceFile, "Not enough facings of location.\n");
				continue;
			}
			
			ProdCopy(&tempProd, &mem_prods[p]);
			tempProd.facings = facecount;
			
			*casesFit = MaxCaseFit(&tempProd, &mem_locs[i]);
			if (*casesFit <= 0) {
				count = CheckOverlap(p, i);
				if (count == 0) {
					if (debugLog || mem_prods[prodIndex].trace)
						fprintf(p4TraceFile, "Product will not fit in location: %s\n", mem_locs[i].Loc_desc);
					continue;
				}
			}
			else {
				count = 1;
			}
			
			if (mem_prods[prodIndex].trace)
				fprintf(p4TraceFile, "%d cases fit\n", *casesFit);
			
			// no we know we've at least found one that fits
			// the next-most specific problem is weight
			if (*found_open_loc < 3)
				*found_open_loc = 3;
			
			// If the Location is Variable Width or we've met our facing requirement, all the facings fit
			// on the single Bay Level, so return its index unless we fail the weight test
			
			facingIndexList.push_back(i);
			
			if (mem_locs[i].VarWidth == 1 || count == facecount) {
				
				if (! CheckWeight(p, facingIndexList, 1) ) {
					if (debugLog || mem_prods[prodIndex].trace)
						fprintf(p4TraceFile, "Weight check failed for location: %s\n", mem_locs[i].Loc_desc);
					
					continue;
				}
				
				if (mem_prods[prodIndex].trace)
					fprintf(p4TraceFile, "Passed weight test.\n");
				
				if (utilization < utilMinimum) {
					// set it to indicate we didn't meet the utilization
					// this will be ignored since eventually we will
					// be comparing against 0 and in that case the case fit
					// message would be more appropriate
					*found_open_loc = 999;
					if (debugLog || mem_prods[prodIndex].trace) {
						fprintf(p4TraceFile, "Utilization check failed for location: %s.  Utilization = %.02f\n", 
							mem_locs[i].Loc_desc, utilization);
					}
					
					continue;
				}
				
				// The previous code seemed to imply that we don't update the facing
				// count for variable width so I don't do it here.
				// We do update it for non-vw in case we have decremented the number
				// of facings
				mem_prods[p].rotatedHeight = tempProd.rotatedHeight;
				mem_prods[p].rotatedWidth = tempProd.rotatedWidth;
				mem_prods[p].rotatedLength = tempProd.rotatedLength;
				
				if (mem_locs[i].VarWidth != 1) {
					mem_prods[p].expandedFacings = facecount;
					//mem_prods[p].facings = facecount;
				}
				else
					mem_prods[p].expandedFacings = 1;
				
				if (mem_prods[prodIndex].trace)
					fprintf(p4TraceFile, "Found needed facings on location %s.\n", mem_locs[i].Loc_desc);
				
				//if (mem_prods[p].facings > 1)
				//	printf("--> Returning less than desired facings. %d\n", mem_prods[p].facings - 1);
				
				//if (mem_prods[prodIndex].facings > 1)
				//	printf("--> Returning less than rank 1 facings. %d\n", mem_prods[prodIndex].facings - 1);
				return i;
				
			} 
			else if ( debugLog || mem_prods[prodIndex].trace) {
				fprintf(p4TraceFile, "Looking for more facings:  %d  Location %s.\n",
					facecount, mem_locs[i].Loc_desc);
			}
			
			/////////////////////////////////////////////////////////////////////////
			// Try to lock in the rest of the required facings.
			// Bay Profile, Bay and Relative Level must match.  Exception- Locations'
			// Racking may be set not to break on Bay change.  This allows multiple
			// Facings to go into Drive-ins and Floor Locations where each Location
			// is also a Bay.
			/////////////////////////////////////////////////////////////////////////
			int lastFacingIdx = i;
			int maxTries = 250;
			int returnedToSide = 0;
			
			for(j = i+1;j < mem_loc_count; j++) {
				
				if (mem_prods[prodIndex].trace)
					fprintf(p4TraceFile, "Trying location %s\n", mem_locs[j].Loc_desc);
				
				maxTries--;
				if (maxTries <= 0) {
					if (mem_prods[prodIndex].trace)
						fprintf(p4TraceFile, "\tSearch limit reached.  Unable to find more adjacent facings.\n");
					break;
				}
				
				/* this was bogus because we can leave the aisle and come back without missing the
				next adjacent facing
				if (mem_locs[j].Aisle_dbid != mem_locs[i].Aisle_dbid) {
				if (mem_prods[prodIndex].trace)
				fprintf(p4TraceFile, "\tDifferent aisle.  Unable to find more adjacent facings.\n");
				
				  // once we leave the aisle there's no hope of find adjacent facings
				  break;
				  }
				*/
				
				
				if (mem_locs[j].Side_dbid != mem_locs[i].Side_dbid) {
					
					if (mem_prods[prodIndex].trace)
						fprintf(p4TraceFile, "\tDifferent side.  Continue looking for adjacent facings.\n");
					// even though we switched sides, we may still go back to the other side and
					// find an adjacent facing
					returnedToSide = 0;
					continue;
				}
				
				// compare bay against the last facing, not the original
				if (mem_locs[j].breakOnBay && mem_locs[j].BayID != mem_locs[lastFacingIdx].BayID) {
					if (mem_prods[prodIndex].trace)
						fprintf(p4TraceFile, "\tDifferent bay and spanning off.  Unable to find more adjacent facings.\n");
					// if we are not allowing adjacent facings outside the same bay then once we
					// leave the bay there is no hope of finding an adjacent facing so break out
					break;
				}
				
				if (mem_locs[j].RelLev != mem_locs[i].RelLev) {
					if (mem_prods[prodIndex].trace)
						fprintf(p4TraceFile, "\tDifferent level.  Continue looking for adjacent facings.\n");
					continue;
				}
				
				if (mem_locs[j].levelType != mem_locs[i].levelType) {
					if (mem_prods[prodIndex].trace)
						fprintf(p4TraceFile, "\tDifferent level type.  Continue looking for adjacent facings.\n");
					continue;
				}
				
				// if we get to here, all we know is that we are on the same side, the same level and in
				// the same bay profile type; we still need to see if the location is adjacent because
				// depending on the sort order, we may jump around
				
				// if the distance between the midpoint of the locations is less the width of the locations
				// combined then there can't be another full location in between these (unless its a real skinny one 
				// in a different bay).  We may miss adjacent locations where the location space is greater
				// than the location width but that's a chance I'm willing to take
				
				// compare distance against the last facing, not the original
				double locDist = CalculateDistance(mem_locs[j].Loc_x, mem_locs[j].Loc_y,
					mem_locs[lastFacingIdx].Loc_x, mem_locs[lastFacingIdx].Loc_y);
				
				if (locDist > (mem_locs[j].Loc_w + mem_locs[lastFacingIdx].Loc_w)) {
					if (mem_prods[prodIndex].trace) {
						fprintf(p4TraceFile, "\tLocations are not adjacent.  Distance: %.02f, Width: %.02f, %.02f\n"
							"Continue looking for adjacent facings.\n",
							locDist, mem_locs[j].Loc_w, mem_locs[lastFacingIdx].Loc_w);
					}
					continue;
				}
				
				
				// We must have an adjacent facing
				
				// Location must be available or we break out
				if (mem_locs[j].Assigned != 0) {
					if (mem_prods[prodIndex].trace)
						fprintf(p4TraceFile, "\tLocation already assigned.  Unable to find more facings.\n");
					break;
				}
				
				if (mem_prods[prodIndex].trace)
					fprintf(p4TraceFile, "Found a potential location: %s\n", mem_locs[j].Loc_desc);
				
				// reset the count for the next try
				maxTries = 250;
				ProdCopy(&tempProd, &mem_prods[p]);
				tempProd.facings = facecount;	// we might have lowered the number
				tempCasesFit = MaxCaseFit(&tempProd, &mem_locs[j]);
				
				if (mem_prods[prodIndex].trace)
					fprintf(p4TraceFile, "%d cases fit\n", tempCasesFit);
				
				if ( tempCasesFit > 0 ) {
					*casesFit += tempCasesFit;
					count++;
				} 
				else {
					int tempcount = CheckOverlap(p, j);
					if (tempcount == 0) {
						if (mem_prods[prodIndex].trace)
							fprintf(p4TraceFile, "Product will not fit.\n");
						
						break;
					}
					else
						count += tempcount;
				}
				
				facingIndexList.push_back(j);
				lastFacingIdx = j;
				
				if (count == facecount) {
					
					if ( debugLog || mem_prods[prodIndex].trace)
						fprintf(p4TraceFile, "Found desired number of facings: %5d\n",count);
					;
					(*found_open_loc) = 3;
					if ( CheckWeight(p, facingIndexList, facecount) ) {
						
						if (mem_prods[prodIndex].trace)
							fprintf(p4TraceFile, "Passed weight test\n");
						
						if (utilization < utilMinimum) {
							(*found_open_loc) = 999;
							if (mem_prods[prodIndex].trace)
								fprintf(p4TraceFile, "Failed utilization test.  Utilization: %.02f\n", utilization);
							break;
						}
						else {
							mem_prods[p].rotatedHeight = tempProd.rotatedHeight;
							mem_prods[p].rotatedWidth = tempProd.rotatedWidth;
							mem_prods[p].rotatedLength = tempProd.rotatedLength;
							
							//if (mem_prods[p].facings > facecount)
							//	printf("--> Returning less than desired facings. %d\n",
							//	mem_prods[p].facings - facecount);
							
							
							//if (mem_prods[prodIndex].facings > facecount)
							//	printf("--> Returning less than rank 1 facings. %d\n", mem_prods[prodIndex].facings - facecount);
							
							//mem_prods[p].facings = facecount;
							mem_prods[p].expandedFacings = facecount;
							if (mem_prods[prodIndex].trace)
								fprintf(p4TraceFile, "Found enough facings.  Returning location %s\n", mem_locs[i].Loc_desc);
							
							return i;
						}
					}
					else {
						if (debugLog || mem_prods[prodIndex].trace)
							fprintf(p4TraceFile, "Additional facing failed weight test.  Last loc: %s\n", mem_locs[j].Loc_desc);
						break;
					}
				}
			}	// end of for extra facings
			
			// save the actual number of facings that we did find so we can go right to it
			// in the next loop
			if (iteration == 0)
				locFacingMap[i] = (j-i);
			
			if (debugLog || mem_prods[prodIndex].trace)
				fprintf(p4TraceFile, "Could not find enough facings.  Needed %d; first location: %s\n", facecount, mem_locs[i].Loc_desc);
			
		}	// end of for each loc
		
	}	// end of while numtimes
	
	//if (mem_prods[prodIndex].facings > facecount)
	//	printf("--> Returning less than rank 1 facings (0). %d\n", mem_prods[prodIndex].facings);
	
	return -1;
}

void Pass4Process::SetSortKey(p4Location *inf1)
{
	ssaLaborProd p;
	ssaLaborLoc  l1;
	int i;
	double c1;
	char buffer[COST_BUFFER_SIZE];
	
	memset(buffer,0,COST_BUFFER_SIZE);
	
	/* ******************************************** */
	/* We order the locations in terms of their     */
	/* relative labor costs.  This is the same      */
	/* calculation as we use when determining the   */
	/* actual labor costs, it just has a dummy      */
	/* product sent into it.                        */
	/* ******************************************** */
	
	
	/* ******************************************** */
	/* Populate two structures with info, and then  */
	/* call the calc routine.                       */
	/* ******************************************** */
	strcpy(l1.desc, inf1->Loc_desc);
	l1.x = inf1->Loc_x;
	l1.y = inf1->Loc_y;
	l1.z = inf1->Loc_z;
	l1.w = inf1->Loc_w;
	l1.d = inf1->Loc_d;
	l1.h = inf1->Loc_h;
	l1.clearance = inf1->clearance;
	
	l1.level = inf1->RelLev;
	l1.fork_fixed_insertion = inf1->LevTime;
	i = GetSectionIdx(inf1->SecID);
	l1.section_idx = i;
	l1.avg_replen_dist = mem_sections[i].AvgReplenDist;
	l1.fork_dist_var = mem_sections[i].ForkVar;
	l1.fork_dist_fxd = mem_sections[i].ForkFxd;
	l1.fork_rate = mem_sections[i].ForkRate;
	l1.sel_dist_var = mem_sections[i].SelVar;
	l1.sel_dist_fxd = mem_sections[i].SelFxd;
	l1.sel_rate = mem_sections[i].SelRate;
	l1.fork_hot_x = mem_sections[i].ForkHotX;
	l1.fork_hot_y = mem_sections[i].ForkHotY;
	l1.fork_hot_z = mem_sections[i].ForkHotZ;
	l1.sel_hot_x = mem_sections[i].SelHotX;
	l1.sel_hot_y = mem_sections[i].SelHotY;
	l1.sel_hot_z = mem_sections[i].SelHotZ;
	l1.TotalMovement = mem_sections[i].TotalMovement;
	
	l1.numPutsPerTrip = mem_sections[i].numPutsPerTrip;
	l1.pickForkTrav = mem_sections[i].pickForkTrav;
	l1.insertForkTrav = mem_sections[i].insertForkTrav;
	l1.totalExtendedCube = mem_sections[i].totalExtendedCube;
	l1.handlingMethod = inf1->handlingMethod;
	l1.stockerRate = mem_sections[i].stockerRate;
	l1.forkFixedInsertion = inf1->forkFixedInsertion;
	l1.forkFixedExtraction = inf1->forkFixedExtraction;
	
	//brd
	l1.BayType = inf1->BayType;
	
	/* ************************************************** */
	/* The locations above are real, because that is what */
	/* we are sorting.  However, we want to compare them  */
	/* as if they had the exact same product in them. Set */
	/* up a dummy product to compare with them both.      */
	/* NOTE: Bay Profile comes from each location.        */
	/* ************************************************** */
	
	//////////////////////////////////////////////////////////////////////
	// the "dummy" product is the average of weight, dimensions, etc. based
	// on the products that have been passed for this product group.  This
	// way we can get a close approximation of the correct Golden Zoning
	// of the locations
	//////////////////////////////////////////////////////////////////////
	
	sprintf(p.desc, "dummyprod");
	// added 8/19/99 - UOI changes
	p.cube = avgLength * avgWidth * avgHeight / mem_sections[i].CubeConversion;
	//p.cube = 1;
	p.NumInPallet = (int)avgNumInPallet;
	p.unitOfIssue = dominantUOI;
	
	//added 8/19/99 - UOI changes
	p.movement = avgMovement;
	
	//p.movement = 1;
	//added 8/19/99 - UOI changes
	p.weight = avgWeight;
	p.caseCube = avgCaseCube / mem_sections[i].CubeConversion;
	p.caseMovement = avgCaseMovement;
	p.palletMovement = avgPalletMovement;
	
	//p.weight = 1;
	
	p.bayProfID = inf1->BayProfID;
	l1.BayProfID = inf1->BayProfID;
	strcpy(p.WMSProdID,"");
	strcpy(p.WMSProdDetID,"");
	
	c1 = CalcLabor(&p, &l1, -1, buffer);
	
	inf1->SortKey = c1;
}

void Pass4Process::CopyProd(p4ProdPack *destP, p4ProdPack *sourceP) {
	destP->dbID = sourceP->dbID;
	strcpy(destP->desc,sourceP->desc);
	destP->height = sourceP->height;
	destP->width = sourceP->width;
	destP->length = sourceP->length;
	destP->movement = sourceP->movement;
	destP->facings = sourceP->facings;
	destP->bayProfID = sourceP->bayProfID;
	destP->NumInPallet = sourceP->NumInPallet;
	destP->weight = sourceP->weight;
	destP->cube = sourceP->cube;
	destP->expandedFacings = sourceP->expandedFacings;
	destP->rotateXAxis = sourceP->rotateXAxis;
	destP->rotateYAxis = sourceP->rotateYAxis;
	destP->rotateZAxis = sourceP->rotateZAxis;
	destP->ChangedInPass = sourceP->ChangedInPass;
	
}

// Record new location in Variable Width Bay Level
int Pass4Process::UpdateVarWidthBay(int locIndex, int prodIndex)
{
	void *tmp;
	int widthUsed=0;
	
	if(vw_loc_max == 0){
		vw_loc_max = LOCATION_MEM_BLOCK;
		if ( debugLog ) 
			fprintf(p4TraceFile,"Allocating varWidtLoc\n");
		vwLoc = (varWidthLoc *)malloc(vw_loc_max * sizeof(varWidthLoc));
		if(vwLoc == NULL)
			throw EngineException("Error allocating memory for Variable Width Locations",
			__FILE__, __LINE__, 200);
		
		memset(vwLoc,0,vw_loc_max*sizeof(varWidthLoc));
	}
	
	if(vw_loc_max <= vw_loc_count){
		vw_loc_max += LOCATION_MEM_BLOCK;
		if ( debugLog ) 
			fprintf(p4TraceFile,"Reallocating varWidtLoc\n");
		tmp = realloc(vwLoc, (vw_loc_max * sizeof(varWidthLoc)));
		if(tmp == NULL)
			throw EngineException("Error resizing Variable Width Location array",
			__FILE__, __LINE__, 200);
		vwLoc = (varWidthLoc *)tmp;
	}
	
	vwLoc[vw_loc_count].locIndex = locIndex;
	
	vwLoc[vw_loc_count].order = ++mem_locs[locIndex].vwLocCount;
	
	sprintf(vwLoc[vw_loc_count].locDesc, "%s-VWPos-%d",	mem_locs[locIndex].Loc_desc,
		vwLoc[vw_loc_count].order);
	
	// Subtract the Product width from the Bay Level width.
	// If remaining space is too small, include it in the current
	// new Location.
	widthUsed = VariableWidthUsed(&mem_prods[prodIndex], &mem_locs[locIndex]);
	if (mem_locs[locIndex].Loc_w - (mem_locs[locIndex].prodWidth + widthUsed) <
		mem_locs[locIndex].minLocWidth)
	{
		widthUsed = (int)mem_locs[locIndex].Loc_w - mem_locs[locIndex].prodWidth;
		mem_locs[locIndex].Assigned = TRUE;
		mem_locs[locIndex].prodWidth = (int)mem_locs[locIndex].Loc_w;
		if ( debugLog ) 
			fprintf(p4TraceFile,"Location %s has %d width left\n",mem_locs[locIndex].Loc_desc,(int)(mem_locs[locIndex].Loc_w-mem_locs[locIndex].prodWidth));
	} else {
		mem_locs[locIndex].prodWidth += widthUsed;
		if ( debugLog ) 
			fprintf(p4TraceFile,"Location %s has %d width left\n",mem_locs[locIndex].Loc_desc,(int)(mem_locs[locIndex].Loc_w-mem_locs[locIndex].prodWidth));
	}
	
	vwLoc[vw_loc_count].width = widthUsed;
	vwLoc[vw_loc_count].levelWidth = (int)mem_locs[locIndex].Loc_w;
	vwLoc[vw_loc_count].levelX = mem_locs[locIndex].Loc_x;
	vwLoc[vw_loc_count].levelY = mem_locs[locIndex].Loc_y;
	
	// This adds a row to a list of Aisle DBIDs which will need to be
	// redone to account for its new VW Locations (unless the row has
	// already been added for this Location's Aisle).
	LogVarWidthAisle(locIndex);
	
	vw_loc_count++;
	
	return widthUsed;
}

// Remember Aisle IDs that will need new Pick Paths
void Pass4Process::LogVarWidthAisle(int locIndex)
{
	void *tmp;
	int i;
	
	// Look for existing row and exit if found.
	for (i=0;i<vw_aisle_count;i++) {
		if (mem_locs[locIndex].Aisle_dbid == vwAisle[i]) return;
	}
	
	if(vw_aisle_max == 0){
		vw_aisle_max = AISLE_MEM_BLOCK;
		if ( debugLog ) 
			fprintf(p4TraceFile,"Allocating vwAisle [integer array]\n");
		vwAisle = (int *)malloc(vw_aisle_max * sizeof(int));
		if(vwAisle == NULL)
			throw EngineException("Error allocating memory for Variable Width Aisles",
			__FILE__, __LINE__, 200);
		
		memset(vwAisle,0,vw_aisle_max * sizeof(int));
	}
	
	if(vw_aisle_max <= vw_aisle_count){
		vw_aisle_max += AISLE_MEM_BLOCK;
		if ( debugLog ) 
			fprintf(p4TraceFile,"Reallocating vwAisle [integer array]\n");
		tmp = realloc(vwAisle, (vw_aisle_max * sizeof(int)));
		if(tmp == NULL)
			throw EngineException("Error resizing Variable Width Aisle array",
			__FILE__, __LINE__, 200);
		vwAisle = (int *)tmp;
	}
	
	vwAisle[vw_aisle_count] = mem_locs[locIndex].Aisle_dbid;
	
	vw_aisle_count++;
	
	return;
}

// Calculate number of cases fit using existing dimensions
int Pass4Process::NoRotation(p4ProdPack *p, p4Location *l)
{
	
	double hCount, wCount, lCount;
	int cases=0;
	int widthNeeded=0;
	
	// Trap divide by zero.
	if (p->width == 0 || p->length == 0 || p->height == 0)
		return 0;
	
	// Number of Product Heights that will fit in Location's Height
	// Except:  Case Flow always fits Case dimensions
	if (l->levelType == BAY_TYPE_FLOW) {
		hCount = (double)((int)( (l->Loc_h-l->clearance) / p->caseHeight));
	} else {
		hCount = (double)((int)( (l->Loc_h-l->clearance) / p->height));
	}
	
	/////////////////////////////////////////////////////////////////////////////
	// In Flow locations, you can't stack, so the count is always <= 1.
	/////////////////////////////////////////////////////////////////////////////
	if (l->levelType == BAY_TYPE_FLOW || l->levelType == BAY_TYPE_PALLET_FLOW) {
		if (hCount > 0) 
			hCount = 1.0;
		else 
			hCount = 0.0;
	}
	
	// Number of Product Widths that will fit in Location's Width
	if (l->VarWidth==1) {
		
		// Calculate width needed to hold Product Facings,
		// including Gaps and Snap increments.
		widthNeeded = VariableWidthUsed(p, l);
		
		// If it fits, the number of cases = Facings.
		// Otherwise, no cases fit, so return 0.
		if (widthNeeded <= (l->Loc_w - l->prodWidth)) {
			wCount = (double)p->facings;
		} else {
			return 0;
		}
	} else {
		// Simple fit
		// Except:  Case Flow always fits Case dimensions
		if (l->levelType == BAY_TYPE_FLOW) {
			wCount = (double)((int)(l->Loc_w / p->caseWidth));
		} else {
			wCount = (double)((int)(l->Loc_w / p->width));
		}
	}
	
	// Number of Product Lengths that will fit in Location's Depth (Length)
	// Except:  Case Flow always fits Case dimensions
	if (l->levelType == BAY_TYPE_FLOW) {
		lCount = (double)((int)(l->Loc_d / p->caseLength));
	} else {
		lCount = (double)((int)(l->Loc_d / p->length));
	}
	
	// Total cases that will fit.
	cases = (int)(hCount * wCount * lCount);
	
	
	//	if (l->BayType == BAY_TYPE_FLOW) {	// return count in eaches even though we fit cases
	//		cases = cases * (int)p->casePack;
	//	}
	
	double prodCube, locCube;
	
	if (l->levelType == BAY_TYPE_FLOW) 
		prodCube = (hCount * p->caseHeight)*(wCount*p->caseWidth)*(lCount*p->caseLength);
	else
		prodCube = (hCount * p->height)*(wCount*p->width)*(lCount*p->length);
	
	locCube = (l->Loc_w*l->Loc_d*(l->Loc_h - l->clearance));
	
	return cases;
	
}

// Check for optimum case orientation - Only X axis free
int Pass4Process::RotateXAxis(p4ProdPack *p, p4Location *l)
{
	
	double hCount, wCount, lCount;
	int t1, t2;
	int widthNeeded=0;
	
	// Total cases that will fit using existing Dimensions.
	t1 = NoRotation(p, l);
	
	// Number of Product Lengths that will fit in Location's Height
	// Except:  Case Flow always fits Case dimensions
	if (l->levelType == BAY_TYPE_FLOW) {
		hCount = (double)((int)((l->Loc_h - l->clearance) / p->caseLength));
	} else {
		hCount = (double)((int)((l->Loc_h - l->clearance) / p->length));
	}
	
	/////////////////////////////////////////////////////////////////////////////
	// In Flow locations, you can't stack, so the count is always <= 1.
	/////////////////////////////////////////////////////////////////////////////
	if (l->levelType == BAY_TYPE_FLOW || l->levelType == BAY_TYPE_PALLET_FLOW) {
		if (hCount > 0) hCount = 1.0;
		else hCount = 0.0;
	}
	
	// Number of Product Widths that will fit in Location's Width.
	if (l->VarWidth==1) {
		
		// Calculate width needed to hold Product Facings,
		// including Gaps and Snap increments.
		widthNeeded = VariableWidthUsed(p, l);
		// If it fits, the number of cases = Facings.
		// Otherwise, no cases fit, so return 0.
		if (widthNeeded <= (l->Loc_w - l->prodWidth)) {
			wCount = (double)(p->facings);
		} 
		else {
			return 0;
		}
	} else {
		// Simple fit
		// Except:  Case Flow always fits Case dimensions
		if (l->levelType == BAY_TYPE_FLOW) {
			wCount = (double)((int)(l->Loc_w / p->caseWidth));
		} else {
			wCount = (double)((int)(l->Loc_w / p->width));
		}
	}
	
	// Number of Product Heights that will fit in Location's Depth (Length)
	// Except:  Case Flow always fits Case dimensions
	if (l->levelType == BAY_TYPE_FLOW) {
		lCount = (double)((int)(l->Loc_d / p->caseHeight));
	} else {
		lCount = (double)((int)(l->Loc_d / p->height));
	}
	
	// Total for rotated X axis.
	t2 = (int)(hCount * wCount * lCount);
	
	if (t1 >= t2) {
		// Existing orientation is best
		return t1;
	} else {
		// T2 wins.  Swap Height and Length (rotate around X-axis)
		if (l->levelType == BAY_TYPE_FLOW) {
			p->rotatedLength = p->caseHeight;
			p->rotatedHeight = p->caseLength;
		} else {
			p->rotatedLength = p->height;
			p->rotatedHeight = p->length;
		}
		p->ChangedInPass = TRUE;
		
		return t2;
	}
	
}

// Check for optimum case orientation - Only Y axis free
int Pass4Process::RotateYAxis(p4ProdPack *p, p4Location *l)
{
	
	double hCount, wCount, lCount;
	int t1, t2;
	int widthNeeded=0;
	p4ProdPack *p1 = (p4ProdPack*) malloc(sizeof(p4ProdPack));
	if (p1 == NULL) {
		throw EngineException("Error Allocating memory for temp p4ProdPack",
			__FILE__, __LINE__, 300);
	}
	memset(p1,0,sizeof(p4ProdPack));
	
	// Total cases that will fit using existing Dimensions.
	t1 = NoRotation(p, l);
	
	// Number of Product Widths that will fit in Location's Height.
	// Except:  Case Flow always fits Case dimensions
	if (l->levelType == BAY_TYPE_FLOW) {
		hCount = (double)((int)((l->Loc_h - l->clearance) / p->caseWidth));
	} 
	else {
		hCount = (double)((int)((l->Loc_h - l->clearance) / p->width));
	}
	
	/////////////////////////////////////////////////////////////////////////////
	// In Flow locations, you can't stack, so the count is always <= 1.
	/////////////////////////////////////////////////////////////////////////////
	if (l->levelType == BAY_TYPE_FLOW || l->levelType == BAY_TYPE_PALLET_FLOW) {
		if (hCount > 0) hCount = 1.0;
		else hCount = 0.0;
	}
	
	// Number of Product Heights that will fit in Location's Width.
	if (l->VarWidth==1) {
		
		// Calculate width needed to hold Product Facings,
		// incl. Gaps and Snap increments, using current Height.
		// Except:  Case Flow always fits Case dimensions
		ProdCopy(p1, p);
		if (l->levelType == BAY_TYPE_FLOW) {
			p1->width = p->caseHeight;
		} else {
			p1->width = p->height;
		}
		widthNeeded = VariableWidthUsed(p1, l);
		
		// If it fits, the number of cases = Facings.
		// Otherwise, no cases fit, so return 0.
		if (widthNeeded <= (l->Loc_w - l->prodWidth)) {
			wCount = (double)(p->facings);
		} else {
			free(p1);
			return 0;
		}
	} else {
		// Simple fit.
		// Except:  Case Flow always fits Case dimensions
		if (l->levelType == BAY_TYPE_FLOW) {
			wCount = (double)((int)(l->Loc_w / p->caseHeight));
		} else {
			wCount = (double)((int)(l->Loc_w / p->height));
		}
	}
	
	// Number of Product Lengths that will fit in Location's Depth (Length).
	// Except:  Case Flow always fits Case dimensions
	if (l->levelType == BAY_TYPE_FLOW) {
		lCount = (double)((int)(l->Loc_d / p->caseLength));
	} else {
		lCount = (double)((int)(l->Loc_d / p->length));
	}
	
	// Total for rotated Y axis.
	t2 = (int)(hCount * wCount * lCount);
	
	if (t1 >= t2) {
		// Existing orientation is best
		free(p1);
		return t1;
	} 
	else {
		// T2 wins.  Swap Height and Width (rotate around Y-axis)
		if (l->levelType == BAY_TYPE_FLOW) {
			p->rotatedHeight = p->caseWidth;
			p->rotatedWidth = p->caseHeight;
		} else {
			p->rotatedHeight = p->width;
			p->rotatedWidth = p->height;
		}
		p->ChangedInPass = TRUE;
		
		free(p1);
		
		return t2;
	}
	
}

// Check for optimum case orientation - Only Z axis free
int Pass4Process::RotateZAxis(p4ProdPack *p, p4Location *l)
{
	
	double hCount, wCount, lCount;
	int t1, t2;
	int widthNeeded=0;
	p4ProdPack *p1 = (p4ProdPack*) malloc(sizeof(p4ProdPack));
	if (p1 == NULL) {
		throw EngineException("Error Allocating memory for temp p4ProdPack",
			__FILE__, __LINE__, 300);
	}
	memset(p1,0,sizeof(p4ProdPack));
	
	// Total cases that will fit using existing Dimensions.
	t1 = NoRotation(p, l);
	
	// Number of Product Heights that will fit in Location's Height.
	// Except:  Case Flow always fits Case dimensions
	if (l->levelType == BAY_TYPE_FLOW) {
		hCount = (double)((int)((l->Loc_h - l->clearance) / p->caseHeight));
	} else {
		hCount = (double)((int)((l->Loc_h - l->clearance) / p->height));
	}
	
	/////////////////////////////////////////////////////////////////////////////
	// In Flow locations, you can't stack, so the count is always <= 1.
	/////////////////////////////////////////////////////////////////////////////
	if (l->levelType == BAY_TYPE_FLOW || l->levelType == BAY_TYPE_PALLET_FLOW) {
		if (hCount > 0) hCount = 1.0;
		else hCount = 0.0;
	}
	
	// Number of Product Lengths that will fit in Location's Width.
	if (l->VarWidth==1) {
		
		// Calculate width needed to hold Product Facings,
		// incl. Gaps and Snap increments, using current Length.
		// Except:  Case Flow always fits Case dimensions
		ProdCopy(p1, p);
		if (l->levelType == BAY_TYPE_FLOW) {
			p1->width = p->caseLength;
		} else {
			p1->width = p->length;
		}
		widthNeeded = VariableWidthUsed(p1, l);
		
		// If it fits, the number of cases = Facings.
		// Otherwise, no cases fit, so return 0.
		if (widthNeeded <= (l->Loc_w - l->prodWidth)) {
			wCount = (double)(p->facings);
		} else {
			free(p1);
			return 0;
		}
	} else {
		// Simple fit.
		// Except:  Case Flow always fits Case dimensions
		if (l->levelType == BAY_TYPE_FLOW) {
			wCount = (double)((int)(l->Loc_w / p->caseLength));
		} else {
			wCount = (double)((int)(l->Loc_w / p->length));
		}
	}
	
	// Number of Product Widths that will fit in Location's Depth (Length).
	// Except:  Case Flow always fits Case dimensions
	if (l->levelType == BAY_TYPE_FLOW) {
		lCount = (double)((int)(l->Loc_d / p->caseWidth));
	} else {
		lCount = (double)((int)(l->Loc_d / p->width));
	}
	
	// Total for rotated Z axis.
	t2 = (int)(hCount * wCount * lCount);
	
	if (t1 >= t2) {
		// Existing orientation is best
		free(p1);
		return t1;
	} else {
		// T2 wins.  Swap Width and Length (rotate around Z-axis)
		if (l->levelType == BAY_TYPE_FLOW) {
			p->rotatedLength = p->caseWidth;
			p->rotatedWidth = p->caseLength;
		} else {
			p->rotatedLength = p->width;
			p->rotatedWidth = p->length;
		}
		p->ChangedInPass = TRUE;
		
		free(p1);
		return t2;
	}
	
}

// Check for optimum case orientation - All axes free
int Pass4Process::RotateAnyAxis(p4ProdPack *p, p4Location *l)
{
	
	double hCount, wCount, lCount;
	int t1, t2, t3, t4, t5, t6;
	int widthNeeded=0;
	p4ProdPack *p1 = (p4ProdPack *)malloc(sizeof(p4ProdPack));
	
	if (p1 == NULL) {
		throw EngineException("Error Allocating memory for temp p4ProdPack",
			__FILE__, __LINE__, 300);
	}
	memset(p1,0,sizeof(p4ProdPack));
	
	// Dummy product for VW width testing.
	ProdCopy(p1, p);
	
	
	////////////////////////////////////////////////////////////////////////
	// Orientation #1
	////////////////////////////////////////////////////////////////////////
	
	// Total cases that will fit using existing Dimensions.
	t1 = NoRotation(p, l);
	
	
	////////////////////////////////////////////////////////////////////////
	// Orientation #2
	////////////////////////////////////////////////////////////////////////
	
	// It should be easier to use the existing function
	t2 = RotateZAxis(p,l);
	
	
	////////////////////////////////////////////////////////////////////////
	// Orientation #3
	////////////////////////////////////////////////////////////////////////
	
	// It should be easier to use the existing function
	t3 = RotateYAxis(p,l);
	
	
	////////////////////////////////////////////////////////////////////////
	// Orientation #4
	////////////////////////////////////////////////////////////////////////
	
	// Number of Product Widths that will fit in Location's Height.
	// Except:  Case Flow always fits Case dimensions
	if (l->levelType == BAY_TYPE_FLOW) {
		hCount = (double)((int)((l->Loc_h - l->clearance) / p->caseWidth));
	} else {
		hCount = (double)((int)((l->Loc_h - l->clearance) / p->width));
	}
	
	/////////////////////////////////////////////////////////////////////////////
	// In Flow locations, you can't stack, so the count is always <= 1.
	/////////////////////////////////////////////////////////////////////////////
	if (l->levelType == BAY_TYPE_FLOW || l->levelType == BAY_TYPE_PALLET_FLOW) {
		if (hCount > 0) 
			hCount = 1.0;
		else 
			hCount = 0.0;
	}
	
	// Number of Product Lengths that will fit in Location's Width.
	if (l->VarWidth==1) {
		
		// Calculate width needed to hold Product Facings,
		// incl. Gaps and Snap increments, using current Length.
		// Except:  Case Flow always fits Case dimensions
		if (l->levelType == BAY_TYPE_FLOW) {
			p1->width = p->caseLength;
		} 
		else {
			p1->width = p->length;
		}
		widthNeeded = VariableWidthUsed(p1, l);
		
		// If it fits, the number of cases = Facings.
		if (widthNeeded <= (l->Loc_w - l->prodWidth)) {
			wCount = (double)(p->facings);
		} 
		else {
			wCount = 0;
		}
	} 
	else {
		// Simple fit.
		// Except:  Case Flow always fits Case dimensions
		if (l->levelType == BAY_TYPE_FLOW) {
			wCount = (double)((int)(l->Loc_w / p->caseLength));
		} else {
			wCount = (double)((int)(l->Loc_w / p->length));
		}
	}
	
	// Number of Product Heights that will fit in Location's Depth (Length).
	// Except:  Case Flow always fits Case dimensions
	if (l->levelType == BAY_TYPE_FLOW) {
		lCount = (double)((int)(l->Loc_d / p->caseHeight));
	} 
	else {
		lCount = (double)((int)(l->Loc_d / p->height));
	}
	
	// Tot/al for orientation #4.
	t4 = (int)(hCount * wCount * lCount);
	
	
	////////////////////////////////////////////////////////////////////////
	// Orientation #5
	////////////////////////////////////////////////////////////////////////
	
	// Number of Product Lengths that will fit in Location's Height.
	// Except:  Case Flow always fits Case dimensions
	if (l->levelType == BAY_TYPE_FLOW) {
		hCount = (double)((int)((l->Loc_h - l->clearance) / p->caseLength));
	} 
	else {
		hCount = (double)((int)((l->Loc_h - l->clearance) / p->length));
	}
	
	/////////////////////////////////////////////////////////////////////////////
	// In Flow locations, you can't stack, so the count is always <= 1.
	/////////////////////////////////////////////////////////////////////////////
	if (l->levelType == BAY_TYPE_FLOW || l->levelType == BAY_TYPE_PALLET_FLOW) {
		if (hCount > 0) 
			hCount = 1.0;
		else 
			hCount = 0.0;
	}
	
	// Number of Product Heights that will fit in Location's Width.
	if (l->VarWidth==1) {
		
		// Calculate width needed to hold Product Facings,
		// incl. Gaps and Snap increments, using current Height.
		// Except:  Case Flow always fits Case dimensions
		if (l->levelType == BAY_TYPE_FLOW) {
			p1->width = p->caseHeight;
		} 
		else {
			p1->width = p->height;
		}
		widthNeeded = VariableWidthUsed(p1, l);
		
		// If it fits, the number of cases = Facings.
		if (widthNeeded <= (l->Loc_w - l->prodWidth)) {
			wCount = (double)(p->facings);
		} 
		else {
			wCount = 0;
		}
	} 
	else {
		// Simple fit.
		// Except:  Case Flow always fits Case dimensions
		if (l->levelType == BAY_TYPE_FLOW) {
			wCount = (double)((int)(l->Loc_w / p->caseHeight));
		} else {
			wCount = (double)((int)(l->Loc_w / p->height));
		}
	}
	
	// Number of Product Widths that will fit in Location's Depth (Length).
	// Except:  Case Flow always fits Case dimensions
	if (l->levelType == BAY_TYPE_FLOW) {
		lCount = (double)((int)(l->Loc_d / p->caseWidth));
	} 
	else {
		lCount = (double)((int)(l->Loc_d / p->width));
	}
	
	// Total for orientation #5.
	t5 = (int)(hCount * wCount * lCount);
	
	
	////////////////////////////////////////////////////////////////////////
	// Orientation #6
	////////////////////////////////////////////////////////////////////////
	
	// It should be simpler to use the existing function
	t6 = RotateXAxis(p,l);
	
	if ( t1 >= t2 && t1 >= t3 && t1 >= t4 && t1 >= t6 ) {
		// Existing orientation is best
		free(p1);
		return t1;
	}
	else if ( t2 >= t1 && t2 >= t3 && t2 >= t4 && t2 >= t5 && t2 >= t6 ) {
		// T2 wins.  Set W=D, D=W
		if (l->levelType == BAY_TYPE_FLOW) {
			p->rotatedWidth = p->caseLength;
			p->rotatedLength = p->caseWidth;
		} 
		else {
			p->rotatedWidth = p->length;
			p->rotatedLength = p->width;
		}
		p->ChangedInPass = TRUE;
		
		free(p1);
		
		return t2;
	}
	else if ( t3 >= t1 && t3 >= t2 && t3 >= t4 && t3 >= t5 && t3 >= t6 ) {
		// T3 wins.  Set H=W, W=H
		if (l->levelType == BAY_TYPE_FLOW) {
			p->rotatedHeight = p->caseWidth;
			p->rotatedWidth = p->caseHeight;
		} 
		else {
			p->rotatedHeight = p->width;
			p->rotatedWidth = p->height;
		}
		p->ChangedInPass = TRUE;
		
		
		free(p1);
		return t3;
	}
	else if ( t4 >= t1 && t4 >= t2 && t4 >= t3 && t4 >= t5 && t4 >= t6 ) {
		// T4 wins.  Set H=W, W=D, D=H
		if (l->levelType == BAY_TYPE_FLOW) {
			p->rotatedHeight = p->caseWidth;
			p->rotatedWidth = p->caseLength;
			p->rotatedLength = p->caseHeight;
		} 
		else {
			p->rotatedHeight = p->width;
			p->rotatedWidth = p->length;
			p->rotatedLength = p->height;
		}
		p->ChangedInPass = TRUE;
		
		free(p1);
		return t4;
	}
	else if ( t5 >= t1 && t5 >= t2 && t5 >= t3 && t5 >= t4 && t5 >= t6 ) {
		// T5 wins.  Set H=D, W=H, D=W
		if (l->levelType == BAY_TYPE_FLOW) {
			p->rotatedHeight = p->caseLength;
			p->rotatedWidth = p->caseHeight;
			p->rotatedLength = p->caseWidth;
		} 
		else {
			p->rotatedHeight = p->length;
			p->rotatedWidth = p->height;
			p->rotatedLength = p->width;
		}
		p->ChangedInPass = TRUE;
		
		free(p1);
		return t5;
	}
	else {
		// T6 wins.  Set D=H, H=D
		if (l->levelType == BAY_TYPE_FLOW) {
			p->rotatedLength = p->caseHeight;
			p->rotatedHeight = p->caseLength;
		} else {
			p->rotatedLength = p->height;
			p->rotatedHeight = p->length;
		}
		p->ChangedInPass = TRUE;
		
		free(p1);
		return t6;
	}
}

// Calculate width Product requires in Variable-Width Bay Level.
// Rotated dimensions have been substituted into p->width before
// calling this method.
int Pass4Process::VariableWidthUsed(p4ProdPack *p, p4Location *l)
{
	double width=0.0, facingWidth;
	int facingCount=p->facings, intWidth=0;
	
	// Use reoriented Width if appropriate
	if (p->rotatedWidth > 0.0) {
		facingWidth = p->rotatedWidth;
	} else {
		// For Case Flow only, always use Case dimension. 
		// Otherwise the dimension is already for appropriate UOI.
		if (l->levelType==BAY_TYPE_FLOW) {
			facingWidth = p->caseWidth;
		} else {
			facingWidth = p->width;
		}
	}
	
	// Add first Product gap (between current Product and side of
	// Location or previous Product).
	width += l->prodGap;
	
	// For each Product Facing...
	while (1) {
		
		// Add the width of the Facing
		width += facingWidth;
		
		// If this is the last Facing, leave loop
		facingCount--;
		if (facingCount<=0) break;
		
		// Add the next Facing Gap and increment to the next Facing
		// snap value if set.
		width = NextSnap(width, l->facingGap, l->facingSnap);
	}
	
	// Increment to next Product snap value if set.  Pass in '0' for
	// Product Gap because that had to be added in already above.
	width += l->prodGap;
	
	width = NextSnap(width, 0, l->prodSnap);
	
	// Round to next integer value.
	intWidth = (int)width;
	if ((width - (double)intWidth) > 0.0) intWidth++;
	
	// Increase to the user's Minimum Location Width setting if necessary
	if (intWidth < l->minLocWidth) intWidth = l->minLocWidth;
	
	if ( debugLog ) 
		fprintf(p4TraceFile,"VarWidthUsed() returning %5d width\n",intWidth);
	
	return intWidth;
	
}

// Increment current width value to next snap level
double Pass4Process::NextSnap(double width, int gap, int snapIncr)
{
	int per;
	double rmndr;
	
	// Add next spacing
	width += (double)gap;
	
	// If the Snap Increment is set to '0', do nothing more.
	if (snapIncr==0) return width;
	
	// Calc number of snaps less than 'width'
	per = (int)(width / snapIncr);
	
	// Is 'width' exactly at a snap level?
	rmndr = width - (per * snapIncr);
	
	// If not, increment to next snap level.
	if (rmndr > 0.0) return (double)((per + 1) * snapIncr);
	
	// Otherwise leave alone
	return width;
}

void Pass4Process::MakeProdsUniform(void) {
	int idxStart =0;
	int idxEnd = 0;
	int idxMid = 0;
	int whichSwap = 0;
	
	p4ProdPack * tempProd = (p4ProdPack *)malloc(sizeof(p4ProdPack)*numAvailRanks);
	
	
	idxStart = 0;
	idxEnd = mem_prod_count - numAvailRanks;
	
	idxMid = (mem_prod_count / 3) * 2;
	for (;idxMid < mem_prod_count && mem_prods[idxMid].ranking != 1;idxMid++) ;
	
	if (idxMid >= mem_prod_count)
		idxMid = mem_prod_count-numAvailRanks;
	
	for (; idxStart < idxMid; idxStart+=numAvailRanks) {
		switch (whichSwap) {
		case 0 : 
			{
				whichSwap = 1;
				break;
			}
		case 1 : 
			{
				memcpy(tempProd, &(mem_prods[idxStart]), sizeof(p4ProdPack)*numAvailRanks);
				memcpy(&(mem_prods[idxStart]), &(mem_prods[idxMid]), sizeof(p4ProdPack)*numAvailRanks);
				memcpy(&(mem_prods[idxMid]), tempProd, sizeof(p4ProdPack)*numAvailRanks);
				//printf("Swap - %d %d %d\n",tempProd[0].ranking,tempProd[1].ranking,tempProd[2].ranking);
				idxMid-=numAvailRanks;
				whichSwap = 2;
				break;
			}
		case 2 : 
			{
				memcpy(tempProd, &(mem_prods[idxStart]), sizeof(p4ProdPack)*numAvailRanks);
				memcpy(&(mem_prods[idxStart]), &(mem_prods[idxEnd]), sizeof(p4ProdPack)*numAvailRanks);
				memcpy(&(mem_prods[idxEnd]), tempProd, sizeof(p4ProdPack)*numAvailRanks);
				//printf("Swap - %d %d %d\n",tempProd[0].ranking,tempProd[1].ranking,tempProd[2].ranking);
				idxEnd-=numAvailRanks;
				whichSwap = 0;
				break;
			}
		}
	}
	free(tempProd);
	return;
}


int LevelLaborSort( const void *p1, const void *p2 ) {
	ssaLaborLevel *inf1, *inf2;
	
	inf1 = (ssaLaborLevel *) p1;
	inf2 = (ssaLaborLevel *) p2;
	
	if ( inf1->ProfId < inf2->ProfId )
		return -1;
	else if ( inf1->ProfId > inf2->ProfId )
		return 1;
	else {
		if ( inf1->RelLev < inf2->RelLev )
			return -1;
		else if (inf1->RelLev > inf2->RelLev )
			return 1;
		else {
			if (inf1->Cube < inf2->Cube )
				return -1;
			else if ( inf1->Cube > inf2->Cube )
				return 1;
			else
				return 0;
		}
	}
	return 0;
}



int Pass4Process::GroupRunComp()
{
	
	int assigned=0, badDimensions=0;
	int curProd=-1;
	int numberUnassigned = -1;
	int prevNumberUnassigned = mem_prod_count;
	int firstFacingIdx;
	int prodIdx, prodDBID, prevProdDBID;
	
	
	while (numberUnassigned != prevNumberUnassigned) {
		
		prevNumberUnassigned = numberUnassigned;
		prevProdDBID = -1;
		
		for(prodIdx=0;prodIdx<mem_prod_count;prodIdx++){
			
			prodDBID = mem_prods[prodIdx].dbID;
			
			if (prodDBID == prevProdDBID)
				continue;
			else
				prevProdDBID = prodDBID;
			
			// product could have already been re-assigned via a swap
			if (mem_prods[prodIdx].newLocIdx >= 0)
				continue;
			
			// Skip assigned products during new product layout
			if (optimizeType == NEW_PRODUCT_LAYOUT && mem_prods[prodIdx].prevLocIdx >= 0)
				continue;
			
			// In tactical, skip those that are already in their best ranking
			if (optimizeType == TACTICAL_LAYOUT && mem_prods[prodIdx].currentAssignedRanking == 1)
				continue;
			
			if (mem_prods[prodIdx].dbID == curProd) {
				if (assigned==1 || badDimensions==1)
					continue;
			} 
			else {
				curProd = mem_prods[prodIdx].dbID;
				badDimensions = 0;
			}
			
			if (mem_prods[prodIdx].width <= 0 || 
				mem_prods[prodIdx].length <= 0 || 
				mem_prods[prodIdx].height <= 0) { 
				badDimensions=1;
				continue;
			}
			
			assigned = 0;
			
			if (debugLog) {
				fprintf(p4TraceFile, "Before GetFacings\n");
				fflush(p4TraceFile);
			}
			
			if (optimizeType == GROUP_LAYOUT)
				firstFacingIdx = GroupGetFacings(0, prodIdx);
			else
				// New product layout will be handled by this method also
				firstFacingIdx = TacticalGetFacings(0, prodIdx);
			
			if (debugLog) {
				fprintf(p4TraceFile, "After GetFacings\n");
				fflush(p4TraceFile);
			}
			assigned = 1;		 // to prevent going through the same product multiple times.
			if(firstFacingIdx < 0) continue;			// no facings found
			
			// we now have the first location index and the number of facings
			// that we found
			// now we need to assign the locations
			
			if (GroupAssignProducts(prodIdx, firstFacingIdx) == 0)
				numberUnassigned--;
			if (debugLog)
				fflush(p4TraceFile);
		} 
		
		// temporary for demo
		prevNumberUnassigned = numberUnassigned;
	}
	
	
	return 0;
	
}


int Pass4Process::GroupAssignProducts(int fromProdIdx, int toLocIdx)
{
	
	int fromProdTotalCases = 0;
	int fromCases, toCases;
	int toProdTotalCases = 0;
	double fromProdCost = 0.0f;
	double toProdCost = 0.0f;
	char fromProdCostBuf[COST_BUFFER_SIZE];
	char toProdCostBuf[COST_BUFFER_SIZE];
	int fromProdRankIdx = -1;
	int toProdRankIdx = -1;
	int toProdIdx = -1;
	int fromLocIdx = -1;
	int firstOccupiedFacing = -1;
	int facingNumber;
	int bayIdx, levelIdx;
	int singleToProdCaseCount = -1;
	int toProdFacingCount;
	double fromCost, toCost, moveCost;
	
	// get the ranking of the product begin placed
	fromProdRankIdx = NextProdIndex(fromProdIdx, toLocIdx);
	if (fromProdRankIdx < 0)
		return -1;		// shouldn't happen
	
	// get the number of facings needed by the product being placed
	// this was set in the getFacings method when we determined the
	// maximum number of facings needed and available
	int facingCount = mem_prods[fromProdRankIdx].expandedFacings;
	
	// the current location of the product being placed
	fromLocIdx = mem_prods[fromProdIdx].prevLocIdx;
	// get the product being displaced; there may not be one if all
	// of the to locations are open
	// the first facing should contain a product if there is one 
	// being displaced; otherwise assume all the facings are open
	toProdIdx = mem_locs[toLocIdx].prevProdIdx;
	if (toProdIdx > 0) {
		// get the actual ranking of the displaced product that will be put in the from location
		toProdRankIdx = NextProdIndex(toProdIdx, fromLocIdx);
		//if (toProdRankIdx < 0)
		// error - we should have already verified that the displaced product
		// can go in the from location
		mem_prods[toProdRankIdx].expandedFacings = 0;
	}
	
	/*	this was the original way I got the to product but
	I now realize that I don't have to do this because
	we will never have a case where the first facing is empty
	but a subsequent one has a product
	for (facingNumber=0; facingNumber < facingCount; ++facingNumber) {
	if (mem_locs[toLocIdx+facingNumber].prevProdIdx >= 0) {
	
	  toProdIdx = mem_locs[toLocIdx+facingNumber].prevProdIdx;
	  if (toProdIdx >= 0) {
	  toProdRankIdx = NextProdIndex(toProdIdx, toLocIdx+facingNumber);
	  mem_prods[toProdRankIdx].expandedFacings = 0;
	  
		break;
		}
		}
		}
	*/
	
	// calculate the total cases for the from product
	for (facingNumber=0; facingNumber < facingCount; facingNumber++) {
		
		// increment the case count of the product being placed
		fromProdTotalCases += MaxCaseFit(&mem_prods[fromProdRankIdx], &mem_locs[toLocIdx+facingNumber]);		
	}
	
	
	
	
	// see how many facings we can allocate to the displaced product
	toProdFacingCount = 0;
	
	if (toProdRankIdx >= 0) {
		for (facingNumber=0; facingNumber < mem_prods[toProdRankIdx].facings; facingNumber++) {
			
			// get the next facing currently occupied by the product being placed
			// need to add some logic to allow the displaced product to go
			// into open locations adjacent to the from product
			if (GetExistingFacing(fromProdIdx, facingNumber) < 0)
				break;	// there are no more facings allocated to the product being placed
			
			toProdFacingCount++;
			
			// no need to keep going on after we've reached the desired number of facings
			if (toProdFacingCount >= mem_prods[toProdRankIdx].facings)
				break;
			
		}
		
		
		// calculate the total case count of the displaced product
		toProdTotalCases = MaxCaseFit(&mem_prods[toProdRankIdx], &mem_locs[fromLocIdx]) *
			toProdFacingCount;
		
	}
	
	
	// calculate the cost to assign the product being placed
	// calculate the cost as if the entire case count was put in the first facing
	fromProdCost = GetCost(fromProdRankIdx, toLocIdx, fromProdTotalCases, fromProdCostBuf);
	if(fromProdCost < 0.00001f)
		fromProdCost = 0.0f; 
	
	
	if (toProdIdx >= 0) {
		// calculate the cost to assign the product being displaced
		fromLocIdx = GetExistingFacing(fromProdIdx, 0);
		toProdCost = GetCost(toProdRankIdx, fromLocIdx, toProdTotalCases, toProdCostBuf);
		if(toProdCost < 0.00001f)
			toProdCost = 0.0f;
		
		// set the expanded facings to 0; we will increment it as we go
		mem_prods[toProdRankIdx].expandedFacings = 0;
		
	}
	
	// assume that after we loaded the products, we verified that the number of facings 
	// the product says it has are valid adjacent facings - just in case the WMS sends 
	// over some bogus ones
	// we've already verified that the displaced product can fit in each of the facings so
	// now we just swap
	
	for (facingNumber=0; facingNumber < mem_prods[fromProdRankIdx].expandedFacings; facingNumber++) {
		
		// only put a cost and case count on the first (primary) facing
		if (facingNumber > 0) {
			toProdCost = 0.0f;
			toCases = 0;
			fromProdCost = 0.0f;
			fromCases = 0;
		}
		else {
			fromCases = fromProdTotalCases;
			toCases = toProdTotalCases;
		}
		
		// assign the from product to the facing
		if (debugLog) {
			fprintf(p4TraceFile, "Assigning product: %d to location: %d  Case: %d   Cost: %5.2f\n",
				mem_prods[fromProdRankIdx].dbID, mem_locs[toLocIdx+facingNumber].Loc_dbid,
				fromCases, fromProdCost);
		}
		AddResult(fromProdRankIdx, toLocIdx+facingNumber, fromProdCost, fromCases, fromProdCostBuf);
		// only need to set the product's new location once
		if (facingNumber == 0) {
			mem_prods[fromProdIdx].newLocIdx = toLocIdx+facingNumber;	
			mem_prods[fromProdRankIdx].newLocIdx = toLocIdx+facingNumber;	
		}
		mem_locs[toLocIdx+facingNumber].newProdIdx = fromProdIdx;
		
		// add the weight to the to bay and level
		bayIdx = FindBay(mem_locs[toLocIdx+facingNumber].BayID);
		if (bayIdx >= 0) {
			mem_bay_weights[bayIdx].currentBayWeight += 
				(fromProdTotalCases/mem_prods[fromProdRankIdx].expandedFacings)
				* mem_prods[fromProdIdx].caseWeight;
		}
		
		levelIdx = FindLevel(mem_locs[toLocIdx+facingNumber].Level_dbid);
		if (levelIdx >= 0) {
			mem_level_weights[levelIdx].currentLevelWeight += 
				(fromProdTotalCases/mem_prods[fromProdRankIdx].expandedFacings)
				* mem_prods[fromProdIdx].caseWeight;
		}
		
		
		// remove the products weight from it's previous locations
		if (facingNumber == 0)
			DecrementWeights(fromProdIdx);
		
		// see if there was a displaced product
		if (toProdIdx < 0)
			continue;
		
		// see if we need any more facings for the displaced product
		if (mem_prods[toProdRankIdx].expandedFacings >= toProdFacingCount)
			continue;
		
		// there is a product being displaced
		// find the next facing of the from product and assign it
		fromLocIdx = GetExistingFacing(fromProdIdx, facingNumber);
		if (fromLocIdx < 0)
			continue;
		
		// check the weight on the from bay and level
		// in GroupGetFacings, we checked to make sure that at least
		// one facing would meet the weight restriction; here we have to
		// check the rest;  as soon as one doesn't, we reset the facing count
		// so that it will not try to assign any more
		bayIdx = FindBay(mem_locs[fromLocIdx].BayID);
		if (bayIdx >= 0) {
			if (mem_bay_weights[bayIdx].currentBayWeight + 
				(toProdTotalCases/toProdFacingCount)*mem_prods[toProdIdx].caseWeight
				> mem_bay_weights[bayIdx].bayMaxWeight) {
				toProdFacingCount = mem_prods[toProdRankIdx].expandedFacings;
				continue;
			}
		}
		
		levelIdx = FindLevel(mem_locs[fromLocIdx].Level_dbid);
		if (levelIdx >= 0) {
			if (mem_level_weights[levelIdx].currentLevelWeight + 
				(toProdTotalCases/toProdFacingCount)*mem_prods[toProdIdx].caseWeight
				> mem_level_weights[levelIdx].levelMaxWeight) {
				toProdFacingCount = mem_prods[toProdRankIdx].expandedFacings;
				continue;
			}
		}
		
		
		// assign the displaced product to the facing
		if (debugLog) {
			fprintf(p4TraceFile, "Assigning product: %d to location: %d  Case: %d   Cost: %5.2f\n",
				mem_prods[toProdRankIdx].dbID, mem_locs[fromLocIdx].Loc_dbid,
				toCases, toProdCost);
		}
		AddResult(toProdRankIdx, fromLocIdx, toProdCost, toCases, toProdCostBuf);
		// only need to set the product's new location once                                           
		if (mem_prods[toProdRankIdx].expandedFacings == 0) {
			mem_prods[toProdIdx].newLocIdx = fromLocIdx;
			mem_prods[toProdRankIdx].newLocIdx = fromLocIdx;
		}
		
		mem_locs[fromLocIdx].newProdIdx = toProdIdx;
		
		// keep track of how many we actually assign
		mem_prods[toProdRankIdx].expandedFacings++;
		
		// add the weight to the from bay and level
		if (bayIdx >= 0) {
			mem_bay_weights[bayIdx].currentBayWeight += 
				(toProdTotalCases/toProdFacingCount) * mem_prods[toProdIdx].caseWeight;
		}
		
		if (levelIdx >= 0) {
			mem_level_weights[levelIdx].currentLevelWeight += 
				(toProdTotalCases/toProdFacingCount) * mem_prods[toProdIdx].caseWeight;
		}
		
		// Remove the product's weight from it's previous locations
		if (facingNumber == 0)
			DecrementWeights(toProdIdx);
		
		
	}
	
	fromCost = mem_prods[fromProdIdx].fromCost;
	toCost = mem_prods[fromProdIdx].toCost;
	
	if (toProdIdx >= 0) {
		fromCost += mem_prods[toProdIdx].fromCost;
		toCost += mem_prods[toProdIdx].toCost;
	}
	
	moveCost = mem_prods[fromProdIdx].moveCost;
	
	TacticalAddInfo(fromProdRankIdx, toProdRankIdx, fromCost, toCost, moveCost);
	
	return 0;
				
}


void Pass4Process::TacticalAddInfo(int fromProdIdx, int toProdIdx, 
								   double fromCost, double toCost, double moveCost)
{
	void *tmp;
	int locIdx;
	
	if(mem_tacticalinfo_max == 0){
		mem_tacticalinfo_max = RESULT_MEM_BLOCK;
		mem_tactical_info = (p4TacticalInfo *)malloc(mem_tacticalinfo_max * sizeof(p4TacticalInfo));
		if(mem_tactical_info == NULL)
			throw EngineException("Error allocating memory for tactical results",
			__FILE__, __LINE__, 200);
		
		memset(mem_tactical_info,0, mem_tacticalinfo_max * sizeof(p4TacticalInfo));
	}
	
	if(mem_tacticalinfo_max <= mem_tacticalinfo_count){
		mem_tacticalinfo_max += RESULT_MEM_BLOCK;
		tmp = realloc(mem_tactical_info, (mem_tacticalinfo_max * sizeof(p4TacticalInfo)));
		if(tmp == NULL)
			throw EngineException("Error resizing tactical result array",__FILE__, __LINE__, 200);
		mem_tactical_info = (p4TacticalInfo *)tmp;
	}
	
	p4TacticalInfo *p;
	p = &mem_tactical_info[mem_tacticalinfo_count];
	
	// From Product Information
	p->FromProductDBID = mem_prods[fromProdIdx].dbID;
	strcpy(p->FromWMSProductID, mem_prods[fromProdIdx].WMSProdID);
	strcpy(p->FromProductDesc, mem_prods[fromProdIdx].desc);
	
	locIdx = mem_prods[fromProdIdx].prevLocIdx;
	if (locIdx >= 0) {
		strcpy(p->FromProductOrigLoc, mem_locs[locIdx].Loc_desc);
		p->FromProductOrigFacings = mem_prods[fromProdIdx].prevFacingCount;
		p->FromProductOrigBayProfileDBID = mem_locs[locIdx].BayProfID;
	} else {
		strcpy(p->FromProductOrigLoc, " ");
		p->FromProductOrigFacings = 0;
		p->FromProductOrigBayProfileDBID = 0;
	}
	locIdx = mem_prods[fromProdIdx].newLocIdx;
	if (locIdx >= 0) {		// it better be
		strcpy(p->FromProductNewLoc, mem_locs[locIdx].Loc_desc);
		p->FromProductNewFacings = mem_prods[fromProdIdx].facings;
		p->FromProductNewBayProfileDBID = mem_locs[locIdx].BayProfID;
	}
	
	// To Product Information
	if (toProdIdx < 0) {
		p->ToProductDBID = 0;
		strcpy(p->ToWMSProductID, "");
		strcpy(p->ToProductDesc, "");
		strcpy(p->ToProductOrigLoc, "");
		p->ToProductOrigFacings = 0;
		p->ToProductOrigBayProfileDBID = 0;
		strcpy(p->ToProductNewLoc, "");
		p->ToProductNewFacings = 0;
		p->ToProductNewBayProfileDBID = 0;
	}
	else {
		p->ToProductDBID = mem_prods[toProdIdx].dbID;
		strcpy(p->ToWMSProductID, mem_prods[toProdIdx].WMSProdID);
		strcpy(p->ToProductDesc, mem_prods[toProdIdx].desc);
		
		locIdx = mem_prods[toProdIdx].prevLocIdx;
		if (locIdx >= 0) {
			strcpy(p->ToProductOrigLoc, mem_locs[locIdx].Loc_desc);
			p->ToProductOrigFacings = mem_prods[toProdIdx].prevFacingCount;
			p->ToProductOrigBayProfileDBID = mem_locs[locIdx].BayProfID;
		}
		locIdx = mem_prods[toProdIdx].newLocIdx;
		if (locIdx >= 0) {		
			strcpy(p->ToProductNewLoc, mem_locs[locIdx].Loc_desc);
			p->ToProductNewFacings = mem_prods[toProdIdx].facings;
			p->ToProductNewBayProfileDBID = mem_locs[locIdx].BayProfID;
		}
	}
	
	// Don't know why we didn't do this before since we are passing
	// in the ranking idx but the from/to cost is on the original (ranking 1) idx
	p->FromCost = fromCost;
	p->ToCost = toCost;
	p->MoveCost = moveCost;
	
	mem_tacticalinfo_count++;
	
}

void Pass4Process::DecrementWeights(int prodIndex)
{
	int dbid, bayDBID, levelDBID, bayIdx, levelIdx;
	double weight;
	
	dbid = mem_prods[prodIndex].dbID;
	
	for (int i=0; i < mem_solution_count; ++i) {
		if (mem_solutions[i].prodDBID == dbid) {
			weight = mem_solutions[i].caseCount * mem_prods[prodIndex].caseWeight;
			
			bayDBID = mem_solutions[i].bayDBID;
			bayIdx = FindBay(bayDBID);
			if (bayIdx >= 0)
				mem_bay_weights[bayIdx].currentBayWeight -= weight;
			
			levelDBID = mem_solutions[i].levelDBID;
			levelIdx = FindLevel(levelDBID);
			if (levelIdx >= 0)
				mem_level_weights[levelIdx].currentLevelWeight -= weight;
		}
		// solutions are sorted by product id so as soon as we
		// pass our product we can stop looking
		else if (mem_solutions[i].prodDBID > dbid)
			break;
	}
	
	return;
	
	
	
}


int Pass4Process::GroupGetFacings(int li, int prodIndex)
{
	
	int maxFacingCount = GetMaxFacingCount(prodIndex);
	int prodRankIdx;
	int numTimes = 0;
	int fromLocIdx = mem_prods[prodIndex].prevLocIdx;
	int facingsNeeded, fromFacingCount, toProdIdx, toProdRankIdx;
	
	// from product is the product we are trying to slot
	// from location is the current location of the product we are trying to slot
	// to location is the location we are trying to put the from product in
	// to product is the product that is currently assigned to the to location
	
	// Loop until we've tried all facing counts for all rankings
	
	if (mem_prods[prodIndex].dbID == 43737 || mem_prods[prodIndex].dbID == 43738) {
		int x;
		x = 0;
	}
	
	while (numTimes <= maxFacingCount) {
		
		// For each location
		for (int toLocIdx=li; toLocIdx < mem_loc_count; toLocIdx++) {
			
			fromFacingCount = mem_prods[prodIndex].prevFacingCount;
			
			// See if this product will work in this location
			prodRankIdx = CheckProductInLocation(prodIndex, toLocIdx);
			if (prodRankIdx < 0)			// for whatever reason the product can't go to the loc
				continue;					// go to next location
			
			// Decrement facing count each time
			facingsNeeded = mem_prods[prodRankIdx].facings - numTimes;
			if (facingsNeeded <= 0)			// tried all possible facing counts for this ranking
				continue;					// go to next location
			
			// set the expanded facings to the needed facings; if we find them
			// we will leave it; otherwise this will be overwritten the next
			// time around
			mem_prods[prodRankIdx].expandedFacings = facingsNeeded;
			
			// check the weight of the to location against the from product
			if (! CheckWeight(prodRankIdx, toLocIdx, facingsNeeded))
				continue;
			
			// see if another product is currently assigned to location 
			toProdIdx = mem_locs[toLocIdx].prevProdIdx;
			
			if (toProdIdx >= 0) {
				
				// see if the to product has enough facings to handle our product
				// add bay spanning logic later
				if (mem_prods[toProdIdx].prevFacingCount +
					// add in any open locations that are adjacent since we can use those
					FindOpenAdjacentLocations(toLocIdx) < facingsNeeded) {
					continue;	
				}
				
				if (fromLocIdx < 0)
					// the current product does not have an assignment
					// so there's nothing to swap.
					// products without assignments must go to open locations
					continue;
				
				// See if the displaced product will work in the from location
				toProdRankIdx = CheckProductInLocation(toProdIdx, fromLocIdx);
				if (toProdRankIdx <= 0)
					continue;
				
				//check the weight of the from location against the displaced product
				// for now, just try a single facing because we don't know how many
				// facings are available
				if (! CheckWeight(toProdRankIdx, fromLocIdx, 1))
					continue;
				
				// we can return here because we know that the products can be
				// swapped
				return toLocIdx;
				
			}
			
			// Now we know that the product can go in the location
			// and that the to location is open
			
			facingsNeeded--;					// subtract the facing we just found
			if (facingsNeeded == 0) {			// we must have only needed one facing
				return toLocIdx;	// we're done	
			}
			
			
			// Try to find extra facings in adjacent locations
			for (int adjLocIdx=1; adjLocIdx < facingsNeeded; ++adjLocIdx) {
				
				// See if the next location is adjacent to this one
				if (! LocsAreAdjacent(toLocIdx, toLocIdx+adjLocIdx))
					break;			// go to the next location
				
				// See if the product works in the location
				prodRankIdx = CheckProductInLocation(prodIndex, toLocIdx+adjLocIdx);
				if (prodRankIdx < 0)		// product can't go in location
					break;
				
				// see if another product is currently assigned to location 
				toProdIdx = mem_locs[toLocIdx].prevProdIdx;
				if (toProdIdx > 0) {
					// if a product is there and had enough facings we would
					// have found it earlier; so we have to skip it
					break;
					
				}
				
				// we've found another adjacent location
				// that is open
				facingsNeeded--;
				if (facingsNeeded == 0) {
					
					// make sure the weight will fit
					if (! CheckWeight(prodRankIdx, toLocIdx, mem_prods[prodRankIdx].expandedFacings))
						break;
					
					return toLocIdx;
				}
				
			}	// end of extra facings for-loop
			
			
		}	// end of location for-loop
		
		numTimes++;
		
	}	// end of decrement facings while-loop
	
	
	return -1;
	
}


// Find the product ranking that requires the most facings
int Pass4Process::GetMaxFacingCount(int prodIndex)
{
	int maxFacings = -1;
	
	for (int p=prodIndex; p < mem_prod_count; ++p) {
		if (mem_prods[p].dbID != mem_prods[prodIndex].dbID)
			break;
		
		if (mem_prods[p].facings > maxFacings)
			maxFacings = mem_prods[p].facings;
	}
	
	return maxFacings;
	
}


// return the prodIndex of the ranking that will work
int Pass4Process::CheckProductInLocation(int prodIndex, int locIndex)
{
	// See if the location already has a new assignment
	if (mem_locs[locIndex].newProdIdx >= 0)
		return -1;
	
	// Make sure location is in product group footprint
	if (mem_prods[prodIndex].prodGroupDBID 
		!= mem_locs[locIndex].prodGroupDBID)
		return -1;
	
	// Find the product ranking (if any) that matches the location
	int p = NextProdIndex(prodIndex, locIndex);
	if (p < 0)
		return -1;
	
	if (MaxCaseFit(&mem_prods[p], &mem_locs[locIndex]) <= 0) {
		if (debugLog)
			fprintf(p4TraceFile, "Product: %d won't fit in location: %d\n", mem_prods[p].dbID, mem_locs[locIndex].Loc_dbid);
		return -1;
	}
	
	return p;
	
}

int Pass4Process::LocsAreAdjacent(int loc1Idx, int loc2Idx)
{
	if (mem_locs[loc1Idx].BayID != mem_locs[loc2Idx].BayID) {
		// locations must be in the same bay for now
		// later if bay spanning is on, we can use
		// the following code to check for adjacent bays
		// but we need more data before it will work
		// if (mem_locs[loc1Idx].sideID != mem_locs[loc2Idx].SideID)
		//		return 0;
		// distanceBetweenBays = abs(mem_locs[loc1Idx].bayXCoord - mem_locs[loc2Idx].bayXCoord);
		// bayWidth = mem_locs[loc1Idx].bayWidth + mem_locs[loc1Idx].barWidth
		// if (distanceBetweenBays != bayWidth) && levels are in the same product group
		//	return 0;
		return 0;
	}
	
	if (mem_locs[loc1Idx].RelLev != mem_locs[loc2Idx].RelLev)
		return 0;
	
	if (mem_locs[loc1Idx].BayProfID != mem_locs[loc2Idx].BayProfID)
		return 0;
	
	return 1;
	
}


// Given a product, return the nth facing that it currently occupies
int Pass4Process::GetExistingFacing(int prodIndex, int facingNumber)
{
	
	int locIdx, startLocIdx;
	startLocIdx = mem_prods[prodIndex].prevLocIdx;
	locIdx = startLocIdx+facingNumber;
	// See if it really is a facing for this product
	if (mem_locs[locIdx].prevProdIdx != prodIndex)
		return -1;
	
	// See if a product was already assigned to it
	if (mem_locs[locIdx].newProdIdx > 0)
		return -1;
	
	return locIdx;
	
}



int Pass4Process::GetSolutions()
{
	char buffer[BUFSIZE];
	unsigned int  err;
	int i;
	
	if (debugLog)
		fprintf(p4TraceFile, "\n\n------------------- Solution Data -------------------------\n");
	
	/* ********************************************* */
	/* Wait for the start of the Bay stream          */
	/* ********************************************* */
	do {
		memset(buffer, 0, BUFSIZE);
		if ( gfnGetDataStream()->ssData.getline(buffer, BUFSIZE) == 0) {
		
			throw EngineException("Error receiving on stream",
				__FILE__, __LINE__, 200);
		}
		if ( debugLog )
			fprintf(p4TraceFile,"\nReceived data: %s",buffer);
	} while (strncmp(buffer, "P4SOLUTIONS", 11) != 0);
	
	mem_solution_count = atoi(buffer+12);		
	if(mem_solution_count == 0)
		return 0;
	
	/* ********************************************* */
	/* Allocate memory to hold the Locations.        */
	/* ********************************************* */
	if ( debugLog )
		fprintf(p4TraceFile,"\nReceiving %d solutions\n",mem_solution_count);
	
	mem_solutions = (p4Solution *)malloc(mem_solution_count * sizeof(p4Solution));
	if(mem_solutions == NULL){
		throw EngineException("Error Allocating memory for solution list",
			__FILE__, __LINE__, 300);
	}
	
	memset(mem_solutions,0,mem_solution_count * sizeof(p4Solution));
	
	for(i=0;i<mem_solution_count;i++) {
		GetOneSolution(i);	
	}
	
	return mem_solution_count;
	
}


void Pass4Process::GetOneSolution(int idx)
{
	
	char buffer[BUFSIZE];
	unsigned int  err;
	char *ptr;
	
	
	/* ******************************************** */
	/* Wait for the start of the Bay stream         */
	/* ******************************************** */
	do {
		memset(buffer, 0, BUFSIZE);
		if ( gfnGetDataStream()->ssData.getline(buffer, BUFSIZE) == 0) {
		
			throw EngineException("Error receiving on stream",
				__FILE__, __LINE__, 200);
		}
		if ( debugLog )
			fprintf(p4TraceFile,"\nReceived data: %s",buffer);
	} while (strncmp(buffer, "SOLUTION", 8) != 0);
	
	
	ptr = strtok(buffer+9, "|");
	mem_solutions[idx].prodDBID= atoi(ptr);
	ptr = strtok(NULL, "|");
	mem_solutions[idx].locDBID = atoi(ptr);
	ptr = strtok(NULL, "|");
	mem_solutions[idx].isPrimary = atoi(ptr);
	ptr = strtok(NULL, "|");
	mem_solutions[idx].totalCost = (double)atof(ptr);
	ptr = strtok(NULL, "|");
	mem_solutions[idx].caseCount = atoi(ptr);
	ptr = strtok(NULL, "|");
	mem_solutions[idx].unitOfIssue= atoi(ptr);
	ptr = strtok(NULL, "|");
	mem_solutions[idx].weight = (double)atof(ptr);
	ptr = strtok(NULL, "|");
	mem_solutions[idx].casePack = atoi(ptr);
	ptr = strtok(NULL, "|");
	mem_solutions[idx].innerPack = atoi(ptr);
	ptr = strtok(NULL, "|");
	mem_solutions[idx].numberInPallet= atoi(ptr);
	ptr = strtok(NULL, "|");
	mem_solutions[idx].ti = atoi(ptr);
	ptr = strtok(NULL, "|");
	mem_solutions[idx].hi = atoi(ptr);
	ptr = strtok(NULL, "|");
	mem_solutions[idx].levelDBID = atoi(ptr);
	ptr = strtok(NULL, "|");
	mem_solutions[idx].bayDBID= atoi(ptr);
	ptr = strtok(NULL, "|");
	mem_solutions[idx].relativeLevel = atoi(ptr);
	ptr = strtok(NULL, "|");
	mem_solutions[idx].levelMaxWeight = (double)atof(ptr);
	ptr = strtok(NULL, "|");
	mem_solutions[idx].bayMaxWeight = (double)atof(ptr);
	
	return;
	
}

void Pass4Process::ProcessSolutions()
{
	
	int i, prodIdx, locIdx;
	char debugFile[1024];
	debugFile[0]=0;
	strcpy(debugFile, logPath);
	strcat(debugFile, "\\P4ProcessSolutions.out");
	
	FILE *f = fopen(debugFile, "w");
	fprintf(stdout, "Starting process Solutions. %d solutions.\n", mem_solution_count);
	for (i=0; i < mem_solution_count; ++i) {
		
		prodIdx = FindProd(mem_solutions[i].prodDBID);
		locIdx = FindLoc(mem_solutions[i].locDBID);
		
		fprintf(f, "prodIdx = %d.  locIdx = %d.\n", prodIdx, locIdx);
		
		// Product may not be in list if it does not have rankings
		if (prodIdx < 0)
			continue;
		
		// Location should always be in list but just in case
		if (locIdx < 0)
			continue;
		
		fprintf(f, "prodIdx = %d.  locIdx = %d.\n", prodIdx, locIdx);
		
		mem_locs[locIdx].prevProdIdx = prodIdx;
		mem_prods[prodIdx].prevLocIdx = locIdx;
		
		//removed by Dylan 11/14/02 - not using rankings any longer
		
		/*// Assign the lowest cost location in the list to the product
		if (mem_prods[prodIdx].prevLocIdx > locIdx || mem_prods[prodIdx].prevLocIdx < 0) {
		// Assign the location to all the rankings for this product
		for (j=0; j < mem_prod_count; ++j) {
		if (mem_prods[prodIdx].dbID == mem_prods[prodIdx+j].dbID) {
		mem_prods[prodIdx+j].prevLocIdx = locIdx;
		// this will allow us to eliminate products that are already in
		// there best ranking during tactical optimization
		if (mem_prods[prodIdx+j].bayProfID == mem_locs[locIdx].BayProfID &&
		mem_prods[prodIdx+j].levelType == mem_locs[locIdx].levelType)
		mem_prods[prodIdx].currentAssignedRanking = mem_prods[prodIdx+j].ranking;
		}
		else
		break;
		}
	} */
		
		
		
		// in group mode, if product is already in its correct 
		// product group don't move it; 
		if (optimizeType == GROUP_LAYOUT) {
			if (mem_prods[prodIdx].prodGroupDBID == mem_locs[locIdx].prodGroupDBID) {
				mem_locs[locIdx].newProdIdx = prodIdx;
				mem_prods[prodIdx].newLocIdx = locIdx;
			}
		}
		
		// removed by Dylan 11/14/02 - we want to look at these products
		
		/*else if (optimizeType == TACTICAL_LAYOUT) {
		// in tactical mode we should not mess with products that
		// are not assigned to a location in their correct product group
		if (mem_prods[prodIdx].prodGroupDBID != mem_locs[locIdx].prodGroupDBID) {
		mem_locs[locIdx].newProdIdx = prodIdx;
		mem_prods[prodIdx].newLocIdx = locIdx;
		}
		}/*
		
		  /*		This is now done in ProcessPreviousBayWeights
		  // as we process the solutions, add the weight to
		  // the current bay and level
		  bayIdx = FindBay(mem_locs[locIdx].BayID);
		  levelIdx = FindLevel(mem_locs[locIdx].Level_dbid);
		  
			// since the solution always contains the case count
			// we don't have to look at the handling method
			// unless we decide to add in the pallet wood
			mem_bay_weights[bayIdx].currentBayWeight += 
			mem_prods[prodIdx].caseWeight * mem_solutions[i].caseCount;
			mem_level_weights[levelIdx].currentLevelWeight += 
			mem_prods[prodIdx].caseWeight * mem_solutions[i].caseCount;
			
		*/
		
	}
	
	fclose(f);
	
	// Fix any facing counts where facings aren't adjacent
	// in case the WMS sends over bad ones
	//ValidateFacings();
	
}

int Pass4Process::FindBay(int bayDBID)
{
	for (int i=0; i <= bay_weight_counter; ++i) {
		if (mem_bay_weights[i].bayDBID == bayDBID) {
			return i;
		}
	}
	
	return -1;
}


int Pass4Process::FindLevel(int levelDBID)
{
	for (int i=0; i <= level_weight_counter; ++i) {
		if (mem_level_weights[i].levelDBID == levelDBID) {
			return i;
		}
	}
	
	return -1;
}


// we might want to build a hash table to do this
int Pass4Process::FindProd(int prodDBID)
{
	for (int i=0; i < mem_prod_count; ++i) {
		if (mem_prods[i].dbID == prodDBID)
			return i;
	}
	
	return -1;
	
}

// we might want to build a hash table to do this
int Pass4Process::FindLoc(int locDBID)
{
	for (int i=0; i < mem_loc_count; ++i) {
		if (mem_locs[i].Loc_dbid == locDBID)
			return i;
	}
	
	return -1;
	
}

int Pass4Process::FindOpenAdjacentLocations(int locIdx)
{
	
	int locCount = 0;
	
	// first look forward
	int i = locIdx+1;
	while (i < mem_loc_count) {
		// make sure location is adjacent
		if (LocsAreAdjacent(i-1, i) == 1) {
			// if there wasn't a product before and there isn't a new assignment
			// then it must be open
			if (mem_locs[i].prevProdIdx < 0 && mem_locs[i].newProdIdx < 0)
				locCount++;
			else
				break;	// stop at the first occupied adjacent location
		}
		else
			break;		// stop at the first non-adjacent location
		
		i++;
		
	}
	
	// then look backward
	i = locIdx-1;
	while (i >= 0) {
		if (LocsAreAdjacent(i, i+1) == 1) {
			if (mem_locs[i].prevProdIdx < 0 && mem_locs[i].newProdIdx < 0)
				locCount++;
			else
				break;
		}
		else
			break;
		
		i--;
	}
	
	return locCount;
}


// This function will make sure that if a product says it has
// a certain number of facings, they are all adjacent
void Pass4Process::ValidateFacings()
{
	int i, j, facingCount, prevProdDBID, locIdx;
	
	
	prevProdDBID = -1;
	
	for (i=0; i < mem_prod_count; ++i) {
		
		
		if (mem_prods[i].dbID == prevProdDBID)
			continue;
		
		prevProdDBID = mem_prods[i].dbID;
		
		facingCount = 1;
		locIdx = mem_prods[i].prevLocIdx;
		if (locIdx < 0)
			continue;
		
		for (j=1; j < mem_loc_count; ++j) {
			// stop as soon as we come to a location not assigned to this product
			if (mem_locs[locIdx+j].prevProdIdx != i)
				break;
			
			// also stop as soon as we find a non-adjacent location
			if (! LocsAreAdjacent(locIdx+j, locIdx+j-1))
				break;
			
			// otherwise we increment the facing count
			facingCount++;
		}
		
		// replace the supposed facing count with the real adjacent facing count
		// the real count will never be higher than the supposed
		if (facingCount < mem_prods[i].prevFacingCount)
			mem_prods[i].prevFacingCount = facingCount;
		
	}
	
	return;
	
}


bool Pass4Process::CheckWeight(int prodIdx, int locIdx, int facingCount)
{
	int cases, i, bayDBID, levelDBID, prevBayDBID, prevLevelDBID, bayIdx, levelIdx;
	double currentBayWeight, bayMaxWeight;
	double currentLevelWeight, levelMaxWeight;
	double weight, prevWeight;
	int prevProdIdx;
	int prodPrevLevelDBID, prodPrevBayDBID, prodPrevLocIdx;
	
	if (ignoreWeight)
		return TRUE;
	
	prevBayDBID = prevLevelDBID = -1;
	
	
	// since all the facings have to be of the same bay profile,
	// the same number of cases will fit in each location
	// so we only have to calculate it once
	cases = MaxCaseFit(&mem_prods[prodIdx], &mem_locs[locIdx]);
	weight = mem_prods[prodIdx].caseWeight * cases;
	
	prodPrevBayDBID = prodPrevLevelDBID = -1;
	
	prodPrevLocIdx=  mem_prods[prodIdx].prevLocIdx;
	if (prodPrevLocIdx >= 0) {
		prodPrevBayDBID = mem_locs[prodPrevLocIdx].BayID;
		prodPrevLevelDBID = mem_locs[prodPrevLocIdx].Level_dbid;
	}
	
	for (i=0; i < facingCount; ++i) {
		
		bayDBID = mem_locs[locIdx+i].BayID;
		if (prevBayDBID != bayDBID) {
			bayIdx = FindBay(bayDBID);
			currentBayWeight = mem_bay_weights[bayIdx].currentBayWeight;
			bayMaxWeight = mem_bay_weights[bayIdx].bayMaxWeight;
			prevBayDBID = bayDBID;
		}
		
		// need to subtract out the weight of the product being displaced if there is one
		prevWeight = 0;
		prevProdIdx = mem_locs[locIdx+i].prevProdIdx;
		if (prevProdIdx >= 0) {
			prevWeight = MaxCaseFit(&mem_prods[prevProdIdx], &mem_locs[locIdx+i]) 
				* mem_prods[prevProdIdx].caseWeight;
		}
		
		// for now, assume that if the product is already assigned to
		// the bay then it will meet the weight restriction
		// technically, it could be looking for more facings in the same bay
		// so we should somehow take the difference of what it has and
		// what it is looking for
		if (prodPrevBayDBID != bayDBID) {
			if (currentBayWeight + weight - prevWeight> bayMaxWeight) {
				if ( debugLog )
					fprintf(p4TraceFile,"Bay %d is full trying product %s, weight %5.2f.  Bay max: %5.2f\n",
					prevBayDBID, mem_prods[prodIdx].WMSProdID, weight, bayMaxWeight);
				return FALSE;
			}
			else
				currentBayWeight += weight - prevWeight;
		}
		
		levelDBID = mem_locs[locIdx+i].Level_dbid;
		if (prevLevelDBID != levelDBID) {
			levelIdx = FindLevel(levelDBID);
			currentLevelWeight = mem_level_weights[levelIdx].currentLevelWeight;
			levelMaxWeight = mem_level_weights[levelIdx].levelMaxWeight;
			prevLevelDBID = levelDBID;
		}
		
		if (prodPrevLevelDBID != levelDBID) {
			if (currentLevelWeight + weight - prevWeight > levelMaxWeight) {
				if ( debugLog )
					fprintf(p4TraceFile,"Level %d is full trying product %s, weight %5.2f. Level max: %5.2f\n",
					prevLevelDBID, mem_prods[prodIdx].WMSProdID, weight, levelMaxWeight);
				return FALSE;
			}
			else
				currentLevelWeight += weight - prevWeight;
		}
		
	}
	
	return TRUE;
	
}


bool Pass4Process::CheckWeight(int prodIdx, vector<int> facingIndexList, int facingCount)
{
	int cases, i, bayDBID, levelDBID, prevBayDBID, prevLevelDBID, bayIdx, levelIdx;
	double currentBayWeight, bayMaxWeight;
	double currentLevelWeight, levelMaxWeight;
	double weight;
	
	if (ignoreWeight || facingIndexList.size() == 0)
		return TRUE;
	
	prevBayDBID = prevLevelDBID = -1;
	
	
	// since all the facings have to be of the same bay profile,
	// the same number of cases will fit in each location
	// so we only have to calculate it once
	cases = MaxCaseFit(&mem_prods[prodIdx], &mem_locs[facingIndexList[0]]);
	weight = mem_prods[prodIdx].caseWeight * cases;
	
	for (i=0; i < facingIndexList.size(); ++i) {
		int locIdx = facingIndexList[i];
		
		bayDBID = mem_locs[locIdx].BayID;
		if (prevBayDBID != bayDBID) {
			bayIdx = FindBay(bayDBID);
			currentBayWeight = mem_bay_weights[bayIdx].currentBayWeight;
			bayMaxWeight = mem_bay_weights[bayIdx].bayMaxWeight;
			prevBayDBID = bayDBID;
		}
		
		// for now, assume that if the product is already assigned to
		// the bay then it will meet the weight restriction
		// technically, it could be looking for more facings in the same bay
		// so we should somehow take the difference of what it has and
		// what it is looking for
		if (currentBayWeight + weight > bayMaxWeight) {
			if ( debugLog )
				fprintf(p4TraceFile,"Bay %d is full trying product %s, weight %5.2f.  Bay max: %5.2f\n",
				prevBayDBID, mem_prods[prodIdx].WMSProdID, weight, bayMaxWeight);
			return FALSE;
		}
		else
			currentBayWeight += weight;
		
		
		levelDBID = mem_locs[locIdx].Level_dbid;
		if (prevLevelDBID != levelDBID) {
			levelIdx = FindLevel(levelDBID);
			currentLevelWeight = mem_level_weights[levelIdx].currentLevelWeight;
			levelMaxWeight = mem_level_weights[levelIdx].levelMaxWeight;
			prevLevelDBID = levelDBID;
		}
		
		if (currentLevelWeight + weight > levelMaxWeight) {
			if ( debugLog )
				fprintf(p4TraceFile,"Level %d is full trying product %s, weight %5.2f. Level max: %5.2f\n",
				prevLevelDBID, mem_prods[prodIdx].WMSProdID, weight, levelMaxWeight);
			return FALSE;
		}
		else
			currentLevelWeight += weight;
		
		
	}
	
	return TRUE;
	
}

void Pass4Process::ProcessPreviousBayWeights()
{
	int i,bayIdx, levelIdx;
	p4Solution *ss;
	double caseWeight, weight;
	
	mem_bay_weights = (p4BayWeight *)malloc(mem_bayweight_count * sizeof(p4BayWeight));
	mem_level_weights = (p4LevelWeight *)malloc(mem_levelweight_count * sizeof(p4LevelWeight));
	
	bay_weight_counter = -1;
	level_weight_counter = -1;
	
	for (i=0; i < mem_solution_count; ++i) {
		ss = &mem_solutions[i];
		
		switch (ss->unitOfIssue) {
		case 0:		// each
			caseWeight = ss->weight * ss->casePack;
			break;
		case 1:		// inner
			caseWeight = ss->weight * (ss->casePack/ss->innerPack);
			break;
		case 2:
			caseWeight = ss->weight;
			break;
		case 3:
			caseWeight = ss->weight / ss->numberInPallet;
			break;
		}
		
		weight = ss->caseCount * caseWeight;
		
		bayIdx = FindBay(ss->bayDBID);
		if (bayIdx < 0) {		// new bay
			bay_weight_counter++;
			mem_bay_weights[bay_weight_counter].bayDBID = ss->bayDBID;
			mem_bay_weights[bay_weight_counter].bayMaxWeight = ss->bayMaxWeight;
			mem_bay_weights[bay_weight_counter].currentBayWeight = weight;
		}
		else {
			mem_bay_weights[bayIdx].currentBayWeight += weight;
		}
		
		levelIdx = FindLevel(ss->levelDBID);
		if (levelIdx < 0) {		// new level
			level_weight_counter++;
			mem_level_weights[level_weight_counter].levelDBID = ss->levelDBID;
			mem_level_weights[level_weight_counter].levelMaxWeight = ss->levelMaxWeight;
			mem_level_weights[level_weight_counter].currentLevelWeight = weight;
			mem_level_weights[level_weight_counter].bayDBID = ss->bayDBID;
			mem_level_weights[level_weight_counter].relativeLevel = ss->relativeLevel;
		}
		else {
			mem_level_weights[levelIdx].currentLevelWeight += weight;
		}
		
		
	}
	
	return;
	
}


int Pass4Process::TacticalGetFacings(int li, int prodIndex)
{
	
	int maxFacingCount = GetMaxFacingCount(prodIndex);
	int availFacingCount;
	int prodRankIdx;
	int numTimes = 0;
	int fromLocIdx = mem_prods[prodIndex].prevLocIdx;
	int facingsNeeded, fromFacingCount, toProdIdx, toProdRankIdx;
	int rankOffset, prevProdRankIdx, prevToProdRankIdx;
	
	
	// get the current ranking that the product's assignment is using
	// if the product is not assigned, set the ranking to one higher
	// than the max, so all rankings will be considered better
	if (mem_prods[prodIndex].prevLocIdx < 0)
		prevProdRankIdx = prodIndex+numAvailRanks;
	else {
		prevProdRankIdx = NextProdIndex(prodIndex, mem_prods[prodIndex].prevLocIdx);
		// if somehow the product is assigned to a location that it doesn't
		// have a ranking for, assume that it is a bad location
		if (prevProdRankIdx < 0)
			prevProdRankIdx = prodIndex+numAvailRanks;
		
		// if the product is already assigned to its best ranking
		// don't try to move it
		if (prevProdRankIdx == prodIndex)
			return -1;
	}
	
	
	// from product is the product we are trying to slot
	// from location is the current location of the product we are trying to slot
	// to location is the location we are trying to put the from product in
	// to product is the product that is currently assigned to the to location
	
	// Loop until we've tried all facing counts for all rankings
	
	//while (numTimes <= maxFacingCount) {
	for (numTimes=0; numTimes < 2; ++numTimes) {
		
		// Loop for each ranking
		for (rankOffset=0; rankOffset < numAvailRanks; rankOffset++) {
			
			// performance, only look for the top ranking
			if (rankOffset > 0)
				break;
			
			// if the product's current ranking is better than or the same as
			// the one we are looking at, don't bother trying to move it
			if (prevProdRankIdx <= prodIndex+rankOffset)
				return -1;
			
			if (debugLog) {
				fprintf(p4TraceFile, "Product: %d is not at it's highest ranking.\n", mem_prods[prodIndex].dbID);
			}
			
			// For each location
			for (int toLocIdx=li; toLocIdx < mem_loc_count; toLocIdx++) {
				
				if (! mem_locs[toLocIdx].isSelect)
					continue;
				
				// the first time through, only look for open locations
				if (numTimes == 0 && mem_locs[toLocIdx].prevProdIdx >= 0)
					continue;
				
				// decrement facing count each time
				// note: leave this step inside the location for loop
				// so that the facingsNeeded will get reset each time
				// we try a new location
				facingsNeeded = mem_prods[prodIndex+rankOffset].facings - numTimes;
				// if we've tried all the facing possibilities for this ranking
				// go to the next ranking
				if (facingsNeeded <= 0)
					break;
				
				fromFacingCount = mem_prods[prodIndex].prevFacingCount;
				
				// See if this product will work in this location
				//if (debugLog)
				//	fprintf(p4TraceFile, "Before CheckProductInLocation\n");
				prodRankIdx = CheckProductInLocation(prodIndex, toLocIdx);
				if (prodRankIdx < 0)		// the product can't go to this location	
					continue;				// try the next location
				
				// look for one ranking at a time
				// if the product ranking that matches the location
				// is not the one we are currently looking for,
				// go to the next location
				if (prodRankIdx != prodIndex+rankOffset)
					continue;
				
				if (debugLog) {
					fprintf(p4TraceFile, "Found a better ranking for product: %d\n", mem_prods[prodIndex].dbID);
					fflush(p4TraceFile);
				}
				
				// set the expanded facings to the needed facings; if we find them
				// we will leave it; otherwise this will be overwritten the next
				// time around
				mem_prods[prodRankIdx].expandedFacings = facingsNeeded;
				
				// check the weight of the to location against the from product
				if (debugLog)
					fprintf(p4TraceFile, "Before CheckWeight: %d, %d, %d\n", prodRankIdx, toLocIdx, facingsNeeded);
				if (! CheckWeight(prodRankIdx, toLocIdx, facingsNeeded)) {
					if (debugLog)
						fprintf(p4TraceFile, "Failed weight test.\n");
					continue;
				}
				
				// see if another product is currently assigned to location 
				toProdIdx = mem_locs[toLocIdx].prevProdIdx;
				
				if (toProdIdx >= 0) {
					
					// In new product layout, only open locations are considered
					if (optimizeType == NEW_PRODUCT_LAYOUT)
						continue;
					
					// Skip when the to product is already in a good ranking
					if (mem_prods[toProdIdx].currentAssignedRanking == 1)
						continue;
					
					if (debugLog) {
						fprintf(p4TraceFile, "\tLoc: %d already has a product.\n", mem_locs[toLocIdx].Loc_dbid);
					}
					
					// see if the to product has enough facings to handle our product
					// add bay spanning logic later
					if (debugLog)
						fprintf(p4TraceFile, "Before FindOpenAdjacentLocations\n");
					availFacingCount = mem_prods[toProdIdx].prevFacingCount + 
						FindOpenAdjacentLocations(toLocIdx);
					
					if (availFacingCount < facingsNeeded) {
						if (debugLog) {
							fprintf(p4TraceFile, "\tNot enough adjacent facings for existing product.\n");
						}
						continue;	
					}
					
					if (fromLocIdx < 0) {
						// the current product does not have an assignment
						// so there's nothing to swap.
						// products without assignments must go to open locations
						if (debugLog) {
							fprintf(p4TraceFile, "\tCurrent product not assigned so nothing to swap.\n");
						}
						continue;
					}
					
					// See if the displaced product will work in the from location
					toProdRankIdx = CheckProductInLocation(toProdIdx, fromLocIdx);
					if (toProdRankIdx <= 0) {
						if (debugLog) {
							fprintf(p4TraceFile, "\tExisting product won't work in current product's location.\n");
						}
						continue;
					}
					
					// Get the displaced product's current ranking
					if (mem_prods[toProdIdx].prevLocIdx < 0)
						prevToProdRankIdx = toProdIdx+numAvailRanks;
					else {
						prevToProdRankIdx = NextProdIndex(toProdIdx, mem_prods[toProdIdx].prevLocIdx);
						// if somehow the product is assigned to a location that it doesn't
						// have a ranking for, assume that it is a bad location
						if (prevToProdRankIdx < 0)
							prevToProdRankIdx = toProdIdx+numAvailRanks;
					}
					
					// if the displaced product's current ranking is better
					// than what it would be if we swapped it, skip it for now
					// Note: add logic here to allow a 2nd pass where we try it;
					// the hurdle rate should still filter out any bad moves
					if (prevToProdRankIdx < toProdRankIdx) {
						if (debugLog) {
							fprintf(p4TraceFile, "\tExisting product has a better ranking than current product's location\n");
						}
						continue;
					}
					
					//check the weight of the from location against the displaced product
					// for now, just try a single facing because we don't know how many
					// facings are available
					if (debugLog)
						fprintf(p4TraceFile, "Before CheckWeight\n");
					if (! CheckWeight(toProdRankIdx, fromLocIdx, 1)) {
						if (debugLog) {
							fprintf(p4TraceFile, "\tExisting product weight too much for current product's location.\n");
						}
						continue;
					}
					
					// Now we're pretty sure the products can be swapped but
					// we still need to check the hurdle rate to make sure we
					// want to do it
					if (debugLog)
						fprintf(p4TraceFile, "Before TacticalCheckHurdleRate\n");
					
					if (! TacticalCheckHurdleRate(prodIndex, toLocIdx)) {
						if (debugLog) {
							fprintf(p4TraceFile, "\tProduct swap doesn't pass the hurdle rate test.\n");
						}
						continue;
					}
					
					// we can return here because we know that the products can be
					// swapped
					return toLocIdx;
					
				}
				
				// Now we know that the product can go in the location
				// and that the to location is open
				
				facingsNeeded--;					// subtract the facing we just found
				if (facingsNeeded == 0) {			// we must have only needed one facing
					// in new product layout, we don't look at the cost
					if (optimizeType == TACTICAL_LAYOUT) {
						if (! TacticalCheckHurdleRate(prodIndex, toLocIdx))
							continue;
					}
					return toLocIdx;	// we're done	
				}
				
				
				// Try to find extra facings in adjacent locations
				for (int adjLocIdx=1; adjLocIdx < facingsNeeded; ++adjLocIdx) {
					
					// See if the next location is adjacent to this one
					// Adjacency implies the same profile and level so 
					// there's no need to check the ranking again
					if (! LocsAreAdjacent(toLocIdx, toLocIdx+adjLocIdx))
						break;			// go to the next location
					
					// See if the product works in the location
					prodRankIdx = CheckProductInLocation(prodIndex, toLocIdx+adjLocIdx);
					if (prodRankIdx < 0)		// product can't go in location
						break;
					
					// see if another product is currently assigned to location 
					toProdIdx = mem_locs[toLocIdx+adjLocIdx].prevProdIdx;
					if (toProdIdx >= 0) {
						// if a product is there and had enough facings we would
						// have found it earlier; so we have to skip it
						break;
						
					}
					
					// we've found another adjacent location
					// that is open
					facingsNeeded--;
					if (facingsNeeded == 0) {
						
						// make sure the weight will fit
						if (! CheckWeight(prodRankIdx, toLocIdx+adjLocIdx, mem_prods[prodRankIdx].expandedFacings))
							break;
						
						// only check cost for tactical, not new product
						if (optimizeType == TACTICAL_LAYOUT) {
							if (! TacticalCheckHurdleRate(prodIndex, toLocIdx))
								break;
						}
						
						return toLocIdx;
					}
					
				}	// end of extra facings for-loop
				
				
			}	// end of location for-loop
		}
		//numTimes++;
		
	}	// end of decrement facings while-loop
	
	
	return -1;
}

int Pass4Process::TacticalCheckHurdleRate(int p, int p2)
{
	int toProdIdx = p2;
	int fromProdIdx = p;
	int fromLocIdx;
	int toLocIdx;
	int fromProdFacingCount, toProdFacingCount;
	double toCost, fromCost, tmpCost;
	double thadj;

	if (debugLog)
		fprintf(p4TraceFile, "Time Horizon: %d, fromProdIdx: %d, toProdIdx: %d\n", timeHorizonValue, p, p2);
	
	//if (strcmp(mem_prods[fromProdIdx].WMSProdID, "2301025") == 0)
	//printf("got it\n");
	
	switch (timeHorizonUnits) {
	case TIME_HORIZON_DAY:
		thadj = 1/7 * timeHorizonValue;
		break;
	case TIME_HORIZON_WEEK:
		thadj = timeHorizonValue;
		break;
	case TIME_HORIZON_MONTH:
		thadj = 4.35 * timeHorizonValue;
		break;
	case TIME_HORIZON_YEAR:
		thadj = 52.15 * timeHorizonValue;
		break;
	}
	
	// the from prod new facing count
	fromProdFacingCount = GetNewFacingCount(p);

	toProdFacingCount = GetNewFacingCount(p2);

	if ((fromProdFacingCount != toProdFacingCount) || fromProdFacingCount == 0 || toProdFacingCount == 0)
		return 0;
	
	fromLocIdx = mem_prods[p].newLocIdx;
		
	// cost of the from product in its current location
	if (fromLocIdx < 0)
		fromCost = 0;
	else {
		fromCost = thadj*TacticalCalcCost(fromProdIdx, fromLocIdx, fromProdFacingCount);
	}
	
	mem_prods[p].fromCost = fromCost;
	
	if (p2 >= 0) {
		// cost of the to product in it current location
		toLocIdx = mem_prods[p2].newLocIdx;
		if (toLocIdx < 0)
			
		tmpCost = thadj*TacticalCalcCost(p2, toLocIdx, toProdFacingCount);
		fromCost += tmpCost;
		mem_prods[toProdIdx].fromCost = tmpCost;
	}
	
	// cost of the from product in its new location
	toCost = thadj*TacticalCalcCost(fromProdIdx, toLocIdx, fromProdFacingCount);
	mem_prods[fromProdIdx].toCost = toCost;
	
	// cost of the to product in its new location
	if (fromLocIdx >= 0 && p >= 0) {
		tmpCost = thadj*TacticalCalcCost(toProdIdx, fromLocIdx, toProdFacingCount);
		toCost += tmpCost;
		mem_prods[toProdIdx].toCost = tmpCost;
	}
	
	
	double moveCost = 0.0;
	if (debugLog)
		fprintf(p4TraceFile, "Before TacticalCalcMoveCost, %d, %d, %d, %d\n", fromProdIdx, toLocIdx, fromProdFacingCount, toProdFacingCount);
	moveCost = TacticalCalcMoveCost(p, toLocIdx, fromProdFacingCount, toProdFacingCount);
	
	mem_prods[fromProdIdx].moveCost = moveCost;
	if (toProdIdx >= 0)
		mem_prods[toProdIdx].moveCost = moveCost;
	
	// spread the from cost and the to cost over the entire time horzon
	// they are calculated as weekly values
	
	
	
	char buffer[256];
	if (debugLog) {
		sprintf(buffer, "Comparing product: %d", mem_prods[fromProdIdx].dbID);
		if (fromLocIdx >= 0)
			sprintf(buffer, "%s in location: %d", buffer, mem_locs[fromLocIdx].Loc_dbid);
		if (toProdIdx >= 0)
			sprintf(buffer, "%s to product: %d", buffer, mem_prods[toProdIdx].dbID);
		if (toLocIdx >= 0)
			sprintf(buffer, "%s in location %d", buffer, mem_locs[toLocIdx].Loc_dbid);
		
		sprintf(buffer, "%s.  From cost: %5.2f   To Cost: %5.2f   Move Cost: %5.2f\n",
			buffer, fromCost, toCost, moveCost);
		
		fprintf(p4TraceFile, "%s", buffer);
		
	}
	
	
	
	if (constraintType == CONSTRAIN_BY_PCT) {
		if (toCost + moveCost <= (1-constraintAmount/100)*fromCost)
			return 1;
		else
			return 0;
	}
	else {
		if (toCost + moveCost + constraintAmount <= fromCost) {
			if (strcmp(mem_prods[fromProdIdx].WMSProdID, "101043") == 0)
				int z = 0;
			return 1;
		}
		else
			return 0;
	}
	
}

double Pass4Process::TacticalCalcMoveCost(int fromProdIdx, int toLocIdx, int fromFacingCount, int toFacingCount)
{
	int maxFacingCount;
	int existingFromFacings, existingToFacings;
	int neededFromFacings, neededToFacings;
	int toProdIdx, i, fromLocIdx;
	int moveCount, counter;
	double moveCost = 0.0;
	CMove *fromMoves, *toMoves, *finalMoves;
	
	fromLocIdx = mem_prods[fromProdIdx].newLocIdx;
	
	toProdIdx = mem_locs[toLocIdx].newProdIdx;
	if (toProdIdx >= 0) {
		existingToFacings = fromFacingCount;  // facing counts will always be equal otherwise no swap
		neededToFacings = toFacingCount;
	}
	else {
		existingToFacings = 0;
		neededToFacings = 0;
	}
	
	existingFromFacings = fromFacingCount;  // facing counts will always be equal otherwise no swap
	neededFromFacings = fromFacingCount;    // same here
	
	maxFacingCount = existingFromFacings;
	if (existingToFacings > maxFacingCount)
		maxFacingCount = existingToFacings;
	if (neededFromFacings > maxFacingCount)
		maxFacingCount = neededFromFacings;
	if (neededToFacings > maxFacingCount)
		maxFacingCount = neededToFacings;
	
	fromMoves = new CMove[maxFacingCount];
	toMoves = new CMove[maxFacingCount];
	
	// first, loop through the facings to create the move types we need
	for (i=0; i < maxFacingCount; ++i) {
		
		// Create the from moves
		if (neededFromFacings > i) {
			if (existingFromFacings > i) {
				// move from
				fromMoves[i].m_MoveType = MOVE_NORMAL;
				fromMoves[i].m_productIdx = fromProdIdx;
				fromMoves[i].m_fromLocIdx = fromLocIdx + i;
				fromMoves[i].m_toLocIdx = toLocIdx + i;
			}
			else {
				// add from
				fromMoves[i].m_MoveType = MOVE_ADD_LOC;
				fromMoves[i].m_productIdx = fromProdIdx;
				fromMoves[i].m_fromLocIdx = -1;
				fromMoves[i].m_toLocIdx = toLocIdx+i;
			}
		}
		else {
			if (existingFromFacings > i) {
				// delete from
				fromMoves[i].m_MoveType = MOVE_DEL_LOC;
				fromMoves[i].m_productIdx = fromProdIdx;
				fromMoves[i].m_fromLocIdx = fromLocIdx+i;
				fromMoves[i].m_toLocIdx = -1;
			}
			else {
				// nothing
				fromMoves[i].m_MoveType = MOVE_NONE;
			}
		}
		
		// Create the to moves;
		if (neededToFacings > i) {
			if (existingToFacings > i) {
				// move to
				toMoves[i].m_MoveType = MOVE_NORMAL;
				toMoves[i].m_productIdx = toProdIdx;
				toMoves[i].m_fromLocIdx = toLocIdx+i;
				toMoves[i].m_toLocIdx = fromLocIdx+i;
				
			}
			else {
				// add to
				toMoves[i].m_MoveType = MOVE_ADD_LOC;
				toMoves[i].m_productIdx = toProdIdx;
				toMoves[i].m_toLocIdx = fromLocIdx+i;
				toMoves[i].m_fromLocIdx = -1;
			}
		}
		else {
			if (existingToFacings > i) {
				// delete to
				toMoves[i].m_MoveType = MOVE_DEL_LOC;
				toMoves[i].m_productIdx = toProdIdx;
				toMoves[i].m_fromLocIdx = toLocIdx+i;
				toMoves[i].m_toLocIdx = -1;
			}
			else {
				// nothing
				toMoves[i].m_MoveType = MOVE_NONE;
			}
		}
		
	}
	
	
	// now we have two arrays - one of from moves, one of to moves
	// now merge to create one big list of moves
	
	// the first time through, just get the count so we 
	// know how big to make the final 
	moveCount = 0;
	for (i=0; i < maxFacingCount; i++) {
		if (fromMoves[i].m_MoveType == MOVE_NORMAL &&
			toMoves[i].m_MoveType == MOVE_NORMAL) {
			// from to tmp
			// normal to move
			// tmp to from
			moveCount += 3;
		}
		else {
			// add from
			// add to
			if (fromMoves[i].m_MoveType != MOVE_NONE)
				moveCount++;
			
			if (toMoves[i].m_MoveType != MOVE_NONE)
				moveCount++;
		}
		
	}		
	
	finalMoves = new CMove[moveCount];
	counter = -1;
	
	for (i=0; i < maxFacingCount; i++) {
		if (fromMoves[i].m_MoveType == MOVE_NORMAL &&
			toMoves[i].m_MoveType == MOVE_NORMAL) {
			// from-product to tmp-loc
			counter++;
			finalMoves[counter].m_MoveType = MOVE_TO_TEMP;
			finalMoves[counter].m_productIdx = fromMoves[i].m_productIdx;
			finalMoves[counter].m_fromLocIdx = fromMoves[i].m_fromLocIdx;
			finalMoves[counter].m_toLocIdx = -1;
			
			// to-product to from-loc
			counter++;
			finalMoves[counter] = toMoves[i];
			
			// from-product to to-loc
			counter++;
			finalMoves[counter].m_MoveType = MOVE_FROM_TEMP;
			finalMoves[counter].m_productIdx = fromMoves[i].m_productIdx;
			finalMoves[counter].m_fromLocIdx = -1;
			finalMoves[counter].m_toLocIdx = fromMoves[i].m_toLocIdx;
		}
		else {
			// add from
			// add to
			if (fromMoves[i].m_MoveType != MOVE_NONE) {
				counter++;
				finalMoves[counter] = fromMoves[i];
			}
			
			if (toMoves[i].m_MoveType != MOVE_NONE) {
				counter++;
				finalMoves[counter] = toMoves[i];
			}
			
		}
		
	}
	
	CMoveCost fromMoveCost, toMoveCost;
	CLaborCalc laborCalc;
	char **stockerLabor;
	
	// load the stocker labor values one time since they won't change
	stockerLabor = (char **)malloc((mem_labor_stocker_count+1)*sizeof(char *));
	// add exception handling here
	stockerLabor[0] = (char *)malloc(400*sizeof(char));
	sprintf(stockerLabor[0], "LABORSTART|%d|%d|", STOCKER_LABOR, mem_labor_stocker_count);
	
	for (i=0; i < mem_labor_stocker_count; ++i) {
		stockerLabor[i+1] = (char *)malloc(100*sizeof(char));
		// add exception handling here
		sprintf(stockerLabor[i+1], "LABOR|%d|%d|%f|%f|%f|",
			mem_labor_stocker[i].ProfId,
			mem_labor_stocker[i].RelLev,
			mem_labor_stocker[i].Cube,
			mem_labor_stocker[i].Fxd,
			mem_labor_stocker[i].Var);
	}
	
	
	laborCalc.LoadLabor(stockerLabor);
	
	for (i=0; i < mem_labor_stocker_count+1; ++i) {
		free(stockerLabor[i]);
	}
	
	free(stockerLabor);
	
	// now we have one list of moves
	// start calculating the cost
	// for each move; create a full move cost record and use the laborCalc
	// class to calculate the cost
	for (i=0; i < moveCount; ++i) {
		// build from move cost
		if (debugLog)
			fprintf(p4TraceFile, "Before CalcMoveCost\n");
		
		moveCost += CalcMoveCost(&finalMoves[i],  &laborCalc);
		// build to move cost
	}
	
	delete [] fromMoves;
	delete [] toMoves;
	delete [] finalMoves;
	
	if (debugLog)
		fprintf(p4TraceFile, "Before Return\n");
	return moveCost;
	
}


double Pass4Process::CalcMoveCost(CMove *pMove, CLaborCalc *pLaborCalc)
{
	CMoveCost fromMoveCost, toMoveCost;
	CProductLabor productLabor;
	int prodIdx, fromLocIdx, toLocIdx;
	int fromCaseQuantity, toCaseQuantity;
	double moveCost;
	
	prodIdx = pMove->m_productIdx;
	fromLocIdx = pMove->m_fromLocIdx;
	toLocIdx = pMove->m_toLocIdx;
	
	// build from
	
	if (fromLocIdx >= 0) {
		fromMoveCost.m_MoveType = pMove->m_MoveType;
		fromCaseQuantity = MaxCaseFit(&mem_prods[prodIdx], &mem_locs[fromLocIdx]);
		BuildMoveCostData(prodIdx, fromLocIdx, fromCaseQuantity, fromMoveCost);
	}
	// else it's either a move from tmp or an add facing
	
	//build to
	
	if (toLocIdx >= 0) {
		toMoveCost.m_MoveType = pMove->m_MoveType;
		toCaseQuantity = MaxCaseFit(&mem_prods[prodIdx], &mem_locs[toLocIdx]);
		BuildMoveCostData(prodIdx, toLocIdx, toCaseQuantity, toMoveCost);
	}
	// else it's either a move to tmp or a delete facing
	
	productLabor.m_ProductDBID = mem_prods[prodIdx].dbID;
	productLabor.m_CaseCube = mem_prods[prodIdx].caseCube;
	productLabor.m_CaseWeight = mem_prods[prodIdx].caseWeight;
	productLabor.m_CasePack = (int)mem_prods[prodIdx].casePack;
	productLabor.m_InnerPack = (int)mem_prods[prodIdx].innerPack;
	
	switch (mem_prods[prodIdx].unitOfIssue) {
	case UOI_EACH:
		productLabor.m_EachCube = mem_prods[prodIdx].cube;
		productLabor.m_EachWeight = mem_prods[prodIdx].weight;
		break;
	case UOI_INNER:
		productLabor.m_InnerCube = mem_prods[prodIdx].cube;
		productLabor.m_InnerWeight = mem_prods[prodIdx].weight;
		break;
	}
	
	// now we have the cost of both sides of the move
	// and the product labor 
	// build the text strings to send to the labor calculation
	char tempText[256];
	char **calcText;
	calcText = (char **)malloc(4*sizeof(char *));
	// add exception handling here
	
	for (int i=0; i < 4; ++i) {
		calcText[i] = (char *)malloc(256*sizeof(char));
		// add exception handling here
	}
	
	
	strcpy(calcText[0], "MOVEPARAMETERS|4");
	
	fromMoveCost.StreamAttributes(tempText);
	strcpy(calcText[1], "FROMMOVECOST|");
	strcat(calcText[1], tempText);
	
	toMoveCost.StreamAttributes(tempText);
	strcpy(calcText[2], "TOMOVECOST|");
	strcat(calcText[2], tempText);
	
	
	productLabor.StreamAttributes(tempText);
	strcpy(calcText[3], tempText);
	
	moveCost = pLaborCalc->CalcMoveCost(calcText);
	
	for (i=0; i < 4; ++i) {
		free(calcText[i]);
	}
	
	free(calcText);
	
	
	return moveCost;
}

void Pass4Process::BuildMoveCostData(int prodIdx, int locIdx, int caseQuantity, CMoveCost &moveCost)
{
	int sectionIdx, aisleIdx;
	
	if (prodIdx < 0 || locIdx < 0) {
		fprintf(stdout, "bad move cost\n");
	}
	moveCost.m_CaseQuantity = caseQuantity;
	moveCost.m_ProductDBID = mem_prods[prodIdx].dbID;
	
	moveCost.m_SectionDBID = mem_locs[locIdx].SecID;
	//	if (mem_locs[locIdx].SecID > 500000)
	//		fprintf(stdout, "bad section\n");
	moveCost.m_AisleDBID = mem_locs[locIdx].Aisle_dbid;
	
	moveCost.m_LocationDBID = mem_locs[locIdx].Loc_dbid;
	moveCost.m_BayProfileDBID = mem_locs[locIdx].BayProfID;
	moveCost.m_BayType = mem_locs[locIdx].levelType;
	moveCost.m_RelativeLevel = mem_locs[locIdx].RelLev;
	
	moveCost.m_LocationCoordinates.m_x = mem_locs[locIdx].Loc_x;
	moveCost.m_LocationCoordinates.m_y = mem_locs[locIdx].Loc_y;
	moveCost.m_LocationCoordinates.m_z = mem_locs[locIdx].Loc_z;
	
	aisleIdx = FindAisle(mem_locs[locIdx].Aisle_dbid);
	if (aisleIdx < 0) {
		moveCost.m_AisleEntryCoordinates.m_x = 0;
		moveCost.m_AisleEntryCoordinates.m_y = 0;
		moveCost.m_AisleEntryCoordinates.m_z = 0;
		
		moveCost.m_AisleExitCoordinates.m_x = 0;
		moveCost.m_AisleExitCoordinates.m_y = 0;
		moveCost.m_AisleExitCoordinates.m_z = 0;
	}
	else {
		moveCost.m_AisleEntryCoordinates.m_x = mem_aisleentryexits[aisleIdx].EntryX;
		moveCost.m_AisleEntryCoordinates.m_y = mem_aisleentryexits[aisleIdx].EntryY;
		moveCost.m_AisleEntryCoordinates.m_z = mem_aisleentryexits[aisleIdx].EntryZ;
		
		moveCost.m_AisleExitCoordinates.m_x = mem_aisleentryexits[aisleIdx].ExitX;
		moveCost.m_AisleExitCoordinates.m_y = mem_aisleentryexits[aisleIdx].ExitY;
		moveCost.m_AisleExitCoordinates.m_z = mem_aisleentryexits[aisleIdx].ExitZ;
	}
	
	
	sectionIdx = GetSectionIdx(mem_locs[locIdx].SecID);
	
	moveCost.m_ForkFixedExtraction = mem_sections[sectionIdx].pickForkTrav;
	moveCost.m_ForkFixedInsertion = mem_sections[sectionIdx].insertForkTrav;
	moveCost.m_ForkLaborRate = mem_sections[sectionIdx].ForkRate;
	moveCost.m_ForkDistanceFixed = mem_sections[sectionIdx].ForkFxd;
	moveCost.m_ForkDistanceVariable = mem_sections[sectionIdx].ForkVar;
	moveCost.m_StockerDistanceFixed = mem_sections[sectionIdx].StockerDistanceFixed;
	moveCost.m_StockerDistanceVariable = mem_sections[sectionIdx].StockerDistanceVariable;
	moveCost.m_StockerLaborRate = mem_sections[sectionIdx].stockerRate;
	
	return;
	
}

double Pass4Process::TacticalCalcCost(int prodIndex, int locIndex, int facingCount)
{
	char costbuf[COST_BUFFER_SIZE];
	int caseCount;
	
	memset(costbuf,0,COST_BUFFER_SIZE);
	
	// all of the facings are of the same type so we can
	// just multiple the case capacity of one location by
	// the total number of locations
	caseCount = facingCount * MaxCaseFit(&mem_prods[prodIndex], &mem_locs[locIndex]);
	
	gProdIdx = prodIndex;
	gLocIdx = locIndex;
	if (debugLog)
		fprintf(p4TraceFile, "Before GetCost: %d, %d\n", prodIndex, locIndex);
	return GetCost(prodIndex, locIndex, caseCount, costbuf);
	
}


int Pass4Process::GetExistingFacingCount(int prodIndex)
{
	
	int count = 0;
	int locIdx = 0;
	
	for (locIdx = 0; locIdx < mem_loc_count; locIdx++) {
		if (mem_locs[locIdx].prevProdIdx == prodIndex)
			count++;
	}
	
	return count;
}

int Pass4Process::GetAisleEntryExit()
{
	char buffer[BUFSIZE];
	unsigned int  err;
	int i;
	
	
	/* ********************************************* */
	/* Wait for the start of the Bay stream          */
	/* ********************************************* */
	do {
		memset(buffer, 0, BUFSIZE);
		if ( gfnGetDataStream()->ssData.getline(buffer, BUFSIZE) == 0) {
			throw EngineException("Error receiving on stream",
				__FILE__, __LINE__, 200);
		}
		if ( debugLog )
			fprintf(p4TraceFile,"\nReceived data: %s",buffer);
	} while (strncmp(buffer, "P4AISLEENTRYEXIT", 16) != 0);
	
	mem_aisleentryexit_count = atoi(buffer+17);		
	if(mem_aisleentryexit_count == 0)
		return 0;
	
	/* ********************************************* */
	/* Allocate memory to hold the Locations.        */
	/* ********************************************* */
	if ( debugLog )
		fprintf(p4TraceFile,"Allocating Memory for AisleEntryExit %d\n",mem_aisleentryexit_count);
	
	mem_aisleentryexits = (p4AisleEntryExit *)malloc(mem_aisleentryexit_count * sizeof(p4AisleEntryExit));
	if(mem_aisleentryexits == NULL){
		throw EngineException("Error Allocating memory for aisle entry exit list",
			__FILE__, __LINE__, 300);
	}
	
	memset(mem_aisleentryexits,0,mem_aisleentryexit_count * sizeof(p4AisleEntryExit));
	
	for(i=0;i<mem_aisleentryexit_count;i++) {
		GetOneAisleEntryExit(i);	
	}
	
	
	return mem_aisleentryexit_count;
}

void Pass4Process::GetOneAisleEntryExit(int idx)
{
	char buffer[BUFSIZE];
	unsigned int  err;
	char *ptr;
	
	/* ******************************************** */
	/* Wait for the start of the Bay stream         */
	/* ******************************************** */
	do {
		memset(buffer, 0, BUFSIZE);
		if ( gfnGetDataStream()->ssData.getline(buffer, BUFSIZE) == 0) {
		
			throw EngineException("Error receiving on stream",
				__FILE__, __LINE__, 200);
		}
		if ( debugLog )
			fprintf(p4TraceFile,"\nReceived data: %s",buffer);
	} while (strncmp(buffer, "AISLEENTRYEXIT", 14) != 0);
	
	
	
	ptr = strtok(buffer+15, "|");
	mem_aisleentryexits[idx].EntryX = atoi(ptr);
	ptr = strtok(NULL, "|");
	mem_aisleentryexits[idx].EntryY = atoi(ptr);
	ptr = strtok(NULL, "|");
	mem_aisleentryexits[idx].ExitX = atoi(ptr);
	ptr = strtok(NULL, "|");
	mem_aisleentryexits[idx].ExitY = atoi(ptr);
	ptr = strtok(NULL, "|");
	mem_aisleentryexits[idx].AisleDBID = atoi(ptr);
	
	mem_aisleentryexits[idx].EntryZ = 0;
	mem_aisleentryexits[idx].ExitZ = 0;
	
}


int Pass4Process::FindAisle(int pAisleDBID)
{
	
	for (int i=0; i < mem_aisleentryexit_count; ++i) {
		if (mem_aisleentryexits[i].AisleDBID == pAisleDBID)
			return i;
	}
	
	return -1;
}




/* To do
1.  Add weight checking - test
2.  Add new data to p4prodpack, p4Location
2a.  p4prodPack
- prevFacingCount
- prevLocIdx
- newLocIdx
- prodGroupDBID

  2b.	p4Location
  - prodGroupDBID
  - prevProdIdx
  - newProdIdx
  - bayXCoord		- later
  - bayWidth		- later
  - bayBarWidth	- later
  
	
	  
		3.  Check existing facings to make sure they are adjacent - done
		4.  Synchronize lists so that after sorting we can still 
		reference prods and locs by index - done
		5.  Change in data to send all products, all locations instead of by product group - done
		6.  Change results writing to delete only those that have a new result - done
		7.  Need global facility optimize attribute and method
		8.  Add logging
		9.  Allow adjacent open locations to be considered as existing facings - done
		10. Change group assign to handle 9 - done
		11. Add caseCount to mem_solutions in forte. - done
		12. Need time horizon by product group
		13. Tactical stuff to send back:  old location, new location, old operating cost,
		new operating cost, move cost, displaced product
		14. Group stuff to send back:  old location, new location, displaced product
		15. Change menu item for pass1
		16. Remove bogus tabs from group and tactical
		17. Re-write time horizon, cost comparison in C++.
		
		  
*/


int Pass4Process::CheckOverlap(int p, int l)
{
	int count;
	
	//
	// Next check for overlapping if allowed
	//
	
	if (! overlapProds)
		return 0;
	
	if (! mem_locs[l].overlapAllowed)
		return 0;
	
	if (mem_locs[l].VarWidth == 1)
		return 0;
	
	if (! DoesDepthHeightFit(&mem_prods[p], &mem_locs[l]) == 1)
		return 0;
	
	
	count = 1;
	p4ProdPack tempProd;
	ProdCopy(&tempProd, &mem_prods[p]);
	
	tempProd.width -= mem_locs[l].Loc_w;
	/* ******************************************** */
	/* reduce the remaining width of the product    */
	/* until it all fits in on the level.          */
	/* ******************************************** */
	for (int j = l+1; j < mem_loc_count; ++j) {
		
		count++;
		
		if (mem_locs[j].BayID != mem_locs[l].BayID && mem_locs[j].BayType != BAY_TYPE_FLOOR)
			return 0;
		
		if (mem_locs[j].Side_dbid != mem_locs[l].Side_dbid)
			return 0;
		
		if (mem_locs[j].RelLev != mem_locs[l].RelLev) 
			return 0;
		
		if (mem_locs[j].BayProfID != mem_locs[l].BayProfID) 
			return 0;
		
		if (MaxCaseFit(&tempProd, &mem_locs[j]) > 0)
			return count;
		
		tempProd.width -= mem_locs[j].Loc_w;
		
	}
	
	return 0;			
				
}

void Pass4Process::TimeStamp(const char *msg)
{
	//if (! TIMESTAMP)
	//	return;
	
	struct tm *newtime;
	time_t aclock;
	
	time( &aclock );                 /* Get time in seconds */
	newtime = localtime( &aclock );  /* Convert time to struct */
	/* tm form */
	char t[50];
	sprintf(t, "%s", asctime(newtime));
	t[strlen(t)-1] = 0;
	
	/* Print local time as a string */
	fprintf(stdout, "%s - %s", t + 11, msg);
	
}

double Pass4Process::CalculateDistance(double x1, double y1, double x2, double y2)
{
	// had trouble with the variable getting too big when I tried to calculate it all at once
	// so I split it up; not sure why it works differently
	
	double xdist, ydist;
	xdist = x2 - x1;
	xdist = xdist * xdist;
	ydist = y2 - y1;
	ydist = ydist * ydist;
	
	return (double)sqrt(xdist + ydist);
	
}

double Pass4Process::distance(double x1, double y1, double x2, double y2)
{
	double distance;
	
	distance = abs((int)(x1-x2)) + abs((int)(y1-y2));
	
	return distance;
}

void Pass4Process::ExecuteP4Query(string buf, vector<string> &results)
{
	char recvlin[BUFSIZE];
	char buff[BUFSIZE];
	int err;
	
	string sockString = "<SQL>";
	int i = -1;
	int subs = 0;
	
	sockString.append(buf);
	sockString.append("</SQL>");
	
	//fprintf(stdout, "%s\n",sockString.c_str());
	/***>>>
	err = p4sock->SendData(sockString.c_str(), sockString.size());
	<<<***/
	gfnGetDataStream()->ssData << sockString;
	/***>>>
	if (err == -1) {
		throw EngineException("Error receiving on stream",
			__FILE__, __LINE__, 200);
	}
	<<<***/
	
	// This function executes an SQL query that comes to us via buf and the results are put in 'results'
	// it works using the same socket connection to the forte server that we currently have open to send 
	// back engine processing results.
	
	// Strip off the <ESG> and <SQLDATA> lines to start
	
	do {
		memset(recvlin, 0, BUFSIZE);
		if ( gfnGetDataStream()->ssData.getline(recvlin, BUFSIZE) == 0){
			throw EngineException("Error receiving on stream",
				__FILE__, __LINE__, 200);
		}
	} while (strncmp(recvlin, "<SQLDATA>", 9) != 0);
	
	// read the rest of the data until the </SQLDATA>, stripping off the first line (column names)
	
	do {
		i++;
		memset(recvlin, 0, BUFSIZE);
		memset(buff, 0, BUFSIZE);
		if ( gfnGetDataStream()->ssData.getline(recvlin, BUFSIZE) == 0) {	
			throw EngineException("Error receiving on stream",
				__FILE__, __LINE__, 200);
		}
		if (i == 0)
			continue;
		results.push_back(recvlin);

	} while (strncmp(recvlin, "</SQLDATA>", 10) != 0);
	
}

void Pass4Process::TacticalFacings(p4ProdPack &product)
{
	
	int i, j;
	// This function looks in the products group for bay types and populates a vector
	// of facings needed in each bay type in the product group

	//fprintf(stdout, "Looking at product: %d.\n", product.WMSProdID);

	for (i = 0; i < levelProfiles.size(); i++) {
		//fprintf(stdout, "levelprofile max facings = %d.\n", levelProfiles[i].maxFacings);
		for (j = 0; j < levelProfiles[i].bayType.size(); j++) {
		product.facingsInfo.facingsneeded.push_back(TacticalGetNeededFacings(product, i, j));
		product.facingsInfo.bayProfID.push_back(levelProfiles[i].bayProfID);
		product.facingsInfo.bayType.push_back(levelProfiles[i].bayType[j]);
		}
	}

	
}

int Pass4Process::isLocationinProductGroup(p4ProdPack product, p4Location loc)
{
	//	fprintf(stdout, "In IsLocationInProductGroup productgroup = %d.\n", product.prodGroupDBID);
	if (loc.prodGroupDBID == product.prodGroupDBID)
		return 0;
	else
		return 1;
}

int Pass4Process::TacticalGetNeededFacings(p4ProdPack &product, int levelProfileIdx, int bayTypeIdx)
{
	double productExtCube;
	double locExtCube;
	int facsNeeded;

	//FILE *f;

	//f = fopen("debug.log", "a+");

	
	
	productExtCube = product.caseCube * product.movement;
			
	locExtCube = levelProfiles[levelProfileIdx].extendedCube[bayTypeIdx];
			
	facsNeeded = RoundClosest(productExtCube/locExtCube);

	//fprintf(stdout, "calculated facsNeeded = %d \n", facsNeeded);
			
	if (facsNeeded > levelProfiles[levelProfileIdx].maxFacings)
		facsNeeded = levelProfiles[levelProfileIdx].maxFacings;
			
	if (facsNeeded == 0)
		facsNeeded++;

	//fprintf(stdout, "facsNeeded = %d.\n", facsNeeded);
	//fprintf(stdout, "product = %d. prodCube = %f, locCube = %f, facsNeeded = %d.\n", product.dbID, productExtCube, locExtCube, facsNeeded);

	//fclose(f);
			
	return facsNeeded;
}

void Pass4Process::GetExtendedValues()
{
	// This function populates the levelProfiles global.  
	
	string sql;
	vector<string> results;
	vector<string> parsedValues;

	int i,j, found;
	
	//Build our SQL
	
	sql.assign("select bp.dbbayprofileid, fi.facingcount, br.baytype, fi.extendedcube, fi.extendedboh ");
	sql.append("from dbbayprofile bp, dbbayrule br, dbfacinginfo fi ");
	sql.append("where bp.dbbayprofileid = br.dbbayprofileid ");
	sql.append("and br.dbbayruleid = fi.dbbayruleid ");
	sql.append("and fi.facingcount = ( select max(facingcount) from dbfacinginfo fi2 ");
	sql.append("where fi2.dbbayruleid = fi.dbbayruleid) ");
	
	//Execute SQL
	
	ExecuteP4Query(sql, results);
	
	//Parse results
	
	for(i = 0; i < results.size(); i++) {
		utilityHelper.ParseString(results[i], "|", parsedValues);
		//check to see if we already have a record for this Bay Profile Id
		found = 0;
		for (j = 0; j < levelProfiles.size(); j++) {
			if (levelProfiles[j].bayProfID == atoi(parsedValues[0].c_str())) {
				// we found the levelProfile associated with this Bay Profile, so break
				found = 1;
				break;
			}
		}
		if (found == 0) {
			// we need to create a record
			levelProfileInfo levelProfile;
			levelProfile.bayProfID = atoi(parsedValues[0].c_str());
			levelProfile.maxFacings = atoi(parsedValues[1].c_str());
			//fprintf(stdout, "Max Facings = %d.\n", levelProfile.maxFacings);
			levelProfile.bayType.push_back(atoi(parsedValues[2].c_str()));
			levelProfile.extendedCube.push_back(atof(parsedValues[3].c_str())/levelProfile.maxFacings);
			//fprintf(stdout, "Ext Cube = %f.\n", atof(parsedValues[3].c_str())/levelProfile.maxFacings);
			levelProfile.extendedBOH.push_back(atof(parsedValues[4].c_str())/levelProfile.maxFacings);
			levelProfiles.push_back(levelProfile);
		}
		else {
			// add the data to the existing vectors
			levelProfiles[j].bayType.push_back(atoi(parsedValues[2].c_str()));
			levelProfiles[j].extendedCube.push_back(atof(parsedValues[3].c_str()));
			levelProfiles[j].extendedBOH.push_back(atof(parsedValues[4].c_str()));
		}
		
	}
	
}

int Pass4Process::RoundClosest(double value)
{
	value = value+.5f;
	return RoundDown(value);
}


int Pass4Process::TacticalRunComp()
{
	// MFS 21Mar06 Too late to make Tactical work for 3.0.  The function
	// ExecuteP4Query(), which is used in two places, attempts to use the
	// socket connection which no longer exists.  That attempt corrupts
	// the stream in the new version, causing a crash.  All nine of the
	// subprocesses of Tactical are unique from the other pass 4 routines.
	// Estimate 2-4 person weeks development work to revive.
	fprintf(stdout, "Starting tactical processing.\n");
	
	// get data on our product groups
	TimeStamp("Starting GetSlottingGroupData\n");
	GetSlottingGroupData();
	
	// build vectors of facings on each product which represent existing facings
	TimeStamp("Starting PopulateFacingLists\n");
	PopulateFacingLists();
	
	// calculate # of facings required for each product, and add locations/products to arrays
	TimeStamp("Starting CategorizeProducts\n");
	CategorizeProducts();
	
	// remove the assignments of reducible products so that they appear open
	TimeStamp("Starting RemoveReducibleAssignments\n");
	RemoveReducibleAssignments();
	
	// Cycle through list of locations and look for open locations.
	TimeStamp("Starting PopulateOpenFacings\n");
	PopulateOpenFacings();
	
	// process the product group info we got from the database
	TimeStamp("Starting PopulatePGroupInfo\n");
	PopulatePGroupInfo();
	
	// clear optimized assignments
	TimeStamp("Starting ClearAssignments\n");
	ClearAssignments();
	
	// slot the unslotted products
	if (m_slotnew) {
	TimeStamp("Starting SlotUnslottedProducts\n");
	SlotUnslottedProducts();
	}
	
	// re-slot existing products that need more facings
	if (m_reslot) {
	TimeStamp("Starting ReslotProducts\n");
	ReslotProducts();
	}
	
	// put back assignments for reducible locations that didn't get used.
	TimeStamp("Starting RestoreReducibleAssignments\n");
	RestoreReducibleAssignments();

	// assign products that haven't moved to their current location
	TimeStamp("Starting AssignUnmovedProducts\n");
	AssignUnmovedProducts();

	// loop through products, look at every other product in group for each product - *optional*
	// this should ensure products are in optimal locations
	if (m_findswaps) {
	TimeStamp("Starting FindSwaps\n");
	FindSwaps();
	}
	
	// add tactical info records, add assignments, process existing products that didn't move
	TimeStamp("Starting ProcessTacticalResults\n");
	ProcessTacticalResults();
	TimeStamp("Finished\n");
	fprintf(stdout, "Finished processing, sending results back to forte.\n");
	
	return 0;
	
}

int Pass4Process::RoundDown(double value)
{
	int valueint = (int)value;
	
	return valueint;
}

int AdjacentSortCompare(const void *p1, const void *p2) 
{
	p4Location *l1, *l2;
	
	l1 = (p4Location *) p1;
	l2 = (p4Location *) p2;
	
	if (l1->Aisle_dbid > l2->Aisle_dbid)
		return 1;
	else if (l2->Aisle_dbid < l2->Aisle_dbid)
		return -1;
	else {
		if (l1->Side_dbid > l2->Side_dbid)
			return 1;
		else if (l1->Side_dbid < l2->Side_dbid)
			return -1;
		else {
			if (l1->RelLev > l2->RelLev)
				return 1;
			else if (l1->RelLev < l2->RelLev)
				return -1;
			else {
				if (l1->Loc_x > l2->Loc_x)
					return 1;
				else if (l1->Loc_x < l2->Loc_x)
					return -1;
				else {
					if (l1->Loc_y > l2->Loc_y)
						return 1;
					else if (l1->Loc_y < l2->Loc_y)
						return -1;
					else
						return 0;
				}
			}
		}
	}
}

void Pass4Process::PopulateAdjacentLocs()
{
	
	//sort by aisle, side, level, x, y
	//should produce ordered list of adjacent locations.
	//Then lets populate the adjacentlocs fields on each mem_loc.
	//Only doing this once saves time.
	//Only populate adjacent facing if we are actually on the same aisle, side, and relative level
	
	qsort(mem_locs, mem_loc_count, sizeof(p4Location), AdjacentSortCompare);
	
	
	// handle start and end cases
	
	if (mem_locs[0].Aisle_dbid == mem_locs[1].Aisle_dbid) {
		if (mem_locs[0].Side_dbid == mem_locs[1].Side_dbid) {
			if (mem_locs[0].RelLev == mem_locs[1].RelLev) {
				if (mem_locs[0].baySpanAllowed == 0) {
					if (mem_locs[0].BayID == mem_locs[1].BayID)
						mem_locs[0].adjacent_dbid1 = mem_locs[1].Loc_dbid;
				}
				else
					mem_locs[0].adjacent_dbid1 = mem_locs[1].Loc_dbid;
			}
		}
	}
	
	if (mem_locs[mem_loc_count-1].Aisle_dbid == mem_locs[mem_loc_count - 2].Aisle_dbid) {
		if (mem_locs[mem_loc_count-1].Side_dbid == mem_locs[mem_loc_count - 2].Side_dbid) {
			if (mem_locs[mem_loc_count-1].RelLev == mem_locs[mem_loc_count - 2].RelLev) {
				if (mem_locs[mem_loc_count-1].baySpanAllowed == 0) {
					if (mem_locs[mem_loc_count-1].BayID == mem_locs[mem_loc_count - 2].BayID)
						mem_locs[mem_loc_count-1].adjacent_dbid2 = mem_locs[mem_loc_count - 2].Loc_dbid;
				}
				else
					mem_locs[mem_loc_count-1].adjacent_dbid2 = mem_locs[mem_loc_count - 2].Loc_dbid;
			}
		}
	}
	
	// handle middle cases
	
	for (int i = 1; i < (mem_loc_count - 1); i++) {
		if (mem_locs[i].Aisle_dbid == mem_locs[i+1].Aisle_dbid) {
			if (mem_locs[i].Side_dbid == mem_locs[i+1].Side_dbid) {
				if (mem_locs[i].RelLev == mem_locs[i+1].RelLev) {
					if (mem_locs[i].baySpanAllowed == 0) {
						if (mem_locs[i].BayID == mem_locs[i+1].BayID)
							mem_locs[i].adjacent_dbid1 = mem_locs[i+1].Loc_dbid;
					}
					else
						mem_locs[i].adjacent_dbid1 = mem_locs[i+1].Loc_dbid;
				}
			}
		}
		
		if (mem_locs[i].Aisle_dbid == mem_locs[i-1].Aisle_dbid) {
			if (mem_locs[i].Side_dbid == mem_locs[i-1].Side_dbid) {
				if (mem_locs[i].RelLev == mem_locs[i-1].RelLev) {
					if (mem_locs[i].baySpanAllowed == 0) {
						if (mem_locs[i].BayID == mem_locs[i-1].BayID)
							mem_locs[i].adjacent_dbid2 = mem_locs[i-1].Loc_dbid;
					}
					else
						mem_locs[i].adjacent_dbid2 = mem_locs[i-1].Loc_dbid;
				}
			}
		}	
	}
}


void Pass4Process::FindAdjacentFacings(vector<int> &results, int prodIdx)
{
	// this function will find "numToFind" or less # of adjacent open facings.
	int breakOut;
	int numFound;
	int numToFind;
	int OrigIdx;
	int ProdGroupDBID;
	int CurIdx, NextIdx;
	vector<int> tempList;
	
	numFound = 0;
	
	ProdGroupDBID = mem_prods[prodIdx].prodGroupDBID;
	
	results.clear();
	
	for (int i = 0; i < open_facings.size(); i++) {
		
		OrigIdx = open_facings[i];
		
		// look for this # of bays in this bay profile type
		for (int p = 0; p < mem_prods[prodIdx].facingsInfo.bayProfID.size(); p++) {
			if (mem_locs[OrigIdx].BayProfID == mem_prods[prodIdx].facingsInfo.bayProfID[p]) {
				if (mem_locs[OrigIdx].BayType == mem_prods[prodIdx].facingsInfo.bayType[p]) {
					numToFind = mem_prods[prodIdx].facingsInfo.facingsneeded[p];
					break;
				}
			}
		}
		
		if (numToFind > maxFacingsThisRun) {
			numToFind = maxFacingsThisRun;
		}

		//fprintf(stdout, "In FAF: Finding %d facings!\n", numToFind);
		
		if ((mem_locs[OrigIdx].Assigned != 0) || 
			mem_locs[OrigIdx].prevProdIdx >= 0 || mem_locs[OrigIdx].newProdIdx >= 0 || 
			mem_locs[OrigIdx].prodGroupDBID != ProdGroupDBID || numFound >= numToFind ||
			(MaxCaseFit(&mem_prods[prodIdx], &mem_locs[OrigIdx]) <= 0))
			continue;
		
		// clear our placeholder list until we know if we found more than 1 open loc.. 
		tempList.clear();
		
		tempList.push_back(OrigIdx);	
		
		if (numToFind == 1) {
			results.clear();
			for (int p = 0; p < tempList.size(); p++)
				results.push_back(tempList[p]);
			break;
		}
		
		breakOut = 0;	
		
		CurIdx = OrigIdx;
		
		while (breakOut == 0) {		
			//look at the first adjacent loc (if any)	
			if (mem_locs[CurIdx].adjacent_dbid1 > 0)
				NextIdx = FindLoc(mem_locs[CurIdx].adjacent_dbid1);
			else
				break;
			
			if (mem_locs[NextIdx].Assigned == 0 && 
				mem_locs[NextIdx].prevProdIdx <= 0 && mem_locs[NextIdx].newProdIdx <= 0 &&
				mem_locs[NextIdx].prodGroupDBID == ProdGroupDBID && tempList.size() < numToFind
				&& (MaxCaseFit(&mem_prods[prodIdx], &mem_locs[NextIdx]) > 0)) {
				tempList.push_back(NextIdx);
			}
			else
				breakOut = 1;
			CurIdx = NextIdx;
		}
		
		// if we found a list bigger than the current list, replace it.
		if (tempList.size() > numFound) {
			results.clear();
			for (int p = 0; p < tempList.size(); p++)
				results.push_back(tempList[p]);
			numFound = tempList.size();
		}
		
		if (numFound >= numToFind)
			break;
		
		tempList.clear();
		
		tempList.push_back(OrigIdx);
		
		breakOut = 0;	
		
		CurIdx = OrigIdx;
		
		while (breakOut == 0) {		
			//look at the first adjacent loc (if any)	
			if (mem_locs[CurIdx].adjacent_dbid2 > 0)
				NextIdx = FindLoc(mem_locs[CurIdx].adjacent_dbid2);
			else
				break;
			
			if (mem_locs[NextIdx].Assigned == 0 && 
				mem_locs[NextIdx].prevProdIdx <= 0 && mem_locs[NextIdx].newProdIdx <= 0 &&
				mem_locs[NextIdx].prodGroupDBID == ProdGroupDBID && tempList.size() < numToFind
				&& (MaxCaseFit(&mem_prods[prodIdx], &mem_locs[NextIdx]) > 0)) {
				tempList.push_back(NextIdx);		
			}
			else
				breakOut = 1;
			
			CurIdx = NextIdx;
		}
		
		// if we found a list bigger than the current list, replace it.
		if (tempList.size() > numFound) {
			results.clear();
			for (int p = 0; p < tempList.size(); p++)
				results.push_back(tempList[p]);
			numFound = results.size();
		}
		
		if (numFound >= numToFind)
			break;
		
	}
}

void Pass4Process::GetSlottingGroupData()
{
	// This function populates the sgInfo global.  
	string sql;
	vector<string> results;
	vector<string> parsedValues;
	slotGroupInfo tempSG;
	int i;
	
	//Build our SQL
	
	sql.assign("select DBSLOTTINGGROUPID, PERCENTOPENLOCS, SUGGESTEDSECTIONID ");
	sql.append("from dbslottinggroup ");
	
	//Execute SQL
	
	ExecuteP4Query(sql, results);
	
	//Parse results
	
	for(i = 0; i < results.size(); i++) {
		utilityHelper.ParseString(results[i], "|", parsedValues);
		tempSG.slottingGroupDBID = atoi(parsedValues[0].c_str());
		tempSG.percentOpen = atoi(parsedValues[1].c_str());
		tempSG.lookOutsideConstraints = atoi(parsedValues[2].c_str());
		sgInfo.push_back(tempSG);
	}
}

int Pass4Process::AssignLocation(int locIndex, int prodIndex)
{
	
	int bayIdx, levelIdx, caseCount;
	
	caseCount = MaxCaseFit(&mem_prods[prodIndex], &mem_locs[locIndex]);
	
	// add the weight to the bay and level
	bayIdx = FindBay(mem_locs[locIndex].BayID);
	if (bayIdx >= 0) {
		mem_bay_weights[bayIdx].currentBayWeight += 
			(caseCount)
			* mem_prods[prodIndex].caseWeight;
	}
				
	levelIdx = FindLevel(mem_locs[locIndex].Level_dbid);
	if (levelIdx >= 0) {
		mem_level_weights[levelIdx].currentLevelWeight += 
			(caseCount)
			* mem_prods[prodIndex].caseWeight;
	}
				
	mem_locs[locIndex].newProdIdx = prodIndex;
	mem_prods[prodIndex].newLocIdx = locIndex;
				
	mem_locs[locIndex].Assigned = 1;	
				
	return 0;
}

int Pass4Process::CalcOpenPercentage(int totalFacings, int openFacings, int percentOpen, int numProds)
{
	
	double tempdub;
	int criticalNumber;
	
	tempdub = (double) percentOpen / 100;
	
	criticalNumber = (int) totalFacings * tempdub;
	
	if ((openFacings - numProds) < criticalNumber)  {
		return 0;
	}
	
	return 1;
	
}

int Pass4Process::isReducible(int locIndex)
{
	
	for (int i = 0; i < reduceable_facings.size(); i++) {
		if (reduceable_facings[i] = locIndex)
			return 1;
	}
	
	return 0;
}


void Pass4Process::ProcessTacticalResults()
{
	
	int prodIndex;
	int locIndex;
	int caseCount;
	char costbuf[COST_BUFFER_SIZE];
	double tmpCost;
	double prevCost;
	
	for (int i = 0; i < mem_prod_count; i++) {
		mem_prods[i].costRowAdded = 0;
	}
	
	for (i = 0; i < mem_loc_count; i++) {
		
		prodIndex = mem_locs[i].newProdIdx;
		
		if (prodIndex < 0)
			continue;
		
		locIndex = i;
		
		mem_prods[prodIndex].facings = GetNewFacingCount(prodIndex);
		mem_prods[prodIndex].newLocIdx = i;
		
		caseCount = MaxCaseFit(&mem_prods[prodIndex], &mem_locs[locIndex]);
		
		tmpCost = GetCost(prodIndex, locIndex, caseCount, costbuf);
		if(tmpCost < 0.00001f)
			tmpCost = 0.0f;
		
		if (mem_prods[prodIndex].prevLocIdx > 0) {
			caseCount = MaxCaseFit(&mem_prods[prodIndex], &mem_locs[mem_prods[prodIndex].prevLocIdx]);
			
			prevCost = GetCost(prodIndex, mem_prods[prodIndex].prevLocIdx, caseCount, costbuf);
			if(prevCost < 0.00001f)
				prevCost = 0.0f;
			
		} else {
			prevCost = 0.0f;
		}
		
		// adds an assignment
		AddResult(prodIndex, locIndex, tmpCost, caseCount, costbuf);
		
		tmpCost = tmpCost * mem_prods[prodIndex].facings;
		
		
		prevCost = prevCost * mem_prods[prodIndex].prevFacingCount;
		
		if (mem_prods[prodIndex].costRowAdded == 0) {
			TacticalAddInfo(prodIndex, prodIndex, prevCost, tmpCost, 0);
			mem_prods[prodIndex].costRowAdded = 1;
		}
		
		/* ******************************************************* */
		/* Print a successful message to the log file              */
		/* ******************************************************* */
		if ( debugLog || mem_prods[prodIndex].trace) {
			sprintf(msg_buf,"Product %20.20s Assigned to Location %d, %20.20s\t%W-%10.4f L-%10.4f H-%10.4f\tBayProfID= %d\n",
				mem_prods[prodIndex].WMSProdID, mem_locs[locIndex].Loc_dbid, mem_locs[locIndex].Loc_desc, 
				mem_locs[locIndex].Loc_w, mem_locs[locIndex].Loc_d,
				mem_locs[locIndex].Loc_h, mem_locs[locIndex].BayProfID);
			fprintf(p4TraceFile,"%s", msg_buf);
		}
	}
}

void Pass4Process::FindAdjacentOpenFacingsSpecificLocation(vector<int> &results, int prodIndex)
{
	
	int breakOut;
	int numFound;
	int numToFind;
	int OrigIdx;
	int ProdGroupDBID;
	int CurIdx, NextIdx;
	vector<int> tempList;
	
	numFound = 0;
	
	results.clear();
	
	OrigIdx = mem_prods[prodIndex].prevLocIdx;
	
	ProdGroupDBID = mem_prods[prodIndex].prodGroupDBID;
	
	for (int i = 0; i < mem_prods[prodIndex].facingsInfo.bayProfID.size(); i++) {
		if (mem_prods[prodIndex].bayProfID = mem_prods[prodIndex].facingsInfo.bayProfID[i]) {
			numToFind = mem_prods[prodIndex].facingsInfo.facingsneeded[i];
			break;
		}
	} 
	
	//fprintf(stdout, "After getting numtofind..\n");
	
	
	// clear our placeholder list until we know if we found more than 1 open loc.. 
	tempList.clear();
	
	tempList.push_back(OrigIdx);
	
	//fprintf(stdout, "After pushing onto the templist..\n");
	
	breakOut = 0;	
	
	CurIdx = OrigIdx;
	
	while (breakOut == 0) {		
		//look at the first adjacent loc (if any)	
		if (mem_locs[CurIdx].adjacent_dbid1 > 0)
			NextIdx = FindLoc(mem_locs[CurIdx].adjacent_dbid1);
		else
			break;
		
		if (mem_locs[NextIdx].Assigned == 0 &&
			(mem_locs[NextIdx].prevProdIdx < 0 || mem_locs[NextIdx].prevProdIdx == prodIndex) &&
			mem_locs[NextIdx].newProdIdx < 0 &&
			mem_locs[NextIdx].prodGroupDBID == ProdGroupDBID &&
			tempList.size() < numToFind &&
			(MaxCaseFit(&mem_prods[prodIndex], &mem_locs[NextIdx]) > 0))  {
			tempList.push_back(NextIdx);
			//fprintf(stdout, "pushing %d onto the templist!\n", NextIdx);
		}
		else
			breakOut = 1;
		
		CurIdx = NextIdx;
	}
	
	// push the results into the results vector, even if we only found the original location
	
	results.clear();
	for (int p = 0; p < tempList.size(); p++)
		results.push_back(tempList[p]);
	numFound = tempList.size();
	
	//fprintf(stdout, "results.size = %d.\n", results.size());
	
	if (numFound >= numToFind)
		return;
	
	tempList.clear();
	
	//fprintf(stdout, "cleared templist\n");
	
	breakOut = 0;	
	
	CurIdx = OrigIdx;
	
	while (breakOut == 0) {		
		//look at the first adjacent loc (if any)	
		if (mem_locs[CurIdx].adjacent_dbid2 > 0)
			NextIdx = FindLoc(mem_locs[CurIdx].adjacent_dbid2);
		else
			break;
		
		if (mem_locs[NextIdx].Assigned == 0 &&
			(mem_locs[NextIdx].prevProdIdx < 0 || mem_locs[NextIdx].prevProdIdx == prodIndex) &&
			mem_locs[NextIdx].newProdIdx < 0 &&
			mem_locs[NextIdx].prodGroupDBID == ProdGroupDBID &&
			tempList.size() < numToFind &&
			(MaxCaseFit(&mem_prods[prodIndex], &mem_locs[NextIdx]) > 0)) {
			tempList.push_back(NextIdx);		
			//fprintf(stdout, "pushing %d onto the templist!\n", NextIdx);
		}
		else
			breakOut = 1;
		
		CurIdx = NextIdx;
	}
	
	
	for (p = 0; p < tempList.size(); p++)
		results.push_back(tempList[p]);
	
	//fprintf(stdout, "results.size = %d.\n", results.size());
}

void Pass4Process::ProdCopy(p4ProdPack *tempProd, p4ProdPack *prod)
{
	// copies a product into a new p4ProdPack structure which has already been allocated
	
	tempProd->dbID = prod->dbID;
	strcpy(tempProd->desc,prod->desc);
	tempProd->height = prod->height;
	tempProd->width = prod->width;
	tempProd->length = prod->length;
	tempProd->movement = prod->movement;
	tempProd->facings = prod->facings;
	tempProd->bayProfID = prod->bayProfID;
	tempProd->NumInPallet = prod->NumInPallet;
	tempProd->weight = prod->weight;
	tempProd->cube = prod->cube;
	tempProd->containerWidth = prod->containerWidth;
	tempProd->containerLength = prod->containerLength;
	tempProd->containerHeight = prod->containerHeight;
	tempProd->contOverrideHeight = prod->contOverrideHeight;
	tempProd->productHi = prod->productHi;
	tempProd->unitOfIssue = prod->unitOfIssue;
	tempProd->expandedFacings = prod->expandedFacings;
	tempProd->reducedFacings = prod->reducedFacings;
	tempProd->rotateXAxis = prod->rotateXAxis;
	tempProd->rotateYAxis = prod->rotateYAxis;
	tempProd->rotateZAxis = prod->rotateZAxis;
	tempProd->ranking = prod->ranking;
	tempProd->levelType = prod->levelType;
	tempProd->rotatedHeight = prod->rotatedHeight;
	tempProd->rotatedWidth = prod->rotatedWidth;
	tempProd->rotatedLength = prod->rotatedLength;
	tempProd->ChangedInPass = prod->ChangedInPass;
	tempProd->usable = prod->usable;
	tempProd->prioritizeNum = prod->prioritizeNum;
	strcpy(tempProd->prioritizeChar,prod->prioritizeChar);
	tempProd->productTi = prod->productTi;
	tempProd->innerWidth = prod->innerWidth;
	tempProd->innerLength = prod->innerLength;
	tempProd->innerHeight = prod->innerHeight;
	tempProd->innerWeight = prod->innerWeight;
	tempProd->eachWidth = prod->eachWidth;
	tempProd->eachLength = prod->eachLength;
	tempProd->eachHeight = prod->eachHeight;
	tempProd->eachWeight = prod->eachWeight;
	tempProd->caseWidth = prod->caseWidth;
	tempProd->caseLength = prod->caseLength;
	tempProd->caseHeight = prod->caseHeight;
	tempProd->caseWeight = prod->caseWeight;
	tempProd->caseMovement = prod->caseMovement;
	tempProd->palletMovement = prod->palletMovement;
	tempProd->palletWeight = prod->palletWeight;
	tempProd->casePack = prod->casePack;
	tempProd->caseCube = prod->caseCube;
	tempProd->innerPack = prod->innerPack;
	strcpy(tempProd->WMSProdID,prod->WMSProdID);
	strcpy(tempProd->WMSProdDetID,prod->WMSProdDetID);
	tempProd->newLocIdx = prod->newLocIdx;
	tempProd->prevLocIdx = prod->prevLocIdx;
	tempProd->prevFacingCount = prod->prevFacingCount;
	tempProd->prodGroupDBID = prod->prodGroupDBID;
	strcpy(tempProd->prodGroupDesc,prod->prodGroupDesc);
	tempProd->fromCost = prod->fromCost;
	tempProd->toCost = prod->toCost;
	tempProd->moveCost = prod->moveCost;
	tempProd->Assigned = prod->Assigned;
	tempProd->currentAssignedRanking = prod->currentAssignedRanking;
	tempProd->nestWidth = prod->nestWidth;
	tempProd->nestLength = prod->nestLength;
	tempProd->nestHeight = prod->nestHeight;
	strcpy(tempProd->lastOptimizeAttribute,prod->lastOptimizeAttribute);
	tempProd->trace = prod->trace;
	for (int i = 0; i < tempProd->facingsInfo.bayProfID.size(); i++)
		tempProd->facingsInfo.bayProfID.push_back(prod->facingsInfo.bayProfID[i]);
	for (i = 0; i < tempProd->facingsInfo.bayType.size(); i++)
		tempProd->facingsInfo.bayType.push_back(prod->facingsInfo.bayType[i]);
	for (i = 0; i < tempProd->facingsInfo.facingsneeded.size(); i++)
		tempProd->facingsInfo.facingsneeded.push_back(prod->facingsInfo.facingsneeded[i]);
	for (i = 0; i < tempProd->facingList.size(); i++)
		tempProd->facingList.push_back(prod->facingList[i]);
}

void Pass4Process::UnassignProduct(int prodIndex)
{
	for (int i = 0; i < mem_loc_count; i++) {
		if (mem_locs[i].prevProdIdx == prodIndex) 
			mem_locs[i].prevProdIdx = -1;
	}
}

int Pass4Process::GetNewFacingCount(int prodIndex)
{
	int count = 0;
	int locIdx = 0;
	
	for (locIdx = 0; locIdx < mem_loc_count; locIdx++) {
		if (mem_locs[locIdx].newProdIdx == prodIndex) {
			mem_prods[prodIndex].newLocIdx = locIdx;
			count++;
		}
	}
	
	return count;
}

int Pass4Process::TacticalGetExistingFacing(int prodIndex, int facingNumber)
{
	
	return mem_prods[prodIndex].facingList[facingNumber];
	
}

void Pass4Process::PopulateFacingLists()
{
	int locIdx;
	
	for (locIdx = 0; locIdx < mem_loc_count; locIdx++) {
		if (mem_locs[locIdx].prevProdIdx >= 0) 
			mem_prods[mem_locs[locIdx].prevProdIdx].facingList.push_back(locIdx);
	}
}

void Pass4Process::SlotUnslottedProducts()
{
	int slotMode, groupInfoIdx, locIndex, caseCount;
	vector<int> facingsList;
	
	
	for (int l = 0; l < mem_unslotted_products.size(); l++) {
		
		if (mem_prods[mem_unslotted_products[l]].prodGroupDBID < 0)
			continue;
		
		slotMode = 1; // default to single-facing in case there is an error
		maxFacingsThisRun = 1;  // default to 1 facing
		
		//fprintf(stdout, "Calculating slotmode..\n");
		
		for (groupInfoIdx = 0; groupInfoIdx < groupInfo.size(); groupInfoIdx++) {
			if (groupInfo[groupInfoIdx].prodGroupId == mem_prods[mem_unslotted_products[l]].prodGroupDBID) {
				if (groupInfo[groupInfoIdx].numProds >= groupInfo[groupInfoIdx].numOpenFacings){
					slotMode = 1; // single-facing mode
					break;
				}
				else {
					slotMode = 0; // multi-facing mode
					maxFacingsThisRun = (groupInfo[groupInfoIdx].numOpenFacings - groupInfo[groupInfoIdx].numProds + 1);
					break;
				}
			}
		}
		
		//fprintf(stdout, "checking open location percentage..Slotmode = %d\n", slotMode);
		
		if (CalcOpenPercentage(groupInfo[groupInfoIdx].numTotalFacings, groupInfo[groupInfoIdx].numOpenFacings,
			groupInfo[groupInfoIdx].percentOpen, groupInfo[groupInfoIdx].numProds) == 0) {
			slotMode = 1;  // below open percentage, only give 1 facing
		}
		
		//fprintf(stdout, "setting number of required facings..\n");
		
		if (slotMode == 1)
			maxFacingsThisRun = 1;
		
		int prodIndex = mem_unslotted_products[l];

		if (maxFacingsThisRun < 1)
			maxFacingsThisRun = 1;

		facingsList.clear();
		
		// Find Facings in our Product Group
		//fprintf(stdout, "Finding %d facings for product %d!\n", maxFacingsThisRun, mem_prods[prodIndex].dbID);
		FindAdjacentFacings(facingsList, prodIndex);
		
		if (facingsList.size() == 0)
			// no facings available
			continue;
		
		//update group info
		groupInfo[groupInfoIdx].numOpenFacings -= facingsList.size();
		groupInfo[groupInfoIdx].numProds--;
		
		//fprintf(stdout, "Assigning %d facings..\n", facingsList.size());
		
		for (int i = 0; i < facingsList.size(); i++) {
			// cycle through each open facing, assigning products as we go.
			
			// Assign Prods to Standard Locations
			
			locIndex = facingsList[i];
			prodIndex = mem_unslotted_products[l];
			
			caseCount = MaxCaseFit(&mem_prods[prodIndex], &mem_locs[locIndex]);		
			if (caseCount == 0)
				// product won't fit in location - skip it
				continue;
			
			//fprintf(stdout, "Assigning facing..\n");
			AssignLocation(locIndex, prodIndex);
			mem_prods[mem_unslotted_products[l]].Assigned = 1;
		}	
	}
}

void Pass4Process::ReslotProducts()
{
	
	vector<int> facingsList;
	vector<int> tmpFacings;
	int i;
	
	fprintf(stdout, "Looking through products that need to be reslotted.\n");
	for (int l = (mem_prods_to_reslot.size() - 1); l >= 0; l--) {	
		if (mem_prods[mem_prods_to_reslot[l]].Assigned == 1)
			continue;
		// try to expand our current location to open/reducible locations beside it
		tmpFacings.clear();
		FindAdjacentOpenFacingsSpecificLocation(tmpFacings, mem_prods_to_reslot[l]);
		if (facingsList.size() < tmpFacings.size()) {
			facingsList.clear();
			for (i = 0; i < tmpFacings.size(); i++) {
				facingsList.push_back(tmpFacings[i]);
			}
		}
		
		// try to find other locations to slot the product
		tmpFacings.clear();
		FindAdjacentFacings(tmpFacings, mem_prods_to_reslot[l]);
		if (facingsList.size() < tmpFacings.size()) {
			facingsList.clear();
			for (i = 0; i < tmpFacings.size(); i++) {
				facingsList.push_back(tmpFacings[i]);
			}
		}
		
		// assign the larger group of facings we found, if any
		if (facingsList.size() > 0) {
			mem_prods[mem_prods_to_reslot[l]].Assigned = 1;
			UnassignProduct(mem_prods_to_reslot[l]);
			for (i = 0; i < facingsList.size(); i++)
				AssignLocation(facingsList[i], mem_prods_to_reslot[l]);
		}
	}
	
}

void Pass4Process::FindSwaps()
{

	// look at each product
	for (int p = 0; p < mem_prod_count; p++) {
		for (int p2 = 0; p < mem_prod_count; p++) {
			if (mem_prods[p].prodGroupDBID = mem_prods[p2].prodGroupDBID) {
				if (checkSwap(p, p2) == 1) {
					fprintf(stdout, "executing a swap!\n");
					ExecuteSwap(p, p2);
				}
			}
		}
	}
}

void Pass4Process::ClearAssignments()
{
	
	int i;
	
	for(i = 0; i < mem_loc_count; i++)
		mem_locs[i].newProdIdx = -1;
	
	for (i = 0; i < open_facings.size(); i++)
		mem_locs[open_facings[i]].Assigned = 0;
	
	for (i = 0; i < mem_unslotted_products.size(); i++)
		mem_prods[mem_unslotted_products[i]].Assigned = 0;
	
}

void Pass4Process::PopulatePGroupInfo()
{
	
	// build product group info so we know which slotMode to use
	int found, i;
	groupInfostruct tmpgroupInfo;
	
	// find number of open facings in each product group
	for (i = 0; i < open_facings.size(); i++) {
		// find the record, if it exists
		found = 0;
		for (int j = 0; j < groupInfo.size(); j++) {
			if ((groupInfo.size() > 0) && (groupInfo[j].prodGroupId == mem_locs[open_facings[i]].prodGroupDBID)) {
				// we found the record.  update the count
				groupInfo[j].numOpenFacings++;
				found = 1;
			}	
		}
		if (found == 0) {// we need to insert a new record.
			tmpgroupInfo.prodGroupId = mem_locs[open_facings[i]].prodGroupDBID;
			tmpgroupInfo.numOpenFacings = 1;
			tmpgroupInfo.numProds = 0;
			tmpgroupInfo.numTotalFacings = 0;
			// add the info we got from the dbslottinggroup table
			for (int k = 0; k < sgInfo.size(); k++) {
				if (sgInfo[k].slottingGroupDBID == tmpgroupInfo.prodGroupId) {
					tmpgroupInfo.lookOutsideConstraints = sgInfo[k].lookOutsideConstraints;
					tmpgroupInfo.percentOpen = sgInfo[k].percentOpen;
				}
			}
			groupInfo.push_back(tmpgroupInfo);
		}
	}
	
	fprintf(stdout, "found numbers of open facings in product groups.\n");
	
	// find total number of facings in each product group
	for (i = 0; i < mem_loc_count; i++) {
		found = 0;
		for (int j = 0; j < groupInfo.size(); j++) {
			if ((groupInfo.size() > 0) && (groupInfo[j].prodGroupId == mem_locs[i].prodGroupDBID)) {
				// we found the record.  update the count
				groupInfo[j].numTotalFacings++;
				found = 1;
			}	
		}
		if (found == 0) {// we need to insert a new record.
			tmpgroupInfo.prodGroupId = mem_locs[i].prodGroupDBID;
			tmpgroupInfo.numTotalFacings = 1;
			tmpgroupInfo.numOpenFacings = 0;
			tmpgroupInfo.numProds = 0;
			// add the info we got from the dbslottinggroup table
			for (int k = 0; k < sgInfo.size(); k++) {
				if (sgInfo[k].slottingGroupDBID == tmpgroupInfo.prodGroupId) {
					tmpgroupInfo.lookOutsideConstraints = sgInfo[k].lookOutsideConstraints;
					tmpgroupInfo.percentOpen = sgInfo[k].percentOpen;
				}
			}
			groupInfo.push_back(tmpgroupInfo);
		}
	}
	
	// find number of products in each product group
	for (i = 0; i < mem_unslotted_products.size(); i++) {
		// find the record, if it exists
		found = 0;
		for (int j = 0; j < groupInfo.size(); j++) {
			if ((groupInfo.size() > 0) && (groupInfo[j].prodGroupId == mem_prods[mem_unslotted_products[i]].prodGroupDBID)) {
				// we found the record.  update the count
				groupInfo[j].numProds++;
				found = 1;
			}	
		}
		if (found == 0) {// we need to insert a new record.
			tmpgroupInfo.prodGroupId = mem_prods[mem_unslotted_products[i]].prodGroupDBID;
			tmpgroupInfo.numProds = 1;
			// add the info we got from the dbslottinggroup table
			for (int k = 0; k < sgInfo.size(); k++) {
				if (sgInfo[k].slottingGroupDBID == tmpgroupInfo.prodGroupId) {
					tmpgroupInfo.lookOutsideConstraints = sgInfo[k].lookOutsideConstraints;
					tmpgroupInfo.percentOpen = sgInfo[k].percentOpen;
				}
			}
			tmpgroupInfo.numTotalFacings = 0;
			tmpgroupInfo.numOpenFacings = 0;
			groupInfo.push_back(tmpgroupInfo);
		}
	}
	
}

void Pass4Process::CategorizeProducts()
{
	
	int prodIdx, currentFacings, numRequiredFacingsCurrentBayType, j;
	
	
	for(prodIdx=0;prodIdx<mem_prod_count;prodIdx++){
		
		// if product has a current location, figure number of required facings
		
		currentFacings = TacticalGetExistingFacingCount(prodIdx);

		fprintf(stdout, "product = %d.  Currentfacings = %d.\n", mem_prods[prodIdx].dbID, currentFacings);
		
		mem_prods[prodIdx].prevFacingCount = currentFacings;
		
		TacticalFacings(mem_prods[prodIdx]); 
		
		if (currentFacings == 0) {  // new product, currentFacings is 0
			mem_unslotted_products.push_back(prodIdx);
			continue;
		}
		
		if (mem_prods[prodIdx].prodGroupDBID < 0) {  // no product group
			mem_prods_nopgroup.push_back(prodIdx);
			continue;
		}
		
		// Gets number of facings required in current bay type
		
		numRequiredFacingsCurrentBayType = GetRequiredFacingsCurrentBayType(prodIdx);

		fprintf(stdout, "numReq = %d.\n", numRequiredFacingsCurrentBayType);
		
		// Check # of facings we'd like vs. # we have
		
		if (currentFacings == numRequiredFacingsCurrentBayType) // no change in # of facings
			continue; 
		else {  
			if (currentFacings < numRequiredFacingsCurrentBayType) {  // we don't have enough!
				//reslot product - we will reslot only products which are in this array.
				mem_prods_to_reslot.push_back(prodIdx);
			}
			else {  // we have too many.. lets make them available to other products
				// add location to reduceable facings array
				// no need to reduce facings unless it's needed by another product.
				// This array will be used as "available" locations for other products which 
				// need to be re-slotted.. but they won't actually be reduced unless they
				// are slotted to other products.
				
				for (j = 0; j < (currentFacings - numRequiredFacingsCurrentBayType); j++) {
					reduceable_facings.push_back(TacticalGetExistingFacing(prodIdx, j));
				}
				
			}
		}	
	}
	
	// print results
	
	fprintf(stdout, "num_to_reslot: %d.  num_reduceable: %d.  Num ok: %d.\n", mem_prods_to_reslot.size(),
		reduceable_facings.size(), mem_prod_count - (mem_prods_to_reslot.size()+reduceable_facings.size() + mem_unslotted_products.size()));
	fprintf(stdout, "num no pgroup: %d.  num_unslotted: %d.\n", mem_prods_nopgroup.size(), mem_unslotted_products.size());
	
}

void Pass4Process::PopulateOpenFacings()
{
	for (int j = 0; j < mem_loc_count; j++) {
		if (mem_locs[j].prevProdIdx < 0 && mem_locs[j].newProdIdx < 0 && mem_locs[j].isSelect == 1)
			open_facings.push_back(j);
	}
	
	fprintf(stdout, "open locations: %d.\n", open_facings.size());
}

int Pass4Process::TacticalGetExistingFacingCount(int prodIndex)
{
	
	return mem_prods[prodIndex].facingList.size();
	
}

int Pass4Process::GetRequiredFacingsCurrentBayType(int prodIndex)
{
	int firstFacingIdx;
	
	firstFacingIdx = TacticalGetExistingFacing(prodIndex, 0);
	
	if (firstFacingIdx == -1) { // shouldn't return -1, but just in case.. 
		mem_unslotted_products.push_back(prodIndex);
		return 0;
	}
	
	// look through our facingsInfo structure we built up in TacticalFacings()
	
	for (int i = 0; i < mem_prods[prodIndex].facingsInfo.bayProfID.size(); i++) {
		if (mem_locs[firstFacingIdx].BayProfID == mem_prods[prodIndex].facingsInfo.bayProfID[i]) {
			if (mem_locs[firstFacingIdx].BayType == mem_prods[prodIndex].facingsInfo.bayType[i])
				return mem_prods[prodIndex].facingsInfo.facingsneeded[i];
		}
	} 
	
	return 0;
	
}

void Pass4Process::RemoveReducibleAssignments()
{
	reducibleAssignments tmpRA;

	for (int i = 0; i < reduceable_facings.size(); i++) {
		tmpRA.locIdx = reduceable_facings[i];
		tmpRA.prodIdx = mem_locs[reduceable_facings[i]].prevProdIdx;
		redAssignments.push_back(tmpRA);
		mem_locs[reduceable_facings[i]].prevProdIdx = -1;
	}
}

void Pass4Process::RestoreReducibleAssignments()
{
	for (int i = 0; i < redAssignments.size(); i++) {
		if (mem_locs[redAssignments[i].locIdx].newProdIdx < 0)
			mem_locs[redAssignments[i].locIdx].prevProdIdx = redAssignments[i].prodIdx;
	}
}

int Pass4Process::checkSwap(int p, int p2)
{
		return TacticalCheckHurdleRate(p, p2);
}

void Pass4Process::ExecuteSwap(int p, int p2)
{
	int i;
	vector<int> p1_locs, p2_locs;

	for (i = 0; i < mem_loc_count; i++) {
		if (mem_locs[i].newProdIdx == p)
			p1_locs.push_back(i);
		if (mem_locs[i].newProdIdx == p2)
			p2_locs.push_back(i);
	}

	for (i = 0; i < p1_locs.size(); i++) { // both should be the same size
		mem_locs[p1_locs[i]].newProdIdx = p2;
		mem_locs[p2_locs[i]].newProdIdx = p;
	}
}

void Pass4Process::AssignUnmovedProducts()
{
	for (int i = 0; i < mem_loc_count; i++) {
		if (mem_locs[i].newProdIdx < 0)
			mem_locs[i].newProdIdx = mem_locs[i].prevProdIdx;
	}
}
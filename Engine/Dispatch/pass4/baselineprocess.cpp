//////////////////////////////////////////////////////////////////////
// Function Name :	BaselineProcess.cpp
// Classname :		
// Description :	The Facility Baseline process.  Calculates Cost
//					for an existing Facility.
// Date Created :	~9/1/98
// Author : 		sc
//////////////////////////////////////////////////////////////////////
// Inputs :			None
// Outputs :		None
// Explanation :	The Execute function passes no parameters in
//					or out as such.  Instead, Rack <PERSON>, Product
//					and Bay/Level/Location data arrive through a
//					socket connection to the Succeed session module.
/* ******************************************************** */
/* Here is the order of operations:                         */
/*    1) Anounce via the socket that we are ready.          */
/*    2) Receive the data that we need:                     */
/*         List of Products.                                */
/*         List of Bay/Level/Location structures.           */
/*    3) Run through products and locs, calculating the     */
/*       cost of each placement.  Keep a running sum of the */
/*       cost total.                                        */
/*    4) Return the data back to the caller.                */
/* ******************************************************** */
//////////////////////////////////////////////////////////////////////
// See comment in RunComp for Cost Analysis algorithm.
//////////////////////////////////////////////////////////////////////
// Revisions : 
//   November 6, 1998-mfs : Standard header comments added
//////////////////////////////////////////////////////////////////////
/*******************************************************
   *
   *                       NOTICE
   *
   *  THIS SOFTWARE IS THE PROPERTY OF AND CONTAINS
   *  CONFIDENTIAL INFORMATION OF SSA GLOBAL 
   *  TECHNOLOGIES, INC., AND SHALL NOT BE COPIED,
   *  USED, NOR DISCLOSED WITHOUT EXPRESS WRITTEN
   *  AUTHORIZATION.  ALTHOUGH PUBLICATION IS NOT
   *  INTENDED, IN THE EVENT OF PUBLICATION, THE
   *  FOLLOWING NOTICE IS APPLICABLE:
   *
   *  (c) COPYRIGHT 2004 SSA GLOBAL TECHNOLOGIES, INC.
   *
   *           SSA GLOBAL TECHNOLOGIES, INC.
   *
   ********************************************************/
//////////////////////////////////////////////////////////////////////

#include <stdlib.h>
#include <stdio.h>
#include <math.h>
#include <direct.h>
#include <errno.h>

#include "BaselineProcess.h"
#include "..\..\..\common\core\debug.h"
#include "..\..\..\common\core\exceptions.h"

#include "..\DataStream.h"



double inches_to_foot = 12.0f;
int LevelLaborSortBase( const void *p1, const void *p2 );

FILE *dummyb_file;
#define BUFSIZE 2048
int origin;

BaselineProcess::BaselineProcess(SockClass *sock)
{
	/* ******************************************************** */
	/* This is the socket that we will use to get our data.     */
	/* ******************************************************** */
	BaselineSock = sock;
	
	mem_loc_count = 0;

}

BaselineProcess::~BaselineProcess()
{
	/* ******************************************************** */
	/* Make sure that we clean up the memory and objects that   */
	/* we have used.  Clean up the socket as well.              */
	/* ******************************************************** */

	//printf("In the Baseline destructor\n");

	// Temporary hack, give the client time to finish reading from the socket before
	// deleting; need to add some kind of handshaking at end when everything is read
	Sleep(5000);
	delete BaselineSock;
	//printf("Deleted the socket\n");

	if(mem_section_count > 0){
		free(mem_sections);
		//printf("Freed the sections\n");
	}

//	if(mem_labor_count > 0){
//		free(mem_labor);
//		printf("Freed the mem labor\n");
//	}
	if(mem_labor_select_count > 0){
		free(mem_labor_select);
	}


	if(mem_labor_stocker_count > 0){
		free(mem_labor_stocker);
	}
	//printf("done with the Baseline destructor\n");

}

void BaselineProcess::Execute(void)
{
	char buffer[BUFSIZE];
	unsigned int  err;
	double CostSum;

	/* ******************************************************** */
	/* Here is the order of operations:                         */
	/*    1) Anounce via the socket that we are ready.          */
	/*    2) Receive the data that we need:                     */
	/*         List of Products.                                */
	/*         List of Bay/Level/Location structures.           */
	/*    3) Run through products and locs, calculating the     */
	/*       cost of each placement.  Keep a running sum of the */
	/*       cost total.                                        */
	/*    4) Return the data back to the caller.                */
	/* ******************************************************** */

	/* ******************************************************** */
	/* Announcement to the caller that we are ready.            */
	/* ******************************************************** */

	CostSum = 0;

	fprintf(stdout, "Starting Baseline Cost Analysis.\n");

	char fileName[256];

	// Look up window registry to find the default first
	HKEY hRegKey;
	logPath[0] = 0;
	DWORD dwType = REG_SZ;
	DWORD dwReturnLength;

	if (RegOpenKeyEx(HKEY_LOCAL_MACHINE, "Software\\SSA Global\\Optimize", 0, KEY_READ, &hRegKey) == ERROR_SUCCESS) {
	int i = 0;
	while ((RegQueryValueEx(hRegKey, "LogFilePath", NULL, &dwType,(LPBYTE)logPath, &dwReturnLength) != ERROR_SUCCESS ) && i<=100)
			i++;
	if (i>100)
		logPath[0] = 0;
	}
	RegCloseKey(hRegKey);


	if ( logPath[0] == 0 )
		strcpy(logPath, ".\\Log");					// MFS 2Mar06 Value missing, default to \Optimize\Log

	if (_mkdir(logPath) != 0) {						// MFS 2Mar06 Try to create/open directory
		if (errno != EEXIST) {						// If that doesn't work...
			if (_stricmp(logPath, ".\\Log")==0)		// Try default unless we already tried it.
				strcpy(logPath, ".");				// Use current dir. as final fallback.
			else
				strcpy(logPath, ".\\Log");
		}
	}
	
	/*
	strcpy(fileName, logPath);
	strcat(fileName, "BaselineCA.out");
	dummyb_file = fopen(fileName, "w");
	if (dummyb_file == NULL)
		fprintf(stdout, "Cannot create file: %s.  Check disk space on device.\n", fileName);
	else {
		char fullPath[256];
		if (_fullpath(fullPath, fileName, 256) != NULL)
			fprintf(stdout, "Baseline Cost Analysis input file: %s\n", fullPath);
		else
			fprintf(stdout, "Baseline Cost Analysis input file: %s\n",fileName);
	}
	*/


	/// We don't use socket any more... No need to handshake...
	//memset(buffer, 0, BUFSIZE);
	///sprintf(buffer,"Ready for data...\n<EOS>\n");

	/***>>>
	err = BaselineSock->SendData(buffer, strlen(buffer));
	<<<***/

	///gfnGetDataStream()->ssData << buffer;

	/***>>>
	if(err < strlen(buffer))
		throw EngineException("Error Sending AOK Signal",
			__FILE__, __LINE__, 200);
	<<<***/
	
	try {
		GetInitialization();

		/* ************************************************ */
		/* Receive data that will drive this calculation    */
		/* ************************************************ */
		GetProducts();
		
		fileName[0]=0;
		strcpy(fileName, logPath);
		strcat(fileName, "\\");
		if (origin == 0)
			strcat(fileName, "BaselineCA.out");
		else
			strcat(fileName, "OptimizedCA.out");

		dummyb_file = fopen(fileName, "w");
		if (dummyb_file == NULL)
			fprintf(stdout, "Cannot create file: %s.  Check disk space on device.\n", fileName);
		else {
			char fullPath[256];
			if (_fullpath(fullPath, fileName, 256) != NULL)
				fprintf(stdout, "Cost Analysis input file: %s\n", fullPath);
			else
			fprintf(stdout, "Cost Analysis input file: %s\n",fileName);
	}

		/* ************************************************ */
		/* Do the actual computation.                       */
		/* ************************************************ */
		CostSum = RunComp();

		/* ************************************************ */
		/* Re-initialize all of our variables.              */
		/* ************************************************ */
		if(mem_loc_count > 0)
			free (mem_locs);

	} catch(EngineException ee) {
		fprintf(stdout, "Baseline Cost Analysis failed.\n");

		printf("Caught an exception\n");
		ee.GetAllMessage(buffer);
		printf("%s", buffer);


		if(mem_loc_count > 0)
			free (mem_locs);
		
		memset(buffer, 0, BUFSIZE);
		sprintf(buffer, "Error in Engine\n<EOS>\n");

		/***>>>
		err = BaselineSock->SendData(buffer, strlen(buffer));
		<<<***/
		gfnGetDataStream()->ssData << buffer;

		/***>>>
		if(err < strlen(buffer))
			throw EngineException("Error Sending ERROR Signal",
			 __FILE__, __LINE__, 200);
		<<<***/
		return;
	} catch(...){
		fprintf(stdout, "Baseline Cost Analysis failed.\n");
		if(mem_loc_count > 0)
			free (mem_locs);
		
		printf("Caught an unhandled exception\n");
		memset(buffer, 0, BUFSIZE);
		sprintf(buffer, "Error in Engine\n<EOS>\n");
		/***>>>
		err = BaselineSock->SendData(buffer, strlen(buffer));
		<<<***/
		gfnGetDataStream()->ssData << buffer;
		/***>>>
		if(err < strlen(buffer))
			throw EngineException("Error Sending ERROR Signal",
			 __FILE__, __LINE__, 200);
		<<<***/
		return;
	}

	/* ************************************************ */
	/* Tell the caller what happened.                   */
	/* ************************************************ */
	memset(buffer, 0, BUFSIZE);
	sprintf(buffer, "<ENDOFBASELINE>Successful Baseline|%f|\n<EOS>\n", CostSum);
	//printf("%s", buffer);
	/***>>>
	err = BaselineSock->SendData(buffer, strlen(buffer));
	<<<***/
	gfnGetDataStream()->ssData << buffer;
	/***>>>
	if(err < strlen(buffer))
		throw EngineException("Error Sending Success Signal",
		 __FILE__, __LINE__, 200);
	fprintf(stdout, "Baseline Cost Analysis completed successfully.\n");
	<<<***/
	fclose(dummyb_file);

}

/* **************************************************************** */
/* This function will go through the process of grabbing all of the */
/* initialization information that this process will need.  It will */
/* consist of a list of sections and their information, and then a  */
/* list of racktypelabor information.  Both of these array's will   */
/* later be accessed by the pass4 process to look up information.   */
/* **************************************************************** */
void BaselineProcess::GetInitialization(void)
{
	char buffer[BUFSIZE];
	unsigned int  err;
	int i;

	/* ********************************************* */
	/* Wait for the start of the Section stream      */
	/* ********************************************* */
	do {
		memset(buffer, 0, BUFSIZE);
		if ( gfnGetDataStream()->ssData.getline(buffer, BUFSIZE) == 0) {
		
			throw EngineException("Error receiving on stream",
				__FILE__, __LINE__, 200);
		}
		// printf("%s", buffer);
	} while (strncmp(buffer, "INITIALIZATION_SECTION", 22) != 0);
	
	mem_section_count = atoi(buffer+23);		
	
	/* ********************************************* */
	/* Allocate memory to hold the Sections.         */
	/* ********************************************* */
	mem_sections = (ssaSection *)malloc(mem_section_count * sizeof(ssaSection));
	if(mem_sections == NULL){
		throw EngineException("Error Allocating memory for section list",
		__FILE__, __LINE__, 300);
	}
	
	for(i=0;i<mem_section_count;i++)
		GetOneSection(i);	

	// Set Cube Conversion Factor to Metric or Imperial
	if ( mem_section_count > 0) {
		if ( mem_sections[0].CubeConversion == 1728.0f)
			inches_to_foot = 12.0f;
		else if ( mem_sections[0].CubeConversion == 1000000.0f)
			inches_to_foot = 100.0f;
		else {
				printf("Cube Conversion passed as %f!\n", mem_sections[0].CubeConversion);
				printf("Resetting to default of 12x12x12=1728.\n");
				mem_sections[0].CubeConversion = 1728.0f;
				inches_to_foot = 12.0f;
		}
	} else {
			printf("No Section data sent!\n");
			printf("Setting Cube Conversion to default of 12x12x12=1728\n",
				mem_sections[0].CubeConversion);
		mem_sections[0].CubeConversion = 1728.0f;
		inches_to_foot = 12.0f;
	}

	do {
		memset(buffer, 0, BUFSIZE);
		if ( gfnGetDataStream()->ssData.getline(buffer, BUFSIZE) == 0) {
		
			throw EngineException("Error receiving on stream",
				__FILE__, __LINE__, 200);
		}
	} while (strncmp(buffer, "INITIALIZATION_RACKLABORSELECT", 24) != 0);
	
	mem_labor_select_count = atoi(buffer+31);		
	
	/* ********************************************* */
	/* Allocate memory to hold the RackTypes.        */
	/* ********************************************* */
	mem_labor_select = (ssaLaborLevel *)malloc(mem_labor_select_count * sizeof(ssaLaborLevel));
	if(mem_labor_select == NULL){
		throw EngineException("Error Allocating mem ory for labor list",
		__FILE__, __LINE__, 300);
	}
	
 	for(i=0;i<mem_labor_select_count;i++)
		GetOneLaborSelect(i);	

	qsort(mem_labor_select,mem_labor_select_count, sizeof(ssaLaborLevel), LevelLaborSortBase);


	// Stocker labor values
	/* ********************************************* */
	/* Wait for the start of the RackType stream     */
	/* ********************************************* */
	do {
		memset(buffer, 0, BUFSIZE);
		if ( gfnGetDataStream()->ssData.getline(buffer, BUFSIZE) == 0) {
		
			throw EngineException("Error receiving on stream",
				__FILE__, __LINE__, 200);
		}
	} while (strncmp(buffer, "INITIALIZATION_RACKLABORSTOCKER", 24) != 0);
	
	mem_labor_stocker_count = atoi(buffer+32);		
	
	/* ********************************************* */
	/* Allocate memory to hold the RackTypes.        */
	/* ********************************************* */
	mem_labor_stocker = (ssaLaborLevel *)malloc(mem_labor_stocker_count * sizeof(ssaLaborLevel));
	if(mem_labor_stocker == NULL){
		throw EngineException("Error Allocating memory for labor list",
		__FILE__, __LINE__, 300);
	}
	
	for(i=0;i<mem_labor_stocker_count;i++)
		GetOneLaborStocker(i);	

	qsort(mem_labor_stocker,mem_labor_stocker_count, sizeof(ssaLaborLevel), LevelLaborSortBase);

	
}

void BaselineProcess::GetOneSection(int idx)
{
	char buffer[BUFSIZE];
	unsigned int  err;
	char *ptr;

	/* ******************************************** */
	/* Read the socket information                  */
	/* ******************************************** */
	memset(buffer, 0, BUFSIZE);
	if ( gfnGetDataStream()->ssData.getline(buffer, BUFSIZE) == 0) {
		throw EngineException("Error receiving on stream",
			__FILE__, __LINE__, 200);
	}

	if(strncmp(buffer, "SECTION", 7) != 0)
		throw EngineException("Corrupt data when expecting section", 
			__FILE__, __LINE__, 200);


	ptr = strtok(buffer+8, "|");
	mem_sections[idx].SecID = atoi(ptr);
	ptr = strtok(NULL, "|");
	strcpy(mem_sections[idx].Sec_desc ,ptr);
	ptr = strtok(NULL, "|");
	mem_sections[idx].ForkFxd = (double)atof(ptr);
	ptr = strtok(NULL, "|");
	mem_sections[idx].ForkVar= (double)atof(ptr);
	ptr = strtok(NULL, "|");
	mem_sections[idx].ForkRate = (double)atof(ptr);
	ptr = strtok(NULL, "|");
	mem_sections[idx].SelFxd = (double)atof(ptr);
	ptr = strtok(NULL, "|");
	mem_sections[idx].SelVar = (double)atof(ptr);
	ptr = strtok(NULL, "|");
	mem_sections[idx].SelRate = (double)atof(ptr);
	ptr = strtok(NULL, "|");
	mem_sections[idx].AvgReplenDist = (double)atof(ptr);
	ptr = strtok(NULL, "|");
	mem_sections[idx].TotalMovement = (double)atof(ptr);
	ptr = strtok(NULL, "|");
	mem_sections[idx].ForkHotX = atoi(ptr);
	ptr = strtok(NULL, "|");
	mem_sections[idx].ForkHotY = atoi(ptr);
	ptr = strtok(NULL, "|");
	mem_sections[idx].ForkHotZ = atoi(ptr);
	ptr = strtok(NULL, "|");
	mem_sections[idx].SelHotX = atoi(ptr);
	ptr = strtok(NULL, "|");
	mem_sections[idx].SelHotY = atoi(ptr);
	ptr = strtok(NULL, "|");
	mem_sections[idx].SelHotZ = atoi(ptr);
	ptr = strtok(NULL, "|");
	mem_sections[idx].CubeConversion = (double)atof(ptr);
	ptr = strtok(NULL, "|");
	mem_sections[idx].SelDist = atoi(ptr);
	ptr = strtok(NULL, "|");
	mem_sections[idx].AvgOrdQty = atoi(ptr);
	ptr = strtok(NULL, "|");
	mem_sections[idx].ContainerQty = atoi(ptr);
	ptr = strtok(NULL, "|");
	mem_sections[idx].OrderCount = atoi(ptr);
	ptr = strtok(NULL, "|");
	mem_sections[idx].ApplyBrokenOrder = atoi(ptr);
	ptr = strtok(NULL, "|");
	mem_sections[idx].ave_x = atoi(ptr);
	ptr = strtok(NULL, "|");
	mem_sections[idx].ave_y = atoi(ptr);

	// added 8/21/99 - UOI changes

	ptr = strtok(NULL, "|");
	mem_sections[idx].numPutsPerTrip = (double)atof(ptr);
	ptr = strtok(NULL, "|");
	mem_sections[idx].pickForkTrav = (double)atof(ptr);
	ptr = strtok(NULL, "|");
	mem_sections[idx].insertForkTrav = (double)atof(ptr);
	ptr = strtok(NULL, "|");
	mem_sections[idx].totalExtendedCube = (double)atof(ptr);
	ptr = strtok(NULL, "|");
	mem_sections[idx].stockerRate = (double)atof(ptr);

	if(mem_sections[idx].ContainerQty <= 0)
		mem_sections[idx].ContainerQty = 1;

	//mem_sections[idx].ave_x = 0;
	//mem_sections[idx].ave_y = 0;
	mem_sections[idx].loc_count = 0;
	//printf("Cube Conversion is %f\n",mem_sections[idx].CubeConversion);
}

void BaselineProcess::GetOneLaborSelect(int idx)
{
	char buffer[BUFSIZE];
	unsigned int  err;
	char *ptr;

	/* ******************************************** */
	/* Read the socket information                  */
	/* ******************************************** */
	memset(buffer, 0, BUFSIZE);
	if ( gfnGetDataStream()->ssData.getline(buffer, BUFSIZE) == 0) {
	
		throw EngineException("Error receiving on stream",
			__FILE__, __LINE__, 200);
	}

	if(strncmp(buffer, "RACKLABOR", 9) != 0)
		throw EngineException("Corrupt data when expecting Labor", 
			__FILE__, __LINE__, 200);

	ptr = strtok(buffer+10, "|");
	mem_labor_select[idx].ProfId = atoi(ptr);
	ptr = strtok(NULL, "|");
	mem_labor_select[idx].RTid = atoi(ptr);		// wbh - this is really the levelprofile id
	ptr = strtok(NULL, "|");
	mem_labor_select[idx].RelLev = atoi(ptr);
	ptr = strtok(NULL, "|");
	mem_labor_select[idx].Cube = (double)atof(ptr);
	ptr = strtok(NULL, "|");
	mem_labor_select[idx].Var = (double)atof(ptr);
	ptr = strtok(NULL, "|");
	mem_labor_select[idx].Fxd = (double)atof(ptr);
}

void BaselineProcess::GetOneLaborStocker(int idx)
{
	char buffer[BUFSIZE];
	unsigned int  err;
	char *ptr;

	/* ******************************************** */
	/* Read the socket information                  */
	/* ******************************************** */
	memset(buffer, 0, BUFSIZE);
	if ( gfnGetDataStream()->ssData.getline(buffer, BUFSIZE) == 0) {
	
		throw EngineException("Error receiving on stream",
			__FILE__, __LINE__, 200);
	}

	if(strncmp(buffer, "RACKLABOR", 9) != 0)
		throw EngineException("Corrupt data when expecting Labor", 
			__FILE__, __LINE__, 200);

	ptr = strtok(buffer+10, "|");
	mem_labor_stocker[idx].ProfId = atoi(ptr);
	ptr = strtok(NULL, "|");
	mem_labor_stocker[idx].RTid = atoi(ptr);
	ptr = strtok(NULL, "|");
	mem_labor_stocker[idx].RelLev = atoi(ptr);
	ptr = strtok(NULL, "|");
	mem_labor_stocker[idx].Cube = (double)atof(ptr);
	ptr = strtok(NULL, "|");
	mem_labor_stocker[idx].Var = (double)atof(ptr);
	ptr = strtok(NULL, "|");
	mem_labor_stocker[idx].Fxd = (double)atof(ptr);
}

int BaselineProcess::GetProducts(void)
{
	char buffer[BUFSIZE];
	unsigned int err;
	int i;

	/* ********************************************* */
	/* Wait for the start of the product stream      */
	/* ********************************************* */
	do {
		memset(buffer, 0, BUFSIZE);
		if ( gfnGetDataStream()->ssData.getline(buffer, BUFSIZE) == 0) {
		
			throw EngineException("Error receiving on stream",
				__FILE__, __LINE__, 200);
		}
	} while (strncmp(buffer, "BASELINEPRODS", 13) != 0 && strncmp(buffer, "OPTIMIZEPRODS", 13) != 0);

	if (strncmp(buffer, "BASELINEPRODS", 13) == 0)
		origin = 0;
	else
		origin = 1;

	mem_loc_count = atoi(buffer+14);		
	
	/* ********************************************* */
	/* Allocate memory to hold the products.         */
	/* ********************************************* */
	mem_locs = (baselineProdLoc *)malloc(mem_loc_count * sizeof(baselineProdLoc));
	if(mem_locs == NULL){
		throw EngineException("Error Allocating memory for prod/loc list",
		__FILE__, __LINE__, 300);
	}
	
	for(i=0;i<mem_loc_count;i++)
		GetOneProd(i);	
/*
	for(i=0;i<mem_section_count;i++){
		mem_sections[i].ave_x = (int) ((double)(mem_sections[i].ave_x)/
			(double)(mem_sections[i].loc_count));
		mem_sections[i].ave_y = (int)((double)(mem_sections[i].ave_y)/
			(double)(mem_sections[i].loc_count));
	}
*/
	return mem_loc_count;

}

void BaselineProcess::GetOneProd(int idx)
{
	char buffer[BUFSIZE];
	int  err;
	char *ptr;
	int i;


	/* ******************************************** */
	/* Read the socket information                  */
	/* ******************************************** */
	memset(buffer, 0, BUFSIZE);
	if ( gfnGetDataStream()->ssData.getline(buffer, BUFSIZE) == 0) {
	
		throw EngineException("Error receiving on stream",
			__FILE__, __LINE__, 200);
	}

	if(strncmp(buffer, "PRODLOC", 7) != 0)
		throw EngineException("Corrupt data when expecting prod/loc", 
			__FILE__, __LINE__, 200);
	

	ptr = strtok(buffer+8, "|");
	mem_locs[idx].SolutionID = (int)atoi(ptr);
	ptr = strtok(NULL, "|");
	strcpy(mem_locs[idx].ProdDesc, ptr);
	ptr = strtok(NULL, "|");
	mem_locs[idx].caseHeight = (double)atof(ptr);
	ptr = strtok(NULL, "|");
	mem_locs[idx].caseWidth= (double)atof(ptr);
	ptr = strtok(NULL, "|");
	mem_locs[idx].caseLength = (double)atof(ptr);
	ptr = strtok(NULL, "|");
	mem_locs[idx].movement = (double)atof(ptr);
	ptr = strtok(NULL, "|");
	mem_locs[idx].racktype = atoi(ptr);
	ptr = strtok(NULL, "|");
	mem_locs[idx].NumInPallet = atoi(ptr);
	ptr = strtok(NULL, "|");
	mem_locs[idx].weight = (double)atof(ptr);
	ptr = strtok(NULL, "|");
	mem_locs[idx].SecID = atoi(ptr);
	ptr = strtok(NULL, "|");
	// brd - 10/22/99 - replaced this with baytype to help correctly do stocker calcs
	//mem_locs[idx].RackType = atoi(ptr);
	mem_locs[idx].BayType = atoi(ptr);
	ptr = strtok(NULL, "|");
	mem_locs[idx].RelLev = atoi(ptr);
	ptr = strtok(NULL, "|");
	mem_locs[idx].LevTime = (double)atof(ptr);
	ptr = strtok(NULL, "|");
	strcpy(mem_locs[idx].Loc_desc ,ptr);
	ptr = strtok(NULL, "|");
	mem_locs[idx].Loc_w = (double)atof(ptr);
	ptr = strtok(NULL, "|");
	mem_locs[idx].Loc_d = (double)atof(ptr);
	ptr = strtok(NULL, "|");
	mem_locs[idx].Loc_h = (double)atof(ptr);
	ptr = strtok(NULL, "|");
	mem_locs[idx].Loc_x = atoi(ptr);
	ptr = strtok(NULL, "|");
	mem_locs[idx].Loc_y = atoi(ptr);
	ptr = strtok(NULL, "|");
	mem_locs[idx].Loc_z = atoi(ptr);
	ptr = strtok(NULL, "|");
	mem_locs[idx].BayProfID = atoi(ptr);

	// UOI changes
	mem_locs[idx].forkFixedInsertion = mem_locs[idx].LevTime;
	ptr = strtok(NULL, "|");
	mem_locs[idx].forkFixedExtraction = (double)atof(ptr);

	ptr = strtok(NULL, "|");
	mem_locs[idx].innerHeight = (double)atof(ptr);
	ptr = strtok(NULL, "|");
	mem_locs[idx].innerWidth = (double)atof(ptr);
	ptr = strtok(NULL, "|");
	mem_locs[idx].innerLength = (double)atof(ptr);
	ptr = strtok(NULL, "|");
	mem_locs[idx].eachHeight = (double)atof(ptr);
	ptr = strtok(NULL, "|");
	mem_locs[idx].eachWidth = (double)atof(ptr);
	ptr = strtok(NULL, "|");
	mem_locs[idx].eachLength = (double)atof(ptr);
	ptr = strtok(NULL, "|");
	mem_locs[idx].casePack = (double)atof(ptr);
	ptr = strtok(NULL, "|");
	mem_locs[idx].innerPack = (double)atof(ptr);
	ptr = strtok(NULL, "|");
	mem_locs[idx].productTi = atoi(ptr);
	ptr = strtok(NULL, "|");
	mem_locs[idx].productHi = atoi(ptr);
	ptr = strtok(NULL, "|");
	mem_locs[idx].unitOfIssue = atoi(ptr);
	ptr = strtok(NULL, "|");
	mem_locs[idx].containerWidth = (double)atof(ptr);
	ptr = strtok(NULL, "|");
	mem_locs[idx].containerLength = (double)atof(ptr);
	ptr = strtok(NULL, "|");
	mem_locs[idx].containerHeight = (double)atof(ptr);
	ptr = strtok(NULL, "|");
	mem_locs[idx].caseQuantity = atoi(ptr);		// 38
	ptr = strtok(NULL, "|");
	mem_locs[idx].handlingMethod = atoi(ptr);

	ptr = strtok(NULL, "|");
	memcpy( mem_locs[idx].WMSProdID, ptr, (strlen( ptr ) + 1) );

	ptr = strtok(NULL, "|");
	memcpy( mem_locs[idx].WMSProdDetID, ptr, (strlen( ptr ) + 1) );

	ptr = strtok(NULL, "|");
	mem_locs[idx].prodDBID = atoi(ptr);

	ptr = strtok(NULL, "|");
	mem_locs[idx].isPrimary = atoi(ptr);

	ptr = strtok(NULL, "|");
	mem_locs[idx].rotatedWidth = (double)atof(ptr);
	
	ptr = strtok(NULL, "|");
	mem_locs[idx].rotatedLength = (double)atof(ptr);
	
	ptr = strtok(NULL, "|");
	mem_locs[idx].rotatedHeight = (double)atof(ptr);

	ptr = strtok(NULL, "|");		// skip case movement
	int dummy = atoi(ptr);
	ptr = strtok(NULL, "|");
	mem_locs[idx].locDBID = atoi(ptr);

	if ( mem_locs[idx].innerWidth == 0 )
		mem_locs[idx].innerWidth = mem_locs[idx].caseWidth;
	if ( mem_locs[idx].innerLength == 0 )
		mem_locs[idx].innerLength = mem_locs[idx].caseLength;
	if ( mem_locs[idx].innerWidth == 0 )
		mem_locs[idx].innerHeight = mem_locs[idx].caseHeight;
	if ( mem_locs[idx].eachWidth == 0 )
		mem_locs[idx].eachWidth = mem_locs[idx].caseWidth;
	if ( mem_locs[idx].eachLength == 0 )
		mem_locs[idx].eachLength = mem_locs[idx].caseLength;
	if ( mem_locs[idx].eachWidth == 0 )
		mem_locs[idx].eachHeight = mem_locs[idx].caseHeight;
	if ( mem_locs[idx].innerPack == 0 )
		mem_locs[idx].innerPack = 1;
	if ( mem_locs[idx].casePack == 0 )
		mem_locs[idx].casePack = 1;
	//mem_locs[idx].cube =
	//	(mem_locs[idx].width * mem_locs[idx].height * mem_locs[idx].length) /
	//		mem_sections[0].CubeConversion;

	// added 8/19/99 - UOI changes
	// brd - 10/20/99 - use numinpallet unless it's 0, else use tixhi
//	if ( ( mem_locs[idx].productHi * mem_locs[idx].productTi ) == mem_locs[idx].NumInPallet ||
//		 mem_locs[idx].NumInPallet == 0 ) {
	if (mem_locs[idx].NumInPallet == 0) {
		mem_locs[idx].NumInPallet = mem_locs[idx].productHi * mem_locs[idx].productTi;
		if ( mem_locs[idx].NumInPallet == 0 )
			mem_locs[idx].NumInPallet = 1;
	}

	switch(mem_locs[idx].unitOfIssue) {
		case 0 : //each
		{
			mem_locs[idx].cube =
				(mem_locs[idx].eachWidth * mem_locs[idx].eachHeight * mem_locs[idx].eachLength) /
				mem_sections[0].CubeConversion;
			//mem_locs[idx].caseCube = mem_locs[idx].cube * mem_locs[idx].casePack;
			mem_locs[idx].caseCube =
				(mem_locs[idx].caseWidth * mem_locs[idx].caseHeight * mem_locs[idx].caseLength) /
				mem_sections[0].CubeConversion;

			mem_locs[idx].caseMovement = mem_locs[idx].movement / mem_locs[idx].casePack;
			mem_locs[idx].palletMovement = mem_locs[idx].caseMovement / ( mem_locs[idx].NumInPallet );
			mem_locs[idx].eachWeight = mem_locs[idx].weight;
			mem_locs[idx].innerWeight = mem_locs[idx].weight * mem_locs[idx].innerPack;
			mem_locs[idx].caseWeight = mem_locs[idx].weight * mem_locs[idx].casePack;
			mem_locs[idx].palletWeight = mem_locs[idx].weight * mem_locs[idx].casePack * mem_locs[idx].NumInPallet;
			mem_locs[idx].width = mem_locs[idx].eachWidth;
			mem_locs[idx].length = mem_locs[idx].eachLength;
			mem_locs[idx].height = mem_locs[idx].eachHeight;
			break;
		}
		case 1 : //inner
		{
			mem_locs[idx].cube =
				(mem_locs[idx].innerWidth * mem_locs[idx].innerHeight * mem_locs[idx].innerLength) /
				mem_sections[0].CubeConversion;
			//mem_locs[idx].caseCube = mem_locs[idx].cube / mem_locs[idx].innerPack * mem_locs[idx].casePack;
			mem_locs[idx].caseCube =
				(mem_locs[idx].caseWidth * mem_locs[idx].caseHeight * mem_locs[idx].caseLength) /
				mem_sections[0].CubeConversion;

			mem_locs[idx].caseMovement = mem_locs[idx].movement * mem_locs[idx].innerPack / mem_locs[idx].casePack;
			mem_locs[idx].palletMovement = mem_locs[idx].caseMovement / ( mem_locs[idx].NumInPallet );
			mem_locs[idx].eachWeight = mem_locs[idx].weight / mem_locs[idx].innerPack;
			mem_locs[idx].innerWeight = mem_locs[idx].weight;
			mem_locs[idx].caseWeight = mem_locs[idx].weight / mem_locs[idx].innerPack * mem_locs[idx].casePack;
			mem_locs[idx].palletWeight = mem_locs[idx].weight / mem_locs[idx].innerPack * mem_locs[idx].casePack * mem_locs[idx].NumInPallet;
			mem_locs[idx].width = mem_locs[idx].innerWidth;
			mem_locs[idx].length = mem_locs[idx].innerLength;
			mem_locs[idx].height = mem_locs[idx].innerHeight;
			break;
		}
		case 2 : //case
		{
			mem_locs[idx].cube =
				(mem_locs[idx].caseWidth * mem_locs[idx].caseHeight * mem_locs[idx].caseLength) /
				mem_sections[0].CubeConversion;
			mem_locs[idx].caseCube = mem_locs[idx].cube;
			mem_locs[idx].caseMovement = mem_locs[idx].movement;
			mem_locs[idx].palletMovement = mem_locs[idx].movement / ( mem_locs[idx].NumInPallet );
			mem_locs[idx].eachWeight = mem_locs[idx].weight / mem_locs[idx].casePack;
			mem_locs[idx].innerWeight = mem_locs[idx].weight / mem_locs[idx].casePack * mem_locs[idx].innerPack;
			mem_locs[idx].caseWeight = mem_locs[idx].weight;
			mem_locs[idx].palletWeight = mem_locs[idx].weight * mem_locs[idx].NumInPallet;
			mem_locs[idx].width = mem_locs[idx].caseWidth;
			mem_locs[idx].length = mem_locs[idx].caseLength;
			mem_locs[idx].height = mem_locs[idx].caseHeight;
			break;
		}
		case 3 : //pallet
		{
			mem_locs[idx].cube =
				(mem_locs[idx].containerWidth * ( mem_locs[idx].caseHeight * mem_locs[idx].productHi + mem_locs[idx].containerHeight) * mem_locs[idx].containerLength ) /
				mem_sections[0].CubeConversion;
			//mem_locs[idx].caseCube = mem_locs[idx].cube / mem_locs[idx].NumInPallet;
			mem_locs[idx].caseCube =
				(mem_locs[idx].caseWidth * mem_locs[idx].caseHeight * mem_locs[idx].caseLength) /
				mem_sections[0].CubeConversion;

			mem_locs[idx].caseMovement = mem_locs[idx].movement * mem_locs[idx].NumInPallet;
			mem_locs[idx].palletMovement = mem_locs[idx].movement;
			mem_locs[idx].eachWeight = mem_locs[idx].weight / mem_locs[idx].casePack / mem_locs[idx].NumInPallet;
			mem_locs[idx].innerWeight = mem_locs[idx].weight / mem_locs[idx].casePack / mem_locs[idx].NumInPallet * mem_locs[idx].innerPack;
			mem_locs[idx].caseWeight = mem_locs[idx].weight / mem_locs[idx].NumInPallet;
			mem_locs[idx].palletWeight = mem_locs[idx].weight;
			mem_locs[idx].width = mem_locs[idx].caseWidth;
			mem_locs[idx].length = mem_locs[idx].caseLength;
			mem_locs[idx].height = mem_locs[idx].caseHeight;
			break;
		}
	}

	if(mem_locs[idx].Loc_h == 0)
		mem_locs[idx].Loc_h = mem_locs[idx].Loc_d;

	i = GetSectionIdx(mem_locs[idx].SecID);
	//mem_sections[i].ave_x += mem_locs[idx].Loc_x;
	//mem_sections[i].ave_y += mem_locs[idx].Loc_y;
	//mem_sections[i].loc_count ++;

//	mem_locs[idx].cube = (mem_locs[idx].width * mem_locs[idx].height * mem_locs[idx].length) /
//		mem_sections[i].CubeConversion;

	mem_locs[idx].cube = (mem_locs[idx].width * mem_locs[idx].height * mem_locs[idx].length) /
		mem_sections[0].CubeConversion;

}

double BaselineProcess::RunComp(void)
{
	int i, j;
	double CostSum, tmpCost;
	baselineLaborProd p;
	baselineLaborLoc  l;

	/* ******************************************** */
	/* We have a list of product location combo's.  */
	/* Our task is to go through that list and calc */
	/* a cost for each prod loc combo.  Sum up the  */
	/* total cost, and then return the total back   */
	/* to the caller.                               */
	/* ******************************************** */

	CostSum = 0;

	for(i=0;i<mem_loc_count;i++) {

		/* ******************************************** */
		/* Populate two structures with info, and then  */
		/* call the calc routine.                       */
		/* ******************************************** */
		strcpy(l.desc, mem_locs[i].Loc_desc);
		l.x = mem_locs[i].Loc_x;
		l.y = mem_locs[i].Loc_y;
		l.z = mem_locs[i].Loc_z;
		l.w = mem_locs[i].Loc_w;
		l.d = mem_locs[i].Loc_d;
		l.h = mem_locs[i].Loc_h;
		l.level = mem_locs[i].RelLev;
		l.BayProfID = mem_locs[i].BayProfID;
		// brd
		l.BayType = mem_locs[i].BayType;

		l.fork_fixed_insertion = mem_locs[i].LevTime;
		j = GetSectionIdx(mem_locs[i].SecID);
		l.section_idx = j;
		mem_current_section_id = mem_locs[i].SecID;
		l.avg_replen_dist = mem_sections[j].AvgReplenDist;
		l.fork_dist_var = mem_sections[j].ForkVar;
		l.fork_dist_fxd = mem_sections[j].ForkFxd;
		l.fork_rate = mem_sections[j].ForkRate;
		l.sel_dist_var = mem_sections[j].SelVar;
		l.sel_dist_fxd = mem_sections[j].SelFxd;
		l.sel_rate = mem_sections[j].SelRate;
		l.fork_hot_x = mem_sections[j].ForkHotX;
		l.fork_hot_y = mem_sections[j].ForkHotY;
		l.fork_hot_z = mem_sections[j].ForkHotZ;
		l.sel_hot_x = mem_sections[j].SelHotX;
		l.sel_hot_y = mem_sections[j].SelHotY;
		l.sel_hot_z = mem_sections[j].SelHotZ;
		l.TotalMovement = mem_sections[j].TotalMovement;

		l.numPutsPerTrip = mem_sections[j].numPutsPerTrip;
		l.pickForkTrav = mem_sections[j].pickForkTrav;
		l.insertForkTrav = mem_sections[j].insertForkTrav;
		l.totalExtendedCube = mem_sections[j].totalExtendedCube;
		l.handlingMethod = mem_locs[i].handlingMethod;
		l.stockerRate = mem_sections[j].stockerRate;
		l.forkFixedInsertion = mem_locs[i].forkFixedInsertion;
		l.forkFixedExtraction = mem_locs[i].forkFixedExtraction;
		l.locDBID = mem_locs[i].locDBID;
		l.isPrimary = mem_locs[i].isPrimary;

	strcpy(p.desc, mem_locs[i].ProdDesc);
//		p.cube = mem_locs[i].cube;
//		p.numInPallet = mem_locs[i].NumInPallet;
//		p.movement = mem_locs[i].movement;
//		p.weight = mem_locs[i].weight;
//		p.BayProfID = mem_locs[i].BayProfID;

		p.cube = mem_locs[i].cube;
		p.NumInPallet = mem_locs[i].NumInPallet;
		p.movement = mem_locs[i].movement;
		p.weight = mem_locs[i].weight;
		p.BayProfID = mem_locs[i].BayProfID;
		p.caseMovement = mem_locs[i].caseMovement;
		p.palletMovement = mem_locs[i].palletMovement;
		p.caseCube = mem_locs[i].caseCube;
		p.unitOfIssue = mem_locs[i].unitOfIssue;
		p.productTi = mem_locs[i].productTi;
		p.productHi = mem_locs[i].productHi;

		//brd
		p.caseWeight = mem_locs[i].caseWeight;
		p.prodDBID = mem_locs[i].prodDBID;
		
		p.rotatedWidth = mem_locs[i].rotatedWidth;
		p.rotatedLength = mem_locs[i].rotatedLength;
		p.rotatedHeight = mem_locs[i].rotatedHeight;

		l.solutionID = mem_locs[i].SolutionID;

		if ( mem_locs[i].caseQuantity == 0 ) {
			if ( mem_locs[i].handlingMethod == 3 )
				mem_locs[i].caseQuantity = mem_locs[i].NumInPallet;
			else
				mem_locs[i].caseQuantity = 1;
		}

		strcpy(p.WMSProdID,mem_locs[i].WMSProdID);
		strcpy(p.WMSProdDetID,mem_locs[i].WMSProdDetID);

		// need to figure out how to calculate cost for extra facings
		if (mem_locs[i].isPrimary == 0) {

			// just do a calculation for one case - we will zero these out later
			tmpCost = CalcLabor(&p, &l, 0);
			// for now, don't add the cost since the case quantity is covered in the primary
			// but we do need to send it back for update
		}
		else {
			tmpCost = CalcLabor(&p, &l, mem_locs[i].caseQuantity);
			if(tmpCost < 0.000001f)
				tmpCost = 0.0f;
			CostSum += tmpCost;
		}
	} 

	return CostSum;

}


double BaselineProcess::CalcLabor(baselineLaborProd *p, baselineLaborLoc *l, int casesInPick)
{
	double SScale;
	double PtwyCost, ReplenCost;
	double Cost;
	double BrokenOrderDist;
	double  BrokenOrderCount;
	double tempdouble=0.0;
	int i,j;
	char buffer[2560];

	memset(buffer, 0, 2560);

	sprintf(buffer, "%d|%s|%s|%s|%d|%d", l->solutionID, p->desc, l->desc,
		mem_sections[l->section_idx].Sec_desc,
		mem_sections[l->section_idx].SecID,
		p->BayProfID);


	sprintf(buffer, "%s|%10.5f", buffer, casesInPick == 0 ? 0 : p->movement);
	// This is where the putaway calculation begins for the forklift cost.
	/* *************************************************** */
	/* Putaway calculation:                                */
	/* *************************************************** */

	////////////////////////////////////////////////////////////////////////////
	// First, calculate the distance from the Forklift hotspot to the current
	// location.  Do this with a line of sight calculation.
	////////////////////////////////////////////////////////////////////////////
	
	double putawayDistance = distance(l->x, l->y, l->fork_hot_x, l->fork_hot_y);
	

//	sprintf(buffer, "%s|%10.5f", buffer, casesInPick == 0 ? 0 : putawayDistance);

	// convert from inches/centimeters to feet/meters
	putawayDistance /= inches_to_foot;

	////////////////////////////////////////////////////////////////////////////
	// Calculate the number of putaways.  This is done by dividing the movement
	// of the current product (given in cases per week) by the number of cases
	// in a pallet which already yielded palletMovement.  This will tell us how many 
	// pallets per week we will have to deal with.  Hence, the number of putaways.
	
	if ( l->numPutsPerTrip == 0 )
		l->numPutsPerTrip = 1;

	//holdPutAwayVal = ((double)p->palletMovement);
	double putaways = p->palletMovement;
	double putawayTrips = ((double)p->palletMovement / l->numPutsPerTrip);
	
	
	sprintf(buffer, "%s|%10.5f", buffer, casesInPick == 0 ? 0 : 2*putawayDistance*putawayTrips);

	// dex: we should display number of putaways, not number of trips
	sprintf(buffer, "%s|%10.5f|%d", buffer, casesInPick == 0 ? 0 : putaways, 
		casesInPick == 0 ? 0 : p->NumInPallet);  // Number of ptwy's


	////////////////////////////////////////////////////////////////////////////
	// Forklift handling time is given by the number of putaways multiplied by the
	// engineered values for the time it takes to accomplish a putaway.
	////////////////////////////////////////////////////////////////////////////

	// pickForkTrav is the time (minutes) it takes to pick up the pallet from the dock
	// insertForkTrav is the time (minutes) it takes to put the pallet into the location

	// we have to insert every pallet in a different location so we use palletMovement but we only 
	// have to go to the dock once to pick up all the pallets for the trip so the extra
	// time for multiple pallets is negligible.
	
	// dex:  num pallets * insertion + num trips * pick up
	//ForkHandle = (l->pickForkTrav+l->insertForkTrav) * numPutAways;	
	double putawayForkHandlingTime = putawayTrips * l->pickForkTrav + p->palletMovement * l->insertForkTrav;
	
	// Convert minutes to hours
	putawayForkHandlingTime *= MIN_TO_HOUR;

	////////////////////////////////////////////////////////////////////////////
	// Here we take the number of putaways, multiply by the forklift travel
	// distance by two to get the total putaway travel since we have to to and
	// from the dock.  We then convert this distance into a time value.
	////////////////////////////////////////////////////////////////////////////

	// fork_dist_var is the travel rate in feet/meters per minute
	// fork_dist_fxd is the amount of startup time 
	// the startup time is used for each trip; the variable time is based on the total distance

	// dex:  ((trips * distance * 2 * variable) + (trips * 2 * fixed))  /inches_to_foot
	//double putawayTravelTime = (numPutAways * ForkDist * 2) * (l->fork_dist_var / inches_to_foot) + l->fork_dist_fxd;

	double putawayStartupTime = 2 * putawayTrips * l->fork_dist_fxd;
	
	// we already converted distance into feet
	double putawayTravelTime = 2 * putawayTrips * putawayDistance * l->fork_dist_var;
		
	// add the startup time to the travel time to get the total travel time
	double putawayTotalTravelTime = putawayStartupTime + putawayTravelTime;
	
	// convert from minutes to hours
	putawayTravelTime *= MIN_TO_HOUR;

	sprintf(buffer, "%s|%10.5f", buffer, casesInPick == 0 ? 0 : putawayTravelTime);  // Travel Hours

	sprintf(buffer, "%s|%10.5f", buffer, casesInPick == 0 ? 0 : putawayForkHandlingTime); // Handling Hours

	////////////////////////////////////////////////////////////////////////////
	// Multiply both the travel time (PtwyCost) and the Handling time by the
	// forklift hourly rate to convert the fork putaway time into a dollar value.
	////////////////////////////////////////////////////////////////////////////

	// Total time is the travel time added to the handling time
	double putawayTotalTime = putawayTravelTime + putawayForkHandlingTime;

	// Cost is total time multiplied by the hourly pay
	PtwyCost = putawayTotalTime * l->fork_rate;
	sprintf(buffer, "%s|%10.5f",  buffer, casesInPick == 0 ? 0 : PtwyCost); // Ptwy cost.



	// The Forklift Replenishment calculation begins here:
	/* *************************************************** */
	/* Replenishment Calculation:                          */
	/* *************************************************** */

	////////////////////////////////////////////////////////////////////////////
	// Sanity check on width, depth, and height of location because we will be
	// using these as divisors in a future calculation.
	////////////////////////////////////////////////////////////////////////////
	if (l->w==0) l->w=1;
	if (l->d==0) l->d=1;
	if (l->h==0) l->h=1;

	////////////////////////////////////////////////////////////////////////////
	// If this is the dummy product calculation (casesInPick == -1), or if the
	// Location holds a full pallet or more, the number of Replenishments needed
	// over the time horizon will be the same as the number of Put-Aways.
	// Otherwise, calculate the ratio of Replenishments to Put-Aways.
	////////////////////////////////////////////////////////////////////////////
	double replensToPWs;
	if ((casesInPick >= p->NumInPallet) || (casesInPick <= 0))
		replensToPWs = 1.0;
	else
		replensToPWs = (double)(p->NumInPallet / casesInPick);

	double replenishments = replensToPWs * putaways;

	// No replenishments for full pallet selects
	if ( l->handlingMethod == 3 && p->unitOfIssue == 3 )
		replenishments = 0;

	sprintf(buffer, "%s|%10.5f", buffer, casesInPick == 0 ? 0 : replenishments); // Number of Replens.

	////////////////////////////////////////////////////////////////////////////
	// Calculate replenishement travel distance.
	////////////////////////////////////////////////////////////////////////////
	// avg_replen_dist is the average distance to go from the replenishment area
	// to the selection area; the hotspot is not involved because they are not
	// going to the dock; assume they have to go both directions
	double replenishmentDistance = replenishments * l->avg_replen_dist * 2;
	sprintf(buffer, "%s|%10.5f", buffer, casesInPick == 0 ? 0 : replenishmentDistance);

	// Convert replenishment distance from inches/centimeters to feet/meters
	// brd - distance should already be in feet
	//replenishmentDistance /= inches_to_foot;

	////////////////////////////////////////////////////////////////////////////
	// Turn the distance into a time by using the calculated engineering standards.
	////////////////////////////////////////////////////////////////////////////
	// dex: time = (distance * variable + numreplens * fixed)/p4inchestofoot
	double replenishmentTravelTime = replenishmentDistance * l->fork_dist_var;
	double replenishmentStartupTime = replenishments *  l->fork_dist_fxd;
	double replenishmentTotalTravelTime = replenishmentStartupTime + replenishmentTravelTime;


	// Convert time to hours
	replenishmentTotalTravelTime = replenishmentTotalTravelTime * MIN_TO_HOUR;
	sprintf(buffer, "%s|%10.5f", buffer, casesInPick == 0 ? 0 : replenishmentTotalTravelTime); // Travel Hours

	////////////////////////////////////////////////////////////////////////////
	// Our Forklift handling time is simply the insertion time for a pallet on
	// this level times the number of replenishments per week that we handle.
	////////////////////////////////////////////////////////////////////////////
	// fork_fixed_insertion - time (minutes) it takes to put a pallet in this location
	double replenishmentHandlingTime = l->fork_fixed_insertion * replenishments;
	
	// Convert from minutes to hours
	replenishmentHandlingTime *= MIN_TO_HOUR;
	sprintf(buffer, "%s|%10.5f", buffer, casesInPick == 0 ? 0 : replenishmentHandlingTime); // Handling Hours

	////////////////////////////////////////////////////////////////////////////
	// Multiply both the handling time and the replenishment travel time by the
	// hourly forklift rate to get a dollar amount for replenishment cost.
	////////////////////////////////////////////////////////////////////////////
	double totalReplenishmentTime = replenishmentHandlingTime + replenishmentTotalTravelTime;
	ReplenCost = totalReplenishmentTime * l->fork_rate;
	sprintf(buffer, "%s|%10.5f", buffer, casesInPick == 0 ? 0 : ReplenCost); // Replen Cost.

	////////////////////////////////////////////////////////////////////////////
	// Total forklift cost is Putaway Cost plus Replenishment cost.
	////////////////////////////////////////////////////////////////////////////
	double ForkTotalCost = PtwyCost + ReplenCost;

	// Selection cost calculations start here.
	/* ********************************************************* */
	/* Selection Costs                                           */
	/* ********************************************************* */

	////////////////////////////////////////////////////////////////////////////
	// Check to see what our broken order distance will be.  If the current
	// section utilizes broken order distances, the distance is calculated as a
	// straight line from the center of the section to the selection hot spot.
	// If the current section does not utilize broken order travel, just set the
	// distance to zero and the rest of the calculations will not have to change.  
	////////////////////////////////////////////////////////////////////////////
	if(mem_sections[l->section_idx].ApplyBrokenOrder == 1){
	//	BrokenOrderDist = distance(mem_sections[l->section_idx]., mem_sections[l->section_idx].ave_y,
	//		l->sel_hot_x, l->sel_hot_y);
		BrokenOrderDist = distance(l->x, l->y,l->sel_hot_x, l->sel_hot_y);
	} else {
		BrokenOrderDist = 0;
	}

//	if (strcmp(l->desc, "LV121") == 0 || strcmp(l->desc, "MC001") == 0)
//		printf("break here\n");

	////////////////////////////////////////////////////////////////////////////
	// Broken order travel is two ways.  Multiply by two to take this into account. 
	////////////////////////////////////////////////////////////////////////////
	BrokenOrderDist *=2;
	sprintf(buffer, "%s|%10.5f", buffer, casesInPick == 0 ? 0 : BrokenOrderDist / inches_to_foot);	// feet

	////////////////////////////////////////////////////////////////////////////
	// Sanity check on the ContainerQty.
	////////////////////////////////////////////////////////////////////////////
	if (mem_sections[l->section_idx].ContainerQty==0)
		mem_sections[l->section_idx].ContainerQty=1;

	////////////////////////////////////////////////////////////////////////////
	// We figure out how many broken order travel segments to allow for by the
	// size of the containers used in this section, and the average quantity for
	// each order. 
	////////////////////////////////////////////////////////////////////////////
	BrokenOrderCount = ((double)(mem_sections[l->section_idx].AvgOrdQty) /
		(double)(mem_sections[l->section_idx].ContainerQty));


	sprintf(buffer, "%s|%10.5f", buffer, casesInPick == 0 ? 0 : BrokenOrderCount);

	// This is the pick path distance	
	double SelDist = mem_sections[l->section_idx].SelDist;

	// Add in one trip from the center to the hotspot to account for 
	// getting in and out of the section
	SelDist += distance(mem_sections[l->section_idx].ave_x, mem_sections[l->section_idx].ave_y,
			l->sel_hot_x, l->sel_hot_y);

	sprintf(buffer, "%s|%10.5f", buffer, casesInPick == 0 ? 0 : SelDist / inches_to_foot);	// feet
	sprintf(buffer, "%s|%d", buffer, casesInPick == 0 ? 0 : mem_sections[l->section_idx].OrderCount);

	////////////////////////////////////////////////////////////////////////////
	// Total selection distance is made up of pickpath distance plus broken
	// order travel, both multiplied by the order count for that section.  This
	// is the total travel distance for all orders for the week in this section.
	// If these are full pallet selects, the travel is just the distance from the
	// location to the selection hotspot
	////////////////////////////////////////////////////////////////////////////

	
	double selectionTotalTravelTime = 0;
	double fullPalletTotalTravelTime = 0;

	// This is a full pallet select
	if ( ( l->handlingMethod == 3 && p->unitOfIssue == 3 ) ) { 
		// dex: (full pallet travel) = same as Putaway travel except use selection hotspot (avg dist from loc to selection hotspot)
		double fullPalletDistance = distance(l->x, l->y, l->sel_hot_x, l->sel_hot_y);	// inches	
		
		// Convert to feet/meters
		fullPalletDistance /= inches_to_foot;
		
		SScale = (double)((p->caseCube * p->caseMovement) / l->totalExtendedCube);
		sprintf(buffer, "%s|%10.5f", buffer, casesInPick == 0 ? 0 : fullPalletDistance);	// feet
		sprintf(buffer, "%s|%10.5f", buffer, casesInPick == 0 ? 0 : SScale);


		// ? for dex - do we care about scale here?
		double fullPalletStartupTime = 2 * p->palletMovement * l->fork_dist_fxd;

		double fullPalletTravelTime = 2 * p->palletMovement * fullPalletDistance * l->fork_dist_var;
		
		// add the startup time to the travel time to get the total travel time
		fullPalletTotalTravelTime = fullPalletStartupTime + fullPalletTravelTime;
		
		// convert from minutes to hours
		fullPalletTotalTravelTime *= MIN_TO_HOUR;
		
		sprintf(buffer, "%s|%10.5f", buffer, casesInPick == 0 ? 0 : fullPalletTotalTravelTime);  // Travel Hours
	}
	
	// Not full pallet 
	else {
		
		double selectionTravelDistance = (SelDist + (BrokenOrderDist * BrokenOrderCount)) 
			* mem_sections[l->section_idx].OrderCount;		// inches
		
		
		// Convert distance from inches/centimeters to feet/meters
		selectionTravelDistance /= inches_to_foot;
		
		////////////////////////////////////////////////////////////////////////////
		// Sanity check on movement as it is central to the rest of the calculations
		// that we will make.
		////////////////////////////////////////////////////////////////////////////
		if ( l->totalExtendedCube < 1 )
			l->totalExtendedCube = 1000000; // default to the total XCube for this PG
		
		////////////////////////////////////////////////////////////////////////////
		// Calculate a scale that will indicate what percentage of work one product
		// is compared to the total in this section.  Thus the scale is the
		// individual product's extended cube divided by the section's extended cube
		// We use this because otherwise every selection would have equal weight as
		// far as the travel when in reality the ones that move the most should
		// be weighted higher
		////////////////////////////////////////////////////////////////////////////
		SScale = (double)((p->caseCube * p->caseMovement) / l->totalExtendedCube);
		sprintf(buffer, "%s|%10.5f", buffer, casesInPick == 0 ? 0 : selectionTravelDistance*SScale);	 // feet
		sprintf(buffer, "%s|%10.5f", buffer, casesInPick == 0 ? 0 : SScale);
		
		////////////////////////////////////////////////////////////////////////////
		// Convert the selection distance into a time by using the engineered values
		// for this section.
		////////////////////////////////////////////////////////////////////////////
		// dex: (dist * scale * var + dist * brokenordercount * 2)
		//SelTCost = (SelDist * SScale * l->sel_dist_var / inches_to_foot + l->sel_dist_fxd);
		
		// the distance times the rate of travel times the scael
		double selectionTravelTime = selectionTravelDistance * SScale * l->sel_dist_var;
		
		// broken order count is at least one; this tells us how many times they had to
		// get going
		double selectionStartupTime = 2 * BrokenOrderCount * l->sel_dist_fxd;
		
		selectionTotalTravelTime = selectionTravelTime + selectionStartupTime;
		
		// Convert minutes to hours
		selectionTotalTravelTime *= MIN_TO_HOUR;
		
		sprintf(buffer, "%s|%10.5f", buffer, casesInPick == 0 ? 0 : selectionTotalTravelTime); // Travel Hours
	}


	////////////////////////////////////////////////////////////////////////////
	// Find the correct level engineering values by using the level index and
	// the cube value for the current product.  This will give us the variable
	// and the fixed factors.
	////////////////////////////////////////////////////////////////////////////
	
	double unitMovement = p->movement;
	double unitWeight = p->weight;
	
	double fullPalletHandlingTime, selectionPickHandlingTime, selectionStockerHandlingTime;

	//////////////////////////////////////////////////////////////////////
	// pallet product going to pallet location
	//////////////////////////////////////////////////////////////////////
	if ( ( l->handlingMethod == 3 && p->unitOfIssue == 3 ) ) { 
		fullPalletHandlingTime = l->forkFixedExtraction;
		selectionPickHandlingTime = 0;
		selectionStockerHandlingTime = 0; 
	}
	//////////////////////////////////////////////////////////////////////
	// pallet product going to case location - use case movement, 
	// dimensions, weight, etc.
	//////////////////////////////////////////////////////////////////////
	else if ( p->unitOfIssue == 3 && l->handlingMethod == 1 ) { 
		//p->cube = p->cube / ( p->NumInPallet );  // scale down to the case cube
		unitMovement = p->movement / ( p->NumInPallet );
		unitWeight = p->weight / ( p->NumInPallet );
		
		i = FindRTLevCubeSelect(l->BayProfID, l->level,p->caseCube);
		j = FindRTLevCubeStocker(l->BayProfID, l->level,p->caseCube);
	
		fullPalletHandlingTime = 0;
		if ( i > -1 )
			selectionPickHandlingTime = (mem_labor_select[i].Var * p->weight + mem_labor_select[i].Fxd);
		else
			selectionPickHandlingTime = 0;
		if ( j > -1 )
			selectionStockerHandlingTime = (mem_labor_stocker[j].Var * p->weight + mem_labor_stocker[j].Fxd);
		else
			selectionStockerHandlingTime = 0;

	}
	//////////////////////////////////////////////////////////////////////
	// Product going to a case handling location, use UOI values for
	// dimensions, weight, etc.
	//////////////////////////////////////////////////////////////////////
	else { 
		i = FindRTLevCubeSelect(l->BayProfID, l->level,p->cube);
		// brd - use case cube to get stocker values on case flow locations
		if (l->BayType == BAY_TYPE_FLOW)
			j = FindRTLevCubeStocker(l->BayProfID, l->level,p->caseCube);
		else
			j = FindRTLevCubeStocker(l->BayProfID, l->level,p->cube);

		fullPalletHandlingTime = 0;
		if ( i > -1 )
			selectionPickHandlingTime = (mem_labor_select[i].Var * p->weight + mem_labor_select[i].Fxd);
		else
			selectionPickHandlingTime = 0;
		if ( j > -1 )
			// brd - use case cube to get stocker handling times on case flow locations
			if (l->BayType == BAY_TYPE_FLOW)
				selectionStockerHandlingTime = (mem_labor_stocker[j].Var * p->caseWeight + mem_labor_stocker[j].Fxd);
			else
				selectionStockerHandlingTime = (mem_labor_stocker[j].Var * p->weight + mem_labor_stocker[j].Fxd);
		else
			selectionStockerHandlingTime = 0;
	}	
		
	////////////////////////////////////////////////////////////////////////////
	// Get the handling time by using the variable and fixed factors that
	// we just looked up, combined with the weight of the current product.
	////////////////////////////////////////////////////////////////////////////
	selectionPickHandlingTime *= MIN_TO_HOUR;
	sprintf(buffer, "%s|%10.5f", buffer, casesInPick == 0 ? 0 : selectionPickHandlingTime); // Hours per touch

	// UOI handle * movement
	// brd - changed this to use UOI movement
	// selectionPickHandlingTime = selectionPickHandlingTime * p->caseMovement;
	selectionPickHandlingTime = selectionPickHandlingTime * unitMovement;
	sprintf(buffer, "%s|%10.5f", buffer, casesInPick == 0 ? 0 : selectionPickHandlingTime); // Handle Hours per week

	// Pick cost
	double selectionPickHandlingCost = selectionPickHandlingTime * l->sel_rate;

	sprintf(buffer, "%s|%10.5f", buffer, casesInPick == 0 ? 0 : selectionPickHandlingCost); // Handle cost

	// Stocker handle
	selectionStockerHandlingTime *= MIN_TO_HOUR;
	sprintf(buffer, "%s|%10.5f", buffer, casesInPick == 0 ? 0 : selectionStockerHandlingTime); // Hours per touch

	// Stocker handle * movement
	// brd - use case movement for stocker in case flow locations
	if (l->BayType == BAY_TYPE_FLOW)
		selectionStockerHandlingTime = selectionStockerHandlingTime * p->caseMovement;
	else
		selectionStockerHandlingTime = selectionStockerHandlingTime * unitMovement;
	sprintf(buffer, "%s|%10.5f", buffer, casesInPick == 0 ? 0 : selectionStockerHandlingTime); // Handle Hours per week

	// Stocker cost
	double selectionStockerHandlingCost = selectionStockerHandlingTime * l->stockerRate;
	sprintf(buffer, "%s|%10.5f", buffer, casesInPick == 0 ? 0 : selectionStockerHandlingCost);


	// Fork (PS) handle
	fullPalletHandlingTime *= MIN_TO_HOUR;
	sprintf(buffer, "%s|%10.5f", buffer, casesInPick == 0 ? 0 : fullPalletHandlingTime); // Hours per touch

	// Fork (PS) handle * movement
	fullPalletHandlingTime = fullPalletHandlingTime * p->palletMovement;
	sprintf(buffer, "%s|%10.5f", buffer, casesInPick == 0 ? 0 : fullPalletHandlingTime); // Handle Hours per week

	// Fork (PS) cost
	double fullPalletSelectCost = fullPalletHandlingTime * l->fork_rate;
	sprintf(buffer, "%s|%10.5f", buffer, casesInPick == 0 ? 0 : fullPalletSelectCost); // Handle Hours per week

	////////////////////////////////////////////////////////////////////////////
	// Total hours:  Add the Travel hours together with the handling hours to
	// get the total hours used for this product for selection activity.
	// Multiply this by the hourly rate to get a dollar value.
	////////////////////////////////////////////////////////////////////////////
	double allSelectionTravelTime = selectionTotalTravelTime + fullPalletTotalTravelTime;
	double allSelectionHandlingTime = selectionPickHandlingTime + selectionStockerHandlingTime + fullPalletHandlingTime;

	double allSelectionTime = allSelectionTravelTime + allSelectionHandlingTime;
	sprintf(buffer, "%s|%10.5f", buffer, casesInPick == 0 ? 0 : allSelectionTime);

	// Cases per hour
	double casesPerHour = 0;

	double nonForkTime = selectionTotalTravelTime + selectionPickHandlingTime + selectionStockerHandlingTime;

	if ( nonForkTime > 0 )
		casesPerHour = p->caseMovement / nonForkTime;

	double forkTime = fullPalletTotalTravelTime + fullPalletHandlingTime;

	if ( forkTime > 0 )
		casesPerHour += p->palletMovement * p->NumInPallet / forkTime;

	sprintf(buffer, "%s|%10.5f", buffer, casesInPick == 0 ? 0 : casesPerHour); //cases per hour

	// selection cost
	double SelectionTotalCost = (selectionTotalTravelTime * l->sel_rate) + ( selectionPickHandlingTime * l->sel_rate )  
		+ ( selectionStockerHandlingTime * l->stockerRate ) 
		+ (fullPalletTotalTravelTime * l->fork_rate)
		+ (fullPalletHandlingTime * l->fork_rate);

	sprintf(buffer, "%s|%10.5f", buffer, casesInPick == 0 ? 0 : SelectionTotalCost);
	
	////////////////////////////////////////////////////////////////////////////
	// Total cost is forklift cost plus selection cost.  Add them together, log
	// and return the result.
	////////////////////////////////////////////////////////////////////////////
	Cost = ForkTotalCost + SelectionTotalCost;
//	sprintf(buffer, "%s|%10.5f|\n", buffer, cost);

	sprintf(buffer, "%s|%10.5f", buffer, casesInPick == 0 ? 0 : Cost);

	// number of cases for stocker handling
	if (selectionStockerHandlingTime > 0)
		sprintf(buffer, "%s|%10.5f",buffer, casesInPick == 0 ? 0 : p->caseMovement);
	else
		sprintf(buffer, "%s|%10.5f", buffer, 0.0);

	// number of full pallet selects
	if ( l->handlingMethod == 3 && p->unitOfIssue == 3 )
		sprintf(buffer, "%s|%10.5f",buffer, casesInPick == 0 ? 0 : p->palletMovement);
	else
		sprintf(buffer, "%s|%10.5f",buffer, 0.0);
	
	sprintf(buffer, "%s|%s|%s|%d|%d|%d|%f|%f|%f|%d\n",
		buffer,p->WMSProdID,p->WMSProdDetID, 
		p->prodDBID,
		(l->isPrimary == 1) ? casesInPick : 0, l->isPrimary,
		p->rotatedWidth, p->rotatedLength, p->rotatedHeight, l->locDBID);

	
	char * bufspreadsheet = strchr(buffer,'|');
	bufspreadsheet++;

	fprintf(dummyb_file, bufspreadsheet);
//	fprintf(dummyb_file, buffer);

	unsigned int err;
	/***>>>
	err = BaselineSock->SendData(buffer, strlen(buffer));
	<<<***/
	gfnGetDataStream()->ssData << buffer;
	
	/***>>>
	if(err < strlen(buffer))
		throw EngineException("Error Intermediate Cost",
		 __FILE__, __LINE__, 200);
	<<<***/
	
	return Cost;
}

int BaselineProcess::FindRTLevCubeSelect(int ProfID, int Lev, double cube)
{
	int i;
	
	/* ********************************************** */
	/* Scan to find the correct RT and Lev.           */
	/* ********************************************** */
/*	printf("Prof %d Lev %d Cube %f",ProfID, Lev,cube);

	for(i=0;i<mem_labor_select_count;i++)
		if( (mem_labor_select[i].ProfId == ProfID) &&
			(mem_labor_select[i].RelLev == Lev))
			break;

	for(;i<mem_labor_select_count;i++){
		if( (mem_labor_select[i].ProfId != ProfID) ||
			(mem_labor_select[i].RelLev != Lev)) {
			//printf(" Returning 1: Prof %d Lev %d Cube %f Var %f Fixed %f \n",
			//	mem_labor_select[i-1].ProfId, mem_labor_select[i-1].RelLev, mem_labor_select[i-1].Cube,
			//	mem_labor_select[i-1].Var, mem_labor_select[i-1].Fxd);
			return i-1;
		}
		if(cube < mem_labor_select[i].Cube) {
			//printf(" Returning 2: Prof %d Lev %d Cube %f Var %f Fixed %f \n",
			//	mem_labor_select[i-1].ProfId, mem_labor_select[i-1].RelLev, mem_labor_select[i-1].Cube,
			//	mem_labor_select[i-1].Var, mem_labor_select[i-1].Fxd);
			return i-1;
		}
	}

	return mem_labor_select_count-1;
*/
	for(i=0;i<mem_labor_select_count;i++)
		if( (mem_labor_select[i].ProfId == ProfID) &&
			(mem_labor_select[i].RelLev == Lev))
			break;

	for(;i<mem_labor_select_count;i++){
		if( (mem_labor_select[i].ProfId != ProfID) ||
			(mem_labor_select[i].RelLev != Lev)) {
			return i-1;
		}
		if(cube < mem_labor_select[i].Cube) {
			return i-1;
		}
	}

	return mem_labor_select_count-1;

}

int BaselineProcess::FindRTLevCubeStocker(int ProfID, int Lev, double cube)
{
	int i;
	
	/* ********************************************** */
	/* Scan to find the correct RT and Lev.           */
	/* ********************************************** */
/*	printf("Prof %d Lev %d Cube %f",ProfID, Lev,cube);

	for(i=0;i<mem_labor_stocker_count;i++)
		if( (mem_labor_stocker[i].ProfId == ProfID) &&
			(mem_labor_stocker[i].RelLev == Lev))
			break;
	if ( i == mem_labor_stocker_count )
		return -1;

	for(;i<mem_labor_stocker_count;i++){
		if( (mem_labor_stocker[i].ProfId != ProfID) ||
			(mem_labor_stocker[i].RelLev != Lev)) {
			//printf(" Returning 1: Prof %d Lev %d Cube %f Var %f Fixed %f \n",
			//	mem_labor_stocker[i-1].ProfId, mem_labor_stocker[i-1].RelLev, mem_labor_stocker[i-1].Cube,
			//	mem_labor_stocker[i-1].Var, mem_labor_stocker[i-1].Fxd);
			return i-1;
		}
		if(cube < mem_labor_stocker[i].Cube) {
			//printf(" Returning 2: Prof %d Lev %d Cube %f Var %f Fixed %f \n",
			//	mem_labor_stocker[i-1].ProfId, mem_labor_stocker[i-1].RelLev, mem_labor_stocker[i-1].Cube,
			//	mem_labor_stocker[i-1].Var, mem_labor_stocker[i-1].Fxd);
			return i-1;
		}
	}

	return mem_labor_stocker_count-1;
*/
	for(i=0;i<mem_labor_stocker_count;i++)
		if( (mem_labor_stocker[i].ProfId == ProfID) &&
			(mem_labor_stocker[i].RelLev == Lev))
			break;
	if ( i == mem_labor_stocker_count )
		return -1;

	for(;i<mem_labor_stocker_count;i++){
		if( (mem_labor_stocker[i].ProfId != ProfID) ||
			(mem_labor_stocker[i].RelLev != Lev)) {
			return i-1;
		}
		if(cube < mem_labor_stocker[i].Cube) {
			return i-1;
		}
	}

	return mem_labor_stocker_count-1;

}

int BaselineProcess::GetSectionIdx(int idx)
{
	int i;
	for(i=0;i<mem_section_count;i++)
		if(mem_sections[i].SecID == idx)
			return i;
	return 0;
}

int LevelLaborSortBase( const void *p1, const void *p2 ) {
	ssaLaborLevel *inf1, *inf2;

	inf1 = (ssaLaborLevel *) p1;
	inf2 = (ssaLaborLevel *) p2;

	if ( inf1->ProfId < inf2->ProfId )
		return -1;
	else if ( inf1->ProfId > inf2->ProfId )
		return 1;
	else {
		if ( inf1->RelLev < inf2->RelLev )
			return -1;
		else if (inf1->RelLev > inf2->RelLev )
			return 1;
		else {
			if (inf1->Cube < inf2->Cube )
				return -1;
			else if ( inf1->Cube > inf2->Cube )
				return 1;
			else
				return 0;
		}
	}
	return 0;
}

double BaselineProcess::distance(double x1, double y1, double x2, double y2)
{
	double distance;

	distance = abs((int)(x1-x2)) + abs((int)(y1-y2));

	return distance;
}

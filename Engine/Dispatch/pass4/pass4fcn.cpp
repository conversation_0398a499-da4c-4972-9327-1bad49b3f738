//////////////////////////////////////////////////////////////////////
// Function Name :	Pass4fcn.cpp
// Classname :		
// Description :	Starts an independent thread for a Pass 4
//					(Assign Products to Locations) process.
// Date Created :	~5/1/98
// Author : 		sc
//////////////////////////////////////////////////////////////////////
// Inputs :			Null pointer to the socket address
// Outputs :		Null pointer
// Explanation :	This and similar function for the other Passes
//					provide multi-threading capability for Succeed's
//					Engine.
//////////////////////////////////////////////////////////////////////
// Revisions : 
//   November 6, 1998-mfs : Standard header comments added
//////////////////////////////////////////////////////////////////////
/*******************************************************
   *
   *                       NOTICE
   *
   *  THIS SOFTWARE IS THE PROPERTY OF AND CONTAINS
   *  CONFIDENTIAL INFORMATION OF SSA GLOBAL 
   *  TECHNOLOGIES, INC., AND SHALL NOT BE COPIED,
   *  USED, NOR DISCLOSED WITHOUT EXPRESS WRITTEN
   *  AUTHORIZATION.  ALTHOUGH PUBLICATION IS NOT
   *  INTENDED, IN THE EVENT OF PUBLICATION, THE
   *  FOLLOWING NOTICE IS APPLICABLE:
   *
   *  (c) COPYRIGHT 2004 SSA GLOBAL TECHNOLOGIES, INC.
   *
   *           SSA GLOBAL TECHNOLOGIES, INC.
   *
   ********************************************************/
//////////////////////////////////////////////////////////////////////

#include <stdlib.h>
#include <stdio.h>
#include <string.h>
#include "P4Process.h"

#include "pass4fcn.h"
#include "..\..\..\common\core\socket_class.h"
#include "..\..\..\common\core\debug.h"
#include "..\DataStream.h"
void *pass4threadstart(void *arg)
{
	/* ************************************************ */
	/* This is the 'main' function for pass4.  It will  */
	/* be launched on a thread and we will be required  */
	/* to do the rest.  We will monitor a socket for    */
	/* all of the information that we need, and when we */
	/* are done, we will cleanup the thread and exit.   */
	/* ************************************************ */

	Pass4Process *aP4;
	SockClass *finalSock;
	SockClass *Pass4Socket;
	char buf[512];
	char hostname[128];
	int err;

	Pass4Socket = (SockClass *)arg;

	/* ************************************************ */
	/* Start the pass4 process off with the             */
	/* communications socket that will provide all of   */
	/* the information.                                 */
	/* ************************************************ */
	aP4 = new Pass4Process( Pass4Socket );

	/* ************************************************ */
	/* This method contains all of the functionality    */
	/* that is necessary for pass four to operate.      */
	/* ************************************************ */
	aP4->Execute();

#ifdef SLOT_DEBUG
	printf("Done executing\n");
#endif

	delete aP4;

	err = gethostname(hostname, 128);
	if(err == SOCKET_ERROR){
#ifdef SLOT_DEBUG
		printf("Error getting local host name\n");
#endif 
		return(void *)NULL;
	}
	finalSock = new SockClass(hostname, 314159);
	memset(buf, 0, 512);
	sprintf(buf, "freeing-%d\n", (int)Pass4Socket);
#ifdef SLOT_DEBUG
	printf("Sending to localhost to free this thread\n");
#endif
	/***>>>
	finalSock->SendData(buf, strlen(buf));
	<<<***/
	//CHECK007
	//gfnGetDataStream()->ssData << buf;

	delete finalSock;

	return (void *)NULL;

}

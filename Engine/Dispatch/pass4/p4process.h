//////////////////////////////////////////////////////////////////////
// Function Name :	p4process.h
// Classname :		Pass4Process, prodPack, p4Location, ssaResult,
//					ssaSection, ssaLaborLevel, ssaLaborProd, ssaLaborLoc
// Description :	Header for p4process.cpp.
// Date Created :	~7/1/98
// Author : 		sc
//////////////////////////////////////////////////////////////////////
// Inputs :			None
// Outputs :		None
// Explanation :	Header for p4process.cpp.
//////////////////////////////////////////////////////////////////////
// Revisions : 
//   November 6, 1998-mfs : Standard header comments added
//////////////////////////////////////////////////////////////////////
/*******************************************************
   *
   *                       NOTICE
   *
   *  THIS SOFTWARE IS THE PROPERTY OF AND CONTAINS
   *  CONFIDENTIAL INFORMATION OF SSA GLOBAL 
   *  TECHNOLOGIES, INC., AND SHALL NOT BE COPIED,
   *  USED, NOR DISCLOSED WITHOUT EXPRESS WRITTEN
   *  AUTHORIZATION.  ALTHOUGH PUBLICATION IS NOT
   *  INTENDED, IN THE EVENT OF PUBLICATION, THE
   *  FOLLOWING NOTICE IS APPLICABLE:
   *
   *  (c) COPYRIGHT 2004 SSA GLOBAL TECHNOLOGIES, INC.
   *
   *           SSA GLOBAL TECHNOLOGIES, INC.
   *
   ********************************************************/
//////////////////////////////////////////////////////////////////////

#ifndef P4PROCESS
#define P4PROCESS

#include "..\..\..\common\core\socket_class.h"
#include "../Common/MoveCost.h"
#include "../Common/Coordinate.h"
#include "../Common/LaborCalc.h"
#include "../Common/ProductLabor.h"

#ifdef DISPATCH_EXPORTS
#include "../Common/Constants.h"
#endif

#include "../Common/Move.h"

#include <vector>
#include "..\dispatch.h"

#define BUFSIZE 2048

#define MIN_TO_HOUR 1.0f/60.0f
#define HOUR_TO_MIN 60.0f

#define LOCATION_MEM_BLOCK 100
#define PRODUCT_MEM_BLOCK 100
#define SECTION_MEM_BLOCK 100
#define LABOR_MEM_BLOCK 100
#define RESULT_MEM_BLOCK 100
#define AISLE_MEM_BLOCK 100
#define CON_PERCENTAGE 1
#define BAY_TYPE_BIN 1
#define BAY_TYPE_DRIVE_IN 2
#define BAY_TYPE_FLOOR 3
#define BAY_TYPE_FLOW 4
#define BAY_TYPE_PALLET 5
#define BAY_TYPE_PIR 6
#define BAY_TYPE_CAROUSEL 7
#define BAY_TYPE_PALLET_FLOW 8

#define LOC_LINE "======================="

using namespace std;

typedef struct {
	int bayDBID;
	double currentBayWeight;
	double bayMaxWeight;
} p4BayWeight;

typedef struct {
	int bayDBID;
	int levelDBID;
	double currentLevelWeight;
	double levelMaxWeight;
	int relativeLevel;
} p4LevelWeight;

typedef struct {
	char msg[256];
	int  prodPkDBID;
	char prodDesc[256];
	char WMSProdID[256];
	char WMSProdDetID[256];
} P4Message;


typedef struct {
	// Loc Vars:
	char   desc[250];
	int    x;
	int    y;
	int    z;
	double    w;
	double    d;
	double    h;
	// Level Vars:
	int    level;
	double  fork_fixed_insertion;
	// Section Vars:
	double  avg_replen_dist;
	double  fork_dist_var;
	double  fork_dist_fxd;
	double  fork_rate;
	double  sel_dist_var;
	double  sel_dist_fxd;
	double  sel_rate;
	double  TotalMovement;
	// Hot Spots for the section:
	int    fork_hot_x;
	int    fork_hot_y;
	int    fork_hot_z;
	int    sel_hot_x;
	int    sel_hot_y;
	int    sel_hot_z;  
	int    section_idx;
	int    BayProfID;
	// added 10/22/99 - brd
	int    BayType;
	// added 8/19/99 - UOI changes
	double  numPutsPerTrip;
	double  pickForkTrav;
	double  insertForkTrav;
	double  totalExtendedCube;
	int	   handlingMethod;	
	double  stockerRate;
	double  forkFixedInsertion;
	double  forkFixedExtraction;
	double clearance;
} ssaLaborLoc;

typedef struct {
	vector<int> facingsneeded;
	vector<int> bayProfID;
	vector<int> bayType;
} facinginfo;

typedef struct {
	int bayProfID;
	int maxFacings;
	vector<int> bayType;
	vector<double> extendedCube;
	vector<int> extendedBOH;
} levelProfileInfo;

typedef struct {
	int     dbID;
	char    desc[200];
	double   height;
	double   width;
	double   length;
	double   movement;
	int     facings;
	int     bayProfID;
	int     NumInPallet;
	double   weight;
	double   cube;
	double	containerWidth;
	double	containerLength;
	double	containerHeight;
	int		contOverrideHeight;
	int		productHi;
	int		unitOfIssue;
	int		expandedFacings;
	int		reducedFacings;
	int		rotateXAxis;
	int		rotateYAxis;
	int		rotateZAxis;
	int		ranking;
	int		levelType;
	double	rotatedHeight;
	double   rotatedWidth;
	double   rotatedLength;
	int		ChangedInPass;
	int		usable;
	double	prioritizeNum;
	char	prioritizeChar[257];
	// added 8/19/99 - UOI changes
	int		productTi;
	double	innerWidth;
	double	innerLength;
	double	innerHeight;
	double	innerWeight;
	double	eachWidth;
	double	eachLength;
	double	eachHeight;
	double	eachWeight;
	double	caseWidth;
	double	caseLength;
	double	caseHeight;
	double	caseWeight;
	double	caseMovement;
	double	palletMovement;
	double	palletWeight;
	double	casePack;
	double	caseCube;
	double	innerPack;
	char	WMSProdID[256];
	char	WMSProdDetID[256];
	int		newLocIdx;
	int		prevLocIdx;
	int		prevFacingCount;
	int		prodGroupDBID;
	char	prodGroupDesc[256];
	double	fromCost;
	double	toCost;
	double	moveCost;
	int		Assigned;
	int		currentAssignedRanking;
	double	nestWidth;
	double	nestLength;
	double	nestHeight;
	char	lastOptimizeAttribute[256];
	int		trace;
	int		costRowAdded;
	facinginfo facingsInfo;
	vector<int> facingList;
} p4ProdPack;

typedef struct {
	int     SecID;
	int     BayID;
	int		BayProfID;
	int		BayType;
	int		levelType;
//	int     RackType;
	int     RelLev;
	double   LevTime;
	int     isSelect;
	int		breakOnBay;
	int     Loc_dbid;
	char    Loc_desc[200];
	double     Loc_w;
	double     Loc_d;
	double     Loc_h;
	int     Loc_x;
	int     Loc_y;
	int     Loc_z;
	int     Assigned;
	double   SortKey;
	int		handlingMethod;
	int		VarWidth;
	int		prodWidth;
	int		vwLocCount;
	int		prodGap;
	int		prodSnap;
	int		facingGap;
	int		facingSnap;
	int		minLocWidth;
	int     Level_dbid;
	int		Aisle_dbid;
	int		Side_dbid;
	double	Aisle_rotation;
	int		pickPathDirection;
	int		overlapAllowed;
	int		caseReorientAllowed;
	// added 8/20/99 - UOI changes
	double	forkFixedInsertion;
	double	forkFixedExtraction;
	int		baySpanAllowed;
	double	levelCurrentWeight;
	double	levelMaxWeight;
	double	bayCurrentWeight;
	double	bayMaxWeight;
	int		prodGroupDBID;
	int		prevProdIdx;
	int		newProdIdx;
	int		AisleEntryX;
	int		AisleEntryY;
	int		AisleEntryZ;
	int		AisleExitX;
	int		AisleExitY;
	int		AisleExitZ;
	int		trace;
	double	clearance;
	int     adjacent_dbid1;
	int		adjacent_dbid2;
} p4Location;

typedef struct {
	int     loc_dbID;
	char    loc_desc[200];
	int     prod_dbID;
	char    prod_desc[200];
	double   cost;
	int		caseCount;
	int		vwOrder;
	double	newHeight;
	double	newWidth;
	double	newLength;
	int		varWidth;
	char	costbuffer[2560]; // formatted in CalcLabor -- intermediate costs
	bool	acceptResult;
	int		locIndex;
	int		prodIndex;
	int		altResIndex;
} ssaResult;

typedef struct {
	int     SecID;
	char	SecDesc[200];
	double   ForkFxd;
	double   ForkVar;
	double   ForkRate;
	double   SelFxd;
	double   SelVar;
	double   SelRate;
	double   AvgReplenDist;
	double   TotalMovement;
	int     ForkHotX;
	int     ForkHotY;
	int     ForkHotZ;
	int     SelHotX;
	int     SelHotY;
	int     SelHotZ;
	double   CubeConversion; // (12*12*12) = english (1000000) = metric
	int     SelDist;
	int     AvgOrdQty;
	int     ContainerQty;
	int     OrderCount;
	int     ApplyBrokenOrder;  // 0 = False, 1 = True
	int     ave_x;
	int     ave_y;
	int     loc_count;
	
	// added 8/19/99 - UOI changes
	double	numPutsPerTrip;
	double	pickForkTrav;
	double	insertForkTrav;
	double	totalExtendedCube;
	double	stockerRate;
	double	StockerDistanceFixed;
	double	StockerDistanceVariable;

} ssaSection;


typedef struct {
	int     RTid;
	int		ProfId;
	int     RelLev;
	double   Cube;
	double   Var;
	double   Fxd;
} ssaLaborLevel;	

typedef struct {
	char   desc[250];
	double  cube;
	int    NumInPallet;
	double  movement;
	double  weight;
	int    bayProfID;
	
	//added 8/20/99 - UOI changes
	double	caseMovement;
	double	palletMovement;
	double	caseCube;
	int		unitOfIssue;
	int		productTi;
	int		productHi;

	// added 10/22/99 - brd
	double   caseWeight;
	char	WMSProdID[256];
	char	WMSProdDetID[256];
} ssaLaborProd;



typedef struct {
//	int		prodIndex;
//	int		prodDbID;
	int		locIndex;
	char	locDesc[250];
	int		order;
	int		width;
	int		levelWidth;
	int		levelX;
	int		levelY;
} varWidthLoc;

/* *************************************************************** */
/* This is the header definition for the class that encapsulates   */
/* the pass4 functionality.  Everything is started by instantiating*/
/* a copy of this class and then calling the execute method.  The  */
/* class handles everything else from there.                       */
/* *************************************************************** */

typedef struct {
	int	prodDBID;
	int locDBID;
	int isPrimary;
	double totalCost;
	int caseCount;
	int unitOfIssue;
	double weight;
	int casePack;
	int innerPack;
	int numberInPallet;
	int ti;
	int hi;
	int bayDBID;
	int levelDBID;
	int relativeLevel;
	double levelMaxWeight;
	double bayMaxWeight;
} p4Solution;


typedef struct {
	int bayDBID;
	int caseQuantity;
	int unitOfIssue;
	double weight;
	int casePack;
	int innerPack;
	int numberInPallet;
	int ti;
	int hi;
	int prodDBID;

} p4PreviousBayWeight;

typedef struct {
	int prodGroupId;
	int numProds;
	int numOpenFacings;
	int numTotalFacings;
	int percentOpen;
	int lookOutsideConstraints;
} groupInfostruct;

typedef struct {
	int AisleDBID;
	int EntryX;
	int EntryY;
	int EntryZ;
	int ExitX;
	int ExitY;
	int ExitZ;
} p4AisleEntryExit;


typedef struct {
	char WMSProductID[256];
	char ProductDescription[200];
	char ProductGroupDescription[256];
	char OldLocationDescription[250];
	char NewLocationDescription[250];
	int ProductDBID;
	int OldLocationDBID;
	int NewLocationDBID;
	int ProductGroupDBID;
} p4GroupAssignment;


typedef struct {
	int FromProductDBID;
	char FromWMSProductID[256];
	char FromProductDesc[256];
	char FromProductOrigLoc[256];
	int	FromProductOrigFacings;
	int FromProductOrigBayProfileDBID;
	char FromProductNewLoc[256];
	int FromProductNewFacings;
	int FromProductNewBayProfileDBID;
	int ToProductDBID;
	char ToWMSProductID[256];
	char ToProductDesc[256];
	char ToProductOrigLoc[256];
	int	ToProductOrigFacings;
	int ToProductOrigBayProfileDBID;
	char ToProductNewLoc[256];
	int ToProductNewFacings;
	int ToProductNewBayProfileDBID;
	double FromCost;
	double ToCost;
	double MoveCost;
} p4TacticalInfo;

typedef struct {
	int slottingGroupDBID;
	int percentOpen;
	int lookOutsideConstraints;
} slotGroupInfo;

typedef struct {
	int prodIdx;
	int locIdx;
} reducibleAssignments;

class DISPATCH_API Pass4Process {
	public:
		int junk;
		double CalculateDistance(double x1, double y1, double x2, double y2);
		void TimeStamp(const char *msg);
		Pass4Process(SockClass *P4Sock);
		virtual ~Pass4Process();
	
		void Execute(void);
		void GetInitialization(void); //moved to public owing to the design change

	protected:
		void AssignUnmovedProducts();
		void ExecuteSwap(int p, int p2);
		int checkSwap(int p, int p2);
		void RestoreReducibleAssignments();
		void RemoveReducibleAssignments();
		void AddResult(int prodIndex, int locIndex, double cost, int caseCount, char * costbuffer);
		int GetRequiredFacingsCurrentBayType(int prodIndex);
		int TacticalGetExistingFacingCount(int prodIndex);
		void PopulateOpenFacings();
		void CategorizeProducts();
		void PopulatePGroupInfo();
		void ClearAssignments();
		void FindSwaps();
		void ReslotProducts();
		void SlotUnslottedProducts();
		void PopulateFacingLists();
		int TacticalGetExistingFacing(int prodIndex, int facingNumber);
		int GetNewFacingCount(int prodIndex);
		void UnassignProduct(int prodIndex);
		void ProdCopy(p4ProdPack *tempProd, p4ProdPack *prod);
		void FindAdjacentOpenFacingsSpecificLocation(vector<int> &results, int prodIndex);
		void ProcessTacticalResults();
		int isReducible(int locIndex);
		int CalcOpenPercentage(int totalFacings, int openFacings, int percentOpen, int numProds);
		int AssignLocation(int locIndex, int ProdIndex);
		void GetSlottingGroupData();
		void FindAdjacentFacings(vector<int> &results, int prodIdx);
		void PopulateAdjacentLocs();
		int RoundDown(double value);
		int TacticalRunComp();
		int RoundClosest(double value);
		void GetExtendedValues();
		int TacticalGetNeededFacings(p4ProdPack &product, int levelProfileIdx, int bayTypeIdx);
		int isLocationinProductGroup(p4ProdPack product, p4Location loc);
		void TacticalFacings(p4ProdPack &product);
		void ExecuteP4Query(string buf, vector<string> &results);
		int num_not_slottable;
		int num_to_reslot;
		int num_open;
		int num_reduceable;
		bool CheckWeight(int prodIdx, vector<int> facingIndexList, int facingCount);
		int CheckOverlap(int p, int l);
		int FindAisle(int pAisleDBID);
		void DecrementWeights(int prodIndex);
		void SetCurrentWeights();
		void ProcessPreviousBayWeights();
		void GetOneAisleEntryExit(int idx);
		int GetAisleEntryExit();
		bool CheckWeight(int prodIdx, int locIdx, int facingCount);
		int FindBay(int bayDBID);
		int FindLevel(int levelDBID);
		void ValidateFacings();
		int FindOpenAdjacentLocations(int locIdx);
		int FindLoc(int locDBID);
		int FindProd(int prodDBID);
		void ProcessSolutions();
		void SetMaxWeights();
		int GetSolutions();
		void GetOneSolution(int i);
		//void GetInitialization(void); //moved to public
		void GetOneSection(int i);
		void GetOneLaborSelect(int i);
		void GetOneLaborStocker(int i);
		int GetProducts(void);
		void GetOneProd(int idx);
		int GetAisleLoc(void);
		void GetOneRow(int idx);
		void GetRankingRule(void);
		void GetOptimizationFlags(void);
		void ResetLists(void);
		double RunComp(void);
		void SendData(void);
		int  DoesDepthHeightFit(p4ProdPack *p, p4Location *l);
		double CalcLabor(ssaLaborProd *p, ssaLaborLoc *l, int casesInPick, char * buffer);
		int  FindRTLevCubeSelect(int ProfID, int Lev, double cube);
		int  FindRTLevCubeStocker(int ProfID, int Lev, double cube);
		int  GetSectionIdx(int SecID);
		double GetCost(int prodidx, int locidx, int casesInPick, char * buffer);
		int  GetFacings(int locIndex, int prodIndex, int *caseCount, int *found_open_loc, 
			vector<int> &facingIndexList);
		void SetSortKey(p4Location *inf1);
		void GroupOnBay(void);
		void CopyProd(p4ProdPack *destP, p4ProdPack *sourceP); 
		int UpdateVarWidthBay(int locIndex, int prodIndex);
		void LogVarWidthAisle(int locIndex);
		int MaxCaseFit(p4ProdPack *p, p4Location *l);
		int NoRotation(p4ProdPack *p, p4Location *l);
		int RotateXAxis(p4ProdPack *p, p4Location *l);
		int RotateYAxis(p4ProdPack *p, p4Location *l);
		int RotateZAxis(p4ProdPack *p, p4Location *l);
		int RotateAnyAxis(p4ProdPack *p, p4Location *l);
		int VariableWidthUsed(p4ProdPack *p, p4Location *l);
		double NextSnap(double width, int gap, int snapIncr);
		int NextProdIndex(int prodIndex, int locIndex);
		void MakeProdsUniform(void);
		//int CheckWeightRestriction(p4ProdPack * prod, p4Location * loc, int numCases);  -- apparently not used anymore

		// brd - group slotting
		int GroupRunComp();
		int GroupAssignProducts(int fromProdIdx, int toLocIdx);
		int GroupGetFacings(int li, int prodIndex);
		int GetMaxFacingCount(int prodIndex);
		int CheckProductInLocation(int prodIndex, int locIndex);
		int LocsAreAdjacent(int loc1Idx, int loc2Idx);
		int GetExistingFacing(int prodIndex, int facingNumber);
		int GetExistingFacingCount(int prodIndex);

		// brd - tactical slotting
		void Pass4Process::TacticalAddInfo(int fromProdIdx, int toProdIdx, 
								   double fromCost, double toCost, double moveCost);
		double TacticalCalcMoveCost(int fromProdIdx, int toLocIdx, int fromFacingCount, int toFacingCount);
		double TacticalCalcCost(int prodIndex, int locIndex, int facingCount);
		int TacticalCheckHurdleRate(int p, int p2);
		int TacticalGetFacings(int li, int prodIndex);
		double CalcMoveCost(CMove *pMove, CLaborCalc *pLaborCalc);
		void BuildMoveCostData(int prodIdx, int locIdx, int caseQuantity, CMoveCost &moveCost);

		SockClass *p4sock;

		char msg_buf[BUFSIZE];
		int maxFacingsThisRun;

		p4Location *mem_locs;
		vector<int> reduceable_facings;
		vector<int> open_facings;

		int     mem_loc_count;

		p4ProdPack *mem_prods;
		vector<int> mem_prods_to_reslot;
		vector<int> mem_not_reslottable_products;
		vector<int> mem_unslotted_products;
		vector<int> mem_prods_nopgroup;
		vector<reducibleAssignments> redAssignments;
		vector<slotGroupInfo> sgInfo;
		vector<groupInfostruct> groupInfo; 

		int       mem_prod_count;

		ssaResult *mem_result;
		int        mem_result_count;	
		int        mem_result_max;

		int mem_result_old_count;
		ssaResult *mem_result_old;

		ssaSection *mem_sections;
		int         mem_section_count;
		
		ssaLaborLevel *mem_labor_select;
		int            mem_labor_select_count;

		ssaLaborLevel *mem_labor_stocker;
		int            mem_labor_stocker_count;

		P4Message	*p4Messages;

		varWidthLoc *vwLoc;
		int			vw_loc_count;
		int			vw_loc_max;

		int			*vwAisle;
		int			vw_aisle_count;
		int			vw_aisle_max;

		int mem_avg_x;
		int mem_avg_y;
		int mem_current_section_id;

		double maxLocHeight;
		double maxLocWidth;
		double maxLocDepth;
		double maxPalLocHeight;
		double maxPalLocWidth;
		double maxPalLocDepth;

		/* ***************************************** */
		/* Optimization flags                        */
		/* ***************************************** */
		int		overlapProds;
		int		sortOptFlag;
		int		optType;
		bool	tacticalSlotting;
		char	optMsgString[257];

		int		overlapBayTypes[100];
		int		numOverlapTypes;

		int		noBayBreakTypes[100];
		int		numNoBayBreakTypes;

		int		variableWidthAllowed;
		int		caseReorientAllowed;

		int		optimizeType;
		int		constraintType;
		double	constraintAmount;
		int		m_slotnew;
		int		m_reslot;
		int		m_findswaps;
		int		timeHorizonUnits;
		int		timeHorizonValue;
		FILE *dummy_file;
		FILE *p4ini_file;

		//FILE *msg_file;
		FILE *p4TraceFile;
		int P4LogModeFlag;
		char P4LogUsr[256];
		char fileName[1024];
		char logPath[1024];
		int numMessages;
		int currentRanking;
		double utilMinimum;
		double utilization;
		double utilStart;
		double utilDecrement;
		int myCurrentRanking;
		int ignoreWeight;
		int changedOnly;
		int ignoreRankings;
		int ignoreRankingsFlag;

		int mem_solution_count;
		p4Solution *mem_solutions;

		int mem_bayweight_count;
		p4BayWeight *mem_bay_weights;

		int mem_levelweight_count;
		p4LevelWeight *mem_level_weights;

		int bay_weight_counter;
		int level_weight_counter;

		int mem_aisleentryexit_count;
		p4AisleEntryExit *mem_aisleentryexits;

		int mem_groupassignment_count;
		int mem_groupassignment_max;
		p4GroupAssignment *mem_group_assignments;

		int mem_tacticalinfo_count;
		int mem_tacticalinfo_max;
		p4TacticalInfo *mem_tactical_info;

		vector<levelProfileInfo> levelProfiles;

		
private:
	double distance(double x1, double y1, double x2, double y2);
}; 

class sortProdsByMovement // function object for sort usage
{
public:
   bool operator () (const p4ProdPack p1, 
      const p4ProdPack p2) const
   {
      return p1.movement > p2.movement;
   };
};

class sortLocsByCost // function object for sort usage
{
public:
   bool operator () (const p4Location l1, 
      const p4Location l2) const
   {
      return l1.SortKey < l2.SortKey;
   };
};



#endif // P4PROCESS defined

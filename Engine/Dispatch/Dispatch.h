
// The following ifdef block is the standard way of creating macros which make exporting 
// from a DLL simpler. All files within this DLL are compiled with the DISPATCH_EXPORTS
// symbol defined on the command line. this symbol should not be defined on any project
// that uses this DLL. This way any other project whose source files include this file see 
// DISPATCH_API functions as being imported from a DLL, wheras this DLL sees symbols
// defined with this macro as being exported.
#ifdef DISPATCH_EXPORTS
#define DISPATCH_API __declspec(dllexport)
#else
#define DISPATCH_API __declspec(dllimport)
#endif

// This class is exported from the Dispatch.dll
class DISPATCH_API CDispatch {
public:
	CDispatch(void);
	// TODO: add your methods here.
};


#include <sstream>
using namespace std;

class DataStream;  //forward declaration
DataStream* gfnGetDataStream();

#ifndef MAX_INPUT_DATA_LEN
#define MAX_INPUT_DATA_LEN	1000
#endif
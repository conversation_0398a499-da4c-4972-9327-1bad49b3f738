/*
 | p1.dat  -  data file for p1.c
 |
 |     Input data for Pass 1
*/

static RACKTYPE Racks[] = {
	{ 1.41, 5.63, 0, "Case Static Shelf, 1 Facing" },
	{ 4.22, 16.88, 0, "Case Static Shelf, 2 Facings" },
	{ 5.29, 21.16, 0, "Case Flow Rack, 1 Facing" },
	{ 7.03, 28.13, 0, "Case Static Shelf, 3 Facings" },
	{ 9.84, 39.38, 0, "Case Static Shelf, 4 Facings" },
	{ 10.58, 42.32, 0, "Case Flow Rack, 2 Facings" },
	{ 15.87, 63.48, 0, "Case Flow Rack, 3 Facings" },
	{ 18.06, 72.22, 0, "<PERSON><PERSON><PERSON>, 3 Level, 1 Facing" },
	{ 21.16, 84.64, 0, "Case Flow Rack, 4 Facings" },
	{ 46.67, 116.67, 0, "<PERSON><PERSON><PERSON>ck, 2 Level, 1 Facing" },
	{ 73.33, 330.00, 0, "<PERSON><PERSON><PERSON>, 1 Level, 1 Facing" },
	{ 93.33, 233.33, 0, "<PERSON><PERSON><PERSON>, 2 Level, 2 Facings" },
	{ 140.00, 350.00, 0, "<PERSON><PERSON><PERSON>, 2 Level, 3 Facings" },
	{ 146.67, 660.00, 0, "<PERSON>llet <PERSON>ck, 1 Level, 2 Facings" },
	{ 183.33, 696.67, 0, "Drive In, 2 Deep, 1 Facing" },
	{ 183.33, 770.00, 0, "Floor, 3 Deep, 1 Facing" },
	{ 183.33, 1063.33, 0, "Drive In, 3 Deep, 1 Facing" },
	{ 186.67, 466.67, 0, "Pallet Rack, 2 Level, 4 Facings" },
	{ 220.00, 990.00, 0, "Pallet Rack, 1 Level, 3 Facings" },
	{ 293.33, 1320.00, 0, "Pallet Rack, 1 Level, 4 Facings" },
	{ 550.00, 1430.00, 0, "Drive In, 2 Deep, 2 Facings" },
	{ 550.00, 1870.00, 0, "Floor, 3 Deep, 2 Facings" },
	{ 550.00, 2163.33, 0, "Drive In, 3 Deep, 2 Facings" },
	{ 916.67, 2970.00, 0, "Floor, 3 Deep, 3 Facings" },
	{ 916.67, 3263.33, 0, "Drive In, 3 Deep, 3 Facings" },
	{ 1283.33, 4070.00, 0, "Floor, 3 Deep, 4 Facings" },
	{ 1283.33, 4363.33, 0, "Drive In, 3 Deep, 4 Facings" }
};
#define ITEMCOUNT (sizeof(Racks) / sizeof(Racks[0]))


static int OptFlag = OPTIMIZE_FOR_CUBE;		/* default: CUBE */
static double regression_a = 0.9720788;		/* "A" regression coef for logs */
static double regression_b = 0.61786669;	/* "B" regression coef for logs */
static double upper_y = 0.22;				/* distance to upper bound from rgr line */
static double lower_y = -0.02;				/* distance to lower bound from rgr line */
static ItemCount = ITEMCOUNT;				/* ITEMCOUNT defined in p1.dat */


			/* optional data area for logs */

static RACKTYPE LogRacks[ITEMCOUNT];


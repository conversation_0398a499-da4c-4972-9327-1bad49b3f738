
    Calling Interface for P1

Inputs:

double InputArray[7] = {
	XCube,			/* Extended Cube, 0 to 1E20 */
	BOH,			/* (Max) Balance on Hand, 0 to 1E20 */
	Hazard,			/* Hazard Flag: 0 -> NO, 1 -> YES */
	HUA,			/* Handling Unit Attribute, 0->U, 1->I, 2->C, 3->P */
	PTB,			/* Pick-to-Belt Flag, 0 -> NO, 1-> YES */
	CubeCuFt,		/* Cube Cubic Feet, 0 to 1E20 */
	CubeMovement	/* Cube Movement, 0 to 1E20 */
};

Outputs:

double OutputArray[1] = {
	RackIndex		/* Rack Type Index, 0 to xx */
};

/*

	NOTES:

	Ranges given for XCube, BOH, CubeCuFt, and CubeMovement are fictitious.
	Actually the half-open interval [0, infinity) is correct. The definition
	for each quantity is to be supplied by <PERSON><PERSON>.

	The Hazard input is a double-precision version of an integer flag,
	0 if NOT a hazard, and 1 if a hazard.

	The HUA input is a double-precision version of an integer flag:
	0 if "U" (Individual Selling Unit", 1 if "I" (Interpack", 2 if "C"
	(Case), and 3 if "P" (pallet).

	The PTB input is a double-precision version of an integer flag,
	0 if NOT pick-to-belt, and 1 if pick-to-belt.

	The "Rack Type Index" of an item is its index position in the
	"Rack Type Table". The table lists the rack type options for each of
	the warehouse subareas (for the "Virtual Warehouse", these are: "Hazard",
	"Carousel", "Pick-to-Belt", "Pick-in-Reserve", and "Conventional" (the
	default)). This is equivalent to the concatenation of the five separate
	tables into one. Thus the Rack Type Index as returned by the CubiCalc
	run-time DLL must be adjusted to determine the index into one of the
	constituent tables: For "Hazard", subtract zero; for "Carousel", subtract
	the number of Hazard entries; for "Pick-to-Belt", subtract the sum of
	the number of Hazard entries and the number of Carousel entries; for
	"Pick-in-Reserve", subtract the pick-to-belt value plus the number of
	pick-to-belt entries; finally, for the default, subtract the
	pick-in-reserve value plus the number of pick-in-reserve entries.

	After initializing DLL, use run call to compute outputs from inputs.

*/


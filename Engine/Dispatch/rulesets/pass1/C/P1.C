/*
 | p1.c  -  Pass 1 Code
 |
 |    Written by:   <PERSON>, Ph.D.
 |    For:          EXE Technologies
 |    On:           31 March 1998
*/

#include <stdio.h>
#include <stdlib.h>
#include <errno.h>
#include <math.h>



#include "p1.h"
#include "p1.dat"



extern char *basename(char *ptr);



static int best_in_stack(int idx, double boh);
static double getval(char *pgmname, char *arg);
static int mktable(char *pgmname, int count);



void main(int argc, char *argv[]) {

	int n, fix, pick, region;
	double xcube, boh, rgr_y;
	char *pgmname, *pp;


	pgmname = basename(argv[0]);

			/* check for cmd-line switch */

	if (argc != 3 && argc != 4) {
		printf("%s: usage: %s [-c l] xcube boh\n", pgmname, pgmname);
		exit(1);
	}

	if (argc == 4) {
		fix = 1;
		if (argv[1][0] == '-') {
			if (argv[1][1] == 'c' || argv[1][1] == 'C')
												 OptFlag = OPTIMIZE_FOR_CUBE;
			else if (argv[1][1] == 'l' || argv[1][1] == 'L')
												 OptFlag = OPTIMIZE_FOR_LABOR;
			else {
				printf("%s: unknown switch: '%s'\n", argv[1]);
				exit(1);
			}
		}
	}
	else fix = 0;

			/* extract xcube and boh values */

	xcube = getval(pgmname, argv[1 + fix]);
	boh = getval(pgmname, argv[2 + fix]);

			/* build logarithmic table */

	if (mktable(pgmname, ItemCount) < 0) exit(1);


					/* -- P1 Starts Here -- */


			/* condition arguments to chart domain */

	if (xcube < Racks[0].xcube) xcube = Racks[0].xcube;
	else if (xcube > Racks[ItemCount - 1].xcube) xcube = Racks[ItemCount - 1].xcube;
	xcube = log10(xcube);
	boh = log10(boh);

			/* calculate regression line ordinate */
			/* see if need to "optimize"; if so, do it */

	rgr_y = regression_a * xcube + regression_b;
	if (rgr_y + upper_y < boh) {
		region = UPPER_REGION;
		if (OptFlag == OPTIMIZE_FOR_LABOR)
				xcube = (boh - regression_b - upper_y) / regression_a;
		else boh = xcube * regression_a + regression_b + upper_y;
	}
	else if (rgr_y - lower_y > boh) {
		region = LOWER_REGION;
		if (OptFlag == OPTIMIZE_FOR_LABOR)
				xcube = (boh - regression_b - lower_y) / regression_a;
		else boh = xcube * regression_a + regression_b + lower_y;
	}
	else region = BAND_REGION;

			/* locate table entry containing our abscissa */
			/* determine best fit to available data */

	for (n = 1; n < ItemCount; ++n) if (xcube <= LogRacks[n].xcube) break;

	if (xcube - LogRacks[n - 1].xcube <= LogRacks[n].xcube - xcube)
											pick = best_in_stack(n - 1, boh);
	else pick = best_in_stack(n, boh);

			/* spill the beans */

	printf("\tOptimizing for %s.\n",
							 OptFlag == OPTIMIZE_FOR_LABOR ? "LABOR" : "CUBE");
	if (region == UPPER_REGION) {
		printf("\tItem in excess inventory region: ");
		printf("moved to XCUBE=%g, BOH=%g\n", pow10(xcube), pow10(boh));
	}
	else if (region == LOWER_REGION) {
		printf("\tItem in underutilization region: ");
		printf("moved to XCUBE=%g, BOH=%g\n", pow10(xcube), pow10(boh));
	}
	else printf("\tItem in Good Practice Band.\n");
	printf("\tI choose %s\n", Racks[pick].name);
}


static int best_in_stack(int idx, double boh) {

	int count, n, pick;
	double diff, val, ftmp;


	count = 1;
	val = LogRacks[idx].xcube;
	n = idx + 1;
	while (LogRacks[n].xcube == val) {
		++n;
		++count;
	}

	pick = idx;
	diff = LogRacks[idx].boh - boh;
	if (diff < 0.0) diff = -diff;
	for (n = 1; n < count; ++n) {
		ftmp = LogRacks[idx + n].boh - boh;
		if (ftmp < 0.0) ftmp = -ftmp;
		if (ftmp < diff) {
			pick = idx + n;
			diff = ftmp;
		}
	}

	return (pick);
}


static double getval(char *pgmname, char *arg) {

	double val;
	char *pp;


	errno = 0;
	val = strtod(arg, &pp);
	if (errno != 0) {
		printf("%s: ?ERRNO reports %d\n", pgmname, errno);
		exit(1);
	}

	if (pp == arg) {
		printf("%s: ?can't convert '%s' to numeric form\n", pgmname, arg);
		exit(1);
	}

	if (*pp != (char)0) {
		printf("%s: ?bad numeric value: '%s'\n", pgmname, arg);
		exit(1);
	}

	if (val <= 0.0) {
		printf("%s: ?value is negative: '%s'\n", pgmname, arg);
		exit(1);
	}

	return (val);
}



static int mktable(char *pgmname, int count) {

	int n;


	for (n = 0; n < ItemCount; ++n) {
		if (Racks[n].xcube <= 0.0) {
			printf("%s: error in Racks table xcube value, position %d\n",
														 			pgmname, n);
			return (-1);
		}
		else LogRacks[n].xcube = log10(Racks[n].xcube);

		if (Racks[n].boh <= 0.0) {
			printf("%s: error in Racks table BOH value, position %d\n",
														 			pgmname, n);
			return (-1);
		}
		else LogRacks[n].boh = log10(Racks[n].boh);
	}

	return (0);
}


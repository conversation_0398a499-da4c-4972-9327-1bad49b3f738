#include <stdio.h>
#include <stdlib.h>
#include <math.h>



struct datapair {
	double x;
	double y;
};
typedef struct datapair DATAPAIR;



static char lbuf[128];
static DATAPAIR regdata[2000];
static DATAPAIR logdata[2000];



char *basename(char *fname);



void main(int argc, char *argv[]) {

	int n, DataCount, high_y, low_y, high_r, low_r, high_l, low_l;
	double val, val2;
	double a_coeff, b_coeff;
	double highval_y, lowval_y, highval_r, lowval_r, highval_l, lowval_l;
	double sum_x, sum_y, sum_xy, sumsq_x;
	double Upper_X, Lower_X, Upper_B, Lower_B;
	FILE *fd;
	char *pgmname, *pp;


			/* set program name; detect improper usage */

	pgmname = basename(argv[0]);

	if (argc != 2) {
		printf("%s: usage: %s datafile\n", pgmname, pgmname);
		exit (1);
	}

			/* load the data arrays */

	fd = fopen(argv[1], "r");
	if (fd == (FILE *)0) {
		printf("%s: ?can't open '%s'\n", pgmname, argv[1]);
		exit (1);
	}

	n = 0;
	errno = 0;
	while (fgets(lbuf, 128, fd) == lbuf) {
		val = strtod(lbuf, &pp);
		if (errno != 0) {
			printf("%s: ?errno reports %d, line %d\n", pgmname, errno, n + 1);
			fclose(fd);
			exit (1);
		}
		if (val <= 0.0) {
			printf("%s: ?nonpositive XCube value, line %d\n", pgmname, n + 1);
			fclose(fd);
			exit (1);
		}
		regdata[n].x = val;
		logdata[n].x = log10(val);

		val = strtod(pp, &pp);
		if (errno != 0) {
			printf("%s: ?errno reports %d, line %d\n", pgmname, errno, n + 1);
			fclose(fd);
			exit (1);
		}
		if (val <= 0.0) {
			printf("%s: ?nonpositive BOH value, line %d\n", pgmname, n + 1);
			fclose(fd);
			exit (1);
		}
		regdata[n].y = val;
		logdata[n].y = log10(val);

		++n;
	}
	DataCount = n;
	fclose(fd);

	for (n = 0; n < DataCount; ++n) {
		logdata[n].x = log10(regdata[n].x);
		logdata[n].y = log10(regdata[n].y);
	}

			/* compute and output the "pure" regression numbers */

	sum_x = sum_y = sum_xy, sumsq_x = 0.0;
	for (n = 0; n < DataCount; ++n) {
		sum_x += regdata[n].x;
		sum_y += regdata[n].y;
		sum_xy += (regdata[n].x * regdata[n].y);
		sumsq_x += (regdata[n].x * regdata[n].x);
	}

	val = DataCount * sumsq_x - sum_x * sum_x;
	a_coeff = (DataCount * sum_xy - sum_x * sum_y) / val;
	b_coeff = (sumsq_x * sum_y - sum_x * sum_xy) / val;

	high_r = low_r = 0;
	highval_r = lowval_r = regdata[0].x;
	for (n = 1; n < DataCount; ++n) {
		if (regdata[n].x > highval_r) {
			highval_r = regdata[n].x;
			high_r = n;
		}
		if (regdata[n].x < lowval_r) {
			lowval_r = regdata[n].x;
			low_r = n;
		}
	}
	Upper_X = highval_r;
	Lower_X = lowval_r;

	high_l = low_l = 0;
	highval_l = lowval_l = regdata[0].y;
	for (n = 1; n < DataCount; ++n) {
		if (regdata[n].y > highval_l) {
			highval_l = regdata[n].y;
			high_l = n;
		}
		if (regdata[n].y < lowval_l) {
			lowval_l = regdata[n].y;
			low_l = n;
		}
	}
	Upper_B = highval_l;
	Lower_B = lowval_l;

	high_y = low_y = 0;
	highval_y = lowval_y = 0.0;
	for (n = 0; n < DataCount; ++n) {
		val = regdata[n].x * a_coeff + b_coeff;
		if (regdata[n].y - val > highval_y) {
			highval_y = regdata[n].y - val;
			high_y = n;
		}
		if (regdata[n].y - val < lowval_y) {
			lowval_y = regdata[n].y - val;
			low_y = n;
		}
	}

	printf("Data for 'regular' elements:\n");
	printf("    SUM xi   = %g\n", sum_x);
	printf("    SUM yi   = %g\n", sum_y);
	printf("    SUM xiyi = %g\n", sum_xy);
	printf("    SUM xixi = %g\n", sumsq_x);
	printf("\n    Regression (a, b) = (%g, %g)\n", a_coeff, b_coeff);
	printf("    XCube: MAX %g (idx %d), MIN %g (idx %d)\n",
								 		highval_r, high_r, lowval_r, low_r);
	printf("    BOH: MAX %g (idx %d), MIN %g (idx %d)\n",
								 		highval_l, high_l, lowval_l, low_l);
	printf("    y-deviation: MAX %g (idx %d), MIN %g (idx %d)\n",
				 						highval_y, high_l, lowval_y, low_l);

			/* compute and output the "log" regression numbers */

	sum_x = sum_y = sum_xy = sumsq_x = 0.0;
	for (n = 0; n < DataCount; ++n) {
		sum_x += logdata[n].x;
		sum_y += logdata[n].y;
		sum_xy += (logdata[n].x * logdata[n].y);
		sumsq_x += (logdata[n].x * logdata[n].x);
	}

	val = DataCount * sumsq_x - sum_x * sum_x;
	a_coeff = (DataCount * sum_xy - sum_x * sum_y) / val;
	b_coeff = (sumsq_x * sum_y - sum_x * sum_xy) / val;

	high_r = low_r = 0;
	highval_r = lowval_r = logdata[0].x;
	for (n = 1; n < DataCount; ++n) {
		if (logdata[n].x > highval_r) {
			highval_r = logdata[n].x;
			high_r = n;
		}
		if (logdata[n].x < lowval_r) {
			lowval_r = logdata[n].x;
			low_r = n;
		}
	}

	high_l = low_l = 0;
	highval_l = lowval_l = logdata[0].y;
	for (n = 0; n < DataCount; ++n) {
		if (logdata[n].y > highval_l) {
			highval_l = logdata[n].y;
			high_l = n;
		}
		if (logdata[n].y < lowval_l) {
			lowval_l = logdata[n].y;
			low_l = n;
		}
	}

	high_y = low_y = 0;
	highval_y = lowval_y = 0.0;
	for (n = 0; n < DataCount; ++n) {
		val = logdata[n].x * a_coeff + b_coeff;
		if (logdata[n].y - val > highval_y) {
			highval_y = logdata[n].y - val;
			high_y = n;
		}
		if (logdata[n].y - val < lowval_y) {
			lowval_y = logdata[n].y - val;
			low_y = n;
		}
	}

	printf("\nData for 'log' elements:\n");
	printf("    SUM xi   = %g\n", sum_x);
	printf("    SUM yi   = %g\n", sum_y);
	printf("    SUM xiyi = %g\n", sum_xy);
	printf("    SUM xixi = %g\n", sumsq_x);
	printf("\n    Regression (a, b) = (%g, %g)\n", a_coeff, b_coeff);
	printf("    MAX XCube = %g (idx %d), MIN XCube = %g (idx %d)\n",
								 		highval_r, high_r, lowval_r, low_r);
	printf("    MAX BOH   = %g (idx %d), MIN BOH = %g (idx %d)\n",
								 		highval_l, high_l, lowval_l, low_l);
	printf("    y-deviation: MAX %g (idx %d), MIN %g (idx %d)\n",
				 						highval_y, high_l, lowval_y, low_l);

			/* output the summary numbers */

	printf("\nSummary Data:\n");
	printf("    Datapoints:  %d\n", DataCount);
	printf("    Regression Coefficients:  (%g %g)\n", a_coeff, b_coeff);
	printf("    Upper/Lower Y:  %g %g (%g)\n",
										 highval_y, lowval_y, fabs(lowval_y));
	printf("    Upper/Lower X:  %g %g\n", Upper_X, Lower_X);
	printf("    Upper/Lower B:  %g %g\n", Upper_B, Lower_B);
}


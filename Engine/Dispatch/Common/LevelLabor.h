// LevelLabor.h: interface for the CLevelLabor class.
//
//////////////////////////////////////////////////////////////////////

#if !defined(AFX_LEVELLABOR_H__7B9BA7F7_7450_11D4_9EAD_00C04FAC149C__INCLUDED_)
#define AFX_LEVELLABOR_H__7B9BA7F7_7450_11D4_9EAD_00C04FAC149C__INCLUDED_

#if _MSC_VER > 1000
#pragma once
#endif // _MSC_VER > 1000

class CLevelLabor  
{
public:
	CLevelLabor(int bayProfileID, int relativeLevel, double cube, double fixedFactor, double variableFactor);
	CLevelLabor();
	virtual ~CLevelLabor();
	CLevelLabor(const CLevelLabor & other);
	CLevelLabor& operator=(const CLevelLabor & other);
	double m_FixedFactor;
	double m_VariableFactor;
	int m_BayProfileDBID;
	int m_RelativeLevel;
	double m_Cube;

};

#endif // !defined(AFX_LEVELLABOR_H__7B9BA7F7_7450_11D4_9EAD_00C04FAC149C__INCLUDED_)

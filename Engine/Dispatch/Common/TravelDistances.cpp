// TravelDistances.cpp: implementation of the CTravelDistances class.
//
//////////////////////////////////////////////////////////////////////

#include "TravelDistances.h"

//////////////////////////////////////////////////////////////////////
// Construction/Destruction
//////////////////////////////////////////////////////////////////////

CTravelDistances::CTravelDistances()
{

}

CTravelDistances::~CTravelDistances()
{

}

void CTravelDistances::Create(double fromAisle, double betweenAisles, double toAisle)
{
	m_FromAisle = fromAisle;
	m_BetweenAisles = betweenAisles;
	m_ToAisle = toAisle;
}

double CTravelDistances::Total()
{
	return m_FromAisle + m_BetweenAisles + m_ToAisle;
}

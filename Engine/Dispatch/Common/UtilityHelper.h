// UtilityHelper.h: interface for the CUtilityHelper class.
//
//////////////////////////////////////////////////////////////////////

#if !defined(AFX_UTILITYHELPER_H__52A25ABF_1B6C_433F_8764_D4EC70E45C61__INCLUDED_)
#define AFX_UTILITYHELPER_H__52A25ABF_1B6C_433F_8764_D4EC70E45C61__INCLUDED_

#if _MSC_VER > 1000
#pragma once
#endif // _MSC_VER > 1000

#include <vector>
#include <string>

class CUtilityHelper  
{
public:
	int ParseString(const std::string& inString, const std::string& delimiter, std::vector<std::string> &strings);
	CUtilityHelper();
	virtual ~CUtilityHelper();

};

#endif // !defined(AFX_UTILITYHELPER_H__52A25ABF_1B6C_433F_8764_D4EC70E45C61__INCLUDED_)

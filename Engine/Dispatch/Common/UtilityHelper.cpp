// UtilityHelper.cpp: implementation of the CUtilityHelper class.
//
//////////////////////////////////////////////////////////////////////

#include "UtilityHelper.h"

//////////////////////////////////////////////////////////////////////
// Construction/Destruction
//////////////////////////////////////////////////////////////////////

CUtilityHelper::CUtilityHelper()
{

}

CUtilityHelper::~CUtilityHelper()
{

}

int CUtilityHelper::ParseString(const std::string& inString, const std::string& delimiter, 
								std::vector<std::string> &strings)
{
	int idx, lastIdx;

	idx = lastIdx = 0;

	strings.erase(strings.begin(), strings.end());

	idx = inString.find(delimiter, 0);
	while (idx != inString.npos) {
		strings.push_back(inString.substr(lastIdx, idx-lastIdx));
		lastIdx = idx+1;
		idx = inString.find(delimiter, lastIdx);
	}

	if (lastIdx < inString.length())
		strings.push_back(inString.substr(lastIdx, inString.length()-lastIdx));
	
	return strings.size();

}

// LevelLabor.cpp: implementation of the CLevelLabor class.
//
//////////////////////////////////////////////////////////////////////

#include "LevelLabor.h"

//////////////////////////////////////////////////////////////////////
// Construction/Destruction
//////////////////////////////////////////////////////////////////////

CLevelLabor::CLevelLabor()
{
	m_BayProfileDBID = 0;
	m_RelativeLevel = 0;
	m_Cube = 0.0;
	m_FixedFactor = 0.0;
	m_VariableFactor = 0.0;
}

CLevelLabor::~CLevelLabor()
{

}

CLevelLabor::CLevelLabor(int bayProfileDBID, int relativeLevel, double cube, double fixedFactor, double variableFactor)
{
	m_BayProfileDBID = bayProfileDBID;
	m_RelativeLevel = relativeLevel;
	m_Cube = cube;
	m_FixedFactor = fixedFactor;
	m_VariableFactor = variableFactor;

	return;
}

CLevelLabor::CLevelLabor(const CLevelLabor & other)
{
	m_BayProfileDBID = other.m_BayProfileDBID;
	m_Cube = other.m_Cube;
	m_FixedFactor = other.m_FixedFactor;
	m_RelativeLevel = other.m_RelativeLevel;
	m_VariableFactor = other.m_VariableFactor;
}

CLevelLabor& CLevelLabor::operator=(const CLevelLabor & other)
{
	m_BayProfileDBID = other.m_BayProfileDBID;
	m_Cube = other.m_Cube;
	m_FixedFactor = other.m_FixedFactor;
	m_RelativeLevel = other.m_RelativeLevel;
	m_VariableFactor = other.m_VariableFactor;
	
	return *this;
}
// Coordinate.h: interface for the CCoordinate class.
//
//////////////////////////////////////////////////////////////////////

#if !defined(AFX_COORDINATE_H__7B9BA7F8_7450_11D4_9EAD_00C04FAC149C__INCLUDED_)
#define AFX_COORDINATE_H__7B9BA7F8_7450_11D4_9EAD_00C04FAC149C__INCLUDED_

#if _MSC_VER > 1000
#pragma once
#endif // _MSC_VER > 1000

class CCoordinate  
{
public:
	CCoordinate();
	CCoordinate(double x, double y, double z);
	virtual ~CCoordinate();
	CCoordinate& operator=(const CCoordinate & other);
	CCoordinate(const CCoordinate & other);
	double m_x;
	double m_y;
	double m_z;
};

#endif // !defined(AFX_COORDINATE_H__7B9BA7F8_7450_11D4_9EAD_00C04FAC149C__INCLUDED_)

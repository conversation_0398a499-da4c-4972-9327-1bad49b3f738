// LaborCalc.cpp: implementation of the CLaborCalc class.
//
//////////////////////////////////////////////////////////////////////

#include "LaborCalc.h"
#include "../Common/constants.h"
#include <string.h>
#include <stdlib.h>
#include <stdio.h>
#include "../Common/Constants.h"

const double MINUTES_PER_HOUR = 60;
const int PARM_BUFFER_SIZE = 256;

int CompareDistances(const void *t1, const void *t2);

//////////////////////////////////////////////////////////////////////
// Construction/Destruction
//////////////////////////////////////////////////////////////////////

CLaborCalc::CLaborCalc()
{
	//fprintf(stdout, "Creating CLaborCalc\n");
	//fflush(stdout);
	m_BetweenAisleDistance = 0.0;
	m_BetweenLocDistance = 0.0;
	m_CostTraceBuffer = NULL;
	m_Divisor = 12;		// default to imperial
	m_ForkLaborRate = 0.0;
	m_FromAisleDistance= 0.0;
	m_StockerLabor = NULL;
	m_StockerLaborCount = 0;
	m_StockerLaborRate = 0.0;
	m_ToAisleDistance = 0.0;

}

CLaborCalc::~CLaborCalc()
{

	//fprintf(stdout, "Freeing CLaborCalc\n");
	//fflush(stdout);
	if (m_StockerLaborCount > 0)
		delete [] m_StockerLabor;
	
}

double CLaborCalc::CalcMoveCost(char **pParameters)
{
	
	double stockerHandlingTime, forkHandlingTime;
	double forkTravelDistance, forkTravelTime, forkTravelCost;
	double stockerTravelDistance, stockerTravelTime, stockerTravelCost;
	double forkHandlingCost, stockerHandlingCost;
	double totalCost;

	//printf("In CalcMoveCost: %d\n", m_FromMoveCost.m_ProductDBID);

	ParseMoveParameters(pParameters);

	// Set the correct rates, etc
	Initialize();

	if (m_MoveType != MOVE_NORMAL)
	
		totalCost = CalcSimpleMoveCost();

	else {
		
		forkTravelDistance = CalculateForkTravelDistance();
		forkTravelTime = CalculateForkTravelTime(forkTravelDistance);
		forkTravelCost = CalculateForkCost(forkTravelTime);
		
		forkHandlingTime = CalculateForkHandlingTime();
		forkHandlingCost = CalculateForkCost(forkHandlingTime);
		
		stockerTravelDistance = CalculateStockerTravelDistance();
		stockerTravelTime = CalculateStockerTravelTime(stockerTravelDistance);
		stockerTravelCost = CalculateStockerCost(stockerTravelTime);
		
		stockerHandlingTime = CalculateTotalStockerHandlingTime();
		stockerHandlingCost = CalculateStockerCost(stockerHandlingTime);
		
		
		totalCost = forkTravelCost + forkHandlingCost + stockerTravelCost + stockerHandlingCost;
	}

	return totalCost;


}

double CLaborCalc::CalcSimpleMoveCost()
{
	double totalCost;
	
	switch (m_MoveType) {
	case MOVE_ADD_LOC:
	case MOVE_FROM_TEMP:
		totalCost = (m_ToMoveCost.m_ForkFixedInsertion / MINUTES_PER_HOUR) * m_ForkLaborRate;
		break;
	case MOVE_DEL_LOC:
	case MOVE_TO_TEMP:
		totalCost = (m_FromMoveCost.m_ForkFixedExtraction / MINUTES_PER_HOUR) * m_ForkLaborRate;
		break;

	}

	return totalCost;

}

double CLaborCalc::CalculateForkTravelDistance()
{
	double forkTravelDistance = 0.0;

	if (IsSameAisle()) {
		// If moving within an aisle, if one of the locations is not stocker, use a fork
		if (! IsStocker(m_FromMoveCost.m_BayType) || ! IsStocker(m_ToMoveCost.m_BayType))
			forkTravelDistance += m_BetweenLocDistance * 2;
	}
	else {
		if (! IsStocker(m_FromMoveCost.m_BayType))
			forkTravelDistance += m_FromAisleDistance * 2;

		if (! IsStocker(m_ToMoveCost.m_BayType))
			forkTravelDistance += m_ToAisleDistance * 2;

		if (IsStocker(m_FromMoveCost.m_BayType) && IsStocker(m_ToMoveCost.m_BayType)) {
			if (! IsSameSection())
				forkTravelDistance += m_BetweenAisleDistance * 2;
		}
		else {
			forkTravelDistance += m_BetweenAisleDistance * 2;
		}

	}

	return forkTravelDistance;

}

double CLaborCalc::CalculateForkTravelTime(double forkTravelDistance)
{
	double temp1, temp2;
	double forkTravelTime = 0.0;

	if (forkTravelDistance > 0) {
		temp1 = forkTravelDistance * m_FromMoveCost.m_ForkDistanceVariable + m_FromMoveCost.m_ForkDistanceFixed;
		temp2 = forkTravelDistance * m_ToMoveCost.m_ForkDistanceVariable + m_ToMoveCost.m_ForkDistanceFixed;
		forkTravelTime = Max(temp1, temp2);
	}

	return forkTravelTime / MINUTES_PER_HOUR;

}



double CLaborCalc::CalculateForkHandlingTime()
{
	double forkHandlingTime = 0.0;

	if (! IsStocker(m_FromMoveCost.m_BayType))
		forkHandlingTime += m_FromMoveCost.m_ForkFixedExtraction;

	if (! IsStocker(m_ToMoveCost.m_BayType))
		forkHandlingTime += m_ToMoveCost.m_ForkFixedInsertion;

	if (! IsSameSection()) {
		if (IsStocker(m_FromMoveCost.m_BayType) && IsStocker(m_ToMoveCost.m_BayType)) {
			forkHandlingTime += m_FromMoveCost.m_ForkFixedExtraction;
			forkHandlingTime += m_ToMoveCost.m_ForkFixedInsertion;
		}
	}

	return forkHandlingTime / MINUTES_PER_HOUR;
	
}


double CLaborCalc::CalculateForkCost(double pTime)
{
	double forkCost = 0.0;

	if (m_FromMoveCost.m_ForkLaborRate > m_ToMoveCost.m_ForkLaborRate)
		forkCost = pTime * m_FromMoveCost.m_ForkLaborRate;
	else
		forkCost = pTime * m_ToMoveCost.m_ForkLaborRate;

	return forkCost;

}

double CLaborCalc::CalculateStockerTravelDistance()
{
	double stockerTravelDistance = 0.0;

	if (IsSameAisle()) {
		// If same aisle and both locations are stocker, use stocker travel instead of fork
		if (IsStocker(m_FromMoveCost.m_BayType) && IsStocker(m_ToMoveCost.m_BayType))
			stockerTravelDistance += m_BetweenLocDistance * 2;
	}
	else {
		if (IsStocker(m_FromMoveCost.m_BayType))
			stockerTravelDistance += m_FromAisleDistance * 2;

		if (IsStocker(m_ToMoveCost.m_BayType))
			stockerTravelDistance += m_ToAisleDistance * 2;

		if (IsSameSection()) {
			if (IsStocker(m_FromMoveCost.m_BayType) && IsStocker(m_ToMoveCost.m_BayType))
				stockerTravelDistance += m_BetweenAisleDistance * 2;
		}

	}

	return stockerTravelDistance;

}

double CLaborCalc::CalculateStockerTravelTime(double stockerTravelDistance)
{
	double temp1, temp2;
	double stockerTravelTime = 0.0;

	if (stockerTravelDistance > 0) {
		temp1 = stockerTravelDistance * m_FromMoveCost.m_StockerDistanceVariable + m_FromMoveCost.m_StockerDistanceFixed;
		temp2 = stockerTravelDistance * m_ToMoveCost.m_StockerDistanceVariable + m_ToMoveCost.m_StockerDistanceFixed;
		stockerTravelTime = Max(temp1, temp2);
	}

	return stockerTravelTime / MINUTES_PER_HOUR;

}

double CLaborCalc::CalculateTotalStockerHandlingTime()
{
	double stockerHandlingTime = 0.0;

	if (IsStocker(m_FromMoveCost.m_BayType))
		stockerHandlingTime += CalculateStockerHandlingTime(m_FromMoveCost);

	if (IsStocker(m_ToMoveCost.m_BayType))
		stockerHandlingTime += CalculateStockerHandlingTime(m_ToMoveCost);


	return stockerHandlingTime / MINUTES_PER_HOUR;
	
}

double CLaborCalc::CalculateStockerHandlingTime(CMoveCost pMoveCost)
{

	double stockerHandlingTime = 0.0;
	double cube = 0.0;
	double weight = 0.0;
	double avgQty = 0.0;
	int i;

	if (IsFlow(pMoveCost.m_BayType)) {
		cube = m_Product.m_CaseCube;	
		avgQty = pMoveCost.m_CaseQuantity / 2.0;
		weight = m_Product.m_CaseWeight;
	}
	else {
		switch (m_Product.m_UnitOfIssue) {
		case UOI_EACH:
			cube = m_Product.m_EachCube;
			avgQty = pMoveCost.m_CaseQuantity * m_Product.m_CasePack / 2.0;
			weight = m_Product.m_EachWeight;
			break;
		case UOI_INNER:
			cube = m_Product.m_InnerCube;
			avgQty = (pMoveCost.m_CaseQuantity * (m_Product.m_CasePack/m_Product.m_InnerPack)) /2.0;
			weight = m_Product.m_InnerWeight;
			break;
		case UOI_CASE:
			cube = m_Product.m_CaseCube;
			avgQty = pMoveCost.m_CaseQuantity / 2.0;
			weight= m_Product.m_CaseWeight;
			break;
		case UOI_PALLET:
			// if the UOI is pallet we should never get to this function
			cube = m_Product.m_CaseCube * pMoveCost.m_CaseQuantity;
			avgQty = 0.5;
			weight = m_Product.m_CaseWeight;
			break;
		}
	}


	i = FindStockerLabor(pMoveCost.m_BayProfileDBID, pMoveCost.m_RelativeLevel, cube);
	if (i < 0)
		stockerHandlingTime = 0.0;
	else
		stockerHandlingTime = (weight * m_StockerLabor[i].m_VariableFactor + m_StockerLabor[i].m_FixedFactor) * avgQty;


	return stockerHandlingTime;
}



double CLaborCalc::CalculateStockerCost(double pTime)
{
	double stockerCost = 0.0;

	if (m_FromMoveCost.m_StockerLaborRate > m_ToMoveCost.m_StockerLaborRate)
		stockerCost = pTime * m_FromMoveCost.m_StockerLaborRate;
	else
		stockerCost = pTime * m_ToMoveCost.m_StockerLaborRate;

	return stockerCost;

}


int CLaborCalc::FindStockerLabor(int ProfID, int Lev, double cube)
{
	int i;
	
	/* ********************************************** */
	/* Scan to find the correct RT and Lev.           */
	/* ********************************************** */
	//printf("Prof %d Lev %d Cube %f",ProfID, Lev,cube);

	// find the first match
	for(i=0; i < m_StockerLaborCount; i++)
		if( (m_StockerLabor[i].m_BayProfileDBID == ProfID) &&
			(m_StockerLabor[i].m_RelativeLevel == Lev))
			break;

	// if itss the last one in the list, return it
	if ( i == m_StockerLaborCount )
		return -1;

	// find the closest cube that is less than or equal to our cube
	for(; i < m_StockerLaborCount; i++){
		if( (m_StockerLabor[i].m_BayProfileDBID != ProfID) ||
			(m_StockerLabor[i].m_RelativeLevel != Lev)) {
			return i-1;
		}
		if(cube < m_StockerLabor[i].m_Cube) {
			return i-1;
		}
	}

	return m_StockerLaborCount-1;
}


int CLaborCalc::IsSameAisle()
{
	// should this be looking at aisle ID?
	return m_FromMoveCost.m_AisleDBID == m_ToMoveCost.m_AisleDBID;
}


int CLaborCalc::IsSameSection()
{
	return m_FromMoveCost.m_SectionDBID == m_ToMoveCost.m_SectionDBID;
}


int CLaborCalc::IsStocker(int pBayType)
{

	if (m_Product.m_UnitOfIssue == UOI_PALLET)
		return 0;

	switch (pBayType) {
	case BAY_TYPE_BIN:
	case BAY_TYPE_FLOW:
	case BAY_TYPE_CAROUSEL:
		return 1;
		break;
	default:
		return 0;
	}
	
	return 0;

}

int CLaborCalc::IsFlow(int pBayType)
{
	return (pBayType == BAY_TYPE_FLOW);
	
}

double CLaborCalc::CalcOperatingCost(char **pParameters)
{

	return 0.0;

}

void CLaborCalc::Initialize()
{

	double fromLocToEntry, fromLocToExit, toLocToEntry, toLocToExit;
	double entryToEntry, exitToEntry, entryToExit, exitToExit;

	// this will be used if the locations are in the same aisle
	m_BetweenLocDistance = 
		CalculateXYDistance(m_FromMoveCost.m_LocationCoordinates, m_ToMoveCost.m_LocationCoordinates);

	// distance between the from loc and each end of the aisle
	fromLocToEntry = 
		CalculateXYDistance(m_FromMoveCost.m_LocationCoordinates, m_FromMoveCost.m_AisleEntryCoordinates);
	fromLocToExit = 
		CalculateXYDistance(m_FromMoveCost.m_LocationCoordinates, m_FromMoveCost.m_AisleExitCoordinates);

	// distance between the to loc and each end of the aisle
	toLocToEntry = 
		CalculateXYDistance(m_ToMoveCost.m_LocationCoordinates, m_ToMoveCost.m_AisleEntryCoordinates);
	toLocToExit = 
		CalculateXYDistance(m_ToMoveCost.m_LocationCoordinates, m_ToMoveCost.m_AisleExitCoordinates);

	// distance between the aisles using all possible routes
	// (starting at entry or exit of from and ending at entry or exit of to)
	entryToEntry = 
		CalculateXYDistance(m_FromMoveCost.m_AisleEntryCoordinates, m_ToMoveCost.m_AisleEntryCoordinates);
	exitToEntry = 
		CalculateXYDistance(m_FromMoveCost.m_AisleExitCoordinates, m_ToMoveCost.m_AisleEntryCoordinates);
	entryToExit = 
		CalculateXYDistance(m_FromMoveCost.m_AisleEntryCoordinates, m_ToMoveCost.m_AisleExitCoordinates);
	exitToExit = 
		CalculateXYDistance(m_FromMoveCost.m_AisleExitCoordinates, m_ToMoveCost.m_AisleExitCoordinates);

	// find the shortest path
	CTravelDistances tempDistances[4];

	// these are the possible routes between the locations
	tempDistances[0].Create(fromLocToEntry, entryToEntry, toLocToEntry);
	tempDistances[1].Create(fromLocToExit, exitToEntry, toLocToEntry);
	tempDistances[2].Create(fromLocToEntry, entryToExit, toLocToExit);
	tempDistances[3].Create(fromLocToExit, exitToExit, toLocToExit);

	// sort the routes by shortest total distance
	qsort(tempDistances, 4, sizeof(CTravelDistances), CompareDistances);
	
	// use the shortest route in our calculations
	m_FromAisleDistance = tempDistances[0].m_FromAisle;
	m_BetweenAisleDistance = tempDistances[0].m_BetweenAisles;
	m_ToAisleDistance = tempDistances[0].m_ToAisle;

	

	// Set the labor rates to the highest between the from and the to
	m_ForkLaborRate = Max(m_FromMoveCost.m_ForkLaborRate, m_ToMoveCost.m_ForkLaborRate);
	m_StockerLaborRate = Max(m_FromMoveCost.m_StockerLaborRate, m_ToMoveCost.m_StockerLaborRate);

}

int CompareDistances(const void *t1, const void *t2)
{
	CTravelDistances *td1, *td2;
	td1 = (CTravelDistances *)t1;
	td2 = (CTravelDistances *)t2;

	if (td1->Total() <= td2->Total())
		return -1;
	else
		return 1;

}


double CLaborCalc::Max(double p1, double p2)
{
	if (p1 < p2)
		return p2;
	else
		return p1;
}

double CLaborCalc::CalculateXYDistance(CCoordinate p1, CCoordinate p2)
{
	double x, y;
	
	x =  p1.m_x - p2.m_x;
	if (x < 0) 
		x = 0 - x;
	
	y =  p1.m_y - p2.m_y;
	if (y < 0)
		y = 0 - y;
	
	return (x + y) / m_Divisor;

}


int CLaborCalc::ParseMoveParameters(char **pParameters)
{

	// Parameters are:
	// MOVEPARAMETERS|Length
	// PRODUCT|DBID|UOI|CASEPACK|CASECUBE|CASEWEIGHT|INNERPACK|INNERCUBE|INNERWEIGHT|EACHCUBE|EACHWEIGHT
	// FROMMOVE|MoveType|SectionDBID|AisleDBID|AisleEntryX|AisleEntryY|AisleEntryZ|AisleExitX|AisleExitY|AisleExitZ|
	//	...	LocDBID|LocX|LocY|LocZ|BayProfileID|RelLev|BayType|CaseQuantity|ForkExtraction|ForkInsertion|ForkLabor|
	//  ... ForkFixed|ForkVar|StockerLaborRate
	// TOMOVE|... (same as FROMMOVE)
	// Optional labor values.  See LoadLabor method

	char line[PARM_BUFFER_SIZE];
	char *ptr;
	int count, i;

	//printf("In ParseMoveParameters: %d\n", m_FromMoveCost.m_ProductDBID);

	if (pParameters == NULL)
		return -1;

	strcpy(line, pParameters[0]);
	ptr = strtok(line, "|");
	if (strcmpi(ptr, "MOVEPARAMETERS") != 0)
		return -1;
	
	ptr = strtok(NULL, "|");
	count = atoi(ptr);

	// we at least need this line, product, from move, to move
	if (count < 4)
		return -1;

	for (i=1; i < count; ++i) {
		//printf("In loop: %d - %d\n", i, m_FromMoveCost.m_ProductDBID);
		strcpy(line, pParameters[i]);
		if (strnicmp(line, "PRODUCT", 7) == 0)
			ParseProduct(line);
		else if (strnicmp(line, "FROMMOVE", 8) == 0)
			ParseMove(line);
		else if (strnicmp(line, "TOMOVE", 6) == 0)
			ParseMove(line);
		else if (strnicmp(line, "LABORSTART", 10) == 0) {
			LoadLabor(pParameters+i);
		}
	
	}

	m_MoveType = m_FromMoveCost.m_MoveType;

	return 0;


}

void CLaborCalc::ParseProduct (char *pProduct)
{

	// PRODUCT|DBID|UOI|CASEPACK|CASECUBE|CASEWEIGHT|INNERPACK|INNERCUBE|INNERWEIGHT|EACHCUBE|EACHWEIGHT

	char *ptr;

	if (pProduct == NULL)
		return;

	ptr = strtok(pProduct, "|");
	if (strcmpi(ptr, "PRODUCT") != 0)
		return;

	ptr = strtok(NULL, "|");
	m_Product.m_ProductDBID = atoi(ptr);
	ptr = strtok(NULL, "|");
	m_Product.m_UnitOfIssue = atoi(ptr);
	ptr = strtok(NULL, "|");
	m_Product.m_CasePack = atoi(ptr);
	ptr = strtok(NULL, "|");
	m_Product.m_CaseCube = (double)atof(ptr);
	ptr = strtok(NULL, "|");
	m_Product.m_CaseWeight = (double)atof(ptr);
	ptr = strtok(NULL, "|");
	m_Product.m_InnerPack = atoi(ptr);
	ptr = strtok(NULL, "|");
	m_Product.m_InnerCube  = (double)atof(ptr);
	ptr = strtok(NULL, "|");
	m_Product.m_InnerWeight = (double)atof(ptr);
	ptr = strtok(NULL, "|");
	m_Product.m_EachCube = (double)atof(ptr);
	ptr = strtok(NULL, "|");
	m_Product.m_EachWeight = (double)atof(ptr);

	//printf("After ParseProduct: %d\n", m_FromMoveCost.m_ProductDBID);

	return;

}

void CLaborCalc::ParseMove(char *pMove) 
{
	// FROMMOVE|MoveType|SectionDBID|AisleDBID|AisleEntryX|AisleEntryY|AisleEntryZ|AisleExitX|AisleExitY|AisleExitZ|
	//	...	LocDBID|LocX|LocY|LocZ|BayProfileID|RelLev|BayType|CaseQuantity|ForkExtraction|ForkInsertion|ForkLabor|
	//  ... ForkFixed|ForkVar|StockerDistFixed|StockerDistVar|StockerLaborRate|

	char *ptr;

	//printf("Move:%s:\n", pMove);
	//printf("In ParseMove: %d\n", m_FromMoveCost.m_ProductDBID);

	if (pMove == NULL)
		return;
	
	ptr = strtok(pMove, "|");
	if (strcmpi(ptr, "FROMMOVECOST") != 0 && strcmpi(ptr, "TOMOVECOST") != 0)
		return;
	
	if (strcmpi(ptr, "FROMMOVECOST") == 0) {
		ptr = strtok(NULL, "|");
		m_FromMoveCost.m_MoveType = atoi(ptr);
		if (m_FromMoveCost.m_MoveType == MOVE_NONE)
			return;
		ptr = strtok(NULL, "|");
		m_FromMoveCost.m_SectionDBID = atoi(ptr);
		ptr = strtok(NULL, "|");
		m_FromMoveCost.m_AisleDBID = atoi(ptr);
		ptr = strtok(NULL, "|");
		m_FromMoveCost.m_AisleEntryCoordinates.m_x = atof(ptr);
		ptr = strtok(NULL, "|");
		m_FromMoveCost.m_AisleEntryCoordinates.m_y = atof(ptr);
		ptr = strtok(NULL, "|");
		m_FromMoveCost.m_AisleEntryCoordinates.m_z = atof(ptr);
		ptr = strtok(NULL, "|");
		m_FromMoveCost.m_AisleExitCoordinates.m_x = atof(ptr);
		ptr = strtok(NULL, "|");
		m_FromMoveCost.m_AisleExitCoordinates.m_y = atof(ptr);
		ptr = strtok(NULL, "|");
		m_FromMoveCost.m_AisleExitCoordinates.m_z = atof(ptr);
		ptr = strtok(NULL, "|");
		m_FromMoveCost.m_LocationDBID = atoi(ptr);
		ptr = strtok(NULL, "|");
		m_FromMoveCost.m_LocationCoordinates.m_x = atof(ptr);
		ptr = strtok(NULL, "|");
		m_FromMoveCost.m_LocationCoordinates.m_y = atof(ptr);
		ptr = strtok(NULL, "|");
		m_FromMoveCost.m_LocationCoordinates.m_z = atof(ptr);
		ptr = strtok(NULL, "|");
		m_FromMoveCost.m_BayProfileDBID = atoi(ptr);
		ptr = strtok(NULL, "|");
		m_FromMoveCost.m_RelativeLevel = atoi(ptr);
		ptr = strtok(NULL, "|");
		m_FromMoveCost.m_BayType = atoi(ptr);
		ptr = strtok(NULL, "|");
		m_FromMoveCost.m_CaseQuantity = atoi(ptr);
		ptr = strtok(NULL, "|");
		m_FromMoveCost.m_ForkFixedExtraction = atof(ptr);
		ptr = strtok(NULL, "|");
		m_FromMoveCost.m_ForkFixedInsertion = atof(ptr);
		ptr = strtok(NULL, "|");
		m_FromMoveCost.m_ForkLaborRate = atof(ptr);
		ptr = strtok(NULL, "|");
		m_FromMoveCost.m_ForkDistanceFixed = atof(ptr);
		ptr = strtok(NULL, "|");
		m_FromMoveCost.m_ForkDistanceVariable = atof(ptr);
		ptr = strtok(NULL, "|");
		m_FromMoveCost.m_StockerDistanceFixed = atof(ptr);
		ptr = strtok(NULL, "|");
		m_FromMoveCost.m_StockerDistanceVariable = atof(ptr);
		ptr = strtok(NULL, "|");
		m_FromMoveCost.m_StockerLaborRate= atof(ptr);
	}
	
	else {
		ptr = strtok(NULL, "|");
		m_ToMoveCost.m_MoveType = atoi(ptr);
		if (m_ToMoveCost.m_MoveType == MOVE_NONE)
			return;
		ptr = strtok(NULL, "|");
		m_ToMoveCost.m_SectionDBID = atoi(ptr);
		ptr = strtok(NULL, "|");
		m_ToMoveCost.m_AisleDBID = atoi(ptr);
		ptr = strtok(NULL, "|");
		m_ToMoveCost.m_AisleEntryCoordinates.m_x = atof(ptr);
		ptr = strtok(NULL, "|");
		m_ToMoveCost.m_AisleEntryCoordinates.m_y = atof(ptr);
		ptr = strtok(NULL, "|");
		m_ToMoveCost.m_AisleEntryCoordinates.m_z = atof(ptr);
		ptr = strtok(NULL, "|");
		m_ToMoveCost.m_AisleExitCoordinates.m_x = atof(ptr);
		ptr = strtok(NULL, "|");
		m_ToMoveCost.m_AisleExitCoordinates.m_y = atof(ptr);
		ptr = strtok(NULL, "|");
		m_ToMoveCost.m_AisleExitCoordinates.m_z = atof(ptr);
		ptr = strtok(NULL, "|");
		m_ToMoveCost.m_LocationDBID = atoi(ptr);
		ptr = strtok(NULL, "|");
		m_ToMoveCost.m_LocationCoordinates.m_x = atof(ptr);
		ptr = strtok(NULL, "|");
		m_ToMoveCost.m_LocationCoordinates.m_y = atof(ptr);
		ptr = strtok(NULL, "|");
		m_ToMoveCost.m_LocationCoordinates.m_z = atof(ptr);
		ptr = strtok(NULL, "|");
		m_ToMoveCost.m_BayProfileDBID = atoi(ptr);
		ptr = strtok(NULL, "|");
		m_ToMoveCost.m_RelativeLevel = atoi(ptr);
		ptr = strtok(NULL, "|");
		m_ToMoveCost.m_BayType = atoi(ptr);
		ptr = strtok(NULL, "|");
		m_ToMoveCost.m_CaseQuantity = atoi(ptr);
		ptr = strtok(NULL, "|");
		m_ToMoveCost.m_ForkFixedExtraction = atof(ptr);
		ptr = strtok(NULL, "|");
		m_ToMoveCost.m_ForkFixedInsertion = atof(ptr);
		ptr = strtok(NULL, "|");
		m_ToMoveCost.m_ForkLaborRate = atof(ptr);
		ptr = strtok(NULL, "|");
		m_ToMoveCost.m_ForkDistanceFixed = atof(ptr);
		ptr = strtok(NULL, "|");
		m_ToMoveCost.m_ForkDistanceVariable = atof(ptr);
		ptr = strtok(NULL, "|");
		m_ToMoveCost.m_StockerDistanceFixed = atof(ptr);
		ptr = strtok(NULL, "|");
		m_ToMoveCost.m_StockerDistanceVariable = atof(ptr);
		ptr = strtok(NULL, "|");
		m_ToMoveCost.m_StockerLaborRate= atof(ptr);
	}


	return;

}



void CLaborCalc::LoadLabor(char **pLabor)
{
	// The first line should be:
	// LABORSTART|TYPE|COUNT
	// Labor values are in the following form:
	// LABOR|BAYPROFILEID|RELATIVELEVEL|CUBE|FIXEDFACTOR|VARIABLEFACTOR|

	char line[100], *ptr;
	int laborCount, i, laborType;

	if (pLabor == NULL)
		return;

	ptr = strtok(pLabor[0], "|");
	if (strcmpi(ptr, "LABORSTART") != 0)
		return;

	ptr = strtok(NULL, "|");
	laborType = atoi(ptr);		// Type 0 - select, 1 - stocker
	ptr = strtok(NULL, "|");
	laborCount = atoi(ptr);

	if (laborType != STOCKER_LABOR)
		return;

	if (laborCount <= 0) {
		m_StockerLaborCount = 0;
		return;
	}

	m_StockerLaborCount = laborCount;
	m_StockerLabor = new CLevelLabor[m_StockerLaborCount];

	//memset(m_StockerLabor, 0, m_StockerLaborCount * sizeof(CLevelLabor));

	for (i=0; i < laborCount; ++i) {
		strcpy(line, pLabor[i+1]);
		ptr = strtok(line, "|");
		if (strcmpi(ptr, "LABOR") != 0)
			continue;

		ptr = strtok(NULL, "|");
		m_StockerLabor[i].m_BayProfileDBID = atoi(ptr);
		ptr = strtok(NULL, "|");
		m_StockerLabor[i].m_RelativeLevel = atoi(ptr);
		ptr = strtok(NULL, "|");
		m_StockerLabor[i].m_Cube = (float)atof(ptr);
		ptr = strtok(NULL, "|");
		m_StockerLabor[i].m_FixedFactor = (float)atof(ptr);
		ptr = strtok(NULL, "|");
		m_StockerLabor[i].m_VariableFactor = (float)atof(ptr);
		
	}

	return;


}

// ProductLabor.h: interface for the CProductLabor class.
//
//////////////////////////////////////////////////////////////////////

#if !defined(AFX_PRODUCTLABOR_H__7B9BA7F6_7450_11D4_9EAD_00C04FAC149C__INCLUDED_)
#define AFX_PRODUCTLABOR_H__7B9BA7F6_7450_11D4_9EAD_00C04FAC149C__INCLUDED_

#if _MSC_VER > 1000
#pragma once
#endif // _MSC_VER > 1000

class CProductLabor  
{
public:
	void StreamAttributes(char *prodTxt);
	int m_ProductDBID;
	int m_UnitOfIssue;
	int m_CasePack;
	int m_InnerPack;
	double m_EachCube;
	double m_InnerCube;
	double m_CaseCube;
	double m_EachWeight;
	double m_InnerWeight;
	double m_CaseWeight;
	

	CProductLabor();
	virtual ~CProductLabor();

};

#endif // !defined(AFX_PRODUCTLABOR_H__7B9BA7F6_7450_11D4_9EAD_00C04FAC149C__INCLUDED_)

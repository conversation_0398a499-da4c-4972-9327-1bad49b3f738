#ifndef DISPATCH_CONSTANTS_DEFINED
#define DISPATCH_CONSTANTS_DEFINED
const int UOI_EACH = 0;
const int UOI_INNER = 1;
const int UOI_CASE = 2;
const int UOI_PALLET = 3;

const int BAY_TYPE_BIN = 1;
const int BAY_TYPE_DRIVE_IN = 2;
const int BAY_TYPE_FLOOR = 3;
const int BAY_TYPE_FLOW = 4;
const int BAY_TYPE_PALLET = 5;
const int BAY_TYPE_PIR = 6;
const int BAY_TYPE_CAROUSEL = 7;
const int BAY_TYPE_PALLET_FLOW = 8;

const int MOVE_NORMAL = 0;
const int MOVE_TO_TEMP = 1;
const int MOVE_FROM_TEMP = 2;
const int MOVE_ADD_LOC = 3;
const int MOVE_DEL_LOC = 4;
const int MOVE_NONE = 5;

const int SELECT_LABOR = 0;
const int STOCKER_LABOR = 1;

const int TIME_HORIZON_DAY = 1;
const int TIME_HORIZON_WEEK = 2;
const int TIME_HORIZON_MONTH = 3;
const int TIME_HORIZON_YEAR = 4;

const int GROUP_LAYOUT = 0;
const int TACTICAL_LAYOUT = 1;
const int STRATEGIC_LAYOUT = 2;
const int NEW_PRODUCT_LAYOUT = 3;

#endif

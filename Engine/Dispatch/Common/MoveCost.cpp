// MoveCost.cpp: implementation of the CMoveCost class.
//
//////////////////////////////////////////////////////////////////////

#include "MoveCost.h"
#include "stdio.h"
#include <string.h>
#include "../Common/Constants.h"
//////////////////////////////////////////////////////////////////////
// Construction/Destruction
//////////////////////////////////////////////////////////////////////

CMoveCost::CMoveCost()
{
//	fprintf(stdout, "Creating movecost\n");
//	fflush(stdout);
	m_MoveType = MOVE_NONE;
}

CMoveCost::~CMoveCost()
{
//	fprintf(stdout, "Freeing movecost\n");
//	fflush(stdout);
}


CMoveCost::CMoveCost(const CMoveCost & other)
{
	m_MoveType = other.m_MoveType;
	m_ForkFixedInsertion = other.m_ForkFixedInsertion;
	m_ForkFixedExtraction = other.m_ForkFixedExtraction;
	m_ForkDistanceFixed = other.m_ForkDistanceFixed;
	m_ForkDistanceVariable = other.m_ForkDistanceVariable;
	m_ForkLaborRate = other.m_ForkLaborRate;
	m_StockerDistanceFixed = other.m_StockerDistanceFixed;
	m_StockerDistanceVariable = other.m_StockerDistanceVariable;
	m_StockerLaborRate = other.m_StockerLaborRate;
	m_CaseQuantity = other.m_CaseQuantity;
	m_LocationDBID = other.m_LocationDBID;
	m_AisleDBID = other.m_AisleDBID;
	m_SectionDBID = other.m_SectionDBID;
	m_BayType = other.m_BayType;
	m_BayProfileDBID = other.m_BayProfileDBID;
	m_RelativeLevel = other.m_RelativeLevel;

	m_AisleEntryCoordinates = other.m_AisleEntryCoordinates;
	m_AisleExitCoordinates = other.m_AisleExitCoordinates;
	m_LocationCoordinates = other.m_LocationCoordinates;
}

CMoveCost& CMoveCost::operator=(const CMoveCost & other)
{
	m_MoveType = other.m_MoveType;
	m_ForkFixedInsertion = other.m_ForkFixedInsertion;
	m_ForkFixedExtraction = other.m_ForkFixedExtraction;
	m_ForkDistanceFixed = other.m_ForkDistanceFixed;
	m_ForkDistanceVariable = other.m_ForkDistanceVariable;
	m_ForkLaborRate = other.m_ForkLaborRate;
	m_StockerDistanceFixed = other.m_StockerDistanceFixed;
	m_StockerDistanceVariable = other.m_StockerDistanceVariable;
	m_StockerLaborRate = other.m_StockerLaborRate;
	m_CaseQuantity = other.m_CaseQuantity;
	m_LocationDBID = other.m_LocationDBID;
	m_AisleDBID = other.m_AisleDBID;
	m_SectionDBID = other.m_SectionDBID;
	m_BayType = other.m_BayType;
	m_BayProfileDBID = other.m_BayProfileDBID;
	m_RelativeLevel = other.m_RelativeLevel;

	m_AisleEntryCoordinates = other.m_AisleEntryCoordinates;
	m_AisleExitCoordinates = other.m_AisleExitCoordinates;
	m_LocationCoordinates = other.m_LocationCoordinates;

	return *this;
}

void CMoveCost::StreamAttributes(char *pMoveText)
{
	// FROMMOVECOST|MoveType|SectionDBID|AisleDBID|AisleEntryX|AisleEntryY|AisleEntryZ|AisleExitX|AisleExitY|AisleExitZ|
	//	...	LocDBID|LocX|LocY|LocZ|BayProfileID|RelLev|BayType|CaseQuantity|ForkExtraction|ForkInsertion|ForkLabor|
	//  ... ForkFixed|ForkVar|StockerDistFixed|StockerDistVar|StockerLaborRate|
	if (m_MoveType == MOVE_NONE) {
		sprintf(pMoveText, "%d|", m_MoveType);
	}
	else {
		
		sprintf(pMoveText, "%d|%d|%d|%f|%f|%f|%f|%f|%f|%d|%f|%f|%f|%d|%d|%d|%d|%f|%f|%f|%f|%f|%f|%f|%f|",
			m_MoveType,
			m_SectionDBID,
			m_AisleDBID,
			m_AisleEntryCoordinates.m_x,
			m_AisleEntryCoordinates.m_y,
			m_AisleEntryCoordinates.m_z,
			m_AisleExitCoordinates.m_x,
			m_AisleExitCoordinates.m_y,
			m_AisleExitCoordinates.m_z,
			m_LocationDBID,
			m_LocationCoordinates.m_x,
			m_LocationCoordinates.m_y,
			m_LocationCoordinates.m_z,
			m_BayProfileDBID,
			m_RelativeLevel,
			m_BayType,
			m_CaseQuantity,
			m_ForkFixedExtraction,
			m_ForkFixedInsertion,
			m_ForkLaborRate,
			m_ForkDistanceFixed,
			m_ForkDistanceVariable,
			m_StockerDistanceFixed,
			m_StockerDistanceVariable,
			m_StockerLaborRate);
	}
	//if (strlen(pMoveText) > 200) {
	//	fprintf(stdout, "long line (%d): %s\n", strlen(pMoveText), pMoveText);
	//}
}

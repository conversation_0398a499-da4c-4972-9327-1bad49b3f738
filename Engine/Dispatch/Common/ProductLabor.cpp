// ProductLabor.cpp: implementation of the CProductLabor class.
//
//////////////////////////////////////////////////////////////////////

#include "ProductLabor.h"
#include <stdio.h>
//////////////////////////////////////////////////////////////////////
// Construction/Destruction
//////////////////////////////////////////////////////////////////////

CProductLabor::CProductLabor()
{

}

CProductLabor::~CProductLabor()
{

}

void CProductLabor::StreamAttributes(char *prodTxt)
{
	// PRODUCT|DBID|UOI|CASEPACK|CASECUBE|CASEWEIGHT|INNERPACK|INNERCUBE|INNERWEIGHT|EACHCUBE|EACHWEIGHT
	sprintf(prodTxt, "PRODUCT|%d|%d|%d|%f|%f|%d|%f|%f|%f|%f",
		m_ProductDBID,
		m_UnitOfIssue,
		m_CasePack,
		m_CaseCube,
		m_CaseWeight,
		m_InnerPack,
		m_InnerCube,
		m_InnerWeight,
		m_EachCube,
		m_EachWeight);
}

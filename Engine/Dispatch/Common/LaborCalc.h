// LaborCalc.h: interface for the CLaborCalc class.
//
//////////////////////////////////////////////////////////////////////

#if !defined(AFX_LABORCALC_H__7B9BA7F1_7450_11D4_9EAD_00C04FAC149C__INCLUDED_)
#define AFX_LABORCALC_H__7B9BA7F1_7450_11D4_9EAD_00C04FAC149C__INCLUDED_

#include "MoveCost.h"	// Added by ClassView
#if _MSC_VER > 1000
#pragma once
#endif // _MSC_VER > 1000

#include "MoveCost.h"
#include "ProductLabor.h"
#include "LevelLabor.h"
#include "Coordinate.h"
#include "TravelDistances.h"

class CLaborCalc  
{
public:

	double CalcOperatingCost(char **pParameters);
	double CalcMoveCost(char **pParameters);
	void LoadLabor(char **pLabor);
	CLaborCalc();
	virtual ~CLaborCalc();
	char **m_CostTraceBuffer;

private:
	CMoveCost m_FromMoveCost;
	CMoveCost m_ToMoveCost;
	CProductLabor m_Product;
	CLevelLabor *m_StockerLabor;
	int m_StockerLaborCount;

	double CalcSimpleMoveCost();
	void Initialize();	
	void ParseProduct (char *pProduct);
	void ParseMove(char *pMove);
	double CalculateXYDistance(CCoordinate p1, CCoordinate p2);
	int ParseMoveParameters(char **pParameters);
	double CalculateForkTravelDistance();
	double CalculateForkHandlingTime();
	double CalculateForkTravelTime(double forkTravelDistance);
	double CalculateStockerTravelTime(double stockerTravelDistance);
	double CalculateStockerTravelDistance();
	double CalculateTotalStockerHandlingTime();
	double CalculateStockerHandlingTime(CMoveCost pMoveCost);
	double CalculateForkCost(double pTime);
	double CalculateStockerCost(double pTime);
	int FindStockerLabor(int ProfID, int Lev, double cube);
	int IsSameAisle();
	int IsSameSection();
	int IsStocker(int pBayType);
	int IsFlow(int pBayType);
	double CLaborCalc::Max(double p1, double p2);

	double m_FromAisleDistance;
	double m_ToAisleDistance;
	double m_BetweenLocDistance;
	double m_BetweenAisleDistance;
	double m_Divisor;
	double m_ForkLaborRate;
	double m_StockerLaborRate;
	int m_MoveType;
};

#endif // !defined(AFX_LABORCALC_H__7B9BA7F1_7450_11D4_9EAD_00C04FAC149C__INCLUDED_)

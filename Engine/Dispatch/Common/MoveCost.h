// MoveCost.h: interface for the CMoveCost class.
//
//////////////////////////////////////////////////////////////////////

#if !defined(AFX_MOVECOST_H__7B9BA7F4_7450_11D4_9EAD_00C04FAC149C__INCLUDED_)
#define AFX_MOVECOST_H__7B9BA7F4_7450_11D4_9EAD_00C04FAC149C__INCLUDED_

#if _MSC_VER > 1000
#pragma once
#endif // _MSC_VER > 1000

#include "Coordinate.h"
class CMoveCost  
{
public:
	void StreamAttributes(char *pMoveText);
	int m_ProductDBID;
	CMoveCost();
	virtual ~CMoveCost();
	CMoveCost(const CMoveCost & other);
	CMoveCost& operator=(const CMoveCost & other);
	int m_MoveType;
	double m_ForkFixedInsertion;
	double m_ForkFixedExtraction;
	double m_ForkDistanceFixed;
	double m_ForkDistanceVariable;
	double m_ForkLaborRate;
	double m_StockerDistanceFixed;
	double m_StockerDistanceVariable;
	double m_StockerLaborRate;
	int m_CaseQuantity;
	int m_LocationDBID;
	int m_AisleDBID;
	int m_SectionDBID;
	int m_BayType;
	int m_BayProfileDBID;
	int m_RelativeLevel;

	CCoordinate m_AisleEntryCoordinates;
	CCoordinate m_AisleExitCoordinates;
	CCoordinate m_LocationCoordinates;


};

#endif // !defined(AFX_MOVECOST_H__7B9BA7F4_7450_11D4_9EAD_00C04FAC149C__INCLUDED_)

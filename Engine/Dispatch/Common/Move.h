// Move.h: interface for the CMove class.
//
//////////////////////////////////////////////////////////////////////

#if !defined(AFX_MOVE_H__DB0F23E1_76BC_11D4_91C7_00400542E36B__INCLUDED_)
#define AFX_MOVE_H__DB0F23E1_76BC_11D4_91C7_00400542E36B__INCLUDED_

#if _MSC_VER > 1000
#pragma once
#endif // _MSC_VER > 1000

class CMove  
{
public:
	CMove();
	virtual ~CMove();
	int m_productIdx;
	int m_fromLocIdx;
	int m_toLocIdx;
	int m_MoveType;
};

#endif // !defined(AFX_MOVE_H__DB0F23E1_76BC_11D4_91C7_00400542E36B__INCLUDED_)

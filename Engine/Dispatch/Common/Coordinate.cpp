// Coordinate.cpp: implementation of the CCoordinate class.
//
//////////////////////////////////////////////////////////////////////

#include "Coordinate.h"

//////////////////////////////////////////////////////////////////////
// Construction/Destruction
//////////////////////////////////////////////////////////////////////

CCoordinate::CCoordinate()
{
	m_x = 0.0;
	m_y = 0.0;
	m_z = 0.0;
}


CCoordinate::CCoordinate(double x, double y, double z)
{
	m_x = x;
	m_y = y;
	m_z = z;
}

CCoordinate::~CCoordinate()
{

}


CCoordinate& CCoordinate::operator=(const CCoordinate & other)
{
	m_x = other.m_x;
	m_y = other.m_y;
	m_z = other.m_z;

	return *this;
}

CCoordinate::CCoordinate(const CCoordinate & other)
{
	m_x = other.m_x;
	m_y = other.m_y;
	m_z = other.m_z;
}
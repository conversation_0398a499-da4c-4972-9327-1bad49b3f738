// TravelDistances.h: interface for the CTravelDistances class.
//
//////////////////////////////////////////////////////////////////////

#if !defined(AFX_TRAVELDISTANCES_H__7B9BA7F9_7450_11D4_9EAD_00C04FAC149C__INCLUDED_)
#define AFX_TRAVELDISTANCES_H__7B9BA7F9_7450_11D4_9EAD_00C04FAC149C__INCLUDED_

#if _MSC_VER > 1000
#pragma once
#endif // _MSC_VER > 1000

class CTravelDistances  
{
public:
	double Total();
	void Create(double fromAisle, double betweenAisles, double toAisle);
	double m_FromAisle;
	double m_BetweenAisles;
	double m_ToAisle;
	CTravelDistances();
	virtual ~CTravelDistances();

};

#endif // !defined(AFX_TRAVELDISTANCES_H__7B9BA7F9_7450_11D4_9EAD_00C04FAC149C__INCLUDED_)

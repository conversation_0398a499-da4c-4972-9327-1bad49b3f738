//////////////////////////////////////////////////////////////////////
// Function Name :	p1struct.h
// Classname :		prodPack, rackType; UNUSED: udf, fuzzyVar, ruleSet
// Description :	Global header for Succeed Pass 1 (Rack Assignment).
// Date Created :	~9/1/98
// Author : 		mfs
//////////////////////////////////////////////////////////////////////
// Inputs :			None
// Outputs :		None
// Explanation :	Global header for Succeed Pass 1 (Rack Assignment).
//////////////////////////////////////////////////////////////////////
// Revisions : 
//   November 6, 1998-mfs : Standard header comments added
//////////////////////////////////////////////////////////////////////
/*******************************************************
   *
   *                       NOTICE
   *
   *  THIS SOFTWARE IS THE PROPERTY OF AND CONTAINS
   *  CONFIDENTIAL INFORMATION OF SSA GLOBAL 
   *  TECHNOLOGIES, INC., AND SHALL NOT BE COPIED,
   *  USED, NOR DISCLOSED WITHOUT EXPRESS WRITTEN
   *  AUTHORIZATION.  ALTHOUGH PUBLICATION IS NOT
   *  INTENDED, IN THE EVENT OF PUBLICATION, THE
   *  FOLLOWING NOTICE IS APPLICABLE:
   *
   *  (c) COPYRIGHT 2004 SSA GLOBAL TECHNOLOGIES, INC.
   *
   *           SSA GLOBAL TECHNOLOGIES, INC.
   *
   ********************************************************/
//////////////////////////////////////////////////////////////////////

#ifndef USER_DEFINED_FIELD
#define USER_DEFINED_FIELD

typedef struct {
		char	udfName[251];
		char	charVal[251];	// Always use charVal, convert to double
} udf;

#endif /* USER_DEFINED_FIELD Defined */

#ifndef PRODUCT_PACK
#define PRODUCT_PACK

typedef struct {
	int		dbID;				// Forte DBID
	char	desc[251];			// for debugging
	double	height;
	double	width;
	double	length;
	double	BOH;
	int		hazard;
	int		optBy;
	double	movement;
	double	cube;
	double	xCube;
	double	xBOH;
	int		UOI;
	double   containerWidth;
	int		prodGpID;
	char	WMSProdID[256];
	char	WMSProdDetID[256];
	// 03/01/00 - brd - added the following fields for checksize
	double	weight;
	double	containerLength;
	double	containerHeight;
	int		contOverrideHeight;
	int		trace;
	int		ti;
	int		hi;
	double	nestWidth;
	double	nestLength;
	double	nestHeight;
} prodPack;

#endif /* PRODUCT_PACK Defined */

#define MAX_INPUT_DATA_LEN	1000
#define MAX_NUM_PRODUCTS	1000
#define MAX_NUM_PROFILES	2000
#define MAX_NUM_RACK_USAGES	100
#define MAX_NUM_RACK_TYPES	50
#define MAX_NUM_RACK_GROUPS	2
#define MAX_NUM_UDFS		50
#define	double_DATA_LEN		10
#define	INT_DATA_LEN		10
#define	UDF_NAME_LEN		251
#define	UDF_CHAR_LEN		251
#define DATA_BUFFER_LEN		1024
#define DATA_LINE_LEN		4096
#define	DATA_DELIMITER		"|"		// Pipe symbol

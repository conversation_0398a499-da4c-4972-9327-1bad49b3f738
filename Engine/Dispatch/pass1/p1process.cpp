//////////////////////////////////////////////////////////////////////
// Function Name :	P1Process.cpp
// Classname :		
// Description :	The Pass 1 process.  Assigns an Ideal and a
//					Best Available (in the current Facility) Rack
//					Type to each Product.
// Date Created :	~4/1/98
// Author : 		mfs
//////////////////////////////////////////////////////////////////////
// Inputs :			None
// Outputs :		None
// Explanation :	The Execute function passes no parameters in
//					or out as such.  Instead, Racking and Product
//					data arrive through a socket connection to the
//					Succeed session module.
//					Rack Type and Rack Usage information are
//					received first.  The Rack Usage numbers are
//					user-defined ranges of Product Extended Cube
//					and BOH appropriate to the rack's physical
//					configuration.  After this information is
//					loaded into the algorithm function's (p1.cpp)
//					tables, each Product is analyzed to determine
//					its Ideal racking.  Data has also been received
//					on the actual count of rack Facings in the
//					current Facility.  A second step in the analysis
//					then determines whether the Ideal racking will
//					be available for the Product, and, if not, which
//					type is the Best Available for it.
//////////////////////////////////////////////////////////////////////
// Revisions : 
//   November 6, 1998-mfs : Standard header comments added
//////////////////////////////////////////////////////////////////////
/*******************************************************
   *
   *                       NOTICE
   *
   *  THIS SOFTWARE IS THE PROPERTY OF AND CONTAINS
   *  CONFIDENTIAL INFORMATION OF SSA GLOBAL 
   *  TECHNOLOGIES, INC., AND SHALL NOT BE COPIED,
   *  USED, NOR DISCLOSED WITHOUT EXPRESS WRITTEN
   *  AUTHORIZATION.  ALTHOUGH PUBLICATION IS NOT
   *  INTENDED, IN THE EVENT OF PUBLICATION, THE
   *  FOLLOWING NOTICE IS APPLICABLE:
   *
   *  (c) COPYRIGHT 2004 SSA GLOBAL TECHNOLOGIES, INC.
   *
   *           SSA GLOBAL TECHNOLOGIES, INC.
   *
   ********************************************************/
//////////////////////////////////////////////////////////////////////

#include <stdlib.h>
#include <malloc.h>
#include <stdio.h>
#include <errno.h>
#include <direct.h>
#include "P1Process.h"
#include <math.h>
#include "..\..\..\common\core\debug.h"
#include "..\..\..\common\core\exceptions.h"
#include "../common/UtilityHelper.h"
#include <vector>
#include <string>
#include "..\Common\Constants.h"
#include "..\DataStream.h"

CUtilityHelper utilityHelper;

FILE * p1ini_file;
FILE * p1TraceFile;

int userLog = 0;
int debugLog = 0;

#define BUFSIZE (1024)
int lastTime = 0;
int numRanks = 3;
double distFactor = 1;
int useActualDist = 0;
int multiple;
int tableCompare(const void * p1, const void * p2);
int tableDiffCompare(const void * p1, const void * p2);
int compareRankings(const void * p1, const void * p2);
char P1LogUsr[256];
int skipUsedProfiles = 1;

unsigned long rejSize;
unsigned long tmpRejSize;

int BreakPalletFlag;
int SortRankingFlag;
std::string LogMode;


#define SHOW_TRACE 0
#define CASE_FIT_ONLY -1
#define NATIVE_FIT 1
#define NO_FIT 0
	
Pass1Process::Pass1Process(SockClass *P1Socket)
{
	dataSocket = P1Socket;
}

Pass1Process::~Pass1Process()
{
	int i;

	//////////////////////////////////////////////////////////////
	// Free allocated memory for...
	//////////////////////////////////////////////////////////////
	// Products
	if ( debugLog )
		fprintf(p1TraceFile,"Freeing prodPk\n");
	if (prodPk != NULL)
		free(prodPk);

	// Prod/RackTypes
	if ( debugLog )
		fprintf(p1TraceFile,"Freeing prodRackIdeal\n");
	if (prodRackIdeal != NULL)
		free(prodRackIdeal);

	if ( debugLog )
		fprintf(p1TraceFile,"Freeing prodRackAVail\n");
	if (prodRackAvail != NULL)
		free(prodRackAvail);

	// Rack Types (Rules)
	if ( debugLog )
		fprintf(p1TraceFile,"Freeing rackTy\n");
	if (rackTy != NULL)
		free(rackTy);

	// Basic Rack Types
	if ( debugLog )
		fprintf(p1TraceFile,"Freeing basicTy\n");
	if (basicTy != NULL)
		free(basicTy);

	// Rack Groups - which are really Super Groups, which contain
	// Tables, which contain TableData

	for (i=0;i<NUMBER_OF_RACKGROUPS;i++) {
		if ( debugLog )
			fprintf(p1TraceFile,"Freeing rackGp[%d].Table->TableData\n", i);
		if ( rackGp[i].Table->TableData != NULL)
			free(rackGp[i].Table->TableData);
		if ( debugLog )
			fprintf(p1TraceFile,"Freeing rackGp[%d].Table\n", i);
		if ( rackGp[i].Table != NULL)
			free(rackGp[i].Table);
	}
	if ( debugLog )
		fprintf(p1TraceFile,"Freeing rackGp\n", i);
	if (rackGp != NULL)
		free(rackGp);

	if ( debugLog )
		fprintf(p1TraceFile,"Freeing prodGpDrive\n", i);
	if (prodGpDrive != NULL)
		free(prodGpDrive);

	// Socket
	if ( debugLog )
		fprintf(p1TraceFile,"Deleting dataSocket\n", i);
	delete dataSocket;

	if ( p1TraceFile != NULL && p1TraceFile != stdout)
		fclose(p1TraceFile);

}

void Pass1Process::Execute(void)
{
	char	line[DATA_LINE_LEN];
	char	returnData[DATA_BUFFER_LEN];
	char	errBuffer[DATA_BUFFER_LEN];
	char	passOutMsg[DATA_BUFFER_LEN];
	char	passOutMsg2[DATA_BUFFER_LEN];
	int		rejectionCount;

	int		exitCode, totNumProds, i, p, bestAvlUsgID = 0, bestAvlRackID = 0;
	int		idealIdx;
	unsigned int bytesSent=0;
	prodPk = NULL;
	prodRackIdeal = NULL;
	prodRackAvail = NULL;
	rackTy = NULL;
	rackGp = NULL;

	fprintf(stdout, "Starting Capital Cost Optimization.\n");

	try {
		/* *************************************************** */
		/* Indicate to the caller that we are ready to accept  */
		/* data on the socket.                                 */
		/* *************************************************** */
		//CHECK007 	We don't use socket any more... No need to handshake...
		///memset(returnData, 0, DATA_BUFFER_LEN);
		///sprintf(returnData, "Ready For Data...\n<EOS>\n");
		/***>>>
		///bytesSent = dataSocket->SendData(returnData, strlen(returnData));
		<<<***/
		///gfnGetDataStream()->ssData << returnData;
		/***>>>
		if(bytesSent != strlen(returnData)){
			throw EngineException("Error returning AOK signal.\n",
				__FILE__, __LINE__, 200);
		}
		<<<***/

		GetUserFlags();
		if ( userLog || debugLog ) {
			p1TraceFile = NULL;
			if (SHOW_TRACE)
				p1TraceFile = stdout;
			else
				p1TraceFile = fopen(P1LogUsr,"w");
			
			if ( p1TraceFile == NULL ) {
				fprintf(stdout, "Cannot Create Trace File %s.  Check disk space on device.\n",P1LogUsr);
				fprintf(stdout, "No logging will be printed.\n");
				userLog = 0;
				debugLog = 0;
			}
			else {
				char fullPath[256];
				if (_fullpath(fullPath, P1LogUsr, 256) != NULL)
					fprintf(stdout, "Trace file: %s\n", fullPath);
				else
					fprintf(stdout, "Trace file: %s\n", P1LogUsr);
			}
		}
		else
			p1TraceFile = stdout;
		
		if ( debugLog )
			fprintf(p1TraceFile,"<DBGMSG> Before ReadIniParameters()\n");

		ReadIniParameters();
				
		if ( debugLog )
			fprintf(p1TraceFile,"<DBGMSG> After ReadIniParameters()\n");

		printf("Parameters:\n"
						"Use case handling: %d\n"
						"Rank by utilized height: %d\n"
						"Log Mode: %s\n"
						"Rankings: %d\n"
						"Skip Used Profiles: %d\n",
						BreakPalletFlag, SortRankingFlag, LogMode.c_str(), numRanks, skipUsedProfiles);
		//////////////////////////////////////////////////////////
		// Allocate memory for...
		//////////////////////////////////////////////////////////
		
		if ( debugLog )
			fprintf(p1TraceFile,"<DBGMSG> Before loadRackTypes()\n");

		loadRackTypes();

		if ( debugLog ) {
			fprintf(p1TraceFile,"<DBGMSG> After loadRackTypes()\n\n");
			fprintf(p1TraceFile,"<DBGMSG> Summary of Profiles:\n");
			fprintf(p1TraceFile,"%5s\t%30.30s\t%10s\t%10s\t%7s\t%7s\n",
				"PID","Desc","Lin Units","Fxd Faces","In Fac", "Haz");
			for (i=0;i<numBasicTypes;i++) {
				fprintf(p1TraceFile,"%5d\t%30.30s\t%10f\t%10d\t",basicTy[i].profileID,
					basicTy[i].desc, basicTy[i].startLinealFacings,
					basicTy[i].startFixedFaces);
				if ( basicTy[i].inFacility == 1 ) {
					fprintf(p1TraceFile,"%7s\t","True");
				}
				else {
					fprintf(p1TraceFile,"%7s\t","False");
				}

				if ( basicTy[i].isHazard == 1 ) {
					fprintf(p1TraceFile,"%7s\n","True");
				}
				else {
					fprintf(p1TraceFile,"%7s\n","False");
				}
			}
		}


		this->rejectionList.reserve(numBasicTypes*2);
	
		// Read in data for Rules (Rack Usages), init P1.dll.
		if ( debugLog )
			fprintf(p1TraceFile,"<DBGMSG> Before loadRules()\n");

		loadRules();
		
		if ( debugLog )
			fprintf(p1TraceFile,"<DBGMSG> After loadRules()\n");

		prodGpDrive	= NULL;

		if ( debugLog )
			fprintf(p1TraceFile,"<DBGMSG> Before loadDriveParameters()\n");

		loadDriveParameters();
		
		if ( debugLog )
			fprintf(p1TraceFile,"<DBGMSG> After loadDriveParameters()\n");

		//if (SLOT_DEBUG) printf( "\n\n" );

		totNumProds = 0;	// numprods tallies for each loop.  This is overall total.
		
		// Return results.
		exitCode = 0;
		while ( exitCode == 0 ) {

			if ( debugLog )
				fprintf(p1TraceFile,"<DBGMSG> Before loadProducts()\n");

			exitCode = loadProducts();	// Returns 1 when all products have been received,
										// otherwise 0, which keeps us in the loop.
			if ( debugLog )
				fprintf(p1TraceFile,"<DBGMSG> After loadProducts()\n");

			if ( numProds > 0 ) {

				//if (SLOT_DEBUG) printf("\nRack Assignments:\n");

				strcpy( returnData, "" );	// Init return stream

				// Pass the data for each product to P1.dll, which returns an index
				// value for the product's Ideal and Best Avaialble Rack Types.
		
				if ( debugLog || userLog )
					fprintf(p1TraceFile,"==========================================================================\n");
				for(i=0; i<numProds; i++) {

					//aPass1->getCost(&prodPk[i], &prodRack[i]);

					if ( debugLog )
						fprintf(p1TraceFile,"<DBGMSG> Before getCost()\n");

					memset(passOutMsg,0,DATA_BUFFER_LEN);

					rejectionCount = 0;
					getCost(&prodPk[i], &prodRackIdeal[i], &prodRackAvail[i*numRanks], numRanks, passOutMsg);
					
					/// CHECK: clear the stream before sending the results...
					/// gfnGetDataStream()->ssData.clear();
					
					// send rejections
					std::vector<std::string>::iterator iter;
					for (iter = rejectionList.begin(); iter != rejectionList.end(); ++iter) {

						int sz = (*iter).size();
						if (strlen(returnData)+ (*iter).size() >= DATA_BUFFER_LEN) {
							//printf("Sending intermediate data\n");
							
							///***>>> Sock code TBR
							///dataSocket->SendData(returnData, strlen(returnData));
							gfnGetDataStream()->ssData << returnData;

							memset(returnData,0,DATA_BUFFER_LEN );	// reInit return stream
						}
						strcat( returnData, (*iter).c_str() );
					}

					rejectionList.erase(rejectionList.begin(), rejectionList.end());

					if ( debugLog )
						fprintf(p1TraceFile,"<DBGMSG> After getCost()\n");
					
					idealIdx = getBasicTypeIndex(prodRackIdeal[i].idealProfileID,prodRackIdeal[i].idealLevelType);
					
					if (idealIdx == -1) {
						if ( debugLog) {
							fprintf(p1TraceFile,"<DBGMSG> Product %20.20s [DBID=%10d]", prodPk[i].desc,prodPk[i].dbID);
							if ( prodPk[i].hazard == 1 )
								fprintf(p1TraceFile,"is hazard, and no hazard profiles are available\n");
							else
								fprintf(p1TraceFile,"is non-hazard, and no non-hazard profiles are available\n");
						}
						else if ( userLog || prodPk[i].trace) {
							fprintf(p1TraceFile,"Product %20.20s [WMSID=%10s]", prodPk[i].desc,prodPk[i].WMSProdID);
							if ( prodPk[i].hazard == 1 )
								fprintf(p1TraceFile,"is hazard, and no hazard profiles are available\n");
							else
								fprintf(p1TraceFile,"is non-hazard, and no non-hazard profiles are available\n");
						}

						if ( prodPk[i].hazard == 1 )
							sprintf(passOutMsg,"Y|M|%d|Product marked as hazard, and no hazard profiles are available|\n",prodPk[i].dbID);
						else
							sprintf(passOutMsg,"Y|M|%d|Product marked as non-hazard, and no non-hazard profiles are available|\n",prodPk[i].dbID);

						if (strlen(returnData)+strlen(passOutMsg)>=DATA_BUFFER_LEN) {

							//printf("Sending intermediate data\n");
							gfnGetDataStream()->ssData << returnData;

							memset(returnData,0,DATA_BUFFER_LEN );	// reInit return stream
						}
						strcat( returnData, passOutMsg );
						continue; //no hazard or non-hazard racks in DB
					}
					prodRackIdeal[i].productPackID = prodPk[i].dbID;

					memset(line,0,DATA_LINE_LEN);

					if ( userLog || debugLog ) {
						if ( prodPk[i].optBy == 0 ) {
							fprintf(p1TraceFile,"Product %20.20s [WMSID=%10s] [ExtendedCube=%10.4f] [ExtendedBOH=%10.4f] [OptBy=Cube] was assigned to:\n", prodPk[i].desc,prodPk[i].WMSProdID,
								prodPk[i].xCube/multiple, prodPk[i].xBOH/multiple);
						}
						else {
							fprintf(p1TraceFile,"Product %20.20s [WMSID=%10s] [ExtendedCube=%10.4f] [ExtendedBOH=%10.4f] [OptBy=Labor] was assigned to:\n", prodPk[i].desc,prodPk[i].WMSProdID,
								prodPk[i].xCube/multiple, prodPk[i].xBOH/multiple);
						}
						fprintf(p1TraceFile,"\tIdeal      : %20.20s\t%5d Facing(s) [ExtendedCube=%10.4f] [ExtendedBOH=%10.4f]\n",
							basicTy[idealIdx].desc, prodRackIdeal[i].facingCount, prodRackIdeal[i].xCube/multiple, prodRackIdeal[i].xBOH/multiple);
					}

					if ( basicTy[idealIdx].startLinealFacings == 0 ) {
						sprintf(line,"Y|I|%d|%s|%d|%d|%d|%s|%d|%s|%s|\n",
							prodPk[i].dbID,
							prodPk[i].desc,
							prodRackIdeal[i].facingCount,
							prodRackIdeal[i].idealProfileID,
							prodRackIdeal[i].idealLevelType,
							basicTy[idealIdx].desc,0,
							prodPk[i].WMSProdID,
							prodPk[i].WMSProdDetID);
					}
					else {
						sprintf(line,"Y|I|%d|%s|%d|%d|%d|%s|%d|%s|%s|\n",
							prodPk[i].dbID,
							prodPk[i].desc,
							prodRackIdeal[i].facingCount,
							prodRackIdeal[i].idealProfileID,
							prodRackIdeal[i].idealLevelType,
							basicTy[idealIdx].desc,
							(int)(prodRackIdeal[i].facingCount * prodPk[i].width),
							prodPk[i].WMSProdID,
							prodPk[i].WMSProdDetID);
					}

					if ( debugLog ) {
						fprintf(p1TraceFile,"<DBGMSG> Sending data on socket [Ideal   ] : %s",line);
					}

					if (strlen(returnData)+strlen(line)>=DATA_BUFFER_LEN) {
						//printf("Sending intermediate data\n");
						gfnGetDataStream()->ssData << returnData;

						memset(returnData,0,DATA_BUFFER_LEN );	// reInit return stream
					}
					strcat( returnData, line );

					for ( p = 0; p < numRanks; p++) {

						idealIdx = getBasicTypeIndex(prodRackAvail[i*numRanks+p].availProfileID,prodRackAvail[i*numRanks+p].availLevelType);
						
						memset(line,0,DATA_LINE_LEN);

						if ( idealIdx > -1 ) {

							if ( userLog || debugLog ) {
								fprintf(p1TraceFile,"\tBest Avail : %30.30s\t%5d Facing(s) [ExtendedCube=%10.4f] [ExtendedBOH=%10.4f] Rank %d\n",
									basicTy[idealIdx].desc, prodRackAvail[i*numRanks+p].facingCount, prodRackAvail[i*numRanks+p].xCube/multiple, prodRackAvail[i*numRanks+p].xBOH/multiple,p+1);
							}

							prodRackAvail[i*numRanks+p].productPackID = prodPk[i].dbID;
							sprintf(line,"Y|B|%d|%s|%d|%d|%d|%d|%d|%d|%s|%d|%s|%s|%d|%d|%d|\n",
								prodPk[i].dbID,
								prodPk[i].desc,
								prodRackAvail[i*numRanks+p].facingCount,
								prodRackAvail[i*numRanks+p].availProfileID,
								prodRackAvail[i*numRanks+p].availLevelType,
								prodRackAvail[i*numRanks+p].linealFacingCount,
								prodRackAvail[i*numRanks+p].linealFacingWidth,
								//prodRackAvail[i*numRanks+p].ranking+1,
								p+1,
								basicTy[idealIdx].desc,
								0, prodPk[i].WMSProdID, prodPk[i].WMSProdDetID,
								prodRackAvail[i*numRanks+p].origHandling,
								prodRackAvail[i*numRanks+p].actualHandling,
								prodRackAvail[i*numRanks+p].fits);
						}
						else {

							if ( userLog || debugLog || prodPk[i].trace) {
								fprintf(p1TraceFile,"\tBest Avail : No profile was found for product.  ");
								if ( prodPk[i].hazard == 1 )
									fprintf(p1TraceFile,"Product is hazard.  Check to make sure hazard racks are available to use\n");
								else
									fprintf(p1TraceFile,"Product is non-hazard.  Check to make sure non-hazard racks are available to use\n");
							}
							if ( prodPk[i].hazard == 1 )
								sprintf(passOutMsg,"Product is hazard, and no hazard profiles are in the facility");
							else
								sprintf(passOutMsg,"Product is non-hazard, and no non-hazard profiles are in the facility");
							prodRackAvail[i*numRanks+p].productPackID = prodPk[i].dbID;
							sprintf(line,"Y|B|%d|%s|%d|%d|%d|%d|%d|%d|%s|%d|%s|%s|%d|%d|%d|\n",
								prodPk[i].dbID,
								prodPk[i].desc,
								0,
								0,
								0,
								0,
								0,
								p+1,
								"No profile found for product.",
								0, prodPk[i].WMSProdID, prodPk[i].WMSProdDetID,
								0,
								0,
								0);
						}
						
						if ( debugLog ) {
							fprintf(p1TraceFile,"<DBGMSG> Sending data on socket [Best Avl] : %s",line);
						}

						if (strlen(returnData)+strlen(line)>=DATA_BUFFER_LEN) {
							//printf("Sending intermediate data\n");
							gfnGetDataStream()->ssData << returnData;

							memset(returnData,0,DATA_BUFFER_LEN );	// reInit return stream
						}
						strcat( returnData, line );
					}

					sprintf(passOutMsg2,"Successfully assigned.  %s|\n",passOutMsg);
					if ( userLog || prodPk[i].trace) {
						fprintf(p1TraceFile,"\t\t%s",passOutMsg2);
					}
					sprintf(passOutMsg,"Y|M|%d|%s",prodPk[i].dbID,passOutMsg2);
					if (strlen(returnData)+strlen(passOutMsg)>=DATA_BUFFER_LEN) {
						//printf("Sending intermediate data\n");
						gfnGetDataStream()->ssData << returnData;

						memset(returnData,0,DATA_BUFFER_LEN );	// reInit return stream
					}
					strcat( returnData, passOutMsg );

					////////////////////////////////////////////////////////////////////////////
					// Construct result line to send back to Forte. Example:
					//
					//   "Y|0|123|Prod Desc|4005|666|Ideal Rack Desc|1|7008|999|Best Avl Rack Desc|2|\n",
					//
					// where 123 is the product's DBID, 4005 is the DBID of the Ideal Usage, 666
					// is the DBID of the Ideal rack type, 1 is the number of Facings of the Ideal
					// rack type, 7008 is the DBID of the Best Avl Usage, 999 is the DBID of the
					// Best Available rack type and 2 is the number of Facings of the Best Available
					// rack type.
					////////////////////////////////////////////////////////////////////////////

					// Check to see if the next line will make the stream longer than the
					// send buffer.  If so, send the existing stream and re-init the buffer
					// before copying the line in.
					if ( debugLog || userLog || prodPk[i].trace)
						fprintf(p1TraceFile,"==========================================================================\n");
				}

				//if (SLOT_DEBUG) printf("\n");
				//printf("Done with that bit\n");

				for ( i=0; i < numBasicTypes; i++) {
					memset(line,0,DATA_LINE_LEN);
					//if ( basicTy[i].avlFixedFaces < 0 || basicTy[i].avlLinealFacings < 0 ) &&
					//	basicTy[i].countNeeded == 0 ) {
					//	basicTy[i].countNeeded = -1* ( basicTy[i].avlFixedFaces );
					//}
					sprintf(line,"Y|L|%d|%d|%s|%d|%d|%d|%d|%d|%d|\n",
						basicTy[i].profileID,
						basicTy[i].levelType,
						basicTy[i].desc,
						(int)basicTy[i].startLinealFacings,
						(int)basicTy[i].avlLinealFacings,
						basicTy[i].startFixedFaces,
						basicTy[i].avlFixedFaces,
						basicTy[i].countNeeded,
						basicTy[i].totFacings);
					
					if ( debugLog ) {
						fprintf(p1TraceFile,"<DBGMSG> Sending data on socket [Rck Summ] : %s",line);
					}

					if (strlen(returnData)+strlen(line)>=DATA_BUFFER_LEN) {
						//printf("Sending intermediate data\n");
						gfnGetDataStream()->ssData << returnData;

						memset(returnData,0,DATA_BUFFER_LEN );	// reInit return stream
					}
					strcat(returnData,line);
				}

				// End-of-Stream protocol tag
				strcat( returnData, END_OF_STREAM );
				strcat( returnData, DATA_DELIMITER );
				strcat( returnData, "\n" );

				// Send last sub-stream of results back through socket.
				gfnGetDataStream()->ssData << returnData;

				//if (SLOT_DEBUG) printf("Data sent.\n");

				// Increment the grand total tally with the number of products processed
				// in this loop.
				totNumProds += numProds;
				//if (SLOT_DEBUG) printf("%d Products processed.\n", totNumProds);

			}	// if numProds > 0

		}	// while exitCode == 0

	} catch(EngineException ee) {

		if (p1TraceFile != NULL)
			fflush(p1TraceFile);

		fprintf(stdout, "Capital Cost Optimization failed.\n");		
		if ( debugLog ) {
			fprintf(p1TraceFile,"Exception Caught During Processing.\n");
			ee.GetAllMessage(errBuffer);
			fprintf(p1TraceFile,"%s\n",errBuffer);
		}

		memset(errBuffer, 0, DATA_BUFFER_LEN);
		sprintf(errBuffer, "Error in Engine\n<EOS>\n");
		/***>>>  TBR
		bytesSent = dataSocket->SendData(errBuffer, strlen(errBuffer));
		*/
		gfnGetDataStream()->ssData << errBuffer;
		/** TBR
		if(bytesSent < strlen(errBuffer))
			throw EngineException("Error Sending ERROR Signal",
				__FILE__, __LINE__, 200);
		**/

		return;

	} catch(...){
		if (p1TraceFile != NULL)
			fflush(p1TraceFile);

		fprintf(stdout, "Capital Cost Analysis failed.\n");
		printf("\n----------\nCaught an unhandled exception!\n----------\n");
		memset(errBuffer, 0, DATA_BUFFER_LEN);
		sprintf(errBuffer, "Error in Engine\n<EOS>\n");
		/**TBR
		bytesSent = dataSocket->SendData(errBuffer, strlen(errBuffer));
		*/
		gfnGetDataStream()->ssData << errBuffer;
		/** TBR
		if(bytesSent < strlen(errBuffer))
			throw EngineException("Error Sending ERROR Signal",
				 __FILE__, __LINE__, 200);
		**/
		if ( debugLog ) {
			fprintf(p1TraceFile,"Exception Caught During Processing.\n");
		}
		// Don't exit- just go back to Listen
		//if (SLOT_DEBUG) printf( "\nAborting Pass 1.\n" );

		return;
	}
	//if ( p1TraceFile != NULL && p1TraceFile != stdout)
	//	fclose(p1TraceFile);

	fprintf(stdout, "Capital Cost Optimization completed successfully.\n");
	return;
}

void Pass1Process::loadRules(void)
{
	int			errCode = 0;

	char		line[MAX_INPUT_DATA_LEN];
	char		*subString;
	int			MemAmount = 0;
	int			n = 0;

	numRackUsages = 0;		// Passed to build methods as array index
	numRackGroups = 0;	// Passed to build methods as array index

	// Get data from the socket and build Rule set structure.
	while( 1 )
	{
		// Read a line of data.
		memset(line, 0, MAX_INPUT_DATA_LEN);

/*
		if( gfnGetDataStream()->ssData.getline( line, MAX_INPUT_DATA_LEN ) == 0 ) {
			throw EngineException("Data file closed.\n",
				 __FILE__, __LINE__, 200);
		}
*/
		if ( gfnGetDataStream()->ssData.getline( line, MAX_INPUT_DATA_LEN ) == 0 )
			throw EngineException("Data file closed.\n", __FILE__, __LINE__, 200);
		if ( debugLog ) {
			fprintf(p1TraceFile,"<DBGMSG> Rules Line Received : %s",line);
		}

//		printf("%s", line);

		// Check beginning code for line type
		subString = strtok(line, DATA_DELIMITER);
		switch ( *subString ) {
			case 'M': 
				subString = strtok(NULL, DATA_DELIMITER);
				MemAmount = (int)atoi( subString );
				if ( debugLog ) {
					fprintf(p1TraceFile,"<DBGMSG> Allocating Rules Memory : %d",MemAmount);
				}
				rackTy = (TABLE_ENTRY *)malloc( sizeof(TABLE_ENTRY) * MemAmount );
				if ( rackTy == NULL )
					throw EngineException("Insufficient memory available for Rack Usages.\n",
						 __FILE__, __LINE__, 200);

				memset(rackTy,0,sizeof(TABLE_ENTRY) * MemAmount);
				// Rack Types sorted by Super Group
				rackGp = (P1DATA *)malloc( sizeof(P1DATA) * NUMBER_OF_RACKGROUPS );
				if ( rackGp == NULL )
					throw EngineException("Insufficient memory available for Rack Type Groups.\n",
						 __FILE__, __LINE__, 200);
				memset(rackGp,0,sizeof(P1DATA)*NUMBER_OF_RACKGROUPS);

				// Tally numbers for the super groups
				for (n=0; n<NUMBER_OF_RACKGROUPS; ++n) {

					// Allocate memory for Tables
					rackGp[n].Table = (P1TABLE *)malloc( sizeof(P1TABLE) );
					if ( rackGp[n].Table == NULL )
						throw EngineException("Insufficient memory available for Rack Type Table header.\n",
							 __FILE__, __LINE__, 200);

					// Allocate memory for Tables
					rackGp[n].Table->TableData = (TABLE_ENTRY *)malloc(sizeof(TABLE_ENTRY) * MemAmount);
					if ( rackGp[n].Table->TableData == NULL )
						throw EngineException("Insufficient memory available for Rack Type Table list.\n",
							 __FILE__, __LINE__, 200);
					// Set Rack Type table count to 0
					memset(rackGp[n].Table->TableData,0,sizeof(TABLE_ENTRY) * MemAmount);
					rackGp[n].Table->ItemCount = 0;
				}
				break;

			case 'R':								// Rack Usage
				//if ( numRackUsages == MAX_NUM_RACK_USAGES ) {
				//	throw EngineException("Max number of Rack Types exceeded.\n",
				//		 __FILE__, __LINE__, 200);
				//}

				if ( debugLog ) {
					fprintf(p1TraceFile,"<DBGMSG> Before buildUsageRec()\n");
				}
				buildUsageRec(numRackUsages);
				if ( debugLog ) {
					fprintf(p1TraceFile,"<DBGMSG> After buildUsageRec()\n");
				}

				break;

			case '<':	// Protocol Tag
				if		( strstr( subString, START_OF_STREAM ) != NULL )
					break;	// Loop again for more data
				else if ( strstr( subString, START_OF_SUBLIST ) != NULL ) {
					break;	// Loop again for more data
				}
				else if ( strstr( subString, END_OF_SUBLIST ) != NULL ) {
					//if (SLOT_DEBUG)
					//	printf("\nError:  End of sublist received during Rules transmission.\n");
				}
				else if( strstr( subString, END_OF_STREAM ) != NULL ) {
					//if (SLOT_DEBUG) printf( "End of Data.\n\n" );

					// Move RackType data into Super Group tables
					// Calculate regression numbers for Super Groups
					if ( debugLog ) {
						fprintf(p1TraceFile,"<DBGMSG> Before addRackGroups() : %s",line);
					}
					addRackGroups();
					if ( debugLog ) {
						fprintf(p1TraceFile,"<DBGMSG> After addRackGroups() : %s",line);
					}

					// Load Rack Type data into DLL
					if ( debugLog ) {
						fprintf(p1TraceFile,"<DBGMSG> Before p1_init() : %s",line);
					}
					errCode = p1_init();
					if ( debugLog ) {
						fprintf(p1TraceFile,"<DBGMSG> After p1_init() : %s",line);
					}

					if (errCode != 0) {
						sprintf(errMsg, "Error %d received from P1.dll.\n", errCode);
						throw EngineException(errMsg, __FILE__, __LINE__, 200);
					}
				}
				else {
					sprintf(errMsg, "\nInvalid protocol tag '%s' read!\n", subString);
					throw EngineException(errMsg, __FILE__, __LINE__, 200);
				}

				return;	// EOS

//			default:								// Unknown Type
				//if (SLOT_DEBUG) printf( "Unrecognized input data line read:\n%s\n", line);

		} // End of switch stmt

	} // End of while loop

	//if (SLOT_DEBUG) printf( "Missing End-Of-Stream protocol tag!\n" );

	return;	// Error

}

void Pass1Process::loadDriveParameters(void)
{
	int			errCode = 0;

	char		line[MAX_INPUT_DATA_LEN];
	char		*subString;
	int			MemAmount = 0;
	int			n = 0;

	numDriveParameters = 0;

	// Get data from the socket and build Rule set structure.
	while( 1 )
	{
		// Read a line of data.
		memset(line, 0, MAX_INPUT_DATA_LEN);
/*
		if( gfnGetDataStream()->ssData.getline( line, MAX_INPUT_DATA_LEN ) == 0 ) {
			throw EngineException("Data file closed.\n",
				 __FILE__, __LINE__, 200);
		}
*/
		if ( gfnGetDataStream()->ssData.getline( line, MAX_INPUT_DATA_LEN ) == 0 )
			throw EngineException("Data file closed.\n", __FILE__, __LINE__, 200);

		if ( debugLog ) {
			fprintf(p1TraceFile,"<DBGMSG> Drive Parameter Line Received : %s",line);
		}
		//printf("%s", line);

		// Check beginning code for line type
		subString = strtok(line, DATA_DELIMITER);
		switch ( *subString ) {
			case 'M': 
				subString = strtok(NULL, DATA_DELIMITER);
				MemAmount = (int)atoi( subString );
				if ( MemAmount != 0 )  {
					if ( debugLog ) {
						fprintf(p1TraceFile,"<DBGMSG> Allocating Product Group Drive Memory : %d",MemAmount);
					}
					prodGpDrive = (PROD_GROUP_DRIVE *)malloc( sizeof(PROD_GROUP_DRIVE) * MemAmount );
					if ( prodGpDrive == NULL )
						throw EngineException("Insufficient memory available for Drive Parameters.\n",
							 __FILE__, __LINE__, 200);

					memset(prodGpDrive,0,sizeof(PROD_GROUP_DRIVE) * MemAmount);
				}
				// Rack Types sorted by Super Group
				break;

			case 'D':								// Rack Usage

				if ( debugLog ) {
					fprintf(p1TraceFile,"<DBGMSG> Before buildDriveRec()\n");
				}
				buildDriveRec(numDriveParameters);
				if ( debugLog ) {
					fprintf(p1TraceFile,"<DBGMSG> After buildDriveRec()\n");
				}

				break;

			case '<':	// Protocol Tag
				if		( strstr( subString, START_OF_STREAM ) != NULL )
					break;	// Loop again for more data
				else if ( strstr( subString, START_OF_SUBLIST ) != NULL ) {
					break;	// Loop again for more data
				}
				else if ( strstr( subString, END_OF_SUBLIST ) != NULL ) {
					//if (SLOT_DEBUG)
					//	printf("\nError:  End of sublist received during Rules transmission.\n");
				}
				else if( strstr( subString, END_OF_STREAM ) != NULL ) {
					//if (SLOT_DEBUG) printf( "End of Data.\n\n" );

				}
				else {
					sprintf(errMsg, "\nInvalid protocol tag '%s' read!\n", subString);
					throw EngineException(errMsg, __FILE__, __LINE__, 200);
				}

				return;	// EOS

//			default:								// Unknown Type
				//if (SLOT_DEBUG) printf( "Unrecognized input data line read:\n%s\n", line);

		} // End of switch stmt

	} // End of while loop

	//if (SLOT_DEBUG) printf( "Missing End-Of-Stream protocol tag!\n" );

	return;	// Error

}

int Pass1Process::loadProducts()
{
	int			errCode = 0;
	int			MemAmount = 0;
	char		line[MAX_INPUT_DATA_LEN];
	char		*subString;

	numProds = 0;		// Passed to build methods as array index

	// Get data from the socket
	while( 1 )
	{
		// Read a line of data.
		memset(line, 0, MAX_INPUT_DATA_LEN);
/***>>> socket code TBR
		if( gfnGetDataStream()->ssData.getline( line, MAX_INPUT_DATA_LEN ) == 0 )
			throw EngineException("Data file closed.\n", __FILE__, __LINE__, 200);
<<<***/
		if ( gfnGetDataStream()->ssData.getline( line, MAX_INPUT_DATA_LEN ) == 0 )
			throw EngineException("Data file closed.\n", __FILE__, __LINE__, 200);
		if ( debugLog ) {
			fprintf(p1TraceFile,"<DBGMSG> Product Line Received : %s",line);
		}

		// Check beginning code for line type
		subString = strtok(line, DATA_DELIMITER);
		switch ( *subString ) {
			case 'M':
				subString = strtok(NULL, DATA_DELIMITER);
				MemAmount = (int)atoi( subString );

				if ( MemAmount == 0 )
					return -1; // No more products

				if ( debugLog ) {
					fprintf(p1TraceFile,"<DBGMSG> Allocating Product Memory : %d",MemAmount);
				}
				prodPk = (prodPack *)malloc( sizeof(prodPack) * MemAmount );
				if ( prodPk == NULL )
					throw EngineException("Insufficient memory available for Products.\n",
						__FILE__, __LINE__, 200);

				memset(prodPk,0,sizeof(prodPack) * MemAmount);
				prodRackIdeal = (idealResult *)malloc( sizeof(idealResult) * MemAmount );
				if ( prodRackIdeal == NULL )
					throw EngineException("Insufficient memory available for Prod/RackTypes.\n",
						__FILE__, __LINE__, 200);

				memset(prodRackIdeal,0,sizeof(idealResult) * MemAmount);
				
				prodRackAvail = (availResult *)malloc( sizeof(availResult) * MemAmount * numRanks );
				if ( prodRackAvail == NULL )
					throw EngineException("Insufficient memory available for Prod/RackTypes.\n",
						__FILE__, __LINE__, 200);
				
				memset(prodRackAvail,0,sizeof(availResult) * MemAmount * numRanks);
				
				break;
			case 'P':								// Product
				//if ( numProds > MAX_NUM_PRODUCTS ) {
				//	throw EngineException("Max number of Products exceeded.\n",
				//		__FILE__, __LINE__, 200);
				//}

				if ( debugLog ) {
					fprintf(p1TraceFile,"<DBGMSG> Before buildProdRec()\n");
				}

				buildProdRec(numProds);

				if ( debugLog ) {
					fprintf(p1TraceFile,"<DBGMSG> After buildProdRec()\n");
				}

				//if (numProds%2!=0) {
				//	if (SLOT_DEBUG) printf("\n");
				//}

				//if (numProds%100==0) {
				//	if (SLOT_DEBUG) printf("%d Products loaded.\n", numProds);
				//}

				numProds++;

				break;

			case '<':	// Protocol Tag
				if		( strstr( subString, START_OF_STREAM ) != NULL )
					break;	// Loop again for more data
				else if ( strstr( subString, START_OF_SUBLIST ) != NULL ) {
					break;	// Loop again for more data
				}
				else if ( strstr( subString, END_OF_SUBLIST ) != NULL ) {
					//if (SLOT_DEBUG) printf( "\nEnd of sublist of %d rows.\n", numProds );
					return 0;	// Loop again for more data
				}
				else if( strstr( subString, END_OF_STREAM ) != NULL ) {
					//if (SLOT_DEBUG) printf( "End of Data.\n\n" );
					return 1;	// EOS
				}
				else {
					sprintf(errMsg, "\nInvalid protocol tag '%s' read!\n", subString);
					throw EngineException(errMsg, __FILE__, __LINE__, 200);
				}

//			default:								// Unknown Type
				//if (SLOT_DEBUG) printf( "Unrecognized input data line read:\n%s\n", line);

		}		// End of switch stmt

	}			// End of while loop = EOF

	return 0;	// No errors

}

void Pass1Process::loadRackTypes(void)
{
	int			errCode = 0;
	int			MemAmount = 0;
	char		line[MAX_INPUT_DATA_LEN];
	char		*subString;

	numBasicTypes = 0;	// Passed to build methods as array index

	// Get data from the socket
	while( 1 )
	{
		// Read a line of data.
		memset(line, 0, MAX_INPUT_DATA_LEN);
/***>>> socket code TBR
		if( gfnGetDataStream()->ssData.getline( line, MAX_INPUT_DATA_LEN ) == 0 ) {
			throw EngineException("Data file closed.\n",
				__FILE__, __LINE__, 200);
		}
<<<***/
		if ( gfnGetDataStream()->ssData.getline( line, MAX_INPUT_DATA_LEN ) == 0 )
			throw EngineException("Data file closed.\n", __FILE__, __LINE__, 200);
		
		if ( debugLog ) {
			fprintf(p1TraceFile,"<DBGMSG> Profile Line Received : %s",line);
		}

		//printf("%s", line);
		// Check beginning code for line type
		subString = strtok(line, DATA_DELIMITER);
		switch ( *subString ) {
			case 'M': 
				subString = strtok(NULL, DATA_DELIMITER);
				MemAmount = (int)atoi( subString );
				if ( debugLog ) {
					fprintf(p1TraceFile,"<DBGMSG> Allocating Profile Memory : %d",MemAmount);
				}
				basicTy = (BASIC_RACK_TYPE *)malloc( sizeof(BASIC_RACK_TYPE) * MemAmount );
				if ( basicTy == NULL )
					throw EngineException("Insufficient memory available for Basic Rack Types.\n",
						__FILE__, __LINE__, 200);

				memset(basicTy,0,sizeof(BASIC_RACK_TYPE) * MemAmount);
				break;

			case 'T':								// Basic Rack Type

				if ( debugLog ) {
					fprintf(p1TraceFile,"<DBGMSG> Before buildRackRec()\n");
				}
				buildRackRec(numBasicTypes);
				if ( debugLog ) {
					fprintf(p1TraceFile,"<DBGMSG> After buildRackRec()\n");
				}

				numBasicTypes++;

				break;

			case '<':
				if		( strstr( subString, START_OF_STREAM ) != NULL )
					break;	// Loop again for more data
				else if ( strstr( subString, START_OF_SUBLIST ) != NULL ) {
					break;	// Loop again for more data
				}
				else if ( strstr( subString, END_OF_SUBLIST ) != NULL ) {
					//if (SLOT_DEBUG) printf( "\nEnd of sublist of %d rows.\n", numBasicTypes );
					break;	// Loop again for more data
				}
				else if( strstr( subString, END_OF_STREAM ) != NULL ) {
					//if (SLOT_DEBUG) printf( "End of Data.\n\n" );
					return;	// EOS
				}
				else {
					sprintf(errMsg, "\nInvalid protocol tag '%s' read!\n", subString);
					throw EngineException(errMsg, __FILE__, __LINE__, 200);
				}

//			default:								// Unknown Type
				//if (SLOT_DEBUG) printf( "Unrecognized input data line read:\n%s\n", line);

		}		// End of switch stmt

	}			// End of while loop = EOS

	return;	// No errors
}

// Move input data line into Product data struct
void Pass1Process::buildProdRec(int n)
{
	char		*subString;

	// printf("entering build prod rec\n");

	// Build a Product Pack structure
	subString = strtok(NULL, DATA_DELIMITER);
	prodPk[n].dbID = (int)atoi( subString );
	//if (SLOT_DEBUG) printf( "* Prod #%d: ", prodPk[n].dbID );

	subString = strtok(NULL, DATA_DELIMITER);
	memcpy( &prodPk[n].desc, subString, (strlen( subString ) + 1) );
	//if (SLOT_DEBUG) printf( "%s, ", prodPk[n].desc );

	subString = strtok(NULL, DATA_DELIMITER);
	prodPk[n].height = (double)atof( subString );
	//if (SLOT_DEBUG) printf( "H=%f ", prodPk[n].height );

	subString = strtok(NULL, DATA_DELIMITER);
	prodPk[n].width = (double)atof( subString );
	//if (SLOT_DEBUG) printf( "W=%f ", prodPk[n].width );

	subString = strtok(NULL, DATA_DELIMITER);
	prodPk[n].length = (double)atof( subString );
	//if (SLOT_DEBUG) printf( "L=%f ", prodPk[n].length );

	subString = strtok(NULL, DATA_DELIMITER);
	prodPk[n].nestWidth = atof( subString );
	//if (SLOT_DEBUG) printf( "H=%f ", prodPk[n].height );

	subString = strtok(NULL, DATA_DELIMITER);
	prodPk[n].nestLength = atof( subString );
	//if (SLOT_DEBUG) printf( "W=%f ", prodPk[n].width );

	subString = strtok(NULL, DATA_DELIMITER);
	prodPk[n].nestHeight = atof( subString );
	//if (SLOT_DEBUG) printf( "L=%f ", prodPk[n].length );

	subString = strtok(NULL, DATA_DELIMITER);
	prodPk[n].BOH = (double)atof( subString );
	//if (SLOT_DEBUG) printf( "BOH=%f\n", prodPk[n].BOH );

	subString = strtok(NULL, DATA_DELIMITER);
	prodPk[n].hazard = (int)atoi( subString );
	//if (SLOT_DEBUG) printf( "     Haz=%d ", prodPk[n].hazard );

	subString = strtok(NULL, DATA_DELIMITER);
	prodPk[n].UOI = (int)atoi( subString );
	//if (SLOT_DEBUG) printf( "UOI=%d ", prodPk[n].UOI );

	subString = strtok(NULL, DATA_DELIMITER);
	prodPk[n].optBy = (int)atoi( subString );
	//if (SLOT_DEBUG) printf( "Opt=%d ", prodPk[n].optBy );

	subString = strtok(NULL, DATA_DELIMITER);
	prodPk[n].cube = (double)atof( subString );
	//if (SLOT_DEBUG) printf( "Cube=%f", prodPk[n].cube );

	subString = strtok(NULL, DATA_DELIMITER);
	prodPk[n].movement = (double)atof( subString );
	//if (SLOT_DEBUG) printf( "Mvmt=%f\n", prodPk[n].movement );

	subString = strtok(NULL, DATA_DELIMITER);
	prodPk[n].prodGpID = (int)atoi( subString );
	//if (SLOT_DEBUG) printf( "ProdGp=%d ", prodPk[n].prodGpID );

	subString = strtok(NULL, DATA_DELIMITER);
	prodPk[n].containerWidth = (double)atof( subString );
	//if (SLOT_DEBUG) printf( "ConWidth=%f\n", prodPk[n].containerWidth );

	subString = strtok(NULL, DATA_DELIMITER);
	memcpy( &prodPk[n].WMSProdID, subString, (strlen( subString ) + 1) );

	subString = strtok(NULL, DATA_DELIMITER);
	memcpy( &prodPk[n].WMSProdDetID, subString, (strlen( subString ) + 1) );

	subString = strtok(NULL, DATA_DELIMITER);
	prodPk[n].weight = (double)atof( subString );

	subString = strtok(NULL, DATA_DELIMITER);
	prodPk[n].containerLength = (double)atof( subString );
	
	subString = strtok(NULL, DATA_DELIMITER);
	prodPk[n].containerHeight = (double)atof( subString );

	subString = strtok(NULL, DATA_DELIMITER);
	prodPk[n].contOverrideHeight = (int)atoi( subString );

	subString = strtok(NULL, DATA_DELIMITER);
	prodPk[n].trace = (int)atoi( subString );

	subString = strtok(NULL, DATA_DELIMITER);
	prodPk[n].ti = (int)atoi( subString );

	subString = strtok(NULL, DATA_DELIMITER);
	prodPk[n].hi = (int)atoi( subString );


//	if (SLOT_DEBUG) printf( "%s, ", prodPk[n].desc );
	// Extended Cube and Extended BOH
	prodPk[n].xCube = ( prodPk[n].cube * prodPk[n].movement );
	prodPk[n].xBOH = ( prodPk[n].cube * prodPk[n].BOH );

	
	if (prodPk[n].trace) {
		if (! userLog && ! debugLog) {

			if (SHOW_TRACE)
				p1TraceFile = stdout;
			else
				p1TraceFile = fopen(P1LogUsr,"w");
			
			if ( p1TraceFile == NULL ) {
				fprintf(stdout, "Cannot Create Trace File %s.  Check disk space on device.\n",P1LogUsr);
				fprintf(stdout, "No logging will be printed.\n");
				userLog = 0;
				debugLog = 0;
				p1TraceFile = stdout;
			}
			else {
				char fullPath[256];
				if (_fullpath(fullPath, P1LogUsr, 256) != NULL)
					fprintf(stdout, "Trace file: %s\n", fullPath);
				else
					fprintf(stdout, "Trace file: %s\n", P1LogUsr);
			}
		}

		fprintf(p1TraceFile, "Tracing Product: %s-%s(%d) - %s\n", 
			prodPk[n].WMSProdID, prodPk[n].WMSProdDetID,
			prodPk[n].dbID,	prodPk[n].desc);
		fprintf(p1TraceFile, "\tExtended Cube: %.04f, Extended BOH: %.04f\n", 
			prodPk[n].xCube, prodPk[n].xBOH);
		fprintf(p1TraceFile, "\tWidth: %.02f, Length: %.02f, Height: %.02f\n",
			prodPk[n].width, prodPk[n].length, prodPk[n].height);
		fprintf(p1TraceFile, "\tContainer Width: %.02f, Length: %.02f, Height; %.02f, Overridden Height: %.02f\n",
			prodPk[n].containerWidth, prodPk[n].containerLength, prodPk[n].containerHeight,
			prodPk[n].contOverrideHeight);
		fprintf(p1TraceFile, "\tTixHi: %dx%d\n", prodPk[n].ti, prodPk[n].hi);
		fprintf(p1TraceFile, "\tUnit of Issue: %s\n",
			prodPk[n].UOI == 0 ? "Each" : prodPk[n].UOI == 1 ? "Inner" : prodPk[n].UOI == 2 ? "Case" : "Pallet");

	}


	return;

}	// End of buildProdRec()

// Move data from input into a Rack Type structure
void Pass1Process::buildUsageRec(int n)
{
	char		*subString;

	// Build a Rack Type structure

	// Copy the stream input into a Table structure
	subString = strtok(NULL, DATA_DELIMITER);
	rackTy[n].profileID = (int)atoi( subString );

	subString = strtok(NULL, DATA_DELIMITER);
	rackTy[n].levelType = (int)atoi( subString );
	
	subString = strtok(NULL, DATA_DELIMITER);
	rackTy[n].facingID = (int)atoi( subString );

	subString = strtok(NULL, DATA_DELIMITER);
	rackTy[n].superGroup = (int)atoi( subString );

	subString = strtok(NULL, DATA_DELIMITER);
	rackTy[n].facingCount = (int)atoi( subString );

	subString = strtok(NULL, DATA_DELIMITER);
	// if (SLOT_DEBUG) printf( "String X=%s, ", subString);
	rackTy[n].xCube = (double)atof( subString );

	subString = strtok(NULL, DATA_DELIMITER);
	// if (SLOT_DEBUG) printf( "B=%s\n", subString);
	rackTy[n].xBOH = (double)atof( subString );

	int basicType;

	if ( (basicType = getBasicTypeIndex(rackTy[n].profileID,rackTy[n].levelType)) != -1 ) {
		strcpy(rackTy[n].desc, basicTy[basicType].desc);
		numRackUsages++;
	}

	return;	// No errors


}	// End of buildUsageRec()

void Pass1Process::buildDriveRec(int n)
{
	char		*subString;

	// printf("entering build prod rec\n");

	// Build a Product Pack structure
	subString = strtok(NULL, DATA_DELIMITER);
	prodGpDrive[n].prodGpID = (int)atoi( subString );

	subString = strtok(NULL, DATA_DELIMITER);
	prodGpDrive[n].bayProfileID = (int)atoi( subString );

	subString = strtok(NULL, DATA_DELIMITER);
	prodGpDrive[n].isLimited = (int)atoi( subString );

	numDriveParameters++;

	return;

}	// End of buildDriveRec

//////////////////////////////////////////////////////////////////
// Move data from input into a Basic Rack Type structure
// This is information about how many locations are available 
// in the current Facility for each Basic Type.
// Users should be able to run Pass 1 without having defined
// locations in the Facility, so this information is not required.
//////////////////////////////////////////////////////////////////
void Pass1Process::buildRackRec(int n)
{
	char		*subString;

	// Build a Rack Type structure

	// Copy the stream input into a Table structure
	subString = strtok(NULL, DATA_DELIMITER);
	basicTy[n].profileID = (int)atoi( subString );

	subString = strtok(NULL, DATA_DELIMITER);
	basicTy[n].levelType = (int)atoi( subString );

	subString = strtok(NULL, DATA_DELIMITER);
	memcpy( &basicTy[n].desc, subString, (strlen( subString ) + 1) );

	subString = strtok(NULL, DATA_DELIMITER);
	basicTy[n].avlLinealFacings = (double)atof( subString );
	basicTy[n].startLinealFacings = basicTy[n].avlLinealFacings;

	subString = strtok(NULL, DATA_DELIMITER);
	basicTy[n].avlFixedFaces = (int)atoi( subString );

	subString = strtok(NULL, DATA_DELIMITER);
	basicTy[n].maxWidth = (double)atof( subString );
	
	subString = strtok(NULL, DATA_DELIMITER);
	basicTy[n].maxDepth = (double)atof( subString );
	
	subString = strtok(NULL, DATA_DELIMITER);
	basicTy[n].maxHeight = (double)atof( subString );
	
	subString = strtok(NULL, DATA_DELIMITER);
	basicTy[n].maxWeight = (double)atof( subString );

	subString = strtok(NULL, DATA_DELIMITER);
	basicTy[n].inFacility = (int)atoi(subString);

	subString = strtok(NULL, DATA_DELIMITER);
	basicTy[n].handlingMethod = (int)atoi(subString);

	subString = strtok(NULL, DATA_DELIMITER);
	basicTy[n].productGap = (double)atof(subString);

	subString = strtok(NULL, DATA_DELIMITER);
	basicTy[n].productSnap = (double)atof(subString);

	subString = strtok(NULL, DATA_DELIMITER);
	basicTy[n].facingGap = (double)atof(subString);

	subString = strtok(NULL, DATA_DELIMITER);
	basicTy[n].facingSnap = (double)atof(subString);

	basicTy[n].startFixedFaces = basicTy[n].avlFixedFaces;
	basicTy[n].countNeeded = 0;
	basicTy[n].totFacings = 0;

	return;	// No errors

}	// End of buildRackRec()

//////////////////////////////////////////////////////////////////
// Looks through the received array of basic types and returns
// 1 if a match is found for the ID passed in, 0 if not.
//////////////////////////////////////////////////////////////////
int Pass1Process::getBasicTypeIndex(int bayProfileID, int levelType)
{
	int i;

	for (i=0; i<numBasicTypes; i++) {
		//printf("Basic Type ProfileID : %d %d\n",basicTy[i].profileID, i);
		if (bayProfileID == basicTy[i].profileID && basicTy[i].levelType == levelType)
			return i;
	}

	return -1;	// Invalid

}	// End of getBasicTypeIndex()

// Calculate regression numbers for super groups and move Rack data into tables
void Pass1Process::addRackGroups(void)
{
	int i, n, high_y, low_y, high_r, low_r, high_l, low_l, gpIdx, tblIdx;
	double val;
	double a_coeff, b_coeff;
	double highval_y, lowval_y, highval_r, lowval_r, highval_l, lowval_l;
	double sum_x, sum_y, sum_xy, sumsq_x;
	double Upper_X, Lower_X, Upper_B, Lower_B;

	// Load the data arrays

	int foundLessThanThousands,foundLessThanHundreds,
		foundLessThanTenths, foundLessThanOnes;

	multiple=foundLessThanThousands=foundLessThanHundreds=
		foundLessThanTenths=foundLessThanOnes=0;
	for (n=0; n<numRackUsages; ++n) {
		if ( rackTy[n].xCube < .001f || rackTy[n].xBOH < .001f )
			foundLessThanThousands = 1;
		else if ( rackTy[n].xCube < .01f || rackTy[n].xBOH < .01f )
			foundLessThanHundreds = 1;
		else if ( rackTy[n].xCube < .1f || rackTy[n].xBOH < .1f )
			foundLessThanTenths = 1;
		else if ( rackTy[n].xCube < 1.0f || rackTy[n].xBOH < 1.0f )
			foundLessThanOnes = 1;
	}
	// this is just to be safe, and keep all points in the positive quadrant.
	if ( foundLessThanThousands == 1 ) {
		multiple = 10000;
		if ( debugLog ) {
			fprintf(p1TraceFile,"<DBGMSG> Found values < .001, Scaling graph by 10000\n");
		}
	}
	else if ( foundLessThanHundreds == 1 ) {
		multiple = 1000;
		if ( debugLog ) {
			fprintf(p1TraceFile,"<DBGMSG> Found values < .01, Scaling graph by 1000\n");
		}
	}
	else if ( foundLessThanTenths == 1 ) {
		multiple = 100;
		if ( debugLog ) {
			fprintf(p1TraceFile,"<DBGMSG> Found values < .1, Scaling graph by 100\n");
		}
	}
	else if ( foundLessThanOnes == 1 ) {
		multiple = 10;
		if ( debugLog ) {
			fprintf(p1TraceFile,"<DBGMSG> Found values < 1, Scaling graph by 10\n");
		}
	}
	else {
		multiple = 1;
		if ( debugLog ) {
			fprintf(p1TraceFile,"<DBGMSG> Not Scaling graph\n");
		}
	}

	// Move Rack Type data into table structures under Super Groups
	if ( debugLog ) {
		fprintf(p1TraceFile,"<DBGMSG> Moving Rules to proper graph table\n");
		fprintf(p1TraceFile,"<DBGMSG> Hazard=Tbl 0, Non-Hazard=Tbl 1\n");
	}
	for (n=0; n<numRackUsages; ++n) {
		// Check for bad Super Group.
		// This assumes that our 5 hard-coded super groups are stored in the DB and
		// passed over to us as 1 - 5.
		//if (rackTy[n].superGroup > MAX_NUM_RACK_GROUPS) {
		//	sprintf(errMsg, "Invalid Super Group ID [%d] on item #%d\n", rackTy[n].superGroup, n);
		//	throw EngineException(errMsg, __FILE__, __LINE__, 200);
		//}

		// Take the base 10 logs of the numbers

		rackTy[n].logxCube = log10(multiple * rackTy[n].xCube);
		rackTy[n].logxBOH = log10(multiple * rackTy[n].xBOH);
		rackTy[n].xCube *= multiple;
		rackTy[n].xBOH *= multiple;

		// Only two groups - hazard and non-hazard
		if ( rackTy[n].superGroup == 1 )
			gpIdx = HAZARD_TABLE;
		else
			gpIdx = DEFAULT_TABLE;

		//gpIdx = rackTy[n].superGroup - 1;
		tblIdx = rackGp[gpIdx].Table->ItemCount;
		memcpy( &rackGp[gpIdx].Table->TableData[tblIdx], &rackTy[n], sizeof(TABLE_ENTRY) );

		// Bump the appropriate super group tally
		rackGp[gpIdx].Table->ItemCount++;
	}

	numRackGroups = 0;
	for (n=0;n<MAX_NUM_RACK_GROUPS;n++) {
		if (rackGp[n].Table->ItemCount > 0)
			numRackGroups++;
	}

	/* compute and output the "pure" regression numbers for each Group */
	if ( debugLog ) {
		fprintf(p1TraceFile,"<DBGMSG> Computing Regression Numbers\n");
	}
	for (i=0; i<MAX_NUM_RACK_GROUPS; i++) {
		if ( rackGp[i].Table->ItemCount > 0 ) {
			sum_x = sum_y = sum_xy = sumsq_x = 0.0;
			for (n=0; n<rackGp[i].Table->ItemCount; ++n) {
				sum_x += rackGp[i].Table->TableData[n].xCube;
				sum_y += rackGp[i].Table->TableData[n].xBOH;
				sum_xy += (rackGp[i].Table->TableData[n].xCube * rackGp[i].Table->TableData[n].xBOH);
				sumsq_x += (rackGp[i].Table->TableData[n].xCube * rackGp[i].Table->TableData[n].xCube);
			}

			val = rackGp[i].Table->ItemCount * sumsq_x - sum_x * sum_x;
			a_coeff = (rackGp[i].Table->ItemCount * sum_xy - sum_x * sum_y) / val;
			b_coeff = (sumsq_x * sum_y - sum_x * sum_xy) / val;

			high_r = low_r = 0;
			highval_r = lowval_r = rackGp[i].Table->TableData[0].xCube;
			for (n=1; n<rackGp[i].Table->ItemCount; n++) {
				if (rackGp[i].Table->TableData[n].xCube > highval_r) {
					highval_r = rackGp[i].Table->TableData[n].xCube;
					high_r = n;
				}
				if (rackGp[i].Table->TableData[n].xCube < lowval_r) {
					lowval_r = rackGp[i].Table->TableData[n].xCube;
					low_r = n;
				}
			}
			Upper_X = highval_r;
			Lower_X = lowval_r;

			high_l = low_l = 0;
			highval_l = lowval_l = rackGp[i].Table->TableData[0].xBOH;
			for (n=1; n<rackGp[i].Table->ItemCount; n++) {
				if (rackGp[i].Table->TableData[n].xBOH > highval_l) {
					highval_l = rackGp[i].Table->TableData[n].xBOH;
					high_l = n;
				}
				if (rackGp[i].Table->TableData[n].xBOH < lowval_l) {
					lowval_l = rackGp[i].Table->TableData[n].xBOH;
					low_l = n;
				}
			}
			Upper_B = highval_l;
			Lower_B = lowval_l;

			high_y = low_y = 0;
			highval_y = lowval_y = 0.0;
			for (n=0; n<rackGp[i].Table->ItemCount; n++) {
				val = rackGp[i].Table->TableData[n].xCube * a_coeff + b_coeff;
				if (rackGp[i].Table->TableData[n].xBOH - val > highval_y) {
					highval_y = rackGp[i].Table->TableData[n].xBOH - val;
					high_y = n;
				}
				if (rackGp[i].Table->TableData[n].xBOH - val < lowval_y) {
					lowval_y = rackGp[i].Table->TableData[n].xBOH - val;
					low_y = n;
				}
			}

			
			if (debugLog) {
				fprintf(p1TraceFile,"<DBGMSG> Data for 'regular' elements (Table %d):\n", i);
				fprintf(p1TraceFile,"\tSUM xi   = %g\n", sum_x);
				fprintf(p1TraceFile,"\tSUM yi   = %g\n", sum_y);
				fprintf(p1TraceFile,"\tSUM xiyi = %g\n", sum_xy);
				fprintf(p1TraceFile,"\tSUM xixi = %g\n", sumsq_x);
				fprintf(p1TraceFile,"\tRegression (a, b) = (%g, %g)\n", a_coeff, b_coeff);
				fprintf(p1TraceFile,"\tXCube: MAX %g (idx %d), MIN %g (idx %d)\n",
								 				highval_r, high_r, lowval_r, low_r);
				fprintf(p1TraceFile,"\tBOH: MAX %g (idx %d), MIN %g (idx %d)\n",
								 				highval_l, high_l, lowval_l, low_l);
				fprintf(p1TraceFile,"\tY-deviation: MAX %g (idx %d), MIN %g (idx %d)\n",
				 								highval_y, high_l, lowval_y, low_l);
			}
			

			//////////////////////////////////////////////////////////////////////////
			// 计算对数回归参数 - Pass1算法的数学基础
			// 使用最小二乘法计算线性回归系数
			//////////////////////////////////////////////////////////////////////////

			// 初始化累加变量
			sum_x = sum_y = sum_xy = sumsq_x = 0.0;

			// 遍历货架类型数据，计算回归所需的累加值
			for (n=0; n<rackGp[i].Table->ItemCount; n++) {
				sum_x += rackGp[i].Table->TableData[n].logxCube;		// ΣX（扩展立方体对数值之和）
				sum_y += rackGp[i].Table->TableData[n].logxBOH;		// ΣY（库存量对数值之和）
				sum_xy += (rackGp[i].Table->TableData[n].logxCube * rackGp[i].Table->TableData[n].logxBOH);	// ΣXY
				sumsq_x += (rackGp[i].Table->TableData[n].logxCube * rackGp[i].Table->TableData[n].logxCube);	// ΣX²
			}

			// 使用最小二乘法公式计算回归系数
			// 分母：n*ΣX² - (ΣX)²
			val = rackGp[i].Table->ItemCount * sumsq_x - sum_x * sum_x;

			// 斜率系数 a = (n*ΣXY - ΣX*ΣY) / (n*ΣX² - (ΣX)²)
			a_coeff = (rackGp[i].Table->ItemCount * sum_xy - sum_x * sum_y) / val;

			// 截距系数 b = (ΣX²*ΣY - ΣX*ΣXY) / (n*ΣX² - (ΣX)²)
			b_coeff = (sumsq_x * sum_y - sum_x * sum_xy) / val;

			high_r = low_r = 0;
			highval_r = lowval_r = rackGp[i].Table->TableData[0].logxCube;
			for (n=1; n<rackGp[i].Table->ItemCount; n++) {
				if (rackGp[i].Table->TableData[n].logxCube > highval_r) {
					highval_r = rackGp[i].Table->TableData[n].logxCube;
					high_r = n;
				}
				if (rackGp[i].Table->TableData[n].logxCube < lowval_r) {
					lowval_r = rackGp[i].Table->TableData[n].logxCube;
					low_r = n;
				}
			}

			high_l = low_l = 0;
			highval_l = lowval_l = rackGp[i].Table->TableData[0].logxBOH;
			for (n=0; n<rackGp[i].Table->ItemCount; n++) {
				if (rackGp[i].Table->TableData[n].logxBOH > highval_l) {
					highval_l = rackGp[i].Table->TableData[n].logxBOH;
					high_l = n;
				}
				if (rackGp[i].Table->TableData[n].logxBOH < lowval_l) {
					lowval_l = rackGp[i].Table->TableData[n].logxBOH;
					low_l = n;
				}
			}

			//////////////////////////////////////////////////////////////////////////
			// 计算热区边界 - 确定回归线上下的偏差范围
			// 热区是Pass1算法中的关键概念，定义了产品参数的"最佳匹配区域"
			//////////////////////////////////////////////////////////////////////////
			high_y = low_y = 0;
			highval_y = lowval_y = 0.0;

			// 遍历所有货架类型，计算每个点到回归线的偏差
			for (n=0; n<rackGp[i].Table->ItemCount; n++) {
				// 根据回归线公式计算预测值：Y_predicted = a * X + b
				val = rackGp[i].Table->TableData[n].logxCube * a_coeff + b_coeff;

				// 计算实际值与预测值的偏差：deviation = Y_actual - Y_predicted
				// 正偏差表示实际值高于回归线
				if (rackGp[i].Table->TableData[n].logxBOH - val > highval_y) {
					highval_y = rackGp[i].Table->TableData[n].logxBOH - val;	// 记录最大正偏差
					high_y = n;												// 记录对应的货架类型索引
				}
				// 负偏差表示实际值低于回归线
				if (rackGp[i].Table->TableData[n].logxBOH - val < lowval_y) {
					lowval_y = rackGp[i].Table->TableData[n].logxBOH - val;	// 记录最大负偏差
					low_y = n;												// 记录对应的货架类型索引
				}
			}

			
			if (debugLog) {
				fprintf(p1TraceFile,"<DBGMSG> Data for 'log' elements:\n");
				fprintf(p1TraceFile,"\tSUM xi   = %g\n", sum_x);
				fprintf(p1TraceFile,"\tSUM yi   = %g\n", sum_y);
				fprintf(p1TraceFile,"\tSUM xiyi = %g\n", sum_xy);
				fprintf(p1TraceFile,"\tSUM xixi = %g\n", sumsq_x);
				fprintf(p1TraceFile,"\tRegression (a, b) = (%g, %g)\n", a_coeff, b_coeff);
				fprintf(p1TraceFile,"\tMAX XCube = %g (idx %d), MIN XCube = %g (idx %d)\n",
								 				highval_r, high_r, lowval_r, low_r);
				fprintf(p1TraceFile,"\tMAX BOH   = %g (idx %d), MIN BOH = %g (idx %d)\n",
								 				highval_l, high_l, lowval_l, low_l);
				fprintf(p1TraceFile,"\ty-deviation: MAX %g (idx %d), MIN %g (idx %d)\n",
				 								highval_y, high_l, lowval_y, low_l);
			}
			

			/* output the summary numbers */

			if (debugLog) {
				fprintf(p1TraceFile,"<DBGMSG> Summary Data: (Table %d)\n",i);
				fprintf(p1TraceFile,"\tDatapoints:  %d\n", rackGp[i].Table->ItemCount);
				fprintf(p1TraceFile,"\tRegression Coefficients:  (%g %g)\n", a_coeff, b_coeff);
				fprintf(p1TraceFile,"\tUpper/Lower Y:  %g %g (%g)\n",
										 highval_y, lowval_y, fabs(lowval_y));
				fprintf(p1TraceFile,"\tUpper/Lower X:  %g %g\n", Upper_X, Lower_X);
				fprintf(p1TraceFile,"\tUpper/Lower B:  %g %g\n", Upper_B, Lower_B);
			}

			//////////////////////////////////////////////////////////////////////////
			// 存储超级组回归参数 - 为后续产品匹配提供数学模型
			//////////////////////////////////////////////////////////////////////////
			rackGp[i].Line.a = a_coeff;				// 回归线斜率系数
			rackGp[i].Line.b = b_coeff;				// 回归线截距系数
			rackGp[i].X.hi = Upper_X;				// 扩展立方体上限
			rackGp[i].X.low = Lower_X;				// 扩展立方体下限
			rackGp[i].B.hi = Upper_B;				// 库存量上限
			rackGp[i].B.low = Lower_B;				// 库存量下限
			rackGp[i].Y.hi = highval_y;				// 热区上边界（最大正偏差）
			rackGp[i].Y.low = fabs(lowval_y);		// 热区下边界（最大负偏差的绝对值）
		}
	}

	// No errors
	return;
}

//////////////////////////////////////////////////////////////////////////
// Pass1算法接口函数 - 将产品数据传递给P1算法核心，返回货架类型索引
//
// 这是Pass1算法的主要入口点，负责：
// 1. 接收产品数据包
// 2. 调用核心算法进行货架类型匹配
// 3. 返回理想货架类型和可用货架类型排名
//////////////////////////////////////////////////////////////////////////
void Pass1Process::getCost(prodPack *aProd, struct idealResult *rackIdeal,
		struct availResult *rackAvail, int numRanks, char * passMsg)
{

	//调试信息：显示提交的产品详细信息
	//if (SLOT_DEBUG) {
	//	printf("提交产品 %s |扩展立方体|%f",aProd->desc, aProd->xCube);
	//	printf("|库存量|%f", aProd->xBOH);
	//	printf("|立方体因子|%f", aProd->cube);
	//	printf("|移动量|%f", aProd->movement);
	//	printf("|危险品|%d", aProd->hazard);
	//	printf("|优化方式|%d|\n", aProd->optBy);
	//}

	// 记录调试日志：显示提交给算法的产品参数
	if (debugLog) {
		fprintf(p1TraceFile,"<DBGMSG> 提交产品 %20.20s [扩展立方体=%10.4f] [扩展库存量=%10.4f]\n",
			aProd->desc, aProd->xCube, aProd->xBOH);
	}

	// 调用Pass1算法核心执行函数
	p1_execute(aProd, rackIdeal, rackAvail, numRanks, passMsg);

}

void Pass1Process::ReadIniParameters(void) {
	
	char buf[BUFSIZE];
	char * ptr;

	p1ini_file = fopen("P1Parameters.ini","r");
	if (p1ini_file == NULL) {
		if (debugLog)
			fprintf(p1TraceFile,"<DBGMSG> Defaulting Number of rankings to %5d\n",numRanks);
		return;
	}
	try {
		while(fgets(buf,BUFSIZE,p1ini_file) != NULL) {

			if (strstr(buf,"NUM_RANKS=") != NULL) {
				ptr = strstr(buf,"=");
				//numRanks = atoi(ptr+1);
				//if (debugLog)
				//	fprintf(p1TraceFile,"<DBGMSG> Number of rankings = %5d\n",numRanks);

			}
			else if (strstr(buf, "DIST_FACTOR=") != NULL) {
				useActualDist = 1;
				ptr = strstr(buf,"=");
				distFactor = atof(ptr+1);
				if (debugLog)
					fprintf(p1TraceFile, "<DBGMSG> Using actual distance.  Factor = %f\n", distFactor);
			}
			else if (strstr(buf, "SKIP_USED_PROFILES") != NULL) {
				ptr = strstr(buf, "=");
				skipUsedProfiles = atoi(ptr+1);
			}
				
		}
	}
	catch(...) {
	}
	fclose(p1ini_file);
	return;
}

/////////////////////////////////////////////////////////////////
/////////////////////////////////////////////////////////////////
/////////////////////////////////////////////////////////////////
/////////////////////////////////////////////////////////////////

int Pass1Process::p1_init() {

	int i,j;

	// Make sure all XCube and BOH entries in tables are > 0.0
	if (debugLog)
		fprintf(p1TraceFile,"<DBGMSG> Cleaning Data and Sorting [XCube then XBOH]\n");
	for (i=0;i<MAX_NUM_RACK_GROUPS;i++) {
		for (j=0;j<rackGp[i].Table->ItemCount;j++) {
			if (rackGp[i].Table->TableData[j].xCube <= 0) {
					//if (SLOT_DEBUG) printf("p1_init:  Bad Rack data passed (XCube <= 0).\n");
					//if (SLOT_DEBUG) printf("p1_init:  Group = %d, Table = %d.\n", i, j);
					//if (SLOT_DEBUG) printf("p1_init:  Resetting XCube to .01.\n");
					rackGp[i].Table->TableData[j].xCube = .01;
			}

			if (rackGp[i].Table->TableData[j].xBOH <= 0) {
					//if (SLOT_DEBUG) printf("p1_init:  Bad Rack data passed (XBOH <= 0).\n");
					//if (SLOT_DEBUG) printf("p1_init:  Group = %d, Table = %d.\n", i, j);
					//if (SLOT_DEBUG) printf("p1_init:  Resetting XBOH to .01.\n");
					rackGp[i].Table->TableData[j].xBOH = .01;
			}
		}
	}

	for ( i = 0; i<MAX_NUM_RACK_GROUPS;i++) {
		if ( debugLog )
			fprintf(p1TraceFile,"<DBGMSG> Before qsort()\n");
		qsort(rackGp[i].Table->TableData, rackGp[i].Table->ItemCount,sizeof(TABLE_ENTRY),tableCompare);
		if ( debugLog )
			fprintf(p1TraceFile,"<DBGMSG> After qsort()\n");
		if ( debugLog ) {
			fprintf(p1TraceFile,"<DBGMSG> Table %d Usage Records:\n",i);
			for ( j = 0; j < rackGp[i].Table->ItemCount; j++ ) {
				fprintf(p1TraceFile,"<DBGMSG> [DBID=%5d] [LevType=%d] [FaceCount=%d] [XCube=%10.4f] [XBOH=%10.4f] [LogXCube=%10.4f] [LogXBOH=%10.4f]\n",
					rackGp[i].Table->TableData[j].profileID,rackGp[i].Table->TableData[j].levelType,rackGp[i].Table->TableData[j].facingCount,
					rackGp[i].Table->TableData[j].xCube,rackGp[i].Table->TableData[j].xBOH,rackGp[i].Table->TableData[j].logxCube,
					rackGp[i].Table->TableData[j].logxBOH);
			}
		}
	}


	return (0);
}


 
void Pass1Process::assignAvail(prodPack *aProd, int currentRanking, 
							   table_entry profile, 
							   struct availResult *rackAvail,
							   int bNoDuplicates,
							   int profileIdx) 
{
	
	int btIdx;
	double prodWidth, prodHeight;
	
	btIdx=getBasicTypeIndex(profile.profileID, profile.levelType);
	
	if (basicTy[btIdx].handlingMethod != 3 || basicTy[btIdx].rejected == CASE_FIT_ONLY) {
		prodWidth = aProd->width;
		prodHeight = aProd->height;
	}
	else {
		prodWidth = aProd->containerWidth;
		if (aProd->contOverrideHeight)
			prodHeight = aProd->containerHeight;
		else
			prodHeight = aProd->containerHeight + (aProd->height*aProd->hi);
	}
	
	rackAvail[currentRanking].profileIdx = profileIdx;
	rackAvail[currentRanking].facingCount = profile.facingCount;
	rackAvail[currentRanking].availProfileID = profile.profileID;
	rackAvail[currentRanking].availLevelType = profile.levelType;
	rackAvail[currentRanking].ranking = currentRanking;
	rackAvail[currentRanking].xBOH = profile.xBOH;
	rackAvail[currentRanking].xCube = profile.xCube;
	rackAvail[currentRanking].diffxCube = profile.diffxCube;
	rackAvail[currentRanking].diffxBOH = profile.diffxBOH;
	rackAvail[currentRanking].heightDiff = fabs(basicTy[btIdx].maxHeight - prodHeight);
	rackAvail[currentRanking].origHandling = basicTy[btIdx].handlingMethod;
	if (basicTy[btIdx].rejected == CASE_FIT_ONLY)
		rackAvail[currentRanking].actualHandling = 1;
	else
		rackAvail[currentRanking].actualHandling = rackAvail[currentRanking].origHandling;

	rackAvail[currentRanking].fits = basicTy[btIdx].rejected;

	if (bNoDuplicates)
		rackAvail[currentRanking].isDuplicate = 0;
	else
		rackAvail[currentRanking].isDuplicate = 1;
	
	
	if (debugLog || userLog || aProd->trace)
		fprintf(p1TraceFile,"\tAssigning ranking %d: Profile: %s(%d)  Type: %s   Facings: %d  Handling: %d-%d\n",
		currentRanking+1, profile.desc, profile.profileID,
		ConvertBayType(profile.levelType).c_str(), profile.facingCount, 
		rackAvail[currentRanking].origHandling, rackAvail[currentRanking].actualHandling);
	
	if (debugLog)
		fprintf(p1TraceFile,"<DBGMSG>    Calculating space used\n");



	// Adjust count of Facings
	// all fixed facings; set lineal to 0
	if (basicTy[btIdx].startLinealFacings == 0 ) { 
		if (debugLog)
			fprintf(p1TraceFile,"<DBGMSG>       [All Fixed] Lineal Space = 0\n");
		rackAvail[currentRanking].linealFacingCount = 0;
		rackAvail[currentRanking].linealFacingWidth = 0;
	}
	
	// all variable facings; set lineal to facingcount; lineal width to facingcount * prodwidth
	else if (basicTy[btIdx].startFixedFaces == 0) {
		if (debugLog)
			fprintf(p1TraceFile,"<DBGMSG>       [All VW] Lineal Space = %5d Facing(s) = %5d width = %10.4f\n",
				(int)(profile.facingCount * prodWidth),profile.facingCount,prodWidth);
		rackAvail[currentRanking].linealFacingWidth =  (int)(profile.facingCount * aProd->width);
		rackAvail[currentRanking].linealFacingCount =  profile.facingCount;
	}
	
	// have a mix of variable and fixed facings : split facings down the middle
	else {
		if ( ( profile.facingCount % 2 ) == 0) {
			rackAvail[currentRanking].linealFacingCount = (int) (profile.facingCount)/2;
			rackAvail[currentRanking].linealFacingWidth = (int) ((profile.facingCount)/2 * prodWidth);
		}
		else {
			if ( profile.facingCount != 1 ) {
				rackAvail[currentRanking].linealFacingCount= (int) (profile.facingCount)/2;
				rackAvail[currentRanking].linealFacingWidth = (int) ((profile.facingCount) * prodWidth / 2);
			}
			else {
				rackAvail[currentRanking].linealFacingWidth = (int) ((profile.facingCount) * prodWidth / 2);
				rackAvail[currentRanking].linealFacingCount = 1;
			}
		}
		if (debugLog)
			fprintf(p1TraceFile,"<DBGMSG>       [Mixed] Lineal Space (approx) = %5d Facing(s) = %5d width = %10.4f\n",(int)(profile.facingCount * prodWidth)/2,profile.facingCount/2,prodWidth);
	}
}

 


int Pass1Process::isInFacility(TABLE_ENTRY profile)
{
	int btIdx;			// index of Basic Type

	// Look up available count from Basic Types
	btIdx=getBasicTypeIndex(profile.profileID,profile.levelType);

	// If no Basic Type data was passed, skip.
	if (btIdx==RACK_NOT_FOUND) return 0;
	else {
		if ( basicTy[btIdx].inFacility == 1 )
			return 1;
		else
			return 0;
	}
}	// End of isAvailable()


int Pass1Process::useSpace(P1DATA *superGroup, int numRankings,
						   struct availResult *rackAvail, prodPack *aProd, 
						   char * passMsg)
{
	int btIdx;			// index of Basic Type
	int foundOne = 0;
	int i;
	double prodWidth;
	int profileIdx, rackIdx;

	if (rackAvail[0].profileIdx == RACK_NOT_FOUND)
		return 0;

	TABLE_ENTRY *profiles = superGroup->Table->TableData;

	for ( i = 0; i < numRankings; i++ ) {

		rackIdx = i;
		profileIdx = rackAvail[i].profileIdx;

		// Look up available count from Basic Types
		
		btIdx=getBasicTypeIndex(profiles[profileIdx].profileID,profiles[profileIdx].levelType);
		
		// If no Basic Type data was passed, skip.
		if (btIdx==RACK_NOT_FOUND) return 1;	// Error
		
		if (debugLog)
			fprintf(p1TraceFile,"<DBGMSG> Using Rank %d: %d - %s.  Looking for Space Available To Reduce\n", 
			(i+1), basicTy[btIdx].profileID, basicTy[btIdx].desc);

		
		if ( basicTy[btIdx].avlFixedFaces > 0 || basicTy[btIdx].avlLinealFacings > 0 ) {

			if (debugLog) {
				fprintf(p1TraceFile,"<DBGMSG>    Found ranking with space: %d - %s (Type: %d)\n",
					rackAvail[i].ranking, basicTy[btIdx].desc, basicTy[btIdx].levelType);
			}

			foundOne = 1;
			break;
		}

	}

	// If none are found, use the first ranking
	if (! foundOne) {
		profileIdx = rackAvail[0].profileIdx;
		rackIdx = 0;
		btIdx=getBasicTypeIndex(profiles[profileIdx].profileID,profiles[profileIdx].levelType);

		if (debugLog) {
			fprintf(p1TraceFile, "<DBGMSG> No available space found. Using ranking 1 - %s (Type: %d)\n",
			basicTy[btIdx].desc, basicTy[btIdx].levelType);
		}
	}
	
	if (rackAvail[rackIdx].actualHandling != 3)
		prodWidth = aProd->width;			// case handling
	else
		prodWidth = aProd->containerWidth;	// pallet handling
	
	
	sprintf(passMsg,"Profile %30.30s reduced by %d facings.",
		basicTy[btIdx].desc, profiles[profileIdx].facingCount);
				
	if (debugLog) {
		fprintf(p1TraceFile, "<DBGMSG> Avl Fxd: %d, Avl Lin Units: %10.4f, Facings Needed: %d\n",
			basicTy[btIdx].avlFixedFaces, basicTy[btIdx].avlLinealFacings, profiles[profileIdx].facingCount);
	}

	int facings = profiles[profileIdx].facingCount;
	int toggle = 0;
	rackAvail[rackIdx].linealFacingCount = 0;
	rackAvail[rackIdx].linealFacingWidth = 0;
	int totalFacingCount = rackAvail[rackIdx].facingCount;

	while (facings > 0) {
		
		// if the profile only has fixed facings, reduce only fixed
		if (basicTy[btIdx].startLinealFacings <= 0) {
			basicTy[btIdx].avlFixedFaces--;
			facings--;
			
			if (basicTy[btIdx].avlFixedFaces < 0)
				basicTy[btIdx].countNeeded++;
			else
				totalFacingCount++;
			

			continue;
		}
		
		// if the profile only has lineal facings, reduce only lineal
		// need to account for gaps and snaps
		if (basicTy[btIdx].startFixedFaces <= 0) {
			basicTy[btIdx].avlLinealFacings -= prodWidth;
			// add a facing gap between each facing
			basicTy[btIdx].avlLinealFacings -= basicTy[btIdx].facingGap;
			facings--;
			if (facings == 0)	// the last time through add two product gaps
				basicTy[btIdx].avlLinealFacings -= 2*basicTy[btIdx].productGap;

			
			// Adjust lineal facings on assignment to match availability
			rackAvail[rackIdx].linealFacingWidth += (int)prodWidth;
			rackAvail[rackIdx].linealFacingCount++;
			
			if (basicTy[btIdx].avlLinealFacings < 0)
				basicTy[btIdx].countNeeded++;
			continue;
		}
		
		// profile has both; alternate between reducing fixed and lineal
		// until one is zero; then reduce the other until it is zero;
		// then go back to alternating
		if (toggle == 0) {
			// reduce fixed
			toggle = 1;		// reset toggle
			// if we have more fixed reduce them
			// even if we don't have more fixed, if we don't have more lineal either then we
			// might as well reduce fixed some more
			if (basicTy[btIdx].avlFixedFaces > 0 || basicTy[btIdx].avlLinealFacings <= 0) {
				basicTy[btIdx].avlFixedFaces--;
				facings--;
				
				if (basicTy[btIdx].avlFixedFaces < 0)
					basicTy[btIdx].countNeeded++;
			}
		}
		else {
			// reduce lineal
			toggle = 0;		// reset toggle
			// if we have more lineal reduce them
			// even if we don't have more limneal, if we don't have more fixed either then
			// we might as well reduce lineal some more
			// need to account for gaps and snaps
			if (basicTy[btIdx].avlLinealFacings > 0 || basicTy[btIdx].avlFixedFaces <= 0) {
				basicTy[btIdx].avlLinealFacings -= prodWidth;
				
				// add a facing gap between each facing
				basicTy[btIdx].avlLinealFacings -= basicTy[btIdx].facingGap;

				facings--;

				if (facings == 0)	// the last time through add two product gaps
					basicTy[btIdx].avlLinealFacings -= 2*basicTy[btIdx].productGap;
				
				// Adjust lineal facings on assignment to match availability
				rackAvail[rackIdx].linealFacingWidth += (int)prodWidth;
				rackAvail[rackIdx].linealFacingWidth += (int)basicTy[btIdx].facingGap;

				if (facings == 0)
					rackAvail[rackIdx].linealFacingWidth += (int)(2*basicTy[btIdx].productGap);

				rackAvail[rackIdx].linealFacingCount++;
			
				if (basicTy[btIdx].avlLinealFacings < 0)
					basicTy[btIdx].countNeeded++;
			}
		}
		
	}
	
	basicTy[btIdx].totFacings += profiles[profileIdx].facingCount;


	return 0;

}	// End of useSpace()


int tableCompare(const void * p1, const void * p2) {

	if ( ((TABLE_ENTRY*)p1)->logxCube < ((TABLE_ENTRY*)p2)->logxCube )
		return -1;
	else if ( ((TABLE_ENTRY*)p1)->logxCube > ((TABLE_ENTRY*)p2)->logxCube )
		return 1;
	else {
		if ( ((TABLE_ENTRY*)p1)->logxBOH < ((TABLE_ENTRY*)p2)->logxBOH )
			return -1;
		else if ( ((TABLE_ENTRY*)p1)->logxBOH > ((TABLE_ENTRY*)p2)->logxBOH )
			return 1;
		else {
			if ( ((TABLE_ENTRY*)p1)->profileID < ((TABLE_ENTRY*)p2)->profileID )
				return -1;
			else if ( ((TABLE_ENTRY*)p1)->profileID > ((TABLE_ENTRY*)p2)->profileID )
				return 1;
			else {
				if ( ((TABLE_ENTRY*)p1)->levelType < ((TABLE_ENTRY*)p2)->levelType )
					return -1;
				else if ( ((TABLE_ENTRY*)p1)->levelType > ((TABLE_ENTRY*)p2)->levelType )
					return 1;
				else
					return 0;
			}
		}
	}
	return 0;
}

int tableDiffCompare(const void * p1, const void * p2) {
	
	if (useActualDist) {
		if( ((TABLE_ENTRY*)p1)->actualDiff < ((TABLE_ENTRY*)p2)->actualDiff)
			return -1;
		else if( ((TABLE_ENTRY*)p1)->actualDiff > ((TABLE_ENTRY*)p2)->actualDiff)
			return 1;
	}
	else {
		if ( ((TABLE_ENTRY*)p1)->diffxCube < ((TABLE_ENTRY*)p2)->diffxCube )
			return -1;
		else if ( ((TABLE_ENTRY*)p1)->diffxCube > ((TABLE_ENTRY*)p2)->diffxCube )
			return 1;
		
		if ( ((TABLE_ENTRY*)p1)->diffxBOH < ((TABLE_ENTRY*)p2)->diffxBOH )
			return -1;
		else if ( ((TABLE_ENTRY*)p1)->diffxBOH > ((TABLE_ENTRY*)p2)->diffxBOH )
			return 1;
	}
	
	if ( ((TABLE_ENTRY*)p1)->profileID < ((TABLE_ENTRY*)p2)->profileID )
		return -1;
	else if ( ((TABLE_ENTRY*)p1)->profileID > ((TABLE_ENTRY*)p2)->profileID )
		return 1;
	
	if ( ((TABLE_ENTRY*)p1)->levelType < ((TABLE_ENTRY*)p2)->levelType )
		return -1;
	else if ( ((TABLE_ENTRY*)p1)->levelType > ((TABLE_ENTRY*)p2)->levelType )
		return 1;
				
	return 0;
}


int Pass1Process::IsBayInPGDriveLimited(int bayProfileID, int prodGpID) {
	int i;
	int foundPg = 0;

	for ( i = 0; i < numDriveParameters; i++ ) {
		if ( prodGpID == prodGpDrive[i].prodGpID && bayProfileID == prodGpDrive[i].bayProfileID )
			return BAY_PROFILE_IN_PG;
		else if ( ( prodGpID == prodGpDrive[i].prodGpID && bayProfileID ) )
			foundPg = 1;
	}
	if ( foundPg == 0 )
		return NO_BAYPROFILE_IN_PG;
	else
		return BAY_PROFILE_NOT_IN_PG;
}

int Pass1Process::IsBayInPGDriveNotLimited(int bayProfileID, int prodGpID) {
	int i;
	int foundPg = 0;
	int	limitedPg = 1;

	for ( i = 0; i < numDriveParameters; i++ ) {
		if ( prodGpID == prodGpDrive[i].prodGpID && bayProfileID == prodGpDrive[i].bayProfileID )
			return BAY_PROFILE_IN_PG;
		else if ( ( prodGpID == prodGpDrive[i].prodGpID && bayProfileID ) ) {
			foundPg = 1;
			if ( prodGpDrive[i].isLimited == 0 )
				limitedPg = 0;
		}
	}
	if ( foundPg == 0 )
		return NO_BAYPROFILE_IN_PG;
	else if ( limitedPg == 0 )
		return BAY_PROFILE_IN_PG;
	else
		return BAY_PROFILE_NOT_IN_PG;
}

int Pass1Process::IsPGDriveLimited(int prodGpID) {
	int i;
	int foundPg = 0;
	int pgLimited = 1;

	for ( i = 0; i < numDriveParameters; i++ ) {
		if ( prodGpID == prodGpDrive[i].prodGpID ) {
			foundPg = 1;
			if ( prodGpDrive[i].isLimited == 0 )
				pgLimited = 0;
		}
	}
	if ( ! foundPg ) 
		return PG_NOT_FOUND;
	else if ( pgLimited == 0 )
		return PG_NOT_LIMITED;
	else
		return PG_LIMITED;
}

int Pass1Process::GetUserFlags(void) 
{
	int			errCode = 0;
	int			MemAmount = 0;
	char		line[MAX_INPUT_DATA_LEN];
	char		*subString;
	std::string strLine;

	// Get data from the socket
	while( 1 )
	{
		// Read a line of data.
		memset(line, 0, MAX_INPUT_DATA_LEN);
/*
		if( gfnGetDataStream()->ssData.getline( line, MAX_INPUT_DATA_LEN ) == 0 )
			throw EngineException("Data file closed.\n", __FILE__, __LINE__, 200);
*/
		if ( gfnGetDataStream()->ssData.getline( line, MAX_INPUT_DATA_LEN ) == 0 )
			throw EngineException("Data file closed.\n", __FILE__, __LINE__, 200);

		strLine = line;
		// Check beginning code for line type
		subString = strtok(line, DATA_DELIMITER);
		switch ( *subString ) {
			case 'L':
				{
					std::vector<std::string> parmList;
					
					// Format: ClientName|BreakPallet|RankHeight|LogMode|Rankings|SkipUsed
					utilityHelper.ParseString(strLine, "|", parmList);
					// Look up window registry to find the default first
					HKEY hRegKey;
					P1LogUsr[0] = 0;
					DWORD dwType = REG_SZ;
					DWORD dwReturnLength;

					if (RegOpenKeyEx(HKEY_LOCAL_MACHINE, "Software\\SSA Global\\Optimize", 0, KEY_READ, &hRegKey) == ERROR_SUCCESS) {
					int i = 0;
					while ((RegQueryValueEx(hRegKey, "LogFilePath", NULL, &dwType,(LPBYTE)P1LogUsr, &dwReturnLength) != ERROR_SUCCESS ) && i<=100)
							i++;
					if (i>100)
						P1LogUsr[0] = 0;
					}
					RegCloseKey(hRegKey);
	
		
					if (P1LogUsr[0] == 0)
						strcpy(P1LogUsr, ".\\Log");					// MFS 2Mar06 Value missing, default to \Optimize\Log

					if (_mkdir(P1LogUsr) != 0) {					// MFS 2Mar06 Try to create/open directory
						if (errno != EEXIST) {						// If that doesn't work...
							if (_stricmp(P1LogUsr, ".\\Log")==0)	// Try default unless we already tried it.
								strcpy(P1LogUsr, ".");				// Use current dir. as final fallback.
							else
								strcpy(P1LogUsr, ".\\Log");
						}
					}

					strcat(P1LogUsr, "\\");							// MFS 2Mar06 Now that we have a directory, add the filename
					strcat(P1LogUsr, parmList[1].c_str());
					strcat(P1LogUsr,"P1Trace.log");

					BreakPalletFlag = atoi(parmList[2].c_str());
					SortRankingFlag = atoi(parmList[3].c_str());
					
					LogMode = parmList[4];
					if (LogMode == "None") {
						userLog = 0;
						debugLog = 0;
					}
					else if (LogMode == "User") {
						userLog = 1;
						debugLog = 0;
					}
					else if (LogMode == "Expert") {
						userLog = 1;
						debugLog = 1;
					}
					else {
						userLog = 0;
						debugLog = 0;
					}

					numRanks = atoi(parmList[5].c_str());
					skipUsedProfiles = atoi(parmList[6].c_str());
					// facDivisor = atoi(parmList[7].c_str());

					break;
				}

			case '<':	// Protocol Tag
				if		( strstr( subString, START_OF_STREAM ) != NULL )
					break;	// Loop again for more data
				else if ( strstr( subString, START_OF_SUBLIST ) != NULL ) {
					break;	// Loop again for more data
				}
				else if ( strstr( subString, END_OF_SUBLIST ) != NULL ) {
					//if (SLOT_DEBUG) printf( "\nEnd of sublist of %d rows.\n", numProds );
					return 0;	// Loop again for more data
				}
				else if( strstr( subString, END_OF_STREAM ) != NULL ) {
					//if (SLOT_DEBUG) printf( "End of Data.\n\n" );
					return 1;	// EOS
				}
				else {
					sprintf(errMsg, "\nInvalid protocol tag '%s' read!\n", subString);
					throw EngineException(errMsg, __FILE__, __LINE__, 200);
				}

//			default:								// Unknown Type
				//if (SLOT_DEBUG) printf( "Unrecognized input data line read:\n%s\n", line);

		}		// End of switch stmt

	}			// End of while loop = EOF

}

int Pass1Process::willProductFit(prodPack *aProd, TABLE_ENTRY tableItem, int tryCaseHandling)
{

	int btIdx;
	BASIC_RACK_TYPE profile;

	btIdx=getBasicTypeIndex(tableItem.profileID, tableItem.levelType);
	profile = basicTy[btIdx];

	if (profile.handlingMethod != 3 || tryCaseHandling) {
		if (aProd->width > profile.maxWidth)
			return 0;

		if (aProd->length > profile.maxDepth)
			return 0;

		if (aProd->height > profile.maxHeight)
			return 0;
	}
	else {
		if (aProd->containerWidth > profile.maxWidth)
			return 0;
		
		if (aProd->containerLength > profile.maxDepth)
			return 0;
		
		if (aProd->contOverrideHeight == 1) {
			if (aProd->containerHeight > profile.maxHeight)
				return 0;
		}
		else {
			if ((aProd->containerHeight + aProd->height*aProd->hi) > profile.maxHeight)
				return 0;
		}
	}


	return 1;

}



void Pass1Process::p1Trace(const char *format, ...)
{

	/*
	va_list arglist;
	fprintf(p1TraceFile, format, arglist);

	if (SHOW_TRACE)
		fprintf(stdout, format, arglist);

	*/

	return;

}


int Pass1Process::checkProfile(int bFacility, 
							   int bLimited, 
							   int bNotLimited, 
							   int bNativeFit, 
							   int bCaseFit, 
							   int bNoDuplicates, 
							   prodPack *aProd, 
							   int *rankings, 
							   int currentRanking,
							   TABLE_ENTRY *profiles,
							   int idx,
							   int allowUsedUpProfiles)
{

	TABLE_ENTRY profile = profiles[idx];

	// never allow exact duplicate
	for (int rankIdx = 0; rankIdx < currentRanking; ++rankIdx) {
		if (rankings[rankIdx] == idx)
			return -1;
	}

	if (bFacility) {
		if (! isInFacility(profile))
			return -2;
	}
	
	if (! allowUsedUpProfiles) {
		int facings = profile.facingCount;

		int btIdx = getBasicTypeIndex(profile.profileID, profile.levelType);
		if (basicTy[btIdx].avlFixedFaces + basicTy[btIdx].avlLinealFacings < facings)
			return -8;
	}

	if (bNoDuplicates) {			// don't allow duplicate profile/level type
		for (int rankIdx = 0; rankIdx < currentRanking; ++rankIdx) {
			if (profiles[rankings[rankIdx]].profileID == profile.profileID
				&& profiles[rankings[rankIdx]].levelType == profile.levelType)
				return -3;
		}
	}

	if (bLimited) {
		if (IsBayInPGDriveLimited(profile.profileID,aProd->prodGpID) == BAY_PROFILE_NOT_IN_PG )
			return -4;
	}

	if (bNotLimited) {
		if (IsBayInPGDriveNotLimited(profile.profileID, aProd->prodGpID) == BAY_PROFILE_NOT_IN_PG )
			return -5;
	}

	if (bNativeFit) {
		int btIdx = getBasicTypeIndex(profile.profileID, profile.levelType);
		if (basicTy[btIdx].rejected == NO_FIT)
			return -6;			// this one has already been rejected

		if (! willProductFit(aProd, profile, 0)) {

			basicTy[btIdx].rejected = NO_FIT;
			if (debugLog)
				fprintf(p1TraceFile,"<DBGMSG> Failed native fit: Prod: %d-%s, Profile: %d-%s\n",
				aProd->dbID, aProd->desc, basicTy[btIdx].profileID, basicTy[btIdx].desc);
			return -6;
		}
	}

	if (bCaseFit) {
		int btIdx = getBasicTypeIndex(profile.profileID, profile.levelType);
		
		if (basicTy[btIdx].rejected == NATIVE_FIT)	
			return -7;			// fits natively so ignore

		if (basicTy[btIdx].handlingMethod != 3)
			return -7;		// we should already have checked case handling as native
		
		if (! willProductFit(aProd, profile, 1))
			return -7;
		else {
			basicTy[btIdx].rejected = CASE_FIT_ONLY;
			if (debugLog)
				fprintf(p1TraceFile,"<DBGMSG> Product fits using case handling: Prod: %d-%s, Profile: %d-%s\n",
					aProd->dbID, aProd->desc, basicTy[btIdx].profileID, basicTy[btIdx].desc);
		}

	}


	// if it falls through all the checks, it must be okay to use as a ranking

	return 1;

}


/**
 * Pass1算法核心执行函数 - 为单个产品选择最优货架类型
 *
 * 算法步骤概述：
 * 1. 确定产品所属的超级组（危险品组或普通组）
 * 2. 调整产品数值到回归线"热区"范围内
 * 3. 计算与所有货架类型的距离差异
 * 4. 选择理想货架类型（忽略设施容量限制）
 * 5. 选择可用货架类型（考虑设施容量限制）
 * 6. 分配空间并更新设施容量
 *
 * @param aProd 产品数据包，包含扩展立方体、库存量等信息
 * @param rackIdeal 返回理想货架类型结果
 * @param rackAvail 返回可用货架类型排名列表
 * @param numAvailRanks 需要返回的可用货架排名数量
 * @param passMsg 处理过程中的消息
 */
void Pass1Process::p1_execute(prodPack *aProd,
	struct idealResult *rackIdeal,	// return values
	struct availResult *rackAvail,
	int numAvailRanks,
	char *passMsg)
{

	P1DATA *tptr;				// 指向要使用的超级组数据
	int idx, btIdx;				// 临时表索引
	int n;					// 循环计数器
	int err=0;					// useSpace()函数的返回值
	int rankNum;				// 当前排名编号
	int *rankings;				// 用于保存之前最佳可用货架的索引数组
	table_entry *profiles;		// 货架类型配置表数据

	if (debugLog)
		fprintf(p1TraceFile,"<DBGMSG> 为索引数组分配临时内存空间\n");

	// 为排名索引数组分配内存
	rankings = (int *) malloc(sizeof(int) * numAvailRanks);

	if (rankings == NULL)
		throw EngineException("临时数组内存分配不足。\n",
			__FILE__, __LINE__, 200);

	// 重置所有排名数组为未找到状态
	for (rankNum=0; rankNum < numAvailRanks; rankNum++)
		rankings[rankNum] = RACK_NOT_FOUND;

	// 初始化返回值和工作变量
	// rackIndex->ideal = rackIndex->bestAvl = isUsable = lastUsable = RACK_NOT_FOUND;
	rackIdeal->facingCount = RACK_NOT_FOUND;		// 理想货架面数初始化
	for ( n = 0; n < numAvailRanks; n++)
		rackAvail[n].facingCount = RACK_NOT_FOUND;	// 可用货架面数初始化

	//////////////////////////////////////////////////////////////////////////
	// 步骤1: 确定超级组 - 根据产品危险品属性选择对应的货架组
	//////////////////////////////////////////////////////////////////////////
	// 检查危险品标志位
	if (aProd->hazard)
		tptr = &rackGp[HAZARD_TABLE];		// 危险品使用危险品货架组
	else
		tptr = &rackGp[DEFAULT_TABLE];		// 普通产品使用默认货架组

	profiles = tptr->Table->TableData;		// 简化代码，直接引用货架配置表数据

	//////////////////////////////////////////////////////////////////////////
	// 步骤2: 标准处理路径 - 核心算法开始
	//////////////////////////////////////////////////////////////////////////

	// 调整产品数值，将产品参数"移动到回归线热区"内
	// 这是Pass1算法的关键步骤，确保产品参数在合理范围内进行匹配
	adjustProductValues(aProd, tptr);

	// 获取调整后的产品关键参数
	double BOH = aProd->BOH;			// 调整后的库存量（Balance On Hand）
	double XCube = aProd->xCube;		// 调整后的扩展立方体

	//////////////////////////////////////////////////////////////////////////
	// 步骤3: 计算产品与所有货架类型的距离差异
	// 这是Pass1算法的核心计算 - 通过多维距离找到最匹配的货架类型
	//////////////////////////////////////////////////////////////////////////
	for (idx=0; idx < tptr->Table->ItemCount; ++idx) {
		// 计算扩展立方体的绝对差值
		profiles[idx].diffxCube = fabs(XCube - profiles[idx].logxCube);
		// 计算库存量的绝对差值
		profiles[idx].diffxBOH = fabs(BOH - profiles[idx].logxBOH);
		// 计算实际欧几里得距离（带权重因子distFactor）
		// 公式: sqrt(distFactor * (XCube差值)² + (BOH差值)²)
		// 这个距离值越小，表示货架类型与产品越匹配
		profiles[idx].actualDiff = fabs(sqrt( distFactor*pow(XCube - profiles[idx].logxCube, 2) +
			pow(BOH - profiles[idx].logxBOH, 2)));
	}
	

	if (debugLog || userLog || aProd->trace) {
		fprintf(p1TraceFile, "\n\tAdjusted Product Values:  Extended Cube: %.04f, Extended BOH: %.04f\n\n",
			XCube, BOH);

		fprintf(p1TraceFile, "\tProfiles before sort:\n");
		for (idx=0; idx < tptr->Table->ItemCount; ++idx) {
			fprintf(p1TraceFile, "\t%-30.30s (%d): Extended Cube: %.04f, Extended BOH: %.04f, "
				"Difference from product: XCube: %.04f, XBOH: %.04f, Linear: %.04f\n", 
				profiles[idx].desc, profiles[idx].profileID, 
				profiles[idx].logxCube, profiles[idx].logxBOH,
				profiles[idx].diffxCube,
				profiles[idx].diffxBOH,
				profiles[idx].actualDiff);
		}
		fprintf(p1TraceFile, "\n");
	}

	for (btIdx=0; btIdx < numBasicTypes; ++btIdx)
		basicTy[btIdx].rejected = NATIVE_FIT;

	// sort profile table by diffxCube and diffxBOH
	qsort(tptr->Table->TableData, tptr->Table->ItemCount,sizeof(TABLE_ENTRY),tableDiffCompare);

	if (debugLog || userLog || aProd->trace) {
		fprintf(p1TraceFile, "\tProfiles after sort:\n");
		for (idx=0; idx < tptr->Table->ItemCount; ++idx) {
			fprintf(p1TraceFile, "\t%-30.30s (%d): Extended Cube: %.04f, Extended BOH: %.04f, "
				"Difference from product: XCube: %.04f, XBOH: %.04f, Linear: %.04f\n", 
				profiles[idx].desc, profiles[idx].profileID, 
				profiles[idx].logxCube, profiles[idx].logxBOH,
				profiles[idx].diffxCube,
				profiles[idx].diffxBOH,
				profiles[idx].actualDiff);
		}
		fprintf(p1TraceFile, "\n");
	}

	if (aProd->trace) {
		fprintf(p1TraceFile, "\nProfile Dimensions:\n");
		for (int idx=0; idx < tptr->Table->ItemCount; ++idx) {
			int bi = getBasicTypeIndex(profiles[idx].profileID, profiles[idx].levelType);
			if (bi < 0)
				continue;

			fprintf(p1TraceFile, "%-30.30s(%d): Width: %.02f, Length: %.02f, Height: %.02f\n",
				basicTy[bi].desc, profiles[idx].profileID,
				basicTy[bi].maxWidth, basicTy[bi].maxDepth, basicTy[bi].maxHeight);
		}
	}

				
	// Ideal 
	// Since the list is sorted by proximity to the product, 
	// the ideal is the first one in the list that fits
	if (debugLog || userLog || aProd->trace)
		fprintf(p1TraceFile,"Starting Ideal Analysis\n");

	for (idx = 0; idx < tptr->Table->ItemCount; ++idx) {
		if (willProductFit(aProd, profiles[idx], 0)) {
			if (debugLog || userLog || aProd->trace) {
				fprintf(p1TraceFile,"\tFound ideal - profile: %s(%d), type: %s, facings: %d\n",
					profiles[idx].desc, profiles[idx].profileID, 
					ConvertBayType(profiles[idx].levelType).c_str(), profiles[idx].facingCount);
			}
			rackIdeal->facingCount = profiles[idx].facingCount;
			rackIdeal->idealProfileID = profiles[idx].profileID;
			rackIdeal->idealLevelType = profiles[idx].levelType;
			rackIdeal->xBOH = profiles[idx].xBOH;
			rackIdeal->xCube = profiles[idx].xCube;
			break;
		}
		else {
			btIdx = getBasicTypeIndex(profiles[idx].profileID, profiles[idx].levelType);
			if (basicTy[btIdx].rejected != NO_FIT) {
				if (debugLog || aProd->trace) {
					fprintf(p1TraceFile,"\tProduct will not fit - profile: %s(%d), type: %s, facings: %d\n",
						profiles[idx].desc, profiles[idx].profileID, 
						ConvertBayType(profiles[idx].levelType).c_str(), profiles[idx].facingCount);
				}
				
				char tmp[50];

				sprintf(tmp,"Y|R|I|%d|%d|%d|\n",profiles[idx].levelType, aProd->dbID, profiles[idx].profileID);
				rejectionList.push_back(tmp);
				basicTy[btIdx].rejected = NO_FIT;
			}
		}
	}
	
	// if the product doesn't fit in any profiles use the first one (closest)
	if (rackIdeal->facingCount == RACK_NOT_FOUND) {
		if (debugLog || aProd->trace) {
			fprintf(p1TraceFile,"\tNo profiles fit, using closest - profile: %s(%d), type: %s, facings: %d\n",
				profiles[0].desc, profiles[0].profileID, 
				ConvertBayType(profiles[0].levelType).c_str(), profiles[0].facingCount);
		}

		rackIdeal->facingCount = profiles[0].facingCount;
		rackIdeal->idealProfileID = profiles[0].profileID;
		rackIdeal->idealLevelType = profiles[0].levelType;
		rackIdeal->xBOH = profiles[0].xBOH;
		rackIdeal->xCube = profiles[0].xCube;
	}

	// Best Available 
	// Iterate through sorted profiles until the product fits
	// Five loops:
	// 1) Find rankings that are limited to the drive parameters and have different profile/type
	// 2) Find rankings that are in the drive parameters but not limited and have different profile/type
	// 3) Find rankings regardless of drive parameters that have different profile/type
	// 4) Find rankings regardless of drive parameters or different profile/type but still fit
	// 5) Find rankings regardless of fit

	if (debugLog || userLog || aProd->trace)
		fprintf(p1TraceFile,"\nStarting Best Available Analysis\n");

	for (btIdx=0; btIdx < numBasicTypes; ++btIdx)
		basicTy[btIdx].rejected = NATIVE_FIT;

	int currentRanking = 0;
	int checkProdGroup = 1;

	int bFacility;
	int bLimited;
	int bNotLimited;
	int bNativeFit;
	int bCaseFit;
	int bNoDuplicates;
	int inner;

	if (aProd->prodGpID == 0 || IsPGDriveLimited(aProd->prodGpID) == PG_NOT_FOUND)
		checkProdGroup = 0;

	int wayOuterTimes = 2;
	int wayOuter;


	if (skipUsedProfiles)
		wayOuter = 0;
	else
		wayOuter = 1;

	for (; wayOuter < wayOuterTimes; ++wayOuter) {


	for (int outer = 0; outer < 4 && currentRanking < numAvailRanks; ++outer) {

		if (outer == 1 && ! BreakPalletFlag)	// no point if  flag is off
			continue;

		inner = 0;
		if (! checkProdGroup)	// skip first two (checking drive parameters)
			inner = 2;

		for ( ; inner < 3 && currentRanking < numAvailRanks; ++inner) {

			if (debugLog) {
				switch (outer) {
				case 0:
					fprintf(p1TraceFile, "<DBGMSG> Outer 0 - Native Fit, No Duplicates");
					break;
				case 1:
					fprintf(p1TraceFile, "<DBGMSG> Outer 1 - Case Fit, No Duplicates");
					break;
				case 2:
					fprintf(p1TraceFile, "<DBGMSG> Outer 2 - Ignore Fit, No Duplicates");
					break;
				case 3:
					fprintf(p1TraceFile, "<DBGMSG> Outer 1 - Ignore Fit, Allow Duplicates");
					break;
				}

				switch (inner) {
				case 0:
					fprintf(p1TraceFile, ", Inner 0 - Drive Parameters Exclusive\n");
					break;
				case 1:
					fprintf(p1TraceFile, ", Inner 1 - Drive Parameters Non-Exclusive\n");
					break;
				case 2:
					fprintf(p1TraceFile, ", Inner 2 - Ignore Drive Parameters\n");
					break;
				}
			}

			for (int idx = 0; idx < tptr->Table->ItemCount && currentRanking < numAvailRanks; ++idx) {

				bFacility = 1;		// always check facility
				bLimited = bNotLimited = bNativeFit = bCaseFit = bNoDuplicates = 0;
				
				switch (outer) {
					
				case 0:	{			
					bNativeFit = 1;			// check fit based on handling method
					bNoDuplicates = 1;		// don't allow same profile in multiple rankings
					switch (inner) {
					case 0:
						bLimited = 1;		// check drive parameters exclusive
						break;
					case 1:
						bNotLimited = 1;	// check drive parameters non-exclusive
						break;
					case 2:
						break;				// ignore drive parameters
					}
					break;
						}
					
				case 1: {
					bCaseFit = 1;			// check fit as case handling
					bNoDuplicates = 1;		// don't allow same profile in multiple rankings
					switch (inner) {
					case 0:
						bLimited = 1;		// check drive parameters exclusive
						break;
					case 1:
						bNotLimited = 1;	// check drive parameters non-exclusive
						break;
					case 2:
						break;				// ignore drive parameters
					}
					break;
						}

				case 2: {
					// don't check fit at all;
					bNoDuplicates = 1;		// don't allow same profile in multiple rankings
					switch (inner) {
					case 0:
						bLimited = 1;		// check drive parameters exclusive
						break;
					case 1:
						bNotLimited = 1;	// check drive parameters non-exclusive
						break;
					case 2:
						break;				// ignore drive parameters
					}
					break;
						}

				case 3: {
					// allow same profile in multiple rankings
					switch (inner) {
					case 0:
						bLimited = 1;		// check drive parameters exclusive
						break;
					case 1:
						bNotLimited = 1;	// check drive parameters non-exclusive
						break;
					case 2:
						break;				// ignore drive parameters
					}
					break;
						}
				}
				

				if (checkProfile(bFacility, bLimited, bNotLimited, bNativeFit, 
							   bCaseFit, bNoDuplicates, aProd, rankings, 
							   currentRanking, profiles,idx, wayOuter) > 0) {
					rankings[currentRanking] = idx;
					assignAvail(aProd, currentRanking, profiles[idx], rackAvail, bNoDuplicates, idx);
					currentRanking++;
				}

			}
		}
	}

	}

	// If at least one ranking was found, use it for all the rankings that weren't found
	if (rankings[0] != RACK_NOT_FOUND) {
		for (int rankIdx = currentRanking; rankIdx < numAvailRanks; ++rankIdx) {

			if (rankings[rankIdx] == RACK_NOT_FOUND) {
				if (debugLog)
					fprintf(p1TraceFile,"<DBGMSG> Copying ranking 1 to ranking %d.\n", rankIdx+1);
				rankings[rankIdx] = rankings[0];

				assignAvail(aProd, rankIdx, profiles[rankings[0]], rackAvail, 0, rankings[0]);
			}
		}	
		
		if (SortRankingFlag) {
			if (debugLog)
				fprintf(p1TraceFile,"<DBGMSG> Sorting rankings.\n");
			sortRankings(rackAvail, numAvailRanks);
		}
		
		// Decrement available space in Facility for Best Available rack type
		if (debugLog)
			fprintf(p1TraceFile,"<DBGMSG> Allocating space.\n");
		err = useSpace(tptr, numAvailRanks, rackAvail, aProd, passMsg);
	}
	else {
		if (debugLog)
			fprintf(p1TraceFile,"<DBGMSG> No profiles found.\n");
	}
	if (debugLog)
		fprintf(p1TraceFile,"<DBGMSG> Processing rejections.\n");
	processRejections(aProd);

	if (debugLog)
		fprintf(p1TraceFile,"<DBGMSG> Freeing Temporary Memory\n");
	free(rankings);

	return;

}

/**
 * 调整产品数值函数 - Pass1算法的关键预处理步骤
 *
 * 功能说明：
 * 1. 将产品参数调整到合理的边界范围内
 * 2. 根据度量系统（英制/公制）进行对数转换
 * 3. 计算回归线纵坐标值
 * 4. 将产品参数调整到回归线"热区"内
 * 5. 根据优化标志选择调整策略（空间优化 vs 劳动力优化）
 *
 * 热区概念：回归线上下一定范围内的区域，产品参数在此区域内
 * 能获得最佳的货架匹配效果
 *
 * @param aProd 产品数据包指针
 * @param tptr 超级组数据指针，包含回归线参数和边界值
 */
void Pass1Process::adjustProductValues(prodPack *aProd, P1DATA *tptr)
{

	double Rgr_Y;				// 回归线纵坐标值
	double BOH, XCube, oldXBOH, oldXCube;	// 当前值和原始值

	// 获取产品的原始参数
	BOH = aProd->BOH;			// 库存量（Balance On Hand）
	XCube = aProd->xCube;		// 扩展立方体

	// 应用倍数因子进行缩放
	BOH *= multiple;
	XCube *= multiple;

	//////////////////////////////////////////////////////////////////////////
	// 步骤1: 将数值重置到边界范围内
	//////////////////////////////////////////////////////////////////////////
	if (debugLog)
		fprintf(p1TraceFile,"<DBGMSG> 边界检查前的对数值 LogXCube[%g] LogXBOH[%g]\n",log10(XCube),log10(BOH));

	if (debugLog)
		fprintf(p1TraceFile,"<DBGMSG> 检查XCube/XBOH是否在上下边界范围内\n");

	// 确保扩展立方体在允许范围内
	if (XCube < tptr->X.low)
		XCube = tptr->X.low;		// 小于下限时设为下限
	else if (XCube > tptr->X.hi)
		XCube = tptr->X.hi;			// 大于上限时设为上限

	// 确保库存量在允许范围内
	if (BOH < tptr->B.low)
		BOH = tptr->B.low;			// 小于下限时设为下限
	else if (BOH > tptr->B.hi)
		BOH = tptr->B.hi;			// 大于上限时设为上限

	//////////////////////////////////////////////////////////////////////////
	// 步骤2: 根据度量系统进行对数转换
	// 历史说明：Dex和Fred在最初开发时发现，对于英制单位，
	// 使用以10为底的对数转换能在回归线上获得最佳的数值分布。
	// 2004年，Les Long和Marc Stuart发现对于公制设施，
	// 不进行对数转换效果更好。
	//////////////////////////////////////////////////////////////////////////

	// 从Windows注册表查找度量系统设置（公制 vs 英制）
	HKEY hRegKey;
	char measureSys[32];
	measureSys[0]=0;
	DWORD dwType = REG_SZ;
	DWORD dwReturnLength;

	if (RegOpenKeyEx(HKEY_LOCAL_MACHINE, "Software\\SSA Global\\Optimize", 0, KEY_READ, &hRegKey) == ERROR_SUCCESS) {
	int i = 0;
	while ((RegQueryValueEx(hRegKey, "Units", NULL, &dwType,(LPBYTE)measureSys, &dwReturnLength) != ERROR_SUCCESS ) && i<=100)
			i++;
	if (i>100)
		measureSys[0] = 0;
	}
	RegCloseKey(hRegKey);

	// 根据度量系统决定是否进行对数转换
	if (_stricmp(measureSys, "Metric")==0) {
		// 公制系统：不进行对数转换
	} else {
		// 英制系统：进行以10为底的对数转换
		XCube = log10(XCube);
		BOH = log10(BOH);
	}
	if (debugLog)
		fprintf(p1TraceFile,"<DBGMSG> 转换后的对数值 LogXCube[%10.6f] LogXBOH[%10.6f]\n",XCube,BOH);

	//////////////////////////////////////////////////////////////////////////
	// 步骤3: 计算回归线纵坐标值
	// 使用线性回归公式: Y = a * X + b
	//////////////////////////////////////////////////////////////////////////
	Rgr_Y = tptr->Line.a * XCube + tptr->Line.b;
	if (debugLog)
		fprintf(p1TraceFile,"<DBGMSG> 回归线纵坐标值 = %10.6f\n", Rgr_Y);

	//////////////////////////////////////////////////////////////////////////
	// 步骤4: 热区调整 - Pass1算法的核心优化逻辑
	//
	// 热区定义：回归线上下一定偏差范围内的区域
	// - 上热区边界：Rgr_Y + tptr->Y.hi
	// - 下热区边界：Rgr_Y - tptr->Y.low
	//
	// 如果产品的BOH值超出热区范围，需要根据优化标志进行调整：
	// - OptFlag==0：优化空间利用率，倾向于选择较小的货架
	// - OptFlag==1：优化劳动力效率，倾向于选择较大的货架
	//////////////////////////////////////////////////////////////////////////

	// 保存原始值用于比较
	oldXCube = XCube;
	oldXBOH = BOH;

	if (Rgr_Y + tptr->Y.hi < BOH) {				// 产品BOH在上热区之外（高于回归线）
		if (aProd->optBy == 1) {
			// 劳动力优化：增大XCube以选择更大的货架
			// 通过回归线公式反推：XCube = (BOH - b - Y.hi) / a
			XCube = (BOH - tptr->Line.b - tptr->Y.hi) / tptr->Line.a;
			if ( oldXCube > XCube )
				XCube = oldXCube;		// 不允许XCube减小
			else {
				if (debugLog)
					fprintf(p1TraceFile,"<DBGMSG> 基于劳动力优化标志重置XCube为 %10.6f\n", XCube);
			}

		}//劳动力优化，选择更大货架（增加XCube）
		else {
			// 空间优化：降低BOH到热区上边界
			BOH = Rgr_Y + tptr->Y.hi;
			if ( BOH > oldXBOH )
				BOH = oldXBOH;			// 不允许BOH增大
			else {
				if (debugLog)
					fprintf(p1TraceFile,"<DBGMSG> 基于空间优化标志重置XBOH为 %10.6f\n", BOH);
			}
		}//空间优化，选择较小货架（减少BOH）
	}
	else if (Rgr_Y - tptr->Y.low > BOH) {		// 产品BOH在下热区之外（低于回归线）
		if (aProd->optBy == 0) {
			// 空间优化：减小XCube以选择更小的货架
			XCube = (BOH - tptr->Line.b - tptr->Y.low) / tptr->Line.a;
			if ( XCube > oldXCube )
				XCube = oldXCube;		// 不允许XCube增大
			else {
				if (debugLog)
					fprintf(p1TraceFile,"<DBGMSG> 基于空间优化标志重置XCube为 %10.6f\n", XCube);
			}
		}//空间优化，选择较小货架（减少XCube）
		else  {
			// 劳动力优化：提高BOH到热区下边界
			BOH = Rgr_Y - tptr->Y.low;
			if ( oldXBOH > BOH )
				BOH = oldXBOH;			// 不允许BOH减小
			else {
				if (debugLog)
					fprintf(p1TraceFile,"<DBGMSG> 基于劳动力优化标志重置XBOH为 %10.6f\n", BOH);
			}
		}//劳动力优化，选择较大货架（增加BOH）
	}

	aProd->xCube = (double)XCube;
	aProd->BOH = (double)BOH;

	return;

}

void Pass1Process::sortRankings(struct availResult *rackAvail, int numAvailRanks)
{
	int btIdx;
	if (debugLog) {
		fprintf(p1TraceFile,"<DBGMSG> Rankings before sort:\n");
		for (int i = 0; i < numAvailRanks; ++i) {
			btIdx = getBasicTypeIndex(rackAvail[i].availProfileID, rackAvail[i].availLevelType);

			fprintf(p1TraceFile, "<DBGMSG>    %d: %d - %s, LevelType: %d, Facings: %d, Handling: %d, HeightDiff: %f\n",
				(i+1), rackAvail[i].availProfileID, basicTy[btIdx].desc, rackAvail[i].availLevelType,
				rackAvail[i].facingCount, rackAvail[i].actualHandling, rackAvail[i].heightDiff);
		}
	}
		
	// sort rankings by handling method (descending), then by height difference
	qsort(rackAvail, numAvailRanks, sizeof(availResult), compareRankings);

	if (debugLog) {
		fprintf(p1TraceFile,"<DBGMSG> Rankings after sort:\n");
		for (int i = 0; i < numAvailRanks; ++i) {
			btIdx = getBasicTypeIndex(rackAvail[i].availProfileID, rackAvail[i].availLevelType);

			fprintf(p1TraceFile, "<DBGMSG>    %d: %d - %s, LevelType: %d, Facings: %d, Handling: %d, HeightDiff: %f\n",
				(i+1), rackAvail[i].availProfileID, basicTy[btIdx].desc, rackAvail[i].availLevelType,
				rackAvail[i].facingCount, rackAvail[i].actualHandling, rackAvail[i].heightDiff);
		}
	}

}


void Pass1Process::processRejections(prodPack *aProd)
{
	if (aProd->trace)
		fprintf(p1TraceFile, "The following profiles were rejected because the product would not fit:\n");

	// for each basicTy; if rejected flag is on, add record to list
	for (int btIdx = 0; btIdx < numBasicTypes; ++btIdx) {
		if (basicTy[btIdx].rejected != NATIVE_FIT) {
			
			char tmp[50];
			sprintf(tmp,"Y|R|B|%d|%d|%d|\n",
				basicTy[btIdx].levelType, aProd->dbID, basicTy[btIdx].profileID);

			rejectionList.push_back(tmp);
			if (debugLog)
				fprintf(p1TraceFile, "   %d - ->%-s<-\n", rejectionList.size(), tmp);

			if (aProd->trace) {
				fprintf(p1TraceFile, "\t%s(%d)\n",
					basicTy[btIdx].desc, basicTy[btIdx].profileID);
			}

			basicTy[btIdx].rejected = NATIVE_FIT;				
		}
	}

}

int compareRankings(const void *p1, const void *p2)
{

	// sort duplicates below everything else
	if (((availResult *)p1)->isDuplicate && ! ((availResult *)p2)->isDuplicate)
		return 1;
	if (! ((availResult *)p1)->isDuplicate && ((availResult *)p2)->isDuplicate)
		return -1;

	
	// sort native fit profiles first
	int p1Fit = ((availResult *)p1)->fits;
	int p2Fit = ((availResult *)p2)->fits;

	if (p1Fit != p2Fit) {
		if (p1Fit == NATIVE_FIT)
			return -1;
		if (p2Fit == NATIVE_FIT)
			return 1;

	}


	// sort pallet handling profiles before case handling
	if (((availResult *)p1)->actualHandling < ((availResult *)p2)->actualHandling)
		return 1;
	if (((availResult *)p1)->actualHandling > ((availResult *)p2)->actualHandling)
		return -1;
	
	// if neither fit natively, sort case fits (break pallet) before no fits
	if (p1Fit != p2Fit) {
		if (p1Fit == CASE_FIT_ONLY)
			return -1;
		if (p2Fit == CASE_FIT_ONLY)
			return 1;
	}
	
	// if both are pallet handling, sort by difference in height between prof and prod
	if (((availResult *)p1)->actualHandling == 3) {
		if (((availResult *)p1)->heightDiff > ((availResult *)p2)->heightDiff)
			return 1;
		if (((availResult *)p1)->heightDiff < ((availResult *)p2)->heightDiff)
			return -1;
	}

	// sort closer xCubes first
	if (((availResult *)p1)->diffxCube > ((availResult *)p2)->diffxCube)
		return 1;
	if (((availResult *)p1)->diffxCube < ((availResult *)p2)->diffxCube)
		return -1;

	// sort closer xBOHs first
	if (((availResult *)p1)->diffxBOH > ((availResult *)p2)->diffxBOH)
		return 1;
	if (((availResult *)p1)->diffxBOH < ((availResult *)p2)->diffxBOH)
		return -1;

	// sort lower facing counts first
	if (((availResult *)p1)->facingCount > ((availResult *)p2)->facingCount)
		return 1;
	if (((availResult *)p1)->facingCount < ((availResult *)p2)->facingCount)
		return -1;
	


	return 0;
}

std::string Pass1Process::ConvertBayType(int bayType)
{
	std::string s;

	switch (bayType) {
	case BAY_TYPE_BIN:
		s = "Bin";
		break;
	case BAY_TYPE_DRIVE_IN:
		s = "DriveIn";
		break;
	case BAY_TYPE_FLOOR:
		s = "Floor";
		break;
	case BAY_TYPE_FLOW:
		s = "CaseFlow";
		break;
	case BAY_TYPE_PALLET:
		s = "Pallet";
		break;
	case BAY_TYPE_PALLET_FLOW:
		s = "PalletFlow";
		break;
	default:
		s = "Unknown";
		break;
	}

	return s;
}

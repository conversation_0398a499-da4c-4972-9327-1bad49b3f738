//////////////////////////////////////////////////////////////////////
// Function Name :	p1process.h
// Classname :		Pass1Process
// Description :	Header for p1process.cpp.
// Date Created :	~5/1/98
// Author : 		mfs
//////////////////////////////////////////////////////////////////////
// Inputs :			None
// Outputs :		None
// Explanation :	Header for p1process.cpp.
//////////////////////////////////////////////////////////////////////
// Revisions : 
//   November 6, 1998-mfs : Standard header comments added
//////////////////////////////////////////////////////////////////////
/*******************************************************
   *
   *                       NOTICE
   *
   *  THIS SOFTWARE IS THE PROPERTY OF AND CONTAINS
   *  CONFIDENTIAL INFORMATION OF SSA GLOBAL 
   *  TECHNOLOGIES, INC., AND SHALL NOT BE COPIED,
   *  USED, NOR DISCLOSED WITHOUT EXPRESS WRITTEN
   *  AUTHORIZATION.  ALTHOUGH PUBLICATION IS NOT
   *  INTENDED, IN THE EVENT OF PUBLICATION, THE
   *  FOLLOWING NOTICE IS APPLICABLE:
   *
   *  (c) COPYRIGHT 2004 SSA GLOBAL TECHNOLOGIES, INC.
   *
   *           SSA GLOBAL TECHNOLOGIES, INC.
   *
   ********************************************************/
//////////////////////////////////////////////////////////////////////
#pragma once 

#ifndef PASS_1_PROCESS
#define PASS_1_PROCESS
#include "..\..\..\common\core\socket_class.h"
#include "p1.h"
#include "p1struct.h"
#include <vector>
#include <string>
#include "..\dispatch.h"
#define START_OF_STREAM "<SSO>"		// Communication protocols
#define END_OF_STREAM "<EOS>"
#define START_OF_SUBLIST "<SSL>"
#define END_OF_SUBLIST "<ESL>"
#define PROTOCOL_TAG_LENGTH 5

class  DISPATCH_API Pass1Process {

	public:
		std::string ConvertBayType(int bayType);
		int Pass1Process::CheckFit(double locW, double locD, double locH, double prodW, double prodL, double prodH);
		void processRejections(prodPack *aProd);
		void sortRankings(struct availResult *rackAvail, int numAvailRanks);
		void adjustProductValues(prodPack *aProd, P1DATA *tptr);
		int Pass1Process::checkProfile(int bFacility, 
			int bLimited, int bNotLimited, 
			int bNativeFit, int bCaseFit, 
			int bNoDuplicates, prodPack *aProd, 
			int *rankings, int currentRanking,
			TABLE_ENTRY *profiles, int idx, int allowUsedUpProfiles);
		void p1Trace(const char *format, ...);
		std::vector<std::string> rejectionList;
		int willProductFit(prodPack *aProd, TABLE_ENTRY profile, int tryCaseHandling);

		// Instantiates a Pass 1 object which listens to the port for
		// its Rule Set and product data
		Pass1Process(SockClass *P1Socket);  
		virtual ~Pass1Process();

		void Execute(void);

		void loadRules(void);		// Retrieve Rules data from socket connection
									// and init the P1.dll library
		void buildUsageRec(int n);	// Move input data into Rack Usage (Rule) structure

		void loadRackTypes(void);	// Retrieve Rack Type data from socket connection
		void buildRackRec(int n);	// Move input data into Basic RackType structure for loc. tracking
		void addRackGroups(void);	// Move RackType data into RackGroup structures
		int getBasicTypeIndex(int bayProfileID, int levelType);	// Validates Basic Rack Types used in Rack Usages

		int loadProducts(void);		// Retrieve Product data from socket connection
		void buildProdRec(int n);	// Move input data into ProductPack structure
		void ReadIniParameters(void);
//		void MyDestructor(void);

		void getCost(prodPack *aProd, struct idealResult *rackIdeal, 
			struct availResult *rackAvail, int numRanks, char * passMsg);

		void loadNumItems(int * numProfiles, int * numBaysTypes, int * numProducts, int * numHazard, int * numNonHazard);
		int	 p1_init();
		void p1_execute(prodPack *aProd,
			struct idealResult *rackIdeal,	/* return values */
			struct availResult *rackAvail,
			int numAvailRanks,
			char * passMsg);
		int isInFacility(TABLE_ENTRY profile);
		int useSpace(P1DATA *superGroup, int numRankings, 
			struct availResult *rackAvail, prodPack *aProd, char * passMsg);
		int Pass1Process::IsBayInPGDriveLimited(int bayProfileID, int prodGpID);
		int Pass1Process::IsBayInPGDriveNotLimited(int bayProfileID, int prodGpID);
		void Pass1Process::buildDriveRec(int n);
		void Pass1Process::loadDriveParameters(void);
		int Pass1Process::IsPGDriveLimited(int prodGpID);
		int GetUserFlags(void);
		void assignAvail(prodPack *aProd, int currentRanking, 
						table_entry profile, 
						 struct availResult *rackAvail,
						 int bNoDuplicates,
						 int profileIdx);
		int facDivisor;

	protected:			

		prodPack		*prodPk;
		TABLE_ENTRY		*rackTy;					// the raw Rack Usage tabular data
		P1DATA			*rackGp;					// the Rack Usage data sorted into Super Group tables
		BASIC_RACK_TYPE	*basicTy;					// the basic Rack Types for tallying location usage
		PROD_GROUP_DRIVE *prodGpDrive;

//		PassOne			*aPass1;					// the 'Fuzzy I/O' Pass object;

		SockClass		*dataSocket;				// data communication socket

		struct idealResult	 *prodRackIdeal;		// Stores Rack Indices returned
													// from P1.dll for each Product
		struct availResult	*prodRackAvail;

		int numProds;
		int numRackUsages;
		int numRackGroups;
		int numBasicTypes;
		int numDriveParameters;

		char errMsg[DATA_LINE_LEN];

};

#endif // Pass1Process defined

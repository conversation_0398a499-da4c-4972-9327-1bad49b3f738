#include "afxwin.h"
#include "p1_fz_io.h"
#include "../core/debug.h"
#include <stdio.h>
#include <stdlib.h>

P1fzio::P1fzio()
{
	/* ************************************************** */
	/* Constructor without a filename doesn't do much for */
	/* us right now, so I won't do much in the way of any */
	/* code for it.                                       */
	/* ************************************************** */

}

P1fzio::P1fzio(char *fname)
{
	mem_FileName = (char *)malloc(512);
	strcpy(mem_FileName, fname);        
	/* Call<PERSON> can now get rid of fname and we don't care */



	/* ******************************************* */
	/* Load up the fuzzy dll.                      */
	/* ******************************************* */
	
	mem_fzInstance = LoadLibrary("c:\\cbc\\lib\\cbc20r32.dll");
	if(mem_fzInstance == NULL){
		if (SLOT_DEBUG) printf("Load of the fuzzy dll failed.\n");
		fini_winsock();
		exit(1);
	}

	mem_fzInit = (int (__stdcall *)(char *))GetProcAddress(mem_fzInstance, "wfz_init"); 
	if(mem_fzInit == NULL){
		if (SLOT_DEBUG) printf("Resolve of fuzzy init function failed.\n");
		fini_winsock();
		exit(1);
	}

	mem_fzClose = (int (__stdcall *)(int ))GetProcAddress(mem_fzInstance, "wfz_close");
	if(mem_fzClose == NULL){
		if (SLOT_DEBUG) printf("Resolve of fuzzy close function failed\n");
		fini_winsock();
		exit(1);
	}

	mem_fzEval = (int (__stdcall *)(int, FZ_NUMERIC *, FZ_NUMERIC *))GetProcAddress(mem_fzInstance, "wfz_eval");
	if(mem_fzEval == NULL){
		if (SLOT_DEBUG) printf("Resolve of fuzzy evaluation function failed\n");
		fini_winsock();
		exit(1);
	}

	mem_inputs = (double *)malloc(P1_INPUT_COUNT * sizeof(double));
	mem_outputs = (double *)malloc(P1_OUTPUT_COUNT * sizeof(double));

	mem_min = 0;
	mem_max = 10000;

	mem_fzChannel = mem_fzInit(mem_FileName);

	/* ****************************************************** */
	/* fuzzy primed and ready to go.                          */
	/* ****************************************************** */

}

P1fzio::~P1fzio()
{
	mem_fzClose(mem_fzChannel);

	FreeLibrary(mem_fzInstance);

	free(mem_inputs);
	free(mem_outputs);
	free(mem_FileName);	
}

float P1fzio::getCost(float mvmt, float inven, float loc_type)
{
	mem_inputs[0] = loc_type;
	mem_inputs[1] = mvmt;
	mem_inputs[2] = inven;

	mem_outputs[0] = 0.0;

	mem_fzEval(mem_fzChannel, mem_inputs, mem_outputs);

	return (float)mem_outputs[0];
}

PassOne::PassOne()
{
}

PassOne::~PassOne()
{
}

/////////////////////////////////////////////////////////////////
// Pass Product data to P1.dll, which returns Rack Type index.
/////////////////////////////////////////////////////////////////
//void PassOne::getCost(udf *udfPtr, int *rulePos, int varCount, RESULTPAIR *rackIndex)
//void PassOne::OLDgetCost(prodPack *aProd, RESULTPAIR *rackIndex)
//{
/*
	if (SLOT_DEBUG) printf("Submitting |XC|%f", (double)atof(udfPtr[0].charVal));
	if (SLOT_DEBUG) printf("|BOH|%f", (double)atof(udfPtr[1].charVal));
	if (SLOT_DEBUG) printf("|CCF|%f", (double)atof(udfPtr[6].charVal));
	if (SLOT_DEBUG) printf("|CM|%f", (double)atof(udfPtr[7].charVal));
	if (SLOT_DEBUG) printf("|Haz|%d", (int)atoi(udfPtr[2].charVal));
	if (SLOT_DEBUG) printf("|HUA|%d", (int)atoi(udfPtr[3].charVal));
	if (SLOT_DEBUG) printf("|PTB|%d", (int)atoi(udfPtr[4].charVal));
	if (SLOT_DEBUG) printf("|Opt|%d|\n", (int)atoi(udfPtr[5].charVal));

	p1_execute(	(double)atof(udfPtr[0].charVal),
				(double)atof(udfPtr[1].charVal),
				(double)atof(udfPtr[6].charVal),
				(double)atof(udfPtr[7].charVal),
				(int)atoi(udfPtr[2].charVal),
				(int)atoi(udfPtr[3].charVal),
				(int)atoi(udfPtr[4].charVal),
				(int)atoi(udfPtr[5].charVal),
				(RESULTPAIR *)rackIndex);
*/
/*
	if (SLOT_DEBUG) printf("Submitting |XC|%f", aProd->xCube);
	if (SLOT_DEBUG) printf("|BOH|%f", aProd->xBOH);
	if (SLOT_DEBUG) printf("|CCF|%f", aProd->cube);
	if (SLOT_DEBUG) printf("|CM|%f", aProd->movement);
	if (SLOT_DEBUG) printf("|Haz|%d", aProd->hazard);
	if (SLOT_DEBUG) printf("|UOI|%d", aProd->UOI);
	if (SLOT_DEBUG) printf("|PTB|%d", aProd->PTB);
	if (SLOT_DEBUG) printf("|Opt|%d|\n", aProd->optBy);

	p1_execute(	aProd->xCube,
				aProd->BOH,
				aProd->UOI,
				aProd->cube,
				aProd->movement,
				aProd->hazard,
				aProd->PTB,
				aProd->optBy,
				(RESULTPAIR *)rackIndex);

//	return rackIndex;
*/
//}

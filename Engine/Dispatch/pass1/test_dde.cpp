#include <test_dde.h>

/* ********************************************************** */
/* Necessary global variables.                                */
/* ********************************************************** */
DWORD hInstance;
HCONV cbcConversation;

HDDEDATA CALLBACK DdeCallback( UINT uType,  // transaction type 
UINT uFmt,  // clipboard data format 
HCONV hconv,  // handle to the conversation 
HSZ hsz1,  // handle to a string 
HSZ hsz2,  // handle to a string 
HDDEDATA hdata,  // handle to a global memory object 
DWORD dwData1,  // transaction-specific data 
DWORD dwData2  // transaction-specific data 
)
{
	// this function does nothing.
	return 0;
}
 

int dde_init(char *filename)
{
	UINT err;
	HSZ cbcTopicName;
	HSZ cbcServiceName;
	char cmdstring[50];

	sprintf(cmdstring, "[open(\"%s\")]", filename);

	hInstance = 0;
	err = DdeInitialize(&hInstance, DdeCallback, APPCMD_CLIENTONLY , 0);

	if(err != DMLERR_NO_ERROR){
		printf("Error initializing Dde access\n");
		print_error(hInstance);
		return 0;
	}

	cbcServiceName = DdeCreateStringHandle(hInstance, "CubiCalc", CP_WINANSI);
	cbcTopicName = DdeCreateStringHandle(hInstance, "system", CP_WINANSI);
	
	cbcConversation = DdeConnect(hInstance, cbcServiceName, 
		cbcTopicName, NULL);

	if(cbcConversation == 0){
		printf("Failed to connect to CubiCalc!\n");
		return 0;
	}

	DdeFreeStringHandle(hInstance, cbcServiceName);
	DdeFreeStringHandle(hInstance, cbcTopicName);

	if(!dde_command(cmdstring))
		return 0;

	// Now the file should be open, change the conversation to 
	// be specific to the open file.
	cbcTopicName = DdeCreateStringHandle(hInstance, filename, CP_WINANSI);
	cbcConversation = DdeConnect(hInstance, cbcServiceName,
		cbcTopicName, NULL);
	if(cbcConversation == 0){
		printf("Failed to reset the conversation to the filename!\n");
		return 0;
	}

	DdeFreeStringHandle(hInstance, cbcTopicName);
	return 1;

}

int dde_get(char *varname, char *value)
{
	HSZ hItem;
	HDDEDATA hData;
	char *ptr;
	DWORD size;


	hItem = DdeCreateStringHandle(hInstance, varname, CP_WINANSI);

	hData = DdeClientTransaction(NULL, 0, cbcConversation, hItem,
		CF_TEXT, XTYP_REQUEST, 1000, NULL);

	if(hData == 0){
		printf("Transaction to get %s failed\n", varname);
		print_error(hInstance);
		return 0;
	}

	ptr = (char *)DdeAccessData(hData, &size);
	strncpy(value, ptr, size);

	DdeFreeStringHandle(hInstance, hItem);

	return 1;

}

int dde_set(char *varname, char *value)
{
	HSZ hItem;
	HDDEDATA hData;

	hItem = DdeCreateStringHandle(hInstance, varname, CP_WINANSI);

	hData = DdeClientTransaction((unsigned char *)value, strlen(value)+1,
		cbcConversation, hItem,
		CF_TEXT, XTYP_POKE, 1000, NULL);

	if(hData == 0){
		printf("Transaction to set %s failed\n", varname);
		print_error(hInstance);
		return 0;
	}

	return 1;
}


int dde_command(char *cmd)
{
	HDDEDATA hData;

//	printf("Telling CubiCalc to execute ---%s---\n", cmd);
	
	hData = DdeClientTransaction((unsigned char *)cmd, strlen(cmd)+1, 
		cbcConversation, 0L, 0, XTYP_EXECUTE, 10000, NULL);

	if(hData == 0){
		printf("Transaction Failed\n");
		print_error(hInstance);
		return 0;
	}

	return 1;
}


void print_error(DWORD hInst)
{
	UINT err;

	err = DdeGetLastError(hInst);

	switch(err) {
	case DMLERR_ADVACKTIMEOUT: {
		printf("Caught a ADV ACK timeout error\n");
		break;
							   }
	case DMLERR_BUSY: {
		printf("Caught a BUSY error\n");
		break;
					  }
	case DMLERR_DATAACKTIMEOUT: {
		printf("Caught a DATA ACK Timeout error\n");
		break;
								}
	case DMLERR_DLL_NOT_INITIALIZED: {
		printf("Caught a DLL Not Initialized error\n");
		break;
									 }
	case DMLERR_EXECACKTIMEOUT: {
		printf("Caught an EXEC ACK timeout error\n");
		break;
								}
	case DMLERR_INVALIDPARAMETER: {
		printf("Caught an InvalidParameter error\n");
		break;
								  }
	case DMLERR_MEMORY_ERROR: {
		printf("Caught a Memory error\n");
		break;
							  }
	case DMLERR_NO_CONV_ESTABLISHED: {
		printf("Caught a no conversation established error\n");
		break;
									 }
	case DMLERR_NO_ERROR: {
		printf("Caught no error\n");
		break;
						  }
	case DMLERR_NOTPROCESSED: {
		printf("Caught a not processed error\n");
		break;
							  }
	case DMLERR_POKEACKTIMEOUT: {
		printf("Caught a POKE ACK timeout error\n");
		break;
								}
	case DMLERR_POSTMSG_FAILED: {
		printf("Caught a post message failed error \n");
		break;
								}
	case DMLERR_REENTRANCY: {
		printf("Caught a Re-entrancy error\n");
		break;
							}
	case DMLERR_SERVER_DIED: {
		printf("Caught a Server Died error\n");
		break;
							 }
	case DMLERR_SYS_ERROR: {
		printf("Caught a System error\n");
		break;
						   }
	case DMLERR_UNADVACKTIMEOUT: {
		printf("Caught an Unadvertized ack timeout error\n");
		break;
								 }
	default : {
		printf("Caught an unknown error (%d)\n", err);
		break;
			  }
	}








}
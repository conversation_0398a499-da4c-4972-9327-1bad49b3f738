--------------------Configuration: PassOne - Win32 Debug--------------------
Begining build with project "C:\Program Files\DevStudio\MyProjects\PassOne\PassOne.dsp", at root.
Active configuration is Win32 (x86) Application (based on Win32 (x86) Application)

Project's tools are:
			"32-bit C/C++ Compiler for 80x86" with flags "/nologo /MTd /W3 /Gm /GX /Zi /Od /D "WIN32" /D "_DEBUG" /D "_WINDOWS" /Fp"Debug/PassOne.pch" /YX /Fo"Debug/" /Fd"Debug/" /FD /c "
			"OLE Type Library Maker" with flags "/nologo /D "_DEBUG" /mktyplib203 /o NUL /win32 "
			"Win32 Resource Compiler" with flags "/l 0x409 /d "_DEBUG" "
			"Browser Database Maker" with flags "/nologo /o"Debug/PassOne.bsc" "
			"COFF Linker for 80x86" with flags "kernel32.lib user32.lib gdi32.lib winspool.lib comdlg32.lib advapi32.lib shell32.lib ole32.lib oleaut32.lib uuid.lib odbc32.lib odbccp32.lib ws2_32.lib /nologo /subsystem:console /incremental:yes /pdb:"Debug/PassOne.pdb" /debug /machine:I386 /out:"Debug/PassOne.exe" /pdbtype:sept "
			"Custom Build" with flags ""
			"<Component 0xa>" with flags ""

Creating temp file "C:\TEMP\RSP251.tmp" with contents </nologo /MTd /W3 /Gm /GX /Zi /Od /D "WIN32" /D "_DEBUG" /D "_WINDOWS" /Fp"Debug/PassOne.pch" /YX /Fo"Debug/" /Fd"Debug/" /FD /c 
"C:\Program Files\DevStudio\MyProjects\PassOne\P1Process.cpp"
>
Creating command line "cl.exe @C:\TEMP\RSP251.tmp" 
Creating temp file "C:\TEMP\RSP252.tmp" with contents <kernel32.lib user32.lib gdi32.lib winspool.lib comdlg32.lib advapi32.lib shell32.lib ole32.lib oleaut32.lib uuid.lib odbc32.lib odbccp32.lib ws2_32.lib /nologo /subsystem:console /incremental:yes /pdb:"Debug/PassOne.pdb" /debug /machine:I386 /out:"Debug/PassOne.exe" /pdbtype:sept 
".\Debug\p1_fz_io.obj"
".\Debug\P1Process.obj"
".\Debug\P1Test.obj"
".\Debug\pass1fcn.obj"
".\Debug\socket_class.obj"
".\Debug\test_dde.obj"
".\Debug\P1.OBJ">
Creating command line "link.exe @C:\TEMP\RSP252.tmp" 
Compiling...
P1Process.cpp
Linking...



PassOne.exe - 0 error(s), 0 warning(s)

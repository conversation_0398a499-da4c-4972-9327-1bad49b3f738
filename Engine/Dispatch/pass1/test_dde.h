#ifndef TEST_DDE
#define TEST_DDE

#include <stdio.h>
#include <stdlib.h>
#include <windows.h>
#include <ddeml.h>


void print_error(DWORD hInst);

/* ********************************************************** */
/* This function will initialize the CubiCalc dde process and */
/* make sure that the file named in filename is loaded into   */
/* Cubicalc.                                                  */
/* ********************************************************** */
int dde_init(char *filename);

/* ********************************************************** */
/* This function will allow us to send any command to CubiCalc*/
/* that is necessary.  The valid commands are: [run] [step]   */
/* [continue] [pause] [monitor] [terminate] [open("proj")]    */
/* The '[,]' characters are necessary.                        */
/* ********************************************************** */
int dde_command(char *cmd);

/* ********************************************************** */
/* This function will allow us to get the value of any of the */
/* current variables in CubiCalc.                             */
/* ********************************************************** */
int dde_get(char *varname, char *value);

/* ********************************************************** */
/* This function will allow us to set the value of any of the */
/* current variables in CubiCalc to value.                    */
/* ********************************************************** */
int dde_set(char *varname, char *value);

#endif // TEST_DDE defined
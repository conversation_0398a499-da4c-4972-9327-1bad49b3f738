//////////////////////////////////////////////////////////////////////
// Function Name :	P1.cpp
// Classname :		
// Description :	<PERSON>ck Assignment algorithm
// Date Created :	23 April 1998
// Author : 		faw
//////////////////////////////////////////////////////////////////////
// Inputs :
//	double XCube,				/* arg "extended cube" */
//	double BOH,					/* arg "balance on hand" */
//	double CaseCuFt,			/* arg "case cubic feet" */
//	double CaseMovement,		/* arg "case movement" */
//	int Hazard,					/* arg hazard flag flag */
//	int HUA,					/* arg "handling unit attribute" flag */
//	int PTB,					/* arg "pick to belt" flag */
//	int OptFlag					/* arg optimization mode */
//	RESULTPAIR *rackIndex) 		/* arg return values */
// Outputs :
//	RESULTPAIR *rackIndex) 		/* arg return values */
// Explanation :
//
// Determine Super Group
//
// Reset values to be within bounds
//
// Calculate regression line ordinate
//
// Adjust actual data values appropriately as necessary.  (If BOH is outside
//  the 'hot zone' around the regression line, reset value of BOH or XCube
//  into the hot zone, based on the OptFlag setting.  OptFlag==0 = optimize
//  for Cube, i.e. use rack having smaller area, OptFlag==1 = optimize for Labor,
//  i.e. use rack with larger area.
//
// Find IDEAL Rack Type (ignore count from Facility)
//
// Find index of first dominating entry 
//
// Step back one index if previous abscissa (X-coordinate) is closer 
//
// Count number of table entries with same abscissa and leave the
//  pointer on the one with the lowest BOH figure.
//
// Find closest fit among matching entries 
//
// If enough of the Ideal type exist in the Facility,
//  Best Available==Ideal and we're finished.
	// Decrement available space in Facility for Best Available rack type
	// Add the offset to the return values and exit.
//
// Find BEST Rack Type AVAILABLE in Facility (because ideal type is not
// available).
//
// Find index of first dominating entry 
//
//		We didn't get to this part of the loop unless this is a usable
//		basic type, so set isUsable so we know at least one valid type
//		exists.  We will use this max usable element in case we don't
//		find a stopping place.
//
//		As soon as XCube value is EXceed(c) -ed by regression value, exit.
//
// We will use this max usable element in case we never find a
// stopping place.
//
// If isUsable still has default value, there are no basic types with
// avilable locations in the Super Group.
//
// Add offset value and return the indices, leaving Best Available
// with default value of not found.  There are no unused locations
// left in the Facility for the required Super Group
//
// If we exited on the first usable row, lastUsable never got set.  So
// here we make it = bestAvl, which will cause us to skip looking back
// to compare with our last usable entry.
//
// If we never picked a usable row, bestAvl never got set.  So here we
// make it = isUsable, along with lastUsable, which will also cause us
// to skip looking back to compare with our last usable entry.
//
// Unless we only found one rack type existing in the Facility,
// compare the index we found with the last usable one and use it
// if its abscissa is closer.
//
// Count number of table entries with same abscissa
//
// Find closest fit among matching entries 
// WHICH ALSO exist in Facility
//
// Decrement available space in Facility for Best Available rack type
//
//////////////////////////////////////////////////////////////////////
// Revisions : 
//   April - June 1998 mfs: Additional coding and changes
//   November 6, 1998-mfs : Standard header comments added
//////////////////////////////////////////////////////////////////////
// (c) Copyright, EXE Tecnologies, 1998
//////////////////////////////////////////////////////////////////////

/*
 |  P1.C  -  Slotting Pass 1, Algorithmic Version
 |
 |  Written by: Fred A Watkins, Ph.D.
 |          Of: Hyperlogic Corporation
 |         For: EXE Technologies
 |          On: 23 April 1998
*/

//////////////////////////////////////////////////////////////////////////
// Additional coding and changes by Marc Stuart, April - June 1998
//////////////////////////////////////////////////////////////////////////

#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <math.h>

#include "p1.h"
#include "../core/debug.h"
#include "../core/exceptions.h"

// Local copies of Rack Usage and Basic Rack Type data
static P1DATA *P1Tables;			
static int mTableCount=0;

static BASIC_RACK_TYPE *P1BasicRack;
static int mNumBasicTypes=0;

// Best Available calculation: 0=use Facings count (for Super Groups HAZ,
// PTB, and DEF) 1=use Cube (CAR and PIR)
static int mUseCube;
int tableCompare(const void * p1, const void * p2);

int p1_init(P1DATA *Tables, int TableCount, BASIC_RACK_TYPE *basicRackTypes, int numBasicTypes);
void p1_exit(void);
void p1_execute(
	double XCube,				/* arg "extended cube" */
	double BOH,					/* arg "balance on hand" */
	int UOI,
	double CaseCuFt,			/* arg "case cubic feet" */
	double CaseMovement,		/* arg "case movement" */
	int Hazard,					/* arg hazard flag flag */
	int OptFlag,				/* arg optimization mode */
	struct ideal_result *rackIdeal,	/* return values */
	struct avail_result *rackAvail);
int isAvailable(P1DATA *superGroup, int n);
int getBasicType(int n);
int useSpace(P1DATA *superGroup, int n, float prodWidth, int UOI);

	/*
	 | p1_init - initialize DLL
	 |
	 | What To Do:
	 |
	 |  1. Make sure all XCube and BOH entries in tables are > 0.0
	 |  2. Allocate storage:
	 |     a. One P1DATA plus data area for each table
	 |     b. Insert parametric data in P1DATA structures
	 |     c. Place each data area pointer into its P1DATA structure
	 |  3. For each table, copy the log10 of the XCube and BOH values
	 |     into the appropriate data area P1TABLE structure
	 |  4. Return status code
	*/

//__declspec( dllexport ) int p1_init(P1DATA *Tables, int TableCount) {
int p1_init(P1DATA *Tables, int TableCount, BASIC_RACK_TYPE *basicRackTypes, int numBasicTypes) {

	int i,j;

	mTableCount = TableCount;
	mNumBasicTypes = numBasicTypes;

	// Make sure all XCube and BOH entries in tables are > 0.0
	for (i=0;i<TableCount;i++) {
		for (j=0;j<Tables[i].Table->ItemCount;j++) {
			if (Tables[i].Table->TableData[j].xCube <= 0) {
					if (SLOT_DEBUG) printf("p1_init:  Bad Rack data passed (XCube <= 0).\n");
					if (SLOT_DEBUG) printf("p1_init:  Group = %d, Table = %d.\n", i, j);
					if (SLOT_DEBUG) printf("p1_init:  Resetting XCube to .01.\n");
					Tables[i].Table->TableData[j].xCube = .01;
			}

			if (Tables[i].Table->TableData[j].xBOH <= 0) {
					if (SLOT_DEBUG) printf("p1_init:  Bad Rack data passed (XBOH <= 0).\n");
					if (SLOT_DEBUG) printf("p1_init:  Group = %d, Table = %d.\n", i, j);
					if (SLOT_DEBUG) printf("p1_init:  Resetting XBOH to .01.\n");
					Tables[i].Table->TableData[j].xBOH = .01;
			}
		}
	}

	 // Allocate storage:
	 // a. One P1DATA plus data area for each table
	P1Tables = Tables;
	 // c. Basic Rack Types

	P1BasicRack = basicRackTypes;

	for ( i = 0; i<TableCount;i++)
		qsort(P1Tables[i].Table->TableData, P1Tables[i].Table->ItemCount,sizeof(TABLE_ENTRY),tableCompare);


	return (0);
}


		/*
		 | p1_exit - close this DLL
		*/

//__declspec( dllexport ) void p1_exit(void) {
void p1_exit(void) {

	/*
	 | What To Do:
	 |  1. Free all allocated storage (start with P1Tables)
	*/
	
//	int i;

//	for (i=0;i<mTableCount;i++) {
//		if ( P1Tables[i].Table->TableData != NULL )
//			free(P1Tables[i].Table->TableData);	// Rack Type list pointer
//		if ( P1Tables[i].Table != NULL )
//			free(P1Tables[i].Table);			// Super Group's pointer to data
//	}
//	if ( P1Tables != NULL)
//		free(P1Tables);							// Super Group pointer

	return;
}


		/*
		 | p1_execute - compute RackIndex given XCube, BOH, parameters, etc.
		*/

//__declspec( dllexport ) int p1_execute(
/**
 * Pass1算法核心执行函数 - 低级数学计算实现
 *
 * 这是Pass1算法的底层实现，包含：
 * 1. 超级组选择（危险品 vs 普通品）
 * 2. 回归线计算和热区调整
 * 3. 欧几里得距离计算
 * 4. 理想货架类型选择
 * 5. 可用货架类型排名
 *
 * @param XCube 扩展立方体（产品尺寸 × 移动量）
 * @param BOH 库存量（Balance On Hand）
 * @param UOI 单位订购信息
 * @param CaseCuFt 箱立方英尺
 * @param CaseMovement 箱移动量
 * @param Hazard 危险品标志（0=普通品，1=危险品）
 * @param OptFlag 优化模式（0=空间优化，1=劳动力优化）
 * @param rackIdeal 返回理想货架类型结果
 * @param rackAvail 返回可用货架类型排名数组
 * @param numAvailRanks 需要返回的排名数量
 * @param prodWidth 产品宽度
 */
void p1_execute(
	double XCube,				/* 参数：扩展立方体 */
	double BOH,					/* 参数：库存量 */
	int	UOI,					/* 参数：单位订购信息 */
	double CaseCuFt,			/* 参数：箱立方英尺 */
	double CaseMovement,		/* 参数：箱移动量 */
	int Hazard,					/* 参数：危险品标志 */
	int OptFlag,				/* 参数：优化模式 */
	struct idealResult *rackIdeal,	/* 返回值：理想货架结果 */
	struct availResult *rackAvail,	/* 返回值：可用货架排名 */
	int numAvailRanks,			/* 参数：排名数量 */
	float prodWidth)			/* 参数：产品宽度 */
{

	P1DATA *tptr;				// 指向要使用的超级组数据
	int iofs;					// 连接表中的起始偏移量
	int idx;					// 临时表索引
	int isUsable, lastUsable;	// 可用性标志
	int idx2;					// 第二个索引
	int n,j;					// 循环计数器
	bool foundNew;				// 是否找到新的匹配
	int count;					// 具有相同横坐标的表项数量
	double Rgr_Y;				// 回归线纵坐标值
	double diff, tmp, val;		// 临时计算值
	double diff2;				// 第二个差值
	int err=0;					// useSpace()函数的返回值
	int rankNum;				// 当前排名编号
	int i;						// 循环变量
	int * previousRankIndexes;	// 用于保存之前最佳可用货架索引的数组
	int btIdx;					// 最佳表索引

	// 为排名索引数组分配内存
	previousRankIndexes = (int *) malloc(sizeof(int) * numAvailRanks);

	if (previousRankIndexes == NULL)
		throw EngineException("临时数组内存分配不足。\n",
			__FILE__, __LINE__, 200);

	// 初始化排名索引数组
	for (rankNum=1; rankNum <= numAvailRanks; rankNum++)
		previousRankIndexes[rankNum-1] = RACK_NOT_FOUND;

	// 初始化返回值和工作变量
	//rackIndex->ideal = rackIndex->bestAvl = isUsable = lastUsable = RACK_NOT_FOUND;
	isUsable = lastUsable = RACK_NOT_FOUND;
	// 初始化返回结构体
	rackIdeal->facingCount = RACK_NOT_FOUND;		// 理想货架面数
	for ( n = 0; n < numAvailRanks; n++)
		rackAvail[n].facingCount = RACK_NOT_FOUND;	// 可用货架面数数组

	// 默认使用面数计算方法进行最佳可用货架计算
	mUseCube=0;

	//////////////////////////////////////////////////////////////////////////
	// 步骤1: 确定超级组 - 根据产品属性选择对应的货架组
	//////////////////////////////////////////////////////////////////////////

	// 检查危险品标志位
	if (Hazard) {
		tptr = &P1Tables[HAZARD_TABLE];			// 危险品使用危险品货架表
		iofs = 0;								// 偏移量为0
	}
	// 所有其他产品使用默认货架组（传统货架）
	else {
		tptr = &P1Tables[DEFAULT_TABLE];		// 普通产品使用默认货架表
		iofs = P1Tables[HAZARD_TABLE].Table->ItemCount;	// 偏移量为危险品表的项目数
//				+ P1Tables[CAROUSEL_TABLE].Table->ItemCount		// 旋转货架表项目数
//				+ P1Tables[PTB_TABLE].Table->ItemCount			// PTB货架表项目数
//				+ P1Tables[PIR_TABLE].Table->ItemCount;		// PIR货架表项目数
	}

	//////////////////////////////////////////////////////////////////////////
	// 步骤2: 标准处理路径 - 参数边界检查和对数转换
	//////////////////////////////////////////////////////////////////////////

	// 将数值重置到边界范围内
	if (XCube < tptr->X.low)
		XCube = tptr->X.low;		// 扩展立方体小于下限时设为下限
	else if (XCube > tptr->X.hi)
		XCube = tptr->X.hi;			// 扩展立方体大于上限时设为上限

	if (BOH < tptr->B.low)
		BOH = tptr->B.low;			// 库存量小于下限时设为下限
	else if (BOH > tptr->B.hi)
		BOH = tptr->B.hi;			// 库存量大于上限时设为上限

	// Dex和Fred决定使用以10为底的对数能在回归线上获得最佳的数值分布
	XCube = log10(XCube);			// 扩展立方体对数转换
	BOH = log10(BOH);				// 库存量对数转换


	//printf("对数转换后 XCube : %f, BOH : %f\n", XCube, BOH);

	//////////////////////////////////////////////////////////////////////////
	// 步骤3: 计算回归线纵坐标值
	// 使用线性回归公式: Y = a * X + b
	//////////////////////////////////////////////////////////////////////////
	Rgr_Y = tptr->Line.a * XCube + tptr->Line.b;

	//////////////////////////////////////////////////////////////////////////
	// 步骤4: 热区调整 - 根据优化标志调整产品参数到热区内
	//
	// 热区概念：回归线上下一定偏差范围内的区域
	// 如果BOH超出热区范围，需要根据优化标志进行调整：
	// - OptFlag==0：空间优化，倾向于选择较小的货架
	// - OptFlag==1：劳动力优化，倾向于选择较大的货架
	//////////////////////////////////////////////////////////////////////////
	if (Rgr_Y + tptr->Y.hi < BOH) {				// 产品BOH在上热区之外（高于回归线）
		if (OptFlag == 0)
					// 空间优化：通过回归线公式反推XCube，选择较小货架
					XCube = (BOH - tptr->Line.b - tptr->Y.hi) / tptr->Line.a;
		else BOH = Rgr_Y + tptr->Y.hi;			// 劳动力优化：降低BOH到热区上边界
	}
	else if (Rgr_Y - tptr->Y.low > BOH) {		// 产品BOH在下热区之外（低于回归线）
		if (OptFlag == 0)
					// 空间优化：通过回归线公式反推XCube，选择较小货架
					XCube = (BOH - tptr->Line.b - tptr->Y.low) / tptr->Line.a;
		else BOH = Rgr_Y - tptr->Y.low;			// 劳动力优化：提高BOH到热区下边界
	}

	//////////////////////////////////////////////////////////////////////////
	// 步骤5: 寻找理想货架类型（忽略设施容量限制）
	// 通过横坐标（XCube）匹配找到最接近的货架类型
	//////////////////////////////////////////////////////////////////////////

//	调试信息：显示所有货架类型的参数
//	for ( idx=0; idx<tptr->Table->ItemCount;++idx)
//		printf("索引: %d, XCube : %f, XBOH : %f, 配置ID : %d, 面数 : %d\n",
//			idx, tptr->Table->TableData[idx].logxCube, tptr->Table->TableData[idx].logxBOH,
//			tptr->Table->TableData[idx].profileID, tptr->Table->TableData[idx].facingCount);
//
	// 寻找第一个主导条目的索引（XCube小于等于表中XCube的第一个条目）
	for (idx=1; idx<tptr->Table->ItemCount; ++idx) {
		if (XCube <= tptr->Table->TableData[idx].logxCube) break;
	}

//	printf("主导索引 %d\n",idx);

	// 如果前一个横坐标（X坐标）更接近，则回退一个索引
	if (fabs(XCube - tptr->Table->TableData[idx-1].logxCube
			<= tptr->Table->TableData[idx].logxCube - XCube)) --idx;

//	printf("Back One %d\n",idx);

	// Count number of table entries with same abscissa and leave the
	// pointer on the one with the lowest BOH figure.
	count=0;
	val = tptr->Table->TableData[idx].logxCube;
	for (n = (tptr->Table->ItemCount - 1); n>=0; --n) {
		if (tptr->Table->TableData[n].logxCube == val) {
			++count;
			idx = n;
		}
	}

	// Find closest fit among matching entries 
	//rackIndex->ideal = idx;
//	printf("Idx : %d\n",idx);

	if ( idx >= 0 && idx < tptr->Table->ItemCount) {
		rackIdeal->facingCount = tptr->Table->TableData[idx].facingCount;
		rackIdeal->idealProfileID = tptr->Table->TableData[idx].profileID;
	}

	diff = fabs(tptr->Table->TableData[idx].logxBOH - BOH);

	for (n=1; n<count; ++n) {
		tmp = fabs(tptr->Table->TableData[(idx+n)].logxBOH - BOH);
		if (tmp < diff) {
			rackIdeal->facingCount = tptr->Table->TableData[idx + n].facingCount;
			rackIdeal->idealProfileID = tptr->Table->TableData[idx + n].profileID;
			diff = tmp;
		}
	}

//	printf("Idx 2 : %d, Prof : %d, face : %d\n",idx,rackIdeal->idealProfileID,rackIdeal->facingCount);

	//////////////////////////////////////////////////////////////
	//  FINISHED LOOKING FOR IDEAL RACKTYPE
	//////////////////////////////////////////////////////////////

	//////////////////////////////////////////////////////////////
	//  New approach for Best Available :  Because of the fact
	//  that we are not sure how many of a particular profile will
	//  available for P3, the best availables will be selecting
	//  only out of what is in the facility, and not decrementing
	//  the number still available.  The user will see a generalized
	//  statement of how many extra of a profile are needed if not
	//  enough of the primary best available are in the facility, 
	//  but each product will get a ranked set of best available 
	//  profiles regardless of how many are in the facility.
	//////////////////////////////////////////////////////////////

	// Find index of first dominating entry 
	for (idx=0; idx<tptr->Table->ItemCount; ++idx) {

		// If is not in the facility, skip.
		if (!(isInFacility(tptr,idx))) continue;


		// We didn't get to this part of the loop unless this is a usable
		// basic type, so set isUsable so we know at least one valid type
		// exists.  We will use this max usable element in case we don't
		// find a stopping place.
		isUsable = idx;

		// As soon as XCube value is EXceed(c) -ed by regression value, exit.
		if (XCube <= tptr->Table->TableData[idx].logxCube) {
			for (i = numAvailRanks-1; i > 0; i--)
				previousRankIndexes[i] = previousRankIndexes[i-1];
			previousRankIndexes[0] = idx;
			//rackAvail[rankNum-1].facingID = idx;
			break;
		}

		// We will use this max usable element in case we never find a
		// stopping place.
		lastUsable = isUsable;
	}

	// If isUsable still has default value, there are no basic types with
	// avialable locations in the Super Group.
	if (isUsable==RACK_NOT_FOUND) {
		// Add offset value and return the indices, leaving Best Available
		// with default value of not found.  There are no unused locations
		// left in the Facility for the required Super Group
		rackAvail[rankNum-1].facingCount = -1;
		rackAvail[rankNum-1].availProfileID = -1;
		rackAvail[rankNum-1].ranking = rankNum;
		return;
	}

	// If we exited on the first usable row, lastUsable never got set.  So
	// here we make it = bestAvl, which will cause us to skip looking back
	// to compare with our last usable entry.
	if (lastUsable==RACK_NOT_FOUND) lastUsable = isUsable;

	// If we never picked a usable row, bestAvl never got set.  So here we
	// make it = isUsable, along with lastUsable, which will also cause us
	// to skip looking back to compare with our last usable entry.
	if (previousRankIndexes[0] == RACK_NOT_FOUND) {
		previousRankIndexes[0] = isUsable;
		lastUsable = isUsable;
	}
	// Unless we only found one rack type existing in the Facility,
	// compare the index we found with the last usable one and use it
	// if its abscissa is closer.
	if (previousRankIndexes[0] != lastUsable) {
		if (XCube - tptr->Table->TableData[lastUsable].logxCube
			<= tptr->Table->TableData[previousRankIndexes[0]].logxCube - XCube) {
			for (i = numAvailRanks-1; i > 0; i--)
				previousRankIndexes[i] = previousRankIndexes[i-1];
			previousRankIndexes[0] = lastUsable;
			//rackAvail[rankNum-1].facingID = lastUsable;
		}
	}

	// Count number of table entries with same abscissa
	count=0;
	val = tptr->Table->TableData[previousRankIndexes[0]].logxCube;
	for (n = previousRankIndexes[0] + 1; n < tptr->Table->ItemCount; ++n) {
		if (tptr->Table->TableData[n].logxCube == val) ++count;
	}

	// Find closest fit among matching entries 
	// WHICH ALSO exist in Facility
	// idx = rackAvail[rankNum-1].facingID;
	idx = previousRankIndexes[0];
	diff = fabs(tptr->Table->TableData[idx].logxBOH - BOH);

	for (n=1; n<=count; ++n) {
		if (!(isInFacility(tptr, (idx+n)))) continue;

		tmp = fabs(tptr->Table->TableData[idx+n].logxBOH - BOH);
		if (tmp < diff) {
			for (i = numAvailRanks-1; i > 0; i--)
				previousRankIndexes[i] = previousRankIndexes[i-1];
			previousRankIndexes[0] = idx + n;
			//rackAvail[rankNum-1].facingID = idx + n;
			diff = tmp;
		}
	}

	// Decrement available space in Facility for Best Available rack type
	err = useSpace(tptr, previousRankIndexes[0], prodWidth, UOI);

	for ( i = 1; i < numAvailRanks; i++ ) {
		if ( previousRankIndexes[i] == RACK_NOT_FOUND )
			break;
	}

	if ( i != numAvailRanks ) {
		for ( ; i < numAvailRanks; i++ ) {

			// first, find the entry with XCube less than
			
			idx2 = previousRankIndexes[i-1];
			foundNew = false;
			while ( foundNew == false && idx2 >= 0) {
				for ( j = 0; j < i; j++ ) {
					if ( tptr->Table->TableData[idx2].logxCube == tptr->Table->TableData[previousRankIndexes[j]].logxCube ) {
						idx2--;
						break; // from inner for loop
					}
				}
				if ( j == i )
					foundNew = true; //previous xCube
			}
			diff2 = 0;
			if ( idx2 >= 0 ) {
				count=0;
				diff2 = fabs(tptr->Table->TableData[idx2].logxCube - XCube);

				val = tptr->Table->TableData[idx2].logxCube;
				for (n = idx2 - 1; n >= 0; --n) {
					if (tptr->Table->TableData[n].logxCube == val) ++count;
				}
				// Find closest fit among matching entries 
				// WHICH ALSO exist in Facility
				// idx = rackAvail[rankNum-1].facingID;
				idx = idx2 - count;
				previousRankIndexes[i] = idx;
				diff = fabs(tptr->Table->TableData[idx].logxBOH - BOH);

				for (n=1; n<=count; ++n) {
					if (!(isInFacility(tptr, (idx+n)))) continue;

					tmp = fabs(tptr->Table->TableData[idx+n].logxBOH - BOH);
					if (tmp < diff) {
						previousRankIndexes[i] = idx + n;
						diff = tmp;
					}
				}
			}
			// Next, look at the entry with XCube greater than
			idx2 = previousRankIndexes[i-1];
			foundNew = false;
			while ( foundNew == false && idx2 < tptr->Table->ItemCount ) {
				for ( j = 0; j < i; j++ ) {
					if ( tptr->Table->TableData[idx2].logxCube == tptr->Table->TableData[previousRankIndexes[j]].logxCube ) {
						idx2++;
						break; // from inner for loop
					}
				}
				if ( j == i )
					foundNew = true; //next xCube
			}
			if ( ( idx2 < tptr->Table->ItemCount &&
				fabs(tptr->Table->TableData[idx2].logxCube - XCube) < diff2 ) || 
				(idx2 < tptr->Table->ItemCount && diff2 == 0) ) {
				// only if this XCube is closer to the actual than the previous step
				count=0;
				
				val = tptr->Table->TableData[idx2].logxCube;
				for (n = idx2 + 1; n < tptr->Table->ItemCount; ++n) {
					if (tptr->Table->TableData[n].logxCube == val) ++count;
				}
				// Find closest fit among matching entries 
				// WHICH ALSO exist in Facility
				// idx = rackAvail[rankNum-1].facingID;
				idx = idx2;
				previousRankIndexes[i] = idx;
				diff = fabs(tptr->Table->TableData[idx].logxBOH - BOH);

				for (n=1; n<=count; ++n) {
					if (!(isInFacility(tptr, (idx+n)))) continue;

					tmp = fabs(tptr->Table->TableData[idx+n].logxBOH - BOH);
					if (tmp < diff) {
						previousRankIndexes[i] = idx + n;
						diff = tmp;
					}
				}
			}
		}

	}

//	printf("Idx 3 : %d, Prof : %d, face : %d\n",idx,rackIdeal->idealProfileID,rackIdeal->facingCount);

	for ( rankNum = 1; rankNum <= numAvailRanks; rankNum++ ) {
		if ( previousRankIndexes[rankNum-1] == RACK_NOT_FOUND && rankNum != 1)
			previousRankIndexes[rankNum-1] = previousRankIndexes[rankNum-2];
		rackAvail[rankNum-1].facingCount = tptr->Table->TableData[previousRankIndexes[rankNum-1]].facingCount;
		rackAvail[rankNum-1].availProfileID = tptr->Table->TableData[previousRankIndexes[rankNum-1]].profileID;
		rackAvail[rankNum-1].ranking = rankNum;
//		printf("Idx 4 : %d, Avail Prof : %d, face : %d\n",idx,rackAvail[rankNum-1].availProfileID,rackAvail[rankNum-1].facingCount);
		btIdx=getBasicType(tptr->Table->TableData[previousRankIndexes[rankNum-1]].profileID);
		// Adjust count of Facings
		// all fixed facings
		if (P1BasicRack[btIdx].startLinealFacings == 0 || UOI == 3) { 
			rackAvail[rankNum-1].linealFacingCount = 0;
			rackAvail[rankNum-1].linealFacingWidth = 0;
		}
		// all variable facings
		else if (P1BasicRack[btIdx].startFixedFaces == 0) {
			rackAvail[rankNum-1].linealFacingWidth =  tptr->Table->TableData[previousRankIndexes[rankNum-1]].facingCount * prodWidth;
			rackAvail[rankNum-1].linealFacingCount =  tptr->Table->TableData[previousRankIndexes[rankNum-1]].facingCount;
		}
		// have a mix of variable and fixed facings : split facings down the middle
		else {
			if ( ( tptr->Table->TableData[n].facingCount % 2 ) == 0) {
				rackAvail[rankNum-1].linealFacingCount = tptr->Table->TableData[n].facingCount/2;
				rackAvail[rankNum-1].linealFacingWidth = tptr->Table->TableData[n].facingCount/2 * prodWidth;
			}
			else {
				rackAvail[rankNum-1].linealFacingCount= tptr->Table->TableData[n].facingCount/2 + 1;
				rackAvail[rankNum-1].linealFacingWidth = (tptr->Table->TableData[n].facingCount/2+1) * prodWidth;
			}
		}
	}

//	printf("Idx 2 : %d, Prof : %d, face : %d\n",idx,rackIdeal->idealProfileID,rackIdeal->facingCount);
	free(previousRankIndexes);
	return;
}

// Returns 1=True if there are Racks of this profile in
// facility
int isInFacility(P1DATA *superGroup, int n)
{
	int btIdx;			// index of Basic Type
	//double totalReq;	// sum of selection and Reserve required in Cube calc.

	// Look up available count from Basic Types
	btIdx=getBasicType(superGroup->Table->TableData[n].profileID);

	// If no Basic Type data was passed, skip.
	if (btIdx==RACK_NOT_FOUND) return 0;
	else {
		if ( P1BasicRack[btIdx].inFacility == 1 )
			return 1;
		else
			return 0;
	}
}	// End of isAvailable()

// Returns the index of the basicTy matching the dbID passed in
int getBasicType(int n)
{
	int i;

	// If no Basic Type information was passed, return code for N/A
	if (mNumBasicTypes==0)
		return RACK_NOT_FOUND;

	// Find the matching index, if any
	for (i=0; i<mNumBasicTypes; i++) {
		if (n == P1BasicRack[i].profileID) {
			return i;
		}
	}

	return RACK_NOT_FOUND;					// No match.

}	// End of getBasicType()


// Decrements available space in Facility for Best Available rack type
int useSpace(P1DATA *superGroup, int n, float prodWidth, int UOI)
{
	int btIdx;			// index of Basic Type
	double totalReq;	// sum of selection and Reserve required in Cube calc.

	// Look up available count from Basic Types
	btIdx=getBasicType(superGroup->Table->TableData[n].profileID);

	// If no Basic Type data was passed, skip.
	if (btIdx==RACK_NOT_FOUND) return 1;	// Error

	// Adjust count of Facings
	// all fixed facings
	if (P1BasicRack[btIdx].startLinealFacings == 0) 
		P1BasicRack[btIdx].avlFixedFaces -= superGroup->Table->TableData[n].facingCount;
	// all variable facings
	else if (P1BasicRack[btIdx].startFixedFaces == 0)
		P1BasicRack[btIdx].avlLinealFacings -= superGroup->Table->TableData[n].facingCount * prodWidth;
	// have a mix of variable and fixed facings : split facings down the middle
	else {
		if ( UOI == 3 ) // pallet handling of product.  Must use fixed facings
			P1BasicRack[btIdx].avlFixedFaces -= superGroup->Table->TableData[n].facingCount;
		else {
			if ( ( superGroup->Table->TableData[n].facingCount % 2 ) == 0) {
				P1BasicRack[btIdx].avlFixedFaces -= superGroup->Table->TableData[n].facingCount/2;
				P1BasicRack[btIdx].avlLinealFacings -= superGroup->Table->TableData[n].facingCount/2 * prodWidth;
			}
			else {
				P1BasicRack[btIdx].avlFixedFaces -= superGroup->Table->TableData[n].facingCount/2 + 1;
				P1BasicRack[btIdx].avlLinealFacings -= (superGroup->Table->TableData[n].facingCount/2+1) * prodWidth;
			}
		}
	}
	if ( P1BasicRack[btIdx].avlFixedFaces < 0 || P1BasicRack[btIdx].avlLinealFacings < 0 )
		P1BasicRack[btIdx].countNeeded += superGroup->Table->TableData[n].facingCount;
	return 0;
}	// End of useSpace()

int tableCompare(const void * p1, const void * p2) {

	if ( ((TABLE_ENTRY*)p1)->logxCube < ((TABLE_ENTRY*)p2)->logxCube )
		return -1;
	else if ( ((TABLE_ENTRY*)p1)->logxCube > ((TABLE_ENTRY*)p2)->logxCube )
		return 1;
	else {
		if ( ((TABLE_ENTRY*)p1)->logxBOH < ((TABLE_ENTRY*)p2)->logxBOH )
			return -1;
		else if ( ((TABLE_ENTRY*)p1)->logxBOH > ((TABLE_ENTRY*)p2)->logxBOH )
			return 1;
		else
			return 0;
	}
	return 0;
}

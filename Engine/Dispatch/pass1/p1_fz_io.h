#ifndef P1_FZ_IO
#define P1_FZ_IO

/* ********************************************************* */
/* This object will demonstrate the ability to load a fuzzy  */
/* definition file and the cbc runtime dll to execute the    */
/* pass 4 cost evaluation.  We will abstract this file into  */
/* a generic fuzzy object as a later step.                   */
/* ********************************************************* */

#include "../core/socket_class.h"
#include <windows.h>
#include "../core/wfz.h"
#include "p1struct.h"
#include "p1.h"

#define P1_INPUT_COUNT 0
#define P1_OUTPUT_COUNT 0

class P1fzio {
	public:

		P1fzio();
		P1fzio(char *fname);

		virtual ~P1fzio();

		virtual float getCost(float mvmt, float inven, float loc_type);

	protected:

		double *mem_inputs;
		double *mem_outputs;

		double mem_min;
		double mem_max;

		char *mem_FileName;

		int mem_fzChannel;

		HINSTANCE mem_fzInstance;

		int (__stdcall *mem_fzInit)(char *file);
		int (__stdcall *mem_fzClose)(int channel);
		int (__stdcall *mem_fzEval)(int channel, FZ_NUMERIC *input,
			FZ_NUMERIC *output);

};

#endif /* P1_FZ_IO defined */ 	

class PassOne : public P1fzio
{
	public:
		PassOne();
		virtual ~PassOne();

		///////////////////////////////////////////////////////////////
		// getCost():
		// Parameters:
		// ptr to product UDFs
		// ptr to array with positions of rules in UDF list
		// number of UDFs used by rule set
		//
		// Called once for each product.
		///////////////////////////////////////////////////////////////
		//virtual void getCost(udf *udfPtr, int *rulePos, int udfCount, RESULTPAIR *rackIndex);
		//virtual void OLDgetCost(prodPack *aProd, RESULTPAIR *rackIndex);

	protected:

//		SockClass	*mySocket;

};

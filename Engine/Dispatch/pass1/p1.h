//////////////////////////////////////////////////////////////////////
// Function Name :	p1.h
// Classname :		
// Description :	Header for p1.cpp Rack Assignment algorithm.
// Date Created :	~4/1/98
// Author : 		faw/mfs
//////////////////////////////////////////////////////////////////////
// Inputs :			None
// Outputs :		None
// Explanation :	Header for p1.cpp Rack Assignment algorithm.
//////////////////////////////////////////////////////////////////////
// Revisions : 
//   November 6, 1998-mfs : Standard header comments added
//////////////////////////////////////////////////////////////////////
/*******************************************************
   *
   *                       NOTICE
   *
   *  THIS SOFTWARE IS THE PROPERTY OF AND CONTAINS
   *  CONFIDENTIAL INFORMATION OF SSA GLOBAL 
   *  TECHNOLOGIES, INC., AND SHALL NOT BE COPIED,
   *  USED, NOR DISCLOSED WITHOUT EXPRESS WRITTEN
   *  AUTHORIZATION.  ALTHOUGH PUBLICATION IS NOT
   *  INTENDED, IN THE EVENT OF PUBLICATION, THE
   *  FOLLOWING NOTICE IS APPLICABLE:
   *
   *  (c) COPYRIGHT 2004 SSA GLOBAL TECHNOLOGIES, INC.
   *
   *           SSA GLOBAL TECHNOLOGIES, INC.
   *
   ********************************************************/
//////////////////////////////////////////////////////////////////////

/*
 |  P1.H  -  Definitions for Slotting Pass 1, Algorithmic Version
 |
 |  Written by: Fred A Watkins, Ph.D.
 |          Of: Hyperlogic Corporation
 |         For: SSA Global Technologies
 |          On: 23 April 1998
*/

#ifndef P1_CBC
#define P1_CBC



/*
 | table selectors
*/

#define HAZARD_TABLE	(0)
#define DEFAULT_TABLE	(1)

#define NUMBER_OF_RACKGROUPS (2)

#define RACK_NOT_FOUND (-1)


/*
 | table entry format
*/
struct table_entry {
	int		profileID;			//bayprofile ID
	int		levelType;			//different handling within a bay
	int		facingID;			//facinginfoID of bayprofile
	int		superGroup;			//0-Hazard, 4-Conventional
	int		facingCount;		//Number of faces
	double	xCube;				//extended cube of profile
	double	xBOH;				//extended balance on hand of profile
	double	logxCube;			//base 10 log of XCube value
	double	logxBOH;			//base 10 log of XBOH value;
	double	diffxCube;			// difference in rack xcube vs. prod xcube
	double	diffxBOH;			// difference in rack xboh vs. prod xboh
	double	actualDiff;			// linear distance between rack and product points
	char	desc[256];
};

typedef struct table_entry TABLE_ENTRY;

struct p1table {
	int ItemCount;			/* size of the table */
	struct table_entry *TableData;	/* the table entries */
};
typedef struct p1table P1TABLE;




/*
 | Pass parametric data
*/

struct coefficients {
	double a;				/* "x" coefficient */
	double b;				/* constant term */
};


struct bounds {
	double hi;				/* upper value */
	double low;				/* lower value */
};
typedef struct bounds BOUNDS;

struct datapair {
	double x;
	double b;
};
typedef struct datapair DATAPAIR;


struct idealResult {
	int productPackID;
	int	idealProfileID;
	int idealLevelType;
	double xCube;
	double xBOH;
	int	facingCount;
};

struct availResult {
	int productPackID;
	int availProfileID;
	int availLevelType;
	int facingCount;
	int linealFacingCount;
	int linealFacingWidth;
	double xCube;
	double xBOH;
	int ranking;
	double heightDiff;
	double diffxCube;
	double diffxBOH;
	int origHandling;		
	int actualHandling;
	int fits;
	int isDuplicate;
	int profileIdx;			// pointer to the actual profile usage
};

struct p1data {
	struct coefficients Line;	/* regression coefficients */
	BOUNDS X;					/* extrema of table XCube values */
	BOUNDS B;					/* extrema of table BOH values */
	BOUNDS Y;					/* max absolute deviation from regression line */
	P1TABLE *Table;				/* where the table data is */
};
typedef struct p1data P1DATA;


struct basicRackType {
	int profileID;			// Bay ProfileID
	int	levelType;			// different handling within a bay
	char desc[256];			// Receive and send back to avoid re-looking up
	float startLinealFacings;
	float avlLinealFacings;			// number of this type unused in current Facility
	int startFixedFaces;
	int avlFixedFaces;
	int countNeeded;
	int totFacings;
	int inFacility;
	int isHazard;
	float maxWidth;
	float maxDepth;
	float maxHeight;
	float maxWeight;
	int rejected;
	int handlingMethod;
	float productGap;
	float productSnap;
	float facingGap;
	float facingSnap;
};
typedef struct basicRackType BASIC_RACK_TYPE;

struct prodGpDrive {
	int		prodGpID;
	int		bayProfileID;
	int		isLimited;
};
typedef struct prodGpDrive PROD_GROUP_DRIVE;

#define		BAY_PROFILE_IN_PG		1
#define		NO_BAYPROFILE_IN_PG		2
#define		BAY_PROFILE_NOT_IN_PG	-1
#define		PG_NOT_LIMITED			1
#define		PG_LIMITED				0
#define		PG_NOT_FOUND			2

#endif

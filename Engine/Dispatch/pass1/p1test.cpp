#include <stdlib.h>
#include <stdio.h>
#include "P1Process.h"
#include "pass1fcn.h"

int main(void)
{

	int		dataPort;		// Location of incoming Rule Set and Product Data
	void	*dataPtr;
	void	*retPtr;

	// Test data
	dataPort = 314159;
	dataPtr = &dataPort;

	// Can't create socket without this
	init_winsock();

	retPtr = pass1threadstart(dataPtr);

	// Clean up socket stuff
	fini_winsock();

	return 0;
}

//#ifndef PASS_2_RESULT
//#define PASS_2_RESULT
//
//typedef struct {
//	prodPack	*prod;
//	slotGroup	*group;
//	float	score;
//} p2Res;
//
//#endif /* PASS_2_RESULT Defined */
//
//slotGroup	*slotGp;
/*
// Move data from input into a Slotting Group structure
int build_slot_group(int n)
{
	char		*sub_str;
	float		temp_flt;
	int			temp_int;
	int			i;

	udf			tempUDF;		// temporary space where UDF is built before
								// being copied into Slotting Group's UDF area

	// Build a Slotting Group structure
	sub_str = strtok(NULL, DATA_DELIMITER);
	printf( "* Slotting Group: %s\n", sub_str );
	memcpy( slotGp[n].className, sub_str, ( strlen(sub_str) + 1) );

	sub_str = strtok(NULL, DATA_DELIMITER);
	temp_flt = (float)atof( sub_str );
	memcpy( &slotGp[n].avgHeight, &temp_flt, sizeof(float) );

	sub_str = strtok(NULL, DATA_DELIMITER);
	temp_flt = (float)atof( sub_str );
	memcpy( &slotGp[n].avgWidth, &temp_flt, sizeof(float) );

	sub_str = strtok(NULL, DATA_DELIMITER);
	temp_flt = (float)atof( sub_str );
	memcpy( &slotGp[n].avgDepth, &temp_flt, sizeof(float) );

	sub_str = strtok(NULL, DATA_DELIMITER);
	temp_flt = (float)atof( sub_str );
	memcpy( &slotGp[n].avgWeight, &temp_flt, sizeof(float) );

	sub_str = strtok(NULL, DATA_DELIMITER);
	temp_flt = (float)atof( sub_str );
	memcpy( &slotGp[n].maxHeight, &temp_flt, sizeof(float) );

	sub_str = strtok(NULL, DATA_DELIMITER);
	temp_flt = (float)atof( sub_str );
	memcpy( &slotGp[n].maxWidth, &temp_flt, sizeof(float) );

	sub_str = strtok(NULL, DATA_DELIMITER);
	temp_flt = (float)atof( sub_str );
	memcpy( &slotGp[n].maxDepth, &temp_flt, sizeof(float) );

	sub_str = strtok(NULL, DATA_DELIMITER);
	temp_flt = (float)atof( sub_str );
	memcpy( &slotGp[n].maxWeight, &temp_flt, sizeof(float) );

	sub_str = strtok(NULL, DATA_DELIMITER);
	temp_int = (int)atoi( sub_str );
	memcpy( &slotGp[n].udfCount, &temp_int, sizeof(int) );

	// Point to new memory area for UDFs
	slotGp[n].udf = (udf *)malloc(sizeof(udf) * slotGp[n].udfCount);
	if ( slotGp[n].udf == NULL ) {
		printf( "Insufficient memory available for Slotting Group UDF list.\n" );
		return 4;
	}

	// The next n lines should be the Slotting Group's UDFs
	holdUDF = slotGp[n].udf;
	for (i=0;i<slotGp[n].udfCount;i++) {

		// Read the next line
		if( fgets( line, MAX_INPUT_DATA_LEN, stream ) == NULL) {
			printf( "fgets() error!\n" );
			return 5;
		}

		// It has to be a UDF line to make sense
		sub_str = strtok(line, DATA_DELIMITER);
		switch ( *sub_str ) {
			case 'U':
				// OK
				break;
			default:
				printf( "Incorrect number of UDFs for Slotting Group %s!\n", slotGp[n].className);
				return 6;
		}

		// Copy the stream input into a temp UDF structure
		sub_str = strtok(NULL, DATA_DELIMITER);
		memcpy( &tempUDF.udfName, sub_str, ( strlen(sub_str) + 1 ) );

		sub_str = strtok(NULL, DATA_DELIMITER);
		memcpy( &tempUDF.charVal, sub_str, ( strlen(sub_str) + 1 ) );

		sub_str = strtok(NULL, DATA_DELIMITER);
		temp_flt = (float)atof( sub_str );
		memcpy( &tempUDF.floatVal, &temp_flt, sizeof(float) );

		sub_str = strtok(NULL, DATA_DELIMITER);
		temp_int = (int)atoi( sub_str );
		memcpy( &tempUDF.intVal, &temp_int, sizeof(int) );

		sub_str = strtok(NULL, DATA_DELIMITER);
		temp_int = (int)atoi( sub_str );
		memcpy( &tempUDF.type, &temp_int, sizeof(int) );

		// Now copy the temp structure over to where we want it
		memcpy( slotGp[n].udf, &tempUDF, sizeof(udf) );
	}

	// No errors
	return 0;

}	// End of build_slot_group()
*/
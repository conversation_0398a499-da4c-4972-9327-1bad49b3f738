# SLOT系统技术架构图表

## 1. 系统整体架构图

```mermaid
graph TB
    subgraph "客户端层 (Client Layer)"
        A1[MFC客户端<br/>C++ Desktop App]
        A2[Web客户端<br/>React + TypeScript]
    end
    
    subgraph "API网关层 (API Gateway)"
        B1[Nginx<br/>负载均衡]
        B2[Spring Cloud Gateway<br/>API路由]
    end
    
    subgraph "业务服务层 (Business Service Layer)"
        C1[传统Server<br/>C++ Socket Server]
        C2[现代Server<br/>Spring Boot REST API]
    end
    
    subgraph "优化引擎层 (Optimization Engine)"
        D1[传统Engine<br/>C++ Algorithms]
        D2[现代Engine<br/>Java Spring Boot]
    end
    
    subgraph "数据访问层 (Data Access Layer)"
        E1[数据库抽象<br/>DB Router/Session]
        E2[JPA/Hibernate<br/>ORM Framework]
    end
    
    subgraph "数据存储层 (Data Storage)"
        F1[(SQL Server<br/>传统数据库)]
        F2[(MySQL<br/>现代数据库)]
        F3[(Redis<br/>缓存)]
    end
    
    A1 --> B1
    A2 --> B2
    B1 --> C1
    B2 --> C2
    C1 --> D1
    C2 --> D2
    C1 --> E1
    C2 --> E2
    E1 --> F1
    E2 --> F2
    E2 --> F3
    D1 --> E1
    D2 --> E2
```

## 2. 传统C++架构详细图

```mermaid
graph TB
    subgraph "Client Layer (MFC/AutoCAD)"
        A1[OptConfig<br/>配置管理]
        A2[Reports<br/>报表生成]
        A3[SsaGraphSession<br/>图形会话]
        A4[arx_modal<br/>AutoCAD集成]
    end
    
    subgraph "Server Layer (C++)"
        B1[Session Manager<br/>会话管理]
        B2[Data Administrator<br/>数据管理]
        B3[Data View<br/>数据视图]
        B4[Pass Managers<br/>优化管理器]
    end
    
    subgraph "Engine Layer (C++)"
        C1[Pass1 Manager<br/>货架类型选择]
        C2[Pass3 Manager<br/>空间分析]
        C3[Pass4 Manager<br/>位置分配]
        C4[Pass5 Manager<br/>最终优化]
    end
    
    subgraph "Data Access Layer"
        D1[DB Router<br/>数据库路由]
        D2[DB Session<br/>数据库会话]
        D3[DB DataSet<br/>数据集]
    end
    
    subgraph "Storage Layer"
        E1[(SQL Server<br/>主数据库)]
    end
    
    A1 --> B1
    A2 --> B2
    A3 --> B3
    A4 --> B4
    B1 --> C1
    B2 --> C2
    B3 --> C3
    B4 --> C4
    C1 --> D1
    C2 --> D2
    C3 --> D3
    D1 --> E1
    D2 --> E1
    D3 --> E1
```

## 3. 现代Java架构详细图

```mermaid
graph TB
    subgraph "Web Client (React)"
        A1[Pages<br/>页面组件]
        A2[Components<br/>可复用组件]
        A3[Services<br/>API服务]
        A4[Visualization<br/>3D可视化]
    end
    
    subgraph "Server Layer (Spring Boot)"
        B1[Controllers<br/>REST API]
        B2[Services<br/>业务服务]
        B3[Repositories<br/>数据访问]
        B4[Entities<br/>JPA实体]
    end
    
    subgraph "Engine Layer (Spring Boot)"
        C1[Algorithm Services<br/>算法服务]
        C2[Math Services<br/>数学计算]
        C3[Optimization Services<br/>优化服务]
        C4[Engine Controllers<br/>引擎API]
    end
    
    subgraph "Data Layer"
        D1[JPA/Hibernate<br/>ORM框架]
        D2[Spring Data<br/>数据访问]
        D3[Redis Template<br/>缓存访问]
    end
    
    subgraph "Storage Layer"
        E1[(MySQL<br/>主数据库)]
        E2[(Redis<br/>缓存数据库)]
    end
    
    A1 --> B1
    A2 --> B2
    A3 --> B3
    A4 --> B4
    B1 --> C1
    B2 --> C2
    B3 --> C3
    B4 --> C4
    C1 --> D1
    C2 --> D2
    C3 --> D3
    D1 --> E1
    D2 --> E1
    D3 --> E2
```

## 4. 优化算法流程图

```mermaid
flowchart TD
    A[产品数据输入] --> B[数据验证]
    B --> C[立方体计算]
    C --> D[基础立方体]
    C --> E[扩展立方体]
    C --> F[库存立方体]
    
    D --> G[Pass1算法]
    E --> G
    F --> H[Pass4算法]
    
    G --> I[货架类型选择]
    H --> J[位置分配]
    
    I --> K[约束检查]
    J --> K
    
    K --> L{是否满足约束?}
    L -->|否| M[参数调整]
    M --> G
    L -->|是| N[成本计算]
    
    N --> O[优化方案输出]
    O --> P[可视化展示]
    O --> Q[报表生成]
```

## 5. 数据流架构图

```mermaid
graph LR
    subgraph "数据输入"
        A1[产品信息<br/>尺寸/重量]
        A2[移动量数据<br/>历史统计]
        A3[设施布局<br/>货架信息]
        A4[库存数据<br/>当前库存]
    end
    
    subgraph "数据处理"
        B1[数据清洗<br/>验证]
        B2[立方体计算<br/>算法]
        B3[回归分析<br/>相关性]
        B4[优化算法<br/>Pass1-5]
    end
    
    subgraph "结果输出"
        C1[优化方案<br/>货架分配]
        C2[成本分析<br/>效益评估]
        C3[可视化图表<br/>3D展示]
        C4[实施报告<br/>详细说明]
    end
    
    A1 --> B1
    A2 --> B1
    A3 --> B1
    A4 --> B1
    
    B1 --> B2
    B2 --> B3
    B3 --> B4
    
    B4 --> C1
    B4 --> C2
    B4 --> C3
    B4 --> C4
```

## 6. 微服务架构图

```mermaid
graph TB
    subgraph "前端服务"
        A1[Web Portal<br/>React App]
        A2[Mobile App<br/>React Native]
    end
    
    subgraph "API网关"
        B1[Spring Cloud Gateway<br/>路由/认证/限流]
    end
    
    subgraph "核心业务服务"
        C1[Facility Service<br/>设施管理]
        C2[Product Service<br/>产品管理]
        C3[Optimization Service<br/>优化服务]
        C4[Report Service<br/>报表服务]
    end
    
    subgraph "算法引擎服务"
        D1[Pass1 Service<br/>货架选择]
        D2[Pass4 Service<br/>位置分配]
        D3[Regression Service<br/>回归分析]
        D4[Cost Service<br/>成本计算]
    end
    
    subgraph "基础设施服务"
        E1[Config Service<br/>配置中心]
        E2[Discovery Service<br/>服务发现]
        E3[Monitor Service<br/>监控服务]
    end
    
    subgraph "数据存储"
        F1[(MySQL Cluster<br/>主数据库)]
        F2[(Redis Cluster<br/>缓存)]
        F3[(MongoDB<br/>日志存储)]
    end
    
    A1 --> B1
    A2 --> B1
    B1 --> C1
    B1 --> C2
    B1 --> C3
    B1 --> C4
    
    C3 --> D1
    C3 --> D2
    C3 --> D3
    C3 --> D4
    
    C1 --> F1
    C2 --> F1
    C3 --> F2
    C4 --> F1
    
    D1 --> F2
    D2 --> F2
    D3 --> F2
    D4 --> F2
    
    E1 --> E2
    E2 --> E3
    E3 --> F3
```

## 7. 部署架构图

```mermaid
graph TB
    subgraph "负载均衡层"
        A1[Nginx<br/>负载均衡器]
        A2[HAProxy<br/>备用LB]
    end
    
    subgraph "应用服务层"
        B1[Web Server 1<br/>Spring Boot]
        B2[Web Server 2<br/>Spring Boot]
        B3[Engine Server 1<br/>算法引擎]
        B4[Engine Server 2<br/>算法引擎]
    end
    
    subgraph "数据服务层"
        C1[MySQL Master<br/>主数据库]
        C2[MySQL Slave<br/>从数据库]
        C3[Redis Master<br/>缓存主节点]
        C4[Redis Slave<br/>缓存从节点]
    end
    
    subgraph "监控服务层"
        D1[Prometheus<br/>监控收集]
        D2[Grafana<br/>监控展示]
        D3[ELK Stack<br/>日志分析]
    end
    
    A1 --> B1
    A1 --> B2
    A2 --> B3
    A2 --> B4
    
    B1 --> C1
    B2 --> C2
    B3 --> C3
    B4 --> C4
    
    B1 --> D1
    B2 --> D1
    B3 --> D1
    B4 --> D1
    
    D1 --> D2
    D1 --> D3
```

## 8. 安全架构图

```mermaid
graph TB
    subgraph "外部访问"
        A1[用户浏览器]
        A2[移动应用]
        A3[第三方系统]
    end
    
    subgraph "安全边界"
        B1[WAF<br/>Web应用防火墙]
        B2[SSL/TLS<br/>加密传输]
        B3[API Gateway<br/>认证授权]
    end
    
    subgraph "认证授权"
        C1[OAuth2/JWT<br/>令牌认证]
        C2[RBAC<br/>角色权限]
        C3[Spring Security<br/>安全框架]
    end
    
    subgraph "应用安全"
        D1[输入验证<br/>参数校验]
        D2[SQL注入防护<br/>预编译语句]
        D3[XSS防护<br/>输出编码]
        D4[CSRF防护<br/>令牌验证]
    end
    
    subgraph "数据安全"
        E1[数据加密<br/>敏感信息]
        E2[访问控制<br/>数据权限]
        E3[审计日志<br/>操作记录]
    end
    
    A1 --> B1
    A2 --> B2
    A3 --> B3
    
    B1 --> C1
    B2 --> C2
    B3 --> C3
    
    C1 --> D1
    C2 --> D2
    C3 --> D3
    
    D1 --> E1
    D2 --> E2
    D3 --> E3
    D4 --> E3
```

## 9. 技术栈对比图

```mermaid
graph LR
    subgraph "传统技术栈"
        A1[C++<br/>核心语言]
        A2[MFC<br/>UI框架]
        A3[AutoCAD<br/>CAD集成]
        A4[SQL Server<br/>数据库]
        A5[Socket<br/>通信协议]
    end
    
    subgraph "现代技术栈"
        B1[Java 17<br/>核心语言]
        B2[React<br/>前端框架]
        B3[Three.js<br/>3D可视化]
        B4[MySQL<br/>数据库]
        B5[REST/WebSocket<br/>通信协议]
    end
    
    A1 -.-> B1
    A2 -.-> B2
    A3 -.-> B3
    A4 -.-> B4
    A5 -.-> B5
```

## 10. 性能监控架构图

```mermaid
graph TB
    subgraph "应用层监控"
        A1[Spring Actuator<br/>应用指标]
        A2[Micrometer<br/>指标收集]
        A3[Custom Metrics<br/>业务指标]
    end
    
    subgraph "基础设施监控"
        B1[Node Exporter<br/>系统指标]
        B2[MySQL Exporter<br/>数据库指标]
        B3[Redis Exporter<br/>缓存指标]
    end
    
    subgraph "监控存储"
        C1[Prometheus<br/>时序数据库]
        C2[InfluxDB<br/>备用存储]
    end
    
    subgraph "可视化展示"
        D1[Grafana<br/>监控面板]
        D2[Kibana<br/>日志分析]
    end
    
    subgraph "告警通知"
        E1[AlertManager<br/>告警管理]
        E2[Email/SMS<br/>通知渠道]
    end
    
    A1 --> C1
    A2 --> C1
    A3 --> C1
    B1 --> C1
    B2 --> C1
    B3 --> C2
    
    C1 --> D1
    C2 --> D2
    
    C1 --> E1
    E1 --> E2
```

这些架构图表全面展示了SLOT系统的技术架构，包括传统和现代两套技术栈的设计思路、数据流向、部署方式和安全考虑。通过这些图表，可以清晰地理解系统的整体架构和各个组件之间的关系。

# Pass1算法详解 - 每一步具体逻辑说明

## 算法概述

Pass1算法是SLOT（Slotting Optimization）系统的核心算法，负责为每个产品选择最优的货架类型。算法基于回归分析和欧几里得距离计算，通过数学模型实现智能货架匹配。

## 算法执行流程

### 步骤1: 数据检索和初始化
**文件位置**: `Server/SLOTPass1Manager.cpp` - RunPass()方法

**具体逻辑**:
1. **产品数据检索**: 从数据库获取产品基本信息
   - 产品描述、尺寸、重量
   - 移动量（Movement）和库存量（BOH）
   - 危险品标志、优化方式标志

2. **扩展立方体计算**: 
   ```cpp
   // 扩展立方体 = 产品立方体 × 移动量
   aProd->xCube = aProd->cube * aProd->movement;
   ```

3. **扩展库存量计算**:
   ```cpp
   // 扩展库存量 = 库存量 × 移动量  
   aProd->xBOH = aProd->BOH * aProd->movement;
   ```

**业务意义**: 扩展立方体和扩展库存量是Pass1算法的核心参数，它们将产品的物理属性与业务活跃度结合，为后续的货架匹配提供数学基础。

### 步骤2: 规则加载和货架类型初始化
**文件位置**: `Server/SLOTPass1Manager.cpp` - RunPass()方法

**具体逻辑**:
1. **加载Pass1规则**: 包括回归参数、边界值、优化策略
2. **初始化货架类型数据**: 加载所有可用的货架类型配置
3. **建立超级组**: 
   - 危险品组（HAZARD_TABLE）
   - 普通品组（DEFAULT_TABLE）

**业务意义**: 建立算法运行的基础数据环境，确保每个产品都能在正确的货架组中进行匹配。

### 步骤3: 超级组选择
**文件位置**: `Engine/Dispatch/pass1/p1process.cpp` - p1_execute()方法

**具体逻辑**:
```cpp
// 根据产品危险品属性选择对应的货架组
if (aProd->hazard == 1)
    tptr = &rackGp[HAZARD_TABLE];     // 危险品使用危险品货架组
else
    tptr = &rackGp[DEFAULT_TABLE];    // 普通产品使用默认货架组
```

**业务意义**: 危险品和普通品需要不同的存储策略，通过超级组分离确保安全性和合规性。

### 步骤4: 回归分析计算
**文件位置**: `Engine/Dispatch/pass1/p1process.cpp` - 回归计算部分

**具体逻辑**:
1. **累加计算**:
   ```cpp
   for (n=0; n<rackGp[i].Table->ItemCount; n++) {
       sum_x += rackGp[i].Table->TableData[n].logxCube;      // ΣX
       sum_y += rackGp[i].Table->TableData[n].logxBOH;       // ΣY  
       sum_xy += (logxCube * logxBOH);                       // ΣXY
       sumsq_x += (logxCube * logxCube);                     // ΣX²
   }
   ```

2. **回归系数计算**:
   ```cpp
   // 使用最小二乘法公式
   val = ItemCount * sumsq_x - sum_x * sum_x;
   a_coeff = (ItemCount * sum_xy - sum_x * sum_y) / val;     // 斜率
   b_coeff = (sumsq_x * sum_y - sum_x * sum_xy) / val;       // 截距
   ```

3. **热区边界计算**:
   ```cpp
   // 计算每个点到回归线的偏差
   val = logxCube * a_coeff + b_coeff;  // 回归线预测值
   deviation = logxBOH - val;           // 实际偏差
   ```

**业务意义**: 回归分析建立了产品参数与货架类型之间的数学关系，热区定义了最优匹配的参数范围。

### 步骤5: 产品参数调整（热区调整）
**文件位置**: `Engine/Dispatch/pass1/p1process.cpp` - adjustProductValues()方法

**具体逻辑**:
1. **边界检查**:
   ```cpp
   // 确保参数在允许范围内
   if (XCube < tptr->X.low) XCube = tptr->X.low;
   if (XCube > tptr->X.hi) XCube = tptr->X.hi;
   if (BOH < tptr->B.low) BOH = tptr->B.low;
   if (BOH > tptr->B.hi) BOH = tptr->B.hi;
   ```

2. **对数转换**:
   ```cpp
   // 根据度量系统决定是否进行对数转换
   if (measureSys != "Metric") {
       XCube = log10(XCube);  // 英制系统使用对数转换
       BOH = log10(BOH);
   }
   ```

3. **热区调整**:
   ```cpp
   Rgr_Y = tptr->Line.a * XCube + tptr->Line.b;  // 计算回归线纵坐标
   
   if (Rgr_Y + tptr->Y.hi < BOH) {  // 产品BOH在上热区之外
       if (aProd->optBy == 1) {
           // 劳动力优化：增大XCube选择更大货架
           XCube = (BOH - tptr->Line.b - tptr->Y.hi) / tptr->Line.a;
       } else {
           // 空间优化：降低BOH到热区上边界
           BOH = Rgr_Y + tptr->Y.hi;
       }
   }
   ```

**业务意义**: 热区调整确保产品参数在最优匹配范围内，根据优化策略（空间vs劳动力）选择不同的调整方向。

### 步骤6: 距离计算和货架匹配
**文件位置**: `Engine/Dispatch/pass1/p1process.cpp` - p1_execute()方法

**具体逻辑**:
```cpp
// 计算产品与所有货架类型的欧几里得距离
for (idx=0; idx < tptr->Table->ItemCount; ++idx) {
    profiles[idx].diffxCube = fabs(XCube - profiles[idx].logxCube);
    profiles[idx].diffxBOH = fabs(BOH - profiles[idx].logxBOH);
    
    // 计算加权欧几里得距离
    profiles[idx].actualDiff = fabs(sqrt(
        distFactor * pow(XCube - profiles[idx].logxCube, 2) +
        pow(BOH - profiles[idx].logxBOH, 2)
    ));
}
```

**业务意义**: 通过多维距离计算找到与产品参数最匹配的货架类型，距离越小表示匹配度越高。

### 步骤7: 理想货架类型选择
**文件位置**: `Engine/Dispatch/pass1/p1.cpp` - p1_execute()方法

**具体逻辑**:
```cpp
// 寻找横坐标最接近的货架类型
for (idx=1; idx<tptr->Table->ItemCount; ++idx) {
    if (XCube <= tptr->Table->TableData[idx].logxCube) break;
}

// 如果前一个横坐标更接近，则回退一个索引
if (fabs(XCube - tptr->Table->TableData[idx-1].logxCube) <= 
    fabs(tptr->Table->TableData[idx].logxCube - XCube)) {
    --idx;
}
```

**业务意义**: 理想货架类型是纯数学意义上的最佳匹配，不考虑设施容量限制。

### 步骤8: 可用货架类型排名
**文件位置**: `Engine/Dispatch/pass1/p1process.cpp` - p1_execute()方法

**具体逻辑**:
1. **按距离排序**: 将所有货架类型按欧几里得距离排序
2. **容量检查**: 检查每个货架类型的可用容量
3. **生成排名**: 返回指定数量的可用货架类型排名

**业务意义**: 可用货架类型考虑了实际的设施容量限制，提供了实际可执行的货架分配方案。

## 算法关键参数说明

### 扩展立方体（XCube）
- **计算公式**: 产品立方体 × 移动量
- **业务含义**: 反映产品的空间需求和活跃度
- **影响因素**: 产品尺寸、业务频率

### 扩展库存量（XBOH）  
- **计算公式**: 库存量 × 移动量
- **业务含义**: 反映产品的库存压力和周转需求
- **影响因素**: 当前库存、业务频率

### 热区（Hot Zone）
- **定义**: 回归线上下一定偏差范围内的区域
- **上边界**: Rgr_Y + Y.hi
- **下边界**: Rgr_Y - Y.low
- **作用**: 定义最优匹配的参数范围

### 优化标志（OptFlag）
- **0**: 空间优化，倾向于选择较小的货架，节省存储空间
- **1**: 劳动力优化，倾向于选择较大的货架，提高操作效率

## 算法输出结果

### 理想货架类型（idealResult）
- **idealProfileID**: 理想货架类型ID
- **facingCount**: 建议的货架面数

### 可用货架类型排名（availResult[]）
- **availProfileID**: 可用货架类型ID
- **availLevelType**: 货架层级类型
- **facingCount**: 可用货架面数
- **ranking**: 排名（1为最佳）

## 算法优势

1. **数学严谨性**: 基于回归分析的科学方法
2. **业务适应性**: 支持不同的优化策略
3. **安全性**: 危险品和普通品分离处理
4. **实用性**: 考虑实际设施容量限制
5. **灵活性**: 支持英制和公制度量系统

Pass1算法通过这8个步骤，实现了从产品属性到货架类型的智能匹配，为仓库优化提供了科学的决策支持。

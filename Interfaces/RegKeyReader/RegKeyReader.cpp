#include "regkeyreader.h"
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <afxwin.h>
#include <afxcmn.h>
#include <afxtempl.h>
#include <afxext.h>
#include <afxctl.h>
#include <afxdisp.h>

C_DLL_EXPORT int read_regkey(const char * regkeyloc, 
							 const char * regkey,
							 const char * regkeyname,
							 const char * regkeyvaluefilename,
							 int		  regkeytype) {
	HKEY hRegKey;
	DWORD dwReturnLength;
	DWORD dwType = REG_SZ;
	BOOL readMe = TRUE;
	FILE * fp;
	int numTimes;

	fp = fopen(regkeyvaluefilename,"w");
	fclose(fp);

	if ( strcmp(regkeyloc, "HKEY_LOCAL_MACHINE") == 0 ) {
		if (RegOpenKeyEx(HKEY_LOCAL_MACHINE, regkey,0, KEY_READ, &hRegKey) != ERROR_SUCCESS)
			return -1;
	}
	else if ( strcmp(regkeyloc, "HKEY_CURRENT_USER") == 0) {
		if (RegOpenKeyEx(HKEY_CURRENT_USER, regkey,0, KEY_READ, &hRegKey) != ERROR_SUCCESS)
			return -1;
	}
	else if ( strcmp(regkeyloc, "HKEY_CLASSES_ROOT") == 0 ) {
		if (RegOpenKeyEx(HKEY_CLASSES_ROOT, regkey,0, KEY_READ, &hRegKey) != ERROR_SUCCESS)
			return -1;
	}
	else if ( strcmp(regkeyloc, "HKEY_USERS") == 0 ) {
		if (RegOpenKeyEx(HKEY_USERS, regkey,0, KEY_READ, &hRegKey) != ERROR_SUCCESS)
			return -1;
	}
	else if ( strcmp(regkeyloc, "HKEY_CURRENT_CONFIG") == 0) {
		if (RegOpenKeyEx(HKEY_CURRENT_CONFIG, regkey,0, KEY_READ, &hRegKey) != ERROR_SUCCESS)
			return -1;
	}
	else if ( strcmp(regkeyloc, "HKEY_DYN_DATA") == 0) {
		if (RegOpenKeyEx(HKEY_DYN_DATA, regkey,0, KEY_READ, &hRegKey) != ERROR_SUCCESS)
			return -1;
	}
	else
		return -2;

	if ( regkeytype == 1 ) {
		char tempStrValue[2048];
		memset(tempStrValue,0,2048);
		numTimes = 0;
		while (RegQueryValueEx(hRegKey, regkeyname, NULL, &dwType, (LPBYTE)tempStrValue, &dwReturnLength) != ERROR_SUCCESS && numTimes <= 100)
			numTimes++;
		if (numTimes > 100)
			readMe = FALSE;
//		else {
			fp = fopen(regkeyvaluefilename,"w");
			fprintf(fp,"%s\n",tempStrValue);
			fclose(fp);
//		}
	}
	else {
		long int tempIntValue = 0;
		numTimes = 0;
		while (RegQueryValueEx(hRegKey, regkeyname, NULL, &dwType, (LPBYTE)tempIntValue, &dwReturnLength) != ERROR_SUCCESS && numTimes <= 100)
			numTimes++;
		if (numTimes > 100)
			readMe = FALSE;
//		else {
			fp = fopen(regkeyvaluefilename,"w");
			fprintf(fp,"%d\n",tempIntValue);
			fclose(fp);
//		}
	}
	RegCloseKey(hRegKey);
	if (readMe == FALSE)
		return -1;
	else
		return 1;
}
<?xml version="1.0" encoding="Windows-1252"?>
<VisualStudioProject
	ProjectType="Visual C++"
	Version="7.10"
	Name="RegKeyReader"
	ProjectGUID="{8FF0AF73-7DB7-48E4-A247-0DA9F41EDF00}"
	SccProjectName="SAK"
	SccAuxPath="SAK"
	SccLocalPath="SAK"
	SccProvider="SAK"
	Keyword="MFCProj">
	<Platforms>
		<Platform
			Name="Win32"/>
	</Platforms>
	<Configurations>
		<Configuration
			Name="Release|Win32"
			OutputDirectory="..\..\Obj.Release"
			IntermediateDirectory="$(outdir)/Interfaces/RegkeyReader"
			ConfigurationType="2"
			UseOfMFC="2"
			ATLMinimizesCRunTimeLibraryUsage="FALSE">
			<Tool
				Name="VCCLCompilerTool"
				Optimization="2"
				InlineFunctionExpansion="1"
				PreprocessorDefinitions="WIN32;NDEBUG;_WINDOWS"
				StringPooling="TRUE"
				RuntimeLibrary="2"
				EnableFunctionLevelLinking="TRUE"
				UsePrecompiledHeader="2"
				PrecompiledHeaderFile="$(outdir)/Interfaces/RegkeyReader/RegKeyReader.pch"
				AssemblerListingLocation="$(outdir)/Interfaces/RegkeyReader/"
				ObjectFile="$(outdir)/Interfaces/RegkeyReader/"
				ProgramDataBaseFileName="$(outdir)/Interfaces/RegkeyReader/"
				WarningLevel="3"
				SuppressStartupBanner="TRUE"/>
			<Tool
				Name="VCCustomBuildTool"/>
			<Tool
				Name="VCLinkerTool"
				OutputFile="$(outdir)/bin/RegKeyReader.dll"
				LinkIncremental="1"
				SuppressStartupBanner="TRUE"
				ProgramDatabaseFile="$(outdir)/Interfaces/RegkeyReader/RegKeyReader.pdb"
				SubSystem="2"
				ImportLibrary="$(outdir)/Libraries/RegKeyReader.lib"
				TargetMachine="1"/>
			<Tool
				Name="VCMIDLTool"
				PreprocessorDefinitions="NDEBUG"
				MkTypLibCompatible="TRUE"
				SuppressStartupBanner="TRUE"
				TargetEnvironment="1"
				TypeLibraryName=".\Release/RegKeyReader.tlb"
				HeaderFileName=""/>
			<Tool
				Name="VCPostBuildEventTool"/>
			<Tool
				Name="VCPreBuildEventTool"/>
			<Tool
				Name="VCPreLinkEventTool"/>
			<Tool
				Name="VCResourceCompilerTool"
				PreprocessorDefinitions="NDEBUG"
				Culture="1033"/>
			<Tool
				Name="VCWebServiceProxyGeneratorTool"/>
			<Tool
				Name="VCXMLDataGeneratorTool"/>
			<Tool
				Name="VCWebDeploymentTool"/>
			<Tool
				Name="VCManagedWrapperGeneratorTool"/>
			<Tool
				Name="VCAuxiliaryManagedWrapperGeneratorTool"/>
		</Configuration>
		<Configuration
			Name="Debug|Win32"
			OutputDirectory="..\..\Obj.Debug"
			IntermediateDirectory="$(outdir)/Interfaces/RegkeyReader"
			ConfigurationType="2"
			UseOfMFC="2"
			ATLMinimizesCRunTimeLibraryUsage="FALSE">
			<Tool
				Name="VCCLCompilerTool"
				Optimization="0"
				PreprocessorDefinitions="WIN32;_DEBUG;_WINDOWS"
				RuntimeLibrary="3"
				UsePrecompiledHeader="2"
				PrecompiledHeaderFile="$(outdir)/Interfaces/RegkeyReader/RegKeyReader.pch"
				AssemblerListingLocation="$(outdir)/Interfaces/RegkeyReader/"
				ObjectFile="$(outdir)/Interfaces/RegkeyReader/"
				ProgramDataBaseFileName="$(outdir)/Interfaces/RegkeyReader/"
				WarningLevel="3"
				SuppressStartupBanner="TRUE"
				DebugInformationFormat="4"/>
			<Tool
				Name="VCCustomBuildTool"/>
			<Tool
				Name="VCLinkerTool"
				OutputFile="$(outdir)/bin/RegKeyReader.dll"
				LinkIncremental="1"
				SuppressStartupBanner="TRUE"
				GenerateDebugInformation="TRUE"
				ProgramDatabaseFile="$(outdir)/Interfaces/RegkeyReader/RegKeyReader.pdb"
				SubSystem="2"
				ImportLibrary="$(outdir)/Libraries/RegKeyReader.lib"
				TargetMachine="1"/>
			<Tool
				Name="VCMIDLTool"
				PreprocessorDefinitions="_DEBUG"
				MkTypLibCompatible="TRUE"
				SuppressStartupBanner="TRUE"
				TargetEnvironment="1"
				TypeLibraryName=".\Debug/RegKeyReader.tlb"
				HeaderFileName=""/>
			<Tool
				Name="VCPostBuildEventTool"/>
			<Tool
				Name="VCPreBuildEventTool"/>
			<Tool
				Name="VCPreLinkEventTool"/>
			<Tool
				Name="VCResourceCompilerTool"
				PreprocessorDefinitions="_DEBUG"
				Culture="1033"/>
			<Tool
				Name="VCWebServiceProxyGeneratorTool"/>
			<Tool
				Name="VCXMLDataGeneratorTool"/>
			<Tool
				Name="VCWebDeploymentTool"/>
			<Tool
				Name="VCManagedWrapperGeneratorTool"/>
			<Tool
				Name="VCAuxiliaryManagedWrapperGeneratorTool"/>
		</Configuration>
	</Configurations>
	<References>
	</References>
	<Files>
		<File
			RelativePath="RegKeyReader.cpp">
			<FileConfiguration
				Name="Release|Win32">
				<Tool
					Name="VCCLCompilerTool"
					Optimization="2"
					PreprocessorDefinitions=""/>
			</FileConfiguration>
			<FileConfiguration
				Name="Debug|Win32">
				<Tool
					Name="VCCLCompilerTool"
					Optimization="0"
					PreprocessorDefinitions=""/>
			</FileConfiguration>
		</File>
		<File
			RelativePath="RegKeyReader.h">
		</File>
	</Files>
	<Globals>
	</Globals>
</VisualStudioProject>

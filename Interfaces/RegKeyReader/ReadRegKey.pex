begin C EXERegKeyReader;
has property restricted = TRUE;
has property multithreaded = TRUE;

class RegistryAcceesClass
	read_regkey(input regkeyloc : pointer to char,
			input regkey : pointer to char,
			input regkeyname : pointer to char,
			input regkeyvaluefilename : pointer to char,
			input regkeytype : integer) : integer;
end;

has property
	LibraryName = 'EXERegKey';
	Extended = (ExternalSharedLibs='RegKeyReader');
end SLOTBTreeLibrary;
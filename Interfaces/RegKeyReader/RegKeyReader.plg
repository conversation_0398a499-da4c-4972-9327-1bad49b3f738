<html>
<body>
<pre>
<h1>Build Log</h1>
<h3>
--------------------Configuration: Reg<PERSON><PERSON><PERSON>eader - Win32 Release--------------------
</h3>
<h3>Command Lines</h3>
Creating temporary file "C:\DOCUME~1\nkumar\LOCALS~1\Temp\RSP15.tmp" with contents
[
/nologo /MD /W3 /GX /O2 /D "WIN32" /D "NDEBUG" /D "_WINDOWS" /D "_WINDLL" /D "_AFXDLL" /Fp"Release/RegKeyReader.pch" /YX /Fo"Release/" /Fd"Release/" /FD /c 
"D:\1naveen\Source Code\Optimize 2.2\SucceedCode\Client\RegKeyReader\RegKeyReader.cpp"
]
Creating command line "cl.exe @C:\DOCUME~1\nkumar\LOCALS~1\Temp\RSP15.tmp" 
Creating command line "link.exe /nologo /subsystem:windows /dll /incremental:no /pdb:"Release/RegKeyReader.pdb" /machine:I386 /out:"Release/RegKeyReader.dll" /implib:"Release/RegKeyReader.lib"  ".\Release\RegKeyReader.obj" "
<h3>Output Window</h3>
Compiling...
RegKeyReader.cpp
Linking...
   Creating library Release/RegKeyReader.lib and object Release/RegKeyReader.exp



<h3>Results</h3>
RegKeyReader.dll - 0 error(s), 0 warning(s)
</pre>
</body>
</html>

// ssaBtreeWinDlg.cpp : implementation file
//

#include "stdafx.h"
#include "ssaBtreeWin.h"
#include "ssaBtreeWinDlg.h"

#include "../ssaBtree.h"

#ifdef _DEBUG
#define new DEBUG_NEW
#undef THIS_FILE
static char THIS_FILE[] = __FILE__;
#endif

/////////////////////////////////////////////////////////////////////////////
// CAboutDlg dialog used for App About

class CAboutDlg : public CDialog
{
public:
	CAboutDlg();

// Dialog Data
	//{{AFX_DATA(CAboutDlg)
	enum { IDD = IDD_ABOUTBOX };
	//}}AFX_DATA

	// ClassWizard generated virtual function overrides
	//{{AFX_VIRTUAL(CAboutDlg)
	protected:
	virtual void DoDataExchange(CDataExchange* pDX);    // DDX/DDV support
	//}}AFX_VIRTUAL

// Implementation
protected:
	//{{AFX_MSG(CAboutDlg)
	//}}AFX_MSG
	DECLARE_MESSAGE_MAP()
};

CAboutDlg::CAboutDlg() : CDialog(CAboutDlg::IDD)
{
	//{{AFX_DATA_INIT(CAboutDlg)
	//}}AFX_DATA_INIT
}

void CAboutDlg::DoDataExchange(CDataExchange* pDX)
{
	CDialog::DoDataExchange(pDX);
	//{{AFX_DATA_MAP(CAboutDlg)
	//}}AFX_DATA_MAP
}

BEGIN_MESSAGE_MAP(CAboutDlg, CDialog)
	//{{AFX_MSG_MAP(CAboutDlg)
		// No message handlers
	//}}AFX_MSG_MAP
END_MESSAGE_MAP()

/////////////////////////////////////////////////////////////////////////////
// CSsaBtreeWinDlg dialog

CSsaBtreeWinDlg::CSsaBtreeWinDlg(CWnd* pParent /*=NULL*/)
	: CDialog(CSsaBtreeWinDlg::IDD, pParent)
{
	//{{AFX_DATA_INIT(CSsaBtreeWinDlg)
	m_edit1 = _T("");
	m_edit2 = _T("");
	m_edit3 = _T("");
	m_edit4 = _T("");
	m_edit5 = _T("");
	m_edit8 = _T("");
	m_edit7 = 0;
	//}}AFX_DATA_INIT
	// Note that LoadIcon does not require a subsequent DestroyIcon in Win32
	m_hIcon = AfxGetApp()->LoadIcon(IDR_MAINFRAME);
}

void CSsaBtreeWinDlg::DoDataExchange(CDataExchange* pDX)
{
	CDialog::DoDataExchange(pDX);
	//{{AFX_DATA_MAP(CSsaBtreeWinDlg)
	DDX_Text(pDX, IDC_EDIT1, m_edit1);
	DDX_Text(pDX, IDC_EDIT2, m_edit2);
	DDX_Text(pDX, IDC_EDIT3, m_edit3);
	DDX_Text(pDX, IDC_EDIT4, m_edit4);
	DDX_Text(pDX, IDC_EDIT5, m_edit5);
	DDX_Text(pDX, IDC_EDIT8, m_edit8);
	DDX_Text(pDX, IDC_EDIT7, m_edit7);
	//}}AFX_DATA_MAP
}

BEGIN_MESSAGE_MAP(CSsaBtreeWinDlg, CDialog)
	//{{AFX_MSG_MAP(CSsaBtreeWinDlg)
	ON_WM_SYSCOMMAND()
	ON_WM_PAINT()
	ON_WM_QUERYDRAGICON()
	ON_BN_CLICKED(IDC_BUTTON1, OnOpenButton)
	ON_BN_CLICKED(IDC_BUTTON2, OnLookUP)
	ON_BN_CLICKED(IDC_BUTTON3, OnSet)
	ON_EN_CHANGE(IDC_EDIT1, OnChangeEdit1)
	//}}AFX_MSG_MAP
END_MESSAGE_MAP()

/////////////////////////////////////////////////////////////////////////////
// CSsaBtreeWinDlg message handlers

BOOL CSsaBtreeWinDlg::OnInitDialog()
{
	CDialog::OnInitDialog();

	// Add "About..." menu item to system menu.

	// IDM_ABOUTBOX must be in the system command range.
	ASSERT((IDM_ABOUTBOX & 0xFFF0) == IDM_ABOUTBOX);
	ASSERT(IDM_ABOUTBOX < 0xF000);

	CMenu* pSysMenu = GetSystemMenu(FALSE);
	if (pSysMenu != NULL)
	{
		CString strAboutMenu;
		strAboutMenu.LoadString(IDS_ABOUTBOX);
		if (!strAboutMenu.IsEmpty())
		{
			pSysMenu->AppendMenu(MF_SEPARATOR);
			pSysMenu->AppendMenu(MF_STRING, IDM_ABOUTBOX, strAboutMenu);
		}
	}

	// Set the icon for this dialog.  The framework does this automatically
	//  when the application's main window is not a dialog
	SetIcon(m_hIcon, TRUE);			// Set big icon
	SetIcon(m_hIcon, FALSE);		// Set small icon
	
	// TODO: Add extra initialization here
	
	return TRUE;  // return TRUE  unless you set the focus to a control
}

void CSsaBtreeWinDlg::OnSysCommand(UINT nID, LPARAM lParam)
{
	if ((nID & 0xFFF0) == IDM_ABOUTBOX)
	{
		CAboutDlg dlgAbout;
		dlgAbout.DoModal();
	}
	else
	{
		CDialog::OnSysCommand(nID, lParam);
	}
}

// If you add a minimize button to your dialog, you will need the code below
//  to draw the icon.  For MFC applications using the document/view model,
//  this is automatically done for you by the framework.

void CSsaBtreeWinDlg::OnPaint() 
{
	if (IsIconic())
	{
		CPaintDC dc(this); // device context for painting

		SendMessage(WM_ICONERASEBKGND, (WPARAM) dc.GetSafeHdc(), 0);

		// Center icon in client rectangle
		int cxIcon = GetSystemMetrics(SM_CXICON);
		int cyIcon = GetSystemMetrics(SM_CYICON);
		CRect rect;
		GetClientRect(&rect);
		int x = (rect.Width() - cxIcon + 1) / 2;
		int y = (rect.Height() - cyIcon + 1) / 2;

		// Draw the icon
		dc.DrawIcon(x, y, m_hIcon);
	}
	else
	{
		CDialog::OnPaint();
	}
}

// The system calls this to obtain the cursor to display while the user drags
//  the minimized window.
HCURSOR CSsaBtreeWinDlg::OnQueryDragIcon()
{
	return (HCURSOR) m_hIcon;
}

void CSsaBtreeWinDlg::OnOpenButton() 
{
	char filename[1024];

	UpdateData();

	sprintf(filename, "%s", m_edit1.GetBuffer(0));
	m_edit1.ReleaseBuffer();
	// TODO: Add your control notification handler code here
	if(open_datastore(filename) < 0){
		AfxMessageBox("Error opening datastore ");
		AfxMessageBox(filename);
	}
}

void CSsaBtreeWinDlg::OnLookUP() 
{
	char data[1024];
	int len;
	UpdateData();
	// TODO: Add your control notification handler code here
	len = 1024;
	if(retObjStream(m_edit7, data, &len) < 0){
		sprintf(data, "<no element found>");
	}
	m_edit8 = data;
	UpdateData(FALSE);

}

void CSsaBtreeWinDlg::OnSet() 
{
	char data[1024];
	// TODO: Add your control notification handler code here
	UpdateData();
	sprintf(data, "%s", m_edit8);
	if(storeObjStream(m_edit7, data, strlen(data)) < 0)
		AfxMessageBox("Error Storing Data");

}

void CSsaBtreeWinDlg::OnOK() 
{
	// TODO: Add extra validation here
	finalize_datastore();
	
	CDialog::OnOK();

}

void CSsaBtreeWinDlg::OnCancel() 
{
	// TODO: Add extra cleanup here
	finalize_datastore();
	
	CDialog::OnCancel();
}

void CSsaBtreeWinDlg::OnChangeEdit1() 
{
	// TODO: If this is a RICHEDIT control, the control will not
	// send this notification unless you override the CDialog::OnInitDialog()
	// function to send the EM_SETEVENTMASK message to the control
	// with the ENM_CHANGE flag ORed into the lParam mask.
	
	// TODO: Add your control notification handler code here
	
}

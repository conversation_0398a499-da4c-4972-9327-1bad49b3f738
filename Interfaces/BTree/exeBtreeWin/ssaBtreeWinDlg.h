// ssaBtreeWinDlg.h : header file
//

#if !defined(AFX_SSABTREEWINDLG_H__4E3FBD96_1F8C_11D2_9369_00600801B684__INCLUDED_)
#define AFX_SSABTREEWINDLG_H__4E3FBD96_1F8C_11D2_9369_00600801B684__INCLUDED_

#if _MSC_VER >= 1000
#pragma once
#endif // _MSC_VER >= 1000

/////////////////////////////////////////////////////////////////////////////
// CSsaBtreeWinDlg dialog

class CSsaBtreeWinDlg : public CDialog
{
// Construction
public:
	CSsaBtreeWinDlg(CWnd* pParent = NULL);	// standard constructor

// Dialog Data
	//{{AFX_DATA(CSsaBtreeWinDlg)
	enum { IDD = IDD_SSABTREEWIN_DIALOG };
	CString	m_edit1;
	CString	m_edit2;
	CString	m_edit3;
	CString	m_edit4;
	CString	m_edit5;
	CString	m_edit8;
	int		m_edit7;
	//}}AFX_DATA

	// ClassWizard generated virtual function overrides
	//{{AFX_VIRTUAL(CSsaBtreeWinDlg)
	protected:
	virtual void DoDataExchange(CDataExchange* pDX);	// DDX/DDV support
	//}}AFX_VIRTUAL

// Implementation
protected:
	HICON m_hIcon;

	// Generated message map functions
	//{{AFX_MSG(CSsaBtreeWinDlg)
	virtual BOOL OnInitDialog();
	afx_msg void OnSysCommand(UINT nID, LPARAM lParam);
	afx_msg void OnPaint();
	afx_msg HCURSOR OnQueryDragIcon();
	afx_msg void OnOpenButton();
	afx_msg void OnLookUP();
	afx_msg void OnSet();
	virtual void OnOK();
	virtual void OnCancel();
	afx_msg void OnChangeEdit1();
	//}}AFX_MSG
	DECLARE_MESSAGE_MAP()
};

//{{AFX_INSERT_LOCATION}}
// Microsoft Developer Studio will insert additional declarations immediately before the previous line.

#endif // !defined(AFX_SSABTREEWINDLG_H__4E3FBD96_1F8C_11D2_9369_00600801B684__INCLUDED_)

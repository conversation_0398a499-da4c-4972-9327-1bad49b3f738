// ssaBtreeWin.h : main header file for the SSABTREEWIN application
//

#if !defined(AFX_SSABTREEWIN_H__4E3FBD94_1F8C_11D2_9369_00600801B684__INCLUDED_)
#define AFX_SSABTREEWIN_H__4E3FBD94_1F8C_11D2_9369_00600801B684__INCLUDED_

#if _MSC_VER >= 1000
#pragma once
#endif // _MSC_VER >= 1000

#ifndef __AFXWIN_H__
	#error include 'stdafx.h' before including this file for PCH
#endif

#include "resource.h"		// main symbols

/////////////////////////////////////////////////////////////////////////////
// CSsaBtreeWinApp:
// See ssaBtreeWin.cpp for the implementation of this class
//

class CSsaBtreeWinApp : public CWinApp
{
public:
	CSsaBtreeWinApp();

// Overrides
	// ClassWizard generated virtual function overrides
	//{{AFX_VIRTUAL(CSsaBtreeWinApp)
	public:
	virtual BOOL InitInstance();
	//}}AFX_VIRTUAL

// Implementation

	//{{AFX_MSG(CSsaBtreeWinApp)
		// NOTE - the ClassWizard will add and remove member functions here.
		//    DO NOT EDIT what you see in these blocks of generated code !
	//}}AFX_MSG
	DECLARE_MESSAGE_MAP()
};


/////////////////////////////////////////////////////////////////////////////

//{{AFX_INSERT_LOCATION}}
// Microsoft Developer Studio will insert additional declarations immediately before the previous line.

#endif // !defined(AFX_SSABTREEWIN_H__4E3FBD94_1F8C_11D2_9369_00600801B684__INCLUDED_)

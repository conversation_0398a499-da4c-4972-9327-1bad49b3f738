--------------------Configuration: exeBtreeWin - Win32 Debug--------------------
Begining build with project "C:\steven_stuff\development\slotting\engine_code\devel\exeBtree\exeBtreeWin\exeBtreeWin.dsp", at root.
Active configuration is Win32 (x86) Application (based on Win32 (x86) Application)

Project's tools are:
			"32-bit C/C++ Compiler for 80x86" with flags "/nologo /MDd /W3 /Gm /GX /Zi /Od /D "WIN32" /D "_DEBUG" /D "_WINDOWS" /D "_AFXDLL" /Fp"Debug/exeBtreeWin.pch" /Yu"stdafx.h" /Fo"Debug/" /Fd"Debug/" /FD /c "
			"OLE Type Library Maker" with flags "/nologo /D "_DEBUG" /mktyplib203 /o NUL /win32 "
			"Win32 Resource Compiler" with flags "/l 0x409 /fo"Debug/exeBtreeWin.res" /d "_DEBUG" /d "_AFXDLL" "
			"Browser Database Maker" with flags "/nologo /o"Debug/exeBtreeWin.bsc" "
			"COFF Linker for 80x86" with flags "../Release/slbtrieve.lib /nologo /subsystem:windows /incremental:yes /pdb:"Debug/exeBtreeWin.pdb" /debug /machine:I386 /out:"Debug/exeBtreeWin.exe" /pdbtype:sept "
			"Custom Build" with flags ""
			"<Component 0xa>" with flags ""

Creating command line "rc.exe /l 0x409 /fo"Debug/exeBtreeWin.res" /d "_DEBUG" /d "_AFXDLL" "C:\steven_stuff\development\slotting\engine_code\devel\exeBtree\exeBtreeWin\exeBtreeWin.rc"" 
Creating temp file "C:\TEMP\RSP43.tmp" with contents </nologo /MDd /W3 /Gm /GX /Zi /Od /D "WIN32" /D "_DEBUG" /D "_WINDOWS" /D "_AFXDLL" /Fp"Debug/exeBtreeWin.pch" /Yu"stdafx.h" /Fo"Debug/" /Fd"Debug/" /FD /c 
"C:\steven_stuff\development\slotting\engine_code\devel\exeBtree\exeBtreeWin\exeBtreeWin.cpp"
"C:\steven_stuff\development\slotting\engine_code\devel\exeBtree\exeBtreeWin\exeBtreeWinDlg.cpp"
>
Creating command line "cl.exe @C:\TEMP\RSP43.tmp" 
Creating temp file "C:\TEMP\RSP44.tmp" with contents </nologo /MDd /W3 /Gm /GX /Zi /Od /D "WIN32" /D "_DEBUG" /D "_WINDOWS" /D "_AFXDLL" /Fp"Debug/exeBtreeWin.pch" /Yc"stdafx.h" /Fo"Debug/" /Fd"Debug/" /FD /c 
"C:\steven_stuff\development\slotting\engine_code\devel\exeBtree\exeBtreeWin\StdAfx.cpp"
>
Creating command line "cl.exe @C:\TEMP\RSP44.tmp" 
Creating temp file "C:\TEMP\RSP45.tmp" with contents <../Release/slbtrieve.lib /nologo /subsystem:windows /incremental:yes /pdb:"Debug/exeBtreeWin.pdb" /debug /machine:I386 /out:"Debug/exeBtreeWin.exe" /pdbtype:sept 
.\Debug\exeBtreeWin.obj
.\Debug\exeBtreeWinDlg.obj
.\Debug\StdAfx.obj
.\Debug\exeBtreeWin.res>
Creating command line "link.exe @C:\TEMP\RSP45.tmp" 
Compiling resources...
Compiling...
StdAfx.cpp
Compiling...
exeBtreeWin.cpp
exeBtreeWinDlg.cpp
Generating Code...
Linking...



exeBtreeWin.exe - 0 error(s), 0 warning(s)

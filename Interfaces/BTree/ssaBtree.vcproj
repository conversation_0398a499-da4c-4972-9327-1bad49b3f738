<?xml version="1.0" encoding="Windows-1252"?>
<VisualStudioProject
	ProjectType="Visual C++"
	Version="7.10"
	Name="ssaBtree"
	ProjectGUID="{4539D577-CEF8-4FEC-873D-487B7EE3B247}"
	SccProjectName="SAK"
	SccAuxPath="SAK"
	SccLocalPath="SAK"
	SccProvider="SAK">
	<Platforms>
		<Platform
			Name="Win32"/>
	</Platforms>
	<Configurations>
		<Configuration
			Name="Release|Win32"
			OutputDirectory="..\..\Obj.Release"
			IntermediateDirectory="$(outdir)/Interfaces/SSABTree"
			ConfigurationType="2"
			UseOfMFC="0"
			ATLMinimizesCRunTimeLibraryUsage="FALSE">
			<Tool
				Name="VCCLCompilerTool"
				Optimization="2"
				InlineFunctionExpansion="1"
				AdditionalIncludeDirectories="..\..\common\core"
				PreprocessorDefinitions="WIN32;NDEBUG;_WINDOWS"
				StringPooling="TRUE"
				RuntimeLibrary="0"
				EnableFunctionLevelLinking="TRUE"
				UsePrecompiledHeader="2"
				PrecompiledHeaderFile="$(outdir)/Interfaces/SSABTree/ssaBtree.pch"
				AssemblerListingLocation="$(outdir)/Interfaces/SSABTree/"
				ObjectFile="$(outdir)/Interfaces/SSABTree/"
				ProgramDataBaseFileName="$(outdir)/Interfaces/SSABTree/"
				WarningLevel="3"
				SuppressStartupBanner="TRUE"/>
			<Tool
				Name="VCCustomBuildTool"/>
			<Tool
				Name="VCLinkerTool"
				AdditionalDependencies="odbc32.lib odbccp32.lib"
				OutputFile="$(outdir)/bin/slbtrieve.dll"
				LinkIncremental="1"
				SuppressStartupBanner="TRUE"
				ProgramDatabaseFile="$(outdir)/Interfaces/SSABTree/slbtrieve.pdb"
				SubSystem="2"
				ImportLibrary="$(outdir)/Libraries/slbtrieve.lib"
				TargetMachine="1"/>
			<Tool
				Name="VCMIDLTool"
				PreprocessorDefinitions="NDEBUG"
				MkTypLibCompatible="TRUE"
				SuppressStartupBanner="TRUE"
				TargetEnvironment="1"
				TypeLibraryName=".\Release/exeBtree.tlb"
				HeaderFileName=""/>
			<Tool
				Name="VCPostBuildEventTool"/>
			<Tool
				Name="VCPreBuildEventTool"/>
			<Tool
				Name="VCPreLinkEventTool"/>
			<Tool
				Name="VCResourceCompilerTool"
				PreprocessorDefinitions="NDEBUG"
				Culture="1033"/>
			<Tool
				Name="VCWebServiceProxyGeneratorTool"/>
			<Tool
				Name="VCXMLDataGeneratorTool"/>
			<Tool
				Name="VCWebDeploymentTool"/>
			<Tool
				Name="VCManagedWrapperGeneratorTool"/>
			<Tool
				Name="VCAuxiliaryManagedWrapperGeneratorTool"/>
		</Configuration>
		<Configuration
			Name="Debug|Win32"
			OutputDirectory="..\..\Obj.Debug"
			IntermediateDirectory="$(outdir)/Interfaces/SSABTree"
			ConfigurationType="2"
			UseOfMFC="0"
			ATLMinimizesCRunTimeLibraryUsage="FALSE">
			<Tool
				Name="VCCLCompilerTool"
				Optimization="0"
				AdditionalIncludeDirectories="..\..\common\core"
				PreprocessorDefinitions="WIN32;_DEBUG;_WINDOWS"
				RuntimeLibrary="1"
				UsePrecompiledHeader="2"
				PrecompiledHeaderFile="$(outdir)/Interfaces/SSABTree/ssaBtree.pch"
				AssemblerListingLocation="$(outdir)/Interfaces/SSABTree/"
				ObjectFile="$(outdir)/Interfaces/SSABTree/"
				ProgramDataBaseFileName="$(outdir)/Interfaces/SSABTree/"
				WarningLevel="3"
				SuppressStartupBanner="TRUE"
				DebugInformationFormat="4"/>
			<Tool
				Name="VCCustomBuildTool"/>
			<Tool
				Name="VCLinkerTool"
				AdditionalDependencies="odbc32.lib odbccp32.lib"
				OutputFile="$(outdir)/bin/slbtrieve.dll"
				LinkIncremental="1"
				SuppressStartupBanner="TRUE"
				GenerateDebugInformation="TRUE"
				ProgramDatabaseFile="$(outdir)/Interfaces/SSABTree/slbtrieve.pdb"
				SubSystem="2"
				ImportLibrary="$(outdir)/Libraries/slbtrieve.lib"
				TargetMachine="1"/>
			<Tool
				Name="VCMIDLTool"
				PreprocessorDefinitions="_DEBUG"
				MkTypLibCompatible="TRUE"
				SuppressStartupBanner="TRUE"
				TargetEnvironment="1"
				TypeLibraryName=".\Debug/exeBtree.tlb"
				HeaderFileName=""/>
			<Tool
				Name="VCPostBuildEventTool"/>
			<Tool
				Name="VCPreBuildEventTool"/>
			<Tool
				Name="VCPreLinkEventTool"/>
			<Tool
				Name="VCResourceCompilerTool"
				PreprocessorDefinitions="_DEBUG"
				Culture="1033"/>
			<Tool
				Name="VCWebServiceProxyGeneratorTool"/>
			<Tool
				Name="VCXMLDataGeneratorTool"/>
			<Tool
				Name="VCWebDeploymentTool"/>
			<Tool
				Name="VCManagedWrapperGeneratorTool"/>
			<Tool
				Name="VCAuxiliaryManagedWrapperGeneratorTool"/>
		</Configuration>
	</Configurations>
	<References>
	</References>
	<Files>
		<File
			RelativePath=".\ssaBtree.cpp">
		</File>
		<File
			RelativePath=".\ssaBtree.h">
		</File>
	</Files>
	<Globals>
	</Globals>
</VisualStudioProject>

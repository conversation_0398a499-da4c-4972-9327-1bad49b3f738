#include <stdlib.h>
#include <stdio.h>
#include <string.h>

#include "../ssaBtree.h"


int main (void)
{
	int ret;
	char str[1024];
	char str2[1024];
	int  len;
	int  i,j;

	printf("Testing the functionality of the ssaBtree dll...\n");

	printf("Opening the datastore...\n");
	//ret = open_datastore("\\\\brianh\\forte\\install\\tempfile.btd");
	ret = init_datastore("c:\\test.btd");
	printf("Return code is (%d).\n", ret);
	if(ret < 0) return ret;

	printf("writing information in the datastore...\n");
	for(i=1;i<=250000;i++){
		memset(str, 0, 1024);
		sprintf(str, "<SSO>This is an object (%d)<ESO>",i);
		len = strlen(str);
		ret = storeObjStream(i,str,len);
		if(ret < 0){
			printf("Return code is (%d) on write.\n", ret);
			return ret;
		}
		//printf("Data is (%s)\n", str);

	}


	printf("Finalizing datastore...\n");
	finalize_datastore();

	printf("Re-Opening the datastore...\n");
	//ret = open_datastore("\\\\brianh\\forte\\install\\tempfile.btd");
	ret = open_datastore("c:\\test.btd");
	printf("Return code is (%d).\n", ret);
	if(ret < 0) return ret;


	printf("reading information in the datastore...\n");
	for(i=1;i<=250000;i++){
		len = 1024;
		memset(str, 0, 1024);
		ret = retObjStream(i,str,&len);
		if(ret < 0){
			printf("Return code is (%d) on read.\n", ret);
			return ret;
		}
		//printf("Data is (%s)\n", str);

	}


	printf("Finalizing datastore...\n");
	finalize_datastore();

	return 0;
}





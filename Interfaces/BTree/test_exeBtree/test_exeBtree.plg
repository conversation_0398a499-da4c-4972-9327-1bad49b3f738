<html>
<body>
<pre>
<h1>Build Log</h1>
<h3>
--------------------Configuration: exeBtree - Win32 Release--------------------
</h3>
<h3>Command Lines</h3>
Creating temporary file "C:\DOCUME~1\ADMINI~1\LOCALS~1\Temp\RSP6D.tmp" with contents
[
/nologo /MT /W3 /GX /O2 /D "WIN32" /D "NDEBUG" /D "_WINDOWS" /Fp"Release/exeBtree.pch" /YX /Fo"Release/" /Fd"Release/" /FD /c 
"D:\OptimizeSourceCode\Interfaces\BTree\exeBtree.cpp"
]
Creating command line "cl.exe @C:\DOCUME~1\ADMINI~1\LOCALS~1\Temp\RSP6D.tmp" 
Creating temporary file "C:\DOCUME~1\ADMINI~1\LOCALS~1\Temp\RSP6E.tmp" with contents
[
kernel32.lib user32.lib gdi32.lib winspool.lib comdlg32.lib advapi32.lib shell32.lib ole32.lib oleaut32.lib uuid.lib odbc32.lib odbccp32.lib /nologo /subsystem:windows /dll /incremental:no /pdb:"Release/slbtrieve.pdb" /machine:I386 /out:"Release/slbtrieve.dll" /implib:"Release/slbtrieve.lib" 
.\Release\exeBtree.obj
]
Creating command line "link.exe @C:\DOCUME~1\ADMINI~1\LOCALS~1\Temp\RSP6E.tmp"
<h3>Output Window</h3>
Compiling...
exeBtree.cpp
D:\OptimizeSourceCode\Interfaces\BTree\exeBtree.cpp(263) : warning C4018: '>=' : signed/unsigned mismatch
D:\OptimizeSourceCode\Interfaces\BTree\exeBtree.cpp(348) : warning C4018: '>=' : signed/unsigned mismatch
D:\OptimizeSourceCode\Interfaces\BTree\exeBtree.cpp(402) : warning C4018: '<' : signed/unsigned mismatch
D:\OptimizeSourceCode\Interfaces\BTree\exeBtree.cpp(484) : warning C4018: '==' : signed/unsigned mismatch
D:\OptimizeSourceCode\Interfaces\BTree\exeBtree.cpp(486) : warning C4018: '==' : signed/unsigned mismatch
D:\OptimizeSourceCode\Interfaces\BTree\exeBtree.cpp(489) : warning C4018: '<' : signed/unsigned mismatch
D:\OptimizeSourceCode\Interfaces\BTree\exeBtree.cpp(491) : warning C4018: '>' : signed/unsigned mismatch
D:\OptimizeSourceCode\Interfaces\BTree\exeBtree.cpp(495) : warning C4018: '==' : signed/unsigned mismatch
D:\OptimizeSourceCode\Interfaces\BTree\exeBtree.cpp(497) : warning C4018: '>' : signed/unsigned mismatch
D:\OptimizeSourceCode\Interfaces\BTree\exeBtree.cpp(499) : warning C4018: '<' : signed/unsigned mismatch
D:\OptimizeSourceCode\Interfaces\BTree\exeBtree.cpp(512) : warning C4018: '==' : signed/unsigned mismatch
D:\OptimizeSourceCode\Interfaces\BTree\exeBtree.cpp(514) : warning C4018: '<' : signed/unsigned mismatch
D:\OptimizeSourceCode\Interfaces\BTree\exeBtree.cpp(516) : warning C4018: '>' : signed/unsigned mismatch
D:\OptimizeSourceCode\Interfaces\BTree\exeBtree.cpp(520) : warning C4018: '==' : signed/unsigned mismatch
D:\OptimizeSourceCode\Interfaces\BTree\exeBtree.cpp(522) : warning C4018: '<' : signed/unsigned mismatch
D:\OptimizeSourceCode\Interfaces\BTree\exeBtree.cpp(524) : warning C4018: '>' : signed/unsigned mismatch
D:\OptimizeSourceCode\Interfaces\BTree\exeBtree.cpp(557) : warning C4018: '==' : signed/unsigned mismatch
D:\OptimizeSourceCode\Interfaces\BTree\exeBtree.cpp(617) : warning C4018: '!=' : signed/unsigned mismatch
D:\OptimizeSourceCode\Interfaces\BTree\exeBtree.cpp(684) : warning C4018: '!=' : signed/unsigned mismatch
D:\OptimizeSourceCode\Interfaces\BTree\exeBtree.cpp(700) : warning C4018: '<' : signed/unsigned mismatch
D:\OptimizeSourceCode\Interfaces\BTree\exeBtree.cpp(421) : warning C4715: 'finalize_datastore' : not all control paths return a value
Linking...
   Creating library Release/slbtrieve.lib and object Release/slbtrieve.exp



<h3>Results</h3>
slbtrieve.dll - 0 error(s), 21 warning(s)
<h3>
--------------------Configuration: exeBtree - Win32 Debug--------------------
</h3>
<h3>Command Lines</h3>
Creating temporary file "C:\DOCUME~1\ADMINI~1\LOCALS~1\Temp\RSP72.tmp" with contents
[
/nologo /MTd /W3 /Gm /GX /ZI /Od /D "WIN32" /D "_DEBUG" /D "_WINDOWS" /Fp"Debug/exeBtree.pch" /YX /Fo"Debug/" /Fd"Debug/" /FD /c 
"D:\OptimizeSourceCode\Interfaces\BTree\exeBtree.cpp"
]
Creating command line "cl.exe @C:\DOCUME~1\ADMINI~1\LOCALS~1\Temp\RSP72.tmp" 
Creating temporary file "C:\DOCUME~1\ADMINI~1\LOCALS~1\Temp\RSP73.tmp" with contents
[
kernel32.lib user32.lib gdi32.lib winspool.lib comdlg32.lib advapi32.lib shell32.lib ole32.lib oleaut32.lib uuid.lib odbc32.lib odbccp32.lib /nologo /subsystem:windows /dll /incremental:yes /pdb:"Debug/slbtrieve.pdb" /debug /machine:I386 /out:"Debug/slbtrieve.dll" /implib:"Debug/slbtrieve.lib" /pdbtype:sept 
.\Debug\exeBtree.obj
]
Creating command line "link.exe @C:\DOCUME~1\ADMINI~1\LOCALS~1\Temp\RSP73.tmp"
<h3>Output Window</h3>
Compiling...
exeBtree.cpp
d:\optimizesourcecode\interfaces\btree\exebtree.cpp(263) : warning C4018: '>=' : signed/unsigned mismatch
d:\optimizesourcecode\interfaces\btree\exebtree.cpp(348) : warning C4018: '>=' : signed/unsigned mismatch
d:\optimizesourcecode\interfaces\btree\exebtree.cpp(402) : warning C4018: '<' : signed/unsigned mismatch
d:\optimizesourcecode\interfaces\btree\exebtree.cpp(484) : warning C4018: '==' : signed/unsigned mismatch
d:\optimizesourcecode\interfaces\btree\exebtree.cpp(486) : warning C4018: '==' : signed/unsigned mismatch
d:\optimizesourcecode\interfaces\btree\exebtree.cpp(489) : warning C4018: '<' : signed/unsigned mismatch
d:\optimizesourcecode\interfaces\btree\exebtree.cpp(491) : warning C4018: '>' : signed/unsigned mismatch
d:\optimizesourcecode\interfaces\btree\exebtree.cpp(495) : warning C4018: '==' : signed/unsigned mismatch
d:\optimizesourcecode\interfaces\btree\exebtree.cpp(497) : warning C4018: '>' : signed/unsigned mismatch
d:\optimizesourcecode\interfaces\btree\exebtree.cpp(499) : warning C4018: '<' : signed/unsigned mismatch
d:\optimizesourcecode\interfaces\btree\exebtree.cpp(512) : warning C4018: '==' : signed/unsigned mismatch
d:\optimizesourcecode\interfaces\btree\exebtree.cpp(514) : warning C4018: '<' : signed/unsigned mismatch
d:\optimizesourcecode\interfaces\btree\exebtree.cpp(516) : warning C4018: '>' : signed/unsigned mismatch
d:\optimizesourcecode\interfaces\btree\exebtree.cpp(520) : warning C4018: '==' : signed/unsigned mismatch
d:\optimizesourcecode\interfaces\btree\exebtree.cpp(522) : warning C4018: '<' : signed/unsigned mismatch
d:\optimizesourcecode\interfaces\btree\exebtree.cpp(524) : warning C4018: '>' : signed/unsigned mismatch
d:\optimizesourcecode\interfaces\btree\exebtree.cpp(557) : warning C4018: '==' : signed/unsigned mismatch
d:\optimizesourcecode\interfaces\btree\exebtree.cpp(617) : warning C4018: '!=' : signed/unsigned mismatch
d:\optimizesourcecode\interfaces\btree\exebtree.cpp(684) : warning C4018: '!=' : signed/unsigned mismatch
d:\optimizesourcecode\interfaces\btree\exebtree.cpp(700) : warning C4018: '<' : signed/unsigned mismatch
d:\optimizesourcecode\interfaces\btree\exebtree.cpp(421) : warning C4715: 'finalize_datastore' : not all control paths return a value
Linking...
   Creating library Debug/slbtrieve.lib and object Debug/slbtrieve.exp



<h3>Results</h3>
slbtrieve.dll - 0 error(s), 21 warning(s)
<h3>
--------------------Configuration: exeBtreeWin - Win32 Release--------------------
</h3>
<h3>Command Lines</h3>
Creating command line "rc.exe /l 0x409 /fo"Release/exeBtreeWin.res" /d "NDEBUG" /d "_AFXDLL" "D:\OptimizeSourceCode\Interfaces\BTree\exeBtreeWin\exeBtreeWin.rc"" 
Creating temporary file "C:\DOCUME~1\ADMINI~1\LOCALS~1\Temp\RSP77.tmp" with contents
[
/nologo /MD /W3 /GX /O2 /D "WIN32" /D "NDEBUG" /D "_WINDOWS" /D "_AFXDLL" /Fp"Release/exeBtreeWin.pch" /Yu"stdafx.h" /Fo"Release/" /Fd"Release/" /FD /c 
"D:\OptimizeSourceCode\Interfaces\BTree\exeBtreeWin\exeBtreeWin.cpp"
"D:\OptimizeSourceCode\Interfaces\BTree\exeBtreeWin\exeBtreeWinDlg.cpp"
]
Creating command line "cl.exe @C:\DOCUME~1\ADMINI~1\LOCALS~1\Temp\RSP77.tmp" 
Creating temporary file "C:\DOCUME~1\ADMINI~1\LOCALS~1\Temp\RSP78.tmp" with contents
[
/nologo /MD /W3 /GX /O2 /D "WIN32" /D "NDEBUG" /D "_WINDOWS" /D "_AFXDLL" /Fp"Release/exeBtreeWin.pch" /Yc"stdafx.h" /Fo"Release/" /Fd"Release/" /FD /c 
"D:\OptimizeSourceCode\Interfaces\BTree\exeBtreeWin\StdAfx.cpp"
]
Creating command line "cl.exe @C:\DOCUME~1\ADMINI~1\LOCALS~1\Temp\RSP78.tmp" 
Creating command line "link.exe /nologo /subsystem:windows /incremental:no /pdb:"Release/exeBtreeWin.pdb" /machine:I386 /out:"Release/exeBtreeWin.exe"  .\Release\exeBtreeWin.obj .\Release\exeBtreeWinDlg.obj .\Release\StdAfx.obj .\Release\exeBtreeWin.res "
<h3>Output Window</h3>
Compiling resources...
Compiling...
StdAfx.cpp
Compiling...
exeBtreeWin.cpp
exeBtreeWinDlg.cpp
Generating Code...
Linking...
exeBtreeWinDlg.obj : error LNK2001: unresolved external symbol _open_datastore
exeBtreeWinDlg.obj : error LNK2001: unresolved external symbol _retObjStream
exeBtreeWinDlg.obj : error LNK2001: unresolved external symbol _storeObjStream
exeBtreeWinDlg.obj : error LNK2001: unresolved external symbol _finalize_datastore
Release/exeBtreeWin.exe : fatal error LNK1120: 4 unresolved externals
Error executing link.exe.



<h3>Results</h3>
exeBtreeWin.exe - 5 error(s), 0 warning(s)
<h3>
--------------------Configuration: exeBtreeWin - Win32 Debug--------------------
</h3>
<h3>Command Lines</h3>
Creating command line "rc.exe /l 0x409 /fo"Debug/exeBtreeWin.res" /d "_DEBUG" /d "_AFXDLL" "D:\OptimizeSourceCode\Interfaces\BTree\exeBtreeWin\exeBtreeWin.rc"" 
Creating temporary file "C:\DOCUME~1\ADMINI~1\LOCALS~1\Temp\RSP7A.tmp" with contents
[
/nologo /MDd /W3 /Gm /GX /ZI /Od /D "WIN32" /D "_DEBUG" /D "_WINDOWS" /D "_AFXDLL" /Fp"Debug/exeBtreeWin.pch" /Yu"stdafx.h" /Fo"Debug/" /Fd"Debug/" /FD /c 
"D:\OptimizeSourceCode\Interfaces\BTree\exeBtreeWin\exeBtreeWin.cpp"
"D:\OptimizeSourceCode\Interfaces\BTree\exeBtreeWin\exeBtreeWinDlg.cpp"
]
Creating command line "cl.exe @C:\DOCUME~1\ADMINI~1\LOCALS~1\Temp\RSP7A.tmp" 
Creating temporary file "C:\DOCUME~1\ADMINI~1\LOCALS~1\Temp\RSP7B.tmp" with contents
[
/nologo /MDd /W3 /Gm /GX /ZI /Od /D "WIN32" /D "_DEBUG" /D "_WINDOWS" /D "_AFXDLL" /Fp"Debug/exeBtreeWin.pch" /Yc"stdafx.h" /Fo"Debug/" /Fd"Debug/" /FD /c 
"D:\OptimizeSourceCode\Interfaces\BTree\exeBtreeWin\StdAfx.cpp"
]
Creating command line "cl.exe @C:\DOCUME~1\ADMINI~1\LOCALS~1\Temp\RSP7B.tmp" 
Creating temporary file "C:\DOCUME~1\ADMINI~1\LOCALS~1\Temp\RSP7C.tmp" with contents
[
../Release/slbtrieve.lib /nologo /subsystem:windows /incremental:yes /pdb:"Debug/exeBtreeWin.pdb" /debug /machine:I386 /out:"Debug/exeBtreeWin.exe" /pdbtype:sept 
.\Debug\exeBtreeWin.obj
.\Debug\exeBtreeWinDlg.obj
.\Debug\StdAfx.obj
.\Debug\exeBtreeWin.res
]
Creating command line "link.exe @C:\DOCUME~1\ADMINI~1\LOCALS~1\Temp\RSP7C.tmp"
<h3>Output Window</h3>
Compiling resources...
Compiling...
StdAfx.cpp
Compiling...
exeBtreeWin.cpp
exeBtreeWinDlg.cpp
Generating Code...
Linking...



<h3>Results</h3>
exeBtreeWin.exe - 0 error(s), 0 warning(s)
<h3>
--------------------Configuration: test_exeBtree - Win32 Release--------------------
</h3>
<h3>Command Lines</h3>
Creating temporary file "C:\DOCUME~1\ADMINI~1\LOCALS~1\Temp\RSP7E.tmp" with contents
[
/nologo /ML /W3 /GX /O2 /D "WIN32" /D "NDEBUG" /D "_CONSOLE" /D "_MBCS" /Fp"Release/test_exeBtree.pch" /YX /Fo"Release/" /Fd"Release/" /FD /c 
"D:\OptimizeSourceCode\Interfaces\BTree\test_exeBtree\main.cpp"
]
Creating command line "cl.exe @C:\DOCUME~1\ADMINI~1\LOCALS~1\Temp\RSP7E.tmp" 
Creating temporary file "C:\DOCUME~1\ADMINI~1\LOCALS~1\Temp\RSP7F.tmp" with contents
[
kernel32.lib user32.lib gdi32.lib winspool.lib comdlg32.lib advapi32.lib shell32.lib ole32.lib oleaut32.lib uuid.lib odbc32.lib odbccp32.lib /nologo /subsystem:console /incremental:no /pdb:"Release/test_exeBtree.pdb" /machine:I386 /out:"Release/test_exeBtree.exe" 
.\Release\main.obj
]
Creating command line "link.exe @C:\DOCUME~1\ADMINI~1\LOCALS~1\Temp\RSP7F.tmp"
<h3>Output Window</h3>
Compiling...
main.cpp
D:\OptimizeSourceCode\Interfaces\BTree\test_exeBtree\main.cpp(12) : warning C4101: 'str2' : unreferenced local variable
D:\OptimizeSourceCode\Interfaces\BTree\test_exeBtree\main.cpp(14) : warning C4101: 'j' : unreferenced local variable
Linking...
main.obj : error LNK2001: unresolved external symbol _retObjStream
main.obj : error LNK2001: unresolved external symbol _open_datastore
main.obj : error LNK2001: unresolved external symbol _finalize_datastore
main.obj : error LNK2001: unresolved external symbol _storeObjStream
main.obj : error LNK2001: unresolved external symbol _init_datastore
Release/test_exeBtree.exe : fatal error LNK1120: 5 unresolved externals
Error executing link.exe.



<h3>Results</h3>
test_exeBtree.exe - 6 error(s), 2 warning(s)
<h3>
--------------------Configuration: test_exeBtree - Win32 Debug--------------------
</h3>
<h3>Command Lines</h3>
Creating temporary file "C:\DOCUME~1\ADMINI~1\LOCALS~1\Temp\RSP81.tmp" with contents
[
/nologo /MLd /W3 /Gm /GX /ZI /Od /D "WIN32" /D "_DEBUG" /D "_CONSOLE" /D "_MBCS" /Fp"Debug/test_exeBtree.pch" /YX /Fo"Debug/" /Fd"Debug/" /FD /c 
"D:\OptimizeSourceCode\Interfaces\BTree\test_exeBtree\main.cpp"
]
Creating command line "cl.exe @C:\DOCUME~1\ADMINI~1\LOCALS~1\Temp\RSP81.tmp" 
Creating temporary file "C:\DOCUME~1\ADMINI~1\LOCALS~1\Temp\RSP82.tmp" with contents
[
kernel32.lib user32.lib gdi32.lib winspool.lib comdlg32.lib advapi32.lib shell32.lib ole32.lib oleaut32.lib uuid.lib odbc32.lib odbccp32.lib ../Release/slbtrieve.lib /nologo /subsystem:console /incremental:yes /pdb:"Debug/test_exeBtree.pdb" /debug /machine:I386 /out:"Debug/test_exeBtree.exe" /pdbtype:sept 
.\Debug\main.obj
]
Creating command line "link.exe @C:\DOCUME~1\ADMINI~1\LOCALS~1\Temp\RSP82.tmp"
<h3>Output Window</h3>
Compiling...
main.cpp
d:\optimizesourcecode\interfaces\btree\test_exebtree\main.cpp(12) : warning C4101: 'str2' : unreferenced local variable
d:\optimizesourcecode\interfaces\btree\test_exebtree\main.cpp(14) : warning C4101: 'j' : unreferenced local variable
Linking...



<h3>Results</h3>
test_exeBtree.exe - 0 error(s), 2 warning(s)
</pre>
</body>
</html>

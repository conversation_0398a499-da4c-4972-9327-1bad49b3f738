#ifndef SSABTREE
#define SSABTREE

#define C_DLL_EXPORT	extern "C" __declspec( dllexport )
#define C_DLL_IMPORT	extern "C" __declspec( dllimport )

//#define DEBUG 1

/* *********************************************** */
/* Error Constants and Error Messages.             */
/* *********************************************** */
#define EB_EOPEN	-1
#define EB_EOPEN_MSG	"Error opening file for BTree datastore"

#define EB_EWRITE	-2
#define EB_EWRITE_MSG	"Error writing to the current BTree datastore"

#define EB_EALLOC	-3
#define EB_EALLOC_MSG	"Error Allocating memory for the BTree index"

#define EB_EFINI	-4
#define EB_EFINI_MSG	"Error Finalizing datastore."

#define EB_EREAD	-5
#define EB_EREAD_MSG	"Error Reading from datastore."

#define EB_EINVAL	-6
#define EB_EINVAL_MSG	"Invalid datastore."

#define EB_INUSE	1
#define EB_NOINUSE	0


/* *********************************************** */
/* These are the publicly visible functions.  Only */
/* these functions will be exported to the world.  */
/* *********************************************** */
C_DLL_EXPORT int init_datastore(char *fname);
C_DLL_EXPORT int open_datastore(char *fname);
C_DLL_EXPORT int storeObjStream(int objid, char *str, int len);
C_DLL_EXPORT int retObjStream(int objid, char *str, int *lSize);
C_DLL_EXPORT int delete_object(int objid);
C_DLL_EXPORT int finalize_datastore(void);
C_DLL_EXPORT void dump_index(void);


/* *********************************************** */
/* These are the internal data structures that are */
/* necessary to perform the disk access and track  */
/* the status of the stored information.           */
/* *********************************************** */

typedef struct {
	unsigned short signature;
	unsigned short usage;
	unsigned int   elem_count;
	unsigned int   elem_start;
} ssaBtreeHeader;

typedef struct {
	unsigned int   elem_key;
	unsigned int   elem_offset;
	unsigned int   elem_len;
} ssaBtreeElem;

/* *********************************************** */
/* These are the internal workhorse functions.     */
/* These should never be called directly from an   */
/* external source.                                */
/* *********************************************** */
void finishDataStore(void);
int  findElem(int elem_key);
int  addElem(int elem_pos, int objid);
int writeData(int objid, char *str, int len);
void writeUnused(void);
void readData(int objid, char *str, int len);

#endif // SLBTRIEVE defined

begin C SLOTBTreeLibrary;
has property restricted = TRUE;
has property multithreaded = TRUE;

class BTreeAccessClass
	init_datastore(input fname : pointer to char) : integer;
	open_datastore(input fname : pointer to char) : integer;
	storeObjStream(input objid : integer,input str : pointer to char,input len : integer) : integer;
	retObjStream(input objid : integer,input str : pointer to char, input lSize : pointer to integer) : integer;
	delete_object(input objid : integer) : integer;
	finalize_datastore() : integer;
end;

has property
	LibraryName = 'SLOTBtre';
	Extended = (ExternalSharedLibs='slbtrieve');
end SLOTBTreeLibrary;
<html>
<body>
<pre>
<h1>Build Log</h1>
<h3>
--------------------Configuration: exeBtree - Win32 Debug--------------------
</h3>
<h3>Command Lines</h3>
Creating temporary file "C:\DOCUME~1\ADMINI~1\LOCALS~1\Temp\RSP7AC.tmp" with contents
[
/nologo /MTd /W3 /Gm /GX /ZI /Od /D "WIN32" /D "_DEBUG" /D "_WINDOWS" /Fp"Debug/exeBtree.pch" /YX /Fo"Debug/" /Fd"Debug/" /FD /c 
"D:\All\OptimizeSourceCode2\Interfaces\BTree\exeBtree.cpp"
]
Creating command line "cl.exe @C:\DOCUME~1\ADMINI~1\LOCALS~1\Temp\RSP7AC.tmp" 
Creating temporary file "C:\DOCUME~1\ADMINI~1\LOCALS~1\Temp\RSP7AD.tmp" with contents
[
kernel32.lib user32.lib gdi32.lib winspool.lib comdlg32.lib advapi32.lib shell32.lib ole32.lib oleaut32.lib uuid.lib odbc32.lib odbccp32.lib /nologo /subsystem:windows /dll /incremental:yes /pdb:"Debug/slbtrieve.pdb" /debug /machine:I386 /out:"Debug/slbtrieve.dll" /implib:"Debug/slbtrieve.lib" /pdbtype:sept 
.\Debug\exeBtree.obj
]
Creating command line "link.exe @C:\DOCUME~1\ADMINI~1\LOCALS~1\Temp\RSP7AD.tmp"
<h3>Output Window</h3>
Compiling...
exeBtree.cpp
d:\all\optimizesourcecode2\interfaces\btree\exebtree.cpp(263) : warning C4018: '>=' : signed/unsigned mismatch
d:\all\optimizesourcecode2\interfaces\btree\exebtree.cpp(348) : warning C4018: '>=' : signed/unsigned mismatch
d:\all\optimizesourcecode2\interfaces\btree\exebtree.cpp(402) : warning C4018: '<' : signed/unsigned mismatch
d:\all\optimizesourcecode2\interfaces\btree\exebtree.cpp(484) : warning C4018: '==' : signed/unsigned mismatch
d:\all\optimizesourcecode2\interfaces\btree\exebtree.cpp(486) : warning C4018: '==' : signed/unsigned mismatch
d:\all\optimizesourcecode2\interfaces\btree\exebtree.cpp(489) : warning C4018: '<' : signed/unsigned mismatch
d:\all\optimizesourcecode2\interfaces\btree\exebtree.cpp(491) : warning C4018: '>' : signed/unsigned mismatch
d:\all\optimizesourcecode2\interfaces\btree\exebtree.cpp(495) : warning C4018: '==' : signed/unsigned mismatch
d:\all\optimizesourcecode2\interfaces\btree\exebtree.cpp(497) : warning C4018: '>' : signed/unsigned mismatch
d:\all\optimizesourcecode2\interfaces\btree\exebtree.cpp(499) : warning C4018: '<' : signed/unsigned mismatch
d:\all\optimizesourcecode2\interfaces\btree\exebtree.cpp(512) : warning C4018: '==' : signed/unsigned mismatch
d:\all\optimizesourcecode2\interfaces\btree\exebtree.cpp(514) : warning C4018: '<' : signed/unsigned mismatch
d:\all\optimizesourcecode2\interfaces\btree\exebtree.cpp(516) : warning C4018: '>' : signed/unsigned mismatch
d:\all\optimizesourcecode2\interfaces\btree\exebtree.cpp(520) : warning C4018: '==' : signed/unsigned mismatch
d:\all\optimizesourcecode2\interfaces\btree\exebtree.cpp(522) : warning C4018: '<' : signed/unsigned mismatch
d:\all\optimizesourcecode2\interfaces\btree\exebtree.cpp(524) : warning C4018: '>' : signed/unsigned mismatch
d:\all\optimizesourcecode2\interfaces\btree\exebtree.cpp(557) : warning C4018: '==' : signed/unsigned mismatch
d:\all\optimizesourcecode2\interfaces\btree\exebtree.cpp(617) : warning C4018: '!=' : signed/unsigned mismatch
d:\all\optimizesourcecode2\interfaces\btree\exebtree.cpp(684) : warning C4018: '!=' : signed/unsigned mismatch
d:\all\optimizesourcecode2\interfaces\btree\exebtree.cpp(700) : warning C4018: '<' : signed/unsigned mismatch
d:\all\optimizesourcecode2\interfaces\btree\exebtree.cpp(421) : warning C4715: 'finalize_datastore' : not all control paths return a value
Linking...
   Creating library Debug/slbtrieve.lib and object Debug/slbtrieve.exp



<h3>Results</h3>
slbtrieve.dll - 0 error(s), 21 warning(s)
</pre>
</body>
</html>

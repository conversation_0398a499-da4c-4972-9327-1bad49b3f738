#include <stdio.h>
#include <stdlib.h>

#include "ssaBtree.h"
#include "../../common/core/ssa_exception.h"

static ssaBtreeHeader eBH;
static ssaBtreeElem   *eBE;  // Pointer to an array of Elements.
static FILE           *eBF; 
static int            datastoreOpen = 0;

char   msg[512];

/* ********************************************* */
/* This function will create a brand new BTree   */
/* like structure on the disk.  If the file that */
/* we are given already exists, it will zero the */
/* file and create the datastore brand new.      */
/* Return of 0 = success.                        */
/* ********************************************* */
C_DLL_EXPORT int init_datastore(char *fname)
{
	FILE *tmpfile;
	
	/* ************************************* */
	/* Check to see if there is a datastore  */
	/* open.  If so finalize it and free the */
	/* index memory.                         */
	/* ************************************* */
	try {
		finishDataStore();
	} catch (SsaException e){
#ifdef DEBUG
		e.GetAllMessage(msg);
		printf("%s (%s)\n", EB_EFINI_MSG, msg);
#endif
		return EB_EFINI;
	}

	/* ************************************* */
	/* This little dance will zero out the   */
	/* file if it exists.                    */
	/* ************************************* */
	tmpfile = fopen(fname, "w+b");
	if(tmpfile != NULL)
		fclose(tmpfile);

	/* ************************************* */
	/* Now open the file for real.           */
	/* ************************************* */
	
	eBF = fopen(fname, "w+b");
	if(eBF == NULL){
#ifdef DEBUG
		printf("%s (%s)\n", EB_EOPEN_MSG, fname);
#endif
		return EB_EOPEN;
	}

	/* ************************************* */
	/* Setup a new header, and write it to   */
	/* the file.                             */
	/* ************************************* */

	eBH.signature = (unsigned short)31415;
	eBH.usage = (unsigned short)EB_INUSE;
	eBH.elem_count = (unsigned int)0;
	eBH.elem_start = (unsigned int)sizeof(ssaBtreeHeader);

	if(fwrite(&eBH, sizeof(ssaBtreeHeader), 1, eBF) != 1){
#ifdef DEBUG
		printf("%s (%s)\n", EB_EWRITE_MSG, fname);	
#endif
		fclose(eBF);
		return EB_EWRITE;
	}

	/* ************************************* */
	/* Allocate some memory for the initial  */
	/* list of elements.                     */
	/* ************************************* */
	eBE = (ssaBtreeElem *)malloc(500 * sizeof(ssaBtreeElem));
	if(eBE == NULL){
#ifdef DEBUG
		printf("%s\n", EB_EALLOC_MSG);
#endif
		fclose(eBF);
		return EB_EALLOC;
	}

	/* ************************************* */
	/* We're ready to rock and roll.  Send   */
	/* back the aok.                         */
	/* ************************************* */
	datastoreOpen = 1;

	return 0;
}

/* ********************************************* */
/* This function will open an existing datastore */
/* on the disk.  It will verify that it is a     */
/* valid datastore.  It will read in the entire  */
/* datastore index and setup the index in memory */
/* for further access.                           */
/* ********************************************* */
C_DLL_EXPORT int open_datastore(char *fname)
{
	int alloc_count;
	/* ************************************* */
	/* Check to see if there is a datastore  */
	/* open.  If so finalize it and free the */
	/* index memory.                         */
	/* ************************************* */
	try {
		finishDataStore();
	} catch (SsaException e){
#ifdef DEBUG
		e.GetAllMessage(msg);
		printf("%s (%s)\n", EB_EFINI_MSG, msg);
#endif
		return EB_EFINI;
	}

	try {
	
	/* ************************************* */
	/* Open the named file.                  */
	/* ************************************* */
	eBF = fopen(fname, "r+b");
	if(eBF == NULL){
#ifdef DEBUG
		printf("%s (%s)\n", EB_EOPEN_MSG, fname);
#endif
		return EB_EOPEN;
	}

	/* ************************************* */
	/* Read in the header and validate that  */
	/* this is a real datastore.             */
	/* ************************************* */
	if(fread(&eBH, sizeof(ssaBtreeHeader), 1, eBF) != 1){
#ifdef DEBUG
		printf("%s (%s)\n", EB_EREAD_MSG, fname);
#endif
		fclose(eBF);
		return EB_EREAD;
	}

	if(eBH.signature != (unsigned short)31415){
#ifdef DEBUG
		printf("%s (%d) (%d) (%d) (%d)\n", EB_EINVAL_MSG, 
			eBH.signature, eBH.usage, eBH.elem_count, eBH.elem_start);
#endif
		fclose(eBF);
		return EB_EINVAL;
	}	

	/* ************************************* */
	/* If we get to here, we have a valid    */
	/* datastore.  Allocate the memory for   */
	/* the index, and then read in the data. */
	/* ************************************* */
	alloc_count = 500;
	if(eBH.elem_count >= 500){
		alloc_count	= (eBH.elem_count / 500) + 1;
		alloc_count *= 500;
	}
	eBE = (ssaBtreeElem *)malloc(alloc_count * sizeof(ssaBtreeElem));
	if(eBE == NULL){
#ifdef DEBUG
		printf("%s\n", EB_EALLOC_MSG);
#endif
		fclose(eBF);
		return EB_EALLOC;
	}

	if(fseek(eBF, eBH.elem_start, SEEK_SET) != 0){
#ifdef DEBUG
		printf("%s (%s)\n", EB_EREAD_MSG, "seeking");
#endif
		fclose(eBF);
		return EB_EREAD;
	}

	if(fread(eBE, sizeof(ssaBtreeElem), eBH.elem_count, eBF) != eBH.elem_count){
#ifdef DEBUG
		printf("%s (%s)\n", EB_EREAD_MSG, "could not get index");
#endif
		perror("Could not get the index");

		fclose(eBF);
		return EB_EREAD;
	}	

	} catch (SsaException e) {
//#ifdef DEBUG
		e.GetAllMessage(msg);
		printf("%s (%s)\n", EB_EWRITE_MSG, msg);
//#endif
		return EB_EWRITE;
	} catch (...) {
		printf("Caught a generic exception in storeObjStream\n");
		return EB_EWRITE;
	}

	/* ************************************* */
	/* We're ready to rock and roll.  Send   */
	/* back the aok.                         */
	/* ************************************* */
	datastoreOpen = 1;

	return 0;
}

/* ********************************************* */
/* This function will accept a character stream  */
/* to store in the datastore.  It will first     */
/* check to see if the objid already exists.  If */
/* it does, it will attempt to replace the one   */
/* that exists if the replacement is of equal or */
/* lesser size.  If not, it will mark the one    */
/* that exists as freed space, and add the new   */
/* value at the end of the file.                 */
/* ********************************************* */
C_DLL_EXPORT int storeObjStream(int objid, char *str, int len)
{
	int pos, add;

	try {

	if(eBH.elem_count == 0){
		/* ***************************** */
		/* Fencepost condition. Handle   */
		/* the first one specially.      */
		/* ***************************** */
		eBH.elem_start = sizeof(ssaBtreeHeader);
		eBE[0].elem_offset = eBH.elem_start;
		eBE[0].elem_len = len;
		eBE[0].elem_key = objid;
		if(fseek(eBF, eBE[0].elem_offset, SEEK_SET) != 0){
#ifdef DEBUG
			printf("Error seeking to position in store\n");
#endif
			return EB_EWRITE;
		}
		add = writeData(objid, str, len);
		eBH.elem_start += add;
		eBH.elem_count ++;
	
		return 0;
	}

			

	pos = findElem(objid);

	
	if(pos >= 0){
		/* ***************************** */
		/* element already exists.       */
		/* ***************************** */
		if(eBE[pos].elem_len >= len){
			/* ********************* */
			/* replace it.           */
			/* ********************* */
			if(fseek(eBF, eBE[pos].elem_offset, SEEK_SET) != 0){
#ifdef DEBUG
				printf("Error seeking to position in store\n");
#endif
				return EB_EWRITE;
			}

			writeData(objid, str, len);
			eBE[pos].elem_len = len;
		} else {
			/* ********************* */
			/* Mark as unused.       */
			/* ********************* */
			if(fseek(eBF, eBE[pos].elem_offset, SEEK_SET) != 0){
#ifdef DEBUG
				printf("Error seeking to position in store\n");
#endif
				return EB_EWRITE;
			}
			writeUnused();
			if(fseek(eBF, eBH.elem_start, SEEK_SET) != 0){
#ifdef DEBUG
				printf("Error seeking to position in store\n");
#endif
				return EB_EWRITE;
			}
			add = writeData(objid, str, len);
			eBE[pos].elem_offset = eBH.elem_start;
			eBE[pos].elem_len = len;
			
			eBH.elem_start += add;
		}	
	} else {
		/* ***************************** */
		/* Did not find the element.     */
		/* Add a new entry to our index. */
		/* ***************************** */
		pos = addElem(pos, objid);
		  
		eBE[pos].elem_len = len;
		eBE[pos].elem_offset = eBH.elem_start;
		if(fseek(eBF, eBE[pos].elem_offset, SEEK_SET) != 0){
#ifdef DEBUG
			printf("Error seeking to position in store\n");
#endif
			return EB_EWRITE;
		}
		add = writeData(objid, str, len);
		eBH.elem_start += add;
	}


	} catch (SsaException e) {
//#ifdef DEBUG
		e.GetAllMessage(msg);
		printf("%s (%s)\n", EB_EWRITE_MSG, msg);
//#endif
		return EB_EWRITE;
	} catch (...) {
		printf("Caught a generic exception in storeObjStream\n");
		return EB_EWRITE;
	}

	return 0;
}

C_DLL_EXPORT int retObjStream(int objid, char *str, int *lSize)
{
	int pos;	

	/* ************************************************* */
	/* Check to see if we have stored this object.       */
	/* ************************************************* */
	pos = findElem(objid);
	if(pos<0)
		return -1;

	/* ************************************************* */
	/* If so, find it on the disk and restore it into    */
	/* the object that will hold it.                     */
	/* ************************************************* */
	if(eBE[pos].elem_len >= *lSize){
		*lSize = eBE[pos].elem_len;
		return -2;
	}

	if(fseek(eBF, eBE[pos].elem_offset, SEEK_SET) != 0){
#ifdef DEBUG
		printf("Error seeking to position in store\n");
#endif
		return EB_EREAD;
	}
	try {
		readData(eBE[pos].elem_key, str, eBE[pos].elem_len);
	} catch (SsaException e){
#ifdef DEBUG
		e.GetAllMessage(msg);
		printf("%s (%s)\n", EB_EREAD_MSG, msg);
#endif
		return EB_EREAD;
	}

	*lSize = eBE[pos].elem_len;

	return 0;
}

/* ********************************************* */
/* This function will mark the object data on    */
/* the disk as being unused.                     */
/* ********************************************* */
C_DLL_EXPORT int delete_object(int objid)
{
	int pos, i;
	
	pos = findElem(objid);
	if(pos < 0)
		return 0;

	if(fseek(eBF, eBE[pos].elem_offset, SEEK_SET) != 0){
#ifdef DEBUG
		printf("Error seeking to position in store\n");
#endif
		return EB_EWRITE;
	}
	try {
		writeUnused();
	} catch (SsaException e) {
#ifdef DEBUG
		e.GetAllMessage(msg);
		printf("%s (%s)\n", EB_EWRITE_MSG, msg);
#endif
		return EB_EWRITE;
	}

	for(i=pos;i<eBH.elem_count-1;i++)
		memcpy(&eBE[i], &eBE[i+1], sizeof(ssaBtreeElem));

	eBH.elem_count--;
	
	return 0;
}

C_DLL_EXPORT int finalize_datastore(void )
{
	try {
		finishDataStore();
	} catch (SsaException e){
#ifdef DEBUG
		e.GetAllMessage(msg);
		printf("%s (%s)\n", EB_EFINI_MSG, msg);
#endif
		return EB_EFINI;
	}
}		

/* ********************************************* */
/* This function will write the index to the     */
/* datastore.  It will update the header info    */
/* and write the header to the datastore.  It    */
/* will then free the index memory and set the   */
/* datastoreOpen flag to 0 = nothing open.       */
/* ********************************************* */
void finishDataStore(void)
{
	if(datastoreOpen != 1)
		return;

	/* ************************************* */
	/* The header has the offset for where   */
	/* the index should start in the file.   */
	/* We have maintained this as additions  */
	/* and deletions were made to the file.  */
	/* it is guaranteed to be = last data    */
	/* start + last data size.               */
	/* ************************************* */
	if(fseek(eBF, eBH.elem_start, SEEK_SET) != 0)
		throw SsaException("Error seeking to index position in file during finishDataStore", __FILE__, __LINE__, 200);

	if(fwrite(eBE, sizeof(ssaBtreeElem), eBH.elem_count, eBF) != eBH.elem_count)
		throw SsaException("Error writing index to current datastore.", __FILE__, __LINE__, 200);

	eBH.usage = EB_NOINUSE;
	if(fseek(eBF, 0, SEEK_SET) != 0)
		throw SsaException("Error seeking to beginning of file in finishDataStore", __FILE__, __LINE__, 200);

	if(fwrite(&eBH, sizeof(ssaBtreeHeader), 1, eBF) != 1)
		throw SsaException("Error Writing header to current datastore in finishDataStore", __FILE__, __LINE__, 200);

	fclose(eBF);

	free(eBE);

	datastoreOpen = 0;
}	

/* ********************************************* */
/* This function will use a binary search to see */
/* if the element referenced by elem_key exists  */
/* in the current datastore.  If it does, it     */
/* will return the index in the eBE array for it */
/* If not, it will return the appropriate index  */
/* for usage in the addElem function.            */
/* ********************************************* */
int findElem(int elem_key)
{
	int a,b,mid;

	a = 0;
	b = eBH.elem_count - 1;

	mid = (a+b)/2;

	/* *************************************** */
	/* Two test conditions to make things run  */
	/* slightly faster.                        */
	/* *************************************** */
	if(elem_key == eBE[a].elem_key)
		return a;
	if(elem_key == eBE[b].elem_key)
		return b;

	if(elem_key < eBE[a].elem_key)
		return 0;
	if(elem_key > eBE[b].elem_key)
		return -(b+1);

	while(1){
		if(elem_key == eBE[mid].elem_key)
			return mid;
		if(elem_key > eBE[mid].elem_key)
			a = mid;
		if(elem_key < eBE[mid].elem_key)
			b = mid;
		mid = (a+b)/2;
		if((mid == a) || (mid == b))
			break;
	}

	/* ********************************************* */
	/* We got here because mid = a or mid = b.  Need */
	/* to finish with special handling.              */
	/* ********************************************* */

	if(mid == a){
		if(elem_key == eBE[a].elem_key)
			return a;
		if(elem_key < eBE[a].elem_key)
			return -a;
		if(elem_key > eBE[a].elem_key)
			return -(a+1);
	}
	if(mid == b){
		if(elem_key == eBE[b].elem_key)
			return b;
		if(elem_key < eBE[b].elem_key)
			return -b;
		if(elem_key > eBE[b].elem_key)
			return -(b+1);
	}

	throw SsaException("Error occured in findElem.",
		__FILE__, __LINE__, 300);

	return 0;
}

/* ********************************************* */
/* This function will add an element to the      */
/* index list and return it's true index in the  */
/* list.  It will also handle allocating more    */
/* memory for the list as necessary.             */
/* ********************************************* */
int addElem(int pos, int key)
{
	void *tmp;
	int i;

	/* ********************************************* */
	/* they hand us stuff to be added if pos <= 0.   */
	/* we check the zero case for them because it is */
	/* more convienient that way.                    */
	/* ********************************************* */

	/* ********************************************* */
	/* The double if here is necessary because pos<0 */
	/* will cause the array access to blow up. Don't */
	/* turn this into an anded if condition.         */
	/* ********************************************* */
	if(pos == 0)
		if(eBE[pos].elem_key == key)
			return 0;
	// else normal add processing.

	if(eBH.elem_count % 500 == 0){
		/* ***************************** */
		/* Time to get more memory.      */
		/* ***************************** */
		tmp = realloc(eBE, (eBH.elem_count + 500) * sizeof(ssaBtreeElem));
		if(tmp == NULL)
			throw SsaException("Error reallocating memory for index",
				__FILE__, __LINE__, 200);
		eBE = (ssaBtreeElem *)tmp;
	}

	pos = -pos; // safe for zero as well.

	for(i=eBH.elem_count;i>pos;i--)
		memcpy(&eBE[i], &eBE[i-1], sizeof(ssaBtreeElem));

	eBE[pos].elem_key = key;

	eBH.elem_count ++;

	return pos;
}

/* ********************************************* */
/* This function will do the business of writing */
/* the data to the disk.  Each record is has the */
/* format of UsedObjidData.  Used is an unsigned */
/* short, Objid is an unsigned int, and Data is  */
/* the data that we were given to store.         */
/* This function returns the total count of byte */
/* written.                                      */
/* ********************************************* */
int writeData(int objid, char *str, int len)
{
	unsigned short used;
	unsigned int   key;
	int total;

	used = 31415;
	key = (unsigned int)objid;
	if(fwrite(&used, sizeof(unsigned short), 1, eBF) != 1){
#ifdef DEBUG
		printf("%s\n", EB_EWRITE_MSG);	
#endif
		throw SsaException("Error writing used marker to file.",
			__FILE__, __LINE__, 200);
	}		

	if(fwrite(&key, sizeof(unsigned int), 1, eBF) != 1){
#ifdef DEBUG
		printf("%s\n", EB_EWRITE_MSG);	
#endif
		throw SsaException("Error writing key to file.",
			__FILE__, __LINE__, 200);
	}		

	if(fwrite(str, sizeof(char), len, eBF) != len){
#ifdef DEBUG
		printf("%s\n", EB_EWRITE_MSG);	
#endif
		throw SsaException("Error writing data to file.",
			__FILE__, __LINE__, 200);
	}		
	total = sizeof(unsigned short) + sizeof(unsigned int) + len;

	return total;
}

/* ********************************************* */
/* This function will write a marker in the file */
/* to indicate that this piece of the file is    */
/* not used.                                     */
/* ********************************************* */
void writeUnused(void)
{
	unsigned short used;

	used = 41415;
	if(fwrite(&used, sizeof(unsigned short), 1, eBF) != 1){
#ifdef DEBUG
		printf("%s\n", EB_EWRITE_MSG);	
#endif
		throw SsaException("Error writing used marker to file.",
			__FILE__, __LINE__, 200);
	}		
}

/* ********************************************* */
/* This function will read the data from the     */
/* datastore on the disk.                        */
/* ********************************************* */
void readData(int objid, char *str, int len)
{
	unsigned short used;
	unsigned int   key;
	

	if(fread(&used, sizeof(unsigned short), 1, eBF) != 1){
#ifdef DEBUG
		printf("%s\n", EB_EREAD_MSG);	
#endif
		throw SsaException("Error reading used marker from file.",
			__FILE__, __LINE__, 200);
	}		
	
	if(used != 31415){
		throw SsaException("Invalid used marker read from file.",
			__FILE__, __LINE__, 200);
	}

	if(fread(&key, sizeof(unsigned int), 1, eBF) != 1){
#ifdef DEBUG
		printf("%s\n", EB_EREAD_MSG);	
#endif
		throw SsaException("Error reading key from file.",
			__FILE__, __LINE__, 200);
	}		

	if((int)key != objid){
		throw SsaException("Mismatch on key read from file.",
			__FILE__, __LINE__, 200);
	}

	if(fread(str, sizeof(char), len, eBF) != len){
#ifdef DEBUG
		printf("%s\n", EB_EREAD_MSG);	
#endif
		throw SsaException("Error reading data from file.",
			__FILE__, __LINE__, 200);
	}		
 
}

C_DLL_EXPORT void dump_index(void)
{
	int i;
	printf("Here is a dump of the current index:\n");
	printf("Record total is (%d)\n", eBH.elem_count);

	for(i=0;i<eBH.elem_count;i++){
		printf("Record (%d) key (%d) offset (%d) len (%d)\n",
			i, eBE[i].elem_key, eBE[i].elem_offset, eBE[i].elem_len);
	}
}










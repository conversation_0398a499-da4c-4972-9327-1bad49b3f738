//===========================================================================
//
//  File Name:    Setup.rul
//
//  Description:  Blank setup main script file
//
//  Comments:     Blank setup is an empty setup project. If you want to
//				  create a new project via. step-by step instructions use the
//				  Project Assistant.
//
//===========================================================================

// Included header files ----------------------------------------------------
#include "ifx.h"

#define OPTIMIZE_REGKEY "SOFTWARE\\SSA Global\\Optimize"

// Prototype for encryption of password
prototype POINTER SsaGraphSession.makeSessionEnSO(BYREF STRING);

prototype AnalyzeSystem(BYREF LIST, BYREF LIST, BYREF LIST, BYREF string, BYREF string, BYREF string, BYREF string, BYREF string);
prototype BuildAnalysisList(BYREF LIST, BYREF LIST, BYREF NUMBER, BYREF string, BYREF string, BYREF string, BYREF string, BYREF string, BYREF string, BYREF string, BYREF string, BYREF string, BYREF string, BYREF string);
prototype GetServicePack(BYREF string, BYREF number);
prototype GetProductType(BYREF string);
prototype GetComputerName(BYREF string);
prototype GetOracleInfo(BYREF string, BYREF string, BYREF string);
prototype GetAutoCadInfo(BYREF string, BYREF string);
prototype GetDatabaseInfo(BYREF LIST);
prototype GetExcelInfo();
prototype GetOptimizeInfo(BYREF string, BYREF string, BYREF string, BYREF string, BYREF string, BYREF string, BYREF BOOL);
prototype CheckRequirements(string, string, BOOL, string, string, number);
prototype WriteRegistry(string, string, number, string);
prototype FeatureSelectItemWithCheck(string, BOOL);
prototype InstallDatabase(BOOL, BOOL, string, string, string);
prototype RunCommand(string, string, string); 
//prototype Log(STRING, BYREF LIST); 
prototype SetupNewSidRegistry(LIST, string);
prototype GetStringFromList(LIST, BYREF string);
prototype DialogAskDestPath(BYREF string, number, number); 
prototype DialogSecurityInfo(BYREF string, BYREF string, number); 
prototype DialogAskInstallDemo(BYREF BOOL, number, number);
prototype DialogSystemPassword(BYREF string, BOOL, NUMBER);
prototype DialogMeasurements(BYREF string, NUMBER, BYREF BOOL, BYREF BOOL);
prototype DialogNewDatabase(string, BYREF BOOL, number, BYREF string, BYREF string, string);
prototype DialogSetupType(BYREF number, number);
prototype DialogSelectFolder(number, number);
prototype CreateOracleDSN(string, string, string);
//prototype DialogInstallDatabase(BYREF BOOL, number);

string g_szLogin, g_szPassword, g_szSystemPwd, g_szUnits, g_szSid, g_szOracleDir, g_szDBName; 
LIST g_listLog, g_listConnectStrings;
BOOL g_bServerInstalled, g_bInstallDemo, DEBUG;
number g_nSetupType;




//---------------------------------------------------------------------------                                                                        
// OnFirstUIBefore
//
// First Install UI Sequence - Before Move Data
//
// The OnFirstUIBefore event is called by OnShowUI when the setup is
// running in first install mode. By default this event displays UI allowing
// the end user to specify installation parameters.
//
// Note: This event will not be called automatically in a
// program...endprogram style setup.
//---------------------------------------------------------------------------
function OnFirstUIBefore()
    number  nResult, nLevel, nSize;
    string  szTitle, szMsg, szOpt1, szOpt2, szLicenseFile;
    string  szUnits, szName, szCompany, szSerial, szTargetPath, szDir, szFeatures, szTargetdir, szPrevOracleSID, szSid, szDBName, szOperatingSystem, szAutoCadVersion, 
    szOracleDir, szOracleHome, szOracleVersion,
    szPrevHome, szServerName, szComputerName, szPrevMajorVersion, szPrevMinorVersion, szPrevBuild,
    szLogin, szPassword;
    BOOL    bInstallDemo, bLicenseAccepted, bUpgrade, bMetric, bImperial, bOracle, bServerInstalled;	
    LIST	listAnalysis, listConnectStrings;  
begin	
   
    g_nSetupType = 0;	
    szDir = TARGETDIR;
    szName = "";
    szCompany = "";
    bLicenseAccepted = FALSE;
    bInstallDemo = FALSE;
    g_listLog = ListCreate ( STRINGLIST );

	if (CMDLINE % "-g") then
		DEBUG = TRUE;
		MessageBox("Running the installation in debug mode\nCMDLINE=" + CMDLINE, INFORMATION);
	else
		DEBUG = FALSE;
	endif;

	nResult = AnalyzeSystem(g_listLog, listAnalysis, listConnectStrings, szOperatingSystem, szAutoCadVersion, szOracleDir, szOracleVersion, szOracleHome);
    if (nResult < 0) then
    	abort;
    endif;
    
    nResult = GetOptimizeInfo(szPrevHome, szServerName, szComputerName, szPrevMajorVersion, szPrevMinorVersion, szPrevBuild, bServerInstalled);
    if (nResult < 0) then
    	abort;
    endif;
    
// Beginning of UI Sequence
Dlg_Start:
    nResult = 0;

Dlg_SdWelcome:
    szTitle = "";
    szMsg = "";
    //{{IS_SCRIPT_TAG(Dlg_SdWelcome)
    nResult = SdWelcome( szTitle, szMsg );
    //}}IS_SCRIPT_TAG(Dlg_SdWelcome)
    if (nResult = BACK) goto Dlg_Start;

Dlg_SdLicense2:
    szTitle = "";
    szOpt1 = "";
    szOpt2 = "";
    //{{IS_SCRIPT_TAG(License_File_Path)
    szLicenseFile = SUPPORTDIR ^ "license.txt";
    //}}IS_SCRIPT_TAG(License_File_Path)
    //{{IS_SCRIPT_TAG(Dlg_SdLicense2)
    nResult = SdLicense2Rtf( szTitle, szOpt1, szOpt2, szLicenseFile, bLicenseAccepted );
    //}}IS_SCRIPT_TAG(Dlg_SdLicense2)
    if (nResult = BACK) then
        goto Dlg_SdWelcome;
    else
        bLicenseAccepted = TRUE;
    endif;

Dlg_SdRegisterUser:
    szMsg = "";
    szTitle = "";
    //{{IS_SCRIPT_TAG(Dlg_SdRegisterUser)	
    nResult = SdRegisterUserEx( szTitle, szMsg, szName, szCompany, szSerial );
    //}}IS_SCRIPT_TAG(Dlg_SdRegisterUser)
    if (nResult = BACK) goto Dlg_SdLicense2;
                 
Dlg_SdShowInfoList:                               
	nResult = SdShowInfoList("", "Current system configuration", listAnalysis);
	if (nResult = BACK) goto Dlg_SdRegisterUser;

Dlg_SecurityInfo:
	nResult = DialogSecurityInfo(szLogin, szPassword, nResult);
	if (nResult = BACK) goto Dlg_SdShowInfoList;

Dlg_SetupType:                             
	nResult = DialogSetupType(g_nSetupType, nResult);
	if (nResult = BACK) goto Dlg_SecurityInfo;
	
    nResult = CheckRequirements(szOperatingSystem, szAutoCadVersion, bServerInstalled, szOracleDir, szOracleVersion, g_nSetupType);
    if (nResult < 0) goto Dlg_SetupType;

Dlg_SystemPassword:
	nResult = DialogSystemPassword(g_szSystemPwd, g_nSetupType, nResult);
	if (nResult = BACK) goto Dlg_SetupType;
	
Dlg_AskOptions: 
	nResult = DialogMeasurements(szUnits, nResult, bMetric, bImperial);
	if (nResult = BACK) goto Dlg_SystemPassword;
 
Dlg_SdShowDlgEdit2:  
	nResult = DialogNewDatabase(szOracleVersion, bOracle, ListCount(listConnectStrings), szSid, szDBName, szOracleDir);
	if (nResult = BACK) goto Dlg_AskOptions;   

Dlg_AskInstallDemo:
	nResult = DialogAskInstallDemo(bInstallDemo, g_nSetupType, nResult);
	if (nResult = BACK) goto Dlg_SdShowDlgEdit2;	

Dlg_SdAskDestPath2:
	nResult = DialogAskDestPath(szDir, g_nSetupType, nResult);
    if (nResult = BACK) goto Dlg_AskInstallDemo;

Dlg_SdSelectFolder:
 	nResult = DialogSelectFolder(g_nSetupType, nResult);
 	if (nResult = BACK) goto Dlg_SdAskDestPath2;
	
Dlg_ObjDialogs:
    nResult = ShowObjWizardPages(nResult);
    if (nResult = BACK) goto Dlg_SdSelectFolder;  
    
Dlg_SdStartCopy2:
    szTitle = "";
    szMsg = "";
    //{{IS_SCRIPT_TAG(Dlg_SdStartCopy2)	
    nResult = SdStartCopy2( szTitle, szMsg );	
    //}}IS_SCRIPT_TAG(Dlg_SdStartCopy2)
    if (nResult = BACK) goto Dlg_ObjDialogs;

    // Added in 11.0 - Set appropriate StatusEx static text.
    SetStatusExStaticText( SdLoadString( IDS_IFX_STATUSEX_STATICTEXT_FIRSTUI ) );

	if (bInstallDemo) then
		if(bMetric) then
			FeatureSelectItemWithCheck("DrawingFilesMetric" , TRUE );
		else
			FeatureSelectItemWithCheck("DrawingFilesImperial" , TRUE );
		endif;     
	else
		FeatureSelectItemWithCheck("DrawingFilesMetric" , FALSE );
		FeatureSelectItemWithCheck("DrawingFilesImperial" , FALSE );
	endif;
	  
	FeatureSelectItemWithCheck("Database\\ImperialDmp" , FALSE );
	FeatureSelectItemWithCheck("Database\\MetricDmp" , FALSE );
	if ((g_nSetupType=0) || (g_nSetupType=2)) then
		if (szUnits % "metric") then
			FeatureSelectItemWithCheck("Database\\MetricDmp" , TRUE );	
		else
			FeatureSelectItemWithCheck("Database\\ImperialDmp" , TRUE );
		endif;
	endif;
	
	
	if (szAutoCadVersion % "R14") then
		FeatureSelectItemWithCheck("AutoCAD_R14", TRUE);
		FeatureSelectItemWithCheck("AutoCAD_R15_R16", FALSE);
	else
		FeatureSelectItemWithCheck("AutoCAD_R14", FALSE);
		FeatureSelectItemWithCheck("AutoCAD_R15_R16", TRUE);
	endif;
		
	if (FeatureSetTarget( MEDIA, "<DRAWINGSDIR>", TARGETDIR^"Facility"^szDBName) < 0) then
		MessageBox("FeatureSetTarget failed for <DRAWINGSDIR>", SEVERE);
		abort;
	endif;

	if (FeatureSetTarget( MEDIA, "<ORACLE_HOME>", szOracleHome) < 0) then
		MessageBox("FeatureSetTarget failed for <ORACLE_HOME>", SEVERE);
		abort;
	endif;
    
	//If "Add new database" install
    if (g_nSetupType = 2) then
    	FeatureSelectItemWithCheck("Application", FALSE);  
    	FeatureSelectItemWithCheck("AutoCAD_R14", FALSE);
    	FeatureSelectItemWithCheck("AutoCAD_R15_R16", FALSE);
    endif;
  
  	// Set global variables for use by WriteRegistry
 	g_szLogin = szLogin;
 	g_szPassword = szPassword;
 	g_szUnits = szUnits;
 	g_szDBName = szDBName;
 	g_bServerInstalled = bServerInstalled;  
 	g_szSid = szSid;
 	g_listConnectStrings = listConnectStrings;
	g_szOracleDir = szOracleDir; 
	g_bInstallDemo = bInstallDemo; 
	g_szDBName = szDBName;
    return 0;
end;

function FeatureSelectItemWithCheck(szFeatureName, bSelect)
	number nResult;
	string szMsg;
begin
	nResult = FeatureSelectItem( MEDIA, szFeatureName, bSelect);
	if (nResult < 0) then
		Sprintf(szMsg, "FeatureSelectItem failed for %s with error %d", szFeatureName, nResult);
		MessageBox(szMsg, SEVERE);
		return -1;
	else
		return 0;
	endif;
end;

function DialogSelectFolder(nSetupType, nResult)
begin
	if ((nSetupType=0) || (nSetupType=1)) then
	 	if (SHELL_OBJECT_FOLDER = "") then
	 		SHELL_OBJECT_FOLDER = "SSA Global Technologies";
	 	endif;
	 	nResult = SdSelectFolder( "", "", SHELL_OBJECT_FOLDER );  
 	endif;
 	return nResult;
end;


function DialogSetupType(nSetupType, nResult)
	string szMsg;
	BOOL bvCheck1, bvCheck2, bvCheck3;
begin
	bvCheck1 = FALSE;
	bvCheck2 = FALSE;
	bvCheck3 = FALSE;
	switch (nSetupType)
	case 0:
		bvCheck1 = TRUE;
	case 1:
		bvCheck2 = TRUE;
	case 2:
		bvCheck3 = TRUE;
	endswitch;
	szMsg = "Choose to install the Optimize application and/or database"; 
	
	nResult = AskOptions(EXCLUSIVE, szMsg, 
				"Install application and new database", bvCheck1, 
				"Install application using an existing database", bvCheck2,
				"Install an additional Optimize database", bvCheck3); 
	if (nResult != BACK) then
		if (bvCheck1) then
			nSetupType = 0;
		elseif (bvCheck2) then
			nSetupType = 1;
		else
			nSetupType = 2;
		endif;
	endif;   
	return nResult;
end;

function DialogSecurityInfo(szLogin, szPassword, nResult)  
begin
	if (szLogin = "") then
		szLogin = "ssauser";
	endif;
	repeat
		nResult = SdShowDlgEdit2 ( "Security Information" , 
		"Enter the login and password you wish to use to access the " + IFX_PRODUCT_NAME + " application" , 
		"Login" , "Password" , szLogin , szPassword );
		if (szLogin="" && nResult!= BACK) then
			MessageBox("Please specify a login", WARNING);
		elseif (szPassword="" && nResult!= BACK) then
			MessageBox("Please specify a password", WARNING);
		endif;
	until (szLogin!="" && szPassword!="") || nResult = BACK;
	return nResult;
end; 


function DialogSystemPassword(szPassword, nSetupType, nResult)
	string szMsg; 
begin
	if ((nSetupType=0) || (nSetupType=2)) then
		szMsg = "Enter the password for the Oracle SYSTEM login. This password is used to install the database and not stored after installation.";
		repeat
			nResult = EnterPassword(szMsg, "", szPassword);
			if (szPassword="" && nResult!= BACK) then
				MessageBox("Please specify a password", WARNING);
			endif;
		until (szPassword!="") || nResult = BACK;  
	endif;
	return nResult;
end; 


////////////////////////////////////////////////////////////////////////////
//
// Function: DialogAskInstallDemo
//
//  Purpose: This function asks the user if a demo data need to be installed.
//
//
///////////////////////////////////////////////////////////////////////////////
function DialogAskInstallDemo(bInstallDemo, nSetupType, nResult)
	BOOL bSelectSecond;
begin
	bSelectSecond = !bInstallDemo;
	nResult = AskOptions(EXCLUSIVE, "Do you want to install the demo data and drawings?", 
			"Yes", bInstallDemo,
			"No", bSelectSecond);
	return nResult;
end;


function DialogAskDestPath(szDir, nSetupType, nResult)
begin
	if ((nSetupType=0) || (nSetupType=1)) then
		nResult = SdAskDestPath2( "", "", szDir );
		if (nResult != BACK) then
	    	TARGETDIR = szDir;
	    endif;
    endif;
    return nResult;
end;

         
function DialogMeasurements(szUnits, nResult, bMetric, bImperial) 
begin
	// If user has not made a choice
	if (!bImperial && !bMetric) then
		bImperial = TRUE;
	endif;
		    
	nResult = AskOptions ( EXCLUSIVE , "Select the unit of measurement to be used", 
			"Imperial" , bImperial , "Metric" , bMetric );
	if (bMetric) then
		szUnits = "Metric";
	else
		szUnits = "Imperial"; 
	endif;              

	return nResult;
end;


///////////////////////////////////////////////////////////////////////////////
//
// Function: DialogNewDatabase
//
//  Purpose: This function asks the user for the sid name, Database name and
//  the location for new database instance datafiles
//
//
///////////////////////////////////////////////////////////////////////////////

function DialogNewDatabase(szOracleVersion, bOracle, nTotalDB,
	szSid, szDBName, szOracleDir)
	NUMBER nResult, nReturn, nIndx, nOracle, nMatchLen, nLen;
	STRING szMsg, szDir, szResult, szIndx, szDBIdx, szDBInfoTemp;
	STRING strSid, substrSid, strDBName, strDBType, szDelimiterSet, szStrTemp, strAtSign;
	LIST listSidID;
	BOOL bSQLServer;
begin
	if nTotalDB = 0 then
		if (szSid = "") then
			szSid = "optimize";
		endif;
		if (szDBName = "") then
			szDBName = "optimize";
		endif;
	else
		NumToStr(szStrTemp, nTotalDB+1);
		szDBName = "opti" + szStrTemp; 
		szSid = "opti" + szStrTemp;
		
		RegDBSetDefaultRoot(HKEY_LOCAL_MACHINE);
		
		for nIndx = 0 to nTotalDB - 1
			NumToStr(szIndx, nIndx);
			szDBIdx = "DB" + szIndx;
		
			nResult = RegDBGetKeyValueEx (OPTIMIZE_REGKEY, szDBIdx, nMatchLen, szDBInfoTemp, nLen);
			szDelimiterSet = "|";
   			listSidID = ListCreate( STRINGLIST );
   			StrGetTokens( listSidID, szDBInfoTemp, szDelimiterSet );
   			ListGetFirstString (listSidID, strDBName);
   			strSid = "";
   			ListGetNextString (listSidID, strSid);
   			StrSub (strAtSign, strSid, 0, 1);
   			substrSid = "";
   			if (strAtSign = "@" ) then
   				StrSub (substrSid, strSid, 1, StrLength(strSid)-1);
   			else
   				substrSid = strSid;
   			endif;
   			ListGetNextString (listSidID, strDBType);
			Sprintf(szStrTemp, "Existing Sid Name: %s   Type: %-12s\n",substrSid,strDBType);
 			szMsg = szMsg + szStrTemp;

		endfor;
	
	endif;
	
	
	repeat
		repeat
			nResult = SdShowDlgEdit2("Database information", "Enter information for the database",	
					"Sid Name", "DB Name", szSid, szDBName);
				
				if (nResult != BACK) then
					if (szSid = "") then
						MessageBox("Database Sid is a required field.", WARNING);
					elseif (szDBName = "") then
						MessageBox("Database Name is a required field.", WARNING);
					elseif (ExistsDir(szOracleDir + "\\" + szDBName)<0) then
						MessageBox("The folder " + szOracleDir + "\\" + szDBName + " does not exist\n" +
							"Please specify the name of an existing but empty database", WARNING);
					endif;	
				endif;
		until ((szSid != "" && szDBName != "" && (ExistsDir(szOracleDir + "\\" + szDBName)>=0)) || nResult = BACK);      		

		if (nResult != BACK) then
			if ((g_nSetupType=0) || (g_nSetupType=2)) then
				nReturn = RunCommand("ConnectTest", "echo quit|SqlPlus -L system/" + g_szSystemPwd + "@" + szSid, SUPPORTDIR);
			else
				nReturn = 0;
			endif;
		endif;
	until (nReturn = 0 || nResult = BACK);  
	
	return nResult;		
end;


function InstallDatabase(bInstallDemo, bMetric, szSid, szDBName, szDBLocation)
	STRING szCommand, szPath, szDmpFileName, szWorkDir, szUpperCaseLogin;
	NUMBER nResult, nvResult;
	NUMBER copyResult;
begin

	if (DEBUG) then
		if (AskYesNo("Run database install?", YES) = NO) then
			return 0;
		endif;
	endif;
	
	SdShowMsg("Setting up the " + IFX_PRODUCT_NAME + " database", TRUE);
	
	
	szWorkDir = TARGETDIR ^ "install" ^ "database";
	ChangeDirectory(szWorkDir);
	
	// Replace tags in CreateUser.sql
	StrToUpper(szUpperCaseLogin, g_szLogin);
    szCommand = SUPPORTDIR ^ "ReplaceTextInFile.vbs";
    szCommand = szCommand + " CreateUser.sql $(USER) " + szUpperCaseLogin;
    szCommand = "wscript.exe " + szCommand;
    RunCommand("ReplaceUserSql", szCommand, szWorkDir);
    
    szCommand = SUPPORTDIR ^ "ReplaceTextInFile.vbs";
    szCommand = szCommand + " CreateUser.sql $(PASSWORD) " + g_szPassword;
    szCommand = "wscript.exe " + szCommand;
    RunCommand("ReplacePasswordSql", szCommand, szWorkDir);
    
    szPath = szDBLocation;                                      
    LongPathToQuote(szPath, TRUE);
    szCommand = SUPPORTDIR ^ "ReplaceTextInFile.vbs";
    szCommand = szCommand + " CreateUser.sql $(DATA_DIR) " + szPath;
    szCommand = "wscript.exe " + szCommand;
    RunCommand("ReplaceDataDirSql", szCommand, szWorkDir);
	
	// Replace tags in import.cmd
	szCommand = SUPPORTDIR ^ "ReplaceTextInFile.vbs";
	szCommand = szCommand + " importCommand.bat $(USER) " + g_szLogin;
    szCommand = "wscript.exe " + szCommand;
    RunCommand("ReplaceUserCmd", szCommand, szWorkDir);
	
	szCommand = SUPPORTDIR ^ "ReplaceTextInFile.vbs";
	szCommand = szCommand + " importCommand.bat $(DB_NAME) " + szDBName;
    szCommand = "wscript.exe " + szCommand;
    RunCommand("ReplaceDbNameCmd", szCommand, szWorkDir);

	szCommand = SUPPORTDIR ^ "ReplaceTextInFile.vbs";
	szCommand = szCommand + " importCommand.bat $(SYSTEM_PWD) " + g_szSystemPwd;
    szCommand = "wscript.exe " + szCommand;
    RunCommand("ReplaceSystemPwdCmd", szCommand, szWorkDir);

 	if (bMetric) then
 		if (bInstallDemo) then
 			szDmpFileName = "metric102.dmp";
 		else
 			szDmpFileName = "metric102nodemo.dmp";
 		endif;
 	else
 		if (bInstallDemo) then
 			szDmpFileName = "imperial102.dmp";
 		else
 			szDmpFileName = "metric102nodemo.dmp";   
 		endif;
 	endif; 
 	
	szCommand = SUPPORTDIR ^ "ReplaceTextInFile.vbs";
	szCommand = szCommand + " importCommand.bat $(DMP_FILE_NAME) " + szDmpFileName;
    szCommand = "wscript.exe " + szCommand;
    RunCommand("ReplaceDmpFileNameCmd", szCommand, szWorkDir);
                 
                 
 	RunCommand("CreateUserSql", "sqlplus system/" + g_szSystemPwd + "@" + szDBName + " @CreateUser.sql", szWorkDir);
 	//Delete the password after use 
    szCommand = SUPPORTDIR ^ "ReplaceTextInFile.vbs";
    szCommand = szCommand + " CreateUser.sql " + g_szPassword + " [deleted]";
    szCommand = "wscript.exe " + szCommand;
    RunCommand("DeletePasswordSql", szCommand, szWorkDir);
 	
 	
  	RunCommand("ImportCmd", "importCommand.bat", szWorkDir);
  	//Delete the password after use
	szCommand = SUPPORTDIR ^ "ReplaceTextInFile.vbs";
	szCommand = szCommand + " importCommand.bat " + g_szSystemPwd + " [deleted]";
    szCommand = "wscript.exe " + szCommand;
    RunCommand("DeleteSystemPwdCmd", szCommand, szWorkDir);    
    
	SdShowMsg("", FALSE);
	return nResult;
end;

function GetStringFromList(lst, szMsg)
	string szLine; 
	number nResult;
begin   
	szMsg = "";
	nResult = ListGetFirstString(lst, szLine);
	while (nResult != END_OF_LIST);
		szMsg = szMsg + szLine + "\n";
		nResult = ListGetNextString(lst, szLine);
	endwhile;
end;

///////////////////////////////////////////////////////////////////////////////
//
// Function: RunCommand
//
//  Purpose: This is a generic function to run a batch command.  It wraps the
//			 command with runquiet.exe program which prevents a window from
//			 being displayed.
//
///////////////////////////////////////////////////////////////////////////////
function RunCommand(name, cmd, path)
	STRING szRunCommand, szFile, szOutFile, szErrFile, szLine, newCmd, szSavePath;
	STRING szReturnLine, szErrMsg;
	LIST listBatchFile, listStdOut, listStdErr, listAll;
	NUMBER nResult, nLineNumber, nReturn;
begin
	
	listStdOut = ListCreate(STRINGLIST);
	listStdErr = ListCreate(STRINGLIST);
	listAll = ListCreate(STRINGLIST);
	
	szFile = SUPPORTDIR ^ name + "_rq.bat";
	szOutFile = SUPPORTDIR ^ name + "_rq.out";
	LongPathToQuote(szOutFile, TRUE);
	szErrFile = SUPPORTDIR ^ name + "_rq.err";
	LongPathToQuote(szErrFile, TRUE);
	
	// Add the cmd to a batch file
	listBatchFile = ListCreate(STRINGLIST);
	ListAddString(listBatchFile, "set path=" + WINSYSDIR + ";" + WINDIR + ";%PATH%", AFTER);	
	if (path != "") then
		ListAddString(listBatchFile, "set path=%path%;" + path, AFTER);
	endif;
	
	newCmd = cmd + " > " + szOutFile;
	newCmd = newCmd + " 2>" + szErrFile;
	ListAddString(listBatchFile, newCmd, AFTER);
	ListAddString(listBatchFile, "if errorlevel 1 (", AFTER);
    ListAddString(listBatchFile, "	echo FAIL>result.txt", AFTER);
	ListAddString(listBatchFile, ") else (", AFTER);
    ListAddString(listBatchFile, "	echo SUCCESS>result.txt", AFTER);
	ListAddString(listBatchFile, ")", AFTER);
	
	// Write the batch file
	nResult = ListWriteToFile(listBatchFile, szFile);
	if (nResult < 0) then
		MessageBox("Error writing to " + szFile, SEVERE);
		return nResult;
	endif;
	
	// Run the batch file	
	//szRunCommand = TARGETDIR ^ "Install" ^ "runquiet.exe";
	GetCurrentDir(szSavePath);
	ChangeDirectory(path);
	LongPathToShortPath(szRunCommand);
	LongPathToShortPath(szFile);
	nResult = LaunchAppAndWait(WINSYSDIR^"cmd.exe" , "/c " + szFile , LAAW_OPTION_WAIT|LAAW_OPTION_SHOW_HOURGLASS|LAAW_OPTION_NO_CHANGEDIRECTORY|LAAW_OPTION_MINIMIZED );
	if (nResult < 0) then
		MessageBox("Error running command: " + cmd, SEVERE);
	endif;
	if (!DEBUG) then 
		DeleteFile(szFile);
	endif;
	ChangeDirectory(szSavePath);
	Delay(1);
	
	// Read the out file
	LongPathToQuote(szOutFile,FALSE);
	LongPathToShortPath(szOutFile);
	ListReadFromFile(listStdOut, szOutFile); 
	if (!DEBUG) then 
		DeleteFile(szOutFile);
	endif;
	
	// Read the error file
	LongPathToQuote(szOutFile,FALSE);
	LongPathToShortPath(szErrFile);
	ListReadFromFile(listStdErr, szErrFile);
	if (!DEBUG) then 
		DeleteFile(szErrFile);
	endif;
	
	if (ListCount(listStdOut) > 0) then
		ListAddString(listAll, "Output:", AFTER);
		ListAddString(listAll, "", AFTER);
	
 		nResult = ListGetFirstString(listStdOut, szLine);
 		while (nResult != END_OF_LIST);
			ListAddString(listAll, szLine, AFTER);
			nResult = ListGetNextString(listStdOut, szLine);
		endwhile;
		ListAddString(listAll, "", AFTER);
	endif;
	
	if (ListCount(listStdErr) > 0) then
		ListAddString(listAll, "Errors:", AFTER);
		ListAddString(listAll, "", AFTER);
	
	
		nResult = ListGetFirstString(listStdErr, szLine);
		while (nResult != END_OF_LIST);
			ListAddString(listAll, szLine, AFTER);
			nResult = ListGetNextString(listStdErr, szLine);
		endwhile;
		ListAddString(listAll, "", AFTER);
	endif;
	
	if (FileGrep(path^"result.txt", "FAIL", szReturnLine, nLineNumber, RESTART) = 0) then
		//if (ListCount(listAll) > 0) then
			//Log(cmd, listAll);
			ListSetIndex(listAll, 0);
			ListAddString(listAll, cmd, BEFORE);
			ListAddString(listAll, "", AFTER);
			if (ListCount(listAll)>20) then
				SdShowInfoList("Installation error", "Error running: " + cmd, listAll);
			else
				GetStringFromList(listAll, szErrMsg);
				MessageBox("Error running: " + cmd + "\n" +
					"__________________________________\n\n" +
					szErrMsg, WARNING);
			endif;
		//endif;    
			nReturn = -1;
	else
		if (DEBUG) then 
			if (ListCount(listAll)>20) then
				SdShowInfoList("DEBUG Info", "Result of running: " + cmd, listAll);
			else
				GetStringFromList(listAll, szErrMsg);
				MessageBox("DEBUG\nResult of running: " + cmd + "\n" +
					"__________________________________\n\n" +
					szErrMsg, INFORMATION);
				endif;
		else
			DeleteFile(path^"result.txt");
		endif;
		nReturn = 0;
	endif;
		
	ListDestroy(listStdOut);
	ListDestroy(listStdErr);
	ListDestroy(listAll);
	ListDestroy(listBatchFile);
	
	return nReturn;
	
end;

//---------------------------------------------------------------------------
// OnSetTARGETDIR
//
// OnSetTARGETDIR is called directly by the framework to initialize
// TARGETDIR to it's default value.
//
// Note: This event is called for all setups.
//---------------------------------------------------------------------------
function OnSetTARGETDIR()
number nId, nIgnore, nResult;
string szId, szTARGETDIR;
begin

    // In maintenance mode the value of TARGETDIR is read from the log file.
    if( MAINTENANCE ) then
        return ISERR_SUCCESS;
    endif;

    // Set TARGETDIR to script default.
    TARGETDIR = "<FOLDER_APPLICATIONS>\\<IFX_COMPANY_NAME>\\<IFX_PRODUCT_NAME>";

    // Read TARGETDIR from the media.
    nResult = MediaGetData( MEDIA, MEDIA_FIELD_TARGETDIR, nIgnore, szTARGETDIR );
          
    // Use the TARGETDIR from the media if anything was read.
    if( nResult >= ISERR_SUCCESS && StrLengthChars( szTARGETDIR ) ) then
        TARGETDIR = szTARGETDIR;
    endif;
        
	// Customize the default TARGETDIR for multi-instance application.
	// TODO: If you want something different customize the code below.	
	/* Ole Mortensen: Commented out the InstallShield code
	if( MAINT_OPTION = MAINT_OPTION_MULTI_INSTANCE  && MULTI_INSTANCE_COUNT > 0) then

		// Start with the current multi-instance count plus one.
		nId = MULTI_INSTANCE_COUNT + 1;

		// Find a unique TARGETDIR.
		while( ExistsDir( TARGETDIR ) = EXISTS )
			
			// Convert to string.
			NumToStr( szId, nId );
			
			// Update IFX_MULTI_INSTANCE_SUFFIX
			IFX_MULTI_INSTANCE_SUFFIX = "_" + szId;
		
			// Update TARGETDIR
			TARGETDIR = TARGETDIR + IFX_MULTI_INSTANCE_SUFFIX;
			
			// Update nId
			nId = nId + 1;

		endwhile;

	endif;
    */
end;
//---------------------------------------------------------------------------
// OnSetUpdateMode
//
// OnSetUpdateMode is called directly by the framework to set the UPDATEMODE
// InstallShield system variable appropriately to control which UI events
// are called by OnShowUI.
//
// Note: This event is called for all setups.
//---------------------------------------------------------------------------
function OnSetUpdateMode()
	number	nIgnore, nMediaFlags, nInstalledVersion, nUpdateVersion, nResult;
	string	szVersion, szIgnore, szMsg;
begin
	
	UPDATEMODE = FALSE; // Non-update mode by default.

	// Get the media flags.
	MediaGetData( MEDIA, MEDIA_FIELD_MEDIA_FLAGS, nMediaFlags, szIgnore );

	if( ! ( nMediaFlags & MEDIA_FLAG_UPDATEMODE_SUPPORTED ) ) then
		return ISERR_SUCCESS; // Update mode not supported by the setup.
	endif;

	// TODO: If you are updating an application that was installed by a previous
	// version of InstallShield, IFX_INSTALLED_VERSION will be empty, and
	// VERSION_COMPARE_RESULT_NOT_INSTALLED will be returned by
	// VerProductCompareVersions. Change the value of IFX_INSTALLED_VERSION (and
	// IFX_INSTALLED_DISPLAY_VERSION) here based on application specific version
	// information determined by the setup. Only do this if IFX_INSTALLED_VERSION
	// is empty.
	//if ( !StrLengthChars( IFX_INSTALLED_VERSION ) && MAINTENANCE ) then
	//	IFX_INSTALLED_VERSION = "X.XX.XXX";
	//	IFX_INSTALLED_DISPLAY_VERSION = IFX_INSTALLED_VERSION;
	//endif;

	// Verify that the installed version is valid.
	if( !StrLengthChars( IFX_INSTALLED_VERSION ) && MAINTENANCE ) then
		// If this error occurs, IFX_INSTALLED_VERSION needs to be set manually.
		szMsg = SdLoadString( IDS_IFX_ERROR_UPDATE_NO_INSTALLED_VERSION );
		MessageBox( szMsg, SEVERE );
		abort;
	endif;

	// Verify that the product version is valid.
	if( !StrLengthChars( IFX_PRODUCT_VERSION ) ) then
		// If this error occures, IFX_PRODUCT_VERSION was not initialized correctly.
		szMsg = SdLoadString( IDS_IFX_ERROR_UPDATE_NO_PRODUCT_VERSION );
		MessageBox( szMsg, SEVERE );
		abort;
	endif;

	// Do the version comparison.
	nResult = VerProductCompareVersions();

	// Make sure that valid data was returned by VerProductCompareVersions
	if( nResult < ISERR_SUCCESS ) then
		szMsg = SdLoadString( IDS_IFX_ERROR_UPDATE_VERSION_COMPARE_FAILURE );
		MessageBox( szMsg, SEVERE );
		abort;
	endif;

	// Set update mode if this is a differential media or the product is already installed and the versions do not match.
	UPDATEMODE = ( nMediaFlags & MEDIA_FLAG_FORMAT_DIFFERENTIAL || ( MAINTENANCE && ( nResult != VERSION_COMPARE_RESULT_SAME ) ) );

end;

///////////////////////////////////////////////////////////////////////////////
//
// Function: AnalyzeSystem
//
//  Purpose: This function obtains the current system configuration.
//
//
///////////////////////////////////////////////////////////////////////////////
function AnalyzeSystem(listLog, listAnalysis, listConnectStrings, szOperatingSystem, szAutoCadVersion, szOracleDir, szOracleVersion, szOracleHome)
	NUMBER nResult, n, nMemory, nServicePack;
	STRING szResult, szSQLServerDir, szAutoCadDir, szJavaVersion, szJavaMicroVersion, szJavaDir,
		   szCPU, szComputerName, szOSVersion, szServicePack, 
		   szProductType, szSQLServerVersion, szMsg;
begin

	nResult = RegDBSetDefaultRoot(HKEY_LOCAL_MACHINE);

	// Get Operating System    

	n = GetSystemInfo(OS, nResult, szResult);
	if (n < 0) then
		szOperatingSystem = "Unavailable";
	elseif (nResult = IS_WINDOWS) then
		szOperatingSystem = "Windows 3.1";
	elseif (nResult = IS_WINDOWSNT) then
		szOperatingSystem = "Windows NT";
	elseif (nResult = IS_WINDOWS95) then
		szOperatingSystem = "Windows 95";
	else
		szOperatingSystem = "Unavailable";
	endif;
	
	n = GetSystemInfo(WINMAJOR, nResult, szOSVersion);
	if (n < 0 || szOSVersion = "") then
			szOSVersion = "Unavailable";
	endif;
	
	// Get Service Pack
	if (szOperatingSystem = "Windows NT") then
		GetServicePack(szServicePack, nServicePack);
	endif;
	
	// Get Product Type
	if (szOperatingSystem = "Windows NT") then	
		GetProductType(szProductType);
	endif;
	
	// Get Memory
	GetSystemInfo(EXTENDEDMEMORY, nMemory, szResult);
	
	// Get Processor    
	GetSystemInfo(CPU, nResult, szResult);
	if (nResult = IS_PENTIUM) then
		szCPU = "Pentium Class";
	elseif (nResult = IS_486) then
		szCPU = "486";
	else
		szCPU = "Unknown";
	endif;
	
	// Check computer name
	if (GetComputerName(szComputerName) < 0) then
		szComputerName = "Unavailable";
	endif;
	
	// Check Succeed Server
	/*TODO Implement if we are going to support update
	if (GetSucceedServerInfo() < 0) then
		szPrevServerMajorVersion = "Not Installed";
		szPrevServerMinorVersion = "N/A";
		szPrevServerHome = "N/A";
	endif;
	
	// Check Succeed Client
	if (GetSucceedClientInfo() < 0) then
		szPrevClientMajorVersion = "Not Installed";
		szPrevClientMinorVersion = "N/A";
		szPrevClientHome = "N/A";
	endif;
	
	// Check Forte
	if (GetForteInfo() < 0) then
		szForteDir = "N/A";
		szForteBinDir = "N/A";
	endif;
	*/
	
	// Check Oracle
	if (GetOracleInfo(szOracleVersion, szOracleDir, szOracleHome) < 0) then
		szOracleDir = "N/A";
	endif;
	
	// Check Autocad
	if (GetAutoCadInfo(szAutoCadVersion, szAutoCadDir) < 0) then
		szAutoCadDir = "N/A"; 
	endif;
	

	// Check Database Information
	GetDatabaseInfo(listConnectStrings);
	
	
	BuildAnalysisList(listLog, listAnalysis, nMemory, szOSVersion, szOperatingSystem, szServicePack, szProductType, szOracleVersion, szSQLServerVersion, szAutoCadVersion, szAutoCadDir, szOracleDir, szSQLServerDir, szCPU);
end;

///////////////////////////////////////////////////////////////////////////////
//
// Function: BuildAnalysisList
//
//  Purpose: This function formats the results of the system configuration
//			 analysis.
//
//
///////////////////////////////////////////////////////////////////////////////
function BuildAnalysisList(listLog, listAnalysis, nMemory, szOSVersion, szOperatingSystem, szServicePack, szProductType, szOracleVersion, szSQLServerVersion, szAutoCadVersion, szAutoCadDir, szOracleDir, szSQLServerDir, szCPU)
	STRING szTemp, szTab, szMsg;
begin
	     
	listAnalysis = ListCreate(STRINGLIST);
	if (listAnalysis = LIST_NULL) then
		MessageBox("Failed to create list for showing system configuration", SEVERE);
		return -1;
	endif;
	
	szTab = "    ";
	
	szMsg = "Hardware";
	ListAddString(listAnalysis, szMsg, AFTER);
	szMsg = szTab + "Processor: " + szCPU;
	ListAddString(listAnalysis, szMsg, AFTER);
	NumToStr(szTemp, nMemory / 1000);
	szMsg = szTab + "Memory: " + szTemp + "MB";
	ListAddString(listAnalysis, szMsg, AFTER);
	szMsg = "";
	ListAddString(listAnalysis, szMsg, AFTER);
	
	szMsg = "Operating System";
	ListAddString(listAnalysis, szMsg, AFTER);
	if (szOperatingSystem = "Windows NT") && (szOSVersion = "5.0") then
		szMsg = szTab + "Windows" + " ";
	else
		szMsg = szTab + szOperatingSystem + " ";
	endif;
	
	if (szProductType = "WinNT") then
		szMsg = szMsg + "Workstation";
	else
		szMsg = szMsg + "Server";
	endif;
	if (szOperatingSystem = "Windows NT") && (szOSVersion = "5.0") then
		szOSVersion = "2000";
	endif;
	szMsg = szMsg + " " + szOSVersion;
	ListAddString(listAnalysis, szMsg, AFTER);
	szMsg = szTab + szServicePack;
	ListAddString(listAnalysis, szMsg, AFTER);
	szMsg = "";
	ListAddString(listAnalysis, szMsg, AFTER);

	szMsg = "Oracle";
	ListAddString(listAnalysis, szMsg, AFTER);
	szMsg = szTab + "Version: " + szOracleVersion;
	ListAddString(listAnalysis, szMsg, AFTER);
	if (szOracleVersion != "Not Installed") then
		szMsg = szTab + "Path: " + szOracleDir;
		ListAddString(listAnalysis, szMsg, AFTER);
	endif;
	szMsg = "";
	ListAddString(listAnalysis, szMsg, AFTER);
	
	szMsg = "AutoCad";
	ListAddString(listAnalysis, szMsg, AFTER);
	if (szAutoCadVersion = "R15.0") then
		szMsg = szTab + "Version: " + "2000";
	else
		szMsg = szTab + "Version: " + szAutoCadVersion;
	endif;
	
	ListAddString(listAnalysis, szMsg, AFTER);
	if (szAutoCadVersion != "Not Installed") then
		szMsg = szTab + "Path: " + szAutoCadDir;
		ListAddString(listAnalysis, szMsg, AFTER);
	endif;
	szMsg = "";
	ListAddString(listAnalysis, szMsg, AFTER);  
	
	szMsg = "Microsoft Excel";
	ListAddString(listAnalysis, szMsg, AFTER);
	if (GetExcelInfo() < 0) then
		szMsg = szTab + "Is not installed";
	else
		szMsg = szTab + "Is installed";
	endif;   
	ListAddString(listAnalysis, szMsg, AFTER);
	szMsg = "";
	ListAddString(listAnalysis, szMsg, AFTER);
end;

function CheckRequirements(szOperatingSystem, szAutoCadVersion, bServerInstalled, szOracleDir, szOracleVersion, nSetupType)
	string szMsg, szDisk, szDiskSpace;
	number nDiskSpace, nResult;
begin
 
 	if (nSetupType = 2) then
		if ( !bServerInstalled) then
			MessageBox("Please run the " + IFX_PRODUCT_NAME + " installation before running this installation to add another database.", SEVERE);
			return -1;
		endif;
	endif;
	
	// Check requirements
	if (szOperatingSystem != "Windows NT" ) then
		szMsg = "The " + IFX_PRODUCT_NAME + " server component must be installed on " +
			" Windows NT or Windows 2000";
		MessageBox(szMsg, SEVERE);
		return -1;
	endif;	

	if (szAutoCadVersion != "R14.0" && szAutoCadVersion != "R15.0" && !(szAutoCadVersion % "R16")) then
		szMsg = IFX_PRODUCT_NAME + " requires Autocad R14, 2000i, or 2005. " +
				"Please install one of these versions " +
				"and restart the " + IFX_PRODUCT_NAME + " installation.";
		MessageBox(szMsg, SEVERE);
		return -1;
	endif;

	if (!bServerInstalled) then
		GetDisk(szOracleDir, szDisk);
   		
   		nDiskSpace = GetDiskSpace(szDisk);
   		if (nResult < 0) then
   			MessageBox("Unable to determine the amount of disk space on " +
   				szDisk + ".", INFORMATION);
   		elseif (nDiskSpace < 400000000) then
   			nDiskSpace = nDiskSpace / 1000000;
   			NumToStr(szDiskSpace, nDiskSpace);
   			szMsg = IFX_PRODUCT_NAME + " requires at least " +
   				"400Mb of free disk space.  Setup has determined that you have " +
   				szDiskSpace + "MB free.  Free some disk space and restart the " +
   				IFX_PRODUCT_NAME + " installation.";
   			MessageBox(szMsg, SEVERE);
  			//return -1;  
  			return 0;
   		endif;
   	endif;

	if ((g_nSetupType=1) || (g_nSetupType=3)) then
		if (szOracleVersion = "Not Installed")then
			szMsg = IFX_PRODUCT_NAME + " requires Oracle 10g\n" +
					"Please install Oracle and restart the " + IFX_PRODUCT_NAME + " installation.";
			MessageBox(szMsg, SEVERE);
			return -1;
		endif;	
	endif;
	
	if ((g_nSetupType=1) || (g_nSetupType=3)) then
		if (GetExcelInfo() < 0) then
			szMsg = IFX_PRODUCT_NAME + " requires Microsoft Excel\n" +
					"Please install Excel and restart the " + IFX_PRODUCT_NAME + " installation.";		
			MessageBox(szMsg, SEVERE);
			return -1;
		endif;	
	endif;
end;

function GetOptimizeInfo(szPrevHome, szServerName, szComputerName, szPrevMajorVersion, szPrevMinorVersion, szPrevBuild, bServerInstalled)
	STRING szTemp, szSucceedReg, szName;
	number nResult, nMatchLen, nLen;
begin

	szSucceedReg = OPTIMIZE_REGKEY;
	RegDBSetDefaultRoot(HKEY_LOCAL_MACHINE);
	nResult = RegDBGetKeyValueEx(szSucceedReg, "Home",
			nMatchLen, szPrevHome, nLen);
	if (nResult < 0 || szPrevHome = "") then
		bServerInstalled = FALSE;
	else
		LongPathToQuote(szPrevHome, FALSE);
		bServerInstalled = TRUE;
		
		StrToUpper (szServerName, szComputerName);
	
		nResult = RegDBGetKeyValueEx(szSucceedReg, "MajorVersion",
			nMatchLen, szPrevMajorVersion, nLen);
		
		nResult = RegDBGetKeyValueEx(szSucceedReg, "MinorVersion",
			nMatchLen, szPrevMinorVersion, nLen);

		nResult = RegDBGetKeyValueEx(szSucceedReg, "BuildNumber",
			nMatchLen, szPrevBuild, nLen);
	endif;	
	
	return 0;
		
end;

///////////////////////////////////////////////////////////////////////////////
//
// Function: GetServicePack
//
//  Purpose: This function determines the NT service pack that is installed.
//
//
///////////////////////////////////////////////////////////////////////////////
function GetServicePack(szServicePack, nServicePack)
	STRING szTemp;
	NUMBER n, nResult, nMatchLen, nLen;
begin

	nServicePack = -1;
	nResult = RegDBGetKeyValueEx("SOFTWARE\\Microsoft\\Windows NT\\CurrentVersion",
		"CSDVersion", nMatchLen, szServicePack, nLen);
	if (nResult < 0) then
		szServicePack = "No service pack is installed.";
		return -1;
	else
		n = StrLength(szServicePack) - 1;
		StrSub(szTemp, szServicePack, n, 1);
		nResult = StrToNum(nServicePack, szTemp);
		if (nResult < 0) then
			nServicePack = -1;
		endif;
	endif;

	return 0;

end;

///////////////////////////////////////////////////////////////////////////////
//
// Function: GetProductType
//
//  Purpose: This function determines if the machine is running NT Workstation
//			 or NT server.
//
//
///////////////////////////////////////////////////////////////////////////////
function GetProductType(szProductType)
	number nResult, nMatchLen, nLen;
begin

	nResult = RegDBGetKeyValueEx("SYSTEM\\CurrentControlSet\\Control\\ProductOptions",
		"ProductType", nMatchLen, szProductType, nLen);

	if (nResult < 0) then
		MessageBox("Error reading product type from the registry.", SEVERE);
		return nResult;
	endif;

	return nResult;

end;

///////////////////////////////////////////////////////////////////////////////
//
// Function: GetComputerName
//
//  Purpose: This function determines the name of this machine.
//
//
///////////////////////////////////////////////////////////////////////////////
function GetComputerName(szComputerName)
	number nResult, nMatchLen, nLen;
begin
	
	nResult = RegDBGetKeyValueEx ("SYSTEM\\CurrentControlSet\\Control\\ComputerName\\ComputerName",
		"ComputerName", nMatchLen, szComputerName, nLen);
	if (nResult < 0) then
		 MessageBox( "Error reading computer name from the registry", SEVERE);
		 return -1;
	endif;	
	
	return 0;
	
end;

 

///////////////////////////////////////////////////////////////////////////////
//
// Function: GetOracleInfo
//
//  Purpose: This function determines if Oracle is installed and the path.
//
//
///////////////////////////////////////////////////////////////////////////////
function GetOracleInfo(szOracleVersion, szOracleDir, szOracleHome)
	BOOL bFound;
	number idx, nvSize, nvType;
	string szBase, szHome, szIdx;
	
begin
	RegDBSetDefaultRoot(HKEY_LOCAL_MACHINE);
	
	bFound = FALSE;
	idx = 10;
	while (1 <= idx)  
		NumToStr(szIdx, idx); 
		if (RegDBKeyExist ("SOFTWARE\\ORACLE\\KEY_OraDb10g_home" + szIdx) = 1) then
			RegDBGetKeyValueEx("SOFTWARE\\ORACLE\\KEY_OraDb10g_home" + szIdx, "ORACLE_BASE" , nvType , szBase , nvSize );
			RegDBGetKeyValueEx("SOFTWARE\\ORACLE\\KEY_OraDb10g_home" + szIdx, "ORACLE_HOME" , nvType , szHome , nvSize );
        	bFound = TRUE;
		endif; 
		idx--;
	endwhile;
	
	if (!bFound) then   
		szOracleVersion = "";
		szOracleDir = "";
		szOracleHome = "";  
		return -1;
	endif;

	szOracleVersion = "10g";
	szOracleDir = szBase ^ "oradata";
	szOracleHome = szHome;
    return 0;
end;

function GetExcelInfo()
begin
	 RegDBSetDefaultRoot(HKEY_LOCAL_MACHINE);
	 if (RegDBKeyExist("SOFTWARE\\Classes\\Excel.Application\\CLSID") != 1) then
	 	return -1; 
	 else
	 	return 0;
	 endif;	
end;

function GetAutoCadInfo(szAutoCadVersion, szAutoCadDir)
	STRING szTemp, szSubKey, szSubSubKey, szProductName;
	number nResult, nType, nLen;	
	LIST listSubKeys, listSubSubKeys;
begin
	//TODO: Hardcoded for test
	//szAutoCadVersion = "R16";
	//szAutoCadDir = "C:\\Program Files\\AutoCAD 2005";
	//return 0;
	RegDBSetDefaultRoot(HKEY_LOCAL_MACHINE);
		
	nResult = RegDBKeyExist("SOFTWARE\\Autodesk");
	if (nResult < 0) then
		szAutoCadVersion = "Not Installed";
		return -1;
	endif;
	
	listSubKeys = ListCreate ( STRINGLIST );
	nResult = RegDBQueryKey ( "SOFTWARE\\Autodesk\\AutoCAD" , REGDB_KEYS , listSubKeys );
	if (nResult < 0) then  
		szAutoCadVersion = "Not Installed"; 
		ListDestroy(listSubKeys);
		return -1;
	endif; 
	
	ListGetFirstString ( listSubKeys , szSubKey );
	listSubSubKeys = ListCreate( STRINGLIST );            
	nResult = RegDBQueryKey ( "SOFTWARE\\Autodesk\\AutoCAD\\" + szSubKey, REGDB_KEYS , listSubSubKeys );
	if (nResult < 0) then  
		szAutoCadVersion = "Not Installed"; 
		ListDestroy(listSubKeys);
		ListDestroy(listSubSubKeys);
		return -1;
	endif; 
	
	ListGetFirstString ( listSubSubKeys , szSubSubKey );
	nResult = RegDBGetKeyValueEx("SOFTWARE\\Autodesk\\AutoCad\\" + szSubKey + "\\" + szSubSubKey, "AcadLocation", nType, szAutoCadDir, nLen);
	if (nResult < 0) then
		szAutoCadVersion = "Missing AcadLocation value in registry.";
		ListDestroy(listSubKeys);
		ListDestroy(listSubSubKeys);
		return -1;
	else
		if (FeatureSetTarget( MEDIA, "<ACADDIR>", szAutoCadDir) < 0) then
			MessageBox("FeatureSetTarget failed for <ACADDIR>", SEVERE);
			ListDestroy(listSubKeys);
			ListDestroy(listSubSubKeys);
			return -1;
		endif;
	endif;

	nResult = RegDBGetKeyValueEx("SOFTWARE\\Autodesk\\AutoCad\\" + szSubKey + "\\" + szSubSubKey, "ProductName", nType, szProductName, nLen);
	if (nResult < 0) then  
		szAutoCadVersion = szSubKey;
	else
		szAutoCadVersion = szSubKey + " (" + szProductName + ")";
	endif;
	
	ListDestroy(listSubKeys);
	ListDestroy(listSubSubKeys);
	return 0;
end;


///////////////////////////////////////////////////////////////////////////////
//
// Function: GetDatabaseInfo
//
//  Purpose: This function determines if how many database instances there.
//
///////////////////////////////////////////////////////////////////////////////
function GetDatabaseInfo(listConnectStrings)
	NUMBER nResult, nIndx, nMatchLen, nTotalDB, nLen;
	STRING szIndx, szDBInfoTemp, szDBIdx, szSucceedReg, szDelimiterSet, strDBName, strSid;
	LIST  listSidID;
begin
	listConnectStrings = ListCreate( STRINGLIST );
	
	RegDBSetDefaultRoot(HKEY_LOCAL_MACHINE);
	szSucceedReg = OPTIMIZE_REGKEY;
	for nIndx = 0 to 10
		NumToStr(szIndx, nIndx);
		szDBIdx = "DB" + szIndx;
		
		nResult = RegDBGetKeyValueEx (szSucceedReg, szDBIdx, nMatchLen, szDBInfoTemp, nLen);
		if (nResult != 0) then	
			nTotalDB = nIndx;
			goto blkGetSid;
		endif;
	endfor;
	
	blkGetSid:
	for nIndx = 0 to nTotalDB - 1
		NumToStr(szIndx, nIndx);
		szDBIdx = "DB" + szIndx;
	
		nResult = RegDBGetKeyValueEx (szSucceedReg, szDBIdx, nMatchLen, szDBInfoTemp, nLen);
		szDelimiterSet = "|";
   		listSidID = ListCreate( STRINGLIST );
   		StrGetTokens( listSidID, szDBInfoTemp, szDelimiterSet );
   		ListGetFirstString (listSidID, strDBName);
   		ListGetNextString (listSidID, strSid);
   		ListAddString (listConnectStrings, strSid, AFTER);	
   	endfor;
	
	return 0;
end;


function WriteRegistry(szLogin, szPassword, nSetupType, szUnits)
	string szEncryptedPwd;
	string szTmp;
	int i;
begin 
	if (CreateRegistrySet ( "" ) < 0) then
		//return -1; //Ignore. Returns an error but seems to work.
	endif;

	RegDBSetDefaultRoot ( HKEY_LOCAL_MACHINE );
	if (RegDBSetKeyValueEx ( OPTIMIZE_REGKEY , "Units" , REGDB_STRING , szUnits , -1 )) then
		return -1;
	endif;
	if (RegDBSetKeyValueEx ( OPTIMIZE_REGKEY , "UserID" , REGDB_STRING , szLogin , -1 )) then
		return -1;
	endif;
	
	/*
	if (UseDLL(SUPPORTDIR^"SsaGraphSession.dll") < 0) then
		MessageBox("Failed to load SsaGraphSession.dll", WARNING);
	else
		szEncryptedPwd = szPassword;
		makeSessionEnSO(szEncryptedPwd);
		UnUseDLL("SsaGraphSession.dll");
	endif;
    */
    for i=0 to StrLength(szPassword)-1
    	Sprintf(szTmp, "%d", szPassword[i]+123);
    	szEncryptedPwd = szEncryptedPwd + szTmp;
    endfor;

	if (RegDBSetKeyValueEx(OPTIMIZE_REGKEY, "SSAProfile", REGDB_STRING, szEncryptedPwd, -1) < 0) then
		return -1;
	endif;
	
	return 0;
end;

///////////////////////////////////////////////////////////////////////////////
//
// Function: Log
//
//  Purpose: This function appends text to the install log file.
//
///////////////////////////////////////////////////////////////////////////////
/*function Log(szMsg, list)
	STRING szLine;
	NUMBER nResult;
begin
//nResult = SdStartCopy( "Log", "Before Log", listLog );
	ListAddString(g_listLog, szMsg, AFTER);
	ListAddString(g_listLog, "", AFTER);
//nResult = SdStartCopy( "Log", "After Log", listLog );	
	
	if (ListCount(list) > 0) then
		nResult = ListGetFirstString(list, szLine);
		while (nResult != END_OF_LIST);
			ListAddString(g_listLog, szLine, AFTER);
			nResult = ListGetNextString(list, szLine);
		endwhile;
	endif;	
	

	if (TARGETDIR != "") then
		ListWriteToFile(g_listLog, TARGETDIR ^ "Log" ^ "install.log");
	else
		ListWriteToFile(g_listLog, SUPPORTDIR ^ "install.log");
	endif;
	
	return 0;
	
end;*/

function CreateOracleDSN(szDsnName, szUser, szSid)
	LIST listSubKeys;
	STRING szSubKey;
	STRING szDriverKey;
	STRING szDriverName;
	STRING szDriverDLL;
	STRING szDsnKey;  
	BOOL bFound, bStop;
	NUMBER nLen, nType, nResult;
begin
	//Enumerate all ODBC drivers
	RegDBSetDefaultRoot(HKEY_LOCAL_MACHINE);
    listSubKeys = ListCreate ( STRINGLIST );
	nResult = RegDBQueryKey ( "SOFTWARE\\ODBC\\ODBCINST.INI" , REGDB_KEYS , listSubKeys );
	if (nResult < 0) then  
		MessageBox("Failed to enumerate ODBC Drivers", SEVERE);
		ListDestroy(listSubKeys);
		return -1;
	endif; 
	
	//Look for the Oracle 10g driver. The name is for example "Oracle in OraDb10g_home1"
	bStop = FALSE;
	bFound = FALSE;
	if (ListGetFirstString(listSubKeys, szSubKey) == 0) then
		while (!bStop && !bFound)
			if (DEBUG) then
				MessageBox("Testing key " + szSubKey, INFORMATION);
			endif;
			if (szSubKey % "OraDb10") then
				bStop = TRUE;
				bFound = TRUE;
				szDriverName = szSubKey; 
				if (DEBUG) then
					MessageBox("Found driverName=" + szSubKey, INFORMATION);
				endif;
				ListDestroy(listSubKeys);
			else
				if (ListGetNextString(listSubKeys, szSubKey) != 0) then
					bStop = TRUE;
				endif;
			endif;
		endwhile;
	endif;
	if (!bFound) then
		MessageBox("Failed to find the Oracle 10g driver entry in registry", SEVERE);
		return -1;
	endif;
	
	
	//Get the full path for the driver DLL
	szDriverKey = "SOFTWARE\\ODBC\\ODBCINST.INI\\" + szDriverName;
	if (RegDBGetKeyValueEx(szDriverKey, "Driver", nType, szDriverDLL, nLen)<0) then
		MessageBox("Error getting the full path for Oracle driver DLL", SEVERE);
		return -1;
	endif;
	
	//Write the DSN name entry
	if (RegDBSetKeyValueEx("SOFTWARE\\ODBC\\ODBC.INI\\ODBC Data Sources", szDsnName, REGDB_STRING, szDriverName, -1)<0) then
		MessageBox("Error writing DSN: " + szDsnName, SEVERE);
		return -1;
	endif;
	
	//Write the DSN key and entries
	szDsnKey = "SOFTWARE\\ODBC\\ODBC.INI\\" + szDsnName; 
	if (RegDBCreateKeyEx(szDsnKey, "")<0) then
		MessageBox("Error creating the \"" + szDsnKey + "\" registry key", SEVERE);
		return -1;
	endif;
	RegDBSetKeyValueEx(szDsnKey, "Application Attributes", REGDB_STRING, "T", -1);
	RegDBSetKeyValueEx(szDsnKey, "Attributes", REGDB_STRING, "W", -1); 
	RegDBSetKeyValueEx(szDsnKey, "BatchAutocommitMode", REGDB_STRING, "IfAllSuccessful", -1);
	RegDBSetKeyValueEx(szDsnKey, "BindAsDATE", REGDB_STRING, "F", -1);
	RegDBSetKeyValueEx(szDsnKey, "CloseCursor", REGDB_STRING, "T", -1);
	RegDBSetKeyValueEx(szDsnKey, "Description", REGDB_STRING, IFX_PRODUCT_NAME, -1); 
	RegDBSetKeyValueEx(szDsnKey, "DisableDPM", REGDB_STRING, "F", -1);
	RegDBSetKeyValueEx(szDsnKey, "DisableMTS", REGDB_STRING, "T", -1);
	RegDBSetKeyValueEx(szDsnKey, "Driver", REGDB_STRING, szDriverDLL, -1);
	RegDBSetKeyValueEx(szDsnKey, "DSN", REGDB_STRING, szDsnName, -1);
	RegDBSetKeyValueEx(szDsnKey, "EXECSchemaOpt", REGDB_STRING, "", -1);
	RegDBSetKeyValueEx(szDsnKey, "EXECSyntax", REGDB_STRING, "F", -1);
	RegDBSetKeyValueEx(szDsnKey, "Failover", REGDB_STRING, "T", -1);
	RegDBSetKeyValueEx(szDsnKey, "FailoverDelay", REGDB_STRING, "10", -1); 
	RegDBSetKeyValueEx(szDsnKey, "FailoverRetryCount", REGDB_STRING, "10", -1);
	RegDBSetKeyValueEx(szDsnKey, "FetchBufferSize", REGDB_STRING, "64000", -1); 
	RegDBSetKeyValueEx(szDsnKey, "ForceWCHAR", REGDB_STRING, "F", -1); 
	RegDBSetKeyValueEx(szDsnKey, "Lobs", REGDB_STRING, "T", -1); 
	RegDBSetKeyValueEx(szDsnKey, "MetadataIdDefault", REGDB_STRING, "F", -1); 
	RegDBSetKeyValueEx(szDsnKey, "NumericSetting", REGDB_STRING, "NLS", -1);
	RegDBSetKeyValueEx(szDsnKey, "Password", REGDB_STRING, "", -1);
	RegDBSetKeyValueEx(szDsnKey, "QueryTimeout", REGDB_STRING, "T", -1); 
	RegDBSetKeyValueEx(szDsnKey, "ResultSets", REGDB_STRING, "T", -1);
	RegDBSetKeyValueEx(szDsnKey, "ServerName", REGDB_STRING, szSid, -1);
	RegDBSetKeyValueEx(szDsnKey, "UserID", REGDB_STRING, szUser, -1);
end;


function SetupNewSidRegistry(listConnectStrings, szSid)
	NUMBER 	nIndx, nResult, nTotalDB;
	STRING	szDBIdx, szDBInfo, szIndx, szODBCRegistry, szTmpSid, svString;  
	STRING szDsnName;
begin

	nResult = RegDBSetDefaultRoot(HKEY_LOCAL_MACHINE);
	nTotalDB = ListCount(listConnectStrings);
		
	
		
	szTmpSid = "@"+ g_szSid;
	
	szIndx = "";
	ListSetIndex (g_listConnectStrings, LISTFIRST);
	for nIndx = 0 to nTotalDB - 1
		ListCurrentString (g_listConnectStrings, svString);
		if (svString = szTmpSid) then
			NumToStr(szIndx, nIndx);	
		endif;
		ListSetIndex(g_listConnectStrings, LISTNEXT);
	endfor;	
		
	if szIndx = "" then
		NumToStr(szIndx, nTotalDB);	
	endif;		
	szDBIdx = "DB" + szIndx;

	szDsnName = "OptiDSN" + szIndx;
	szDBInfo = g_szDBName + "|@" + g_szSid + "|Oracle|OptiDSN" + szIndx ;
		
    CreateOracleDSN(szDsnName, g_szLogin, g_szSid);
	
	nResult = RegDBSetKeyValueEx(OPTIMIZE_REGKEY, szDBIdx, REGDB_STRING, szDBInfo, -1 );
	if (nResult < 0) then
		MessageBox("Error setting Registry Entry: " + szDBIdx, SEVERE);
		return nResult;
	endif;
	
	nResult = RegDBSetKeyValueEx(OPTIMIZE_REGKEY, "MRUDB", REGDB_STRING, g_szDBName, -1 );
	if (nResult < 0) then
		MessageBox("Error setting Registry Entry: MRUDB", SEVERE);
		return nResult;
	endif;	
		
	nResult = RegDBSetKeyValueEx(OPTIMIZE_REGKEY, "DBTimeout", REGDB_STRING, "60", -1 );
	if (nResult < 0) then
		MessageBox("Error setting Registry Entry: DBTimeout", SEVERE);
		return nResult;
	endif;
	
	return nResult;
		
end;


//---------------------------------------------------------------------------
// OnFirstUIAfter
//
// First Install UI Sequence - After Move Data
//
// The OnFirstUIAfter event called by OnShowUI after the file transfer
// of the setup when the setup is running in first install mode. By default
// this event displays UI that informs the end user that the setup has been
// completed successfully.
//
// Note: This event will not be called automatically in a
// program...endprogram style setup.
//---------------------------------------------------------------------------
function OnFirstUIAfter()
    STRING szTitle, szMsg1, szMsg2, szOpt1, szOpt2;
    BOOL bvOpt1, bvOpt2, bShowUpdateServiceDlg, bMetric;
begin

    ShowObjWizardPages(NEXT);
    
    szTitle = "";
    szMsg1 = ""; 
    szMsg2 = "";
    szOpt1 = "";
    szOpt2 = "";
	bvOpt1   = FALSE;
    bvOpt2   = FALSE;
    
	if (WriteRegistry(g_szLogin, g_szPassword, g_nSetupType, g_szUnits) < 0) then
		MessageBox("Error writing registry settings", SEVERE);
		abort;
	endif;
	if (SetupNewSidRegistry(g_listConnectStrings, g_szSid) < 0) then
		abort;
	endif;
		
	if (g_szUnits % "metric") then
		bMetric = TRUE;
	else
		bMetric = FALSE;
	endif;
		

	// Install database
	if ((g_nSetupType=0) || (g_nSetupType=2)) then
		if (InstallDatabase(g_bInstallDemo, bMetric, g_szSid, g_szDBName, g_szOracleDir + "\\" + g_szDBName) < 0) then
			abort;
		endif;
	endif;


	// Create empty folders if they do not exist   
	if ((g_nSetupType=0) || (g_nSetupType=1)) then
		if (CreateDir(TARGETDIR^"log")<0) then
			MessageBox("Failed to create \"log\" folder", WARNING);
		endif;
		if (CreateDir(TARGETDIR^"RackTypes")<0) then
			MessageBox("Failed to create \"RackTypes\" folder", WARNING);
		endif;
	endif;

	
	
	// Set this to true if you have the update service enabled, and if you want to check for updates.
	// Note: the ISUS Starter Edition does not support checking for updates programatically.  So, 
	// only set this to true if you have at least the ISUS Professional Edition.
	bShowUpdateServiceDlg = FALSE;
    
    //{{IS_SCRIPT_TAG(Dlg_SdDinishEx)	
    
    if ( BATCH_INSTALL ) then
    	SdFinishReboot ( szTitle , szMsg1 , SYS_BOOTMACHINE , szMsg2 , 0 );
    else
		
		// If the update service is enabled, show finish dialog that includes
		// update check option.
		if( bShowUpdateServiceDlg && ( ENABLED_ISERVICES & SERVICE_ISUPDATE ) ) then

			if( SdFinishUpdateEx( szTitle, szMsg1, szMsg2, szOpt1, szOpt2, TRUE ) ) then

				// Don't check for updates in silent mode.
				if( MODE != SILENTMODE ) then
					UpdateServiceCheckForUpdates( "", FALSE );
				endif;

			endif;

		else
			SdFinish ( szTitle , szMsg1 , szMsg2 , szOpt1 , szOpt2 , bvOpt1 , bvOpt2 );
		endif;

    endif;
    //}}IS_SCRIPT_TAG(Dlg_SdDinishEx)	
end;
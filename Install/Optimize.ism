<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<?xml-stylesheet type="text/xsl" href="is.xsl" ?>
<!DOCTYPE msi [
   <!ELEMENT msi   (summary,table*)>
   <!ATTLIST msi version    CDATA #REQUIRED>
   <!ATTLIST msi xmlns:dt   CDATA #IMPLIED
                 codepage   CDATA #IMPLIED
                 compression (MSZIP|LZX|none) "LZX">
   
   <!ELEMENT summary       (codepage?,title?,subject?,author?,keywords?,comments?,
                            template,lastauthor?,revnumber,lastprinted?,
                            createdtm?,lastsavedtm?,pagecount,wordcount,
                            charcount?,appname?,security?)>
                            
   <!ELEMENT codepage      (#PCDATA)>
   <!ELEMENT title         (#PCDATA)>
   <!ELEMENT subject       (#PCDATA)>
   <!ELEMENT author        (#PCDATA)>
   <!ELEMENT keywords      (#PCDATA)>
   <!ELEMENT comments      (#PCDATA)>
   <!ELEMENT template      (#PCDATA)>
   <!ELEMENT lastauthor    (#PCDATA)>
   <!ELEMENT revnumber     (#PCDATA)>
   <!ELEMENT lastprinted   (#PCDATA)>
   <!ELEMENT createdtm     (#PCDATA)>
   <!ELEMENT lastsavedtm   (#PCDATA)>
   <!ELEMENT pagecount     (#PCDATA)>
   <!ELEMENT wordcount     (#PCDATA)>
   <!ELEMENT charcount     (#PCDATA)>
   <!ELEMENT appname       (#PCDATA)>
   <!ELEMENT security      (#PCDATA)>                            
                                
   <!ELEMENT table         (col+,row*)>
   <!ATTLIST table
                name        CDATA #REQUIRED>

   <!ELEMENT col           (#PCDATA)>
   <!ATTLIST col
                 key       (yes|no) #IMPLIED
                 def       CDATA #IMPLIED>
                 
   <!ELEMENT row            (td+)>
   
   <!ELEMENT td             (#PCDATA)>
   <!ATTLIST td
                 href       CDATA #IMPLIED
                 dt:dt     (string|bin.base64) #IMPLIED
                 md5        CDATA #IMPLIED>
]>
<msi version="2.0" xmlns:dt="urn:schemas-microsoft-com:datatypes">
	
	<summary>
		<codepage>1252</codepage>
		<title>Installation Database</title>
		<subject>Blank Project Template</subject>
		<author>SSA Global</author>
		<keywords>Installer,MSI,Database</keywords>
		<comments>Contact:  Your local administrator</comments>
		<template>Intel;1033</template>
		<lastauthor>Administrator</lastauthor>
		<revnumber>{D57F70ED-CAFF-47FE-A8D6-342609575682}</revnumber>
		<lastprinted/>
		<createdtm>06/21/1999 02:00</createdtm>
		<lastsavedtm>07/14/2000 05:50</lastsavedtm>
		<pagecount>200</pagecount>
		<wordcount>0</wordcount>
		<charcount/>
		<appname>InstallShield Developer</appname>
		<security>1</security>
	</summary>
	
	<table name="Binary">
		<col key="yes" def="s72">Name</col>
		<col def="V0">Data</col>
		<col def="S255">ISBuildSourcePath</col>
	</table>

	<table name="CheckBox">
		<col key="yes" def="s72">Property</col>
		<col def="S64">Value</col>
	</table>

	<table name="ComboBox">
		<col key="yes" def="s72">Property</col>
		<col key="yes" def="i2">Order</col>
		<col def="s64">Value</col>
		<col def="L64">Text</col>
	</table>

	<table name="Component">
		<col key="yes" def="s72">Component</col>
		<col def="S38">ComponentId</col>
		<col def="s72">Directory_</col>
		<col def="i2">Attributes</col>
		<col def="S255">Condition</col>
		<col def="S72">KeyPath</col>
		<col def="I4">ISAttributes</col>
		<col def="S255">ISComments</col>
		<col def="S255">ISScanAtBuildFile</col>
		<col def="S255">ISRegFileToMergeAtBuild</col>
		<col def="S0">ISDotNetInstallerArgsInstall</col>
		<col def="S0">ISDotNetInstallerArgsCommit</col>
		<col def="S0">ISDotNetInstallerArgsUninstall</col>
		<col def="S0">ISDotNetInstallerArgsRollback</col>
		<row><td>AutoCad_R14</td><td>{FBBF8411-A832-4CBC-B72D-4B1F34A093C2}</td><td>NEW_DIRECTORY2</td><td>0</td><td/><td/><td>51729</td><td/><td/><td/><td>/LogFile=</td><td>/LogFile=</td><td>/LogFile=</td><td>/LogFile=</td></row>
		<row><td>AutoCad_R15_R16</td><td>{777DC543-4F94-4A7D-9A85-D0B42D6B2F6E}</td><td>NEW_DIRECTORY3</td><td>0</td><td/><td/><td>51729</td><td/><td/><td/><td>/LogFile=</td><td>/LogFile=</td><td>/LogFile=</td><td>/LogFile=</td></row>
		<row><td>Bin</td><td>{34B03AEC-122F-4AD3-B979-1F2727F4352B}</td><td>TARGETDIR</td><td>8</td><td/><td/><td>51729</td><td/><td/><td/><td>/LogFile=</td><td>/LogFile=</td><td>/LogFile=</td><td>/LogFile=</td></row>
		<row><td>DbCmd</td><td>{6341C08F-0DB2-4DE0-AC2A-02E59D28D8F1}</td><td>NEW_DIRECTORY5</td><td>0</td><td/><td/><td>51729</td><td/><td/><td/><td>/LogFile=</td><td>/LogFile=</td><td>/LogFile=</td><td>/LogFile=</td></row>
		<row><td>DefaultComponent</td><td>{4D5C9712-0CA4-4FA9-9178-9C6CA5B0E009}</td><td>TARGETDIR</td><td>0</td><td/><td/><td>51729</td><td/><td/><td/><td>/LogFile=</td><td>/LogFile=</td><td>/LogFile=</td><td>/LogFile=</td></row>
		<row><td>Excel</td><td>{33B12F1F-1077-4D7F-AFA9-80654FC9FD5C}</td><td>TARGETDIR</td><td>8</td><td/><td/><td>51729</td><td/><td/><td/><td>/LogFile=</td><td>/LogFile=</td><td>/LogFile=</td><td>/LogFile=</td></row>
		<row><td>FacilityImperial</td><td>{42A057BE-CC75-4E50-A1D3-229638727770}</td><td>NEW_DIRECTORY9</td><td>8</td><td/><td/><td>51729</td><td/><td/><td/><td>/LogFile=</td><td>/LogFile=</td><td>/LogFile=</td><td>/LogFile=</td></row>
		<row><td>FacilityMetric</td><td>{A3395480-F58F-4EDD-9DBE-72338EFBD29C}</td><td>NEW_DIRECTORY9</td><td>8</td><td/><td/><td>51729</td><td/><td/><td/><td>/LogFile=</td><td>/LogFile=</td><td>/LogFile=</td><td>/LogFile=</td></row>
		<row><td>Help</td><td>{9036C956-AAC2-497A-ACEE-A53B393EAC94}</td><td>TARGETDIR</td><td>8</td><td/><td/><td>51729</td><td/><td/><td/><td>/LogFile=</td><td>/LogFile=</td><td>/LogFile=</td><td>/LogFile=</td></row>
		<row><td>ISRegistrySetHolder</td><td>{49534A2F-6C89-4D35-B4AE-DCFFC84C006A}</td><td>TARGETDIR</td><td>0</td><td/><td/><td>51729</td><td/><td/><td/><td>/LogFile=</td><td>/LogFile=</td><td>/LogFile=</td><td>/LogFile=</td></row>
		<row><td>ImperialDmp</td><td>{313B702C-7024-4B52-A062-AF6E12C47691}</td><td>NEW_DIRECTORY5</td><td>0</td><td/><td/><td>51729</td><td/><td/><td/><td>/LogFile=</td><td>/LogFile=</td><td>/LogFile=</td><td>/LogFile=</td></row>
		<row><td>Interface</td><td>{8CF2550A-0747-43DF-9AD6-68B838914906}</td><td>INTERFACE</td><td>8</td><td/><td>product.xml</td><td>51729</td><td/><td/><td/><td>/LogFile=</td><td>/LogFile=</td><td>/LogFile=</td><td>/LogFile=</td></row>
		<row><td>Menu</td><td>{2F4EE810-9BA9-45DA-AD5A-F57B8DDB2DC4}</td><td>TARGETDIR</td><td>8</td><td/><td/><td>51729</td><td/><td/><td/><td>/LogFile=</td><td>/LogFile=</td><td>/LogFile=</td><td>/LogFile=</td></row>
		<row><td>MetricDmp</td><td>{FDF4E9FC-05B5-45A5-A6FA-B53ED8CB34F8}</td><td>NEW_DIRECTORY5</td><td>0</td><td/><td/><td>51729</td><td/><td/><td/><td>/LogFile=</td><td>/LogFile=</td><td>/LogFile=</td><td>/LogFile=</td></row>
		<row><td>Stock_Objects</td><td>{7ED43C17-0959-4CCE-ADCC-F307561ED2F8}</td><td>TARGETDIR</td><td>8</td><td/><td/><td>51729</td><td/><td/><td/><td>/LogFile=</td><td>/LogFile=</td><td>/LogFile=</td><td>/LogFile=</td></row>
		<row><td>System32</td><td>{DD373292-76D1-4113-9DDF-C8FBD213A577}</td><td>WINSYSDIR</td><td>8</td><td/><td/><td>55825</td><td/><td/><td/><td>/LogFile=</td><td>/LogFile=</td><td>/LogFile=</td><td>/LogFile=</td></row>
		<row><td>Warehouse</td><td>{5844B95F-8273-4A97-BAF0-307D448AF587}</td><td>TARGETDIR</td><td>8</td><td/><td/><td>51729</td><td/><td/><td/><td>/LogFile=</td><td>/LogFile=</td><td>/LogFile=</td><td>/LogFile=</td></row>
	</table>

	<table name="Control">
		<col key="yes" def="s72">Dialog_</col>
		<col key="yes" def="s50">Control</col>
		<col def="s20">Type</col>
		<col def="i2">X</col>
		<col def="i2">Y</col>
		<col def="i2">Width</col>
		<col def="i2">Height</col>
		<col def="I4">Attributes</col>
		<col def="S72">Property</col>
		<col def="L0">Text</col>
		<col def="S50">Control_Next</col>
		<col def="L50">Help</col>
		<col def="I4">ISWindowStyle</col>
		<col def="I4">ISControlId</col>
		<col def="S255">ISBuildSourcePath</col>
		<col def="S72">Binary_</col>
	</table>

	<table name="Dialog">
		<col key="yes" def="s72">Dialog</col>
		<col def="i2">HCentering</col>
		<col def="i2">VCentering</col>
		<col def="i2">Width</col>
		<col def="i2">Height</col>
		<col def="I4">Attributes</col>
		<col def="L128">Title</col>
		<col def="s50">Control_First</col>
		<col def="S50">Control_Default</col>
		<col def="S50">Control_Cancel</col>
		<col def="S255">ISComments</col>
		<col def="S72">TextStyle_</col>
		<col def="I4">ISWindowStyle</col>
		<col def="I4">ISResourceId</col>
	</table>

	<table name="Directory">
		<col key="yes" def="s72">Directory</col>
		<col def="S72">Directory_Parent</col>
		<col def="l255">DefaultDir</col>
		<col def="S255">ISDescription</col>
		<col def="I4">ISAttributes</col>
		<col def="S255">ISFolderName</col>
		<row><td>COMMONFILES</td><td>PROGRAMFILES</td><td>Common Files</td><td/><td>0</td><td/></row>
		<row><td>COMMONFILES64</td><td>PROGRAMFILES64</td><td>Common Files (64-bit)</td><td/><td>0</td><td/></row>
		<row><td>DesktopFolder</td><td/><td>Desktop</td><td/><td>3</td><td/></row>
		<row><td>FOLDER_FONTS</td><td>WINDIR</td><td>Fonts Folder</td><td/><td>0</td><td/></row>
		<row><td>INTERFACE</td><td>TARGETDIR</td><td>interface</td><td/><td>0</td><td/></row>
		<row><td>ISRUNTIMETEXTSUBS</td><td/><td>Script-defined Folders</td><td/><td>0</td><td/></row>
		<row><td>LOG</td><td>TARGETDIR</td><td>Log</td><td/><td>0</td><td/></row>
		<row><td>NEW_DIRECTORY1</td><td>ISRUNTIMETEXTSUBS</td><td>ACADDIR</td><td/><td>0</td><td/></row>
		<row><td>NEW_DIRECTORY2</td><td>NEW_DIRECTORY1</td><td>Support</td><td/><td>0</td><td/></row>
		<row><td>NEW_DIRECTORY3</td><td>TARGETDIR</td><td>bin</td><td/><td>0</td><td/></row>
		<row><td>NEW_DIRECTORY4</td><td>TARGETDIR</td><td>Install</td><td/><td>0</td><td/></row>
		<row><td>NEW_DIRECTORY5</td><td>NEW_DIRECTORY4</td><td>Database</td><td/><td>0</td><td/></row>
		<row><td>NEW_DIRECTORY9</td><td>ISRUNTIMETEXTSUBS</td><td>DRAWINGSDIR</td><td/><td>0</td><td/></row>
		<row><td>ORACLE_HOME</td><td>ISRUNTIMETEXTSUBS</td><td>ORACLE_HOME</td><td/><td>0</td><td/></row>
		<row><td>PROGRAMFILES</td><td/><td>Program Files</td><td/><td>0</td><td/></row>
		<row><td>PROGRAMFILES64</td><td/><td>Program Files (64-bit)</td><td/><td>0</td><td/></row>
		<row><td>ProgramMenuFolder</td><td/><td>Programs</td><td/><td>3</td><td/></row>
		<row><td>RACKTYPES</td><td>TARGETDIR</td><td>RackTypes</td><td/><td>0</td><td/></row>
		<row><td>SUPPORTDIR</td><td/><td>Support Folder</td><td/><td>0</td><td/></row>
		<row><td>StartMenuFolder</td><td/><td>Start Menu</td><td/><td>3</td><td/></row>
		<row><td>StartupFolder</td><td/><td>Startup</td><td/><td>3</td><td/></row>
		<row><td>TARGETDIR</td><td/><td>Application Target Folder</td><td/><td>0</td><td/></row>
		<row><td>TEMP</td><td>TARGETDIR</td><td>Temp</td><td/><td>0</td><td/></row>
		<row><td>WINDIR</td><td/><td>Windows</td><td/><td>0</td><td/></row>
		<row><td>WINSYSDIR</td><td>WINDIR</td><td>Windows System</td><td/><td>0</td><td/></row>
		<row><td>WINSYSDIR64</td><td>WINDIR</td><td>Windows System (64-bit)</td><td/><td>0</td><td/></row>
		<row><td>newfolder1</td><td>ProgramMenuFolder</td><td>&lt;SHELL_OBJECT_FOLDER&gt;</td><td/><td>1</td><td>SSA Global Technologies</td></row>
	</table>

	<table name="Feature">
		<col key="yes" def="s38">Feature</col>
		<col def="S38">Feature_Parent</col>
		<col def="L64">Title</col>
		<col def="L255">Description</col>
		<col def="I2">Display</col>
		<col def="i2">Level</col>
		<col def="S72">Directory_</col>
		<col def="i2">Attributes</col>
		<col def="S255">ISReleaseFlags</col>
		<col def="S255">ISComments</col>
		<col def="S255">ISFeatureCabName</col>
		<col def="S255">ISProFeatureName</col>
		<row><td>{0352E3F6-0F40-4F10-BFEF-1B113F870F6E}</td><td/><td>##ID_STRING6##</td><td/><td>6</td><td>1</td><td>TARGETDIR</td><td>0</td><td/><td/><td/><td>DrawingFilesMetric</td></row>
		<row><td>{20C1F0AB-9D55-48C3-8D2E-543090B20EE9}</td><td/><td>##ID_STRING5##</td><td/><td>4</td><td>1</td><td>TARGETDIR</td><td>0</td><td/><td/><td/><td>DrawingFilesImperial</td></row>
		<row><td>{230E25F0-F03B-455B-AEB6-788FE12F52D3}</td><td/><td>##ID_STRING1##</td><td>##ID_STRING10##</td><td>2</td><td>1</td><td>TARGETDIR</td><td>0</td><td/><td/><td/><td>Application</td></row>
		<row><td>{27CC191D-043A-4904-980E-68D5A7CF786B}</td><td/><td>##ID_STRING7##</td><td/><td>8</td><td>1</td><td>TARGETDIR</td><td>0</td><td/><td/><td/><td>AutoCAD_R14</td></row>
		<row><td>{5FD837EF-0207-481F-891C-9C4F0D65E875}</td><td>{66720526-A5D8-4201-8086-F8CA9BBCD922}</td><td>##ID_STRING18##</td><td/><td>20</td><td>1</td><td>TARGETDIR</td><td>0</td><td/><td/><td/><td>MetricDmp</td></row>
		<row><td>{66720526-A5D8-4201-8086-F8CA9BBCD922}</td><td/><td>##ID_STRING16##</td><td/><td>18</td><td>1</td><td>TARGETDIR</td><td>0</td><td/><td/><td/><td>Database</td></row>
		<row><td>{68FEFF0D-6AFB-42D0-A5EA-79DA841D211D}</td><td/><td>##ID_STRING11##</td><td/><td>10</td><td>1</td><td>TARGETDIR</td><td>0</td><td/><td/><td/><td>AutoCAD_R15_R16</td></row>
		<row><td>{6FACE334-236D-41D3-89D7-48E4F72ABCA8}</td><td>{66720526-A5D8-4201-8086-F8CA9BBCD922}</td><td>##ID_STRING17##</td><td/><td>18</td><td>1</td><td>TARGETDIR</td><td>0</td><td/><td/><td/><td>ImperialDmp</td></row>
		<row><td>{E5CB530C-8C03-495E-A2F1-E094F587FC87}</td><td>{230E25F0-F03B-455B-AEB6-788FE12F52D3}</td><td>New Merge Module Holder Object 1</td><td/><td>20</td><td>1</td><td>TARGETDIR</td><td>0</td><td/><td/><td/><td>New Merge Module Holder Object 1</td></row>
	</table>

	<table name="FeatureComponents">
		<col key="yes" def="s38">Feature_</col>
		<col key="yes" def="s72">Component_</col>
		<row><td>{0352E3F6-0F40-4F10-BFEF-1B113F870F6E}</td><td>FacilityMetric</td></row>
		<row><td>{20C1F0AB-9D55-48C3-8D2E-543090B20EE9}</td><td>FacilityImperial</td></row>
		<row><td>{230E25F0-F03B-455B-AEB6-788FE12F52D3}</td><td>Bin</td></row>
		<row><td>{230E25F0-F03B-455B-AEB6-788FE12F52D3}</td><td>DefaultComponent</td></row>
		<row><td>{230E25F0-F03B-455B-AEB6-788FE12F52D3}</td><td>Excel</td></row>
		<row><td>{230E25F0-F03B-455B-AEB6-788FE12F52D3}</td><td>Help</td></row>
		<row><td>{230E25F0-F03B-455B-AEB6-788FE12F52D3}</td><td>ISRegistrySetHolder</td></row>
		<row><td>{230E25F0-F03B-455B-AEB6-788FE12F52D3}</td><td>Interface</td></row>
		<row><td>{230E25F0-F03B-455B-AEB6-788FE12F52D3}</td><td>Menu</td></row>
		<row><td>{230E25F0-F03B-455B-AEB6-788FE12F52D3}</td><td>Stock_Objects</td></row>
		<row><td>{230E25F0-F03B-455B-AEB6-788FE12F52D3}</td><td>System32</td></row>
		<row><td>{230E25F0-F03B-455B-AEB6-788FE12F52D3}</td><td>Warehouse</td></row>
		<row><td>{27CC191D-043A-4904-980E-68D5A7CF786B}</td><td>AutoCad_R14</td></row>
		<row><td>{5FD837EF-0207-481F-891C-9C4F0D65E875}</td><td>MetricDmp</td></row>
		<row><td>{66720526-A5D8-4201-8086-F8CA9BBCD922}</td><td>DbCmd</td></row>
		<row><td>{68FEFF0D-6AFB-42D0-A5EA-79DA841D211D}</td><td>AutoCad_R15_R16</td></row>
		<row><td>{6FACE334-236D-41D3-89D7-48E4F72ABCA8}</td><td>ImperialDmp</td></row>
	</table>

	<table name="File">
		<col key="yes" def="s72">File</col>
		<col def="s72">Component_</col>
		<col def="s255">FileName</col>
		<col def="i4">FileSize</col>
		<col def="S72">Version</col>
		<col def="S20">Language</col>
		<col def="I2">Attributes</col>
		<col def="i2">Sequence</col>
		<col def="S255">ISBuildSourcePath</col>
		<col def="I4">ISAttributes</col>
		<col def="S72">ISComponentSubFolder_</col>
		<row><td>acad.lsp</td><td>Bin</td><td>acad.lsp</td><td>0</td><td/><td/><td/><td>1</td><td>&lt;PATH_TO_PROGRAMFILES_FILES&gt;\bin\acad.lsp</td><td>1</td><td>Bin</td></row>
		<row><td>acad.lsp1</td><td>Menu</td><td>acad.lsp</td><td>0</td><td/><td/><td/><td>1</td><td>&lt;PATH_TO_PROGRAMFILES_FILES&gt;\menu\acad.lsp</td><td>1</td><td>Menu</td></row>
		<row><td>acad.mnl</td><td>AutoCad_R14</td><td>acad.mnl</td><td>0</td><td/><td/><td/><td>1</td><td>&lt;PATH_TO_PROGRAMFILES_FILES&gt;\menu\acad.mnl</td><td>1</td><td/></row>
		<row><td>acad.mnl1</td><td>Menu</td><td>acad.mnl</td><td>0</td><td/><td/><td/><td>1</td><td>&lt;PATH_TO_PROGRAMFILES_FILES&gt;\menu\acad.mnl</td><td>1</td><td>Menu</td></row>
		<row><td>column.dwg</td><td>Warehouse</td><td>column.dwg</td><td>0</td><td/><td/><td/><td>1</td><td>&lt;PATH_TO_PROGRAMFILES_FILES&gt;\Warehouse\column.dwg</td><td>1</td><td>Warehouse</td></row>
		<row><td>conversion.exe</td><td>Interface</td><td>Conversion.exe</td><td>0</td><td/><td/><td/><td>1</td><td>&lt;PATH_TO_PROGRAMFILES_FILES&gt;\interface\Conversion.exe</td><td>1</td><td/></row>
		<row><td>costanalysis.doc</td><td>Help</td><td>CostAnalysis.doc</td><td>0</td><td/><td/><td/><td>1</td><td>&lt;PATH_TO_PROGRAMFILES_FILES&gt;\Help\CostAnalysis.doc</td><td>1</td><td>Help</td></row>
		<row><td>costanalysis.txt</td><td>Help</td><td>CostAnalysis.txt</td><td>0</td><td/><td/><td/><td>1</td><td>&lt;PATH_TO_PROGRAMFILES_FILES&gt;\Help\CostAnalysis.txt</td><td>1</td><td>Help</td></row>
		<row><td>costanalysis.xls</td><td>Excel</td><td>CostAnalysis.xls</td><td>0</td><td/><td/><td/><td>1</td><td>&lt;PATH_TO_PROGRAMFILES_FILES&gt;\Excel\CostAnalysis.xls</td><td>1</td><td>Excel</td></row>
		<row><td>createuser.sql</td><td>DbCmd</td><td>CreateUser.sql</td><td>0</td><td/><td/><td/><td>1</td><td>&lt;PATH_TO_INSTALL_FILES&gt;\database\CreateUser.sql</td><td>1</td><td/></row>
		<row><td>demonstrationbaseline.dwg</td><td>FacilityImperial</td><td>DemonstrationBaseline.dwg</td><td>0</td><td/><td/><td/><td>1</td><td>&lt;PATH_TO_PROGRAMFILES_FILES&gt;\facility\imperial\DemonstrationBaseline.dwg</td><td>1</td><td/></row>
		<row><td>demonstrationbaseline.dwg1</td><td>FacilityMetric</td><td>DemonstrationBaseline.dwg</td><td>0</td><td/><td/><td/><td>1</td><td>&lt;PATH_TO_METRIC_FILES&gt;\DemonstrationBaseline.dwg</td><td>1</td><td/></row>
		<row><td>demonstrationmaster.dwg</td><td>FacilityImperial</td><td>DemonstrationMaster.dwg</td><td>0</td><td/><td/><td/><td>1</td><td>&lt;PATH_TO_PROGRAMFILES_FILES&gt;\facility\imperial\DemonstrationMaster.dwg</td><td>1</td><td/></row>
		<row><td>demonstrationmaster.dwg1</td><td>FacilityMetric</td><td>DemonstrationMaster.dwg</td><td>0</td><td/><td/><td/><td>1</td><td>&lt;PATH_TO_METRIC_FILES&gt;\DemonstrationMaster.dwg</td><td>1</td><td/></row>
		<row><td>demonstrationresequence.dwg</td><td>FacilityImperial</td><td>DemonstrationResequence.dwg</td><td>0</td><td/><td/><td/><td>1</td><td>&lt;PATH_TO_PROGRAMFILES_FILES&gt;\facility\imperial\DemonstrationResequence.dwg</td><td>1</td><td/></row>
		<row><td>demonstrationresequence.dwg1</td><td>FacilityMetric</td><td>DemonstrationResequence.dwg</td><td>0</td><td/><td/><td/><td>1</td><td>&lt;PATH_TO_METRIC_FILES&gt;\DemonstrationResequence.dwg</td><td>1</td><td/></row>
		<row><td>demonstrationtargetsectionon1</td><td>FacilityMetric</td><td>DemonstrationTargetSectionOnly.dwg</td><td>0</td><td/><td/><td/><td>1</td><td>&lt;PATH_TO_METRIC_FILES&gt;\DemonstrationTargetSectionOnly.dwg</td><td>1</td><td/></row>
		<row><td>demonstrationwithnohz.dwg</td><td>FacilityImperial</td><td>DemonstrationWithNoHZ.dwg</td><td>0</td><td/><td/><td/><td>1</td><td>&lt;PATH_TO_PROGRAMFILES_FILES&gt;\facility\imperial\DemonstrationWithNoHZ.dwg</td><td>1</td><td/></row>
		<row><td>demonstrationwithnohz.dwg1</td><td>FacilityMetric</td><td>DemonstrationWithNoHZ.dwg</td><td>0</td><td/><td/><td/><td>1</td><td>&lt;PATH_TO_METRIC_FILES&gt;\DemonstrationWithNoHZ.dwg</td><td>1</td><td/></row>
		<row><td>demorelease1.3product.csv</td><td>Interface</td><td>DemoRelease1.3Product.csv</td><td>0</td><td/><td/><td/><td>1</td><td>&lt;PATH_TO_PROGRAMFILES_FILES&gt;\interface\DemoRelease1.3Product.csv</td><td>1</td><td/></row>
		<row><td>demorelease1.3product.xls</td><td>Interface</td><td>DemoRelease1.3PRoduct.xls</td><td>0</td><td/><td/><td/><td>1</td><td>&lt;PATH_TO_PROGRAMFILES_FILES&gt;\interface\DemoRelease1.3PRoduct.xls</td><td>1</td><td/></row>
		<row><td>dispatch.dll</td><td>Bin</td><td>Dispatch.dll</td><td>0</td><td/><td/><td/><td>1</td><td>&lt;PATH_TO_PROGRAMFILES_FILES&gt;\bin\Dispatch.dll</td><td>1</td><td>Bin</td></row>
		<row><td>dsciloc.dat</td><td>Interface</td><td>dsciloc.dat</td><td>0</td><td/><td/><td/><td>1</td><td>&lt;PATH_TO_PROGRAMFILES_FILES&gt;\interface\dsciloc.dat</td><td>1</td><td/></row>
		<row><td>file_interface_record.doc</td><td>Help</td><td>File_Interface_Record.doc</td><td>0</td><td/><td/><td/><td>1</td><td>&lt;PATH_TO_PROGRAMFILES_FILES&gt;\Help\File_Interface_Record.doc</td><td>1</td><td>Help</td></row>
		<row><td>file_interface_record.txt</td><td>Help</td><td>File_Interface_Record.txt</td><td>0</td><td/><td/><td/><td>1</td><td>&lt;PATH_TO_PROGRAMFILES_FILES&gt;\Help\File_Interface_Record.txt</td><td>1</td><td>Help</td></row>
		<row><td>hardware_software_requiremen</td><td>Help</td><td>Hardware_Software_Requirements.doc</td><td>0</td><td/><td/><td/><td>1</td><td>&lt;PATH_TO_PROGRAMFILES_FILES&gt;\Help\Hardware_Software_Requirements.doc</td><td>1</td><td>Help</td></row>
		<row><td>hardware_software_requiremen1</td><td>Help</td><td>Hardware_Software_Requirements.txt</td><td>0</td><td/><td/><td/><td>1</td><td>&lt;PATH_TO_PROGRAMFILES_FILES&gt;\Help\Hardware_Software_Requirements.txt</td><td>1</td><td>Help</td></row>
		<row><td>help.map</td><td>Help</td><td>help.map</td><td>0</td><td/><td/><td/><td>1</td><td>&lt;PATH_TO_PROGRAMFILES_FILES&gt;\Help\help.map</td><td>1</td><td>Help</td></row>
		<row><td>hotspot.dwg</td><td>Warehouse</td><td>hotspot.dwg</td><td>0</td><td/><td/><td/><td>1</td><td>&lt;PATH_TO_PROGRAMFILES_FILES&gt;\Warehouse\hotspot.dwg</td><td>1</td><td>Warehouse</td></row>
		<row><td>imperial102.dmp</td><td>ImperialDmp</td><td>imperial102.dmp</td><td>0</td><td/><td/><td/><td>1</td><td>&lt;PATH_TO_INSTALL_FILES&gt;\database\imperial102.dmp</td><td>1</td><td/></row>
		<row><td>imperial102nodemo.dmp</td><td>ImperialDmp</td><td>imperial102nodemo.dmp</td><td>0</td><td/><td/><td/><td>1</td><td>&lt;PATH_TO_INSTALL_FILES&gt;\database\imperial102nodemo.dmp</td><td>1</td><td/></row>
		<row><td>importcommand.bat</td><td>DbCmd</td><td>importCommand.bat</td><td>0</td><td/><td/><td/><td>1</td><td>&lt;PATH_TO_INSTALL_FILES&gt;\database\importCommand.bat</td><td>1</td><td/></row>
		<row><td>metric102.dmp</td><td>MetricDmp</td><td>metric102.dmp</td><td>0</td><td/><td/><td/><td>1</td><td>&lt;PATH_TO_INSTALL_FILES&gt;\database\metric102.dmp</td><td>1</td><td/></row>
		<row><td>metric102nodemo.dmp</td><td>MetricDmp</td><td>metric102nodemo.dmp</td><td>0</td><td/><td/><td/><td>1</td><td>&lt;PATH_TO_INSTALL_FILES&gt;\database\metric102nodemo.dmp</td><td>1</td><td/></row>
		<row><td>modal.arx1</td><td>Bin</td><td>modal.arx</td><td>0</td><td/><td/><td/><td>1</td><td>&lt;PATH_TO_PROGRAMFILES_FILES&gt;\bin\modal.arx</td><td>1</td><td>Bin</td></row>
		<row><td>modal.arx2</td><td>AutoCad_R15_R16</td><td>modal.arx</td><td>0</td><td/><td/><td/><td>1</td><td>&lt;PATH_TO_PROGRAMFILES_FILES&gt;\bin\modal.arx</td><td>1</td><td/></row>
		<row><td>move.xml</td><td>Interface</td><td>move.xml</td><td>0</td><td/><td/><td/><td>1</td><td>&lt;PATH_TO_PROGRAMFILES_FILES&gt;\interface\move.xml</td><td>1</td><td/></row>
		<row><td>optconfig.exe</td><td>Bin</td><td>OptConfig.exe</td><td>0</td><td/><td/><td/><td>1</td><td>&lt;PATH_TO_PROGRAMFILES_FILES&gt;\bin\OptConfig.exe</td><td>1</td><td>Bin</td></row>
		<row><td>optconfig.hlp</td><td>Bin</td><td>OPTCONFIG.HLP</td><td>0</td><td/><td/><td/><td>1</td><td>&lt;PATH_TO_PROGRAMFILES_FILES&gt;\bin\OPTCONFIG.HLP</td><td>1</td><td>Bin</td></row>
		<row><td>optimize.cnt</td><td>Help</td><td>Optimize.CNT</td><td>0</td><td/><td/><td/><td>1</td><td>&lt;PATH_TO_PROGRAMFILES_FILES&gt;\Help\Optimize.CNT</td><td>1</td><td>Help</td></row>
		<row><td>optimize.hlp</td><td>Help</td><td>OPTIMIZE.HLP</td><td>0</td><td/><td/><td/><td>1</td><td>&lt;PATH_TO_PROGRAMFILES_FILES&gt;\Help\OPTIMIZE.HLP</td><td>1</td><td>Help</td></row>
		<row><td>optimize.ico</td><td>Bin</td><td>Optimize.ico</td><td>0</td><td/><td/><td/><td>1</td><td>&lt;PATH_TO_PROGRAMFILES_FILES&gt;\bin\Optimize.ico</td><td>1</td><td>Bin</td></row>
		<row><td>optimizereports.exe</td><td>Bin</td><td>OptimizeReports.exe</td><td>0</td><td/><td/><td/><td>1</td><td>&lt;PATH_TO_PROGRAMFILES_FILES&gt;\bin\OptimizeReports.exe</td><td>1</td><td>Bin</td></row>
		<row><td>optiserver.dll</td><td>Bin</td><td>OptiServer.dll</td><td>0</td><td/><td/><td/><td>1</td><td>&lt;PATH_TO_PROGRAMFILES_FILES&gt;\bin\OptiServer.dll</td><td>1</td><td>Bin</td></row>
		<row><td>phantombay.dwg</td><td>Warehouse</td><td>phantombay.dwg</td><td>0</td><td/><td/><td/><td>1</td><td>&lt;PATH_TO_PROGRAMFILES_FILES&gt;\Warehouse\phantombay.dwg</td><td>1</td><td>Warehouse</td></row>
		<row><td>prdasg.dat</td><td>Interface</td><td>prdasg.dat</td><td>0</td><td/><td/><td/><td>1</td><td>&lt;PATH_TO_PROGRAMFILES_FILES&gt;\interface\prdasg.dat</td><td>1</td><td/></row>
		<row><td>product.xml</td><td>Interface</td><td>product.xml</td><td>0</td><td/><td/><td/><td>1</td><td>&lt;PATH_TO_PROGRAMFILES_FILES&gt;\interface\product.xml</td><td>1</td><td/></row>
		<row><td>regkeyreader.dll1</td><td>Bin</td><td>RegKeyReader.dll</td><td>0</td><td/><td/><td/><td>1</td><td>&lt;PATH_TO_PROGRAMFILES_FILES&gt;\bin\RegKeyReader.dll</td><td>1</td><td>Bin</td></row>
		<row><td>roboex32.dll</td><td>System32</td><td>Roboex32.dll</td><td>0</td><td/><td/><td/><td>1</td><td>&lt;PATH_TO_PROGRAMFILES_FILES&gt;\system32\Roboex32.dll</td><td>1</td><td/></row>
		<row><td>slbtrieve.dll1</td><td>Bin</td><td>slbtrieve.dll</td><td>0</td><td/><td/><td/><td>1</td><td>&lt;PATH_TO_PROGRAMFILES_FILES&gt;\bin\slbtrieve.dll</td><td>1</td><td>Bin</td></row>
		<row><td>slotting2.mnc</td><td>Menu</td><td>SLOTTING2.mnc</td><td>0</td><td/><td/><td/><td>1</td><td>&lt;PATH_TO_PROGRAMFILES_FILES&gt;\menu\SLOTTING2.mnc</td><td>1</td><td>Menu</td></row>
		<row><td>slotting2.mnl</td><td>Menu</td><td>slotting2.mnl</td><td>0</td><td/><td/><td/><td>1</td><td>&lt;PATH_TO_PROGRAMFILES_FILES&gt;\Menu\slotting2.mnl</td><td>1</td><td>Menu</td></row>
		<row><td>slotting2.mnr</td><td>Menu</td><td>SLOTTING2.mnr</td><td>0</td><td/><td/><td/><td>1</td><td>&lt;PATH_TO_PROGRAMFILES_FILES&gt;\menu\SLOTTING2.mnr</td><td>1</td><td>Menu</td></row>
		<row><td>slotting2.mns</td><td>Menu</td><td>Slotting2.mns</td><td>0</td><td/><td/><td/><td>1</td><td>&lt;PATH_TO_PROGRAMFILES_FILES&gt;\menu\Slotting2.mns</td><td>1</td><td>Menu</td></row>
		<row><td>slotting_asgdsk.dwg</td><td>Stock_Objects</td><td>slotting_asgdsk.dwg</td><td>0</td><td/><td/><td/><td>1</td><td>&lt;PATH_TO_PROGRAMFILES_FILES&gt;\Stock_Objects\slotting_asgdsk.dwg</td><td>1</td><td>Stock_Objects</td></row>
		<row><td>slotting_door.dwg</td><td>Stock_Objects</td><td>slotting_door.dwg</td><td>0</td><td/><td/><td/><td>1</td><td>&lt;PATH_TO_PROGRAMFILES_FILES&gt;\Stock_Objects\slotting_door.dwg</td><td>1</td><td>Stock_Objects</td></row>
		<row><td>slotting_floor.dwg</td><td>Stock_Objects</td><td>slotting_floor.dwg</td><td>0</td><td/><td/><td/><td>1</td><td>&lt;PATH_TO_PROGRAMFILES_FILES&gt;\Stock_Objects\slotting_floor.dwg</td><td>1</td><td>Stock_Objects</td></row>
		<row><td>slotting_obst.dwg</td><td>Stock_Objects</td><td>slotting_obst.dwg</td><td>0</td><td/><td/><td/><td>1</td><td>&lt;PATH_TO_PROGRAMFILES_FILES&gt;\Stock_Objects\slotting_obst.dwg</td><td>1</td><td>Stock_Objects</td></row>
		<row><td>slotting_wall.dwg</td><td>Stock_Objects</td><td>slotting_wall.dwg</td><td>0</td><td/><td/><td/><td>1</td><td>&lt;PATH_TO_PROGRAMFILES_FILES&gt;\Stock_Objects\slotting_wall.dwg</td><td>1</td><td>Stock_Objects</td></row>
		<row><td>ssa_optimize___forklift_labo</td><td>Excel</td><td>SSA Optimize - Forklift Labor Standards (Imperial).xls</td><td>0</td><td/><td/><td/><td>1</td><td>&lt;PATH_TO_PROGRAMFILES_FILES&gt;\excel\SSA Optimize - Forklift Labor Standards (Imperial).xls</td><td>1</td><td>Excel</td></row>
		<row><td>ssa_optimize___selection_lab</td><td>Excel</td><td>SSA Optimize - Selection Labor Standards (Imperial).xls</td><td>0</td><td/><td/><td/><td>1</td><td>&lt;PATH_TO_PROGRAMFILES_FILES&gt;\excel\SSA Optimize - Selection Labor Standards (Imperial).xls</td><td>1</td><td>Excel</td></row>
		<row><td>ssa_optimize___stocker_labor</td><td>Excel</td><td>SSA Optimize - Stocker Labor Standards (Imperial).xls</td><td>0</td><td/><td/><td/><td>1</td><td>&lt;PATH_TO_PROGRAMFILES_FILES&gt;\excel\SSA Optimize - Stocker Labor Standards (Imperial).xls</td><td>1</td><td>Excel</td></row>
		<row><td>ssagraphsession.dll</td><td>Bin</td><td>SsaGraphSession.dll</td><td>0</td><td/><td/><td/><td>1</td><td>&lt;PATH_TO_PROGRAMFILES_FILES&gt;\bin\SsaGraphSession.dll</td><td>1</td><td>Bin</td></row>
	</table>

	<table name="Font">
		<col key="yes" def="s72">File_</col>
		<col def="S128">FontTitle</col>
	</table>

	<table name="ISAlias">
		<col key="yes" def="S0">Alias</col>
		<col key="yes" def="S72">Identifier</col>
		<col key="yes" def="S50">Table</col>
	</table>

	<table name="ISAssistantTag">
		<col key="yes" def="s72">Tag</col>
		<col def="S255">Data</col>
		<row><td>BiildCDROMEnabled</td><td>Yes</td></row>
		<row><td>BiildInternetEnabled</td><td/></row>
		<row><td>BiildSingleExeEnabled</td><td>Yes</td></row>
		<row><td>BiildSingleMSIEnabled</td><td/></row>
		<row><td>PROJECT_ASSISTANT_DEFAULT_FEATURE</td><td>{230E25F0-F03B-455B-AEB6-788FE12F52D3}</td></row>
		<row><td>PROJECT_ASSISTANT_FEATURES</td><td>Selectable</td></row>
		<row><td>PROJECT_ASSISTANT_LICENSE_FILE</td><td>SetupFile1</td></row>
		<row><td>RegistryPageEnabled</td><td>Yes</td></row>
	</table>

	<table name="ISComponentExtended">
		<col key="yes" def="s72">Component_</col>
		<col def="I4">OS</col>
		<col def="S0">Language</col>
		<col def="s72">FilterProperty</col>
		<col def="I4">Platforms</col>
		<col def="S0">FTPLocation</col>
		<col def="S0">HTTPLocation</col>
		<col def="S0">Miscellaneous</col>
		<row><td>AutoCad_R14</td><td/><td/><td>_7BE21144_0EA4_421D_B8B0_084E9CC9F136_FILTER</td><td/><td/><td/><td/></row>
		<row><td>AutoCad_R15_R16</td><td/><td/><td>_950D09D1_864F_4CAD_9E59_9D8C8C6B4B97_FILTER</td><td/><td/><td/><td/></row>
		<row><td>Bin</td><td/><td/><td>_B84EDD88_63C3_4D78_9EE3_34BF470B60C6_FILTER</td><td/><td/><td/><td/></row>
		<row><td>DbCmd</td><td/><td/><td>_25B12E8D_E53A_43FC_8290_D6CDC5C8748F_FILTER</td><td/><td/><td/><td/></row>
		<row><td>DefaultComponent</td><td/><td/><td>_C4F871FF_2ACD_4DFB_8F76_A39572482EF4_FILTER</td><td/><td/><td/><td/></row>
		<row><td>Excel</td><td/><td/><td>_9A7B1A81_8D80_4648_A69F_930E90C3F637_FILTER</td><td/><td/><td/><td/></row>
		<row><td>FacilityImperial</td><td/><td/><td>_3F542143_233E_4CB7_ACBE_DBCB8373C3A3_FILTER</td><td/><td/><td/><td/></row>
		<row><td>FacilityMetric</td><td/><td/><td>_B3A7E0C7_7470_4A1E_A83C_1D2F56787AF7_FILTER</td><td/><td/><td/><td/></row>
		<row><td>Help</td><td/><td/><td>_03A941F3_6CDF_4F9A_9A87_672F4706132B_FILTER</td><td/><td/><td/><td/></row>
		<row><td>ISRegistrySetHolder</td><td/><td/><td>_ADD3DBD1_6BF3_4DB2_9C28_9F81969AB12D_FILTER</td><td/><td/><td/><td/></row>
		<row><td>ImperialDmp</td><td/><td/><td>_FEF7C25E_E41C_4ABE_9769_804488420F7E_FILTER</td><td/><td/><td/><td/></row>
		<row><td>Interface</td><td/><td/><td>_FAAFBBBF_E27B_4663_BB84_71589B31AD59_FILTER</td><td/><td/><td/><td/></row>
		<row><td>Menu</td><td/><td/><td>_D60220F7_12FB_4D28_959E_AFD9FFB2BDAA_FILTER</td><td/><td/><td/><td/></row>
		<row><td>MetricDmp</td><td/><td/><td>_998C98FC_1F95_4BF3_8D46_E4DE56C99B49_FILTER</td><td/><td/><td/><td/></row>
		<row><td>Stock_Objects</td><td/><td/><td>_66954991_46BB_4A7E_A87B_840D80B51ED8_FILTER</td><td/><td/><td/><td/></row>
		<row><td>System32</td><td/><td/><td>_4DA847A2_D988_431C_8F5F_76EA16205711_FILTER</td><td/><td/><td/><td/></row>
		<row><td>Warehouse</td><td/><td/><td>_8FB335C0_8B70_4AC0_A22A_E66ADFB32AEC_FILTER</td><td/><td/><td/><td/></row>
	</table>

	<table name="ISComponentSubFolder">
		<col key="yes" def="s72">ISSubFolder</col>
		<col def="s72">Component_</col>
		<col def="S72">ISSubFolder_Parent</col>
		<col def="s255">Path</col>
		<row><td>Bin</td><td>Bin</td><td/><td>Bin</td></row>
		<row><td>Excel</td><td>Excel</td><td/><td>Excel</td></row>
		<row><td>Help</td><td>Help</td><td/><td>Help</td></row>
		<row><td>Menu</td><td>Menu</td><td/><td>Menu</td></row>
		<row><td>Stock_Objects</td><td>Stock_Objects</td><td/><td>Stock_Objects</td></row>
		<row><td>Warehouse</td><td>Warehouse</td><td/><td>Warehouse</td></row>
	</table>

	<table name="ISDRMFile">
		<col key="yes" def="s72">ISDRMFile</col>
		<col def="S72">File_</col>
		<col def="S72">ISDRMLicense_</col>
		<col def="s255">Shell</col>
	</table>

	<table name="ISDRMFileAttribute">
		<col key="yes" def="s72">ISDRMFile_</col>
		<col key="yes" def="s72">Property</col>
		<col def="S0">Value</col>
	</table>

	<table name="ISDRMLicense">
		<col key="yes" def="s72">ISDRMLicense</col>
		<col def="S255">Description</col>
		<col def="S50">ProjectVersion</col>
		<col def="I4">Attributes</col>
		<col def="S255">LicenseNumber</col>
		<col def="S255">RequestCode</col>
		<col def="S255">ResponseCode</col>
	</table>

	<table name="ISDependency">
		<col key="yes" def="S50">ISDependency</col>
		<col def="I2">Exclude</col>
	</table>

	<table name="ISDisk1File">
		<col key="yes" def="s72">ISDisk1File</col>
		<col def="s255">ISBuildSourcePath</col>
		<col def="I4">Disk</col>
	</table>

	<table name="ISDynamicFile">
		<col key="yes" def="s72">Component_</col>
		<col key="yes" def="s255">SourceFolder</col>
		<col def="I2">IncludeFlags</col>
		<col def="S0">IncludeFiles</col>
		<col def="S0">ExcludeFiles</col>
		<col def="I4">ISAttributes</col>
	</table>

	<table name="ISFeatureExtended">
		<col key="yes" def="s38">Feature_</col>
		<col def="S255">Installing</col>
		<col def="S50">Installed</col>
		<col def="S255">Uninstalling</col>
		<col def="S255">Uninstalled</col>
		<col def="S0">FTPLocation</col>
		<col def="S0">HTTPLocation</col>
		<col def="S0">Miscellaneous</col>
		<col def="L255">StatusText</col>
		<col def="I4">ISAttributes</col>
		<col def="S255">Password</col>
		<col def="S50">CDRomFolder</col>
		<col def="S50">Moniker</col>
		<row><td>{0352E3F6-0F40-4F10-BFEF-1B113F870F6E}</td><td/><td/><td/><td/><td/><td/><td/><td/><td>4</td><td/><td/><td/></row>
		<row><td>{20C1F0AB-9D55-48C3-8D2E-543090B20EE9}</td><td/><td/><td/><td/><td/><td/><td/><td/><td>4</td><td/><td/><td/></row>
		<row><td>{230E25F0-F03B-455B-AEB6-788FE12F52D3}</td><td/><td/><td/><td/><td/><td/><td/><td/><td>36</td><td/><td/><td/></row>
		<row><td>{27CC191D-043A-4904-980E-68D5A7CF786B}</td><td/><td/><td/><td/><td/><td/><td/><td/><td>4</td><td/><td/><td/></row>
		<row><td>{5FD837EF-0207-481F-891C-9C4F0D65E875}</td><td/><td/><td/><td/><td/><td/><td/><td/><td>4</td><td/><td/><td/></row>
		<row><td>{66720526-A5D8-4201-8086-F8CA9BBCD922}</td><td/><td/><td/><td/><td/><td/><td/><td/><td>4</td><td/><td/><td/></row>
		<row><td>{68FEFF0D-6AFB-42D0-A5EA-79DA841D211D}</td><td/><td/><td/><td/><td/><td/><td/><td/><td>4</td><td/><td/><td/></row>
		<row><td>{6FACE334-236D-41D3-89D7-48E4F72ABCA8}</td><td/><td/><td/><td/><td/><td/><td/><td/><td>4</td><td/><td/><td/></row>
		<row><td>{E5CB530C-8C03-495E-A2F1-E094F587FC87}</td><td/><td/><td/><td/><td/><td/><td/><td/><td>20</td><td/><td/><td>@ismk11:ED4FF843-0DE2-4F5F-936C-29845A706E45</td></row>
	</table>

	<table name="ISFeatureMergeModuleExcludes">
		<col key="yes" def="s38">Feature_</col>
		<col key="yes" def="s255">ModuleID</col>
		<col key="yes" def="i2">Language</col>
	</table>

	<table name="ISFeatureMergeModules">
		<col key="yes" def="s38">Feature_</col>
		<col key="yes" def="s255">ISMergeModule_</col>
		<col key="yes" def="i2">Language_</col>
		<row><td>{E5CB530C-8C03-495E-A2F1-E094F587FC87}</td><td>MSFLXGRD.7EBEDD26_AA66_11D2_B980_006097C4DE24</td><td>0</td></row>
		<row><td>{E5CB530C-8C03-495E-A2F1-E094F587FC87}</td><td>VC_User_ATL.BA9B6DD3_0DE0_11D5_A548_0090278A1BB8</td><td>0</td></row>
		<row><td>{E5CB530C-8C03-495E-A2F1-E094F587FC87}</td><td>VC_User_ATL71.15ACF2F5_9BDB_4FFF_9C85_9814946343FF</td><td>0</td></row>
		<row><td>{E5CB530C-8C03-495E-A2F1-E094F587FC87}</td><td>VC_User_CRT.BA9B6D09_0DE0_11D5_A548_0090278A1BB8</td><td>0</td></row>
		<row><td>{E5CB530C-8C03-495E-A2F1-E094F587FC87}</td><td>VC_User_CRT71.07D8793C_DC93_47EF_8FDA_3517BC941A0F</td><td>0</td></row>
		<row><td>{E5CB530C-8C03-495E-A2F1-E094F587FC87}</td><td>VC_User_CRT_IO.BA9B7684_0DE0_11D5_A548_0090278A1BB8</td><td>0</td></row>
		<row><td>{E5CB530C-8C03-495E-A2F1-E094F587FC87}</td><td>VC_User_MFC.BA9B6D6E_0DE0_11D5_A548_0090278A1BB8</td><td>0</td></row>
		<row><td>{E5CB530C-8C03-495E-A2F1-E094F587FC87}</td><td>VC_User_MFC71.211ADFCA_ED54_4E97_A0C1_80845B72BE77</td><td>0</td></row>
		<row><td>{E5CB530C-8C03-495E-A2F1-E094F587FC87}</td><td>VC_User_STL.BA9B76E9_0DE0_11D5_A548_0090278A1BB8</td><td>0</td></row>
		<row><td>{E5CB530C-8C03-495E-A2F1-E094F587FC87}</td><td>VC_User_STL71.09528776_2EA8_4A65_AA88_693146381D7D</td><td>0</td></row>
	</table>

	<table name="ISIISAppPool">
		<col key="yes" def="s72">AppPool</col>
		<col def="l72">Name</col>
		<col def="s72">Component_</col>
		<col def="I4">Attributes</col>
		<col def="S72">User</col>
		<col def="S72">UserPassword</col>
		<col def="I4">RecycleMinutes</col>
		<col def="I4">RecycleRequests</col>
		<col def="S72">RecycleTimes</col>
		<col def="I4">IdleTimeout</col>
		<col def="I4">QueueLimit</col>
		<col def="S72">CPUMon</col>
		<col def="I4">MaxProc</col>
	</table>

	<table name="ISIISCommon">
		<col key="yes" def="s50">ISIISCommon</col>
		<col def="S50">ISIISCommon_Parent</col>
		<col def="L255">DisplayName</col>
		<col def="s50">RootDir</col>
		<col def="i4">Attributes</col>
		<col def="L255">DefDoc</col>
		<col def="I4">SessionTimeout</col>
		<col def="I4">ScriptTimeout</col>
		<col def="S255">AnonyUserName</col>
		<col def="S255">AnonyPasswrd</col>
		<col def="S0">CustomErrors</col>
		<col def="L255">AppName</col>
		<col def="S72">SSLCert</col>
		<col def="L72">AppPool_</col>
	</table>

	<table name="ISIISMetaData">
		<col key="yes" def="s72">ISIISCommon_</col>
		<col key="yes" def="I4">MetaDataProp</col>
		<col def="I4">MetaDataType</col>
		<col def="i4">MetaDataUserType</col>
		<col def="i4">MetaDataAttributes</col>
		<col def="i4">Order</col>
		<col def="s0">MetaDataValue</col>
	</table>

	<table name="ISIISWebServiceExtension">
		<col key="yes" def="s72">WebServiceExtension</col>
		<col def="s0">Component_</col>
		<col def="s72">File</col>
		<col def="l72">Description</col>
		<col def="s72">Group</col>
		<col def="i4">Attributes</col>
	</table>

	<table name="ISLanguage">
		<col key="yes" def="s50">ISLanguage</col>
		<col def="I2">Included</col>
		<row><td>1033</td><td>1</td></row>
	</table>

	<table name="ISLinkerLibrary">
		<col key="yes" def="s72">ISLinkerLibrary</col>
		<col def="s255">Library</col>
		<col def="i4">Order</col>
		<row><td>ifx.obl</td><td>&lt;ISProductFolder&gt;\Script\ifx\Lib\ifx.obl</td><td>1</td></row>
		<row><td>isrt.obl</td><td>&lt;ISProductFolder&gt;\Script\ISRT\Lib\isrt.obl</td><td>2</td></row>
	</table>

	<table name="ISLocalControl">
		<col key="yes" def="s72">Dialog_</col>
		<col key="yes" def="s50">Control_</col>
		<col key="yes" def="s50">ISLanguage_</col>
		<col def="I4">Attributes</col>
		<col def="I2">X</col>
		<col def="I2">Y</col>
		<col def="I2">Width</col>
		<col def="I2">Height</col>
		<col def="S72">Binary_</col>
		<col def="S255">ISBuildSourcePath</col>
	</table>

	<table name="ISLocalDialog">
		<col key="yes" def="S50">Dialog_</col>
		<col key="yes" def="S50">ISLanguage_</col>
		<col def="I4">Attributes</col>
		<col def="S72">TextStyle_</col>
		<col def="i2">Width</col>
		<col def="i2">Height</col>
	</table>

	<table name="ISLocalRadioButton">
		<col key="yes" def="s72">Property</col>
		<col key="yes" def="i2">Order</col>
		<col key="yes" def="s50">ISLanguage_</col>
		<col def="i2">X</col>
		<col def="i2">Y</col>
		<col def="i2">Width</col>
		<col def="i2">Height</col>
	</table>

	<table name="ISMergeModule">
		<col key="yes" def="s255">ISMergeModule</col>
		<col key="yes" def="i2">Language</col>
		<col def="s255">Name</col>
		<col def="S255">Destination</col>
		<col def="I4">ISAttributes</col>
		<row><td>MSFLXGRD.7EBEDD26_AA66_11D2_B980_006097C4DE24</td><td>0</td><td>MSFlexGrid</td><td>[SystemFolder]</td><td/></row>
		<row><td>VC_User_ATL.BA9B6DD3_0DE0_11D5_A548_0090278A1BB8</td><td>0</td><td>VC User ATL</td><td>[SystemFolder]</td><td/></row>
		<row><td>VC_User_ATL71.15ACF2F5_9BDB_4FFF_9C85_9814946343FF</td><td>0</td><td>VC User ATL71 RTL X86 ---</td><td>[SystemFolder]</td><td/></row>
		<row><td>VC_User_CRT.BA9B6D09_0DE0_11D5_A548_0090278A1BB8</td><td>0</td><td>VC User CRT</td><td>[SystemFolder]</td><td/></row>
		<row><td>VC_User_CRT71.07D8793C_DC93_47EF_8FDA_3517BC941A0F</td><td>0</td><td>VC User CRT71 RTL X86 ---</td><td>[SystemFolder]</td><td/></row>
		<row><td>VC_User_CRT_IO.BA9B7684_0DE0_11D5_A548_0090278A1BB8</td><td>0</td><td>VC User CRT IO</td><td>[SystemFolder]</td><td/></row>
		<row><td>VC_User_MFC.BA9B6D6E_0DE0_11D5_A548_0090278A1BB8</td><td>0</td><td>VC User MFC</td><td>[SystemFolder]</td><td/></row>
		<row><td>VC_User_MFC71.211ADFCA_ED54_4E97_A0C1_80845B72BE77</td><td>0</td><td>VC User MFC71 RTL X86 ---</td><td>[SystemFolder]</td><td/></row>
		<row><td>VC_User_STL.BA9B76E9_0DE0_11D5_A548_0090278A1BB8</td><td>0</td><td>VC User STL</td><td>[SystemFolder]</td><td/></row>
		<row><td>VC_User_STL71.09528776_2EA8_4A65_AA88_693146381D7D</td><td>0</td><td>VC User STL71 RTL X86 ---</td><td>[SystemFolder]</td><td/></row>
	</table>

	<table name="ISMergeModuleCfgValues">
		<col key="yes" def="s255">ISMergeModule_</col>
		<col key="yes" def="i2">Language_</col>
		<col key="yes" def="s72">ModuleConfiguration_</col>
		<col def="L0">Value</col>
		<col def="i2">Format</col>
		<col def="L255">Type</col>
		<col def="L255">ContextData</col>
		<col def="L255">DefaultValue</col>
		<col def="I2">Attributes</col>
		<col def="L255">DisplayName</col>
		<col def="L255">Description</col>
		<col def="L255">HelpLocation</col>
		<col def="L255">HelpKeyword</col>
		<row><td>VC_User_ATL.BA9B6DD3_0DE0_11D5_A548_0090278A1BB8</td><td>0</td><td>DIR_RETARGET_TARGETDIR</td><td>TARGETDIR</td><td>1</td><td>Directory</td><td>IsolationDir</td><td>TARGETDIR</td><td>2</td><td>Module Retargetable Folder</td><td/><td/><td/></row>
		<row><td>VC_User_CRT.BA9B6D09_0DE0_11D5_A548_0090278A1BB8</td><td>0</td><td>DIR_RETARGET_TARGETDIR</td><td>TARGETDIR</td><td>1</td><td>Directory</td><td>IsolationDir</td><td>TARGETDIR</td><td>2</td><td>Module Retargetable Folder</td><td/><td/><td/></row>
		<row><td>VC_User_CRT_IO.BA9B7684_0DE0_11D5_A548_0090278A1BB8</td><td>0</td><td>DIR_RETARGET_TARGETDIR</td><td>TARGETDIR</td><td>1</td><td>Directory</td><td>IsolationDir</td><td>TARGETDIR</td><td>2</td><td>Module Retargetable Folder</td><td/><td/><td/></row>
		<row><td>VC_User_MFC.BA9B6D6E_0DE0_11D5_A548_0090278A1BB8</td><td>0</td><td>DIR_RETARGET_TARGETDIR</td><td>TARGETDIR</td><td>1</td><td>Directory</td><td>IsolationDir</td><td>TARGETDIR</td><td>2</td><td>Module Retargetable Folder</td><td/><td/><td/></row>
		<row><td>VC_User_STL.BA9B76E9_0DE0_11D5_A548_0090278A1BB8</td><td>0</td><td>DIR_RETARGET_TARGETDIR</td><td>TARGETDIR</td><td>1</td><td>Directory</td><td>IsolationDir</td><td>TARGETDIR</td><td>2</td><td>Module Retargetable Folder</td><td/><td/><td/></row>
	</table>

	<table name="ISPathVariable">
		<col key="yes" def="s32">ISPathVariable</col>
		<col def="S255">Value</col>
		<col def="S255">TestValue</col>
		<col def="i4">Type</col>
		<row><td>CommonFilesFolder</td><td/><td/><td>1</td></row>
		<row><td>ISPROJECTDIR</td><td/><td/><td>1</td></row>
		<row><td>ISProductFolder</td><td/><td/><td>1</td></row>
		<row><td>ISProjectDataFolder</td><td/><td/><td>1</td></row>
		<row><td>ISProjectFolder</td><td/><td/><td>1</td></row>
		<row><td>PATH_TO_73_FILES</td><td>I:\Database\73</td><td/><td>2</td></row>
		<row><td>PATH_TO_80_FILES</td><td>I:\Database\80</td><td/><td>2</td></row>
		<row><td>PATH_TO_81_FILES</td><td>I:\Database\81</td><td/><td>2</td></row>
		<row><td>PATH_TO_CDROM_IMAGE_DB_FILES</td><td>C:\Shared\optimize\CDROM_IMAGE_DB</td><td/><td>2</td></row>
		<row><td>PATH_TO_CDROM_IMAGE_FILES</td><td>C:\Shared\optimize\CDROM_IMAGE</td><td/><td>2</td></row>
		<row><td>PATH_TO_DATABASE_FILES</td><td>I:\InstallFiles\Database</td><td/><td>2</td></row>
		<row><td>PATH_TO_INSTALL_FILES</td><td>I:\InstallFiles\Install</td><td/><td>2</td></row>
		<row><td>PATH_TO_METRIC_FILES</td><td>I:\ProgramFiles\Facility\Metric</td><td/><td>2</td></row>
		<row><td>PATH_TO_PROGRAMFILES_FILES</td><td>I:\ProgramFiles</td><td/><td>2</td></row>
		<row><td>PATH_TO_SUPPORT_FILES</td><td>I:\Support</td><td/><td>2</td></row>
		<row><td>PATH_TO_SYSTEMFILES_FILES</td><td>I:\SystemFiles</td><td/><td>2</td></row>
		<row><td>ProgramFilesFolder</td><td/><td/><td>1</td></row>
		<row><td>SystemFolder</td><td/><td/><td>1</td></row>
		<row><td>WindowsFolder</td><td/><td/><td>1</td></row>
	</table>

	<table name="ISProObjectProperty">
		<col key="yes" def="s72">Feature_</col>
		<col key="yes" def="s72">Property</col>
		<col def="L0">Value</col>
		<row><td>{E5CB530C-8C03-495E-A2F1-E094F587FC87}</td><td>Cache</td><td>0,Bool</td></row>
		<row><td>{E5CB530C-8C03-495E-A2F1-E094F587FC87}</td><td>CommandLine</td><td>""</td></row>
		<row><td>{E5CB530C-8C03-495E-A2F1-E094F587FC87}</td><td>InstallChk</td><td>0,Long</td></row>
		<row><td>{E5CB530C-8C03-495E-A2F1-E094F587FC87}</td><td>InstallMsiEngine</td><td>1,Long</td></row>
		<row><td>{E5CB530C-8C03-495E-A2F1-E094F587FC87}</td><td>InstallStatusText</td><td>"Installing merge modules"</td></row>
		<row><td>{E5CB530C-8C03-495E-A2F1-E094F587FC87}</td><td>Logging</td><td>0,Bool</td></row>
		<row><td>{E5CB530C-8C03-495E-A2F1-E094F587FC87}</td><td>Msi12Engine</td><td>0,Long</td></row>
		<row><td>{E5CB530C-8C03-495E-A2F1-E094F587FC87}</td><td>Msi20Engine</td><td>1,Long</td></row>
		<row><td>{E5CB530C-8C03-495E-A2F1-E094F587FC87}</td><td>MsiEngineChkBox12</td><td>0,Long</td></row>
		<row><td>{E5CB530C-8C03-495E-A2F1-E094F587FC87}</td><td>MsiEngineChkBox20</td><td>1,Long</td></row>
		<row><td>{E5CB530C-8C03-495E-A2F1-E094F587FC87}</td><td>MsiPackage</td><td>""</td></row>
		<row><td>{E5CB530C-8C03-495E-A2F1-E094F587FC87}</td><td>MsiTargetPath</td><td>""</td></row>
		<row><td>{E5CB530C-8C03-495E-A2F1-E094F587FC87}</td><td>ProductCode</td><td>""</td></row>
		<row><td>{E5CB530C-8C03-495E-A2F1-E094F587FC87}</td><td>UIOption</td><td>2,Long</td></row>
		<row><td>{E5CB530C-8C03-495E-A2F1-E094F587FC87}</td><td>UnInstallChk</td><td>0,Long</td></row>
		<row><td>{E5CB530C-8C03-495E-A2F1-E094F587FC87}</td><td>Uninstall</td><td>-1,Bool</td></row>
		<row><td>{E5CB530C-8C03-495E-A2F1-E094F587FC87}</td><td>UninstallStatusText</td><td>"Uninstalling merge modules"</td></row>
	</table>

	<table name="ISProductConfiguration">
		<col key="yes" def="s72">ISProductConfiguration</col>
		<col def="S255">ProductConfigurationFlags</col>
		<col def="I4">GeneratePackageCode</col>
		<row><td>Media</td><td/><td>0</td></row>
	</table>

	<table name="ISProductConfigurationProperty">
		<col key="yes" def="s72">ISProductConfiguration_</col>
		<col key="yes" def="s72">Property</col>
		<col def="L255">Value</col>
	</table>

	<table name="ISRegistrySet">
		<col key="yes" def="s72">ISRegistrySet</col>
		<col def="S255">Comments</col>
		<row><td>&lt;Default&gt;</td><td>Default Registry Set.  Always installed.</td></row>
		<row><td>AppSettings</td><td/></row>
	</table>

	<table name="ISRegistrySetComponents">
		<col key="yes" def="s72">ISRegistrySet_</col>
		<col key="yes" def="s72">Component_</col>
	</table>

	<table name="ISRelease">
		<col key="yes" def="s72">ISRelease</col>
		<col key="yes" def="s72">ISProductConfiguration_</col>
		<col def="S255">BuildLocation</col>
		<col def="s255">PackageName</col>
		<col def="i4">Type</col>
		<col def="s255">SupportedLanguagesUI</col>
		<col def="i4">MsiSourceType</col>
		<col def="i4">ReleaseType</col>
		<col def="s72">Platforms</col>
		<col def="S255">SupportedLanguagesData</col>
		<col def="s6">DefaultLanguage</col>
		<col def="i4">SupportedOSs</col>
		<col def="s50">DiskSize</col>
		<col def="i4">DiskSizeUnit</col>
		<col def="i4">DiskClusterSize</col>
		<col def="S255">ReleaseFlags</col>
		<col def="i4">DiskSpanning</col>
		<col def="S255">SynchMsi</col>
		<col def="s255">MediaLocation</col>
		<col def="S255">URLLocation</col>
		<col def="S255">DigitalURL</col>
		<col def="S255">DigitalPVK</col>
		<col def="S255">DigitalSPC</col>
		<col def="S255">Password</col>
		<col def="S255">VersionCopyright</col>
		<col def="i4">Attributes</col>
		<col def="S255">CDBrowser</col>
		<col def="S255">DotNetBuildConfiguration</col>
		<col def="S255">MsiCommandLine</col>
		<col def="I4">ISSetupPrerequisiteLocation</col>
		<row><td>CDROM_IMAGE</td><td>Media</td><td/><td>PackageName</td><td>0</td><td>1033</td><td>2</td><td>1</td><td>Intel</td><td/><td>1033</td><td>3</td><td>650</td><td>0</td><td>2048</td><td/><td>0</td><td/><td>MediaLocation</td><td/><td>http://</td><td/><td/><td/><td/><td>141</td><td/><td/><td/><td/></row>
	</table>

	<table name="ISReleasePro">
		<col key="yes" def="s72">ISRelease_</col>
		<col key="yes" def="s72">ISProductConfiguration_</col>
		<col def="I4">BuildType</col>
		<col def="I4">BuildSize</col>
		<col def="S50">GUID</col>
		<col def="S255">SingleExeFilename</col>
		<col def="S255">SingleExeIconName</col>
		<col def="S255">PreProcDefines</col>
		<col def="S255">SetupDlgAppName</col>
		<col def="I4">Platform</col>
		<col def="S255">WebPageURL</col>
		<col def="S255">SkinName</col>
		<col def="S255">FTPFolder</col>
		<col def="S255">FTPUsername</col>
		<col def="S255">FTPPassword</col>
		<col def="S255">CopyToFolder</col>
		<col def="S255">BatchFileName</col>
		<col def="S255">SupportedVersions</col>
		<col def="S255">InitDlgProductName</col>
		<col def="I4">Attributes</col>
		<col def="S255">CertPassword</col>
		<row><td>CDROM_IMAGE</td><td>Media</td><td/><td/><td>{1B66AC04-17B5-4C28-ACBF-3593E7187EDF}</td><td/><td/><td/><td/><td>-1</td><td/><td>-1</td><td/><td/><td/><td>&lt;PATH_TO_CDROM_IMAGE_FILES&gt;</td><td/><td/><td/><td>14336</td><td/></row>
	</table>

	<table name="ISReleaseProDataAsFiles">
		<col key="yes" def="s72">ISRelease_</col>
		<col key="yes" def="s72">ISProductConfiguration_</col>
		<col key="yes" def="s38">Feature_</col>
	</table>

	<table name="ISReleaseProFeatureInclude">
		<col key="yes" def="s72">ISRelease_</col>
		<col key="yes" def="s72">ISProductConfiguration_</col>
		<col key="yes" def="s38">Feature_</col>
	</table>

	<table name="ISReleaseProOtherDiskFiles">
		<col key="yes" def="s72">ISRelease_</col>
		<col key="yes" def="s72">ISProductConfiguration_</col>
		<col key="yes" def="s38">ISDisk1File_</col>
		<col def="I4">Disk</col>
	</table>

	<table name="ISReleaseProPreviousMedias">
		<col key="yes" def="s72">ISRelease_</col>
		<col key="yes" def="s72">ISProductConfiguration_</col>
		<col key="yes" def="S50">PreviousProMedia</col>
		<col def="s255">MediaFile</col>
		<col def="S50">Version</col>
	</table>

	<table name="ISReleaseProReserveSpace">
		<col key="yes" def="s72">ISRelease_</col>
		<col key="yes" def="s72">ISProductConfiguration_</col>
		<col key="yes" def="i4">Disk</col>
		<col def="I4">KBsReserved</col>
	</table>

	<table name="ISReleasePublishInfo">
		<col key="yes" def="s72">ISRelease_</col>
		<col def="s72">ISProductConfiguration_</col>
		<col def="S255">Repository</col>
		<col def="S255">DisplayName</col>
		<col def="S255">Publisher</col>
		<col def="S255">Description</col>
		<col def="I4">ISAttributes</col>
	</table>

	<table name="ISRequiredFeature">
		<col key="yes" def="s38">RequiringFeature</col>
		<col key="yes" def="s38">RequiredFeature</col>
	</table>

	<table name="ISSQLConnection">
		<col key="yes" def="s72">ISSQLConnection</col>
		<col def="s255">Server</col>
		<col def="s255">Database</col>
		<col def="s255">UserName</col>
		<col def="s255">Password</col>
		<col def="s255">Authentication</col>
		<col def="i2">Attributes</col>
		<col def="i2">Order</col>
		<col def="S0">Comments</col>
		<col def="I4">CmdTimeout</col>
	</table>

	<table name="ISSQLConnectionDBServer">
		<col key="yes" def="s72">ISSQLConnectionDBServer</col>
		<col key="yes" def="s72">ISSQLConnection_</col>
		<col key="yes" def="s72">ISSQLDBMetaData_</col>
		<col def="i2">Order</col>
	</table>

	<table name="ISSQLConnectionScript">
		<col key="yes" def="s72">ISSQLConnection_</col>
		<col key="yes" def="s72">ISSQLScriptFile_</col>
		<col def="i2">Order</col>
	</table>

	<table name="ISSQLDBMetaData">
		<col key="yes" def="s72">ISSQLDBMetaData</col>
		<col def="S0">DisplayName</col>
		<col def="S0">AdoDriverName</col>
		<col def="S0">AdoCxnDriver</col>
		<col def="S0">AdoCxnServer</col>
		<col def="S0">AdoCxnDatabase</col>
		<col def="S0">AdoCxnUserID</col>
		<col def="S0">AdoCxnPassword</col>
		<col def="S0">AdoCxnWindowsSecurity</col>
		<col def="S0">AdoCxnNetLibrary</col>
		<col def="S0">TestDatabaseCmd</col>
		<col def="S0">TestTableCmd</col>
		<col def="S0">VersionInfoCmd</col>
		<col def="S0">VersionBeginToken</col>
		<col def="S0">VersionEndToken</col>
		<col def="S0">LocalInstanceNames</col>
		<col def="S0">CreateDbCmd</col>
		<col def="S0">SwitchDbCmd</col>
		<col def="I4">ISAttributes</col>
		<col def="S0">TestTableCmd2</col>
		<col def="S0">WinAuthentUserId</col>
		<col def="S0">DsnODBCName</col>
	</table>

	<table name="ISSQLRequirement">
		<col key="yes" def="s72">ISSQLRequirement</col>
		<col key="yes" def="s72">ISSQLConnection_</col>
		<col def="S15">MajorVersion</col>
		<col def="S25">ServicePackLevel</col>
		<col def="i4">Attributes</col>
		<col def="S72">ISSQLConnectionDBServer_</col>
	</table>

	<table name="ISSQLScriptError">
		<col key="yes" def="i4">ErrNumber</col>
		<col key="yes" def="S72">ISSQLScriptFile_</col>
		<col def="i2">ErrHandling</col>
		<col def="L255">Message</col>
		<col def="i2">Attributes</col>
	</table>

	<table name="ISSQLScriptFile">
		<col key="yes" def="s72">ISSQLScriptFile</col>
		<col def="s72">Component_</col>
		<col def="i2">Scheduling</col>
		<col def="L255">InstallText</col>
		<col def="L255">UninstallText</col>
		<col def="S0">ISBuildSourcePath</col>
		<col def="S0">Comments</col>
		<col def="i2">ErrorHandling</col>
		<col def="i2">Attributes</col>
		<col def="S15">Version</col>
	</table>

	<table name="ISSQLScriptImport">
		<col key="yes" def="s72">ISSQLScriptFile_</col>
		<col def="S255">Server</col>
		<col def="S255">Database</col>
		<col def="S255">UserName</col>
		<col def="S255">Password</col>
		<col def="i4">Authentication</col>
		<col def="S0">IncludeTables</col>
		<col def="S0">ExcludeTables</col>
		<col def="i4">Attributes</col>
	</table>

	<table name="ISSQLScriptReplace">
		<col key="yes" def="s72">ISSQLScriptReplace</col>
		<col key="yes" def="s72">ISSQLScriptFile_</col>
		<col def="S0">Search</col>
		<col def="S0">Replace</col>
		<col def="i4">Attributes</col>
	</table>

	<table name="ISScriptFile">
		<col key="yes" def="s255">ISScriptFile</col>
		<row><td>&lt;ISProjectDataFolder&gt;\Script Files\Setup.Rul</td></row>
	</table>

	<table name="ISSetupFile">
		<col key="yes" def="s72">ISSetupFile</col>
		<col def="S255">FileName</col>
		<col def="V0">Stream</col>
		<col def="S50">Language</col>
		<col def="I2">Splash</col>
		<col def="S0">Path</col>
		<row><td>SetupFile1</td><td>license.txt</td><td/><td>0</td><td>0</td><td>&lt;PATH_TO_INSTALL_FILES&gt;\license.txt</td></row>
		<row><td>SetupFile2</td><td>ReplaceTextInFile.vbs</td><td/><td>0</td><td>0</td><td>&lt;PATH_TO_SUPPORT_FILES&gt;\ReplaceTextInFile.vbs</td></row>
		<row><td>SetupFile3</td><td>SsaGraphSession.dll</td><td/><td>0</td><td>0</td><td>&lt;PATH_TO_PROGRAMFILES_FILES&gt;\bin\SsaGraphSession.dll</td></row>
	</table>

	<table name="ISSetupPrerequisites">
		<col key="yes" def="s72">ISSetupPrerequisites</col>
		<col def="S255">ISBuildSourcePath</col>
		<col def="I2">Order</col>
	</table>

	<table name="ISSetupType">
		<col key="yes" def="s38">ISSetupType</col>
		<col def="L255">Description</col>
		<col def="L255">Display_Name</col>
		<col def="i2">Display</col>
		<col def="S255">Comments</col>
		<row><td>Complete</td><td>##IDPROP_SETUPTYPE_COMPLETE_DESC##</td><td>##IDPROP_SETUPTYPE_COMPLETE##</td><td>1</td><td/></row>
		<row><td>Custom</td><td>##IDPROP_SETUPTYPE_CUSTOM_DESC_PRO##</td><td>##IDPROP_SETUPTYPE_CUSTOM##</td><td>2</td><td/></row>
	</table>

	<table name="ISSetupTypeFeatures">
		<col key="yes" def="s38">ISSetupType_</col>
		<col key="yes" def="s38">Feature_</col>
		<row><td>Complete</td><td>{0352E3F6-0F40-4F10-BFEF-1B113F870F6E}</td></row>
		<row><td>Complete</td><td>{20C1F0AB-9D55-48C3-8D2E-543090B20EE9}</td></row>
		<row><td>Complete</td><td>{230E25F0-F03B-455B-AEB6-788FE12F52D3}</td></row>
		<row><td>Complete</td><td>{27CC191D-043A-4904-980E-68D5A7CF786B}</td></row>
		<row><td>Complete</td><td>{5FD837EF-0207-481F-891C-9C4F0D65E875}</td></row>
		<row><td>Complete</td><td>{66720526-A5D8-4201-8086-F8CA9BBCD922}</td></row>
		<row><td>Complete</td><td>{68FEFF0D-6AFB-42D0-A5EA-79DA841D211D}</td></row>
		<row><td>Complete</td><td>{6FACE334-236D-41D3-89D7-48E4F72ABCA8}</td></row>
		<row><td>Complete</td><td>{E5CB530C-8C03-495E-A2F1-E094F587FC87}</td></row>
		<row><td>Custom</td><td>{0352E3F6-0F40-4F10-BFEF-1B113F870F6E}</td></row>
		<row><td>Custom</td><td>{20C1F0AB-9D55-48C3-8D2E-543090B20EE9}</td></row>
		<row><td>Custom</td><td>{230E25F0-F03B-455B-AEB6-788FE12F52D3}</td></row>
		<row><td>Custom</td><td>{27CC191D-043A-4904-980E-68D5A7CF786B}</td></row>
		<row><td>Custom</td><td>{5FD837EF-0207-481F-891C-9C4F0D65E875}</td></row>
		<row><td>Custom</td><td>{66720526-A5D8-4201-8086-F8CA9BBCD922}</td></row>
		<row><td>Custom</td><td>{68FEFF0D-6AFB-42D0-A5EA-79DA841D211D}</td></row>
		<row><td>Custom</td><td>{6FACE334-236D-41D3-89D7-48E4F72ABCA8}</td></row>
		<row><td>Custom</td><td>{E5CB530C-8C03-495E-A2F1-E094F587FC87}</td></row>
	</table>

	<table name="ISShortcutComponents">
		<col key="yes" def="s38">Shortcut_</col>
		<col key="yes" def="s72">Component_</col>
		<row><td>NewShortcut1</td><td>DefaultComponent</td></row>
		<row><td>NewShortcut2</td><td>Bin</td></row>
		<row><td>Optimize1</td><td>DefaultComponent</td></row>
	</table>

	<table name="ISStorages">
		<col key="yes" def="s72">Name</col>
		<col def="S0">ISBuildSourcePath</col>
	</table>

	<table name="ISString">
		<col key="yes" def="s255">ISString</col>
		<col key="yes" def="s50">ISLanguage_</col>
		<col def="S0">Value</col>
		<col def="I2">Encoded</col>
		<col def="S0">Comment</col>
		<col def="I4">TimeStamp</col>
		<row><td>IDPROP_SETUPTYPE_COMPLETE</td><td>1033</td><td>Complete</td><td>0</td><td/><td>-2095497426</td></row>
		<row><td>IDPROP_SETUPTYPE_COMPLETE_DESC</td><td>1033</td><td>Complete</td><td>0</td><td/><td>-2095497426</td></row>
		<row><td>IDPROP_SETUPTYPE_CUSTOM</td><td>1033</td><td>Custom</td><td>0</td><td/><td>-2095497426</td></row>
		<row><td>IDPROP_SETUPTYPE_CUSTOM_DESC_PRO</td><td>1033</td><td>Custom</td><td>0</td><td/><td>-2095497426</td></row>
		<row><td>IDS_FEATURE_DISPLAY_NAME2</td><td>1033</td><td>New Feature</td><td>0</td><td/><td>-1382478578</td></row>
		<row><td>IDS_FEATURE_DISPLAY_NAME3</td><td>1033</td><td>Server</td><td>0</td><td/><td>-1382464242</td></row>
		<row><td>IDS_FEATURE_DISPLAY_NAME4</td><td>1033</td><td>New Database</td><td>0</td><td/><td>-1248237014</td></row>
		<row><td>IDS_SHORTCUT_DISPLAY_NAME1</td><td>1033</td><td>Launch ConvertGroups.exe</td><td>0</td><td/><td>-1248213590</td></row>
		<row><td>IDS_SHORTCUT_DISPLAY_NAME2</td><td>1033</td><td>Launch InstallDatabase.exe</td><td>0</td><td/><td>-1248205398</td></row>
		<row><td>IDS_SHORTCUT_DISPLAY_NAME3</td><td>1033</td><td>Launch ConvertGroups.exe</td><td>0</td><td/><td>-1248248342</td></row>
		<row><td>IDS_SHORTCUT_DISPLAY_NAME4</td><td>1033</td><td>Launch InstallDatabase.exe</td><td>0</td><td/><td>-1248244246</td></row>
		<row><td>IDS_SHORTCUT_DISPLAY_NAME5</td><td>1033</td><td>Launch gzip.exe</td><td>0</td><td/><td>-1248244566</td></row>
		<row><td>IDS_SHORTCUT_DISPLAY_NAME6</td><td>1033</td><td>Launch Conversion.exe</td><td>0</td><td/><td>-1248249077</td></row>
		<row><td>IDS_SHORTCUT_DISPLAY_NAME7</td><td>1033</td><td>Launch Conversion.exe</td><td>0</td><td/><td>-1248228565</td></row>
		<row><td>IDS_SQLSCRIPT_INSTALLING</td><td>1033</td><td>Executing SQL Install Script...</td><td>0</td><td/><td>-2095497426</td></row>
		<row><td>IDS_SQLSCRIPT_UNINSTALLING</td><td>1033</td><td>Executing SQL Uninstall Script...</td><td>0</td><td/><td>-2095497426</td></row>
		<row><td>IDS__DialogId_12018</td><td>1033</td><td>InstallShield Wizard</td><td>0</td><td/><td>-174466034</td></row>
		<row><td>IDS__DialogId_12018_ControlId_1</td><td>1033</td><td>&amp;Next &gt;</td><td>0</td><td/><td>-174466034</td></row>
		<row><td>IDS__DialogId_12018_ControlId_12</td><td>1033</td><td>&lt; &amp;Back</td><td>0</td><td/><td>-174466034</td></row>
		<row><td>IDS__DialogId_12018_ControlId_1200</td><td>1033</td><td>@10550,10551;1;0;;0,128,128</td><td>0</td><td/><td>-174466034</td></row>
		<row><td>IDS__DialogId_12018_ControlId_2</td><td>1033</td><td>C</td><td>0</td><td/><td>-174466034</td></row>
		<row><td>IDS__DialogId_12018_ControlId_50</td><td>1033</td><td>Edit Data</td><td>0</td><td/><td>-174466034</td></row>
		<row><td>IDS__DialogId_12018_ControlId_51</td><td>1033</td><td>Enter requested data.</td><td>0</td><td/><td>-174466034</td></row>
		<row><td>IDS__DialogId_12018_ControlId_711</td><td>1033</td><td>This text is modifed by the 'szMsg' parameter.  You can reposition controls in this dialog and add static text fields.</td><td>0</td><td/><td>-174466034</td></row>
		<row><td>IDS__DialogId_12018_ControlId_719</td><td>1033</td><td>Field 1:</td><td>0</td><td/><td>-174466034</td></row>
		<row><td>IDS__DialogId_12018_ControlId_720</td><td>1033</td><td>Field 2:</td><td>0</td><td/><td>-174466034</td></row>
		<row><td>IDS__DialogId_12018_ControlId_9</td><td>1033</td><td>Cancel</td><td>0</td><td/><td>-174466034</td></row>
		<row><td>IDS__DialogId_12019</td><td>1033</td><td>InstallShield Wizard</td><td>0</td><td/><td>-174490834</td></row>
		<row><td>IDS__DialogId_12019_ControlId_1</td><td>1033</td><td>&amp;Next &gt;</td><td>0</td><td/><td>-174488786</td></row>
		<row><td>IDS__DialogId_12019_ControlId_12</td><td>1033</td><td>&lt; &amp;Back</td><td>0</td><td/><td>-174488786</td></row>
		<row><td>IDS__DialogId_12019_ControlId_1200</td><td>1033</td><td>@10550,10551;1;0;;0,128,128</td><td>0</td><td/><td>-174488786</td></row>
		<row><td>IDS__DialogId_12019_ControlId_2</td><td>1033</td><td>C</td><td>0</td><td/><td>-174488786</td></row>
		<row><td>IDS__DialogId_12019_ControlId_50</td><td>1033</td><td>Edit Data</td><td>0</td><td/><td>-174488786</td></row>
		<row><td>IDS__DialogId_12019_ControlId_51</td><td>1033</td><td>Enter requested data.</td><td>0</td><td/><td>-174488786</td></row>
		<row><td>IDS__DialogId_12019_ControlId_711</td><td>1033</td><td>This text is modifed by the 'szMsg' parameter.  You can reposition controls in this dialog and add static text fields.</td><td>0</td><td/><td>-174488786</td></row>
		<row><td>IDS__DialogId_12019_ControlId_719</td><td>1033</td><td>Field 1:</td><td>0</td><td/><td>-174490834</td></row>
		<row><td>IDS__DialogId_12019_ControlId_720</td><td>1033</td><td>Field 2:</td><td>0</td><td/><td>-174490834</td></row>
		<row><td>IDS__DialogId_12019_ControlId_721</td><td>1033</td><td>Field 3:</td><td>0</td><td/><td>-174490834</td></row>
		<row><td>IDS__DialogId_12019_ControlId_9</td><td>1033</td><td>Cancel</td><td>0</td><td/><td>-174488786</td></row>
		<row><td>IDS__DialogId_12020</td><td>1033</td><td>InstallShield Wizard</td><td>0</td><td/><td>-660950832</td></row>
		<row><td>IDS__DialogId_12020_ControlId_1</td><td>1033</td><td>&amp;Next &gt;</td><td>0</td><td/><td>-660950832</td></row>
		<row><td>IDS__DialogId_12020_ControlId_12</td><td>1033</td><td>&lt; &amp;Back</td><td>0</td><td/><td>-660950832</td></row>
		<row><td>IDS__DialogId_12020_ControlId_1200</td><td>1033</td><td>@10550,10551;1;0;;0,128,128   </td><td>0</td><td/><td>-660950832</td></row>
		<row><td>IDS__DialogId_12020_ControlId_2</td><td>1033</td><td>C</td><td>0</td><td/><td>-660950832</td></row>
		<row><td>IDS__DialogId_12020_ControlId_50</td><td>1033</td><td>Select Options</td><td>0</td><td/><td>-660950832</td></row>
		<row><td>IDS__DialogId_12020_ControlId_501</td><td>1033</td><td>Option One</td><td>0</td><td/><td>-660950832</td></row>
		<row><td>IDS__DialogId_12020_ControlId_502</td><td>1033</td><td>Option Two</td><td>0</td><td/><td>-660950832</td></row>
		<row><td>IDS__DialogId_12020_ControlId_503</td><td>1033</td><td>Option Three</td><td>0</td><td/><td>-660950832</td></row>
		<row><td>IDS__DialogId_12020_ControlId_504</td><td>1033</td><td>Option Four</td><td>0</td><td/><td>-660950832</td></row>
		<row><td>IDS__DialogId_12020_ControlId_51</td><td>1033</td><td>Select the options you want to install.</td><td>0</td><td/><td>-660950832</td></row>
		<row><td>IDS__DialogId_12020_ControlId_711</td><td>1033</td><td>To install a feature, click the check box next to it.  If the check box is clear, that feature will not be installed.</td><td>0</td><td/><td>-660950832</td></row>
		<row><td>IDS__DialogId_12020_ControlId_9</td><td>1033</td><td>Cancel</td><td>0</td><td/><td>-660950832</td></row>
		<row><td>IDS__NewSkinnableDialog</td><td>1033</td><td>InstallShield Wizard</td><td>0</td><td/><td>278302730</td></row>
		<row><td>IDS__NewSkinnableDialog_Back</td><td>1033</td><td>&lt; &amp;Back</td><td>0</td><td/><td>278304778</td></row>
		<row><td>IDS__NewSkinnableDialog_C</td><td>1033</td><td>C</td><td>0</td><td/><td>278304778</td></row>
		<row><td>IDS__NewSkinnableDialog_Cancel</td><td>1033</td><td>Cancel</td><td>0</td><td/><td>278302730</td></row>
		<row><td>IDS__NewSkinnableDialog_DlgDesc</td><td>1033</td><td>Your Sub-Title Here</td><td>0</td><td/><td>278304778</td></row>
		<row><td>IDS__NewSkinnableDialog_DlgTitle</td><td>1033</td><td>Your Title Here</td><td>0</td><td/><td>278304778</td></row>
		<row><td>IDS__NewSkinnableDialog_MainImage</td><td>1033</td><td>@10550,10551;1;0;;0,128,128   </td><td>0</td><td/><td>278302730</td></row>
		<row><td>IDS__NewSkinnableDialog_Next</td><td>1033</td><td>&amp;Next &gt;</td><td>0</td><td/><td>278302730</td></row>
		<row><td>ID_STRING1</td><td>1033</td><td>Application</td><td>0</td><td/><td>-1248255446</td></row>
		<row><td>ID_STRING10</td><td>1033</td><td>Select this feature to install the Optimize application</td><td>0</td><td/><td>-1248229781</td></row>
		<row><td>ID_STRING11</td><td>1033</td><td>AutoCAD R15 and R16 Files</td><td>0</td><td/><td>-174516049</td></row>
		<row><td>ID_STRING13</td><td>1033</td><td>Oracle 73 files</td><td>0</td><td/><td>236551791</td></row>
		<row><td>ID_STRING14</td><td>1033</td><td>Oracle 8.0 files</td><td>0</td><td/><td>236559983</td></row>
		<row><td>ID_STRING15</td><td>1033</td><td>Oracle 8.1 files</td><td>0</td><td/><td>236568175</td></row>
		<row><td>ID_STRING16</td><td>1033</td><td>Database</td><td>0</td><td/><td>236539503</td></row>
		<row><td>ID_STRING17</td><td>1033</td><td>Imperial measurement dmp files</td><td>0</td><td/><td>236523151</td></row>
		<row><td>ID_STRING18</td><td>1033</td><td>Metric measurement dmp files</td><td>0</td><td/><td>236535439</td></row>
		<row><td>ID_STRING2</td><td>1033</td><td>SSA Global Technologies</td><td>0</td><td/><td>-308681624</td></row>
		<row><td>ID_STRING3</td><td>1033</td><td>Optimize</td><td>0</td><td/><td>-308720312</td></row>
		<row><td>ID_STRING4</td><td>1033</td><td>Optimize</td><td>0</td><td/><td>-308689368</td></row>
		<row><td>ID_STRING5</td><td>1033</td><td>Drawing Files</td><td>0</td><td/><td>-308681489</td></row>
		<row><td>ID_STRING6</td><td>1033</td><td>Metric Drawing Files</td><td>0</td><td/><td>-308734705</td></row>
		<row><td>ID_STRING7</td><td>1033</td><td>AutoCAD R14 Files</td><td>0</td><td/><td>-174491505</td></row>
		<row><td>ID_STRING8</td><td>1033</td><td>SSA Global</td><td>0</td><td/><td>-1248242581</td></row>
		<row><td>ID_STRING9</td><td>1033</td><td>NewShortcut2</td><td>0</td><td/><td>1360664944</td></row>
	</table>

	<table name="ISVRoot">
		<col key="yes" def="s50">VRootKey</col>
		<col def="l255">VRootName</col>
		<col def="s50">VRootDir</col>
		<col def="i4">VRootProps</col>
		<col def="S255">VRootAppName</col>
		<col def="S255">VRootDefDoc</col>
		<col def="S255">Condition</col>
		<col def="I4">SessionTimeout</col>
		<col def="I4">ScriptTimeout</col>
		<col def="S255">AnonyUserName</col>
		<col def="S255">AnonyPasswrd</col>
		<col def="s72">Component_</col>
	</table>

	<table name="ISVRootAppMaps">
		<col key="yes" def="s255">VRootKey</col>
		<col key="yes" def="s255">VRootAppMapKey</col>
		<col def="l50">Extension</col>
		<col def="l255">ExecPath</col>
		<col def="l255">Verb</col>
		<col def="i4">AppMapProps</col>
	</table>

	<table name="ISWebSite">
		<col key="yes" def="s50">ISIISCommon_</col>
		<col def="s50">Port</col>
		<col def="S50">IP</col>
		<col def="s50">SiteNumber</col>
		<col def="I4">WebSiteProps</col>
	</table>

	<table name="ISXmlElement">
		<col key="yes" def="s72">ISXmlElement</col>
		<col def="s72">ISXmlFile_</col>
		<col def="S72">ISXmlElement_Parent</col>
		<col def="L255">XPath</col>
		<col def="L255">Content</col>
		<col def="I4">ISAttributes</col>
	</table>

	<table name="ISXmlElementAttrib">
		<col key="yes" def="s72">ISXmlElementAttrib</col>
		<col key="yes" def="s72">ISXmlElement_</col>
		<col def="L255">Name</col>
		<col def="L255">Value</col>
		<col def="I4">ISAttributes</col>
	</table>

	<table name="ISXmlFile">
		<col key="yes" def="s72">ISXmlFile</col>
		<col def="l255">FileName</col>
		<col def="s72">Component_</col>
		<col def="s50">Directory</col>
		<col def="I4">ISAttributes</col>
	</table>

	<table name="ISXmlLocator">
		<col key="yes" def="s72">Signature_</col>
		<col key="yes" def="S72">Parent</col>
		<col def="S255">Element</col>
		<col def="S255">Attribute</col>
		<col def="I2">ISAttributes</col>
	</table>

	<table name="Icon">
		<col key="yes" def="s72">Name</col>
		<col def="V0">Data</col>
		<col def="S255">ISBuildSourcePath</col>
		<col def="I2">ISIconIndex</col>
		<row><td>ARPPRODUCTICON.exe</td><td/><td>&lt;PATH_TO_PROGRAMFILES_FILES&gt;\bin\Optimize.ico</td><td>0</td></row>
		<row><td>NewShortcut1_D1AFEDECE52443E4A3AA3F48A6480B05.exe</td><td/><td>&lt;TARGETDIR&gt;\bin\Optimize.ico</td><td/></row>
		<row><td>NewShortcut2_52DE9F9FC8F34C3F88AA66134387F7A4.exe</td><td/><td>&lt;TARGETDIR&gt;\bin\Optimize.ico</td><td/></row>
		<row><td>Optimize1_D1AFEDECE52443E4A3AA3F48A6480B05.exe</td><td/><td>&lt;TARGETDIR&gt;\bin\Optimize.ico</td><td/></row>
	</table>

	<table name="InstallShield">
		<col key="yes" def="s72">Property</col>
		<col def="S0">Value</col>
		<row><td>ActiveLanguage</td><td>1033</td></row>
		<row><td>Comments</td><td/></row>
		<row><td>CurrentMedia</td><td dt:dt="bin.base64" md5="4dca6c2e8aaaab8f5c45189cd6eade53">
QwBEAFIATwBNAF8ASQBNAEEARwBFAAEATQBlAGQAaQBhAA==
			</td></row>
		<row><td>DoMaintenance</td><td>1</td></row>
		<row><td>GenerateMIF</td><td>0</td></row>
		<row><td>ISCompilerOption_CompileBeforeBuild</td><td>1</td></row>
		<row><td>ISCompilerOption_Debug</td><td>0</td></row>
		<row><td>ISCompilerOption_IncludePath</td><td/></row>
		<row><td>ISCompilerOption_MaxErrors</td><td>50</td></row>
		<row><td>ISCompilerOption_MaxWarnings</td><td>50</td></row>
		<row><td>ISCompilerOption_OutputPath</td><td>&lt;ISProjectDataFolder&gt;\Script Files</td></row>
		<row><td>ISCompilerOption_PreProcessor</td><td/></row>
		<row><td>ISCompilerOption_WarningLevel</td><td>3</td></row>
		<row><td>ISCompilerOption_WarningsAsErrors</td><td>0</td></row>
		<row><td>ISEnableUpdateService</td><td>0</td></row>
		<row><td>ISEngineBinding</td><td>1</td></row>
		<row><td>ISMaintenanceExperience</td><td>2</td></row>
		<row><td>ISOSFilter</td><td>13697024</td></row>
		<row><td>ISShowUpdateUI</td><td>1</td></row>
		<row><td>ISUSLock</td><td>{98F59D52-305A-4F20-9163-F378C31D7616}</td></row>
		<row><td>ISUSSignature</td><td>{3F1E9133-763E-49D5-8BD4-8599D8863ECD}</td></row>
		<row><td>MIFFileName</td><td>Status</td></row>
		<row><td>MIFSerial</td><td/></row>
		<row><td>Owner</td><td/></row>
		<row><td>SaveAsSchema</td><td/></row>
		<row><td>SccDatabasePath</td><td>\\Exeaim-0011\VSS\Optimize,</td></row>
		<row><td>SccEnabled</td><td>-1</td></row>
		<row><td>SccPath</td><td>"$/Optimize/OPTIMIZEBASE4/Install", OBDAAAAA</td></row>
		<row><td>SchemaVersion</td><td>761</td></row>
		<row><td>Script</td><td>1</td></row>
		<row><td>SetupArguments</td><td/></row>
		<row><td>TARGETDIR</td><td>&lt;FOLDER_APPLICATIONS&gt;\&lt;IFX_COMPANY_NAME&gt;\&lt;IFX_PRODUCT_NAME&gt;</td></row>
		<row><td>Type</td><td>PRO</td></row>
	</table>

	<table name="ListBox">
		<col key="yes" def="s72">Property</col>
		<col key="yes" def="i2">Order</col>
		<col def="s64">Value</col>
		<col def="L64">Text</col>
	</table>

	<table name="ListView">
		<col key="yes" def="s72">Property</col>
		<col key="yes" def="i2">Order</col>
		<col def="s64">Value</col>
		<col def="L64">Text</col>
		<col def="S72">Binary_</col>
	</table>

	<table name="MsiPatchOldAssemblyFile">
		<col key="yes" def="s72">File_</col>
		<col key="yes" def="S72">Assembly_</col>
	</table>

	<table name="MsiPatchOldAssemblyName">
		<col key="yes" def="s72">Assembly_</col>
		<col key="yes" def="s255">Name</col>
		<col def="S255">Value</col>
	</table>

	<table name="Property">
		<col key="yes" def="s72">Property</col>
		<col def="L0">Value</col>
		<col def="S255">ISComments</col>
		<row><td>ARPNOMODIFY</td><td>1</td><td/></row>
		<row><td>ARPPRODUCTICON</td><td>ARPPRODUCTICON.exe</td><td/></row>
		<row><td>ARPURLINFOABOUT</td><td>http://www.ssaglobal.com</td><td/></row>
		<row><td>DWUSINTERVAL</td><td>30</td><td/></row>
		<row><td>DWUSLINK</td><td>CE0C70F8C9ACC0EFDEACD7EFAEEC978F39CBC78FDE9BB0FFCE4BF76FDEDC271FE9ECA0A8E9AC</td><td/></row>
		<row><td>DefaultUIFont</td><td>Tahoma8</td><td/></row>
		<row><td>ISAppExeFile</td><td>OptimizeReports.exe</td><td/></row>
		<row><td>ISENABLEDWUSFINISHDIALOG</td><td/><td/></row>
		<row><td>ISVROOT_PORT_NO</td><td>0</td><td/></row>
		<row><td>IS_SQLSERVER_AUTHENTICATION</td><td>0</td><td/></row>
		<row><td>IS_SQLSERVER_DATABASE</td><td/><td/></row>
		<row><td>IS_SQLSERVER_PASSWORD</td><td/><td/></row>
		<row><td>IS_SQLSERVER_SERVER</td><td/><td/></row>
		<row><td>IS_SQLSERVER_USERNAME</td><td>sa</td><td/></row>
		<row><td>Manufacturer</td><td>SSA Global</td><td/></row>
		<row><td>Option_Four</td><td>Option Four</td><td/></row>
		<row><td>Option_One</td><td>Option One</td><td/></row>
		<row><td>Option_Three</td><td>Option Three</td><td/></row>
		<row><td>Option_Two</td><td>Option Two</td><td/></row>
		<row><td>PROGMSG_IIS_CREATEAPPPOOL</td><td>##IDS_PROGMSG_IIS_CREATEAPPPOOL##</td><td/></row>
		<row><td>PROGMSG_IIS_CREATEAPPPOOLS</td><td>##IDS_PROGMSG_IIS_CREATEAPPPOOLS##</td><td/></row>
		<row><td>PROGMSG_IIS_CREATEVROOT</td><td>##IDS_PROGMSG_IIS_CREATEVROOT##</td><td/></row>
		<row><td>PROGMSG_IIS_CREATEVROOTS</td><td>##IDS_PROGMSG_IIS_CREATEVROOTS##</td><td/></row>
		<row><td>PROGMSG_IIS_CREATEWEBSERVICEEXTENSION</td><td>##IDS_PROGMSG_IIS_CREATEWEBSERVICEEXTENSION##</td><td/></row>
		<row><td>PROGMSG_IIS_CREATEWEBSERVICEEXTENSIONS</td><td>##IDS_PROGMSG_IIS_CREATEWEBSERVICEEXTENSIONS##</td><td/></row>
		<row><td>PROGMSG_IIS_EXTRACT</td><td>##IDS_PROGMSG_IIS_EXTRACT##</td><td/></row>
		<row><td>PROGMSG_IIS_EXTRACTDONE</td><td>##IDS_PROGMSG_IIS_EXTRACTDONE##</td><td/></row>
		<row><td>PROGMSG_IIS_EXTRACTDONEz</td><td>##IDS_PROGMSG_IIS_EXTRACTDONE##</td><td/></row>
		<row><td>PROGMSG_IIS_EXTRACTzDONE</td><td>##IDS_PROGMSG_IIS_EXTRACTDONE##</td><td/></row>
		<row><td>PROGMSG_IIS_REMOVEAPPPOOL</td><td>##IDS_PROGMSG_IIS_REMOVEAPPPOOL##</td><td/></row>
		<row><td>PROGMSG_IIS_REMOVEAPPPOOLS</td><td>##IDS_PROGMSG_IIS_REMOVEAPPPOOLS##</td><td/></row>
		<row><td>PROGMSG_IIS_REMOVESITE</td><td>##IDS_PROGMSG_IIS_REMOVESITE##</td><td/></row>
		<row><td>PROGMSG_IIS_REMOVEVROOT</td><td>##IDS_PROGMSG_IIS_REMOVEVROOT##</td><td/></row>
		<row><td>PROGMSG_IIS_REMOVEVROOTS</td><td>##IDS_PROGMSG_IIS_REMOVEVROOTS##</td><td/></row>
		<row><td>PROGMSG_IIS_REMOVEWEBSERVICEEXTENSION</td><td>##IDS_PROGMSG_IIS_REMOVEWEBSERVICEEXTENSION##</td><td/></row>
		<row><td>PROGMSG_IIS_REMOVEWEBSERVICEEXTENSIONS</td><td>##IDS_PROGMSG_IIS_REMOVEWEBSERVICEEXTENSIONS##</td><td/></row>
		<row><td>PROGMSG_IIS_ROLLBACKAPPPOOLS</td><td>##IDS_PROGMSG_IIS_ROLLBACKAPPPOOLS##</td><td/></row>
		<row><td>PROGMSG_IIS_ROLLBACKVROOTS</td><td>##IDS_PROGMSG_IIS_ROLLBACKVROOTS##</td><td/></row>
		<row><td>PROGMSG_IIS_ROLLBACKWEBSERVICEEXTENSIONS</td><td>##IDS_PROGMSG_IIS_ROLLBACKWEBSERVICEEXTENSIONS##</td><td/></row>
		<row><td>ProObjDiffSupported</td><td>0</td><td/></row>
		<row><td>ProObjDisplayName</td><td>##IDS_PROOBJ_DISPLAYNAME##</td><td/></row>
		<row><td>ProObjFlags</td><td>21</td><td/></row>
		<row><td>ProObjHTMLPage</td><td>##IDS_PROOBJ_HTMLPAGE##</td><td/></row>
		<row><td>ProObjIconFile</td><td>##IDS_PROOBJ_ICONFILE##</td><td/></row>
		<row><td>ProObjRegAtBuild</td><td>1</td><td/></row>
		<row><td>ProObjRegWizard</td><td>0</td><td/></row>
		<row><td>ProObjShortName</td><td>##IDS_PROOBJ_SHORTNAME##</td><td/></row>
		<row><td>ProObjUseDefaultSettings</td><td>##IDS_PROOBJ_USEDEFAULTSETTINGS##</td><td/></row>
		<row><td>ProductCode</td><td>{52DE9F9F-C8F3-4C3F-88AA-66134387F7A4}</td><td/></row>
		<row><td>ProductName</td><td>Optimize</td><td/></row>
		<row><td>ProductVersion</td><td>3.00.0000</td><td/></row>
	</table>

	<table name="RadioButton">
		<col key="yes" def="s72">Property</col>
		<col key="yes" def="i2">Order</col>
		<col def="s64">Value</col>
		<col def="i2">X</col>
		<col def="i2">Y</col>
		<col def="i2">Width</col>
		<col def="i2">Height</col>
		<col def="L64">Text</col>
		<col def="L50">Help</col>
		<col def="I4">ISControlId</col>
	</table>

	<table name="Registry">
		<col key="yes" def="s72">Registry</col>
		<col def="i2">Root</col>
		<col def="s255">Key</col>
		<col def="S255">Name</col>
		<col def="S0">Value</col>
		<col def="s72">ISRegistrySet_</col>
		<col def="I4">ISAttributes</col>
		<row><td>Registry1</td><td>2</td><td>SOFTWARE\SSA Global\Optimize</td><td>LogFilePath</td><td>&lt;TARGETDIR&gt;\log</td><td>AppSettings</td><td>0</td></row>
		<row><td>Registry10</td><td>2</td><td>SOFTWARE\SSA Global\Optimize</td><td>OptimizationLogMode</td><td>0</td><td>AppSettings</td><td/></row>
		<row><td>Registry11</td><td>0</td><td>.rep\ShellNew</td><td/><td/><td>AppSettings</td><td>0</td></row>
		<row><td>Registry113</td><td>2</td><td>SOFTWARE\SSA Global\Optimize\ColorProductGroups</td><td/><td/><td>AppSettings</td><td/></row>
		<row><td>Registry12</td><td>0</td><td>.rep\ShellNew</td><td>NullFile</td><td/><td>AppSettings</td><td>0</td></row>
		<row><td>Registry123</td><td>2</td><td>SOFTWARE\SSA Global\Optimize\ColorProductGroups</td><td>AutoReset</td><td>0</td><td>AppSettings</td><td/></row>
		<row><td>Registry124</td><td>2</td><td>SOFTWARE\SSA Global\Optimize\ColorProductGroups</td><td>ByProduct</td><td>0</td><td>AppSettings</td><td/></row>
		<row><td>Registry125</td><td>2</td><td>SOFTWARE\SSA Global\Optimize\ColorProductGroups</td><td>Colors</td><td>220|</td><td>AppSettings</td><td/></row>
		<row><td>Registry126</td><td>2</td><td>SOFTWARE\SSA Global\Optimize\ColorProductGroups</td><td>Groups.LesLong.1079</td><td>1880,1|1881,2|</td><td>AppSettings</td><td/></row>
		<row><td>Registry127</td><td>2</td><td>SOFTWARE\SSA Global\Optimize\ColorProductGroups</td><td>Groups.Optimize.1086</td><td>2062,1|2063,2|</td><td>AppSettings</td><td/></row>
		<row><td>Registry128</td><td>2</td><td>SOFTWARE\SSA Global\Optimize\ColorProductGroups</td><td>Groups.Optimize.1087</td><td>2064,1|</td><td>AppSettings</td><td/></row>
		<row><td>Registry129</td><td>2</td><td>SOFTWARE\SSA Global\Optimize\ColorProductGroups</td><td>Groups.LesLong.1081</td><td>1882,1|1883,2|</td><td>AppSettings</td><td/></row>
		<row><td>Registry13</td><td>0</td><td>OptimizeReports.Document</td><td/><td/><td>AppSettings</td><td>1</td></row>
		<row><td>Registry130</td><td>2</td><td>SOFTWARE\SSA Global\Optimize\ColorProductGroups</td><td>Groups.Optimize.1096</td><td>2336,130|</td><td>AppSettings</td><td/></row>
		<row><td>Registry131</td><td>2</td><td>SOFTWARE\SSA Global\Optimize\ColorProductGroups</td><td>Groups.Optimize.1095</td><td>2299,220|</td><td>AppSettings</td><td/></row>
		<row><td>Registry132</td><td>2</td><td>SOFTWARE\SSA Global\Optimize\Dialogs</td><td/><td/><td>AppSettings</td><td/></row>
		<row><td>Registry133</td><td>2</td><td>SOFTWARE\SSA Global\Optimize\Dialogs\SectionProperties</td><td/><td/><td>AppSettings</td><td/></row>
		<row><td>Registry134</td><td>2</td><td>SOFTWARE\SSA Global\Optimize\Dialogs\SectionProperties</td><td>ForkFixedPerTrip</td><td>0.067100</td><td>AppSettings</td><td/></row>
		<row><td>Registry135</td><td>2</td><td>SOFTWARE\SSA Global\Optimize\Dialogs\SectionProperties</td><td>ForkLaborRate</td><td>20.750000</td><td>AppSettings</td><td/></row>
		<row><td>Registry136</td><td>2</td><td>SOFTWARE\SSA Global\Optimize\Dialogs\SectionProperties</td><td>ReplenishAvgDistance</td><td>100.000000</td><td>AppSettings</td><td/></row>
		<row><td>Registry137</td><td>2</td><td>SOFTWARE\SSA Global\Optimize\Dialogs\SectionProperties</td><td>ForkInsertionTime</td><td>0.663700</td><td>AppSettings</td><td/></row>
		<row><td>Registry138</td><td>2</td><td>SOFTWARE\SSA Global\Optimize\Dialogs\SectionProperties</td><td>ForkPickupTime</td><td>0.241900</td><td>AppSettings</td><td/></row>
		<row><td>Registry139</td><td>2</td><td>SOFTWARE\SSA Global\Optimize\Dialogs\SectionProperties</td><td>PalletsPerPutawayTrip</td><td>1.700000</td><td>AppSettings</td><td/></row>
		<row><td>Registry14</td><td>0</td><td>OptimizeReports.Document</td><td/><td>Optimize Document</td><td>AppSettings</td><td>0</td></row>
		<row><td>Registry140</td><td>2</td><td>SOFTWARE\SSA Global\Optimize\Dialogs\SectionProperties</td><td>SelectionTravelTime</td><td>0.002900</td><td>AppSettings</td><td/></row>
		<row><td>Registry141</td><td>2</td><td>SOFTWARE\SSA Global\Optimize\Dialogs\SectionProperties</td><td>SelectFixedPerTrip</td><td>0.065900</td><td>AppSettings</td><td/></row>
		<row><td>Registry142</td><td>2</td><td>SOFTWARE\SSA Global\Optimize\Dialogs\SectionProperties</td><td>SelectionLaborRate</td><td>17.500000</td><td>AppSettings</td><td/></row>
		<row><td>Registry143</td><td>2</td><td>SOFTWARE\SSA Global\Optimize\Dialogs\SectionProperties</td><td>AverageCubePerTrip</td><td>0.000000</td><td>AppSettings</td><td/></row>
		<row><td>Registry144</td><td>2</td><td>SOFTWARE\SSA Global\Optimize\Dialogs\SectionProperties</td><td>StockerTravelTime</td><td>0.002900</td><td>AppSettings</td><td/></row>
		<row><td>Registry145</td><td>2</td><td>SOFTWARE\SSA Global\Optimize\Dialogs\SectionProperties</td><td>StockerFixedPerTrip</td><td>0.078200</td><td>AppSettings</td><td/></row>
		<row><td>Registry146</td><td>2</td><td>SOFTWARE\SSA Global\Optimize\Dialogs\SectionProperties</td><td>StockerLaborRate</td><td>17.500000</td><td>AppSettings</td><td/></row>
		<row><td>Registry147</td><td>2</td><td>SOFTWARE\SSA Global\Optimize\Dialogs\SectionProperties</td><td>OrderCount</td><td>250</td><td>AppSettings</td><td/></row>
		<row><td>Registry148</td><td>2</td><td>SOFTWARE\SSA Global\Optimize\Dialogs\SectionProperties</td><td>AverageOrderQuantity</td><td>0</td><td>AppSettings</td><td/></row>
		<row><td>Registry149</td><td>2</td><td>SOFTWARE\SSA Global\Optimize\Dialogs\SectionProperties</td><td>ContainerQuantity</td><td>0</td><td>AppSettings</td><td/></row>
		<row><td>Registry15</td><td>0</td><td>OptimizeReports.Document\DefaultIcon</td><td/><td/><td>AppSettings</td><td>1</td></row>
		<row><td>Registry150</td><td>2</td><td>SOFTWARE\SSA Global\Optimize\Dialogs\SectionProperties</td><td>ApplyBrokenOrder</td><td>0</td><td>AppSettings</td><td/></row>
		<row><td>Registry151</td><td>2</td><td>SOFTWARE\SSA Global\Optimize\Dialogs\SectionProperties</td><td>ForkTravelTime</td><td>0.002300</td><td>AppSettings</td><td/></row>
		<row><td>Registry152</td><td>2</td><td>SOFTWARE\SSA Global\Optimize\Dialogs\CapitalCostStartup</td><td/><td/><td>AppSettings</td><td/></row>
		<row><td>Registry153</td><td>2</td><td>SOFTWARE\SSA Global\Optimize\Dialogs\CapitalCostStartup</td><td>Options</td><td>1|1|None|8|5000|</td><td>AppSettings</td><td/></row>
		<row><td>Registry154</td><td>2</td><td>SOFTWARE\SSA Global\Optimize\Dialogs\ColorModel</td><td/><td/><td>AppSettings</td><td/></row>
		<row><td>Registry155</td><td>2</td><td>SOFTWARE\SSA Global\Optimize\Dialogs\ColorModel</td><td>Mode</td><td>0</td><td>AppSettings</td><td/></row>
		<row><td>Registry156</td><td>2</td><td>SOFTWARE\SSA Global\Optimize\Dialogs\ColorModel</td><td>Color</td><td>5</td><td>AppSettings</td><td/></row>
		<row><td>Registry157</td><td>2</td><td>SOFTWARE\SSA Global\Optimize\Dialogs\ColorModel</td><td>Clear</td><td>1</td><td>AppSettings</td><td/></row>
		<row><td>Registry158</td><td>2</td><td>SOFTWARE\SSA Global\Optimize\Dialogs\ColorModel</td><td>Origin</td><td>1</td><td>AppSettings</td><td/></row>
		<row><td>Registry159</td><td>2</td><td>SOFTWARE\SSA Global\Optimize\Dialogs\ColorModel</td><td>Attribute</td><td>Movement</td><td>AppSettings</td><td/></row>
		<row><td>Registry16</td><td>2</td><td>SOFTWARE\SSA Global\Optimize</td><td>Home</td><td>&lt;TARGETDIR&gt;</td><td>AppSettings</td><td/></row>
		<row><td>Registry160</td><td>2</td><td>SOFTWARE\SSA Global\Optimize\Dialogs\ColorModel</td><td>Direction</td><td>0</td><td>AppSettings</td><td/></row>
		<row><td>Registry161</td><td>2</td><td>SOFTWARE\SSA Global\Optimize\Dialogs\CostComparisonDialog</td><td/><td/><td>AppSettings</td><td/></row>
		<row><td>Registry162</td><td>2</td><td>SOFTWARE\SSA Global\Optimize\Dialogs\CostComparisonDialog</td><td>TimeUnits</td><td>1</td><td>AppSettings</td><td/></row>
		<row><td>Registry163</td><td>2</td><td>SOFTWARE\SSA Global\Optimize\Dialogs\CostComparisonDialog</td><td>TimeValue</td><td>6</td><td>AppSettings</td><td/></row>
		<row><td>Registry164</td><td>2</td><td>SOFTWARE\SSA Global\Optimize\Dialogs\CostComparisonDialog</td><td>OptimizeSoma1</td><td>13</td><td>AppSettings</td><td/></row>
		<row><td>Registry165</td><td>2</td><td>SOFTWARE\SSA Global\Optimize\Dialogs\CostComparisonDialog</td><td>OptimizeDemonstrationWithNoHZ_Training</td><td>7</td><td>AppSettings</td><td/></row>
		<row><td>Registry166</td><td>2</td><td>SOFTWARE\SSA Global\Optimize\Dialogs\Main</td><td/><td/><td>AppSettings</td><td/></row>
		<row><td>Registry167</td><td>2</td><td>SOFTWARE\SSA Global\Optimize\Dialogs\Main</td><td>ShowWizard</td><td>Yes</td><td>AppSettings</td><td/></row>
		<row><td>Registry168</td><td>2</td><td>SOFTWARE\SSA Global\Optimize\Dialogs\PickPathProperties</td><td/><td/><td>AppSettings</td><td/></row>
		<row><td>Registry169</td><td>2</td><td>SOFTWARE\SSA Global\Optimize\Dialogs\PickPathProperties</td><td>PathType</td><td>4</td><td>AppSettings</td><td/></row>
		<row><td>Registry17</td><td>2</td><td>SOFTWARE\SSA Global\Optimize</td><td>MajorVersion</td><td>3.0</td><td>AppSettings</td><td/></row>
		<row><td>Registry170</td><td>2</td><td>SOFTWARE\SSA Global\Optimize\Dialogs\PickPathProperties</td><td>BaysInPattern</td><td>2</td><td>AppSettings</td><td/></row>
		<row><td>Registry171</td><td>2</td><td>SOFTWARE\SSA Global\Optimize\Dialogs\PickPathProperties</td><td>LeftBayStart</td><td>00</td><td>AppSettings</td><td/></row>
		<row><td>Registry172</td><td>2</td><td>SOFTWARE\SSA Global\Optimize\Dialogs\PickPathProperties</td><td>LeftBayStep</td><td>1</td><td>AppSettings</td><td/></row>
		<row><td>Registry173</td><td>2</td><td>SOFTWARE\SSA Global\Optimize\Dialogs\PickPathProperties</td><td>LeftBayScheme</td><td>2</td><td>AppSettings</td><td/></row>
		<row><td>Registry174</td><td>2</td><td>SOFTWARE\SSA Global\Optimize\Dialogs\PickPathProperties</td><td>LeftBayPattern</td><td> </td><td>AppSettings</td><td/></row>
		<row><td>Registry175</td><td>2</td><td>SOFTWARE\SSA Global\Optimize\Dialogs\PickPathProperties</td><td>LeftLevelStart</td><td>2</td><td>AppSettings</td><td/></row>
		<row><td>Registry176</td><td>2</td><td>SOFTWARE\SSA Global\Optimize\Dialogs\PickPathProperties</td><td>LeftLevelStep</td><td>1</td><td>AppSettings</td><td/></row>
		<row><td>Registry177</td><td>2</td><td>SOFTWARE\SSA Global\Optimize\Dialogs\PickPathProperties</td><td>LeftLevelScheme</td><td>0</td><td>AppSettings</td><td/></row>
		<row><td>Registry178</td><td>2</td><td>SOFTWARE\SSA Global\Optimize\Dialogs\PickPathProperties</td><td>LeftLevelPattern</td><td> </td><td>AppSettings</td><td/></row>
		<row><td>Registry179</td><td>2</td><td>SOFTWARE\SSA Global\Optimize\Dialogs\PickPathProperties</td><td>LeftLevelBreak</td><td>1</td><td>AppSettings</td><td/></row>
		<row><td>Registry18</td><td>2</td><td>SOFTWARE\SSA Global\Optimize</td><td>MinorVersion</td><td>0.0</td><td>AppSettings</td><td/></row>
		<row><td>Registry180</td><td>2</td><td>SOFTWARE\SSA Global\Optimize\Dialogs\PickPathProperties</td><td>LeftLocStart</td><td>2</td><td>AppSettings</td><td/></row>
		<row><td>Registry181</td><td>2</td><td>SOFTWARE\SSA Global\Optimize\Dialogs\PickPathProperties</td><td>LeftLocStep</td><td>1</td><td>AppSettings</td><td/></row>
		<row><td>Registry182</td><td>2</td><td>SOFTWARE\SSA Global\Optimize\Dialogs\PickPathProperties</td><td>LeftLocScheme</td><td>0</td><td>AppSettings</td><td/></row>
		<row><td>Registry183</td><td>2</td><td>SOFTWARE\SSA Global\Optimize\Dialogs\PickPathProperties</td><td>LeftLocPattern</td><td> </td><td>AppSettings</td><td/></row>
		<row><td>Registry184</td><td>2</td><td>SOFTWARE\SSA Global\Optimize\Dialogs\PickPathProperties</td><td>LeftLocBreak</td><td>1</td><td>AppSettings</td><td/></row>
		<row><td>Registry185</td><td>2</td><td>SOFTWARE\SSA Global\Optimize\Dialogs\PickPathProperties</td><td>RightBayStart</td><td>00</td><td>AppSettings</td><td/></row>
		<row><td>Registry186</td><td>2</td><td>SOFTWARE\SSA Global\Optimize\Dialogs\PickPathProperties</td><td>RightBayStep</td><td>1</td><td>AppSettings</td><td/></row>
		<row><td>Registry187</td><td>2</td><td>SOFTWARE\SSA Global\Optimize\Dialogs\PickPathProperties</td><td>RightBayScheme</td><td>1</td><td>AppSettings</td><td/></row>
		<row><td>Registry188</td><td>2</td><td>SOFTWARE\SSA Global\Optimize\Dialogs\PickPathProperties</td><td>RightBayPattern</td><td> </td><td>AppSettings</td><td/></row>
		<row><td>Registry189</td><td>2</td><td>SOFTWARE\SSA Global\Optimize\Dialogs\PickPathProperties</td><td>RightLevelStart</td><td>2</td><td>AppSettings</td><td/></row>
		<row><td>Registry19</td><td>2</td><td>SOFTWARE\SSA Global\Optimize</td><td>BuildNumber</td><td>1</td><td>AppSettings</td><td/></row>
		<row><td>Registry190</td><td>2</td><td>SOFTWARE\SSA Global\Optimize\Dialogs\PickPathProperties</td><td>RightLevelStep</td><td>1</td><td>AppSettings</td><td/></row>
		<row><td>Registry191</td><td>2</td><td>SOFTWARE\SSA Global\Optimize\Dialogs\PickPathProperties</td><td>RightLevelScheme</td><td>0</td><td>AppSettings</td><td/></row>
		<row><td>Registry192</td><td>2</td><td>SOFTWARE\SSA Global\Optimize\Dialogs\PickPathProperties</td><td>RightLevelPattern</td><td> </td><td>AppSettings</td><td/></row>
		<row><td>Registry193</td><td>2</td><td>SOFTWARE\SSA Global\Optimize\Dialogs\PickPathProperties</td><td>RightLevelBreak</td><td>1</td><td>AppSettings</td><td/></row>
		<row><td>Registry194</td><td>2</td><td>SOFTWARE\SSA Global\Optimize\Dialogs\PickPathProperties</td><td>RightLocStart</td><td>2</td><td>AppSettings</td><td/></row>
		<row><td>Registry195</td><td>2</td><td>SOFTWARE\SSA Global\Optimize\Dialogs\PickPathProperties</td><td>RightLocStep</td><td>1</td><td>AppSettings</td><td/></row>
		<row><td>Registry196</td><td>2</td><td>SOFTWARE\SSA Global\Optimize\Dialogs\PickPathProperties</td><td>RightLocScheme</td><td>0</td><td>AppSettings</td><td/></row>
		<row><td>Registry197</td><td>2</td><td>SOFTWARE\SSA Global\Optimize\Dialogs\PickPathProperties</td><td>RightLocPattern</td><td>1</td><td>AppSettings</td><td/></row>
		<row><td>Registry198</td><td>2</td><td>SOFTWARE\SSA Global\Optimize\Dialogs\PickPathProperties</td><td>RightLocBreak</td><td> </td><td>AppSettings</td><td/></row>
		<row><td>Registry199</td><td>2</td><td>SOFTWARE\SSA Global\Optimize\Dialogs\ProductGroupLayoutStartup</td><td/><td/><td>AppSettings</td><td/></row>
		<row><td>Registry2</td><td>0</td><td>.rep</td><td/><td/><td>AppSettings</td><td>1</td></row>
		<row><td>Registry20</td><td>2</td><td>SOFTWARE\SSA Global\Optimize</td><td>Debug</td><td>0</td><td>AppSettings</td><td/></row>
		<row><td>Registry200</td><td>2</td><td>SOFTWARE\SSA Global\Optimize\Dialogs\ProductGroupLayoutStartup</td><td>Options</td><td>1|1|Expert|5000|0</td><td>AppSettings</td><td/></row>
		<row><td>Registry201</td><td>2</td><td>SOFTWARE\SSA Global\Optimize\Dialogs\ProductLayoutStartup</td><td/><td/><td>AppSettings</td><td/></row>
		<row><td>Registry202</td><td>2</td><td>SOFTWARE\SSA Global\Optimize\Dialogs\ProductLayoutStartup</td><td>Options</td><td>2|1|1|1|0|0|1.000000|0|1|1|0|Expert|5000|0|25|</td><td>AppSettings</td><td/></row>
		<row><td>Registry205</td><td>2</td><td>SOFTWARE\SSA Global\Optimize\ColorProductGroups</td><td>+</td><td/><td>AppSettings</td><td/></row>
		<row><td>Registry206</td><td>2</td><td>SOFTWARE\SSA Global\Optimize\Dialogs\CapitalCostStartup</td><td>+</td><td/><td>AppSettings</td><td/></row>
		<row><td>Registry207</td><td>2</td><td>SOFTWARE\SSA Global\Optimize\Dialogs\ColorModel</td><td>+</td><td/><td>AppSettings</td><td/></row>
		<row><td>Registry208</td><td>2</td><td>SOFTWARE\SSA Global\Optimize\Dialogs\CostComparisonDialog</td><td>+</td><td/><td>AppSettings</td><td/></row>
		<row><td>Registry209</td><td>2</td><td>SOFTWARE\SSA Global\Optimize\Dialogs\Main</td><td>+</td><td/><td>AppSettings</td><td/></row>
		<row><td>Registry21</td><td>2</td><td>SOFTWARE\SSA Global\Optimize</td><td>SessionLog</td><td>0</td><td>AppSettings</td><td/></row>
		<row><td>Registry210</td><td>2</td><td>SOFTWARE\SSA Global\Optimize\Dialogs\PickPathProperties</td><td>+</td><td/><td>AppSettings</td><td/></row>
		<row><td>Registry211</td><td>2</td><td>SOFTWARE\SSA Global\Optimize\Dialogs\ProductGroupLayoutStartup</td><td>+</td><td/><td>AppSettings</td><td/></row>
		<row><td>Registry212</td><td>2</td><td>SOFTWARE\SSA Global\Optimize\Dialogs\ProductLayoutStartup</td><td>+</td><td/><td>AppSettings</td><td/></row>
		<row><td>Registry213</td><td>2</td><td>SOFTWARE\SSA Global\Optimize\Dialogs\SectionProperties</td><td>+</td><td/><td>AppSettings</td><td/></row>
		<row><td>Registry22</td><td>2</td><td>SOFTWARE\SSA Global\Optimize</td><td>CopyBufferSize</td><td>50000000</td><td>AppSettings</td><td/></row>
		<row><td>Registry23</td><td>0</td><td>OptimizeReports.Document\DefaultIcon</td><td/><td>&lt;TARGETDIR&gt;\OptimizeReports.exe,1</td><td>AppSettings</td><td>0</td></row>
		<row><td>Registry24</td><td>0</td><td>OptimizeReports.Document\shell</td><td/><td/><td>AppSettings</td><td>1</td></row>
		<row><td>Registry25</td><td>0</td><td>OptimizeReports.Document\shell</td><td/><td/><td>AppSettings</td><td>0</td></row>
		<row><td>Registry26</td><td>0</td><td>OptimizeReports.Document\shell\open</td><td/><td/><td>AppSettings</td><td>1</td></row>
		<row><td>Registry27</td><td>0</td><td>OptimizeReports.Document\shell\open</td><td/><td/><td>AppSettings</td><td>0</td></row>
		<row><td>Registry28</td><td>2</td><td>SOFTWARE\SSA Global\Optimize</td><td>DBBuffer</td><td>500</td><td>AppSettings</td><td/></row>
		<row><td>Registry29</td><td>2</td><td>SOFTWARE\SSA Global\Optimize</td><td>StartEngineAtBoot</td><td>0</td><td>AppSettings</td><td/></row>
		<row><td>Registry3</td><td>0</td><td>.rep</td><td/><td>OptimizeReports.Document</td><td>AppSettings</td><td>0</td></row>
		<row><td>Registry30</td><td>2</td><td>SOFTWARE\SSA Global\Optimize</td><td>DBTimeout</td><td>60</td><td>AppSettings</td><td/></row>
		<row><td>Registry31</td><td>2</td><td>SOFTWARE\SSA Global\Optimize</td><td>InterfaceProductFile</td><td>&lt;TARGETDIR&gt;\Interface\prdasg.dat</td><td>AppSettings</td><td/></row>
		<row><td>Registry32</td><td>0</td><td>OptimizeReports.Document\shell\open\command</td><td/><td/><td>AppSettings</td><td>1</td></row>
		<row><td>Registry33</td><td>0</td><td>OptimizeReports.Document\shell\open\command</td><td/><td>&lt;TARGETDIR&gt;\bin\OptimizeReports.exe "%1"</td><td>AppSettings</td><td>0</td></row>
		<row><td>Registry4</td><td>2</td><td>SOFTWARE\SSA Global\Optimize</td><td>ORACLE_HOME</td><td>&lt;ORACLE_HOME&gt;</td><td>AppSettings</td><td>0</td></row>
		<row><td>Registry5</td><td>2</td><td>SOFTWARE\SSA Global\Optimize</td><td>AutoLoginParameters</td><td/><td>AppSettings</td><td>0</td></row>
		<row><td>Registry6</td><td>0</td><td>.rep\ShellNew</td><td/><td/><td>AppSettings</td><td>1</td></row>
		<row><td>Registry7</td><td>2</td><td>SOFTWARE</td><td/><td/><td>AppSettings</td><td/></row>
		<row><td>Registry8</td><td>2</td><td>SOFTWARE\SSA Global</td><td/><td/><td>AppSettings</td><td/></row>
		<row><td>Registry9</td><td>2</td><td>SOFTWARE\SSA Global\Optimize</td><td/><td/><td>AppSettings</td><td/></row>
	</table>

	<table name="Shortcut">
		<col key="yes" def="s72">Shortcut</col>
		<col def="s72">Directory_</col>
		<col def="L128">Name</col>
		<col def="S72">Component_</col>
		<col def="S255">Target</col>
		<col def="S255">Arguments</col>
		<col def="L255">Description</col>
		<col def="I2">Hotkey</col>
		<col def="S72">Icon_</col>
		<col def="I2">IconIndex</col>
		<col def="I2">ShowCmd</col>
		<col def="S72">WkDir</col>
		<col def="S255">ISComments</col>
		<col def="S255">ISShortcutName</col>
		<col def="I4">ISAttributes</col>
		<row><td>NewShortcut1</td><td>newfolder1</td><td>##ID_STRING3##</td><td/><td>&lt;ACADDIR&gt;\acad.exe</td><td>/s "&lt;TARGETDIR&gt;\menu;&lt;TARGETDIR&gt;\bin;&lt;ACADDIR&gt;\SUPPORT;&lt;ACADDIR&gt;\FONTS"</td><td/><td/><td>NewShortcut1_D1AFEDECE52443E4A3AA3F48A6480B05.exe</td><td>0</td><td>1</td><td>TARGETDIR</td><td/><td>Optimize</td><td>18</td></row>
		<row><td>NewShortcut2</td><td>newfolder1</td><td>##ID_STRING9##</td><td/><td>&lt;TARGETDIR&gt;\Bin\OptConfig.exe</td><td/><td/><td/><td>NewShortcut2_52DE9F9FC8F34C3F88AA66134387F7A4.exe</td><td>0</td><td>1</td><td/><td/><td>Optimize Configuration</td><td>18</td></row>
		<row><td>Optimize1</td><td>DesktopFolder</td><td>##ID_STRING4##</td><td/><td>&lt;ACADDIR&gt;\acad.exe</td><td>/s "&lt;TARGETDIR&gt;\menu;&lt;TARGETDIR&gt;\bin;&lt;ACADDIR&gt;\SUPPORT;&lt;ACADDIR&gt;\FONTS"</td><td/><td>0</td><td>Optimize1_D1AFEDECE52443E4A3AA3F48A6480B05.exe</td><td>0</td><td>1</td><td>TARGETDIR</td><td/><td>Optimize</td><td>18</td></row>
	</table>

	<table name="TextStyle">
		<col key="yes" def="s72">TextStyle</col>
		<col def="s32">FaceName</col>
		<col def="i2">Size</col>
		<col def="I4">Color</col>
		<col def="I2">StyleBits</col>
		<row><td>Arial8</td><td>Arial</td><td>8</td><td/><td/></row>
		<row><td>Arial9</td><td>Arial</td><td>9</td><td/><td/></row>
		<row><td>CourierNew8</td><td>Courier New</td><td>8</td><td/><td/></row>
		<row><td>CourierNew9</td><td>Courier New</td><td>9</td><td/><td/></row>
		<row><td>Helv8</td><td>Helv</td><td>8</td><td>0</td><td>0</td></row>
		<row><td>JPN_TextStyle</td><td>‚l‚r ‚oƒSƒVƒbƒN</td><td>8</td><td>0</td><td>0</td></row>
		<row><td>MSGothic9</td><td>MS Gothic</td><td>9</td><td/><td/></row>
		<row><td>MSSansBold8</td><td>Tahoma</td><td>8</td><td/><td>1</td></row>
		<row><td>MSSansSerif8</td><td>MS Sans Serif</td><td>8</td><td/><td/></row>
		<row><td>MSSansSerif9</td><td>MS Sans Serif</td><td>9</td><td/><td/></row>
		<row><td>Tahoma10</td><td>Tahoma</td><td>10</td><td/><td/></row>
		<row><td>Tahoma8</td><td>Tahoma</td><td>8</td><td/><td/></row>
		<row><td>Tahoma9</td><td>Tahoma</td><td>9</td><td/><td/></row>
		<row><td>TahomaBold10</td><td>Tahoma</td><td>10</td><td/><td>1</td></row>
		<row><td>TahomaBold8</td><td>Tahoma</td><td>8</td><td/><td>1</td></row>
		<row><td>Times8</td><td>Times New Roman</td><td>8</td><td/><td/></row>
		<row><td>Times9</td><td>Times New Roman</td><td>9</td><td/><td/></row>
		<row><td>TimesItalic12</td><td>Times New Roman</td><td>12</td><td/><td>2</td></row>
	</table>

	<table name="_Validation">
		<col key="yes" def="s32">Table</col>
		<col key="yes" def="s32">Column</col>
		<col def="s4">Nullable</col>
		<col def="I4">MinValue</col>
		<col def="I4">MaxValue</col>
		<col def="S255">KeyTable</col>
		<col def="I2">KeyColumn</col>
		<col def="S32">Category</col>
		<col def="S255">Set</col>
		<col def="S255">Description</col>
		<row><td>Binary</td><td>Data</td><td>Y</td><td/><td/><td/><td/><td>Binary</td><td/><td>Binary stream. The binary icon data in PE (.DLL or .EXE) or icon (.ICO) format.</td></row>
		<row><td>Binary</td><td>ISBuildSourcePath</td><td>Y</td><td/><td/><td/><td/><td>Text</td><td/><td>Full path to the ICO or EXE file.</td></row>
		<row><td>Binary</td><td>Name</td><td>N</td><td/><td/><td/><td/><td>Identifier</td><td/><td>Unique key identifying the binary data.</td></row>
		<row><td>CheckBox</td><td>Property</td><td>N</td><td/><td/><td/><td/><td>Identifier</td><td/><td>A named property to be tied to the item.</td></row>
		<row><td>CheckBox</td><td>Value</td><td>Y</td><td/><td/><td/><td/><td>Formatted</td><td/><td>The value string associated with the item.</td></row>
		<row><td>ComboBox</td><td>Order</td><td>N</td><td>1</td><td>32767</td><td/><td/><td/><td/><td>A positive integer used to determine the ordering of the items within one list.	The integers do not have to be consecutive.</td></row>
		<row><td>ComboBox</td><td>Property</td><td>N</td><td/><td/><td/><td/><td>Identifier</td><td/><td>A named property to be tied to this item. All the items tied to the same property become part of the same combobox.</td></row>
		<row><td>ComboBox</td><td>Text</td><td>Y</td><td/><td/><td/><td/><td>Formatted</td><td/><td>The visible text to be assigned to the item. Optional. If this entry or the entire column is missing, the text is the same as the value.</td></row>
		<row><td>ComboBox</td><td>Value</td><td>N</td><td/><td/><td/><td/><td>Formatted</td><td/><td>The value string associated with this item. Selecting the line will set the associated property to this value.</td></row>
		<row><td>Component</td><td>Attributes</td><td>N</td><td/><td/><td/><td/><td/><td/><td>Remote execution option, one of irsEnum</td></row>
		<row><td>Component</td><td>Component</td><td>N</td><td/><td/><td/><td/><td>Text</td><td/><td>Primary key used to identify a particular component record.</td></row>
		<row><td>Component</td><td>ComponentId</td><td>Y</td><td/><td/><td/><td/><td>Guid</td><td/><td>A string GUID unique to this component, version, and language.</td></row>
		<row><td>Component</td><td>Condition</td><td>Y</td><td/><td/><td/><td/><td>Condition</td><td/><td>A conditional statement that will disable this component if the specified condition evaluates to the 'True' state. If a component is disabled, it will not be installed, regardless of the 'Action' state associated with the component.</td></row>
		<row><td>Component</td><td>Directory_</td><td>N</td><td/><td/><td>Directory</td><td>1</td><td>Identifier</td><td/><td>Required key of a Directory table record. This is actually a property name whose value contains the actual path, set either by the AppSearch action or with the default setting obtained from the Directory table.</td></row>
		<row><td>Component</td><td>ISAttributes</td><td>Y</td><td/><td/><td/><td/><td/><td/><td>This is used to store Installshield custom properties of a component.</td></row>
		<row><td>Component</td><td>ISComments</td><td>Y</td><td/><td/><td/><td/><td>Text</td><td/><td>User Comments.</td></row>
		<row><td>Component</td><td>ISDotNetInstallerArgsCommit</td><td>Y</td><td/><td/><td/><td/><td>Text</td><td/><td>Arguments passed to the key file of the component if if implements the .NET Installer class</td></row>
		<row><td>Component</td><td>ISDotNetInstallerArgsInstall</td><td>Y</td><td/><td/><td/><td/><td>Text</td><td/><td>Arguments passed to the key file of the component if if implements the .NET Installer class</td></row>
		<row><td>Component</td><td>ISDotNetInstallerArgsRollback</td><td>Y</td><td/><td/><td/><td/><td>Text</td><td/><td>Arguments passed to the key file of the component if if implements the .NET Installer class</td></row>
		<row><td>Component</td><td>ISDotNetInstallerArgsUninstall</td><td>Y</td><td/><td/><td/><td/><td>Text</td><td/><td>Arguments passed to the key file of the component if if implements the .NET Installer class</td></row>
		<row><td>Component</td><td>ISRegFileToMergeAtBuild</td><td>Y</td><td/><td/><td/><td/><td>Text</td><td/><td>Path and File name of a .REG file to merge into the component at build time.</td></row>
		<row><td>Component</td><td>ISScanAtBuildFile</td><td>Y</td><td/><td/><td/><td/><td>Text</td><td/><td>File used by the Dot Net scanner to populate dependant assemblies' File_Application field.</td></row>
		<row><td>Component</td><td>KeyPath</td><td>Y</td><td/><td/><td>File;Registry</td><td>1</td><td>Identifier</td><td/><td>Either the primary key into the File table, Registry table, or ODBCDataSource table. This extract path is stored when the component is installed, and is used to detect the presence of the component and to return the path to it.</td></row>
		<row><td>Control</td><td>Attributes</td><td>Y</td><td>0</td><td>2147483647</td><td/><td/><td/><td/><td>A 32-bit word that specifies the attribute flags to be applied to this control.</td></row>
		<row><td>Control</td><td>Binary_</td><td>Y</td><td/><td/><td>Binary</td><td>1</td><td>Identifier</td><td/><td>External key to the Binary table.</td></row>
		<row><td>Control</td><td>Control</td><td>N</td><td/><td/><td/><td/><td>Identifier</td><td/><td>Name of the control. This name must be unique within a dialog, but can repeat on different dialogs. </td></row>
		<row><td>Control</td><td>Control_Next</td><td>Y</td><td/><td/><td>Control</td><td>2</td><td>Identifier</td><td/><td>The name of an other control on the same dialog. This link defines the tab order of the controls. The links have to form one or more cycles!</td></row>
		<row><td>Control</td><td>Dialog_</td><td>N</td><td/><td/><td>Dialog</td><td>1</td><td>Identifier</td><td/><td>External key to the Dialog table, name of the dialog.</td></row>
		<row><td>Control</td><td>Height</td><td>N</td><td>0</td><td>32767</td><td/><td/><td/><td/><td>Height of the bounding rectangle of the control.</td></row>
		<row><td>Control</td><td>Help</td><td>Y</td><td/><td/><td/><td/><td>Text</td><td/><td>The help strings used with the button. The text is optional. </td></row>
		<row><td>Control</td><td>ISBuildSourcePath</td><td>Y</td><td/><td/><td/><td/><td>Text</td><td/><td>Full path to .rtf file for scrollable text control</td></row>
		<row><td>Control</td><td>ISControlId</td><td>Y</td><td/><td/><td/><td/><td/><td/><td>A number used to represent the control ID of the Control, Used in Dialog export</td></row>
		<row><td>Control</td><td>ISWindowStyle</td><td>Y</td><td>0</td><td>2147483647</td><td/><td/><td/><td/><td>A 32-bit word that specifies non-MSI window styles to be applied to this control.</td></row>
		<row><td>Control</td><td>Property</td><td>Y</td><td/><td/><td/><td/><td>Identifier</td><td/><td>The name of a defined property to be linked to this control. </td></row>
		<row><td>Control</td><td>Text</td><td>Y</td><td/><td/><td/><td/><td>Formatted</td><td/><td>A string used to set the initial text contained within a control (if appropriate).</td></row>
		<row><td>Control</td><td>Type</td><td>N</td><td/><td/><td/><td/><td>Identifier</td><td/><td>The type of the control.</td></row>
		<row><td>Control</td><td>Width</td><td>N</td><td>0</td><td>32767</td><td/><td/><td/><td/><td>Width of the bounding rectangle of the control.</td></row>
		<row><td>Control</td><td>X</td><td>N</td><td>0</td><td>32767</td><td/><td/><td/><td/><td>Horizontal coordinate of the upper left corner of the bounding rectangle of the control.</td></row>
		<row><td>Control</td><td>Y</td><td>N</td><td>0</td><td>32767</td><td/><td/><td/><td/><td>Vertical coordinate of the upper left corner of the bounding rectangle of the control.</td></row>
		<row><td>Dialog</td><td>Attributes</td><td>Y</td><td>0</td><td>2147483647</td><td/><td/><td/><td/><td>A 32-bit word that specifies the attribute flags to be applied to this dialog.</td></row>
		<row><td>Dialog</td><td>Control_Cancel</td><td>Y</td><td/><td/><td>Control</td><td>2</td><td>Identifier</td><td/><td>Defines the cancel control. Hitting escape or clicking on the close icon on the dialog is equivalent to pushing this button.</td></row>
		<row><td>Dialog</td><td>Control_Default</td><td>Y</td><td/><td/><td>Control</td><td>2</td><td>Identifier</td><td/><td>Defines the default control. Hitting return is equivalent to pushing this button.</td></row>
		<row><td>Dialog</td><td>Control_First</td><td>N</td><td/><td/><td>Control</td><td>2</td><td>Identifier</td><td/><td>Defines the control that has the focus when the dialog is created.</td></row>
		<row><td>Dialog</td><td>Dialog</td><td>N</td><td/><td/><td/><td/><td>Identifier</td><td/><td>Name of the dialog.</td></row>
		<row><td>Dialog</td><td>HCentering</td><td>N</td><td>0</td><td>100</td><td/><td/><td/><td/><td>Horizontal position of the dialog on a 0-100 scale. 0 means left end, 100 means right end of the screen, 50 center.</td></row>
		<row><td>Dialog</td><td>Height</td><td>N</td><td>0</td><td>32767</td><td/><td/><td/><td/><td>Height of the bounding rectangle of the dialog.</td></row>
		<row><td>Dialog</td><td>ISComments</td><td>Y</td><td/><td/><td/><td/><td>Text</td><td/><td>Author’s comments for this dialog.</td></row>
		<row><td>Dialog</td><td>ISResourceId</td><td>Y</td><td/><td/><td/><td/><td/><td/><td>A Number the Specifies the Dialog ID to be used in Dialog Export</td></row>
		<row><td>Dialog</td><td>ISWindowStyle</td><td>Y</td><td/><td/><td/><td/><td/><td/><td>A 32-bit word that specifies non-MSI window styles to be applied to this control. This is only used in Script Based Setups.</td></row>
		<row><td>Dialog</td><td>TextStyle_</td><td>Y</td><td/><td/><td/><td/><td>Identifier</td><td/><td>Foreign Key into TextStyle table, only used in Script Based Projects.</td></row>
		<row><td>Dialog</td><td>Title</td><td>Y</td><td/><td/><td/><td/><td>Formatted</td><td/><td>A text string specifying the title to be displayed in the title bar of the dialog's window.</td></row>
		<row><td>Dialog</td><td>VCentering</td><td>N</td><td>0</td><td>100</td><td/><td/><td/><td/><td>Vertical position of the dialog on a 0-100 scale. 0 means top end, 100 means bottom end of the screen, 50 center.</td></row>
		<row><td>Dialog</td><td>Width</td><td>N</td><td>0</td><td>32767</td><td/><td/><td/><td/><td>Width of the bounding rectangle of the dialog.</td></row>
		<row><td>Directory</td><td>DefaultDir</td><td>N</td><td/><td/><td/><td/><td>Text</td><td/><td>The default sub-path under parent's path.</td></row>
		<row><td>Directory</td><td>Directory</td><td>N</td><td/><td/><td/><td/><td>Identifier</td><td/><td>Unique identifier for directory entry, primary key. If a property by this name is defined, it contains the full path to the directory.</td></row>
		<row><td>Directory</td><td>Directory_Parent</td><td>Y</td><td/><td/><td>Directory</td><td>1</td><td>Identifier</td><td/><td>Reference to the entry in this table specifying the default parent directory. A record parented to itself or with a Null parent represents a root of the install tree.</td></row>
		<row><td>Directory</td><td>ISAttributes</td><td>Y</td><td/><td/><td/><td/><td/><td>0;1;2;3;4;5;6;7</td><td>This is used to store Installshield custom properties of a directory.  Currently the only one is Shortcut.</td></row>
		<row><td>Directory</td><td>ISDescription</td><td>Y</td><td/><td/><td/><td/><td>Text</td><td/><td>Description of folder</td></row>
		<row><td>Directory</td><td>ISFolderName</td><td>Y</td><td/><td/><td/><td/><td>Text</td><td/><td>This is used in Pro projects because the pro identifier used in the tree wasn't necessarily unique.</td></row>
		<row><td>Feature</td><td>Attributes</td><td>N</td><td/><td/><td/><td/><td/><td>0;1;2;4;5;6;8;9;10;16;17;18;20;21;22;24;25;26;32;33;34;36;37;38;48;49;50;52;53;54</td><td>Feature attributes</td></row>
		<row><td>Feature</td><td>Description</td><td>Y</td><td/><td/><td/><td/><td>Text</td><td/><td>Longer descriptive text describing a visible feature item.</td></row>
		<row><td>Feature</td><td>Directory_</td><td>Y</td><td/><td/><td>Directory</td><td>1</td><td>UpperCase</td><td/><td>The name of the Directory that can be configured by the UI. A non-null value will enable the browse button.</td></row>
		<row><td>Feature</td><td>Display</td><td>Y</td><td>0</td><td>32767</td><td/><td/><td/><td/><td>Numeric sort order, used to force a specific display ordering.</td></row>
		<row><td>Feature</td><td>Feature</td><td>N</td><td/><td/><td/><td/><td>Guid</td><td/><td>Primary key used to identify a particular feature record.</td></row>
		<row><td>Feature</td><td>Feature_Parent</td><td>Y</td><td/><td/><td>Feature</td><td>1</td><td>Guid</td><td/><td>Optional key of a parent record in the same table. If the parent is not selected, then the record will not be installed. Null indicates a root item.</td></row>
		<row><td>Feature</td><td>ISComments</td><td>Y</td><td/><td/><td/><td/><td/><td/><td>Comments</td></row>
		<row><td>Feature</td><td>ISFeatureCabName</td><td>Y</td><td/><td/><td/><td/><td/><td/><td>Name of CAB used when compressing CABs by Feature. Used to override build generated name for CAB file.</td></row>
		<row><td>Feature</td><td>ISProFeatureName</td><td>Y</td><td/><td/><td/><td/><td>Text</td><td/><td>The name of the feature used by pro projects.  This doesn't have to be unique.</td></row>
		<row><td>Feature</td><td>ISReleaseFlags</td><td>Y</td><td/><td/><td/><td/><td/><td/><td>Release Flags that specify whether this  feature will be built in a particular release.</td></row>
		<row><td>Feature</td><td>Level</td><td>N</td><td>0</td><td>32767</td><td/><td/><td/><td/><td>The install level at which record will be initially selected. An install level of 0 will disable an item and prevent its display.</td></row>
		<row><td>Feature</td><td>Title</td><td>Y</td><td/><td/><td/><td/><td>Text</td><td/><td>Short text identifying a visible feature item.</td></row>
		<row><td>FeatureComponents</td><td>Component_</td><td>N</td><td/><td/><td>Component</td><td>1</td><td>Text</td><td/><td>Foreign key into Component table.</td></row>
		<row><td>FeatureComponents</td><td>Feature_</td><td>N</td><td/><td/><td>Feature</td><td>1</td><td>Text</td><td/><td>Foreign key into Feature table.</td></row>
		<row><td>File</td><td>Attributes</td><td>Y</td><td>0</td><td>32767</td><td/><td/><td/><td/><td>Integer containing bit flags representing file attributes (with the decimal value of each bit position in parentheses)</td></row>
		<row><td>File</td><td>Component_</td><td>N</td><td/><td/><td>Component</td><td>1</td><td>Text</td><td/><td>Foreign key referencing Component that controls the file.</td></row>
		<row><td>File</td><td>File</td><td>N</td><td/><td/><td/><td/><td>Identifier</td><td/><td>Primary key, non-localized token, must match identifier in cabinet.  For uncompressed files, this field is ignored.</td></row>
		<row><td>File</td><td>FileName</td><td>N</td><td/><td/><td/><td/><td>Text</td><td/><td>File name used for installation.  This may contain a "short name|long name" pair.  It may be just a long name, hence it cannot be of the Filename data type.</td></row>
		<row><td>File</td><td>FileSize</td><td>N</td><td>0</td><td>2147483647</td><td/><td/><td/><td/><td>Size of file in bytes (long integer).</td></row>
		<row><td>File</td><td>ISAttributes</td><td>Y</td><td>0</td><td>2147483647</td><td/><td/><td/><td/><td>This field contains the following attributes: UseSystemSettings(0x1)</td></row>
		<row><td>File</td><td>ISBuildSourcePath</td><td>Y</td><td/><td/><td/><td/><td>Text</td><td/><td>Full path, the category is of Text instead of Path because of potential use of path variables.</td></row>
		<row><td>File</td><td>ISComponentSubFolder_</td><td>Y</td><td/><td/><td>ISComponentSubFolder</td><td>1</td><td>Identifier</td><td/><td>Foreign key referencing component subfolder containing this file.  Only for Pro.</td></row>
		<row><td>File</td><td>Language</td><td>Y</td><td/><td/><td/><td/><td>Language</td><td/><td>List of decimal language Ids, comma-separated if more than one.</td></row>
		<row><td>File</td><td>Sequence</td><td>N</td><td>1</td><td>32767</td><td/><td/><td/><td/><td>Sequence with respect to the media images; order must track cabinet order.</td></row>
		<row><td>File</td><td>Version</td><td>Y</td><td/><td/><td>File</td><td>1</td><td>Version</td><td/><td>Version string for versioned files;  Blank for unversioned files.</td></row>
		<row><td>Font</td><td>File_</td><td>N</td><td/><td/><td>File</td><td>1</td><td>Identifier</td><td/><td>Primary key, foreign key into File table referencing font file.</td></row>
		<row><td>Font</td><td>FontTitle</td><td>Y</td><td/><td/><td/><td/><td>Text</td><td/><td>Font name.</td></row>
		<row><td>ISAlias</td><td>Alias</td><td>Y</td><td/><td/><td/><td/><td/><td/><td/></row>
		<row><td>ISAlias</td><td>Identifier</td><td>Y</td><td/><td/><td/><td/><td/><td/><td/></row>
		<row><td>ISAlias</td><td>Table</td><td>Y</td><td/><td/><td/><td/><td/><td/><td/></row>
		<row><td>ISAssistantTag</td><td>Data</td><td>Y</td><td/><td/><td/><td/><td/><td/><td/></row>
		<row><td>ISAssistantTag</td><td>Tag</td><td>N</td><td/><td/><td/><td/><td/><td/><td/></row>
		<row><td>ISComponentExtended</td><td>Component_</td><td>N</td><td/><td/><td>Component</td><td>1</td><td>Text</td><td/><td>Primary key used to identify a particular component record.</td></row>
		<row><td>ISComponentExtended</td><td>FTPLocation</td><td>Y</td><td/><td/><td/><td/><td>Text</td><td/><td>FTP Location</td></row>
		<row><td>ISComponentExtended</td><td>FilterProperty</td><td>N</td><td/><td/><td/><td/><td>Identifier</td><td/><td>Property to set if you want to filter a component</td></row>
		<row><td>ISComponentExtended</td><td>HTTPLocation</td><td>Y</td><td/><td/><td/><td/><td>Text</td><td/><td>HTTP Location</td></row>
		<row><td>ISComponentExtended</td><td>Language</td><td>Y</td><td/><td/><td/><td/><td>Text</td><td/><td>Language</td></row>
		<row><td>ISComponentExtended</td><td>Miscellaneous</td><td>Y</td><td/><td/><td/><td/><td>Text</td><td/><td>Miscellaneous</td></row>
		<row><td>ISComponentExtended</td><td>OS</td><td>Y</td><td/><td/><td/><td/><td/><td/><td>bitwise addition of OSs</td></row>
		<row><td>ISComponentExtended</td><td>Platforms</td><td>Y</td><td/><td/><td/><td/><td/><td/><td>bitwise addition of Platforms.</td></row>
		<row><td>ISComponentSubFolder</td><td>Component_</td><td>N</td><td/><td/><td>Component</td><td>1</td><td>Text</td><td/><td>Foreign key into the Component table denoting the component containing this subfolder.</td></row>
		<row><td>ISComponentSubFolder</td><td>ISSubFolder</td><td>N</td><td/><td/><td/><td/><td>Identifier</td><td/><td>Primary key, non-localized token.</td></row>
		<row><td>ISComponentSubFolder</td><td>ISSubFolder_Parent</td><td>Y</td><td/><td/><td>ISComponentSubFolder</td><td>1</td><td>Identifier</td><td/><td>Primary key into this table.  Not required.</td></row>
		<row><td>ISComponentSubFolder</td><td>Path</td><td>N</td><td/><td/><td/><td/><td>Text</td><td/><td>The subfolder path.</td></row>
		<row><td>ISDRMFile</td><td>File_</td><td>Y</td><td/><td/><td>File</td><td>1</td><td>Identifier</td><td/><td>Foreign key into File table.  A null value will cause a build warning.</td></row>
		<row><td>ISDRMFile</td><td>ISDRMFile</td><td>N</td><td/><td/><td/><td/><td>Identifier</td><td/><td>Unique identifier for this item.</td></row>
		<row><td>ISDRMFile</td><td>ISDRMLicense_</td><td>Y</td><td/><td/><td>ISDRMLicense</td><td>1</td><td>Identifier</td><td/><td>Foreign key referencing License that packages this file.</td></row>
		<row><td>ISDRMFile</td><td>Shell</td><td>N</td><td/><td/><td/><td/><td>Text</td><td/><td>Text indicating the activation shell used at runtime.</td></row>
		<row><td>ISDRMFileAttribute</td><td>ISDRMFile_</td><td>N</td><td/><td/><td>ISDRMFile</td><td>1</td><td>Identifier</td><td/><td>Primary foreign key into ISDRMFile table.</td></row>
		<row><td>ISDRMFileAttribute</td><td>Property</td><td>N</td><td/><td/><td/><td/><td>Text</td><td/><td>The name of the attribute</td></row>
		<row><td>ISDRMFileAttribute</td><td>Value</td><td>Y</td><td/><td/><td/><td/><td>Text</td><td/><td>The value of the attribute</td></row>
		<row><td>ISDRMLicense</td><td>Attributes</td><td>Y</td><td/><td/><td/><td/><td>Number</td><td/><td>Bitwise field used to specify binary attributes of this license.</td></row>
		<row><td>ISDRMLicense</td><td>Description</td><td>Y</td><td/><td/><td/><td/><td>Text</td><td/><td>An internal description of this license.</td></row>
		<row><td>ISDRMLicense</td><td>ISDRMLicense</td><td>N</td><td/><td/><td/><td/><td>Identifier</td><td/><td>Unique key identifying the license record.</td></row>
		<row><td>ISDRMLicense</td><td>LicenseNumber</td><td>Y</td><td/><td/><td/><td/><td>Text</td><td/><td>The license number.</td></row>
		<row><td>ISDRMLicense</td><td>ProjectVersion</td><td>Y</td><td/><td/><td/><td/><td>Text</td><td/><td>The version of the project that this license is tied to.</td></row>
		<row><td>ISDRMLicense</td><td>RequestCode</td><td>Y</td><td/><td/><td/><td/><td>Text</td><td/><td>The request code.</td></row>
		<row><td>ISDRMLicense</td><td>ResponseCode</td><td>Y</td><td/><td/><td/><td/><td>Text</td><td/><td>The response code.</td></row>
		<row><td>ISDependency</td><td>Exclude</td><td>Y</td><td/><td/><td/><td/><td/><td/><td/></row>
		<row><td>ISDependency</td><td>ISDependency</td><td>Y</td><td/><td/><td/><td/><td/><td/><td/></row>
		<row><td>ISDisk1File</td><td>Disk</td><td>Y</td><td/><td/><td/><td/><td/><td>-1;0;1</td><td>Used to differentiate between disk1(1), last disk(-1), and other(0).</td></row>
		<row><td>ISDisk1File</td><td>ISBuildSourcePath</td><td>N</td><td/><td/><td/><td/><td>Text</td><td/><td>Full path of file to be copied to Disk1 folder</td></row>
		<row><td>ISDisk1File</td><td>ISDisk1File</td><td>N</td><td/><td/><td/><td/><td>Identifier</td><td/><td>Primary key for ISDisk1File table</td></row>
		<row><td>ISDynamicFile</td><td>Component_</td><td>N</td><td/><td/><td>Component</td><td>1</td><td>Text</td><td/><td>Foreign key referencing Component that controls the file.</td></row>
		<row><td>ISDynamicFile</td><td>ExcludeFiles</td><td>Y</td><td/><td/><td/><td/><td>Text</td><td/><td>Wildcards for excluded files.</td></row>
		<row><td>ISDynamicFile</td><td>ISAttributes</td><td>Y</td><td/><td/><td/><td/><td/><td>0;1;2;3</td><td>This is used to store Installshield custom properties of a dynamic filet.  Currently the only one is SelfRegister.</td></row>
		<row><td>ISDynamicFile</td><td>IncludeFiles</td><td>Y</td><td/><td/><td/><td/><td>Text</td><td/><td>Wildcards for included files.</td></row>
		<row><td>ISDynamicFile</td><td>IncludeFlags</td><td>Y</td><td/><td/><td/><td/><td/><td/><td>Include flags.</td></row>
		<row><td>ISDynamicFile</td><td>SourceFolder</td><td>N</td><td/><td/><td/><td/><td>Text</td><td/><td>Full path, the category is of Text instead of Path because of potential use of path variables.</td></row>
		<row><td>ISFeatureExtended</td><td>CDRomFolder</td><td>Y</td><td/><td/><td/><td/><td>Text</td><td/><td>Files in this feature will be placed in this subfolder if your media is the CD-ROM type.</td></row>
		<row><td>ISFeatureExtended</td><td>FTPLocation</td><td>Y</td><td/><td/><td/><td/><td>Text</td><td/><td>FTP Location</td></row>
		<row><td>ISFeatureExtended</td><td>Feature_</td><td>N</td><td/><td/><td>Feature</td><td>1</td><td>Text</td><td/><td>Foreign key into Feature table.</td></row>
		<row><td>ISFeatureExtended</td><td>HTTPLocation</td><td>Y</td><td/><td/><td/><td/><td>Text</td><td/><td>HTTP Location</td></row>
		<row><td>ISFeatureExtended</td><td>ISAttributes</td><td>Y</td><td/><td/><td/><td/><td/><td/><td>Non-msi feature attributes.  Mostly used by pro projects.</td></row>
		<row><td>ISFeatureExtended</td><td>Installed</td><td>Y</td><td/><td/><td/><td/><td>Text</td><td/><td>Name of the Features Installed Event</td></row>
		<row><td>ISFeatureExtended</td><td>Installing</td><td>Y</td><td/><td/><td/><td/><td>Text</td><td/><td>Name of the Features Installing Event</td></row>
		<row><td>ISFeatureExtended</td><td>Miscellaneous</td><td>Y</td><td/><td/><td/><td/><td>Text</td><td/><td>Miscellaneous</td></row>
		<row><td>ISFeatureExtended</td><td>Moniker</td><td>Y</td><td/><td/><td/><td/><td>Text</td><td/><td>Used by pro to identify objects.</td></row>
		<row><td>ISFeatureExtended</td><td>Password</td><td>Y</td><td/><td/><td/><td/><td>Text</td><td/><td>The password for this feature.</td></row>
		<row><td>ISFeatureExtended</td><td>StatusText</td><td>Y</td><td/><td/><td/><td/><td>Text</td><td/><td>Text displayed during file transfer in pro projects.</td></row>
		<row><td>ISFeatureExtended</td><td>Uninstalled</td><td>Y</td><td/><td/><td/><td/><td>Text</td><td/><td>Name of the Features UnInstalled Event</td></row>
		<row><td>ISFeatureExtended</td><td>Uninstalling</td><td>Y</td><td/><td/><td/><td/><td>Text</td><td/><td>Name of the Features UnInstalling Event</td></row>
		<row><td>ISFeatureMergeModuleExcludes</td><td>Feature_</td><td>N</td><td/><td/><td/><td/><td>Text</td><td/><td>Foreign key used to identify a particular feature record.</td></row>
		<row><td>ISFeatureMergeModuleExcludes</td><td>Language</td><td>N</td><td/><td/><td/><td/><td/><td/><td>Foreign key into ISMergeModule table.</td></row>
		<row><td>ISFeatureMergeModuleExcludes</td><td>ModuleID</td><td>N</td><td/><td/><td/><td/><td>Identifier</td><td/><td>Foreign key into ISMergeModule table.
</td></row>
		<row><td>ISFeatureMergeModules</td><td>Feature_</td><td>N</td><td/><td/><td>Feature</td><td>1</td><td>Text</td><td/><td>Foreign key used to identify a particular feature record.</td></row>
		<row><td>ISFeatureMergeModules</td><td>ISMergeModule_</td><td>N</td><td/><td/><td>ISMergeModule</td><td>1</td><td>Text</td><td/><td>Foreign key into ISMergeModule table.
</td></row>
		<row><td>ISFeatureMergeModules</td><td>Language_</td><td>N</td><td/><td/><td>ISMergeModule</td><td>2</td><td/><td/><td>Foreign key into ISMergeModule table.</td></row>
		<row><td>ISIISAppPool</td><td>AppPool</td><td>N</td><td/><td/><td/><td/><td/><td/><td/></row>
		<row><td>ISIISAppPool</td><td>Attributes</td><td>Y</td><td/><td/><td/><td/><td/><td/><td/></row>
		<row><td>ISIISAppPool</td><td>CPUMon</td><td>Y</td><td/><td/><td/><td/><td/><td/><td/></row>
		<row><td>ISIISAppPool</td><td>Component_</td><td>N</td><td/><td/><td>Component</td><td>1</td><td>Text</td><td/><td>Foreign key into Component table</td></row>
		<row><td>ISIISAppPool</td><td>IdleTimeout</td><td>Y</td><td/><td/><td/><td/><td/><td/><td/></row>
		<row><td>ISIISAppPool</td><td>MaxProc</td><td>Y</td><td/><td/><td/><td/><td/><td/><td/></row>
		<row><td>ISIISAppPool</td><td>Name</td><td>N</td><td/><td/><td/><td/><td>Text</td><td/><td>Localizable Display Name</td></row>
		<row><td>ISIISAppPool</td><td>QueueLimit</td><td>Y</td><td/><td/><td/><td/><td/><td/><td/></row>
		<row><td>ISIISAppPool</td><td>RecycleMinutes</td><td>Y</td><td/><td/><td/><td/><td/><td/><td/></row>
		<row><td>ISIISAppPool</td><td>RecycleRequests</td><td>Y</td><td/><td/><td/><td/><td/><td/><td/></row>
		<row><td>ISIISAppPool</td><td>RecycleTimes</td><td>Y</td><td/><td/><td/><td/><td/><td/><td/></row>
		<row><td>ISIISAppPool</td><td>User</td><td>Y</td><td/><td/><td/><td/><td/><td/><td/></row>
		<row><td>ISIISAppPool</td><td>UserPassword</td><td>Y</td><td/><td/><td/><td/><td/><td/><td/></row>
		<row><td>ISIISCommon</td><td>AnonyPasswrd</td><td>Y</td><td/><td/><td/><td/><td>Text</td><td/><td>Password for anonymous access</td></row>
		<row><td>ISIISCommon</td><td>AnonyUserName</td><td>Y</td><td/><td/><td/><td/><td>Text</td><td/><td>User name for anonymous access</td></row>
		<row><td>ISIISCommon</td><td>AppName</td><td>Y</td><td/><td/><td/><td/><td>Text</td><td/><td>AppName of this VRoot</td></row>
		<row><td>ISIISCommon</td><td>AppPool_</td><td>Y</td><td/><td/><td/><td/><td>Text</td><td/><td>Localizable App Pool  Name</td></row>
		<row><td>ISIISCommon</td><td>Attributes</td><td>N</td><td/><td/><td/><td/><td>Number</td><td/><td>Attributes for this IIS node</td></row>
		<row><td>ISIISCommon</td><td>CustomErrors</td><td>Y</td><td/><td/><td/><td/><td>Text</td><td/><td>Delimited list of custom errors</td></row>
		<row><td>ISIISCommon</td><td>DefDoc</td><td>Y</td><td/><td/><td/><td/><td>Text</td><td/><td>Localizable Defeault Doc</td></row>
		<row><td>ISIISCommon</td><td>DisplayName</td><td>Y</td><td/><td/><td/><td/><td>Text</td><td/><td>Localizable Virtual Root Name</td></row>
		<row><td>ISIISCommon</td><td>ISIISCommon</td><td>N</td><td/><td/><td/><td/><td>Identifier</td><td/><td>Primary key used to identify a particular  record.</td></row>
		<row><td>ISIISCommon</td><td>ISIISCommon_Parent</td><td>Y</td><td/><td/><td>ISIISCommon</td><td>1</td><td>Identifier</td><td/><td>This record's parent record.</td></row>
		<row><td>ISIISCommon</td><td>RootDir</td><td>N</td><td/><td/><td/><td/><td>Text</td><td/><td>Root directory for this IIS Node</td></row>
		<row><td>ISIISCommon</td><td>SSLCert</td><td>Y</td><td/><td/><td>Binary</td><td>1</td><td>Identifier</td><td/><td>Foreign key into the binary table.</td></row>
		<row><td>ISIISCommon</td><td>ScriptTimeout</td><td>Y</td><td/><td/><td/><td/><td>Number</td><td/><td>ASP Script Timeout</td></row>
		<row><td>ISIISCommon</td><td>SessionTimeout</td><td>Y</td><td/><td/><td/><td/><td>Number</td><td/><td>Session Timeout</td></row>
		<row><td>ISIISMetaData</td><td>ISIISCommon_</td><td>N</td><td/><td/><td>ISIISCommon</td><td>1</td><td>Identifier</td><td/><td>Foreign key into ISIISCommon table</td></row>
		<row><td>ISIISMetaData</td><td>MetaDataAttributes</td><td>N</td><td/><td/><td/><td/><td/><td/><td>This is the dwMDAttributes item in the METADATA_RECORD structure</td></row>
		<row><td>ISIISMetaData</td><td>MetaDataProp</td><td>Y</td><td/><td/><td/><td/><td/><td/><td>This is the dwMDIdentifier item in the METADATA_RECORD structure</td></row>
		<row><td>ISIISMetaData</td><td>MetaDataType</td><td>Y</td><td/><td/><td/><td/><td/><td/><td>This is the dwMDDataType item in the METADATA_RECORD structure</td></row>
		<row><td>ISIISMetaData</td><td>MetaDataUserType</td><td>N</td><td/><td/><td/><td/><td/><td/><td>This is the dwMDUserType item in the METADATA_RECORD structure</td></row>
		<row><td>ISIISMetaData</td><td>MetaDataValue</td><td>N</td><td/><td/><td/><td/><td>Text</td><td/><td>This is the pbMDData  item in the METADATA_RECORD structure</td></row>
		<row><td>ISIISMetaData</td><td>Order</td><td>N</td><td/><td/><td/><td/><td/><td/><td>Use this column to order the meta data properties.</td></row>
		<row><td>ISIISWebServiceExtension</td><td>Attributes</td><td>N</td><td/><td/><td/><td/><td/><td/><td/></row>
		<row><td>ISIISWebServiceExtension</td><td>Component_</td><td>N</td><td/><td/><td>Component</td><td>1</td><td>Text</td><td/><td>Foreign key into Component table</td></row>
		<row><td>ISIISWebServiceExtension</td><td>Description</td><td>N</td><td/><td/><td/><td/><td>Text</td><td/><td>Localizable Description</td></row>
		<row><td>ISIISWebServiceExtension</td><td>File</td><td>N</td><td/><td/><td/><td/><td/><td/><td/></row>
		<row><td>ISIISWebServiceExtension</td><td>Group</td><td>N</td><td/><td/><td/><td/><td/><td/><td/></row>
		<row><td>ISIISWebServiceExtension</td><td>WebServiceExtension</td><td>N</td><td/><td/><td/><td/><td/><td/><td/></row>
		<row><td>ISLanguage</td><td>ISLanguage</td><td>N</td><td/><td/><td/><td/><td>Language</td><td/><td>This is the language ID.</td></row>
		<row><td>ISLanguage</td><td>Included</td><td>Y</td><td/><td/><td/><td/><td/><td>0;1</td><td>Specify whether this language should be included.</td></row>
		<row><td>ISLinkerLibrary</td><td>ISLinkerLibrary</td><td>N</td><td/><td/><td/><td/><td>Identifier</td><td/><td>Unique identifier for the link library.
</td></row>
		<row><td>ISLinkerLibrary</td><td>Library</td><td>N</td><td/><td/><td/><td/><td>Text</td><td/><td>Full path of the object library (.obl file).
</td></row>
		<row><td>ISLinkerLibrary</td><td>Order</td><td>N</td><td/><td/><td/><td/><td/><td/><td>Order of the Library</td></row>
		<row><td>ISLocalControl</td><td>Attributes</td><td>Y</td><td/><td/><td/><td/><td/><td/><td>A 32-bit word that specifies the attribute flags to be applied to this control.</td></row>
		<row><td>ISLocalControl</td><td>Binary_</td><td>Y</td><td/><td/><td>Binary</td><td>1</td><td>Identifier</td><td/><td>External key to the Binary table.</td></row>
		<row><td>ISLocalControl</td><td>Control_</td><td>N</td><td/><td/><td>Control</td><td>2</td><td>Identifier</td><td/><td>Name of the control. This name must be unique within a dialog, but can repeat on different dialogs.</td></row>
		<row><td>ISLocalControl</td><td>Dialog_</td><td>N</td><td/><td/><td>Dialog</td><td>1</td><td>Identifier</td><td/><td>External key to the Dialog table, name of the dialog.</td></row>
		<row><td>ISLocalControl</td><td>Height</td><td>Y</td><td/><td/><td/><td/><td/><td/><td>Height of the bounding rectangle of the control.</td></row>
		<row><td>ISLocalControl</td><td>ISBuildSourcePath</td><td>Y</td><td/><td/><td/><td/><td>Text</td><td/><td>Full path to .rtf file for scrollable text control</td></row>
		<row><td>ISLocalControl</td><td>ISLanguage_</td><td>N</td><td/><td/><td>ISLanguage</td><td>1</td><td>Language</td><td/><td>This is a foreign key to the ISLanguage table.</td></row>
		<row><td>ISLocalControl</td><td>Width</td><td>Y</td><td/><td/><td/><td/><td/><td/><td>Width of the bounding rectangle of the control.</td></row>
		<row><td>ISLocalControl</td><td>X</td><td>Y</td><td/><td/><td/><td/><td/><td/><td>Horizontal coordinate of the upper left corner of the bounding rectangle of the control.</td></row>
		<row><td>ISLocalControl</td><td>Y</td><td>Y</td><td/><td/><td/><td/><td/><td/><td>Vertical coordinate of the upper left corner of the bounding rectangle of the control.</td></row>
		<row><td>ISLocalDialog</td><td>Attributes</td><td>Y</td><td/><td/><td/><td/><td/><td/><td>A 32-bit word that specifies the attribute flags to be applied to this dialog.</td></row>
		<row><td>ISLocalDialog</td><td>Dialog_</td><td>Y</td><td/><td/><td>Dialog</td><td>1</td><td>Identifier</td><td/><td>Name of the dialog.</td></row>
		<row><td>ISLocalDialog</td><td>Height</td><td>N</td><td>0</td><td>32767</td><td/><td/><td/><td/><td>Height of the bounding rectangle of the dialog.</td></row>
		<row><td>ISLocalDialog</td><td>ISLanguage_</td><td>Y</td><td/><td/><td>ISLanguage</td><td>1</td><td>Language</td><td/><td>This is a foreign key to the ISLanguage table.</td></row>
		<row><td>ISLocalDialog</td><td>TextStyle_</td><td>Y</td><td/><td/><td/><td/><td>Identifier</td><td/><td>Foreign Key into TextStyle table, only used in Script Based Projects.</td></row>
		<row><td>ISLocalDialog</td><td>Width</td><td>N</td><td>0</td><td>32767</td><td/><td/><td/><td/><td>Width of the bounding rectangle of the dialog.</td></row>
		<row><td>ISLocalRadioButton</td><td>Height</td><td>N</td><td>0</td><td>32767</td><td/><td/><td/><td/><td>The height of the button.</td></row>
		<row><td>ISLocalRadioButton</td><td>ISLanguage_</td><td>N</td><td/><td/><td>ISLanguage</td><td>1</td><td>Language</td><td/><td>This is a foreign key to the ISLanguage table.</td></row>
		<row><td>ISLocalRadioButton</td><td>Order</td><td>N</td><td>1</td><td>32767</td><td>RadioButton</td><td>2</td><td/><td/><td>A positive integer used to determine the ordering of the items within one list..The integers do not have to be consecutive.</td></row>
		<row><td>ISLocalRadioButton</td><td>Property</td><td>N</td><td/><td/><td>RadioButton</td><td>1</td><td>Identifier</td><td/><td>A named property to be tied to this radio button. All the buttons tied to the same property become part of the same group.</td></row>
		<row><td>ISLocalRadioButton</td><td>Width</td><td>N</td><td>0</td><td>32767</td><td/><td/><td/><td/><td>The width of the button.</td></row>
		<row><td>ISLocalRadioButton</td><td>X</td><td>N</td><td>0</td><td>32767</td><td/><td/><td/><td/><td>The horizontal coordinate of the upper left corner of the bounding rectangle of the radio button.</td></row>
		<row><td>ISLocalRadioButton</td><td>Y</td><td>N</td><td>0</td><td>32767</td><td/><td/><td/><td/><td>The vertical coordinate of the upper left corner of the bounding rectangle of the radio button.</td></row>
		<row><td>ISMergeModule</td><td>Destination</td><td>Y</td><td/><td/><td/><td/><td>Text</td><td/><td>Destination.
</td></row>
		<row><td>ISMergeModule</td><td>ISAttributes</td><td>Y</td><td/><td/><td/><td/><td/><td/><td>This is used to store Installshield custom properties of a merge module.</td></row>
		<row><td>ISMergeModule</td><td>ISMergeModule</td><td>N</td><td/><td/><td/><td/><td>Text</td><td/><td>The GUID identifying the merge module.
</td></row>
		<row><td>ISMergeModule</td><td>Language</td><td>N</td><td/><td/><td/><td/><td/><td/><td>Default decimal language of module.</td></row>
		<row><td>ISMergeModule</td><td>Name</td><td>N</td><td/><td/><td/><td/><td>Text</td><td/><td>Name of the merge module.
</td></row>
		<row><td>ISMergeModuleCfgValues</td><td>Attributes</td><td>Y</td><td/><td/><td/><td/><td/><td/><td>Attributes (from configurable merge module)</td></row>
		<row><td>ISMergeModuleCfgValues</td><td>ContextData</td><td>Y</td><td/><td/><td/><td/><td>Text</td><td/><td>ContextData  (from configurable merge module)</td></row>
		<row><td>ISMergeModuleCfgValues</td><td>DefaultValue</td><td>Y</td><td/><td/><td/><td/><td>Text</td><td/><td>DefaultValue  (from configurable merge module)</td></row>
		<row><td>ISMergeModuleCfgValues</td><td>Description</td><td>Y</td><td/><td/><td/><td/><td>Text</td><td/><td>Description (from configurable merge module)</td></row>
		<row><td>ISMergeModuleCfgValues</td><td>DisplayName</td><td>Y</td><td/><td/><td/><td/><td>Text</td><td/><td>DisplayName (from configurable merge module)</td></row>
		<row><td>ISMergeModuleCfgValues</td><td>Format</td><td>N</td><td/><td/><td/><td/><td/><td/><td>Format (from configurable merge module)</td></row>
		<row><td>ISMergeModuleCfgValues</td><td>HelpKeyword</td><td>Y</td><td/><td/><td/><td/><td>Text</td><td/><td>HelpKeyword (from configurable merge module)</td></row>
		<row><td>ISMergeModuleCfgValues</td><td>HelpLocation</td><td>Y</td><td/><td/><td/><td/><td>Text</td><td/><td>HelpLocation (from configurable merge module)</td></row>
		<row><td>ISMergeModuleCfgValues</td><td>ISMergeModule_</td><td>N</td><td/><td/><td>ISMergeModule</td><td>1</td><td>Text</td><td/><td>The module signature, a foreign key into the ISMergeModule table</td></row>
		<row><td>ISMergeModuleCfgValues</td><td>Language_</td><td>N</td><td/><td/><td>ISMergeModule</td><td>2</td><td/><td/><td>Default decimal language of module.</td></row>
		<row><td>ISMergeModuleCfgValues</td><td>ModuleConfiguration_</td><td>N</td><td/><td/><td/><td/><td>Identifier</td><td/><td>Identifier, foreign key into ModuleConfiguration table (ModuleConfiguration.Name)</td></row>
		<row><td>ISMergeModuleCfgValues</td><td>Type</td><td>Y</td><td/><td/><td/><td/><td>Text</td><td/><td>Type (from configurable merge module)</td></row>
		<row><td>ISMergeModuleCfgValues</td><td>Value</td><td>Y</td><td/><td/><td/><td/><td>Text</td><td/><td>Value for this item.</td></row>
		<row><td>ISPathVariable</td><td>ISPathVariable</td><td>N</td><td/><td/><td/><td/><td/><td/><td>The name of the path variable.</td></row>
		<row><td>ISPathVariable</td><td>TestValue</td><td>Y</td><td/><td/><td/><td/><td>Text</td><td/><td>The test value of the path variable.</td></row>
		<row><td>ISPathVariable</td><td>Type</td><td>N</td><td/><td/><td/><td/><td/><td>1;2;4;8</td><td>The type of the path variable.</td></row>
		<row><td>ISPathVariable</td><td>Value</td><td>Y</td><td/><td/><td/><td/><td>Text</td><td/><td>The value of the path variable.</td></row>
		<row><td>ISProObjectProperty</td><td>Feature_</td><td>N</td><td/><td/><td>Feature</td><td>1</td><td>Text</td><td/><td>Foreign key into the Feature table.
</td></row>
		<row><td>ISProObjectProperty</td><td>Property</td><td>N</td><td/><td/><td/><td/><td>Text</td><td/><td>Object property name</td></row>
		<row><td>ISProObjectProperty</td><td>Value</td><td>Y</td><td/><td/><td/><td/><td>Text</td><td/><td>String value for property. Never null or empty.



</td></row>
		<row><td>ISProductConfiguration</td><td>GeneratePackageCode</td><td>Y</td><td/><td/><td/><td/><td>Number</td><td>0;1</td><td>Indicates whether or not to generate a package code.
</td></row>
		<row><td>ISProductConfiguration</td><td>ISProductConfiguration</td><td>N</td><td/><td/><td/><td/><td>Text</td><td/><td>The name of the product configuration.
</td></row>
		<row><td>ISProductConfiguration</td><td>ProductConfigurationFlags</td><td>Y</td><td/><td/><td/><td/><td>Text</td><td/><td>Product configuration (release) flags.
</td></row>
		<row><td>ISProductConfigurationProperty</td><td>ISProductConfiguration_</td><td>N</td><td/><td/><td>ISProductConfiguration</td><td>1</td><td>Text</td><td/><td>Foreign key into the ISProductConfiguration table.
</td></row>
		<row><td>ISProductConfigurationProperty</td><td>Property</td><td>N</td><td/><td/><td>Property</td><td>1</td><td>Text</td><td/><td>Product Congiuration property name</td></row>
		<row><td>ISProductConfigurationProperty</td><td>Value</td><td>Y</td><td/><td/><td/><td/><td>Text</td><td/><td>String value for property. Never null or empty.



</td></row>
		<row><td>ISRegistrySet</td><td>Comments</td><td>Y</td><td/><td/><td/><td/><td>Text</td><td/><td>User Comments.</td></row>
		<row><td>ISRegistrySet</td><td>ISRegistrySet</td><td>N</td><td/><td/><td/><td/><td>Text</td><td/><td>Name of the Registry Set</td></row>
		<row><td>ISRegistrySetComponents</td><td>Component_</td><td>N</td><td/><td/><td>Component</td><td>1</td><td>Text</td><td/><td>Foreign key into Component  table</td></row>
		<row><td>ISRegistrySetComponents</td><td>ISRegistrySet_</td><td>N</td><td/><td/><td>ISRegistrySet</td><td>1</td><td>Text</td><td/><td>Foreign key into ISRegistrySet table</td></row>
		<row><td>ISRelease</td><td>Attributes</td><td>N</td><td/><td/><td/><td/><td/><td/><td>Bitfield holding boolean values for various release attributes.</td></row>
		<row><td>ISRelease</td><td>BuildLocation</td><td>Y</td><td/><td/><td/><td/><td>Text</td><td/><td>Build location.</td></row>
		<row><td>ISRelease</td><td>CDBrowser</td><td>Y</td><td/><td/><td/><td/><td>Text</td><td/><td>Demoshield browser location.</td></row>
		<row><td>ISRelease</td><td>DefaultLanguage</td><td>N</td><td/><td/><td/><td/><td>Language</td><td/><td>Default language for setup.</td></row>
		<row><td>ISRelease</td><td>DigitalPVK</td><td>Y</td><td/><td/><td/><td/><td>Text</td><td/><td>Digital signing private key (.pvk) file.</td></row>
		<row><td>ISRelease</td><td>DigitalSPC</td><td>Y</td><td/><td/><td/><td/><td>Text</td><td/><td>Digital signing Software Publisher Certificate (.spc) file.</td></row>
		<row><td>ISRelease</td><td>DigitalURL</td><td>Y</td><td/><td/><td/><td/><td>Text</td><td/><td>Digital signing URL.</td></row>
		<row><td>ISRelease</td><td>DiskClusterSize</td><td>N</td><td/><td/><td/><td/><td/><td/><td>Disk cluster size.</td></row>
		<row><td>ISRelease</td><td>DiskSize</td><td>N</td><td/><td/><td/><td/><td>Text</td><td/><td>Disk size.</td></row>
		<row><td>ISRelease</td><td>DiskSizeUnit</td><td>N</td><td/><td/><td/><td/><td/><td>0;1;2</td><td>Disk size units (KB or MB).</td></row>
		<row><td>ISRelease</td><td>DiskSpanning</td><td>N</td><td/><td/><td/><td/><td/><td>0;1;2</td><td>Disk spanning (automatic, enforce size, etc.).</td></row>
		<row><td>ISRelease</td><td>DotNetBuildConfiguration</td><td>Y</td><td/><td/><td/><td/><td>Text</td><td/><td>Build Configuration for .NET solutions.</td></row>
		<row><td>ISRelease</td><td>ISProductConfiguration_</td><td>N</td><td/><td/><td>ISProductConfiguration</td><td>1</td><td>Text</td><td/><td>Foreign key into the ISProductConfiguration table.</td></row>
		<row><td>ISRelease</td><td>ISRelease</td><td>N</td><td/><td/><td/><td/><td>Text</td><td/><td>The name of the release.
</td></row>
		<row><td>ISRelease</td><td>ISSetupPrerequisiteLocation</td><td>Y</td><td/><td/><td/><td/><td/><td>0;1;2</td><td>Location the Setup Prerequisites will be placed in</td></row>
		<row><td>ISRelease</td><td>MediaLocation</td><td>N</td><td/><td/><td/><td/><td>Text</td><td/><td>Media location on disk.</td></row>
		<row><td>ISRelease</td><td>MsiCommandLine</td><td>Y</td><td/><td/><td/><td/><td>Text</td><td/><td>Command line passed to the msi package from setup.exe</td></row>
		<row><td>ISRelease</td><td>MsiSourceType</td><td>N</td><td>-1</td><td>4</td><td/><td/><td/><td/><td>MSI media source type.</td></row>
		<row><td>ISRelease</td><td>PackageName</td><td>N</td><td/><td/><td/><td/><td>Text</td><td/><td>Package name.</td></row>
		<row><td>ISRelease</td><td>Password</td><td>Y</td><td/><td/><td/><td/><td>Text</td><td/><td>Password.</td></row>
		<row><td>ISRelease</td><td>Platforms</td><td>N</td><td/><td/><td/><td/><td>Text</td><td/><td>Platforms supported (Intel, Alpha, etc.).</td></row>
		<row><td>ISRelease</td><td>ReleaseFlags</td><td>Y</td><td/><td/><td/><td/><td>Text</td><td/><td>Release flags.</td></row>
		<row><td>ISRelease</td><td>ReleaseType</td><td>N</td><td/><td/><td/><td/><td/><td>1;2;4</td><td>Release type (single, uncompressed, etc.).</td></row>
		<row><td>ISRelease</td><td>SupportedLanguagesData</td><td>Y</td><td/><td/><td/><td/><td>Text</td><td/><td>Languages supported (for component filtering).</td></row>
		<row><td>ISRelease</td><td>SupportedLanguagesUI</td><td>N</td><td/><td/><td/><td/><td>Text</td><td/><td>UI languages supported.</td></row>
		<row><td>ISRelease</td><td>SupportedOSs</td><td>N</td><td/><td/><td/><td/><td/><td/><td>Indicate which operating systmes are supported.</td></row>
		<row><td>ISRelease</td><td>SynchMsi</td><td>Y</td><td/><td/><td/><td/><td>Text</td><td/><td>MSI file to synchronize file keys and other data with (patch-like functionality).</td></row>
		<row><td>ISRelease</td><td>Type</td><td>N</td><td>0</td><td>6</td><td/><td/><td/><td/><td>Release type (CDROM, Network, etc.).</td></row>
		<row><td>ISRelease</td><td>URLLocation</td><td>Y</td><td/><td/><td/><td/><td>Text</td><td/><td>Media location via URL.</td></row>
		<row><td>ISRelease</td><td>VersionCopyright</td><td>Y</td><td/><td/><td/><td/><td>Text</td><td/><td>Version stamp information.</td></row>
		<row><td>ISReleasePro</td><td>Attributes</td><td>Y</td><td/><td/><td/><td/><td/><td/><td>Bitfield holding boolean values for various release attributes.</td></row>
		<row><td>ISReleasePro</td><td>BatchFileName</td><td>Y</td><td/><td/><td/><td/><td>Text</td><td/><td>Post Build ... Execute Batch File</td></row>
		<row><td>ISReleasePro</td><td>BuildSize</td><td>Y</td><td/><td/><td/><td/><td/><td/><td>Media size.  Only visible for IFTW and custom build types.</td></row>
		<row><td>ISReleasePro</td><td>BuildType</td><td>Y</td><td/><td/><td/><td/><td/><td/><td>The type of media being built.</td></row>
		<row><td>ISReleasePro</td><td>CertPassword</td><td>Y</td><td/><td/><td/><td/><td>Text</td><td/><td>Digital certificate password</td></row>
		<row><td>ISReleasePro</td><td>CopyToFolder</td><td>Y</td><td/><td/><td/><td/><td>Text</td><td/><td>Post Build ... Copy To Folder</td></row>
		<row><td>ISReleasePro</td><td>FTPFolder</td><td>Y</td><td/><td/><td/><td/><td>Text</td><td/><td>PostBuild ... FTP Site Folder</td></row>
		<row><td>ISReleasePro</td><td>FTPPassword</td><td>Y</td><td/><td/><td/><td/><td>Text</td><td/><td>PostBuild ... FTP Site password</td></row>
		<row><td>ISReleasePro</td><td>FTPUsername</td><td>Y</td><td/><td/><td/><td/><td>Text</td><td/><td>PostBuild ... FTP Site username</td></row>
		<row><td>ISReleasePro</td><td>GUID</td><td>Y</td><td/><td/><td/><td/><td>Guid</td><td/><td>Guid for the Media.  Internal value for Pro.</td></row>
		<row><td>ISReleasePro</td><td>ISProductConfiguration_</td><td>N</td><td/><td/><td>ISProductConfiguration</td><td>1</td><td>Text</td><td/><td>Foreign key into the ISProductConfiguration table.</td></row>
		<row><td>ISReleasePro</td><td>ISRelease_</td><td>N</td><td/><td/><td>ISRelease</td><td>1</td><td>Text</td><td/><td>The name of the Pro release.
</td></row>
		<row><td>ISReleasePro</td><td>InitDlgProductName</td><td>Y</td><td/><td/><td/><td/><td>Text</td><td/><td>An alternate product display name, used in the setup initialization dialog.</td></row>
		<row><td>ISReleasePro</td><td>Platform</td><td>Y</td><td/><td/><td/><td/><td/><td/><td>Platform(s)</td></row>
		<row><td>ISReleasePro</td><td>PreProcDefines</td><td>Y</td><td/><td/><td/><td/><td>Text</td><td/><td>Compiler PreProcessor Defines</td></row>
		<row><td>ISReleasePro</td><td>SetupDlgAppName</td><td>Y</td><td/><td/><td/><td/><td>Text</td><td/><td>Only in UI Page...Advanced</td></row>
		<row><td>ISReleasePro</td><td>SingleExeFilename</td><td>Y</td><td/><td/><td/><td/><td>Text</td><td/><td>Single Exe File Name.</td></row>
		<row><td>ISReleasePro</td><td>SingleExeIconName</td><td>Y</td><td/><td/><td/><td/><td>Text</td><td/><td>Single Exe Icon File Name.</td></row>
		<row><td>ISReleasePro</td><td>SkinName</td><td>Y</td><td/><td/><td/><td/><td>Text</td><td/><td>User Interface ... Skin</td></row>
		<row><td>ISReleasePro</td><td>SupportedVersions</td><td>Y</td><td/><td/><td/><td/><td>Text</td><td/><td>Update ... Supported Version(s)</td></row>
		<row><td>ISReleasePro</td><td>WebPageURL</td><td>Y</td><td/><td/><td/><td/><td>Text</td><td/><td>Internet... Web Page URL.</td></row>
		<row><td>ISReleaseProDataAsFiles</td><td>Feature_</td><td>N</td><td/><td/><td>Feature</td><td>1</td><td>Text</td><td/><td>Foreign key to the feature table</td></row>
		<row><td>ISReleaseProDataAsFiles</td><td>ISProductConfiguration_</td><td>N</td><td/><td/><td>ISProductConfiguration</td><td>1</td><td>Text</td><td/><td>Foreign key into the ISProductConfiguration table.</td></row>
		<row><td>ISReleaseProDataAsFiles</td><td>ISRelease_</td><td>N</td><td/><td/><td>ISRelease</td><td>1</td><td>Text</td><td/><td>The name of the Pro release.
</td></row>
		<row><td>ISReleaseProFeatureInclude</td><td>Feature_</td><td>N</td><td/><td/><td>Feature</td><td>1</td><td>Text</td><td/><td>Foreign key to the feature table</td></row>
		<row><td>ISReleaseProFeatureInclude</td><td>ISProductConfiguration_</td><td>N</td><td/><td/><td>ISProductConfiguration</td><td>1</td><td>Text</td><td/><td>Foreign key into the ISProductConfiguration table.</td></row>
		<row><td>ISReleaseProFeatureInclude</td><td>ISRelease_</td><td>N</td><td/><td/><td>ISRelease</td><td>1</td><td>Text</td><td/><td>The name of the Pro release.
</td></row>
		<row><td>ISReleaseProOtherDiskFiles</td><td>Disk</td><td>Y</td><td/><td/><td/><td/><td/><td/><td>The disk to put the file on</td></row>
		<row><td>ISReleaseProOtherDiskFiles</td><td>ISDisk1File_</td><td>N</td><td/><td/><td>ISDisk1File</td><td>1</td><td>Identifier</td><td/><td>Foreign key to the ISDisk1File  table</td></row>
		<row><td>ISReleaseProOtherDiskFiles</td><td>ISProductConfiguration_</td><td>N</td><td/><td/><td>ISProductConfiguration</td><td>1</td><td>Text</td><td/><td>Foreign key into the ISProductConfiguration table.</td></row>
		<row><td>ISReleaseProOtherDiskFiles</td><td>ISRelease_</td><td>N</td><td/><td/><td>ISRelease</td><td>1</td><td>Text</td><td/><td>The name of the Pro release.
</td></row>
		<row><td>ISReleaseProPreviousMedias</td><td>ISProductConfiguration_</td><td>N</td><td/><td/><td>ISProductConfiguration</td><td>1</td><td>Text</td><td/><td>Foreign key into the ISProductConfiguration table.</td></row>
		<row><td>ISReleaseProPreviousMedias</td><td>ISRelease_</td><td>N</td><td/><td/><td>ISRelease</td><td>1</td><td>Text</td><td/><td>The name of the Pro release.
</td></row>
		<row><td>ISReleaseProPreviousMedias</td><td>MediaFile</td><td>N</td><td/><td/><td/><td/><td>Text</td><td/><td>The media file</td></row>
		<row><td>ISReleaseProPreviousMedias</td><td>PreviousProMedia</td><td>Y</td><td/><td/><td/><td/><td>Identifier</td><td/><td>Unique identifier for this record.  The following two columns do not have to be unique.</td></row>
		<row><td>ISReleaseProPreviousMedias</td><td>Version</td><td>Y</td><td/><td/><td/><td/><td>Text</td><td/><td>Version of media file.  -1 means to read it from the media file.</td></row>
		<row><td>ISReleaseProReserveSpace</td><td>Disk</td><td>N</td><td/><td/><td/><td/><td/><td/><td>The disk to put the file on</td></row>
		<row><td>ISReleaseProReserveSpace</td><td>ISProductConfiguration_</td><td>N</td><td/><td/><td>ISProductConfiguration</td><td>1</td><td>Text</td><td/><td>Foreign key into the ISProductConfiguration table.</td></row>
		<row><td>ISReleaseProReserveSpace</td><td>ISRelease_</td><td>N</td><td/><td/><td>ISRelease</td><td>1</td><td>Text</td><td/><td>The name of the Pro release.
</td></row>
		<row><td>ISReleaseProReserveSpace</td><td>KBsReserved</td><td>Y</td><td/><td/><td/><td/><td/><td/><td>Amount of space in KBs to reserve.</td></row>
		<row><td>ISReleasePublishInfo</td><td>Description</td><td>Y</td><td/><td/><td/><td/><td>Text</td><td/><td>Repository item description</td></row>
		<row><td>ISReleasePublishInfo</td><td>DisplayName</td><td>Y</td><td/><td/><td/><td/><td>Text</td><td/><td>Repository item display name</td></row>
		<row><td>ISReleasePublishInfo</td><td>ISAttributes</td><td>Y</td><td/><td/><td/><td/><td/><td/><td>Bitfield holding various attributes</td></row>
		<row><td>ISReleasePublishInfo</td><td>ISProductConfiguration_</td><td>N</td><td/><td/><td>ISProductConfiguration</td><td>1</td><td>Text</td><td/><td>Foreign key into the ISProductConfiguration table.</td></row>
		<row><td>ISReleasePublishInfo</td><td>ISRelease_</td><td>N</td><td/><td/><td>ISRelease</td><td>1</td><td>Text</td><td/><td>The name of the release.</td></row>
		<row><td>ISReleasePublishInfo</td><td>Publisher</td><td>Y</td><td/><td/><td/><td/><td>Text</td><td/><td>Repository item publisher</td></row>
		<row><td>ISReleasePublishInfo</td><td>Repository</td><td>Y</td><td/><td/><td/><td/><td>Text</td><td/><td>Repository which to  publish the built merge module</td></row>
		<row><td>ISRequiredFeature</td><td>RequiredFeature</td><td>N</td><td/><td/><td>Feature</td><td>1</td><td>Guid</td><td/><td>Foreign key into Feature table.</td></row>
		<row><td>ISRequiredFeature</td><td>RequiringFeature</td><td>N</td><td/><td/><td>Feature</td><td>1</td><td>Guid</td><td/><td>Foreign key into Feature table.</td></row>
		<row><td>ISSQLConnection</td><td>Attributes</td><td>N</td><td/><td/><td/><td/><td/><td/><td/></row>
		<row><td>ISSQLConnection</td><td>Authentication</td><td>N</td><td/><td/><td/><td/><td/><td/><td/></row>
		<row><td>ISSQLConnection</td><td>CmdTimeout</td><td>Y</td><td/><td/><td/><td/><td/><td/><td/></row>
		<row><td>ISSQLConnection</td><td>Comments</td><td>Y</td><td/><td/><td/><td/><td/><td/><td/></row>
		<row><td>ISSQLConnection</td><td>Database</td><td>N</td><td/><td/><td/><td/><td/><td/><td/></row>
		<row><td>ISSQLConnection</td><td>ISSQLConnection</td><td>N</td><td/><td/><td/><td/><td/><td/><td/></row>
		<row><td>ISSQLConnection</td><td>Order</td><td>N</td><td/><td/><td/><td/><td/><td/><td/></row>
		<row><td>ISSQLConnection</td><td>Password</td><td>N</td><td/><td/><td/><td/><td/><td/><td/></row>
		<row><td>ISSQLConnection</td><td>Server</td><td>N</td><td/><td/><td/><td/><td/><td/><td/></row>
		<row><td>ISSQLConnection</td><td>UserName</td><td>N</td><td/><td/><td/><td/><td/><td/><td/></row>
		<row><td>ISSQLConnectionDBServer</td><td>ISSQLConnectionDBServer</td><td>N</td><td/><td/><td/><td/><td/><td/><td/></row>
		<row><td>ISSQLConnectionDBServer</td><td>ISSQLConnection_</td><td>N</td><td/><td/><td>ISSQLConnection</td><td>1</td><td/><td/><td/></row>
		<row><td>ISSQLConnectionDBServer</td><td>ISSQLDBMetaData_</td><td>N</td><td/><td/><td>ISSQLDBMetaData</td><td>1</td><td/><td/><td/></row>
		<row><td>ISSQLConnectionDBServer</td><td>Order</td><td>N</td><td/><td/><td/><td/><td/><td/><td/></row>
		<row><td>ISSQLConnectionScript</td><td>ISSQLConnection_</td><td>N</td><td/><td/><td>ISSQLConnection</td><td>1</td><td/><td/><td/></row>
		<row><td>ISSQLConnectionScript</td><td>ISSQLScriptFile_</td><td>N</td><td/><td/><td>ISSQLScriptFile</td><td>1</td><td/><td/><td/></row>
		<row><td>ISSQLConnectionScript</td><td>Order</td><td>N</td><td/><td/><td/><td/><td/><td/><td/></row>
		<row><td>ISSQLDBMetaData</td><td>AdoCxnDatabase</td><td>Y</td><td/><td/><td/><td/><td/><td/><td/></row>
		<row><td>ISSQLDBMetaData</td><td>AdoCxnDriver</td><td>Y</td><td/><td/><td/><td/><td/><td/><td/></row>
		<row><td>ISSQLDBMetaData</td><td>AdoCxnNetLibrary</td><td>Y</td><td/><td/><td/><td/><td/><td/><td/></row>
		<row><td>ISSQLDBMetaData</td><td>AdoCxnPassword</td><td>Y</td><td/><td/><td/><td/><td/><td/><td/></row>
		<row><td>ISSQLDBMetaData</td><td>AdoCxnServer</td><td>Y</td><td/><td/><td/><td/><td/><td/><td/></row>
		<row><td>ISSQLDBMetaData</td><td>AdoCxnUserID</td><td>Y</td><td/><td/><td/><td/><td/><td/><td/></row>
		<row><td>ISSQLDBMetaData</td><td>AdoCxnWindowsSecurity</td><td>Y</td><td/><td/><td/><td/><td/><td/><td/></row>
		<row><td>ISSQLDBMetaData</td><td>AdoDriverName</td><td>Y</td><td/><td/><td/><td/><td/><td/><td/></row>
		<row><td>ISSQLDBMetaData</td><td>CreateDbCmd</td><td>Y</td><td/><td/><td/><td/><td/><td/><td/></row>
		<row><td>ISSQLDBMetaData</td><td>DisplayName</td><td>Y</td><td/><td/><td/><td/><td/><td/><td/></row>
		<row><td>ISSQLDBMetaData</td><td>DsnODBCName</td><td>Y</td><td/><td/><td/><td/><td/><td/><td/></row>
		<row><td>ISSQLDBMetaData</td><td>ISAttributes</td><td>Y</td><td/><td/><td/><td/><td/><td/><td/></row>
		<row><td>ISSQLDBMetaData</td><td>ISSQLDBMetaData</td><td>N</td><td/><td/><td/><td/><td/><td/><td/></row>
		<row><td>ISSQLDBMetaData</td><td>LocalInstanceNames</td><td>Y</td><td/><td/><td/><td/><td/><td/><td/></row>
		<row><td>ISSQLDBMetaData</td><td>SwitchDbCmd</td><td>Y</td><td/><td/><td/><td/><td/><td/><td/></row>
		<row><td>ISSQLDBMetaData</td><td>TestDatabaseCmd</td><td>Y</td><td/><td/><td/><td/><td/><td/><td/></row>
		<row><td>ISSQLDBMetaData</td><td>TestTableCmd</td><td>Y</td><td/><td/><td/><td/><td/><td/><td/></row>
		<row><td>ISSQLDBMetaData</td><td>TestTableCmd2</td><td>Y</td><td/><td/><td/><td/><td/><td/><td/></row>
		<row><td>ISSQLDBMetaData</td><td>VersionBeginToken</td><td>Y</td><td/><td/><td/><td/><td/><td/><td/></row>
		<row><td>ISSQLDBMetaData</td><td>VersionEndToken</td><td>Y</td><td/><td/><td/><td/><td/><td/><td/></row>
		<row><td>ISSQLDBMetaData</td><td>VersionInfoCmd</td><td>Y</td><td/><td/><td/><td/><td/><td/><td/></row>
		<row><td>ISSQLDBMetaData</td><td>WinAuthentUserId</td><td>Y</td><td/><td/><td/><td/><td/><td/><td/></row>
		<row><td>ISSQLRequirement</td><td>Attributes</td><td>N</td><td/><td/><td/><td/><td/><td/><td/></row>
		<row><td>ISSQLRequirement</td><td>ISSQLConnectionDBServer_</td><td>Y</td><td/><td/><td>ISSQLConnectionDBServer</td><td>1</td><td/><td/><td/></row>
		<row><td>ISSQLRequirement</td><td>ISSQLConnection_</td><td>N</td><td/><td/><td>ISSQLConnection</td><td>1</td><td/><td/><td/></row>
		<row><td>ISSQLRequirement</td><td>ISSQLRequirement</td><td>N</td><td/><td/><td/><td/><td/><td/><td/></row>
		<row><td>ISSQLRequirement</td><td>MajorVersion</td><td>Y</td><td/><td/><td/><td/><td/><td/><td/></row>
		<row><td>ISSQLRequirement</td><td>ServicePackLevel</td><td>Y</td><td/><td/><td/><td/><td/><td/><td/></row>
		<row><td>ISSQLScriptError</td><td>Attributes</td><td>N</td><td/><td/><td/><td/><td/><td/><td/></row>
		<row><td>ISSQLScriptError</td><td>ErrHandling</td><td>N</td><td/><td/><td/><td/><td/><td/><td/></row>
		<row><td>ISSQLScriptError</td><td>ErrNumber</td><td>N</td><td/><td/><td/><td/><td/><td/><td/></row>
		<row><td>ISSQLScriptError</td><td>ISSQLScriptFile_</td><td>Y</td><td/><td/><td>ISSQLScriptFile</td><td>1</td><td>Identifier</td><td/><td>Foreign key referencing ISSQLScriptFile</td></row>
		<row><td>ISSQLScriptError</td><td>Message</td><td>Y</td><td/><td/><td/><td/><td>Text</td><td/><td>Custom end-user message. Reserved for future use.</td></row>
		<row><td>ISSQLScriptFile</td><td>Attributes</td><td>N</td><td/><td/><td/><td/><td/><td/><td/></row>
		<row><td>ISSQLScriptFile</td><td>Comments</td><td>Y</td><td/><td/><td/><td/><td>Text</td><td/><td>Comments</td></row>
		<row><td>ISSQLScriptFile</td><td>Component_</td><td>N</td><td/><td/><td>Component</td><td>1</td><td>Identifier</td><td/><td>Foreign key referencing Component that controls the SQL script.</td></row>
		<row><td>ISSQLScriptFile</td><td>ErrorHandling</td><td>N</td><td/><td/><td/><td/><td/><td/><td/></row>
		<row><td>ISSQLScriptFile</td><td>ISBuildSourcePath</td><td>Y</td><td/><td/><td/><td/><td>Text</td><td/><td>Full path, the category is of Text instead of Path because of potential use of path variables.</td></row>
		<row><td>ISSQLScriptFile</td><td>ISSQLScriptFile</td><td>N</td><td/><td/><td/><td/><td>Identifier</td><td/><td>This is the primary key to the ISSQLScriptFile table</td></row>
		<row><td>ISSQLScriptFile</td><td>InstallText</td><td>Y</td><td/><td/><td/><td/><td>Text</td><td/><td>Feedback end-user text at install</td></row>
		<row><td>ISSQLScriptFile</td><td>Scheduling</td><td>N</td><td/><td/><td/><td/><td/><td/><td/></row>
		<row><td>ISSQLScriptFile</td><td>UninstallText</td><td>Y</td><td/><td/><td/><td/><td>Text</td><td/><td>Feedback end-user text at Uninstall</td></row>
		<row><td>ISSQLScriptFile</td><td>Version</td><td>Y</td><td/><td/><td/><td/><td>Text</td><td/><td>Schema Version (####.#####.####)</td></row>
		<row><td>ISSQLScriptImport</td><td>Attributes</td><td>N</td><td/><td/><td/><td/><td/><td/><td/></row>
		<row><td>ISSQLScriptImport</td><td>Authentication</td><td>N</td><td/><td/><td/><td/><td/><td/><td/></row>
		<row><td>ISSQLScriptImport</td><td>Database</td><td>Y</td><td/><td/><td/><td/><td/><td/><td/></row>
		<row><td>ISSQLScriptImport</td><td>ExcludeTables</td><td>Y</td><td/><td/><td/><td/><td/><td/><td/></row>
		<row><td>ISSQLScriptImport</td><td>ISSQLScriptFile_</td><td>N</td><td/><td/><td>ISSQLScriptFile</td><td>1</td><td/><td/><td/></row>
		<row><td>ISSQLScriptImport</td><td>IncludeTables</td><td>Y</td><td/><td/><td/><td/><td/><td/><td/></row>
		<row><td>ISSQLScriptImport</td><td>Password</td><td>Y</td><td/><td/><td/><td/><td/><td/><td/></row>
		<row><td>ISSQLScriptImport</td><td>Server</td><td>Y</td><td/><td/><td/><td/><td/><td/><td/></row>
		<row><td>ISSQLScriptImport</td><td>UserName</td><td>Y</td><td/><td/><td/><td/><td/><td/><td/></row>
		<row><td>ISSQLScriptReplace</td><td>Attributes</td><td>N</td><td/><td/><td/><td/><td/><td/><td/></row>
		<row><td>ISSQLScriptReplace</td><td>ISSQLScriptFile_</td><td>N</td><td/><td/><td>ISSQLScriptFile</td><td>1</td><td/><td/><td/></row>
		<row><td>ISSQLScriptReplace</td><td>ISSQLScriptReplace</td><td>N</td><td/><td/><td/><td/><td/><td/><td/></row>
		<row><td>ISSQLScriptReplace</td><td>Replace</td><td>Y</td><td/><td/><td/><td/><td/><td/><td/></row>
		<row><td>ISSQLScriptReplace</td><td>Search</td><td>Y</td><td/><td/><td/><td/><td/><td/><td/></row>
		<row><td>ISScriptFile</td><td>ISScriptFile</td><td>N</td><td/><td/><td/><td/><td>Text</td><td/><td>This is the full path of the script file. The path portion may be expressed in path variable form.
</td></row>
		<row><td>ISSetupFile</td><td>FileName</td><td>Y</td><td/><td/><td/><td/><td>Text</td><td/><td>This is the file name to use when streaming the file to the support files location</td></row>
		<row><td>ISSetupFile</td><td>ISSetupFile</td><td>N</td><td/><td/><td/><td/><td>Identifier</td><td/><td>This is the primary key to the ISSetupFile table</td></row>
		<row><td>ISSetupFile</td><td>Language</td><td>Y</td><td/><td/><td/><td/><td>Text</td><td/><td>Four digit language identifier.  0 for Language Neutral</td></row>
		<row><td>ISSetupFile</td><td>Path</td><td>Y</td><td/><td/><td/><td/><td>Text</td><td/><td>Link to the source file on the build machine</td></row>
		<row><td>ISSetupFile</td><td>Splash</td><td>Y</td><td/><td/><td/><td/><td>Short</td><td/><td>Boolean value indication whether his setup file entry belongs in the Splasc Screen section</td></row>
		<row><td>ISSetupFile</td><td>Stream</td><td>Y</td><td/><td/><td/><td/><td>Binary</td><td/><td>Binary stream. The bits to stream to the support location</td></row>
		<row><td>ISSetupPrerequisites</td><td>ISBuildSourcePath</td><td>Y</td><td/><td/><td/><td/><td/><td/><td/></row>
		<row><td>ISSetupPrerequisites</td><td>ISSetupPrerequisites</td><td>N</td><td/><td/><td/><td/><td/><td/><td/></row>
		<row><td>ISSetupPrerequisites</td><td>Order</td><td>Y</td><td/><td/><td/><td/><td/><td/><td/></row>
		<row><td>ISSetupType</td><td>Comments</td><td>Y</td><td/><td/><td/><td/><td>Text</td><td/><td>User Comments.</td></row>
		<row><td>ISSetupType</td><td>Description</td><td>Y</td><td/><td/><td/><td/><td>Text</td><td/><td>Longer descriptive text describing a visible feature item.</td></row>
		<row><td>ISSetupType</td><td>Display</td><td>N</td><td>0</td><td>32767</td><td/><td/><td/><td/><td>Numeric sort order, used to force a specific display ordering.</td></row>
		<row><td>ISSetupType</td><td>Display_Name</td><td>Y</td><td/><td/><td/><td/><td>Formatted</td><td/><td>A string used to set the initial text contained within a control (if appropriate).</td></row>
		<row><td>ISSetupType</td><td>ISSetupType</td><td>N</td><td/><td/><td/><td/><td>Text</td><td/><td>Primary key used to identify a particular feature record.</td></row>
		<row><td>ISSetupTypeFeatures</td><td>Feature_</td><td>N</td><td/><td/><td>Feature</td><td>1</td><td>Text</td><td/><td>Foreign key into Feature table.</td></row>
		<row><td>ISSetupTypeFeatures</td><td>ISSetupType_</td><td>N</td><td/><td/><td>ISSetupType</td><td>1</td><td>Text</td><td/><td>Foreign key into ISSetupType table.</td></row>
		<row><td>ISShortcutComponents</td><td>Component_</td><td>N</td><td/><td/><td>Component</td><td>1</td><td>Text</td><td/><td>Foreign key into Component table.</td></row>
		<row><td>ISShortcutComponents</td><td>Shortcut_</td><td>N</td><td/><td/><td>Shortcut</td><td>1</td><td>Identifier</td><td/><td>Foreign key into Shortcut  table.</td></row>
		<row><td>ISStorages</td><td>ISBuildSourcePath</td><td>Y</td><td/><td/><td/><td/><td/><td/><td>Path to the file to stream into sub-storage</td></row>
		<row><td>ISStorages</td><td>Name</td><td>N</td><td/><td/><td/><td/><td/><td/><td>Name of the sub-storage key</td></row>
		<row><td>ISString</td><td>Comment</td><td>Y</td><td/><td/><td/><td/><td>Text</td><td/><td>Comment</td></row>
		<row><td>ISString</td><td>Encoded</td><td>Y</td><td/><td/><td/><td/><td/><td/><td>Encoding for multi-byte strings.</td></row>
		<row><td>ISString</td><td>ISLanguage_</td><td>N</td><td/><td/><td/><td/><td>Language</td><td/><td>This is a foreign key to the ISLanguage table.</td></row>
		<row><td>ISString</td><td>ISString</td><td>N</td><td/><td/><td/><td/><td>Text</td><td/><td>String id.</td></row>
		<row><td>ISString</td><td>TimeStamp</td><td>Y</td><td/><td/><td/><td/><td>Time/Date</td><td/><td>Time Stamp. MSI's Time/Date column type is just an int, with bits packed in a certain order.</td></row>
		<row><td>ISString</td><td>Value</td><td>Y</td><td/><td/><td/><td/><td>Text</td><td/><td>real string value.</td></row>
		<row><td>ISVRoot</td><td>AnonyPasswrd</td><td>Y</td><td/><td/><td/><td/><td>Text</td><td/><td>Obsolete column.  Moved to IISCommon</td></row>
		<row><td>ISVRoot</td><td>AnonyUserName</td><td>Y</td><td/><td/><td/><td/><td>Text</td><td/><td>Obsolete column.  Moved to IISCommon</td></row>
		<row><td>ISVRoot</td><td>Component_</td><td>N</td><td/><td/><td>Component</td><td>1</td><td>Text</td><td/><td>Foreign key into Component table</td></row>
		<row><td>ISVRoot</td><td>Condition</td><td>Y</td><td/><td/><td/><td/><td>Text</td><td/><td>Obsolete column.  Moved to Component</td></row>
		<row><td>ISVRoot</td><td>ScriptTimeout</td><td>Y</td><td/><td/><td/><td/><td/><td/><td>Obsolete column.  Moved to IISCommon</td></row>
		<row><td>ISVRoot</td><td>SessionTimeout</td><td>Y</td><td/><td/><td/><td/><td/><td/><td>Obsolete column.  Moved to IISCommon</td></row>
		<row><td>ISVRoot</td><td>VRootAppName</td><td>Y</td><td/><td/><td/><td/><td>Text</td><td/><td>AppName of this VRoot</td></row>
		<row><td>ISVRoot</td><td>VRootDefDoc</td><td>Y</td><td/><td/><td/><td/><td>Text</td><td/><td>Obsolete column.  Moved to IISCommon</td></row>
		<row><td>ISVRoot</td><td>VRootDir</td><td>N</td><td/><td/><td/><td/><td>Text</td><td/><td>Obsolete column.  Moved to IISCommon</td></row>
		<row><td>ISVRoot</td><td>VRootKey</td><td>N</td><td/><td/><td/><td/><td>Text</td><td/><td>Foreign key into ISIISCommon table</td></row>
		<row><td>ISVRoot</td><td>VRootName</td><td>N</td><td/><td/><td/><td/><td>Text</td><td/><td>Obsolete column.  Moved to IISCommon</td></row>
		<row><td>ISVRoot</td><td>VRootProps</td><td>N</td><td/><td/><td/><td/><td/><td/><td>Properties of this VRoot</td></row>
		<row><td>ISVRootAppMaps</td><td>AppMapProps</td><td>N</td><td/><td/><td/><td/><td/><td/><td/></row>
		<row><td>ISVRootAppMaps</td><td>ExecPath</td><td>N</td><td/><td/><td/><td/><td>Text</td><td/><td>Localizable Exec Path</td></row>
		<row><td>ISVRootAppMaps</td><td>Extension</td><td>N</td><td/><td/><td/><td/><td>Text</td><td/><td>Localizable Extension</td></row>
		<row><td>ISVRootAppMaps</td><td>VRootAppMapKey</td><td>N</td><td/><td/><td/><td/><td/><td/><td/></row>
		<row><td>ISVRootAppMaps</td><td>VRootKey</td><td>N</td><td/><td/><td>ISIISCommon</td><td>1</td><td/><td/><td/></row>
		<row><td>ISVRootAppMaps</td><td>Verb</td><td>N</td><td/><td/><td/><td/><td>Text</td><td/><td>Localizable Verb</td></row>
		<row><td>ISWebSite</td><td>IP</td><td>Y</td><td/><td/><td/><td/><td/><td/><td/></row>
		<row><td>ISWebSite</td><td>ISIISCommon_</td><td>N</td><td/><td/><td>ISIISCommon</td><td>1</td><td>Text</td><td/><td>Foreign key into ISIISCommon table</td></row>
		<row><td>ISWebSite</td><td>Port</td><td>N</td><td/><td/><td/><td/><td/><td/><td/></row>
		<row><td>ISWebSite</td><td>SiteNumber</td><td>N</td><td/><td/><td/><td/><td/><td/><td/></row>
		<row><td>ISWebSite</td><td>WebSiteProps</td><td>Y</td><td/><td/><td/><td/><td/><td/><td/></row>
		<row><td>ISXmlElement</td><td>Content</td><td>Y</td><td/><td/><td/><td/><td>Text</td><td/><td>Element contents</td></row>
		<row><td>ISXmlElement</td><td>ISAttributes</td><td>Y</td><td/><td/><td/><td/><td>Number</td><td/><td>Internal XML element attributes</td></row>
		<row><td>ISXmlElement</td><td>ISXmlElement</td><td>N</td><td/><td/><td/><td/><td>Identifier</td><td/><td>Primary key, non-localized, internal token for Xml element</td></row>
		<row><td>ISXmlElement</td><td>ISXmlElement_Parent</td><td>Y</td><td/><td/><td>ISXmlElement</td><td>1</td><td>Identifier</td><td/><td>Foreign key into ISXMLElement table.</td></row>
		<row><td>ISXmlElement</td><td>ISXmlFile_</td><td>N</td><td/><td/><td>ISXmlFile</td><td>1</td><td>Identifier</td><td/><td>Foreign key into XmlFile table.</td></row>
		<row><td>ISXmlElement</td><td>XPath</td><td>Y</td><td/><td/><td/><td/><td>Text</td><td/><td>XPath fragment including any operators</td></row>
		<row><td>ISXmlElementAttrib</td><td>ISAttributes</td><td>Y</td><td/><td/><td/><td/><td>Number</td><td/><td>Internal XML elementattib attributes</td></row>
		<row><td>ISXmlElementAttrib</td><td>ISXmlElementAttrib</td><td>N</td><td/><td/><td/><td/><td>Identifier</td><td/><td>Primary key, non-localized, internal token for Xml element attribute</td></row>
		<row><td>ISXmlElementAttrib</td><td>ISXmlElement_</td><td>N</td><td/><td/><td>ISXmlElement</td><td>1</td><td>Identifier</td><td/><td>Foreign key into ISXMLElement table.</td></row>
		<row><td>ISXmlElementAttrib</td><td>Name</td><td>Y</td><td/><td/><td/><td/><td>Text</td><td/><td>Localized attribute name</td></row>
		<row><td>ISXmlElementAttrib</td><td>Value</td><td>Y</td><td/><td/><td/><td/><td>Text</td><td/><td>Localized attribute value</td></row>
		<row><td>ISXmlFile</td><td>Component_</td><td>N</td><td/><td/><td>Component</td><td>1</td><td>Identifier</td><td/><td>Foreign key into Component table.</td></row>
		<row><td>ISXmlFile</td><td>Directory</td><td>N</td><td/><td/><td/><td/><td>Identifier</td><td/><td>Foreign key into Directory table.</td></row>
		<row><td>ISXmlFile</td><td>FileName</td><td>N</td><td/><td/><td/><td/><td>Text</td><td/><td>Localized XML file name</td></row>
		<row><td>ISXmlFile</td><td>ISAttributes</td><td>Y</td><td/><td/><td/><td/><td>Number</td><td/><td>Internal XML file attributes</td></row>
		<row><td>ISXmlFile</td><td>ISXmlFile</td><td>N</td><td/><td/><td/><td/><td>Identifier</td><td/><td>Primary key, non-localized,internal token for Xml file</td></row>
		<row><td>ISXmlLocator</td><td>Attribute</td><td>Y</td><td/><td/><td/><td/><td/><td/><td>The name of an attribute within the XML element.</td></row>
		<row><td>ISXmlLocator</td><td>Element</td><td>Y</td><td/><td/><td/><td/><td/><td/><td>XPath query that will locate an element in an XML file.</td></row>
		<row><td>ISXmlLocator</td><td>ISAttributes</td><td>Y</td><td/><td/><td/><td/><td/><td>0;1;2</td><td/></row>
		<row><td>ISXmlLocator</td><td>Parent</td><td>Y</td><td/><td/><td/><td/><td>Identifier</td><td/><td>The parent file signature. It is also a foreign key in the Signature table.</td></row>
		<row><td>ISXmlLocator</td><td>Signature_</td><td>N</td><td/><td/><td/><td/><td>Identifier</td><td/><td>The Signature_ represents a unique file signature and is also the foreign key in the Signature,  RegLocator, IniLocator, ISXmlLocator, CompLocator and the DrLocator tables.</td></row>
		<row><td>Icon</td><td>Data</td><td>Y</td><td/><td/><td/><td/><td>Binary</td><td/><td>Binary stream. The binary icon data in PE (.DLL or .EXE) or icon (.ICO) format.</td></row>
		<row><td>Icon</td><td>ISBuildSourcePath</td><td>Y</td><td/><td/><td/><td/><td>Text</td><td/><td>Full path to the ICO or EXE file.</td></row>
		<row><td>Icon</td><td>ISIconIndex</td><td>Y</td><td>-32767</td><td>32767</td><td/><td/><td/><td/><td>Optional icon index to be extracted.</td></row>
		<row><td>Icon</td><td>Name</td><td>N</td><td/><td/><td/><td/><td>Identifier</td><td/><td>Primary key. Name of the icon file.</td></row>
		<row><td>InstallShield</td><td>Property</td><td>N</td><td/><td/><td/><td/><td>Identifier</td><td/><td>Name of property, uppercase if settable by launcher or loader.</td></row>
		<row><td>InstallShield</td><td>Value</td><td>Y</td><td/><td/><td/><td/><td>Text</td><td/><td>String value for property.</td></row>
		<row><td>ListBox</td><td>Order</td><td>N</td><td>1</td><td>32767</td><td/><td/><td/><td/><td>A positive integer used to determine the ordering of the items within one list..The integers do not have to be consecutive.</td></row>
		<row><td>ListBox</td><td>Property</td><td>N</td><td/><td/><td/><td/><td>Identifier</td><td/><td>A named property to be tied to this item. All the items tied to the same property become part of the same listbox.</td></row>
		<row><td>ListBox</td><td>Text</td><td>Y</td><td/><td/><td/><td/><td>Text</td><td/><td>The visible text to be assigned to the item. Optional. If this entry or the entire column is missing, the text is the same as the value.</td></row>
		<row><td>ListBox</td><td>Value</td><td>N</td><td/><td/><td/><td/><td>Formatted</td><td/><td>The value string associated with this item. Selecting the line will set the associated property to this value.</td></row>
		<row><td>ListView</td><td>Binary_</td><td>Y</td><td/><td/><td>Binary</td><td>1</td><td>Identifier</td><td/><td>The name of the icon to be displayed with the icon. The binary information is looked up from the Binary Table.</td></row>
		<row><td>ListView</td><td>Order</td><td>N</td><td>1</td><td>32767</td><td/><td/><td/><td/><td>A positive integer used to determine the ordering of the items within one list..The integers do not have to be consecutive.</td></row>
		<row><td>ListView</td><td>Property</td><td>N</td><td/><td/><td/><td/><td>Identifier</td><td/><td>A named property to be tied to this item. All the items tied to the same property become part of the same listview.</td></row>
		<row><td>ListView</td><td>Text</td><td>Y</td><td/><td/><td/><td/><td>Text</td><td/><td>The visible text to be assigned to the item. Optional. If this entry or the entire column is missing, the text is the same as the value.</td></row>
		<row><td>ListView</td><td>Value</td><td>N</td><td/><td/><td/><td/><td>Text</td><td/><td>The value string associated with this item. Selecting the line will set the associated property to this value.</td></row>
		<row><td>MsiPatchOldAssemblyFile</td><td>Assembly_</td><td>Y</td><td/><td/><td>MsiPatchOldAssemblyName</td><td>1</td><td/><td/><td/></row>
		<row><td>MsiPatchOldAssemblyFile</td><td>File_</td><td>N</td><td/><td/><td>File</td><td>1</td><td/><td/><td/></row>
		<row><td>MsiPatchOldAssemblyName</td><td>Assembly_</td><td>N</td><td/><td/><td/><td/><td/><td/><td/></row>
		<row><td>MsiPatchOldAssemblyName</td><td>Name</td><td>N</td><td/><td/><td/><td/><td/><td/><td/></row>
		<row><td>MsiPatchOldAssemblyName</td><td>Value</td><td>Y</td><td/><td/><td/><td/><td/><td/><td/></row>
		<row><td>Property</td><td>ISComments</td><td>Y</td><td/><td/><td/><td/><td>Text</td><td/><td>User Comments.</td></row>
		<row><td>Property</td><td>Property</td><td>N</td><td/><td/><td/><td/><td>Identifier</td><td/><td>Name of property, uppercase if settable by launcher or loader.</td></row>
		<row><td>Property</td><td>Value</td><td>Y</td><td/><td/><td/><td/><td>Text</td><td/><td>String value for property.</td></row>
		<row><td>RadioButton</td><td>Height</td><td>N</td><td>0</td><td>32767</td><td/><td/><td/><td/><td>The height of the button.</td></row>
		<row><td>RadioButton</td><td>Help</td><td>Y</td><td/><td/><td/><td/><td>Text</td><td/><td>The help strings used with the button. The text is optional.</td></row>
		<row><td>RadioButton</td><td>ISControlId</td><td>Y</td><td/><td/><td/><td/><td/><td/><td>A number used to represent the control ID of the Control, Used in Dialog export</td></row>
		<row><td>RadioButton</td><td>Order</td><td>N</td><td>1</td><td>32767</td><td/><td/><td/><td/><td>A positive integer used to determine the ordering of the items within one list..The integers do not have to be consecutive.</td></row>
		<row><td>RadioButton</td><td>Property</td><td>N</td><td/><td/><td/><td/><td>Identifier</td><td/><td>A named property to be tied to this radio button. All the buttons tied to the same property become part of the same group.</td></row>
		<row><td>RadioButton</td><td>Text</td><td>Y</td><td/><td/><td/><td/><td>Text</td><td/><td>The visible title to be assigned to the radio button.</td></row>
		<row><td>RadioButton</td><td>Value</td><td>N</td><td/><td/><td/><td/><td>Formatted</td><td/><td>The value string associated with this button. Selecting the button will set the associated property to this value.</td></row>
		<row><td>RadioButton</td><td>Width</td><td>N</td><td>0</td><td>32767</td><td/><td/><td/><td/><td>The width of the button.</td></row>
		<row><td>RadioButton</td><td>X</td><td>N</td><td>0</td><td>32767</td><td/><td/><td/><td/><td>The horizontal coordinate of the upper left corner of the bounding rectangle of the radio button.</td></row>
		<row><td>RadioButton</td><td>Y</td><td>N</td><td>0</td><td>32767</td><td/><td/><td/><td/><td>The vertical coordinate of the upper left corner of the bounding rectangle of the radio button.</td></row>
		<row><td>Registry</td><td>ISAttributes</td><td>Y</td><td/><td/><td/><td/><td/><td/><td>This is used to store Installshield custom properties of a registry item.  Currently the only one is Automatic.</td></row>
		<row><td>Registry</td><td>ISRegistrySet_</td><td>N</td><td/><td/><td>ISRegistrySet</td><td>1</td><td>Text</td><td/><td>Foreign key into the Component table referencing component that controls the installing of the registry value.</td></row>
		<row><td>Registry</td><td>Key</td><td>N</td><td/><td/><td/><td/><td>Text</td><td/><td>The key for the registry value.</td></row>
		<row><td>Registry</td><td>Name</td><td>Y</td><td/><td/><td/><td/><td>Formatted</td><td/><td>The registry value name.</td></row>
		<row><td>Registry</td><td>Registry</td><td>N</td><td/><td/><td/><td/><td>Identifier</td><td/><td>Primary key, non-localized token.</td></row>
		<row><td>Registry</td><td>Root</td><td>N</td><td>-1</td><td>3</td><td/><td/><td/><td/><td>The predefined root key for the registry value, one of rrkEnum.</td></row>
		<row><td>Registry</td><td>Value</td><td>Y</td><td/><td/><td/><td/><td>Text</td><td/><td>The registry value.</td></row>
		<row><td>Shortcut</td><td>Arguments</td><td>Y</td><td/><td/><td/><td/><td>Formatted</td><td/><td>The command-line arguments for the shortcut.</td></row>
		<row><td>Shortcut</td><td>Component_</td><td>Y</td><td/><td/><td>Component</td><td>1</td><td>Text</td><td/><td>Foreign key into the Component table denoting the component whose selection gates the the shortcut creation/deletion.</td></row>
		<row><td>Shortcut</td><td>Description</td><td>Y</td><td/><td/><td/><td/><td>Text</td><td/><td>The description for the shortcut.</td></row>
		<row><td>Shortcut</td><td>Directory_</td><td>N</td><td/><td/><td>Directory</td><td>1</td><td>Identifier</td><td/><td>Foreign key into the Directory table denoting the directory where the shortcut file is created.</td></row>
		<row><td>Shortcut</td><td>Hotkey</td><td>Y</td><td>0</td><td>32767</td><td/><td/><td/><td/><td>The hotkey for the shortcut. It has the virtual-key code for the key in the low-order byte, and the modifier flags in the high-order byte. </td></row>
		<row><td>Shortcut</td><td>ISAttributes</td><td>Y</td><td/><td/><td/><td/><td/><td/><td>This is used to store Installshield custom properties of a shortcut.  Mainly used in pro project types.</td></row>
		<row><td>Shortcut</td><td>ISComments</td><td>Y</td><td/><td/><td/><td/><td>Text</td><td/><td>Author’s comments on this Shortcut.</td></row>
		<row><td>Shortcut</td><td>ISShortcutName</td><td>Y</td><td/><td/><td/><td/><td>Text</td><td/><td>A non-unique name for the shortcut.  Mainly used by pro pro project types.</td></row>
		<row><td>Shortcut</td><td>IconIndex</td><td>Y</td><td>-32767</td><td>32767</td><td/><td/><td/><td/><td>The icon index for the shortcut.</td></row>
		<row><td>Shortcut</td><td>Icon_</td><td>Y</td><td/><td/><td>Icon</td><td>1</td><td>Identifier</td><td/><td>Foreign key into the File table denoting the external icon file for the shortcut.</td></row>
		<row><td>Shortcut</td><td>Name</td><td>Y</td><td/><td/><td/><td/><td>Text</td><td/><td>The name of the shortcut to be created.</td></row>
		<row><td>Shortcut</td><td>Shortcut</td><td>N</td><td/><td/><td/><td/><td>Identifier</td><td/><td>Primary key, non-localized token.</td></row>
		<row><td>Shortcut</td><td>ShowCmd</td><td>Y</td><td/><td/><td/><td/><td/><td>1;3;7</td><td>The show command for the application window.The following values may be used.</td></row>
		<row><td>Shortcut</td><td>Target</td><td>Y</td><td/><td/><td/><td/><td>Text</td><td/><td>The shortcut target. This is usually a property that is expanded to a file or a folder that the shortcut points to.</td></row>
		<row><td>Shortcut</td><td>WkDir</td><td>Y</td><td/><td/><td/><td/><td>Text</td><td/><td>Name of property defining location of working directory.</td></row>
		<row><td>TextStyle</td><td>Color</td><td>Y</td><td>0</td><td>16777215</td><td/><td/><td/><td/><td>A long integer indicating the color of the string in the RGB format (Red, Green, Blue each 0-255, RGB = R + 256*G + 256^2*B).</td></row>
		<row><td>TextStyle</td><td>FaceName</td><td>N</td><td/><td/><td/><td/><td>Text</td><td/><td>A string indicating the name of the font used. Required. The string must be at most 31 characters long.</td></row>
		<row><td>TextStyle</td><td>Size</td><td>N</td><td>0</td><td>32767</td><td/><td/><td/><td/><td>The size of the font used. This size is given in our units (1/12 of the system font height). Assuming that the system font is set to 12 point size, this is equivalent to the point size.</td></row>
		<row><td>TextStyle</td><td>StyleBits</td><td>Y</td><td>0</td><td>15</td><td/><td/><td/><td/><td>A combination of style bits.</td></row>
		<row><td>TextStyle</td><td>TextStyle</td><td>N</td><td/><td/><td/><td/><td>Identifier</td><td/><td>Name of the style. The primary key of this table. This name is embedded in the texts to indicate a style change.</td></row>
		<row><td>_Validation</td><td>Category</td><td>Y</td><td/><td/><td/><td/><td/><td>"Text";"Formatted";"Template";"Condition";"Guid";"Path";"Version";"Language";"Identifier";"Binary";"UpperCase";"LowerCase";"Filename";"Paths";"AnyPath";"WildCardFilename";"RegPath";"KeyFormatted";"CustomSource";"Property";"Cabinet";"Shortcut";"URL";"DefaultDir"</td><td>String category</td></row>
		<row><td>_Validation</td><td>Column</td><td>N</td><td/><td/><td/><td/><td>Identifier</td><td/><td>Name of column</td></row>
		<row><td>_Validation</td><td>Description</td><td>Y</td><td/><td/><td/><td/><td>Text</td><td/><td>Description of column</td></row>
		<row><td>_Validation</td><td>KeyColumn</td><td>Y</td><td>1</td><td>32</td><td/><td/><td/><td/><td>Column to which foreign key connects</td></row>
		<row><td>_Validation</td><td>KeyTable</td><td>Y</td><td/><td/><td/><td/><td>Identifier</td><td/><td>For foreign key, Name of table to which data must link</td></row>
		<row><td>_Validation</td><td>MaxValue</td><td>Y</td><td>-2147483647</td><td>2147483647</td><td/><td/><td/><td/><td>Maximum value allowed</td></row>
		<row><td>_Validation</td><td>MinValue</td><td>Y</td><td>-2147483647</td><td>2147483647</td><td/><td/><td/><td/><td>Minimum value allowed</td></row>
		<row><td>_Validation</td><td>Nullable</td><td>N</td><td/><td/><td/><td/><td/><td>Y;N;@</td><td>Whether the column is nullable</td></row>
		<row><td>_Validation</td><td>Set</td><td>Y</td><td/><td/><td/><td/><td>Text</td><td/><td>Set of values that are permitted</td></row>
		<row><td>_Validation</td><td>Table</td><td>N</td><td/><td/><td/><td/><td>Identifier</td><td/><td>Name of table</td></row>
	</table>
</msi>
